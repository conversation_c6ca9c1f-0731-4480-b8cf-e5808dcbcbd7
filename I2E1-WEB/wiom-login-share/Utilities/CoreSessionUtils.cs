using i2e1_basics.Cache;
using Microsoft.AspNetCore.Http;
using System.Web;
using wiom_login_share.Models;

namespace wiom_login_share.Utilities
{
    public delegate T SessionFiller<T>(long shardId, string token);
    public delegate void Callback();

    public class CoreSessionUtils
    {
        public static T GetValueFromToken<T>(HttpContext _httpContext, string tokenKey, string cacheKey)
        {
            if (_httpContext == null)
                return default(T);

            object value = _httpContext.Items[cacheKey];
            T obj;
            if (value == null)
            {
                var token = _httpContext.Request.Headers[tokenKey].ToString();
                if (string.IsNullOrEmpty(token))
                {
                    var cookie = CookieUtils.GetCookie(_httpContext, tokenKey);
                    if (!string.IsNullOrEmpty(cookie))
                    {
                        token = cookie;
                    }

                    if (string.IsNullOrEmpty(token))
                    {
                        var queryParam = _httpContext.Request.Query[tokenKey].ToString();
                        if (string.IsNullOrEmpty(queryParam))
                        {
                            return default(T);
                        }
                        else
                        {
                            token = queryParam;
                        }
                    }  
                }
                token = HttpUtility.UrlDecode(token);
                if (token.Contains("$"))
                    token = token.Substring(token.IndexOf('$')+1);

                obj = SessionCacheHelper.GetInstance().GetSession<T>(token, cacheKey);
                _httpContext.Items[cacheKey] = obj;
            }
            else
            {
                obj = (T)value;
            }
            return obj;
        }

        public static void SetValueAndToken<T>(HttpContext _httpContext, long shardId, string tokenKey, string cacheKey, T value, string tokenValue = null)
        {
            if (tokenValue == null)
            {
                tokenValue = CookieUtils.GetCookie(_httpContext, tokenKey);
                if (string.IsNullOrEmpty(tokenValue))
                    tokenValue = Guid.NewGuid().ToString();
            }

            if (!string.IsNullOrEmpty(tokenValue) && tokenValue.Contains("$"))
                tokenValue = tokenValue.Substring(tokenValue.IndexOf('$') + 1);

            if (value == null)
            {
                SessionCacheHelper.GetInstance().DeleteSession(tokenValue, cacheKey);
                CookieUtils.DeleteCookie(_httpContext, tokenKey);
            }
            else
            {
                if (_httpContext.Items["writePending" + tokenValue + ";" + cacheKey] == null)
                {
                    AddOnEndRequest(_httpContext, () =>
                    {
                        SessionCacheHelper.GetInstance().SetSession(tokenValue, cacheKey, _httpContext.Items[cacheKey]);
                    });
                }
                _httpContext.Items[cacheKey] = value;
                _httpContext.Items["writePending" + tokenValue + ";" + cacheKey] = true;
                CookieUtils.SetCookie(_httpContext, tokenKey, tokenValue, false, DateTime.UtcNow.AddYears(1), true, SameSiteMode.None);
            }
        }
        public static void AddOnEndRequest(HttpContext httpContext, Callback callback)
        {
            var callbacks = (List<Callback>)httpContext.Items["OnEndRequest"];
            if (callbacks == null)
            {
                callbacks = new List<Callback>();
                httpContext.Items["OnEndRequest"] = callbacks;
            }
            callbacks.Add(callback);
        }

        public static User GetLoginServerUser(HttpContext httpContext)
        {
            return CoreSessionUtils.GetValueFromToken<User>(httpContext, "login-user-session", "LOGIN_SERVER_USER");
        }
        public static void SetLoginServerUser(User value, HttpContext httpContext)
        {
            CoreSessionUtils.SetValueAndToken(httpContext, value.backEndNasid.shard_id, "login-user-session", "LOGIN_SERVER_USER", value, value.GetToken());
        }
        
    }
}
