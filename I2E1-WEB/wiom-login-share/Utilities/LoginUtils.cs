using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using wifidog_core.Models;
using wiom_login_share.Models;
using ControllerType = wiom_login_share.Models.ControllerType;

namespace wiom_login_share.Utilities
{
    public class RouterUtils
    {
        internal static LoginResponse getRouterLoginUrl(User user, string chap, string landingPage)
        {
            LoginResponse response = new LoginResponse();
            switch (user.controllertype)
            {
                case ControllerType.I2E1:
                    response.isGet = true;
                    response.url = "http://" + user.uamip + ':' + user.uamport + "/logon";
                    response.parameters.Add(new NameValuePair("username", user.GetToken()));
                    response.parameters.Add(new NameValuePair("password", chap));
                    response.parameters.Add(new NameValuePair("userurl", landingPage));
                    return response;
                case ControllerType.ARUBA:
                    response.isGet = false;
                    response.url = "https://" + user.uamip + "/auth/index.html/u";
                    response.parameters.Add(new NameValuePair("cmd", "authenticate"));
                    response.parameters.Add(new NameValuePair("user", user.GetToken()));
                    response.parameters.Add(new NameValuePair("password", user.otp));
                    response.parameters.Add(new NameValuePair("mac", user.mac));
                    response.parameters.Add(new NameValuePair("url", landingPage));
                    return response;
                case ControllerType.RUCKUS:
                    response.isGet = false;
                    response.url = "http://" + user.uamip + ":9997/login";
                    response.parameters.Add(new NameValuePair("username", user.GetToken()));
                    response.parameters.Add(new NameValuePair("password", "1234"));
                    response.parameters.Add(new NameValuePair("ip", user.clientip));
                    response.parameters.Add(new NameValuePair("url", landingPage));
                    return response;
                case ControllerType.MICROTIK:
                    response.isGet = false;
                    response.url = user.uamip;
                    response.parameters.Add(new NameValuePair("username", user.GetToken()));
                    response.parameters.Add(new NameValuePair("password", user.otp));
                    response.parameters.Add(new NameValuePair("dst", landingPage));
                    return response;
                case ControllerType.WIFIDOG:
                    response.isGet = true;
                    response.url = "http://" + user.uamip + ':' + user.uamport + "/wifidog/auth";
                    response.parameters.Add(new NameValuePair("token", user.GetToken()));
                    response.parameters.Add(new NameValuePair("url", landingPage));
                    return response;
                case ControllerType.HFCL:
                    response.isGet = true;
                    response.url = "http://" + user.uamip + ':' + user.uamport + "/logon";
                    response.parameters.Add(new NameValuePair("username", user.GetToken()));
                    response.parameters.Add(new NameValuePair("token", user.GetToken()));
                    response.parameters.Add(new NameValuePair("url", landingPage));
                    return response;
                case ControllerType.CAMBIUM:
                    response.isGet = true;
                    response.url = "http://" + user.uamip + ':' + user.uamport + "/cgi-bin/hotspot_login.cgi" + user.attributes["query"];
                    response.parameters.Add(new NameValuePair("ga_user", user.GetToken()));
                    response.parameters.Add(new NameValuePair("ga_pass", "1234"));
                    return response;
            }
            return null;
        }

        public static Dictionary<string, object> getRouterLoginUrlDict(User user, string chap, string landingPage)
        {
            var data = new Dictionary<string, object>();
            data.Add("landingPage", getRouterLoginUrl(user, chap, landingPage));
            data.Add("chap", chap);
            return data;
        }
    }
}
