using i2e1_basics.Utilities;
using System.Text;
using System.Web;
using wifidog_core.Models;

namespace wiom_login_share.Models
{
    [Serializable]
    public enum UserState
    {
        notyet = 0,
        failed,
        success,
        already,
        logoff
    }
    [Serializable]
    public enum OTPState
    {
        GO_TO_RECEPTION = 0,
        SUCCESS = 1,
        DEVICE_COUNT_REACHED = 2,
        MOBILE_COUNT_REACHED = 3,
        OTP_REQUIRED = 4,
        OTP_SENT 
    }
    [Serializable]
    public enum DeviceType
    {
        CHROME = 0,
        FIREFOX,
        IE,
        OPERA,
        SAFARI,
        EDGE,
        BLINK
    }
    [Serializable]
    public enum ControllerType
    {
        I2E1 = 0,
        ARUBA = 1,
        MICROTIK = 2,
        AIRTEL = 3,
        WIFIDOG = 4,
        RUCKUS = 5,
        HFCL = 6,
        CAMBIUM = 7
    }

    public enum Origin
    {
        WOFR = 0,
        I2E1 = 1,
        LINQ = 2,
        BOT = 7
    }

    public enum StoreGroupId
    {
        NONE = 0,
        TRAI = 9,
        PAYTM = 11
    }
    public enum App
    {
        COUPON_RESELLER = 0,
        WIOM_DASHBOARD,
        I2E1_ADMIN_PORTAL,
        HOME_ROUTER = 5,
        ACCOUNT_AGGREGATION,
        WIOM_SALES,
        INTERNAL_SERVICES = 8,
        LEAD_SERVICES = 9,
        LEAD_VERIFICATION = 10
    }
    [Serializable]
    public class UserProfile
    {
        public LongIdInfo id { get; set; }
        public string mobile { get; set; }

        public string email { get; set; }

        public string profilePhoto { get; set; }

        public string name { get; set; }

        public string gender { get; set; }

        public string username { get; set; }
    }

    [Serializable]
    public class User : WifiUser
    {
        public User()
        {
            templateid = new List<int>();
            attributes = new Dictionary<string, string>();
        }

        private int pTestGroup;

        private string pRouterMac;
        public int combinedSettingId { get; set; }

        public string name { get; set; }

        public string device { get; set; }

        public bool isVip { get; set; }

        public string called
        {
            get { return pRouterMac; }
            set { pRouterMac = value == null ? null : value.ToUpper().Split('_')[0].Replace('-', ':'); }
        }

        public List<int> templateid { get; set; }

        public int testGroup {
            get 
            {
                if (pTestGroup == 0 && backEndNasid != null && backEndNasid.local_value != 0)
                {
                    pTestGroup = (int)(backEndNasid.GetLongId() % 10) + 1;
                }

                return pTestGroup;
            }
            set
            {
                pTestGroup = value;
            }
        }

        public string uamip { get; set; }

        public string clientip { get; set; }

        public string uamport { get; set; }

        public string deviceId { get; set; }

        public ControllerType controllertype { get; set; }

        public int smscount { get; set; }

        public string otp { get; set; }

        public UserState res { get; set; }

        public string smsapi { get; set; }

        public string challenge { get; set; }

        private AuthType? pClientAuthType;

        public AuthType? clientAuthType
        {
            get
            {
                return pClientAuthType;
            }
            set
            {
                if (value != null)
                {
                    pClientAuthType = value;
                }
            }
        }

        public bool askaccesscode { get; set; }

        public bool guestmodeswitched { get; set; }

        public Dictionary<string, string> attributes { get; set; }

        public int GetBaseTemplateId()
        {
            return templateid.Count == 0 ? 0 : templateid[0];
        }

        public bool IsWaniNas()
        {
            return backEndNasid.local_value == 96019 || backEndNasid.local_value == 96018;
        }

        private string _uniqueIdentifier;
        public string unique_identifier
        {
            get {

                if (string.IsNullOrEmpty(_uniqueIdentifier))
                    _uniqueIdentifier = GenerateUniqueIdentifier();

                return _uniqueIdentifier;
            }
            set
            {
                if (string.IsNullOrEmpty(value))
                {
                    _uniqueIdentifier = GenerateUniqueIdentifier();
                }
                else
                {
                    _uniqueIdentifier = value;
                }
            }
        }
        private string GenerateUniqueIdentifier()
        {
            return mac + "_" + DateTime.UtcNow.Ticks;
        }
    }
    [Serializable]
    public class LoginResponse
    {
        public LoginResponse()
        {
            parameters = new List<NameValuePair>();
        }
        public string url { get; set; }

        public bool isGet { get; set; }

        public List<NameValuePair> parameters { get; set; }

        public string SerializeParameters()
        {
            StringBuilder sb = new StringBuilder(url);
            sb.Append("?");
            foreach (var pair in parameters)
            {
                sb.Append(pair.name).Append("=").Append(HttpUtility.UrlEncode(pair.value)).Append("&");
            }
            return sb.ToString();
        }
    }
    [Serializable]
    public enum AuthType
    {
        DISABLE = -1,
        PHONE = 0,
        IDENTITY, //deprecated now
        NATIONAL_ID,
        REGISTERED_MOBILE,
        AUTO_LOGIN,
        PHONE_OR_NATIONAL_ID,
        REGISTERED_MOBILE_OR_NATIONAL_ID,
        NATIONAL_ID_OR_PHONE,
        LAST_NAME_ROOM_NO = 8,
        ACCESS_CODE = 9,
        FACEBOOK_LOGIN = 10,
        SOCIAL_LOGIN = 11,
        DATA_VOUCHER = 12,
        DATA_VOUCHER_WITHOUT_OTP = 13,
        PHONE_OR_DATA_VOUCHER_WITHOUT_OTP = 14,
        WANI_LOGIN = 15,
        PHONE_WITHOUT_OTP = 16
    }
}
