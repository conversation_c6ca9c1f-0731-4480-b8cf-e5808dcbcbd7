using i2e1_basics.Models;
using i2e1_basics.Utilities;
using Newtonsoft.Json;
using wifidog_core.Models.RouterPlan;
using wiom_login_share.Models;

namespace wiom_login_share.ExposeApi
{
    public class GenericApi : BasicSingleton<GenericApi>, ISingletonTemplate
    {
        private readonly BasicHttpClient basicHttpClient;
        void ISingletonTemplate.OnInit(params string[] vals)
        {
            Logger.GetInstance().Info("wiom_login_share.ExposeApi:GenericApi is instantiated");
        }

        public GenericApi()
        {
            string baseUrl = "https://dev.i2e1.in/";
            if (I2e1ConfigurationManager.DEPLOYED_ON.ToLower() == "prod" || I2e1ConfigurationManager.DEPLOYED_ON.ToLower() == "production")
            {
                baseUrl = "https://www.i2e1.in/";
            }
            else if (!String.IsNullOrEmpty(I2e1ConfigurationManager.DEPLOYED_ON))
            {
                baseUrl = $"https://{I2e1ConfigurationManager.DEPLOYED_ON.ToLower()}.i2e1.in/";
            }
            basicHttpClient = new BasicHttpClient(baseUrl);
        }
        public async Task<List<HomeRouterPlan>> ReloginUser(User user, long fdmId)
        {
            Logger.GetInstance().Info($"GenericApi: ReloginUser called with user: {JsonConvert.SerializeObject(user)}, fdmId: {fdmId}");

            Dictionary<string, object> data = new Dictionary<string, object>()
            {
                { "user", user },
                { "fdmId", fdmId }
            };

            var response = await basicHttpClient.PostAsync(ApiConstant.CREATE_MAC_MOBILE_MAPPING, null, data);
            var jsonResponse = JsonConvert.DeserializeObject<JsonResponse>(response);

            Logger.GetInstance().Info("GenericApi: ReloginUser: jsonResponse " + JsonConvert.SerializeObject(jsonResponse));

            if (jsonResponse.status == ResponseStatus.SUCCESS)
                return JsonConvert.DeserializeObject<List<HomeRouterPlan>>(jsonResponse.data?.ToString(), BasicConstants.JSON_SERIALIZER_SETTINGS);

            return null;
        }

    }
       
}