using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Filters;
using Microsoft.AspNetCore.Routing;
using wiom_login_share.Utilities;

namespace I2E1_WEB.Attributes;

public class LoginPortalAuthorizationAttribute : TypeFilterAttribute
{
    public LoginPortalAuthorizationAttribute() : base(typeof(LoginPortalAuthorization))
    {
    }
}

public class LoginPortalAuthorization : IAuthorizationFilter
{
    public void OnAuthorization(AuthorizationFilterContext filterContext)
    {
        var data = CoreSessionUtils.GetLoginServerUser(filterContext.HttpContext);
        if (data == null)
        {
            filterContext.Result = new RedirectToRouteResult(new RouteValueDictionary
                     {{ "controller", "Login" },
                      { "action", "SessionExpired" } });
        }
        else
            filterContext.HttpContext.Items["loginUser"] = data;
    }
}