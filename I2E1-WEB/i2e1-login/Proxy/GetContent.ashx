<%@ WebHandler Language="C#" Class="GetContent" %>
using System;
using System.Web;
using System.Collections.Generic;

public class GetContent : IHttpHandler {
    
    public void ProcessRequest (HttpContext context) {
        var strURL = context.Server.UrlDecode(context.Request["url"]);

        var webClient = new System.Net.WebClient();
        try {
            var data = webClient.DownloadData(strURL);
            //context.Response.Headers.Clear();
            var myWebHeaderCollection = webClient.ResponseHeaders;

            for (int i = 0; i < myWebHeaderCollection.Count; i++)
            {
                if (myWebHeaderCollection.GetKey(i).ToLower() == "content-type")
                    context.Response.ContentType = myWebHeaderCollection.Get(i);
            }
            context.Response.OutputStream.Write(data, 0, data.Length);
            context.Response.Flush();
        }
        catch (Exception) {
            
        }
    }
 
    public bool IsReusable {
        get {
            return true;
        }
    }
}