@using i2e1_core.Utilities;
@using i2e1_basics.Utilities
@using I2E1_Message.Utils
@using I2E1_WEB.Controllers
@using i2e1_core.Models.WIOM
@{
    var paymentDomain = CoreUtil.GetPaymentServerUrl();
    var loginDomain = CoreUtil.GetLoginServerUrl();
    var selectedPlanId = ViewBag.selectedPlanId;
    var price = ViewBag.price;
    var nasid = ViewBag.nasid;
    var deviceId = ViewBag.deviceId;
    var mobile = ViewBag.mobile;
    var UniqueIdentifier = ViewBag.uniqueIdentifier;
    var stringQRCode = ViewBag.stringQRCode;
    var nextPageUrl = $"{loginDomain}/Login/plan_selection?nasid={nasid}&deviceId={deviceId}&mobile={mobile}";
}
<!DOCTYPE html>
<html>
<head>
    <title>WIOM Share</title>
    <meta name="theme-color" content="#ffffff" />
    <meta name="accept-language" content="@System.Threading.Thread.CurrentThread.CurrentCulture.ToString()" />
    <meta name="viewport" content="width=device-width, height=device-height, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0, user-scalable=no" />
    <link rel="stylesheet" type="text/css" href="@Util.GetUrlWithVersion("/Templates/i2e1/site.css")" />
    <link rel="shortcut icon" href="/images/wiom/wiom-app-for.svg" type="image/x-icon">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+Devanagari:wght@100;200;300;400;500;600;700;800;900&display=swap" rel="stylesheet">
    <script src="@Util.GetUrlWithVersion("/jsLibs/jquery.js")"></script>
    <script src="@Util.GetUrlWithVersion("/Templates/i2e1/i2e1-sdk.js")"></script>
    <style>
        html, body {
            background-color: #fff;
            font-family: 'Noto Sans Devanagari', sans-serif !important;
            letter-spacing: 0 !important;
            margin: 0;
            padding: 0;
            height: 100%;
            width: 100%;
            overflow-x: hidden;
        }
        
        .container {
            min-height: 100vh;
            width: 100%;
            margin: 0;
            padding: 0;
            display: flex;
            flex-direction: column;
            justify-content: flex-start;
        }
        
        .header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 1.5rem 1.25rem;
            width: 100%;
            box-sizing: border-box;
        }
        
        .header img {
            height: 2.5em; /* 40px */
        }
        
        .share-card {
            background-color: #fff;
            display: flex;
            flex-direction: column;
            padding: 1.5rem 1.25rem;
            box-sizing: border-box;
            width: 100%;
            flex: 0 0 auto;
        }
        
        .share-title {
            font-size: 1.5rem;
            font-weight: 700;
            color: #161021;
            margin-bottom: 1em; /* 16px */
            text-align: left;
        }
        
        .share-description {
            font-size: 1rem;
            color: #161021;
            margin-bottom: 1.5em; /* 24px */
        }
        
        .share-text {
            display: flex;
            align-items: center;
            justify-content: center;
            text-align: center;
            color: #D9008D;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            padding: 1.5rem 0;
            text-decoration: none;
            margin-top: 2rem;
        }
        
        .share-text img {
            height: 1.5em; /* 24px */
            margin-right: 0.5em; /* 8px */
        }
        
        .next-button {
            display: block;
            background-color: #D9008D;
            color: white;
            border: none;
            border-radius: 0.5em; /* 8px */
            padding: 0.75em 1.5em; /* 12px 24px */
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            width: 100%;
            text-align: center;
            text-decoration: none;
        }
        
        .price-tag {
            font-size: 1rem;
            font-weight: 500;
            color: #161021;
            margin-top: 1em; /* 16px */
            text-align: center;
        }
        
        .steps-wrapper {
            border: 0.0625em solid #E8E4F0; /* 1px */
            border-radius: 0.75em; /* 12px */
            padding: 1em; /* 16px */
            margin-bottom: 1.5em; /* 24px */
            width: 100%;
            box-sizing: border-box;
        }
        
        .step {
            display: flex;
            align-items: anchor-center;
            margin-bottom: 1em; /* 16px */
            justify-content: flex-start;
        }
        
        .step:last-child {
            margin-bottom: 0;
        }
        
        .step-number {
            color: black;
            font-weight: 700;
            margin-right: 0.25em; /* Small right margin for readability */
            flex-shrink: 0;
            min-width: 4em; /* 64px */
            text-align: right;
        }
        
        .step-text {
            font-size: 1rem;
            color: #161021;
            font-weight: 500;
            display: flex;
            align-items: center;
            flex: 1;
            padding-left: 0; /* Remove left padding */
        }
        
        .qr-box {
            background-color: #F1EDF7;
            border: 0.0625em solid #E8E4F0; /* 1px */
            border-radius: 0.75em; /* 12px */
            padding: 1.5em; /* 24px */
            margin-top: 1.5em; /* 24px */
            display: flex;
            flex-direction: column;
            align-items: center;
            width: 100%;
            box-sizing: border-box;
        }
        
        .steps-container {
            display: flex;
            justify-content: center;
            width: 100%;
        }
        
        #qr-code {
            width: 12.5em; /* 200px */
            height: 12.5em; /* 200px */
            background-color: white;
            padding: 0.75em; /* 12px */
            border-radius: 0.5em; /* 8px */
            margin-bottom: 1em; /* 16px */
        }
        
        .content-wrapper {
            display: flex;
            flex-direction: column;
        }
        
        .spacer {
            flex: 1;
            min-height: 2rem;
        }
        
        .back-icon {
            cursor: pointer;
            width: 24px;
            height: 24px;
            margin-right: 10px;
            color: #161021;
        }
        
        .header-left {
            display: flex;
            align-items: center;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="header-left">
                <img src="/images/back-icon.png" onclick="goBack()" alt="Back" />
            </div>
            <select class="languageSelect" id="languageSelect" onchange="handleLanguageChange(this)">
                <option value="en">English</option>
                <option value="hi" selected>हिंदी</option>
            </select>
        </div>
        
        <div class="share-card">
            <div class="content-wrapper">
                <div class="share-title">For Payment</div>
                
                <div class="steps-wrapper">
                    <div class="step">
                        <div class="step-number">Step 1:</div>
                        <div class="step-text">Give cash to friend or nearby shopkeeper</div>
                    </div>
                    <div class="step">
                        <div class="step-number">Step 2:</div>
                        <div class="step-text">Ask them make payment using this QR code</div>
                    </div>
                </div>
                    
                <div class="steps-container">
                    <div class="qr-box">
                        <div id="qr-code"></div>
                        <div class="price-tag">Amount ₹@price</div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="spacer"></div>
        
        <a href="javascript:void(0)" class="share-text" onclick="shareToWA()">
            <img src="/images/Frame 427323145.png" alt="WhatsApp Icon" />
            Share payment link on WhatsApp
        </a>
    </div>
    
    <script src="https://cdnjs.cloudflare.com/ajax/libs/qrcodejs/1.0.0/qrcode.min.js"></script>
    <script>
        var logger = getLogger({
            mobile: '@ViewBag.mobile',
            plan_id: '@ViewBag.selectedPlanId',
            uniqueIdentifier: '@ViewBag.uniqueIdentifier',
            deviceId: '@deviceId'
        });
        
        document.addEventListener('DOMContentLoaded', function() {
            generateQRCode();
        });
        
        function generateQRCode() {
            var qrCodeElement = document.getElementById('qr-code');
            if (qrCodeElement) {
                // Clear any existing content
                qrCodeElement.innerHTML = '';
                
                new QRCode(qrCodeElement, {
                    text: '@Html.Raw(stringQRCode)',
                    width: 200,
                    height: 200,
                    colorDark: "#161021",
                    colorLight: "#F1EDF7",
                    correctLevel: QRCode.CorrectLevel.H
                });
                
                // Log QR code generation
                logger("qr_code_generated");
            }
        }
        
        function shareToWA() {
            const curLang = document.getElementById('languageSelect').value;
            const planSelectionUrl = '@loginDomain/Login/PlanSelectionPage?lang=' + curLang + '&nasid=@nasid&deviceId=@deviceId&uniqueIdentifier=@Html.Raw(UniqueIdentifier)&mobile=@mobile';
            
            fetch('@loginDomain/U/Generate?url=' + encodeURIComponent(planSelectionUrl))
                .then(response => response.json())
                .then(resp => {
                    console.log(resp);
                    window.open(`https://api.whatsapp.com/send?text=` + encodeURIComponent('Please pay using this link ' + resp.data), '_blank');
                    // Log the share event
                    logger("whatsapp_share_click");
                })
                .catch(err => {
                    console.log(err);
                });
        }
        
        function handleLanguageChange(selectElement) {
            var selectedLanguage = selectElement.value;
            window.localStorage.setItem('currLang', selectedLanguage);
            
            // Reload the page to apply language change
            window.location.reload();
        }
        
        function goBack() {
            window.history.back();
        }
    </script>
</body>
</html>
