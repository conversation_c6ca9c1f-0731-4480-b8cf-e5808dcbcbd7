@using i2e1_core.Models
@using I2E1_Message.Utils
@using i2e1_core.Utilities;
@using i2e1_basics.Utilities
@using I2E1_WEB
@model wiom_login_share.Models.User;
<!DOCTYPE html>
<html>
<head>
    <title>i2e1 Login Successful</title>
    <!-- The below comment is required for troubleshooting .Kindly dont remove it as it wont do any harm -->
    <!-- QWERTPOIUY123456789 -->
    <meta name="theme-color" content="#ffffff" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0, user-scalable=no" />
    <link rel="stylesheet" type="text/css" href="@Util.GetUrlWithVersion("/Templates/i2e1/site.css")" />
    <link rel="shortcut icon" href="/images/wiom/wiom-app-for.svg" type="image/x-icon">
    <script src="@Util.GetUrlWithVersion("/jsLibs/jquery.js")"></script>
    <script src="@Util.GetUrlWithVersion("/Templates/i2e1/i2e1-sdk.js")"></script>
    <script src="@Util.GetUrlWithVersion("/Templates/i2e1/template-utils.js")"></script>
    @{
        Html.RenderPartial("~/Views/Login/Shared/WebSocketEvents.cshtml");
        Dictionary<string, string> resourceDict = new Dictionary<string, string>();

        resourceDict["Culture"] = System.Threading.Thread.CurrentThread.CurrentUICulture.Name;
    }
    <script type="text/javascript">
        var currLang = 'hi';
    var _loginUser = @Html.Raw(Newtonsoft.Json.JsonConvert.SerializeObject(Model));
    var _progressbar;
    var _viewBag = {
        sessionExpired: '@ViewBag.sessionExpired',
        dataExhausted: '@ViewBag.dataExhausted',
        landingObject: '@Html.Raw(Newtonsoft.Json.JsonConvert.SerializeObject(ViewBag.landingObject))',
        loginLogo: '',
        swapLink: '@Html.Raw(ViewBag.swapLink)',
        resources:@Html.Raw(Newtonsoft.Json.JsonConvert.SerializeObject(resourceDict))
    }

        function handleLanguageChange(select) {
            const selectedOption = select.value;
            currLang = selectedOption;
            const successTitle1 = document.getElementById("success-title1");
            const successTitle2 = document.getElementById("success-title2");
            if (currLang == "en") {
                successTitle1.innerHTML = "You are connected to ∞ <b>Wiom Net</b>";
                successTitle2.innerText = "Enjoy unlimited internet";
            }
            else {
                successTitle1.innerHTML = "आप <b>∞ व्योम नेट</b> से जुड़ गए हैं";
                successTitle2.innerText = "अनलिमिटेड इंटरनेट का आनंद लें";
            }
        }

    var _getLandingPage = function () {
        i2e1Api.getLandingPage({
            onSuccess: function(response){
                $('.landing-page-message > p').text(response.data.pageTitle);
                var i =0;
                response.data.icons.forEach(function(site){
                    if(i == 4){
                        $('.landing-page-tiles-container').
                                            append($('<span/>').
                                            html('<a target="_blank" style="background-color:white;" class="small" title="i2e1" href="http://www.i2e1.com"><img style="width: 95%;margin: 21% 0;" src="/images/wiom/wiom_logo.png" /></a><br/>'));
                    }
                    else{
                        $('.landing-page-tiles-container').
                                            append($('<span/>').
                                                html('<a target="_blank" class="small" href="' + site.url + '" title="' + site.title + '"><img width="100%" height="100%" src="' + site.imageUrl +'" /></a>'));
                    }
                    i++;
                });
            }
        });
    };

    var _logout = function() {
        window.location.href = 'http://logout.i2e1.com?ip=@Model.uamip';
    }
    var _getMobile = function() {
        return "";
    }
    var _landingSearch = function () {
        if (!document.getElementById('search').value) return;
        window.location.href = 'https://www.google.com/search?q=' + encodeURIComponent(document.getElementById('search').value)
    }
    var _onTileClick = function (link) {
        window.location.href = link
    }


    setTimeout(() => {
        var upiId = _getCookie('pay-via-upi');
        if (upiId) {
            window.open(`upi://pay?cu=INR&pa=${upiId}&pn=${upiId}&tn=Linq_Payment`);
        }
    }, 2000);
    i2e1Api.logEvent("land_pgload", _loginUser.nasid, { mobile: _loginUser.mobile });
    </script>
    <script type="text/javascript">
    (function ($) {
        $.getJSON("@Util.GetUrlWithVersion(Url.Action("GetResources", "Login"))", function(data){
            _viewBag = _viewBag || {};
            _viewBag.resources = data;
            _localise();
            i2e1Api.getLandingPage({
                onSuccess: function(response){
                    if(response.data.pageTitle == 'You are now connnected') {
                        $('.landing-page-message > p').html(_viewBag.resources.landingState_connected_msg);
                    }
                }
            });
        });
    })(jQuery);

    function toggleLanguage() {
        currLang = (currLang === 'hi') ? 'en' : 'hi';
        window.localStorage.setItem('currLang', currLang);

        const helpTexts = document.querySelectorAll(".help-text");
        helpTexts.forEach(helpText => {
            helpText.innerText = (currLang === 'en') ? "Help" : "मदद";
        });

        const successTitle1 = document.getElementById("success-title1");
        const successTitle2 = document.getElementById("success-title2");
            const hotspotCount = document.getElementById("hotspot_count"); // <-- important!


        if (currLang === 'en') {
            successTitle1.innerHTML = "Connected to <b>∞ व्योम नेट</b>";
            successTitle2.innerText = "Enjoy Unlimited Internet";
                    if (hotspotCount) hotspotCount.innerText = "Available at 1,00,000+ locations"; // 🌟 ENGLISH for hotspot

        } else {
            successTitle1.innerHTML = "आप <b>∞ व्योम नेट</b> से जुड़ गए हैं";
            successTitle2.innerText = "अनलिमिटेड इंटरनेट का आनंद लें";
                    if (hotspotCount) hotspotCount.innerText = "1,00,000+ लोकेशन पर उपलब्ध"; // 🌟 HINDI for hotspot

        }
    }


    </script>
</head>
<body onload="_bodyOnload(_i2e1Constants.landingState);" class="landing">
    <div>
        <div id="success" class="" style="margin:0 auto;color: #1a1a1a;height:auto; max-width: 40rem; letter-spacing: 0">
            <div style="display: flex; align-items: center; justify-content: space-between; padding: 1.5rem;">
    <div style="display: flex; align-items: center;">
        <img src="/images/wiom-icon.svg" style="margin-right: 8px;" />
        <span style="font-family: 'Noto Sans'; font-weight: 700; font-size: 1.25rem; line-height: 1.75rem; letter-spacing: 0px; color: #D9008D;">
            Wiom
        </span>
    </div>
    <div style="display: flex; align-items: center;">
        <a id="help-text" href="tel:8880322222" class="help-text" style="margin-right: 1em; cursor: pointer; font-family: 'Noto Sans'; font-weight: 600; font-size: 0.875rem; line-height: 1.25rem; letter-spacing: 0px; text-align: center; color: #D9008D; text-decoration: none;">
            Help
        </a>
        <img src="/images/language_icon.png" alt="Language" onclick="toggleLanguage()" style="cursor: pointer; width: 24px; height: 24px;" />
    </div>
</div>

            <div style="margin-top: 5rem"><img src="~/images/payment-success-icon.svg" /></div>
            <p id="success-title1" style="color: #444444; width: 90%; margin: 1rem auto">आप <b>∞ व्योम नेट</b> से जुड़ गए हैं</p>
            <h2 id="success-title2" style="color: #1a1a1a; font-weight:700; width: 90%; margin: 3rem auto">अनलिमिटेड इंटरनेट का आनंद लें </h2>
            @*<p style="font-size: 1.125rem;margin-top:2rem;">You have unlocked plan</p>*@
            @*<p style="font-weight: 600" id="data"></p>*@
            @*<p style="text-align:right;font-size:0.625rem;color:#000;">*Terms & Condition Apply</p>*@
            <div style="position: relative; max-height: 26rem; max-width: 40rem; margin: 0 auto;">
                <img style="width: 100%;" src="~/images/payment_success_bottom_v2.png" />
                <div id="hotspot_count" style="text-align: center; font-size: 0.75rem;margin-top=1rem; padding-bottom: 1rem; border-radius: 8px; display: inline-block;font-weight:600">
                    1,00,000+ लोकेशन पर उपलब्ध
                </div>
            </div>

        </div>
    </div>
    @*<div style="display: flex;padding:1.5rem;
    align-items: center;">
        <img style="border-radius: 0.5em" src="/images/wiom/wiom-app-for.svg" />
        <span _ngcontent-anu-c47="" style="font-weight: 700; font-size: 1.2em; color: #D92B90; margin-left: 0.5em;">WIOM</span>
    </div>
    <div class="top-container display-none">
        <div class="login container" style="width:90%;">
            <form class="landing-state state-transition display-none" onkeyup="_submitThis('_landingSearch')" onsubmit="return false;">
                <div style="margin-top:3rem;" class="landing-page-message" cage="landing-page-message">
                    <p></p>
                </div>
                <div class="user-attributes">
                    <div class="data"></div>
                    <div class="time"></div>
                </div>
                <div action="https://www.google.com/search">
                    <img src="/images/search.png" onclick="_landingSearch()" />
                    <input id="search" type="text" name="q" placeholder="Search" i18nPlaceHolder="landingState_search" />
                    <input type="button" name="enter" style="display:none;" onclick="_landingSearch(false)" />
                </div>
                <div cage="landing-page-tiles-container" class="landing-page-tiles-container"></div>
                <button class="primary login_button" onclick="_swapState(_i2e1Constants.customerProfileState); _getMyUsage();" style="margin-top: 1rem;">
                    See My profile
                    <span style="width: .5rem;height: .5rem;border-top: 2px solid white;border-right: 2px solid white;display: inline-block;transform: rotateZ(45deg);"></span>
                </button>
            </form>

            <form class="form error-state" style="display: none;">
                <div id="errorMsg" class="errorSpan"></div>
                <div class="limit-exhausted" style="display: none;"></div>
                <div class="time-exhausted" style="display: none;"></div>
                <div class="user-blocked" style="display: none;"></div>
                <div class="go-back-to-login">
                    <a onclick="_swapState(_i2e1Constants.firstState);">login with other number</a>
                </div>
            </form>

            <div class="customer-profile-state state-transition display-none">
                <div class="current-plan-div">
                    <p style="margin:0">Plan 1</p>
                    <p class="current-plan-p" style="margin:0"></p>
                </div>
                <hr />
                <div class="balance-div">
                    <div class="balance-data fs-14">
                        <p style="margin:2px 0">Data Left</p>
                        <p style="margin:0">
                            <span id="data-p" style="color:#48bb78;font-weight:700"></span>
                        </p>
                    </div>
                    <div class="balance-time fs-14">
                        <p style="margin:2px 0">Time Left</p>
                        <p style="margin:0">
                            <span id="time-p" style="color:#48bb78;font-weight:700"></span>
                        </p>
                    </div>
                </div>
                <div class="tabs">
                    <div class="payment-tab active" onclick="showPaymentTab()">
                        <p class="fs-12">Payments</p>
                    </div>
                    <div class="usage-tab" onclick="showUsageTab()">
                        <p class="fs-12">Usage</p>
                    </div>
                    <div class="payment-div">
                        <table style="width: 100%;border-collapse:collapse;">
                            <thead>
                                <tr>
                                    <th style="width:22%">Date</th>
                                    <th style="width:34%">PDO Name</th>
                                    <th style="width:22%">Amount</th>
                                    <th style="width:22%">Medium </th>
                                </tr>
                            </thead>
                            <tbody class="payment-body">
                            </tbody>
                        </table>
                    </div>
                    <div class="usage-div display-none">
                        <table style="width: 100%;border-collapse:collapse;">
                            <thead>
                                <tr>
                                    <th style="width:20%">Date</th>
                                    <th style="width:20%">Mac Id</th>
                                    <th style="width:20%">Data Upload</th>
                                    <th style="width:20%">Data Download</th>
                                </tr>
                            </thead>
                            <tbody class="usage-body">
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
            <div class="footer">
                <div class="top-border"></div>
                <span style="display: inline-block;"><img style="width: 1rem;margin-right: 0.5em;" src="/images/call.svg"/><b><a href="tel:0918880322222">8880322222</a>
                </b></span>
                <span style="float:right">@Model.backEndNasid</span>
                <br/>
                <span style="display: inline-block;"><img style="width: 1rem;margin-right: 0.5em;" src="/images/sms-icon.png"/><b><a target="_blank" href="mailto:<EMAIL>"><EMAIL></a> 
                </b></span>
            </div>
        </div>
        <div class="popup display-none" onclick="closePopup()">
            <div class="popup-inner-div">
                <div>
                    <p class="fs-12">PDO Name</p>
                    <p class="pdo-name fw-700"></p>
                </div>
                <hr />
                <div style="display:flex;justify-content:space-around;">
                    <div>
                        <p class="fs-12 light-grey">Date</p>
                        <p class="pdo-date"></p>
                    </div>
                    <div>
                        <p class="fs-12 light-grey pdo-start-time-label">Time</p>
                        <p class="pdo-start-time"></p>
                    </div>
                    <div style="display:none;" class="pdo-end-time-div">
                        <p class="fs-12 light-grey pdo-end-time-label">End Time</p>
                        <p class="pdo-end-time"></p>
                    </div>
                </div>
                <hr />
                <div style="display:flex;justify-content:space-between;">
                    <div>
                        <p class="fs-12 light-grey pdo-plan-label">Plan</p>
                        <p class="pdo-plan"></p>
                    </div>
                    <div>
                        <p class="fs-12 light-grey">Amount</p>
                        <p class="pdo-price"></p>
                    </div>
                    <div>
                        <p class="fs-12 light-grey">Medium</p>
                        <p class="pdo-medium"></p>
                    </div>
                </div>
                <div class="download-btn"></div>
            </div>
        </div>*@
    </div>

    <div id="loader">
        <div class="deactivate"></div>
        <div class="img-section">
            <div>
                <img src="~/images/wifi_loader_84X84.gif" />
            </div>
        </div>
    </div>
    <div id="internal-templates display-none">
    </div>
    <div id="forignTemplates" class="display-none">
        @{
            int baseTemplate = Model.GetBaseTemplateId();
            if (baseTemplate == 0)
            {
                Model.templateid.Add(int.Parse(I2e1ConfigurationManager.GetInstance().GetSetting("i2e1.DefaultTemplateId")));
            }

            for (int i = 0; i < Model.templateid.Count; ++i)
            {
                if (Model.templateid[i] != 0)
                {
                    <div id=forignTemplate_@i>
                        @Html.Raw(CacheHelper.GetInstance().GetTemplateContent(Model.templateid[i], Model.backEndNasid).templateContent)
                    </div>
                }
            }
        }
    </div>
    <script>
        $('.footer').show();
        var getDate = function (str, option) {
                if (option == "date") {
                    str = str.substring(0, str.indexOf("T"));
                    str = str.split("-").reverse().join("/");
                    return str;
                } else if (option == "time") {
                    str = str.substr(str.indexOf("T") + 1, str.length);
                    str = str.substr(0, str.lastIndexOf(":"));
                    return str;
                }
            };

        var showUsageTab = function () {
            $(".usage-tab").addClass("active")
            $(".usage-div").removeClass("display-none");
            $(".payment-tab").removeClass("active");
            $(".payment-div").addClass("display-none");
        }
        var showPaymentTab = function () {
            $(".usage-tab").removeClass("active")
            $(".usage-div").addClass("display-none");
            $(".payment-tab").addClass("active");
            $(".payment-div").removeClass("display-none");
        }
        var roundoffData = function (dataLeft) {
            if (dataLeft >= 1073741824) //converting to GB
            {
                dataLeft = roundoff(dataLeft / 1073741824) + " GB";
            }
            else if (dataLeft >= 1048576) //converting to MB
            {
                dataLeft = roundoff(dataLeft / 1048576) + " MB";
            }
            else if (dataLeft >= 1024) //converting to KB
            {
                dataLeft = roundoff(dataLeft / 1024) + " KB";
            }
            return dataLeft;
        }

        var roundoffTime = function (totalSec) {
            if (totalSec > 0) {
                var days = parseInt(totalSec / 86400);
                var hours = parseInt(totalSec / 3600) % 24;
                var minutes = parseInt(totalSec / 60) % 60;
                var seconds = totalSec % 60;
                var result = (days >= 1 ? days + (days === 1 ? ' ' + _viewBag.resources.landingState_time_day + ' ' : ' ' + _viewBag.resources.landingState_time_days + ' ') : '') +
                    (hours >= 1 ? hours + (hours === 1 ? ' ' + _viewBag.resources.landingState_time_hr + ' ' : ' ' + _viewBag.resources.landingState_time_hrs + ' ') : '');
                if (days < 1)
                    result += (minutes >= 1 ? minutes + (minutes === 1 ? ' ' + _viewBag.resources.landingState_time_min + ' ' : ' ' + _viewBag.resources.landingState_time_mins + ' ') : '');
                return result ? result : ' ' + _viewBag.resources.landingState_time_lessthan_a_minute + ' ';
            }
            return 'None';
        }
        var userUsage = [];
        var detailedUsage=[];
        var _getMyUsage = function () {
            var d = new Date();
            var s = new Date();
            s.setDate(s.getDate() - 30)
            var getDate = function (str) {
                str = str.substring(0, str.indexOf("T"));
                str = str.split("-").reverse().join("/");
                return str;
            };
            i2e1Api.doAjax("/Login/GetMyDataUsage", {
                startTime: s,
                endTime: d
            }, function (res) {
                if (res.status == 0) {
                    var extraData = "";
                    userUsage = res.data;
                    var sortOnTime = (a, b) => {
                        return a.otpIssuedTime > b.otpIssuedTime;
                    }
                    var imageNText;
                    userUsage.sort(sortOnTime);
                    res.data.forEach((el, i) => {
                        if (el.extraData) {
                            extraData = el.extraDataObject;
                        } else {
                            extraData;
                        }
                        if(el.otp == 'PAY_ONLINE')
                            return;
                        if (el.otp == 'DONE') {
                            imageNText = '<span style="vertical-align:super;">Online</span>'
                        }
                        else if(el.otp == 'CASH'){
                            imageNText = `<img src = '/images/wiom/wallet.svg'/><span style="vertical-align:super;">Cash</span>`;
                        }
                        else if(el.otp == 'ROAM'){
                            imageNText='<img style="border-radius: 0.5em;width:1rem;margin-right:0.25rem" src="/images/wiom/wiom-app-for.svg" /><span style="vertical-align:super;">Member</span>'
                        }
                        else if(el.otp == 'FREE'){
                            imageNText='<span style="vertical-align:super;">None</span>'
                        }
                        else {
                            imageNText = `<span style="vertical-align:super;">Coupon</span>`
                        }
                        $(".payment-body").append("<tr class='payment-tr-" + i + "' onclick='openPopup(" + i + ")'></tr>");
                        $(".payment-tr-" + i).append("<td style='width: 22%'>" + getDate(el.otpIssuedTime) + "</td >");
                        $(".payment-tr-" + i).append("<td style='font-weight: 700;width:34%'><p class='truncate'>" + el.name + "</p></td >");
                        $(".payment-tr-" + i).append("<td style='width: 22%'>" + (el.charges ? ('₹' + el.charges) : 'Free') + "</td >");
                        $(".payment-tr-" + i).append("<td style='width: 22%'>" + imageNText + "</td >");

                    });
                    if (userUsage.length && userUsage[0].extraDataObject) {
                        var newExtraData = userUsage[0].extraDataObject;
                        $(".current-plan-p").append(roundoffData(userUsage[0].dataPlan * 1024 * 1024) + " for " + roundoffTime(newExtraData.time_limit));
                    }
                }
                $("#data-p").append(window.dataLeft);
                $("#time-p").append(window.timeLeft);
            });
            i2e1Api.doAjax("/Login/GetDetailedDataUsage", {
                startTime: s,
                endTime: d
            }, function (res) {
                if (res.status == 0) {
                    var sortOnTime = (a, b) => {
                        if(a.sessionStart > b.sessionStart)
                            return -1;
                        else if (a.sessionStart < b.sessionStart)
                            return 1;
                        return 0;
                    }
                    res.data.sort(sortOnTime);
                    detailedUsage = res.data;
                    res.data.forEach((el, i) => {
                        if(el.otp == 'PAY_ONLINE')
                            return;

                        $(".usage-body").append("<tr class='usage-tr-" + i + "' onclick='openPopup(" + i + ", \"usage\")'></tr>");
                        $(".usage-tr-" + i).append("<td style='width: 20%'>" + getDate(el.sessionStart) + "</td >");
                        $(".usage-tr-" + i).append("<td style='font-weight: 700;width: 20%'><p class='truncate'>" + el.macId + "</p></td >");
                        $(".usage-tr-" + i).append("<td style='width: 20%'>" + roundoffData(el.dataUpload) + "</td >");
                        $(".usage-tr-" + i).append("<td style='width: 20%'>" + roundoffData(el.dataDownload) + "</td >");

                    });
                }
            });
        }

        function openPopup(i, option) {
            option = option || "";
            $(".popup").removeClass("display-none");
            var usageObj, extraData = "";
            if (option == "usage") {
                $(".pdo-date").text(getDate(detailedUsage[i].sessionStart, "date"));
                $(".pdo-start-time").text(getDate(detailedUsage[i].sessionStart, "time"));
                $(".pdo-end-time").text(getDate(detailedUsage[i].sessionEnd, "time"));
                $(".pdo-plan").text(roundoffData(detailedUsage[i].dataUpload + detailedUsage[i].dataDownload));
                $('.pdo-plan-label').text('Data Used');
                $(".pdo-start-time-label").text('Start Time');
                $('.pdo-end-time-div').show();
                var arr = userUsage.filter(function(m){ return m.id == detailedUsage[i].fdmId})
                usageObj = arr.length ? arr[0] : {};
            }
            else{
                usageObj = userUsage[i];
                if (usageObj.extraDataObject) {
                    extraData = usageObj.extraDataObject;
                } else {
                    extraData;
                }
                $(".pdo-date").text(getDate(usageObj.otpIssuedTime, "date"));
                $(".pdo-start-time").text(getDate(usageObj.otpIssuedTime, "time"));
                $(".pdo-plan").text(roundoffData(usageObj.dataPlan * 1024 * 1024) + "/" + roundoffTime(extraData.time_limit));
                $('.pdo-plan-label').text('Plan');
                $(".pdo-start-time-label").text('Time');
                $('.pdo-end-time-div').hide();
            }
            
            $(".pdo-name").append(usageObj.name);
            var imageNText;

            if(usageObj.otp == 'PAY_ONLINE'){
                imageNText = `<span style="vertical-align:super;">For Payment</span>`
            }
            else if (usageObj.otp == 'DONE') {
                imageNText = '<span style="vertical-align:super;">Online</span>'
            }
            else if(usageObj.otp == 'CASH'){
                imageNText = `<img src = '/images/wiom/wallet.svg'/><span style="vertical-align:super;">Cash</span>`;
            }
            else if(usageObj.otp == 'ROAM'){
                imageNText='<img style="border-radius: 0.5em;width:1rem;margin-right:0.25rem" src="/images/wiom/wiom-app-for.svg" /><span style="vertical-align:super;">Member</span>'
            }
            else if(usageObj.otp == 'FREE'){
                imageNText='<span style="vertical-align:super;">None</span>'
            }
            else {
                imageNText = `<span style="vertical-align:super;">Coupon</span>`
            }
            $(".pdo-price").text(usageObj.charges ? "₹" + usageObj.charges : 'Free');
            $(".pdo-medium").append(imageNText);
        }

        function closePopup() {
            $(".popup").addClass("display-none");
            $(".pdo-name").html("");
            $(".pdo-date").html("");
            $(".pdo-time").html("");
            $(".pdo-price").html("");
            $(".pdo-plan").html("");
            $(".pdo-medium").html("");
            $(".download-btn").html("");
        }

        function generateUUID() {
            var d = new Date().getTime();
            var uuid = 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (c) {
                var r = (d + Math.random() * 16) % 16 | 0;
                d = Math.floor(d / 16);
                return (c == 'x' ? r : (r & 0x3 | 0x8)).toString(16);
            });
            return uuid;
        };
        var session = generateUUID();
        var mobile = _getUrlParams().mobile;
        var nasid = _getUrlParams().backEndNasid;
    </script>
</body>
</html>
