@using I2E1_Message.Models
@using i2e1_core.Models;
@using I2E1_Message.Utils
@using i2e1_core.Utilities;
@using i2e1_basics.Utilities
@model wiom_login_share.Models.User;
<!DOCTYPE html>
<html lang="en">
<head>

    <script type="text/javascript">
    @{
        Dictionary<string, string> resourceDict = new Dictionary<string, string>();
            resourceDict["Culture"] = System.Threading.Thread.CurrentThread.CurrentUICulture.Name;
    }
        window.onerror = function (message, source, lineno, colno, error) {
            $.ajax({
                type: "POST",
                url: "/Login/LogJsError",
                data: {
                    message: message,
                    source: source,
                    lineno: lineno,
                    colno: colno,
                    error: btoa(error.stack),
                    url: window.location.href || document.referrer
                }
            })
        };
        window.i2e1Domain = 'https://' + window.location.hostname + (window.location.port ? (':' + window.location.port) : '') + '/';
        window.cdnDomain = 'https://' + window.location.hostname +'/';
        window.loginUser = @Html.Raw(Newtonsoft.Json.JsonConvert.SerializeObject(Model));
        window.viewBag = {
            redirectUrl: '@(ViewBag.redirectUrl == null ? "" : ViewBag.redirectUrl)',
            loginResponse: @Html.Raw(Newtonsoft.Json.JsonConvert.SerializeObject(ViewBag.loginResponse)),
            initialState: '@(ViewBag.state == null ? "" : ViewBag.state)',
            mobile: '@(ViewBag.mobile == null ? "" : ViewBag.mobile)',
            sessionExpired: '@ViewBag.sessionExpired',
            dataExhausted: '@ViewBag.dataExhausted',
            facebookPage: '@(ViewBag.FbPage == null ? "" : ViewBag.FbPage)',
            facebookCheckin: '@((ViewBag.FbCheckin == null || ViewBag.FbCheckin == "0") ? "" : ViewBag.FbCheckin)',
            facebookAppId: @I2e1ConfigurationManager.GetInstance().GetSetting("facebookAppId"),
            loginLogo: '@(string.IsNullOrEmpty(ViewBag.loginLogo) ? null : "/Proxy/GetContent.ashx?url=" + Util.UrlEncode(ViewBag.loginLogo))',
            swapLink: '@Html.Raw(ViewBag.swapLink)',
            resources:@Html.Raw(Newtonsoft.Json.JsonConvert.SerializeObject(resourceDict)),
            welcomeBack: @((ViewBag.welcomeBack != null && ViewBag.welcomeBack == true) ? "true" : "false")
        }
        window.loginUser.clientAuthType = '@(Model.clientAuthType)';
        window.viewBag.welcomeBack = @((ViewBag.welcomeBack != null && ViewBag.welcomeBack == true) ? "true" : "false");
        window.viewBag.globalOtp_Enabled= '@(ViewBag.GlobalOtp_Enabled)';
        window.viewBag.globalOTP_Enforce= '@(ViewBag.GlobalOTP_Enforce)';
        window.viewBag.globalOTP_code= '@(ViewBag.GlobalOTP_code)';
    </script>
    <meta charset="utf-8">
    <meta name="theme-color" content="#ffffff" />
    <meta name="accept-language" content="@System.Threading.Thread.CurrentThread.CurrentCulture.ToString()" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width,initial-scale=1">
    <meta name="theme-color" content="#ffffff">
    <link rel="icon" href="@Util.GetUrlWithVersion("../images/UI/favicon.ico")">
    <link rel="stylesheet" href="../images/UI/site.css">
    <title>i2e1 login</title>
    <link href="@Util.GetUrlWithVersion("../images/UI/css/app.css")" rel="preload" as="style">
    <link href="@Util.GetUrlWithVersion("../images/UI/js/app.js")" rel="preload" as="script">
    <link href="@Util.GetUrlWithVersion("../images/UI/js/chunk-vendors.js")" rel="preload" as="script">
    <link href="@Util.GetUrlWithVersion("../images/UI/css/app.css")" rel="stylesheet">
</head>
<body>
    <noscript><strong>We're sorry but i2e1-login-ui doesn't work properly without JavaScript enabled. Please enable it to continue.</strong></noscript><div id="app"></div>
    <script src="@Util.GetUrlWithVersion("../images/UI/js/chunk-vendors.js")"></script>
    <script src="@Util.GetUrlWithVersion("../images/UI/js/app.js")"></script>
    <div class="display-none">
        <!-- Cheap way to preload Images -->
        <img src="../images/wiom/welldone.png" />
        <img src="../images/wiom/loading.svg" />
    </div>
    <div id="forignTemplates" class="display-none">
        @{
            int baseTemplate = Model.GetBaseTemplateId();
            if (baseTemplate == 0)
            {
                Model.templateid.Add(int.Parse(I2e1ConfigurationManager.GetInstance().GetSetting("i2e1.DefaultTemplateId")));
            }

            for (int i = 0; i < Model.templateid.Count; ++i)
            {
                if (Model.templateid[i] != 0)
                {
                    <div id=forignTemplate_@i>
                        @Html.Raw(CacheHelper.GetInstance().GetTemplateContent(Model.templateid[i], Model.backEndNasid).templateContent)
                    </div>
                }
            }
        }
    </div>
    <div id="style-overrides">
        <style>
            input#resend-otp {
                color: #999;
                background-color: #AC4012;
                border: none;
                font-size: 14px !important;
            }
        </style>
    </div>
</body>
</html>