@model wiom_login_share.Models.LoginResponse
@using I2E1_Message.Utils
@using i2e1_core.Utilities
@using i2e1_basics.Utilities
<!DOCTYPE html>
<html>
<head>
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0, user-scalable=no" />
    <link rel="shortcut icon" href="images/favicon.ico" type="image/x-icon">
    <link rel="stylesheet" type="text/css" href="@Util.GetUrlWithVersion("/Templates/i2e1/site.css")" />
    <script type="text/javascript" src="~/jsLibs/jquery.js"></script>
    <script type="text/javascript" src="@Util.GetUrlWithVersion("/Templates/i2e1/i2e1-sdk.js")"></script>
    @{
        Html.RenderPartial("~/Views/Login/Shared/WebSocketEvents.cshtml");
    }
    <title>i2e1 login</title>
    <script type="text/javascript">

    var _getUrlParams = function() {
        return @Html.Raw(Newtonsoft.Json.JsonConvert.SerializeObject(ViewBag.LoginServerUser));
    }

    var _getMobile = function() {
        return _getUrlParams().mobile;
    }

    _logMPEvent(_i2e1Constants.autologinRedirectionState, {
        event: _i2e1Constants.stateOpened
    });

    var _i2e1Domain = 'https://' + window.location.hostname + (window.location.port ? (':' + window.location.port) : '') + '/';
    var _loginResponse = @Html.Raw(Newtonsoft.Json.JsonConvert.SerializeObject(Model));
    var _redirectSource = '@(ViewBag.RedirectSource == null ? "Auto Login" : ViewBag.RedirectSource)';

    var _makeLogin = function () {
        $('.connected .redirecting-state').removeClass('display-none');

        _logMPEvent(_i2e1Constants.autologinRedirectionState, {
            event: _i2e1Constants.doingLogin
        });
        _makei2e1Login(_loginResponse);
    }

    var _facebook_share = function () {
        $('.fb-share').disabled = true;
        var share = function () {

            var facebookAppId = '@I2e1ConfigurationManager.GetInstance().GetSetting("facebookAppId")';
            var facebookPage = '@(ViewBag.FbPage == null ? "" : ViewBag.FbPage)';

            var url = 'https://www.facebook.com/dialog/share?' +
                 'app_id=' + facebookAppId +
                 '&display=popup' +
                 '&href=' + encodeURIComponent(facebookPage) +
                 '&redirect_uri=' + encodeURIComponent(_i2e1Domain + 'Login/FacebookLogin');
            _logMPEvent(_i2e1Constants.facebookRedirectedState, {
                event: _i2e1Constants.sharePressed,
                fbShareUrl: url
            });
            window.location.href = url;
        }
        share();
    }

    $(document).ready(function () {
        var _nameOfUser = '@ViewBag.name';
        if (_nameOfUser)
            document.getElementById('user-name').innerHTML = 'Hello ' + _nameOfUser + '</br></br>';

        document.getElementById('nasid-span').innerText = _getUrlParams().nasid;

        var facebook_err = '@(ViewBag.facebook_err_msg == null ? null : ViewBag.facebook_err_msg)';
        var facebookMandatory = '@((ViewBag.FbCheckin == null || ViewBag.FbCheckin == "0") ? "" : ViewBag.FbCheckin)';

        if (_redirectSource == "Facebook") {
            if (facebookMandatory) {
                $('.connected.fb-mandatory').removeClass('display-none');
                $('.inner-header .fb-mandatory').toggleClass('display-none');
                $('.loader').addClass('display-none');
                _logMPEvent(_i2e1Constants.facebookRedirectedState, {
                    event: 'Error For FB State'
                });
            } else {
                $('.inner-header .fb').toggleClass('display-none');
                _logMPEvent(_i2e1Constants.facebookRedirectedState, {
                    event: 'Success For FB State'
                });
                _makeLogin();
            }
        } else {
            _makeLogin();
        }
    });
    </script>
</head>
<body>
    <div class="top-container">
        <div class="login container">
            <div class="outer-card1" cage="outer-header">
                <img id="outer-card-img" class="pixelated" src="../../images/logo.png" />
                <span style="font-size: 0.8rem;" id="nasid-span"></span>
            </div>
            <div id="progressBar"><div style="width: 100%;"></div></div>
            <form class="inner-header">Redirecting...</form>
            <form class="inner-header fb display-none">Thanks for sharing</form>
            <form class="inner-header fb-mandatory display-none">Share is mandatory</form>

            <form class="form redirect-state">
                <div id="user-name" class="user-profile">
                </div>
                <div class="connected">
                    <div class="redirecting-state display-none" i18n="redirectionState_please_wait">Please wait while we are logging you in</div>
                </div>

                <div class="loader">
                    <div class="deactivate1"></div>
                    <div class="img-section1">
                        <div>
                            <img src="~/images/wifi_loader_84X84.gif" />
                        </div>
                    </div>
                </div>
                <br />
                <div class="connected fb-mandatory display-none">
                    <div>Please share to proceed</div>
                    <br />
                    <button class="primary" type="button" onclick="_facebook_share();">
                        <img src="~/images/facebook.png" /> <div>Post on Facebook</div>
                    </button>

                </div>

                <div class="connected">
                    <span class="issues-contact-us" i18n="footer_facing_issues_call_us_at">
                        Facing issues? Contact us @@<a target="_blank" href="tel:+918880322222">+91 (0) 8880322222</a>
                    </span>   
                </div>
            </form>
        </div>
    </div>


</body>
</html>