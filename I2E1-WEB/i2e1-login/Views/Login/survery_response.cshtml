@using I2E1_Message.Utils
<html>
<head>
    <title>i2e1</title>
    <meta name="viewport" content="width=device-width, height=device-height, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0, user-scalable=no" />
    <link rel="shortcut icon" href="/images/favicon.ico" type="image/x-icon">
    <script src="@Util.GetUrlWithVersion("/jsLibs/jquery.js")"></script>
    <script src="@Util.GetUrlWithVersion("/Templates/i2e1/i2e1-sdk.js")"></script>
</head>
<body style="background: #00000080;">
    <div class="top-container">
        <div class="login container" style="color: #f25f58;height:auto;box-shadow:none !important;background-color: #fff !important;">
            <img src="~/images/kantar/survey_response.svg" />
            <h2>Hurray...</h2>
            <p style="font-size: 1.125rem;margin-bottom:2rem;">
                You have unlocked<br />
                <b>Free internet for 7 Days*</b>
            </p>
            <p style="text-align:right;font-size:0.625rem;color:#000;">*Terms & Condition Apply</p>
        </div>
        <img style="position: relative; top: -2.5rem;" src="~/images/kantar/wifi_icon.svg" />
    </div>
    <script>
        var landingObject = @Html.Raw(Newtonsoft.Json.JsonConvert.SerializeObject(ViewBag.landingObject));
        setTimeout(function () {
            _makei2e1Login(landingObject.landingPage);
        }, 3000);
    </script>
</body>
<style type="text/css">
    .login.container {
        height: 25rem;
    }
    .ss-exp {
        font-size: 2rem;
        color: #484848;
        height: 24rem;
        display: flex;
        flex-direction: column;
        justify-content: center;
        text-align: center;
    }
    .ss-exp a {
        font-size: 1.5rem;
        margin-top: 1rem;
    }
</style>
<link rel="stylesheet" type="text/css" href="@Util.GetUrlWithVersion("/Templates/i2e1/site.css")" />
</html>