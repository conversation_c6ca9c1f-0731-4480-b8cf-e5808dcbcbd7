@using i2e1_core.Models
@using I2E1_WEB.Models
@using I2E1_WEB
@using I2E1_Message.Utils
@using wiom_login_share.Models
@model wiom_login_share.Models.User;
@{
    Layout = "~/Views/Login/layout_base.cshtml";
}
@section ScriptSection{
    <script type="text/javascript">
        var _loginUser = @Html.Raw(Newtonsoft.Json.JsonConvert.SerializeObject(Model));

        var _getMobile = function () {
            return document.getElementById("username").value;
        }

        var _validateUsername = function () {
            return true;
        };

        var _generateOTP = function () {
            if (_validateUsername()) {
                _logMPEvent(_i2e1Constants.firstState, { event: _i2e1Constants.nationalIdSignInClicked, nationalId: true });
                _reset();
                var username = document.getElementById("username");
                var success = function (response) {
                    if (response.status == 1) {
                        _swapState(_i2e1Constants.errorState, response.msg);
                    } else {
                        _swapState(_i2e1Constants.questionState, { userProfile: response.data.userProfile, otpResponse: response.data.otpResponse, postHook: function () {
							setTimeout(function(){
								$('.question-area').each(function(e) { $(this).removeClass('display-none');  $(this).children().removeClass('display-none'); });
							}, 1000);
						}});
                    }
                }

                var failure = function (response) {
                    _handleOTPError(response.msg, null, _i2e1Constants.firstState);
                }

                if(_processAnswers(3)) return;

                if (username.value) {
                    i2e1Api.generateOTP(username.value, {
                        questions: _i2e1Ques,
                        clientAuthType: _mapClientType(_loginUser.clientAuthType),
                        onSuccess: success,
                        onFailure: failure
                    });
                } else {
                    _handleOTPError('Enter passport/national id', 'username', _i2e1Constants.firstState);
                }
            }
        };

        var _connect = function () {
            _logMPEvent(_i2e1Constants.secondState, { event: _i2e1Constants.submitOTPPressed });
            var username =_getMobile();
            var accessCode = _getAccessCode();

            if (!accessCode && _loginUser.askaccesscode) {
                return;
            }

            var err1 = false, err2;
            err1 = _processAnswers(0);
            err2 =  _processAnswers(5);

            !err1 && !err2 && i2e1Api.submitOTP(username, '', {
                accessCode: accessCode,
                name: '',
                questions: _i2e1Ques,
                onSuccess: _connect.success,
                onFailure: _connect.failure,
                captcha: document.getElementById('captcha').value,
                doLogin: _doLoginRequired()
            });
        };

        _connect.success = function (response) {
            if (_previousState == '.first-state' && _loginUser.clientAuthType == 12 && _loginUser.attributes.freeDataPlan > 0 && _loginUser.attributes.freeSessionTime > 0) {
                _swapState(_i2e1Constants.dataVoucherState);
            } else
                _preLogin(response.data.landingPage);
        };

        _connect.failure = function (response) {
            var _showCaptcha = function (captchaUrl) {
                if (captchaUrl) {
                    document.getElementById('captcha-holder').style.display = 'inline-block';
                    captchaUrl && (document.getElementById('captcha-img').src = captchaUrl);
                } else {
                    document.getElementById('captcha-holder').style.display = 'none';
                }
            };
            _showCaptcha(response.data);
            if(response.msg.indexOf("Access is Blocked By Administrator")>-1){
                _handleOTPError(response.msg, 'otp_access_code', _i2e1Constants.secondState);
            }
            else if(response.msg.indexOf('Access') > -1) {
                _handleOTPError( _loginUser.attributes.dataVoucherPlaceholder || 'Enter voucher code', 'data_voucher', _i2e1Constants.dataVoucherState);
            }
        };
        var _getAccessCode = function() {
            var voucher = document.getElementById("data_voucher").value;
            if (!voucher) _handleOTPError('Enter voucher code', 'data_voucher', _i2e1Constants.dataVoucherState);
            return document.getElementById("data_voucher").value;
        }

    </script>
}

<form class="form first-state state-transition display-none" onkeyup="_submitThis('_generateOTP');" onsubmit="return false;">
    <p class="general-msg display-none"></p>
    <div class="username-area">
        <div class="group">
            <input type="text" id="username" value="@Model.mobile" required="required" pattern=".*\S.*" />
            <label for="username" i18n="firstState_enter_national_id">Enter passport/national id</label>
        </div>
        <div class="error-message display-none" id="fdm-notauthorised-error" i18n="firstState_fdm_notauthorised">
            Please Contact Reception for Internet Access
        </div>
    </div>
    <div class="question-area type-3 display-none"></div>
    <div cage="access-code-connect-button">
        <input type="button" id="connect" class="primary login_button" value="Connect" i18nInputValue="firstState_connect" onclick="_generateOTP(false)" />
    </div>

    @{
        if (Model.clientAuthType == AuthType.NATIONAL_ID_OR_PHONE ||
            (ViewBag.GuestAuthType != null && ViewBag.GuestAuthType != Model.clientAuthType && ViewBag.GuestAuthType != AuthType.DISABLE))
        {
            <div class="tnc-area corner">
                <span class="tnc1" cage="tnc" i18n="firstState_tnc">
                    By continuing you accept <a onclick='_onTileClick("/Templates/i2e1/tnc.html")'>Terms & Conditions</a>
                </span>
            </div>
        }
        else
        {
            <div class="tnc-area">
                <span class="tnc1" cage="tnc" i18n="firstState_tnc">
                    By continuing you accept <a onclick='_onTileClick("/Templates/i2e1/tnc.html")'>Terms & Conditions</a>
                </span>
            </div>
        }

        if (Model.clientAuthType == AuthType.NATIONAL_ID_OR_PHONE)
        {
            <span class="change_auth_link1" i18n="firstState_dont_have_national_id_click_here">
                Don't have passport/national id ? <a href="/Login/GetAuthenticationPage/?authType=PHONE">Click here</a>
            </span>
        }

        if (Model.clientAuthType == AuthType.NATIONAL_ID_OR_PHONE &&
            (ViewBag.GuestAuthType != null && ViewBag.GuestAuthType != Model.clientAuthType && ViewBag.GuestAuthType != AuthType.DISABLE))
        {
            <div class="seperator">
                <span i18n="firstState_or">OR</span>
            </div>
        }

        if (ViewBag.GuestAuthType != null && ViewBag.GuestAuthType != Model.clientAuthType && ViewBag.GuestAuthType != AuthType.DISABLE)
        {
            <span class="change_auth_link1">
                <a href="/Login/GetAuthenticationPage/?authType=@ViewBag.GuestAuthType">
                    <span cage="guest-mode" i18n="firstState_sign_in_as_guest">Sign In as Guest</span>
                </a>
            </span>
        }
    }

</form>

<form class="form second-state state-transition display-none" onkeyup="_submitThis('_connect');" onsubmit="return false;">
    <div class="button-group">
        <div class="back-area" onclick="_swapState(_i2e1Constants.firstState, {resend: false, reverse: true})">
            <img title="Go back to change phone number" i18nTitle="secondState_back" src="../../images/back.png" />
        </div>
    </div>
    <div class="access-code-area" style="display:none">
        <div class="material-input">
            <div class="group">
                <input type="text" id="data_voucher" required="required" pattern=".*\S.*" />
                <label for="data_voucher" i18n="secondState_enter_voucher_code">Enter Voucher Code</label>
            </div>
        </div>
    </div>
    <div id="captcha-holder" style="display:none;">
        <div>
            <span i18n="secondState_captcha">Captcha</span><br />
            <img id="captcha-img" title="captcha" src="" />
        </div>
        <div>
            <span style="opacity:0;" i18n="secondState_enter_captcha">Enter Captcha</span>
            <input id="captcha" placeholder="Enter Captcha" type="text" />
        </div>
    </div>
    <div class="questions-confirm-area display-none">
        <span class="tick"><img src="~/images/wiom/done.svg" /></span>
        <div class="loud" i18n="secondState_questions_loud">Get <b>1 GB</b> Free Internet today</div>
        <div class="less-loud" i18n="secondState_questions_lessloud">Answer simple questions</div>
        <div class="questions-confirm-cta">
            <input type="button" class="primary login_button" i18nInputValue="secondState_questions_cta_okay" onclick="_answerQuestions()" value="Okay" />
        </div>
    </div>
    <div class="question-area type-0 display-none"></div>
    <div class="question-area type-5 display-none"></div>

    <div cage="connect-button">
        <input type="button" id="connect" class="primary login_button" value="Connect" i18nInputValue="secondState_connect" onclick="_connect(false)" />
    </div>
</form>