@using i2e1_core.Models
@using i2e1_core.Utilities;
@using I2E1_Message.Utils

@model wiom_login_share.Models.User;

<script type="text/javascript">
    var landingObject = '@Html.Raw(Newtonsoft.Json.JsonConvert.SerializeObject(ViewBag.landingObject))';
    var landingBag = {
        reply: '@(string.IsNullOrEmpty(ViewBag.reply) ? "" : ViewBag.reply)',
        landingPage: '@Html.Raw(string.IsNullOrEmpty(ViewBag.landingPage) ? "" : ViewBag.landingPage)',
        landingObject: landingObject
    };
    if (landingBag.landingPage != null) {
        window.location.href = landingBag.landingPage;
    }
</script>
@{
    var template = CacheHelper.GetInstance().GetTemplateContent(Model.GetBaseTemplateId());
    if (template.isFullOverriden)
    {
        ViewBag.isLoggedIn = true;
        ViewBag.fullTemplatePath = template.templatePath;
        Html.RenderPartial("~/Views/Login/sdk.cshtml", Model);
    }
    else
    {
        Html.RenderPartial("~/Views/Login/landing.cshtml", Model);
    }
}