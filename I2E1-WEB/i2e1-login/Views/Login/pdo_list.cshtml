
@using I2E1_Message.Utils
@using i2e1_core.Models
@using i2e1_core.Utilities
@using System.Threading
<html>
<head>
    <title>i2e1</title>
    <meta name="viewport" content="width=device-width, height=device-height, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0, user-scalable=no" />
    <link rel="shortcut icon" href="/images/favicon.ico" type="image/x-icon">
    <script src="@Util.GetUrlWithVersion("/jsLibs/jquery.js")"></script>
</head>
<body>
    <div class="top-container">
        <h3 class="header" style="text-align:center; color: #484848;font-size:24px">Buy Coupon</h3>
        <h3 style="margin-bottom:2rem;font-size: 1rem;text-align: center;padding: 0 1rem;" onclick="_buyFrom()">for UNLIMITED internet from your nearest shop</h3>
        <div class="login container">
            <div class="ss-exp">
                <div class="list" id="coupon_sellers_div">
                </div>
            </div>
        </div>
    </div>
    <script>
        var pdoList = @Html.Raw(ViewBag.pdoList);
        var div = $('#coupon_sellers_div');
        div.empty();
        var appendToList = function (start) {
            var i = start;
            for (; i < pdoList.length; ++i) {
                div.append("<div style='border-bottom: 1px solid #eee;padding: 1em 0.5em;'><a style='float:right;margin-top: 0.8em;' href='tel:" + pdoList[i].mobile +
                    "'><img src='/images/call.svg'/></a><div style='width:90%'><b>" + pdoList[i].shop_name + "</b><br/>" + pdoList[i].address + "</div></div>");
            }
        }
        appendToList(0);
    </script>
</body>
<style type="text/css">

    .ss-exp {
        font-size: 2rem;
        color: #484848;
        display: flex;
        flex-direction: column;
        justify-content: center;
        text-align: left;
    }

        .ss-exp a {
            font-size: 1.5rem;
            margin-top: 1rem;
        }
    .list{
        font-size:1rem;
    }
    .list > div{
        margin:0.5rem 0;
    }
</style>
<link rel="stylesheet" type="text/css" href="@Util.GetUrlWithVersion("/Templates/i2e1/site.css")" />
</html>
