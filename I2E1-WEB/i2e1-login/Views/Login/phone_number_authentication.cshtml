@using i2e1_core.Models
@using I2E1_Message.Utils
@using i2e1_core.Utilities
@using wifidog_core.Models.WIOM
@using wiom_login_share.Models
@model wiom_login_share.Models.User;
@{
    Layout = "~/Views/Login/layout_base.cshtml";
    var plans = CoreCacheHelper.GetInstance().GetActivePlansInSetting(Model.combinedSettingId);
    var freePlans = plans == null ? new List<PDOPlan>(0) : plans.Where(m => m.price == 0).ToList();
}
@section ScriptSection {
    @if (ViewBag.GlobalOtp_Enabled != null && ViewBag.GlobalOtp_Enabled)
    {
        <link rel="stylesheet" href="~/jsLibs/countrycode/intlTelInput.css">
        <script src="~/jsLibs/countrycode/intlTelInput.js"></script>
    }

    <script type="text/javascript">
    var _otpMsg = "Enter 4 digit password";
    var _loginUser = @Html.Raw(Newtonsoft.Json.JsonConvert.SerializeObject(Model));
    $('.otp-area').removeClass('display-none');
    $('.resend-otp-area').removeClass('display-none');
    // Code to Handle Case if Autologin Enabled
    _viewBag.globalOtp_Enabled = '@(ViewBag.GlobalOtp_Enabled)';
    _viewBag.globalOTP_Enforce = '@(ViewBag.GlobalOTP_Enforce)';
    _viewBag.globalOTP_code = '@(ViewBag.GlobalOTP_code)';
    _viewBag.plans = @Html.Raw(Newtonsoft.Json.JsonConvert.SerializeObject(plans));

    var _validateUsername = function () {
        if (_viewBag.GlobalOtp_Enabled == "True") return true;
        var username = document.getElementById("username").value;
        if (username == "3333333333") return true;
        if(/^[6-9]{1}$/.test(username.charAt(0))) {
            if (/^[6-9][0-9]{9}$/.test(username)) {
                return true;
            } else {
                _handleOTPError(_viewBag.resources.firstState_10_digit_phone_error, 'username', _i2e1Constants.firstState);
            }
        } else {
            _handleOTPError(_viewBag.resources.firstState_invalid_phno, 'username', _i2e1Constants.firstState);
        }
    };

    var _generateOTP = function (resend, successHandler) {
        if (_validateUsername()) {
            if (!resend)
                _logMPEvent(_i2e1Constants.firstState, { event: _i2e1Constants.generateOTPPressed });
            else {
                _logMPEvent(_i2e1Constants.secondState, { event: _i2e1Constants.resendOTPPressed });
                _i2e1Ques = [];
            }

            var username = $("#username").val();
            if (!resend && !$('.question-area.type-3').hasClass('display-none')) {
                if(_processAnswers(3)) return;
            }

            var countryCode = '';
            @if (ViewBag.GlobalOtp_Enabled != null && ViewBag.GlobalOtp_Enabled)
            {
                @:countryCode = $('.selected-flag').attr('title');
                @:countryCode = countryCode.substring(countryCode.indexOf('+'));
                @:countryCode = username.indexOf('+') == 0 ? '' : countryCode;
                @:$('#username').val(countryCode + username.replace(/ /g,''))
            }
            i2e1Api.logEvent("phonenmbr_submit", _loginUser.nasid, { mobile: username });
            i2e1Api.generateOTP($('#username').val(), {
                isresend: resend,
                questions: _i2e1Ques,
                onSuccess: function(response) {
                    _generateOTP.success(response, resend);
                },
                onFailure: function(response) {
                    _generateOTP.failure(response);
                },
                clientAuthType: _mapClientType(_loginUser.clientAuthType),
                globalOTP: _viewBag.GlobalOtp_Enabled == "True" ? true: false
            });
        }
    };

    _generateOTP.failure = function (response) {
        _handleOTPError(response.msg, 'username', _i2e1Constants.firstState);
    }

    _generateOTP.success = function (response, resend) {
        if(response.status == 1)
            _swapState(_i2e1Constants.errorState, response.msg);
        else if(response.data.otpResponse.landingPage){
            _swapState(_i2e1Constants.questionState, { userProfile: response.data.userProfile, otpResponse: response.data.otpResponse });
            $("div[cage=connect-button] input")[0].addEventListener('click', function () {
                _question_proceed(username.value, response.data.otpResponse);
            });
        }
        _swapState(_i2e1Constants.secondState, { resend: resend, userProfile: response.data.userProfile, stateTransition: !resend });
    }

    var _getMobile = function () {
        return document.getElementById("username").value;
    };

    var _getMobileWithCode = function () {
        return _viewBag.globalOTP_code ? _viewBag.globalOTP_code + '-' + _getMobile() : '+91-' + _getMobile();
    };

    var _getOTP = function () {
        if(document.getElementById("digit-1"))
            return document.getElementById("digit-1").value + document.getElementById("digit-2").value + document.getElementById("digit-3").value + document.getElementById("digit-4").value;
    };

    var _confirm = function () {
        var username = _getMobile();
        var otp = _getOTP();
        var accessCode = _getAccessCode();
        if (!otp)
            console.error("Missing OTP while Checking OTP");
        i2e1Api.checkOTP(username, otp, {
            accessCode: accessCode,
            name: '',
            questions: _i2e1Ques,
            onSuccess: _confirm.success,
            onFailure: _connect.failure,
            captcha: document.getElementById('captcha').value,
            doLogin: false
        });
    };

    _confirm.success = function () {
        $('.questions-confirm-area').removeClass('display-none');
        $('.otp-area').addClass('display-none');
        $('#confirm').addClass('display-none');
        $('#connect').addClass('display-none');
        $('.second-state .barking').addClass('display-none');
        $('.bottom-button-group').addClass('display-none');
    };

    _confirm.failure = function () {
        $('.questions-confirm-area').addClass('display-none');
        $('.otp-area').removeClass('display-none');
        _connect.failure();
    };

    var _connect = function (otp) {
        for (var i = 0; i < _i2e1Ques.length; ++i) {
            if (_i2e1Ques[i].answerType == 1 && (_i2e1Ques[i].answer === null || _i2e1Ques[i].answer === undefined)) {
                showPopUp("Select an option to proceed", false);
                return;
            }
        }

        var username =_getMobile();
        var otp = _getOTP() || otp;
        var accessCode = _getAccessCode();
        if (accessCode != "" && accessCode != "BYPASS") {
            _viewBag.plans = null
        }
        if (!_validateOTP(otp)) {
            _handleOTPError(_viewBag.resources.secondState_enter_4_digit_password, 'otp-group', _i2e1Constants.secondState);
            return;
        }

        if (!accessCode) {
            return;
        }

        var err1 = false, err2;
        err1 = _processAnswers(0);
        err2 = _processAnswers(5);

        i2e1Api.logEvent("otp_submit", _loginUser.nasid, { mobile: username });
        !err1 && !err2 && i2e1Api.submitOTP(username, otp, {
            accessCode: accessCode,
            name: '',
            questions: _i2e1Ques,
            onSuccess: _connect.success,
            onFailure: _connect.failure,
            captcha: document.getElementById('captcha').value,
            doLogin: _doLoginRequired()
        });
    };

    _connect.success = function (response) {
        if(!_viewBag.plans || !_viewBag.plans.length){
            showPopUp("No plans configured. Please configure using Wiom Admin Dashboard", false);
        }
        else if (!response.data.sessionExists) {
            _viewBag.landingPage = response.data.landingPage;
            _swapState(_i2e1Constants.dataVoucherState, { noBack: true })
        } else
            _preLogin(response.data.landingPage);
    };

    _connect.failure = function (response) {
        response.errMsg = response.errMsg || response.msg
        var _showCaptcha = function (captchaUrl) {
            if (captchaUrl) {
                document.getElementById('captcha-holder').style.display = 'inline-block';
                captchaUrl && (document.getElementById('captcha-img').src = captchaUrl);
            } else {
                document.getElementById('captcha-holder').style.display = 'none';
            }
        };
        _showCaptcha(response.data);
        if (response.errMsg.indexOf("Access is Blocked By Administrator") > -1) {
            _handleOTPError(_viewBag.resources.secondState_access_blocked, 'otp_access_code', _i2e1Constants.secondState);
        }
        else if (response.errMsg.indexOf('Access') > -1) {
            _handleOTPError(_loginUser.attributes.dataVoucherPlaceholder || 'Enter bill access code', 'otp_access_code', _i2e1Constants.secondState);
        }
        else {
            _handleOTPError(_viewBag.resources.secondState_otp_invalid, 'otp-group', _i2e1Constants.secondState);
        }
    };

    var _submitDataVoucherPlan = function (planId, accessCode) {
        if (planId || accessCode) {
            _submitPlan(planId, accessCode)
            i2e1Api.logEvent("coupon_submit", _loginUser.nasid, { mobile: _loginUser.mobile, code: accessCode });
        } else {
            _handleOTPError(_viewBag.resources.dataVoucherDtate_invalid_access_code, 'data_voucher', _i2e1Constants.dataVoucherState);
        }
    }
    var _submitPlan = function (planId, accessCode) {
        if (!planId && !accessCode)
            return
        var username = _getMobile();
        accessCode = accessCode || _getAccessCode();
        if (accessCode != "" && accessCode != "BYPASS") {
            _viewBag.plans = null
        }

        i2e1Api.submitPlan(username, planId, {
            accessCode: accessCode,
            name: '',
            onSuccess: _submitPlan.success,
            onFailure: _submitPlan.failure,
            captcha: document.getElementById('captcha').value
        });
    };

    _submitPlan.success = function (response) {
        _preLogin(response.data.landingPage);
        i2e1Api.logEvent("coupon_valid", _loginUser.nasid, { mobile: _loginUser.mobile });
    };

    _submitPlan.failure = function (response) {
        if (response.errorCode == "LS006") {
            i2e1Api.logEvent("coupon_incompatible", _loginUser.nasid, { mobile: _loginUser.mobile });
            _handleOTPError("Incorrect code. Use WIOM Gold code.", 'data_voucher', _i2e1Constants.dataVoucherState);
        }
        else if (response.errorCode == "LS007") {
            i2e1Api.logEvent("coupon_invalid", _loginUser.nasid, { mobile: _loginUser.mobile });
            _handleOTPError("Incorrect code. Try again.", 'data_voucher', _i2e1Constants.dataVoucherState);
        }
        else if(response.errorCode == "LS008") {
            i2e1Api.logEvent("coupon_used", _loginUser.nasid, { mobile: _loginUser.mobile });
            _handleOTPError("Code used already. Try new code.", 'data_voucher', _i2e1Constants.dataVoucherState);
        }
        else if (response.errorCode == "LS009") {
            i2e1Api.logEvent("coupon_incompatible", _loginUser.nasid, { mobile: _loginUser.mobile });
            _handleOTPError("Gold code used. Enter correct code.", 'data_voucher', _i2e1Constants.dataVoucherState);
        }
        else if (response.errorCode == "LS010") {
            i2e1Api.logEvent("coupon_used_by_same_user", _loginUser.nasid, { mobile: _loginUser.mobile });
            _showPopup('<p>Your plan using this coupon is already active</p>', 'Start my Plan', function () {
                window.location.href = "http://172.16.2.1";
            });
        }
    };


    var _getAccessCode = function() {
        if (_previousState == '.second-state' || _previousState == '.plan-selection-state') {
            if ($('.data-voucher-state .chooser input:checked').attr('id') == 'free') return 'FREE';
            var voucher = document.getElementById("data_voucher").value;
            if (!voucher) _handleOTPError('Enter voucher code', 'data_voucher', _i2e1Constants.dataVoucherState);
            return document.getElementById("data_voucher").value;
        } else {
            if(!$(document.getElementById("otp_access_code")).is(':visible')) {
                return 'BYPASS'
            }
            var accessCode = document.getElementById("otp_access_code").value;
            if (!accessCode) _handleOTPError('Enter voucher code', 'otp_access_code', _i2e1Constants.secondState);
            return document.getElementById("otp_access_code").value;
        }
    }

    var _togglePlan = function (plan) {
        switch(plan) {
            case 'free':
                $('.data-voucher-state .data-voucher-area').addClass('display-none');
                break;
            case 'coupon':
                $('.data-voucher-state .data-voucher-area').removeClass('display-none');
                break;
        }
    }

    var _handleUsernameKeyPress = function(event, elem) {
        if(_viewBag.globalOtp_Enabled == "True" && _viewBag.globalOTP_code != "in") return;
        if (elem.value.length >= 10
            && event.which != 46 // delete
            && event.which != 8 // backspace
        ) {
            event.preventDefault();
        }
    }

    var otpStarPlaceholder = '';
    var _handleOTPKeyPress = function(event, elem) {
        if (elem.value.length >= 4
            && event.which != 46 // delete
            && event.which != 8 // backspace
        ) {
            event.preventDefault();
        }
    }

    var _popUpMenu = function () {
        window.location.href = "http://172.16.2.3";
    };

    var _wifi = function () {
        $('.wifi-menu-area').show();
        $('.menu-area').hide();
        $('.phone-number').show();
        $('#get_otp').show();
        $('.tnc-area').show();
        $('.footer').show();
        $('.outer-card1').show();
        };
    function _freePlanChoosen() {
        var plan = document.querySelector('[name=free-internet]:checked');
        if (plan) {
            if (plan.value == 0) {
                _submitPlan(plan.getAttribute('plan_id'));
            } else if (plan.value == -1) {
                _showPopup('<h3>Fill a Survey</h3>to get FREE internet for 7 days', 'Start Now', function() {
                    var mobile = _getMobile(), nasid = _loginUser.nasid;
                    var url = "https://interviewingap.nfieldmr.com/Interviews/pjTFm/mKTmgwxMSxtxu4GvC7XR?Respondentkey=" + (_loginUser.sessionid.substring(0, 30) + '_' + mobile + '_' + nasid) +
                        "&mobile=" + mobile + "&nasid=" + nasid;
                    window.location.href = url;
                });
            }
        }
    }
    var _buyOnline = function () {
        _swapState(_i2e1Constants.planSelectionState)
        i2e1Api.logEvent("buypln_click", _loginUser.nasid, { mobile: _loginUser.mobile });
        }
    </script>
}

<div id="generic-popup" style="display: none;
        position: fixed;
        top: 0;
        bottom: 0;
        left: 0;
        right: 0;
        background-color: #00000080;z-index: 1;">
    <div style="position: absolute; top: 33%; width: 100%;">
        <div style=" background-color: #fff; margin: 0 auto; width: 18rem; padding: 1rem;">
            <img style="width: 1rem; float: right;" id="popup-close-btn" src="/images/close.png" />
            <div style="padding: 2rem 0;" id="popup-inner-html"></div>
            <button class="primary login_button" id="popup-ok-btn">Start Now</button>
        </div>
    </div>
</div>

<form class="form first-state state-transition display-none" onkeyup="_submitThis('_generateOTP');" onsubmit="return false;">
    <div class="barking">
        <div class="loud" i18n="firstState_bark_loud">Get High Speed Internet</div>
        <div class="less-loud" i18n="firstState_bark_lessloud">Sign In Using Phone Number</div>
    </div>
    <div class="username-area phone-number">
        <div class="title" i18n="helpState_enter_mobile_number">Enter Mobile Number</div>
        <div class="groups">
            <div class="group">
                <input type="tel"
                       id="username"
                       name="username"
                       value="@(!string.IsNullOrEmpty(Model.mobile) && !Model.mobile.Contains('@')?Model.mobile: string.Empty)"
                       maxlength="10"
                       max="9999999999"
                       placeholder="e.g. 8880322222"
                       required="required" pattern=".*\S.*" />
                <label id="label-username" for="username" name="username_for"></label>
                <a onclick="$('#username').val('')[0].focus(); _fieldHandler(event, {field: 'username'});"><img style="width:.75rem;padding-top:.2rem" src="~/images/close.png" /></a>
            </div>
        </div>
    </div>


    <div class="question-area type-3 display-none"></div>
    <div cage="generate-otp">
        <input disabled type="button" id="get_otp" name="enter" class="primary login_button" value="Sign In" i18nInputValue="firstState_generate_otp" onclick="_generateOTP(false)" />
    </div>

    @{
        if (Model.clientAuthType == AuthType.PHONE_OR_NATIONAL_ID || Model.clientAuthType == AuthType.REGISTERED_MOBILE_OR_NATIONAL_ID)
        {
            <div class="tnc-area corner">
                <span class="tnc1" cage="tnc" i18n="firstState_tnc">
                    By continuing you accept <a onclick='_onTileClick("/Templates/i2e1/tnc.html")'>Terms & Conditions</a>
                </span>
            </div>
        }
        else
        {
            <div class="tnc-area">
                <span class="tnc1" cage="tnc" i18n="firstState_tnc">
                    By continuing you accept <a onclick='_onTileClick("/Templates/i2e1/tnc.html")'>Terms & Conditions</a>
                </span>
            </div>
        }


        if (Model.clientAuthType == AuthType.PHONE_OR_NATIONAL_ID || Model.clientAuthType == AuthType.REGISTERED_MOBILE_OR_NATIONAL_ID)
        {
            <div class="change_auth_link1" i18n="firstState_dont_have_phno_click_here">
                Don't have phone number? <a href="/Login/GetAuthenticationPage/?authType=NATIONAL_ID">Click here</a>
            </div>

        }
        <div class="menu-area display-none">
            <input type="button" id="menu" class="primary login_button" style="background-color:brown !important;" value="Food Menu" i18nInputValue="firstState_menu" onclick="_popUpMenu()" />
            <input type="button" id="wifi" class="primary login_button" value="Free-Wifi" i18nInputValue="firstState_wifi" onclick="_wifi()" />
        </div>
    }
</form>

@if(Model.IsWaniNas())
{
    <script>
        var _validateOTP = function (otp) {
            otp = otp || _getOTP();
            return true;
        };
    </script>
    <form class="form second-state state-transition display-none" onsubmit="return false;">
    <div class="welcome-back">
        <div class="namaste"><img src="~/images/wiom/namaste.svg" width="80px" /></div>
        <div class="greetings" i18n="secondState_welcomeback_greetings"></div>
    </div>
    <div class="barking">
        <div class="loud" i18n="secondState_bark_loud">Verify Number</div>
        <div class="less-loud">with Password (OTP) In PM WANI app</div>
    </div>
    <div class="otp-area">
        <div class="group">
            <div class="title" style="margin-bottom:2rem">Enter Password</div>
            <input style="border-bottom: 2px solid #d4d4d4;width:100%;letter-spacing: 0.2rem;" type="text" id="wani_password" required="required" pattern=".*\S.*" />
            <label for="otp-group"></label>
        </div>
    </div>

    <div id="captcha-holder" style="display:none;">
        <div>
            <span i18n="secondState_captcha">Captcha</span><br />
            <img id="captcha-img" title="captcha" src="" />
        </div>
        <div>
            <span style="opacity:0;" secondState_enter_captcha>Enter Captcha</span>
            <input id="captcha" placeholder="Enter Captcha" type="text" />
        </div>
    </div>

    <div class="question-area type-0 display-none phone-number-flow"></div>
    <div class="question-area type-5 display-none phone-number-flow"></div>
    <div cage="connect-button">
        <input type="button" id="confirm" class="primary login_button" i18nInputValue="secondState_confirm" value="Confirm" onclick="_confirm(false)" />
        <input type="button" id="connect" class="primary login_button display-none" i18nInputValue="secondState_connect" value="Connect" onclick="_connect(document.getElementById('wani_password').value)" />
    </div>
    <div class="bottom-button-group">
        <div class="back-area" onclick="_swapState(_i2e1Constants.firstState, {resend: false, reverse: true})">
            <span i18n="secondState_change">Change Number</span>
        </div>
    </div>
</form>
}
else
{
    <form class="form second-state state-transition display-none" onkeyup="_submitThis('_connect');" onsubmit="return false;">
    <div class="welcome-back">
        <div class="namaste"><img src="~/images/wiom/namaste.svg" width="80px" /></div>
        <div class="greetings" i18n="secondState_welcomeback_greetings"></div>
    </div>
    <div class="barking">
        <div class="loud" i18n="secondState_bark_loud">Verify Number</div>
        <div class="less-loud" i18n="secondState_bark_lessloud">with Password (OTP) Sent using SMS</div>
    </div>
    <div id="primary-otp-area" class="otp-area">
        <div class="otp-title title" i18n="secondState_enter_4_digit_password">Enter 4 digit password</div>
        <div class="group">
            <div id="otp-group">
                <input type="number" min="0" max="9" id="digit-1" name="digit-1" data-next="digit-2" maxlength="1" />
                <input type="number" min="0" max="9" id="digit-2" name="digit-2" data-next="digit-3" data-previous="digit-1" maxlength="1" />
                <input type="number" min="0" max="9" id="digit-3" name="digit-3" data-next="digit-4" data-previous="digit-2" maxlength="1" />
                <input type="number" min="0" max="9" id="digit-4" name="digit-4" data-next="digit-5" data-previous="digit-3" maxlength="1" />
            </div>
            <label for="otp-group"></label>
        </div>
    </div>
    <div class="access-code-area" style="display:none">
        <div class="group">
            <input type="text" id="otp_access_code" required="required" pattern=".*\S.*" />
            <label for="otp_access_code" i18n="secondState_enter_voucher_code">Enter Voucher Code</label>
        </div>
    </div>

    <div id="captcha-holder" style="display:none;">
        <div>
            <span i18n="secondState_captcha">Captcha</span><br />
            <img id="captcha-img" title="captcha" src="" />
        </div>
        <div>
            <span style="opacity:0;" secondState_enter_captcha>Enter Captcha</span>
            <input id="captcha" placeholder="Enter Captcha" type="text" />
        </div>
    </div>

    <div class="questions-confirm-area display-none">
        <span class="tick"><img src="~/images/wiom/done.svg" /></span>
        <div class="loud" i18n="secondState_questions_loud">Get Free Internet today</div>
        <div class="less-loud" i18n="secondState_questions_lessloud">Answer simple questions</div>
        <div class="questions-confirm-cta">
            <input type="button" class="primary login_button" i18nInputValue="secondState_questions_cta_okay" onclick="_answerQuestions()" value="Okay" />
        </div>
    </div>

    <div class="question-area type-0 display-none phone-number-flow"></div>
    <div class="question-area type-5 display-none phone-number-flow"></div>
    <div cage="connect-button">
        <input type="button" id="confirm" class="primary login_button" i18nInputValue="secondState_confirm" value="Confirm" onclick="_confirm(false)" />
        <input type="button" id="connect" class="primary login_button display-none" i18nInputValue="secondState_connect" value="Connect" onclick="_connect(false)" />
    </div>
    @if (Model.clientAuthType == AuthType.PHONE_OR_DATA_VOUCHER_WITHOUT_OTP)
    {
        <span class="change_auth_link" i18n="secondState_not_getting_otp_click_here">Not Getting OTP? <a onclick="$('#otp_access_code').show();$('#otp').val('1234').hide()">Click here</a></span>
    }
    <div class="bottom-button-group">
        <div class="back-area" onclick="_swapState(_i2e1Constants.firstState, {resend: false, reverse: true})">
            <span i18n="secondState_change">Change Number</span>
        </div>
        <div id="primary-resend-area" class="resend-otp-area">
            <input type="button" id="resend-otp" class="small_button primary" disabled i18nInputValue="secondState_resend" value="Resend" onclick="_generateOTP(true)" />
        </div>
    </div>
</form>
}


<form class="form data-voucher-state state-transition display-none" onsubmit="return false;">
    <h3 class="header" i18n="dataVoucherState_free_session_expired" style="text-align:center; color: #484848;font-size:24px">Buy Coupon</h3>
    <h3 id="buy_coupon_from" class="display-none" i18n="dataVoucherState_buy_coupon_from" style="margin-bottom:2rem;font-size: 1rem;text-align: center;" onclick="_buyFrom(true)">for UNLIMITED internet from your<br /><span style="color: rgb(244, 105, 58)">nearest shop ></span></h3>
    <div class="data-voucher-area material-input">
        <div class="title">Enter Coupon Code</div>
        <div class="group">
            <input type="text" id="data_voucher" required="required" pattern=".*\S.*" placeholder="e.g. WIOM1234" />
            <label for="data_voucher" name="data_voucher_for"></label>
        </div>
    </div>
    <img src="~/images/help_video.png" style="width: 2rem;position: relative;left: 90%;top: .5rem;" onclick="_toggleHelpVideo()" />

    <div cage="connect-button">
        <input type="button" class="primary login_button" value="Connect" i18nInputValue="dataVoucherState_connect" onclick="_submitDataVoucherPlan('', $('#data_voucher').val())" />
    </div>
    <div class="planMacMappingDiv" style="display:none">
        <p style="text-align:center;font-size:0.75rem;">
            *Existing Plan is being used on other device/s<br />
            <a style="color:#f25f58" onclick="$('#existing-plan-overlay').show()">Know More</a>
        </p>
    </div>
    <div id="buy-online-container" style="text-align:center;font-size:0.875rem;margin:1.5rem 0;">
        <hr style="width:100%" />
        <span style=" font-size: 1rem; position: relative; top: -1rem; background: white; padding: 7px;">or</span>
        <br />
        <h3 onclick="_buyOnline()">
            <img src="~/images/kantar/survey_icon.svg" />
            <a style="position: relative;top: -0.5rem;margin-left: 0.5rem;color: #f25f58">Buy Plans</a>
        </h3>
    </div>
    <div id="free-plan-overlay" style="position: fixed;z-index:1;
        font-size: 0.875rem;
        left: 0;right:0;bottom:0;
        display: none;">
        <img style="position:absolute;left:1rem;" src="~/images/kantar/free.svg" />
        <div style="background-color: #214294; padding: 0.5rem 1rem 0.5rem 5.5rem; margin: 0.5rem; color: white;">
            Free Internet*
            <button class="primary login_button" onclick="_swapState(_i2e1Constants.freePlanState)" style="width: 8rem; float: right; height: 2.5rem;">Get Now</button>
            <p style="font-size:0.5rem;">*T&C Apply</p>
        </div>
    </div>
</form>

<form class="form free-plan-state state-transition display-none" onsubmit="return false;">
    <h3 i18n="dataVoucherState_choose_your_plan" style="text-align:center">Choose your plan</h3>
    <ul class="chooser">
        @foreach (var plan in freePlans)
        {
            if (plan.price == 0)
            {
                <li plan_li="@plan.id">
                    @{<input type="radio" name="free-internet" plan_id="@plan.id" id="<EMAIL>" value="@plan.price" required />}
                        @{<label for="<EMAIL>">@plan.name</label>}
                    </li>
                }
            }
        <li id="suvery-plan-li" plan_li="-1" style="display:none">
            <input type="radio" name="free-internet" plan_id="-1" id="plan_-1" value="-1" />
            <label for="plan_-1">Free Unlimited Internet for 7 Days</label>
        </li>
    </ul>
    <div cage="connect-button">
        <input type="submit" class="primary login_button" value="Confirm" onclick="_freePlanChoosen(false)" />
    </div>
</form>

@{
    if (plans != null && plans.Count >= 1)
    {
        <div class="form plan-selection-state state-transition display-none">
            <form class="plans plan-chooser" onkeyup="_submitThis('_selectPlan');" onsubmit="return false;">
                <h3 i18n="dataVoucherState_choose_your_plan" style="text-align:center">Choose your plan</h3>
                <ul class="chooser">
                    @foreach (var plan in plans)
                    {
                        <li plan_li="@plan.id" charges="@plan.price">
                            @{<input onclick="i2e1Api.logEvent('plan_select', _loginUser.nasid, { mobile: _loginUser.mobile, planId: @plan.id, price: @plan.price });" type="radio" name="wani-internet" plan_id="@plan.id" id="<EMAIL>" value="@plan.price" />}
                            @{<label for="<EMAIL>">@plan.name</label>}
                        </li>
                    }
                </ul>
                <div cage="connect-button">
                    <input type="button" class="primary login_button" value="Connect" i18nInputValue="planSelectionState_selectPlan" onclick="_selectPlan(false)" />
                </div>
                <div class="planMacMappingDiv" style="display:none">
                    <p style="text-align:center;font-size:0.75rem;">
                        *Existing Plan is being used on other device/s<br />
                        <a style="color:#f25f58" onclick="$('#existing-plan-overlay').show()">Know More</a>
                    </p>
                </div>
            </form>
            <form class="plans plan-submitter display-none" onkeyup="_submitThis('_payVia');" onsubmit="return false;">
                <h3 style="font-size: 1.5rem;">Choose the mode of Payment</h3>
                <div cage="connect-button" id="pay_via_cash" class="payment-btn">
                    <button class="primary login_button btn-grp" onclick="_payVia('cash')">
                        <img src="~/images/wiom/wallet.svg" style="height: 1.5rem;padding: .25rem;" />
                        <span i18nInputValue="planSelectionState_cash">Pay Cash</span>
                    </button>
                </div>
                <div cage="connect-button" id="pay_via_upi" class="payment-btn" style="display:none">
                    <button style="margin-top: 1rem;" class="primary login_button btn-grp"
                            onclick="$('[name=pay_amount]').html('₹ ' + _selectedPlan[0].price);_toPlanSelectionInnerState('.already-paid-upi')">
                        <img src="~/images/wiom/upi.svg" />
                        <span i18nInputValue="planSelectionState_upi">Pay UPI</span>
                    </button>
                </div>
                <div cage="connect-button" id="pay_via_online" class="payment-btn" style="display:none">
                    <button style="margin-top: 1rem;" class="primary login_button btn-grp"
                            onclick="$('[name=pay_amount]').html('₹ ' + _selectedPlan[0].price); _payVia('online')">
                        <img src="~/images/wiom/wallet.svg" style="height: 1.5rem;padding: .25rem;" />
                        <span i18nInputValue="planSelectionState_upi">Pay Online</span>
                    </button>
                </div>
            </form>
            <form class="plans already-paid-cash display-none" onsubmit="return false;">
                <h3 style="font-size: 3rem;color: rgb(244, 105, 58);margin-bottom: 1rem;" name="pay_amount"></h3>
                <h3 style="font-size: 1.5rem;font-weight: 100;">Pay cash</h3>
                <h3 style="font-size: 1.5rem;font-weight: 100;" name="merchant_name"></h3>
                <div cage="connect-button">
                    <input type="button" class="primary login_button" value="I have Paid, Connect Now" i18nInputValue="planSelectionState_alreadypaid" onclick="_verifyPayment()" />
                </div>
            </form>

            <form class="plans already-paid-upi display-none" onsubmit="return false;">
                <h3 style="font-size: 1.5rem;font-weight: 100;margin-bottom: .5rem">Pay <span style="color: rgb(244, 105, 58);" name="pay_amount"></span> to</h3>
                <h3 style="font-size: 1.5rem;font-weight: 600;" name="merchant_name"></h3>
                <div style="text-align: start;font-size: .9rem;">
                    <p style="display:flex;justify-content:space-between"><span>1. Use UPI ID <span name="upi_id"></span></span><img style="width:1.2rem;vertical-align:bottom" src="~/images/copy_red.png" onclick="_copy(_viewBag.paymentModes.upiId)" /></p>
                    <p>2. Click below after payment is done</p>
                </div>
                <div cage="connect-button">
                    <input type="button" class="primary login_button" value="Paying via UPI" i18nInputValue="planSelectionState_alreadypaidupi" onclick="_payVia('upi');" />
                </div>
            </form>
            <form class="plans already-paid-online display-none" onsubmit="return false;">
                <h3 style="font-size: 1.5rem;font-weight: 600;">We have sent you an SMS with the payment link</h3>
                <img src="/images/open_sms.png"/>
                <p style="color: #666666; font-size: 12px; text-align: left;">
                    <img style="position: relative; top: 0.25rem; margin-right: 0.5rem;" src="/images/info.png" />In the SMS, click on the link to complete payment
                </p>
            </form>

            <form class="plans plan-confirmation display-none" onsubmit="return false;">
                <div style="font-size:3rem;color:#f4693a;text-align:center;font-weight:600;">Great!</div>
                <div style="font-size:1.3rem;text-align:center;font-weight:600;">Waiting for the merchant to confirm…</div>
                <div style="border:1px solid #f4693a;padding:2px;margin:1rem;border-radius:0.5rem;">
                    <div style="height:0.6rem;background:#f4693a;border-radius:0.5rem;width:50%"></div>
                </div>
                <div style="font-size:1rem;text-align:center;margin-top:3rem;">Taking too long?</div>
                <div cage="connect-button" style="margin-top:0.5rem;">
                    <input type="button" class="primary login_button" value="Request again" i18nInputValue="planSelectionState_request_again" onclick="_verifyPayment()" />
                </div>
            </form>
        </div>
        <div id="existing-plan-overlay">
            <div style="position: absolute; top: 33%; width: 100%;">
                <div style=" background-color: #fff; margin: 0 auto; width: 18rem; padding: 1rem;text-align:center">
                    <h2>Existing Plan</h2>
                    <h4>Plan: <span id="device_plan"></span></h4>
                    <div style="font-size:0.75rem">
                        <hr />
                        <div>
                            <div class="float-div" style="width:50%;">Devices Allowed <br /><b id="devices_allowed">1</b></div>
                            <div class="float-div" style="width:50%;">Date<br /><b id="plan_start_date"></b></div>
                        </div>
                        <hr />
                        <div id="devices_connected"></div>
                    </div>
                    <button style="margin-top:3rem;" class="primary login_button" onclick="$('#existing-plan-overlay').hide()">OK</button>
                </div>
            </div>
        </div>

        <script>
            _verifyPayment = function () {
                i2e1Api.doAjax("/Login/confirmPlanPayment", { fdmId: _viewBag.fdmId }, function (res) {
                    if (res.status == 0) {
                        _preLogin(res.data.landingPage);
                    } else {
                        _toPlanSelectionInnerState('.plan-confirmation')
                    }
                });
            }

            i2e1Api.getOperator(function (response) {
                if (response && response.shopName)
                    $('[name=merchant_name]').html('to <span style="font-weight:bold;">' + response.shopName + '</span>')
            });
            _gotoCoupon = function () {
                _swapState(_i2e1Constants.dataVoucherState)
            }
            _selectedPlan = null;
            _toPlanSelectionInnerState = function (state) {
                if (state == '.plan-submitter') {
                    _backFN = function () {
                        _toPlanSelectionInnerState('.plan-chooser')
                    }
                }
                $('.plan-selection-state form').addClass('display-none')
                $('.plan-selection-state ' + state).removeClass('display-none')
                var visiblePaymentModes = $('.plan-selection-state ' + state + " .payment-btn:visible");
                if (visiblePaymentModes.length == 1)
                    visiblePaymentModes.find('button').click();
            }
            _selectPlan = function () {
                var plan = document.querySelector('[name=wani-internet]:checked')
                if (plan) {
                    i2e1Api.logEvent("plan_submit", _loginUser.nasid, { mobile: _loginUser.mobile });
                    _selectedPlan = _viewBag.plans.filter(function (p) {
                        if ('plan_' + p.id == plan.id)
                            return true;
                    })
                    if (plan.value == 0) {
                        _submitPlan(plan.getAttribute('plan_id'));
                    } else {
                        _toPlanSelectionInnerState('.plan-submitter');
                    }
                }
            }
            _payVia = function (payBy, swapToPayment) {
                i2e1Api.notifyOperator(_selectedPlan[0], {
                    payMode: payBy,
                    upiId: _viewBag.paymentModes && _viewBag.paymentModes.upiId,
                    onSuccess: function (response) {
                        if (response.status == 0) {
                            _payVia.success(response, payBy, swapToPayment);
                        } else if (response.status == 1) {
                            $('#pay_via_cash_finally').css({ 'display': 'block' });
                        } else {
                            _payVia.failure(response);
                        }
                    },
                    onFailure: function (response) {
                        _payVia.failure(response);
                    }
                });
            }

            _payVia.success = function (response, payBy, swapToPayment) {
                if (response.status == 1)
                    _swapState(_i2e1Constants.errorState, response.msg);
                else if (response.status == 0) {
                    _backFN = null;
                    if (response.data) {
                        window.landingPage = response.data.landingPage;
                        _viewBag.fdmId = response.data.fdmId;
                    }
                    if (swapToPayment)
                        _swapState(_i2e1Constants.planSelectionState);
                    $('[name=pay_amount]').html('₹ ' + _selectedPlan[0].price)
                    if (response.data.initiatePayment) {
                        _preLogin(window.landingPage, 'payment');
                    }
                    else {
                        _toPlanSelectionInnerState('.already-paid-' + payBy);
                    }
                    if (payBy == 'upi') {
                        _preLogin(response.data.landingPage);
                    }
                }
            }
            _payVia.failure = function (response) {
                _swapState(_i2e1Constants.errorState, response);
                $('#pay_via_cash_finally').css({ 'display': 'block' });
            }
        </script>
    }
}

<script>
    $('#otp-group').find('input').each(function () {
        $(this).on('keyup', function (e) {
            var parent = $($(this).parent());
            var prev = parent.find('input#' + $(this).data('previous'));
            var next = parent.find('input#' + $(this).data('next'));
            if (e.keyCode === 8 || e.keyCode === 37) {
                if (prev.length) {
                    $(prev).select();
                }
                return;
            } else if ((e.keyCode >= 48 && e.keyCode <= 57) || (e.keyCode >= 65 && e.keyCode <= 90) || (e.keyCode >= 96 && e.keyCode <= 105) || e.keyCode === 39) {
                if (next.length) {
                    $(next).select();
                }
                return;
            }
            var value = $(this).val();
            var valid = ["0", "1", "2", "3", "4", "5", "6", "7", "8", "9"];
            if (value.length == 0) {
                if (prev.length) {
                    $(prev).select();
                }
                return;
            }
            if (value.length > 1) {
                $(this)[0].value = $(this).val().substring(0, 1);
                value = $(this).val();
            }
            if (value.length == 1) {
                if (valid.indexOf(value) < 0) {
                    $(this)[0].value = "";
                } else if (next.length) {
                    $(next).select();
                }
                return;
            }
        });
    });
</script>
