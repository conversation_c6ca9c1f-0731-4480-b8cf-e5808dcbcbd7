@using I2E1_Message.Models
@using i2e1_core.Models
@using i2e1_core.Utilities;
@using i2e1_basics.Utilities
@using I2E1_Message.Utils
@using wiom_login_share.Models
<!DOCTYPE html>
<html>
<head>
    <title>i2e1 login</title>
    <!-- The below comment is required for troubleshooting .Kindly dont remove it as it wont do any harm -->
    <!-- QWERTPOIUY123456789 -->
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0, user-scalable=no" />
    <style>
        #customBtn {
            display: inline-block;
            background-color: #dc483b;
            color: #444;
            border-radius: 5px;
            color: white;
        }
    </style>
    <script>
        var googleUser = {};
        var initGoogle = function () {
            gapi.load('auth2', function () {
                // Retrieve the singleton for the GoogleAuth library and set up the client.
                auth2 = gapi.auth2.init({
                    client_id: '379428667479-v7h3r1r3t3gahb2fa37lichpmta92g0l.apps.googleusercontent.com'
                });
                attachSignin(document.getElementById('customBtn'));
            });
        };

        function attachSignin(element) {
            console.log(element.id);
            auth2.attachClickHandler(element, {},
                function (googleUser) {
                    onSignIn(googleUser);
                }, function (error) {
                    alert(JSON.stringify(error, undefined, 2));
                });
        }

        function onSignIn(googleUser) {
            var success = function (response) {
                _preLogin(response.data.landingPage);
            };

            var failure = function (response) {
                progress(50, 500);
                _showCaptcha(response.data);

                if (response.msg.indexOf('Access') > -1)
                    _handleOTPError(response.msg, 'otp_access_code');
                else
                    _handleOTPError(response.msg, 'otp');
            };

            i2e1Api.submitToken(googleUser.getAuthResponse().id_token, "google", {
                questions: _i2e1Ques,
                onSuccess: success,
                onFailure: failure
            });
        };
    </script>




    <script src="https://apis.google.com/js/platform.js?onload=initGoogle" async defer></script>

    <link rel="stylesheet" type="text/css" href="/Templates/i2e1/site.css" />
    <link rel="shortcut icon" href="images/favicon.ico" type="image/x-icon">
    @*@System.Web.Optimization.Scripts.Render("~/bundles/newwebsite")*@
    @{
        Html.RenderPartial("~/Views/Login/Shared/WebSocketEvents.cshtml");
    }
    @if (ViewBag.globalOTP != null)
    {
        <link rel="stylesheet" href="~/jsLibs/countrycode/intlTelInput.css">
        <script src="~/jsLibs/countrycode/intlTelInput.js"></script>
    }
    <script type="text/javascript">
        @{
            List<Template> templates = new List<Template>();
            int baseTemplate = Model.GetBaseTemplateId();
            if(baseTemplate == 0) {
                Model.templateid.Add(int.Parse(I2e1ConfigurationManager.GetInstance().GetSetting("i2e1.DefaultTemplateId")));
            }

            for(int i = 0 ; i < Model.templateid.Count; ++i)
            {
                if (Model.templateid[i] != 0)
                {
                    templates.Add(CacheHelper.GetInstance().GetTemplateContent(Model.templateid[i]));
                }
            }
        }

        var _templates = @Html.Raw(Newtonsoft.Json.JsonConvert.SerializeObject(templates));
        var _loginUser = @Html.Raw(Newtonsoft.Json.JsonConvert.SerializeObject(Model));
        var _progressbar;
        var _viewBag = {
            sessionExpired: '@ViewBag.sessionExpired',
            dataExhausted: '@ViewBag.dataExhausted',
            facebookPage: '@(ViewBag.FbPage == null ? "" : ViewBag.FbPage)',
            facebookCheckin: '@((ViewBag.FbCheckin == null || ViewBag.FbCheckin == "0") ? "" : ViewBag.FbCheckin)',
            facebookAppId: '@I2e1ConfigurationManager.GetInstance().GetSetting("facebookAppId")',
            loginLogo: '@(ViewBag.loginLogo == null ? null : ViewBag.loginLogo)'
        }

        var _generateOTP = function (resend) {
            var username = document.getElementById("username");

            var success = function (response) {
                _swapState('second', { resend: resend });
            }

            var failure = function (response) {
                _handleOTPError(response.msg, 'username');
            }

            if(_processAnswers(3)) return;

            if (_validateUsername()) {
                progress(25, 5000);
                //success();
                i2e1Api.generateOTP(username.value.replace(/ /g,''), {
                    isresend: resend,
                    questions: _i2e1Ques,
                    onSuccess: success,
                    onFailure: failure,
                    clientAuthType: _mapClientType(_loginUser.clientAuthType),
                    globalOTP: _viewBag.globalOTP[0] == "True" ? true: false
                });
            } else {
                failure({msg: 'Invalid phone number'});
            }
        };

        var _connect = function () {
            var username = document.getElementById("username").value;
            var otpField = document.getElementById("otp");
            var accessCode = document.getElementById("otp_access_code").value;

            if (!_validateOTP()) {
                otpField.className += " input-error";
                return;
            }

            var success = function (response) {
                _preLogin(response.data.landingPage);
            };

            var _showCaptcha = function (captchaUrl) {
                if (captchaUrl) {
                    document.getElementById('captcha-holder').style.display = 'inline-block';
                    captchaUrl && (document.getElementById('captcha-img').src = captchaUrl);
                } else {
                    document.getElementById('captcha-holder').style.display = 'none';
                }
            };
            var failure = function (response) {
                progress(50, 500);
                _showCaptcha(response.data);

                if(response.msg.indexOf('Access') > -1)
                    _handleOTPError(response.msg, 'otp_access_code');
                else
                    _handleOTPError(response.msg, 'otp');
            };

            if(_processAnswers(0)) return;

            i2e1Api.submitOTP(username, otpField.value, {
                accessCode: accessCode,
                name: '',
                questions: _i2e1Ques,
                onSuccess: success,
                onFailure: failure,
                captcha: document.getElementById('captcha').value,
                doLogin: _doLoginRequired()
            });
        };

        function statusChangeCallback(response){
            var success = function (response) {
                _preLogin(response.data.landingPage);
            };
            var failure = function (response) {
                progress(50, 500);
                _showCaptcha(response.data);

                if(response.msg.indexOf('Access') > -1)
                    _handleOTPError(response.msg, 'otp_access_code');
                else
                    _handleOTPError(response.msg, 'otp');
            };
            console.log(response);
            i2e1Api.submitToken(response.authResponse.accessToken, "facebook", {
                questions: _i2e1Ques,
                onSuccess: success,
                onFailure: failure
            });
        }

        function checkLoginState() {
            FB.getLoginStatus(function(response) {
                statusChangeCallback(response);
            });
        }
    </script>
</head>
<body onload="_bodyOnload()">
    <script>
        window.fbAsyncInit = function() {
            FB.init({
                appId      : '325123661215346',
                xfbml      : true,
                version    : 'v2.6'
            });
            FB.AppEvents.logPageView();
        };
        (function(d, s, id){
            var js, fjs = d.getElementsByTagName(s)[0];
            if (d.getElementById(id)) {return;}
            js = d.createElement(s); js.id = id;
            js.src = "//connect.facebook.net/en_US/sdk.js";
            fjs.parentNode.insertBefore(js, fjs);
        }(document, 'script', 'facebook-jssdk'));
    </script>
    <div class="top-container display-none">
        <div class="outer-card" cage="outer-header">
            <img id="outer-card-img" class="pixelated" src="../../images/logo.png" />
        </div>
        <div class="login container">
            <div id="progressBar"><div></div></div>
            <div class="user-header" cage="user-header"></div>
            <form cage="inner-header" class="inner-header">Get <img class="wifi-img" src="~/images/grey_wifi.png" /><span class="rainbow" style="display:none;"></span> with i2e1</form>
            <form class="form first-state" onkeyup="_submitThis('_generateOTP');" onsubmit="return false;">
                <span cage="mobile-helper"></span>
                <div for="username" class="inline-error"></div>
                <div class="question-area type-3 display-none"></div>
                <h4>Login using your social accounts to access free internet</h4>

                <a onclick="FB.login(statusChangeCallback, {scope:'public_profile,email'})" style="background-color: #3b5998;color: white;display: inline-block;border-radius: 5px;margin: 15px;">
                    <span style="border-right: 1px solid #fff;width: 50px;height: 35px;display: inline-block;padding-top: 10px;">
                        <img src="/images/facebook.png" border="0" style="width:20px">
                    </span>
                    <span style="padding: 0 20px;">Log In with Facebook</span>
                </a>
                <div id="googleLogin" style="padding-left:40px"></div>
                <div id="customBtn" class="customGPlusSignIn">
                    <span class="icon"></span>
                    <span class="buttonText">Sign in Using Google</span>
                </div>

                <br/>
                <div cage="generate-otp">
                    <span class="tnc">By clicking on the buttons above you agree to i2e1's <a target="_blank" href="~/Templates/i2e1/tnc.html">Terms & Conditions</a></span>
                </div>
                @{
                    if (Model.clientAuthType == AuthType.PHONE_OR_NATIONAL_ID || Model.clientAuthType == AuthType.REGISTERED_MOBILE_OR_NATIONAL_ID)
                    {
                        <span class="change_auth_link">Don't have phone number? <a href="/Login/GetAuthenticationPage/?authType=NATIONAL_ID">Click here</a></span>
                    }
                }
                @{
                    if (ViewBag.GuestAuthType != null && Model.clientAuthType != ViewBag.GuestAuthType && ViewBag.GuestAuthType != AuthType.DISABLE)
                    {
                        <span class="change_auth_link"><a href="/Login/GetAuthenticationPage/?authType=@ViewBag.GuestAuthType">Sign In as Guest</a></span>
                    }
                }
            </form>
            <form class="form second-state" style="display: none;" onkeyup="_submitThis('_connect');" onsubmit="return false;">
                <div id="show_username"></div>
                <div class="button-group">
                    <img src="~/images/arrow_back_grey600_24dp.png" id="back" onclick="_swapState('first', {resend: false})" />
                    <input type="button" id="resend-otp" class="small_button" value="Resend OTP" disabled onclick="_generateOTP(true)" />
                </div>
                <div for="otp" class="inline-error"></div>
                <input type="number" id="otp" value="" placeholder="Enter OTP" />
                <div for="otp_access_code" class="inline-error"></div>
                <input type="text" id="otp_access_code" style="display:none;text-align:center;" value="" placeholder="Enter access code" />
                <div id="captcha-holder" style="display:none;">
                    <div>
                        <span>Captcha</span><br />
                        <img id="captcha-img" title="captcha" src="" />
                    </div>
                    <div>
                        <span style="opacity:0;">Enter Captcha</span>
                        <input id="captcha" placeholder="Enter Captcha" type="text" />
                    </div>
                </div>
                <div class="question-area display-none"></div>
                <div cage="connect-button">
                    <input type="button" id="connect" class="primary" value="Connect" onclick="_connect(false)" />
                </div>
            </form>
            <form class="form voucher-state" style="display: none;">
                <input id="skipVoucher1" class="skip-voucher small_button" type="button" value="Skip" />
                <div class="campaigns"></div>
                <input id="skipVoucher2" class="skip-voucher small_button" type="button" value="Skip" />
            </form>
            <form class="form social-state" style="display: none;" onkeyup="_submitThis('_facebook_share');" onsubmit="return false;">
                <div class="social">
                    <div class="inline-error"></div>
                    <img id="client_logo" />
                    <button class="primary fb-share" type="button" onclick="_facebook_share();"><img src="~/images/facebook.png" /> <div>Share / Checkin</div></button>
                </div>
                <input id="skipSocial" class="small_button" style="display:none;" type="button" value="Skip" />
            </form>
            <form class="form redirecting-state" style="display: none;">
                <div class="connected">Redirecting...</div>
            </form>
            <form class="form error-state" style="display: none;">
                <div id="errorMsg" class="errorSpan"></div>
                <div class="limit-exhausted" style="display: none;"></div>
                <div class="time-exhausted" style="display: none;"></div>
                <div class="user-blocked" style="display: none;"></div>
                <div class="go-back-to-login">
                    <a onclick="_swapState('first');">login with other number</a>
                </div>
            </form>
            <div class="custom-state"></div>
            <hr />
            <div class="footer fade-text">
                <span><a target="_blank" href="http://www.i2e1.com">About</a></span>
                <img class="pixelated" src="~/images/logo.png" />
                <span><a target="_blank" href="~/Templates/i2e1/help.html">Help</a></span>
            </div>
        </div>
    </div>
    <div id="loader">
        <div class="deactivate"></div>
        <div class="img-section">
            <div>
                <img src="~/images/wifi_loader_84X84.gif" />
            </div>
        </div>
    </div>
    <div id="internal-templates display-none">
    </div>
    <div id="forignTemplates" class="display-none"></div>
</body>
</html>