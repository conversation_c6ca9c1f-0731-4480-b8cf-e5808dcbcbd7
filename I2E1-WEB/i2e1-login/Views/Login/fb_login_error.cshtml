@using i2e1_core.Models
@using I2E1_Message.Utils
@using I2E1_WEB;
@model wiom_login_share.Models.User;
<!DOCTYPE html>
<html>
<head>
    <title>i2e1 login</title>
    <meta name="theme-color" content="#ffffff" />
    <meta name="accept-language" content="@System.Threading.Thread.CurrentThread.CurrentCulture.ToString()" />
    <!-- The below comment is required for troubleshooting .Kindly dont remove it as it wont do any harm -->
    <!-- QWERTPOIUY123456789 -->
    <meta name="viewport" content="width=device-width, height=device-height, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0, user-scalable=no" />
    <link rel="stylesheet" type="text/css" href="@Util.GetUrlWithVersion("/Templates/i2e1/site.css")" />
    <link rel="shortcut icon" href="images/favicon.ico" type="image/x-icon">
    

    
</head>
<body>


    <div class="top-container">
        <div class="login container">
            <form class="form error-state state-transition">
                <div id="errorMsg" class="errorSpan">
                    We are not able to log you in facebook due to following:
                    <p><b>Error:</b> @ViewBag.FBLoginError</p>
                    <p><b>Description:</b> @ViewBag.FBLoginErrorDesc</p>
                    <p><b>Reason:</b> @ViewBag.FBLoginErrorReason</p>                     
                </div>
                <div class="go-back-to-login">
                    <a href="http://********">Try log in again</a>
                </div>
            </form>
        </div>
    </div>
</body>
</html>
