@using i2e1_core.Models
@using I2E1_WEB.Models
@using wiom_login_share.Models
@model wiom_login_share.Models.User;
@{
    Layout = "~/Views/Login/layout_base.cshtml";
}
@section ScriptSection{
    <script type="text/javascript">
        var _loginUser = @Html.Raw(Newtonsoft.Json.JsonConvert.SerializeObject(Model));

        var _lastNameMsg = "Last Name";
        var _getMobile = function () {
            var room_no = document.getElementById("room_no").value;
            var last_name = document.getElementById("last_name").value;

            if (!room_no || !last_name) return null;
            return last_name + '@@' + room_no;
        }

        var _validateUsername = function () {
            var room_no = document.getElementById("room_no").value;
            if (!room_no) {
                _handleOTPError("Room number", 'room_no', _i2e1Constants.firstState);
                return;
            }
            var last_name = document.getElementById("last_name").value;
            if (!last_name) _handleOTPError(_lastNameMsg, 'last_name', _i2e1Constants.firstState);

            if (room_no && last_name) return true;
        }

        var _connect = function () {
            _logMPEvent(_i2e1Constants.firstState, { event: _i2e1Constants.lastNameRoomSigninClicked, last_name_room_no: true });
            _reset();

            if (_validateUsername()) {
                var username = _getMobile();
                var success = function (response) {
                    if (response.status == 1) {
                        _swapState(_i2e1Constants.errorState, response.msg);
                    } else {
                        _swapState(_i2e1Constants.questionState, { userProfile: response.data.userProfile, otpResponse: response.data.otpResponse });
                        document.getElementById('question_submit').addEventListener('click', function () {
                            _question_proceed(username, response.data.otpResponse);
                        });
                    }
                }

                var failure = function (response) {
                    _handleOTPError(response.msg, null, _i2e1Constants.firstState);
                }

                if(_processAnswers(3)) return;

                i2e1Api.generateOTP(username, {
                    questions: _i2e1Ques,
                    clientAuthType: _mapClientType(_loginUser.clientAuthType),
                    onSuccess: success,
                    onFailure: failure
                });
            } //else _handleOTPError('Invalid user', null, _i2e1Constants.firstState);

        };

    </script>
}
<form class="form first-state state-transition display-none" onkeyup="_submitThis('_generateOTP');" onsubmit="return false;">
    <p class="general-msg display-none"></p>
    <div class="input-area">
        <div class="group">
            <label for="room_no_user"></label>
            <div class="roomno-area">
                <div class="group">
                    <input type="text" id="room_no" required="required" pattern=".*\S.*" />
                    <label for="room_no" i18n="firstState_room_no">Room no.</label>
                </div>
            </div>
            <div class="last-name-area">
                <div class="group">
                    <input type="text" id="last_name" required="required" pattern=".*\S.*" />
                    <label for="last_name" i18n="firstState_last_name">Last name</label>
                </div>
            </div>
        </div>
    </div>

    <div class="question-area type-3 display-none"></div>
    <div cage="room-no-connect-button">
        <input type="button" id="connect" class="primary login_button" i18nInputValue="secondState_connect" value="Connect" onclick="_connect(false)" />
    </div>

    @{
        if (ViewBag.GuestAuthType != null && Model.clientAuthType != ViewBag.GuestAuthType && ViewBag.GuestAuthType != AuthType.DISABLE)
        {
            <div class="tnc-area corner">
                <span class="tnc1" cage="tnc" i18n="firstState_tnc">
                    By continuing you accept <a onclick='_onTileClick("/Templates/i2e1/tnc.html")'>Terms & Conditions</a>
                </span>
            </div>
            <div class="change_auth_link1">
                <a href="/Login/GetAuthenticationPage/?authType=@ViewBag.GuestAuthType">
                    <span cage="guest-mode" i18n="firstState_sign_in_as_guest">Sign In as Guest</span>
                </a>
            </div>
        }
        else
        {
            <div class="tnc-area">
                <span class="tnc1" cage="tnc" i18n="firstState_tnc">
                    By continuing you accept <a onclick='_onTileClick("/Templates/i2e1/tnc.html")'>Terms & Conditions</a>
                </span>
            </div>
        }
    }
</form>

<form class="form second-state state-transition display-none" onkeyup="_submitThis('_question_proceed');" onsubmit="return false;">
    <div class="button-group">
        <div>
            <div class="back-area" onclick="_swapState(_i2e1Constants.firstState, {resend: false, reverse: true})">
                <img title="Go back to change phone number" i18nTitle="secondState_back" src="../../images/back.png" />
            </div>
        </div>
    </div>
    <div class="question-area type-0 display-none"></div>
    <div class="question-area type-5 display-none"></div>
    <div cage="connect-button">
        <input type="button" id="question_submit" class="primary" i18nInputValue="secondState_connect" value="Connect" />
    </div>
</form>
