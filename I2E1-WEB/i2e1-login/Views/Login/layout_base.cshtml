@using i2e1_core.Models
@using I2E1_Message.Utils
@using i2e1_core.Utilities
@using i2e1_basics.Utilities
@using System.Threading 
@model wiom_login_share.Models.User;
<!DOCTYPE html>
<html>
<head>
    <title>i2e1 login</title>
    <meta name="theme-color" content="#ffffff" />
    <meta name="accept-language" content="@System.Threading.Thread.CurrentThread.CurrentCulture.ToString()" />
    <!-- The below comment is required for troubleshooting .Kindly dont remove it as it wont do any harm -->
    <!-- QWERTPOIUY123456789 -->
    <meta name="viewport" content="width=device-width, height=device-height, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0, user-scalable=no" />
    <link rel="stylesheet" type="text/css" href="@Util.GetUrlWithVersion("/Templates/i2e1/site.css")" />
    <link rel="shortcut icon" href="/images/favicon.ico" type="image/x-icon">
    <script src="@Util.GetUrlWithVersion("/jsLibs/jquery.js")"></script>
    @{
        Html.RenderPartial("~/Views/Login/Shared/WebSocketEvents.cshtml");

        var lp = ViewBag.locale;
        if (!string.IsNullOrEmpty(lp))
        {
            var culture = new System.Globalization.CultureInfo(lp);
            Thread.CurrentThread.CurrentCulture = culture;
            Thread.CurrentThread.CurrentUICulture = culture;
        }
        else{
             var culture = new System.Globalization.CultureInfo("en");
            Thread.CurrentThread.CurrentCulture = culture;
            Thread.CurrentThread.CurrentUICulture = culture;
        }
        Dictionary<string, string> resourceDict = new Dictionary<string, string>();

        resourceDict["Culture"] = System.Threading.Thread.CurrentThread.CurrentUICulture.Name;
    }
    <script src="@Util.GetUrlWithVersion("/Templates/i2e1/i2e1-sdk.js")"></script>
    <script src="@Util.GetUrlWithVersion("/Templates/i2e1/template-utils.js")"></script>
    <script type="text/javascript">
    var _viewBag = {
        redirectUrl: '@(ViewBag.redirectUrl == null ? "" : ViewBag.redirectUrl)',
        loginResponse: @Html.Raw(Newtonsoft.Json.JsonConvert.SerializeObject(ViewBag.loginResponse)),
        initialState: '@(ViewBag.state == null ? "" : ViewBag.state)',
        mobile: '@(ViewBag.mobile == null ? "" : ViewBag.mobile)',
        sessionExpired: '@ViewBag.sessionExpired',
        dataExhausted: '@ViewBag.dataExhausted',
        facebookPage: '@(ViewBag.FbPage == null ? "" : ViewBag.FbPage)',
        facebookCheckin: '@((ViewBag.FbCheckin == null || ViewBag.FbCheckin == "0") ? "" : ViewBag.FbCheckin)',
        facebookAppId: @I2e1ConfigurationManager.GetInstance().GetSetting("facebookAppId"),
        loginLogo: '@(string.IsNullOrEmpty(ViewBag.loginLogo) ? null : "/Proxy/GetContent.ashx?url=" + Util.UrlEncode(ViewBag.loginLogo))',
        swapLink: '@Html.Raw(ViewBag.swapLink)',
        resources:@Html.Raw(Newtonsoft.Json.JsonConvert.SerializeObject(resourceDict))
    }

        console.log(_viewBag.resources)
    if (_viewBag.sessionExpired) {
        window.parent.postMessage({
            action: '_sessionExpired'
        }, '*');
    } else if (_viewBag.dataExhausted) {
        window.parent.postMessage({
            action: '_dataExhausted'
        }, '*');
    }


    var _landingSearch = function () {
        if (!document.getElementById('search').value) return;
        if (window.postMessage)
            window.parent.postMessage({
                action: '_fullRedirect',
                link: 'https://www.google.com/search?q=' + encodeURIComponent(document.getElementById('search').value)
            }, '*');
        else window.open(event.data.link);
    }
    $(document).ready(function () {
        document.querySelector('meta[name="accept-language"]').setAttribute("content", _viewBag.resources.Culture);
        $(".language-selectors a").show();
        $('.language-selectors').find('#' + _viewBag.resources.Culture.split('-')[0]).hide();
    });

    _buyFrom = function (show) {
        if (show)
            i2e1Api.logEvent("nearestshop_click", _loginUser.nasid, { mobile: _loginUser.mobile });
        $('.top-container > div.login.container').toggleClass('display-none')
    }
    Object.defineProperty(this, '_backFN', {
        get: function () { return myVar; },
        set: function (v) {
            myVar = v;
            if (myVar) {
                $('.outer-card1 .back-button').removeClass('silent')
            } else {
                $('.outer-card1 .back-button').addClass('silent')
            }
        }
    });
    _back = function () {
        if (_backFN) {
            _backFN();
        }
    }

    _toggleHelpVideo = function () {
        $('.help-video').toggleClass('display-none')
        i2e1Api.logEvent("video_click", _loginUser.nasid, { mobile: _loginUser.mobile });
    }
    </script>
    @RenderSection("ScriptSection", false)
</head>
<body>
    @if (Model.backEndNasid == null)
    {
        <p style="background: #d07b7b;color: #fff;" i18n="please_configure_nas_id">Please Configure Wifi Identifier</p>
    }
    <div id="message-pop-up" style="display: none;
        position: fixed;
        top: 0;
        bottom: 0;
        left: 0;
        right: 0;
        background-color: #00000080;z-index: 1;">
        <div style="position: absolute; top: 33%; width: 100%;">
            <div style=" background-color: #fff; margin: 0 auto; width: 18rem; padding: 1rem;">
                <img style="width: 1rem; float: right;" onclick="$('#message-pop-up').hide()" src="/images/close.png" />
                <h2 style="color: #48bb78;display: none" id="header-success">Success</h2>
                <h2 style="color: #F25F58;display: none" id="header-failed">Failed</h2>
                <h3 id="message">Details saved successfully</h3>
                <button style="margin-top:2rem;" class="primary login_button" onclick="$('#message-pop-up').hide()">OK</button>
            </div>
        </div>
    </div>

    <div class="top-container display-none">
        <div class="login container">
            <div class="language-selectors">
                <a href="#" onclick="_changeLanguage('en')" id="en">English</a>
                <a href="#" onclick="_changeLanguage('hi')" id="hi">हिंदी</a>
                <a href="#" onclick="_changeLanguage('te')" id="te">తెలుగు</a>
            </div>

            <div class="outer-card1" cage="outer-header">
                <img class="back-button silent" src="~/images/back.png" onclick="_back()" />
                <img id="outer-card-img" onerror="this.src='/images/wiom/wiom_logo.png'" class="wiom-logo" src="/images/wiom/wiom_logo.png" />
            </div>

            @RenderBody()

            <form class="form social-state state-transition display-none" onkeyup="_submitThis('_facebook_share');" onsubmit="return false;">
                <div class="social">
                    <div class="inline-error"></div>
                    <img id="client_logo" />
                    <button class="primary fb-share" type="button" onclick="_facebook_share();"><img src="~/images/facebook.png" /> <div>Post on Facebook</div></button>
                </div>
                <input id="skipSocial" class="small_button primary" style="display:none;" type="button" value="Skip" />
            </form>
            <form class="form redirecting-state state-transition display-none">

                <div class="no-offers">
                    <img class="success-img" src="~/images/success_screen.svg" />
                    <h3 style="color: rgb(244, 105, 58);margin: .5rem;font-size: 1.5rem;" i18n="redirectionState_enjoy">Enjoy!</h3>
                    <h3 id="redirectionState_use_free_wifi_now" style="margin:.5rem 0 0.1rem 0;font-size: 1.3rem;" i18n="redirectionState_use_free_wifi_now"><span id="redirect_state_data_left"></span> internet for the next <span id="redirect_state_time_left"></span></h3>
                    <div style="font-size: .9rem;" i18n="redirectionState_you_are_almost_there">Connecting you in 5 seconds…</div>
                    <div style="border:1px solid #f4693a;padding:2px;margin: 1rem 3rem;border-radius:0.5rem;">
                        <div style="height:0.4rem;background:#f4693a;border-radius:0.5rem;width:50%"></div>
                    </div>
                </div>

                <div class="offers display-none">
                    <div class="campaigns"></div>
                    <input id="skipVoucher2" class="skip-voucher small_button primary" type="button" value="Skip" />
                </div>
            </form>

            <form class="form error-state state-transition display-none">
                <div id="errorMsg" class="errorSpan"></div>
                <div id="pay_via_cash_finally" class="btn-grp" style="display: none;">
                    <button type="button" class="primary login_button btn-grp" onclick="_payVia('cash', true)">
                        <img src="/images/wiom/wallet.svg" style="height: 1.5rem;padding: .25rem;">
                        <span i18ninputvalue="planSelectionState_cash">Pay Cash</span>
                    </button>
                    <br />
                    <div style="text-align:center; margin: 0px auto">- or -</div>
                    <br />
                </div>
                <div class="limit-exhausted" style="display: none;"></div>
                <div class="time-exhausted" style="display: none;"></div>
                <div class="user-blocked" style="display: none;"></div>
                <div class="unknown-error" style="display: none;"></div>
                <div class="go-back-to-login">
                    <a onclick="_swapState(_i2e1Constants.firstState);" i18n="errorState_try_logging_again">Try logging again</a>
                </div>
            </form>
            <form class="form internet-over-state state-transition display-none">
                <img src="/images/internet_over.png" />
                <h3 style="margin: 0.5rem 0;">Free Internet Over</h3>
                <h3 style="margin: 3rem 0 0.5rem 0;">Get UNLIMITED Internet in just ₹5</h3>
                <p style="color: #666666; font-size: 14px;">Valid across <b>10,000</b> locations in Delhi</p>
                <input type="button" class="primary login_button" value="Buy Online" onclick="_swapState(_i2e1Constants.planSelectionState);" />
            </form>
            <form class="form custom-state state-transition display-none" cage="custom-state"></form>
            <form class="landing-state state-transition display-none" onkeyup="_submitThis('_landingSearch')" onsubmit="return false;">

                <div class="landing-page-message" cage="landing-page-message">
                    <p></p>
                </div>
                <div action="https://www.google.com/search">
                    <img src="/images/search.png" onclick="_landingSearch()" />
                    <input id="search" type="text" name="q" placeholder="Search" />
                    <input type="button" name="enter" style="display:none;" onclick="_landingSearch(false)" />
                </div>
                <div cage="landing-page-tiles-container" class="landing-page-tiles-container"></div>
            </form>

            <div class="form payment-state state-transition display-none"></div>
            <div class="footer">
                <div class="top-border"></div>
                <span style="display: inline-block;"><img style="width: 1rem;margin-right: 0.5em;" src="/images/call.svg"/><b><a href="tel:0918880322222">8880322222</a>
                </b></span>
                <span style="float:right">@Model.backEndNasid</span>
                <br/>
                <span style="display: inline-block;"><img style="width: 1rem;margin-right: 0.5em;" src="/images/sms-icon.png"/><b><a target="_blank" href="mailto:<EMAIL>"><EMAIL></a> 
                </b></span>
                <div class="stepper">
                    <div class="step"></div>
                    <div class="connector">
                        <div></div>
                    </div>
                    <div class="step"></div>
                    <div class="connector">
                        <div></div>
                    </div>
                    <div class="step"></div>
                </div>

            </div>

        </div>

        <div id="coupon_sellers" class="login container display-none">
            <form class="form" onsubmit="false" style="min-height:25rem;">
                <div style="padding: 1rem;">
                    <h3 i18n="dataVoucherState_list_of_shops" style="text-align:center">List of shops</h3>
                    <img style="position: absolute; width: 1rem; top: 1rem; right: 1rem;" src="~/images/close.png" onclick="_buyFrom()" />
                </div>

                <div class="list" id="coupon_sellers_div">
                </div>
            </form>
        </div>
    </div>

    <div class="help-video display-none" onclick="_toggleHelpVideo()">
        <div class="video">
            <img src="~/images/close.png" />
            <video src="~/images/data_voucher_help.mp4" controls></video>
        </div>
    </div>

    <div id="loader">
        <div class="deactivate"></div>
        <div class="img-section">
            <div>
                <img src="~/images/wifi_loader_84X84.gif" />
            </div>
        </div>
    </div>

    <div id="internal-templates" class="display-none">
        <div class="answerType-0">
            <div class="material-input question text-question current">
                <div class="group">
                    <input type="text" required="required" pattern=".*\S.*" placeholder="" />
                    <label></label>
                </div>
            </div>
        </div>

        <div class="answerType-1">
            <div class="question select current">
                <div class="question-text">You choose to travel most frequently with<span class="required">*</span></div>
                <!--<ul class="list-unstyled" answer-type="1">
                    <li class="init"><b>Select your answer</b></li>
                </ul>-->
                <div class="options-list"></div>
            </div>
        </div>

        <div class="answerType-2">
            <div class="question select current">
                <div class="question-text">You choose to travel most frequently with<span class="required">*</span></div>
                <!--<ul class="list-unstyled" answer-type="2">
                    <li class="init"><b>Select your answer</b></li>
                </ul>-->
                <div class="options-list"></div>
            </div>
        </div>

        <div id="campaign-template" class="campaign-template image">
            <div class="advertisement-campaign display-none">
                <div class="top-container">
                    <div class="login container transition" style="left:-20px">
                        <div class="circle">
                            <div class="content skip">
                                <span class="data"><img class="grayscale" src="/images/cross.png"></span>
                            </div>
                        </div>
                        <form class="campaign">
                            <img src="" />
                        </form>
                    </div>
                </div>
            </div>
        </div>

        <div id="campaign-template" class="campaign-template video">
            <div class="advertisement-campaign display-none">
                <div class="top-container">
                    <div class="login container transition" style="left:-20px">
                        <div class="progressBar">
                            <div style="width: 17.625rem;"></div>
                        </div>
                        <div class="circle skip">
                            <div class="content">
                                <span class="data"><img class="grayscale" src="/images/cross.png"></span>
                            </div>
                        </div>
                        <span class="ctp">Click to play</span>
                        <form class="campaign">
                            <video width="400" autoplay>
                                <source src="" type="video/mp4">
                                <source src="" type="video/ogg">
                                Your browser does not support HTML5 video.
                            </video>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div id="forignTemplates" class="display-none">
        @{
            int baseTemplate = Model.GetBaseTemplateId();
            if (baseTemplate == 0)
            {
                Model.templateid.Add(int.Parse(I2e1ConfigurationManager.GetInstance().GetSetting("i2e1.DefaultTemplateId")));
            }

            for (int i = 0; i < Model.templateid.Count; ++i)
            {
                if (Model.templateid[i] != 0)
                {
                    <div id=forignTemplate_@i>
                        @Html.Raw(CacheHelper.GetInstance().GetTemplateContent(Model.templateid[i], Model.backEndNasid).templateContent)
                    </div>
                }
            }
        }
    </div>
</body>
<script type="text/javascript">
    $('.language-selectors').find('#' + _viewBag.resources.Culture).hide();
    $('.language-selectors').find('#' + _viewBag.resources.Culture.split('-')[0]).hide();
    $("body").on("domChanged", function () {
        $('input').on('keyup', _fieldHandler)
    });
    $('input').on('keyup', _fieldHandler)

    _bodyOnload()
    if (_loginUser.nasid == 0) {
        _swapState(_i2e1Constants.errorState, { errorType: 'time-exhausted', msg: "Invalid Device Id", errorImg: "/images/error-icon.png" });
        $('.go-back-to-login').hide();
    }
</script>
</html>
