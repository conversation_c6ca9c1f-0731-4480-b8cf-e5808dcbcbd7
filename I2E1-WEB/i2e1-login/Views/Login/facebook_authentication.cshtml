@using i2e1_core.Models
@using I2E1_WEB.Models
@using i2e1_core.Utilities;
@using i2e1_basics.Utilities;
@model wiom_login_share.Models.User;
@{
    Layout = "~/Views/Login/layout_base.cshtml";
}
@section ScriptSection{
    @if (ViewBag.globalOTP != null)
    {
        <link rel="stylesheet" href="~/jsLibs/countrycode/intlTelInput.css">
        <script src="~/jsLibs/countrycode/intlTelInput.js"></script>
    }
    <script type="text/javascript">
        var _getMobile = function () {
            return 'FB-User';
        }
        var _loginUser = @Html.Raw(Newtonsoft.Json.JsonConvert.SerializeObject(Model));
        var _progressbar;
        var _viewBag = {
            sessionExpired: '@ViewBag.sessionExpired',
            dataExhausted: '@ViewBag.dataExhausted',
            facebookPage: '@(ViewBag.FbPage == null ? "" : ViewBag.FbPage)',
            facebookCheckin: '@((ViewBag.FbCheckin == null || ViewBag.FbCheckin == "0") ? "" : ViewBag.FbCheckin)',
            facebookAppId: '@I2e1ConfigurationManager.GetInstance().GetSetting("facebookAppId")',
            loginLogo: '@(ViewBag.loginLogo == null ? null : ViewBag.loginLogo)'
        }

        function _statusChangeCallback(response){
            var success = function (response) {
                _preLogin(response.data.landingPage);
            };
            var failure = function (response) {
                progress(50, 500);
                _showCaptcha(response.data);
                _handleOTPError(response.msg, 'facebook-btn');
            };
            console.log(response);
            if(_processAnswers(3)) return;
            i2e1Api.submitToken(response.authResponse.accessToken, "facebook", {
                questions: _i2e1Ques,
                onSuccess: success,
                onFailure: failure
            });
        }
    </script>
}
<script>
    window.fbAsyncInit = function() {
        FB.init({
            appId      : _viewBag.facebookAppId,
            xfbml      : true,
            version    : 'v2.6'
        });
        FB.AppEvents.logPageView();
    };
    (function(d, s, id){
        var js, fjs = d.getElementsByTagName(s)[0];
        if (d.getElementById(id)) {return;}
        js = d.createElement(s); js.id = id;
        js.src = "//connect.facebook.net/en_US/sdk.js";
        fjs.parentNode.insertBefore(js, fjs);
    }(document, 'script', 'facebook-jssdk'));
</script>
<form class="form first-state" onsubmit="return false;">
<div class="question-area type-3 display-none"></div>
<span cage="facebook-helper"></span>
<div for="facebook-btn" class="inline-error"></div>
<h4>Login using facebook to access<br/> internet</h4>

<a id="facebook-btn" onclick="FB.login(_statusChangeCallback, {scope:'public_profile,email'})">
<span>
<img src="/images/facebook.png" border="0" style="width:20px">
</span>
<span>Log In with Facebook</span>
</a>
<div cage="generate-otp">
<span class="tnc">By "logging in with facebook" you agree to our <a target="_blank" href="~/Templates/i2e1/tnc.html">Terms & Conditions</a></span>
</div>
</form>