@using i2e1_core.Models
@using I2E1_Message.Utils
@using i2e1_core.Utilities;
@using I2E1_WEB
@model wiom_login_share.Models.User;
<!DOCTYPE html>
<html>
<head>
    <title>i2e1 Login Successful</title>
    <!-- The below comment is required for troubleshooting .Kindly dont remove it as it wont do any harm -->
    <!-- QWERTPOIUY123456789 -->
    <meta name="theme-color" content="#ffffff" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0, user-scalable=no" />
    <link rel="stylesheet" type="text/css" href="@Util.GetUrlWithVersion("/Templates/i2e1/site.css")" />
    <link rel="shortcut icon" href="images/favicon.ico" type="image/x-icon">
    <script src="@Util.GetUrlWithVersion("/jsLibs/jquery.js")"></script>
    <script src="@Util.GetUrlWithVersion("/Templates/i2e1/template-utils.js")"></script>
    @{
        Dictionary<string, string> resourceDict = new Dictionary<string, string>();

        resourceDict["Culture"] = System.Threading.Thread.CurrentThread.CurrentUICulture.Name;
    }
    <script type="text/javascript">
    var _loginUser = @Html.Raw(Newtonsoft.Json.JsonConvert.SerializeObject(Model));
    </script>
    
</head>
<body class="download-wifi-app">
    <div class="top-container">
        <div class="login container">
            <div class="outer-card1" cage="outer-header" style="margin-bottom: 0">
                <img class="back-button silent" src="~/images/back.png" onclick="_back()" />
                <img id="outer-card-img" onerror="this.src='/images/wiom/wiom_logo.png'" class="wiom-logo" src="/images/wiom/wiom_logo.png" />
            </div>

            <form class="form download-wifi-app-state">
                <div class="barking">
                    <img onerror="this.src='~/images/linq.png'" src="~/images/linq.png" style="width: 7.5rem;"/>
                    <div class="loud" i18n="downloadAppState_bark_lessloud"
                         style="font-size:1.2rem;">Download Linq WiFi App</div>
                    <div class="loud" i18n="downloadAppState_bark_loud">Get UNLIMITED Interent</div>
                    <div class="loud" i18n="downloadAppState_bark_lessloud"
                         style="font-size:1rem;margin: 3rem 0 2rem 0;color: grey;">Check SMS sent to you for next steps</div>
                </div>

                <div cage="connect-button">
                    <input type="button" class="primary login_button" value="Download App" i18nInputValue="downloadAppState_download" onclick="window.location.href='https://wifilinq.page.link/invite'" />
                </div>
                <p style="color: #f4693a;font-weight: 900;"><a class="resend-sms" onclick="window.location.reload()">Resend App Link Via SMS</a></p>
            </form>

            <div class="footer">
                <div class="top-border"></div>
                <span i18n="footer_facing_issues_call_us_at">Get Help<b><a href="tel:0918880322222">8880322222</a></b></span>
                <div class="stepper">
                    <div class="step"></div>
                    <div class="connector">
                        <div></div>
                    </div>
                    <div class="step"></div>
                    <div class="connector">
                        <div></div>
                    </div>
                    <div class="step"></div>
                </div>

            </div>

        </div>
    
    </div>

</body>
</html>
