<script type="text/javascript">
    var jsErrorLogger = function (message, source, data) {
        $.ajax({
            type: "POST",
            url: "/Login/LogJsError",
            data: {
                message: message,
                source: source,
                lineno: 0,
                colno: 0,
                error: btoa(JSON.stringify(data)),
                url: window.location.href || document.referrer
            }
        })
    };
    
    var _i2e1Domain = 'https://' + window.location.hostname + (window.location.port ? (':' + window.location.port) : '') + '/';

    var _i2e1Constants = {
        pageLoad: 'Page Load',
        firstState: 'First State',
        secondState: 'Second State',
        questionState: 'Question State',
        offerState: 'Offer State',
        socialState: 'Facebook State',
        fbLoginState: 'Facebook Login State',
        facebookRedirectedState: 'Facebook Redirected State',
        mobileConnectRedirectedState: 'Mobile Connect Error State',
        redirectionState: 'Redirection State',
        helpState: 'Help State',
        stateChange: 'State Change',
        errorState: 'Error State',
        customState: 'Custom State',
        paymentState: 'Payment State',
        landingState: 'Landing State',
        swappPromoState: 'Swapp State',
        userProfile: 'User Profile',
        stateOpened: 'State Opened',
        sharePressed: 'Share Pressed',
        doingLogin: 'Doing Login',
        generateOTPPressed: 'Generate OTP Pressed',
        submitOTPPressed: 'Submit OTP Pressed',
        resendOTPPressed: 'Resend OTP Pressed',
        error: 'Error Occured',
        invalidAnswer: 'Invalid Answer',
        questionAsked: 'Question Asked',
        offerShown: 'Offer Shown',
        offerClicked: 'Offer Clicked',
        submitPressed: 'Submit Pressed',
        generalEvent: 'i2e1 Website',
        nationalIdSignInClicked: 'National Id Sign In Clicked',
        lastNameRoomSigninClicked: 'Last Name & Room Sign In Clicked',
        djuboSignInPressed: 'Djubo Sign In Pressed',
        menuClicked: 'Menu Clicked',
        tncClicked: 'TnC Clicked',
        tileClicked: 'Tile Clicked',
        dataVoucherState: 'Data Voucher State',
        internetOverState: 'Internet Over State',
        planSelectionState: 'Plan Selection State',
        freePlanState: 'Free Plan State',
        storeDetailState: 'Store Detail State',
        submitDIYREGPressed: 'Submit Detail pressed',
        macConfirmationState: 'Mac Confirm State',
        customerProfileState: 'Customer Profile State'
    }

    var _logMPEvent = function (state, properties) {
    }

    var wsDomain = 'www.i2e1.in';
    var _rowdata = null;
    window._wsUrl = 'wss://' + wsDomain + ':8081/wsee';
    window.socket_id = 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (c) {
        const r = Math.random() * 16 | 0,
            v = c === 'x' ? r : (r & 0x3 | 0x8);
        return v.toString(16);
    });

    var _wsCon = new WebSocket(window._wsUrl);
    window._wsStatus = false;
    _wsCon.onopen = function () {
        window._wsStatus = true;
        if (_rowdata) window.socket_send_data(_rowdata);
            _rowdata = null;
        console.log("new connection setup");
    };
    _wsCon.onerror = function (err) {
        console.log("Got error", err);
        _wsCon.close();
    };

    _wsCon.onclose = function (evt) {
        console.log("connection closed by server");
        window._wsStatus = false;
    };

    window.socket_send_data = function(rowdata){
        try {
            if (!window._wsStatus) {
                _rowData = rowdata;
                 window._wsStatus = false;
                console.log('socket reconnecting...');
                _wsCon = new WebSocket(window._wsUrl);
                _wsCon.onopen = function () {
                    window._wsStatus = true;
                    if (_rowdata) window.socket_send_data(_rowdata);
                    _rowdata = null;
                    console.log("new connection setup");
                };
                _wsCon.onerror = function (err) {
                    console.log("Got error", err);
                    _wsCon.close();
                };

                _wsCon.onclose = function (evt) {
                    console.log("connection closed by server");
                    window._wsStatus = false;
                };

                setTimeout(function () {
                    try {
                        _wsCon.send(rowdata);
                    }
                    catch (ex) {
                        console.log("Exception sending event to event collector : " + JSON.stringify(ex));
                    }
                }, 500)
            }
            else
                _wsCon.send(rowdata);
        }
        catch (ex) {
            console.log("Exception sending event to event collector : " + JSON.stringify(ex));
        }
               
    };
</script>