@using I2E1_Message.Utils
@using i2e1_core.Models
@using wiom_routerplan_share.Models.RouterPlan;
@model wiom_login_share.Models.User;
@{
    var passportUser = (HomeRouterPlan)ViewBag.passportUser;
    int totalDays = 0;
    if(passportUser != null)
        totalDays = (int)(passportUser.planEndTime - passportUser.planStartTime).TotalDays;
}
<html>
<head>
    <title>i2e1</title>
    <meta name="viewport" content="width=device-width, height=device-height, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0, user-scalable=no" />
    <link rel="shortcut icon" href="/images/favicon.ico" type="image/x-icon">
    <script src="@Util.GetUrlWithVersion("/jsLibs/jquery.js")"></script>
    <script src="@Util.GetUrlWithVersion("/Templates/i2e1/i2e1-sdk.js")"></script>
    <script>
        var _loginUser = @Html.Raw(Newtonsoft.Json.JsonConvert.SerializeObject(Model));
    </script>
</head>
<body>
    @if (passportUser != null)
    {
        <div class="top-container">
            <div class="login container" style="height:auto">
                <div class="outer-card1" cage="outer-header">
                    <img class="back-button silent" src="~/images/back.png" onclick="_back()" />
                    <img id="outer-card-img" onerror="this.src='/images/wiom/wiom_logo.png'" class="wiom-logo" src="/images/wiom/wiom_logo.png" />
                </div>
                <h2>Coupon details</h2>
                <p style="font-size: 1.125rem;margin-bottom:2rem;">
                    <span style="float:left;width:33%">
                        Price <br /><b>₹@passportUser.charges/-</b>
                    </span>
                    <span style="float:left;width:33%">
                        Data <br /><b>@(passportUser.dataLimit == 0 ? "Unlimited" : passportUser.dataLimit/1024/1024/1024 + " GB")</b>
                    </span>
                    <span style="float:left;width:33%">
                        Validity <br /><b>@(totalDays +  (totalDays > 1 ? " Days" : " Day"))</b>
                    </span>
                </p>
                <div style="margin-top:6rem">
                    <span style=" border: 1px #ddd dashed; padding: 0.5rem 1rem; background: #efefef;">Coupon code: <b>@passportUser.otp</b></span>
                    <input type="button" class="primary login_button" style="margin: 4rem 0 1rem 0; width: 100%;" value="Apply to @Model.mobile" onclick="_submitPlan('@passportUser.otp')" />
                    <a style="color: #F25F58; font-size: 0.875rem;" onclick="window.close()">I will use this coupon later</a>
                </div>
                <div class="footer">
                    <div class="top-border"></div>
                    <span i18n="footer_facing_issues_call_us_at">Get Help<b> - <a href="tel:0918880322222">8880322222</a></b></span>
                    <div class="stepper">
                        <div class="step"></div>
                        <div class="connector">
                            <div></div>
                        </div>
                        <div class="step"></div>
                        <div class="connector">
                            <div></div>
                        </div>
                        <div class="step"></div>
                    </div>
                </div>
            </div>
        </div>
    }
    <div id="message-pop-up" style="display: none;
        position: fixed;
        top: 0;
        bottom: 0;
        left: 0;
        right: 0;
        background-color: #00000080;z-index: 1;">
        <div style="position: absolute; top: 33%; width: 100%;">
            <div style=" background-color: #fff; margin: 0 auto; width: 18rem; padding: 1rem;">
                <img style="width: 1rem; float: right;" onclick="$('#message-pop-up').hide()" src="/images/close.png" />
                <h2 style="color: #48bb78;display: none" id="header-success">Success</h2>
                <h2 style="color: #F25F58;display: none" id="header-failed">Failed</h2>
                <h3 id="message">Details saved successfully</h3>
                <button style="margin-top:2rem;" class="primary login_button" onclick="$('#message-pop-up').hide()">OK</button>
            </div>
        </div>
    </div>
    <script>
        var mobile = "@Model.mobile";
        var nasid = 0;
        var _submitPlan = function (accessCode) {
            if (!accessCode)
                return

            i2e1Api.submitPlan(mobile, 0, {
                accessCode: accessCode,
                name: '',
                onSuccess: _submitPlan.success,
                onFailure: _submitPlan.failure
            });
        };

        _submitPlan.success = function (response) {
            $('#header-success,#header-failed').hide();
            $('#header-success').show();
            $('#message').text('Details saved successfully');
            $('#message-pop-up').show();
            i2e1Api.logEvent("coupon_valid", nasid, { mobile: mobile });
        };

        _submitPlan.failure = function (response) {
            $('#header-success,#header-failed').hide();
            $('#header-failed').show();
            $('#message-pop-up').show();
            if (response.data.invalidCode) {
                $('#message').text('Invalid code');
                i2e1Api.logEvent("coupon_invalid", nasid, { mobile: mobile });
            }
            else {
                $('#message').text('Coupon already used');
                i2e1Api.logEvent("coupon_used", nasid, { mobile: mobile });
            }
        };
        @if(passportUser == null)
        {
            @:$('#header-success,#header-failed').hide();
            @:$('#header-failed').show();
            @:$('#message').text('Coupon not found');
            @:$('#message-pop-up').show();
        }
    </script>
</body>
<style type="text/css">
    .login.container {
        height: 25rem;
    }

    .ss-exp {
        font-size: 2rem;
        color: #484848;
        height: 24rem;
        display: flex;
        flex-direction: column;
        justify-content: center;
        text-align: center;
    }

        .ss-exp a {
            font-size: 1.5rem;
            margin-top: 1rem;
        }
</style>
<link rel="stylesheet" type="text/css" href="@Util.GetUrlWithVersion("/Templates/i2e1/site.css")" />
</html>