@using I2E1_Message.Utils
@using I2E1_WEB;
<!DOCTYPE html>
<html>
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=0">
    <title>login</title>
    <link rel="stylesheet" type="text/css" href="/styles/bootstrap/css/bootstrap.min.css" />
    <script type="text/javascript" src="/jsLibs/jquery.js"></script>
    <script type="text/javascript" src="/Templates/i2e1/i2e1-sdk.js"></script>
    @{
        Html.RenderPartial("~/Views/Login/Shared/WebSocketEvents.cshtml");
    }
    <script type="text/javascript">
        var _loginUser = @Html.Raw(Newtonsoft.Json.JsonConvert.SerializeObject(Model));
        var _viewBag = {
            loginResponse: @Html.Raw(Newtonsoft.Json.JsonConvert.SerializeObject(ViewBag.loginResponse)),
            sessionExpired: '@ViewBag.sessionExpired',
            dataExhausted: '@ViewBag.dataExhausted',
            isLoggedIn: '@ViewBag.isLoggedIn'
        }
        var onAutoLogin = function () {
            $('body>div').toggleClass('display-none');
        }
    </script>
    <style type="text/css">
        .loader {
            display: table;
            position: absolute;
            height: 100%;
            width: 100%;
        }

            .loader > div {
                display: table-cell;
                vertical-align: middle;
            }

                .loader > div > div {
                    margin-left: auto;
                    margin-right: auto;
                    width: 75px;
                    /*whatever width you want*/
                }

            .loader img {
                width: 100%;
            }

            .loader p {
                text-align: center;
                font-size: 20px;
            }

        .display-none {
            display: none;
        }
    </style>
    <script type="text/javascript" src='~/Proxy/GetContent.ashx?url=@Util.UrlEncode(ViewBag.fullTemplatePath + ".js")'></script>
    <link rel="stylesheet" type="text/css" href='~/Proxy/GetContent.ashx?url=@Util.UrlEncode(ViewBag.fullTemplatePath + ".css")' />
</head>
<body>
    <div class="client-section">
        @Html.Raw(CacheHelper.GetInstance().GetTemplateContent(Model.templateid[0]).templateContent)
    </div>
    <div class="loader display-none">
        <div>
            <div>
                <img src="~/images/wifi_loader_84X84.gif" />
            </div>
            <p>please wait...</p>
        </div>
    </div>
    <script type="text/javascript">
        if (_viewBag.loginResponse) {
            onAutoLogin();
            setTimeout(function () {
                _makei2e1Login(_viewBag.loginResponse)
            }, 500);
        }
    </script>

</body>
</html>
