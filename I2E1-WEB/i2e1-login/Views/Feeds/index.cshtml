@model List<string>
    <!doctype html>
    <html>

    <head>
        <meta name="viewport" content="width=device-width, initial-scale=1">
        <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.1.0/jquery.min.js"></script>
        <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/4.7.0/css/font-awesome.css">
        <style>
        @@import url('/styles/Lato.css');

        .grid-container {
            display: grid;
            grid-template-columns: auto auto;
        }

        .grid-item-left {
            text-align: left;
        }

        .grid-item-center {
            text-align: center;
        }

        .grid-item-right {
            text-align: right;
        }

        .card {
            position: relative;
            display: -ms-flexbox;
            display: flex;
            -ms-flex-direction: column;
            flex-direction: column;
            min-width: 0;
            word-wrap: break-word;
            border: none;
        }

        .card-body {
            padding: 0;
            margin-right: 16px;
            margin-left: 16px;
        }

        .card-title {
            padding-bottom: 32px;
            padding-top: 32px;
            color: #999999;
            margin: 0;

        }

        .card-text {
             padding-bottom: 32px;
             color: #202124;
             font-family: Lato;
             font-size: 14px;
             margin: 0;
        }

        .card:nth-child(even) {
            background: #f9f9f9;
        }
</style>
</head>
<body style="font-family: Lato; margin: 0;">
        @if (Model != null && Model.Count > 0)
        {
            <div class="cards" style="background-color:white">


                @foreach (var pair in Model)
                {
                    @Html.Raw(pair);
                }

            </div>
        }
</body>

</html>
