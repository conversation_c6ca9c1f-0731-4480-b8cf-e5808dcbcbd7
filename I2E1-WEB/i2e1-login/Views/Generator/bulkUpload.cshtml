@using I2E1_WEB.Models
@using I2E1_WEB.Utilities
@using wifidog_core.Models
@model List<FDMConfig>
<!DOCTYPE html>
<html>
<head>
    <meta name="viewport" content="width=device-width" />
    <title>i2e1 Password Generator</title>
    <link rel="stylesheet" type="text/css" href="/styles/bootstrap/css/bootstrap.min.css">
    <script type="text/javascript" src="~/jsLibs/jquery.js"></script>
    <script type="text/javascript" src="~/jsLibs/moment-2-13-0.js"></script>
    <script type="text/javascript" src="~/jsLibs/datepicker/datepicker.js"></script>
    <link rel="stylesheet" type="text/css" href="~/jsLibs/datepicker/datepicker.css" />
    <link rel="stylesheet" href="~/styles/pure.css">
    <style>
        table {
            table-layout: fixed;
        }

            table td {
                width: 30%;
                padding: 0 3px;
                word-wrap: break-word;
            }

            table a {
                padding: 5px 10px;
                display: inline-block;
                color: #fff;
                text-decoration: none;
                background-color: #B70000;
            }

        .datepicker.basic .picker table td, .datepicker.basic .picker table th {
            padding: 2px 0;
        }

        table tr.notAllowed td {
            background-color: #FFA5A5;
        }

        table tr.notAllowed a {
            background-color: green;
        }
        /* CSSTerm.com Simple CSS menu */

        br {
            clear: left;
        }

        .menu_simple {
            width: 100%;
            background-color: #005555;
            position: relative;
        }

            .menu_simple ul {
                margin: 0;
                padding: 0;
                float: left;
            }

                .menu_simple ul li {
                    display: inline;
                }

                    .menu_simple ul li a {
                        float: left;
                        text-decoration: none;
                        color: white;
                        padding: 10.5px 11px;
                        background-color: #005555;
                    }

                        .menu_simple ul li a:visited {
                            color: white;
                        }

                        .menu_simple ul li a:hover, .menu_simple ul li .current {
                            color: white;
                            background-color: #5FD367;
                        }
    </style>
    <script>
        function registerUser() {
            var days = document.getElementById('expiryDays').value;
            if (!days || days == 0) {
                alert("Please select validity period");
                return false;
            }
            return true;
        }

        $(function () {
            $(".datepicker").datepicker({
                dateFormat: "yy-mm-dd"
            });
        });
    </script>
</head>
<body>
    @if (ViewBag.passwordGeneratorUser != null)
    {
        var user = ViewBag.passwordGeneratorUser;
        <div style="margin: 0 auto;width: 650px;">
            <h1 style="text-align:center;">i2e1 Authorization Portal</h1>
        </div>
        <form class="pure-form pure-form-aligned" style="padding:10px;border:solid 5px #000000;width:650px;margin:0px auto;" method="post" enctype="multipart/form-data" action="/PasswordGenerator/Upload">
            <div class="menu_simple">
                <ul>
                    <li><a href="/PasswordGenerator">Register</a></li>
                    <li><a href="/PasswordGenerator/BulkUpload">BulkUpload</a></li>
                    <li><a href="/PasswordGenerator/ViewOTP">View OTP</a></li>
                    <li style="position:absolute;right:0;"><a href="/PasswordGenerator/Logout">Logout</a></li>
                </ul>
                <br />
            </div>
            <fieldset>

                <div class="pure-control-group">
                    <label for="">Upload Excel File(in .xls format)</label>
                    <input type="file" id="file" name="file" />
                    <a href="~/resources/sample_bulk_upload.xls">Download Template</a>
                </div>
                <div class="pure-control-group">
                    <label for="">Change Plan</label>
                    <select name="dataPlan">
                        <option value="0">Select</option>
                        @if (user.nasid != 2349)
                        {
                            <option value="50">+50 MB</option>
                            <option value="100">+100 MB</option>
                            <option value="200">+200 MB</option>
                            <option value="500">+500 MB</option>
                            <option value="1024">+1 GB</option>
                            <option value="2048">+2 GB</option>
                            <option value="-50">-50 MB</option>
                            <option value="-100">-100 MB</option>
                        }
                        <option value="102400">Unlimited</option>
                    </select>
                </div>

                <div class="pure-control-group">
                    <label for="expiryDays">Validity Period</label>
                    <input class="datepicker" name="expiryTime" value="@DateTime.UtcNow.AddDays(1).ToString("dd.MM.yyyy")" required />
                </div>
                <div style="margin:0px auto;text-align:center;" class="pure-control-group">
                    <input value="Generate" type="submit" class="pure-button pure-button-primary" />
                </div>

            </fieldset>
        </form>
    }
</body>
</html>