@using I2E1_Message.Models
@using I2E1_WEB.Models
@using i2e1_core.Models
@using I2E1_WEB.Utilities
@using wiom_routerplan_share.Models.RouterPlan
@model List<HomeRouterPlan>
<!DOCTYPE html>
<html>
<head>
    <meta name="viewport" content="width=device-width" />
    <title>i2e1 Password Generator</title>
    <link rel="stylesheet" type="text/css" href="/styles/bootstrap/css/bootstrap.min.css">
    <link rel="stylesheet" href="~/styles/pure.css">
    <style>
        table {
            table-layout: fixed;
        }

            table td {
                width: 30%;
                padding: 0 3px;
                word-wrap: break-word;
            }

            table a {
                padding: 5px 10px;
                display: inline-block;
                color: #fff;
                text-decoration: none;
                background-color: #B70000;
            }

            table tr.notAllowed td {
                background-color: #FFA5A5;
            }

            table tr.notAllowed a {
                background-color: green;
            }
        /* CSSTerm.com Simple CSS menu */

        br {
            clear: left;
        }

        .menu_simple {
            width: 100%;
            background-color: #005555;
            position: relative;
        }

            .menu_simple ul {
                margin: 0;
                padding: 0;
                float: left;
            }

                .menu_simple ul li {
                    display: inline;
                }

                    .menu_simple ul li a {
                        float: left;
                        text-decoration: none;
                        color: white;
                        padding: 10.5px 11px;
                        background-color: #005555;
                    }

                        .menu_simple ul li a:visited {
                            color: white;
                        }

                        .menu_simple ul li a:hover, .menu_simple ul li .current {
                            color: white;
                            background-color: #5FD367;
                        }
    </style>
    <script>
        function registerUser() {
            var days = document.getElementById('expiryDays').value;
            if (!days || days == 0) {
                alert("Please select validity period");
                return false;
            }
            return true;
        }
    </script>
</head>
<body>
    @if (ViewBag.passwordGeneratorUser != null)
    {
        var user = ViewBag.passwordGeneratorUser;
        <div style="margin: 0 auto;width: 650px;">
            <h1 style="text-align:center;">i2e1 Authorization Portal</h1>
        </div>
        <div class="pure-form pure-form-aligned" style="padding:10px;border:solid 5px #000000;width:650px;margin:0px auto;">
            <div class="menu_simple">
                <ul>
                    <li><a href="/PasswordGenerator">Register</a></li>
                    <li><a href="/PasswordGenerator/BulkUpload">BulkUpload</a></li>
                    <li><a href="/PasswordGenerator/ViewOTP">View OTP</a></li>
                    <li style="position:absolute;right:0;"><a href="/PasswordGenerator/Logout">Logout</a></li>
                </ul>
                <br />
            </div>
            <fieldset>
                @if (Model != null && Model.Count > 0)
                {
                    <h4>Registered Users</h4>
                    <table class="pure-table">
                        <thead>
                            <tr>
                                <th>User Id</th>
                                @*<th>Mac Id</th>*@
                                <th>OTP Issued Time</th>
                                <th>OTP</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach (var val in Model)
                            {
                                <tr>
                                    <td>
                                        @val.mobile
                                    </td>
                                   @* <td>
                                        @val.macId
                                    </td>*@
                                    <td>
                                        @val.planStartTime
                                    </td>
                                    <td>
                                        @val.otp
                                    </td>
                                </tr>
                            }
                        </tbody>
                    </table>
                }
                else
                {
                    <h4>No OTP found</h4>
                }
            </fieldset>
        </div>
    }
</body>
</html>