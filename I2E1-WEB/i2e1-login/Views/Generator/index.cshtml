@*@using i2e1_core.Models
@using I2E1_WEB.Models
@using I2E1_WEB.Utilities
@using System.Web
@using i2e1_core.Models.RouterPlan;
@model List<HomeRouterPlan>
    <!DOCTYPE html>
    <html>
    <head>
        <link rel="stylesheet" type="text/css" href="/styles/bootstrap/css/bootstrap.min.css">
        <script type="text/javascript" src="~/jsLibs/jquery.js"></script>
        <script type="text/javascript" src="~/jsLibs/moment-2-13-0.js"></script>
        <script type="text/javascript" src="~/jsLibs/datepicker/datepicker.js"></script>
        <link rel="stylesheet" type="text/css" href="~/jsLibs/datepicker/datepicker.css" />
        <meta name="viewport" content="width=device-width" />
        <title>i2e1 Password Generator</title>
        <link rel="stylesheet" href="~/styles/pure.css">
        <style>
            a {
                cursor: pointer;
            }

            .userDetailsDiv {
                border: 1px solid #aaa;
                padding: 5px;
            }

                .userDetailsDiv span {
                    width: 110px;
                    display: inline-block;
                }

            .datepicker.basic .picker table td, .datepicker.basic .picker table th {
                padding: 2px 0;
            }

            .alert {
                padding: 20px;
                background-color: rgba(244, 74, 54, 0.58);
                color: white;
            }

            .closebtn {
                margin-left: 15px;
                color: white;
                font-weight: bold;
                float: right;
                font-size: 22px;
                line-height: 20px;
                cursor: pointer;
                transition: 0.3s;
            }

                .closebtn:hover {
                    color: black;
                }

            table {
                table-layout: fixed;
            }

                table.detailsTable > tbody > tr > td {
                    width: 30%;
                    padding: 0 3px;
                    word-wrap: break-word;
                }

                table.detailsTable a {
                    padding: 5px 10px;
                    display: inline-block;
                    color: #fff;
                    text-decoration: none;
                    background-color: #B75000;
                }

            .notAllowed {
                background-color: #FFA5A5;
            }
            /* CSSTerm.com Simple CSS menu */

            br {
                clear: left;
            }

            .menu_simple {
                width: 100%;
                background-color: #005555;
                position: relative;
            }

                .menu_simple ul {
                    margin: 0;
                    padding: 0;
                    float: left;
                }

                    .menu_simple ul li {
                        display: inline;
                    }

                        .menu_simple ul li a {
                            float: left;
                            text-decoration: none;
                            color: white;
                            padding: 10.5px 11px;
                            background-color: #005555;
                        }

                            .menu_simple ul li a:visited {
                                color: white;
                            }

                            .menu_simple ul li a:hover, .menu_simple ul li .current {
                                color: white;
                                background-color: #5FD367;
                            }
        </style>
        <script>
            function registerUser() {

                var roomno = document.getElementById('roomno');
                var lastname = document.getElementById('lastname');
                if (roomno != null && lastname != null) {
                    var roomno = roomno.value;
                    var lastname = lastname.value;
                    document.getElementById('mobile').value = lastname + "@@" + roomno;
                }

                var days = document.getElementById('expiryDays').value;
                if (!days || days == 0) {
                    alert("Please select validity period");
                    return false;
                }
                return true;
            }
            function logoff(mobile) {
                window.location.href = '/PasswordGenerator/LogoutUser?username=' + mobile;
            }
            function authorize(mobile) {
                window.location.href = '/PasswordGenerator/AuthorizeUser?username=' + mobile;
            }
            function printAccessCode(username, accessCode) {
                w = window.open();
                if (username.indexOf('@@') > -1) {
                    username = username.replace('@@', ' <b>Room No<b>: ');
                }
                document.getElementById('pUsername').innerHTML = username;
                document.getElementById('pOtp').innerText = accessCode;
                w.document.write(document.getElementById('printDiv').innerHTML);

                var is_chrome = Boolean(w.chrome);
                if (is_chrome) {
                    setTimeout(function () {
                        w.focus();
                        w.print();
                        w.close();
                    }, 250);
                }
                else {
                    w.document.close();
                    w.focus();
                    w.print();
                    w.close();
                }
            }
            $(function () {
                $(".datepicker").datepicker({
                    dateFormat: "yy-mm-dd"
                });
            });
        </script>
    </head>
    <body>
      
            @if (ViewBag.passwordGeneratorUser != null)
            {
                var user = ViewBag.passwordGeneratorUser;
            <div style="margin: 0 auto;width: 750px;">
                <h1 style="text-align:center;">i2e1 Authorization Portal</h1>
            </div>
            <form class="pure-form pure-form-aligned" style="padding:10px;border:solid 5px #000000;width:850px;margin:0px auto;margin-top:30px;" method="post" onsubmit="return registerUser()" action="/PasswordGenerator/Generate">
                <div class="menu_simple">
                    <ul>
                        <li><a href="/PasswordGenerator">Register</a></li>
                        <li><a href="/PasswordGenerator/BulkUpload">BulkUpload</a></li>
                        <li><a href="/PasswordGenerator/ViewOTP">View OTP</a></li>
                        <li style="position:absolute;right:0;"><a href="/PasswordGenerator/Logout">Logout</a></li>
                    </ul>
                    <br />
                </div>
                <fieldset>
                    <div class="pure-control-group">
                        @if (user.authType == AuthType.LAST_NAME_ROOM_NO)
                        {<div class="pure-control-group">
                                <label for="Room No">Room No</label>
                                <input id="mobile" name="mobile" type="hidden" placeholder="Input ID" autocorrect="off" autocomplete="off" autocapitalize="off" required>
                                <input id="roomno" type="text" placeholder="Room No" autocorrect="off" autocomplete="off" autocapitalize="off" required>
                            </div>
                            <div class="pure-control-group">
                                <label for="lastname">Last Name</label>
                                <input id="lastname" type="text" placeholder="Last Name" autocorrect="off" autocomplete="off" autocapitalize="off" required>
                            </div>
                        }
                        else
                        {
                            <label for="mobile">Passport/Mobile/Access Code </label>
                            <input id="mobile" name="mobile" type="text" placeholder="Input ID" autocorrect="off" autocomplete="off" autocapitalize="off" required>
                        }
                    </div>


                    @if (user.askName)
                    {
                        <div class="pure-control-group">
                            <label for="name">Name</label>
                            <input id="name" name="name" type="text" placeholder="Name" />
                        </div>
                    }
                    @if (user.askEmail)
                    {
                        <div class="pure-control-group">
                            <label for="name">Email</label>
                            <input id="email" name="email" type="email" placeholder="Email" />
                        </div>
                    }
                    @if (user.changeDataPlan)
                    {
                        <div class="pure-control-group">
                            <label for="">Change Plan</label>
                            <select name="dataPlan">
                                <option value="0">Select</option>
                                <option value="50">50 MB</option>
                                <option value="100">100 MB</option>
                                <option value="200">200 MB</option>
                                <option value="500">500 MB</option>
                                <option value="1024">1 GB</option>
                                <option value="2048">2 GB</option>
                                <option value="5120">5 GB</option>
                                <option value="10240">10 GB</option>
                                <option value="51200">50 GB</option>
                                <option value="102400">Unlimited</option>
                            </select>
                        </div>
                    }
                    <div class="pure-control-group">
                        <label for="expiryDays">Validity Period</label>
                        <input class="datepicker" name="expiryTime" value="@DateTime.UtcNow.AddDays(1).ToString("dd.MM.yyyy")" required />
                    </div>
                    <div style="margin:0px auto;text-align:center;" class="pure-control-group">
                        <input value="Generate" type="submit" class="pure-button pure-button-primary" />
                    </div>
                </fieldset>
            </form>
            if (Model != null && Model.Count > 0)
            {
                <div class="pure-form pure-form-aligned" style="padding:10px;border:solid 5px #000000;border-top:0px;width:850px;margin:0px auto;">
                    <fieldset>
                        <h4>Registered Users</h4>
                        <div class="detailsTable">
                            @foreach (var pair in Model)
                            {
                                <div class="@(pair.authState ? "" : "notAllowed") userDetailsDiv" id="<EMAIL>">
                                    <form action="/PasswordGenerator/ChangePlan?mobile=@HttpUtility.UrlEncode(pair.mobile)" method="post" class="detailsForm">
                                        <input type="hidden" value="@pair.authState.ToString()" name="authState" class="authStateInput" />
                                        <span>
                                            <b>Username:</b><br />
                                            @if (pair.mobile.Contains('@'))
                                            {
                                                <span>
                                                    @pair.mobile.Split('@')[0] @pair.mobile.Split('@')[1]
                                                </span>
                                            }
                                            else
                                            {
                                                <span>
                                                    @pair.mobile
                                                </span>
                                            }
                                        </span>
                                        @if (user.askAccessCode)
                                        {
                                            <span>
                                                <b>Access Code:</b>
                                                @pair.otp
                                            </span>
                                        }
                                        @if (!string.IsNullOrEmpty(pair.name))
                                        {
                                            <span>
                                                <b>Name:</b><br />
                                                @pair.name
                                            </span>
                                        }
                                        <span>
                                            <b>Expiry Date:</b><br />
                                            <input class="datepicker" name="expiryTime" style="width:100px" value="@pair.otpExpiryTime.ToString("dd.MM.yyyy")" required />
                                        </span>
                                        @if (user.changeDataPlan)
                                        {
                                            <span style="position:relative;left:115px;">
                                                <b>Plan Details:</b>
                                                <select name="dataPlan">
                                                    @if (pair.dataPlan == 0)
                                                    {
                                                        <option value="0" selected>Select</option>
                                                    }
                                                    else
                                                    {
                                                        <option value="0">Select</option>
                                                    }

                                                    @if (pair.dataPlan == 50)
                                                    {
                                                        <option value="50" selected>50 MB</option>
                                                    }
                                                    else
                                                    {
                                                        <option value="50">50 MB</option>
                                                    }
                                                    @if (pair.dataPlan == 100)
                                                    {
                                                        <option value="100" selected>100 MB</option>
                                                    }
                                                    else
                                                    {
                                                        <option value="100">100 MB</option>
                                                    }
                                                    @if (pair.dataPlan == 200)
                                                    {
                                                        <option value="200" selected>200 MB</option>
                                                    }
                                                    else
                                                    {
                                                        <option value="200">200 MB</option>
                                                    }
                                                    @if (pair.dataPlan == 500)
                                                    {
                                                        <option value="500" selected>500 MB</option>
                                                    }
                                                    else
                                                    {
                                                        <option value="500">500 MB</option>
                                                    }
                                                    @if (pair.dataPlan == 1024)
                                                    {
                                                        <option value="1024" selected>1 GB</option>
                                                    }
                                                    else
                                                    {
                                                        <option value="1024">1 GB</option>
                                                    }
                                                    @if (pair.dataPlan == 2048)
                                                    {
                                                        <option value="2048" selected>2 GB</option>
                                                    }
                                                    else
                                                    {
                                                        <option value="2048">2 GB</option>
                                                    }
                                                    @if (pair.dataPlan == 5120)
                                                    {
                                                        <option value="5120" selected>5 GB</option>
                                                    }
                                                    else
                                                    {
                                                        <option value="5120">5 GB</option>
                                                    }
                                                    @if (pair.dataPlan == 10240)
                                                    {
                                                        <option value="10240" selected>10 GB</option>
                                                    }
                                                    else
                                                    {
                                                        <option value="10240">10 GB</option>
                                                    }
                                                    @if (pair.dataPlan == 102400)
                                                    {
                                                        <option value="102400" selected>Unlimited</option>
                                                    }
                                                    else
                                                    {
                                                        <option value="102400">Unlimited</option>
                                                    }


                                                </select>
                                            </span>
                                        }
                                        <span style="float:right;margin:10px;text-align:right;">
                                            <a title="Save" style="padding:5px; text-decoration:none" onclick="$(this).parents('.detailsForm')[0].submit()">
                                                <img width="15px;" src="~/images/save.png" />
                                            </a>
                                            @if (pair.authState)
                                            {
                                                <a title="Logout" style="padding:5px;color:#ba1515" class="glyphicon glyphicon-off" onclick="logoff('@pair.mobile')"></a>
                                            }
                                            else
                                            {
                                                <a title="Allow" style="padding:5px;" class="glyphicon glyphicon-ok" onclick="$(this).parents('.detailsForm').find('.authStateInput').val('True'); $(this).parents('.detailsForm')[0].submit()"></a>
                                            }
                                            @if (user.askAccessCode)
                                            {
                                                <a title="Print" onclick="printAccessCode('@pair.mobile', '@pair.otp')" class="glyphicon glyphicon-print"></a>
                                            }
                                        </span>
                                    </form>
                                </div>
                            }
                        </div>

                    </fieldset>
                </div>
            }
        }
        else
        {
            <h2 style=" text-align: center;margin: 80px 0 30px 0;">i2e1 Authorization Portal</h2>
            <form class="pure-form pure-form-aligned" style="padding:10px;border:solid 5px #000000;width:512px;margin:0px auto;" method="post" action="/PasswordGenerator/Login">
                <fieldset>
                    <div class="pure-control-group">
                        <label for="username">Enter username</label>
                        <input id="username" name="username" placeholder="username" autocorrect="off" autocomplete="off" autocapitalize="off" />
                    </div>
                    <div class="pure-control-group">
                        <label for="password">Enter password</label>
                        <input id="password" name="password" type="password" placeholder="password" />
                    </div>
                    <div style="margin:0px auto;text-align:center;" class="pure-control-group">
                        <input value="Login" type="submit" class="pure-button pure-button-primary" />
                    </div>

                    @if (ViewBag.PasswordGeneratorError != null)
                    {
                        <div class="alert" style="position: absolute; top: 5px; margin-left: 70px;">
                            <span class="closebtn" onclick="this.parentElement.style.display='none';">&times;</span>
                            <strong>Error!</strong> User Name or Password Incorrect
                        </div>
                    }
                </fieldset>
            </form>
        }

        <div id="printDiv" style="display:none;">
            <style>
                @@media print {
                    .imageDiv {
                        background-color: #bcbcbc !important;
                        -webkit-print-color-adjust: exact;
                    }
                }

                img {
                    -webkit-print-color-adjust: exact;
                }
            </style>
            <div align="center" style="font-family: sans-serif;border: 1px solid #ddd;width: 500px;margin: 5px auto;padding: 20px 10px;">
                <div class="imageDiv" style="text-align: right;background-color: #bcbcbc;padding: 20px 20px;margin-bottom: 20px;">
                    <img src="https://i2e1storage.blob.core.windows.net/images/indian-oil.png" style="float: left;height:65px;">
                    <img src="/images/logo.png" style="width: 100px;">
                </div>
                <b>Username</b>: <span id="pUsername"></span><br><b>Access Code</b>: <span id="pOtp"></span>
            </div>
        </div>
        <div class="footer" style="position: fixed;
        left: 0;
        right: 0;
        bottom: 0;
        text-align: center;
        background: white;
        border-top: 1px solid #c4c4c4;
        color: black;
        line-height: 2;
        font-weight: bold;
        font-size: 1.5rem;">
            For any issue or help, please call us on +91 8880322222 or
            mail us at: <EMAIL>
        </div>
    </body>
</html>*@