@using I2E1_Message.Utils
@using I2E1_Message.Models
@model List<I2E1_Message.Models.WikiData>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta http-equiv="Content-Type" content="text/html" charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=0">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/normalize/8.0.0/normalize.min.css" rel="stylesheet" />
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet" />
    @*<link href="@Util.GetUrlWithVersion("/Listing/material.min.css")" rel="stylesheet"/>*@
    <link href="~/styles/Lato.css" rel="stylesheet">
    <link href="@Util.GetUrlWithVersion("/Listing/500mInfo.css")" rel="stylesheet" />
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.3.1/jquery.min.js"></script>
    @*<script src="@Util.GetUrlWithVersion("/Listing/material.min.js")"></script>*@
    <script src="/Listing/lazysizes.min.js" async=""></script>
</head>
<body>
    @{
        Html.RenderPartial("~/Views/500mInfo/pages/pictures.cshtml", Model);
        Html.RenderPartial("~/Views/500mInfo/pages/wikipedia.cshtml", Model);
    }
@{
    if (ViewBag.isArea51)
    {
        <div class="mascot-fnc">
            <div class="container">
                <div class="head">
                    <span>Find & Connect</span>
                </div>
                <div class="content">
                    <div class="mascot-comment">
                        <button class="btn ripple" type="button" style="width: 45%; margin: 118px 0 0 0;" onclick='callAppFunction("goToFnC", [])'>
                            FIND & CONNECT
                        </button>
                    </div>
                </div>
            </div>
        </div>
    }
}
@*<div class="wifi_list">
    <div class="container">
        <div class="head">
            <span>WiFi Signals List</span>
        </div>
        <div class="content grid">
            <div>
                <span>Find WiFi Signals near me</span><br />
                <img class="wifi_signal" src="~/images/500mInfo/wifi_signal.svg" />
            </div>
            <button class="btn ripple" type="button" onclick='callAppFunction("goToList",[])'>
                <span>GO TO LIST</span>
            </button>
        </div>
    </div>
</div>*@
    @{
        if (ViewBag.isArea51)
        {
            <div class="story">
                <div class="container">
                    <div class="head">
                        <span>Once Upon A Time…</span><br />
                        <span class="sub_heading">Read inspirational stories of businesses around you</span>
                    </div>
                    <div class="content">
                        <div class="story_text">
                            <p>
                                Shop owner Mr. Vijender has done BSCHCM from JIMS, Delhi. After that he joined a 5 star hotel in Goa, and later went to New Zealand for higher studies.
                                In New Zealand he started as a housekeeping staff and very quickly escalated to housekeeping manager in a Japanese Restaurant.
                            </p>
                            <p class="read_more" id="story_readmore">Read more.</p>
                            <p class="extra_read">
                                Later he served as a Chef in an Indian Restaurant.
                                Now he is back in India, and has entrepreneurial ambition to make Pizza Eatery into a big Pizza Chain.
                                He has been recently married and stays with his family in Masudpur.<br />
                                <span class="read_less">Less.</span>
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        }
    }
    @*<div class="add_business">
        <div class="container">
            <div class="head">
                <span>Contribute To Society</span>
            </div>
            <div class="content grid">
                <div>
                    <span>Help your neighbourhood </span><br />
                    <span>businessman. Add him on Linq!</span><br />
                    <img class="wifi_signal" src="~/images/500mInfo/contribute.svg" />
                </div>
                <button class="btn ripple" type="button" onclick='callAppFunction("addBusiness", [])'>
                    <span>ADD BUSINESS</span>
                </button>
            </div>
        </div>
    </div>*@
    <script>
        $(".story_text .read_more").click(function () {
            $(this).hide();
            $(".extra_read").show();
        });
        $(".read_less").click(function () {
            $("#story_readmore").show();
            $(".extra_read").hide();
        });


        //cookie
        function getCookie(cname) {
            var name = cname + "=";
            var ca = document.cookie.split(';');
            for (var i = 0; i < ca.length; i++) {
                var c = ca[i];
                while (c.charAt(0) == ' ') c = c.substring(1);
                if (c.indexOf(name) != -1) return c.substring(name.length, c.length);
            }
            return "";
        }
        //check ios or android for data
        function callAppFunction(methodName, methodData) {
            if (getCookie('isios') == "True") {
                var iosData = {
                    methodName: methodName,
                    data: methodData
                }
                window.webkit.messageHandlers.iosCallback.postMessage(iosData);
                //console.log(iosData);
            } else {
                var functionString = "Android." + methodName;
                if (eval("typeof " + functionString) != "undefined") {
                    var funcString = 'Android.' + methodName + '(';
                    for (var i = 0; i < methodData.length; ++i) {
                        if (typeof methodData[i] == 'string')
                            funcString += "'" + methodData[i] + "'";
                        else
                            funcString += methodData[i];

                        if (i != methodData.length - 1)
                            funcString += ",";
                    }
                    console.log(funcString);
                    eval(funcString + ")");
                }
            }
        }
    </script>
</body>
</html>
