@using Amazon.S3.Model.Internal.MarshallTransformations
@using I2E1_Message.Models
@if (ViewBag.isArea51)
{
   <div class="pictures">
        <div class="pictures_container">
            <div class="pictures_head grid">
                <span>View In Pictures</span>
            </div>
            <div class="content">
                <div class="row">
                    @*@foreach (var wikiData in Model)
                            {
                            <div class="column">
                            <img src=@wikiData.wikiImageUrl style="width:100%">
                                </div>}*@
                    <section id="photos">
                        @foreach (var wikiData in ViewBag.WikiData)
                        {
                            var fullUrl = wikiData.wikiImageUrl;
                            if (!string.IsNullOrEmpty(fullUrl))
                            {
                                var newTumbUrl = fullUrl.Replace("commons", "commons/thumb");
                                int urlLen = newTumbUrl.Length;
                                var num = newTumbUrl.LastIndexOf("/");
                                var newUrl = newTumbUrl.Substring(0, num+1);
                                var restUrl = newTumbUrl.Substring(num+1);
                                var newRestUrl = "110px-" + restUrl;
                                var newCompleteUrl = newUrl + restUrl + "/" + newRestUrl;
                                <div class="column">
                                    <a href="@wikiData.wikiPageUrl" target="_blank">
                                        <img src="/images/500mInfo/image_placeholder.svg" class="lazyload" data-src=@newCompleteUrl onerror="this.onerror=null; this.src='../images/500mInfo/image_placeholder.svg'";/>
                                    </a>
                                </div>
                            }
                        }
                    </section>
                </div>
            </div>
        </div>
    </div>
}
<script>
    if ((@ViewBag.WikiData).count < 13) {
        $("#photos").css('column-count', '4');
    }else if ((@ViewBag.WikiData).count = 13 && (@ViewBag.WikiData).count < 25) {
        $("#photos").css('column-count', '8');
    } else {
        
        $("#photos").css('column-count', '11');
    }

</script>