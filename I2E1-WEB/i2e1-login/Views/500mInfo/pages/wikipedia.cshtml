@using Amazon.S3.Model.Internal.MarshallTransformations
@using I2E1_Message.Models
@{
    if (ViewBag.isArea51)
    {
        foreach (var wikiExtractData in ViewBag.ExtractData)
         {
             if (!string.IsNullOrEmpty(wikiExtractData.wikiPageExtract))
             {
                 <div class="wikipedia">
                     <div class="container">
                         <div class="wiki_head grid">
                             <div class="heading">
                                 <span>Wikipedia Says…</span>
                             </div>
                             <div class="wiki_img">
                                 <img src="~/images/500mInfo/wikipedia.svg"/>
                             </div>
                         </div>
                         <div class="content">
                             <div>
                                 <div>
                                     <span>
                                         @wikiExtractData.wikiPageExtract
                                     </span>
                                     <br/>
                                     <span class="read_more">
                                         <a href=@wikiExtractData.wikiPageUrl target="_blank">Read more.</a>
                                     </span>
                                 </div>
                             </div>
                         </div>
                     </div>
                 </div>
             }
         }
    }
}
