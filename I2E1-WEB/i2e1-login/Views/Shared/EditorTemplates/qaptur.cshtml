@using RestSharp.Extensions
@model List<KeyValuePair<string, string>>

<ul class="dropdown-ul-@ViewData["list-id"]" style="list-style: none; margin: 0; padding: 0; box-shadow: 0 1px 2px 0 #bcbcbc; height: 250px; overflow: auto; display: none;">
    @foreach (var pair in Model)
    {
        <li style="border-bottom: 1px solid #bcbcbc; min-height: 25px; text-align: center;padding:10px 0 0 0;" value="@pair.Value">@pair.Value</li>
    }
</ul>
    <span style="color: #ffbb00; font-size: 12px; font-style: italic;">Filled with Qaptur</span>
    <br />
<script>
    enableSmartListing('@ViewData["list-id"]');
</script>
