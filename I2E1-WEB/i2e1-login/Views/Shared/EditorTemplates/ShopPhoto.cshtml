@using RestSharp.Extensions
@using I2E1_Message.Models
@using i2e1_core.Models.SWAP

<div style="display: grid; grid-template-columns: 50% 50%;">
    <span class="@ViewData["id"]" style="padding-top: 4px;">@ViewData["label"]</span>
    <span style="color: #1da1f2;font-weight: bold;">
        <i class="material-icons camera" style="position: relative; top: 1px;">
            add_photo_alternate
            <input imgname="@ViewData["id"]" class="@ViewData["class"]" type="file" title="Profile Photo"/>
        </i>
    </span>
</div>
<div>
    @* <img class="photo-preview" src="@ShopPhoto.GetFullPhotoUrl(Model.photoUrl, int.Parse(ViewData["listingId"].ToString()))" id="@ViewData["id"]-img"
    style="@(string.IsNullOrEmpty(Model.photoUrl) ? "display: none;" : "")" >
    @Html.HiddenFor(m => Model.photoUrl, new { @class = "photo-url-db" })
    <div class="rmimage" style="@(string.IsNullOrEmpty(Model.photoUrl) ? "display:none;" : "") color: #1da1f2; font-size:16px; border: 1px solid #ffbb00; border-radius: 4px; width:25%;">
        <i class="fa fa-times-circle"> Delete</i>
    </div> *@
</div>