@using i2e1_core.Models
@using I2E1_Message.Utils
@model wiom_login_share.Models.User
@{
    Layout = "~/Views/Login/layout_base.cshtml";
}
@section ScriptSection {
    @if (ViewBag.GlobalOtp_Enabled != null && ViewBag.GlobalOtp_Enabled)
    {
        <link rel="stylesheet" href="/jsLibs/countrycode/intlTelInput.css">
        <script src="/jsLibs/countrycode/intlTelInput.js"></script>
    }

    <style>
        .group label {
            bottom: 0rem !important;
        }
        .group label.focus {
            bottom: 1.8rem !important;
        }
        .group input:focus ~ label {
            bottom: -1.8rem !important;
        }
    </style>

    <script type="text/javascript">
    var DIY = true;
    var _loginResponse;
    var _otpMsg = "Enter 4 digit password";
    var _loginUser = @Html.Raw(Newtonsoft.Json.JsonConvert.SerializeObject(Model));


    _viewBag.globalOtp_Enabled = '@(ViewBag.GlobalOtp_Enabled)';
    _viewBag.globalOTP_Enforce = '@(ViewBag.GlobalOTP_Enforce)';
    _viewBag.globalOTP_code = '@(ViewBag.GlobalOTP_code)';

    var _validateUsername = function () {
        if (_viewBag.GlobalOtp_Enabled == "True") return true;
        var username = document.getElementById("username").value;
        if (username == "3333333333") return true;
        if (/^[6-9]{1}$/.test(username.charAt(0))) {
            if (/^[6-9][0-9]{9}$/.test(username)) {
                return true;
            } else {
                _handleOTPError(_viewBag.resources.firstState_10_digit_phone_error, 'username', _i2e1Constants.firstState);
            }
        } else {
            _handleOTPError(_viewBag.resources.firstState_invalid_phno, 'username', _i2e1Constants.firstState);
        }
    };

    var _generateOTP = function (resend, successHandler) {
        if (_validateUsername()) {
            _logMPEvent(_i2e1Constants.firstState, { event: _i2e1Constants.generateOTPPressed });

            var username = document.getElementById("username");


            i2e1Api.generateOTPForRegistration($('#username').val(), {
                isresend: resend,
                onSuccess: function (response) {
                    _generateOTP.success(response, resend);
                },
                onFailure: function (response) {
                    _generateOTP.failure(response);
                },
                clientAuthType: _mapClientType(_loginUser.clientAuthType),
                globalOTP: _viewBag.GlobalOtp_Enabled == "True" ? true : false
            });
        }
    };

    _generateOTP.failure = function (response) {
        _handleOTPError(response.msg, 'username', _i2e1Constants.firstState);
    }

    _generateOTP.success = function (response, resend) {
        if (response.status == 1)
            _swapState(_i2e1Constants.errorState, response.msg);
        else if (response.status == 0) {
            _swapState(_i2e1Constants.secondState, { resend: resend, stateTransition: !resend });
        }
    }

    var _getMobile = function () {
        return document.getElementById("username").value;
    }

    var _getMobileWithCode = function () {
        return _viewBag.globalOTP_code ? _viewBag.globalOTP_code + '-' + _getMobile() : '+91-' + _getMobile();
    }

    var _getOTP = function () {
        return document.getElementById("digit-1").value + document.getElementById("digit-2").value + document.getElementById("digit-3").value + document.getElementById("digit-4").value;
    }

    var _connect = function () {
        _logMPEvent(_i2e1Constants.secondState, { event: _i2e1Constants.submitOTPPressed });
        var username = _getMobile();
        var otp = _getOTP();

        if (!_validateOTP(otp)) {
            _handleOTPError(_otpMsg, 'otp', _i2e1Constants.secondState);
            return;
        }

        i2e1Api.submitOTPForRegistration(username, otp, {
            name: '',
            onSuccess: _connect.success,
            onFailure: _connect.failure,
            captcha: document.getElementById('captcha').value
        });
    };

    _connect.success = function (response) {
        _swapState(_i2e1Constants.storeDetailState);
    };

    _connect.failure = function (response) {
        _handleOTPError("Invalid OTP", 'otp', _i2e1Constants.storeDetailState);
    };

    var _handleUsernameKeyPress = function (event, elem) {
        if (_viewBag.globalOtp_Enabled == "True" && _viewBag.globalOTP_code != "in") return;
        if (elem.value.length >= 10
            && event.which != 46 // delete
            && event.which != 8 // backspace
        ) {
            event.preventDefault();
        }
    }

    var otpStarPlaceholder = '****';
    var _handleOTPKeyPress = function (event, elem) {
        if (elem.value.length >= 4
            && event.which != 46 // delete
            && event.which != 8 // backspace
        ) {
            event.preventDefault();
        }
    }

    var _register = function () {
        _logMPEvent(_i2e1Constants.storeDetailState, { event: _i2e1Constants.submitDIYREGPressed });
        var shopName = $('#shopname').val();
        var address = $('#address').val();
        var email = $('#email').val();
        var pincode = $('#pincode').val();
        if (!shopName || shopName == "") {
            _handleOTPError("Invalid Shop name", 'shopname', _i2e1Constants.storeDetailState);
            return;
        }

        if (!address || address == "") {
            _handleOTPError("Invalid Address", 'address', _i2e1Constants.storeDetailState);
            return;
        }

        if (!pincode || pincode == "") {
            _handleOTPError("Invalid Pincode", 'pincode', _i2e1Constants.storeDetailState);
            return;
        }

        if (!email) {
            email = "";
        }

        _loginUser.mobile = _getMobile();
        _loginUser.otp = _getOTP();
        i2e1Api.submitDetailsForRegistration('@ViewBag.deviceId', shopName, address, pincode, email, _loginUser, {
            onSuccess: _register.success,
            onFailure: _register.failure,
        });
    };

    _startInternetSession = function () {
        console.log(_loginResponse);
        _makei2e1Login(_loginResponse);
    }

    _register.success = function (response) {
        $('.store-detail-state>div').toggleClass('display-none');
        _loginResponse = response.data;
    };

    _register.failure = function (response) {
        msg = "";
        switch (response.msg) {
            case "ERROR_OTP_INCORRECT":
                msg = "Your OTP is expired/Incorrect.";
                break;
            case "ERROR_ALREADY_REGISTERED":
                msg = "Router is already registered. Please reconnect to WiFi.";
                break;
            case "SUCCESS":
                $('.store-detail-state>div').toggleClass('display-none');
                _loginResponse = response.data;
            case "ERROR_DUPLICATE_SHOP_NAME":
                msg = "Please choose a different Shop Name. Simmilar one already exists.";
                break;
            default:
                msg = "Error: Cannot Register. Please contact Support.";
                break;
        };
        alert(msg);
    };

    _resendDIYCredentials = function () {
        _loginUser.mobile = _getMobile();
        _loginUser.otp = _getOTP();
        i2e1Api.resendDIYCredentials(_loginUser, {
            onSuccess: function () {

            },
            onFailure: function () {

            },
        });
    };
    </script>
}

<form class="form first-state state-transition display-none" onkeyup="_submitThis('_generateOTP');" onsubmit="return false;">
    <div class="barking">
        <div class="less-loud" i18n="firstState_bark_lessloud_wifi_reg">Pair your Box with your</div>
        <div class="loud" i18n="firstState_bark_loud_wifi_reg">Mobile Number</div>
    </div>
    <div class="username-area phone-number">
        <div class="title" i18n="helpState_enter_mobile_number">Enter Mobile Number</div>
        <div class="group">
            <input type="tel"
                   id="username"
                   name="username"
                   value="@(!string.IsNullOrEmpty(Model.mobile) && !Model.mobile.Contains('@')?Model.mobile: string.Empty)"
                   onfocus="$('[for=username]')[0].innerText=''"
                   onblur="$('[for=username]')[0].innerHTML = _viewBag.resources.firstState_enter_phone_number"
                   required="required" pattern=".*\S.*" />
            <label class="diy" for="username" name="username_for" i18n="firstState_enter_phone_number">e.g. 8880322222</label>
            <a onclick="$('#username').val('')[0].focus();$('[for=username]')[0].innerHTML = _viewBag.resources.firstState_enter_phone_number;">X</a>
        </div>
        <div class="error-message display-none" id="username-error" i18n="firstState_mobile_invalid">
            Invalid Phone Number
        </div>
        <div class="error-message display-none" id="fdm-notauthorised-error" i18n="firstState_fdm_notauthorised">
            Please Contact Reception for Internet Access
        </div>
    </div>
    <div cage="generate-otp">
        <input type="button" id="get_otp" name="enter" class="primary wiom_login_button" value="Verify Mobile via SMS" i18nInputValue="firstState_generate_otp" onclick="_generateOTP(false)" />
    </div>
    <div class="tnc-area">
        <span class="tnc1" cage="tnc" i18n="firstState_tnc">
            By continuing you accept <a onclick='_onTileClick("/Templates/i2e1/tnc.html")'>Terms & Conditions</a>
        </span>
    </div>
</form>
    <form class="form second-state state-transition display-none" onkeyup="_submitThis('_connect');" onsubmit="return false;">
        <div class="barking">
            <div class="loud" i18n="secondState_bark_loud">Verify Number</div>
            <div class="less-loud" i18n="secondState_bark_lessloud">with Password (OTP) Sent using SMS</div>
        </div>
        <div class="otp-area" style="margin-top:50px">
            <div class="otp-title title" i18n="secondState_enter_4_digit_password">Enter 4 digit password</div>
            <div class="group" id="otp-group" style="margin-bottom:10px">
                <input type="number" min="0" max="9" id="digit-1" name="digit-1" data-next="digit-2" maxlength="1" />
                <input type="number" min="0" max="9" id="digit-2" name="digit-2" data-next="digit-3" data-previous="digit-1" maxlength="1" />
                <input type="number" min="0" max="9" id="digit-3" name="digit-3" data-next="digit-4" data-previous="digit-2" maxlength="1" />
                <input type="number" min="0" max="9" id="digit-4" name="digit-4" data-next="digit-5" data-previous="digit-3" maxlength="1" />
            </div>
            <div class="error-message display-none" id="otp-error" i18n="secondState_otp_invalid">
                Invalid OTP
            </div>
        </div>
        <div id="captcha-holder" style="display:none;">
            <div>
                <span i18n="secondState_captcha">Captcha</span><br />
                <img id="captcha-img" title="captcha" src="" />
            </div>
            <div>
                <span style="opacity:0;" secondState_enter_captcha>Enter Captcha</span>
                <input id="captcha" placeholder="Enter Captcha" type="text" />
            </div>
        </div>
        <div cage="connect-button">
            <input type="button" id="connect" class="primary wiom_login_button" i18nInputValue="secondState_verifyOTP" value="Verify OTP" onclick="_connect(false)" />
        </div>
        <div class="bottom-button-group">
            <div class="back-area" onclick="_swapState(_i2e1Constants.firstState, {resend: false, reverse: true})">
                <span i18n="secondState_change">Change Number</span>
            </div>
            <div id="primary-resend-area" class="resend-otp-area">
                <input type="button" id="resend-otp" class="small_button primary" disabled i18nInputValue="secondState_resend" value="Resend" onclick="_generateOTP(true)" />
            </div>
        </div>
    </form>
    <form class="form store-detail-state state-transition display-none" onkeyup="_submitThis('_register');" onsubmit="return false;">
        <div class="reg-successfull display-none" style="font-weight: bolder;">
            Registration Successful
            <img src="~/images/success_check_circle.png" style="width:60%;max-width:16rem;" />
            <br />
            You will recieve an SMS on your registered Mobile Number with your WIOM Username, Password and Control Panel Link.
            <div cage="connect-button">
                <input style="color:white !important" type="button" class="primary wiom_login_button" i18nInputValue="storeDetailState_connect_to_internet" value="Connect to Internet" onclick="_startInternetSession(false)" />
            </div>
            <br />
            <p style="font-size:70% !important">
                If you havent recieved an SMS please click <input type="button" class="primary small_button " i18nInputValue="storeDetailState_resend_diy_credentials" value="Resend" onclick="_resendDIYCredentials(false)" />
            </p>
        </div>
        <div class="shop-name-area">
            <div class="title mandatory" i18n="storeDetailState_enter_shop_name">Enter Shop Name</div>
            <div class="group">
                <input type="text"
                       id="shopname"
                       required="required"
                       pattern=".*\S.*"
                       autocomplete="off"
                       onfocus="$('html, body').animate({ scrollTop: 50 }, 300); $('[for=shopname]')[0].innerText=''"
                       onblur="$('[for=shopname]')[0]=_viewBag.resources.storeDetailState_enter_shop_name_ph"/>
                <label class="diy" for="shopname" i18n="storeDetailState_enter_shop_name_ph">e.g. Sharma General Store</label>
            </div>
        </div>
        <div class="user-email-area">
            <div class="title mandatory" i18n="storeDetailState_enter_shop_email">Enter Email</div>
            <div class="group">
                <input type="text"
                       id="email"
                       required="required"
                       autocomplete="off"
                       onfocus="$('html, body').animate({ scrollTop: 50 }, 300); $('[for=email]')[0].innerText=''"
                       onblur="$('[for=email]')[0]=_viewBag.resources.storeDetailState_enter_shop_email_ph"/>
                <label class="diy" for="email" i18n="storeDetailState_enter_shop_email_ph">e.g. <EMAIL></label>
            </div>
        </div>
        <div class="user-email-area">
            <div class="title mandatory" i18n="storeDetailState_address">Enter Shop Address</div>
            <div class="group">
                <input type="text"
                       id="address"
                       required="required"
                       pattern=".*\S.*"
                       autocomplete="off"
                       onfocus="$('html, body').animate({ scrollTop: 50 }, 300); $('[for=address]')[0].innerText=''"
                       onblur="$('[for=address]')[0]=_viewBag.resources.storeDetailState_address_ph" />
                <label class="diy" for="address" i18n="storeDetailState_address_ph">e.g. 315/274 Ground Floor</label>
            </div>
        </div>
        <div class="address-area">
            <div class="title mandatory" i18n="storeDetailState_pioncode">Enter Address Pincode</div>
            <div class="group">
                <input type="text"
                       id="pincode"
                       required="required"
                       pattern=".*\S.*"
                       autocomplete="off"
                       onfocus="$('html, body').animate({ scrollTop: 50 }, 300); $('[for=pincode]')[0].innerText=''"
                       onblur="$('[for=pincode]')[0]=_viewBag.resources.storeDetailState_pincode_ph" />
                <label class="diy" for="pincode" i18n="storeDetailState_pincode_ph">e.g. 110050</label>
            </div>

        </div>
        <div cage="connect-button">
            <input type="button" id="connect" class="primary wiom_login_button" i18nInputValue="storeDetailState_register" value="Save" onclick="_register(false)" />
        </div>
    </form>
<script>
    var _init_device_reg = function () {
        var div = document.createElement('h2');
        div.innerText = "Device Registration";
        document.getElementsByTagName('body')[0].prepend(div);
        document.getElementsByTagName('title')[0].innerText = div.innerText;
    }
    // Customizations for WIOM Registration
    $('.language-selectors').hide();
    $('#otp-group').find('input').each(function () {
        $(this).attr('maxlength', 1);
        $(this).on('keyup input', function (e) {
            var parent = $($(this).parent());
            if (e.keyCode === 8 || e.keyCode === 37) {
                var prev = parent.find('input#' + $(this).data('previous'));
                if (prev.length) {
                    $(prev).select();
                }
            } else if ((e.keyCode >= 48 && e.keyCode <= 57) || (e.keyCode >= 65 && e.keyCode <= 90) || (e.keyCode >= 96 && e.keyCode <= 105) || e.keyCode === 39) {
                var next = parent.find('input#' + $(this).data('next'));
                if (next.length) {
                    $(next).select();
                }
            }
        });
    });
</script>