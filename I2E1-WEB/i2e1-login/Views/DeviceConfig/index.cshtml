@using I2E1_WEB.Utilities;
@using wifidog_core.Models
@model List<DeviceConfig>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>i2e1 Device Configuration</title>
    <link href="https://fonts.googleapis.com/css?family=Open+Sans:100,300,400,600" rel="stylesheet" type="text/css">
    <link href="https://code.ionicframework.com/ionicons/2.0.1/css/ionicons.min.css" rel="stylesheet" type="text/css">
    <link rel="stylesheet" href="~/styles/pure.css">
    <link type="text/css" rel="stylesheet" href="~/styles/deviconfigstyle.css">
    <script src="https://ajax.googleapis.com/ajax/libs/jquery/1.11.2/jquery.min.js"></script>

    <script>

        function showdialog(){
            var message = '@ViewBag.message'
            console.log(message)
            if (message.length > 1) {
                alert(message);
            }
        }

        $(document).ready(function () {
            $('input[type="file"]').change(function (e) {
                var fileName = e.target.files[0].name;
                $('#fname1').html(fileName);
            });
        });
        function chunk(str, n) {
            var ret = [];
            var i;
            var len;
            for (i = 0, len = str.length; i < len; i += n) {
                ret.push(str.substr(i, n))
            }
            return ret
        };
        function checkmac() {
            mac1 = document.getElementById("macid1").value;
            mac2 = document.getElementById("macid2").value;
            mac1 = mac1.replace(/-/g, "");
            mac1 = mac1.replace(/:/g, "");
            mac2 = mac2.replace(/-/g, "");
            mac2 = mac2.replace(/:/g, "");
            var regex = /^([0-9A-Fa-f]{2}){5}([0-9A-Fa-f]{2})$/;

            if (!regex.test(mac1) || !regex.test(mac2)) {
                alert("Invalid Mac Address");
                return false;
            }
            if (mac1.length != 12 || mac2.length != 12) {
                alert("Invalid Mac Length");
                return false;
            }

            if (mac1.localeCompare(mac2) == 0) {
                /*var htmlString = $('body').html().toString();
                console.log(htmlString);
                mac1=chunk(mac1, 2).join('-');
                console.log(mac1);
                var index = htmlString.indexOf(mac1);

                if (index != -1){
                    alert("mac taken");
                    return false

                }*/
                return true;
            }
            else {
                alert("Mac and confirm Mac do not match");
                //                        document.getElementById("errormessage").style.display = 'block';
                return false;
            }
        }
        function toggledisplay(show, hide1, hide2) {
            document.getElementById(show).style.display = "block";
            document.getElementById(hide1).style.display = "none";
            document.getElementById(hide2).style.display = "none";
        }

        function navigate(target, mac) {
            //Perform your navigation
            console.log(target);
            console.log(mac);
            window.location.href = target + '?mac=' + mac;
        }
        function highlight(aa) {
            var button = aa + "+1";
            document.getElementById(button).disabled = false;
            var Dplan = document.getElementById(aa).value;
            document.getElementById('temp+' + aa).value = Dplan;
            console.log(document.getElementById('temp+' + aa).value);
        }
        function enablebutton(buttonid) {
            console.log(buttonid);
            document.getElementById(buttonid).disabled = false;
        }

        $(document).ready(function () {
            $("#btnExport").click(function (e) {
                e.preventDefault();

                //getting data from our table
                var data_type = 'data:application/vnd.ms-excel';
                var table_div = document.getElementById('tabledata');
                var table_html = table_div.outerHTML.replace(/ /g, '%20');

                var a = document.createElement('a');
                a.href = data_type + ', ' + table_html;
                a.download = 'exported_table_' + Math.floor((Math.random() * 9999999) + 1000000) + '.xls';
                a.click();
            });
        });

    </script>
</head>
<body onload="showdialog()">
    <div class="top">
        <div class="budget">
            <div class="budget__title">
                <h1>i2e1 Device Configuration Portal</h1>
            </div>
        </div>
    </div>


    <div class="bottom">
        <button style="float:right" class="add__btn"><a class="ion-power" style="float:right;" href="/DeviceConfig/Logout"></a></button>
        <div class="add" id="configure">
            <div class="add__container">
                <button style="float:left" class="add__btn"><i class="ion-ios-search-strong" onclick="toggledisplay('search','configure','bulkupload')"></i></button>
                <button style="float:left" class="add__btn"><i class="ion-upload" onclick="toggledisplay('bulkupload','search','configure')"></i></button>

                <h3>Configure New Device</h3><br />
                <form name="myForm" id="myForm" method="post" onsubmit="return checkmac();" action="/DeviceConfig/SubmitConfig">
                    <input type="text" id="macid1" name="macid" class="add__description" placeholder="Mac" required>
                    <input type="text" id="macid2" class="add__description" placeholder="Confirm Mac" required>
                    <input type="number" id="nasid" name="nasid" class="add__value" placeholder="Nasid" required>
                    <input type="number" id="controller" name="controllerId" class="add__value" placeholder="Controller" required>
                    <select id="deviceType" name="deviceType" class="add__type">
                        <option value="841">841</option>
                        <option value="3020">3020</option>
                        <option value="3420">3420</option>
                        <option value="8968">8968</option>
                        <option value="3600">3600</option>
                        <option value="1043">1043</option>
                        <option value="Archer C5">Archer C5</option>
                        <option value="Archer C7">Archer C7</option>
                        <option value="Archer C20(US)">"Archer C20(US)</option>
                        <option value="ThinPc">Thin Pc</option>
                        <option value="HFCL">HFCL</option>
                        <option value="Mikrotik">Mikrotik</option>
                    </select>
                    <select id="productId" name="productId" class="add__type">
                        <option value="1">i2e1</option>
                        <option value="10001">WIOM Basic</option>
                        <option value="10002">WIOM Magic(DIY)</option>
                        <option value="10003">WIOM PDO</option>
                    </select>
                    <select id="channelType" name="channelType" class="add__type">
                        <option value="1">Direct Offline Sales</option>
                        <option value="2">Online</option>
                        <option value="3">Distributor</option>
                        <option value="4">Strategic Partner</option>
                    </select>
                    <input type="text" name="channelName" id="appassword" class="add__description" placeholder="Channel Name">
                    <input type="text" name="accessPointPassword" id="appassword" class="add__description" placeholder="AP Password">
                    <input type="text" name="devicePassword" id="devicepassword" class="add__description" placeholder="Luci Password">
                    <label>Monitor Mode</label><input type="checkbox" name="monitormode" id="MonitorMode" />
                    <button class="add__btn"><i class="ion-ios-checkmark-outline"></i></button>
                </form>
            </div>
        </div>
        <div class="add" id="search" style="display: none">
            <div class="add__container">
                <button style="float:left" class="add__btn"><i class="ion-android-add-circle" onclick="toggledisplay('configure','search','bulkupload')"></i></button>
                <button style="float:left" class="add__btn"><i class="ion-upload" onclick="toggledisplay('bulkupload','search','confiure')"></i></button>

                <h3>Search Device</h3><br />
                <form action="/DeviceConfig/GetDeviceConfigs" method="post">
                    <input type="text" name="macid" class="add__description" placeholder="Mac">
                    <input type="number" name="nasid" class="add__value" placeholder="Nasid">
                    <button class="add__btn"><i class="ion-ios-checkmark-outline"></i></button>
                </form>
            </div>
        </div>
        <div class="add" id="bulkupload" style="display: none">
            <div class="add__container">
                <button style="float:left" class="add__btn"><i class="ion-ios-search-strong" onclick="toggledisplay('search','configure','bulkupload')"></i></button>
                <button style="float:left" class="add__btn"><i class="ion-android-add-circle" onclick="toggledisplay('configure','search','bulkupload')"></i></button>
                <h3>Bulk Upload Data From:<span id="fname1">No File Selected</span></h3>   <br />
                <table>
                    <tr>
                        <td>
                            <form name="myForm" id="myForm1" method="post" enctype="multipart/form-data" action="/DeviceConfig/SubmitConfigFromExcel">
                                <div class="upload-btn-wrapper">
                                    <button class="btn">Device Config file</button>
                                    <input type="file" name="myfile" />
                                </div>

                                <button class="add__btn"><i class="ion-ios-checkmark-outline"></i></button>

                            </form>
                            * <a href="http://i2e1storage.blob.core.windows.net/uploadedresources/Template.xls">Download Template</a>
                        </td>
                        <td>
                            <form name="myForm2" id="myForm2" method="post" enctype="multipart/form-data" action="/DeviceConfig/BulkUploadPortalEntry">
                                <div class="upload-btn-wrapper">
                                    <button class="btn">Portal Csv file</button>
                                    <input type="file" name="csv" />
                                </div>

                                <button class="add__btn"><i class="ion-ios-checkmark-outline"></i></button>

                            </form>
                            * <a href="https://i2e1-storage.s3.ap-south-1.amazonaws.com/PortalBulk.csv">Download Template</a>
                        </td>
                        <td>
                            <form name="myForm3" id="myForm3" method="post" enctype="multipart/form-data" action="/DeviceConfig/BulkUploadAuthEntry">
                                <div class="upload-btn-wrapper">
                                    <button class="btn">Authencation Csv file</button>
                                    <input type="file" name="csv" />
                                </div>

                                <button class="add__btn"><i class="ion-ios-checkmark-outline"></i></button>

                            </form>
                            * <a href="https://i2e1-storage.s3.ap-south-1.amazonaws.com/authBulk.csv">Download Template</a>
                        </td>
                        <td>
                            <form name="myForm4" id="myForm4" method="post" enctype="multipart/form-data" action="/DeviceConfig/BulkUploadVipEntry">
                                <div class="upload-btn-wrapper">
                                    <button class="btn">Vip Csv file</button>
                                    <input type="file" name="csv" />
                                </div>

                                <button class="add__btn"><i class="ion-ios-checkmark-outline"></i></button>

                            </form>
                            * <a href="https://i2e1-storage.s3.ap-south-1.amazonaws.com/VIPBulk.csv">Download Template</a>
                        </td>
                        <td>
                            <form name="myForm5" id="myForm5" method="post" enctype="multipart/form-data" action="/DeviceConfig/BulkUploadNasIDs">
                                <div class="upload-btn-wrapper">
                                    <button class="btn">Nas ping Csv file</button>
                                    <input type="file" name="csv" />
                                </div>

                                <button class="add__btn"><i class="ion-ios-checkmark-outline"></i></button>

                            </form>
                            * <a href="https://i2e1-storage.s3.ap-south-1.amazonaws.com/naslistBulk.csv">Download Template</a>
                        </td>
                        <td>
                            <form name="myForm6" id="myForm6" method="post" enctype="multipart/form-data" action="/DeviceConfig/BulkUploadStoreContact">
                                <div class="upload-btn-wrapper">
                                    <button class="btn">Store Contact Csv file</button>
                                    <input type="file" name="csv" />
                                </div>

                                <button class="add__btn"><i class="ion-ios-checkmark-outline"></i></button>

                            </form>
                            * <a href="https://i2e1-storage.s3.ap-south-1.amazonaws.com/BulkUploadStoreContact.csv">Download Template</a>
                        </td>
                        <td>
                            <form name="myForm6" id="myForm6" method="post" enctype="multipart/form-data" action="/DeviceConfig/BulkConfigureSecondaryNasIDs">
                                <div class="upload-btn-wrapper">
                                    <button class="btn">Nas,Secondary nas mapping</button>
                                    <input type="file" name="csv" />
                                </div>

                                <button class="add__btn"><i class="ion-ios-checkmark-outline"></i></button>

                            </form>
                            * <a href="https://i2e1-storage.s3.ap-south-1.amazonaws.com/nassecondarynasmapping.csv">Download Template</a>
                        </td>
                    </tr>
                </table>





            </div>
        </div>

        <div class="container clearfix">
            <div class="income">
                <a style="background:#0C4DA2" class="pure-button pure-button-primary" id="btnExport">Export To Excel</a>

                <p class="add__btn"><a class="ion-refresh" href="/DeviceConfig/Index"></a></p>
                @if (Model != null && Model.Count > 0)
                {
                    <p style="float:right" class="add__btn"><a class="ion-android-arrow-forward" href="/DeviceConfig/GetConfigsView?seek=1"></a></p>
                }
                @if (ViewBag.DevicePageCounter > 1)
                {
                    <button style="float:right" class="add__btn ion-android-radio-button-off"></button>
                    <p style="float:right" class="add__btn"><a class="ion-android-arrow-back" href="/DeviceConfig/GetConfigsView?seek=-1"></a></p>
                }


                @if (Model != null && Model.Count > 0)
                {
                    <table class="pure-table" style="width: 1800px;border-radius: 25px;" id="customers">
                        <thead>
                            <tr>
                                <th>Nasid</th>
                                <th>Secondary Nasid</th>
                                <th>Mac Id</th>
                                <th>Device Type</th>
                                <th>Product</th>
                                <th>Channel Type</th>
                                <th>Channel Name</th>
                                <th>Added Time</th>
                                <th>Last Modified Time</th>
                                <th>Passwords</th>
                                <th></th>
                            </tr>
                        </thead>
                        @foreach (var pair in Model)
                        {
                            <form id="@pair.nasid" action="/DeviceConfig/EditConfig" method="post">
                            <tr>
                                <td style="width:10%"><input type="number" name="nasid" value="@pair.nasid" onchange="enablebutton('@pair.macid')" size="6"></td>
                                <td style="width:10%"><input type="number" name="secondaryNasid" value="@pair.secondaryNasid" onchange="enablebutton('@pair.macid')" size="6"></td>
                                <td>@pair.macid <input type="text" name="macid" value="@pair.macid" hidden></td>
                                <td>
                                    <select id="deviceType" name="deviceType" onchange="enablebutton('@pair.macid')">
                                        @if (pair.deviceType == "841")
                                        {
                                            <option value="841" selected>841</option>
                                        }
                                        else
                                        {
                                            <option value="841">841</option>
                                        }
                                        @if (pair.deviceType == "3020")
                                        {
                                            <option value="3020" selected>3020</option>
                                        }
                                        else
                                        {
                                            <option value="3020">3020</option>
                                        } @if (pair.deviceType == "3420")
                                        {
                                            <option value="3420" selected>3420</option>
                                        }
                                        else
                                        {
                                            <option value="841">3420</option>
                                        } @if (pair.deviceType == "8968")
                                        {
                                            <option value="8968" selected>8968</option>
                                        }
                                        else
                                        {
                                            <option value="8968">8968</option>
                                        } @if (pair.deviceType == "3600")
                                        {
                                            <option value="3600" selected>3600</option>
                                        }
                                        else
                                        {
                                            <option value="3600">3600</option>
                                        } @if (pair.deviceType == "1043")
                                        {
                                            <option value="1043" selected>1043</option>
                                        }
                                        else
                                        {
                                            <option value="1043">1043</option>
                                        } @if (pair.deviceType == "Archer")
                                        {
                                            <option value="Archer" selected>Archer</option>
                                        }
                                        else
                                        {
                                            <option value="Archer">Archer</option>
                                        } @if (pair.deviceType == "Archer C5")
                                        {
                                            <option value="Archer C5" selected>Archer C5</option>
                                        }
                                        else
                                        {
                                            <option value="Archer C5">Archer C5</option>
                                        } @if (pair.deviceType == "Archer C7")
                                        {
                                            <option value="Archer C7" selected>Archer C7</option>
                                        }
                                        else
                                        {
                                            <option value="Archer C7">Archer C7</option>
                                        } @if (pair.deviceType == "Archer C20(US)")
                                        {
                                            <option value="Archer C20(US)" selected>Archer C20(US)</option>
                                        }
                                        else
                                        {
                                            <option value="Archer C20(US)">Archer C20(US) C20(US)</option>
                                        }
                                        @if (pair.deviceType == "HFCL")
                                        {
                                            <option value="HFCL" selected>HFCL</option>
                                        }
                                        else
                                        {
                                            <option value="HFCL">HFCL</option>
                                        }
                                        @if (pair.deviceType == "MIKROTIK")
                                        {
                                            <option value="MIKROTIK" selected>MIKROTIK</option>
                                        }
                                        else
                                        {
                                            <option value="MIKROTIK">MIKROTIK</option>
                                        }
                                    </select>
                                </td>
                                <td>
                                    <select id="productId" name="productId" onchange="enablebutton('@pair.macid')">
                                        @if (pair.productId.ToString() == "1")
                                        {
                                            <option value="1" selected>i2e1</option>
                                        }
                                        else
                                        {
                                            <option value="1">i2e1</option>
                                        }
                                        @if (pair.productId.ToString() == "10001")
                                        {
                                            <option value="10001" selected>WIOM Basic</option>
                                        }
                                        else
                                        {
                                            <option value="10001">WIOM Basic</option>
                                        }
                                        @if (pair.productId.ToString() == "10002")
                                        {
                                            <option value="10002" selected>WIOM Magic(DIY)</option>
                                        }
                                        else
                                        {
                                            <option value="10002">WIOM Magic(DIY)</option>
                                        }
                                        @if (pair.productId.ToString() == "10003")
                                        {
                                            <option value="10003" selected>WIOM PDO</option>
                                        }
                                        else
                                        {
                                            <option value="10003">WIOM PDO</option>
                                        }

                                    </select>
                                </td>
                                <td>
                                    <select id="channelType" name="channelType" onchange="enablebutton('@pair.macid')">
                                        @if (pair.channelType.ToString() == "1")
                                        {
                                            <option value="1" selected>Direct Offline Sales</option>
                                        }
                                        else
                                        {
                                            <option value="1">Direct Offline Sales</option>
                                        }
                                        @if (pair.channelType.ToString() == "2")
                                        {
                                            <option value="2" selected>Online</option>
                                        }
                                        else
                                        {
                                            <option value="2">Online</option>
                                        }
                                        @if (pair.channelType.ToString() == "3")
                                        {
                                            <option value="3" selected>Distributor</option>
                                        }
                                        else
                                        {
                                            <option value="3">Distributor</option>
                                        }
                                        @if (pair.channelType.ToString() == "4")
                                        {
                                            <option value="4" selected>Strategic Partner</option>
                                        }
                                        else
                                        {
                                            <option value="4">Strategic Partner</option>
                                        }


                                    </select>
                                </td>
                                <td><input type="text" name="channelName" value="@pair.channelName" onchange="enablebutton('@pair.macid')" /></td>
                                <td>@pair.addedtime</td>
                                <td>@pair.modtime</td>
                                <td>
                                    <input type="text" name="accessPointPassword" value="@pair.accessPointPassword" hidden /><br>
                                    <input type="text" name="devicePassword" value="@pair.devicePassword" hidden />
                                    AP:
                                    @if (string.IsNullOrEmpty(pair.accessPointPassword))
                                    {
                                        <input type="text" name="accessPointPasswordNew" onchange="enablebutton('pair.macid')" /><br>
                                    }
                                    else
                                    {
                                        <input type="text" name="accessPointPasswordNew" onchange="enablebutton('pair.macid')" required value="@pair.accessPointPassword" /><br>
                                    }
                                    Device:
                                    @if (string.IsNullOrEmpty(pair.devicePassword))
                                    {
                                        <input type="text" name="devicePasswordNew" onchange="enablebutton('@pair.macid')" />
                                    }
                                    else
                                    {
                                        <input type="text" name="devicePasswordNew" onchange="enablebutton('@pair.macid')" value="@pair.devicePassword" required />
                                    }
                                </td>
                                <td>
                                    <input value="Save" style="background:#0C4DA2" type="submit" class="pure-button pure-button-primary" id="@pair.macid" onclick="document.getElementById(@pair.macid).submit()" disabled />
                                    @if (pair.status)
                                    {
                                        <a href="@Url.Action("ToggleStatus", "DeviceConfig", new { mac = pair.macid })"><input value="Deactivate" style="background:#FF0000" type="button" class="pure-button pure-button-primary" /></a>
                                    }
                                    else
                                    {
                                        <a href="@Url.Action("ToggleStatus", "DeviceConfig", new { mac = pair.macid })"><input value="Activate" style="background:#008000" type="button" class="pure-button pure-button-primary" /></a>
                                    }

                                    @if (!pair.isOnPortal)
                                    {
                                        <br /><br /> <a href="@Url.Action("AddToTController", "DeviceConfig", new { nasid = pair.nasid })"><input value="Add To Portal" style="background:#a21fd8" type="button" class="pure-button pure-button-primary" /></a>
                                    }


                                </td>
                            </tr>
                            </form>
                        }
                    </table>
                    <fieldset id="tabledata" style="display:none">
                        <table class="pure-table" style="display:none">
                            <thead>
                                <tr>
                                    <th>Nasid</th>
                                    <th>Mode</th>
                                    <th>Mac Id</th>
                                    <th>Device Type</th>
                                    <th>Product</th>
                                    <th>Channel Type</th>
                                    <th>Channel Name</th>
                                    <th>Added  Time</th>
                                    <th>Last Modified Time</th>
                                    <th>Active</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach (var pair in Model)
                                {
                                    <tr>
                                        <form id="@pair.nasid" action="/DeviceConfig/EditConfig" method="post">

                                            <td style="width:10%">@pair.nasid</td>
                                            @if (pair.deviceMode == "0")
                                            {
                                                <td>1</td>
                                            }
                                            else
                                            {
                                                <td>0</td>
                                            }
                                            <td>@pair.macid</td>
                                            <td>pair.deviceType</td>
                                            @switch (pair.productId.ToString())
                                            {
                                                case "10002":
                                                    <td>WIOM Magic</td>
                                                    break;
                                                case "10001":
                                                    <td>WIOM Basic</td>
                                                    break;
                                                case "10003":
                                                    <td>WIOM PDO</td>
                                                    break;
                                                default:
                                                    <td>i2e1</td>
                                                    break;
                                            };
                                            @switch (pair.channelType.ToString())
                                            {
                                                case "1":
                                                    <td>Online</td>
                                                    break;
                                                case "2":
                                                    <td>Distributor</td>
                                                    break;
                                                case "3":
                                                    <td>Strategic Partner</td>
                                                    break;
                                                default:
                                                    <td>Direct Offline Sales</td>
                                                    break;
                                            };
                                            <td>@pair.channelName</td>
                                            <td>@pair.addedtime</td>
                                            <td>@pair.modtime</td>
                                            <td>@pair.status</td>
                                        </form>
                                    </tr>
                                }
                        </table>
                    </fieldset>
                }
            </div>
        </div>
    </div>
</body>
</html>