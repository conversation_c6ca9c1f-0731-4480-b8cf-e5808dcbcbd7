@using i2e1_core.Models
@using I2E1_Message.Utils
@using I2E1_WEB
@model wiom_login_share.Models.User

<!DOCTYPE html>
<html>
<head>
    <title>i2e1 login</title>
    <meta name="theme-color" content="#ffffff" />
    <meta name="viewport" content="width=device-width, height=device-height, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0, user-scalable=no" />
    <link rel="shortcut icon" href="images/favicon.ico" type="image/x-icon">
    <link rel="stylesheet" type="text/css" href="@Util.GetUrlWithVersion("/Templates/i2e1/site.css")" />
    <script src="@Util.GetUrlWithVersion("/jsLibs/jquery.js")"></script>
    <script src="@Util.GetUrlWithVersion("/Templates/i2e1/i2e1-sdk.js")"></script>
    <script src="@Util.GetUrlWithVersion("/Templates/i2e1/template-utils.js")"></script>
    <script>
        var macError = '@(ViewBag.macError == null ? "" : ViewBag.macError)';
        var _loginUser = @Html.Raw(Newtonsoft.Json.JsonConvert.SerializeObject(Model));
        var mac = null;
        function _registration() {
            if (!mac) {
                window.location.search.substr(1).split('&').forEach((pair) => {
                    if (pair.indexOf('gw_id') == 0) {
                        mac = pair.split('=')[1];
                        mac = mac.split('_')[0];
                    }
                });
            }
            window.location.href = "/DeviceConfig/DIYRegistration?deviceId=@ViewBag.deviceId";
        }
    </script>
</head>
<body>
    <div class="top-container">
        <div class="login container">
            <div class="language-selectors">
                <a href="#" onclick="_changeLanguage('en')" id="en">English</a>
                <a href="#" onclick="_changeLanguage('hi')" id="hi">हिंदी</a>
                <a href="#" onclick="_changeLanguage('te')" id="te">తెలుగు</a>
            </div>
            <div class="outer-card1" cage="outer-header">
                <img class="back-button silent" src="~/images/back.png" onclick="_back()" />
                <img id="outer-card-img" class="wiom-logo" src="~/images/wiom_logo_small.png" />
            </div>
            <div class='form custom-state state-transition'>
                <div class="barking">
                    <div class="loud" i18n="coming_soon_bark_loud_wifi_reg" style="font-size: 1.5rem;margin-top:4.5rem">
                        Welcome to Wiom Wifi <br/>
                        WANI Enabled PDO
                    </div>
                </div>
                <div style="margin: 8.5rem 0 1.5rem 0;font-size: .9rem;color: #999999;font-weight: 700;">If you are the merchant, click below to start</div>
                <button class="primary wiom_login_button" onclick="_registration(false)" style="margin-bottom: 3rem;">Start Now</button>
            </div>
            <div class="footer">
                <div class="top-border"></div>
                <span i18n="footer_facing_issues_call_us_at" style="margin-bottom: 1.8rem;">Get Help - <b><a href="tel:8880322222">8880322222</a></b></span>
            </div>
        </div>
    </div>
</body>
</html>