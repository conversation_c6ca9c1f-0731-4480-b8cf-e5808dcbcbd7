@using wifidog_core.Models
@model Speedtest

@if (Model.nasid == null)
{
    <h1>Missing Parameter : nasid</h1>
}
else
{
    <!DOCTYPE html>
    <html>
    <head>
        <meta charset="utf-8" />
        <title></title>
        <script type="text/javascript">
            function watchForResults() {
                document.getElementById('waiting').style.display = "block";
                document.getElementById('results').style.display = "none";
                fetch('https://i2e1.in/deviceConfig/GetSpeedtestResults?nasid=@Model.nasid&operationId=@Model.operation_id')
                .then(
                    function (response) {
                        console.log(response);
                        if (response.status !== 200) {
                            alert('Looks like there was a problem. Status Code: ' +
                                response.status);
                            return;
                        }
                        // Examine the text in the response
                        response.json().then(function (data) {
                            if (data.data && data.data.result && data.data.result == "true") {
                                document.getElementById('waiting').style.display = "none";
                                document.getElementById('results').style.display = "block";
                                document.getElementById('download_bw').innerHTML = data.data.download_bw + ' Mbps';
                                document.getElementById('timestamp').innerHTML = data.data.timestamp;
                            } else {
                                setTimeout(function () { alert("Refreshing!"); watchForResults() }, 20000);
                            }
                        });
                    }
                )
                .catch(function (err) {
                    watchForResults();
                });
            };
        </script>
    </head>
    <body onload="watchForResults()">
        <p id="waiting">Waiting for Results. This page Auto-Refreshes every 20 seconds.</p>
        <table id="results">
            <tr>
                <th>Download Speed</th>
                <th>Timestamp</th>
            </tr>
            <tr>
                <td id="download_bw"></td>
                <td id="timestamp"></td>
            </tr>
        </table>
    </body>
</html>
}
