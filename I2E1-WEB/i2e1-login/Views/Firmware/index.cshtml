<!DOCTYPE html>

<html lang="en">
<head>
    <meta charset="utf-8" />
    <title>Omnia Firmware Selector</title>
    <link rel="stylesheet" href="https://i2e1-storage.s3.ap-south-1.amazonaws.com/openwrt/index.css" />
    <script src="https://i2e1-storage.s3.ap-south-1.amazonaws.com/openwrt/i18n.js"></script>
    <script src="https://i2e1-storage.s3.ap-south-1.amazonaws.com/openwrt/config.js"></script>
    <script src="https://i2e1-storage.s3.ap-south-1.amazonaws.com/openwrt/index.js"></script>
</head>
<body onload="init()">

    <header>
        <div>
            <!--
                    <h6 class="tr-title">OpenWrt Firmware Selector</h6>
            -->
            <img src="https://i2e1-storage.s3.ap-south-1.amazonaws.com/openwrt/logo.svg" alt="Logo" style="max-width: 5%;
        max-height: 5%;">
            <div style="flex-grow: 1;"></div>

            <select id="language-selection" size="1">
                <option value="ca">Català</option>
                <option value="en">English</option>
                <option value="es">Español</option>
                <option value="de">Deutsch</option>
                <option value="fr">Français</option>
                <option value="it">Italiano</option>
                <option value="no">Norsk</option>
                <option value="pl">Polski</option>
                <option value="tr">Türkçe</option>
            </select>
        </div>
    </header>

    <div class="container">
        <div>
            <h2 class="tr-load">Download firmware!</h2>
            <p class="tr-message">Please use the input below to download firmware for your device!</p>
            <br>

            <select id="versions" size="1"></select>
            <div class="autocomplete">
                <input id="models" type="text" placeholder="Model" spellcheck="false" autocapitalize="off" autocorrect="off">
            </div>

            <br />
            <br />

            <div>
                <img id="buildspinner" src="spinner.gif" alt="Logo">
                <div id="buildstatus"></div>
            </div>

            <div id="images">
                <div id="custom">
                    <h3 class="tr-customize">Customize</h3>
                    <textarea id="packages" spellcheck="false" autocapitalize="off" autocorrect="off">luci</textarea>
                    <a href="javascript:build_asa_request()" class="custom-link">
                        <span>&#9881;</span><span class="tr-request-build">Request Build</span>
                    </a>
                </div>

                <div>
                    <h3 id="images-title" class="tr-version-build">Release Build</h3>
                    <div><span class="column tr-model">Model:</span> <span id="image-model"></span></div>
                    <div><span class="column tr-target">Target:</span> <span id="image-target"></span></div>
                    <div><span class="column tr-version">Version:</span> <span id="image-version"></span> (<span id="image-code"></span>)</div>
                    <div><span class="column tr-date">Date:</span> <span id="image-date"></span></div>
                    <div><span class="column tr-tags">Tags:</span> <span id="image-tags"></span></div>
                    <div><span class="column tr-comment">Comment:</span> <span id="image-comment"></span></div>
                </div>

                <div id="download-links">
                    <h3 id="downloads-title" class="tr-downloads">Downloads</h3>
                </div>

                <div>
                    <span id="factory-help" class="download-help tr-factory-help">Factory images are for flashing routers with OpenWrt for the first time using the web interface of the original firmware.</span>
                    <span id="sysupgrade-help" class="download-help tr-sysupgrade-help">Sysupgrade images are for flashing routers that already run OpenWrt. The image can be applied using the web interface or the console.</span>
                    <span id="kernel-help" class="download-help tr-kernel-help">Linux kernel as a separate image.</span>
                    <span id="rootfs-help" class="download-help tr-rootfs-help">Root file system as a separate image.</span>
                    <span id="sdcard-help" class="download-help tr-sdcard-help">Image that is meant to be flashed on an SD-Card.</span>
                    <span id="tftp-help" class="download-help tr-tftp-help">Image that can be applied using the TFTP meachnism of the bootloader</span>
                    <span id="other-help" class="download-help tr-other-help">Image of unknown purpose.</span>
                </div>
            </div>

            <div id="footer">
                <span><a href="https://github.com/mwarning/yet_another_firmware_selector">YAFS</a> v2.2.0</span>
            </div>
        </div>
    </div>

</body>
</html>
