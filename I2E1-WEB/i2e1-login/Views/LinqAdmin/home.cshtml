<!DOCTYPE html>
<html lang="en">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1.0" />
    <title>Linq Admin Dashboard</title>
    <script src="https://cdn.jsdelivr.net/npm/vue@2.5.17/dist/vue.js"></script>
    <!-- CSS  -->
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
    <link href="css/materialize.css" type="text/css" rel="stylesheet" media="screen,projection" />
    <link href="css/style.css" type="text/css" rel="stylesheet" media="screen,projection" />
    <link href="https://maxcdn.bootstrapcdn.com/font-awesome/4.1.0/css/font-awesome.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/select2/4.0.6-rc.0/css/select2.min.css" rel="stylesheet" />

</head>
<body>
    <div id="top"></div>
    <main id="linq-admin">
        <div class="navbar-fixed">
            <nav class="nav-extended">
                <div class="nav-wrapper linq-blue">
                    <a href="#" data-target="listing-sidenav" class="sidenav-trigger show-on-large"><i class="material-icons">menu</i></a>
                    <a href="#!" class="brand-logo"><img src="assets/linq_logo_orange.svg" style="height: 50px; padding-top:15px;" /></a>
                </div>
            </nav>
        </div>
        <ul class="sidenav" id="listing-sidenav">
            <li>
                <div class="user-view">
                    <div class="background">
                        <img src="../images/500mInfo/old_paper.png">
                    </div>
                    <a href="#">
                        <img v-if="linqAdminInfo.picture == ''" class="circle" src="../images/swapp/swaap_logo.png" />
                        <img v-if="linqAdminInfo.picture != ''" class="circle" :src="linqAdminInfo.picture" />
                    </a>
                    <a href="#"><span class="white-text name">Hello {{linqAdminInfo.firstName}} of House {{linqAdminInfo.lastName}}</span></a>
                    <button v-on:click="logout()" class="btn-small waves-effect waves-red">Logout<i class="material-icons right">exit_to_app</i></button>
                </div>
            </li>
            <li><a href="Categories"><i class="material-icons">label</i>Metadata</a></li>
            <li><a href="Users"><i class="material-icons">perm_identity</i>Meddlers</a></li>
            <li><a href="Listings"><i class="material-icons">location_city</i>Listings</a></li>
        </ul>
        <div id="app">
            <div id="categories-tab" class="col s12">
                <br /><br />
                <div class="section no-pad-bot">
                    <div class="container">
                        <h5>Categories</h5><a v-on:click="showAddCategoryModal()" class="waves-effect waves-green btn-small">Add</a>
                        <category-add-modal v-bind:cat="newCategory"
                                            v-on:add-category="addCategory(newCategory)">
                        </category-add-modal>
                        <table class="striped">
                            <category-header></category-header>
                            <tbody>
                                <category-row v-for="category in categories"
                                              v-bind:cat="category"
                                              v-bind:key="category.id"
                                              v-on:launch-edit-category="showEditCategoryModal(category)">
                                </category-row>
                            </tbody>
                        </table>
                        <category-edit-modal v-bind:cat="selectedCategory"
                                             v-on:delete-category="deleteCategory(selectedCategory)"
                                             v-on:edit-category="editCategory(selectedCategory)">
                        </category-edit-modal>
                    </div>
                </div>
            </div>
            <div id="subcategories-tab" class="col s12">
                <div class="section no-pad-bot">
                    <div class="container">
                        <br /><br />
                        <h5>Sub Categories</h5><a v-on:click="showAddSubCategoryModal()" class="waves-effect waves-green btn-small">Add</a>
                        <subcategory-add-modal v-bind:subcat="newSubCategory"
                                               v-on:add-subcategory="addSubCategory(newSubCategory)">
                        </subcategory-add-modal>
                        <table class="striped">
                            <subcategory-header></subcategory-header>
                            <tbody>
                                <subcategory-row v-for="subCategory in subCategories"
                                                 v-bind:subcat="subCategory"
                                                 v-bind:key="subCategory.subCategoryId"
                                                 v-on:launch-edit-subcategory="showEditSubCategoryModal(subCategory)">
                                </subcategory-row>
                            </tbody>
                        </table>
                        <subcategory-edit-modal v-bind:subcat="selectedSubCategory"
                                                v-on:delete-subcategory="deleteSubCategory(selectedSubCategory)"
                                                v-on:edit-subcategory="editSubCategory(selectedSubCategory)">
                        </subcategory-edit-modal>
                    </div>
                </div>
            </div>
            <div id="tags-tab" class="col s12">
                <div class="section no-pad-bot">
                    <div class="container">
                        <br /><br />
                        <h5>Tags</h5><a v-on:click="showAddTagModal()" class="waves-effect waves-green btn-small">Add</a>
                        <tag-add-modal v-bind:tagged="newTag"
                                       v-on:add-tag="addTag(newTag)">
                        </tag-add-modal>
                        <table class="striped">
                            <tags-header></tags-header>
                            <tbody>
                                <tags-row v-for="tag in tags"
                                          v-bind:tagged="tag"
                                          v-bind:key="tag.subCategoryId+tag.tagName"
                                          v-on:inline-delete-tag="deleteTag(tag)"
                                          v-on:launch-edit-tag="showEditTagModal(tag)">
                                </tags-row>
                            </tbody>
                        </table>
                        <tag-edit-modal v-bind:tagged="selectedTag"
                                        v-on:delete-tag="deleteTag(selectedTag)"
                                        v-on:edit-tag="editTag(selectedTag)">
                        </tag-edit-modal>
                    </div>
                </div>
            </div>
        </div>
    </main>
    <footer class="page-footer orange">
        <div class="container">
        </div>
        <div class="footer-copyright">
            <div class="container">
                © 2019 Copyright Linq
                <a class="grey-text text-lighten-4 right" href="#">Made by blackrat@i2e1</a>
            </div>
        </div>
    </footer>
    <!--  Scripts-->
    <script src="https://code.jquery.com/jquery-2.1.1.min.js"></script>
    <script src="js/jquery.floatThead.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/select2/4.0.6-rc.0/js/select2.min.js"></script>
    <script src="js/materialize.js"></script>
    <script src="https://unpkg.com/axios/dist/axios.min.js"></script>
    <script src="js/init.js"></script>
    <script src="js/application.js"></script>
</body>
</html>
