<!DOCTYPE html>
<html lang="en">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1.0" />
    <title>Linq Admin Dashboard</title>
    <script src="https://cdn.jsdelivr.net/npm/vue@2.5.17/dist/vue.js"></script>
    <!-- CSS  -->
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
    <link href="css/materialize.css" type="text/css" rel="stylesheet" media="screen,projection" />
    <link href="css/style.css" type="text/css" rel="stylesheet" media="screen,projection" />
    <link href="https://maxcdn.bootstrapcdn.com/font-awesome/4.1.0/css/font-awesome.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/select2/4.0.6-rc.0/css/select2.min.css" rel="stylesheet" />

</head>
<body>
    <div id="top"></div>
    <main id="linq-admin">
        <div class="navbar-fixed">
            <nav class="nav-extended">
                <div class="nav-wrapper linq-blue">
                    <a href="#" data-target="listing-sidenav" class="sidenav-trigger show-on-large"><i class="material-icons">menu</i></a>
                    <a href="#!" class="brand-logo"><img src="assets/linq_logo_orange.svg" style="height: 50px; padding-top:15px;" /></a>
                </div>
            </nav>
        </div>
        <ul class="sidenav" id="listing-sidenav">
            <li>
                <div class="user-view">
                    <div class="background">
                        <img src="../images/500mInfo/old_paper.png">
                    </div>
                    <a href="#">
                        <img v-if="linqAdminInfo.picture == ''" class="circle" src="../images/swapp/swaap_logo.png" />
                        <img v-if="linqAdminInfo.picture != ''" class="circle" :src="linqAdminInfo.picture" />
                    </a>
                    <a href="#"><span class="white-text name">Hello {{linqAdminInfo.firstName}} of House {{linqAdminInfo.lastName}}</span></a>
                    <button v-on:click="logout()" class="btn-small waves-effect waves-red">Logout<i class="material-icons right">exit_to_app</i></button>
                </div>
            </li>
            <li><a href="Categories"><i class="material-icons">label</i>Metadata</a></li>
            <li><a href="Contacts"><i class="material-icons">perm_identity</i>Meddlers</a></li>
            <li><a href="Listings"><i class="material-icons">location_city</i>Listings</a></li>
        </ul>
        <div id="app">
            <div id="salescontacts-tab" class="col s12">
                <br /><br />
                <div class="section no-pad-bot">
                    <div class="container">
                        <br /><br />
                        <h5>Meddlers</h5>
                        <h6>Meddlers have the power to edit any listing using the Admin UI</h6>
                        <table class="striped">
                            <contact-header></contact-header>
                            <tbody style="display:block; height: 512px; overflow: auto;">
                                <contact-row v-for="(contact, index) in contacts.salesContacts"
                                             v-bind:contact="contact"
                                             v-bind:key="contact+'::'+index"
                                             v-on:delete-contact="deleteContact('salesContacts', index)">
                                </contact-row>
                                <tr>
                                    <td><a v-on:click="newContact('salesContacts')" class="waves-effect waves-green btn-small">Add</a></td>
                                    <td></td>
                                    <td><a v-on:click="saveContacts(contacts)" class="waves-effect waves-green btn-small">Save</a></td>
                                    <td></td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
        <footer class="page-footer orange">
            <div class="footer-copyright">
                <div class="container">
                    Made by blackrat@i2e1
                </div>
            </div>
        </footer>
    </main>
    <!--  Scripts-->
    <script src="https://code.jquery.com/jquery-2.1.1.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/select2/4.0.6-rc.0/js/select2.min.js"></script>
    <script src="js/materialize.js"></script>
    <script src="https://unpkg.com/axios/dist/axios.min.js"></script>
    <script src="js/init.js"></script>
    <script src="js/application.js"></script>
</body>
</html>
