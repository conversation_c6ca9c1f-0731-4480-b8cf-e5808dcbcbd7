<!DOCTYPE html>
<html lang="en">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1.0" />
    <meta http-equiv="X-UA-Compatible" content="IE=10; IE=9; IE=8; IE=7; IE=EDGE" />
    <title>Linq Admin > Listings</title>
    <script src="https://cdn.jsdelivr.net/npm/vue@2.5.17/dist/vue.js"></script>
    <!-- CSS  -->
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
    <link href="css/materialize.css" type="text/css" rel="stylesheet" media="screen,projection" />
    <link href="css/style.css" type="text/css" rel="stylesheet" media="screen,projection" />
    <link href="css/style.listing.css" type="text/css" rel="stylesheet" media="screen,projection" />
    <link href="https://maxcdn.bootstrapcdn.com/font-awesome/4.1.0/css/font-awesome.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/select2/4.0.6-rc.0/css/select2.min.css" rel="stylesheet" />
</head>
<body>
    <div id="top"></div>
    <main id="linq-admin">
        <div class="navbar-fixed">
            <nav class="nav-extended">
                <div class="nav-wrapper linq-blue">
                    <a href="#" data-target="listing-sidenav" class="sidenav-trigger show-on-large"><i class="material-icons">menu</i></a>
                    <a href="#!" class="brand-logo"><img src="assets/linq_logo_orange.svg" style="height: 50px; padding-top:15px;" /></a>
                </div>
            </nav>
        </div>
        <ul class="sidenav" id="listing-sidenav">
            <li>
                <div class="user-view">
                    <div class="background">
                        <img src="../images/500mInfo/old_paper.png">
                    </div>
                    <a href="#">
                        <img v-if="linqAdminInfo.picture == ''" class="circle" src="../images/swapp/swaap_logo.png" />
                        <img v-if="linqAdminInfo.picture != ''" class="circle" :src="linqAdminInfo.picture" />
                    </a>
                    <a href="#"><span class="white-text name">Hello {{linqAdminInfo.firstName}} of House {{linqAdminInfo.lastName}}</span></a>
                    <button v-on:click="logout()" class="btn-small waves-effect waves-red">Logout<i class="material-icons right">exit_to_app</i></button>
                </div>
            </li>
            <li><a href="Categories"><i class="material-icons">label</i>Metadata</a></li>
            <li><a href="Users"><i class="material-icons">perm_identity</i>Meddlers</a></li>
            <li><a href="Listings"><i class="material-icons">location_city</i>Listings</a></li>
        </ul>
        <div id="app">
            <listing-search-modal v-bind:searchphrase="searchPhrase"
                                  v-on:search-listings="searchListings(searchPhrase)">
            </listing-search-modal>

            <listing-transfer-modal v-bind:listing="listingToTransfer"
                                    v-bind:userinfo="userInfo"
                                    v-bind:ownerinfo="ownerInfo"
                                    v-bind:admins="listingAdmins"
                                    v-on:get-user-info="getUserInfo(listingAdmins.owner)"
                                    v-on:transfer-listing="transferListing(listingAdmins)">
            </listing-transfer-modal>
            <div id="listings-tab" class="col s12">
                <div class="section no-pad-bot">
                    <div class="container" id="listing-table">
                        <div class="row">
                            <div class="col s10">
                                <h5 style="margin-top:0px">Listings</h5>
                            </div>
                            <div class="col s2">
                                <span v-show="!searchMode && listingStartIndex > 0"><a v-on:click="previousPage()" class="btn-floating btn-small green hide-on-small-only show-on-med-and-up"><i class="material-icons">chevron_left</i></a></span>
                                <span v-show="!searchMode && listingEndIndex <= totalListings"><a v-on:click="nextPage()" class="btn-floating btn-small blue hide-on-small-only show-on-med-and-up"><i class="material-icons">chevron_right</i></a></span>
                            </div>
                        </div>


                        <h6 v-if="searchMode">Search Results for {{searchPhrase.search}}. <a class="hover-pointer" v-on:click="endSearchMode()">Close (x)</a></h6>
                        <desktop-listing-search v-bind:searchphrase="searchPhrase"
                                                v-on:search-listings="searchListings(searchPhrase)">
                        </desktop-listing-search>
                        <table class="striped">
                            <thead>
                                <tr>
                                    <th>id</th>
                                    <th>Owner</th>
                                    <th>Address</th>
                                    <th>City</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr is="listing-row"
                                    v-for="(listing, index) in currentListings.listings"
                                    v-bind:listing="listing"
                                    v-bind:idx="index"
                                    v-bind:key="listing.listingId+'::'+index"
                                    v-on:launch-edit-listing="openEditListing(listing)"
                                    v-on:launch-transfer-listing="openTransferListing(listing)"
                                    v-on:launch-listing-assets="openListingAssets(listing)"
                                    v-on:toggle-listing-active="setLinqActiveStatus(listing, idx)"
                                    v-on:delete-listing="deleteListing(listing, idx)">
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
                <div class="fixed-action-btn show-on-small hide-on-med-and-up">
                    <a class="btn-floating btn-large red">
                        <i class="large material-icons">looks</i>
                    </a>
                    <ul>
                        <li><a class="btn-floating yellow darken-1"><i v-on:click="openSearchListings()" class="material-icons">search</i></a></li>
                        <li v-show="!searchMode && listingStartIndex > 0"><a v-on:click="previousPage()" class="btn-floating green"><i class="material-icons">chevron_left</i></a></li>
                        <li v-show="!searchMode && listingEndIndex <= totalListings"><a v-on:click="nextPage()" class="btn-floating blue"><i class="material-icons">chevron_right</i></a></li>
                    </ul>
                </div>
                <div class="fixed-action-btn hide-on-small-only show-on-medium-and-up">
                    <a class="btn-floating yellow darken-1"><i v-on:click="gotoDesktopSearchListings()" class="material-icons">search</i></a>
                </div>
            </div>
        </div>
    </main>
    <footer class="page-footer orange">
        <div class="container">
        </div>
        <div class="footer-copyright">
            <div class="container">
                © 2019 Copyright Linq
                <a class="grey-text text-lighten-4 right" href="#">Made by blackrat@i2e1</a>
            </div>
        </div>
    </footer>
    <!--  Scripts-->
    <script src="https://code.jquery.com/jquery-2.1.1.min.js"></script>
    <script src="js/jquery.floatThead.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/select2/4.0.6-rc.0/js/select2.min.js"></script>
    <script src="js/materialize.js"></script>
    <script src="https://unpkg.com/axios/dist/axios.min.js"></script>
    <script src="js/init.js"></script>
    <script src="js/application.listings.js"></script>
</body>
</html>
