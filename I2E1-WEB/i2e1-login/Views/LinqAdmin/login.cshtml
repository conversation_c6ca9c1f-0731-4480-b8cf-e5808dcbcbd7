<html itemscope itemtype="http://schema.org/Article">
<head>
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1.0" />
    <script src="//ajax.googleapis.com/ajax/libs/jquery/1.8.2/jquery.min.js"></script>
    <script src="https://apis.google.com/js/api:client.js?onload=init" async defer></script>
    </script>
    <script>
        var googleUser = {};
        function init() {
            gapi.load('auth2', function () {
                auth2 = gapi.auth2.init({
                    client_id: '303939939579-a02pc4b6m9hno2a2qggc2kjrdi5c3rko.apps.googleusercontent.com',
                    cookiepolicy: 'single_host_origin'
					
                }).then(function (GoogleAuth) {
                    GoogleAuth.attachClickHandler(
                        document.getElementById('signinButton'),
                        {},
                        function (googleUser) {
                            document.getElementById('googleToken').value = googleUser.getAuthResponse().id_token;
                            document.getElementById('authType').value = 'LINQ_ADMIN';
                            document.getElementById('loginForm').submit();
                            return true;
                        },
                        function (error) {
                            alert(JSON.stringify(error));
                        }
                    );
                });
            });
        }
    </script>
    <style>
        body {
            background-color: #fb0;
            padding-top: 10rem;
            background: linear-gradient(91deg, #ffbb00, #1da1f2);
            background-size: 400% 400%;
            -webkit-animation: bg 6s ease infinite;
            -moz-animation: bg 6s ease infinite;
            animation: bg 6s ease infinite;
        }

        div#signinButton {
            display: block;
            width: 160px;
            margin: 0px auto;
            padding: 5px 20px 5px 20px;
            box-shadow: rgba(0,0,0,0.1) 2px 2px 5px 2px;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            font-size: 14px;
            border-radius: 5px;
            -webkit-transition: box-shadow 0.2s; /* Safari */
            transition: box-shadow 0.2s;
            background-color: #fff;
            line-height: 20px;
        }

            div#signinButton:hover {
                box-shadow: rgba(0,0,0,0.2) 5px 5px 5px 2px;
                cursor: pointer;
            }

            div#signinButton .icon {
                display: inline-block;
                width: 20px;
                height: 20px;
                margin-top: 5px;
            }

            div#signinButton .buttonText {
                display: inline-block;
                height: 20px;
                margin-top: -12px;
                vertical-align: middle;
            }

            div#signinButton .icon svg {
            }

        div.details {
            padding-top: 50px;
            display: block;
            margin: 0px auto;
            height: 500px;
            width: 400px;
            background-color: #1da1f2;
            border-radius: 10px;
            box-shadow: rgba(0,0,0,0.1) 2px 2px 5px 2px;
        }

        .logo {
            height: 100px;
            display: block;
            margin: 0px auto;
            margin-bottom: 70px;
        }

        div#gSignInWrapper {
            margin-top: 100px;
        }

        h6.title {
            text-align: center;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            font-size: 36px;
            color: #fff;
            text-shadow: 2px 2px 2px #aaa;
        }

        h4.footer {
            text-align: center;
            color: #fff;
            font-weight: 400;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin-top: 100px;
            font-size: 12px;
        }

        @@-webkit-keyframes bg {
            0% { background-position: 0% 52% }
            50% { background-position: 100% 49% }
            100% { background-position: 0% 52% }
        }
        @@-moz-keyframes bg {
            0% { background-position: 0% 52% }
            50% { background-position: 100% 49% }
            100% { background-position: 0% 52% }
        }
        @@keyframes bg {
            0% { background-position: 0% 52% }
            50% { background-position: 100% 49% }
            100% { background-position: 0% 52% }
        }
    </style>
</head>
<body>
    <form id="loginForm" method="post" action="/Client/LoginWeb" class="i2e1-form">
        <input type="hidden" name="googleToken" id="googleToken" />
        <input type="hidden" name="authType" id="authType" />
    </form>
    <div class="details">
        <img class="logo" src="assets/linq_logo_orange.svg" />
        <h6 class="title">Linq Admin Login</h6>
        <div class="cell">
            <div id="gSignInWrapper">
                <div id="signinButton" class="customGPlusSignIn">
                    <span class="icon">
                        <svg version="1.1" xmlns="http://www.w3.org/2000/svg" width="20px" height="20px" viewBox="0 0 48 48" class="abcRioButtonSvg">
                            <g>
                                <path fill="#EA4335" d="M24 9.5c3.54 0 6.71 1.22 9.21 3.6l6.85-6.85C35.9 2.38 30.47 0 24 0 14.62 0 6.51 5.38 2.56 13.22l7.98 6.19C12.43 13.72 17.74 9.5 24 9.5z"></path>
                                <path fill="#4285F4" d="M46.98 24.55c0-1.57-.15-3.09-.38-4.55H24v9.02h12.94c-.58 2.96-2.26 5.48-4.78 7.18l7.73 6c4.51-4.18 7.09-10.36 7.09-17.65z"></path>
                                <path fill="#FBBC05" d="M10.53 28.59c-.48-1.45-.76-2.99-.76-4.59s.27-3.14.76-4.59l-7.98-6.19C.92 16.46 0 20.12 0 24c0 3.88.92 7.54 2.56 10.78l7.97-6.19z"></path>
                                <path fill="#34A853" d="M24 48c6.48 0 11.93-2.13 15.89-5.81l-7.73-6c-2.15 1.45-4.92 2.3-8.16 2.3-6.26 0-11.57-4.22-13.47-9.91l-7.98 6.19C6.51 42.62 14.62 48 24 48z"></path>
                                <path fill="none" d="M0 0h48v48H0z"></path>
                            </g>
                        </svg>
                    </span>
                    <span class="buttonText">Sign in Using Google</span>
                </div>
            </div>
        </div>
        <div class="cell">
            <h4 class="footer">Copyright i2e1 2019</h4>
        </div>
    </div>
</body>
</html>