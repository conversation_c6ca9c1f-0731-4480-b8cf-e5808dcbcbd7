@using I2E1_Message.Utils
<!DOCTYPE html>
<html ng-app="oneInsightsApp">
<head>
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0, user-scalable=no" />
    <title page-title></title>
    <!-- Styles-->
    <link rel="shortcut icon" href="../images/favicon.ico" type="image/x-icon">
    <link rel="stylesheet" type="text/css" href="../jsLibs/bootstrap/bootstrap_paper.min.css" />
    <link rel="stylesheet" type="text/css" href="@Util.GetUrlWithVersion("styles/oneinsights.css")" />   

    <!--------------- Font Awsome -------------->
    <link rel="stylesheet" type="text/css" href="../jsLibs/font-awesome/css/font-awesome.min.css" />

    <!-- Scripts Libraries-->
    <script src="../jsLibs/jquery.js"></script>
    <script src="../jsLibs/moment-2-13-0.js"></script>
    <script src="~/jsLibs/angular143.min.js" type="text/javascript"></script>
    <script src="//ajax.googleapis.com/ajax/libs/angularjs/1.4.0/angular-cookies.js"></script>
    <script src="../jsLibs/angular-ui-router.min.js"></script>
    <script src="../jsLibs/angular-sanitize.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/angular.js/1.4.0/angular-animate.min.js"></script>
    <script src="~/jsLibs/imagesloaded.pkgd.min.js"></script>
    <script src="~/jsLibs/masonry.js"></script>
    <script src="../jsLibs/bootstrap/bootstrap.min.js"></script>
    <script src="~/jsLibs/bootstrap/ui-bootstrap-tpls-2.5.0.min.js"></script>
    <script src="~/jsLibs/jquery.touchSwipe.min.js"></script>

    <!-- google chart ng-js unminified for development -->
    <script src="~/jsLibs/charts/ng-google-chart-1-0-0-beta-1.js" type="text/javascript"></script>
    <script src="~/jsLibs/charts/rgbcolor.min.js" type="text/javascript"></script>
    <script src="~/jsLibs/charts/canvg.min.js" type="text/javascript"></script>
    <script src="~/jsLibs/charts/dom-to-image.min.js" type="text/javascript"></script>
    <script src="~/jsLibs/charts/download.js" type="text/javascript"></script>


    <script type="text/javascript" src="~/jsLibs/uiselect/select.js"></script>

    <!-- themes -->
    <link rel="stylesheet" href="~/jsLibs/uiselect/select.css" />
    <link rel="stylesheet" href="~/jsLibs/uiselect/select2.css">
    <link rel="stylesheet" href="~/jsLibs/uiselect/selectize.css">

    <!--  Scripts -->
    <script src="@Util.GetUrlWithVersion("scripts/app.js")"></script>
    <script src="@Util.GetUrlWithVersion("scripts/util.js")"></script>
    <script src="@Util.GetUrlWithVersion("scripts/service.js")"></script>
    <script src="@Util.GetUrlWithVersion("scripts/directive.js")"></script>
    <script src="@Util.GetUrlWithVersion("scripts/filters.js")"></script>
    <script src="@Util.GetUrlWithVersion("scripts/controller.js")"></script>
    <script src="~/jsLibs/angular-masonry.js"></script>

    <script type="text/javascript">
        function getUrlWithVersion(url) {
            return url + '?ver=@Util.Version';
        }
    </script>

    <!-- Global site tag (gtag.js) - Google Analytics -->
    <!--<script async src="https://www.googletagmanager.com/gtag/js?id=UA-111888033-1"></script>
    <script>
        window.dataLayer = window.dataLayer || [];
        function gtag(){dataLayer.push(arguments);}
        gtag('js', new Date());
        gtag('config', 'UA-111888033-1');
    </script>-->
    <!-- Google Tag Manager -->
    <script>
        (function (w, d, s, l, i) {
            w[l] = w[l] || []; w[l].push({
                'gtm.start':
                new Date().getTime(), event: 'gtm.js'
            }); var f = d.getElementsByTagName(s)[0],
            j = d.createElement(s), dl = l != 'dataLayer' ? '&l=' + l : ''; j.async = true; j.src =
            'https://www.googletagmanager.com/gtm.js?id=' + i + dl; f.parentNode.insertBefore(j, f);
        })(window, document, 'script', 'dataLayer', 'GTM-5QCTN34');
    </script>
    <!-- End Google Tag Manager -->

</head>
<body ng-controller="appController">
    <!-- Google Tag Manager (noscript) -->
    <!--<noscript>
        <iframe src="https://www.googletagmanager.com/ns.html?id=GTM-NG3FSZK"
                height="0" width="0" style="display:none;visibility:hidden"></iframe>
    </noscript>-->
    <!-- Google Tag Manager (noscript) -->
    <noscript>
        <iframe src="https://www.googletagmanager.com/ns.html?id=GTM-5QCTN34"
                height="0" width="0" style="display:none;visibility:hidden"></iframe>
    </noscript>
    <!-- End Google Tag Manager (noscript) -->
    <!-- End Google Tag Manager (noscript) -->
    <div class="app">
        <!--
          Main sidebar seen on the left. may be static or collapsing depending on selected state.

            * Collapsing - navigation automatically collapse when mouse leaves it and expand when enters.
            * Static - stays always open.
        -->
        <nav class="navigator navbar navbar-default navbar-fixed-top" data-ng-include="'partial/navbar.html'"> </nav>
        <div class="page-content">
            <nav id="sidebar" class="sidebar" role="navigation" data-ng-include="'partial/sidebar.html'"> </nav>
            <!-- This is the white navigation bar seen on the top. A bit enhanced BS navbar. See .page-controls in _base.scss. -->
            <div class="content-wrap" nav-collapse-toggler type="swipe">
                <!-- main page content. the place to put widgets in. usually consists of .row > .col-md-* > .widget.  -->
                <main role="main">
                    <nav role="navigation" class="filters" data-ng-include="'partial/filters.html'"> </nav>
                    <div id="content" class="content view-animate fade-up" data-ui-view></div>
                </main>
            </div>

        </div>
    </div>
</body>
</html>