@using I2E1_Message.Utils
<!DOCTYPE html>
<html>
<head>
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0, user-scalable=no" />

    <script src="../jsLibs/jquery.js"></script>
    <script src="../jsLibs/moment-2-13-0.js"></script>
    <script src="../jsLibs/angular.min.js"></script>
    <script src="../jsLibs/angular-ui-router.min.js"></script>
    <script src="../jsLibs/angular-sanitize.js"></script>

    <!---------bootstrap libs---------->
    <script src="../jsLibs/bootstrap/bootstrap.min.js"></script>
    <link rel="stylesheet" type="text/css" href="../jsLibs/bootstrap/bootstrap_paper.min.css" />

    <!--------------- Font Awsome -------------->
    <link rel="stylesheet" type="text/css" href="../jsLibs/font-awesome/css/font-awesome.min.css" />

    <! <!---------selectize libs---------->
    <link rel="stylesheet" type="text/css" href="../jsLibs/selectize/selectize.css" />
    <script src="../jsLibs/selectize/standalone/selectize.js"></script>
    <script src="../jsLibs/selectize/angular-selectize.js"></script>

    <!--------chart libs--------->
    <script src="../jsLibs/Chart.js"></script>

    <!--------date picker libs--------->
    <script src="../jsLibs/daterangepicker/daterangepicker.js"></script>
    <link rel="stylesheet" type="text/css" href="../jsLibs/daterangepicker/daterangepicker.css" />

    <!--------angular ui select-------->
    <script src="../jsLibs/uiselect/select.js"></script>
    <link rel="stylesheet" type="text/css" href="../jsLibs/uiselect/select.css" />
    <link rel="stylesheet" type="text/css" href="../jsLibs/uiselect/select2.css">

    <!-------- library for exporting chart data into csv files ------------------>
    <script src="../jsLibs/alasql.min.js"></script>
    
    <script src="@Util.GetUrlWithVersion("scripts/util.js")"></script>
    <script src="@Util.GetUrlWithVersion("scripts/app.js")"></script>
    <script src="@Util.GetUrlWithVersion("scripts/service.js")"></script>
    <script src="@Util.GetUrlWithVersion("scripts/directive.js")"></script>
    <script src="@Util.GetUrlWithVersion("scripts/controller.js")"></script>


    <link rel="shortcut icon" href="../images/favicon.ico" type="image/x-icon">

    <link rel="stylesheet" type="text/css" href="@Util.GetUrlWithVersion("styles/analytics.css")" />

    <title>I2E1 - Analytics</title>

    <script type="text/javascript">
        function getUrlWithVersion(url) {
            return url + '?ver=@Util.Version';
        }
    </script>
</head>
<body ng-app="i2e1Analytics">
    <div ui-view></div>
    <div id="loader" class="loader" style="display: none;">
        <div class="deactivate"></div>
        <div class="img-section">
            <div>
                <img src="~/images/wifi_loader_84X84.gif">
            </div>
        </div>
    </div>
</body>
</html>
