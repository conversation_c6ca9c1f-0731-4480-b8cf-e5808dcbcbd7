@using Newtonsoft.Json
@using i2e1_core.Utilities
@{
    var paymentDomain = CoreUtil.GetPaymentServerUrl();
}
<html>
<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title></title>
    <link href="https://fonts.googleapis.com/css?family=Lato" rel="stylesheet" />
    <link rel="stylesheet" href="~/listing/loader.css" />
    <style>
        body{
            font-family:Lato;
            margin:0px;
        }
        .header {
            color: #1da1f2;
            font-size:20px;
            text-align:center;
        }
        .img{
            height:200px;
            width:200px;
            object-fit:contain;
        }
        .sub-text {
            text-align: center;
            margin-left:50px;
            margin-right:50px;
        }
        .button {
            width: 100%;
            margin-left: 100px;
            margin-right: 100px;
            background: #1da1f2;
            color: #fff;
            padding-top: 5px;
            padding-bottom: 5px;
            margin-top: 30px;
            font-weight: 900;
            outline: none;
            border: none;
        }
        .countdown{
            display:flex;
            justify-content:center;
        }
        svg {
            position: relative;
            width: 120px;
            height: 120px;
            transform: rotateY(-180deg) rotateZ(-90deg);
        }

            svg .circle1 {
                stroke-linecap: round;
                stroke-width: 7px;
                stroke-border: none;
                border: none;
                stroke: #1da1f2;
                fill: none;
            }

            svg #circle {
                stroke-dasharray: 330px;
                stroke-dashoffset: 0px;
                stroke-linecap: round;
                stroke-width: 10px;
                stroke: #ebeff0;
                fill: none;
            }
    </style>
</head>
    <body>
        <div>
                <div class="col-center" id="demo" style="height:530px;">
                    <div class="row-center header-text header">Payment Under Process</div>
                    <div style="position:relative;margin-top:30px;height:100px;width:100%;">
                        <div class="lds-spinner">
                            <div></div>
                            <div></div>
                            <div></div>
                            <div></div>
                            <div></div>
                            <div></div>
                            <div></div>
                            <div></div>
                            <div></div>
                            <div></div>
                            <div></div>
                            <div></div>
                        </div>
                    </div>
                    <div class="row-center" style="text-align: center;font-weight: 600;font-size: 14px;line-height: 17px;">
                        Thank you for purchasing WIOM @Html.Raw(ViewBag.plan). Your payment is under process.
                    </div>

                </div>
            </div>
        <script type="text/javascript">
            var planType = @ViewBag.planType;
            var loaderCount = 30;
            var loaderMaxCount = 330;
            var totaltime = 120;
            var callApiTimer =2;
            function callAppFunction(methodName, methodData) {
                    var functionString = "Android." + methodName;
                    if (eval("typeof Android") != "undefined" && eval("typeof " + functionString) != "undefined") {
                        console.log("available");
                        var funcString = 'Android.' + methodName + '(';
                        for (var i = 0; i < methodData.length; ++i) {
                            if (typeof methodData[i] == 'string')
                                funcString += "'" + methodData[i] + "'";
                            else
                                funcString += methodData[i];

                            if (i != methodData.length - 1)
                                funcString += ",";
                        }
                        console.log(funcString);
                        eval(funcString + ")");
                        return true;
                    }

                console.log("notavailable");
                return false;
            }
            var result = {};
            var mainDiv = document.getElementById("demo");
            window.addEventListener("load", function (e) {
                getPaymentResult();
            })
            function checkPaymentStatus(paymentTime) {
                return new Promise((resolve, reject) => {
                    setTimeout(() => {
                    fetch("@paymentDomain/api/WiomApi/checkPaymentStatus?transactionId=@Html.Raw(ViewBag.transactionId)&planType=@ViewBag.planType&pg=@ViewBag.pg")
                            .then(res => res.json())
                            .then((result) => {
                                resolve(result.data);
                            })
                            .catch((err) => {
                                reject("failed");
                            })

                    }, paymentTime)
                });
            }
            function loaderProcess(startLoaderCount, endLoaderCount, maxTimer) {
                var loadTime = 100;
                var incremetLoader = (endLoaderCount - startLoaderCount) / (maxTimer / loadTime);
                var x = setInterval(() => {
                    startLoaderCount += incremetLoader;
                    if (startLoaderCount >= endLoaderCount) {
                        clearInterval(x);
                        document.getElementById("circle").style["stroke-dashoffset"] = endLoaderCount + "px";
                    }
                    else {
                        document.getElementById("circle").style["stroke-dashoffset"] = startLoaderCount + "px";
                    }
                }, loadTime);
            }
            async function getPaymentResult() {
                var i = 0;
                var no_of_loop = totaltime / callApiTimer;
                for (i = 0; i < no_of_loop; i++) {
                    try {
                        var response = await checkPaymentStatus(callApiTimer * 1000);
                        var status = response.status;
                        if (status != "pending") {
                            setResult(status);
                            break;
                        }
                    }
                    catch (err) {}
                }
                if (i == no_of_loop) {
                    setResult("failed");
                }
            }
            function setResult(result) {
                if (result =="success") {
                    gotoPlan(1)
                }
                else {
                    gotoPlan(2)
                }
            }

            function gotoPlan(status, linq = 0) {
                if (planType == 2) {
                    window.location.href = "http://localhost:4200/reseller?status=" + status + "&transactionId=@ViewBag.transactionId";
                }
                else {
                    window.location.href = "https://www.i2e1.com/wiompayment/plandetail?status=" + status + "&transactionId=@ViewBag.transactionId";
                }
            }
        </script>
    </body>
</html>