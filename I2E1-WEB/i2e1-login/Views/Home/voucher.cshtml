@using I2E1_Message.Models
@using I2E1_Message.Utils
@using I2E1_WEB.Utilities
<!DOCTYPE html>
<html>
<head>
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0, user-scalable=no" />
    <link rel="shortcut icon" href="images/favicon.ico" type="image/x-icon">
    <link rel="stylesheet" type="text/css" href="@Util.GetUrlWithVersion("styles/site.css")" />
    <script type="text/javascript" src="~/jsLibs/jquery.js"></script>
    <script type="text/javascript">
    </script>
    <title>i2e1</title>
    <script>
        function showOffer() {
            $('.showVoucher').show();
            $('.hideVoucher').hide();
        }
    </script>
    <style>
        .showVoucher{
            display:none;
        }
    </style>
</head>
<body>
    <div style="text-align: center;max-width:500px;width:100%;margin:0 auto;">
        <br>
        <div class="ad container">
            @foreach (var voucher in Model)
            {
                <div>
                    <div class="adds" ng-click="processAddClick($event)">
                        <div class="glyphicon glyphicon-remove-circle"></div>
                        <div class="text showVoucher">
                            @voucher.text
                        </div>
                        <div class="text hideVoucher">
                            loading...
                        </div>
                        <div class="img showVoucher">
                            <a ng-if="offer.image && offer.image_link" target="_blank" href="@voucher.image_link">
                            <img style="width:100%" src="@voucher.image" onload="showOffer()" />
                            </a>
                        </div>
                        @if (!string.IsNullOrEmpty(voucher.offerCode))
                        {
                            <hr ng-if="offer.offerCode" style="margin:10px 0 0 0;" />
                            <div ng-if="offer.offerCode" class="offerCode">
                                <span style="padding: 0 5px; background-color: yellow;">Code: @voucher.offerCode</span>
                            </div>
                        }
                    </div>
                    <hr />
                </div>
            }
        </div>
        <style>
            form.landing.container {
                padding: 0 20px;
            }

            .container {
                padding: 0;
            }

            .landing.container {
                position: relative;
            }

            .focus {
                position: absolute;
                top: 0;
                left: 0;
                width: 100%;
                z-index: 102;
            }

            .glyphicon-remove-circle {
                display: none;
            }

            .focus .glyphicon-remove-circle {
                display: block;
                position: absolute;
                right: -11px;
                top: -11px;
                font-size: 24px;
                background-color: white;
                border-radius: 100%;
                padding: 2px;
            }

            .offerCode {
                text-transform: uppercase;
                font-size: 20px;
            }

            .deactivate {
                display: none;
                position: fixed;
                width: 100%;
                height: 100%;
                background-color: rgba(255,255,255,0.8);
                top: 0;
                left: 0;
                z-index: 99;
            }

                .deactivate.show-up {
                    display: block;
                }

            .ad {
                overflow-y: auto;
            }

            .adds {
                background: white;
                border: 2px solid #9d9d9d;
                padding: 10px 10px;
                margin-bottom: 15px;
                box-shadow: 0 3px 5px #888888;
            }

            .text {
                font-weight: bold;
                text-transform: none;
                text-align: center;
            }
        </style>
    </div>
    <div id="footer" class="footer" style="clear:both;left:0;text-align:center;position:fixed;padding:0;height: 50px;">
        <hr style="margin-bottom: 7px;margin-top:0" />
        <table>
            <tr>
                <td width="33%" style="text-align: left;">
                    <span style="padding-left:10px;"><a target="_blank" href="http://www.i2e1.com">About i2e1</a></span>
                </td>
                <td width="33%">
                    <div>
                        <a target="_blank" href="http://www.i2e1.com/"><img height="22px" src="images/logo.png" /></a>
                    </div>
                </td>
                <td width="33%" style="text-align:right;">
                    <span style="padding-right:10px;" id="help"><a target="_blank" href="~/partial/help.html">HELP</a></span>
                </td>
            </tr>
        </table>
    </div>
</body>
</html>