@model wiom_login_share.Models.LoginResponse
@using I2E1_Message.Models
@using I2E1_Message.Utils
@using I2E1_WEB.Utilities
<!DOCTYPE html>
<html>
<head>
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0, user-scalable=no" />
    <link rel="shortcut icon" href="images/favicon.ico" type="image/x-icon">
    <link rel="stylesheet" type="text/css" href="@Util.GetUrlWithVersion("styles/site.css")" />
    <script type="text/javascript" src="~/jsLibs/jquery.js"></script>
    <script type="text/javascript">
    </script>
    <title>i2e1</title>
    <script>
        var loginResponse = @Html.Raw(Newtonsoft.Json.JsonConvert.SerializeObject(Model));
        var makeLogin = function () {
            var form = $('<form style="display:none">').attr('action', loginResponse.url);
            var url = loginResponse.url + "?";
            for (var i = 0; i < loginResponse.parameters.length; ++i) {
                var input = $('<input>').attr('name', loginResponse.parameters[i].name).attr('value', loginResponse.parameters[i].value);
                form.append(input);
                url += loginResponse.parameters[i].name + "=" + encodeURIComponent(loginResponse.parameters[i].value) + "&";
            }
            var input = $('<input>').attr('type', 'submit');
            form.append(input);
            if (loginResponse.isGet) {
                window.location.href = url;
            }
            else {
                form.attr('method', 'post');
                $('body').append(form);
                form.submit();
            }
        }
        $(document).ready(function () {
            makeLogin();
        });
    </script>
</head>
<body>
    <div style="text-align: center;margin-top: 100px;">
        <h4>Please wait while we are logging you in.</h4>
        <img src="~/images/wifi_loader_84X84.gif" />
        <div class="followUs" style="padding-top:10px;">
        <span style="font-size:16px;">Get Help - </span>
        <a target="_blank" href="tel:+918880322222">8880322222</a></div>
    </div>
        <div id="footer" class="footer" style="clear:both;left:0;text-align:center;position:fixed;padding:0;height: 50px;">
            <hr style="margin-bottom: 7px;margin-top:0" />
            <table>
                <tr>
                    <td width="33%" style="text-align: left;">
                        <span style="padding-left:10px;"><a target="_blank" href="http://www.i2e1.com">About i2e1</a></span>
                    </td>
                    <td width="33%">
                        <div>
                            <a target="_blank" href="http://www.i2e1.com/"><img height="22px" src="images/logo.png" /></a>
                        </div>
                    </td>
                    <td width="33%" style="text-align:right;">
                        <span style="padding-right:10px;" id="help"><a target="_blank" href="~/partial/help.html">HELP</a></span>
                    </td>
                </tr>
            </table>          
        </div>  
</body>
</html>