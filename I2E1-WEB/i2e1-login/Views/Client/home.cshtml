@using I2E1_Message.Utils

<!DOCTYPE html>
<html>
<head>
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0, user-scalable=no" />
    <link href="https://fonts.googleapis.com/css?family=Roboto" rel="stylesheet" type="text/css">
    <link rel="shortcut icon" href="/images/favicon.ico" type="image/x-icon">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/4.7.0/css/font-awesome.min.css">
    <script src="https://apis.google.com/js/api:client.js"></script>
    <link rel="stylesheet" type="text/css" href="/jsLibs/bootstrap/bootstrap_paper.min.css" />
    <!--<link rel="stylesheet" type="text/css" href="https://stackpath.bootstrapcdn.com/bootstrap/3.3.7/css/bootstrap.min.css" />-->
    <!--<link rel="stylesheet" type="text/css" href="/Client/styles/material.css" />-->
    <!--
        <link rel="stylesheet" type="text/css" href="/Client/styles/select.css" />
    -->

    <!-- Below is added for ui-select -->
    <link rel="stylesheet" type="text/css" href="~/jsLibs/uiselect/select.css" />
    <link rel="stylesheet" type="text/css" href="~/jsLibs/uiselect/select2.css">
    <link rel="stylesheet" type="text/css" href="/Client/styles/selectize.css">
    <link rel="stylesheet" href="~/jsLibs/uiselect/selectize.css">


    <!-- Common script files -->
    <script src="../jsLibs/jquery.js"></script>
    <script src="../jsLibs/fileUploader.js"></script>
    <script src="../jsLibs/angular143.min.js"></script>
    <script src="../jsLibs/angular-ui-router.min.js"></script>
    <script src="../jsLibs/angular-sanitize.js"></script>
    <script src="../jsLibs/selectize/standalone/selectize.js"></script>
    <script src="../jsLibs/selectize/angular-selectize.js"></script>
    <script src="../jsLibs/bootstrap/ui-bootstrap-tpls-0.13.0.min.js"></script>
    <script src="../jsLibs/bootstrap/bootstrap.min.js"></script>
    <script src="~/jsLibs/uiselect/select.js"></script>
    <!--<script type="text/javascript" src="https://cdnjs.cloudflare.com/ajax/libs/angular-ui-select/0.20.0/select.min.js"></script>-->
    <script src="../jsLibs/jquery.stickytableheaders.min.js"></script>
    <script src="../jsLibs/masonry.js"></script>
    <script src="../jsLibs/draganddrop.js"></script>

    <script src="../jsLibs/imagesloaded.pkgd.min.js"></script>
    <script src="../jsLibs/lodash.min.js"></script>

    <!-- *********   Added for slider ************ -->
    <link rel="stylesheet" type="text/css" href="../jsLibs/rzslider/rzslider.css" />
    <script src="../jsLibs/rzslider/rzslider.js" type="text/javascript"></script>


    <!--------date picker libs--------->
    <script src="/jsLibs/moment-2-13-0.js"></script>
    <script src="/jsLibs/daterangepicker/daterangepicker.min.js"></script>
    <script src="/jsLibs/daterangepicker/angular-daterangepicker.min.js"></script>
    <link rel="stylesheet" type="text/css" href="/jsLibs/daterangepicker/daterangepicker.min.css" />

    <!-- Google Maps JavaScript API -->
    <script type="text/javascript" src="https://maps.googleapis.com/maps/api/js?key=AIzaSyAkOXB0-P4U1Mayig0PhSwJHLz1O6KklAw&libraries=places"></script>


    <link rel="stylesheet" type="text/css" href="@Util.GetUrlWithVersion("styles/client.css")" />

    <!------ Clipboar Library -->

    <script src="/jsLibs/clipboard.min.js"></script>

    <script src="@Util.GetUrlWithVersion("scripts/ng-infinite-scroll.js")"></script>
    <script src="@Util.GetUrlWithVersion("scripts/vs-google-autocomplete.js")"></script>
    <script src="@Util.GetUrlWithVersion("scripts/app.js")"></script>
    <script src="@Util.GetUrlWithVersion("scripts/service.js")"></script>
    <script src="@Util.GetUrlWithVersion("scripts/directive.js")"></script>
    <script src="scripts/ng-analytics.min.js"></script>
    <script src="@Util.GetUrlWithVersion("scripts/fileUploadService.js")"></script>
    <script src="@Util.GetUrlWithVersion("scripts/i2e1-categories.js")"></script>
    <script src="@Util.GetUrlWithVersion("scripts/utils.js")"></script>
    <script src="@Util.GetUrlWithVersion("scripts/controller.js")"></script>

    <!--Load the AJAX API-->
    <script type="text/javascript" src="scripts/jsapi_20150830.js"></script>
    <script type="text/javascript">

        // Load the Visualization API and the piechart package.
        google.load('visualization', '1.0', { 'packages': ['corechart'] });

        // Set a callback to run when the Google Visualization API is loaded.
        google.setOnLoadCallback(drawChart);

        function getUrlWithVersion(url) {
            return url + '?ver=@Util.Version';
        }
    </script>

    <title>i2e1 | Manage your Wifi</title>
</head>
<body ng-app="i2e1Admin">
    <div ui-view></div>
    <div id="loader" class="loader" style="display: none;">
        <div class="deactivate"></div>
        <div class="img-section">
            <div>
                <img src="~/images/wifi_loader_84X84.gif">
            </div>
        </div>
    </div>
</body>
<script type="text/ng-template" id="/confirmationDialog.html">
    <div class="modal-header bootstrap-dialog-header {{data.type ? data.type : ''}}">
        <button class="close" ng-click="cancel()">×</button>
        <h4>{{data.title ? data.title : 'Title'}}</h4>
    </div>
    <div class="modal-body">
        <div class="bootstrap-dialog-body">
            <div class="bootstrap-dialog-message">{{data.msg}}</div>
        </div>
    </div>
    <div class="modal-footer">
        <div class="bootstrap-dialog-footer">
            <button ng-if="data.ok" ng-click="ok()" class="btn btn-primary">{{data.ok ? data.ok : 'Yes'}}</button>
            <button ng-if="data.cancel" ng-click="cancel()" class="btn btn-warning">{{data.cancel ? data.cancel : 'Cancel'}}</button>
        </div>
    </div>
</script>
</html>