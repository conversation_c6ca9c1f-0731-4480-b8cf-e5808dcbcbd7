@using I2E1_Message.Utils
<!DOCTYPE html>
<html>
<head>
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0, user-scalable=no" />
    <meta name="theme-color" content="#ffffff" />
    <script src="https://apis.google.com/js/api:client.js"></script>
    <script src="../jsLibs/jquery.js"></script>
    <script src="../jsLibs/angular143.min.js"></script>
    <script src="../jsLibs/angular-ui-router.min.js"></script>
    <script src="../jsLibs/bootstrap/ui-bootstrap-tpls-0.13.0.min.js"></script>
    <script src="../jsLibs/bootstrap/bootstrap.min.js"></script>

    <link href="https://fonts.googleapis.com/css?family=Roboto" rel="stylesheet" type="text/css">
    <link rel="shortcut icon" href="/images/favicon.ico" type="image/x-icon">
    <link rel="stylesheet" type="text/css" href="/jsLibs/bootstrap/bootstrap_paper.min.css" />

    <link rel="stylesheet" type="text/css" href="@Util.GetUrlWithVersion("/Client/styles/loginApp.min.css")" />
    <script src="@Util.GetUrlWithVersion("/Client/scripts/loginApp.js")"></script>
    <script type="text/javascript">
    function getUrlWithVersion(url) {
        return url + '?ver=@Util.Version';
    }
    </script>

    <title>i2e1 | Manage your Wifi</title>
</head>

<body ng-app="loginApp">
    @if (ViewBag.ErrorMessage != null)
    {
        @:
        <div class="message popup">
            <span class="error appear">@ViewBag.ErrorMessage</span>
        </div>
    }
    else
    {
            @:
        <div class="message">
            <span class="success"></span>
            <span class="error"></span>
            <span class="info"></span>
        </div>
    }
    
    <div ui-view></div>
</body>
</html>

