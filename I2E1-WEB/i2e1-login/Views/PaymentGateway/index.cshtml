@using I2E1_WEB.Models
@model List<AdminUser>
<!DOCTYPE html>
<html>
<head>
    <meta name="viewport" content="width=device-width" />
    <title>Payment Gateway</title>
    <link rel="stylesheet" href="~/styles/pure.css">
    <style>
        .alert {
            padding: 20px;
            background-color: rgba(244, 74, 54, 0.58);
            color: white;
        }

        .closebtn {
            margin-left: 15px;
            color: white;
            font-weight: bold;
            float: right;
            font-size: 22px;
            line-height: 20px;
            cursor: pointer;
            transition: 0.3s;
        }

            .closebtn:hover {
                color: black;
            }

        table {
            table-layout: fixed;
        }

            table td {
                width: 30%;
                padding: 0 3px;
                word-wrap: break-word;
            }

                table td span {
                    border: 1px solid #666;
                    display: inline-block;
                    padding: 3px;
                    border-radius: 3px;
                    margin: 2px;
                }
    </style>
<script>
    window.onload = function () {
        var d = new Date().getTime();
        document.getElementById("tid").value = d;
    };
</script>
</head>
<body>
    <form method="post" name="customerData" action="/PaymentGateway/MakePayment" >
    <table width="40%" height="100" border='1' align="center"><caption><font size="4" color="blue"><b>Integration Kit</b></font></caption></table>
    <table width="40%" height="100" border='1' align="center">
        <tr>
            <td>Parameter Name:</td>
            <td>Parameter Value:</td>
        </tr>
        <tr>
            <td colspan="2"> Compulsory information</td>
        </tr>
        <tr>
            <td>TID	:</td>
            <td><input type="text" name="tid" id="tid" readonly /></td>
        </tr>
        <tr>
            <td>Merchant Id</td>
            <td><input type="text" name="merchant_id" id="merchant_id" value="104980" /></td>
        </tr>
        <tr>
            <td>Order Id</td>
            <td><input type="text" name="order_id" value="123654789" /></td>
        </tr>
        <tr>
            <td>Amount</td>
            <td><input type="text" name="amount" value="1.00" /></td>
        </tr>
        <tr>
            <td>Currency</td>
            <td><input type="text" name="currency" value="INR" /></td>
        </tr>
        <tr>
            <td>Redirect URL</td>
            <td><input type="text" name="redirect_url" value="http://facebook.com" /></td>
        </tr>
        <tr>
            <td>Cancel URLbbcvcbvcc</td>
            <td><input type="text" name="cancel_url" value="http://google.com" /></td>
        </tr>
        <tr>
            <td></td>
            <td><input type="submit" value="Checkout" /></td>
        </tr>
    </table>
    </form>
</body>
</html>
