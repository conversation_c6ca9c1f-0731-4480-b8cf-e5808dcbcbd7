@using I2E1_Message.Utils
<!DOCTYPE html>
<html>
<head>
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0, user-scalable=no" />
    <link href="https://fonts.googleapis.com/css?family=Roboto" rel="stylesheet" type="text/css">
    <link rel="shortcut icon" href="/images/favicon.ico" type="image/x-icon">
    <script src="https://apis.google.com/js/api:client.js"></script>
    <link rel="stylesheet" type="text/css" href="/jsLibs/bootstrap/bootstrap_paper.min.css" />
    <link rel="stylesheet" type="text/css" href="/AdminPortal/styles/material.css" />
    <link rel="stylesheet" type="text/css" href="/AdminPortal/styles/select.css" />
    <link rel="stylesheet" type="text/css" href="@Util.GetUrlWithVersion("/AdminPortal/styles/site.css")" />
    <link rel="stylesheet" type="text/css" href="/AdminPortal/styles/select2.css">
    <link rel="stylesheet" type="text/css" href="/AdminPortal/styles/selectize.css">
    <title>i2e1</title>
</head>
<body>
    @if (ViewBag.ErrorMessage != null)
    {
        <div style="text-align: center;position: absolute;width: 100%;top: 2px;z-index: 10000;">
            <span style="color: #fff;padding: 4px 8px;" class="failure">@ViewBag.ErrorMessage</span>
        </div>
    }
    <div style="text-align:center;margin-top:20px;">
        <img style="margin: 18px 10px;width: 90px" src="../images/logo.png" />
        <span style="font-size: 20px;position: relative;top: 20px;"> Device Management Portal</span>
        <h6>Login to continue</h6>
    </div>
    ﻿<form id="loginForm" method="post" action="/Admin/LoginWeb" class="col-lg-4 col-lg-offset-4 col-md-4 col-md-offset-4 col-sm-10 col-sm-offset-1 col-xs-10 col-xs-offset-1">
        <script>
            var googleUser = {};
            var startApp = function () {
                gapi.load('auth2', function () {
                    // Retrieve the singleton for the GoogleAuth library and set up the client.
                    auth2 = gapi.auth2.init({
                        client_id: '303939939579-a02pc4b6m9hno2a2qggc2kjrdi5c3rko.apps.googleusercontent.com',
                        cookiepolicy: 'single_host_origin',
                        // Request scopes in addition to 'profile' and 'email'
                        //scope: 'additional_scope'
                    });
                    attachSignin(document.getElementById('customBtn'));
                });
            };

            function attachSignin(element) {
                console.log(element.id);
                auth2.attachClickHandler(element, {},
                    function (googleUser) {
                        onSignIn(googleUser);
                    }, function (error) {
                        alert(JSON.stringify(error, undefined, 2));
                    });
            }
            function onSignIn(googleUser) {
                document.getElementById('token').value = googleUser.getAuthResponse().id_token;
                document.getElementById('authType').value = 'GMAIL';
                document.getElementById('loginForm').submit.click();
            };
        </script>
        <style type="text/css">
            #customBtn {
                display: inline-block;
                background-color: #dc483b;
                color: #444;
                border-radius: 5px;
                color: white;
            }

            #customBtn:hover {
                cursor: pointer;
            }

            span.label {
                font-family: serif;
                font-weight: normal;
            }

            span.icon {
                background: url('/images/g-plus.png') transparent 5px 50% no-repeat;
                display: inline-block;
                vertical-align: middle;
                width: 45px;
                height: 34px;
                border-right: 1px solid #fff;
                background-size: 25px;
            }

            span.buttonText {
                display: inline-block;
                vertical-align: middle;
                padding-left: 25px;
                padding-right: 25px;
                font-size: 14px;
                font-weight: bold;
                font-family: 'Roboto', sans-serif;
            }

            .admin-sign-up {
                text-align: center;
                vertical-align: text-top;
            }

            .admin-sign-up a:hover {
                text-decoration: none;
            }

        </style>
        <div id="gSignInWrapper" style="text-align: center;">
            <div id="customBtn" class="customGPlusSignIn">
                <span class="icon"></span>
                <span class="buttonText">Sign in Using Google</span>
            </div>
        </div>
        <div id="name"></div>
        <script>startApp();</script>

        <div class="entry-seperator">
            <span>OR</span>
            <hr>
        </div>
        <div>
            <input type="hidden" name="reply" value="@Context.Request.Query["reply"]" />
            <input type="hidden" name="token" id="token" />
            <input type="hidden" name="authType" id="authType" />
            <input placeholder="Username" name="username" class="form-control" />
            <input placeholder="Password" name="password" type="password" class="form-control" />
            @if (ViewBag.CaptchaUrl != null)
            {
                <div style="padding-top:15px;">
                    There has been too many retries. Please validate you are not bot.
                    <img src="@ViewBag.CaptchaUrl" />
                    <input placeholder="Enter Captcha" name="captcha" class="form-control" />
                </div>
            }
            <button id="submit" type="submit" class="btn btn-primary pull-right blue">Log In</button>
        </div>
        <!--
            
        -->
         <div class="entry-seperator" style="clear:both">
             <span>OR</span>
             <hr>
         </div>
         <div class="admin-sign-up">
             <a href="/AdminPortal/SignUp">Sign Up</a>
         </div>
        
    </form>
    <div id="loader" style="display: none;">
        <div style="position: fixed; width: 100%; height: 100%;background-color: white; top: 0;left: 0; opacity: 0.6;"></div>
        <div style="position: fixed; left: 50%;top: 32%;z-index: 100;">
            <div style="position: relative; left: -50%;">
                <img src="../images/Loader.gif" width="100px">
            </div>
        </div>
    </div>
    <div class="navbar-fixed-bottom text-right" style="color:#666666;font-weight:500;padding:5px">
    </div>
</body>
</html>