@using I2E1_Message.Utils
<!DOCTYPE html>
<html>
<head>
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0, user-scalable=no" />
    <link href="https://fonts.googleapis.com/css?family=Roboto" rel="stylesheet" type="text/css">
    <link rel="shortcut icon" href="/images/favicon.ico" type="image/x-icon">
    <link rel="stylesheet" type="text/css" href="/jsLibs/bootstrap/bootstrap_paper.min.css" />
    <link rel="stylesheet" type="text/css" href="/AdminPortal/styles/material.css" />
    <link rel="stylesheet" type="text/css" href="/AdminPortal/styles/select.css" />
    <link rel="stylesheet" type="text/css" href="@Util.GetUrlWithVersion("/AdminPortal/styles/site.css")" />
    <link rel="stylesheet" type="text/css" href="/AdminPortal/styles/select2.css">
    <link rel="stylesheet" type="text/css" href="/AdminPortal/styles/selectize.css">
    <title>I2e1 - Sign Up</title> 
    <style>
        form input.error {
            background: rgba(255, 0, 0, 0.13);
        }

    </style>
    <script type="text/javascript">
        var signUp = function () {
            var err = false;
            var signUpForm = document.getElementById('signUpForm');
            var fields = signUpForm.getElementsByTagName('input');
            var password = document.getElementById('password').value;
            var confirm_password = document.getElementById('confirm_password').value;

            for (var i = 0; i < 5; i++) {
                if (!fields[i].value) {
                    err = true;
                    addErrorClass(fields[i]);
                }
            }

            if (!validateEmail(document.getElementById('emailId').value)) {
                showError("Not a valid e-mail address");
                addErrorClass(document.getElementById('emailId'));
                err = true;
            }

            if (document.getElementById('contact_no').value.length != 10) {
                showError("Please enter 10 digit phone number");
                addErrorClass(document.getElementById('contact_no'));
                err = true;
            }

            if (password.length < 8) {
                showError("Password must be greater than 8 characters");
                addErrorClass(document.getElementById('password'));
                err = true;
            }
            
            event.preventDefault();
            if (err) {
                return;
            }

            if (password === confirm_password) {
                signUpForm.submit();
            } else {
                showError("Password and confirm password do not match");
            }
        };

        var showError = function (errMsg) {
            document.getElementById("errorMessage").getElementsByTagName("span")[0].innerText = errMsg;
            document.getElementById("errorMessage").style.display = 'block';
        };

        var addErrorClass = function (field) {
            field.className += ' error';
        };

        var validateEmail = function (email) {
            var re = /^[_a-z0-9-]+(\.[_a-z0-9-]+)*@@[a-z0-9-]+(\.[a-z0-9-]+)*(\.[a-z]{2,4})$/;
            return re.test(email);
        }

    </script>
</head>
<body>
    @if (ViewBag.ErrorMessage != null)
    {
        <div style="text-align: center;position: absolute;width: 100%;top: 2px;z-index: 10000;">
            <span style="color: #fff;padding: 4px 8px;" class="failure">@ViewBag.ErrorMessage</span>
        </div>
    }
    @if (ViewBag.SuccessMessage != null)
    {
        <script type="text/javascript">
            setTimeout(function () { window.location.href = '/AdminPortal/Index' }, 5000);
        </script>
        <div style="text-align: center;position: absolute;width: 100%;top: 2px;z-index: 10000;">
            <span style="color: #fff;padding: 4px 8px;" class="success">@ViewBag.SuccessMessage</span>
        </div>
    }
    
    <div id="errorMessage" style="text-align: center;position: absolute;width: 100%;top: 2px;z-index: 10000;display: none;">
        <span style="color: #fff;padding: 4px 8px;" class="failure"></span>
    </div>
    
    <div style="text-align:center;margin-top:20px;">
        <img style="margin: 18px 10px;width: 90px" src="../images/logo.png" />
        <span style="font-size: 20px;position: relative;top: 20px;"> Device Management Portal</span>
        <h6>Create new account</h6>
    </div>
    ﻿<form id="signUpForm" method="post" action="/Admin/AdminSignUp" class="col-lg-4 col-lg-offset-4 col-md-4 col-md-offset-4 col-sm-10 col-sm-offset-1 col-xs-10 col-xs-offset-1">
        <input id="emailId" type="email" placeholder="Email Id" name="emailId" class="form-control" />
        <input id="name" placeholder="Name" name="name" class="form-control" />
        <input id="contact_no" placeholder="Phone number" name="contact_no" class="form-control" />
        <input id="password" placeholder="Password" name="password" class="form-control" type="password" />
        <input id="confirm_password" placeholder="Confirm password" name="confirm_password" class="form-control" type="password" />
        
        <button id="_submit" onclick="signUp()" type="submit" class="btn btn-primary pull-right blue">Submit</button>
    </form>
    <script type="text/javascript">
        var fields;
        (function () {
            fields = document.getElementById('signUpForm').getElementsByTagName('input');
            for (var i = 0; i < 5; i++) {
                fields[i].onkeypress = function () {
                    this.className = "form-control";
                };
            }
        })();
    </script>

</body>
</html>
