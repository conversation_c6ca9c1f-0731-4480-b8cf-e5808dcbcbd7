@using I2E1_Message.Utils
<!DOCTYPE html>
<html>
<head>
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0, user-scalable=no" />
    <script src="https://ajax.googleapis.com/ajax/libs/jquery/1.12.0/jquery.min.js"></script>
    <script src="https://apis.google.com/js/api:client.js"></script>
    <script src="https://i2e1storage.blob.core.windows.net/cdn/countrycode.js"></script>
    <link rel="stylesheet" type="text/css" href="../jsLibs/bootstrap/bootstrap_paper.min.css" />
    <link rel="stylesheet" type="text/css" href="styles/material.css" />
    <link rel="stylesheet" type="text/css" href="styles/select.css" />
    <link rel="stylesheet" type="text/css" href="@Util.GetUrlWithVersion("styles/site.css")" />
    <link rel="stylesheet" href="styles/select2.css">
    <link rel="stylesheet" href="styles/selectize.css">
    <script src="../jsLibs/jquery.js"></script>
    <script src="../jsLibs/fileUploader.js"></script>
    <script src="../jsLibs/angular143.min.js"></script>
    <script src="../jsLibs/angular-ui-router.min.js"></script>
    <script src="../jsLibs/angular-sanitize.js"></script>
    <script src="../jsLibs/checklist-model.js"></script>
    <script src="../jsLibs/bootstrap/ui-bootstrap-tpls-0.13.0.min.js"></script>
    <script src="../jsLibs/bootstrap/bootstrap.min.js"></script>
    <script src="scripts/select.js"></script>
    <script src="scripts/countries.js"></script>
    <!--------date picker libs--------->
    <script src="../jsLibs/moment-2-13-0.js"></script>
    <script src="../jsLibs/daterangepicker/daterangepicker.js"></script>
    <link rel="stylesheet" type="text/css" href="../jsLibs/daterangepicker/daterangepicker.css" />

    <script src="@Util.GetUrlWithVersion("scripts/app.js")"></script>
    <script src="@Util.GetUrlWithVersion("scripts/service.js")"></script>
    <script src="@Util.GetUrlWithVersion("scripts/directive.js")"></script>
    <script src="scripts/ng-analytics.min.js"></script>
    <script src="scripts/fileUploadService.js"></script>
    <script src="@Util.GetUrlWithVersion("scripts/controller.js")"></script><link rel="shortcut icon" href="../images/favicon.ico" type="image/x-icon">
    <title>i2e1</title>
    <!--Load the AJAX API-->
    <script type="text/javascript" src="scripts/jsapi_20150830.js"></script>
    <script type="text/javascript">

        // Load the Visualization API and the piechart package.
        google.load('visualization', '1.0', { 'packages': ['corechart'] });

        // Set a callback to run when the Google Visualization API is loaded.
        google.setOnLoadCallback(drawChart);

        function getUrlWithVersion(url) {
            return url + '?ver=@Util.Version';
        }
    </script>
   
   
</head>
<body ng-app="i2e1Admin">
    <div ng-show="message" style="text-align: center;position: absolute;width: 100%;top: 2px;z-index: 10000;">
        <span style="color: #fff;padding: 4px 8px;" ng-class="{'failure':messageType==='failure', 'success':messageType === 'success', 'info':messageType === 'info'}">{{message}}</span>
    </div>
    <div ui-view></div>
    <div id="loader" style="display: none;">
        <div style="position: fixed; width: 100%; height: 100%;background-color: white; top: 0;left: 0; opacity: 0.6;"></div>
        <div style="position: fixed; left: 50%;top: 32%;z-index: 100;">
            <div style="position: relative; left: -50%;">
                <img src="../images/Loader.gif" width="100px">
            </div>
        </div>
    </div>
    <div class="navbar-fixed-bottom text-right" style="color:#666666;font-weight:500;padding:5px">
</div>
<script type="text/ng-template" id="/nasidControl.html">
    <div class="col-md-10 col-md-offset-1 col-xs-12">
        <div id="nas-dropdown" class="input-group">
            <span class="input-group-addon">Location(s)</span>
            <ui-select style="margin-bottom: -7px;" ng-model="router.selected" theme="selectize" ng-change="fetchDetails()">
                <ui-select-match placeholder="Select or search in the list">
                    {{$select.selected.storeName}}, {{$select.selected.city}} -
                    {{$select.selected.state}} ({{$select.selected.nasid}})
                </ui-select-match>
                <ui-select-choices repeat="router in routerDetails | propsFilter: {nasid: $select.search, storeName: $select.search}">
                    <span ng-bind-html="router.storeName | highlight: $select.search"></span>
                    <span ng-bind-html="router.city | highlight: $select.search"></span>
                    <span ng-bind-html="router.state | highlight: $select.search"></span>
                    <span ng-bind-html="router.nasid | highlight: $select.search"></span>
                </ui-select-choices>
            </ui-select>
        </div>
    </div>
</script>
<script type="text/ng-template" id="/nasidControlMultiple.html">
    <div class="col-md-10 col-md-offset-1 col-xs-12">
        <div id="nas-dropdown" class="input-group">
            <span class="input-group-addon">Location(s)</span>
            <ui-select multiple limit="10" ng-model="router.selected" theme="select2" close-on-select="false" ng-change="fetchDetails()">
                <ui-select-match placeholder="Select or search in the list">
                    {{$item.storeName}}, {{$item.city}} - {{$item.state}} ({{$item.nasid}})
                </ui-select-match>
                <ui-select-choices repeat="router in routerDetails | propsFilter: {nasid: $select.search, storeName: $select.search}">
                    <span ng-bind-html="router.storeName | highlight: $select.search"></span>
                    <span ng-bind-html="router.city | highlight: $select.search"></span>
                    <span ng-bind-html="router.state | highlight: $select.search"></span>
                    <span ng-bind-html="router.nasid | highlight: $select.search"></span>
                </ui-select-choices>
            </ui-select>
        </div>
    </div>
</script>
<script type="text/ng-template" id="/confirmationDialog.html">
    <div style="padding: 30px 20px">
        <button class="btn" style="float:right;background: transparent;" ng-click="cancel()"><b>X</b></button>
        <div>
            <div>
                <img width="75px" src="../images/logo.png" />
                <p style="color:#0c4da2;padding:10px 0;">Information to Everyone</p>
            </div>
        </div>
        <div>
            <p>{{data.msg}}</p>
        </div>
        <div style="text-align:right;">
            <button ng-click="ok()" class="btn-primary blue">Yes</button>
            <button ng-click="cancel()" class="btn-warning">Cancel</button>
        </div>
    </div>
</script>
</body>
</html>
