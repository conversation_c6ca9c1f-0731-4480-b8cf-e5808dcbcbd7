var version = '1234';
var cacheName = 'weatherPWA-step-6-1';
var dataCacheName = 'weatherData-v1';
var filesToCache = [
'/discover/',
'/pwa/scripts/app.js',
'/pwa/styles/inline.css'
];

self.addEventListener('install', function(e) {
  console.log('[ServiceWorker] Install');
  caches.delete(cacheName);
  caches.delete(dataCacheName);
  e.waitUntil(
    caches.open(cacheName).then(function(cache) {
      console.log('[ServiceWorker] Caching app shell');
      return cache.addAll(filesToCache);
    })
  );
});

self.addEventListener('activate', function(e) {
    console.log('[ServiceWorker] Activate');
  return self.clients.claim();
});

self.addEventListener('fetch', function (e) {
    console.log('[Service Worker] Fetching', e.request.url);
    if (e.request.url.indexOf('random=') > 0 || e.request.url.indexOf('googleapis.com=') > 0) {
        console.log(e.request.url);
        e.respondWith(fetch(e.request));
    }
    else{
        e.respondWith(
      caches.match(e.request).then(function (response) {
          if (response)
              console.log("Fetched", response);
          return response || fetch(e.request).then(function (response) {
              return caches.open(dataCacheName).then(function (cache) {
                  cache.put(e.request.url, response.clone());
                  return response;
              });
          });
      })
    );
    }

});