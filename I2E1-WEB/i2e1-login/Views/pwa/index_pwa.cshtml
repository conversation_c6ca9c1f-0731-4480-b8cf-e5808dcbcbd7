@using I2E1_Message.Utils
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="theme-color" content="#ffffff" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <link rel="icon" href="~/pwa/images/icons/icon-32x32.png"/>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Wofr</title>
    <link rel="stylesheet" type="text/css" href="@Util.GetUrlWithVersion("/pwa/styles/inline.css")">
    <link rel="stylesheet" type="text/css" href="/jsLibs/font-awesome/css/font-awesome.min.css">
    <link rel="manifest" href="@Util.GetUrlWithVersion("/pwa/manifest.json")">
    <meta name="mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black">
    <meta name="apple-mobile-web-app-title" content="Wofr">
    <link rel="apple-touch-icon" href="/pwa/images/icons/icon-144x144.png">
    <meta name="msapplication-TileImage" content="/pwa/images/icons/icon-144x144.png">
    <meta name="application-name" content="Wofr">
</head>
<body>
    <main class="welcome-screen">
        <div class="layer" style="background-color: rgba(162, 164, 208, 0.69);z-index:-1;"></div>
        <div style="margin: 0 auto;max-width: 360px;padding-left:10px;">
            <p style="font-size: 16px;margin: 70px 0 0 0;">Welcome to</p>
            <span class="market-place-name" style="font-size:30px;"></span>
            <br/>
            <img style="margin-top: 80px;width: 180px;position: relative;top: 15px;left: -7px;" src="/pwa/images/icons/icon76.png" />
            <p style="font-size:16px;">#Happening places</p><p style="font-size:16px;">#Awesome offers</p>
            <p style="font-size:16px;">#Free wifi</p>
        </div>
        <div style="position: absolute;bottom: 60px;width: 100%; text-align: center;">
            <p style="margin-bottom: 40px;"><img style="width:120px;" src="/pwa/images/Loader.gif" /></p>
        </div>

    </main>
    <header class="header">
        <button id="butBack" class="headerButton" hidden></button>
        <h1 class="header__title"><img style="width:30px;" src="/pwa/images/<EMAIL>"/>
        <span class="market-place-name">Market Place</span>
        <div class="total-data-div" hidden>Enjoy <span class="total-data"></span> of data</div>
        </h1>
        <div style="position: absolute;right: 0;">
            <button id="butAddToHome" class="headerButton" aria-label="Add" hidden></button>
            <button id="butRefresh" class="headerButton" aria-label="Refresh"></button>
            <button id="butProfile" class="headerButton" aria-label="Profile"></button>
        </div>
    </header>
    <div class="midHeader" hidden>
        <a>All</a><a>Cafe</a><a>Bar</a><a>Dining Out</a>
    </div>
    <main class="main list-container list-page">
        <div>
            
        </div>
        <div class="card cardTemplate" hidden>
            <div class="layer"></div>
            <div class="info-div">
                <div class="shop-name overflow" style="line-height: 30px;"></div>
                <div class="offer-code" hidden></div>
                <div class="wifi-status" hidden></div>
                <div><a class="contact-number"></a></div>
            </div>
            <div class="card-last-updated" hidden></div>
            <div class="nasid" hidden></div>
            <div class="campaign-id" hidden></div>
            <a class="redeem-btn" hidden>Redeem</a>
        </div>
    </main>
    <main class="main card-container details-page" hidden>
        <div class="template">
            <div style="margin:16px;">
                <div class="card-image" style="width:100%;height:200px;position:relative;color:#fff">
                    <div class="layer"></div>
                    <div class="total-reviews" hidden>
                        <span class="fa fa-star"></span>
                        <span class="fa fa-star"></span>
                        <span class="fa fa-star"></span>
                        <span class="fa fa-star"></span>
                        <span class="fa fa-star"></span>
                    </div>
                    <div class="info-div" style="margin-left: 15px;">
                        <div class="shop-name overflow" style="line-height: 30px;margin:0;"></div>
                        <div class="shop-types"></div>
                    </div>
                </div>
                <div style="margin-top: 18px;font-weight:600;">
                    <div class="navigation-div" style="float:right;text-align:center;" hidden>
                        <a class="navigate-link" style="width: 40px;height: 45px;display: inline-block"></a>
                        <div style="font-size: 8px;">Get Directions</div>
                    </div>
                    <p><a class="contact-number" style="color:#de863f"></a></p>
                    <p class="wifi-status"></p>
                    <p class="offer-code-div" hidden>
                        <span class="offer-code"></span><br />
                        <span style="font-size: 9px;font-weight: 500;" class="offer-details"></span>
                    </p>
                    <p class="opening-hours-div" hidden></p>
                </div>
            </div>
            <div></div>
            <div style="cursor:pointer;text-align: center;background-color: #de863f;padding: 15px;font-size: 20px;font-weight: 600;color: #fff;" class="redeem-big-btn" hidden>Redeem</div>
            <hr style="box-shadow: 0px 1.5px 2px 1px #d6d6d6;" />
            <div class="google-reviews" style="margin-top:20px;padding: 0 10px 10px 10px;" hidden>
                <p style="color: #de863f;font-size: 18px;">Google Reviews</p>
                <div style="font-style: italic;margin: 30px 10px 0 0" class="review-card template">
                    <div style="font-size: 16px;margin-bottom: 5px;" class="author-name"></div>
                    <div>
                        <span class="fa fa-star"></span>
                        <span class="fa fa-star"></span>
                        <span class="fa fa-star"></span>
                        <span class="fa fa-star"></span>
                        <span class="fa fa-star"></span>
                        Posted on <span class="author-time"></span>
                    </div>
                    <div class="author-text"></div>
                </div>
            </div>
        </div>
    </main>

    <main class="main card-container profile-page" hidden>
        <div style="margin:16px;">
            <a class="edit-btn" style="float:right;"><img src="/pwa/images/<EMAIL>" /></a>
            <img src="/pwa/images/<EMAIL>" />
            <div class="view-container">
                <p class="person-name" hidden></p>
                <p class="contact-number"></p>
                <p class="email-id"></p>
            </div>
            <div style="padding: 25px 10px 0 10px;" class="edit-container" hidden>
                <input id="user-name" type="text" placeholder="Name" />
                <input id="user-email" type="email" placeholder="<EMAIL>" />
                <p style="text-align:center;margin-top: 30px;"><a style="width: 200px;border-radius: 0;" class="primary-btn" id="save-profile">SAVE</a></p>
            </div>
            <hr />
        </div>
    </main>
    <div class="dialog-container">
        <div class="dialog">
            <div class="dialog-title">Provide your Mobile Number to Redeem</div>
            <div class="dialog-body">
                <input id="mobileToRedeem" type="tel" placeholder="Mobile Number"/>
            </div>
            <div class="dialog-buttons">
                <a id="butRedeemWithMobile" style="margin-right:10px;" class="primary-btn">Redeem</a>
                <a id="butCancel" class="primary-btn" onclick="$('.dialog-container').removeClass('dialog-container--visible');">Cancel</a>
            </div>
        </div>
    </div>
    <div class="loader">
        <svg viewBox="0 0 32 32" width="32" height="32">
            <circle id="spinner" cx="16" cy="16" r="14" fill="none"></circle>
        </svg>
    </div>
    <div id="map" style="display:none"></div>
    <!-- Uncomment the line below when ready to test with fake data -->
    <script src="/jsLibs/jquery.js"></script>
    <script src="@Util.GetUrlWithVersion("/pwa/scripts/app.js")" async></script>
    <!-- Google Analytics -->
    <script>
        var isCaptive = @(ViewBag.isCaptive == null ? "false" : "true");
        if(!isCaptive){
            (function (i, s, o, g, r, a, m) {
                i['GoogleAnalyticsObject'] = r; i[r] = i[r] || function () {
                    (i[r].q = i[r].q || []).push(arguments)
                }, i[r].l = 1 * new Date(); a = s.createElement(o),
                m = s.getElementsByTagName(o)[0]; a.async = 1; a.src = g; m.parentNode.insertBefore(a, m)
            })(window, document, 'script', 'https://www.google-analytics.com/analytics.js', 'ga');
            ga('create', 'UA-108055560-1', 'auto');
            ga('send', 'pageview');
        }
    </script>
    <!-- End Google Analytics -->
</body>
</html>
