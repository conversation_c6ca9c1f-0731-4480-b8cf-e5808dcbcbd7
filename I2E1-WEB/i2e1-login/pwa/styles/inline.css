
* {
  box-sizing: border-box; }

html, body {
  padding: 0;
  margin: 0;
  height: 100%;
  width: 100%;
  font-family: 'Helvetica', 'Verdana', sans-serif;
  font-weight: 400;
  font-size:13px;
  font-display: optional;
  color: #444;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale; }

html {
  overflow: hidden; }

body {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -webkit-flex-direction: column;
      -ms-flex-direction: column;
          flex-direction: column;
  -webkit-flex-wrap: nowrap;
      -ms-flex-wrap: nowrap;
          flex-wrap: nowrap;
  -webkit-box-pack: start;
  -webkit-justify-content: flex-start;
      -ms-flex-pack: start;
          justify-content: flex-start;
  -webkit-box-align: stretch;
  -webkit-align-items: stretch;
      -ms-flex-align: stretch;
          align-items: stretch;
  -webkit-align-content: stretch;
      -ms-flex-line-pack: stretch;
          align-content: stretch;
  background: #fff; }

hr{
    border-top: none;
    border-bottom: 1px solid rgb(220, 208, 208);
    margin: 20px 0 20px 0;
}

a{
    cursor: pointer;
}
p{
    margin: 8px 0;
}

input{
    border: none;
    border-bottom: 1px solid #ddd;
    background-color: transparent;
    width: 100%;
    outline: none;
    margin: 9px 0px;
    font-size: 16px;

    -webkit-box-flex: 1;
    box-flex: 1;
    -webkit-flex-grow: 1;
    flex-grow: 1;
    -webkit-flex-shrink: 1;
    flex-shrink: 1;
    background-color: transparent;
    display: block;
    height: 24px;
    line-height: 24px;
    min-width: 0%;
    outline: none;
    padding: 0;
    z-index: 0;
}

input:focus{
    outline:none;
    border-bottom:2px solid #ddd;
}
.template{
    display:none;
}
.welcome-screen{
    position: fixed;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    background-color: #5454d0;
    z-index: 10001;
    color: #fff;
    background-image: url(/pwa/images/background.jpg);
    background-repeat: no-repeat;
    background-size: cover;
}

.primary-btn{
    display:inline-block;
    bottom: 10px;
    background-color: #de863f;
    padding: 9px 25px;
    font-weight: bold;
    border-radius: 3px;
    color:#fff;
}

.header {
  width: 100%;
  height: 56px;
  color: #FFF;
  background: linear-gradient(to left,#3a7cd0, #08dab8);
  position: fixed;
  font-size: 20px;
  box-shadow: 0 4px 5px 0 rgba(0, 0, 0, 0.14), 0 2px 9px 1px rgba(0, 0, 0, 0.12), 0 4px 2px -2px rgba(0, 0, 0, 0.2);
  padding: 16px 16px 0 16px;
  will-change: transform;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
  -webkit-flex-direction: row;
      -ms-flex-direction: row;
          flex-direction: row;
  -webkit-flex-wrap: nowrap;
      -ms-flex-wrap: nowrap;
          flex-wrap: nowrap;
  -webkit-box-pack: start;
  -webkit-justify-content: flex-start;
      -ms-flex-pack: start;
          justify-content: flex-start;
  -webkit-box-align: stretch;
  -webkit-align-items: stretch;
      -ms-flex-align: stretch;
          align-items: stretch;
  -webkit-align-content: center;
      -ms-flex-line-pack: center;
          align-content: center;
  -webkit-transition: -webkit-transform 0.233s cubic-bezier(0, 0, 0.21, 1) 0.1s;
  transition: -webkit-transform 0.233s cubic-bezier(0, 0, 0.21, 1) 0.1s;
  transition: transform 0.233s cubic-bezier(0, 0, 0.21, 1) 0.1s;
  transition: transform 0.233s cubic-bezier(0, 0, 0.21, 1) 0.1s, -webkit-transform 0.233s cubic-bezier(0, 0, 0.21, 1) 0.1s;
  z-index: 1000; }
  .header .headerButton {
    width: 24px;
    height: 24px;
    margin-right: 16px;
    text-indent: -30000px;
    overflow: hidden;
    -webkit-transition: opacity 0.333s cubic-bezier(0, 0, 0.21, 1);
    transition: opacity 0.333s cubic-bezier(0, 0, 0.21, 1);
    border: none;
    outline: none;
    cursor: pointer; }
  .header #butRefresh {
    background: url(/pwa/images/ic_refresh_white_24px.svg) center center no-repeat; }
  .header #butAddToHome {
    background: url('/pwa/images/add <NAME_EMAIL>') center center no-repeat; }
  .header #butProfile {
    background: url(/pwa/images/<EMAIL>) center center no-repeat; }
  .header #butBack {
    background: url(/pwa/images/<EMAIL>) center center no-repeat; }

.header__title {
  font-weight: 400;
  font-size: 12px;
  margin: 0;
  -webkit-box-flex: 1;
  -webkit-flex: 1;
      -ms-flex: 1;
          flex: 1; 
}
.header__title .market-place-name{
    position: relative;
    top: -16px;
    left: 1px;
    font-weight: 600;
    font-size: 14px;
}
.total-data-div{
    position: relative;
    top: -13px;
    left: 35px;
}

.midHeader{
    padding: 70px 0 7px 0;
    background-color: #fff;
}
.midHeader a{
    display: inline-block;
    width: 77px;
    height: 21px;
    text-align: center;
}

.overflow{
    text-overflow: ellipsis;
    width: 90%;
    white-space: NOWRAP;
    overflow: HIDDEN;
}

.loader {
  left: 50%;
  top: 50%;
  position: fixed;
  -webkit-transform: translate(-50%, -50%);
          transform: translate(-50%, -50%); }
  .loader #spinner {
    box-sizing: border-box;
    stroke: #673AB7;
    stroke-width: 3px;
    -webkit-transform-origin: 50%;
            transform-origin: 50%;
    -webkit-animation: line 1.6s cubic-bezier(0.4, 0, 0.2, 1) infinite, rotate 1.6s linear infinite;
            animation: line 1.6s cubic-bezier(0.4, 0, 0.2, 1) infinite, rotate 1.6s linear infinite; }

.checked {
        color: #f1b400;
}

@-webkit-keyframes rotate {
  from {
    -webkit-transform: rotate(0);
            transform: rotate(0); }
  to {
    -webkit-transform: rotate(450deg);
            transform: rotate(450deg); } }

@keyframes rotate {
  from {
    -webkit-transform: rotate(0);
            transform: rotate(0); }
  to {
    -webkit-transform: rotate(450deg);
            transform: rotate(450deg); } }

@-webkit-keyframes line {
  0% {
    stroke-dasharray: 2, 85.964;
    -webkit-transform: rotate(0);
            transform: rotate(0); }
  50% {
    stroke-dasharray: 65.973, 21.9911;
    stroke-dashoffset: 0; }
  100% {
    stroke-dasharray: 2, 85.964;
    stroke-dashoffset: -65.973;
    -webkit-transform: rotate(90deg);
            transform: rotate(90deg); } }

@keyframes line {
  0% {
    stroke-dasharray: 2, 85.964;
    -webkit-transform: rotate(0);
            transform: rotate(0); }
  50% {
    stroke-dasharray: 65.973, 21.9911;
    stroke-dashoffset: 0; }
  100% {
    stroke-dasharray: 2, 85.964;
    stroke-dashoffset: -65.973;
    -webkit-transform: rotate(90deg);
            transform: rotate(90deg); } }

.main {
  padding-top: 60px;
  -webkit-box-flex: 1;
  -webkit-flex: 1;
      -ms-flex: 1;
          flex: 1;
  overflow-x: hidden;
  overflow-y: auto;
  -webkit-overflow-scrolling: touch; }

.dialog-container {
  background: rgba(0, 0, 0, 0.57);
  position: fixed;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  opacity: 0;
  pointer-events: none;
  will-change: opacity;
  -webkit-transition: opacity 0.333s cubic-bezier(0, 0, 0.21, 1);
  transition: opacity 0.333s cubic-bezier(0, 0, 0.21, 1); }

.dialog-container--visible {
  opacity: 1;
  pointer-events: auto; }

.dialog {
  background: #FFF;
  border-radius: 2px;
  box-shadow: 0 0 14px rgba(0, 0, 0, 0.24), 0 14px 28px rgba(0, 0, 0, 0.48);
  min-width: 280px;
  position: absolute;
  left: 50%;
  top: 50%;
  -webkit-transform: translate(-50%, -50%) translateY(30px);
          transform: translate(-50%, -50%) translateY(30px);
  -webkit-transition: -webkit-transform 0.333s cubic-bezier(0, 0, 0.21, 1) 0.05s;
  transition: -webkit-transform 0.333s cubic-bezier(0, 0, 0.21, 1) 0.05s;
  transition: transform 0.333s cubic-bezier(0, 0, 0.21, 1) 0.05s;
  transition: transform 0.333s cubic-bezier(0, 0, 0.21, 1) 0.05s, -webkit-transform 0.333s cubic-bezier(0, 0, 0.21, 1) 0.05s; }

.dialog > div {
  padding-left: 24px;
  padding-right: 24px; }

.dialog-title {
  padding-top: 20px;
  font-size: 1.25em; }

.dialog-body {
  padding-top: 20px;
  padding-bottom: 24px; }
  .dialog-body select {
    width: 100%;
    font-size: 2em; }

.dialog-buttons {
  padding: 8px !important;
  float: right; }

.card {
  padding: 16px;
  position: relative;
  box-sizing: border-box;
  background: #fff;
  border-radius: 2px;
  margin: 16px;
  height: 200px;
  color: #fff;
  box-shadow: 0 2px 2px 0 rgba(0, 0, 0, 0.14), 0 3px 1px -2px rgba(0, 0, 0, 0.2), 0 1px 5px 0 rgba(0, 0, 0, 0.12); }

.toggle-offer{
    float: right;
    margin-top: -52px;
    margin-right: 15px;
}

.layer {
    background-color: rgba(10, 10, 10, 0.6);
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
}

.info-div{
    position:absolute;
    width:100%;
    bottom: 20px;
}

.info-div > div{
    margin-top: 8px;
    padding: 2px 0;
    line-height: 7px;
    margin-top:10px;
}

.details-page p{
    line-height: 20px;
}

.shop-name{
    font-size: 30px;
    font-weight: 600;
}

.contact-number{
    background-image: url(/pwa/images/<EMAIL>);
    background-repeat: no-repeat;
    line-height: 19px;
    padding-left: 25px !important;
    color:inherit;
    text-decoration:none;
    display: inline-block;
}

.list-container .contact-number{
    background-image:url(/pwa/images/<EMAIL>);
    padding-left: 20px !important;
    line-height: 12px;
}

.profile-page .contact-number{
    font-weight:600;
}

.email-id{
    background-image: url(/pwa/images/<EMAIL>);
    background-repeat: no-repeat;
    font-weight: 600;
    line-height: 19px;
    padding-left: 25px !important;
}

.person-name{
    font-weight: 600;
}

.wifi-status{
    background-image: url(/pwa/images/<EMAIL>);
    background-repeat: no-repeat;
    padding-left: 25px !important;
}
.list-container .wifi-status{
    background-image:url(/pwa/images/<EMAIL>);
    padding-left: 20px !important;
}

.offer-code-div{
    background-image: url(/pwa/images/Offer_black.png);
    background-repeat: no-repeat;
    padding-left: 25px !important;
    font-weight: 600;
    font-size: 16px;
}

.opening-hours-div{
    background-image: url(/pwa/images/opening.png);
    background-repeat: no-repeat;
    padding-left: 25px !important;
    font-weight: 600;
}
.list-container .offer-code{
    background-image:url(/pwa/images/Offer_white.png);
    background-repeat: no-repeat;
    padding-left: 20px !important;
    font-size: 16px;
    font-weight: 600;
}

.redeem-btn{
    position: absolute;
    bottom: 10px;
    right: 10px;
    background-color: #de863f;
    padding: 9px 25px;
    font-weight: bold;
    border-radius: 3px;
}

.card-container{
    position:absolute;
    bottom:0;
    right:0;left:0;
    top:0;
    padding-top: 60px;
    background-color:inherit;
}

.total-reviews{
    position:absolute;
    right: 8px;
    top: 8px;
}

.navigate-link{
    background-image:url(/pwa/images/directions.png);
}

.store_open_text{
    font-size: 9px;
    padding-left: 5px;
}