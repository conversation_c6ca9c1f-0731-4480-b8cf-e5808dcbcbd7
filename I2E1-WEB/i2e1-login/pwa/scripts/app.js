var globalCache = {};

var app = {
    globalCache : function(){
        return window.isCaptive ? globalCache : localStorage;
    },
    stack: [],
    localGet: function (key) {
        if (window.isCaptive) {
            return globalCache[key]
        }
        var data = localStorage[key];
        if (data)
            return JSON.parse(data);
        return null;
    },
    localSet: function (key, value) {
        value.lastUpdated = new Date();
        if (window.isCaptive)
            globalCache[key] = value;
        else
            localStorage[key] = JSON.stringify(value);
    },
    localDelete : function(key){
        if (window.isCaptive)
            delete globalCache[key];
        else
            localStorage.removeItem(key);
    },
    setCard: function (nasid, campaignId, data) {
        app.localSet('card_' + nasid + "_" + campaignId, data);
    },
    getCard: function (nasid, campaignId) {
        return app.localGet('card_' + nasid + "_" + campaignId);
    },
    setRedeemed: function (nasid, campaignId, data) {
        app.localSet('redeemed_' + nasid + "_" + campaignId, data);
    },
    getRedeemed: function (nasid, campaignId) {
        var data = app.localGet('redeemed_' + nasid + "_" + campaignId);
        if (data && 7 > Math.floor((new Date() - new Date(data.lastUpdated)) / (1000 * 60 * 60 * 24))) {
            return data;
        }
    },
    setGooglePlaceDetails: function (placeId, data) {
        app.localSet('locationDetails' + placeId, data);
    },
    getGooglePlaceDetails: function(placeId){
        var data = app.localGet('locationDetails' + placeId);
        if (data && 7 > Math.floor((new Date() - new Date(data.lastUpdated)) / (1000 * 60 * 60 * 24))) {
            return data;
        }
    },
    setUser: function (user) {
        var page = $('.profile-page');
        if (user.name) {
            recordEvent('.profile-page .person-name', 'show', 'hide');
            page.find('.person-name').text(user.name);
        }
        gEvent('LISTING', 'PROFILE_CLICKED', user.mobile);
        page.find('.contact-number').text(user.mobile);
        page.find('.email-id').text(user.email);
    },
    months: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'],
    doRefresh: function () {
        app.getForecast();
    }
};

function gEvent(category, action, label) {
    if (!window.isCaptive)
    return ga('send', 'event', category, action, label, getUrlParams().discoveryId);
}

function showLoader() {
    app.loadCounter++;
    app.spinner.show();
}

function hideLoader() {
    app.loadCounter--;
    if (app.loadCounter == 0)
        app.spinner.hide();
}

function getUrlParams() {
    var str = window.location.search.substring(1);
    if (str) {
        var pairs = str.split('=');
        var obj = {};
        for (var i = 0; i < pairs.length; ++i) {
            var j = i + 1;
            obj[pairs[i]] = pairs[j];
            i = j;
        }
        app.localSet('queryString', obj);
    }
    else
        var obj = app.localGet('queryString');

    return obj;
}

function doAjax(url, method, data, onSuccess, onFailure) {
    showLoader();
    url = url.indexOf('?') != -1 ? url + "&discovery-id=" + getUrlParams().discoveryId : url + '?discovery-id=' + getUrlParams().discoveryId;
    url += '&random=' + Math.random();
    return $.ajax({
        type: method,
        url: url,
        data: data ? JSON.stringify(data) : '',
        contentType: "application/json; charset=utf-8",
        dataType: "json",
        timeout: 20 * 1000,
        success: function (data) {
            hideLoader();
            if (onSuccess) onSuccess(data);
        }
    }).error(function (obj, status, errMsg) {
        hideLoader();
        if (onFailure) onFailure(errMsg)
    });
}

function _roundoff(val) {
    return Math.floor(val * 100) / 100;
}

function _roundOffData(dataLeft) {
    if (!dataLeft) return '';
    if (dataLeft >= 1073741824) //converting to GB
        dataLeft = _roundoff(dataLeft / 1073741824) + " GB";
    else if (dataLeft >= 1048576) //converting to MB
        dataLeft = _roundoff(dataLeft / 1048576) + " MB";
    else if (dataLeft >= 1024) //converting to MB
        dataLeft = _roundoff(dataLeft / 1024) + " KB";

    return dataLeft;
}

function _roundoffTime(totalSec) {
    var days = parseInt(totalSec / 86400);
    var hours = parseInt(totalSec / 3600) % 24;
    var minutes = parseInt(totalSec / 60) % 60;
    var seconds = totalSec % 60;
    var result = (days >= 1 ? days + (days == 1 ? ' day ' : ' days ') : '') +
                 (hours >= 1 ? hours + (hours == 1 ? ' hr ' : ' hrs ') : '') +
                 (minutes >= 1 ? minutes + (minutes == 1 ? ' min' : ' mins') : '');
    
}

function startRecording() {
    app.stack.push([]);
    $('#butBack').show();
    $('.header .header__title').hide();
}

function recordEvent(selector, initialAction, reverseAction) {
    if (selector === 'refresh') {
        reverseAction = app.doRefresh;
        app.doRefresh = app[initialAction];
    }
    else
        $(selector)[initialAction]();
    app.stack[app.stack.length - 1].push({ selector: selector, initialAction: initialAction, reverseAction: reverseAction });
}

function playReverse() {
    for (var i = 0; i < app.stack[app.stack.length - 1].length; ++i) {
        var obj = app.stack[app.stack.length - 1][i];
        if (obj.selector === 'refresh')
            app.doRefresh = obj.reverseAction;
        else
            $(obj.selector)[obj.reverseAction]();
    }
    app.stack.splice(app.stack.length - 1);
    if (app.stack.length == 0) {
        $('#butBack').hide();
        $('.header .header__title').show();
    }
}

(function () {
    app.spinner = $('.loader');
    app.cardTemplate = $('.cardTemplate');
    app.loadCounter = 0;

    $('#butRefresh').click(function () {
        gEvent('LISTING', 'REFRESH_CLICKED', '');
        app.doRefresh();
    });

    $('#butAddToHome').click(function () {
        if (window.deferredPrompt) {
            deferredPrompt.prompt();
            deferredPrompt.userChoice.then(function (choiceResult) {
                console.log(choiceResult.outcome);
                var session = app.localGet('marketPlace' + getUrlParams().discoveryId);
                if (choiceResult.outcome == 'dismissed') {
                    gEvent('LISTING', 'ADDTO_HOME_CLICKED', 'CANCELLED');
                    console.log('User cancelled home screen install');
                    doAjax('/discover/log', 'POST', {
                        category: 'ACTION', source: 'PWA_UI', name: 'ADD_TO_HOME_SCREEN', data: 'PROMPT_CANCELLED',
                        mobile: session.mobile, mac: session.mac, marketplace: session.marketPlaceId, campaignId: session.campaignId, discoveryId: getUrlParams().discoveryId
                    });
                    $('#butAddToHome').hide();
                }
                else {
                    gEvent('LISTING', 'ADDTO_HOME_CLICKED', 'SUCCESS');
                    console.log('User added to home screen');
                    doAjax('/discover/log', 'POST', {
                        category: 'ACTION', source: 'PWA_UI', name: 'ADD_TO_HOME_SCREEN', data: 'ADDED_SUCCESSFULY',
                        mobile: session.mobile, mac: session.mac, marketplace: session.marketPlaceId, campaignId: session.campaignId, discoveryId: getUrlParams().discoveryId
                    });
                }
                deferredPrompt = null;
            });
        }
    });

    $('#butBack').click(function () {
        gEvent('BACK_CLICKED');
        playReverse();
    });

    app.showOffer = true;
    $('.toggle-offer a').click(function () {
        app.showOffer = !app.showOffer;
        var cards = [];
        Object.keys(app.globalCache()).forEach(function (key) {
            if (key.indexOf('card_') == 0) {
                var card = app.localGet(key);
                if (app.showOffer || !card.voucher)
                    cards.push(card)
            }
        });
        cards.sort(function (a, b) {
            return a.index - b.index;
        });
        if (app.showOffer) {
            $('.toggle-offer a img').attr('src', '/pwa/images/Offer <EMAIL>');
        }
        else {
            $('.toggle-offer a img').attr('src', '/pwa/images/<EMAIL>');
        }
        app.updateForecastCard(cards);
    });

    $('.profile-page .edit-btn').click(function () {
        var profile = app.localGet('profile');
        $('#user-name').val(profile.name);
        $('#user-email').val(profile.email);
        gEvent('PROFILE', 'EDIT_CLICKED', profile.email);
        startRecording();
        recordEvent('.view-container', 'hide', 'show');
        recordEvent('.edit-container', 'show', 'hide');
    });

    $('#save-profile').click(function () {
        var profile = app.localGet('profile');
        var name = $('#user-name').val();
        var email = $('#user-email').val();
        if (name)
            profile.name = name;
        if (email)
            profile.email = email;
        gEvent('PROFILE', 'SAVE_CLICKED', profile.email);
        doAjax('/Discover/SaveUser', 'POST', profile, function () {
            app.localSet('profile', profile);
            playReverse();
            app.setUser(profile);
        });
    });

    $('#butProfile').click(function () {
        startRecording();
        var profile = app.localGet("profile");
        if (profile)
            app.setUser(profile);
        app.fetchProfile = function () {
            doAjax('/Discover/GetUserProfile', 'GET', null, function (response) {
                app.setUser(response.data);
                app.localSet('profile', response.data);
            });
        }
        app.fetchProfile();
        recordEvent('.profile-page', 'fadeIn', 'fadeOut');
        recordEvent('refresh', 'fetchProfile');
    });

    // Updates a weather card with the latest weather forecast. If the card
    // doesn't already exist, it's cloned from the template.
    app.updateForecastCard = function (data) {
        var dataAvailable = 0;
        var oldDataAvailable = 0;
        $('.list-container .card:not(.cardTemplate)').remove();
        for (var i = 0; i < data.length; ++i) {
            var dataLastUpdated = new Date(data[i].created);
            var card = app.cardTemplate.clone(true);
            card.removeClass('cardTemplate');
            card.show();
            $('.list-container').append(card);

            // Verifies the data provide is newer than what's already visible
            // on the card, if it's not bail, if it is, continue and update the
            // time saved in the card
            var cardLastUpdatedElem = card.find('.card-last-updated');
            var cardLastUpdated = cardLastUpdatedElem.text();
            if (cardLastUpdated) {
                cardLastUpdated = new Date(cardLastUpdated);
                // Bail if the card has more recent data then the data
                if (dataLastUpdated.getTime() < cardLastUpdated.getTime()) {
                    return;
                }
            }
            var store = data[i].store || {};
            var voucher = data[i].voucher;

            cardLastUpdatedElem.text(data[i].created);
            card.find('.shop-name').text(store.storeName);
            card.find('.nasid').text(store.nasid);
            if (store.storeImg)
                card.css('background-image', "url('" + store.storeImg + "')");
            if (voucher) {
                var oldCard = app.getRedeemed(store.nasid, voucher.id);
                var campaignId = voucher.id;
                var nasid = store.nasid;
                card.find('.offer-code').text(voucher.text).show();
                card.find('.campaign-id').text(campaignId);
                // TODO: Get Old text
                if (oldCard) {
                    card.find('.redeem-btn').show().text(oldCard.redeemedCode);
                } else {
                    card.find('.redeem-btn').show().click(function (event) {
                        var link = $(this).parents('.card');
                        app.redeemVoucher('LISTING', this, link.find('.campaign-id').text(), link.find('.nasid').text(), link);
                        event.stopPropagation();
                    });
                }
            }
            card.click(function () {
                app.showFullCard(this);
            });
            if (store.nasid) {
                var wifiAvailable = app.localGet("wifiStatus" + store.nasid);
                if (wifiAvailable) {
                    card.find('.wifi-status').show().html(wifiAvailable.text);
                    oldDataAvailable += wifiAvailable.dataLeft;
                    $('.total-data-div').show().find('.total-data').text(_roundOffData(oldDataAvailable));
                }
                if (!wifiAvailable || (Math.floor((new Date() - new Date(wifiAvailable.lastUpdated)) / (1000 * 60 * 60)) >= 1)) {
                    (function () {
                        var finalCard = card, nasid = store.nasid;
                        doAjax('/Discover/GetWifiPolicy?nasid=' + nasid, 'GET', null, function (response) {
                            var timeLeft = _roundoffTime(response.data.timeLeft);
                            if (timeLeft) {
                                var dataLeft = _roundOffData(response.data.dataLeft);
                                var text = (dataLeft ? dataLeft + ' for ' : '') + timeLeft;
                                app.localSet("wifiStatus" + nasid, { text: text, timeLeft: response.data.timeLeft, dataLeft: response.data.dataLeft });
                                finalCard.find('.wifi-status').show().html(text);
                                dataAvailable += response.data.dataLeft;
                                $('.total-data-div').show().find('.total-data').text(_roundOffData(dataAvailable));
                            }
                        }, function () {
                        })
                    })();
                }
                (function (card, placeId) {
                    if (placeId)
                        setTimeout(function () {
                            app.placeDetailsByPlaceId(placeId, function (response) {
                                card.find('.contact-number').text(response.formatted_phone_number).attr('href', 'tel:' + response.formatted_phone_number);
                                app.localSet(placeId, response);
                            });
                        }, 500 * i);
                })(card, store.googlePlaceId);
            }
            app.setCard(store.nasid, voucher ? voucher.id : '', data[i]);

        }
    };

    app.redeemVoucher = function (where, link, campaignId, nasid, card, mobile) {
        var discoveryId = getUrlParams().discoveryId;
        var marketPlaceData = app.localGet('marketPlace' + discoveryId);
        if (!marketPlaceData.mobile && mobile) {
            marketPlaceData.mobile = mobile;
            app.localSet('marketPlace' + discoveryId, marketPlaceData);
        }
        if (marketPlaceData.mobile) {
            gEvent(where, 'REDEEM_CLICKED', campaignId);
            var data = app.getCard(nasid, campaignId);
            var redeemed = app.getRedeemed(nasid, campaignId);
            if (redeemed) {
                $(link).unbind("click").text(redeemed.redeemedCode);
                card.find('.redeem-btn').unbind('click').text(redeemed.redeemedCode);
                $('.offer-details').text(redeemed.redeemedText);
            }
            else {
                doAjax('/Discover/RedeemVoucher', 'POST', { campaignId: campaignId, nasid: nasid, mobile: mobile }, function (response) {
                    response.data.parameters.offer_code = response.data.parameters.offer_code ? response.data.parameters.offer_code : 'Redeemed';
                    $(link).unbind("click").text(response.data.parameters.offer_code);
                    card.find('.redeem-btn').unbind('click').text(response.data.parameters.offer_code);
                    $('.offer-details').text(response.data.finalContent)
                    redeemed = { redeemedCode: response.data.parameters.offer_code, redeemedText: response.data.finalContent }
                    app.setRedeemed(nasid, campaignId, redeemed);
                }, function () {
                });
            }
        }
        else {
            $('.dialog-container').addClass('dialog-container--visible');
            $('#butRedeemWithMobile').unbind('click').click(function () {
                $('.dialog-container').removeClass('dialog-container--visible');
                app.redeemVoucher(where, link, campaignId, nasid, card, $('#mobileToRedeem').val());
            });
        }
    }

    app.showFullCard = function (card) {
        card = $(card)
        var campaignId = card.find('.campaign-id').text();
        var nasid = card.find('.nasid').text();
        var data = app.getCard(nasid, campaignId);
        var voucher = data.voucher, store = data.store;
        var wifiStatus = app.localGet("wifiStatus" + nasid);
        gEvent('LISTING', 'CARD_CLICKED', nasid);
        $('.details-page > div:not(.template)').remove();
        var newCard = $('.details-page > .template').clone();
        newCard.removeClass('template');
        newCard.find('.shop-name').text(store.storeName);
        newCard.find('.wifi-status').text(wifiStatus.text);
        newCard.find('.nasid').text(store.nasid);
        $('.details-page').append(newCard);
        newCard.find('.card-image').css('background-image', "url('" + store.storeImg + "')");
        startRecording();
        var session = app.localGet('marketPlace' + getUrlParams().discoveryId);
        if (voucher) {
            newCard.find('.campaign-id').text(voucher.id);
            recordEvent('.details-page > div:not(.template) .offer-code-div', 'show', 'hide');
            recordEvent('.details-page > div:not(.template) .redeem-big-btn', 'show', 'hide');
            newCard.find('.offer-code').text(voucher.text);
            var redeemed = app.getRedeemed(nasid, campaignId);
            if (redeemed) {
                newCard.find('.redeem-big-btn').text(redeemed.redeemedCode);
                newCard.find('.offer-details').text(redeemed.redeemedText);
            }
            else {
                newCard.find('.redeem-big-btn').unbind('click').click(function () {
                    app.redeemVoucher('DETAIL', newCard.find('.redeem-big-btn'), campaignId, nasid, card);
                });
                newCard.find('.offer-details').text('');
            }
            doAjax('/discover/log', 'POST', {
                category: 'CLICK', source: 'PWA_UI', name: 'VOUCHER_CARD', data: campaignId,
                mobile: session.mobile, mac: session.mac, nasid: nasid, marketplace: session.marketPlaceId, campaignId: session.campaignId, discoveryId: getUrlParams().discoveryId
            });
        } else {
            doAjax('/discover/log', 'POST', {
                category: 'CLICK', source: 'PWA_UI', name: 'WIFI_CARD', data: campaignId,
                mobile: session.mobile, mac: session.mac, nasid: nasid, marketplace: session.marketPlaceId, campaignId: session.campaignId, discoveryId: getUrlParams().discoveryId
            });
        }

        recordEvent('.list-page', 'fadeOut', 'fadeIn');
        recordEvent('.details-page', 'fadeIn', 'fadeOut');
        app.getGoogleReviews = function () {
            if (store.googlePlaceId) {
                recordEvent('.details-page > div:not(.template) .google-reviews', 'show', 'hide');
                function updateGoogleDetails(response) {
                    var reviewCard = newCard.find('.review-card.template');
                    newCard.find('.contact-number').text(response.formatted_phone_number).attr('href', 'tel:' + response.formatted_phone_number);
                    newCard.find('.total-reviews').find('.fa-star:lt(' + Math.floor(response.rating + 0.5) + ')').addClass('checked');
                    newCard.find('.total-reviews').show().append($('<div>').text(response.reviews.length + (response.reviews.length > 1 ? ' Reviews' : ' Review')));
                    var types = '';
                    for (var i = 0; i < 3 && i < response.types.length; ++i) {
                        if (response.types[i].indexOf('_') < 0)
                            types += ', ' + response.types[i].charAt(0).toUpperCase() + response.types[i].slice(1);
                    }
                    newCard.find('.shop-types').text(types.substring(1));
                    if (response.opening_hours) {
                        var openingHourText = response.opening_hours.weekday_text[new Date().getDay() - 1];
                        var text = $('<span class="store_open_text">');
                        if (response.opening_hours.open_now)
                            text.text("Open Now").css('color', '#6dbb6d');
                        else
                            text.text('Closed').css('color', '#f00');
                        newCard.find('.opening-hours-div').show().text(openingHourText.substring(openingHourText.indexOf(':') + 1)).append(text);
                    }

                    var reviewContainer = newCard.find('.google-reviews');
                    for (var i = 0; i < response.reviews.length; ++i) {
                        var review = response.reviews[i];
                        var cloneCard = reviewCard.clone();
                        var date = new Date(0);
                        date.setUTCSeconds(review.time)
                        cloneCard.find('.fa-star:lt(' + review.rating + ')').addClass('checked');
                        cloneCard.find('.author-time').text(date.getDate() + ' ' + app.months[date.getMonth()] + ' ' + date.getFullYear());
                        cloneCard.find('.author-name').text(review.author_name);
                        cloneCard.find('.author-text').text(review.text);
                        reviewContainer.append(cloneCard.removeClass('template'));
                    }
                    var lat = response.geometry.location.lat;
                    var lng = response.geometry.location.lng;
                    if (lat && lng) {
                        newCard.find('.navigation-div').show();
                        newCard.find('.navigate-link').click(function () {
                            window.open("http://maps.google.com/maps?daddr=" + lat + ',' + lng + "&ll=");
                        });
                    }
                }
                var response = app.localGet(store.googlePlaceId);
                if (response)
                    updateGoogleDetails(response);
                else
                    app.placeDetailsByPlaceId(store.googlePlaceId, function (response) {
                        updateGoogleDetails(response);
                    });
            }
        }
        app.getGoogleReviews();
    }

    app.getForecast = function () {
        var url = '/Discover/GetCards';
        // TODO add cache logic here

        var cards = [];
        Object.keys(app.globalCache()).forEach(function (key) {
            if (key.indexOf('card_') == 0) {
                cards.push(app.localGet(key))
            }
        });
        cards.sort(function (a, b) { return a.index - b.index; });
        app.updateForecastCard(cards);
        doAjax(url, "GET", null, function (response) {
            Object.keys(app.globalCache()).forEach(function (key) {
                if (key.indexOf('card_') == 0) {
                    app.localDelete(key);
                }
            });
            var results = response.data;
            for (var i = 0; i < response.data.length; ++i)
                response.data[i].index = i;
            app.updateForecastCard(results);
        }, function () {
        });
    };

    app.setMarketPlace = function (marketPlaceData) {
        $('.market-place-name').text(marketPlaceData.marketPlaceName);
    }

    setTimeout(function () { $('.welcome-screen').fadeOut('slow'); }, 3000);

    app.placeDetailsByPlaceId = function (placeId, onSucess) {
        var request = {
            placeId: placeId
        };
        var data = app.getGooglePlaceDetails(placeId);
        if (data)
            onSucess(data);
        else {
            doAjax('/Discover/GetLocationDetails?placeId=' + placeId, 'GET', null, function (response) {
                if (response.status == 0) {
                    var placeData = JSON.parse(response.data);
                    console.log(placeData);
                    app.setGooglePlaceDetails(placeId, placeData.result);
                    onSucess(placeData.result);
                }
            });
        }
    }

    var discoveryId = getUrlParams().discoveryId;
    doAjax('/Discover/GetMarketPlaceDetails', 'GET', null, function (response) {
        app.localSet('marketPlace' + discoveryId, response.data.session);
        app.setMarketPlace(response.data.session);
    });
    var marketPlaceData = app.localGet('marketPlace' + discoveryId);
    if (marketPlaceData)
        app.setMarketPlace(marketPlaceData);

    if ('serviceWorker' in navigator) {
        navigator.serviceWorker
                 .register('/discover/serviceworker')
                 .then(function () { console.log('Service Worker Registered'); });
    }

    app.getForecast();
    window.addEventListener('beforeinstallprompt', function (e) {
        $('#butAddToHome').css('display', 'inline-block');
        console.log('Deferring Promt for Later');
        e.preventDefault();
        // Stash the event so it can be triggered later.
        window.deferredPrompt = e;
        return false;
    });
})();
