    document.write(`<header id="grad1">
        <!--<div class="container">-->
        <div class="row">
            <div class="col-md-3 text-center col-sm-6 col-6">
                <div>
                    <a href="index.html"><img src="assets/ic_linq_logo.svg" class="logo-text" alt="India’s Leading App for Free WiFi without password from a WiFi Map of 1 million+ hotspots" /></a>
                </div>
            </div>
        </div>
        <!--</div>-->
    </header>
    <div class="container">
        <h1>Linq FAQs</h1><br />
        <div>
            <h5 style="color: #227e9c">What is Linq?</h5>
            <p>Linq is the only app that helps you to <strong>Get</strong> and <strong>Share</strong> free WiFi internet. Using Linq, you can:</p>
            <ul>
                <li>Seamlessly <strong>Find</strong> & <strong>Get</strong> access to millions of WiFi hotspots and active <strong>Linqs</strong> near you.</li>
                <li>Get Free WiFi Internet, higher speeds and save your precious phone data.</li>
                <li><strong>Share your phone internet</strong> with your friends by converting your phone into a portable hotspot. Join the ever-growing community of Active <strong>Linqs.</strong></li>
            </ul>
            <p>Get Free WiFi Internet, higher speeds and save your precious phone data.</p>
        </div><br />
        <div>
            <h5 style="color: #227e9c">What are the key benefits of using Linq?</h5>
            <p>Linq is the only app that empowers you to Get access to free WiFi networks near you and Share your phone internet with friends. </p>
            <p>Using Linq to <strong>Get</strong> WiFi internet has following key benefits:</p>
            <ol>
                <li>Save your precious phone data by connecting seamlessly to free WiFi hotspots and growing community of Linqs around you. Get More!</li>
                <li>Enjoy ‘Home like’ WiFi experience by automatically connecting to partner networks and get preferential data & amazing offers. Benefit More!</li>
                <li>Be assured of your WiFi consumption by accessing history of your WiFi internet usage.</li>
            </ol>
            <p>Using Linq to <strong>Share</strong> phone internet has following key benefits:</p>
            <ol>
                <li>Your single phone internet connection can serve multiple friends, family and other devices. No need to buy expensive hotspot devices.</li>
                <li>Share internet without worrying about misuse by controlling data/time limits for each user and access the history anytime. Even expensive hotspot devices don’t have such features</li>
                <li>Spread happiness by sharing your unused phone data. Become a part of ever-growing community of Linqs and contribute in creating a connected world</li>
            </ol>

        </div><br />
        <div>
            <h5 style="color: #227e9c">How do I Get WiFi Internet using Linq?</h5>
            <p>You can Get WiFi Internet using Linq in 2 simple steps:</p>
            <ol>
                <li>
                    <strong>Click on “Get WiFi” option on the Home screen.</strong>
                    <p>After clicking on “Get WiFi”, Linq will show you following 3 types of WiFi networks near you.</p>
                    <ol type="i">
                        <li>Active Linqs: These are WiFi networks created by users like you have who have downloaded Linq and are sharing their phone internet.</li>
                        <li>Instant Connect Networks: These networks don’t need a password and you can connect with them by clicking on the Network name on the screen. You can identify Linq partner networks with “Recommended” sign in this section.</li>
                        <li>Networks with password: These networks are password protected</li>
                    </ol>
                </li>
                <li>
                    <strong>Choose the network that you want to get connected and click on it.</strong>
                    <p>For Active Linqs and partner networks, you will get connected instantly just by clicking against their names. For partner networks, you will also get higher data limits.</p>
                    <p>For other networks, you will receive a notification on your phone, you can click on the notification and go through the steps listed on the screen.</p>
                    <p>For Networks with password, you can connect with one click (in case you have stored the password) or enter the password manually, if you know it.</p>
                </li>
            </ol>
        </div><br />
        <div>
            <p style="color: #227e9c"><strong>How do I Share my Phone Internet using Linq?</strong></p>
            <p><strong><u>4 simple Steps for Sharing internet with your friends having Android phones:</u></strong></p>
            <ol>
                <li>
                    <strong>Click on “Share Internet” option on the Home screen.</strong>
                    <p>On clicking “Share Internet”, Linq will show you further 2 options:</p>
                    <ol type="i">
                        <li>With Friend’s phone</li>
                        <li>With Other Devices</li>
                    </ol>
                </li>
                <li><strong>Choose “With Friend’s Phone” option between the 2 options shown to you.</strong></li>
                <li>
                    <strong>Select your friend(s) you want to share internet with.</strong>
                    <p>If your friend’s name is not in your phone contact list, you may also add their phone numbers manually.</p>
                    <p>If you don’t choose any contacts, then anyone with Linq app can make a request to you allowing them to use your internet. However, they can only consume your internet once you approve their request.</p>
                </li>
                <li>
                    <strong>Select the time and data limit you want to set.</strong>
                    <p>After choosing the time and data limit, click on “Continue”.</p>
                    <p>Now, Linq will automatically send SMS to your chosen contacts with the link to connect to your hotspot. If they have Linq app already on their phone, they are instantly connected. Else they will be directed to Play Store to download the app and can then connect.</p>
                    <p>In case your friends do not receive an SMS, they can also open Linq on their phone (if they have it installed), else they can download from Play Store.</p>
                    <p>Your hotspot is now created and ready to be used by your friends. You can now see who is using your phone internet and also track their consumption. Once they have utilized the data/time limits you had set, they will be automatically disconnected. You can also disconnect them at any point in time by click on “End Session”.</p>
                    <p>Remember, your friends can access your internet only when they are in your close vicinity.</p>
                </li>
            </ol>
            <p><strong><u>3 simple Steps for Sharing internet with your friends having Apple phones or other devices (like laptops/tablets):</u></strong></p>
            <ol>
                <li><strong>Click on “Share Internet” option on the Home screen.</strong></li>
                <p>On clicking “Share Internet”, Linq will show you further 2 options:</p>
                <ol type="i">
                    <li>With Friend’s phone</li>
                    <li>With Other Devices</li>
                </ol>
                <li><strong>Choose “With Other Devices” option between the 2 options shown to you.</strong></li>
                <li><strong>Select the time limit and password (optional) you want to set.</strong></li>
            </ol>
            <p>Your hotspot is now created and ready to be used by your friends. Just request your friends to join your WiFi hotspot with the password (if any) you have set.</p>
            <p>You can now see who is using your phone internet and also track total data consumption during the session. Once they have utilized the time limits you had set, they will be automatically disconnected. You can also disconnect them at any point in time by click on “End Session”.</p>
            <p>Remember, your friends can access your internet only when they are in your close vicinity.</p>
        </div><br />
        <div>
            <p style="color: #227e9c"><strong>What are the key features of Linq?</strong></p>
            <p>Linq is the only app that empowers you to Get access to free WiFi networks near you and Share your phone internet with friends. You can enjoy these amazing features in Linq:</p>
            <ol>
                <li>
                    <strong>Get Access to millions of WiFi Hotspots seamlessly. Using Linq, you can easily:</strong>
                    <ol type="i">
                        <li>Find WiFi networks most relevant to you</li>
                        <li>Automatically remember your favourite networks</li>
                        <li>Connect securely and instantly to Linqs & other WiFi networks</li>
                        <li>Get Preferential data & amazing offers on our Partner WiFi networks</li>
                        <li>Access complete history of when and where you have used your internet using Linq</li>
                    </ol>
                </li>
                <li>
                    <strong>Share your phone internet with friends and other devices like laptop/i-Pad. Using Linq, you can automatically:</strong>
                    <ol type="i">
                        <li>Create a Secure Hotspot from your phone</li>
                        <li>Control data and time limit available to each user of your hotspot</li>
                        <li>Let your friends with Linq connect seamlessly without disclosing any passwords</li>
                        <li>Access complete history of who and when has used your internet using Linq</li>
                        <li>Share phone internet with other devices like Laptop/i-Pad with time limits</li>
                    </ol>
                </li>
            </ol>
        </div><br />
        <div>
            <p style="color: #227e9c"><strong>Where can I access the partner networks for higher data limit?</strong></p>
            <p>Currently we have over 3,500 partner hotspots across all major cities in India (with higher concentration in Delhi, Mumbai and Bangalore). Our team is working aggressively to add more networks and will soon show the locations on a map.</p>
        </div><br />
        <div>
            <p style="color: #227e9c"><strong>How do I share internet with a friend who has ios device?</strong></p>
            <p>As of now, Linq is available only on Android Phones. However, you can still share your internet with your friends with ios device by using “With Other Devices” option in “Share Internet”.</p>
            <p>We will soon be coming up with Linq for iOS as well.</p>
        </div><br />
        <div>
            <p style="color: #227e9c"><strong>What is the benefit of using Linq over my Phone Hotspot?</strong></p>
            <p>Linq gives you multiple benefits over using your Phone Hotspot:</p>
            <ol>
                <li>No need to share passwords when sharing through Linq</li>
                <li>Full control over data and time allocated to each user connecting to your hotspot</li>
                <li>Access the details of all the connections at any point in future</li>
            </ol>
        </div><br />
        <div>
            <p style="color: #227e9c"><strong>How else can Linq help?</strong></p>
            <p>Linq has multiple use cases. E.g.:</p>
            <ol>
                <li>Service providers (like cab driver, salon operators etc) can delight their customers by offering internet</li>
                <li>Securely Share small sachets of internet with the people in need</li>
                <li>Shopkeepers can share small amount of internet with customers who want to pay through digital means but do not have access to internet</li>
                <li>If you have more ideas about how Linq can create a better and connected world, please email your valuable thoughts to<a href="mailto:<EMAIL>" target="_blank"> <EMAIL></a></li>
            </ol>
        </div><br />
        <div>
            <p style="color: #227e9c"><strong>Who should I contact if I have any queries regarding Linq?</strong></p>
            <p>Please email us at <a class="text-primary" href="mailto:<EMAIL>" target="_blank"> <EMAIL></a> for any queries. We will respond to you within 24 hours.</p>
        </div>
    </div>
    <footer id="grad3">
        <div class="container">
            <div class="row">
                <div class="col-md-5 col-sm-12 col-12 pt-3">
                    <p><a href="index.html">Linq</a>&nbsp;&nbsp;&nbsp;Copyright &copy 2018&nbsp;&nbsp;|&nbsp;&nbsp;All Rights Reserved</p>
                </div>
                <div class="col-md-4 offset-md-3 col-sm-12 col-12 pt-3">
                    <p class="footrt"><a class="active" href="faq.html" target="_blank">FAQs</a>&nbsp;&nbsp;&nbsp;&nbsp;<a href="mailto:<EMAIL>" target="_blank">Contact Us</a>&nbsp;&nbsp;&nbsp;&nbsp;<a href="privacy.html" target="_blank">Privacy Policy</a>&nbsp;&nbsp;&nbsp;&nbsp;<a href="map.html" target="_blank">WiFi Locator</a></p>
                </div>
            </div>
        </div>
    </footer>`);