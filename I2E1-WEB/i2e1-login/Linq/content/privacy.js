document.write(`<header id="grad1">
        <!--<div class="container">-->
        <div class="row">
            <div class="col-md-3 text-center col-sm-6 col-6">
                <div>
                    <a href="index.html"><img src="assets/ic_linq_logo.svg" class="logo-text" alt="India’s Leading App for Free WiFi without password from a WiFi Map of 1 million+ hotspots" /></a>
                </div>
            </div>
        </div>
        <!--</div>-->
    </header>
    <div class="container">
        <div>
            <h1>PRIVACY POLICY</h1><br />
            <p>We, Omnia information Pvt Ltd ("Linq"), are committed to protecting the security of the information supplied by every user (“User”) of the Linq mobile application (“Application”). This privacy policy (“Privacy Policy”) provides the manner in which <PERSON><PERSON> collects and uses the User information. The User is advised to please read the Privacy Policy carefully prior to using the Application.</p>
            <p>By agreeing to use the Application, the User hereby agrees to be bound by the terms and conditions contained herein and the User expressly gives <PERSON><PERSON> the consent to collect and use the User’s information solely in the manner contemplated herein below.</p>
            <ol>
                <li><h5 style="color: #227e9c">PRIVACY AND PROTECTION OF USER INFORMATION</h5></li>
                <p>1.1. INFORMATION COLLECTED. Linq will automatically receive and collect certain anonymous information in standard usage logs through the web server. Such information shall include, but not limited to:</p>
                <p>&nbsp;&nbsp;1.1.1. an IP address and/or Mac Address and/or any other identifier, assigned to the mobile device used by the User;</p>
                <p>&nbsp;&nbsp;1.1.2. the service set identifier (SSID) of the free wireless internet network the User’s mobile device is connected to;</p>
                <p>&nbsp;&nbsp;1.1.3. location of the SSID; and</p>
                <p>&nbsp;&nbsp;1.1.4. the type of mobile device used by the User</p>
                <p>&nbsp;&nbsp;1.1.5. Other information like SMS on your phone, operating system, location, interests etc</p>
                <p>1.2. Linq may collect the following personally identifiable information about the User:</p>
                <p>&nbsp;&nbsp;1.2.1. name including first and last name;</p>
                <p>&nbsp;&nbsp;1.2.2. email address; and</p>
                <p>&nbsp;&nbsp;1.2.3. mobile number ; and</p>
                <p>&nbsp;&nbsp;1.2.4. demographics (like age, gender etc)</p>
                <p>&nbsp;&nbsp;1.2.5. Interest Information</p>
                <li><h5 style="color: #227e9c">METHOD AND MANNER OF USE OF USER INFORMATION</h5></li>
                <p>2.1. Linq may collect anonymous traffic information from the User when the User uses the Application.</p>
                <p>2.2. Linq may use the User’s information:</p>
                <p>&nbsp;&nbsp;2.2.1. To connect to the wireless internet network being provided by the concerned service provider;</p>
                <p>&nbsp;&nbsp;2.2.2. To provide personalized features;</p>
                <p>&nbsp;&nbsp;2.2.3. To modify the Application from time to time to suit the User’s interest;</p>
                <p>&nbsp;&nbsp;2.2.4. To get in touch with the User, when necessary, especially, to inform about any information relating to User’s account and security updates;</p>
                <p>&nbsp;&nbsp;2.2.5. To provide the Application and the functions and features therein efficiently;</p>
                <p>&nbsp;&nbsp;2.2.6. To preserve social history as governed by existing law or policy; and</p>
                <p>&nbsp;&nbsp;2.2.7. For promotional and marketing purposes, either through itself or through a third party.</p>
                <p>&nbsp;&nbsp;2.2.8. We also put together data from the information we already have about you, so we can offer and suggest a variety of services and features. We may put together your current city with GPS and other location information we have about you to, for example, tell you about people or events nearby, or offer deals to you in which you might be interested. We may also put together data about you to serve you ads or other content that might be more relevant to you.</p>
                <p>&nbsp;&nbsp;2.2.9. While you are allowing us to use the information we receive about you, you always own all of your information. Your trust is important to us, which is why we don't share information we receive about you with others unless we have:</p>
                <ul>
                    <li><p>received your permission;</p></li>
                    <li><p>given you notice, such as by telling you about it in this policy;</p></li>
                </ul>
                <p>You may receive telephone or text messages from us or our partners, with information on new products and services or upcoming events, WiFi Offers, and deals and discounts, which we think you may be interested in. You hereby authorize us and our partners to send SMS/Voice calls/Notifications to mobile number, even if the number is registered for DND “Do not Disturb” service. If you do not wish to receive such telephone calls or text messages, please let us know by sending us an email at <a href="mailto:<EMAIL>" target="_blank"> <EMAIL>.</a></p>
                <li><h5 style="color: #227e9c">SHARING OF INFORMATION</h5></li>
                <p>3.1. Linq will not use User information for any purpose other than in connection with the Application and the purposes set out in Clause 2.2 above.</p>
                <p>3.2. Linq will not rent, sell or share User information and will not disclose any of the User’s personally identifiable information to third parties, unless:</p>
                <p>&nbsp;&nbsp;3.2.1. it is to help investigate, prevent or take action regarding unlawful and illegal activities; suspected fraud, potential threat to the safety or security of any person, violations of Linq’s terms of use or to defend against legal claims;</p>
                <p>&nbsp;&nbsp;3.2.2. it is a case of special circumstances such as compliance with court orders, requests/order, notices from legal authorities or law enforcement agencies compel us to make such disclosure; and</p>
                <p>&nbsp;&nbsp;3.2.3. it forms part of the information Linq shares with advertisers on an aggregate basis.</p>
                <li><h5 style="color: #227e9c">INFORMATION SECURITY</h5></li>
                <p>4.1. The information provided by the User is stored in access controlled facilities with restricted access. User Information transmitted over the internet is protected through the use of encryption, using the Secure Socket Layer (SSL) protocol.</p>
                <p>4.2. If a password is used to help protect User’s accounts and information contained therein, it is the responsibility of the User to keep the password confidential. The User has to ensure that he/she always logs out, before sharing the mobile device with a third party and it is advised that the User utilizes a service to protect access to the User’s mobile device.</p>
                <li><h5 style="color: #227e9c">PROCEDURE FOR REMOVING USER NAME FROM THE RECORDS</h5></li>
                <p>5.1. If and when a User is desirous of having his/her name and other details removed from the records of Linq, immediately upon receiving the User’s written request to that effect Linq shall remove and/delete all such information.</p>
                <li><h5 style="color: #227e9c">PROCEDURE FOR CORRECTING INACCURACIES IN THE INFORMATION</h5></li>
                <p>6.1. The User may correct or update any information online. In the event of loss of access details, the User may retrieve the same or receive new access details by using any of the following options:</p>
                <p>&nbsp;&nbsp;6.1.1. Send an e-mail to: <a class="text-primary" href="mailto:<EMAIL>" target="_blank"><EMAIL></a></p>
                <li><h5 style="color: #227e9c">POLICY UPDATES</h5></li>
                <p>7.1. Linq reserves the right to change or update this policy at any time. Such changes shall be effective immediately upon posting on <a class="text-primary" href="index.html" target="_blank">www.getlinq.in</a></p>
            </ol>
        </div>
    </div>
    <footer id="grad3">
        <div class="container">
            <div class="row">
                <div class="col-md-5 col-sm-12 col-12 pt-3">
                    <p><a href="index.html">Linq</a>&nbsp;&nbsp;&nbsp;Copyright &copy 2018&nbsp;&nbsp;|&nbsp;&nbsp;All Rights Reserved</p>
                </div>
                <div class="col-md-4 offset-md-3 col-sm-12 col-12 pt-3">
                    <p class="footrt"><a href="faq.html" target="_blank">FAQs</a>&nbsp;&nbsp;&nbsp;&nbsp;<a href="mailto:<EMAIL>" target="_blank">Contact Us</a>&nbsp;&nbsp;&nbsp;&nbsp;<a class="active" href="privacy.html" target="_blank">Privacy Policy</a>&nbsp;&nbsp;&nbsp;&nbsp;<a href="map.html" target="_blank">WiFi Locator</a></p>
                </div>
            </div>
        </div>
    </footer>`);