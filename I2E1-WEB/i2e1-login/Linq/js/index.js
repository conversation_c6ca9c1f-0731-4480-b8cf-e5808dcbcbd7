let scanner = new Instascan.Scanner({ video: document.getElementById('preview') });
// scanner.addListener('scan', function (content) {
//  // alert(content);
// });
scanner.addListener('scan',getResult);
Instascan.Camera.getCameras().then(function (cameras) {
  if (cameras.length > 0) {
    //scanner.start(cameras[0]);
    document.getElementById('preview').style="";
     scanner.start(cameras[cameras.length-1]);
  } else {
    console.error('No cameras found.');
  }
}).catch(function (e) {
  console.error(e);
});

function getResult(content){
   // debugger;
    //let split=content.split('/')[3].substring(0,10);
    //document.getElementById("qr-result-data").value+=split
    var regexQuery = "/(http(s)?://.)?(www\.)?[-a-zA-Z0-9@:%._\+~#=]{2,256}\.[a-z]{2,6}\b([-a-zA-Z0-9@:%_\+.~#?&//=]*)/";
    var url = new RegExp(regexQuery,"g");
    if (url.test(content) && content.search("linq.app")===-1?false:true) {
          window.location.href=content;
    }
    else{
      alert("Qr code is not valid");
    }

}