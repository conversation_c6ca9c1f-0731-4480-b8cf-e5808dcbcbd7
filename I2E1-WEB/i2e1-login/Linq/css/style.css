body{
    margin:0px;
    font-family: 'Lucida Sans', 'Lucida Sans Regular', 'Lucida Grande', 'Lucida Sans Unicode', Geneva, Verdana, sans-serif
}
.container{
    display: flex;
    justify-content: center;
    background: black;
}
.qr-infomation{
   font-size: 16px;
   font-weight: bold; 
   border-bottom: 1px solid blue;
   color:white;
   display: flex;
   height: 30px;
   padding: 10px;

}
.qr-cemra{
max-width: 720px;
width:100%;    
}
.qr-result{
    display: flex;
    justify-content: center;
    position: fixed;
    bottom: 0px;
    width:100%;
    height: 100px;
    border-radius: 20px;
    background: white;
    border-radius:10px 10px 0px 0px;
    margin-top: 10px;
    
}
.qr-container{
    background: black;
}
#preview{
   
    width: 100%;
    height: 100%;
  
    object-fit: fill;
    
    
}
.qr-text{
    margin-left: 20px;
}
#qr-result-data{
    padding-left: 95px;
    margin: 10px;
    width:100%;
   
    height: 100%;
    border:none;
}
.pre-define-text{
    color: blue;
    position: absolute;
    left:35px;;
    top:50%;
}
.send-button{
    position: absolute;
    cursor: pointer;
    right: 15px;
    top:15px;
    height: 30px;
    width: 30px;
    background-image: url("/image/send.png");
    background-size: cover;
}
.qr-line{
    position: absolute;
    border: 3px solid gray;

    -webkit-animation-name: example; /* Safari 4.0 - 8.0 */
    -webkit-animation-duration: 4s; /* Safari 4.0 - 8.0 */
    -webkit-animation-delay: 2s; /* Safari 4.0 - 8.0 */
    animation-name: example;
    animation-duration: 4s;
    animation-delay: 2s;
    width:100%;
    z-index: 2;
    animation-iteration-count:infinite;
}
@-webkit-keyframes example {
    0%   { top:0px;}
    25%  {  top:50%;}
    50%  { top:100%;}
    75%  { top:50%;}
    100% { top:0%;}
  }
  
  /* Standard syntax */
  @keyframes example {
    0%   { top:0px;}
    25%  {  top:50%;}
    50%  { top:100%;}
    75%  { top:50%;}
    100% { top:0%;}
  }
@media only screen and (max-width: 600px) {
    .container{
        width:100%;
    }
  }
