@font-face {
    font-family: 'Roboto';
    src: url('https://www.i2e1.in/jsLibs/fonts/Roboto/Roboto-Regular-webfont.eot');
    src: url('https://www.i2e1.in/jsLibs/fonts/Roboto/Roboto-Regular-webfont.eot?#iefix') format('embedded-opentype'), url('https://www.i2e1.in/jsLibs/fonts/Roboto/Roboto-Regular-webfont.woff') format('woff'), url('https://www.i2e1.in/jsLibs/fonts/Roboto/Roboto-Regular-webfont.ttf') format('truetype'), url('https://www.i2e1.in/jsLibs/fonts/Roboto/Roboto-Regular-webfont.svg#RobotoRegular') format('svg');
    font-weight: normal;
    font-style: normal;
}

body {
    font-family: 'Roboto', sans-serif;
}

.row {
    margin-right: 0px;
}

/* Download button effects start*/

.btn {
    border-radius: 28px;
    background-color: rgb(29 161 242);
    -webkit-background-color: rgb(29 161 242);
    -moz-background-color: rgb(29 161 242);
    -o-background-color: rgb(29 161 242);
    /* background-image: linear-gradient(to right, rgb(40 143 124), rgb(28 112 184)); */
    color: WHITE;
    width: 164px;
    padding-top: 10px;
    font-size: 16px;
    margin: 3% auto;
    box-shadow: 0 0 20px #eee;
    -webkit-box-shadow: 0 1px 3px rgba(0, 0, 0, 0.12), 0 1px 2px rgba(0, 0, 0, 0.24);
    -moz-box-shadow: 0 1px 3px rgba(0, 0, 0, 0.12), 0 1px 2px rgba(0, 0, 0, 0.24);
    -o-box-shadow: 0 1px 3px rgba(0, 0, 0, 0.12), 0 1px 2px rgba(0, 0, 0, 0.24);
}

.short-text {
    display: none;
}

.btn:hover {
    color: #d7dde5;
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.19), 0 6px 6px rgba(0, 0, 0, 0.23);
    background-color: rgb(29 135 199);
    -webkit-background-color: rgb(29 135 199);
    -moz-background-color: rgb(29 135 199);
    -o-background-color: rgb(29 135 199);
}

/* Download button effects end*/

#grad1 {
    max-width: 100%;
    max-height: 138px;
}

#grad2 {
    background-color: #f9f9f9;
    -webkit-background-color: #f9f9f9;
    -moz-background-color: #f9f9f9;
    -o-background-color: #f9f9f9;
}

#grad3 {
    background-color: rgb(29 161 242);
    -webkit-background-color: rgb(29 161 242);
    -moz-background-color: rgb(29 161 242);
    -o-background-color: rgb(29 161 242);
    /* background: linear-gradient(to right, rgb(40 143 124), rgb(28 112 184)); */
    /* -webkit-background: linear-gradient(to right, rgb(40 143 124), rgb(28 112 184)); */
    width: 100%;
}

/* hero image start*/

#hero {
    background-image: url(assets/banner_image.jpg);
    background-size: cover;
    position: relative;
    background-position: center;
    background-repeat: no-repeat;
    height: 70vh;
    box-shadow: inset 0 0 10px rgba(0, 0, 0, .7);
    /*  box-shadow: inset 0px -9px 6px -6px #888888;*/
}

.header {
    position: absolute;
    top: 50%;
    text-align: center;
    width: 100%;
    color: #fff;
    -ms-transform: translate(0, -50%);
    /* IE 9 */
    -webkit-transform: translate(0, -50%);
    /* Safari */
    transform: translate(0, -50%);
}

/* hero image end*/

/*hero image overlay effect start*/

#color-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: black;
    opacity: 0.5;
}

/*hero image overlay effect end*/

.mr-5, .mx-5 {
    margin-right: 0 !important;
}

.hero-head {
    text-align: left;
    margin-left: 4%;
    margin-right: 12%;
    font-size: 35px;
}

.hero-body {
    text-align: justify;
    margin-left: 4%;
    margin-right: 12%;
    font-size: 20px;
    line-height: 30px;
}


.logo-text {
    width: 171px;
    height: 48px;
    margin-top: 3%;
    margin-bottom: 3%;
    margin-left: 10%
}

.headernav {
    padding-top: 10px;
    padding-bottom: 10px;
}

.dots {
    background-image: url(assets/Statistics_linq_WifiHotspots_India.svg);
    background-repeat: no-repeat;
    max-width: 100%;
    max-height: 100%;
    background-size: contain;
    padding-bottom: 20%;
    padding-top: 8%;
    margin-left: 5%;
}

.category {
    max-width: 100%;
    height: 30%;
    background-image: url(assets/Statistics2_linq_WifiHotspots_India.svg);
    background-repeat: no-repeat;
    background-size: contain;
    margin-left: 5%;
    margin-bottom: 20%;
}

.screens {
    margin-top: 7%;
}

.screen1 {
    background-image: url(assets/Carousel1_linq_Free_WifiHotspots_India.png);
    background-size: contain;
    background-repeat: no-repeat;
    max-width: 275px;
    height: 490px;
}

.screen2 {
    background-image: url(assets/Carousel2_linq_Free_WifiHotspots_India.png);
    background-size: contain;
    background-repeat: no-repeat;
    max-width: 275px;
    height: 490px;
    color: #1da1f2;
}

.screen3 {
    background-image: url(assets/Carousel3_linq_Free_WifiHotspots_India.png);
    background-size: contain;
    background-repeat: no-repeat;
    max-width: 275px;
    height: 490px;
}

.screen4 {
    background-image: url(assets/Carousel4_linq_Free_WifiHotspots_India.png);
    background-size: contain;
    background-repeat: no-repeat;
    max-width: 275px;
    height: 490px;
    color: #1da1f2;
}

.screen-head {
    padding-top: 10%
}

.screen-text {
    margin: 4% 10%;
}

.zoom {
    transition: transform .2s;
}

    .zoom:hover {
        transform: scale(1.1);
    }

.section {
    margin-top: 3%;
    min-height: 400px;
}


.head-content {
    margin: 11% 24% 5% 0%;
    color: #268a86;
}

.shadow {
    -webkit-box-shadow: 0 8px 6px -6px black;
    -moz-box-shadow: 0 8px 6px -6px black;
    box-shadow: 0 8px 6px -6px black;
}

footer {
    font-size: 14px;
    color: white;
}

a {
    color: white;
}

    a:hover {
        color: #d7dde5;
        text-decoration: none;
    }

/*--------------------------------------media query------------------------------------------*/

@media only screen and (max-width: 767px) {

    #hero {
        background-image: url(assets/banner_image-small1.jpg);
        background-size: cover;
        position: relative;
        background-position: center;
        background-repeat: no-repeat;
        height: 70vh;
        max-width: 100%;
        box-shadow: inset 0 0 10px rgba(0, 0, 0, .7);
    }

    .logo-text {
        width: 171px;
        height: 48px;
        margin-top: 3%;
        margin-bottom: 3%;
        margin-left: 0;
    }

    .col-12 {
        padding-right: 0;
    }

    .hero-head {
        margin: 0;
        text-align: center;
        font-size: 32px;
        margin-left: 2%;
        margin-right: 2%;
    }

    .hero-body {
        margin: 0;
        text-align: center;
        font-size: 16px;
    }

    .btn {
        border-radius: 28px;
        background-color: rgb(29 161 242);
        -webkit-background-color: rgb(29 161 242);
        -moz-background-color: rgb(29 161 242);
        -o-background-color: rgb(29 161 242);
        color: white;
        width: 120px;
        padding-top: 8px;
        font-size: 16px;
        margin: 6% 0% 6% 20%;
        height: 40px;
    }

    .full-text {
        display: none;
    }

    .short-text {
        display: inline-block;
    }

    .know-more {
        margin: 15% auto;
        height: 50%;
    }

    .dots {
        padding: 0%;
        margin: 0%;
        display: none;
    }

    .category {
        padding: 0%;
        margin: 0%;
        display: none;
    }

    .mockup-content {
        display: none;
        padding: 0%;
        margin: 0%;
    }

    .content-head {
        text-align: center;
    }

    .content-body {
        display: none;
        margin: 0%;
        padding: 0%;
    }

    .screen1, .screen2, .screen3, .screen4 {
        height: 360px;
    }

    .screens {
        margin-top: 0%;
        padding-top: 0;
        max-width: 275px;
        height: 490px;
        background-image: url(assets/Linq_Wifi_App_Mobileview.gif);
        background-repeat: no-repeat;
        background-size: contain;
        /*        background-image: url(assets/carousel1.png);
        -webkit-animation-name: screenshot;  /*Safari 4.0 - 8.0*/
        /*        -webkit-animation-duration: 5s;  /*Safari 4.0 - 8.0*/
        /*        animation-duration: 20s;
        animation-timing-function: linear;
        animation-iteration-count: infinite; */
    }

    /* Safari 4.0 - 8.0 */
    /*    @-webkit-keyframes screenshot {
        0%   {background-image: url(assets/carousel1.png);}
        25%  {background-image:url(assets/carousel2.png);}
        50%  {background-image:url(assets/carousel3.png
        100% {background-image:url(assets/carousel4.png);}
    } */
    /* Standard syntax */
    /*    @keyframes screenshot {
        0%   {background-image: url(assets/carousel1.png);}
        25%  {background-image:url(assets/carousel2.png);}
        50%  {background-image:url(assets/carousel3.png);}
        100% {background-image:url(assets/carousel4.png);}
    } */
    .screen1,
    .screen2,
    .screen3,
    .screen4 {
        display: none;
    }

    /*
    .video{
        padding-right: 5%;
    }
*/
    .video-head {
        margin-top: 15%;
        text-align: center;
        padding-left: 3%;
        margin-right: 0;
    }

    .downloads {
        margin-top: 20%;
        margin-bottom: 15%;
        text-align: center;
    }

    footer {
        text-align: center;
        max-height: 100%;
    }
}
