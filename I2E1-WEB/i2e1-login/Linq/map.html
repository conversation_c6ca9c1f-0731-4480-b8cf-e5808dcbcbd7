<!Doctype HTML>
<html lang="en">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    <meta name="description" content="World’s leading Free Wi-Fi connect and internet sharing App which connects your phone to the nearest WiFi hotspot from a WiFi Map of 10,000+ hotspot devices.">
    <meta name="keywords" content="Free Wi-Fi,Free Wi-Fi Connect,high speed internet,Wi-Fi hotspots,portable hotspot,Free Wi-Fi Hotspots,free wifi connect without password,Free WiFi Map">
    <meta name="author" content="Abhishek">

    <!-- Bootstrap CSS -->
    <link rel="stylesheet" href="css/bootstrap.min.css">
    <link rel="stylesheet" type="text/css" href="css/main.css">
    <link href="https://fonts.googleapis.com/css?family=Roboto" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css?family=Roboto:300,300i,400,500" rel="stylesheet">

    <title>Enjoy Free WiFi wherever you go: Download Linq</title>
    <link href="assets/linq_app_website_browser_icon.png" rel="shortcut icon" type="image/x-icon">
    <script src="js/jquery-3.3.1.min.js"></script>
    <script async src="//pagead2.googlesyndication.com/pagead/js/adsbygoogle.js"></script>
    <script>
	(adsbygoogle = window.adsbygoogle || []).push({
	google_ad_client: "ca-pub-5187478381316162",
	enable_page_level_ads: true
	});
    </script>
    <style>
        /* Always set the map height explicitly to define the size of the div
             * element that contains the map. */
        #map {
            height: 100%;
        }
        /* Optional: Makes the sample page fill the window. */
        body {
            height: 95%;
            width: 100%;
        }

        html {
            height: 95%;
            width: 100%;
            /* padding: 1%;  */
        }

        #description {
            font-family: Roboto;
            font-size: 15px;
            font-weight: 300;
        }

        #infowindow-content .title {
            font-weight: bold;
        }

        #infowindow-content {
            display: none;
        }

        #map #infowindow-content {
            display: inline;
        }

        .pac-card {
            margin: 10px 10px 0 0;
            border-radius: 2px 0 0 2px;
            box-sizing: border-box;
            -moz-box-sizing: border-box;
            outline: none;
            /* box-shadow: 0 2px 6px rgba(0, 0, 0, 0.5); */
            background-color: #fff;
            font-family: Roboto;
        }

        #pac-container {
            padding-bottom: 12px;
            margin-right: 12px;
        }

        .pac-controls {
            display: inline-block;
            padding: 5px 11px;
        }

        .pac-controls label {
            font-family: Roboto;
            font-size: 13px;
            font-weight: 300;
        }

        #pac-input {
            font-family: Roboto;
            font-size: 18px;
            font-weight: 200;
            margin-left: 12px;
            padding: 0 11px 0 13px;
            text-overflow: ellipsis;
            width: 300px;
            height: 30px;
            box-shadow: 3px 3px #cccccc;
            border-color: #000;
        }

        #pac-input:focus {
            border-color: #000;
        }

        #title {
            color: #fff;
            background-color: #000000;
            font-size: 25px;
            font-weight: 500;
            padding: 6px 12px;
        }

        #target {
            width: 345px;
        }
    </style>

</head>
<body>
    <header id="grad1">
        <!--<div class="container">-->
        <div class="row">
            <div class="col-md-3 text-center col-sm-6 col-6">
                <div>
                    <a href="index.html"><img style="font-family: Roboto" src="assets/ic_linq_logo.svg" class="logo-text" alt="India’s Leading App for Free WiFi without password from a WiFi Map of 1 million+ hotspots" /></a>
                </div>
            </div>
            <div class="col-md-3 offset-md-6 col-sm-6 col-6 text-center">
                <a href="https://play.google.com/store/apps/details?id=com.i2e1.swapp" class="btn btn-lg" role="button" aria-pressed="true" target="_blank">
                    <img src="assets/playstore.png" style="width: 24px;" alt="Logo of Google Play store" /><span class="full-text"> &nbsp;Download</span>
                    <span class="short-text">Get Now</span>
                </a>
            </div>
        </div>
        <!--</div>-->
    </header>
    <input id="pac-input" class="controls" type="text" placeholder="Search Google Maps" autofocus="autofocus">
    <div id="map"></div>
    <script>
        var map;
        function initMap() {
            map = new google.maps.Map(document.getElementById('map'), {
              center: {lat: 28.63145, lng: 77.21667},
              zoom: 12
            });

        // Create the search box and link it to the UI element.
        var input = document.getElementById('pac-input');
        var searchBox = new google.maps.places.SearchBox(input);
        map.controls[google.maps.ControlPosition.LEFT_TOP].push(input);

        // Bias the SearchBox results towards current map's viewport.
        map.addListener('bounds_changed', function() {
          searchBox.setBounds(map.getBounds());
        });

        var markers = [];
        // Listen for the event fired when the user selects a prediction and retrieve
        // more details for that place.
        searchBox.addListener('places_changed', function() {
          var places = searchBox.getPlaces();

          if (places.length == 0) {
            return;
          }

          // Clear out the old markers.
          markers.forEach(function(marker) {
            marker.setMap(null);
          });
          markers = [];

          // For each place, get the icon, name and location.
          var bounds = new google.maps.LatLngBounds();
          places.forEach(function(place) {
            if (!place.geometry) {
              console.log("Returned place contains no geometry");
              return;
            }
            var icon = {
              url: place.icon,
              size: new google.maps.Size(71, 71),
              origin: new google.maps.Point(0, 0),
              anchor: new google.maps.Point(17, 34),
              scaledSize: new google.maps.Size(25, 25)
            };

            // Create a marker for each place.
            markers.push(new google.maps.Marker({
              map: map,
              icon: icon,
              title: place.name,
              position: place.geometry.location
            }));

            if (place.geometry.viewport) {
              // Only geocodes have viewport.
              bounds.union(place.geometry.viewport);
            } else {
              bounds.extend(place.geometry.location);
            }
            });
            map.fitBounds(bounds);
            map.setZoom(12);
            });

            var contentString;

            var infowindow = new google.maps.InfoWindow({
            content: contentString
            });
            var jqxhr = $.ajax( "https://www.i2e1.in/Kelp/GetNewtorks?latitude=28.63145&longitude=77.21667&radius=4000" )
            .done(function(data) {
            data = data.data;
            var marker, i;
            var markers = new Array();
            for (i = 0; i < data.length; i++) {
            marker = new google.maps.Circle({
            center: new google.maps.LatLng(data[i].latitude, data[i].longitude),
            radius: 500,
            strokeColor: "#FFBB00",
            strokeOpacity: 1,
            strokeWeight: 1,
            fillColor: "#FF9900", //"#FFB200", //"rgb(40 143 124)",
            fillOpacity: 0.6,
            map: map
            });
            markers.push(marker);
            google.maps.event.addListener(marker, 'click', (function(marker, i) {
            return function() {
            infowindow.setContent(data[i].name);
            infowindow.open(map, marker);
            }
            })(marker, i));
            }
            })
            .fail(function(xhr, status, error) {
            var err = eval("(" + xhr.responseText + ")");
            alert(err.Message);
            });
          }
    </script>
    <script src="https://maps.googleapis.com/maps/api/js?key=AIzaSyCmilCuFcOtOQDaEtGPZlc9QvWeO5t1-yU&libraries=places&callback=initMap"
            async defer></script>

    <footer id="grad3">
        <div class="container">
            <div class="row">
                <div class="col-md-5 col-sm-12 col-12 pt-3">
                    <p><a href="index.html">Linq</a>&nbsp;&nbsp;&nbsp;Copyright &copy 2018&nbsp;&nbsp;|&nbsp;&nbsp;All Rights Reserved</p>
                </div>
                <div class="col-md-4 offset-md-3 col-sm-12 col-12 pt-3">
                    <p class="footrt"><a href="faq.html" target="_blank">FAQs</a>&nbsp;&nbsp;&nbsp;&nbsp;<a href="mailto:<EMAIL>" target="_blank">Contact Us</a>&nbsp;&nbsp;&nbsp;&nbsp;<a class="active" href="privacy.html" target="_blank">Privacy Policy</a>&nbsp;&nbsp;&nbsp;&nbsp;<a href="map.html" target="_blank">WiFi Locator</a></p>
                </div>
            </div>
        </div>
    </footer>
</body>
</html>
