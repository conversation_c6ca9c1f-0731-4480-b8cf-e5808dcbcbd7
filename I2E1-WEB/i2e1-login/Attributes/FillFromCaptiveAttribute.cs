using i2e1_basics.Utilities;
using i2e1_core.Models;
using i2e1_core.Models.Client;
using i2e1_core.Utilities;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Filters;
using System;
using System.Collections.Generic;
using System.Collections.Specialized;
using System.Web;
using UAParser;
using wiom_login_share.Models;
using wiom_login_share.Utilities;
using wiom_router_api;

namespace I2E1_WEB.Attributes;

public class FillFromCaptiveAttribute : ActionFilterAttribute
{
    Parser parser = Parser.GetDefault();
    public override void OnActionExecuting(ActionExecutingContext filterContext)
    {
        
        var queryString = filterContext.HttpContext.Request.Query;

        User user = null;
        string mac = queryString["mac"];
        
        if (string.IsNullOrEmpty(mac))
        {
            mac = queryString["ga_cmac"];
        }
        string res = queryString["res"];
        string token = queryString["token"];

        if (res == "failed")
        {
            if (token == "1234567890" || token == "WIOM_HOME")
            {
                filterContext.Result = new RedirectResult("http://172.16.2.1/");
                return;
            }
            else if (!string.IsNullOrEmpty(token))
            {
                user = CoreSessionUtils.GetLoginServerUser(filterContext.HttpContext);
                if (user != null)
                    user.res = UserState.failed;
            }
        }
        if (user == null && string.IsNullOrEmpty(mac))
        {
            string device = string.Empty;
            try
            {
                ClientInfo clientInfo = parser.Parse(filterContext.HttpContext.Request.Headers["User-Agent"]);
                device = clientInfo.OS.Family + " " + clientInfo.Device.Brand + " " + clientInfo.Device.Model;
            }
            catch
            {
            }

            string fullUrl = $"{filterContext.HttpContext.Request.Path}{filterContext.HttpContext.Request.QueryString}";
            //Logger.GetInstance().Info($"FillFromCaptiveAttribute: OnActionExecuting: NoMac {fullUrl}");

            if (fullUrl.Contains("res=notyet"))
            {
    //            var placeHolderNas = new LongIdInfo(ShardHelper.SHARD0, DBObjectType.SECONDARY_NAS, 1);
    //            var guid = Guid.NewGuid().ToString().Replace("-", string.Empty).Substring(0, 12);
				//user = new User()
				//{
				//	mac = guid,
				//	called = queryString["gw_id"].ToString().Split('_')[0],
				//	uamip = queryString["gw_address"],
				//	uamport = queryString["gw_port"],
				//	res = string.IsNullOrEmpty(queryString["res"]) ? UserState.notyet :
				//		(UserState)Enum.Parse(typeof(UserState), queryString["res"]),
				//	controllertype = ControllerType.WIFIDOG
				//};

				//user.nasid = placeHolderNas.ToString();

                PmWaniUtils.SendPMWaniLogs(filterContext.HttpContext, "sign_in_pageload", Guid.NewGuid().ToString(), new Dictionary<string, object>()
                {
                    { "dateTime", DateTime.UtcNow.ToString() },
                    { "url", fullUrl },
                    { "device", device },
                    { "OS", string.IsNullOrEmpty(device) ? string.Empty : (device.Split(" ").Length>0 ? device.Split(" ")[0]: string.Empty) }
                });
            }

            if(user == null)
            {
				filterContext.Result = new PhysicalFileResult(AppDomain.CurrentDomain.BaseDirectory + "ErrorPages/noMac.html", "text/html");
				return;
			}
        }
        else
        {
            if (user == null)
            {
                if (string.IsNullOrEmpty(queryString["nasid"]) && string.IsNullOrEmpty(queryString["gw_address"]))
                {
                    //Cambium
                    if (!string.IsNullOrEmpty(queryString["ga_nas_id"]))
                    {
                        user = generateCambiumUser(mac, queryString);
                        user.attributes.Add("query", filterContext.HttpContext.Request.QueryString.ToString());
                    }
                    //Aruba
                    else if (string.IsNullOrEmpty(queryString["client_mac"]))
                    {
                        user = generateArubaUser(mac, queryString);
                    }
                    //Rucus
                    else
                    {
                        user = generateRuckusUser(mac, queryString);
                    }
                }
                else if (!string.IsNullOrEmpty(queryString["controllertype"]))
                {
                    var controller = queryString["controllertype"];
                    ControllerType controllerType = string.IsNullOrEmpty(controller) ? ControllerType.I2E1 : (ControllerType)Enum.Parse(typeof(ControllerType), controller);
                    switch (controllerType)
                    {
                        case ControllerType.HFCL:
                            user = new User()
                            {
                                mac = mac,
                                called = queryString["gw_mac"],
                                uamip = queryString["gw_address"],
                                uamport = queryString["gw_port"],
                                res = string.IsNullOrEmpty(queryString["res"]) ? UserState.notyet :
                        (UserState)Enum.Parse(typeof(UserState), queryString["res"]),
                                nasid = queryString["gw_id"],
                                controllertype = controllerType
                            };
                            break;
                        default:
                            user = new User()
                            {
                                mac = mac,
                                called = queryString["called"],
                                uamip = queryString["uamip"],
                                uamport = queryString["uamport"],
                                challenge = queryString["challenge"],
                                res = string.IsNullOrEmpty(queryString["res"]) ? UserState.notyet :
                        (UserState)Enum.Parse(typeof(UserState), queryString["res"]),
                                nasid = queryString["nasid"],
                                controllertype = controllerType
                            };
                            break;
                    }

                }
                else if (!string.IsNullOrEmpty(queryString["gw_address"]))
                {
                    user = new User()
                    {
                        mac = mac,
                        called = queryString["gw_id"].ToString().Split('_')[0],
                        uamip = queryString["gw_address"],
                        uamport = queryString["gw_port"],
                        res = string.IsNullOrEmpty(queryString["res"]) ? UserState.notyet :
                        (UserState)Enum.Parse(typeof(UserState), queryString["res"]),
                        controllertype = ControllerType.WIFIDOG
                    };
                    string ip = queryString["ip"];
                    if (ip != null && ip.StartsWith("172.16.3."))
                    {
                        var nasSplitTemplate = RouterApiClient.GetInstance().GetNasDetailsFromMacAsync(queryString["gw_id"]).Result;
                        user.nasid = nasSplitTemplate.secondaryNas.ToString();
                        user.deviceId = nasSplitTemplate.deviceId;
                    }
                    else
                    {
                        user.nasid = queryString["gw_id"];
                    }
                }
                else
                {
                    var controllerType = queryString["controllertype"];
                    var ctrlType = string.IsNullOrEmpty(controllerType) ? ControllerType.I2E1 : (ControllerType)int.Parse(controllerType);
                    var responseState = string.IsNullOrEmpty(queryString["res"]) ? UserState.notyet :
                        (UserState)Enum.Parse(typeof(UserState), queryString["res"]);

                    if (ctrlType == ControllerType.I2E1 &&
                        responseState == UserState.success &&
                        !string.IsNullOrEmpty(queryString["userurl"]))
                    {
                        filterContext.Result = new RedirectResult(queryString["userurl"], false);
                        return;
                    }
                    user = new User()
                    {
                        mac = mac,
                        called = queryString["called"],
                        uamip = queryString["uamip"],
                        uamport = queryString["uamport"],
                        challenge = queryString["challenge"],
                        res = responseState,
                        nasid = queryString["nasid"],
                        controllertype = ctrlType
                    };
                }
            }
        }
        FillParametersForUser(filterContext, user);
    }

    private void FillParametersForUser(ActionExecutingContext filterContext, User user)
    {
		string loginUserSession = filterContext.HttpContext.Request.Headers["login-user-session"];
		if (loginUserSession == null)
			filterContext.HttpContext.Request.Headers.Add("login-user-session", user.GetToken());

		var response = user.res;
		var previousSession = CoreSessionUtils.GetLoginServerUser(filterContext.HttpContext);
		if (previousSession != null && (user.res == UserState.notyet || user.res == UserState.success || user.res == UserState.already)
		&& previousSession.backEndNasid == user.backEndNasid && previousSession.mac == user.mac && user.controllertype == previousSession.controllertype)
		{
			user = previousSession;
			user.res = response;
		}

		var routerConfig = CoreCacheHelper.GetInstance().GetRouterBasic(user.backEndNasid);
		user.combinedSettingId = routerConfig.combinedSettingId;
		user.storegroupid = routerConfig.storeGroupId;
		user.templateid = new System.Collections.Generic.List<int>() { 0 };
		user.clientAuthType = AuthType.PHONE;

		var dataVoucherDetails = CoreCacheHelper.GetInstance().GetAdvanceCongifInCombinedSetting(user.combinedSettingId, AdvanceConfigType.DATA_VOUCHER_DETAILS);
		if (!String.IsNullOrEmpty(dataVoucherDetails.GetJoinedParameters()))
		{
			if (dataVoucherDetails.parameters.Length >= 1) user.attributes["dataVoucherPlaceholder"] = dataVoucherDetails.parameters[0];
			if (dataVoucherDetails.parameters.Length >= 2) user.attributes["freeDataPlan"] = dataVoucherDetails.parameters[1];
			if (dataVoucherDetails.parameters.Length >= 3) user.attributes["freeSessionTime"] = dataVoucherDetails.parameters[2];
		}

		switch (user.clientAuthType)
		{
			case AuthType.DATA_VOUCHER:
				if (dataVoucherDetails != null && dataVoucherDetails.parameters.Length >= 3)
				{
					user.askaccesscode = !(!string.IsNullOrEmpty(dataVoucherDetails.parameters[1]) && dataVoucherDetails.parameters[1] != "0" &&
							!string.IsNullOrEmpty(dataVoucherDetails.parameters[2]) && dataVoucherDetails.parameters[2] != "0");
				}
				else if (dataVoucherDetails != null && dataVoucherDetails.parameters.Length == 1)
				{
					user.askaccesscode = true;
				}
				break;
			default:
				user.askaccesscode = false;
				break;
		}
		user.otp = "1234";

		string url = filterContext.HttpContext.Request.Query["url"];
		if (user.storegroupid == 1 && url != null && url.Contains("pmwifi.in"))
		{
			Dictionary<string, string> parameters = GetUrlParameters(url);
			if (parameters != null)
			{
                if(parameters.ContainsKey("uniqueIdentifier"))
				    user.unique_identifier = parameters["uniqueIdentifier"] ?? user.unique_identifier;
                if (parameters.ContainsKey("uniqueplankey"))
                    user.attributes["uniqueplankey"] = parameters["uniqueplankey"];
                if (parameters.ContainsKey("newFlow"))
                {
                    user.attributes["newFlow"] = parameters["newFlow"];
                    if (parameters.ContainsKey("planId"))
                        user.attributes["planId"] = parameters["planId"];
                    if (parameters.ContainsKey("orderId"))
                        user.attributes["orderId"] = parameters["orderId"];
                    if (parameters.ContainsKey("dynamicintentlink"))
                        user.attributes["dynamicintentlink"] = parameters["dynamicintentlink"];
                }
            }
		}
		CoreSessionUtils.SetLoginServerUser(user, filterContext.HttpContext);
		try
		{
			ClientInfo clientInfo = parser.Parse(filterContext.HttpContext.Request.Headers["User-Agent"]);
			user.device = clientInfo.OS.Family + " " + clientInfo.Device.Brand + " " + clientInfo.Device.Model;
		}
		catch (Exception ex)
		{
			//Logger.GetInstance().Error(ex.ToString());
		}

		filterContext.ActionArguments["user"] = user;

		/*if (user.storegroupid == 1)
		{
            string ua = filterContext.HttpContext.Request.Headers["User-Agent"];
            PmWaniUtils.SendPMWaniLogs(filterContext.HttpContext, "click_on_wifi_ss", user.unique_identifier, new System.Collections.Generic.Dictionary<string, object>()
				{
					{ "dateTime", DateTime.UtcNow.ToString() },
					{ "mac_id", user.mac },
				    { "device", user.device },
				    { "routerMac", string.IsNullOrEmpty(user.called) ? "null" : user.called},
				    { "deviceId", user.deviceId},
                    { "ua", ua }
			});
		}*/
	}

	private User generateArubaUser(string mac, IQueryCollection queryString)
	{
		return new User()
		{
			mac = mac,
            called = HttpUtility.UrlDecode(queryString["apmac"]),
            uamip = queryString["switchip"],
            uamport = string.Empty,
            res = UserState.notyet,
            nasid = "2568",
            controllertype = ControllerType.ARUBA
        };
    }

    private User generateRuckusUser(string mac, IQueryCollection queryString)
    {
        return new User()
        {
            mac = sanitisemac(queryString["client_mac"]),
            called = sanitisemac(queryString["mac"]),
            uamip = queryString["sip"],
            clientip = queryString["uip"],
            uamport = string.Empty,
            res = string.IsNullOrEmpty(queryString["res"]) ? UserState.notyet :
                        (UserState)Enum.Parse(typeof(UserState), queryString["res"]),
            nasid = queryString["lid"],
            controllertype = ControllerType.RUCKUS
        };
    }

    private static string sanitisemac(string mac)
    {
        if (mac.IndexOf(":") > -1 || mac.IndexOf("-") > -1)
            return mac;
        string macAddStrNew = mac;
        int insertedCount = 0;
        for (int i = 2; i < mac.Length; i = i + 2)
            macAddStrNew = macAddStrNew.Insert(i + insertedCount++, ":");
        return macAddStrNew;

    }

    private User generateCambiumUser(string mac, IQueryCollection queryString)
    {
        return new User()
        {
            mac = queryString["ga_cmac"],
            called = queryString["ga_ap_mac"],
            uamip = queryString["ga_srvr"],
            uamport = "880",
            res = UserState.notyet,
            nasid = queryString["ga_nas_id"],
            controllertype = ControllerType.CAMBIUM
        };
    }
    private static Dictionary<string, string> GetUrlParameters(string url)
    {
        try
        {
            Uri uri = new Uri(url);
            string query = uri.Query;

            NameValueCollection queryParameters = HttpUtility.ParseQueryString(query);

            Dictionary<string, string> parameters = new Dictionary<string, string>();

            foreach (string key in queryParameters)
            {
                if (key != null)
                {
                    parameters[key] = queryParameters[key];
                }
            }

            return parameters;
        }
        catch (Exception ex) { 
            Logger.GetInstance().Error("GetUrlParameters" + ex.ToString());
            return null; 
        }
    }
}
