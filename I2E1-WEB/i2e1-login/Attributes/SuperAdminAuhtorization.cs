using i2e1_basics.Models;
using i2e1_core.Models;
using i2e1_core.Utilities;
using I2E1_WEB.Models;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Filters;

namespace I2E1_WEB.Attributes;

public class SuperAdminAuthorizationAttribute : TypeFilterAttribute
{
    public SuperAdminAuthorizationAttribute() : base(typeof(SuperAdminAuthorization))
    {
    }
}

public class SuperAdminAuthorization : IAuthorizationFilter
{
    public void OnAuthorization(AuthorizationFilterContext filterContext)
    {
        JwtObject jwtObject = JWTManager.Authenticate(filterContext, out ResponseStatus responseStatus);

        if (jwtObject == null)
        {
            if (responseStatus == ResponseStatus.LOGOUT)
                filterContext.Result = new JsonResult(new ErrorResponse(ResponseStatus.LOGOUT, ErrorCodes.LOGOUT));
            else if (responseStatus == ResponseStatus.UPDATE)
                filterContext.Result = new JsonResult(new ErrorResponse(ResponseStatus.UPDATE, ErrorCodes.UPDATE_APP));
            else
                filterContext.Result = new JsonResult(ErrorCodes.NOT_LOGGED_IN);
        }
        else if(jwtObject.userType == AdminUserType.STANDARD)
        {
            filterContext.Result = new JsonResult(ErrorCodes.NOT_LOGGED_IN);
        }
    }
}
