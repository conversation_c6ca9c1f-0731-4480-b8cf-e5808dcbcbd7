using i2e1_basics.Utilities;
using Microsoft.AspNetCore.Mvc.Filters;
using System.IO;
using System.Text;

namespace I2E1_WEB.Attributes;

public class ApiExceptionFilterAttribute : ExceptionFilterAttribute
{
    public override void OnException(ExceptionContext context)
    {
        var exception = context.Exception;
        if (exception != null)
        {
            Logger.GetInstance().Error(exception.ToString());
            context.HttpContext.Response.StatusCode = 500;
            MemoryStream myMemoryStream = new MemoryStream(Encoding.ASCII.GetBytes("Something wrong happened"));
            Stream myStream = myMemoryStream;
            context.HttpContext.Response.Body = myStream;
        }
    }
}