using i2e1_core.Models;
using i2e1_core.Utilities;
using I2E1_WEB.Models;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Filters;
using Newtonsoft.Json;
using System.Net.Http;

namespace I2E1_WEB.Attributes;

public class ApiAuthorizationAttribute : TypeFilterAttribute
{
    public ApiAuthorizationAttribute() : base(typeof(ApiAuthorization))
    {
    }
}

public class ApiAuthorization : IAuthorizationFilter
{
    public void OnAuthorization(AuthorizationFilterContext filterContext)
    {
        string token = filterContext.HttpContext.Request.Headers["i2e1-token"];
        if (token != null)
        {
            filterContext.HttpContext.Items["i2e1-token"] = token;
        }
        else
        {
            filterContext.Result = new JsonResult(ErrorCodes.NOT_LOGGED_IN);
        }
    }
}
