using i2e1_basics.Utilities;
using i2e1_core.Utilities;
using Microsoft.AspNetCore.Mvc.Filters;

namespace I2E1_WEB.Attributes;

public class ApiLoggerFilter : ActionFilterAttribute
{
    public override void OnActionExecuting(ActionExecutingContext actionContext)
    {
        var verb = actionContext.HttpContext.Request.Method;
        string mobile = "";
        var queryString = actionContext.ActionArguments;
        if(queryString.ContainsKey("mobile"))
               mobile = queryString["mobile"].ToString();
        if (queryString.ContainsKey("appId"))
            mobile =mobile +"_" +queryString["appId"].ToString();

        actionContext.ActionDescriptor.Properties.Add("mobile", mobile);
        string actionName = actionContext.ActionDescriptor.DisplayName;
        string controllerName =actionContext.Controller.ToString();
        Logger.GetInstance().Info(mobile + ":" + controllerName+"/"+actionName + ": start");
    }

    public override void OnActionExecuted(ActionExecutedContext actionExecutedContext)
    {
        string mobile =actionExecutedContext.ActionDescriptor.Properties["mobile"].ToString();
        string controllerName = actionExecutedContext.Controller.ToString();
        string actionName = actionExecutedContext.ActionDescriptor.DisplayName;
        Logger.GetInstance().Info(mobile + ":" + controllerName + "/" + actionName + ": end");

    }
}