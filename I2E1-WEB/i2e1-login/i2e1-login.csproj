<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net6.0</TargetFramework>
    <AspNetCoreHostingModel>InProcess</AspNetCoreHostingModel>
    <RootNamespace>i2e1_login</RootNamespace>
    <MvcBuildViews>true</MvcBuildViews>
    <UserSecretsId>5bcc106c-48f1-422a-a277-b1b73d502d5e</UserSecretsId>
    <PreserveCompilationContext>true</PreserveCompilationContext>
    <RazorCompileOnPublish>false</RazorCompileOnPublish>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="AspNetCoreRateLimit" Version="5.0.0" />
    <PackageReference Include="Azure.Storage.Blobs" Version="12.8.0" />
    <PackageReference Include="CsvHelper" Version="32.0.2" />
    <PackageReference Include="EPPlus" Version="6.2.0" />
    <PackageReference Include="ExcelDataReader" Version="3.6.0" />
    <PackageReference Include="Handlebars.Net" Version="2.1.4" />
    <PackageReference Include="Microsoft.AspNetCore.Authentication.JwtBearer" Version="6.0.0" />
    <PackageReference Include="Microsoft.AspNetCore.Mvc.NewtonsoftJson" Version="6.0.0" />
    <PackageReference Include="Microsoft.AspNetCore.Mvc.Razor.RuntimeCompilation" Version="6.0.0" />
    <PackageReference Include="Microsoft.AspNetCore.ResponseCompression" Version="2.2.0" />
    <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
    <PackageReference Include="RazorPay.Core" Version="1.0.2" />
    <PackageReference Include="System.Drawing.Common" Version="6.0.0" />
    <PackageReference Include="System.IdentityModel.Tokens.Jwt" Version="6.25.1" />
    <PackageReference Include="UAParser" Version="3.1.47" />
    <PackageReference Include="WindowsAzure.Storage" Version="9.3.3" />
  </ItemGroup>


  <ItemGroup>
    <ProjectReference Include="..\i2e1-core\i2e1-core.csproj" />
  </ItemGroup>
  
  <ItemGroup>
    <None Update="ErrorPages\*">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
  </ItemGroup>
  
  <ItemGroup>
    <Folder Include="Models\WIOM\" />
  </ItemGroup>
</Project>
