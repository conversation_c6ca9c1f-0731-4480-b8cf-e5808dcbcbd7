using I2E1_Message.Models;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;

namespace I2E1_WEB.Models;

[Serializable]
public enum status
{
    Success,
    Failure,
    Aborted,
    Invalid
}

[Serializable]
public enum currencyType
{
    INR,
    USD,
    EUR,
    GBP,
    SGD
}

[Serializable]
public class TransactionUser
{

    public string order_id { get; set; }

    public string tracking_id { get; set; }

    public string bank_ref_no { get; set; }

    public status order_status { get; set; }

    public float amount { get; set; }

    public currencyType currency { get; set; }

    public string failure_message { get; set; }

    public string payment_mode { get; set; }

    public string card_name { get; set; }

    public int status_code { get; set; }

    public string status_message { get; set; }

    public string billing_name { get; set; }

    public string billing_address { get; set; }

    public string billing_city { get; set; }

    public string billing_state { get; set; }

    public string billing_zip { get; set; }

    public string billing_country { get; set; }

    public string billing_tel { get; set; }

    public string billing_email { get; set; }

    public string delivery_name { get; set; }

    public string delivery_address { get; set; }

    public string delivery_city { get; set; }

    public string delivery_state { get; set; }

    public string delivery_zip { get; set; }

    public string delivery_country { get; set; }

    public string deivery_tel { get; set; }

    public string merchant_param1 { get; set; }

    public string merchant_param2 { get; set; }

    public string merchant_param3 { get; set; }

    public string merchant_param4 { get; set; }

    public string merchant_param5 { get; set; }

    public string vault { get; set; }

    public string offer_type { get; set; }

    public string discount_value { get; set; }

    public string retry { get; set; }

    public int response_code { get; set; }
    
}