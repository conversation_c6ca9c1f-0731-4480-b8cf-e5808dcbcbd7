using i2e1_core.Models.Client;
using System;
using System.Collections.Generic;

namespace I2E1_WEB.Models.Analytics;

[Serializable]
public class PartnerDetail
{
    public PartnerDetail(String partnerCode)
    {
        this.partnerCode = partnerCode;
    }
    public string partnerCode {get; set;}
    public List<StorePublicDetails> stores { get; set; }
}

public class QueryObject
{
    public string tsType { get; set; }
    public DateTime from { get; set; }
    public DateTime to { get; set; }
    public List<int> nasids { get; set; }
    public List<int> sources { get; set; }
    public string partnerCode { get; set; }
    public int partnerId { get; set; }
    public List<string> industries { get; set; }
}

