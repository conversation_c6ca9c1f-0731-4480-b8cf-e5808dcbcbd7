using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;

namespace I2E1_WEB.Models;


[Serializable]
public class Page
{
    public string url { get; set; }
    public string html1 { get; set; }
    public string html2 { get; set; }
}

[Serializable]
public class CaptivePortal
{
    public string  ssid { get; set; }
    public string mac { get; set; }
    public string appid { get; set; }
    public string appversion { get; set; }
    public Page[] page { get; set; }
}