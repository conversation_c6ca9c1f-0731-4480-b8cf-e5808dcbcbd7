using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace I2E1_Message.Models
{
    [Serializable]
    public class Parameters
    {
        public string  defaultcode { get; set; }
        public bool enforce { get; set; }
    }

    [Serializable]
    public class GlobalOtp
    {

        public bool enable { get; set; }

        public Parameters parameters { get; set; }

        public GlobalOtp()
        {
            this.enable = false;
        }
    }
}
