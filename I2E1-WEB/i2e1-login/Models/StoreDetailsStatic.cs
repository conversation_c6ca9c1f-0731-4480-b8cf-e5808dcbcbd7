using i2e1_basics.Utilities;
using System;

namespace I2E1_WEB.Models;

[Serializable]
	public class StoreDetailsStatic
{
    public LongIdInfo nasid { get; set; }

    public DateTime? locationStartDate { get; set; }

    public DateTime? locationCloseDate { get; set; }

    public DateTime? locationRetagDate { get; set; }

    public string closeReason { get; set; }

    public string pincode { get; set; }

    public string position { get; set; }

    public string managerName { get; set; }

    public string managerEmail { get; set; }

    public string managerPhone { get; set; }

    public string salesId { get; set; }

    public string installerId { get; set; }

    public string partnerCode { get; set; }

    public int partnerId { get; set; }

    public string locationSubCategory { get; set; }

    public string packageType { get; set; }

    public string locationCategory { get; set; }

    public string mode { get; set; }

    public string locationOpeningTime { get; set; }

    public string locationClosingTime { get; set; }

    public string deviceType { get; set; }

    public string deviceVersion { get; set; }

    public string deviceMac { get; set; }

    public string address { get; set; }

    public string GST { get; set; }

    public DateTime? packageExpiry { get; set; }

    public string externalStoreIdentifier { get; set; }

    public string salesChannel { get; set; }

    public string salesChannelName { get; set; }

    public string legalBusinessName { get; set; }
    public string legalBusinessAddress { get; set; }
    public string gstNumber { get; set; }
}