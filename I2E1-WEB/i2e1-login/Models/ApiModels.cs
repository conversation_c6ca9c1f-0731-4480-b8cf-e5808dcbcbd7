using i2e1_basics.Utilities;
using i2e1_core.Models.Client;
using System;
using System.Collections.Generic;

namespace I2E1_WEB.Models;

[Serializable]
public enum ApiClient
{
    NONE = 0,
    COHO = 1,
    OYO,
    MOBILYZED,
    BYG,
    MYHQ
}

[Serializable]
public enum UpdateOperation
{
    ADD = 0,
    DELETE
}

[Serializable]
public enum UpdateList
{
    VIP_LIST = 0,
    WHITE_LIST,
    BLACK_LIST,
    MAC_WHITE_LIST
}

[Serializable]
public class ApiRequestBase
{
    public string locationId { get; set; }

    public LongIdInfo nasid { get; set; }

    public string producerMobileNumber { get; set; }
}

[Serializable]
public class ApiModels : ApiRequestBase
{
    public string mobile { get; set; }

    public List<string> macIds { get; set; }

    public string emailId { get; set; }
}

[Serializable]
public class UpdateLocationRequest : ApiRequestBase
{
    public StorePublicDetails locationDetails { get; set; }
}

[Serializable]
public class UpdateListRequest : ApiRequestBase
{
    public string username { get; set; }

    public string name { get; set; }

    public UpdateOperation updateOperation { get; set; }

    public UpdateList updateList { get; set; }
}

[Serializable]
public class LogoutUserRequest : ApiRequestBase
{
    public string mobile { get; set; }

    public DateTime? logoutTime { get; set; }
}

[Serializable]
public class FetchSessionsDetailed : ApiRequestBase
{
    public DateTime startDate { get; set; }

    public DateTime endDate { get; set; }

    public string apitoken { get; set; }
}

[Serializable]
public class FDMUserDetailsRequest : ApiRequestBase
{
    public DateTime? startDate { get; set; }

    public DateTime? endDate { get; set; }
}

[Serializable]
public class GetUsersInDurationRequest : ApiRequestBase
{
    public int? timeInterval { get; set; } // In minutes

    public string mobile { get; set; }

    public DateTime startDate { get; set; }

    public DateTime endDate { get; set; }
    public string appId { get; set; }

}

[Serializable]
public class SyncListRequest
{
    public int sourceNasId { get; set; }

    public int destinationNasId { get; set; }

    public string sourceLocationId { get; set; }

    public string destinationLocationId { get; set; }

    public string sourceProducerMobileNumber { get; set; }

    public string destinationProducerMobileNumber { get; set; }

    public UpdateList updateList { get; set; }
}