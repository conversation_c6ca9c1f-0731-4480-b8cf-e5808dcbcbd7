using System;
using System.Collections.Generic;
using System.Data.SqlClient;
using System.Linq;
using System.Web;

namespace I2E1_Message.Models
{
    [Serializable]
    public enum QuestionType
    {
        SECOND_PAGE = 0,
        POLL,
        FEEDBACK,
        FIRST_PAGE,
        SECOND_PAGE_UPPER_QUESTION = 5
    }

    [Serializable]
    public enum AnswerType
    {
        TEXT = 0,
        RADIO,
        CHECKBOX,
        STARRED,
        MULTILINE_TEXT,
        TEXT_WITH_SEPERATE_LABEL
    }

    [Serializable]
    public class Question
    {
        public int id { get; set; }

        public int templateId { get; set; }

        public String questionKey { get; set; }

        public string quesText { get; set; }

        public QuestionType quesType { get; set; }

        public AnswerType answerType { get; set; }

        public List<Option> options { get; set; }

        public bool hidden { get; set; }

        public string mobile { get; set; }

        public string answer { get; set; }

        public int nextQuestionId { get; set; }

        public bool randomized { get; set; }

        public bool binarySplit { get; set; }

        public int displayIndex { get; set; }

        public List<string> optionsOrder { get; set; }

        public long displayTime { get; set; }

        public long selectionTime { get; set; }

        public int journey { get; set; }

        public QuestionSequence CreateSequence(int firstQuestion, int lastQuestionId)
        {
            QuestionSequence sequence = new QuestionSequence();
            sequence.NQ = this.nextQuestionId;
            sequence.FQ = firstQuestion;
            sequence.LQ = lastQuestionId;
            if (this.options != null)
            {
                foreach (Option op in this.options)
                {
                    if (op.nextQuestionId == 0)
                        op.nextQuestionId = this.nextQuestionId;
                    sequence.DS.Add(op.id, op.nextQuestionId);
                }
            }
            return sequence;
        }
    }

    [Serializable]
    public class QuestionSequence
    {
        public Dictionary<int, int> DS { get; set; } // Dependendent Sequence

        public QuestionSequence()
        {
            this.DS = new Dictionary<int, int>();
        }

        public int FQ { get; set; } //first question

        public int LQ { get; set; } // last question

        public int NQ { get; set; } // nest question

        public bool randomized { get; set; }

        public bool binarySplit { get; set; }
    }

    [Serializable]
    public class Option
    {
        public int id { get; set; }

        public string text { get; set; }

        public string image { get; set; }

        public bool isSelected { get; set; }

        public int nextQuestionId { get; set; }
    }

}