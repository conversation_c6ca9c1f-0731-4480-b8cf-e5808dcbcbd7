using I2E1_Message.Utils;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Web;

namespace I2E1_Message.Models
{


    [Serializable]
    public class AppUser
    {
        public string phone { get; set; }

        public string mac { get; set; }

        public string uuid { get; set; }

        public string model { get; set; }

        public string android_Version { get; set; }

        public string token { get; set; }

        public string email { get; set; }

        public int usertype { get; set; }

    }

    [Serializable]
    public class FinalObject
    {
        public string version { get; set; }

        public DateTime timestamp { get; set; }

        public DateTime expiry { get; set; }

        public bool complete { get; set; }

        public JObject pdolist { get; set; }

        public JObject appproviderdata { get; set; }

    }

    [Serializable]
    public class wanlistv2
    {
        public string geoLoc { get; set; }

        public string cpUrl { get; set; }
        public JObject macid { get; set; }

        public string rating { get; set; }

        public string ssid { get; set; }

        public string status { get; set; }

        public string OPENBETWEEN { get; set; }

        public string AVGSPEED { get; set; }

        public string FREEBAND { get; set; }

        public string PAYMENTMODES { get; set; }

        public string providerId { get; set; }

        public keydata encryptionkey { get; set; }

    }

    public class wanListLinq
    {
        public string latitude { get; set; }

        public string longitude { get; set; }

        public string cpUrl { get; set; }

        public string ssid { get; set; }

        public string OPENBETWEEN { get; set; }

        public string AVGSPEED { get; set; }

        public string FREEBAND { get; set; }

        public string PAYMENTMODES { get; set; }

        public string providerId { get; set; }

        public keydata encryptionkey { get; set; }

    }

    [Serializable]
    public class wanlist
    {
        public string geoLoc { get; set; }

        public HashSet<string> cpUrl { get; set; }
        public HashSet<string> macid { get; set; }

        public string rating { get; set; }

        public string ssid { get; set; }

        public string status { get; set; }

        public string OPENBETWEEN { get; set; }

        public string AVGSPEED { get; set; }

        public string FREEBAND { get; set; }

        public string PAYMENTMODES { get; set; }

        public string providerId { get; set; }

        public keydata encryptionkey { get; set; }

    }

    [Serializable]
    public class PDOData
    {
        public string apUrl { get; set; }

        public string email { get; set; }

        public string id { get; set; }

        public string name { get; set; }

        public string phone { get; set; }

        public string rating { get; set; }

        public string status { get; set; }

        public string lastSync { get; set; }

        public keydata keys { get; set; }

    }

    [Serializable]
    public class AppProviderData
    {
        public string authUrl { get; set; }

        public string id { get; set; }

        public string name { get; set; }

        public keydata keys { get; set; }

    }

    [Serializable]
    public class keydata
    {
        public string exp { get; set; }
        public string key { get; set; }

    }

    public class Linq_Shop_Assets
    {
        public string usermobile { get; set; }

        public string shopname { get; set; }

        public int linqId { get; set; }

        public string shopOwnerName { get; set; }

        public string gst { get; set; }

        public string upi { get; set; }

        public string year { get; set; }

        public int status { get; set; }

        public bool isIos { get; set; }

    }
}