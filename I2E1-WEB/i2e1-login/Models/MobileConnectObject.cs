using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;

namespace I2E1_WEB.Models;

[Serializable]
public class MobileConnectObject
{

    //provided by discover api

    public string ttl { get; set; }

    public DiscoveryResponse response { get; set; }

    public string subscriberid { get; set; }

    //public string redirecturi = "https://localhost:44300/MobileConnect/AuthorizeUser";

    public string redirecturi = "https://www.i2e1.in/MobileConnect/AuthorizeUser";
    
    //these are not provided by discover api
    public string sessionid { get; set; }

    public string mobile { get; set; }

    public string code { get; set; }

    public string error { get; set; }

    public string authorizationurl { get; set; }

    public string tokenurl { get; set; }
}

[Serializable]
public class DiscoveryResponse
{
    public string client_id { get; set; }

    public string client_secret { get; set; }

    public string serving_operatior { get; set; }

    public string country { get; set; }

    public string currency { get; set; }

    public Api apis { get; set; }

}

[Serializable]
public class Api
{
    public Operatorid operatorid { get; set; }
}

[Serializable]
public class Operatorid
{
    public Link[] link { get; set; }
}

[Serializable]
public class Link
{
    public string href { get; set; }

    public string rel { get; set; }
}

[Serializable]
public class TokenResponse
{
    public string scope { get; set; }

    public string token_type { get; set; }

    public string expires_in { get; set; }

    public string refresh_token { get; set; }

    public string id_token { get; set; }

    public string access_token { get; set; }

    public string error { get; set; }

    public string error_description { get; set; }
}