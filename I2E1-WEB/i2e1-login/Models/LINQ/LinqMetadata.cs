using Amazon;
using Amazon.S3;
using Amazon.S3.Model;
using Amazon.S3.Transfer;
using I2E1_WEB.Database;
using Microsoft.AspNetCore.Http;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.IO;
using System.Threading.Tasks;

namespace I2E1_WEB.Models.LINQ;

public class LinqCategory
{
    //categoryName
    public string text { get; set; }
    //categoryId
    public int? id { get; set; }
}

public class LinqSubCategory
{
    public int? subCategoryId { get; set; }
    public string subCategoryName { get; set; }
    public string categoryName { get; set; }
    public bool isProduct { get; set; }
    public bool isService { get; set; }
    public bool isPublicPlace { get; set; }
    public int? imgCounter { get; set; }
    public int? categoryId { get; set; }
}

public class LinqNewSubCategory
{
    public int? categoryId { get; set; }
    public string name { get; set; }
    public string categoryName { get; set; }
    public bool? isProduct { get; set; }
    public bool? isService { get; set; }
    public bool? isPublicPlace { get; set; }
}

public class LinqTag
{
    public int? subCategoryId { get; set; }
    public string tagName { get; set; }
    public int? oldId { get; set; }
    public string oldName { get; set; }
    public string subCategoryName { get; set; }
}

public class LinqSubCategoryImage
{
    public string image { get; set; }
    public string thumbnail { get; set; }
}

public class UploadLinqImage
{
    public IFormFile image { get; set; }
    public string subcategory { get; set; }
}

public class UploadLinqContacts
{
    public IFormFile contacts { get; set; }
}


public class LinqImages
{
    private const string bucketName = "i2e1-linq";
    private static IAmazonS3 s3Client = new AmazonS3Client("********************", "xVCn89BUAe9MCJTNBDagyLwKUq05nJUiiPYF9L5G", RegionEndpoint.APSouth1);

    //public static List<LinqSubCategoryImage> GetSubcategoryImagesList(int subCategoryId)
    //{
    //    List<LinqSubCategoryImage> images = new List<LinqSubCategoryImage>();
    //    S3DirectoryInfo dir = new S3DirectoryInfo(s3Client, bucketName, "subcategories/xxxhdpi/"+ subCategoryId.ToString());
    //    foreach (IS3FileSystemInfo file in dir.GetFileSystemInfos())
    //    {
    //        Console.WriteLine(file.Name);
    //        Console.WriteLine(file.Extension);
    //        Console.WriteLine(file.LastWriteTime);
    //        LinqSubCategoryImage image = new LinqSubCategoryImage();
    //        if (file.Name.EndsWith("_crop_sq.jpg"))
    //        {
    //            string[] remove = { "_crop_sq.jpg" };
    //            image.image = file.Name.Substring(0, file.Name.LastIndexOf(remove[0]))+".jpg";
    //            image.thumbnail = file.Name;
    //            images.Add(image);
    //        }
    //    }
    //    return images;
    //}

    //public static List<LinqSubCategoryImage> GetBadSubcategoryImagesList(int subCategoryId)
    //{
    //    List<LinqSubCategoryImage> images = new List<LinqSubCategoryImage>();
    //    S3DirectoryInfo dir = new S3DirectoryInfo(s3Client, bucketName, "subcategories/original" + subCategoryId.ToString());
    //    foreach (IS3FileSystemInfo file in dir.GetFileSystemInfos())
    //    {
    //        Console.WriteLine(file.Name);
    //        Console.WriteLine(file.Extension);
    //        Console.WriteLine(file.LastWriteTime);
    //        LinqSubCategoryImage image = new LinqSubCategoryImage();
    //        if (file.Name.EndsWith(".jpg"))
    //        {
    //            // Correct Extension and case

    //            if (file.Name.EndsWith("_crop_sq.jpg"))
    //            {
    //                int fileNameInt;
    //                if (int.TryParse(file.Name.Substring(0, file.Name.LastIndexOf("_crop_sq.jpg")), out fileNameInt))
    //                {
    //                    // Legit Image Thumbnail with Integer Name Prefix
    //                }
    //                else
    //                {
    //                    // Poorly formed Name of Image Thumbnail
    //                    image.image = "";
    //                    image.thumbnail = s3Client + "/" + bucketName + "/" + "subcategories/original" + "/" + subCategoryId.ToString() + "/" + file.Name;
    //                    images.Add(image);
    //                }
    //            }
    //            else
    //            {
    //                int fileNameInt;
    //                if (int.TryParse(file.Name.Substring(0, file.Name.LastIndexOf(".jpg")), out fileNameInt))
    //                {
    //                    // Legit Image with Integer Name Prefix
    //                }
    //                else
    //                {
    //                    // Poorly formed Name of Image Thumbnail
    //                    image.image = s3Client + "/" + bucketName + "/" + "subcategories/original" + "/" + subCategoryId.ToString() + "/" + file.Name;
    //                    image.thumbnail = "";
    //                    images.Add(image);
    //                }
    //            }
    //        }
    //        else
    //        {
    //            // Incorrect Extension and/or Case
    //            image.image = s3Client + "/" + bucketName + "/" + "subcategories/original" + "/" + subCategoryId.ToString() + "/" + file.Name;
    //            image.thumbnail = "";
    //            images.Add(image);
    //        }
    //    }
    //    return images;
    //}

    private static async void MoveSubcategoryImage(string oldKey, string newKey)
    {
        try
        {
            DeleteObjectRequest deleteRequest;
            DeleteObjectResponse deleteResponse;
            CopyObjectRequest copyRequest;
            CopyObjectResponse copyResponse;
            deleteRequest = new DeleteObjectRequest
            {
                BucketName = bucketName,
                Key = oldKey.Replace(oldKey.Substring(oldKey.LastIndexOf(".")), "_crop_sq.jpg")
            };
            deleteResponse = await s3Client.DeleteObjectAsync(deleteRequest);
            copyRequest = new CopyObjectRequest
            {
                SourceBucket = bucketName,
                SourceKey = oldKey,
                DestinationBucket = bucketName,
                DestinationKey = newKey
            };
            copyResponse = await s3Client.CopyObjectAsync(copyRequest);
            Debug.WriteLine("Copying : " + oldKey + " To : " + newKey);
            deleteRequest = new DeleteObjectRequest
            {
                BucketName = bucketName,
                Key = oldKey
            };
            deleteResponse = await s3Client.DeleteObjectAsync(deleteRequest);
            Debug.WriteLine("Deleting : " + oldKey);
            /**/
            Debug.WriteLine("Deleting : " + oldKey);
        }
        catch (AmazonS3Exception e)
        {
            Console.WriteLine("Error encountered on server. Message:'{0}' when writing an object", e.Message);
        }
        catch (Exception e)
        {
            Console.WriteLine("Unknown encountered on server. Message:'{0}' when writing an object", e.Message);
        }
    }

    //public static async Task<List<LinqSubCategoryImage>> HealBadSubcategoryImagesList(int subCategoryId)
    //{
    //    List<LinqSubCategoryImage> images = new List<LinqSubCategoryImage>();
    //    S3DirectoryInfo dir = new S3DirectoryInfo(s3Client, bucketName, "subcategories/original/" + subCategoryId.ToString());
    //    int index = LinqAdminDatabaseRequest.GetImageUploadCount(subCategoryId.ToString());
    //    foreach (IS3FileSystemInfo file in dir.GetFileSystemInfos())
    //    {
    //        Console.WriteLine(file.Name);
    //        Console.WriteLine(file.Extension);
    //        Console.WriteLine(file.LastWriteTime);
    //        LinqSubCategoryImage image = new LinqSubCategoryImage();
    //        index++;
    //        if (file.Name.EndsWith(".jpg"))
    //        {
    //            // Correct Extension and case

    //            if (file.Name.EndsWith("_crop_sq.jpg"))
    //            {
    //                int fileNameInt;
    //                if (int.TryParse(file.Name.Substring(0, file.Name.LastIndexOf("_crop_sq.jpg")), out fileNameInt))
    //                {
    //                    // Legit Image Thumbnail with Integer Name Prefix
    //                }
    //                else
    //                {
    //                    // Poorly formed Name of Image Thumbnail
    //                    string badThumb = "subcategories/original" + "/" + subCategoryId.ToString() + "/" + file.Name;
    //                    var deleteRequest = new DeleteObjectRequest
    //                    {
    //                        BucketName = bucketName,
    //                        Key = badThumb
    //                    };
    //                    DeleteObjectResponse deleteResponse = await s3Client.DeleteObjectAsync(deleteRequest);
    //                    Debug.WriteLine("Deleting : " + badThumb);
    //                    image.image = bucketName + "/" + badThumb;
    //                    image.thumbnail = "";
    //                    images.Add(image);
    //                }
    //            }
    //            else
    //            {
    //                int fileNameInt;
    //                if (int.TryParse(file.Name.Substring(0, file.Name.LastIndexOf(".jpg")), out fileNameInt))
    //                {
    //                    // Legit Image with Integer Name Prefix
    //                }
    //                else
    //                {
    //                    // Poorly formed Name of Image Thumbnail
    //                    string badThumb = "subcategories/original" + "/" + subCategoryId.ToString() + "/" + file.Name;
    //                    var deleteRequest = new DeleteObjectRequest
    //                    {
    //                        BucketName = bucketName,
    //                        Key = badThumb
    //                    };
    //                    DeleteObjectResponse deleteResponse = await s3Client.DeleteObjectAsync(deleteRequest);
    //                    Debug.WriteLine("Deleting : " + badThumb);
    //                    image.image = bucketName + "/" + badThumb;
    //                    image.thumbnail = "";
    //                    images.Add(image);
    //                }
    //            }
    //        }
    //        else
    //        {
    //            // Incorrect Extension and/or Case
    //            if (true)
    //            {
    //                int fileNameInt;
    //                // Legit Image with Integer Name Prefix
    //                if (file.Name.EndsWith(".jpeg") && int.TryParse(file.Name.Substring(0, file.Name.LastIndexOf(".jpeg")), out fileNameInt))
    //                    MoveSubcategoryImage("subcategories/original" + "/" + subCategoryId.ToString() + "/" + file.Name, 
    //                        "subcategories/original" + "/" + subCategoryId.ToString() + "/" + file.Name.Substring(0, file.Name.LastIndexOf(".jpeg")) + ".jpg");

    //                if (file.Name.EndsWith(".JPEG") && int.TryParse(file.Name.Substring(0, file.Name.LastIndexOf(".JPEG")), out fileNameInt))
    //                    MoveSubcategoryImage("subcategories/original" + "/" + subCategoryId.ToString() + "/" + file.Name,
    //                        "subcategories/original" + "/" + subCategoryId.ToString() + "/" + file.Name.Substring(0, file.Name.LastIndexOf(".JPEG")) + ".jpg");

    //                if (file.Name.EndsWith(".png") && int.TryParse(file.Name.Substring(0, file.Name.LastIndexOf(".png")), out fileNameInt))
    //                    MoveSubcategoryImage("subcategories/original" + "/" + subCategoryId.ToString() + "/" + file.Name,
    //                        "subcategories/original" + "/" + subCategoryId.ToString() + "/" + file.Name.Substring(0, file.Name.LastIndexOf(".png")) + ".jpg");

    //                if (file.Name.EndsWith(".PNG") && int.TryParse(file.Name.Substring(0, file.Name.LastIndexOf(".PNG")), out fileNameInt))
    //                    MoveSubcategoryImage("subcategories/original" + "/" + subCategoryId.ToString() + "/" + file.Name,
    //                        "subcategories/original" + "/" + subCategoryId.ToString() + "/" + file.Name.Substring(0, file.Name.LastIndexOf(".PNG")) + ".jpg");

    //                if (file.Name.EndsWith(".JPG") && int.TryParse(file.Name.Substring(0, file.Name.LastIndexOf(".JPG")), out fileNameInt))
    //                    MoveSubcategoryImage("subcategories/original" + "/" + subCategoryId.ToString() + "/" + file.Name,
    //                        "subcategories/original" + "/" + subCategoryId.ToString() + "/" + file.Name.Substring(0, file.Name.LastIndexOf(".JPG")) + ".jpg");

    //                //LinqAdminDatabaseRequest.IncrementImageUploadCount(subCategoryId.ToString());
    //                var imagesList = LinqImages.GetSubcategoryImagesList(subCategoryId);
    //                LinqAdminDatabaseRequest.SetImageUploadCount(subCategoryId.ToString(), imagesList.Count);
    //                image.image = bucketName + "/" + "subcategories/original" + "/" + subCategoryId.ToString() + "/" + file.Name;
    //                image.thumbnail = "";
    //                images.Add(image);
    //            }
    //        }
    //    }
    //    return images;
    //}

    public static async Task UploadFileAsync(string keyName, Stream stream)
    {
        try
        {
            var uploadRequest = new TransferUtilityUploadRequest
            {
                Key = keyName,
                BucketName = bucketName,
                CannedACL = S3CannedACL.PublicRead,
                InputStream = stream
            };

            var fileTransferUtility = new TransferUtility(s3Client);
            await fileTransferUtility.UploadAsync(uploadRequest);
            Console.WriteLine("Upload Image completed");
        }
        catch (AmazonS3Exception e)
        {
            Console.WriteLine("Error encountered on server. Message:'{0}' when writing an object", e.Message);
        }
        catch (Exception e)
        {
            Console.WriteLine("Unknown encountered on server. Message:'{0}' when writing an object", e.Message);
        }

    }
}

public class LinqMetadata
{
}