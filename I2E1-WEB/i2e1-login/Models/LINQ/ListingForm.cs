using i2e1_core.Models.SWAP;
using I2E1_Message.Models;
using System.Collections.Generic;

namespace I2E1_WEB.Models.LINQ;

public class ListingSession
{
    public string appId { get; set; }

    public string mobile { get; set; }

    public int listingId { get; set; }

    public bool isEdit { get; set; }

    public string screen_size { get; set; }
}

public class ListingBase
{
    public int listingId { get; set; }

    public string latitude { get; set; }

    public string longitude { get; set; }
}

public class ListingForm : ListingBase
{
    private List<OperatingHours> pOperatingHours;

    private string pWebsite;

    public ListingForm()
    {
        operatingHours = new List<OperatingHours>();
        shopSubCat = new List<SubCategory>();
    }

    public ListingForm(bool createNew)
    {
        InitializeOperatingHours();
    }

    public string shopName { get; set; }

    public string shopOwnerName { get; set; }

    public List<SubCategory> shopSubCat { get; set; }

    public string shopAddress { get; set; }

    public string shopLocality { get; set; }

    public string shopCity { get; set; }

    public string shopState { get; set; }

    public string pincode { get; set; }

    public string marketPlace { get; set; }

    public string shopLandmark { get; set; }

    public string shopContactP { get; set; }

    public string shopContactS { get; set; }

    public string shopContactT { get; set; }

    public string shopContactQ { get; set; }

    public string products { get; set; }

    public string emailId { get; set; }

    public bool isActive { get; set; }

    public string website {
        get {
            if (string.IsNullOrEmpty(pWebsite) || pWebsite.StartsWith("http"))
                return pWebsite;

            return "http://" + pWebsite;
        }
        set { pWebsite = value; }
    }

    public List<OperatingHours> operatingHours {
        get
        {
            return pOperatingHours;
        }
        set
        {
            if (value != null)
                pOperatingHours = value;
        }
    }
    public string source { get; set; }

    public void InitializeOperatingHours()
    {
        operatingHours = new List<OperatingHours>();
        operatingHours.Add(new OperatingHours() { day = "Mon", openingHour = "10:00", closingHour = "21:00", isOpen = true });
        operatingHours.Add(new OperatingHours() { day = "Tue", openingHour = "10:00", closingHour = "21:00", isOpen = true });
        operatingHours.Add(new OperatingHours() { day = "Wed", openingHour = "10:00", closingHour = "21:00", isOpen = true });
        operatingHours.Add(new OperatingHours() { day = "Thu", openingHour = "10:00", closingHour = "21:00", isOpen = true });
        operatingHours.Add(new OperatingHours() { day = "Fri", openingHour = "10:00", closingHour = "21:00", isOpen = true });
        operatingHours.Add(new OperatingHours() { day = "Sat", openingHour = "10:00", closingHour = "21:00", isOpen = true });
        operatingHours.Add(new OperatingHours() { day = "Sun", openingHour = "10:00", closingHour = "21:00", isOpen = true });
    }
}

public class ListingForm2 : ListingBase
{
    public ListingForm2()
    {
        InternetStatus = new List<InternetStatus>();

        selectedStatus = 3;
        profilePhoto = new ShopPhoto();
        visitingCardPhoto = new ShopPhoto();
        shopBoardPhoto = new ShopPhoto();
        shopOwnerPhoto = new ShopPhoto();
        shopPhotos = new List<ShopPhoto>();
        shopPhotos.Add(new ShopPhoto());
    }

    public string famousFor { get; set; }

    public bool tfour { get; set; }

    public bool lateNight { get; set; }

    public bool homeDelivery { get; set; }

    public bool ccDc { get; set; }

    public bool khata { get; set; }

    public string discount { get; set; }

    public string paytmNumber { get; set; }

    public string mobikwikNumber { get; set; }

    public string upiId { get; set; }

    public string gstNumber { get; set; }

    public string whatsapp { get; set; }

    public string facebook { get; set; }

    public string twitter { get; set; }

    public string instagram { get; set; }

    public string zomato { get; set; }

    public string shopThumbUrl { get; set; }

    public ShopPhoto profilePhoto { get; set; }

    public ShopPhoto visitingCardPhoto { get; set; }

    public ShopPhoto shopBoardPhoto { get; set; }

    public ShopPhoto shopOwnerPhoto { get; set; }

    public List<ShopPhoto> shopPhotos { get; set; }

    public List<InternetStatus> InternetStatus { get; set; }

    public int selectedStatus { get; set; }

    public string funfacts { get; set; }

}

public class ListingForm3 : ListingBase
{
    public ListingForm3()
    {
        ProbableLead = new List<ProbableLead>();
        ProbableLead.Add(new ProbableLead() { leadType = "High Value", leadCode = 1 });
        ProbableLead.Add(new ProbableLead() { leadType = "Low Value", leadCode = 2 });
        ProbableLead.Add(new ProbableLead() { leadType = "None", leadCode = 0 });

        selectedLeadType = 0;
    }
    public string year { get; set; }

    public string origin { get; set; }

    public byte howManyKids { get; set; }

    public bool haveSmartphone { get; set; }

    public List<ProbableLead> ProbableLead { get; set; }

    public int selectedLeadType { get; set; }
}

public class InternetStatus
{
    public string internetType { get; set; }

    public int code { get; set; }
}

public class ProbableLead
{
    public string leadType { get; set; }

    public int leadCode { get; set; }
}

public class CompleteListingForm
{
    public ListingForm page1 { get; set; }

    public ListingForm2 page2 { get; set; }

    public ListingForm3 page3 { get; set; }

    public string ownerMobile { get; set; }
}
