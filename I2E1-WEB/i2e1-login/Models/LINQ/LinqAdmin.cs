using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;

namespace I2E1_WEB.Models.LINQ;

public class LinqListingData
{
    public int count { get; set; }
}

public class LinqListing
{
    public int listingId { get; set; }

    public string shopName { get; set; }

    public string shopAddress { get; set; }

    public string shopLocality { get; set; }

    public string shopCity { get; set; }

    public string shopState { get; set; }

    public string mobile { get; set; }

    public string nasid { get; set; }

    public bool active { get; set; }
}

public class LinqAdminContacts
{
    public List<LinqUser> salesContacts { get; set; }
}

public class LinqAdminLogin
{
    public string userName { get; set; }
    public string password { get; set; }
}

public class LinqAdminAuthorization
{
    public string token { get; set; }
}

public class ListingAdmins
{
    public int listingId { get; set; }
    public LinqUser owner { get; set; }
    public List<LinqUser> moderators { get; set; }
}

public class LinqUser
{
    public string mobile { get; set; }
    public string name { get; set; }
    public string email { get; set; }
    public string firstName { get; set; }
    public string lastName { get; set; }
    public bool isLinqUser { get; set; }
}

public class LinqAdmin
{
}