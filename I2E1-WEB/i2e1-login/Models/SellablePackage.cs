using i2e1_core.Models.Client;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;


namespace I2E1_WEB.Models;

[Serializable]
	public class SellablePackage : I2e1Object
{
    public int sellablePackageId { get; set; }
    public int packageId { get; set; }
    public int numberOfLocations { get; set; }
    public int price { get; set; }
    public List<Feature> features { get; set; }
    public string packageName { get; set; }
    public string packageCategory { get; set; }
    public int packageValidity { get; set; } // package validity in months
    public PackageJson packageJsonData { get; set; }
}

[Serializable]
	public class PackageJson
{
    public int smsCount { get; set; }
}
