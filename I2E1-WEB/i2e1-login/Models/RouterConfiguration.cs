using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;

namespace I2E1_WEB.Models;

[Serializable]
	public  enum PROTOCOL{
    DYNAMIC = 0,
    STATIC
}
[Serializable]
	public  class RouterConfiguration
{
    public string macid { get; set; }

    public int nasid { get; set; }

    public string ssid { get; set; }

    public PROTOCOL wanProtocol { get; set; }

    public string staticIp { get; set; }

    public string subnetMask { get; set; }

    public string gateway { get; set; }

    public string dns1 { get; set; }

    public string dns2 { get; set; }
}