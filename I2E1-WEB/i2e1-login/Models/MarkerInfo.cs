using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;

namespace I2E1_Message.Models
{
    public enum markerFeedTypes
    {
        SHOPINFO,
        WOFFERS,
        PHOTOS,
        FEEDS,
        REVIEW
    }

    [Serializable]
    public class MarkerInfo
    {
        public string mobile { get; set; }
        public string mac_id { get; set; }
        public string app_id { get; set; }
        public markerFeedTypes google_place_id { get; set; }
        public string info { get; set; }
        public string address { get; set; }
        public string phone { get; set; }
        public string url { get; set; }
        public string rating { get; set; }
        public string latitude { get; set; }
        public string longitude { get; set; }
        public List<string> reviewerPhotoUrl { get; set; }
        public List<string> reviewerUrl { get; set; }
        public List<string> photos { get; set; }
        public List<string> weekdays { get; set; }
        public List<ReviewerInfo> rInfo { get; set; }
        public string wofferPhoto { get; set; }
        public string wofferHeading { get; set; }
        public string wofferText { get; set; }
        public string openNow { get; set; }
        public string closeTime { get; set; }
        public int campaginID { get; set; }
        public int nasID { get; set; }
    }

    public class ReviewerInfo
    {
        public string reviewerText { get; set; }
        public string reviewerName { get; set; }
        public string reviewerphoto { get; set; }
        public string reviewerRating { get; set; }
    }

    public class OperatingHours
    {
        public string day { get; set; }

        public bool isOpen { get; set; }

        public string openingHour { get; set; }

        public string closingHour { get; set; }
    }

    public enum ContentType
    {
        PHOTO = 0,
        TEXT = 1,
        URL = 2,
        PDF = 3,
        NONE = 100
    }

    public class ExternalLink
    {
        public string label { get; set; }

        public string url { get; set; }

        public bool isRenderable { get; set; }
    }

    public class PhoneNumber
    {
        public string number { get; set; }

        public string type { get; set; }
    }

    public class WikiData
    {
        public string wikiImageUrl { get; set; }

        public string wikiPageUrl { get; set; }

    }

    public class WikiExtractData
    {
        public string wikiPageUrl { get; set; }

        public string wikiPageExtract { get; set; }
    }

}

