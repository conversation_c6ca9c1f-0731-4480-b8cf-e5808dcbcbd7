using i2e1_basics.Utilities;
using i2e1_core.Models.Client;
using i2e1_core.Utilities;
using System.Collections.Generic;

namespace I2E1_Message.Models.Client
{
    public class CombinedSetting
    {
        public int settingId { get; set; }
        public string name { get; set; }
        public string owner { get; set; }
        public List<UserGroupNew> userGroups { get; set; }
        public Dictionary<string, AdvanceConfig> advanceConfigs { get; set; }
        public List<LongIdInfo> nases { get; set; }
    }
}
