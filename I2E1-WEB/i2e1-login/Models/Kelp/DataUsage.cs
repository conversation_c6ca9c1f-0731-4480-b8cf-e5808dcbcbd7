using i2e1_basics.Models;
using i2e1_core.Models.SWAP;
using System;

namespace I2E1_WEB.Models.Kelp;

public class DataUsageSearchQuery : SearchQuery
{
    public string username { get; set; }
    public string interval { get; set; }
}

public class DataUsage
{
    public NetworkDetail network { get; set; }
    public string username { get; set; }
    public long dataUsed { get; set; }
    public DateTime time { get; set; }
}
