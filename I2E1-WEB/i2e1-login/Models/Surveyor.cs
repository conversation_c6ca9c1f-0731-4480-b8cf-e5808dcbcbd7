using I2E1_Message.Models;
using I2E1_Message.Utils;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Web;

namespace I2E1_WEB.Models;

public class Survey
{
    public SurveyQuestion question { get; set; }
    public List<SurveyResponse> response { get; set; }
}

public class SurveySession
{
    public string mobile { get; set; }
    public string date_epoch { get; set; }
    public string nasid { get; set; }
}

public class SurveyQuestion
{
    public string id { get; set; }
    public string text { get; set; }

}

public class SurveyResponse
{
    public string id { get; set; }
    public string text { get; set; }
}

public class SurveyPayload
{
    public SurveySession session { get; set; }
    public List<Survey> survey { get; set; }
}