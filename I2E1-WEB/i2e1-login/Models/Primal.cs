using Newtonsoft.Json;
using Newtonsoft.Json.Converters;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;

namespace I2E1_WEB.Models;

[Serializable]
	public  enum Segments
{
    LOYAL = 1,
    NEW = 2,
    ATTRITOR = 3
}

[Serializable]
	public enum Categories
{
    OFFER = 1,
    GREETING = 2
}

[Serializable]
	public  class Primal
{

}

[Serializable]
	public  class PrimalComms : Primal
{
    public string commId { get; set; }

    public int nasid { get; set; }

    public int enabled { get; set; }

    public int userid { get; set; }

    public string storeName { get; set; }

    public string templateId { get; set; }

    public Segments segment { get; set; }

    public Categories smsCategory { get; set; }

    public int discount { get; set; }

    public int valid_upto { get; set; }

    public DateTime creat_date { get; set; }

    public string preview { get; set; }
}

[Serializable]
public class PrimalTemplate
{
    public string id { get; set; }

    public string content { get; set; }

    public int nasid { get; set; }

    public int userid { get; set; }

    public Segments segment { get; set; }

    public Categories category { get; set; }

    public int isCustom { get; set; }
}

[Serializable]
	public  class PrimalSms
{
    public string id { get; set; }
}