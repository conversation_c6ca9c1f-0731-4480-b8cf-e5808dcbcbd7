using I2E1_Message;
using I2E1_Message.Models;
using I2E1_Message.Models.Client;
using I2E1_WEB.Database;
using System;
using System.Collections.Generic;
using System.Data.SqlClient;
using System.Linq;
using System.Web;

namespace I2E1_WEB.Models.Client;

[Serializable]
public class MarketPlace
{
    public int marketPlaceId { get; set; }

    public string marketPlaceName { get; set; }

    public string googlePlaceId { get; set; }

    public string googleCategories { get; set; }

    public string vicinity { get; set; }

    public string subLocality { get; set; }

    public string locality { get; set; }

    public string city { get; set; }

    public string state { get; set; }

    public string stateCode { get; set; }

    public string country { get; set; }

    public string countryCode { get; set; }

    public string pinCode { get; set; }

    public string latitude { get; set; }

    public string longitude { get; set; }

}