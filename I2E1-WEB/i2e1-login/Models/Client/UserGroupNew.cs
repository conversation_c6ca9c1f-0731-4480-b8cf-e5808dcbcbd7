using i2e1_basics.Utilities;
using i2e1_core.Models.Client;
using I2E1_WEB.Database;
using System;

namespace I2E1_WEB.Models.Client;

[Serializable]
public class WebUserGroupNew : UserGroupNew
{
    public WebUserGroupNew()
    {

    }

    public WebUserGroupNew(UserGroupNew group)
    {
        this.adminId = group.adminId;
        this.basicConfigs = group.basicConfigs;
        this.groupName = group.groupName;
        this.groupId = group.groupId;
        this.values = group.values;
    }

    public bool UpdateGroup() {
        //ClientDatabaseRequest.UpdateUserGroupBasicConfig(this);
        return false;
    }

    public bool UpdateUserGroupBasicConfig(int combinedSettingId, LongIdInfo longAdminId)
    {
        return ClientDatabaseRequest.UpdateUserGroupBasicConfig(combinedSettingId, this.groupId, this.basicConfigs, longAdminId);
    }
}
