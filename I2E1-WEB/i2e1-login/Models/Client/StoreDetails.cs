using i2e1_basics.Models;
using i2e1_basics.Utilities;
using i2e1_core.Models.WIOM;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;

namespace i2e1_core.Models.Client
{
    [Serializable]
    public class CityState
    {
        public string city { get; set; }
        public string state { get; set; }
    }

    [Serializable]
    public enum StoreTags
    {
        Zoho_Inactive, /*********** 1 if its on else 0**************/
        <PERSON><PERSON><PERSON>,  /*********** 1 if its on else 0**************/
        Pilot, /*********** 1 if its on else 0**************/
        Swap_Promo_off, /*********** 1 means promotion off **************/
        i2e1_Owned_Internet, /******* 1 if internet is provided by i2e1 else 0 ********/
        Facebook, /******* 1 if paid by Facebook else 0 ********/
        Connect_Bharat /****** 1 if belongs to Connect Bharat else 0 *******/
    }

    public enum RouterWorkingStatus
    {
        ACTIVE=0,
        WARNING=1,
        FAILURE=2
    }

    [Serializable]
    public class StoreSearchQuery : SearchQuery
    {
        public LongIdInfo nasid { get; set; }
        public string storeName { get; set; }
        public string storeNameAlias { get; set; }
        public string address { get; set; }
        public string city { get; set; }
        public string state { get; set; }
        public string category { get; set; }
        public string installedState { get; set; }
        public string partnerCode { get; set; }
        public int partnerId { get; set; }
        public string partnerAccountType { get; set; }
        public string storeTags { get; set; }
    }
   

    [Serializable]
    public class StorePublicDetails : Store
    {
        public string location { get; set; }

        public string partner { get; set; }

        public string emailId { get; set; }

        public int internetBillingStartDay { get; set; }

        public long internetPlan { get; set; }

        public int uploadKpbs { get; set; }

        public int downloadKbps { get; set; }

    }
    [Serializable]
    [JsonObject(ItemNullValueHandling = NullValueHandling.Ignore)]
    public class StoreInfo
    {
        public Dictionary<string, object> extraData { get; set; }
        public LongIdInfo nasid { get; set; }
        public int controllerid { get; set; }

        public string marketPlaceName { get; set; }

        public string marketPlaceCity { get; set; }
        public int marketPlaceId { get; set; }

        public string marketPlaceGoogleId { get; set; }

        public string brandName { get; set; }
        public string storeName { get; set; }
        public string storeNameAlias { get; set; }
        public string address { get; set; }

        public string subLocality { get; set; }
        public string locality { get; set; }
        public string city { get; set; }
        public string state { get; set; }
        public string pinCode { get; set; }
        public string latitude { get; set; }
        public string longitude { get; set; }

        public string category { get; set; }
        public string subCategory { get; set; }
        public string microCategory { get; set; }
        public string googleCategories { get; set; }
        public string googlePlaceId { get; set; }
        public List<string> storeTags { get; set; }

        public RouterState routerState { get; set; }
        public string mode { get; set; }
        public LongIdInfo mmNasId { get; set; }
        public string salesId { get; set; }
        public DateTime? locationStartDate { get; set; }
        public string locationOpeningTime { get; set; }
        public string locationClosingTime { get; set; }

        public string deviceType { get; set; }
        public string partner { get; set; }
        public int partnerId { get; set; }
        public int clientId { get; set; }

        public string installerId { get; set; }
        public DateTime? locationCloseDate { get; set; }
        public DateTime? locationRetagDate { get; set; }
        public string closeReason { get; set; }
        public string location { get; set; }
        public string deviceMac { get; set; }
        public string deviceVersion { get; set; }
        public string firmwareVersion { get; set; }
        public double lastPingDelay { get; set; }

        public string zohoContactId { get; set; }

        public string mktPlaceAlias { get; set; }
        public string shopDpName { get; set; }

        public string email { get; set; }
        public string mobile { get; set; }
        public string managerName { get; set; }
        public string managerEmail { get; set; }
        public string managerPhone { get; set; }
        public string productName { get; set; }
        public string salesChannel { get; set; }
        public string salesChannelName { get; set; }

        public string legalBusinessName { get; set; }
        public string legalBusinessAddress { get; set; }
        public string gstNumber { get; set; }
        public WiomSubscription subscription { get; set; }
    }


    [Serializable]
    public class DevicesStats
    {
        public int activeDeviceCount { get; set; }

        public int totalDeviceCount { get; set; }
    }

    [Serializable]
    public class BrandStoreAddress
    {
        public string brandName { get; set; }

        public string marketPlaceName { get; set; }

        public string marketPlaceId { get; set; }

        public string storeName { get; set; }

        public string googleCategories { get; set; }

        public string googlePlaceId { get; set; }

        public string vicinity { get; set; }

        public string subLocality { get; set; }

        public string locality { get; set; }

        public string city { get; set; }

        public string state { get; set; }

        public string pinCode { get; set; }

        public string latitude { get; set; }

        public string longitude { get; set; }

        public string category { get; set; }

        public string subCategory { get; set; }

    }

}