using Microsoft.AspNetCore.Builder;
using Microsoft.Extensions.DependencyInjection;
using System;

namespace i2e1_login.WebSockets
{
    /// <summary>
    /// Extension methods for WebSocket integration
    /// </summary>
    public static class WebSocketExtensions
    {
        /// <summary>
        /// Add WebSocket services to the service collection
        /// </summary>
        public static IServiceCollection AddPaymentWebSockets(this IServiceCollection services)
        {
            return services;
        }

        /// <summary>
        /// Configure the application to use WebSockets for payment status updates
        /// </summary>
        public static IApplicationBuilder UsePaymentWebSockets(this IApplicationBuilder app)
        {
            // Enable WebSockets
            app.UseWebSockets(new WebSocketOptions
            {
                KeepAliveInterval = TimeSpan.FromMinutes(2),
                ReceiveBufferSize = 4 * 1024 // 4KB
            });
            
            // Add payment status WebSocket middleware
            app.UseMiddleware<PaymentStatusWebSocketMiddleware>();
            
            return app;
        }
    }
}
