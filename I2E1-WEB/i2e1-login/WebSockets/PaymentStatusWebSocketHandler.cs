using i2e1_core.Utilities;
using Microsoft.AspNetCore.Http;
using Newtonsoft.Json;
using System;
using System.Collections.Concurrent;
using System.Net.WebSockets;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using System.Linq;
using System.Collections.Generic;
using i2e1_core.PG;
using i2e1_basics.Utilities;
using System.Net.Http;
using i2e1_basics.Models;

namespace i2e1_login.WebSockets
{
    /// <summary>
    /// WebSocket handler for payment status updates
    /// </summary>
    public class PaymentStatusWebSocketHandler
    {
        private static readonly ConcurrentDictionary<string, List<WebSocket>> _orderSockets = new ConcurrentDictionary<string, List<WebSocket>>();
        private static readonly ConcurrentDictionary<WebSocket, string> _socketOrders = new ConcurrentDictionary<WebSocket, string>();
        private static readonly ConcurrentDictionary<WebSocket, CancellationTokenSource> _socketCancellationTokens = new ConcurrentDictionary<WebSocket, CancellationTokenSource>();
        
        /// <summary>
        /// Process an incoming WebSocket request
        /// </summary>
        public static async Task ProcessWebSocketAsync(HttpContext context, WebSocket webSocket)
        {
            // Get order ID from query string
            string orderId = context.Request.Query["orderId"].ToString();
            if (string.IsNullOrEmpty(orderId))
            {
                await SendErrorMessageAsync(webSocket, "Order ID is required");
                await webSocket.CloseAsync(WebSocketCloseStatus.InvalidPayloadData, "Order ID is required", CancellationToken.None);
                return;
            }

            // Register the connection
            RegisterConnection(webSocket, orderId);
            
            // Send confirmation message
            await SendMessageAsync(webSocket, new
            {
                type = "connection_established",
                message = "Connected to payment status updates",
                orderId = orderId
            });
            
            // Start a ping timer to keep the connection alive
            var pingCts = new CancellationTokenSource();
            _socketCancellationTokens[webSocket] = pingCts;
            _ = StartPingTimerAsync(webSocket, pingCts.Token);
            
            // Start periodic payment status check
            _ = StartPaymentStatusCheckTimerAsync(webSocket, orderId, pingCts.Token);
            
            // Process incoming messages
            await ProcessMessagesAsync(webSocket, orderId);
        }
        
        /// <summary>
        /// Register a new WebSocket connection for an order
        /// </summary>
        private static void RegisterConnection(WebSocket webSocket, string orderId)
        {
            // Add to order-sockets dictionary
            _orderSockets.AddOrUpdate(
                orderId,
                new List<WebSocket> { webSocket },
                (key, existingList) =>
                {
                    existingList.Add(webSocket);
                    return existingList;
                });
            
            // Add to socket-order dictionary
            _socketOrders[webSocket] = orderId;
            
            Logger.GetInstance().Info($"WebSocket connection registered for order {orderId}");
        }
        
        /// <summary>
        /// Unregister a WebSocket connection
        /// </summary>
        private static void UnregisterConnection(WebSocket webSocket)
        {
            // Remove from socket-order dictionary
            if (_socketOrders.TryRemove(webSocket, out string orderId))
            {
                // Remove from order-sockets dictionary
                if (_orderSockets.TryGetValue(orderId, out var sockets))
                {
                    sockets.Remove(webSocket);
                    
                    // If no more sockets for this order, remove the order entry
                    if (sockets.Count == 0)
                    {
                        _orderSockets.TryRemove(orderId, out _);
                    }
                }
                
                Logger.GetInstance().Info($"WebSocket connection unregistered for order {orderId}");
            }
            
            // Cancel and remove ping timer
            if (_socketCancellationTokens.TryRemove(webSocket, out var cts))
            {
                cts.Cancel();
                cts.Dispose();
            }
        }
        
        /// <summary>
        /// Process incoming WebSocket messages
        /// </summary>
        private static async Task ProcessMessagesAsync(WebSocket webSocket, string orderId)
        {
            var buffer = new byte[1024 * 4];
            WebSocketReceiveResult result = null;
            
            try
            {
                while (webSocket.State == WebSocketState.Open)
                {
                    result = await webSocket.ReceiveAsync(new ArraySegment<byte>(buffer), CancellationToken.None);
                    
                    if (result.MessageType == WebSocketMessageType.Close)
                    {
                        await webSocket.CloseAsync(WebSocketCloseStatus.NormalClosure, "Connection closed by client", CancellationToken.None);
                        break;
                    }
                    
                    if (result.MessageType == WebSocketMessageType.Text)
                    {
                        var message = Encoding.UTF8.GetString(buffer, 0, result.Count);
                        await HandleMessageAsync(webSocket, orderId, message);
                    }
                }
            }
            catch (WebSocketException ex)
            {
                Logger.GetInstance().Error($"WebSocket error for order {orderId}: {ex.Message}");
            }
            catch (Exception ex)
            {
                Logger.GetInstance().Error($"Error processing WebSocket messages for order {orderId}: {ex.Message}");
            }
            finally
            {
                UnregisterConnection(webSocket);
                
                if (webSocket.State != WebSocketState.Closed && webSocket.State != WebSocketState.Aborted)
                {
                    try
                    {
                        await webSocket.CloseAsync(WebSocketCloseStatus.EndpointUnavailable, "Server error", CancellationToken.None);
                    }
                    catch
                    {
                        // Ignore errors during close
                    }
                }
            }
        }
        
        /// <summary>
        /// Handle an incoming message from the client
        /// </summary>
        private static async Task HandleMessageAsync(WebSocket webSocket, string orderId, string message)
        {
            try
            {
                var data = JsonConvert.DeserializeObject<dynamic>(message);
                string messageType = data?.type?.ToString();
                
                switch (messageType)
                {
                    case "check_status":
                        await CheckAndSendPaymentStatusAsync(webSocket, orderId);
                        break;
                        
                    case "pong":
                        // Client responded to ping, nothing to do
                        break;
                        
                    default:
                        Logger.GetInstance().Info($"Received unknown message type from client: {messageType}");
                        break;
                }
            }
            catch (Exception ex)
            {
                Logger.GetInstance().Error($"Error handling WebSocket message: {ex.Message}");
                await SendErrorMessageAsync(webSocket, "Invalid message format");
            }
        }
        
        /// <summary>
        /// Check payment status and send update to client
        /// </summary>
        private static async Task CheckAndSendPaymentStatusAsync(WebSocket webSocket, string orderId)
        {
            try
            {
                // Get payment status from the database or payment gateway
                var paymentStatus = await GetPaymentStatusAsync(orderId);
                
                // Send status update to client
                await SendMessageAsync(webSocket, new
                {
                    type = "payment_status",
                    status = paymentStatus
                });
            }
            catch (Exception ex)
            {
                Logger.GetInstance().Error($"Error checking payment status for order {orderId}: {ex.Message}");
                await SendErrorMessageAsync(webSocket, "Error checking payment status");
            }
        }
        
        /// <summary>
        /// Get payment status from the database or payment gateway
        /// </summary>
        private static async Task<JsonResponse> GetPaymentStatusAsync(string orderId)
        {
            try
            {
                // Get payment domain from configuration
                string paymentDomain = I2e1ConfigurationManager.GetMicroserviceDomainUrl("payment");
                
                // Call the same API endpoint that the client uses
                using (var httpClient = new HttpClient())
                {
                    // Default values for planType and pg
                    int planType = 0;
                    int pg = 0;
                    
                    // Make the API request
                    var apiUrl = $"{paymentDomain}/api/WiomApi/checkPaymentStatus?transactionId={orderId}&planType={planType}&pg={pg}";
                    var httpResponse = await httpClient.GetAsync(apiUrl);
                    
                    if (!httpResponse.IsSuccessStatusCode)
                    {
                        return null;
                    }
                    
                    // Parse the JSON response
                    var jsonContent = await httpResponse.Content.ReadAsStringAsync();
                    var response = JsonConvert.DeserializeObject<JsonResponse>(jsonContent);
                    
                    
                    return response;
                }
            }
            catch (Exception ex)
            {
                Logger.GetInstance().Error($"Error in GetPaymentStatusAsync for order {orderId}: {ex.Message}");
                return null;
            }
        }
        
        /// <summary>
        /// Start a timer to send periodic pings to keep the connection alive
        /// </summary>
        private static async Task StartPingTimerAsync(WebSocket webSocket, CancellationToken cancellationToken)
        {
            try
            {
                while (!cancellationToken.IsCancellationRequested)
                {
                    await Task.Delay(TimeSpan.FromSeconds(30), cancellationToken);
                    if (webSocket.State == WebSocketState.Open)
                    {
                        await SendMessageAsync(webSocket, new { type = "ping" });
                    }
                }
            }
            catch (OperationCanceledException)
            {
                // Timer was cancelled, do nothing
            }
            catch (Exception ex)
            {
                Logger.GetInstance().Error($"Error in ping timer: {ex.Message}");
            }
        }

        /// <summary>
        /// Start a timer to periodically check and send payment status
        /// </summary>
        private static async Task StartPaymentStatusCheckTimerAsync(WebSocket webSocket, string orderId, CancellationToken cancellationToken)
        {
            try
            {
                while (!cancellationToken.IsCancellationRequested)
                {
                    if (webSocket.State == WebSocketState.Open)
                    {
                        await CheckAndSendPaymentStatusAsync(webSocket, orderId);
                    }
                    await Task.Delay(TimeSpan.FromSeconds(5), cancellationToken);
                }
            }
            catch (OperationCanceledException)
            {
                // Timer was cancelled, do nothing
            }
            catch (Exception ex)
            {
                Logger.GetInstance().Error($"Error in payment status check timer: {ex.Message}");
            }
        }
        
        /// <summary>
        /// Send a message to a WebSocket client
        /// </summary>
        private static async Task SendMessageAsync(WebSocket webSocket, object data)
        {
            if (webSocket.State != WebSocketState.Open)
                return;
                
            try
            {
                var json = JsonConvert.SerializeObject(data);
                var bytes = Encoding.UTF8.GetBytes(json);
                await webSocket.SendAsync(new ArraySegment<byte>(bytes), WebSocketMessageType.Text, true, CancellationToken.None);
            }
            catch (Exception ex)
            {
                Logger.GetInstance().Error($"Error sending WebSocket message: {ex.Message}");
            }
        }
        
        /// <summary>
        /// Send an error message to a WebSocket client
        /// </summary>
        private static async Task SendErrorMessageAsync(WebSocket webSocket, string errorMessage)
        {
            await SendMessageAsync(webSocket, new
            {
                type = "error",
                message = errorMessage,
                timestamp = DateTime.UtcNow
            });
        }
        
        /// <summary>
        /// Broadcast a payment status update to all clients for a specific order
        /// </summary>
        public static async Task BroadcastPaymentStatusUpdateAsync(string orderId, string status, string errorInfo = null)
        {
            if (_orderSockets.TryGetValue(orderId, out var sockets))
            {
                var message = new
                {
                    type = "payment_status",
                    status = status.ToLowerInvariant(),
                    timestamp = DateTime.UtcNow,
                    orderId = orderId,
                    error = errorInfo
                };
                
                var tasks = new List<Task>();
                foreach (var socket in sockets.ToList())
                {
                    if (socket.State == WebSocketState.Open)
                    {
                        tasks.Add(SendMessageAsync(socket, message));
                    }
                }
                
                await Task.WhenAll(tasks);
            }
        }
    }
}
