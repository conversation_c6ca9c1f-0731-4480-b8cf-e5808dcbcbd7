using i2e1_basics.Utilities;
using i2e1_core.Utilities;
using Microsoft.AspNetCore.Http;
using System;
using System.Net.WebSockets;
using System.Threading.Tasks;

namespace i2e1_login.WebSockets
{
    /// <summary>
    /// Middleware for handling WebSocket connections for payment status updates
    /// </summary>
    public class PaymentStatusWebSocketMiddleware
    {
        private readonly RequestDelegate _next;

        public PaymentStatusWebSocketMiddleware(RequestDelegate next)
        {
            _next = next;
        }

        public async Task InvokeAsync(HttpContext context)
        {
            if (context.Request.Path == "/ws/payment")
            {
                if (context.WebSockets.IsWebSocketRequest)
                {
                    try
                    {
                        WebSocket webSocket = await context.WebSockets.AcceptWebSocketAsync();
                        Logger.GetInstance().Info("WebSocket connection accepted");
                        
                        await PaymentStatusWebSocketHandler.ProcessWebSocketAsync(context, webSocket);
                    }
                    catch (Exception ex)
                    {
                        Logger.GetInstance().Error($"Error handling WebSocket request: {ex.Message}");
                        context.Response.StatusCode = 500;
                    }
                }
                else
                {
                    context.Response.StatusCode = 400;
                }
            }
            else
            {
                await _next(context);
            }
        }
    }
}
