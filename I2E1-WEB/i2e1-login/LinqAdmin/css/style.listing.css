.container#listing-table thead {
    background-color: #ddd;
}

.container#listing-table thead tr th, .container#listing-table tr td {
    font-size: 12px;
    vertical-align: top;
}
tr:nth-child(even) {
    background-color: #f2f2f2;
}
#desktop-listing-search .input-field input[type="search"] {
    height: inherit;
    padding-left: 4rem;
    width: calc(100% - 4rem);
    border: 0;
    -webkit-box-shadow: none;
    box-shadow: none;
}

body {
    display: flex;
    min-height: 100vh;
    flex-direction: column;
}

main {
    flex: 1 0 auto;
}

.icon-button {
    width: 24px;
    height: 24px;
}

.icon-button i {
    color: #000;
}

.icon-button:hover {
    width: 24px;
    height: 24px;
    cursor: pointer;
    color: #fff;
    background-color: #000;
    border-radius: 3px;
}

.icon-button:hover i {
    color: #fff;
    background-color: #000;
    border-radius: 3px;
}

thead tr th {
    font-size: 14px !important;
}

a.btn-small {
    height: 28px !important;
    padding: 0 12px !important;
}