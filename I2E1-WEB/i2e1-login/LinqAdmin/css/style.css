/* Custom Stylesheet */
/**
 * Use this file to override Materialize files so you can update
 * the core Materialize files in the future
 *
 * Made By MaterializeCSS.com
 */

.icon-block {
  padding: 0 15px;
}
.icon-block .material-icons {
	font-size: inherit;
}

.linq-blue {
    background-color: #52a2f2 !important;
}

.linq-orange, .orange {
    background-color: #f0ba13 !important;
}

.orange-text {
    color: #f0ba13 !important;
}

.blue-text {
    color: #52a2f2 !important;
}

th, td {
    padding: 5px 5px !important;
}

.subcategory-image {
    position: relative;
    display: inline-block;
}

.subcategory-image:hover .options {
    display: block;
}

.options {
    padding-top: 7px;
    padding-right: 7px;
    position: absolute;
    right: 0;
    top: 0;
    display: none;
}

.options a {
    border-radius: 3px;
    padding: 3px;
    color: #fff;
    font-size: 12px;
}

.chip .edit, .chip .delete {
    cursor: pointer;
    float: right;
    font-size: 16px;
    line-height: 32px;
    padding-left: 8px;
}

.chip .delete:hover {
    font-size: 18px !important;
}
.chip .edit:hover {
    font-size: 18px !important;
}
.subcategory-image img {
    width: 130px;
}

body {
    display: flex;
    min-height: 100vh;
    flex-direction: column;
}

main {
    flex: 1 0 auto;
}

input[type="number"]::-webkit-outer-spin-button, input[type="number"]::-webkit-inner-spin-button {
    -webkit-appearance: none;
    margin: 0;
}

input[type="number"] {
    -moz-appearance: textfield;
}

.inline-compact {
    margin-bottom: 0px;
    margin-top: 0px;
}

.inline-compact input {
    height: 2.5rem;
}

.hover-pointer {
    cursor: pointer !important;
}

.no-margin-bottom {
    margin-bottom: 0px !important;
}