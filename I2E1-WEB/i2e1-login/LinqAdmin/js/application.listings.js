// Listings
Vue.component('listing-header', {
    template: ``
});
Vue.component('listing-row', {
    props: ['listing', 'idx'],
    template: `<tr>
        <td>{{listing.listingId}} <br/><br/><b v-if="listing.nasid && listing.nasid > 0">Nasid: {{listing.nasid}}</b></td>
        <td>{{listing.mobile}}</td>
        <td >
            <b>{{listing.shopName}}</b>
            <br/>
            {{listing.shopAddress}}, {{listing.shopLocality}}
        </td>
        <td>{{listing.shopCity}}, {{listing.shopState}}</td>
        <td>
            <table>
                <tr>
                    <td>
                        <a data-position="left" data-tooltip="Edit Linq" v-on:click="$emit('launch-edit-listing', listing)" class="btn-small tooltipped">
                            <i class="material-icons">edit</i>
                        </a>
                    </td>
                    <td>
                        <a data-position="right" data-tooltip="Owner/Moderator Edit" v-on:click="$emit('launch-transfer-listing', listing)" class="btn-small tooltipped">
                            <i class="material-icons">shuffle</i>
                        </a>
                    </td>
                </tr>
                <tr>
                    <td>
                        <a data-position="left" data-tooltip="Linq Assets" v-on:click="$emit('launch-listing-assets', listing)" class="btn-small tooltipped">
                            <i class="material-icons">border_horizontal</i>
                        </a>
                    </td>
                    <td>
                        <a data-position="right" data-tooltip="Click to Un-Hide Linq" v-if="!listing.active" v-on:click="$emit('toggle-listing-active', listing, idx)" class="btn-small tooltipped">
                            <i class="material-icons" style="color:red;">visibility_off</i>
                        </a>
                        <a data-position="right" data-tooltip="Click to Hide Linq" v-if="listing.active" v-on:click="$emit('toggle-listing-active', listing, idx)" class="btn-small tooltipped">
                            <i class="material-icons" style="color:blue;">visibility</i>
                        </a>
                    </td>
                </tr>
                <tr>
                    <td>
                        <a data-position="left" data-tooltip="Delete Linq" v-on:click="$emit('delete-listing', listing, idx)" class="btn-small tooltipped" style="background-color: white;">
                            <i class="material-icons" style="color: red">delete</i>
                        </a>
                    </td>
                </tr>
            </table>
        </td>
    </tr>`
});
Vue.component('listing-transfer-modal', {
    props: ['listing', 'userinfo', 'ownerinfo', 'admins'],
    template: `<div id="listing-transfer" class="modal modal-fixed-footer">
        <div class= "modal-content">
            <h5>Listing Ownership</h5>
            <br/>
            <!-- Ownership -->
            <form v-on:submit.prevent>
            <div class="row">
                <!-- Current Owner Data -->
                <div class="col s12 m6">
                    <div class="row">
                        <h6>Current Owner Details</h6>
                        <span style="font-weight:bold;color:#999;">Name: </span><span>{{admins.owner.name ? admins.owner.name : 'Not Available'}}</span><br/>
                        <span style="font-weight:bold;color:#999;">Mobile: </span><span>{{admins.owner.mobile ? admins.owner.mobile: 'Not Available'}}</span><br/>
                        <span style="font-weight:bold;color:#999;">Email: </span><span>{{admins.owner.email ? admins.owner.email : 'Not Available'}}</span><br/>
                        <span style="font-weight:bold;color:#999;">Linq User?: </span><span>{{admins.owner.isLinqUser ? 'Yes' : 'No'}}</span><br/>
                    </div>
                    <div class="row">
                        <div class="col s12">
                            <h6>Transferee Details</h6>
                            <div class="row no-margin-bottom">
                                <div class="input-field col s6">
                                    <input v-model="admins.owner.transferee" placeholder="" id="transfer_to" type="number" class="validate" maxlength="12">
                                    <label for="transfer_to">Transfer To</label>
                                </div>
                                <div class="input-field col s6">
                                    <button v-on:click="$emit('get-user-info', admins.owner)" class="waves-effect waves-red btn-small">Check</button>
                                </div>
                            </div>
                            <div class="row">
                                <span style="font-weight:bold;color:#999;">Name: </span><span>{{userinfo.name ? userinfo.name : 'Not Available'}}</span><br/>
                                <span style="font-weight:bold;color:#999;">Email: </span><span>{{userinfo.email ? userinfo.email : 'Not Available'}}</span><br/>
                                <span style="font-weight:bold;color:#999;">Linq User?: </span><span>{{userinfo.isLinqUser ? 'Yes' : 'No'}}</span><br/>
                            </div>
                        </div>
                    </div>
                </div>
                <!-- Moderators -->
                <div class="col s12 m6">
                    <h6>Moderators</h6>
                    <div class="row">
                        <div class="input-field inline-compact" v-for="(moderator, index) in admins.moderators">
                            <i v-on:click="admins.moderators.splice(index, 1)" class="material-icons prefix hover-pointer">delete_forever</i>
                            <input placeholder="Moderator #" v-model="moderator.mobile" type="number" class="validate">
                        </div>
                    </div>
                    <div class="row">
                        <div class="input-field col s12">
                            <button v-on:click="admins.moderators.push({mobile:''})" class="waves-effect waves-red btn-small">Add</button>
                        </div>
                    </div>
                </div>
            </div>
            </form>
        </div>
        <div class="modal-footer">
            <button href="#!" class="modal-close waves-effect waves-orange btn-small">Cancel</button>
            <button v-on:click="$emit('transfer-listing', admins)" class="waves-effect waves-green btn-small">Save</button>
        </div>
    </div>`
});
Vue.component('desktop-listing-search', {
    props: ['searchphrase'],
    template: `<form id="desktop-listing-search" class="hide-on-small-only show-on-med-and-up" @submit.prevent="searchListings">
                    <div class="input-field">
                        <input id="desktop_search_listing_name" type="search" v-on:keyup.enter="$emit('search-listings', searchphrase)" v-model="searchphrase.search" placeholder="Search by Id or Mobile or Word" required>
                        <label class="label-icon" for="desktop_search_listing_name">
                            <i class="material-icons">search</i>
                        </label>
                    </div>
                </form>`
});
Vue.component('listing-search-modal', {
    props: ['searchphrase'],
    template: `<div id="listing-search" class="modal bottom-sheet">
        <div class= "modal-content">
            <div class="col s6">
                <form class="col s12" @submit.prevent="searchListings">
                    <div class="row">
                        <div class="input-field col s12">
                            <input v-on:keyup.enter="$emit('search-listings', searchphrase)" v-model="searchphrase.search" placeholder="Search Listings" id="search_listing_name" type="text" class="validate" required>
                            <label for="search_listing_name">
                                <i class="material-icons">search</i>
                            </label>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>`
});
const Axios = axios.create();
Axios.interceptors.response.use(function (response) {
    return response;
}, function (error) {
    if (error.response && error.response.status && error.response.status === 403)
        window.location("/LinqAdmin/Login");
    else
        return Promise.reject(error);
});
var app = new Vue({
        el: '#linq-admin',
        data: {
            listingStartIndex: 0,
            listingEndIndex: 100,
            listings: [],
            currentListings: [],
            totalListings: 0,
            searchPhrase: {
                search: ""
            },
            searchMode: false,
            listingToTransfer: {
                listingId: 0,
                shopName: "",
                shopAddress: "",
                shopLocality: "",
                shopCity: "",
                shopState: "",
                mobile: "",
                oldMobile: ""
            },
            userInfo: {
                mobile: "",
                name: "",
                email: "",
                isLinqUser: false
            },
            ownerInfo: {
                mobile: "",
                name: "",
                email: "",
                isLinqUser: false
            },
            listingAdmins: {
                listingId: 0,
                owner: {
                    mobile: "",
                    name: "",
                    email: "",
                    isLinqUser: false,
                    transferee: ""
                },
                moderators: []
            },
            linqAdminInfo: {
                firstName: 'Admin',
                lastName: 'Linq',
                picture: ''
            }
        },
        created: function () {
            $('#login').hide();
            $('#linq-admin').show();
            this.getAllListings();
            this.getListings(1, 100);
            this.getLinqListingData();
            this.getLinqAdminInfo();
        },
        mounted: function () {
            $('#listing-search').modal();
            $('#listing-transfer').modal();
        },
        updated: function () {
            $('.tooltipped').tooltip();
        },
        methods: {
            getLinqAdminInfo: function () {
                var _self = this;
                axios.get('/LinqAdmin/GetLinqAdminInfo')
                    .then(function (response) {
                        _self.linqAdminInfo = response.data.data;
                    })
                    .catch(function (error) {
                        _self.linqAdminInfo = {
                            firstName: '',
                            lastName: '',
                            picture: ''
                        };
                    });
            },
            logout: function () {
                localStorage.removeItem('linq_admin_token');
                window.location.reload();
            },
            getLinqListingData: function () {
                var _self = this;
                axios.post('/LinqAdmin/GetLinqListingData')
                .then(function (response) {
                    var res = response.data.data;
                    _self.totalListings = res.data.count;
                })
                .catch(function (error) {
                    _self.totalListings = 100;
                });
            },
            getListings: function (start = 1, end = 100) {
                var _self = this;
                axios.post('/LinqAdmin/GetLinqListings', {
                    start: start,
                    end: end
                })
                .then(function (response) {
                    _self.currentListings = response.data.data;
                })
                .catch(function (error) {
                    _self.currentListings = [];
                });
            },
            searchListings: function (search) {
                var _self = this;
                _self.searchMode = true;
                $('#listing-search').modal('close');
                axios.post('/LinqAdmin/SearchLinqListings', {
                    search: search.search
                })
                    .then(function (response) {
                        _self.currentListings = response.data.data;
                    })
                    .catch(function (error) {
                        _self.currentListings = [];
                    });
            },
            endSearchMode: function () {
                var _self = this;
                _self.searchMode = false;
                _self.searchPhrase.search = "";
                _self.getListings();
            },
            getAllListings: function () {
                var _self = this;
                axios.post('/LinqAdmin/GetAllLinqListings')
                .then(function (response) {
                    _self.listings = response.data.data;
                })
                .catch(function (error) {
                    _self.listings = [];
                });
            },
            getUserInfo: function (listing) {
                var _self = this;
                axios.post('/LinqAdmin/GetUserInfo', {
                    mobile: listing.transferee
                })
                .then(function (response) {
                    console.log(response.data.data.data);
                    _self.userInfo = response.data.data.data;
                })
                .catch(function (error) {
                    _self.userInfo = {};
                });
            },
            getLinqAdmins: function (listing) {
                var _self = this;
                axios.post('/LinqAdmin/GetOwnerInfo', {
                    listingId: listing.listingId
                })
                .then(function (response) {
                    console.log(response.data.data.data);
                    _self.listingAdmins = response.data.data.data;
                    _self.listingAdmins.owner.transferee = _self.listingAdmins.owner.mobile;
                })
                .catch(function (error) {
                    _self.listingAdmins = {
                        listingId: listing.listingId,
                        owner: {
                            mobile: listing.mobile,
                            name: null,
                            email: null,
                            isLinqUser: false
                        },
                        moderators: []
                    };
                });
        },
            setLinqActiveStatus: function (listing, index) {
                console.log(listing, index);
                var _self = this;
                axios.post('/LinqAdmin/SetLinqActiveStatus', {
                    listingId: listing.listingId,
                    active: !listing.active
                })
                .then(function (response) {
                    if (_self.searchMode && _self.searchPhrase && _self.searchPhrase.search)
                        _self.searchListings(_self.searchPhrase);
                    else
                        _self.getListings(_self.listingStartIndex, _self.listingEndIndex);
                })
                .catch(function (error) {
                    alert("Unable to change Active state.");
                });
            },
            openEditListing: function (listing) {
                window.open("https://www.mylinq.in/mylinq/" + listing.listingId + "?private=true", "_blank");
            },
            openListingAssets: function (listing) {
                window.open("http://mylinq.in/assets/myresources?update=true&linqId=" + listing.listingId, "_blank");
            },
            openSearchListings: function () {
                var _self = this;
                $('#listing-search').modal('open');
                $("#search_listing_name").focus();
            },
            gotoDesktopSearchListings: function () {
                var _self = this;
                $("#desktop_search_listing_name").focus();
                window.scroll(0, 0);
            },
            openTransferListing: function (listing) {
                var _self = this;
                _self.listingToTransfer = listing;
                _self.getLinqAdmins(listing);
                _self.userInfo = {
                    name: "",
                    email: "",
                    isLinqUser: false
                };
                $('#listing-transfer').modal('open');
            },
            transferListing: function (listingAdmins) {
                var _self = this;
                listingAdmins.owner.mobile = listingAdmins.owner.transferee;
                axios.post('/LinqAdmin/TransferOwnership', {
                    admins: listingAdmins
                })
                .then(function (response) {
                    if (response.data.data.status)
                        alert("Listing Transfer Success!");
                    else
                        alert("Listing Transfer Failed!");
                    if (_self.searchMode && _self.searchPhrase && _self.searchPhrase.search)
                        _self.searchListings(_self.searchPhrase);
                    $('#listing-transfer').modal('close');
                })
                .catch(function (error) {
                    alert("Listing Transfer Failed, Try Again!");
                });
            },
            deleteListing: function (listing, index) {
                var _self = this;
                if (confirm("Do you want to delete Linq?") === true) {
                    axios.post('/LinqAdmin/DeleteLinqListing', {
                        listingId: listing.listingId
                    })
                    .then(function (response) {
                            console.log(response);
                        if (response.data.deleted === listing.listingId) {
                            alert("Listing Deleted!");
                            if (_self.searchMode && _self.searchPhrase && _self.searchPhrase.search)
                                _self.searchListings(_self.searchPhrase);
                            else
                                _self.getListings(_self.listingStartIndex, _self.listingEndIndex);
                        } else {
                            alert("Listing Delete Failed!");
                        }
                    })
                    .catch(function (error) {
                        alert("Listing Delete Failed!");
                    });
                }
            },
            nextPage: function () {
                var _self = this;
                if (_self.listingEndIndex <= _self.totalListings) {
                    var data = {
                        start: _self.listingStartIndex + 100,
                        end: _self.listingEndIndex + 100
                    };
                    axios.post('/LinqAdmin/GetLinqListings', data)
                        .then(function (response) {
                            _self.currentListings = response.data.data;
                            _self.listingStartIndex += 100;
                            _self.listingEndIndex += 100;
                        })
                        .catch(function (error) {

                        });
                } else {
                    alert("You cant get the next page.");
                }
            },
            previousPage: function () {
                var _self = this;
                if (_self.listingStartIndex !== 0) {
                    var data = {
                        start: _self.listingStartIndex - 100,
                        end: _self.listingEndIndex - 100
                    };
                    axios.post('/LinqAdmin/GetLinqListings', data)
                    .then(function (response) {
                        _self.currentListings = response.data.data;
                        _self.listingStartIndex -= 100;
                        _self.listingEndIndex -= 100;
                    })
                    .catch(function (error) {

                    });
                } else {
                    alert("You cant get the previous page.");
                }
            }
        }
    });