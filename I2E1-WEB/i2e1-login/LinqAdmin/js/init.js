(function($){
  $(function(){
    $('.sidenav').sidenav();
      $(".metadata-dropdown-trigger").dropdown();
      var elems = document.querySelectorAll('.fixed-action-btn');
      var instances = M.FloatingActionButton.init(elems, {
          direction: 'top',
          hoverEnabled: false
      });
      var instance = M.FloatingActionButton.getInstance(elems);
      var $table = $('table.striped');
      $table.floatThead({
          position: 'fixed',
          top: 65,
          zIndex: 0,
          autoReflow: true
      });
      $('.tooltipped').tooltip();
  }); // end of document ready
})(jQuery); // end of jQuery name space
