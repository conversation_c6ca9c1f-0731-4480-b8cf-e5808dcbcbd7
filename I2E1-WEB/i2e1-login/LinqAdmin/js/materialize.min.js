/*!
 * Materialize v1.0.0-rc.1 (http://materializecss.com)
 * Copyright 2014-2017 Materialize
 * MIT License (https://raw.githubusercontent.com/Dogfalo/materialize/master/LICENSE)
 */
function _possibleConstructorReturn(t,e){if(!t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!e||"object"!=typeof e&&"function"!=typeof e?t:e}function _inherits(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function, not "+typeof e);t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}),e&&(Object.setPrototypeOf?Object.setPrototypeOf(t,e):t.__proto__=e)}function _classCallCheck(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}var _get=function t(e,i,n){null===e&&(e=Function.prototype);var s=Object.getOwnPropertyDescriptor(e,i);if(void 0===s){var o=Object.getPrototypeOf(e);return null===o?void 0:t(o,i,n)}if("value"in s)return s.value;var a=s.get;if(void 0!==a)return a.call(n)},_createClass=function(){function t(t,e){for(var i=0;i<e.length;i++){var n=e[i];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,n.key,n)}}return function(e,i,n){return i&&t(e.prototype,i),n&&t(e,n),e}}();!function(t){window.cash=t()}(function(){function t(t,e){return e=e||x,H.test(t)?e.getElementsByClassName(t.slice(1)):W.test(t)?e.getElementsByTagName(t):e.querySelectorAll(t)}function e(t){if(!O){var e=(O=x.implementation.createHTMLDocument(null)).createElement("base");e.href=x.location.href,O.head.appendChild(e)}return O.body.innerHTML=t,O.body.childNodes}function i(t){"loading"!==x.readyState?t():x.addEventListener("DOMContentLoaded",t)}function n(n,s){if(!n)return this;if(n.cash&&n!==L)return n;var o,a=n,r=0;if(A(n))a=R.test(n)?x.getElementById(n.slice(1)):P.test(n)?e(n):t(n,s);else if(I(n))return i(n),this;if(!a)return this;if(a.nodeType||a===L)this[0]=a,this.length=1;else for(o=this.length=a.length;r<o;r++)this[r]=a[r];return this}function s(t,e){return new n(t,e)}function o(t,e){for(var i=t.length,n=0;n<i&&!1!==e.call(t[n],t[n],n,t);n++);}function a(t,e){var i=t&&(t.matches||t.webkitMatchesSelector||t.mozMatchesSelector||t.msMatchesSelector||t.oMatchesSelector);return!!i&&i.call(t,e)}function r(t){return A(t)?a:t.cash?function(e){return t.is(e)}:function(t,e){return t===e}}function l(t){return s($.call(t).filter(function(t,e,i){return i.indexOf(t)===e}))}function h(t){return t[F]=t[F]||{}}function d(t,e,i){return h(t)[e]=i}function u(t,e){var i=h(t);return void 0===i[e]&&(i[e]=t.dataset?t.dataset[e]:s(t).attr("data-"+e)),i[e]}function c(t,e){var i=h(t);i?delete i[e]:t.dataset?delete t.dataset[e]:s(t).removeAttr("data-"+name)}function p(t){return A(t)&&t.match(q)}function v(t,e){return t.classList?t.classList.contains(e):new RegExp("(^| )"+e+"( |$)","gi").test(t.className)}function f(t,e,i){t.classList?t.classList.add(e):i.indexOf(" "+e+" ")&&(t.className+=" "+e)}function m(t,e){t.classList?t.classList.remove(e):t.className=t.className.replace(e,"")}function g(t,e){return parseInt(L.getComputedStyle(t[0],null)[e],10)||0}function _(t,e,i){var n=u(t,"_cashEvents")||d(t,"_cashEvents",{});n[e]=n[e]||[],n[e].push(i),t.addEventListener(e,i)}function y(t,e,i){var n,s=u(t,"_cashEvents"),a=s&&s[e];a&&(i?(t.removeEventListener(e,i),(n=a.indexOf(i))>=0&&a.splice(n,1)):(o(a,function(i){t.removeEventListener(e,i)}),a=[]))}function k(t,e){return"&"+encodeURIComponent(t)+"="+encodeURIComponent(e).replace(/%20/g,"+")}function b(t){var e=[];return o(t.options,function(t){t.selected&&e.push(t.value)}),e.length?e:null}function w(t){var e=t.selectedIndex;return e>=0?t.options[e].value:null}function C(t){var e=t.type;if(!e)return null;switch(e.toLowerCase()){case"select-one":return w(t);case"select-multiple":return b(t);case"radio":case"checkbox":return t.checked?t.value:null;default:return t.value?t.value:null}}function E(t,e,i){if(i){var n=t.childNodes[0];t.insertBefore(e,n)}else t.appendChild(e)}function M(t,e,i){var n=A(e);n||!e.length?o(t,n?function(t){return t.insertAdjacentHTML(i?"afterbegin":"beforeend",e)}:function(t,n){return E(t,0===n?e:e.cloneNode(!0),i)}):o(e,function(e){return M(t,e,i)})}var O,x=document,L=window,T=Array.prototype,$=T.slice,B=T.filter,D=T.push,S=function(){},I=function(t){return typeof t==typeof S&&t.call},A=function(t){return"string"==typeof t},R=/^#[\w-]*$/,H=/^\.[\w-]*$/,P=/<.+>/,W=/^\w+$/,j=s.fn=s.prototype=n.prototype={cash:!0,length:0,push:D,splice:T.splice,map:T.map,init:n};Object.defineProperty(j,"constructor",{value:s}),s.parseHTML=e,s.noop=S,s.isFunction=I,s.isString=A,s.extend=j.extend=function(t){t=t||{};var e=$.call(arguments),i=e.length,n=1;for(1===e.length&&(t=this,n=0);n<i;n++)if(e[n])for(var s in e[n])e[n].hasOwnProperty(s)&&(t[s]=e[n][s]);return t},s.extend({merge:function(t,e){for(var i=+e.length,n=t.length,s=0;s<i;n++,s++)t[n]=e[s];return t.length=n,t},each:o,matches:a,unique:l,isArray:Array.isArray,isNumeric:function(t){return!isNaN(parseFloat(t))&&isFinite(t)}});var F=s.uid="_cash"+Date.now();j.extend({data:function(t,e){if(A(t))return void 0===e?u(this[0],t):this.each(function(i){return d(i,t,e)});for(var i in t)this.data(i,t[i]);return this},removeData:function(t){return this.each(function(e){return c(e,t)})}});var q=/\S+/g;j.extend({addClass:function(t){var e=p(t);return e?this.each(function(t){var i=" "+t.className+" ";o(e,function(e){f(t,e,i)})}):this},attr:function(t,e){if(t){if(A(t))return void 0===e?this[0]?this[0].getAttribute?this[0].getAttribute(t):this[0][t]:void 0:this.each(function(i){i.setAttribute?i.setAttribute(t,e):i[t]=e});for(var i in t)this.attr(i,t[i]);return this}},hasClass:function(t){var e=!1,i=p(t);return i&&i.length&&this.each(function(t){return!(e=v(t,i[0]))}),e},prop:function(t,e){if(A(t))return void 0===e?this[0][t]:this.each(function(i){i[t]=e});for(var i in t)this.prop(i,t[i]);return this},removeAttr:function(t){return this.each(function(e){e.removeAttribute?e.removeAttribute(t):delete e[t]})},removeClass:function(t){if(!arguments.length)return this.attr("class","");var e=p(t);return e?this.each(function(t){o(e,function(e){m(t,e)})}):this},removeProp:function(t){return this.each(function(e){delete e[t]})},toggleClass:function(t,e){if(void 0!==e)return this[e?"addClass":"removeClass"](t);var i=p(t);return i?this.each(function(t){var e=" "+t.className+" ";o(i,function(i){v(t,i)?m(t,i):f(t,i,e)})}):this}}),j.extend({add:function(t,e){return l(s.merge(this,s(t,e)))},each:function(t){return o(this,t),this},eq:function(t){return s(this.get(t))},filter:function(t){if(!t)return this;var e=I(t)?t:r(t);return s(B.call(this,function(i){return e(i,t)}))},first:function(){return this.eq(0)},get:function(t){return void 0===t?$.call(this):t<0?this[t+this.length]:this[t]},index:function(t){var e=t?s(t)[0]:this[0],i=t?this:s(e).parent().children();return $.call(i).indexOf(e)},last:function(){return this.eq(-1)}});var N=function(){var t=/(?:^\w|[A-Z]|\b\w)/g,e=/[\s-_]+/g;return function(i){return i.replace(t,function(t,e){return t[0===e?"toLowerCase":"toUpperCase"]()}).replace(e,"")}}(),z=function(){var t={},e=document.createElement("div").style;return function(i){if(i=N(i),t[i])return t[i];var n=i.charAt(0).toUpperCase()+i.slice(1);return o((i+" "+["webkit","moz","ms","o"].join(n+" ")+n).split(" "),function(n){if(n in e)return t[n]=i=t[i]=n,!1}),t[i]}}();s.prefixedProp=z,s.camelCase=N,j.extend({css:function(t,e){if(A(t))return t=z(t),arguments.length>1?this.each(function(i){return i.style[t]=e}):L.getComputedStyle(this[0])[t];for(var i in t)this.css(i,t[i]);return this}}),o(["Width","Height"],function(t){var e=t.toLowerCase();j[e]=function(){return this[0].getBoundingClientRect()[e]},j["inner"+t]=function(){return this[0]["client"+t]},j["outer"+t]=function(e){return this[0]["offset"+t]+(e?g(this,"margin"+("Width"===t?"Left":"Top"))+g(this,"margin"+("Width"===t?"Right":"Bottom")):0)}}),j.extend({off:function(t,e){return this.each(function(i){return y(i,t,e)})},on:function(t,e,n,s){var o;if(!A(t)){for(var r in t)this.on(r,e,t[r]);return this}return I(e)&&(n=e,e=null),"ready"===t?(i(n),this):(e&&(o=n,n=function(t){for(var i=t.target;!a(i,e);){if(i===this||null===i)return i=!1;i=i.parentNode}i&&o.call(i,t)}),this.each(function(e){var i=n;s&&(i=function(){n.apply(this,arguments),y(e,t,i)}),_(e,t,i)}))},one:function(t,e,i){return this.on(t,e,i,!0)},ready:i,trigger:function(t,e){if(document.createEvent){var i=document.createEvent("HTMLEvents");return i.initEvent(t,!0,!1),i=this.extend(i,e),this.each(function(t){return t.dispatchEvent(i)})}}}),j.extend({serialize:function(){var t="";return o(this[0].elements||this,function(e){if(!e.disabled&&"FIELDSET"!==e.tagName){var i=e.name;switch(e.type.toLowerCase()){case"file":case"reset":case"submit":case"button":break;case"select-multiple":var n=C(e);null!==n&&o(n,function(e){t+=k(i,e)});break;default:var s=C(e);null!==s&&(t+=k(i,s))}}}),t.substr(1)},val:function(t){return void 0===t?C(this[0]):this.each(function(e){return e.value=t})}}),j.extend({after:function(t){return s(t).insertAfter(this),this},append:function(t){return M(this,t),this},appendTo:function(t){return M(s(t),this),this},before:function(t){return s(t).insertBefore(this),this},clone:function(){return s(this.map(function(t){return t.cloneNode(!0)}))},empty:function(){return this.html(""),this},html:function(t){if(void 0===t)return this[0].innerHTML;var e=t.nodeType?t[0].outerHTML:t;return this.each(function(t){return t.innerHTML=e})},insertAfter:function(t){var e=this;return s(t).each(function(t,i){var n=t.parentNode,s=t.nextSibling;e.each(function(t){n.insertBefore(0===i?t:t.cloneNode(!0),s)})}),this},insertBefore:function(t){var e=this;return s(t).each(function(t,i){var n=t.parentNode;e.each(function(e){n.insertBefore(0===i?e:e.cloneNode(!0),t)})}),this},prepend:function(t){return M(this,t,!0),this},prependTo:function(t){return M(s(t),this,!0),this},remove:function(){return this.each(function(t){if(t.parentNode)return t.parentNode.removeChild(t)})},text:function(t){return void 0===t?this[0].textContent:this.each(function(e){return e.textContent=t})}});var V=x.documentElement;return j.extend({position:function(){var t=this[0];return{left:t.offsetLeft,top:t.offsetTop}},offset:function(){var t=this[0].getBoundingClientRect();return{top:t.top+L.pageYOffset-V.clientTop,left:t.left+L.pageXOffset-V.clientLeft}},offsetParent:function(){return s(this[0].offsetParent)}}),j.extend({children:function(t){var e=[];return this.each(function(t){D.apply(e,t.children)}),e=l(e),t?e.filter(function(e){return a(e,t)}):e},closest:function(t){return!t||this.length<1?s():this.is(t)?this.filter(t):this.parent().closest(t)},is:function(t){if(!t)return!1;var e=!1,i=r(t);return this.each(function(n){return!(e=i(n,t))}),e},find:function(e){if(!e||e.nodeType)return s(e&&this.has(e).length?e:null);var i=[];return this.each(function(n){D.apply(i,t(e,n))}),l(i)},has:function(e){var i=A(e)?function(i){return 0!==t(e,i).length}:function(t){return t.contains(e)};return this.filter(i)},next:function(){return s(this[0].nextElementSibling)},not:function(t){if(!t)return this;var e=r(t);return this.filter(function(i){return!e(i,t)})},parent:function(){var t=[];return this.each(function(e){e&&e.parentNode&&t.push(e.parentNode)}),l(t)},parents:function(t){var e,i=[];return this.each(function(n){for(e=n;e&&e.parentNode&&e!==x.body.parentNode;)e=e.parentNode,(!t||t&&a(e,t))&&i.push(e)}),l(i)},prev:function(){return s(this[0].previousElementSibling)},siblings:function(t){var e=this.parent().children(t),i=this[0];return e.filter(function(t){return t!==i})}}),s});var Component=function(){function t(e,i,n){_classCallCheck(this,t),i instanceof Element||console.error(Error(i+" is not an HTML Element"));var s=e.getInstance(i);s&&s.destroy(),this.el=i,this.$el=cash(i)}return _createClass(t,null,[{key:"init",value:function(t,e,i){var n=null;if(e instanceof Element)n=new t(e,i);else if(e&&(e.jquery||e.cash||e instanceof NodeList)){for(var s=[],o=0;o<e.length;o++)s.push(new t(e[o],i));n=s}return n}}]),t}();!function(t){t.Package?M={}:t.M={},M.jQueryLoaded=!!t.jQuery}(window),"function"==typeof define&&define.amd?define("M",[],function(){return M}):"undefined"==typeof exports||exports.nodeType||("undefined"!=typeof module&&!module.nodeType&&module.exports&&(exports=module.exports=M),exports.default=M),M.keys={TAB:9,ENTER:13,ESC:27,ARROW_UP:38,ARROW_DOWN:40},M.tabPressed=!1,M.keyDown=!1;var docHandleKeydown=function(t){M.keyDown=!0,t.which!==M.keys.TAB&&t.which!==M.keys.ARROW_DOWN&&t.which!==M.keys.ARROW_UP||(M.tabPressed=!0)},docHandleKeyup=function(t){M.keyDown=!1,t.which!==M.keys.TAB&&t.which!==M.keys.ARROW_DOWN&&t.which!==M.keys.ARROW_UP||(M.tabPressed=!1)},docHandleFocus=function(t){M.keyDown&&document.body.classList.add("keyboard-focused")},docHandleBlur=function(t){document.body.classList.remove("keyboard-focused")};document.addEventListener("keydown",docHandleKeydown,!0),document.addEventListener("keyup",docHandleKeyup,!0),document.addEventListener("focus",docHandleFocus,!0),document.addEventListener("blur",docHandleBlur,!0),M.initializeJqueryWrapper=function(t,e,i){jQuery.fn[e]=function(n){if(t.prototype[n]){var s=Array.prototype.slice.call(arguments,1);if("get"===n.slice(0,3)){var o=this.first()[0][i];return o[n].apply(o,s)}return this.each(function(){var t=this[i];t[n].apply(t,s)})}if("object"==typeof n||!n)return t.init(this,arguments[0]),this;jQuery.error("Method "+n+" does not exist on jQuery."+e)}},M.AutoInit=function(t){var e=t||document.body,i={Autocomplete:e.querySelectorAll(".autocomplete:not(.no-autoinit)"),Carousel:e.querySelectorAll(".carousel:not(.no-autoinit)"),Chips:e.querySelectorAll(".chips:not(.no-autoinit)"),Collapsible:e.querySelectorAll(".collapsible:not(.no-autoinit)"),Datepicker:e.querySelectorAll(".datepicker:not(.no-autoinit)"),Dropdown:e.querySelectorAll(".dropdown-trigger:not(.no-autoinit)"),Materialbox:e.querySelectorAll(".materialboxed:not(.no-autoinit)"),Modal:e.querySelectorAll(".modal:not(.no-autoinit)"),Parallax:e.querySelectorAll(".parallax:not(.no-autoinit)"),Pushpin:e.querySelectorAll(".pushpin:not(.no-autoinit)"),ScrollSpy:e.querySelectorAll(".scrollspy:not(.no-autoinit)"),FormSelect:e.querySelectorAll("select:not(.no-autoinit)"),Sidenav:e.querySelectorAll(".sidenav:not(.no-autoinit)"),Tabs:e.querySelectorAll(".tabs:not(.no-autoinit)"),TapTarget:e.querySelectorAll(".tap-target:not(.no-autoinit)"),Timepicker:e.querySelectorAll(".timepicker:not(.no-autoinit)"),Tooltip:e.querySelectorAll(".tooltipped:not(.no-autoinit)"),FloatingActionButton:e.querySelectorAll(".fixed-action-btn:not(.no-autoinit)")};for(var n in i)M[n].init(i[n])},M.objectSelectorString=function(t){return((t.prop("tagName")||"")+(t.attr("id")||"")+(t.attr("class")||"")).replace(/\s/g,"")},M.guid=function(){function t(){return Math.floor(65536*(1+Math.random())).toString(16).substring(1)}return function(){return t()+t()+"-"+t()+"-"+t()+"-"+t()+"-"+t()+t()+t()}}(),M.escapeHash=function(t){return t.replace(/(:|\.|\[|\]|,|=|\/)/g,"\\$1")},M.elementOrParentIsFixed=function(t){var e=$(t),i=!1;return e.add(e.parents()).each(function(){if("fixed"===$(this).css("position"))return i=!0,!1}),i},M.checkWithinContainer=function(t,e,i){var n={top:!1,right:!1,bottom:!1,left:!1},s=t.getBoundingClientRect(),o=t===document.body?Math.max(s.bottom,window.innerHeight):s.bottom,a=t.scrollLeft,r=t.scrollTop,l=e.left-a,h=e.top-r;return(l<s.left+i||l<i)&&(n.left=!0),(l+e.width>s.right-i||l+e.width>window.innerWidth-i)&&(n.right=!0),(h<s.top+i||h<i)&&(n.top=!0),(h+e.height>o-i||h+e.height>window.innerHeight-i)&&(n.bottom=!0),n},M.checkPossibleAlignments=function(t,e,i,n){var s={top:!0,right:!0,bottom:!0,left:!0,spaceOnTop:null,spaceOnRight:null,spaceOnBottom:null,spaceOnLeft:null},o="visible"===getComputedStyle(e).overflow,a=e.getBoundingClientRect(),r=Math.min(a.height,window.innerHeight),l=Math.min(a.width,window.innerWidth),h=t.getBoundingClientRect(),d=e.scrollLeft,u=e.scrollTop,c=i.left-d,p=i.top-u,v=i.top+h.height-u;return s.spaceOnRight=o?window.innerWidth-(h.left+i.width):l-(c+i.width),s.spaceOnRight<0&&(s.left=!1),s.spaceOnLeft=o?h.right-i.width:c-i.width+h.width,s.spaceOnLeft<0&&(s.right=!1),s.spaceOnBottom=o?window.innerHeight-(h.top+i.height+n):r-(p+i.height+n),s.spaceOnBottom<0&&(s.top=!1),s.spaceOnTop=o?h.bottom-(i.height+n):v-(i.height-n),s.spaceOnTop<0&&(s.bottom=!1),s},M.getOverflowParent=function(t){return null==t?null:t===document.body||"visible"!==getComputedStyle(t).overflow?t:M.getOverflowParent(t.parentElement)},M.getIdFromTrigger=function(t){var e=t.getAttribute("data-target");return e||(e=(e=t.getAttribute("href"))?e.slice(1):""),e},M.getDocumentScrollTop=function(){return window.pageYOffset||document.documentElement.scrollTop||document.body.scrollTop||0},M.getDocumentScrollLeft=function(){return window.pageXOffset||document.documentElement.scrollLeft||document.body.scrollLeft||0};var getTime=Date.now||function(){return(new Date).getTime()};M.throttle=function(t,e,i){var n=void 0,s=void 0,o=void 0,a=null,r=0;i||(i={});var l=function(){r=!1===i.leading?0:getTime(),a=null,o=t.apply(n,s),n=s=null};return function(){var h=getTime();r||!1!==i.leading||(r=h);var d=e-(h-r);return n=this,s=arguments,d<=0?(clearTimeout(a),a=null,r=h,o=t.apply(n,s),n=s=null):a||!1===i.trailing||(a=setTimeout(l,d)),o}};var $jscomp={scope:{}};$jscomp.defineProperty="function"==typeof Object.defineProperties?Object.defineProperty:function(t,e,i){if(i.get||i.set)throw new TypeError("ES3 does not support getters and setters.");t!=Array.prototype&&t!=Object.prototype&&(t[e]=i.value)},$jscomp.getGlobal=function(t){return"undefined"!=typeof window&&window===t?t:"undefined"!=typeof global&&null!=global?global:t},$jscomp.global=$jscomp.getGlobal(this),$jscomp.SYMBOL_PREFIX="jscomp_symbol_",$jscomp.initSymbol=function(){$jscomp.initSymbol=function(){},$jscomp.global.Symbol||($jscomp.global.Symbol=$jscomp.Symbol)},$jscomp.symbolCounter_=0,$jscomp.Symbol=function(t){return $jscomp.SYMBOL_PREFIX+(t||"")+$jscomp.symbolCounter_++},$jscomp.initSymbolIterator=function(){$jscomp.initSymbol();var t=$jscomp.global.Symbol.iterator;t||(t=$jscomp.global.Symbol.iterator=$jscomp.global.Symbol("iterator")),"function"!=typeof Array.prototype[t]&&$jscomp.defineProperty(Array.prototype,t,{configurable:!0,writable:!0,value:function(){return $jscomp.arrayIterator(this)}}),$jscomp.initSymbolIterator=function(){}},$jscomp.arrayIterator=function(t){var e=0;return $jscomp.iteratorPrototype(function(){return e<t.length?{done:!1,value:t[e++]}:{done:!0}})},$jscomp.iteratorPrototype=function(t){return $jscomp.initSymbolIterator(),t={next:t},t[$jscomp.global.Symbol.iterator]=function(){return this},t},$jscomp.array=$jscomp.array||{},$jscomp.iteratorFromArray=function(t,e){$jscomp.initSymbolIterator(),t instanceof String&&(t+="");var i=0,n={next:function(){if(i<t.length){var s=i++;return{value:e(s,t[s]),done:!1}}return n.next=function(){return{done:!0,value:void 0}},n.next()}};return n[Symbol.iterator]=function(){return n},n},$jscomp.polyfill=function(t,e,i,n){if(e){for(i=$jscomp.global,t=t.split("."),n=0;n<t.length-1;n++){var s=t[n];s in i||(i[s]={}),i=i[s]}(e=e(n=i[t=t[t.length-1]]))!=n&&null!=e&&$jscomp.defineProperty(i,t,{configurable:!0,writable:!0,value:e})}},$jscomp.polyfill("Array.prototype.keys",function(t){return t||function(){return $jscomp.iteratorFromArray(this,function(t){return t})}},"es6-impl","es3");var $jscomp$this=this;!function(t){M.anime=t()}(function(){function t(t){if(!H.col(t))try{return document.querySelectorAll(t)}catch(t){}}function e(t,e){for(var i=t.length,n=2<=arguments.length?arguments[1]:void 0,s=[],o=0;o<i;o++)if(o in t){var a=t[o];e.call(n,a,o,t)&&s.push(a)}return s}function i(t){return t.reduce(function(t,e){return t.concat(H.arr(e)?i(e):e)},[])}function n(e){return H.arr(e)?e:(H.str(e)&&(e=t(e)||e),e instanceof NodeList||e instanceof HTMLCollection?[].slice.call(e):[e])}function s(t,e){return t.some(function(t){return t===e})}function o(t){var e,i={};for(e in t)i[e]=t[e];return i}function a(t,e){var i,n=o(t);for(i in t)n[i]=e.hasOwnProperty(i)?e[i]:t[i];return n}function r(t,e){var i,n=o(t);for(i in e)n[i]=H.und(t[i])?e[i]:t[i];return n}function l(t){t=t.replace(/^#?([a-f\d])([a-f\d])([a-f\d])$/i,function(t,e,i,n){return e+e+i+i+n+n});var e=/^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(t);return"rgba("+(t=parseInt(e[1],16))+","+parseInt(e[2],16)+","+(e=parseInt(e[3],16))+",1)"}function h(t){function e(t,e,i){return 0>i&&(i+=1),1<i&&--i,i<1/6?t+6*(e-t)*i:.5>i?e:i<2/3?t+(e-t)*(2/3-i)*6:t}s=/hsl\((\d+),\s*([\d.]+)%,\s*([\d.]+)%\)/g.exec(t)||/hsla\((\d+),\s*([\d.]+)%,\s*([\d.]+)%,\s*([\d.]+)\)/g.exec(t);t=parseInt(s[1])/360;var i=parseInt(s[2])/100,n=parseInt(s[3])/100,s=s[4]||1;if(0==i)n=i=t=n;else{var o=.5>n?n*(1+i):n+i-n*i,a=2*n-o,n=e(a,o,t+1/3),i=e(a,o,t);t=e(a,o,t-1/3)}return"rgba("+255*n+","+255*i+","+255*t+","+s+")"}function d(t){if(t=/([\+\-]?[0-9#\.]+)(%|px|pt|em|rem|in|cm|mm|ex|ch|pc|vw|vh|vmin|vmax|deg|rad|turn)?$/.exec(t))return t[2]}function u(t){return-1<t.indexOf("translate")||"perspective"===t?"px":-1<t.indexOf("rotate")||-1<t.indexOf("skew")?"deg":void 0}function c(t,e){return H.fnc(t)?t(e.target,e.id,e.total):t}function p(t,e){if(e in t.style)return getComputedStyle(t).getPropertyValue(e.replace(/([a-z])([A-Z])/g,"$1-$2").toLowerCase())||"0"}function v(t,e){return H.dom(t)&&s(R,e)?"transform":H.dom(t)&&(t.getAttribute(e)||H.svg(t)&&t[e])?"attribute":H.dom(t)&&"transform"!==e&&p(t,e)?"css":null!=t[e]?"object":void 0}function f(t,i){var n=u(i),n=-1<i.indexOf("scale")?1:0+n;if(!(t=t.style.transform))return n;for(var s=[],o=[],a=[],r=/(\w+)\((.+?)\)/g;s=r.exec(t);)o.push(s[1]),a.push(s[2]);return t=e(a,function(t,e){return o[e]===i}),t.length?t[0]:n}function m(t,e){switch(v(t,e)){case"transform":return f(t,e);case"css":return p(t,e);case"attribute":return t.getAttribute(e)}return t[e]||0}function g(t,e){var i=/^(\*=|\+=|-=)/.exec(t);if(!i)return t;var n=d(t)||0;switch(e=parseFloat(e),t=parseFloat(t.replace(i[0],"")),i[0][0]){case"+":return e+t+n;case"-":return e-t+n;case"*":return e*t+n}}function _(t,e){return Math.sqrt(Math.pow(e.x-t.x,2)+Math.pow(e.y-t.y,2))}function y(t){t=t.points;for(var e,i=0,n=0;n<t.numberOfItems;n++){var s=t.getItem(n);0<n&&(i+=_(e,s)),e=s}return i}function k(t){if(t.getTotalLength)return t.getTotalLength();switch(t.tagName.toLowerCase()){case"circle":return 2*Math.PI*t.getAttribute("r");case"rect":return 2*t.getAttribute("width")+2*t.getAttribute("height");case"line":return _({x:t.getAttribute("x1"),y:t.getAttribute("y1")},{x:t.getAttribute("x2"),y:t.getAttribute("y2")});case"polyline":return y(t);case"polygon":var e=t.points;return y(t)+_(e.getItem(e.numberOfItems-1),e.getItem(0))}}function b(t,e){function i(i){return i=void 0===i?0:i,t.el.getPointAtLength(1<=e+i?e+i:0)}var n=i(),s=i(-1),o=i(1);switch(t.property){case"x":return n.x;case"y":return n.y;case"angle":return 180*Math.atan2(o.y-s.y,o.x-s.x)/Math.PI}}function w(t,e){var i,n=/-?\d*\.?\d+/g;if(i=H.pth(t)?t.totalLength:t,H.col(i))if(H.rgb(i)){var s=/rgb\((\d+,\s*[\d]+,\s*[\d]+)\)/g.exec(i);i=s?"rgba("+s[1]+",1)":i}else i=H.hex(i)?l(i):H.hsl(i)?h(i):void 0;else s=(s=d(i))?i.substr(0,i.length-s.length):i,i=e&&!/\s/g.test(i)?s+e:s;return i+="",{original:i,numbers:i.match(n)?i.match(n).map(Number):[0],strings:H.str(t)||e?i.split(n):[]}}function C(t){return t=t?i(H.arr(t)?t.map(n):n(t)):[],e(t,function(t,e,i){return i.indexOf(t)===e})}function E(t){var e=C(t);return e.map(function(t,i){return{target:t,id:i,total:e.length}})}function M(t,e){var i=o(e);if(H.arr(t)){var s=t.length;2!==s||H.obj(t[0])?H.fnc(e.duration)||(i.duration=e.duration/s):t={value:t}}return n(t).map(function(t,i){return i=i?0:e.delay,t=H.obj(t)&&!H.pth(t)?t:{value:t},H.und(t.delay)&&(t.delay=i),t}).map(function(t){return r(t,i)})}function O(t,e){var i,n={};for(i in t){var s=c(t[i],e);H.arr(s)&&1===(s=s.map(function(t){return c(t,e)})).length&&(s=s[0]),n[i]=s}return n.duration=parseFloat(n.duration),n.delay=parseFloat(n.delay),n}function x(t){return H.arr(t)?P.apply(this,t):W[t]}function L(t,e){var i;return t.tweens.map(function(n){var s=(n=O(n,e)).value,o=m(e.target,t.name),a=i?i.to.original:o,a=H.arr(s)?s[0]:a,r=g(H.arr(s)?s[1]:s,a),o=d(r)||d(a)||d(o);return n.from=w(a,o),n.to=w(r,o),n.start=i?i.end:t.offset,n.end=n.start+n.delay+n.duration,n.easing=x(n.easing),n.elasticity=(1e3-Math.min(Math.max(n.elasticity,1),999))/1e3,n.isPath=H.pth(s),n.isColor=H.col(n.from.original),n.isColor&&(n.round=1),i=n})}function T(t,n){return e(i(t.map(function(t){return n.map(function(e){var i=v(t.target,e.name);if(i){var n=L(e,t);e={type:i,property:e.name,animatable:t,tweens:n,duration:n[n.length-1].end,delay:n[0].delay}}else e=void 0;return e})})),function(t){return!H.und(t)})}function $(t,e,i,n){var s="delay"===t;return e.length?(s?Math.min:Math.max).apply(Math,e.map(function(e){return e[t]})):s?n.delay:i.offset+n.delay+n.duration}function B(t){var e,i=a(I,t),n=a(A,t),s=E(t.targets),o=[],l=r(i,n);for(e in t)l.hasOwnProperty(e)||"targets"===e||o.push({name:e,offset:l.offset,tweens:M(t[e],n)});return t=T(s,o),r(i,{children:[],animatables:s,animations:t,duration:$("duration",t,i,n),delay:$("delay",t,i,n)})}function D(t){function i(){return window.Promise&&new Promise(function(t){return u=t})}function n(t){return v.reversed?v.duration-t:t}function s(t){for(var i=0,n={},s=v.animations,o=s.length;i<o;){var a=s[i],r=a.animatable,l=(h=a.tweens)[c=h.length-1];c&&(l=e(h,function(e){return t<e.end})[0]||l);for(var h=Math.min(Math.max(t-l.start-l.delay,0),l.duration)/l.duration,d=isNaN(h)?1:l.easing(h,l.elasticity),h=l.to.strings,u=l.round,c=[],f=void 0,f=l.to.numbers.length,m=0;m<f;m++){var g=void 0,g=l.to.numbers[m],_=l.from.numbers[m],g=l.isPath?b(l.value,d*g):_+d*(g-_);u&&(l.isColor&&2<m||(g=Math.round(g*u)/u)),c.push(g)}if(l=h.length)for(f=h[0],d=0;d<l;d++)u=h[d+1],m=c[d],isNaN(m)||(f=u?f+(m+u):f+(m+" "));else f=c[0];j[a.type](r.target,a.property,f,n,r.id),a.currentValue=f,i++}if(i=Object.keys(n).length)for(s=0;s<i;s++)S||(S=p(document.body,"transform")?"transform":"-webkit-transform"),v.animatables[s].target.style[S]=n[s].join(" ");v.currentTime=t,v.progress=t/v.duration*100}function o(t){v[t]&&v[t](v)}function a(){v.remaining&&!0!==v.remaining&&v.remaining--}function r(t){var e=v.duration,r=v.offset,p=r+v.delay,f=v.currentTime,m=v.reversed,g=n(t);if(v.children.length){var _=v.children,y=_.length;if(g>=v.currentTime)for(var k=0;k<y;k++)_[k].seek(g);else for(;y--;)_[y].seek(g)}(g>=p||!e)&&(v.began||(v.began=!0,o("begin")),o("run")),g>r&&g<e?s(g):(g<=r&&0!==f&&(s(0),m&&a()),(g>=e&&f!==e||!e)&&(s(e),m||a())),o("update"),t>=e&&(v.remaining?(h=l,"alternate"===v.direction&&(v.reversed=!v.reversed)):(v.pause(),v.completed||(v.completed=!0,o("complete"),"Promise"in window&&(u(),c=i()))),d=0)}t=void 0===t?{}:t;var l,h,d=0,u=null,c=i(),v=B(t);return v.reset=function(){var t=v.direction,e=v.loop;for(v.currentTime=0,v.progress=0,v.paused=!0,v.began=!1,v.completed=!1,v.reversed="reverse"===t,v.remaining="alternate"===t&&1===e?2:e,s(0),t=v.children.length;t--;)v.children[t].reset()},v.tick=function(t){l=t,h||(h=l),r((d+l-h)*D.speed)},v.seek=function(t){r(n(t))},v.pause=function(){var t=F.indexOf(v);-1<t&&F.splice(t,1),v.paused=!0},v.play=function(){v.paused&&(v.paused=!1,h=0,d=n(v.currentTime),F.push(v),q||N())},v.reverse=function(){v.reversed=!v.reversed,h=0,d=n(v.currentTime)},v.restart=function(){v.pause(),v.reset(),v.play()},v.finished=c,v.reset(),v.autoplay&&v.play(),v}var S,I={update:void 0,begin:void 0,run:void 0,complete:void 0,loop:1,direction:"normal",autoplay:!0,offset:0},A={duration:1e3,delay:0,easing:"easeOutElastic",elasticity:500,round:0},R="translateX translateY translateZ rotate rotateX rotateY rotateZ scale scaleX scaleY scaleZ skewX skewY perspective".split(" "),H={arr:function(t){return Array.isArray(t)},obj:function(t){return-1<Object.prototype.toString.call(t).indexOf("Object")},pth:function(t){return H.obj(t)&&t.hasOwnProperty("totalLength")},svg:function(t){return t instanceof SVGElement},dom:function(t){return t.nodeType||H.svg(t)},str:function(t){return"string"==typeof t},fnc:function(t){return"function"==typeof t},und:function(t){return void 0===t},hex:function(t){return/(^#[0-9A-F]{6}$)|(^#[0-9A-F]{3}$)/i.test(t)},rgb:function(t){return/^rgb/.test(t)},hsl:function(t){return/^hsl/.test(t)},col:function(t){return H.hex(t)||H.rgb(t)||H.hsl(t)}},P=function(){function t(t,e,i){return(((1-3*i+3*e)*t+(3*i-6*e))*t+3*e)*t}return function(e,i,n,s){if(0<=e&&1>=e&&0<=n&&1>=n){var o=new Float32Array(11);if(e!==i||n!==s)for(var a=0;11>a;++a)o[a]=t(.1*a,e,n);return function(a){if(e===i&&n===s)return a;if(0===a)return 0;if(1===a)return 1;for(var r=0,l=1;10!==l&&o[l]<=a;++l)r+=.1;var l=r+(a-o[--l])/(o[l+1]-o[l])*.1,h=3*(1-3*n+3*e)*l*l+2*(3*n-6*e)*l+3*e;if(.001<=h){for(r=0;4>r&&0!=(h=3*(1-3*n+3*e)*l*l+2*(3*n-6*e)*l+3*e);++r)var d=t(l,e,n)-a,l=l-d/h;a=l}else if(0===h)a=l;else{var l=r,r=r+.1,u=0;do{0<(h=t(d=l+(r-l)/2,e,n)-a)?r=d:l=d}while(1e-7<Math.abs(h)&&10>++u);a=d}return t(a,i,s)}}}}(),W=function(){function t(t,e){return 0===t||1===t?t:-Math.pow(2,10*(t-1))*Math.sin(2*(t-1-e/(2*Math.PI)*Math.asin(1))*Math.PI/e)}var e,i="Quad Cubic Quart Quint Sine Expo Circ Back Elastic".split(" "),n={In:[[.55,.085,.68,.53],[.55,.055,.675,.19],[.895,.03,.685,.22],[.755,.05,.855,.06],[.47,0,.745,.715],[.95,.05,.795,.035],[.6,.04,.98,.335],[.6,-.28,.735,.045],t],Out:[[.25,.46,.45,.94],[.215,.61,.355,1],[.165,.84,.44,1],[.23,1,.32,1],[.39,.575,.565,1],[.19,1,.22,1],[.075,.82,.165,1],[.175,.885,.32,1.275],function(e,i){return 1-t(1-e,i)}],InOut:[[.455,.03,.515,.955],[.645,.045,.355,1],[.77,0,.175,1],[.86,0,.07,1],[.445,.05,.55,.95],[1,0,0,1],[.785,.135,.15,.86],[.68,-.55,.265,1.55],function(e,i){return.5>e?t(2*e,i)/2:1-t(-2*e+2,i)/2}]},s={linear:P(.25,.25,.75,.75)},o={};for(e in n)o.type=e,n[o.type].forEach(function(t){return function(e,n){s["ease"+t.type+i[n]]=H.fnc(e)?e:P.apply($jscomp$this,e)}}(o)),o={type:o.type};return s}(),j={css:function(t,e,i){return t.style[e]=i},attribute:function(t,e,i){return t.setAttribute(e,i)},object:function(t,e,i){return t[e]=i},transform:function(t,e,i,n,s){n[s]||(n[s]=[]),n[s].push(e+"("+i+")")}},F=[],q=0,N=function(){function t(){q=requestAnimationFrame(e)}function e(e){var i=F.length;if(i){for(var n=0;n<i;)F[n]&&F[n].tick(e),n++;t()}else cancelAnimationFrame(q),q=0}return t}();return D.version="2.2.0",D.speed=1,D.running=F,D.remove=function(t){t=C(t);for(var e=F.length;e--;)for(var i=F[e],n=i.animations,o=n.length;o--;)s(t,n[o].animatable.target)&&(n.splice(o,1),n.length||i.pause())},D.getValue=m,D.path=function(e,i){var n=H.str(e)?t(e)[0]:e,s=i||100;return function(t){return{el:n,property:t,totalLength:k(n)*(s/100)}}},D.setDashoffset=function(t){var e=k(t);return t.setAttribute("stroke-dasharray",e),e},D.bezier=P,D.easings=W,D.timeline=function(t){var e=D(t);return e.pause(),e.duration=0,e.add=function(i){return e.children.forEach(function(t){t.began=!0,t.completed=!0}),n(i).forEach(function(i){var n=r(i,a(A,t||{}));n.targets=n.targets||t.targets,i=e.duration;var s=n.offset;n.autoplay=!1,n.direction=e.direction,n.offset=H.und(s)?i:g(s,i),e.began=!0,e.completed=!0,e.seek(n.offset),(n=D(n)).began=!0,n.completed=!0,n.duration>i&&(e.duration=n.duration),e.children.push(n)}),e.seek(0),e.reset(),e.autoplay&&e.restart(),e},e},D.random=function(t,e){return Math.floor(Math.random()*(e-t+1))+t},D}),function(t,e){"use strict";var i={accordion:!0,onOpenStart:void 0,onOpenEnd:void 0,onCloseStart:void 0,onCloseEnd:void 0,inDuration:300,outDuration:300},n=function(n){function s(e,i){_classCallCheck(this,s);var n=_possibleConstructorReturn(this,(s.__proto__||Object.getPrototypeOf(s)).call(this,s,e,i));n.el.M_Collapsible=n,n.options=t.extend({},s.defaults,i),n.$headers=n.$el.children("li").children(".collapsible-header"),n.$headers.attr("tabindex",0),n._setupEventHandlers();var o=n.$el.children("li.active").children(".collapsible-body");return n.options.accordion?o.first().css("display","block"):o.css("display","block"),n}return _inherits(s,n),_createClass(s,[{key:"destroy",value:function(){this._removeEventHandlers(),this.el.M_Collapsible=void 0}},{key:"_setupEventHandlers",value:function(){var t=this;this._handleCollapsibleClickBound=this._handleCollapsibleClick.bind(this),this._handleCollapsibleKeydownBound=this._handleCollapsibleKeydown.bind(this),this.el.addEventListener("click",this._handleCollapsibleClickBound),this.$headers.each(function(e){e.addEventListener("keydown",t._handleCollapsibleKeydownBound)})}},{key:"_removeEventHandlers",value:function(){this.el.removeEventListener("click",this._handleCollapsibleClickBound)}},{key:"_handleCollapsibleClick",value:function(e){var i=t(e.target).closest(".collapsible-header");if(e.target&&i.length){var n=i.closest(".collapsible");if(n[0]===this.el){var s=i.closest("li"),o=n.children("li"),a=s[0].classList.contains("active"),r=o.index(s);a?this.close(r):this.open(r)}}}},{key:"_handleCollapsibleKeydown",value:function(t){13===t.keyCode&&this._handleCollapsibleClickBound(t)}},{key:"_animateIn",value:function(t){var i=this,n=this.$el.children("li").eq(t);if(n.length){var s=n.children(".collapsible-body");e.remove(s[0]),s.css({display:"block",overflow:"hidden",height:0,paddingTop:"",paddingBottom:""});var o=s.css("padding-top"),a=s.css("padding-bottom"),r=s[0].scrollHeight;s.css({paddingTop:0,paddingBottom:0}),e({targets:s[0],height:r,paddingTop:o,paddingBottom:a,duration:this.options.inDuration,easing:"easeInOutCubic",complete:function(t){s.css({overflow:"",paddingTop:"",paddingBottom:"",height:""}),"function"==typeof i.options.onOpenEnd&&i.options.onOpenEnd.call(i,n[0])}})}}},{key:"_animateOut",value:function(t){var i=this,n=this.$el.children("li").eq(t);if(n.length){var s=n.children(".collapsible-body");e.remove(s[0]),s.css("overflow","hidden"),e({targets:s[0],height:0,paddingTop:0,paddingBottom:0,duration:this.options.outDuration,easing:"easeInOutCubic",complete:function(){s.css({height:"",overflow:"",padding:"",display:""}),"function"==typeof i.options.onCloseEnd&&i.options.onCloseEnd.call(i,n[0])}})}}},{key:"open",value:function(e){var i=this,n=this.$el.children("li").eq(e);if(n.length&&!n[0].classList.contains("active")){if("function"==typeof this.options.onOpenStart&&this.options.onOpenStart.call(this,n[0]),this.options.accordion){var s=this.$el.children("li");this.$el.children("li.active").each(function(e){var n=s.index(t(e));i.close(n)})}n[0].classList.add("active"),this._animateIn(e)}}},{key:"close",value:function(t){var e=this.$el.children("li").eq(t);e.length&&e[0].classList.contains("active")&&("function"==typeof this.options.onCloseStart&&this.options.onCloseStart.call(this,e[0]),e[0].classList.remove("active"),this._animateOut(t))}}],[{key:"init",value:function(t,e){return _get(s.__proto__||Object.getPrototypeOf(s),"init",this).call(this,this,t,e)}},{key:"getInstance",value:function(t){return(t.jquery?t[0]:t).M_Collapsible}},{key:"defaults",get:function(){return i}}]),s}(Component);M.Collapsible=n,M.jQueryLoaded&&M.initializeJqueryWrapper(n,"collapsible","M_Collapsible")}(cash,M.anime),function(t,e){"use strict";var i={alignment:"left",autoFocus:!0,constrainWidth:!0,container:null,coverTrigger:!0,closeOnClick:!0,hover:!1,inDuration:150,outDuration:250,onOpenStart:null,onOpenEnd:null,onCloseStart:null,onCloseEnd:null,onItemClick:null},n=function(n){function s(e,i){_classCallCheck(this,s);var n=_possibleConstructorReturn(this,(s.__proto__||Object.getPrototypeOf(s)).call(this,s,e,i));return n.el.M_Dropdown=n,s._dropdowns.push(n),n.id=M.getIdFromTrigger(e),n.dropdownEl=document.getElementById(n.id),n.$dropdownEl=t(n.dropdownEl),n.options=t.extend({},s.defaults,i),n.isOpen=!1,n.isScrollable=!1,n.isTouchMoving=!1,n.focusedIndex=-1,n.filterQuery=[],n.options.container?t(n.options.container).append(n.dropdownEl):n.$el.after(n.dropdownEl),n._makeDropdownFocusable(),n._resetFilterQueryBound=n._resetFilterQuery.bind(n),n._handleDocumentClickBound=n._handleDocumentClick.bind(n),n._handleDocumentTouchmoveBound=n._handleDocumentTouchmove.bind(n),n._handleDropdownClickBound=n._handleDropdownClick.bind(n),n._handleDropdownKeydownBound=n._handleDropdownKeydown.bind(n),n._handleTriggerKeydownBound=n._handleTriggerKeydown.bind(n),n._setupEventHandlers(),n}return _inherits(s,n),_createClass(s,[{key:"destroy",value:function(){this._resetDropdownStyles(),this._removeEventHandlers(),s._dropdowns.splice(s._dropdowns.indexOf(this),1),this.el.M_Dropdown=void 0}},{key:"_setupEventHandlers",value:function(){this.el.addEventListener("keydown",this._handleTriggerKeydownBound),this.dropdownEl.addEventListener("click",this._handleDropdownClickBound),this.options.hover?(this._handleMouseEnterBound=this._handleMouseEnter.bind(this),this.el.addEventListener("mouseenter",this._handleMouseEnterBound),this._handleMouseLeaveBound=this._handleMouseLeave.bind(this),this.el.addEventListener("mouseleave",this._handleMouseLeaveBound),this.dropdownEl.addEventListener("mouseleave",this._handleMouseLeaveBound)):(this._handleClickBound=this._handleClick.bind(this),this.el.addEventListener("click",this._handleClickBound))}},{key:"_removeEventHandlers",value:function(){this.el.removeEventListener("keydown",this._handleTriggerKeydownBound),this.dropdownEl.removeEventListener("click",this._handleDropdownClickBound),this.options.hover?(this.el.removeEventListener("mouseenter",this._handleMouseEnterBound),this.el.removeEventListener("mouseleave",this._handleMouseLeaveBound),this.dropdownEl.removeEventListener("mouseleave",this._handleMouseLeaveBound)):this.el.removeEventListener("click",this._handleClickBound)}},{key:"_setupTemporaryEventHandlers",value:function(){document.body.addEventListener("click",this._handleDocumentClickBound,!0),document.body.addEventListener("touchend",this._handleDocumentClickBound),document.body.addEventListener("touchmove",this._handleDocumentTouchmoveBound),this.dropdownEl.addEventListener("keydown",this._handleDropdownKeydownBound)}},{key:"_removeTemporaryEventHandlers",value:function(){document.body.removeEventListener("click",this._handleDocumentClickBound,!0),document.body.removeEventListener("touchend",this._handleDocumentClickBound),document.body.removeEventListener("touchmove",this._handleDocumentTouchmoveBound),this.dropdownEl.removeEventListener("keydown",this._handleDropdownKeydownBound)}},{key:"_handleClick",value:function(t){t.preventDefault(),this.open()}},{key:"_handleMouseEnter",value:function(){this.open()}},{key:"_handleMouseLeave",value:function(e){var i=e.toElement||e.relatedTarget,n=!!t(i).closest(".dropdown-content").length,s=!1,o=t(i).closest(".dropdown-trigger");o.length&&o[0].M_Dropdown&&o[0].M_Dropdown.isOpen&&(s=!0),s||n||this.close()}},{key:"_handleDocumentClick",value:function(e){var i=this,n=t(e.target);this.options.closeOnClick&&n.closest(".dropdown-content").length&&!this.isTouchMoving?setTimeout(function(){i.close()},0):!n.closest(".dropdown-trigger").length&&n.closest(".dropdown-content").length||setTimeout(function(){i.close()},0),this.isTouchMoving=!1}},{key:"_handleTriggerKeydown",value:function(t){t.which!==M.keys.ARROW_DOWN&&t.which!==M.keys.ENTER||this.isOpen||(t.preventDefault(),this.open())}},{key:"_handleDocumentTouchmove",value:function(e){t(e.target).closest(".dropdown-content").length&&(this.isTouchMoving=!0)}},{key:"_handleDropdownClick",value:function(e){if("function"==typeof this.options.onItemClick){var i=t(e.target).closest("li")[0];this.options.onItemClick.call(this,i)}}},{key:"_handleDropdownKeydown",value:function(e){if(e.which===M.keys.TAB)e.preventDefault(),this.close();else if(e.which!==M.keys.ARROW_DOWN&&e.which!==M.keys.ARROW_UP||!this.isOpen)if(e.which===M.keys.ENTER&&this.isOpen){var i=this.dropdownEl.children[this.focusedIndex],n=t(i).find("a, button").first();n.length?n[0].click():i.click()}else e.which===M.keys.ESC&&this.isOpen&&(e.preventDefault(),this.close());else{e.preventDefault();var s=e.which===M.keys.ARROW_DOWN?1:-1,o=this.focusedIndex,a=!1;do{if(o+=s,this.dropdownEl.children[o]&&-1!==this.dropdownEl.children[o].tabIndex){a=!0;break}}while(o<this.dropdownEl.children.length&&o>=0);a&&(this.focusedIndex=o,this._focusFocusedItem())}var r=String.fromCharCode(e.which).toLowerCase(),l=[9,13,27,38,40];if(r&&-1===l.indexOf(e.which)){this.filterQuery.push(r);var h=this.filterQuery.join(""),d=t(this.dropdownEl).find("li").filter(function(e){return 0===t(e).text().toLowerCase().indexOf(h)})[0];d&&(this.focusedIndex=t(d).index(),this._focusFocusedItem())}this.filterTimeout=setTimeout(this._resetFilterQueryBound,1e3)}},{key:"_resetFilterQuery",value:function(){this.filterQuery=[]}},{key:"_resetDropdownStyles",value:function(){this.$dropdownEl.css({display:"",width:"",height:"",left:"",top:"","transform-origin":"",transform:"",opacity:""})}},{key:"_makeDropdownFocusable",value:function(){this.dropdownEl.tabIndex=0,t(this.dropdownEl).children().each(function(t){t.getAttribute("tabindex")||t.setAttribute("tabindex",0)})}},{key:"_focusFocusedItem",value:function(){this.focusedIndex>=0&&this.focusedIndex<this.dropdownEl.children.length&&this.options.autoFocus&&this.dropdownEl.children[this.focusedIndex].focus()}},{key:"_getDropdownPosition",value:function(){this.el.offsetParent.getBoundingClientRect();var t=this.el.getBoundingClientRect(),e=this.dropdownEl.getBoundingClientRect(),i=e.height,n=e.width,s=t.left-e.left,o=t.top-e.top,a={left:s,top:o,height:i,width:n},r=this.dropdownEl.offsetParent,l=M.checkPossibleAlignments(this.el,r,a,this.options.coverTrigger?0:t.height),h="top",d=this.options.alignment;if(o+=this.options.coverTrigger?0:t.height,this.isScrollable=!1,l.top||(l.bottom?h="bottom":(this.isScrollable=!0,l.spaceOnTop>l.spaceOnBottom?(h="bottom",i+=l.spaceOnTop,o-=l.spaceOnTop):i+=l.spaceOnBottom)),!l[d]){var u="left"===d?"right":"left";l[u]?d=u:l.spaceOnLeft>l.spaceOnRight?(d="right",n+=l.spaceOnLeft,s-=l.spaceOnLeft):(d="left",n+=l.spaceOnRight)}return"bottom"===h&&(o=o-e.height+(this.options.coverTrigger?t.height:0)),"right"===d&&(s=s-e.width+t.width),{x:s,y:o,verticalAlignment:h,horizontalAlignment:d,height:i,width:n}}},{key:"_animateIn",value:function(){var t=this;e.remove(this.dropdownEl),e({targets:this.dropdownEl,opacity:{value:[0,1],easing:"easeOutQuad"},scaleX:[.3,1],scaleY:[.3,1],duration:this.options.inDuration,easing:"easeOutQuint",complete:function(e){if(t.options.autoFocus&&t.dropdownEl.focus(),"function"==typeof t.options.onOpenEnd){var i=e.animatables[0].target;t.options.onOpenEnd.call(i,t.el)}}})}},{key:"_animateOut",value:function(){var t=this;e.remove(this.dropdownEl),e({targets:this.dropdownEl,opacity:{value:0,easing:"easeOutQuint"},scaleX:.3,scaleY:.3,duration:this.options.outDuration,easing:"easeOutQuint",complete:function(e){if(t._resetDropdownStyles(),"function"==typeof t.options.onCloseEnd){e.animatables[0].target;t.options.onCloseEnd.call(t,t.el)}}})}},{key:"_placeDropdown",value:function(){var t=this.options.constrainWidth?this.el.getBoundingClientRect().width:this.dropdownEl.getBoundingClientRect().width;this.dropdownEl.style.width=t+"px";var e=this._getDropdownPosition();this.dropdownEl.style.left=e.x+"px",this.dropdownEl.style.top=e.y+"px",this.dropdownEl.style.height=e.height+"px",this.dropdownEl.style.width=e.width+"px",this.dropdownEl.style.transformOrigin=("left"===e.horizontalAlignment?"0":"100%")+" "+("top"===e.verticalAlignment?"0":"100%")}},{key:"open",value:function(){this.isOpen||(this.isOpen=!0,"function"==typeof this.options.onOpenStart&&this.options.onOpenStart.call(this,this.el),this._resetDropdownStyles(),this.dropdownEl.style.display="block",this._placeDropdown(),this._animateIn(),this._setupTemporaryEventHandlers())}},{key:"close",value:function(){this.isOpen&&(this.isOpen=!1,this.focusedIndex=-1,"function"==typeof this.options.onCloseStart&&this.options.onCloseStart.call(this,this.el),this._animateOut(),this._removeTemporaryEventHandlers(),this.options.autoFocus&&this.el.focus())}},{key:"recalculateDimensions",value:function(){this.isOpen&&(this.$dropdownEl.css({width:"",height:"",left:"",top:"","transform-origin":""}),this._placeDropdown())}}],[{key:"init",value:function(t,e){return _get(s.__proto__||Object.getPrototypeOf(s),"init",this).call(this,this,t,e)}},{key:"getInstance",value:function(t){return(t.jquery?t[0]:t).M_Dropdown}},{key:"defaults",get:function(){return i}}]),s}(Component);n._dropdowns=[],window.M.Dropdown=n,M.jQueryLoaded&&M.initializeJqueryWrapper(n,"dropdown","M_Dropdown")}(cash,M.anime),function(t,e){"use strict";var i={opacity:.5,inDuration:250,outDuration:250,onOpenStart:null,onOpenEnd:null,onCloseStart:null,onCloseEnd:null,preventScrolling:!0,dismissible:!0,startingTop:"4%",endingTop:"10%"},n=function(n){function s(e,i){_classCallCheck(this,s);var n=_possibleConstructorReturn(this,(s.__proto__||Object.getPrototypeOf(s)).call(this,s,e,i));return n.el.M_Modal=n,n.options=t.extend({},s.defaults,i),n.isOpen=!1,n.id=n.$el.attr("id"),n._openingTrigger=void 0,n.$overlay=t('<div class="modal-overlay"></div>'),n.el.tabIndex=0,n._nthModalOpened=0,s._count++,n._setupEventHandlers(),n}return _inherits(s,n),_createClass(s,[{key:"destroy",value:function(){s._count--,this._removeEventHandlers(),this.el.removeAttribute("style"),this.$overlay.remove(),this.el.M_Modal=void 0}},{key:"_setupEventHandlers",value:function(){this._handleOverlayClickBound=this._handleOverlayClick.bind(this),this._handleModalCloseClickBound=this._handleModalCloseClick.bind(this),1===s._count&&document.body.addEventListener("click",this._handleTriggerClick),this.$overlay[0].addEventListener("click",this._handleOverlayClickBound),this.el.addEventListener("click",this._handleModalCloseClickBound)}},{key:"_removeEventHandlers",value:function(){0===s._count&&document.body.removeEventListener("click",this._handleTriggerClick),this.$overlay[0].removeEventListener("click",this._handleOverlayClickBound),this.el.removeEventListener("click",this._handleModalCloseClickBound)}},{key:"_handleTriggerClick",value:function(e){var i=t(e.target).closest(".modal-trigger");if(i.length){var n=M.getIdFromTrigger(i[0]),s=document.getElementById(n).M_Modal;s&&s.open(i),e.preventDefault()}}},{key:"_handleOverlayClick",value:function(){this.options.dismissible&&this.close()}},{key:"_handleModalCloseClick",value:function(e){t(e.target).closest(".modal-close").length&&this.close()}},{key:"_handleKeydown",value:function(t){27===t.keyCode&&this.options.dismissible&&this.close()}},{key:"_handleFocus",value:function(t){this.el.contains(t.target)||this._nthModalOpened!==s._modalsOpen||this.el.focus()}},{key:"_animateIn",value:function(){var i=this;t.extend(this.el.style,{display:"block",opacity:0}),t.extend(this.$overlay[0].style,{display:"block",opacity:0}),e({targets:this.$overlay[0],opacity:this.options.opacity,duration:this.options.inDuration,easing:"easeOutQuad"});var n={targets:this.el,duration:this.options.inDuration,easing:"easeOutCubic",complete:function(){"function"==typeof i.options.onOpenEnd&&i.options.onOpenEnd.call(i,i.el,i._openingTrigger)}};this.el.classList.contains("bottom-sheet")?(t.extend(n,{bottom:0,opacity:1}),e(n)):(t.extend(n,{top:[this.options.startingTop,this.options.endingTop],opacity:1,scaleX:[.8,1],scaleY:[.8,1]}),e(n))}},{key:"_animateOut",value:function(){var i=this;e({targets:this.$overlay[0],opacity:0,duration:this.options.outDuration,easing:"easeOutQuart"});var n={targets:this.el,duration:this.options.outDuration,easing:"easeOutCubic",complete:function(){i.el.style.display="none",i.$overlay.remove(),"function"==typeof i.options.onCloseEnd&&i.options.onCloseEnd.call(i,i.el)}};this.el.classList.contains("bottom-sheet")?(t.extend(n,{bottom:"-100%",opacity:0}),e(n)):(t.extend(n,{top:[this.options.endingTop,this.options.startingTop],opacity:0,scaleX:.8,scaleY:.8}),e(n))}},{key:"open",value:function(t){if(!this.isOpen)return this.isOpen=!0,s._modalsOpen++,this._nthModalOpened=s._modalsOpen,this.$overlay[0].style.zIndex=1e3+2*s._modalsOpen,this.el.style.zIndex=1e3+2*s._modalsOpen+1,this._openingTrigger=t?t[0]:void 0,"function"==typeof this.options.onOpenStart&&this.options.onOpenStart.call(this,this.el,this._openingTrigger),this.options.preventScrolling&&(document.body.style.overflow="hidden"),this.el.classList.add("open"),this.el.insertAdjacentElement("afterend",this.$overlay[0]),this.options.dismissible&&(this._handleKeydownBound=this._handleKeydown.bind(this),this._handleFocusBound=this._handleFocus.bind(this),document.addEventListener("keydown",this._handleKeydownBound),document.addEventListener("focus",this._handleFocusBound,!0)),e.remove(this.el),e.remove(this.$overlay[0]),this._animateIn(),this.el.focus(),this}},{key:"close",value:function(){if(this.isOpen)return this.isOpen=!1,s._modalsOpen--,this._nthModalOpened=0,"function"==typeof this.options.onCloseStart&&this.options.onCloseStart.call(this,this.el),this.el.classList.remove("open"),0===s._modalsOpen&&(document.body.style.overflow=""),this.options.dismissible&&(document.removeEventListener("keydown",this._handleKeydownBound),document.removeEventListener("focus",this._handleFocusBound,!0)),e.remove(this.el),e.remove(this.$overlay[0]),this._animateOut(),this}}],[{key:"init",value:function(t,e){return _get(s.__proto__||Object.getPrototypeOf(s),"init",this).call(this,this,t,e)}},{key:"getInstance",value:function(t){return(t.jquery?t[0]:t).M_Modal}},{key:"defaults",get:function(){return i}}]),s}(Component);n._modalsOpen=0,n._count=0,M.Modal=n,M.jQueryLoaded&&M.initializeJqueryWrapper(n,"modal","M_Modal")}(cash,M.anime),function(t,e){"use strict";var i={inDuration:275,outDuration:200,onOpenStart:null,onOpenEnd:null,onCloseStart:null,onCloseEnd:null},n=function(n){function s(e,i){_classCallCheck(this,s);var n=_possibleConstructorReturn(this,(s.__proto__||Object.getPrototypeOf(s)).call(this,s,e,i));return n.el.M_Materialbox=n,n.options=t.extend({},s.defaults,i),n.overlayActive=!1,n.doneAnimating=!0,n.placeholder=t("<div></div>").addClass("material-placeholder"),n.originalWidth=0,n.originalHeight=0,n.originInlineStyles=n.$el.attr("style"),n.caption=n.el.getAttribute("data-caption")||"",n.$el.before(n.placeholder),n.placeholder.append(n.$el),n._setupEventHandlers(),n}return _inherits(s,n),_createClass(s,[{key:"destroy",value:function(){this._removeEventHandlers(),this.el.M_Materialbox=void 0}},{key:"_setupEventHandlers",value:function(){this._handleMaterialboxClickBound=this._handleMaterialboxClick.bind(this),this.el.addEventListener("click",this._handleMaterialboxClickBound)}},{key:"_removeEventHandlers",value:function(){this.el.removeEventListener("click",this._handleMaterialboxClickBound)}},{key:"_handleMaterialboxClick",value:function(t){!1===this.doneAnimating||this.overlayActive&&this.doneAnimating?this.close():this.open()}},{key:"_handleWindowScroll",value:function(){this.overlayActive&&this.close()}},{key:"_handleWindowResize",value:function(){this.overlayActive&&this.close()}},{key:"_handleWindowEscape",value:function(t){27===t.keyCode&&this.doneAnimating&&this.overlayActive&&this.close()}},{key:"_makeAncestorsOverflowVisible",value:function(){this.ancestorsChanged=t();for(var e=this.placeholder[0].parentNode;null!==e&&!t(e).is(document);){var i=t(e);"visible"!==i.css("overflow")&&(i.css("overflow","visible"),void 0===this.ancestorsChanged?this.ancestorsChanged=i:this.ancestorsChanged=this.ancestorsChanged.add(i)),e=e.parentNode}}},{key:"_animateImageIn",value:function(){var t=this,i={targets:this.el,height:[this.originalHeight,this.newHeight],width:[this.originalWidth,this.newWidth],left:M.getDocumentScrollLeft()+this.windowWidth/2-this.placeholder.offset().left-this.newWidth/2,top:M.getDocumentScrollTop()+this.windowHeight/2-this.placeholder.offset().top-this.newHeight/2,duration:this.options.inDuration,easing:"easeOutQuad",complete:function(){t.doneAnimating=!0,"function"==typeof t.options.onOpenEnd&&t.options.onOpenEnd.call(t,t.el)}};this.maxWidth=this.$el.css("max-width"),this.maxHeight=this.$el.css("max-height"),"none"!==this.maxWidth&&(i.maxWidth=this.newWidth),"none"!==this.maxHeight&&(i.maxHeight=this.newHeight),e(i)}},{key:"_animateImageOut",value:function(){var t=this,i={targets:this.el,width:this.originalWidth,height:this.originalHeight,left:0,top:0,duration:this.options.outDuration,easing:"easeOutQuad",complete:function(){t.placeholder.css({height:"",width:"",position:"",top:"",left:""}),t.attrWidth&&t.$el.attr("width",t.attrWidth),t.attrHeight&&t.$el.attr("height",t.attrHeight),t.$el.removeAttr("style"),t.$el.attr("style",t.originInlineStyles),t.$el.removeClass("active"),t.doneAnimating=!0,t.ancestorsChanged.length&&t.ancestorsChanged.css("overflow",""),"function"==typeof t.options.onCloseEnd&&t.options.onCloseEnd.call(t,t.el)}};e(i)}},{key:"_updateVars",value:function(){this.windowWidth=window.innerWidth,this.windowHeight=window.innerHeight,this.caption=this.el.getAttribute("data-caption")||""}},{key:"open",value:function(){var i=this;this._updateVars(),this.originalWidth=this.el.getBoundingClientRect().width,this.originalHeight=this.el.getBoundingClientRect().height,this.doneAnimating=!1,this.$el.addClass("active"),this.overlayActive=!0,"function"==typeof this.options.onOpenStart&&this.options.onOpenStart.call(this,this.el),this.placeholder.css({width:this.placeholder[0].getBoundingClientRect().width+"px",height:this.placeholder[0].getBoundingClientRect().height+"px",position:"relative",top:0,left:0}),this._makeAncestorsOverflowVisible(),this.$el.css({position:"absolute","z-index":1e3,"will-change":"left, top, width, height"}),this.attrWidth=this.$el.attr("width"),this.attrHeight=this.$el.attr("height"),this.attrWidth&&(this.$el.css("width",this.attrWidth+"px"),this.$el.removeAttr("width")),this.attrHeight&&(this.$el.css("width",this.attrHeight+"px"),this.$el.removeAttr("height")),this.$overlay=t('<div id="materialbox-overlay"></div>').css({opacity:0}).one("click",function(){i.doneAnimating&&i.close()}),this.$el.before(this.$overlay);var n=this.$overlay[0].getBoundingClientRect();this.$overlay.css({width:this.windowWidth+"px",height:this.windowHeight+"px",left:-1*n.left+"px",top:-1*n.top+"px"}),e.remove(this.el),e.remove(this.$overlay[0]),e({targets:this.$overlay[0],opacity:1,duration:this.options.inDuration,easing:"easeOutQuad"}),""!==this.caption&&(this.$photocaption&&e.remove(this.$photoCaption[0]),this.$photoCaption=t('<div class="materialbox-caption"></div>'),this.$photoCaption.text(this.caption),t("body").append(this.$photoCaption),this.$photoCaption.css({display:"inline"}),e({targets:this.$photoCaption[0],opacity:1,duration:this.options.inDuration,easing:"easeOutQuad"}));var s=0,o=this.originalWidth/this.windowWidth,a=this.originalHeight/this.windowHeight;this.newWidth=0,this.newHeight=0,o>a?(s=this.originalHeight/this.originalWidth,this.newWidth=.9*this.windowWidth,this.newHeight=.9*this.windowWidth*s):(s=this.originalWidth/this.originalHeight,this.newWidth=.9*this.windowHeight*s,this.newHeight=.9*this.windowHeight),this._animateImageIn(),this._handleWindowScrollBound=this._handleWindowScroll.bind(this),this._handleWindowResizeBound=this._handleWindowResize.bind(this),this._handleWindowEscapeBound=this._handleWindowEscape.bind(this),window.addEventListener("scroll",this._handleWindowScrollBound),window.addEventListener("resize",this._handleWindowResizeBound),window.addEventListener("keyup",this._handleWindowEscapeBound)}},{key:"close",value:function(){var t=this;this._updateVars(),this.doneAnimating=!1,"function"==typeof this.options.onCloseStart&&this.options.onCloseStart.call(this,this.el),e.remove(this.el),e.remove(this.$overlay[0]),""!==this.caption&&e.remove(this.$photoCaption[0]),window.removeEventListener("scroll",this._handleWindowScrollBound),window.removeEventListener("resize",this._handleWindowResizeBound),window.removeEventListener("keyup",this._handleWindowEscapeBound),e({targets:this.$overlay[0],opacity:0,duration:this.options.outDuration,easing:"easeOutQuad",complete:function(){t.overlayActive=!1,t.$overlay.remove()}}),this._animateImageOut(),""!==this.caption&&e({targets:this.$photoCaption[0],opacity:0,duration:this.options.outDuration,easing:"easeOutQuad",complete:function(){t.$photoCaption.remove()}})}}],[{key:"init",value:function(t,e){return _get(s.__proto__||Object.getPrototypeOf(s),"init",this).call(this,this,t,e)}},{key:"getInstance",value:function(t){return(t.jquery?t[0]:t).M_Materialbox}},{key:"defaults",get:function(){return i}}]),s}(Component);M.Materialbox=n,M.jQueryLoaded&&M.initializeJqueryWrapper(n,"materialbox","M_Materialbox")}(cash,M.anime),function(t){"use strict";var e={responsiveThreshold:0},i=function(i){function n(e,i){_classCallCheck(this,n);var s=_possibleConstructorReturn(this,(n.__proto__||Object.getPrototypeOf(n)).call(this,n,e,i));return s.el.M_Parallax=s,s.options=t.extend({},n.defaults,i),s._enabled=window.innerWidth>s.options.responsiveThreshold,s.$img=s.$el.find("img").first(),s.$img.each(function(){var e=this;e.complete&&t(e).trigger("load")}),s._updateParallax(),s._setupEventHandlers(),s._setupStyles(),n._parallaxes.push(s),s}return _inherits(n,i),_createClass(n,[{key:"destroy",value:function(){n._parallaxes.splice(n._parallaxes.indexOf(this),1),this.$img[0].style.transform="",this._removeEventHandlers(),this.$el[0].M_Parallax=void 0}},{key:"_setupEventHandlers",value:function(){this._handleImageLoadBound=this._handleImageLoad.bind(this),this.$img[0].addEventListener("load",this._handleImageLoadBound),0===n._parallaxes.length&&(n._handleScrollThrottled=M.throttle(n._handleScroll,5),window.addEventListener("scroll",n._handleScrollThrottled),n._handleWindowResizeThrottled=M.throttle(n._handleWindowResize,5),window.addEventListener("resize",n._handleWindowResizeThrottled))}},{key:"_removeEventHandlers",value:function(){this.$img[0].removeEventListener("load",this._handleImageLoadBound),0===n._parallaxes.length&&(window.removeEventListener("scroll",n._handleScrollThrottled),window.removeEventListener("resize",n._handleWindowResizeThrottled))}},{key:"_setupStyles",value:function(){this.$img[0].style.opacity=1}},{key:"_handleImageLoad",value:function(){this._updateParallax()}},{key:"_updateParallax",value:function(){var t=this.$el.height()>0?this.el.parentNode.offsetHeight:500,e=this.$img[0].offsetHeight-t,i=this.$el.offset().top+t,n=this.$el.offset().top,s=M.getDocumentScrollTop(),o=window.innerHeight,a=e*((s+o-n)/(t+o));this._enabled?i>s&&n<s+o&&(this.$img[0].style.transform="translate3D(-50%, "+a+"px, 0)"):this.$img[0].style.transform=""}}],[{key:"init",value:function(t,e){return _get(n.__proto__||Object.getPrototypeOf(n),"init",this).call(this,this,t,e)}},{key:"getInstance",value:function(t){return(t.jquery?t[0]:t).M_Parallax}},{key:"_handleScroll",value:function(){for(var t=0;t<n._parallaxes.length;t++){var e=n._parallaxes[t];e._updateParallax.call(e)}}},{key:"_handleWindowResize",value:function(){for(var t=0;t<n._parallaxes.length;t++){var e=n._parallaxes[t];e._enabled=window.innerWidth>e.options.responsiveThreshold}}},{key:"defaults",get:function(){return e}}]),n}(Component);i._parallaxes=[],M.Parallax=i,M.jQueryLoaded&&M.initializeJqueryWrapper(i,"parallax","M_Parallax")}(cash),function(t,e){"use strict";var i={duration:300,onShow:null,swipeable:!1,responsiveThreshold:1/0},n=function(n){function s(e,i){_classCallCheck(this,s);var n=_possibleConstructorReturn(this,(s.__proto__||Object.getPrototypeOf(s)).call(this,s,e,i));return n.el.M_Tabs=n,n.options=t.extend({},s.defaults,i),n.$tabLinks=n.$el.children("li.tab").children("a"),n.index=0,n._setTabsAndTabWidth(),n._setupActiveTabLink(),n._createIndicator(),n.options.swipeable?n._setupSwipeableTabs():n._setupNormalTabs(),n._setupEventHandlers(),n}return _inherits(s,n),_createClass(s,[{key:"destroy",value:function(){this._removeEventHandlers(),this._indicator.parentNode.removeChild(this._indicator),this.options.swipeable?this._teardownSwipeableTabs():this._teardownNormalTabs(),this.$el[0].M_Tabs=void 0}},{key:"_setupEventHandlers",value:function(){this._handleWindowResizeBound=this._handleWindowResize.bind(this),window.addEventListener("resize",this._handleWindowResizeBound),this._handleTabClickBound=this._handleTabClick.bind(this),this.el.addEventListener("click",this._handleTabClickBound)}},{key:"_removeEventHandlers",value:function(){window.removeEventListener("resize",this._handleWindowResizeBound),this.el.removeEventListener("click",this._handleTabClickBound)}},{key:"_handleWindowResize",value:function(){this._setTabsAndTabWidth(),0!==this.tabWidth&&0!==this.tabsWidth&&(this._indicator.style.left=this._calcLeftPos(this.$activeTabLink)+"px",this._indicator.style.right=this._calcRightPos(this.$activeTabLink)+"px")}},{key:"_handleTabClick",value:function(e){var i=this,n=t(e.target).closest("li.tab"),s=t(e.target).closest("a");if(s.length&&s.parent().hasClass("tab"))if(n.hasClass("disabled"))e.preventDefault();else if(!s.attr("target")){this._setTabsAndTabWidth(),this.$activeTabLink.removeClass("active");var o=this.$content;this.$activeTabLink=s,this.$content=t(M.escapeHash(s[0].hash)),this.$tabLinks=this.$el.children("li.tab").children("a"),this.$activeTabLink.addClass("active");var a=this.index;this.index=Math.max(this.$tabLinks.index(s),0),this.options.swipeable?this._tabsCarousel&&this._tabsCarousel.set(this.index,function(){"function"==typeof i.options.onShow&&i.options.onShow.call(i,i.$content[0])}):this.$content.length&&(this.$content[0].style.display="block",this.$content.addClass("active"),"function"==typeof this.options.onShow&&this.options.onShow.call(this,this.$content[0]),o.length&&!o.is(this.$content)&&(o[0].style.display="none",o.removeClass("active"))),this._animateIndicator(a),e.preventDefault()}}},{key:"_createIndicator",value:function(){var t=this,e=document.createElement("li");e.classList.add("indicator"),this.el.appendChild(e),this._indicator=e,setTimeout(function(){t._indicator.style.left=t._calcLeftPos(t.$activeTabLink)+"px",t._indicator.style.right=t._calcRightPos(t.$activeTabLink)+"px"},0)}},{key:"_setupActiveTabLink",value:function(){this.$activeTabLink=t(this.$tabLinks.filter('[href="'+location.hash+'"]')),0===this.$activeTabLink.length&&(this.$activeTabLink=this.$el.children("li.tab").children("a.active").first()),0===this.$activeTabLink.length&&(this.$activeTabLink=this.$el.children("li.tab").children("a").first()),this.$tabLinks.removeClass("active"),this.$activeTabLink[0].classList.add("active"),this.index=Math.max(this.$tabLinks.index(this.$activeTabLink),0),this.$activeTabLink.length&&(this.$content=t(M.escapeHash(this.$activeTabLink[0].hash)),this.$content.addClass("active"))}},{key:"_setupSwipeableTabs",value:function(){var e=this;window.innerWidth>this.options.responsiveThreshold&&(this.options.swipeable=!1);var i=t();this.$tabLinks.each(function(e){var n=t(M.escapeHash(e.hash));n.addClass("carousel-item"),i=i.add(n)});var n=t('<div class="tabs-content carousel carousel-slider"></div>');i.first().before(n),n.append(i),i[0].style.display="";var s=this.$activeTabLink.closest(".tab").index();this._tabsCarousel=M.Carousel.init(n[0],{fullWidth:!0,noWrap:!0,onCycleTo:function(i){var n=e.index;e.index=t(i).index(),e.$activeTabLink.removeClass("active"),e.$activeTabLink=e.$tabLinks.eq(e.index),e.$activeTabLink.addClass("active"),e._animateIndicator(n),"function"==typeof e.options.onShow&&e.options.onShow.call(e,e.$content[0])}}),this._tabsCarousel.set(s)}},{key:"_teardownSwipeableTabs",value:function(){var t=this._tabsCarousel.$el;this._tabsCarousel.destroy(),t.after(t.children()),t.remove()}},{key:"_setupNormalTabs",value:function(){this.$tabLinks.not(this.$activeTabLink).each(function(e){if(e.hash){var i=t(M.escapeHash(e.hash));i.length&&(i[0].style.display="none")}})}},{key:"_teardownNormalTabs",value:function(){this.$tabLinks.each(function(e){if(e.hash){var i=t(M.escapeHash(e.hash));i.length&&(i[0].style.display="")}})}},{key:"_setTabsAndTabWidth",value:function(){this.tabsWidth=this.$el.width(),this.tabWidth=Math.max(this.tabsWidth,this.el.scrollWidth)/this.$tabLinks.length}},{key:"_calcRightPos",value:function(t){return Math.ceil(this.tabsWidth-t.position().left-t[0].getBoundingClientRect().width)}},{key:"_calcLeftPos",value:function(t){return Math.floor(t.position().left)}},{key:"updateTabIndicator",value:function(){this._animateIndicator(this.index)}},{key:"_animateIndicator",value:function(t){var i=0,n=0;this.index-t>=0?i=90:n=90;var s={targets:this._indicator,left:{value:this._calcLeftPos(this.$activeTabLink),delay:i},right:{value:this._calcRightPos(this.$activeTabLink),delay:n},duration:this.options.duration,easing:"easeOutQuad"};e.remove(this._indicator),e(s)}},{key:"select",value:function(t){var e=this.$tabLinks.filter('[href="#'+t+'"]');e.length&&e.trigger("click")}}],[{key:"init",value:function(t,e){return _get(s.__proto__||Object.getPrototypeOf(s),"init",this).call(this,this,t,e)}},{key:"getInstance",value:function(t){return(t.jquery?t[0]:t).M_Tabs}},{key:"defaults",get:function(){return i}}]),s}(Component);window.M.Tabs=n,M.jQueryLoaded&&M.initializeJqueryWrapper(n,"tabs","M_Tabs")}(cash,M.anime),function(t,e){"use strict";var i={exitDelay:200,enterDelay:0,html:null,margin:5,inDuration:250,outDuration:200,position:"bottom",transitionMovement:10},n=function(n){function s(e,i){_classCallCheck(this,s);var n=_possibleConstructorReturn(this,(s.__proto__||Object.getPrototypeOf(s)).call(this,s,e,i));return n.el.M_Tooltip=n,n.options=t.extend({},s.defaults,i),n.isOpen=!1,n.isHovered=!1,n.isFocused=!1,n._appendTooltipEl(),n._setupEventHandlers(),n}return _inherits(s,n),_createClass(s,[{key:"destroy",value:function(){t(this.tooltipEl).remove(),this._removeEventHandlers(),this.el.M_Tooltip=void 0}},{key:"_appendTooltipEl",value:function(){var t=document.createElement("div");t.classList.add("material-tooltip"),this.tooltipEl=t;var e=document.createElement("div");e.classList.add("tooltip-content"),e.innerHTML=this.options.html,t.appendChild(e),document.body.appendChild(t)}},{key:"_updateTooltipContent",value:function(){this.tooltipEl.querySelector(".tooltip-content").innerHTML=this.options.html}},{key:"_setupEventHandlers",value:function(){this._handleMouseEnterBound=this._handleMouseEnter.bind(this),this._handleMouseLeaveBound=this._handleMouseLeave.bind(this),this._handleFocusBound=this._handleFocus.bind(this),this._handleBlurBound=this._handleBlur.bind(this),this.el.addEventListener("mouseenter",this._handleMouseEnterBound),this.el.addEventListener("mouseleave",this._handleMouseLeaveBound),this.el.addEventListener("focus",this._handleFocusBound,!0),this.el.addEventListener("blur",this._handleBlurBound,!0)}},{key:"_removeEventHandlers",value:function(){this.el.removeEventListener("mouseenter",this._handleMouseEnterBound),this.el.removeEventListener("mouseleave",this._handleMouseLeaveBound),this.el.removeEventListener("focus",this._handleFocusBound,!0),this.el.removeEventListener("blur",this._handleBlurBound,!0)}},{key:"open",value:function(){this.isOpen||(this.isOpen=!0,this.options=t.extend({},this.options,this._getAttributeOptions()),this._updateTooltipContent(),this._setEnterDelayTimeout())}},{key:"close",value:function(){this.isOpen&&(this.isHovered=!1,this.isFocused=!1,this.isOpen=!1,this._setExitDelayTimeout())}},{key:"_setExitDelayTimeout",value:function(){var t=this;clearTimeout(this._exitDelayTimeout),this._exitDelayTimeout=setTimeout(function(){t.isHovered||t.isFocused||t._animateOut()},this.options.exitDelay)}},{key:"_setEnterDelayTimeout",value:function(){var t=this;clearTimeout(this._enterDelayTimeout),this._enterDelayTimeout=setTimeout(function(){(t.isHovered||t.isFocused)&&t._animateIn()},this.options.enterDelay)}},{key:"_positionTooltip",value:function(){var e=this.el,i=this.tooltipEl,n=e.offsetHeight,s=e.offsetWidth,o=i.offsetHeight,a=i.offsetWidth,r=void 0,l=this.options.margin,h=void 0,d=void 0;this.xMovement=0,this.yMovement=0,h=e.getBoundingClientRect().top+M.getDocumentScrollTop(),d=e.getBoundingClientRect().left+M.getDocumentScrollLeft(),"top"===this.options.position?(h+=-o-l,d+=s/2-a/2,this.yMovement=-this.options.transitionMovement):"right"===this.options.position?(h+=n/2-o/2,d+=s+l,this.xMovement=this.options.transitionMovement):"left"===this.options.position?(h+=n/2-o/2,d+=-a-l,this.xMovement=-this.options.transitionMovement):(h+=n+l,d+=s/2-a/2,this.yMovement=this.options.transitionMovement),r=this._repositionWithinScreen(d,h,a,o),t(i).css({top:r.y+"px",left:r.x+"px"})}},{key:"_repositionWithinScreen",value:function(t,e,i,n){var s=M.getDocumentScrollLeft(),o=M.getDocumentScrollTop(),a=t-s,r=e-o,l={left:a,top:r,width:i,height:n},h=this.options.margin+this.options.transitionMovement,d=M.checkWithinContainer(document.body,l,h);return d.left?a=h:d.right&&(a-=a+i-window.innerWidth),d.top?r=h:d.bottom&&(r-=r+n-window.innerHeight),{x:a+s,y:r+o}}},{key:"_animateIn",value:function(){this._positionTooltip(),this.tooltipEl.style.visibility="visible",e.remove(this.tooltipEl),e({targets:this.tooltipEl,opacity:1,translateX:this.xMovement,translateY:this.yMovement,duration:this.options.inDuration,easing:"easeOutCubic"})}},{key:"_animateOut",value:function(){e.remove(this.tooltipEl),e({targets:this.tooltipEl,opacity:0,translateX:0,translateY:0,duration:this.options.outDuration,easing:"easeOutCubic"})}},{key:"_handleMouseEnter",value:function(){this.isHovered=!0,this.isFocused=!1,this.open()}},{key:"_handleMouseLeave",value:function(){this.isHovered=!1,this.isFocused=!1,this.close()}},{key:"_handleFocus",value:function(){M.tabPressed&&(this.isFocused=!0,this.open())}},{key:"_handleBlur",value:function(){this.isFocused=!1,this.close()}},{key:"_getAttributeOptions",value:function(){var t={},e=this.el.getAttribute("data-tooltip"),i=this.el.getAttribute("data-position");return e&&(t.html=e),i&&(t.position=i),t}}],[{key:"init",value:function(t,e){return _get(s.__proto__||Object.getPrototypeOf(s),"init",this).call(this,this,t,e)}},{key:"getInstance",value:function(t){return(t.jquery?t[0]:t).M_Tooltip}},{key:"defaults",get:function(){return i}}]),s}(Component);M.Tooltip=n,M.jQueryLoaded&&M.initializeJqueryWrapper(n,"tooltip","M_Tooltip")}(cash,M.anime),function(t){"use strict";function e(t){return null!==t&&t===t.window}function i(t){return e(t)?t:9===t.nodeType&&t.defaultView}function n(t){var e,n,s={top:0,left:0},o=t&&t.ownerDocument;return e=o.documentElement,void 0!==t.getBoundingClientRect&&(s=t.getBoundingClientRect()),n=i(o),{top:s.top+n.pageYOffset-e.clientTop,left:s.left+n.pageXOffset-e.clientLeft}}function s(t){var e="";for(var i in t)t.hasOwnProperty(i)&&(e+=i+":"+t[i]+";");return e}function o(t){if(!1===d.allowEvent(t))return null;for(var e=null,i=t.target||t.srcElement;null!==i.parentNode;){if(!(i instanceof SVGElement)&&-1!==i.className.indexOf("waves-effect")){e=i;break}i=i.parentNode}return e}function a(e){var i=o(e);null!==i&&(h.show(e,i),"ontouchstart"in t&&(i.addEventListener("touchend",h.hide,!1),i.addEventListener("touchcancel",h.hide,!1)),i.addEventListener("mouseup",h.hide,!1),i.addEventListener("mouseleave",h.hide,!1),i.addEventListener("dragend",h.hide,!1))}var r=r||{},l=document.querySelectorAll.bind(document),h={duration:750,show:function(t,e){if(2===t.button)return!1;var i=e||this,o=document.createElement("div");o.className="waves-ripple",i.appendChild(o);var a=n(i),r=t.pageY-a.top,l=t.pageX-a.left,d="scale("+i.clientWidth/100*10+")";"touches"in t&&(r=t.touches[0].pageY-a.top,l=t.touches[0].pageX-a.left),o.setAttribute("data-hold",Date.now()),o.setAttribute("data-scale",d),o.setAttribute("data-x",l),o.setAttribute("data-y",r);var u={top:r+"px",left:l+"px"};o.className=o.className+" waves-notransition",o.setAttribute("style",s(u)),o.className=o.className.replace("waves-notransition",""),u["-webkit-transform"]=d,u["-moz-transform"]=d,u["-ms-transform"]=d,u["-o-transform"]=d,u.transform=d,u.opacity="1",u["-webkit-transition-duration"]=h.duration+"ms",u["-moz-transition-duration"]=h.duration+"ms",u["-o-transition-duration"]=h.duration+"ms",u["transition-duration"]=h.duration+"ms",u["-webkit-transition-timing-function"]="cubic-bezier(0.250, 0.460, 0.450, 0.940)",u["-moz-transition-timing-function"]="cubic-bezier(0.250, 0.460, 0.450, 0.940)",u["-o-transition-timing-function"]="cubic-bezier(0.250, 0.460, 0.450, 0.940)",u["transition-timing-function"]="cubic-bezier(0.250, 0.460, 0.450, 0.940)",o.setAttribute("style",s(u))},hide:function(t){d.touchup(t);var e=this,i=(e.clientWidth,null),n=e.getElementsByClassName("waves-ripple");if(!(n.length>0))return!1;var o=(i=n[n.length-1]).getAttribute("data-x"),a=i.getAttribute("data-y"),r=i.getAttribute("data-scale"),l=350-(Date.now()-Number(i.getAttribute("data-hold")));l<0&&(l=0),setTimeout(function(){var t={top:a+"px",left:o+"px",opacity:"0","-webkit-transition-duration":h.duration+"ms","-moz-transition-duration":h.duration+"ms","-o-transition-duration":h.duration+"ms","transition-duration":h.duration+"ms","-webkit-transform":r,"-moz-transform":r,"-ms-transform":r,"-o-transform":r,transform:r};i.setAttribute("style",s(t)),setTimeout(function(){try{e.removeChild(i)}catch(t){return!1}},h.duration)},l)},wrapInput:function(t){for(var e=0;e<t.length;e++){var i=t[e];if("input"===i.tagName.toLowerCase()){var n=i.parentNode;if("i"===n.tagName.toLowerCase()&&-1!==n.className.indexOf("waves-effect"))continue;var s=document.createElement("i");s.className=i.className+" waves-input-wrapper";var o=i.getAttribute("style");o||(o=""),s.setAttribute("style",o),i.className="waves-button-input",i.removeAttribute("style"),n.replaceChild(s,i),s.appendChild(i)}}}},d={touches:0,allowEvent:function(t){var e=!0;return"touchstart"===t.type?d.touches+=1:"touchend"===t.type||"touchcancel"===t.type?setTimeout(function(){d.touches>0&&(d.touches-=1)},500):"mousedown"===t.type&&d.touches>0&&(e=!1),e},touchup:function(t){d.allowEvent(t)}};r.displayEffect=function(e){"duration"in(e=e||{})&&(h.duration=e.duration),h.wrapInput(l(".waves-effect")),"ontouchstart"in t&&document.body.addEventListener("touchstart",a,!1),document.body.addEventListener("mousedown",a,!1)},r.attach=function(e){"input"===e.tagName.toLowerCase()&&(h.wrapInput([e]),e=e.parentNode),"ontouchstart"in t&&e.addEventListener("touchstart",a,!1),e.addEventListener("mousedown",a,!1)},t.Waves=r,document.addEventListener("DOMContentLoaded",function(){r.displayEffect()},!1)}(window),function(t,e){"use strict";var i={html:"",displayLength:4e3,inDuration:300,outDuration:375,classes:"",completeCallback:null,activationPercent:.8},n=function(){function n(e){_classCallCheck(this,n),this.options=t.extend({},n.defaults,e),this.message=this.options.html,this.panning=!1,this.timeRemaining=this.options.displayLength,0===n._toasts.length&&n._createContainer(),n._toasts.push(this);var i=this._createToast();i.M_Toast=this,this.el=i,this.$el=t(i),this._animateIn(),this._setTimer()}return _createClass(n,[{key:"_createToast",value:function(){var e=document.createElement("div");return e.classList.add("toast"),this.options.classes.length&&t(e).addClass(this.options.classes),("object"==typeof HTMLElement?this.message instanceof HTMLElement:this.message&&"object"==typeof this.message&&null!==this.message&&1===this.message.nodeType&&"string"==typeof this.message.nodeName)?e.appendChild(this.message):this.message.jquery?t(e).append(this.message[0]):e.innerHTML=this.message,n._container.appendChild(e),e}},{key:"_animateIn",value:function(){e({targets:this.el,top:0,opacity:1,duration:this.options.inDuration,easing:"easeOutCubic"})}},{key:"_setTimer",value:function(){var t=this;this.timeRemaining!==1/0&&(this.counterInterval=setInterval(function(){t.panning||(t.timeRemaining-=20),t.timeRemaining<=0&&t.dismiss()},20))}},{key:"dismiss",value:function(){var t=this;window.clearInterval(this.counterInterval);var i=this.el.offsetWidth*this.options.activationPercent;this.wasSwiped&&(this.el.style.transition="transform .05s, opacity .05s",this.el.style.transform="translateX("+i+"px)",this.el.style.opacity=0),e({targets:this.el,opacity:0,marginTop:-40,duration:this.options.outDuration,easing:"easeOutExpo",complete:function(){"function"==typeof t.options.completeCallback&&t.options.completeCallback(),t.$el.remove(),n._toasts.splice(n._toasts.indexOf(t),1),0===n._toasts.length&&n._removeContainer()}})}}],[{key:"getInstance",value:function(t){return(t.jquery?t[0]:t).M_Toast}},{key:"_createContainer",value:function(){var t=document.createElement("div");t.setAttribute("id","toast-container"),t.addEventListener("touchstart",n._onDragStart),t.addEventListener("touchmove",n._onDragMove),t.addEventListener("touchend",n._onDragEnd),t.addEventListener("mousedown",n._onDragStart),document.addEventListener("mousemove",n._onDragMove),document.addEventListener("mouseup",n._onDragEnd),document.body.appendChild(t),n._container=t}},{key:"_removeContainer",value:function(){document.removeEventListener("mousemove",n._onDragMove),document.removeEventListener("mouseup",n._onDragEnd),t(n._container).remove(),n._container=null}},{key:"_onDragStart",value:function(e){if(e.target&&t(e.target).closest(".toast").length){var i=t(e.target).closest(".toast")[0].M_Toast;i.panning=!0,n._draggedToast=i,i.el.classList.add("panning"),i.el.style.transition="",i.startingXPos=n._xPos(e),i.time=Date.now(),i.xPos=n._xPos(e)}}},{key:"_onDragMove",value:function(t){if(n._draggedToast){t.preventDefault();var e=n._draggedToast;e.deltaX=Math.abs(e.xPos-n._xPos(t)),e.xPos=n._xPos(t),e.velocityX=e.deltaX/(Date.now()-e.time),e.time=Date.now();var i=e.xPos-e.startingXPos,s=e.el.offsetWidth*e.options.activationPercent;e.el.style.transform="translateX("+i+"px)",e.el.style.opacity=1-Math.abs(i/s)}}},{key:"_onDragEnd",value:function(){if(n._draggedToast){var t=n._draggedToast;t.panning=!1,t.el.classList.remove("panning");var e=t.xPos-t.startingXPos,i=t.el.offsetWidth*t.options.activationPercent;Math.abs(e)>i||t.velocityX>1?(t.wasSwiped=!0,t.dismiss()):(t.el.style.transition="transform .2s, opacity .2s",t.el.style.transform="",t.el.style.opacity=""),n._draggedToast=null}}},{key:"_xPos",value:function(t){return t.targetTouches&&t.targetTouches.length>=1?t.targetTouches[0].clientX:t.clientX}},{key:"dismissAll",value:function(){for(var t in n._toasts)n._toasts[t].dismiss()}},{key:"defaults",get:function(){return i}}]),n}();n._toasts=[],n._container=null,n._draggedToast=null,M.Toast=n,M.toast=function(t){return new n(t)}}(cash,M.anime),function(t,e){"use strict";var i={edge:"left",draggable:!0,inDuration:250,outDuration:200,onOpenStart:null,onOpenEnd:null,onCloseStart:null,onCloseEnd:null,preventScrolling:!0},n=function(n){function s(e,i){_classCallCheck(this,s);var n=_possibleConstructorReturn(this,(s.__proto__||Object.getPrototypeOf(s)).call(this,s,e,i));return n.el.M_Sidenav=n,n.id=n.$el.attr("id"),n.options=t.extend({},s.defaults,i),n.isOpen=!1,n.isFixed=n.el.classList.contains("sidenav-fixed"),n.isDragged=!1,n.lastWindowWidth=window.innerWidth,n.lastWindowHeight=window.innerHeight,n._createOverlay(),n._createDragTarget(),n._setupEventHandlers(),n._setupClasses(),n._setupFixed(),s._sidenavs.push(n),n}return _inherits(s,n),_createClass(s,[{key:"destroy",value:function(){this._removeEventHandlers(),this._overlay.parentNode.removeChild(this._overlay),this.dragTarget.parentNode.removeChild(this.dragTarget),this.el.M_Sidenav=void 0,this.el.style.transform="";var t=s._sidenavs.indexOf(this);t>=0&&s._sidenavs.splice(t,1)}},{key:"_createOverlay",value:function(){var t=document.createElement("div");this._closeBound=this.close.bind(this),t.classList.add("sidenav-overlay"),t.addEventListener("click",this._closeBound),document.body.appendChild(t),this._overlay=t}},{key:"_setupEventHandlers",value:function(){0===s._sidenavs.length&&document.body.addEventListener("click",this._handleTriggerClick),this._handleDragTargetDragBound=this._handleDragTargetDrag.bind(this),this._handleDragTargetReleaseBound=this._handleDragTargetRelease.bind(this),this._handleCloseDragBound=this._handleCloseDrag.bind(this),this._handleCloseReleaseBound=this._handleCloseRelease.bind(this),this._handleCloseTriggerClickBound=this._handleCloseTriggerClick.bind(this),this.dragTarget.addEventListener("touchmove",this._handleDragTargetDragBound),this.dragTarget.addEventListener("touchend",this._handleDragTargetReleaseBound),this._overlay.addEventListener("touchmove",this._handleCloseDragBound),this._overlay.addEventListener("touchend",this._handleCloseReleaseBound),this.el.addEventListener("touchmove",this._handleCloseDragBound),this.el.addEventListener("touchend",this._handleCloseReleaseBound),this.el.addEventListener("click",this._handleCloseTriggerClickBound),this.isFixed&&(this._handleWindowResizeBound=this._handleWindowResize.bind(this),window.addEventListener("resize",this._handleWindowResizeBound))}},{key:"_removeEventHandlers",value:function(){1===s._sidenavs.length&&document.body.removeEventListener("click",this._handleTriggerClick),this.dragTarget.removeEventListener("touchmove",this._handleDragTargetDragBound),this.dragTarget.removeEventListener("touchend",this._handleDragTargetReleaseBound),this._overlay.removeEventListener("touchmove",this._handleCloseDragBound),this._overlay.removeEventListener("touchend",this._handleCloseReleaseBound),this.el.removeEventListener("touchmove",this._handleCloseDragBound),this.el.removeEventListener("touchend",this._handleCloseReleaseBound),this.el.removeEventListener("click",this._handleCloseTriggerClickBound),this.isFixed&&window.removeEventListener("resize",this._handleWindowResizeBound)}},{key:"_handleTriggerClick",value:function(e){var i=t(e.target).closest(".sidenav-trigger");if(e.target&&i.length){var n=M.getIdFromTrigger(i[0]),s=document.getElementById(n).M_Sidenav;s&&s.open(i),e.preventDefault()}}},{key:"_startDrag",value:function(t){var i=t.targetTouches[0].clientX;this.isDragged=!0,this._startingXpos=i,this._xPos=this._startingXpos,this._time=Date.now(),this._width=this.el.getBoundingClientRect().width,this._overlay.style.display="block",this._initialScrollTop=this.isOpen?this.el.scrollTop:M.getDocumentScrollTop(),this._verticallyScrolling=!1,e.remove(this.el),e.remove(this._overlay)}},{key:"_dragMoveUpdate",value:function(t){var e=t.targetTouches[0].clientX,i=this.isOpen?this.el.scrollTop:M.getDocumentScrollTop();this.deltaX=Math.abs(this._xPos-e),this._xPos=e,this.velocityX=this.deltaX/(Date.now()-this._time),this._time=Date.now(),this._initialScrollTop!==i&&(this._verticallyScrolling=!0)}},{key:"_handleDragTargetDrag",value:function(t){if(this.options.draggable&&!this._isCurrentlyFixed()&&!this._verticallyScrolling){this.isDragged||this._startDrag(t),this._dragMoveUpdate(t);var e=this._xPos-this._startingXpos,i=e>0?"right":"left";e=Math.min(this._width,Math.abs(e)),this.options.edge===i&&(e=0);var n=e,s="translateX(-100%)";"right"===this.options.edge&&(s="translateX(100%)",n=-n),this.percentOpen=Math.min(1,e/this._width),this.el.style.transform=s+" translateX("+n+"px)",this._overlay.style.opacity=this.percentOpen}}},{key:"_handleDragTargetRelease",value:function(){this.isDragged&&(this.percentOpen>.2?this.open():this._animateOut(),this.isDragged=!1,this._verticallyScrolling=!1)}},{key:"_handleCloseDrag",value:function(t){if(this.isOpen){if(!this.options.draggable||this._isCurrentlyFixed()||this._verticallyScrolling)return;this.isDragged||this._startDrag(t),this._dragMoveUpdate(t);var e=this._xPos-this._startingXpos,i=e>0?"right":"left";e=Math.min(this._width,Math.abs(e)),this.options.edge!==i&&(e=0);var n=-e;"right"===this.options.edge&&(n=-n),this.percentOpen=Math.min(1,1-e/this._width),this.el.style.transform="translateX("+n+"px)",this._overlay.style.opacity=this.percentOpen}}},{key:"_handleCloseRelease",value:function(){this.isOpen&&this.isDragged&&(this.percentOpen>.8?this._animateIn():this.close(),this.isDragged=!1,this._verticallyScrolling=!1)}},{key:"_handleCloseTriggerClick",value:function(e){t(e.target).closest(".sidenav-close").length&&!this._isCurrentlyFixed()&&this.close()}},{key:"_handleWindowResize",value:function(){this.lastWindowWidth!==window.innerWidth&&(window.innerWidth>992?this.open():this.close()),this.lastWindowWidth=window.innerWidth,this.lastWindowHeight=window.innerHeight}},{key:"_setupClasses",value:function(){"right"===this.options.edge&&(this.el.classList.add("right-aligned"),this.dragTarget.classList.add("right-aligned"))}},{key:"_removeClasses",value:function(){this.el.classList.remove("right-aligned"),this.dragTarget.classList.remove("right-aligned")}},{key:"_setupFixed",value:function(){this._isCurrentlyFixed()&&this.open()}},{key:"_isCurrentlyFixed",value:function(){return this.isFixed&&window.innerWidth>992}},{key:"_createDragTarget",value:function(){var t=document.createElement("div");t.classList.add("drag-target"),document.body.appendChild(t),this.dragTarget=t}},{key:"_preventBodyScrolling",value:function(){document.body.style.overflow="hidden"}},{key:"_enableBodyScrolling",value:function(){document.body.style.overflow=""}},{key:"open",value:function(){!0!==this.isOpen&&(this.isOpen=!0,"function"==typeof this.options.onOpenStart&&this.options.onOpenStart.call(this,this.el),this._isCurrentlyFixed()?(e.remove(this.el),e({targets:this.el,translateX:0,duration:0,easing:"easeOutQuad"}),this._enableBodyScrolling(),this._overlay.style.display="none"):(this.options.preventScrolling&&this._preventBodyScrolling(),this.isDragged&&1==this.percentOpen||this._animateIn()))}},{key:"close",value:function(){if(!1!==this.isOpen)if(this.isOpen=!1,"function"==typeof this.options.onCloseStart&&this.options.onCloseStart.call(this,this.el),this._isCurrentlyFixed()){var t="left"===this.options.edge?"-105%":"105%";this.el.style.transform="translateX("+t+")"}else this._enableBodyScrolling(),this.isDragged&&0==this.percentOpen?this._overlay.style.display="none":this._animateOut()}},{key:"_animateIn",value:function(){this._animateSidenavIn(),this._animateOverlayIn()}},{key:"_animateSidenavIn",value:function(){var t=this,i="left"===this.options.edge?-1:1;this.isDragged&&(i="left"===this.options.edge?i+this.percentOpen:i-this.percentOpen),e.remove(this.el),e({targets:this.el,translateX:[100*i+"%",0],duration:this.options.inDuration,easing:"easeOutQuad",complete:function(){"function"==typeof t.options.onOpenEnd&&t.options.onOpenEnd.call(t,t.el)}})}},{key:"_animateOverlayIn",value:function(){var i=0;this.isDragged?i=this.percentOpen:t(this._overlay).css({display:"block"}),e.remove(this._overlay),e({targets:this._overlay,opacity:[i,1],duration:this.options.inDuration,easing:"easeOutQuad"})}},{key:"_animateOut",value:function(){this._animateSidenavOut(),this._animateOverlayOut()}},{key:"_animateSidenavOut",value:function(){var t=this,i="left"===this.options.edge?-1:1,n=0;this.isDragged&&(n="left"===this.options.edge?i+this.percentOpen:i-this.percentOpen),e.remove(this.el),e({targets:this.el,translateX:[100*n+"%",105*i+"%"],duration:this.options.outDuration,easing:"easeOutQuad",complete:function(){"function"==typeof t.options.onCloseEnd&&t.options.onCloseEnd.call(t,t.el)}})}},{key:"_animateOverlayOut",value:function(){var i=this;e.remove(this._overlay),e({targets:this._overlay,opacity:0,duration:this.options.outDuration,easing:"easeOutQuad",complete:function(){t(i._overlay).css("display","none")}})}}],[{key:"init",value:function(t,e){return _get(s.__proto__||Object.getPrototypeOf(s),"init",this).call(this,this,t,e)}},{key:"getInstance",value:function(t){return(t.jquery?t[0]:t).M_Sidenav}},{key:"defaults",get:function(){return i}}]),s}(Component);n._sidenavs=[],window.M.Sidenav=n,M.jQueryLoaded&&M.initializeJqueryWrapper(n,"sidenav","M_Sidenav")}(cash,M.anime),function(t,e){"use strict";var i={throttle:100,scrollOffset:200,activeClass:"active",getActiveElement:function(t){return'a[href="#'+t+'"]'}},n=function(n){function s(e,i){_classCallCheck(this,s);var n=_possibleConstructorReturn(this,(s.__proto__||Object.getPrototypeOf(s)).call(this,s,e,i));return n.el.M_ScrollSpy=n,n.options=t.extend({},s.defaults,i),s._elements.push(n),s._count++,s._increment++,n.tickId=-1,n.id=s._increment,n._setupEventHandlers(),n._handleWindowScroll(),n}return _inherits(s,n),_createClass(s,[{key:"destroy",value:function(){s._elements.splice(s._elements.indexOf(this),1),s._elementsInView.splice(s._elementsInView.indexOf(this),1),s._visibleElements.splice(s._visibleElements.indexOf(this.$el),1),s._count--,this._removeEventHandlers(),t(this.options.getActiveElement(this.$el.attr("id"))).removeClass(this.options.activeClass),this.el.M_ScrollSpy=void 0}},{key:"_setupEventHandlers",value:function(){var t=M.throttle(this._handleWindowScroll,200);this._handleThrottledResizeBound=t.bind(this),this._handleWindowScrollBound=this._handleWindowScroll.bind(this),1===s._count&&(window.addEventListener("scroll",this._handleWindowScrollBound),window.addEventListener("resize",this._handleThrottledResizeBound),document.body.addEventListener("click",this._handleTriggerClick))}},{key:"_removeEventHandlers",value:function(){0===s._count&&(window.removeEventListener("scroll",this._handleWindowScrollBound),window.removeEventListener("resize",this._handleThrottledResizeBound),document.body.removeEventListener("click",this._handleTriggerClick))}},{key:"_handleTriggerClick",value:function(i){for(var n=t(i.target),o=s._elements.length-1;o>=0;o--){var a=s._elements[o];if(n.is('a[href="#'+a.$el.attr("id")+'"]')){i.preventDefault();var r=a.$el.offset().top+1;e({targets:[document.documentElement,document.body],scrollTop:r-a.options.scrollOffset,duration:400,easing:"easeOutCubic"});break}}}},{key:"_handleWindowScroll",value:function(){s._ticks++;for(var t=M.getDocumentScrollTop(),e=M.getDocumentScrollLeft(),i=e+window.innerWidth,n=t+window.innerHeight,o=s._findElements(t,i,n,e),a=0;a<o.length;a++){var r=o[a];r.tickId<0&&r._enter(),r.tickId=s._ticks}for(var l=0;l<s._elementsInView.length;l++){var h=s._elementsInView[l],d=h.tickId;d>=0&&d!==s._ticks&&(h._exit(),h.tickId=-1)}s._elementsInView=o}},{key:"_enter",value:function(){s._visibleElements=s._visibleElements.filter(function(t){return 0!=t.height()}),s._visibleElements[0]?(t(this.options.getActiveElement(s._visibleElements[0].attr("id"))).removeClass(this.options.activeClass),s._visibleElements[0][0].M_ScrollSpy&&this.id<s._visibleElements[0][0].M_ScrollSpy.id?s._visibleElements.unshift(this.$el):s._visibleElements.push(this.$el)):s._visibleElements.push(this.$el),t(this.options.getActiveElement(s._visibleElements[0].attr("id"))).addClass(this.options.activeClass)}},{key:"_exit",value:function(){var e=this;s._visibleElements=s._visibleElements.filter(function(t){return 0!=t.height()}),s._visibleElements[0]&&(t(this.options.getActiveElement(s._visibleElements[0].attr("id"))).removeClass(this.options.activeClass),s._visibleElements=s._visibleElements.filter(function(t){return t.attr("id")!=e.$el.attr("id")}),s._visibleElements[0]&&t(this.options.getActiveElement(s._visibleElements[0].attr("id"))).addClass(this.options.activeClass))}}],[{key:"init",value:function(t,e){return _get(s.__proto__||Object.getPrototypeOf(s),"init",this).call(this,this,t,e)}},{key:"getInstance",value:function(t){return(t.jquery?t[0]:t).M_ScrollSpy}},{key:"_findElements",value:function(t,e,i,n){for(var o=[],a=0;a<s._elements.length;a++){var r=s._elements[a],l=t+r.options.scrollOffset||200;if(r.$el.height()>0){var h=r.$el.offset().top,d=r.$el.offset().left,u=d+r.$el.width(),c=h+r.$el.height();!(d>e||u<n||h>i||c<l)&&o.push(r)}}return o}},{key:"defaults",get:function(){return i}}]),s}(Component);n._elements=[],n._elementsInView=[],n._visibleElements=[],n._count=0,n._increment=0,n._ticks=0,M.ScrollSpy=n,M.jQueryLoaded&&M.initializeJqueryWrapper(n,"scrollSpy","M_ScrollSpy")}(cash,M.anime),function(t){"use strict";var e={data:{},limit:1/0,onAutocomplete:null,minLength:1,sortFunction:function(t,e,i){return t.indexOf(i)-e.indexOf(i)}},i=function(i){function n(e,i){_classCallCheck(this,n);var s=_possibleConstructorReturn(this,(n.__proto__||Object.getPrototypeOf(n)).call(this,n,e,i));return s.el.M_Autocomplete=s,s.options=t.extend({},n.defaults,i),s.isOpen=!1,s.count=0,s.activeIndex=-1,s.oldVal,s.$inputField=s.$el.closest(".input-field"),s.$active=t(),s._mousedown=!1,s._setupDropdown(),s._setupEventHandlers(),s}return _inherits(n,i),_createClass(n,[{key:"destroy",value:function(){this._removeEventHandlers(),this._removeDropdown(),this.el.M_Autocomplete=void 0}},{key:"_setupEventHandlers",value:function(){this._handleInputBlurBound=this._handleInputBlur.bind(this),this._handleInputKeyupAndFocusBound=this._handleInputKeyupAndFocus.bind(this),this._handleInputKeydownBound=this._handleInputKeydown.bind(this),this._handleInputClickBound=this._handleInputClick.bind(this),this._handleContainerMousedownAndTouchstartBound=this._handleContainerMousedownAndTouchstart.bind(this),this._handleContainerMouseupAndTouchendBound=this._handleContainerMouseupAndTouchend.bind(this),this.el.addEventListener("blur",this._handleInputBlurBound),this.el.addEventListener("keyup",this._handleInputKeyupAndFocusBound),this.el.addEventListener("focus",this._handleInputKeyupAndFocusBound),this.el.addEventListener("keydown",this._handleInputKeydownBound),this.el.addEventListener("click",this._handleInputClickBound),this.container.addEventListener("mousedown",this._handleContainerMousedownAndTouchstartBound),this.container.addEventListener("mouseup",this._handleContainerMouseupAndTouchendBound),void 0!==window.ontouchstart&&(this.container.addEventListener("touchstart",this._handleContainerMousedownAndTouchstartBound),this.container.addEventListener("touchend",this._handleContainerMouseupAndTouchendBound))}},{key:"_removeEventHandlers",value:function(){this.el.removeEventListener("blur",this._handleInputBlurBound),this.el.removeEventListener("keyup",this._handleInputKeyupAndFocusBound),this.el.removeEventListener("focus",this._handleInputKeyupAndFocusBound),this.el.removeEventListener("keydown",this._handleInputKeydownBound),this.el.removeEventListener("click",this._handleInputClickBound),this.container.removeEventListener("mousedown",this._handleContainerMousedownAndTouchstartBound),this.container.removeEventListener("mouseup",this._handleContainerMouseupAndTouchendBound),void 0!==window.ontouchstart&&(this.container.removeEventListener("touchstart",this._handleContainerMousedownAndTouchstartBound),this.container.removeEventListener("touchend",this._handleContainerMouseupAndTouchendBound))}},{key:"_setupDropdown",value:function(){var e=this;this.container=document.createElement("ul"),this.container.id="autocomplete-options-"+M.guid(),t(this.container).addClass("autocomplete-content dropdown-content"),this.$inputField.append(this.container),this.el.setAttribute("data-target",this.container.id),this.dropdown=M.Dropdown.init(this.el,{autoFocus:!1,closeOnClick:!1,coverTrigger:!1,onItemClick:function(i){e.selectOption(t(i))}}),this.el.removeEventListener("click",this.dropdown._handleClickBound)}},{key:"_removeDropdown",value:function(){this.container.parentNode.removeChild(this.container)}},{key:"_handleInputBlur",value:function(){this._mousedown||(this.close(),this._resetAutocomplete())}},{key:"_handleInputKeyupAndFocus",value:function(t){"keyup"===t.type&&(n._keydown=!1),this.count=0;var e=this.el.value.toLowerCase();13!==t.keyCode&&38!==t.keyCode&&40!==t.keyCode&&(this.oldVal!==e&&M.tabPressed&&this.open(),this.oldVal=e)}},{key:"_handleInputKeydown",value:function(e){n._keydown=!0;var i=e.keyCode,s=void 0,o=t(this.container).children("li").length;i===M.keys.ENTER&&this.activeIndex>=0?(s=t(this.container).children("li").eq(this.activeIndex)).length&&(this.selectOption(s),e.preventDefault()):i!==M.keys.ARROW_UP&&i!==M.keys.ARROW_DOWN||(e.preventDefault(),i===M.keys.ARROW_UP&&this.activeIndex>0&&this.activeIndex--,i===M.keys.ARROW_DOWN&&this.activeIndex<o-1&&this.activeIndex++,this.$active.removeClass("active"),this.activeIndex>=0&&(this.$active=t(this.container).children("li").eq(this.activeIndex),this.$active.addClass("active")))}},{key:"_handleInputClick",value:function(t){this.open()}},{key:"_handleContainerMousedownAndTouchstart",value:function(t){this._mousedown=!0}},{key:"_handleContainerMouseupAndTouchend",value:function(t){this._mousedown=!1}},{key:"_highlight",value:function(t,e){var i=e.find("img"),n=e.text().toLowerCase().indexOf(""+t.toLowerCase()),s=n+t.length-1,o=e.text().slice(0,n),a=e.text().slice(n,s+1),r=e.text().slice(s+1);e.html("<span>"+o+"<span class='highlight'>"+a+"</span>"+r+"</span>"),i.length&&e.prepend(i)}},{key:"_resetCurrentElement",value:function(){this.activeIndex=-1,this.$active.removeClass("active")}},{key:"_resetAutocomplete",value:function(){t(this.container).empty(),this._resetCurrentElement(),this.oldVal=null,this.isOpen=!1,this._mousedown=!1}},{key:"selectOption",value:function(t){var e=t.text().trim();this.el.value=e,this.$el.trigger("change"),this._resetAutocomplete(),this.close(),"function"==typeof this.options.onAutocomplete&&this.options.onAutocomplete.call(this,e)}},{key:"_renderDropdown",value:function(e,i){var n=this;this._resetAutocomplete();var s=[];for(var o in e)if(e.hasOwnProperty(o)&&-1!==o.toLowerCase().indexOf(i)){if(this.count>=this.options.limit)break;var a={data:e[o],key:o};s.push(a),this.count++}if(this.options.sortFunction){s.sort(function(t,e){return n.options.sortFunction(t.key.toLowerCase(),e.key.toLowerCase(),i.toLowerCase())})}for(var r=0;r<s.length;r++){var l=s[r],h=t("<li></li>");l.data?h.append('<img src="'+l.data+'" class="right circle"><span>'+l.key+"</span>"):h.append("<span>"+l.key+"</span>"),t(this.container).append(h),this._highlight(i,h)}}},{key:"open",value:function(){var t=this.el.value.toLowerCase();this._resetAutocomplete(),t.length>=this.options.minLength&&(this.isOpen=!0,this._renderDropdown(this.options.data,t)),this.dropdown.isOpen?this.dropdown.recalculateDimensions():this.dropdown.open()}},{key:"close",value:function(){this.dropdown.close()}},{key:"updateData",value:function(t){var e=this.el.value.toLowerCase();this.options.data=t,this.isOpen&&this._renderDropdown(t,e)}}],[{key:"init",value:function(t,e){return _get(n.__proto__||Object.getPrototypeOf(n),"init",this).call(this,this,t,e)}},{key:"getInstance",value:function(t){return(t.jquery?t[0]:t).M_Autocomplete}},{key:"defaults",get:function(){return e}}]),n}(Component);i._keydown=!1,M.Autocomplete=i,M.jQueryLoaded&&M.initializeJqueryWrapper(i,"autocomplete","M_Autocomplete")}(cash),function(t){M.updateTextFields=function(){t("input[type=text], input[type=password], input[type=email], input[type=url], input[type=tel], input[type=number], input[type=search], input[type=date], input[type=time], textarea").each(function(e,i){var n=t(this);e.value.length>0||t(e).is(":focus")||e.autofocus||null!==n.attr("placeholder")?n.siblings("label").addClass("active"):e.validity?n.siblings("label").toggleClass("active",!0===e.validity.badInput):n.siblings("label").removeClass("active")})},M.validate_field=function(t){var e=null!==t.attr("data-length"),i=parseInt(t.attr("data-length")),n=t[0].value.length;0!==n||!1!==t[0].validity.badInput||t.is(":required")?t.hasClass("validate")&&(t.is(":valid")&&e&&n<=i||t.is(":valid")&&!e?(t.removeClass("invalid"),t.addClass("valid")):(t.removeClass("valid"),t.addClass("invalid"))):t.hasClass("validate")&&(t.removeClass("valid"),t.removeClass("invalid"))},M.textareaAutoResize=function(e){if(e instanceof Element&&(e=t(e)),e.length){var i=t(".hiddendiv").first();i.length||(i=t('<div class="hiddendiv common"></div>'),t("body").append(i));var n=e.css("font-family"),s=e.css("font-size"),o=e.css("line-height"),a=e.css("padding-top"),r=e.css("padding-right"),l=e.css("padding-bottom"),h=e.css("padding-left");s&&i.css("font-size",s),n&&i.css("font-family",n),o&&i.css("line-height",o),a&&i.css("padding-top",a),r&&i.css("padding-right",r),l&&i.css("padding-bottom",l),h&&i.css("padding-left",h),e.data("original-height")||e.data("original-height",e.height()),"off"===e.attr("wrap")&&i.css("overflow-wrap","normal").css("white-space","pre"),i.text(e[0].value+"\n");var d=i.html().replace(/\n/g,"<br>");i.html(d),e[0].offsetWidth>0&&e[0].offsetHeight>0?i.css("width",e.width()+"px"):i.css("width",window.innerWidth/2+"px"),e.data("original-height")<=i.innerHeight()?e.css("height",i.innerHeight()+"px"):e[0].value.length<e.data("previous-length")&&e.css("height",e.data("original-height")+"px"),e.data("previous-length",e[0].value.length)}else console.error("No textarea element found")},t(document).ready(function(){var e="input[type=text], input[type=password], input[type=email], input[type=url], input[type=tel], input[type=number], input[type=search], input[type=date], input[type=time], textarea";t(document).on("change",e,function(){0===this.value.length&&null===t(this).attr("placeholder")||t(this).siblings("label").addClass("active"),M.validate_field(t(this))}),t(document).ready(function(){M.updateTextFields()}),t(document).on("reset",function(i){var n=t(i.target);n.is("form")&&(n.find(e).removeClass("valid").removeClass("invalid"),n.find(e).each(function(e){this.value.length&&t(this).siblings("label").removeClass("active")}),setTimeout(function(){n.find("select").each(function(){if(this.M_FormSelect){var e=t(this).find("option[selected]").text();t(this).siblings("input.select-dropdown")[0].value=e}})},0))}),document.addEventListener("focus",function(i){t(i.target).is(e)&&t(i.target).siblings("label, .prefix").addClass("active")},!0),document.addEventListener("blur",function(i){var n=t(i.target);if(n.is(e)){var s=".prefix";0===n[0].value.length&&!0!==n[0].validity.badInput&&null===n.attr("placeholder")&&(s+=", label"),n.siblings(s).removeClass("active"),M.validate_field(n)}},!0);t(document).on("keyup","input[type=radio], input[type=checkbox]",function(e){if(e.which===M.keys.TAB)return t(this).addClass("tabbed"),void t(this).one("blur",function(e){t(this).removeClass("tabbed")})});t(".materialize-textarea").each(function(){var e=t(this);e.data("original-height",e.height()),e.data("previous-length",this.value.length),M.textareaAutoResize(e)}),t(document).on("keyup",".materialize-textarea",function(){M.textareaAutoResize(t(this))}),t(document).on("keydown",".materialize-textarea",function(){M.textareaAutoResize(t(this))}),t(document).on("change",'.file-field input[type="file"]',function(){for(var e=t(this).closest(".file-field").find("input.file-path"),i=t(this)[0].files,n=[],s=0;s<i.length;s++)n.push(i[s].name);e[0].value=n.join(", "),e.trigger("change")})})}(cash),function(t,e){"use strict";var i={indicators:!0,height:400,duration:500,interval:6e3},n=function(n){function s(i,n){_classCallCheck(this,s);var o=_possibleConstructorReturn(this,(s.__proto__||Object.getPrototypeOf(s)).call(this,s,i,n));return o.el.M_Slider=o,o.options=t.extend({},s.defaults,n),o.$slider=o.$el.find(".slides"),o.$slides=o.$slider.children("li"),o.activeIndex=o.$slides.filter(function(e){return t(e).hasClass("active")}).first().index(),-1!=o.activeIndex&&(o.$active=o.$slides.eq(o.activeIndex)),o._setSliderHeight(),o.$slides.find(".caption").each(function(t){o._animateCaptionIn(t,0)}),o.$slides.find("img").each(function(e){var i="data:image/gif;base64,R0lGODlhAQABAIABAP///wAAACH5BAEKAAEALAAAAAABAAEAAAICTAEAOw==";t(e).attr("src")!==i&&(t(e).css("background-image",'url("'+t(e).attr("src")+'")'),t(e).attr("src",i))}),o._setupIndicators(),o.$active?o.$active.css("display","block"):(o.$slides.first().addClass("active"),e({targets:o.$slides.first()[0],opacity:1,duration:o.options.duration,easing:"easeOutQuad"}),o.activeIndex=0,o.$active=o.$slides.eq(o.activeIndex),o.options.indicators&&o.$indicators.eq(o.activeIndex).addClass("active")),o.$active.find("img").each(function(t){e({targets:o.$active.find(".caption")[0],opacity:1,translateX:0,translateY:0,duration:o.options.duration,easing:"easeOutQuad"})}),o._setupEventHandlers(),o.start(),o}return _inherits(s,n),_createClass(s,[{key:"destroy",value:function(){this.pause(),this._removeIndicators(),this._removeEventHandlers(),this.el.M_Slider=void 0}},{key:"_setupEventHandlers",value:function(){var t=this;this._handleIntervalBound=this._handleInterval.bind(this),this._handleIndicatorClickBound=this._handleIndicatorClick.bind(this),this.options.indicators&&this.$indicators.each(function(e){e.addEventListener("click",t._handleIndicatorClickBound)})}},{key:"_removeEventHandlers",value:function(){var t=this;this.options.indicators&&this.$indicators.each(function(e){e.removeEventListener("click",t._handleIndicatorClickBound)})}},{key:"_handleIndicatorClick",value:function(e){var i=t(e.target).index();this.set(i)}},{key:"_handleInterval",value:function(){var t=this.$slider.find(".active").index();this.$slides.length===t+1?t=0:t+=1,this.set(t)}},{key:"_animateCaptionIn",value:function(i,n){var s={targets:i,opacity:0,duration:n,easing:"easeOutQuad"};t(i).hasClass("center-align")?s.translateY=-100:t(i).hasClass("right-align")?s.translateX=100:t(i).hasClass("left-align")&&(s.translateX=-100),e(s)}},{key:"_setSliderHeight",value:function(){this.$el.hasClass("fullscreen")||(this.options.indicators?this.$el.css("height",this.options.height+40+"px"):this.$el.css("height",this.options.height+"px"),this.$slider.css("height",this.options.height+"px"))}},{key:"_setupIndicators",value:function(){var e=this;this.options.indicators&&(this.$indicators=t('<ul class="indicators"></ul>'),this.$slides.each(function(i,n){var s=t('<li class="indicator-item"></li>');e.$indicators.append(s[0])}),this.$el.append(this.$indicators[0]),this.$indicators=this.$indicators.children("li.indicator-item"))}},{key:"_removeIndicators",value:function(){this.$el.find("ul.indicators").remove()}},{key:"set",value:function(t){var i=this;if(t>=this.$slides.length?t=0:t<0&&(t=this.$slides.length-1),this.activeIndex!=t){this.$active=this.$slides.eq(this.activeIndex);var n=this.$active.find(".caption");this.$active.removeClass("active"),e({targets:this.$active[0],opacity:0,duration:this.options.duration,easing:"easeOutQuad",complete:function(){i.$slides.not(".active").each(function(t){e({targets:t,opacity:0,translateX:0,translateY:0,duration:0,easing:"easeOutQuad"})})}}),this._animateCaptionIn(n[0],this.options.duration),this.options.indicators&&(this.$indicators.eq(this.activeIndex).removeClass("active"),this.$indicators.eq(t).addClass("active")),e({targets:this.$slides.eq(t)[0],opacity:1,duration:this.options.duration,easing:"easeOutQuad"}),e({targets:this.$slides.eq(t).find(".caption")[0],opacity:1,translateX:0,translateY:0,duration:this.options.duration,delay:this.options.duration,easing:"easeOutQuad"}),this.$slides.eq(t).addClass("active"),this.activeIndex=t,this.start()}}},{key:"pause",value:function(){clearInterval(this.interval)}},{key:"start",value:function(){clearInterval(this.interval),this.interval=setInterval(this._handleIntervalBound,this.options.duration+this.options.interval)}},{key:"next",value:function(){var t=this.activeIndex+1;t>=this.$slides.length?t=0:t<0&&(t=this.$slides.length-1),this.set(t)}},{key:"prev",value:function(){var t=this.activeIndex-1;t>=this.$slides.length?t=0:t<0&&(t=this.$slides.length-1),this.set(t)}}],[{key:"init",value:function(t,e){return _get(s.__proto__||Object.getPrototypeOf(s),"init",this).call(this,this,t,e)}},{key:"getInstance",value:function(t){return(t.jquery?t[0]:t).M_Slider}},{key:"defaults",get:function(){return i}}]),s}(Component);M.Slider=n,M.jQueryLoaded&&M.initializeJqueryWrapper(n,"slider","M_Slider")}(cash,M.anime),function(t,e){t(document).on("click",".card",function(i){if(t(this).children(".card-reveal").length){var n=t(i.target).closest(".card");void 0===n.data("initialOverflow")&&n.data("initialOverflow",void 0===n.css("overflow")?"":n.css("overflow"));var s=t(this).find(".card-reveal");t(i.target).is(t(".card-reveal .card-title"))||t(i.target).is(t(".card-reveal .card-title i"))?e({targets:s[0],translateY:0,duration:225,easing:"easeInOutQuad",complete:function(e){var i=e.animatables[0].target;t(i).css({display:"none"}),n.css("overflow",n.data("initialOverflow"))}}):(t(i.target).is(t(".card .activator"))||t(i.target).is(t(".card .activator i")))&&(n.css("overflow","hidden"),s.css({display:"block"}),e({targets:s[0],translateY:"-100%",duration:300,easing:"easeInOutQuad"}))}})}(cash,M.anime),function(t){"use strict";var e={data:[],placeholder:"",secondaryPlaceholder:"",autocompleteOptions:{},limit:1/0,onChipAdd:null,onChipSelect:null,onChipDelete:null},i=function(i){function n(e,i){_classCallCheck(this,n);var s=_possibleConstructorReturn(this,(n.__proto__||Object.getPrototypeOf(n)).call(this,n,e,i));return s.el.M_Chips=s,s.options=t.extend({},n.defaults,i),s.$el.addClass("chips input-field"),s.chipsData=[],s.$chips=t(),s._setupInput(),s.hasAutocomplete=Object.keys(s.options.autocompleteOptions).length>0,s.$input.attr("id")||s.$input.attr("id",M.guid()),s.options.data.length&&(s.chipsData=s.options.data,s._renderChips(s.chipsData)),s.hasAutocomplete&&s._setupAutocomplete(),s._setPlaceholder(),s._setupLabel(),s._setupEventHandlers(),s}return _inherits(n,i),_createClass(n,[{key:"getData",value:function(){return this.chipsData}},{key:"destroy",value:function(){this._removeEventHandlers(),this.$chips.remove(),this.el.M_Chips=void 0}},{key:"_setupEventHandlers",value:function(){this._handleChipClickBound=this._handleChipClick.bind(this),this._handleInputKeydownBound=this._handleInputKeydown.bind(this),this._handleInputFocusBound=this._handleInputFocus.bind(this),this._handleInputBlurBound=this._handleInputBlur.bind(this),this.el.addEventListener("click",this._handleChipClickBound),document.addEventListener("keydown",n._handleChipsKeydown),document.addEventListener("keyup",n._handleChipsKeyup),this.el.addEventListener("blur",n._handleChipsBlur,!0),this.$input[0].addEventListener("focus",this._handleInputFocusBound),this.$input[0].addEventListener("blur",this._handleInputBlurBound),this.$input[0].addEventListener("keydown",this._handleInputKeydownBound)}},{key:"_removeEventHandlers",value:function(){this.el.removeEventListener("click",this._handleChipClickBound),document.removeEventListener("keydown",n._handleChipsKeydown),document.removeEventListener("keyup",n._handleChipsKeyup),this.el.removeEventListener("blur",n._handleChipsBlur,!0),this.$input[0].removeEventListener("focus",this._handleInputFocusBound),this.$input[0].removeEventListener("blur",this._handleInputBlurBound),this.$input[0].removeEventListener("keydown",this._handleInputKeydownBound)}},{key:"_handleChipClick",value:function(e){var i=t(e.target).closest(".chip"),n=t(e.target).is(".close");if(i.length){var s=i.index();n?(this.deleteChip(s),this.$input[0].focus()):this.selectChip(s)}else this.$input[0].focus()}},{key:"_handleInputFocus",value:function(){this.$el.addClass("focus")}},{key:"_handleInputBlur",value:function(){this.$el.removeClass("focus")}},{key:"_handleInputKeydown",value:function(t){if(n._keydown=!0,13===t.keyCode){if(this.hasAutocomplete&&this.autocomplete&&this.autocomplete.isOpen)return;t.preventDefault(),this.addChip({tag:this.$input[0].value}),this.$input[0].value=""}else 8!==t.keyCode&&37!==t.keyCode||""!==this.$input[0].value||!this.chipsData.length||(t.preventDefault(),this.selectChip(this.chipsData.length-1))}},{key:"_renderChip",value:function(e){if(e.tag){var i=document.createElement("div"),n=document.createElement("i");if(i.classList.add("chip"),i.textContent=e.tag,i.setAttribute("tabindex",0),t(n).addClass("material-icons close"),n.textContent="close",e.image){var s=document.createElement("img");s.setAttribute("src",e.image),i.insertBefore(s,i.firstChild)}return i.appendChild(n),i}}},{key:"_renderChips",value:function(){this.$chips.remove();for(var t=0;t<this.chipsData.length;t++){var e=this._renderChip(this.chipsData[t]);this.$el.append(e),this.$chips.add(e)}this.$el.append(this.$input[0])}},{key:"_setupAutocomplete",value:function(){var t=this;this.options.autocompleteOptions.onAutocomplete=function(e){t.addChip({tag:e}),t.$input[0].value="",t.$input[0].focus()},this.autocomplete=M.Autocomplete.init(this.$input[0],this.options.autocompleteOptions)}},{key:"_setupInput",value:function(){this.$input=this.$el.find("input"),this.$input.length||(this.$input=t("<input></input>"),this.$el.append(this.$input)),this.$input.addClass("input")}},{key:"_setupLabel",value:function(){this.$label=this.$el.find("label"),this.$label.length&&this.$label.setAttribute("for",this.$input.attr("id"))}},{key:"_setPlaceholder",value:function(){void 0!==this.chipsData&&!this.chipsData.length&&this.options.placeholder?t(this.$input).prop("placeholder",this.options.placeholder):(void 0===this.chipsData||this.chipsData.length)&&this.options.secondaryPlaceholder&&t(this.$input).prop("placeholder",this.options.secondaryPlaceholder)}},{key:"_isValid",value:function(t){if(t.hasOwnProperty("tag")&&""!==t.tag){for(var e=!1,i=0;i<this.chipsData.length;i++)if(this.chipsData[i].tag===t.tag){e=!0;break}return!e}return!1}},{key:"addChip",value:function(e){if(this._isValid(e)&&!(this.chipsData.length>=this.options.limit)){var i=this._renderChip(e);this.$chips.add(i),this.chipsData.push(e),t(this.$input).before(i),this._setPlaceholder(),"function"==typeof this.options.onChipAdd&&this.options.onChipAdd.call(this,this.$el,i)}}},{key:"deleteChip",value:function(e){var i=this.$chips.eq(e);this.$chips.eq(e).remove(),this.$chips=this.$chips.filter(function(e){return t(e).index()>=0}),this.chipsData.splice(e,1),this._setPlaceholder(),"function"==typeof this.options.onChipDelete&&this.options.onChipDelete.call(this,this.$el,i[0])}},{key:"selectChip",value:function(t){var e=this.$chips.eq(t);this._selectedChip=e,e[0].focus(),"function"==typeof this.options.onChipSelect&&this.options.onChipSelect.call(this,this.$el,e[0])}}],[{key:"init",value:function(t,e){return _get(n.__proto__||Object.getPrototypeOf(n),"init",this).call(this,this,t,e)}},{key:"getInstance",value:function(t){return(t.jquery?t[0]:t).M_Chips}},{key:"_handleChipsKeydown",value:function(e){n._keydown=!0;var i=t(e.target).closest(".chips"),s=e.target&&i.length;if(!t(e.target).is("input, textarea")&&s){var o=i[0].M_Chips;if(8===e.keyCode||46===e.keyCode){e.preventDefault();var a=o.chipsData.length;if(o._selectedChip){var r=o._selectedChip.index();o.deleteChip(r),o._selectedChip=null,a=Math.max(r-1,0)}o.chipsData.length&&o.selectChip(a)}else if(37===e.keyCode){if(o._selectedChip){var l=o._selectedChip.index()-1;if(l<0)return;o.selectChip(l)}}else if(39===e.keyCode&&o._selectedChip){var h=o._selectedChip.index()+1;h>=o.chipsData.length?o.$input[0].focus():o.selectChip(h)}}}},{key:"_handleChipsKeyup",value:function(t){n._keydown=!1}},{key:"_handleChipsBlur",value:function(e){n._keydown||(t(e.target).closest(".chips")[0].M_Chips._selectedChip=null)}},{key:"defaults",get:function(){return e}}]),n}(Component);i._keydown=!1,M.Chips=i,M.jQueryLoaded&&M.initializeJqueryWrapper(i,"chips","M_Chips"),t(document).ready(function(){t(document.body).on("click",".chip .close",function(){var e=t(this).closest(".chips");e.length&&e[0].M_Chips||t(this).closest(".chip").remove()})})}(cash),function(t){"use strict";var e={top:0,bottom:1/0,offset:0,onPositionChange:null},i=function(i){function n(e,i){_classCallCheck(this,n);var s=_possibleConstructorReturn(this,(n.__proto__||Object.getPrototypeOf(n)).call(this,n,e,i));return s.el.M_Pushpin=s,s.options=t.extend({},n.defaults,i),s.originalOffset=s.el.offsetTop,n._pushpins.push(s),s._setupEventHandlers(),s._updatePosition(),s}return _inherits(n,i),_createClass(n,[{key:"destroy",value:function(){this.el.style.top=null,this._removePinClasses(),this._removeEventHandlers();var t=n._pushpins.indexOf(this);n._pushpins.splice(t,1)}},{key:"_setupEventHandlers",value:function(){document.addEventListener("scroll",n._updateElements)}},{key:"_removeEventHandlers",value:function(){document.removeEventListener("scroll",n._updateElements)}},{key:"_updatePosition",value:function(){var t=M.getDocumentScrollTop()+this.options.offset;this.options.top<=t&&this.options.bottom>=t&&!this.el.classList.contains("pinned")&&(this._removePinClasses(),this.el.style.top=this.options.offset+"px",this.el.classList.add("pinned"),"function"==typeof this.options.onPositionChange&&this.options.onPositionChange.call(this,"pinned")),t<this.options.top&&!this.el.classList.contains("pin-top")&&(this._removePinClasses(),this.el.style.top=0,this.el.classList.add("pin-top"),"function"==typeof this.options.onPositionChange&&this.options.onPositionChange.call(this,"pin-top")),t>this.options.bottom&&!this.el.classList.contains("pin-bottom")&&(this._removePinClasses(),this.el.classList.add("pin-bottom"),this.el.style.top=this.options.bottom-this.originalOffset+"px","function"==typeof this.options.onPositionChange&&this.options.onPositionChange.call(this,"pin-bottom"))}},{key:"_removePinClasses",value:function(){this.el.classList.remove("pin-top","pinned","pin-bottom")}}],[{key:"init",value:function(t,e){return _get(n.__proto__||Object.getPrototypeOf(n),"init",this).call(this,this,t,e)}},{key:"getInstance",value:function(t){return(t.jquery?t[0]:t).M_Pushpin}},{key:"_updateElements",value:function(){for(var t in n._pushpins)n._pushpins[t]._updatePosition()}},{key:"defaults",get:function(){return e}}]),n}(Component);i._pushpins=[],M.Pushpin=i,M.jQueryLoaded&&M.initializeJqueryWrapper(i,"pushpin","M_Pushpin")}(cash),function(t,e){"use strict";var i={direction:"top",hoverEnabled:!0,toolbarEnabled:!1};t.fn.reverse=[].reverse;var n=function(n){function s(e,i){_classCallCheck(this,s);var n=_possibleConstructorReturn(this,(s.__proto__||Object.getPrototypeOf(s)).call(this,s,e,i));return n.el.M_FloatingActionButton=n,n.options=t.extend({},s.defaults,i),n.isOpen=!1,n.$anchor=n.$el.children("a").first(),n.$menu=n.$el.children("ul").first(),n.$floatingBtns=n.$el.find("ul .btn-floating"),n.$floatingBtnsReverse=n.$el.find("ul .btn-floating").reverse(),n.offsetY=0,n.offsetX=0,n.$el.addClass("direction-"+n.options.direction),"top"===n.options.direction?n.offsetY=40:"right"===n.options.direction?n.offsetX=-40:"bottom"===n.options.direction?n.offsetY=-40:n.offsetX=40,n._setupEventHandlers(),n}return _inherits(s,n),_createClass(s,[{key:"destroy",value:function(){this._removeEventHandlers(),this.el.M_FloatingActionButton=void 0}},{key:"_setupEventHandlers",value:function(){this._handleFABClickBound=this._handleFABClick.bind(this),this._handleOpenBound=this.open.bind(this),this._handleCloseBound=this.close.bind(this),this.options.hoverEnabled&&!this.options.toolbarEnabled?(this.el.addEventListener("mouseenter",this._handleOpenBound),this.el.addEventListener("mouseleave",this._handleCloseBound)):this.el.addEventListener("click",this._handleFABClickBound)}},{key:"_removeEventHandlers",value:function(){this.options.hoverEnabled&&!this.options.toolbarEnabled?(this.el.removeEventListener("mouseenter",this._handleOpenBound),this.el.removeEventListener("mouseleave",this._handleCloseBound)):this.el.removeEventListener("click",this._handleFABClickBound)}},{key:"_handleFABClick",value:function(){this.isOpen?this.close():this.open()}},{key:"_handleDocumentClick",value:function(e){t(e.target).closest(this.$menu).length||this.close()}},{key:"open",value:function(){this.isOpen||(this.options.toolbarEnabled?this._animateInToolbar():this._animateInFAB(),this.isOpen=!0)}},{key:"close",value:function(){this.isOpen&&(this.options.toolbarEnabled?(window.removeEventListener("scroll",this._handleCloseBound,!0),document.body.removeEventListener("click",this._handleDocumentClickBound,!0),this._animateOutToolbar()):this._animateOutFAB(),this.isOpen=!1)}},{key:"_animateInFAB",value:function(){var t=this;this.$el.addClass("active");var i=0;this.$floatingBtnsReverse.each(function(n){e({targets:n,opacity:1,scale:[.4,1],translateY:[t.offsetY,0],translateX:[t.offsetX,0],duration:275,delay:i,easing:"easeInOutQuad"}),i+=40})}},{key:"_animateOutFAB",value:function(){var t=this;this.$floatingBtnsReverse.each(function(i){e.remove(i),e({targets:i,opacity:0,scale:.4,translateY:t.offsetY,translateX:t.offsetX,duration:175,easing:"easeOutQuad",complete:function(){t.$el.removeClass("active")}})})}},{key:"_animateInToolbar",value:function(){var e=this,i=void 0,n=window.innerWidth,s=window.innerHeight,o=this.el.getBoundingClientRect(),a=t('<div class="fab-backdrop"></div>'),r=this.$anchor.css("background-color");this.$anchor.append(a),this.offsetX=o.left-n/2+o.width/2,this.offsetY=s-o.bottom,i=n/a[0].clientWidth,this.btnBottom=o.bottom,this.btnLeft=o.left,this.btnWidth=o.width,this.$el.addClass("active"),this.$el.css({"text-align":"center",width:"100%",bottom:0,left:0,transform:"translateX("+this.offsetX+"px)",transition:"none"}),this.$anchor.css({transform:"translateY("+-this.offsetY+"px)",transition:"none"}),a.css({"background-color":r}),setTimeout(function(){e.$el.css({transform:"",transition:"transform .2s cubic-bezier(0.550, 0.085, 0.680, 0.530), background-color 0s linear .2s"}),e.$anchor.css({overflow:"visible",transform:"",transition:"transform .2s"}),setTimeout(function(){e.$el.css({overflow:"hidden","background-color":r}),a.css({transform:"scale("+i+")",transition:"transform .2s cubic-bezier(0.550, 0.055, 0.675, 0.190)"}),e.$menu.children("li").children("a").css({opacity:1}),e._handleDocumentClickBound=e._handleDocumentClick.bind(e),window.addEventListener("scroll",e._handleCloseBound,!0),document.body.addEventListener("click",e._handleDocumentClickBound,!0)},100)},0)}},{key:"_animateOutToolbar",value:function(){var t=this,e=window.innerWidth,i=window.innerHeight,n=this.$el.find(".fab-backdrop"),s=this.$anchor.css("background-color");this.offsetX=this.btnLeft-e/2+this.btnWidth/2,this.offsetY=i-this.btnBottom,this.$el.removeClass("active"),this.$el.css({"background-color":"transparent",transition:"none"}),this.$anchor.css({transition:"none"}),n.css({transform:"scale(0)","background-color":s}),this.$menu.children("li").children("a").css({opacity:""}),setTimeout(function(){n.remove(),t.$el.css({"text-align":"",width:"",bottom:"",left:"",overflow:"","background-color":"",transform:"translate3d("+-t.offsetX+"px,0,0)"}),t.$anchor.css({overflow:"",transform:"translate3d(0,"+t.offsetY+"px,0)"}),setTimeout(function(){t.$el.css({transform:"translate3d(0,0,0)",transition:"transform .2s"}),t.$anchor.css({transform:"translate3d(0,0,0)",transition:"transform .2s cubic-bezier(0.550, 0.055, 0.675, 0.190)"})},20)},200)}}],[{key:"init",value:function(t,e){return _get(s.__proto__||Object.getPrototypeOf(s),"init",this).call(this,this,t,e)}},{key:"getInstance",value:function(t){return(t.jquery?t[0]:t).M_FloatingActionButton}},{key:"defaults",get:function(){return i}}]),s}(Component);M.FloatingActionButton=n,M.jQueryLoaded&&M.initializeJqueryWrapper(n,"floatingActionButton","M_FloatingActionButton")}(cash,M.anime),function(t){"use strict";var e={autoClose:!1,format:"mmm dd, yyyy",parse:null,defaultDate:null,setDefaultDate:!1,disableWeekends:!1,disableDayFn:null,firstDay:0,minDate:null,maxDate:null,yearRange:10,minYear:0,maxYear:9999,minMonth:void 0,maxMonth:void 0,startRange:null,endRange:null,isRTL:!1,showMonthAfterYear:!1,showDaysInNextAndPreviousMonths:!1,container:null,showClearBtn:!1,i18n:{cancel:"Cancel",clear:"Clear",done:"Ok",previousMonth:"‹",nextMonth:"›",months:["January","February","March","April","May","June","July","August","September","October","November","December"],monthsShort:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"],weekdays:["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"],weekdaysShort:["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],weekdaysAbbrev:["S","M","T","W","T","F","S"]},events:[],onSelect:null,onOpen:null,onClose:null,onDraw:null},i=function(i){function n(e,i){_classCallCheck(this,n);var s=_possibleConstructorReturn(this,(n.__proto__||Object.getPrototypeOf(n)).call(this,n,e,i));s.el.M_Datepicker=s,s.options=t.extend({},n.defaults,i),i&&i.hasOwnProperty("i18n")&&"object"==typeof i.i18n&&(s.options.i18n=t.extend({},n.defaults.i18n,i.i18n)),s.options.minDate&&s.options.minDate.setHours(0,0,0,0),s.options.maxDate&&s.options.maxDate.setHours(0,0,0,0),s.id=M.guid(),s._setupVariables(),s._insertHTMLIntoDOM(),s._setupModal(),s._setupEventHandlers(),s.options.defaultDate||(s.options.defaultDate=new Date(Date.parse(s.el.value)));var o=s.options.defaultDate;return n._isDate(o)?s.options.setDefaultDate?(s.setDate(o,!0),s.setInputValue()):s.gotoDate(o):s.gotoDate(new Date),s.isOpen=!1,s}return _inherits(n,i),_createClass(n,[{key:"destroy",value:function(){this._removeEventHandlers(),this.modal.destroy(),t(this.modalEl).remove(),this.destroySelects(),this.el.M_Datepicker=void 0}},{key:"destroySelects",value:function(){var t=this.calendarEl.querySelector(".orig-select-year");t&&M.FormSelect.getInstance(t).destroy();var e=this.calendarEl.querySelector(".orig-select-month");e&&M.FormSelect.getInstance(e).destroy()}},{key:"_insertHTMLIntoDOM",value:function(){this.options.showClearBtn&&(t(this.clearBtn).css({visibility:""}),this.clearBtn.innerHTML=this.options.i18n.clear),this.doneBtn.innerHTML=this.options.i18n.done,this.cancelBtn.innerHTML=this.options.i18n.cancel,this.options.container?this.$modalEl.appendTo(this.options.container):this.$modalEl.insertBefore(this.el)}},{key:"_setupModal",value:function(){var t=this;this.modalEl.id="modal-"+this.id,this.modal=M.Modal.init(this.modalEl,{onCloseEnd:function(){t.isOpen=!1}})}},{key:"toString",value:function(t){var e=this;return t=t||this.options.format,n._isDate(this.date)?t.split(/(d{1,4}|m{1,4}|y{4}|yy|!.)/g).map(function(t){return e.formats[t]?e.formats[t]():t}).join(""):""}},{key:"setDate",value:function(t,e){if(!t)return this.date=null,this._renderDateDisplay(),this.draw();if("string"==typeof t&&(t=new Date(Date.parse(t))),n._isDate(t)){var i=this.options.minDate,s=this.options.maxDate;n._isDate(i)&&t<i?t=i:n._isDate(s)&&t>s&&(t=s),this.date=new Date(t.getTime()),this._renderDateDisplay(),n._setToStartOfDay(this.date),this.gotoDate(this.date),e||"function"!=typeof this.options.onSelect||this.options.onSelect.call(this,this.date)}}},{key:"setInputValue",value:function(){this.el.value=this.toString(),this.$el.trigger("change",{firedBy:this})}},{key:"_renderDateDisplay",value:function(){var t=n._isDate(this.date)?this.date:new Date,e=this.options.i18n,i=e.weekdaysShort[t.getDay()],s=e.monthsShort[t.getMonth()],o=t.getDate();this.yearTextEl.innerHTML=t.getFullYear(),this.dateTextEl.innerHTML=i+", "+s+" "+o}},{key:"gotoDate",value:function(t){var e=!0;if(n._isDate(t)){if(this.calendars){var i=new Date(this.calendars[0].year,this.calendars[0].month,1),s=new Date(this.calendars[this.calendars.length-1].year,this.calendars[this.calendars.length-1].month,1),o=t.getTime();s.setMonth(s.getMonth()+1),s.setDate(s.getDate()-1),e=o<i.getTime()||s.getTime()<o}e&&(this.calendars=[{month:t.getMonth(),year:t.getFullYear()}]),this.adjustCalendars()}}},{key:"adjustCalendars",value:function(){this.calendars[0]=this.adjustCalendar(this.calendars[0]),this.draw()}},{key:"adjustCalendar",value:function(t){return t.month<0&&(t.year-=Math.ceil(Math.abs(t.month)/12),t.month+=12),t.month>11&&(t.year+=Math.floor(Math.abs(t.month)/12),t.month-=12),t}},{key:"nextMonth",value:function(){this.calendars[0].month++,this.adjustCalendars()}},{key:"prevMonth",value:function(){this.calendars[0].month--,this.adjustCalendars()}},{key:"render",value:function(t,e,i){var s=this.options,o=new Date,a=n._getDaysInMonth(t,e),r=new Date(t,e,1).getDay(),l=[],h=[];n._setToStartOfDay(o),s.firstDay>0&&(r-=s.firstDay)<0&&(r+=7);for(var d=0===e?11:e-1,u=11===e?0:e+1,c=0===e?t-1:t,p=11===e?t+1:t,v=n._getDaysInMonth(c,d),f=a+r,m=f;m>7;)m-=7;f+=7-m;for(var g=!1,_=0,y=0;_<f;_++){var k=new Date(t,e,_-r+1),b=!!n._isDate(this.date)&&n._compareDates(k,this.date),w=n._compareDates(k,o),C=-1!==s.events.indexOf(k.toDateString()),E=_<r||_>=a+r,M=_-r+1,O=e,x=t,L=s.startRange&&n._compareDates(s.startRange,k),T=s.endRange&&n._compareDates(s.endRange,k),$=s.startRange&&s.endRange&&s.startRange<k&&k<s.endRange,B=s.minDate&&k<s.minDate||s.maxDate&&k>s.maxDate||s.disableWeekends&&n._isWeekend(k)||s.disableDayFn&&s.disableDayFn(k);E&&(_<r?(M=v+M,O=d,x=c):(M-=a,O=u,x=p));var D={day:M,month:O,year:x,hasEvent:C,isSelected:b,isToday:w,isDisabled:B,isEmpty:E,isStartRange:L,isEndRange:T,isInRange:$,showDaysInNextAndPreviousMonths:s.showDaysInNextAndPreviousMonths};h.push(this.renderDay(D)),7==++y&&(l.push(this.renderRow(h,s.isRTL,g)),h=[],y=0,g=!1)}return this.renderTable(s,l,i)}},{key:"renderDay",value:function(t){var e=[],i="false";if(t.isEmpty){if(!t.showDaysInNextAndPreviousMonths)return'<td class="is-empty"></td>';e.push("is-outside-current-month"),e.push("is-selection-disabled")}return t.isDisabled&&e.push("is-disabled"),t.isToday&&e.push("is-today"),t.isSelected&&(e.push("is-selected"),i="true"),t.hasEvent&&e.push("has-event"),t.isInRange&&e.push("is-inrange"),t.isStartRange&&e.push("is-startrange"),t.isEndRange&&e.push("is-endrange"),'<td data-day="'+t.day+'" class="'+e.join(" ")+'" aria-selected="'+i+'"><button class="datepicker-day-button" type="button" data-year="'+t.year+'" data-month="'+t.month+'" data-day="'+t.day+'">'+t.day+"</button></td>"}},{key:"renderRow",value:function(t,e,i){return'<tr class="datepicker-row'+(i?" is-selected":"")+'">'+(e?t.reverse():t).join("")+"</tr>"}},{key:"renderTable",value:function(t,e,i){return'<div class="datepicker-table-wrapper"><table cellpadding="0" cellspacing="0" class="datepicker-table" role="grid" aria-labelledby="'+i+'">'+this.renderHead(t)+this.renderBody(e)+"</table></div>"}},{key:"renderHead",value:function(t){var e=void 0,i=[];for(e=0;e<7;e++)i.push('<th scope="col"><abbr title="'+this.renderDayName(t,e)+'">'+this.renderDayName(t,e,!0)+"</abbr></th>");return"<thead><tr>"+(t.isRTL?i.reverse():i).join("")+"</tr></thead>"}},{key:"renderBody",value:function(t){return"<tbody>"+t.join("")+"</tbody>"}},{key:"renderTitle",value:function(e,i,n,s,o,a){var r=void 0,l=void 0,h=void 0,d=this.options,u=n===d.minYear,c=n===d.maxYear,p='<div id="'+a+'" class="datepicker-controls" role="heading" aria-live="assertive">',v=void 0,f=void 0,m=!0,g=!0;for(h=[],r=0;r<12;r++)h.push('<option value="'+(n===o?r-i:12+r-i)+'"'+(r===s?' selected="selected"':"")+(u&&r<d.minMonth||c&&r>d.maxMonth?'disabled="disabled"':"")+">"+d.i18n.months[r]+"</option>");for(v='<select class="datepicker-select orig-select-month" tabindex="-1">'+h.join("")+"</select>",t.isArray(d.yearRange)?(r=d.yearRange[0],l=d.yearRange[1]+1):(r=n-d.yearRange,l=1+n+d.yearRange),h=[];r<l&&r<=d.maxYear;r++)r>=d.minYear&&h.push('<option value="'+r+'" '+(r===n?'selected="selected"':"")+">"+r+"</option>");f='<select class="datepicker-select orig-select-year" tabindex="-1">'+h.join("")+"</select>";p+='<button class="month-prev'+(m?"":" is-disabled")+'" type="button"><svg fill="#000000" height="24" viewBox="0 0 24 24" width="24" xmlns="http://www.w3.org/2000/svg"><path d="M15.41 16.09l-4.58-4.59 4.58-4.59L14 5.5l-6 6 6 6z"/><path d="M0-.5h24v24H0z" fill="none"/></svg></button>',p+='<div class="selects-container">',d.showMonthAfterYear?p+=f+v:p+=v+f,p+="</div>",u&&(0===s||d.minMonth>=s)&&(m=!1),c&&(11===s||d.maxMonth<=s)&&(g=!1);return p+='<button class="month-next'+(g?"":" is-disabled")+'" type="button"><svg fill="#000000" height="24" viewBox="0 0 24 24" width="24" xmlns="http://www.w3.org/2000/svg"><path d="M8.59 16.34l4.58-4.59-4.58-4.59L10 5.75l6 6-6 6z"/><path d="M0-.25h24v24H0z" fill="none"/></svg></button>',p+="</div>"}},{key:"draw",value:function(t){if(this.isOpen||t){var e=this.options,i=e.minYear,n=e.maxYear,s=e.minMonth,o=e.maxMonth,a="",r=void 0;this._y<=i&&(this._y=i,!isNaN(s)&&this._m<s&&(this._m=s)),this._y>=n&&(this._y=n,!isNaN(o)&&this._m>o&&(this._m=o)),r="datepicker-title-"+Math.random().toString(36).replace(/[^a-z]+/g,"").substr(0,2);for(var l=0;l<1;l++)this._renderDateDisplay(),a+=this.renderTitle(this,l,this.calendars[l].year,this.calendars[l].month,this.calendars[0].year,r)+this.render(this.calendars[l].year,this.calendars[l].month,r);this.destroySelects(),this.calendarEl.innerHTML=a;var h=this.calendarEl.querySelector(".orig-select-year"),d=this.calendarEl.querySelector(".orig-select-month");M.FormSelect.init(h,{classes:"select-year",dropdownOptions:{container:document.body,constrainWidth:!1}}),M.FormSelect.init(d,{classes:"select-month",dropdownOptions:{container:document.body,constrainWidth:!1}}),h.addEventListener("change",this._handleYearChange.bind(this)),d.addEventListener("change",this._handleMonthChange.bind(this)),"function"==typeof this.options.onDraw&&this.options.onDraw(this)}}},{key:"_setupEventHandlers",value:function(){this._handleInputKeydownBound=this._handleInputKeydown.bind(this),this._handleInputClickBound=this._handleInputClick.bind(this),this._handleInputChangeBound=this._handleInputChange.bind(this),this._handleCalendarClickBound=this._handleCalendarClick.bind(this),this._finishSelectionBound=this._finishSelection.bind(this),this._handleMonthChange=this._handleMonthChange.bind(this),this._closeBound=this.close.bind(this),this.el.addEventListener("click",this._handleInputClickBound),this.el.addEventListener("keydown",this._handleInputKeydownBound),this.el.addEventListener("change",this._handleInputChangeBound),this.calendarEl.addEventListener("click",this._handleCalendarClickBound),this.doneBtn.addEventListener("click",this._finishSelectionBound),this.cancelBtn.addEventListener("click",this._closeBound),this.options.showClearBtn&&(this._handleClearClickBound=this._handleClearClick.bind(this),this.clearBtn.addEventListener("click",this._handleClearClickBound))}},{key:"_setupVariables",value:function(){var e=this;this.$modalEl=t(n._template),this.modalEl=this.$modalEl[0],this.calendarEl=this.modalEl.querySelector(".datepicker-calendar"),this.yearTextEl=this.modalEl.querySelector(".year-text"),this.dateTextEl=this.modalEl.querySelector(".date-text"),this.options.showClearBtn&&(this.clearBtn=this.modalEl.querySelector(".datepicker-clear")),this.doneBtn=this.modalEl.querySelector(".datepicker-done"),this.cancelBtn=this.modalEl.querySelector(".datepicker-cancel"),this.formats={d:function(){return e.date.getDate()},dd:function(){var t=e.date.getDate();return(t<10?"0":"")+t},ddd:function(){return e.options.i18n.weekdaysShort[e.date.getDay()]},dddd:function(){return e.options.i18n.weekdays[e.date.getDay()]},m:function(){return e.date.getMonth()+1},mm:function(){var t=e.date.getMonth()+1;return(t<10?"0":"")+t},mmm:function(){return e.options.i18n.monthsShort[e.date.getMonth()]},mmmm:function(){return e.options.i18n.months[e.date.getMonth()]},yy:function(){return(""+e.date.getFullYear()).slice(2)},yyyy:function(){return e.date.getFullYear()}}}},{key:"_removeEventHandlers",value:function(){this.el.removeEventListener("click",this._handleInputClickBound),this.el.removeEventListener("keydown",this._handleInputKeydownBound),this.el.removeEventListener("change",this._handleInputChangeBound),this.calendarEl.removeEventListener("click",this._handleCalendarClickBound)}},{key:"_handleInputClick",value:function(){this.open()}},{key:"_handleInputKeydown",value:function(t){t.which===M.keys.ENTER&&(t.preventDefault(),this.open())}},{key:"_handleCalendarClick",value:function(e){if(this.isOpen){var i=t(e.target);i.hasClass("is-disabled")||(!i.hasClass("datepicker-day-button")||i.hasClass("is-empty")||i.parent().hasClass("is-disabled")?i.closest(".month-prev").length?this.prevMonth():i.closest(".month-next").length&&this.nextMonth():(this.setDate(new Date(e.target.getAttribute("data-year"),e.target.getAttribute("data-month"),e.target.getAttribute("data-day"))),this.options.autoClose&&this._finishSelection()))}}},{key:"_handleClearClick",value:function(){this.date=null,this.setInputValue(),this.close()}},{key:"_handleMonthChange",value:function(t){this.gotoMonth(t.target.value)}},{key:"_handleYearChange",value:function(t){this.gotoYear(t.target.value)}},{key:"gotoMonth",value:function(t){isNaN(t)||(this.calendars[0].month=parseInt(t,10),this.adjustCalendars())}},{key:"gotoYear",value:function(t){isNaN(t)||(this.calendars[0].year=parseInt(t,10),this.adjustCalendars())}},{key:"_handleInputChange",value:function(t){var e=void 0;t.firedBy!==this&&(e=this.options.parse?this.options.parse(this.el.value,this.options.format):new Date(Date.parse(this.el.value)),n._isDate(e)&&this.setDate(e))}},{key:"renderDayName",value:function(t,e,i){for(e+=t.firstDay;e>=7;)e-=7;return i?t.i18n.weekdaysAbbrev[e]:t.i18n.weekdays[e]}},{key:"_finishSelection",value:function(){this.setInputValue(),this.close()}},{key:"open",value:function(){if(!this.isOpen)return this.isOpen=!0,"function"==typeof this.options.onOpen&&this.options.onOpen.call(this),this.draw(),this.modal.open(),this}},{key:"close",value:function(){if(this.isOpen)return this.isOpen=!1,"function"==typeof this.options.onClose&&this.options.onClose.call(this),this.modal.close(),this}}],[{key:"init",value:function(t,e){return _get(n.__proto__||Object.getPrototypeOf(n),"init",this).call(this,this,t,e)}},{key:"_isDate",value:function(t){return/Date/.test(Object.prototype.toString.call(t))&&!isNaN(t.getTime())}},{key:"_isWeekend",value:function(t){var e=t.getDay();return 0===e||6===e}},{key:"_setToStartOfDay",value:function(t){n._isDate(t)&&t.setHours(0,0,0,0)}},{key:"_getDaysInMonth",value:function(t,e){return[31,n._isLeapYear(t)?29:28,31,30,31,30,31,31,30,31,30,31][e]}},{key:"_isLeapYear",value:function(t){return t%4==0&&t%100!=0||t%400==0}},{key:"_compareDates",value:function(t,e){return t.getTime()===e.getTime()}},{key:"_setToStartOfDay",value:function(t){n._isDate(t)&&t.setHours(0,0,0,0)}},{key:"getInstance",value:function(t){return(t.jquery?t[0]:t).M_Datepicker}},{key:"defaults",get:function(){return e}}]),n}(Component);i._template=['<div class= "modal datepicker-modal">','<div class="modal-content datepicker-container">','<div class="datepicker-date-display">','<span class="year-text"></span>','<span class="date-text"></span>',"</div>",'<div class="datepicker-calendar-container">','<div class="datepicker-calendar"></div>','<div class="datepicker-footer">','<button class="btn-flat datepicker-clear waves-effect" style="visibility: hidden;" type="button"></button>','<div class="confirmation-btns">','<button class="btn-flat datepicker-cancel waves-effect" type="button"></button>','<button class="btn-flat datepicker-done waves-effect" type="button"></button>',"</div>","</div>","</div>","</div>","</div>"].join(""),M.Datepicker=i,M.jQueryLoaded&&M.initializeJqueryWrapper(i,"datepicker","M_Datepicker")}(cash),function(t){"use strict";var e={dialRadius:135,outerRadius:105,innerRadius:70,tickRadius:20,duration:350,container:null,defaultTime:"now",fromNow:0,showClearBtn:!1,i18n:{cancel:"Cancel",clear:"Clear",done:"Ok"},autoClose:!1,twelveHour:!0,vibrate:!0,onOpenStart:null,onOpenEnd:null,onCloseStart:null,onCloseEnd:null,onSelect:null},i=function(i){function n(e,i){_classCallCheck(this,n);var s=_possibleConstructorReturn(this,(n.__proto__||Object.getPrototypeOf(n)).call(this,n,e,i));return s.el.M_Timepicker=s,s.options=t.extend({},n.defaults,i),s.id=M.guid(),s._insertHTMLIntoDOM(),s._setupModal(),s._setupVariables(),s._setupEventHandlers(),s._clockSetup(),s._pickerSetup(),s}return _inherits(n,i),_createClass(n,[{key:"destroy",value:function(){this._removeEventHandlers(),this.modal.destroy(),t(this.modalEl).remove(),this.el.M_Timepicker=void 0}},{key:"_setupEventHandlers",value:function(){this._handleInputKeydownBound=this._handleInputKeydown.bind(this),this._handleInputClickBound=this._handleInputClick.bind(this),this._handleClockClickStartBound=this._handleClockClickStart.bind(this),this._handleDocumentClickMoveBound=this._handleDocumentClickMove.bind(this),this._handleDocumentClickEndBound=this._handleDocumentClickEnd.bind(this),this.el.addEventListener("click",this._handleInputClickBound),this.el.addEventListener("keydown",this._handleInputKeydownBound),this.plate.addEventListener("mousedown",this._handleClockClickStartBound),this.plate.addEventListener("touchstart",this._handleClockClickStartBound),t(this.spanHours).on("click",this.showView.bind(this,"hours")),t(this.spanMinutes).on("click",this.showView.bind(this,"minutes"))}},{key:"_removeEventHandlers",value:function(){this.el.removeEventListener("click",this._handleInputClickBound),this.el.removeEventListener("keydown",this._handleInputKeydownBound)}},{key:"_handleInputClick",value:function(){this.open()}},{key:"_handleInputKeydown",value:function(t){t.which===M.keys.ENTER&&(t.preventDefault(),this.open())}},{key:"_handleClockClickStart",value:function(t){t.preventDefault();var e=this.plate.getBoundingClientRect(),i={x:e.left,y:e.top};this.x0=i.x+this.options.dialRadius,this.y0=i.y+this.options.dialRadius,this.moved=!1;var s=n._Pos(t);this.dx=s.x-this.x0,this.dy=s.y-this.y0,this.setHand(this.dx,this.dy,!1),document.addEventListener("mousemove",this._handleDocumentClickMoveBound),document.addEventListener("touchmove",this._handleDocumentClickMoveBound),document.addEventListener("mouseup",this._handleDocumentClickEndBound),document.addEventListener("touchend",this._handleDocumentClickEndBound)}},{key:"_handleDocumentClickMove",value:function(t){t.preventDefault();var e=n._Pos(t),i=e.x-this.x0,s=e.y-this.y0;this.moved=!0,this.setHand(i,s,!1,!0)}},{key:"_handleDocumentClickEnd",value:function(e){var i=this;e.preventDefault(),document.removeEventListener("mouseup",this._handleDocumentClickEndBound),document.removeEventListener("touchend",this._handleDocumentClickEndBound);var s=n._Pos(e),o=s.x-this.x0,a=s.y-this.y0;this.moved&&o===this.dx&&a===this.dy&&this.setHand(o,a),"hours"===this.currentView?this.showView("minutes",this.options.duration/2):this.options.autoClose&&(t(this.minutesView).addClass("timepicker-dial-out"),setTimeout(function(){i.done()},this.options.duration/2)),"function"==typeof this.options.onSelect&&this.options.onSelect.call(this,this.hours,this.minutes),document.removeEventListener("mousemove",this._handleDocumentClickMoveBound),document.removeEventListener("touchmove",this._handleDocumentClickMoveBound)}},{key:"_insertHTMLIntoDOM",value:function(){this.$modalEl=t(n._template),this.modalEl=this.$modalEl[0],this.modalEl.id="modal-"+this.id;var e=document.querySelector(this.options.container);this.options.container&&e?this.$modalEl.appendTo(e):this.$modalEl.insertBefore(this.el)}},{key:"_setupModal",value:function(){var t=this;this.modal=M.Modal.init(this.modalEl,{onOpenStart:this.options.onOpenStart,onOpenEnd:this.options.onOpenEnd,onCloseStart:this.options.onCloseStart,onCloseEnd:function(){"function"==typeof t.options.onCloseEnd&&t.options.onCloseEnd.call(t),t.isOpen=!1}})}},{key:"_setupVariables",value:function(){this.currentView="hours",this.vibrate=navigator.vibrate?"vibrate":navigator.webkitVibrate?"webkitVibrate":null,this._canvas=this.modalEl.querySelector(".timepicker-canvas"),this.plate=this.modalEl.querySelector(".timepicker-plate"),this.hoursView=this.modalEl.querySelector(".timepicker-hours"),this.minutesView=this.modalEl.querySelector(".timepicker-minutes"),this.spanHours=this.modalEl.querySelector(".timepicker-span-hours"),this.spanMinutes=this.modalEl.querySelector(".timepicker-span-minutes"),this.spanAmPm=this.modalEl.querySelector(".timepicker-span-am-pm"),this.footer=this.modalEl.querySelector(".timepicker-footer"),this.amOrPm="PM"}},{key:"_pickerSetup",value:function(){var e=t('<button class="btn-flat timepicker-clear waves-effect" style="visibility: hidden;" type="button" tabindex="'+(this.options.twelveHour?"3":"1")+'">'+this.options.i18n.clear+"</button>").appendTo(this.footer).on("click",this.clear.bind(this));this.options.showClearBtn&&e.css({visibility:""});var i=t('<div class="confirmation-btns"></div>');t('<button class="btn-flat timepicker-close waves-effect" type="button" tabindex="'+(this.options.twelveHour?"3":"1")+'">'+this.options.i18n.cancel+"</button>").appendTo(i).on("click",this.close.bind(this)),t('<button class="btn-flat timepicker-close waves-effect" type="button" tabindex="'+(this.options.twelveHour?"3":"1")+'">'+this.options.i18n.done+"</button>").appendTo(i).on("click",this.done.bind(this)),i.appendTo(this.footer)}},{key:"_clockSetup",value:function(){this.options.twelveHour&&(this.$amBtn=t('<div class="am-btn">AM</div>'),this.$pmBtn=t('<div class="pm-btn">PM</div>'),this.$amBtn.on("click",this._handleAmPmClick.bind(this)).appendTo(this.spanAmPm),this.$pmBtn.on("click",this._handleAmPmClick.bind(this)).appendTo(this.spanAmPm)),this._buildHoursView(),this._buildMinutesView(),this._buildSVGClock()}},{key:"_buildSVGClock",value:function(){var t=this.options.dialRadius,e=this.options.tickRadius,i=2*t,s=n._createSVGEl("svg");s.setAttribute("class","timepicker-svg"),s.setAttribute("width",i),s.setAttribute("height",i);var o=n._createSVGEl("g");o.setAttribute("transform","translate("+t+","+t+")");var a=n._createSVGEl("circle");a.setAttribute("class","timepicker-canvas-bearing"),a.setAttribute("cx",0),a.setAttribute("cy",0),a.setAttribute("r",4);var r=n._createSVGEl("line");r.setAttribute("x1",0),r.setAttribute("y1",0);var l=n._createSVGEl("circle");l.setAttribute("class","timepicker-canvas-bg"),l.setAttribute("r",e),o.appendChild(r),o.appendChild(l),o.appendChild(a),s.appendChild(o),this._canvas.appendChild(s),this.hand=r,this.bg=l,this.bearing=a,this.g=o}},{key:"_buildHoursView",value:function(){var e=t('<div class="timepicker-tick"></div>');if(this.options.twelveHour)for(var i=1;i<13;i+=1){var n=e.clone(),s=i/6*Math.PI,o=this.options.outerRadius;n.css({left:this.options.dialRadius+Math.sin(s)*o-this.options.tickRadius+"px",top:this.options.dialRadius-Math.cos(s)*o-this.options.tickRadius+"px"}),n.html(0===i?"00":i),this.hoursView.appendChild(n[0])}else for(var a=0;a<24;a+=1){var r=e.clone(),l=a/6*Math.PI,h=a>0&&a<13?this.options.innerRadius:this.options.outerRadius;r.css({left:this.options.dialRadius+Math.sin(l)*h-this.options.tickRadius+"px",top:this.options.dialRadius-Math.cos(l)*h-this.options.tickRadius+"px"}),r.html(0===a?"00":a),this.hoursView.appendChild(r[0])}}},{key:"_buildMinutesView",value:function(){for(var e=t('<div class="timepicker-tick"></div>'),i=0;i<60;i+=5){var s=e.clone(),o=i/30*Math.PI;s.css({left:this.options.dialRadius+Math.sin(o)*this.options.outerRadius-this.options.tickRadius+"px",top:this.options.dialRadius-Math.cos(o)*this.options.outerRadius-this.options.tickRadius+"px"}),s.html(n._addLeadingZero(i)),this.minutesView.appendChild(s[0])}}},{key:"_handleAmPmClick",value:function(e){var i=t(e.target);this.amOrPm=i.hasClass("am-btn")?"AM":"PM",this._updateAmPmView()}},{key:"_updateAmPmView",value:function(){this.options.twelveHour&&(this.$amBtn.toggleClass("text-primary","AM"===this.amOrPm),this.$pmBtn.toggleClass("text-primary","PM"===this.amOrPm))}},{key:"_updateTimeFromInput",value:function(){var t=((this.el.value||this.options.defaultTime||"")+"").split(":");if(this.options.twelveHour&&void 0!==t[1]&&(t[1].toUpperCase().indexOf("AM")>0?this.amOrPm="AM":this.amOrPm="PM",t[1]=t[1].replace("AM","").replace("PM","")),"now"===t[0]){var e=new Date(+new Date+this.options.fromNow);t=[e.getHours(),e.getMinutes()],this.options.twelveHour&&(this.amOrPm=t[0]>=12&&t[0]<24?"PM":"AM")}this.hours=+t[0]||0,this.minutes=+t[1]||0,this.spanHours.innerHTML=this.hours,this.spanMinutes.innerHTML=n._addLeadingZero(this.minutes),this._updateAmPmView()}},{key:"showView",value:function(e,i){"minutes"===e&&t(this.hoursView).css("visibility");var n="hours"===e,s=n?this.hoursView:this.minutesView,o=n?this.minutesView:this.hoursView;this.currentView=e,t(this.spanHours).toggleClass("text-primary",n),t(this.spanMinutes).toggleClass("text-primary",!n),o.classList.add("timepicker-dial-out"),t(s).css("visibility","visible").removeClass("timepicker-dial-out"),this.resetClock(i),clearTimeout(this.toggleViewTimer),this.toggleViewTimer=setTimeout(function(){t(o).css("visibility","hidden")},this.options.duration)}},{key:"resetClock",value:function(e){var i=this.currentView,n=this[i],s="hours"===i,o=n*(Math.PI/(s?6:30)),a=s&&n>0&&n<13?this.options.innerRadius:this.options.outerRadius,r=Math.sin(o)*a,l=-Math.cos(o)*a,h=this;e?(t(this.canvas).addClass("timepicker-canvas-out"),setTimeout(function(){t(h.canvas).removeClass("timepicker-canvas-out"),h.setHand(r,l)},e)):this.setHand(r,l)}},{key:"setHand",value:function(t,e,i){var s=this,o=Math.atan2(t,-e),a="hours"===this.currentView,r=Math.PI/(a||i?6:30),l=Math.sqrt(t*t+e*e),h=a&&l<(this.options.outerRadius+this.options.innerRadius)/2,d=h?this.options.innerRadius:this.options.outerRadius;this.options.twelveHour&&(d=this.options.outerRadius),o<0&&(o=2*Math.PI+o);var u=Math.round(o/r);o=u*r,this.options.twelveHour?a?0===u&&(u=12):(i&&(u*=5),60===u&&(u=0)):a?(12===u&&(u=0),u=h?0===u?12:u:0===u?0:u+12):(i&&(u*=5),60===u&&(u=0)),this[this.currentView]!==u&&this.vibrate&&this.options.vibrate&&(this.vibrateTimer||(navigator[this.vibrate](10),this.vibrateTimer=setTimeout(function(){s.vibrateTimer=null},100))),this[this.currentView]=u,a?this.spanHours.innerHTML=u:this.spanMinutes.innerHTML=n._addLeadingZero(u);var c=Math.sin(o)*(d-this.options.tickRadius),p=-Math.cos(o)*(d-this.options.tickRadius),v=Math.sin(o)*d,f=-Math.cos(o)*d;this.hand.setAttribute("x2",c),this.hand.setAttribute("y2",p),this.bg.setAttribute("cx",v),this.bg.setAttribute("cy",f)}},{key:"open",value:function(){this.isOpen||(this.isOpen=!0,this._updateTimeFromInput(),this.showView("hours"),this.modal.open())}},{key:"close",value:function(){this.isOpen&&(this.isOpen=!1,this.modal.close())}},{key:"done",value:function(t,e){var i=this.el.value,s=e?"":n._addLeadingZero(this.hours)+":"+n._addLeadingZero(this.minutes);this.time=s,!e&&this.options.twelveHour&&(s=s+" "+this.amOrPm),this.el.value=s,s!==i&&this.$el.trigger("change"),this.close(),this.el.focus()}},{key:"clear",value:function(){this.done(null,!0)}}],[{key:"init",value:function(t,e){return _get(n.__proto__||Object.getPrototypeOf(n),"init",this).call(this,this,t,e)}},{key:"_addLeadingZero",value:function(t){return(t<10?"0":"")+t}},{key:"_createSVGEl",value:function(t){return document.createElementNS("http://www.w3.org/2000/svg",t)}},{key:"_Pos",value:function(t){return t.targetTouches&&t.targetTouches.length>=1?{x:t.targetTouches[0].clientX,y:t.targetTouches[0].clientY}:{x:t.clientX,y:t.clientY}}},{key:"getInstance",value:function(t){return(t.jquery?t[0]:t).M_Timepicker}},{key:"defaults",get:function(){return e}}]),n}(Component);i._template=['<div class= "modal timepicker-modal">','<div class="modal-content timepicker-container">','<div class="timepicker-digital-display">','<div class="timepicker-text-container">','<div class="timepicker-display-column">','<span class="timepicker-span-hours text-primary"></span>',":",'<span class="timepicker-span-minutes"></span>',"</div>",'<div class="timepicker-display-column timepicker-display-am-pm">','<div class="timepicker-span-am-pm"></div>',"</div>","</div>","</div>",'<div class="timepicker-analog-display">','<div class="timepicker-plate">','<div class="timepicker-canvas"></div>','<div class="timepicker-dial timepicker-hours"></div>','<div class="timepicker-dial timepicker-minutes timepicker-dial-out"></div>',"</div>",'<div class="timepicker-footer"></div>',"</div>","</div>","</div>"].join(""),M.Timepicker=i,M.jQueryLoaded&&M.initializeJqueryWrapper(i,"timepicker","M_Timepicker")}(cash),function(t){"use strict";var e={},i=function(i){function n(e,i){_classCallCheck(this,n);var s=_possibleConstructorReturn(this,(n.__proto__||Object.getPrototypeOf(n)).call(this,n,e,i));return s.el.M_CharacterCounter=s,s.options=t.extend({},n.defaults,i),s.isInvalid=!1,s.isValidLength=!1,s._setupCounter(),s._setupEventHandlers(),s}return _inherits(n,i),_createClass(n,[{key:"destroy",value:function(){this._removeEventHandlers(),this.el.CharacterCounter=void 0,this._removeCounter()}},{key:"_setupEventHandlers",value:function(){this._handleUpdateCounterBound=this.updateCounter.bind(this),this.el.addEventListener("focus",this._handleUpdateCounterBound,!0),this.el.addEventListener("input",this._handleUpdateCounterBound,!0)}},{key:"_removeEventHandlers",value:function(){this.el.removeEventListener("focus",this._handleUpdateCounterBound,!0),this.el.removeEventListener("input",this._handleUpdateCounterBound,!0)}},{key:"_setupCounter",value:function(){this.counterEl=document.createElement("span"),t(this.counterEl).addClass("character-counter").css({float:"right","font-size":"12px",height:1}),this.$el.parent().append(this.counterEl)}},{key:"_removeCounter",value:function(){t(this.counterEl).remove()}},{key:"updateCounter",value:function(){var e=+this.$el.attr("data-length"),i=this.el.value.length;this.isValidLength=i<=e;var n=i;e&&(n+="/"+e,this._validateInput()),t(this.counterEl).html(n)}},{key:"_validateInput",value:function(){this.isValidLength&&this.isInvalid?(this.isInvalid=!1,this.$el.removeClass("invalid")):this.isValidLength||this.isInvalid||(this.isInvalid=!0,this.$el.removeClass("valid"),this.$el.addClass("invalid"))}}],[{key:"init",value:function(t,e){return _get(n.__proto__||Object.getPrototypeOf(n),"init",this).call(this,this,t,e)}},{key:"getInstance",value:function(t){return(t.jquery?t[0]:t).M_CharacterCounter}},{key:"defaults",get:function(){return e}}]),n}(Component);M.CharacterCounter=i,M.jQueryLoaded&&M.initializeJqueryWrapper(i,"characterCounter","M_CharacterCounter")}(cash),function(t){"use strict";var e={duration:200,dist:-100,shift:0,padding:0,numVisible:5,fullWidth:!1,indicators:!1,noWrap:!1,onCycleTo:null},i=function(i){function n(e,i){_classCallCheck(this,n);var s=_possibleConstructorReturn(this,(n.__proto__||Object.getPrototypeOf(n)).call(this,n,e,i));return s.el.M_Carousel=s,s.options=t.extend({},n.defaults,i),s.hasMultipleSlides=s.$el.find(".carousel-item").length>1,s.showIndicators=s.options.indicators&&s.hasMultipleSlides,s.noWrap=s.options.noWrap||!s.hasMultipleSlides,s.pressed=!1,s.dragged=!1,s.offset=s.target=0,s.images=[],s.itemWidth=s.$el.find(".carousel-item").first().innerWidth(),s.itemHeight=s.$el.find(".carousel-item").first().innerHeight(),s.dim=2*s.itemWidth+s.options.padding||1,s._autoScrollBound=s._autoScroll.bind(s),s._trackBound=s._track.bind(s),s.options.fullWidth&&(s.options.dist=0,s._setCarouselHeight(),s.showIndicators&&s.$el.find(".carousel-fixed-item").addClass("with-indicators")),s.$indicators=t('<ul class="indicators"></ul>'),s.$el.find(".carousel-item").each(function(e,i){if(s.images.push(e),s.showIndicators){var n=t('<li class="indicator-item"></li>');0===i&&n[0].classList.add("active"),s.$indicators.append(n)}}),s.showIndicators&&s.$el.append(s.$indicators),s.count=s.images.length,s.options.numVisible=Math.min(s.count,s.options.numVisible),s.xform="transform",["webkit","Moz","O","ms"].every(function(t){var e=t+"Transform";return void 0===document.body.style[e]||(s.xform=e,!1)}),s._setupEventHandlers(),s._scroll(s.offset),s}return _inherits(n,i),_createClass(n,[{key:"destroy",value:function(){this._removeEventHandlers(),this.el.M_Carousel=void 0}},{key:"_setupEventHandlers",value:function(){var t=this;this._handleCarouselTapBound=this._handleCarouselTap.bind(this),this._handleCarouselDragBound=this._handleCarouselDrag.bind(this),this._handleCarouselReleaseBound=this._handleCarouselRelease.bind(this),this._handleCarouselClickBound=this._handleCarouselClick.bind(this),void 0!==window.ontouchstart&&(this.el.addEventListener("touchstart",this._handleCarouselTapBound),this.el.addEventListener("touchmove",this._handleCarouselDragBound),this.el.addEventListener("touchend",this._handleCarouselReleaseBound)),this.el.addEventListener("mousedown",this._handleCarouselTapBound),this.el.addEventListener("mousemove",this._handleCarouselDragBound),this.el.addEventListener("mouseup",this._handleCarouselReleaseBound),this.el.addEventListener("mouseleave",this._handleCarouselReleaseBound),this.el.addEventListener("click",this._handleCarouselClickBound),this.showIndicators&&this.$indicators&&(this._handleIndicatorClickBound=this._handleIndicatorClick.bind(this),this.$indicators.find(".indicator-item").each(function(e,i){e.addEventListener("click",t._handleIndicatorClickBound)}));var e=M.throttle(this._handleResize,200);this._handleThrottledResizeBound=e.bind(this),window.addEventListener("resize",this._handleThrottledResizeBound)}},{key:"_removeEventHandlers",value:function(){var t=this;void 0!==window.ontouchstart&&(this.el.removeEventListener("touchstart",this._handleCarouselTapBound),this.el.removeEventListener("touchmove",this._handleCarouselDragBound),this.el.removeEventListener("touchend",this._handleCarouselReleaseBound)),this.el.removeEventListener("mousedown",this._handleCarouselTapBound),this.el.removeEventListener("mousemove",this._handleCarouselDragBound),this.el.removeEventListener("mouseup",this._handleCarouselReleaseBound),this.el.removeEventListener("mouseleave",this._handleCarouselReleaseBound),this.el.removeEventListener("click",this._handleCarouselClickBound),this.showIndicators&&this.$indicators&&this.$indicators.find(".indicator-item").each(function(e,i){e.removeEventListener("click",t._handleIndicatorClickBound)}),window.removeEventListener("resize",this._handleThrottledResizeBound)}},{key:"_handleCarouselTap",value:function(e){"mousedown"===e.type&&t(e.target).is("img")&&e.preventDefault(),this.pressed=!0,this.dragged=!1,this.verticalDragged=!1,this.reference=this._xpos(e),this.referenceY=this._ypos(e),this.velocity=this.amplitude=0,this.frame=this.offset,this.timestamp=Date.now(),clearInterval(this.ticker),this.ticker=setInterval(this._trackBound,100)}},{key:"_handleCarouselDrag",value:function(t){var e=void 0,i=void 0,n=void 0;if(this.pressed)if(e=this._xpos(t),i=this._ypos(t),n=this.reference-e,Math.abs(this.referenceY-i)<30&&!this.verticalDragged)(n>2||n<-2)&&(this.dragged=!0,this.reference=e,this._scroll(this.offset+n));else{if(this.dragged)return t.preventDefault(),t.stopPropagation(),!1;this.verticalDragged=!0}if(this.dragged)return t.preventDefault(),t.stopPropagation(),!1}},{key:"_handleCarouselRelease",value:function(t){if(this.pressed)return this.pressed=!1,clearInterval(this.ticker),this.target=this.offset,(this.velocity>10||this.velocity<-10)&&(this.amplitude=.9*this.velocity,this.target=this.offset+this.amplitude),this.target=Math.round(this.target/this.dim)*this.dim,this.noWrap&&(this.target>=this.dim*(this.count-1)?this.target=this.dim*(this.count-1):this.target<0&&(this.target=0)),this.amplitude=this.target-this.offset,this.timestamp=Date.now(),requestAnimationFrame(this._autoScrollBound),this.dragged&&(t.preventDefault(),t.stopPropagation()),!1}},{key:"_handleCarouselClick",value:function(e){if(this.dragged)return e.preventDefault(),e.stopPropagation(),!1;if(!this.options.fullWidth){var i=t(e.target).closest(".carousel-item").index();0!==this._wrap(this.center)-i&&(e.preventDefault(),e.stopPropagation()),this._cycleTo(i)}}},{key:"_handleIndicatorClick",value:function(e){e.stopPropagation();var i=t(e.target).closest(".indicator-item");i.length&&this._cycleTo(i.index())}},{key:"_handleResize",value:function(t){this.options.fullWidth?(this.itemWidth=this.$el.find(".carousel-item").first().innerWidth(),this.imageHeight=this.$el.find(".carousel-item.active").height(),this.dim=2*this.itemWidth+this.options.padding,this.offset=2*this.center*this.itemWidth,this.target=this.offset,this._setCarouselHeight(!0)):this._scroll()}},{key:"_setCarouselHeight",value:function(t){var e=this,i=this.$el.find(".carousel-item.active").length?this.$el.find(".carousel-item.active").first():this.$el.find(".carousel-item").first(),n=i.find("img").first();if(n.length)if(n[0].complete){var s=n.height();if(s>0)this.$el.css("height",s+"px");else{var o=n[0].naturalWidth,a=n[0].naturalHeight,r=this.$el.width()/o*a;this.$el.css("height",r+"px")}}else n.one("load",function(t,i){e.$el.css("height",t.offsetHeight+"px")});else if(!t){var l=i.height();this.$el.css("height",l+"px")}}},{key:"_xpos",value:function(t){return t.targetTouches&&t.targetTouches.length>=1?t.targetTouches[0].clientX:t.clientX}},{key:"_ypos",value:function(t){return t.targetTouches&&t.targetTouches.length>=1?t.targetTouches[0].clientY:t.clientY}},{key:"_wrap",value:function(t){return t>=this.count?t%this.count:t<0?this._wrap(this.count+t%this.count):t}},{key:"_track",value:function(){var t=void 0,e=void 0,i=void 0,n=void 0;e=(t=Date.now())-this.timestamp,this.timestamp=t,i=this.offset-this.frame,this.frame=this.offset,n=1e3*i/(1+e),this.velocity=.8*n+.2*this.velocity}},{key:"_autoScroll",value:function(){var t=void 0,e=void 0;this.amplitude&&(t=Date.now()-this.timestamp,(e=this.amplitude*Math.exp(-t/this.options.duration))>2||e<-2?(this._scroll(this.target-e),requestAnimationFrame(this._autoScrollBound)):this._scroll(this.target))}},{key:"_scroll",value:function(e){var i=this;this.$el.hasClass("scrolling")||this.el.classList.add("scrolling"),null!=this.scrollingTimeout&&window.clearTimeout(this.scrollingTimeout),this.scrollingTimeout=window.setTimeout(function(){i.$el.removeClass("scrolling")},this.options.duration);var n=void 0,s=void 0,o=void 0,a=void 0,r=void 0,l=void 0,h=void 0,d=void 0,u=void 0,c=void 0,p=this.center,v=1/this.options.numVisible;if(this.offset="number"==typeof e?e:this.offset,this.center=Math.floor((this.offset+this.dim/2)/this.dim),o=this.offset-this.center*this.dim,a=o<0?1:-1,r=-a*o*2/this.dim,s=this.count>>1,this.options.fullWidth?(h="translateX(0)",c=1):(h="translateX("+(this.el.clientWidth-this.itemWidth)/2+"px) ",h+="translateY("+(this.el.clientHeight-this.itemHeight)/2+"px)",c=1-v*r),this.showIndicators){var f=this.center%this.count,m=this.$indicators.find(".indicator-item.active");m.index()!==f&&(m.removeClass("active"),this.$indicators.find(".indicator-item").eq(f)[0].classList.add("active"))}if(!this.noWrap||this.center>=0&&this.center<this.count){l=this.images[this._wrap(this.center)],t(l).hasClass("active")||(this.$el.find(".carousel-item").removeClass("active"),l.classList.add("active"));var g=h+" translateX("+-o/2+"px) translateX("+a*this.options.shift*r*n+"px) translateZ("+this.options.dist*r+"px)";this._updateItemStyle(l,c,0,g)}for(n=1;n<=s;++n){if(this.options.fullWidth?(d=this.options.dist,u=n===s&&o<0?1-r:1):(d=this.options.dist*(2*n+r*a),u=1-v*(2*n+r*a)),!this.noWrap||this.center+n<this.count){l=this.images[this._wrap(this.center+n)];var _=h+" translateX("+(this.options.shift+(this.dim*n-o)/2)+"px) translateZ("+d+"px)";this._updateItemStyle(l,u,-n,_)}if(this.options.fullWidth?(d=this.options.dist,u=n===s&&o>0?1-r:1):(d=this.options.dist*(2*n-r*a),u=1-v*(2*n-r*a)),!this.noWrap||this.center-n>=0){l=this.images[this._wrap(this.center-n)];var y=h+" translateX("+(-this.options.shift+(-this.dim*n-o)/2)+"px) translateZ("+d+"px)";this._updateItemStyle(l,u,-n,y)}}if(!this.noWrap||this.center>=0&&this.center<this.count){l=this.images[this._wrap(this.center)];var k=h+" translateX("+-o/2+"px) translateX("+a*this.options.shift*r+"px) translateZ("+this.options.dist*r+"px)";this._updateItemStyle(l,c,0,k)}var b=this.$el.find(".carousel-item").eq(this._wrap(this.center));p!==this.center&&"function"==typeof this.options.onCycleTo&&this.options.onCycleTo.call(this,b[0],this.dragged),"function"==typeof this.oneTimeCallback&&(this.oneTimeCallback.call(this,b[0],this.dragged),this.oneTimeCallback=null)}},{key:"_updateItemStyle",value:function(t,e,i,n){t.style[this.xform]=n,t.style.zIndex=i,t.style.opacity=e,t.style.visibility="visible"}},{key:"_cycleTo",value:function(t,e){var i=this.center%this.count-t;this.noWrap||(i<0?Math.abs(i+this.count)<Math.abs(i)&&(i+=this.count):i>0&&Math.abs(i-this.count)<i&&(i-=this.count)),this.target=this.dim*Math.round(this.offset/this.dim),i<0?this.target+=this.dim*Math.abs(i):i>0&&(this.target-=this.dim*i),"function"==typeof e&&(this.oneTimeCallback=e),this.offset!==this.target&&(this.amplitude=this.target-this.offset,this.timestamp=Date.now(),requestAnimationFrame(this._autoScrollBound))}},{key:"next",value:function(t){(void 0===t||isNaN(t))&&(t=1);var e=this.center+t;if(e>this.count||e<0){if(this.noWrap)return;e=this._wrap(e)}this._cycleTo(e)}},{key:"prev",value:function(t){(void 0===t||isNaN(t))&&(t=1);var e=this.center-t;if(e>this.count||e<0){if(this.noWrap)return;e=this._wrap(e)}this._cycleTo(e)}},{key:"set",value:function(t,e){if((void 0===t||isNaN(t))&&(t=0),t>this.count||t<0){if(this.noWrap)return;t=this._wrap(t)}this._cycleTo(t,e)}}],[{key:"init",value:function(t,e){return _get(n.__proto__||Object.getPrototypeOf(n),"init",this).call(this,this,t,e)}},{key:"getInstance",value:function(t){return(t.jquery?t[0]:t).M_Carousel}},{key:"defaults",get:function(){return e}}]),n}(Component);M.Carousel=i,M.jQueryLoaded&&M.initializeJqueryWrapper(i,"carousel","M_Carousel")}(cash),function(t){"use strict";var e={onOpen:void 0,onClose:void 0},i=function(i){function n(e,i){_classCallCheck(this,n);var s=_possibleConstructorReturn(this,(n.__proto__||Object.getPrototypeOf(n)).call(this,n,e,i));return s.el.M_TapTarget=s,s.options=t.extend({},n.defaults,i),s.isOpen=!1,s.$origin=t("#"+s.$el.attr("data-target")),s._setup(),s._calculatePositioning(),s._setupEventHandlers(),s}return _inherits(n,i),_createClass(n,[{key:"destroy",value:function(){this._removeEventHandlers(),this.el.TapTarget=void 0}},{key:"_setupEventHandlers",value:function(){this._handleDocumentClickBound=this._handleDocumentClick.bind(this),this._handleTargetClickBound=this._handleTargetClick.bind(this),this._handleOriginClickBound=this._handleOriginClick.bind(this),this.el.addEventListener("click",this._handleTargetClickBound),this.originEl.addEventListener("click",this._handleOriginClickBound);var t=M.throttle(this._handleResize,200);this._handleThrottledResizeBound=t.bind(this),window.addEventListener("resize",this._handleThrottledResizeBound)}},{key:"_removeEventHandlers",value:function(){this.el.removeEventListener("click",this._handleTargetClickBound),this.originEl.removeEventListener("click",this._handleOriginClickBound),window.removeEventListener("resize",this._handleThrottledResizeBound)}},{key:"_handleTargetClick",value:function(t){this.open()}},{key:"_handleOriginClick",value:function(t){this.close()}},{key:"_handleResize",value:function(t){this._calculatePositioning()}},{key:"_handleDocumentClick",value:function(e){t(e.target).closest(".tap-target-wrapper").length||(this.close(),e.preventDefault(),e.stopPropagation())}},{key:"_setup",value:function(){this.wrapper=this.$el.parent()[0],this.waveEl=t(this.wrapper).find(".tap-target-wave")[0],this.originEl=t(this.wrapper).find(".tap-target-origin")[0],this.contentEl=this.$el.find(".tap-target-content")[0],t(this.wrapper).hasClass(".tap-target-wrapper")||(this.wrapper=document.createElement("div"),this.wrapper.classList.add("tap-target-wrapper"),this.$el.before(t(this.wrapper)),this.wrapper.append(this.el)),this.contentEl||(this.contentEl=document.createElement("div"),this.contentEl.classList.add("tap-target-content"),this.$el.append(this.contentEl)),this.waveEl||(this.waveEl=document.createElement("div"),this.waveEl.classList.add("tap-target-wave"),this.originEl||(this.originEl=this.$origin.clone(!0,!0),this.originEl.addClass("tap-target-origin"),this.originEl.removeAttr("id"),this.originEl.removeAttr("style"),this.originEl=this.originEl[0],this.waveEl.append(this.originEl)),this.wrapper.append(this.waveEl))}},{key:"_calculatePositioning",value:function(){var e="fixed"===this.$origin.css("position");if(!e)for(var i=this.$origin.parents(),n=0;n<i.length&&!(e="fixed"==t(i[n]).css("position"));n++);var s=this.$origin.outerWidth(),o=this.$origin.outerHeight(),a=e?this.$origin.offset().top-M.getDocumentScrollTop():this.$origin.offset().top,r=e?this.$origin.offset().left-M.getDocumentScrollLeft():this.$origin.offset().left,l=window.innerWidth,h=window.innerHeight,d=l/2,u=h/2,c=r<=d,p=r>d,v=a<=u,f=a>u,m=r>=.25*l&&r<=.75*l,g=this.$el.outerWidth(),_=this.$el.outerHeight(),y=a+o/2-_/2,k=r+s/2-g/2,b=e?"fixed":"absolute",w=m?g:g/2+s,C=_/2,E=v?_/2:0,O=c&&!m?g/2-s:0,x=s,L=f?"bottom":"top",T=2*s,$=T,B=_/2-$/2,D=g/2-T/2,S={};S.top=v?y+"px":"",S.right=p?l-k-g+"px":"",S.bottom=f?h-y-_+"px":"",S.left=c?k+"px":"",S.position=b,t(this.wrapper).css(S),t(this.contentEl).css({width:w+"px",height:C+"px",top:E+"px",right:"0px",bottom:"0px",left:O+"px",padding:x+"px",verticalAlign:L}),t(this.waveEl).css({top:B+"px",left:D+"px",width:T+"px",height:$+"px"})}},{key:"open",value:function(){this.isOpen||("function"==typeof this.options.onOpen&&this.options.onOpen.call(this,this.$origin[0]),this.isOpen=!0,this.wrapper.classList.add("open"),document.body.addEventListener("click",this._handleDocumentClickBound,!0),document.body.addEventListener("touchend",this._handleDocumentClickBound))}},{key:"close",value:function(){this.isOpen&&("function"==typeof this.options.onClose&&this.options.onClose.call(this,this.$origin[0]),this.isOpen=!1,this.wrapper.classList.remove("open"),document.body.removeEventListener("click",this._handleDocumentClickBound,!0),document.body.removeEventListener("touchend",this._handleDocumentClickBound))}}],[{key:"init",value:function(t,e){return _get(n.__proto__||Object.getPrototypeOf(n),"init",this).call(this,this,t,e)}},{key:"getInstance",value:function(t){return(t.jquery?t[0]:t).M_TapTarget}},{key:"defaults",get:function(){return e}}]),n}(Component);M.TapTarget=i,M.jQueryLoaded&&M.initializeJqueryWrapper(i,"tapTarget","M_TapTarget")}(cash),function(t){"use strict";var e={classes:"",dropdownOptions:{}},i=function(i){function n(e,i){_classCallCheck(this,n);var s=_possibleConstructorReturn(this,(n.__proto__||Object.getPrototypeOf(n)).call(this,n,e,i));return s.$el.hasClass("browser-default")?_possibleConstructorReturn(s):(s.el.M_FormSelect=s,s.options=t.extend({},n.defaults,i),s.isMultiple=s.$el.prop("multiple"),s.el.tabIndex=-1,s._keysSelected={},s._valueDict={},s._setupDropdown(),s._setupEventHandlers(),s)}return _inherits(n,i),_createClass(n,[{key:"destroy",value:function(){this._removeEventHandlers(),this._removeDropdown(),this.el.M_FormSelect=void 0}},{key:"_setupEventHandlers",value:function(){var e=this;this._handleSelectChangeBound=this._handleSelectChange.bind(this),this._handleOptionClickBound=this._handleOptionClick.bind(this),this._handleInputClickBound=this._handleInputClick.bind(this),t(this.dropdownOptions).find("li:not(.optgroup)").each(function(t){t.addEventListener("click",e._handleOptionClickBound)}),this.el.addEventListener("change",this._handleSelectChangeBound),this.input.addEventListener("click",this._handleInputClickBound)}},{key:"_removeEventHandlers",value:function(){var e=this;t(this.dropdownOptions).find("li:not(.optgroup)").each(function(t){t.removeEventListener("click",e._handleOptionClickBound)}),this.el.removeEventListener("change",this._handleSelectChangeBound),this.input.removeEventListener("click",this._handleInputClickBound)}},{key:"_handleSelectChange",value:function(t){this._setValueToInput()}},{key:"_handleOptionClick",value:function(e){e.preventDefault();var i=t(e.target).closest("li")[0],n=i.id;if(!t(i).hasClass("disabled")&&!t(i).hasClass("optgroup")&&n.length){var s=!0;if(this.isMultiple){var o=t(this.dropdownOptions).find("li.disabled.selected");o.length&&(o.removeClass("selected"),o.find('input[type="checkbox"]').prop("checked",!1),this._toggleEntryFromArray(o[0].id)),s=this._toggleEntryFromArray(n)}else t(this.dropdownOptions).find("li").removeClass("selected"),t(i).toggleClass("selected",s);t(this._valueDict[n].el).prop("selected",s),this.$el.trigger("change")}e.stopPropagation()}},{key:"_handleInputClick",value:function(){this.dropdown&&this.dropdown.isOpen&&(this._setValueToInput(),this._setSelectedStates())}},{key:"_setupDropdown",value:function(){var e=this;this.wrapper=document.createElement("div"),t(this.wrapper).addClass("select-wrapper "+this.options.classes),this.$el.before(t(this.wrapper)),this.wrapper.appendChild(this.el),this.el.disabled&&this.wrapper.classList.add("disabled"),this.$selectOptions=this.$el.children("option, optgroup"),this.dropdownOptions=document.createElement("ul"),this.dropdownOptions.id="select-options-"+M.guid(),t(this.dropdownOptions).addClass("dropdown-content select-dropdown "+(this.isMultiple?"multiple-select-dropdown":"")),this.$selectOptions.length&&this.$selectOptions.each(function(i){if(t(i).is("option")){var n=void 0;n=e.isMultiple?e._appendOptionWithIcon(e.$el,i,"multiple"):e._appendOptionWithIcon(e.$el,i),e._addOptionToValueDict(i,n)}else if(t(i).is("optgroup")){var s=t(i).children("option");t(e.dropdownOptions).append(t('<li class="optgroup"><span>'+i.getAttribute("label")+"</span></li>")[0]),s.each(function(t){var i=e._appendOptionWithIcon(e.$el,t,"optgroup-option");e._addOptionToValueDict(t,i)})}}),this.$el.after(this.dropdownOptions),this.input=document.createElement("input"),t(this.input).addClass("select-dropdown dropdown-trigger"),this.input.setAttribute("type","text"),this.input.setAttribute("readonly","true"),this.input.setAttribute("data-target",this.dropdownOptions.id),this.el.disabled&&t(this.input).prop("disabled","true"),this.$el.before(this.input),this._setValueToInput();var i=t('<svg class="caret" height="24" viewBox="0 0 24 24" width="24" xmlns="http://www.w3.org/2000/svg"><path d="M7 10l5 5 5-5z"/><path d="M0 0h24v24H0z" fill="none"/></svg>');if(this.$el.before(i[0]),!this.el.disabled){var n=t.extend({},this.options.dropdownOptions);n.onOpenEnd=function(i){var n=t(e.dropdownOptions).find(".selected").first();if(e.dropdown.isScrollable&&n.length){var s=n[0].getBoundingClientRect().top-e.dropdownOptions.getBoundingClientRect().top;s-=e.dropdownOptions.clientHeight/2,e.dropdownOptions.scrollTop=s}},this.isMultiple&&(n.closeOnClick=!1),this.dropdown=M.Dropdown.init(this.input,n)}this._setSelectedStates()}},{key:"_addOptionToValueDict",value:function(t,e){var i=Object.keys(this._valueDict).length,n=this.dropdownOptions.id+i,s={};e.id=n,s.el=t,s.optionEl=e,this._valueDict[n]=s}},{key:"_removeDropdown",value:function(){t(this.wrapper).find(".caret").remove(),t(this.input).remove(),t(this.dropdownOptions).remove(),t(this.wrapper).before(this.$el),t(this.wrapper).remove()}},{key:"_appendOptionWithIcon",value:function(e,i,n){var s=i.disabled?"disabled ":"",o="optgroup-option"===n?"optgroup-option ":"",a=this.isMultiple?'<label><input type="checkbox"'+s+'"/><span>'+i.innerHTML+"</span></label>":i.innerHTML,r=t("<li></li>"),l=t("<span></span>");l.html(a),r.addClass(s+" "+o),r.append(l);var h=i.getAttribute("data-icon");if(h){var d=t('<img alt="" src="'+h+'">');r.prepend(d)}return t(this.dropdownOptions).append(r[0]),r[0]}},{key:"_toggleEntryFromArray",value:function(e){var i=!this._keysSelected.hasOwnProperty(e),n=t(this._valueDict[e].optionEl);return i?this._keysSelected[e]=!0:delete this._keysSelected[e],n.toggleClass("selected",i),n.find('input[type="checkbox"]').prop("checked",i),n.prop("selected",i),i}},{key:"_setValueToInput",value:function(){var e=[];if(this.$el.find("option").each(function(i){if(t(i).prop("selected")){var n=t(i).text();e.push(n)}}),!e.length){var i=this.$el.find("option:disabled").eq(0);i.length&&""===i[0].value&&e.push(i.text())}this.input.value=e.join(", ")}},{key:"_setSelectedStates",value:function(){this._keysSelected={};for(var e in this._valueDict){var i=this._valueDict[e],n=t(i.el).prop("selected");t(i.optionEl).find('input[type="checkbox"]').prop("checked",n),n?(this._activateOption(t(this.dropdownOptions),t(i.optionEl)),this._keysSelected[e]=!0):t(i.optionEl).removeClass("selected")}}},{key:"_activateOption",value:function(e,i){i&&(this.isMultiple||e.find("li.selected").removeClass("selected"),t(i).addClass("selected"))}},{key:"getSelectedValues",value:function(){var t=[];for(var e in this._keysSelected)t.push(this._valueDict[e].el.value);return t}}],[{key:"init",value:function(t,e){return _get(n.__proto__||Object.getPrototypeOf(n),"init",this).call(this,this,t,e)}},{key:"getInstance",value:function(t){return(t.jquery?t[0]:t).M_FormSelect}},{key:"defaults",get:function(){return e}}]),n}(Component);M.FormSelect=i,M.jQueryLoaded&&M.initializeJqueryWrapper(i,"formSelect","M_FormSelect")}(cash),function(t,e){"use strict";var i={},n=function(n){function s(e,i){_classCallCheck(this,s);var n=_possibleConstructorReturn(this,(s.__proto__||Object.getPrototypeOf(s)).call(this,s,e,i));return n.el.M_Range=n,n.options=t.extend({},s.defaults,i),n._mousedown=!1,n._setupThumb(),n._setupEventHandlers(),n}return _inherits(s,n),_createClass(s,[{key:"destroy",value:function(){this._removeEventHandlers(),this._removeThumb(),this.el.M_Range=void 0}},{key:"_setupEventHandlers",value:function(){this._handleRangeChangeBound=this._handleRangeChange.bind(this),this._handleRangeMousedownTouchstartBound=this._handleRangeMousedownTouchstart.bind(this),this._handleRangeInputMousemoveTouchmoveBound=this._handleRangeInputMousemoveTouchmove.bind(this),this._handleRangeMouseupTouchendBound=this._handleRangeMouseupTouchend.bind(this),this._handleRangeBlurMouseoutTouchleaveBound=this._handleRangeBlurMouseoutTouchleave.bind(this),this.el.addEventListener("change",this._handleRangeChangeBound),this.el.addEventListener("mousedown",this._handleRangeMousedownTouchstartBound),this.el.addEventListener("touchstart",this._handleRangeMousedownTouchstartBound),this.el.addEventListener("input",this._handleRangeInputMousemoveTouchmoveBound),this.el.addEventListener("mousemove",this._handleRangeInputMousemoveTouchmoveBound),this.el.addEventListener("touchmove",this._handleRangeInputMousemoveTouchmoveBound),this.el.addEventListener("mouseup",this._handleRangeMouseupTouchendBound),this.el.addEventListener("touchend",this._handleRangeMouseupTouchendBound),this.el.addEventListener("blur",this._handleRangeBlurMouseoutTouchleaveBound),this.el.addEventListener("mouseout",this._handleRangeBlurMouseoutTouchleaveBound),this.el.addEventListener("touchleave",this._handleRangeBlurMouseoutTouchleaveBound)}},{key:"_removeEventHandlers",value:function(){this.el.removeEventListener("change",this._handleRangeChangeBound),this.el.removeEventListener("mousedown",this._handleRangeMousedownTouchstartBound),this.el.removeEventListener("touchstart",this._handleRangeMousedownTouchstartBound),this.el.removeEventListener("input",this._handleRangeInputMousemoveTouchmoveBound),this.el.removeEventListener("mousemove",this._handleRangeInputMousemoveTouchmoveBound),this.el.removeEventListener("touchmove",this._handleRangeInputMousemoveTouchmoveBound),this.el.removeEventListener("mouseup",this._handleRangeMouseupTouchendBound),this.el.removeEventListener("touchend",this._handleRangeMouseupTouchendBound),this.el.removeEventListener("blur",this._handleRangeBlurMouseoutTouchleaveBound),this.el.removeEventListener("mouseout",this._handleRangeBlurMouseoutTouchleaveBound),this.el.removeEventListener("touchleave",this._handleRangeBlurMouseoutTouchleaveBound)}},{key:"_handleRangeChange",value:function(){t(this.value).html(this.$el.val()),t(this.thumb).hasClass("active")||this._showRangeBubble();var e=this._calcRangeOffset();t(this.thumb).addClass("active").css("left",e+"px")}},{key:"_handleRangeMousedownTouchstart",value:function(e){if(t(this.value).html(this.$el.val()),this._mousedown=!0,this.$el.addClass("active"),t(this.thumb).hasClass("active")||this._showRangeBubble(),"input"!==e.type){var i=this._calcRangeOffset();t(this.thumb).addClass("active").css("left",i+"px")}}},{key:"_handleRangeInputMousemoveTouchmove",value:function(){if(this._mousedown){t(this.thumb).hasClass("active")||this._showRangeBubble();var e=this._calcRangeOffset();t(this.thumb).addClass("active").css("left",e+"px"),t(this.value).html(this.$el.val())}}},{key:"_handleRangeMouseupTouchend",value:function(){this._mousedown=!1,this.$el.removeClass("active")}},{key:"_handleRangeBlurMouseoutTouchleave",value:function(){if(!this._mousedown){var i=7+parseInt(this.$el.css("padding-left"))+"px";t(this.thumb).hasClass("active")&&(e.remove(this.thumb),e({targets:this.thumb,height:0,width:0,top:10,easing:"easeOutQuad",marginLeft:i,duration:100})),t(this.thumb).removeClass("active")}}},{key:"_setupThumb",value:function(){this.thumb=document.createElement("span"),this.value=document.createElement("span"),t(this.thumb).addClass("thumb"),t(this.value).addClass("value"),t(this.thumb).append(this.value),this.$el.after(this.thumb)}},{key:"_removeThumb",value:function(){t(this.thumb).remove()}},{key:"_showRangeBubble",value:function(){var i=-7+parseInt(t(this.thumb).parent().css("padding-left"))+"px";e.remove(this.thumb),e({targets:this.thumb,height:30,width:30,top:-30,marginLeft:i,duration:300,easing:"easeOutQuint"})}},{key:"_calcRangeOffset",value:function(){var t=this.$el.width()-15,e=parseFloat(this.$el.attr("max"))||100,i=parseFloat(this.$el.attr("min"))||0;return(parseFloat(this.$el.val())-i)/(e-i)*t}}],[{key:"init",value:function(t,e){return _get(s.__proto__||Object.getPrototypeOf(s),"init",this).call(this,this,t,e)}},{key:"getInstance",value:function(t){return(t.jquery?t[0]:t).M_Range}},{key:"defaults",get:function(){return i}}]),s}(Component);M.Range=n,M.jQueryLoaded&&M.initializeJqueryWrapper(n,"range","M_Range"),n.init(t("input[type=range]"))}(cash,M.anime);