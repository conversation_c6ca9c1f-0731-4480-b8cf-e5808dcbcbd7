//Category
Vue.component('category-header', {
    template: `
    <thead>
        <tr>
            <th>Category</th>
            <th></th>
        </tr>
    </thead>`
});
Vue.component('category-row', {
    props: ['cat'],
    template: `<tr>
        <td>{{cat.text}}</td>
        <td><a v-on:click="$emit('launch-edit-category', cat)" class="modal-close waves-effect waves-green btn-small">Edit</a></td>
    </tr>`
});
Vue.component('category-add-modal', {
    props: ['cat'],
    template: `<div id="category-adder" class="modal modal-fixed-footer">
        <div class= "modal-content">
            <h5>Adding Category</h5>
            <br/>
            <form class="col s6" @submit.prevent="addCategory">
                <div class="row">
                    <div class="input-field col s6">
                        <input v-model="cat.text" placeholder="" id="add_category_name" type="text" class="validate">
                        <label for="add_category_name">Category Name</label>
                    </div>
                    <div class="input-field col s6">
                    </div>
                </div>
            </form>
        </div>
        <div class="modal-footer">
            <button href="#!" class="modal-close waves-effect waves-orange btn-small">Cancel</button>
            <button v-on:click="$emit('add-category', cat)" class="modal-close waves-effect waves-green btn-small">Create</button>
        </div>
    </div>`
});
Vue.component('category-edit-modal', {
    props: ['cat'],
    template: `<div id="category-editor" class="modal modal-fixed-footer">
        <div class= "modal-content">
            <h5>Editing Category</h5>
            <br/>
            <form class="col s6" @submit.prevent="editCategory">
                <div class="row">
                    <div class="input-field col s6">
                        <input v-model="cat.text" placeholder="" id="edit_category_name" type="text" class="validate">
                        <label for="edit_category_name">Category Name</label>
                    </div>
                    <div class="input-field col s6">
                    </div>
                </div>
            </form>
        </div>
        <div class="modal-footer">
            <button v-on:click="$emit('delete-category', cat)" class="modal-close waves-effect waves-red btn-small" disabled>DELETE</button>
            <button href="#!" class="modal-close waves-effect waves-orange btn-small">Cancel</button>
            <button v-on:click="$emit('edit-category', cat)" class="modal-close waves-effect waves-green btn-small">Save</button>
        </div>
    </div>`
});
// Sub Category
Vue.component('subcategory-header', {
    template: `<thead>
        <tr>
            <th>Category Name</th>
            <th>Subcategory Id</th>
            <th>Subcategory Name</th>
            <th>Product?</th>
            <th>Service?</th>
            <th>Public?</th>
            <th>Images</th>
            <th></th>
        </tr>
    </thead>`
});
Vue.component('subcategory-row', {
    props: ['subcat'],
    template: `<tr>
        <td>{{subcat.categoryName}}</td>
        <td>{{subcat.subCategoryId}}</td>
        <td>{{subcat.subCategoryName}}</td>
        <td>{{subcat.isProduct}}</td>
        <td>{{subcat.isService}}</td>
        <td>{{subcat.isPublicPlace}}</td>
        <td>{{subcat.imgCounter}}</td>
        <td><a v-on:click="$emit('launch-edit-subcategory', subcat)" class="modal-close waves-effect waves-green btn-small">Edit</a></td>
    </tr>`
});
Vue.component('category-selector', {
    props: ['subcat'],
    template: `<div>
        <label for="category_select">Category</label>
        <select class="browser-default" id="category_select" v-model="subcat.categoryId">
            <option v-for="cat in subcat.categories" v-bind:value="cat.id">{{ cat.text }}</option>
        </select>
        </div>`
});
Vue.component('image-uploader', {
    props: ['subcategory'],
    methods: {
        submitFile() {
            let formData = new FormData();
            formData.append('image', this.file);
            formData.append('subcategory', this.subcategory.subCategoryId);
            Axios.post('/LinqAdmin/UploadImage',
                formData).then(function () {
                    alert("Image Uploaded. It will appear in a few minutes.");
            })
                .catch(function (error) {
                    alert("Failed to Upload Image");
                console.log(error);
            });
        },
        handleFileUpload() {
            this.file = this.$refs.file.files[0];
        }
    },
    template: `<form @submit.prevent="submitFile()">
        <label> 
        <input type="file" id="file" ref="file" accept=".jpg, image/jpg, image/jpeg" v-on:change="handleFileUpload()"/>
        </label>
        <button class="waves-effect waves-orange btn-small" type="submit">Submit</button>
        </form>`
});
Vue.component('image-viewer', {
    props: ['subcat'],
    template: `<div>
            <span v-if="subCategoryImagesError">Unable to fetch Images. Try Again.</span>
            <button v-on:click="fetchImages(subcat.subCategoryId)" class="waves-effect waves-orange btn-small">Fetch Images</button><br/><br/>
            <div class="subcategory-image" v-for="image in subCategoryImages" >
		        <img :src="'https://s3.ap-south-1.amazonaws.com/i2e1-linq/subcategories/xxxhdpi/'+subcat.subCategoryId+'/'+image.thumbnail">
		        <div class="options" >
                    <a href="#" class="linq-orange" v-on:click="deleteImage(subcat.subCategoryId, image.image)">&#128465; Delete</a>
                    <a href="#" class="linq-orange" v-on:click="open('https://s3.ap-south-1.amazonaws.com/i2e1-linq/subcategories/xxxhdpi/'+subcat.subCategoryId+'/'+image.image)"><i class="fa fa-trash fa-lg"></i>&#x2195;&#x2194; View</a>
                </div>
	        </div>
        </div>`,
    data: function () {
        return {
            subCategoryImagesError: false,
            subCategoryImages: []
        }
    },
    methods: {
        fetchImages: function (subCategoryId) {
            _self = this;
            Axios.get('/LinqAdmin/GetSubcategoryImages?subCategoryId=' + subCategoryId)
            .then(function (response) {
                _self.subCategoryImages = response.data.data;
            })
            .catch(function (error) {
                _self.subCategoryImagesError = true;
            });
        },
        deleteImage: function (subCategoryId, image) {
            if (confirm("Delete Image?")) {
                console.log("Deleting " + image + "From " + subCategoryId);
                this.fetchImages(subCategoryId);
            } else {
                console.log("Okay fine, not deleting.");
            }
        },
        open: function (url) {
            window.open(url, "_blank");
        }
    }
});
Vue.component('subcategory-add-modal', {
    props: ['subcat'],
    template: `<div id="subcategory-adder" class="modal modal-fixed-footer">
        <div class= "modal-content">
            <h5>Adding Subcategory</h5>
            <br/>
            <div class="col s6">
                <form class="col s12" @submit.prevent="addSubCategory">
                    <div class="row">
                        <div class="input-field col s12">
                            <input v-model="subcat.subCategoryName" placeholder="" id="add_subcategory_name" type="text" class="validate">
                            <label for="add_subcategory_name">Subcategory Name</label>
                        </div>
                    </div>
                    <div class="row">
                        <div class="input-field col s12">
                            <category-selector v-bind:subcat="subcat" ></category-selector>
                        </div>
                    </div>
                    <div class="row">
                        <div class="input-field col s4">
                            <label>
                                <input type="checkbox" id="is_product" v-model="subcat.isProduct" />
                                <span>Product</span>
                            </label>
                        </div>
                        <div class="input-field col s4">
                            <label>
                                <input type="checkbox" id="is_service" v-model="subcat.isService" />
                                <span>Service</span>
                            </label>
                        </div>
                        <div class="input-field col s4">
                            <label>
                                <input type="checkbox" id="is_public" v-model="subcat.isPublicPlace" />
                                <span>Public</span>
                            </label>
                        </div>
                    </div>
                </form>
            </div>
        </div>
        <div class="modal-footer">
            <button href="#!" class="modal-close waves-effect waves-orange btn-small">Cancel</button>
            <button v-on:click="$emit('add-subcategory', subcat)" class="modal-close waves-effect waves-green btn-small">Create</button>
        </div>
    </div>`
});
Vue.component('tags-viewer', {
    props: ['tagged', 'name'],
    template: `<div>
            <div class="chip">
                {{name}}
                <i class="delete material-icons" v-on:click="$emit('viewer-delete-tag', tagged)">delete</i>
                <i class="edit material-icons" v-on:click="$emit('viewer-edit-tag', tagged)">edit</i>
            </div>
        </div>`
});
Vue.component('subcategory-edit-modal', {
    props: ['subcat'],
    template: `<div v-if="typeof(subcat) != 'undefined'" id="subcategory-editor" class="modal modal-fixed-footer">
        <div class= "modal-content">
            <h5>Editing Subcategory</h5>
            <br/>
            <div class="row">
                <div class="col s6">
                    <form class="col s12" @submit.prevent="editSubCategory">
                        <div class="row">
                            <div class="input-field col s12">
                                <input v-model="subcat.subCategoryName" placeholder="" id="edit_subcategory_name" type="text" class="validate">
                                <label for="edit_subcategory_name">Subcategory Name</label>
                            </div>
                        </div>
                        <div class="row">
                            <div class="input-field col s12">
                                <category-selector :key="subcat.subCategoryId" v-bind:subcat="subcat" ></category-selector>
                            </div>
                        </div>
                        <div class="row">
                            <div class="input-field col s4">
                                <label>
                                    <input type="checkbox" id="is_product" v-model="subcat.isProduct" />
                                    <span>Product</span>
                                </label>
                            </div>
                            <div class="input-field col s4">
                                <label>
                                    <input type="checkbox" id="is_service" v-model="subcat.isService" />
                                    <span>Service</span>
                                </label>
                            </div>
                            <div class="input-field col s4">
                                <label>
                                    <input type="checkbox" id="is_public" v-model="subcat.isPublicPlace" />
                                    <span>Public</span>
                                </label>
                            </div>
                        </div>
                    </form>
                    <div class="row">
                        <div class="col s12">
                            <br /><br />
                            <tags-viewer 
                                    v-for="(tagged, index) in subcat.tags"
                                    v-bind:tagged="tagged"
                                    v-bind:name="tagged.tagName"
                                    v-on:update:name="tagged.tagName = $event"
                                    :key="tagged.subCategoryId+tagged.tagName" 
                                    v-on:viewer-delete-tag="viewerDeleteTag(tagged, index)"
                                    v-on:viewer-edit-tag="viewerEditTag(tagged, index)"
                                ></tags-viewer>
                        </div>
                    </div>
                </div>
                <div class="col s6">                  
                    <h6>Upload Image</h6>
                    <image-uploader v-bind:subcategory="subcat" ></image-uploader>
                    <h6>Images({{subcat.imgCounter}})</h6>
                    <image-viewer :key="subcat.subCategoryId" v-bind:subcat="subcat" ></image-viewer>
                </div>
            </div>

        </div>
        <div class="modal-footer">
            <button href="#!" class="modal-close waves-effect waves-red btn-small" disabled>DELETE</button>
            <button href="#!" class="modal-close waves-effect waves-orange btn-small">Cancel</button>
            <button v-on:click="$emit('edit-subcategory', subcat)" class="modal-close waves-effect waves-green btn-small">Save</button>
        </div>
    </div>`,
    methods: {
        viewerDeleteTag: function (tag, index) {
            var _self = this;
            if (confirm("Delete " + tag.tagName + "?")) {
                Axios.post('/LinqAdmin/DeleteTag', {
                        subCategoryId: tag.subCategoryId,
                        tagName: tag.tagName
                    })
                    .then(function (response) {
                        _self.$emit('update:tags', index);
                    })
                    .catch(function (error) {
                        alert("Cannot Delete.");
                    });
            }
        },
        viewerEditTag: function (tag, index) {
            var data = {
                oldId: tag.subCategoryId,
                oldName: tag.tagName,
                subCategoryId: tag.subCategoryId,
                tagName: tag.tagName
            };
            data.tagName = prompt("Rename Tag", data.oldName);
            if (data.tagName && data.tagName !== data.oldName && data.tagName.length > 3) {
                var _self = this;
                Axios.post('/LinqAdmin/EditTag', data)
                    .then(function (response) {
                        if (response.data.data) {
                            _self.$emit('update:name', data.tagName);
                        } else {
                            alert("Cannot Modify. Tag combo already exists.");
                        }
                    })
                    .catch(function (error) {
                    });
            } else {
                if (data.tagName !== data.oldName && data.tagName.length > 3)
                    alert("Error: Invalid Modification.");
            }
        }
    }
});
// Tags
Vue.component('subcategory-selector', {
    props: ['tagged'],
    template: `<div>
        <label for="subcategory_select">Sub Category</label>
        <select class="browser-default" id="subcategory_select" v-model="tagged.subCategoryId">
            <option v-for="subcat in tagged.subCategories" v-bind:value="subcat.subCategoryId">{{ subcat.subCategoryName }}</option>
        </select>
        </div>`
});
Vue.component('tags-header', {
    template: `<thead>
        <tr>
            <th>Sub Category</th>
            <th>Tag</th>
            <th></th>
        </tr>
    </thead>`
});
Vue.component('tags-row', {
    props: ['tagged'],
    template: `<tr>
        <td>{{tagged.subCategoryName}}</td>
        <td>
              <div class="chip" ref="tagged.subCategoryName+tagged.tagName">
                {{tagged.tagName}}
                <i class="delete material-icons" v-on:click="$emit('inline-delete-tag', tagged)">delete</i>
                <i class="edit material-icons" v-on:click="$emit('launch-edit-tag', tagged)">edit</i>
              </div>
        </td>
    </tr>`
});
Vue.component('tag-add-modal', {
    props: ['tagged'],
    template: `<div id="tag-adder" class="modal modal-fixed-footer">
        <div class= "modal-content">
            <h5>Adding Tag</h5>
            <br/>
            <form class="col s12" @submit.prevent="addTag">
                <div class="row">
                    <div class="input-field col s6">
                        <subcategory-selector v-bind:tagged="tagged" ></subcategory-selector>
                    </div>
                    <div class="input-field col s6">
                    </div>
                </div>
                <div class="row">
                    <div class="input-field col s6">
                        <input v-model="tagged.tagName" placeholder="" id="add_tag_name" type="text" class="validate">
                        <label for="add_tag_name">Tag Name</label>
                    </div>
                    <div class="input-field col s6">
                    </div>
                </div>
            </form>
        </div>
        <div class="modal-footer">
            <button href="#!" class="modal-close waves-effect waves-orange btn-small">Cancel</button>
            <button v-on:click="$emit('add-tag', tagged)" class="waves-effect waves-green btn-small">Create</button>
        </div>
    </div>`
});
Vue.component('tag-edit-modal', {
    props: ['tagged'],
    template: `<div id="tag-editor" class="modal modal-fixed-footer">
        <div class= "modal-content">
            <h5>Editing Tag</h5>
            <br/>
            <form class="col s12" @submit.prevent="editTag">
                <div class="row">
                    <div class="input-field col s6">
                        <subcategory-selector v-bind:tagged="tagged" ></subcategory-selector>
                    </div>
                    <div class="input-field col s6">
                    </div>
                </div>
                <div class="row">
                    <div class="input-field col s6">
                        <input v-model="tagged.tagName" placeholder="" id="edit_tag_name" type="text" class="validate">
                        <label for="edit_tag_name">Tag Name</label>
                    </div>
                    <div class="input-field col s6">
                    </div>
                </div>
            </form>
        </div>
        <div class="modal-footer">
            <button v-on:click="$emit('delete-tag', tagged)" class="modal-close waves-effect waves-red btn-small">DELETE</button>
            <button href="#!" class="modal-close waves-effect waves-orange btn-small">Cancel</button>
            <button v-on:click="$emit('edit-tag', tagged)" class="waves-effect waves-green btn-small">Save</button>
        </div>
    </div>`
});
Vue.component('contact-header', {
    template: `<thead>
        <tr>
            <th>FirstName</th>
            <th>LastName</th>
            <th>Mobile</th>
            <th></th>
        </tr>
    </thead>`
});
Vue.component('contact-row', {
    props: ['contact','index'],
    template: `<tr>
        <td><input type="text" v-model="contact.firstName" placeholder="First Name" /></td>
        <td><input type="text" v-model="contact.lastName" placeholder="Last Name" /></td>
        <td><input type="text" v-model="contact.mobile" placeholder="Mobile" /></td>
        <td>
            <a class="delete material-icons" v-on:click="$emit('delete-contact', index)">delete</a>
        </td>
    </tr>`
});
const Axios = axios.create();
Axios.interceptors.response.use(function (response) {
    return response;
}, function (error) {
    if (error.response && error.response.status && error.response.status === 403)
        window.location("/LinqAdmin/Login");
    else
        return Promise.reject(error);
});
var app = new Vue({
    el: '#linq-admin',
    data: {
        categories: [],
        subCategories: [],
        tags: [],
        selectedCategory: {},
        selectedSubCategory: {},
        selectedTag: {},
        newCategory: {
        },
        newSubCategory: {
            subCategory: '',
            categoryId: 1,
            categoryName: '',
            isProduct: false,
            isService: false,
            isPublicPlace: false,
            categories: []
        },
        newTag: {
            subCategoryId: 1,
            subCategories: []
        },
        contacts: {
            salesContacts: []
        },
        listings: [],
        currentListings: [],
        userInfo: {},
        linqAdminInfo: {
            firstName: 'Admin',
            lastName: 'Linq',
            picture: ''
        }
    },
    created: function () {
        $('#login').hide();
        $('#linq-admin').show();
        this.getCategories();
        this.getSubCategories();
        this.getTags();
        this.getContacts();
        this.getLinqAdminInfo();
    },
    mounted: function () {
        $('#category-editor').modal();
        $('#subcategory-editor').modal();
        $('#tag-editor').modal();
        $('#category-adder').modal();
        $('#subcategory-adder').modal();
        $('#tag-adder').modal();
    },
    methods: {
        getLinqAdminInfo: function () {
            var _self = this;
            axios.get('/LinqAdmin/GetLinqAdminInfo')
                .then(function (response) {
                    _self.linqAdminInfo = response.data.data;
                })
                .catch(function (error) {
                    _self.linqAdminInfo = {
                        firstName: '',
                        lastName: '',
                        picture: ''
                    };
                });
        },
        logout: function () {
            Axios.get('/LinqAdmin/Logout').then(function () {
                window.location.href = '/LinqAdmin/Login';
            });
        },
        getCategories: function () {
            var _self = this;
            axios.get('/LinqAdmin/GetCategories')
                .then(function (response) {
                    _self.categories = response.data.data;
                })
                .catch(function (error) {
                    _self.categories = [];
                });
        },
        showAddCategoryModal: function () {
            $('#category-adder').modal('open');
        },
        showEditCategoryModal: function (category) {
            this.selectedCategory = category;
            $('#category-editor').modal('open');
            console.log(category.categoryName);
        },
        addCategory: function (category) {
            var _self = this;
            Axios.post('/LinqAdmin/AddCategory', category)
                .then(function (response) {
                    _self.getCategories();
                })
                .catch(function (error) {
                    _self.getCategories();
                });
        },
        editCategory: function (category) {
            var _self = this;
            Axios.post('/LinqAdmin/EditCategory', category)
                .then(function (response) {
                    _self.getCategories();
                })
                .catch(function (error) {
                    _self.getCategories();
                });
        },
        deleteCategory: function (category) {
            var _self = this;
            Axios.post('/LinqAdmin/DeleteCategory', category)
                .then(function (response) {
                    _self.getCategories();
                })
                .catch(function (error) {
                    _self.getCategories();
                });
        },
        getSubCategories: function () {
            var _self = this;
            Axios.get('/LinqAdmin/GetSubCategories')
                .then(function (response) {
                    _self.subCategories = response.data.data;
                })
                .catch(function (error) {
                    _self.subCategories = [];
                });
        },
        showAddSubCategoryModal: function () {
            this.newSubCategory.categories = this.categories;
            $('#subcategory-adder').modal('open');
        },
        addSubCategory: function (subCategory) {
            var _self = this;
            Axios.post('/LinqAdmin/AddSubCategory', {
                name: subCategory.subCategoryName,
                categoryName: subCategory.categoryName,
                categoryId: subCategory.categoryId,
                isProduct: subCategory.isProduct,
                isService: subCategory.isService,
                isPublicPlace: subCategory.isPublicPlace
            })
                .then(function (response) {
                    _self.getSubCategories();
                })
                .catch(function (error) {
                    
                });
        },
        getSubCategoryImages: function (subCategoryId, fetched) {
            var _self = this;
            Axios.get('/LinqAdmin/GetSubcategoryImages?subCategoryId=' + subCategoryId)
            .then(function (response) {
                _self.subCategoryImages = response.data.data;
                return fetched(response.data.data, null);
            })
            .catch(function (error) {
                return fetched({}, error);
            });
        },
        showEditSubCategoryModal: function (subCategory) {
            this.selectedSubCategory = subCategory;
            this.selectedSubCategory.categories = this.categories;
            this.selectedSubCategory.tags = this.tags.filter(tag => tag.subCategoryId === subCategory.subCategoryId);
            this.subCategoryImages = [];
            $('#subcategory-editor').modal('open');
        },
        editSubCategory: function (subCategory) {
            var _self = this;
            Axios.post('/LinqAdmin/EditSubCategory', {
                categoryId: subCategory.categoryId,
                subCategoryName: subCategory.subCategoryName,
                categoryName: subCategory.categoryName,
                subCategoryId: subCategory.subCategoryId,
                isProduct: subCategory.isProduct,
                isService: subCategory.isService,
                isPublicPlace: subCategory.isPublicPlace
            })
            .then(function (response) {
                _self.getSubCategories();
            })
            .catch(function (error) {
                _self.getSubCategories();
            });
        },
        getTags: function () {
            var _self = this;
            Axios.get('/LinqAdmin/GetTags')
            .then(function (response) {
                _self.tags = response.data.data;
            })
            .catch(function (error) {
                _self.tags = [];
            });
        },
        showAddTagModal: function () {
            this.newTag.subCategories = this.subCategories;
            $('#tag-adder').modal('open');
        },
        showEditTagModal: function (tag) {
            this.selectedTag = tag;
            this.selectedTag.oldId = tag.subCategoryId;
            this.selectedTag.oldName = tag.tagName;
            this.selectedTag.subCategories = this.subCategories;
            $('#tag-editor').modal('open');
            console.log(tag.tagName);
        },
        addTag: function (tag) {
            var _self = this;
            Axios.post('/LinqAdmin/AddTag', {
                    subCategoryId: tag.subCategoryId,
                    tagName: tag.tagName
                })
                .then(function (response) {
                    _self.getTags();
                    if (response.data.data) {
                        $('#tag-adder').modal('close');
                    } else {
                        alert("Cannot Add. Tag combo already exists.");
                    }
                })
                .catch(function (error) {
                    $('#tag-adder').modal('close');
                });
        },
        editTag: function (tag) {
            var _self = this;
            Axios.post('/LinqAdmin/EditTag', {
                    oldId: tag.oldId,
                    oldName: tag.oldName,
                    subCategoryId: tag.subCategoryId,
                    tagName: tag.tagName
                })
                .then(function (response) {
                    _self.getTags();
                    if (response.data.data) {
                        $('#tag-editor').modal('close');
                    } else {
                        alert("Cannot Modify. Tag combo already exists.");
                    }
                })
                .catch(function (error) {
                    $('#tag-editor').modal('close');
                });
        },
        deleteTag: function (tag) {
            var _self = this;
            if (confirm("Delete " + tag.tagName + "?")) {
                Axios.post('/LinqAdmin/DeleteTag', {
                    subCategoryId: tag.subCategoryId,
                    tagName: tag.tagName
                })
                    .then(function (response) {
                        _self.getTags();
                    })
                    .catch(function (error) {
                    });
            } else {
                _self.getTags();
            }
        },
        getContacts: function () {
            var _self = this;
            Axios.get('/LinqAdmin/GetLinqContacts')
                .then(function (response) {
                    _self.contacts = response.data.data;
                })
                .catch(function (error) {
                    _self.contacts = [];
                });
        },
        newContact: function (contactType) {
            var _self = this;
            _self.contacts[contactType].push({firstName:'',lastName:'',mobile:''});
        },
        deleteContact: function (contactType, index) {
            var _self = this;
            _self.contacts[contactType].splice(index, 1);
        },
        saveContacts: function (contacts) {
            var _self = this;
            Axios.post('/LinqAdmin/SaveLinqContacts', contacts)
                .then(function (response) {
                    _self.contacts = response.data.data;
                })
                .catch(function (error) {
                    
                });
        }
    }
});
