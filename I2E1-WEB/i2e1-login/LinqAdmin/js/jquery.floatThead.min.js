/** @preserve jQuery.floatThead 2.1.2 - https://mkoryak.github.io/floatThead/ - Copyright (c) 2012 - 2018 <PERSON><PERSON> **/
!function(ht){ht.floatThead=ht.floatThead||{},ht.floatThead.defaults={headerCellSelector:"tr:visible:first>*:visible",zIndex:1001,position:"auto",top:0,bottom:0,scrollContainer:function(t){return ht([])},responsiveContainer:function(t){return ht([])},getSizingRow:function(t,e,o){return t.find("tbody tr:visible:first>*:visible")},floatTableClass:"floatThead-table",floatWrapperClass:"floatThead-wrapper",floatContainerClass:"floatThead-container",copyTableClass:!0,autoReflow:!1,debug:!1,support:{bootstrap:!0,datatables:!0,jqueryUI:!0,perfectScrollbar:!0},floatContainerCss:{"overflow-x":"hidden"}};var vt=function(){var n={},o=Object.prototype.hasOwnProperty;n.has=function(t,e){return o.call(t,e)},n.keys=Object.keys||function(t){if(t!==Object(t))throw new TypeError("Invalid object");var e=[];for(var o in t)n.has(t,o)&&e.push(o);return e};var r=0;return n.uniqueId=function(t){var e=++r+"";return t?t+e:e},ht.each(["Arguments","Function","String","Number","Date","RegExp"],function(){var e=this;n["is"+e]=function(t){return Object.prototype.toString.call(t)=="[object "+e+"]"}}),n.debounce=function(o,n,r){var a,i,l,s,d;return function(){l=this,i=arguments,s=new Date;var e=function(){var t=new Date-s;t<n?a=setTimeout(e,n-t):(a=null,r||(d=o.apply(l,i)))},t=r&&!a;return a||(a=setTimeout(e,n)),t&&(d=o.apply(l,i)),d}},n}(),bt="undefined"!=typeof MutationObserver,wt=function(){for(var t=3,e=document.createElement("b"),o=e.all||[];t=1+t,e.innerHTML="\x3c!--[if gt IE "+t+"]><i><![endif]--\x3e",o[0];);return 4<t?t:document.documentMode}(),t=/Gecko\//.test(navigator.userAgent),gt=/WebKit\//.test(navigator.userAgent);wt||t||gt||(wt=11);var l=function(){if(gt){var t=ht("<div>").css("width",0).append(ht("<table>").css("max-width","100%").append(ht("<tr>").append(ht("<th>").append(ht("<div>").css("min-width",100).text("X")))));ht("body").append(t);var e=0==t.find("table").width();return t.remove(),e}return!1},mt=!t&&!wt,yt=ht(window),Tt=t&&window.matchMedia;if(!window.matchMedia||Tt){var e=window.onbeforeprint,o=window.onafterprint;window.onbeforeprint=function(){e&&e(),yt.triggerHandler("fth-beforeprint")},window.onafterprint=function(){o&&o(),yt.triggerHandler("fth-afterprint")}}function Ct(t){var e=t[0].parentElement;do{if("visible"!=window.getComputedStyle(e).getPropertyValue("overflow"))break}while(e=e.parentElement);return e==document.body?ht([]):ht(e)}function xt(t){window&&window.console&&window.console.error&&window.console.error("jQuery.floatThead: "+t)}function jt(t){var e=t.getBoundingClientRect();return e.width||e.right-e.left}function St(){var t=document.createElement("scrolltester");t.style.cssText="width:100px;height:100px;overflow:scroll!important;position:absolute;top:-9999px;display:block",document.body.appendChild(t);var e=t.offsetWidth-t.clientWidth;return document.body.removeChild(t),e}function zt(t,e,o){var n=o?"outerWidth":"width";if(l&&t.css("max-width")){var r=0;o&&(r+=parseInt(t.css("borderLeft"),10),r+=parseInt(t.css("borderRight"),10));for(var a=0;a<e.length;a++)r+=jt(e.get(a));return r}return t[n]()}ht.fn.floatThead=function(t){if(t=t||{},wt<8)return this;var ut=null;if(vt.isFunction(l)&&(l=l()),vt.isString(t)){var r=t,a=Array.prototype.slice.call(arguments,1),i=this;return this.filter("table").each(function(){var t=ht(this),e=t.data("floatThead-lazy");e&&t.floatThead(e);var o=t.data("floatThead-attached");if(o&&vt.isFunction(o[r])){var n=o[r].apply(this,a);void 0!==n&&(i=n)}}),i}var pt=ht.extend({},ht.floatThead.defaults||{},t);if(ht.each(t,function(t,e){t in ht.floatThead.defaults||!pt.debug||xt("Used ["+t+"] key to init plugin, but that param is not an option for the plugin. Valid options are: "+vt.keys(ht.floatThead.defaults).join(", "))}),pt.debug){var e=ht.fn.jquery.split(".");1==parseInt(e[0],10)&&parseInt(e[1],10)<=7&&xt("jQuery version "+ht.fn.jquery+" detected! This plugin supports 1.8 or better, or 1.7.x with jQuery UI 1.8.24 -> http://jqueryui.com/resources/download/jquery-ui-1.8.24.zip")}return this.filter(":not(."+pt.floatTableClass+")").each(function(){var e=vt.uniqueId(),m=ht(this);if(m.data("floatThead-attached"))return!0;if(!m.is("table"))throw new Error('jQuery.floatThead must be run on a table element. ex: $("table").floatThead();');bt=pt.autoReflow&&bt;var d=m.children("thead:first"),o=m.children("tbody:first");if(0==d.length||0==o.length)return pt.debug&&(0==d.length?xt("The thead element is missing."):xt("The tbody element is missing.")),m.data("floatThead-lazy",pt),void m.unbind("reflow").one("reflow",function(){m.floatThead(pt)});m.data("floatThead-lazy")&&m.unbind("reflow"),m.data("floatThead-lazy",!1);var y,T,n=!0,C={vertical:0,horizontal:0};vt.isFunction(St)&&(St=St());var f=0;!0===pt.scrollContainer&&(pt.scrollContainer=Ct);var x=pt.scrollContainer(m)||ht([]),j=0<x.length,S=j?ht([]):pt.responsiveContainer(m)||ht([]),z=$(),I=null;"auto"===pt.position?I=null:"fixed"===pt.position?I=!1:"absolute"===pt.position?I=!0:pt.debug&&xt('Invalid value given to "position" option, valid is "fixed", "absolute" and "auto". You passed: ',pt.position),null==I&&(I=j);var r=m.find("caption"),L=1==r.length;if(L)var H="top"===(r.css("caption-side")||r.attr("align")||"top");var a=ht("<fthfoot>").css({display:"table-footer-group","border-spacing":0,height:0,"border-collapse":"collapse",visibility:"hidden"}),W=!1,i=ht([]),q=wt<=9&&!j&&I,c=ht("<table/>"),u=ht("<colgroup/>"),p=m.children("colgroup:first"),h=!0;0==p.length&&(p=ht("<colgroup/>"),h=!1);var v=ht("<fthtr>").css({display:"table-row","border-spacing":0,height:0,"border-collapse":"collapse"}),R=ht("<div>").css(pt.floatContainerCss).attr("aria-hidden","true"),M=!1,l=ht("<thead/>"),b=ht('<tr class="size-row" aria-hidden="true"/>'),w=ht([]),g=ht([]),E=ht([]),k=ht([]);l.append(b),m.prepend(p),mt&&(a.append(v),m.append(a)),c.append(u),R.append(c),pt.copyTableClass&&c.attr("class",m.attr("class")),c.attr({cellpadding:m.attr("cellpadding"),cellspacing:m.attr("cellspacing"),border:m.attr("border")});var t=m.css("display");if(c.css({borderCollapse:m.css("borderCollapse"),border:m.css("border"),display:t}),j||c.css("width","auto"),"none"===t&&(M=!0),c.addClass(pt.floatTableClass).css({margin:0,"border-bottom-width":0}),I){var s=function(t,e){var o=t.css("position"),n=t;if(!("relative"==o||"absolute"==o)||e){var r={paddingLeft:t.css("paddingLeft"),paddingRight:t.css("paddingRight")};R.css(r),n=t.data("floatThead-containerWrap")||t.wrap(ht("<div>").addClass(pt.floatWrapperClass).css({position:"relative",clear:"both"})).parent(),t.data("floatThead-containerWrap",n),W=!0}return n};j?(i=s(x,!0)).prepend(R):(i=s(m),m.before(R))}else m.before(R);R.css({position:I?"absolute":"fixed",marginTop:0,top:I?0:"auto",zIndex:pt.zIndex,willChange:"transform"}),R.addClass(pt.floatContainerClass),U();var D={"table-layout":"fixed"},F={"table-layout":m.css("tableLayout")||"auto"},O=m[0].style.width||"",N=m.css("minWidth")||"";function A(t){return t+".fth-"+e+".floatTHead"}function Q(){var t=0;if(d.children("tr:visible").each(function(){t+=ht(this).outerHeight(!0)}),"collapse"==m.css("border-collapse")){var e=parseInt(m.css("border-top-width"),10);parseInt(m.find("thead tr:first").find(">*:first").css("border-top-width"),10)<e&&(t-=e/2)}b.outerHeight(t),w.outerHeight(t)}function U(){y=(vt.isFunction(pt.top)?pt.top(m):pt.top)||0,T=(vt.isFunction(pt.bottom)?pt.bottom(m):pt.bottom)||0}function G(){if(!n){if(n=!0,I){var t=zt(m,k,!0);i.width()<t&&m.css("minWidth",t)}m.css(D),c.css(D),c.append(d),o.before(l),Q()}}function P(){n&&(n=!1,I&&m.width(O),l.detach(),m.prepend(d),m.css(F),c.css(F),m.css("minWidth",N),m.css("minWidth",zt(m,k)))}var V=!1;function X(t){V!=t&&(V=t,m.triggerHandler("floatThead",[t,R]))}function Y(t){I!=t&&(I=t,R.css({position:I?"absolute":"fixed"}))}function B(){var l,s=function(){var t,e=d.find(pt.headerCellSelector);if(h?t=p.find("col").length:(t=0,e.each(function(){t+=parseInt(ht(this).attr("colspan")||1,10)})),t!==f){f=t;for(var o,n=[],r=[],a=[],i=0;i<t;i++)o=e.eq(i).text(),n.push('<th class="floatThead-col" aria-label="'+o+'"/>'),r.push("<col/>"),a.push(ht("<fthtd>").css({display:"table-cell",height:0,width:"auto"}));r=r.join(""),n=n.join(""),mt&&(v.empty(),v.append(a),k=v.find("fthtd")),b.html(n),w=b.find("th"),h||p.html(r),g=p.find("col"),u.html(r),E=u.find("col")}return t}();return function(){var t=R.scrollLeft();g=p.find("col");var e,o,n,r,a=(e=m,o=g,n=k,r=wt,mt?n:r?pt.getSizingRow(e,o,n):o);if(a.length==s&&0<s){if(!h)for(l=0;l<s;l++)g.eq(l).css("width","");P();var i=[];for(l=0;l<s;l++)i[l]=jt(a.get(l));for(l=0;l<s;l++)E.eq(l).width(i[l]),g.eq(l).width(i[l]);G()}else c.append(d),m.css(F),c.css(F),Q();R.scrollLeft(t),m.triggerHandler("reflowed",[R])}}function K(t){var e=x.css("border-"+t+"-width"),o=0;return e&&~e.indexOf("px")&&(o=parseInt(e,10)),o}function $(){return"auto"==S.css("overflow-x")}function J(){var i,l=x.scrollTop(),s=0,d=L?r.outerHeight(!0):0,f=H?d:-d,c=R.height(),u=m.offset(),p=0,h=0;if(j){var t=x.offset();s=u.top-t.top+l,L&&H&&(s+=d),p=K("left"),h=K("top"),s-=h}else i=u.top-y-c+T+C.horizontal;var v=yt.scrollTop(),b=yt.scrollLeft(),w=function(){return($()?S:x).scrollLeft()||0},g=w();return function(t){z=$();var e=m[0].offsetWidth<=0&&m[0].offsetHeight<=0;if(!e&&M)return M=!1,setTimeout(function(){m.triggerHandler("reflow")},1),null;if(e&&(M=!0,!I))return null;if("windowScroll"==t)v=yt.scrollTop(),b=yt.scrollLeft();else if("containerScroll"==t)if(S.length){if(!z)return;g=S.scrollLeft()}else l=x.scrollTop(),g=x.scrollLeft();else"init"!=t&&(v=yt.scrollTop(),b=yt.scrollLeft(),l=x.scrollTop(),g=w());if(!gt||!(v<0||b<0)){if(q)Y("windowScrollDone"==t);else if("windowScrollDone"==t)return null;var o,n;u=m.offset(),L&&H&&(u.top+=d);var r=m.outerHeight();if(j&&I){if(l<=s){var a=s-l+h;o=0<a?a:0,X(!1)}else r-c<l-s?o=r-c-l-s:(o=W?h:l,X(!0));n=p}else!j&&I?(i+r+f<v?o=r-c+f+T:u.top>=v+y?(o=0,P(),X(!1)):(o=y+v-u.top+s+(H?d:0),G(),X(!0)),n=g):j&&!I?(l<s||r<l-s?(o=u.top-v,P(),X(!1)):(o=u.top+l-v-s,G(),X(!0)),n=u.left+g-b):j||I||(i+r+f<v?o=r+y-v+i+f:u.top>v+y?(o=u.top-v,G(),X(!1)):(o=y,X(!0)),n=u.left+g-b);return{top:Math.round(o),left:Math.round(n)}}}}function Z(){var i=null,l=null,s=null;return function(t,e,o){if(null!=t&&(i!=t.top||l!=t.left)){if(8===wt)R.css({top:t.top,left:t.left});else{var n="translateX("+t.left+"px) translateY("+t.top+"px)",r={"-webkit-transform":n,"-moz-transform":n,"-ms-transform":n,"-o-transform":n,transform:n,top:0};r[/rtl/i.test(document.documentElement.dir||"")?"right":"left"]=0,R.css(r)}i=t.top,l=t.left}e&&function(){var t=zt(m,k,!0),e=z?S:x,o=e.length?jt(e[0]):t,n="hidden"!=e.css("overflow-y")?o-C.vertical:o;if(R.width(n),j){var r=100*t/n;c.css("width",r+"%")}else c.css("width",t+"px")}(),o&&Q();var a=(z?S:x).scrollLeft();I&&s==a||(R.scrollLeft(a),s=a)}}function _(){if(x.length)if(pt.support&&pt.support.perfectScrollbar&&x.data().perfectScrollbar)C={horizontal:0,vertical:0};else{if("scroll"==x.css("overflow-x"))C.horizontal=St;else{var t=x.width(),e=zt(m,k),o=n<r?St:0;C.horizontal=t-o<e?St:0}if("scroll"==x.css("overflow-y"))C.vertical=St;else{var n=x.height(),r=m.height(),a=t<e?St:0;C.vertical=n-a<r?St:0}}}_();var tt=function(){B()()};tt();var et=J(),ot=Z();ot(et("init"),!0);var nt=vt.debounce(function(){ot(et("windowScrollDone"),!1)},1),rt=function(){ot(et("windowScroll"),!1),q&&nt()},at=function(){ot(et("containerScroll"),!1)},it=vt.debounce(function(){m.is(":hidden")||(_(),U(),tt(),et=J(),ot(et("reflow"),!0,!0))},1),lt=function(){P()},st=function(){G()},dt=function(t){t.matches?lt():st()},ft=null;if(window.matchMedia&&window.matchMedia("print").addListener&&!Tt?(ft=window.matchMedia("print")).addListener(dt):(yt.on("fth-beforeprint",lt),yt.on("fth-afterprint",st)),j?I?x.on(A("scroll"),at):(x.on(A("scroll"),at),yt.on(A("scroll"),rt)):(S.on(A("scroll"),at),yt.on(A("scroll"),rt)),yt.on(A("load"),it),function(t,e){if(8==wt){var o=yt.width(),n=vt.debounce(function(){var t=yt.width();o!=t&&(o=t,e())},1);yt.on(t,n)}else yt.on(t,vt.debounce(e,1))}(A("resize"),function(){m.is(":hidden")||(U(),_(),tt(),et=J(),(ot=Z())(et("resize"),!0,!0))}),m.on("reflow",it),pt.support&&pt.support.datatables&&function(t){if(t.dataTableSettings)for(var e=0;e<t.dataTableSettings.length;e++){var o=t.dataTableSettings[e].nTable;if(t[0]==o)return!0}return!1}(m)&&m.on("filter",it).on("sort",it).on("page",it),pt.support&&pt.support.bootstrap&&yt.on(A("shown.bs.tab"),it),pt.support&&pt.support.jqueryUI&&yt.on(A("tabsactivate"),it),bt){var ct=null;vt.isFunction(pt.autoReflow)&&(ct=pt.autoReflow(m,x)),ct||(ct=x.length?x[0]:m[0]),(ut=new MutationObserver(function(t){for(var e=function(t){return t&&t[0]&&("THEAD"==t[0].nodeName||"TD"==t[0].nodeName||"TH"==t[0].nodeName)},o=0;o<t.length;o++)if(!e(t[o].addedNodes)&&!e(t[o].removedNodes)){it();break}})).observe(ct,{childList:!0,subtree:!0})}m.data("floatThead-attached",{destroy:function(){var t=".fth-"+e;return P(),m.css(F),p.remove(),mt&&a.remove(),l.parent().length&&l.replaceWith(d),X(!1),bt&&(ut.disconnect(),ut=null),m.off("reflow reflowed"),x.off(t),S.off(t),W&&(x.length?x.unwrap():m.unwrap()),j?x.data("floatThead-containerWrap",!1):m.data("floatThead-containerWrap",!1),m.css("minWidth",N),R.remove(),m.data("floatThead-attached",!1),yt.off(t),yt.off("fth-beforeprint fth-afterprint"),ft&&ft.removeListener(dt),lt=st=function(){},function(){return m.floatThead(pt)}},reflow:function(){it()},setHeaderHeight:function(){Q()},getFloatContainer:function(){return R},getRowGroups:function(){return n?R.find(">table>thead").add(m.children("tbody,tfoot")):m.children("thead,tbody,tfoot")}})}),this}}(function(){var t=window.jQuery;return"undefined"!=typeof module&&module.exports&&!t&&(t=require("jquery")),t}());