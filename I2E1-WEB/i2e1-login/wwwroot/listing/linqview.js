$(document).ready(function () {
    $("#firstDay").click(function () {
        $("ul.allDay").toggle();
        $("#firstDay").toggle();
    });
    $(".allDay").click(function () {
        $("ul.allDay").toggle();
        $("#firstDay").toggle();
    });

    $(".tile__img").click(function() {
       var zoom = $(this).attr("src");
        openImage(zoom);
    })
});

/*document.addEventListener("DOMContentLoaded", function () {
    var lazyImages = [].slice.call(document.querySelectorAll("img.lazy"));;

    if ("IntersectionObserver" in window && "IntersectionObserverEntry" in window && "intersectionRatio" in window.IntersectionObserverEntry.prototype) {
        var lazyImageObserver = new IntersectionObserver(function (entries, observer) {
            entries.forEach(function (entry) {
                if (entry.isIntersecting) {
                    var lazyImage = entry.target;
                    lazyImage.src = lazyImage.dataset.src;
                    lazyImage.srcset = lazyImage.dataset.srcset;
                    lazyImage.classList.remove("lazy");
                    lazyImageObserver.unobserve(lazyImage);
                }
            });
        });

        lazyImages.forEach(function (lazyImage) {
            lazyImageObserver.observe(lazyImage);
        });
    }
});*/

// zoom images on tap
function openImage(zoom) {
    var allImages = [];
    $(".tile__img").each(function () {
        var url = $(this).attr('src').replace("thumbnail_", "");
        allImages.push({ url: url, isActive: $(this).attr('src') == zoom });
    });
    //Android.loadImages(JSON.stringify(allImages));
    callAppFunction("loadImages", [JSON.stringify(allImages)])
}

//cookie
function getCookie(cname) {
    var name = cname + "=";
    var ca = document.cookie.split(';');
    for (var i = 0; i < ca.length; i++) {
        var c = ca[i];
        while (c.charAt(0) == ' ') c = c.substring(1);
        if (c.indexOf(name) != -1) return c.substring(name.length, c.length);
    }
    return "";
}

//check ios or android for data
function callAppFunction(methodName, methodData) {
    if (getCookie('isios') == "True") {
        var iosData = {
            methodName: methodName,
            data: methodData
        }
        //console.log(iosData);
        window.webkit.messageHandlers.iosCallback.postMessage(iosData);
    } else {
        var functionString = "Android." + methodName;
        if (eval("typeof " + functionString) != "undefined") {
            var funcString = 'Android.' + methodName + '(';
            for (var i = 0; i < methodData.length; ++i) {
                if (typeof methodData[i] == 'string')
                    funcString += "'" + methodData[i] + "'";
                else
                    funcString += methodData[i];

                if (i != methodData.length - 1)
                    funcString += ",";
            }
            console.log(funcString);
            eval(funcString + ")");
        }
    }
}
