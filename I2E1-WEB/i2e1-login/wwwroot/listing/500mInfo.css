        body {
            font-family: lato !important;
            background-color: #ebeff0;
        }
        a{
            text-decoration: none;
            color:#1da1f2;
        }
        .read_more, .read_less {
            color: #1da1f2;
            font-weight: 700;
        }
        .pictures {
            min-height: 301px;
            background-color: #ffffff;
            margin-bottom: 12px;
        }
        
        .pictures_container{
            padding: 16px 0 20px 16px;
        }
        .pictures_head{
            font-weight: bold;
            font-size: 14px;
            padding: 0 16px 12px 0;
        }
        .view_all {
            color: #1da1f2;
            position: relative;
            float: right;
            margin: auto 0 auto auto;
        }
        .row {
            display: -ms-flexbox; /* IE10 */
            display: flex;
            -ms-flex-wrap: nowrap; /* IE10 */
            flex-wrap: nowrap;
            overflow-x:auto;
            -webkit-overflow-scrolling: touch;
        }
        .column {
            -ms-flex: 25%; /* IE10 */
            /*            flex: 25%;
            max-width: 25%;
            padding: 0 2px 0 0;
            flex: 0 0 auto;*/
            padding-right: 4px;
            width: 76px;
        }
        .column img {
            margin-top: 2px;
            border-radius: 4px;
            vertical-align: middle;
            width: 76px;
            height: 76px;
        }
        .wifi_list {
            min-height: 133px;
            background-color: #ffffff;
            margin-bottom: 12px;
        }
        .container{
            padding: 16px 16px 16px 16px;
        }
        .head{
            font-weight: bold;
            font-size: 14px;
            padding-bottom: 24px;
        }
        .content{
            font-size: 14px;
            line-height: 17px !important;
        }
        .wifi_signal{
            padding: 8px 0 0 0;
            float: left;
            width: 100px;
            height: 60px;
        }
        .btn{
            float: right;
            font-family: lato !important;
            height: 40px !important;            
            line-height: 17px !important;
            border: none;           
            border-radius: 4px;
            box-shadow: 0 3px 6px 0 rgba(0, 0, 0, .16) !important;
            background-color: #1da1f2 !important;
            color: #ffffff !important;
            font-weight: bold;
        }
        .btn:focus {
            outline: none;
        }
        .add_business{            
            min-height: 165px;
            background-color: #ffffff;
            margin-bottom: 12px;
        }
        .wikipedia {            
            height: auto;
            background-color: #ffffff;
            margin-bottom: 12px;            
        }
        .grid {
            display: grid;
            grid-template-columns: auto auto;
        }
        .wiki_img {
            position: relative;
            width: 40px;
            height: 40px;
            float: right;
            margin: auto 0 auto auto;
        }
        .heading {
            padding-top: 4px;
        }
        .wiki_head{
            font-weight: bold;
            font-size: 14px;
            padding-bottom: 5.5px;
        }
        .story{ 
            background-image: url(../images/500mInfo/old_paper.png);
            background-repeat: no-repeat;
            background-size: cover; 
            /*background-color: #f1e9d2; */      
            min-height: 200px;
            margin-bottom: 12px;            
            /*background-color: #f8f5de;
            background-image: linear-gradient(to right, rgba(255,210,0,0.4), rgba(200, 160, 0, 0.1) 11%, rgba(0,0,0,0) 35%, rgba(200, 160, 0, 0.1) 65%);
            box-shadow: inset 0 0 75px rgba(255,210,0,0.3), inset 0 0 20px rgba(255,210,0,0.4), inset 0 0 30px rgba(220,120,0,0.8);*/
            width: 100%;
        } 
        .story_text{
            padding-bottom: 15px;
        }       
        .story_text p{
            margin: 0;
        }
        .extra_read {
            display: none;
        }
        .sub_heading{
            font-size: 10px;
            font-weight: normal;
            font-style: italic;
            line-height: 12px;
        }
        .mascot-fnc {
            height: auto;
            background-color: #ffffff;
            margin-bottom: 12px;
        }
        .mascot-comment {
            background-image: url("../images/500mInfo/Mascot_fnc.svg");
            background-repeat: no-repeat;
            margin: 4px 0 0 13px;
            height: 180px;
        }
        
        /*Create ripple effect*/

        
        .ripple {
            position: relative;
            overflow: hidden;
            transform: translate3d(0, 0, 0);
        }

        .ripple:after {
            content: "";
            display: block;
            position: absolute;
            width: 100%;
            height: 100%;
            top: 0;
            left: 0;
            pointer-events: none;
            background-image: radial-gradient(circle, #fff 10%, transparent 11.01%);
            background-repeat: no-repeat;
            background-position: 50%;
            transform: scale(10, 10);
            opacity: 0;
            transition: transform .3s, opacity 1s;
        }

        .ripple:active:after {
            transform: scale(0, 0);
            opacity: .3;
            transition: 0s;
        }
        /*-------*/
        #photos {
            /* Prevent vertical gaps */
            line-height: 0;   
            -webkit-column-count: 11;
            -webkit-column-gap:   0px;
            -moz-column-count:    11;
            -moz-column-gap:      0px;
            column-count:         11;
            column-gap:           0px;
        }
        @media (max-width: 1200px) {
            #photos {
                -moz-column-count:    11;
                -webkit-column-count: 11;
                column-count:         11;
            }
        }
        @media (max-width: 1000px) {
            #photos {
                -moz-column-count:    11;
                -webkit-column-count: 11;
                column-count:         11;
            }
        }
        @media (max-width: 800px) {
            #photos {
                -moz-column-count:    11;
                -webkit-column-count: 11;
                column-count:         11;
            }
        }
        @media (max-width: 400px) {
            #photos {
                -moz-column-count:    11;
                -webkit-column-count: 11;
                column-count:         11;
            }
        }
