        body {
            margin: 0;
            padding: 0;
            font-family: Lato;
            background-color: #ebeff0;
            max-width: 100%;
            margin: 0 auto;
        }

        hr {
            margin: 0 0 0 50px;
            border: .5px solid #bcbcbc;
            background-color: #bcbcbc;
            opacity: 0.4;
        }
        p {
            margin: 0;
        }
        span {
            min-width: 92%;
            font-size: 14px;
            color: #999;
            padding-right:16px;
            text-align: left;
        }
        a {
            color: #1da1f2;
            text-decoration: none;
        }
        .shopphone{
            display: inline-block;
            width: 80px;
        }
        .material-icons.grey {
            color: #999;
        }

        #u_0_0 div div {
            width: auto !important;
        }
        /*.lazy, .tile__img { border-radius: 4px }*/
        .material-icons.blue { color: #1da1f2 }
        .feed-title, .photo-title, .review-title { color: #999 }
        .grid-auto{display: grid; grid-template-columns: auto auto;}
        .grid-container {display: grid;grid-template-columns: 50px auto;padding-top: 16px;padding-bottom: 15px}
        .grid-container-new {display: grid;grid-template-columns: 36px auto;padding-bottom: 8px}
        .grid-container-photos, .grid-container-woffer {grid-template-columns: auto auto;grid-template-rows: 19px;display: grid}
        .feed-head, .photos-head, .woffer-head {padding-top: 32px;height: 19px}
        .icon-grey, .icons {font-size: 18px;padding-left: 16px;padding-right: 16px}
        .drop-down, .woffer-title { font-size: 16px }
        .tile:last-child{margin-right: 16px;}
        .woffer-title {padding-left: 16px;color: #999;margin: 0}
        .photos-head { margin-bottom: 24px }
        .fb-title, .feed-title, .insta-title,
        .photo-title, .review-title {padding-left: 15px;margin-top: 0;font-size: 20px;font-weight: 700;color: #999;}
        .fb-title { padding-top: 27px }
        .fb-img { padding-top: 16px }
        .insta-title { padding-top: 35px }
        .insta-img { padding-top: 16px }
        .review-head {padding-top: 32px;height: 19px}
        .average, .checked { color: #fb0 }
        .rating {display: grid;padding: 33px 0}
        .rtext, .rtext1 {max-width: 296px;font-size: 14px;line-height: 1.2;-webkit-box-orient: vertical;overflow: hidden;text-overflow: ellipsis;padding-top: 8px}
        .rtext {display: block;display: -webkit-box;-webkit-line-clamp: 3}
        .rtext1 {display: block;display: -webkit-box}
        .average {text-align: center;padding: 12px 0 0}
        .row {overflow: scroll;max-width: 100%}
        .row__inner {transition: 450ms -webkit-transform;transition: 450ms transform;transition: 450ms transform, 450ms -webkit-transform;font-size: 0;white-space: nowrap;padding-bottom: 10px}
        .tile {
            position: relative;
            display: inline-block;
            width: 300px;
            height: 180px;
            margin-left: 16px;
            transition: 450ms all;
            -webkit-transform-origin: center left;
            transform-origin: center left
        }
        .tile__img {
            width: 300px;
            height: 180px;
            margin-right: 16px;
            border-radius: 4px;
            -o-object-fit: cover;
            object-fit: cover
        }
        /*------------------------Carousel style end------------------------------*/
        .pull-right { float: right; }
        .open-arrow {right: 0;position: absolute;top: 5px;}
        .info-items {background-color: #fff;margin-top: 12px}
        /*.info-items .row { padding-right: 16px }*/
        .allDay span { padding-right: 0;}
        .info-items .row .row_title .info-title {margin-top: 0;font-size: 16px;font-weight: 700;color: #999;position: relative;}
        .text-box {color: #999999;font-size: 14px;}
        ul.payment-option-list {margin: 0;padding: 16px 0 12px 16px;}
        .payment-option-list li {display: inline-block;position: relative; padding-right: 2px; height: 59px; text-align: center;}
        .payment-option-list li img { width: 40px; height: 40px;}
        .facilities-text {color: #202124; font-size: 12px; margin: 0;}
        .facilities-text-disable {color: #bcbcbc; font-size: 12px; margin: 0;}
        .payment-option-list li img { width: 40px }
        .dealing-span {margin-right: 12px; border-radius: 12px; color: #202124; background-color: #ebeff0; padding:2px 12px; }
        .dealings {background-color: #ffffff;margin-top: 12px;padding: 24px 0 14px 0;}
        .dealings .tile.items {color: #fff;font-size: 12px;padding: 16px;height: 99px;border-radius: 5px;}
        .dealings .tile.items:last-child { margin-right: 16px; }
        .dealings .tile.items:nth-child(1):nth-last-child(1) {width: 84%;display: block;margin: auto;}
        .dealings .tile.items .inner-content {display: block;height: 100%;position: relative;}
        .dealings .tile.items .inner-content .item-title {margin-top: 0;font-size: 16px}
        .selling-info { background: #00ccff; }
        .special-info { background: #ff6c6c; }
        .facilities-info { background: #00dcb0; }
        .item-description {display: block;display: -webkit-box;max-width: 200px;-webkit-line-clamp: 4;-webkit-box-orient: vertical;overflow: hidden;text-overflow: ellipsis;}
        /*.forcewrap { -ms-text-overflow: ellipsis;-o-text-overflow: ellipsis;
        text-overflow: ellipsis;overflow: hidden;-moz-binding: url( 'bindings.xml#ellipsis' );
        white-space: nowrap;display: block;font-size: 12px;margin: 0;max-width: 95%; }
        
            .forcewrap.multiline {line-height: 1.2em;  max-height: 3.6em;white-space: normal;}
            .manual-ellipsis.multiline:after {content: "\02026";position: absolute;right: 10px;bottom: 5px;padding-left: 5px;}

        */

        .forcewrap {display: block;display: -webkit-box;max-width: 100%;height: 54px;margin: 0;font-size: 12px;line-height: 1.5;-webkit-line-clamp: 3;
            -webkit-box-orient: vertical;overflow: hidden;text-overflow: ellipsis;word-break: break-word;white-space: normal;}
        .story{   
            height: auto;
            margin-top: 12px;            
            background-color: #f8f5de;
            background-image: linear-gradient(to right, rgba(255,210,0,0.4), rgba(200, 160, 0, 0.1) 11%, rgba(0,0,0,0) 35%, rgba(200, 160, 0, 0.1) 65%);
            box-shadow: inset 0 0 75px rgba(255,210,0,0.3), inset 0 0 20px rgba(255,210,0,0.4), inset 0 0 30px rgba(220,120,0,0.8);
            width: 100%;
        }
        .container{
            padding: 32px 16px 16px 16px;
        }
        .head{
            font-weight: bold;
            font-size: 16px;
            padding-bottom: 24px;
        }
        .content{
            font-size: 14px;
            line-height: 17px !important;
        }
        .story_text{
            padding-bottom: 15px;
        }       
        .story_text p{
            margin: 0;
        }
        .extra_read {
            display: none;
        }
        .sub_heading{
            font-size: 10px;
            font-weight: normal;
            font-style: italic;
            line-height: 12px;
        }
        .rating {width: 100%;display: flex;align-items: center}.horizontal-review .fa { display: inline }.average:after, .average:before, .horizontal-review ul li:after, .horizontal-review ul li:before, .horizontal-review:after {content: "";display: table;clear: both}.horizontal-review ul {list-style-type: none;padding: 0;margin: 0}.left, .right {margin-top: 10px;float: left}.left { width: 30% }.right { width: 70% }.bar-container {width: 100%;background-color: #f1f1f1;text-align: center;color: #fff}.bar {height: 18px;background-color: #fb0;border-top-right-radius: 5px;border-bottom-right-radius: 5px}
        .comments {            
            height: auto;
            background-color: #ffffff;
            margin-top: 12px;            
        }
        .comment-heading {
            color: #202124 !important;
            font-size: 16px;   
        }
        .comment-subheading{
            font-weight: normal;
            font-size: 12px;
            color: #999999;
            font-style: italic;
        }
        .comment-head{
            font-weight: bold;
            font-size: 14px;
            padding-bottom: 20px;
        }
        .comment-ul {
            list-style-type: none;
            margin: 0;
            padding: 0;
        }
        .grid {
            display: grid;
            grid-template-columns: 30px auto;
        }
        .name {
            display: inline-block;
            font-size: 14px;
            font-weight: bold;
        }
        .time {
            float: right;
            display: inline-block;
            min-width: 0;
        }
        .subject{
            color: #999999;
            font-size: 12px;
        }
        .jaiho_img {
            height: 24px;
            width: 24px;
        }
        .connect-div {
            padding: 32px 16px;
            height: 40px;
        }
        .connect-btn {
            width: 100%;
            height: 40px;
            border: none;           
            border-radius: 4px;
            background-color: #1da1f2;
            box-shadow: 0 3px 6px 0 rgba(0, 0, 0, .6);
            color: #ffffff;
        }
        .connect-btn:focus {
            outline: none;
        }
        /*Create ripple effec*/

        .ripple {
          position: relative;
          overflow: hidden;
          transform: translate3d(0, 0, 0);
        }

        .ripple:after {
          content: "";
          display: block;
          position: absolute;
          width: 100%;
          height: 100%;
          top: 0;
          left: 0;
          pointer-events: none;
          background-image: radial-gradient(circle, #fff 10%, transparent 10.01%);
          background-repeat: no-repeat;
          background-position: 50%;
          transform: scale(10, 10);
          opacity: 0;
          transition: transform .5s, opacity 1s;
        }

        .ripple:active:after {
          transform: scale(0, 0);
          opacity: .3;
          transition: 0s;
        }
       /*----------------carousel-----------*/ 
ul.slides {
    display: block;
    position: relative;
    height: 190px;
    margin: 0;
    padding: 0;
    overflow: hidden;
    list-style: none;
}

.slides * {
    user-select: none;
    -ms-user-select: none;
    -moz-user-select: none;
    -khtml-user-select: none;
    -webkit-user-select: none;
    -webkit-touch-callout: none;
}

ul.slides input {
    display: none; 
}


.slide-container { 
    display: block; 
}

.slide-image {
    display: block;
    position: absolute;
    width: 100%;
    height: 100%;
    top: 0;
    opacity: 0;
    transition: all .7s ease-in-out;
}   

.slide-image img {
    width: auto;
    min-width: 100%;
    height: 100%;
}

.carousel-controls {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    z-index: 999;
    font-size: 100px;
    line-height: 190px;
    color: #fff;
}

.carousel-controls label {
    display: none;
    position: absolute;
    padding: 0 20px;
    opacity: 0;
    transition: opacity .2s;
    cursor: pointer;
}

.slide-image:hover + .carousel-controls label{
    opacity: 0.5;
}

.carousel-controls label:hover {
    opacity: 1;
}

.carousel-controls .prev-slide {
    width: 49%;
    text-align: left;
    left: 0;
}

.carousel-controls .next-slide {
    width: 49%;
    text-align: right;
    right: 0;
}

.carousel-dots {
    position: absolute;
    left: 0;
    right: 0;
    bottom: 5px;
    z-index: 999;
    text-align: center;
}

.carousel-dots .carousel-dot {
    display: inline-block;
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background-color: #000;
    opacity: 0.5;
    margin: 8px;
}

input:checked + .slide-container .slide-image {
    opacity: 1;
    transform: scale(1);
    transition: opacity 1s ease-in-out;
}

input:checked + .slide-container .carousel-controls label {
     display: block; 
}

input#img-1:checked ~ .carousel-dots label#img-dot-1,
input#img-2:checked ~ .carousel-dots label#img-dot-2,
input#img-3:checked ~ .carousel-dots label#img-dot-3,
input#img-4:checked ~ .carousel-dots label#img-dot-4,
input#img-5:checked ~ .carousel-dots label#img-dot-5,
input#img-6:checked ~ .carousel-dots label#img-dot-6,
input#img-6:checked ~ .carousel-dots label#img-dot-7,
input#img-6:checked ~ .carousel-dots label#img-dot-8,
input#img-6:checked ~ .carousel-dots label#img-dot-9 {
	opacity: 1;
}


input:checked + .slide-container .nav label { display: block; }

/*-------------slick.js--------------*/

        .slick-dots {
            position: absolute;
            bottom: -25px;
            display: block;
            width: 100%;
            padding: 0;
            margin: 0;
            list-style: none;
            text-align: center;
        }
        .slick-dots li {
            position: relative;
            display: inline-block;
            width: 20px;
            height: 20px;
            /*margin: 0 5px;*/
            padding: 0;
            cursor: pointer;
        }
        .slick-dots li button {
            font-size: 0;
            line-height: 0;
            display: block;
            width: 20px;
            height: 20px;
            padding: 5px;
            cursor: pointer;
            color: transparent;
            border: 0;
            outline: none;
            background: transparent;
        }
        .slick-dots li button:before {
            /*font-family: 'lato';*/
            font-size: 40px;
            line-height: 20px;
            position: absolute;
            top: -25px;
            left: 0;
            width: 20px;
            height: 20px;
            content: '•';
            text-align: center;
            opacity: .50;
            color: #ffffff;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
        }

        .slick-dots li.slick-active button:before {
            opacity: 1;
            color: #ffffff;
        }


        /*------------social-------------*/
        
        .column {
            -ms-flex: 25%; /* IE10 */
            /*            flex: 25%;*/
            max-width: 25%;
            padding: 0 2px 0 0;
            flex: 0 0 auto;
        }
        .column img {
            margin-top: 2px;
            border-radius: 4px;
            vertical-align: middle;
        }        
       
        .social {            
            height: auto;
            background-color: #ffffff;
            margin-top: 12px;            
        }
        .sub-heading{
            font-weight: normal;
            font-size: 12px;
            color: #999999;
        }
        .social-head{
            font-weight: bold;
            font-size: 14px;
            padding-bottom: 24px;
        }
        .social-head span {
            padding-left: 12px;
            position: relative;
            top: -5px;
        }
        .social-head p {
            padding-left: 30px;
        }

        .social-head img {
            width: 24px;
        }
        .social-ul {
            list-style-type: none;
            margin: 0;
            padding: 0;
        }

        .stickydiv {
            position: -webkit-sticky;
            position: sticky;
            top: -1px;
            z-index: 2;
        }



        @media screen and (min-width: 720px) {
            body {
                width: 720px;
            }
        }

        @media screen and (max-width: 320px) {
            body {
                width: 320px;
            }
        }

        @media screen and (max-width: 321px) and (min-width: 375px) {
            body {
                width: 375px;
            }
        }

        @media screen and (max-width: 376px) and (min-width: 414px) {
            body {
                width: 414px;
            }
        }

        @media screen and (max-width: 415px) and (min-width: 719px) {
            body {
                width: 414px;
            }
        }

