var dirtyBit = false;
var confirm = true;
var _cat_and_subcat = [];

function removeDuplicateFromArray(arr) {
    let unique_array = arr.filter(function (elem, index, self) {
        return index == self.indexOf(elem);
    });
    return unique_array
}

var category = {
    init: function (prefix) {
        var index = 0;
        category.renderHiddenIndex = function (item) {
            var data = "<input type='hidden' name='" + prefix + ".Index' value='" + index + "' /><input class='sub-cat-id' name='" + prefix + "[" + index + "].id' type='hidden' value='" + item.id + "'/><input name='" + prefix + "[" + index + "].category' type='hidden' value='" + item.category + "'/><input name='" + prefix + "[" + index + "].subCategory' type='hidden' value='" + item.subCategory + "'/>";
            index++;
            return data;
        };
    },
    addToList: function(item) {
        $(".ui-menu").hide();
        $('#search').val('');

        if (!$('.target').find('input[value="'+ item.id +'"]').length) {
            category.renderSubCat(item);
            category.renderCat(item);
        }
    },
    removeCat: function() {
        var field = $(event.target).parent();
        var id = field.children('input.sub-cat-id').val();
        field.remove();
        $('#catSearch').find('[data-cat="' + id + '"]').remove();
    },
    renderCat: function (item) {
        if ($("#catSearch").find('[data-value="' + item.category + '"]').length == 0)
        $("#catSearch").append("<span href='#' class='tag' data-cat='" + item.id + "' data-value='"+ item.category +"'>" + item.category + "</span>");
    },
    renderSubCat: function(item) {
        $(".target").append("<span href='#' class='tag'>" + category.renderHiddenIndex(item) + item.subCategory + "<a onclick='category.removeCat()' class='material-icons cross-category' style='position: relative; top:4px; font-size:18px; background:#ebeff0; color: #bcbcbc;'>clear</a></span>");
    }
}; 



$.getJSON("/Listing/GetAllCategories", function (response) {
    var items = response.data;
    _cat_and_subcat = items;
    $.widget("custom.catcomplete", $.ui.autocomplete, {
        _create: function () {
            this._super();
            this.widget().menu("option", "items", "> :not(.ui-autocomplete-category)");
        },
        _renderItemData: function () {

        },
        _renderMenu: function (ul, items) {
            var that = this,
                currentCategory = $("#search").val().toLowerCase(),
                subCategory = "";
            $.each(items, function (index, item) {
                var li;
                item.name = item.subCategory;
                if (item.name != subCategory && item.name.toLowerCase().indexOf(currentCategory) >= 0) {
                    li = $("<li class='ui-autocomplete-category'>" + item.name + "</li>");
                    li.click(function () {
                        category.addToList(item);
                    });
                    ul.append(li);
                    subCategory = item.name;
                }
            });
        }
    });
    $("#search").catcomplete({
        delay: 100,
        source: function (request, response) {
            response($.grep(items, function (item) {
                return true;
            }));
        }
    });
});

// check box check-uncheck

$(document).ready(function () {
    $('#checkTfour').click(function () {
        $('#checkTfour').css('display', 'none');
        $('#checkTfour1').css('display', 'inline-block');
        $("#clock").hide();
    });
});
$(document).ready(function () {
    $('#checkTfour1').click(function () {
        $('#checkTfour1').css('display', 'none');
        $('#checkTfour').css('display', 'inline-block');
        $("#clock").show();
    });
});


//add more mobile numbers
$(document).ready(function () {
    $(".addnum2").click(function () {
        $("#biznum2").show();
        $("#bizclear2").show();
        $(".addnum3").show();
        $(".addnum2").hide();
    });
});
$(document).ready(function () {
    $("#bizclear2").click(function () {
        $("#biznum2").hide().val(null);
        $("#bizclear2").hide();
        $(".addnum2").show();
        $(".addnum3").hide();
        $(".addnum4").hide();
    });
});
$(document).ready(function () {
    $(".addnum3").click(function () {
        $("#biznum3").show();
        $("#bizclear3").show();
        $(".addnum4").show();
        $(".addnum3").hide();
    });
});
$(document).ready(function () {
    $("#bizclear3").click(function () {
        $("#biznum3").hide().val(null);
        $("#bizclear3").hide();
        $(".addnum4").hide();
        $(".addnum2").hide();
        $(".addnum3").show();
    });
});
$(document).ready(function () {
    $(".addnum4").click(function () {
        $("#biznum4").show();
        $("#bizclear4").show();
        $(".addnum4").hide();
    });
});
$(document).ready(function () {
    $("#bizclear4").click(function () {
        $("#biznum4").hide().val(null);
        $("#bizclear4").hide();
        $(".addnum4").show();
        $(".addnum2").hide();
        $(".addnum3").hide();
    });
});


//states
/*$(function () {
    var availableTags = [
        'Andhra Pradesh', 'Arunachal Pradesh', 'Assam', 'Bihar','Chhattisgarh', 'Goa', 'Gujarat', 'Haryana',
        'Himachal Pradesh', 'Jammu and Kashmir', 'Jharkhand', 'Karnataka', 'Kerala', 'Madhya Pradesh',
        'Maharashtra', 'Manipur', 'Meghalaya', 'Mizoram', 'Nagaland', 'Odisha', 'Punjab', 'Rajasthan',
        'Sikkim', 'Tamil Nadu', 'Telangana', 'Tripura', 'Uttar Pradesh', 'Uttarakhand', 'West Bengal',
        'Andaman and Nicobar Islands', 'Chandigarh', 'Dadra and Nagar Haveli', 'Daman and Diu', 'Delhi',
        'Lakshadweep', 'Puducherry'
    ];
    $("#tags").autocomplete({
        source: availableTags
    });
});*/


function compressImage(source_img_obj, quality, output_format){       
    var mime_type;
    if(output_format==="png"){
        mime_type = "image/png";
    } else if(output_format==="webp") {
        mime_type = "image/webp";
    } else {
        mime_type = "image/jpeg";
    }

    var cvs = document.createElement('canvas');
    cvs.width = source_img_obj.naturalWidth;
    cvs.height = source_img_obj.naturalHeight;
    var ctx = cvs.getContext("2d").drawImage(source_img_obj, 0, 0);
    var newImageData = cvs.toDataURL(mime_type, quality);
    var result_image_obj = new Image();
    result_image_obj.src = newImageData;
    return result_image_obj;
}

function ajaxPost(url, event) {
    event && event.preventDefault();
    $.ajax({
        type: "POST",
        url: url + (url.indexOf('?') == -1 ? "?loadPartial=true" : "&loadPartial=true"),
        data: $('#parentDiv').children('form').serialize()
    }).done(function (o) {
        if (o.indexOf("span class=dialogView") != -1)
            $('#errorDiv').html(o);
        else
            $('#parentDiv').html(o);
        $("html, body").animate({ scrollTop: $(document).height(0) }, 10);
        dirtyBit = false; //clear dirtyBit
    }).fail(function (xhrObject, textStatus, errorThrown) {
    }).always(function () {
        $('#loader').hide();
    });
    $('#loader').show();
};



function rawMarkerToAndroidMarker(rawMarker) {
    var page1 = rawMarker.page1, page2 = rawMarker.page2;
    page1.wifi_mac_address = page1.listingId;
    var operatingArryToString;
    if (JSON.stringify(page1.operatingHours).indexOf('\\') == -1) {
        operatingArryToString = JSON.stringify(page1.operatingHours).replace(/"/g, '\"');
    } else {
        operatingArryToString = JSON.stringify(page1.operatingHours);
    }
    var thumbUrl = '';
    if(page1.shopName)
        page1.name = page1.shopName;
    if (page1.shopAddress)
        page1.address = page1.shopAddress;
    if (page1.shopCity)
        page1.city = page1.shopCity;
    if (page1.shopState)
        page1.state = page1.shopState;
    if (page1.shopSubCat) {
        page1.category = "";
        page1.subCategory = "";
        page1.shopSubCat.forEach(function (m) {
            page1.category += m.category + ",";
            page1.subCategory += m.subCategory + ",";
            thumbUrl = "https://cdn.linq.app/subcategories/original/" + m.id + "/1_crop_sq.jpg";
        });
        if (page1.category.charAt(page1.category.length - 1) == ',') {
            page1.category = page1.category.substring(0, page1.category.length - 1);
            page1.subCategory = page1.subCategory.substring(0, page1.subCategory.length - 1);
        }
    }
    page1.type = 6;
    if (page1.shopContactP) {
        page1.extra = {
            contactNumber: page1.shopContactP,
            secondaryContactNumber: page1.shopContactS,
            websiteLink: page1.website,
            facebookLink: page2.facebook,
            whatsappNumber: page2.whatsapp,
            instagramLink: page2.instagram,
            twitterLink: page2.twitter,
            paytmNumber: page2.paytmNumber,
            upi: page2.upiId,
            operating_hours: operatingArryToString.replace(/"/g, '\\"'),
            thumb: thumbUrl,
            share_url: "https://mylinq.in/" + page1.shopContactP + "?listingId=" + page1.listingId
        };
    }

    var finalObject = {};
    for (var key in page1) {
        if (page1.hasOwnProperty(key) && page1[key] != null) {
            finalObject[key] = page1[key];
        }
    }
    return JSON.stringify(finalObject);
};

function getDirtyBit() {
    return dirtyBit;
}

function confirmNavigation() {
    window.location.href = confirmNavigation.path;
}

function confirmCancel() {
    console.log("confirmCancel : cancelled by user");
}

function navigateTopage(path) {
    if (path) {
        if (getDirtyBit()) {
            confirmNavigation.path = path;
            callAppFunction("showPopup", []);
        } else {
            window.location.href = path;
        }
    }
}

$(document).ready(function() {
    $("#parentDiv").on('keyup',
        'input',
        function() {
            dirtyBit = true;
        })
});

function enableSmartListing(id) {
    $('#' + id).addClass('qaptur-input');
    $('#' + id).click(function() {
        $(".dropdown-ul-" + id).slideToggle();
    });

    $('#' + id).focusout(function() {
        $(".dropdown-ul-" + id).slideUp();
    })
    //to select value from li
    var input = $('#' + id);
    $("ul.dropdown-ul-" + id +" li").click(function() {
        input.val($(this).attr('value'))
        $("ul.dropdown-ul-" + id +" li.active").removeClass("active");
        $(this).addClass("active");
        $("ul.dropdown-ul-" + id).slideUp();
    });
}
//cookie
function getCookie(cname) {
    var name = cname + "=";
    var ca = document.cookie.split(';');
    for (var i = 0; i < ca.length; i++) {
        var c = ca[i];
        while (c.charAt(0) == ' ') c = c.substring(1);
        if (c.indexOf(name) != -1) return c.substring(name.length, c.length);
    }
    return "";
}

function openMap() {
    
}


//check ios or android for data
function callAppFunction(methodName, methodData) {
    if (getCookie('isios') == "True") {
        var iosData = {
            methodName: methodName,
            data: methodData
        }
        //console.log(iosData);
        window.webkit.messageHandlers.iosCallback.postMessage(iosData);
    } else {
        var functionString = "Android." + methodName;
        if (eval("typeof " + functionString) != "undefined") {
            var funcString = 'Android.' + methodName + '(';
            for (var i = 0; i < methodData.length; ++i) {
                if (typeof methodData[i] == 'string')
                    funcString += "'" + methodData[i] + "'";
                else
                    funcString += methodData[i];

                if (i != methodData.length - 1)
                    funcString += ",";
            }
            console.log(funcString);
            eval(funcString + ")");
        }
    }
}


