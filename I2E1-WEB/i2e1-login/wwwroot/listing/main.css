html,body{background-color:#f9f9f9;font-family:Lato;margin:0;padding:0;max-width:100%;}
a{cursor:pointer;}
input{width:100%;border:0;height:25px;}
input[type=text],input[type=number],input[type=tel],input[type=email],input[type=url]{
width: 100%;height: 30px;margin-top: 5px;box-sizing: border-box;border: none;font-family: lato;border-bottom: 1px solid #bcbcbc;}
input[type=text]:focus,input[type=number]:focus,input[type=tel]:focus,input[type=email]:focus,input[type=url]:focus{
width: 100%; height: 30px;box-sizing: border-box;border: none;outline: none;border-bottom: 1px solid #1da1f2;}
input[type="file"]{width: 22px;opacity: 0;left: -24px;top: -8px;position: relative;font-family: Lato;}
.qaptur-input{border:none;background-color:#f9f9f9;border-radius: 2px;height: 32px;}
.qaptur-input ~ .helpText{display: none;}
input[type=checkbox]{width: 20px;border: 1px solid #1da1f2;margin: 0 3px 0 0;}
/*input[type=checkbox]:checked{width: 20px;border: 1px solid #1da1f2;margin: 0 3px 0 0;outline-color: #FF6C6C;}
input[type="checkbox"]{content: '';background: #ffffff;box-shadow: inset 0 1px 3px rgba(0, 0, 0.0.1);}*/
/*Remove the autofill background colour of chrome*/
input:-webkit-autofill,input:-webkit-autofill:hover,input:-webkit-autofill:focus,input:-webkit-autofill:active {
-webkit-box-shadow: 0 0 0px 1000px white inset !important;}
/*change the color of cursor in input and textares starts*/
input, textarea {caret-color: #ffbb00;}
/*change the color of cursor in input and textares ends*/
placeholder {color: #bcbcbc;font-weight: 400;}
p {margin: 0;padding: 0;}
textarea {margin: 0;padding: 2px;border-radius: 4px;border-color: #999999;font-family: Lato;width: 100%;}
.card {margin: 12px 8px;box-shadow: 0 1px 2px 0 #bcbcbc;-webkit-border-radius: 4px;-moz-border-radius: 4px;border-radius: 4px;}
.tabtext {color: #bcbcbc;position: relative;margin: 0 28px 0 22px;text-align: right;}
.selected {color: #ffbb00;}
.head {height: 61px;font-size: 14px;line-height: 17px;}
.headText {padding: 24px 0px 20px 16px;}
.currentLocation {background-color: #ffffff;height: 40px;}
.locationText {color: #1da1f2;font-size: 14px;line-height: 17px;font-weight: bold;padding: 11px 0px 12px 16px;}
.business {background-color: #ffffff;min-height: 50px;-webkit-border-radius: 4px;-moz-border-radius: 4px;border-radius: 4px;}
.bizhead {padding: 20px 0px 0px 16px;font-size: 14px;font-weight: bold;line-height: 17px;color: #999999;}
.bizName {padding: 16px 16px 16px 16px;font-size: 14px;}
.helpText {font-size: 12px;color: #999999;padding: 6px 0px 12px 0px;}
.openingHours{min-height:86px;background-color:#fff;margin-top:12px;-webkit-border-radius:4px;-moz-border-radius:4px;border-radius:4px}
.hourHead{padding:20px 0 0 16px;font-size:14px;font-weight:700;line-height:17px;color:#999}
.timing{padding:16px 0 16px 16px;font-size:14px;line-height:17px;color:#202124}
.openTime,.closeTime,.dOpenTime,.dCloseTime{width: 78px;color: #202124;font-weight: bold;font-family: Lato;text-align: center;}
.hour,.minute{width: 40px !important;color: #1da1f2;}
.hour:focus,.minute:focus{border: none;outline: none;border: 1px solid #1da1f2;}
.contact {background-color: #ffffff;margin-top: 12px;min-height: 122px;}
.numberHead {padding: 20px 0px 0px 16px;font-size: 14px;font-weight: bold;line-height: 17px;color: #999999;}
.bizNumber {padding: 16px 16px 16px 16px;font-size: 14px;}
.ownerNumber {padding: 16px 16px 16px 16px;font-size: 14px;}
.savebtn {margin: 24px 16px;height: 40px;}
.btn-1, .btn-2 {background-color: #bcbcbc;border-radius: 4px;font-weight: bold;font-size: 16px;color: #ffffff;margin: 0 0px 0 16px;width: 90%;height: 40px;border: none;}
.enabled {background-color: #1da1f2;box-shadow: 0px 3px 6px #999999;border-radius: 4px;font-weight: bold;font-size: 16px;color: #ffffff;width: 100%;height: 40px;border: none;text-transform: uppercase;}
.disabled {box-shadow: 0px 3px 6px #999999;border-radius: 4px;font-weight: bold;font-size: 16px;color: #ffffff;width: 100%;height: 40px;border: none;text-transform: uppercase;}
#loader .deactivate,.loader .deactivate {position: fixed;width: 100%;height: 100%;background-color: #7d7575;top: 0;left: 0;opacity: 0.1;z-index: 99;}
#loader .img-section,.loader .img-section {position: fixed;left: 50%;top: 40%;z-index: 100;}
#loader .img-section > div,.loader .img-section > div {position: relative;left: -50%;}
.ui-autocomplete-category {width: 90%;background-color: white;padding: .2em .4em;margin: 0 .2em;line-height: 1.5;}
.ui-helper-hidden-accessible {display: none;}
ul {list-style: none;}
ul#ui-id-1 {padding-left: 7px !important;background: #ffffff;}

.ui-autocomplete {
    max-height: 150px;
    overflow-y: scroll;
    overflow-x: hidden;
}

.multiselect {
    width: 200px;
}

.selectBox {
    position: relative;
}

.selectBox select {
    width: 100%;
    font-weight: bold;
}

.overSelect {
    position: absolute;
    left: 0;
    right: 0;
    top: 0;
    bottom: 0;
}

#checkboxes {
    display: none;
    border: 1px #dadada solid;
}

#checkboxes label {
    display: block;
}

#checkboxes label:hover {
    background-color: #1e90ff;
}

/*----tabs----*/
.steps > ul {
    font-weight: bold;
    padding: 0;
    text-align: center;
    position: relative;
    z-index: 1;
    margin: 0;
    height: 100%;
}

.steps > ul > li {
    display: inline-block;
    background-color: #f1f1f1;
    /*padding-top: 0.6em;*/
    position: relative;
    width: 50%;
    margin: 0 0 0 -2px;
    /*    top: 17px;*/
    height: 100%;
}

.steps > ul > li a {
    text-decoration: none;
    position: relative;
    top: 13px;
}


.steps {
    background-color: #ffffff;
    color: #ffbb00;
    height: 50px;
    /*margin: 8px 12px 0px 12px;*/
    box-shadow:0 3px 6px #bcbcbc;
    border-radius: 4px 4px 0 0;
    /*padding-top: 10px;*/
}

.progressbar {
    background-color: #ffffff;
    height: 2px;
    /*margin: 1px 12px 0px 12px;*/
    border-radius: 4px 4px 4px 4px;
}

#progress {
    position: relative;
    /*margin-left: 16px;*/
    top: -6.5px;
    width: 63%;
    /*background-color: #ebeff0;*/
    display: inline-block;
    padding-top: 20%;
}

#myBar {
    /*width: 25%;*/
    height: 2px;
    background-color: #ffffff;
    /*text-align: center;
    color: white;*/
    line-height: 30px;
    
}

#menu ul li.active {
    background-color: #1da1f2;
    color: #ffffff;
}

#menu ul li.active .tabtext{
    color: #ffffff;
}

#menu ul li.inactive .tabtext{
    color: #1da1f2;
}

.number {
    width: 18px; 
    padding-right: 8px; 
    position: relative; 
    top: 3px;
}

.submenu-1, .submenu-2 {
    padding: 0 16px 0 16px;
}

.error {
    border-color: #FF6C6C;
}

.error-message {
    color: #FF6C6C;
    margin-bottom: 1px;
    outline: none;
    font-size: 12px;
}

ul#ui-id-2 {
    left: 0 !important;
    background: #ffffff !important;
}

.photo-preview {    
    border: 1px solid #ffbb00;
    border-radius: 4px;
    padding: 5px;
    width: 25%;
}
.target {
    outline: none;
}
.tag {
    color: #202124;
    background-color: #ebeff0;
    /*    border: 1px solid #b3cee1;*/
    border-radius: 12px;
    padding: 3px 4px 3px 4px;
    margin: 2px 2px 2px 0;
    text-decoration: none;
    font-size: 14px;
    line-height: 2.4;
}
.tag:hover {
    background-color: #c4dae9;
    border-bottom: 1px solid #c4dae9;
    border-right: 1px solid #c4dae9;
    text-decoration: none;
}

.mandatory {
    color:#ff6c6c
}

.ui-timepicker-standard {
    padding: 0 !important;
    border: none !important;
    box-shadow: 0 0 10px 0 #bcbcbc !important;
    border-radius: 2px !important; 
    width: 14% !important;
}

.ui-timepicker-standard a {
    color: #bcbcbc !important;
    font-family: lato;
    font-size: 14px;
    padding: 0.2em !important;
}

.ui-timepicker-standard .ui-state-hover {
    color: #1da1f2 !important;
    border: none !important;
    background-color: #ffffff !important;
    border-bottom: 1px solid #bcbcbc !important;
}

.ui-corner-all {    
    border-bottom: 1px solid #bcbcbc !important;
}

.selectTime{
    color: #1da1f2;
}

.ui-timepicker-viewport {
    padding-right: 0 !important;
    width: 100% !important;
}
.ui-corner-all, .ui-state-hover{
    border-bottom-right-radius: 0px !important;
    border-bottom-left-radius: 0px !important;
    border-top-right-radius: 0px !important;
    border-top-left-radius: 0px !important;
}

.facility-gap {
    padding-bottom: 16px;
}

.facility-text-gap {
    padding-left: 8px;
}

/*dropdown in datalist*/
input::-webkit-calendar-picker-indicator {
    /* display: none;*/
    color: #1da1f2;    
    background:#ebeff0;
    border:7px solid #ebeff0;
}


/*checkbox customization*/

input[type="checkbox"] {
    -webkit-appearance:none;/* Hides the default checkbox style */ 
    height:18px;
    width:18px;
    cursor:pointer;
    position:relative;
    -webkit-transition: .15s;
    border-radius:2px;
    /*background-color:#ffbb00;*/
}

input[type="checkbox"]:checked {
    background-color:#1da1f2;
}

input[type="checkbox"]:before, input[type="checkbox"]:checked:before {
    position:absolute;
    top:0;
    left:0;
    width:100%;
    height:100%;
    /*line-height:2em;*/
    text-align:center;
    color:#fff;
    content: "\2718";
}

input[type="checkbox"]:checked:before {
    content: "\2714";
}
input[type="checkbox"]:hover:before {
    background:rgba(255,255,255,0.3);
}


/*radio button customization

input[type="radio"]{
    -webkit-appearance:none;
    border-radius: 50%;
    border:1px solid #1da1f2;
    height:21px;
    -webkit-transition: .15s;
}
input[type="radio"]:checked {
    background-color:#1da1f2;
}
input[type="radio"]:before, input[type="radio"]:checked:before {
    position:absolute;
    top:0;
    left:0;
    width:100%;
    height:100%;
    line-height:2em;
    text-align:center;
    color:#fff;
    content: '✘';
}
input[type="radio"]:checked:before {
    content:  "\2609";
}*/

.filterclear {
    position: relative;
    z-index: 1;
    float: right;
    top: -25px;
    color: #bcbcbc;
}
