var analyticsApp = angular.module('i2e1Analytics', ['ngSanitize', 'ui.router', 'ui.select', 'selectize'])
.config(function ($stateProvider, $urlRouterProvider) {
    $urlRouterProvider.otherwise('/landing/home');
    $stateProvider
        .state('landing', {
            url: '/landing',
            templateUrl: getUrlWithVersion('partial/landing.html'),
            controller: 'landingController'
        })
        .state('landing.home', {
            url: '/home',
            templateUrl: getUrlWithVersion('partial/home.html'),
            controller: 'homeController'
        })
        .state('landing.overview', {
            url: '/overview',
            templateUrl: getUrlWithVersion('partial/overview.html'),
            controller: 'overviewController'
        })
        .state('landing.trends', {
            url: '/trends',
            templateUrl: getUrlWithVersion('partial/trends.html'),
            controller: 'trendsController'
        })
        .state('landing.hourlyTrends', {
            url: '/hourlyTrends',
            templateUrl: getUrlWithVersion('partial/hourlyTrends.html'),
            controller: 'hourlyTrendsController'
        })
        .state('landing.loyalty', {
            url: '/loyalty',
            templateUrl: getUrlWithVersion('partial/loyalty.html'),
            controller: 'loyaltyController'
        })
        .state('landing.csc', {
            url: '/crossSroreComparison',
            templateUrl: getUrlWithVersion('partial/crossSroreComparison.html'),
            controller: 'cscController'
        })
        .state('landing.distributions', {
            url: '/distributions',
            templateUrl: getUrlWithVersion('partial/distributions.html'),
            controller: 'distributionsController'
        })
        .state('landing.csp', {
            url: '/crossStorePerformance',
            templateUrl: getUrlWithVersion('partial/crossStorePerformance.html'),
            controller: 'cspController'
        });
});


analyticsApp.filter('propsFilter', function () {
    return function (items, props) {
        var out = [];

        if (angular.isArray(items)) {
            items.forEach(function (item) {
                var itemMatches = false;

                var keys = Object.keys(props);
                for (var i = 0; i < keys.length; i++) {
                    var prop = keys[i];
                    var text = props[prop].toString().toLowerCase();
                    if (item[prop].toString().toLowerCase().indexOf(text) !== -1) {
                        itemMatches = true;
                        break;
                    }
                }

                if (itemMatches) {
                    out.push(item);
                }
            });
        } else {
            // Let the output be the input untouched
            out = items;
        }

        return out;
    }
});

analyticsApp.filter('exactFilter', function () {
    return function (items, props) {
        var out = [];

        if (angular.isArray(items)) {
            items.forEach(function (item) {
                var itemMatches = true;

                var keys = Object.keys(props);
                for (var i = 0; i < keys.length; i++) {
                    var prop = keys[i];
                    var vals = props[prop], found = false;
                    for (var j = 0; j < vals.length; ++j) {
                        if (item[prop] && item[prop].toString() === vals[j].toString()) {
                            found = true;
                            break;
                        }
                    }
                    if (!found && vals.length) {
                        itemMatches = false; break;
                    }
                }

                if (itemMatches) {
                    out.push(item);
                }
            });
        } else {
            // Let the output be the input untouched
            out = items;
        }

        return out;
    }
});