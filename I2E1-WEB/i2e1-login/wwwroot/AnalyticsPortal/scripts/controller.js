analyticsApp
.controller('landingController', ['$rootScope', '$scope', '$state', 'service', '$location', '$anchorScroll', function ($rootScope, $scope, $state, service, $location, $anchorScroll) {
    service.whichUser().then(function (data) {
        $scope.$parent.routerDetails = data.storeDetails.filter(function (store) {
            if (store.storeName && store.mmNasId) return true;
        });
        $rootScope.name = data.name || 'User';
        $rootScope.userName = data.username || '';

        if ($rootScope.userName === '<EMAIL>') {
            $scope.$parent.routerDetails = service.demofizeStores($scope.$parent.routerDetails)
        }

        $rootScope.nasid = $scope.location = $scope.$parent.routerDetails[0].nasid;
        $rootScope.mmNasId = $scope.$parent.routerDetails[0].mmNasId;
        $rootScope.nasList = $scope.locations = $scope.$parent.routerDetails.map(function (a) { return a.nasid; });
        $rootScope.nasList.length = $scope.locations.length = $scope.locations.length > 40 ? 40 : $scope.locations.length;

        $rootScope.mmNasList = $scope.$parent.routerDetails.map(function (a) { return a.mmNasId; });
        $rootScope.mmNasList.length = $rootScope.nasList.length;
        /********* Below prsocess date is called for updating no of active days after page load ******/
        processDates($rootScope.dates.fromDate, $rootScope.dates.toDate);
    });

    //--------------------
    $rootScope.dates = {};
    $rootScope.dates.fromDate = moment().subtract(7, 'days');
    $rootScope.dates.toDate = moment().subtract(1, 'days');
    $rootScope.dates.completeFromDate = $rootScope.dates.fromDate.format(Constants.completeFloorDate);
    $rootScope.dates.completeToDate = $rootScope.dates.toDate.format(Constants.completeCielDate);
    $rootScope.dates.fromShowDate = $rootScope.dates.fromDate.format(Constants.dateFormat);
    $rootScope.dates.toShowDate = $rootScope.dates.toDate.format(Constants.dateFormat);

    var processDates = function (start, end) {
        $rootScope.dates.fromShowDate = start.format(Constants.dateFormat);
        $rootScope.dates.toShowDate = end.format(Constants.dateFormat);
        $rootScope.dates.fromDate = start;
        $rootScope.dates.toDate = end;
        service.GetDeltaDates($rootScope.dates.fromDate,
            $rootScope.dates.toDate).then(function (data) {
                $rootScope.dates.deltaStartDate = data.startDate;
                $rootScope.dates.deltaEndDate = data.endDate;
            });

        $rootScope.$broadcast('params-updated');

        $rootScope.nasid && service.GetActiveDays($rootScope.mmNasId || $rootScope.nasid,
            $rootScope.dates.fromDate,
            $rootScope.dates.toDate).then(function (data) {
                $rootScope.dates.activeDays = data.active_days;
                $rootScope.dates.totalDays = data.total_days;
            });
    }
    $('#date-range-picker').daterangepicker({
        startDate: $rootScope.dates.fromDate,
        endDate: $rootScope.dates.toDate,
        linkedCalendars: false,
        maxDate: new Date(),
        ranges: {
            'Today': [moment(), moment()],
            'Yesterday': [moment().subtract(1, 'days'), moment().subtract(1, 'days')],
            'Last 7 Days': [moment().subtract(7, 'days'), moment().subtract(1, 'days')],
            'Last 30 Days': [moment().subtract(30, 'days'), moment().subtract(1, 'days')],
            'This Month': [moment().startOf('month'), moment().endOf('month')],
            'Last Month': [moment().subtract(1, 'month').startOf('month'), moment().subtract(1, 'month').endOf('month')]
        },
        locale: {
            format: Constants.dateFormat
        },
    }, processDates);
    //-------------------

    $rootScope.showMap = false;
    $scope.updateLocation = function () {
        $rootScope.showMap = true;
        $rootScope.nasid = $scope.location;
        $rootScope.mmNasId = getMMNas($scope.$parent.routerDetails, $scope.location);

        $scope.location && ($rootScope.mmNasId = getMMNas($scope.routerDetails, $scope.location));

        if ($state.current.name === 'landing.csc' || $state.current.name === 'landing.csp') {
            $rootScope.nasList = $scope.locations;
            if ($scope.locations.indexOf("-1") > -1) {
                $scope.selectAllOptions();
                return;
            }
            if ($scope.locations && $scope.locations.length) {
                $rootScope.mmNasList = [];
                angular.forEach($scope.locations, function (primaryNas, key) {
                    primaryNas &&
                    $rootScope.mmNasList.push(getMMNas($scope.routerDetails, primaryNas));
                });
            }
        }
        processDates($rootScope.dates.fromDate, $rootScope.dates.toDate);
        $rootScope.$broadcast('params-updated');
    };

    $scope.singleLocationSelectorConfig = {
        valueField: 'nasid',
        labelField: 'storeName',
        searchField: ['storeName', 'nasid', 'state', 'city', 'address', 'category', 'emailId', 'mmNasId'],
        delimiter: '|',
        placeholder: 'Choose Location',
        sortField: 'city',
        render: {
            item: function (item) {
                var ret = '<div class="item" title="' + item.nasid + ',' + item.mmNasId + '"><span class="storeName">' + item.storeName + '</span> - ';
                ret += item.city ? '<span class="city">' + item.city + '</span>' : '<span class="city">No City</span>';
                ret += '</div>';
                return ret;
            },
            option: function (item, escape) {
                var label = escape(item.storeName) || escape(item.emailId);
                var caption = '<span>Location: ' + escape(item.city) + '</span>';

                caption += (item.contactNumber || item.emailId) ? '<span>Contact: ' + escape(item.contactNumber || item.emailId) + '</span>' : '';

                return '<div title="' + item.nasid + ',' + item.mmNasId + '">' +
                    '<div class="primary">' + label + '</div>' +
                    (caption ? '<div class="secondary">' + caption + '</div>' : '') +
                '</div>';
            }
        },
        maxItems: 1
    };

    $scope.selectInstance;
    $scope.multipleLocationSelectorConfig = {
        valueField: 'nasid',
        labelField: 'storeName',
        searchField: ['storeName', 'nasid', 'state', 'city', 'address', 'category', 'emailId'],
        plugins: ['remove_button'],
        delimiter: '|',
        placeholder: 'Choose Locations',
        sortField: 'city',
        onInitialize: function(selectsize){
            $scope.selectInstance = selectsize;
            $scope.selectInstance.addOption(newSelectAllItem);
        },
        render: {
            item: function (item) {
                var ret = '<div class="item" title="' + item.nasid + ',' + item.mmNasId + '"><span class="storeName">' + item.storeName + '</span> - ';
                ret += item.city ? '<span class="city">' + item.city + '</span>' : '<span class="city">No City</span>';
                ret += '</div>';
                return ret;
            },
            option: function (item, escape) {
                var label = escape(item.storeName) || escape(item.emailId);
                var caption = '<span>Location: ' + escape(item.city) + '</span>';

                caption += (item.contactNumber || item.emailId) ? '<span>Contact: ' + escape(item.contactNumber || item.emailId) + '</span>' : '';

                return '<div title="' + item.nasid + ',' + item.mmNasId + '">' +
                    '<div class="primary">' + label + '</div>' +
                    (caption && item.nasid != '-1'  ? '<div class="secondary">' + caption + '</div>' : '') +
                '</div>';
            }
        },
        maxItems: $state.current.name === 'landing.csc' ? 10 : 40
    };

    $scope.selectAllOptions = function () {
        var locationArray = [];
        angular.forEach($scope.selectInstance.options, function (value, index) {
            if (index != '-1')
                locationArray.push(index);
        });
        $scope.locations = locationArray;
        $scope.updateLocation();
    };

    $rootScope.scrollTo = function (id) {
        var old = $location.hash();
        $location.hash(id);
        $anchorScroll();
        $location.hash(old);
    }

    $rootScope.switchLocationSelector = function (options) {
        if (options && (options.multiSelect || options.label === 'CSC')) {
            $('.single-location').addClass('display-none');
            $('.multiple-location').removeClass('display-none');
        } else {
            $('.multiple-location').addClass('display-none');
            $('.single-location').removeClass('display-none');
        }
        $rootScope.scrollTo('date-range-picker');
    }
    $rootScope.switchLocationSelector();
    
    $scope.leftRail = [
        { icon: 'home_low', label: 'Home', sref: 'landing.home' },
        { icon: 'overview_low', label: 'Overview', sref: 'landing.overview' },
        { icon: 'trends_low', label: 'Trends', sref: 'landing.trends' },
        { icon: 'Hourly-trends_low', label: 'Hourly Trends', sref: 'landing.hourlyTrends' },
        { icon: 'Loyalty_low', label: 'Engagement', sref: 'landing.loyalty' },
        { icon: 'distribution_low', label: 'Distribution', sref: 'landing.distributions' },
        { icon: 'cross-store_low', label: 'CSC', sref: 'landing.csc' },
        { icon: 'cross_store_performance', label: 'CSP', sref: 'landing.csp' }
    ];

    $scope.data = {
        selected: ''
    }

    $rootScope.togglePageDefinition = function () {
        $('.define.glyphicon').toggleClass('display-none');
    };

    $scope.openMenu = function () {
        $('.left-side-icon-bar').toggleClass('hover-this');
        $('.menu').toggleClass('move-this');
        $('.screen-backdrop').toggleClass('backdrop');
    };

    $scope.hideMenu = function () {
        $('.left-side-icon-bar').removeClass('hover-this');
        $('.menu').removeClass('move-this');
        $('.screen-backdrop').removeClass('backdrop');
    };

    $scope.logout = function () {
        service.logout();
    };

    if ($state.current.name === 'landing') $state.go('landing.home')
}])
.controller('homeController', ['$rootScope', '$scope', 'service', 'messageService', function ($rootScope, $scope, service, messageService) {
    $scope.$on('params-updated', function () {
        if (!$rootScope.nasid) {
            messageService.showError('Please select a location');
            return;
        }
        
        service.GetMapSummaryWifi(
            $rootScope.nasid,
            $rootScope.dates.fromDate,
            $rootScope.dates.toDate).then(function (data) {
                angular.forEach(data, function (value, key) {
                    $scope.vicinity[key] = value.formatNumber();
                })
            });
        service.GetMapSummaryFootfall(
            $rootScope.mmNasId,
            $rootScope.dates.fromDate,
            $rootScope.dates.toDate).then(function (data) {
                angular.forEach(data, function (value, key) {
                    $scope.vicinity[key] = value.formatNumber();
                })
            });
    });

    $scope.vicinity = {
        total_foot_vicinity: 0,
        total_foot_passerby: 0,
        total_foot_instore: 0,
        total_wifi_users: 0
    }
    $rootScope.$broadcast('params-updated');
}])
.controller('overviewController', ['$rootScope', '$scope', 'service', 'messageService', function ($rootScope, $scope, service, messageService) {
    var mapData = function (data) {
        angular.forEach(data, function (value, key) {
            $scope.inVicinity[key] && ($scope.inVicinity[key].data = String.prototype.formatNumber(value instanceof Object ? formatData(value.data, 'tinyreal') : formatData(value, 'tinyreal')));
            $scope.inStore[key] && ($scope.inStore[key].data = String.prototype.formatNumber(value instanceof Object ? formatData(value.data, 'tinyreal') : formatData(value, 'tinyreal')));
            $scope.wifiUsers[key] && ($scope.wifiUsers[key].data = String.prototype.formatNumber(value instanceof Object ? formatData(value.data, 'tinyreal') : formatData(value, 'tinyreal')));
        });
    };

    var mapRate = function (data) {
        angular.forEach(data, function (value, key) {
            if ($scope.inVicinity[key]) {
                $scope.inVicinity[key].rate = value instanceof Object ? value.rate : value;
                $scope.inVicinity[key].rateDirection = $scope.inVicinity[key].rate > 0 ? 'top' : 'bottom';
                $scope.inVicinity[key].rate = formatRate($scope.inVicinity[key].rate);
            }
            if ($scope.inStore[key]) {
                $scope.inStore[key].rate = value instanceof Object ? value.rate : value;
                $scope.inStore[key].rateDirection = $scope.inStore[key].rate > 0 ? 'top' : 'bottom';
                $scope.inStore[key].rate = formatRate($scope.inStore[key].rate);
            }
            if ($scope.wifiUsers[key]) {
                $scope.wifiUsers[key].rate = value instanceof Object ? value.rate : value;
                $scope.wifiUsers[key].rateDirection = $scope.wifiUsers[key].rate > 0 ? 'top' : 'bottom';
                $scope.wifiUsers[key].rate = formatRate($scope.wifiUsers[key].rate);
            }
        });
    };
    
    $scope.$on('params-updated', function () {
        reset();
        if (!$rootScope.nasid) {
            messageService.showError('Please select a location');
            return;
        }

        service.GetMapOverviewFootfallVicinity(
            $rootScope.mmNasId,
            $rootScope.dates.fromDate,
            $rootScope.dates.toDate).then(function (data) {
                mapData(data);
                mapRate(data);
            });
        service.GetMapOverviewFootfallConversion(
            $rootScope.mmNasId,
            $rootScope.dates.fromDate,
            $rootScope.dates.toDate).then(function (data) {
                mapData(data);
                mapRate(data);
            });
       
        service.GetMapOverviewFootfallInstore(
            $rootScope.mmNasId,
            $rootScope.dates.fromDate,
            $rootScope.dates.toDate).then(function (data) {
                mapData(data);
                mapRate(data);
            });
        
        service.GetMapOverviewFootfallInstoreRepeat(
            $rootScope.mmNasId,
            $rootScope.dates.fromDate,
            $rootScope.dates.toDate).then(function (data) {
                mapData(data);
                mapRate(data);
            });
        


        service.GetMapOverviewUsers(
           $rootScope.nasid,
           $rootScope.dates.fromDate,
           $rootScope.dates.toDate).then(function (data) {
               mapData(data);
               mapRate(data);
           });
        service.GetMapOverviewRepeatUsers(
            $rootScope.nasid,
            $rootScope.dates.fromDate,
            $rootScope.dates.toDate).then(function (data) {
                mapData(data);
                mapRate(data);
            });
    });
    var reset = function () {
        $scope.inVicinity = {
            'avg_daily_foot_vicinity': {
                id: 2, cardHeader: 'AVERAGE DAILY FOOTFALL', data: '', rate: '', rateDirection: '', title: 'Total population identified in the selected time period',
                rateTitle: ''
            },
            'total_foot_vicinity': {
                id: 1, cardHeader: 'TOTAL FOOTFALL', data: '', rate: '', rateDirection: '', title: 'Total population identified in the selected time period',
                rateTitle: ''
            }
        };

        $scope.inStore = {
            'rep_30_percent_instore': { id: 5, cardHeader: '% REPEAT IN LAST 30 DAYS', data: '', rate: '', rateDirection: '', unit: '%' },
            'avg_time_instore': { id: 4, cardHeader: 'AVERAGE DAILY TIME SPENT', data: '', rate: '', rateDirection: '', title: 'Average daily time spent by each person in the categories provided below', unit: 'min' },
            'avg_convert_rate': { id: 3, cardHeader: 'AVERAGE CONVERSION RATE', data: '', rate: '', rateDirection: '', unit: '%' },
            'avg_daily_foot_instore': { id: 2, cardHeader: 'AVERAGE DAILY VISITORS', data: '', rate: 23, rateDirection: '' },
            'total_foot_instore': { id: 1, cardHeader: 'TOTAL VISITORS', data: '', rate: '', rateDirection: '' }
        };

        $scope.wifiUsers = {
            'rep_30_percent': { id: 5, cardHeader: '% REPEAT IN LAST 30 DAYS', data: '', rate: '', rateDirection: '', title: 'Percentage of total population that visited more than once in the past 30 days of the selected time period ', unit: '%' },
            'avg_daily_time_spent': { id: 4, cardHeader: 'AVERAGE DAILY TIME SPENT', data: '', rate: '', rateDirection: '', unit: 'min' },
            'avg_daily_data_used': { id: 3, cardHeader: 'AVERAGE DAILY DATA USED', data: '', rate: '', rateDirection: '', title: 'Average daily data consumed by a Wi-Fi user', unit: 'mb' },
            'avg_daily_wifiUsers': { id: 2, cardHeader: 'AVERAGE DAILY USERS', data: '', rate: '', rateDirection: '' },
            'total_wifiUsers': { id: 1, cardHeader: 'TOTAL WIFI USERS', data: '', rate: '', rateDirection: '' }
        };
    };
    reset();
    $rootScope.$broadcast('params-updated');
}])
.controller('trendsController', ['$rootScope', '$scope', 'service', '$compile', 'messageService', function ($rootScope, $scope, service, $compile, messageService) {
    $scope.metrics = [
        { define: 'Total population identified in the selected time period', name: 'Footfall', title: { Days: 'Footfall', Months: 'Average daily footfall' } },
        { define: 'Total population identified in the selected time period', name: Constants.trends_avg_conv_rate, title: { Days: 'Average conversion rate', Months: 'Average daily conversion rate' } },
        { define: 'Average time spent by each person in the categories provided below', name: Constants.trends_avg_time_spent, title: { Days: 'Average time spent per person (in mins)', Months: 'Average daily time spent per person (in mins)' } },
        { define: 'Average data consumed by a Wi-Fi user', name: Constants.trends_avg_data_used, title: { Days: 'Average data used per person (in MB)', Months: 'Average daily data used per person (in MB)' } },
        { define: 'Percentage of total population that visited more than once in the past 30 days of the selected time period ', name: Constants.trends_repeat_30, title: { Days: Constants.trends_repeat_30, Months: Constants.trends_repeat_30 } }];
    $scope.metric = { selected: $scope.metrics[0] };
    $scope.intervals = ['Days', 'Months'];
    $scope.interval = { selected: 'Days' };
    $scope.chartType = 'bar';
    
    var graphs = getTrendGraphs(); //$rootScope.exportGraphItems = graphs;

    var plotGraph = function (graph, type) {
        var details = getGraphDataSet(graph.labels, graph.dataPoints, graph.bgColors);
        var options = graph.options || {};
        graph.options.animation = {
            duration: 1,
            onComplete: function (data) {
                var time = Math.round(+new Date() / 1000);
                var canvas = document.getElementById(graph.graphId);
                var url_base64 = canvas.toDataURL('image/png');
                $('#' + graph.graphId).parent().find('.saveAsJpegButton').attr('href', url_base64);
                $('#' + graph.graphId).parent().find('.saveAsJpegButton').attr('download', time + '.jpeg');
            }
        }
        if (type == 'line') {
            graph.options.lineSegmantation = {
                'segmentationColors': graph.bgColors,
                'dataPoints': graph.dataPoints
            }
        } else {
            graph.options.lineSegmantation = false;
        }
        createGraph(graph.graphId, type, details, options);
    };

    $scope.$on('params-updated', function (event, args) {
        if (!$rootScope.nasid) {
            messageService.showError('Please select a location');
            return;
        }
        
        $('.graphs').empty();
        if (args != null)
            $scope.chartType = args.type;

        $.each(graphs, function (key, graph) {
            if (graph.metric === $scope.metric.selected.name && graph.interval === $scope.interval.selected) {
                var scope = $scope.$new();
                $.each(graph.apis, function (key2, api) {
                    graph.graphId = 'graph' + key;
                    scope.graph = graph;
                    var content = getNewTemplate(graph.graphId, graph.header);
                    $('.graphs').append(content);
                    service[api](graph.mmNas? $rootScope.mmNasId : $rootScope.nasid,
                        $rootScope.dates.fromDate,
                        $rootScope.dates.toDate).then(function (data) {
                            graph.labels = [];
                            graph.dataPoints = [];
                            graph.bgColors = [];
                            graph.mapper(data);
                            plotGraph(graph, $scope.chartType);
                            $('#graph' + key).parent().find('.exportButton').attr('onclick', "exportData(" + JSON.stringify(graph) + ")");
                        });
                   
                });
            }
        });
    });
    $rootScope.$broadcast('params-updated', { type: 'bar' });

    $scope.changeGraphType = function (graphType) {
        $scope.chartType = graphType;
        $rootScope.$broadcast('params-updated', { type: graphType });
    }
}])
.controller('hourlyTrendsController', ['$rootScope', '$scope', 'service', 'messageService', function ($rootScope, $scope, service, messageService) {
    $scope.metrics = [
        { nas: 'mmNasId', api: 'GetMapHourlyTrendsFootfallVicinity', field: 'avg_daily_foot_vicinity', define: 'Average population identified in the vicinity of the selected location', name: 'Footfall in Vicinity' },
        { nas: 'mmNasId', api: 'GetMapHourlyTrendsConversion', field: 'convert_rate', define: 'Percentage of total population in vicinity that entered the selected location', name: 'Conversion Rate' },
        { nas: 'nasid', api: 'GetMapHourlyTrendsWifiUsers', field: 'avg_daily_wifi_users', define: 'Average Wi-Fi users', name: 'Average daily Wi-Fi users' },
        { nas: 'mmNasId', api: 'GetMapHourlyTrendsInstore', field: 'avg_daily_foot_in', define: 'Daily In Store Visiters', name: 'Daily In Store Visiters' }];
    $scope.metric = { selected: $scope.metrics[0] };
    
    var hourSlots = function () {
        return angular.copy([
            { label: '9AM-11AM' },
            { label: '11AM-1PM' },
            { label: '1PM-3PM' },
            { label: '3PM-5PM' },
            { label: '5PM-7PM' },
            { label: '7PM-9PM' },
            { label: '9PM-11PM' },
            { label: '11PM-1AM' }
        ]);
    };

    $scope.hourSlots = hourSlots();
    $scope.days = [
        { hourSlots: hourSlots(), name: 'Monday' },
        { hourSlots: hourSlots(), name: 'Tuesday' },
        { hourSlots: hourSlots(), name: 'Wednesday' },
        { hourSlots: hourSlots(), name: 'Thursday' },
        { hourSlots: hourSlots(), name: 'Friday' },
        { hourSlots: hourSlots(), name: 'Saturday' },
        { hourSlots: hourSlots(), name: 'Sunday' }
    ];
    $scope.exportGraphItems = [];
    var mapData = function (data, field) {
        var maxRadius = 0;
        var minRadius = 9999;
        var biggestRadius = getScreenRadius();
        var smallestRadius = 2;
        angular.forEach($scope.days, function (day, key) {
            angular.forEach(day.hourSlots, function (hour, a) {
                hour.value = 0;
                $('.day.' + day.name + ' .content.' + hour.label + ' circle').attr({
                    r: 0
                });
            });
        });
        $scope.exportGraphItems = [];
        angular.forEach(data, function (value, key) {
            var day = $scope.days[value.day_week - 1];
            var hour = day.hourSlots[value.day_time-1];
            hour.value = value[field];
            if ($scope.metric.selected.field == 'convert_rate') {
                hour.value = formatRate(hour.value) + ' %';
            } else {
                hour.value = formatNumber(hour.value);
            }
            var exportGraphItems = {};
            exportGraphItems.labels = [];
            exportGraphItems.dataPoints = [];
            exportGraphItems.metric = $scope.metric.selected.field;
            exportGraphItems.labels.push(day.name);
            exportGraphItems.dataPoints.push(value[field]);
            $scope.exportGraphItems.push(exportGraphItems);
            maxRadius = (value[field] - maxRadius > 0) ? value[field] : maxRadius;
            minRadius = (value[field] - minRadius < 0) ? value[field] : minRadius;
        });

        angular.forEach(data, function (value, key) {
            var day = $scope.days[value.day_week - 1];
            var hour = day.hourSlots[value.day_time - 1];
            var radius = smallestRadius + ((biggestRadius-smallestRadius) * (value[field] - minRadius) / (maxRadius - minRadius));
            if (value[field] == 0) radius = 0;
            $('.day.' + day.name + ' .content.' + hour.label + ' circle').attr({
                r: radius
            });
            var exportGraphItems = {};
            exportGraphItems.labels = [];
            exportGraphItems.dataPoints = [];
            exportGraphItems.metric = $scope.metric.selected.field;
            exportGraphItems.labels.push(day.name);
            exportGraphItems.dataPoints.push(value[field]);
            $scope.exportGraphItems.push(exportGraphItems);
        });
    };
    $scope.$on('params-updated', function () {
        if (!$rootScope.nasid) {
            messageService.showError('Please select a location');
            return;
        }

        service[$scope.metric.selected.api](
            $rootScope[$scope.metric.selected.nas],
            $rootScope.dates.fromDate,
            $rootScope.dates.toDate).then(function (data) {
                mapData(data, $scope.metric.selected.field);
                $('.exportButton').attr('onclick', "exportData(" + JSON.stringify($scope.exportGraphItems) + ")");
            });
    });
    $rootScope.$broadcast('params-updated');
}])
.controller('loyaltyController', ['$rootScope', '$scope', 'service', '$compile', 'messageService', function ($rootScope, $scope, service, $compile, messageService) {
    $scope.metrics = [
        { define: 'Percentage of total population that visited more than once in the past 7 days of the selected time period', name: 'Last 7 Days' },
        { define: 'Percentage of total population that visited more than once in the past 15 days of the selected time period', name: 'Last 15 Days' },
        { define: 'Percentage of total population that visited more than once in the past 30 days of the selected time period', name: 'Last 30 Days' }];
    $scope.metric = { selected: $scope.metrics[0] };
    $scope.chartType = 'bar';

    var graphs = getLoyalityGraphs(); //$rootScope.exportGraphItems = graphs;
    var plotGraph = function (graph, type) {
        var details = getGraphDataSet(graph.labels, graph.dataPoints, graph.bgColors);
        var options = graph.options || {};
        graph.options.animation = {
            duration: 1,
            onComplete: function (data) {
                var time = Math.round(+new Date() / 1000);
                var canvas = document.getElementById(graph.graphId);
                var url_base64 = canvas.toDataURL('image/png');
                $('#' + graph.graphId).parent().find('.saveAsJpegButton').attr('href', url_base64);
                $('#' + graph.graphId).parent().find('.saveAsJpegButton').attr('download', time + '.jpeg');
            }
        }

        if (type == 'line') {
            graph.options.lineSegmantation = {
                'segmentationColors': graph.bgColors,
                'dataPoints': graph.dataPoints
            }
        } else {
            graph.options.lineSegmantation = false;
        }
        createGraph(graph.graphId, type, details, options);
    };

    $scope.$on('params-updated', function (event, args) {
        if (!$rootScope.nasid) {
            messageService.showError('Please select a location');
            return;
        }
        $('.graphs').empty();

        if (args != null)
            $scope.chartType = args.type;
        $.each(graphs, function (key, graph) {
            if (graph.metric === $scope.metric.selected.name) {
                var scope = $scope.$new();
                $.each(graph.apis, function (key2, api) {
                    graph.graphId = 'graph' + key;
                    scope.graph = graph;
                    var content = getNewTemplate(graph.graphId,graph.header);
                    $('.graphs').append(content);
                    service[api](graph.mmNas ? $rootScope.mmNasId : $rootScope.nasid,
                        $rootScope.dates.fromDate,
                        $rootScope.dates.toDate).then(function (data) {
                            graph.mapper(data);
                            plotGraph(graph, $scope.chartType);
                            $('#graph' + key).parent().find('.exportButton').attr('onclick', "exportData(" + JSON.stringify(graph) + ")");
                        });
                });
            }
        });
    });
    $rootScope.$broadcast('params-updated');

    $scope.changeGraphType = function (graphType) {
        $scope.chartType = graphType;
        $rootScope.$broadcast('params-updated', { type: graphType });
    }
}])
.controller('distributionsController', ['$rootScope', '$scope', 'service', '$compile', 'messageService', function ($rootScope, $scope, service, $compile, messageService) {
    $scope.metrics = [
        { define: 'Distribution of population in every segment, across number of visits made in the past 30 days of the selected time period', name: 'Visit Count' },
        { define: 'Distribution of population across the average daily time spent per person ', name: 'Time Spent' },
        { define: 'Distribution of Wi-Fi users across the average daily data consumed per person', name: 'Data Used' }];
        
    $scope.metric = { selected: $scope.metrics[0] };

    var plotGraph = function (graph) {
        var details = getDistributionDataSet(graph.labels, graph.dataPoints, graph.backgroundColor);
        var options = {
            cutoutPercentage: 70,
            legend: {
                display: true
            },
            tooltips: {
                mode: 'label',
                callbacks: {
                    label: function (tooltipItem, data) {
                        return ' ' + data.datasets[tooltipItem.datasetIndex].data[tooltipItem.index] + ' %';
                    }
                }
            },
            animation: {
                duration: 1,
                onComplete: function (data) {
                    var time = Math.round(+new Date() / 1000);
                    var url_base64 = document.getElementById(graph.graphId).toDataURL('image/png');
                    $('#' + graph.graphId).parent().find('.saveAsJpegButton').attr('href', url_base64);
                    $('#' + graph.graphId).parent().find('.saveAsJpegButton').attr('download', time+'.jpeg');
                }
            }
        }
        createGraph(graph.graphId, 'doughnut', details, options);
    };

    $scope.$on('params-updated', function () {
        if (!$rootScope.nasid) {
            messageService.showError('Please select a location');
            return;
        }
        $('.graphs').empty();
        var graphs = getDistributionGraphs();
        $rootScope.exportGraphItems = graphs;
        $.each(graphs, function (key, graph) {
            if (graph.metric === $scope.metric.selected.name) {
                var scope = $scope.$new();
                graph.graphId = 'graph' + key;
                scope.graph = graph;
                var content = getNewTemplate(graph.graphId, graph.header);
                $('.graphs').append(content);
                $.each(graph.apis, function (key2, api) {
                    service[api](
                        graph.mmNas ? $rootScope.mmNasId : $rootScope.nasid,
                        $rootScope.dates.fromDate,
                        $rootScope.dates.toDate).then(function (data) {
                            graph.mapper.call(graph, data);
                            plotGraph(graph);
                            $('#graph' + key).parent().find('.exportButton').attr('onclick', "exportData(" + JSON.stringify(graph) + ")");
                        });
                });
            }
        });
    });
    $rootScope.$broadcast('params-updated');
}])
.controller('cscController', ['$rootScope', '$scope', 'service', '$compile', 'messageService', function ($rootScope, $scope, service, $compile, messageService) {
    $rootScope.switchLocationSelector({ multiSelect: true });
    $scope.metrics = [
        { title: 'Instore count', define: 'Instore users in selected time period', name: 'Instore count' },
        { title: 'Total Wi-Fi users', define: 'Total Wi-Fi users that connected at the selected location(s)', name: 'Total Wi-Fi users' },
        { title: 'Conversion Rate', define: 'Percentage of population in vicinity that entered the selected location(s)', name: 'Conversion Rate' },
        { title: 'Average daily time spent per person (Instore, in mins)', define: 'Average daily time spent by visitors at the selected location(s)', name: 'Avg Time Spent - In Store User' },
        { title: 'Instore repeat metric in last 30 days', define: 'Percentage of instore visitors that visited the selected location(s) more than once in the past 30 days of the selected time period ', name: '% Instore repeat in last 30 days' }];
    $scope.metric = { selected: $scope.metrics[0] };

    var plotGraph = function (graph) {
        var labels = [];
        var full_labels = [];
        angular.forEach(graph.labels, function (value, key) {
            var item = $rootScope.routerDetails.filter(function (nas) {
                return (nas.nasid == value || nas.mmNasId == value) ? true : false;
            });

            item.length && labels.push(item[0].storeNameAlias);
            full_labels.push(item[0].storeName);
        });
        var details = getComparisonDataSet(labels, graph.dataPoints);
        details.full_labels = full_labels;
        var exportGraphItems = graph;
        exportGraphItems.labels = labels;
        $scope.exportGraphItems = [exportGraphItems];
        graph.options.tooltips = {
            callbacks: {
                title: function (tooltipItem, data) { return data.full_labels[tooltipItem[0].index]; },
                    label: function (tooltipItem, data) {
                        return tooltipItem.yLabel;
                    }
                }
        }
        graph.options.animation = {
            duration: 1,
            onComplete: function (data) {
                var time = Math.round(+new Date() / 1000);
                var canvas = document.getElementById(graph.graphId); 
                var url_base64 = canvas.toDataURL('image/png');
                $('#' + graph.graphId).parent().find('.saveAsJpegButton').attr('href', url_base64);
                $('#' + graph.graphId).parent().find('.saveAsJpegButton').attr('download', time + '.jpeg');
            }
        }
        createGraph(graph.graphId, 'bar', details, graph.options);
    };
    
    $scope.$on('params-updated', function () {
        if (!$rootScope.nasList) {
            messageService.showError('Please select a location');
            return;
        }
        $('.graphs').empty();
        var graphs = getComparisonGraphs();
       
        $.each(graphs, function (key, graph) {
            if (graph.metric === $scope.metric.selected.name) {
                var scope = $scope.$new();
                $.each(graph.apis, function (key2, api) {
                    service[api](graph.mmNas ? $rootScope.mmNasList : $rootScope.nasList,
                        $rootScope.dates.fromDate,
                        $rootScope.dates.toDate).then(function (data) {
                            graph.mapper(data);
                            graph.graphId = 'graph' + key;
                            scope.graph = graph;
                            var content = $compile(getGraphTemplate())(scope);
                            $('.graphs').append(content);
                            plotGraph(graph);
                            $('#graph' + key).parent().find('.exportButton').attr('onclick', "exportData(" + JSON.stringify($scope.exportGraphItems) + ")");
                        });
                });
            }
        });
    });

    $rootScope.$broadcast('params-updated');
}])
.controller('cspController', ['$rootScope', '$scope', 'service', '$compile', 'messageService', function ($rootScope, $scope, service, $compile, messageService) {
    $rootScope.switchLocationSelector({ multiSelect: true });
    $scope.metrics = [
        { title: 'Conversion rate', define: '', name: 'Conversion rate' }];
    $scope.metric = { selected: $scope.metrics[0] };

    var plotGraph = function (graph) {
        var labels = [];
        var full_labels = [];
        angular.forEach(graph.labels, function (value, key) {
            var item = $rootScope.routerDetails.filter(function (nas) {
                return (nas.nasid == value || nas.mmNasId == value) ? true : false;
            });
            item.length && full_labels.push(item[0].storeName);
            item.length && labels.push(item[0].storeNameAlias);
        });
        
        var details = getComparisonDataSetForScatter(labels, graph.dataPoints, graph.bgColors, graph.bgBorder);
        var exportGraphItems = graph;
        exportGraphItems.labels = labels;
        angular.forEach(exportGraphItems.dataPoints, function (value, key) {
            var dataPointOjb = {};
            dataPointOjb.conversion_rate = value.y;
            dataPointOjb.daily_footfall = value.x;
            exportGraphItems.dataPoints[key] = dataPointOjb;
        });
        details.full_labels = full_labels;
        $scope.exportGraphItems = [exportGraphItems];
        createScatterGraph(graph.graphId, 'bubble', details, graph.xAvg, graph.yAvg);
    };

    $scope.$on('params-updated', function () {
        if (!$rootScope.nasList) {
            messageService.showError('Please select a location');
            return;
        }
        $('.graphs').empty();
  
        var graphs = getCSPGraphs();
        $.each(graphs, function (key, graph) {
            if (graph.metric === $scope.metric.selected.name) {
                var scope = $scope.$new();
                $.each(graph.apis, function (key2, api) {
                    service[api](graph.mmNas ? $rootScope.mmNasList : $rootScope.nasList,
                        $rootScope.dates.fromDate,
                        $rootScope.dates.toDate).then(function (data) {
                            graph.mapper(data);
                            graph.graphId = 'graph' + key;
                            scope.graph = graph;
                            var content = $compile(getGraphTemplate())(scope);
                            $('.graphs').append(content);
                            plotGraph(graph);
                            $('#graph' + key).parent().find('.exportButton').attr('onclick', "exportData(" + JSON.stringify($scope.exportGraphItems) + ")");
                        });
                });
            }
        });
    });

    $rootScope.$broadcast('params-updated');
}]);