analyticsApp
.service('messageService', ['$rootScope', '$timeout', function ($rootScope, $timeout) {
	var showMessage = function (text, type, timeout) {
		timeout = timeout || 5000;
		$('.messege .' + type).text(text)
		$('.messege .' + type).addClass('appear');
		$('.messege').addClass('popup');
		$timeout(function () {
			$('.messege .' + type).text('');
			$('.messege .' + type).remove('appear');
			$('.messege').removeClass('popup');
		}, timeout);
	}

	this.showError = function (text, timeout) {
		showMessage(text, 'error', timeout);
	}

	this.showSuccess = function (text, timeout) {
		showMessage(text, 'success', timeout);
	}

	this.showInfo = function (text, timeout) {
		showMessage(text, 'info', timeout);
	}
}])
.service('ajaxCall', ['$http', '$state', '$q', 'messageService',
	function ($http, $state, $q, messageService) {
		var count = 0;
		var makeCall = function (options) {
			typeof options.showLoader === "undefined" ? options.showLoader = true : options.showLoader = false;
			count++;
			var deferred = $q.defer();
			if (options.showLoader) $('#loader').show();
			$http(options).
			success(function (response) {
				count--;
				if (count == 0)
					$('#loader').hide();
				if (response && response.status === 1) {
					messageService.showError(response.msg);
					deferred.reject();
				}
				else
					deferred.resolve(response);
			}).error(function () {
				count--;
				if (count == 0)
					$('#loader').hide();
				messageService.showError('ERROR! Please try after some time');
			});
			return deferred.promise;
		}
		this.get = function (url, loading) {
			return makeCall({ method: 'GET', url: url, cache: false, showLoader: loading });
		}
		this.post = function (url, data, loading) {
			return makeCall({ method: 'POST', data: data, url: url, showLoader: loading });
		}
}])
.service('service', ['ajaxCall', function (ajaxCall) {
	this.whichUser = function () {
		return ajaxCall.get('/Admin/GetUser').then(function (response) {
			return response.data;
		});
	};

	this.demofizeStores = function (stores) {
	    return stores.filter(function (store) {
	        store.storeName = 'Demo Outlet';
	        store.contactPerson = 'Demo Store Keeper';
	        store.contactNumber = 'Demo Store Keeper';
	        return true;
	    });
	}

	this.GetMMNas = function () {
		return ajaxCall.get('/AdminPortal/GetMMNas').then(function (response) {
			return response.data;
		});
	};

	this.logout = function () {
		ajaxCall.get('/Admin/Logout').then(function () {
			window.location.href = "/AdminPortal/Index";
		});
	};

	this.GetActiveDays = function (nasid, from, to) {
	    return ajaxCall.get(getAnalyticsPortalQuery('GetActiveDays', nasid, from, to))
			.then(function (response) { return response.data; });
	};

	this.GetMapSummaryWifi = function (nasid, from, to) {
		return ajaxCall.get(getAnalyticsPortalQuery('GetMapSummaryWifi', nasid, from, to))
			.then(function (response) { return response.data; });
	};

	this.GetMapSummaryFootfall = function (nasid, from, to) {
		return ajaxCall.get(getAnalyticsPortalQuery('GetMapSummaryFootfall', nasid, from, to))
			.then(function (response) { return response.data; });
	};

	// ----------overview apis------
	this.GetDeltaDates = function (from, to) {
		return ajaxCall.get(getAnalyticsPortalQuery('GetOverviewDates', null, from, to))
			.then(function (response) { return response.data; });
	};

	this.GetMapOverviewUsers = function (nasid, from, to) {
		return ajaxCall.get(getAnalyticsPortalQuery('GetMapOverviewUsers', nasid, from, to), false)
			.then(function (response) { return response.data; });
	};

	this.GetMapOverviewRepeatUsers = function (nasid, from, to) {
		return ajaxCall.get(getAnalyticsPortalQuery('GetMapOverviewRepeatUsers', nasid, from, to), false)
			.then(function (response) { return response.data; });
	};

	this.GetMapOverviewFootfallVicinity = function (nasid, from, to) {
		return ajaxCall.get(getAnalyticsPortalQuery('GetMapOverviewFootfallVicinity', nasid, from, to), false)
			.then(function (response) { return response.data; });
	};

	this.GetMapOverviewFootfallInstore = function (nasid, from, to) {
		return ajaxCall.get(getAnalyticsPortalQuery('GetMapOverviewFootfallInstore', nasid, from, to), false)
			.then(function (response) { return response.data; });
	};

	this.GetMapOverviewFootfallConversion = function (nasid, from, to) {
		return ajaxCall.get(getAnalyticsPortalQuery('GetMapOverviewFootfallConversion', nasid, from, to), false)
			.then(function (response) { return response.data; });
	};

	this.GetMapOverviewFootfallInstoreRepeat = function (nasid, from, to) {
		return ajaxCall.get(getAnalyticsPortalQuery('GetMapOverviewFootfallInstoreRepeat', nasid, from, to), false)
			.then(function (response) { return response.data; });
	};
	// ----------overview apis end------
	
	// ----------trends api------
	this.GetMapTrendsDailyFootfall = function (nasid, from, to) {
		return ajaxCall.get(getAnalyticsPortalQuery('GetMapTrendsDailyFootfall', nasid, from, to))
			.then(function (response) { return response.data; });
	};
	
	this.GetMapTrendsMonthlyFootfallInstore = function (nasid, from, to) {
		return ajaxCall.get(getAnalyticsPortalQuery('GetMapTrendsMonthlyFootfallInstore', nasid, from, to))
			.then(function (response) { return response.data; });
	};
	
	this.GetMapTrendsMonthlyFootfallInstoreRepeat = function (nasid, from, to) {
		return ajaxCall.get(getAnalyticsPortalQuery('GetMapTrendsMonthlyFootfallInstoreRepeat', nasid, from, to))
			.then(function (response) { return response.data; });
	};
	
	this.GetMapTrendsMonthlyFootfallVicinity = function (nasid, from, to) {
		return ajaxCall.get(getAnalyticsPortalQuery('GetMapTrendsMonthlyFootfallVicinity', nasid, from, to))
			.then(function (response) { return response.data; });
	};

	this.GetMapTrendsMonthlyFootfallRepeatVicinity = function (nasid, from, to) {
		return ajaxCall.get(getAnalyticsPortalQuery('GetMapTrendsMonthlyFootfallRepeatVicinity', nasid, from, to))
			.then(function (response) { return response.data; });
	};
	
	this.GetMapTrendsMonthlyFootfallPasserby = function (nasid, from, to) {
		return ajaxCall.get(getAnalyticsPortalQuery('GetMapTrendsMonthlyFootfallPasserby', nasid, from, to))
			.then(function (response) { return response.data; });
	};
	
	this.GetMapTrendsMonthlyFootfallRepeatPasserby = function (nasid, from, to) {
		return ajaxCall.get(getAnalyticsPortalQuery('GetMapTrendsMonthlyFootfallRepeatPasserby', nasid, from, to))
			.then(function (response) { return response.data; });
	};
	
	this.GetMapTrendsMonthlyFootfallConversion = function (nasid, from, to) {
		return ajaxCall.get(getAnalyticsPortalQuery('GetMapTrendsMonthlyFootfallConversion', nasid, from, to))
			.then(function (response) { return response.data; });
	};
	
	this.GetMapTrendsMonthlyUsers = function (nasid, from, to) {
		return ajaxCall.get(getAnalyticsPortalQuery('GetMapTrendsMonthlyUsers', nasid, from, to))
			.then(function (response) { return response.data; });
	};
	
	this.GetMapTrendsMonthlyRepeatUsers = function (nasid, from, to) {
		return ajaxCall.get(getAnalyticsPortalQuery('GetMapTrendsMonthlyRepeatUsers', nasid, from, to))
			.then(function (response) { return response.data; });
	};

	this.GetMapTrendsMonthlyFootfallTimeInstore = function (nasid, from, to) {
		return ajaxCall.get(getAnalyticsPortalQuery('GetMapTrendsMonthlyFootfallTimeInstore', nasid, from, to))
			.then(function (response) { return response.data; });
	};

	this.GetMapTrendsDailyUsers = function (nasid, from, to) {
		return ajaxCall.get(getAnalyticsPortalQuery('GetMapTrendsDailyUsers', nasid, from, to))
			.then(function (response) { return response.data });
	};
	
	// ----------trends api end------

	//------------hourly trends api -----------
	this.GetMapHourlyTrendsWifiUsers = function (nasid, from, to) {
		return ajaxCall.get(getAnalyticsPortalQuery('GetMapHourlyTrendsWifiUsers', nasid, from, to))
			.then(function (response) { return response.data });
	};

	this.GetMapHourlyTrendsConversion = function (nasid, from, to) {
		return ajaxCall.get(getAnalyticsPortalQuery('GetMapHourlyTrendsConversion', nasid, from, to))
			.then(function (response) { return response.data });
	};

	this.GetMapHourlyTrendsFootfallVicinity = function (nasid, from, to) {
		return ajaxCall.get(getAnalyticsPortalQuery('GetMapHourlyTrendsFootfallVicinity', nasid, from, to))
			.then(function (response) { return response.data });
	};

    this.GetMapHourlyTrendsInstore = function (nasid, from, to) {
	    return ajaxCall.get(getAnalyticsPortalQuery('GetMapHourlyTrendsInstore', nasid, from, to))
			.then(function (response) { return response.data });
	};

	//------------hourly trends end ------------

	//----------lotality apis------
	this.GetMapUserLoyalty = function (nasid, from, to) {
		return ajaxCall.get(getAnalyticsPortalQuery('GetMapUserLoyalty', nasid, from, to))
			.then(function (response) { return response.data });
	};
	
	this.GetMapLoyaltyFootfallRepeat = function (nasid, from, to) {
		return ajaxCall.get(getAnalyticsPortalQuery('GetMapLoyaltyFootfallRepeat', nasid, from, to))
			.then(function (response) { return response.data });
	};
	//----------lotality apis ends------

	//----------------distribution apis
	this.GetMapVisitDistribution = function (nasid, from, to) {
		return ajaxCall.get(getAnalyticsPortalQuery('GetMapVisitDistribution', nasid, from, to))
			.then(function (response) { return response.data });
	};

	this.GetMapTimeDistribution = function (nasid, from, to) {
		return ajaxCall.get(getAnalyticsPortalQuery('GetMapTimeDistribution', nasid, from, to))
			.then(function (response) { return response.data });
	};

	this.GetMapDataDistribution = function (nasid, from, to) {
		return ajaxCall.get(getAnalyticsPortalQuery('GetMapDataDistribution', nasid, from, to))
			.then(function (response) { return response.data });
	};

	this.GetMapDistributionFootfallVisitInstore = function (nasid, from, to) {
		return ajaxCall.get(getAnalyticsPortalQuery('GetMapDistributionFootfallVisitInstore', nasid, from, to))
			.then(function (response) { return response.data });
	};

	this.GetMapDistributionFootfallTimeInstore = function (nasid, from, to) {
		return ajaxCall.get(getAnalyticsPortalQuery('GetMapDistributionFootfallTimeInstore', nasid, from, to))
			.then(function (response) { return response.data });
	};

	this.GetMapDistributionFootfallVisitPasserby = function (nasid, from, to) {
		return ajaxCall.get(getAnalyticsPortalQuery('GetMapDistributionFootfallVisitPasserby', nasid, from, to))
			.then(function (response) { return response.data });
	};

	this.GetMapDistributionFootfallTimePasserby = function (nasid, from, to) {
		return ajaxCall.get(getAnalyticsPortalQuery('GetMapDistributionFootfallTimePasserby', nasid, from, to))
			.then(function (response) { return response.data });
	};

	this.GetMapDistributionFootfallVisitVicinity = function (nasid, from, to) {
		return ajaxCall.get(getAnalyticsPortalQuery('GetMapDistributionFootfallVisitVicinity', nasid, from, to))
			.then(function (response) { return response.data });
	};

	this.GetMapDistributionFootfallTimeVicinity = function (nasid, from, to) {
		return ajaxCall.get(getAnalyticsPortalQuery('GetMapDistributionFootfallTimeVicinity', nasid, from, to))
			.then(function (response) { return response.data });
	};
	//--------------- distribution ends


	//-----------------comparison apis
	this.GetMapStoreComparisonUsers = function (nasids, from, to) {
		return ajaxCall.get(getComparisonQuery('GetMapStoreComparisonUsers', createNasList(nasids), from, to))
			.then(function (response) { return response.data });
	};

	this.GetMapStoreComparisonRepeatUsers = function (nasids, from, to) {
		return ajaxCall.get(getComparisonQuery('GetMapStoreComparisonRepeatUsers', createNasList(nasids), from, to))
			.then(function (response) { return response.data });
	};

	this.GetMapStoreComparisonFootfallConversion = function (nasids, from, to) {
		return ajaxCall.get(getComparisonQuery('GetMapStoreComparisonFootfallConversion', createNasList(nasids), from, to))
			.then(function (response) { return response.data });
	};

	this.GetMapStoreComparisonFootfallInstore = function (nasids, from, to) {
		return ajaxCall.get(getComparisonQuery('GetMapStoreComparisonFootfallInstore', createNasList(nasids), from, to))
			.then(function (response) { return response.data });
	};
    //-----------------comparison apis end

    /***** Store Performance APIs **************/

	this.GetStorePerformanceConversionRate = function (nasids, from, to) {
	    return ajaxCall.get(getComparisonQuery('GetStorePerformanceConversionRate', createNasList(nasids), from, to))
			.then(function (response) { return response.data });
	};

	this.GetStorePerformanceAverageConversionRate = function (nasids, from, to) {
	    return ajaxCall.get(getComparisonQuery('GetStorePerformanceAverageConversionRate', createNasList(nasids), from, to))
			.then(function (response) { return response.data });
	};
}])
;