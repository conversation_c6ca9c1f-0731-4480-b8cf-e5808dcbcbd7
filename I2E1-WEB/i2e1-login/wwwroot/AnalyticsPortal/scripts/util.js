var newSelectAllItem = {
    "deviceId": null,
    "mmNasId": -1,
    "lastPingDelay": null,
    "active": false,
    "smsLimit": 0,
    "routerState": 0,
    "nasid": -1,
    "controllerid": 1,
    "storeName": "Select All",
    "storeNameAlias": '',
    "address": '',
    "location": '',
    "latitude": '',
    "longitude": '',
    "city": '',
    "state": '',
    "category": '',
    "contactPerson": '',
    "contactNumber": '',
    "emailId": '',
    "internetBillingStartDay": 0,
    "internetPlan": 0,
    "uploadKpbs": 0,
    "downloadKbps": 0
};

String.prototype.replaceAll = function (search, replacement) {
	var target = this;
	return target.replace(new RegExp(search, 'g'), replacement);
};
String.prototype.isEmpty = function () {
	return (this.length === 0 || !this.trim());
};
var formatNumber = function (number) {
    var x1 = number || this;
    var rgx = /(\d+)(\d{3})/;
    while (rgx.test(x1)) {
        x1 = x1.toString().replace(rgx, '$1' + ',' + '$2');
    }
    x1 += '';
    return x1 ? x1 : 0;
};

String.prototype.formatNumber = formatNumber
var formatData = function (number, format) {
    function round(value, precision) {
        var multiplier = Math.pow(10, precision || 0);
        return Math.round(value * multiplier) / multiplier;
    }
    number = Math.abs(number);
    if (format != null) {
        switch (format) {
            case 'whole':
                number = round(number, 0);
                break;
            case 'tinyreal':
                number = round(number, 1);
                break;
            case 'smallreal':
                number = round(number, 2);
                break;
        }
    } else {
        number = Math.ceil(number * 10) / 10;
    }
    return number;
}
var formatRate = function (number) {
    number = Math.abs(number);
    number = Math.ceil(number * 10) / 10;
    return number;
}
var getMMNas = function (nases, primaryNas) {
    var mmNas = 0;
    mmNas = nases.filter(function (nas) {
        if (nas.nasid == primaryNas) {
            return true;
        }
    })[0].mmNasId;
    return mmNas;
};


var Constants = {
    dateFormat: 'DD MMM YYYY',
    competeDateFormat: 'DD-MMM-YYYY : hh:mm',
    completeCielDate: 'DD-MMM-YYYY : 23:59',
    completeFloorDate: 'DD-MMM-YYYY : 00:00',
    backendDateFormat: 'YYYY, MM, DD',
    trends_repeat_30: 'Repeat metric',
    trends_avg_data_used: 'Average Data Used',
    trends_avg_time_spent: 'Average Time Spent',
    trends_avg_conv_rate: 'Average Conversion Rate'
    
};

var detectScreen = function () {
    var screens = $('.screen-detector span');
    var currentScreen = 'normal';
    $.each(screens, function (key, screen) {
        if ($(screen).css('opacity') == 1) {
            currentScreen = screen.className;
        }
    });

    return currentScreen;
};

var getScreenRadius = function () {
    var screen = detectScreen();
    var radius = 24;
    switch (screen) {
        case "normal": radius = 24; break;
        case "tablets": radius = 15; break;
        case "smartphones": radius = 15; break;
    }
    
    return radius;
};

var horizonalLinePlugin = {
    afterDraw: function (chartInstance) {
        var yScale = chartInstance.scales["y-axis-0"];
        var canvas = chartInstance.chart;
        var ctx = canvas.ctx;
        var index;
        var line;
        var style;

        if (chartInstance.options.horizontalLine) {
            for (index = 0; index < chartInstance.options.horizontalLine.length; index++) {
                line = chartInstance.options.horizontalLine[index];

                if (!line.style) {
                    style = "#ff0000";
                } else {
                    style = line.style;
                }

                if (line.y) {
                    yValue = yScale.getPixelForValue(line.y);
                } else {
                    yValue = 0;
                }

                ctx.lineWidth = .5;

                if (yValue) {
                    ctx.beginPath();
                    ctx.setLineDash([5, 5]);
                    ctx.moveTo(0, yValue);
                    ctx.lineTo(canvas.width, yValue);
                    ctx.font = Chart.helpers.fontString(Chart.defaults.global.defaultFontSize, Chart.defaults.global.defaultFontStyle, Chart.defaults.global.defaultFontFamily);
                    ctx.textAlign = 'right';
                    ctx.fillStyle = 'rgb(124, 124, 124)';
                    ctx.textBaseline = 'top';
                    ctx.strokeStyle = style;
                    ctx.stroke();
                }

                if (line.text) {
                    ctx.fillStyle = line.text_color;
                    ctx.fillText(line.text, canvas.width, yValue + 10);
                }
            }
            return;
        };
    }
};
var verticalLinePlugin = {
    afterDraw: function (chartInstance) {
        var xScale = chartInstance.scales["x-axis-0"];
        var canvas = chartInstance.chart;
        var ctx = canvas.ctx;
        var index;
        var line;
        var style;

        if (chartInstance.options.verticalLine) {
            for (index = 0; index < chartInstance.options.verticalLine.length; index++) {
                line = chartInstance.options.verticalLine[index];

                if (!line.style) {
                    style = "#ff0000";
                } else {
                    style = line.style;
                }

                if (line.x) {
                    xValue = xScale.getPixelForValue(line.x);
                } else {
                    xValue = 0;
                }

                ctx.lineWidth = .5;
                if (xValue) {
                    ctx.beginPath();
                    ctx.setLineDash([5, 5]);
                    ctx.moveTo(xValue, 0);
                    ctx.lineTo(xValue, canvas.height);
                    ctx.font = Chart.helpers.fontString(Chart.defaults.global.defaultFontSize, Chart.defaults.global.defaultFontStyle, Chart.defaults.global.defaultFontFamily);
                    ctx.textAlign = 'left';
                    ctx.fillStyle = 'rgb(124, 124, 124)';
                    ctx.textBaseline = 'top';
                    ctx.strokeStyle = style;
                    ctx.stroke();
                }

                if (line.text) {
                    ctx.fillStyle = line.text_color;
                    ctx.fillText(line.text, xValue + 5, 5);
                }
            }
            return;
        };
    }
};

var lineSegmantation = {
    afterDraw: function (chartInstance) {
        var xScale = chartInstance.scales["x-axis-0"];
        var yScale = chartInstance.scales["y-axis-0"];
        var canvas = chartInstance.chart;
        var ctx = canvas.ctx;
        if (chartInstance.options.lineSegmantation) {
            bgColors = chartInstance.options.lineSegmantation.segmentationColors;
            dataPoints = chartInstance.options.lineSegmantation.dataPoints;
            previousValue = {};
            previousValue.x = null;
            previousValue.y = null;
            Chart.helpers.each(chartInstance.data.datasets.forEach(function (dataset, i) {
                var meta = chartInstance.getDatasetMeta(i);
                Chart.helpers.each(meta.data.forEach(function (bar, index) {
                    xValue = bar._model.x;
                    yValue = bar._model.y;
                    if (previousValue.x !== null && previousValue.y !== null) {
                        ctx.lineWidth = 1;
                        ctx.beginPath();
                        ctx.moveTo(previousValue.x, previousValue.y);
                        ctx.lineTo(xValue, yValue);
                        ctx.fillStyle = bgColors[index-1];
                        ctx.strokeStyle = bgColors[index-1];
                        ctx.stroke();
                    }
                    previousValue.x = xValue;
                    previousValue.y = yValue;
                }), chartInstance)
            }), chartInstance);
            return;
        }
    }
}

Chart.pluginService.register(horizonalLinePlugin);
Chart.pluginService.register(verticalLinePlugin);
Chart.pluginService.register(lineSegmantation);

var createGraph = function (graphId, type, data, options) {
    setTimeout(function () {
        chart = new Chart($('#' + graphId), {
            type: type,
            data: data,
            options: options || {}
        });
    }, 500);
};

var textWidth = function (text) {
    var html_calc = '<span>' + text + '</span>';
    $('body').append(html_calc);    
    var width = $(html_calc).find('span:first').width();
    return width;
};

var createScatterGraph = function (graphId, type, data, xAvg, yAvg) {
    var graphData = data;
    var unit = '%';
    var formaty = null;
    setTimeout(function () {
        chart = new Chart($('#' + graphId), {
            type: type,
            data: data,
            options: {
                animation: {
                    duration: 1,
                    onProgress: function (data) {
                        var chartInstance = this.chart,
                        ctx = chartInstance.ctx;
                        ctx.font = Chart.helpers.fontString(Chart.defaults.global.defaultFontSize, Chart.defaults.global.defaultFontStyle, Chart.defaults.global.defaultFontFamily);
                        ctx.textAlign = 'left';
                        ctx.fillStyle = 'rgb(124, 124, 124)';
                        ctx.textBaseline = 'bottom';
                        canvasWidth = chartInstance.canvas.getBoundingClientRect().width;
                        this.data.datasets.forEach(function (dataset, i) {
                            var meta = chartInstance.controller.getDatasetMeta(i);
                            meta.data.forEach(function (bar, index) {
                                label = graphData.labels[index];
                                var labelWidth = ctx.measureText(label).width;
                                if (bar._model.x + labelWidth + 50 > canvasWidth)
                                    ctx.fillText(label, bar._model.x - 15 - labelWidth, bar._model.y + 7);
                                else
                                    ctx.fillText(label, bar._model.x + 15, bar._model.y + 7);
                            });
                        });
                        var time = Math.round(+new Date() / 1000);
                        var canvas = document.getElementById(graphId);
                        var url_base64 = canvas.toDataURL('image/png');
                        $('#' + graphId).parent().find('.saveAsJpegButton').attr('href', url_base64);
                        $('#' + graphId).parent().find('.saveAsJpegButton').attr('download', time + '.jpeg');
                    }
                },
                scales: {
                    yAxes: [{
                        scaleLabel: {
                            display: true,
                            labelString: 'Conversion Rate'
                        },
                        ticks: {
                            beginAtZero: true,
                            min: 0,
                            callback: function (value, index, values) {
                                value += '';
                                formaty && (value = formaty(value));
                                value = value.indexOf('.') !== -1 ? formatRate(value) : value;
                                return unit ? value + ' ' + unit : value;
                            }
                        },
                        gridLines: {
                            display: false
                        }
                    }],
                    xAxes: [{
                        scaleLabel: {
                            display: true,
                            labelString: 'Average Daily Footfall Vicinity'
                        },
                        gridLines: {
                            display: false
                        }
                    }]
                },
                "horizontalLine": [{
                    "y": yAvg * 100,
                    "style": "#a29d9d",
                    "text": "Avg Conversion Rate",
                    "text_color": "#a29d9d"
                }],
                "verticalLine": [{
                    "x": xAvg,
                    "style": "#a29d9d",
                    "text": "Avg Daily Footfall",
                    "text_color": "#a29d9d"
                }],
                tooltips: {
                    callbacks: {
                        label: function (tooltipItem, data) {
                            var value = data.datasets[tooltipItem.datasetIndex].data[tooltipItem.index];
                            var location = data.full_labels[tooltipItem.index];
                            return location + ' (Conversion rate : ' + formatData(tooltipItem.yLabel, 'tinyreal') + ' %, ' + ' Footfall Vicinity : ' + formatNumber(tooltipItem.xLabel) + ')';
                        }
                    },
                    enabled: false,
                    custom: customTooltips
                }
            }
        });
    }, 500);
};

var customTooltips = function (tooltip) {

    // Tooltip Element
    var tooltipEl = $('#chartjs-tooltip');

    if (!tooltipEl[0]) {
        $('body').append('<div id="chartjs-tooltip"></div>');
        tooltipEl = $('#chartjs-tooltip');
    }

    // Hide if no tooltip
    if (!tooltip.opacity) {
        tooltipEl.css({
            opacity: 0
        });
        $('.chartjs-wrap canvas')
          .each(function (index, el) {
              $(el).css('cursor', 'default');
          });
        return;
    }

    $(this._chart.canvas).css('cursor', 'pointer');

    // Set caret Position
    tooltipEl.removeClass('above below no-transform');
    if (tooltip.yAlign) {
        tooltipEl.addClass(tooltip.yAlign);
    } else {
        tooltipEl.addClass('no-transform');
    }

    // Set Text
    if (tooltip.body) {
        var innerHtml = [
          (tooltip.beforeTitle || []).join('\n'), (tooltip.title || []).join('\n'), (tooltip.afterTitle || []).join('\n'), (tooltip.beforeBody || []).join('\n'), (tooltip.body || []).join('\n'), (tooltip.afterBody || []).join('\n'), (tooltip.beforeFooter || [])
          .join('\n'), (tooltip.footer || []).join('\n'), (tooltip.afterFooter || []).join('\n')
        ];
        tooltipEl.html(innerHtml.join('\n'));
    }

    // Find Y Location on page
    var top = 0;
    if (tooltip.yAlign) {
        if (tooltip.yAlign == 'above') {
            top = tooltip.y - tooltip.caretHeight - tooltip.caretPadding;
        } else {
            top = tooltip.y + tooltip.caretHeight + tooltip.caretPadding;
        }
    }

    var position = $(this._chart.canvas)[0].getBoundingClientRect();
    // Display, position, and set styles for font
    tooltipEl.css({
        opacity: 1,
        width: tooltip.width ? (tooltip.width + 'px') : 'auto',
        left: event.pageX + 'px',
        top:position.top + tooltip.y - 30 + 'px',
        fontFamily: tooltip._fontFamily,
        fontSize: '10px',
        fontStyle: tooltip._fontStyle,
        padding: tooltip.yPadding + 'px ' + tooltip.xPadding + 'px',
    });
};

var getGraphTemplate = function () {
    return '<div class="graph-container">' +
        '<div class="graph-header"><h6>{{graph.header}}</h6>' +
        '<div class="dropdown">' +
        '<button state="menu" type="button" title="More Options" class="dropdown-toggle" data-toggle="dropdown" href="#"><i class="fa fa-bars" aria-hidden="true"></i></button>' +
        '<ul class="dropdown-menu pull-right">' +
        '<li><a href="javascript:void(0)" class="saveAsJpegButton">Save as JPEG</a></li>' +
        '<li><a href="javascript:void(0)" class="exportButton">Export as CSV</a></li>' +
        '</ul>' +
        '</div>' +
        '</div>' +
		'<canvas class="graph" id="{{graph.graphId}}"></canvas>' +
		'</div>';
};

var getAnalyticsPortalQuery = function (api, nasid, from, to) {
	var query = '/Analytics/' + api;
	query += nasid ? '?nasid=' + nasid + '&' : '?';
	query += 'startTime=' + from.format(Constants.backendDateFormat);
	query += '&endTime=' + to.format(Constants.backendDateFormat);
	return query;
};

var getComparisonQuery = function (api, nasids, from, to) {
	var query = '/Analytics/' + api;
	query += '?nasids=' + nasids + '&';
	query += 'startTime=' + from.format(Constants.backendDateFormat);
	query += '&endTime=' + to.format(Constants.backendDateFormat);
	return query;
};

var getComparisonDataSetForScatter = function (labels, data, bgColors, bgBorder) {
    Chart.defaults.global.legend.display = false;
    return angular.copy({
        labels: labels,
        datasets: [
			{
			    backgroundColor: bgColors,
			    borderColor: bgBorder,
			    borderWidth: 1,
			    hoverBackgroundColor: "rgba(75,192,192,0.4)",
			    hoverBorderColor: "rgba(75,192,192,1)",
			    data: data,
			}
        ]
    });
};

var getComparisonDataSet = function (labels, data) {
    Chart.defaults.global.legend.display = false;
	return angular.copy({
		labels: labels,
		datasets: [
			{
			    backgroundColor: "rgba(75,192,192,0.6)",
			    borderColor: "rgba(75,192,192,1)",
				borderWidth: 1,
				hoverBackgroundColor: "rgba(75,192,192,0.4)",
				hoverBorderColor: "rgba(75,192,192,1)",
				data: data,
			}
		]
	});
};

var getGraphDataSet = function (labels, data, bgColors) {
    Chart.defaults.global.legend.display = false;
    return angular.copy({
        labels: labels,
        datasets: [
			{
			    fill: false,
			    lineTension: 0.1,
			    backgroundColor: bgColors,
			    borderColor: bgColors,
			    borderCapStyle: 'butt',
			    borderDash: [],
			    borderDashOffset: 0.0,
			    borderJoinStyle: 'miter',
			    pointBorderColor: bgColors,
			    pointBackgroundColor: bgColors,
			    pointBorderWidth: 1,
			    pointHoverRadius: 5,
			    pointHoverBackgroundColor: bgColors,
			    pointHoverBorderColor: bgColors,
			    pointHoverBorderWidth: 2,
			    pointRadius: 2,
			    pointHitRadius: 10,
			    data: data
			}]
    });
};

var getGraphOptions = function (options) {
    options = options || {};
    var unit = options.unit;
    var formaty = options.formaty;
    return {
        tooltips: {
            callbacks: {
                label: function (tooltipItem, data) {
                    var value = data.datasets[tooltipItem.datasetIndex].data[tooltipItem.index];
                    formaty && (value = formaty(value));
                    if (unit === '%') value = formatRate(value);
                    if (unit === 'min') value = formatData(value, 'whole');
                    return unit ? value + ' ' + unit : value;
                }
            }
        },
        scales: {
            yAxes: [{
                ticks: {
                    beginAtZero: true,
                    min: 0,
                    callback: function (value, index, values) {
                        value += '';
                        formaty && (value = formaty(value));
                        value = value.indexOf('.') !== -1 ? formatRate(value) : value;
                        return unit ? value + ' ' + unit : value;
                    }
                }
            }]
        }
    }
}

var yAxesBeginAtZero = function () {
    return {
        yAxes: [{
            ticks: {
                beginAtZero: true,
                min: 0
            }
        }]
    }
}

var CSCxAxexCorrection = function (dataArray) {
    return {
        xAxes: [{
            ticks: {
                callback: function (value, index, values) {
                    return value + ' %';
                }
            }
        }]
    }
};

var getTrendGraphs = function () {
    return [{
        metric: 'Footfall', interval: 'Days',
        header: 'In Vicinity', apis: ['GetMapTrendsDailyFootfall'],
        mapper: function (data) {
            keyedValueMapper.call(this, data, 'avg_daily_foot_vicinity');
        },
        labels: [], dataPoints: [], bgColors: [], mmNas: true, options: getGraphOptions({ formaty: String.prototype.formatNumber }),
        define: "Footfall in the location's vicinity"
    }, {
        metric: 'Footfall', interval: 'Days',
        header: 'In Store', apis: ['GetMapTrendsDailyFootfall'],
        mapper: function (data) {
            keyedValueMapper.call(this, data, 'avg_daily_foot_instore');
        },
        labels: [], dataPoints: [],  bgColors: [], mmNas: true, options: getGraphOptions({ formaty: String.prototype.formatNumber }),
        define: "People inside location"
    }, {
        metric: 'Footfall', interval: 'Days',
        header: 'Passer by', apis: ['GetMapTrendsDailyFootfall'],
        mapper: function (data) {
            keyedValueMapper.call(this, data, 'avg_daily_foot_passerby');
        },
        labels: [], dataPoints: [], bgColors: [], mmNas: true, options: getGraphOptions({ formaty: String.prototype.formatNumber }),
        define: "People passed by location"
    }, {
        metric: 'Footfall', interval: 'Days',
        header: 'Wifi Users', apis: ['GetMapTrendsDailyUsers'],
        mapper: function (data) {
            for (var i = 0; i < data.length; i++) {
                this.labels.push(data[i].label);
                this.dataPoints.push(data[i].avg_footfall);
                if (data[i].label_day == "Fri" || data[i].label_day == "Sat" || data[i].label_day == "Sun")
                    this.bgColors.push('red');
                else
                    this.bgColors.push('rgba(75,192,192,0.6)');
            };
        },
        labels: [], dataPoints: [], bgColors: [], mmNas: false, options: getGraphOptions({ formaty: String.prototype.formatNumber }),
        define: "Wifi users in the location"
    }, {
        metric: 'Footfall', interval: 'Months',
        header: 'In Vicinity', apis: ['GetMapTrendsMonthlyFootfallVicinity'],
        mapper: function (data) {
            keyedValueMapper.call(this, data, 'avg_daily_foot_vicinity');
        },
        labels: [], dataPoints: [],  bgColors: [], mmNas: true, options: getGraphOptions({ formaty: String.prototype.formatNumber }),

    }, {
        metric: 'Footfall', interval: 'Months',
        header: 'In Store', apis: ['GetMapTrendsMonthlyFootfallInstore'],
        mapper: function (data) {
            keyedValueMapper.call(this, data, 'avg_monthly_foot_instore');
        },
        labels: [], dataPoints: [], bgColors: [], mmNas: true, options: getGraphOptions({ formaty: String.prototype.formatNumber })
    }, {
        metric: 'Footfall', interval: 'Months',
        header: 'Passer by', apis: ['GetMapTrendsMonthlyFootfallPasserby'],
        mapper: function (data) {
            keyedValueMapper.call(this, data, 'avg_daily_foot_passerby');
        },
        labels: [], dataPoints: [], mmNas: true, options: getGraphOptions({ formaty: String.prototype.formatNumber }),
        define: "People passed by location"
    }, {
        metric: 'Footfall', interval: 'Months',
        header: 'Wifi Users', apis: ['GetMapTrendsMonthlyUsers'],
        mapper: function (data) {
            for (var i = 0; i < data.length; i++) {
                this.labels.push(data[i].label);
                this.dataPoints.push(data[i].avg_footfall);
                if (data[i].label_day == "Fri" || data[i].label_day == "Sat" || data[i].label_day == "Sun")
                    this.bgColors.push('red');
                else
                    this.bgColors.push('rgba(75,192,192,0.6)');
            };
        },
        labels: [], dataPoints: [],  bgColors: [], mmNas: false, options: getGraphOptions({ formaty: String.prototype.formatNumber }),
        define: 'Wifi users in the location'
    },





    {
        metric: Constants.trends_avg_time_spent, interval: 'Days',
        header: 'In Vicinity', apis: ['GetMapTrendsDailyFootfall'],
        mapper: function (data) {
            keyedValueMapper.call(this, data, 'avg_time_vicinity');
        },
        labels: [], dataPoints: [],  bgColors: [], mmNas: true, options: getGraphOptions({ unit: 'min' })
    }, {
        metric: Constants.trends_avg_time_spent, interval: 'Days',
        header: 'In Store', apis: ['GetMapTrendsDailyFootfall'],
        mapper: function (data) {
            keyedValueMapper.call(this, data, 'avg_time_instore');
        },
        labels: [], dataPoints: [], bgColors: [], mmNas: true, options: getGraphOptions({ unit: 'min' })
    }, {
        metric: Constants.trends_avg_time_spent, interval: 'Days',
        header: 'Passer by', apis: ['GetMapTrendsDailyFootfall'],
        mapper: function (data) {
            keyedValueMapper.call(this, data, 'avg_time_passerby');
        },
        labels: [], dataPoints: [], bgColors: [], mmNas: true, options: getGraphOptions({ unit: 'min' })
    }, {
        metric: Constants.trends_avg_time_spent, interval: 'Days',
        header: 'Wifi Users', apis: ['GetMapTrendsDailyUsers'],
        mapper: function (data) {
            for (var i = 0; i < data.length; i++) {
                this.labels.push(data[i].label);
                this.dataPoints.push(data[i].avg_time_spent);
                if (data[i].label_day == "Fri" || data[i].label_day == "Sat" || data[i].label_day == "Sun")
                    this.bgColors.push('red');
                else
                    this.bgColors.push('rgba(75,192,192,0.6)');
            };
        },
        labels: [], dataPoints: [],  bgColors: [], mmNas: false, options: getGraphOptions({ unit: 'min' })
    }, {
        metric: Constants.trends_avg_time_spent, interval: 'Months',
        header: 'In Vicinity', apis: ['GetMapTrendsMonthlyFootfallVicinity'],
        mapper: function (data) {
            keyedValueMapper.call(this, data, 'avg_time_vicinity');
        },
        labels: [], dataPoints: [], bgColors: [], mmNas: true, options: getGraphOptions({ unit: 'min' })
    }, {
        metric: Constants.trends_avg_time_spent, interval: 'Months',
        header: 'In Store', apis: ['GetMapTrendsMonthlyFootfallTimeInstore'],
        mapper: function (data) {
            keyedValueMapper.call(this, data, 'avg_time_instore');
        },
        labels: [], dataPoints: [], mmNas: true, options: getGraphOptions({ unit: 'min' })
    }, {
        metric: Constants.trends_avg_time_spent, interval: 'Months',
        header: 'Passer by', apis: ['GetMapTrendsMonthlyFootfallPasserby'],
        mapper: function (data) {
            keyedValueMapper.call(this, data, 'avg_time_passerby');
        },
        labels: [], dataPoints: [], bgColors: [], mmNas: true, options: getGraphOptions({ unit: 'min' })
    }, {
        metric: Constants.trends_avg_time_spent, interval: 'Months',
        header: 'Wifi Users', apis: ['GetMapTrendsMonthlyUsers'],
        mapper: function (data) {
            for (var i = 0; i < data.length; i++) {
                this.labels.push(data[i].label);
                this.dataPoints.push(data[i].avg_time_spent);
                if (data[i].label_day == "Fri" || data[i].label_day == "Sat" || data[i].label_day == "Sun")
                    this.bgColors.push('red');
                else
                    this.bgColors.push('rgba(75,192,192,0.6)');
            };
        },
        labels: [], dataPoints: [], bgColors: [], mmNas: false, options: getGraphOptions({ unit: 'min' })
    },



    {
        metric: Constants.trends_avg_conv_rate, interval: 'Days',
        header: '', apis: ['GetMapTrendsDailyFootfall'],
        mapper: function (data) {
            keyedValueMapper.call(this, data, 'avg_daily_convert_rate');
        },
        labels: [], dataPoints: [], bgColors: [], mmNas: true, options: getGraphOptions({ unit: '%' })
    }, {
        metric: Constants.trends_avg_conv_rate, interval: 'Months',
        header: '', apis: ['GetMapTrendsMonthlyFootfallConversion'],
        mapper: function (data) {
            for (var i = 0; i < data.length; i++) {
                this.labels.push(data[i].label);
                this.dataPoints.push(data[i].avg_convert_rate);
                if (data[i].label_day == "Fri" || data[i].label_day == "Sat" || data[i].label_day == "Sun")
                    this.bgColors.push('red');
                else
                    this.bgColors.push('rgba(75,192,192,0.6)');
            };
        },
        labels: [], dataPoints: [], bgColors: [], mmNas: false, options: getGraphOptions({ unit: '%' })
    },



    {
         metric: Constants.trends_avg_data_used, interval: 'Days',
         header: 'In Vicinity', apis: [],
         mapper: function (data) {
             for (var i = 0; i < data.length; i++) {
                 this.labels.push(data[i].label);
                 this.dataPoints.push(data[i].avg_data_used);
                 if (data[i].label_day == "Fri" || data[i].label_day == "Sat" || data[i].label_day == "Sun")
                     this.bgColors.push('red');
                 else
                     this.bgColors.push('rgba(75,192,192,0.6)');
             };
         },
         labels: [], dataPoints: [], bgColors: [], mmNas: true, options: getGraphOptions({ unit: 'MB' })
    }, {
        metric: Constants.trends_avg_data_used, interval: 'Days',
        header: 'In Store', apis: [],
        mapper: function (data) {
            for (var i = 0; i < data.length; i++) {
                this.labels.push(data[i].label);
                this.dataPoints.push(data[i].avg_data_used);
                if (data[i].label_day == "Fri" || data[i].label_day == "Sat" || data[i].label_day == "Sun")
                    this.bgColors.push('red');
                else
                    this.bgColors.push('rgba(75,192,192,0.6)');
            };
        },
        labels: [], dataPoints: [], bgColors: [], mmNas: true, options: getGraphOptions({ unit: 'MB' })
    }, {
        metric: Constants.trends_avg_data_used, interval: 'Days',
        header: 'Passer by', apis: [],
        mapper: function (data) {
            for (var i = 0; i < data.length; i++) {
                this.labels.push(data[i].label);
                this.dataPoints.push(data[i].avg_data_used);
                if (data[i].label_day == "Fri" || data[i].label_day == "Sat" || data[i].label_day == "Sun")
                    this.bgColors.push('red');
                else
                    this.bgColors.push('rgba(75,192,192,0.6)');
            };
        },
        labels: [], dataPoints: [], bgColors: [], mmNas: true, options: getGraphOptions({ unit: 'MB' })
    }, {
        metric: Constants.trends_avg_data_used, interval: 'Days',
        header: 'Wifi Users', apis: ['GetMapTrendsDailyUsers'],
        mapper: function (data) {
            for (var i = 0; i < data.length; i++) {
                this.labels.push(data[i].label);
                this.dataPoints.push(data[i].avg_data_used);
                if (data[i].label_day == "Fri" || data[i].label_day == "Sat" || data[i].label_day == "Sun")
                    this.bgColors.push('red');
                else
                    this.bgColors.push('rgba(75,192,192,0.6)');
            };
        },
        labels: [], dataPoints: [], bgColors: [], mmNas: false, options: getGraphOptions({ unit: 'MB' })
    }, {
        metric: Constants.trends_avg_data_used, interval: 'Months',
        header: 'In Vicinity', apis: [],
        mapper: function (data) {
            for (var i = 0; i < data.length; i++) {
                this.labels.push(data[i].label);
                this.dataPoints.push(data[i].avg_data_used);
                if (data[i].label_day == "Fri" || data[i].label_day == "Sat" || data[i].label_day == "Sun")
                    this.bgColors.push('red');
                else
                    this.bgColors.push('rgba(75,192,192,0.6)');
            };
        },
        labels: [], dataPoints: [], mmNas: true, options: getGraphOptions({ unit: 'MB' })
    }, {
        metric: Constants.trends_avg_data_used, interval: 'Months',
        header: 'In Store', apis: [],
        mapper: function (data) {
            for (var i = 0; i < data.length; i++) {
                this.labels.push(data[i].label);
                this.dataPoints.push(data[i].avg_data_used)
            };
        },
        labels: [], dataPoints: [], bgColors: [], mmNas: true, options: getGraphOptions({ unit: 'MB' })
    }, {
        metric: Constants.trends_avg_data_used, interval: 'Months',
        header: 'Passer by', apis: [],
        mapper: function (data) {
            for (var i = 0; i < data.length; i++) {
                this.labels.push(data[i].label);
                this.dataPoints.push(data[i].avg_data_used);
                if (data[i].label_day == "Fri" || data[i].label_day == "Sat" || data[i].label_day == "Sun")
                    this.bgColors.push('red');
                else
                    this.bgColors.push('rgba(75,192,192,0.6)');
            };
        },
        labels: [], dataPoints: [], bgColors: [], mmNas: true, options: getGraphOptions({ unit: 'MB' })
    }, {
        metric: Constants.trends_avg_data_used, interval: 'Months',
        header: 'Wifi Users', apis: ['GetMapTrendsMonthlyUsers'],
        mapper: function (data) {
            for (var i = 0; i < data.length; i++) {
                this.labels.push(data[i].label);
                this.dataPoints.push(data[i].avg_data_used);
                if (data[i].label_day == "Fri" || data[i].label_day == "Sat" || data[i].label_day == "Sun")
                    this.bgColors.push('red');
                else
                    this.bgColors.push('rgba(75,192,192,0.6)');
            };
        },
        labels: [], dataPoints: [], bgColors: [], mmNas: false, options: getGraphOptions({ unit: 'MB' })
    },



     {
         metric: Constants.trends_repeat_30, interval: 'Days',
         header: 'In Vicinity', apis: ['GetMapTrendsDailyFootfall'],
         mapper: function (data) {
             keyedValueMapper.call(this, data, 'rep_30_percent_vicinity');
         },
         labels: [], dataPoints: [],bgColors: [],
         mmNas: true, options: getGraphOptions({ unit: '%' })
     }, {
         metric: Constants.trends_repeat_30, interval: 'Days',
         header: 'In Store', apis: ['GetMapTrendsDailyFootfall'],
         mapper: function (data) {
             keyedValueMapper.call(this, data, 'rep_30_percent_instore');
         },
         labels: [], dataPoints: [], bgColors: [],
         mmNas: true, options: getGraphOptions({ unit: '%' })
     }, {
        metric: Constants.trends_repeat_30, interval: 'Days',
        header: 'Passer by', apis: ['GetMapTrendsDailyFootfall'],
        mapper: function (data) {
            keyedValueMapper.call(this, data, 'rep_30_percent_passerby');
        },
        labels: [], dataPoints: [],bgColors: [],
        mmNas: true, options: getGraphOptions({ unit: '%' })
    }, {
        metric: Constants.trends_repeat_30, interval: 'Days',
        header: 'Wifi Users', apis: ['GetMapTrendsDailyUsers'],
        mapper: function (data) {
            for (var i = 0; i < data.length; i++) {
                this.labels.push(data[i].label);
                this.dataPoints.push(data[i].rep_30_percent);
                if (data[i].label_day == "Fri" || data[i].label_day == "Sat" || data[i].label_day == "Sun")
                    this.bgColors.push('red');
                else
                    this.bgColors.push('rgba(75,192,192,0.6)');
            };
        },
        labels: [], dataPoints: [],bgColors: [],
        mmNas: false, options: getGraphOptions({ unit: '%' })
    }, {
        metric: Constants.trends_repeat_30, interval: 'Months',
        header: 'In Vicinity', apis: ['GetMapTrendsMonthlyFootfallRepeatVicinity'],
        mapper: function (data) {
            keyedValueMapper.call(this, data, 'rep_30_percent_vicinity');
        },
        labels: [], dataPoints: [],bgColors: [],
        mmNas: true, options: getGraphOptions({ unit: '%' })
    }, {
        metric: Constants.trends_repeat_30, interval: 'Months',
        header: 'In Store', apis: ['GetMapTrendsMonthlyFootfallInstoreRepeat'],
        mapper: function (data) {
            keyedValueMapper.call(this, data, 'rep_30_percent_instore');
        },
        labels: [], dataPoints: [],
        mmNas: true, options: getGraphOptions({ unit: '%' })
    }, {
        metric: Constants.trends_repeat_30, interval: 'Months',
        header: 'Passer by', apis: ['GetMapTrendsMonthlyFootfallRepeatPasserby'],
        mapper: function (data) {
            keyedValueMapper.call(this, data, 'rep_30_percent_passerby');
        },
        labels: [], dataPoints: [],bgColors: [],
        mmNas: true, options: getGraphOptions({ unit: '%' })
    }, {
        metric: Constants.trends_repeat_30, interval: 'Months',
        header: 'Wifi Users', apis: ['GetMapTrendsMonthlyRepeatUsers'],
        mapper: function (data) {
            keyedValueMapper.call(this, data, 'rep_30_percent');
        },
        labels: [], dataPoints: [],bgColors: [],
        mmNas: false, options: getGraphOptions({ unit: '%' })
    }];
};

var getLoyalityGraphs = function () {
    return [{
        metric: 'Last 7 Days',
        header: 'In Vicinity', apis: ['GetMapLoyaltyFootfallRepeat'],
        mapper: function (data) {
            keyedValueMapper.call(this, data, 'rep_7_percent_vicinity')
        },
        labels: [], dataPoints: [], bgColors: [],
        define : 'Engagement in vicinity in last 7 days',
        mmNas: true, options: getGraphOptions({ unit: '%' })
    }, {
        metric: 'Last 15 Days',
        header: 'In Vicinity', apis: ['GetMapLoyaltyFootfallRepeat'],
        mapper: function (data) {
            keyedValueMapper.call(this, data, 'rep_14_percent_vicinity')
        },
        labels: [], dataPoints: [], bgColors: [],
        define: 'Engagement in vicinity in last 15 days',
        mmNas: true, options: getGraphOptions({ unit: '%' })
    }, {
        metric: 'Last 30 Days',
        header: 'In Vicinity', apis: ['GetMapLoyaltyFootfallRepeat'],
        mapper: function (data) {
            keyedValueMapper.call(this, data, 'rep_30_percent_vicinity')
        },
        labels: [], dataPoints: [],
        define: 'Engagement in vicinity in last 30 days',
        mmNas: true, options: getGraphOptions({ unit: '%' })
    }, {
        metric: 'Last 7 Days',
        header: 'In Store', apis: ['GetMapLoyaltyFootfallRepeat'],
        mapper: function (data) {
            keyedValueMapper.call(this, data, 'rep_7_percent_instore')
        },
        labels: [], dataPoints: [], bgColors: [],
        define: 'Engagement in location in last 7 days',
        mmNas: true, options: getGraphOptions({ unit: '%' })
    }, {
        metric: 'Last 15 Days',
        header: 'In Store', apis: ['GetMapLoyaltyFootfallRepeat'],
        mapper: function (data) {
            keyedValueMapper.call(this, data, 'rep_14_percent_instore')
        },
        labels: [], dataPoints: [], bgColors: [],
        define: 'Engagement in location in last 15 days',
        mmNas: true, options: getGraphOptions({ unit: '%' })
    }, {
        metric: 'Last 30 Days',
        header: 'In Store', apis: ['GetMapLoyaltyFootfallRepeat'],
        mapper: function (data) {
            keyedValueMapper.call(this, data, 'rep_30_percent_instore')
        },
        labels: [], dataPoints: [],
        define: 'Engagement in location in last 30 days',
        mmNas: true, options: getGraphOptions({ unit: '%' })
    }, {
        metric: 'Last 7 Days',
        header: 'Passer by', apis: ['GetMapLoyaltyFootfallRepeat'],
        mapper: function (data) {
            keyedValueMapper.call(this, data, 'rep_7_percent_passerby')
        },
        labels: [], dataPoints: [], bgColors: [],
        define: 'Engagement in for passed users in last 7 days',
        mmNas: true, options: getGraphOptions({ unit: '%' })
    }, {
        metric: 'Last 15 Days',
        header: 'Passer by', apis: ['GetMapLoyaltyFootfallRepeat'],
        mapper: function (data) {
            keyedValueMapper.call(this, data, 'rep_14_percent_passerby')
        },
        labels: [], dataPoints: [], bgColors: [],
        define: 'Engagement in for passed users in last 15 days',
        mmNas: true, options: getGraphOptions({ unit: '%' })
    }, {
        metric: 'Last 30 Days',
        header: 'Passer by', apis: ['GetMapLoyaltyFootfallRepeat'],
        mapper: function (data) {
            keyedValueMapper.call(this, data, 'rep_30_percent_passerby')
        },
        labels: [], dataPoints: [], bgColors: [],
        define: 'Engagement in for passed users in last 30 days',
        mmNas: true, options: getGraphOptions({ unit: '%' })
    }, {
        metric: 'Last 7 Days',
        header: 'Wifi Users', apis: ['GetMapUserLoyalty'],
        mapper: function (data) {
            keyedValueMapper.call(this, data, 'rep_7_percent')
        },
        labels: [], dataPoints: [], bgColors: [],
        define: 'Engagement in wi-fi users in last 7 days',
        mmNas: false, options: getGraphOptions({ unit: '%' })
    }, {
        metric: 'Last 15 Days',
        header: 'Wifi Users', apis: ['GetMapUserLoyalty'],
        mapper: function (data) {
            keyedValueMapper.call(this, data, 'rep_14_percent')
        },
        labels: [], dataPoints: [], bgColors: [],
        define: 'Engagement in wi-fi users in last 15 days',
        mmNas: false, options: getGraphOptions({ unit: '%' })
    }, {
        metric: 'Last 30 Days',
        header: 'Wifi Users', apis: ['GetMapUserLoyalty'],
        mapper: function (data) {
            keyedValueMapper.call(this, data, 'rep_30_percent')
        },
        labels: [], dataPoints: [], bgColors: [],
        define: 'Engagement in wi-fi users in last 30 days',
        mmNas: false, options: getGraphOptions({ unit: '%' })
    }];
};

var getDistributionDataSet = function (labels, data, backgroundColor) {
    Chart.defaults.global.legend.display = true;
    return angular.copy({
        labels: labels,
        datasets: [
			{
			    data: data,
			    backgroundColor: backgroundColor
			}]
    });
};

var getDistributionGraphs = function () {

    return [{
        metric: 'Data Used',
        header: 'Wifi Users', apis: ['GetMapDataDistribution'],
        mapper: distributionWifiUserMapper,
        define : 'Data used by wi fi users',
        labels: [], dataPoints: [], mmNas: false, options: getGraphOptions({ unit: '%' })
    },


    {
        metric: 'Visit Count',
        header: 'In Vicinity', apis: ['GetMapDistributionFootfallVisitVicinity'],
        mapper: distributionInVicinityMapper,
        define: 'Visit counts users in vicinity',
        labels: [], dataPoints: [], mmNas: true, options: getGraphOptions({ unit: '%' })
    }, {
        metric: 'Visit Count',
        header: 'In Store', apis: ['GetMapDistributionFootfallVisitInstore'],
        mapper: distributionInStoreMapper,
        define: 'Visit count of users in store',
        labels: [], dataPoints: [], mmNas: true, options: getGraphOptions({ unit: '%' })
    }, {
        metric: 'Visit Count',
        header: 'Passer by', apis: ['GetMapDistributionFootfallVisitPasserby'],
        mapper: distributionPasserByMapper,
        define: 'Visit count of users pass',
        labels: [], dataPoints: [], mmNas: true, options: getGraphOptions({ unit: '%' })
    }, {
        metric: 'Visit Count',
        header: 'Wifi Users', apis: ['GetMapVisitDistribution'],
        mapper: distributionWifiUserMapper,
        define: 'Visit counts of wi fi users',
        labels: [], dataPoints: [], mmNas: false, options: getGraphOptions({ unit: '%' })
    },


	{
	    metric: 'Time Spent',
	    header: 'In Vicinity', apis: ['GetMapDistributionFootfallTimeVicinity'],
	    mapper: distributionInVicinityMapper,
	    define: 'Time spent by users in vicinity',
	    labels: [], dataPoints: [], mmNas: true, options: getGraphOptions({ unit: '%' })
	}, {
	    metric: 'Time Spent',
	    header: 'In Store', apis: ['GetMapDistributionFootfallTimeInstore'],
	    mapper: distributionInStoreMapper,
	    define: 'Time spent by users in store',
	    labels: [], dataPoints: [], mmNas: true, options: getGraphOptions({ unit: '%' })
	}, {
	    metric: 'Time Spent',
	    header: 'Passer by', apis: ['GetMapDistributionFootfallTimePasserby'],
	    mapper: distributionPasserByMapper,
	    define: 'Time spent by users pass',
	    labels: [], dataPoints: [], mmNas: true, options: getGraphOptions({ unit: '%' })
	}, {
	    metric: 'Time Spent',
	    header: 'Wifi Users', apis: ['GetMapTimeDistribution'],
	    mapper: distributionWifiUserMapper,
	    define: 'Time spent by users in wi fi',
	    labels: [], dataPoints: [], mmNas: false, options: getGraphOptions({ unit: '%' })
	}]
};

var distributionWifiUserMapper = function (data) {
    var colors = ['rgba(14, 181, 178, 1)', 'rgba(14, 181, 178, .8)', 'rgba(14, 181, 178, .6)',
		'rgba(14, 181, 178, .4)', 'rgba(14, 181, 178, .2)', 'rgba(14, 181, 178, .1)'];
	mapDistributionsData.call(this, data, colors);
};

var distributionInStoreMapper = function (data) {
	var colors = ['rgba(30, 255, 0, .8)', 'rgba(30, 255, 0, .6)',
		'rgba(30, 255, 0, .4)', 'rgba(30, 255, 0, .2)', 'rgba(30, 255, 0, .1)'];
	mapDistributionsData.call(this, data, colors);
};

var distributionPasserByMapper = function (data) {
	var colors = ['rgba(59, 77, 230, .8)', 'rgba(59, 77, 230, .6)',
		'rgba(59, 77, 230, .4)', 'rgba(59, 77, 230, .2)', 'rgba(59, 77, 230, .1)'];
	mapDistributionsData.call(this, data, colors);
};

var distributionInVicinityMapper = function (data) {
	var colors = ['rgba(255, 0, 0, .8)', 'rgba(255, 0, 0, .6)',
		'rgba(255, 0, 0, .4)', 'rgba(255, 0, 0, .2)', 'rgba(255, 0, 0, .1)'];
	mapDistributionsData.call(this, data, colors);
};

var mapDistributionsData = function (data, colors) {
    var sumAll = 0;
    this.backgroundColor = colors;
    for (var i = 0; i < data.length; i++) {
        sumAll += parseInt(data[i].frequency);
    };
    for (var i = 0; i < data.length; i++) {
        var toPush = data.filter(function (obj) {
            return obj.orderer == (i + 1) ? true : false;
        });
        if (toPush.length) {
            this.labels.push(toPush[0].range);
            this.dataPoints.push(formatRate((toPush[0].frequency * 100) / sumAll));
        }
    };
};

var getComparisonGraphs = function () {
	return [{
	    metric: '% Instore repeat in last 30 days',
	    apis: ['GetMapStoreComparisonFootfallInstore'],
	    mapper: function (data) {
	        angularForEachMapper.call(this, data, 'rep_30_percent_instore');
	    },
	    define:'Percent instore repeat in last 30 days',
	    labels: [], dataPoints: [], mmNas: true, options: getGraphOptions({ unit: '%' })
	}, {
		metric: 'Conversion Rate',
		apis: ['GetMapStoreComparisonFootfallConversion'],
		mapper: function (data) {
		    angularForEachMapper.call(this, data, 'avg_convert_rate');
		},
		define: 'Conersion rate',
		labels: [], dataPoints: [], mmNas: true, options: getGraphOptions({ unit: '%' })
	}, {
	    metric: 'Total Wi-Fi users',
		apis: ['GetMapStoreComparisonUsers'],
		mapper: function (data) {
		    angularForEachMapper.call(this, data, 'total_wifi_users');
		},
		define: 'Total wi fi users',
		labels: [], dataPoints: [], mmNas: false, options: getGraphOptions({ formaty: String.prototype.formatNumber })
	}, {
		metric: 'Avg Time Spent - In Store User',
		apis: ['GetMapStoreComparisonFootfallInstore'],
		mapper: function (data) {
		    angularForEachMapper.call(this, data, 'avg_time_instore');
		},
		define: 'Average time spent in store',
		labels: [], dataPoints: [], mmNas: true, options: getGraphOptions({ unit: 'min' })
	}, {
	    metric: 'Instore count',
	    apis: ['GetMapStoreComparisonFootfallConversion'],
	    mapper: function (data) {
	        angularForEachMapper.call(this, data, 'instore_count');
	    },
	    define: 'In store count',
	    labels: [], dataPoints: [], mmNas: true, options: getGraphOptions({ formaty: String.prototype.formatNumber })
	}]
};

var getCSPGraphs = function () {
    return [{
        metric: 'Conversion rate',
        apis: ['GetStorePerformanceConversionRate'],
        mapper: function (data) {
            mapperForCSPplot.call(this, data);
        },
        labels: [], dataPoints: [],bgColors:[],bgBorder:[], xAvg:0, yAvg:0, mmNas: true
    }];
}

var createNasList = function (nasids) {
	var nasList = nasids.slice(0);
	return JSON.stringify(nasList).replaceAll('\"', '').replaceAll("\\[", '').replaceAll("\\]", '');
};

var keyedValueMapper = function (data, field) {
    this.labels = [];
    this.dataPoints = [];
    this.bgColors = [];
    for (var i = 0; i < data.length; i++) {
        this.labels.push(data[i].label);
        this.dataPoints.push(data[i][field]);
        if (data[i].label_day == "Fri" || data[i].label_day == "Sat" || data[i].label_day == "Sun")
            this.bgColors.push('red');
        else
            this.bgColors.push('rgba(75,192,192,0.6)');
    };
}

var angularForEachMapper = function (data, field) {
    angular.forEach(data, function (value, key) {
        this.labels.push(key);
        this.dataPoints.push(value[field]);
    }, this);
};


var mapperForCSPplot = function (data) {
    var avrageCord = getAverageCords(data);
    this.bgColors = [];
    this.bgBorder = [];
    this.xAvg = avrageCord.Xaverage;
    this.yAvg = avrageCord.Yaverage;
    angular.forEach(data, function (value, key) {
        var obj = {};
        this.labels.push(key);
        obj.y = value['convert_rate'] * 100;
        obj.x = value['daily_vic'];
        this.dataPoints.push(obj);
        if(value['convert_rate'] < avrageCord.Yaverage && value['daily_vic'] < avrageCord.Xaverage) 
        {
            this.bgColors.push('#690b0b');
            this.bgBorder.push('#690b0b');
        }
        else if (value['convert_rate'] > avrageCord.Yaverage && value['daily_vic'] < avrageCord.Xaverage)
        {
            this.bgColors.push('#dbdb08');
            this.bgBorder.push('#dbdb08');
        }
        else if (value['convert_rate'] < avrageCord.Yaverage && value['daily_vic'] > avrageCord.Xaverage)
        {
            this.bgColors.push('red');
            this.bgBorder.push('red');
        }
        else if (value['convert_rate'] > avrageCord.Yaverage && value['daily_vic'] > avrageCord.Xaverage)
        {
            this.bgColors.push('green');
            this.bgBorder.push('green');
        }
    }, this);
}

var getNewTemplate = function (graphId, graphHeader) {
    return '<div class="graph-container">' +
		'<div class="row graph-header"><div class="col-md-10"><h6>' + graphHeader + '</h6></div>' +
        '<div class="col-md-2 dropdown">' +
        '<button state="menu" type="button" title="More Options" class="dropdown-toggle" data-toggle="dropdown" href="#"><i class="fa fa-bars" aria-hidden="true"></i></button>' +
        '<ul class="dropdown-menu pull-right">' +
        '<li><a href="javascript:void(0)" class="saveAsJpegButton">Save as JPEG</a></li>' +
        '<li><a href="javascript:void(0)" class="exportButton">Export as CSV</a></li>' +
        '</ul>' +
        '</div>'+
        '</div>' +
        '<canvas class="graph" id="' + graphId + '"></canvas>' +
		'</div>';
}

var getAverageCords = function (dataPoints) {
    var sumX = 0;
    var sumY = 0;
    var i = 0;
    angular.forEach(dataPoints, function (value, key) {
        sumY += Number(value['convert_rate']);
        sumX += Number(value['daily_vic']);
        i += 1;
    }, this);
    obj = {};
    obj.Yaverage = (sumY / i);
    obj.Xaverage = (sumX / i);
    return obj;
}

var exportData = function (data) {
    if (Array.isArray(data))
        var graphData = data;
    else
        var graphData = [data];
    var new_array = [];
    var filename = 'report.csv';
    for (var i = 0; i < graphData.length; i++) {
        if (graphData[i] && graphData[i].dataPoints.length > 0) {
            for (var j = 0; j < graphData[i].dataPoints.length; j++) {
                var obj = {};
                if (graphData[i].hasOwnProperty("define")) {
                    var str = graphData[i].define;
                    str = str.replace(/[^a-z0-9]/gi, '_').toLowerCase();
                    filename = str + '.csv';
                }
                obj.metric = graphData[i].metric;
                if (graphData[i].hasOwnProperty("header"))
                    obj.header = graphData[i].header;
                obj.label = graphData[i].labels[j];
                if (typeof (graphData[i].dataPoints[j]) == 'object') {
                    angular.forEach(graphData[i].dataPoints[j], function (value, key) {
                        obj[key] = value;
                    });
                } else {
                    obj.value = graphData[i].dataPoints[j];
                }

                new_array.push(obj);
            }
        } 
    }
    var exportItems = new_array;
    alasql.promise('SELECT * INTO CSV("' + filename + '",{headers:true,quote:"\'",separator:","}) FROM ?', [exportItems])
           .then(function () {
               console.log('Data saved');
           }).catch(function (err) {
               console.log('Error:', err);
           });;
};