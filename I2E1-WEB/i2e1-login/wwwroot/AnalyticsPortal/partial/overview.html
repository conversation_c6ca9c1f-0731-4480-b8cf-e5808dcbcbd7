<div class="overview-container">
    <h4 class="header">OVERVIEW</h4>
    <hr />

    <div class="in-vicinity">
        <h6>In Vicinity</h6>
        <div ng-repeat="obj in inVicinity | orderObjectBy:'id':false" class="overview-square-card" title="{{obj.title}}">
            <div class="card-loader" ng-show="!(obj.data!=='' && obj.rate!=='')">
                <div class="deactivate"></div>
                <div class="img-section">
                    <div>
                        <img src="../../images/wifi_loader_84X84.gif">
                    </div>
                </div>
            </div>
            <div class="card-header">{{obj.cardHeader}}</div>
            <div class="content" ng-show="(obj.data!=='' && obj.rate!=='')">
                <div class="data">
                    {{obj.data}} {{obj.unit}}
                    <br />
                    <span ng-if="obj.rate" class="rate">{{obj.rate}}% <i class="glyphicon glyphicon-triangle-{{obj.rateDirection}}"></i></span>
                </div>
            </div>
            <div ng-if="obj.rate" class="card-footer">vs Last Period <i class="glyphicon glyphicon-question-sign" title="As compared to dates from {{dates.deltaStartDate}} to {{dates.deltaEndDate}}"></i></div>
        </div>
    </div>

    <hr />

    <div class="in-store">
        <h6>In Store</h6>
        <div data-index="{{$index}}" ng-repeat="obj in inStore | orderObjectBy:'id':false" class="overview-square-card" title="{{obj.title}}">
            <div class="card-loader" ng-show="!(obj.data!=='' && obj.rate!=='')">
                <div class="deactivate"></div>
                <div class="img-section">
                    <div>
                        <img src="../../images/wifi_loader_84X84.gif">
                    </div>
                </div>
            </div>
            <div class="card-header">{{obj.cardHeader}}</div>
            <div class="content" ng-show="(obj.data!=='' && obj.rate!=='')">
                <div class="data">
                    {{obj.data}} {{obj.unit}}
                    <br />
                    <span ng-if="obj.rate" class="rate">{{obj.rate}}% <i class="glyphicon glyphicon-triangle-{{obj.rateDirection}}"></i></span>
                </div>
            </div>
            <div ng-if="obj.rate" class="card-footer">vs Last Period <i class="glyphicon glyphicon-question-sign" title="As compared to dates from {{dates.deltaStartDate}} to {{dates.deltaEndDate}}"></i></div>
        </div>
    </div>

    <hr />

    <div class="wifi-users">
        <h6>Wifi Users</h6>
        <div ng-repeat="obj in wifiUsers | orderObjectBy:'id':false" class=" overview-square-card" title="{{obj.title}}">
            <div class="card-loader" ng-show="!(obj.data!=='' && obj.rate!=='')">
                <div class="deactivate"></div>
                <div class="img-section">
                    <div>
                        <img src="../../images/wifi_loader_84X84.gif">
                    </div>
                </div>
            </div>
            <div class="card-header">{{obj.cardHeader}}</div>
            <div class="content" ng-show="(obj.data!=='' && obj.rate!=='')">
                <div class="data">
                    {{obj.data}} {{obj.unit}}
                    <br />
                    <span ng-if="obj.rate" class="rate">{{obj.rate}}% <i class="glyphicon glyphicon-triangle-{{obj.rateDirection}}"></i></span>
                </div>
            </div>
            <div ng-if="obj.rate" class="card-footer">vs Last Period <i class="glyphicon glyphicon-question-sign" title="As compared to dates from {{dates.deltaStartDate}} to {{dates.deltaEndDate}}"></i></div>
        </div>
    </div>
</div>