<div class="title">
    <div class=" row user-row">
        <div class="icon-area"><span class="glyphicon glyphicon-user"></span></div>
        <span class="label-name">{{name}}</span>
        <span class="label-username">{{userName}}</span>
    </div>
    <img src="../images/Logo-Color-256.png" />
    <div class="links-group" ng-if="userName != '<EMAIL>'">
        <a class="link" href="/AdminPortal/home" title="Admin Portal">Admin Portal</a>
    </div>
    <div class="signout" ng-click="logout()" title="Signout"><span class="glyphicon glyphicon-off"></span></div>
    <div class="messege">
        <span class="success"></span>
        <span class="error"></span>
        <span class="info"></span>
    </div>
</div>

<div class="left-side-icon-bar" ng-click="hideMenu()">
    <div class="row" ng-class="data.selected===row.label?'active':''"
         ng-repeat="row in leftRail" ui-sref="{{row.sref}}" ng-click="data.selected=row.label;switchLocationSelector(row);">
        <div class="icon-area"><img src="../images/{{row.icon}}.png" /></div>
        <span class="label-area">{{row.label}}</span>
    </div>
</div>
<div class="menu" ng-click="openMenu()">
    <div class="icon-area"><span class="glyphicon glyphicon-menu-hamburger"></span></div>
</div>

<div class="container">
    <div class="selects">
        <i class="glyphicon glyphicon-search"></i>
        <selectize config="singleLocationSelectorConfig"
                   options='$parent.routerDetails'
                   ng-model="location"
                   ng-change="updateLocation()"
                   class="single-location select-loaction"
                   title="Enter location here, type to search"></selectize>
        
        <selectize config="multipleLocationSelectorConfig"
                   options='$parent.routerDetails'
                   ng-model="locations"
                   ng-change="updateLocation()"
                   class="multiple-location select-loaction"
                   title="Select multiple locations, type to search"></selectize>
    </div>
    <hr />

    <div class="row date-range-picker">
        <div class="date-range-btn" title="Change date ranges">
            <button id="date-range-picker" class="btn btn-primary btn-lg">Select Range</button>
        </div>
        <div class="from-to-dates">
            <div class="from" title="{{dates.completeFromDate}}">
                <span>From:</span>
                <div class="date">
                    {{dates.fromShowDate}}
                </div>
            </div>
            <div class="to" title="{{dates.completeToDate}}">
                <span>To:</span>
                <div class="date">
                    {{dates.toShowDate}}
                </div>
            </div>
        </div>

        <div class="about-period single-location">
            <div>Number of active days: <span>{{dates.activeDays}}</span></div>
            <div>Total days: <span>{{dates.totalDays}}</span></div>
        </div>
    </div>
    <div class="row notes"><div class="col-md-12">All numbers are based on 'smartphones with Wi-Fi switched on' detected by i2e1. Based on our estimates, this represents 60-80% of the actual people at the location</div></div>
    <div ui-view ng-show="showMap"></div>
    <div class="no-location-selected" ng-show="!showMap">
        <div>Please Select a location</div>
        <script type="text/javascript">
            $('select-loaction').focus();
        </script>
    </div>
</div>

<div class="screen-backdrop" ng-click="openMenu()"></div>
<span class="screen-detector">
    <span class="tablets"></span>
    <span class="smartphones"></span>
    <span class="normal"></span>
</span>