<div class="loyality-container">
    <h4 class="header">CUSTOMER ENGAGEMENT</h4>
    <hr />
    <div class="sub-header">
        <div class="row">
            <div class="col-md-6">
                <ul class="list-inline" id="graph-selector">
                    <li>
                        <h5>{{metric.selected.name}}</h5>
                    </li>
                    <li ng-click="changeGraphType('bar')">
                        <button type="button" class="btn btn-default" aria-label="Left Align">
                            <i class="fa fa-bar-chart" aria-hidden="true"></i>
                        </button>
                    </li>
                    <li ng-click="changeGraphType('line')">
                        <button type="button" class="btn btn-default" aria-label="Left Align">
                            <i class="fa fa-line-chart" aria-hidden="true"></i>
                        </button>
                    </li>
                </ul>
            </div>
            <div class="col-md-6">
                <div class="selectors">
                    <div class="metric-selector">
                        <ui-select ng-model="metric.selected" theme="selectize" title="Select metrics" ng-change="updateLocation()">
                            <ui-select-match placeholder="Metrices">{{$select.selected.name}}</ui-select-match>
                            <ui-select-choices repeat="metric in metrics | filter: $select.search">
                                <div ng-bind-html="metric.name"></div>
                            </ui-select-choices>
                        </ui-select>
                    </div>
                </div>
            </div>
        </div>
        <span class="define">{{metric.selected.define}}</span>
    </div>
    <div class="graphs">
    </div>
</div>