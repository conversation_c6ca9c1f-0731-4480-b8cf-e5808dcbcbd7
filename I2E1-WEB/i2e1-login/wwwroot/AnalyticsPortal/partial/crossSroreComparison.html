<div class="csc-container">
    <h4 class="header">Cross Store Comparison</h4>
    <hr />

    <div class="sub-header">
        <h5>{{metric.selected.title}}</h5><br />
        <div class="row">
            <div class="col-md-6">
                <span class="pull-left define">{{metric.selected.define}}</span>
            </div>
            <div class="col-md-6">

                <div class="selectors pull-right">
                    <div class="metric-selector">
                        <ui-select ng-model="metric.selected" theme="selectize" title="Select matrics" ng-change="updateLocation()">
                            <ui-select-match placeholder="Metrices">{{$select.selected.name}}</ui-select-match>
                            <ui-select-choices repeat="metric in metrics | filter: $select.search">
                                <div ng-bind="metric.name"></div>
                            </ui-select-choices>
                        </ui-select>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="row graph_row">
        <div class="col-md-1"></div>
        <div class="col-md-10"><div class="graphs"></div></div>
        <div class="col-md-1"></div>
    </div>
    </div>
