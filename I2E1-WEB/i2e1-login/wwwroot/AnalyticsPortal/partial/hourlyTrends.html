<div class="hourly-trends-container">
    <h4 class="header">HOURLY TRENDS</h4>
    <hr />
    <div class="sub-header">
        <div class="graph-header">
            <h5>{{metric.selected.name}}</h5>
            <span class="define">{{metric.selected.define}}</span>
        </div>
        <div class="selectors">
            <div class="metric-selector">
                <ui-select ng-model="metric.selected" theme="selectize" title="Select matrics" ng-change="updateLocation()">
                    <ui-select-match placeholder="Metrices">{{$select.selected.name}}</ui-select-match>
                    <ui-select-choices repeat="metric in metrics | filter: $select.search">
                        <div ng-bind-html="metric.name"></div>
                    </ui-select-choices>
                </ui-select>
            </div>
        </div>
    </div>
    <div class="graph-container">
        <div class="row">
            <div class="col-md-12">
                <div class="dropdown">
                    <button state="menu" type="button" title="More Options" class="dropdown-toggle" data-toggle="dropdown" href="#"><i class="fa fa-bars" aria-hidden="true"></i></button>
                    <ul class="dropdown-menu pull-right">
                        <li><a href="javascript:void(0)" class="saveAsJpegButton">Save as JPEG</a></li>
                        <li><a href="javascript:void(0)" class="exportButton">Export as CSV</a></li>
                    </ul>
                </div>
            </div>
            <div class="col-md-12">
                <div class="hourly-trend">
                    <div class="day row">
                        <div class="day-name">
                            <div class="content">
                                <span class="data">
                                </span>
                            </div>
                        </div>
                        <div class="hour-cell {{hour}} {{hour.label}} header" ng-repeat="hour in hourSlots">
                            <div class="content">
                                <span class="data">
                                    {{hour.label}}
                                </span>
                            </div>
                        </div>
                    </div>
                    <br />
                    <span ng-repeat="day in days">
                        <div class="day row {{day.name}}">
                            <div class="day-name">
                                <div class="content">
                                    <span class="data">
                                        {{day.name}}
                                    </span>
                                </div>
                            </div>
                            <div class="hour-cell" title="{{hour.value}}" ng-repeat="hour in day.hourSlots">
                                <div class="content {{hour.label}}">
                                    <svg xmlns="http://www.w3.org/2000/svg" version="1.1">
                                        <circle cx="50%" cy="50%" r="0" fill="rgb(14, 181, 178)" />
                                    </svg>
                                </div>
                            </div>
                        </div>
                        <br />
                    </span>
                </div>
            </div>
        </div>
    </div>
</div>