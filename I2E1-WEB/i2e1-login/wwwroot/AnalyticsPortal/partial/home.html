<div class="home-container">
    <h4 class="header">VICINITY</h4>
    <hr />
    <div class="vicinity circle" title="Defined as population in and around the selected location">
        <div class="content">
            <span class="data">
                {{vicinity.total_foot_vicinity}}
                </br>
                <span class="label">Footfall in Vicinity</span>
            </span>
        </div>
    </div>
    <div class="vicinity-breakup">
        <div class="in-store circle" title="Defined as population that entered the selected location">
            <div class="content">
                <span class="data">{{vicinity.total_foot_instore}}</span>
                <span class="label">In-Store Visitors</span>
            </div>
        </div>
        <div class="line">
            <div class="vicinity-verticle-line">
                <div></div>
                <div></div>
            </div>
            <div></div>
        </div>
        <div class="passer-by circle" title="Defined as population outside that passed by the selected location">
            <div class="content">
                <span class="data">{{vicinity.total_foot_passerby}}</span>
                <span class="label">Passer by</span>
            </div>
        </div>
        <div class="verticle-line-zone">
            <div class="verticle-line"></div>
        </div>
    </div>

    <div class="in-store-breakup">
        <div class="wifi-users circle" title="Defined as population that accessed i2e1 hotspot at the selected location">
            <div class="content">
                <span class="data">{{vicinity.total_wifi_users}}</span>
                <span class="label">Wi-Fi Users</span>
            </div>
        </div>
        <div class="line"></div>
    </div>
</div>