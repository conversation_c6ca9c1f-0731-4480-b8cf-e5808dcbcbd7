<div class="distributions-container">
    <h4 class="header">DISTRIBUTION</h4>
    <hr />

    <div class="sub-header">
        <h5>{{metric.selected.name}}</h5><br />
        <span class="define">{{metric.selected.define}}</span>

        <div class="selectors">
            <div class="metric-selector">
                <ui-select ng-model="metric.selected" theme="selectize" title="Select matrics" ng-change="updateLocation()">
                    <ui-select-match placeholder="Metrices">{{$select.selected.name}}</ui-select-match>
                    <ui-select-choices repeat="metric in metrics | filter: $select.search">
                        <div ng-bind="metric.name"></div>
                    </ui-select-choices>
                </ui-select>
            </div>
            
        </div>
    </div>

    <div class="graphs"></div>
</div>