<div class="trends-container">
    <h4 class="header">TRENDS</h4>
    <hr />

    <div class="sub-header">
        <div class="row">
            <div class="col-md-6">
                <ul class="list-inline" id="graph-selector">
                    <li>
                        <h5>{{metric.selected.title[interval.selected]}}</h5>
                    </li>
                    <li ng-click="changeGraphType('bar')">
                        <button type="button" class="btn btn-default" aria-label="Left Align">
                            <i class="fa fa-bar-chart" aria-hidden="true"></i>
                        </button>
                    </li>
                    <li ng-click="changeGraphType('line')">
                        <button type="button" class="btn btn-default" aria-label="Left Align">
                            <i class="fa fa-line-chart" aria-hidden="true"></i>
                        </button>
                    </li>
                </ul>
            </div>
            <div class="col-md-6">
                <div class="selectors">
                    <div class="metric-selector">
                        <ui-select ng-model="metric.selected" theme="selectize" title="Select matrics" ng-change="updateLocation()">
                            <ui-select-match placeholder="Metrices">{{$select.selected.name}}</ui-select-match>
                            <ui-select-choices repeat="item in metrics | filter: $select.search">
                                <span ng-bind-html="item.name | highlight: $select.search"></span>
                            </ui-select-choices>
                        </ui-select>
                    </div>
                    <div class="spanSelector">
                        <ui-select ng-model="interval.selected" theme="selectize" title="Select interval span" ng-change="updateLocation()">
                            <ui-select-match placeholder="Span">{{$select.selected}}</ui-select-match>
                            <ui-select-choices repeat="span in intervals | filter: $select.search">
                                <div ng-bind="span"></div>
                            </ui-select-choices>
                        </ui-select>
                    </div>
                </div>
            </div>
        </div>
        <span class="define">{{metric.selected.define}}</span>
    </div>

    <div class="graphs">

    </div>
</div>
