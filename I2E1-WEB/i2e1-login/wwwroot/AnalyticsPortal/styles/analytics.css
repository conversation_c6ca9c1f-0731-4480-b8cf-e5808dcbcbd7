html,
body {
  height: 100%;
}
body {
  background-color: #F6F6F6;
  font-size: 1.6rem;
  margin: 0;
}
body #chartjs-tooltip {
  opacity: 1;
  z-index: 999;
  position: absolute;
  background: #000000;
  color: white;
  border-radius: 3px;
  transition: all .1s ease;
  pointer-events: none;
  transform: translate(-50%, 0);
}
body .chartjs-tooltip-key {
  display: inline-block;
  width: 10px;
  height: 10px;
  margin-right: 10px;
}
.saturate {
  filter: saturate(3);
}
.grayscale {
  filter: grayscale(100%);
}
.contrast {
  filter: contrast(160%);
}
.brightness {
  filter: brightness(0.25);
}
.blur {
  filter: blur(3px);
}
.invert {
  filter: invert(100%);
}
.sepia {
  filter: sepia(100%);
}
.huerotate {
  filter: hue-rotate(180deg);
}
.rss.opacity {
  filter: opacity(50%);
}
.display-none {
  display: none !important;
}
a:focus {
  text-decoration: none;
  outline: unset;
}
#loader .deactivate,
.card-loader .deactivate {
  position: fixed;
  width: 100%;
  height: 100%;
  background-color: white;
  top: 0;
  left: 0;
  opacity: 0.6;
  z-index: 99;
}
#loader .img-section,
.card-loader .img-section {
  position: fixed;
  left: 50%;
  top: 32%;
  z-index: 100;
}
@media only screen and (max-width: 479px), only screen and (min-width: 479px) and (max-width: 959px) {
  #loader .img-section,
  .card-loader .img-section {
    height: 75%;
  }
}
#loader .img-section > div,
.card-loader .img-section > div {
  position: relative;
  left: -50%;
}
.selectize-input {
  background: white !important;
  border-radius: 0.1rem !important;
  border: 1px solid rgba(136, 136, 136, 0.5) !important;
  box-shadow: 1px 1px 0 0 rgba(136, 136, 136, 0.21);
}
.selectize-input.selectize-focus {
  border: 1px solid rgba(136, 136, 136, 0.5) !important;
}
.selectize-input.focus {
  box-shadow: 1px 1px 0 0 rgba(136, 136, 136, 0.21);
}
.selectize-dropdown {
  border: 1px solid rgba(136, 136, 136, 0.5) !important;
  box-shadow: 1px 1px 0 0 rgba(136, 136, 136, 0.21);
}
.circle {
  border: 0.5rem solid #F6F6F6;
  border-radius: 100rem;
  box-shadow: 0 0 2px #888;
  display: inline-block;
  position: relative;
  height: 5rem;
  width: 5rem;
}
@media only screen and (max-width: 479px) {
  .circle {
    font-size: 1rem;
  }
}
.circle .content {
  position: absolute;
  width: 100%;
  height: 100%;
  text-align: center;
  display: table;
}
.circle .content span.data {
  display: table-cell;
  vertical-align: middle;
  font-weight: bold;
}
.notes {
  font-size: 1.3rem;
  margin-bottom: 10px;
}
.sub-container-background {
  height: 100%;
  min-height: 30rem;
  background-color: white;
  text-align: center;
  line-height: 1;
  padding: 1rem;
  margin-top: 1rem;
  overflow-x: auto;
  width: 100%;
  margin-bottom: 2rem;
  font-size: 1.8rem;
  border: 1px solid rgba(136, 136, 136, 0.21);
}
.sub-container-background h5,
.sub-container-background h6 {
  text-align: justify;
}
.sub-container-background h5 i.glyphicon,
.sub-container-background h6 i.glyphicon {
  color: rgba(136, 136, 136, 0.8);
  vertical-align: text-top;
}
.sub-container-background h4.header {
  color: rgba(136, 136, 136, 0.8);
}
@media only screen and (max-width: 479px), only screen and (min-width: 479px) and (max-width: 959px) {
  .sub-container-background h4.header {
    font-size: 1.5rem;
  }
}
.sub-container-background .sub-header {
  text-align: justify;
  position: relative;
}
.sub-container-background .sub-header h5 {
  display: inline-block;
}
@media only screen and (max-width: 479px) {
  .sub-container-background .sub-header h5 {
    margin: 0;
  }
}
@media only screen and (max-width: 479px), only screen and (min-width: 479px) and (max-width: 959px) {
  .sub-container-background .sub-header h5 {
    font-size: 1.3rem;
  }
}
.sub-container-background .sub-header .define {
  font-size: 1.5rem;
  color: #ababab;
  display: inline-block;
  text-align: left;
  line-height: 1.4;
  width: 50rem;
}
.sub-container-background .sub-header .selectors {
  /*position: absolute;
            right: 0;
            top: 2.5rem;*/
}
.sub-container-background .sub-header .selectors > div {
  padding: 0 1rem;
  width: 20rem;
  display: inline-block;
}
@media only screen and (max-width: 479px) {
  .sub-container-background .sub-header .selectors > div {
    width: 9rem;
  }
  .sub-container-background .sub-header .selectors > div .selectize-input {
    height: 2.5rem;
    line-height: 1rem;
  }
  .sub-container-background .sub-header .selectors > div .selectize-input:after {
    top: 60%;
    right: .25rem;
  }
}
.sub-container-background .sub-header .selectors .selectize-input {
  box-shadow: 1px 1px 0 0 #888888;
}
.sub-container-background .sub-header .selectors .selectize-input.selectize-focus {
  border: 1px solid rgba(136, 136, 136, 0.5) !important;
}
.overview-square-card {
  position: relative;
  width: 14rem;
  height: 14rem;
  border: 1px solid rgba(136, 136, 136, 0.8);
  display: inline-block;
  text-align: center;
  padding: .5rem;
  margin-right: 2rem;
}
.overview-square-card .card-loader .deactivate {
  position: absolute;
}
.overview-square-card .card-loader .img-section {
  position: absolute;
}
.overview-square-card .card-loader img {
  width: 5rem;
}
.overview-square-card .glyphicon-question-sign {
  color: rgba(136, 136, 136, 0.8);
}
@media only screen and (max-width: 479px) {
  .overview-square-card {
    margin: 0 .2rem;
  }
}
.overview-square-card .card-header {
  position: absolute;
  width: 92%;
  font-size: 1rem;
  text-align: left;
  font-weight: 500;
  color: #0022a5;
}
.overview-square-card .content {
  position: absolute;
  width: 92%;
  height: 92%;
  text-align: center;
  display: table;
}
.overview-square-card .content div.data {
  display: table-cell;
  vertical-align: middle;
  font-size: 2.7rem;
  color: black;
  font-weight: 300;
  line-height: 0.8;
}
.overview-square-card .content div.data .rate {
  font-size: 1.5rem;
}
.overview-square-card .content div.data .rate .glyphicon {
  font-size: 2rem;
  vertical-align: sub;
  color: lawngreen;
}
.overview-square-card .content div.data .rate .glyphicon.glyphicon-triangle-bottom {
  color: red;
}
.overview-square-card .card-footer {
  position: absolute;
  width: 92%;
  bottom: .5rem;
  font-size: x-small;
  color: black;
}
.title {
  position: fixed;
  top: 0;
  left: 0;
  bottom: 0;
  right: 0;
  background-color: white;
  text-align: center;
  height: 4.5rem;
  z-index: 10;
  box-shadow: 0 0.0625rem 0.3125rem 0 #888888;
}
.title img {
  position: absolute;
  top: .5rem;
  left: 47%;
  height: 3.5rem;
  image-rendering: -moz-crisp-edges;
  image-rendering: -o-crisp-edges;
  image-rendering: -webkit-optimize-contrast;
  -ms-interpolation-mode: nearest-neighbor;
}
.title .signout {
  position: absolute;
  top: 0;
  right: 0;
  padding: .5rem 1rem;
  cursor: pointer;
  transition: font-size 0.2s ease-in-out;
}
@media (pointer: coarse) {
  .title .signout {
    color: black;
    font-size: 2rem;
  }
}
.title .signout:hover {
  color: black;
  font-size: 2rem;
}
.title .user-row {
  line-height: 4.5rem;
  height: inherit;
  width: 20rem;
  float: left;
  color: black;
}
.title .user-row .icon-area {
  width: 5rem;
  position: absolute;
  left: 0;
}
.title .user-row .label-name {
  position: absolute;
  left: 5rem;
  transition: line-height 0.4s ease-in-out;
}
.title .user-row .label-username {
  opacity: 0;
  position: absolute;
  left: 10rem;
  font-size: 1.2rem;
  top: 1rem;
  transition: all 0.5s ease-in-out;
}
.title .user-row:hover .label-username {
  opacity: 1;
  left: 5rem;
  color: #666666;
}
.title .user-row:hover .label-name {
  line-height: 3rem;
}
.title .messege {
  position: absolute;
  width: 100%;
  top: 0;
  opacity: 0;
  transition: all 0.3s ease-in-out;
}
.title .messege.popup {
  top: 4.5rem;
  opacity: 1;
}
.title .messege.popup .appear {
  display: inline;
}
.title .messege span {
  white-space: nowrap;
  padding: 0 1rem;
  border-radius: 2px;
  display: none;
  transition: top 0.5s ease-in-out;
}
.title .messege .success {
  background-color: #b6ff00;
}
.title .messege .error {
  background-color: #ec7979;
}
.title .messege .info {
  background-color: #ffd800;
}
.title .links-group {
  height: 4.5rem;
  line-height: 4.5rem;
  position: absolute;
  right: 5rem;
  background: rgba(136, 136, 136, 0.8);
}
@media only screen and (max-width: 479px) {
  .title .links-group {
    display: none;
  }
}
.title .links-group .link {
  color: rgba(255, 255, 255, 0.75);
  margin: 0 1rem;
}
.title .links-group .link:hover {
  color: white;
  text-decoration: none;
  display: inline-block;
  padding: 0 1rem;
}
hr {
  border-top: 1px solid rgba(136, 136, 136, 0.21);
}
.backdrop {
  position: absolute;
  top: 0;
  left: 0;
  bottom: 0;
  right: 0;
  z-index: 5;
  background-color: rgba(0, 0, 0, 0.25);
}
.screen-detector span {
  display: none;
  opacity: 0;
}
.screen-detector .normal {
  opacity: 1;
  display: inline-block;
}
@media only screen and (max-width: 479px) {
  .screen-detector span.smartphones {
    opacity: 1;
    display: inline-block;
  }
  .screen-detector span.normal {
    display: none;
    opacity: 0;
  }
}
@media only screen and (min-width: 479px) and (max-width: 959px) {
  .screen-detector span.tablets {
    opacity: 1;
    display: inline-block;
  }
  .screen-detector span.normal {
    display: none !important;
    opacity: 0 !important;
  }
}
.menu {
  display: none;
  position: absolute;
  top: 4.8rem;
  left: 6rem;
  z-index: 12;
  cursor: pointer;
}
.menu .icon-area {
  width: 100%;
  height: 100%;
}
.menu .icon-area span {
  vertical-align: bottom;
}
.menu:hover {
  color: black;
}
.menu.move-this {
  display: none;
}
@media (pointer: coarse) {
  .menu {
    display: inline-block;
  }
}
@media only screen and (max-width: 479px) {
  .menu {
    left: 1.7rem;
    z-index: 25;
  }
}
.left-side-icon-bar {
  width: 5rem;
  height: 100%;
  position: fixed;
  top: 4.6rem;
  z-index: 15;
  text-align: center;
  background-color: white;
  border-right: 1px solid rgba(136, 136, 136, 0.21);
  transition: width 0.5s ease-in-out;
}
.left-side-icon-bar .row {
  line-height: 5rem;
  height: 5rem;
  margin: 0;
}
.left-side-icon-bar .row.user-row {
  background-color: #F6F6F6;
  cursor: auto !important;
  border-bottom: 1px solid rgba(136, 136, 136, 0.21);
}
.left-side-icon-bar .row .label-area {
  display: inline-block;
  position: absolute;
  left: 5rem;
  white-space: nowrap;
  transition: opacity 0.3s ease-out;
  opacity: 0;
}
.left-side-icon-bar .row .icon-area {
  width: 5rem;
  display: inline-block;
  position: absolute;
  left: 0;
}
.left-side-icon-bar .row .icon-area img {
  width: 2.5rem;
}
.left-side-icon-bar .row:hover {
  background-color: #F6F6F6;
  color: black;
  cursor: pointer;
}
.left-side-icon-bar .row.active {
  background-color: #e6e6e6;
  color: black;
  text-shadow: 1px 1px 1px rgba(158, 158, 158, 0.49);
}
.left-side-icon-bar:hover,
.left-side-icon-bar.hover-this {
  width: 20rem;
}
.left-side-icon-bar:hover .row .label-area,
.left-side-icon-bar.hover-this .row .label-area {
  z-index: 12;
  opacity: 1;
  transition: opacity 1s ease-in;
}
@media (pointer: coarse) {
  .left-side-icon-bar:hover {
    width: 5rem;
  }
  .left-side-icon-bar:hover .row .label-area {
    display: none;
  }
}
@media only screen and (max-width: 479px) {
  .left-side-icon-bar {
    width: 0;
  }
  .left-side-icon-bar .row .icon-area {
    opacity: 0;
  }
  .left-side-icon-bar:hover {
    width: 0;
  }
  .left-side-icon-bar:hover .row {
    z-index: 3;
    display: none;
  }
}
@media only screen and (max-width: 479px) and (pointer: coarse) {
  .left-side-icon-bar.hover-this {
    width: 20rem;
  }
  .left-side-icon-bar.hover-this .row .icon-area {
    opacity: 1;
    transition: opacity 1s ease-in;
  }
}
.daterangepicker {
  background-color: #e6e6e6;
  padding: 1rem;
}
.daterangepicker .ranges {
  background-color: white;
  padding: 1rem;
  line-height: 2.5rem;
  border-radius: 4px;
}
.daterangepicker .ranges .applyBtn {
  color: #4ab04d;
}
.daterangepicker .ranges .btn-sm {
  width: 47%;
  margin: 0 1px;
  border-radius: 0;
  color: white;
}
.daterangepicker .ranges .cancelBtn {
  background-color: grey;
}
.daterangepicker .ranges li {
  border-radius: 0;
  border: none;
  color: unset;
}
.daterangepicker .ranges li.active {
  border: none;
  color: black;
  text-shadow: 1px 1px 1px rgba(158, 158, 158, 0.49);
  background-color: #e6e6e6;
}
.daterangepicker .ranges li:hover {
  border: none;
  background-color: #F6F6F6;
  color: black;
}
.daterangepicker .daterangepicker_input {
  background-color: white !important;
  margin-bottom: 1rem;
}
.daterangepicker .input-mini {
  border: 0;
  border-radius: 0;
  background-color: white;
  transition: unset;
}
.daterangepicker .input-mini.active {
  border: 0;
  border-radius: 0;
  background-color: white;
}
.daterangepicker .input-mini:focus {
  box-shadow: none;
}
.daterangepicker .calendar-table {
  border: none !important;
  border-radius: 4px !important;
}
.daterangepicker td.in-range {
  background-color: #F6F6F6;
  color: black;
  text-shadow: 1px 1px 1px rgba(158, 158, 158, 0.49);
}
.daterangepicker td.active,
.daterangepicker td.active:hover {
  background-color: #e6e6e6;
  color: black;
  text-shadow: 1px 1px 1px rgba(158, 158, 158, 0.49);
}
.daterangepicker .calendar.left {
  margin-right: 1rem !important;
}
.container {
  position: absolute;
  top: 4.6rem;
  left: 0;
  bottom: 0;
  overflow-y: auto;
  padding: 2rem 6rem 0 10rem;
  width: 100%;
  min-width: 36rem;
  z-index: 4;
}
@media only screen and (max-width: 479px) {
  .container {
    padding: 2rem 3rem 0 6rem;
  }
}
.container .no-location-selected {
  text-align: center;
}
.container .date-range-picker {
  height: 6rem;
  width: 100%;
  margin-bottom: 2rem;
  margin: 0;
}
.container .date-range-picker .date-range-btn .btn-primary {
  background-color: red;
  border-radius: 0.1rem;
}
.container .date-range-picker .date-range-btn .btn-primary:active {
  background-color: rgba(255, 0, 0, 0.5);
  background-image: none;
}
@media only screen and (max-width: 479px) {
  .container .date-range-picker .date-range-btn .btn-primary {
    width: inherit;
    font-size: 1.15rem;
  }
}
.container .date-range-picker > div {
  display: inline-block;
  height: 95%;
  vertical-align: bottom;
}
.container .date-range-picker .from-to-dates {
  width: 17rem;
  margin-left: 4rem;
  vertical-align: super;
}
.container .date-range-picker .from-to-dates .date {
  float: right;
  width: 12rem;
  color: green;
  font-style: italic;
  font-size: 1.3rem;
  font-family: monospace;
}
@media only screen and (max-width: 479px) {
  .container .date-range-picker .from-to-dates {
    width: 14rem;
  }
  .container .date-range-picker .from-to-dates .date {
    width: 9rem;
  }
}
.container .date-range-picker .from-to-dates > div {
  line-height: 3rem;
}
.container .date-range-picker .about-period {
  float: right;
  vertical-align: top;
}
.container .date-range-picker .about-period > div {
  width: 22rem;
}
.container .date-range-picker .about-period span {
  float: right;
  width: 3rem;
  color: green;
  font-style: italic;
  font-family: monospace;
}
@media only screen and (max-width: 479px) {
  .container .date-range-picker .about-period {
    float: left;
    vertical-align: top;
    width: 100%;
  }
}
@media only screen and (max-width: 479px) {
  .container .date-range-picker {
    text-align: justify;
    margin: 0;
  }
}
.selects {
  width: 100%;
}
.selects i.glyphicon {
  position: absolute;
  z-index: 2;
  top: 3.1rem;
  left: 11rem;
  opacity: .5;
}
.selects i.glyphicon:hover {
  opacity: 0.8;
}
@media only screen and (max-width: 479px) {
  .selects i.glyphicon {
    left: 7rem;
  }
}
.selects > .ui-select-container {
  width: 100%;
  margin-bottom: 1rem;
}
.selects > .ui-select-container .caret {
  display: none !important;
}
.selects > .ui-select-container .selectize-input {
  padding-left: 3rem;
}
.selects > .ui-select-container .selectize-input:after {
  position: unset;
  margin-top: 0;
  border-style: unset;
}
.selects > .selectize-control.single .selectize-input [data-value] {
  background-color: transparent;
  border: none;
  padding-right: 0 !important;
}
.selects > .selectize-control .selectize-input {
  padding-left: 3rem !important;
}
.selects > .selectize-control .selectize-input [data-value] {
  background-image: none;
  background-color: #F6F6F6;
  color: #666666;
  text-shadow: unset;
  border-radius: 2px;
  border: 1px solid rgba(136, 136, 136, 0.21);
  box-shadow: none;
}
.selects > .selectize-control .selectize-input [data-value].item .storeName {
  font-weight: bold;
}
.selects > .selectize-control .selectize-input [data-value].item .city {
  font-style: italic;
  font-size: smaller;
  font-family: monospace;
}
.selects > .selectize-control .selectize-input [data-value].active {
  background-image: none;
  background-color: #e6e6e6;
  border: 1px solid rgba(136, 136, 136, 0.21);
  color: black;
}
.selects > .selectize-control .selectize-input [data-value] .remove {
  border-left: 1px solid rgba(136, 136, 136, 0.21);
}
.selects > .selectize-control .selectize-input::after {
  content: none;
}
.selects > .selectize-control .selectize-dropdown .selectize-dropdown-content .create.active,
.selects > .selectize-control .selectize-dropdown .selectize-dropdown-content [data-value].active {
  background-image: none;
  background-color: #F6F6F6;
}
.selects > .selectize-control .selectize-dropdown .selectize-dropdown-content [data-value] .secondary {
  font-size: 1rem;
  opacity: 0.8;
}
.selects > .selectize-control .selectize-dropdown .selectize-dropdown-content [data-value] .secondary span {
  margin-right: 2rem;
}
.selects .location > .ui-select-container {
  display: inline-block;
  margin-right: 2rem;
  width: 30rem;
}
.overview-container {
  height: 100%;
  min-height: 30rem;
  background-color: white;
  text-align: center;
  line-height: 1;
  padding: 1rem;
  margin-top: 1rem;
  overflow-x: auto;
  width: 100%;
  margin-bottom: 2rem;
  font-size: 1.8rem;
  border: 1px solid rgba(136, 136, 136, 0.21);
}
.overview-container h5,
.overview-container h6 {
  text-align: justify;
}
.overview-container h5 i.glyphicon,
.overview-container h6 i.glyphicon {
  color: rgba(136, 136, 136, 0.8);
  vertical-align: text-top;
}
.overview-container h4.header {
  color: rgba(136, 136, 136, 0.8);
}
@media only screen and (max-width: 479px), only screen and (min-width: 479px) and (max-width: 959px) {
  .overview-container h4.header {
    font-size: 1.5rem;
  }
}
.overview-container .sub-header {
  text-align: justify;
  position: relative;
}
.overview-container .sub-header h5 {
  display: inline-block;
}
@media only screen and (max-width: 479px) {
  .overview-container .sub-header h5 {
    margin: 0;
  }
}
@media only screen and (max-width: 479px), only screen and (min-width: 479px) and (max-width: 959px) {
  .overview-container .sub-header h5 {
    font-size: 1.3rem;
  }
}
.overview-container .sub-header .define {
  font-size: 1.5rem;
  color: #ababab;
  display: inline-block;
  text-align: left;
  line-height: 1.4;
  width: 50rem;
}
.overview-container .sub-header .selectors {
  /*position: absolute;
            right: 0;
            top: 2.5rem;*/
}
.overview-container .sub-header .selectors > div {
  padding: 0 1rem;
  width: 20rem;
  display: inline-block;
}
@media only screen and (max-width: 479px) {
  .overview-container .sub-header .selectors > div {
    width: 9rem;
  }
  .overview-container .sub-header .selectors > div .selectize-input {
    height: 2.5rem;
    line-height: 1rem;
  }
  .overview-container .sub-header .selectors > div .selectize-input:after {
    top: 60%;
    right: .25rem;
  }
}
.overview-container .sub-header .selectors .selectize-input {
  box-shadow: 1px 1px 0 0 #888888;
}
.overview-container .sub-header .selectors .selectize-input.selectize-focus {
  border: 1px solid rgba(136, 136, 136, 0.5) !important;
}
.overview-container .in-vicinity {
  text-align: justify;
}
.overview-container .in-vicinity .overview-square-card {
  border-color: #31459b;
}
.overview-container .in-store {
  text-align: justify;
}
.overview-container .in-store .overview-square-card {
  border-color: #4ab04d;
}
.overview-container .wifi-users {
  text-align: justify;
}
.overview-container .wifi-users .overview-square-card {
  border-color: #e75349;
}
.home-container {
  height: 100%;
  min-height: 30rem;
  background-color: white;
  text-align: center;
  line-height: 1;
  padding: 1rem;
  margin-top: 1rem;
  overflow-x: auto;
  width: 100%;
  margin-bottom: 2rem;
  font-size: 1.8rem;
  border: 1px solid rgba(136, 136, 136, 0.21);
}
.home-container h5,
.home-container h6 {
  text-align: justify;
}
.home-container h5 i.glyphicon,
.home-container h6 i.glyphicon {
  color: rgba(136, 136, 136, 0.8);
  vertical-align: text-top;
}
.home-container h4.header {
  color: rgba(136, 136, 136, 0.8);
}
@media only screen and (max-width: 479px), only screen and (min-width: 479px) and (max-width: 959px) {
  .home-container h4.header {
    font-size: 1.5rem;
  }
}
.home-container .sub-header {
  text-align: justify;
  position: relative;
}
.home-container .sub-header h5 {
  display: inline-block;
}
@media only screen and (max-width: 479px) {
  .home-container .sub-header h5 {
    margin: 0;
  }
}
@media only screen and (max-width: 479px), only screen and (min-width: 479px) and (max-width: 959px) {
  .home-container .sub-header h5 {
    font-size: 1.3rem;
  }
}
.home-container .sub-header .define {
  font-size: 1.5rem;
  color: #ababab;
  display: inline-block;
  text-align: left;
  line-height: 1.4;
  width: 50rem;
}
.home-container .sub-header .selectors {
  /*position: absolute;
            right: 0;
            top: 2.5rem;*/
}
.home-container .sub-header .selectors > div {
  padding: 0 1rem;
  width: 20rem;
  display: inline-block;
}
@media only screen and (max-width: 479px) {
  .home-container .sub-header .selectors > div {
    width: 9rem;
  }
  .home-container .sub-header .selectors > div .selectize-input {
    height: 2.5rem;
    line-height: 1rem;
  }
  .home-container .sub-header .selectors > div .selectize-input:after {
    top: 60%;
    right: .25rem;
  }
}
.home-container .sub-header .selectors .selectize-input {
  box-shadow: 1px 1px 0 0 #888888;
}
.home-container .sub-header .selectors .selectize-input.selectize-focus {
  border: 1px solid rgba(136, 136, 136, 0.5) !important;
}
.home-container .label {
  color: inherit;
}
.home-container .vicinity.circle {
  height: 14rem;
  width: 14rem;
  border-color: #31459b;
}
@media only screen and (max-width: 479px) {
  .home-container .vicinity.circle {
    width: 9rem;
    height: 9rem;
  }
}
.home-container .line {
  line-height: 0;
  display: inline-block;
  width: 20rem;
  height: 100%;
}
@media only screen and (max-width: 479px) {
  .home-container .line {
    width: 10rem;
  }
}
.home-container .vicinity-breakup {
  height: 18rem;
  position: relative;
}
@media only screen and (max-width: 479px) {
  .home-container .vicinity-breakup {
    height: 10rem;
  }
}
.home-container .vicinity-breakup .circle {
  width: 8rem;
  height: 8rem;
  border-width: 0.4rem;
  vertical-align: sub;
}
@media only screen and (max-width: 479px) {
  .home-container .vicinity-breakup .circle {
    width: 6rem;
    height: 6rem;
  }
}
.home-container .vicinity-breakup .in-store.circle {
  border-color: #4ab04d;
}
.home-container .vicinity-breakup .in-store.circle .label {
  position: absolute;
  top: 2.55rem;
  left: -20rem;
  bottom: 0;
  right: 0;
}
@media only screen and (max-width: 479px) {
  .home-container .vicinity-breakup .in-store.circle .label {
    position: absolute;
    top: -1.5rem;
    left: -0.5rem;
    bottom: 0;
    right: 0;
  }
}
.home-container .vicinity-breakup .passer-by.circle {
  border-color: orangered;
}
.home-container .vicinity-breakup .passer-by.circle .label {
  position: absolute;
  top: 2.55rem;
  left: 8rem;
  bottom: 0;
  right: 0;
}
@media only screen and (max-width: 479px) {
  .home-container .vicinity-breakup .passer-by.circle .label {
    position: absolute;
    top: -1.5rem;
    left: 0;
    bottom: 0;
    right: 0;
  }
}
.home-container .vicinity-breakup .line {
  margin: 0 -0.4rem;
}
.home-container .vicinity-breakup .line .vicinity-verticle-line > div {
  height: 9rem;
  width: 50%;
}
@media only screen and (max-width: 479px) {
  .home-container .vicinity-breakup .line .vicinity-verticle-line > div {
    height: 5rem;
  }
}
.home-container .vicinity-breakup .line .vicinity-verticle-line > div:nth-child(1) {
  border-right: 1px solid rgba(140, 133, 133, 0.5);
}
.home-container .vicinity-breakup .line > div:nth-child(2) {
  border-top: 1px solid rgba(140, 133, 133, 0.5);
  height: 3.5rem;
}
.home-container .vicinity-breakup .line > div {
  margin: 0;
  padding: 0;
  width: 100%;
  height: 50%;
  display: inline-block;
}
.home-container .verticle-line-zone {
  height: 5rem;
  position: absolute;
  bottom: 0;
  right: 50%;
}
@media only screen and (max-width: 479px) {
  .home-container .verticle-line-zone {
    height: 3rem;
    bottom: -1.6rem;
  }
}
.home-container .verticle-line-zone .verticle-line {
  border-left: 1px solid rgba(140, 133, 133, 0.5);
  height: 100%;
  position: absolute;
  left: -14rem;
}
@media only screen and (max-width: 479px) {
  .home-container .verticle-line-zone .verticle-line {
    left: -8.2rem;
  }
}
@media only screen and (max-width: 479px) {
  .home-container .in-store-breakup {
    margin-top: 1.5rem;
  }
}
.home-container .in-store-breakup .wifi-users + .line {
  width: 27.5rem;
}
@media only screen and (max-width: 479px) {
  .home-container .in-store-breakup .wifi-users + .line {
    width: 16rem;
  }
}
.home-container .wifi-users.circle {
  border-width: 0.3rem;
  border-color: #e75349;
  width: 6rem;
  height: 6rem;
}
@media only screen and (max-width: 479px) {
  .home-container .wifi-users.circle {
    width: 4rem;
    height: 4rem;
  }
}
.home-container .wifi-users.circle .label {
  position: absolute;
  top: 1.8rem;
  left: -15rem;
  bottom: 0;
  right: 0;
}
@media only screen and (max-width: 479px) {
  .home-container .wifi-users.circle .label {
    position: absolute;
    top: 1.1rem;
    left: 3.5rem;
    bottom: 0;
    right: 0;
  }
}
.trends-container {
  height: 100%;
  min-height: 30rem;
  background-color: white;
  text-align: center;
  line-height: 1;
  padding: 1rem;
  margin-top: 1rem;
  overflow-x: auto;
  width: 100%;
  margin-bottom: 2rem;
  font-size: 1.8rem;
  border: 1px solid rgba(136, 136, 136, 0.21);
}
.trends-container h5,
.trends-container h6 {
  text-align: justify;
}
.trends-container h5 i.glyphicon,
.trends-container h6 i.glyphicon {
  color: rgba(136, 136, 136, 0.8);
  vertical-align: text-top;
}
.trends-container h4.header {
  color: rgba(136, 136, 136, 0.8);
}
@media only screen and (max-width: 479px), only screen and (min-width: 479px) and (max-width: 959px) {
  .trends-container h4.header {
    font-size: 1.5rem;
  }
}
.trends-container .sub-header {
  text-align: justify;
  position: relative;
}
.trends-container .sub-header h5 {
  display: inline-block;
}
@media only screen and (max-width: 479px) {
  .trends-container .sub-header h5 {
    margin: 0;
  }
}
@media only screen and (max-width: 479px), only screen and (min-width: 479px) and (max-width: 959px) {
  .trends-container .sub-header h5 {
    font-size: 1.3rem;
  }
}
.trends-container .sub-header .define {
  font-size: 1.5rem;
  color: #ababab;
  display: inline-block;
  text-align: left;
  line-height: 1.4;
  width: 50rem;
}
.trends-container .sub-header .selectors {
  /*position: absolute;
            right: 0;
            top: 2.5rem;*/
}
.trends-container .sub-header .selectors > div {
  padding: 0 1rem;
  width: 20rem;
  display: inline-block;
}
@media only screen and (max-width: 479px) {
  .trends-container .sub-header .selectors > div {
    width: 9rem;
  }
  .trends-container .sub-header .selectors > div .selectize-input {
    height: 2.5rem;
    line-height: 1rem;
  }
  .trends-container .sub-header .selectors > div .selectize-input:after {
    top: 60%;
    right: .25rem;
  }
}
.trends-container .sub-header .selectors .selectize-input {
  box-shadow: 1px 1px 0 0 #888888;
}
.trends-container .sub-header .selectors .selectize-input.selectize-focus {
  border: 1px solid rgba(136, 136, 136, 0.5) !important;
}
.trends-container .graphs {
  overflow-x: auto;
  width: 100%;
}
.trends-container .graphs .graph-container {
  padding: 2rem;
  display: inline-block;
}
.trends-container .graphs .graph-container canvas {
  width: 50rem !important;
  height: 25rem !important;
}
.trends-container .sub-header .selectors {
  float: right;
  margin-top: 10px;
}
.trends-container .sub-header > div.row {
  margin: 0;
}
.trends-container .sub-header > div.row .col-md-6 {
  padding: 0;
}
.trends-container .graph-header .col-md-2.dropdown {
  text-align: right;
}
.trends-container .graph-header button {
  background: #fff;
  font-size: 1.3rem;
  border: 1px solid #ccc;
  color: #666666;
  padding: 3px 3px 2px;
  cursor: pointer;
  margin: 10px 0px;
}
.hourly-trends-container {
  overflow-x: unset !important;
  height: 100%;
  min-height: 30rem;
  background-color: white;
  text-align: center;
  line-height: 1;
  padding: 1rem;
  margin-top: 1rem;
  overflow-x: auto;
  width: 100%;
  margin-bottom: 2rem;
  font-size: 1.8rem;
  border: 1px solid rgba(136, 136, 136, 0.21);
}
.hourly-trends-container h5,
.hourly-trends-container h6 {
  text-align: justify;
}
.hourly-trends-container h5 i.glyphicon,
.hourly-trends-container h6 i.glyphicon {
  color: rgba(136, 136, 136, 0.8);
  vertical-align: text-top;
}
.hourly-trends-container h4.header {
  color: rgba(136, 136, 136, 0.8);
}
@media only screen and (max-width: 479px), only screen and (min-width: 479px) and (max-width: 959px) {
  .hourly-trends-container h4.header {
    font-size: 1.5rem;
  }
}
.hourly-trends-container .sub-header {
  text-align: justify;
  position: relative;
}
.hourly-trends-container .sub-header h5 {
  display: inline-block;
}
@media only screen and (max-width: 479px) {
  .hourly-trends-container .sub-header h5 {
    margin: 0;
  }
}
@media only screen and (max-width: 479px), only screen and (min-width: 479px) and (max-width: 959px) {
  .hourly-trends-container .sub-header h5 {
    font-size: 1.3rem;
  }
}
.hourly-trends-container .sub-header .define {
  font-size: 1.5rem;
  color: #ababab;
  display: inline-block;
  text-align: left;
  line-height: 1.4;
  width: 50rem;
}
.hourly-trends-container .sub-header .selectors {
  /*position: absolute;
            right: 0;
            top: 2.5rem;*/
}
.hourly-trends-container .sub-header .selectors > div {
  padding: 0 1rem;
  width: 20rem;
  display: inline-block;
}
@media only screen and (max-width: 479px) {
  .hourly-trends-container .sub-header .selectors > div {
    width: 9rem;
  }
  .hourly-trends-container .sub-header .selectors > div .selectize-input {
    height: 2.5rem;
    line-height: 1rem;
  }
  .hourly-trends-container .sub-header .selectors > div .selectize-input:after {
    top: 60%;
    right: .25rem;
  }
}
.hourly-trends-container .sub-header .selectors .selectize-input {
  box-shadow: 1px 1px 0 0 #888888;
}
.hourly-trends-container .sub-header .selectors .selectize-input.selectize-focus {
  border: 1px solid rgba(136, 136, 136, 0.5) !important;
}
.hourly-trends-container .graph-container {
  width: 100%;
  background-color: white;
}
@media only screen and (max-width: 479px), only screen and (min-width: 479px) and (max-width: 959px) {
  .hourly-trends-container .graph-container {
    overflow-x: auto;
    height: 34rem;
  }
}
.hourly-trends-container .hourly-trend {
  margin-top: 2rem;
}
@media only screen and (max-width: 479px), only screen and (min-width: 479px) and (max-width: 959px) {
  .hourly-trends-container .hourly-trend {
    font-size: 1.2rem;
    min-width: 40rem;
  }
}
.hourly-trends-container .hourly-trend .day {
  height: 6rem;
  border: 1px solid rgba(136, 136, 136, 0.21);
  display: inline-block;
}
@media only screen and (max-width: 479px), only screen and (min-width: 479px) and (max-width: 959px) {
  .hourly-trends-container .hourly-trend .day {
    height: 4rem;
  }
}
.hourly-trends-container .hourly-trend .day .day-name {
  display: inline-block;
  width: 10rem;
  height: 6rem;
  margin-right: -0.4rem;
}
@media only screen and (max-width: 479px), only screen and (min-width: 479px) and (max-width: 959px) {
  .hourly-trends-container .hourly-trend .day .day-name {
    width: 6rem;
    height: 4rem;
  }
}
.hourly-trends-container .hourly-trend .day .non.header {
  font-size: 1.2rem;
}
.hourly-trends-container .hourly-trend .day .hour-cell {
  display: inline-block;
  width: 6rem;
  height: 6rem;
  border-left: 1px solid rgba(136, 136, 136, 0.21);
}
@media only screen and (max-width: 479px), only screen and (min-width: 479px) and (max-width: 959px) {
  .hourly-trends-container .hourly-trend .day .hour-cell {
    width: 4rem;
    height: 4rem;
  }
}
.hourly-trends-container .hourly-trend .day .content {
  width: inherit;
  height: 6rem;
  text-align: center;
  display: table;
}
@media only screen and (max-width: 479px), only screen and (min-width: 479px) and (max-width: 959px) {
  .hourly-trends-container .hourly-trend .day .content {
    height: 4rem;
  }
}
.hourly-trends-container .hourly-trend .day .content svg {
  height: 6rem;
  width: 6rem;
}
@media only screen and (max-width: 479px), only screen and (min-width: 479px) and (max-width: 959px) {
  .hourly-trends-container .hourly-trend .day .content svg {
    height: 4rem;
    width: 4rem;
  }
}
.hourly-trends-container .hourly-trend .day .content .data {
  display: table-cell;
  vertical-align: middle;
  font-family: monospace;
  font-size: 1.2rem;
}
.hourly-trends-container .dropdown {
  text-align: right;
}
.hourly-trends-container .dropdown button {
  background: #fff;
  font-size: 1.3rem;
  border: 1px solid #ccc;
  color: #666666;
  padding: 3px 3px 2px;
  cursor: pointer;
}
.loyality-container {
  height: 100%;
  min-height: 30rem;
  background-color: white;
  text-align: center;
  line-height: 1;
  padding: 1rem;
  margin-top: 1rem;
  overflow-x: auto;
  width: 100%;
  margin-bottom: 2rem;
  font-size: 1.8rem;
  border: 1px solid rgba(136, 136, 136, 0.21);
}
.loyality-container h5,
.loyality-container h6 {
  text-align: justify;
}
.loyality-container h5 i.glyphicon,
.loyality-container h6 i.glyphicon {
  color: rgba(136, 136, 136, 0.8);
  vertical-align: text-top;
}
.loyality-container h4.header {
  color: rgba(136, 136, 136, 0.8);
}
@media only screen and (max-width: 479px), only screen and (min-width: 479px) and (max-width: 959px) {
  .loyality-container h4.header {
    font-size: 1.5rem;
  }
}
.loyality-container .sub-header {
  text-align: justify;
  position: relative;
}
.loyality-container .sub-header h5 {
  display: inline-block;
}
@media only screen and (max-width: 479px) {
  .loyality-container .sub-header h5 {
    margin: 0;
  }
}
@media only screen and (max-width: 479px), only screen and (min-width: 479px) and (max-width: 959px) {
  .loyality-container .sub-header h5 {
    font-size: 1.3rem;
  }
}
.loyality-container .sub-header .define {
  font-size: 1.5rem;
  color: #ababab;
  display: inline-block;
  text-align: left;
  line-height: 1.4;
  width: 50rem;
}
.loyality-container .sub-header .selectors {
  /*position: absolute;
            right: 0;
            top: 2.5rem;*/
}
.loyality-container .sub-header .selectors > div {
  padding: 0 1rem;
  width: 20rem;
  display: inline-block;
}
@media only screen and (max-width: 479px) {
  .loyality-container .sub-header .selectors > div {
    width: 9rem;
  }
  .loyality-container .sub-header .selectors > div .selectize-input {
    height: 2.5rem;
    line-height: 1rem;
  }
  .loyality-container .sub-header .selectors > div .selectize-input:after {
    top: 60%;
    right: .25rem;
  }
}
.loyality-container .sub-header .selectors .selectize-input {
  box-shadow: 1px 1px 0 0 #888888;
}
.loyality-container .sub-header .selectors .selectize-input.selectize-focus {
  border: 1px solid rgba(136, 136, 136, 0.5) !important;
}
.loyality-container .graphs {
  overflow-x: auto;
  width: 100%;
}
.loyality-container .graphs .graph-container {
  padding: 2rem;
  display: inline-block;
}
.loyality-container .graphs .graph-container canvas {
  width: 50rem !important;
  height: 25rem !important;
}
.loyality-container .sub-header .selectors {
  float: right;
  margin-top: 10px;
}
.loyality-container .sub-header > div.row {
  margin: 0;
}
.loyality-container .sub-header > div.row .col-md-6 {
  padding: 0;
}
.loyality-container .graph-header .col-md-2.dropdown {
  text-align: right;
}
.loyality-container .graph-header button {
  background: #fff;
  font-size: 1.3rem;
  border: 1px solid #ccc;
  color: #666666;
  padding: 3px 3px 2px;
  cursor: pointer;
  margin: 10px 0px;
}
.distributions-container {
  height: 100%;
  min-height: 30rem;
  background-color: white;
  text-align: center;
  line-height: 1;
  padding: 1rem;
  margin-top: 1rem;
  overflow-x: auto;
  width: 100%;
  margin-bottom: 2rem;
  font-size: 1.8rem;
  border: 1px solid rgba(136, 136, 136, 0.21);
}
.distributions-container h5,
.distributions-container h6 {
  text-align: justify;
}
.distributions-container h5 i.glyphicon,
.distributions-container h6 i.glyphicon {
  color: rgba(136, 136, 136, 0.8);
  vertical-align: text-top;
}
.distributions-container h4.header {
  color: rgba(136, 136, 136, 0.8);
}
@media only screen and (max-width: 479px), only screen and (min-width: 479px) and (max-width: 959px) {
  .distributions-container h4.header {
    font-size: 1.5rem;
  }
}
.distributions-container .sub-header {
  text-align: justify;
  position: relative;
}
.distributions-container .sub-header h5 {
  display: inline-block;
}
@media only screen and (max-width: 479px) {
  .distributions-container .sub-header h5 {
    margin: 0;
  }
}
@media only screen and (max-width: 479px), only screen and (min-width: 479px) and (max-width: 959px) {
  .distributions-container .sub-header h5 {
    font-size: 1.3rem;
  }
}
.distributions-container .sub-header .define {
  font-size: 1.5rem;
  color: #ababab;
  display: inline-block;
  text-align: left;
  line-height: 1.4;
  width: 50rem;
}
.distributions-container .sub-header .selectors {
  /*position: absolute;
            right: 0;
            top: 2.5rem;*/
}
.distributions-container .sub-header .selectors > div {
  padding: 0 1rem;
  width: 20rem;
  display: inline-block;
}
@media only screen and (max-width: 479px) {
  .distributions-container .sub-header .selectors > div {
    width: 9rem;
  }
  .distributions-container .sub-header .selectors > div .selectize-input {
    height: 2.5rem;
    line-height: 1rem;
  }
  .distributions-container .sub-header .selectors > div .selectize-input:after {
    top: 60%;
    right: .25rem;
  }
}
.distributions-container .sub-header .selectors .selectize-input {
  box-shadow: 1px 1px 0 0 #888888;
}
.distributions-container .sub-header .selectors .selectize-input.selectize-focus {
  border: 1px solid rgba(136, 136, 136, 0.5) !important;
}
.distributions-container .graphs {
  overflow-x: auto;
  width: 100%;
}
.distributions-container .graphs .graph-container {
  padding: 2rem;
  display: inline-block;
}
.distributions-container .graphs .graph-container canvas {
  width: 50rem !important;
  height: 25rem !important;
}
.distributions-container .sub-header .selectors {
  float: right;
  margin-top: 10px;
}
.distributions-container .sub-header > div.row {
  margin: 0;
}
.distributions-container .sub-header > div.row .col-md-6 {
  padding: 0;
}
.distributions-container .graph-header .col-md-2.dropdown {
  text-align: right;
}
.distributions-container .graph-header button {
  background: #fff;
  font-size: 1.3rem;
  border: 1px solid #ccc;
  color: #666666;
  padding: 3px 3px 2px;
  cursor: pointer;
  margin: 10px 0px;
}
.distributions-container .graph {
  border: 1px solid rgba(136, 136, 136, 0.21);
}
.csc-container {
  height: 100%;
  min-height: 30rem;
  background-color: white;
  text-align: center;
  line-height: 1;
  padding: 1rem;
  margin-top: 1rem;
  overflow-x: auto;
  width: 100%;
  margin-bottom: 2rem;
  font-size: 1.8rem;
  border: 1px solid rgba(136, 136, 136, 0.21);
}
.csc-container .metric-selector {
  width: 25rem !important;
}
.csc-container .graphs,
.csc-container .graph_row {
  margin: 0;
}
.csc-container .graph {
  margin-top: 2rem;
  width: 100% !important;
}
.csc-container .dropdown {
  text-align: right;
}
.csc-container .graph-header button {
  background: #fff;
  font-size: 1.3rem;
  border: 1px solid #ccc;
  color: #666666;
  padding: 3px 3px 2px;
  cursor: pointer;
}
