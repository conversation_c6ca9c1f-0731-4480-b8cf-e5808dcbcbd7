@smartphones: ~"only screen and (max-width: 479px)";
@tablets: ~"only screen and (min-width: 479px) and (max-width: 959px)";

html, body { height: 100%; }
body {
    background-color: @background-color;
    font-size: 1.6rem;
    margin: 0;
     #chartjs-tooltip {
        opacity: 1;
        z-index:999;
        position: absolute;
        background: rgba(0, 0, 0, 1);
        color: white;
        border-radius: 3px;
        -webkit-transition: all .1s ease;
        transition: all .1s ease;
        pointer-events: none;
        -webkit-transform: translate(-50%, 0);
        transform: translate(-50%, 0);
	}
	.chartjs-tooltip-key {
		display: inline-block;
		width: 10px;
		height: 10px;
		margin-right: 10px;
	}
}


@background-color: #F6F6F6;
@background-active-color: rgb(230,230,230);
@active-text-shadow: 1px 1px 1px rgba(158, 158, 158, 0.49);
@icon-width: 5rem;
@title-height: 4.5rem;
@connector-line: 1px solid rgba(140, 133, 133, 0.5);
@border-color: 1px solid rgba(136, 136, 136, 0.21);
@in-store-border-color: #4ab04d;
@vicinity-border-color: #31459b;
@wifi-users-border-color: #e75349;

.saturate {-webkit-filter: saturate(3); filter: saturate(3);}
.grayscale {-webkit-filter: grayscale(100%); filter: grayscale(100%);}
.contrast {-webkit-filter: contrast(160%); filter: contrast(160%);}
.brightness {-webkit-filter: brightness(0.25); filter: brightness(0.25);}
.blur {-webkit-filter: blur(3px); filter: blur(3px);}
.invert {-webkit-filter: invert(100%); filter: invert(100%);}
.sepia {-webkit-filter: sepia(100%); filter: sepia(100%);}
.huerotate {-webkit-filter: hue-rotate(180deg); filter: hue-rotate(180deg);}
.rss.opacity {-webkit-filter: opacity(50%); filter: opacity(50%);}

.display-none { display: none !important; }

a:focus {
    text-decoration: none;
    outline: unset;
}

#loader, .card-loader {
    .deactivate {
        position: fixed; 
        width: 100%; 
        height: 100%;
        background-color: white; 
        top: 0;
        left: 0; 
        opacity: 0.6;
        z-index: 99;
    }

    .img-section {
        @media @smartphones, @tablets {
            height: 75%;
        }
        position: fixed; 
        left: 50%;
        top: 32%;
        z-index: 100;
        > div {
            position: relative; 
            left: -50%;
        }
    }
}

.selectize-input {
    background: white !important;
    border-radius: 0.1rem !important;
    border: 1px solid rgba(136, 136, 136, 0.5) !important;
    box-shadow: 1px 1px 0 0 rgba(136, 136, 136, 0.21);
    &.selectize-focus { border: 1px solid rgba(136, 136, 136, 0.5) !important; }
    &.focus { box-shadow: 1px 1px 0 0 rgba(136, 136, 136, 0.21); }
}
.selectize-dropdown {
    border: 1px solid rgba(136, 136, 136, 0.5) !important;
    box-shadow: 1px 1px 0 0 rgba(136, 136, 136, 0.21);
}

.position-tlbr(@position, @top, @left, @bottom, @right) {
    position: @position;
    top: @top;
    left: @left;
    bottom: @bottom;
    right: @right;
}

.circle {
    border: 0.5rem solid #F6F6F6;
    border-radius: 100rem;  
    box-shadow: 0 0 2px #888;
    display: inline-block;
    position: relative;
    height: 5rem;
    width: 5rem;
    
    @media @smartphones {
        font-size: 1rem;
    }

    .content {
        position: absolute;
        width: 100%;
        height:100%;
        text-align: center;
        display: table;

        span.data {
            display: table-cell;
            vertical-align: middle;
            font-weight: bold;
        }
    }
}

.notes {
    font-size: 1.3rem;
    margin-bottom: 10px;
}

.sub-container-background {
    height: 100%;
    min-height: 30rem;
    background-color: white;
    text-align: center;
    line-height: 1;
    padding: 1rem;
    margin-top: 1rem;
    overflow-x: auto;
    width: 100%;
    margin-bottom: 2rem;
    font-size: 1.8rem;
    border: @border-color;
    
    h5, h6 { 
        text-align: justify;
        i.glyphicon {
            color :rgba(136,136,136,0.80);
            vertical-align: text-top;
        }
    }
    h4.header { 
        color :rgba(136 ,136 ,136 ,0.8);
        @media @smartphones, @tablets { font-size: 1.5rem; }
    }

    .sub-header {
        text-align: justify;
        position: relative;

        h5 { 
            display: inline-block;
            @media @smartphones { margin: 0; }
            @media @smartphones, @tablets { font-size: 1.3rem; }
        }

        .define {
            font-size: 1.5rem;
            color: #ababab;
            display: inline-block;
            text-align: left;
            line-height: 1.4;
            width: 50rem
        }

        .selectors {
            /*position: absolute;
            right: 0;
            top: 2.5rem;*/
            > div {
                padding: 0 1rem;
                width: 20rem;
                display: inline-block;

                @media @smartphones {
                    width: 9rem;
                    .selectize-input {
                        height: 2.5rem;
                        line-height: 1rem;
                        &:after {
                            top: 60%;
                            right: .25rem;
                        }
                    }
                }
            }

            .selectize-input {
                box-shadow: 1px 1px 0 0 #888888;

                &.selectize-focus { border: 1px solid rgba(136, 136, 136, 0.5) !important; }
            }
         }
    }
 }

.overview-square-card {
    position: relative;
    width: 14rem;
    height: 14rem;
    border: 1px solid rgba(136, 136, 136, 0.8);
    display: inline-block;
    text-align: center;
    padding: .5rem;
    margin-right: 2rem;

    .card-loader {
        .deactivate {
            position: absolute;
        }
        .img-section {
            position: absolute
        }
        img {
            width: 5rem;
        }
    }

    .glyphicon-question-sign { color: rgba(136, 136, 136, 0.8); }

    @media @smartphones {
        margin: 0 .2rem;
    }

    .card-header {
        position: absolute;
        width: 92%;
        font-size: 1rem;
        text-align: left;
        font-weight: 500;
        color: rgb(0, 34, 165);
    }

    .content {
        position: absolute;
        width: 92%;
        height:92%;
        text-align: center;
        display: table;

        div.data {
            display: table-cell;
            vertical-align: middle;
            font-size: 2.7rem;
            color: black;
            font-weight: 300;
            line-height: 0.8;

            .rate {
                font-size: 1.5rem;
                .glyphicon {
                    font-size: 2rem;
                    vertical-align: sub;
                    color: lawngreen;
                    &.glyphicon-triangle-bottom { color: red; }
                }
            }
        }
    }

    .card-footer {
        position: absolute;
        width: 92%;
        bottom :.5rem;
        font-size: x-small;
        color: black;
    }
}

.title {
    .position-tlbr(fixed, 0, 0, 0, 0);
    background-color: white;
    text-align: center;
    height: @title-height;
    z-index: 10;
    box-shadow: 0 0.0625rem 0.3125rem 0 #888888;

    img {
        position: absolute;
        top: .5rem;
        left: 47%;
        height: 3.5rem;
        image-rendering: -moz-crisp-edges;
        image-rendering: -o-crisp-edges;
        image-rendering: -webkit-optimize-contrast;
        -ms-interpolation-mode: nearest-neighbor;
    }

    .signout {
        position: absolute;
        top: 0;
        right: 0;
        padding: .5rem 1rem;
        cursor: pointer;
        -webkit-transition: font-size .2s ease-in-out;
        -moz-transition: font-size .2s ease-in-out;
        -o-transition: font-size .2s ease-in-out;
        transition: font-size .2s ease-in-out;

        @media (pointer:coarse) {
            color: black;
            font-size: 2rem;
        }
        &:hover {
            color: black;
            font-size: 2rem;
        }
    }

    .user-row {
        line-height: 4.5rem;
        height: inherit;
        width: 20rem;
        float: left;
        color: black;
        
        .icon-area {
            width: @icon-width;
            position: absolute;
            left: 0;
        }
        .label-name {
            position: absolute;
            left: @icon-width;
            -webkit-transition: line-height .4s ease-in-out;
            -moz-transition: line-height .4s ease-in-out;
            -o-transition: line-height .4s ease-in-out;
            transition: line-height .4s ease-in-out;
        }
        .label-username {
            opacity: 0;
            position: absolute;
            left: 10rem;
            font-size: 1.2rem;
            top: 1rem;
            -webkit-transition: all .5s ease-in-out;
            -moz-transition: all .5s ease-in-out;
            -o-transition: all .5s ease-in-out;
            transition: all .5s ease-in-out;
        }
        &:hover {
            .label-username {
                opacity: 1;
                left: @icon-width ;
                color: #666666;
            }
            .label-name {
                line-height: 3rem;
            }
        }
        
    }

    .messege {
        position: absolute;
        width: 100%;
        top: 0;
        opacity: 0;
        -webkit-transition: all .3s ease-in-out;
        -moz-transition: all .3s ease-in-out;
        -o-transition: all .3s ease-in-out;
        transition: all .3s ease-in-out;

        &.popup { 
            top: @title-height;
            opacity: 1;

            .appear {
                display: inline;
            }
        }
        span {
            white-space: nowrap;
            padding: 0 1rem;
            border-radius: 2px;
            display: none;
            -webkit-transition: top .5s ease-in-out;
            -moz-transition: top .5s ease-in-out;
            -o-transition: top .5s ease-in-out;
            transition: top .5s ease-in-out;
        }
        .success {
            background-color: #b6ff00;
        }
        .error {
            background-color: #ec7979;
        }
        .info {
            background-color: #ffd800;
        }
    }

    .links-group {
        @media @smartphones { display: none; }

        height: @title-height;
        line-height: 4.5rem;
        position: absolute;
        right: 5rem;
        background: rgba(136,136,136,0.80);
    
        .link {
            color: rgba(255, 255, 255, 0.75);
            margin: 0 1rem; 
            &:hover {
                color: white;
                text-decoration: none;
                display: inline-block;
                padding: 0 1rem;
            }
        }
    }
}

hr { border-top: @border-color; }

.backdrop {
    .position-tlbr(absolute, 0, 0, 0, 0);
    z-index: 5;
    background-color: rgba(0, 0, 0, 0.25);
}

.screen-detector {
    span { 
        display: none;
        opacity: 0;
    }
    .normal {
        opacity: 1;
        display: inline-block;
    }

    @media @smartphones {
        span.smartphones { 
            opacity: 1;
            display: inline-block;
        }
        span.normal {
            display: none;
            opacity: 0;
        }
    }

    @media @tablets {
        span.tablets { 
            opacity: 1;
            display: inline-block;
        }
        span.normal {
            display: none !important;
            opacity: 0 !important;
        }
    }
}

.menu {
    display: none;
    position: absolute;
    top: 4.8rem;
    left: 6rem;
    z-index: 12;
    cursor: pointer;

    .icon-area {
        width: 100%;
        height: 100%;
        span { vertical-align: bottom; }
    }

    &:hover { color:black; }
    &.move-this { display: none; }

    @media (pointer:coarse) { display: inline-block; }
    @media @smartphones { left: 1.7rem; z-index: 25;}
}

.left-side-icon-bar {
    width: @icon-width;
    height: 100%;
    position: fixed;
    top: @title-height + .1rem;
    z-index:15;
    text-align: center;
    background-color: white;
    border-right: @border-color;
    -webkit-transition: width .5s ease-in-out;
    -moz-transition: width .5s ease-in-out;
    -o-transition: width .5s ease-in-out;
    transition: width .5s ease-in-out;

    .row {
        line-height: @icon-width;
        height: @icon-width;
        margin: 0;

        &.user-row { 
            background-color: @background-color;
            cursor: auto !important;
            border-bottom: @border-color;
        }

        .label-area { 
            display: inline-block;
            position: absolute;
            left: @icon-width;
            white-space: nowrap;

            -webkit-transition: opacity .3s ease-out;
            -moz-transition: opacity .3s ease-out;
            -o-transition: opacity .3s ease-out;
            transition: opacity .3s ease-out;
            opacity: 0;
        }

        .icon-area {
            width: @icon-width;
            display: inline-block;
            position: absolute;
            left: 0;

            img {
                width: 2.5rem;
            }
        }

        &:hover {
            background-color: @background-color;
            color:black;
            cursor:pointer;
        }

        &.active {
            background-color: @background-active-color;
            color:black;
            text-shadow: @active-text-shadow;
        }
    }
  
    &:hover,
    &.hover-this {
        width: 20rem;
        .row .label-area {
            z-index: 12;
            opacity: 1;
            -webkit-transition: opacity 1s ease-in;
            -moz-transition: opacity 1s ease-in;
            -o-transition: opacity 1s ease-in;
            transition: opacity 1s ease-in;
        }
    }

    &:hover {
         @media (pointer:coarse) {
            width: @icon-width;
            .row .label-area {
                display: none;
            }
        }
    }

    @media @smartphones {
        width: 0;
        .row .icon-area {
            opacity: 0;
        }
        &:hover { 
            width: 0;
            .row {
                z-index: 3;
                display: none;
            }
        }
        &.hover-this {
            @media (pointer:coarse) {
                width: 20rem;
                .row .icon-area {
                    opacity: 1;
                    -webkit-transition: opacity 1s ease-in;
                    -moz-transition: opacity 1s ease-in;
                    -o-transition: opacity 1s ease-in;
                    transition: opacity 1s ease-in;
                }
            }
        }
    }
}

.daterangepicker {
    background-color: @background-active-color;
    padding: 1rem;
    .ranges {
        background-color: white;
        padding: 1rem;
        line-height: 2.5rem;
        border-radius: 4px;

        .applyBtn {
            color: #4ab04d;
        }
        .btn-sm {
            width: 47%;
            margin: 0 1px;
            border-radius: 0;
            color: white;
        }
        .cancelBtn  {
            background-color: grey;
        }
        li {
            border-radius: 0;
            border: none;
            color: unset;
            &.active { 
                border: none;
                color: black;
                text-shadow: @active-text-shadow;
                background-color: @background-active-color;
            }
            &:hover { 
                border: none;
                background-color: @background-color;
                color: black;
            }
        }
    }
    .daterangepicker_input {
        background-color: white !important;
        margin-bottom: 1rem;
    }
    .input-mini {
        border: 0;
        border-radius: 0; 
        background-color: white;
        
        -webkit-transition: unset;
        -moz-transition: unset;
        -o-transition: unset;
        transition: unset;

        &.active {
            border: 0;
            border-radius: 0; 
            background-color: white;

        }
        &:focus {
            box-shadow: none;
        }
    }
    .calendar-table {
        border: none !important;
        border-radius: 4px !important;
    }
       
    td {
        &.in-range {
            background-color: @background-color;
            color: black;
            text-shadow:@active-text-shadow;
        }
        &.active,&.active:hover {
            background-color: @background-active-color;
            color: black;
            text-shadow:@active-text-shadow;
        }
    }

    .calendar.left {
        margin-right: 1rem !important;
    }
}

.container {
    position: absolute;
    top: @title-height + .1rem;
    left: 0;
    bottom: 0;
    overflow-y: auto;
    padding: 2rem 6rem 0 10rem;
    width: 100%;
    min-width: 36rem;
    z-index: 4;

    @media @smartphones {
        padding: 2rem 3rem 0 6rem;
    }

    .no-location-selected { text-align: center; }

    .date-range-picker {
        height: 6rem;
        width: 100%;
        margin-bottom: 2rem;
        margin: 0;
        
        .date-range-btn {
            .btn-primary {
                background-color: red;
                border-radius: 0.1rem;

                &:active {
                    background-color: rgba(255, 0, 0, 0.5);
                    background-image: none;
                }
            }
            @media @smartphones {
                .btn-primary {
                    width: inherit;
                    font-size: 1.15rem;
                }
            }
        }
        
        > div { 
            display: inline-block;
            height: 95%;
            vertical-align: bottom;
        }

        .from-to-dates {
            width: 17rem;
            margin-left: 4rem;
            vertical-align: super;
            .date {
                float: right;
                width: 12rem;
                color: green;
                font-style: italic;
                font-size: 1.3rem;
                font-family: monospace;
            }
            @media @smartphones {
                width: 14rem;
                .date { width: 9rem; }
            }
            > div {
                line-height: 3rem;
            }
        }

        .about-period {
            float: right;
            vertical-align: top;
            > div {
                width: 22rem;
            }
            span {
                float: right;
                width: 3rem;
                color: green;
                font-style: italic;
                font-family: monospace;
            }
            @media @smartphones {
                float: left;
                vertical-align: top;
                width: 100%;
            }
        }

        @media @smartphones {
            text-align: justify;
            margin: 0;
        }
    }
} 

.selects {
    width: 100%;

    i.glyphicon {
        position: absolute;
        z-index: 2;
        top: 3.1rem;
        left: 11rem;
        opacity: .5;
        &:hover {
            opacity: 0.8;
        }
        @media @smartphones { left: 7rem; }
    }

    > .ui-select-container {
        width: 100%;
        margin-bottom: 1rem;

        .caret {
            display: none !important;
        }

        .selectize-input {
            padding-left: 3rem;
        }

        .selectize-input:after {
            position: unset;
            margin-top: 0;
            border-style: unset;
        }
    }
   
    > .selectize-control {
        &.single .selectize-input [data-value] {
            background-color: transparent;
            border: none;
            padding-right: 0 !important;
        }

        .selectize-input {
            padding-left: 3rem !important;
            [data-value] {
                background-image: none;
                background-color: @background-color;
                color: #666666;
                text-shadow: unset;
                border-radius: 2px;
                border: @border-color;
                box-shadow: none;
                &.item {
                    .storeName {
                        font-weight: bold;
                    }
                    .city {
                        font-style: italic;
                        font-size: smaller;
                        font-family: monospace;
                    }
                }
                &.active {
                    background-image: none;
                    background-color: @background-active-color;
                    border: @border-color;
                    color: black;
                }

                .remove {
                    border-left: @border-color;
                }
            }

            &::after {
                content: none;
            }
        }
        .selectize-dropdown {
            .selectize-dropdown-content {
                .create.active,
                [data-value].active {
                    background-image: none;
                    background-color: @background-color;
                }

                [data-value] {
                    .primary {

                    }
                    .secondary {
                        font-size: 1rem;
                        opacity: 0.8;
                        span { margin-right: 2rem;}
                    }
                }
            }
        }
    } 

    .location {
        > .ui-select-container {
            display: inline-block;
            margin-right: 2rem;
            width: 30rem;
        }
    }
}

.overview-container {
   .sub-container-background;

   .in-vicinity {
       text-align: justify;
       .overview-square-card {
            border-color: @vicinity-border-color;
       }
   }

   .in-store {
       text-align: justify;
       .overview-square-card {
            border-color: @in-store-border-color;
       }
   }

   .wifi-users {
       text-align: justify;
       .overview-square-card {
            border-color: @wifi-users-border-color;
       }
   }
}

.home-container {
    .sub-container-background;
    
    .label { color: inherit; }
    .vicinity {
        &.circle {
            height:14rem;
            width: 14rem;
            border-color: @vicinity-border-color;
            @media @smartphones {
                width: 9rem;
                height: 9rem;
            }
        }
    }

    .line {
        line-height: 0;
        display:inline-block;
        width: 20rem;
        height: 100%;
        @media @smartphones {
            width: 10rem;
        }
    }

    .vicinity-breakup {
        height: 18rem;
        position: relative;

        @media @smartphones {
            height: 10rem;
        }

        .circle {
            width: 8rem;
            height:8rem;
            border-width: 0.4rem;
            vertical-align: sub;

            @media @smartphones {
                width: 6rem;
                height: 6rem;
            }
        }
        
        .in-store.circle {
            border-color: @in-store-border-color;
            .label {
                .position-tlbr(absolute, 2.55rem, -20rem, 0, 0);
                @media @smartphones { .position-tlbr(absolute, -1.5rem, -0.5rem, 0, 0); }
            }
        }

        .passer-by.circle {
            border-color: orangered;
            .label {
                .position-tlbr(absolute, 2.55rem, 8rem, 0, 0);
                @media @smartphones { .position-tlbr(absolute, -1.5rem, 0, 0, 0); }
            }
        }

        .line {
            margin: 0 -.4rem;
            .vicinity-verticle-line {
                > div {
                    height: 9rem;
                    width: 50%;
                    @media @smartphones {
                        height: 5rem;
                    }
                }
                > div:nth-child(1) {
                    border-right: @connector-line;
                }
                    
            }

            > div:nth-child(2) {
                border-top: @connector-line;
                height: 3.5rem;
            }
            > div {
                margin: 0;
                padding: 0;
                width: 100%;
                height: 50%;
                display: inline-block;
            }
        }
    }
    .verticle-line-zone {
        height: 5rem;
        position: absolute;
        bottom: 0;
        right: 50%;
        @media @smartphones { 
            height: 3rem;
            bottom: -1.6rem;    
        }
        
        .verticle-line {
            border-left: @connector-line;
            height: 100%;
            position: absolute;
            left: -14rem;
            @media @smartphones { left: -8.2rem; }
        }    
    }
    .in-store-breakup {
        @media @smartphones { margin-top: 1.5rem; }
        .wifi-users + .line {
            width: 27.5rem;
            @media @smartphones { width: 16rem; }
        }
    }
    

    .wifi-users {
        &.circle {
            border-width: 0.3rem;
            border-color: @wifi-users-border-color;
            width: 6rem;
            height: 6rem;
            @media @smartphones {
                width: 4rem;
                height: 4rem;
            }

            .label {
                .position-tlbr(absolute, 1.8rem, -15rem, 0, 0);
                @media @smartphones { .position-tlbr(absolute, 1.1rem, 3.5rem, 0, 0); }
            }
        }
    }
    
}

.trends-container {
    .sub-container-background;
    
    .graphs {
        overflow-x: auto;
        width: 100%;
        .graph-container {
            padding: 2rem;
            display: inline-block;

            canvas {
                width: 50rem !important;
                height: 25rem !important;
            }
        }
    }
    .sub-header .selectors {
        float: right;
        margin-top: 10px;
    }
        
    .sub-header>div.row {
        margin: 0;
    }
    .sub-header>div.row .col-md-6 {
        padding: 0;
    }

    .graph-header .col-md-2.dropdown{
        text-align: right;
    }

    .graph-header button {
        background: #fff;
        font-size: 1.3rem;
        border: 1px solid #ccc;
        color: #666666;
        padding: 3px 3px 2px;
        cursor: pointer;
        margin: 10px 0px;
    }
}

.hourly-trends-container { 
     overflow-x: unset !important;
    .sub-container-background;
    .graph-container {
        width: 100%;
        background-color: white;
        
        @media @smartphones, @tablets {
            overflow-x: auto;
            height: 34rem;
        }
    }
    .hourly-trend {
        margin-top: 2rem;
        @media @smartphones, @tablets {
            font-size:1.2rem;
            min-width: 40rem;
        }

        .day {
            height: 6rem;
            border: @border-color;
            display: inline-block;
            @media @smartphones, @tablets { height: 4rem; }

            .day-name {
                display: inline-block;
                width: 10rem;
                height: 6rem;
                margin-right: -0.4rem;
                @media @smartphones, @tablets { 
                    width: 6rem;
                    height: 4rem;
                }
            }

            .non.header {
                font-size: 1.2rem;
            }

            .hour-cell {
               display: inline-block;
               width: 6rem;
               height: 6rem;
               border-left: @border-color;
               @media @smartphones, @tablets { 
                   width: 4rem;
                   height: 4rem;
               }

               
            }

            .content {
                    width: inherit;
                    height: 6rem;
                    text-align: center;
                    display: table;

                    @media @smartphones, @tablets {
                        height: 4rem;
                    }

                    svg {
                        height: 6rem;
                        width: 6rem;
                        @media @smartphones, @tablets {
                            height: 4rem;
                            width: 4rem;
                        }
                    }
                    
                    .data {
                        display: table-cell;
                        vertical-align: middle;
                        font-family: monospace;
                        font-size: 1.2rem;
                    }
                }
        }
    }
    .dropdown {
        text-align: right;
    }

    .dropdown button {
        background: #fff;
        font-size: 1.3rem;
        border: 1px solid #ccc;
        color: #666666;
        padding: 3px 3px 2px;
        cursor: pointer;
    }
}

.loyality-container {
    .trends-container;
}

.distributions-container {
    .trends-container;
    .graph {
        border: @border-color;
    }
}

.csc-container {
    height: 100%;
    min-height: 30rem;
    background-color: white;
    text-align: center;
    line-height: 1;
    padding: 1rem;
    margin-top: 1rem;
    overflow-x: auto;
    width: 100%;
    margin-bottom: 2rem;
    font-size: 1.8rem;
    border: 1px solid rgba(136, 136, 136, 0.21);

    .metric-selector {
        width: 25rem !important;
    }
    .graphs,.graph_row{
        margin: 0;
    }
    .graph {
        margin-top: 2rem;
        width: 100% !important;
    }
    .dropdown {
        text-align: right;
    }

    .graph-header button {
        background: #fff;
        font-size: 1.3rem;
        border: 1px solid #ccc;
        color: #666666;
        padding: 3px 3px 2px;
        cursor: pointer;
    }
}


