//! AlaSQL v0.3.3 | © 2014-2016 <PERSON><PERSON> & Mathias <PERSON> | License: MIT 
!function(e,t){"function"==typeof define&&define.amd?define([],t):"object"==typeof exports?module.exports=t():e.alasql=t()}(this,function(){function e(e){return"(y="+e+",y===y?y:undefined)"}function t(e,t){return"(y="+e+',typeof y=="undefined"?undefined:'+t+")"}function n(){return!0}function r(){}function a(){var e=navigator.userAgent.toLowerCase();return e.indexOf("msie")!==-1&&parseInt(e.split("msie")[1])}function i(e,t,n){function r(e,n,a){var o,u,c,l=e[n],h=1e5;if(l.selid){if("PATH"===l.selid){for(var d=[{node:a,stack:[]}],f={},p=A.databases[A.useid].objects;d.length>0;){var b=d.shift(),E=b.node,g=b.stack,c=r(l.args,0,E);if(c.length>0){if(n+1+1>e.length)return g;var m=[];return g&&g.length>0&&g.forEach(function(t){m=m.concat(r(e,n+1,t))}),m}"undefined"==typeof f[E.$id]&&(f[E.$id]=!0,E.$out&&E.$out.length>0&&E.$out.forEach(function(e){var t=p[e],n=g.concat(t);n.push(p[t.$out[0]]),d.push({node:p[t.$out[0]],stack:n})}))}return[]}if("NOT"===l.selid){var u=r(l.args,0,a);return u.length>0?[]:n+1+1>e.length?[a]:r(e,n+1,a)}if("DISTINCT"===l.selid){var u;if(u="undefined"==typeof l.args||0===l.args.length?G(a):r(l.args,0,a),0===u.length)return[];var v=G(u);return n+1+1>e.length?v:r(e,n+1,v)}if("AND"===l.selid){var v=!0;return l.args.forEach(function(e){v=v&&r(e,0,a).length>0}),v?n+1+1>e.length?[a]:r(e,n+1,a):[]}if("OR"===l.selid){var v=!1;return l.args.forEach(function(e){v=v||r(e,0,a).length>0}),v?n+1+1>e.length?[a]:r(e,n+1,a):[]}if("ALL"===l.selid){var u=r(l.args[0],0,a);return 0===u.length?[]:n+1+1>e.length?u:r(e,n+1,u)}if("ANY"===l.selid){var u=r(l.args[0],0,a);return 0===u.length?[]:n+1+1>e.length?[u[0]]:r(e,n+1,[u[0]])}if("UNIONALL"===l.selid){var u=[];return l.args.forEach(function(e){u=u.concat(r(e,0,a))}),0===u.length?[]:n+1+1>e.length?u:r(e,n+1,u)}if("UNION"===l.selid){var u=[];l.args.forEach(function(e){u=u.concat(r(e,0,a))});var u=G(u);return 0===u.length?[]:n+1+1>e.length?u:r(e,n+1,u)}if("IF"===l.selid){var u=r(l.args,0,a);return 0===u.length?[]:n+1+1>e.length?[a]:r(e,n+1,a)}if("REPEAT"===l.selid){var S,T,y=l.args[0].value;T=l.args[1]?l.args[1].value:y,l.args[2]&&(S=l.args[2].variable);var N=[];if(0===y&&(n+1+1>e.length?N=[a]:(S&&(A.vars[S]=0),N=N.concat(r(e,n+1,a)))),T>0)for(var C=[{value:a,lvl:1}],R=0;C.length>0;){var u=C[0];if(C.shift(),u.lvl<=T){S&&(A.vars[S]=u.lvl);var O=r(l.sels,0,u.value);O.forEach(function(e){C.push({value:e,lvl:u.lvl+1})}),u.lvl>=y&&(n+1+1>e.length?N=N.concat(O):O.forEach(function(t){N=N.concat(r(e,n+1,t))}))}if(R++,R>h)throw new Error("Security brake. Number of iterations = "+R)}return N}if("OF"===l.selid){if(n+1+1>e.length)return[a];var w=[];return Object.keys(a).forEach(function(t){A.vars[l.args[0].variable]=t,w=w.concat(r(e,n+1,a[t]))}),w}if("TO"===l.selid){var I=A.vars[l.args[0]],x=[];if(x=void 0!==I?I.slice(0):[],x.push(a),n+1+1>e.length)return[a];A.vars[l.args[0]]=x;var w=r(e,n+1,a);return A.vars[l.args[0]]=I,w}if("ARRAY"===l.selid){var u=r(l.args,0,a);return u.length>0?(o=u,n+1+1>e.length?[o]:r(e,n+1,o)):[]}if("SUM"===l.selid){var u=r(l.args,0,a);if(!(u.length>0))return[];var o=u.reduce(function(e,t){return e+t},0);return n+1+1>e.length?[o]:r(e,n+1,o)}if("AVG"===l.selid)return u=r(l.args,0,a),u.length>0?(o=u.reduce(function(e,t){return e+t},0)/u.length,n+1+1>e.length?[o]:r(e,n+1,o)):[];if("COUNT"===l.selid)return u=r(l.args,0,a),u.length>0?(o=u.length,n+1+1>e.length?[o]:r(e,n+1,o)):[];if("FIRST"===l.selid)return u=r(l.args,0,a),u.length>0?(o=u[0],n+1+1>e.length?[o]:r(e,n+1,o)):[];if("LAST"===l.selid)return u=r(l.args,0,a),u.length>0?(o=u[u.length-1],n+1+1>e.length?[o]:r(e,n+1,o)):[];if("MIN"===l.selid){if(u=r(l.args,0,a),0===u.length)return[];var o=u.reduce(function(e,t){return Math.min(e,t)},1/0);return n+1+1>e.length?[o]:r(e,n+1,o)}if("MAX"===l.selid){var u=r(l.args,0,a);if(0===u.length)return[];var o=u.reduce(function(e,t){return Math.max(e,t)},-(1/0));return n+1+1>e.length?[o]:r(e,n+1,o)}if("PLUS"===l.selid){var N=[],C=r(l.args,0,a).slice();n+1+1>e.length?N=N.concat(C):C.forEach(function(t){N=N.concat(r(e,n+1,t))});for(var R=0;C.length>0;){var u=C.shift();if(u=r(l.args,0,u),C=C.concat(u),n+1+1>e.length?N=N.concat(u):u.forEach(function(t){var a=r(e,n+1,t);N=N.concat(a)}),R++,R>h)throw new Error("Security brake. Number of iterations = "+R)}return N}if("STAR"===l.selid){var N=[];N=r(e,n+1,a);var C=r(l.args,0,a).slice();n+1+1>e.length?N=N.concat(C):C.forEach(function(t){N=N.concat(r(e,n+1,t))});for(var R=0;C.length>0;){var u=C[0];if(C.shift(),u=r(l.args,0,u),C=C.concat(u),n+1+1<=e.length&&u.forEach(function(t){N=N.concat(r(e,n+1,t))}),R++,R>h)throw new Error("Loop brake. Number of iterations = "+R)}return N}if("QUESTION"===l.selid){var N=[];N=N.concat(r(e,n+1,a));var u=r(l.args,0,a);return n+1+1<=e.length&&u.forEach(function(t){N=N.concat(r(e,n+1,t))}),N}if("WITH"!==l.selid){if("ROOT"===l.selid)return n+1+1>e.length?[a]:r(e,n+1,s);throw new Error("Wrong selector "+l.selid)}var u=r(l.args,0,a);if(0===u.length)return[];var c={status:1,values:u}}else{if(!l.srchid)throw new Error("Selector not found");var c=A.srch[l.srchid.toUpperCase()](a,l.args,i,t)}"undefined"==typeof c&&(c={status:1,values:[a]});var v=[];if(1===c.status){var D=c.values;if(n+1+1>e.length)v=D;else for(var R=0;R<c.values.length;R++)v=v.concat(r(e,n+1,D[R]))}return v}var a,s,i={},o=P(this.selectors);if(void 0!==o&&o.length>0&&(o&&o[0]&&"PROP"===o[0].srchid&&o[0].args&&o[0].args[0]&&("XML"===o[0].args[0].toUpperCase()?(i.mode="XML",o.shift()):"HTML"===o[0].args[0].toUpperCase()?(i.mode="HTML",o.shift()):"JSON"===o[0].args[0].toUpperCase()&&(i.mode="JSON",o.shift())),o.length>0&&"VALUE"===o[0].srchid&&(i.value=!0,o.shift())),this.from instanceof z.Column){var u=this.from.databaseid||e;s=A.databases[u].tables[this.from.columnid].data}else if(this.from instanceof z.FuncValue&&A.from[this.from.funcid.toUpperCase()]){var c=this.from.args.map(function(e){var n=e.toJS(),r=new Function("params,alasql","var y;return "+n).bind(this);return r(t,A)});s=A.from[this.from.funcid.toUpperCase()].apply(this,c)}else if("undefined"==typeof this.from)s=A.databases[e].objects;else{var l=new Function("params,alasql","var y;return "+this.from.toJS());s=l(t,A),"object"==typeof Mongo&&"object"!=typeof Mongo.Collection&&s instanceof Mongo.Collection&&(s=s.find().fetch())}if(a=void 0!==o&&o.length>0?r(o,0,s):s,this.into){var h,d;"undefined"!=typeof this.into.args[0]&&(h=new Function("params,alasql","var y;return "+this.into.args[0].toJS())(t,A)),"undefined"!=typeof this.into.args[1]&&(d=new Function("params,alasql","var y;return "+this.into.args[1].toJS())(t,A)),a=A.into[this.into.funcid.toUpperCase()](h,d,a,[],n)}else i.value&&a.length>0&&(a=a[0]),n&&(a=n(a));return a}function o(e,t,n,r,a){e.sources.length;e.sourceslen=e.sources.length;var s=e.sourceslen;e.query=e,e.A=r,e.B=a,e.cb=n,e.oldscope=t,e.queriesfn&&(e.sourceslen+=e.queriesfn.length,s+=e.queriesfn.length,e.queriesdata=[],e.queriesfn.forEach(function(t,n){t.query.params=e.params,u([],-n-1,e)}));var i;i=t?P(t):{},e.scope=i;var o;return e.sources.forEach(function(t,n){t.query=e;var r=t.datafn(e,e.params,u,n,A);"undefined"!=typeof r&&((e.intofn||e.intoallfn)&&r instanceof Array&&(r=r.length),o=r),t.queriesdata=e.queriesdata}),0!=e.sources.length&&0!==s||(o=c(e)),o}function u(e,t,n){if(t>=0){var r=n.sources[t];r.data=e,"function"==typeof r.data&&(r.getfn=r.data,r.dontcache=r.getfn.dontcache,"OUTER"!=r.joinmode&&"RIGHT"!=r.joinmode&&"ANTI"!=r.joinmode||(r.dontcache=!1),r.data={})}else n.queriesdata[-t-1]=B(e);if(n.sourceslen--,!(n.sourceslen>0))return c(n)}function c(e){var t,n=e.scope;te(e),e.data=[],e.xgroups={},e.groups=[];var r=0;if(d(e,n,r),e.groupfn){if(e.data=[],0===e.groups.length){var a={};e.selectGroup.length>0&&e.selectGroup.forEach(function(e){"COUNT"==e.aggregatorid||"SUM"==e.aggregatorid?a[e.nick]=0:a[e.nick]=void 0}),e.groups=[a]}if(e.aggrKeys.length>0){var s="";e.aggrKeys.forEach(function(e){s+="g['"+e.nick+"']=alasql.aggr['"+e.funcid+"'](undefined,g['"+e.nick+"'],3);"});var i=new Function("g,params,alasql","var y;"+s)}for(var o=0,u=e.groups.length;o<u;o++){var a=e.groups[o];if(i&&i(a,e.params,A),!e.havingfn||e.havingfn(a,e.params,A)){var c=e.selectgfn(a,e.params,A);e.data.push(c)}}}if(h(e),e.unionallfn){var f,p;if(e.corresponding)e.unionallfn.query.modifier||(e.unionallfn.query.modifier=void 0),f=e.unionallfn(e.params);else{e.unionallfn.query.modifier||(e.unionallfn.query.modifier="RECORDSET"),p=e.unionallfn(e.params),f=[],u=p.data.length;for(var o=0;o<u;o++){for(var b={},E=Math.min(e.columns.length,p.columns.length)-1;0<=E;E--)b[e.columns[E].columnid]=p.data[o][p.columns[E].columnid];f.push(b)}}e.data=e.data.concat(f)}else if(e.unionfn){if(e.corresponding)e.unionfn.query.modifier||(e.unionfn.query.modifier="ARRAY"),f=e.unionfn(e.params);else{e.unionfn.query.modifier||(e.unionfn.query.modifier="RECORDSET"),p=e.unionfn(e.params),f=[],u=p.data.length;for(var o=0;o<u;o++){b={},t=Math.min(e.columns.length,p.columns.length);for(var E=0;E<t;E++)b[e.columns[E].columnid]=p.data[o][p.columns[E].columnid];f.push(b)}}e.data=U(e.data,f)}else if(e.exceptfn){if(e.corresponding){e.exceptfn.query.modifier||(e.exceptfn.query.modifier="ARRAY");var f=e.exceptfn(e.params)}else{e.exceptfn.query.modifier||(e.exceptfn.query.modifier="RECORDSET");for(var p=e.exceptfn(e.params),f=[],o=0,u=p.data.length;o<u;o++){for(var b={},E=Math.min(e.columns.length,p.columns.length)-1;0<=E;E--)b[e.columns[E].columnid]=p.data[o][p.columns[E].columnid];f.push(b)}}e.data=_(e.data,f)}else if(e.intersectfn){if(e.corresponding)e.intersectfn.query.modifier||(e.intersectfn.query.modifier=void 0),f=e.intersectfn(e.params);else for(e.intersectfn.query.modifier||(e.intersectfn.query.modifier="RECORDSET"),p=e.intersectfn(e.params),f=[],u=p.data.length,o=0;o<u;o++){for(b={},t=Math.min(e.columns.length,p.columns.length),E=0;E<t;E++)b[e.columns[E].columnid]=p.data[o][p.columns[E].columnid];f.push(b)}e.data=F(e.data,f)}if(e.orderfn){if(e.explain)var g=Date.now();e.data=e.data.sort(e.orderfn),e.explain&&e.explaination.push({explid:e.explid++,description:"QUERY BY",ms:Date.now()-g})}if(l(e),"undefined"!=typeof angular&&e.removeKeys.push("$$hashKey"),e.removeKeys.length>0){var m=e.removeKeys;if(t=m.length,t>0)for(u=e.data.length,o=0;o<u;o++)for(E=0;E<t;E++)delete e.data[o][m[E]];e.columns.length>0&&(e.columns=e.columns.filter(function(e){var t=!1;return m.forEach(function(n){e.columnid==n&&(t=!0)}),!t}))}if("undefined"!=typeof e.removeLikeKeys&&e.removeLikeKeys.length>0){for(var v=e.removeLikeKeys,o=0,u=e.data.length;o<u;o++){b=e.data[o];for(var S in b)for(E=0;E<e.removeLikeKeys.length;E++)A.utils.like(e.removeLikeKeys[E],S)&&delete b[S]}e.columns.length>0&&(e.columns=e.columns.filter(function(e){var t=!1;return v.forEach(function(n){A.utils.like(n,e.columnid)&&(t=!0)}),!t}))}if(e.pivotfn&&e.pivotfn(),e.unpivotfn&&e.unpivotfn(),e.intoallfn){var T=e.intoallfn(e.columns,e.cb,e.params,e.alasql);return T}if(e.intofn){for(u=e.data.length,o=0;o<u;o++)e.intofn(e.data[o],o,e.params,e.alasql);return e.cb&&e.cb(e.data.length,e.A,e.B),e.data.length}return T=e.data,e.cb&&(T=e.cb(e.data,e.A,e.B)),T}function l(e){if(e.limit){var t=0;e.offset&&(t=(0|e.offset)-1||0);var n;n=e.percent?(e.data.length*e.limit/100|0)+t:(0|e.limit)+t,e.data=e.data.slice(t,n)}}function h(e){if(e.distinct){for(var t={},n=0,r=e.data.length;n<r;n++){var a=Object.keys(e.data[n]).map(function(t){return e.data[n][t]}).join("`");t[a]=e.data[n]}e.data=[];for(var s in t)e.data.push(t[s])}}function d(e,t,n){if(n>=e.sources.length)e.wherefn(t,e.params,A)&&(e.groupfn?e.groupfn(t,e.params,A):e.data.push(e.selectfn(t,e.params,A)));else if(e.sources[n].applyselect){var r=e.sources[n];r.applyselect(e.params,function(a){if(a.length>0)for(var s=0;s<a.length;s++)t[r.alias]=a[s],d(e,t,n+1);else"OUTER"==r.applymode&&(t[r.alias]={},d(e,t,n+1))},t)}else{var r=e.sources[n],a=e.sources[n+1],s=r.alias||r.tableid,i=!1,o=r.data,u=!1;(!r.getfn||r.getfn&&!r.dontcache)&&"RIGHT"!=r.joinmode&&"OUTER"!=r.joinmode&&"ANTI"!=r.joinmode&&"ix"==r.optimization&&(o=r.ix[r.onleftfn(t,e.params,A)]||[],u=!0);var c=0;if("undefined"==typeof o)throw new Error("Data source number "+n+" in undefined");for(var l,h=o.length;(l=o[c])||!u&&r.getfn&&(l=r.getfn(c))||c<h;)u||!r.getfn||r.dontcache||(o[c]=l),t[s]=l,r.onleftfn&&r.onleftfn(t,e.params,A)!=r.onrightfn(t,e.params,A)||r.onmiddlefn(t,e.params,A)&&("SEMI"!=r.joinmode&&"ANTI"!=r.joinmode&&d(e,t,n+1),"LEFT"!=r.joinmode&&"INNER"!=r.joinmode&&(l._rightjoin=!0),i=!0),c++;if("LEFT"!=r.joinmode&&"OUTER"!=r.joinmode&&"SEMI"!=r.joinmode||i||(t[s]={},d(e,t,n+1)),n+1<e.sources.length&&("OUTER"==a.joinmode||"RIGHT"==a.joinmode||"ANTI"==a.joinmode)){t[r.alias]={};for(var l,f=0,p=a.data.length;(l=a.data[f])||a.getfn&&(l=a.getfn(f))||f<p;)a.getfn&&!a.dontcache&&(a.data[f]=l),l._rightjoin?delete l._rightjoin:0==n&&(t[a.alias]=l,d(e,t,n+2)),f++}t[s]=void 0}}function f(e,t){if("undefined"==typeof t||"number"==typeof t||"string"==typeof t||"boolean"==typeof t)return t;var n=e.modifier||A.options.modifier,r=e.columns;if("undefined"==typeof r||0==r.length)if(t.length>0){for(var a={},s=Math.min(t.length,A.options.columnlookup||10)-1;0<=s;s--)for(var i in t[s])a[i]=!0;r=Object.keys(a).map(function(e){return{columnid:e}})}else r=[];if("VALUE"===n)if(t.length>0){var i;i=r&&r.length>0?r[0].columnid:Object.keys(t[0])[0],t=t[0][i]}else t=void 0;else if("ROW"===n)if(t.length>0){var i,o=[];for(var i in t[0])o.push(t[0][i]);t=o}else t=void 0;else if("COLUMN"===n){var u=[];if(t.length>0){var i;i=r&&r.length>0?r[0].columnid:Object.keys(t[0])[0];for(var s=0,c=t.length;s<c;s++)u.push(t[s][i])}t=u}else if("MATRIX"===n){for(var u=[],s=0;s<t.length;s++){for(var o=[],l=t[s],h=0;h<r.length;h++)o.push(l[r[h].columnid]);u.push(o)}t=u}else if("INDEX"===n){var i,d,u={};if(r&&r.length>0)i=r[0].columnid,d=r[1].columnid;else{var f=Object.keys(t[0]);i=f[0],d=f[1]}for(var s=0,c=t.length;s<c;s++)u[t[s][i]]=t[s][d];t=u}else if("RECORDSET"===n)t=new A.Recordset({columns:r,data:t});else if("TEXTSTRING"===n){var i;i=r&&r.length>0?r[0].columnid:Object.keys(t[0])[0];for(var s=0,c=t.length;s<c;s++)t[s]=t[s][i];t=t.join("\n")}return t}function p(e,t,n){var r="",a=[];e.ixsources={},e.sources.forEach(function(t){e.ixsources[t.alias]=t});var s;if(e.ixsources[t])var s=e.ixsources[t].columns;return n&&"json"==A.options.joinstar&&(r+="r['"+t+"']={};"),s&&s.length>0?s.forEach(function(s){n&&"underscore"==A.options.joinstar?a.push("'"+t+"_"+s.columnid+"':p['"+t+"']['"+s.columnid+"']"):n&&"json"==A.options.joinstar?r+="r['"+t+"']['"+s.columnid+"']=p['"+t+"']['"+s.columnid+"'];":a.push("'"+s.columnid+"':p['"+t+"']['"+s.columnid+"']"),e.selectColumns[O(s.columnid)]=!0;var i={columnid:s.columnid,dbtypeid:s.dbtypeid,dbsize:s.dbsize,dbprecision:s.dbprecision,dbenum:s.dbenum};e.columns.push(i),e.xcolumns[i.columnid]=i}):(r+='var w=p["'+t+'"];for(var k in w){r[k]=w[k]};',e.dirtyColumns=!0),{s:a.join(","),sp:r}}function b(e,t){if(e instanceof Array){for(var n=[[]],r=0;r<e.length;r++)if(e[r]instanceof z.Column)e[r].nick=O(e[r].columnid),t.groupColumns[e[r].nick]=e[r].nick,n=n.map(function(n){return n.concat(e[r].nick+"\t"+e[r].toJS("p",t.sources[0].alias,t.defcols))});else if(e[r]instanceof z.FuncValue)t.groupColumns[O(e[r].toString())]=O(e[r].toString()),n=n.map(function(n){return n.concat(O(e[r].toString())+"\t"+e[r].toJS("p",t.sources[0].alias,t.defcols))});else if(e[r]instanceof z.GroupExpression)if("ROLLUP"==e[r].type)n=se(n,ne(e[r].group,t));else if("CUBE"==e[r].type)n=se(n,re(e[r].group,t));else{if("GROUPING SETS"!=e[r].type)throw new Error("Unknown grouping function");n=se(n,ae(e[r].group,t))}else n=""===e[r]?[["1\t1"]]:n.map(function(n){return t.groupColumns[O(e[r].toString())]=O(e[r].toString()),n.concat(O(e[r].toString())+"\t"+e[r].toJS("p",t.sources[0].alias,t.defcols))});return n}return e instanceof z.FuncValue?(t.groupColumns[O(e.toString())]=O(e.toString()),[e.toString()+"\t"+e.toJS("p",t.sources[0].alias,t.defcols)]):e instanceof z.Column?(e.nick=O(e.columnid),t.groupColumns[e.nick]=e.nick,[e.nick+"\t"+e.toJS("p",t.sources[0].alias,t.defcols)]):(t.groupColumns[O(e.toString())]=O(e.toString()),[O(e.toString())+"\t"+e.toJS("p",t.sources[0].alias,t.defcols)])}function E(e,t,n,r){var a="";if("string"==typeof e)a='"'+e+'"';else if("number"==typeof e)a="("+e+")";else if("boolean"==typeof e)a=e;else{if("object"!=typeof e)throw new Error("2Can not parse JSON object "+JSON.stringify(e));if(e instanceof Array)a+="["+e.map(function(e){return E(e,t,n,r)}).join(",")+"]";else if(!e.toJS||e instanceof z.Json){a="{";var s=[];for(var i in e){var o="";if("string"==typeof i)o+='"'+i+'"';else if("number"==typeof i)o+=i;else{if("boolean"!=typeof i)throw new Error("THis is not ES6... no expressions on left side yet");o+=i}o+=":"+E(e[i],t,n,r),s.push(o)}a+=s.join(",")+"}"}else{if(!e.toJS)throw new Error("1Can not parse JSON object "+JSON.stringify(e));a=e.toJS(t,n,r)}}return a}function g(e){var t="";if(void 0===e)t+="undefined";else if(e instanceof Array){t+="<style>",t+="table {border:1px black solid; border-collapse: collapse; border-spacing: 0px;}",t+="td,th {border:1px black solid; padding-left:5px; padding-right:5px}",t+="th {background-color: #EEE}",t+="</style>",t+="<table>";var n=[];for(var r in e[0])n.push(r);t+="<tr><th>#",n.forEach(function(e){t+="<th>"+e});for(var a=0,s=e.length;a<s;a++)t+="<tr><th>"+(a+1),n.forEach(function(n){t+="<td> ",e[a][n]==+e[a][n]?(t+='<div style="text-align:right">',t+="undefined"==typeof e[a][n]?"NULL":e[a][n],t+="</div>"):t+="undefined"==typeof e[a][n]?"NULL":"string"==typeof e[a][n]?e[a][n]:le(e[a][n])});t+="</table>"}else t+="<p>"+le(e)+"</p>";return t}function m(e,t,n){if(!(n<=0)){var r=t-e.scrollTop,a=r/n*10;setTimeout(function(){e.scrollTop!==t&&(e.scrollTop=e.scrollTop+a,m(e,t,n-10))},10)}}function S(e,t,n,r,a,s){var i={};n=n||{},A.utils.extend(i,n),"undefined"==typeof i.headers&&(i.headers=!0);var o;return A.utils.loadBinaryFile(t,!!r,function(t){var n,o=e.read(t,{type:"binary"});n="undefined"==typeof i.sheetid?o.SheetNames[0]:i.sheetid;var u;"undefined"==typeof i.range?u=o.Sheets[n]["!ref"]:(u=i.range,o.Sheets[n][u]&&(u=o.Sheets[n][u]));for(var c=u.split(":"),l=c[0].match(/[A-Z]+/)[0],h=+c[0].match(/[0-9]+/)[0],d=c[1].match(/[A-Z]+/)[0],f=+c[1].match(/[0-9]+/)[0],p={},b=A.utils.xlscn(l),E=A.utils.xlscn(d),g=b;g<=E;g++){var m=A.utils.xlsnc(g);i.headers&&o.Sheets[n][m+""+h]?p[m]=o.Sheets[n][m+""+h].v:p[m]=m}var v=[];i.headers&&h++;for(var S=h;S<=f;S++){for(var T={},g=b;g<=E;g++){var m=A.utils.xlsnc(g);o.Sheets[n][m+""+S]&&(T[p[m]]=o.Sheets[n][m+""+S].v)}v.push(T)}v.length>0&&v[v.length-1]&&0==Object.keys(v[v.length-1]).length&&v.pop(),r&&(v=r(v,a,s))},function(e){throw e}),o}function T(e){function t(){return{declaration:n(),root:r()}}function n(){var e=o(/^<\?xml\s*/);if(e){for(var t={attributes:{}};!u()&&!c("?>");){var n=s();if(!n)return t;t.attributes[n.name]=n.value}return o(/\?>\s*/),t}}function r(){var e=o(/^<([\w-:.]+)\s*/);if(e){for(var t={name:e[1],attributes:{},children:[]};!(u()||c(">")||c("?>")||c("/>"));){var n=s();if(!n)return t;t.attributes[n.name]=n.value}if(o(/^\s*\/>\s*/))return t;o(/\??>\s*/),t.content=a();for(var i;i=r();)t.children.push(i);return o(/^<\/[\w-:.]+>\s*/),t}}function a(){var e=o(/^([^<]*)/);return e?e[1]:""}function s(){var e=o(/([\w:-]+)\s*=\s*("[^"]*"|'[^']*'|\w+)\s*/);if(e)return{name:e[1],value:i(e[2])}}function i(e){return e.replace(/^['"]|['"]$/g,"")}function o(t){var n=e.match(t);if(n)return e=e.slice(n[0].length),n}function u(){return 0==e.length}function c(t){return 0==e.indexOf(t)}return e=e.trim(),e=e.replace(/<!--[\s\S]*?-->/g,""),t()}var A=function(e,t,n,r){if(t=t||[],"function"==typeof t&&(r=n,n=t,t=[]),"object"!=typeof t&&(t=[t]),"function"==typeof importScripts||!A.webworker){if(0===arguments.length)return new z.Select({columns:[new z.Column({columnid:"*"})],from:[new z.ParamValue({param:0})]});if(1===arguments.length&&"object"==typeof e&&e instanceof Array){var a=new z.Select({columns:[new z.Column({columnid:"*"})],from:[new z.ParamValue({param:0})]});return a.preparams=[e],a}return"string"==typeof e&&"#"===e[0]&&"object"==typeof document?e=document.querySelector(e).textContent:"object"==typeof e&&e instanceof HTMLElement?e=e.textContent:"function"==typeof e&&(e=e.toString().slice(14,-3)),A.exec(e,t,n,r)}var s=A.lastid++;A.buffer[s]=n,A.webworker.postMessage({id:s,sql:e,params:t})};A.version="0.3.3",A.debug=void 0;var y=function(){return null},N="",C=function(){function e(){this.yy={}}var t=function(e,t,n,r){for(n=n||{},r=e.length;r--;n[e[r]]=t);return n},n=[2,13],r=[1,104],a=[1,102],s=[1,103],i=[1,6],o=[1,42],u=[1,78],c=[1,75],l=[1,94],h=[1,93],d=[1,68],f=[1,101],p=[1,85],b=[1,70],E=[1,83],g=[1,65],m=[1,69],v=[1,63],S=[1,67],T=[1,60],y=[1,73],N=[1,61],C=[1,66],R=[1,82],O=[1,76],w=[1,84],x=[1,86],D=[1,87],L=[1,80],k=[1,81],$=[1,79],M=[1,88],U=[1,89],_=[1,90],F=[1,91],P=[1,92],q=[1,98],G=[1,64],V=[1,77],B=[1,71],j=[1,96],H=[1,97],J=[1,62],Y=[1,72],W=[1,108],X=[1,107],K=[10,302,599,761],Q=[10,302,306,599,761],z=[1,115],Z=[1,116],ee=[1,117],te=[1,118],ne=[1,119],re=[129,349,406],ae=[1,127],se=[1,126],ie=[1,132],oe=[1,162],ue=[1,173],ce=[1,176],le=[1,171],he=[1,179],de=[1,183],fe=[1,158],pe=[1,180],be=[1,167],Ee=[1,169],ge=[1,172],me=[1,181],ve=[1,164],Se=[1,191],Te=[1,186],Ae=[1,187],ye=[1,192],Ne=[1,193],Ce=[1,194],Re=[1,195],Oe=[1,196],we=[1,197],Ie=[1,198],xe=[1,199],De=[1,200],Le=[1,174],ke=[1,175],$e=[1,177],Me=[1,178],Ue=[1,184],_e=[1,190],Fe=[1,182],Pe=[1,185],qe=[1,170],Ge=[1,168],Ve=[1,189],Be=[1,201],je=[2,4,5],He=[2,462],Je=[1,204],Ye=[1,209],We=[1,218],Xe=[1,214],Ke=[10,71,77,92,97,117,127,161,167,168,182,197,231,244,246,302,306,599,761],Qe=[2,4,5,10,71,75,76,77,111,114,115,117,121,122,123,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,144,145,147,148,149,151,153,155,161,163,165,167,168,169,170,171,172,174,182,184,186,197,276,277,278,279,280,281,282,283,284,302,306,417,421,599,761],ze=[2,4,5,10,53,71,73,75,76,77,88,92,94,97,98,106,111,114,115,117,121,122,123,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,144,145,147,148,149,151,153,155,161,163,165,167,168,169,170,171,172,174,178,179,180,182,184,186,188,197,205,207,221,222,223,224,225,226,227,228,231,238,241,242,244,246,265,276,277,278,279,280,281,282,283,284,286,292,296,302,304,305,306,307,308,309,310,311,312,313,314,315,316,317,318,319,320,321,322,326,327,328,329,331,334,335,343,392,396,397,400,402,404,405,413,414,415,417,421,431,433,434,436,437,438,439,440,444,445,448,449,461,467,502,504,505,514,599,761],Ze=[1,248],et=[1,255],tt=[1,264],nt=[1,269],rt=[1,268],at=[2,4,5,10,71,76,77,92,97,106,117,127,130,131,136,142,144,148,151,153,155,161,167,168,178,179,180,182,197,231,244,246,264,265,266,267,269,276,277,278,279,280,281,282,283,284,286,287,288,289,290,291,292,293,294,295,298,299,302,306,308,313,417,421,599,761],st=[2,161],it=[1,280],ot=[10,73,77,302,306,502,599,761],ut=[2,4,5,10,53,71,73,75,76,77,88,92,94,97,98,106,111,114,115,117,121,122,123,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,144,145,147,148,149,151,153,155,161,163,165,167,168,169,170,171,172,174,178,179,180,182,184,186,188,192,197,205,207,221,222,223,224,225,226,227,228,229,230,231,238,241,242,244,246,265,276,277,278,279,280,281,282,283,284,286,292,293,296,302,304,305,306,307,308,309,310,311,312,313,314,315,316,317,318,319,320,321,322,326,327,328,329,331,334,335,337,339,343,352,364,365,366,369,370,382,385,392,396,397,398,399,400,401,402,404,405,413,414,415,417,421,423,430,431,433,434,436,437,438,439,440,444,445,448,449,461,467,502,504,505,511,512,513,514,599,761],ct=[2,4,5,10,53,71,88,123,145,155,188,265,286,302,331,334,335,343,392,396,397,400,402,404,405,413,414,415,431,433,434,436,437,438,439,440,444,445,448,449,502,504,505,514,599,761],lt=[1,561],ht=[1,563],dt=[2,494],ft=[1,568],pt=[1,579],bt=[1,582],Et=[1,583],gt=[10,77,88,131,136,145,188,292,302,306,467,599,761],mt=[10,73,302,306,599,761],vt=[2,558],St=[1,599],Tt=[2,4,5,155],At=[1,637],yt=[1,609],Nt=[1,643],Ct=[1,644],Rt=[1,617],Ot=[1,628],wt=[1,615],It=[1,623],xt=[1,616],Dt=[1,624],Lt=[1,626],kt=[1,618],$t=[1,619],Mt=[1,638],Ut=[1,635],_t=[1,636],Ft=[1,612],Pt=[1,614],qt=[1,606],Gt=[1,607],Vt=[1,608],Bt=[1,610],jt=[1,611],Ht=[1,613],Jt=[1,620],Yt=[1,621],Wt=[1,625],Xt=[1,627],Kt=[1,629],Qt=[1,630],zt=[1,631],Zt=[1,632],en=[1,633],tn=[1,639],nn=[1,640],rn=[1,641],an=[1,642],sn=[2,286],on=[2,4,5,10,53,71,73,75,76,77,88,92,94,97,98,106,111,114,115,117,121,122,123,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,144,145,147,148,149,151,153,155,161,163,165,167,168,169,170,171,172,174,178,179,180,182,184,186,188,197,205,207,221,222,223,224,225,226,227,228,229,230,231,238,241,242,244,246,265,276,277,278,279,280,281,282,283,284,286,292,293,296,302,304,305,306,307,308,309,310,311,312,313,314,315,316,317,318,319,320,321,322,326,327,328,329,331,334,335,339,343,352,364,365,369,370,392,396,397,400,402,404,405,413,414,415,417,421,423,431,433,434,436,437,438,439,440,444,445,448,449,461,467,502,504,505,514,599,761],un=[2,356],cn=[1,665],ln=[1,675],hn=[2,4,5,10,53,71,73,75,76,77,88,92,94,97,98,106,111,114,115,117,121,122,123,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,144,145,147,148,149,151,153,155,161,163,165,167,168,169,170,171,172,174,178,179,180,182,184,186,188,197,205,207,221,222,223,224,225,226,227,228,229,230,231,238,241,242,244,246,265,276,277,278,279,280,281,282,283,284,286,292,296,302,304,305,306,307,308,309,310,311,312,313,314,315,316,317,318,319,320,321,322,326,327,328,329,331,334,335,343,392,396,397,400,402,404,405,413,414,415,417,421,423,431,433,434,436,437,438,439,440,444,445,448,449,461,467,502,504,505,514,599,761],dn=[1,691],fn=[1,700],pn=[1,699],bn=[2,4,5,10,71,73,77,92,97,117,127,161,167,168,205,207,221,222,223,224,225,226,227,228,229,230,231,244,246,302,306,599,761],En=[10,71,73,77,92,97,117,127,161,167,168,205,207,221,222,223,224,225,226,227,228,229,230,231,244,246,302,306,599,761],gn=[2,201],mn=[1,722],vn=[10,71,77,92,97,117,127,161,167,168,182,231,244,246,302,306,599,761],Sn=[2,162],Tn=[1,725],An=[2,4,5,111],yn=[1,738],Nn=[1,757],Cn=[1,737],Rn=[1,736],On=[1,731],wn=[1,732],In=[1,734],xn=[1,735],Dn=[1,739],Ln=[1,740],kn=[1,741],$n=[1,742],Mn=[1,743],Un=[1,744],_n=[1,745],Fn=[1,746],Pn=[1,747],qn=[1,748],Gn=[1,749],Vn=[1,750],Bn=[1,751],jn=[1,752],Hn=[1,753],Jn=[1,754],Yn=[1,756],Wn=[1,758],Xn=[1,759],Kn=[1,760],Qn=[1,761],zn=[1,762],Zn=[1,763],er=[1,764],tr=[1,767],nr=[1,768],rr=[1,769],ar=[1,770],sr=[1,771],ir=[1,772],or=[1,773],ur=[1,774],cr=[1,775],lr=[1,776],hr=[1,777],dr=[1,778],fr=[73,88,188],pr=[10,73,77,153,186,229,293,302,306,339,352,364,365,369,370,599,761],br=[1,795],Er=[10,73,77,296,302,306,599,761],gr=[1,796],mr=[1,802],vr=[1,803],Sr=[1,807],Tr=[10,73,77,302,306,599,761],Ar=[2,4,5,76,130,131,136,142,144,148,151,153,155,178,179,180,264,265,266,267,269,276,277,278,279,280,281,282,283,284,286,287,288,289,290,291,292,293,294,295,298,299,308,313,417,421],yr=[10,71,77,92,97,106,117,127,161,167,168,182,197,231,244,246,302,306,599,761],Nr=[2,4,5,10,71,76,77,92,97,106,117,127,130,131,136,142,144,148,151,153,155,161,163,167,168,178,179,180,182,184,186,194,197,231,244,246,264,265,266,267,269,276,277,278,279,280,281,282,283,284,286,287,288,289,290,291,292,293,294,295,298,299,302,306,308,313,417,421,599,761],Cr=[2,4,5,131,292],Rr=[1,841],Or=[10,73,75,77,302,306,599,761],wr=[2,731],Ir=[10,73,75,77,131,138,140,144,151,302,306,417,421,599,761],xr=[2,1154],Dr=[10,73,75,77,138,140,144,151,302,306,417,421,599,761],Lr=[10,73,75,77,138,140,144,302,306,417,421,599,761],kr=[10,73,77,138,140,302,306,599,761],$r=[10,77,88,131,145,188,292,302,306,467,599,761],Mr=[331,334,335],Ur=[2,757],_r=[1,866],Fr=[1,867],Pr=[1,868],qr=[1,869],Gr=[1,876],Vr=[1,875],Br=[163,165,330],jr=[2,441],Hr=[1,931],Jr=[2,4,5,76,130,155,286,287,288,289],Yr=[1,946],Wr=[2,4,5,10,53,71,73,75,76,77,88,92,94,97,98,106,111,117,121,123,127,128,129,130,131,133,134,136,138,139,140,141,142,144,145,147,148,149,151,153,155,161,163,165,167,168,169,170,171,172,174,178,180,182,184,186,188,197,205,207,221,222,223,224,225,226,227,228,231,238,241,242,244,246,265,276,277,278,279,280,281,282,283,284,286,292,296,302,304,305,306,307,309,310,311,313,314,315,316,317,318,319,320,321,322,326,327,328,329,331,334,335,343,392,396,397,400,402,404,405,413,414,415,417,421,431,433,434,436,437,438,439,440,444,445,448,449,461,467,502,504,505,514,599,761],Xr=[2,4,5,10,53,71,73,75,76,77,88,92,94,97,98,106,111,114,115,117,121,122,123,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,144,145,147,148,149,151,153,155,161,163,165,167,168,169,170,171,172,174,178,179,180,182,184,186,188,197,205,207,221,222,223,224,225,226,227,228,231,238,241,242,244,246,265,276,277,278,279,280,281,282,283,284,286,292,296,302,304,305,306,307,308,309,310,311,313,314,315,316,317,318,319,320,321,322,326,327,328,329,331,334,335,343,392,396,397,400,402,404,405,413,414,415,417,421,431,433,434,436,437,438,439,440,444,445,448,449,461,467,502,504,505,514,599,761],Kr=[2,372],Qr=[1,953],zr=[302,304,306],Zr=[73,296],ea=[73,296,423],ta=[1,960],na=[2,4,5,10,53,71,73,75,77,88,92,94,97,98,106,111,114,115,117,121,122,123,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,144,145,147,148,149,151,153,155,161,163,165,167,168,169,170,171,172,174,178,179,180,182,184,186,188,197,205,207,221,222,223,224,225,226,227,228,231,238,241,242,244,246,265,276,277,278,279,280,281,282,283,284,286,292,296,302,304,305,306,307,308,309,310,311,312,313,314,315,316,317,318,319,320,321,322,326,327,328,329,331,334,335,343,392,396,397,400,402,404,405,413,414,415,417,421,431,433,434,436,437,438,439,440,444,445,448,449,461,467,502,504,505,514,599,761],ra=[73,423],aa=[10,71,77,92,97,117,127,161,167,168,231,244,246,302,306,599,761],sa=[1,998],ia=[10,71,77,302,306,599,761],oa=[1,1004],ua=[1,1005],ca=[1,1006],la=[2,4,5,10,71,73,75,76,77,111,114,115,117,121,122,123,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,144,145,147,148,149,151,153,155,161,163,165,167,168,169,170,171,172,174,178,179,180,182,184,186,197,276,277,278,279,280,281,282,283,284,302,306,417,421,599,761],ha=[1,1056],da=[1,1055],fa=[1,1069],pa=[1,1068],ba=[1,1076],Ea=[10,71,73,77,92,97,106,117,127,161,167,168,182,197,231,244,246,302,306,599,761],ga=[1,1107],ma=[10,77,88,145,188,302,306,467,599,761],va=[1,1127],Sa=[1,1126],Ta=[1,1125],Aa=[2,4,5,10,53,71,73,75,76,77,88,92,94,97,98,106,111,114,115,117,121,122,123,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,144,145,147,148,149,151,153,155,161,163,165,167,168,169,170,171,172,174,178,179,180,182,184,186,188,197,205,207,221,222,223,224,225,226,227,228,229,231,238,241,242,244,246,265,276,277,278,279,280,281,282,283,284,286,292,293,296,302,304,305,306,307,308,309,310,311,312,313,314,315,316,317,318,319,320,321,322,326,327,328,329,331,334,335,339,343,352,364,365,369,370,392,396,397,400,402,404,405,413,414,415,417,421,431,433,434,436,437,438,439,440,444,445,448,449,461,467,502,504,505,514,599,761],ya=[1,1139],Na=[2,4,5,10,53,71,73,75,76,77,88,92,94,97,98,106,111,117,121,123,127,128,129,130,131,133,134,136,138,139,142,144,145,147,148,149,151,153,155,161,163,165,167,168,169,170,171,172,174,180,182,184,186,188,197,205,207,221,222,223,224,225,226,227,228,231,238,241,242,244,246,265,276,277,278,279,280,281,282,283,284,286,292,296,302,304,305,306,307,309,310,311,316,317,318,319,320,321,322,326,327,328,329,331,334,335,343,392,396,397,400,402,404,405,413,414,415,417,421,431,433,434,436,437,438,439,440,444,445,448,449,461,467,502,504,505,514,599,761],Ca=[2,4,5,10,53,71,73,75,76,77,88,92,94,97,98,106,111,117,121,123,127,128,129,130,131,133,134,136,138,139,142,144,145,147,148,149,151,153,155,161,163,165,167,168,169,170,171,172,174,180,182,184,186,188,197,205,207,221,222,223,224,225,226,227,228,231,238,241,242,244,246,265,276,277,278,279,280,281,282,283,284,286,292,296,302,304,305,306,307,309,311,316,317,318,319,320,321,322,326,327,328,329,331,334,335,343,392,396,397,400,402,404,405,413,414,415,417,421,431,433,434,436,437,438,439,440,444,445,448,449,461,467,502,504,505,514,599,761],Ra=[2,4,5,10,53,71,73,75,76,77,88,92,94,97,98,106,111,117,121,123,127,128,129,130,131,132,133,134,136,137,138,139,140,141,142,144,145,147,148,149,151,153,155,161,163,165,167,168,169,170,171,172,174,178,179,180,182,184,186,188,197,205,207,221,222,223,224,225,226,227,228,231,238,241,242,244,246,265,276,277,278,279,280,281,282,283,284,286,292,296,302,304,305,306,307,309,310,311,313,314,315,316,317,318,319,320,321,322,326,327,328,329,331,334,335,343,392,396,397,400,402,404,405,413,414,415,417,421,431,433,434,436,437,438,439,440,444,445,448,449,461,467,502,504,505,514,599,761],Oa=[2,4,5,10,53,71,73,75,76,77,88,92,94,97,98,106,111,117,121,123,127,128,129,130,131,133,134,136,138,139,140,141,142,144,145,147,148,149,151,153,155,161,163,165,167,168,169,170,171,172,174,180,182,184,186,188,197,205,207,221,222,223,224,225,226,227,228,231,238,241,242,244,246,265,276,277,278,279,280,281,282,283,284,286,292,296,302,304,305,306,307,309,310,311,314,315,316,317,318,319,320,321,322,326,327,328,329,331,334,335,343,392,396,397,400,402,404,405,413,414,415,417,421,431,433,434,436,437,438,439,440,444,445,448,449,461,467,502,504,505,514,599,761],wa=[2,4,5,10,53,71,73,75,76,77,88,92,94,97,98,106,117,121,123,127,128,129,130,131,133,134,136,138,139,142,144,145,147,148,149,151,153,155,161,163,165,167,168,169,170,171,172,174,180,182,184,186,188,197,205,207,221,222,223,224,225,226,227,228,231,238,241,242,244,246,265,276,277,278,279,280,281,282,283,284,286,292,296,302,304,305,306,310,316,317,318,319,320,321,322,326,327,329,331,334,335,343,392,396,397,400,402,404,405,413,414,415,417,421,431,433,434,436,437,438,439,440,444,445,448,449,461,467,502,504,505,514,599,761],Ia=[2,403],xa=[2,4,5,10,53,71,73,75,76,77,88,92,94,97,106,117,121,127,128,129,130,131,133,134,136,142,144,145,147,148,149,151,155,161,163,165,167,168,169,170,171,172,174,180,182,184,186,188,197,205,207,221,222,223,224,225,226,227,228,231,238,241,242,244,246,265,276,277,278,279,280,281,282,283,284,286,292,296,302,304,305,306,310,326,327,329,331,334,335,343,392,396,397,400,402,404,405,413,414,415,417,421,431,433,434,436,437,438,439,440,444,445,448,449,461,467,502,504,505,514,599,761],Da=[2,284],La=[2,4,5,10,53,71,73,75,76,77,88,92,94,97,98,106,111,114,115,117,121,122,123,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,144,145,147,148,149,151,153,155,161,163,165,167,168,169,170,171,172,174,178,179,180,182,184,186,188,197,205,207,221,222,223,224,225,226,227,228,231,238,241,242,244,246,265,276,277,278,279,280,281,282,283,284,286,292,296,302,304,305,306,307,308,309,310,311,312,313,314,315,316,317,318,319,320,321,322,326,327,328,329,331,334,335,343,392,396,397,400,402,404,405,413,414,415,417,421,423,431,433,434,436,437,438,439,440,444,445,448,449,461,467,502,504,505,514,599,761],ka=[1,1175],$a=[10,77,302,306,599,761],Ma=[1,1186],Ua=[10,71,77,117,127,161,167,168,231,244,246,302,306,599,761],_a=[10,71,73,77,92,97,117,127,161,167,168,182,197,231,244,246,302,306,599,761],Fa=[2,4,5,71,75,76,77,111,114,115,117,121,122,123,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,144,145,147,148,149,151,153,155,161,163,165,167,168,169,170,171,172,174,184,186,276,277,278,279,280,281,282,283,284,417,421],Pa=[2,4,5,71,73,75,76,77,111,114,115,117,121,122,123,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,144,145,147,148,149,151,153,155,161,163,165,167,168,169,170,171,172,174,184,186,276,277,278,279,280,281,282,283,284,417,421],qa=[2,1078],Ga=[2,4,5,71,73,75,76,111,114,115,117,121,122,123,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,144,145,147,148,149,151,153,155,161,163,165,167,168,169,170,171,172,174,184,186,276,277,278,279,280,281,282,283,284,417,421],Va=[1,1238],Ba=[10,73,77,127,302,304,306,461,599,761],ja=[114,115,123],Ha=[2,575],Ja=[1,1266],Ya=[75,138],Wa=[2,717],Xa=[1,1283],Ka=[1,1284],Qa=[2,4,5,10,53,71,75,88,123,145,155,188,229,265,286,302,306,331,334,335,343,392,396,397,400,402,404,405,413,414,415,431,433,434,436,437,438,439,440,444,445,448,449,502,504,505,514,599,761],za=[2,327],Za=[1,1309],es=[1,1319],ts=[10,73,77,302,304,306,461,599,761],ns=[1,1322],rs=[10,71,73,77,92,97,117,127,161,167,168,205,207,221,222,223,224,225,226,227,228,231,244,246,302,306,599,761],as=[10,302,304,306,461,599,761],ss=[10,71,77,117,161,167,168,231,244,246,302,306,599,761],is=[1,1337],os=[1,1341],us=[1,1342],cs=[1,1344],ls=[1,1345],hs=[1,1346],ds=[1,1347],fs=[1,1348],ps=[1,1349],bs=[1,1350],Es=[1,1351],gs=[1,1376],ms=[73,77],vs=[1,1433],Ss=[10,71,77,117,161,167,168,244,246,302,306,599,761],Ts=[10,71,77,92,97,117,127,161,167,168,205,207,221,222,223,224,225,226,227,228,231,244,246,302,306,599,761],As=[1,1474],ys=[1,1476],Ns=[2,4,5,76,142,144,151,155,180,286,287,288,289,298,417,421],Cs=[1,1490],Rs=[10,71,73,77,161,167,168,244,246,302,306,599,761],Os=[1,1508],ws=[1,1510],Is=[1,1511],xs=[1,1507],Ds=[1,1506],Ls=[1,1505],ks=[1,1512],$s=[1,1502],Ms=[1,1503],Us=[1,1504],_s=[1,1529],Fs=[2,4,5,10,53,71,88,123,145,155,188,265,286,302,306,331,334,335,343,392,396,397,400,402,404,405,413,414,415,431,433,434,436,437,438,439,440,444,445,448,449,502,504,505,514,599,761],Ps=[1,1541],qs=[1,1549],Gs=[1,1548],Vs=[10,71,77,161,167,168,244,246,302,306,599,761],Bs=[10,71,77,92,97,117,127,161,167,168,205,207,221,222,223,224,225,226,227,228,229,230,231,244,246,302,306,599,761],js=[2,4,5,10,71,77,92,97,117,127,161,167,168,205,207,221,222,223,224,225,226,227,228,229,230,231,244,246,302,306,599,761],Hs=[1,1606],Js=[1,1608],Ys=[1,1605],Ws=[1,1607],Xs=[186,192,364,365,366,369],Ks=[2,506],Qs=[1,1613],zs=[1,1633],Zs=[10,71,77,161,167,168,302,306,599,761],ei=[1,1643],ti=[1,1644],ni=[1,1645],ri=[1,1664],ai=[4,10,242,302,306,339,352,599,761],si=[1,1712],ii=[10,71,73,77,117,161,167,168,238,244,246,302,306,599,761],oi=[2,4,5,76],ui=[1,1806],ci=[1,1818],li=[1,1837],hi=[10,71,77,161,167,168,302,306,411,599,761],di=[10,73,77,229,302,306,599,761],fi={
trace:function(){},yy:{},symbols_:{error:2,Literal:3,LITERAL:4,BRALITERAL:5,NonReserved:6,LiteralWithSpaces:7,main:8,Statements:9,EOF:10,Statements_group0:11,AStatement:12,ExplainStatement:13,EXPLAIN:14,QUERY:15,PLAN:16,Statement:17,AlterTable:18,AttachDatabase:19,Call:20,CreateDatabase:21,CreateIndex:22,CreateGraph:23,CreateTable:24,CreateView:25,CreateEdge:26,CreateVertex:27,Declare:28,Delete:29,DetachDatabase:30,DropDatabase:31,DropIndex:32,DropTable:33,DropView:34,If:35,Insert:36,Merge:37,Reindex:38,RenameTable:39,Select:40,ShowCreateTable:41,ShowColumns:42,ShowDatabases:43,ShowIndex:44,ShowTables:45,TruncateTable:46,WithSelect:47,CreateTrigger:48,DropTrigger:49,BeginTransaction:50,CommitTransaction:51,RollbackTransaction:52,EndTransaction:53,UseDatabase:54,Update:55,Help:56,JavaScript:57,Source:58,Assert:59,While:60,Continue:61,Break:62,BeginEnd:63,Print:64,Require:65,SetVariable:66,ExpressionStatement:67,AddRule:68,Query:69,Echo:70,WITH:71,WithTablesList:72,COMMA:73,WithTable:74,AS:75,LPAR:76,RPAR:77,SelectClause:78,Select_option0:79,IntoClause:80,FromClause:81,Select_option1:82,WhereClause:83,GroupClause:84,OrderClause:85,LimitClause:86,UnionClause:87,SEARCH:88,Select_repetition0:89,Select_option2:90,PivotClause:91,PIVOT:92,Expression:93,FOR:94,PivotClause_option0:95,PivotClause_option1:96,UNPIVOT:97,IN:98,ColumnsList:99,PivotClause_option2:100,PivotClause2:101,AsList:102,AsLiteral:103,AsPart:104,RemoveClause:105,REMOVE:106,RemoveClause_option0:107,RemoveColumnsList:108,RemoveColumn:109,Column:110,LIKE:111,StringValue:112,ArrowDot:113,ARROW:114,DOT:115,SearchSelector:116,ORDER:117,BY:118,OrderExpressionsList:119,SearchSelector_option0:120,DOTDOT:121,CARET:122,EQ:123,SearchSelector_repetition_plus0:124,SearchSelector_repetition_plus1:125,SearchSelector_option1:126,WHERE:127,OF:128,CLASS:129,NUMBER:130,STRING:131,SLASH:132,VERTEX:133,EDGE:134,EXCLAMATION:135,SHARP:136,MODULO:137,GT:138,LT:139,GTGT:140,LTLT:141,DOLLAR:142,Json:143,AT:144,SET:145,SetColumnsList:146,TO:147,VALUE:148,ROW:149,ExprList:150,COLON:151,PlusStar:152,NOT:153,SearchSelector_repetition2:154,IF:155,SearchSelector_repetition3:156,Aggregator:157,SearchSelector_repetition4:158,SearchSelector_group0:159,SearchSelector_repetition5:160,UNION:161,SearchSelectorList:162,ALL:163,SearchSelector_repetition6:164,ANY:165,SearchSelector_repetition7:166,INTERSECT:167,EXCEPT:168,AND:169,OR:170,PATH:171,RETURN:172,ResultColumns:173,REPEAT:174,SearchSelector_repetition8:175,SearchSelectorList_repetition0:176,SearchSelectorList_repetition1:177,PLUS:178,STAR:179,QUESTION:180,SearchFrom:181,FROM:182,SelectModifier:183,DISTINCT:184,TopClause:185,UNIQUE:186,SelectClause_option0:187,SELECT:188,COLUMN:189,MATRIX:190,TEXTSTRING:191,INDEX:192,RECORDSET:193,TOP:194,NumValue:195,TopClause_option0:196,INTO:197,Table:198,FuncValue:199,ParamValue:200,VarValue:201,FromTablesList:202,JoinTablesList:203,ApplyClause:204,CROSS:205,APPLY:206,OUTER:207,FromTable:208,FromTable_option0:209,FromTable_option1:210,INDEXED:211,INSERTED:212,FromString:213,JoinTable:214,JoinMode:215,JoinTableAs:216,OnClause:217,JoinTableAs_option0:218,JoinTableAs_option1:219,JoinModeMode:220,NATURAL:221,JOIN:222,INNER:223,LEFT:224,RIGHT:225,FULL:226,SEMI:227,ANTI:228,ON:229,USING:230,GROUP:231,GroupExpressionsList:232,HavingClause:233,GroupExpression:234,GROUPING:235,ROLLUP:236,CUBE:237,HAVING:238,CORRESPONDING:239,OrderExpression:240,DIRECTION:241,COLLATE:242,NOCASE:243,LIMIT:244,OffsetClause:245,OFFSET:246,LimitClause_option0:247,FETCH:248,LimitClause_option1:249,LimitClause_option2:250,LimitClause_option3:251,ResultColumn:252,Star:253,AggrValue:254,Op:255,LogicValue:256,NullValue:257,ExistsValue:258,CaseValue:259,CastClause:260,ArrayValue:261,NewClause:262,Expression_group0:263,CURRENT_TIMESTAMP:264,JAVASCRIPT:265,NEW:266,CAST:267,ColumnType:268,CONVERT:269,PrimitiveValue:270,OverClause:271,OVER:272,OverPartitionClause:273,OverOrderByClause:274,PARTITION:275,SUM:276,COUNT:277,MIN:278,MAX:279,AVG:280,FIRST:281,LAST:282,AGGR:283,ARRAY:284,FuncValue_option0:285,REPLACE:286,DATEADD:287,DATEDIFF:288,INTERVAL:289,TRUE:290,FALSE:291,NSTRING:292,NULL:293,EXISTS:294,ARRAYLBRA:295,RBRA:296,ParamValue_group0:297,BRAQUESTION:298,CASE:299,WhensList:300,ElseClause:301,END:302,When:303,WHEN:304,THEN:305,ELSE:306,REGEXP:307,TILDA:308,GLOB:309,ESCAPE:310,NOT_LIKE:311,BARBAR:312,MINUS:313,AMPERSAND:314,BAR:315,GE:316,LE:317,EQEQ:318,EQEQEQ:319,NE:320,NEEQEQ:321,NEEQEQEQ:322,CondOp:323,AllSome:324,ColFunc:325,BETWEEN:326,NOT_BETWEEN:327,IS:328,DOUBLECOLON:329,SOME:330,UPDATE:331,SetColumn:332,SetColumn_group0:333,DELETE:334,INSERT:335,Into:336,VALUES:337,ValuesListsList:338,DEFAULT:339,ValuesList:340,Value:341,DateValue:342,CREATE:343,TemporaryClause:344,TableClass:345,IfNotExists:346,CreateTableDefClause:347,CreateTableOptionsClause:348,TABLE:349,CreateTableOptions:350,CreateTableOption:351,IDENTITY:352,TEMP:353,ColumnDefsList:354,ConstraintsList:355,Constraint:356,ConstraintName:357,PrimaryKey:358,ForeignKey:359,UniqueKey:360,IndexKey:361,Check:362,CONSTRAINT:363,CHECK:364,PRIMARY:365,KEY:366,PrimaryKey_option0:367,ColsList:368,FOREIGN:369,REFERENCES:370,ForeignKey_option0:371,OnForeignKeyClause:372,ParColsList:373,OnDeleteClause:374,OnUpdateClause:375,NO:376,ACTION:377,UniqueKey_option0:378,UniqueKey_option1:379,ColumnDef:380,ColumnConstraintsClause:381,ColumnConstraints:382,SingularColumnType:383,NumberMax:384,ENUM:385,MAXNUM:386,ColumnConstraintsList:387,ColumnConstraint:388,ParLiteral:389,ColumnConstraint_option0:390,ColumnConstraint_option1:391,DROP:392,DropTable_group0:393,IfExists:394,TablesList:395,ALTER:396,RENAME:397,ADD:398,MODIFY:399,ATTACH:400,DATABASE:401,DETACH:402,AsClause:403,USE:404,SHOW:405,VIEW:406,CreateView_option0:407,CreateView_option1:408,SubqueryRestriction:409,READ:410,ONLY:411,OPTION:412,HELP:413,SOURCE:414,ASSERT:415,JsonObject:416,ATLBRA:417,JsonArray:418,JsonValue:419,JsonPrimitiveValue:420,LCUR:421,JsonPropertiesList:422,RCUR:423,JsonElementsList:424,JsonProperty:425,OnOff:426,SetPropsList:427,AtDollar:428,SetProp:429,OFF:430,COMMIT:431,TRANSACTION:432,ROLLBACK:433,BEGIN:434,ElseStatement:435,WHILE:436,CONTINUE:437,BREAK:438,PRINT:439,REQUIRE:440,StringValuesList:441,PluginsList:442,Plugin:443,ECHO:444,DECLARE:445,DeclaresList:446,DeclareItem:447,TRUNCATE:448,MERGE:449,MergeInto:450,MergeUsing:451,MergeOn:452,MergeMatchedList:453,OutputClause:454,MergeMatched:455,MergeNotMatched:456,MATCHED:457,MergeMatchedAction:458,MergeNotMatchedAction:459,TARGET:460,OUTPUT:461,CreateVertex_option0:462,CreateVertex_option1:463,CreateVertex_option2:464,CreateVertexSet:465,SharpValue:466,CONTENT:467,CreateEdge_option0:468,GRAPH:469,GraphList:470,GraphVertexEdge:471,GraphElement:472,GraphVertexEdge_option0:473,GraphVertexEdge_option1:474,GraphElementVar:475,GraphVertexEdge_option2:476,GraphVertexEdge_option3:477,GraphVertexEdge_option4:478,GraphVar:479,GraphAsClause:480,GraphAtClause:481,GraphElement2:482,GraphElement2_option0:483,GraphElement2_option1:484,GraphElement2_option2:485,GraphElement2_option3:486,GraphElement_option0:487,GraphElement_option1:488,GraphElement_option2:489,SharpLiteral:490,GraphElement_option3:491,GraphElement_option4:492,GraphElement_option5:493,ColonLiteral:494,DeleteVertex:495,DeleteVertex_option0:496,DeleteEdge:497,DeleteEdge_option0:498,DeleteEdge_option1:499,DeleteEdge_option2:500,Term:501,COLONDASH:502,TermsList:503,QUESTIONDASH:504,CALL:505,TRIGGER:506,BeforeAfter:507,InsertDeleteUpdate:508,CreateTrigger_option0:509,CreateTrigger_option1:510,BEFORE:511,AFTER:512,INSTEAD:513,REINDEX:514,A:515,ABSENT:516,ABSOLUTE:517,ACCORDING:518,ADA:519,ADMIN:520,ALWAYS:521,ASC:522,ASSERTION:523,ASSIGNMENT:524,ATTRIBUTE:525,ATTRIBUTES:526,BASE64:527,BERNOULLI:528,BLOCKED:529,BOM:530,BREADTH:531,C:532,CASCADE:533,CATALOG:534,CATALOG_NAME:535,CHAIN:536,CHARACTERISTICS:537,CHARACTERS:538,CHARACTER_SET_CATALOG:539,CHARACTER_SET_NAME:540,CHARACTER_SET_SCHEMA:541,CLASS_ORIGIN:542,COBOL:543,COLLATION:544,COLLATION_CATALOG:545,COLLATION_NAME:546,COLLATION_SCHEMA:547,COLUMNS:548,COLUMN_NAME:549,COMMAND_FUNCTION:550,COMMAND_FUNCTION_CODE:551,COMMITTED:552,CONDITION_NUMBER:553,CONNECTION:554,CONNECTION_NAME:555,CONSTRAINTS:556,CONSTRAINT_CATALOG:557,CONSTRAINT_NAME:558,CONSTRAINT_SCHEMA:559,CONSTRUCTOR:560,CONTROL:561,CURSOR_NAME:562,DATA:563,DATETIME_INTERVAL_CODE:564,DATETIME_INTERVAL_PRECISION:565,DB:566,DEFAULTS:567,DEFERRABLE:568,DEFERRED:569,DEFINED:570,DEFINER:571,DEGREE:572,DEPTH:573,DERIVED:574,DESC:575,DESCRIPTOR:576,DIAGNOSTICS:577,DISPATCH:578,DOCUMENT:579,DOMAIN:580,DYNAMIC_FUNCTION:581,DYNAMIC_FUNCTION_CODE:582,EMPTY:583,ENCODING:584,ENFORCED:585,EXCLUDE:586,EXCLUDING:587,EXPRESSION:588,FILE:589,FINAL:590,FLAG:591,FOLLOWING:592,FORTRAN:593,FOUND:594,FS:595,G:596,GENERAL:597,GENERATED:598,GO:599,GOTO:600,GRANTED:601,HEX:602,HIERARCHY:603,ID:604,IGNORE:605,IMMEDIATE:606,IMMEDIATELY:607,IMPLEMENTATION:608,INCLUDING:609,INCREMENT:610,INDENT:611,INITIALLY:612,INPUT:613,INSTANCE:614,INSTANTIABLE:615,INTEGRITY:616,INVOKER:617,ISOLATION:618,K:619,KEY_MEMBER:620,KEY_TYPE:621,LENGTH:622,LEVEL:623,LIBRARY:624,LINK:625,LOCATION:626,LOCATOR:627,M:628,MAP:629,MAPPING:630,MAXVALUE:631,MESSAGE_LENGTH:632,MESSAGE_OCTET_LENGTH:633,MESSAGE_TEXT:634,MINVALUE:635,MORE:636,MUMPS:637,NAME:638,NAMES:639,NAMESPACE:640,NESTING:641,NEXT:642,NFC:643,NFD:644,NFKC:645,NFKD:646,NIL:647,NORMALIZED:648,NULLABLE:649,NULLS:650,OBJECT:651,OCTETS:652,OPTIONS:653,ORDERING:654,ORDINALITY:655,OTHERS:656,OVERRIDING:657,P:658,PAD:659,PARAMETER_MODE:660,PARAMETER_NAME:661,PARAMETER_ORDINAL_POSITION:662,PARAMETER_SPECIFIC_CATALOG:663,PARAMETER_SPECIFIC_NAME:664,PARAMETER_SPECIFIC_SCHEMA:665,PARTIAL:666,PASCAL:667,PASSING:668,PASSTHROUGH:669,PERMISSION:670,PLACING:671,PLI:672,PRECEDING:673,PRESERVE:674,PRIOR:675,PRIVILEGES:676,PUBLIC:677,RECOVERY:678,RELATIVE:679,REPEATABLE:680,REQUIRING:681,RESPECT:682,RESTART:683,RESTORE:684,RESTRICT:685,RETURNED_CARDINALITY:686,RETURNED_LENGTH:687,RETURNED_OCTET_LENGTH:688,RETURNED_SQLSTATE:689,RETURNING:690,ROLE:691,ROUTINE:692,ROUTINE_CATALOG:693,ROUTINE_NAME:694,ROUTINE_SCHEMA:695,ROW_COUNT:696,SCALE:697,SCHEMA:698,SCHEMA_NAME:699,SCOPE_CATALOG:700,SCOPE_NAME:701,SCOPE_SCHEMA:702,SECTION:703,SECURITY:704,SELECTIVE:705,SELF:706,SEQUENCE:707,SERIALIZABLE:708,SERVER:709,SERVER_NAME:710,SESSION:711,SETS:712,SIMPLE:713,SIZE:714,SPACE:715,SPECIFIC_NAME:716,STANDALONE:717,STATE:718,STATEMENT:719,STRIP:720,STRUCTURE:721,STYLE:722,SUBCLASS_ORIGIN:723,T:724,TABLE_NAME:725,TEMPORARY:726,TIES:727,TOKEN:728,TOP_LEVEL_COUNT:729,TRANSACTIONS_COMMITTED:730,TRANSACTIONS_ROLLED_BACK:731,TRANSACTION_ACTIVE:732,TRANSFORM:733,TRANSFORMS:734,TRIGGER_CATALOG:735,TRIGGER_NAME:736,TRIGGER_SCHEMA:737,TYPE:738,UNBOUNDED:739,UNCOMMITTED:740,UNDER:741,UNLINK:742,UNNAMED:743,UNTYPED:744,URI:745,USAGE:746,USER_DEFINED_TYPE_CATALOG:747,USER_DEFINED_TYPE_CODE:748,USER_DEFINED_TYPE_NAME:749,USER_DEFINED_TYPE_SCHEMA:750,VALID:751,VERSION:752,WHITESPACE:753,WORK:754,WRAPPER:755,WRITE:756,XMLDECLARATION:757,XMLSCHEMA:758,YES:759,ZONE:760,SEMICOLON:761,PERCENT:762,ROWS:763,FuncValue_option0_group0:764,$accept:0,$end:1},terminals_:{2:"error",4:"LITERAL",5:"BRALITERAL",10:"EOF",14:"EXPLAIN",15:"QUERY",16:"PLAN",53:"EndTransaction",71:"WITH",73:"COMMA",75:"AS",76:"LPAR",77:"RPAR",88:"SEARCH",92:"PIVOT",94:"FOR",97:"UNPIVOT",98:"IN",106:"REMOVE",111:"LIKE",114:"ARROW",115:"DOT",117:"ORDER",118:"BY",121:"DOTDOT",122:"CARET",123:"EQ",127:"WHERE",128:"OF",129:"CLASS",130:"NUMBER",131:"STRING",132:"SLASH",133:"VERTEX",134:"EDGE",135:"EXCLAMATION",136:"SHARP",137:"MODULO",138:"GT",139:"LT",140:"GTGT",141:"LTLT",142:"DOLLAR",144:"AT",145:"SET",147:"TO",148:"VALUE",149:"ROW",151:"COLON",153:"NOT",155:"IF",161:"UNION",163:"ALL",165:"ANY",167:"INTERSECT",168:"EXCEPT",169:"AND",170:"OR",171:"PATH",172:"RETURN",174:"REPEAT",178:"PLUS",179:"STAR",180:"QUESTION",182:"FROM",184:"DISTINCT",186:"UNIQUE",188:"SELECT",189:"COLUMN",190:"MATRIX",191:"TEXTSTRING",192:"INDEX",193:"RECORDSET",194:"TOP",197:"INTO",205:"CROSS",206:"APPLY",207:"OUTER",211:"INDEXED",212:"INSERTED",221:"NATURAL",222:"JOIN",223:"INNER",224:"LEFT",225:"RIGHT",226:"FULL",227:"SEMI",228:"ANTI",229:"ON",230:"USING",231:"GROUP",235:"GROUPING",236:"ROLLUP",237:"CUBE",238:"HAVING",239:"CORRESPONDING",241:"DIRECTION",242:"COLLATE",243:"NOCASE",244:"LIMIT",246:"OFFSET",248:"FETCH",264:"CURRENT_TIMESTAMP",265:"JAVASCRIPT",266:"NEW",267:"CAST",269:"CONVERT",272:"OVER",275:"PARTITION",276:"SUM",277:"COUNT",278:"MIN",279:"MAX",280:"AVG",281:"FIRST",282:"LAST",283:"AGGR",284:"ARRAY",286:"REPLACE",287:"DATEADD",288:"DATEDIFF",289:"INTERVAL",290:"TRUE",291:"FALSE",292:"NSTRING",293:"NULL",294:"EXISTS",295:"ARRAYLBRA",296:"RBRA",298:"BRAQUESTION",299:"CASE",302:"END",304:"WHEN",305:"THEN",306:"ELSE",307:"REGEXP",308:"TILDA",309:"GLOB",310:"ESCAPE",311:"NOT_LIKE",312:"BARBAR",313:"MINUS",314:"AMPERSAND",315:"BAR",316:"GE",317:"LE",318:"EQEQ",319:"EQEQEQ",320:"NE",321:"NEEQEQ",322:"NEEQEQEQ",326:"BETWEEN",327:"NOT_BETWEEN",328:"IS",329:"DOUBLECOLON",330:"SOME",331:"UPDATE",334:"DELETE",335:"INSERT",337:"VALUES",339:"DEFAULT",342:"DateValue",343:"CREATE",349:"TABLE",352:"IDENTITY",353:"TEMP",363:"CONSTRAINT",364:"CHECK",365:"PRIMARY",366:"KEY",369:"FOREIGN",370:"REFERENCES",376:"NO",377:"ACTION",382:"ColumnConstraints",385:"ENUM",386:"MAXNUM",392:"DROP",396:"ALTER",397:"RENAME",398:"ADD",399:"MODIFY",400:"ATTACH",401:"DATABASE",402:"DETACH",404:"USE",405:"SHOW",406:"VIEW",410:"READ",411:"ONLY",412:"OPTION",413:"HELP",414:"SOURCE",415:"ASSERT",417:"ATLBRA",421:"LCUR",423:"RCUR",430:"OFF",431:"COMMIT",432:"TRANSACTION",433:"ROLLBACK",434:"BEGIN",436:"WHILE",437:"CONTINUE",438:"BREAK",439:"PRINT",440:"REQUIRE",444:"ECHO",445:"DECLARE",448:"TRUNCATE",449:"MERGE",457:"MATCHED",460:"TARGET",461:"OUTPUT",467:"CONTENT",469:"GRAPH",502:"COLONDASH",504:"QUESTIONDASH",505:"CALL",506:"TRIGGER",511:"BEFORE",512:"AFTER",513:"INSTEAD",514:"REINDEX",515:"A",516:"ABSENT",517:"ABSOLUTE",518:"ACCORDING",519:"ADA",520:"ADMIN",521:"ALWAYS",522:"ASC",523:"ASSERTION",524:"ASSIGNMENT",525:"ATTRIBUTE",526:"ATTRIBUTES",527:"BASE64",528:"BERNOULLI",529:"BLOCKED",530:"BOM",531:"BREADTH",532:"C",533:"CASCADE",534:"CATALOG",535:"CATALOG_NAME",536:"CHAIN",537:"CHARACTERISTICS",538:"CHARACTERS",539:"CHARACTER_SET_CATALOG",540:"CHARACTER_SET_NAME",541:"CHARACTER_SET_SCHEMA",542:"CLASS_ORIGIN",543:"COBOL",544:"COLLATION",545:"COLLATION_CATALOG",546:"COLLATION_NAME",547:"COLLATION_SCHEMA",548:"COLUMNS",549:"COLUMN_NAME",550:"COMMAND_FUNCTION",551:"COMMAND_FUNCTION_CODE",552:"COMMITTED",553:"CONDITION_NUMBER",554:"CONNECTION",555:"CONNECTION_NAME",556:"CONSTRAINTS",557:"CONSTRAINT_CATALOG",558:"CONSTRAINT_NAME",559:"CONSTRAINT_SCHEMA",560:"CONSTRUCTOR",561:"CONTROL",562:"CURSOR_NAME",563:"DATA",564:"DATETIME_INTERVAL_CODE",565:"DATETIME_INTERVAL_PRECISION",566:"DB",567:"DEFAULTS",568:"DEFERRABLE",569:"DEFERRED",570:"DEFINED",571:"DEFINER",572:"DEGREE",573:"DEPTH",574:"DERIVED",575:"DESC",576:"DESCRIPTOR",577:"DIAGNOSTICS",578:"DISPATCH",579:"DOCUMENT",580:"DOMAIN",581:"DYNAMIC_FUNCTION",582:"DYNAMIC_FUNCTION_CODE",583:"EMPTY",584:"ENCODING",585:"ENFORCED",586:"EXCLUDE",587:"EXCLUDING",588:"EXPRESSION",589:"FILE",590:"FINAL",591:"FLAG",592:"FOLLOWING",593:"FORTRAN",594:"FOUND",595:"FS",596:"G",597:"GENERAL",598:"GENERATED",599:"GO",600:"GOTO",601:"GRANTED",602:"HEX",603:"HIERARCHY",604:"ID",605:"IGNORE",606:"IMMEDIATE",607:"IMMEDIATELY",608:"IMPLEMENTATION",609:"INCLUDING",610:"INCREMENT",611:"INDENT",612:"INITIALLY",613:"INPUT",614:"INSTANCE",615:"INSTANTIABLE",616:"INTEGRITY",617:"INVOKER",618:"ISOLATION",619:"K",620:"KEY_MEMBER",621:"KEY_TYPE",622:"LENGTH",623:"LEVEL",624:"LIBRARY",625:"LINK",626:"LOCATION",627:"LOCATOR",628:"M",629:"MAP",630:"MAPPING",631:"MAXVALUE",632:"MESSAGE_LENGTH",633:"MESSAGE_OCTET_LENGTH",634:"MESSAGE_TEXT",635:"MINVALUE",636:"MORE",637:"MUMPS",638:"NAME",639:"NAMES",640:"NAMESPACE",641:"NESTING",642:"NEXT",643:"NFC",644:"NFD",645:"NFKC",646:"NFKD",647:"NIL",648:"NORMALIZED",649:"NULLABLE",650:"NULLS",651:"OBJECT",652:"OCTETS",653:"OPTIONS",654:"ORDERING",655:"ORDINALITY",656:"OTHERS",657:"OVERRIDING",658:"P",659:"PAD",660:"PARAMETER_MODE",661:"PARAMETER_NAME",662:"PARAMETER_ORDINAL_POSITION",663:"PARAMETER_SPECIFIC_CATALOG",664:"PARAMETER_SPECIFIC_NAME",665:"PARAMETER_SPECIFIC_SCHEMA",666:"PARTIAL",667:"PASCAL",668:"PASSING",669:"PASSTHROUGH",670:"PERMISSION",671:"PLACING",672:"PLI",673:"PRECEDING",674:"PRESERVE",675:"PRIOR",676:"PRIVILEGES",677:"PUBLIC",678:"RECOVERY",679:"RELATIVE",680:"REPEATABLE",681:"REQUIRING",682:"RESPECT",683:"RESTART",684:"RESTORE",685:"RESTRICT",686:"RETURNED_CARDINALITY",687:"RETURNED_LENGTH",688:"RETURNED_OCTET_LENGTH",689:"RETURNED_SQLSTATE",690:"RETURNING",691:"ROLE",692:"ROUTINE",693:"ROUTINE_CATALOG",694:"ROUTINE_NAME",695:"ROUTINE_SCHEMA",696:"ROW_COUNT",697:"SCALE",698:"SCHEMA",699:"SCHEMA_NAME",700:"SCOPE_CATALOG",701:"SCOPE_NAME",702:"SCOPE_SCHEMA",703:"SECTION",704:"SECURITY",705:"SELECTIVE",706:"SELF",707:"SEQUENCE",708:"SERIALIZABLE",709:"SERVER",710:"SERVER_NAME",711:"SESSION",712:"SETS",713:"SIMPLE",714:"SIZE",715:"SPACE",716:"SPECIFIC_NAME",717:"STANDALONE",718:"STATE",719:"STATEMENT",720:"STRIP",721:"STRUCTURE",722:"STYLE",723:"SUBCLASS_ORIGIN",724:"T",725:"TABLE_NAME",726:"TEMPORARY",727:"TIES",728:"TOKEN",729:"TOP_LEVEL_COUNT",730:"TRANSACTIONS_COMMITTED",731:"TRANSACTIONS_ROLLED_BACK",732:"TRANSACTION_ACTIVE",733:"TRANSFORM",734:"TRANSFORMS",735:"TRIGGER_CATALOG",736:"TRIGGER_NAME",737:"TRIGGER_SCHEMA",738:"TYPE",739:"UNBOUNDED",740:"UNCOMMITTED",741:"UNDER",742:"UNLINK",743:"UNNAMED",744:"UNTYPED",745:"URI",746:"USAGE",747:"USER_DEFINED_TYPE_CATALOG",748:"USER_DEFINED_TYPE_CODE",749:"USER_DEFINED_TYPE_NAME",750:"USER_DEFINED_TYPE_SCHEMA",751:"VALID",752:"VERSION",753:"WHITESPACE",754:"WORK",755:"WRAPPER",756:"WRITE",757:"XMLDECLARATION",758:"XMLSCHEMA",759:"YES",760:"ZONE",761:"SEMICOLON",762:"PERCENT",763:"ROWS"},productions_:[0,[3,1],[3,1],[3,2],[7,1],[7,2],[8,2],[9,3],[9,1],[9,1],[13,2],[13,4],[12,1],[17,0],[17,1],[17,1],[17,1],[17,1],[17,1],[17,1],[17,1],[17,1],[17,1],[17,1],[17,1],[17,1],[17,1],[17,1],[17,1],[17,1],[17,1],[17,1],[17,1],[17,1],[17,1],[17,1],[17,1],[17,1],[17,1],[17,1],[17,1],[17,1],[17,1],[17,1],[17,1],[17,1],[17,1],[17,1],[17,1],[17,1],[17,1],[17,1],[17,1],[17,1],[17,1],[17,1],[17,1],[17,1],[17,1],[17,1],[17,1],[17,1],[17,1],[17,1],[17,1],[17,1],[17,1],[47,3],[72,3],[72,1],[74,5],[40,10],[40,4],[91,8],[91,11],[101,4],[103,2],[103,1],[102,3],[102,1],[104,1],[104,3],[105,3],[108,3],[108,1],[109,1],[109,2],[113,1],[113,1],[116,1],[116,5],[116,5],[116,1],[116,2],[116,1],[116,2],[116,2],[116,3],[116,4],[116,4],[116,4],[116,4],[116,4],[116,1],[116,1],[116,1],[116,1],[116,1],[116,1],[116,2],[116,2],[116,2],[116,1],[116,1],[116,1],[116,1],[116,1],[116,1],[116,2],[116,3],[116,4],[116,3],[116,1],[116,4],[116,2],[116,2],[116,4],[116,4],[116,4],[116,4],[116,4],[116,5],[116,4],[116,4],[116,4],[116,4],[116,4],[116,4],[116,4],[116,4],[116,6],[162,3],[162,1],[152,1],[152,1],[152,1],[181,2],[78,4],[78,4],[78,4],[78,3],[183,1],[183,2],[183,2],[183,2],[183,2],[183,2],[183,2],[183,2],[185,3],[185,4],[185,0],[80,0],[80,2],[80,2],[80,2],[80,2],[80,2],[81,2],[81,3],[81,5],[81,0],[204,6],[204,7],[204,6],[204,7],[202,1],[202,3],[208,4],[208,5],[208,3],[208,3],[208,2],[208,3],[208,1],[208,3],[208,2],[208,3],[208,1],[208,1],[208,2],[208,3],[208,1],[208,1],[208,2],[208,3],[208,1],[208,2],[208,3],[213,1],[198,3],[198,1],[203,2],[203,2],[203,1],[203,1],[214,3],[216,1],[216,2],[216,3],[216,3],[216,2],[216,3],[216,4],[216,5],[216,1],[216,2],[216,3],[216,1],[216,2],[216,3],[215,1],[215,2],[220,1],[220,2],[220,2],[220,3],[220,2],[220,3],[220,2],[220,3],[220,2],[220,2],[220,2],[217,2],[217,2],[217,0],[83,0],[83,2],[84,0],[84,4],[232,1],[232,3],[234,5],[234,4],[234,4],[234,1],[233,0],[233,2],[87,0],[87,2],[87,3],[87,2],[87,2],[87,3],[87,4],[87,3],[87,3],[85,0],[85,3],[119,1],[119,3],[240,1],[240,2],[240,3],[240,4],[86,0],[86,3],[86,8],[245,0],[245,2],[173,3],[173,1],[252,3],[252,2],[252,3],[252,2],[252,3],[252,2],[252,1],[253,5],[253,3],[253,1],[110,5],[110,3],[110,3],[110,1],[93,1],[93,1],[93,1],[93,1],[93,1],[93,1],[93,1],[93,1],[93,1],[93,1],[93,1],[93,1],[93,1],[93,1],[93,1],[93,1],[93,1],[93,1],[93,3],[93,3],[93,3],[93,1],[93,1],[57,1],[262,2],[262,2],[260,6],[260,8],[260,6],[260,8],[270,1],[270,1],[270,1],[270,1],[270,1],[270,1],[270,1],[254,5],[254,6],[254,6],[271,0],[271,4],[271,4],[271,5],[273,3],[274,3],[157,1],[157,1],[157,1],[157,1],[157,1],[157,1],[157,1],[157,1],[157,1],[199,5],[199,3],[199,4],[199,4],[199,8],[199,8],[199,8],[199,8],[199,3],[150,1],[150,3],[195,1],[256,1],[256,1],[112,1],[112,1],[257,1],[201,2],[258,4],[261,3],[200,2],[200,2],[200,1],[200,1],[259,5],[259,4],[300,2],[300,1],[303,4],[301,2],[301,0],[255,3],[255,3],[255,3],[255,3],[255,5],[255,3],[255,5],[255,3],[255,3],[255,3],[255,3],[255,3],[255,3],[255,3],[255,3],[255,3],[255,3],[255,3],[255,3],[255,3],[255,5],[255,3],[255,3],[255,3],[255,5],[255,3],[255,3],[255,3],[255,3],[255,3],[255,3],[255,3],[255,3],[255,3],[255,3],[255,3],[255,6],[255,6],[255,3],[255,3],[255,2],[255,2],[255,2],[255,2],[255,2],[255,3],[255,5],[255,6],[255,5],[255,6],[255,4],[255,5],[255,3],[255,4],[255,3],[255,4],[255,3],[255,3],[255,3],[255,3],[255,3],[325,1],[325,1],[325,4],[323,1],[323,1],[323,1],[323,1],[323,1],[323,1],[324,1],[324,1],[324,1],[55,6],[55,4],[146,1],[146,3],[332,3],[332,4],[29,5],[29,3],[36,5],[36,7],[36,5],[36,5],[36,8],[36,4],[36,6],[36,7],[336,0],[336,1],[338,3],[338,1],[338,1],[338,5],[338,3],[338,3],[340,1],[340,3],[341,1],[341,1],[341,1],[341,1],[341,1],[341,1],[99,1],[99,3],[24,9],[24,5],[345,1],[345,1],[348,0],[348,1],[350,2],[350,1],[351,1],[351,3],[351,3],[351,3],[344,0],[344,1],[346,0],[346,3],[347,3],[347,1],[347,2],[355,1],[355,3],[356,2],[356,2],[356,2],[356,2],[356,2],[357,0],[357,2],[362,4],[358,6],[359,9],[373,3],[372,0],[372,2],[374,4],[375,4],[360,6],[361,5],[361,5],[368,1],[368,1],[368,3],[368,3],[354,1],[354,3],[380,3],[380,2],[380,1],[383,6],[383,4],[383,1],[383,4],[268,2],[268,1],[384,1],[384,1],[381,0],[381,1],[387,2],[387,1],[389,3],[388,2],[388,5],[388,3],[388,6],[388,1],[388,2],[388,4],[388,2],[388,1],[388,2],[388,1],[388,1],[388,3],[388,5],[33,4],[395,3],[395,1],[394,0],[394,2],[18,6],[18,6],[18,6],[18,8],[18,6],[39,5],[19,4],[19,7],[19,6],[19,9],[30,3],[21,4],[21,6],[21,9],[21,6],[403,0],[403,2],[54,3],[54,2],[31,4],[31,5],[31,5],[22,8],[22,9],[32,3],[43,2],[43,4],[43,3],[43,5],[45,2],[45,4],[45,4],[45,6],[42,4],[42,6],[44,4],[44,6],[41,4],[41,6],[25,11],[25,8],[409,3],[409,3],[409,5],[34,4],[56,2],[56,1],[67,2],[58,2],[59,2],[59,2],[59,4],[143,4],[143,2],[143,2],[143,2],[143,2],[143,1],[143,2],[143,2],[419,1],[419,1],[420,1],[420,1],[420,1],[420,1],[420,1],[420,1],[420,1],[420,3],[416,3],[416,4],[416,2],[418,2],[418,3],[418,1],[422,3],[422,1],[425,3],[425,3],[425,3],[424,3],[424,1],[66,4],[66,3],[66,4],[66,5],[66,5],[66,6],[428,1],[428,1],[427,3],[427,2],[429,1],[429,1],[429,3],[426,1],[426,1],[51,2],[52,2],[50,2],[35,4],[35,3],[435,2],[60,3],[61,1],[62,1],[63,3],[64,2],[64,2],[65,2],[65,2],[443,1],[443,1],[70,2],[441,3],[441,1],[442,3],[442,1],[28,2],[446,1],[446,3],[447,3],[447,4],[447,5],[447,6],[46,3],[37,6],[450,1],[450,2],[451,2],[452,2],[453,2],[453,2],[453,1],[453,1],[455,4],[455,6],[458,1],[458,3],[456,5],[456,7],[456,7],[456,9],[456,7],[456,9],[459,3],[459,6],[459,3],[459,6],[454,0],[454,2],[454,5],[454,4],[454,7],[27,6],[466,2],[465,0],[465,2],[465,2],[465,1],[26,8],[23,3],[23,4],[470,3],[470,1],[471,3],[471,7],[471,6],[471,3],[471,4],[475,1],[475,1],[479,2],[480,3],[481,2],[482,4],[472,4],[472,3],[472,2],[472,1],[494,2],[490,2],[490,2],[495,4],[497,6],[68,3],[68,2],[503,3],[503,1],[501,1],[501,4],[69,2],[20,2],[48,9],[48,8],[48,9],[507,0],[507,1],[507,1],[507,1],[507,2],[508,1],[508,1],[508,1],[49,3],[38,2],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[11,1],[11,1],[79,0],[79,1],[82,0],[82,1],[89,0],[89,2],[90,0],[90,1],[95,0],[95,1],[96,0],[96,1],[100,0],[100,1],[107,0],[107,1],[120,0],[120,1],[124,1],[124,2],[125,1],[125,2],[126,0],[126,1],[154,0],[154,2],[156,0],[156,2],[158,0],[158,2],[159,1],[159,1],[160,0],[160,2],[164,0],[164,2],[166,0],[166,2],[175,0],[175,2],[176,0],[176,2],[177,0],[177,2],[187,0],[187,1],[196,0],[196,1],[209,0],[209,1],[210,0],[210,1],[218,0],[218,1],[219,0],[219,1],[247,0],[247,1],[249,0],[249,1],[250,0],[250,1],[251,0],[251,1],[263,1],[263,1],[764,1],[764,1],[285,0],[285,1],[297,1],[297,1],[333,1],[333,1],[367,0],[367,1],[371,0],[371,1],[378,0],[378,1],[379,0],[379,1],[390,0],[390,1],[391,0],[391,1],[393,1],[393,1],[407,0],[407,1],[408,0],[408,1],[462,0],[462,1],[463,0],[463,1],[464,0],[464,1],[468,0],[468,1],[473,0],[473,1],[474,0],[474,1],[476,0],[476,1],[477,0],[477,1],[478,0],[478,1],[483,0],[483,1],[484,0],[484,1],[485,0],[485,1],[486,0],[486,1],[487,0],[487,1],[488,0],[488,1],[489,0],[489,1],[491,0],[491,1],[492,0],[492,1],[493,0],[493,1],[496,0],[496,2],[498,0],[498,2],[499,0],[499,2],[500,0],[500,2],[509,0],[509,1],[510,0],[510,1]],performAction:function(e,t,n,r,a,s,i){var o=s.length-1;switch(a){case 1:r.casesensitive?this.$=s[o]:this.$=s[o].toLowerCase();break;case 2:this.$=I(s[o].substr(1,s[o].length-2));break;case 3:this.$=s[o].toLowerCase();break;case 4:this.$=s[o];break;case 5:this.$=s[o]?s[o-1]+" "+s[o]:s[o-1];break;case 6:return new r.Statements({statements:s[o-1]});case 7:this.$=s[o-2],s[o]&&s[o-2].push(s[o]);break;case 8:case 9:case 69:case 79:case 84:case 142:case 176:case 204:case 205:case 241:case 260:case 272:case 351:case 369:case 448:case 465:case 466:case 470:case 478:case 519:case 520:case 557:case 642:case 652:case 676:case 678:case 680:case 694:case 695:case 725:case 749:this.$=[s[o]];break;case 10:this.$=s[o],s[o].explain=!0;break;case 11:this.$=s[o],s[o].explain=!0;break;case 12:this.$=s[o],r.exists&&(this.$.exists=r.exists),delete r.exists,r.queries&&(this.$.queries=r.queries),delete r.queries;break;case 13:case 161:case 171:case 236:case 237:case 239:case 247:case 249:case 258:case 266:case 269:case 372:case 482:case 492:case 494:case 506:case 512:case 513:case 558:this.$=void 0;break;case 67:this.$=new r.WithSelect({withs:s[o-1],select:s[o]});break;case 68:case 556:s[o-2].push(s[o]),this.$=s[o-2];break;case 70:this.$={name:s[o-4],select:s[o-1]};break;case 71:r.extend(this.$,s[o-9]),r.extend(this.$,s[o-8]),r.extend(this.$,s[o-7]),r.extend(this.$,s[o-6]),r.extend(this.$,s[o-5]),r.extend(this.$,s[o-4]),r.extend(this.$,s[o-3]),r.extend(this.$,s[o-2]),r.extend(this.$,s[o-1]),r.extend(this.$,s[o]),this.$=s[o-9];break;case 72:this.$=new r.Search({selectors:s[o-2],from:s[o]}),r.extend(this.$,s[o-1]);break;case 73:this.$={pivot:{expr:s[o-5],columnid:s[o-3],inlist:s[o-2],as:s[o]}};break;case 74:this.$={unpivot:{tocolumnid:s[o-8],forcolumnid:s[o-6],inlist:s[o-3],as:s[o]}};break;case 75:case 511:case 540:case 576:case 612:case 629:case 630:case 633:case 655:this.$=s[o-1];break;case 76:case 77:case 85:case 146:case 184:case 246:case 279:case 287:case 288:case 289:case 290:case 291:case 292:case 293:case 294:case 295:case 296:case 297:case 298:case 299:case 300:case 303:case 304:case 317:case 318:case 319:case 320:case 321:case 322:case 371:case 437:case 438:case 439:case 440:case 441:case 442:case 507:case 533:case 537:case 539:case 616:case 617:case 618:case 619:case 620:case 621:case 625:case 627:case 628:case 637:case 653:case 654:case 716:case 731:case 732:case 734:case 735:case 741:case 742:this.$=s[o];break;case 78:case 83:case 724:case 748:this.$=s[o-2],this.$.push(s[o]);break;case 80:this.$={expr:s[o]};break;case 81:this.$={expr:s[o-2],as:s[o]};break;case 82:this.$={removecolumns:s[o]};break;case 86:this.$={like:s[o]};break;case 89:case 103:this.$={srchid:"PROP",args:[s[o]]};break;case 90:this.$={srchid:"ORDERBY",args:s[o-1]};break;case 91:var u=s[o-1];u||(u="ASC"),this.$={srchid:"ORDERBY",args:[{expression:new r.Column({columnid:"_"}),direction:u}]};break;case 92:this.$={srchid:"PARENT"};break;case 93:this.$={srchid:"APROP",args:[s[o]]};break;case 94:this.$={selid:"ROOT"};break;case 95:this.$={srchid:"EQ",args:[s[o]]};break;case 96:this.$={srchid:"LIKE",args:[s[o]]};break;case 97:case 98:this.$={selid:"WITH",args:s[o-1]};break;case 99:this.$={srchid:s[o-3].toUpperCase(),args:s[o-1]};break;case 100:this.$={srchid:"WHERE",args:[s[o-1]]};break;case 101:this.$={selid:"OF",args:[s[o-1]]};break;case 102:this.$={srchid:"CLASS",args:[s[o-1]]};break;case 104:this.$={srchid:"NAME",args:[s[o].substr(1,s[o].length-2)]};break;case 105:this.$={srchid:"CHILD"};break;case 106:this.$={srchid:"VERTEX"};break;case 107:this.$={srchid:"EDGE"};break;case 108:this.$={srchid:"REF"};break;case 109:this.$={srchid:"SHARP",args:[s[o]]};break;case 110:this.$={srchid:"ATTR",args:"undefined"==typeof s[o]?void 0:[s[o]]};break;case 111:this.$={srchid:"ATTR"};break;case 112:this.$={srchid:"OUT"};break;case 113:this.$={srchid:"IN"};break;case 114:this.$={srchid:"OUTOUT"};break;case 115:this.$={srchid:"ININ"};break;case 116:this.$={srchid:"CONTENT"};break;case 117:this.$={srchid:"EX",args:[new r.Json({value:s[o]})]};break;case 118:this.$={srchid:"AT",args:[s[o]]};break;case 119:this.$={srchid:"AS",args:[s[o]]};break;case 120:this.$={srchid:"SET",args:s[o-1]};break;case 121:this.$={selid:"TO",args:[s[o]]};break;case 122:this.$={srchid:"VALUE"};break;case 123:this.$={srchid:"ROW",args:s[o-1]};break;case 124:this.$={srchid:"CLASS",args:[s[o]]};break;case 125:this.$={selid:s[o],args:[s[o-1]]};break;case 126:this.$={selid:"NOT",args:s[o-1]};break;case 127:this.$={selid:"IF",args:s[o-1]};break;case 128:this.$={selid:s[o-3],args:s[o-1]};break;case 129:this.$={selid:"DISTINCT",args:s[o-1]};break;case 130:this.$={selid:"UNION",args:s[o-1]};break;case 131:this.$={selid:"UNIONALL",args:s[o-1]};break;case 132:this.$={selid:"ALL",args:[s[o-1]]};break;case 133:this.$={selid:"ANY",args:[s[o-1]]};break;case 134:this.$={selid:"INTERSECT",args:s[o-1]};break;case 135:this.$={selid:"EXCEPT",args:s[o-1]};break;case 136:this.$={selid:"AND",args:s[o-1]};break;case 137:this.$={selid:"OR",args:s[o-1]};break;case 138:this.$={selid:"PATH",args:[s[o-1]]};break;case 139:this.$={srchid:"RETURN",args:s[o-1]};break;case 140:this.$={selid:"REPEAT",sels:s[o-3],args:s[o-1]};break;case 141:this.$=s[o-2],this.$.push(s[o]);break;case 143:this.$="PLUS";break;case 144:this.$="STAR";break;case 145:this.$="QUESTION";break;case 147:this.$=new r.Select({columns:s[o],distinct:!0}),r.extend(this.$,s[o-3]),r.extend(this.$,s[o-1]);
break;case 148:this.$=new r.Select({columns:s[o],distinct:!0}),r.extend(this.$,s[o-3]),r.extend(this.$,s[o-1]);break;case 149:this.$=new r.Select({columns:s[o],all:!0}),r.extend(this.$,s[o-3]),r.extend(this.$,s[o-1]);break;case 150:s[o]?(this.$=new r.Select({columns:s[o]}),r.extend(this.$,s[o-2]),r.extend(this.$,s[o-1])):this.$=new r.Select({columns:[new r.Column({columnid:"_"})],modifier:"COLUMN"});break;case 151:"SELECT"==s[o]?this.$=void 0:this.$={modifier:s[o]};break;case 152:this.$={modifier:"VALUE"};break;case 153:this.$={modifier:"ROW"};break;case 154:this.$={modifier:"COLUMN"};break;case 155:this.$={modifier:"MATRIX"};break;case 156:this.$={modifier:"TEXTSTRING"};break;case 157:this.$={modifier:"INDEX"};break;case 158:this.$={modifier:"RECORDSET"};break;case 159:this.$={top:s[o-1],percent:"undefined"!=typeof s[o]||void 0};break;case 160:this.$={top:s[o-1]};break;case 162:case 327:case 514:case 515:case 717:this.$=void 0;break;case 163:case 164:case 165:case 166:this.$={into:s[o]};break;case 167:var c=s[o];c=c.substr(1,c.length-2);var l=c.substr(-3).toUpperCase(),h=c.substr(-4).toUpperCase();"#"==c[0]?this.$={into:new r.FuncValue({funcid:"HTML",args:[new r.StringValue({value:c}),new r.Json({value:{headers:!0}})]})}:"XLS"==l||"CSV"==l||"TAB"==l?this.$={into:new r.FuncValue({funcid:l,args:[new r.StringValue({value:c}),new r.Json({value:{headers:!0}})]})}:"XLSX"!=h&&"JSON"!=h||(this.$={into:new r.FuncValue({funcid:h,args:[new r.StringValue({value:c}),new r.Json({value:{headers:!0}})]})});break;case 168:this.$={from:s[o]};break;case 169:this.$={from:s[o-1],joins:s[o]};break;case 170:this.$={from:s[o-2],joins:s[o-1]};break;case 172:this.$=new r.Apply({select:s[o-2],applymode:"CROSS",as:s[o]});break;case 173:this.$=new r.Apply({select:s[o-3],applymode:"CROSS",as:s[o]});break;case 174:this.$=new r.Apply({select:s[o-2],applymode:"OUTER",as:s[o]});break;case 175:this.$=new r.Apply({select:s[o-3],applymode:"OUTER",as:s[o]});break;case 177:case 242:case 449:case 521:case 522:this.$=s[o-2],s[o-2].push(s[o]);break;case 178:this.$=s[o-2],this.$.as=s[o];break;case 179:this.$=s[o-3],this.$.as=s[o];break;case 180:this.$=s[o-1],this.$.as="default";break;case 181:this.$=new r.Json({value:s[o-2]}),s[o-2].as=s[o];break;case 182:this.$=s[o-1],s[o-1].as=s[o];break;case 183:this.$=s[o-2],s[o-2].as=s[o];break;case 185:case 631:case 634:this.$=s[o-2];break;case 186:case 190:case 194:case 197:this.$=s[o-1],s[o-1].as=s[o];break;case 187:case 191:case 195:case 198:this.$=s[o-2],s[o-2].as=s[o];break;case 188:case 189:case 193:case 196:this.$=s[o],s[o].as="default";break;case 192:this.$={inserted:!0},s[o].as="default";break;case 199:var c=s[o];c=c.substr(1,c.length-2);var d,l=c.substr(-3).toUpperCase(),h=c.substr(-4).toUpperCase();if("#"==c[0])d=new r.FuncValue({funcid:"HTML",args:[new r.StringValue({value:c}),new r.Json({value:{headers:!0}})]});else if("XLS"==l||"CSV"==l||"TAB"==l)d=new r.FuncValue({funcid:l,args:[new r.StringValue({value:c}),new r.Json({value:{headers:!0}})]});else{if("XLSX"!=h&&"JSON"!=h)throw new Error("Unknown string in FROM clause");d=new r.FuncValue({funcid:h,args:[new r.StringValue({value:c}),new r.Json({value:{headers:!0}})]})}this.$=d;break;case 200:"INFORMATION_SCHEMA"==s[o-2]?this.$=new r.FuncValue({funcid:s[o-2],args:[new r.StringValue({value:s[o]})]}):this.$=new r.Table({databaseid:s[o-2],tableid:s[o]});break;case 201:this.$=new r.Table({tableid:s[o]});break;case 202:case 203:this.$=s[o-1],s[o-1].push(s[o]);break;case 206:this.$=new r.Join(s[o-2]),r.extend(this.$,s[o-1]),r.extend(this.$,s[o]);break;case 207:this.$={table:s[o]};break;case 208:this.$={table:s[o-1],as:s[o]};break;case 209:this.$={table:s[o-2],as:s[o]};break;case 210:this.$={json:new r.Json({value:s[o-2],as:s[o]})};break;case 211:this.$={param:s[o-1],as:s[o]};break;case 212:this.$={param:s[o-2],as:s[o]};break;case 213:this.$={select:s[o-3],as:s[o]};break;case 214:this.$={select:s[o-4],as:s[o]};break;case 215:this.$={funcid:s[o],as:"default"};break;case 216:this.$={funcid:s[o-1],as:s[o]};break;case 217:this.$={funcid:s[o-2],as:s[o]};break;case 218:this.$={variable:s[o],as:"default"};break;case 219:this.$={variable:s[o-1],as:s[o]};break;case 220:this.$={variable:s[o-2],as:s[o]};break;case 221:this.$={joinmode:s[o]};break;case 222:this.$={joinmode:s[o-1],natural:!0};break;case 223:case 224:this.$="INNER";break;case 225:case 226:this.$="LEFT";break;case 227:case 228:this.$="RIGHT";break;case 229:case 230:this.$="OUTER";break;case 231:this.$="SEMI";break;case 232:this.$="ANTI";break;case 233:this.$="CROSS";break;case 234:this.$={on:s[o]};break;case 235:case 690:this.$={using:s[o]};break;case 238:this.$={where:new r.Expression({expression:s[o]})};break;case 240:this.$={group:s[o-1]},r.extend(this.$,s[o]);break;case 243:this.$=new r.GroupExpression({type:"GROUPING SETS",group:s[o-1]});break;case 244:this.$=new r.GroupExpression({type:"ROLLUP",group:s[o-1]});break;case 245:this.$=new r.GroupExpression({type:"CUBE",group:s[o-1]});break;case 248:this.$={having:s[o]};break;case 250:this.$={union:s[o]};break;case 251:this.$={unionall:s[o]};break;case 252:this.$={except:s[o]};break;case 253:this.$={intersect:s[o]};break;case 254:this.$={union:s[o],corresponding:!0};break;case 255:this.$={unionall:s[o],corresponding:!0};break;case 256:this.$={except:s[o],corresponding:!0};break;case 257:this.$={intersect:s[o],corresponding:!0};break;case 259:this.$={order:s[o]};break;case 261:this.$=s[o-2],s[o-2].push(s[o]);break;case 262:this.$=new r.Expression({expression:s[o],direction:"ASC"});break;case 263:this.$=new r.Expression({expression:s[o-1],direction:s[o].toUpperCase()});break;case 264:this.$=new r.Expression({expression:s[o-2],direction:"ASC",nocase:!0});break;case 265:this.$=new r.Expression({expression:s[o-3],direction:s[o].toUpperCase(),nocase:!0});break;case 267:this.$={limit:s[o-1]},r.extend(this.$,s[o]);break;case 268:this.$={limit:s[o-2],offset:s[o-6]};break;case 270:this.$={offset:s[o]};break;case 271:case 500:case 524:case 641:case 651:case 675:case 677:case 681:s[o-2].push(s[o]),this.$=s[o-2];break;case 273:case 275:case 277:s[o-2].as=s[o],this.$=s[o-2];break;case 274:case 276:case 278:s[o-1].as=s[o],this.$=s[o-1];break;case 280:this.$=new r.Column({columid:s[o],tableid:s[o-2],databaseid:s[o-4]});break;case 281:this.$=new r.Column({columnid:s[o],tableid:s[o-2]});break;case 282:this.$=new r.Column({columnid:s[o]});break;case 283:this.$=new r.Column({columnid:s[o],tableid:s[o-2],databaseid:s[o-4]});break;case 284:case 285:this.$=new r.Column({columnid:s[o],tableid:s[o-2]});break;case 286:this.$=new r.Column({columnid:s[o]});break;case 301:this.$=new r.DomainValueValue;break;case 302:this.$=new r.Json({value:s[o]});break;case 305:case 306:case 307:r.queries||(r.queries=[]),r.queries.push(s[o-1]),s[o-1].queriesidx=r.queries.length,this.$=s[o-1];break;case 308:this.$=s[o];break;case 309:this.$=new r.FuncValue({funcid:"CURRENT_TIMESTAMP"});break;case 310:this.$=new r.JavaScript({value:s[o].substr(2,s[o].length-4)});break;case 311:this.$=new r.FuncValue({funcid:s[o],newid:!0});break;case 312:this.$=s[o],r.extend(this.$,{newid:!0});break;case 313:this.$=new r.Convert({expression:s[o-3]}),r.extend(this.$,s[o-1]);break;case 314:this.$=new r.Convert({expression:s[o-5],style:s[o-1]}),r.extend(this.$,s[o-3]);break;case 315:this.$=new r.Convert({expression:s[o-1]}),r.extend(this.$,s[o-3]);break;case 316:this.$=new r.Convert({expression:s[o-3],style:s[o-1]}),r.extend(this.$,s[o-5]);break;case 323:this.$=new r.FuncValue({funcid:"CURRENT_TIMESTAMP"});break;case 324:s[o-2].length>1&&("MAX"==s[o-4].toUpperCase()||"MIN"==s[o-4].toUpperCase())?this.$=new r.FuncValue({funcid:s[o-4],args:s[o-2]}):this.$=new r.AggrValue({aggregatorid:s[o-4].toUpperCase(),expression:s[o-2].pop(),over:s[o]});break;case 325:this.$=new r.AggrValue({aggregatorid:s[o-5].toUpperCase(),expression:s[o-2],distinct:!0,over:s[o]});break;case 326:this.$=new r.AggrValue({aggregatorid:s[o-5].toUpperCase(),expression:s[o-2],over:s[o]});break;case 328:case 329:this.$=new r.Over,r.extend(this.$,s[o-1]);break;case 330:this.$=new r.Over,r.extend(this.$,s[o-2]),r.extend(this.$,s[o-1]);break;case 331:this.$={partition:s[o]};break;case 332:this.$={order:s[o]};break;case 333:this.$="SUM";break;case 334:this.$="COUNT";break;case 335:this.$="MIN";break;case 336:case 535:this.$="MAX";break;case 337:this.$="AVG";break;case 338:this.$="FIRST";break;case 339:this.$="LAST";break;case 340:this.$="AGGR";break;case 341:this.$="ARRAY";break;case 342:var f=s[o-4],p=s[o-1];p.length>1&&("MIN"==f.toUpperCase()||"MAX"==f.toUpperCase())?this.$=new r.FuncValue({funcid:f,args:p}):A.aggr[s[o-4]]?this.$=new r.AggrValue({aggregatorid:"REDUCE",funcid:f,expression:p.pop(),distinct:"DISTINCT"==s[o-2]}):this.$=new r.FuncValue({funcid:f,args:p});break;case 343:this.$=new r.FuncValue({funcid:s[o-2]});break;case 344:this.$=new r.FuncValue({funcid:"IIF",args:s[o-1]});break;case 345:this.$=new r.FuncValue({funcid:"REPLACE",args:s[o-1]});break;case 346:this.$=new r.FuncValue({funcid:"DATEADD",args:[new r.StringValue({value:s[o-5]}),s[o-3],s[o-1]]});break;case 347:this.$=new r.FuncValue({funcid:"DATEADD",args:[s[o-5],s[o-3],s[o-1]]});break;case 348:this.$=new r.FuncValue({funcid:"DATEDIFF",args:[new r.StringValue({value:s[o-5]}),s[o-3],s[o-1]]});break;case 349:this.$=new r.FuncValue({funcid:"DATEDIFF",args:[s[o-5],s[o-3],s[o-1]]});break;case 350:this.$=new r.FuncValue({funcid:"INTERVAL",args:[s[o-1],new r.StringValue({value:s[o].toLowerCase()})]});break;case 352:s[o-2].push(s[o]),this.$=s[o-2];break;case 353:this.$=new r.NumValue({value:+s[o]});break;case 354:this.$=new r.LogicValue({value:!0});break;case 355:this.$=new r.LogicValue({value:!1});break;case 356:this.$=new r.StringValue({value:s[o].substr(1,s[o].length-2).replace(/(\\\')/g,"'").replace(/(\'\')/g,"'")});break;case 357:this.$=new r.StringValue({value:s[o].substr(2,s[o].length-3).replace(/(\\\')/g,"'").replace(/(\'\')/g,"'")});break;case 358:this.$=new r.NullValue({value:void 0});break;case 359:this.$=new r.VarValue({variable:s[o]});break;case 360:r.exists||(r.exists=[]),this.$=new r.ExistsValue({value:s[o-1],existsidx:r.exists.length}),r.exists.push(s[o-1]);break;case 361:this.$=new r.ArrayValue({value:s[o-1]});break;case 362:case 363:this.$=new r.ParamValue({param:s[o]});break;case 364:"undefined"==typeof r.question&&(r.question=0),this.$=new r.ParamValue({param:r.question++});break;case 365:"undefined"==typeof r.question&&(r.question=0),this.$=new r.ParamValue({param:r.question++,array:!0});break;case 366:this.$=new r.CaseValue({expression:s[o-3],whens:s[o-2],elses:s[o-1]});break;case 367:this.$=new r.CaseValue({whens:s[o-2],elses:s[o-1]});break;case 368:case 692:case 693:this.$=s[o-1],this.$.push(s[o]);break;case 370:this.$={when:s[o-2],then:s[o]};break;case 373:case 374:this.$=new r.Op({left:s[o-2],op:"REGEXP",right:s[o]});break;case 375:this.$=new r.Op({left:s[o-2],op:"GLOB",right:s[o]});break;case 376:this.$=new r.Op({left:s[o-2],op:"LIKE",right:s[o]});break;case 377:this.$=new r.Op({left:s[o-4],op:"LIKE",right:s[o-2],escape:s[o]});break;case 378:this.$=new r.Op({left:s[o-2],op:"NOT LIKE",right:s[o]});break;case 379:this.$=new r.Op({left:s[o-4],op:"NOT LIKE",right:s[o-2],escape:s[o]});break;case 380:this.$=new r.Op({left:s[o-2],op:"||",right:s[o]});break;case 381:this.$=new r.Op({left:s[o-2],op:"+",right:s[o]});break;case 382:this.$=new r.Op({left:s[o-2],op:"-",right:s[o]});break;case 383:this.$=new r.Op({left:s[o-2],op:"*",right:s[o]});break;case 384:this.$=new r.Op({left:s[o-2],op:"/",right:s[o]});break;case 385:this.$=new r.Op({left:s[o-2],op:"%",right:s[o]});break;case 386:this.$=new r.Op({left:s[o-2],op:"^",right:s[o]});break;case 387:this.$=new r.Op({left:s[o-2],op:">>",right:s[o]});break;case 388:this.$=new r.Op({left:s[o-2],op:"<<",right:s[o]});break;case 389:this.$=new r.Op({left:s[o-2],op:"&",right:s[o]});break;case 390:this.$=new r.Op({left:s[o-2],op:"|",right:s[o]});break;case 391:case 392:case 394:this.$=new r.Op({left:s[o-2],op:"->",right:s[o]});break;case 393:this.$=new r.Op({left:s[o-4],op:"->",right:s[o-1]});break;case 395:case 396:case 398:this.$=new r.Op({left:s[o-2],op:"!",right:s[o]});break;case 397:this.$=new r.Op({left:s[o-4],op:"!",right:s[o-1]});break;case 399:this.$=new r.Op({left:s[o-2],op:">",right:s[o]});break;case 400:this.$=new r.Op({left:s[o-2],op:">=",right:s[o]});break;case 401:this.$=new r.Op({left:s[o-2],op:"<",right:s[o]});break;case 402:this.$=new r.Op({left:s[o-2],op:"<=",right:s[o]});break;case 403:this.$=new r.Op({left:s[o-2],op:"=",right:s[o]});break;case 404:this.$=new r.Op({left:s[o-2],op:"==",right:s[o]});break;case 405:this.$=new r.Op({left:s[o-2],op:"===",right:s[o]});break;case 406:this.$=new r.Op({left:s[o-2],op:"!=",right:s[o]});break;case 407:this.$=new r.Op({left:s[o-2],op:"!==",right:s[o]});break;case 408:this.$=new r.Op({left:s[o-2],op:"!===",right:s[o]});break;case 409:r.queries||(r.queries=[]),this.$=new r.Op({left:s[o-5],op:s[o-4],allsome:s[o-3],right:s[o-1],queriesidx:r.queries.length}),r.queries.push(s[o-1]);break;case 410:this.$=new r.Op({left:s[o-5],op:s[o-4],allsome:s[o-3],right:s[o-1]});break;case 411:"BETWEEN1"==s[o-2].op?"AND"==s[o-2].left.op?this.$=new r.Op({left:s[o-2].left.left,op:"AND",right:new r.Op({left:s[o-2].left.right,op:"BETWEEN",right1:s[o-2].right,right2:s[o]})}):this.$=new r.Op({left:s[o-2].left,op:"BETWEEN",right1:s[o-2].right,right2:s[o]}):"NOT BETWEEN1"==s[o-2].op?"AND"==s[o-2].left.op?this.$=new r.Op({left:s[o-2].left.left,op:"AND",right:new r.Op({left:s[o-2].left.right,op:"NOT BETWEEN",right1:s[o-2].right,right2:s[o]})}):this.$=new r.Op({left:s[o-2].left,op:"NOT BETWEEN",right1:s[o-2].right,right2:s[o]}):this.$=new r.Op({left:s[o-2],op:"AND",right:s[o]});break;case 412:this.$=new r.Op({left:s[o-2],op:"OR",right:s[o]});break;case 413:this.$=new r.UniOp({op:"NOT",right:s[o]});break;case 414:this.$=new r.UniOp({op:"-",right:s[o]});break;case 415:this.$=new r.UniOp({op:"+",right:s[o]});break;case 416:this.$=new r.UniOp({op:"~",right:s[o]});break;case 417:this.$=new r.UniOp({op:"#",right:s[o]});break;case 418:this.$=new r.UniOp({right:s[o-1]});break;case 419:r.queries||(r.queries=[]),this.$=new r.Op({left:s[o-4],op:"IN",right:s[o-1],queriesidx:r.queries.length}),r.queries.push(s[o-1]);break;case 420:r.queries||(r.queries=[]),this.$=new r.Op({left:s[o-5],op:"NOT IN",right:s[o-1],queriesidx:r.queries.length}),r.queries.push(s[o-1]);break;case 421:this.$=new r.Op({left:s[o-4],op:"IN",right:s[o-1]});break;case 422:this.$=new r.Op({left:s[o-5],op:"NOT IN",right:s[o-1]});break;case 423:this.$=new r.Op({left:s[o-3],op:"IN",right:[]});break;case 424:this.$=new r.Op({left:s[o-4],op:"NOT IN",right:[]});break;case 425:case 427:this.$=new r.Op({left:s[o-2],op:"IN",right:s[o]});break;case 426:case 428:this.$=new r.Op({left:s[o-3],op:"NOT IN",right:s[o]});break;case 429:this.$=new r.Op({left:s[o-2],op:"BETWEEN1",right:s[o]});break;case 430:this.$=new r.Op({left:s[o-2],op:"NOT BETWEEN1",right:s[o]});break;case 431:this.$=new r.Op({op:"IS",left:s[o-2],right:s[o]});break;case 432:this.$=new r.Op({op:"IS",left:s[o-2],right:new r.UniOp({op:"NOT",right:new r.NullValue({value:void 0})})});break;case 433:this.$=new r.Convert({expression:s[o-2]}),r.extend(this.$,s[o]);break;case 434:case 435:this.$=s[o];break;case 436:this.$=s[o-1];break;case 443:this.$="ALL";break;case 444:this.$="SOME";break;case 445:this.$="ANY";break;case 446:this.$=new r.Update({table:s[o-4],columns:s[o-2],where:s[o]});break;case 447:this.$=new r.Update({table:s[o-2],columns:s[o]});break;case 450:this.$=new r.SetColumn({column:s[o-2],expression:s[o]});break;case 451:this.$=new r.SetColumn({variable:s[o-2],expression:s[o],method:s[o-3]});break;case 452:this.$=new r.Delete({table:s[o-2],where:s[o]});break;case 453:this.$=new r.Delete({table:s[o]});break;case 454:this.$=new r.Insert({into:s[o-2],values:s[o]});break;case 455:case 456:this.$=new r.Insert({into:s[o-2],values:s[o],orreplace:!0});break;case 457:this.$=new r.Insert({into:s[o-2],default:!0});break;case 458:this.$=new r.Insert({into:s[o-5],columns:s[o-3],values:s[o]});break;case 459:this.$=new r.Insert({into:s[o-1],select:s[o]});break;case 460:this.$=new r.Insert({into:s[o-1],select:s[o],orreplace:!0});break;case 461:this.$=new r.Insert({into:s[o-4],columns:s[o-2],select:s[o]});break;case 464:this.$=[s[o-1]];break;case 467:this.$=s[o-4],s[o-4].push(s[o-1]);break;case 468:case 469:case 471:case 479:this.$=s[o-2],s[o-2].push(s[o]);break;case 480:this.$=new r.CreateTable({table:s[o-4]}),r.extend(this.$,s[o-7]),r.extend(this.$,s[o-6]),r.extend(this.$,s[o-5]),r.extend(this.$,s[o-2]),r.extend(this.$,s[o]);break;case 481:this.$=new r.CreateTable({table:s[o]}),r.extend(this.$,s[o-3]),r.extend(this.$,s[o-2]),r.extend(this.$,s[o-1]);break;case 483:this.$={class:!0};break;case 493:this.$={temporary:!0};break;case 495:this.$={ifnotexists:!0};break;case 496:this.$={columns:s[o-2],constraints:s[o]};break;case 497:this.$={columns:s[o]};break;case 498:this.$={as:s[o]};break;case 499:case 523:this.$=[s[o]];break;case 501:case 502:case 503:case 504:case 505:s[o].constraintid=s[o-1],this.$=s[o];break;case 508:this.$={type:"CHECK",expression:s[o-1]};break;case 509:this.$={type:"PRIMARY KEY",columns:s[o-1],clustered:(s[o-3]+"").toUpperCase()};break;case 510:this.$={type:"FOREIGN KEY",columns:s[o-5],fktable:s[o-2],fkcolumns:s[o-1]};break;case 516:this.$={type:"UNIQUE",columns:s[o-1],clustered:(s[o-3]+"").toUpperCase()};break;case 525:this.$=new r.ColumnDef({columnid:s[o-2]}),r.extend(this.$,s[o-1]),r.extend(this.$,s[o]);break;case 526:this.$=new r.ColumnDef({columnid:s[o-1]}),r.extend(this.$,s[o]);break;case 527:this.$=new r.ColumnDef({columnid:s[o],dbtypeid:""});break;case 528:this.$={dbtypeid:s[o-5],dbsize:s[o-3],dbprecision:+s[o-1]};break;case 529:this.$={dbtypeid:s[o-3],dbsize:s[o-1]};break;case 530:this.$={dbtypeid:s[o]};break;case 531:this.$={dbtypeid:"ENUM",enumvalues:s[o-1]};break;case 532:this.$=s[o-1],s[o-1].dbtypeid+="["+s[o]+"]";break;case 534:case 743:this.$=+s[o];break;case 536:this.$=void 0;break;case 538:r.extend(s[o-1],s[o]),this.$=s[o-1];break;case 541:this.$={primarykey:!0};break;case 542:case 543:this.$={foreignkey:{table:s[o-1],columnid:s[o]}};break;case 544:this.$={identity:{value:s[o-3],step:s[o-1]}};break;case 545:this.$={identity:{value:1,step:1}};break;case 546:case 548:this.$={default:s[o]};break;case 547:this.$={default:s[o-1]};break;case 549:this.$={null:!0};break;case 550:this.$={notnull:!0};break;case 551:this.$={check:s[o]};break;case 552:this.$={unique:!0};break;case 553:this.$={onupdate:s[o]};break;case 554:this.$={onupdate:s[o-1]};break;case 555:this.$=new r.DropTable({tables:s[o],type:s[o-2]}),r.extend(this.$,s[o-1]);break;case 559:this.$={ifexists:!0};break;case 560:this.$=new r.AlterTable({table:s[o-3],renameto:s[o]});break;case 561:this.$=new r.AlterTable({table:s[o-3],addcolumn:s[o]});break;case 562:this.$=new r.AlterTable({table:s[o-3],modifycolumn:s[o]});break;case 563:this.$=new r.AlterTable({table:s[o-5],renamecolumn:s[o-2],to:s[o]});break;case 564:this.$=new r.AlterTable({table:s[o-3],dropcolumn:s[o]});break;case 565:this.$=new r.AlterTable({table:s[o-2],renameto:s[o]});break;case 566:this.$=new r.AttachDatabase({databaseid:s[o],engineid:s[o-2].toUpperCase()});break;case 567:this.$=new r.AttachDatabase({databaseid:s[o-3],engineid:s[o-5].toUpperCase(),args:s[o-1]});break;case 568:this.$=new r.AttachDatabase({databaseid:s[o-2],engineid:s[o-4].toUpperCase(),as:s[o]});break;case 569:this.$=new r.AttachDatabase({databaseid:s[o-5],engineid:s[o-7].toUpperCase(),as:s[o],args:s[o-3]});break;case 570:this.$=new r.DetachDatabase({databaseid:s[o]});break;case 571:this.$=new r.CreateDatabase({databaseid:s[o]}),r.extend(this.$,s[o]);break;case 572:this.$=new r.CreateDatabase({engineid:s[o-4].toUpperCase(),databaseid:s[o-1],as:s[o]}),r.extend(this.$,s[o-2]);break;case 573:this.$=new r.CreateDatabase({engineid:s[o-7].toUpperCase(),databaseid:s[o-4],args:s[o-2],as:s[o]}),r.extend(this.$,s[o-5]);break;case 574:this.$=new r.CreateDatabase({engineid:s[o-4].toUpperCase(),as:s[o],args:[s[o-1]]}),r.extend(this.$,s[o-2]);break;case 575:this.$=void 0;break;case 577:case 578:this.$=new r.UseDatabase({databaseid:s[o]});break;case 579:this.$=new r.DropDatabase({databaseid:s[o]}),r.extend(this.$,s[o-1]);break;case 580:case 581:this.$=new r.DropDatabase({databaseid:s[o],engineid:s[o-3].toUpperCase()}),r.extend(this.$,s[o-1]);break;case 582:this.$=new r.CreateIndex({indexid:s[o-5],table:s[o-3],columns:s[o-1]});break;case 583:this.$=new r.CreateIndex({indexid:s[o-5],table:s[o-3],columns:s[o-1],unique:!0});break;case 584:this.$=new r.DropIndex({indexid:s[o]});break;case 585:this.$=new r.ShowDatabases;break;case 586:this.$=new r.ShowDatabases({like:s[o]});break;case 587:this.$=new r.ShowDatabases({engineid:s[o-1].toUpperCase()});break;case 588:this.$=new r.ShowDatabases({engineid:s[o-3].toUpperCase(),like:s[o]});break;case 589:this.$=new r.ShowTables;break;case 590:this.$=new r.ShowTables({like:s[o]});break;case 591:this.$=new r.ShowTables({databaseid:s[o]});break;case 592:this.$=new r.ShowTables({like:s[o],databaseid:s[o-2]});break;case 593:this.$=new r.ShowColumns({table:s[o]});break;case 594:this.$=new r.ShowColumns({table:s[o-2],databaseid:s[o]});break;case 595:this.$=new r.ShowIndex({table:s[o]});break;case 596:this.$=new r.ShowIndex({table:s[o-2],databaseid:s[o]});break;case 597:this.$=new r.ShowCreateTable({table:s[o]});break;case 598:this.$=new r.ShowCreateTable({table:s[o-2],databaseid:s[o]});break;case 599:this.$=new r.CreateTable({table:s[o-6],view:!0,select:s[o-1],viewcolumns:s[o-4]}),r.extend(this.$,s[o-9]),r.extend(this.$,s[o-7]);break;case 600:this.$=new r.CreateTable({table:s[o-3],view:!0,select:s[o-1]}),r.extend(this.$,s[o-6]),r.extend(this.$,s[o-4]);break;case 604:this.$=new r.DropTable({tables:s[o],view:!0}),r.extend(this.$,s[o-1]);break;case 605:this.$=new r.Help({subject:s[o].value.toUpperCase()});break;case 606:this.$=new r.Help;break;case 607:case 753:this.$=new r.ExpressionStatement({expression:s[o]});break;case 608:this.$=new r.Source({url:s[o].value});break;case 609:this.$=new r.Assert({value:s[o]});break;case 610:this.$=new r.Assert({value:s[o].value});break;case 611:this.$=new r.Assert({value:s[o],message:s[o-2]});break;case 613:case 624:case 626:this.$=s[o].value;break;case 614:case 622:this.$=+s[o].value;break;case 615:this.$=!!s[o].value;break;case 623:this.$=""+s[o].value;break;case 632:this.$={};break;case 635:this.$=[];break;case 636:r.extend(s[o-2],s[o]),this.$=s[o-2];break;case 638:this.$={},this.$[s[o-2].substr(1,s[o-2].length-2)]=s[o];break;case 639:case 640:this.$={},this.$[s[o-2]]=s[o];break;case 643:this.$=new r.SetVariable({variable:s[o-2].toLowerCase(),value:s[o]});break;case 644:this.$=new r.SetVariable({variable:s[o-1].toLowerCase(),value:s[o]});break;case 645:this.$=new r.SetVariable({variable:s[o-2],expression:s[o]});break;case 646:this.$=new r.SetVariable({variable:s[o-3],props:s[o-2],expression:s[o]});break;case 647:this.$=new r.SetVariable({variable:s[o-2],expression:s[o],method:s[o-3]});break;case 648:this.$=new r.SetVariable({variable:s[o-3],props:s[o-2],expression:s[o],method:s[o-4]});break;case 649:this.$="@";break;case 650:this.$="$";break;case 656:this.$=!0;break;case 657:this.$=!1;break;case 658:this.$=new r.CommitTransaction;break;case 659:this.$=new r.RollbackTransaction;break;case 660:this.$=new r.BeginTransaction;break;case 661:this.$=new r.If({expression:s[o-2],thenstat:s[o-1],elsestat:s[o]}),s[o-1].exists&&(this.$.exists=s[o-1].exists),s[o-1].queries&&(this.$.queries=s[o-1].queries);break;case 662:this.$=new r.If({expression:s[o-1],thenstat:s[o]}),s[o].exists&&(this.$.exists=s[o].exists),s[o].queries&&(this.$.queries=s[o].queries);break;case 663:this.$=s[o];break;case 664:this.$=new r.While({expression:s[o-1],loopstat:s[o]}),s[o].exists&&(this.$.exists=s[o].exists),s[o].queries&&(this.$.queries=s[o].queries);break;case 665:this.$=new r.Continue;break;case 666:this.$=new r.Break;break;case 667:this.$=new r.BeginEnd({statements:s[o-1]});break;case 668:this.$=new r.Print({exprs:s[o]});break;case 669:this.$=new r.Print({select:s[o]});break;case 670:this.$=new r.Require({paths:s[o]});break;case 671:this.$=new r.Require({plugins:s[o]});break;case 672:case 673:this.$=s[o].toUpperCase();break;case 674:this.$=new r.Echo({expr:s[o]});break;case 679:this.$=new r.Declare({declares:s[o]});break;case 682:this.$={variable:s[o-1]},r.extend(this.$,s[o]);break;case 683:this.$={variable:s[o-2]},r.extend(this.$,s[o]);break;case 684:this.$={variable:s[o-3],expression:s[o]},r.extend(this.$,s[o-2]);break;case 685:this.$={variable:s[o-4],expression:s[o]},r.extend(this.$,s[o-2]);break;case 686:this.$=new r.TruncateTable({table:s[o]});break;case 687:this.$=new r.Merge,r.extend(this.$,s[o-4]),r.extend(this.$,s[o-3]),r.extend(this.$,s[o-2]),r.extend(this.$,{matches:s[o-1]}),r.extend(this.$,s[o]);break;case 688:case 689:this.$={into:s[o]};break;case 691:this.$={on:s[o]};break;case 696:this.$={matched:!0,action:s[o]};break;case 697:this.$={matched:!0,expr:s[o-2],action:s[o]};break;case 698:this.$={delete:!0};break;case 699:this.$={update:s[o]};break;case 700:case 701:this.$={matched:!1,bytarget:!0,action:s[o]};break;case 702:case 703:this.$={matched:!1,bytarget:!0,expr:s[o-2],action:s[o]};break;case 704:this.$={matched:!1,bysource:!0,action:s[o]};break;case 705:this.$={matched:!1,bysource:!0,expr:s[o-2],action:s[o]};break;case 706:this.$={insert:!0,values:s[o]};break;case 707:this.$={insert:!0,values:s[o],columns:s[o-3]};break;case 708:this.$={insert:!0,defaultvalues:!0};break;case 709:this.$={insert:!0,defaultvalues:!0,columns:s[o-3]};break;case 711:this.$={output:{columns:s[o]}};break;case 712:this.$={output:{columns:s[o-3],intovar:s[o],method:s[o-1]}};break;case 713:this.$={output:{columns:s[o-2],intotable:s[o]}};break;case 714:this.$={output:{columns:s[o-5],intotable:s[o-3],intocolumns:s[o-1]}};break;case 715:this.$=new r.CreateVertex({class:s[o-3],sharp:s[o-2],name:s[o-1]}),r.extend(this.$,s[o]);break;case 718:this.$={sets:s[o]};break;case 719:this.$={content:s[o]};break;case 720:this.$={select:s[o]};break;case 721:this.$=new r.CreateEdge({from:s[o-3],to:s[o-1],name:s[o-5]}),r.extend(this.$,s[o]);break;case 722:this.$=new r.CreateGraph({graph:s[o]});break;case 723:this.$=new r.CreateGraph({from:s[o]});break;case 726:this.$=s[o-2],s[o-1]&&(this.$.json=new r.Json({value:s[o-1]})),s[o]&&(this.$.as=s[o]);break;case 727:this.$={source:s[o-6],target:s[o]},s[o-3]&&(this.$.json=new r.Json({value:s[o-3]})),s[o-2]&&(this.$.as=s[o-2]),r.extend(this.$,s[o-4]);break;case 728:this.$={source:s[o-5],target:s[o]},s[o-2]&&(this.$.json=new r.Json({value:s[o-3]})),s[o-1]&&(this.$.as=s[o-2]);break;case 729:this.$={source:s[o-2],target:s[o]};break;case 733:this.$={vars:s[o],method:s[o-1]};break;case 736:case 737:var b=s[o-1];this.$={prop:s[o-3],sharp:s[o-2],name:"undefined"==typeof b?void 0:b.substr(1,b.length-2),class:s[o]};break;case 738:var E=s[o-1];this.$={sharp:s[o-2],name:"undefined"==typeof E?void 0:E.substr(1,E.length-2),class:s[o]};break;case 739:var g=s[o-1];this.$={name:"undefined"==typeof g?void 0:g.substr(1,g.length-2),class:s[o]};break;case 740:this.$={class:s[o]};break;case 746:this.$=new r.AddRule({left:s[o-2],right:s[o]});break;case 747:this.$=new r.AddRule({right:s[o]});break;case 750:this.$=new r.Term({termid:s[o]});break;case 751:this.$=new r.Term({termid:s[o-3],args:s[o-1]});break;case 754:this.$=new r.CreateTrigger({trigger:s[o-6],when:s[o-5],action:s[o-4],table:s[o-2],statement:s[o]}),s[o].exists&&(this.$.exists=s[o].exists),s[o].queries&&(this.$.queries=s[o].queries);break;case 755:this.$=new r.CreateTrigger({trigger:s[o-5],when:s[o-4],action:s[o-3],table:s[o-1],funcid:s[o]});break;case 756:this.$=new r.CreateTrigger({trigger:s[o-6],when:s[o-4],action:s[o-3],table:s[o-5],statement:s[o]}),s[o].exists&&(this.$.exists=s[o].exists),s[o].queries&&(this.$.queries=s[o].queries);break;case 757:case 758:case 760:this.$="AFTER";break;case 759:this.$="BEFORE";break;case 761:this.$="INSTEADOF";break;case 762:this.$="INSERT";break;case 763:this.$="DELETE";break;case 764:this.$="UPDATE";break;case 765:this.$=new r.DropTrigger({trigger:s[o]});break;case 766:this.$=new r.Reindex({indexid:s[o]});break;case 1040:case 1060:case 1062:case 1064:case 1068:case 1070:case 1072:case 1074:case 1076:case 1078:this.$=[];break;case 1041:case 1055:case 1057:case 1061:case 1063:case 1065:case 1069:case 1071:case 1073:case 1075:case 1077:case 1079:s[o-1].push(s[o]);break;case 1054:case 1056:this.$=[s[o]]}},table:[t([10,599,761],n,{8:1,9:2,12:3,13:4,17:5,18:7,19:8,20:9,21:10,22:11,23:12,24:13,25:14,26:15,27:16,28:17,29:18,30:19,31:20,32:21,33:22,34:23,35:24,36:25,37:26,38:27,39:28,40:29,41:30,42:31,43:32,44:33,45:34,46:35,47:36,48:37,49:38,50:39,51:40,52:41,54:43,55:44,56:45,57:46,58:47,59:48,60:49,61:50,62:51,63:52,64:53,65:54,66:55,67:56,68:57,69:58,70:59,78:74,501:95,183:99,3:100,2:r,4:a,5:s,14:i,53:o,71:u,88:c,123:l,145:h,155:d,188:f,265:p,286:b,331:E,334:g,335:m,343:v,392:S,396:T,397:y,400:N,402:C,404:R,405:O,413:w,414:x,415:D,431:L,433:k,434:$,436:M,437:U,438:_,439:F,440:P,444:q,445:G,448:V,449:B,502:j,504:H,505:J,514:Y}),{1:[3]},{10:[1,105],11:106,599:W,761:X},t(K,[2,8]),t(K,[2,9]),t(Q,[2,12]),t(K,n,{17:5,18:7,19:8,20:9,21:10,22:11,23:12,24:13,25:14,26:15,27:16,28:17,29:18,30:19,31:20,32:21,33:22,34:23,35:24,36:25,37:26,38:27,39:28,40:29,41:30,42:31,43:32,44:33,45:34,46:35,47:36,48:37,49:38,50:39,51:40,52:41,54:43,55:44,56:45,57:46,58:47,59:48,60:49,61:50,62:51,63:52,64:53,65:54,66:55,67:56,68:57,69:58,70:59,78:74,501:95,183:99,3:100,12:109,2:r,4:a,5:s,15:[1,110],53:o,71:u,88:c,123:l,145:h,155:d,188:f,265:p,286:b,331:E,334:g,335:m,343:v,392:S,396:T,397:y,400:N,402:C,404:R,405:O,413:w,414:x,415:D,431:L,433:k,434:$,436:M,437:U,438:_,439:F,440:P,444:q,445:G,448:V,449:B,502:j,504:H,505:J,514:Y}),t(Q,[2,14]),t(Q,[2,15]),t(Q,[2,16]),t(Q,[2,17]),t(Q,[2,18]),t(Q,[2,19]),t(Q,[2,20]),t(Q,[2,21]),t(Q,[2,22]),t(Q,[2,23]),t(Q,[2,24]),t(Q,[2,25]),t(Q,[2,26]),t(Q,[2,27]),t(Q,[2,28]),t(Q,[2,29]),t(Q,[2,30]),t(Q,[2,31]),t(Q,[2,32]),t(Q,[2,33]),t(Q,[2,34]),t(Q,[2,35]),t(Q,[2,36]),t(Q,[2,37]),t(Q,[2,38]),t(Q,[2,39]),t(Q,[2,40]),t(Q,[2,41]),t(Q,[2,42]),t(Q,[2,43]),t(Q,[2,44]),t(Q,[2,45]),t(Q,[2,46]),t(Q,[2,47]),t(Q,[2,48]),t(Q,[2,49]),t(Q,[2,50]),t(Q,[2,51]),t(Q,[2,52]),t(Q,[2,53]),t(Q,[2,54]),t(Q,[2,55]),t(Q,[2,56]),t(Q,[2,57]),t(Q,[2,58]),t(Q,[2,59]),t(Q,[2,60]),t(Q,[2,61]),t(Q,[2,62]),t(Q,[2,63]),t(Q,[2,64]),t(Q,[2,65]),t(Q,[2,66]),{349:[1,111]},{2:r,3:112,4:a,5:s},{2:r,3:114,4:a,5:s,155:z,199:113,286:Z,287:ee,288:te,289:ne},t(re,[2,492],{3:121,344:125,2:r,4:a,5:s,133:ae,134:se,186:[1,123],192:[1,122],353:[1,129],401:[1,120],469:[1,124],506:[1,128]}),{144:ie,446:130,447:131},{182:[1,133]},{401:[1,134]},{2:r,3:136,4:a,5:s,129:[1,142],192:[1,137],349:[1,141],393:138,401:[1,135],406:[1,139],506:[1,140]},{2:r,3:166,4:a,5:s,57:163,76:oe,93:143,110:147,112:151,130:ue,131:ce,136:le,142:he,143:159,144:de,148:fe,151:pe,153:be,155:z,157:165,178:Ee,179:ge,180:me,195:149,199:145,200:153,201:154,253:148,254:144,255:146,256:150,257:152,258:155,259:156,260:157,261:160,262:161,264:ve,265:p,266:Se,267:Te,269:Ae,276:ye,277:Ne,278:Ce,279:Re,280:Oe,281:we,282:Ie,283:xe,284:De,286:Z,287:ee,288:te,289:ne,290:Le,291:ke,292:$e,293:Me,294:Ue,295:_e,298:Fe,299:Pe,308:qe,313:Ge,416:188,417:Ve,421:Be},t(je,He,{336:202,170:[1,203],197:Je}),t(je,He,{336:205,197:Je}),{2:r,3:217,4:a,5:s,76:Ye,131:We,142:he,143:210,144:de,151:pe,155:z,180:me,197:[1,208],198:211,199:213,200:212,201:215,208:207,212:Xe,213:216,286:Z,287:ee,288:te,289:ne,298:Fe,416:188,417:Ve,421:Be,450:206},{2:r,3:219,4:a,5:s},{349:[1,220]},t(Ke,[2,1036],{79:221,105:222,106:[1,223]}),t(Qe,[2,1040],{89:224}),{2:r,3:228,4:a,5:s,189:[1,226],192:[1,229],343:[1,225],349:[1,230],401:[1,227]},{349:[1,231]},{2:r,3:234,4:a,5:s,72:232,74:233},t([302,599,761],n,{12:3,13:4,17:5,18:7,19:8,20:9,21:10,22:11,23:12,24:13,25:14,26:15,27:16,28:17,29:18,30:19,31:20,
32:21,33:22,34:23,35:24,36:25,37:26,38:27,39:28,40:29,41:30,42:31,43:32,44:33,45:34,46:35,47:36,48:37,49:38,50:39,51:40,52:41,54:43,55:44,56:45,57:46,58:47,59:48,60:49,61:50,62:51,63:52,64:53,65:54,66:55,67:56,68:57,69:58,70:59,78:74,501:95,183:99,3:100,9:236,2:r,4:a,5:s,14:i,53:o,71:u,88:c,123:l,145:h,155:d,188:f,265:p,286:b,331:E,334:g,335:m,343:v,392:S,396:T,397:y,400:N,402:C,404:R,405:O,413:w,414:x,415:D,431:L,432:[1,235],433:k,434:$,436:M,437:U,438:_,439:F,440:P,444:q,445:G,448:V,449:B,502:j,504:H,505:J,514:Y}),{432:[1,237]},{432:[1,238]},{2:r,3:240,4:a,5:s,401:[1,239]},{2:r,3:242,4:a,5:s,198:241},t(Q,[2,606],{112:243,131:ce,292:$e}),t(ze,[2,310]),{112:244,131:ce,292:$e},{2:r,3:114,4:a,5:s,112:250,130:ue,131:[1,247],142:he,143:245,144:Ze,151:pe,155:z,180:me,195:249,199:254,200:253,256:251,257:252,264:et,270:246,286:Z,287:ee,288:te,289:ne,290:Le,291:ke,292:$e,293:Me,298:Fe,416:188,417:Ve,421:Be},{2:r,3:166,4:a,5:s,57:163,76:oe,93:256,110:147,112:151,130:ue,131:ce,136:le,142:he,143:159,144:de,148:fe,151:pe,153:be,155:z,157:165,178:Ee,179:ge,180:me,195:149,199:145,200:153,201:154,253:148,254:144,255:146,256:150,257:152,258:155,259:156,260:157,261:160,262:161,264:ve,265:p,266:Se,267:Te,269:Ae,276:ye,277:Ne,278:Ce,279:Re,280:Oe,281:we,282:Ie,283:xe,284:De,286:Z,287:ee,288:te,289:ne,290:Le,291:ke,292:$e,293:Me,294:Ue,295:_e,298:Fe,299:Pe,308:qe,313:Ge,416:188,417:Ve,421:Be},t(Q,[2,665]),t(Q,[2,666]),{2:r,3:166,4:a,5:s,40:258,57:163,76:oe,78:74,88:c,93:259,110:147,112:151,130:ue,131:ce,136:le,142:he,143:159,144:de,148:fe,150:257,151:pe,153:be,155:z,157:165,178:Ee,179:ge,180:me,183:99,188:f,195:149,199:145,200:153,201:154,253:148,254:144,255:146,256:150,257:152,258:155,259:156,260:157,261:160,262:161,264:ve,265:p,266:Se,267:Te,269:Ae,276:ye,277:Ne,278:Ce,279:Re,280:Oe,281:we,282:Ie,283:xe,284:De,286:Z,287:ee,288:te,289:ne,290:Le,291:ke,292:$e,293:Me,294:Ue,295:_e,298:Fe,299:Pe,308:qe,313:Ge,416:188,417:Ve,421:Be},{2:r,3:265,4:a,5:s,112:262,131:ce,292:$e,441:260,442:261,443:263,444:tt},{2:r,3:266,4:a,5:s,142:nt,144:rt,428:267},{2:r,3:166,4:a,5:s,57:163,76:oe,93:270,110:147,112:151,130:ue,131:ce,136:le,142:he,143:159,144:de,148:fe,151:pe,153:be,155:z,157:165,178:Ee,179:ge,180:me,195:149,199:145,200:153,201:154,253:148,254:144,255:146,256:150,257:152,258:155,259:156,260:157,261:160,262:161,264:ve,265:p,266:Se,267:Te,269:Ae,276:ye,277:Ne,278:Ce,279:Re,280:Oe,281:we,282:Ie,283:xe,284:De,286:Z,287:ee,288:te,289:ne,290:Le,291:ke,292:$e,293:Me,294:Ue,295:_e,298:Fe,299:Pe,308:qe,313:Ge,416:188,417:Ve,421:Be},{502:[1,271]},{2:r,3:100,4:a,5:s,501:273,503:272},{2:r,3:114,4:a,5:s,155:z,199:274,286:Z,287:ee,288:te,289:ne},{2:r,3:166,4:a,5:s,57:163,76:oe,93:275,110:147,112:151,130:ue,131:ce,136:le,142:he,143:159,144:de,148:fe,151:pe,153:be,155:z,157:165,178:Ee,179:ge,180:me,195:149,199:145,200:153,201:154,253:148,254:144,255:146,256:150,257:152,258:155,259:156,260:157,261:160,262:161,264:ve,265:p,266:Se,267:Te,269:Ae,276:ye,277:Ne,278:Ce,279:Re,280:Oe,281:we,282:Ie,283:xe,284:De,286:Z,287:ee,288:te,289:ne,290:Le,291:ke,292:$e,293:Me,294:Ue,295:_e,298:Fe,299:Pe,308:qe,313:Ge,416:188,417:Ve,421:Be},t(at,st,{185:279,163:[1,278],184:[1,276],186:[1,277],194:it}),t(ot,[2,750],{76:[1,281]}),t([2,4,5,10,71,76,77,92,97,106,117,127,130,131,136,142,144,151,153,155,161,163,167,168,178,179,180,182,184,186,194,197,231,244,246,264,265,266,267,269,276,277,278,279,280,281,282,283,284,286,287,288,289,290,291,292,293,294,295,298,299,302,306,308,313,417,421,599,761],[2,151],{148:[1,282],149:[1,283],189:[1,284],190:[1,285],191:[1,286],192:[1,287],193:[1,288]}),t(ut,[2,1]),t(ut,[2,2]),{6:289,130:[1,438],171:[1,461],244:[1,410],281:[1,372],282:[1,406],366:[1,403],377:[1,294],398:[1,296],406:[1,548],410:[1,470],412:[1,442],414:[1,508],430:[1,441],432:[1,524],437:[1,341],457:[1,417],461:[1,447],467:[1,340],511:[1,306],512:[1,298],513:[1,398],515:[1,290],516:[1,291],517:[1,292],518:[1,293],519:[1,295],520:[1,297],521:[1,299],522:[1,300],523:[1,301],524:[1,302],525:[1,303],526:[1,304],527:[1,305],528:[1,307],529:[1,308],530:[1,309],531:[1,310],532:[1,311],533:[1,312],534:[1,313],535:[1,314],536:[1,315],537:[1,316],538:[1,317],539:[1,318],540:[1,319],541:[1,320],542:[1,321],543:[1,322],544:[1,323],545:[1,324],546:[1,325],547:[1,326],548:[1,327],549:[1,328],550:[1,329],551:[1,330],552:[1,331],553:[1,332],554:[1,333],555:[1,334],556:[1,335],557:[1,336],558:[1,337],559:[1,338],560:[1,339],561:[1,342],562:[1,343],563:[1,344],564:[1,345],565:[1,346],566:[1,347],567:[1,348],568:[1,349],569:[1,350],570:[1,351],571:[1,352],572:[1,353],573:[1,354],574:[1,355],575:[1,356],576:[1,357],577:[1,358],578:[1,359],579:[1,360],580:[1,361],581:[1,362],582:[1,363],583:[1,364],584:[1,365],585:[1,366],586:[1,367],587:[1,368],588:[1,369],589:[1,370],590:[1,371],591:[1,373],592:[1,374],593:[1,375],594:[1,376],595:[1,377],596:[1,378],597:[1,379],598:[1,380],599:[1,381],600:[1,382],601:[1,383],602:[1,384],603:[1,385],604:[1,386],605:[1,387],606:[1,388],607:[1,389],608:[1,390],609:[1,391],610:[1,392],611:[1,393],612:[1,394],613:[1,395],614:[1,396],615:[1,397],616:[1,399],617:[1,400],618:[1,401],619:[1,402],620:[1,404],621:[1,405],622:[1,407],623:[1,408],624:[1,409],625:[1,411],626:[1,412],627:[1,413],628:[1,414],629:[1,415],630:[1,416],631:[1,418],632:[1,419],633:[1,420],634:[1,421],635:[1,422],636:[1,423],637:[1,424],638:[1,425],639:[1,426],640:[1,427],641:[1,428],642:[1,429],643:[1,430],644:[1,431],645:[1,432],646:[1,433],647:[1,434],648:[1,435],649:[1,436],650:[1,437],651:[1,439],652:[1,440],653:[1,443],654:[1,444],655:[1,445],656:[1,446],657:[1,448],658:[1,449],659:[1,450],660:[1,451],661:[1,452],662:[1,453],663:[1,454],664:[1,455],665:[1,456],666:[1,457],667:[1,458],668:[1,459],669:[1,460],670:[1,462],671:[1,463],672:[1,464],673:[1,465],674:[1,466],675:[1,467],676:[1,468],677:[1,469],678:[1,471],679:[1,472],680:[1,473],681:[1,474],682:[1,475],683:[1,476],684:[1,477],685:[1,478],686:[1,479],687:[1,480],688:[1,481],689:[1,482],690:[1,483],691:[1,484],692:[1,485],693:[1,486],694:[1,487],695:[1,488],696:[1,489],697:[1,490],698:[1,491],699:[1,492],700:[1,493],701:[1,494],702:[1,495],703:[1,496],704:[1,497],705:[1,498],706:[1,499],707:[1,500],708:[1,501],709:[1,502],710:[1,503],711:[1,504],712:[1,505],713:[1,506],714:[1,507],715:[1,509],716:[1,510],717:[1,511],718:[1,512],719:[1,513],720:[1,514],721:[1,515],722:[1,516],723:[1,517],724:[1,518],725:[1,519],726:[1,520],727:[1,521],728:[1,522],729:[1,523],730:[1,525],731:[1,526],732:[1,527],733:[1,528],734:[1,529],735:[1,530],736:[1,531],737:[1,532],738:[1,533],739:[1,534],740:[1,535],741:[1,536],742:[1,537],743:[1,538],744:[1,539],745:[1,540],746:[1,541],747:[1,542],748:[1,543],749:[1,544],750:[1,545],751:[1,546],752:[1,547],753:[1,549],754:[1,550],755:[1,551],756:[1,552],757:[1,553],758:[1,554],759:[1,555],760:[1,556]},{1:[2,6]},t(K,n,{17:5,18:7,19:8,20:9,21:10,22:11,23:12,24:13,25:14,26:15,27:16,28:17,29:18,30:19,31:20,32:21,33:22,34:23,35:24,36:25,37:26,38:27,39:28,40:29,41:30,42:31,43:32,44:33,45:34,46:35,47:36,48:37,49:38,50:39,51:40,52:41,54:43,55:44,56:45,57:46,58:47,59:48,60:49,61:50,62:51,63:52,64:53,65:54,66:55,67:56,68:57,69:58,70:59,78:74,501:95,183:99,3:100,12:557,2:r,4:a,5:s,53:o,71:u,88:c,123:l,145:h,155:d,188:f,265:p,286:b,331:E,334:g,335:m,343:v,392:S,396:T,397:y,400:N,402:C,404:R,405:O,413:w,414:x,415:D,431:L,433:k,434:$,436:M,437:U,438:_,439:F,440:P,444:q,445:G,448:V,449:B,502:j,504:H,505:J,514:Y}),t(ct,[2,1034]),t(ct,[2,1035]),t(K,[2,10]),{16:[1,558]},{2:r,3:242,4:a,5:s,198:559},{401:[1,560]},t(Q,[2,753]),{76:lt},{76:[1,562]},{76:ht},{76:[1,564]},{76:[1,565]},{2:r,3:166,4:a,5:s,57:163,76:oe,93:566,110:147,112:151,130:ue,131:ce,136:le,142:he,143:159,144:de,148:fe,151:pe,153:be,155:z,157:165,178:Ee,179:ge,180:me,195:149,199:145,200:153,201:154,253:148,254:144,255:146,256:150,257:152,258:155,259:156,260:157,261:160,262:161,264:ve,265:p,266:Se,267:Te,269:Ae,276:ye,277:Ne,278:Ce,279:Re,280:Oe,281:we,282:Ie,283:xe,284:De,286:Z,287:ee,288:te,289:ne,290:Le,291:ke,292:$e,293:Me,294:Ue,295:_e,298:Fe,299:Pe,308:qe,313:Ge,416:188,417:Ve,421:Be},t(je,dt,{346:567,155:ft}),{401:[1,569]},{2:r,3:570,4:a,5:s},{192:[1,571]},{2:r,3:577,4:a,5:s,131:pt,136:bt,142:nt,144:rt,151:Et,182:[1,573],428:584,470:572,471:574,472:575,475:576,479:581,490:578,494:580},{129:[1,588],345:585,349:[1,587],406:[1,586]},{112:590,131:ce,182:[2,1134],292:$e,468:589},t(gt,[2,1128],{462:591,3:592,2:r,4:a,5:s}),{2:r,3:593,4:a,5:s},t(re,[2,493]),t(Q,[2,679],{73:[1,594]}),t(mt,[2,680]),{2:r,3:595,4:a,5:s},{2:r,3:242,4:a,5:s,198:596},{2:r,3:597,4:a,5:s},t(je,vt,{394:598,155:St}),{401:[1,600]},{2:r,3:601,4:a,5:s},t(je,vt,{394:602,155:St}),t(je,vt,{394:603,155:St}),{2:r,3:604,4:a,5:s},t(Tt,[2,1122]),t(Tt,[2,1123]),t(Q,n,{17:5,18:7,19:8,20:9,21:10,22:11,23:12,24:13,25:14,26:15,27:16,28:17,29:18,30:19,31:20,32:21,33:22,34:23,35:24,36:25,37:26,38:27,39:28,40:29,41:30,42:31,43:32,44:33,45:34,46:35,47:36,48:37,49:38,50:39,51:40,52:41,54:43,55:44,56:45,57:46,58:47,59:48,60:49,61:50,62:51,63:52,64:53,65:54,66:55,67:56,68:57,69:58,70:59,78:74,501:95,183:99,3:100,12:605,113:622,323:634,2:r,4:a,5:s,53:o,71:u,88:c,98:At,111:yt,114:Nt,115:Ct,122:Rt,123:Ot,132:wt,135:It,137:xt,138:Dt,139:Lt,140:kt,141:$t,145:h,153:Mt,155:d,169:Ut,170:_t,178:Ft,179:Pt,188:f,265:p,286:b,307:qt,308:Gt,309:Vt,311:Bt,312:jt,313:Ht,314:Jt,315:Yt,316:Wt,317:Xt,318:Kt,319:Qt,320:zt,321:Zt,322:en,326:tn,327:nn,328:rn,329:an,331:E,334:g,335:m,343:v,392:S,396:T,397:y,400:N,402:C,404:R,405:O,413:w,414:x,415:D,431:L,433:k,434:$,436:M,437:U,438:_,439:F,440:P,444:q,445:G,448:V,449:B,502:j,504:H,505:J,514:Y}),t(ze,[2,287]),t(ze,[2,288]),t(ze,[2,289]),t(ze,[2,290]),t(ze,[2,291]),t(ze,[2,292]),t(ze,[2,293]),t(ze,[2,294]),t(ze,[2,295]),t(ze,[2,296]),t(ze,[2,297]),t(ze,[2,298]),t(ze,[2,299]),t(ze,[2,300]),t(ze,[2,301]),t(ze,[2,302]),t(ze,[2,303]),t(ze,[2,304]),{2:r,3:166,4:a,5:s,26:651,27:650,36:646,40:645,57:163,76:oe,78:74,88:c,93:648,110:147,112:151,130:ue,131:ce,136:le,142:he,143:159,144:de,148:fe,151:pe,153:be,155:z,157:165,178:Ee,179:ge,180:me,183:99,188:f,195:149,199:145,200:153,201:154,253:148,254:144,255:146,256:150,257:152,258:155,259:156,260:157,261:160,262:161,263:647,264:ve,265:p,266:Se,267:Te,269:Ae,276:ye,277:Ne,278:Ce,279:Re,280:Oe,281:we,282:Ie,283:xe,284:De,286:[1,649],287:ee,288:te,289:ne,290:Le,291:ke,292:$e,293:Me,294:Ue,295:_e,298:Fe,299:Pe,308:qe,313:Ge,335:m,343:[1,652],416:188,417:Ve,421:Be},t(ze,[2,308]),t(ze,[2,309]),{76:[1,653]},t([2,4,5,10,53,71,73,75,77,88,92,94,97,98,106,111,114,117,121,122,123,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,144,145,147,148,149,151,153,155,161,163,165,167,168,169,170,171,172,174,178,179,180,182,184,186,188,197,205,207,221,222,223,224,225,226,227,228,231,238,241,242,244,246,265,276,277,278,279,280,281,282,283,284,286,292,296,302,304,305,306,307,308,309,310,311,312,313,314,315,316,317,318,319,320,321,322,326,327,328,329,331,334,335,343,392,396,397,400,402,404,405,413,414,415,417,421,431,433,434,436,437,438,439,440,444,445,448,449,461,467,502,504,505,514,599,761],sn,{76:lt,115:[1,654]}),{2:r,3:166,4:a,5:s,57:163,76:oe,93:655,110:147,112:151,130:ue,131:ce,136:le,142:he,143:159,144:de,148:fe,151:pe,153:be,155:z,157:165,178:Ee,179:ge,180:me,195:149,199:145,200:153,201:154,253:148,254:144,255:146,256:150,257:152,258:155,259:156,260:157,261:160,262:161,264:ve,265:p,266:Se,267:Te,269:Ae,276:ye,277:Ne,278:Ce,279:Re,280:Oe,281:we,282:Ie,283:xe,284:De,286:Z,287:ee,288:te,289:ne,290:Le,291:ke,292:$e,293:Me,294:Ue,295:_e,298:Fe,299:Pe,308:qe,313:Ge,416:188,417:Ve,421:Be},{2:r,3:166,4:a,5:s,57:163,76:oe,93:656,110:147,112:151,130:ue,131:ce,136:le,142:he,143:159,144:de,148:fe,151:pe,153:be,155:z,157:165,178:Ee,179:ge,180:me,195:149,199:145,200:153,201:154,253:148,254:144,255:146,256:150,257:152,258:155,259:156,260:157,261:160,262:161,264:ve,265:p,266:Se,267:Te,269:Ae,276:ye,277:Ne,278:Ce,279:Re,280:Oe,281:we,282:Ie,283:xe,284:De,286:Z,287:ee,288:te,289:ne,290:Le,291:ke,292:$e,293:Me,294:Ue,295:_e,298:Fe,299:Pe,308:qe,313:Ge,416:188,417:Ve,421:Be},{2:r,3:166,4:a,5:s,57:163,76:oe,93:657,110:147,112:151,130:ue,131:ce,136:le,142:he,143:159,144:de,148:fe,151:pe,153:be,155:z,157:165,178:Ee,179:ge,180:me,195:149,199:145,200:153,201:154,253:148,254:144,255:146,256:150,257:152,258:155,259:156,260:157,261:160,262:161,264:ve,265:p,266:Se,267:Te,269:Ae,276:ye,277:Ne,278:Ce,279:Re,280:Oe,281:we,282:Ie,283:xe,284:De,286:Z,287:ee,288:te,289:ne,290:Le,291:ke,292:$e,293:Me,294:Ue,295:_e,298:Fe,299:Pe,308:qe,313:Ge,416:188,417:Ve,421:Be},{2:r,3:166,4:a,5:s,57:163,76:oe,93:658,110:147,112:151,130:ue,131:ce,136:le,142:he,143:159,144:de,148:fe,151:pe,153:be,155:z,157:165,178:Ee,179:ge,180:me,195:149,199:145,200:153,201:154,253:148,254:144,255:146,256:150,257:152,258:155,259:156,260:157,261:160,262:161,264:ve,265:p,266:Se,267:Te,269:Ae,276:ye,277:Ne,278:Ce,279:Re,280:Oe,281:we,282:Ie,283:xe,284:De,286:Z,287:ee,288:te,289:ne,290:Le,291:ke,292:$e,293:Me,294:Ue,295:_e,298:Fe,299:Pe,308:qe,313:Ge,416:188,417:Ve,421:Be},{2:r,3:166,4:a,5:s,57:163,76:oe,93:659,110:147,112:151,130:ue,131:ce,136:le,142:he,143:159,144:de,148:fe,151:pe,153:be,155:z,157:165,178:Ee,179:ge,180:me,195:149,199:145,200:153,201:154,253:148,254:144,255:146,256:150,257:152,258:155,259:156,260:157,261:160,262:161,264:ve,265:p,266:Se,267:Te,269:Ae,276:ye,277:Ne,278:Ce,279:Re,280:Oe,281:we,282:Ie,283:xe,284:De,286:Z,287:ee,288:te,289:ne,290:Le,291:ke,292:$e,293:Me,294:Ue,295:_e,298:Fe,299:Pe,308:qe,313:Ge,416:188,417:Ve,421:Be},t(ze,[2,282]),t([2,4,5,10,53,71,73,75,76,77,88,92,94,97,98,106,111,114,115,117,121,122,123,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,144,145,147,148,149,151,153,155,161,163,165,167,168,169,170,171,172,174,178,179,180,182,184,186,188,197,205,207,221,222,223,224,225,226,227,228,229,230,231,238,241,242,244,246,248,264,265,266,267,269,276,277,278,279,280,281,282,283,284,286,287,288,289,290,291,292,293,294,295,296,298,299,302,304,305,306,307,308,309,310,311,312,313,314,315,316,317,318,319,320,321,322,326,327,328,329,331,334,335,339,343,352,364,365,369,370,392,396,397,400,402,404,405,411,413,414,415,417,421,423,431,433,434,436,437,438,439,440,444,445,448,449,461,467,502,504,505,514,599,761,762,763],[2,353]),t(on,[2,354]),t(on,[2,355]),t(on,un),t(on,[2,357]),t([2,4,5,10,53,71,73,75,76,77,88,92,94,97,98,106,111,114,115,117,121,122,123,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,144,145,147,148,149,151,153,155,161,163,165,167,168,169,170,171,172,174,178,179,180,182,184,186,188,197,205,207,221,222,223,224,225,226,227,228,229,231,238,241,242,244,246,265,276,277,278,279,280,281,282,283,284,286,292,293,296,302,304,305,306,307,308,309,310,311,312,313,314,315,316,317,318,319,320,321,322,326,327,328,329,331,334,335,339,343,352,364,365,369,370,392,396,397,400,402,404,405,413,414,415,417,421,423,431,433,434,436,437,438,439,440,444,445,448,449,461,467,502,504,505,514,599,761],[2,358]),{2:r,3:661,4:a,5:s,130:[1,662],297:660},{2:r,3:663,4:a,5:s},t(on,[2,364]),t(on,[2,365]),{2:r,3:664,4:a,5:s,76:cn,112:666,130:ue,131:ce,142:he,151:pe,180:me,195:667,200:669,256:668,290:Le,291:ke,292:$e,298:Fe,416:670,421:Be},{76:[1,671]},{2:r,3:166,4:a,5:s,57:163,76:oe,93:672,110:147,112:151,130:ue,131:ce,136:le,142:he,143:159,144:de,148:fe,151:pe,153:be,155:z,157:165,178:Ee,179:ge,180:me,195:149,199:145,200:153,201:154,253:148,254:144,255:146,256:150,257:152,258:155,259:156,260:157,261:160,262:161,264:ve,265:p,266:Se,267:Te,269:Ae,276:ye,277:Ne,278:Ce,279:Re,280:Oe,281:we,282:Ie,283:xe,284:De,286:Z,287:ee,288:te,289:ne,290:Le,291:ke,292:$e,293:Me,294:Ue,295:_e,298:Fe,299:Pe,300:673,303:674,304:ln,308:qe,313:Ge,416:188,417:Ve,421:Be},{76:[1,676]},{76:[1,677]},t(hn,[2,617]),{2:r,3:692,4:a,5:s,76:dn,110:687,112:685,130:ue,131:ce,142:he,143:682,144:Ze,151:pe,155:z,180:me,195:684,199:690,200:689,256:686,257:688,286:Z,287:ee,288:te,289:ne,290:Le,291:ke,292:$e,293:Me,296:[1,680],298:Fe,416:188,417:Ve,418:678,419:681,420:683,421:Be,424:679},{2:r,3:166,4:a,5:s,57:163,76:oe,93:259,110:147,112:151,130:ue,131:ce,136:le,142:he,143:159,144:de,148:fe,150:693,151:pe,153:be,155:z,157:165,178:Ee,179:ge,180:me,195:149,199:145,200:153,201:154,253:148,254:144,255:146,256:150,257:152,258:155,259:156,260:157,261:160,262:161,264:ve,265:p,266:Se,267:Te,269:Ae,276:ye,277:Ne,278:Ce,279:Re,280:Oe,281:we,282:Ie,283:xe,284:De,286:Z,287:ee,288:te,289:ne,290:Le,291:ke,292:$e,293:Me,294:Ue,295:_e,298:Fe,299:Pe,308:qe,313:Ge,416:188,417:Ve,421:Be},{2:r,3:694,4:a,5:s,155:z,199:695,286:Z,287:ee,288:te,289:ne},{76:[2,333]},{76:[2,334]},{76:[2,335]},{76:[2,336]},{76:[2,337]},{76:[2,338]},{76:[2,339]},{76:[2,340]},{76:[2,341]},{2:r,3:701,4:a,5:s,130:fn,131:pn,422:696,423:[1,697],425:698},{2:r,3:242,4:a,5:s,198:702},{286:[1,703]},t(je,[2,463]),{2:r,3:242,4:a,5:s,198:704},{230:[1,706],451:705},{230:[2,688]},{2:r,3:217,4:a,5:s,76:Ye,131:We,142:he,143:210,144:de,151:pe,155:z,180:me,198:211,199:213,200:212,201:215,208:707,212:Xe,213:216,286:Z,287:ee,288:te,289:ne,298:Fe,416:188,417:Ve,421:Be},{40:708,78:74,88:c,183:99,188:f},t(bn,[2,1084],{209:709,75:[1,710]}),t(En,[2,184],{3:711,2:r,4:a,5:s,75:[1,712],153:[1,713]}),t(En,[2,188],{3:714,2:r,4:a,5:s,75:[1,715]}),t(En,[2,189],{3:716,2:r,4:a,5:s,75:[1,717]}),t(En,[2,192]),t(En,[2,193],{3:718,2:r,4:a,5:s,75:[1,719]}),t(En,[2,196],{3:720,2:r,4:a,5:s,75:[1,721]}),t([2,4,5,10,71,73,75,77,92,97,117,127,153,161,167,168,182,205,207,221,222,223,224,225,226,227,228,229,230,231,244,246,302,306,599,761],gn,{76:lt,115:mn}),t([2,4,5,10,71,73,75,77,92,97,117,127,161,167,168,205,207,221,222,223,224,225,226,227,228,229,230,231,244,246,302,306,599,761],[2,199]),t(Q,[2,766]),{2:r,3:242,4:a,5:s,198:723},t(vn,Sn,{80:724,197:Tn}),t(Ke,[2,1037]),t(An,[2,1050],{107:726,189:[1,727]}),t([10,77,182,302,306,599,761],Sn,{416:188,80:728,116:729,3:730,113:733,143:755,157:765,159:766,2:r,4:a,5:s,71:yn,75:Nn,76:Cn,111:Rn,114:Nt,115:Ct,117:On,121:wn,122:In,123:xn,127:Dn,128:Ln,129:kn,130:$n,131:Mn,132:Un,133:_n,134:Fn,135:Pn,136:qn,137:Gn,138:Vn,139:Bn,140:jn,141:Hn,142:Jn,144:Yn,145:Wn,147:Xn,148:Kn,149:Qn,151:zn,153:Zn,155:er,161:tr,163:nr,165:rr,167:ar,168:sr,169:ir,170:or,171:ur,172:cr,174:lr,184:hr,186:dr,197:Tn,276:ye,277:Ne,278:Ce,279:Re,280:Oe,281:we,282:Ie,283:xe,284:De,417:Ve,421:Be}),{349:[1,779]},{182:[1,780]},t(Q,[2,585],{111:[1,781]}),{401:[1,782]},{182:[1,783]},t(Q,[2,589],{111:[1,784],182:[1,785]}),{2:r,3:242,4:a,5:s,198:786},{40:787,73:[1,788],78:74,88:c,183:99,188:f},t(fr,[2,69]),{75:[1,789]},t(Q,[2,660]),{11:106,302:[1,790],599:W,761:X},t(Q,[2,658]),t(Q,[2,659]),{2:r,3:791,4:a,5:s},t(Q,[2,578]),{145:[1,792]},t([2,4,5,10,53,71,73,75,76,77,88,94,123,127,145,147,153,155,182,186,188,229,265,286,293,302,306,331,334,335,337,339,343,352,364,365,369,370,392,396,397,398,399,400,402,404,405,413,414,415,431,433,434,436,437,438,439,440,444,445,448,449,502,504,505,511,512,513,514,599,761],gn,{115:mn}),t(Q,[2,605]),t(Q,[2,608]),t(Q,[2,609]),t(Q,[2,610]),t(Q,un,{73:[1,793]}),{76:cn,112:666,130:ue,131:ce,142:he,151:pe,180:me,195:667,200:669,256:668,290:Le,291:ke,292:$e,298:Fe,416:670,421:Be},t(pr,[2,317]),t(pr,[2,318]),t(pr,[2,319]),t(pr,[2,320]),t(pr,[2,321]),t(pr,[2,322]),t(pr,[2,323]),t(Q,n,{17:5,18:7,19:8,20:9,21:10,22:11,23:12,24:13,25:14,26:15,27:16,28:17,29:18,30:19,31:20,32:21,33:22,34:23,35:24,36:25,37:26,38:27,39:28,40:29,41:30,42:31,43:32,44:33,45:34,46:35,47:36,48:37,49:38,50:39,51:40,52:41,54:43,55:44,56:45,57:46,58:47,59:48,60:49,61:50,62:51,63:52,64:53,65:54,66:55,67:56,68:57,69:58,70:59,78:74,501:95,183:99,3:100,113:622,323:634,12:794,2:r,4:a,5:s,53:o,71:u,88:c,98:At,111:yt,114:Nt,115:Ct,122:Rt,123:Ot,132:wt,135:It,137:xt,138:Dt,139:Lt,140:kt,141:$t,145:h,153:Mt,155:d,169:Ut,170:_t,178:Ft,179:Pt,188:f,265:p,286:b,307:qt,308:Gt,309:Vt,311:Bt,312:jt,313:Ht,314:Jt,315:Yt,316:Wt,317:Xt,318:Kt,319:Qt,320:zt,321:Zt,322:en,326:tn,327:nn,328:rn,329:an,331:E,334:g,335:m,343:v,392:S,396:T,397:y,400:N,402:C,404:R,405:O,413:w,414:x,415:D,431:L,433:k,434:$,436:M,437:U,438:_,439:F,440:P,444:q,445:G,448:V,449:B,502:j,504:H,505:J,514:Y}),t(Q,[2,668],{73:br}),t(Q,[2,669]),t(Er,[2,351],{113:622,323:634,98:At,111:yt,114:Nt,115:Ct,122:Rt,123:gr,132:wt,135:It,137:xt,138:Dt,139:Lt,140:kt,141:$t,153:Mt,169:Ut,170:_t,178:Ft,179:Pt,307:qt,308:Gt,309:Vt,311:Bt,312:jt,313:Ht,314:Jt,315:Yt,316:Wt,317:Xt,318:Kt,319:Qt,320:zt,321:Zt,322:en,326:tn,327:nn,328:rn,329:an}),t(Q,[2,670],{73:[1,797]}),t(Q,[2,671],{73:[1,798]}),t(mt,[2,676]),t(mt,[2,678]),t(mt,[2,672]),t(mt,[2,673]),{113:804,114:Nt,115:Ct,123:[1,799],229:mr,426:800,427:801,430:vr},{2:r,3:805,4:a,5:s},t(je,[2,649]),t(je,[2,650]),t(Q,[2,607],{113:622,323:634,98:At,111:yt,114:Nt,115:Ct,122:Rt,123:gr,132:wt,135:It,137:xt,138:Dt,139:Lt,140:kt,141:$t,153:Mt,169:Ut,170:_t,178:Ft,179:Pt,307:qt,308:Gt,309:Vt,311:Bt,312:jt,313:Ht,314:Jt,315:Yt,316:Wt,317:Xt,318:Kt,319:Qt,320:zt,321:Zt,322:en,326:tn,327:nn,328:rn,329:an}),{2:r,3:100,4:a,5:s,501:273,503:806},t(Q,[2,747],{73:Sr}),t(Tr,[2,749]),t(Q,[2,752]),t(Q,[2,674],{113:622,323:634,98:At,111:yt,114:Nt,115:Ct,122:Rt,123:gr,132:wt,135:It,137:xt,138:Dt,139:Lt,140:kt,141:$t,153:Mt,169:Ut,170:_t,178:Ft,179:Pt,307:qt,308:Gt,309:Vt,311:Bt,312:jt,313:Ht,314:Jt,315:Yt,316:Wt,317:Xt,318:Kt,319:Qt,320:zt,321:Zt,322:en,326:tn,327:nn,328:rn,329:an}),t(Ar,st,{185:808,194:it}),t(Ar,st,{185:809,194:it}),t(Ar,st,{185:810,194:it}),t(yr,[2,1080],{254:144,199:145,255:146,110:147,253:148,195:149,256:150,112:151,257:152,200:153,201:154,258:155,259:156,260:157,143:159,261:160,262:161,57:163,157:165,3:166,416:188,187:811,173:812,252:813,93:814,2:r,4:a,5:s,76:oe,130:ue,131:ce,136:le,142:he,144:de,148:fe,151:pe,153:be,155:z,178:Ee,179:ge,180:me,264:ve,265:p,266:Se,267:Te,269:Ae,276:ye,277:Ne,278:Ce,279:Re,280:Oe,281:we,282:Ie,283:xe,284:De,286:Z,287:ee,288:te,289:ne,290:Le,291:ke,292:$e,293:Me,294:Ue,295:_e,298:Fe,299:Pe,308:qe,313:Ge,417:Ve,421:Be}),{76:[1,816],130:ue,195:815},{2:r,3:100,4:a,5:s,501:273,503:817},t(Nr,[2,152]),t(Nr,[2,153]),t(Nr,[2,154]),t(Nr,[2,155]),t(Nr,[2,156]),t(Nr,[2,157]),t(Nr,[2,158]),t(ut,[2,3]),t(ut,[2,767]),t(ut,[2,768]),t(ut,[2,769]),t(ut,[2,770]),t(ut,[2,771]),t(ut,[2,772]),t(ut,[2,773]),t(ut,[2,774]),t(ut,[2,775]),t(ut,[2,776]),t(ut,[2,777]),t(ut,[2,778]),t(ut,[2,779]),t(ut,[2,780]),t(ut,[2,781]),t(ut,[2,782]),t(ut,[2,783]),t(ut,[2,784]),t(ut,[2,785]),t(ut,[2,786]),t(ut,[2,787]),t(ut,[2,788]),t(ut,[2,789]),t(ut,[2,790]),t(ut,[2,791]),t(ut,[2,792]),t(ut,[2,793]),t(ut,[2,794]),t(ut,[2,795]),t(ut,[2,796]),t(ut,[2,797]),t(ut,[2,798]),t(ut,[2,799]),t(ut,[2,800]),t(ut,[2,801]),t(ut,[2,802]),t(ut,[2,803]),t(ut,[2,804]),t(ut,[2,805]),t(ut,[2,806]),t(ut,[2,807]),t(ut,[2,808]),t(ut,[2,809]),t(ut,[2,810]),t(ut,[2,811]),t(ut,[2,812]),t(ut,[2,813]),t(ut,[2,814]),t(ut,[2,815]),t(ut,[2,816]),t(ut,[2,817]),t(ut,[2,818]),t(ut,[2,819]),t(ut,[2,820]),t(ut,[2,821]),t(ut,[2,822]),t(ut,[2,823]),t(ut,[2,824]),t(ut,[2,825]),t(ut,[2,826]),t(ut,[2,827]),t(ut,[2,828]),t(ut,[2,829]),t(ut,[2,830]),t(ut,[2,831]),t(ut,[2,832]),t(ut,[2,833]),t(ut,[2,834]),t(ut,[2,835]),t(ut,[2,836]),t(ut,[2,837]),t(ut,[2,838]),t(ut,[2,839]),t(ut,[2,840]),t(ut,[2,841]),t(ut,[2,842]),t(ut,[2,843]),t(ut,[2,844]),t(ut,[2,845]),t(ut,[2,846]),t(ut,[2,847]),t(ut,[2,848]),t(ut,[2,849]),t(ut,[2,850]),t(ut,[2,851]),t(ut,[2,852]),t(ut,[2,853]),t(ut,[2,854]),t(ut,[2,855]),t(ut,[2,856]),t(ut,[2,857]),t(ut,[2,858]),t(ut,[2,859]),t(ut,[2,860]),t(ut,[2,861]),t(ut,[2,862]),t(ut,[2,863]),t(ut,[2,864]),t(ut,[2,865]),t(ut,[2,866]),t(ut,[2,867]),t(ut,[2,868]),t(ut,[2,869]),t(ut,[2,870]),t(ut,[2,871]),t(ut,[2,872]),t(ut,[2,873]),t(ut,[2,874]),t(ut,[2,875]),t(ut,[2,876]),t(ut,[2,877]),t(ut,[2,878]),t(ut,[2,879]),t(ut,[2,880]),t(ut,[2,881]),t(ut,[2,882]),t(ut,[2,883]),t(ut,[2,884]),t(ut,[2,885]),t(ut,[2,886]),t(ut,[2,887]),t(ut,[2,888]),t(ut,[2,889]),t(ut,[2,890]),t(ut,[2,891]),t(ut,[2,892]),t(ut,[2,893]),t(ut,[2,894]),t(ut,[2,895]),t(ut,[2,896]),t(ut,[2,897]),t(ut,[2,898]),t(ut,[2,899]),t(ut,[2,900]),t(ut,[2,901]),t(ut,[2,902]),t(ut,[2,903]),t(ut,[2,904]),t(ut,[2,905]),t(ut,[2,906]),t(ut,[2,907]),t(ut,[2,908]),t(ut,[2,909]),t(ut,[2,910]),t(ut,[2,911]),t(ut,[2,912]),t(ut,[2,913]),t(ut,[2,914]),t(ut,[2,915]),t(ut,[2,916]),t(ut,[2,917]),t(ut,[2,918]),t(ut,[2,919]),t(ut,[2,920]),t(ut,[2,921]),t(ut,[2,922]),t(ut,[2,923]),t(ut,[2,924]),t(ut,[2,925]),t(ut,[2,926]),t(ut,[2,927]),t(ut,[2,928]),t(ut,[2,929]),t(ut,[2,930]),t(ut,[2,931]),t(ut,[2,932]),t(ut,[2,933]),t(ut,[2,934]),t(ut,[2,935]),t(ut,[2,936]),t(ut,[2,937]),t(ut,[2,938]),t(ut,[2,939]),t(ut,[2,940]),t(ut,[2,941]),t(ut,[2,942]),t(ut,[2,943]),t(ut,[2,944]),t(ut,[2,945]),t(ut,[2,946]),t(ut,[2,947]),t(ut,[2,948]),t(ut,[2,949]),t(ut,[2,950]),t(ut,[2,951]),t(ut,[2,952]),t(ut,[2,953]),t(ut,[2,954]),t(ut,[2,955]),t(ut,[2,956]),t(ut,[2,957]),t(ut,[2,958]),t(ut,[2,959]),t(ut,[2,960]),t(ut,[2,961]),t(ut,[2,962]),t(ut,[2,963]),t(ut,[2,964]),t(ut,[2,965]),t(ut,[2,966]),t(ut,[2,967]),t(ut,[2,968]),t(ut,[2,969]),t(ut,[2,970]),t(ut,[2,971]),t(ut,[2,972]),t(ut,[2,973]),t(ut,[2,974]),t(ut,[2,975]),t(ut,[2,976]),t(ut,[2,977]),t(ut,[2,978]),t(ut,[2,979]),t(ut,[2,980]),t(ut,[2,981]),t(ut,[2,982]),t(ut,[2,983]),t(ut,[2,984]),t(ut,[2,985]),t(ut,[2,986]),t(ut,[2,987]),t(ut,[2,988]),t(ut,[2,989]),t(ut,[2,990]),t(ut,[2,991]),t(ut,[2,992]),t(ut,[2,993]),t(ut,[2,994]),t(ut,[2,995]),t(ut,[2,996]),t(ut,[2,997]),t(ut,[2,998]),t(ut,[2,999]),t(ut,[2,1e3]),t(ut,[2,1001]),t(ut,[2,1002]),t(ut,[2,1003]),t(ut,[2,1004]),t(ut,[2,1005]),t(ut,[2,1006]),t(ut,[2,1007]),t(ut,[2,1008]),t(ut,[2,1009]),t(ut,[2,1010]),t(ut,[2,1011]),t(ut,[2,1012]),t(ut,[2,1013]),t(ut,[2,1014]),t(ut,[2,1015]),t(ut,[2,1016]),t(ut,[2,1017]),t(ut,[2,1018]),t(ut,[2,1019]),t(ut,[2,1020]),t(ut,[2,1021]),t(ut,[2,1022]),t(ut,[2,1023]),t(ut,[2,1024]),t(ut,[2,1025]),t(ut,[2,1026]),t(ut,[2,1027]),t(ut,[2,1028]),t(ut,[2,1029]),t(ut,[2,1030]),t(ut,[2,1031]),t(ut,[2,1032]),t(ut,[2,1033]),t(K,[2,7]),t(K,n,{17:5,18:7,19:8,20:9,21:10,22:11,23:12,24:13,25:14,26:15,27:16,28:17,29:18,30:19,31:20,32:21,33:22,34:23,35:24,36:25,37:26,38:27,39:28,40:29,41:30,42:31,43:32,44:33,45:34,46:35,47:36,48:37,49:38,50:39,51:40,52:41,54:43,55:44,56:45,57:46,58:47,59:48,60:49,61:50,62:51,63:52,64:53,65:54,66:55,67:56,68:57,69:58,70:59,78:74,501:95,183:99,3:100,12:818,2:r,4:a,5:s,53:o,71:u,88:c,123:l,145:h,155:d,188:f,265:p,286:b,331:E,334:g,335:m,343:v,392:S,396:T,397:y,400:N,402:C,404:R,405:O,413:w,414:x,415:D,431:L,433:k,434:$,436:M,437:U,438:_,439:F,440:P,444:q,445:G,448:V,449:B,502:j,504:H,505:J,514:Y}),{392:[1,822],397:[1,819],398:[1,820],399:[1,821]},{2:r,3:823,4:a,5:s},t(Ar,[2,1104],{285:824,764:826,77:[1,825],163:[1,828],184:[1,827]}),{2:r,3:166,4:a,5:s,57:163,76:oe,93:259,110:147,112:151,130:ue,131:ce,136:le,142:he,143:159,144:de,148:fe,150:829,151:pe,153:be,155:z,157:165,178:Ee,179:ge,180:me,195:149,199:145,200:153,201:154,253:148,254:144,255:146,256:150,257:152,258:155,259:156,260:157,261:160,262:161,264:ve,265:p,266:Se,267:Te,269:Ae,276:ye,277:Ne,278:Ce,279:Re,280:Oe,281:we,282:Ie,283:xe,284:De,286:Z,287:ee,288:te,289:ne,290:Le,291:ke,292:$e,293:Me,294:Ue,295:_e,298:Fe,299:Pe,308:qe,313:Ge,416:188,417:Ve,421:Be},{2:r,3:166,4:a,5:s,57:163,76:oe,93:259,110:147,112:151,130:ue,131:ce,136:le,142:he,143:159,144:de,148:fe,150:830,151:pe,153:be,155:z,157:165,178:Ee,179:ge,180:me,195:149,199:145,200:153,201:154,253:148,254:144,255:146,256:150,257:152,258:155,259:156,260:157,261:160,262:161,264:ve,265:p,266:Se,267:Te,269:Ae,276:ye,277:Ne,278:Ce,279:Re,280:Oe,281:we,282:Ie,283:xe,284:De,286:Z,287:ee,288:te,289:ne,290:Le,291:ke,292:$e,293:Me,294:Ue,295:_e,298:Fe,299:Pe,308:qe,313:Ge,416:188,417:Ve,421:Be},{2:r,3:831,4:a,5:s,131:[1,832]},{2:r,3:833,4:a,5:s,131:[1,834]},{2:r,3:835,4:a,5:s,98:At,111:yt,113:622,114:Nt,115:Ct,122:Rt,123:gr,132:wt,135:It,137:xt,138:Dt,139:Lt,140:kt,141:$t,153:Mt,169:Ut,170:_t,178:Ft,179:Pt,307:qt,308:Gt,309:Vt,311:Bt,312:jt,313:Ht,314:Jt,315:Yt,316:Wt,317:Xt,318:Kt,319:Qt,320:zt,321:Zt,322:en,323:634,326:tn,327:nn,328:rn,329:an},{2:r,3:836,4:a,5:s},{153:[1,837]},t(Cr,dt,{346:838,155:ft}),{229:[1,839]},{2:r,3:840,4:a,5:s},t(Q,[2,722],{73:Rr}),{2:r,3:166,4:a,5:s,57:163,76:oe,93:842,110:147,112:151,130:ue,131:ce,136:le,142:he,143:159,144:de,148:fe,151:pe,153:be,155:z,157:165,178:Ee,179:ge,180:me,195:149,199:145,200:153,201:154,253:148,254:144,255:146,256:150,257:152,258:155,259:156,260:157,261:160,262:161,264:ve,265:p,266:Se,267:Te,269:Ae,276:ye,277:Ne,278:Ce,279:Re,280:Oe,281:we,282:Ie,283:xe,284:De,286:Z,287:ee,288:te,289:ne,290:Le,291:ke,292:$e,293:Me,294:Ue,295:_e,298:Fe,299:Pe,308:qe,313:Ge,416:188,417:Ve,421:Be},t(Tr,[2,725]),t(Or,[2,1136],{416:188,473:843,143:844,138:wr,140:wr,144:Ze,417:Ve,421:Be}),{138:[1,845],140:[1,846]},t(Ir,xr,{487:848,490:849,76:[1,847],136:bt}),t(Dr,[2,1160],{491:850,131:[1,851]}),t(Lr,[2,1164],{493:852,494:853,151:Et}),t(Lr,[2,740]),t(kr,[2,732]),{2:r,3:854,4:a,5:s,130:[1,855]},{2:r,3:856,4:a,5:s},{2:r,3:857,4:a,5:s},t(je,dt,{346:858,155:ft}),t(je,dt,{346:859,155:ft}),t(Tt,[2,482]),t(Tt,[2,483]),{182:[1,860]},{182:[2,1135]},t($r,[2,1130],{463:861,466:862,136:[1,863]}),t(gt,[2,1129]),t(Mr,Ur,{507:864,94:_r,229:[1,865],511:Fr,512:Pr,513:qr}),{144:ie,447:870},{4:Gr,7:874,75:[1,872],268:871,383:873,385:Vr},t(Q,[2,453],{127:[1,877]}),t(Q,[2,570]),{2:r,3:878,4:a,5:s},{294:[1,879]},t(Cr,vt,{394:880,155:St}),t(Q,[2,584]),{2:r,3:242,4:a,5:s,198:882,395:881},{2:r,3:242,4:a,5:s,198:882,395:883},t(Q,[2,765]),t(K,[2,662],{435:884,306:[1,885]}),{2:r,3:166,4:a,5:s,57:163,76:oe,93:886,110:147,112:151,130:ue,131:ce,136:le,142:he,143:159,144:de,148:fe,151:pe,153:be,155:z,157:165,178:Ee,179:ge,180:me,195:149,199:145,200:153,201:154,253:148,254:144,255:146,256:150,257:152,258:155,259:156,260:157,261:160,262:161,264:ve,265:p,266:Se,267:Te,269:Ae,276:ye,277:Ne,278:Ce,279:Re,280:Oe,281:we,282:Ie,283:xe,284:De,286:Z,287:ee,288:te,289:ne,290:Le,291:ke,292:$e,293:Me,294:Ue,295:_e,298:Fe,299:Pe,308:qe,313:Ge,416:188,417:Ve,421:Be},{2:r,3:166,4:a,5:s,57:163,76:oe,93:887,110:147,112:151,130:ue,131:ce,136:le,142:he,143:159,144:de,148:fe,151:pe,153:be,155:z,157:165,178:Ee,179:ge,180:me,195:149,199:145,200:153,201:154,253:148,254:144,255:146,256:150,257:152,258:155,259:156,260:157,261:160,262:161,264:ve,265:p,266:Se,267:Te,269:Ae,276:ye,277:Ne,278:Ce,279:Re,280:Oe,281:we,282:Ie,283:xe,284:De,286:Z,287:ee,288:te,289:ne,290:Le,291:ke,292:$e,293:Me,294:Ue,295:_e,298:Fe,299:Pe,308:qe,313:Ge,416:188,417:Ve,421:Be},{2:r,3:166,4:a,5:s,57:163,76:oe,93:888,110:147,112:151,130:ue,131:ce,136:le,142:he,143:159,144:de,148:fe,151:pe,153:be,155:z,157:165,178:Ee,179:ge,180:me,195:149,199:145,200:153,201:154,253:148,254:144,255:146,256:150,257:152,258:155,259:156,260:157,261:160,262:161,264:ve,265:p,266:Se,267:Te,269:Ae,276:ye,277:Ne,278:Ce,279:Re,280:Oe,281:we,282:Ie,283:xe,284:De,286:Z,287:ee,288:te,289:ne,290:Le,291:ke,292:$e,293:Me,294:Ue,295:_e,298:Fe,299:Pe,308:qe,313:Ge,416:188,417:Ve,421:Be},{2:r,3:166,4:a,5:s,57:163,76:oe,93:889,110:147,112:151,130:ue,131:ce,136:le,142:he,143:159,144:de,148:fe,151:pe,153:be,155:z,157:165,178:Ee,179:ge,180:me,195:149,199:145,200:153,201:154,253:148,254:144,255:146,256:150,257:152,258:155,259:156,260:157,261:160,262:161,264:ve,265:p,266:Se,267:Te,269:Ae,276:ye,277:Ne,278:Ce,279:Re,280:Oe,281:we,282:Ie,283:xe,284:De,286:Z,287:ee,288:te,289:ne,290:Le,291:ke,292:$e,293:Me,294:Ue,295:_e,298:Fe,299:Pe,308:qe,313:Ge,416:188,417:Ve,421:Be},{2:r,3:166,4:a,5:s,57:163,76:oe,93:890,110:147,112:151,130:ue,131:ce,136:le,142:he,143:159,144:de,148:fe,151:pe,153:be,155:z,157:165,178:Ee,179:ge,180:me,195:149,199:145,200:153,201:154,253:148,254:144,255:146,256:150,257:152,258:155,259:156,260:157,261:160,262:161,264:ve,265:p,266:Se,267:Te,269:Ae,276:ye,277:Ne,278:Ce,279:Re,280:Oe,281:we,282:Ie,283:xe,284:De,286:Z,287:ee,288:te,289:ne,290:Le,291:ke,292:$e,293:Me,294:Ue,295:_e,298:Fe,299:Pe,308:qe,313:Ge,416:188,417:Ve,421:Be},{2:r,3:166,4:a,5:s,57:163,76:oe,93:891,110:147,112:151,130:ue,131:ce,136:le,142:he,143:159,144:de,148:fe,151:pe,153:be,155:z,157:165,178:Ee,179:ge,180:me,195:149,199:145,200:153,201:154,253:148,254:144,255:146,256:150,257:152,258:155,259:156,260:157,261:160,262:161,
264:ve,265:p,266:Se,267:Te,269:Ae,276:ye,277:Ne,278:Ce,279:Re,280:Oe,281:we,282:Ie,283:xe,284:De,286:Z,287:ee,288:te,289:ne,290:Le,291:ke,292:$e,293:Me,294:Ue,295:_e,298:Fe,299:Pe,308:qe,313:Ge,416:188,417:Ve,421:Be},{2:r,3:166,4:a,5:s,57:163,76:oe,93:892,110:147,112:151,130:ue,131:ce,136:le,142:he,143:159,144:de,148:fe,151:pe,153:be,155:z,157:165,178:Ee,179:ge,180:me,195:149,199:145,200:153,201:154,253:148,254:144,255:146,256:150,257:152,258:155,259:156,260:157,261:160,262:161,264:ve,265:p,266:Se,267:Te,269:Ae,276:ye,277:Ne,278:Ce,279:Re,280:Oe,281:we,282:Ie,283:xe,284:De,286:Z,287:ee,288:te,289:ne,290:Le,291:ke,292:$e,293:Me,294:Ue,295:_e,298:Fe,299:Pe,308:qe,313:Ge,416:188,417:Ve,421:Be},{2:r,3:166,4:a,5:s,57:163,76:oe,93:893,110:147,112:151,130:ue,131:ce,136:le,142:he,143:159,144:de,148:fe,151:pe,153:be,155:z,157:165,178:Ee,179:ge,180:me,195:149,199:145,200:153,201:154,253:148,254:144,255:146,256:150,257:152,258:155,259:156,260:157,261:160,262:161,264:ve,265:p,266:Se,267:Te,269:Ae,276:ye,277:Ne,278:Ce,279:Re,280:Oe,281:we,282:Ie,283:xe,284:De,286:Z,287:ee,288:te,289:ne,290:Le,291:ke,292:$e,293:Me,294:Ue,295:_e,298:Fe,299:Pe,308:qe,313:Ge,416:188,417:Ve,421:Be},{2:r,3:166,4:a,5:s,57:163,76:oe,93:894,110:147,112:151,130:ue,131:ce,136:le,142:he,143:159,144:de,148:fe,151:pe,153:be,155:z,157:165,178:Ee,179:ge,180:me,195:149,199:145,200:153,201:154,253:148,254:144,255:146,256:150,257:152,258:155,259:156,260:157,261:160,262:161,264:ve,265:p,266:Se,267:Te,269:Ae,276:ye,277:Ne,278:Ce,279:Re,280:Oe,281:we,282:Ie,283:xe,284:De,286:Z,287:ee,288:te,289:ne,290:Le,291:ke,292:$e,293:Me,294:Ue,295:_e,298:Fe,299:Pe,308:qe,313:Ge,416:188,417:Ve,421:Be},{2:r,3:166,4:a,5:s,57:163,76:oe,93:895,110:147,112:151,130:ue,131:ce,136:le,142:he,143:159,144:de,148:fe,151:pe,153:be,155:z,157:165,178:Ee,179:ge,180:me,195:149,199:145,200:153,201:154,253:148,254:144,255:146,256:150,257:152,258:155,259:156,260:157,261:160,262:161,264:ve,265:p,266:Se,267:Te,269:Ae,276:ye,277:Ne,278:Ce,279:Re,280:Oe,281:we,282:Ie,283:xe,284:De,286:Z,287:ee,288:te,289:ne,290:Le,291:ke,292:$e,293:Me,294:Ue,295:_e,298:Fe,299:Pe,308:qe,313:Ge,416:188,417:Ve,421:Be},{2:r,3:166,4:a,5:s,57:163,76:oe,93:896,110:147,112:151,130:ue,131:ce,136:le,142:he,143:159,144:de,148:fe,151:pe,153:be,155:z,157:165,178:Ee,179:ge,180:me,195:149,199:145,200:153,201:154,253:148,254:144,255:146,256:150,257:152,258:155,259:156,260:157,261:160,262:161,264:ve,265:p,266:Se,267:Te,269:Ae,276:ye,277:Ne,278:Ce,279:Re,280:Oe,281:we,282:Ie,283:xe,284:De,286:Z,287:ee,288:te,289:ne,290:Le,291:ke,292:$e,293:Me,294:Ue,295:_e,298:Fe,299:Pe,308:qe,313:Ge,416:188,417:Ve,421:Be},{2:r,3:166,4:a,5:s,57:163,76:oe,93:897,110:147,112:151,130:ue,131:ce,136:le,142:he,143:159,144:de,148:fe,151:pe,153:be,155:z,157:165,178:Ee,179:ge,180:me,195:149,199:145,200:153,201:154,253:148,254:144,255:146,256:150,257:152,258:155,259:156,260:157,261:160,262:161,264:ve,265:p,266:Se,267:Te,269:Ae,276:ye,277:Ne,278:Ce,279:Re,280:Oe,281:we,282:Ie,283:xe,284:De,286:Z,287:ee,288:te,289:ne,290:Le,291:ke,292:$e,293:Me,294:Ue,295:_e,298:Fe,299:Pe,308:qe,313:Ge,416:188,417:Ve,421:Be},{2:r,3:166,4:a,5:s,57:163,76:oe,93:898,110:147,112:151,130:ue,131:ce,136:le,142:he,143:159,144:de,148:fe,151:pe,153:be,155:z,157:165,178:Ee,179:ge,180:me,195:149,199:145,200:153,201:154,253:148,254:144,255:146,256:150,257:152,258:155,259:156,260:157,261:160,262:161,264:ve,265:p,266:Se,267:Te,269:Ae,276:ye,277:Ne,278:Ce,279:Re,280:Oe,281:we,282:Ie,283:xe,284:De,286:Z,287:ee,288:te,289:ne,290:Le,291:ke,292:$e,293:Me,294:Ue,295:_e,298:Fe,299:Pe,308:qe,313:Ge,416:188,417:Ve,421:Be},{2:r,3:166,4:a,5:s,57:163,76:oe,93:899,110:147,112:151,130:ue,131:ce,136:le,142:he,143:159,144:de,148:fe,151:pe,153:be,155:z,157:165,178:Ee,179:ge,180:me,195:149,199:145,200:153,201:154,253:148,254:144,255:146,256:150,257:152,258:155,259:156,260:157,261:160,262:161,264:ve,265:p,266:Se,267:Te,269:Ae,276:ye,277:Ne,278:Ce,279:Re,280:Oe,281:we,282:Ie,283:xe,284:De,286:Z,287:ee,288:te,289:ne,290:Le,291:ke,292:$e,293:Me,294:Ue,295:_e,298:Fe,299:Pe,308:qe,313:Ge,416:188,417:Ve,421:Be},{2:r,3:166,4:a,5:s,57:163,76:oe,93:900,110:147,112:151,130:ue,131:ce,136:le,142:he,143:159,144:de,148:fe,151:pe,153:be,155:z,157:165,178:Ee,179:ge,180:me,195:149,199:145,200:153,201:154,253:148,254:144,255:146,256:150,257:152,258:155,259:156,260:157,261:160,262:161,264:ve,265:p,266:Se,267:Te,269:Ae,276:ye,277:Ne,278:Ce,279:Re,280:Oe,281:we,282:Ie,283:xe,284:De,286:Z,287:ee,288:te,289:ne,290:Le,291:ke,292:$e,293:Me,294:Ue,295:_e,298:Fe,299:Pe,308:qe,313:Ge,416:188,417:Ve,421:Be},{2:r,3:166,4:a,5:s,57:163,76:oe,93:901,110:147,112:151,130:ue,131:ce,136:le,142:he,143:159,144:de,148:fe,151:pe,153:be,155:z,157:165,178:Ee,179:ge,180:me,195:149,199:145,200:153,201:154,253:148,254:144,255:146,256:150,257:152,258:155,259:156,260:157,261:160,262:161,264:ve,265:p,266:Se,267:Te,269:Ae,276:ye,277:Ne,278:Ce,279:Re,280:Oe,281:we,282:Ie,283:xe,284:De,286:Z,287:ee,288:te,289:ne,290:Le,291:ke,292:$e,293:Me,294:Ue,295:_e,298:Fe,299:Pe,308:qe,313:Ge,416:188,417:Ve,421:Be},{2:r,3:902,4:a,5:s,76:[1,904],130:ue,155:z,195:903,199:905,286:Z,287:ee,288:te,289:ne},{2:r,3:906,4:a,5:s,76:[1,908],130:ue,155:z,195:907,199:909,286:Z,287:ee,288:te,289:ne},t(Br,[2,437],{254:144,199:145,255:146,110:147,253:148,195:149,256:150,112:151,257:152,200:153,201:154,258:155,259:156,260:157,143:159,261:160,262:161,57:163,157:165,3:166,416:188,93:910,2:r,4:a,5:s,76:oe,130:ue,131:ce,136:le,142:he,144:de,148:fe,151:pe,153:be,155:z,178:Ee,179:ge,180:me,264:ve,265:p,266:Se,267:Te,269:Ae,276:ye,277:Ne,278:Ce,279:Re,280:Oe,281:we,282:Ie,283:xe,284:De,286:Z,287:ee,288:te,289:ne,290:Le,291:ke,292:$e,293:Me,294:Ue,295:_e,298:Fe,299:Pe,308:qe,313:Ge,417:Ve,421:Be}),t(Br,[2,438],{254:144,199:145,255:146,110:147,253:148,195:149,256:150,112:151,257:152,200:153,201:154,258:155,259:156,260:157,143:159,261:160,262:161,57:163,157:165,3:166,416:188,93:911,2:r,4:a,5:s,76:oe,130:ue,131:ce,136:le,142:he,144:de,148:fe,151:pe,153:be,155:z,178:Ee,179:ge,180:me,264:ve,265:p,266:Se,267:Te,269:Ae,276:ye,277:Ne,278:Ce,279:Re,280:Oe,281:we,282:Ie,283:xe,284:De,286:Z,287:ee,288:te,289:ne,290:Le,291:ke,292:$e,293:Me,294:Ue,295:_e,298:Fe,299:Pe,308:qe,313:Ge,417:Ve,421:Be}),t(Br,[2,439],{254:144,199:145,255:146,110:147,253:148,195:149,256:150,112:151,257:152,200:153,201:154,258:155,259:156,260:157,143:159,261:160,262:161,57:163,157:165,3:166,416:188,93:912,2:r,4:a,5:s,76:oe,130:ue,131:ce,136:le,142:he,144:de,148:fe,151:pe,153:be,155:z,178:Ee,179:ge,180:me,264:ve,265:p,266:Se,267:Te,269:Ae,276:ye,277:Ne,278:Ce,279:Re,280:Oe,281:we,282:Ie,283:xe,284:De,286:Z,287:ee,288:te,289:ne,290:Le,291:ke,292:$e,293:Me,294:Ue,295:_e,298:Fe,299:Pe,308:qe,313:Ge,417:Ve,421:Be}),t(Br,[2,440],{254:144,199:145,255:146,110:147,253:148,195:149,256:150,112:151,257:152,200:153,201:154,258:155,259:156,260:157,143:159,261:160,262:161,57:163,157:165,3:166,416:188,93:913,2:r,4:a,5:s,76:oe,130:ue,131:ce,136:le,142:he,144:de,148:fe,151:pe,153:be,155:z,178:Ee,179:ge,180:me,264:ve,265:p,266:Se,267:Te,269:Ae,276:ye,277:Ne,278:Ce,279:Re,280:Oe,281:we,282:Ie,283:xe,284:De,286:Z,287:ee,288:te,289:ne,290:Le,291:ke,292:$e,293:Me,294:Ue,295:_e,298:Fe,299:Pe,308:qe,313:Ge,417:Ve,421:Be}),t(Br,jr,{254:144,199:145,255:146,110:147,253:148,195:149,256:150,112:151,257:152,200:153,201:154,258:155,259:156,260:157,143:159,261:160,262:161,57:163,157:165,3:166,416:188,93:914,2:r,4:a,5:s,76:oe,130:ue,131:ce,136:le,142:he,144:de,148:fe,151:pe,153:be,155:z,178:Ee,179:ge,180:me,264:ve,265:p,266:Se,267:Te,269:Ae,276:ye,277:Ne,278:Ce,279:Re,280:Oe,281:we,282:Ie,283:xe,284:De,286:Z,287:ee,288:te,289:ne,290:Le,291:ke,292:$e,293:Me,294:Ue,295:_e,298:Fe,299:Pe,308:qe,313:Ge,417:Ve,421:Be}),{2:r,3:166,4:a,5:s,57:163,76:oe,93:915,110:147,112:151,130:ue,131:ce,136:le,142:he,143:159,144:de,148:fe,151:pe,153:be,155:z,157:165,178:Ee,179:ge,180:me,195:149,199:145,200:153,201:154,253:148,254:144,255:146,256:150,257:152,258:155,259:156,260:157,261:160,262:161,264:ve,265:p,266:Se,267:Te,269:Ae,276:ye,277:Ne,278:Ce,279:Re,280:Oe,281:we,282:Ie,283:xe,284:De,286:Z,287:ee,288:te,289:ne,290:Le,291:ke,292:$e,293:Me,294:Ue,295:_e,298:Fe,299:Pe,308:qe,313:Ge,416:188,417:Ve,421:Be},{2:r,3:166,4:a,5:s,57:163,76:oe,93:916,110:147,112:151,130:ue,131:ce,136:le,142:he,143:159,144:de,148:fe,151:pe,153:be,155:z,157:165,178:Ee,179:ge,180:me,195:149,199:145,200:153,201:154,253:148,254:144,255:146,256:150,257:152,258:155,259:156,260:157,261:160,262:161,264:ve,265:p,266:Se,267:Te,269:Ae,276:ye,277:Ne,278:Ce,279:Re,280:Oe,281:we,282:Ie,283:xe,284:De,286:Z,287:ee,288:te,289:ne,290:Le,291:ke,292:$e,293:Me,294:Ue,295:_e,298:Fe,299:Pe,308:qe,313:Ge,416:188,417:Ve,421:Be},t(Br,[2,442],{254:144,199:145,255:146,110:147,253:148,195:149,256:150,112:151,257:152,200:153,201:154,258:155,259:156,260:157,143:159,261:160,262:161,57:163,157:165,3:166,416:188,93:917,2:r,4:a,5:s,76:oe,130:ue,131:ce,136:le,142:he,144:de,148:fe,151:pe,153:be,155:z,178:Ee,179:ge,180:me,264:ve,265:p,266:Se,267:Te,269:Ae,276:ye,277:Ne,278:Ce,279:Re,280:Oe,281:we,282:Ie,283:xe,284:De,286:Z,287:ee,288:te,289:ne,290:Le,291:ke,292:$e,293:Me,294:Ue,295:_e,298:Fe,299:Pe,308:qe,313:Ge,417:Ve,421:Be}),{2:r,3:166,4:a,5:s,57:163,76:oe,93:918,110:147,112:151,130:ue,131:ce,136:le,142:he,143:159,144:de,148:fe,151:pe,153:be,155:z,157:165,178:Ee,179:ge,180:me,195:149,199:145,200:153,201:154,253:148,254:144,255:146,256:150,257:152,258:155,259:156,260:157,261:160,262:161,264:ve,265:p,266:Se,267:Te,269:Ae,276:ye,277:Ne,278:Ce,279:Re,280:Oe,281:we,282:Ie,283:xe,284:De,286:Z,287:ee,288:te,289:ne,290:Le,291:ke,292:$e,293:Me,294:Ue,295:_e,298:Fe,299:Pe,308:qe,313:Ge,416:188,417:Ve,421:Be},{2:r,3:166,4:a,5:s,57:163,76:oe,93:919,110:147,112:151,130:ue,131:ce,136:le,142:he,143:159,144:de,148:fe,151:pe,153:be,155:z,157:165,178:Ee,179:ge,180:me,195:149,199:145,200:153,201:154,253:148,254:144,255:146,256:150,257:152,258:155,259:156,260:157,261:160,262:161,264:ve,265:p,266:Se,267:Te,269:Ae,276:ye,277:Ne,278:Ce,279:Re,280:Oe,281:we,282:Ie,283:xe,284:De,286:Z,287:ee,288:te,289:ne,290:Le,291:ke,292:$e,293:Me,294:Ue,295:_e,298:Fe,299:Pe,308:qe,313:Ge,416:188,417:Ve,421:Be},{163:[1,921],165:[1,923],324:920,330:[1,922]},{2:r,3:166,4:a,5:s,57:163,76:oe,93:924,110:147,112:151,130:ue,131:ce,136:le,142:he,143:159,144:de,148:fe,151:pe,153:be,155:z,157:165,178:Ee,179:ge,180:me,195:149,199:145,200:153,201:154,253:148,254:144,255:146,256:150,257:152,258:155,259:156,260:157,261:160,262:161,264:ve,265:p,266:Se,267:Te,269:Ae,276:ye,277:Ne,278:Ce,279:Re,280:Oe,281:we,282:Ie,283:xe,284:De,286:Z,287:ee,288:te,289:ne,290:Le,291:ke,292:$e,293:Me,294:Ue,295:_e,298:Fe,299:Pe,308:qe,313:Ge,416:188,417:Ve,421:Be},{2:r,3:166,4:a,5:s,57:163,76:oe,93:925,110:147,112:151,130:ue,131:ce,136:le,142:he,143:159,144:de,148:fe,151:pe,153:be,155:z,157:165,178:Ee,179:ge,180:me,195:149,199:145,200:153,201:154,253:148,254:144,255:146,256:150,257:152,258:155,259:156,260:157,261:160,262:161,264:ve,265:p,266:Se,267:Te,269:Ae,276:ye,277:Ne,278:Ce,279:Re,280:Oe,281:we,282:Ie,283:xe,284:De,286:Z,287:ee,288:te,289:ne,290:Le,291:ke,292:$e,293:Me,294:Ue,295:_e,298:Fe,299:Pe,308:qe,313:Ge,416:188,417:Ve,421:Be},{2:r,3:692,4:a,5:s,76:[1,926],110:929,144:Hr,155:z,199:930,201:928,286:Z,287:ee,288:te,289:ne,325:927},{98:[1,932],293:[1,933]},{2:r,3:166,4:a,5:s,57:163,76:oe,93:934,110:147,112:151,130:ue,131:ce,136:le,142:he,143:159,144:de,148:fe,151:pe,153:be,155:z,157:165,178:Ee,179:ge,180:me,195:149,199:145,200:153,201:154,253:148,254:144,255:146,256:150,257:152,258:155,259:156,260:157,261:160,262:161,264:ve,265:p,266:Se,267:Te,269:Ae,276:ye,277:Ne,278:Ce,279:Re,280:Oe,281:we,282:Ie,283:xe,284:De,286:Z,287:ee,288:te,289:ne,290:Le,291:ke,292:$e,293:Me,294:Ue,295:_e,298:Fe,299:Pe,308:qe,313:Ge,416:188,417:Ve,421:Be},{2:r,3:166,4:a,5:s,57:163,76:oe,93:935,110:147,112:151,130:ue,131:ce,136:le,142:he,143:159,144:de,148:fe,151:pe,153:be,155:z,157:165,178:Ee,179:ge,180:me,195:149,199:145,200:153,201:154,253:148,254:144,255:146,256:150,257:152,258:155,259:156,260:157,261:160,262:161,264:ve,265:p,266:Se,267:Te,269:Ae,276:ye,277:Ne,278:Ce,279:Re,280:Oe,281:we,282:Ie,283:xe,284:De,286:Z,287:ee,288:te,289:ne,290:Le,291:ke,292:$e,293:Me,294:Ue,295:_e,298:Fe,299:Pe,308:qe,313:Ge,416:188,417:Ve,421:Be},{2:r,3:166,4:a,5:s,57:163,76:oe,93:936,110:147,112:151,130:ue,131:ce,136:le,142:he,143:159,144:de,148:fe,151:pe,153:be,155:z,157:165,178:Ee,179:ge,180:me,195:149,199:145,200:153,201:154,253:148,254:144,255:146,256:150,257:152,258:155,259:156,260:157,261:160,262:161,264:ve,265:p,266:Se,267:Te,269:Ae,276:ye,277:Ne,278:Ce,279:Re,280:Oe,281:we,282:Ie,283:xe,284:De,286:Z,287:ee,288:te,289:ne,290:Le,291:ke,292:$e,293:Me,294:Ue,295:_e,298:Fe,299:Pe,308:qe,313:Ge,416:188,417:Ve,421:Be},{4:Gr,7:874,268:937,383:873,385:Vr},t(Jr,[2,87]),t(Jr,[2,88]),{77:[1,938]},{77:[1,939]},{77:[1,940]},{77:[1,941],98:At,111:yt,113:622,114:Nt,115:Ct,122:Rt,123:gr,132:wt,135:It,137:xt,138:Dt,139:Lt,140:kt,141:$t,153:Mt,169:Ut,170:_t,178:Ft,179:Pt,307:qt,308:Gt,309:Vt,311:Bt,312:jt,313:Ht,314:Jt,315:Yt,316:Wt,317:Xt,318:Kt,319:Qt,320:zt,321:Zt,322:en,323:634,326:tn,327:nn,328:rn,329:an},t(je,He,{336:205,76:ht,197:Je}),{77:[2,1100]},{77:[2,1101]},{133:ae,134:se},{2:r,3:166,4:a,5:s,57:163,76:oe,93:259,110:147,112:151,130:ue,131:ce,136:le,142:he,143:159,144:de,148:fe,150:942,151:pe,153:be,155:z,157:165,163:[1,944],178:Ee,179:ge,180:me,184:[1,943],195:149,199:145,200:153,201:154,253:148,254:144,255:146,256:150,257:152,258:155,259:156,260:157,261:160,262:161,264:ve,265:p,266:Se,267:Te,269:Ae,276:ye,277:Ne,278:Ce,279:Re,280:Oe,281:we,282:Ie,283:xe,284:De,286:Z,287:ee,288:te,289:ne,290:Le,291:ke,292:$e,293:Me,294:Ue,295:_e,298:Fe,299:Pe,308:qe,313:Ge,416:188,417:Ve,421:Be},{2:r,3:945,4:a,5:s,148:Yr,179:[1,947]},t([2,4,5,10,53,71,73,75,76,77,88,92,94,97,98,106,117,121,127,128,129,130,131,133,134,136,142,144,145,147,148,149,151,153,155,161,163,165,167,168,169,170,171,172,174,180,182,184,186,188,197,205,207,221,222,223,224,225,226,227,228,231,238,241,242,244,246,265,276,277,278,279,280,281,282,283,284,286,292,296,302,304,305,306,310,326,327,329,331,334,335,343,392,396,397,400,402,404,405,413,414,415,417,421,431,433,434,436,437,438,439,440,444,445,448,449,461,467,502,504,505,514,599,761],[2,413],{113:622,323:634,111:yt,114:Nt,115:Ct,122:Rt,123:gr,132:wt,135:It,137:xt,138:Dt,139:Lt,140:kt,141:$t,178:Ft,179:Pt,307:qt,308:Gt,309:Vt,311:Bt,312:jt,313:Ht,314:Jt,315:Yt,316:Wt,317:Xt,318:Kt,319:Qt,320:zt,321:Zt,322:en,328:rn}),t(Wr,[2,414],{113:622,323:634,114:Nt,115:Ct,122:Rt,132:wt,135:It,137:xt,179:Pt,308:Gt,312:jt}),t(Wr,[2,415],{113:622,323:634,114:Nt,115:Ct,122:Rt,132:wt,135:It,137:xt,179:Pt,308:Gt,312:jt}),t(Xr,[2,416],{113:622,323:634,312:jt}),t(Xr,[2,417],{113:622,323:634,312:jt}),t(on,[2,362]),t(on,[2,1106]),t(on,[2,1107]),t(on,[2,363]),t([2,4,5,10,53,71,73,75,76,77,88,92,94,97,98,106,111,114,115,117,121,122,123,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,144,145,147,148,149,151,153,155,161,163,165,167,168,169,170,171,172,174,178,179,180,182,184,186,188,197,205,207,221,222,223,224,225,226,227,228,229,230,231,238,241,242,244,246,265,276,277,278,279,280,281,282,283,284,286,292,296,302,304,305,306,307,308,309,310,311,312,313,314,315,316,317,318,319,320,321,322,326,327,328,329,331,334,335,343,392,396,397,400,402,404,405,413,414,415,417,421,431,433,434,436,437,438,439,440,444,445,448,449,461,467,502,504,505,514,599,761],[2,359]),{2:r,3:166,4:a,5:s,57:163,76:oe,93:948,110:147,112:151,130:ue,131:ce,136:le,142:he,143:159,144:de,148:fe,151:pe,153:be,155:z,157:165,178:Ee,179:ge,180:me,195:149,199:145,200:153,201:154,253:148,254:144,255:146,256:150,257:152,258:155,259:156,260:157,261:160,262:161,264:ve,265:p,266:Se,267:Te,269:Ae,276:ye,277:Ne,278:Ce,279:Re,280:Oe,281:we,282:Ie,283:xe,284:De,286:Z,287:ee,288:te,289:ne,290:Le,291:ke,292:$e,293:Me,294:Ue,295:_e,298:Fe,299:Pe,308:qe,313:Ge,416:188,417:Ve,421:Be},t(hn,[2,613]),t(hn,[2,614]),t(hn,[2,615]),t(hn,[2,616]),t(hn,[2,618]),{40:949,78:74,88:c,183:99,188:f},{98:At,111:yt,113:622,114:Nt,115:Ct,122:Rt,123:gr,132:wt,135:It,137:xt,138:Dt,139:Lt,140:kt,141:$t,153:Mt,169:Ut,170:_t,178:Ft,179:Pt,300:950,303:674,304:ln,307:qt,308:Gt,309:Vt,311:Bt,312:jt,313:Ht,314:Jt,315:Yt,316:Wt,317:Xt,318:Kt,319:Qt,320:zt,321:Zt,322:en,323:634,326:tn,327:nn,328:rn,329:an},{301:951,302:Kr,303:952,304:ln,306:Qr},t(zr,[2,369]),{2:r,3:166,4:a,5:s,57:163,76:oe,93:954,110:147,112:151,130:ue,131:ce,136:le,142:he,143:159,144:de,148:fe,151:pe,153:be,155:z,157:165,178:Ee,179:ge,180:me,195:149,199:145,200:153,201:154,253:148,254:144,255:146,256:150,257:152,258:155,259:156,260:157,261:160,262:161,264:ve,265:p,266:Se,267:Te,269:Ae,276:ye,277:Ne,278:Ce,279:Re,280:Oe,281:we,282:Ie,283:xe,284:De,286:Z,287:ee,288:te,289:ne,290:Le,291:ke,292:$e,293:Me,294:Ue,295:_e,298:Fe,299:Pe,308:qe,313:Ge,416:188,417:Ve,421:Be},{2:r,3:166,4:a,5:s,57:163,76:oe,93:955,110:147,112:151,130:ue,131:ce,136:le,142:he,143:159,144:de,148:fe,151:pe,153:be,155:z,157:165,178:Ee,179:ge,180:me,195:149,199:145,200:153,201:154,253:148,254:144,255:146,256:150,257:152,258:155,259:156,260:157,261:160,262:161,264:ve,265:p,266:Se,267:Te,269:Ae,276:ye,277:Ne,278:Ce,279:Re,280:Oe,281:we,282:Ie,283:xe,284:De,286:Z,287:ee,288:te,289:ne,290:Le,291:ke,292:$e,293:Me,294:Ue,295:_e,298:Fe,299:Pe,308:qe,313:Ge,416:188,417:Ve,421:Be},{4:Gr,7:874,268:956,383:873,385:Vr},t(hn,[2,619]),{73:[1,958],296:[1,957]},t(hn,[2,635]),t(Zr,[2,642]),t(ea,[2,620]),t(ea,[2,621]),t(ea,[2,622]),t(ea,[2,623]),t(ea,[2,624]),t(ea,[2,625]),t(ea,[2,626]),t(ea,[2,627]),t(ea,[2,628]),{2:r,3:166,4:a,5:s,57:163,76:oe,93:959,110:147,112:151,130:ue,131:ce,136:le,142:he,143:159,144:de,148:fe,151:pe,153:be,155:z,157:165,178:Ee,179:ge,180:me,195:149,199:145,200:153,201:154,253:148,254:144,255:146,256:150,257:152,258:155,259:156,260:157,261:160,262:161,264:ve,265:p,266:Se,267:Te,269:Ae,276:ye,277:Ne,278:Ce,279:Re,280:Oe,281:we,282:Ie,283:xe,284:De,286:Z,287:ee,288:te,289:ne,290:Le,291:ke,292:$e,293:Me,294:Ue,295:_e,298:Fe,299:Pe,308:qe,313:Ge,416:188,417:Ve,421:Be},t([2,4,5,10,53,71,73,75,77,88,92,94,97,98,106,111,114,117,121,122,123,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,144,145,147,148,149,151,153,155,161,163,165,167,168,169,170,171,172,174,178,179,180,182,184,186,188,197,205,207,221,222,223,224,225,226,227,228,231,238,241,242,244,246,265,276,277,278,279,280,281,282,283,284,286,292,296,302,304,305,306,307,308,309,310,311,312,313,314,315,316,317,318,319,320,321,322,326,327,328,329,331,334,335,343,392,396,397,400,402,404,405,413,414,415,417,421,423,431,433,434,436,437,438,439,440,444,445,448,449,461,467,502,504,505,514,599,761],sn,{76:lt,115:ta}),{73:br,296:[1,961]},t(na,[2,311],{76:lt}),t(ze,[2,312]),{73:[1,963],423:[1,962]},t(hn,[2,632]),t(ra,[2,637]),{151:[1,964]},{151:[1,965]},{151:[1,966]},{40:970,76:[1,969],78:74,88:c,183:99,188:f,337:[1,967],339:[1,968]},t(je,He,{336:971,197:Je}),{337:[1,972]},{229:[1,974],452:973},{2:r,3:217,4:a,5:s,76:Ye,131:We,142:he,143:210,144:de,151:pe,155:z,180:me,198:211,199:213,200:212,201:215,208:975,212:Xe,213:216,286:Z,287:ee,288:te,289:ne,298:Fe,416:188,417:Ve,421:Be},{230:[2,689]},{77:[1,976]},t(En,[2,1086],{210:977,3:978,2:r,4:a,5:s}),t(bn,[2,1085]),t(En,[2,182]),{2:r,3:979,4:a,5:s},{211:[1,980]},t(En,[2,186]),{2:r,3:981,4:a,5:s},t(En,[2,190]),{2:r,3:982,4:a,5:s},t(En,[2,194]),{2:r,3:983,4:a,5:s},t(En,[2,197]),{2:r,3:984,4:a,5:s},{2:r,3:985,4:a,5:s},{147:[1,986]},t(aa,[2,171],{81:987,182:[1,988]}),{2:r,3:217,4:a,5:s,131:[1,993],142:he,144:[1,994],151:pe,155:z,180:me,198:989,199:990,200:991,201:992,286:Z,287:ee,288:te,289:ne,298:Fe},{2:r,3:999,4:a,5:s,108:995,109:996,110:997,111:sa},t(An,[2,1051]),t(ia,[2,1042],{90:1e3,181:1001,182:[1,1002]}),t(Qe,[2,1041],{152:1003,178:oa,179:ua,180:ca}),t([2,4,5,10,71,73,75,77,111,114,115,117,121,122,123,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,144,145,147,148,149,151,153,155,161,163,165,167,168,169,170,171,172,174,178,179,180,182,184,186,197,276,277,278,279,280,281,282,283,284,302,306,417,421,599,761],[2,89],{76:[1,1007]}),{118:[1,1008]},t(la,[2,92]),{2:r,3:1009,4:a,5:s},t(la,[2,94]),{2:r,3:166,4:a,5:s,57:163,76:oe,93:1010,110:147,112:151,130:ue,131:ce,136:le,142:he,143:159,144:de,148:fe,151:pe,153:be,155:z,157:165,178:Ee,179:ge,180:me,195:149,199:145,200:153,201:154,253:148,254:144,255:146,256:150,257:152,258:155,259:156,260:157,261:160,262:161,264:ve,265:p,266:Se,267:Te,269:Ae,276:ye,277:Ne,278:Ce,279:Re,280:Oe,281:we,282:Ie,283:xe,284:De,286:Z,287:ee,288:te,289:ne,290:Le,291:ke,292:$e,293:Me,294:Ue,295:_e,298:Fe,299:Pe,308:qe,313:Ge,416:188,417:Ve,421:Be},{2:r,3:166,4:a,5:s,57:163,76:oe,93:1011,110:147,112:151,130:ue,131:ce,136:le,142:he,143:159,144:de,148:fe,151:pe,153:be,155:z,157:165,178:Ee,179:ge,180:me,195:149,199:145,200:153,201:154,253:148,254:144,255:146,256:150,257:152,258:155,259:156,260:157,261:160,262:161,264:ve,265:p,266:Se,267:Te,269:Ae,276:ye,277:Ne,278:Ce,279:Re,280:Oe,281:we,282:Ie,283:xe,284:De,286:Z,287:ee,288:te,289:ne,290:Le,291:ke,292:$e,293:Me,294:Ue,295:_e,298:Fe,299:Pe,308:qe,313:Ge,416:188,417:Ve,421:Be},{2:r,3:730,4:a,5:s,71:yn,75:Nn,76:Cn,111:Rn,113:733,114:Nt,115:Ct,116:1013,117:On,121:wn,122:In,123:xn,124:1012,127:Dn,128:Ln,129:kn,130:$n,131:Mn,132:Un,133:_n,134:Fn,135:Pn,136:qn,137:Gn,138:Vn,139:Bn,140:jn,141:Hn,142:Jn,143:755,144:Yn,145:Wn,147:Xn,148:Kn,149:Qn,151:zn,153:Zn,155:er,157:765,159:766,161:tr,163:nr,165:rr,167:ar,168:sr,169:ir,170:or,171:ur,172:cr,174:lr,184:hr,186:dr,276:ye,277:Ne,278:Ce,279:Re,280:Oe,281:we,282:Ie,283:xe,284:De,416:188,417:Ve,421:Be},{76:[1,1014]},{76:[1,1015]},{76:[1,1016]},{76:[1,1017]},t(la,[2,103]),t(la,[2,104]),t(la,[2,105]),t(la,[2,106]),t(la,[2,107]),t(la,[2,108]),{2:r,3:1018,4:a,5:s},{2:r,3:1019,4:a,5:s,132:[1,1020]},t(la,[2,112]),t(la,[2,113]),t(la,[2,114]),t(la,[2,115]),t(la,[2,116]),t(la,[2,117]),{2:r,3:1021,4:a,5:s,76:cn,112:666,130:ue,131:ce,142:he,151:pe,180:me,195:667,200:669,256:668,290:Le,291:ke,292:$e,298:Fe,416:670,421:Be},{144:[1,1022]},{76:[1,1023]},{144:[1,1024]},t(la,[2,122]),{76:[1,1025]},{2:r,3:1026,4:a,5:s},{76:[1,1027]},{76:[1,1028]},{76:[1,1029]},{76:[1,1030]},{76:[1,1031],163:[1,1032]},{76:[1,1033]},{76:[1,1034]},{76:[1,1035]},{76:[1,1036]},{76:[1,1037]},{76:[1,1038]},{76:[1,1039]},{76:[1,1040]},{76:[1,1041]},{76:[2,1066]},{76:[2,1067]},{2:r,3:242,4:a,5:s,198:1042},{2:r,3:242,4:a,5:s,198:1043},{112:1044,131:ce,292:$e},t(Q,[2,587],{111:[1,1045]}),{2:r,3:242,4:a,5:s,198:1046},{112:1047,131:ce,292:$e},{2:r,3:1048,4:a,5:s},t(Q,[2,686]),t(Q,[2,67]),{2:r,3:234,4:a,5:s,74:1049},{76:[1,1050]},t(Q,[2,667]),t(Q,[2,577]),{2:r,3:999,4:a,5:s,110:1053,142:ha,144:da,146:1051,332:1052,333:1054},{143:1057,144:Ze,416:188,417:Ve,421:Be},t(Q,[2,664]),{2:r,3:166,4:a,5:s,57:163,76:oe,93:1058,110:147,112:151,130:ue,131:ce,136:le,142:he,143:159,144:de,148:fe,151:pe,153:be,155:z,157:165,178:Ee,179:ge,180:me,195:149,199:145,200:153,201:154,253:148,254:144,255:146,256:150,257:152,258:155,259:156,260:157,261:160,262:161,264:ve,265:p,266:Se,267:Te,269:Ae,276:ye,277:Ne,278:Ce,279:Re,280:Oe,281:we,282:Ie,283:xe,284:De,286:Z,287:ee,288:te,289:ne,290:Le,291:ke,292:$e,293:Me,294:Ue,295:_e,298:Fe,299:Pe,308:qe,313:Ge,416:188,417:Ve,421:Be},t(Br,jr,{254:144,199:145,255:146,110:147,253:148,195:149,256:150,112:151,257:152,200:153,201:154,258:155,259:156,260:157,143:159,261:160,262:161,57:163,157:165,3:166,416:188,93:1059,2:r,4:a,5:s,76:oe,130:ue,131:ce,136:le,142:he,144:de,148:fe,151:pe,153:be,155:z,178:Ee,179:ge,180:me,264:ve,265:p,266:Se,267:Te,269:Ae,276:ye,277:Ne,278:Ce,279:Re,280:Oe,281:we,282:Ie,283:xe,284:De,286:Z,287:ee,288:te,289:ne,290:Le,291:ke,292:$e,293:Me,294:Ue,295:_e,298:Fe,299:Pe,308:qe,313:Ge,417:Ve,421:Be}),{112:1060,131:ce,292:$e},{2:r,3:265,4:a,5:s,443:1061,444:tt},{2:r,3:166,4:a,5:s,57:163,76:oe,93:1063,110:147,112:151,130:ue,131:ce,136:le,142:he,143:159,144:de,148:fe,151:pe,153:be,155:z,157:165,178:Ee,179:ge,180:me,195:149,199:145,200:153,201:154,229:mr,253:148,254:144,255:146,256:150,257:152,258:155,259:156,260:157,261:160,262:161,264:ve,265:p,266:Se,267:Te,269:Ae,276:ye,277:Ne,278:Ce,279:Re,280:Oe,281:we,282:Ie,283:xe,284:De,286:Z,287:ee,288:te,289:ne,290:Le,291:ke,292:$e,293:Me,294:Ue,295:_e,298:Fe,299:Pe,308:qe,313:Ge,416:188,417:Ve,421:Be,426:1062,430:vr},t(Q,[2,644]),{113:1065,114:Nt,115:Ct,123:[1,1064]},t(Q,[2,656]),t(Q,[2,657]),{2:r,3:1067,4:a,5:s,76:fa,130:pa,429:1066},{113:804,114:Nt,115:Ct,123:[1,1070],427:1071},t(Q,[2,746],{73:Sr}),{2:r,3:100,4:a,5:s,501:1072},{2:r,3:166,4:a,5:s,57:163,76:oe,93:814,110:147,112:151,130:ue,131:ce,136:le,142:he,143:159,144:de,148:fe,151:pe,153:be,155:z,157:165,173:1073,178:Ee,179:ge,180:me,195:149,199:145,200:153,201:154,252:813,253:148,254:144,255:146,256:150,257:152,258:155,259:156,260:157,261:160,262:161,264:ve,265:p,266:Se,267:Te,269:Ae,276:ye,277:Ne,278:Ce,279:Re,280:Oe,281:we,282:Ie,283:xe,284:De,286:Z,287:ee,288:te,289:ne,290:Le,291:ke,292:$e,293:Me,294:Ue,295:_e,298:Fe,299:Pe,308:qe,313:Ge,416:188,417:Ve,421:Be},{2:r,3:166,4:a,5:s,57:163,76:oe,93:814,110:147,112:151,130:ue,131:ce,136:le,142:he,143:159,144:de,148:fe,151:pe,153:be,155:z,157:165,173:1074,178:Ee,179:ge,180:me,195:149,199:145,200:153,201:154,252:813,253:148,254:144,255:146,256:150,257:152,258:155,259:156,260:157,261:160,262:161,264:ve,265:p,266:Se,267:Te,269:Ae,276:ye,277:Ne,278:Ce,279:Re,280:Oe,281:we,282:Ie,283:xe,284:De,286:Z,287:ee,288:te,289:ne,290:Le,291:ke,292:$e,293:Me,294:Ue,295:_e,298:Fe,299:Pe,308:qe,313:Ge,416:188,417:Ve,421:Be},{2:r,3:166,4:a,5:s,57:163,76:oe,93:814,110:147,112:151,130:ue,131:ce,136:le,142:he,143:159,144:de,148:fe,151:pe,153:be,155:z,157:165,173:1075,178:Ee,179:ge,180:me,195:149,199:145,200:153,201:154,252:813,253:148,254:144,255:146,256:150,257:152,258:155,259:156,260:157,261:160,262:161,264:ve,265:p,266:Se,267:Te,269:Ae,276:ye,277:Ne,278:Ce,279:Re,280:Oe,281:we,282:Ie,283:xe,284:De,286:Z,287:ee,288:te,289:ne,290:Le,291:ke,292:$e,293:Me,294:Ue,295:_e,298:Fe,299:Pe,308:qe,313:Ge,416:188,417:Ve,421:Be},t(yr,[2,150]),t(yr,[2,1081],{73:ba}),t(Ea,[2,272]),t(Ea,[2,279],{113:622,323:634,3:1078,112:1080,2:r,4:a,5:s,75:[1,1077],98:At,111:yt,114:Nt,115:Ct,122:Rt,123:gr,130:[1,1079],131:ce,132:wt,135:It,137:xt,138:Dt,139:Lt,140:kt,141:$t,153:Mt,169:Ut,170:_t,178:Ft,179:Pt,292:$e,307:qt,308:Gt,309:Vt,311:Bt,312:jt,313:Ht,314:Jt,315:Yt,316:Wt,317:Xt,318:Kt,319:Qt,320:zt,321:Zt,322:en,326:tn,327:nn,328:rn,329:an}),t(at,[2,1082],{196:1081,762:[1,1082]}),{130:ue,195:1083},{73:Sr,77:[1,1084]},t(K,[2,11]),{147:[1,1085],189:[1,1086]},{189:[1,1087]},{189:[1,1088]},{189:[1,1089]},t(Q,[2,566],{75:[1,1091],76:[1,1090]}),{2:r,3:166,4:a,5:s,57:163,76:oe,93:259,110:147,112:151,130:ue,131:ce,136:le,142:he,143:159,144:de,148:fe,150:1092,151:pe,153:be,155:z,157:165,178:Ee,179:ge,180:me,195:149,199:145,200:153,201:154,253:148,254:144,255:146,256:150,257:152,258:155,259:156,260:157,261:160,262:161,264:ve,265:p,266:Se,267:Te,269:Ae,276:ye,277:Ne,278:Ce,279:Re,280:Oe,281:we,282:Ie,283:xe,284:De,286:Z,287:ee,288:te,289:ne,290:Le,291:ke,292:$e,293:Me,294:Ue,295:_e,298:Fe,299:Pe,308:qe,313:Ge,416:188,417:Ve,421:Be},t(on,[2,343]),t(Ar,[2,1105]),t(Ar,[2,1102]),t(Ar,[2,1103]),{73:br,77:[1,1093]},{73:br,77:[1,1094]},{73:[1,1095]},{73:[1,1096]},{73:[1,1097]},{73:[1,1098]},t(on,[2,350]),t(Q,[2,571]),{294:[1,1099]},{2:r,3:1100,4:a,5:s,112:1101,131:ce,292:$e},{2:r,3:242,4:a,5:s,198:1102},{229:[1,1103]},{2:r,3:577,4:a,5:s,131:pt,136:bt,142:nt,144:rt,151:Et,428:584,471:1104,472:575,475:576,479:581,490:578,494:580},t(Q,[2,723],{113:622,323:634,98:At,111:yt,114:Nt,115:Ct,122:Rt,123:gr,132:wt,135:It,137:xt,138:Dt,139:Lt,140:kt,141:$t,153:Mt,169:Ut,170:_t,178:Ft,179:Pt,307:qt,308:Gt,309:Vt,311:Bt,312:jt,313:Ht,314:Jt,315:Yt,316:Wt,317:Xt,318:Kt,319:Qt,320:zt,321:Zt,322:en,326:tn,327:nn,328:rn,329:an}),t(Tr,[2,1138],{474:1105,480:1106,75:ga}),t(Or,[2,1137]),{2:r,3:1110,4:a,5:s,131:pt,136:bt,143:1109,144:Ze,151:Et,416:188,417:Ve,421:Be,472:1108,490:578,494:580},{2:r,3:1110,4:a,5:s,131:pt,136:bt,142:nt,144:rt,151:Et,428:584,472:1112,475:1111,479:581,490:578,494:580},{2:r,3:577,4:a,5:s,131:pt,136:bt,142:nt,144:rt,151:Et,428:584,470:1113,471:574,472:575,475:576,479:581,490:578,494:580},t(Dr,[2,1156],{488:1114,131:[1,1115]}),t(Ir,[2,1155]),t(Lr,[2,1162],{492:1116,494:1117,151:Et}),t(Dr,[2,1161]),t(Lr,[2,739]),t(Lr,[2,1165]),t(Ir,[2,742]),t(Ir,[2,743]),t(Lr,[2,741]),t(kr,[2,733]),{2:r,3:242,4:a,5:s,198:1118},{2:r,3:242,4:a,5:s,198:1119},{2:r,3:166,4:a,5:s,57:163,76:oe,93:1120,110:147,112:151,130:ue,131:ce,136:le,142:he,143:159,144:de,148:fe,151:pe,153:be,155:z,157:165,178:Ee,179:ge,180:me,195:149,199:145,200:153,201:154,253:148,254:144,255:146,256:150,257:152,258:155,259:156,260:157,261:160,262:161,264:ve,265:p,266:Se,267:Te,269:Ae,276:ye,277:Ne,278:Ce,279:Re,280:Oe,281:we,282:Ie,283:xe,284:De,286:Z,287:ee,288:te,289:ne,290:Le,291:ke,292:$e,293:Me,294:Ue,295:_e,298:Fe,299:Pe,308:qe,313:Ge,416:188,417:Ve,421:Be},t(ma,[2,1132],{464:1121,112:1122,131:ce,292:$e}),t($r,[2,1131]),{2:r,3:1123,4:a,5:s},{331:va,334:Sa,335:Ta,508:1124},{2:r,3:242,4:a,5:s,198:1128},t(Mr,[2,758]),t(Mr,[2,759]),t(Mr,[2,760]),{128:[1,1129]},t(mt,[2,681]),t(mt,[2,682],{123:[1,1130]}),{4:Gr,7:874,268:1131,383:873,385:Vr},t([2,4,10,53,71,73,75,76,77,88,92,94,97,98,106,111,114,115,117,121,122,123,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,144,145,147,148,149,151,153,155,161,163,165,167,168,169,170,171,172,174,178,179,180,182,184,186,188,197,205,207,221,222,223,224,225,226,227,228,229,231,238,241,242,244,246,265,276,277,278,279,280,281,282,283,284,286,292,293,296,302,304,305,306,307,308,309,310,311,312,313,314,315,316,317,318,319,320,321,322,326,327,328,329,331,334,335,339,343,352,364,365,369,370,392,396,397,400,402,404,405,413,414,415,417,421,431,433,434,436,437,438,439,440,444,445,448,449,461,467,502,504,505,514,599,761],[2,533],{5:[1,1132]}),t([2,5,10,53,71,73,75,77,88,92,94,97,98,106,111,114,115,117,121,122,123,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,144,145,147,148,149,151,153,155,161,163,165,167,168,169,170,171,172,174,178,179,180,182,184,186,188,197,205,207,221,222,223,224,225,226,227,228,229,231,238,241,242,244,246,265,276,277,278,279,280,281,282,283,284,286,292,293,296,302,304,305,306,307,308,309,310,311,312,313,314,315,316,317,318,319,320,321,322,326,327,328,329,331,334,335,339,343,352,364,365,369,370,392,396,397,400,402,404,405,413,414,415,417,421,431,433,434,436,437,438,439,440,444,445,448,449,461,467,502,504,505,514,599,761],[2,530],{4:[1,1134],76:[1,1133]}),{76:[1,1135]},t(Aa,[2,4]),{2:r,3:166,4:a,5:s,57:163,76:oe,93:1136,110:147,112:151,130:ue,131:ce,136:le,142:he,143:159,144:de,148:fe,151:pe,153:be,155:z,157:165,178:Ee,179:ge,180:me,195:149,199:145,200:153,201:154,253:148,254:144,255:146,256:150,257:152,258:155,259:156,260:157,261:160,262:161,264:ve,265:p,266:Se,267:Te,269:Ae,276:ye,277:Ne,278:Ce,279:Re,280:Oe,281:we,282:Ie,283:xe,284:De,286:Z,287:ee,288:te,289:ne,290:Le,291:ke,292:$e,293:Me,294:Ue,295:_e,298:Fe,299:Pe,308:qe,313:Ge,416:188,417:Ve,421:Be},t(Q,[2,579]),t(Cr,[2,559]),{2:r,3:1137,4:a,5:s,112:1138,131:ce,292:$e},t(Q,[2,555],{73:ya}),t(mt,[2,557]),t(Q,[2,604],{73:ya}),t(Q,[2,661]),t(Q,n,{17:5,18:7,19:8,20:9,21:10,22:11,23:12,24:13,25:14,26:15,27:16,28:17,29:18,30:19,31:20,32:21,33:22,34:23,35:24,36:25,37:26,38:27,39:28,40:29,41:30,42:31,43:32,44:33,45:34,46:35,47:36,48:37,49:38,50:39,51:40,52:41,54:43,55:44,56:45,57:46,58:47,59:48,60:49,61:50,62:51,63:52,64:53,65:54,66:55,67:56,68:57,69:58,70:59,78:74,501:95,183:99,3:100,12:1140,2:r,4:a,5:s,53:o,71:u,88:c,123:l,145:h,155:d,188:f,265:p,286:b,331:E,334:g,335:m,343:v,392:S,396:T,397:y,400:N,402:C,404:R,405:O,413:w,414:x,415:D,431:L,433:k,434:$,436:M,437:U,438:_,439:F,440:P,444:q,445:G,448:V,449:B,502:j,504:H,505:J,514:Y}),t(Na,[2,373],{113:622,323:634,114:Nt,115:Ct,122:Rt,132:wt,135:It,137:xt,140:kt,141:$t,178:Ft,179:Pt,308:Gt,312:jt,313:Ht,314:Jt,315:Yt}),t(Xr,[2,374],{113:622,323:634,312:jt}),t(Na,[2,375],{113:622,323:634,114:Nt,115:Ct,122:Rt,132:wt,135:It,137:xt,140:kt,141:$t,178:Ft,179:Pt,308:Gt,312:jt,313:Ht,
314:Jt,315:Yt}),t(Ca,[2,376],{113:622,323:634,114:Nt,115:Ct,122:Rt,132:wt,135:It,137:xt,140:kt,141:$t,178:Ft,179:Pt,308:Gt,310:[1,1141],312:jt,313:Ht,314:Jt,315:Yt}),t(Ca,[2,378],{113:622,323:634,114:Nt,115:Ct,122:Rt,132:wt,135:It,137:xt,140:kt,141:$t,178:Ft,179:Pt,308:Gt,310:[1,1142],312:jt,313:Ht,314:Jt,315:Yt}),t(ze,[2,380],{113:622,323:634}),t(Wr,[2,381],{113:622,323:634,114:Nt,115:Ct,122:Rt,132:wt,135:It,137:xt,179:Pt,308:Gt,312:jt}),t(Wr,[2,382],{113:622,323:634,114:Nt,115:Ct,122:Rt,132:wt,135:It,137:xt,179:Pt,308:Gt,312:jt}),t(Ra,[2,383],{113:622,323:634,114:Nt,115:Ct,122:Rt,135:It,308:Gt,312:jt}),t(Ra,[2,384],{113:622,323:634,114:Nt,115:Ct,122:Rt,135:It,308:Gt,312:jt}),t(Ra,[2,385],{113:622,323:634,114:Nt,115:Ct,122:Rt,135:It,308:Gt,312:jt}),t([2,4,5,10,53,71,73,75,76,77,88,92,94,97,98,106,111,117,121,122,123,127,128,129,130,131,132,133,134,136,137,138,139,140,141,142,144,145,147,148,149,151,153,155,161,163,165,167,168,169,170,171,172,174,178,179,180,182,184,186,188,197,205,207,221,222,223,224,225,226,227,228,231,238,241,242,244,246,265,276,277,278,279,280,281,282,283,284,286,292,296,302,304,305,306,307,309,310,311,313,314,315,316,317,318,319,320,321,322,326,327,328,329,331,334,335,343,392,396,397,400,402,404,405,413,414,415,417,421,431,433,434,436,437,438,439,440,444,445,448,449,461,467,502,504,505,514,599,761],[2,386],{113:622,323:634,114:Nt,115:Ct,135:It,308:Gt,312:jt}),t(Oa,[2,387],{113:622,323:634,114:Nt,115:Ct,122:Rt,132:wt,135:It,137:xt,178:Ft,179:Pt,308:Gt,312:jt,313:Ht}),t(Oa,[2,388],{113:622,323:634,114:Nt,115:Ct,122:Rt,132:wt,135:It,137:xt,178:Ft,179:Pt,308:Gt,312:jt,313:Ht}),t(Oa,[2,389],{113:622,323:634,114:Nt,115:Ct,122:Rt,132:wt,135:It,137:xt,178:Ft,179:Pt,308:Gt,312:jt,313:Ht}),t(Oa,[2,390],{113:622,323:634,114:Nt,115:Ct,122:Rt,132:wt,135:It,137:xt,178:Ft,179:Pt,308:Gt,312:jt,313:Ht}),t(na,[2,391],{76:lt}),t(ze,[2,392]),{2:r,3:166,4:a,5:s,57:163,76:oe,93:1143,110:147,112:151,130:ue,131:ce,136:le,142:he,143:159,144:de,148:fe,151:pe,153:be,155:z,157:165,178:Ee,179:ge,180:me,195:149,199:145,200:153,201:154,253:148,254:144,255:146,256:150,257:152,258:155,259:156,260:157,261:160,262:161,264:ve,265:p,266:Se,267:Te,269:Ae,276:ye,277:Ne,278:Ce,279:Re,280:Oe,281:we,282:Ie,283:xe,284:De,286:Z,287:ee,288:te,289:ne,290:Le,291:ke,292:$e,293:Me,294:Ue,295:_e,298:Fe,299:Pe,308:qe,313:Ge,416:188,417:Ve,421:Be},t(ze,[2,394]),t(na,[2,395],{76:lt}),t(ze,[2,396]),{2:r,3:166,4:a,5:s,57:163,76:oe,93:1144,110:147,112:151,130:ue,131:ce,136:le,142:he,143:159,144:de,148:fe,151:pe,153:be,155:z,157:165,178:Ee,179:ge,180:me,195:149,199:145,200:153,201:154,253:148,254:144,255:146,256:150,257:152,258:155,259:156,260:157,261:160,262:161,264:ve,265:p,266:Se,267:Te,269:Ae,276:ye,277:Ne,278:Ce,279:Re,280:Oe,281:we,282:Ie,283:xe,284:De,286:Z,287:ee,288:te,289:ne,290:Le,291:ke,292:$e,293:Me,294:Ue,295:_e,298:Fe,299:Pe,308:qe,313:Ge,416:188,417:Ve,421:Be},t(ze,[2,398]),t(wa,[2,399],{113:622,323:634,111:yt,114:Nt,115:Ct,122:Rt,132:wt,135:It,137:xt,140:kt,141:$t,178:Ft,179:Pt,307:qt,308:Gt,309:Vt,311:Bt,312:jt,313:Ht,314:Jt,315:Yt,328:rn}),t(wa,[2,400],{113:622,323:634,111:yt,114:Nt,115:Ct,122:Rt,132:wt,135:It,137:xt,140:kt,141:$t,178:Ft,179:Pt,307:qt,308:Gt,309:Vt,311:Bt,312:jt,313:Ht,314:Jt,315:Yt,328:rn}),t(wa,[2,401],{113:622,323:634,111:yt,114:Nt,115:Ct,122:Rt,132:wt,135:It,137:xt,140:kt,141:$t,178:Ft,179:Pt,307:qt,308:Gt,309:Vt,311:Bt,312:jt,313:Ht,314:Jt,315:Yt,328:rn}),t(wa,[2,402],{113:622,323:634,111:yt,114:Nt,115:Ct,122:Rt,132:wt,135:It,137:xt,140:kt,141:$t,178:Ft,179:Pt,307:qt,308:Gt,309:Vt,311:Bt,312:jt,313:Ht,314:Jt,315:Yt,328:rn}),t([2,4,5,10,53,71,88,98,123,138,139,145,153,155,169,170,188,265,286,302,306,316,317,318,319,320,321,322,326,327,329,331,334,335,343,392,396,397,400,402,404,405,413,414,415,431,433,434,436,437,438,439,440,444,445,448,449,502,504,505,514,599,761],Ia,{113:622,323:634,111:yt,114:Nt,115:Ct,122:Rt,132:wt,135:It,137:xt,140:kt,141:$t,178:Ft,179:Pt,307:qt,308:Gt,309:Vt,311:Bt,312:jt,313:Ht,314:Jt,315:Yt,328:rn}),t(wa,[2,404],{113:622,323:634,111:yt,114:Nt,115:Ct,122:Rt,132:wt,135:It,137:xt,140:kt,141:$t,178:Ft,179:Pt,307:qt,308:Gt,309:Vt,311:Bt,312:jt,313:Ht,314:Jt,315:Yt,328:rn}),t(wa,[2,405],{113:622,323:634,111:yt,114:Nt,115:Ct,122:Rt,132:wt,135:It,137:xt,140:kt,141:$t,178:Ft,179:Pt,307:qt,308:Gt,309:Vt,311:Bt,312:jt,313:Ht,314:Jt,315:Yt,328:rn}),t(wa,[2,406],{113:622,323:634,111:yt,114:Nt,115:Ct,122:Rt,132:wt,135:It,137:xt,140:kt,141:$t,178:Ft,179:Pt,307:qt,308:Gt,309:Vt,311:Bt,312:jt,313:Ht,314:Jt,315:Yt,328:rn}),t(wa,[2,407],{113:622,323:634,111:yt,114:Nt,115:Ct,122:Rt,132:wt,135:It,137:xt,140:kt,141:$t,178:Ft,179:Pt,307:qt,308:Gt,309:Vt,311:Bt,312:jt,313:Ht,314:Jt,315:Yt,328:rn}),t(wa,[2,408],{113:622,323:634,111:yt,114:Nt,115:Ct,122:Rt,132:wt,135:It,137:xt,140:kt,141:$t,178:Ft,179:Pt,307:qt,308:Gt,309:Vt,311:Bt,312:jt,313:Ht,314:Jt,315:Yt,328:rn}),{76:[1,1145]},{76:[2,443]},{76:[2,444]},{76:[2,445]},t(xa,[2,411],{113:622,323:634,98:At,111:yt,114:Nt,115:Ct,122:Rt,123:gr,132:wt,135:It,137:xt,138:Dt,139:Lt,140:kt,141:$t,153:Mt,178:Ft,179:Pt,307:qt,308:Gt,309:Vt,311:Bt,312:jt,313:Ht,314:Jt,315:Yt,316:Wt,317:Xt,318:Kt,319:Qt,320:zt,321:Zt,322:en,328:rn}),t([2,4,5,10,53,71,73,75,76,77,88,92,94,97,106,117,121,127,128,129,130,131,133,134,136,142,144,145,147,148,149,151,155,161,163,165,167,168,170,171,172,174,180,182,184,186,188,197,205,207,221,222,223,224,225,226,227,228,231,238,241,242,244,246,265,276,277,278,279,280,281,282,283,284,286,292,296,302,304,305,306,310,329,331,334,335,343,392,396,397,400,402,404,405,413,414,415,417,421,431,433,434,436,437,438,439,440,444,445,448,449,461,467,502,504,505,514,599,761],[2,412],{113:622,323:634,98:At,111:yt,114:Nt,115:Ct,122:Rt,123:gr,132:wt,135:It,137:xt,138:Dt,139:Lt,140:kt,141:$t,153:Mt,169:Ut,178:Ft,179:Pt,307:qt,308:Gt,309:Vt,311:Bt,312:jt,313:Ht,314:Jt,315:Yt,316:Wt,317:Xt,318:Kt,319:Qt,320:zt,321:Zt,322:en,326:tn,327:nn,328:rn}),{2:r,3:166,4:a,5:s,40:1146,57:163,76:oe,77:[1,1148],78:74,88:c,93:259,110:147,112:151,130:ue,131:ce,136:le,142:he,143:159,144:de,148:fe,150:1147,151:pe,153:be,155:z,157:165,178:Ee,179:ge,180:me,183:99,188:f,195:149,199:145,200:153,201:154,253:148,254:144,255:146,256:150,257:152,258:155,259:156,260:157,261:160,262:161,264:ve,265:p,266:Se,267:Te,269:Ae,276:ye,277:Ne,278:Ce,279:Re,280:Oe,281:we,282:Ie,283:xe,284:De,286:Z,287:ee,288:te,289:ne,290:Le,291:ke,292:$e,293:Me,294:Ue,295:_e,298:Fe,299:Pe,308:qe,313:Ge,416:188,417:Ve,421:Be},t(ze,[2,425]),t(ze,[2,427]),t(ze,[2,434]),t(ze,[2,435]),{2:r,3:664,4:a,5:s,76:[1,1149]},{2:r,3:692,4:a,5:s,76:[1,1150],110:929,144:Hr,155:z,199:930,201:1152,286:Z,287:ee,288:te,289:ne,325:1151},t(ze,[2,432]),t(xa,[2,429],{113:622,323:634,98:At,111:yt,114:Nt,115:Ct,122:Rt,123:gr,132:wt,135:It,137:xt,138:Dt,139:Lt,140:kt,141:$t,153:Mt,178:Ft,179:Pt,307:qt,308:Gt,309:Vt,311:Bt,312:jt,313:Ht,314:Jt,315:Yt,316:Wt,317:Xt,318:Kt,319:Qt,320:zt,321:Zt,322:en,328:rn}),t(xa,[2,430],{113:622,323:634,98:At,111:yt,114:Nt,115:Ct,122:Rt,123:gr,132:wt,135:It,137:xt,138:Dt,139:Lt,140:kt,141:$t,153:Mt,178:Ft,179:Pt,307:qt,308:Gt,309:Vt,311:Bt,312:jt,313:Ht,314:Jt,315:Yt,316:Wt,317:Xt,318:Kt,319:Qt,320:zt,321:Zt,322:en,328:rn}),t([2,4,5,10,53,71,73,75,76,77,88,92,94,97,98,106,117,121,123,127,128,129,130,131,133,134,136,138,139,142,144,145,147,148,149,151,153,155,161,163,165,167,168,169,170,171,172,174,180,182,184,186,188,197,205,207,221,222,223,224,225,226,227,228,231,238,241,242,244,246,265,276,277,278,279,280,281,282,283,284,286,292,296,302,304,305,306,310,316,317,318,319,320,321,322,326,327,328,329,331,334,335,343,392,396,397,400,402,404,405,413,414,415,417,421,431,433,434,436,437,438,439,440,444,445,448,449,461,467,502,504,505,514,599,761],[2,431],{113:622,323:634,111:yt,114:Nt,115:Ct,122:Rt,132:wt,135:It,137:xt,140:kt,141:$t,178:Ft,179:Pt,307:qt,308:Gt,309:Vt,311:Bt,312:jt,313:Ht,314:Jt,315:Yt}),t(ze,[2,433]),t(ze,[2,305]),t(ze,[2,306]),t(ze,[2,307]),t(ze,[2,418]),{73:br,77:[1,1153]},{2:r,3:166,4:a,5:s,57:163,76:oe,93:1154,110:147,112:151,130:ue,131:ce,136:le,142:he,143:159,144:de,148:fe,151:pe,153:be,155:z,157:165,178:Ee,179:ge,180:me,195:149,199:145,200:153,201:154,253:148,254:144,255:146,256:150,257:152,258:155,259:156,260:157,261:160,262:161,264:ve,265:p,266:Se,267:Te,269:Ae,276:ye,277:Ne,278:Ce,279:Re,280:Oe,281:we,282:Ie,283:xe,284:De,286:Z,287:ee,288:te,289:ne,290:Le,291:ke,292:$e,293:Me,294:Ue,295:_e,298:Fe,299:Pe,308:qe,313:Ge,416:188,417:Ve,421:Be},{2:r,3:166,4:a,5:s,57:163,76:oe,93:1155,110:147,112:151,130:ue,131:ce,136:le,142:he,143:159,144:de,148:fe,151:pe,153:be,155:z,157:165,178:Ee,179:ge,180:me,195:149,199:145,200:153,201:154,253:148,254:144,255:146,256:150,257:152,258:155,259:156,260:157,261:160,262:161,264:ve,265:p,266:Se,267:Te,269:Ae,276:ye,277:Ne,278:Ce,279:Re,280:Oe,281:we,282:Ie,283:xe,284:De,286:Z,287:ee,288:te,289:ne,290:Le,291:ke,292:$e,293:Me,294:Ue,295:_e,298:Fe,299:Pe,308:qe,313:Ge,416:188,417:Ve,421:Be},t(ze,Da),t(La,[2,285]),t(ze,[2,281]),{77:[1,1157],98:At,111:yt,113:622,114:Nt,115:Ct,122:Rt,123:gr,132:wt,135:It,137:xt,138:Dt,139:Lt,140:kt,141:$t,153:Mt,169:Ut,170:_t,178:Ft,179:Pt,307:qt,308:Gt,309:Vt,311:Bt,312:jt,313:Ht,314:Jt,315:Yt,316:Wt,317:Xt,318:Kt,319:Qt,320:zt,321:Zt,322:en,323:634,326:tn,327:nn,328:rn,329:an},{77:[1,1158]},{301:1159,302:Kr,303:952,304:ln,306:Qr},{302:[1,1160]},t(zr,[2,368]),{2:r,3:166,4:a,5:s,57:163,76:oe,93:1161,110:147,112:151,130:ue,131:ce,136:le,142:he,143:159,144:de,148:fe,151:pe,153:be,155:z,157:165,178:Ee,179:ge,180:me,195:149,199:145,200:153,201:154,253:148,254:144,255:146,256:150,257:152,258:155,259:156,260:157,261:160,262:161,264:ve,265:p,266:Se,267:Te,269:Ae,276:ye,277:Ne,278:Ce,279:Re,280:Oe,281:we,282:Ie,283:xe,284:De,286:Z,287:ee,288:te,289:ne,290:Le,291:ke,292:$e,293:Me,294:Ue,295:_e,298:Fe,299:Pe,308:qe,313:Ge,416:188,417:Ve,421:Be},{98:At,111:yt,113:622,114:Nt,115:Ct,122:Rt,123:gr,132:wt,135:It,137:xt,138:Dt,139:Lt,140:kt,141:$t,153:Mt,169:Ut,170:_t,178:Ft,179:Pt,305:[1,1162],307:qt,308:Gt,309:Vt,311:Bt,312:jt,313:Ht,314:Jt,315:Yt,316:Wt,317:Xt,318:Kt,319:Qt,320:zt,321:Zt,322:en,323:634,326:tn,327:nn,328:rn,329:an},{75:[1,1163],98:At,111:yt,113:622,114:Nt,115:Ct,122:Rt,123:gr,132:wt,135:It,137:xt,138:Dt,139:Lt,140:kt,141:$t,153:Mt,169:Ut,170:_t,178:Ft,179:Pt,307:qt,308:Gt,309:Vt,311:Bt,312:jt,313:Ht,314:Jt,315:Yt,316:Wt,317:Xt,318:Kt,319:Qt,320:zt,321:Zt,322:en,323:634,326:tn,327:nn,328:rn,329:an},{73:[1,1164]},t(hn,[2,633]),{2:r,3:692,4:a,5:s,76:dn,110:687,112:685,130:ue,131:ce,142:he,143:682,144:Ze,151:pe,155:z,180:me,195:684,199:690,200:689,256:686,257:688,286:Z,287:ee,288:te,289:ne,290:Le,291:ke,292:$e,293:Me,296:[1,1165],298:Fe,416:188,417:Ve,419:1166,420:683,421:Be},{77:[1,1167],98:At,111:yt,113:622,114:Nt,115:Ct,122:Rt,123:gr,132:wt,135:It,137:xt,138:Dt,139:Lt,140:kt,141:$t,153:Mt,169:Ut,170:_t,178:Ft,179:Pt,307:qt,308:Gt,309:Vt,311:Bt,312:jt,313:Ht,314:Jt,315:Yt,316:Wt,317:Xt,318:Kt,319:Qt,320:zt,321:Zt,322:en,323:634,326:tn,327:nn,328:rn,329:an},{2:r,3:1168,4:a,5:s,148:Yr},t(ze,[2,361]),t(hn,[2,630]),{2:r,3:701,4:a,5:s,130:fn,131:pn,423:[1,1169],425:1170},{2:r,3:692,4:a,5:s,76:dn,110:687,112:685,130:ue,131:ce,142:he,143:682,144:Ze,151:pe,155:z,180:me,195:684,199:690,200:689,256:686,257:688,286:Z,287:ee,288:te,289:ne,290:Le,291:ke,292:$e,293:Me,298:Fe,416:188,417:Ve,419:1171,420:683,421:Be},{2:r,3:692,4:a,5:s,76:dn,110:687,112:685,130:ue,131:ce,142:he,143:682,144:Ze,151:pe,155:z,180:me,195:684,199:690,200:689,256:686,257:688,286:Z,287:ee,288:te,289:ne,290:Le,291:ke,292:$e,293:Me,298:Fe,416:188,417:Ve,419:1172,420:683,421:Be},{2:r,3:692,4:a,5:s,76:dn,110:687,112:685,130:ue,131:ce,142:he,143:682,144:Ze,151:pe,155:z,180:me,195:684,199:690,200:689,256:686,257:688,286:Z,287:ee,288:te,289:ne,290:Le,291:ke,292:$e,293:Me,298:Fe,416:188,417:Ve,419:1173,420:683,421:Be},{76:ka,142:he,143:1176,144:Ze,151:pe,180:me,200:1177,298:Fe,338:1174,416:188,417:Ve,421:Be},{337:[1,1178]},{2:r,3:999,4:a,5:s,99:1179,110:1180},t($a,[2,459]),{2:r,3:242,4:a,5:s,198:1181},{76:ka,142:he,143:1176,144:Ze,151:pe,180:me,200:1177,298:Fe,338:1182,416:188,417:Ve,421:Be},{304:Ma,453:1183,455:1184,456:1185},{2:r,3:166,4:a,5:s,57:163,76:oe,93:1187,110:147,112:151,130:ue,131:ce,136:le,142:he,143:159,144:de,148:fe,151:pe,153:be,155:z,157:165,178:Ee,179:ge,180:me,195:149,199:145,200:153,201:154,253:148,254:144,255:146,256:150,257:152,258:155,259:156,260:157,261:160,262:161,264:ve,265:p,266:Se,267:Te,269:Ae,276:ye,277:Ne,278:Ce,279:Re,280:Oe,281:we,282:Ie,283:xe,284:De,286:Z,287:ee,288:te,289:ne,290:Le,291:ke,292:$e,293:Me,294:Ue,295:_e,298:Fe,299:Pe,308:qe,313:Ge,416:188,417:Ve,421:Be},{229:[2,690]},t(En,[2,180],{3:1188,2:r,4:a,5:s,75:[1,1189]}),t(En,[2,181]),t(En,[2,1087]),t(En,[2,183]),t(En,[2,185]),t(En,[2,187]),t(En,[2,191]),t(En,[2,195]),t(En,[2,198]),t([2,4,5,10,53,71,73,75,76,77,88,92,94,97,117,123,127,145,147,153,155,161,167,168,182,186,188,205,207,221,222,223,224,225,226,227,228,229,230,231,244,246,265,286,293,302,306,331,334,335,337,339,343,352,364,365,369,370,392,396,397,398,399,400,402,404,405,413,414,415,431,433,434,436,437,438,439,440,444,445,448,449,502,504,505,511,512,513,514,599,761],[2,200]),{2:r,3:1190,4:a,5:s},t(Ua,[2,1038],{82:1191,91:1192,92:[1,1193],97:[1,1194]}),{2:r,3:217,4:a,5:s,76:[1,1196],131:We,142:he,143:210,144:de,151:pe,155:z,180:me,198:211,199:213,200:212,201:215,202:1195,208:1197,212:Xe,213:216,286:Z,287:ee,288:te,289:ne,298:Fe,416:188,417:Ve,421:Be},t(vn,[2,163]),t(vn,[2,164]),t(vn,[2,165]),t(vn,[2,166]),t(vn,[2,167]),{2:r,3:664,4:a,5:s},t(Ke,[2,82],{73:[1,1198]}),t(_a,[2,84]),t(_a,[2,85]),{112:1199,131:ce,292:$e},t([10,71,73,77,92,97,117,123,127,161,167,168,182,197,205,207,221,222,223,224,225,226,227,228,231,244,246,302,306,599,761],sn,{115:ta}),t(ia,[2,72]),t(ia,[2,1043]),{2:r,3:166,4:a,5:s,57:163,76:oe,93:1200,110:147,112:151,130:ue,131:ce,136:le,142:he,143:159,144:de,148:fe,151:pe,153:be,155:z,157:165,178:Ee,179:ge,180:me,195:149,199:145,200:153,201:154,253:148,254:144,255:146,256:150,257:152,258:155,259:156,260:157,261:160,262:161,264:ve,265:p,266:Se,267:Te,269:Ae,276:ye,277:Ne,278:Ce,279:Re,280:Oe,281:we,282:Ie,283:xe,284:De,286:Z,287:ee,288:te,289:ne,290:Le,291:ke,292:$e,293:Me,294:Ue,295:_e,298:Fe,299:Pe,308:qe,313:Ge,416:188,417:Ve,421:Be},t(la,[2,125]),t(la,[2,143]),t(la,[2,144]),t(la,[2,145]),{2:r,3:166,4:a,5:s,57:163,76:oe,77:[2,1058],93:259,110:147,112:151,126:1201,130:ue,131:ce,136:le,142:he,143:159,144:de,148:fe,150:1202,151:pe,153:be,155:z,157:165,178:Ee,179:ge,180:me,195:149,199:145,200:153,201:154,253:148,254:144,255:146,256:150,257:152,258:155,259:156,260:157,261:160,262:161,264:ve,265:p,266:Se,267:Te,269:Ae,276:ye,277:Ne,278:Ce,279:Re,280:Oe,281:we,282:Ie,283:xe,284:De,286:Z,287:ee,288:te,289:ne,290:Le,291:ke,292:$e,293:Me,294:Ue,295:_e,298:Fe,299:Pe,308:qe,313:Ge,416:188,417:Ve,421:Be},{76:[1,1203]},t(la,[2,93]),t([2,4,5,10,71,73,75,76,77,117,121,123,127,128,129,130,131,133,134,136,138,139,142,144,145,147,148,149,151,153,155,161,163,165,167,168,169,170,171,172,174,180,182,184,186,197,276,277,278,279,280,281,282,283,284,302,306,417,421,599,761],[2,95],{113:622,323:634,98:At,111:yt,114:Nt,115:Ct,122:Rt,132:wt,135:It,137:xt,140:kt,141:$t,178:Ft,179:Pt,307:qt,308:Gt,309:Vt,311:Bt,312:jt,313:Ht,314:Jt,315:Yt,316:Wt,317:Xt,318:Kt,319:Qt,320:zt,321:Zt,322:en,326:tn,327:nn,328:rn,329:an}),t([2,4,5,10,71,73,75,76,77,111,117,121,123,127,128,129,130,131,133,134,136,138,139,142,144,145,147,148,149,151,153,155,161,163,165,167,168,169,170,171,172,174,180,182,184,186,197,276,277,278,279,280,281,282,283,284,302,306,417,421,599,761],[2,96],{113:622,323:634,98:At,114:Nt,115:Ct,122:Rt,132:wt,135:It,137:xt,140:kt,141:$t,178:Ft,179:Pt,307:qt,308:Gt,309:Vt,311:Bt,312:jt,313:Ht,314:Jt,315:Yt,316:Wt,317:Xt,318:Kt,319:Qt,320:zt,321:Zt,322:en,326:tn,327:nn,328:rn,329:an}),{2:r,3:730,4:a,5:s,71:yn,75:Nn,76:Cn,77:[1,1204],111:Rn,113:733,114:Nt,115:Ct,116:1205,117:On,121:wn,122:In,123:xn,127:Dn,128:Ln,129:kn,130:$n,131:Mn,132:Un,133:_n,134:Fn,135:Pn,136:qn,137:Gn,138:Vn,139:Bn,140:jn,141:Hn,142:Jn,143:755,144:Yn,145:Wn,147:Xn,148:Kn,149:Qn,151:zn,153:Zn,155:er,157:765,159:766,161:tr,163:nr,165:rr,167:ar,168:sr,169:ir,170:or,171:ur,172:cr,174:lr,184:hr,186:dr,276:ye,277:Ne,278:Ce,279:Re,280:Oe,281:we,282:Ie,283:xe,284:De,416:188,417:Ve,421:Be},t(Fa,[2,1054],{152:1003,178:oa,179:ua,180:ca}),{2:r,3:730,4:a,5:s,71:yn,75:Nn,76:Cn,111:Rn,113:733,114:Nt,115:Ct,116:1207,117:On,121:wn,122:In,123:xn,125:1206,127:Dn,128:Ln,129:kn,130:$n,131:Mn,132:Un,133:_n,134:Fn,135:Pn,136:qn,137:Gn,138:Vn,139:Bn,140:jn,141:Hn,142:Jn,143:755,144:Yn,145:Wn,147:Xn,148:Kn,149:Qn,151:zn,153:Zn,155:er,157:765,159:766,161:tr,163:nr,165:rr,167:ar,168:sr,169:ir,170:or,171:ur,172:cr,174:lr,184:hr,186:dr,276:ye,277:Ne,278:Ce,279:Re,280:Oe,281:we,282:Ie,283:xe,284:De,416:188,417:Ve,421:Be},{2:r,3:166,4:a,5:s,57:163,76:oe,93:1208,110:147,112:151,130:ue,131:ce,136:le,142:he,143:159,144:de,148:fe,151:pe,153:be,155:z,157:165,178:Ee,179:ge,180:me,195:149,199:145,200:153,201:154,253:148,254:144,255:146,256:150,257:152,258:155,259:156,260:157,261:160,262:161,264:ve,265:p,266:Se,267:Te,269:Ae,276:ye,277:Ne,278:Ce,279:Re,280:Oe,281:we,282:Ie,283:xe,284:De,286:Z,287:ee,288:te,289:ne,290:Le,291:ke,292:$e,293:Me,294:Ue,295:_e,298:Fe,299:Pe,308:qe,313:Ge,416:188,417:Ve,421:Be},{2:r,3:166,4:a,5:s,57:163,76:oe,93:1209,110:147,112:151,130:ue,131:ce,136:le,142:he,143:159,144:de,148:fe,151:pe,153:be,155:z,157:165,178:Ee,179:ge,180:me,195:149,199:145,200:153,201:154,253:148,254:144,255:146,256:150,257:152,258:155,259:156,260:157,261:160,262:161,264:ve,265:p,266:Se,267:Te,269:Ae,276:ye,277:Ne,278:Ce,279:Re,280:Oe,281:we,282:Ie,283:xe,284:De,286:Z,287:ee,288:te,289:ne,290:Le,291:ke,292:$e,293:Me,294:Ue,295:_e,298:Fe,299:Pe,308:qe,313:Ge,416:188,417:Ve,421:Be},{2:r,3:1210,4:a,5:s},t(la,[2,109]),t(la,[2,110]),t(la,[2,111]),t(la,[2,118]),{2:r,3:1211,4:a,5:s},{2:r,3:999,4:a,5:s,110:1053,142:ha,144:da,146:1212,332:1052,333:1054},{2:r,3:1213,4:a,5:s},{2:r,3:166,4:a,5:s,57:163,76:oe,93:259,110:147,112:151,130:ue,131:ce,136:le,142:he,143:159,144:de,148:fe,150:1214,151:pe,153:be,155:z,157:165,178:Ee,179:ge,180:me,195:149,199:145,200:153,201:154,253:148,254:144,255:146,256:150,257:152,258:155,259:156,260:157,261:160,262:161,264:ve,265:p,266:Se,267:Te,269:Ae,276:ye,277:Ne,278:Ce,279:Re,280:Oe,281:we,282:Ie,283:xe,284:De,286:Z,287:ee,288:te,289:ne,290:Le,291:ke,292:$e,293:Me,294:Ue,295:_e,298:Fe,299:Pe,308:qe,313:Ge,416:188,417:Ve,421:Be},t(la,[2,124]),t(Fa,[2,1060],{154:1215}),t(Fa,[2,1062],{156:1216}),t(Fa,[2,1064],{158:1217}),t(Fa,[2,1068],{160:1218}),t(Pa,qa,{162:1219,177:1220}),{76:[1,1221]},t(Fa,[2,1070],{164:1222}),t(Fa,[2,1072],{166:1223}),t(Pa,qa,{177:1220,162:1224}),t(Pa,qa,{177:1220,162:1225}),t(Pa,qa,{177:1220,162:1226}),t(Pa,qa,{177:1220,162:1227}),{2:r,3:730,4:a,5:s,71:yn,75:Nn,76:Cn,111:Rn,113:733,114:Nt,115:Ct,116:1228,117:On,121:wn,122:In,123:xn,127:Dn,128:Ln,129:kn,130:$n,131:Mn,132:Un,133:_n,134:Fn,135:Pn,136:qn,137:Gn,138:Vn,139:Bn,140:jn,141:Hn,142:Jn,143:755,144:Yn,145:Wn,147:Xn,148:Kn,149:Qn,151:zn,153:Zn,155:er,157:765,159:766,161:tr,163:nr,165:rr,167:ar,168:sr,169:ir,170:or,171:ur,172:cr,174:lr,184:hr,186:dr,276:ye,277:Ne,278:Ce,279:Re,280:Oe,281:we,282:Ie,283:xe,284:De,416:188,417:Ve,421:Be},{2:r,3:166,4:a,5:s,57:163,76:oe,93:814,110:147,112:151,130:ue,131:ce,136:le,142:he,143:159,144:de,148:fe,151:pe,153:be,155:z,157:165,173:1229,178:Ee,179:ge,180:me,195:149,199:145,200:153,201:154,252:813,253:148,254:144,255:146,256:150,257:152,258:155,259:156,260:157,261:160,262:161,264:ve,265:p,266:Se,267:Te,269:Ae,276:ye,277:Ne,278:Ce,279:Re,280:Oe,281:we,282:Ie,283:xe,284:De,286:Z,287:ee,288:te,289:ne,290:Le,291:ke,292:$e,293:Me,294:Ue,295:_e,298:Fe,299:Pe,308:qe,313:Ge,416:188,417:Ve,421:Be},t(Ga,[2,1074],{175:1230}),t(Q,[2,597],{182:[1,1231]}),t(Q,[2,593],{182:[1,1232]}),t(Q,[2,586]),{112:1233,131:ce,292:$e},t(Q,[2,595],{182:[1,1234]}),t(Q,[2,590]),t(Q,[2,591],{111:[1,1235]}),t(fr,[2,68]),{40:1236,78:74,88:c,183:99,188:f},t(Q,[2,447],{73:Va,127:[1,1237]}),t(Ba,[2,448]),{123:[1,1239]},{2:r,3:1240,4:a,5:s},t(je,[2,1108]),t(je,[2,1109]),t(Q,[2,611]),t(Er,[2,352],{113:622,323:634,98:At,111:yt,114:Nt,115:Ct,122:Rt,123:gr,132:wt,135:It,137:xt,138:Dt,139:Lt,140:kt,141:$t,153:Mt,169:Ut,170:_t,178:Ft,179:Pt,307:qt,308:Gt,309:Vt,311:Bt,312:jt,313:Ht,314:Jt,315:Yt,316:Wt,317:Xt,318:Kt,319:Qt,320:zt,321:Zt,322:en,326:tn,327:nn,328:rn,329:an}),t(wa,Ia,{113:622,323:634,111:yt,114:Nt,115:Ct,122:Rt,132:wt,135:It,137:xt,140:kt,141:$t,178:Ft,179:Pt,307:qt,308:Gt,309:Vt,311:Bt,312:jt,313:Ht,314:Jt,315:Yt,328:rn}),t(mt,[2,675]),t(mt,[2,677]),t(Q,[2,643]),t(Q,[2,645],{113:622,323:634,98:At,111:yt,114:Nt,115:Ct,122:Rt,123:gr,132:wt,135:It,137:xt,138:Dt,139:Lt,140:kt,141:$t,153:Mt,169:Ut,170:_t,178:Ft,179:Pt,307:qt,308:Gt,309:Vt,311:Bt,312:jt,313:Ht,314:Jt,315:Yt,316:Wt,317:Xt,318:Kt,319:Qt,320:zt,321:Zt,322:en,326:tn,327:nn,328:rn,329:an}),{2:r,3:166,4:a,5:s,57:163,76:oe,93:1241,110:147,112:151,130:ue,131:ce,136:le,142:he,143:159,144:de,148:fe,151:pe,153:be,155:z,157:165,178:Ee,179:ge,180:me,195:149,199:145,200:153,201:154,253:148,254:144,255:146,256:150,257:152,258:155,259:156,260:157,261:160,262:161,264:ve,265:p,266:Se,267:Te,269:Ae,276:ye,277:Ne,278:Ce,279:Re,280:Oe,281:we,282:Ie,283:xe,284:De,286:Z,287:ee,288:te,289:ne,290:Le,291:ke,292:$e,293:Me,294:Ue,295:_e,298:Fe,299:Pe,308:qe,313:Ge,416:188,417:Ve,421:Be},{2:r,3:1067,4:a,5:s,76:fa,130:pa,429:1242},t(ja,[2,652]),t(ja,[2,653]),t(ja,[2,654]),{2:r,3:166,4:a,5:s,57:163,76:oe,93:1243,110:147,112:151,130:ue,131:ce,136:le,142:he,143:159,144:de,148:fe,151:pe,153:be,155:z,157:165,178:Ee,179:ge,180:me,195:149,199:145,200:153,201:154,253:148,254:144,255:146,256:150,257:152,258:155,259:156,260:157,261:160,262:161,264:ve,265:p,266:Se,267:Te,269:Ae,276:ye,277:Ne,278:Ce,279:Re,280:Oe,281:we,282:Ie,283:xe,284:De,286:Z,287:ee,288:te,289:ne,290:Le,291:ke,292:$e,293:Me,294:Ue,295:_e,298:Fe,299:Pe,308:qe,313:Ge,416:188,417:Ve,421:Be},{2:r,3:166,4:a,5:s,57:163,76:oe,93:1244,110:147,112:151,130:ue,131:ce,136:le,142:he,143:159,144:de,148:fe,151:pe,153:be,155:z,157:165,178:Ee,179:ge,180:me,195:149,199:145,200:153,201:154,253:148,254:144,255:146,256:150,257:152,258:155,259:156,260:157,261:160,262:161,264:ve,265:p,266:Se,267:Te,269:Ae,276:ye,277:Ne,278:Ce,279:Re,280:Oe,281:we,282:Ie,283:xe,284:De,286:Z,287:ee,288:te,289:ne,290:Le,291:ke,292:$e,293:Me,294:Ue,295:_e,298:Fe,299:Pe,308:qe,313:Ge,416:188,417:Ve,421:Be},{113:1065,114:Nt,115:Ct,123:[1,1245]},t(Tr,[2,748]),t(yr,[2,147],{73:ba}),t(yr,[2,148],{73:ba}),t(yr,[2,149],{73:ba}),{2:r,3:166,4:a,5:s,57:163,76:oe,93:814,110:147,112:151,130:ue,131:ce,136:le,142:he,143:159,144:de,148:fe,151:pe,153:be,155:z,157:165,178:Ee,179:ge,180:me,195:149,199:145,200:153,201:154,252:1246,253:148,254:144,255:146,256:150,257:152,258:155,259:156,260:157,261:160,262:161,264:ve,265:p,266:Se,267:Te,269:Ae,276:ye,277:Ne,278:Ce,279:Re,280:Oe,281:we,282:Ie,283:xe,284:De,286:Z,287:ee,288:te,289:ne,290:Le,291:ke,292:$e,293:Me,294:Ue,295:_e,298:Fe,299:Pe,308:qe,313:Ge,416:188,417:Ve,421:Be},{2:r,3:1247,4:a,5:s,112:1249,130:[1,1248],131:ce,292:$e},t(Ea,[2,274]),t(Ea,[2,276]),t(Ea,[2,278]),t(at,[2,159]),t(at,[2,1083]),{77:[1,1250]},t(ot,[2,751]),{2:r,3:1251,4:a,5:s},{2:r,3:1252,4:a,5:s},{2:r,3:1254,4:a,5:s,380:1253},{2:r,3:1254,4:a,5:s,380:1255},{2:r,3:1256,4:a,5:s},{2:r,3:166,4:a,5:s,57:163,76:oe,93:259,110:147,112:151,130:ue,131:ce,136:le,142:he,143:159,144:de,148:fe,150:1257,151:pe,153:be,155:z,157:165,178:Ee,179:ge,180:me,195:149,199:145,200:153,201:154,253:148,254:144,255:146,256:150,257:152,258:155,259:156,260:157,261:160,262:161,264:ve,265:p,266:Se,267:Te,269:Ae,276:ye,277:Ne,278:Ce,279:Re,280:Oe,281:we,282:Ie,283:xe,284:De,286:Z,287:ee,288:te,289:ne,290:Le,291:ke,292:$e,293:Me,294:Ue,295:_e,298:Fe,299:Pe,308:qe,313:Ge,416:188,417:Ve,421:Be},{2:r,3:1258,4:a,5:s},{73:br,77:[1,1259]},t(on,[2,344]),t(on,[2,345]),{2:r,3:166,4:a,5:s,57:163,76:oe,93:1260,110:147,112:151,130:ue,131:ce,136:le,142:he,143:159,144:de,148:fe,151:pe,153:be,155:z,157:165,178:Ee,179:ge,180:me,195:149,199:145,200:153,201:154,253:148,254:144,255:146,256:150,257:152,258:155,259:156,260:157,261:160,262:161,264:ve,265:p,266:Se,267:Te,269:Ae,276:ye,277:Ne,278:Ce,279:Re,280:Oe,281:we,282:Ie,283:xe,284:De,286:Z,287:ee,288:te,289:ne,290:Le,291:ke,292:$e,293:Me,294:Ue,295:_e,298:Fe,299:Pe,308:qe,313:Ge,416:188,417:Ve,421:Be},{2:r,3:166,4:a,5:s,57:163,76:oe,93:1261,110:147,112:151,130:ue,131:ce,136:le,142:he,143:159,144:de,148:fe,151:pe,153:be,155:z,157:165,178:Ee,179:ge,180:me,195:149,199:145,200:153,201:154,253:148,254:144,255:146,256:150,257:152,258:155,259:156,260:157,261:160,262:161,264:ve,265:p,266:Se,267:Te,269:Ae,276:ye,277:Ne,278:Ce,279:Re,280:Oe,281:we,282:Ie,283:xe,284:De,286:Z,287:ee,288:te,289:ne,290:Le,291:ke,292:$e,293:Me,294:Ue,295:_e,298:Fe,299:Pe,308:qe,313:Ge,416:188,417:Ve,421:Be},{2:r,3:166,4:a,5:s,57:163,76:oe,93:1262,110:147,112:151,130:ue,131:ce,136:le,142:he,143:159,144:de,148:fe,151:pe,153:be,155:z,157:165,178:Ee,179:ge,180:me,195:149,199:145,200:153,201:154,253:148,254:144,255:146,256:150,257:152,258:155,259:156,260:157,261:160,262:161,264:ve,265:p,266:Se,267:Te,269:Ae,276:ye,277:Ne,278:Ce,279:Re,280:Oe,281:we,282:Ie,283:xe,284:De,286:Z,287:ee,288:te,289:ne,290:Le,291:ke,292:$e,293:Me,294:Ue,295:_e,298:Fe,299:Pe,308:qe,313:Ge,416:188,417:Ve,421:Be},{2:r,3:166,4:a,5:s,57:163,76:oe,93:1263,110:147,112:151,130:ue,131:ce,136:le,142:he,143:159,144:de,148:fe,151:pe,153:be,155:z,157:165,178:Ee,179:ge,180:me,195:149,199:145,200:153,201:154,253:148,254:144,255:146,256:150,257:152,258:155,259:156,260:157,261:160,262:161,264:ve,265:p,266:Se,267:Te,269:Ae,276:ye,277:Ne,278:Ce,279:Re,280:Oe,281:we,282:Ie,283:xe,284:De,286:Z,287:ee,288:te,289:ne,290:Le,291:ke,292:$e,293:Me,294:Ue,295:_e,298:Fe,299:Pe,308:qe,313:Ge,416:188,417:Ve,421:Be},t(Cr,[2,495]),t(Q,Ha,{403:1264,75:Ja,76:[1,1265]}),t(Q,Ha,{403:1267,75:Ja}),{76:[1,1268]},{2:r,3:242,4:a,5:s,198:1269},t(Tr,[2,724]),t(Tr,[2,726]),t(Tr,[2,1139]),{142:nt,144:rt,428:1270},t(Ya,[2,1140],{416:188,476:1271,143:1272,144:Ze,417:Ve,421:Be}),{75:ga,138:[2,1144],478:1273,480:1274},t([10,73,75,77,131,138,144,151,302,306,417,421,599,761],xr,{487:848,490:849,136:bt}),t(Tr,[2,729]),t(Tr,wr),{73:Rr,77:[1,1275]},t(Lr,[2,1158],{489:1276,494:1277,151:Et}),t(Dr,[2,1157]),t(Lr,[2,738]),t(Lr,[2,1163]),t(Q,[2,481],{76:[1,1278]}),{75:[1,1280],76:[1,1279]},{98:At,111:yt,113:622,114:Nt,115:Ct,122:Rt,123:gr,132:wt,135:It,137:xt,138:Dt,139:Lt,140:kt,141:$t,147:[1,1281],153:Mt,169:Ut,170:_t,178:Ft,179:Pt,307:qt,308:Gt,309:Vt,311:Bt,312:jt,313:Ht,314:Jt,315:Yt,316:Wt,317:Xt,318:Kt,319:Qt,320:zt,321:Zt,322:en,323:634,326:tn,327:nn,328:rn,329:an},t($a,Wa,{78:74,183:99,465:1282,40:1285,88:c,145:Xa,188:f,467:Ka}),t(ma,[2,1133]),t($r,[2,716]),{229:[1,1286]},t(Qa,[2,762]),t(Qa,[2,763]),t(Qa,[2,764]),t(Mr,Ur,{507:1287,94:_r,511:Fr,512:Pr,513:qr}),t(Mr,[2,761]),{2:r,3:166,4:a,5:s,57:163,76:oe,93:1288,110:147,112:151,130:ue,131:ce,136:le,142:he,143:159,144:de,148:fe,151:pe,153:be,155:z,157:165,178:Ee,179:ge,180:me,195:149,199:145,200:153,201:154,253:148,254:144,255:146,256:150,257:152,258:155,259:156,260:157,261:160,262:161,264:ve,265:p,266:Se,267:Te,269:Ae,276:ye,277:Ne,278:Ce,279:Re,280:Oe,281:we,282:Ie,283:xe,284:De,286:Z,287:ee,288:te,289:ne,290:Le,291:ke,292:$e,293:Me,294:Ue,295:_e,298:Fe,299:Pe,308:qe,313:Ge,416:188,417:Ve,421:Be},t(mt,[2,683],{123:[1,1289]}),t(Aa,[2,532]),{130:[1,1291],384:1290,386:[1,1292]},t(Aa,[2,5]),{2:r,3:166,4:a,5:s,57:163,76:oe,93:1294,110:147,112:151,130:ue,131:ce,136:le,142:he,143:159,144:de,148:fe,151:pe,153:be,155:z,157:165,178:Ee,179:ge,180:me,195:149,199:145,200:153,201:154,253:148,254:144,255:146,256:150,257:152,258:155,259:156,260:157,261:160,262:161,264:ve,265:p,266:Se,267:Te,269:Ae,276:ye,277:Ne,278:Ce,279:Re,280:Oe,281:we,282:Ie,283:xe,284:De,286:Z,287:ee,288:te,289:ne,290:Le,291:ke,292:$e,293:Me,294:Ue,295:_e,298:Fe,299:Pe,308:qe,313:Ge,340:1293,416:188,417:Ve,421:Be},t(Q,[2,452],{113:622,323:634,98:At,111:yt,114:Nt,115:Ct,122:Rt,123:gr,132:wt,135:It,137:xt,138:Dt,139:Lt,140:kt,141:$t,153:Mt,169:Ut,170:_t,178:Ft,179:Pt,307:qt,308:Gt,309:Vt,311:Bt,312:jt,313:Ht,314:Jt,315:Yt,316:Wt,317:Xt,318:Kt,319:Qt,320:zt,321:Zt,322:en,326:tn,327:nn,328:rn,329:an}),t(Q,[2,580]),t(Q,[2,581]),{2:r,3:242,4:a,5:s,198:1295},t(Q,[2,663]),{2:r,3:166,4:a,5:s,57:163,76:oe,93:1296,110:147,112:151,130:ue,131:ce,136:le,142:he,143:159,144:de,148:fe,151:pe,153:be,155:z,157:165,178:Ee,179:ge,180:me,195:149,199:145,200:153,201:154,253:148,254:144,255:146,256:150,257:152,258:155,259:156,260:157,261:160,262:161,264:ve,265:p,266:Se,267:Te,269:Ae,276:ye,277:Ne,278:Ce,279:Re,280:Oe,281:we,282:Ie,283:xe,284:De,286:Z,287:ee,288:te,289:ne,290:Le,291:ke,292:$e,293:Me,294:Ue,295:_e,298:Fe,299:Pe,308:qe,313:Ge,416:188,417:Ve,421:Be},{2:r,3:166,4:a,5:s,57:163,76:oe,93:1297,110:147,112:151,130:ue,131:ce,136:le,142:he,143:159,144:de,148:fe,151:pe,153:be,155:z,157:165,178:Ee,179:ge,180:me,195:149,199:145,200:153,201:154,253:148,254:144,255:146,256:150,257:152,258:155,259:156,260:157,261:160,262:161,264:ve,265:p,266:Se,267:Te,269:Ae,276:ye,277:Ne,278:Ce,279:Re,280:Oe,281:we,282:Ie,283:xe,284:De,286:Z,287:ee,288:te,289:ne,290:Le,291:ke,292:$e,293:Me,294:Ue,295:_e,298:Fe,299:Pe,308:qe,313:Ge,416:188,417:Ve,421:Be},{77:[1,1298],98:At,111:yt,113:622,114:Nt,115:Ct,122:Rt,123:gr,132:wt,135:It,137:xt,138:Dt,139:Lt,140:kt,141:$t,153:Mt,169:Ut,170:_t,178:Ft,179:Pt,307:qt,308:Gt,309:Vt,311:Bt,312:jt,313:Ht,314:Jt,315:Yt,316:Wt,317:Xt,318:Kt,319:Qt,320:zt,321:Zt,322:en,323:634,326:tn,327:nn,328:rn,329:an},{77:[1,1299],98:At,111:yt,113:622,114:Nt,115:Ct,122:Rt,123:gr,132:wt,135:It,137:xt,138:Dt,139:Lt,140:kt,141:$t,153:Mt,169:Ut,170:_t,178:Ft,179:Pt,307:qt,308:Gt,309:Vt,311:Bt,312:jt,313:Ht,314:Jt,315:Yt,316:Wt,317:Xt,318:Kt,319:Qt,320:zt,321:Zt,322:en,323:634,326:tn,327:nn,328:rn,329:an},{2:r,3:166,4:a,5:s,40:1300,57:163,76:oe,78:74,88:c,93:259,110:147,112:151,130:ue,131:ce,136:le,142:he,143:159,144:de,148:fe,150:1301,151:pe,153:be,155:z,157:165,178:Ee,179:ge,180:me,183:99,188:f,195:149,199:145,200:153,201:154,253:148,254:144,255:146,256:150,257:152,258:155,259:156,260:157,261:160,262:161,264:ve,265:p,266:Se,267:Te,269:Ae,276:ye,277:Ne,278:Ce,279:Re,280:Oe,281:we,282:Ie,283:xe,284:De,286:Z,287:ee,288:te,289:ne,290:Le,291:ke,292:$e,293:Me,294:Ue,295:_e,298:Fe,299:Pe,308:qe,313:Ge,416:188,417:Ve,421:Be},{77:[1,1302]},{73:br,77:[1,1303]},t(ze,[2,423]),{2:r,3:166,4:a,5:s,57:163,76:oe,93:1304,110:147,112:151,130:ue,131:ce,136:le,142:he,143:159,144:de,148:fe,151:pe,153:be,155:z,157:165,178:Ee,179:ge,180:me,195:149,199:145,200:153,201:154,253:148,254:144,255:146,256:150,257:152,258:155,259:156,260:157,261:160,262:161,264:ve,265:p,266:Se,267:Te,269:Ae,276:ye,277:Ne,278:Ce,279:Re,280:Oe,281:we,282:Ie,283:xe,284:De,286:Z,287:ee,288:te,289:ne,290:Le,291:ke,292:$e,293:Me,294:Ue,295:_e,298:Fe,299:Pe,308:qe,313:Ge,416:188,417:Ve,421:Be},{2:r,3:166,4:a,5:s,40:1305,57:163,76:oe,77:[1,1307],78:74,88:c,93:259,110:147,112:151,130:ue,131:ce,136:le,142:he,143:159,144:de,148:fe,150:1306,151:pe,153:be,155:z,157:165,178:Ee,179:ge,180:me,183:99,188:f,195:149,199:145,200:153,201:154,253:148,254:144,255:146,256:150,257:152,258:155,259:156,260:157,261:160,262:161,264:ve,265:p,266:Se,267:Te,269:Ae,276:ye,277:Ne,278:Ce,279:Re,280:Oe,281:we,282:Ie,283:xe,284:De,286:Z,287:ee,288:te,289:ne,290:Le,291:ke,292:$e,293:Me,294:Ue,295:_e,298:Fe,299:Pe,308:qe,313:Ge,416:188,417:Ve,421:Be},t(ze,[2,426]),t(ze,[2,428]),t(ze,za,{271:1308,272:Za}),{77:[1,1310],98:At,111:yt,113:622,114:Nt,115:Ct,122:Rt,123:gr,132:wt,135:It,137:xt,138:Dt,139:Lt,140:kt,141:$t,153:Mt,169:Ut,170:_t,178:Ft,179:Pt,307:qt,308:Gt,309:Vt,311:Bt,312:jt,313:Ht,314:Jt,315:Yt,316:Wt,317:Xt,318:Kt,319:Qt,320:zt,321:Zt,322:en,323:634,326:tn,327:nn,328:rn,329:an},{77:[1,1311],98:At,111:yt,113:622,114:Nt,115:Ct,122:Rt,123:gr,132:wt,135:It,137:xt,138:Dt,139:Lt,140:kt,141:$t,153:Mt,169:Ut,170:_t,178:Ft,179:Pt,307:qt,308:Gt,309:Vt,311:Bt,312:jt,313:Ht,314:Jt,315:Yt,316:Wt,317:Xt,318:Kt,319:Qt,320:zt,321:Zt,322:en,323:634,326:tn,327:nn,328:rn,
329:an},{2:r,3:1312,4:a,5:s,179:[1,1313]},t(hn,[2,612]),t(ze,[2,360]),{302:[1,1314]},t(ze,[2,367]),{98:At,111:yt,113:622,114:Nt,115:Ct,122:Rt,123:gr,132:wt,135:It,137:xt,138:Dt,139:Lt,140:kt,141:$t,153:Mt,169:Ut,170:_t,178:Ft,179:Pt,302:[2,371],307:qt,308:Gt,309:Vt,311:Bt,312:jt,313:Ht,314:Jt,315:Yt,316:Wt,317:Xt,318:Kt,319:Qt,320:zt,321:Zt,322:en,323:634,326:tn,327:nn,328:rn,329:an},{2:r,3:166,4:a,5:s,57:163,76:oe,93:1315,110:147,112:151,130:ue,131:ce,136:le,142:he,143:159,144:de,148:fe,151:pe,153:be,155:z,157:165,178:Ee,179:ge,180:me,195:149,199:145,200:153,201:154,253:148,254:144,255:146,256:150,257:152,258:155,259:156,260:157,261:160,262:161,264:ve,265:p,266:Se,267:Te,269:Ae,276:ye,277:Ne,278:Ce,279:Re,280:Oe,281:we,282:Ie,283:xe,284:De,286:Z,287:ee,288:te,289:ne,290:Le,291:ke,292:$e,293:Me,294:Ue,295:_e,298:Fe,299:Pe,308:qe,313:Ge,416:188,417:Ve,421:Be},{4:Gr,7:874,268:1316,383:873,385:Vr},{2:r,3:166,4:a,5:s,57:163,76:oe,93:1317,110:147,112:151,130:ue,131:ce,136:le,142:he,143:159,144:de,148:fe,151:pe,153:be,155:z,157:165,178:Ee,179:ge,180:me,195:149,199:145,200:153,201:154,253:148,254:144,255:146,256:150,257:152,258:155,259:156,260:157,261:160,262:161,264:ve,265:p,266:Se,267:Te,269:Ae,276:ye,277:Ne,278:Ce,279:Re,280:Oe,281:we,282:Ie,283:xe,284:De,286:Z,287:ee,288:te,289:ne,290:Le,291:ke,292:$e,293:Me,294:Ue,295:_e,298:Fe,299:Pe,308:qe,313:Ge,416:188,417:Ve,421:Be},t(hn,[2,634]),t(Zr,[2,641]),t(ea,[2,629]),t(La,Da),t(hn,[2,631]),t(ra,[2,636]),t(ra,[2,638]),t(ra,[2,639]),t(ra,[2,640]),t($a,[2,454],{73:es}),{2:r,3:166,4:a,5:s,57:163,76:oe,93:1294,110:147,112:151,130:ue,131:ce,136:le,142:he,143:159,144:de,148:fe,151:pe,153:be,155:z,157:165,178:Ee,179:ge,180:me,195:149,199:145,200:153,201:154,253:148,254:144,255:146,256:150,257:152,258:155,259:156,260:157,261:160,262:161,264:ve,265:p,266:Se,267:Te,269:Ae,276:ye,277:Ne,278:Ce,279:Re,280:Oe,281:we,282:Ie,283:xe,284:De,286:Z,287:ee,288:te,289:ne,290:Le,291:ke,292:$e,293:Me,294:Ue,295:_e,298:Fe,299:Pe,308:qe,313:Ge,340:1320,416:188,417:Ve,421:Be},t(ts,[2,465]),t(ts,[2,466]),t($a,[2,457]),{73:ns,77:[1,1321]},t(rs,[2,478]),{40:1324,78:74,88:c,183:99,188:f,337:[1,1323]},t($a,[2,456],{73:es}),t(Q,[2,710],{454:1325,455:1326,456:1327,304:Ma,461:[1,1328]}),t(as,[2,694]),t(as,[2,695]),{153:[1,1330],457:[1,1329]},{98:At,111:yt,113:622,114:Nt,115:Ct,122:Rt,123:gr,132:wt,135:It,137:xt,138:Dt,139:Lt,140:kt,141:$t,153:Mt,169:Ut,170:_t,178:Ft,179:Pt,304:[2,691],307:qt,308:Gt,309:Vt,311:Bt,312:jt,313:Ht,314:Jt,315:Yt,316:Wt,317:Xt,318:Kt,319:Qt,320:zt,321:Zt,322:en,323:634,326:tn,327:nn,328:rn,329:an},t(En,[2,178]),{2:r,3:1331,4:a,5:s},t(Q,[2,565]),t(ss,[2,237],{83:1332,127:[1,1333]}),t(Ua,[2,1039]),{76:[1,1334]},{76:[1,1335]},t(aa,[2,168],{203:1336,214:1338,204:1339,215:1340,220:1343,73:is,205:os,207:us,221:cs,222:ls,223:hs,224:ds,225:fs,226:ps,227:bs,228:Es}),{2:r,3:217,4:a,5:s,40:708,76:Ye,78:74,88:c,131:We,142:he,143:210,144:de,151:pe,155:z,180:me,183:99,188:f,198:211,199:213,200:212,201:215,202:1352,208:1197,212:Xe,213:216,286:Z,287:ee,288:te,289:ne,298:Fe,416:188,417:Ve,421:Be},t(rs,[2,176]),{2:r,3:999,4:a,5:s,109:1353,110:997,111:sa},t(_a,[2,86]),t(ia,[2,146],{113:622,323:634,98:At,111:yt,114:Nt,115:Ct,122:Rt,123:gr,132:wt,135:It,137:xt,138:Dt,139:Lt,140:kt,141:$t,153:Mt,169:Ut,170:_t,178:Ft,179:Pt,307:qt,308:Gt,309:Vt,311:Bt,312:jt,313:Ht,314:Jt,315:Yt,316:Wt,317:Xt,318:Kt,319:Qt,320:zt,321:Zt,322:en,326:tn,327:nn,328:rn,329:an}),{77:[1,1354]},{73:br,77:[2,1059]},{2:r,3:166,4:a,5:s,57:163,76:oe,77:[2,1052],93:1359,110:147,112:151,119:1355,120:1356,130:ue,131:ce,136:le,142:he,143:159,144:de,148:fe,151:pe,153:be,155:z,157:165,178:Ee,179:ge,180:me,195:149,199:145,200:153,201:154,240:1357,241:[1,1358],253:148,254:144,255:146,256:150,257:152,258:155,259:156,260:157,261:160,262:161,264:ve,265:p,266:Se,267:Te,269:Ae,276:ye,277:Ne,278:Ce,279:Re,280:Oe,281:we,282:Ie,283:xe,284:De,286:Z,287:ee,288:te,289:ne,290:Le,291:ke,292:$e,293:Me,294:Ue,295:_e,298:Fe,299:Pe,308:qe,313:Ge,416:188,417:Ve,421:Be},t(la,[2,97]),t(Fa,[2,1055],{152:1003,178:oa,179:ua,180:ca}),{2:r,3:730,4:a,5:s,71:yn,75:Nn,76:Cn,77:[1,1360],111:Rn,113:733,114:Nt,115:Ct,116:1361,117:On,121:wn,122:In,123:xn,127:Dn,128:Ln,129:kn,130:$n,131:Mn,132:Un,133:_n,134:Fn,135:Pn,136:qn,137:Gn,138:Vn,139:Bn,140:jn,141:Hn,142:Jn,143:755,144:Yn,145:Wn,147:Xn,148:Kn,149:Qn,151:zn,153:Zn,155:er,157:765,159:766,161:tr,163:nr,165:rr,167:ar,168:sr,169:ir,170:or,171:ur,172:cr,174:lr,184:hr,186:dr,276:ye,277:Ne,278:Ce,279:Re,280:Oe,281:we,282:Ie,283:xe,284:De,416:188,417:Ve,421:Be},t(Fa,[2,1056],{152:1003,178:oa,179:ua,180:ca}),{77:[1,1362],98:At,111:yt,113:622,114:Nt,115:Ct,122:Rt,123:gr,132:wt,135:It,137:xt,138:Dt,139:Lt,140:kt,141:$t,153:Mt,169:Ut,170:_t,178:Ft,179:Pt,307:qt,308:Gt,309:Vt,311:Bt,312:jt,313:Ht,314:Jt,315:Yt,316:Wt,317:Xt,318:Kt,319:Qt,320:zt,321:Zt,322:en,323:634,326:tn,327:nn,328:rn,329:an},{77:[1,1363],98:At,111:yt,113:622,114:Nt,115:Ct,122:Rt,123:gr,132:wt,135:It,137:xt,138:Dt,139:Lt,140:kt,141:$t,153:Mt,169:Ut,170:_t,178:Ft,179:Pt,307:qt,308:Gt,309:Vt,311:Bt,312:jt,313:Ht,314:Jt,315:Yt,316:Wt,317:Xt,318:Kt,319:Qt,320:zt,321:Zt,322:en,323:634,326:tn,327:nn,328:rn,329:an},{77:[1,1364]},t(la,[2,119]),{73:Va,77:[1,1365]},t(la,[2,121]),{73:br,77:[1,1366]},{2:r,3:730,4:a,5:s,71:yn,75:Nn,76:Cn,77:[1,1367],111:Rn,113:733,114:Nt,115:Ct,116:1368,117:On,121:wn,122:In,123:xn,127:Dn,128:Ln,129:kn,130:$n,131:Mn,132:Un,133:_n,134:Fn,135:Pn,136:qn,137:Gn,138:Vn,139:Bn,140:jn,141:Hn,142:Jn,143:755,144:Yn,145:Wn,147:Xn,148:Kn,149:Qn,151:zn,153:Zn,155:er,157:765,159:766,161:tr,163:nr,165:rr,167:ar,168:sr,169:ir,170:or,171:ur,172:cr,174:lr,184:hr,186:dr,276:ye,277:Ne,278:Ce,279:Re,280:Oe,281:we,282:Ie,283:xe,284:De,416:188,417:Ve,421:Be},{2:r,3:730,4:a,5:s,71:yn,75:Nn,76:Cn,77:[1,1369],111:Rn,113:733,114:Nt,115:Ct,116:1370,117:On,121:wn,122:In,123:xn,127:Dn,128:Ln,129:kn,130:$n,131:Mn,132:Un,133:_n,134:Fn,135:Pn,136:qn,137:Gn,138:Vn,139:Bn,140:jn,141:Hn,142:Jn,143:755,144:Yn,145:Wn,147:Xn,148:Kn,149:Qn,151:zn,153:Zn,155:er,157:765,159:766,161:tr,163:nr,165:rr,167:ar,168:sr,169:ir,170:or,171:ur,172:cr,174:lr,184:hr,186:dr,276:ye,277:Ne,278:Ce,279:Re,280:Oe,281:we,282:Ie,283:xe,284:De,416:188,417:Ve,421:Be},{2:r,3:730,4:a,5:s,71:yn,75:Nn,76:Cn,77:[1,1371],111:Rn,113:733,114:Nt,115:Ct,116:1372,117:On,121:wn,122:In,123:xn,127:Dn,128:Ln,129:kn,130:$n,131:Mn,132:Un,133:_n,134:Fn,135:Pn,136:qn,137:Gn,138:Vn,139:Bn,140:jn,141:Hn,142:Jn,143:755,144:Yn,145:Wn,147:Xn,148:Kn,149:Qn,151:zn,153:Zn,155:er,157:765,159:766,161:tr,163:nr,165:rr,167:ar,168:sr,169:ir,170:or,171:ur,172:cr,174:lr,184:hr,186:dr,276:ye,277:Ne,278:Ce,279:Re,280:Oe,281:we,282:Ie,283:xe,284:De,416:188,417:Ve,421:Be},{2:r,3:730,4:a,5:s,71:yn,75:Nn,76:Cn,77:[1,1373],111:Rn,113:733,114:Nt,115:Ct,116:1374,117:On,121:wn,122:In,123:xn,127:Dn,128:Ln,129:kn,130:$n,131:Mn,132:Un,133:_n,134:Fn,135:Pn,136:qn,137:Gn,138:Vn,139:Bn,140:jn,141:Hn,142:Jn,143:755,144:Yn,145:Wn,147:Xn,148:Kn,149:Qn,151:zn,153:Zn,155:er,157:765,159:766,161:tr,163:nr,165:rr,167:ar,168:sr,169:ir,170:or,171:ur,172:cr,174:lr,184:hr,186:dr,276:ye,277:Ne,278:Ce,279:Re,280:Oe,281:we,282:Ie,283:xe,284:De,416:188,417:Ve,421:Be},{73:gs,77:[1,1375]},t(ms,[2,142],{416:188,3:730,113:733,143:755,157:765,159:766,116:1377,2:r,4:a,5:s,71:yn,75:Nn,76:Cn,111:Rn,114:Nt,115:Ct,117:On,121:wn,122:In,123:xn,127:Dn,128:Ln,129:kn,130:$n,131:Mn,132:Un,133:_n,134:Fn,135:Pn,136:qn,137:Gn,138:Vn,139:Bn,140:jn,141:Hn,142:Jn,144:Yn,145:Wn,147:Xn,148:Kn,149:Qn,151:zn,153:Zn,155:er,161:tr,163:nr,165:rr,167:ar,168:sr,169:ir,170:or,171:ur,172:cr,174:lr,184:hr,186:dr,276:ye,277:Ne,278:Ce,279:Re,280:Oe,281:we,282:Ie,283:xe,284:De,417:Ve,421:Be}),t(Pa,qa,{177:1220,162:1378}),{2:r,3:730,4:a,5:s,71:yn,75:Nn,76:Cn,77:[1,1379],111:Rn,113:733,114:Nt,115:Ct,116:1380,117:On,121:wn,122:In,123:xn,127:Dn,128:Ln,129:kn,130:$n,131:Mn,132:Un,133:_n,134:Fn,135:Pn,136:qn,137:Gn,138:Vn,139:Bn,140:jn,141:Hn,142:Jn,143:755,144:Yn,145:Wn,147:Xn,148:Kn,149:Qn,151:zn,153:Zn,155:er,157:765,159:766,161:tr,163:nr,165:rr,167:ar,168:sr,169:ir,170:or,171:ur,172:cr,174:lr,184:hr,186:dr,276:ye,277:Ne,278:Ce,279:Re,280:Oe,281:we,282:Ie,283:xe,284:De,416:188,417:Ve,421:Be},{2:r,3:730,4:a,5:s,71:yn,75:Nn,76:Cn,77:[1,1381],111:Rn,113:733,114:Nt,115:Ct,116:1382,117:On,121:wn,122:In,123:xn,127:Dn,128:Ln,129:kn,130:$n,131:Mn,132:Un,133:_n,134:Fn,135:Pn,136:qn,137:Gn,138:Vn,139:Bn,140:jn,141:Hn,142:Jn,143:755,144:Yn,145:Wn,147:Xn,148:Kn,149:Qn,151:zn,153:Zn,155:er,157:765,159:766,161:tr,163:nr,165:rr,167:ar,168:sr,169:ir,170:or,171:ur,172:cr,174:lr,184:hr,186:dr,276:ye,277:Ne,278:Ce,279:Re,280:Oe,281:we,282:Ie,283:xe,284:De,416:188,417:Ve,421:Be},{73:gs,77:[1,1383]},{73:gs,77:[1,1384]},{73:gs,77:[1,1385]},{73:gs,77:[1,1386]},{77:[1,1387],152:1003,178:oa,179:ua,180:ca},{73:ba,77:[1,1388]},{2:r,3:730,4:a,5:s,71:yn,73:[1,1389],75:Nn,76:Cn,111:Rn,113:733,114:Nt,115:Ct,116:1390,117:On,121:wn,122:In,123:xn,127:Dn,128:Ln,129:kn,130:$n,131:Mn,132:Un,133:_n,134:Fn,135:Pn,136:qn,137:Gn,138:Vn,139:Bn,140:jn,141:Hn,142:Jn,143:755,144:Yn,145:Wn,147:Xn,148:Kn,149:Qn,151:zn,153:Zn,155:er,157:765,159:766,161:tr,163:nr,165:rr,167:ar,168:sr,169:ir,170:or,171:ur,172:cr,174:lr,184:hr,186:dr,276:ye,277:Ne,278:Ce,279:Re,280:Oe,281:we,282:Ie,283:xe,284:De,416:188,417:Ve,421:Be},{2:r,3:1391,4:a,5:s},{2:r,3:1392,4:a,5:s},t(Q,[2,588]),{2:r,3:1393,4:a,5:s},{112:1394,131:ce,292:$e},{77:[1,1395]},{2:r,3:166,4:a,5:s,57:163,76:oe,93:1396,110:147,112:151,130:ue,131:ce,136:le,142:he,143:159,144:de,148:fe,151:pe,153:be,155:z,157:165,178:Ee,179:ge,180:me,195:149,199:145,200:153,201:154,253:148,254:144,255:146,256:150,257:152,258:155,259:156,260:157,261:160,262:161,264:ve,265:p,266:Se,267:Te,269:Ae,276:ye,277:Ne,278:Ce,279:Re,280:Oe,281:we,282:Ie,283:xe,284:De,286:Z,287:ee,288:te,289:ne,290:Le,291:ke,292:$e,293:Me,294:Ue,295:_e,298:Fe,299:Pe,308:qe,313:Ge,416:188,417:Ve,421:Be},{2:r,3:999,4:a,5:s,110:1053,142:ha,144:da,332:1397,333:1054},{2:r,3:166,4:a,5:s,57:163,76:oe,93:1398,110:147,112:151,130:ue,131:ce,136:le,142:he,143:159,144:de,148:fe,151:pe,153:be,155:z,157:165,178:Ee,179:ge,180:me,195:149,199:145,200:153,201:154,253:148,254:144,255:146,256:150,257:152,258:155,259:156,260:157,261:160,262:161,264:ve,265:p,266:Se,267:Te,269:Ae,276:ye,277:Ne,278:Ce,279:Re,280:Oe,281:we,282:Ie,283:xe,284:De,286:Z,287:ee,288:te,289:ne,290:Le,291:ke,292:$e,293:Me,294:Ue,295:_e,298:Fe,299:Pe,308:qe,313:Ge,416:188,417:Ve,421:Be},{123:[1,1399]},t(Q,[2,646],{113:622,323:634,98:At,111:yt,114:Nt,115:Ct,122:Rt,123:gr,132:wt,135:It,137:xt,138:Dt,139:Lt,140:kt,141:$t,153:Mt,169:Ut,170:_t,178:Ft,179:Pt,307:qt,308:Gt,309:Vt,311:Bt,312:jt,313:Ht,314:Jt,315:Yt,316:Wt,317:Xt,318:Kt,319:Qt,320:zt,321:Zt,322:en,326:tn,327:nn,328:rn,329:an}),t(ja,[2,651]),{77:[1,1400],98:At,111:yt,113:622,114:Nt,115:Ct,122:Rt,123:gr,132:wt,135:It,137:xt,138:Dt,139:Lt,140:kt,141:$t,153:Mt,169:Ut,170:_t,178:Ft,179:Pt,307:qt,308:Gt,309:Vt,311:Bt,312:jt,313:Ht,314:Jt,315:Yt,316:Wt,317:Xt,318:Kt,319:Qt,320:zt,321:Zt,322:en,323:634,326:tn,327:nn,328:rn,329:an},t(Q,[2,647],{113:622,323:634,98:At,111:yt,114:Nt,115:Ct,122:Rt,123:gr,132:wt,135:It,137:xt,138:Dt,139:Lt,140:kt,141:$t,153:Mt,169:Ut,170:_t,178:Ft,179:Pt,307:qt,308:Gt,309:Vt,311:Bt,312:jt,313:Ht,314:Jt,315:Yt,316:Wt,317:Xt,318:Kt,319:Qt,320:zt,321:Zt,322:en,326:tn,327:nn,328:rn,329:an}),{2:r,3:166,4:a,5:s,57:163,76:oe,93:1401,110:147,112:151,130:ue,131:ce,136:le,142:he,143:159,144:de,148:fe,151:pe,153:be,155:z,157:165,178:Ee,179:ge,180:me,195:149,199:145,200:153,201:154,253:148,254:144,255:146,256:150,257:152,258:155,259:156,260:157,261:160,262:161,264:ve,265:p,266:Se,267:Te,269:Ae,276:ye,277:Ne,278:Ce,279:Re,280:Oe,281:we,282:Ie,283:xe,284:De,286:Z,287:ee,288:te,289:ne,290:Le,291:ke,292:$e,293:Me,294:Ue,295:_e,298:Fe,299:Pe,308:qe,313:Ge,416:188,417:Ve,421:Be},t(Ea,[2,271]),t(Ea,[2,273]),t(Ea,[2,275]),t(Ea,[2,277]),t(at,[2,160]),t(Q,[2,560]),{147:[1,1402]},t(Q,[2,561]),t(Tr,[2,527],{383:873,7:874,268:1403,4:Gr,382:[1,1404],385:Vr}),t(Q,[2,562]),t(Q,[2,564]),{73:br,77:[1,1405]},t(Q,[2,568]),t(on,[2,342]),{73:[1,1406],98:At,111:yt,113:622,114:Nt,115:Ct,122:Rt,123:gr,132:wt,135:It,137:xt,138:Dt,139:Lt,140:kt,141:$t,153:Mt,169:Ut,170:_t,178:Ft,179:Pt,307:qt,308:Gt,309:Vt,311:Bt,312:jt,313:Ht,314:Jt,315:Yt,316:Wt,317:Xt,318:Kt,319:Qt,320:zt,321:Zt,322:en,323:634,326:tn,327:nn,328:rn,329:an},{73:[1,1407],98:At,111:yt,113:622,114:Nt,115:Ct,122:Rt,123:gr,132:wt,135:It,137:xt,138:Dt,139:Lt,140:kt,141:$t,153:Mt,169:Ut,170:_t,178:Ft,179:Pt,307:qt,308:Gt,309:Vt,311:Bt,312:jt,313:Ht,314:Jt,315:Yt,316:Wt,317:Xt,318:Kt,319:Qt,320:zt,321:Zt,322:en,323:634,326:tn,327:nn,328:rn,329:an},{73:[1,1408],98:At,111:yt,113:622,114:Nt,115:Ct,122:Rt,123:gr,132:wt,135:It,137:xt,138:Dt,139:Lt,140:kt,141:$t,153:Mt,169:Ut,170:_t,178:Ft,179:Pt,307:qt,308:Gt,309:Vt,311:Bt,312:jt,313:Ht,314:Jt,315:Yt,316:Wt,317:Xt,318:Kt,319:Qt,320:zt,321:Zt,322:en,323:634,326:tn,327:nn,328:rn,329:an},{73:[1,1409],98:At,111:yt,113:622,114:Nt,115:Ct,122:Rt,123:gr,132:wt,135:It,137:xt,138:Dt,139:Lt,140:kt,141:$t,153:Mt,169:Ut,170:_t,178:Ft,179:Pt,307:qt,308:Gt,309:Vt,311:Bt,312:jt,313:Ht,314:Jt,315:Yt,316:Wt,317:Xt,318:Kt,319:Qt,320:zt,321:Zt,322:en,323:634,326:tn,327:nn,328:rn,329:an},t(Q,[2,572]),{2:r,3:166,4:a,5:s,57:163,76:oe,93:259,110:147,112:151,130:ue,131:ce,136:le,142:he,143:159,144:de,148:fe,150:1410,151:pe,153:be,155:z,157:165,178:Ee,179:ge,180:me,195:149,199:145,200:153,201:154,253:148,254:144,255:146,256:150,257:152,258:155,259:156,260:157,261:160,262:161,264:ve,265:p,266:Se,267:Te,269:Ae,276:ye,277:Ne,278:Ce,279:Re,280:Oe,281:we,282:Ie,283:xe,284:De,286:Z,287:ee,288:te,289:ne,290:Le,291:ke,292:$e,293:Me,294:Ue,295:_e,298:Fe,299:Pe,308:qe,313:Ge,416:188,417:Ve,421:Be},{2:r,3:1411,4:a,5:s},t(Q,[2,574]),{2:r,3:166,4:a,5:s,57:163,76:oe,93:1359,110:147,112:151,119:1412,130:ue,131:ce,136:le,142:he,143:159,144:de,148:fe,151:pe,153:be,155:z,157:165,178:Ee,179:ge,180:me,195:149,199:145,200:153,201:154,240:1357,253:148,254:144,255:146,256:150,257:152,258:155,259:156,260:157,261:160,262:161,264:ve,265:p,266:Se,267:Te,269:Ae,276:ye,277:Ne,278:Ce,279:Re,280:Oe,281:we,282:Ie,283:xe,284:De,286:Z,287:ee,288:te,289:ne,290:Le,291:ke,292:$e,293:Me,294:Ue,295:_e,298:Fe,299:Pe,308:qe,313:Ge,416:188,417:Ve,421:Be},{76:[1,1413]},{2:r,3:1414,4:a,5:s},{75:ga,138:[2,1142],477:1415,480:1416},t(Ya,[2,1141]),{138:[1,1417]},{138:[2,1145]},t(Tr,[2,730]),t(Lr,[2,737]),t(Lr,[2,1159]),{2:r,3:1254,4:a,5:s,75:[1,1420],347:1418,354:1419,380:1421},{2:r,3:999,4:a,5:s,99:1422,110:1180},{40:1423,78:74,88:c,183:99,188:f},{2:r,3:166,4:a,5:s,57:163,76:oe,93:1424,110:147,112:151,130:ue,131:ce,136:le,142:he,143:159,144:de,148:fe,151:pe,153:be,155:z,157:165,178:Ee,179:ge,180:me,195:149,199:145,200:153,201:154,253:148,254:144,255:146,256:150,257:152,258:155,259:156,260:157,261:160,262:161,264:ve,265:p,266:Se,267:Te,269:Ae,276:ye,277:Ne,278:Ce,279:Re,280:Oe,281:we,282:Ie,283:xe,284:De,286:Z,287:ee,288:te,289:ne,290:Le,291:ke,292:$e,293:Me,294:Ue,295:_e,298:Fe,299:Pe,308:qe,313:Ge,416:188,417:Ve,421:Be},t($a,[2,715]),{2:r,3:999,4:a,5:s,110:1053,142:ha,144:da,146:1425,332:1052,333:1054},{2:r,3:166,4:a,5:s,57:163,76:oe,93:259,110:147,112:151,130:ue,131:ce,136:le,142:he,143:159,144:de,148:fe,150:1426,151:pe,153:be,155:z,157:165,178:Ee,179:ge,180:me,195:149,199:145,200:153,201:154,253:148,254:144,255:146,256:150,257:152,258:155,259:156,260:157,261:160,262:161,264:ve,265:p,266:Se,267:Te,269:Ae,276:ye,277:Ne,278:Ce,279:Re,280:Oe,281:we,282:Ie,283:xe,284:De,286:Z,287:ee,288:te,289:ne,290:Le,291:ke,292:$e,293:Me,294:Ue,295:_e,298:Fe,299:Pe,308:qe,313:Ge,416:188,417:Ve,421:Be},t($a,[2,720]),{2:r,3:242,4:a,5:s,198:1427},{331:va,334:Sa,335:Ta,508:1428},t(mt,[2,684],{113:622,323:634,98:At,111:yt,114:Nt,115:Ct,122:Rt,123:gr,132:wt,135:It,137:xt,138:Dt,139:Lt,140:kt,141:$t,153:Mt,169:Ut,170:_t,178:Ft,179:Pt,307:qt,308:Gt,309:Vt,311:Bt,312:jt,313:Ht,314:Jt,315:Yt,316:Wt,317:Xt,318:Kt,319:Qt,320:zt,321:Zt,322:en,326:tn,327:nn,328:rn,329:an}),{2:r,3:166,4:a,5:s,57:163,76:oe,93:1429,110:147,112:151,130:ue,131:ce,136:le,142:he,143:159,144:de,148:fe,151:pe,153:be,155:z,157:165,178:Ee,179:ge,180:me,195:149,199:145,200:153,201:154,253:148,254:144,255:146,256:150,257:152,258:155,259:156,260:157,261:160,262:161,264:ve,265:p,266:Se,267:Te,269:Ae,276:ye,277:Ne,278:Ce,279:Re,280:Oe,281:we,282:Ie,283:xe,284:De,286:Z,287:ee,288:te,289:ne,290:Le,291:ke,292:$e,293:Me,294:Ue,295:_e,298:Fe,299:Pe,308:qe,313:Ge,416:188,417:Ve,421:Be},{73:[1,1430],77:[1,1431]},t(ms,[2,534]),t(ms,[2,535]),{73:vs,77:[1,1432]},t(ms,[2,470],{113:622,323:634,98:At,111:yt,114:Nt,115:Ct,122:Rt,123:gr,132:wt,135:It,137:xt,138:Dt,139:Lt,140:kt,141:$t,153:Mt,169:Ut,170:_t,178:Ft,179:Pt,307:qt,308:Gt,309:Vt,311:Bt,312:jt,313:Ht,314:Jt,315:Yt,316:Wt,317:Xt,318:Kt,319:Qt,320:zt,321:Zt,322:en,326:tn,327:nn,328:rn,329:an}),t(mt,[2,556]),t(Na,[2,377],{113:622,323:634,114:Nt,115:Ct,122:Rt,132:wt,135:It,137:xt,140:kt,141:$t,178:Ft,179:Pt,308:Gt,312:jt,313:Ht,314:Jt,315:Yt}),t(Na,[2,379],{113:622,323:634,114:Nt,115:Ct,122:Rt,132:wt,135:It,137:xt,140:kt,141:$t,178:Ft,179:Pt,308:Gt,312:jt,313:Ht,314:Jt,315:Yt}),t(ze,[2,393]),t(ze,[2,397]),{77:[1,1434]},{73:br,77:[1,1435]},t(ze,[2,419]),t(ze,[2,421]),{77:[1,1436],98:At,111:yt,113:622,114:Nt,115:Ct,122:Rt,123:gr,132:wt,135:It,137:xt,138:Dt,139:Lt,140:kt,141:$t,153:Mt,169:Ut,170:_t,178:Ft,179:Pt,307:qt,308:Gt,309:Vt,311:Bt,312:jt,313:Ht,314:Jt,315:Yt,316:Wt,317:Xt,318:Kt,319:Qt,320:zt,321:Zt,322:en,323:634,326:tn,327:nn,328:rn,329:an},{77:[1,1437]},{73:br,77:[1,1438]},t(ze,[2,424]),t(ze,[2,324]),{76:[1,1439]},t(ze,za,{271:1440,272:Za}),t(ze,za,{271:1441,272:Za}),t(La,[2,283]),t(ze,[2,280]),t(ze,[2,366]),t(zr,[2,370],{113:622,323:634,98:At,111:yt,114:Nt,115:Ct,122:Rt,123:gr,132:wt,135:It,137:xt,138:Dt,139:Lt,140:kt,141:$t,153:Mt,169:Ut,170:_t,178:Ft,179:Pt,307:qt,308:Gt,309:Vt,311:Bt,312:jt,313:Ht,314:Jt,315:Yt,316:Wt,317:Xt,318:Kt,319:Qt,320:zt,321:Zt,322:en,326:tn,327:nn,328:rn,329:an}),{73:[1,1443],77:[1,1442]},{73:[1,1445],77:[1,1444],98:At,111:yt,113:622,114:Nt,115:Ct,122:Rt,123:gr,132:wt,135:It,137:xt,138:Dt,139:Lt,140:kt,141:$t,153:Mt,169:Ut,170:_t,178:Ft,179:Pt,307:qt,308:Gt,309:Vt,311:Bt,312:jt,313:Ht,314:Jt,315:Yt,316:Wt,317:Xt,318:Kt,319:Qt,320:zt,321:Zt,322:en,323:634,326:tn,327:nn,328:rn,329:an},{2:r,3:1312,4:a,5:s},{76:[1,1446],142:he,143:1447,144:Ze,151:pe,180:me,200:1448,298:Fe,416:188,417:Ve,421:Be},{73:vs,77:[1,1449]},{40:1451,78:74,88:c,183:99,188:f,337:[1,1450]},{2:r,3:999,4:a,5:s,110:1452},{76:ka,142:he,143:1176,144:Ze,151:pe,180:me,200:1177,298:Fe,338:1453,416:188,417:Ve,421:Be},t($a,[2,460]),t(Q,[2,687]),t(as,[2,692]),t(as,[2,693]),{2:r,3:166,4:a,5:s,57:163,76:oe,93:814,110:147,112:151,130:ue,131:ce,136:le,142:he,143:159,144:de,148:fe,151:pe,153:be,155:z,157:165,173:1454,178:Ee,179:ge,180:me,195:149,199:145,200:153,201:154,252:813,253:148,254:144,255:146,256:150,257:152,258:155,259:156,260:157,261:160,262:161,264:ve,265:p,266:Se,267:Te,269:Ae,276:ye,277:Ne,278:Ce,279:Re,280:Oe,281:we,282:Ie,283:xe,284:De,286:Z,287:ee,288:te,289:ne,290:Le,291:ke,292:$e,293:Me,294:Ue,295:_e,298:Fe,299:Pe,308:qe,313:Ge,416:188,417:Ve,421:Be},{169:[1,1456],305:[1,1455]},{457:[1,1457]},t(En,[2,179]),t(Ss,[2,239],{84:1458,231:[1,1459]}),{2:r,3:166,4:a,5:s,57:163,76:oe,93:1460,110:147,112:151,130:ue,131:ce,136:le,142:he,143:159,144:de,148:fe,151:pe,153:be,155:z,157:165,178:Ee,179:ge,180:me,195:149,199:145,200:153,201:154,253:148,254:144,255:146,256:150,257:152,258:155,259:156,260:157,261:160,262:161,264:ve,265:p,266:Se,267:Te,269:Ae,276:ye,277:Ne,278:Ce,279:Re,280:Oe,281:we,282:Ie,283:xe,284:De,286:Z,287:ee,288:te,289:ne,290:Le,291:ke,292:$e,293:Me,294:Ue,295:_e,298:Fe,299:Pe,308:qe,313:Ge,416:188,417:Ve,421:Be},{2:r,3:166,4:a,5:s,57:163,76:oe,93:1461,110:147,112:151,130:ue,131:ce,136:le,142:he,143:159,144:de,148:fe,151:pe,153:be,155:z,157:165,178:Ee,179:ge,180:me,195:149,199:145,200:153,201:154,253:148,254:144,255:146,256:150,257:152,258:155,259:156,260:157,261:160,262:161,264:ve,265:p,266:Se,267:Te,269:Ae,276:ye,277:Ne,278:Ce,279:Re,280:Oe,281:we,282:Ie,283:xe,284:De,286:Z,287:ee,288:te,289:ne,290:Le,291:ke,292:$e,293:Me,294:Ue,295:_e,298:Fe,299:Pe,308:qe,313:Ge,416:188,417:Ve,421:Be},{2:r,3:1462,4:a,5:s},t(aa,[2,169],{215:1340,220:1343,214:1463,204:1464,205:os,207:us,221:cs,222:ls,223:hs,224:ds,225:fs,226:ps,227:bs,228:Es}),{2:r,3:217,4:a,5:s,76:Ye,131:We,142:he,143:210,144:de,151:pe,155:z,180:me,198:211,199:213,200:212,201:215,208:1465,212:Xe,213:216,286:Z,287:ee,288:te,289:ne,298:Fe,416:188,417:Ve,421:Be},t(Ts,[2,204]),t(Ts,[2,205]),{2:r,3:217,4:a,5:s,76:[1,1470],142:he,143:1468,144:de,151:pe,155:z,180:me,198:1467,199:1471,200:1469,201:1472,216:1466,286:Z,287:ee,288:te,289:ne,298:Fe,416:188,417:Ve,421:Be},{206:[1,1473],222:As},{206:[1,1475],222:ys},t(Ns,[2,221]),{205:[1,1479],207:[1,1478],220:1477,222:ls,223:hs,224:ds,225:fs,226:ps,227:bs,228:Es},t(Ns,[2,223]),{222:[1,1480]},{207:[1,1482],222:[1,1481]},{207:[1,1484],222:[1,1483]},{207:[1,1485]},{222:[1,1486]},{222:[1,1487]},{73:is,203:1488,204:1339,205:os,207:us,214:1338,215:1340,220:1343,221:cs,222:ls,223:hs,224:ds,225:fs,226:ps,227:bs,228:Es},t(_a,[2,83]),t(la,[2,99]),{73:Cs,77:[1,1489]},{77:[1,1491]},t(Rs,[2,260]),{77:[2,1053]},t(Rs,[2,262],{113:622,323:634,98:At,111:yt,114:Nt,115:Ct,122:Rt,123:gr,132:wt,135:It,137:xt,138:Dt,139:Lt,140:kt,141:$t,153:Mt,169:Ut,170:_t,178:Ft,179:Pt,241:[1,1492],242:[1,1493],307:qt,308:Gt,309:Vt,311:Bt,312:jt,313:Ht,314:Jt,315:Yt,316:Wt,317:Xt,318:Kt,319:Qt,320:zt,321:Zt,322:en,326:tn,327:nn,328:rn,329:an}),t(la,[2,98]),t(Fa,[2,1057],{152:1003,178:oa,179:ua,180:ca}),t(la,[2,100]),t(la,[2,101]),t(la,[2,102]),t(la,[2,120]),t(la,[2,123]),t(la,[2,126]),t(Fa,[2,1061],{152:1003,178:oa,179:ua,180:ca}),t(la,[2,127]),t(Fa,[2,1063],{152:1003,178:oa,179:ua,180:ca}),t(la,[2,128]),t(Fa,[2,1065],{152:1003,178:oa,179:ua,180:ca}),t(la,[2,129]),t(Fa,[2,1069],{152:1003,178:oa,179:ua,180:ca}),t(la,[2,130]),t(Pa,[2,1076],{176:1494}),t(Pa,[2,1079],{152:1003,178:oa,179:ua,180:ca}),{73:gs,77:[1,1495]},t(la,[2,132]),t(Fa,[2,1071],{152:1003,178:oa,179:ua,180:ca}),t(la,[2,133]),t(Fa,[2,1073],{152:1003,178:oa,179:ua,180:ca}),t(la,[2,134]),t(la,[2,135]),t(la,[2,136]),t(la,[2,137]),t(la,[2,138]),t(la,[2,139]),{2:r,3:166,4:a,5:s,57:163,76:oe,93:259,110:147,112:151,130:ue,131:ce,136:le,142:he,143:159,144:de,148:fe,150:1496,151:pe,153:be,155:z,157:165,178:Ee,179:ge,180:me,195:149,199:145,200:153,201:154,253:148,254:144,255:146,256:150,257:152,258:155,259:156,260:157,261:160,262:161,264:ve,265:p,266:Se,267:Te,269:Ae,276:ye,277:Ne,278:Ce,279:Re,280:Oe,281:we,282:Ie,283:xe,284:De,286:Z,287:ee,288:te,289:ne,290:Le,291:ke,292:$e,293:Me,294:Ue,295:_e,298:Fe,299:Pe,308:qe,313:Ge,416:188,417:Ve,421:Be},t(Ga,[2,1075],{152:1003,178:oa,179:ua,180:ca}),t(Q,[2,598]),t(Q,[2,594]),t(Q,[2,596]),t(Q,[2,592]),t(fr,[2,70]),t(Q,[2,446],{113:622,323:634,98:At,111:yt,114:Nt,115:Ct,122:Rt,123:gr,132:wt,135:It,137:xt,138:Dt,139:Lt,140:kt,141:$t,153:Mt,169:Ut,170:_t,178:Ft,179:Pt,307:qt,308:Gt,309:Vt,311:Bt,312:jt,313:Ht,314:Jt,315:Yt,316:Wt,317:Xt,318:Kt,319:Qt,320:zt,321:Zt,322:en,326:tn,327:nn,328:rn,329:an}),t(Ba,[2,449]),t(Ba,[2,450],{113:622,323:634,98:At,111:yt,114:Nt,115:Ct,122:Rt,123:gr,132:wt,135:It,137:xt,138:Dt,139:Lt,140:kt,141:$t,153:Mt,169:Ut,170:_t,178:Ft,179:Pt,307:qt,308:Gt,309:Vt,311:Bt,312:jt,313:Ht,314:Jt,315:Yt,316:Wt,317:Xt,318:Kt,319:Qt,320:zt,321:Zt,322:en,326:tn,327:nn,328:rn,329:an}),{2:r,3:166,4:a,5:s,57:163,76:oe,93:1497,110:147,112:151,130:ue,131:ce,136:le,142:he,143:159,144:de,148:fe,151:pe,153:be,155:z,157:165,178:Ee,179:ge,180:me,195:149,199:145,200:153,201:154,253:148,254:144,255:146,256:150,257:152,258:155,259:156,260:157,261:160,262:161,264:ve,265:p,266:Se,267:Te,269:Ae,276:ye,277:Ne,278:Ce,279:Re,280:Oe,281:we,282:Ie,283:xe,284:De,286:Z,287:ee,288:te,289:ne,290:Le,291:ke,292:$e,293:Me,294:Ue,295:_e,298:Fe,299:Pe,308:qe,313:Ge,416:188,417:Ve,421:Be},t(ja,[2,655]),t(Q,[2,648],{113:622,323:634,98:At,111:yt,114:Nt,115:Ct,122:Rt,123:gr,132:wt,135:It,137:xt,138:Dt,139:Lt,140:kt,141:$t,153:Mt,169:Ut,170:_t,178:Ft,179:Pt,307:qt,308:Gt,309:Vt,311:Bt,312:jt,313:Ht,314:Jt,315:Yt,316:Wt,317:Xt,318:Kt,319:Qt,320:zt,321:Zt,322:en,326:tn,327:nn,328:rn,329:an}),{2:r,3:1498,4:a,5:s},t(Tr,[2,536],{381:1499,387:1500,388:1501,362:1509,153:Os,186:ws,229:Is,293:xs,339:Ds,352:Ls,364:ks,365:$s,369:Ms,370:Us}),t(Tr,[2,526]),t(Q,[2,567],{75:[1,1513]}),{2:r,3:166,4:a,5:s,57:163,76:oe,93:1514,110:147,112:151,130:ue,131:ce,136:le,142:he,143:159,144:de,148:fe,151:pe,153:be,155:z,157:165,178:Ee,179:ge,180:me,195:149,199:145,200:153,201:154,253:148,254:144,255:146,256:150,257:152,258:155,259:156,260:157,261:160,262:161,264:ve,265:p,266:Se,267:Te,269:Ae,276:ye,277:Ne,278:Ce,279:Re,280:Oe,281:we,282:Ie,283:xe,284:De,286:Z,287:ee,288:te,289:ne,290:Le,291:ke,292:$e,293:Me,294:Ue,295:_e,298:Fe,299:Pe,308:qe,313:Ge,416:188,417:Ve,421:Be},{2:r,3:166,4:a,5:s,57:163,76:oe,93:1515,110:147,112:151,130:ue,131:ce,136:le,142:he,143:159,144:de,148:fe,151:pe,153:be,155:z,157:165,178:Ee,179:ge,180:me,195:149,199:145,200:153,201:154,253:148,254:144,255:146,256:150,257:152,258:155,259:156,260:157,261:160,262:161,264:ve,265:p,266:Se,267:Te,269:Ae,276:ye,277:Ne,278:Ce,279:Re,280:Oe,281:we,282:Ie,283:xe,284:De,286:Z,287:ee,288:te,289:ne,290:Le,291:ke,292:$e,293:Me,294:Ue,295:_e,298:Fe,299:Pe,308:qe,313:Ge,416:188,417:Ve,421:Be},{2:r,3:166,4:a,5:s,57:163,76:oe,93:1516,110:147,112:151,130:ue,131:ce,136:le,142:he,143:159,144:de,148:fe,151:pe,153:be,155:z,157:165,178:Ee,179:ge,180:me,195:149,199:145,200:153,201:154,253:148,254:144,255:146,256:150,257:152,258:155,259:156,260:157,261:160,262:161,264:ve,265:p,266:Se,267:Te,269:Ae,276:ye,277:Ne,278:Ce,279:Re,280:Oe,281:we,282:Ie,283:xe,284:De,286:Z,287:ee,288:te,289:ne,290:Le,291:ke,292:$e,293:Me,294:Ue,295:_e,298:Fe,299:Pe,308:qe,313:Ge,416:188,417:Ve,421:Be},{2:r,3:166,4:a,5:s,57:163,76:oe,93:1517,110:147,112:151,130:ue,131:ce,136:le,142:he,143:159,144:de,148:fe,151:pe,153:be,155:z,157:165,178:Ee,179:ge,180:me,195:149,199:145,200:153,201:154,253:148,254:144,255:146,256:150,257:152,258:155,259:156,260:157,261:160,262:161,264:ve,265:p,266:Se,267:Te,269:Ae,276:ye,277:Ne,278:Ce,279:Re,280:Oe,281:we,282:Ie,283:xe,284:De,286:Z,287:ee,288:te,289:ne,290:Le,291:ke,292:$e,293:Me,294:Ue,295:_e,298:Fe,299:Pe,308:qe,313:Ge,416:188,417:Ve,421:Be},{73:br,77:[1,1518]},t(Q,[2,576]),{73:Cs,77:[1,1519]},{2:r,3:166,4:a,5:s,57:163,76:oe,93:1359,110:147,112:151,119:1520,130:ue,131:ce,136:le,142:he,143:159,144:de,148:fe,151:pe,153:be,155:z,157:165,178:Ee,179:ge,180:me,195:149,199:145,200:153,201:154,240:1357,253:148,254:144,255:146,256:150,257:152,258:155,259:156,260:157,261:160,262:161,264:ve,265:p,266:Se,267:Te,269:Ae,276:ye,277:Ne,278:Ce,279:Re,280:Oe,281:we,282:Ie,283:xe,284:De,286:Z,287:ee,288:te,289:ne,290:Le,291:ke,292:$e,293:Me,294:Ue,295:_e,298:Fe,299:Pe,308:qe,313:Ge,416:188,417:Ve,421:Be},t([10,73,77,138,302,306,599,761],[2,734]),{138:[1,1521]},{138:[2,1143]},{2:r,3:1110,4:a,5:s,131:pt,136:bt,142:nt,144:rt,151:Et,428:584,472:1112,475:1522,479:581,490:578,494:580},{77:[1,1523]},{73:[1,1524],77:[2,497]},{40:1525,78:74,88:c,183:99,188:f},t(ms,[2,523]),{73:ns,77:[1,1526]},t(Q,[2,1126],{408:1527,409:1528,71:_s}),t($a,Wa,{78:74,183:99,113:622,323:634,40:1285,465:1530,88:c,98:At,111:yt,114:Nt,115:Ct,122:Rt,123:gr,132:wt,135:It,137:xt,138:Dt,139:Lt,140:kt,141:$t,145:Xa,153:Mt,169:Ut,170:_t,178:Ft,179:Pt,188:f,307:qt,308:Gt,309:Vt,311:Bt,312:jt,313:Ht,314:Jt,315:Yt,316:Wt,317:Xt,318:Kt,319:Qt,320:zt,321:Zt,322:en,326:tn,327:nn,328:rn,329:an,467:Ka}),t($a,[2,718],{73:Va}),t($a,[2,719],{73:br}),t([10,53,71,88,123,145,155,188,265,286,302,306,331,334,335,343,392,396,397,400,402,404,405,413,414,415,431,433,434,436,437,438,439,440,444,445,448,449,502,504,505,514,599,761],[2,1174],{509:1531,3:1532,2:r,4:a,5:s,75:[1,1533]}),t(Fs,[2,1176],{510:1534,75:[1,1535]}),t(mt,[2,685],{113:622,323:634,98:At,111:yt,114:Nt,115:Ct,122:Rt,123:gr,132:wt,135:It,137:xt,138:Dt,139:Lt,140:kt,141:$t,153:Mt,169:Ut,170:_t,178:Ft,179:Pt,307:qt,308:Gt,309:Vt,311:Bt,312:jt,313:Ht,314:Jt,315:Yt,316:Wt,317:Xt,318:Kt,319:Qt,320:zt,321:Zt,322:en,326:tn,327:nn,328:rn,329:an}),{130:[1,1536]},t(Aa,[2,529]),t(Aa,[2,531]),{2:r,3:166,4:a,5:s,57:163,76:oe,93:1537,110:147,112:151,130:ue,131:ce,136:le,142:he,143:159,144:de,148:fe,151:pe,153:be,155:z,157:165,178:Ee,179:ge,180:me,195:149,199:145,200:153,201:154,253:148,254:144,255:146,256:150,257:152,258:155,259:156,260:157,261:160,262:161,264:ve,265:p,266:Se,267:Te,269:Ae,276:ye,277:Ne,278:Ce,279:Re,280:Oe,281:we,282:Ie,283:xe,284:De,286:Z,287:ee,288:te,289:ne,290:Le,291:ke,292:$e,293:Me,294:Ue,295:_e,298:Fe,299:Pe,308:qe,313:Ge,416:188,417:Ve,421:Be},t(ze,[2,409]),t(ze,[2,410]),t(ze,[2,436]),t(ze,[2,420]),t(ze,[2,422]),{117:Ps,273:1538,274:1539,275:[1,1540]},t(ze,[2,325]),t(ze,[2,326]),t(ze,[2,313]),{130:[1,1542]},t(ze,[2,315]),{130:[1,1543]},{2:r,3:166,4:a,5:s,57:163,76:oe,93:1294,110:147,112:151,130:ue,131:ce,136:le,142:he,143:159,144:de,148:fe,151:pe,153:be,155:z,157:165,178:Ee,179:ge,180:me,195:149,199:145,200:153,201:154,253:148,254:144,255:146,256:150,257:152,258:155,259:156,260:157,261:160,262:161,264:ve,265:p,266:Se,267:Te,269:Ae,276:ye,277:Ne,278:Ce,279:Re,280:Oe,281:we,282:Ie,283:xe,284:De,286:Z,287:ee,288:te,289:ne,290:Le,291:ke,292:$e,293:Me,294:Ue,295:_e,298:Fe,299:Pe,308:qe,313:Ge,340:1544,416:188,417:Ve,421:Be},t(ts,[2,468]),t(ts,[2,469]),t(ts,[2,464]),{76:ka,142:he,143:1176,144:Ze,151:pe,180:me,200:1177,298:Fe,338:1545,416:188,417:Ve,421:Be},t($a,[2,461]),t(rs,[2,479]),t($a,[2,455],{73:es}),t(Q,[2,711],{73:ba,197:[1,1546]}),{331:qs,334:Gs,458:1547},{2:r,3:166,4:a,5:s,57:163,76:oe,93:1550,110:147,112:151,130:ue,131:ce,136:le,142:he,143:159,144:de,148:fe,151:pe,153:be,155:z,157:165,178:Ee,179:ge,180:me,195:149,199:145,200:153,201:154,253:148,254:144,255:146,256:150,257:152,258:155,259:156,260:157,261:160,262:161,264:ve,265:p,266:Se,267:Te,269:Ae,276:ye,277:Ne,278:Ce,279:Re,280:Oe,281:we,282:Ie,283:xe,284:De,286:Z,287:ee,288:te,289:ne,290:Le,291:ke,292:$e,293:Me,294:Ue,295:_e,298:Fe,299:Pe,308:qe,313:Ge,416:188,417:Ve,421:Be},{118:[1,1552],169:[1,1553],305:[1,1551]},t(Vs,[2,258],{85:1554,117:[1,1555]}),{118:[1,1556]},t(ss,[2,238],{113:622,323:634,98:At,111:yt,114:Nt,115:Ct,122:Rt,123:gr,132:wt,135:It,137:xt,138:Dt,139:Lt,140:kt,141:$t,153:Mt,169:Ut,170:_t,178:Ft,179:Pt,307:qt,308:Gt,309:Vt,311:Bt,312:jt,313:Ht,314:Jt,315:Yt,316:Wt,317:Xt,318:Kt,319:Qt,320:zt,321:Zt,322:en,326:tn,327:nn,328:rn,329:an}),{94:[1,1557],98:At,111:yt,113:622,114:Nt,115:Ct,122:Rt,123:gr,132:wt,135:It,137:xt,138:Dt,139:Lt,140:kt,141:$t,153:Mt,169:Ut,170:_t,178:Ft,179:Pt,307:qt,308:Gt,309:Vt,311:Bt,312:jt,313:Ht,314:Jt,315:Yt,316:Wt,317:Xt,318:Kt,319:Qt,320:zt,321:Zt,322:en,323:634,326:tn,327:nn,328:rn,329:an},{94:[1,1558]},t(Ts,[2,202]),t(Ts,[2,203]),t(rs,[2,177]),t(Ts,[2,236],{217:1559,229:[1,1560],230:[1,1561]}),t(Bs,[2,207],{3:1562,2:r,4:a,5:s,75:[1,1563]}),t(js,[2,1088],{218:1564,75:[1,1565]}),{2:r,3:1566,4:a,5:s,75:[1,1567]},{40:1568,78:74,88:c,183:99,188:f},t(Bs,[2,215],{3:1569,2:r,4:a,5:s,75:[1,1570]}),t(Bs,[2,218],{3:1571,2:r,4:a,5:s,75:[1,1572]}),{76:[1,1573]},t(Ns,[2,233]),{76:[1,1574]},t(Ns,[2,229]),t(Ns,[2,222]),{222:ys},{222:As},t(Ns,[2,224]),t(Ns,[2,225]),{222:[1,1575]},t(Ns,[2,227]),{222:[1,1576]},{222:[1,1577]},t(Ns,[2,231]),t(Ns,[2,232]),{77:[1,1578],204:1464,205:os,207:us,214:1463,215:1340,220:1343,221:cs,222:ls,223:hs,224:ds,225:fs,226:ps,227:bs,228:Es},t(la,[2,90]),{2:r,3:166,4:a,5:s,57:163,76:oe,93:1359,110:147,112:151,130:ue,131:ce,136:le,142:he,143:159,144:de,148:fe,151:pe,153:be,155:z,157:165,178:Ee,179:ge,180:me,195:149,199:145,200:153,201:154,240:1579,253:148,254:144,255:146,256:150,257:152,258:155,259:156,260:157,261:160,262:161,264:ve,265:p,266:Se,267:Te,269:Ae,276:ye,277:Ne,278:Ce,279:Re,280:Oe,281:we,282:Ie,283:xe,284:De,286:Z,287:ee,288:te,289:ne,290:Le,291:ke,292:$e,293:Me,294:Ue,295:_e,298:Fe,299:Pe,308:qe,313:Ge,416:188,417:Ve,421:Be},t(la,[2,91]),t(Rs,[2,263]),{243:[1,1580]},t(ms,[2,141],{416:188,3:730,113:733,143:755,157:765,159:766,116:1581,
2:r,4:a,5:s,71:yn,75:Nn,76:Cn,111:Rn,114:Nt,115:Ct,117:On,121:wn,122:In,123:xn,127:Dn,128:Ln,129:kn,130:$n,131:Mn,132:Un,133:_n,134:Fn,135:Pn,136:qn,137:Gn,138:Vn,139:Bn,140:jn,141:Hn,142:Jn,144:Yn,145:Wn,147:Xn,148:Kn,149:Qn,151:zn,153:Zn,155:er,161:tr,163:nr,165:rr,167:ar,168:sr,169:ir,170:or,171:ur,172:cr,174:lr,184:hr,186:dr,276:ye,277:Ne,278:Ce,279:Re,280:Oe,281:we,282:Ie,283:xe,284:De,417:Ve,421:Be}),t(la,[2,131]),{73:br,77:[1,1582]},t(Ba,[2,451],{113:622,323:634,98:At,111:yt,114:Nt,115:Ct,122:Rt,123:gr,132:wt,135:It,137:xt,138:Dt,139:Lt,140:kt,141:$t,153:Mt,169:Ut,170:_t,178:Ft,179:Pt,307:qt,308:Gt,309:Vt,311:Bt,312:jt,313:Ht,314:Jt,315:Yt,316:Wt,317:Xt,318:Kt,319:Qt,320:zt,321:Zt,322:en,326:tn,327:nn,328:rn,329:an}),t(Q,[2,563]),t(Tr,[2,525]),t(Tr,[2,537],{362:1509,388:1583,153:Os,186:ws,229:Is,293:xs,339:Ds,352:Ls,364:ks,365:$s,369:Ms,370:Us}),t(pr,[2,539]),{366:[1,1584]},{366:[1,1585]},{2:r,3:242,4:a,5:s,198:1586},t(pr,[2,545],{76:[1,1587]}),{2:r,3:114,4:a,5:s,76:[1,1589],112:250,130:ue,131:ce,142:he,151:pe,155:z,180:me,195:249,199:1590,200:253,256:251,257:252,264:et,270:1588,286:Z,287:ee,288:te,289:ne,290:Le,291:ke,292:$e,293:Me,298:Fe},t(pr,[2,549]),{293:[1,1591]},t(pr,[2,551]),t(pr,[2,552]),{331:[1,1592]},{76:[1,1593]},{2:r,3:1594,4:a,5:s},{77:[1,1595],98:At,111:yt,113:622,114:Nt,115:Ct,122:Rt,123:gr,132:wt,135:It,137:xt,138:Dt,139:Lt,140:kt,141:$t,153:Mt,169:Ut,170:_t,178:Ft,179:Pt,307:qt,308:Gt,309:Vt,311:Bt,312:jt,313:Ht,314:Jt,315:Yt,316:Wt,317:Xt,318:Kt,319:Qt,320:zt,321:Zt,322:en,323:634,326:tn,327:nn,328:rn,329:an},{77:[1,1596],98:At,111:yt,113:622,114:Nt,115:Ct,122:Rt,123:gr,132:wt,135:It,137:xt,138:Dt,139:Lt,140:kt,141:$t,153:Mt,169:Ut,170:_t,178:Ft,179:Pt,307:qt,308:Gt,309:Vt,311:Bt,312:jt,313:Ht,314:Jt,315:Yt,316:Wt,317:Xt,318:Kt,319:Qt,320:zt,321:Zt,322:en,323:634,326:tn,327:nn,328:rn,329:an},{77:[1,1597],98:At,111:yt,113:622,114:Nt,115:Ct,122:Rt,123:gr,132:wt,135:It,137:xt,138:Dt,139:Lt,140:kt,141:$t,153:Mt,169:Ut,170:_t,178:Ft,179:Pt,307:qt,308:Gt,309:Vt,311:Bt,312:jt,313:Ht,314:Jt,315:Yt,316:Wt,317:Xt,318:Kt,319:Qt,320:zt,321:Zt,322:en,323:634,326:tn,327:nn,328:rn,329:an},{77:[1,1598],98:At,111:yt,113:622,114:Nt,115:Ct,122:Rt,123:gr,132:wt,135:It,137:xt,138:Dt,139:Lt,140:kt,141:$t,153:Mt,169:Ut,170:_t,178:Ft,179:Pt,307:qt,308:Gt,309:Vt,311:Bt,312:jt,313:Ht,314:Jt,315:Yt,316:Wt,317:Xt,318:Kt,319:Qt,320:zt,321:Zt,322:en,323:634,326:tn,327:nn,328:rn,329:an},t(Q,Ha,{403:1599,75:Ja}),t(Q,[2,582]),{73:Cs,77:[1,1600]},{2:r,3:1110,4:a,5:s,131:pt,136:bt,142:nt,144:rt,151:Et,428:584,472:1112,475:1601,479:581,490:578,494:580},t(Tr,[2,728]),t(Q,[2,484],{348:1602,350:1603,351:1604,4:Hs,242:Js,339:Ys,352:Ws}),t(Xs,Ks,{3:1254,355:1609,380:1610,356:1611,357:1612,2:r,4:a,5:s,363:Qs}),{77:[2,498]},{75:[1,1614]},t(Q,[2,600]),t(Q,[2,1127]),{364:[1,1616],410:[1,1615]},t($a,[2,721]),t(Q,n,{17:5,18:7,19:8,20:9,21:10,22:11,23:12,24:13,25:14,26:15,27:16,28:17,29:18,30:19,31:20,32:21,33:22,34:23,35:24,36:25,37:26,38:27,39:28,40:29,41:30,42:31,43:32,44:33,45:34,46:35,47:36,48:37,49:38,50:39,51:40,52:41,54:43,55:44,56:45,57:46,58:47,59:48,60:49,61:50,62:51,63:52,64:53,65:54,66:55,67:56,68:57,69:58,70:59,78:74,501:95,183:99,3:100,12:1617,2:r,4:a,5:s,53:o,71:u,88:c,123:l,145:h,155:d,188:f,265:p,286:b,331:E,334:g,335:m,343:v,392:S,396:T,397:y,400:N,402:C,404:R,405:O,413:w,414:x,415:D,431:L,433:k,434:$,436:M,437:U,438:_,439:F,440:P,444:q,445:G,448:V,449:B,502:j,504:H,505:J,514:Y}),t(Q,[2,755]),t(Fs,[2,1175]),t(Q,n,{17:5,18:7,19:8,20:9,21:10,22:11,23:12,24:13,25:14,26:15,27:16,28:17,29:18,30:19,31:20,32:21,33:22,34:23,35:24,36:25,37:26,38:27,39:28,40:29,41:30,42:31,43:32,44:33,45:34,46:35,47:36,48:37,49:38,50:39,51:40,52:41,54:43,55:44,56:45,57:46,58:47,59:48,60:49,61:50,62:51,63:52,64:53,65:54,66:55,67:56,68:57,69:58,70:59,78:74,501:95,183:99,3:100,12:1618,2:r,4:a,5:s,53:o,71:u,88:c,123:l,145:h,155:d,188:f,265:p,286:b,331:E,334:g,335:m,343:v,392:S,396:T,397:y,400:N,402:C,404:R,405:O,413:w,414:x,415:D,431:L,433:k,434:$,436:M,437:U,438:_,439:F,440:P,444:q,445:G,448:V,449:B,502:j,504:H,505:J,514:Y}),t(Fs,[2,1177]),{77:[1,1619]},t(ms,[2,471],{113:622,323:634,98:At,111:yt,114:Nt,115:Ct,122:Rt,123:gr,132:wt,135:It,137:xt,138:Dt,139:Lt,140:kt,141:$t,153:Mt,169:Ut,170:_t,178:Ft,179:Pt,307:qt,308:Gt,309:Vt,311:Bt,312:jt,313:Ht,314:Jt,315:Yt,316:Wt,317:Xt,318:Kt,319:Qt,320:zt,321:Zt,322:en,326:tn,327:nn,328:rn,329:an}),{77:[1,1620],117:Ps,274:1621},{77:[1,1622]},{118:[1,1623]},{118:[1,1624]},{77:[1,1625]},{77:[1,1626]},{73:vs,77:[1,1627]},t($a,[2,458],{73:es}),{2:r,3:242,4:a,5:s,142:nt,144:rt,198:1629,428:1628},t(as,[2,696]),t(as,[2,698]),{145:[1,1630]},{98:At,111:yt,113:622,114:Nt,115:Ct,122:Rt,123:gr,132:wt,135:It,137:xt,138:Dt,139:Lt,140:kt,141:$t,153:Mt,169:Ut,170:_t,178:Ft,179:Pt,305:[1,1631],307:qt,308:Gt,309:Vt,311:Bt,312:jt,313:Ht,314:Jt,315:Yt,316:Wt,317:Xt,318:Kt,319:Qt,320:zt,321:Zt,322:en,323:634,326:tn,327:nn,328:rn,329:an},{335:zs,459:1632},{414:[1,1635],460:[1,1634]},{2:r,3:166,4:a,5:s,57:163,76:oe,93:1636,110:147,112:151,130:ue,131:ce,136:le,142:he,143:159,144:de,148:fe,151:pe,153:be,155:z,157:165,178:Ee,179:ge,180:me,195:149,199:145,200:153,201:154,253:148,254:144,255:146,256:150,257:152,258:155,259:156,260:157,261:160,262:161,264:ve,265:p,266:Se,267:Te,269:Ae,276:ye,277:Ne,278:Ce,279:Re,280:Oe,281:we,282:Ie,283:xe,284:De,286:Z,287:ee,288:te,289:ne,290:Le,291:ke,292:$e,293:Me,294:Ue,295:_e,298:Fe,299:Pe,308:qe,313:Ge,416:188,417:Ve,421:Be},t(Zs,[2,266],{86:1637,244:[1,1638],246:[1,1639]}),{118:[1,1640]},{2:r,3:166,4:a,5:s,57:163,76:oe,93:1646,110:147,112:151,130:ue,131:ce,136:le,142:he,143:159,144:de,148:fe,151:pe,153:be,155:z,157:165,178:Ee,179:ge,180:me,195:149,199:145,200:153,201:154,232:1641,234:1642,235:ei,236:ti,237:ni,253:148,254:144,255:146,256:150,257:152,258:155,259:156,260:157,261:160,262:161,264:ve,265:p,266:Se,267:Te,269:Ae,276:ye,277:Ne,278:Ce,279:Re,280:Oe,281:we,282:Ie,283:xe,284:De,286:Z,287:ee,288:te,289:ne,290:Le,291:ke,292:$e,293:Me,294:Ue,295:_e,298:Fe,299:Pe,308:qe,313:Ge,416:188,417:Ve,421:Be},{2:r,3:1647,4:a,5:s},{2:r,3:1648,4:a,5:s},t(Ts,[2,206]),{2:r,3:166,4:a,5:s,57:163,76:oe,93:1649,110:147,112:151,130:ue,131:ce,136:le,142:he,143:159,144:de,148:fe,151:pe,153:be,155:z,157:165,178:Ee,179:ge,180:me,195:149,199:145,200:153,201:154,253:148,254:144,255:146,256:150,257:152,258:155,259:156,260:157,261:160,262:161,264:ve,265:p,266:Se,267:Te,269:Ae,276:ye,277:Ne,278:Ce,279:Re,280:Oe,281:we,282:Ie,283:xe,284:De,286:Z,287:ee,288:te,289:ne,290:Le,291:ke,292:$e,293:Me,294:Ue,295:_e,298:Fe,299:Pe,308:qe,313:Ge,416:188,417:Ve,421:Be},{2:r,3:999,4:a,5:s,99:1650,110:1180},t(Bs,[2,208]),{2:r,3:1651,4:a,5:s},t(Bs,[2,1090],{219:1652,3:1653,2:r,4:a,5:s}),t(js,[2,1089]),t(Bs,[2,211]),{2:r,3:1654,4:a,5:s},{77:[1,1655]},t(Bs,[2,216]),{2:r,3:1656,4:a,5:s},t(Bs,[2,219]),{2:r,3:1657,4:a,5:s},{40:1658,78:74,88:c,183:99,188:f},{40:1659,78:74,88:c,183:99,188:f},t(Ns,[2,226]),t(Ns,[2,228]),t(Ns,[2,230]),t(aa,[2,170]),t(Rs,[2,261]),t(Rs,[2,264],{241:[1,1660]}),t(Pa,[2,1077],{152:1003,178:oa,179:ua,180:ca}),t(la,[2,140]),t(pr,[2,538]),t(pr,[2,541]),{370:[1,1661]},t(pr,[2,1120],{391:1662,389:1663,76:ri}),{130:ue,195:1665},t(pr,[2,546]),{2:r,3:166,4:a,5:s,57:163,76:oe,93:1666,110:147,112:151,130:ue,131:ce,136:le,142:he,143:159,144:de,148:fe,151:pe,153:be,155:z,157:165,178:Ee,179:ge,180:me,195:149,199:145,200:153,201:154,253:148,254:144,255:146,256:150,257:152,258:155,259:156,260:157,261:160,262:161,264:ve,265:p,266:Se,267:Te,269:Ae,276:ye,277:Ne,278:Ce,279:Re,280:Oe,281:we,282:Ie,283:xe,284:De,286:Z,287:ee,288:te,289:ne,290:Le,291:ke,292:$e,293:Me,294:Ue,295:_e,298:Fe,299:Pe,308:qe,313:Ge,416:188,417:Ve,421:Be},t(pr,[2,548]),t(pr,[2,550]),{2:r,3:114,4:a,5:s,76:[1,1668],112:250,130:ue,131:ce,142:he,151:pe,155:z,180:me,195:249,199:254,200:253,256:251,257:252,264:et,270:1667,286:Z,287:ee,288:te,289:ne,290:Le,291:ke,292:$e,293:Me,298:Fe},{2:r,3:166,4:a,5:s,57:163,76:oe,93:1669,110:147,112:151,130:ue,131:ce,136:le,142:he,143:159,144:de,148:fe,151:pe,153:be,155:z,157:165,178:Ee,179:ge,180:me,195:149,199:145,200:153,201:154,253:148,254:144,255:146,256:150,257:152,258:155,259:156,260:157,261:160,262:161,264:ve,265:p,266:Se,267:Te,269:Ae,276:ye,277:Ne,278:Ce,279:Re,280:Oe,281:we,282:Ie,283:xe,284:De,286:Z,287:ee,288:te,289:ne,290:Le,291:ke,292:$e,293:Me,294:Ue,295:_e,298:Fe,299:Pe,308:qe,313:Ge,416:188,417:Ve,421:Be},t(Q,[2,569]),t(on,[2,346]),t(on,[2,347]),t(on,[2,348]),t(on,[2,349]),t(Q,[2,573]),t(Q,[2,583]),t(Tr,[2,727]),t(Q,[2,480]),t(Q,[2,485],{351:1670,4:Hs,242:Js,339:Ys,352:Ws}),t(ai,[2,487]),t(ai,[2,488]),{123:[1,1671]},{123:[1,1672]},{123:[1,1673]},{73:[1,1674],77:[2,496]},t(ms,[2,524]),t(ms,[2,499]),{186:[1,1682],192:[1,1683],358:1675,359:1676,360:1677,361:1678,362:1679,364:ks,365:[1,1680],366:[1,1684],369:[1,1681]},{2:r,3:1685,4:a,5:s},{40:1686,78:74,88:c,183:99,188:f},{411:[1,1687]},{412:[1,1688]},t(Q,[2,754]),t(Q,[2,756]),t(Aa,[2,528]),t(ze,[2,328]),{77:[1,1689]},t(ze,[2,329]),{2:r,3:166,4:a,5:s,57:163,76:oe,93:1646,110:147,112:151,130:ue,131:ce,136:le,142:he,143:159,144:de,148:fe,151:pe,153:be,155:z,157:165,178:Ee,179:ge,180:me,195:149,199:145,200:153,201:154,232:1690,234:1642,235:ei,236:ti,237:ni,253:148,254:144,255:146,256:150,257:152,258:155,259:156,260:157,261:160,262:161,264:ve,265:p,266:Se,267:Te,269:Ae,276:ye,277:Ne,278:Ce,279:Re,280:Oe,281:we,282:Ie,283:xe,284:De,286:Z,287:ee,288:te,289:ne,290:Le,291:ke,292:$e,293:Me,294:Ue,295:_e,298:Fe,299:Pe,308:qe,313:Ge,416:188,417:Ve,421:Be},{2:r,3:166,4:a,5:s,57:163,76:oe,93:1359,110:147,112:151,119:1691,130:ue,131:ce,136:le,142:he,143:159,144:de,148:fe,151:pe,153:be,155:z,157:165,178:Ee,179:ge,180:me,195:149,199:145,200:153,201:154,240:1357,253:148,254:144,255:146,256:150,257:152,258:155,259:156,260:157,261:160,262:161,264:ve,265:p,266:Se,267:Te,269:Ae,276:ye,277:Ne,278:Ce,279:Re,280:Oe,281:we,282:Ie,283:xe,284:De,286:Z,287:ee,288:te,289:ne,290:Le,291:ke,292:$e,293:Me,294:Ue,295:_e,298:Fe,299:Pe,308:qe,313:Ge,416:188,417:Ve,421:Be},t(ze,[2,314]),t(ze,[2,316]),t(ts,[2,467]),{2:r,3:1692,4:a,5:s},t(Q,[2,713],{76:[1,1693]}),{2:r,3:999,4:a,5:s,110:1053,142:ha,144:da,146:1694,332:1052,333:1054},{331:qs,334:Gs,458:1695},t(as,[2,700]),{76:[1,1697],337:[1,1696],339:[1,1698]},{169:[1,1700],305:[1,1699]},{169:[1,1702],305:[1,1701]},{98:At,111:yt,113:622,114:Nt,115:Ct,122:Rt,123:gr,132:wt,135:It,137:xt,138:Dt,139:Lt,140:kt,141:$t,153:Mt,169:Ut,170:_t,178:Ft,179:Pt,305:[1,1703],307:qt,308:Gt,309:Vt,311:Bt,312:jt,313:Ht,314:Jt,315:Yt,316:Wt,317:Xt,318:Kt,319:Qt,320:zt,321:Zt,322:en,323:634,326:tn,327:nn,328:rn,329:an},t(ia,[2,249],{87:1704,161:[1,1705],167:[1,1707],168:[1,1706]}),{130:ue,195:1708},{130:ue,195:1709},{2:r,3:166,4:a,5:s,57:163,76:oe,93:1359,110:147,112:151,119:1710,130:ue,131:ce,136:le,142:he,143:159,144:de,148:fe,151:pe,153:be,155:z,157:165,178:Ee,179:ge,180:me,195:149,199:145,200:153,201:154,240:1357,253:148,254:144,255:146,256:150,257:152,258:155,259:156,260:157,261:160,262:161,264:ve,265:p,266:Se,267:Te,269:Ae,276:ye,277:Ne,278:Ce,279:Re,280:Oe,281:we,282:Ie,283:xe,284:De,286:Z,287:ee,288:te,289:ne,290:Le,291:ke,292:$e,293:Me,294:Ue,295:_e,298:Fe,299:Pe,308:qe,313:Ge,416:188,417:Ve,421:Be},t(Ss,[2,247],{233:1711,73:si,238:[1,1713]}),t(ii,[2,241]),{145:[1,1714]},{76:[1,1715]},{76:[1,1716]},t(ii,[2,246],{113:622,323:634,98:At,111:yt,114:Nt,115:Ct,122:Rt,123:gr,132:wt,135:It,137:xt,138:Dt,139:Lt,140:kt,141:$t,153:Mt,169:Ut,170:_t,178:Ft,179:Pt,307:qt,308:Gt,309:Vt,311:Bt,312:jt,313:Ht,314:Jt,315:Yt,316:Wt,317:Xt,318:Kt,319:Qt,320:zt,321:Zt,322:en,326:tn,327:nn,328:rn,329:an}),{77:[2,1044],95:1717,98:[1,1719],101:1718},{98:[1,1720]},t(Ts,[2,234],{113:622,323:634,98:At,111:yt,114:Nt,115:Ct,122:Rt,123:gr,132:wt,135:It,137:xt,138:Dt,139:Lt,140:kt,141:$t,153:Mt,169:Ut,170:_t,178:Ft,179:Pt,307:qt,308:Gt,309:Vt,311:Bt,312:jt,313:Ht,314:Jt,315:Yt,316:Wt,317:Xt,318:Kt,319:Qt,320:zt,321:Zt,322:en,326:tn,327:nn,328:rn,329:an}),t(Ts,[2,235],{73:ns}),t(Bs,[2,209]),t(Bs,[2,210]),t(Bs,[2,1091]),t(Bs,[2,212]),{2:r,3:1721,4:a,5:s,75:[1,1722]},t(Bs,[2,217]),t(Bs,[2,220]),{77:[1,1723]},{77:[1,1724]},t(Rs,[2,265]),{2:r,3:242,4:a,5:s,198:1725},t(pr,[2,543]),t(pr,[2,1121]),{2:r,3:1726,4:a,5:s},{73:[1,1727]},{77:[1,1728],98:At,111:yt,113:622,114:Nt,115:Ct,122:Rt,123:gr,132:wt,135:It,137:xt,138:Dt,139:Lt,140:kt,141:$t,153:Mt,169:Ut,170:_t,178:Ft,179:Pt,307:qt,308:Gt,309:Vt,311:Bt,312:jt,313:Ht,314:Jt,315:Yt,316:Wt,317:Xt,318:Kt,319:Qt,320:zt,321:Zt,322:en,323:634,326:tn,327:nn,328:rn,329:an},t(pr,[2,553]),{2:r,3:166,4:a,5:s,57:163,76:oe,93:1729,110:147,112:151,130:ue,131:ce,136:le,142:he,143:159,144:de,148:fe,151:pe,153:be,155:z,157:165,178:Ee,179:ge,180:me,195:149,199:145,200:153,201:154,253:148,254:144,255:146,256:150,257:152,258:155,259:156,260:157,261:160,262:161,264:ve,265:p,266:Se,267:Te,269:Ae,276:ye,277:Ne,278:Ce,279:Re,280:Oe,281:we,282:Ie,283:xe,284:De,286:Z,287:ee,288:te,289:ne,290:Le,291:ke,292:$e,293:Me,294:Ue,295:_e,298:Fe,299:Pe,308:qe,313:Ge,416:188,417:Ve,421:Be},{77:[1,1730],98:At,111:yt,113:622,114:Nt,115:Ct,122:Rt,123:gr,132:wt,135:It,137:xt,138:Dt,139:Lt,140:kt,141:$t,153:Mt,169:Ut,170:_t,178:Ft,179:Pt,307:qt,308:Gt,309:Vt,311:Bt,312:jt,313:Ht,314:Jt,315:Yt,316:Wt,317:Xt,318:Kt,319:Qt,320:zt,321:Zt,322:en,323:634,326:tn,327:nn,328:rn,329:an},t(ai,[2,486]),{2:r,3:1731,4:a,5:s},{130:ue,195:1732},{2:r,3:1733,4:a,5:s},t(Xs,Ks,{357:1612,356:1734,363:Qs}),t(Tr,[2,501]),t(Tr,[2,502]),t(Tr,[2,503]),t(Tr,[2,504]),t(Tr,[2,505]),{366:[1,1735]},{366:[1,1736]},t(oi,[2,1114],{378:1737,366:[1,1738]}),{2:r,3:1739,4:a,5:s},{2:r,3:1740,4:a,5:s},t(Xs,[2,507]),t(Q,[2,1124],{407:1741,409:1742,71:_s}),t(Q,[2,601]),t(Q,[2,602],{363:[1,1743]}),t(ze,[2,330]),t([77,117],[2,331],{73:si}),{73:Cs,77:[2,332]},t(Q,[2,712]),{2:r,3:999,4:a,5:s,99:1744,110:1180},t(as,[2,699],{73:Va}),t(as,[2,697]),{76:ka,142:he,143:1176,144:Ze,151:pe,180:me,200:1177,298:Fe,338:1745,416:188,417:Ve,421:Be},{2:r,3:999,4:a,5:s,99:1746,110:1180},{337:[1,1747]},{335:zs,459:1748},{2:r,3:166,4:a,5:s,57:163,76:oe,93:1749,110:147,112:151,130:ue,131:ce,136:le,142:he,143:159,144:de,148:fe,151:pe,153:be,155:z,157:165,178:Ee,179:ge,180:me,195:149,199:145,200:153,201:154,253:148,254:144,255:146,256:150,257:152,258:155,259:156,260:157,261:160,262:161,264:ve,265:p,266:Se,267:Te,269:Ae,276:ye,277:Ne,278:Ce,279:Re,280:Oe,281:we,282:Ie,283:xe,284:De,286:Z,287:ee,288:te,289:ne,290:Le,291:ke,292:$e,293:Me,294:Ue,295:_e,298:Fe,299:Pe,308:qe,313:Ge,416:188,417:Ve,421:Be},{335:zs,459:1750},{2:r,3:166,4:a,5:s,57:163,76:oe,93:1751,110:147,112:151,130:ue,131:ce,136:le,142:he,143:159,144:de,148:fe,151:pe,153:be,155:z,157:165,178:Ee,179:ge,180:me,195:149,199:145,200:153,201:154,253:148,254:144,255:146,256:150,257:152,258:155,259:156,260:157,261:160,262:161,264:ve,265:p,266:Se,267:Te,269:Ae,276:ye,277:Ne,278:Ce,279:Re,280:Oe,281:we,282:Ie,283:xe,284:De,286:Z,287:ee,288:te,289:ne,290:Le,291:ke,292:$e,293:Me,294:Ue,295:_e,298:Fe,299:Pe,308:qe,313:Ge,416:188,417:Ve,421:Be},{335:zs,459:1752},t(ia,[2,71]),{40:1753,78:74,88:c,163:[1,1754],183:99,188:f,239:[1,1755]},{40:1756,78:74,88:c,183:99,188:f,239:[1,1757]},{40:1758,78:74,88:c,183:99,188:f,239:[1,1759]},t(Zs,[2,269],{245:1760,246:[1,1761]}),{247:1762,248:[2,1092],763:[1,1763]},t(Vs,[2,259],{73:Cs}),t(Ss,[2,240]),{2:r,3:166,4:a,5:s,57:163,76:oe,93:1646,110:147,112:151,130:ue,131:ce,136:le,142:he,143:159,144:de,148:fe,151:pe,153:be,155:z,157:165,178:Ee,179:ge,180:me,195:149,199:145,200:153,201:154,234:1764,235:ei,236:ti,237:ni,253:148,254:144,255:146,256:150,257:152,258:155,259:156,260:157,261:160,262:161,264:ve,265:p,266:Se,267:Te,269:Ae,276:ye,277:Ne,278:Ce,279:Re,280:Oe,281:we,282:Ie,283:xe,284:De,286:Z,287:ee,288:te,289:ne,290:Le,291:ke,292:$e,293:Me,294:Ue,295:_e,298:Fe,299:Pe,308:qe,313:Ge,416:188,417:Ve,421:Be},{2:r,3:166,4:a,5:s,57:163,76:oe,93:1765,110:147,112:151,130:ue,131:ce,136:le,142:he,143:159,144:de,148:fe,151:pe,153:be,155:z,157:165,178:Ee,179:ge,180:me,195:149,199:145,200:153,201:154,253:148,254:144,255:146,256:150,257:152,258:155,259:156,260:157,261:160,262:161,264:ve,265:p,266:Se,267:Te,269:Ae,276:ye,277:Ne,278:Ce,279:Re,280:Oe,281:we,282:Ie,283:xe,284:De,286:Z,287:ee,288:te,289:ne,290:Le,291:ke,292:$e,293:Me,294:Ue,295:_e,298:Fe,299:Pe,308:qe,313:Ge,416:188,417:Ve,421:Be},{76:[1,1766]},{2:r,3:166,4:a,5:s,57:163,76:oe,93:1646,110:147,112:151,130:ue,131:ce,136:le,142:he,143:159,144:de,148:fe,151:pe,153:be,155:z,157:165,178:Ee,179:ge,180:me,195:149,199:145,200:153,201:154,232:1767,234:1642,235:ei,236:ti,237:ni,253:148,254:144,255:146,256:150,257:152,258:155,259:156,260:157,261:160,262:161,264:ve,265:p,266:Se,267:Te,269:Ae,276:ye,277:Ne,278:Ce,279:Re,280:Oe,281:we,282:Ie,283:xe,284:De,286:Z,287:ee,288:te,289:ne,290:Le,291:ke,292:$e,293:Me,294:Ue,295:_e,298:Fe,299:Pe,308:qe,313:Ge,416:188,417:Ve,421:Be},{2:r,3:166,4:a,5:s,57:163,76:oe,93:1646,110:147,112:151,130:ue,131:ce,136:le,142:he,143:159,144:de,148:fe,151:pe,153:be,155:z,157:165,178:Ee,179:ge,180:me,195:149,199:145,200:153,201:154,232:1768,234:1642,235:ei,236:ti,237:ni,253:148,254:144,255:146,256:150,257:152,258:155,259:156,260:157,261:160,262:161,264:ve,265:p,266:Se,267:Te,269:Ae,276:ye,277:Ne,278:Ce,279:Re,280:Oe,281:we,282:Ie,283:xe,284:De,286:Z,287:ee,288:te,289:ne,290:Le,291:ke,292:$e,293:Me,294:Ue,295:_e,298:Fe,299:Pe,308:qe,313:Ge,416:188,417:Ve,421:Be},{77:[1,1769]},{77:[2,1045]},{76:[1,1770]},{76:[1,1771]},t(Bs,[2,213]),{2:r,3:1772,4:a,5:s},{2:r,3:1773,4:a,5:s,75:[1,1774]},{2:r,3:1775,4:a,5:s,75:[1,1776]},t(pr,[2,1118],{390:1777,389:1778,76:ri}),{77:[1,1779]},{130:ue,195:1780},t(pr,[2,547]),{77:[1,1781],98:At,111:yt,113:622,114:Nt,115:Ct,122:Rt,123:gr,132:wt,135:It,137:xt,138:Dt,139:Lt,140:kt,141:$t,153:Mt,169:Ut,170:_t,178:Ft,179:Pt,307:qt,308:Gt,309:Vt,311:Bt,312:jt,313:Ht,314:Jt,315:Yt,316:Wt,317:Xt,318:Kt,319:Qt,320:zt,321:Zt,322:en,323:634,326:tn,327:nn,328:rn,329:an},t(pr,[2,508]),t(ai,[2,489]),t(ai,[2,490]),t(ai,[2,491]),t(ms,[2,500]),{2:r,3:1783,4:a,5:s,76:[2,1110],367:1782},{76:[1,1784]},{2:r,3:1786,4:a,5:s,76:[2,1116],379:1785},t(oi,[2,1115]),{76:[1,1787]},{76:[1,1788]},t(Q,[2,599]),t(Q,[2,1125]),t(Xs,Ks,{357:1612,356:1789,363:Qs}),{73:ns,77:[1,1790]},t(as,[2,706],{73:es}),{73:ns,77:[1,1791]},t(as,[2,708]),t(as,[2,701]),{98:At,111:yt,113:622,114:Nt,115:Ct,122:Rt,123:gr,132:wt,135:It,137:xt,138:Dt,139:Lt,140:kt,141:$t,153:Mt,169:Ut,170:_t,178:Ft,179:Pt,305:[1,1792],307:qt,308:Gt,309:Vt,311:Bt,312:jt,313:Ht,314:Jt,315:Yt,316:Wt,317:Xt,318:Kt,319:Qt,320:zt,321:Zt,322:en,323:634,326:tn,327:nn,328:rn,329:an},t(as,[2,704]),{98:At,111:yt,113:622,114:Nt,115:Ct,122:Rt,123:gr,132:wt,135:It,137:xt,138:Dt,139:Lt,140:kt,141:$t,153:Mt,169:Ut,170:_t,178:Ft,179:Pt,305:[1,1793],307:qt,308:Gt,309:Vt,311:Bt,312:jt,313:Ht,314:Jt,315:Yt,316:Wt,317:Xt,318:Kt,319:Qt,320:zt,321:Zt,322:en,323:634,326:tn,327:nn,328:rn,329:an},t(as,[2,702]),t(ia,[2,250]),{40:1794,78:74,88:c,183:99,188:f,239:[1,1795]},{40:1796,78:74,88:c,183:99,188:f},t(ia,[2,252]),{40:1797,78:74,88:c,183:99,188:f},t(ia,[2,253]),{40:1798,78:74,88:c,183:99,188:f},t(Zs,[2,267]),{130:ue,195:1799},{248:[1,1800]},{248:[2,1093]},t(ii,[2,242]),t(Ss,[2,248],{113:622,323:634,98:At,111:yt,114:Nt,115:Ct,122:Rt,123:gr,132:wt,135:It,137:xt,138:Dt,139:Lt,140:kt,141:$t,153:Mt,169:Ut,170:_t,178:Ft,179:Pt,307:qt,308:Gt,309:Vt,311:Bt,312:jt,313:Ht,314:Jt,315:Yt,316:Wt,317:Xt,318:Kt,319:Qt,320:zt,321:Zt,322:en,326:tn,327:nn,328:rn,329:an}),{2:r,3:166,4:a,5:s,57:163,76:oe,93:1646,110:147,112:151,130:ue,131:ce,136:le,142:he,143:159,144:de,148:fe,151:pe,153:be,155:z,157:165,178:Ee,179:ge,180:me,195:149,199:145,200:153,201:154,232:1801,234:1642,235:ei,236:ti,237:ni,253:148,254:144,255:146,256:150,257:152,258:155,259:156,260:157,261:160,262:161,264:ve,265:p,266:Se,267:Te,269:Ae,276:ye,277:Ne,278:Ce,279:Re,280:Oe,281:we,282:Ie,283:xe,284:De,286:Z,287:ee,288:te,289:ne,290:Le,291:ke,292:$e,293:Me,294:Ue,295:_e,298:Fe,299:Pe,308:qe,313:Ge,416:188,417:Ve,421:Be},{73:si,77:[1,1802]},{73:si,77:[1,1803]},t(Ua,[2,1046],{96:1804,103:1805,3:1807,2:r,4:a,5:s,75:ui}),{2:r,3:166,4:a,5:s,57:163,76:oe,93:1810,102:1808,104:1809,110:147,112:151,130:ue,131:ce,136:le,142:he,143:159,144:de,148:fe,151:pe,153:be,155:z,157:165,178:Ee,179:ge,180:me,195:149,199:145,200:153,201:154,253:148,254:144,255:146,256:150,257:152,258:155,259:156,260:157,261:160,262:161,264:ve,265:p,266:Se,267:Te,269:Ae,276:ye,277:Ne,278:Ce,279:Re,280:Oe,281:we,282:Ie,283:xe,284:De,286:Z,287:ee,288:te,289:ne,290:Le,291:ke,292:$e,293:Me,294:Ue,295:_e,298:Fe,299:Pe,308:qe,313:Ge,416:188,417:Ve,421:Be},{2:r,3:999,4:a,5:s,99:1811,110:1180},t(Bs,[2,214]),t(Ts,[2,172]),{2:r,3:1812,4:a,5:s},t(Ts,[2,174]),{2:r,3:1813,4:a,5:s},t(pr,[2,542]),t(pr,[2,1119]),t(pr,[2,540]),{77:[1,1814]},t(pr,[2,554]),{76:[1,1815]},{76:[2,1111]},{2:r,3:1817,4:a,5:s,131:ci,368:1816},{76:[1,1819]},{76:[2,1117]},{2:r,3:999,4:a,5:s,99:1820,110:1180},{2:r,3:999,4:a,5:s,99:1821,110:1180},t(Q,[2,603]),t(Q,[2,714]),{337:[1,1822],339:[1,1823]},{335:zs,459:1824},{331:qs,334:Gs,458:1825},t(ia,[2,251]),{40:1826,78:74,88:c,183:99,188:f},t(ia,[2,254]),t(ia,[2,256]),t(ia,[2,257]),t(Zs,[2,270]),{130:[2,1094],249:1827,642:[1,1828]},{73:si,77:[1,1829]},t(ii,[2,244]),t(ii,[2,245]),t(Ua,[2,73]),t(Ua,[2,1047]),{2:r,3:1830,4:a,5:s},t(Ua,[2,77]),{73:[1,1832],77:[1,1831]},t(ms,[2,79]),t(ms,[2,80],{113:622,323:634,75:[1,1833],98:At,111:yt,114:Nt,115:Ct,122:Rt,123:gr,132:wt,135:It,137:xt,138:Dt,139:Lt,140:kt,141:$t,153:Mt,169:Ut,170:_t,178:Ft,179:Pt,307:qt,308:Gt,309:Vt,311:Bt,312:jt,313:Ht,314:Jt,315:Yt,316:Wt,317:Xt,318:Kt,319:Qt,320:zt,321:Zt,322:en,326:tn,327:nn,328:rn,329:an}),{73:ns,77:[1,1834]},t(Ts,[2,173]),t(Ts,[2,175]),t(pr,[2,544]),{2:r,3:1817,4:a,5:s,131:ci,368:1835},{73:li,77:[1,1836]},t(ms,[2,519]),t(ms,[2,520]),{2:r,3:999,4:a,5:s,99:1838,110:1180},{73:ns,77:[1,1839]},{73:ns,77:[1,1840]},{76:ka,142:he,143:1176,144:Ze,151:pe,180:me,200:1177,298:Fe,338:1841,416:188,417:Ve,421:Be},{337:[1,1842]},t(as,[2,703]),t(as,[2,705]),t(ia,[2,255]),{130:ue,195:1843},{130:[2,1095]},t(ii,[2,243]),t(Ua,[2,76]),{77:[2,75]},{2:r,3:166,4:a,5:s,57:163,76:oe,93:1810,104:1844,110:147,112:151,130:ue,131:ce,136:le,142:he,143:159,144:de,148:fe,151:pe,153:be,155:z,157:165,178:Ee,179:ge,180:me,195:149,199:145,200:153,201:154,253:148,254:144,255:146,256:150,257:152,258:155,259:156,260:157,261:160,262:161,264:ve,265:p,266:Se,267:Te,269:Ae,276:ye,277:Ne,278:Ce,279:Re,280:Oe,281:we,282:Ie,283:xe,284:De,286:Z,287:ee,288:te,289:ne,290:Le,291:ke,292:$e,293:Me,294:Ue,295:_e,298:Fe,299:Pe,308:qe,313:Ge,416:188,417:Ve,421:Be},{2:r,3:1845,4:a,5:s},{77:[1,1846]},{73:li,77:[1,1847]},{370:[1,1848]},{2:r,3:1849,4:a,5:s,131:[1,1850]},{73:ns,77:[1,1851]},t(Tr,[2,517]),t(Tr,[2,518]),t(as,[2,707],{73:es}),t(as,[2,709]),t(hi,[2,1096],{250:1852,763:[1,1853]}),t(ms,[2,78]),t(ms,[2,81]),t(Ua,[2,1048],{3:1807,100:1854,103:1855,2:r,4:a,5:s,75:ui}),t(Tr,[2,509]),{2:r,3:242,4:a,5:s,198:1856},t(ms,[2,521]),t(ms,[2,522]),t(Tr,[2,516]),t(Zs,[2,1098],{251:1857,411:[1,1858]}),t(hi,[2,1097]),t(Ua,[2,74]),t(Ua,[2,1049]),t(di,[2,1112],{371:1859,373:1860,76:[1,1861]}),t(Zs,[2,268]),t(Zs,[2,1099]),t(Tr,[2,512],{372:1862,374:1863,229:[1,1864]}),t(di,[2,1113]),{2:r,3:1817,4:a,5:s,131:ci,368:1865},t(Tr,[2,510]),{229:[1,1867],375:1866},{334:[1,1868]},{73:li,77:[1,1869]},t(Tr,[2,513]),{331:[1,1870]},{376:[1,1871]},t(di,[2,511]),{376:[1,1872]},{377:[1,1873]},{377:[1,1874]},{229:[2,514]},t(Tr,[2,515])],defaultActions:{105:[2,6],192:[2,333],193:[2,334],194:[2,335],195:[2,336],196:[2,337],197:[2,338],198:[2,339],199:[2,340],200:[2,341],207:[2,688],590:[2,1135],650:[2,1100],651:[2,1101],707:[2,689],777:[2,1066],778:[2,1067],921:[2,443],922:[2,444],923:[2,445],975:[2,690],1274:[2,1145],1358:[2,1053],1416:[2,1143],1525:[2,498],1718:[2,1045],1763:[2,1093],1783:[2,1111],1786:[2,1117],1828:[2,1095],1831:[2,75],1873:[2,514]},parseError:function(e,t){function n(e,t){this.message=e,this.hash=t}if(!t.recoverable)throw n.prototype=Error,new n(e,t);this.trace(e)},parse:function(e){function t(e){a.length=a.length-2*e,s.length=s.length-e,i.length=i.length-e}function n(e){for(var t=a.length-1,n=0;;){if(d.toString()in o[e])return n;if(0===e||t<2)return!1;t-=2,e=a[t],++n}}var r=this,a=[0],s=[null],i=[],o=this.table,u="",c=0,l=0,h=0,d=2,f=1,p=i.slice.call(arguments,1),b=Object.create(this.lexer),E={yy:{}};for(var g in this.yy)Object.prototype.hasOwnProperty.call(this.yy,g)&&(E.yy[g]=this.yy[g]);b.setInput(e,E.yy),E.yy.lexer=b,E.yy.parser=this,"undefined"==typeof b.yylloc&&(b.yylloc={});var m=b.yylloc;i.push(m);var v=b.options&&b.options.ranges;"function"==typeof E.yy.parseError?this.parseError=E.yy.parseError:this.parseError=Object.getPrototypeOf(this).parseError;for(var S,T,A,y,N,C,R,O,w,I=function(){var e;return e=b.lex()||f,"number"!=typeof e&&(e=r.symbols_[e]||e),e},x={};;){if(A=a[a.length-1],this.defaultActions[A]?y=this.defaultActions[A]:(null!==S&&"undefined"!=typeof S||(S=I()),y=o[A]&&o[A][S]),"undefined"==typeof y||!y.length||!y[0]){var D,L="";if(h)T!==f&&(D=n(A));else{D=n(A),w=[];for(C in o[A])this.terminals_[C]&&C>d&&w.push("'"+this.terminals_[C]+"'");L=b.showPosition?"Parse error on line "+(c+1)+":\n"+b.showPosition()+"\nExpecting "+w.join(", ")+", got '"+(this.terminals_[S]||S)+"'":"Parse error on line "+(c+1)+": Unexpected "+(S==f?"end of input":"'"+(this.terminals_[S]||S)+"'"),this.parseError(L,{text:b.match,token:this.terminals_[S]||S,line:b.yylineno,loc:m,expected:w,recoverable:D!==!1})}if(3==h){if(S===f||T===f)throw new Error(L||"Parsing halted while starting to recover from another error.");l=b.yyleng,u=b.yytext,c=b.yylineno,m=b.yylloc,S=I()}if(D===!1)throw new Error(L||"Parsing halted. No suitable error recovery rule available.");t(D),T=S==d?null:S,S=d,A=a[a.length-1],y=o[A]&&o[A][d],h=3}if(y[0]instanceof Array&&y.length>1)throw new Error("Parse Error: multiple actions possible at state: "+A+", token: "+S);switch(y[0]){case 1:a.push(S),s.push(b.yytext),i.push(b.yylloc),a.push(y[1]),S=null,T?(S=T,T=null):(l=b.yyleng,u=b.yytext,c=b.yylineno,m=b.yylloc,h>0&&h--);break;case 2:if(R=this.productions_[y[1]][1],x.$=s[s.length-R],x._$={first_line:i[i.length-(R||1)].first_line,last_line:i[i.length-1].last_line,first_column:i[i.length-(R||1)].first_column,last_column:i[i.length-1].last_column},v&&(x._$.range=[i[i.length-(R||1)].range[0],i[i.length-1].range[1]]),N=this.performAction.apply(x,[u,l,c,E.yy,y[1],s,i].concat(p)),"undefined"!=typeof N)return N;R&&(a=a.slice(0,-1*R*2),s=s.slice(0,-1*R),i=i.slice(0,-1*R)),a.push(this.productions_[y[1]][0]),s.push(x.$),i.push(x._$),O=o[a[a.length-2]][a[a.length-1]],a.push(O);break;case 3:return!0}}return!0}},pi=["A","ABSENT","ABSOLUTE","ACCORDING","ACTION","ADA","ADD","ADMIN","AFTER","ALWAYS","ASC","ASSERTION","ASSIGNMENT","ATTRIBUTE","ATTRIBUTES","BASE64","BEFORE","BERNOULLI","BLOCKED","BOM","BREADTH","C","CASCADE","CATALOG","CATALOG_NAME","CHAIN","CHARACTERISTICS","CHARACTERS","CHARACTER_SET_CATALOG","CHARACTER_SET_NAME","CHARACTER_SET_SCHEMA","CLASS_ORIGIN","COBOL","COLLATION","COLLATION_CATALOG","COLLATION_NAME","COLLATION_SCHEMA","COLUMNS","COLUMN_NAME","COMMAND_FUNCTION","COMMAND_FUNCTION_CODE","COMMITTED","CONDITION_NUMBER","CONNECTION","CONNECTION_NAME","CONSTRAINTS","CONSTRAINT_CATALOG","CONSTRAINT_NAME","CONSTRAINT_SCHEMA","CONSTRUCTOR","CONTENT","CONTINUE","CONTROL","CURSOR_NAME","DATA","DATETIME_INTERVAL_CODE","DATETIME_INTERVAL_PRECISION","DB","DEFAULTS","DEFERRABLE","DEFERRED","DEFINED","DEFINER","DEGREE","DEPTH","DERIVED","DESC","DESCRIPTOR","DIAGNOSTICS","DISPATCH","DOCUMENT","DOMAIN","DYNAMIC_FUNCTION","DYNAMIC_FUNCTION_CODE","EMPTY","ENCODING","ENFORCED","EXCLUDE","EXCLUDING","EXPRESSION","FILE","FINAL","FIRST","FLAG","FOLLOWING","FORTRAN","FOUND","FS","G","GENERAL","GENERATED","GO","GOTO","GRANTED","HEX","HIERARCHY","ID","IGNORE","IMMEDIATE","IMMEDIATELY","IMPLEMENTATION","INCLUDING","INCREMENT","INDENT","INITIALLY","INPUT","INSTANCE","INSTANTIABLE","INSTEAD","INTEGRITY","INVOKER","ISOLATION","K","KEY","KEY_MEMBER","KEY_TYPE","LAST","LENGTH","LEVEL","LIBRARY","LIMIT","LINK","LOCATION","LOCATOR","M","MAP","MAPPING","MATCHED","MAXVALUE","MESSAGE_LENGTH","MESSAGE_OCTET_LENGTH","MESSAGE_TEXT","MINVALUE","MORE","MUMPS","NAME","NAMES","NAMESPACE","NESTING","NEXT","NFC","NFD","NFKC","NFKD","NIL","NORMALIZED","NULLABLE","NULLS","NUMBER","OBJECT","OCTETS","OFF","OPTION","OPTIONS","ORDERING","ORDINALITY","OTHERS","OUTPUT","OVERRIDING","P","PAD","PARAMETER_MODE","PARAMETER_NAME","PARAMETER_ORDINAL_POSITION","PARAMETER_SPECIFIC_CATALOG","PARAMETER_SPECIFIC_NAME","PARAMETER_SPECIFIC_SCHEMA","PARTIAL","PASCAL","PASSING","PASSTHROUGH","PATH","PERMISSION","PLACING","PLI","PRECEDING","PRESERVE","PRIOR","PRIVILEGES","PUBLIC","READ","RECOVERY","RELATIVE","REPEATABLE","REQUIRING","RESPECT","RESTART","RESTORE","RESTRICT","RETURNED_CARDINALITY","RETURNED_LENGTH","RETURNED_OCTET_LENGTH","RETURNED_SQLSTATE","RETURNING","ROLE","ROUTINE","ROUTINE_CATALOG","ROUTINE_NAME","ROUTINE_SCHEMA","ROW_COUNT","SCALE","SCHEMA","SCHEMA_NAME","SCOPE_CATALOG","SCOPE_NAME","SCOPE_SCHEMA","SECTION","SECURITY","SELECTIVE","SELF","SEQUENCE","SERIALIZABLE","SERVER","SERVER_NAME","SESSION","SETS","SIMPLE","SIZE","SOURCE","SPACE","SPECIFIC_NAME","STANDALONE","STATE","STATEMENT","STRIP","STRUCTURE","STYLE","SUBCLASS_ORIGIN","T","TABLE_NAME","TEMPORARY","TIES","TOKEN","TOP_LEVEL_COUNT","TRANSACTION","TRANSACTIONS_COMMITTED","TRANSACTIONS_ROLLED_BACK","TRANSACTION_ACTIVE","TRANSFORM","TRANSFORMS","TRIGGER_CATALOG","TRIGGER_NAME","TRIGGER_SCHEMA","TYPE","UNBOUNDED","UNCOMMITTED","UNDER","UNLINK","UNNAMED","UNTYPED","URI","USAGE","USER_DEFINED_TYPE_CATALOG","USER_DEFINED_TYPE_CODE","USER_DEFINED_TYPE_NAME","USER_DEFINED_TYPE_SCHEMA","VALID","VERSION","VIEW","WHITESPACE","WORK","WRAPPER","WRITE","XMLDECLARATION","XMLSCHEMA","YES","ZONE"];fi.parseError=function(e,t){if(!(t.expected&&t.expected.indexOf("'LITERAL'")>-1&&/[a-zA-Z_][a-zA-Z_0-9]*/.test(t.token)&&pi.indexOf(t.token)>-1))throw new SyntaxError(e)};var bi=function(){var e={EOF:1,parseError:function(e,t){if(!this.yy.parser)throw new Error(e);this.yy.parser.parseError(e,t)},setInput:function(e,t){return this.yy=t||this.yy||{},this._input=e,this._more=this._backtrack=this.done=!1,this.yylineno=this.yyleng=0,this.yytext=this.matched=this.match="",this.conditionStack=["INITIAL"],this.yylloc={first_line:1,first_column:0,last_line:1,last_column:0},this.options.ranges&&(this.yylloc.range=[0,0]),this.offset=0,this},input:function(){var e=this._input[0];this.yytext+=e,this.yyleng++,this.offset++,this.match+=e,this.matched+=e;var t=e.match(/(?:\r\n?|\n).*/g);return t?(this.yylineno++,this.yylloc.last_line++):this.yylloc.last_column++,this.options.ranges&&this.yylloc.range[1]++,this._input=this._input.slice(1),e},unput:function(e){var t=e.length,n=e.split(/(?:\r\n?|\n)/g);this._input=e+this._input,this.yytext=this.yytext.substr(0,this.yytext.length-t),this.offset-=t;var r=this.match.split(/(?:\r\n?|\n)/g);this.match=this.match.substr(0,this.match.length-1),this.matched=this.matched.substr(0,this.matched.length-1),n.length-1&&(this.yylineno-=n.length-1);var a=this.yylloc.range;return this.yylloc={first_line:this.yylloc.first_line,last_line:this.yylineno+1,first_column:this.yylloc.first_column,last_column:n?(n.length===r.length?this.yylloc.first_column:0)+r[r.length-n.length].length-n[0].length:this.yylloc.first_column-t},this.options.ranges&&(this.yylloc.range=[a[0],a[0]+this.yyleng-t]),this.yyleng=this.yytext.length,this},more:function(){return this._more=!0,this},reject:function(){return this.options.backtrack_lexer?(this._backtrack=!0,this):this.parseError("Lexical error on line "+(this.yylineno+1)+". You can only invoke reject() in the lexer when the lexer is of the backtracking persuasion (options.backtrack_lexer = true).\n"+this.showPosition(),{text:"",token:null,line:this.yylineno})},less:function(e){this.unput(this.match.slice(e))},pastInput:function(){var e=this.matched.substr(0,this.matched.length-this.match.length);return(e.length>20?"...":"")+e.substr(-20).replace(/\n/g,"")},upcomingInput:function(){var e=this.match;return e.length<20&&(e+=this._input.substr(0,20-e.length)),(e.substr(0,20)+(e.length>20?"...":"")).replace(/\n/g,"");
},showPosition:function(){var e=this.pastInput(),t=new Array(e.length+1).join("-");return e+this.upcomingInput()+"\n"+t+"^"},test_match:function(e,t){var n,r,a;if(this.options.backtrack_lexer&&(a={yylineno:this.yylineno,yylloc:{first_line:this.yylloc.first_line,last_line:this.last_line,first_column:this.yylloc.first_column,last_column:this.yylloc.last_column},yytext:this.yytext,match:this.match,matches:this.matches,matched:this.matched,yyleng:this.yyleng,offset:this.offset,_more:this._more,_input:this._input,yy:this.yy,conditionStack:this.conditionStack.slice(0),done:this.done},this.options.ranges&&(a.yylloc.range=this.yylloc.range.slice(0))),r=e[0].match(/(?:\r\n?|\n).*/g),r&&(this.yylineno+=r.length),this.yylloc={first_line:this.yylloc.last_line,last_line:this.yylineno+1,first_column:this.yylloc.last_column,last_column:r?r[r.length-1].length-r[r.length-1].match(/\r?\n?/)[0].length:this.yylloc.last_column+e[0].length},this.yytext+=e[0],this.match+=e[0],this.matches=e,this.yyleng=this.yytext.length,this.options.ranges&&(this.yylloc.range=[this.offset,this.offset+=this.yyleng]),this._more=!1,this._backtrack=!1,this._input=this._input.slice(e[0].length),this.matched+=e[0],n=this.performAction.call(this,this.yy,this,t,this.conditionStack[this.conditionStack.length-1]),this.done&&this._input&&(this.done=!1),n)return n;if(this._backtrack){for(var s in a)this[s]=a[s];return!1}return!1},next:function(){if(this.done)return this.EOF;this._input||(this.done=!0);var e,t,n,r;this._more||(this.yytext="",this.match="");for(var a=this._currentRules(),s=0;s<a.length;s++)if(n=this._input.match(this.rules[a[s]]),n&&(!t||n[0].length>t[0].length)){if(t=n,r=s,this.options.backtrack_lexer){if(e=this.test_match(n,a[s]),e!==!1)return e;if(this._backtrack){t=!1;continue}return!1}if(!this.options.flex)break}return t?(e=this.test_match(t,a[r]),e!==!1&&e):""===this._input?this.EOF:this.parseError("Lexical error on line "+(this.yylineno+1)+". Unrecognized text.\n"+this.showPosition(),{text:"",token:null,line:this.yylineno})},lex:function(){var e=this.next();return e?e:this.lex()},begin:function(e){this.conditionStack.push(e)},popState:function(){var e=this.conditionStack.length-1;return e>0?this.conditionStack.pop():this.conditionStack[0]},_currentRules:function(){return this.conditionStack.length&&this.conditionStack[this.conditionStack.length-1]?this.conditions[this.conditionStack[this.conditionStack.length-1]].rules:this.conditions.INITIAL.rules},topState:function(e){return e=this.conditionStack.length-1-Math.abs(e||0),e>=0?this.conditionStack[e]:"INITIAL"},pushState:function(e){this.begin(e)},stateStackSize:function(){return this.conditionStack.length},options:{"case-insensitive":!0},performAction:function(e,t,n,r){switch(n){case 0:return 265;case 1:return 298;case 2:return 417;case 3:return 295;case 4:return 5;case 5:return 5;case 6:return 292;case 7:return 292;case 8:return 131;case 9:return 131;case 10:return;case 11:break;case 12:return 312;case 13:return 315;case 14:return t.yytext="VALUE",88;case 15:return t.yytext="VALUE",188;case 16:return t.yytext="ROW",188;case 17:return t.yytext="COLUMN",188;case 18:return t.yytext="MATRIX",188;case 19:return t.yytext="INDEX",188;case 20:return t.yytext="RECORDSET",188;case 21:return t.yytext="TEXT",188;case 22:return t.yytext="SELECT",188;case 23:return 517;case 24:return 377;case 25:return 398;case 26:return 512;case 27:return 283;case 28:return 163;case 29:return 396;case 30:return 169;case 31:return 228;case 32:return 165;case 33:return 206;case 34:return 284;case 35:return 75;case 36:return 415;case 37:return 241;case 38:return 400;case 39:return 352;case 40:return 280;case 41:return 511;case 42:return 434;case 43:return 326;case 44:return 438;case 45:return 327;case 46:return 311;case 47:return 118;case 48:return 111;case 49:return 311;case 50:return 111;case 51:return 311;case 52:return 111;case 53:return 311;case 54:return 505;case 55:return 299;case 56:return 267;case 57:return 364;case 58:return 129;case 59:return"CLOSE";case 60:return 242;case 61:return 189;case 62:return 189;case 63:return 431;case 64:return 363;case 65:return 467;case 66:return 437;case 67:return 269;case 68:return 239;case 69:return 277;case 70:return 343;case 71:return 205;case 72:return 237;case 73:return 264;case 74:return"CURSOR";case 75:return 401;case 76:return 287;case 77:return 288;case 78:return 445;case 79:return 339;case 80:return 334;case 81:return"DELETED";case 82:return 241;case 83:return 402;case 84:return 184;case 85:return 392;case 86:return 444;case 87:return 134;case 88:return 302;case 89:return 385;case 90:return 306;case 91:return 310;case 92:return 168;case 93:return 505;case 94:return 505;case 95:return 294;case 96:return 14;case 97:return 291;case 98:return 248;case 99:return 281;case 100:return 94;case 101:return 369;case 102:return 182;case 103:return 226;case 104:return 309;case 105:return 599;case 106:return 469;case 107:return 231;case 108:return 235;case 109:return 238;case 110:return 413;case 111:return 155;case 112:return 352;case 113:return 328;case 114:return 98;case 115:return 192;case 116:return 211;case 117:return 223;case 118:return 513;case 119:return 335;case 120:return 212;case 121:return 167;case 122:return 289;case 123:return 197;case 124:return 222;case 125:return 366;case 126:return 282;case 127:return"LET";case 128:return 224;case 129:return 111;case 130:return 244;case 131:return 457;case 132:return 190;case 133:return 279;case 134:return 386;case 135:return 278;case 136:return 449;case 137:return 168;case 138:return 399;case 139:return 221;case 140:return 642;case 141:return 266;case 142:return 243;case 143:return 376;case 144:return 153;case 145:return 293;case 146:return 430;case 147:return 229;case 148:return 411;case 149:return 128;case 150:return 246;case 151:return"OPEN";case 152:return 412;case 153:return 170;case 154:return 117;case 155:return 207;case 156:return 272;case 157:return 171;case 158:return 275;case 159:return 762;case 160:return 92;case 161:return 16;case 162:return 365;case 163:return 439;case 164:return 675;case 165:return 15;case 166:return 410;case 167:return 193;case 168:return"REDUCE";case 169:return 370;case 170:return 307;case 171:return 514;case 172:return 679;case 173:return 106;case 174:return 397;case 175:return 174;case 176:return 286;case 177:return 440;case 178:return 684;case 179:return 172;case 180:return 172;case 181:return 225;case 182:return 433;case 183:return 236;case 184:return 149;case 185:return 763;case 186:return 401;case 187:return 88;case 188:return 227;case 189:return 145;case 190:return 145;case 191:return 405;case 192:return 330;case 193:return 414;case 194:return"STRATEGY";case 195:return"STORE";case 196:return 276;case 197:return 349;case 198:return 349;case 199:return 460;case 200:return 353;case 201:return 353;case 202:return 191;case 203:return 305;case 204:return"TIMEOUT";case 205:return 147;case 206:return 194;case 207:return 432;case 208:return 432;case 209:return 506;case 210:return 290;case 211:return 448;case 212:return 161;case 213:return 186;case 214:return 97;case 215:return 331;case 216:return 404;case 217:return 230;case 218:return 148;case 219:return 337;case 220:return 133;case 221:return 406;case 222:return 304;case 223:return 127;case 224:return 436;case 225:return 71;case 226:return 432;case 227:return 130;case 228:return 130;case 229:return 114;case 230:return 136;case 231:return 178;case 232:return 313;case 233:return 179;case 234:return 132;case 235:return 137;case 236:return 322;case 237:return 319;case 238:return 321;case 239:return 318;case 240:return 316;case 241:return 314;case 242:return 315;case 243:return 141;case 244:return 140;case 245:return 138;case 246:return 317;case 247:return 320;case 248:return 139;case 249:return 123;case 250:return 320;case 251:return 76;case 252:return 77;case 253:return 144;case 254:return 421;case 255:return 423;case 256:return 296;case 257:return 502;case 258:return 504;case 259:return 121;case 260:return 115;case 261:return 73;case 262:return 329;case 263:return 151;case 264:return 761;case 265:return 142;case 266:return 180;case 267:return 135;case 268:return 122;case 269:return 308;case 270:return 4;case 271:return 10;case 272:return"INVALID"}},rules:[/^(?:``([^\`])+``)/i,/^(?:\[\?\])/i,/^(?:@\[)/i,/^(?:ARRAY\[)/i,/^(?:\[([^\]])*?\])/i,/^(?:`([^\`])*?`)/i,/^(?:N(['](\\.|[^']|\\')*?['])+)/i,/^(?:X(['](\\.|[^']|\\')*?['])+)/i,/^(?:(['](\\.|[^']|\\')*?['])+)/i,/^(?:(["](\\.|[^"]|\\")*?["])+)/i,/^(?:--(.*?)($|\r\n|\r|\n))/i,/^(?:\s+)/i,/^(?:\|\|)/i,/^(?:\|)/i,/^(?:VALUE\s+OF\s+SEARCH\b)/i,/^(?:VALUE\s+OF\s+SELECT\b)/i,/^(?:ROW\s+OF\s+SELECT\b)/i,/^(?:COLUMN\s+OF\s+SELECT\b)/i,/^(?:MATRIX\s+OF\s+SELECT\b)/i,/^(?:INDEX\s+OF\s+SELECT\b)/i,/^(?:RECORDSET\s+OF\s+SELECT\b)/i,/^(?:TEXT\s+OF\s+SELECT\b)/i,/^(?:SELECT\b)/i,/^(?:ABSOLUTE\b)/i,/^(?:ACTION\b)/i,/^(?:ADD\b)/i,/^(?:AFTER\b)/i,/^(?:AGGR\b)/i,/^(?:ALL\b)/i,/^(?:ALTER\b)/i,/^(?:AND\b)/i,/^(?:ANTI\b)/i,/^(?:ANY\b)/i,/^(?:APPLY\b)/i,/^(?:ARRAY\b)/i,/^(?:AS\b)/i,/^(?:ASSERT\b)/i,/^(?:ASC\b)/i,/^(?:ATTACH\b)/i,/^(?:AUTO(_)?INCREMENT\b)/i,/^(?:AVG\b)/i,/^(?:BEFORE\b)/i,/^(?:BEGIN\b)/i,/^(?:BETWEEN\b)/i,/^(?:BREAK\b)/i,/^(?:NOT\s+BETWEEN\b)/i,/^(?:NOT\s+LIKE\b)/i,/^(?:BY\b)/i,/^(?:~~\*)/i,/^(?:!~~\*)/i,/^(?:~~)/i,/^(?:!~~)/i,/^(?:ILIKE\b)/i,/^(?:NOT\s+ILIKE\b)/i,/^(?:CALL\b)/i,/^(?:CASE\b)/i,/^(?:CAST\b)/i,/^(?:CHECK\b)/i,/^(?:CLASS\b)/i,/^(?:CLOSE\b)/i,/^(?:COLLATE\b)/i,/^(?:COLUMN\b)/i,/^(?:COLUMNS\b)/i,/^(?:COMMIT\b)/i,/^(?:CONSTRAINT\b)/i,/^(?:CONTENT\b)/i,/^(?:CONTINUE\b)/i,/^(?:CONVERT\b)/i,/^(?:CORRESPONDING\b)/i,/^(?:COUNT\b)/i,/^(?:CREATE\b)/i,/^(?:CROSS\b)/i,/^(?:CUBE\b)/i,/^(?:CURRENT_TIMESTAMP\b)/i,/^(?:CURSOR\b)/i,/^(?:DATABASE(S)?)/i,/^(?:DATEADD\b)/i,/^(?:DATEDIFF\b)/i,/^(?:DECLARE\b)/i,/^(?:DEFAULT\b)/i,/^(?:DELETE\b)/i,/^(?:DELETED\b)/i,/^(?:DESC\b)/i,/^(?:DETACH\b)/i,/^(?:DISTINCT\b)/i,/^(?:DROP\b)/i,/^(?:ECHO\b)/i,/^(?:EDGE\b)/i,/^(?:END\b)/i,/^(?:ENUM\b)/i,/^(?:ELSE\b)/i,/^(?:ESCAPE\b)/i,/^(?:EXCEPT\b)/i,/^(?:EXEC\b)/i,/^(?:EXECUTE\b)/i,/^(?:EXISTS\b)/i,/^(?:EXPLAIN\b)/i,/^(?:FALSE\b)/i,/^(?:FETCH\b)/i,/^(?:FIRST\b)/i,/^(?:FOR\b)/i,/^(?:FOREIGN\b)/i,/^(?:FROM\b)/i,/^(?:FULL\b)/i,/^(?:GLOB\b)/i,/^(?:GO\b)/i,/^(?:GRAPH\b)/i,/^(?:GROUP\b)/i,/^(?:GROUPING\b)/i,/^(?:HAVING\b)/i,/^(?:HELP\b)/i,/^(?:IF\b)/i,/^(?:IDENTITY\b)/i,/^(?:IS\b)/i,/^(?:IN\b)/i,/^(?:INDEX\b)/i,/^(?:INDEXED\b)/i,/^(?:INNER\b)/i,/^(?:INSTEAD\b)/i,/^(?:INSERT\b)/i,/^(?:INSERTED\b)/i,/^(?:INTERSECT\b)/i,/^(?:INTERVAL\b)/i,/^(?:INTO\b)/i,/^(?:JOIN\b)/i,/^(?:KEY\b)/i,/^(?:LAST\b)/i,/^(?:LET\b)/i,/^(?:LEFT\b)/i,/^(?:LIKE\b)/i,/^(?:LIMIT\b)/i,/^(?:MATCHED\b)/i,/^(?:MATRIX\b)/i,/^(?:MAX(\s+)?(?=\())/i,/^(?:MAX(\s+)?(?=(,|\))))/i,/^(?:MIN(\s+)?(?=\())/i,/^(?:MERGE\b)/i,/^(?:MINUS\b)/i,/^(?:MODIFY\b)/i,/^(?:NATURAL\b)/i,/^(?:NEXT\b)/i,/^(?:NEW\b)/i,/^(?:NOCASE\b)/i,/^(?:NO\b)/i,/^(?:NOT\b)/i,/^(?:NULL\b)/i,/^(?:OFF\b)/i,/^(?:ON\b)/i,/^(?:ONLY\b)/i,/^(?:OF\b)/i,/^(?:OFFSET\b)/i,/^(?:OPEN\b)/i,/^(?:OPTION\b)/i,/^(?:OR\b)/i,/^(?:ORDER\b)/i,/^(?:OUTER\b)/i,/^(?:OVER\b)/i,/^(?:PATH\b)/i,/^(?:PARTITION\b)/i,/^(?:PERCENT\b)/i,/^(?:PIVOT\b)/i,/^(?:PLAN\b)/i,/^(?:PRIMARY\b)/i,/^(?:PRINT\b)/i,/^(?:PRIOR\b)/i,/^(?:QUERY\b)/i,/^(?:READ\b)/i,/^(?:RECORDSET\b)/i,/^(?:REDUCE\b)/i,/^(?:REFERENCES\b)/i,/^(?:REGEXP\b)/i,/^(?:REINDEX\b)/i,/^(?:RELATIVE\b)/i,/^(?:REMOVE\b)/i,/^(?:RENAME\b)/i,/^(?:REPEAT\b)/i,/^(?:REPLACE\b)/i,/^(?:REQUIRE\b)/i,/^(?:RESTORE\b)/i,/^(?:RETURN\b)/i,/^(?:RETURNS\b)/i,/^(?:RIGHT\b)/i,/^(?:ROLLBACK\b)/i,/^(?:ROLLUP\b)/i,/^(?:ROW\b)/i,/^(?:ROWS\b)/i,/^(?:SCHEMA(S)?)/i,/^(?:SEARCH\b)/i,/^(?:SEMI\b)/i,/^(?:SET\b)/i,/^(?:SETS\b)/i,/^(?:SHOW\b)/i,/^(?:SOME\b)/i,/^(?:SOURCE\b)/i,/^(?:STRATEGY\b)/i,/^(?:STORE\b)/i,/^(?:SUM\b)/i,/^(?:TABLE\b)/i,/^(?:TABLES\b)/i,/^(?:TARGET\b)/i,/^(?:TEMP\b)/i,/^(?:TEMPORARY\b)/i,/^(?:TEXTSTRING\b)/i,/^(?:THEN\b)/i,/^(?:TIMEOUT\b)/i,/^(?:TO\b)/i,/^(?:TOP\b)/i,/^(?:TRAN\b)/i,/^(?:TRANSACTION\b)/i,/^(?:TRIGGER\b)/i,/^(?:TRUE\b)/i,/^(?:TRUNCATE\b)/i,/^(?:UNION\b)/i,/^(?:UNIQUE\b)/i,/^(?:UNPIVOT\b)/i,/^(?:UPDATE\b)/i,/^(?:USE\b)/i,/^(?:USING\b)/i,/^(?:VALUE\b)/i,/^(?:VALUES\b)/i,/^(?:VERTEX\b)/i,/^(?:VIEW\b)/i,/^(?:WHEN\b)/i,/^(?:WHERE\b)/i,/^(?:WHILE\b)/i,/^(?:WITH\b)/i,/^(?:WORK\b)/i,/^(?:(\d*[.])?\d+[eE]\d+)/i,/^(?:(\d*[.])?\d+)/i,/^(?:->)/i,/^(?:#)/i,/^(?:\+)/i,/^(?:-)/i,/^(?:\*)/i,/^(?:\/)/i,/^(?:%)/i,/^(?:!===)/i,/^(?:===)/i,/^(?:!==)/i,/^(?:==)/i,/^(?:>=)/i,/^(?:&)/i,/^(?:\|)/i,/^(?:<<)/i,/^(?:>>)/i,/^(?:>)/i,/^(?:<=)/i,/^(?:<>)/i,/^(?:<)/i,/^(?:=)/i,/^(?:!=)/i,/^(?:\()/i,/^(?:\))/i,/^(?:@)/i,/^(?:\{)/i,/^(?:\})/i,/^(?:\])/i,/^(?::-)/i,/^(?:\?-)/i,/^(?:\.\.)/i,/^(?:\.)/i,/^(?:,)/i,/^(?:::)/i,/^(?::)/i,/^(?:;)/i,/^(?:\$)/i,/^(?:\?)/i,/^(?:!)/i,/^(?:\^)/i,/^(?:~)/i,/^(?:[a-zA-Z_][a-zA-Z_0-9]*)/i,/^(?:$)/i,/^(?:.)/i],conditions:{INITIAL:{rules:[0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,254,255,256,257,258,259,260,261,262,263,264,265,266,267,268,269,270,271,272],inclusive:!0}}};return e}();return fi.lexer=bi,e.prototype=fi,fi.Parser=e,new e}();"undefined"!=typeof y&&"undefined"!=typeof exports&&(exports.parser=C,exports.Parser=C.Parser,exports.parse=function(){return C.parse.apply(C,arguments)},exports.main=function(e){e[1]||(console.log("Usage: "+e[0]+" FILE"),process.exit(1));var t=y("fs").readFileSync(y("path").normalize(e[1]),"utf8");return exports.parser.parse(t)},"undefined"!=typeof module&&y.main===module&&exports.main(process.argv.slice(1))),A.prettyflag=!1,A.pretty=function(e,t){var n=A.prettyflag;A.prettyflag=!t;var r=A.parse(e).toString();return A.prettyflag=n,r};var R=A.utils={},O=R.escapeq=function(e){return(""+e).replace(/["'\\\n\r\u2028\u2029]/g,function(e){switch(e){case'"':case"'":case"\\":return"\\"+e;case"\n":return"\\n";case"\r":return"\\r";case"\u2028":return"\\u2028";case"\u2029":return"\\u2029"}})},w=R.undoubleq=function(e){return e.replace(/(\')/g,"''")},I=R.doubleq=function(e){return e.replace(/(\'\')/g,"\\'")},x=(R.doubleqq=function(e){return e.replace(/\'/g,"'")},function(e){return e[0]===String.fromCharCode(65279)&&(e=e.substr(1)),e});R.global=function(){try{return Function("return this")()}catch(t){var e=self||window||e;if(e)return e;throw new Error("Unable to locate global object")}}();R.isNativeFunction=function(e){return"function"==typeof e&&!!~e.toString().indexOf("[native code]")};R.isWebWorker=function(){try{var e=R.global.importScripts;return R.isNativeFunction(e)}catch(e){return!1}}(),R.isNode=function(){try{return R.isNativeFunction(R.global.process.reallyExit)}catch(e){return!1}}(),R.isBrowser=function(){try{return R.isNativeFunction(R.global.location.reload)}catch(e){return!1}}(),R.isBrowserify=function(){return R.isBrowser&&"undefined"!=typeof process&&process.browser}(),R.isRequireJS=function(){return R.isBrowser&&"function"==typeof y&&"function"==typeof y.specified}(),R.isMeteor=function(){return"undefined"!=typeof Meteor&&Meteor.release}(),R.isMeteorClient=(R.isMeteorClient=function(){return R.isMeteor&&Meteor.isClient})(),R.isMeteorServer=function(){return R.isMeteor&&Meteor.isServer}(),R.isCordova=function(){return"object"==typeof cordova}(),R.hasIndexedDB=function(){return!!R.global.indexedDB}(),R.isArray=function(e){return"[object Array]"===Object.prototype.toString.call(e)};var D=R.loadFile=function(e,t,n,r){var a;if(R.isNode||R.isMeteorServer);else if(R.isCordova)R.global.requestFileSystem(LocalFileSystem.PERSISTENT,0,function(t){t.root.getFile(e,{create:!1},function(e){e.file(function(e){var t=new FileReader;t.onloadend=function(e){n(x(this.result))},t.readAsText(e)})})});else if("string"==typeof e)if("#"===e.substr(0,1)&&"undefined"!=typeof document)a=document.querySelector(e).textContent,n(a);else{var s=new XMLHttpRequest;s.onreadystatechange=function(){s.readyState===XMLHttpRequest.DONE&&(200===s.status?n&&n(x(s.responseText)):r&&r(s))},s.open("GET",e,t),s.responseType="text",s.send()}else if(e instanceof Event){var i=e.target.files,o=new FileReader;i[0].name;o.onload=function(e){var t=e.target.result;n(x(t))},o.readAsText(i[0])}},L=(R.loadBinaryFile=function(e,t,n,r){if(R.isNode||R.isMeteorServer);else if("string"==typeof e){var a=new XMLHttpRequest;a.open("GET",e,t),a.responseType="arraybuffer",a.onload=function(){for(var e=new Uint8Array(a.response),t=[],r=0;r<e.length;++r)t[r]=String.fromCharCode(e[r]);n(t.join(""))},a.send()}else if(e instanceof Event){var s=e.target.files,i=new FileReader;s[0].name;i.onload=function(e){var t=e.target.result;n(t)},i.readAsArrayBuffer(s[0])}else e instanceof Blob&&n(e)},R.removeFile=function(e,t){if(!R.isNode)throw new Error("You can remove files only in Node.js and Apache Cordova")},R.deleteFile=function(e,t){},R.fileExists=function(e,t){if(!R.isNode)throw new Error("You can use exists() only in Node.js or Apach Cordova")},R.saveFile=function(e,t,n){var r=1;if(void 0===e)r=t,n&&(r=n(r));else if(R.isNode);else if(9===a()){var s=t.replace(/\r\n/g,"&#A;&#D;");s=s.replace(/\n/g,"&#D;"),s=s.replace(/\t/g,"&#9;");var i=R.global.open("about:blank","_blank");i.document.write(s),i.document.close(),i.document.execCommand("SaveAs",!1,e),i.close()}else{var o=new Blob([t],{type:"text/plain;charset=utf-8"});me(o,e),n&&(r=n(r))}return r},R.hash=function(e){for(var t=5381,n=e.length;n;)t=33*t^e.charCodeAt(--n);return t}),k=R.arrayUnion=function(e,t){var n=t.slice(0);return e.forEach(function(e){n.indexOf(e)<0&&n.push(e)}),n},$=R.arrayDiff=function(e,t){return e.filter(function(e){return t.indexOf(e)<0})},M=R.arrayIntersect=function(e,t){var n=[];return e.forEach(function(e){var r=!1;t.forEach(function(t){r=r||e===t}),r&&n.push(e)}),n},U=R.arrayUnionDeep=function(e,t){var n=t.slice(0);return e.forEach(function(e){var t=!1;n.forEach(function(n){t=t||q(e,n)}),t||n.push(e)}),n},_=R.arrayExceptDeep=function(e,t){var n=[];return e.forEach(function(e){var r=!1;t.forEach(function(t){r=r||q(e,t)}),r||n.push(e)}),n},F=R.arrayIntersectDeep=function(e,t){var n=[];return e.forEach(function(e){var r=!1;t.forEach(function(t){r=r||q(e,t,!0)}),r&&n.push(e)}),n},P=R.cloneDeep=function e(t){if(null===t||"object"!=typeof t)return t;if(t instanceof Date)return new Date(t);var n=t.constructor();for(var r in t)t.hasOwnProperty(r)&&(n[r]=e(t[r]));return n},q=R.deepEqual=function(e,t){if("object"==typeof e&&null!==e&&"object"==typeof t&&null!==t){if(Object.keys(e).length!==Object.keys(t).length)return!1;for(var n in e){if(!t.hasOwnProperty(n))return!1;if(!q(e[n],t[n]))return!1}return!0}return e===t},G=R.distinctArray=function(e){for(var t={},n=0,r=e.length;n<r;n++){var a;a="object"==typeof e[n]?Object.keys(e[n]).sort().map(function(t){return t+"`"+e[n][t]}).join("`"):e[n],t[a]=e[n]}var s=[];for(var i in t)s.push(t[i]);return s},V=R.extend=function(e,t){e=e||{};for(var n in t)t.hasOwnProperty(n)&&(e[n]=t[n]);return e},B=R.flatArray=function(e){if(!e||0===e.length)return[];if("object"==typeof e&&e instanceof A.Recordset)return e.data.map(function(t){return t[e.columns[0].columnid]});var t=Object.keys(e[0])[0];return void 0===t?[]:e.map(function(e){return e[t]})};R.arrayOfArrays=function(e){return e.map(function(e){var t=[];for(var n in e)t.push(e[n]);return t})},R.xlsnc=function(e){var t=String.fromCharCode(65+e%26);return e>=26&&(e=(e/26|0)-1,t=String.fromCharCode(65+e%26)+t,e>26&&(e=(e/26|0)-1,t=String.fromCharCode(65+e%26)+t)),t},R.xlscn=function(e){var t=e.charCodeAt(0)-65;return e.length>1&&(t=26*(t+1)+e.charCodeAt(1)-65,e.length>2&&(t=26*(t+1)+e.charCodeAt(2)-65)),t},R.domEmptyChildren=function(e){for(var t=e.childNodes.length;t--;)e.removeChild(e.lastChild)},R.like=function(e,t,n){n||(n="");for(var r=0,a="^";r<e.length;){var s=e[r],i="";r<e.length-1&&(i=e[r+1]),s===n?(a+="\\"+i,r++):"["===s&&"^"===i?(a+="[^",r++):a+="["===s||"]"===s?s:"%"===s?".*":"_"===s?".":"/.*+?|(){}".indexOf(s)>-1?"\\"+s:s,r++}return a+="$",(""+(t||"")).toUpperCase().search(RegExp(a.toUpperCase()))>-1};R.glob=function(e,t){for(var n=0,r="^";n<t.length;){var a=t[n],s="";n<t.length-1&&(s=t[n+1]),"["===a&&"^"===s?(r+="[^",n++):r+="["===a||"]"===a?a:"*"===a?".*":"?"===a?".":"/.*+?|(){}".indexOf(a)>-1?"\\"+a:a,n++}return r+="$",(""+(e||"")).toUpperCase().search(RegExp(r.toUpperCase()))>-1},R.findAlaSQLPath=function(){if(R.isWebWorker)return"";if(R.isMeteorClient)return"/packages/dist/";if(R.isMeteorServer)return"assets/packages/dist/";if(R.isNode)return N;if(R.isBrowser)for(var e=document.getElementsByTagName("script"),t=0;t<e.length;t++){if("alasql-worker.js"===e[t].src.substr(-16).toLowerCase())return e[t].src.substr(0,e[t].src.length-16);if("alasql-worker.min.js"===e[t].src.substr(-20).toLowerCase())return e[t].src.substr(0,e[t].src.length-20);if("alasql.js"===e[t].src.substr(-9).toLowerCase())return e[t].src.substr(0,e[t].src.length-9);if("alasql.min.js"===e[t].src.substr(-13).toLowerCase())return e[t].src.substr(0,e[t].src.length-13)}return""};var j=function(){var e=null;if(R.isNode||R.isBrowserify||R.isMeteorServer||(e=R.global.XLSX||null),null===e)throw new Error("Please include the xlsx.js library");return e},H=function(){var e=null;if(R.isNode||R.isBrowserify||R.isMeteorServer||(e=R.global.XLS||null),null===e)throw new Error("Please include the xlsjs library");return e};A.path=A.utils.findAlaSQLPath(),A.utils.uncomment=function(e){e=("__"+e+"__").split("");for(var t,n=!1,r=!1,a=!1,s=0,i=e.length;s<i;s++){var o="\\"!==e[s-1]||"\\"===e[s-2];n?e[s]===t&&o&&(n=!1):r?"*"===e[s]&&"/"===e[s+1]?(e[s]=e[s+1]="",r=!1,s++):e[s]="":a?("\n"!==e[s+1]&&"\r"!==e[s+1]||(a=!1),e[s]=""):'"'===e[s]||"'"===e[s]?(n=!0,t=e[s]):"["===e[s]&&"@"!==e[s-1]?(n=!0,t="]"):"/"===e[s]&&"*"===e[s+1]&&(e[s]="",r=!0)}return e=e.join("").slice(2,-2)},A.parser=C,A.parser.parseError=function(e,t){throw new Error("Have you used a reserved keyword without `escaping` it?\n"+e)},A.parse=function(e){return C.parse(A.utils.uncomment(e))},A.engines={},A.databases={},A.databasenum=0,A.options={},A.options.errorlog=!1,A.options.valueof=!1,A.options.dropifnotexists=!1,A.options.datetimeformat="sql",A.options.casesensitive=!0,A.options.logtarget="output",A.options.logprompt=!0,A.options.progress=!1,A.options.modifier=void 0,A.options.columnlookup=10,A.options.autovertex=!0,A.options.usedbo=!0,A.options.autocommit=!0,A.options.cache=!0,A.options.tsql=!0,A.options.mysql=!0,A.options.postgres=!0,A.options.oracle=!0,A.options.sqlite=!0,A.options.orientdb=!0,A.options.nocount=!1,A.options.nan=!1,A.options.joinstar="overwrite",A.vars={},A.declares={},A.prompthistory=[],A.plugins={},A.from={},A.into={},A.fn={},A.aggr={},A.busy=0,A.MAXSQLCACHESIZE=1e4,A.DEFAULTDATABASEID="alasql",A.lastid=0,A.buffer={},A.use=function(e){if(e||(e=A.DEFAULTDATABASEID),A.useid!==e){A.useid=e;var t=A.databases[A.useid];A.tables=t.tables,t.resetSqlCache(),A.options.usedbo&&(A.databases.dbo=t)}},A.autoval=function(e,t,n,r){var a=r?A.databases[r]:A.databases[A.useid];if(!a.tables[e])throw new Error("Tablename not found: "+e);if(!a.tables[e].identities[t])throw new Error("Colname not found: "+t);return n?a.tables[e].identities[t].value||null:a.tables[e].identities[t].value-a.tables[e].identities[t].step||null},A.exec=function(e,t,n,r){if("function"==typeof t&&(r=n,n=t,t={}),delete A.error,t=t||{},!A.options.errorlog)return A.dexec(A.useid,e,t,n,r);try{return A.dexec(A.useid,e,t,n,r)}catch(e){A.error=e,n&&n(null,A.error)}},A.dexec=function(e,t,n,r,a){var s,i=A.databases[e];if(A.options.cache){s=L(t);var o=i.sqlCache[s];if(o&&i.dbversion===o.dbversion)return o(n,r)}var u=A.parse(t);if(u.statements){if(0===u.statements.length)return 0;if(1===u.statements.length){if(u.statements[0].compile){var o=u.statements[0].compile(e);if(!o)return;o.sql=t,o.dbversion=i.dbversion,A.options.cache&&(i.sqlCacheSize>A.MAXSQLCACHESIZE&&i.resetSqlCache(),i.sqlCacheSize++,i.sqlCache[s]=o);var c=A.res=o(n,r,a);return c}A.precompile(u.statements[0],A.useid,n);var c=A.res=u.statements[0].execute(e,n,r,a);return c}return r?void A.adrun(e,u,n,r,a):A.drun(e,u,n,r,a)}},A.drun=function(e,t,n,r,a){var s=A.useid;s!==e&&A.use(e);for(var i=[],o=0,u=t.statements.length;o<u;o++)if(t.statements[o])if(t.statements[o].compile){var c=t.statements[o].compile(A.useid);i.push(A.res=c(n,null,a))}else A.precompile(t.statements[o],A.useid,n),i.push(A.res=t.statements[o].execute(A.useid,n));return s!==e&&A.use(s),r&&r(i),A.res=i,i},A.adrun=function(e,t,n,r,a){function s(l){void 0!==l&&c.push(l);var h=t.statements.shift();if(h)if(h.compile){var d=h.compile(A.useid);d(n,s,a),A.options.progress!==!1&&A.options.progress(o,i++)}else A.precompile(t.statements[0],A.useid,n),h.execute(A.useid,n,s),A.options.progress!==!1&&A.options.progress(o,i++);else u!==e&&A.use(u),r(c)}var i=0,o=t.statements.length;A.options.progress!==!1&&A.options.progress(o,i++);var u=A.useid;u!==e&&A.use(e);var c=[];s()},A.compile=function(e,t){t=t||A.useid;var n=A.parse(e);if(1===n.statements.length){var r=n.statements[0].compile(t);return r.promise=function(e){return new Promise(function(t,n){r(e,function(e,r){r?n(r):t(e)})})},r}throw new Error("Cannot compile, because number of statements in SQL is not equal to 1")},R.global.Promise||R.isNode||function(){"use strict";function e(e){return"function"==typeof e||"object"==typeof e&&null!==e}function t(e){return"function"==typeof e}function n(e){H=e}function r(e){X=e}function a(){return function(){process.nextTick(c)}}function s(){return function(){j(c)}}function i(){var e=0,t=new z(c),n=document.createTextNode("");return t.observe(n,{characterData:!0}),function(){n.data=e=++e%2}}function o(){var e=new MessageChannel;return e.port1.onmessage=c,function(){e.port2.postMessage(0)}}function u(){return function(){setTimeout(c,1)}}function c(){for(var e=0;W>e;e+=2){var t=te[e],n=te[e+1];t(n),te[e]=void 0,te[e+1]=void 0}W=0}function l(){try{var e=y,t=e("vertx");return j=t.runOnLoop||t.runOnContext,s()}catch(e){return u()}}function h(e,t){var n=this,r=new this.constructor(f);void 0===r[ae]&&k(r);var a=n._state;if(a){var s=arguments[a-1];X(function(){x(a,r,s,n._result)})}else R(n,r,e,t);return r}function d(e){var t=this;if(e&&"object"==typeof e&&e.constructor===t)return e;var n=new t(f);return T(n,e),n}function f(){}function p(){return new TypeError("You cannot resolve a promise with itself")}function b(){return new TypeError("A promises callback cannot return that same promise.")}function E(e){try{return e.then}catch(e){return ue.error=e,ue}}function g(e,t,n,r){try{e.call(t,n,r)}catch(e){return e}}function m(e,t,n){X(function(e){var r=!1,a=g(n,t,function(n){r||(r=!0,t!==n?T(e,n):N(e,n))},function(t){r||(r=!0,C(e,t))},"Settle: "+(e._label||" unknown promise"));!r&&a&&(r=!0,C(e,a))},e)}function v(e,t){t._state===ie?N(e,t._result):t._state===oe?C(e,t._result):R(t,void 0,function(t){T(e,t)},function(t){C(e,t)})}function S(e,n,r){n.constructor===e.constructor&&r===ne&&constructor.resolve===re?v(e,n):r===ue?C(e,ue.error):void 0===r?N(e,n):t(r)?m(e,n,r):N(e,n)}function T(t,n){t===n?C(t,p()):e(n)?S(t,n,E(n)):N(t,n)}function A(e){e._onerror&&e._onerror(e._result),O(e)}function N(e,t){e._state===se&&(e._result=t,e._state=ie,0!==e._subscribers.length&&X(O,e))}function C(e,t){e._state===se&&(e._state=oe,e._result=t,X(A,e))}function R(e,t,n,r){var a=e._subscribers,s=a.length;e._onerror=null,a[s]=t,a[s+ie]=n,a[s+oe]=r,0===s&&e._state&&X(O,e)}function O(e){var t=e._subscribers,n=e._state;if(0!==t.length){for(var r,a,s=e._result,i=0;i<t.length;i+=3)r=t[i],a=t[i+n],r?x(n,r,a,s):a(s);e._subscribers.length=0}}function w(){this.error=null}function I(e,t){try{return e(t)}catch(e){return ce.error=e,ce}}function x(e,n,r,a){var s,i,o,u,c=t(r);if(c){if(s=I(r,a),s===ce?(u=!0,i=s.error,s=null):o=!0,n===s)return void C(n,b())}else s=a,o=!0;n._state!==se||(c&&o?T(n,s):u?C(n,i):e===ie?N(n,s):e===oe&&C(n,s))}function D(e,t){try{t(function(t){T(e,t)},function(t){C(e,t)})}catch(t){C(e,t)}}function L(){return le++}function k(e){e[ae]=le++,e._state=void 0,e._result=void 0,e._subscribers=[]}function $(e){return new be(this,e).promise}function M(e){var t=this;return new t(Y(e)?function(n,r){for(var a=e.length,s=0;a>s;s++)t.resolve(e[s]).then(n,r)}:function(e,t){t(new TypeError("You must pass an array to race."))})}function U(e){var t=this,n=new t(f);return C(n,e),n}function _(){throw new TypeError("You must pass a resolver function as the first argument to the promise constructor")}function F(){throw new TypeError("Failed to construct 'Promise': Please use the 'new' operator, this object constructor cannot be called as a function.")}function P(e){this[ae]=L(),this._result=this._state=void 0,this._subscribers=[],f!==e&&("function"!=typeof e&&_(),this instanceof P?D(this,e):F())}function q(e,t){this._instanceConstructor=e,this.promise=new e(f),this.promise[ae]||k(this.promise),Array.isArray(t)?(this._input=t,this.length=t.length,this._remaining=t.length,this._result=new Array(this.length),0===this.length?N(this.promise,this._result):(this.length=this.length||0,this._enumerate(),0===this._remaining&&N(this.promise,this._result))):C(this.promise,G())}function G(){return new Error("Array Methods must be provided an Array")}function V(){var e;if("undefined"!=typeof global)e=global;else if("undefined"!=typeof self)e=self;else try{e=Function("return this")()}catch(e){throw new Error("polyfill failed because global object is unavailable in this environment")}var t=e.Promise;(!t||"[object Promise]"!==Object.prototype.toString.call(t.resolve())||t.cast)&&(e.Promise=pe)}var B;B=Array.isArray?Array.isArray:function(e){return"[object Array]"===Object.prototype.toString.call(e)};var j,H,J,Y=B,W=0,X=function(e,t){te[W]=e,te[W+1]=t,W+=2,2===W&&(H?H(c):J())},K="undefined"!=typeof window?window:void 0,Q=K||{},z=Q.MutationObserver||Q.WebKitMutationObserver,Z="undefined"==typeof self&&"undefined"!=typeof process&&"[object process]"==={}.toString.call(process),ee="undefined"!=typeof Uint8ClampedArray&&"undefined"!=typeof importScripts&&"undefined"!=typeof MessageChannel,te=new Array(1e3);J=Z?a():z?i():ee?o():void 0===K&&"function"==typeof y?l():u();var ne=h,re=d,ae=Math.random().toString(36).substring(16),se=void 0,ie=1,oe=2,ue=new w,ce=new w,le=0,he=$,de=M,fe=U,pe=P;P.all=he,P.race=de,P.resolve=re,P.reject=fe,P._setScheduler=n,P._setAsap=r,P._asap=X,P.prototype={constructor:P,then:ne,catch:function(e){return this.then(null,e)}};var be=q;q.prototype._enumerate=function(){for(var e=this.length,t=this._input,n=0;this._state===se&&e>n;n++)this._eachEntry(t[n],n)},q.prototype._eachEntry=function(e,t){var n=this._instanceConstructor,r=n.resolve;if(r===re){var a=E(e);if(a===ne&&e._state!==se)this._settledAt(e._state,t,e._result);else if("function"!=typeof a)this._remaining--,this._result[t]=e;else if(n===pe){var s=new n(f);S(s,e,a),this._willSettleAt(s,t)}else this._willSettleAt(new n(function(t){t(e)}),t)}else this._willSettleAt(r(e),t)},q.prototype._settledAt=function(e,t,n){var r=this.promise;r._state===se&&(this._remaining--,e===oe?C(r,n):this._result[t]=n),0===this._remaining&&N(r,this._result)},q.prototype._willSettleAt=function(e,t){var n=this;R(e,void 0,function(e){n._settledAt(ie,t,e)},function(e){n._settledAt(oe,t,e)})};var Ee=V,ge={Promise:pe,polyfill:Ee};"function"==typeof define&&define.amd?define(function(){return ge}):"undefined"!=typeof module&&module.exports?module.exports=ge:"undefined"!=typeof this&&(this.ES6Promise=ge),
Ee()}.call(this);var J=function(e,t,n,r){return new R.global.Promise(function(a,s){A(e,t,function(e,t){t?s(t):(n&&r&&A.options.progress!==!1&&A.options.progress(n,r),a(e))})})},Y=function(e){if(!(e.length<1)){for(var t,n,r,a=[],s=0;s<e.length;s++){if(t=e[s],"string"==typeof t&&(t=[t]),!R.isArray(t)||t.length<1||2<t.length)throw new Error("Error in .promise parameter");n=t[0],r=t[1]||void 0,a.push(J(n,r,s,e.length))}return R.global.Promise.all(a)}};A.promise=function(e,t){if("undefined"==typeof Promise)throw new Error("Please include a Promise/A+ library");if("string"==typeof e)return J(e,t);if(!R.isArray(e)||e.length<1||"undefined"!=typeof t)throw new Error("Error in .promise parameters");return Y(e)};var W=A.Database=function(e){var t=this;if(t===A)if(e){if(t=A.databases[e],A.databases[e]=t,!t)throw new Error('Database "'+e+'" not found')}else t=A.databases.alasql,A.options.tsql&&(A.databases.tempdb=A.databases.alasql);return e||(e="db"+A.databasenum++),t.databaseid=e,A.databases[e]=t,t.dbversion=0,t.tables={},t.views={},t.triggers={},t.indices={},t.objects={},t.counter=0,t.resetSqlCache(),t};W.prototype.resetSqlCache=function(){this.sqlCache={},this.sqlCacheSize=0},W.prototype.exec=function(e,t,n){return A.dexec(this.databaseid,e,t,n)},W.prototype.autoval=function(e,t,n){return A.autoval(e,t,n,this.databaseid)},W.prototype.transaction=function(e){var t=new A.Transaction(this.databaseid),n=e(t);return n};var X=A.Transaction=function(e){return this.transactionid=Date.now(),this.databaseid=e,this.commited=!1,this.dbversion=A.databases[e].dbversion,this.bank=JSON.stringify(A.databases[e]),this};X.prototype.commit=function(){this.commited=!0,A.databases[this.databaseid].dbversion=Date.now(),delete this.bank},X.prototype.rollback=function(){if(this.commited)throw new Error("Transaction already commited");A.databases[this.databaseid]=JSON.parse(this.bank),delete this.bank},X.prototype.exec=function(e,t,n){return A.dexec(this.databaseid,e,t,n)},X.prototype.executeSQL=X.prototype.exec;var K=A.Table=function(e){this.data=[],this.columns=[],this.xcolumns={},this.inddefs={},this.indices={},this.uniqs={},this.uniqdefs={},this.identities={},this.checks=[],this.checkfns=[],this.beforeinsert={},this.afterinsert={},this.insteadofinsert={},this.beforedelete={},this.afterdelete={},this.insteadofdelete={},this.beforeupdate={},this.afterupdate={},this.insteadofupdate={},V(this,e)};K.prototype.indexColumns=function(){var e=this;e.xcolumns={},e.columns.forEach(function(t){e.xcolumns[t.columnid]=t})};var Q=(A.View=function(e){this.columns=[],this.xcolumns={},this.query=[],V(this,e)},A.Query=function(e){this.alasql=A,this.columns=[],this.xcolumns={},this.selectGroup=[],this.groupColumns={},V(this,e)}),z=(A.Recordset=function(e){V(this,e)},C.yy=A.yy={});z.extend=V,z.casesensitive=A.options.casesensitive;var Z=z.Base=function(e){return z.extend(this,e)};Z.prototype.toString=function(){},Z.prototype.toType=function(){},Z.prototype.toJS=function(){},Z.prototype.compile=r,Z.prototype.exec=function(){},Z.prototype.compile=r,Z.prototype.exec=function(){},z.Statements=function(e){return z.extend(this,e)},z.Statements.prototype.toString=function(){return this.statements.map(function(e){return e.toString()}).join("; ")},z.Statements.prototype.compile=function(e){var t=this.statements.map(function(t){return t.compile(e)});return 1===t.length?t[0]:function(e,n){var r=t.map(function(t){return t(e)});return n&&n(r),r}},z.Search=function(e){return z.extend(this,e)},z.Search.prototype.toString=function(){var e="SEARCH ";return this.selectors&&(e+=this.selectors.toString()),this.from&&(e+="FROM "+this.from.toString()),e},z.Search.prototype.toJS=function(e){var t="this.queriesfn["+(this.queriesidx-1)+"](this.params,null,"+e+")";return t},z.Search.prototype.compile=function(e){var t=e,n=this,r=function(e,a){var s;return i.bind(n)(t,e,function(e){s=f(r.query,e),a&&(s=a(s))}),s};return r.query={},r},A.srch={},A.srch.PROP=function(e,t,n){if("XML"===n.mode){var r=[];return e.children.forEach(function(e){e.name.toUpperCase()===t[0].toUpperCase()&&r.push(e)}),r.length>0?{status:1,values:r}:{status:-1,values:[]}}return"object"!=typeof e||null===e||"object"!=typeof t||"undefined"==typeof e[t[0]]?{status:-1,values:[]}:{status:1,values:[e[t[0]]]}},A.srch.APROP=function(e,t){return"object"!=typeof e||null===e||"object"!=typeof t||"undefined"==typeof e[t[0]]?{status:1,values:[void 0]}:{status:1,values:[e[t[0]]]}},A.srch.EQ=function(e,t,n,r){var a=t[0].toJS("x",""),s=new Function("x,alasql,params","return "+a);return e===s(e,A,r)?{status:1,values:[e]}:{status:-1,values:[]}},A.srch.LIKE=function(e,t,n,r){var a=t[0].toJS("x",""),s=new Function("x,alasql,params","return "+a);return e.toUpperCase().match(new RegExp("^"+s(e,A,r).toUpperCase().replace(/%/g,".*").replace(/\?|_/g,".")+"$"),"g")?{status:1,values:[e]}:{status:-1,values:[]}},A.srch.ATTR=function(e,t,n){if("XML"===n.mode)return"undefined"==typeof t?{status:1,values:[e.attributes]}:"object"==typeof e&&"object"==typeof e.attributes&&"undefined"!=typeof e.attributes[t[0]]?{status:1,values:[e.attributes[t[0]]]}:{status:-1,values:[]};throw new Error("ATTR is not using in usual mode")},A.srch.CONTENT=function(e,t,n){if("XML"===n.mode)return{status:1,values:[e.content]};throw new Error("ATTR is not using in usual mode")},A.srch.SHARP=function(e,t){var n=A.databases[A.useid].objects[t[0]];return"undefined"!=typeof e&&e===n?{status:1,values:[e]}:{status:-1,values:[]}},A.srch.PARENT=function(){return console.log("PARENT not implemented",arguments),{status:-1,values:[]}},A.srch.CHILD=function(e,t,n){return"object"==typeof e?e instanceof Array?{status:1,values:e}:"XML"===n.mode?{status:1,values:Object.keys(e.children).map(function(t){return e.children[t]})}:{status:1,values:Object.keys(e).map(function(t){return e[t]})}:{status:1,values:[]}},A.srch.KEYS=function(e){return"object"==typeof e&&null!==e?{status:1,values:Object.keys(e)}:{status:1,values:[]}},A.srch.WHERE=function(e,t,n,r){var a=t[0].toJS("x",""),s=new Function("x,alasql,params","return "+a);return s(e,A,r)?{status:1,values:[e]}:{status:-1,values:[]}},A.srch.NAME=function(e,t){return e.name===t[0]?{status:1,values:[e]}:{status:-1,values:[]}},A.srch.CLASS=function(e,t){return e.$class==t?{status:1,values:[e]}:{status:-1,values:[]}},A.srch.VERTEX=function(e){return"VERTEX"===e.$node?{status:1,values:[e]}:{status:-1,values:[]}},A.srch.INSTANCEOF=function(e,t){return e instanceof A.fn[t[0]]?{status:1,values:[e]}:{status:-1,values:[]}},A.srch.EDGE=function(e){return"EDGE"===e.$node?{status:1,values:[e]}:{status:-1,values:[]}},A.srch.EX=function(e,t,n,r){var a=t[0].toJS("x",""),s=new Function("x,alasql,params","return "+a);return{status:1,values:[s(e,A,r)]}},A.srch.RETURN=function(e,t,n,r){var a={};return t&&t.length>0&&t.forEach(function(t){var n=t.toJS("x",""),s=new Function("x,alasql,params","return "+n);"undefined"==typeof t.as&&(t.as=t.toString()),a[t.as]=s(e,A,r)}),{status:1,values:[a]}},A.srch.REF=function(e){return{status:1,values:[A.databases[A.useid].objects[e]]}},A.srch.OUT=function(e){if(e.$out&&e.$out.length>0){var t=e.$out.map(function(e){return A.databases[A.useid].objects[e]});return{status:1,values:t}}return{status:-1,values:[]}},A.srch.OUTOUT=function(e){if(e.$out&&e.$out.length>0){var t=[];return e.$out.forEach(function(e){var n=A.databases[A.useid].objects[e];n&&n.$out&&n.$out.length>0&&n.$out.forEach(function(e){t=t.concat(A.databases[A.useid].objects[e])})}),{status:1,values:t}}return{status:-1,values:[]}},A.srch.IN=function(e){if(e.$in&&e.$in.length>0){var t=e.$in.map(function(e){return A.databases[A.useid].objects[e]});return{status:1,values:t}}return{status:-1,values:[]}},A.srch.ININ=function(e){if(e.$in&&e.$in.length>0){var t=[];return e.$in.forEach(function(e){var n=A.databases[A.useid].objects[e];n&&n.$in&&n.$in.length>0&&n.$in.forEach(function(e){t=t.concat(A.databases[A.useid].objects[e])})}),{status:1,values:t}}return{status:-1,values:[]}},A.srch.AS=function(e,t){return A.vars[t[0]]=e,{status:1,values:[e]}},A.srch.AT=function(e,t){var n=A.vars[t[0]];return{status:1,values:[n]}},A.srch.CLONEDEEP=function(e){var t=P(e);return{status:1,values:[t]}},A.srch.SET=function(e,t,n,r){var a=t.map(function(e){return"@"===e.method?"alasql.vars['"+e.variable+"']="+e.expression.toJS("x",""):"$"===e.method?"params['"+e.variable+"']="+e.expression.toJS("x",""):"x['"+e.column.columnid+"']="+e.expression.toJS("x","")}).join(";"),s=new Function("x,params,alasql",a);return s(e,r,A),{status:1,values:[e]}},A.srch.ROW=function(e,t,n,r){var a="var y;return [";a+=t.map(function(e){return e.toJS("x","")}).join(","),a+="]";var s=new Function("x,params,alasql",a),i=s(e,r,A);return{status:1,values:[i]}},A.srch.D3=function(e){return"VERTEX"!==e.$node&&"EDGE"===e.$node&&(e.source=e.$in[0],e.target=e.$out[0]),{status:1,values:[e]}};var ee=function(e){if(e){if(e&&1===e.length&&e[0].expression&&"function"==typeof e[0].expression){var t=e[0].expression;return function(e,n){var r=t(e),a=t(n);return r>a?1:r===a?0:-1}}var n="",r="";return e.forEach(function(e){var t="";if(e.expression instanceof z.NumValue&&(e.expression=self.columns[e.expression.value-1]),e.expression instanceof z.Column){var a=e.expression.columnid;A.options.valueof&&(t=".valueOf()"),e.nocase&&(t+=".toUpperCase()"),"_"===a?(n+="if(a"+t+("ASC"===e.direction?">":"<")+"b"+t+")return 1;",n+="if(a"+t+"==b"+t+"){"):(n+="if((a['"+a+"']||'')"+t+("ASC"===e.direction?">":"<")+"(b['"+a+"']||'')"+t+")return 1;",n+="if((a['"+a+"']||'')"+t+"==(b['"+a+"']||'')"+t+"){")}else t=".valueOf()",e.nocase&&(t+=".toUpperCase()"),n+="if(("+e.toJS("a","")+"||'')"+t+("ASC"===e.direction?">(":"<(")+e.toJS("b","")+"||'')"+t+")return 1;",n+="if(("+e.toJS("a","")+"||'')"+t+"==("+e.toJS("b","")+"||'')"+t+"){";r+="}"}),n+="return 0;",n+=r+"return -1",new Function("a,b",n)}};A.srch.ORDERBY=function(e,t){var n=e.sort(ee(t));return{status:1,values:n}};var te=function(e){for(var t=0,n=e.sources.length;t<n;t++){var r=e.sources[t];if(delete r.ix,t>0&&"ix"==r.optimization&&r.onleftfn&&r.onrightfn){if(r.databaseid&&A.databases[r.databaseid].tables[r.tableid]){A.databases[r.databaseid].tables[r.tableid].indices||(e.database.tables[r.tableid].indices={});var a=A.databases[r.databaseid].tables[r.tableid].indices[L(r.onrightfns+"`"+r.srcwherefns)];!A.databases[r.databaseid].tables[r.tableid].dirty&&a&&(r.ix=a)}if(!r.ix){r.ix={};for(var s,i={},o=0,u=r.data.length;(s=r.data[o])||r.getfn&&(s=r.getfn(o))||o<u;){if(r.getfn&&!r.dontcache&&(r.data[o]=s),i[r.alias||r.tableid]=s,r.srcwherefn(i,e.params,A)){var c=r.onrightfn(i,e.params,A),l=r.ix[c];l||(l=r.ix[c]=[]),l.push(s)}o++}r.databaseid&&A.databases[r.databaseid].tables[r.tableid]&&(A.databases[r.databaseid].tables[r.tableid].indices[L(r.onrightfns+"`"+r.srcwherefns)]=r.ix)}}else if(r.wxleftfn){if(A.databases[r.databaseid].engineid||(a=A.databases[r.databaseid].tables[r.tableid].indices[L(r.wxleftfns+"`")]),!A.databases[r.databaseid].tables[r.tableid].dirty&&a)r.ix=a,r.data=r.ix[r.wxrightfn(null,e.params,A)];else{for(r.ix={},i={},o=0,u=r.data.length;(s=r.data[o])||r.getfn&&(s=r.getfn(o))||o<u;)r.getfn&&!r.dontcache&&(r.data[o]=s),i[r.alias||r.tableid]=r.data[o],c=r.wxleftfn(i,e.params,A),l=r.ix[c],l||(l=r.ix[c]=[]),l.push(r.data[o]),o++;A.databases[r.databaseid].engineid||(A.databases[r.databaseid].tables[r.tableid].indices[L(r.wxleftfns+"`")]=r.ix)}r.srcwherefns&&(r.data?(i={},r.data=r.data.filter(function(t){return i[r.alias]=t,r.srcwherefn(i,e.params,A)})):r.data=[])}else if(r.srcwherefns&&!r.dontcache)if(r.data){var i={};r.data=r.data.filter(function(t){return i[r.alias]=t,r.srcwherefn(i,e.params,A)}),i={},o=0,u=r.data.length;for(var h=[];(s=r.data[o])||r.getfn&&(s=r.getfn(o))||o<u;)r.getfn&&!r.dontcache&&(r.data[o]=s),i[r.alias]=s,r.srcwherefn(i,e.params,A)&&h.push(s),o++;r.data=h}else r.data=[];r.databaseid&&A.databases[r.databaseid].tables[r.tableid]}};z.Select=function(e){return z.extend(this,e)},z.Select.prototype.toString=function(){var e="";return this.explain&&(e+="EXPLAIN "),e+="SELECT ",this.modifier&&(e+=this.modifier+" "),this.top&&(e+="TOP "+this.top.value+" ",this.percent&&(e+="PERCENT ")),e+=this.columns.map(function(e){var t=e.toString();return"undefined"!=typeof e.as&&(t+=" AS "+e.as),t}).join(", "),this.from&&(e+=" FROM "+this.from.map(function(e){var t=e.toString();return e.as&&(t+=" AS "+e.as),t}).join(",")),this.joins&&(e+=this.joins.map(function(e){var t=" ";if(e.joinmode&&(t+=e.joinmode+" "),e.table)t+="JOIN "+e.table.toString();else{if(!(e instanceof z.Apply))throw new Error("Wrong type in JOIN mode");t+=e.toString()}return e.using&&(t+=" USING "+e.using.toString()),e.on&&(t+=" ON "+e.on.toString()),t})),this.where&&(e+=" WHERE "+this.where.toString()),this.group&&this.group.length>0&&(e+=" GROUP BY "+this.group.map(function(e){return e.toString()}).join(", ")),this.having&&(e+=" HAVING "+this.having.toString()),this.order&&this.order.length>0&&(e+=" ORDER BY "+this.order.map(function(e){return e.toString()}).join(", ")),this.limit&&(e+=" LIMIT "+this.limit.value),this.offset&&(e+=" OFFSET "+this.offset.value),this.union&&(e+=" UNION "+(this.corresponding?"CORRESPONDING ":"")+this.union.toString()),this.unionall&&(e+=" UNION ALL "+(this.corresponding?"CORRESPONDING ":"")+this.unionall.toString()),this.except&&(e+=" EXCEPT "+(this.corresponding?"CORRESPONDING ":"")+this.except.toString()),this.intersect&&(e+=" INTERSECT "+(this.corresponding?"CORRESPONDING ":"")+this.intersect.toString()),e},z.Select.prototype.toJS=function(e){var t="alasql.utils.flatArray(this.queriesfn["+(this.queriesidx-1)+"](this.params,null,"+e+"))[0]";return t},z.Select.prototype.compile=function(e){var t=A.databases[e],n=new Q;if(n.removeKeys=[],n.aggrKeys=[],n.explain=this.explain,n.explaination=[],n.explid=1,n.modifier=this.modifier,n.database=t,this.compileWhereExists(n),this.compileQueries(n),n.defcols=this.compileDefCols(n,e),n.fromfn=this.compileFrom(n),this.joins&&this.compileJoins(n),n.rownums=[],this.compileSelectGroup0(n),this.group||n.selectGroup.length>0?n.selectgfns=this.compileSelectGroup1(n):n.selectfns=this.compileSelect1(n),this.compileRemoveColumns(n),this.where&&this.compileWhereJoins(n),n.wherefn=this.compileWhere(n),(this.group||n.selectGroup.length>0)&&(n.groupfn=this.compileGroup(n)),this.having&&(n.havingfn=this.compileHaving(n)),this.order&&(n.orderfn=this.compileOrder(n)),this.group||n.selectGroup.length>0?n.selectgfn=this.compileSelectGroup2(n):n.selectfn=this.compileSelect2(n),n.distinct=this.distinct,this.pivot&&(n.pivotfn=this.compilePivot(n)),this.unpivot&&(n.pivotfn=this.compileUnpivot(n)),this.top?n.limit=this.top.value:this.limit&&(n.limit=this.limit.value,this.offset&&(n.offset=this.offset.value)),n.percent=this.percent,n.corresponding=this.corresponding,this.union?(n.unionfn=this.union.compile(e),this.union.order?n.orderfn=this.union.compileOrder(n):n.orderfn=null):this.unionall?(n.unionallfn=this.unionall.compile(e),this.unionall.order?n.orderfn=this.unionall.compileOrder(n):n.orderfn=null):this.except?(n.exceptfn=this.except.compile(e),this.except.order?n.orderfn=this.except.compileOrder(n):n.orderfn=null):this.intersect&&(n.intersectfn=this.intersect.compile(e),this.intersect.order?n.intersectfn=this.intersect.compileOrder(n):n.orderfn=null),this.into){if(this.into instanceof z.Table)A.options.autocommit&&A.databases[this.into.databaseid||e].engineid?n.intoallfns='return alasql.engines["'+A.databases[this.into.databaseid||e].engineid+'"].intoTable("'+(this.into.databaseid||e)+'","'+this.into.tableid+'",this.data, columns, cb);':n.intofns="alasql.databases['"+(this.into.databaseid||e)+"'].tables['"+this.into.tableid+"'].data.push(r);";else if(this.into instanceof z.VarValue)n.intoallfns='alasql.vars["'+this.into.variable+'"]=this.data;res=this.data.length;if(cb)res=cb(res);return res;';else if(this.into instanceof z.FuncValue){var r="return alasql.into['"+this.into.funcid.toUpperCase()+"'](";this.into.args&&this.into.args.length>0?(r+=this.into.args[0].toJS()+",",r+=this.into.args.length>1?this.into.args[1].toJS()+",":"undefined,"):r+="undefined, undefined,",n.intoallfns=r+"this.data,columns,cb)"}else this.into instanceof z.ParamValue&&(n.intofns="params['"+this.into.param+"'].push(r)");n.intofns?n.intofn=new Function("r,i,params,alasql","var y;"+n.intofns):n.intoallfns&&(n.intoallfn=new Function("columns,cb,params,alasql","var y;"+n.intoallfns))}var a=function(e,t,r){n.params=e;var a=o(n,r,function(e){if(n.rownums.length>0)for(var r=0,a=e.length;r<a;r++)for(var s=0,i=n.rownums.length;s<i;s++)e[r][n.rownums[s]]=r+1;var o=f(n,e);return t&&t(o),o});return a};return a.query=n,a},z.Select.prototype.execute=function(e,t,n){return this.compile(e)(t,n)},z.ExistsValue=function(e){return z.extend(this,e)},z.ExistsValue.prototype.toString=function(){return"EXISTS("+this.value.toString()+")"},z.ExistsValue.prototype.toType=function(){return"boolean"},z.ExistsValue.prototype.toJS=function(e,t,n){return"this.existsfn["+this.existsidx+"](params,null,"+e+").data.length"},z.Select.prototype.compileWhereExists=function(e){this.exists&&(e.existsfn=this.exists.map(function(t){var n=t.compile(e.database.databaseid);return n.query.modifier="RECORDSET",n}))},z.Select.prototype.compileQueries=function(e){this.queries&&(e.queriesfn=this.queries.map(function(t){var n=t.compile(e.database.databaseid);return n.query.modifier="RECORDSET",n}))},A.precompile=function(e,t,n){e&&(e.params=n,e.queries&&(e.queriesfn=e.queries.map(function(n){var r=n.compile(t||e.database.databaseid);return r.query.modifier="RECORDSET",r})),e.exists&&(e.existsfn=e.exists.map(function(n){var r=n.compile(t||e.database.databaseid);return r.query.modifier="RECORDSET",r})))},z.Select.prototype.compileFrom=function(e){var t=this;e.sources=[],e.aliases={},t.from&&(t.from.forEach(function(t){var r=t.as||t.tableid;if(t instanceof z.Table)e.aliases[r]={tableid:t.tableid,databaseid:t.databaseid||e.database.databaseid,type:"table"};else if(t instanceof z.Select)e.aliases[r]={type:"subquery"};else if(t instanceof z.Search)e.aliases[r]={type:"subsearch"};else if(t instanceof z.ParamValue)e.aliases[r]={type:"paramvalue"};else if(t instanceof z.FuncValue)e.aliases[r]={type:"funcvalue"};else if(t instanceof z.VarValue)e.aliases[r]={type:"varvalue"};else if(t instanceof z.FromData)e.aliases[r]={type:"fromdata"};else if(t instanceof z.Json)e.aliases[r]={type:"json"};else{if(!t.inserted)throw new Error("Wrong table at FROM");e.aliases[r]={type:"inserted"}}var a={alias:r,databaseid:t.databaseid||e.database.databaseid,tableid:t.tableid,joinmode:"INNER",onmiddlefn:n,srcwherefns:"",srcwherefn:n};if(t instanceof z.Table)a.columns=A.databases[a.databaseid].tables[a.tableid].columns,A.options.autocommit&&A.databases[a.databaseid].engineid&&!A.databases[a.databaseid].tables[a.tableid].view?a.datafn=function(e,t,n,r,s){return s.engines[s.databases[a.databaseid].engineid].fromTable(a.databaseid,a.tableid,n,r,e)}:A.databases[a.databaseid].tables[a.tableid].view?a.datafn=function(e,t,n,r,s){var i=s.databases[a.databaseid].tables[a.tableid].select(t);return n&&(i=n(i,r,e)),i}:a.datafn=function(e,t,n,r,s){var i=s.databases[a.databaseid].tables[a.tableid].data;return n&&(i=n(i,r,e)),i};else if(t instanceof z.Select)a.subquery=t.compile(e.database.databaseid),"undefined"==typeof a.subquery.query.modifier&&(a.subquery.query.modifier="RECORDSET"),a.columns=a.subquery.query.columns,a.datafn=function(e,t,n,r,s){var i;return a.subquery(e.params,function(t){return i=t.data,n&&(i=n(i,r,e)),i}),i};else if(t instanceof z.Search)a.subsearch=t,a.columns=[],a.datafn=function(e,t,n,r,s){var i;return a.subsearch.execute(e.database.databaseid,e.params,function(t){return i=t,n&&(i=n(i,r,e)),i}),i};else if(t instanceof z.ParamValue){var s="var res = alasql.prepareFromData(params['"+t.param+"']";t.array&&(s+=",true"),s+=");if(cb)res=cb(res,idx,query);return res",a.datafn=new Function("query,params,cb,idx,alasql",s)}else if(t.inserted){var s="var res = alasql.prepareFromData(alasql.inserted";t.array&&(s+=",true"),s+=");if(cb)res=cb(res,idx,query);return res",a.datafn=new Function("query,params,cb,idx,alasql",s)}else if(t instanceof z.Json){var s="var res = alasql.prepareFromData("+t.toJS();t.array&&(s+=",true"),s+=");if(cb)res=cb(res,idx,query);return res",a.datafn=new Function("query,params,cb,idx,alasql",s)}else if(t instanceof z.VarValue){var s="var res = alasql.prepareFromData(alasql.vars['"+t.variable+"']";t.array&&(s+=",true"),s+=");if(cb)res=cb(res,idx,query);return res",a.datafn=new Function("query,params,cb,idx,alasql",s)}else if(t instanceof z.FuncValue){var i="var res=alasql.from['"+t.funcid.toUpperCase()+"'](";t.args&&t.args.length>0?(i+=t.args[0]?t.args[0].toJS("query.oldscope")+",":"null,",i+=t.args[1]?t.args[1].toJS("query.oldscope")+",":"null,"):i+="null,null,",i+="cb,idx,query",i+=");/*if(cb)res=cb(res,idx,query);*/return res",a.datafn=new Function("query, params, cb, idx, alasql",i)}else{if(!(t instanceof z.FromData))throw new Error("Wrong table at FROM");a.datafn=function(e,n,r,a,s){var i=t.data;return r&&(i=r(i,a,e)),i}}e.sources.push(a)}),e.defaultTableid=e.sources[0].alias)},A.prepareFromData=function(e,t){var n=e;if("string"==typeof e){if(n=e.split(/\r?\n/),t)for(var r=0,a=n.length;r<a;r++)n[r]=[n[r]]}else if(t){n=[];for(var r=0,a=e.length;r<a;r++)n.push([e[r]])}else if("object"==typeof e&&!(e instanceof Array))if("undefined"!=typeof Mongo&&"undefined"!=typeof Mongo.Collection&&e instanceof Mongo.Collection)n=e.find().fetch();else{n=[];for(var s in e)e.hasOwnProperty(s)&&n.push([s,e[s]])}return n},z.Select.prototype.compileJoins=function(e){this.joins.forEach(function(t){if("CROSS"==t.joinmode){if(t.using||t.on)throw new Error("CROSS JOIN cannot have USING or ON clauses");"INNER"==t.joinmode}var r,a;if(t instanceof z.Apply)r={alias:t.as,applymode:t.applymode,onmiddlefn:n,srcwherefns:"",srcwherefn:n,columns:[]},r.applyselect=t.select.compile(e.database.databaseid),r.columns=r.applyselect.query.columns,r.datafn=function(e,t,n,r,a){var s;return n&&(s=n(s,r,e)),s},e.sources.push(r);else{if(t.table){if(a=t.table,r={alias:t.as||a.tableid,databaseid:a.databaseid||e.database.databaseid,tableid:a.tableid,joinmode:t.joinmode,onmiddlefn:n,srcwherefns:"",srcwherefn:n,columns:[]},!A.databases[r.databaseid].tables[r.tableid])throw new Error("Table '"+r.tableid+"' is not exists in database '"+r.databaseid)+"'";r.columns=A.databases[r.databaseid].tables[r.tableid].columns,A.options.autocommit&&A.databases[r.databaseid].engineid?r.datafn=function(e,t,n,a,s){return s.engines[s.databases[r.databaseid].engineid].fromTable(r.databaseid,r.tableid,n,a,e)}:A.databases[r.databaseid].tables[r.tableid].view?r.datafn=function(e,t,n,a,s){var i=s.databases[r.databaseid].tables[r.tableid].select(t);return n&&(i=n(i,a,e)),i}:r.datafn=function(e,t,n,a,s){var i=s.databases[r.databaseid].tables[r.tableid].data;return n&&(i=n(i,a,e)),i},e.aliases[r.alias]={tableid:a.tableid,databaseid:a.databaseid||e.database.databaseid}}else if(t.select){var a=t.select;r={alias:t.as,joinmode:t.joinmode,onmiddlefn:n,srcwherefns:"",srcwherefn:n,columns:[]},r.subquery=a.compile(e.database.databaseid),"undefined"==typeof r.subquery.query.modifier&&(r.subquery.query.modifier="RECORDSET"),r.columns=r.subquery.query.columns,r.datafn=function(e,t,n,a,s){return r.subquery(e.params,null,n,a).data},e.aliases[r.alias]={type:"subquery"}}else if(t.param){r={alias:t.as,joinmode:t.joinmode,onmiddlefn:n,srcwherefns:"",srcwherefn:n};var s=t.param.param,i="var res=alasql.prepareFromData(params['"+s+"']";t.array&&(i+=",true"),i+=");if(cb)res=cb(res, idx, query);return res",r.datafn=new Function("query,params,cb,idx, alasql",i),e.aliases[r.alias]={type:"paramvalue"}}else if(t.variable){r={alias:t.as,joinmode:t.joinmode,onmiddlefn:n,srcwherefns:"",srcwherefn:n};var i="var res=alasql.prepareFromData(alasql.vars['"+t.variable+"']";t.array&&(i+=",true"),i+=");if(cb)res=cb(res, idx, query);return res",r.datafn=new Function("query,params,cb,idx, alasql",i),e.aliases[r.alias]={type:"varvalue"}}else if(t.funcid){r={alias:t.as,joinmode:t.joinmode,onmiddlefn:n,srcwherefns:"",srcwherefn:n};var o="var res=alasql.from['"+js.funcid.toUpperCase()+"'](";t.args&&t.args.length>0?(o+=t.args[0]?t.args[0].toJS("query.oldscope")+",":"null,",o+=t.args[1]?t.args[1].toJS("query.oldscope")+",":"null,"):o+="null,null,",o+="cb,idx,query",o+=");/*if(cb)res=cb(res,idx,query);*/return res",r.datafn=new Function("query, params, cb, idx, alasql",o),e.aliases[r.alias]={type:"funcvalue"}}var u=r.alias;if(t.natural){if(t.using||t.on)throw new Error("NATURAL JOIN cannot have USING or ON clauses");if(e.sources.length>0){var c=e.sources[e.sources.length-1],l=A.databases[c.databaseid].tables[c.tableid],h=A.databases[r.databaseid].tables[r.tableid];if(!l||!h)throw new Error("In this version of Alasql NATURAL JOIN works for tables with predefined columns only");var d=l.columns.map(function(e){return e.columnid}),f=h.columns.map(function(e){return e.columnid});t.using=M(d,f).map(function(e){return{columnid:e}})}}if(t.using){var c=e.sources[e.sources.length-1];r.onleftfns=t.using.map(function(e){return"p['"+(c.alias||c.tableid)+"']['"+e.columnid+"']"}).join('+"`"+'),r.onleftfn=new Function("p,params,alasql","var y;return "+r.onleftfns),r.onrightfns=t.using.map(function(e){return"p['"+(r.alias||r.tableid)+"']['"+e.columnid+"']"}).join('+"`"+'),r.onrightfn=new Function("p,params,alasql","var y;return "+r.onrightfns),r.optimization="ix"}else if(t.on)if(t.on instanceof z.Op&&"="==t.on.op&&!t.on.allsome){r.optimization="ix";var p="",b="",E="",g=!1,m=t.on.left.toJS("p",e.defaultTableid,e.defcols),v=t.on.right.toJS("p",e.defaultTableid,e.defcols);m.indexOf("p['"+u+"']")>-1&&!(v.indexOf("p['"+u+"']")>-1)?(m.match(/p\[\'.*?\'\]/g)||[]).every(function(e){return e=="p['"+u+"']"})?b=m:g=!0:!(m.indexOf("p['"+u+"']")>-1)&&v.indexOf("p['"+u+"']")>-1&&(v.match(/p\[\'.*?\'\]/g)||[]).every(function(e){return e=="p['"+u+"']"})?p=m:g=!0,v.indexOf("p['"+u+"']")>-1&&!(m.indexOf("p['"+u+"']")>-1)?(v.match(/p\[\'.*?\'\]/g)||[]).every(function(e){return e=="p['"+u+"']"})?b=v:g=!0:!(v.indexOf("p['"+u+"']")>-1)&&m.indexOf("p['"+u+"']")>-1&&(m.match(/p\[\'.*?\'\]/g)||[]).every(function(e){return e=="p['"+u+"']"})?p=v:g=!0,g&&(b="",p="",E=t.on.toJS("p",e.defaultTableid,e.defcols),r.optimization="no"),r.onleftfns=p,r.onrightfns=b,r.onmiddlefns=E||"true",r.onleftfn=new Function("p,params,alasql","var y;return "+r.onleftfns),r.onrightfn=new Function("p,params,alasql","var y;return "+r.onrightfns),r.onmiddlefn=new Function("p,params,alasql","var y;return "+r.onmiddlefns)}else r.optimization="no",r.onmiddlefns=t.on.toJS("p",e.defaultTableid,e.defcols),r.onmiddlefn=new Function("p,params,alasql","var y;return "+t.on.toJS("p",e.defaultTableid,e.defcols));e.sources.push(r)}})},z.Select.prototype.compileWhere=function(e){if(this.where){if("function"==typeof this.where)return this.where;var t=this.where.toJS("p",e.defaultTableid,e.defcols);return e.wherefns=t,new Function("p,params,alasql","var y;return "+t)}return function(){return!0}},z.Select.prototype.compileWhereJoins=function(e){},z.Select.prototype.compileGroup=function(e){if(e.sources.length>0)var t=e.sources[0].alias;else var t="";var n=e.defcols,r=[[]];this.group&&(r=b(this.group,e));var a=[];r.forEach(function(e){a=k(a,e)}),e.allgroups=a,e.ingroup=[];var s="";return r.forEach(function(r){s+="var g=this.xgroups[";var i=r.map(function(t){var n=t.split("\t")[0],r=t.split("\t")[1];return""===n?"1":(e.ingroup.push(n),r)});0===i.length&&(i=["''"]),s+=i.join('+"`"+'),s+="];if(!g) {this.groups.push((g=this.xgroups[",s+=i.join('+"`"+'),s+="] = {",s+=r.map(function(e){var t=e.split("\t")[0],n=e.split("\t")[1];return""===t?"":"'"+t+"':"+n+","}).join("");var o=$(a,r);s+=o.map(function(e){var t=e.split("\t")[0];return"'"+t+"':null,"}).join("");var u="",c="";"undefined"!=typeof e.groupStar&&(c+="for(var f in p['"+e.groupStar+"']) {g[f]=p['"+e.groupStar+"'][f];};"),s+=e.selectGroup.map(function(r){var a=r.expression.toJS("p",t,n),s=r.nick;return r instanceof z.AggrValue?(r.distinct&&(u+=",g['$$_VALUES_"+s+"']={},g['$$_VALUES_"+s+"']["+a+"]=true"),"SUM"===r.aggregatorid?"'"+s+"':("+a+")||0,":"MIN"===r.aggregatorid||"MAX"===r.aggregatorid||"FIRST"===r.aggregatorid||"LAST"===r.aggregatorid?"'"+s+"':"+a+",":"ARRAY"===r.aggregatorid?"'"+s+"':["+a+"],":"COUNT"===r.aggregatorid?"*"===r.expression.columnid?"'"+s+"':1,":"'"+s+"':(typeof "+a+' != "undefined")?1:0,':"AVG"===r.aggregatorid?(e.removeKeys.push("_SUM_"+s),e.removeKeys.push("_COUNT_"+s),"'"+s+"':"+a+",'_SUM_"+s+"':("+a+")||0,'_COUNT_"+s+"':(typeof "+a+' != "undefined")?1:0,'):"AGGR"===r.aggregatorid?(u+=",g['"+s+"']="+r.expression.toJS("g",-1),""):"REDUCE"===r.aggregatorid?(e.aggrKeys.push(r),"'"+s+"':alasql.aggr['"+r.funcid+"']("+a+",undefined,1),"):""):""}).join(""),s+="}"+u+",g));"+c+"} else {",s+=e.selectGroup.map(function(e){var r=e.nick,a=e.expression.toJS("p",t,n);if(e instanceof z.AggrValue){var s="",i="";if(e.distinct)var s="if(typeof "+a+'!="undefined" && (!g[\'$$_VALUES_'+r+"']["+a+"])) \t\t\t\t \t\t {",i="g['$$_VALUES_"+r+"']["+a+"]=true;}";return"SUM"===e.aggregatorid?s+"g['"+r+"']+=("+a+"||0);"+i:"COUNT"===e.aggregatorid?"*"===e.expression.columnid?s+"g['"+r+"']++;"+i:s+"if(typeof "+a+'!="undefined") g[\''+r+"']++;"+i:"ARRAY"===e.aggregatorid?s+"g['"+r+"'].push("+a+");"+i:"MIN"===e.aggregatorid?s+"g['"+r+"']=Math.min(g['"+r+"'],"+a+");"+i:"MAX"===e.aggregatorid?s+"g['"+r+"']=Math.max(g['"+r+"'],"+a+");"+i:"FIRST"===e.aggregatorid?"":"LAST"===e.aggregatorid?s+"g['"+r+"']="+a+";"+i:"AVG"===e.aggregatorid?""+s+"g['_SUM_"+r+"']+=(y="+a+")||0;g['_COUNT_"+r+"']+=(typeof y!=\"undefined\")?1:0;g['"+r+"']=g['_SUM_"+r+"']/g['_COUNT_"+r+"'];"+i:"AGGR"===e.aggregatorid?""+s+"g['"+r+"']="+e.expression.toJS("g",-1)+";"+i:"REDUCE"===e.aggregatorid?""+s+"g['"+r+"']=alasql.aggr."+e.funcid+"("+a+",g['"+r+"'],2);"+i:""}return""}).join(""),s+="}"}),new Function("p,params,alasql","var y;"+s)},z.Select.prototype.compileSelect1=function(t){var n=this;t.columns=[],t.xcolumns={},t.selectColumns={},t.dirtyColumns=!1;var r="var r={",a="",s=[];return this.columns.forEach(function(r){if(r instanceof z.Column)if("*"===r.columnid)if(r.func)a+="r=params['"+r.param+"'](p['"+t.sources[0].alias+"'],p,params,alasql);";else if(r.tableid){var i=p(t,r.tableid,!1);i.s&&(s=s.concat(i.s)),a+=i.sp}else for(var o in t.aliases){var i=p(t,o,!0);i.s&&(s=s.concat(i.s)),a+=i.sp}else{var u=r.tableid,c=r.databaseid||t.sources[0].databaseid||t.database.databaseid;if(u||(u=t.defcols[r.columnid]),u||(u=t.defaultTableid),"_"!==r.columnid?s.push("'"+O(r.as||r.columnid)+"':p['"+u+"']['"+r.columnid+"']"):s.push("'"+O(r.as||r.columnid)+"':p['"+u+"']"),t.selectColumns[O(r.as||r.columnid)]=!0,t.aliases[u]&&"table"===t.aliases[u].type){if(!A.databases[c].tables[t.aliases[u].tableid])throw new Error("Table '"+u+"' does not exists in database");var l=A.databases[c].tables[t.aliases[u].tableid].columns,h=A.databases[c].tables[t.aliases[u].tableid].xcolumns;if(h&&l.length>0){var d=h[r.columnid];if(void 0===d)throw new Error("Column does not exists: "+r.columnid);var f={columnid:r.as||r.columnid,dbtypeid:d.dbtypeid,dbsize:d.dbsize,dbpecision:d.dbprecision,dbenum:d.dbenum};t.columns.push(f),t.xcolumns[f.columnid]=f}else{var f={columnid:r.as||r.columnid};t.columns.push(f),t.xcolumns[f.columnid]=f,t.dirtyColumns=!0}}else{var f={columnid:r.as||r.columnid};t.columns.push(f),t.xcolumns[f.columnid]=f}}else if(r instanceof z.AggrValue){n.group||(n.group=[""]),r.as||(r.as=O(r.toString())),"SUM"===r.aggregatorid||"MAX"===r.aggregatorid||"MIN"===r.aggregatorid||"FIRST"===r.aggregatorid||"LAST"===r.aggregatorid||"AVG"===r.aggregatorid||"ARRAY"===r.aggregatorid||"REDUCE"===r.aggregatorid?s.push("'"+O(r.as)+"':"+e(r.expression.toJS("p",t.defaultTableid,t.defcols))):"COUNT"===r.aggregatorid&&s.push("'"+O(r.as)+"':1");var f={columnid:r.as||r.columnid||r.toString()};t.columns.push(f),t.xcolumns[f.columnid]=f}else{s.push("'"+O(r.as||r.columnid||r.toString())+"':"+e(r.toJS("p",t.defaultTableid,t.defcols))),
t.selectColumns[O(r.as||r.columnid||r.toString())]=!0;var f={columnid:r.as||r.columnid||r.toString()};t.columns.push(f),t.xcolumns[f.columnid]=f}}),r+=s.join(",")+"};"+a},z.Select.prototype.compileSelect2=function(e){var t=e.selectfns;return this.orderColumns&&this.orderColumns.length>0&&this.orderColumns.forEach(function(n,r){var a="$$$"+r;t+=n instanceof z.Column&&e.xcolumns[n.columnid]?"r['"+a+"']=r['"+n.columnid+"'];":"r['"+a+"']="+n.toJS("p",e.defaultTableid,e.defcols)+";",e.removeKeys.push(a)}),new Function("p,params,alasql","var y;"+t+"return r")},z.Select.prototype.compileSelectGroup0=function(e){var t=this;t.columns.forEach(function(n,r){if(n instanceof z.Column&&"*"===n.columnid)e.groupStar=n.tableid||"default";else{var a;a=O(n instanceof z.Column?n.columnid:n.toString(!0));for(var s=0;s<r;s++)if(a===t.columns[s].nick){a=t.columns[s].nick+":"+r;break}n.nick=a,!n.funcid||"ROWNUM"!==n.funcid.toUpperCase()&&"ROW_NUMBER"!==n.funcid.toUpperCase()||e.rownums.push(n.as)}}),this.columns.forEach(function(t){t.findAggregator&&t.findAggregator(e)}),this.having&&this.having.findAggregator&&this.having.findAggregator(e)},z.Select.prototype.compileSelectGroup1=function(t){var n=this,r="var r = {};";return n.columns.forEach(function(n){if(n instanceof z.Column&&"*"===n.columnid)return r+="for(var k in g) {r[k]=g[k]};","";var a=n.as;void 0===a&&(a=n instanceof z.Column?O(n.columnid):n.nick),t.groupColumns[a]=n.nick,r+="r['"+a+"']=",r+=e(n.toJS("g",""))+";";for(var s=0;s<t.removeKeys.length;s++)if(t.removeKeys[s]===a){t.removeKeys.splice(s,1);break}}),r},z.Select.prototype.compileSelectGroup2=function(e){var t=this,n=e.selectgfns;return t.columns.forEach(function(t){e.ingroup.indexOf(t.nick)>-1&&(n+="r['"+(t.as||t.nick)+"']=g['"+t.nick+"'];")}),this.orderColumns&&this.orderColumns.length>0&&this.orderColumns.forEach(function(t,r){var a="$$$"+r;n+=t instanceof z.Column&&e.groupColumns[t.columnid]?"r['"+a+"']=r['"+t.columnid+"'];":"r['"+a+"']="+t.toJS("g","")+";",e.removeKeys.push(a)}),new Function("g,params,alasql","var y;"+n+"return r")},z.Select.prototype.compileRemoveColumns=function(e){"undefined"!=typeof this.removecolumns&&(e.removeKeys=e.removeKeys.concat(this.removecolumns.filter(function(e){return"undefined"==typeof e.like}).map(function(e){return e.columnid})),e.removeLikeKeys=this.removecolumns.filter(function(e){return"undefined"!=typeof e.like}).map(function(e){return e.like.value}))},z.Select.prototype.compileHaving=function(e){return this.having?(s=this.having.toJS("g",-1),e.havingfns=s,new Function("g,params,alasql","var y;return "+s)):function(){return!0}},z.Select.prototype.compileOrder=function(e){var t=this;if(t.orderColumns=[],this.order){if(this.order&&1==this.order.length&&this.order[0].expression&&"function"==typeof this.order[0].expression){var n=this.order[0].expression;return function(e,t){var r=n(e),a=n(t);return r>a?1:r==a?0:-1}}var r="",a="";return this.order.forEach(function(n,s){if(n.expression instanceof z.NumValue)var i=t.columns[n.expression.value-1];else var i=n.expression;t.orderColumns.push(i);var o="$$$"+s,u="";if(n.expression instanceof z.Column){var c=n.expression.columnid;if(e.xcolumns[c]){var l=e.xcolumns[c].dbtypeid;"DATE"!=l&&"DATETIME"!=l&&"DATETIME2"!=l||(u=".valueOf()")}else A.options.valueof&&(u=".valueOf()")}n.nocase&&(u+=".toUpperCase()"),r+="if((a['"+o+"']||'')"+u+("ASC"==n.direction?">":"<")+"(b['"+o+"']||'')"+u+")return 1;",r+="if((a['"+o+"']||'')"+u+"==(b['"+o+"']||'')"+u+"){",a+="}"}),r+="return 0;",r+=a+"return -1",e.orderfns=r,new Function("a,b","var y;"+r)}},z.Select.prototype.compilePivot=function(e){var t=this,n=t.pivot.columnid,r=t.pivot.expr.expression.columnid,a=t.pivot.expr.aggregatorid,s=t.pivot.inlist;return s&&(s=s.map(function(e){return e.expr.columnid})),function(){var e=this,t=e.columns.filter(function(e){return e.columnid!=n&&e.columnid!=r}).map(function(e){return e.columnid}),i=[],o={},u={},c={},l=[];if(e.data.forEach(function(e){if(!s||s.indexOf(e[n])>-1){var h=t.map(function(t){return e[t]}).join("`"),d=u[h];if(d||(d={},u[h]=d,l.push(d),t.forEach(function(t){d[t]=e[t]})),c[h]||(c[h]={}),c[h][e[n]]?c[h][e[n]]++:c[h][e[n]]=1,o[e[n]]||(o[e[n]]=!0,i.push(e[n])),"SUM"==a||"AVG"==a)"undefined"==typeof d[e[n]]&&(d[e[n]]=0),d[e[n]]+=e[r];else if("COUNT"==a)"undefined"==typeof d[e[n]]&&(d[e[n]]=0),d[e[n]]++;else if("MIN"==a)"undefined"==typeof d[e[n]]&&(d[e[n]]=1/0),e[r]<d[e[n]]&&(d[e[n]]=e[r]);else if("MAX"==a)"undefined"==typeof d[e[n]]&&(d[e[n]]=-(1/0)),e[r]>d[e[n]]&&(d[e[n]]=e[r]);else if("FIRST"==a)"undefined"==typeof d[e[n]]&&(d[e[n]]=e[r]);else if("LAST"==a)d[e[n]]=e[r];else{if(!A.aggr[a])throw new Error("Wrong aggregator in PIVOT clause");A.aggr[a](d[e[n]],e[r])}}}),"AVG"==a)for(var h in u){var d=u[h];for(var f in d)t.indexOf(f)==-1&&f!=r&&(d[f]=d[f]/c[h][f])}e.data=l,s&&(i=s);var p=e.columns.filter(function(e){return e.columnid==r})[0];e.columns=e.columns.filter(function(e){return!(e.columnid==n||e.columnid==r)}),i.forEach(function(t){var n=P(p);n.columnid=t,e.columns.push(n)})}},z.Select.prototype.compileUnpivot=function(e){var t=this,n=t.unpivot.tocolumnid,r=t.unpivot.forcolumnid,a=t.unpivot.inlist.map(function(e){return e.columnid});return function(){var t=[],s=e.columns.map(function(e){return e.columnid}).filter(function(e){return a.indexOf(e)==-1&&e!=r&&e!=n});e.data.forEach(function(e){a.forEach(function(a){var i={};s.forEach(function(t){i[t]=e[t]}),i[r]=a,i[n]=e[a],t.push(i)})}),e.data=t}};var ne=function(e,t){for(var n=[],r=0,a=e.length,s=0;s<a+1;s++){for(var i=[],o=0;o<a;o++){if(e[o]instanceof z.Column){e[o].nick=O(e[o].columnid),t.groupColumns[O(e[o].columnid)]=e[o].nick;var u=e[o].nick+"\t"+e[o].toJS("p",t.sources[0].alias,t.defcols)}else{t.groupColumns[O(e[o].toString())]=O(e[o].toString());var u=O(e[o].toString())+"\t"+e[o].toJS("p",t.sources[0].alias,t.defcols)}r&1<<o&&i.push(u)}n.push(i),r=(r<<1)+1}return n},re=function(e,t){for(var n=[],r=e.length,a=1<<r,s=0;s<a;s++){for(var i=[],o=0;o<r;o++)s&1<<o&&(i=i.concat(b(e[o],t)));n.push(i)}return n},ae=function(e,t){return e.reduce(function(e,n){return e=e.concat(b(n,t))},[])},se=function(e,t){for(var n=[],r=0;r<e.length;r++)for(var a=0;a<t.length;a++)n.push(e[r].concat(t[a]));return n};z.Select.prototype.compileDefCols=function(e,t){var n={".":{}};return this.from&&this.from.forEach(function(e){if(n["."][e.as||e.tableid]=!0,e instanceof z.Table){var r=e.as||e.tableid,a=A.databases[e.databaseid||t].tables[e.tableid];if(void 0===a)throw new Error("Table does not exists: "+e.tableid);a.columns&&a.columns.forEach(function(e){n[e.columnid]?n[e.columnid]="-":n[e.columnid]=r})}else if(e instanceof z.Select);else if(e instanceof z.Search);else if(e instanceof z.ParamValue);else if(e instanceof z.VarValue);else if(e instanceof z.FuncValue);else if(e instanceof z.FromData);else if(e instanceof z.Json);else if(!e.inserted)throw new Error("Unknown type of FROM clause")}),this.joins&&this.joins.forEach(function(e){if(n["."][e.as||e.table.tableid]=!0,e.table){var r=e.table.tableid;e.as&&(r=e.as);var r=e.as||e.table.tableid,a=A.databases[e.table.databaseid||t].tables[e.table.tableid];a.columns&&a.columns.forEach(function(e){n[e.columnid]?n[e.columnid]="-":n[e.columnid]=r})}else if(e.select);else if(e.param);else if(!e.func)throw new Error("Unknown type of FROM clause")}),n},z.Union=function(e){return z.extend(this,e)},z.Union.prototype.toString=function(){return"UNION"},z.Union.prototype.compile=function(e){return null},z.Apply=function(e){return z.extend(this,e)},z.Apply.prototype.toString=function(){var e=this.applymode+" APPLY ("+this.select.toString()+")";return this.as&&(e+=" AS "+this.as),e},z.Over=function(e){return z.extend(this,e)},z.Over.prototype.toString=function(){var e="OVER (";return this.partition&&(e+="PARTITION BY "+this.partition.toString(),this.order&&(e+=" ")),this.order&&(e+="ORDER BY "+this.order.toString()),e+=")"},z.ExpressionStatement=function(e){return z.extend(this,e)},z.ExpressionStatement.prototype.toString=function(){return this.expression.toString()},z.ExpressionStatement.prototype.execute=function(e,t,n){if(this.expression){A.precompile(this,e,t);var r=new Function("params,alasql,p","var y;return "+this.expression.toJS("({})","",null)).bind(this),a=r(t,A);return n&&(a=n(a)),a}},z.Expression=function(e){return z.extend(this,e)},z.Expression.prototype.toString=function(e){var t=this.expression.toString(e);return this.order&&(t+=" "+this.order.toString()),this.nocase&&(t+=" COLLATE NOCASE"),t},z.Expression.prototype.findAggregator=function(e){this.expression.findAggregator&&this.expression.findAggregator(e)},z.Expression.prototype.toJS=function(e,t,n){return this.expression.reduced?"true":this.expression.toJS(e,t,n)},z.Expression.prototype.compile=function(e,t,r){return this.reduced?n():new Function("p","var y;return "+this.toJS(e,t,r))},z.JavaScript=function(e){return z.extend(this,e)},z.JavaScript.prototype.toString=function(){var e="``"+this.value+"``";return e},z.JavaScript.prototype.toJS=function(){return"("+this.value+")"},z.JavaScript.prototype.execute=function(e,t,n){var r=1,a=new Function("params,alasql,p",this.value);return a(t,A),n&&(r=n(r)),r},z.Literal=function(e){return z.extend(this,e)},z.Literal.prototype.toString=function(e){var t=this.value;return this.value1&&(t=this.value1+"."+t),this.alias&&!e&&(t+=" AS "+this.alias),t},z.Join=function(e){return z.extend(this,e)},z.Join.prototype.toString=function(){var e=" ";return this.joinmode&&(e+=this.joinmode+" "),e+="JOIN "+this.table.toString()},z.Table=function(e){return z.extend(this,e)},z.Table.prototype.toString=function(){var e=this.tableid;return this.databaseid&&(e=this.databaseid+"."+e),e},z.View=function(e){return z.extend(this,e)},z.View.prototype.toString=function(){var e=this.viewid;return this.databaseid&&(e=this.databaseid+"."+e),e},z.Op=function(e){return z.extend(this,e)},z.Op.prototype.toString=function(){if("IN"===this.op||"NOT IN"===this.op)return this.left.toString()+" "+this.op+" ("+this.right.toString()+")";if(this.allsome)return this.left.toString()+" "+this.op+" "+this.allsome+" ("+this.right.toString()+")";if("->"===this.op||"!"===this.op){var e=this.left.toString()+this.op;return"string"!=typeof this.right&&"number"!=typeof this.right&&(e+="("),e+=this.right.toString(),"string"!=typeof this.right&&"number"!=typeof this.right&&(e+=")"),e}return this.left.toString()+" "+this.op+" "+(this.allsome?this.allsome+" ":"")+this.right.toString()},z.Op.prototype.findAggregator=function(e){this.left&&this.left.findAggregator&&this.left.findAggregator(e),this.right&&this.right.findAggregator&&!this.allsome&&this.right.findAggregator(e)},z.Op.prototype.toType=function(e){if(["-","*","/","%","^"].indexOf(this.op)>-1)return"number";if(["||"].indexOf(this.op)>-1)return"string";if("+"===this.op){if("string"===this.left.toType(e)||"string"===this.right.toType(e))return"string";if("number"===this.left.toType(e)||"number"===this.right.toType(e))return"number"}return["AND","OR","NOT","=","==","===","!=","!==","!===",">",">=","<","<=","IN","NOT IN","LIKE","NOT LIKE","REGEXP","GLOB"].indexOf(this.op)>-1?"boolean":"BETWEEN"===this.op||"NOT BETWEEN"===this.op||"IS NULL"===this.op||"IS NOT NULL"===this.op?"boolean":this.allsome?"boolean":this.op?"unknown":this.left.toType()},z.Op.prototype.toJS=function(e,t,n){var r,a=[],s=this.op,i=this,o=function(r){r.toJS&&(r=r.toJS(e,t,n));var s=a.push(r)-1;return"y["+s+"]"},u=function(){return o(i.left)},c=function(){return o(i.right)};if("="===this.op?s="===":"<>"===this.op?s="!=":"OR"===this.op&&(s="||"),"->"===this.op){var l="("+u()+"||{})";if("string"==typeof this.right)r=l+'["'+this.right+'"]';else if("number"==typeof this.right)r=l+"["+this.right+"]";else if(this.right instanceof z.FuncValue){var h=[];if(this.right.args&&0!==this.right.args.length)var h=this.right.args.map(o);r=""+l+"['"+this.right.funcid+"']("+h.join(",")+")"}else r=""+l+"["+c()+"]"}if("!"===this.op&&"string"==typeof this.right&&(r="alasql.databases[alasql.useid].objects["+u()+']["'+this.right+'"]'),"IS"===this.op&&(r="(("+u()+"==null) === ("+c()+"==null))"),"=="===this.op&&(r="alasql.utils.deepEqual("+u()+","+c()+")"),"==="!==this.op&&"!==="!==this.op||(r="("+("!==="===this.op?"!":"")+"(("+u()+").valueOf()===("+c()+").valueOf()))"),"!=="===this.op&&(r="(!alasql.utils.deepEqual("+u()+","+c()+"))"),"||"===this.op&&(r="(''+("+u()+"||'')+("+c()+'||""))'),"LIKE"===this.op||"NOT LIKE"===this.op){var r="("+("NOT LIKE"===this.op?"!":"")+"alasql.utils.like("+c()+","+u();this.escape&&(r+=","+o(this.escape)),r+="))"}if("REGEXP"===this.op&&(r="alasql.stdfn.REGEXP_LIKE("+u()+","+c()+")"),"GLOB"===this.op&&(r="alasql.utils.glob("+u()+","+c()+")"),"BETWEEN"===this.op||"NOT BETWEEN"===this.op){var d=u();r="("+("NOT BETWEEN"===this.op?"!":"")+"(("+o(this.right1)+"<="+d+") && ("+d+"<="+o(this.right2)+")))"}if("IN"===this.op&&(this.right instanceof z.Select?(r="(",r+="alasql.utils.flatArray(this.queriesfn["+this.queriesidx+"](params,null,"+e+"))",r+=".indexOf(",r+=u()+")>-1)"):r=this.right instanceof Array?"(["+this.right.map(o).join(",")+"].indexOf("+u()+")>-1)":"("+c()+".indexOf("+u()+")>-1)"),"NOT IN"===this.op&&(this.right instanceof z.Select?(r="(",r+="alasql.utils.flatArray(this.queriesfn["+this.queriesidx+"](params,null,p))",r+=".indexOf(",r+=u()+")<0)"):this.right instanceof Array?(r="(["+this.right.map(o).join(",")+"].indexOf(",r+=u()+")<0)"):(r="("+c()+".indexOf(",r+=u()+")==-1)")),"ALL"===this.allsome){var r;if(this.right instanceof z.Select)r="alasql.utils.flatArray(this.query.queriesfn["+this.queriesidx+"](params,null,p))",r+=".every(function(b){return (",r+=u()+")"+s+"b})";else{if(!(this.right instanceof Array))throw new Error("NOT IN operator without SELECT");r=""+(1==this.right.length?o(this.right[0]):"["+this.right.map(o).join(",")+"]"),r+=".every(function(b){return (",r+=u()+")"+s+"b})"}}if("SOME"===this.allsome||"ANY"===this.allsome){var r;if(this.right instanceof z.Select)r="alasql.utils.flatArray(this.query.queriesfn["+this.queriesidx+"](params,null,p))",r+=".some(function(b){return (",r+=u()+")"+s+"b})";else{if(!(this.right instanceof Array))throw new Error("SOME/ANY operator without SELECT");r=""+(1==this.right.length?o(this.right[0]):"["+this.right.map(o).join(",")+"]"),r+=".some(function(b){return (",r+=u()+")"+s+"b})"}}if("AND"===this.op){if(this.left.reduced){if(this.right.reduced)return"true";r=c()}else this.right.reduced&&(r=u());s="&&"}var f=r||"("+u()+s+c()+")",p="y=[("+a.join("), (")+")]";return"&&"==s||"||"==s||"IS"==s||"IS NULL"==s||"IS NOT NULL"==s?"("+p+", "+f+")":"("+p+", y.some(function(e){return e == null}) ? void 0 : "+f+")"},z.VarValue=function(e){return z.extend(this,e)},z.VarValue.prototype.toString=function(){return"@"+this.variable},z.VarValue.prototype.toType=function(){return"unknown"},z.VarValue.prototype.toJS=function(){return"alasql.vars['"+this.variable+"']"},z.NumValue=function(e){return z.extend(this,e)},z.NumValue.prototype.toString=function(){return this.value.toString()},z.NumValue.prototype.toType=function(){return"number"},z.NumValue.prototype.toJS=function(){return""+this.value},z.StringValue=function(e){return z.extend(this,e)},z.StringValue.prototype.toString=function(){return"'"+this.value.toString()+"'"},z.StringValue.prototype.toType=function(){return"string"},z.StringValue.prototype.toJS=function(){return"'"+O(this.value)+"'"},z.DomainValueValue=function(e){return z.extend(this,e)},z.DomainValueValue.prototype.toString=function(){return"VALUE"},z.DomainValueValue.prototype.toType=function(){return"object"},z.DomainValueValue.prototype.toJS=function(e,t,n){return e},z.ArrayValue=function(e){return z.extend(this,e)},z.ArrayValue.prototype.toString=function(){return"ARRAY[]"},z.ArrayValue.prototype.toType=function(){return"object"},z.ArrayValue.prototype.toJS=function(e,t,n){return"[("+this.value.map(function(r){return r.toJS(e,t,n)}).join("), (")+")]"},z.LogicValue=function(e){return z.extend(this,e)},z.LogicValue.prototype.toString=function(){return this.value?"TRUE":"FALSE"},z.LogicValue.prototype.toType=function(){return"boolean"},z.LogicValue.prototype.toJS=function(){return this.value?"true":"false"},z.NullValue=function(e){return z.extend(this,e)},z.NullValue.prototype.toString=function(){return"NULL"},z.NullValue.prototype.toJS=function(){return"undefined"},z.ParamValue=function(e){return z.extend(this,e)},z.ParamValue.prototype.toString=function(){return"$"+this.param},z.ParamValue.prototype.toJS=function(){return"string"==typeof this.param?"params['"+this.param+"']":"params["+this.param+"]"},z.UniOp=function(e){return z.extend(this,e)},z.UniOp.prototype.toString=function(){var e;return"~"===this.op&&(e=this.op+this.right.toString()),"-"===this.op&&(e=this.op+this.right.toString()),"+"===this.op&&(e=this.op+this.right.toString()),"#"===this.op&&(e=this.op+this.right.toString()),"NOT"===this.op&&(e=this.op+"("+this.right.toString()+")"),null==this.op&&(e="("+this.right.toString()+")"),"(y = "+e+", y === void 0 ? void 0 : y)"},z.UniOp.prototype.findAggregator=function(e){this.right.findAggregator&&this.right.findAggregator(e)},z.UniOp.prototype.toType=function(){return"-"===this.op?"number":"+"===this.op?"number":"NOT"===this.op?"boolean":void 0},z.UniOp.prototype.toJS=function(e,t,n){return"~"===this.op?"(~("+this.right.toJS(e,t,n)+"))":"-"===this.op?"(-("+this.right.toJS(e,t,n)+"))":"+"===this.op?"("+this.right.toJS(e,t,n)+")":"NOT"===this.op?"!("+this.right.toJS(e,t,n)+")":"#"===this.op?this.right instanceof z.Column?"(alasql.databases[alasql.useid].objects['"+this.right.columnid+"'])":"(alasql.databases[alasql.useid].objects["+this.right.toJS(e,t,n)+"])":null==this.op?"("+this.right.toJS(e,t,n)+")":void 0},z.Column=function(e){return z.extend(this,e)},z.Column.prototype.toString=function(e){var t;return t=this.columnid==+this.columnid?"["+this.columnid+"]":this.columnid,this.tableid&&(t=+this.columnid===this.columnid?this.tableid+t:this.tableid+"."+t,this.databaseid&&(t=this.databaseid+"."+t)),this.alias&&!e&&(t+=" AS "+this.alias),t},z.Column.prototype.toJS=function(e,t,n){var r="";if(this.tableid||""!==t||n)if("g"===e)r="g['"+this.nick+"']";else if(this.tableid)r="_"!==this.columnid?e+"['"+this.tableid+"']['"+this.columnid+"']":"g"===e?"g['_']":e+"['"+this.tableid+"']";else if(n){var a=n[this.columnid];if("-"===a)throw new Error('Cannot resolve column "'+this.columnid+'" because it exists in two source tables');r=a?"_"!==this.columnid?e+"['"+a+"']['"+this.columnid+"']":e+"['"+a+"']":"_"!==this.columnid?e+"['"+(this.tableid||t)+"']['"+this.columnid+"']":e+"['"+(this.tableid||t)+"']"}else r=t===-1?e+"['"+this.columnid+"']":"_"!==this.columnid?e+"['"+(this.tableid||t)+"']['"+this.columnid+"']":e+"['"+(this.tableid||t)+"']";else r="_"!==this.columnid?e+"['"+this.columnid+"']":"g"===e?"g['_']":e;return r},z.AggrValue=function(e){return z.extend(this,e)},z.AggrValue.prototype.toString=function(e){var t="";return t+="REDUCE"===this.aggregatorid?this.funcid+"(":this.aggregatorid+"(",this.distinct&&(t+="DISTINCT "),this.expression&&(t+=this.expression.toString()),t+=")",this.over&&(t+=" "+this.over.toString()),this.alias&&!e&&(t+=" AS "+this.alias),t},z.AggrValue.prototype.findAggregator=function(e){var t=O(this.toString())+":"+e.selectGroup.length,n=!1;if(!n){if(!this.nick){this.nick=t;for(var n=!1,r=0;r<e.removeKeys.length;r++)if(e.removeKeys[r]===t){n=!0;break}n||e.removeKeys.push(t)}e.selectGroup.push(this)}},z.AggrValue.prototype.toType=function(){return["SUM","COUNT","AVG","MIN","MAX","AGGR","VAR","STDDEV"].indexOf(this.aggregatorid)>-1?"number":["ARRAY"].indexOf(this.aggregatorid)>-1?"array":["FIRST","LAST"].indexOf(this.aggregatorid)>-1?this.expression.toType():void 0},z.AggrValue.prototype.toJS=function(){var e=this.nick;return void 0===e&&(e=this.toString()),"g['"+e+"']"},z.OrderExpression=function(e){return z.extend(this,e)},z.OrderExpression.prototype.toString=z.Expression.prototype.toString,z.GroupExpression=function(e){return z.extend(this,e)},z.GroupExpression.prototype.toString=function(){return this.type+"("+this.group.toString()+")"},z.FromData=function(e){return z.extend(this,e)},z.FromData.prototype.toString=function(){return this.data?"DATA("+(1e16*Math.random()|0)+")":"?"},z.FromData.prototype.toJS=function(){},z.Select.prototype.exec=function(e,t){this.preparams&&(e=this.preparams.concat(e));var n=A.useid;db=A.databases[n];var r=this.toString(),a=L(r),s=this.compile(n);if(s){s.sql=r,s.dbversion=db.dbversion,db.sqlCacheSize>A.MAXSQLCACHESIZE&&db.resetSqlCache(),db.sqlCacheSize++,db.sqlCache[a]=s;var i=A.res=s(e,t);return i}},z.Select.prototype.Select=function(){var e=this;if(arguments.length>1)args=Array.prototype.slice.call(arguments);else{if(1!=arguments.length)throw new Error("Wrong number of arguments of Select() function");arguments[0]instanceof Array?args=arguments[0]:args=[arguments[0]]}return e.columns=[],args.forEach(function(t){if("string"==typeof t)e.columns.push(new z.Column({columnid:t}));else if("function"==typeof t){var n=0;e.preparams?n=e.preparams.length:e.preparams=[],e.preparams.push(t),e.columns.push(new z.Column({columnid:"*",func:t,param:n}))}}),e},z.Select.prototype.From=function(e){var t=this;if(t.from||(t.from=[]),e instanceof Array){var n=0;t.preparams?n=t.preparams.length:t.preparams=[],t.preparams.push(e),t.from.push(new z.ParamValue({param:n}))}else{if("string"!=typeof e)throw new Error("Unknown arguments in From() function");t.from.push(new z.Table({tableid:e}))}return t},z.Select.prototype.OrderBy=function(){var e=this;if(e.order=[],0==arguments.length)args=["_"];else if(arguments.length>1)args=Array.prototype.slice.call(arguments);else{if(1!=arguments.length)throw new Error("Wrong number of arguments of Select() function");arguments[0]instanceof Array?args=arguments[0]:args=[arguments[0]]}return args.length>0&&args.forEach(function(t){var n=new z.Column({columnid:t});"function"==typeof t&&(n=t),e.order.push(new z.OrderExpression({expression:n,direction:"ASC"}))}),e},z.Select.prototype.Top=function(e){var t=this;return t.top=new z.NumValue({value:e}),t},z.Select.prototype.GroupBy=function(){var e=this;if(arguments.length>1)args=Array.prototype.slice.call(arguments);else{if(1!=arguments.length)throw new Error("Wrong number of arguments of Select() function");arguments[0]instanceof Array?args=arguments[0]:args=[arguments[0]]}return e.group=[],args.forEach(function(t){var n=new z.Column({columnid:t});e.group.push(n)}),e},z.Select.prototype.Where=function(e){var t=this;return"function"==typeof e&&(t.where=e),t},z.FuncValue=function(e){return z.extend(this,e)},z.FuncValue.prototype.toString=function(e){var t="";return A.fn[this.funcid]?t+=this.funcid:A.aggr[this.funcid]?t+=this.funcid:(A.stdlib[this.funcid.toUpperCase()]||A.stdfn[this.funcid.toUpperCase()])&&(t+=this.funcid.toUpperCase()),t+="(",this.args&&this.args.length>0&&(t+=this.args.map(function(e){return e.toString()}).join(",")),t+=")",this.as&&!e&&(t+=" AS "+this.as.toString()),t},z.FuncValue.prototype.execute=function(e,t,n){var r=1;A.precompile(this,e,t);var a=new Function("params,alasql","var y;return "+this.toJS("","",null));return a(t,A),n&&(r=n(r)),r},z.FuncValue.prototype.findAggregator=function(e){this.args&&this.args.length>0&&this.args.forEach(function(t){t.findAggregator&&t.findAggregator(e)})},z.FuncValue.prototype.toJS=function(e,t,n){var r="",a=this.funcid;return!A.fn[a]&&A.stdlib[a.toUpperCase()]?r+=this.args&&this.args.length>0?A.stdlib[a.toUpperCase()].apply(this,this.args.map(function(n){return n.toJS(e,t)})):A.stdlib[a.toUpperCase()]():!A.fn[a]&&A.stdfn[a.toUpperCase()]?(this.newid&&(r+="new "),r+="alasql.stdfn."+this.funcid.toUpperCase()+"(",this.args&&this.args.length>0&&(r+=this.args.map(function(r){return r.toJS(e,t,n)}).join(",")),r+=")"):(this.newid&&(r+="new "),r+="alasql.fn."+this.funcid+"(",this.args&&this.args.length>0&&(r+=this.args.map(function(r){return r.toJS(e,t,n)}).join(",")),r+=")"),r};var ie=A.stdlib={},oe=A.stdfn={};ie.ABS=function(e){return"Math.abs("+e+")"},ie.CLONEDEEP=function(e){return"alasql.utils.cloneDeep("+e+")"},oe.CONCAT=function(){return Array.prototype.slice.call(arguments).join("")},ie.EXP=function(e){return"Math.pow(Math.E,"+e+")"},ie.IIF=function(e,t,n){if(3==arguments.length)return"(("+e+")?("+t+"):("+n+"))";throw new Error("Number of arguments of IFF is not equals to 3")},ie.IFNULL=function(e,t){return"("+e+"||"+t+")"},ie.INSTR=function(e,t){return"(("+e+").indexOf("+t+")+1)"},ie.LEN=ie.LENGTH=function(e){return t(e,"y.length")},ie.LOWER=ie.LCASE=function(e){return t(e,"String(y).toLowerCase()")},ie.MAX=ie.GREATEST=function(){return"Math.max("+Array.prototype.join.call(arguments,",")+")"},ie.MIN=ie.LEAST=function(){return"Math.min("+Array.prototype.join.call(arguments,",")+")"},ie.SUBSTRING=ie.SUBSTR=ie.MID=function(e,n,r){return 2==arguments.length?t(e,"y.substr("+n+"-1)"):3==arguments.length?t(e,"y.substr("+n+"-1,"+r+")"):void 0},oe.REGEXP_LIKE=function(e,t,n){return(e||"").search(RegExp(t,n))>-1},ie.ISNULL=ie.NULLIF=function(e,t){return"("+e+"=="+t+"?undefined:"+e+")"},ie.POWER=function(e,t){return"Math.pow("+e+","+t+")"},ie.RANDOM=function(e){return 0==arguments.length?"Math.random()":"(Math.random()*("+e+")|0)"},ie.ROUND=function(e,t){return 2==arguments.length?"Math.round(("+e+")*Math.pow(10,("+t+")))/Math.pow(10,("+t+"))":"Math.round("+e+")"},ie.CEIL=ie.CEILING=function(e){return"Math.ceil("+e+")"},ie.FLOOR=function(e){return"Math.floor("+e+")"},ie.ROWNUM=function(){return"1"},ie.ROW_NUMBER=function(){return"1"},ie.SQRT=function(e){return"Math.sqrt("+e+")"},ie.TRIM=function(e){return t(e,"y.trim()")},ie.UPPER=ie.UCASE=function(e){return t(e,"String(y).toUpperCase()")},oe.CONCAT_WS=function(){return args=Array.prototype.slice.call(arguments),args.slice(1,args.length).join(args[0])},A.aggr.GROUP_CONCAT=function(e,t,n){return 1==n?e:2==n?t+","+e:void 0},A.aggr.MEDIAN=function(e,t,n){if(2===n)return null===e?t:(t.push(e),t);if(1===n)return null===e?[]:[e];var r=t.sort();return r[r.length/2|0]},A.aggr.VAR=function(e,t,n){if(1===n)return null===e?{arr:[],sum:0}:{arr:[e],sum:e};if(2===n)return null===e?t:(t.arr.push(e),t.sum+=e,t);for(var r=t.arr.length,a=t.sum/r,s=0,i=0;i<r;i++)s+=(t.arr[i]-a)*(t.arr[i]-a);return s/=r-1},A.aggr.STDEV=function(e,t,n){return 1===n||2===n?A.aggr.VAR(e,t,n):Math.sqrt(A.aggr.VAR(e,t,n))},A.aggr.VARP=function(e,t,n){if(1==n)return{arr:[e],sum:e};if(2==n)return t.arr.push(e),t.sum+=e,t;for(var r=t.arr.length,a=t.sum/r,s=0,i=0;i<r;i++)s+=(t.arr[i]-a)*(t.arr[i]-a);return s/=r},A.aggr.STD=A.aggr.STDDEV=A.aggr.STDEVP=function(e,t,n){return 1==n||2==n?A.aggr.VARP(e,t,n):Math.sqrt(A.aggr.VARP(e,t,n))},oe.REPLACE=function(e,t,n){return(e||"").split(t).join(n)};for(var ue=[],ce=0;ce<256;ce++)ue[ce]=(ce<16?"0":"")+ce.toString(16);oe.NEWID=oe.UUID=oe.GEN_RANDOM_UUID=function(){var e=4294967295*Math.random()|0,t=4294967295*Math.random()|0,n=4294967295*Math.random()|0,r=4294967295*Math.random()|0;return ue[255&e]+ue[e>>8&255]+ue[e>>16&255]+ue[e>>24&255]+"-"+ue[255&t]+ue[t>>8&255]+"-"+ue[t>>16&15|64]+ue[t>>24&255]+"-"+ue[63&n|128]+ue[n>>8&255]+"-"+ue[n>>16&255]+ue[n>>24&255]+ue[255&r]+ue[r>>8&255]+ue[r>>16&255]+ue[r>>24&255]},z.CaseValue=function(e){return z.extend(this,e)},z.CaseValue.prototype.toString=function(){var e="CASE ";return this.expression&&(e+=this.expression.toString()),this.whens&&(e+=this.whens.map(function(e){return" WHEN "+e.when.toString()+" THEN "+e.then.toString()}).join()),e+=" END"},z.CaseValue.prototype.findAggregator=function(e){this.expression&&this.expression.findAggregator&&this.expression.findAggregator(e),this.whens&&this.whens.length>0&&this.whens.forEach(function(t){t.when.findAggregator&&t.when.findAggregator(e),t.then.findAggregator&&t.then.findAggregator(e)}),this.elses&&this.elses.findAggregator&&this.elses.findAggregator(e)},z.CaseValue.prototype.toJS=function(e,t,n){var r="((function("+e+",params,alasql){var y,r;";return this.expression?(r+="v="+this.expression.toJS(e,t,n)+";",r+=(this.whens||[]).map(function(r){return" if(v=="+r.when.toJS(e,t,n)+") {r="+r.then.toJS(e,t,n)+"}"}).join(" else "),this.elses&&(r+=" else {r="+this.elses.toJS(e,t,n)+"}")):(r+=(this.whens||[]).map(function(r){return" if("+r.when.toJS(e,t,n)+") {r="+r.then.toJS(e,t,n)+"}"}).join(" else "),this.elses&&(r+=" else {r="+this.elses.toJS(e,t,n)+"}")),r+=";return r;}).bind(this))("+e+",params,alasql)"},z.Json=function(e){return z.extend(this,e)},z.Json.prototype.toString=function(){var e="";return e+=le(this.value),e+=""};var le=A.utils.JSONtoString=function(e){var t="";if("string"==typeof e)t='"'+e+'"';else if("number"==typeof e)t=e;else if("boolean"==typeof e)t=e;else{if("object"!=typeof e)throw new Error("2Can not show JSON object "+JSON.stringify(e));if(e instanceof Array)t+="["+e.map(function(e){return le(e)}).join(",")+"]";else if(!e.toJS||e instanceof z.Json){t="{";var n=[];for(var r in e){var a="";if("string"==typeof r)a+='"'+r+'"';else if("number"==typeof r)a+=r;else{if("boolean"!=typeof r)throw new Error("THis is not ES6... no expressions on left side yet");a+=r}a+=":"+le(e[r]),n.push(a)}t+=n.join(",")+"}"}else{if(!e.toString)throw new Error("1Can not show JSON object "+JSON.stringify(e));t=e.toString()}}return t};z.Json.prototype.toJS=function(e,t,n){return E(this.value,e,t,n)},z.Convert=function(e){return z.extend(this,e)},z.Convert.prototype.toString=function(){var e="CONVERT(";return e+=this.dbtypeid,"undefined"!=typeof this.dbsize&&(e+="("+this.dbsize,this.dbprecision&&(e+=","+dbprecision),e+=")"),e+=","+this.expression.toString(),this.style&&(e+=","+this.style),e+=")"},z.Convert.prototype.toJS=function(e,t,n){return"alasql.stdfn.CONVERT("+this.expression.toJS(e,t,n)+',{dbtypeid:"'+this.dbtypeid+'",dbsize:'+this.dbsize+",style:"+this.style+"})"},A.stdfn.CONVERT=function(e,t){var n=e;if(t.style){var r;switch(r=/\d{8}/.test(n)?new Date((+n.substr(0,4)),+n.substr(4,2)-1,(+n.substr(6,2))):new Date(n),t.style){case 1:n=("0"+(r.getMonth()+1)).substr(-2)+"/"+("0"+r.getDate()).substr(-2)+"/"+("0"+r.getYear()).substr(-2);break;case 2:n=("0"+r.getYear()).substr(-2)+"."+("0"+(r.getMonth()+1)).substr(-2)+"."+("0"+r.getDate()).substr(-2);break;case 3:n=("0"+r.getDate()).substr(-2)+"/"+("0"+(r.getMonth()+1)).substr(-2)+"/"+("0"+r.getYear()).substr(-2);break;case 4:n=("0"+r.getDate()).substr(-2)+"."+("0"+(r.getMonth()+1)).substr(-2)+"."+("0"+r.getYear()).substr(-2);break;case 5:n=("0"+r.getDate()).substr(-2)+"-"+("0"+(r.getMonth()+1)).substr(-2)+"-"+("0"+r.getYear()).substr(-2);break;case 6:n=("0"+r.getDate()).substr(-2)+" "+r.toString().substr(4,3).toLowerCase()+" "+("0"+r.getYear()).substr(-2);break;case 7:n=r.toString().substr(4,3)+" "+("0"+r.getDate()).substr(-2)+","+("0"+r.getYear()).substr(-2);break;case 8:case 108:n=("0"+r.getHours()).substr(-2)+":"+("0"+r.getMinutes()).substr(-2)+":"+("0"+r.getSeconds()).substr(-2);break;case 10:n=("0"+(r.getMonth()+1)).substr(-2)+"-"+("0"+r.getDate()).substr(-2)+"-"+("0"+r.getYear()).substr(-2);break;case 11:n=("0"+r.getYear()).substr(-2)+"/"+("0"+(r.getMonth()+1)).substr(-2)+"/"+("0"+r.getDate()).substr(-2);break;case 12:n=("0"+r.getYear()).substr(-2)+("0"+(r.getMonth()+1)).substr(-2)+("0"+r.getDate()).substr(-2);break;case 101:n=("0"+(r.getMonth()+1)).substr(-2)+"/"+("0"+r.getDate()).substr(-2)+"/"+r.getFullYear();break;case 102:n=r.getFullYear()+"."+("0"+(r.getMonth()+1)).substr(-2)+"."+("0"+r.getDate()).substr(-2);break;case 103:n=("0"+r.getDate()).substr(-2)+"/"+("0"+(r.getMonth()+1)).substr(-2)+"/"+r.getFullYear();break;case 104:n=("0"+r.getDate()).substr(-2)+"."+("0"+(r.getMonth()+1)).substr(-2)+"."+r.getFullYear();break;case 105:n=("0"+r.getDate()).substr(-2)+"-"+("0"+(r.getMonth()+1)).substr(-2)+"-"+r.getFullYear();break;case 106:n=("0"+r.getDate()).substr(-2)+" "+r.toString().substr(4,3).toLowerCase()+" "+r.getFullYear();break;case 107:n=r.toString().substr(4,3)+" "+("0"+r.getDate()).substr(-2)+","+r.getFullYear();break;case 110:n=("0"+(r.getMonth()+1)).substr(-2)+"-"+("0"+r.getDate()).substr(-2)+"-"+r.getFullYear();
break;case 111:n=r.getFullYear()+"/"+("0"+(r.getMonth()+1)).substr(-2)+"/"+("0"+r.getDate()).substr(-2);break;case 112:n=r.getFullYear()+("0"+(r.getMonth()+1)).substr(-2)+("0"+r.getDate()).substr(-2);break;default:throw new Error("The CONVERT style "+t.style+" is not realized yet.")}}var a=t.dbtypeid.toUpperCase();if("Date"==t.dbtypeid)return new Date(n);if("DATE"==a){var s=new Date(n),i=s.getFullYear()+"."+("0"+(s.getMonth()+1)).substr(-2)+"."+("0"+s.getDate()).substr(-2);return i}if("DATETIME"==a||"DATETIME2"==a){var s=new Date(n),i=s.getFullYear()+"."+("0"+(s.getMonth()+1)).substr(-2)+"."+("0"+s.getDate()).substr(-2);return i+=" "+("0"+s.getHours()).substr(-2)+":"+("0"+s.getMinutes()).substr(-2)+":"+("0"+s.getSeconds()).substr(-2),i+="."+("00"+s.getMilliseconds()).substr(-3)}if(["MONEY"].indexOf(a)>-1){var o=+n;return(0|o)+100*o%100/100}if(["BOOLEAN"].indexOf(a)>-1)return!!n;if(["INT","INTEGER","SMALLINT","BIGINT","SERIAL","SMALLSERIAL","BIGSERIAL"].indexOf(t.dbtypeid.toUpperCase())>-1)return 0|n;if(["STRING","VARCHAR","NVARCHAR","CHARACTER VARIABLE"].indexOf(t.dbtypeid.toUpperCase())>-1)return t.dbsize?(""+n).substr(0,t.dbsize):""+n;if(["CHAR","CHARACTER","NCHAR"].indexOf(a)>-1)return(n+new Array(t.dbsize+1).join(" ")).substr(0,t.dbsize);if(["NUMBER","FLOAT"].indexOf(a)>-1){if("undefined"!=typeof t.dbprecision){var o=+n,u=Math.pow(10,t.dbprecision);return(0|o)+o*u%u/u}return+n}if(["DECIMAL","NUMERIC"].indexOf(a)>-1){var o=+n,u=Math.pow(10,t.dbprecision);return(0|o)+o*u%u/u}if(["JSON"].indexOf(a)>-1){if("object"==typeof n)return n;try{return JSON.parse(n)}catch(e){throw new Error("Cannot convert string to JSON")}}return n},z.ColumnDef=function(e){return z.extend(this,e)},z.ColumnDef.prototype.toString=function(){var e=this.columnid;return this.dbtypeid&&(e+=" "+this.dbtypeid),this.dbsize&&(e+="("+this.dbsize,this.dbprecision&&(e+=","+this.dbprecision),e+=")"),this.primarykey&&(e+=" PRIMARY KEY"),this.notnull&&(e+=" NOT NULL"),e},z.CreateTable=function(e){return z.extend(this,e)},z.CreateTable.prototype.toString=function(){var e="CREATE";if(this.temporary&&(e+=" TEMPORARY"),e+=this.view?" VIEW":" "+(this.class?"CLASS":"TABLE"),this.ifnotexists&&(e+=" IF  NOT EXISTS"),e+=" "+this.table.toString(),this.viewcolumns&&(e+="("+this.viewcolumns.map(function(e){return e.toString()}).join(",")+")"),this.as)e+=" AS "+this.as;else{var t=this.columns.map(function(e){return e.toString()});e+=" ("+t.join(",")+")"}return this.view&&this.select&&(e+=" AS "+this.select.toString()),e},z.CreateTable.prototype.execute=function(e,t,n){var r=A.databases[this.table.databaseid||e],a=this.table.tableid;if(!a)throw new Error("Table name is not defined");var s=this.columns,i=this.constraints||[];if(this.ifnotexists&&r.tables[a])return n?n(0):0;if(r.tables[a])throw new Error("Can not create table '"+a+"', because it already exists in the database '"+r.databaseid+"'");var o=r.tables[a]=new A.Table;this.class&&(o.isclass=!0);var u=[],c=[];if(s&&s.forEach(function(e){var t=e.dbtypeid;A.fn[t]||(t=t.toUpperCase()),["SERIAL","SMALLSERIAL","BIGSERIAL"].indexOf(t)>-1&&(e.identity={value:1,step:1});var n={columnid:e.columnid,dbtypeid:t,dbsize:e.dbsize,dbprecision:e.dbprecision,notnull:e.notnull,identity:e.identity};if(e.identity&&(o.identities[e.columnid]={value:+e.identity.value,step:+e.identity.step}),e.check&&o.checks.push({id:e.check.constrantid,fn:new Function("r","var y;return "+e.check.expression.toJS("r",""))}),e.default&&u.push("'"+e.columnid+"':"+e.default.toJS("r","")),e.primarykey){var r=o.pk={};r.columns=[e.columnid],r.onrightfns="r['"+e.columnid+"']",r.onrightfn=new Function("r","var y;return "+r.onrightfns),r.hh=L(r.onrightfns),o.uniqs[r.hh]={}}if(e.unique){var a={};o.uk=o.uk||[],o.uk.push(a),a.columns=[e.columnid],a.onrightfns="r['"+e.columnid+"']",a.onrightfn=new Function("r","var y;return "+a.onrightfns),a.hh=L(a.onrightfns),o.uniqs[a.hh]={}}if(e.foreignkey){var s=e.foreignkey.table,i=A.databases[s.databaseid||A.useid].tables[s.tableid];if("undefined"==typeof s.columnid){if(!(i.pk.columns&&i.pk.columns.length>0))throw new Error("FOREIGN KEY allowed only to tables with PRIMARY KEYs");s.columnid=i.pk.columns[0]}var l=function(t){var n={};if("undefined"==typeof t[e.columnid])return!0;n[s.columnid]=t[e.columnid];var r=i.pk.onrightfn(n);if(!i.uniqs[i.pk.hh][r])throw new Error('Foreign key "'+t[e.columnid]+'" is not found in table '+i.tableid);return!0};o.checks.push({fn:l})}e.onupdate&&c.push("r['"+e.columnid+"']="+e.onupdate.toJS("r","")),o.columns.push(n),o.xcolumns[n.columnid]=n}),o.defaultfns=u.join(","),o.onupdatefns=c.join(";"),i.forEach(function(e){var t;if("PRIMARY KEY"===e.type){if(o.pk)throw new Error("Primary key already exists");var n=o.pk={};n.columns=e.columns,n.onrightfns=n.columns.map(function(e){return"r['"+e+"']"}).join("+'`'+"),n.onrightfn=new Function("r","var y;return "+n.onrightfns),n.hh=L(n.onrightfns),o.uniqs[n.hh]={}}else if("CHECK"===e.type)t=new Function("r","var y;return "+e.expression.toJS("r",""));else if("UNIQUE"===e.type){var r={};o.uk=o.uk||[],o.uk.push(r),r.columns=e.columns,r.onrightfns=r.columns.map(function(e){return"r['"+e+"']"}).join("+'`'+"),r.onrightfn=new Function("r","var y;return "+r.onrightfns),r.hh=L(r.onrightfns),o.uniqs[r.hh]={}}else if("FOREIGN KEY"===e.type){var a=o.xcolumns[e.columns[0]],s=e.fktable;e.fkcolumns&&e.fkcolumns.length>0&&(s.columnid=e.fkcolumns[0]);var i=A.databases[s.databaseid||A.useid].tables[s.tableid];"undefined"==typeof s.columnid&&(s.columnid=i.pk.columns[0]),t=function(e){var t={};if("undefined"==typeof e[a.columnid])return!0;t[s.columnid]=e[a.columnid];var n=i.pk.onrightfn(t);if(!i.uniqs[i.pk.hh][n])throw new Error('Foreign key "'+e[a.columnid]+'" is not found in table '+i.tableid);return!0}}t&&o.checks.push({fn:t,id:e.constraintid,fk:"FOREIGN KEY"===e.type})}),this.view&&this.viewcolumns){var l=this;this.viewcolumns.forEach(function(e,t){l.select.columns[t].as=e.columnid})}if(this.view&&this.select&&(o.view=!0,o.select=this.select.compile(this.table.databaseid||e)),r.engineid)return A.engines[r.engineid].createTable(this.table.databaseid||e,a,this.ifnotexists,n);o.insert=function(n,r){var a=A.inserted;A.inserted=[n];var s=this,i=!1,o=!1;for(var u in s.beforeinsert){var c=s.beforeinsert[u];c&&(c.funcid?A.fn[c.funcid](n)===!1&&(o=o||!0):c.statement&&c.statement.execute(e)===!1&&(o=o||!0))}if(!o){var l=!1;for(var u in s.insteadofinsert){l=!0;var c=s.insteadofinsert[u];c&&(c.funcid?A.fn[c.funcid](n):c.statement&&c.statement.execute(e))}if(!l){for(var h in s.identities){var d=s.identities[h];n[h]=d.value}if(s.checks&&s.checks.length>0&&s.checks.forEach(function(e){if(!e.fn(n))throw new Error("Violation of CHECK constraint "+(e.id||""))}),s.columns.forEach(function(e){if(e.notnull&&"undefined"==typeof n[e.columnid])throw new Error("Wrong NULL value in NOT NULL column "+e.columnid)}),s.pk){var f=s.pk,p=f.onrightfn(n);if("undefined"!=typeof s.uniqs[f.hh][p]){if(!r)throw new Error("Cannot insert record, because it already exists in primary key index");i=s.uniqs[f.hh][p]}}if(s.uk&&s.uk.length&&s.uk.forEach(function(e){var t=e.onrightfn(n);if("undefined"!=typeof s.uniqs[e.hh][t]){if(!r)throw new Error("Cannot insert record, because it already exists in unique index");i=s.uniqs[e.hh][t]}}),i)s.update(function(e){for(var t in n)e[t]=n[t]},s.data.indexOf(i),t);else{s.data.push(n);for(var h in s.identities){var d=s.identities[h];d.value+=d.step}if(s.pk){var f=s.pk,p=f.onrightfn(n);s.uniqs[f.hh][p]=n}s.uk&&s.uk.length&&s.uk.forEach(function(e){var t=e.onrightfn(n);s.uniqs[e.hh][t]=n})}for(var u in s.afterinsert){var c=s.afterinsert[u];c&&(c.funcid?A.fn[c.funcid](n):c.statement&&c.statement.execute(e))}A.inserted=a}}},o.delete=function(t){var n=this,r=n.data[t],a=!1;for(var s in n.beforedelete){var i=n.beforedelete[s];i&&(i.funcid?A.fn[i.funcid](r)===!1&&(a=a||!0):i.statement&&i.statement.execute(e)===!1&&(a=a||!0))}if(a)return!1;var o=!1;for(var s in n.insteadofdelete){o=!0;var i=n.insteadofdelete[s];i&&(i.funcid?A.fn[i.funcid](r):i.statement&&i.statement.execute(e))}if(!o){if(this.pk){var u=this.pk,c=u.onrightfn(r);if("undefined"==typeof this.uniqs[u.hh][c])throw new Error("Something wrong with primary key index on table");this.uniqs[u.hh][c]=void 0}n.uk&&n.uk.length&&n.uk.forEach(function(e){var t=e.onrightfn(r);if("undefined"==typeof n.uniqs[e.hh][t])throw new Error("Something wrong with unique index on table");n.uniqs[e.hh][t]=void 0});for(var s in n.afterdelete){var i=n.afterdelete[s];i&&(i.funcid?A.fn[i.funcid](r):i.statement&&i.statement.execute(e))}}},o.deleteall=function(){this.data.length=0,this.pk&&(this.uniqs[this.pk.hh]={}),o.uk&&o.uk.length&&o.uk.forEach(function(e){o.uniqs[e.hh]={}})},o.update=function(t,n,r){var a,s=P(this.data[n]);if(this.pk&&(a=this.pk,a.pkaddr=a.onrightfn(s,r),"undefined"==typeof this.uniqs[a.hh][a.pkaddr]))throw new Error("Something wrong with index on table");o.uk&&o.uk.length&&o.uk.forEach(function(e){if(e.ukaddr=e.onrightfn(s),"undefined"==typeof o.uniqs[e.hh][e.ukaddr])throw new Error("Something wrong with unique index on table")}),t(s,r,A);var i=!1;for(var u in o.beforeupdate){var c=o.beforeupdate[u];c&&(c.funcid?A.fn[c.funcid](this.data[n],s)===!1&&(i=i||!0):c.statement&&c.statement.execute(e)===!1&&(i=i||!0))}if(i)return!1;var l=!1;for(var u in o.insteadofupdate){l=!0;var c=o.insteadofupdate[u];c&&(c.funcid?A.fn[c.funcid](this.data[n],s):c.statement&&c.statement.execute(e))}if(!l){if(o.checks&&o.checks.length>0&&o.checks.forEach(function(e){if(!e.fn(s))throw new Error("Violation of CHECK constraint "+(e.id||""))}),o.columns.forEach(function(e){if(e.notnull&&"undefined"==typeof s[e.columnid])throw new Error("Wrong NULL value in NOT NULL column "+e.columnid)}),this.pk&&(a.newpkaddr=a.onrightfn(s),"undefined"!=typeof this.uniqs[a.hh][a.newpkaddr]&&a.newpkaddr!==a.pkaddr))throw new Error("Record already exists");o.uk&&o.uk.length&&o.uk.forEach(function(e){if(e.newukaddr=e.onrightfn(s),"undefined"!=typeof o.uniqs[e.hh][e.newukaddr]&&e.newukaddr!==e.ukaddr)throw new Error("Record already exists")}),this.pk&&(this.uniqs[a.hh][a.pkaddr]=void 0,this.uniqs[a.hh][a.newpkaddr]=s),o.uk&&o.uk.length&&o.uk.forEach(function(e){o.uniqs[e.hh][e.ukaddr]=void 0,o.uniqs[e.hh][e.newukaddr]=s}),this.data[n]=s;for(var u in o.afterupdate){var c=o.afterupdate[u];c&&(c.funcid?A.fn[c.funcid](this.data[n],s):c.statement&&c.statement.execute(e))}}};var h;return A.options.nocount||(h=1),n&&(h=n(h)),h},A.fn.Date=Object,A.fn.Date=Date,A.fn.Number=Number,A.fn.String=String,A.fn.Boolean=Boolean,oe.EXTEND=A.utils.extend,oe.CHAR=String.fromCharCode.bind(String),oe.ASCII=function(e){return e.charCodeAt(0)},oe.COALESCE=function(){for(var e=0;e<arguments.length;e++)if("undefined"!=typeof arguments[e]&&("number"!=typeof arguments[e]||!isNaN(arguments[e])))return arguments[e]},oe.USER=function(){return"alasql"},oe.OBJECT_ID=function(e){return!!A.tables[e]},oe.DATE=function(e){return/\d{8}/.test(e)?new Date((+e.substr(0,4)),+e.substr(4,2)-1,(+e.substr(6,2))):new Date(e)},oe.NOW=function(){var e=new Date,t=e.getFullYear()+"."+("0"+(e.getMonth()+1)).substr(-2)+"."+("0"+e.getDate()).substr(-2);return t+=" "+("0"+e.getHours()).substr(-2)+":"+("0"+e.getMinutes()).substr(-2)+":"+("0"+e.getSeconds()).substr(-2),t+="."+("00"+e.getMilliseconds()).substr(-3)},oe.GETDATE=oe.NOW,oe.CURRENT_TIMESTAMP=oe.NOW,oe.SECOND=function(e){var e=new Date(e);return e.getSeconds()},oe.MINUTE=function(e){var e=new Date(e);return e.getMinutes()},oe.HOUR=function(e){var e=new Date(e);return e.getHours()},oe.DAYOFWEEK=oe.WEEKDAY=function(e){var e=new Date(e);return e.getDay()},oe.DAY=oe.DAYOFMONTH=function(e){var e=new Date(e);return e.getDate()},oe.MONTH=function(e){var e=new Date(e);return e.getMonth()+1},oe.YEAR=function(e){var e=new Date(e);return e.getFullYear()};var he={year:31536e6,quarter:7884e6,month:2592e6,week:6048e5,day:864e5,dayofyear:864e5,weekday:864e5,hour:36e5,minute:6e4,second:1e3,millisecond:1,microsecond:.001};A.stdfn.DATEDIFF=function(e,t,n){var r=new Date(n).getTime()-new Date(t).getTime();return r/he[e.toLowerCase()]},A.stdfn.DATEADD=function(e,t,n){var r=new Date(n).getTime()+t*he[e.toLowerCase()];return new Date(r)},A.stdfn.INTERVAL=function(e,t){return e*he[t.toLowerCase()]},A.stdfn.DATE_ADD=A.stdfn.ADDDATE=function(e,t){var n=new Date(e).getTime()+t;return new Date(n)},A.stdfn.DATE_SUB=A.stdfn.SUBDATE=function(e,t){var n=new Date(e).getTime()-t;return new Date(n)},z.DropTable=function(e){return z.extend(this,e)},z.DropTable.prototype.toString=function(){var e="DROP ";return e+=this.view?"VIEW":"TABLE",this.ifexists&&(e+=" IF EXISTS"),e+=" "+this.tables.toString()},z.DropTable.prototype.execute=function(e,t,n){var r=this.ifexists,a=0,s=0,i=this.tables.length;return this.tables.forEach(function(t){var o=A.databases[t.databaseid||e],u=t.tableid;if(!r||r&&o.tables[u]){if(o.tables[u])o.engineid?A.engines[o.engineid].dropTable(t.databaseid||e,u,r,function(e){delete o.tables[u],a+=e,s++,s==i&&n&&n(a)}):(delete o.tables[u],a++,s++,s==i&&n&&n(a));else if(!A.options.dropifnotexists)throw new Error("Can not drop table '"+t.tableid+"', because it does not exist in the database.")}else s++,s==i&&n&&n(a)}),a},z.TruncateTable=function(e){return z.extend(this,e)},z.TruncateTable.prototype.toString=function(){var e="TRUNCATE TABLE";return e+=" "+this.table.toString()},z.TruncateTable.prototype.execute=function(e,t,n){var r=A.databases[this.table.databaseid||e],a=this.table.tableid;if(r.engineid)return A.engines[r.engineid].truncateTable(this.table.databaseid||e,a,this.ifexists,n);if(!r.tables[a])throw new Error("Cannot truncate table becaues it does not exist");return r.tables[a].data=[],n?n(0):0},z.CreateVertex=function(e){return z.extend(this,e)},z.CreateVertex.prototype.toString=function(){var e="CREATE VERTEX ";return this.class&&(e+=this.class+" "),this.sharp&&(e+="#"+this.sharp+" "),this.sets?e+=this.sets.toString():this.content?e+=this.content.toString():this.select&&(e+=this.select.toString()),e},z.CreateVertex.prototype.toJS=function(e){var t="this.queriesfn["+(this.queriesidx-1)+"](this.params,null,"+e+")";return t},z.CreateVertex.prototype.compile=function(e){var t=e,n=this.sharp;if("undefined"!=typeof this.name)var r="x.name="+this.name.toJS(),a=new Function("x",r);if(this.sets&&this.sets.length>0)var r=this.sets.map(function(e){return"x['"+e.column.columnid+"']="+e.expression.toJS("x","")}).join(";"),s=new Function("x,params,alasql",r);var i=function(e,r){var i,o,u=A.databases[t];o="undefined"!=typeof n?n:u.counter++;var c={$id:o,$node:"VERTEX"};return u.objects[c.$id]=c,i=c,a&&a(c),s&&s(c,e,A),r&&(i=r(i)),i};return i},z.CreateEdge=function(e){return z.extend(this,e)},z.CreateEdge.prototype.toString=function(){var e="CREATE EDGE ";return this.class&&(e+=this.class+" "),e},z.CreateEdge.prototype.toJS=function(e){var t="this.queriesfn["+(this.queriesidx-1)+"](this.params,null,"+e+")";return t},z.CreateEdge.prototype.compile=function(e){var t=e,n=new Function("params,alasql","var y;return "+this.from.toJS()),r=new Function("params,alasql","var y;return "+this.to.toJS());if("undefined"!=typeof this.name)var a="x.name="+this.name.toJS(),s=new Function("x",a);if(this.sets&&this.sets.length>0)var a=this.sets.map(function(e){return"x['"+e.column.columnid+"']="+e.expression.toJS("x","")}).join(";"),i=new Function("x,params,alasql","var y;"+a);var o=function(e,a){var o=0,u=A.databases[t],c={$id:u.counter++,$node:"EDGE"},l=n(e,A),h=r(e,A);return c.$in=[l.$id],c.$out=[h.$id],void 0===l.$out&&(l.$out=[]),l.$out.push(c.$id),void 0===typeof h.$in&&(h.$in=[]),h.$in.push(c.$id),u.objects[c.$id]=c,o=c,s&&s(c),i&&i(c,e,A),a&&(o=a(o)),o};return o},z.CreateGraph=function(e){return z.extend(this,e)},z.CreateGraph.prototype.toString=function(){var e="CREATE GRAPH ";return this.class&&(e+=this.class+" "),e},z.CreateGraph.prototype.execute=function(e,t,n){function r(e){var t=A.databases[A.useid].objects;for(var n in t)if(t[n].name===e)return t[n]}function a(n){var r={};"undefined"!=typeof n.as&&(A.vars[n.as]=r),"undefined"!=typeof n.prop&&(r.$id=n.prop,r.name=n.prop),"undefined"!=typeof n.sharp&&(r.$id=n.sharp),"undefined"!=typeof n.name&&(r.name=n.name),"undefined"!=typeof n.class&&(r.$class=n.class);var a=A.databases[e];if("undefined"==typeof r.$id&&(r.$id=a.counter++),r.$node="VERTEX","undefined"!=typeof n.json&&V(r,new Function("params,alasql","var y;return "+n.json.toJS())(t,A)),a.objects[r.$id]=r,"undefined"!=typeof r.$class){if("undefined"==typeof A.databases[e].tables[r.$class])throw new Error("No such class. Pleace use CREATE CLASS");A.databases[e].tables[r.$class].data.push(r)}return s.push(r.$id),r}var s=[];return this.from&&A.from[this.from.funcid]&&(this.graph=A.from[this.from.funcid.toUpperCase()]),this.graph.forEach(function(n){if(n.source){var i={};"undefined"!=typeof n.as&&(A.vars[n.as]=i),"undefined"!=typeof n.prop&&(i.name=n.prop),"undefined"!=typeof n.sharp&&(i.$id=n.sharp),"undefined"!=typeof n.name&&(i.name=n.name),"undefined"!=typeof n.class&&(i.$class=n.class);var o=A.databases[e];"undefined"==typeof i.$id&&(i.$id=o.counter++),i.$node="EDGE","undefined"!=typeof n.json&&V(i,new Function("params,alasql","var y;return "+n.json.toJS())(t,A));var u;if(n.source.vars){var c=A.vars[n.source.vars];u="object"==typeof c?c:o.objects[c]}else{var l=n.source.sharp;"undefined"==typeof l&&(l=n.source.prop),u=A.databases[e].objects[l],"undefined"!=typeof u||!A.options.autovertex||"undefined"==typeof n.source.prop&&"undefined"==typeof n.source.name||(u=r(n.source.prop||n.source.name),"undefined"==typeof u&&(u=a(n.source)))}var h;if(n.source.vars){var c=A.vars[n.target.vars];h="object"==typeof c?c:o.objects[c]}else{var d=n.target.sharp;"undefined"==typeof d&&(d=n.target.prop),h=A.databases[e].objects[d],"undefined"!=typeof h||!A.options.autovertex||"undefined"==typeof n.target.prop&&"undefined"==typeof n.target.name||(h=r(n.target.prop||n.target.name),"undefined"==typeof h&&(h=a(n.target)))}if(i.$in=[u.$id],i.$out=[h.$id],"undefined"==typeof u.$out&&(u.$out=[]),u.$out.push(i.$id),"undefined"==typeof h.$in&&(h.$in=[]),h.$in.push(i.$id),o.objects[i.$id]=i,"undefined"!=typeof i.$class){if("undefined"==typeof A.databases[e].tables[i.$class])throw new Error("No such class. Pleace use CREATE CLASS");A.databases[e].tables[i.$class].data.push(i)}s.push(i.$id)}else a(n)}),n&&(s=n(s)),s},z.CreateGraph.prototype.compile1=function(e){var t=e,n=new Function("params,alasql","var y;return "+this.from.toJS()),r=new Function("params,alasql","var y;return "+this.to.toJS());if("undefined"!=typeof this.name)var a="x.name="+this.name.toJS(),s=new Function("x",a);if(this.sets&&this.sets.length>0)var a=this.sets.map(function(e){return"x['"+e.column.columnid+"']="+e.expression.toJS("x","")}).join(";"),i=new Function("x,params,alasql","var y;"+a);var o=function(e,a){var o=0,u=A.databases[t],c={$id:u.counter++,$node:"EDGE"},l=n(e,A),h=r(e,A);return c.$in=[l.$id],c.$out=[h.$id],"undefined"==typeof l.$out&&(l.$out=[]),l.$out.push(c.$id),"undefined"==typeof h.$in&&(h.$in=[]),h.$in.push(c.$id),u.objects[c.$id]=c,o=c,s&&s(c),i&&i(c,e,A),a&&(o=a(o)),o};return o},z.AlterTable=function(e){return z.extend(this,e)},z.AlterTable.prototype.toString=function(){var e="ALTER TABLE "+this.table.toString();return this.renameto&&(e+=" RENAME TO "+this.renameto),e},z.AlterTable.prototype.execute=function(e,t,n){var r=A.databases[e];if(r.dbversion=Date.now(),this.renameto){var a=this.table.tableid,s=this.renameto,i=1;if(r.tables[s])throw new Error("Can not rename a table '"+a+"' to '"+s+"', because the table with this name already exists");if(s==a)throw new Error("Can not rename a table '"+a+"' to itself");return r.tables[s]=r.tables[a],delete r.tables[a],i=1,n&&n(i),i}if(this.addcolumn){var r=A.databases[this.table.databaseid||e];r.dbversion++;var o=this.table.tableid,u=r.tables[o],c=this.addcolumn.columnid;if(u.xcolumns[c])throw new Error('Cannot add column "'+c+'", because it already exists in the table "'+o+'"');var l={columnid:c,dbtypeid:this.dbtypeid,dbsize:this.dbsize,dbprecision:this.dbprecision,dbenum:this.dbenum,defaultfns:null},h=function(){};u.columns.push(l),u.xcolumns[c]=l;for(var d=0,f=u.data.length;d<f;d++)u.data[d][c]=h();return n?n(1):1}if(this.modifycolumn){var r=A.databases[this.table.databaseid||e];r.dbversion++;var o=this.table.tableid,u=r.tables[o],c=this.modifycolumn.columnid;if(!u.xcolumns[c])throw new Error('Cannot modify column "'+c+'", because it was not found in the table "'+o+'"');var l=u.xcolumns[c];return l.dbtypeid=this.dbtypeid,l.dbsize=this.dbsize,l.dbprecision=this.dbprecision,l.dbenum=this.dbenum,n?n(1):1}if(this.renamecolumn){var r=A.databases[this.table.databaseid||e];r.dbversion++;var l,o=this.table.tableid,u=r.tables[o],c=this.renamecolumn,p=this.to;if(!u.xcolumns[c])throw new Error('Column "'+c+'" is not found in the table "'+o+'"');if(u.xcolumns[p])throw new Error('Column "'+p+'" already exists in the table "'+o+'"');if(c!=p){for(var b=0;b<u.columns.length;b++)u.columns[b].columnid==c&&(u.columns[b].columnid=p);u.xcolumns[p]=u.xcolumns[c],delete u.xcolumns[c];for(var d=0,f=u.data.length;d<f;d++)u.data[d][p]=u.data[d][c],delete u.data[d][c];return u.data.length}return n?n(0):0}if(this.dropcolumn){var r=A.databases[this.table.databaseid||e];r.dbversion++;for(var o=this.table.tableid,u=r.tables[o],c=this.dropcolumn,E=!1,b=0;b<u.columns.length;b++)if(u.columns[b].columnid==c){E=!0,u.columns.splice(b,1);break}if(!E)throw new Error('Cannot drop column "'+c+'", because it was not found in the table "'+o+'"');delete u.xcolumns[c];for(var d=0,f=u.data.length;d<f;d++)delete u.data[d][c];return n?n(u.data.length):u.data.length}throw Error("Unknown ALTER TABLE method")},z.CreateIndex=function(e){return z.extend(this,e)},z.CreateIndex.prototype.toString=function(){var e="CREATE";return this.unique&&(e+=" UNIQUE"),e+=" INDEX "+this.indexid+" ON "+this.table.toString(),e+="("+this.columns.toString()+")"},z.CreateIndex.prototype.execute=function(e,t,n){var r=A.databases[e],a=this.table.tableid,s=r.tables[a],i=this.indexid;r.indices[i]=a;var o=this.columns.map(function(e){return e.expression.toJS("r","")}).join("+'`'+"),u=new Function("r,params,alasql","return "+o);if(this.unique){s.uniqdefs[i]={rightfns:o};var c=s.uniqs[i]={};if(s.data.length>0)for(var l=0,h=s.data.length;l<h;l++){var d=o(s.data[l]);c[d]||(c[d]={num:0}),c[d].num++}}else{var f=L(o);s.inddefs[i]={rightfns:o,hh:f},s.indices[f]={};var p=s.indices[f]={};if(s.data.length>0)for(var l=0,h=s.data.length;l<h;l++){var d=u(s.data[l],t,A);p[d]||(p[d]=[]),p[d].push(s.data[l])}}var b=1;return n&&(b=n(b)),b},z.Reindex=function(e){return z.extend(this,e)},z.Reindex.prototype.toString=function(){var e="REINDEX "+this.indexid;return e},z.Reindex.prototype.execute=function(e,t,n){var r=A.databases[e],a=this.indexid,s=r.indices[a],i=r.tables[s];i.indexColumns();var o=1;return n&&(o=n(o)),o},z.DropIndex=function(e){return z.extend(this,e)},z.DropIndex.prototype.toString=function(){return"DROP INDEX"+this.indexid},z.DropIndex.prototype.compile=function(e){this.indexid;return function(){return 1}},z.WithSelect=function(e){return z.extend(this,e)},z.WithSelect.prototype.toString=function(){var e="WITH ";return e+=this.withs.map(function(e){return e.name+" AS ("+e.select.toString()+")"}).join(",")+" ",e+=this.select.toString()},z.WithSelect.prototype.execute=function(e,t,n){var r=this,a=[];r.withs.forEach(function(n){a.push(A.databases[e].tables[n.name]);var r=A.databases[e].tables[n.name]=new K({tableid:n.name});r.data=n.select.execute(e,t)});var s=1;return s=this.select.execute(e,t,function(t){return r.withs.forEach(function(t,n){a[n]?A.databases[e].tables[t.name]=a[n]:delete A.databases[e].tables[t.name]}),n&&(t=n(t)),t})},z.If=function(e){return z.extend(this,e)},z.If.prototype.toString=function(){var e="IF ";return e+=this.expression.toString(),e+=" "+this.thenstat.toString(),this.elsestat&&(e+=" ELSE "+this.thenstat.toString()),e},z.If.prototype.execute=function(e,t,n){var r,a=new Function("params,alasql,p","var y;return "+this.expression.toJS("({})","",null)).bind(this);return a(t,A)?r=this.thenstat.execute(e,t,n):this.elsestat?r=this.elsestat.execute(e,t,n):n&&(r=n(r)),r},z.While=function(e){return z.extend(this,e)},z.While.prototype.toString=function(){var e="WHILE ";return e+=this.expression.toString(),e+=" "+this.loopstat.toString()},z.While.prototype.execute=function(e,t,n){function r(u){o?s.push(u):o=!0,setTimeout(function(){i(t,A)?a.loopstat.execute(e,t,r):s=n(s)},0)}var a=this,s=[],i=new Function("params,alasql,p","var y;return "+this.expression.toJS());if(n){var o=!1;r()}else for(;i(t,A);){var u=a.loopstat.execute(e,t);s.push(u)}return s},z.Break=function(e){return z.extend(this,e)},z.Break.prototype.toString=function(){var e="BREAK";return e},z.Break.prototype.execute=function(e,t,n,r){var a=1;return n&&(a=n(a)),a},z.Continue=function(e){return z.extend(this,e)},z.Continue.prototype.toString=function(){var e="CONTINUE";return e},z.Continue.prototype.execute=function(e,t,n,r){var a=1;return n&&(a=n(a)),a},z.BeginEnd=function(e){return z.extend(this,e)},z.BeginEnd.prototype.toString=function(){var e="BEGIN "+this.statements.toString()+" END";return e},z.BeginEnd.prototype.execute=function(e,t,n,r){function a(){s.statements[o].execute(e,t,function(e){return i.push(e),o++,o<s.statements.length?a():void(n&&(i=n(i)))})}var s=this,i=[],o=0;return a(),i},z.Insert=function(e){return z.extend(this,e)},z.Insert.prototype.toString=function(){var e="INSERT ";return this.orreplace&&(e+="OR REPLACE "),this.replaceonly&&(e="REPLACE "),e+="INTO "+this.into.toString(),this.columns&&(e+="("+this.columns.toString()+")"),this.values&&(e+=" VALUES "+this.values.toString()),this.select&&(e+=" "+this.select.toString()),e},z.Insert.prototype.toJS=function(e,t,n){var r="this.queriesfn["+(this.queriesidx-1)+"](this.params,null,"+e+")";return r},z.Insert.prototype.compile=function(e){var t=this;e=t.into.databaseid||e;var n=A.databases[e],r=t.into.tableid,a=n.tables[r];if(!a)throw"Table '"+r+"' could not be found";var s,i="",o="",i="db.tables['"+r+"'].dirty=true;",u="var a,aa=[],x;";if(this.values){this.exists&&(this.existsfn=this.exists.map(function(t){var n=t.compile(e);return n.query.modifier="RECORDSET",n})),this.queries&&(this.queriesfn=this.queries.map(function(t){var n=t.compile(e);return n.query.modifier="RECORDSET",n})),t.values.forEach(function(s){var u=[];t.columns?t.columns.forEach(function(e,t){var n="'"+e.columnid+"':";a.xcolumns&&a.xcolumns[e.columnid]?["INT","FLOAT","NUMBER","MONEY"].indexOf(a.xcolumns[e.columnid].dbtypeid)>=0?n+="(x="+s[t].toJS()+",x==undefined?undefined:+x)":A.fn[a.xcolumns[e.columnid].dbtypeid]?(n+="(new "+a.xcolumns[e.columnid].dbtypeid+"(",n+=s[t].toJS(),n+="))"):n+=s[t].toJS():n+=s[t].toJS(),u.push(n)}):s instanceof Array&&a.columns&&a.columns.length>0?a.columns.forEach(function(e,t){var n="'"+e.columnid+"':";["INT","FLOAT","NUMBER","MONEY"].indexOf(e.dbtypeid)>=0?n+="+"+s[t].toJS():A.fn[e.dbtypeid]?(n+="(new "+e.dbtypeid+"(",n+=s[t].toJS(),n+="))"):n+=s[t].toJS(),u.push(n)}):o=E(s),n.tables[r].defaultfns&&u.unshift(n.tables[r].defaultfns),i+=o?"a="+o+";":"a={"+u.join(",")+"};",n.tables[r].isclass&&(i+="var db=alasql.databases['"+e+"'];",i+='a.$class="'+r+'";',i+="a.$id=db.counter++;",i+="db.objects[a.$id]=a;"),n.tables[r].insert?(i+="var db=alasql.databases['"+e+"'];",i+="db.tables['"+r+"'].insert(a,"+(t.orreplace?"true":"false")+");"):i+="aa.push(a);"}),s=u+i,n.tables[r].insert||(i+="alasql.databases['"+e+"'].tables['"+r+"'].data=alasql.databases['"+e+"'].tables['"+r+"'].data.concat(aa);"),i+=n.tables[r].insert&&n.tables[r].isclass?"return a.$id;":"return "+t.values.length;var c=new Function("db, params, alasql","var y;"+u+i).bind(this)}else if(this.select){if(this.select.modifier="RECORDSET",selectfn=this.select.compile(e),n.engineid&&A.engines[n.engineid].intoTable){var l=function(e,t){var a=selectfn(e),s=A.engines[n.engineid].intoTable(n.databaseid,r,a.data,null,t);return s};return l}var h="return alasql.utils.extend(r,{"+a.defaultfns+"})",d=new Function("r,db,params,alasql",h),c=function(e,n,a){var s=selectfn(n).data;if(e.tables[r].insert)for(var i=0,o=s.length;i<o;i++){var u=P(s[i]);d(u,e,n,a),e.tables[r].insert(u,t.orreplace)}else e.tables[r].data=e.tables[r].data.concat(s);return a.options.nocount?void 0:s.length}}else{if(!this.default)throw new Error("Wrong INSERT parameters");var f="db.tables['"+r+"'].data.push({"+a.defaultfns+"});return 1;",c=new Function("db,params,alasql",f)}if(n.engineid&&A.engines[n.engineid].intoTable&&A.options.autocommit)var l=function(e,t){var a=new Function("db,params","var y;"+s+"return aa;")(n,e),i=A.engines[n.engineid].intoTable(n.databaseid,r,a,null,t);return i};else var l=function(t,n){var a=A.databases[e];A.options.autocommit&&a.engineid&&A.engines[a.engineid].loadTableData(e,r);var s=c(a,t,A);return A.options.autocommit&&a.engineid&&A.engines[a.engineid].saveTableData(e,r),A.options.nocount&&(s=void 0),n&&n(s),s};return l},z.Insert.prototype.execute=function(e,t,n){return this.compile(e)(t,n)},z.CreateTrigger=function(e){return z.extend(this,e)},z.CreateTrigger.prototype.toString=function(){var e="CREATE TRIGGER "+this.trigger+" ";return this.when&&(e+=this.when+" "),e+=this.action+" ON ",this.table.databaseid&&(e+=this.table.databaseid+"."),e+=this.table.tableid+" ",e+=this.statement.toString()},z.CreateTrigger.prototype.execute=function(e,t,n){var r=1,a=this.trigger;e=this.table.databaseid||e;var s=A.databases[e],i=this.table.tableid,o={action:this.action,when:this.when,statement:this.statement,funcid:this.funcid};return s.triggers[a]=o,"INSERT"==o.action&&"BEFORE"==o.when?s.tables[i].beforeinsert[a]=o:"INSERT"==o.action&&"AFTER"==o.when?s.tables[i].afterinsert[a]=o:"INSERT"==o.action&&"INSTEADOF"==o.when?s.tables[i].insteadofinsert[a]=o:"DELETE"==o.action&&"BEFORE"==o.when?s.tables[i].beforedelete[a]=o:"DELETE"==o.action&&"AFTER"==o.when?s.tables[i].afterdelete[a]=o:"DELETE"==o.action&&"INSTEADOF"==o.when?s.tables[i].insteadofdelete[a]=o:"UPDATE"==o.action&&"BEFORE"==o.when?s.tables[i].beforeupdate[a]=o:"UPDATE"==o.action&&"AFTER"==o.when?s.tables[i].afterupdate[a]=o:"UPDATE"==o.action&&"INSTEADOF"==o.when&&(s.tables[i].insteadofupdate[a]=o),n&&(r=n(r)),r},z.DropTrigger=function(e){return z.extend(this,e)},z.DropTrigger.prototype.toString=function(){var e="DROP TRIGGER "+this.trigger;return e},z.DropTrigger.prototype.execute=function(e,t,n){var r=0,a=A.databases[e],s=this.trigger,i=a.triggers[s];if(!i)throw new Error("Trigger not found");return r=1,delete a.tables[i].beforeinsert[s],delete a.tables[i].afterinsert[s],delete a.tables[i].insteadofinsert[s],delete a.tables[i].beforedelte[s],delete a.tables[i].afterdelete[s],delete a.tables[i].insteadofdelete[s],delete a.tables[i].beforeupdate[s],delete a.tables[i].afterupdate[s],delete a.tables[i].insteadofupdate[s],delete a.triggers[s],n&&(r=n(r)),r},z.Delete=function(e){return z.extend(this,e)},z.Delete.prototype.toString=function(){var e="DELETE FROM "+this.table.toString();return this.where&&(e+=" WHERE "+this.where.toString()),e},z.Delete.prototype.compile=function(e){e=this.table.databaseid||e;var t,n=this.table.tableid,r=A.databases[e];if(this.where){this.exists&&(this.existsfn=this.exists.map(function(t){var n=t.compile(e);return n.query.modifier="RECORDSET",n})),this.queries&&(this.queriesfn=this.queries.map(function(t){var n=t.compile(e);return n.query.modifier="RECORDSET",n}));var a=new Function("r,params,alasql","var y;return ("+this.where.toJS("r","")+")").bind(this);t=function(t,s){if(r.engineid&&A.engines[r.engineid].deleteFromTable)return A.engines[r.engineid].deleteFromTable(e,n,a,t,s);A.options.autocommit&&r.engineid&&"LOCALSTORAGE"==r.engineid&&A.engines[r.engineid].loadTableData(e,n);for(var i=r.tables[n],o=i.data.length,u=[],c=0,l=i.data.length;c<l;c++)a(i.data[c],t,A)?i.delete&&i.delete(c,t,A):u.push(i.data[c]);i.data=u;var h=o-i.data.length;return A.options.autocommit&&r.engineid&&"LOCALSTORAGE"==r.engineid&&A.engines[r.engineid].saveTableData(e,n),
s&&s(h),h}}else t=function(t,a){A.options.autocommit&&r.engineid&&A.engines[r.engineid].loadTableData(e,n);var s=r.tables[n];s.dirty=!0;var i=r.tables[n].data.length;r.tables[n].data.length=0;for(var o in r.tables[n].uniqs)r.tables[n].uniqs[o]={};for(var o in r.tables[n].indices)r.tables[n].indices[o]={};return A.options.autocommit&&r.engineid&&A.engines[r.engineid].saveTableData(e,n),a&&a(i),i};return t},z.Delete.prototype.execute=function(e,t,n){return this.compile(e)(t,n)},z.Update=function(e){return z.extend(this,e)},z.Update.prototype.toString=function(){var e="UPDATE "+this.table.toString();return this.columns&&(e+=" SET "+this.columns.toString()),this.where&&(e+=" WHERE "+this.where.toString()),e},z.SetColumn=function(e){return z.extend(this,e)},z.SetColumn.prototype.toString=function(){return this.column.toString()+"="+this.expression.toString()},z.Update.prototype.compile=function(e){e=this.table.databaseid||e;var t=this.table.tableid;if(this.where){this.exists&&(this.existsfn=this.exists.map(function(t){var n=t.compile(e);return n.query.modifier="RECORDSET",n})),this.queries&&(this.queriesfn=this.queries.map(function(t){var n=t.compile(e);return n.query.modifier="RECORDSET",n}));var n=new Function("r,params,alasql","var y;return "+this.where.toJS("r","")).bind(this)}var r=A.databases[e].tables[t].onupdatefns||"";r+=";",this.columns.forEach(function(e){r+="r['"+e.column.columnid+"']="+e.expression.toJS("r","")+";"});var a=new Function("r,params,alasql","var y;"+r),s=function(r,s){var i=A.databases[e];if(i.engineid&&A.engines[i.engineid].updateTable)return A.engines[i.engineid].updateTable(e,t,a,n,r,s);A.options.autocommit&&i.engineid&&A.engines[i.engineid].loadTableData(e,t);var o=i.tables[t];if(!o)throw new Error("Table '"+t+"' not exists");for(var u=0,c=0,l=o.data.length;c<l;c++)n&&!n(o.data[c],r,A)||(o.update?o.update(a,c,r):a(o.data[c],r,A),u++);return A.options.autocommit&&i.engineid&&A.engines[i.engineid].saveTableData(e,t),s&&s(u),u};return s},z.Update.prototype.execute=function(e,t,n){return this.compile(e)(t,n)},z.Merge=function(e){return z.extend(this,e)},z.Merge.prototype.toString=function(){var e="MERGE ";return e+=this.into.tableid+" ",this.into.as&&(e+="AS "+this.into.as+" "),e+="USING "+this.using.tableid+" ",this.using.as&&(e+="AS "+this.using.as+" "),e+="ON "+this.on.toString()+" ",this.matches.forEach(function(t){e+="WHEN ",t.matched||(e+="NOT "),e+="MATCHED ",t.bytarget&&(e+="BY TARGET "),t.bysource&&(e+="BY SOURCE "),t.expr&&(e+="AND "+t.expr.toString()+" "),e+="THEN ",t.action.delete&&(e+="DELETE "),t.action.insert&&(e+="INSERT ",t.action.columns&&(e+="("+t.action.columns.toString()+") "),t.action.values&&(e+="VALUES ("+t.action.values.toString()+") "),t.action.defaultvalues&&(e+="DEFAULT VALUES ")),t.action.update&&(e+="UPDATE ",e+=t.action.update.map(function(e){return e.toString()}).join(",")+" ")}),e},z.Merge.prototype.execute=function(e,t,n){var r=1;return n&&(r=n(r)),r},z.CreateDatabase=function(e){return z.extend(this,e)},z.CreateDatabase.prototype.toString=function(){var e="CREATE";return this.engineid&&(e+=" "+this.engineid),e+=" DATABASE",this.ifnotexists&&(e+=" IF NOT EXISTS"),e+=" "+this.databaseid,this.args&&this.args.length>0&&(e+="("+this.args.map(function(e){return e.toString()}).join(", ")+")"),this.as&&(e+=" AS "+this.as),e},z.CreateDatabase.prototype.execute=function(e,t,n){var r;if(this.args&&this.args.length>0&&(r=this.args.map(function(e){return new Function("params,alasql","var y;return "+e.toJS())(t,A)})),this.engineid){var a=A.engines[this.engineid].createDatabase(this.databaseid,this.args,this.ifnotexists,this.as,n);return a}var s=this.databaseid;if(A.databases[s])throw new Error("Database '"+s+"' already exists");var a=(new A.Database(s),1);return n?n(a):a},z.AttachDatabase=function(e){return z.extend(this,e)},z.AttachDatabase.prototype.toString=function(){var e="ATTACH";return this.engineid&&(e+=" "+this.engineid),e+=" DATABASE "+this.databaseid,args&&(e+="(",args.length>0&&(e+=args.map(function(e){return e.toString()}).join(", ")),e+=")"),this.as&&(e+=" AS "+this.as),e},z.AttachDatabase.prototype.execute=function(e,t,n){if(!A.engines[this.engineid])throw new Error('Engine "'+this.engineid+'" is not defined.');var r=A.engines[this.engineid].attachDatabase(this.databaseid,this.as,this.args,t,n);return r},z.DetachDatabase=function(e){return z.extend(this,e)},z.DetachDatabase.prototype.toString=function(){var e="DETACH";return e+=" DATABASE "+this.databaseid},z.DetachDatabase.prototype.execute=function(e,t,n){if(!A.databases[this.databaseid].engineid)throw new Error('Cannot detach database "'+this.engineid+'", because it was not attached.');var r,a=this.databaseid;if(a==A.DEFAULTDATABASEID)throw new Error("Drop of default database is prohibited");if(A.databases[a])delete A.databases[a],a==A.useid&&A.use(),r=1;else{if(!this.ifexists)throw new Error("Database '"+a+"' does not exist");r=0}return n&&n(r),r},z.UseDatabase=function(e){return z.extend(this,e)},z.UseDatabase.prototype.toString=function(){return"USE DATABASE "+this.databaseid},z.UseDatabase.prototype.execute=function(e,t,n){var r=this.databaseid;if(!A.databases[r])throw new Error("Database '"+r+"' does not exist");A.use(r);var a=1;return n&&n(a),a},z.DropDatabase=function(e){return z.extend(this,e)},z.DropDatabase.prototype.toString=function(){var e="DROP";return this.ifexists&&(e+=" IF EXISTS"),e+=" DATABASE "+this.databaseid},z.DropDatabase.prototype.execute=function(e,t,n){if(this.engineid)return A.engines[this.engineid].dropDatabase(this.databaseid,this.ifexists,n);var r,a=this.databaseid;if(a==A.DEFAULTDATABASEID)throw new Error("Drop of default database is prohibited");if(A.databases[a]){if(A.databases[a].engineid)throw new Error("Cannot drop database '"+a+"', because it is attached. Detach it.");delete A.databases[a],a==A.useid&&A.use(),r=1}else{if(!this.ifexists)throw new Error("Database '"+a+"' does not exist");r=0}return n&&n(r),r},z.Declare=function(e){return z.extend(this,e)},z.Declare.prototype.toString=function(){var e="DECLARE ";return this.declares&&this.declares.length>0&&(e=this.declares.map(function(e){var t="";return t+="@"+e.variable+" ",t+=e.dbtypeid,this.dbsize&&(t+="("+this.dbsize,this.dbprecision&&(t+=","+this.dbprecision),t+=")"),e.expression&&(t+=" = "+e.expression.toString()),t}).join(",")),e},z.Declare.prototype.execute=function(e,t,n){var r=1;return this.declares&&this.declares.length>0&&this.declares.map(function(e){var n=e.dbtypeid;A.fn[n]||(n=n.toUpperCase()),A.declares[e.variable]={dbtypeid:n,dbsize:e.dbsize,dbprecision:e.dbprecision},e.expression&&(A.vars[e.variable]=new Function("params,alasql","return "+e.expression.toJS("({})","",null))(t,A),A.declares[e.variable]&&(A.vars[e.variable]=A.stdfn.CONVERT(A.vars[e.variable],A.declares[e.variable])))}),n&&(r=n(r)),r},z.ShowDatabases=function(e){return z.extend(this,e)},z.ShowDatabases.prototype.toString=function(){var e="SHOW DATABASES";return this.like&&(e+="LIKE "+this.like.toString()),e},z.ShowDatabases.prototype.execute=function(e,t,n){if(this.engineid)return A.engines[this.engineid].showDatabases(this.like,n);var r=this,a=[];for(dbid in A.databases)a.push({databaseid:dbid});return r.like&&a&&a.length>0&&(a=a.filter(function(e){return A.utils.like(r.like.value,e.databaseid)})),n&&n(a),a},z.ShowTables=function(e){return z.extend(this,e)},z.ShowTables.prototype.toString=function(){var e="SHOW TABLES";return this.databaseid&&(e+=" FROM "+this.databaseid),this.like&&(e+=" LIKE "+this.like.toString()),e},z.ShowTables.prototype.execute=function(e,t,n){var r=A.databases[this.databaseid||e],a=this,s=[];for(tableid in r.tables)s.push({tableid:tableid});return a.like&&s&&s.length>0&&(s=s.filter(function(e){return A.utils.like(a.like.value,e.tableid)})),n&&n(s),s},z.ShowColumns=function(e){return z.extend(this,e)},z.ShowColumns.prototype.toString=function(){var e="SHOW COLUMNS";return this.table.tableid&&(e+=" FROM "+this.table.tableid),this.databaseid&&(e+=" FROM "+this.databaseid),e},z.ShowColumns.prototype.execute=function(e){var t=A.databases[this.databaseid||e],n=t.tables[this.table.tableid];if(n&&n.columns){var r=n.columns.map(function(e){return{columnid:e.columnid,dbtypeid:e.dbtypeid,dbsize:e.dbsize}});return r}return[]},z.ShowIndex=function(e){return z.extend(this,e)},z.ShowIndex.prototype.toString=function(){var e="SHOW INDEX";return this.table.tableid&&(e+=" FROM "+this.table.tableid),this.databaseid&&(e+=" FROM "+this.databaseid),e},z.ShowIndex.prototype.execute=function(e){var t=A.databases[this.databaseid||e],n=t.tables[this.table.tableid],r=[];if(n&&n.indices)for(var a in n.indices)r.push({hh:a,len:Object.keys(n.indices[a]).length});return r},z.ShowCreateTable=function(e){return z.extend(this,e)},z.ShowCreateTable.prototype.toString=function(){var e="SHOW CREATE TABLE "+this.table.tableid;return this.databaseid&&(e+=" FROM "+this.databaseid),e},z.ShowCreateTable.prototype.execute=function(e){var t=A.databases[this.databaseid||e],n=t.tables[this.table.tableid];if(n){var r="CREATE TABLE "+this.table.tableid+" (",a=[];return n.columns&&(n.columns.forEach(function(e){var t=e.columnid+" "+e.dbtypeid;e.dbsize&&(t+="("+e.dbsize+")"),e.primarykey&&(t+=" PRIMARY KEY"),a.push(t)}),r+=a.join(", ")),r+=")"}throw new Error('There is no such table "'+this.table.tableid+'"')},z.SetVariable=function(e){return z.extend(this,e)},z.SetVariable.prototype.toString=function(){var e="SET ";return"undefined"!=typeof this.value&&(e+=this.variable.toUpperCase()+" "+(this.value?"ON":"OFF")),this.expression&&(e+=this.method+this.variable+" = "+this.expression.toString()),e},z.SetVariable.prototype.execute=function(e,t,n){if("undefined"!=typeof this.value){var r=this.value;"ON"==r?r=!0:"OFF"==r&&(r=!1),A.options[this.variable]=r}else if(this.expression){this.exists&&(this.existsfn=this.exists.map(function(t){var n=t.compile(e);return n.query&&!n.query.modifier&&(n.query.modifier="RECORDSET"),n})),this.queries&&(this.queriesfn=this.queries.map(function(t){var n=t.compile(e);return n.query&&!n.query.modifier&&(n.query.modifier="RECORDSET"),n}));var a=new Function("params,alasql","return "+this.expression.toJS("({})","",null)).bind(this)(t,A);if(A.declares[this.variable]&&(a=A.stdfn.CONVERT(a,A.declares[this.variable])),this.props&&this.props.length>0){if("@"==this.method)var s="alasql.vars['"+this.variable+"']";else var s="params['"+this.variable+"']";s+=this.props.map(function(e){return"string"==typeof e?"['"+e+"']":"number"==typeof e?"["+e+"]":"["+e.toJS()+"]"}).join(),new Function("value,params,alasql","var y;"+s+"=value")(a,t,A)}else"@"==this.method?A.vars[this.variable]=a:t[this.variable]=a}var a=1;return n&&(a=n(a)),a},A.test=function(e,t,n){if(0===arguments.length)return void A.log(A.con.results);if(1===arguments.length){var r=Date.now();return n(),void A.con.log(Date.now()-r)}2===arguments.length&&(n=t,t=1);for(var r=Date.now(),a=0;a<t;a++)n();A.con.results[e]=Date.now()-r},A.log=function(e,t){var n=A.useid,r=A.options.logtarget;R.isNode&&(r="console");var a;if(a="string"==typeof e?A(e,t):e,"console"===r||R.isNode)"string"==typeof e&&A.options.logprompt&&console.log(n+">",e),a instanceof Array&&console.table?console.table(a):console.log(le(a));else{var s;s="output"===r?document.getElementsByTagName("output")[0]:"string"==typeof r?document.getElementById(r):r;var i="";if("string"==typeof e&&A.options.logprompt&&(i+="<pre><code>"+A.pretty(e)+"</code></pre>"),a instanceof Array)if(0===a.length)i+="<p>[ ]</p>";else if("object"!=typeof a[0]||a[0]instanceof Array)for(var o=0,u=a.length;o<u;o++)i+="<p>"+g(a[o])+"</p>";else i+=g(a);else i+=g(a);s.innerHTML+=i}},A.clear=function(){var e=A.options.logtarget;if(R.isNode||R.isMeteorServer)console.clear&&console.clear();else{var t;t="output"===e?document.getElementsByTagName("output")[0]:"string"==typeof e?document.getElementById(e):e,t.innerHTML=""}},A.write=function(e){var t=A.options.logtarget;if(R.isNode||R.isMeteorServer)console.log&&console.log(e);else{var n;n="output"===t?document.getElementsByTagName("output")[0]:"string"==typeof t?document.getElementById(t):t,n.innerHTML+=e}},A.prompt=function(e,t,n){if(R.isNode)throw new Error("The prompt not realized for Node.js");var r=0;if("string"==typeof e&&(e=document.getElementById(e)),"string"==typeof t&&(t=document.getElementById(t)),t.textContent=A.useid,n){A.prompthistory.push(n),r=A.prompthistory.length;try{var a=Date.now();A.log(n),A.write('<p style="color:blue">'+(Date.now()-a)+" ms</p>")}catch(e){A.write("<p>"+olduseid+"&gt;&nbsp;<b>"+sql+"</b></p>"),A.write('<p style="color:red">'+e+"<p>")}}var s=e.getBoundingClientRect().top+document.getElementsByTagName("body")[0].scrollTop;m(document.getElementsByTagName("body")[0],s,500),e.onkeydown=function(n){if(13===n.which){var a=e.value,s=A.useid;e.value="",A.prompthistory.push(a),r=A.prompthistory.length;try{var i=Date.now();A.log(a),A.write('<p style="color:blue">'+(Date.now()-i)+" ms</p>")}catch(e){A.write("<p>"+s+"&gt;&nbsp;"+A.pretty(a,!1)+"</p>"),A.write('<p style="color:red">'+e+"<p>")}e.focus(),t.textContent=A.useid;var o=e.getBoundingClientRect().top+document.getElementsByTagName("body")[0].scrollTop;m(document.getElementsByTagName("body")[0],o,500)}else 38===n.which?(r--,r<0&&(r=0),A.prompthistory[r]&&(e.value=A.prompthistory[r],n.preventDefault())):40===n.which&&(r++,r>=A.prompthistory.length?(r=A.prompthistory.length,e.value=""):A.prompthistory[r]&&(e.value=A.prompthistory[r],n.preventDefault()))}},z.BeginTransaction=function(e){return z.extend(this,e)},z.BeginTransaction.prototype.toString=function(){return"BEGIN TRANSACTION"},z.BeginTransaction.prototype.execute=function(e,t,n){var r=1;return A.databases[e].engineid?A.engines[A.databases[A.useid].engineid].begin(e,n):(n&&n(r),r)},z.CommitTransaction=function(e){return z.extend(this,e)},z.CommitTransaction.prototype.toString=function(){return"COMMIT TRANSACTION"},z.CommitTransaction.prototype.execute=function(e,t,n){var r=1;return A.databases[e].engineid?A.engines[A.databases[A.useid].engineid].commit(e,n):(n&&n(r),r)},z.RollbackTransaction=function(e){return z.extend(this,e)},z.RollbackTransaction.prototype.toString=function(){return"ROLLBACK TRANSACTION"},z.RollbackTransaction.prototype.execute=function(e,t,n){var r=1;return A.databases[e].engineid?A.engines[A.databases[e].engineid].rollback(e,n):(n&&n(r),r)},A.options.tsql&&(A.stdfn.OBJECT_ID=function(e,t){"undefined"==typeof t&&(t="T"),t=t.toUpperCase();var n=e.split("."),r=A.useid,a=n[0];2==n.length&&(r=n[0],a=n[1]);var s=A.databases[r].tables;r=A.databases[r].databaseid;for(var i in s)if(i==a){if(s[i].view&&"V"==t)return r+"."+i;if(!s[i].view&&"T"==t)return r+"."+i;return}}),A.options.mysql,(A.options.mysql||A.options.sqlite)&&(A.from.INFORMATION_SCHEMA=function(e,t,n,r,a){if("VIEWS"==e||"TABLES"==e){var s=[];for(var i in A.databases){var o=A.databases[i].tables;for(var u in o)(o[u].view&&"VIEWS"==e||!o[u].view&&"TABLES"==e)&&s.push({TABLE_CATALOG:i,TABLE_NAME:u})}return n&&(s=n(s,r,a)),s}throw new Error("Unknown INFORMATION_SCHEMA table")}),A.options.postgres,A.options.oracle,A.options.sqlite,A.into.SQL=function(e,t,n,r,a){var s;"object"==typeof e&&(t=e,e=void 0);var i={};if(A.utils.extend(i,t),"undefined"==typeof i.tableid)throw new Error("Table for INSERT TO is not defined.");var o="";0===r.length&&"object"==typeof n[0]&&(r=Object.keys(n[0]).map(function(e){return{columnid:e}}));for(var u=0,c=n.length;u<c;u++)o+="INSERT INTO "+t.tableid+"(",o+=r.map(function(e){return e.columnid}).join(","),o+=") VALUES (",o+=r.map(function(e){var t=n[u][e.columnid];return e.typeid?"STRING"!==e.typeid&&"VARCHAR"!==e.typeid&&"NVARCHAR"!==e.typeid&&"CHAR"!==e.typeid&&"NCHAR"!==e.typeid||(t="'"+w(t)+"'"):"string"==typeof t&&(t="'"+w(t)+"'"),t}),o+=");\n";return s=A.utils.saveFile(e,o),a&&(s=a(s)),s},A.into.HTML=function(e,t,n,r,a){var s=1;if("object"!=typeof exports){var i={headers:!0};A.utils.extend(i,t);var o=document.querySelector(e);if(!o)throw new Error("Selected HTML element is not found");0===r.length&&"object"==typeof n[0]&&(r=Object.keys(n[0]).map(function(e){return{columnid:e}}));var u=document.createElement("table"),c=document.createElement("thead");if(u.appendChild(c),i.headers){for(var l=document.createElement("tr"),h=0;h<r.length;h++){var d=document.createElement("th");d.textContent=r[h].columnid,l.appendChild(d)}c.appendChild(l)}var f=document.createElement("tbody");u.appendChild(f);for(var p=0;p<n.length;p++){for(var l=document.createElement("tr"),h=0;h<r.length;h++){var d=document.createElement("td");d.textContent=n[p][r[h].columnid],l.appendChild(d)}f.appendChild(l)}A.utils.domEmptyChildren(o),o.appendChild(u)}return a&&(s=a(s)),s},A.into.JSON=function(e,t,n,r,a){var s=1;"object"==typeof e&&(t=e,e=void 0);var i=JSON.stringify(n);return s=A.utils.saveFile(e,i),a&&(s=a(s)),s},A.into.TXT=function(e,t,n,r,a){0===r.length&&n.length>0&&(r=Object.keys(n[0]).map(function(e){return{columnid:e}})),"object"==typeof e&&(t=e,e=void 0);var s=n.length,i="";if(n.length>0){var o=r[0].columnid;i+=n.map(function(e){return e[o]}).join("\n")}return s=A.utils.saveFile(e,i),a&&(s=a(s)),s},A.into.TAB=A.into.TSV=function(e,t,n,r,a){var s={};return A.utils.extend(s,t),s.separator="\t",A.into.CSV(e,s,n,r,a)},A.into.CSV=function(e,t,n,r,a){0===r.length&&n.length>0&&(r=Object.keys(n[0]).map(function(e){return{columnid:e}})),"object"==typeof e&&(t=e,e=void 0);var s={headers:!0};s.separator=";",s.quote='"',s.utf8Bom=!0,t&&!t.headers&&"undefined"!=typeof t.headers&&(s.utf8Bom=!1),A.utils.extend(s,t);var i=n.length,o=s.utf8Bom?"\ufeff":"";return s.headers&&(o+=s.quote+r.map(function(e){return e.columnid.trim()}).join(s.quote+s.separator+s.quote)+s.quote+"\r\n"),n.forEach(function(e){o+=r.map(function(t){var n=e[t.columnid];return n=(n+"").replace(new RegExp("\\"+s.quote,"g"),'""'),+n!=n&&(n=s.quote+n+s.quote),n}).join(s.separator)+"\r\n"}),i=A.utils.saveFile(e,o),a&&(i=a(i)),i},A.into.XLS=function(e,t,n,r,a){function s(){var e='<html xmlns:o="urn:schemas-microsoft-com:office:office" xmlns:x="urn:schemas-microsoft-com:office:excel" \t\txmlns="http://www.w3.org/TR/REC-html40"><head> \t\t<meta charset="utf-8" /> \t\t<!--[if gte mso 9]><xml><x:ExcelWorkbook><x:ExcelWorksheets> ';if(e+=" <x:ExcelWorksheet><x:Name>"+o.sheetid+"</x:Name><x:WorksheetOptions><x:DisplayGridlines/>     </x:WorksheetOptions> \t\t</x:ExcelWorksheet>",e+="</x:ExcelWorksheets></x:ExcelWorkbook></xml><![endif]--></head>",e+="<body","undefined"!=typeof o.style&&(e+=' style="',e+="function"==typeof o.style?o.style(o):o.style,e+='"'),e+=">",e+="<table>","undefined"!=typeof o.caption){var a=o.caption;"string"==typeof a&&(a={title:a}),e+="<caption","undefined"!=typeof a.style&&(e+=' style="',e+="function"==typeof a.style?a.style(o,a):a.style,e+='" '),e+=">",e+=a.title,e+="</caption>"}return"undefined"!=typeof o.columns?r=o.columns:0==r.length&&n.length>0&&"object"==typeof n[0]&&(r=n[0]instanceof Array?n[0].map(function(e,t){return{columnid:t}}):Object.keys(n[0]).map(function(e){return{columnid:e}})),r.forEach(function(e,t){"undefined"!=typeof o.column&&V(e,o.column),"undefined"==typeof e.width&&(o.column&&"undefined"!=o.column.width?e.width=o.column.width:e.width="120px"),"number"==typeof e.width&&(e.width=e.width+"px"),"undefined"==typeof e.columnid&&(e.columnid=t),"undefined"==typeof e.title&&(e.title=""+e.columnid.trim()),o.headers&&o.headers instanceof Array&&(e.title=o.headers[t])}),e+="<colgroups>",r.forEach(function(t){e+='<col style="width: '+t.width+'"></col>'}),e+="</colgroups>",o.headers&&(e+="<thead>",e+="<tr>",r.forEach(function(t,n){e+="<th ","undefined"!=typeof t.style&&(e+=' style="',e+="function"==typeof t.style?t.style(o,t,n):t.style,e+='" '),e+=">","undefined"!=typeof t.title&&(e+="function"==typeof t.title?t.title(o,t,n):t.title),e+="</th>"}),e+="</tr>",e+="</thead>"),e+="<tbody>",n&&n.length>0&&n.forEach(function(n,a){if(!(a>o.limit)){e+="<tr";var s={};V(s,o.row),o.rows&&o.rows[a]&&V(s,o.rows[a]),"undefined"!=typeof s&&"undefined"!=typeof s.style&&(e+=' style="',e+="function"==typeof s.style?s.style(o,n,a):s.style,e+='" '),e+=">",r.forEach(function(r,i){var u={};V(u,o.cell),V(u,s.cell),"undefined"!=typeof o.column&&V(u,o.column.cell),V(u,r.cell),o.cells&&o.cells[a]&&o.cells[a][i]&&V(u,o.cells[a][i]);var c=n[r.columnid];"function"==typeof u.value&&(c=u.value(c,o,n,r,u,a,i));var l=u.typeid;"function"==typeof l&&(l=l(c,o,n,r,u,a,i)),"undefined"==typeof l&&("number"==typeof c?l="number":"string"==typeof c?l="string":"boolean"==typeof c?l="boolean":"object"==typeof c&&c instanceof Date&&(l="date"));var h="";"money"==l?h='mso-number-format:"\\#\\,\\#\\#0\\\\ _р_\\.";white-space:normal;':"number"==l?h=" ":"date"==l?h='mso-number-format:"Short Date";':t.types&&t.types[l]&&t.types[l].typestyle&&(h=t.types[l].typestyle),h=h||'mso-number-format:"\\@";',e+="<td style='"+h+"' ","undefined"!=typeof u.style&&(e+=' style="',e+="function"==typeof u.style?u.style(c,o,n,r,a,i):u.style,e+='" '),e+=">";var d=u.format;if("undefined"==typeof c)e+="";else if("undefined"!=typeof d)if("function"==typeof d)e+=d(c);else{if("string"!=typeof d)throw new Error("Unknown format type. Should be function or string");e+=c}else e+="number"==l||"date"==l?c.toString():"money"==l?(+c).toFixed(2):c;e+="</td>"}),e+="</tr>"}}),e+="</tbody>",e+="</table>",e+="</body>",e+="</html>"}"object"==typeof e&&(t=e,e=void 0);var i={};t&&t.sheets&&(i=t.sheets);var o={headers:!0};"undefined"!=typeof i.Sheet1?o=i[0]:"undefined"!=typeof t&&(o=t),"undefined"==typeof o.sheetid&&(o.sheetid="Sheet1");var u=s(),c=A.utils.saveFile(e,u);return a&&(c=a(c)),c},A.into.XLSXML=function(e,t,n,r,a){function s(){function e(e){var t="";for(var n in e){t+="<"+n;for(var r in e[n])t+=" ",t+="x:"==r.substr(0,2)?r:"ss:",t+=r+'="'+e[n][r]+'"';t+="/>"}var a=L(t);return u[a]||(u[a]={styleid:c},s+='<Style ss:ID="s'+c+'">',s+=t,s+="</Style>",c++),"s"+u[a].styleid}var a='<?xml version="1.0"?> \t\t<Workbook xmlns="urn:schemas-microsoft-com:office:spreadsheet" \t\t xmlns:o="urn:schemas-microsoft-com:office:office" \t\t xmlns:x="urn:schemas-microsoft-com:office:excel" \t\t xmlns:ss="urn:schemas-microsoft-com:office:spreadsheet" \t\t xmlns:html="http://www.w3.org/TR/REC-html40"> \t\t <DocumentProperties xmlns="urn:schemas-microsoft-com:office:office"> \t\t </DocumentProperties> \t\t <OfficeDocumentSettings xmlns="urn:schemas-microsoft-com:office:office"> \t\t  <AllowPNG/> \t\t </OfficeDocumentSettings> \t\t <ExcelWorkbook xmlns="urn:schemas-microsoft-com:office:excel"> \t\t  <ActiveSheet>0</ActiveSheet> \t\t </ExcelWorkbook> \t\t <Styles> \t\t  <Style ss:ID="Default" ss:Name="Normal"> \t\t   <Alignment ss:Vertical="Bottom"/> \t\t   <Borders/> \t\t   <Font ss:FontName="Calibri" x:Family="Swiss" ss:Size="12" ss:Color="#000000"/> \t\t   <Interior/> \t\t   <NumberFormat/> \t\t   <Protection/> \t\t  </Style>',s="",o=" </Styles>",u={},c=62;for(var l in i){var h=i[l];"undefined"!=typeof h.columns?r=h.columns:0==r.length&&n.length>0&&"object"==typeof n[0]&&(r=n[0]instanceof Array?n[0].map(function(e,t){return{columnid:t}}):Object.keys(n[0]).map(function(e){return{columnid:e}})),r.forEach(function(e,t){"undefined"!=typeof h.column&&V(e,h.column),"undefined"==typeof e.width&&(h.column&&"undefined"!=typeof h.column.width?e.width=h.column.width:e.width=120),"number"==typeof e.width&&(e.width=e.width),"undefined"==typeof e.columnid&&(e.columnid=t),"undefined"==typeof e.title&&(e.title=""+e.columnid.trim()),h.headers&&h.headers instanceof Array&&(e.title=h.headers[idx])}),o+='<Worksheet ss:Name="'+l+'"> \t  \t\t\t<Table ss:ExpandedColumnCount="'+r.length+'" ss:ExpandedRowCount="'+((h.headers?1:0)+Math.min(n.length,h.limit||n.length))+'" x:FullColumns="1" \t   \t\t\tx:FullRows="1" ss:DefaultColumnWidth="65" ss:DefaultRowHeight="15">',r.forEach(function(e,t){o+='<Column ss:Index="'+(t+1)+'" ss:AutoFitWidth="0" ss:Width="'+e.width+'"/>'}),h.headers&&(o+='<Row ss:AutoFitHeight="0">',r.forEach(function(t,n){if(o+="<Cell ","undefined"!=typeof t.style){var r={};"function"==typeof t.style?V(r,t.style(h,t,n)):V(r,t.style),o+='ss:StyleID="'+e(r)+'"'}o+='><Data ss:Type="String">',"undefined"!=typeof t.title&&(o+="function"==typeof t.title?t.title(h,t,n):t.title),o+="</Data></Cell>"}),o+="</Row>"),n&&n.length>0&&n.forEach(function(n,a){if(!(a>h.limit)){var s={};if(V(s,h.row),h.rows&&h.rows[a]&&V(s,h.rows[a]),o+="<Row ","undefined"!=typeof s){var i={};"undefined"!=typeof s.style&&("function"==typeof s.style?V(i,s.style(h,n,a)):V(i,s.style),o+='ss:StyleID="'+e(i)+'"')}o+=">",r.forEach(function(r,i){var u={};V(u,h.cell),V(u,s.cell),"undefined"!=typeof h.column&&V(u,h.column.cell),V(u,r.cell),h.cells&&h.cells[a]&&h.cells[a][i]&&V(u,h.cells[a][i]);var c=n[r.columnid];"function"==typeof u.value&&(c=u.value(c,h,n,r,u,a,i));var l=u.typeid;"function"==typeof l&&(l=l(c,h,n,r,u,a,i)),"undefined"==typeof l&&("number"==typeof c?l="number":"string"==typeof c?l="string":"boolean"==typeof c?l="boolean":"object"==typeof c&&c instanceof Date&&(l="date"));var d="String";"number"==l?d="Number":"date"==l&&(d="Date");var f="";"money"==l?f='mso-number-format:"\\#\\,\\#\\#0\\\\ _р_\\.";white-space:normal;':"number"==l?f=" ":"date"==l?f='mso-number-format:"Short Date";':t.types&&t.types[l]&&t.types[l].typestyle&&(f=t.types[l].typestyle),f=f||'mso-number-format:"\\@";',o+="<Cell ";var p={};"undefined"!=typeof u.style&&("function"==typeof u.style?V(p,u.style(c,h,n,r,a,i)):V(p,u.style),o+='ss:StyleID="'+e(p)+'"'),o+=">",o+='<Data ss:Type="'+d+'">';var b=u.format;if("undefined"==typeof c)o+="";else if("undefined"!=typeof b)if("function"==typeof b)o+=b(c);else{if("string"!=typeof b)throw new Error("Unknown format type. Should be function or string");o+=c}else o+="number"==l||"date"==l?c.toString():"money"==l?(+c).toFixed(2):c;o+="</Data></Cell>"}),o+="</Row>"}}),o+="</Table></Worksheet>"}return o+="</Workbook>",a+s+o}t=t||{},"object"==typeof e&&(t=e,e=void 0);var i={};t&&t.sheets?i=t.sheets:i.Sheet1=t;var o=A.utils.saveFile(e,s());return a&&(o=a(o)),o},A.into.XLSX=function(e,t,n,r,s){function i(){"object"==typeof t&&t instanceof Array?n&&n.length>0&&n.forEach(function(e,n){o(t[n],e,void 0,n+1)}):o(t,n,r,1),u(s)}function o(e,t,n,r){var a={sheetid:"Sheet "+r,headers:!0};A.utils.extend(a,e),(!n||0==n.length)&&t.length>0&&(n=Object.keys(t[0]).map(function(e){return{columnid:e}}));var s={};h.SheetNames.indexOf(a.sheetid)>-1?s=h.Sheets[a.sheetid]:(h.SheetNames.push(a.sheetid),h.Sheets[a.sheetid]={},s=h.Sheets[a.sheetid]);var i="A1";a.range&&(i=a.range);var o=A.utils.xlscn(i.match(/[A-Z]+/)[0]),u=+i.match(/[0-9]+/)[0]-1;if(h.Sheets[a.sheetid]["!ref"])var c=h.Sheets[a.sheetid]["!ref"],l=A.utils.xlscn(c.match(/[A-Z]+/)[0]),d=+c.match(/[0-9]+/)[0]-1;else var l=1,d=1;var f=Math.max(o+n.length,l),p=Math.max(u+t.length+2,d),b=u+1;h.Sheets[a.sheetid]["!ref"]="A1:"+A.utils.xlsnc(f)+p,a.headers&&(n.forEach(function(e,t){s[A.utils.xlsnc(o+t)+""+b]={v:e.columnid.trim()}}),b++);for(var E=0;E<t.length;E++)n.forEach(function(e,n){var r={v:t[E][e.columnid]};"number"==typeof t[E][e.columnid]?r.t="n":"string"==typeof t[E][e.columnid]?r.t="s":"boolean"==typeof t[E][e.columnid]?r.t="b":"object"==typeof t[E][e.columnid]&&t[E][e.columnid]instanceof Date&&(r.t="d"),s[A.utils.xlsnc(o+n)+""+b]=r}),b++}function u(t){function n(e){for(var t=new ArrayBuffer(e.length),n=new Uint8Array(t),r=0;r!=e.length;++r)n[r]=255&e.charCodeAt(r);return t}var r;if("undefined"==typeof e)c=h;else if(r=j(),R.isNode||R.isMeteorServer)r.writeFile(h,e);else{var s={bookType:"xlsx",bookSST:!1,type:"binary"},i=r.write(h,s);if(9==a())throw new Error("Cannot save XLSX files in IE9. Please use XLS() export function");me(new Blob([n(i)],{type:"application/octet-stream"}),e)}}var c=1;q(r,[{columnid:"_"}])&&(n=n.map(function(e){return e._}),r=void 0);var l=j();"object"==typeof e&&(t=e,e=void 0);var h={SheetNames:[],Sheets:{}};return t.sourcefilename?A.utils.loadBinaryFile(t.sourcefilename,!!s,function(e){h=l.read(e,{type:"binary"}),i()}):i(),s&&(c=s(c)),c},A.from.METEOR=function(e,t,n,r,a){var s=e.find(t).fetch();return n&&(s=n(s,r,a)),s},A.from.TABLETOP=function(e,t,n,r,a){var s=[],i={headers:!0,simpleSheet:!0,key:e};return A.utils.extend(i,t),i.callback=function(e){for(var t=0;t<e.length;t++)for(var i in e[t])e[t][i]==+e[t][i]&&e[t].hasOwnProperty(i)&&(e[t][i]=+e[t][i]);s=e,n&&(s=n(s,r,a))},Tabletop.init(i),null},A.from.HTML=function(e,t,n,r,a){var s={};A.utils.extend(s,t);var i=document.querySelector(e);if(!i&&"TABLE"!==i.tagName)throw new Error("Selected HTML element is not a TABLE");var o=[],u=s.headers;if(u&&!(u instanceof Array)){u=[];for(var c=i.querySelector("thead tr").children,l=0;l<c.length;l++)c.item(l).style&&"none"===c.item(l).style.display&&s.skipdisplaynone?u.push(void 0):u.push(c.item(l).textContent)}for(var h=i.querySelectorAll("tbody tr"),d=0;d<h.length;d++){for(var f=h.item(d).children,p={},l=0;l<f.length;l++)f.item(l).style&&"none"===f.item(l).style.display&&s.skipdisplaynone||(u?p[u[l]]=f.item(l).textContent:p[l]=f.item(l).textContent);o.push(p)}return n&&(o=n(o,r,a)),o},A.from.RANGE=function(e,t,n,r,a){for(var s=[],i=e;i<=t;i++)s.push(i);return n&&(s=n(s,r,a)),s},A.from.FILE=function(e,t,n,r,a){var s;if("string"==typeof e)s=e;else{if(!(e instanceof Event))throw new Error("Wrong usage of FILE() function");s=e.target.files[0].name}var i=s.split("."),o=i[i.length-1].toUpperCase();if(A.from[o])return A.from[o](e,t,n,r,a);throw new Error("Cannot recognize file type for loading")},A.from.JSON=function(e,t,n,r,a){var s;return A.utils.loadFile(e,!!n,function(e){s=JSON.parse(e),n&&(s=n(s,r,a))}),s},A.from.TXT=function(e,t,n,r,a){var s;return A.utils.loadFile(e,!!n,function(e){s=e.split(/\r?\n/),""===s[s.length-1]&&s.pop();for(var t=0,i=s.length;t<i;t++)s[t]==+s[t]&&(s[t]=+s[t]),s[t]=[s[t]];n&&(s=n(s,r,a))}),s},A.from.TAB=A.from.TSV=function(e,t,n,r,a){return t=t||{},t.separator="\t",A.from.CSV(e,t,n,r,a)},A.from.CSV=function(e,t,n,r,a){function s(e){function t(){if(E>=b)return f;if(c)return c=!1,d;var t=E;if(e.charCodeAt(t)===h){for(var n=t;n++<b;)if(e.charCodeAt(n)===h){if(e.charCodeAt(n+1)!==h)break;++n}E=n+2;var r=e.charCodeAt(n+1);return 13===r?(c=!0,10===e.charCodeAt(n+2)&&++E):10===r&&(c=!0),e.substring(t+1,n).replace(/""/g,'"')}for(;E<b;){var r=e.charCodeAt(E++),a=1;if(10===r)c=!0;else if(13===r)c=!0,10===e.charCodeAt(E)&&(++E,++a);else if(r!==l)continue;return e.substring(t,E-a)}return e.substring(t)}for(var s,c,l=i.separator.charCodeAt(0),h=i.quote.charCodeAt(0),d={},f={},p=[],b=e.length,E=0,g=0;(s=t())!==f;){for(var m=[];s!==d&&s!==f;)m.push(s.trim()),s=t();if(i.headers){if(0===g){if("boolean"==typeof i.headers)u=m;else if(i.headers instanceof Array){u=i.headers;var v={};u.forEach(function(e,t){v[e]=m[t],"undefined"!=typeof v[e]&&0!==v[e].length&&v[e].trim()==+v[e]&&(v[e]=+v[e])}),p.push(v)}}else{var v={};u.forEach(function(e,t){v[e]=m[t],"undefined"!=typeof v[e]&&0!==v[e].length&&v[e].trim()==+v[e]&&(v[e]=+v[e])}),p.push(v)}g++}else p.push(m)}if(o=p,i.headers&&a&&a.sources&&a.sources[r]){var S=a.sources[r].columns=[];u.forEach(function(e){S.push({columnid:e})})}n&&(o=n(o,r,a))}var i={separator:",",quote:'"',headers:!0};A.utils.extend(i,t);var o,u=[];return new RegExp("\n").test(e)?s(e):A.utils.loadFile(e,!!n,s),o},A.from.XLS=function(e,t,n,r,a){return S(H(),e,t,n,r,a)},A.from.XLSX=function(e,t,n,r,a){return S(j(),e,t,n,r,a)},A.from.XML=function(e,t,n,r,a){var s;return A.utils.loadFile(e,!!n,function(e){s=T(e).root,n&&(s=n(s,r,a))}),s},A.from.GEXF=function(e,t,n,r,a){var s;return A("SEARCH FROM XML("+e+")",[],function(e){s=e,console.log(s),n&&(s=n(s))}),s},z.Help=function(e){return z.extend(this,e)},z.Help.prototype.toString=function(){var e="HELP";return this.subject&&(e+=" "+this.subject),e};var de=[{command:"ALTER TABLE table RENAME TO table"},{command:"ALTER TABLE table ADD COLUMN column coldef"},{command:"ALTER TABLE table MODIFY COLUMN column coldef"},{command:"ALTER TABLE table RENAME COLUMN column TO column"},{command:"ALTER TABLE table DROP column"},{command:"ATTACH engine DATABASE database"
},{command:"ASSERT value"},{command:"BEGIN [TRANSACTION]"},{command:"COMMIT [TRANSACTION]"},{command:"CREATE [engine] DATABASE [IF NOT EXISTS] database"},{command:"CREATE TABLE [IF NOT EXISTS] table (column definitions)"},{command:"DELETE FROM table [WHERE expression]"},{command:"DETACH DATABASE database"},{command:"DROP [engine] DATABASE [IF EXISTS] database"},{command:"DROP TABLE [IF EXISTS] table"},{command:"INSERT INTO table VALUES value,..."},{command:"INSERT INTO table DEFAULT VALUES"},{command:"INSERT INTO table SELECT select"},{command:"HELP [subject]"},{command:"ROLLBACK [TRANSACTION]"},{command:"SELECT [modificator] columns [INTO table] [FROM table,...] [[mode] JOIN [ON] [USING]] [WHERE ] [GROUP BY] [HAVING] [ORDER BY] "},{command:"SET option value"},{command:"SHOW [engine] DATABASES"},{command:"SHOW TABLES"},{command:"SHOW CREATE TABLE table"},{command:"UPDATE table SET column1 = expression1, ... [WHERE expression]"},{command:"USE [DATABASE] database"},{command:"expression"},{command:'See also <a href="http://github/agershun/alasq">http://github/agershun/alasq</a> for more information'}];z.Help.prototype.execute=function(e,t,n){var r=[];return this.subject?r.push('See also <a href="http://github/agershun/alasq">http://github/agershun/alasq</a> for more information'):r=de,n&&(r=n(r)),r},z.Print=function(e){return z.extend(this,e)},z.Print.prototype.toString=function(){var e="PRINT";return this.statement&&(e+=" "+this.statement.toString()),e},z.Print.prototype.execute=function(e,t,n){var r=this,a=1;if(A.precompile(this,e,t),this.exprs&&this.exprs.length>0){var s=this.exprs.map(function(e){var n=new Function("params,alasql,p","var y;return "+e.toJS("({})","",null)).bind(r),a=n(t,A);return le(a)});console.log.apply(console,s)}else if(this.select){var i=this.select.execute(e,t);console.log(le(i))}else console.log();return n&&(a=n(a)),a},z.Source=function(e){return z.extend(this,e)},z.Source.prototype.toString=function(){var e="SOURCE";return this.url&&(e+=" '"+this.url+" '"),e},z.Source.prototype.execute=function(e,t,n){var r;return D(this.url,!!n,function(e){return r=A(e),n&&(r=n(r)),r},function(e){throw e}),r},z.Require=function(e){return z.extend(this,e)},z.Require.prototype.toString=function(){var e="REQUIRE";return this.paths&&this.paths.length>0&&(e+=this.paths.map(function(e){return e.toString()}).join(",")),this.plugins&&this.plugins.length>0&&(e+=this.plugins.map(function(e){return e.toUpperCase()}).join(",")),e},z.Require.prototype.execute=function(e,t,n){var r=this,a=0,s="";return this.paths&&this.paths.length>0?this.paths.forEach(function(e){D(e.value,!!n,function(e){a++,s+=e,a<r.paths.length||(new Function("params,alasql",s)(t,A),n&&(a=n(a)))})}):this.plugins&&this.plugins.length>0?this.plugins.forEach(function(e){A.plugins[e]||D(A.path+"/alasql-"+e.toLowerCase()+".js",!!n,function(i){a++,s+=i,a<r.plugins.length||(new Function("params,alasql",s)(t,A),A.plugins[e]=!0,n&&(a=n(a)))})}):n&&(a=n(a)),a},z.Assert=function(e){return z.extend(this,e)},z.Source.prototype.toString=function(){var e="ASSERT";return this.value&&(e+=" "+JSON.stringify(this.value)),e},z.Assert.prototype.execute=function(e){if(!q(A.res,this.value))throw new Error((this.message||"Assert wrong")+": "+JSON.stringify(A.res)+" == "+JSON.stringify(this.value));return 1};var fe=A.engines.WEBSQL=function(){};fe.createDatabase=function(e,t,n,r){var a=1,s=openDatabase(e,t[0],t[1],t[2]);if(this.dbid){var i=A.createDatabase(this.dbid);i.engineid="WEBSQL",i.wdbid=e,sb.wdb=i}if(!s)throw new Error('Cannot create WebSQL database "'+databaseid+'"');return r&&r(a),a},fe.dropDatabase=function(e){throw new Error("This is impossible to drop WebSQL database.")},fe.attachDatabase=function(e,t,n,r,a){var s=1;if(A.databases[t])throw new Error('Unable to attach database as "'+t+'" because it already exists');return alasqlopenDatabase(e,n[0],n[1],n[2]),s};var pe=A.engines.INDEXEDDB=function(){};R.hasIndexedDB&&("function"==typeof R.global.indexedDB.webkitGetDatabaseNames?pe.getDatabaseNames=R.global.indexedDB.webkitGetDatabaseNames.bind(R.global.indexedDB):(pe.getDatabaseNames=function(){var e={},t={contains:function(e){return!0},notsupported:!0};return setTimeout(function(){var n={target:{result:t}};e.onsuccess(n)},0),e},pe.getDatabaseNamesNotSupported=!0)),pe.showDatabases=function(e,t){var n=pe.getDatabaseNames();n.onsuccess=function(n){var r=n.target.result;if(pe.getDatabaseNamesNotSupported)throw new Error("SHOW DATABASE is not supported in this browser");var a=[];if(e)var s=new RegExp(e.value.replace(/\%/g,".*"),"g");for(var i=0;i<r.length;i++)e&&!r[i].match(s)||a.push({databaseid:r[i]});t(a)}},pe.createDatabase=function(e,t,n,r,a){console.log(arguments);var s=R.global.indexedDB;if(n){var i=s.open(e,1);i.onsuccess=function(e){e.target.result.close(),a&&a(1)}}else{var o=s.open(e,1);o.onupgradeneeded=function(e){console.log("abort"),e.target.transaction.abort()},o.onsuccess=function(t){if(console.log("success"),!n)throw new Error('IndexedDB: Cannot create new database "'+e+'" because it already exists');a&&a(0)}}},pe.createDatabase=function(e,t,n,r,a){var s=R.global.indexedDB;if(pe.getDatabaseNamesNotSupported)if(n){var i=!0,o=s.open(e);o.onupgradeneeded=function(e){i=!1},o.onsuccess=function(e){e.target.result.close(),i?a&&a(0):a&&a(1)}}else{var u=s.open(e);u.onupgradeneeded=function(e){e.target.transaction.abort()},u.onabort=function(e){a&&a(1)},u.onsuccess=function(t){throw t.target.result.close(),new Error('IndexedDB: Cannot create new database "'+e+'" because it already exists')}}else{var u=pe.getDatabaseNames();u.onsuccess=function(t){var r=t.target.result;if(r.contains(e)){if(n)return void(a&&a(0));throw new Error('IndexedDB: Cannot create new database "'+e+'" because it already exists')}var i=s.open(e,1);i.onsuccess=function(e){e.target.result.close(),a&&a(1)}}}},pe.dropDatabase=function(e,t,n){var r=R.global.indexedDB,a=pe.getDatabaseNames();a.onsuccess=function(a){var s=a.target.result;if(!s.contains(e)){if(t)return void(n&&n(0));throw new Error('IndexedDB: Cannot drop new database "'+e+'" because it does not exist')}var i=r.deleteDatabase(e);i.onsuccess=function(e){n&&n(1)}}},pe.attachDatabase=function(e,t,n,r,a){if(!R.hasIndexedDB)throw new Error("The current browser does not support IndexedDB");var s=R.global.indexedDB,i=pe.getDatabaseNames();i.onsuccess=function(n){var r=n.target.result;if(!r.contains(e))throw new Error('IndexedDB: Cannot attach database "'+e+'" because it does not exist');var i=s.open(e);i.onsuccess=function(n){var r=n.target.result,s=new A.Database(t||e);s.engineid="INDEXEDDB",s.ixdbid=e,s.tables=[];for(var i=r.objectStoreNames,o=0;o<i.length;o++)s.tables[i[o]]={};n.target.result.close(),a&&a(1)}}},pe.createTable=function(e,t,n,r){var a=R.global.indexedDB,s=A.databases[e].ixdbid,i=pe.getDatabaseNames();i.onsuccess=function(n){var i=n.target.result;if(!i.contains(s))throw new Error('IndexedDB: Cannot create table in database "'+s+'" because it does not exist');var o=a.open(s);o.onversionchange=function(e){e.target.result.close()},o.onsuccess=function(n){var i=n.target.result.version;n.target.result.close();var o=a.open(s,i+1);o.onupgradeneeded=function(e){var n=e.target.result;n.createObjectStore(t,{autoIncrement:!0})},o.onsuccess=function(e){e.target.result.close(),r&&r(1)},o.onerror=function(e){throw e},o.onblocked=function(n){throw new Error('Cannot create table "'+t+'" because database "'+e+'"  is blocked')}}}},pe.dropTable=function(e,t,n,r){var a=R.global.indexedDB,s=A.databases[e].ixdbid,i=pe.getDatabaseNames();i.onsuccess=function(i){var o=i.target.result;if(!o.contains(s))throw new Error('IndexedDB: Cannot drop table in database "'+s+'" because it does not exist');var u=a.open(s);u.onversionchange=function(e){e.target.result.close()},u.onsuccess=function(i){var o=i.target.result.version;i.target.result.close();var u=a.open(s,o+1);u.onupgradeneeded=function(r){var a=r.target.result;if(a.objectStoreNames.contains(t))a.deleteObjectStore(t),delete A.databases[e].tables[t];else if(!n)throw new Error('IndexedDB: Cannot drop table "'+t+'" because it does not exist')},u.onsuccess=function(e){e.target.result.close(),r&&r(1)},u.onerror=function(e){throw e},u.onblocked=function(n){throw new Error('Cannot drop table "'+t+'" because database "'+e+'" is blocked')}}}},pe.intoTable=function(e,t,n,r,a){var s=R.global.indexedDB,i=A.databases[e].ixdbid,o=s.open(i);o.onsuccess=function(e){for(var r=e.target.result,s=r.transaction([t],"readwrite"),i=s.objectStore(t),o=0,u=n.length;o<u;o++)i.add(n[o]);s.oncomplete=function(){r.close(),a&&a(u)}}},pe.fromTable=function(e,t,n,r,a){var s=R.global.indexedDB,i=A.databases[e].ixdbid,o=s.open(i);o.onsuccess=function(e){var s=[],i=e.target.result,o=i.transaction([t]),u=o.objectStore(t),c=u.openCursor();c.onblocked=function(e){},c.onerror=function(e){},c.onsuccess=function(e){var t=e.target.result;t?(s.push(t.value),t.continue()):(i.close(),n&&n(s,r,a))}}},pe.deleteFromTable=function(e,t,n,r,a){var s=R.global.indexedDB,i=A.databases[e].ixdbid,o=s.open(i);o.onsuccess=function(e){var s=e.target.result,i=s.transaction([t],"readwrite"),o=i.objectStore(t),u=o.openCursor(),c=0;u.onblocked=function(e){},u.onerror=function(e){},u.onsuccess=function(e){var t=e.target.result;t?(n&&!n(t.value,r)||(t.delete(),c++),t.continue()):(s.close(),a&&a(c))}}},pe.updateTable=function(e,t,n,r,a,s){var i=R.global.indexedDB,o=A.databases[e].ixdbid,u=i.open(o);u.onsuccess=function(e){var i=e.target.result,o=i.transaction([t],"readwrite"),u=o.objectStore(t),c=u.openCursor(),l=0;c.onblocked=function(e){},c.onerror=function(e){},c.onsuccess=function(e){var t=e.target.result;if(t){if(!r||r(t.value,a)){var o=t.value;n(o,a),t.update(o),l++}t.continue()}else i.close(),s&&s(l)}}};var be=A.engines.LOCALSTORAGE=function(){};be.get=function(e){var t=localStorage.getItem(e);if("undefined"!=typeof t){var n=void 0;try{n=JSON.parse(t)}catch(e){throw new Error("Cannot parse JSON object from localStorage"+t)}return n}},be.set=function(e,t){"undefined"==typeof t?localStorage.removeItem(e):localStorage.setItem(e,JSON.stringify(t))},be.storeTable=function(e,t){var n=A.databases[e],r=n.tables[t],a={};a.columns=r.columns,a.data=r.data,a.identities=r.identities,be.set(n.lsdbid+"."+t,a)},be.restoreTable=function(e,t){var n=A.databases[e],r=be.get(n.lsdbid+"."+t),a=new A.Table;for(var s in r)a[s]=r[s];return n.tables[t]=a,a.indexColumns(),a},be.removeTable=function(e,t){var n=A.databases[e];localStorage.removeItem(n.lsdbid+"."+t)},be.createDatabase=function(e,t,n,r,a){var s=1,i=be.get("alasql");if(n&&i&&i.databases&&i.databases[e])s=0;else{if(i||(i={databases:{}}),i.databases&&i.databases[e])throw new Error('localStorage: Cannot create new database "'+e+'" because it already exists');i.databases[e]=!0,be.set("alasql",i),be.set(e,{databaseid:e,tables:{}})}return a&&(s=a(s)),s},be.dropDatabase=function(e,t,n){var r=1,a=be.get("alasql");if(t&&a&&a.databases&&!a.databases[e])r=0;else{if(!a){if(t)return n?n(0):0;throw new Error("There is no any AlaSQL databases in localStorage")}if(a.databases&&!a.databases[e])throw new Error('localStorage: Cannot drop database "'+e+'" because there is no such database');delete a.databases[e],be.set("alasql",a);var s=be.get(e);for(var i in s.tables)localStorage.removeItem(e+"."+i);localStorage.removeItem(e)}return n&&(r=n(r)),r},be.attachDatabase=function(e,t,n,r,a){var s=1;if(A.databases[t])throw new Error('Unable to attach database as "'+t+'" because it already exists');t||(t=e);var i=new A.Database(t);if(i.engineid="LOCALSTORAGE",i.lsdbid=e,i.tables=be.get(e).tables,!A.options.autocommit&&i.tables)for(var o in i.tables)be.restoreTable(t,o);return a&&(s=a(s)),s},be.showDatabases=function(e,t){var n=[],r=be.get("alasql");if(e)var a=new RegExp(e.value.replace(/\%/g,".*"),"g");if(r&&r.databases){for(dbid in r.databases)n.push({databaseid:dbid});e&&n&&n.length>0&&(n=n.filter(function(e){return e.databaseid.match(a)}))}return t&&(n=t(n)),n},be.createTable=function(e,t,n,r){var a=1,s=A.databases[e].lsdbid,i=be.get(s+"."+t);if(i&&!n)throw new Error('Table "'+t+'" alsready exists in localStorage database "'+s+'"');var o=be.get(s);A.databases[e].tables[t];return o.tables[t]=!0,be.set(s,o),be.storeTable(e,t),r&&(a=r(a)),a},be.dropTable=function(e,t,n,r){var a=1,s=A.databases[e].lsdbid;if(A.options.autocommit)var i=be.get(s);else var i=A.databases[e];if(!n&&!i.tables[t])throw new Error('Cannot drop table "'+t+'" in localStorage, because it does not exist');return delete i.tables[t],be.set(s,i),be.removeTable(e,t),r&&(a=r(a)),a},be.fromTable=function(e,t,n,r,a){var s=(A.databases[e].lsdbid,be.restoreTable(e,t).data);return n&&(s=n(s,r,a)),s},be.intoTable=function(e,t,n,r,a){var s=(A.databases[e].lsdbid,n.length),i=be.restoreTable(e,t);return i.data||(i.data=[]),i.data=i.data.concat(n),be.storeTable(e,t),a&&(s=a(s)),s},be.loadTableData=function(e,t){A.databases[e],A.databases[e].lsdbid;be.restoreTable(e,t)},be.saveTableData=function(e,t){var n=A.databases[e],r=A.databases[e].lsdbid;be.storeTable(r,t),n.tables[t].data=void 0},be.commit=function(e,t){var n=A.databases[e],r=A.databases[e].lsdbid,a={databaseid:r,tables:{}};if(n.tables)for(var s in n.tables)a.tables[s]=!0,be.storeTable(e,s);return be.set(r,a),t?t(1):1},be.begin=be.commit,be.rollback=function(e,t){return};var Ee=A.engines.SQLITE=function(){};Ee.createDatabase=function(e,t,n,r,a){throw new Error("Connot create SQLITE database in memory. Attach it.")},Ee.dropDatabase=function(e){throw new Error("This is impossible to drop SQLite database. Detach it.")},Ee.attachDatabase=function(e,t,n,r,a){var s=1;if(A.databases[t])throw new Error('Unable to attach database as "'+t+'" because it already exists');if(n[0]&&n[0]instanceof z.StringValue||n[0]instanceof z.ParamValue){if(n[0]instanceof z.StringValue)var i=n[0].value;else if(n[0]instanceof z.ParamValue)var i=r[n[0].param];return A.utils.loadBinaryFile(i,!0,function(n){var r=new A.Database(t||e);r.engineid="SQLITE",r.sqldbid=e;var s=r.sqldb=new SQL.Database(n);r.tables=[];var i=s.exec("SELECT * FROM sqlite_master WHERE type='table'")[0].values;i.forEach(function(e){r.tables[e[1]]={};var t=r.tables[e[1]].columns=[],n=A.parse(e[4]),a=n.statements[0].columns;a&&a.length>0&&a.forEach(function(e){t.push(e)})}),a(1)},function(e){throw new Error('Cannot open SQLite database file "'+n[0].value+'"')}),s}throw new Error("Cannot attach SQLite database without a file")},Ee.fromTable=function(e,t,n,r,a){var s=A.databases[e].sqldb.exec("SELECT * FROM "+t),i=a.sources[r].columns=[];s[0].columns.length>0&&s[0].columns.forEach(function(e){i.push({columnid:e})});var o=[];s[0].values.length>0&&s[0].values.forEach(function(e){var t={};i.forEach(function(n,r){t[n.columnid]=e[r]}),o.push(t)}),n&&n(o,r,a)},Ee.intoTable=function(e,t,n,r,a){for(var s=A.databases[e].sqldb,i=0,o=n.length;i<o;i++){var u="INSERT INTO "+t+" (",c=n[i],l=Object.keys(c);u+=l.join(","),u+=") VALUES (",u+=l.map(function(e){return v=c[e],"string"==typeof v&&(v="'"+v+"'"),v}).join(","),u+=")",s.exec(u)}var h=o;return a&&a(h),h};var ge=A.engines.FILESTORAGE=A.engines.FILE=function(){};if(ge.createDatabase=function(e,t,n,r,a){var s=1,i=t[0].value;return A.utils.fileExists(i,function(e){if(e){if(n)return s=0,a&&(s=a(s)),s;throw new Error("Cannot create new database file, because it alreagy exists")}var t={tables:{}};A.utils.saveFile(i,JSON.stringify(t),function(e){a&&(s=a(s))})}),s},ge.dropDatabase=function(e,t,n){var r,a=e.value;return A.utils.fileExists(a,function(e){if(e)r=1,A.utils.deleteFile(a,function(){r=1,n&&(r=n(r))});else{if(!t)throw new Error("Cannot drop database file, because it does not exist");r=0,n&&(r=n(r))}}),r},ge.attachDatabase=function(e,t,n,r,a){var s=1;if(A.databases[t])throw new Error('Unable to attach database as "'+t+'" because it already exists');var i=new A.Database(t||e);return i.engineid="FILESTORAGE",i.filename=n[0].value,D(i.filename,!!a,function(e){try{i.data=JSON.parse(e)}catch(e){throw new Error("Data in FileStorage database are corrupted")}if(i.tables=i.data.tables,!A.options.autocommit&&i.tables)for(var t in i.tables)i.tables[t].data=i.data[t];a&&(s=a(s))}),s},ge.createTable=function(e,t,n,r){var a=A.databases[e],s=a.data[t],i=1;if(s&&!n)throw new Error('Table "'+t+'" alsready exists in the database "'+fsdbid+'"');var o=A.databases[e].tables[t];return a.data.tables[t]={columns:o.columns},a.data[t]=[],ge.updateFile(e),r&&r(i),i},ge.updateFile=function(e){var t=A.databases[e];return t.issaving?void(t.postsave=!0):(t.issaving=!0,t.postsave=!1,void A.utils.saveFile(t.filename,JSON.stringify(t.data),function(){t.issaving=!1,t.postsave&&setTimeout(function(){ge.updateFile(e)},50)}))},ge.dropTable=function(e,t,n,r){var a=1,s=A.databases[e];if(!n&&!s.tables[t])throw new Error('Cannot drop table "'+t+'" in fileStorage, because it does not exist');return delete s.tables[t],delete s.data.tables[t],delete s.data[t],ge.updateFile(e),r&&r(a),a},ge.fromTable=function(e,t,n,r,a){var s=A.databases[e],i=s.data[t];return n&&(i=n(i,r,a)),i},ge.intoTable=function(e,t,n,r,a){var s=A.databases[e],i=n.length,o=s.data[t];return o||(o=[]),s.data[t]=o.concat(n),ge.updateFile(e),a&&a(i),i},ge.loadTableData=function(e,t){var n=A.databases[e];n.tables[t].data=n.data[t]},ge.saveTableData=function(e,t){var n=A.databases[e];n.data[t]=n.tables[t].data,n.tables[t].data=null,ge.updateFile(e)},ge.commit=function(e,t){var n=A.databases[e];if(n.tables)for(var r in n.tables)n.data.tables[r]={columns:n.tables[r].columns},n.data[r]=n.tables[r].data;return ge.updateFile(e),t?t(1):1},ge.begin=ge.commit,ge.rollback=function(e,t){function n(){setTimeout(function(){return a.issaving?n():void A.loadFile(a.filename,!!t,function(n){a.data=n,a.tables={};for(var s in a.data.tables){var i=new A.Table({columns:a.data.tables[s].columns});V(i,a.data.tables[s]),a.tables[s]=i,A.options.autocommit||(a.tables[s].data=a.data[s]),a.tables[s].indexColumns()}delete A.databases[e],A.databases[e]=new A.Database(e),V(A.databases[e],a),A.databases[e].engineid="FILESTORAGE",A.databases[e].filename=a.filename,t&&(r=t(r))})},100)}var r=1,a=A.databases[e];a.dbversion++,n()},R.isBrowser&&!R.isWebWorker){if(A=A||!1,!A)throw new Error("alasql was not found");A.worker=function(){throw new Error("Can find webworker in this enviroment")},"undefined"!=typeof Worker&&(A.worker=function(e,t,n){if(e===!0&&(e=void 0),"undefined"==typeof e)for(var r=document.getElementsByTagName("script"),a=0;a<r.length;a++){if("alasql-worker.js"===r[a].src.substr(-16).toLowerCase()){e=r[a].src.substr(0,r[a].src.length-16)+"alasql.js";break}if("alasql-worker.min.js"===r[a].src.substr(-20).toLowerCase()){e=r[a].src.substr(0,r[a].src.length-20)+"alasql.min.js";break}if("alasql.js"===r[a].src.substr(-9).toLowerCase()){e=r[a].src;break}if("alasql.min.js"===r[a].src.substr(-13).toLowerCase()){e=r[a].src.substr(0,r[a].src.length-13)+"alasql.min.js";break}}if("undefined"==typeof e)throw new Error("Path to alasql.js is not specified");if(e!==!1){var s="importScripts('";s+=e,s+="');self.onmessage = function(event) {alasql(event.data.sql,event.data.params, function(data){postMessage({id:event.data.id, data:data});});}";var i=new Blob([s],{type:"text/plain"});if(A.webworker=new Worker(URL.createObjectURL(i)),A.webworker.onmessage=function(e){var t=e.data.id;A.buffer[t](e.data.data),delete A.buffer[t]},A.webworker.onerror=function(e){throw e},arguments.length>1){var o="REQUIRE "+t.map(function(e){return'"'+e+'"'}).join(",");A(o,[],n)}}else if(e===!1)return void delete A.webworker});var me=me||function(e){"use strict";if(!("undefined"==typeof e||"undefined"!=typeof navigator&&/MSIE [1-9]\./.test(navigator.userAgent))){var t=e.document,n=function(){return e.URL||e.webkitURL||e},r=t.createElementNS("http://www.w3.org/1999/xhtml","a"),a="download"in r,s=function(e){var t=new MouseEvent("click");e.dispatchEvent(t)},i=/constructor/i.test(e.HTMLElement)||e.safari,o=/CriOS\/[\d]+/.test(navigator.userAgent),u=function(t){(e.setImmediate||e.setTimeout)(function(){throw t},0)},c="application/octet-stream",l=4e4,h=function(e){var t=function(){"string"==typeof e?n().revokeObjectURL(e):e.remove()};setTimeout(t,l)},d=function(e,t,n){t=[].concat(t);for(var r=t.length;r--;){var a=e["on"+t[r]];if("function"==typeof a)try{a.call(e,n||e)}catch(e){u(e)}}},f=function(e){return/^\s*(?:text\/\S*|application\/xml|\S*\/\S*\+xml)\s*;.*charset\s*=\s*utf-8/i.test(e.type)?new Blob([String.fromCharCode(65279),e],{type:e.type}):e},p=function(t,u,l){l||(t=f(t));var p,b=this,E=t.type,g=E===c,m=function(){d(b,"writestart progress write writeend".split(" "))},v=function(){if((o||g&&i)&&e.FileReader){var r=new FileReader;return r.onloadend=function(){var t=o?r.result:r.result.replace(/^data:[^;]*;/,"data:attachment/file;"),n=e.open(t,"_blank");n||(e.location.href=t),t=void 0,b.readyState=b.DONE,m()},r.readAsDataURL(t),void(b.readyState=b.INIT)}if(p||(p=n().createObjectURL(t)),g)e.location.href=p;else{var a=e.open(p,"_blank");a||(e.location.href=p)}b.readyState=b.DONE,m(),h(p)};return b.readyState=b.INIT,a?(p=n().createObjectURL(t),void setTimeout(function(){r.href=p,r.download=u,s(r),m(),h(p),b.readyState=b.DONE})):void v()},b=p.prototype,E=function(e,t,n){return new p(e,t||e.name||"download",n)};return"undefined"!=typeof navigator&&navigator.msSaveOrOpenBlob?function(e,t,n){return t=t||e.name||"download",n||(e=f(e)),navigator.msSaveOrOpenBlob(e,t)}:(b.abort=function(){},b.readyState=b.INIT=0,b.WRITING=1,b.DONE=2,b.error=b.onwritestart=b.onprogress=b.onwrite=b.onabort=b.onerror=b.onwriteend=null,E)}}("undefined"!=typeof self&&self||"undefined"!=typeof window&&window||this.content);"undefined"!=typeof module&&module.exports?module.exports.saveAs=me:"undefined"!=typeof define&&null!==define&&null!==define.amd&&define("FileSaver.js",function(){return me}),(R.isCordova||R.isMeteorServer||R.isNode)&&console.warn("It looks like you are using the browser version of AlaSQL. Please use the alasql.fs.js file instead."),A.utils.saveAs=me}return new W("alasql"),A.use("alasql"),A});