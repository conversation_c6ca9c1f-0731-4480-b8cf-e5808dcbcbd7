window.console||(window.console={},window.console.log=function(t){},window.console.dir=function(t){}),Array.indexOf||(Array.prototype.indexOf=function(t){for(var e=0;e<this.length;e++)if(this[e]==t)return e;return-1}),function(){function t(){var t={};return t.FRAMERATE=30,t.MAX_VIRTUAL_PIXELS=3e4,t.init=function(e){t.Definitions={},t.Styles={},t.Animations=[],t.Images=[],t.ctx=e,t.ViewPort=new function(){this.viewPorts=[],this.Clear=function(){this.viewPorts=[]},this.SetCurrent=function(t,e){this.viewPorts.push({width:t,height:e})},this.RemoveCurrent=function(){this.viewPorts.pop()},this.Current=function(){return this.viewPorts[this.viewPorts.length-1]},this.width=function(){return this.Current().width},this.height=function(){return this.Current().height},this.ComputeSize=function(t){return null!=t&&"number"==typeof t?t:"x"==t?this.width():"y"==t?this.height():Math.sqrt(Math.pow(this.width(),2)+Math.pow(this.height(),2))/Math.sqrt(2)}}},t.init(),t.ImagesLoaded=function(){for(var e=0;e<t.Images.length;e++)if(!t.Images[e].loaded)return!1;return!0},t.trim=function(t){return t.replace(/^\s+|\s+$/g,"")},t.compressSpaces=function(t){return t.replace(/[\s\r\t\n]+/gm," ")},t.ajax=function(t){var e;return(e=window.XMLHttpRequest?new XMLHttpRequest:new ActiveXObject("Microsoft.XMLHTTP"))?(e.open("GET",t,!1),e.send(null),e.responseText):null},t.parseXml=function(t){if(window.DOMParser)return(new DOMParser).parseFromString(t,"text/xml");t=t.replace(/<!DOCTYPE svg[^>]*>/,"");var e=new ActiveXObject("Microsoft.XMLDOM");return e.async="false",e.loadXML(t),e},t.Property=function(e,i){this.name=e,this.value=i,this.hasValue=function(){return null!=this.value&&""!=this.value},this.numValue=function(){if(!this.hasValue())return 0;var t=parseFloat(this.value);return(this.value+"").match(/%$/)&&(t/=100),t},this.valueOrDefault=function(t){return this.hasValue()?this.value:t},this.numValueOrDefault=function(t){return this.hasValue()?this.numValue():t};var n=this;this.Color={addOpacity:function(e){var i=n.value;if(null!=e&&""!=e){var s=new RGBColor(n.value);s.ok&&(i="rgba("+s.r+", "+s.g+", "+s.b+", "+e+")")}return new t.Property(n.name,i)}},this.Definition={getDefinition:function(){var e=n.value.replace(/^(url\()?#([^\)]+)\)?$/,"$2");return t.Definitions[e]},isUrl:function(){return 0==n.value.indexOf("url(")},getFillStyle:function(e){var i=this.getDefinition();return null!=i&&i.createGradient?i.createGradient(t.ctx,e):null!=i&&i.createPattern?i.createPattern(t.ctx,e):null}},this.Length={DPI:function(t){return 96},EM:function(e){var i=12,n=new t.Property("fontSize",t.Font.Parse(t.ctx.font).fontSize);return n.hasValue()&&(i=n.Length.toPixels(e)),i},toPixels:function(e){if(!n.hasValue())return 0;var i=n.value+"";return i.match(/em$/)?n.numValue()*this.EM(e):i.match(/ex$/)?n.numValue()*this.EM(e)/2:i.match(/px$/)?n.numValue():i.match(/pt$/)?1.25*n.numValue():i.match(/pc$/)?15*n.numValue():i.match(/cm$/)?n.numValue()*this.DPI(e)/2.54:i.match(/mm$/)?n.numValue()*this.DPI(e)/25.4:i.match(/in$/)?n.numValue()*this.DPI(e):i.match(/%$/)?n.numValue()*t.ViewPort.ComputeSize(e):n.numValue()}},this.Time={toMilliseconds:function(){if(!n.hasValue())return 0;var t=n.value+"";return t.match(/s$/)?1e3*n.numValue():(t.match(/ms$/),n.numValue())}},this.Angle={toRadians:function(){if(!n.hasValue())return 0;var t=n.value+"";return t.match(/deg$/)?n.numValue()*(Math.PI/180):t.match(/grad$/)?n.numValue()*(Math.PI/200):t.match(/rad$/)?n.numValue():n.numValue()*(Math.PI/180)}}},t.Font=new function(){this.Styles=["normal","italic","oblique","inherit"],this.Variants=["normal","small-caps","inherit"],this.Weights=["normal","bold","bolder","lighter","100","200","300","400","500","600","700","800","900","inherit"],this.CreateFont=function(e,i,n,s,a,r){var h=null!=r?this.Parse(r):this.CreateFont("","","","","",t.ctx.font);return{fontFamily:a||h.fontFamily,fontSize:s||h.fontSize,fontStyle:e||h.fontStyle,fontWeight:n||h.fontWeight,fontVariant:i||h.fontVariant,toString:function(){return[this.fontStyle,this.fontVariant,this.fontWeight,this.fontSize,this.fontFamily].join(" ")}}};var e=this;this.Parse=function(i){for(var n={},s=t.trim(t.compressSpaces(i||"")).split(" "),a={fontSize:!1,fontStyle:!1,fontWeight:!1,fontVariant:!1},r="",h=0;h<s.length;h++)a.fontStyle||-1==e.Styles.indexOf(s[h])?a.fontVariant||-1==e.Variants.indexOf(s[h])?a.fontWeight||-1==e.Weights.indexOf(s[h])?a.fontSize?"inherit"!=s[h]&&(r+=s[h]):("inherit"!=s[h]&&(n.fontSize=s[h].split("/")[0]),a.fontStyle=a.fontVariant=a.fontWeight=a.fontSize=!0):("inherit"!=s[h]&&(n.fontWeight=s[h]),a.fontStyle=a.fontVariant=a.fontWeight=!0):("inherit"!=s[h]&&(n.fontVariant=s[h]),a.fontStyle=a.fontVariant=!0):("inherit"!=s[h]&&(n.fontStyle=s[h]),a.fontStyle=!0);return""!=r&&(n.fontFamily=r),n}},t.ToNumberArray=function(e){for(var i=t.trim(t.compressSpaces((e||"").replace(/,/g," "))).split(" "),n=0;n<i.length;n++)i[n]=parseFloat(i[n]);return i},t.Point=function(t,e){this.x=t,this.y=e,this.angleTo=function(t){return Math.atan2(t.y-this.y,t.x-this.x)},this.applyTransform=function(t){var e=this.x*t[0]+this.y*t[2]+t[4],i=this.x*t[1]+this.y*t[3]+t[5];this.x=e,this.y=i}},t.CreatePoint=function(e){var i=t.ToNumberArray(e);return new t.Point(i[0],i[1])},t.CreatePath=function(e){for(var i=t.ToNumberArray(e),n=[],s=0;s<i.length;s+=2)n.push(new t.Point(i[s],i[s+1]));return n},t.BoundingBox=function(t,e,n,s){this.x1=Number.NaN,this.y1=Number.NaN,this.x2=Number.NaN,this.y2=Number.NaN,this.x=function(){return this.x1},this.y=function(){return this.y1},this.width=function(){return this.x2-this.x1},this.height=function(){return this.y2-this.y1},this.addPoint=function(t,e){null!=t&&((isNaN(this.x1)||isNaN(this.x2))&&(this.x1=t,this.x2=t),t<this.x1&&(this.x1=t),t>this.x2&&(this.x2=t)),null!=e&&((isNaN(this.y1)||isNaN(this.y2))&&(this.y1=e,this.y2=e),e<this.y1&&(this.y1=e),e>this.y2&&(this.y2=e))},this.addX=function(t){this.addPoint(t,null)},this.addY=function(t){this.addPoint(null,t)},this.addBoundingBox=function(t){this.addPoint(t.x1,t.y1),this.addPoint(t.x2,t.y2)},this.addQuadraticCurve=function(t,e,i,n,s,a){var r=t+2/3*(i-t),h=e+2/3*(n-e),o=r+1/3*(s-t),l=h+1/3*(a-e);this.addBezierCurve(t,e,r,o,h,l,s,a)},this.addBezierCurve=function(t,e,n,s,a,r,h,o){var l=[t,e],u=[n,s],c=[a,r],m=[h,o];for(this.addPoint(l[0],l[1]),this.addPoint(m[0],m[1]),i=0;i<=1;i++){var d=function(t){return Math.pow(1-t,3)*l[i]+3*Math.pow(1-t,2)*t*u[i]+3*(1-t)*Math.pow(t,2)*c[i]+Math.pow(t,3)*m[i]},f=6*l[i]-12*u[i]+6*c[i],p=-3*l[i]+9*u[i]-9*c[i]+3*m[i],y=3*u[i]-3*l[i];if(0!=p){var g=Math.pow(f,2)-4*y*p;if(!(g<0)){var v=(-f+Math.sqrt(g))/(2*p);0<v&&v<1&&(0==i&&this.addX(d(v)),1==i&&this.addY(d(v)));var x=(-f-Math.sqrt(g))/(2*p);0<x&&x<1&&(0==i&&this.addX(d(x)),1==i&&this.addY(d(x)))}}else{if(0==f)continue;var b=-y/f;0<b&&b<1&&(0==i&&this.addX(d(b)),1==i&&this.addY(d(b)))}}},this.isPointInBox=function(t,e){return this.x1<=t&&t<=this.x2&&this.y1<=e&&e<=this.y2},this.addPoint(t,e),this.addPoint(n,s)},t.Transform=function(e){var i=this;this.Type={},this.Type.translate=function(e){this.p=t.CreatePoint(e),this.apply=function(t){t.translate(this.p.x||0,this.p.y||0)},this.applyToPoint=function(t){t.applyTransform([1,0,0,1,this.p.x||0,this.p.y||0])}},this.Type.rotate=function(e){var i=t.ToNumberArray(e);this.angle=new t.Property("angle",i[0]),this.cx=i[1]||0,this.cy=i[2]||0,this.apply=function(t){t.translate(this.cx,this.cy),t.rotate(this.angle.Angle.toRadians()),t.translate(-this.cx,-this.cy)},this.applyToPoint=function(t){var e=this.angle.Angle.toRadians();t.applyTransform([1,0,0,1,this.p.x||0,this.p.y||0]),t.applyTransform([Math.cos(e),Math.sin(e),-Math.sin(e),Math.cos(e),0,0]),t.applyTransform([1,0,0,1,-this.p.x||0,-this.p.y||0])}},this.Type.scale=function(e){this.p=t.CreatePoint(e),this.apply=function(t){t.scale(this.p.x||1,this.p.y||this.p.x||1)},this.applyToPoint=function(t){t.applyTransform([this.p.x||0,0,0,this.p.y||0,0,0])}},this.Type.matrix=function(e){this.m=t.ToNumberArray(e),this.apply=function(t){t.transform(this.m[0],this.m[1],this.m[2],this.m[3],this.m[4],this.m[5])},this.applyToPoint=function(t){t.applyTransform(this.m)}},this.Type.SkewBase=function(e){this.base=i.Type.matrix,this.base(e),this.angle=new t.Property("angle",e)},this.Type.SkewBase.prototype=new this.Type.matrix,this.Type.skewX=function(t){this.base=i.Type.SkewBase,this.base(t),this.m=[1,0,Math.tan(this.angle.Angle.toRadians()),1,0,0]},this.Type.skewX.prototype=new this.Type.SkewBase,this.Type.skewY=function(t){this.base=i.Type.SkewBase,this.base(t),this.m=[1,Math.tan(this.angle.Angle.toRadians()),0,1,0,0]},this.Type.skewY.prototype=new this.Type.SkewBase,this.transforms=[],this.apply=function(t){for(var e=0;e<this.transforms.length;e++)this.transforms[e].apply(t)},this.applyToPoint=function(t){for(var e=0;e<this.transforms.length;e++)this.transforms[e].applyToPoint(t)};for(var n=t.trim(t.compressSpaces(e)).split(/\s(?=[a-z])/),s=0;s<n.length;s++){var a=n[s].split("(")[0],r=n[s].split("(")[1].replace(")",""),h=new this.Type[a](r);this.transforms.push(h)}},t.AspectRatio=function(e,i,n,s,a,r,h,o,l,u){var c=(i=(i=t.compressSpaces(i)).replace(/^defer\s/,"")).split(" ")[0]||"xMidYMid",m=i.split(" ")[1]||"meet",d=n/s,f=a/r,p=Math.min(d,f),y=Math.max(d,f);"meet"==m&&(s*=p,r*=p),"slice"==m&&(s*=y,r*=y),l=new t.Property("refX",l),u=new t.Property("refY",u),l.hasValue()&&u.hasValue()?e.translate(-p*l.Length.toPixels("x"),-p*u.Length.toPixels("y")):(c.match(/^xMid/)&&("meet"==m&&p==f||"slice"==m&&y==f)&&e.translate(n/2-s/2,0),c.match(/YMid$/)&&("meet"==m&&p==d||"slice"==m&&y==d)&&e.translate(0,a/2-r/2),c.match(/^xMax/)&&("meet"==m&&p==f||"slice"==m&&y==f)&&e.translate(n-s,0),c.match(/YMax$/)&&("meet"==m&&p==d||"slice"==m&&y==d)&&e.translate(0,a-r)),"none"==c?e.scale(d,f):"meet"==m?e.scale(p,p):"slice"==m&&e.scale(y,y),e.translate(null==h?0:-h,null==o?0:-o)},t.Element={},t.Element.ElementBase=function(e){if(this.attributes={},this.styles={},this.children=[],this.attribute=function(e,i){var n=this.attributes[e];return null!=n?n:(n=new t.Property(e,""),1==i&&(this.attributes[e]=n),n)},this.style=function(e,i){var n=this.styles[e];if(null!=n)return n;var s=this.attribute(e);if(null!=s&&s.hasValue())return s;var a=this.parent;if(null!=a){var r=a.style(e);if(null!=r&&r.hasValue())return r}return n=new t.Property(e,""),1==i&&(this.styles[e]=n),n},this.render=function(t){if("none"!=this.attribute("display").value&&"hidden"!=this.attribute("visibility").value){if(t.save(),this.setContext(t),this.attribute("mask").hasValue()){var e=this.attribute("mask").Definition.getDefinition();null!=e&&e.apply(t,this)}else this.renderChildren(t);this.clearContext(t),t.restore()}},this.setContext=function(t){},this.clearContext=function(t){},this.renderChildren=function(t){for(var e=0;e<this.children.length;e++)this.children[e].render(t)},this.addChild=function(e,i){var n=e;i&&(n=t.CreateElement(e)),n.parent=this,this.children.push(n)},null!=e&&1==e.nodeType){for(o=0;o<e.childNodes.length;o++){var i=e.childNodes[o];1==i.nodeType&&this.addChild(i,!0)}for(o=0;o<e.attributes.length;o++){var n=e.attributes[o];this.attributes[n.nodeName]=new t.Property(n.nodeName,n.nodeValue)}if(null!=(h=t.Styles[e.nodeName]))for(var s in h)this.styles[s]=h[s];if(this.attribute("class").hasValue())for(var a=t.compressSpaces(this.attribute("class").value).split(" "),r=0;r<a.length;r++){if(null!=(h=t.Styles["."+a[r]]))for(var s in h)this.styles[s]=h[s];if(null!=(h=t.Styles[e.nodeName+"."+a[r]]))for(var s in h)this.styles[s]=h[s]}if(this.attribute("style").hasValue())for(var h=this.attribute("style").value.split(";"),o=0;o<h.length;o++)if(""!=t.trim(h[o])){var l=h[o].split(":"),s=t.trim(l[0]),u=t.trim(l[1]);this.styles[s]=new t.Property(s,u)}this.attribute("id").hasValue()&&null==t.Definitions[this.attribute("id").value]&&(t.Definitions[this.attribute("id").value]=this)}},t.Element.RenderedElementBase=function(e){this.base=t.Element.ElementBase,this.base(e),this.setContext=function(e){if(this.style("fill").Definition.isUrl())null!=(n=this.style("fill").Definition.getFillStyle(this))&&(e.fillStyle=n);else if(this.style("fill").hasValue()){var i=this.style("fill");this.style("fill-opacity").hasValue()&&(i=i.Color.addOpacity(this.style("fill-opacity").value)),e.fillStyle="none"==i.value?"rgba(0,0,0,0)":i.value}if(this.style("stroke").Definition.isUrl()){var n=this.style("stroke").Definition.getFillStyle(this);null!=n&&(e.strokeStyle=n)}else if(this.style("stroke").hasValue()){var s=this.style("stroke");this.style("stroke-opacity").hasValue()&&(s=s.Color.addOpacity(this.style("stroke-opacity").value)),e.strokeStyle="none"==s.value?"rgba(0,0,0,0)":s.value}if(this.style("stroke-width").hasValue()&&(e.lineWidth=this.style("stroke-width").Length.toPixels()),this.style("stroke-linecap").hasValue()&&(e.lineCap=this.style("stroke-linecap").value),this.style("stroke-linejoin").hasValue()&&(e.lineJoin=this.style("stroke-linejoin").value),this.style("stroke-miterlimit").hasValue()&&(e.miterLimit=this.style("stroke-miterlimit").value),void 0!==e.font&&(e.font=t.Font.CreateFont(this.style("font-style").value,this.style("font-variant").value,this.style("font-weight").value,this.style("font-size").hasValue()?this.style("font-size").Length.toPixels()+"px":"",this.style("font-family").value).toString()),this.attribute("transform").hasValue()&&new t.Transform(this.attribute("transform").value).apply(e),this.attribute("clip-path").hasValue()){var a=this.attribute("clip-path").Definition.getDefinition();null!=a&&a.apply(e)}this.style("opacity").hasValue()&&(e.globalAlpha=this.style("opacity").numValue())}},t.Element.RenderedElementBase.prototype=new t.Element.ElementBase,t.Element.PathElementBase=function(e){this.base=t.Element.RenderedElementBase,this.base(e),this.path=function(e){return null!=e&&e.beginPath(),new t.BoundingBox},this.renderChildren=function(e){this.path(e),t.Mouse.checkPath(this,e),""!=e.fillStyle&&e.fill(),""!=e.strokeStyle&&e.stroke();var i=this.getMarkers();if(null!=i){if(this.attribute("marker-start").Definition.isUrl()&&(n=this.attribute("marker-start").Definition.getDefinition()).render(e,i[0][0],i[0][1]),this.attribute("marker-mid").Definition.isUrl())for(var n=this.attribute("marker-mid").Definition.getDefinition(),s=1;s<i.length-1;s++)n.render(e,i[s][0],i[s][1]);this.attribute("marker-end").Definition.isUrl()&&(n=this.attribute("marker-end").Definition.getDefinition()).render(e,i[i.length-1][0],i[i.length-1][1])}},this.getBoundingBox=function(){return this.path()},this.getMarkers=function(){return null}},t.Element.PathElementBase.prototype=new t.Element.RenderedElementBase,t.Element.svg=function(e){this.base=t.Element.RenderedElementBase,this.base(e),this.baseClearContext=this.clearContext,this.clearContext=function(e){this.baseClearContext(e),t.ViewPort.RemoveCurrent()},this.baseSetContext=this.setContext,this.setContext=function(e){e.strokeStyle="rgba(0,0,0,0)",e.lineCap="butt",e.lineJoin="miter",e.miterLimit=4,this.baseSetContext(e),this.attribute("x").hasValue()&&this.attribute("y").hasValue()&&e.translate(this.attribute("x").Length.toPixels("x"),this.attribute("y").Length.toPixels("y"));var i=t.ViewPort.width(),n=t.ViewPort.height();if(void 0===this.root&&this.attribute("width").hasValue()&&this.attribute("height").hasValue()){i=this.attribute("width").Length.toPixels("x"),n=this.attribute("height").Length.toPixels("y");var s=0,a=0;this.attribute("refX").hasValue()&&this.attribute("refY").hasValue()&&(s=-this.attribute("refX").Length.toPixels("x"),a=-this.attribute("refY").Length.toPixels("y")),e.beginPath(),e.moveTo(s,a),e.lineTo(i,a),e.lineTo(i,n),e.lineTo(s,n),e.closePath(),e.clip()}if(t.ViewPort.SetCurrent(i,n),this.attribute("viewBox").hasValue()){var r=t.ToNumberArray(this.attribute("viewBox").value),h=r[0],o=r[1];i=r[2],n=r[3],t.AspectRatio(e,this.attribute("preserveAspectRatio").value,t.ViewPort.width(),i,t.ViewPort.height(),n,h,o,this.attribute("refX").value,this.attribute("refY").value),t.ViewPort.RemoveCurrent(),t.ViewPort.SetCurrent(r[2],r[3])}}},t.Element.svg.prototype=new t.Element.RenderedElementBase,t.Element.rect=function(e){this.base=t.Element.PathElementBase,this.base(e),this.path=function(e){var i=this.attribute("x").Length.toPixels("x"),n=this.attribute("y").Length.toPixels("y"),s=this.attribute("width").Length.toPixels("x"),a=this.attribute("height").Length.toPixels("y"),r=this.attribute("rx").Length.toPixels("x"),h=this.attribute("ry").Length.toPixels("y");return this.attribute("rx").hasValue()&&!this.attribute("ry").hasValue()&&(h=r),this.attribute("ry").hasValue()&&!this.attribute("rx").hasValue()&&(r=h),null!=e&&(e.beginPath(),e.moveTo(i+r,n),e.lineTo(i+s-r,n),e.quadraticCurveTo(i+s,n,i+s,n+h),e.lineTo(i+s,n+a-h),e.quadraticCurveTo(i+s,n+a,i+s-r,n+a),e.lineTo(i+r,n+a),e.quadraticCurveTo(i,n+a,i,n+a-h),e.lineTo(i,n+h),e.quadraticCurveTo(i,n,i+r,n),e.closePath()),new t.BoundingBox(i,n,i+s,n+a)}},t.Element.rect.prototype=new t.Element.PathElementBase,t.Element.circle=function(e){this.base=t.Element.PathElementBase,this.base(e),this.path=function(e){var i=this.attribute("cx").Length.toPixels("x"),n=this.attribute("cy").Length.toPixels("y"),s=this.attribute("r").Length.toPixels();return null!=e&&(e.beginPath(),e.arc(i,n,s,0,2*Math.PI,!0),e.closePath()),new t.BoundingBox(i-s,n-s,i+s,n+s)}},t.Element.circle.prototype=new t.Element.PathElementBase,t.Element.ellipse=function(e){this.base=t.Element.PathElementBase,this.base(e),this.path=function(e){var i=(Math.sqrt(2)-1)/3*4,n=this.attribute("rx").Length.toPixels("x"),s=this.attribute("ry").Length.toPixels("y"),a=this.attribute("cx").Length.toPixels("x"),r=this.attribute("cy").Length.toPixels("y");return null!=e&&(e.beginPath(),e.moveTo(a,r-s),e.bezierCurveTo(a+i*n,r-s,a+n,r-i*s,a+n,r),e.bezierCurveTo(a+n,r+i*s,a+i*n,r+s,a,r+s),e.bezierCurveTo(a-i*n,r+s,a-n,r+i*s,a-n,r),e.bezierCurveTo(a-n,r-i*s,a-i*n,r-s,a,r-s),e.closePath()),new t.BoundingBox(a-n,r-s,a+n,r+s)}},t.Element.ellipse.prototype=new t.Element.PathElementBase,t.Element.line=function(e){this.base=t.Element.PathElementBase,this.base(e),this.getPoints=function(){return[new t.Point(this.attribute("x1").Length.toPixels("x"),this.attribute("y1").Length.toPixels("y")),new t.Point(this.attribute("x2").Length.toPixels("x"),this.attribute("y2").Length.toPixels("y"))]},this.path=function(e){var i=this.getPoints();return null!=e&&(e.beginPath(),e.moveTo(i[0].x,i[0].y),e.lineTo(i[1].x,i[1].y)),new t.BoundingBox(i[0].x,i[0].y,i[1].x,i[1].y)},this.getMarkers=function(){var t=this.getPoints(),e=t[0].angleTo(t[1]);return[[t[0],e],[t[1],e]]}},t.Element.line.prototype=new t.Element.PathElementBase,t.Element.polyline=function(e){this.base=t.Element.PathElementBase,this.base(e),this.points=t.CreatePath(this.attribute("points").value),this.path=function(e){var i=new t.BoundingBox(this.points[0].x,this.points[0].y);null!=e&&(e.beginPath(),e.moveTo(this.points[0].x,this.points[0].y));for(var n=1;n<this.points.length;n++)i.addPoint(this.points[n].x,this.points[n].y),null!=e&&e.lineTo(this.points[n].x,this.points[n].y);return i},this.getMarkers=function(){for(var t=[],e=0;e<this.points.length-1;e++)t.push([this.points[e],this.points[e].angleTo(this.points[e+1])]);return t.push([this.points[this.points.length-1],t[t.length-1][1]]),t}},t.Element.polyline.prototype=new t.Element.PathElementBase,t.Element.polygon=function(e){this.base=t.Element.polyline,this.base(e),this.basePath=this.path,this.path=function(t){var e=this.basePath(t);return null!=t&&(t.lineTo(this.points[0].x,this.points[0].y),t.closePath()),e}},t.Element.polygon.prototype=new t.Element.polyline,t.Element.path=function(e){this.base=t.Element.PathElementBase,this.base(e);var i=this.attribute("d").value;i=(i=(i=(i=(i=(i=(i=(i=i.replace(/,/gm," ")).replace(/([MmZzLlHhVvCcSsQqTtAa])([MmZzLlHhVvCcSsQqTtAa])/gm,"$1 $2")).replace(/([MmZzLlHhVvCcSsQqTtAa])([MmZzLlHhVvCcSsQqTtAa])/gm,"$1 $2")).replace(/([MmZzLlHhVvCcSsQqTtAa])([^\s])/gm,"$1 $2")).replace(/([^\s])([MmZzLlHhVvCcSsQqTtAa])/gm,"$1 $2")).replace(/([0-9])([+\-])/gm,"$1 $2")).replace(/(\.[0-9]*)(\.)/gm,"$1 $2")).replace(/([Aa](\s+[0-9]+){3})\s+([01])\s*([01])/gm,"$1 $3 $4 "),i=t.compressSpaces(i),i=t.trim(i),this.PathParser=new function(e){this.tokens=e.split(" "),this.reset=function(){this.i=-1,this.command="",this.previousCommand="",this.start=new t.Point(0,0),this.control=new t.Point(0,0),this.current=new t.Point(0,0),this.points=[],this.angles=[]},this.isEnd=function(){return this.i>=this.tokens.length-1},this.isCommandOrEnd=function(){return!!this.isEnd()||null!=this.tokens[this.i+1].match(/[A-Za-z]/)},this.isRelativeCommand=function(){return this.command==this.command.toLowerCase()},this.getToken=function(){return this.i=this.i+1,this.tokens[this.i]},this.getScalar=function(){return parseFloat(this.getToken())},this.nextCommand=function(){this.previousCommand=this.command,this.command=this.getToken()},this.getPoint=function(){var e=new t.Point(this.getScalar(),this.getScalar());return this.makeAbsolute(e)},this.getAsControlPoint=function(){var t=this.getPoint();return this.control=t,t},this.getAsCurrentPoint=function(){var t=this.getPoint();return this.current=t,t},this.getReflectedControlPoint=function(){return"c"!=this.previousCommand.toLowerCase()&&"s"!=this.previousCommand.toLowerCase()?this.current:new t.Point(2*this.current.x-this.control.x,2*this.current.y-this.control.y)},this.makeAbsolute=function(t){return this.isRelativeCommand()&&(t.x=this.current.x+t.x,t.y=this.current.y+t.y),t},this.addMarker=function(t,e){this.addMarkerAngle(t,null==e?null:e.angleTo(t))},this.addMarkerAngle=function(t,e){this.points.push(t),this.angles.push(e)},this.getMarkerPoints=function(){return this.points},this.getMarkerAngles=function(){for(var t=0;t<this.angles.length;t++)if(null==this.angles[t])for(var e=t+1;e<this.angles.length;e++)if(null!=this.angles[e]){this.angles[t]=this.angles[e];break}return this.angles}}(i),this.path=function(e){var i=this.PathParser;i.reset();var n=new t.BoundingBox;for(null!=e&&e.beginPath();!i.isEnd();)switch(i.nextCommand(),i.command.toUpperCase()){case"M":a=i.getAsCurrentPoint();for(i.addMarker(a),n.addPoint(a.x,a.y),null!=e&&e.moveTo(a.x,a.y),i.start=i.current;!i.isCommandOrEnd();){a=i.getAsCurrentPoint();i.addMarker(a),n.addPoint(a.x,a.y),null!=e&&e.lineTo(a.x,a.y)}break;case"L":for(;!i.isCommandOrEnd();){var s=i.current,a=i.getAsCurrentPoint();i.addMarker(a,s),n.addPoint(a.x,a.y),null!=e&&e.lineTo(a.x,a.y)}break;case"H":for(;!i.isCommandOrEnd();){r=new t.Point((i.isRelativeCommand()?i.current.x:0)+i.getScalar(),i.current.y);i.addMarker(r,i.current),i.current=r,n.addPoint(i.current.x,i.current.y),null!=e&&e.lineTo(i.current.x,i.current.y)}break;case"V":for(;!i.isCommandOrEnd();){var r=new t.Point(i.current.x,(i.isRelativeCommand()?i.current.y:0)+i.getScalar());i.addMarker(r,i.current),i.current=r,n.addPoint(i.current.x,i.current.y),null!=e&&e.lineTo(i.current.x,i.current.y)}break;case"C":for(;!i.isCommandOrEnd();){var h=i.current,o=i.getPoint(),l=i.getAsControlPoint(),u=i.getAsCurrentPoint();i.addMarker(u,l),n.addBezierCurve(h.x,h.y,o.x,o.y,l.x,l.y,u.x,u.y),null!=e&&e.bezierCurveTo(o.x,o.y,l.x,l.y,u.x,u.y)}break;case"S":for(;!i.isCommandOrEnd();){var h=i.current,o=i.getReflectedControlPoint(),l=i.getAsControlPoint(),u=i.getAsCurrentPoint();i.addMarker(u,l),n.addBezierCurve(h.x,h.y,o.x,o.y,l.x,l.y,u.x,u.y),null!=e&&e.bezierCurveTo(o.x,o.y,l.x,l.y,u.x,u.y)}break;case"Q":for(;!i.isCommandOrEnd();){var h=i.current,l=i.getAsControlPoint(),u=i.getAsCurrentPoint();i.addMarker(u,l),n.addQuadraticCurve(h.x,h.y,l.x,l.y,u.x,u.y),null!=e&&e.quadraticCurveTo(l.x,l.y,u.x,u.y)}break;case"T":for(;!i.isCommandOrEnd();){var h=i.current,l=i.getReflectedControlPoint();i.control=l;u=i.getAsCurrentPoint();i.addMarker(u,l),n.addQuadraticCurve(h.x,h.y,l.x,l.y,u.x,u.y),null!=e&&e.quadraticCurveTo(l.x,l.y,u.x,u.y)}break;case"A":for(;!i.isCommandOrEnd();){var h=i.current,c=i.getScalar(),m=i.getScalar(),d=i.getScalar()*(Math.PI/180),f=i.getScalar(),p=i.getScalar(),u=i.getAsCurrentPoint(),y=new t.Point(Math.cos(d)*(h.x-u.x)/2+Math.sin(d)*(h.y-u.y)/2,-Math.sin(d)*(h.x-u.x)/2+Math.cos(d)*(h.y-u.y)/2),g=Math.pow(y.x,2)/Math.pow(c,2)+Math.pow(y.y,2)/Math.pow(m,2);g>1&&(c*=Math.sqrt(g),m*=Math.sqrt(g));var v=(f==p?-1:1)*Math.sqrt((Math.pow(c,2)*Math.pow(m,2)-Math.pow(c,2)*Math.pow(y.y,2)-Math.pow(m,2)*Math.pow(y.x,2))/(Math.pow(c,2)*Math.pow(y.y,2)+Math.pow(m,2)*Math.pow(y.x,2)));isNaN(v)&&(v=0);var x=new t.Point(v*c*y.y/m,v*-m*y.x/c),b=new t.Point((h.x+u.x)/2+Math.cos(d)*x.x-Math.sin(d)*x.y,(h.y+u.y)/2+Math.sin(d)*x.x+Math.cos(d)*x.y),E=function(t){return Math.sqrt(Math.pow(t[0],2)+Math.pow(t[1],2))},P=function(t,e){return(t[0]*e[0]+t[1]*e[1])/(E(t)*E(e))},w=function(t,e){return(t[0]*e[1]<t[1]*e[0]?-1:1)*Math.acos(P(t,e))},B=w([1,0],[(y.x-x.x)/c,(y.y-x.y)/m]),C=[(y.x-x.x)/c,(y.y-x.y)/m],T=[(-y.x-x.x)/c,(-y.y-x.y)/m],V=w(C,T);P(C,T)<=-1&&(V=Math.PI),P(C,T)>=1&&(V=0),0==p&&V>0&&(V-=2*Math.PI),1==p&&V<0&&(V+=2*Math.PI);var M=new t.Point(b.x-c*Math.cos((B+V)/2),b.y-m*Math.sin((B+V)/2));if(i.addMarkerAngle(M,(B+V)/2+(0==p?1:-1)*Math.PI/2),i.addMarkerAngle(u,V+(0==p?1:-1)*Math.PI/2),n.addPoint(u.x,u.y),null!=e){var P=c>m?c:m,k=c>m?1:c/m,S=c>m?m/c:1;e.translate(b.x,b.y),e.rotate(d),e.scale(k,S),e.arc(0,0,P,B,B+V,1-p),e.scale(1/k,1/S),e.rotate(-d),e.translate(-b.x,-b.y)}}break;case"Z":null!=e&&e.closePath(),i.current=i.start}return n},this.getMarkers=function(){for(var t=this.PathParser.getMarkerPoints(),e=this.PathParser.getMarkerAngles(),i=[],n=0;n<t.length;n++)i.push([t[n],e[n]]);return i}},t.Element.path.prototype=new t.Element.PathElementBase,t.Element.pattern=function(e){this.base=t.Element.ElementBase,this.base(e),this.createPattern=function(e,i){var n=new t.Element.svg;n.attributes.viewBox=new t.Property("viewBox",this.attribute("viewBox").value),n.attributes.x=new t.Property("x",this.attribute("x").value),n.attributes.y=new t.Property("y",this.attribute("y").value),n.attributes.width=new t.Property("width",this.attribute("width").value),n.attributes.height=new t.Property("height",this.attribute("height").value),n.children=this.children;var s=document.createElement("canvas");return s.width=this.attribute("width").Length.toPixels(),s.height=this.attribute("height").Length.toPixels(),n.render(s.getContext("2d")),e.createPattern(s,"repeat")}},t.Element.pattern.prototype=new t.Element.ElementBase,t.Element.marker=function(e){this.base=t.Element.ElementBase,this.base(e),this.baseRender=this.render,this.render=function(e,i,n){e.translate(i.x,i.y),"auto"==this.attribute("orient").valueOrDefault("auto")&&e.rotate(n),"strokeWidth"==this.attribute("markerUnits").valueOrDefault("strokeWidth")&&e.scale(e.lineWidth,e.lineWidth),e.save();var s=new t.Element.svg;s.attributes.viewBox=new t.Property("viewBox",this.attribute("viewBox").value),s.attributes.refX=new t.Property("refX",this.attribute("refX").value),s.attributes.refY=new t.Property("refY",this.attribute("refY").value),s.attributes.width=new t.Property("width",this.attribute("markerWidth").value),s.attributes.height=new t.Property("height",this.attribute("markerHeight").value),s.attributes.fill=new t.Property("fill",this.attribute("fill").valueOrDefault("black")),s.attributes.stroke=new t.Property("stroke",this.attribute("stroke").valueOrDefault("none")),s.children=this.children,s.render(e),e.restore(),"strokeWidth"==this.attribute("markerUnits").valueOrDefault("strokeWidth")&&e.scale(1/e.lineWidth,1/e.lineWidth),"auto"==this.attribute("orient").valueOrDefault("auto")&&e.rotate(-n),e.translate(-i.x,-i.y)}},t.Element.marker.prototype=new t.Element.ElementBase,t.Element.defs=function(e){this.base=t.Element.ElementBase,this.base(e),this.render=function(t){}},t.Element.defs.prototype=new t.Element.ElementBase,t.Element.GradientBase=function(e){this.base=t.Element.ElementBase,this.base(e),this.gradientUnits=this.attribute("gradientUnits").valueOrDefault("objectBoundingBox"),this.stops=[];for(var i=0;i<this.children.length;i++){var n=this.children[i];this.stops.push(n)}this.getGradient=function(){},this.createGradient=function(e,i){var n=this;this.attribute("xlink:href").hasValue()&&(n=this.attribute("xlink:href").Definition.getDefinition());for(var s=this.getGradient(e,i),a=0;a<n.stops.length;a++)s.addColorStop(n.stops[a].offset,n.stops[a].color);if(this.attribute("gradientTransform").hasValue()){var r=t.ViewPort.viewPorts[0],h=new t.Element.rect;h.attributes.x=new t.Property("x",-t.MAX_VIRTUAL_PIXELS/3),h.attributes.y=new t.Property("y",-t.MAX_VIRTUAL_PIXELS/3),h.attributes.width=new t.Property("width",t.MAX_VIRTUAL_PIXELS),h.attributes.height=new t.Property("height",t.MAX_VIRTUAL_PIXELS);var o=new t.Element.g;o.attributes.transform=new t.Property("transform",this.attribute("gradientTransform").value),o.children=[h];var l=new t.Element.svg;l.attributes.x=new t.Property("x",0),l.attributes.y=new t.Property("y",0),l.attributes.width=new t.Property("width",r.width),l.attributes.height=new t.Property("height",r.height),l.children=[o];var u=document.createElement("canvas");u.width=r.width,u.height=r.height;var c=u.getContext("2d");return c.fillStyle=s,l.render(c),c.createPattern(u,"no-repeat")}return s}},t.Element.GradientBase.prototype=new t.Element.ElementBase,t.Element.linearGradient=function(e){this.base=t.Element.GradientBase,this.base(e),this.getGradient=function(t,e){var i=e.getBoundingBox(),n="objectBoundingBox"==this.gradientUnits?i.x()+i.width()*this.attribute("x1").numValue():this.attribute("x1").Length.toPixels("x"),s="objectBoundingBox"==this.gradientUnits?i.y()+i.height()*this.attribute("y1").numValue():this.attribute("y1").Length.toPixels("y"),a="objectBoundingBox"==this.gradientUnits?i.x()+i.width()*this.attribute("x2").numValue():this.attribute("x2").Length.toPixels("x"),r="objectBoundingBox"==this.gradientUnits?i.y()+i.height()*this.attribute("y2").numValue():this.attribute("y2").Length.toPixels("y");return t.createLinearGradient(n,s,a,r)}},t.Element.linearGradient.prototype=new t.Element.GradientBase,t.Element.radialGradient=function(e){this.base=t.Element.GradientBase,this.base(e),this.getGradient=function(t,e){var i=e.getBoundingBox(),n="objectBoundingBox"==this.gradientUnits?i.x()+i.width()*this.attribute("cx").numValue():this.attribute("cx").Length.toPixels("x"),s="objectBoundingBox"==this.gradientUnits?i.y()+i.height()*this.attribute("cy").numValue():this.attribute("cy").Length.toPixels("y"),a=n,r=s;this.attribute("fx").hasValue()&&(a="objectBoundingBox"==this.gradientUnits?i.x()+i.width()*this.attribute("fx").numValue():this.attribute("fx").Length.toPixels("x")),this.attribute("fy").hasValue()&&(r="objectBoundingBox"==this.gradientUnits?i.y()+i.height()*this.attribute("fy").numValue():this.attribute("fy").Length.toPixels("y"));var h="objectBoundingBox"==this.gradientUnits?(i.width()+i.height())/2*this.attribute("r").numValue():this.attribute("r").Length.toPixels();return t.createRadialGradient(a,r,0,n,s,h)}},t.Element.radialGradient.prototype=new t.Element.GradientBase,t.Element.stop=function(e){this.base=t.Element.ElementBase,this.base(e),this.offset=this.attribute("offset").numValue();var i=this.style("stop-color");this.style("stop-opacity").hasValue()&&(i=i.Color.addOpacity(this.style("stop-opacity").value)),this.color=i.value},t.Element.stop.prototype=new t.Element.ElementBase,t.Element.AnimateBase=function(e){this.base=t.Element.ElementBase,this.base(e),t.Animations.push(this),this.duration=0,this.begin=this.attribute("begin").Time.toMilliseconds(),this.maxDuration=this.begin+this.attribute("dur").Time.toMilliseconds(),this.getProperty=function(){var t=this.attribute("attributeType").value,e=this.attribute("attributeName").value;return"CSS"==t?this.parent.style(e,!0):this.parent.attribute(e,!0)},this.initialValue=null,this.removed=!1,this.calcValue=function(){return""},this.update=function(t){if(null==this.initialValue&&(this.initialValue=this.getProperty().value),this.duration>this.maxDuration){if("indefinite"!=this.attribute("repeatCount").value)return"remove"==this.attribute("fill").valueOrDefault("remove")&&!this.removed&&(this.removed=!0,this.getProperty().value=this.initialValue,!0);this.duration=0}this.duration=this.duration+t;var e=!1;if(this.begin<this.duration){var i=this.calcValue();this.attribute("type").hasValue()&&(i=this.attribute("type").value+"("+i+")"),this.getProperty().value=i,e=!0}return e},this.progress=function(){return(this.duration-this.begin)/(this.maxDuration-this.begin)}},t.Element.AnimateBase.prototype=new t.Element.ElementBase,t.Element.animate=function(e){this.base=t.Element.AnimateBase,this.base(e),this.calcValue=function(){var t=this.attribute("from").numValue();return t+(this.attribute("to").numValue()-t)*this.progress()}},t.Element.animate.prototype=new t.Element.AnimateBase,t.Element.animateColor=function(e){this.base=t.Element.AnimateBase,this.base(e),this.calcValue=function(){var t=new RGBColor(this.attribute("from").value),e=new RGBColor(this.attribute("to").value);if(t.ok&&e.ok){var i=t.r+(e.r-t.r)*this.progress(),n=t.g+(e.g-t.g)*this.progress(),s=t.b+(e.b-t.b)*this.progress();return"rgb("+parseInt(i,10)+","+parseInt(n,10)+","+parseInt(s,10)+")"}return this.attribute("from").value}},t.Element.animateColor.prototype=new t.Element.AnimateBase,t.Element.animateTransform=function(e){this.base=t.Element.animate,this.base(e)},t.Element.animateTransform.prototype=new t.Element.animate,t.Element.font=function(e){this.base=t.Element.ElementBase,this.base(e),this.horizAdvX=this.attribute("horiz-adv-x").numValue(),this.isRTL=!1,this.isArabic=!1,this.fontFace=null,this.missingGlyph=null,this.glyphs=[];for(var i=0;i<this.children.length;i++){var n=this.children[i];"font-face"==n.type?(this.fontFace=n,n.style("font-family").hasValue()&&(t.Definitions[n.style("font-family").value]=this)):"missing-glyph"==n.type?this.missingGlyph=n:"glyph"==n.type&&(""!=n.arabicForm?(this.isRTL=!0,this.isArabic=!0,void 0===this.glyphs[n.unicode]&&(this.glyphs[n.unicode]=[]),this.glyphs[n.unicode][n.arabicForm]=n):this.glyphs[n.unicode]=n)}},t.Element.font.prototype=new t.Element.ElementBase,t.Element.fontface=function(e){this.base=t.Element.ElementBase,this.base(e),this.ascent=this.attribute("ascent").value,this.descent=this.attribute("descent").value,this.unitsPerEm=this.attribute("units-per-em").numValue()},t.Element.fontface.prototype=new t.Element.ElementBase,t.Element.missingglyph=function(e){this.base=t.Element.path,this.base(e),this.horizAdvX=0},t.Element.missingglyph.prototype=new t.Element.path,t.Element.glyph=function(e){this.base=t.Element.path,this.base(e),this.horizAdvX=this.attribute("horiz-adv-x").numValue(),this.unicode=this.attribute("unicode").value,this.arabicForm=this.attribute("arabic-form").value},t.Element.glyph.prototype=new t.Element.path,t.Element.text=function(e){if(this.base=t.Element.RenderedElementBase,this.base(e),null!=e){this.children=[];for(var i=0;i<e.childNodes.length;i++){var n=e.childNodes[i];1==n.nodeType?this.addChild(n,!0):3==n.nodeType&&this.addChild(new t.Element.tspan(n),!1)}}this.baseSetContext=this.setContext,this.setContext=function(t){if(this.baseSetContext(t),this.style("text-anchor").hasValue()){var e=this.style("text-anchor").value;t.textAlign="middle"==e?"center":e}this.attribute("alignment-baseline").hasValue()&&(t.textBaseline=this.attribute("alignment-baseline").value)},this.renderChildren=function(t){for(var e=this.attribute("x").Length.toPixels("x"),i=this.attribute("y").Length.toPixels("y"),n=0;n<this.children.length;n++){var s=this.children[n];s.attribute("x").hasValue()?s.x=s.attribute("x").Length.toPixels("x"):(s.attribute("dx").hasValue()&&(e+=s.attribute("dx").Length.toPixels("x")),s.x=e,e+=s.measureText(t)),s.attribute("y").hasValue()?s.y=s.attribute("y").Length.toPixels("y"):(s.attribute("dy").hasValue()&&(i+=s.attribute("dy").Length.toPixels("y")),s.y=i),s.render(t)}}},t.Element.text.prototype=new t.Element.RenderedElementBase,t.Element.TextElementBase=function(e){this.base=t.Element.RenderedElementBase,this.base(e),this.getGlyph=function(t,e,i){var n=e[i],s=null;if(t.isArabic){var a="isolated";(0==i||" "==e[i-1])&&i<e.length-2&&" "!=e[i+1]&&(a="terminal"),i>0&&" "!=e[i-1]&&i<e.length-2&&" "!=e[i+1]&&(a="medial"),i>0&&" "!=e[i-1]&&(i==e.length-1||" "==e[i+1])&&(a="initial"),void 0!==t.glyphs[n]&&null==(s=t.glyphs[n][a])&&"glyph"==t.glyphs[n].type&&(s=t.glyphs[n])}else s=t.glyphs[n];return null==s&&(s=t.missingGlyph),s},this.renderChildren=function(e){var i=this.parent.style("font-family").Definition.getDefinition();if(null==i)""!=e.strokeStyle&&e.strokeText(t.compressSpaces(this.getText()),this.x,this.y),""!=e.fillStyle&&e.fillText(t.compressSpaces(this.getText()),this.x,this.y);else{var n=this.parent.style("font-size").numValueOrDefault(t.Font.Parse(t.ctx.font).fontSize),s=this.parent.style("font-style").valueOrDefault(t.Font.Parse(t.ctx.font).fontStyle),a=this.getText();i.isRTL&&(a=a.split("").reverse().join("")),"middle"==this.parent.style("text-anchor").value&&(this.x=this.x-this.measureText(e)/2);for(var r=t.ToNumberArray(this.parent.attribute("dx").value),h=0;h<a.length;h++){var o=this.getGlyph(i,a,h),l=n/i.fontFace.unitsPerEm;e.translate(this.x,this.y),e.scale(l,-l);var u=e.lineWidth;e.lineWidth=e.lineWidth*i.fontFace.unitsPerEm/n,"italic"==s&&e.transform(1,0,.4,1,0,0),o.render(e),"italic"==s&&e.transform(1,0,-.4,1,0,0),e.lineWidth=u,e.scale(1/l,-1/l),e.translate(-this.x,-this.y),this.x+=n*(o.horizAdvX||i.horizAdvX)/i.fontFace.unitsPerEm,void 0===r[h]||isNaN(r[h])||(this.x+=r[h])}}},this.getText=function(){},this.measureText=function(e){var i=this.parent.style("font-family").Definition.getDefinition();if(null!=i){var n=this.parent.style("font-size").numValueOrDefault(t.Font.Parse(t.ctx.font).fontSize),s=0,a=this.getText();i.isRTL&&(a=a.split("").reverse().join(""));for(var r=t.ToNumberArray(this.parent.attribute("dx").value),h=0;h<a.length;h++)s+=(this.getGlyph(i,a,h).horizAdvX||i.horizAdvX)*n/i.fontFace.unitsPerEm,void 0===r[h]||isNaN(r[h])||(s+=r[h]);return s}var o=t.compressSpaces(this.getText());return e.measureText?e.measureText(o).width:10*o.length}},t.Element.TextElementBase.prototype=new t.Element.RenderedElementBase,t.Element.tspan=function(e){this.base=t.Element.TextElementBase,this.base(e),this.text=3==e.nodeType?e.nodeValue:e.childNodes.length>0?e.childNodes[0].nodeValue:e.text,this.getText=function(){return this.text}},t.Element.tspan.prototype=new t.Element.TextElementBase,t.Element.tref=function(e){this.base=t.Element.TextElementBase,this.base(e),this.getText=function(){var t=this.attribute("xlink:href").Definition.getDefinition();if(null!=t)return t.children[0].getText()}},t.Element.tref.prototype=new t.Element.TextElementBase,t.Element.a=function(e){this.base=t.Element.TextElementBase,this.base(e),this.hasText=!0;for(var i=0;i<e.childNodes.length;i++)3!=e.childNodes[i].nodeType&&(this.hasText=!1);this.text=this.hasText?e.childNodes[0].nodeValue:"",this.getText=function(){return this.text},this.baseRenderChildren=this.renderChildren,this.renderChildren=function(e){if(this.hasText){this.baseRenderChildren(e);var i=new t.Property("fontSize",t.Font.Parse(t.ctx.font).fontSize);t.Mouse.checkBoundingBox(this,new t.BoundingBox(this.x,this.y-i.Length.toPixels("y"),this.x+this.measureText(e),this.y))}else{var n=new t.Element.g;n.children=this.children,n.parent=this,n.render(e)}},this.onclick=function(){window.open(this.attribute("xlink:href").value)},this.onmousemove=function(){t.ctx.canvas.style.cursor="pointer"}},t.Element.a.prototype=new t.Element.TextElementBase,t.Element.image=function(e){this.base=t.Element.RenderedElementBase,this.base(e),t.Images.push(this),this.img=document.createElement("img"),this.loaded=!1;var i=this;this.img.onload=function(){i.loaded=!0},this.img.src=this.attribute("xlink:href").value,this.renderChildren=function(e){var i=this.attribute("x").Length.toPixels("x"),n=this.attribute("y").Length.toPixels("y"),s=this.attribute("width").Length.toPixels("x"),a=this.attribute("height").Length.toPixels("y");0!=s&&0!=a&&(e.save(),e.translate(i,n),t.AspectRatio(e,this.attribute("preserveAspectRatio").value,s,this.img.width,a,this.img.height,0,0),e.drawImage(this.img,0,0),e.restore())}},t.Element.image.prototype=new t.Element.RenderedElementBase,t.Element.g=function(e){this.base=t.Element.RenderedElementBase,this.base(e),this.getBoundingBox=function(){for(var e=new t.BoundingBox,i=0;i<this.children.length;i++)e.addBoundingBox(this.children[i].getBoundingBox());return e}},t.Element.g.prototype=new t.Element.RenderedElementBase,t.Element.symbol=function(e){this.base=t.Element.RenderedElementBase,this.base(e),this.baseSetContext=this.setContext,this.setContext=function(e){if(this.baseSetContext(e),this.attribute("viewBox").hasValue()){var i=t.ToNumberArray(this.attribute("viewBox").value),n=i[0],s=i[1];width=i[2],height=i[3],t.AspectRatio(e,this.attribute("preserveAspectRatio").value,this.attribute("width").Length.toPixels("x"),width,this.attribute("height").Length.toPixels("y"),height,n,s),t.ViewPort.SetCurrent(i[2],i[3])}}},t.Element.symbol.prototype=new t.Element.RenderedElementBase,t.Element.style=function(e){this.base=t.Element.ElementBase,this.base(e);var i=e.childNodes[0].nodeValue+(e.childNodes.length>1?e.childNodes[1].nodeValue:"");i=i.replace(/(\/\*([^*]|[\r\n]|(\*+([^*\/]|[\r\n])))*\*+\/)|(^[\s]*\/\/.*)/gm,"");for(var n=(i=t.compressSpaces(i)).split("}"),s=0;s<n.length;s++)if(""!=t.trim(n[s]))for(var a=n[s].split("{"),r=a[0].split(","),h=a[1].split(";"),o=0;o<r.length;o++){var l=t.trim(r[o]);if(""!=l){for(var u={},c=0;c<h.length;c++){var m=h[c].indexOf(":"),d=h[c].substr(0,m),f=h[c].substr(m+1,h[c].length-m);null!=d&&null!=f&&(u[t.trim(d)]=new t.Property(t.trim(d),t.trim(f)))}if(t.Styles[l]=u,"@font-face"==l)for(var p=u["font-family"].value.replace(/"/g,""),y=u.src.value.split(","),g=0;g<y.length;g++)if(y[g].indexOf('format("svg")')>0)for(var v=y[g].indexOf("url"),x=y[g].indexOf(")",v),b=y[g].substr(v+5,x-v-6),E=t.parseXml(t.ajax(b)).getElementsByTagName("font"),P=0;P<E.length;P++){var w=t.CreateElement(E[P]);t.Definitions[p]=w}}}},t.Element.style.prototype=new t.Element.ElementBase,t.Element.use=function(e){this.base=t.Element.RenderedElementBase,this.base(e),this.baseSetContext=this.setContext,this.setContext=function(t){this.baseSetContext(t),this.attribute("x").hasValue()&&t.translate(this.attribute("x").Length.toPixels("x"),0),this.attribute("y").hasValue()&&t.translate(0,this.attribute("y").Length.toPixels("y"))},this.getDefinition=function(){var t=this.attribute("xlink:href").Definition.getDefinition();return this.attribute("width").hasValue()&&(t.attribute("width",!0).value=this.attribute("width").value),this.attribute("height").hasValue()&&(t.attribute("height",!0).value=this.attribute("height").value),t},this.path=function(t){var e=this.getDefinition();null!=e&&e.path(t)},this.renderChildren=function(t){var e=this.getDefinition();null!=e&&e.render(t)}},t.Element.use.prototype=new t.Element.RenderedElementBase,t.Element.mask=function(e){this.base=t.Element.ElementBase,this.base(e),this.apply=function(t,e){var i=this.attribute("x").Length.toPixels("x"),n=this.attribute("y").Length.toPixels("y"),s=this.attribute("width").Length.toPixels("x"),a=this.attribute("height").Length.toPixels("y"),r=e.attribute("mask").value;e.attribute("mask").value="";var h=document.createElement("canvas");h.width=i+s,h.height=n+a;var o=h.getContext("2d");this.renderChildren(o);var l=document.createElement("canvas");l.width=i+s,l.height=n+a;var u=l.getContext("2d");e.render(u),u.globalCompositeOperation="destination-in",u.fillStyle=o.createPattern(h,"no-repeat"),u.fillRect(0,0,i+s,n+a),t.fillStyle=u.createPattern(l,"no-repeat"),t.fillRect(0,0,i+s,n+a),e.attribute("mask").value=r},this.render=function(t){}},t.Element.mask.prototype=new t.Element.ElementBase,t.Element.clipPath=function(e){this.base=t.Element.ElementBase,this.base(e),this.apply=function(t){for(var e=0;e<this.children.length;e++)this.children[e].path&&(this.children[e].path(t),t.clip())},this.render=function(t){}},t.Element.clipPath.prototype=new t.Element.ElementBase,t.Element.title=function(t){},t.Element.title.prototype=new t.Element.ElementBase,t.Element.desc=function(t){},t.Element.desc.prototype=new t.Element.ElementBase,t.Element.MISSING=function(t){console.log("ERROR: Element '"+t.nodeName+"' not yet implemented.")},t.Element.MISSING.prototype=new t.Element.ElementBase,t.CreateElement=function(e){var i=e.nodeName.replace(/^[^:]+:/,"");i=i.replace(/\-/g,"");var n=null;return n=void 0!==t.Element[i]?new t.Element[i](e):new t.Element.MISSING(e),n.type=e.nodeName,n},t.load=function(e,i){t.loadXml(e,t.ajax(i))},t.loadXml=function(e,i){t.loadXmlDoc(e,t.parseXml(i))},t.loadXmlDoc=function(e,i){t.init(e);var n=function(t){for(var i=e.canvas;i;)t.x-=i.offsetLeft,t.y-=i.offsetTop,i=i.offsetParent;return window.scrollX&&(t.x+=window.scrollX),window.scrollY&&(t.y+=window.scrollY),t};null!=t.opts&&1==t.opts.ignoreMouse||(e.canvas.onclick=function(e){var i=n(new t.Point(null!=e?e.clientX:event.clientX,null!=e?e.clientY:event.clientY));t.Mouse.onclick(i.x,i.y)},e.canvas.onmousemove=function(e){var i=n(new t.Point(null!=e?e.clientX:event.clientX,null!=e?e.clientY:event.clientY));t.Mouse.onmousemove(i.x,i.y)});var s=t.CreateElement(i.documentElement);s.root=!0;var a=!0,r=function(){t.ViewPort.Clear(),e.canvas.parentNode&&t.ViewPort.SetCurrent(e.canvas.parentNode.clientWidth,e.canvas.parentNode.clientHeight),null!=t.opts&&1==t.opts.ignoreDimensions||(s.style("width").hasValue()&&(e.canvas.width=s.style("width").Length.toPixels("x"),e.canvas.style.width=e.canvas.width+"px"),s.style("height").hasValue()&&(e.canvas.height=s.style("height").Length.toPixels("y"),e.canvas.style.height=e.canvas.height+"px")),t.ViewPort.SetCurrent(e.canvas.clientWidth,e.canvas.clientHeight),null!=t.opts&&null!=t.opts.offsetX&&(s.attribute("x",!0).value=t.opts.offsetX),null!=t.opts&&null!=t.opts.offsetY&&(s.attribute("y",!0).value=t.opts.offsetY),null!=t.opts&&null!=t.opts.scaleWidth&&null!=t.opts.scaleHeight&&(s.attribute("width",!0).value=t.opts.scaleWidth,s.attribute("height",!0).value=t.opts.scaleHeight,s.attribute("viewBox",!0).value="0 0 "+e.canvas.clientWidth+" "+e.canvas.clientHeight,s.attribute("preserveAspectRatio",!0).value="none"),null!=t.opts&&1==t.opts.ignoreClear||e.clearRect(0,0,e.canvas.clientWidth,e.canvas.clientHeight),s.render(e),a&&(a=!1,null!=t.opts&&"function"==typeof t.opts.renderCallback&&t.opts.renderCallback())},h=!0;t.ImagesLoaded()&&(h=!1,r()),t.intervalID=setInterval(function(){var e=!1;if(h&&t.ImagesLoaded()&&(h=!1,e=!0),null!=t.opts&&1==t.opts.ignoreMouse||(e|=t.Mouse.hasEvents()),null==t.opts||1!=t.opts.ignoreAnimation)for(var i=0;i<t.Animations.length;i++)e|=t.Animations[i].update(1e3/t.FRAMERATE);null!=t.opts&&"function"==typeof t.opts.forceRedraw&&1==t.opts.forceRedraw()&&(e=!0),e&&(r(),t.Mouse.runEvents())},1e3/t.FRAMERATE)},t.stop=function(){t.intervalID&&clearInterval(t.intervalID)},t.Mouse=new function(){this.events=[],this.hasEvents=function(){return 0!=this.events.length},this.onclick=function(t,e){this.events.push({type:"onclick",x:t,y:e,run:function(t){t.onclick&&t.onclick()}})},this.onmousemove=function(t,e){this.events.push({type:"onmousemove",x:t,y:e,run:function(t){t.onmousemove&&t.onmousemove()}})},this.eventElements=[],this.checkPath=function(t,e){for(var i=0;i<this.events.length;i++){var n=this.events[i];e.isPointInPath&&e.isPointInPath(n.x,n.y)&&(this.eventElements[i]=t)}},this.checkBoundingBox=function(t,e){for(var i=0;i<this.events.length;i++){var n=this.events[i];e.isPointInBox(n.x,n.y)&&(this.eventElements[i]=t)}},this.runEvents=function(){t.ctx.canvas.style.cursor="";for(var e=0;e<this.events.length;e++)for(var i=this.events[e],n=this.eventElements[e];n;)i.run(n),n=n.parent;this.events=[],this.eventElements=[]}},t}this.canvg=function(e,i,n){if(null!=e||null!=i||null!=n){"string"==typeof e&&(e=document.getElementById(e));var s;null==e.svg?(s=t(),e.svg=s):(s=e.svg).stop(),s.opts=n;var a=e.getContext("2d");void 0!==i.documentElement?s.loadXmlDoc(a,i):"<"==i.substr(0,1)?s.loadXml(a,i):s.load(a,i)}else for(var r=document.getElementsByTagName("svg"),h=0;h<r.length;h++){var o=r[h],l=document.createElement("canvas");l.width=o.clientWidth,l.height=o.clientHeight,o.parentNode.insertBefore(l,o),o.parentNode.removeChild(o);var u=document.createElement("div");u.appendChild(o),canvg(l,u.innerHTML)}}}(),CanvasRenderingContext2D&&(CanvasRenderingContext2D.prototype.drawSvg=function(t,e,i,n,s){canvg(this.canvas,t,{ignoreMouse:!0,ignoreAnimation:!0,ignoreDimensions:!0,ignoreClear:!0,offsetX:e,offsetY:i,scaleWidth:n,scaleHeight:s})});