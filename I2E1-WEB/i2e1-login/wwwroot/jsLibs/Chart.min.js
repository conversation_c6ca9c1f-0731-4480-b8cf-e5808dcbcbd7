/*!
 * Chart.js
 * http://chartjs.org/
 * Version: 2.1.4
 *
 * Copyright 2016 <PERSON>
 * Released under the MIT license
 * https://github.com/chartjs/Chart.js/blob/master/LICENSE.md
 */
(function e(n,t,i){function r(u,e){var s,h,o;if(!t[u]){if(!n[u]){if(s=typeof require=="function"&&require,!e&&s)return s(u,!0);if(f)return f(u,!0);h=new Error("Cannot find module '"+u+"'");throw h.code="MODULE_NOT_FOUND",h;}o=t[u]={exports:{}};n[u][0].call(o.exports,function(t){var i=n[u][1][t];return r(i?i:t)},o,o.exports,e,n,t,i)}return t[u].exports}for(var f=typeof require=="function"&&require,u=0;u<i.length;u++)r(i[u]);return r})({1:[function(){},{}],2:[function(n,t){function u(n){var t;if(n){var f=[0,0,0],e=1,u=n.match(/^#([a-fA-F0-9]{3})$/);if(u)for(u=u[1],t=0;t<f.length;t++)f[t]=parseInt(u[t]+u[t],16);else if(u=n.match(/^#([a-fA-F0-9]{6})$/))for(u=u[1],t=0;t<f.length;t++)f[t]=parseInt(u.slice(t*2,t*2+2),16);else if(u=n.match(/^rgba?\(\s*([+-]?\d+)\s*,\s*([+-]?\d+)\s*,\s*([+-]?\d+)\s*(?:,\s*([+-]?[\d\.]+)\s*)?\)$/)){for(t=0;t<f.length;t++)f[t]=parseInt(u[t+1]);e=parseFloat(u[4])}else if(u=n.match(/^rgba?\(\s*([+-]?[\d\.]+)\%\s*,\s*([+-]?[\d\.]+)\%\s*,\s*([+-]?[\d\.]+)\%\s*(?:,\s*([+-]?[\d\.]+)\s*)?\)$/)){for(t=0;t<f.length;t++)f[t]=Math.round(parseFloat(u[t+1])*2.55);e=parseFloat(u[4])}else if(u=n.match(/(\w+)/)){if(u[1]=="transparent")return[0,0,0,0];if(f=r[u[1]],!f)return}for(t=0;t<f.length;t++)f[t]=i(f[t],0,255);return e=e||e==0?i(e,0,1):1,f[3]=e,f}}function f(n){var r,t;if(n&&(r=/^hsla?\(\s*([+-]?\d+)(?:deg)?\s*,\s*([+-]?[\d\.]+)%\s*,\s*([+-]?[\d\.]+)%\s*(?:,\s*([+-]?[\d\.]+)\s*)?\)/,t=n.match(r),t)){var u=parseFloat(t[4]),f=i(parseInt(t[1]),0,360),e=i(parseFloat(t[2]),0,100),o=i(parseFloat(t[3]),0,100),s=i(isNaN(u)?1:u,0,1);return[f,e,o,s]}}function h(n){var r,t;if(n&&(r=/^hwb\(\s*([+-]?\d+)(?:deg)?\s*,\s*([+-]?[\d\.]+)%\s*,\s*([+-]?[\d\.]+)%\s*(?:,\s*([+-]?[\d\.]+)\s*)?\)/,t=n.match(r),t)){var u=parseFloat(t[4]),f=i(parseInt(t[1]),0,360),e=i(parseFloat(t[2]),0,100),o=i(parseFloat(t[3]),0,100),s=i(isNaN(u)?1:u,0,1);return[f,e,o,s]}}function v(n){var t=u(n);return t&&t.slice(0,3)}function y(n){var t=f(n);return t&&t.slice(0,3)}function p(n){var t=u(n);return t?t[3]:(t=f(n))||(t=h(n))?t[3]:void 0}function w(n){return"#"+e(n[0])+e(n[1])+e(n[2])}function b(n,t){return t<1||n[3]&&n[3]<1?c(n,t):"rgb("+n[0]+", "+n[1]+", "+n[2]+")"}function c(n,t){return t===undefined&&(t=n[3]!==undefined?n[3]:1),"rgba("+n[0]+", "+n[1]+", "+n[2]+", "+t+")"}function k(n,t){if(t<1||n[3]&&n[3]<1)return l(n,t);var i=Math.round(n[0]/255*100),r=Math.round(n[1]/255*100),u=Math.round(n[2]/255*100);return"rgb("+i+"%, "+r+"%, "+u+"%)"}function l(n,t){var i=Math.round(n[0]/255*100),r=Math.round(n[1]/255*100),u=Math.round(n[2]/255*100);return"rgba("+i+"%, "+r+"%, "+u+"%, "+(t||n[3]||1)+")"}function d(n,t){return t<1||n[3]&&n[3]<1?a(n,t):"hsl("+n[0]+", "+n[1]+"%, "+n[2]+"%)"}function a(n,t){return t===undefined&&(t=n[3]!==undefined?n[3]:1),"hsla("+n[0]+", "+n[1]+"%, "+n[2]+"%, "+t+")"}function g(n,t){return t===undefined&&(t=n[3]!==undefined?n[3]:1),"hwb("+n[0]+", "+n[1]+"%, "+n[2]+"%"+(t!==undefined&&t!==1?", "+t:"")+")"}function nt(n){return o[n.slice(0,3)]}function i(n,t,i){return Math.min(Math.max(t,n),i)}function e(n){var t=n.toString(16).toUpperCase();return t.length<2?"0"+t:t}var r=n("color-name"),o,s;t.exports={getRgba:u,getHsla:f,getRgb:v,getHsl:y,getHwb:h,getAlpha:p,hexString:w,rgbString:b,rgbaString:c,percentString:k,percentaString:l,hslString:d,hslaString:a,hwbString:g,keyword:nt};o={};for(s in r)o[r[s]]=s},{"color-name":6}],3:[function(n,t){var u=n("color-convert"),r=n("chartjs-color-string"),i=function(n){if(n instanceof i)return n;if(!(this instanceof i))return new i(n);this.values={rgb:[0,0,0],hsl:[0,0,0],hsv:[0,0,0],hwb:[0,0,0],cmyk:[0,0,0,0],alpha:1};var t;if(typeof n=="string")if(t=r.getRgba(n),t)this.setValues("rgb",t);else if(t=r.getHsla(n))this.setValues("hsl",t);else if(t=r.getHwb(n))this.setValues("hwb",t);else throw new Error('Unable to parse color from string "'+n+'"');else if(typeof n=="object")if(t=n,t.r!==undefined||t.red!==undefined)this.setValues("rgb",t);else if(t.l!==undefined||t.lightness!==undefined)this.setValues("hsl",t);else if(t.v!==undefined||t.value!==undefined)this.setValues("hsv",t);else if(t.w!==undefined||t.whiteness!==undefined)this.setValues("hwb",t);else if(t.c!==undefined||t.cyan!==undefined)this.setValues("cmyk",t);else throw new Error("Unable to parse color from object "+JSON.stringify(n));};i.prototype={rgb:function(){return this.setSpace("rgb",arguments)},hsl:function(){return this.setSpace("hsl",arguments)},hsv:function(){return this.setSpace("hsv",arguments)},hwb:function(){return this.setSpace("hwb",arguments)},cmyk:function(){return this.setSpace("cmyk",arguments)},rgbArray:function(){return this.values.rgb},hslArray:function(){return this.values.hsl},hsvArray:function(){return this.values.hsv},hwbArray:function(){var n=this.values;return n.alpha!==1?n.hwb.concat([n.alpha]):n.hwb},cmykArray:function(){return this.values.cmyk},rgbaArray:function(){var n=this.values;return n.rgb.concat([n.alpha])},hslaArray:function(){var n=this.values;return n.hsl.concat([n.alpha])},alpha:function(n){return n===undefined?this.values.alpha:(this.setValues("alpha",n),this)},red:function(n){return this.setChannel("rgb",0,n)},green:function(n){return this.setChannel("rgb",1,n)},blue:function(n){return this.setChannel("rgb",2,n)},hue:function(n){return n&&(n%=360,n=n<0?360+n:n),this.setChannel("hsl",0,n)},saturation:function(n){return this.setChannel("hsl",1,n)},lightness:function(n){return this.setChannel("hsl",2,n)},saturationv:function(n){return this.setChannel("hsv",1,n)},whiteness:function(n){return this.setChannel("hwb",1,n)},blackness:function(n){return this.setChannel("hwb",2,n)},value:function(n){return this.setChannel("hsv",2,n)},cyan:function(n){return this.setChannel("cmyk",0,n)},magenta:function(n){return this.setChannel("cmyk",1,n)},yellow:function(n){return this.setChannel("cmyk",2,n)},black:function(n){return this.setChannel("cmyk",3,n)},hexString:function(){return r.hexString(this.values.rgb)},rgbString:function(){return r.rgbString(this.values.rgb,this.values.alpha)},rgbaString:function(){return r.rgbaString(this.values.rgb,this.values.alpha)},percentString:function(){return r.percentString(this.values.rgb,this.values.alpha)},hslString:function(){return r.hslString(this.values.hsl,this.values.alpha)},hslaString:function(){return r.hslaString(this.values.hsl,this.values.alpha)},hwbString:function(){return r.hwbString(this.values.hwb,this.values.alpha)},keyword:function(){return r.keyword(this.values.rgb,this.values.alpha)},rgbNumber:function(){var n=this.values.rgb;return n[0]<<16|n[1]<<8|n[2]},luminosity:function(){for(var i,r=this.values.rgb,n=[],t=0;t<r.length;t++)i=r[t]/255,n[t]=i<=.03928?i/12.92:Math.pow((i+.055)/1.055,2.4);return.2126*n[0]+.7152*n[1]+.0722*n[2]},contrast:function(n){var t=this.luminosity(),i=n.luminosity();return t>i?(t+.05)/(i+.05):(i+.05)/(t+.05)},level:function(n){var t=this.contrast(n);return t>=7.1?"AAA":t>=4.5?"AA":""},dark:function(){var n=this.values.rgb,t=(n[0]*299+n[1]*587+n[2]*114)/1e3;return t<128},light:function(){return!this.dark()},negate:function(){for(var t=[],n=0;n<3;n++)t[n]=255-this.values.rgb[n];return this.setValues("rgb",t),this},lighten:function(n){var t=this.values.hsl;return t[2]+=t[2]*n,this.setValues("hsl",t),this},darken:function(n){var t=this.values.hsl;return t[2]-=t[2]*n,this.setValues("hsl",t),this},saturate:function(n){var t=this.values.hsl;return t[1]+=t[1]*n,this.setValues("hsl",t),this},desaturate:function(n){var t=this.values.hsl;return t[1]-=t[1]*n,this.setValues("hsl",t),this},whiten:function(n){var t=this.values.hwb;return t[1]+=t[1]*n,this.setValues("hwb",t),this},blacken:function(n){var t=this.values.hwb;return t[2]+=t[2]*n,this.setValues("hwb",t),this},greyscale:function(){var n=this.values.rgb,t=n[0]*.3+n[1]*.59+n[2]*.11;return this.setValues("rgb",[t,t,t]),this},clearer:function(n){var t=this.values.alpha;return this.setValues("alpha",t-t*n),this},opaquer:function(n){var t=this.values.alpha;return this.setValues("alpha",t+t*n),this},rotate:function(n){var t=this.values.hsl,i=(t[0]+n)%360;return t[0]=i<0?360+i:i,this.setValues("hsl",t),this},mix:function(n,t){var i=this,r=n,e=t===undefined?.5:t,u=2*e-1,o=i.alpha()-r.alpha(),f=((u*o==-1?u:(u+o)/(1+u*o))+1)/2,s=1-f;return this.rgb(f*i.red()+s*r.red(),f*i.green()+s*r.green(),f*i.blue()+s*r.blue()).alpha(i.alpha()*e+r.alpha()*(1-e))},toJSON:function(){return this.rgb()},clone:function(){var f=new i,r=this.values,e=f.values,n,u;for(var t in r)r.hasOwnProperty(t)&&(n=r[t],u={}.toString.call(n),u==="[object Array]"?e[t]=n.slice(0):u==="[object Number]"?e[t]=n:console.error("unexpected color value:",n));return f}};i.prototype.spaces={rgb:["red","green","blue"],hsl:["hue","saturation","lightness"],hsv:["hue","saturation","value"],hwb:["hue","whiteness","blackness"],cmyk:["cyan","magenta","yellow","black"]};i.prototype.maxes={rgb:[255,255,255],hsl:[360,100,100],hsv:[360,100,100],hwb:[360,100,100],cmyk:[100,100,100,100]};i.prototype.getValues=function(n){for(var i=this.values,r={},t=0;t<n.length;t++)r[n.charAt(t)]=i[n][t];return i.alpha!==1&&(r.a=i.alpha),r};i.prototype.setValues=function(n,t){var r=this.values,o=this.spaces,c=this.maxes,f=1,i,s,h,e;if(n==="alpha")f=t;else if(t.length)r[n]=t.slice(0,n.length),f=t[n.length];else if(t[n.charAt(0)]!==undefined){for(i=0;i<n.length;i++)r[n][i]=t[n.charAt(i)];f=t.a}else if(t[o[n][0]]!==undefined){for(s=o[n],i=0;i<n.length;i++)r[n][i]=t[s[i]];f=t.alpha}if(r.alpha=Math.max(0,Math.min(1,f===undefined?r.alpha:f)),n==="alpha")return!1;for(i=0;i<n.length;i++)h=Math.max(0,Math.min(c[n][i],r[n][i])),r[n][i]=Math.round(h);for(e in o)e!==n&&(r[e]=u[n][e](r[n]));return!0};i.prototype.setSpace=function(n,t){var i=t[0];return i===undefined?this.getValues(n):(typeof i=="number"&&(i=Array.prototype.slice.call(t)),this.setValues(n,i),this)};i.prototype.setChannel=function(n,t,i){var r=this.values[n];return i===undefined?r[t]:i===r[t]?this:(r[t]=i,this.setValues(n,r),this)};typeof window!="undefined"&&(window.Color=i);t.exports=i},{"chartjs-color-string":2,"color-convert":5}],4:[function(n,t){function u(n){var u=n[0]/255,f=n[1]/255,e=n[2]/255,r=Math.min(u,f,e),t=Math.max(u,f,e),o=t-r,i,h,s;return t==r?i=0:u==t?i=(f-e)/o:f==t?i=2+(e-u)/o:e==t&&(i=4+(u-f)/o),i=Math.min(i*60,360),i<0&&(i+=360),s=(r+t)/2,h=t==r?0:s<=.5?o/(t+r):o/(2-t-r),[i,h*100,s*100]}function c(n){var r=n[0],u=n[1],f=n[2],o=Math.min(r,u,f),i=Math.max(r,u,f),e=i-o,t,s,h;return s=i==0?0:e/i*100,i==o?t=0:r==i?t=(u-f)/e:u==i?t=2+(f-r)/e:f==i&&(t=4+(r-u)/e),t=Math.min(t*60,360),t<0&&(t+=360),h=i/255*100,[t,s,h]}function f(n){var i=n[0],r=n[1],t=n[2],f=u(n)[0],e=1/255*Math.min(i,Math.min(r,t)),t=1-1/255*Math.max(i,Math.max(r,t));return[f,e*100,t*100]}function e(n){var i=n[0]/255,r=n[1]/255,u=n[2]/255,f,e,o,t;return t=Math.min(1-i,1-r,1-u),f=(1-i-t)/(1-t)||0,e=(1-r-t)/(1-t)||0,o=(1-u-t)/(1-t)||0,[f*100,e*100,o*100,t*100]}function o(n){return nt[JSON.stringify(n)]}function y(n){var t=n[0]/255,i=n[1]/255,r=n[2]/255;t=t>.04045?Math.pow((t+.055)/1.055,2.4):t/12.92;i=i>.04045?Math.pow((i+.055)/1.055,2.4):i/12.92;r=r>.04045?Math.pow((r+.055)/1.055,2.4):r/12.92;var u=t*.4124+i*.3576+r*.1805,f=t*.2126+i*.7152+r*.0722,e=t*.0193+i*.1192+r*.9505;return[u*100,f*100,e*100]}function p(n){var u=y(n),i=u[0],t=u[1],r=u[2],f,e,o;return i/=95.047,t/=100,r/=108.883,i=i>.008856?Math.pow(i,1/3):7.787*i+16/116,t=t>.008856?Math.pow(t,1/3):7.787*t+16/116,r=r>.008856?Math.pow(r,1/3):7.787*r+16/116,f=116*t-16,e=500*(i-t),o=200*(t-r),[f,e,o]}function ft(n){return k(p(n))}function l(n){var h=n[0]/360,o=n[1]/100,i=n[2]/100,r,f,t,s,u,e;if(o==0)return u=i*255,[u,u,u];for(f=i<.5?i*(1+o):i+o-i*o,r=2*i-f,s=[0,0,0],e=0;e<3;e++)t=h+1/3*-(e-1),t<0&&t++,t>1&&t--,u=6*t<1?r+(f-r)*6*t:2*t<1?f:3*t<2?r+(f-r)*(2/3-t)*6:r,s[e]=u*255;return s}function et(n){var f=n[0],i=n[1]/100,t=n[2]/100,r,u;return t===0?[0,0,0]:(t*=2,i*=t<=1?t:2-t,u=(t+i)/2,r=2*i/(t+i),[f,r*100,u*100])}function ot(n){return f(l(n))}function st(n){return e(l(n))}function ht(n){return o(l(n))}function a(n){var r=n[0]/60,u=n[1]/100,t=n[2]/100,s=Math.floor(r)%6,o=r-Math.floor(r),i=255*t*(1-u),f=255*t*(1-u*o),e=255*t*(1-u*(1-o)),t=255*t;switch(s){case 0:return[t,e,i];case 1:return[f,t,i];case 2:return[i,t,e];case 3:return[i,f,t];case 4:return[e,i,t];case 5:return[t,i,f]}}function ct(n){var f=n[0],r=n[1]/100,u=n[2]/100,i,t;return t=(2-r)*u,i=r*u,i/=t<=1?t:2-t,i=i||0,t/=2,[f,i*100,t*100]}function lt(n){return f(a(n))}function at(n){return e(a(n))}function vt(n){return o(a(n))}function s(n){var h=n[0]/360,t=n[1]/100,o=n[2]/100,s=t+o,f,i,e,u;s>1&&(t/=s,o/=s);f=Math.floor(6*h);i=1-o;e=6*h-f;(f&1)!=0&&(e=1-e);u=t+e*(i-t);switch(f){default:case 6:case 0:r=i;g=u;b=t;break;case 1:r=u;g=i;b=t;break;case 2:r=t;g=i;b=u;break;case 3:r=t;g=u;b=i;break;case 4:r=u;g=t;b=i;break;case 5:r=i;g=t;b=u}return[r*255,g*255,b*255]}function yt(n){return u(s(n))}function pt(n){return c(s(n))}function wt(n){return e(s(n))}function bt(n){return o(s(n))}function h(n){var f=n[0]/100,e=n[1]/100,o=n[2]/100,t=n[3]/100,i,r,u;return i=1-Math.min(1,f*(1-t)+t),r=1-Math.min(1,e*(1-t)+t),u=1-Math.min(1,o*(1-t)+t),[i*255,r*255,u*255]}function kt(n){return u(h(n))}function dt(n){return c(h(n))}function gt(n){return f(h(n))}function ni(n){return o(h(n))}function it(n){var u=n[0]/100,f=n[1]/100,e=n[2]/100,t,i,r;return t=u*3.2406+f*-1.5372+e*-.4986,i=u*-.9689+f*1.8758+e*.0415,r=u*.0557+f*-.204+e*1.057,t=t>.0031308?1.055*Math.pow(t,1/2.4)-.055:t=t*12.92,i=i>.0031308?1.055*Math.pow(i,1/2.4)-.055:i=i*12.92,r=r>.0031308?1.055*Math.pow(r,1/2.4)-.055:r=r*12.92,t=Math.min(Math.max(0,t),1),i=Math.min(Math.max(0,i),1),r=Math.min(Math.max(0,r),1),[t*255,i*255,r*255]}function rt(n){var i=n[0],t=n[1],r=n[2],u,f,e;return i/=95.047,t/=100,r/=108.883,i=i>.008856?Math.pow(i,1/3):7.787*i+16/116,t=t>.008856?Math.pow(t,1/3):7.787*t+16/116,r=r>.008856?Math.pow(r,1/3):7.787*r+16/116,u=116*t-16,f=500*(i-t),e=200*(t-r),[u,f,e]}function ti(n){return k(rt(n))}function w(n){var f=n[0],e=n[1],o=n[2],r,i,u,t;return f<=8?(i=f*100/903.3,t=7.787*(i/100)+16/116):(i=100*Math.pow((f+16)/116,3),t=Math.pow(i/100,1/3)),r=r/95.047<=.008856?r=95.047*(e/500+t-16/116)/7.787:95.047*Math.pow(e/500+t,3),u=u/108.883<=.008859?u=108.883*(t-o/200-16/116)/7.787:108.883*Math.pow(t-o/200,3),[r,i,u]}function k(n){var e=n[0],i=n[1],r=n[2],u,t,f;return u=Math.atan2(r,i),t=u*180/Math.PI,t<0&&(t+=360),f=Math.sqrt(i*i+r*r),[e,f,t]}function ut(n){return it(w(n))}function d(n){var f=n[0],i=n[1],e=n[2],r,u,t;return t=e/180*Math.PI,r=i*Math.cos(t),u=i*Math.sin(t),[f,r,u]}function ii(n){return w(d(n))}function ri(n){return ut(d(n))}function i(n){return v[n]}function ui(n){return u(i(n))}function fi(n){return c(i(n))}function ei(n){return f(i(n))}function oi(n){return e(i(n))}function si(n){return p(i(n))}function hi(n){return y(i(n))}var v,nt,tt;t.exports={rgb2hsl:u,rgb2hsv:c,rgb2hwb:f,rgb2cmyk:e,rgb2keyword:o,rgb2xyz:y,rgb2lab:p,rgb2lch:ft,hsl2rgb:l,hsl2hsv:et,hsl2hwb:ot,hsl2cmyk:st,hsl2keyword:ht,hsv2rgb:a,hsv2hsl:ct,hsv2hwb:lt,hsv2cmyk:at,hsv2keyword:vt,hwb2rgb:s,hwb2hsl:yt,hwb2hsv:pt,hwb2cmyk:wt,hwb2keyword:bt,cmyk2rgb:h,cmyk2hsl:kt,cmyk2hsv:dt,cmyk2hwb:gt,cmyk2keyword:ni,keyword2rgb:i,keyword2hsl:ui,keyword2hsv:fi,keyword2hwb:ei,keyword2cmyk:oi,keyword2lab:si,keyword2xyz:hi,xyz2rgb:it,xyz2lab:rt,xyz2lch:ti,lab2xyz:w,lab2rgb:ut,lab2lch:k,lch2lab:d,lch2xyz:ii,lch2rgb:ri};v={aliceblue:[240,248,255],antiquewhite:[250,235,215],aqua:[0,255,255],aquamarine:[127,255,212],azure:[240,255,255],beige:[245,245,220],bisque:[255,228,196],black:[0,0,0],blanchedalmond:[255,235,205],blue:[0,0,255],blueviolet:[138,43,226],brown:[165,42,42],burlywood:[222,184,135],cadetblue:[95,158,160],chartreuse:[127,255,0],chocolate:[210,105,30],coral:[255,127,80],cornflowerblue:[100,149,237],cornsilk:[255,248,220],crimson:[220,20,60],cyan:[0,255,255],darkblue:[0,0,139],darkcyan:[0,139,139],darkgoldenrod:[184,134,11],darkgray:[169,169,169],darkgreen:[0,100,0],darkgrey:[169,169,169],darkkhaki:[189,183,107],darkmagenta:[139,0,139],darkolivegreen:[85,107,47],darkorange:[255,140,0],darkorchid:[153,50,204],darkred:[139,0,0],darksalmon:[233,150,122],darkseagreen:[143,188,143],darkslateblue:[72,61,139],darkslategray:[47,79,79],darkslategrey:[47,79,79],darkturquoise:[0,206,209],darkviolet:[148,0,211],deeppink:[255,20,147],deepskyblue:[0,191,255],dimgray:[105,105,105],dimgrey:[105,105,105],dodgerblue:[30,144,255],firebrick:[178,34,34],floralwhite:[255,250,240],forestgreen:[34,139,34],fuchsia:[255,0,255],gainsboro:[220,220,220],ghostwhite:[248,248,255],gold:[255,215,0],goldenrod:[218,165,32],gray:[128,128,128],green:[0,128,0],greenyellow:[173,255,47],grey:[128,128,128],honeydew:[240,255,240],hotpink:[255,105,180],indianred:[205,92,92],indigo:[75,0,130],ivory:[255,255,240],khaki:[240,230,140],lavender:[230,230,250],lavenderblush:[255,240,245],lawngreen:[124,252,0],lemonchiffon:[255,250,205],lightblue:[173,216,230],lightcoral:[240,128,128],lightcyan:[224,255,255],lightgoldenrodyellow:[250,250,210],lightgray:[211,211,211],lightgreen:[144,238,144],lightgrey:[211,211,211],lightpink:[255,182,193],lightsalmon:[255,160,122],lightseagreen:[32,178,170],lightskyblue:[135,206,250],lightslategray:[119,136,153],lightslategrey:[119,136,153],lightsteelblue:[176,196,222],lightyellow:[255,255,224],lime:[0,255,0],limegreen:[50,205,50],linen:[250,240,230],magenta:[255,0,255],maroon:[128,0,0],mediumaquamarine:[102,205,170],mediumblue:[0,0,205],mediumorchid:[186,85,211],mediumpurple:[147,112,219],mediumseagreen:[60,179,113],mediumslateblue:[123,104,238],mediumspringgreen:[0,250,154],mediumturquoise:[72,209,204],mediumvioletred:[199,21,133],midnightblue:[25,25,112],mintcream:[245,255,250],mistyrose:[255,228,225],moccasin:[255,228,181],navajowhite:[255,222,173],navy:[0,0,128],oldlace:[253,245,230],olive:[128,128,0],olivedrab:[107,142,35],orange:[255,165,0],orangered:[255,69,0],orchid:[218,112,214],palegoldenrod:[238,232,170],palegreen:[152,251,152],paleturquoise:[175,238,238],palevioletred:[219,112,147],papayawhip:[255,239,213],peachpuff:[255,218,185],peru:[205,133,63],pink:[255,192,203],plum:[221,160,221],powderblue:[176,224,230],purple:[128,0,128],rebeccapurple:[102,51,153],red:[255,0,0],rosybrown:[188,143,143],royalblue:[65,105,225],saddlebrown:[139,69,19],salmon:[250,128,114],sandybrown:[244,164,96],seagreen:[46,139,87],seashell:[255,245,238],sienna:[160,82,45],silver:[192,192,192],skyblue:[135,206,235],slateblue:[106,90,205],slategray:[112,128,144],slategrey:[112,128,144],snow:[255,250,250],springgreen:[0,255,127],steelblue:[70,130,180],tan:[210,180,140],teal:[0,128,128],thistle:[216,191,216],tomato:[255,99,71],turquoise:[64,224,208],violet:[238,130,238],wheat:[245,222,179],white:[255,255,255],whitesmoke:[245,245,245],yellow:[255,255,0],yellowgreen:[154,205,50]};nt={};for(tt in v)nt[JSON.stringify(v[tt])]=tt},{}],5:[function(n,t){var f=n("./conversions"),i=function(){return new u},r,u;for(r in f){i[r+"Raw"]=function(n){return function(t){return typeof t=="number"&&(t=Array.prototype.slice.call(arguments)),f[n](t)}}(r);var o=/(\w+)2(\w+)/.exec(r),e=o[1],s=o[2];i[e]=i[e]||{};i[e][s]=i[r]=function(n){return function(t){var i,r;if(typeof t=="number"&&(t=Array.prototype.slice.call(arguments)),i=f[n](t),typeof i=="string"||i===undefined)return i;for(r=0;r<i.length;r++)i[r]=Math.round(i[r]);return i}}(r)}u=function(){this.convs={}};u.prototype.routeSpace=function(n,t){var i=t[0];return i===undefined?this.getValues(n):(typeof i=="number"&&(i=Array.prototype.slice.call(t)),this.setValues(n,i))};u.prototype.setValues=function(n,t){return this.space=n,this.convs={},this.convs[n]=t,this};u.prototype.getValues=function(n){var t=this.convs[n],r,u;return t||(r=this.space,u=this.convs[r],t=i[r][n](u),this.convs[n]=t),t};["rgb","hsl","hsv","cmyk","keyword"].forEach(function(n){u.prototype[n]=function(){return this.routeSpace(n,arguments)}});t.exports=i},{"./conversions":4}],6:[function(n,t){t.exports={aliceblue:[240,248,255],antiquewhite:[250,235,215],aqua:[0,255,255],aquamarine:[127,255,212],azure:[240,255,255],beige:[245,245,220],bisque:[255,228,196],black:[0,0,0],blanchedalmond:[255,235,205],blue:[0,0,255],blueviolet:[138,43,226],brown:[165,42,42],burlywood:[222,184,135],cadetblue:[95,158,160],chartreuse:[127,255,0],chocolate:[210,105,30],coral:[255,127,80],cornflowerblue:[100,149,237],cornsilk:[255,248,220],crimson:[220,20,60],cyan:[0,255,255],darkblue:[0,0,139],darkcyan:[0,139,139],darkgoldenrod:[184,134,11],darkgray:[169,169,169],darkgreen:[0,100,0],darkgrey:[169,169,169],darkkhaki:[189,183,107],darkmagenta:[139,0,139],darkolivegreen:[85,107,47],darkorange:[255,140,0],darkorchid:[153,50,204],darkred:[139,0,0],darksalmon:[233,150,122],darkseagreen:[143,188,143],darkslateblue:[72,61,139],darkslategray:[47,79,79],darkslategrey:[47,79,79],darkturquoise:[0,206,209],darkviolet:[148,0,211],deeppink:[255,20,147],deepskyblue:[0,191,255],dimgray:[105,105,105],dimgrey:[105,105,105],dodgerblue:[30,144,255],firebrick:[178,34,34],floralwhite:[255,250,240],forestgreen:[34,139,34],fuchsia:[255,0,255],gainsboro:[220,220,220],ghostwhite:[248,248,255],gold:[255,215,0],goldenrod:[218,165,32],gray:[128,128,128],green:[0,128,0],greenyellow:[173,255,47],grey:[128,128,128],honeydew:[240,255,240],hotpink:[255,105,180],indianred:[205,92,92],indigo:[75,0,130],ivory:[255,255,240],khaki:[240,230,140],lavender:[230,230,250],lavenderblush:[255,240,245],lawngreen:[124,252,0],lemonchiffon:[255,250,205],lightblue:[173,216,230],lightcoral:[240,128,128],lightcyan:[224,255,255],lightgoldenrodyellow:[250,250,210],lightgray:[211,211,211],lightgreen:[144,238,144],lightgrey:[211,211,211],lightpink:[255,182,193],lightsalmon:[255,160,122],lightseagreen:[32,178,170],lightskyblue:[135,206,250],lightslategray:[119,136,153],lightslategrey:[119,136,153],lightsteelblue:[176,196,222],lightyellow:[255,255,224],lime:[0,255,0],limegreen:[50,205,50],linen:[250,240,230],magenta:[255,0,255],maroon:[128,0,0],mediumaquamarine:[102,205,170],mediumblue:[0,0,205],mediumorchid:[186,85,211],mediumpurple:[147,112,219],mediumseagreen:[60,179,113],mediumslateblue:[123,104,238],mediumspringgreen:[0,250,154],mediumturquoise:[72,209,204],mediumvioletred:[199,21,133],midnightblue:[25,25,112],mintcream:[245,255,250],mistyrose:[255,228,225],moccasin:[255,228,181],navajowhite:[255,222,173],navy:[0,0,128],oldlace:[253,245,230],olive:[128,128,0],olivedrab:[107,142,35],orange:[255,165,0],orangered:[255,69,0],orchid:[218,112,214],palegoldenrod:[238,232,170],palegreen:[152,251,152],paleturquoise:[175,238,238],palevioletred:[219,112,147],papayawhip:[255,239,213],peachpuff:[255,218,185],peru:[205,133,63],pink:[255,192,203],plum:[221,160,221],powderblue:[176,224,230],purple:[128,0,128],rebeccapurple:[102,51,153],red:[255,0,0],rosybrown:[188,143,143],royalblue:[65,105,225],saddlebrown:[139,69,19],salmon:[250,128,114],sandybrown:[244,164,96],seagreen:[46,139,87],seashell:[255,245,238],sienna:[160,82,45],silver:[192,192,192],skyblue:[135,206,235],slateblue:[106,90,205],slategray:[112,128,144],slategrey:[112,128,144],snow:[255,250,250],springgreen:[0,255,127],steelblue:[70,130,180],tan:[210,180,140],teal:[0,128,128],thistle:[216,191,216],tomato:[255,99,71],turquoise:[64,224,208],violet:[238,130,238],wheat:[245,222,179],white:[255,255,255],whitesmoke:[245,245,245],yellow:[255,255,0],yellowgreen:[154,205,50]}},{}],7:[function(n,t){var i=n("./core/core.js")();n("./core/core.helpers")(i);n("./core/core.element")(i);n("./core/core.animation")(i);n("./core/core.controller")(i);n("./core/core.datasetController")(i);n("./core/core.layoutService")(i);n("./core/core.legend")(i);n("./core/core.plugin.js")(i);n("./core/core.scale")(i);n("./core/core.scaleService")(i);n("./core/core.title")(i);n("./core/core.tooltip")(i);n("./elements/element.arc")(i);n("./elements/element.line")(i);n("./elements/element.point")(i);n("./elements/element.rectangle")(i);n("./scales/scale.category")(i);n("./scales/scale.linear")(i);n("./scales/scale.logarithmic")(i);n("./scales/scale.radialLinear")(i);n("./scales/scale.time")(i);n("./controllers/controller.bar")(i);n("./controllers/controller.bubble")(i);n("./controllers/controller.doughnut")(i);n("./controllers/controller.line")(i);n("./controllers/controller.polarArea")(i);n("./controllers/controller.radar")(i);n("./charts/Chart.Bar")(i);n("./charts/Chart.Bubble")(i);n("./charts/Chart.Doughnut")(i);n("./charts/Chart.Line")(i);n("./charts/Chart.PolarArea")(i);n("./charts/Chart.Radar")(i);n("./charts/Chart.Scatter")(i);window.Chart=t.exports=i},{"./charts/Chart.Bar":8,"./charts/Chart.Bubble":9,"./charts/Chart.Doughnut":10,"./charts/Chart.Line":11,"./charts/Chart.PolarArea":12,"./charts/Chart.Radar":13,"./charts/Chart.Scatter":14,"./controllers/controller.bar":15,"./controllers/controller.bubble":16,"./controllers/controller.doughnut":17,"./controllers/controller.line":18,"./controllers/controller.polarArea":19,"./controllers/controller.radar":20,"./core/core.animation":21,"./core/core.controller":22,"./core/core.datasetController":23,"./core/core.element":24,"./core/core.helpers":25,"./core/core.js":26,"./core/core.layoutService":27,"./core/core.legend":28,"./core/core.plugin.js":29,"./core/core.scale":30,"./core/core.scaleService":31,"./core/core.title":32,"./core/core.tooltip":33,"./elements/element.arc":34,"./elements/element.line":35,"./elements/element.point":36,"./elements/element.rectangle":37,"./scales/scale.category":38,"./scales/scale.linear":39,"./scales/scale.logarithmic":40,"./scales/scale.radialLinear":41,"./scales/scale.time":42}],8:[function(n,t){"use strict";t.exports=function(n){n.Bar=function(t,i){return i.type="bar",new n(t,i)}}},{}],9:[function(n,t){"use strict";t.exports=function(n){n.Bubble=function(t,i){return i.type="bubble",new n(t,i)}}},{}],10:[function(n,t){"use strict";t.exports=function(n){n.Doughnut=function(t,i){return i.type="doughnut",new n(t,i)}}},{}],11:[function(n,t){"use strict";t.exports=function(n){n.Line=function(t,i){return i.type="line",new n(t,i)}}},{}],12:[function(n,t){"use strict";t.exports=function(n){n.PolarArea=function(t,i){return i.type="polarArea",new n(t,i)}}},{}],13:[function(n,t){"use strict";t.exports=function(n){n.Radar=function(t,i){return i.options=n.helpers.configMerge({aspectRatio:1},i.options),i.type="radar",new n(t,i)}}},{}],14:[function(n,t){"use strict";t.exports=function(n){var t={hover:{mode:"single"},scales:{xAxes:[{type:"linear",position:"bottom",id:"x-axis-1"}],yAxes:[{type:"linear",position:"left",id:"y-axis-1"}]},tooltips:{callbacks:{title:function(){return""},label:function(n){return"("+n.xLabel+", "+n.yLabel+")"}}}};n.defaults.scatter=t;n.controllers.scatter=n.controllers.line;n.Scatter=function(t,i){return i.type="scatter",new n(t,i)}}},{}],15:[function(n,t){"use strict";t.exports=function(n){var t=n.helpers;n.defaults.bar={hover:{mode:"label"},scales:{xAxes:[{type:"category",categoryPercentage:.8,barPercentage:.9,gridLines:{offsetGridLines:!0}}],yAxes:[{type:"linear"}]}};n.controllers.bar=n.DatasetController.extend({dataElementType:n.elements.Rectangle,initialize:function(t,i){n.DatasetController.prototype.initialize.call(this,t,i);this.getMeta().bar=!0},getBarCount:function(){var n=0;return t.each(this.chart.data.datasets,function(t,i){var r=this.chart.getDatasetMeta(i);r.bar&&this.chart.isDatasetVisible(i)&&++n},this),n},update:function(n){t.each(this.getMeta().data,function(t,i){this.updateElement(t,i,n)},this)},updateElement:function(n,i,r){var o=this.getMeta(),c=this.getScaleForId(o.xAxisID),s=this.getScaleForId(o.yAxisID),h=s.getBasePixel(),f=this.chart.options.elements.rectangle,u=n.custom||{},e=this.getDataset();t.extend(n,{_xScale:c,_yScale:s,_datasetIndex:this.index,_index:i,_model:{x:this.calculateBarX(i,this.index),y:r?h:this.calculateBarY(i,this.index),label:this.chart.data.labels[i],datasetLabel:e.label,base:r?h:this.calculateBarBase(this.index,i),width:this.calculateBarWidth(i),backgroundColor:u.backgroundColor?u.backgroundColor:t.getValueAtIndexOrDefault(e.backgroundColor,i,f.backgroundColor),borderSkipped:u.borderSkipped?u.borderSkipped:f.borderSkipped,borderColor:u.borderColor?u.borderColor:t.getValueAtIndexOrDefault(e.borderColor,i,f.borderColor),borderWidth:u.borderWidth?u.borderWidth:t.getValueAtIndexOrDefault(e.borderWidth,i,f.borderWidth)}});n.pivot()},calculateBarBase:function(n,t){var a=this.getMeta(),u=this.getScaleForId(a.yAxisID),e=0,i,s,h,r,c,l;if(u.options.stacked){var f=this.chart,o=f.data.datasets,v=o[n].data[t];if(v<0)for(i=0;i<n;i++)s=o[i],h=f.getDatasetMeta(i),h.bar&&h.yAxisID===u.id&&f.isDatasetVisible(i)&&(e+=s.data[t]<0?s.data[t]:0);else for(r=0;r<n;r++)c=o[r],l=f.getDatasetMeta(r),l.bar&&l.yAxisID===u.id&&f.isDatasetVisible(r)&&(e+=c.data[t]>0?c.data[t]:0);return u.getPixelForValue(e)}return u.getBasePixel()},getRuler:function(n){var h=this.getMeta(),t=this.getScaleForId(h.xAxisID),u=this.getBarCount(),r,e,o,s;r=t.options.type==="category"?t.getPixelForTick(n+1)-t.getPixelForTick(n):t.width/t.ticks.length;var f=r*t.options.categoryPercentage,c=(r-r*t.options.categoryPercentage)/2,i=f/u;return t.ticks.length!==this.chart.data.labels.length&&(e=t.ticks.length/this.chart.data.labels.length,i=i*e),o=i*t.options.barPercentage,s=i-i*t.options.barPercentage,{datasetCount:u,tickWidth:r,categoryWidth:f,categorySpacing:c,fullBarWidth:i,barWidth:o,barSpacing:s}},calculateBarWidth:function(n){var i=this.getScaleForId(this.getMeta().xAxisID),t=this.getRuler(n);return i.options.stacked?t.categoryWidth:t.barWidth},getBarIndex:function(n){for(var i=0,r,t=0;t<n;++t)r=this.chart.getDatasetMeta(t),r.bar&&this.chart.isDatasetVisible(t)&&++i;return i},calculateBarX:function(n,t){var e=this.getMeta(),u=this.getScaleForId(e.xAxisID),f=this.getBarIndex(t),i=this.getRuler(n),r=u.getPixelForValue(null,n,t,this.chart.isCombo);return(r-=this.chart.isCombo?i.tickWidth/2:0,u.options.stacked)?r+i.categoryWidth/2+i.categorySpacing:r+i.barWidth/2+i.categorySpacing+i.barWidth*f+i.barSpacing/2+i.barSpacing*f},calculateBarY:function(n,t){var h=this.getMeta(),r=this.getScaleForId(h.yAxisID),u=this.getDataset().data[n],e,o,i,f,s;if(r.options.stacked){for(e=0,o=0,i=0;i<t;i++)f=this.chart.data.datasets[i],s=this.chart.getDatasetMeta(i),s.bar&&s.yAxisID===r.id&&this.chart.isDatasetVisible(i)&&(f.data[n]<0?o+=f.data[n]||0:e+=f.data[n]||0);return u<0?r.getPixelForValue(o+u):r.getPixelForValue(e+u)}return r.getPixelForValue(u)},draw:function(n){var i=n||1;t.each(this.getMeta().data,function(n,t){var r=this.getDataset().data[t];r===null||r===undefined||isNaN(r)||n.transition(i).draw()},this)},setHoverStyle:function(n){var u=this.chart.data.datasets[n._datasetIndex],f=n._index,i=n.custom||{},r=n._model;r.backgroundColor=i.hoverBackgroundColor?i.hoverBackgroundColor:t.getValueAtIndexOrDefault(u.hoverBackgroundColor,f,t.getHoverColor(r.backgroundColor));r.borderColor=i.hoverBorderColor?i.hoverBorderColor:t.getValueAtIndexOrDefault(u.hoverBorderColor,f,t.getHoverColor(r.borderColor));r.borderWidth=i.hoverBorderWidth?i.hoverBorderWidth:t.getValueAtIndexOrDefault(u.hoverBorderWidth,f,r.borderWidth)},removeHoverStyle:function(n){var r=this.chart.data.datasets[n._datasetIndex],u=n._index,i=n.custom||{},f=n._model,e=this.chart.options.elements.rectangle;f.backgroundColor=i.backgroundColor?i.backgroundColor:t.getValueAtIndexOrDefault(r.backgroundColor,u,e.backgroundColor);f.borderColor=i.borderColor?i.borderColor:t.getValueAtIndexOrDefault(r.borderColor,u,e.borderColor);f.borderWidth=i.borderWidth?i.borderWidth:t.getValueAtIndexOrDefault(r.borderWidth,u,e.borderWidth)}});n.defaults.horizontalBar={hover:{mode:"label"},scales:{xAxes:[{type:"linear",position:"bottom"}],yAxes:[{position:"left",type:"category",categoryPercentage:.8,barPercentage:.9,gridLines:{offsetGridLines:!0}}]},elements:{rectangle:{borderSkipped:"left"}},tooltips:{callbacks:{title:function(n,t){var i="";return n.length>0&&(n[0].yLabel?i=n[0].yLabel:t.labels.length>0&&n[0].index<t.labels.length&&(i=t.labels[n[0].index])),i},label:function(n,t){var i=t.datasets[n.datasetIndex].label||"";return i+": "+n.xLabel}}}};n.controllers.horizontalBar=n.controllers.bar.extend({updateElement:function(n,i,r){var o=this.getMeta(),s=this.getScaleForId(o.xAxisID),c=this.getScaleForId(o.yAxisID),h=s.getBasePixel(),u=n.custom||{},f=this.getDataset(),e=this.chart.options.elements.rectangle;t.extend(n,{_xScale:s,_yScale:c,_datasetIndex:this.index,_index:i,_model:{x:r?h:this.calculateBarX(i,this.index),y:this.calculateBarY(i,this.index),label:this.chart.data.labels[i],datasetLabel:f.label,base:r?h:this.calculateBarBase(this.index,i),height:this.calculateBarHeight(i),backgroundColor:u.backgroundColor?u.backgroundColor:t.getValueAtIndexOrDefault(f.backgroundColor,i,e.backgroundColor),borderSkipped:u.borderSkipped?u.borderSkipped:e.borderSkipped,borderColor:u.borderColor?u.borderColor:t.getValueAtIndexOrDefault(f.borderColor,i,e.borderColor),borderWidth:u.borderWidth?u.borderWidth:t.getValueAtIndexOrDefault(f.borderWidth,i,e.borderWidth)},draw:function(){function h(n){return c[(o+n)%4]}var t=this._chart.ctx,n=this._view,s=n.height/2,r=n.y-s,u=n.y+s,f=n.base-(n.base-n.x),e=n.borderWidth/2,i;n.borderWidth&&(r+=e,u-=e,f+=e);t.beginPath();t.fillStyle=n.backgroundColor;t.strokeStyle=n.borderColor;t.lineWidth=n.borderWidth;var c=[[n.base,u],[n.base,r],[f,r],[f,u]],o=["bottom","left","top","right"].indexOf(n.borderSkipped,0);for(o===-1&&(o=0),t.moveTo.apply(t,h(0)),i=1;i<4;i++)t.lineTo.apply(t,h(i));t.fill();n.borderWidth&&t.stroke()},inRange:function(n,t){var i=this._view,r=!1;return i&&(r=i.x<i.base?t>=i.y-i.height/2&&t<=i.y+i.height/2&&n>=i.x&&n<=i.base:t>=i.y-i.height/2&&t<=i.y+i.height/2&&n>=i.base&&n<=i.x),r}});n.pivot()},calculateBarBase:function(n,t){var l=this.getMeta(),u=this.getScaleForId(l.xAxisID),f=0,c,i,e,o,r,s,h;if(u.options.stacked){if(c=this.chart.data.datasets[n].data[t],c<0)for(i=0;i<n;i++)e=this.chart.data.datasets[i],o=this.chart.getDatasetMeta(i),o.bar&&o.xAxisID===u.id&&this.chart.isDatasetVisible(i)&&(f+=e.data[t]<0?e.data[t]:0);else for(r=0;r<n;r++)s=this.chart.data.datasets[r],h=this.chart.getDatasetMeta(r),h.bar&&h.xAxisID===u.id&&this.chart.isDatasetVisible(r)&&(f+=s.data[t]>0?s.data[t]:0);return u.getPixelForValue(f)}return u.getBasePixel()},getRuler:function(n){var h=this.getMeta(),t=this.getScaleForId(h.yAxisID),u=this.getBarCount(),r,e,o,s;r=t.options.type==="category"?t.getPixelForTick(n+1)-t.getPixelForTick(n):t.width/t.ticks.length;var f=r*t.options.categoryPercentage,c=(r-r*t.options.categoryPercentage)/2,i=f/u;return t.ticks.length!==this.chart.data.labels.length&&(e=t.ticks.length/this.chart.data.labels.length,i=i*e),o=i*t.options.barPercentage,s=i-i*t.options.barPercentage,{datasetCount:u,tickHeight:r,categoryHeight:f,categorySpacing:c,fullBarHeight:i,barHeight:o,barSpacing:s}},calculateBarHeight:function(n){var i=this.getScaleForId(this.getMeta().yAxisID),t=this.getRuler(n);return i.options.stacked?t.categoryHeight:t.barHeight},calculateBarX:function(n,t){var h=this.getMeta(),r=this.getScaleForId(h.xAxisID),u=this.getDataset().data[n],e,o,i,f,s;if(r.options.stacked){for(e=0,o=0,i=0;i<t;i++)f=this.chart.data.datasets[i],s=this.chart.getDatasetMeta(i),s.bar&&s.xAxisID===r.id&&this.chart.isDatasetVisible(i)&&(f.data[n]<0?o+=f.data[n]||0:e+=f.data[n]||0);return u<0?r.getPixelForValue(o+u):r.getPixelForValue(e+u)}return r.getPixelForValue(u)},calculateBarY:function(n,t){var e=this.getMeta(),u=this.getScaleForId(e.yAxisID),f=this.getBarIndex(t),i=this.getRuler(n),r=u.getPixelForValue(null,n,t,this.chart.isCombo);return(r-=this.chart.isCombo?i.tickHeight/2:0,u.options.stacked)?r+i.categoryHeight/2+i.categorySpacing:r+i.barHeight/2+i.categorySpacing+i.barHeight*f+i.barSpacing/2+i.barSpacing*f}})}},{}],16:[function(n,t){"use strict";t.exports=function(n){var t=n.helpers;n.defaults.bubble={hover:{mode:"single"},scales:{xAxes:[{type:"linear",position:"bottom",id:"x-axis-0"}],yAxes:[{type:"linear",position:"left",id:"y-axis-0"}]},tooltips:{callbacks:{title:function(){return""},label:function(n,t){var r=t.datasets[n.datasetIndex].label||"",i=t.datasets[n.datasetIndex].data[n.index];return r+": ("+i.x+", "+i.y+", "+i.r+")"}}}};n.controllers.bubble=n.DatasetController.extend({dataElementType:n.elements.Point,update:function(n){var i=this.getMeta(),r=i.data;t.each(r,function(t,i){this.updateElement(t,i,n)},this)},updateElement:function(n,i,r){var l=this.getMeta(),s=this.getScaleForId(l.xAxisID),h=this.getScaleForId(l.yAxisID),u=n.custom||{},f=this.getDataset(),c=f.data[i],e=this.chart.options.elements.point,o;t.extend(n,{_xScale:s,_yScale:h,_datasetIndex:this.index,_index:i,_model:{x:r?s.getPixelForDecimal(.5):s.getPixelForValue(c,i,this.index,this.chart.isCombo),y:r?h.getBasePixel():h.getPixelForValue(c,i,this.index),radius:r?0:u.radius?u.radius:this.getRadius(c),backgroundColor:u.backgroundColor?u.backgroundColor:t.getValueAtIndexOrDefault(f.backgroundColor,i,e.backgroundColor),borderColor:u.borderColor?u.borderColor:t.getValueAtIndexOrDefault(f.borderColor,i,e.borderColor),borderWidth:u.borderWidth?u.borderWidth:t.getValueAtIndexOrDefault(f.borderWidth,i,e.borderWidth),hitRadius:u.hitRadius?u.hitRadius:t.getValueAtIndexOrDefault(f.hitRadius,i,e.hitRadius)}});o=n._model;o.skip=u.skip?u.skip:isNaN(o.x)||isNaN(o.y);n.pivot()},getRadius:function(n){return n.r||this.chart.options.elements.point.radius},setHoverStyle:function(n){var u=this.chart.data.datasets[n._datasetIndex],f=n._index,i=n.custom||{},r=n._model;r.radius=i.hoverRadius?i.hoverRadius:t.getValueAtIndexOrDefault(u.hoverRadius,f,this.chart.options.elements.point.hoverRadius)+this.getRadius(this.getDataset().data[n._index]);r.backgroundColor=i.hoverBackgroundColor?i.hoverBackgroundColor:t.getValueAtIndexOrDefault(u.hoverBackgroundColor,f,t.getHoverColor(r.backgroundColor));r.borderColor=i.hoverBorderColor?i.hoverBorderColor:t.getValueAtIndexOrDefault(u.hoverBorderColor,f,t.getHoverColor(r.borderColor));r.borderWidth=i.hoverBorderWidth?i.hoverBorderWidth:t.getValueAtIndexOrDefault(u.hoverBorderWidth,f,r.borderWidth)},removeHoverStyle:function(n){var r=this.chart.data.datasets[n._datasetIndex],f=n._index,i=n.custom||{},u=n._model,e=this.chart.options.elements.point;u.radius=i.radius?i.radius:this.getRadius(r.data[n._index]);u.backgroundColor=i.backgroundColor?i.backgroundColor:t.getValueAtIndexOrDefault(r.backgroundColor,f,e.backgroundColor);u.borderColor=i.borderColor?i.borderColor:t.getValueAtIndexOrDefault(r.borderColor,f,e.borderColor);u.borderWidth=i.borderWidth?i.borderWidth:t.getValueAtIndexOrDefault(r.borderWidth,f,e.borderWidth)}})}},{}],17:[function(n,t){"use strict";t.exports=function(n){var t=n.helpers,i=n.defaults;i.doughnut={animation:{animateRotate:!0,animateScale:!1},aspectRatio:1,hover:{mode:"single"},legendCallback:function(n){var t=[],i;t.push('<ul class="'+n.id+'-legend">');var u=n.data,r=u.datasets,f=u.labels;if(r.length)for(i=0;i<r[0].data.length;++i)t.push('<li><span style="background-color:'+r[0].backgroundColor[i]+'"><\/span>'),f[i]&&t.push(f[i]),t.push("<\/li>");return t.push("<\/ul>"),t.join("")},legend:{labels:{generateLabels:function(n){var i=n.data;return i.labels.length&&i.datasets.length?i.labels.map(function(r,u){var h=n.getDatasetMeta(0),e=i.datasets[0],c=h.data[u],f=c.custom||{},o=t.getValueAtIndexOrDefault,s=n.options.elements.arc,l=f.backgroundColor?f.backgroundColor:o(e.backgroundColor,u,s.backgroundColor),a=f.borderColor?f.borderColor:o(e.borderColor,u,s.borderColor),v=f.borderWidth?f.borderWidth:o(e.borderWidth,u,s.borderWidth);return{text:r,fillStyle:l,strokeStyle:a,lineWidth:v,hidden:isNaN(e.data[u])||h.data[u].hidden,index:u}}):[]}},onClick:function(n,t){for(var f=t.index,r=this.chart,u,i=0,e=(r.data.datasets||[]).length;i<e;++i)u=r.getDatasetMeta(i),u.data[f].hidden=!u.data[f].hidden;r.update()}},cutoutPercentage:50,rotation:Math.PI*-.5,circumference:Math.PI*2,tooltips:{callbacks:{title:function(){return""},label:function(n,t){return t.labels[n.index]+": "+t.datasets[n.datasetIndex].data[n.index]}}}};i.pie=t.clone(i.doughnut);t.extend(i.pie,{cutoutPercentage:0});n.controllers.doughnut=n.controllers.pie=n.DatasetController.extend({dataElementType:n.elements.Arc,linkScales:t.noop,getRingIndex:function(n){for(var i=0,t=0;t<n;++t)this.chart.isDatasetVisible(t)&&++i;return i},update:function(n){var f=this,i=f.chart,h=i.chartArea,c=i.options,p=c.elements.arc,w=h.right-h.left-p.borderWidth,b=h.bottom-h.top-p.borderWidth,k=Math.min(w,b),v={x:0,y:0},d=f.getMeta(),y=c.cutoutPercentage,g=c.circumference,r;if(g<Math.PI*2){r=c.rotation%(Math.PI*2);r+=Math.PI*2*(r>=Math.PI?-1:r<-Math.PI?1:0);var u=r+g,e={x:Math.cos(r),y:Math.sin(r)},o={x:Math.cos(u),y:Math.sin(u)},tt=r<=0&&0<=u||r<=Math.PI*2&&Math.PI*2<=u,it=r<=Math.PI*.5&&Math.PI*.5<=u||r<=Math.PI*2.5&&Math.PI*2.5<=u,rt=r<=-Math.PI&&-Math.PI<=u||r<=Math.PI&&Math.PI<=u,ut=r<=-Math.PI*.5&&-Math.PI*.5<=u||r<=Math.PI*1.5&&Math.PI*1.5<=u,s=y/100,l={x:rt?-1:Math.min(e.x*(e.x<0?1:s),o.x*(o.x<0?1:s)),y:ut?-1:Math.min(e.y*(e.y<0?1:s),o.y*(o.y<0?1:s))},a={x:tt?1:Math.max(e.x*(e.x>0?1:s),o.x*(o.x>0?1:s)),y:it?1:Math.max(e.y*(e.y>0?1:s),o.y*(o.y>0?1:s))},nt={width:(a.x-l.x)*.5,height:(a.y-l.y)*.5};k=Math.min(w/nt.width,b/nt.height);v={x:(a.x+l.x)*-.5,y:(a.y+l.y)*-.5}}i.outerRadius=Math.max(k/2,0);i.innerRadius=Math.max(y?i.outerRadius/100*y:1,0);i.radiusLength=(i.outerRadius-i.innerRadius)/i.getVisibleDatasetCount();i.offsetX=v.x*i.outerRadius;i.offsetY=v.y*i.outerRadius;d.total=f.calculateTotal();f.outerRadius=i.outerRadius-i.radiusLength*f.getRingIndex(f.index);f.innerRadius=f.outerRadius-i.radiusLength;t.each(d.data,function(t,i){f.updateElement(t,i,n)})},updateElement:function(n,i,r){var e=this,h=e.chart,l=h.chartArea,o=h.options,a=o.animation,v=o.elements.arc,y=(l.left+l.right)/2,p=(l.top+l.bottom)/2,w=o.rotation,b=o.rotation,s=e.getDataset(),k=r&&a.animateRotate?0:n.hidden?0:e.calculateCircumference(s.data[i])*(o.circumference/(2*Math.PI)),d=r&&a.animateScale?0:e.innerRadius,g=r&&a.animateScale?0:e.outerRadius,f=n.custom||{},c=t.getValueAtIndexOrDefault,u;t.extend(n,{_datasetIndex:e.index,_index:i,_model:{x:y+h.offsetX,y:p+h.offsetY,startAngle:w,endAngle:b,circumference:k,outerRadius:g,innerRadius:d,label:c(s.label,i,h.data.labels[i])}});u=n._model;u.backgroundColor=f.backgroundColor?f.backgroundColor:c(s.backgroundColor,i,v.backgroundColor);u.hoverBackgroundColor=f.hoverBackgroundColor?f.hoverBackgroundColor:c(s.hoverBackgroundColor,i,v.hoverBackgroundColor);u.borderWidth=f.borderWidth?f.borderWidth:c(s.borderWidth,i,v.borderWidth);u.borderColor=f.borderColor?f.borderColor:c(s.borderColor,i,v.borderColor);r&&a.animateRotate||(u.startAngle=i===0?o.rotation:e.getMeta().data[i-1]._model.endAngle,u.endAngle=u.startAngle+u.circumference);n.pivot()},removeHoverStyle:function(t){n.DatasetController.prototype.removeHoverStyle.call(this,t,this.chart.options.elements.arc)},calculateTotal:function(){var r=this.getDataset(),u=this.getMeta(),i=0,n;return t.each(u.data,function(t,u){n=r.data[u];isNaN(n)||t.hidden||(i+=Math.abs(n))}),i},calculateCircumference:function(n){var t=this.getMeta().total;return t>0&&!isNaN(n)?Math.PI*2*(n/t):0}})}},{}],18:[function(n,t){"use strict";t.exports=function(n){var t=n.helpers;n.defaults.line={showLines:!0,hover:{mode:"label"},scales:{xAxes:[{type:"category",id:"x-axis-0"}],yAxes:[{type:"linear",id:"y-axis-0"}]}};n.controllers.line=n.DatasetController.extend({datasetElementType:n.elements.Line,dataElementType:n.elements.Point,addElementAndReset:function(t){var i=this,r=i.chart.options;n.DatasetController.prototype.addElementAndReset.call(i,t);r.showLines&&r.elements.line.tension!==0&&i.updateBezierControlPoints()},update:function(n){var f=this,h=f.getMeta(),e=h.dataset,c=h.data||[],l=f.chart.options,u=l.elements.line,s=f.getScaleForId(h.yAxisID),o,a,r,i;for(l.showLines&&(r=f.getDataset(),i=e.custom||{},r.tension!==undefined&&r.lineTension===undefined&&(r.lineTension=r.tension),e._scale=s,e._datasetIndex=f.index,e._children=c,e._model={tension:i.tension?i.tension:t.getValueOrDefault(r.lineTension,u.tension),backgroundColor:i.backgroundColor?i.backgroundColor:r.backgroundColor||u.backgroundColor,borderWidth:i.borderWidth?i.borderWidth:r.borderWidth||u.borderWidth,borderColor:i.borderColor?i.borderColor:r.borderColor||u.borderColor,borderCapStyle:i.borderCapStyle?i.borderCapStyle:r.borderCapStyle||u.borderCapStyle,borderDash:i.borderDash?i.borderDash:r.borderDash||u.borderDash,borderDashOffset:i.borderDashOffset?i.borderDashOffset:r.borderDashOffset||u.borderDashOffset,borderJoinStyle:i.borderJoinStyle?i.borderJoinStyle:r.borderJoinStyle||u.borderJoinStyle,fill:i.fill?i.fill:r.fill!==undefined?r.fill:u.fill,scaleTop:s.top,scaleBottom:s.bottom,scaleZero:s.getBasePixel()},e.pivot()),o=0,a=c.length;o<a;++o)f.updateElement(c[o],o,n);l.showLines&&u.tension!==0&&f.updateBezierControlPoints()},getPointBackgroundColor:function(n,i){var r=this.chart.options.elements.point.backgroundColor,u=this.getDataset(),f=n.custom||{};return f.backgroundColor?r=f.backgroundColor:u.pointBackgroundColor?r=t.getValueAtIndexOrDefault(u.pointBackgroundColor,i,r):u.backgroundColor&&(r=u.backgroundColor),r},getPointBorderColor:function(n,i){var r=this.chart.options.elements.point.borderColor,u=this.getDataset(),f=n.custom||{};return f.borderColor?r=f.borderColor:u.pointBorderColor?r=t.getValueAtIndexOrDefault(u.pointBorderColor,i,r):u.borderColor&&(r=u.borderColor),r},getPointBorderWidth:function(n,i){var r=this.chart.options.elements.point.borderWidth,u=this.getDataset(),f=n.custom||{};return f.borderWidth?r=f.borderWidth:u.pointBorderWidth?r=t.getValueAtIndexOrDefault(u.pointBorderWidth,i,r):u.borderWidth&&(r=u.borderWidth),r},updateElement:function(n,i,r){var u=this,e=u.getMeta(),o=n.custom||{},f=u.getDataset(),s=u.index,a=f.data[i],v=u.getScaleForId(e.yAxisID),y=u.getScaleForId(e.xAxisID),h=u.chart.options.elements.point,c,l;f.radius!==undefined&&f.pointRadius===undefined&&(f.pointRadius=f.radius);f.hitRadius!==undefined&&f.pointHitRadius===undefined&&(f.pointHitRadius=f.hitRadius);c=y.getPixelForValue(a,i,s,u.chart.isCombo);l=r?v.getBasePixel():u.calculatePointY(a,i,s,u.chart.isCombo);n._xScale=y;n._yScale=v;n._datasetIndex=s;n._index=i;n._model={x:c,y:l,skip:o.skip||isNaN(c)||isNaN(l),radius:o.radius||t.getValueAtIndexOrDefault(f.pointRadius,i,h.radius),pointStyle:o.pointStyle||t.getValueAtIndexOrDefault(f.pointStyle,i,h.pointStyle),backgroundColor:u.getPointBackgroundColor(n,i),borderColor:u.getPointBorderColor(n,i),borderWidth:u.getPointBorderWidth(n,i),tension:e.dataset._model?e.dataset._model.tension:0,hitRadius:o.hitRadius||t.getValueAtIndexOrDefault(f.pointHitRadius,i,h.hitRadius)}},calculatePointY:function(n,t,i){var e=this,o=e.chart,l=e.getMeta(),u=e.getScaleForId(l.yAxisID),s=0,h=0,r,f,c;if(u.options.stacked){for(r=0;r<i;r++)f=o.data.datasets[r],c=o.getDatasetMeta(r),c.type==="line"&&o.isDatasetVisible(r)&&(f.data[t]<0?h+=f.data[t]||0:s+=f.data[t]||0);return n<0?u.getPixelForValue(h+n):u.getPixelForValue(s+n)}return u.getPixelForValue(n)},updateBezierControlPoints:function(){for(var o=this.getMeta(),n=this.chart.chartArea,f=o.data||[],e,r,u,i=0,s=f.length;i<s;++i)e=f[i],r=e._model,u=t.splineCurve(t.previousItem(f,i)._model,r,t.nextItem(f,i)._model,o.dataset._model.tension),r.controlPointPreviousX=Math.max(Math.min(u.previous.x,n.right),n.left),r.controlPointPreviousY=Math.max(Math.min(u.previous.y,n.bottom),n.top),r.controlPointNextX=Math.max(Math.min(u.next.x,n.right),n.left),r.controlPointNextY=Math.max(Math.min(u.next.y,n.bottom),n.top),e.pivot()},draw:function(n){for(var u=this.getMeta(),i=u.data||[],f=n||1,t=0,r=i.length;t<r;++t)i[t].transition(f);for(this.chart.options.showLines&&u.dataset.transition(f).draw(),t=0,r=i.length;t<r;++t)i[t].draw()},setHoverStyle:function(n){var r=this.chart.data.datasets[n._datasetIndex],u=n._index,f=n.custom||{},i=n._model;i.radius=f.hoverRadius||t.getValueAtIndexOrDefault(r.pointHoverRadius,u,this.chart.options.elements.point.hoverRadius);i.backgroundColor=f.hoverBackgroundColor||t.getValueAtIndexOrDefault(r.pointHoverBackgroundColor,u,t.getHoverColor(i.backgroundColor));i.borderColor=f.hoverBorderColor||t.getValueAtIndexOrDefault(r.pointHoverBorderColor,u,t.getHoverColor(i.borderColor));i.borderWidth=f.hoverBorderWidth||t.getValueAtIndexOrDefault(r.pointHoverBorderWidth,u,i.borderWidth)},removeHoverStyle:function(n){var i=this,r=i.chart.data.datasets[n._datasetIndex],u=n._index,e=n.custom||{},f=n._model;r.radius!==undefined&&r.pointRadius===undefined&&(r.pointRadius=r.radius);f.radius=e.radius||t.getValueAtIndexOrDefault(r.pointRadius,u,i.chart.options.elements.point.radius);f.backgroundColor=i.getPointBackgroundColor(n,u);f.borderColor=i.getPointBorderColor(n,u);f.borderWidth=i.getPointBorderWidth(n,u)}})}},{}],19:[function(n,t){"use strict";t.exports=function(n){var t=n.helpers;n.defaults.polarArea={scale:{type:"radialLinear",lineArc:!0},animation:{animateRotate:!0,animateScale:!0},aspectRatio:1,legendCallback:function(n){var t=[],i;t.push('<ul class="'+n.id+'-legend">');var u=n.data,r=u.datasets,f=u.labels;if(r.length)for(i=0;i<r[0].data.length;++i)t.push('<li><span style="background-color:'+r[0].backgroundColor[i]+'">'),f[i]&&t.push(f[i]),t.push("<\/span><\/li>");return t.push("<\/ul>"),t.join("")},legend:{labels:{generateLabels:function(n){var i=n.data;return i.labels.length&&i.datasets.length?i.labels.map(function(r,u){var h=n.getDatasetMeta(0),e=i.datasets[0],c=h.data[u],f=c.custom||{},o=t.getValueAtIndexOrDefault,s=n.options.elements.arc,l=f.backgroundColor?f.backgroundColor:o(e.backgroundColor,u,s.backgroundColor),a=f.borderColor?f.borderColor:o(e.borderColor,u,s.borderColor),v=f.borderWidth?f.borderWidth:o(e.borderWidth,u,s.borderWidth);return{text:r,fillStyle:l,strokeStyle:a,lineWidth:v,hidden:isNaN(e.data[u])||h.data[u].hidden,index:u}}):[]}},onClick:function(n,t){for(var f=t.index,r=this.chart,u,i=0,e=(r.data.datasets||[]).length;i<e;++i)u=r.getDatasetMeta(i),u.data[f].hidden=!u.data[f].hidden;r.update()}},tooltips:{callbacks:{title:function(){return""},label:function(n,t){return t.labels[n.index]+": "+n.yLabel}}}};n.controllers.polarArea=n.DatasetController.extend({dataElementType:n.elements.Arc,linkScales:t.noop,update:function(n){var r=this,i=r.chart,u=i.chartArea,e=this.getMeta(),f=i.options,o=f.elements.arc,s=Math.min(u.right-u.left,u.bottom-u.top);i.outerRadius=Math.max((s-o.borderWidth/2)/2,0);i.innerRadius=Math.max(f.cutoutPercentage?i.outerRadius/100*f.cutoutPercentage:1,0);i.radiusLength=(i.outerRadius-i.innerRadius)/i.getVisibleDatasetCount();r.outerRadius=i.outerRadius-i.radiusLength*r.index;r.innerRadius=r.outerRadius-i.radiusLength;e.count=r.countVisibleElements();t.each(e.data,function(t,i){r.updateElement(t,i,n)})},updateElement:function(n,i,r){for(var s=this,h=s.chart,c=h.chartArea,f=s.getDataset(),w=h.options,v=w.animation,o=w.elements.arc,u=n.custom||{},y=h.scale,e=t.getValueAtIndexOrDefault,l=h.data.labels,b=s.calculateCircumference(f.data[i]),k=(c.left+c.right)/2,d=(c.top+c.bottom)/2,g=0,tt=s.getMeta(),a=0;a<i;++a)isNaN(f.data[a])||tt.data[a].hidden||++g;var it=n.hidden?0:y.getDistanceFromCenterForValue(f.data[i]),p=-.5*Math.PI+b*g,nt=p+(n.hidden?0:b),rt={x:k,y:d,innerRadius:0,outerRadius:v.animateScale?0:y.getDistanceFromCenterForValue(f.data[i]),startAngle:v.animateRotate?Math.PI*-.5:p,endAngle:v.animateRotate?Math.PI*-.5:nt,backgroundColor:u.backgroundColor?u.backgroundColor:e(f.backgroundColor,i,o.backgroundColor),borderWidth:u.borderWidth?u.borderWidth:e(f.borderWidth,i,o.borderWidth),borderColor:u.borderColor?u.borderColor:e(f.borderColor,i,o.borderColor),label:e(l,i,l[i])};t.extend(n,{_datasetIndex:s.index,_index:i,_scale:y,_model:r?rt:{x:k,y:d,innerRadius:0,outerRadius:it,startAngle:p,endAngle:nt,backgroundColor:u.backgroundColor?u.backgroundColor:e(f.backgroundColor,i,o.backgroundColor),borderWidth:u.borderWidth?u.borderWidth:e(f.borderWidth,i,o.borderWidth),borderColor:u.borderColor?u.borderColor:e(f.borderColor,i,o.borderColor),label:e(l,i,l[i])}});n.pivot()},removeHoverStyle:function(t){n.DatasetController.prototype.removeHoverStyle.call(this,t,this.chart.options.elements.arc)},countVisibleElements:function(){var i=this.getDataset(),r=this.getMeta(),n=0;return t.each(r.data,function(t,r){isNaN(i.data[r])||t.hidden||n++}),n},calculateCircumference:function(n){var t=this.getMeta().count;return t>0&&!isNaN(n)?2*Math.PI/t:0}})}},{}],20:[function(n,t){"use strict";t.exports=function(n){var t=n.helpers;n.defaults.radar={scale:{type:"radialLinear"},elements:{line:{tension:0}}};n.controllers.radar=n.DatasetController.extend({datasetElementType:n.elements.Line,dataElementType:n.elements.Point,linkScales:t.noop,addElementAndReset:function(t){n.DatasetController.prototype.addElementAndReset.call(this,t);this.updateBezierControlPoints()},update:function(n){var f=this.getMeta(),s=f.dataset,o=f.data,i=s.custom||{},r=this.getDataset(),u=this.chart.options.elements.line,e=this.chart.scale;r.tension!==undefined&&r.lineTension===undefined&&(r.lineTension=r.tension);t.extend(f.dataset,{_datasetIndex:this.index,_children:o,_loop:!0,_model:{tension:i.tension?i.tension:t.getValueOrDefault(r.lineTension,u.tension),backgroundColor:i.backgroundColor?i.backgroundColor:r.backgroundColor||u.backgroundColor,borderWidth:i.borderWidth?i.borderWidth:r.borderWidth||u.borderWidth,borderColor:i.borderColor?i.borderColor:r.borderColor||u.borderColor,fill:i.fill?i.fill:r.fill!==undefined?r.fill:u.fill,borderCapStyle:i.borderCapStyle?i.borderCapStyle:r.borderCapStyle||u.borderCapStyle,borderDash:i.borderDash?i.borderDash:r.borderDash||u.borderDash,borderDashOffset:i.borderDashOffset?i.borderDashOffset:r.borderDashOffset||u.borderDashOffset,borderJoinStyle:i.borderJoinStyle?i.borderJoinStyle:r.borderJoinStyle||u.borderJoinStyle,scaleTop:e.top,scaleBottom:e.bottom,scaleZero:e.getBasePosition()}});f.dataset.pivot();t.each(o,function(t,i){this.updateElement(t,i,n)},this);this.updateBezierControlPoints()},updateElement:function(n,i,r){var u=n.custom||{},f=this.getDataset(),o=this.chart.scale,e=this.chart.options.elements.point,s=o.getPointPositionForValue(i,f.data[i]);t.extend(n,{_datasetIndex:this.index,_index:i,_scale:o,_model:{x:r?o.xCenter:s.x,y:r?o.yCenter:s.y,tension:u.tension?u.tension:t.getValueOrDefault(f.tension,this.chart.options.elements.line.tension),radius:u.radius?u.radius:t.getValueAtIndexOrDefault(f.pointRadius,i,e.radius),backgroundColor:u.backgroundColor?u.backgroundColor:t.getValueAtIndexOrDefault(f.pointBackgroundColor,i,e.backgroundColor),borderColor:u.borderColor?u.borderColor:t.getValueAtIndexOrDefault(f.pointBorderColor,i,e.borderColor),borderWidth:u.borderWidth?u.borderWidth:t.getValueAtIndexOrDefault(f.pointBorderWidth,i,e.borderWidth),pointStyle:u.pointStyle?u.pointStyle:t.getValueAtIndexOrDefault(f.pointStyle,i,e.pointStyle),hitRadius:u.hitRadius?u.hitRadius:t.getValueAtIndexOrDefault(f.hitRadius,i,e.hitRadius)}});n._model.skip=u.skip?u.skip:isNaN(n._model.x)||isNaN(n._model.y)},updateBezierControlPoints:function(){var n=this.chart.chartArea,i=this.getMeta();t.each(i.data,function(r,u){var f=r._model,e=t.splineCurve(t.previousItem(i.data,u,!0)._model,f,t.nextItem(i.data,u,!0)._model,f.tension);f.controlPointPreviousX=Math.max(Math.min(e.previous.x,n.right),n.left);f.controlPointPreviousY=Math.max(Math.min(e.previous.y,n.bottom),n.top);f.controlPointNextX=Math.max(Math.min(e.next.x,n.right),n.left);f.controlPointNextY=Math.max(Math.min(e.next.y,n.bottom),n.top);r.pivot()},this)},draw:function(n){var i=this.getMeta(),r=n||1;t.each(i.data,function(n){n.transition(r)});i.dataset.transition(r).draw();t.each(i.data,function(n){n.draw()})},setHoverStyle:function(n){var u=this.chart.data.datasets[n._datasetIndex],i=n.custom||{},f=n._index,r=n._model;r.radius=i.hoverRadius?i.hoverRadius:t.getValueAtIndexOrDefault(u.pointHoverRadius,f,this.chart.options.elements.point.hoverRadius);r.backgroundColor=i.hoverBackgroundColor?i.hoverBackgroundColor:t.getValueAtIndexOrDefault(u.pointHoverBackgroundColor,f,t.getHoverColor(r.backgroundColor));r.borderColor=i.hoverBorderColor?i.hoverBorderColor:t.getValueAtIndexOrDefault(u.pointHoverBorderColor,f,t.getHoverColor(r.borderColor));r.borderWidth=i.hoverBorderWidth?i.hoverBorderWidth:t.getValueAtIndexOrDefault(u.pointHoverBorderWidth,f,r.borderWidth)},removeHoverStyle:function(n){var r=this.chart.data.datasets[n._datasetIndex],i=n.custom||{},u=n._index,f=n._model,e=this.chart.options.elements.point;f.radius=i.radius?i.radius:t.getValueAtIndexOrDefault(r.radius,u,e.radius);f.backgroundColor=i.backgroundColor?i.backgroundColor:t.getValueAtIndexOrDefault(r.pointBackgroundColor,u,e.backgroundColor);f.borderColor=i.borderColor?i.borderColor:t.getValueAtIndexOrDefault(r.pointBorderColor,u,e.borderColor);f.borderWidth=i.borderWidth?i.borderWidth:t.getValueAtIndexOrDefault(r.pointBorderWidth,u,e.borderWidth)}})}},{}],21:[function(n,t){"use strict";t.exports=function(n){var t=n.helpers;n.defaults.global.animation={duration:1e3,easing:"easeOutQuart",onProgress:t.noop,onComplete:t.noop};n.Animation=n.Element.extend({currentStep:null,numSteps:60,easing:"",render:null,onAnimationProgress:null,onAnimationComplete:null});n.animationService={frameDuration:17,animations:[],dropFrames:0,request:null,addAnimation:function(n,t,i,r){r||(n.animating=!0);for(var u=0;u<this.animations.length;++u)if(this.animations[u].chartInstance===n){this.animations[u].animationObject=t;return}this.animations.push({chartInstance:n,animationObject:t});this.animations.length===1&&this.requestAnimationFrame()},cancelAnimation:function(n){var i=t.findIndex(this.animations,function(t){return t.chartInstance===n});i!==-1&&(this.animations.splice(i,1),n.animating=!1)},requestAnimationFrame:function(){var n=this;n.request===null&&(n.request=t.requestAnimFrame.call(window,function(){n.request=null;n.startDigest()}))},startDigest:function(){var u=Date.now(),t=0,n,i,r;for(this.dropFrames>1&&(t=Math.floor(this.dropFrames),this.dropFrames=this.dropFrames%1),n=0;n<this.animations.length;)this.animations[n].animationObject.currentStep===null&&(this.animations[n].animationObject.currentStep=0),this.animations[n].animationObject.currentStep+=1+t,this.animations[n].animationObject.currentStep>this.animations[n].animationObject.numSteps&&(this.animations[n].animationObject.currentStep=this.animations[n].animationObject.numSteps),this.animations[n].animationObject.render(this.animations[n].chartInstance,this.animations[n].animationObject),this.animations[n].animationObject.onAnimationProgress&&this.animations[n].animationObject.onAnimationProgress.call&&this.animations[n].animationObject.onAnimationProgress.call(this.animations[n].chartInstance,this.animations[n]),this.animations[n].animationObject.currentStep===this.animations[n].animationObject.numSteps?(this.animations[n].animationObject.onAnimationComplete&&this.animations[n].animationObject.onAnimationComplete.call&&this.animations[n].animationObject.onAnimationComplete.call(this.animations[n].chartInstance,this.animations[n]),this.animations[n].chartInstance.animating=!1,this.animations.splice(n,1)):++n;i=Date.now();r=(i-u)/this.frameDuration;this.dropFrames+=r;this.animations.length>0&&this.requestAnimationFrame()}}}},{}],22:[function(n,t){"use strict";t.exports=function(n){var t=n.helpers;n.types={};n.instances={};n.controllers={};n.Controller=function(i){return this.chart=i,this.config=i.config,this.options=this.config.options=t.configMerge(n.defaults.global,n.defaults[this.config.type],this.config.options||{}),this.id=t.uid(),Object.defineProperty(this,"data",{get:function(){return this.config.data}}),n.instances[this.id]=this,this.options.responsive&&this.resize(!0),this.initialize(),this};t.extend(n.Controller.prototype,{initialize:function(){return n.pluginService.notifyPlugins("beforeInit",[this]),this.bindEvents(),this.ensureScalesHaveIDs(),this.buildOrUpdateControllers(),this.buildScales(),this.buildSurroundingItems(),this.updateLayout(),this.resetElements(),this.initToolTip(),this.update(),n.pluginService.notifyPlugins("afterInit",[this]),this},clear:function(){return t.clear(this.chart),this},stop:function(){return n.animationService.cancelAnimation(this),this},resize:function(n){var r=this.chart.canvas,i=t.getMaximumWidth(this.chart.canvas),u=this.options.maintainAspectRatio&&isNaN(this.chart.aspectRatio)===!1&&isFinite(this.chart.aspectRatio)&&this.chart.aspectRatio!==0?i/this.chart.aspectRatio:t.getMaximumHeight(this.chart.canvas),f=this.chart.width!==i||this.chart.height!==u;return f?(r.width=this.chart.width=i,r.height=this.chart.height=u,t.retinaScale(this.chart),n||(this.stop(),this.update(this.options.responsiveAnimationDuration)),this):this},ensureScalesHaveIDs:function(){var i=this.options,r=i.scales||{},n=i.scale;t.each(r.xAxes,function(n,t){n.id=n.id||"x-axis-"+t});t.each(r.yAxes,function(n,t){n.id=n.id||"y-axis-"+t});n&&(n.id=n.id||"scale")},buildScales:function(){var i=this,r=i.options,f=i.scales={},u=[];r.scales&&(u=u.concat((r.scales.xAxes||[]).map(function(n){return{options:n,dtype:"category"}}),(r.scales.yAxes||[]).map(function(n){return{options:n,dtype:"linear"}})));r.scale&&u.push({options:r.scale,dtype:"radialLinear",isDefault:!0});t.each(u,function(r){var e=r.options,s=t.getValueOrDefault(e.type,r.dtype),o=n.scaleService.getScaleConstructor(s),u;o&&(u=new o({id:e.id,options:e,ctx:i.chart.ctx,chart:i}),f[u.id]=u,r.isDefault&&(i.scale=u))});n.scaleService.addScalesToLayout(this)},buildSurroundingItems:function(){this.options.title&&(this.titleBlock=new n.Title({ctx:this.chart.ctx,options:this.options.title,chart:this}),n.layoutService.addBox(this,this.titleBlock));this.options.legend&&(this.legend=new n.Legend({ctx:this.chart.ctx,options:this.options.legend,chart:this}),n.layoutService.addBox(this,this.legend))},updateLayout:function(){n.layoutService.update(this,this.chart.width,this.chart.height)},buildOrUpdateControllers:function(){var i=[],u=[],r;if(t.each(this.data.datasets,function(t,r){var f=this.getDatasetMeta(r);f.type||(f.type=t.type||this.config.type);i.push(f.type);f.controller?f.controller.updateIndex(r):(f.controller=new n.controllers[f.type](this,r),u.push(f.controller))},this),i.length>1)for(r=1;r<i.length;r++)if(i[r]!==i[r-1]){this.isCombo=!0;break}return u},resetElements:function(){t.each(this.data.datasets,function(n,t){this.getDatasetMeta(t).controller.reset()},this)},update:function(i,r){n.pluginService.notifyPlugins("beforeUpdate",[this]);this.tooltip._data=this.data;var u=this.buildOrUpdateControllers();t.each(this.data.datasets,function(n,t){this.getDatasetMeta(t).controller.buildOrUpdateElements()},this);n.layoutService.update(this,this.chart.width,this.chart.height);n.pluginService.notifyPlugins("afterScaleUpdate",[this]);t.each(u,function(n){n.reset()});t.each(this.data.datasets,function(n,t){this.getDatasetMeta(t).controller.update()},this);n.pluginService.notifyPlugins("afterUpdate",[this]);this.render(i,r)},render:function(i,r){var u,f;return n.pluginService.notifyPlugins("beforeRender",[this]),u=this.options.animation,u&&(typeof i!="undefined"&&i!==0||typeof i=="undefined"&&u.duration!==0)?(f=new n.Animation,f.numSteps=(i||u.duration)/16.66,f.easing=u.easing,f.render=function(n,i){var u=t.easingEffects[i.easing],r=i.currentStep/i.numSteps,f=u(r);n.draw(f,r,i.currentStep)},f.onAnimationProgress=u.onProgress,f.onAnimationComplete=u.onComplete,n.animationService.addAnimation(this,f,i,r)):(this.draw(),u&&u.onComplete&&u.onComplete.call&&u.onComplete.call(this)),this},draw:function(i){var u=i||1,r;this.clear();n.pluginService.notifyPlugins("beforeDraw",[this,u]);t.each(this.boxes,function(n){n.draw(this.chartArea)},this);this.scale&&this.scale.draw();r=this.chart.ctx;r.save();r.beginPath();r.rect(this.chartArea.left,this.chartArea.top,this.chartArea.right-this.chartArea.left,this.chartArea.bottom-this.chartArea.top);r.clip();t.each(this.data.datasets,function(n,t){this.isDatasetVisible(t)&&this.getDatasetMeta(t).controller.draw(i)},this,!0);r.restore();this.tooltip.transition(u).draw();n.pluginService.notifyPlugins("afterDraw",[this,u])},getElementAtEvent:function(n){var r=t.getRelativePosition(n,this.chart),i=[];return t.each(this.data.datasets,function(n,u){if(this.isDatasetVisible(u)){var f=this.getDatasetMeta(u);t.each(f.data,function(n){if(n.inRange(r.x,r.y))return i.push(n),i})}},this),i},getElementsAtEvent:function(n){var r=t.getRelativePosition(n,this.chart),i=[],u=function(){var n,i,t;if(this.data.datasets)for(n=0;n<this.data.datasets.length;n++)if(i=this.getDatasetMeta(n),this.isDatasetVisible(n))for(t=0;t<i.data.length;t++)if(i.data[t].inRange(r.x,r.y))return i.data[t]}.call(this);return u?(t.each(this.data.datasets,function(n,t){if(this.isDatasetVisible(t)){var r=this.getDatasetMeta(t);i.push(r.data[u._index])}},this),i):i},getElementsAtEventForMode:function(n,t){var i=this;switch(t){case"single":return i.getElementAtEvent(n);case"label":return i.getElementsAtEvent(n);case"dataset":return i.getDatasetAtEvent(n);default:return n}},getDatasetAtEvent:function(n){var t=this.getElementAtEvent(n);return t.length>0&&(t=this.getDatasetMeta(t[0]._datasetIndex).data),t},getDatasetMeta:function(n){var t=this.data.datasets[n],i;return t._meta||(t._meta={}),i=t._meta[this.id],i||(i=t._meta[this.id]={type:null,data:[],dataset:null,controller:null,hidden:null,xAxisID:null,yAxisID:null}),i},getVisibleDatasetCount:function(){for(var t=0,n=0,i=this.data.datasets.length;n<i;++n)this.isDatasetVisible(n)&&t++;return t},isDatasetVisible:function(n){var t=this.getDatasetMeta(n);return typeof t.hidden=="boolean"?!t.hidden:!this.data.datasets[n].hidden},generateLegend:function(){return this.options.legendCallback(this)},destroy:function(){this.clear();t.unbindEvents(this,this.events);t.removeResizeListener(this.chart.canvas.parentNode);var i=this.chart.canvas;i.width=this.chart.width;i.height=this.chart.height;this.chart.originalDevicePixelRatio!==undefined&&this.chart.ctx.scale(1/this.chart.originalDevicePixelRatio,1/this.chart.originalDevicePixelRatio);i.style.width=this.chart.originalCanvasStyleWidth;i.style.height=this.chart.originalCanvasStyleHeight;n.pluginService.notifyPlugins("destroy",[this]);delete n.instances[this.id]},toBase64Image:function(){return this.chart.canvas.toDataURL.apply(this.chart.canvas,arguments)},initToolTip:function(){this.tooltip=new n.Tooltip({_chart:this.chart,_chartInstance:this,_data:this.data,_options:this.options},this)},bindEvents:function(){t.bindEvents(this,this.options.events,function(n){this.eventHandler(n)})},updateHoverStyle:function(n,t,i){var e=i?"setHoverStyle":"removeHoverStyle",r,u,f;switch(t){case"single":n=[n[0]];break;case"label":case"dataset":break;default:return}for(u=0,f=n.length;u<f;++u)r=n[u],r&&this.getDatasetMeta(r._datasetIndex).controller[e](r)},eventHandler:function(n){var i=this,u=i.tooltip,e=i.options||{},r=e.hover,f=e.tooltips;return i.lastActive=i.lastActive||[],i.lastTooltipActive=i.lastTooltipActive||[],n.type==="mouseout"?(i.active=[],i.tooltipActive=[]):(i.active=i.getElementsAtEventForMode(n,r.mode),i.tooltipActive=i.getElementsAtEventForMode(n,f.mode)),r.onHover&&r.onHover.call(i,i.active),(n.type==="mouseup"||n.type==="click")&&(e.onClick&&e.onClick.call(i,n,i.active),i.legend&&i.legend.handleEvent&&i.legend.handleEvent(n)),i.lastActive.length&&i.updateHoverStyle(i.lastActive,r.mode,!1),i.active.length&&r.mode&&i.updateHoverStyle(i.active,r.mode,!0),(f.enabled||f.custom)&&(u.initialize(),u._active=i.tooltipActive,u.update(!0)),u.pivot(),i.animating||t.arrayEquals(i.active,i.lastActive)&&t.arrayEquals(i.tooltipActive,i.lastTooltipActive)||(i.stop(),(f.enabled||f.custom)&&u.update(!0),i.render(r.animationDuration,!0)),i.lastActive=i.active,i.lastTooltipActive=i.tooltipActive,i}})}},{}],23:[function(n,t){"use strict";t.exports=function(n){var t=n.helpers,i=t.noop;n.DatasetController=function(n,t){this.initialize.call(this,n,t)};t.extend(n.DatasetController.prototype,{datasetElementType:null,dataElementType:null,initialize:function(n,t){this.chart=n;this.index=t;this.linkScales();this.addElements()},updateIndex:function(n){this.index=n},linkScales:function(){var n=this.getMeta(),t=this.getDataset();n.xAxisID===null&&(n.xAxisID=t.xAxisID||this.chart.options.scales.xAxes[0].id);n.yAxisID===null&&(n.yAxisID=t.yAxisID||this.chart.options.scales.yAxes[0].id)},getDataset:function(){return this.chart.data.datasets[this.index]},getMeta:function(){return this.chart.getDatasetMeta(this.index)},getScaleForId:function(n){return this.chart.scales[n]},reset:function(){this.update(!0)},createMetaDataset:function(){var n=this,t=n.datasetElementType;return t&&new t({_chart:n.chart.chart,_datasetIndex:n.index})},createMetaData:function(n){var t=this,i=t.dataElementType;return i&&new i({_chart:t.chart.chart,_datasetIndex:t.index,_index:n})},addElements:function(){for(var t=this,i=t.getMeta(),f=t.getDataset().data||[],r=i.data,n=0,u=f.length;n<u;++n)r[n]=r[n]||t.createMetaData(i,n);i.dataset=i.dataset||t.createMetaDataset()},addElementAndReset:function(n){var t=this,i=t.createMetaData(n);t.getMeta().data.splice(n,0,i);t.updateElement(i,n,!0)},buildOrUpdateElements:function(){var u=this.getMeta(),r=u.data,n=this.getDataset().data.length,t=r.length,i;if(n<t)r.splice(n,t-n);else if(n>t)for(i=t;i<n;++i)this.addElementAndReset(i)},update:i,draw:function(n){var i=n||1;t.each(this.getMeta().data,function(n){n.transition(i).draw()})},removeHoverStyle:function(n,i){var u=this.chart.data.datasets[n._datasetIndex],f=n._index,r=n.custom||{},e=t.getValueAtIndexOrDefault,s=t.color,o=n._model;o.backgroundColor=r.backgroundColor?r.backgroundColor:e(u.backgroundColor,f,i.backgroundColor);o.borderColor=r.borderColor?r.borderColor:e(u.borderColor,f,i.borderColor);o.borderWidth=r.borderWidth?r.borderWidth:e(u.borderWidth,f,i.borderWidth)},setHoverStyle:function(n){var u=this.chart.data.datasets[n._datasetIndex],f=n._index,i=n.custom||{},e=t.getValueAtIndexOrDefault,s=t.color,o=t.getHoverColor,r=n._model;r.backgroundColor=i.hoverBackgroundColor?i.hoverBackgroundColor:e(u.hoverBackgroundColor,f,o(r.backgroundColor));r.borderColor=i.hoverBorderColor?i.hoverBorderColor:e(u.hoverBorderColor,f,o(r.borderColor));r.borderWidth=i.hoverBorderWidth?i.hoverBorderWidth:e(u.hoverBorderWidth,f,r.borderWidth)}});n.DatasetController.extend=t.inherits}},{}],24:[function(n,t){"use strict";t.exports=function(n){var t=n.helpers;n.elements={};n.Element=function(n){t.extend(this,n);this.initialize.apply(this,arguments)};t.extend(n.Element.prototype,{initialize:function(){this.hidden=!1},pivot:function(){return this._view||(this._view=t.clone(this._model)),this._start=t.clone(this._view),this},transition:function(n){return(this._view||(this._view=t.clone(this._model)),n===1)?(this._view=this._model,this._start=null,this):(this._start||this.pivot(),t.each(this._model,function(i,r){var f,u;if(r[0]!=="_")if(this._view.hasOwnProperty(r)){if(i!==this._view[r])if(typeof i=="string")try{f=t.color(this._model[r]).mix(t.color(this._start[r]),n);this._view[r]=f.rgbString()}catch(e){this._view[r]=i}else typeof i=="number"?(u=this._start[r]!==undefined&&isNaN(this._start[r])===!1?this._start[r]:0,this._view[r]=(this._model[r]-u)*n+u):this._view[r]=i}else this._view[r]=typeof i!="number"||isNaN(this._view[r])?i:i*n},this),this)},tooltipPosition:function(){return{x:this._model.x,y:this._model.y}},hasValue:function(){return t.isNumber(this._model.x)&&t.isNumber(this._model.y)}});n.Element.extend=t.inherits}},{}],25:[function(n,t){"use strict";var i=n("chartjs-color");t.exports=function(n){function u(n,t,i){var r;return typeof n=="string"?(r=parseInt(n,10),n.indexOf("%")!=-1&&(r=r/100*t.parentNode[i])):r=n,r}function f(n){return n!==undefined&&n!==null&&n!=="none"}function e(n,t,i){var r=document.defaultView,e=n.parentNode,o=r.getComputedStyle(n)[t],s=r.getComputedStyle(e)[t],h=f(o),c=f(s),l=Number.POSITIVE_INFINITY;return h||c?Math.min(h?u(o,n,i):l,c?u(s,e,i):l):"none"}var t=n.helpers={},r;t.each=function(n,i,r,u){var f,e,o;if(t.isArray(n))if(e=n.length,u)for(f=e-1;f>=0;f--)i.call(r,n[f],f);else for(f=0;f<e;f++)i.call(r,n[f],f);else if(typeof n=="object")for(o=Object.keys(n),e=o.length,f=0;f<e;f++)i.call(r,n[o[f]],o[f])};t.clone=function(n){var i={};return t.each(n,function(n,r){i[r]=t.isArray(n)?n.slice(0):typeof n=="object"&&n!==null?t.clone(n):n}),i};t.extend=function(n){for(var u=arguments.length,r=[],i=1;i<u;i++)r.push(arguments[i]);return t.each(r,function(i){t.each(i,function(t,i){n[i]=t})}),n};t.configMerge=function(i){var r=t.clone(i);return t.each(Array.prototype.slice.call(arguments,1),function(i){t.each(i,function(i,u){if(u==="scales")r[u]=t.scaleMerge(r.hasOwnProperty(u)?r[u]:{},i);else if(u==="scale")r[u]=t.configMerge(r.hasOwnProperty(u)?r[u]:{},n.scaleService.getScaleDefaults(i.type),i);else if(r.hasOwnProperty(u)&&t.isArray(r[u])&&t.isArray(i)){var f=r[u];t.each(i,function(n,i){i<f.length?f[i]=typeof f[i]=="object"&&f[i]!==null&&typeof n=="object"&&n!==null?t.configMerge(f[i],n):n:f.push(n)})}else r[u]=r.hasOwnProperty(u)&&typeof r[u]=="object"&&r[u]!==null&&typeof i=="object"?t.configMerge(r[u],i):i})}),r};t.extendDeep=function(){function n(i){return t.each(arguments,function(r){r!==i&&t.each(r,function(t,r){i[r]&&i[r].constructor&&i[r].constructor===Object?n(i[r],t):i[r]=t})}),i}return n.apply(this,arguments)};t.scaleMerge=function(i,r){var u=t.clone(i);return t.each(r,function(i,r){r==="xAxes"||r==="yAxes"?u.hasOwnProperty(r)?t.each(i,function(i,f){var o=t.getValueOrDefault(i.type,r==="xAxes"?"category":"linear"),e=n.scaleService.getScaleDefaults(o);f>=u[r].length||!u[r][f].type?u[r].push(t.configMerge(e,i)):u[r][f]=i.type&&i.type!==u[r][f].type?t.configMerge(u[r][f],e,i):t.configMerge(u[r][f],i)}):(u[r]=[],t.each(i,function(i){var f=t.getValueOrDefault(i.type,r==="xAxes"?"category":"linear");u[r].push(t.configMerge(n.scaleService.getScaleDefaults(f),i))})):u[r]=u.hasOwnProperty(r)&&typeof u[r]=="object"&&u[r]!==null&&typeof i=="object"?t.configMerge(u[r],i):i}),u};t.getValueAtIndexOrDefault=function(n,i,r){return n===undefined||n===null?r:t.isArray(n)?i<n.length?n[i]:r:n};t.getValueOrDefault=function(n,t){return n===undefined?t:n};t.indexOf=function(n,t){if(Array.prototype.indexOf)return n.indexOf(t);for(var i=0;i<n.length;i++)if(n[i]===t)return i;return-1};t.where=function(n,i){if(t.isArray(n)&&Array.prototype.filter)return n.filter(i);var r=[];return t.each(n,function(n){i(n)&&r.push(n)}),r};t.findIndex=function(n,t,i){var u=-1,r;if(Array.prototype.findIndex)u=n.findIndex(t,i);else for(r=0;r<n.length;++r)if(i=i!==undefined?i:n,t.call(i,n[r],r,n)){u=r;break}return u};t.findNextWhere=function(n,t,i){var r,u;for((i===undefined||i===null)&&(i=-1),r=i+1;r<n.length;r++)if(u=n[r],t(u))return u};t.findPreviousWhere=function(n,t,i){var r,u;for((i===undefined||i===null)&&(i=n.length),r=i-1;r>=0;r--)if(u=n[r],t(u))return u};t.inherits=function(n){var r=this,i=n&&n.hasOwnProperty("constructor")?n.constructor:function(){return r.apply(this,arguments)},u=function(){this.constructor=i};return u.prototype=r.prototype,i.prototype=new u,i.extend=t.inherits,n&&t.extend(i.prototype,n),i.__super__=r.prototype,i};t.noop=function(){};t.uid=function(){var n=0;return function(){return n++}}();t.warn=function(n){console&&typeof console.warn=="function"&&console.warn(n)};t.isNumber=function(n){return!isNaN(parseFloat(n))&&isFinite(n)};t.almostEquals=function(n,t,i){return Math.abs(n-t)<i};t.max=function(n){return n.reduce(function(n,t){return isNaN(t)?n:Math.max(n,t)},Number.NEGATIVE_INFINITY)};t.min=function(n){return n.reduce(function(n,t){return isNaN(t)?n:Math.min(n,t)},Number.POSITIVE_INFINITY)};t.sign=function(n){return Math.sign?Math.sign(n):(n=+n,n===0||isNaN(n))?n:n>0?1:-1};t.log10=function(n){return Math.log10?Math.log10(n):Math.log(n)/Math.LN10};t.toRadians=function(n){return n*(Math.PI/180)};t.toDegrees=function(n){return n*(180/Math.PI)};t.getAngleFromPoint=function(n,t){var i=t.x-n.x,r=t.y-n.y,f=Math.sqrt(i*i+r*r),u=Math.atan2(r,i);return u<-.5*Math.PI&&(u+=2*Math.PI),{angle:u,distance:f}};t.aliasPixel=function(n){return n%2==0?0:.5};t.splineCurve=function(n,t,i,r){var f=n.skip?t:n,u=t,e=i.skip?t:i,h=Math.sqrt(Math.pow(u.x-f.x,2)+Math.pow(u.y-f.y,2)),c=Math.sqrt(Math.pow(e.x-u.x,2)+Math.pow(e.y-u.y,2)),o=h/(h+c),s=c/(h+c),l,a;return o=isNaN(o)?0:o,s=isNaN(s)?0:s,l=r*o,a=r*s,{previous:{x:u.x-l*(e.x-f.x),y:u.y-l*(e.y-f.y)},next:{x:u.x+a*(e.x-f.x),y:u.y+a*(e.y-f.y)}}};t.nextItem=function(n,t,i){return i?t>=n.length-1?n[0]:n[t+1]:t>=n.length-1?n[n.length-1]:n[t+1]};t.previousItem=function(n,t,i){return i?t<=0?n[n.length-1]:n[t-1]:t<=0?n[0]:n[t-1]};t.niceNum=function(n,i){var u=Math.floor(t.log10(n)),r=n/Math.pow(10,u),f;return f=i?r<1.5?1:r<3?2:r<7?5:10:r<=1?1:r<=2?2:r<=5?5:10,f*Math.pow(10,u)};r=t.easingEffects={linear:function(n){return n},easeInQuad:function(n){return n*n},easeOutQuad:function(n){return-1*n*(n-2)},easeInOutQuad:function(n){return(n/=1/2)<1?1/2*n*n:-1/2*(--n*(n-2)-1)},easeInCubic:function(n){return n*n*n},easeOutCubic:function(n){return 1*((n=n/1-1)*n*n+1)},easeInOutCubic:function(n){return(n/=1/2)<1?1/2*n*n*n:1/2*((n-=2)*n*n+2)},easeInQuart:function(n){return n*n*n*n},easeOutQuart:function(n){return-1*((n=n/1-1)*n*n*n-1)},easeInOutQuart:function(n){return(n/=1/2)<1?1/2*n*n*n*n:-1/2*((n-=2)*n*n*n-2)},easeInQuint:function(n){return 1*(n/=1)*n*n*n*n},easeOutQuint:function(n){return 1*((n=n/1-1)*n*n*n*n+1)},easeInOutQuint:function(n){return(n/=1/2)<1?1/2*n*n*n*n*n:1/2*((n-=2)*n*n*n*n+2)},easeInSine:function(n){return-1*Math.cos(n/1*(Math.PI/2))+1},easeOutSine:function(n){return 1*Math.sin(n/1*(Math.PI/2))},easeInOutSine:function(n){return-1/2*(Math.cos(Math.PI*n/1)-1)},easeInExpo:function(n){return n===0?1:1*Math.pow(2,10*(n/1-1))},easeOutExpo:function(n){return n===1?1:1*(-Math.pow(2,-10*n)+1)},easeInOutExpo:function(n){return n===0?0:n===1?1:(n/=1/2)<1?1/2*Math.pow(2,10*(n-1)):1/2*(-Math.pow(2,-10*--n)+2)},easeInCirc:function(n){return n>=1?n:-1*(Math.sqrt(1-(n/=1)*n)-1)},easeOutCirc:function(n){return 1*Math.sqrt(1-(n=n/1-1)*n)},easeInOutCirc:function(n){return(n/=1/2)<1?-1/2*(Math.sqrt(1-n*n)-1):1/2*(Math.sqrt(1-(n-=2)*n)+1)},easeInElastic:function(n){var r=1.70158,t=0,i=1;return n===0?0:(n/=1)==1?1:(t||(t=1*.3),i<Math.abs(1)?(i=1,r=t/4):r=t/(2*Math.PI)*Math.asin(1/i),-(i*Math.pow(2,10*(n-=1))*Math.sin((n*1-r)*2*Math.PI/t)))},easeOutElastic:function(n){var r=1.70158,t=0,i=1;return n===0?0:(n/=1)==1?1:(t||(t=1*.3),i<Math.abs(1)?(i=1,r=t/4):r=t/(2*Math.PI)*Math.asin(1/i),i*Math.pow(2,-10*n)*Math.sin((n*1-r)*2*Math.PI/t)+1)},easeInOutElastic:function(n){var r=1.70158,t=0,i=1;return n===0?0:(n/=1/2)==2?1:(t||(t=1*.3*1.5),i<Math.abs(1)?(i=1,r=t/4):r=t/(2*Math.PI)*Math.asin(1/i),n<1)?-.5*i*Math.pow(2,10*(n-=1))*Math.sin((n*1-r)*2*Math.PI/t):i*Math.pow(2,-10*(n-=1))*Math.sin((n*1-r)*2*Math.PI/t)*.5+1},easeInBack:function(n){var t=1.70158;return 1*(n/=1)*n*((t+1)*n-t)},easeOutBack:function(n){var t=1.70158;return 1*((n=n/1-1)*n*((t+1)*n+t)+1)},easeInOutBack:function(n){var t=1.70158;return(n/=1/2)<1?1/2*n*n*(((t*=1.525)+1)*n-t):1/2*((n-=2)*n*(((t*=1.525)+1)*n+t)+2)},easeInBounce:function(n){return 1-r.easeOutBounce(1-n)},easeOutBounce:function(n){return(n/=1)<1/2.75?1*7.5625*n*n:n<2/2.75?1*(7.5625*(n-=1.5/2.75)*n+.75):n<2.5/2.75?1*(7.5625*(n-=2.25/2.75)*n+.9375):1*(7.5625*(n-=2.625/2.75)*n+.984375)},easeInOutBounce:function(n){return n<1/2?r.easeInBounce(n*2)*.5:r.easeOutBounce(n*2-1)*.5+1*.5}};t.requestAnimFrame=function(){return window.requestAnimationFrame||window.webkitRequestAnimationFrame||window.mozRequestAnimationFrame||window.oRequestAnimationFrame||window.msRequestAnimationFrame||function(n){return window.setTimeout(n,1e3/60)}}();t.cancelAnimFrame=function(){return window.cancelAnimationFrame||window.webkitCancelAnimationFrame||window.mozCancelAnimationFrame||window.oCancelAnimationFrame||window.msCancelAnimationFrame||function(n){return window.clearTimeout(n,1e3/60)}}();t.getRelativePosition=function(n,i){var f,e,s=n.originalEvent||n,r=n.currentTarget||n.srcElement,u=r.getBoundingClientRect(),o=s.touches;o&&o.length>0?(f=o[0].clientX,e=o[0].clientY):(f=s.clientX,e=s.clientY);var h=parseFloat(t.getStyle(r,"padding-left")),c=parseFloat(t.getStyle(r,"padding-top")),l=parseFloat(t.getStyle(r,"padding-right")),a=parseFloat(t.getStyle(r,"padding-bottom")),v=u.right-u.left-h-l,y=u.bottom-u.top-c-a;return f=Math.round((f-u.left-h)/v*r.width/i.currentDevicePixelRatio),e=Math.round((e-u.top-c)/y*r.height/i.currentDevicePixelRatio),{x:f,y:e}};t.addEvent=function(n,t,i){n.addEventListener?n.addEventListener(t,i):n.attachEvent?n.attachEvent("on"+t,i):n["on"+t]=i};t.removeEvent=function(n,i,r){n.removeEventListener?n.removeEventListener(i,r,!1):n.detachEvent?n.detachEvent("on"+i,r):n["on"+i]=t.noop};t.bindEvents=function(n,i,r){var u=n.events=n.events||{};t.each(i,function(i){u[i]=function(){r.apply(n,arguments)};t.addEvent(n.chart.canvas,i,u[i])})};t.unbindEvents=function(n,i){var r=n.chart.canvas;t.each(i,function(n,i){t.removeEvent(r,i,n)})};t.getConstraintWidth=function(n){return e(n,"max-width","clientWidth")};t.getConstraintHeight=function(n){return e(n,"max-height","clientHeight")};t.getMaximumWidth=function(n){var i=n.parentNode,f=parseInt(t.getStyle(i,"padding-left"))+parseInt(t.getStyle(i,"padding-right")),r=i.clientWidth-f,u=t.getConstraintWidth(n);return isNaN(u)?r:Math.min(r,u)};t.getMaximumHeight=function(n){var i=n.parentNode,f=parseInt(t.getStyle(i,"padding-top"))+parseInt(t.getStyle(i,"padding-bottom")),r=i.clientHeight-f,u=t.getConstraintHeight(n);return isNaN(u)?r:Math.min(r,u)};t.getStyle=function(n,t){return n.currentStyle?n.currentStyle[t]:document.defaultView.getComputedStyle(n,null).getPropertyValue(t)};t.retinaScale=function(n){var f=n.ctx,t=n.canvas,r=t.width,u=t.height,i=n.currentDevicePixelRatio=window.devicePixelRatio||1;i!==1&&(t.height=u*i,t.width=r*i,f.scale(i,i),n.originalDevicePixelRatio=n.originalDevicePixelRatio||i);t.style.width=r+"px";t.style.height=u+"px"};t.clear=function(n){n.ctx.clearRect(0,0,n.width,n.height)};t.fontString=function(n,t,i){return t+" "+n+"px "+i};t.longestText=function(n,i,r,u){var e,f,o,s,h;if(u=u||{},e=u.data=u.data||{},f=u.garbageCollect=u.garbageCollect||[],u.font!==i&&(e=u.data={},f=u.garbageCollect=[],u.font=i),n.font=i,o=0,t.each(r,function(t){if(t!==undefined&&t!==null){var i=e[t];i||(i=e[t]=n.measureText(t).width,f.push(t));i>o&&(o=i)}}),s=f.length/2,s>r.length){for(h=0;h<s;h++)delete e[f[h]];f.splice(0,s)}return o};t.drawRoundedRectangle=function(n,t,i,r,u,f){n.beginPath();n.moveTo(t+f,i);n.lineTo(t+r-f,i);n.quadraticCurveTo(t+r,i,t+r,i+f);n.lineTo(t+r,i+u-f);n.quadraticCurveTo(t+r,i+u,t+r-f,i+u);n.lineTo(t+f,i+u);n.quadraticCurveTo(t,i+u,t,i+u-f);n.lineTo(t,i+f);n.quadraticCurveTo(t,i,t+f,i);n.closePath()};t.color=function(t){return i?t instanceof CanvasGradient?i(n.defaults.global.defaultColor):i(t):(console.log("Color.js not found!"),t)};t.addResizeListener=function(n,t){var r=document.createElement("iframe"),u="chartjs-hidden-iframe",i;r.classlist?r.classlist.add(u):r.setAttribute("class",u);i=r.style;i.width="100%";i.display="block";i.border=0;i.height=0;i.margin=0;i.position="absolute";i.left=0;i.right=0;i.top=0;i.bottom=0;n.insertBefore(r,n.firstChild);(r.contentWindow||r).onresize=function(){t&&t()}};t.removeResizeListener=function(n){var t=n.querySelector(".chartjs-hidden-iframe");t&&t.parentNode.removeChild(t)};t.isArray=function(n){return Array.isArray?Array.isArray(n):Object.prototype.toString.call(n)==="[object Array]"};
//! @see http://stackoverflow.com/a/14853974
t.arrayEquals=function(n,i){var r,e,u,f;if(!n||!i||n.length!=i.length)return!1;for(r=0,e=n.length;r<e;++r)if(u=n[r],f=i[r],u instanceof Array&&f instanceof Array){if(!t.arrayEquals(u,f))return!1}else if(u!=f)return!1;return!0};t.pushAllIfDefined=function(n,i){typeof n!="undefined"&&(t.isArray(n)?i.push.apply(i,n):i.push(n))};t.callCallback=function(n,t,i){n&&typeof n.call=="function"&&n.apply(i,t)};t.getHoverColor=function(n){return n instanceof CanvasPattern?n:t.color(n).saturate(.5).darken(.1).rgbString()}}},{"chartjs-color":3}],26:[function(n,t){"use strict";t.exports=function(){var n=function(t,i){this.config=i;t.length&&t[0].getContext&&(t=t[0]);t.getContext&&(t=t.getContext("2d"));this.ctx=t;this.canvas=t.canvas;this.width=t.canvas.width||parseInt(n.helpers.getStyle(t.canvas,"width"))||n.helpers.getMaximumWidth(t.canvas);this.height=t.canvas.height||parseInt(n.helpers.getStyle(t.canvas,"height"))||n.helpers.getMaximumHeight(t.canvas);this.aspectRatio=this.width/this.height;(isNaN(this.aspectRatio)||isFinite(this.aspectRatio)===!1)&&(this.aspectRatio=i.aspectRatio!==undefined?i.aspectRatio:2);this.originalCanvasStyleWidth=t.canvas.style.width;this.originalCanvasStyleHeight=t.canvas.style.height;n.helpers.retinaScale(this);i&&(this.controller=new n.Controller(this));var r=this;return n.helpers.addResizeListener(t.canvas.parentNode,function(){r.controller&&r.controller.config.options.responsive&&r.controller.resize()}),this.controller?this.controller:this};return n.defaults={global:{responsive:!0,responsiveAnimationDuration:0,maintainAspectRatio:!0,events:["mousemove","mouseout","click","touchstart","touchmove"],hover:{onHover:null,mode:"single",animationDuration:400},onClick:null,defaultColor:"rgba(0,0,0,0.1)",defaultFontColor:"#666",defaultFontFamily:"'Helvetica Neue', 'Helvetica', 'Arial', sans-serif",defaultFontSize:12,defaultFontStyle:"normal",showLines:!0,elements:{},legendCallback:function(n){var t=[],i;for(t.push('<ul class="'+n.id+'-legend">'),i=0;i<n.data.datasets.length;i++)t.push('<li><span style="background-color:'+n.data.datasets[i].backgroundColor+'"><\/span>'),n.data.datasets[i].label&&t.push(n.data.datasets[i].label),t.push("<\/li>");return t.push("<\/ul>"),t.join("")}}},n}},{}],27:[function(n,t){"use strict";t.exports=function(n){var t=n.helpers;n.layoutService={defaults:{},addBox:function(n,t){n.boxes||(n.boxes=[]);n.boxes.push(t)},removeBox:function(n,t){n.boxes&&n.boxes.splice(n.boxes.indexOf(t),1)},update:function(n,i,r){function ht(n){var t,i=n.isHorizontal();i?(t=n.update(n.options.fullWidth?k:u,st),f-=t.height):(t=n.update(ot,rt),u-=t.width);tt.push({horizontal:i,minSize:t,box:n})}function ut(n){var i=t.findNextWhere(tt,function(t){return t.box===n}),r;i&&(n.isHorizontal()?(r={left:e,right:y,top:0,bottom:0},n.update(n.options.fullWidth?k:u,nt/2,r)):n.update(i.minSize.width,f))}function ct(n){var i=t.findNextWhere(tt,function(t){return t.box===n}),r={left:0,right:0,top:o,bottom:p};i&&n.update(i.minSize.width,f,r)}function it(n){n.isHorizontal()?(n.left=n.options.fullWidth?s:e,n.right=n.options.fullWidth?i-s:e+u,n.top=g,n.bottom=g+n.height,g=n.bottom):(n.left=d,n.right=d+n.width,n.top=o,n.bottom=o+f,d=n.right)}var w,b;if(n){var s=0,v=0,h=t.where(n.boxes,function(n){return n.options.position==="left"}),c=t.where(n.boxes,function(n){return n.options.position==="right"}),l=t.where(n.boxes,function(n){return n.options.position==="top"}),a=t.where(n.boxes,function(n){return n.options.position==="bottom"}),ft=t.where(n.boxes,function(n){return n.options.position==="chartArea"});l.sort(function(n,t){return(t.options.fullWidth?1:0)-(n.options.fullWidth?1:0)});a.sort(function(n,t){return(n.options.fullWidth?1:0)-(t.options.fullWidth?1:0)});var k=i-2*s,nt=r-2*v,et=k/2,rt=nt/2,ot=(i-et)/(h.length+c.length),st=(r-rt)/(l.length+a.length),u=k,f=nt,tt=[];t.each(h.concat(c,l,a),ht);var e=s,y=s,o=v,p=v;t.each(h.concat(c),ut);t.each(h,function(n){e+=n.width});t.each(c,function(n){y+=n.width});t.each(l.concat(a),ut);t.each(l,function(n){o+=n.height});t.each(a,function(n){p+=n.height});t.each(h.concat(c),ct);e=s;y=s;o=v;p=v;t.each(h,function(n){e+=n.width});t.each(c,function(n){y+=n.width});t.each(l,function(n){o+=n.height});t.each(a,function(n){p+=n.height});w=r-o-p;b=i-e-y;(b!==u||w!==f)&&(t.each(h,function(n){n.height=w}),t.each(c,function(n){n.height=w}),t.each(l,function(n){n.options.fullWidth||(n.width=b)}),t.each(a,function(n){n.options.fullWidth||(n.width=b)}),f=w,u=b);var d=s,g=v;t.each(h.concat(l),it);d+=u;g+=f;t.each(c,it);t.each(a,it);n.chartArea={left:e,top:o,right:e+u,bottom:o+f};t.each(ft,function(t){t.left=n.chartArea.left;t.top=n.chartArea.top;t.right=n.chartArea.right;t.bottom=n.chartArea.bottom;t.update(u,f)})}}}}},{}],28:[function(n,t){"use strict";t.exports=function(n){var t=n.helpers,i=t.noop;n.defaults.global.legend={display:!0,position:"top",fullWidth:!0,reverse:!1,onClick:function(n,t){var r=t.datasetIndex,i=this.chart,u=i.getDatasetMeta(r);u.hidden=u.hidden===null?!i.data.datasets[r].hidden:null;i.update()},labels:{boxWidth:40,padding:10,generateLabels:function(n){var i=n.data;return t.isArray(i.datasets)?i.datasets.map(function(t,i){return{text:t.label,fillStyle:t.backgroundColor,hidden:!n.isDatasetVisible(i),lineCap:t.borderCapStyle,lineDash:t.borderDash,lineDashOffset:t.borderDashOffset,lineJoin:t.borderJoinStyle,lineWidth:t.borderWidth,strokeStyle:t.borderColor,datasetIndex:i}},this):[]}}};n.Legend=n.Element.extend({initialize:function(n){t.extend(this,n);this.legendHitBoxes=[];this.doughnutMode=!1},beforeUpdate:i,update:function(n,t,i){return this.beforeUpdate(),this.maxWidth=n,this.maxHeight=t,this.margins=i,this.beforeSetDimensions(),this.setDimensions(),this.afterSetDimensions(),this.beforeBuildLabels(),this.buildLabels(),this.afterBuildLabels(),this.beforeFit(),this.fit(),this.afterFit(),this.afterUpdate(),this.minSize},afterUpdate:i,beforeSetDimensions:i,setDimensions:function(){this.isHorizontal()?(this.width=this.maxWidth,this.left=0,this.right=this.width):(this.height=this.maxHeight,this.top=0,this.bottom=this.height);this.paddingLeft=0;this.paddingTop=0;this.paddingRight=0;this.paddingBottom=0;this.minSize={width:0,height:0}},afterSetDimensions:i,beforeBuildLabels:i,buildLabels:function(){this.legendItems=this.options.labels.generateLabels.call(this,this.chart);this.options.reverse&&this.legendItems.reverse()},afterBuildLabels:i,beforeFit:i,fit:function(){var l=this.options,i=l.labels,o=l.display,e=this.ctx,s=n.defaults.global,h=t.getValueOrDefault,f=h(i.fontSize,s.defaultFontSize),v=h(i.fontStyle,s.defaultFontStyle),y=h(i.fontFamily,s.defaultFontFamily),p=t.fontString(f,v,y),w=this.legendHitBoxes=[],r=this.minSize,a=this.isHorizontal(),u,c;a?(r.width=this.maxWidth,r.height=o?10:0):(r.width=o?10:0,r.height=this.maxHeight);o&&a&&(u=this.lineWidths=[0],c=this.legendItems.length?f+i.padding:0,e.textAlign="left",e.textBaseline="top",e.font=p,t.each(this.legendItems,function(n,t){var r=i.boxWidth+f/2+e.measureText(n.text).width;u[u.length-1]+r+i.padding>=this.width&&(c+=f+i.padding,u[u.length]=this.left);w[t]={left:0,top:0,width:r,height:f};u[u.length-1]+=r+i.padding},this),r.height+=c);this.width=r.width;this.height=r.height},afterFit:i,isHorizontal:function(){return this.options.position==="top"||this.options.position==="bottom"},draw:function(){var a=this.options,f=a.labels,e=n.defaults.global,h=e.elements.line,c=this.width,v=this.lineWidths,s,l;if(a.display){var i=this.ctx,o={x:this.left+(c-v[0])/2,y:this.top+f.padding,line:0},r=t.getValueOrDefault,y=r(f.fontColor,e.defaultFontColor),u=r(f.fontSize,e.defaultFontSize),p=r(f.fontStyle,e.defaultFontStyle),w=r(f.fontFamily,e.defaultFontFamily),b=t.fontString(u,p,w);this.isHorizontal()&&(i.textAlign="left",i.textBaseline="top",i.lineWidth=.5,i.strokeStyle=y,i.fillStyle=y,i.font=b,s=f.boxWidth,l=this.legendHitBoxes,t.each(this.legendItems,function(n,t){var p=i.measureText(n.text).width,w=s+u/2+p,a=o.x,y=o.y;a+w>=c&&(y=o.y+=u+f.padding,o.line++,a=o.x=this.left+(c-v[o.line])/2);i.save();i.fillStyle=r(n.fillStyle,e.defaultColor);i.lineCap=r(n.lineCap,h.borderCapStyle);i.lineDashOffset=r(n.lineDashOffset,h.borderDashOffset);i.lineJoin=r(n.lineJoin,h.borderJoinStyle);i.lineWidth=r(n.lineWidth,h.borderWidth);i.strokeStyle=r(n.strokeStyle,e.defaultColor);i.setLineDash&&i.setLineDash(r(n.lineDash,h.borderDash));i.strokeRect(a,y,s,u);i.fillRect(a,y,s,u);i.restore();l[t].left=a;l[t].top=y;i.fillText(n.text,s+u/2+a,y);n.hidden&&(i.beginPath(),i.lineWidth=2,i.moveTo(s+u/2+a,y+u/2),i.lineTo(s+u/2+a+p,y+u/2),i.stroke());o.x+=w+f.padding},this))}},handleEvent:function(n){var o=t.getRelativePosition(n,this.chart.chart),u=o.x,f=o.y,s=this.options,e,r,i;if(u>=this.left&&u<=this.right&&f>=this.top&&f<=this.bottom)for(e=this.legendHitBoxes,r=0;r<e.length;++r)if(i=e[r],u>=i.left&&u<=i.left+i.width&&f>=i.top&&f<=i.top+i.height){s.onClick&&s.onClick.call(this,n,this.legendItems[r]);break}}})}},{}],29:[function(n,t){"use strict";t.exports=function(n){var i=n.helpers,t;n.plugins=[];n.pluginService={register:function(t){var i=n.plugins;i.indexOf(t)===-1&&i.push(t)},remove:function(t){var i=n.plugins,r=i.indexOf(t);r!==-1&&i.splice(r,1)},notifyPlugins:function(t,r,u){i.each(n.plugins,function(n){n[t]&&typeof n[t]=="function"&&n[t].apply(u,r)},u)}};t=i.noop;n.PluginBase=n.Element.extend({beforeInit:t,afterInit:t,beforeUpdate:t,afterUpdate:t,beforeDraw:t,afterDraw:t,destroy:t})}},{}],30:[function(n,t){"use strict";t.exports=function(n){var t=n.helpers;n.defaults.scale={display:!0,position:"left",gridLines:{display:!0,color:"rgba(0, 0, 0, 0.1)",lineWidth:1,drawBorder:!0,drawOnChartArea:!0,drawTicks:!0,tickMarkLength:10,zeroLineWidth:1,zeroLineColor:"rgba(0,0,0,0.25)",offsetGridLines:!1},scaleLabel:{labelString:"",display:!1},ticks:{beginAtZero:!1,minRotation:0,maxRotation:50,mirror:!1,padding:10,reverse:!1,display:!0,autoSkip:!0,autoSkipPadding:0,labelOffset:0,callback:function(n){return""+n}}};n.Scale=n.Element.extend({beforeUpdate:function(){t.callCallback(this.options.beforeUpdate,[this])},update:function(n,i,r){return this.beforeUpdate(),this.maxWidth=n,this.maxHeight=i,this.margins=t.extend({left:0,right:0,top:0,bottom:0},r),this.beforeSetDimensions(),this.setDimensions(),this.afterSetDimensions(),this.beforeDataLimits(),this.determineDataLimits(),this.afterDataLimits(),this.beforeBuildTicks(),this.buildTicks(),this.afterBuildTicks(),this.beforeTickToLabelConversion(),this.convertTicksToLabels(),this.afterTickToLabelConversion(),this.beforeCalculateTickRotation(),this.calculateTickRotation(),this.afterCalculateTickRotation(),this.beforeFit(),this.fit(),this.afterFit(),this.afterUpdate(),this.minSize},afterUpdate:function(){t.callCallback(this.options.afterUpdate,[this])},beforeSetDimensions:function(){t.callCallback(this.options.beforeSetDimensions,[this])},setDimensions:function(){this.isHorizontal()?(this.width=this.maxWidth,this.left=0,this.right=this.width):(this.height=this.maxHeight,this.top=0,this.bottom=this.height);this.paddingLeft=0;this.paddingTop=0;this.paddingRight=0;this.paddingBottom=0},afterSetDimensions:function(){t.callCallback(this.options.afterSetDimensions,[this])},beforeDataLimits:function(){t.callCallback(this.options.beforeDataLimits,[this])},determineDataLimits:t.noop,afterDataLimits:function(){t.callCallback(this.options.afterDataLimits,[this])},beforeBuildTicks:function(){t.callCallback(this.options.beforeBuildTicks,[this])},buildTicks:t.noop,afterBuildTicks:function(){t.callCallback(this.options.afterBuildTicks,[this])},beforeTickToLabelConversion:function(){t.callCallback(this.options.beforeTickToLabelConversion,[this])},convertTicksToLabels:function(){this.ticks=this.ticks.map(function(n,t,i){return this.options.ticks.userCallback?this.options.ticks.userCallback(n,t,i):this.options.ticks.callback(n,t,i)},this)},afterTickToLabelConversion:function(){t.callCallback(this.options.afterTickToLabelConversion,[this])},beforeCalculateTickRotation:function(){t.callCallback(this.options.beforeCalculateTickRotation,[this])},calculateTickRotation:function(){var r=this.ctx,f=n.defaults.global,i=this.options.ticks,u=t.getValueOrDefault(i.fontSize,f.defaultFontSize),y=t.getValueOrDefault(i.fontStyle,f.defaultFontStyle),p=t.getValueOrDefault(i.fontFamily,f.defaultFontFamily),c=t.fontString(u,y,p),e,l,o;if(r.font=c,e=r.measureText(this.ticks[0]).width,l=r.measureText(this.ticks[this.ticks.length-1]).width,this.labelRotation=i.minRotation||0,this.paddingRight=0,this.paddingLeft=0,this.options.display&&this.isHorizontal()){this.paddingRight=l/2+3;this.paddingLeft=e/2+3;this.longestTextCache||(this.longestTextCache={});for(var s=t.longestText(r,c,this.ticks,this.longestTextCache),a=s,h,v,w=this.getPixelForTick(1)-this.getPixelForTick(0)-6;a>w&&this.labelRotation<i.maxRotation;){if(h=Math.cos(t.toRadians(this.labelRotation)),v=Math.sin(t.toRadians(this.labelRotation)),o=h*e,o+u/2>this.yLabelWidth&&(this.paddingLeft=o+u/2),this.paddingRight=u/2,v*s>this.maxHeight){this.labelRotation--;break}this.labelRotation++;a=h*s}}this.margins&&(this.paddingLeft=Math.max(this.paddingLeft-this.margins.left,0),this.paddingRight=Math.max(this.paddingRight-this.margins.right,0))},afterCalculateTickRotation:function(){t.callCallback(this.options.afterCalculateTickRotation,[this])},beforeFit:function(){t.callCallback(this.options.beforeFit,[this])},fit:function(){var i=this.minSize={width:0,height:0},o=this.options,r=n.defaults.global,f=o.ticks,s=o.scaleLabel,h=o.display,c=this.isHorizontal(),e=t.getValueOrDefault(f.fontSize,r.defaultFontSize),k=t.getValueOrDefault(f.fontStyle,r.defaultFontStyle),d=t.getValueOrDefault(f.fontFamily,r.defaultFontFamily),a=t.fontString(e,k,d),l=t.getValueOrDefault(s.fontSize,r.defaultFontSize),g=t.getValueOrDefault(s.fontStyle,r.defaultFontStyle),nt=t.getValueOrDefault(s.fontFamily,r.defaultFontFamily),ut=t.fontString(l,g,nt),v=o.gridLines.tickMarkLength,u,y,w,b;if(i.width=c?this.isFullWidth()?this.maxWidth-this.margins.left-this.margins.right:this.maxWidth:h?v:0,i.height=c?h?v:0:this.maxHeight,s.display&&h&&(c?i.height+=l*1.5:i.width+=l*1.5),f.display&&h)if(this.longestTextCache||(this.longestTextCache={}),u=t.longestText(this.ctx,a,this.ticks,this.longestTextCache),c){this.longestLabelWidth=u;y=Math.sin(t.toRadians(this.labelRotation))*this.longestLabelWidth+1.5*e;i.height=Math.min(this.maxHeight,i.height+y);this.ctx.font=a;var p=this.ctx.measureText(this.ticks[0]).width,tt=this.ctx.measureText(this.ticks[this.ticks.length-1]).width,it=Math.cos(t.toRadians(this.labelRotation)),rt=Math.sin(t.toRadians(this.labelRotation));this.paddingLeft=this.labelRotation!==0?it*p+3:p/2+3;this.paddingRight=this.labelRotation!==0?rt*(e/2)+3:tt/2+3}else w=this.maxWidth-i.width,b=f.mirror,b?u=0:u+=this.options.ticks.padding,u<w?i.width+=u:i.width=this.maxWidth,this.paddingTop=e/2,this.paddingBottom=e/2;this.margins&&(this.paddingLeft=Math.max(this.paddingLeft-this.margins.left,0),this.paddingTop=Math.max(this.paddingTop-this.margins.top,0),this.paddingRight=Math.max(this.paddingRight-this.margins.right,0),this.paddingBottom=Math.max(this.paddingBottom-this.margins.bottom,0));this.width=i.width;this.height=i.height},afterFit:function(){t.callCallback(this.options.afterFit,[this])},isHorizontal:function(){return this.options.position==="top"||this.options.position==="bottom"},isFullWidth:function(){return this.options.fullWidth},getRightValue:function i(n){return n===null||typeof n=="undefined"?NaN:typeof n=="number"&&isNaN(n)?NaN:typeof n=="object"?n instanceof Date||n.isValid?n:i(this.isHorizontal()?n.x:n.y):n},getLabelForIndex:t.noop,getPixelForValue:t.noop,getValueForPixel:t.noop,getPixelForTick:function(n,t){var u,f;if(this.isHorizontal()){var e=this.width-(this.paddingLeft+this.paddingRight),i=e/Math.max(this.ticks.length-(this.options.gridLines.offsetGridLines?0:1),1),r=i*n+this.paddingLeft;return t&&(r+=i/2),u=this.left+Math.round(r),u+(this.isFullWidth()?this.margins.left:0)}return f=this.height-(this.paddingTop+this.paddingBottom),this.top+n*(f/(this.ticks.length-1))},getPixelForDecimal:function(n){if(this.isHorizontal()){var t=this.width-(this.paddingLeft+this.paddingRight),i=t*n+this.paddingLeft,r=this.left+Math.round(i);return r+(this.isFullWidth()?this.margins.left:0)}return this.top+n*this.height},getBasePixel:function(){var n=this,t=n.min,i=n.max;return n.getPixelForValue(n.beginAtZero?0:t<0&&i<0?i:t>0&&i>0?t:0)},draw:function(i){var e=this.options,ot,st,ht,ct,lt;if(e.display){var r=this.ctx,s=n.defaults.global,f=e.ticks,u=e.gridLines,h=e.scaleLabel,c,a=this.labelRotation!==0,o,v,y,at=f.autoSkip,p;f.maxTicksLimit&&(p=f.maxTicksLimit);var vt=t.getValueOrDefault(f.fontColor,s.defaultFontColor),rt=t.getValueOrDefault(f.fontSize,s.defaultFontSize),yt=t.getValueOrDefault(f.fontStyle,s.defaultFontStyle),pt=t.getValueOrDefault(f.fontFamily,s.defaultFontFamily),ut=t.fontString(rt,yt,pt),w=u.tickMarkLength,ft=t.getValueOrDefault(h.fontColor,s.defaultFontColor),l=t.getValueOrDefault(h.fontSize,s.defaultFontSize),wt=t.getValueOrDefault(h.fontStyle,s.defaultFontStyle),bt=t.getValueOrDefault(h.fontFamily,s.defaultFontFamily),et=t.fontString(l,wt,bt),b=t.toRadians(this.labelRotation),kt=Math.cos(b),dt=Math.sin(b),d=this.longestLabelWidth*kt,gt=rt*dt;if(r.fillStyle=vt,this.isHorizontal()){if(c=!0,ot=e.position==="bottom"?this.top:this.bottom-w,st=e.position==="bottom"?this.top+w:this.bottom,o=!1,a&&(d/=2),(d+f.autoSkipPadding)*this.ticks.length>this.width-(this.paddingLeft+this.paddingRight)&&(o=1+Math.floor((d+f.autoSkipPadding)*this.ticks.length/(this.width-(this.paddingLeft+this.paddingRight)))),p&&this.ticks.length>p)while(!o||this.ticks.length/(o||1)>p)o||(o=1),o+=1;at||(o=!1);t.each(this.ticks,function(n,s){var v=this.ticks.length===s+1,y=o>1&&s%o>0||s%o==0&&s+o>=this.ticks.length,h,l;(!y||v)&&n!==undefined&&n!==null&&(h=this.getPixelForTick(s),l=this.getPixelForTick(s,u.offsetGridLines),u.display&&(s===(typeof this.zeroLineIndex!="undefined"?this.zeroLineIndex:0)?(r.lineWidth=u.zeroLineWidth,r.strokeStyle=u.zeroLineColor,c=!0):c&&(r.lineWidth=u.lineWidth,r.strokeStyle=u.color,c=!1),h+=t.aliasPixel(r.lineWidth),r.beginPath(),u.drawTicks&&(r.moveTo(h,ot),r.lineTo(h,st)),u.drawOnChartArea&&(r.moveTo(h,i.top),r.lineTo(h,i.bottom)),r.stroke()),f.display&&(r.save(),r.translate(l+f.labelOffset,a?this.top+12:e.position==="top"?this.bottom-w:this.top+w),r.rotate(b*-1),r.font=ut,r.textAlign=a?"right":"center",r.textBaseline=a?"middle":e.position==="top"?"bottom":"top",r.fillText(n,0,0),r.restore()))},this);h.display&&(r.textAlign="center",r.textBaseline="middle",r.fillStyle=ft,r.font=et,v=this.left+(this.right-this.left)/2,y=e.position==="bottom"?this.bottom-l/2:this.top+l/2,r.fillText(h.labelString,v,y))}else c=!0,ht=e.position==="right"?this.left:this.right-5,ct=e.position==="right"?this.left+5:this.right,t.each(this.ticks,function(n,o){var s,h,l;n!==undefined&&n!==null&&(s=this.getPixelForTick(o),u.display&&(o===(typeof this.zeroLineIndex!="undefined"?this.zeroLineIndex:0)?(r.lineWidth=u.zeroLineWidth,r.strokeStyle=u.zeroLineColor,c=!0):c&&(r.lineWidth=u.lineWidth,r.strokeStyle=u.color,c=!1),s+=t.aliasPixel(r.lineWidth),r.beginPath(),u.drawTicks&&(r.moveTo(ht,s),r.lineTo(ct,s)),u.drawOnChartArea&&(r.moveTo(i.left,s),r.lineTo(i.right,s)),r.stroke()),f.display&&(l=this.getPixelForTick(o,u.offsetGridLines),r.save(),e.position==="left"?f.mirror?(h=this.right+f.padding,r.textAlign="left"):(h=this.right-f.padding,r.textAlign="right"):f.mirror?(h=this.left-f.padding,r.textAlign="right"):(h=this.left+f.padding,r.textAlign="left"),r.translate(h,l+f.labelOffset),r.rotate(b*-1),r.font=ut,r.textBaseline="middle",r.fillText(n,0,0),r.restore()))},this),h.display&&(v=e.position==="left"?this.left+l/2:this.right-l/2,y=this.top+(this.bottom-this.top)/2,lt=e.position==="left"?-.5*Math.PI:.5*Math.PI,r.save(),r.translate(v,y),r.rotate(lt),r.textAlign="center",r.fillStyle=ft,r.font=et,r.textBaseline="middle",r.fillText(h.labelString,0,0),r.restore());if(u.drawBorder){r.lineWidth=u.lineWidth;r.strokeStyle=u.color;var g=this.left,nt=this.right,tt=this.top,it=this.bottom,k=t.aliasPixel(r.lineWidth);this.isHorizontal()?(tt=it=e.position==="top"?this.bottom:this.top,tt+=k,it+=k):(g=nt=e.position==="left"?this.right:this.left,g+=k,nt+=k);r.beginPath();r.moveTo(g,tt);r.lineTo(nt,it);r.stroke()}}}})}},{}],31:[function(n,t){"use strict";t.exports=function(n){var t=n.helpers;n.scaleService={constructors:{},defaults:{},registerScaleType:function(n,i,r){this.constructors[n]=i;this.defaults[n]=t.clone(r)},getScaleConstructor:function(n){return this.constructors.hasOwnProperty(n)?this.constructors[n]:undefined},getScaleDefaults:function(i){return this.defaults.hasOwnProperty(i)?t.scaleMerge(n.defaults.scale,this.defaults[i]):{}},updateScaleDefaults:function(n,i){var r=this.defaults;r.hasOwnProperty(n)&&(r[n]=t.extend(r[n],i))},addScalesToLayout:function(i){t.each(i.scales,function(t){n.layoutService.addBox(i,t)})}}}},{}],32:[function(n,t){"use strict";t.exports=function(n){var i=n.helpers,t;n.defaults.global.title={display:!1,position:"top",fullWidth:!0,fontStyle:"bold",padding:10,text:""};t=i.noop;n.Title=n.Element.extend({initialize:function(t){i.extend(this,t);this.options=i.configMerge(n.defaults.global.title,t.options);this.legendHitBoxes=[]},beforeUpdate:t,update:function(n,t,i){return this.beforeUpdate(),this.maxWidth=n,this.maxHeight=t,this.margins=i,this.beforeSetDimensions(),this.setDimensions(),this.afterSetDimensions(),this.beforeBuildLabels(),this.buildLabels(),this.afterBuildLabels(),this.beforeFit(),this.fit(),this.afterFit(),this.afterUpdate(),this.minSize},afterUpdate:t,beforeSetDimensions:t,setDimensions:function(){this.isHorizontal()?(this.width=this.maxWidth,this.left=0,this.right=this.width):(this.height=this.maxHeight,this.top=0,this.bottom=this.height);this.paddingLeft=0;this.paddingTop=0;this.paddingRight=0;this.paddingBottom=0;this.minSize={width:0,height:0}},afterSetDimensions:t,beforeBuildLabels:t,buildLabels:t,afterBuildLabels:t,beforeFit:t,fit:function(){var t=this,h=t.ctx,o=i.getValueOrDefault,u=t.options,s=n.defaults.global,f=u.display,e=o(u.fontSize,s.defaultFontSize),r=t.minSize;t.isHorizontal()?(r.width=t.maxWidth,r.height=f?e+u.padding*2:0):(r.width=f?e+u.padding*2:0,r.height=t.maxHeight);t.width=r.width;t.height=r.height},afterFit:t,isHorizontal:function(){var n=this.options.position;return n==="top"||n==="bottom"},draw:function(){var u=this,t=u.ctx,f=i.getValueOrDefault,r=u.options,e=n.defaults.global;if(r.display){var s=f(r.fontSize,e.defaultFontSize),p=f(r.fontStyle,e.defaultFontStyle),w=f(r.fontFamily,e.defaultFontFamily),b=i.fontString(s,p,w),a=0,h,c,o=u.top,l=u.left,v=u.bottom,y=u.right;t.fillStyle=f(r.fontColor,e.defaultFontColor);t.font=b;u.isHorizontal()?(h=l+(y-l)/2,c=o+(v-o)/2):(h=r.position==="left"?l+s/2:y-s/2,c=o+(v-o)/2,a=Math.PI*(r.position==="left"?-.5:.5));t.save();t.translate(h,c);t.rotate(a);t.textAlign="center";t.textBaseline="middle";t.fillText(r.text,0,0);t.restore()}}})}},{}],33:[function(n,t){"use strict";t.exports=function(n){function i(n,i){return i&&(t.isArray(i)?n=n.concat(i):n.push(i)),n}var t=n.helpers;n.defaults.global.tooltips={enabled:!0,custom:null,mode:"single",backgroundColor:"rgba(0,0,0,0.8)",titleFontStyle:"bold",titleSpacing:2,titleMarginBottom:6,titleColor:"#fff",titleAlign:"left",bodySpacing:2,bodyColor:"#fff",bodyAlign:"left",footerFontStyle:"bold",footerSpacing:2,footerMarginTop:6,footerColor:"#fff",footerAlign:"left",yPadding:6,xPadding:6,yAlign:"center",xAlign:"center",caretSize:5,cornerRadius:6,multiKeyBackground:"#fff",callbacks:{beforeTitle:t.noop,title:function(n,t){var i="";return n.length>0&&(n[0].xLabel?i=n[0].xLabel:t.labels.length>0&&n[0].index<t.labels.length&&(i=t.labels[n[0].index])),i},afterTitle:t.noop,beforeBody:t.noop,beforeLabel:t.noop,label:function(n,t){var i=t.datasets[n.datasetIndex].label||"";return i+": "+n.yLabel},afterLabel:t.noop,afterBody:t.noop,beforeFooter:t.noop,footer:t.noop,afterFooter:t.noop}};n.Tooltip=n.Element.extend({initialize:function(){var r=n.defaults.global,u=this._options,i=u.tooltips;t.extend(this,{_model:{xPadding:i.xPadding,yPadding:i.yPadding,xAlign:i.yAlign,yAlign:i.xAlign,bodyColor:i.bodyColor,_bodyFontFamily:t.getValueOrDefault(i.bodyFontFamily,r.defaultFontFamily),_bodyFontStyle:t.getValueOrDefault(i.bodyFontStyle,r.defaultFontStyle),_bodyAlign:i.bodyAlign,bodyFontSize:t.getValueOrDefault(i.bodyFontSize,r.defaultFontSize),bodySpacing:i.bodySpacing,titleColor:i.titleColor,_titleFontFamily:t.getValueOrDefault(i.titleFontFamily,r.defaultFontFamily),_titleFontStyle:t.getValueOrDefault(i.titleFontStyle,r.defaultFontStyle),titleFontSize:t.getValueOrDefault(i.titleFontSize,r.defaultFontSize),_titleAlign:i.titleAlign,titleSpacing:i.titleSpacing,titleMarginBottom:i.titleMarginBottom,footerColor:i.footerColor,_footerFontFamily:t.getValueOrDefault(i.footerFontFamily,r.defaultFontFamily),_footerFontStyle:t.getValueOrDefault(i.footerFontStyle,r.defaultFontStyle),footerFontSize:t.getValueOrDefault(i.footerFontSize,r.defaultFontSize),_footerAlign:i.footerAlign,footerSpacing:i.footerSpacing,footerMarginTop:i.footerMarginTop,caretSize:i.caretSize,cornerRadius:i.cornerRadius,backgroundColor:i.backgroundColor,opacity:0,legendColorBackground:i.multiKeyBackground}})},getTitle:function(){var t=this._options.tooltips.callbacks.beforeTitle.apply(this,arguments),r=this._options.tooltips.callbacks.title.apply(this,arguments),u=this._options.tooltips.callbacks.afterTitle.apply(this,arguments),n=[];return n=i(n,t),n=i(n,r),i(n,u)},getBeforeBody:function(){var n=this._options.tooltips.callbacks.beforeBody.apply(this,arguments);return t.isArray(n)?n:n!==undefined?[n]:[]},getBody:function(n,i){var r=[];return t.each(n,function(n){t.pushAllIfDefined(this._options.tooltips.callbacks.beforeLabel.call(this,n,i),r);t.pushAllIfDefined(this._options.tooltips.callbacks.label.call(this,n,i),r);t.pushAllIfDefined(this._options.tooltips.callbacks.afterLabel.call(this,n,i),r)},this),r},getAfterBody:function(){var n=this._options.tooltips.callbacks.afterBody.apply(this,arguments);return t.isArray(n)?n:n!==undefined?[n]:[]},getFooter:function(){var t=this._options.tooltips.callbacks.beforeFooter.apply(this,arguments),r=this._options.tooltips.callbacks.footer.apply(this,arguments),u=this._options.tooltips.callbacks.afterFooter.apply(this,arguments),n=[];return n=i(n,t),n=i(n,r),i(n,u)},getAveragePosition:function(n){var i,u,f,e,r;if(!n.length)return!1;for(i=[],u=[],t.each(n,function(n){if(n&&n.hasValue()){var t=n.tooltipPosition();i.push(t.x);u.push(t.y)}}),f=0,e=0,r=0;r<i.length;r++)f+=i[r],e+=u[r];return{x:Math.round(f/i.length),y:Math.round(e/i.length)}},update:function(n){var f,e;if(this._active.length){this._model.opacity=1;var i=this._active[0],o=[],u,r=[];this._options.tooltips.mode==="single"?(f=i._yScale||i._scale,r.push({xLabel:i._xScale?i._xScale.getLabelForIndex(i._index,i._datasetIndex):"",yLabel:f?f.getLabelForIndex(i._index,i._datasetIndex):"",index:i._index,datasetIndex:i._datasetIndex}),u=this.getAveragePosition(this._active)):(t.each(this._data.datasets,function(n,t){var e,u,f;this._chartInstance.isDatasetVisible(t)&&(e=this._chartInstance.getDatasetMeta(t),u=e.data[i._index],u&&(f=i._yScale||i._scale,r.push({xLabel:u._xScale?u._xScale.getLabelForIndex(u._index,u._datasetIndex):"",yLabel:f?f.getLabelForIndex(u._index,u._datasetIndex):"",index:i._index,datasetIndex:t})))},this),t.each(this._active,function(n){n&&o.push({borderColor:n._view.borderColor,backgroundColor:n._view.backgroundColor})},null),u=this.getAveragePosition(this._active));t.extend(this._model,{title:this.getTitle(r,this._data),beforeBody:this.getBeforeBody(r,this._data),body:this.getBody(r,this._data),afterBody:this.getAfterBody(r,this._data),footer:this.getFooter(r,this._data)});t.extend(this._model,{x:Math.round(u.x),y:Math.round(u.y),caretPadding:t.getValueOrDefault(u.padding,2),labelColors:o});e=this.getTooltipSize(this._model);this.determineAlignment(e);t.extend(this._model,this.getBackgroundPoint(this._model,e))}else this._model.opacity=0;return n&&this._options.tooltips.custom&&this._options.tooltips.custom.call(this,this._model),this},getTooltipSize:function(n){var r=this._chart.ctx,i={height:n.yPadding*2,width:0},u=n.body.length+n.beforeBody.length+n.afterBody.length;return i.height+=n.title.length*n.titleFontSize,i.height+=(n.title.length-1)*n.titleSpacing,i.height+=n.title.length?n.titleMarginBottom:0,i.height+=u*n.bodyFontSize,i.height+=u?(u-1)*n.bodySpacing:0,i.height+=n.footer.length?n.footerMarginTop:0,i.height+=n.footer.length*n.footerFontSize,i.height+=n.footer.length?(n.footer.length-1)*n.footerSpacing:0,r.font=t.fontString(n.titleFontSize,n._titleFontStyle,n._titleFontFamily),t.each(n.title,function(n){i.width=Math.max(i.width,r.measureText(n).width)}),r.font=t.fontString(n.bodyFontSize,n._bodyFontStyle,n._bodyFontFamily),t.each(n.beforeBody.concat(n.afterBody),function(n){i.width=Math.max(i.width,r.measureText(n).width)}),t.each(n.body,function(t){i.width=Math.max(i.width,r.measureText(t).width+(this._options.tooltips.mode!=="single"?n.bodyFontSize+2:0))},this),r.font=t.fontString(n.footerFontSize,n._footerFontStyle,n._footerFontFamily),t.each(n.footer,function(n){i.width=Math.max(i.width,r.measureText(n).width)}),i.width+=2*n.xPadding,i},determineAlignment:function(n){this._model.y<n.height?this._model.yAlign="top":this._model.y>this._chart.height-n.height&&(this._model.yAlign="bottom");var t,i,u,f,r,e=this,o=(this._chartInstance.chartArea.left+this._chartInstance.chartArea.right)/2,s=(this._chartInstance.chartArea.top+this._chartInstance.chartArea.bottom)/2;this._model.yAlign==="center"?(t=function(n){return n<=o},i=function(n){return n>o}):(t=function(t){return t<=n.width/2},i=function(t){return t>=e._chart.width-n.width/2});u=function(t){return t+n.width>e._chart.width};f=function(t){return t-n.width<0};r=function(n){return n<=s?"top":"bottom"};t(this._model.x)?(this._model.xAlign="left",u(this._model.x)&&(this._model.xAlign="center",this._model.yAlign=r(this._model.y))):i(this._model.x)&&(this._model.xAlign="right",f(this._model.x)&&(this._model.xAlign="center",this._model.yAlign=r(this._model.y)))},getBackgroundPoint:function(n,t){var i={x:n.x,y:n.y};return n.xAlign==="right"?i.x-=t.width:n.xAlign==="center"&&(i.x-=t.width/2),n.yAlign==="top"?i.y+=n.caretPadding+n.caretSize:i.y-=n.yAlign==="bottom"?t.height+n.caretPadding+n.caretSize:t.height/2,n.yAlign==="center"?n.xAlign==="left"?i.x+=n.caretPadding+n.caretSize:n.xAlign==="right"&&(i.x-=n.caretPadding+n.caretSize):n.xAlign==="left"?i.x-=n.cornerRadius+n.caretPadding:n.xAlign==="right"&&(i.x+=n.cornerRadius+n.caretPadding),i},drawCaret:function(n,i,r){var u=this._view,s=this._chart.ctx,f,e,h,o,c,l,a;u.yAlign==="center"?(u.xAlign==="left"?(f=n.x,e=f-u.caretSize,h=f):(f=n.x+i.width,e=f+u.caretSize,h=f),c=n.y+i.height/2,o=c-u.caretSize,l=c+u.caretSize):(u.xAlign==="left"?(f=n.x+u.cornerRadius,e=f+u.caretSize,h=e+u.caretSize):u.xAlign==="right"?(f=n.x+i.width-u.cornerRadius,e=f-u.caretSize,h=e-u.caretSize):(e=n.x+i.width/2,f=e-u.caretSize,h=e+u.caretSize),u.yAlign==="top"?(o=n.y,c=o-u.caretSize,l=o):(o=n.y+i.height,c=o+u.caretSize,l=o));a=t.color(u.backgroundColor);s.fillStyle=a.alpha(r*a.alpha()).rgbString();s.beginPath();s.moveTo(f,o);s.lineTo(e,c);s.lineTo(h,l);s.closePath();s.fill()},drawTitle:function(n,i,r,u){if(i.title.length){r.textAlign=i._titleAlign;r.textBaseline="top";var f=t.color(i.titleColor);r.fillStyle=f.alpha(u*f.alpha()).rgbString();r.font=t.fontString(i.titleFontSize,i._titleFontStyle,i._titleFontFamily);t.each(i.title,function(t,u){r.fillText(t,n.x,n.y);n.y+=i.titleFontSize+i.titleSpacing;u+1===i.title.length&&(n.y+=i.titleMarginBottom-i.titleSpacing)})}},drawBody:function(n,i,r,u){r.textAlign=i._bodyAlign;r.textBaseline="top";var f=t.color(i.bodyColor);r.fillStyle=f.alpha(u*f.alpha()).rgbString();r.font=t.fontString(i.bodyFontSize,i._bodyFontStyle,i._bodyFontFamily);t.each(i.beforeBody,function(t){r.fillText(t,n.x,n.y);n.y+=i.bodyFontSize+i.bodySpacing});t.each(i.body,function(f,e){this._options.tooltips.mode!=="single"&&(r.fillStyle=t.color(i.legendColorBackground).alpha(u).rgbaString(),r.fillRect(n.x,n.y,i.bodyFontSize,i.bodyFontSize),r.strokeStyle=t.color(i.labelColors[e].borderColor).alpha(u).rgbaString(),r.strokeRect(n.x,n.y,i.bodyFontSize,i.bodyFontSize),r.fillStyle=t.color(i.labelColors[e].backgroundColor).alpha(u).rgbaString(),r.fillRect(n.x+1,n.y+1,i.bodyFontSize-2,i.bodyFontSize-2),r.fillStyle=t.color(i.bodyColor).alpha(u).rgbaString());r.fillText(f,n.x+(this._options.tooltips.mode!=="single"?i.bodyFontSize+2:0),n.y);n.y+=i.bodyFontSize+i.bodySpacing},this);t.each(i.afterBody,function(t){r.fillText(t,n.x,n.y);n.y+=i.bodyFontSize});n.y-=i.bodySpacing},drawFooter:function(n,i,r,u){if(i.footer.length){n.y+=i.footerMarginTop;r.textAlign=i._footerAlign;r.textBaseline="top";var f=t.color(i.footerColor);r.fillStyle=f.alpha(u*f.alpha()).rgbString();r.font=t.fontString(i.footerFontSize,i._footerFontStyle,i._footerFontFamily);t.each(i.footer,function(t){r.fillText(t,n.x,n.y);n.y+=i.footerFontSize+i.footerSpacing})}},draw:function(){var r=this._chart.ctx,n=this._view,e;if(n.opacity!==0){var o=n.caretPadding,f=this.getTooltipSize(n),i={x:n.x,y:n.y},u=Math.abs(n.opacity<.001)?0:n.opacity;this._options.tooltips.enabled&&(e=t.color(n.backgroundColor),r.fillStyle=e.alpha(u*e.alpha()).rgbString(),t.drawRoundedRectangle(r,i.x,i.y,f.width,f.height,n.cornerRadius),r.fill(),this.drawCaret(i,f,u,o),i.x+=n.xPadding,i.y+=n.yPadding,this.drawTitle(i,n,r,u),this.drawBody(i,n,r,u),this.drawFooter(i,n,r,u))}}})}},{}],34:[function(n,t){"use strict";t.exports=function(n){var i=n.helpers,t=n.defaults.global;t.elements.arc={backgroundColor:t.defaultColor,borderColor:"#fff",borderWidth:2};n.elements.Arc=n.Element.extend({inLabelRange:function(n){var t=this._view;return t?Math.pow(n-t.x,2)<Math.pow(t.radius+t.hoverRadius,2):!1},inRange:function(n,t){var r=this._view,h,c;if(r){for(var o=i.getAngleFromPoint(r,{x:n,y:t}),u=o.angle,s=o.distance,e=r.startAngle,f=r.endAngle;f<e;)f+=2*Math.PI;while(u>f)u-=2*Math.PI;while(u<e)u+=2*Math.PI;return h=u>=e&&u<=f,c=s>=r.innerRadius&&s<=r.outerRadius,h&&c}return!1},tooltipPosition:function(){var n=this._view,t=n.startAngle+(n.endAngle-n.startAngle)/2,i=(n.outerRadius-n.innerRadius)/2+n.innerRadius;return{x:n.x+Math.cos(t)*i,y:n.y+Math.sin(t)*i}},draw:function(){var t=this._chart.ctx,n=this._view,i=n.startAngle,r=n.endAngle;t.beginPath();t.arc(n.x,n.y,n.outerRadius,i,r);t.arc(n.x,n.y,n.innerRadius,r,i,!0);t.closePath();t.strokeStyle=n.borderColor;t.lineWidth=n.borderWidth;t.fillStyle=n.backgroundColor;t.fill();t.lineJoin="bevel";n.borderWidth&&t.stroke()}})}},{}],35:[function(n,t){"use strict";t.exports=function(n){var t=n.helpers,i=n.defaults.global;n.defaults.global.elements.line={tension:.4,backgroundColor:i.defaultColor,borderWidth:3,borderColor:i.defaultColor,borderCapStyle:"butt",borderDash:[],borderDashOffset:0,borderJoinStyle:"miter",fill:!0};n.elements.Line=n.Element.extend({lineToNextPoint:function(n,t,i,r,u){var f=this._chart.ctx;t._view.skip?r.call(this,n,t,i):n._view.skip?u.call(this,n,t,i):t._view.tension===0?f.lineTo(t._view.x,t._view.y):f.bezierCurveTo(n._view.controlPointNextX,n._view.controlPointNextY,t._view.controlPointPreviousX,t._view.controlPointPreviousY,t._view.x,t._view.y)},draw:function(){function s(t){f._view.skip||e._view.skip?t&&n.lineTo(o._view.scaleZero.x,o._view.scaleZero.y):n.bezierCurveTo(e._view.controlPointNextX,e._view.controlPointNextY,f._view.controlPointPreviousX,f._view.controlPointPreviousY,f._view.x,f._view.y)}var o=this,r=this._view,n=this._chart.ctx,f=this._children[0],e=this._children[this._children.length-1],u;n.save();this._children.length>0&&r.fill&&(n.beginPath(),t.each(this._children,function(i,u){var e=t.previousItem(this._children,u),f=t.nextItem(this._children,u);u===0?(this._loop?n.moveTo(r.scaleZero.x,r.scaleZero.y):n.moveTo(i._view.x,r.scaleZero),i._view.skip?this._loop||n.moveTo(f._view.x,this._view.scaleZero):n.lineTo(i._view.x,i._view.y)):this.lineToNextPoint(e,i,f,function(t,i,r){this._loop?n.lineTo(this._view.scaleZero.x,this._view.scaleZero.y):(n.lineTo(t._view.x,this._view.scaleZero),n.moveTo(r._view.x,this._view.scaleZero))},function(t,i){n.lineTo(i._view.x,i._view.y)})},this),this._loop?s(!0):(n.lineTo(this._children[this._children.length-1]._view.x,r.scaleZero),n.lineTo(this._children[0]._view.x,r.scaleZero)),n.fillStyle=r.backgroundColor||i.defaultColor,n.closePath(),n.fill());u=i.elements.line;n.lineCap=r.borderCapStyle||u.borderCapStyle;n.setLineDash&&n.setLineDash(r.borderDash||u.borderDash);n.lineDashOffset=r.borderDashOffset||u.borderDashOffset;n.lineJoin=r.borderJoinStyle||u.borderJoinStyle;n.lineWidth=r.borderWidth||u.borderWidth;n.strokeStyle=r.borderColor||i.defaultColor;n.beginPath();t.each(this._children,function(i,r){var u=t.previousItem(this._children,r),f=t.nextItem(this._children,r);r===0?n.moveTo(i._view.x,i._view.y):this.lineToNextPoint(u,i,f,function(t,i,r){n.moveTo(r._view.x,r._view.y)},function(t,i){n.moveTo(i._view.x,i._view.y)})},this);this._loop&&this._children.length>0&&s();n.stroke();n.restore()}})}},{}],36:[function(n,t){"use strict";t.exports=function(n){var r=n.helpers,i=n.defaults.global,t=i.defaultColor;i.elements.point={radius:3,pointStyle:"circle",backgroundColor:t,borderWidth:1,borderColor:t,hitRadius:1,hoverRadius:4,hoverBorderWidth:1};n.elements.Point=n.Element.extend({inRange:function(n,t){var i=this._view;return i?Math.pow(n-i.x,2)+Math.pow(t-i.y,2)<Math.pow(i.hitRadius+i.radius,2):!1},inLabelRange:function(n){var t=this._view;return t?Math.pow(n-t.x,2)<Math.pow(t.radius+t.hitRadius,2):!1},tooltipPosition:function(){var n=this._view;return{x:n.x,y:n.y,padding:n.radius+n.borderWidth}},draw:function(){var c=this._view,n=this._chart.ctx,l=c.pointStyle,e=c.radius,u=c.x,f=c.y,y,a,s,h,v,o;if(!c.skip){if(typeof l=="object"&&(y=l.toString(),y==="[object HTMLImageElement]"||y==="[object HTMLCanvasElement]")){n.drawImage(l,u-l.width/2,f-l.height/2);return}if(!isNaN(e)&&!(e<=0)){n.strokeStyle=c.borderColor||t;n.lineWidth=r.getValueOrDefault(c.borderWidth,i.elements.point.borderWidth);n.fillStyle=c.backgroundColor||t;switch(l){default:n.beginPath();n.arc(u,f,e,0,Math.PI*2);n.closePath();n.fill();break;case"triangle":n.beginPath();a=3*e/Math.sqrt(3);v=a*Math.sqrt(3)/2;n.moveTo(u-a/2,f+v/3);n.lineTo(u+a/2,f+v/3);n.lineTo(u,f-2*v/3);n.closePath();n.fill();break;case"rect":o=1/Math.SQRT2*e;n.fillRect(u-o,f-o,2*o,2*o);n.strokeRect(u-o,f-o,2*o,2*o);break;case"rectRot":n.translate(u,f);n.rotate(Math.PI/4);o=1/Math.SQRT2*e;n.fillRect(-o,-o,2*o,2*o);n.strokeRect(-o,-o,2*o,2*o);n.setTransform(1,0,0,1,0,0);break;case"cross":n.beginPath();n.moveTo(u,f+e);n.lineTo(u,f-e);n.moveTo(u-e,f);n.lineTo(u+e,f);n.closePath();break;case"crossRot":n.beginPath();s=Math.cos(Math.PI/4)*e;h=Math.sin(Math.PI/4)*e;n.moveTo(u-s,f-h);n.lineTo(u+s,f+h);n.moveTo(u-s,f+h);n.lineTo(u+s,f-h);n.closePath();break;case"star":n.beginPath();n.moveTo(u,f+e);n.lineTo(u,f-e);n.moveTo(u-e,f);n.lineTo(u+e,f);s=Math.cos(Math.PI/4)*e;h=Math.sin(Math.PI/4)*e;n.moveTo(u-s,f-h);n.lineTo(u+s,f+h);n.moveTo(u-s,f+h);n.lineTo(u+s,f-h);n.closePath();break;case"line":n.beginPath();n.moveTo(u-e,f);n.lineTo(u+e,f);n.closePath();break;case"dash":n.beginPath();n.moveTo(u,f);n.lineTo(u+e,f);n.closePath()}n.stroke()}}}})}},{}],37:[function(n,t){"use strict";t.exports=function(n){var i=n.helpers,t=n.defaults.global;t.elements.rectangle={backgroundColor:t.defaultColor,borderWidth:0,borderColor:t.defaultColor,borderSkipped:"bottom"};n.elements.Rectangle=n.Element.extend({draw:function(){function h(n){return c[(o+n)%4]}var t=this._chart.ctx,n=this._view,s=n.width/2,r=n.x-s,u=n.x+s,f=n.base-(n.base-n.y),e=n.borderWidth/2,i;n.borderWidth&&(r+=e,u-=e,f+=e);t.beginPath();t.fillStyle=n.backgroundColor;t.strokeStyle=n.borderColor;t.lineWidth=n.borderWidth;var c=[[r,n.base],[r,f],[u,f],[u,n.base]],o=["bottom","left","top","right"].indexOf(n.borderSkipped,0);for(o===-1&&(o=0),t.moveTo.apply(t,h(0)),i=1;i<4;i++)t.lineTo.apply(t,h(i));t.fill();n.borderWidth&&t.stroke()},height:function(){var n=this._view;return n.base-n.y},inRange:function(n,t){var i=this._view;return i?i.y<i.base?n>=i.x-i.width/2&&n<=i.x+i.width/2&&t>=i.y&&t<=i.base:n>=i.x-i.width/2&&n<=i.x+i.width/2&&t>=i.base&&t<=i.y:!1},inLabelRange:function(n){var t=this._view;return t?n>=t.x-t.width/2&&n<=t.x+t.width/2:!1},tooltipPosition:function(){var n=this._view;return{x:n.x,y:n.y}}})}},{}],38:[function(n,t){"use strict";t.exports=function(n){var t=n.helpers,i=n.Scale.extend({determineDataLimits:function(){this.minIndex=0;this.maxIndex=this.chart.data.labels.length-1;var n;this.options.ticks.min!==undefined&&(n=t.indexOf(this.chart.data.labels,this.options.ticks.min),this.minIndex=n!==-1?n:this.minIndex);this.options.ticks.max!==undefined&&(n=t.indexOf(this.chart.data.labels,this.options.ticks.max),this.maxIndex=n!==-1?n:this.maxIndex);this.min=this.chart.data.labels[this.minIndex];this.max=this.chart.data.labels[this.maxIndex]},buildTicks:function(){this.ticks=this.minIndex===0&&this.maxIndex===this.chart.data.labels.length-1?this.chart.data.labels:this.chart.data.labels.slice(this.minIndex,this.maxIndex+1)},getLabelForIndex:function(n){return this.ticks[n]},getPixelForValue:function(n,t,i,r){var u=Math.max(this.maxIndex+1-this.minIndex-(this.options.gridLines.offsetGridLines?0:1),1);if(this.isHorizontal()){var h=this.width-(this.paddingLeft+this.paddingRight),f=h/u,e=f*(t-this.minIndex)+this.paddingLeft;return this.options.gridLines.offsetGridLines&&r&&(e+=f/2),this.left+Math.round(e)}var c=this.height-(this.paddingTop+this.paddingBottom),o=c/u,s=o*(t-this.minIndex)+this.paddingTop;return this.options.gridLines.offsetGridLines&&r&&(s+=o/2),this.top+Math.round(s)},getPixelForTick:function(n,t){return this.getPixelForValue(this.ticks[n],n+this.minIndex,null,t)},getValueForPixel:function(n){var r=Math.max(this.ticks.length-(this.options.gridLines.offsetGridLines?0:1),1),t=this.isHorizontal(),u=t?this.width-(this.paddingLeft+this.paddingRight):this.height-(this.paddingTop+this.paddingBottom),i=u/r;return this.options.gridLines.offsetGridLines&&(n-=i/2),n-=t?this.paddingLeft:this.paddingTop,n<=0?0:Math.round(n/i)}});n.scaleService.registerScaleType("category",i,{position:"bottom"})}},{}],39:[function(n,t){"use strict";t.exports=function(n){var t=n.helpers,i={position:"left",ticks:{callback:function(n,i,r){var e=r.length>3?r[2]-r[1]:r[1]-r[0],o,u,f;return Math.abs(e)>1&&n!==Math.floor(n)&&(e=n-Math.floor(n)),o=t.log10(Math.abs(e)),u="",n!==0?(f=-1*Math.floor(o),f=Math.max(Math.min(f,20),0),u=n.toFixed(f)):u="0",u}}},r=n.Scale.extend({determineDataLimits:function(){function h(t){return l?t.xAxisID===n.id:t.yAxisID===n.id}var n=this,f=n.options,i=f.ticks,r=n.chart,c=r.data,s=c.datasets,l=n.isHorizontal(),e,o;if(n.min=null,n.max=null,f.stacked){var u={},a=!1,v=!1;t.each(s,function(i,e){var o=r.getDatasetMeta(e),s,c;u[o.type]===undefined&&(u[o.type]={positiveValues:[],negativeValues:[]});s=u[o.type].positiveValues;c=u[o.type].negativeValues;r.isDatasetVisible(e)&&h(o)&&t.each(i.data,function(t,i){var r=+n.getRightValue(t);isNaN(r)||o.data[i].hidden||(s[i]=s[i]||0,c[i]=c[i]||0,f.relativePoints?s[i]=100:r<0?(v=!0,c[i]+=r):(a=!0,s[i]+=r))})});t.each(u,function(i){var r=i.positiveValues.concat(i.negativeValues),u=t.min(r),f=t.max(r);n.min=n.min===null?u:Math.min(n.min,u);n.max=n.max===null?f:Math.max(n.max,f)})}else t.each(s,function(i,u){var f=r.getDatasetMeta(u);r.isDatasetVisible(u)&&h(f)&&t.each(i.data,function(t,i){var r=+n.getRightValue(t);isNaN(r)||f.data[i].hidden||(n.min===null?n.min=r:r<n.min&&(n.min=r),n.max===null?n.max=r:r>n.max&&(n.max=r))})});i.beginAtZero&&(e=t.sign(n.min),o=t.sign(n.max),e<0&&o<0?n.max=0:e>0&&o>0&&(n.min=0));i.min!==undefined?n.min=i.min:i.suggestedMin!==undefined&&(n.min=Math.min(n.min,i.suggestedMin));i.max!==undefined?n.max=i.max:i.suggestedMax!==undefined&&(n.max=Math.max(n.max,i.suggestedMax));n.min===n.max&&(n.max++,i.beginAtZero||n.min--)},buildTicks:function(){var i=this,w=i.options,r=w.ticks,c=t.getValueOrDefault,l=i.isHorizontal(),f=i.ticks=[],o,a,u,v,y,s;l?o=Math.min(r.maxTicksLimit?r.maxTicksLimit:11,Math.ceil(i.width/50)):(a=c(r.fontSize,n.defaults.global.defaultFontSize),o=Math.min(r.maxTicksLimit?r.maxTicksLimit:11,Math.ceil(i.height/(2*a))));o=Math.max(2,o);v=r.fixedStepSize&&r.fixedStepSize>0||r.stepSize&&r.stepSize>0;v?u=c(r.fixedStepSize,r.stepSize):(y=t.niceNum(i.max-i.min,!1),u=t.niceNum(y/(o-1),!0));var h=Math.floor(i.min/u)*u,p=Math.ceil(i.max/u)*u,e=(p-h)/u;for(e=t.almostEquals(e,Math.round(e),u/1e3)?Math.round(e):Math.ceil(e),f.push(r.min!==undefined?r.min:h),s=1;s<e;++s)f.push(h+s*u);f.push(r.max!==undefined?r.max:p);l||f.reverse();i.max=t.max(f);i.min=t.min(f);r.reverse?(f.reverse(),i.start=i.max,i.end=i.min):(i.start=i.min,i.end=i.max)},getLabelForIndex:function(n,t){return+this.getRightValue(this.chart.data.datasets[t].data[n])},convertTicksToLabels:function(){var t=this;t.ticksAsNumbers=t.ticks.slice();t.zeroLineIndex=t.ticks.indexOf(0);n.Scale.prototype.convertTicksToLabels.call(t)},getPixelForValue:function(n){var t=this,f=t.paddingLeft,e=t.paddingBottom,u=t.start,o=+t.getRightValue(n),i,r,s=t.end-u;return t.isHorizontal()?(r=t.width-(f+t.paddingRight),i=t.left+r/s*(o-u),Math.round(i+f)):(r=t.height-(t.paddingTop+e),i=t.bottom-e-r/s*(o-u),Math.round(i))},getValueForPixel:function(n){var t=this,i=t.isHorizontal(),r=t.paddingLeft,u=t.paddingBottom,f=i?t.width-(r+t.paddingRight):t.height-(t.paddingTop+u),e=(i?n-t.left-r:t.bottom-u-n)/f;return t.start+(t.end-t.start)*e},getPixelForTick:function(n,t){return this.getPixelForValue(this.ticksAsNumbers[n],null,null,t)}});n.scaleService.registerScaleType("linear",r,i)}},{}],40:[function(n,t){"use strict";t.exports=function(n){var t=n.helpers,i={position:"left",ticks:{callback:function(n,i,r){var u=n/Math.pow(10,Math.floor(t.log10(n)));return u===1||u===2||u===5||i===0||i===r.length-1?n.toExponential():""}}},r=n.Scale.extend({determineDataLimits:function(){function s(t){return c?t.xAxisID===n.id:t.yAxisID===n.id}var n=this,u=n.options,f=u.ticks,i=n.chart,h=i.data,e=h.datasets,o=t.getValueOrDefault,c=n.isHorizontal(),r;n.min=null;n.max=null;u.stacked?(r={},t.each(e,function(f,e){var o=i.getDatasetMeta(e);i.isDatasetVisible(e)&&s(o)&&(r[o.type]===undefined&&(r[o.type]=[]),t.each(f.data,function(t,i){var f=r[o.type],e=+n.getRightValue(t);isNaN(e)||o.data[i].hidden||(f[i]=f[i]||0,u.relativePoints?f[i]=100:f[i]+=e)}))}),t.each(r,function(i){var r=t.min(i),u=t.max(i);n.min=n.min===null?r:Math.min(n.min,r);n.max=n.max===null?u:Math.max(n.max,u)})):t.each(e,function(r,u){var f=i.getDatasetMeta(u);i.isDatasetVisible(u)&&s(f)&&t.each(r.data,function(t,i){var r=+n.getRightValue(t);isNaN(r)||f.data[i].hidden||(n.min===null?n.min=r:r<n.min&&(n.min=r),n.max===null?n.max=r:r>n.max&&(n.max=r))})});n.min=o(f.min,n.min);n.max=o(f.max,n.max);n.min===n.max&&(n.min!==0&&n.min!==null?(n.min=Math.pow(10,Math.floor(t.log10(n.min))-1),n.max=Math.pow(10,Math.floor(t.log10(n.max))+1)):(n.min=1,n.max=10))},buildTicks:function(){for(var n=this,h=n.options,e=h.ticks,o=t.getValueOrDefault,i=n.ticks=[],r=o(e.min,Math.pow(10,Math.floor(t.log10(n.min)))),u,f,s;r<n.max;)i.push(r),u=Math.floor(t.log10(r)),f=Math.floor(r/Math.pow(10,u))+1,f===10&&(f=1,++u),r=f*Math.pow(10,u);s=o(e.max,r);i.push(s);n.isHorizontal()||i.reverse();n.max=t.max(i);n.min=t.min(i);e.reverse?(i.reverse(),n.start=n.max,n.end=n.min):(n.start=n.min,n.end=n.max)},convertTicksToLabels:function(){this.tickValues=this.ticks.slice();n.Scale.prototype.convertTicksToLabels.call(this)},getLabelForIndex:function(n,t){return+this.getRightValue(this.chart.data.datasets[t].data[n])},getPixelForTick:function(n,t){return this.getPixelForValue(this.tickValues[n],null,null,t)},getPixelForValue:function(n){var i=this,u,r,e=i.start,f=+i.getRightValue(n),s=t.log10(i.end)-t.log10(e),h=i.paddingTop,c=i.paddingBottom,o=i.paddingLeft;return i.isHorizontal()?f===0?r=i.left+o:(u=i.width-(o+i.paddingRight),r=i.left+u/s*(t.log10(f)-t.log10(e))+o):f===0?r=i.top+h:(u=i.height-(h+c),r=i.bottom-c-u/s*(t.log10(f)-t.log10(e))),r},getValueForPixel:function(n){var i=this,f=t.log10(i.end)-t.log10(i.start),u,r;return i.isHorizontal()?(r=i.width-(i.paddingLeft+i.paddingRight),u=i.start*Math.pow(10,(n-i.left-i.paddingLeft)*f/r)):(r=i.height-(i.paddingTop+i.paddingBottom),u=Math.pow(10,(i.bottom-i.paddingBottom-n)*f/r)/i.start),u}});n.scaleService.registerScaleType("logarithmic",r,i)}},{}],41:[function(n,t){"use strict";t.exports=function(n){var t=n.helpers,i=n.defaults.global,r={display:!0,animate:!0,lineArc:!1,position:"chartArea",angleLines:{display:!0,color:"rgba(0, 0, 0, 0.1)",lineWidth:1},ticks:{showLabelBackdrop:!0,backdropColor:"rgba(255,255,255,0.75)",backdropPaddingY:2,backdropPaddingX:2},pointLabels:{fontSize:10,callback:function(n){return n}}},u=n.Scale.extend({getValueCount:function(){return this.chart.data.labels.length},setDimensions:function(){var n=this.options,r,u;this.width=this.maxWidth;this.height=this.maxHeight;this.xCenter=Math.round(this.width/2);this.yCenter=Math.round(this.height/2);r=t.min([this.height,this.width]);u=t.getValueOrDefault(n.ticks.fontSize,i.defaultFontSize);this.drawingArea=n.display?r/2-(u/2+n.ticks.backdropPaddingY):r/2},determineDataLimits:function(){if(this.min=null,this.max=null,t.each(this.chart.data.datasets,function(n,i){if(this.chart.isDatasetVisible(i)){var r=this.chart.getDatasetMeta(i);t.each(n.data,function(n,t){var i=+this.getRightValue(n);isNaN(i)||r.data[t].hidden||(this.min===null?this.min=i:i<this.min&&(this.min=i),this.max===null?this.max=i:i>this.max&&(this.max=i))},this)}},this),this.options.ticks.beginAtZero){var n=t.sign(this.min),i=t.sign(this.max);n<0&&i<0?this.max=0:n>0&&i>0&&(this.min=0)}this.options.ticks.min!==undefined?this.min=this.options.ticks.min:this.options.ticks.suggestedMin!==undefined&&(this.min=Math.min(this.min,this.options.ticks.suggestedMin));this.options.ticks.max!==undefined?this.max=this.options.ticks.max:this.options.ticks.suggestedMax!==undefined&&(this.max=Math.max(this.max,this.options.ticks.suggestedMax));this.min===this.max&&(this.min--,this.max++)},buildTicks:function(){var e,r,u;this.ticks=[];e=t.getValueOrDefault(this.options.ticks.fontSize,i.defaultFontSize);r=Math.min(this.options.ticks.maxTicksLimit?this.options.ticks.maxTicksLimit:11,Math.ceil(this.drawingArea/(1.5*e)));r=Math.max(2,r);var s=t.niceNum(this.max-this.min,!1),n=t.niceNum(s/(r-1),!0),f=Math.floor(this.min/n)*n,o=Math.ceil(this.max/n)*n,h=Math.ceil((o-f)/n);for(this.ticks.push(this.options.ticks.min!==undefined?this.options.ticks.min:f),u=1;u<h;++u)this.ticks.push(f+u*n);this.ticks.push(this.options.ticks.max!==undefined?this.options.ticks.max:o);this.max=t.max(this.ticks);this.min=t.min(this.ticks);this.options.ticks.reverse?(this.ticks.reverse(),this.start=this.max,this.end=this.min):(this.start=this.min,this.end=this.max);this.zeroLineIndex=this.ticks.indexOf(0)},convertTicksToLabels:function(){n.Scale.prototype.convertTicksToLabels.call(this);this.pointLabels=this.chart.data.labels.map(this.options.pointLabels.callback,this)},getLabelForIndex:function(n,t){return+this.getRightValue(this.chart.data.datasets[t].data[n])},fit:function(){var c=this.options.pointLabels,v=t.getValueOrDefault(c.fontSize,i.defaultFontSize),d=t.getValueOrDefault(c.fontStyle,i.defaultFontStyle),g=t.getValueOrDefault(c.fontFamily,i.defaultFontFamily),nt=t.fontString(v,d,g),y=t.min([this.height/2-v-5,this.width/2]),r,n,u,o,s=this.width,l,p,h=0,a,w,b,k,f,e;for(this.ctx.font=nt,n=0;n<this.getValueCount();n++)r=this.getPointPosition(n,y),u=this.ctx.measureText(this.pointLabels[n]?this.pointLabels[n]:"").width+5,n===0||n===this.getValueCount()/2?(o=u/2,r.x+o>s&&(s=r.x+o,l=n),r.x-o<h&&(h=r.x-o,a=n)):n<this.getValueCount()/2?r.x+u>s&&(s=r.x+u,l=n):n>this.getValueCount()/2&&r.x-u<h&&(h=r.x-u,a=n);b=h;k=Math.ceil(s-this.width);p=this.getIndexAngle(l);w=this.getIndexAngle(a);f=k/Math.sin(p+Math.PI/2);e=b/Math.sin(w+Math.PI/2);f=t.isNumber(f)?f:0;e=t.isNumber(e)?e:0;this.drawingArea=Math.round(y-(e+f)/2);this.setCenterPoint(e,f)},setCenterPoint:function(n,t){var i=this.width-t-this.drawingArea,r=n+this.drawingArea;this.xCenter=Math.round((r+i)/2+this.left);this.yCenter=Math.round(this.height/2+this.top)},getIndexAngle:function(n){var t=Math.PI*2/this.getValueCount();return n*t-Math.PI/2},getDistanceFromCenterForValue:function(n){if(n===null)return 0;var t=this.drawingArea/(this.max-this.min);return this.options.reverse?(this.max-n)*t:(n-this.min)*t},getPointPosition:function(n,t){var i=this.getIndexAngle(n);return{x:Math.round(Math.cos(i)*t)+this.xCenter,y:Math.round(Math.sin(i)*t)+this.yCenter}},getPointPositionForValue:function(n,t){return this.getPointPosition(n,this.getDistanceFromCenterForValue(t))},getBasePosition:function(){var n=this,t=n.min,i=n.max;return n.getPointPositionForValue(0,n.beginAtZero?0:t<0&&i<0?i:t>0&&i>0?t:0)},draw:function(){var n,r,f;if(this.options.display&&(n=this.ctx,t.each(this.ticks,function(r,u){var o,s,f,e,c;if(u>0||this.options.reverse){if(o=this.getDistanceFromCenterForValue(this.ticks[u]),s=this.yCenter-o,this.options.gridLines.display)if(n.strokeStyle=this.options.gridLines.color,n.lineWidth=this.options.gridLines.lineWidth,this.options.lineArc)n.beginPath(),n.arc(this.xCenter,this.yCenter,o,0,Math.PI*2),n.closePath(),n.stroke();else{for(n.beginPath(),f=0;f<this.getValueCount();f++)e=this.getPointPosition(f,this.getDistanceFromCenterForValue(this.ticks[u])),f===0?n.moveTo(e.x,e.y):n.lineTo(e.x,e.y);n.closePath();n.stroke()}if(this.options.ticks.display){var l=t.getValueOrDefault(this.options.ticks.fontColor,i.defaultFontColor),h=t.getValueOrDefault(this.options.ticks.fontSize,i.defaultFontSize),a=t.getValueOrDefault(this.options.ticks.fontStyle,i.defaultFontStyle),v=t.getValueOrDefault(this.options.ticks.fontFamily,i.defaultFontFamily),y=t.fontString(h,a,v);n.font=y;this.options.ticks.showLabelBackdrop&&(c=n.measureText(r).width,n.fillStyle=this.options.ticks.backdropColor,n.fillRect(this.xCenter-c/2-this.options.ticks.backdropPaddingX,s-h/2-this.options.ticks.backdropPaddingY,c+this.options.ticks.backdropPaddingX*2,h+this.options.ticks.backdropPaddingY*2));n.textAlign="center";n.textBaseline="middle";n.fillStyle=l;n.fillText(r,this.xCenter,s)}}},this),!this.options.lineArc))for(n.lineWidth=this.options.angleLines.lineWidth,n.strokeStyle=this.options.angleLines.color,r=this.getValueCount()-1;r>=0;r--){this.options.angleLines.display&&(f=this.getPointPosition(r,this.getDistanceFromCenterForValue(this.options.reverse?this.min:this.max)),n.beginPath(),n.moveTo(this.xCenter,this.yCenter),n.lineTo(f.x,f.y),n.stroke(),n.closePath());var o=this.getPointPosition(r,this.getDistanceFromCenterForValue(this.options.reverse?this.min:this.max)+5),h=t.getValueOrDefault(this.options.pointLabels.fontColor,i.defaultFontColor),c=t.getValueOrDefault(this.options.pointLabels.fontSize,i.defaultFontSize),l=t.getValueOrDefault(this.options.pointLabels.fontStyle,i.defaultFontStyle),a=t.getValueOrDefault(this.options.pointLabels.fontFamily,i.defaultFontFamily),v=t.fontString(c,l,a);n.font=v;n.fillStyle=h;var s=this.pointLabels.length,e=this.pointLabels.length/2,u=e/2,y=r<u||r>s-u,p=r===u||r===s-u;n.textAlign=r===0?"center":r===e?"center":r<e?"left":"right";n.textBaseline=p?"middle":y?"bottom":"top";n.fillText(this.pointLabels[r]?this.pointLabels[r]:"",o.x,o.y)}}});n.scaleService.registerScaleType("radialLinear",u,r)}},{}],42:[function(n,t){"use strict";var i=n("moment");i=typeof i=="function"?i:window.moment;t.exports=function(n){var t=n.helpers,r={units:[{name:"millisecond",steps:[1,2,5,10,20,50,100,250,500]},{name:"second",steps:[1,2,5,10,30]},{name:"minute",steps:[1,2,5,10,30]},{name:"hour",steps:[1,2,3,6,12]},{name:"day",steps:[1,2,5]},{name:"week",maxStep:4},{name:"month",maxStep:3},{name:"quarter",maxStep:4},{name:"year",maxStep:!1}]},u=n.Scale.extend({initialize:function(){if(!i)throw new Error("Chart.js - Moment.js could not be found! You must include it before Chart.js to use the time scale. Download at https://momentjs.com");n.Scale.prototype.initialize.call(this)},getLabelMoment:function(n,t){return this.labelMoments[n][t]},getMomentStartOf:function(n){return this.options.time.unit==="week"&&this.options.time.isoWeekday!==!1?n.clone().startOf("isoWeek").isoWeekday(this.options.time.isoWeekday):n.clone().startOf(this.tickUnit)},determineDataLimits:function(){this.labelMoments=[];var n=[];this.chart.data.labels&&this.chart.data.labels.length>0?(t.each(this.chart.data.labels,function(t){var i=this.parseTime(t);i.isValid()&&(this.options.time.round&&i.startOf(this.options.time.round),n.push(i))},this),this.firstTick=i.min.call(this,n),this.lastTick=i.max.call(this,n)):(this.firstTick=null,this.lastTick=null);t.each(this.chart.data.datasets,function(r,u){var f=[],e=this.chart.isDatasetVisible(u);typeof r.data[0]=="object"&&r.data[0]!==null?t.each(r.data,function(n){var t=this.parseTime(this.getRightValue(n));t.isValid()&&(this.options.time.round&&t.startOf(this.options.time.round),f.push(t),e&&(this.firstTick=this.firstTick!==null?i.min(this.firstTick,t):t,this.lastTick=this.lastTick!==null?i.max(this.lastTick,t):t))},this):f=n;this.labelMoments.push(f)},this);this.options.time.min&&(this.firstTick=this.parseTime(this.options.time.min));this.options.time.max&&(this.lastTick=this.parseTime(this.options.time.max));this.firstTick=(this.firstTick||i()).clone();this.lastTick=(this.lastTick||i()).clone()},buildTicks:function(){var u,f,i,e,a,v,h,y,o,c,p;this.ctx.save();var l=t.getValueOrDefault(this.options.ticks.fontSize,n.defaults.global.defaultFontSize),w=t.getValueOrDefault(this.options.ticks.fontStyle,n.defaults.global.defaultFontStyle),b=t.getValueOrDefault(this.options.ticks.fontFamily,n.defaults.global.defaultFontFamily),k=t.fontString(l,w,b);if(this.ctx.font=k,this.ticks=[],this.unitScale=1,this.scaleSizeInUnits=0,this.options.time.unit)this.tickUnit=this.options.time.unit||"day",this.displayFormat=this.options.time.displayFormats[this.tickUnit],this.scaleSizeInUnits=this.lastTick.diff(this.firstTick,this.tickUnit,!0),this.unitScale=t.getValueOrDefault(this.options.time.unitStepSize,1);else{var d=this.isHorizontal()?this.width-(this.paddingLeft+this.paddingRight):this.height-(this.paddingTop+this.paddingBottom),g=this.tickFormatFunction(this.firstTick,0,[]),s=this.ctx.measureText(g).width,nt=Math.cos(t.toRadians(this.options.ticks.maxRotation)),tt=Math.sin(t.toRadians(this.options.ticks.maxRotation));for(s=s*nt+l*tt,u=d/s,this.tickUnit="millisecond",this.scaleSizeInUnits=this.lastTick.diff(this.firstTick,this.tickUnit,!0),this.displayFormat=this.options.time.displayFormats[this.tickUnit],f=0,i=r.units[f];f<r.units.length;)if(this.unitScale=1,t.isArray(i.steps)&&Math.ceil(this.scaleSizeInUnits/u)<t.max(i.steps)){for(e=0;e<i.steps.length;++e)if(i.steps[e]>=Math.ceil(this.scaleSizeInUnits/u)){this.unitScale=t.getValueOrDefault(this.options.time.unitStepSize,i.steps[e]);break}break}else if(i.maxStep===!1||Math.ceil(this.scaleSizeInUnits/u)<i.maxStep){this.unitScale=t.getValueOrDefault(this.options.time.unitStepSize,Math.ceil(this.scaleSizeInUnits/u));break}else++f,i=r.units[f],this.tickUnit=i.name,a=this.firstTick.diff(this.getMomentStartOf(this.firstTick),this.tickUnit,!0),v=this.getMomentStartOf(this.lastTick.clone().add(1,this.tickUnit)).diff(this.lastTick,this.tickUnit,!0),this.scaleSizeInUnits=this.lastTick.diff(this.firstTick,this.tickUnit,!0)+a+v,this.displayFormat=this.options.time.displayFormats[i.name]}for(this.options.time.min?h=this.getMomentStartOf(this.firstTick):(this.firstTick=this.getMomentStartOf(this.firstTick),h=this.firstTick),this.options.time.max||(y=this.getMomentStartOf(this.lastTick),y.diff(this.lastTick,this.tickUnit,!0)!==0&&(this.lastTick=this.getMomentStartOf(this.lastTick.add(1,this.tickUnit)))),this.smallestLabelSeparation=this.width,t.each(this.chart.data.datasets,function(n,t){for(var i=1;i<this.labelMoments[t].length;i++)this.smallestLabelSeparation=Math.min(this.smallestLabelSeparation,this.labelMoments[t][i].diff(this.labelMoments[t][i-1],this.tickUnit,!0))},this),this.options.time.displayFormat&&(this.displayFormat=this.options.time.displayFormat),this.ticks.push(this.firstTick.clone()),o=1;o<=this.scaleSizeInUnits;++o){if(c=h.clone().add(o,this.tickUnit),this.options.time.max&&c.diff(this.lastTick,this.tickUnit,!0)>=0)break;o%this.unitScale==0&&this.ticks.push(c)}p=this.ticks[this.ticks.length-1].diff(this.lastTick,this.tickUnit);(p!==0||this.scaleSizeInUnits===0)&&(this.options.time.max?(this.ticks.push(this.lastTick.clone()),this.scaleSizeInUnits=this.lastTick.diff(this.ticks[0],this.tickUnit,!0)):(this.ticks.push(this.lastTick.clone()),this.scaleSizeInUnits=this.lastTick.diff(this.firstTick,this.tickUnit,!0)));this.ctx.restore()},getLabelForIndex:function(n,t){var i=this.chart.data.labels&&n<this.chart.data.labels.length?this.chart.data.labels[n]:"";return typeof this.chart.data.datasets[t].data[0]=="object"&&(i=this.getRightValue(this.chart.data.datasets[t].data[n])),this.options.time.tooltipFormat&&(i=this.parseTime(i).format(this.options.time.tooltipFormat)),i},tickFormatFunction:function(n,i,r){var u=n.format(this.displayFormat),f=this.options.ticks,e=t.getValueOrDefault(f.callback,f.userCallback);return e?e(u,i,r):u},convertTicksToLabels:function(){this.tickMoments=this.ticks;this.ticks=this.ticks.map(this.tickFormatFunction,this)},getPixelForValue:function(n,t,i){var u=n&&n.isValid&&n.isValid()?n:this.getLabelMoment(i,t),f,r;if(u){if(f=u.diff(this.firstTick,this.tickUnit,!0),r=f/this.scaleSizeInUnits,this.isHorizontal()){var e=this.width-(this.paddingLeft+this.paddingRight),c=e/Math.max(this.ticks.length-1,1),s=e*r+this.paddingLeft;return this.left+Math.round(s)}var o=this.height-(this.paddingTop+this.paddingBottom),l=o/Math.max(this.ticks.length-1,1),h=o*r+this.paddingTop;return this.top+Math.round(h)}},getPixelForTick:function(n,t){return this.getPixelForValue(this.tickMoments[n],null,null,t)},getValueForPixel:function(n){var r=this.isHorizontal()?this.width-(this.paddingLeft+this.paddingRight):this.height-(this.paddingTop+this.paddingBottom),t=(n-(this.isHorizontal()?this.left+this.paddingLeft:this.top+this.paddingTop))/r;return t*=this.scaleSizeInUnits,this.firstTick.clone().add(i.duration(t,this.tickUnit).asSeconds(),"seconds")},parseTime:function(n){return typeof this.options.time.parser=="string"?i(n,this.options.time.parser):typeof this.options.time.parser=="function"?this.options.time.parser(n):typeof n.getMonth=="function"||typeof n=="number"?i(n):n.isValid&&n.isValid()?n:typeof this.options.time.format!="string"&&this.options.time.format.call?(console.warn("options.time.format is deprecated and replaced by options.time.parser. See http://nnnick.github.io/Chart.js/docs-v2/#scales-time-scale"),this.options.time.format(n)):i(n,this.options.time.format)}});n.scaleService.registerScaleType("time",u,{position:"bottom",time:{parser:!1,format:!1,unit:!1,round:!1,displayFormat:!1,isoWeekday:!1,displayFormats:{millisecond:"h:mm:ss.SSS a",second:"h:mm:ss a",minute:"h:mm:ss a",hour:"MMM D, hA",day:"ll",week:"ll",month:"MMM YYYY",quarter:"[Q]Q - YYYY",year:"YYYY"}},ticks:{autoSkip:!1}})}},{moment:1}]},{},[7]);
//# sourceMappingURL=Chart.min.js.map
