var UserState = {
    notyet: 0,
    failed: 1,
    success: 2,
    already: 3,
    logoff: 4
}

var _doLogin = function (landingPage, userDestinationUrl) {
    setTimeout(function () {
        window.parent.postMessage({
            action: '_makei2e1Login',
            parameter: landingPage,
            url: userDestinationUrl
        }, '*');
    }, 5000);
    var _receiveMessage = function (event) {
        if (event.data.action === '_swapState' && (event.origin.indexOf(window.location.hostname) === 7 || event.origin.indexOf(window.location.hostname) === 8)) {
            $('.promotional_offer').hide(); $('.content2').show();
        }
    }
    window.addEventListener("message", _receiveMessage, false);
}

var _preLogin = function (landingPage) {
    window.landingPage = landingPage;
    _doLogin(landingPage);
}

function makei2e1Login(mobile, chap, loginResponse){
    _preLogin(loginResponse);
}
var loginUser = null;
var loaderCount = 0;
var i2e1Api = {
    baseUri: window.location.protocol + '//' + window.location.hostname + (window.location.port ? (':' + window.location.port) : '') + '/',
    initialize: function (login, success) {
        i2e1Api.getUser(function (user) {
            switch (user.res) {
                case UserState.notyet:
                case UserState.failed:
                case UserState.logoff:
                    login();
                    break;
                case UserState.success:
                case UserState.already:
                    success();
            }
        })       
    },
    changeState: function (url) {
        window.location.href = url + window.location.search;
    },
    doAjax: function (url, data, onsuccess, onfailure) {
        url = i2e1Api.baseUri + url;
        loaderCount++;
        if (loaderCount > 0)
            $('#loader').show();
        $.ajax({
            type: "POST",
            url: url,
            data: JSON.stringify(data),
            contentType: "application/json; charset=utf-8",
            dataType: "json",
            success: function (data) { if (onsuccess) onsuccess(data) },
            failure: function (errMsg) { if (onfailure) onfailure(errMsg) },
            complete: function () { loaderCount--; if (loaderCount <= 0) $('#loader').hide();}
        });
    },
    getUser: function (callback) {
        if (loginUser)
            callback(loginUser);
        else
            i2e1Api.doAjax('/Login/GetUser', {}, function (response) {
                loginUser = response.data;
                callback(loginUser);
            });
    },
    generateOTP: function (mobile, isresend, onsuccess, onfailure) {
        i2e1Api.doAjax('/Login/GenerateOTP/', {
            mobile: mobile,
            smsApi: isresend ? 'change' : null,
            clientAuthType: 'PHONE'
        }, onsuccess, onfailure);
    },
    submitOTP: function (mobile, otp, name,captcha, onsuccess, onfailure, waitInSec) {
        i2e1Api.getUser(function (user) {
            mobile = mobile ? mobile : user.mac.replace(/-/g, '') + "MAC";
            i2e1Api.doAjax('/Login/SubmitOTP/', {
                mobile: mobile,
                otp: otp,
                name: name,
                clientAuthType: 'PHONE',
                captcha: captcha
            }, function (response) {
                if (response.status === 0) {
                    if (onsuccess)
                        onsuccess();
                    if (waitInSec) {
                        setTimeout(function () {
                            makei2e1Login(mobile, response.data.chap, response.data.landingPage);
                        }, waitInSec * 1000);
                    }
                    else
                        makei2e1Login(mobile, response.data.chap, response.data.landingPage);
                }
                else {
                    if (onfailure)
                        onfailure(response);
                }
            }, function () {
                if (onfailure)
                    onfailure(response);
            });
        });
    },
    checkOTP: function (mobile, otp, name, captcha, onsuccess, onfailure, waitInSec) {
        var params = _getUrlParams();
        if (!otp)
            console.error("Missing OTP while Checking OTP");
        i2e1Api.getUser(function (user) {
            mobile = mobile ? mobile : user.mac.replace(/-/g, '') + "MAC";
            i2e1Api.doAjax('/Login/CheckOTP/', {
                mobile: mobile,
                otp: otp,
                name: name,
                clientAuthType: 'PHONE',
                captcha: captcha
            }, function (response) {
                if (response.status === 0) {
                    if (onsuccess)
                        onsuccess();
                    /*if (waitInSec) {
                        setTimeout(function () {
                            //TODO: Go to Questions
                            //makei2e1Login(mobile, response.data.chap, response.data.landingPage);
                        }, waitInSec * 1000);
                    }
                    else
                        //TODO: Go to Questions
                        //makei2e1Login(mobile, response.data.chap, response.data.landingPage);*/
                }
                else {
                    if (onfailure)
                        onfailure(response);
                }
            }, function () {
                if (onfailure)
                    onfailure(response);
            });
        });
    }
}