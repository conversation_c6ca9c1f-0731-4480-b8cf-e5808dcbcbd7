.datepicker {
    position: relative;
    display: inline-block;
}
.datepicker .picker {
    box-sizing: border-box;
    position: absolute;
    z-index: 10000;
    min-width: 100%;
}
.datepicker .picker * {
    box-sizing: inherit;
}
.datepicker .picker .header {
    position: relative;
    text-align: center;
}
.datepicker .picker .header .month {
    cursor: pointer;
}
.datepicker .picker .header .prev,
.datepicker .picker .header .next {
    position: absolute;
    cursor: pointer;
}
.datepicker .picker .header .prev {
    left: 0;
}
.datepicker .picker .header .next {
    right: 0;
}
.datepicker .picker table {
    width: 100%;
    border: none;
    background: none;
    border-collapse: collapse;
    margin: 0;
    padding: 0;
}
.datepicker .picker table td,
.datepicker .picker table th {
    text-align: center;
    padding: 0 3px;
}
.datepicker .picker table td.day {
    cursor: pointer;
}
.datepicker .picker table td.day.invalid {
    opacity: 0.5;
    cursor: default;
}

/* themes */
.datepicker.basic .picker {
    padding: 15px;
    background: white;
    border: 1px solid #888;
    border-radius: 3px;
    box-shadow: 2px 4px 14px rgba(0, 0, 0, 0.2);
    font-size: 90%;
}
.datepicker.basic .picker .header {
    padding: 0 0 8px 0;
}
.datepicker.basic .picker table td,
.datepicker.basic .picker table th {
    padding: 2px 6px;
}
.datepicker.basic .picker table td.day:hover {
    background: #eee;
}
.datepicker.basic .picker table td.day.today {
    font-weight: bold;
}
.datepicker.basic .picker table td.day.current {
    background: #aaa;
}


.datepicker.flat .picker {
    max-width: 500px;
    padding: 0;
    background: #EBEEF0;
    color: #444;
    font: normal normal 14px/20px sans-serif;
}
.datepicker.flat .picker .header {
    background: #337E9D;
    color: #fafafa;
    font-size: 85%;
    line-height: 40px;
    text-transform: uppercase;
}
.datepicker.flat .picker .header .prev {
    padding: 0 10px;
    left: 20px;
    font-size: 80%;
}
.datepicker.flat .picker .header .next {
    padding: 0 10px;
    right: 20px;
    font-size: 80%;
}
.datepicker.flat .picker .calendar {
    margin: 10px 20px;
}
.datepicker.flat .picker table th {
    opacity: 0.5;
    font-weight: normal;
    font-size: 80%;
}
.datepicker.flat .picker table td,
.datepicker.flat .picker table th {
    padding: 3px 8px;
}
.datepicker.flat .picker table td.day:hover {
    background: #D2D8ED;
}
.datepicker.flat .picker table td.day.today {
    font-weight: bold;
}
.datepicker.flat .picker table td.day.current {
    background: #989AA2;
    color: #f0f0f0;
}
