body {
        background-color: #fff;
        font-family: 'Noto Sans Devanagari', sans-serif !important;
        letter-spacing: 0 !important;
    }

    .payment-method-container {
        display: flex;
        flex-direction: column;
        gap: 16px;
    }

    .payment-method {
        border: 1px solid #E8E4F0;
        display: flex;
        align-items: center;
        /* margin-bottom: 10px; */
        cursor: pointer;
        height: 4rem;
        /* margin-top: 1rem; */
        color: #FAF9FC;
        padding: 0.1rem;
        border-radius: 1rem;
    }

    .payment-method img {
        width: 2.5rem;
        height: 2.5rem;
        margin: 1em;
        cursor: pointer;
    }

    .payment-method p {
        font-size: 1.2rem;
        color: #161021;
        font-weight: 700;
        line-height: 1.5rem;
        margin-left: 0.4em;
    }

    #upiCollectContainer {
        display: none;
    }

    #upiIntentContainer {
        display: block;
        max-height: 100vh;      /* or whatever height you need */
  overflow-y: auto;
    }
    .sticky-header {
        position: sticky;
        top: 0;
        background: #fff;      /* match your background */
        z-index: 100;          /* sit above scrolled content */
      }

    #upi-input::placeholder {
        font-size: 16px;
        font-weight: 400;
        line-height: 24px;
        text-align: left;
        color: #A7A1B2;
    }
    
    #upi-input-desktop::placeholder {
        font-size: 16px;
        font-weight: 400;
        line-height: 24px;
        text-align: left;
        color: #A7A1B2;
    }
    .modal {
        display: none;
        position: fixed;
        width: 60%;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        background-color: rgba(255, 255, 255, 0.9);
        border-radius: 24px;
        padding: 32px 24px;
        box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
        z-index: 1000;
        color: #FAF9FC;
    }

    .modal-background {
        display: none;
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0, 0, 0, 0.5);
        z-index: 999;
        backdrop-filter: blur(5px);
    }
    .modal-buttons{
        display: flex;
        flex-direction: column;
        gap: 20px;
    }


    #loader {
        z-index: 1001;
        position: relative;
        display: none;
    }

    .timer-circle {
        width: 8rem;
        height: 8rem;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: 700;
        font-size: 2rem;
        color: #161021;
    }
    .timer-circle {
        display: block;
        margin: 20px auto;
        border-radius: 50%;
    }

    .timer-circle::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        border-radius: 50%;
        background-color: transparent;
        clip-path: polygon(50% 50%, 50% 0, 50% 0, 50% 0);
        transition: clip-path 1s linear;
    }

    #demo {
        height: 530px;
        margin-top: 12rem;
    }

    #demo img {
        width: 8em;
        margin: 1em;
    }

    .flex-container {
        display: flex;
        padding: 1.5rem 0;
        align-items: center;
        justify-content: space-between;
    }

    .flex-container img {
        border-radius: 0.5em;
    }

    .languageSelect {
        text-align: center;
    }

    .plan-amount-text {
        font-family: 'Noto Sans', sans-serif;
        font-weight: 700;
        font-size: 24px;
        line-height: 32px;
        letter-spacing: 0px;
        color: #161021;
        text-align: left;
        margin-top: 24px;

            /* remove any default padding */
    }

    .unlimited-internet {
        font-family: 'Noto Sans', sans-serif;
        font-weight: 700;
        font-size: 24px;
        line-height: 32px;
        letter-spacing: 0px;
        
        align-items: start;
       
        color: #161021;

    }

    #pay-option {
        color: #161021;
        text-align: left;
        font-size: 16px;
        font-weight: 400;
    }

    #enter-upi-id {
        cursor: pointer;
        font-weight: 600;
        font-size: 14px;
        line-height: 20px;
        color: #D9008D;
        text-align: left;
        padding-left: 16px;
        padding-right: 16px;
    }

    .flex-container-upi {
        display: flex;
        padding: 0px 16px;
        font-weight: 700;
        font-size: 1.0rem;
        line-height: 1rem;
        color: #161021;
    }

    #user_upi_id {
        text-align: left;
        font-size: 16px;
        color: #161021;
    }

    #upi-input {
        width: 100%;
        height: 4rem;
        padding: 0.75rem 4.5rem 0.75rem 1rem;
        gap: 0.75rem;
        border-radius: 0.8rem;
        border: 1px solid var(--border-color, #D7D3E0);
        font-size: 1rem;
        font-weight: 700;
    }
    
    #upi-input-desktop {
        width: 23.5rem;
        height: 3rem;
        padding: 0.75rem 1rem;
        gap: 0.75rem;
        border-radius: 0.8rem;
        border: 1px solid var(--border-color, #D7D3E0);
        font-size: 1rem;
        font-weight: 400;
    }

    #verify-btn {
        padding: 4px 8px;
        border-radius: 8px;
        border: 1px solid rgba(255, 229, 246, 1);
        display: none;
        position: absolute;
        top: 50%;
        transform: translateY( -50%);
        right: 1.5rem;
        cursor: pointer;
        color: #D9008D;
        font-weight: 700;
        margin-right: 10px;
    }
    
    #verify-btn-desktop {
        display: none;
        position: absolute;
        top: 1.5rem;
        transform: translate(-50%, -50%);
        right: 0.1rem;
        cursor: pointer;
        color: #D9008D;
    }

    #green-tick {
        display: none;
        position: absolute;
        top: 50%;
        transform: translateY( -50%);
        right: 1.5rem;
        padding-right:12px ;
    }
    
    #green-tick-desktop {
        display: none;
        position: absolute;
        top: 1.5rem;
        transform: translate(-50%, -50%);
        right: 0.1rem;
    }

    #verificationSuccess {
        color: #008043;
        display: none;
        text-align: left;
        margin-left: 2rem;
        margin-top: 1rem;
    }
    
    #vpaNameDesktop {
        color: #008043;
        font-weight: 700;
    }

    #verified-account {
    }

    #verificationFailure {
        color: #E01E00;
        display: none;
        text-align: left;
        margin-left: 2rem;
        margin-top: 1rem;
    }

    #pay-upi-btn {
        display: none;
        position: fixed;
        left: 50%;                         /* <-- this is missing */
        transform: translateX(-50%);
        bottom: 1rem;     
        border: none;
        color: white;
        width: 92%;
        border-radius: 16px;
        height: 4rem;
        box-shadow: none;
        font-size: 16px;
        background-color: #D92B90;
        font-weight: 600;
        padding: 1rem 0;
        z-index: 1000;              /* ensure it sits above other content */

    }
    
    #create-qr-btn {
        border: none;
        color: white;
        width: 67%;
        border-radius: 0.95rem;
        height: 3rem;
        box-shadow: none;
        font-size: 1rem;
        background-color: #D92B90;
        font-weight: 600;
        padding: 1rem 0;
        margin-left: 0rem;
        margin-top: 2rem;
    }

    #pay-upi-btn-desktop {
        display: none;
        border: none;
        color: white;
        width: 99%;
        border-radius: 0.95rem;
        height: 3rem;
        box-shadow: none;
        font-size: 1rem;
        background-color: #D92B90;
        font-weight: 600;
        padding: 1rem 0;
        margin-left: 0.5rem;
        margin-top: 1rem;
    }

    .flex-column-center {
        display: flex;
        flex-direction: column;
        align-items: center;
        text-align: center;
        height: 21rem;
    }

    #open-upi {
        font-weight: 700;
        color: #161021;
        line-height:2rem;
        font-size: 1.5rem;
    }

    #orderIdDisplay {
        color: #161021;
        font-weight: 400;
        line-height: 1.5rem;
        margin-top: 0.5rem;
        font-size: 1rem;
    }

    .flex-center {
        display: flex;
        justify-content: center;
        align-items: center;
        text-align: center;
        color: #161021;
        background-color: #F1EDF7;
        width: 100%;
        height: 6rem;
        cursor: pointer;
    }

    #cancel-button {
        font-weight: 700;
        line-height: 1.4rem;
        font-size: 16px;
        line-height: 24px;
    }

    #cancel-txn {
        font-size: 1.2rem;
        font-weight: 700;
        color: #161021;
        text-align: left;
    }

    .flex-column-evenly {
        display: flex;
        flex-direction: column;
        margin-top: 1rem;
        justify-content: space-evenly;
        min-height: 7rem;
    }

    #no {
        background-color: #D9008D;
        border: none;
        color: white;
        border-radius: 1rem;
        cursor: pointer;
        padding: 0.5rem 1rem;
    }

    #yes {
        background-color: #FFE5F6;
        border: none;
        color: #D9008D;
        border-radius: 1rem;
        cursor: pointer;
        padding: 0.5rem 1rem;
    }

    .info-container {
        width: 10%;
        background-color: #EFEDF2;
        display: flex;
        justify-content: center;
        align-items: center;
        height: 6rem;
        margin-left: 12rem;
        border-radius: 1.5rem 0 0 1.5rem;
    }

    .info-text {
        font-weight: 700;
        font-size: 1.2rem;
        line-height: 2.3rem;
        color: #161021;
        word-wrap: break-word;
    }

    .details-container {
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        align-items: center;
        padding: 1rem;
        width: 67%;
        height: 4rem;
        background-color: #FAF9FC;
        border-radius: 0 1.5rem 1.5rem 0;
    }

    .details-text {
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        height: 100%;
        color: #161021;
    }

    .details-title {
        margin: 0;
        font-weight: 700;
        font-size: 1.2rem;
        line-height: 1.3rem;
    }

    .details-amount {
        margin: 0;
        font-weight: 700;
        font-size: 1.2rem;
        line-height: 0.3rem;
        text-align: left;
    }

    .change-plan {
        align-self: center;
        margin: 0;
        font-weight: 700;
        font-size: 1rem;
        line-height: 1.5rem;
        color: #D9008D;
        cursor: pointer;
    }

    .payment-option-container {
        width: 10%;
        background-color: #EFEDF2;
        display: flex;
        justify-content: center;
        margin-left: 12rem;
        border-radius: 1.5rem 0 0 1.5rem;
    }

    .payment-option-title {
        font-weight: 700;
        font-size: 1.2rem;
        line-height: 2.3rem;
        color: #161021;
        word-wrap: break-word;
    }

    #upi-id-desktop {
        display: flex;
        flex-direction: column;
        padding: 1rem;
        width: 69%;
        background-color: #FAF9FC;
        border-radius: 0 1.5rem 1.5rem 0;
        box-sizing: border-box;
    }

    .pay-by-scan-container {
        display: flex;
        justify-content: space-between;
        align-items: center;
        width: 100%;
    }

    .pay-by-scan-content {
        display: flex;
        flex-direction: column;
        align-items: flex-start;
        width: 70%;
    }

    .pay-by-scan-title {
        font-weight: 700;
        font-size: 1.2rem;
        line-height: 1.0rem;
        color: #161021;
        display: block;
    }

    .pay-scan-text {
        font-weight: 400;
        font-size: 0.8rem;
        margin-top: 1rem;
        color: #161021;
        display: block;
    }

    .pay-by-scan-image {
        display: block;
        width: auto;
        height: auto;
        margin-top: 0.1rem;
    }

    .qr-logo {
        height: 11rem;
        width: 11rem;
        margin-right: 3rem;
    }

    #user_upi_id_desktop {
        font-weight: 700;
        font-size: 1.2rem;
        color: #161021;
        margin: 0 0 1rem 0;
        text-align: left;
    }

    .upi-input-container {
        position: relative;
        margin-top: 1rem;
        clear: both;
    }

    #upi-input-desktop {
        padding: 0.5rem;
        font-size: 1rem;
        width: 98%;
        box-sizing: border-box;
        float: left;
    }

    .upi-receipt-container {
        margin-top: 5rem;
        clear: both;
        text-align: left;
    }

    #upi-id-receipt {
        font-size: 1rem;
        color: #161021;
    }

    .account-verified {
        display: none;
        font-size: 1rem;
        color: #008043;
    }

    .account-unverified {
        display: none;
        font-size: 1rem;
        color: red;
    }

    #qr {
        display: none;
        flex-direction: column;
        padding: 1rem;
        width: 69%;
        background-color: #FAF9FC;
        border-radius: 0 1.5rem 1.5rem 0;
        box-sizing: border-box;
    }

    .qr-header {
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        width: 7rem;
    }

    .qr-back-button {
        height: 1.2rem;
        width: 1.2rem;
        margin-top: 0.5rem;
        cursor: pointer;
    }

    #hide-qr {
        margin-top: 0.5rem;
        font-weight: 700;
        font-size: 1.2rem;
        color: #161021;
    }

    .qr-container {
        background-color: #EFEDF2;
        border-radius: 1.5rem;
    }

    #qr-code {
        margin-right: 1rem;
    }

    .bank-image {
        height: auto;
        width: 33rem;
        margin-top: 3rem;
    }

    .payment-step-container {
        display: flex;
        flex-direction: column;
        justify-content: space-evenly;
        width: 32%;
        height: 6rem;
        background-color: #EFEDF2;
        margin-top: 1rem;
        border-radius: 1rem;
    }

    .step-description {
        text-align: left;
        margin-left: 1rem;
    }

    .step-heading {
        text-align: left;
        margin-left: 1rem;
        line-height: 0rem;
        font-weight: 700;
        font-size: 1.2rem;
        color: #161021;
    }

    .custom-flex-container {
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        width: 100%;
    }

    .custom-column-container {
        display: flex;
        flex-direction: column;
        justify-content: space-evenly;
        width: 33%;
        height: 6rem;
        background-color: #EFEDF2;
        margin-top: 1rem;
        border-radius: 1rem;
    }
/* Add styles for WhatsApp share page */
#whatsappShareContainer {
    display: none;
    width: 100%;
    height: 100vh;
    position: fixed;
    top: 0;
    left: 0;
    overflow: hidden;
    background-color: #fff;
    display: none;
    flex-direction: column;
}

.back-icon {
    cursor: pointer;
    width: 1.5em;
    height: 1.5em;
    margin-right: 0.9375em;
    color: #161021;
}

.header-left {
    display: flex;
    align-items: center;
}

.share-card {
    background-color: #fff;
    display: flex;
    flex-direction: column;
    padding: 1.5rem 1.25rem;
    box-sizing: border-box;
    width: 100%;
    flex: 1 1 auto;
    overflow: hidden;
    display: flex;
    flex-direction: column;
}

.share-title {
    font-size: 1.5rem;
    font-weight: 700;
    color: #161021;
    text-align: left;
    flex: 0 0 auto;
}

.share-description {
    font-size: 1rem;
    color: #161021;
    margin-bottom: 1.5em; /* 24px */
}

.share-text {
    display: flex;
    align-items: center;
    justify-content: center;
    text-align: center;
    color: #D9008D;
    font-size: 1rem;
    font-weight: 400;
    cursor: pointer;
    padding: 1.5rem 0;
    text-decoration: none;
    margin-top: 2rem;
}

    .share-text img {
        height: 1.5em; /* 24px */
        margin-right: 0.5em; /* 8px */
    }

.price-tag {
    font-size: 1rem;
    font-weight: 500;
    color: #161021;
    margin-top: 1em; /* 16px */
    text-align: center;
}

.steps-wrapper {
    border: 0.0625em solid #E8E4F0; /* 1px */
    border-radius: 0.75em; /* 12px */
    padding: 1em; /* 16px */
    width: 100%;
    box-sizing: border-box;
    flex: 0 0 auto;
}

.step {
    display: flex;
    align-items: anchor-center;
    margin-bottom: 1em; /* 16px */
    justify-content: flex-start;
}

    .step:last-child {
        margin-bottom: 0;
    }

.step-number {
    color: black;
    font-weight: normal;
    margin-right: 12px; /* Small right margin for readability */
    flex-shrink: 0;
    text-align: left;
}

.step-text {
    font-size: 1rem;
    color: #161021;
    font-weight: bold;
    display: flex;
    align-items: center;
    flex: 1;
    padding-left: 0; /* Remove left padding */
    text-align: left;
}

.qr-box {
    background-color: #F1EDF7;
    border: 0.0625em solid #E8E4F0; /* 1px */
    border-radius: 0.75em; /* 12px */
    padding: 1.5em; /* 24px */
    display: flex;
    flex-direction: column;
    align-items: center;
    width: 100%;
    box-sizing: border-box;
}

.steps-container {
    display: flex;
    justify-content: center;
    width: 100%;
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
}

#qr-code-whatsapp {
    width: 12.5em;
    height: 12.5em;
    background-color: white;
    padding: 0.75em;
    border-radius: 0.5em;
    margin-bottom: 1em;
    display: flex;
    justify-content: center;
    align-items: center;
}

.content-wrapper {
    display: flex;
    flex-direction: column;
    flex: 1;
    display: flex;
    flex-direction: column;
}

.spacer {
    flex: 1;
    min-height: 2rem;
}

input:-webkit-autofill {
    box-shadow: 0 0 0px 1000px white inset !important;
    -webkit-box-shadow: 0 0 0px 1000px white inset !important;
    -webkit-text-fill-color: #000 !important;
    transition: background-color 5000s ease-in-out 0s;
  }
