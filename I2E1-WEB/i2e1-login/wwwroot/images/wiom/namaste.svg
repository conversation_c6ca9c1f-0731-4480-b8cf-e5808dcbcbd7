<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="77.763" height="75.48" viewBox="0 0 77.763 75.48">
    <defs>
        <linearGradient id="linear-gradient" x1="-180.901" x2="-180.566" y1=".645" y2="-.017" gradientUnits="objectBoundingBox">
            <stop offset="0" stop-color="#852015"/>
            <stop offset="1" stop-color="#ef463b"/>
        </linearGradient>
        <linearGradient id="linear-gradient-2" x1="-182.032" x2="-181.697" y1=".645" y2="-.017" gradientUnits="objectBoundingBox">
            <stop offset="0" stop-color="#007241"/>
            <stop offset="1" stop-color="#1fb46a"/>
        </linearGradient>
        <linearGradient id="linear-gradient-3" x1="-181.491" x2="-181.156" y1=".645" y2="-.017" gradientUnits="objectBoundingBox">
            <stop offset="0" stop-color="#815c1d"/>
            <stop offset="1" stop-color="#fcb448"/>
        </linearGradient>
        <linearGradient id="linear-gradient-5" x1="-214.091" x2="-214.599" y1=".462" y2=".651" gradientUnits="objectBoundingBox">
            <stop offset="0" stop-color="#eed7be"/>
            <stop offset="1" stop-color="#8d2978" stop-opacity="0"/>
        </linearGradient>
        <linearGradient id="linear-gradient-6" x1="-249.054" x2="-249.703" y1=".208" y2=".804" gradientUnits="objectBoundingBox">
            <stop offset="0" stop-color="#edb382"/>
            <stop offset="1" stop-color="#8a2a79" stop-opacity="0"/>
        </linearGradient>
        <linearGradient id="linear-gradient-7" x1="-2679.897" x2="-2678.655" y1=".497" y2=".517" gradientUnits="objectBoundingBox">
            <stop offset="0" stop-color="#ef463b"/>
            <stop offset="1" stop-color="#ef463b" stop-opacity="0"/>
        </linearGradient>
        <linearGradient id="linear-gradient-8" x1="-2440.111" x2="-2438.942" y1="22.386" y2="22.409" xlink:href="#linear-gradient-7"/>
        <linearGradient id="linear-gradient-9" x1="-2942.917" x2="-2941.422" y1="-27.072" y2="-27.048" xlink:href="#linear-gradient-7"/>
        <linearGradient id="linear-gradient-10" x1="-326.718" x2="-326.108" y1=".579" y2=".216" xlink:href="#linear-gradient"/>
        <linearGradient id="linear-gradient-11" x1="-326.718" x2="-326.108" y1=".579" y2=".216" xlink:href="#linear-gradient-2"/>
        <linearGradient id="linear-gradient-12" x1="-326.718" x2="-326.108" y1=".579" y2=".216" xlink:href="#linear-gradient-3"/>
        <linearGradient id="linear-gradient-14" x1="-209.564" x2="-210.072" y1=".462" y2=".651" xlink:href="#linear-gradient-5"/>
        <linearGradient id="linear-gradient-15" x1="-243.948" x2="-244.596" y1=".208" y2=".804" xlink:href="#linear-gradient-6"/>
        <linearGradient id="linear-gradient-16" x1="-2674.782" x2="-2673.538" y1=".497" y2=".517" xlink:href="#linear-gradient-7"/>
        <linearGradient id="linear-gradient-17" x1="-2427.457" x2="-2426.291" y1="22.314" y2="22.337" xlink:href="#linear-gradient-7"/>
        <linearGradient id="linear-gradient-18" x1="-2930.659" x2="-2929.166" y1="-26.957" y2="-26.933" xlink:href="#linear-gradient-7"/>
        <radialGradient id="radial-gradient" cx=".727" cy=".449" r=".531" gradientTransform="matrix(.554 -.946 -.619 -.124 -164.397 -348.843)" gradientUnits="objectBoundingBox">
            <stop offset="0" stop-color="#fff"/>
            <stop offset="1" stop-color="#ef463b" stop-opacity="0"/>
        </radialGradient>
        <radialGradient id="radial-gradient-2" cx=".727" cy=".449" r=".531" gradientTransform="matrix(.555 -.946 -.619 -.124 -164.927 -350.113)" gradientUnits="objectBoundingBox">
            <stop offset="0" stop-color="#fff"/>
            <stop offset="1" stop-color="#fcb448" stop-opacity="0"/>
        </radialGradient>
        <radialGradient id="radial-gradient-3" cx=".727" cy=".449" r=".531" gradientTransform="matrix(.555 -.946 -.619 -.124 -165.428 -351.175)" gradientUnits="objectBoundingBox">
            <stop offset="0" stop-color="#fff"/>
            <stop offset="1" stop-color="#1fb46a" stop-opacity="0"/>
        </radialGradient>
        <radialGradient id="radial-gradient-4" cx=".273" cy=".449" r=".531" gradientTransform="matrix(-.555 -.946 .619 -.124 -564.345 -345.247)" xlink:href="#radial-gradient"/>
        <radialGradient id="radial-gradient-5" cx=".273" cy=".449" r=".531" gradientTransform="matrix(-.555 -.946 .619 -.124 -564.383 -345.284)" xlink:href="#radial-gradient-2"/>
        <radialGradient id="radial-gradient-6" cx=".273" cy=".449" r=".531" gradientTransform="matrix(-.555 -.946 .619 -.124 -564.388 -345.306)" xlink:href="#radial-gradient-3"/>
        <style>
            .cls-1{fill:url(#linear-gradient)}.cls-4{fill:#eed7be}.cls-6{fill:#edb382}.cls-8{fill:#ef463b}.cls-12{fill:#1fb46a}.cls-13{fill:#fcb448}.cls-17{fill:url(#linear-gradient-10)}
        </style>
    </defs>
    <g id="Group_110183" data-name="Group 110183" transform="translate(-837.331 -263.45)">
        <ellipse id="Ellipse_11310" cx="8.619" cy="4.729" class="cls-1" data-name="Ellipse 11310" rx="8.619" ry="4.729" transform="rotate(-65.085 699.003 -527.285)"/>
        <ellipse id="Ellipse_11311" cx="8.619" cy="4.729" fill="url(#linear-gradient-2)" data-name="Ellipse 11311" rx="8.619" ry="4.729" transform="rotate(-65.1 707.76 -532.198)"/>
        <ellipse id="Ellipse_11312" cx="8.619" cy="4.729" fill="url(#linear-gradient-3)" data-name="Ellipse 11312" rx="8.619" ry="4.729" transform="rotate(-65.1 703.9 -529.518)"/>
        <ellipse id="Ellipse_11313" cx="8.619" cy="4.729" class="cls-1" data-name="Ellipse 11313" rx="8.619" ry="4.729" transform="rotate(-65.085 699.003 -527.285)"/>
        <g id="Group_110181" data-name="Group 110181" transform="translate(876.353 263.45)">
            <path id="Path_226751" d="M848.66 263.969l-.06.031z" class="cls-4" data-name="Path 226751" transform="translate(-847.335 -262.114)"/>
            <path id="Path_226752" d="M848.606 264l-.054.032z" class="cls-4" data-name="Path 226752" transform="translate(-847.464 -262.039)"/>
            <path id="Path_226753" d="M862.009 324.468c-5.422-2.992-10.455-8.353-11.681-10.891s-1.391-9.011-.926-11.692-.1-7.313.1-8.006-.579-5.826-.432-6.091-.411-4.8-.265-5.276a25.308 25.308 0 0 0-.182-3.889 6.1 6.1 0 0 1 .708-2.817l.043-1-.84.715a19.869 19.869 0 0 1-.061-4.01c.161-2.645 1.526-3.521 1.526-3.521a1.839 1.839 0 0 0-1.526.322s.161-1.129.407-2.484c.161-.872 1.144-1.512 1.819-1.859a17.257 17.257 0 0 1 .4 3.349c.05.554.375 3.285.475 3.875s.465 3.12.769 4.046 1.433 5.376 1.648 6.187a49.922 49.922 0 0 0 1.952 5.973 18.922 18.922 0 0 1 1.9 4.561 9.2 9.2 0 0 1 .039 1.119 12.716 12.716 0 0 1 .518 2.28c.129 1.115.608 6.595.737 8.218s.682 6.516 2.28 8.518 5.276 4.615 10.423 6.77v10.269c-4.062-1.821-7.876-3.586-9.831-4.666z" class="cls-4" data-name="Path 226753" transform="translate(-847.824 -262.124)"/>
            <path id="Path_226754" d="M848.464 264.116l-.047.057z" class="cls-4" data-name="Path 226754" transform="translate(-847.814 -261.736)"/>
            <path id="Path_226755" d="M848.5 264.073l-.043.039z" class="cls-4" data-name="Path 226755" transform="translate(-847.706 -261.846)"/>
            <path id="Path_226756" d="M849.012 263.93a.358.358 0 0 1 .146.125.358.358 0 0 0-.146-.125z" class="cls-4" data-name="Path 226756" transform="translate(-846.282 -262.214)"/>
            <path id="Path_226757" d="M848.723 263.945l-.068.025z" class="cls-4" data-name="Path 226757" transform="translate(-847.201 -262.176)"/>
            <path id="Path_226758" d="M848.54 264.035l-.035.025z" class="cls-4" data-name="Path 226758" transform="translate(-847.59 -261.944)"/>
            <path id="Path_226759" d="M849.069 263.938a1.089 1.089 0 0 0-.125-.029 1.089 1.089 0 0 1 .125.029z" class="cls-4" data-name="Path 226759" transform="translate(-846.457 -262.268)"/>
            <path id="Path_226760" d="M849.088 263.961a.469.469 0 0 0-.107-.043.469.469 0 0 1 .107.043z" class="cls-4" data-name="Path 226760" transform="translate(-846.362 -262.245)"/>
            <path id="Path_226761" d="M848.786 263.927l-.079.022z" class="cls-4" data-name="Path 226761" transform="translate(-847.067 -262.222)"/>
            <path id="Path_226762" d="M849.039 263.919c-.043-.007-.089-.011-.136-.014.047.004.097.007.136.014z" class="cls-4" data-name="Path 226762" transform="translate(-846.563 -262.279)"/>
            <path id="Path_226763" d="M848.863 263.913l-.1.022z" class="cls-4" data-name="Path 226763" transform="translate(-846.934 -262.258)"/>
            <path id="Path_226764" d="M848.931 263.906l-.122.011z" class="cls-4" data-name="Path 226764" transform="translate(-846.805 -262.276)"/>
            <path id="Path_226765" d="M848.994 263.907a.854.854 0 0 0-.136 0 .854.854 0 0 1 .136 0z" class="cls-4" data-name="Path 226765" transform="translate(-846.679 -262.281)"/>
            <path id="Path_226766" fill="url(#linear-gradient-5)" d="M867.609 295.08c-2.963-1.258-7.974-3.406-12.639-5.487v-10.269c5.147 2.155 14.226 5.426 14.226 5.426a12.381 12.381 0 0 1-1.587 10.33z" data-name="Path 226766" transform="translate(-830.944 -222.585)"/>
            <path id="Path_226768" d="M848.836 263.919l-.107.029z" class="cls-6" data-name="Path 226768" transform="translate(-847.011 -262.243)"/>
            <path id="Path_226769" d="M848.9 263.9h-.054z" class="cls-6" data-name="Path 226769" transform="translate(-846.717 -262.279)"/>
            <path id="Path_226770" d="M848.863 263.909l-.075.014z" class="cls-6" data-name="Path 226770" transform="translate(-846.859 -262.268)"/>
            <path id="Path_226771" d="M848.512 264.132a1.542 1.542 0 0 0-.132.193 1.542 1.542 0 0 1 .132-.193z" class="cls-6" data-name="Path 226771" transform="translate(-847.909 -261.694)"/>
            <path id="Path_226775" d="M848.986 263.921h-.007z" class="cls-6" data-name="Path 226775" transform="translate(-846.367 -262.248)"/>
            <path id="Path_226776" d="M848.589 264.042c-.04.036-.079.072-.118.111.039-.039.079-.075.118-.111z" class="cls-6" data-name="Path 226776" transform="translate(-847.675 -261.926)"/>
            <path id="Path_226777" d="M848.748 263.952c-.043.018-.086.039-.129.061.042-.022.081-.043.129-.061z" class="cls-6" data-name="Path 226777" transform="translate(-847.294 -262.158)"/>
            <path id="Path_226778" d="M848.534 264.084c-.036.036-.068.075-.1.114.032-.039.066-.078.1-.114z" class="cls-6" data-name="Path 226778" transform="translate(-847.781 -261.818)"/>
            <path id="Path_226779" d="M848.653 264.007a1.272 1.272 0 0 0-.139.1 1.272 1.272 0 0 1 .139-.1z" class="cls-6" data-name="Path 226779" transform="translate(-847.564 -262.016)"/>
            <path id="Path_226780" d="M848.693 263.977a1.219 1.219 0 0 0-.125.075 1.219 1.219 0 0 1 .125-.075z" class="cls-6" data-name="Path 226780" transform="translate(-847.425 -262.093)"/>
            <path id="Path_226781" d="M848.792 263.933l-.118.043z" class="cls-6" data-name="Path 226781" transform="translate(-847.152 -262.207)"/>
            <path id="Path_226782" d="M848.287 271.045s-.229-5.044.432-6.134a1.527 1.527 0 0 1 .132-.193l.047-.057c.036-.039.068-.079.1-.114l.043-.039.118-.111.036-.025a1.272 1.272 0 0 1 .139-.1l.054-.032a1.218 1.218 0 0 1 .125-.075l.057-.029c.042-.021.086-.043.129-.061l.068-.025.118-.043.079-.022.107-.029.1-.022.075-.014.122-.011h.054a.852.852 0 0 1 .136 0h.025c.047 0 .093.007.136.014h.011a1.085 1.085 0 0 1 .125.029h.007a.468.468 0 0 1 .107.043.36.36 0 0 1 .146.125c-.675.347-1.658.986-1.819 1.859-.247 1.355-.407 2.484-.407 2.484a1.839 1.839 0 0 1 1.526-.322s-1.365.876-1.526 3.521a19.869 19.869 0 0 0 .061 4.01l.84-.715-.043 1a6.1 6.1 0 0 0-.708 2.817 25.308 25.308 0 0 1 .182 3.889c-.146.479.415 5.011.265 5.276s.629 5.394.432 6.091.365 5.326-.1 8.006-.3 9.154.926 11.692 6.259 7.9 11.681 10.891c1.955 1.079 5.769 2.845 9.84 4.664v2.777c-8.21-4.686-18.694-11.03-20.892-14.087-3.728-5.172-3.07-12.346-3.02-14.884s-.279-8.393.332-9.558a24.9 24.9 0 0 1 .025-5.733 13.917 13.917 0 0 1-.1-5.526 17.564 17.564 0 0 1-.043-5.2s-.388-4.637-.28-6.027z" class="cls-6" data-name="Path 226782" transform="translate(-848.248 -262.281)"/>
            <path id="Path_226783" fill="url(#linear-gradient-6)" d="M867.609 287.684c-.075.122-.15.24-.229.361l-.011.014c-.24.365-.5.729-.786 1.1l-.032.043c-.082.1-.161.2-.247.3l-.078.1c-.072.082-.143.168-.214.254l-.1.111-.225.25-.093.1c-.107.118-.218.232-.332.35 0 0-4.6-2.448-10.294-5.694V282.2c4.667 2.077 9.678 4.225 12.641 5.484z" data-name="Path 226783" transform="translate(-830.944 -215.189)"/>
            <path id="Path_226784" d="M850.338 273.287a6.261 6.261 0 0 1 .032 1.423 2.948 2.948 0 0 0-.19 1.837s.258-.726.475-1.265a3.046 3.046 0 0 0-.317-1.995z" class="cls-6" data-name="Path 226784" transform="translate(-843.48 -238.126)"/>
            <path id="Path_226785" d="M851.423 275.13s.143 5.912 1.9 9.168c0 0-.729-1.869-.261-1.944a21.774 21.774 0 0 1-1.639-7.224z" class="cls-6" data-name="Path 226785" transform="translate(-840.076 -233.382)"/>
            <path id="Path_226786" d="M851.891 274.432a42.808 42.808 0 0 1 .129 4.636s-.2-1.9-.282-3.246a8.281 8.281 0 0 1-.794 3.246 14.01 14.01 0 0 0 .39-4.053c-.071-.772.021-1.884.039-2.867a13.22 13.22 0 0 1 .518 2.284z" class="cls-6" data-name="Path 226786" transform="translate(-841.309 -241.058)"/>
            <path id="Path_226787" d="M853.875 287.1a17.35 17.35 0 0 0-1.444-3.46 24.978 24.978 0 0 1-1.14-5.115c-.136-1.144-.972-3.521-1.2-5.211s-.761-5.269-.761-5.269a13.957 13.957 0 0 0-.24-1.855c.211-.629.436-.693.436-.693a15.569 15.569 0 0 1 .172 1.952c-.025.608.343 2.892.34 3.213s.468 2.867.568 3.3.783 3.263.844 3.56.4 1.948.432 2.245a18.163 18.163 0 0 0 1.094 4.271 10.637 10.637 0 0 1 1.315 3.077 26.649 26.649 0 0 1-.408 2.917c-.115-.861.042-2.348-.008-2.932z" class="cls-6" data-name="Path 226787" transform="translate(-846.084 -258.178)"/>
            <path id="Path_226788" d="M851.53 282.943a12.43 12.43 0 0 1-.836-3.9c-.05-1.015-.708-4.107-.786-4.818a46.042 46.042 0 0 0-.633-4.615 10.339 10.339 0 0 0-.375-1.483l.072-.558s.622 2.395.715 2.917.361 2.327.45 2.71.218 1.952.218 1.952a19.527 19.527 0 0 1 .661 3.828c-.143.69.975 4.361 1.34 5.072a13.572 13.572 0 0 1 .711 2.452 20.1 20.1 0 0 0-1.537-3.557z" class="cls-6" data-name="Path 226788" transform="translate(-846.573 -252.854)"/>
            <path id="Path_226789" d="M850.037 271.6a12.6 12.6 0 0 0-.6-4.811 2.96 2.96 0 0 0-.822 1.516 4.067 4.067 0 0 0 1.422 3.295z" class="cls-8" data-name="Path 226789" transform="translate(-847.336 -254.849)"/>
            <path id="Path_226790" d="M849.12 264.8a5.874 5.874 0 0 1 .836 5.04 4.819 4.819 0 0 1-1.1-2.72 5.443 5.443 0 0 1 .264-2.32z" class="cls-8" data-name="Path 226790" transform="translate(-846.712 -259.972)"/>
            <path id="Path_226791" d="M849.441 263.45a8.858 8.858 0 0 1 .608 5.194 4.794 4.794 0 0 1-1.215-2.6 4.946 4.946 0 0 1 .607-2.594z" class="cls-8" data-name="Path 226791" transform="translate(-846.765 -263.45)"/>
            <path id="Path_226792" fill="url(#linear-gradient-7)" d="M849.369 263.506a6.436 6.436 0 0 1 .575 2.041 8.569 8.569 0 0 1 .047 1.905s-.089-1.315-.261-1.437-.322.8-.411.447.018-1.362-.086-1.441-.369.172-.369.172.115-1.433.505-1.687z" data-name="Path 226792" transform="translate(-846.661 -263.306)" style="mix-blend-mode:screen;isolation:isolate"/>
            <path id="Path_226793" fill="url(#linear-gradient-8)" d="M849.154 264.882a4.472 4.472 0 0 1 .8 1.834 6.856 6.856 0 0 1 .147 1.834s-.139-1.026-.322-1.133-.261.8-.375.465-.079-1.326-.189-1.4-.354.2-.354.2a5.182 5.182 0 0 1 .293-1.8z" data-name="Path 226793" transform="translate(-846.671 -259.767)" style="mix-blend-mode:screen;isolation:isolate"/>
            <path id="Path_226794" fill="url(#linear-gradient-9)" d="M849.226 266.848a6.318 6.318 0 0 1 .475 2.077 12.075 12.075 0 0 1 .036 1.708 4.781 4.781 0 0 0-.379-1.18c-.161-.139-.143.393-.143.393a10.149 10.149 0 0 1-.236-1.415c.114-.629-.268.157-.268.157a2.026 2.026 0 0 1 .515-1.74z" data-name="Path 226794" transform="translate(-847.132 -254.706)" style="mix-blend-mode:screen;isolation:isolate"/>
        </g>
        <path id="Path_226795" d="M854.465 292.041a1.222 1.222 0 0 0 .6.711 1.123 1.123 0 0 0 .74.054c1.269-.293 3.017-2.273 4.282-4.993a10.791 10.791 0 0 0 1.247-5.933 1.437 1.437 0 0 0-.711-1.094 1.562 1.562 0 0 0-1.458.257l-.979-.382c1.544-1.265 3.174-1.762 4.457-1.165 1.169.54 1.83 1.884 1.962 3.617a12.826 12.826 0 0 1-1.3 6.191 12.35 12.35 0 0 1-4.329 5.286 3.8 3.8 0 0 1-3.592.54c-1.183-.55-1.848-1.923-1.966-3.7z" class="cls-12" data-name="Path 226795" transform="translate(41.403 40.659)"/>
        <path id="Path_226796" d="M852.106 290.086c.3.175.593.354.89.529.061.808.315 1.38.779 1.594a1.571 1.571 0 0 0 1.469-.268 11.3 11.3 0 0 0 3.553-4.675 11.129 11.129 0 0 0 1.272-5.8 1.535 1.535 0 0 0-.736-1.23 1.608 1.608 0 0 0-1.5.29c-.315-.139-.622-.282-.922-.422 1.54-1.262 3.167-1.751 4.446-1.158 1.208.561 1.877 1.98 1.973 3.807a14.137 14.137 0 0 1-5.094 10.916c-1.447 1.076-2.945 1.469-4.143.912-1.358-.627-2.034-2.339-1.987-4.495z" class="cls-13" data-name="Path 226796" transform="translate(38.029 39.253)"/>
        <path id="Path_226797" d="M850.686 289.137c.289.186.583.372.879.561.054.84.307 1.437.783 1.658a1.569 1.569 0 0 0 1.465-.264 11.3 11.3 0 0 0 3.56-4.679 12.8 12.8 0 0 0 1.326-5.054c0-1-.258-1.726-.79-1.973a1.812 1.812 0 0 0-1.766.468c-.264-.175-.518-.347-.765-.518 1.569-1.319 3.238-1.848 4.55-1.24 1.419.661 2.095 2.506 1.973 4.807a13.905 13.905 0 0 1-1.315 5 12.907 12.907 0 0 1-3.842 4.954c-1.43 1.047-2.9 1.419-4.078.869-1.376-.636-2.052-2.387-1.98-4.589z" class="cls-8" data-name="Path 226797" transform="translate(34.366 37.058)"/>
        <path id="Path_226798" fill="url(#radial-gradient)" d="M856.941 285.9c1.587-3.749 1.708-7.142.361-7.942 2.387 1.069 3.077 4.668 1.49 8.418s-4.886 6.327-7.628 6.084c1.699-.022 4.19-2.807 5.777-6.56z" data-name="Path 226798" transform="translate(35.613 37.349)" style="mix-blend-mode:screen;isolation:isolate"/>
        <path id="Path_226799" fill="url(#radial-gradient-2)" d="M858.362 286.757c1.587-3.753 1.712-7.145.361-7.946 2.391 1.069 3.077 4.668 1.49 8.418s-4.886 6.33-7.624 6.084c1.698-.022 4.189-2.807 5.773-6.556z" data-name="Path 226799" transform="translate(39.279 39.545)" style="mix-blend-mode:screen;isolation:isolate"/>
        <path id="Path_226800" fill="url(#radial-gradient-3)" d="M859.67 287.3c1.583-3.75 1.708-7.142.357-7.946 2.391 1.069 3.077 4.668 1.49 8.421s-4.886 6.327-7.624 6.08c1.699-.019 4.19-2.804 5.777-6.555z" data-name="Path 226800" transform="translate(42.639 40.948)" style="mix-blend-mode:screen;isolation:isolate"/>
        <ellipse id="Ellipse_11314" cx="4.729" cy="8.619" class="cls-17" data-name="Ellipse 11314" rx="4.729" ry="8.619" transform="rotate(-24.915 1142.93 -1765.834)"/>
        <ellipse id="Ellipse_11315" cx="4.729" cy="8.619" fill="url(#linear-gradient-11)" data-name="Ellipse 11315" rx="4.729" ry="8.619" transform="rotate(-24.9 1150.093 -1742.412)"/>
        <ellipse id="Ellipse_11316" cx="4.729" cy="8.619" fill="url(#linear-gradient-12)" data-name="Ellipse 11316" rx="4.729" ry="8.619" transform="rotate(-24.9 1148.01 -1753.952)"/>
        <ellipse id="Ellipse_11317" cx="4.729" cy="8.619" class="cls-17" data-name="Ellipse 11317" rx="4.729" ry="8.619" transform="rotate(-24.915 1142.93 -1765.834)"/>
        <g id="Group_110182" data-name="Group 110182" transform="translate(837.331 263.45)">
            <path id="Path_226801" d="M847.8 263.969l.054.029z" class="cls-4" data-name="Path 226801" transform="translate(-810.383 -262.114)"/>
            <path id="Path_226802" d="M847.85 264l.054.032z" class="cls-4" data-name="Path 226802" transform="translate(-810.252 -262.039)"/>
            <path id="Path_226803" d="M851.285 324.468c5.426-2.992 10.458-8.353 11.684-10.891s1.387-9.011.926-11.692.1-7.313-.1-8.006.579-5.826.429-6.091.414-4.8.264-5.276a26.276 26.276 0 0 1 .183-3.889 6.125 6.125 0 0 0-.7-2.817l-.042-1 .84.715a20.192 20.192 0 0 0 .061-4.01c-.161-2.645-1.53-3.521-1.53-3.521a1.85 1.85 0 0 1 1.53.322s-.161-1.129-.407-2.484c-.161-.872-1.144-1.512-1.823-1.859a17.314 17.314 0 0 0-.4 3.349c-.054.554-.375 3.285-.475 3.875s-.465 3.12-.768 4.046-1.433 5.376-1.648 6.187a49.882 49.882 0 0 1-1.952 5.973 18.761 18.761 0 0 0-1.9 4.561 8.471 8.471 0 0 0-.039 1.119 12.911 12.911 0 0 0-.522 2.28c-.125 1.115-.608 6.595-.733 8.218s-.686 6.516-2.284 8.518-5.272 4.615-10.419 6.77v10.269c4.059-1.821 7.869-3.586 9.825-4.666z" class="cls-4" data-name="Path 226803" transform="translate(-826.733 -262.124)"/>
            <path id="Path_226804" d="M847.987 264.116l.05.057z" class="cls-4" data-name="Path 226804" transform="translate(-809.899 -261.736)"/>
            <path id="Path_226805" d="M847.947 264.073l.043.039z" class="cls-4" data-name="Path 226805" transform="translate(-810.002 -261.846)"/>
            <path id="Path_226806" d="M847.512 263.93a.341.341 0 0 0-.146.125.341.341 0 0 1 .146-.125z" class="cls-4" data-name="Path 226806" transform="translate(-811.5 -262.214)"/>
            <path id="Path_226807" d="M847.744 263.945l.068.025z" class="cls-4" data-name="Path 226807" transform="translate(-810.524 -262.176)"/>
            <path id="Path_226808" d="M847.9 264.035l.032.025z" class="cls-4" data-name="Path 226808" transform="translate(-810.113 -261.944)"/>
            <path id="Path_226809" d="M847.438 263.938a1.174 1.174 0 0 1 .129-.029 1.174 1.174 0 0 0-.129.029z" class="cls-4" data-name="Path 226809" transform="translate(-811.312 -262.268)"/>
            <path id="Path_226810" d="M847.407 263.961a.478.478 0 0 1 .107-.043.478.478 0 0 0-.107.043z" class="cls-4" data-name="Path 226810" transform="translate(-811.392 -262.245)"/>
            <path id="Path_226811" d="M847.689 263.927l.075.022z" class="cls-4" data-name="Path 226811" transform="translate(-810.666 -262.222)"/>
            <path id="Path_226812" d="M847.477 263.919c.043-.007.089-.011.136-.014-.046.004-.093.007-.136.014z" class="cls-4" data-name="Path 226812" transform="translate(-811.212 -262.279)"/>
            <path id="Path_226813" d="M847.629 263.913l.107.022z" class="cls-4" data-name="Path 226813" transform="translate(-810.82 -262.258)"/>
            <path id="Path_226814" d="M847.575 263.906l.118.011z" class="cls-4" data-name="Path 226814" transform="translate(-810.96 -262.276)"/>
            <path id="Path_226815" d="M847.521 263.907a.9.9 0 0 1 .139 0 .9.9 0 0 0-.139 0z" class="cls-4" data-name="Path 226815" transform="translate(-811.098 -262.281)"/>
            <path id="Path_226816" fill="url(#linear-gradient-14)" d="M839.407 295.08c2.963-1.258 7.974-3.406 12.639-5.487v-10.269c-5.147 2.155-14.226 5.426-14.226 5.426a12.381 12.381 0 0 0 1.587 10.33z" data-name="Path 226816" transform="translate(-837.331 -222.585)"/>
            <path id="Path_226818" d="M847.659 263.919l.107.029z" class="cls-6" data-name="Path 226818" transform="translate(-810.743 -262.243)"/>
            <path id="Path_226819" d="M847.56 263.9h.054z" class="cls-6" data-name="Path 226819" transform="translate(-810.998 -262.279)"/>
            <path id="Path_226820" d="M847.608 263.909l.075.014z" class="cls-6" data-name="Path 226820" transform="translate(-810.875 -262.268)"/>
            <path id="Path_226821" d="M848 264.132a1.529 1.529 0 0 1 .132.193 1.529 1.529 0 0 0-.132-.193z" class="cls-6" data-name="Path 226821" transform="translate(-809.863 -261.694)"/>
            <path id="Path_226826" d="M847.913 264.042c.043.036.082.072.122.111-.04-.039-.079-.075-.122-.111z" class="cls-6" data-name="Path 226826" transform="translate(-810.089 -261.926)"/>
            <path id="Path_226827" d="M847.763 263.952c.043.018.086.039.129.061-.043-.022-.086-.043-.129-.061z" class="cls-6" data-name="Path 226827" transform="translate(-810.476 -262.158)"/>
            <path id="Path_226828" d="M847.959 264.084a1.5 1.5 0 0 1 .1.114 1.5 1.5 0 0 0-.1-.114z" class="cls-6" data-name="Path 226828" transform="translate(-809.971 -261.818)"/>
            <path id="Path_226829" d="M847.865 264.007a1.715 1.715 0 0 1 .139.1 1.715 1.715 0 0 0-.139-.1z" class="cls-6" data-name="Path 226829" transform="translate(-810.213 -262.016)"/>
            <path id="Path_226830" d="M847.814 263.977a1.208 1.208 0 0 1 .129.075 1.208 1.208 0 0 0-.129-.075z" class="cls-6" data-name="Path 226830" transform="translate(-810.344 -262.093)"/>
            <path id="Path_226831" d="M847.71 263.933l.122.043z" class="cls-6" data-name="Path 226831" transform="translate(-810.612 -262.207)"/>
            <path id="Path_226832" d="M865.435 271.045s.225-5.044-.432-6.134a1.528 1.528 0 0 0-.132-.193l-.05-.057a1.5 1.5 0 0 0-.1-.114l-.043-.039c-.04-.039-.079-.075-.122-.111l-.032-.025a1.7 1.7 0 0 0-.139-.1l-.054-.032a1.219 1.219 0 0 0-.129-.075l-.054-.029c-.043-.021-.086-.043-.129-.061l-.068-.025-.122-.043-.075-.022-.107-.029-.107-.022-.075-.014-.118-.011h-.054a.9.9 0 0 0-.139 0h-.021c-.046 0-.093.007-.136.014h-.011a1.171 1.171 0 0 0-.129.029.481.481 0 0 0-.107.043.34.34 0 0 0-.146.125c.679.347 1.662.986 1.823 1.859.247 1.355.407 2.484.407 2.484a1.85 1.85 0 0 0-1.53-.322s1.369.876 1.53 3.521a20.192 20.192 0 0 1-.061 4.01l-.84-.715.042 1a6.125 6.125 0 0 1 .7 2.817 26.276 26.276 0 0 0-.183 3.889c.15.479-.411 5.011-.264 5.276s-.629 5.394-.429 6.091-.364 5.326.1 8.006.3 9.154-.926 11.692-6.259 7.9-11.684 10.891c-1.955 1.079-5.766 2.845-9.836 4.664v2.777c8.21-4.686 18.69-11.03 20.892-14.087 3.728-5.172 3.07-12.346 3.017-14.884s.279-8.393-.329-9.558a24.893 24.893 0 0 0-.025-5.733 13.881 13.881 0 0 0 .1-5.526 17.5 17.5 0 0 0 .046-5.2s.389-4.637.281-6.027z" class="cls-6" data-name="Path 226832" transform="translate(-826.733 -262.281)"/>
            <path id="Path_226833" fill="url(#linear-gradient-15)" d="M837.912 287.684c.072.122.15.24.229.361l.007.014c.239.365.5.729.79 1.1l.032.043c.078.1.161.2.243.3l.082.1c.068.082.139.168.214.254l.1.111c.072.082.147.168.225.25l.093.1c.108.118.218.232.329.35 0 0 4.6-2.448 10.3-5.694V282.2c-4.67 2.077-9.681 4.225-12.644 5.484z" data-name="Path 226833" transform="translate(-835.836 -215.189)"/>
            <path id="Path_226834" d="M846.539 273.287a6.281 6.281 0 0 0-.028 1.423 2.974 2.974 0 0 1 .189 1.837s-.257-.726-.475-1.265a3.055 3.055 0 0 1 .314-1.995z" class="cls-6" data-name="Path 226834" transform="translate(-814.66 -238.126)"/>
            <path id="Path_226835" d="M846.367 275.13s-.146 5.912-1.905 9.168c0 0 .729-1.869.261-1.944a21.889 21.889 0 0 0 1.644-7.224z" class="cls-6" data-name="Path 226835" transform="translate(-818.974 -233.382)"/>
            <path id="Path_226836" d="M845.313 274.432a44.054 44.054 0 0 0-.125 4.636s.2-1.9.279-3.246a8.259 8.259 0 0 0 .8 3.246 14.191 14.191 0 0 1-.393-4.053c.071-.772-.022-1.884-.036-2.867a12.614 12.614 0 0 0-.525 2.284z" class="cls-6" data-name="Path 226836" transform="translate(-817.158 -241.058)"/>
            <path id="Path_226837" d="M846.289 287.1a17.35 17.35 0 0 1 1.444-3.46 24.986 24.986 0 0 0 1.141-5.115c.132-1.144.972-3.521 1.2-5.211s.761-5.269.761-5.269a14.04 14.04 0 0 1 .243-1.855c-.211-.629-.436-.693-.436-.693a15.3 15.3 0 0 0-.175 1.952c.025.608-.343 2.892-.34 3.213s-.465 2.867-.565 3.3-.783 3.263-.844 3.56-.4 1.948-.436 2.245a17.979 17.979 0 0 1-1.09 4.271 10.62 10.62 0 0 0-1.315 3.077 25.9 25.9 0 0 0 .407 2.917c.116-.861-.045-2.348.005-2.932z" class="cls-6" data-name="Path 226837" transform="translate(-815.339 -258.178)"/>
            <path id="Path_226838" d="M847.886 282.943a12.319 12.319 0 0 0 .836-3.9c.054-1.015.711-4.107.786-4.818s.154-1.977.636-4.615a10.311 10.311 0 0 1 .375-1.483l-.071-.558s-.622 2.395-.715 2.917-.361 2.327-.454 2.71-.214 1.952-.214 1.952a19.527 19.527 0 0 0-.661 3.828c.139.69-.976 4.361-1.341 5.072a13.583 13.583 0 0 0-.711 2.452 20.4 20.4 0 0 1 1.534-3.557z" class="cls-6" data-name="Path 226838" transform="translate(-814.105 -252.854)"/>
            <path id="Path_226839" d="M847.434 271.6a12.641 12.641 0 0 1 .593-4.811 2.95 2.95 0 0 1 .825 1.516 4.078 4.078 0 0 1-1.418 3.295z" class="cls-8" data-name="Path 226839" transform="translate(-811.394 -254.849)"/>
            <path id="Path_226840" d="M848.3 264.8a5.886 5.886 0 0 0-.836 5.04 4.81 4.81 0 0 0 1.1-2.72 5.4 5.4 0 0 0-.264-2.32z" class="cls-8" data-name="Path 226840" transform="translate(-811.971 -259.972)"/>
            <path id="Path_226841" d="M847.971 263.45a8.861 8.861 0 0 0-.608 5.194 4.791 4.791 0 0 0 1.215-2.6 4.946 4.946 0 0 0-.607-2.594z" class="cls-8" data-name="Path 226841" transform="translate(-811.906 -263.45)"/>
            <path id="Path_226842" fill="url(#linear-gradient-16)" d="M847.884 263.506a6.388 6.388 0 0 0-.579 2.041 8.434 8.434 0 0 0-.043 1.905s.089-1.315.261-1.437.318.8.411.447-.022-1.362.086-1.441.365.172.365.172-.111-1.433-.501-1.687z" data-name="Path 226842" transform="translate(-811.852 -263.306)" style="mix-blend-mode:screen;isolation:isolate"/>
            <path id="Path_226843" fill="url(#linear-gradient-17)" d="M848.164 264.882a4.542 4.542 0 0 0-.8 1.834 6.948 6.948 0 0 0-.15 1.834s.143-1.026.325-1.133.261.8.375.465.078-1.326.189-1.4.354.2.354.2a5.211 5.211 0 0 0-.293-1.8z" data-name="Path 226843" transform="translate(-811.909 -259.767)" style="mix-blend-mode:screen;isolation:isolate"/>
            <path id="Path_226844" fill="url(#linear-gradient-18)" d="M847.961 266.848a6.415 6.415 0 0 0-.475 2.077 11.687 11.687 0 0 0-.032 1.708 4.781 4.781 0 0 1 .379-1.18c.157-.139.139.393.139.393a10.265 10.265 0 0 0 .24-1.415c-.115-.629.268.157.268.157a2.02 2.02 0 0 0-.519-1.74z" data-name="Path 226844" transform="translate(-811.316 -254.706)" style="mix-blend-mode:screen;isolation:isolate"/>
        </g>
        <path id="Path_226845" d="M849.032 292.041a1.222 1.222 0 0 1-.6.711 1.122 1.122 0 0 1-.74.054c-1.269-.293-3.021-2.273-4.282-4.993a10.788 10.788 0 0 1-1.247-5.933 1.443 1.443 0 0 1 .708-1.094 1.569 1.569 0 0 1 1.462.257l.979-.382c-1.544-1.265-3.174-1.762-4.457-1.165-1.169.54-1.83 1.884-1.962 3.617a12.824 12.824 0 0 0 1.3 6.191 12.355 12.355 0 0 0 4.328 5.286 3.791 3.791 0 0 0 3.589.54c1.183-.55 1.851-1.923 1.97-3.7z" class="cls-12" data-name="Path 226845" transform="translate(3.951 40.659)"/>
        <path id="Path_226846" d="M851.406 290.086c-.3.175-.593.354-.89.529-.061.808-.318 1.38-.779 1.594a1.571 1.571 0 0 1-1.469-.268 11.3 11.3 0 0 1-3.553-4.675 11.126 11.126 0 0 1-1.273-5.8 1.534 1.534 0 0 1 .736-1.23 1.608 1.608 0 0 1 1.5.29c.311-.139.618-.282.922-.422-1.541-1.262-3.167-1.751-4.446-1.158-1.208.561-1.876 1.98-1.977 3.807a14.158 14.158 0 0 0 5.1 10.916c1.448 1.076 2.945 1.469 4.139.912 1.361-.627 2.037-2.339 1.99-4.495z" class="cls-13" data-name="Path 226846" transform="translate(7.31 39.253)"/>
        <path id="Path_226847" d="M852.824 289.137c-.289.186-.586.372-.883.561-.05.84-.307 1.437-.783 1.658a1.564 1.564 0 0 1-1.462-.264 11.271 11.271 0 0 1-3.56-4.679 12.861 12.861 0 0 1-1.33-5.054c0-1 .261-1.726.794-1.973a1.813 1.813 0 0 1 1.765.468c.265-.175.519-.347.765-.518-1.569-1.319-3.242-1.848-4.55-1.24-1.423.661-2.095 2.506-1.973 4.807a13.833 13.833 0 0 0 1.315 5 12.9 12.9 0 0 0 3.843 4.954c1.426 1.047 2.9 1.419 4.078.869 1.377-.636 2.057-2.387 1.981-4.589z" class="cls-8" data-name="Path 226847" transform="translate(10.975 37.058)"/>
        <path id="Path_226848" fill="url(#radial-gradient-4)" d="M844.588 285.9c-1.587-3.749-1.709-7.142-.361-7.942-2.391 1.069-3.077 4.668-1.49 8.418s4.886 6.327 7.624 6.084c-1.699-.022-4.186-2.807-5.773-6.56z" data-name="Path 226848" transform="translate(11.709 37.349)" style="mix-blend-mode:screen;isolation:isolate"/>
        <path id="Path_226849" fill="url(#radial-gradient-5)" d="M843.161 286.757c-1.587-3.753-1.709-7.145-.357-7.946-2.391 1.069-3.077 4.668-1.491 8.418s4.886 6.33 7.624 6.084c-1.698-.022-4.189-2.807-5.776-6.556z" data-name="Path 226849" transform="translate(8.045 39.545)" style="mix-blend-mode:screen;isolation:isolate"/>
        <path id="Path_226850" fill="url(#radial-gradient-6)" d="M841.856 287.3c-1.587-3.75-1.709-7.142-.357-7.946-2.391 1.069-3.077 4.668-1.491 8.421s4.886 6.327 7.624 6.08c-1.698-.019-4.189-2.804-5.776-6.555z" data-name="Path 226850" transform="translate(4.686 40.948)" style="mix-blend-mode:screen;isolation:isolate"/>
    </g>
</svg>
