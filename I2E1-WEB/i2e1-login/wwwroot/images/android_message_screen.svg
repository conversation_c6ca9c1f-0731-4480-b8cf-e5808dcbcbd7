<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 420 640">
  <defs>
    <style>
      .cls-1 {
        fill: #0083d3;
        stroke: #c6c6c6;
      }

      .cls-2 {
        clip-path: url(#clip-phone_mock);
      }

      .cls-11, .cls-12, .cls-3, .cls-5 {
        fill: #fff;
      }

      .cls-3, .cls-5 {
        stroke: #e5e5e5;
      }

      .cls-3 {
        stroke-width: 4px;
      }

      .cls-4 {
        fill: #e5e5e5;
      }

      .cls-5 {
        stroke-width: 2px;
      }

      .cls-6 {
        clip-path: url(#clip-path);
      }

      .cls-13, .cls-7 {
        fill: none;
      }

      .cls-8 {
        fill: #1da1f2;
      }

      .cls-9 {
        fill: rgba(0,0,0,0.17);
      }

      .cls-10 {
        opacity: 0.9;
      }

      .cls-11 {
        font-size: 14px;
        font-family: Roboto-Medium, Roboto;
        font-weight: 500;
      }

      .cls-13 {
        opacity: 0.5;
      }

      .cls-14 {
        stroke: none;
      }

      .cls-15 {
        filter: url(#Rectangle_1247);
      }

      .cls-16 {
        filter: url(#Rectangle_1);
      }
    </style>
    <filter id="Rectangle_1" x="8" y="10" width="404" height="786" filterUnits="userSpaceOnUse">
      <feOffset dy="3" input="SourceAlpha"/>
      <feGaussianBlur stdDeviation="3" result="blur"/>
      <feFlood flood-opacity="0.161"/>
      <feComposite operator="in" in2="blur"/>
      <feComposite in="SourceGraphic"/>
    </filter>
    <clipPath id="clip-path">
      <rect id="Rectangle_2" data-name="Rectangle 2" class="cls-1" width="360" height="640" rx="8" transform="translate(6946 1804)"/>
    </clipPath>
    <filter id="Rectangle_1247" x="-9" y="627" width="438" height="19" filterUnits="userSpaceOnUse">
      <feOffset dy="-3" input="SourceAlpha"/>
      <feGaussianBlur stdDeviation="3" result="blur-2"/>
      <feFlood flood-opacity="0.161"/>
      <feComposite operator="in" in2="blur-2"/>
      <feComposite in="SourceGraphic"/>
    </filter>
    <clipPath id="clip-phone_mock">
      <rect width="420" height="640"/>
    </clipPath>
  </defs>
  <g id="phone_mock" data-name="phone mock" class="cls-2">
    <g id="Group_4" data-name="Group 4" transform="translate(-6245 -2773)">
      <g class="cls-16" transform="matrix(1, 0, 0, 1, 6245, 2773)">
        <g id="Rectangle_1-2" data-name="Rectangle 1" class="cls-3" transform="translate(17 16)">
          <rect class="cls-14" width="386" height="768" rx="36"/>
          <rect class="cls-7" x="2" y="2" width="382" height="764" rx="34"/>
        </g>
      </g>
      <circle id="Ellipse_1" data-name="Ellipse 1" class="cls-4" cx="8" cy="8" r="8" transform="translate(6517.807 2821.133)"/>
      <circle id="Ellipse_2" data-name="Ellipse 2" class="cls-4" cx="4" cy="4" r="4" transform="translate(6547.807 2826.133)"/>
      <rect id="Rectangle_4" data-name="Rectangle 4" class="cls-4" width="100" height="8" rx="4" transform="translate(6405 2829)"/>
      <g id="Rectangle_1246" data-name="Rectangle 1246" class="cls-5" transform="translate(6273 2851)">
        <rect class="cls-14" width="364" height="644" rx="10"/>
        <rect class="cls-7" x="1" y="1" width="362" height="642" rx="9"/>
      </g>
      <g id="Mask_Group_1" data-name="Mask Group 1" class="cls-6" transform="translate(-671 1049)">
        <g id="Group_1182" data-name="Group 1182">
          <g id="app_bar_light" data-name="app bar light" transform="translate(5688 2353)">
            <path id="rectangle-3" class="cls-7" d="M0,0H360V80H0Z" transform="translate(1258 -548)"/>
            <rect id="ui_appBar_light" class="cls-8" width="360" height="80" transform="translate(1258 -549)"/>
            <g id="status_bar" data-name="status bar" transform="translate(1258 -549)">
              <rect id="status_bar_bg" data-name="status bar bg" class="cls-9" width="360" height="24"/>
              <g id="status_bar_contents" data-name="status bar contents" transform="translate(256 4)">
                <g id="time" class="cls-10" transform="translate(60)">
                  <text id="_12:30" data-name="12:30" class="cls-11" transform="translate(36 15)"><tspan x="-35.54" y="0">19:47</tspan></text>
                </g>
                <g id="battery" transform="translate(41)">
                  <path id="bounds" class="cls-7" d="M0,0H16V16H0Z"/>
                  <path id="Shape" class="cls-12" d="M6,.875V0H3V.875H0V14H9V.875Z" transform="translate(3 1)"/>
                </g>
                <g id="cellular" transform="translate(21)">
                  <path id="bounds-2" data-name="bounds" class="cls-7" d="M0,0H16V16H0Z"/>
                  <path id="Shape-2" data-name="Shape" class="cls-12" d="M0,14H14V0Z" transform="translate(0 1)"/>
                </g>
                <g id="wifi">
                  <path id="bounds-3" data-name="bounds" class="cls-7" d="M0,0H16V16H0Z" transform="translate(2)"/>
                  <path id="Shape-3" data-name="Shape" class="cls-12" d="M0,3.016a15,15,0,0,1,18.046,0L9.023,14Z" transform="translate(0.977 1)"/>
                </g>
              </g>
            </g>
          </g>
        </g>
      </g>
    </g>
    <g id="ic_back_arrow" transform="translate(29 75)">
      <g id="Group_283" data-name="Group 283" transform="translate(16 40)">
        <rect id="Rectangle_155" data-name="Rectangle 155" class="cls-7" width="24" height="24"/>
        <path id="Path_77" data-name="Path 77" class="cls-12" d="M20,11H7.8l5.6-5.6L12,4,4,12l8,8,1.4-1.4L7.8,13H20Z"/>
      </g>
    </g>
    <g id="Group_1183" data-name="Group 1183" transform="translate(33 76)">
      <rect id="Rectangle_160" data-name="Rectangle 160" class="cls-13" width="24" height="24" transform="translate(320 40)"/>
      <path id="ic_more_vert_24px" class="cls-12" d="M12,8a2,2,0,1,0-2-2A2.006,2.006,0,0,0,12,8Zm0,2a2,2,0,1,0,2,2A2.006,2.006,0,0,0,12,10Zm0,6a2,2,0,1,0,2,2A2.006,2.006,0,0,0,12,16Z" transform="translate(326 40)"/>
    </g>
    <g class="cls-15" transform="matrix(1, 0, 0, 1, 0, 0)">
      <rect id="Rectangle_1247-2" data-name="Rectangle 1247" class="cls-12" width="420" height="1" transform="translate(0 639)"/>
    </g>
  </g>
</svg>
