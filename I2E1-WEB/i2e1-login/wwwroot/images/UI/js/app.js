(function(e){function t(t){for(var n,a,r=t[0],c=t[1],l=t[2],d=0,h=[];d<r.length;d++)a=r[d],Object.prototype.hasOwnProperty.call(s,a)&&s[a]&&h.push(s[a][0]),s[a]=0;for(n in c)Object.prototype.hasOwnProperty.call(c,n)&&(e[n]=c[n]);u&&u(t);while(h.length)h.shift()();return o.push.apply(o,l||[]),i()}function i(){for(var e,t=0;t<o.length;t++){for(var i=o[t],n=!0,r=1;r<i.length;r++){var c=i[r];0!==s[c]&&(n=!1)}n&&(o.splice(t--,1),e=a(a.s=i[0]))}return e}var n={},s={app:0},o=[];function a(t){if(n[t])return n[t].exports;var i=n[t]={i:t,l:!1,exports:{}};return e[t].call(i.exports,i,i.exports,a),i.l=!0,i.exports}a.m=e,a.c=n,a.d=function(e,t,i){a.o(e,t)||Object.defineProperty(e,t,{enumerable:!0,get:i})},a.r=function(e){"undefined"!==typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},a.t=function(e,t){if(1&t&&(e=a(e)),8&t)return e;if(4&t&&"object"===typeof e&&e&&e.__esModule)return e;var i=Object.create(null);if(a.r(i),Object.defineProperty(i,"default",{enumerable:!0,value:e}),2&t&&"string"!=typeof e)for(var n in e)a.d(i,n,function(t){return e[t]}.bind(null,n));return i},a.n=function(e){var t=e&&e.__esModule?function(){return e["default"]}:function(){return e};return a.d(t,"a",t),t},a.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},a.p="/";var r=window["webpackJsonp"]=window["webpackJsonp"]||[],c=r.push.bind(r);r.push=t,r=r.slice();for(var l=0;l<r.length;l++)t(r[l]);var u=c;o.push([0,"chunk-vendors"]),i()})({0:function(e,t,i){e.exports=i("56d7")},"143f":function(e){e.exports=JSON.parse('{"hilao":"aiyoo","dataVoucherState":{"choose_your_plan":"మీ ప్లాన్ ఎంచుకోండి","connect":"కనెక్ట్","enter_voucher_code":"కూపన్ కోడ్ నమోదు చేయండి","free_internet":"ఉచిత ఇంటర్నెట్","use_coupon":"వినియోగదారుడి కూపన్"},"errorState":{"try_logging_again":"మళ్ళీ లాగింగ్ ప్రయత్నించండి"},"firstState":{"10_digit_phone_error":"ఫోన్ నంబర్కు 10 అంకెలు ఉండాలి","bark_lessloud":"మీ ఫోన్ నంబర్ తో సైన్ ఇన్ చేయండి","bark_loud":"లైఫ్ టైమ్ ఉచిత వైఫై పొందండి","connect":"కనెక్ట్","continue_as":"కొనసాగించడానికి మీ ఖాతాను ఎంచుకోండి","dont_have_national_id_click_here":"పాస్పోర్ట్ / జాతీయ ఐడి లేదా?  <a href=\\"/Login/GetAuthenticationPage/?authType=PHONE\\">ఇక్కడ నొక్కండి</a>","dont_have_phno_click_here":"ఫోన్ నంబర్ లేదా? <a href=\\"/Login/GetAuthenticationPage/?authType=NATIONAL_ID\\">ఇక్కడ నొక్కండి</a>","enter_national_id":"పాస్పోర్ట్ లేదా ప్రభుత్వ అధికార గుర్తింపుని నమోదు చేయండి","enter_phone_number":"ఉదాహరణ: 9876543201","fdm_notauthorised":"దయచేసి ఇంటర్నెట్ యాక్సెస్ కోసం రిసెప్షన్‌ను సంప్రదించండి","generate_otp":"సైన్ ఇన్ చేయండి","invalid_phno":"చెల్లని ఫోన్ నంబర్","last_name":"చివరి పేరు","mobile_invalid":"చెల్లని ఫోన్ నంబర్","or":"లేదా","room_no":"రూమ్ సంఖ్య","sign_in_as_guest":"అతిథిగా సైన్ ఇన్ చేయండి","tnc":"కొనసాగించడం ద్వారా మీరు మా <a onclick=\'_onTileClick(\\"/Templates/i2e1/tnc.html\\")\'>నిబంధనలు మరియు షరతులను</a> అంగీకరిస్తున్నారు","welcome_back":"పునఃస్వాగతం!"},"footer":{"facing_issues_call_shopkeeper":"సహాయానికి దుకాణదారుడు ని సంప్రదించండి","facing_issues_call_us_at":"సహాయం పొందండి - <b><a href=\\"tel:8880322222\\">88803 22222</a></b>"},"help":"సహాయం","helpState":{"enter_mobile_number":"మొబైల్ నంబర్‌ను నమోదు చేయండి","enter_password":"4 అంకెల పాస్వర్డ్ను నమోదు చేయండి","help":"సహాయం","i2e1_connects_you_to_wifi":"i2e1 మిమ్మల్ని ఉచిత Wi-Fi కి కనెక్ట్ చేస్తుంది","three_simple_steps_to_wifi":"Wi-Fi పొందడానికి 3 సులభమైన దశలు"},"landingState":{"congratulations":"అభినందనలు!","connected_msg":"మీరు ఇప్పుడు కనెక్ట్ అయ్యారు","data_left":"మిగిలిన డేటా","logout":"లాగ్అవుట్","search":"శోధన","time_day":"రోజు","time_days":"రోజులు","time_hr":"గంట","time_hrs":"గంటలు","time_left":"మిగిలిన సమయం","time_lessthan_a_minute":"ఒక నిమిషం కన్నా తక్కువ","time_min":"నిమిషం","time_mins":"నిమిషాలు","you_are_now_connected":"మీరు ఇప్పుడు కనెక్ట్ చేయబడ్డారు"},"logout":"లాగ్అవుట్","please_configure_nas_id":"దయచేసి వైఫై ఐడెంటిఫైయర్ను కాన్ఫిగర్ చేయండి","redirectionState":{"data_policy":"ఇప్పుడు ఉచిత <span class=\\"data\\">%{data}</span> వై-ఫై ఉపయోగించండి","please_wait":"బాగా చేసారు!","time_policy":"సమయం వ్యవధి: <span class=\\"time\\">%{time}</span>","you_are_almost_there":"5 క్షణాలలో కనెక్ట్ చేయబడుతుంది"},"secondState":{"back":"ఫోన్ నంబర్ని మార్చడానికి వెనుకకు వెళ్ళoడి","bark_lessloud":"మీ OTP SMS ద్వారా పంపబడుతుంది","bark_loud":"ఫోన్ నంబర్ ధృవీకరణ","captcha":"క్యాప్ఛా నమోదు చేయండి","change":"ఫోన్ నంబర్ మార్చండి","confirm":"నిర్ధారించండి","connect":"ఉచిత వైఫైకి కనెక్ట్ అవ్వండి","enter_4_digit_password":"4 అంకెల పాస్‌వర్డ్‌ను నమోదు చేయండి","enter_4_digit_password_sent_to_mobile":"{0}  కు పంపిన పాస్‌వర్డ్‌ను నమోదు చేయండి","enter_captcha":"క్యాప్ ఛా నమోదు చేయండి","enter_voucher_code":"రసీదు కోడ్ నమోదు చేయండి","not_getting_otp_click_here":"OTP అందలేదా?  <a onclick=\\"$(\'#otp_access_code\').show();$(\'#otp\').val(\'1234\').hide()\\">ఇక్కడ నొక్కండి</a>","questions_cta_okay":"సరే","questions_lessloud":"సాధారణ ప్రశ్నలకు సమాధానం ఇవ్వండి","questions_loud":"ఈ రోజు <b>1</b> జిబి ఉచిత ఇంటర్నెట్ పొందండి","resend":"మరల పంపు","welcomeback_greetings":"<img src=\\"%{prefix}images/wiom/welcomeback.en.svg\\" />","otp_invalid":"చెల్లని OTP"}}')},"499d":function(e,t,i){i("c975"),i("b0c0"),i("ac1f"),i("5319"),e.exports={_searchObjectInArray:function(e,t,i){for(var n=0;n<i.length;n++)if(i[n][e]===t)return i[n]},getMobile:function(e){return e.mobile&&""!=e.mobile?e.mobile:e.i2e1Data.loginUser.tempmobile?e.i2e1Data.loginUser.tempmobile:""},makei2e1Login:function(e){for(var t=document.createElement("form"),i=e.url+"?",n=(e.isAutoLogin,0);n<e.parameters.length;++n){var s=e.parameters[n].value;if("localhost"!==window.location.hostname||"url"!==e.parameters[n].name&&"userurl"!==e.parameters[n].name){var o=document.createElement("input");o.setAttribute("name",e.parameters[n].name),o.setAttribute("value",s),t.appendChild(o),i+=e.parameters[n].name+"="+encodeURIComponent(s)+"&"}else i=e.parameters[n].value;"url"!==e.parameters[n].name&&"userurl"!==e.parameters[n].name||e.parameters[n].value.indexOf(window.location.hostname)<0&&e.parameters[n].value.toLowerCase().indexOf("jsonlanding")<0&&e.parameters[n].value}if(window.location.href.indexOf("doTest")>-1){var a=this._searchObjectInArray("name","userurl",e.parameters);window.location.href=a.value}else"https:"===window.location.protocol?window.location.href=i.replace("JsonLanding","LoggedIn"):e.isGet||"localhost"===window.location.hostname?window.location.href=i:(t.attr("action",e.url),t.attr("method","post"),o=document.createElement("input"),o.setAttribute("type","submit"),t.append(o),document.body.appendChild(t),t.submit())},doLogin:function(){this.makei2e1Login(window.landingPage)}}},"49f8":function(e,t,i){var n={"./en.json":"edd4","./hi.json":"7a03","./te.json":"143f"};function s(e){var t=o(e);return i(t)}function o(e){if(!i.o(n,e)){var t=new Error("Cannot find module '"+e+"'");throw t.code="MODULE_NOT_FOUND",t}return n[e]}s.keys=function(){return Object.keys(n)},s.resolve=o,e.exports=s,s.id="49f8"},"56d7":function(e,t,i){"use strict";i.r(t);i("4160"),i("d3b7"),i("ac1f"),i("466d"),i("1276"),i("159b"),i("ddb0"),i("e260"),i("e6cf"),i("cca6"),i("a79d");var n=i("7a23"),s={class:"top-container"};function o(e,t,i,o,a,r){var c=Object(n["r"])("LoginContainer");return Object(n["o"])(),Object(n["e"])("div",s,[Object(n["h"])(c)])}var a={class:"login container"},r={class:"overlay"},c={class:"overlay-content"},l={class:"login-container"},u={class:"outer-card1",cage:"outer-header",style:{"text-align":"right"}};function d(e,t,i,s,o,d){var h=Object(n["r"])("LanguageSelector"),p=Object(n["r"])("FirstState"),g=Object(n["r"])("SecondState"),m=Object(n["r"])("RedirectionState"),b=Object(n["r"])("Footer");return Object(n["o"])(),Object(n["e"])("div",a,[Object(n["w"])(Object(n["h"])("div",r,[Object(n["h"])("div",c,[Object(n["h"])("img",{id:"loader",style:{width:"50px"},src:e.cdnDomain+"images/wiom/loading.svg",alt:"Loading..."},null,8,["src"])])],512),[[n["u"],e.loader]]),Object(n["h"])(h),Object(n["h"])("div",l,[Object(n["h"])("div",u,[e.logo?(Object(n["o"])(),Object(n["e"])("img",{key:0,id:"outer-card-img",onerror:"this.src=window.loginUser.cdnDomain+'images/wiom/wiom_logo.png'",class:"pixelated",src:e.logo,style:{width:"100px",height:"auto"}},null,8,["src"])):Object(n["f"])("",!0)]),"firstState"==e.state?(Object(n["o"])(),Object(n["e"])(p,{key:"firstState::"+e.loginUserSession,onGotMobile:t[1]||(t[1]=function(e){return d.setMobile(e)}),onGenerateOtp:t[2]||(t[2]=function(e){return d.generateOTP(e)}),onSwapState:t[3]||(t[3]=function(e){return d.swapState(e)}),i2e1Data:e.i2e1Data,onShowLoader:t[4]||(t[4]=function(t){return e.loader=t})},null,8,["i2e1Data"])):Object(n["f"])("",!0),"secondState"==e.state?(Object(n["o"])(),Object(n["e"])(g,{key:"secondState::"+e.mobile,onSwapState:t[5]||(t[5]=function(e){return d.swapState(e)}),onResendOtp:t[6]||(t[6]=function(e){return d.generateOTP(e,!0)}),onSessionAttributes:t[7]||(t[7]=function(t){return e.sessionAttributes=t}),i2e1Data:e.i2e1Data,onShowLoader:t[8]||(t[8]=function(t){return e.loader=t})},null,8,["i2e1Data"])):Object(n["f"])("",!0),"redirectionState"==e.state?(Object(n["o"])(),Object(n["e"])(m,{key:"redirectionState::"+e.mobile,onSwapState:t[9]||(t[9]=function(e){return d.swapState(e)}),i2e1Data:e.i2e1Data,sessionAttributes:e.sessionAttributes},null,8,["i2e1Data","sessionAttributes"])):Object(n["f"])("",!0)]),Object(n["h"])(b,{state:e.state},null,8,["state"])])}i("c975"),i("4d63"),i("25f0"),i("5319"),i("841c");var h={setCookie:function(e,t){var i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:1,n=new Date;n.setTime(n.getTime()+24*i*60*60*1e3);var s="expires="+n.toUTCString();document.cookie=e+"="+t+";"+s+";path=/"},getCookie:function(e){for(var t=e+"=",i=document.cookie.split(";"),n=0;n<i.length;n++){var s=i[n];while(" "==s.charAt(0))s=s.substring(1);if(0==s.indexOf(t))return s.substring(t.length,s.length)}return""},deleteCookie:function(e){document.cookie=e+"=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;"},reload:function(){location.reload()},getQueryStringValue:function(e){return decodeURIComponent(window.location.search.replace(new RegExp("^(?:.*[&\\?]"+encodeURIComponent(e).replace(/[.+*]/g,"\\$&")+"(?:\\=([^&]*))?)?.*$","i"),"$1"))},shuffle:function(e){var t,i,n=e.length;while(0!==n)i=Math.floor(Math.random()*n),n-=1,t=e[n],e[n]=e[i],e[i]=t;return e},roundOffData:function(e){return Math.floor(100*e)/100},roundOffTime:function(e){if(e>0){var t=parseInt(e/86400),i=parseInt(e/3600)%24,n=parseInt(e/60)%60,s=(t>=1?t+(1===t?" day ":" days "):"")+(i>=1?i+(1===i?" hr ":" hrs "):"");return t<1&&(s+=n>=1?n+(1===n?" min":" mins"):""),s||"less than a minute"}return"None"}},p=i("bc3a"),g=i.n(p),m="https://i2e1.in";window.cdnDomain.indexOf("localhost")>-1?m="https://localhost:44300":window.cdnDomain.indexOf("testease")>-1&&(m="https://testease.i2e1.in");var b=g.a.create({baseURL:m});b.interceptors.response.use((function(e){return e}),(function(e){if(!e.response||!e.response.status||403!==e.response.status)return Promise.reject(e);window.location("/")}));var f=b,_={class:"language-selectors"};function O(e,t,i,s,o,a){return Object(n["o"])(),Object(n["e"])("div",_,[Object(n["w"])(Object(n["h"])("a",{onClick:t[1]||(t[1]=function(e){return a.changeLanguage("en")}),id:"en"},"English",512),[[n["u"],a.showOption("en")]]),Object(n["w"])(Object(n["h"])("a",{onClick:t[2]||(t[2]=function(e){return a.changeLanguage("hi")}),id:"hi"},"हिंदी",512),[[n["u"],a.showOption("hi")]]),Object(n["w"])(Object(n["h"])("a",{onClick:t[3]||(t[3]=function(e){return a.changeLanguage("te")}),id:"te"},"తెలుగు",512),[[n["u"],a.showOption("te")]])])}var w={name:"LanguageSelector",props:[],emits:["current-language"],data:function(){return{currentLanguage:null}},mounted:function(){this.currentLanguage=this.$i18n.locale},methods:{showOption:function(e){return this.currentLanguage!=e},changeLanguage:function(e){this.currentLanguage=e,this.$emit("current-language",e),this.$i18n.locale=e}}};w.render=O;var v=w,j={class:"form first-state state-transition"},y={class:"barking"},S={class:"loud"},T={class:"less-loud"},k={cage:"generate-otp"},C={class:"tnc-area"};function Q(e,t,i,s,o,a){var r=Object(n["r"])("PhoneInput"),c=Object(n["r"])("Survey");return Object(n["o"])(),Object(n["e"])("form",j,[Object(n["h"])("div",y,[Object(n["h"])("div",S,Object(n["s"])(e.$t("firstState.bark_loud")),1),Object(n["h"])("div",T,Object(n["s"])(e.$t("firstState.bark_lessloud")),1)]),Object(n["h"])(r,{onValid:t[1]||(t[1]=function(e){return a.setValid(e)}),onInvalid:t[2]||(t[2]=function(e){return a.setInvalid()})}),Object(n["h"])(c,{questionType:e.questionType,state:e.state},null,8,["questionType","state"]),Object(n["h"])("div",k,[Object(n["h"])("input",{type:"button",id:"get_otp",ref:"get_otp",name:"enter",class:"primary login_button",value:e.$t("firstState.generate_otp"),onClick:t[3]||(t[3]=function(t){e.disableCTA=!0,e.$emit("generate-otp",e.mobile)}),disabled:!this.validUsername&&this.disableCTA},null,8,["value","disabled"])]),Object(n["h"])("div",C,[Object(n["h"])("span",{class:"tnc1",cage:"tnc",innerHTML:e.$t("firstState.tnc")},null,8,["innerHTML"])])])}var L={class:"username-area phone-number"},A={class:"title"},D={class:"group"},x={class:"error-message display-none",id:"fdm-notauthorised-error"},P=Object(n["h"])("div",{class:"group username_prefix"},[Object(n["h"])("input",{id:"username_prefix",type:"tel",value:"+91",disabled:""})],-1);function $(e,t,i,s,o,a){return Object(n["o"])(),Object(n["e"])("div",L,[Object(n["h"])("div",A,Object(n["s"])(e.$t("helpState.enter_mobile_number")),1),Object(n["h"])("div",D,[Object(n["w"])(Object(n["h"])("input",{type:"tel",id:"username",name:"username","onUpdate:modelValue":t[1]||(t[1]=function(t){return e.mobile=t}),onChange:t[2]||(t[2]=function(e){return a.validate()}),maxlength:"10",max:"9999999999",required:"required",pattern:".*\\S.*"},null,544),[[n["t"],e.mobile]]),Object(n["h"])("label",{for:"username",name:"username_for",class:a.textAlign},Object(n["s"])(e.$t("firstState.enter_phone_number")),3)]),Object(n["h"])("div",{class:"error-message display-none",id:"username-error",innerHTML:e.$t("firstState.mobile_invalid")},null,8,["innerHTML"]),Object(n["w"])(Object(n["h"])("div",{class:"error-message display-none",id:"username-error"},Object(n["s"])(e.$t("firstState.mobile_invalid")),513),[[n["u"],a.validate()]]),Object(n["h"])("div",x,Object(n["s"])(e.$t("firstState.fdm_notauthorised")),1),P])}var q={name:"PhoneInput",data:function(){return{mobile:null,valid:null}},emits:["valid","invalid"],methods:{validate:function(){return this.mobile&&10==this.mobile.length&&parseInt(this.mobile)<=9999999999&&parseInt(this.mobile)>=3333333333?(this.$emit("valid",this.mobile),!0):(this.$emit("invalid"),!1)}},watch:{mobile:function(){this.validate()}},computed:{textAlign:function(){return{"bump-up":"en"!=this.$i18n.locale}}}};q.render=$;var B=q,I=Object(n["h"])("div",{class:"question-area type-0 phone-number-flow"},null,-1),U=Object(n["h"])("div",{class:"question-area type-5 phone-number-flow"},null,-1);function M(e,t,i,s,o,a){var r=Object(n["r"])("PreSurvey");return Object(n["o"])(),Object(n["e"])(n["a"],null,["firstState"!=i.state?(Object(n["o"])(),Object(n["e"])(r,{key:0,i2e1Data:i.i2e1Data},null,8,["i2e1Data"])):Object(n["f"])("",!0),I,U],64)}var E={class:"questions-confirm-area"},N=Object(n["h"])("span",{class:"tick"},[Object(n["h"])("img",{src:"images/wiom/done.svg"})],-1),F={class:"questions-confirm-cta"};function H(e,t,i,s,o,a){return Object(n["o"])(),Object(n["e"])("div",E,[N,Object(n["h"])("div",{class:"loud",innerHTML:e.$t("secondState.questions_loud",{data:i.i2e1Data.viewBag.dataLeft})},null,8,["innerHTML"]),Object(n["h"])("div",{class:"less-loud",innerHTML:e.$t("secondState.questions_lessloud")},null,8,["innerHTML"]),Object(n["h"])("div",F,[Object(n["h"])("input",{type:"button",class:"primary login_button",onclick:"_answerQuestions()",value:e.$t("secondState.questions_cta_okay")},null,8,["value"])])])}var G={name:"PreSurvey",props:["i2e1Data"]};G.render=H;var R=G,V={name:"Survey",props:["questionType","state","i2e1Data"],components:{PreSurvey:R}};V.render=M;var W=V,J=i("499d"),z=i.n(J),Y={name:"FirstState",components:{PhoneInput:B,Survey:W},props:["i2e1Data"],emits:["swap-state","got-mobile","generate-otp"],data:function(){return{questionType:[3],i2e1Ques:[],validUsername:null,mobile:null,state:"firstState",disableCTA:!1}},methods:{setValid:function(e){this.validUsername=!0,this.mobile=e,this.disableCTA=!1,this.$emit("got-mobile",this.mobile)},setInvalid:function(){this.validUsername=!1,this.mobile=null},validateUsername:function(){return this.validUsername}},mounted:function(){console.log(window.getComputedStyle(this.$refs.get_otp)),console.log(this.$refs.get_otp.style)}};Y.render=Q;var K=Y,Z={class:"form second-state state-transition",onsubmit:"return false;"},X={class:"button-group"},ee={class:"welcome-back"},te={class:"namaste"},ie={class:"barking"},ne={class:"loud"},se={class:"less-loud"},oe={id:"captcha-holder"},ae=Object(n["h"])("br",null,null,-1),re=Object(n["h"])("img",{id:"captcha-img",title:"captcha",src:""},null,-1),ce={style:{opacity:"0"}},le=Object(n["h"])("input",{id:"captcha",placeholder:"Enter Captcha",type:"text"},null,-1),ue={class:"questions-confirm-area"},de={class:"tick"},he={class:"less-loud"},pe={class:"questions-confirm-cta"},ge={key:0},me={key:0,class:"answerType-0"},be=Object(n["h"])("div",{class:"material-input question text-question"},[Object(n["h"])("div",{class:"group"},[Object(n["h"])("input",{type:"text",required:"required",pattern:".*\\S.*"}),Object(n["h"])("label")])],-1),fe={key:1},_e={class:"question-text"},Oe=Object(n["h"])("span",{class:"required"},"*",-1),we={class:"options-list"},ve={for:"{{ 'option-' + option.id }}"},je={cage:"connect-button"},ye={class:"bottom-button-group"},Se={id:"primary-resend-area",class:"resend-otp-area"};function Te(e,t,i,s,o,a){var r=Object(n["r"])("OTPInput");return Object(n["o"])(),Object(n["e"])("form",Z,[Object(n["h"])("div",X,[Object(n["w"])(Object(n["h"])("div",{class:"back-area",onClick:t[1]||(t[1]=function(t){return e.$emit("swap-state","firstState")})},[Object(n["h"])("img",{title:e.$t("secondState.back"),src:i.i2e1Data.loginUser.cdnDomain+"images/back.png"},null,8,["title","src"])],512),[[n["u"],e.showBack]])]),Object(n["w"])(Object(n["h"])("div",ee,[Object(n["h"])("div",te,[Object(n["h"])("img",{src:i.i2e1Data.loginUser.cdnDomain+"images/wiom/namaste.svg",style:{width:"80px"}},null,8,["src"])]),Object(n["h"])("div",{class:"greetings",innerHTML:e.$t("secondState.welcomeback_greetings",{prefix:i.i2e1Data.loginUser.cdnDomain})},null,8,["innerHTML"])],512),[[n["u"],e.welcomeBack]]),Object(n["w"])(Object(n["h"])("div",ie,[Object(n["h"])("div",ne,Object(n["s"])(e.$t("secondState.bark_loud")),1),Object(n["h"])("div",se,Object(n["s"])(e.$t("secondState.bark_lessloud")),1)],512),[[n["u"],e.showBarking]]),Object(n["w"])(Object(n["h"])(r,{onValid:t[2]||(t[2]=function(e){return a.ValidOTP(e)}),onInvalid:t[3]||(t[3]=function(e){return a.InvalidOTP()})},null,512),[[n["u"],e.showOTPArea]]),Object(n["w"])(Object(n["h"])("div",{class:"error-message",id:"otp-error"},Object(n["s"])(e.$t("secondState.otp_invalid")),513),[[n["u"],e.invalidOTP]]),Object(n["w"])(Object(n["h"])("div",oe,[Object(n["h"])("div",null,[Object(n["h"])("span",null,Object(n["s"])(e.$t("secondState.captcha")),1),ae,re]),Object(n["h"])("div",null,[Object(n["h"])("span",ce,Object(n["s"])(e.$t("secondState.enter_captcha")),1),le])],512),[[n["u"],e.showCaptcha]]),Object(n["w"])(Object(n["h"])("div",ue,[Object(n["w"])(Object(n["h"])("span",de,[Object(n["h"])("img",{src:i.i2e1Data.loginUser.cdnDomain+"images/wiom/done.svg"},null,8,["src"])],512),[[n["u"],e.showSurveyConfirmation&&!e.welcomeBack]]),Object(n["h"])("div",{class:"loud",innerHTML:e.$t("secondState.questions_loud",{data:e.dataLeft})},null,8,["innerHTML"]),Object(n["h"])("div",he,Object(n["s"])(e.$t("secondState.questions_lessloud")),1),Object(n["h"])("div",pe,[Object(n["h"])("input",{type:"button",class:"primary login_button",value:e.$t("secondState.questions_cta_okay"),onClick:t[4]||(t[4]=function(e){return a.answerQuestions()})},null,8,["value"])])],512),[[n["u"],e.showSurveyConfirmation]]),e.i2e1Ques.length>0&&e.startedAnswering?(Object(n["o"])(),Object(n["e"])("div",ge,[(Object(n["o"])(!0),Object(n["e"])(n["a"],null,Object(n["q"])(e.i2e1Ques,(function(e){return Object(n["o"])(),Object(n["e"])("div",{class:["question-area phone-number-flow",{"type-0":0===e.quesType,"type-5":5===e.quesType,"type-3":3===e.quesType}],key:e.id+"::"+e.journey,question:e},[[0,4,5].indexOf(e.answerType)>-1?(Object(n["o"])(),Object(n["e"])("div",me,[be])):Object(n["f"])("",!0),[1,2].indexOf(e.answerType)>-1?(Object(n["o"])(),Object(n["e"])("div",fe,[Object(n["w"])(Object(n["h"])("div",{class:["question select","question"+e.id+"_"+e.journey]},[Object(n["h"])("div",_e,[Object(n["g"])(Object(n["s"])(e.question.quesText),1),Oe]),Object(n["h"])("div",we,[(Object(n["o"])(!0),Object(n["e"])(n["a"],null,Object(n["q"])(e.options,(function(t){return Object(n["w"])((Object(n["o"])(),Object(n["e"])("p",{key:e.id+"::"+e.journey+"::"+t.id,class:"{{ 'answer_'+option.id+'_'+currentQuestionIndex }}",onClick:function(i){return a.mark(e.index,e.id,t.id,e.answerType)}},[Object(n["h"])("input",{class:"",type:"checkbox",checked:t.id==e.answer,onClick:function(i){return a.mark(e.index,e.id,t.id,e.answerType)}},null,8,["checked","onClick"]),Object(n["h"])("label",ve,[t.image?(Object(n["o"])(),Object(n["e"])("img",{key:0,class:"content",src:i.i2e1Data.loginUser.cdnDomain+t.image},null,8,["src"])):Object(n["f"])("",!0),Object(n["g"])(Object(n["s"])(t.text),1)])],8,["onClick"])),[[n["u"],!t.hide]])})),128))]),Object(n["w"])(Object(n["h"])("input",{class:"next-question",id:"done_' + question.id + '_' + question.journey + '",type:"button",onClick:function(t){return a.done(e.index,e.id,e.journey)},value:"Next"},null,8,["onClick"]),[[n["u"],e.showNext]])],2),[[n["u"],!e.hide]])])):Object(n["f"])("",!0)],10,["question"])})),128))])):Object(n["f"])("",!0),Object(n["h"])("div",je,[Object(n["w"])(Object(n["h"])("input",{type:"button",id:"confirm",ref:"confirm",class:"primary login_button",value:e.$t("secondState.confirm"),onClick:t[5]||(t[5]=function(e){return a.confirm(!1)})},null,8,["value"]),[[n["u"],e.showConfirmCTA]]),Object(n["w"])(Object(n["h"])("input",{type:"button",id:"connect",ref:"connect",class:"primary login_button",value:e.$t("secondState.connect"),onClick:t[6]||(t[6]=function(e){return a.connect(!1)})},null,8,["value"]),[[n["u"],e.showConnectCTA]])]),Object(n["w"])(Object(n["h"])("div",ye,[Object(n["h"])("div",{class:"back-area",onClick:t[7]||(t[7]=function(t){return e.$emit("swap-state","firstState")})},[Object(n["h"])("span",null,Object(n["s"])(e.$t("secondState.change")),1)]),Object(n["h"])("div",Se,[Object(n["h"])("input",{type:"button",id:"resend-otp",class:"small_button primary",value:e.$t("secondState.resend"),onClick:t[8]||(t[8]=function(t){return e.$emit("resend-otp",a.getMobile())})},null,8,["value"])])],512),[[n["u"],e.showBottomButtons]])])}i("4de4"),i("a434"),i("498a");var ke={id:"primary-otp-area",class:"otp-area"},Ce={class:"otp-title"},Qe={class:"group",id:"otp-group"};function Le(e,t,i,s,o,a){return Object(n["o"])(),Object(n["e"])("div",ke,[Object(n["h"])("div",Ce,Object(n["s"])(e.$t("secondState.enter_4_digit_password")),1),Object(n["h"])("div",Qe,[Object(n["w"])(Object(n["h"])("input",{type:"number",min:"0",max:"9",ref:"digit1",maxlength:"1","onUpdate:modelValue":t[1]||(t[1]=function(t){return e.digit1=t})},null,512),[[n["t"],e.digit1]]),Object(n["w"])(Object(n["h"])("input",{type:"number",min:"0",max:"9",ref:"digit2",maxlength:"1","onUpdate:modelValue":t[2]||(t[2]=function(t){return e.digit2=t})},null,512),[[n["t"],e.digit2]]),Object(n["w"])(Object(n["h"])("input",{type:"number",min:"0",max:"9",ref:"digit3",maxlength:"1","onUpdate:modelValue":t[3]||(t[3]=function(t){return e.digit3=t})},null,512),[[n["t"],e.digit3]]),Object(n["w"])(Object(n["h"])("input",{type:"number",min:"0",max:"9",ref:"digit4",maxlength:"1","onUpdate:modelValue":t[4]||(t[4]=function(t){return e.digit4=t})},null,512),[[n["t"],e.digit4]])])])}var Ae={name:"OTPInput",emits:["valid","invalid"],data:function(){return{valid:["0","1","2","3","4","5","6","7","8","9"],digit1:"",digit2:"",digit3:"",digit4:"",otp:""}},mounted:function(){this.$refs.digit1.focus()},methods:{validate:function(){this.otp=this.digit1+this.digit2+this.digit3+this.digit4,4==this.otp.length&&parseInt(this.otp)>999&&parseInt(this.otp)<1e4?this.$emit("valid",this.otp):this.$emit("invalid")}},watch:{digit1:function(){this.digit1.length>1&&(this.digit1=this.digit1.substring(0,1)),1==this.digit1.length&&(this.valid.indexOf(this.digit1)<0?this.digit1="":this.$refs.digit2.select()),this.validate()},digit2:function(){this.digit2.length>1&&(this.digit2=this.digit2.substring(0,1)),1==this.digit2.length&&(this.valid.indexOf(this.digit2)<0?this.digit2="":this.$refs.digit3.select()),0==this.digit2.length&&this.$refs.digit1.select(),this.validate()},digit3:function(){this.digit3.length>1&&(this.digit3=this.digit3.substring(0,1)),1==this.digit3.length&&(this.valid.indexOf(this.digit3)<0?this.digit3="":this.$refs.digit4.select()),0==this.digit3.length&&this.$refs.digit2.select(),this.validate()},digit4:function(){this.digit4.length>1&&(this.digit4=this.digit4.substring(0,1)),1==this.digit4.length&&this.valid.indexOf(this.digit4)<0&&(this.digit4=""),0==this.digit4.length&&this.$refs.digit3.select(),this.validate()}}};Ae.render=Le;var De=Ae,xe={components:{OTPInput:De},props:["i2e1Data"],emits:["swap-state","resend-otp","session-attributes","show-loader"],mounted:function(){this.onMount()},data:function(){return{questionTypes:[0,5],nasid:0,OTP:null,showOTPArea:!0,loginUserSession:null,showSurveyConfirmation:!1,showCaptcha:!1,showConfirmCTA:!1,showConnectCTA:!1,invalidOTP:!1,surveyStarted:!1,welcomeBack:!1,showBottomButtons:!0,showQuestions:!1,i2e1Ques:[],startedAnswering:!1,showBack:!0,showBarking:!0,i2e1FirstAnsweredAt:[],landingPage:null,dataPolicy:"",dataLeft:"",timeLeft:""}},methods:{onMount:function(){this.i2e1Data.loginUser.tempmobile&&(this.i2e1Data.viewBag.welcomeBack||"true"==this.i2e1Data.viewBag.welcomeBack)&&this.WelcomeBack(),this.getQuestions(),this.getNasAttributes(),this.$emit("show-loader",!1),console.log(window.getComputedStyle(this.$refs.connect)),console.log(window.getComputedStyle(this.$refs.confirm))},WelcomeBack:function(){this.showOTPArea=!1,this.showBottomButtons=!1,this.showBack=!1,this.showBarking=!1,this.welcomeBack=!0,this.i2e1Ques.length>0&&(this.showSurveyConfirmation=!0,this.showConnectCTA=!1,this.showConfirmCTA=!1,this.showOTPArea=!1,setTimeout((function(){this.answerQuestions()}),4e3))},ValidOTP:function(e){this.invalidOTP=!1,this.OTP=e},InvalidOTP:function(){this.OTP=null},connect:function(){this.$emit("show-loader",!0);var e=this,t=function(t){e.$emit("show-loader",!1)},i=function(t){e.$emit("show-loader",!1),e.preLogin(t.data.data.landingPage)},n=z.a.getMobile(this);this.OTP;this.i2e1Data.viewBag.welcomeBack&&(this.mobile="3333333333",this.OTP="1234");var s={},o={};this.processAnswers(0)||this.processAnswers(5)||f.post("Login/SubmitOTP?login-user-session="+h.getCookie("login_user_session"),{mobile:n,accessCode:s.accessCode,macId:o.mac,routerId:o.nasid,challenge:o.challenge,otp:this.OTP,name:s.name,clientAuthType:s.clientAuthType||"PHONE",captcha:s.captcha,questions:this.i2e1Ques,doLogin:null===s.doLogin||s.doLogin}).then((function(e){i(e)})).catch((function(e){t(e)}))},confirm:function(){this.$emit("show-loader",!0);var e=this,t=function(){e.showSurveyConfirmation=!0,e.showOTPArea=!1,e.showConfirmCTA=!1,e.showConnectCTA=!1,e.showBarking=!1,e.showBottomButtons=!1,setTimeout((function(){e.answerQuestions()}),4e3),e.$emit("show-loader",!1)},i=function(){var t=arguments.length>0&&void 0!==arguments[0]&&arguments[0];e.showSurveyConfirmation=!1,e.showOTPArea=!0,t&&(e.invalidOTP=!0,setTimeout((function(){e.invalidOTP=!1}),4e3)),e.$emit("show-loader",!1)};this.OTP||console.catch("Missing OTP while Checking OTP"),f.post("Login/CheckOTP?login-user-session="+h.getCookie("login_user_session"),{mobile:z.a.getMobile(this),otp:this.OTP,name:"",clientAuthType:"PHONE",captcha:""}).then((function(e){if(e.data&&0==e.data.status)t();else{var n=!0;i(n)}})).catch((function(e){i()}))},getQuestions:function(){var e=this,t=this;this.loginUserSession=h.getCookie("login_user_session");var i={questionTypes:this.questionTypes,mobile:z.a.getMobile(this).toString(),routerId:this.i2e1Data.loginUser.nasid};f.post("Login/GetQuestions?login-user-session="+this.loginUserSession,i).then((function(i){var n=[];if(n=i.data.data,n.length>0&&n[0])if(e.showQuestions=!1,e.showConfirmCTA=!0,e.showConnectCTA=!1,0===e.questionTypes.indexOf(3))for(var s=0;s<n.length;s++){if(n[s].journey=0,2===n[s].answerType&&n[s].randomized&&(n[s].options=h.shuffle(n[s].options)),2===n[s].answerType&&n[s].binarySplit&&(n[s].journey=n[s].options.length-2,n[s].options.length>2))for(var o=2;o<=n[s].options.length;o++){var a=n[s];a.journey=a.options.length-o,n=n.splice(s+1,0,a)}e.i2e1Ques.push(n[s]),e.prepareQuestion(n[s],s,n.length)}else n.length?!e.i2e1Data.loginUser.tempmobile||!0!==e.i2e1Data.viewBag.welcomeBack&&"true"!==e.i2e1Data.viewBag.welcomeBack?e.showConfirmCTA=!0:(e.showSurveyConfirmation=!0,e.showOTPArea=!1,e.showConfirmCTA=!1,e.showConnectCTA=!1,setTimeout((function(){t.answerQuestions()}),4e3)):(e.showConnectCTA=!0,!e.i2e1Data.loginUser.tempmobile||!0!==e.i2e1Data.viewBag.welcomeBack&&"true"!==e.i2e1Data.viewBag.welcomeBack||e.connect(!1)),e.questionTypes.forEach((function(e){for(var i=n.filter((function(t){return t&&t.quesType===e})),s=0;s<i.length;s++){s===i.length-1&&!0,i[s].journey=0,i[s].randomized&&(i[s].options=h.shuffle(i[s].options)),i[s].journey=0;var o=i[s].options.length;if(i[s].binarySplit)if(i[s].journey=o-2,i[s].options.length>2)for(var a=2;a<=o;a++)i[s].journey=o-a,t.i2e1Ques.push({hide:!0,id:i[s].id,templateId:t.i2e1Data.loginUser.templateid[0]||0,journey:i[s].journey,options:i[s].options,remainingOptions:[],answer:null,optionsOrder:[],randomized:i[s].randomized,binarySplit:i[s].binarySplit,answerType:i[s].answerType,question:i[s],index:t.i2e1Ques.length,totalJourneys:o-1,showNext:!1});else t.i2e1Ques.push({hide:!0,id:i[s].id,templateId:t.i2e1Data.loginUser.templateid[0]||0,journey:i[s].journey,options:i[s].options,remainingOptions:[],answer:null,optionsOrder:[],randomized:i[s].randomized,binarySplit:i[s].binarySplit,quesType:i[s].quesType,answerType:i[s].answerType,question:i[s],index:t.i2e1Ques.length,totalJourneys:1,showNext:!1});else t.i2e1Ques.push({hide:!0,id:i[s].id,templateId:t.i2e1Data.loginUser.templateid[0]||0,journey:i[s].journey,options:i[s].options,remainingOptions:[],answer:null,optionsOrder:[],randomized:i[s].randomized,binarySplit:i[s].binarySplit,quesType:i[s].quesType,answerType:i[s].answerType,question:i[s],index:t.i2e1Ques.length,totalJourneys:1,showNext:!1})}for(var r=0;r<t.i2e1Ques.length;r++)t.prepareQuestion(t.i2e1Ques[r].question,r,t.i2e1Ques[r].totalJourneys)}));else e.showConfirmCTA=!1,e.showConnectCTA=!0})).catch((function(t){e.showConfirmCTA=!1,e.showConnectCTA=!0}))},answerQuestions:function(){var e=this;0==this.startedAnswering&&(this.startedAnswering=!0,this.showSurveyConfirmation=!1,this.showBarking=!1,this.showBottomButtons=!1,this.i2e1Ques[0].hide=!1,this.i2e1Ques&&this.i2e1Ques.length<2&&(this.showConnectCTA=!0),this.i2e1Ques&&this.i2e1Ques.length&&(this.i2e1Ques[0].displayTime=Date.now(),this.i2e1Ques[0].optionsOrder=[],this.i2e1Ques[0].options.forEach((function(t,i){e.i2e1Ques[0].binarySplit&&i>=2?(e.i2e1Ques[0].options[i].hide=!0,e.i2e1Ques[0].remainingOptions.push(t.id)):e.i2e1Ques[0].optionsOrder.push(t.id)})),this.showBack=!1,1==this.i2e1Ques.length&&(this.i2e1Ques[0].hide=!0)),!this.i2e1Data.loginUser.mobile||!0!==this.i2e1Data.viewBag.welcomeBack&&"true"!==this.i2e1Data.viewBag.welcomeBack||(this.showWelcomeBack=!1),this.i2e1Ques[0].hide=!1)},mark:function(e,t,i,n){void 0===this.i2e1Ques[e].selectionTime&&(this.i2e1Ques[e].selectionTime=Date.now()),1==parseInt(n)&&(this.i2e1Ques[e].answer=i)},done:function(e,t,i){var n=this,s=e+1,o=0;s!=this.i2e1Ques.length&&null!==this.i2e1Ques[e].answer&&void 0!==this.i2e1Ques[e].answer&&(this.i2e1Ques[s].binarySplit?this.i2e1Ques[e].id===this.i2e1Ques[s].id?(this.i2e1Ques[s].options.forEach((function(e,t){n.i2e1Ques[s].options[t].hide=!0})),this.i2e1Ques[e].remainingOptions.push(this.i2e1Ques[e].answer),this.i2e1Ques[s].optionsOrder=[],this.i2e1Ques[s].options.forEach((function(t,i){o<2&&n.i2e1Ques[e].remainingOptions.indexOf(t.id)>-1?(n.i2e1Ques[s].options[i].hide=!1,n.i2e1Ques[s].optionsOrder.push(t.id),o+=1):n.i2e1Ques[s].remainingOptions.push(t.id)}))):(this.i2e1Ques[s].optionsOrder=[],this.i2e1Ques[s].options.forEach((function(e,t){o<2?(n.i2e1Ques[s].options[t].hide=!1,n.i2e1Ques[s].optionsOrder.push(e.id),o+=1):n.i2e1Ques[s].remainingOptions.push(e.id)}))):(this.i2e1Ques[s].remainingOptions=[],this.i2e1Ques[s].optionsOrder=[],this.i2e1Ques[s].options.forEach((function(e,t){n.i2e1Ques[s].optionsOrder.push(e.id)}))),this.i2e1Ques[e].hide=!0,this.i2e1Ques[s].hide=!1,s>=this.i2e1Ques.length-1&&(this.showConnectCTA=!0),this.i2e1Ques[s].displayTime=Date.now())},prepareQuestion:function(e,t,i){switch(e.answerType){case 0:case 4:case 5:break;case 1:case 2:if(e.options.forEach((function(t,i){e.options[i].displayIndex=i+1})),e.journey>0)this.i2e1Ques[t].showNext=!0;else{var n=t+1;n<this.i2e1Ques.length&&(this.i2e1Ques[t].showNext=!0)}break;case 3:break}},processAnswers:function(e){var t=!1;if(this.i2e1Ques.length){for(var i=0;i<this.i2e1Ques.length;i++){switch(this.i2e1Ques[i].answerType){case 1:this.i2e1Ques[i].answer&&0!=this.i2e1Ques[i].answer?this.i2e1Ques[i].optionsOrder?this.i2e1Ques[i].displayIndex=this.i2e1Ques[i].optionsOrder.indexOf(this.i2e1Ques[i].answer)+1:this.i2e1Ques[i].displayIndex=0:t=!0;break;case 0:case 4:case 5:this.i2e1Ques[i].answer&&0!=this.i2e1Ques[i].answer||(t=!0),this.i2e1Ques[i].optionsOrder?this.i2e1Ques[i].displayIndex=this.i2e1Ques[i].optionsOrder.indexOf(this.i2e1Ques[i].answer)+1:this.i2e1Ques[i].displayIndex=0,"email"===this.i2e1Ques[i].questionKey.toLowerCase()&&(t=!1),this.i2e1Ques[i].answer=this.i2e1Ques[i].answer.trim();break;case 3:this.i2e1Ques[i].optionsOrder?this.i2e1Ques[i].displayIndex=this.i2e1Ques[i].optionsOrder.indexOf(this.i2e1Ques[i].answer)+1:this.i2e1Ques[i].displayIndex=0;break;case 2:for(var n=0;n<this.i2e1Ques[i].options.length;n++)this.i2e1Ques[i].selectionTime=this.i2e1FirstAnsweredAt[this.i2e1Ques[i].id],this.i2e1Ques[i].options[n].isSelected=!0,t=!1;break}if(t)return t}return t}},preLogin:function(e,t){window.landingPage=e,this.$emit("swap-state","redirectionState")},getMobile:function(){return z.a.getMobile(this)},getNasAttributes:function(){var e=this;this.getMobile()&&this.getMobile().length>0&&f.post("/Login/GetNasAttributes?login-user-session="+h.getCookie("login_user_session")).then((function(t){if(t.data.data){var i=t.data.data;if(i.vip)e.dataLeft="",e.timeLeft="";else{var n=i.dataLeft&&i.dataLeft>=0?i.dataLeft:0,s=i.timeLeft;i.dataLeft>=0&&n>=0?(n>=1073741824?n=h.roundOffData(n/1073741824)+" GB":n>=1048576?n=h.roundOffData(n/1048576)+" MB":n>=1024?n=h.roundOffData(n/1024)+" KB":0===n&&(n=""),e.dataLeft=n):e.dataLeft="",s&&s>=0&&(s=h.roundOffTime(s),e.timeLeft=0==s?"":s)}e.$emit("session-attributes",{dataLeft:e.dataLeft,timeLeft:e.timeLeft})}})).catch((function(t){e.dataLeft="",e.timeLeft=""}))}}};xe.render=Te;var Pe=xe,$e={class:"form redirecting-state state-transition"},qe={class:"no-offers"},Be={id:"greeting"},Ie=Object(n["h"])("hr",null,null,-1),Ue={class:"connected"},Me={key:0,class:"offers"},Ee=Object(n["h"])("div",{class:"campaigns"},null,-1);function Ne(e,t,i,s,o,a){return Object(n["o"])(),Object(n["e"])("form",$e,[Object(n["h"])("div",qe,[Object(n["h"])("div",Be,[Object(n["h"])("img",{src:i.i2e1Data.loginUser.cdnDomain+"images/wiom/welldone.png",style:{"vertical-align":"text-bottom",height:"30px"}},null,8,["src"]),Object(n["h"])("span",null,Object(n["s"])(e.$t("redirectionState.please_wait")),1)]),Object(n["h"])("div",{class:"datamessage",innerHTML:e.$t("redirectionState.data_policy",{data:i.sessionAttributes.dataLeft})},null,8,["innerHTML"]),Object(n["w"])(Object(n["h"])("div",{class:"timemessage",innerHTML:e.$t("redirectionState.time_policy",{time:i.sessionAttributes.timeLeft})},null,8,["innerHTML"]),[[n["u"],i.sessionAttributes.timeLeft.length>0]]),Object(n["w"])(Object(n["h"])("div",{class:"timemessage",innerHTML:e.$t("redirectionState.time_policy",{time:"NA"})},null,8,["innerHTML"]),[[n["u"],0==i.sessionAttributes.timeLeft.length]]),Ie,Object(n["h"])("div",Ue,[Object(n["h"])("div",null,Object(n["s"])(e.$t("redirectionState.you_are_almost_there")),1)])]),i.options&&i.options.offers&&i.options.offers.length?(Object(n["o"])(),Object(n["e"])("div",Me,[Ee,Object(n["h"])("input",{id:"skipVoucher2",class:"skip-voucher small_button primary",type:"button",value:"Skip",onClick:t[1]||(t[1]=function(e){return a.skip()})})])):Object(n["f"])("",!0)])}var Fe={name:"RedirectionState",emits:["swap-state","got-mobile"],props:["i2e1Data","options","sessionAttributes"],mounted:function(){this.onMount()},data:function(){return{questionType:[3],i2e1Ques:[],validUsername:null,mobile:null,state:"firstState",disableCTA:!1}},methods:{onMount:function(){this.options&&this.options.offers&&this.options.offers.length||z.a.doLogin()},skip:function(){z.a.doLogin()}}};Fe.render=Ne;var He=Fe,Ge=i("ade3"),Re={class:"footer"},Ve=Object(n["h"])("div",{class:"top-border"},null,-1),We={class:"stepper"},Je=Object(n["h"])("div",{class:"connector"},[Object(n["h"])("div")],-1),ze=Object(n["h"])("div",{class:"connector"},[Object(n["h"])("div")],-1);function Ye(e,t,i,s,o,a){return Object(n["o"])(),Object(n["e"])("div",Re,[Ve,Object(n["h"])("span",{innerHTML:e.$t("footer.facing_issues_call_us_at")},null,8,["innerHTML"]),Object(n["h"])("div",We,[Object(n["h"])("div",{class:["step",{visited:"firstState"===i.state}]},null,2),Je,Object(n["h"])("div",{class:["step",{visited:"secondState"===i.state}]},null,2),ze,Object(n["h"])("div",{class:["step",Object(Ge["a"])({visited:"thirdState"===i.state},"visited","redirectionState"===i.state)]},null,2)])])}var Ke={name:"Footer",props:["state"],data:function(){return{}}};Ke.render=Ye;var Ze=Ke,Xe={name:"LoginContainer",components:{LanguageSelector:v,FirstState:K,SecondState:Pe,RedirectionState:He,Footer:Ze},mounted:function(){this.loadi2e1Data(),this.doSomethingBad(),this.initState()},data:function(){return{questionTypes:[3],mobile:null,nasid:0,loginUserSession:null,state:"firstState",i2e1Data:{i2e1Constants:null,viewBag:null,loginUser:null},cdnDomain:null,sessionAttributes:{dataLeft:"",timeLeft:""},logo:null,loader:!1,repaintSecondState:!1}},methods:{loadi2e1Data:function(){this.loader=!0,this.i2e1Data={i2e1Constants:window.i2e1Constants,viewBag:window.viewBag,loginUser:window.loginUser},this.i2e1Data.loginUser.cdnDomain=window.cdnDomain,this.loginUserSession=h.getQueryStringValue("login-user-session"),this.cdnDomain=window.cdnDomain,this.loginUserSession&&""!=this.loginUserSession&&(h.setCookie("login_user_session",this.loginUserSession),this.nasid=this.loginUserSession.split("-")[this.loginUserSession.split("-").length-1],this.getCustomLogo(),this.loader=!1)},setMobile:function(e){this.mobile=e,this.i2e1Data.loginUser.tempmobile=e},getCustomLogo:function(){var e=this;try{setTimeout((function(){var t=document.getElementsByTagName("outer-header")[0].firstElementChild.getAttribute("src");0==t.indexOf("http")?e.logo=document.getElementsByTagName("outer-header")[0].firstElementChild.getAttribute("src"):e.logo="https://i2e1.in"+document.getElementsByTagName("outer-header")[0].firstElementChild.getAttribute("src"),console.log("FOUND LOGO",e.logo)}),120)}catch(t){console.log("Error :",JSON.stringify(t)),this.logo=this.cdnDomain+"images/wiom/wiom_logo.png"}},overrideCTAColor:function(e){for(var t=0;t<document.getElementsByClassName("primary login_button").length;t++){var i=window.getComputedStyle(document.getElementsByClassName("primary login_button")[t]);for(var n in i)"background-color"==i[n]&&0,"background-image"==i[n]&&0}},doSomethingBad:function(){var e={questionTypes:this.questionTypes,mobile:z.a.getMobile(this),routerId:this.nasid};f.post("Login/GetQuestions?login-user-session="+this.loginUserSession,e).then((function(){})).catch((function(){}))},initState:function(e){this.i2e1Data.viewBag.sessionExpired?this.swapState("errorState",{errorType:"time-exhausted",msg:"Your time limit is over",errorImg:"/images/session_time_out.png"}):this.i2e1Data.viewBag.dataExhausted?this.swapState("errorState",{errorType:"limit-exhausted",msg:"Your data limit is over",errorImg:"/images/limitExhausted.png"}):this.i2e1Data.viewBag.loginResponse&&e!==this.i2e1Data.i2e1Constants.customState?"Auto Login"===this.i2e1Data.viewBag.loginResponse.msg&&(this.i2e1Data.i2e1Constants.redirectionState="AL Redirection State",this.i2e1Data.i2e1Constants.landingState="AL Landing State",this.i2e1Data.viewBag.loginResponse.data.isAutoLogin=!0):e?this.swapState(e):this.i2e1Data.loginUser.tempmobile&&""!=this.i2e1Data.loginUser.tempmobile?(this.mobile=this.i2e1Data.loginUser.tempmobile,this.swapState("secondState")):this.swapState("firstState")},swapState:function(e){this.state=e},generateOTPFailure:function(e){this.loader=!1},generateOTPSuccess:function(e,t){1==e.status||e.data.otpResponse.landingPage,t||this.swapState("secondState"),t&&(this.loader=!1)},generateOTP:function(e,t){var i=this;this.loader=!0,f.post("Login/GenerateOTP?login-user-session="+h.getCookie("login_user_session"),{mobile:e,smsApi:t?"change":null,clientAuthType:"PHONE"}).then((function(e){e.data?i.generateOTPSuccess(e.data,t):i.generateOTPFailure(e)})).catch((function(e){i.generateOTPFailure(e)}))},setSessionAttributes:function(e){this.sessionAttributes=e}}};Xe.render=d;var et=Xe,tt={name:"App",components:{LoginContainer:et}};tt.render=o;var it=tt,nt=i("ab42");function st(){var e=i("49f8"),t={};return e.keys().forEach((function(i){var n=i.match(/([A-Za-z0-9-_]+)\./i);if(n&&n.length>1){var s=n[1];t[s]=e(i)}})),t}var ot=Object(nt["a"])({locale:window.viewBag.resources.Culture.split("-")[0],fallbackLocale:"en",messages:st()}),at=Object(n["d"])(it);at.use(ot),at.mount("#app")},"7a03":function(e){e.exports=JSON.parse('{"hilao":"hilao","dataVoucherState":{"choose_your_plan":"अपना प्लान चुनें","connect":"जुड़ें","enter_voucher_code":"वाउचर कोड डालें","free_internet":"फ्री इंटरनेट","use_coupon":"उपयोगकर्ता का कूपन"},"errorState":{"try_logging_again":"दोबारा लॉगिन करें"},"firstState":{"10_digit_phone_error":"फोन नंबर में 10 अंक होने चाहिए","bark_lessloud":"मोबाइल नंबर से साइन इन करें","bark_loud":"आजीवन फ्री WiFi पाने के लिए","connect":"जुड़ें","continue_as":"जारी रखने के लिए अपना मोबाइल नंबर डालें","dont_have_national_id_click_here":"पासपोर्ट या सरकारी पहचान पत्र नहीं है? <a href=\\"/Login/GetAuthenticationPage/?authType=PHONE\\">यहाँ क्लिक करें</a>","dont_have_phno_click_here":"क्या आपके पास फ़ोन नंबर नहीं है? <a href=\\"/Login/GetAuthenticationPage/?authType=NATIONAL_ID\\">यहाँ क्लिक करें</a>","enter_national_id":"पासपोर्ट या सरकारी पहचान पत्र दें","enter_phone_number":"उदाहरण: 9876543201","fdm_notauthorised":"Please Contact Reception for Internet Access","generate_otp":"साइन इन करें","invalid_phno":"आपका नंबर अमान्य है","last_name":"अपना उपनाम डालें","mobile_invalid":"गलत मोबाइल नंबर","or":"या","room_no":"कमरा क्रमांक","sign_in_as_guest":"अथिति के रूप में लॉग इन करें","tnc":"जारी रखकर आप <a onclick=\'_onTileClick(\\"/Templates/i2e1/tnc.html\\")\'>नियम और शर्तें</a> स्वीकार करते हैं","welcome_back":"पुनः पधारने पर आपका स्वागत है"},"footer":{"facing_issues_call_shopkeeper":"सहायता के लिए दूकानदार से संपर्क करें","facing_issues_call_us_at":"मदद लें  - <b><a href=\\"tel:8880322222\\">8880322222</a></b>"},"help":"सहायता के लिए यहाँ क्लिक करें","helpState":{"enter_mobile_number":"मोबाइल नंबर डालें","enter_password":"चार अंकों का पासवर्ड डालें","help":"सहायता के लिए यहाँ क्लिक करें","i2e1_connects_you_to_wifi":"i2e1 आपको मुफ्त वाई-फाई से जोड़ता है","three_simple_steps_to_wifi":"वाई-फाई प्राप्त करने हेतु तीन आसान कदम"},"landingState":{"congratulations":"बधाई हो|","connected_msg":"आप इंटरनेट से जुड़ चुके हैं","data_left_prefix":"","data_left_suffix":" फ्री WiFi का इस्तेमाल करें!","logout":"लॉग आउट","search":"खोजें","time_day":"दिन","time_days":"दिन","time_hr":"घंटा","time_hrs":"घंटा","time_left":"बचा हुआ समय","time_lessthan_a_minute":"एक मिनट से कम","time_min":"मिनट","time_mins":"मिनट","you_are_now_connected":"आप इंटरनेट से जुड़ चुके हैं"},"logout":"लॉग आउट","please_configure_nas_id":"कृपया वाई-फाई पहचानकर्ता को कॉन्फ़िगर करें","redirectionState":{"data_policy":"<span class=\\"data\\">%{data}</span> फ्री WiFi का इस्तेमाल करें","please_wait":"बहुत बढ़िया!","time_policy":"समय अवधि: <span class=\\"time\\">%{time}</span>","you_are_almost_there":"आपको 5 सेकंड में जोड़ा जा रहा है…"},"secondState":{"back":"फ़ोन नंबर बदलने के लिए पीछे जाएं","bark_lessloud":"आपके SMS पर भेजे गए (OTP) पासवर्ड से","bark_loud":"नंबर की पुष्टि करें","captcha":"कैप्चा भरें","change":"नंबर बदलें","confirm":"पुष्टि करें","connect":"फ्री WiFi से जुड़ें","enter_4_digit_password":"४ अंको का पासवर्ड डालें","enter_4_digit_password_sent_to_mobile":"{0} पे भेजा गया पासवर्ड डालें","enter_captcha":"कैप्चा भरें","enter_voucher_code":"वाउचर कोड डालें","not_getting_otp_click_here":"ओटीपी नहीं आने पर <a onclick=\\"$(\'#otp_access_code\').show();$(\'#otp\').val(\'1234\').hide()\\">यहाँ क्लिक करें</a>","otp_invalid":"गलत OTP","questions_cta_okay":"ठीक है","questions_lessloud":"आसान सवालों के जवाब दे कर","questions_loud":"<b>1 GB</b> मुफ्त इंटरनेट प्राप्त करें","resend":"फिर से भेजें","welcomeback_greetings":"<img src=\\"%{prefix}images/wiom/welcomeback.hi.svg\\" />"}}')},edd4:function(e){e.exports=JSON.parse('{"hilao":"hello","dataVoucherState":{"choose_your_plan":"Choose your plan","connect":"Connect","enter_voucher_code":"Enter voucher code","free_internet":"Free Internet","use_coupon":"User coupon?"},"errorState":{"try_logging_again":"Try logging again"},"firstState":{"10_digit_phone_error":"Phone number should have 10 digits","bark_lessloud":"Sign in using Phone Number","bark_loud":"Get Life Time Free WiFi","connect":"Connect","continue_as":"Continue as","dont_have_national_id_click_here":"Don\'t have passport/national id ? <a href=\\"/Login/GetAuthenticationPage/?authType=PHONE\\">Click here</a>","dont_have_phno_click_here":"Don\'t have phone number? <a href=\\"/Login/GetAuthenticationPage/?authType=NATIONAL_ID\\">Click here</a>","enter_national_id":"Enter passport/national id","enter_phone_number":"e.g. 9876543201","fdm_notauthorised":"Please Contact Reception for Internet Access","generate_otp":"Sign In","invalid_phno":"Invalid phone number","last_name":"Last name","mobile_invalid":"Invalid Mobile Number","or":"OR","room_no":"Room no.","sign_in_as_guest":"Sign In as Guest","tnc":"By continuing you accept <a onclick=\'_onTileClick(\\"/Templates/i2e1/tnc.html\\")\'>Terms & Conditions</a>","welcome_back":"Welcome Back!"},"footer":{"facing_issues_call_shopkeeper":"Facing issues? Contact Shopkeeper","facing_issues_call_us_at":"Get Help - <b><a href=\\"tel:8880322222\\">8880322222</a></b>"},"help":"Help","helpState":{"enter_mobile_number":"Enter Mobile Number","enter_password":"Enter 4 digit password","help":"HELP","i2e1_connects_you_to_wifi":"i2e1 connects you to free Wi-Fi","three_simple_steps_to_wifi":"3 simple steps to get Wi-Fi"},"landingState":{"congratulations":"Congratulations!","connected_msg":"You are now Connected","data_left":"Data left","data_left_prefix":"Use Free ","data_left_suffix":" Wi-Fi now!","logout":"Logout","search":"Search","time_day":"day","time_days":"days","time_hr":"hr","time_hrs":"hrs","time_left":"Time left","time_lessthan_a_minute":"less than a minute","time_min":"min","time_mins":"mins","you_are_now_connected":"You are now Connected"},"logout":"Logout","please_configure_nas_id":"Please Configure Wifi Identifier","redirectionState":{"data_policy":"Use Free <span class=\\"data\\">%{data}</span> Wi-Fi Now","please_wait":"Well Done!","time_policy":"Time Duration: <span class=\\"time\\">%{time}</span>","you_are_almost_there":"Connecting you in 5 seconds…"},"secondState":{"back":"Go back to change phone number","bark_lessloud":"with Password (OTP) Sent using SMS","bark_loud":"Verify Number","captcha":"Captcha","change":"Change Number","confirm":"Confirm","connect":"CONNECT TO FREE WIFI","enter_4_digit_password":"Enter 4 Digit Password","enter_4_digit_password_sent_to_mobile":"Enter password sent to {0}","enter_captcha":"Enter Captcha","enter_voucher_code":"Enter Voucher Code","not_getting_otp_click_here":"Not Getting OTP? <a onclick=\\"$(\'#otp_access_code\').show();$(\'#otp\').val(\'1234\').hide()\\">Click here</a>","otp_invalid":"Invalid OTP","questions_cta_okay":"Okay","questions_lessloud":"Answer simple questions","questions_loud":"Get <b>%{data}</b> Free Internet today","resend":"Resend","welcomeback_greetings":"<img src=\'%{prefix}images/wiom/welcomeback.en.svg\' />"}}')}});
//# sourceMappingURL=app.ef44ce60.js.map