{"version": 3, "sources": ["webpack:///webpack/bootstrap", "webpack:///./src/Libs/Login.js", "webpack:///./src/locales sync [A-Za-z0-9-_,\\s]+\\.json$/", "webpack:///./src/App.vue", "webpack:///./src/components/LoginContainer.vue", "webpack:///./src/utils/Helper.js", "webpack:///./src/plugins/axios.js", "webpack:///./src/components/Common/LanguageSelector.vue", "webpack:///./src/components/Common/LanguageSelector.vue?656f", "webpack:///./src/components/FirstState/FirstState.vue", "webpack:///./src/components/FirstState/PhoneInput.vue", "webpack:///./src/components/FirstState/PhoneInput.vue?4d4b", "webpack:///./src/components/Common/Survey/Survey.vue", "webpack:///./src/components/Common/Survey/PreSurvey.vue", "webpack:///./src/components/Common/Survey/PreSurvey.vue?5643", "webpack:///./src/components/Common/Survey/Survey.vue?f254", "webpack:///./src/components/FirstState/FirstState.vue?7b26", "webpack:///./src/components/SecondState/SecondState.vue", "webpack:///./src/components/SecondState/OTPInput.vue", "webpack:///./src/components/SecondState/OTPInput.vue?02f6", "webpack:///./src/components/SecondState/SecondState.vue?30be", "webpack:///./src/components/RedirectionState/RedirectionState.vue", "webpack:///./src/components/RedirectionState/RedirectionState.vue?c7f1", "webpack:///./src/components/Common/Footer.vue", "webpack:///./src/components/Common/Footer.vue?ecfd", "webpack:///./src/components/LoginContainer.vue?1da1", "webpack:///./src/App.vue?dfb6", "webpack:///./src/main.js"], "names": ["webpackJsonpCallback", "data", "moduleId", "chunkId", "chunkIds", "moreModules", "executeModules", "i", "resolves", "length", "Object", "prototype", "hasOwnProperty", "call", "installedChunks", "push", "modules", "parentJsonpFunction", "shift", "deferredModules", "apply", "checkDeferredModules", "result", "deferredModule", "fulfilled", "j", "depId", "splice", "__webpack_require__", "s", "installedModules", "exports", "module", "l", "m", "c", "d", "name", "getter", "o", "defineProperty", "enumerable", "get", "r", "Symbol", "toStringTag", "value", "t", "mode", "__esModule", "ns", "create", "key", "bind", "n", "object", "property", "p", "jsonpArray", "window", "oldJsonpFunction", "slice", "_searchObjectInArray", "<PERSON><PERSON><PERSON>", "nameValue", "myArray", "getMobile", "that", "mobile", "i2e1Data", "loginUser", "tempmobile", "makei2e1Login", "loginResponse", "form", "document", "createElement", "url", "isAutoLogin", "parameters", "location", "hostname", "input", "setAttribute", "append<PERSON><PERSON><PERSON>", "encodeURIComponent", "indexOf", "toLowerCase", "href", "obj", "this", "protocol", "replace", "isGet", "attr", "append", "body", "submit", "do<PERSON><PERSON><PERSON>", "landingPage", "map", "webpackContext", "req", "id", "webpackContextResolve", "e", "Error", "code", "keys", "resolve", "class", "cage", "style", "src", "cdnDomain", "alt", "loader", "logo", "onerror", "state", "loginUserSession", "setMobile", "$event", "generateOTP", "swapState", "sessionAttributes", "<PERSON><PERSON><PERSON><PERSON>", "cname", "cvalue", "exdays", "Date", "setTime", "getTime", "expires", "toUTCString", "cookie", "<PERSON><PERSON><PERSON><PERSON>", "ca", "split", "char<PERSON>t", "substring", "deleteC<PERSON>ie", "reload", "getQueryStringValue", "decodeURIComponent", "search", "RegExp", "shuffle", "array", "temporaryValue", "randomIndex", "currentIndex", "Math", "floor", "random", "roundOffData", "val", "roundOffTime", "totalSec", "days", "parseInt", "hours", "minutes", "A<PERSON>os", "axios", "baseURL", "interceptors", "response", "use", "error", "status", "Promise", "reject", "changeLanguage", "showOption", "props", "emits", "currentLanguage", "mounted", "$i18n", "locale", "methods", "language", "$emit", "render", "$t", "<PERSON><PERSON><PERSON><PERSON>", "setInvalid", "questionType", "type", "ref", "disableCTA", "disabled", "validUsername", "validate", "maxlength", "max", "required", "pattern", "for", "textAlign", "valid", "watch", "computed", "viewBag", "dataLeft", "onclick", "components", "Pre<PERSON>urvey", "PhoneInput", "Survey", "i2e1Ques", "validateUsername", "console", "log", "getComputedStyle", "$refs", "get_otp", "onsubmit", "title", "placeholder", "showBack", "welcomeBack", "showBarking", "ValidOTP", "InvalidOTP", "showOTPArea", "invalidOTP", "showCaptcha", "showSurveyConfirmation", "answerQuestions", "startedAnswering", "question", "quesType", "journey", "answerType", "quesText", "options", "option", "mark", "index", "checked", "answer", "image", "text", "hide", "done", "showNext", "confirm", "showConfirmCTA", "connect", "showConnectCTA", "showBottomButtons", "min", "digit1", "digit2", "digit3", "digit4", "otp", "focus", "select", "OTPInput", "onMount", "questionTypes", "nasid", "OTP", "surveyStarted", "showQuestions", "i2e1FirstAnsweredAt", "dataPolicy", "timeLeft", "WelcomeBack", "getQuestions", "getNasAttributes", "setTimeout", "_self", "failure", "success", "preLogin", "username", "params", "processAnswers", "post", "accessCode", "macId", "mac", "routerId", "challenge", "clientAuthType", "<PERSON><PERSON>a", "questions", "then", "catch", "invalid", "toString", "randomized", "binarySplit", "temp", "prepareQuestion", "for<PERSON>ach", "ques", "filter", "q", "count", "templateId", "templateid", "remainingOptions", "optionsOrder", "totalJourneys", "displayTime", "now", "showWelcomeBack", "quesIndex", "undefined", "selectionTime", "questionId", "next", "currentQuestionIndex", "currentQuestionJourneys", "displayIndex", "err<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "trim", "isSelected", "info", "vip", "Helper", "offers", "skip", "LanguageSelector", "FirstState", "SecondState", "RedirectionState", "Footer", "loadi2e1Data", "doSomethingBad", "initState", "i2e1Constants", "repaintSecondState", "getCustomLogo", "getElementsByTagName", "first<PERSON><PERSON><PERSON><PERSON><PERSON>", "getAttribute", "ex", "JSON", "stringify", "overrideCTAColor", "element", "getElementsByClassName", "prop", "found", "sessionExpired", "errorType", "msg", "errorImg", "dataExhausted", "customState", "redirectionState", "landingState", "nextState", "generateOTPFailure", "generateOTPSuccess", "resend", "otpResponse", "smsApi", "setSessionAttributes", "LoginContainer", "loadLocaleMessages", "locales", "require", "messages", "matched", "match", "i18n", "createI18n", "resources", "Culture", "fallback<PERSON><PERSON><PERSON>", "app", "createApp", "App", "mount"], "mappings": "aACE,SAASA,EAAqBC,GAQ7B,IAPA,IAMIC,EAAUC,EANVC,EAAWH,EAAK,GAChBI,EAAcJ,EAAK,GACnBK,EAAiBL,EAAK,GAIHM,EAAI,EAAGC,EAAW,GACpCD,EAAIH,EAASK,OAAQF,IACzBJ,EAAUC,EAASG,GAChBG,OAAOC,UAAUC,eAAeC,KAAKC,EAAiBX,IAAYW,EAAgBX,IACpFK,EAASO,KAAKD,EAAgBX,GAAS,IAExCW,EAAgBX,GAAW,EAE5B,IAAID,KAAYG,EACZK,OAAOC,UAAUC,eAAeC,KAAKR,EAAaH,KACpDc,EAAQd,GAAYG,EAAYH,IAG/Be,GAAqBA,EAAoBhB,GAE5C,MAAMO,EAASC,OACdD,EAASU,OAATV,GAOD,OAHAW,EAAgBJ,KAAKK,MAAMD,EAAiBb,GAAkB,IAGvDe,IAER,SAASA,IAER,IADA,IAAIC,EACIf,EAAI,EAAGA,EAAIY,EAAgBV,OAAQF,IAAK,CAG/C,IAFA,IAAIgB,EAAiBJ,EAAgBZ,GACjCiB,GAAY,EACRC,EAAI,EAAGA,EAAIF,EAAed,OAAQgB,IAAK,CAC9C,IAAIC,EAAQH,EAAeE,GACG,IAA3BX,EAAgBY,KAAcF,GAAY,GAE3CA,IACFL,EAAgBQ,OAAOpB,IAAK,GAC5Be,EAASM,EAAoBA,EAAoBC,EAAIN,EAAe,KAItE,OAAOD,EAIR,IAAIQ,EAAmB,GAKnBhB,EAAkB,CACrB,IAAO,GAGJK,EAAkB,GAGtB,SAASS,EAAoB1B,GAG5B,GAAG4B,EAAiB5B,GACnB,OAAO4B,EAAiB5B,GAAU6B,QAGnC,IAAIC,EAASF,EAAiB5B,GAAY,CACzCK,EAAGL,EACH+B,GAAG,EACHF,QAAS,IAUV,OANAf,EAAQd,GAAUW,KAAKmB,EAAOD,QAASC,EAAQA,EAAOD,QAASH,GAG/DI,EAAOC,GAAI,EAGJD,EAAOD,QAKfH,EAAoBM,EAAIlB,EAGxBY,EAAoBO,EAAIL,EAGxBF,EAAoBQ,EAAI,SAASL,EAASM,EAAMC,GAC3CV,EAAoBW,EAAER,EAASM,IAClC3B,OAAO8B,eAAeT,EAASM,EAAM,CAAEI,YAAY,EAAMC,IAAKJ,KAKhEV,EAAoBe,EAAI,SAASZ,GACX,qBAAXa,QAA0BA,OAAOC,aAC1CnC,OAAO8B,eAAeT,EAASa,OAAOC,YAAa,CAAEC,MAAO,WAE7DpC,OAAO8B,eAAeT,EAAS,aAAc,CAAEe,OAAO,KAQvDlB,EAAoBmB,EAAI,SAASD,EAAOE,GAEvC,GADU,EAAPA,IAAUF,EAAQlB,EAAoBkB,IAC/B,EAAPE,EAAU,OAAOF,EACpB,GAAW,EAAPE,GAA8B,kBAAVF,GAAsBA,GAASA,EAAMG,WAAY,OAAOH,EAChF,IAAII,EAAKxC,OAAOyC,OAAO,MAGvB,GAFAvB,EAAoBe,EAAEO,GACtBxC,OAAO8B,eAAeU,EAAI,UAAW,CAAET,YAAY,EAAMK,MAAOA,IACtD,EAAPE,GAA4B,iBAATF,EAAmB,IAAI,IAAIM,KAAON,EAAOlB,EAAoBQ,EAAEc,EAAIE,EAAK,SAASA,GAAO,OAAON,EAAMM,IAAQC,KAAK,KAAMD,IAC9I,OAAOF,GAIRtB,EAAoB0B,EAAI,SAAStB,GAChC,IAAIM,EAASN,GAAUA,EAAOiB,WAC7B,WAAwB,OAAOjB,EAAO,YACtC,WAA8B,OAAOA,GAEtC,OADAJ,EAAoBQ,EAAEE,EAAQ,IAAKA,GAC5BA,GAIRV,EAAoBW,EAAI,SAASgB,EAAQC,GAAY,OAAO9C,OAAOC,UAAUC,eAAeC,KAAK0C,EAAQC,IAGzG5B,EAAoB6B,EAAI,IAExB,IAAIC,EAAaC,OAAO,gBAAkBA,OAAO,iBAAmB,GAChEC,EAAmBF,EAAW3C,KAAKsC,KAAKK,GAC5CA,EAAW3C,KAAOf,EAClB0D,EAAaA,EAAWG,QACxB,IAAI,IAAItD,EAAI,EAAGA,EAAImD,EAAWjD,OAAQF,IAAKP,EAAqB0D,EAAWnD,IAC3E,IAAIU,EAAsB2C,EAI1BzC,EAAgBJ,KAAK,CAAC,EAAE,kBAEjBM,K,smHCvJTW,EAAOD,QAAU,CACf+B,qBAAsB,SAASC,EAASC,EAAWC,GACjD,IAAK,IAAI1D,EAAI,EAAGA,EAAI0D,EAAQxD,OAAQF,IAClC,GAAI0D,EAAQ1D,GAAGwD,KAAaC,EAC1B,OAAOC,EAAQ1D,IAIrB2D,UAAW,SAASC,GAClB,OAAIA,EAAKC,QAAyB,IAAfD,EAAKC,OAAqBD,EAAKC,OACzCD,EAAKE,SAASC,UAAUC,WACxBJ,EAAKE,SAASC,UAAUC,WACrB,IAEdC,cAAe,SAASC,GAOtB,IANA,IAAIC,EAAOC,SAASC,cAAc,QAC9BC,EAAMJ,EAAcI,IAAM,IAKrBtE,GAFOkE,EAAcK,YAEjB,GAAGvE,EAAIkE,EAAcM,WAAWtE,SAAUF,EAAG,CACxD,IAAIuC,EAAQ2B,EAAcM,WAAWxE,GAAGuC,MAExC,GAC+B,cAA7Ba,OAAOqB,SAASC,UACsB,QAArCR,EAAcM,WAAWxE,GAAG8B,MACU,YAArCoC,EAAcM,WAAWxE,GAAG8B,KAGzB,CACL,IAAI6C,EAAQP,SAASC,cAAc,SACnCM,EAAMC,aAAa,OAAQV,EAAcM,WAAWxE,GAAG8B,MACvD6C,EAAMC,aAAa,QAASrC,GAC5B4B,EAAKU,YAAYF,GACjBL,GACEJ,EAAcM,WAAWxE,GAAG8B,KAC5B,IACAgD,mBAAmBvC,GACnB,SAVF+B,EAAMJ,EAAcM,WAAWxE,GAAGuC,MAcG,QAArC2B,EAAcM,WAAWxE,GAAG8B,MACS,YAArCoC,EAAcM,WAAWxE,GAAG8B,MAG1BoC,EAAcM,WAAWxE,GAAGuC,MAAMwC,QAAQ3B,OAAOqB,SAASC,UACxD,GACFR,EAAcM,WAAWxE,GAAGuC,MACzByC,cACAD,QAAQ,eAAiB,GAEbb,EAAcM,WAAWxE,GAAGuC,MASjD,GAAIa,OAAOqB,SAASQ,KAAKF,QAAQ,WAAa,EAAG,CAC/C,IAAIG,EAAMC,KAAK5B,qBACb,OACA,UACAW,EAAcM,YAEhBpB,OAAOqB,SAASQ,KAAOC,EAAI3C,UAEM,WAA7Ba,OAAOqB,SAASW,SAClBhC,OAAOqB,SAASQ,KAAOX,EAAIe,QAAQ,cAAe,YAElDnB,EAAcoB,OACe,cAA7BlC,OAAOqB,SAASC,SAEhBtB,OAAOqB,SAASQ,KAAOX,GAEvBH,EAAKoB,KAAK,SAAUrB,EAAcI,KAClCH,EAAKoB,KAAK,SAAU,QACpBZ,EAAQP,SAASC,cAAc,SAC/BM,EAAMC,aAAa,OAAQ,UAC3BT,EAAKqB,OAAOb,GACZP,SAASqB,KAAKZ,YAAYV,GAC1BA,EAAKuB,WAIXC,QAAS,WACPR,KAAKlB,cAAcb,OAAOwC,gB,uBCzF9B,IAAIC,EAAM,CACT,YAAa,OACb,YAAa,OACb,YAAa,QAId,SAASC,EAAeC,GACvB,IAAIC,EAAKC,EAAsBF,GAC/B,OAAO1E,EAAoB2E,GAE5B,SAASC,EAAsBF,GAC9B,IAAI1E,EAAoBW,EAAE6D,EAAKE,GAAM,CACpC,IAAIG,EAAI,IAAIC,MAAM,uBAAyBJ,EAAM,KAEjD,MADAG,EAAEE,KAAO,mBACHF,EAEP,OAAOL,EAAIE,GAEZD,EAAeO,KAAO,WACrB,OAAOlG,OAAOkG,KAAKR,IAEpBC,EAAeQ,QAAUL,EACzBxE,EAAOD,QAAUsE,EACjBA,EAAeE,GAAK,Q,4KCvBbO,MAAM,iB,uFAAX,eAEM,MAFN,EAEM,CADJ,eAAiB,K,OCDdA,MAAM,mB,GACYA,MAAM,W,GAAeA,MAAM,mB,GAE3CA,MAAM,mB,GACJA,MAAM,cAAcC,KAAK,eAAeC,MAAA,wB,wNAJjD,eA+BM,MA/BN,EA+BM,C,eA9BJ,eAAuL,MAAvL,EAAuL,CAAlJ,eAA4I,MAA5I,EAA4I,CAA/G,eAAyG,OAApGT,GAAG,SAASS,MAAA,eAA0BC,IAAK,EAAAC,UAAS,0BAA8BC,IAAI,c,iCAAhJ,EAAAC,UACb,eAAoB,GACpB,eA0BM,MA1BN,EA0BM,CAzBJ,eAEM,MAFN,EAEM,CADO,EAAAC,M,iBAAX,eAAsL,O,MAArKd,GAAG,iBAAiBe,QAAQ,kEAAkER,MAAM,YAAmBG,IAAK,EAAAI,KAAML,MAAA,+B,yCAIxI,cAAL,EAAAO,O,iBAFR,eAOuC,GANpCnE,IAAG,eAAiB,EAAAoE,iBAEhB,YAAU,+BAAE,EAAAC,UAAUC,KACtB,cAAY,+BAAE,EAAAC,YAAYD,KAC1B,YAAU,+BAAE,EAAAE,UAAUF,KACpBrD,SAAU,EAAAA,SACZ,aAAW,+BAAE,EAAA+C,OAASM,K,4CAGhB,eAAL,EAAAH,O,iBAFR,eAOuC,GANpCnE,IAAG,gBAAkB,EAAAgB,OAEjB,YAAU,+BAAE,EAAAwD,UAAUF,KACtB,YAAU,+BAAE,EAAAC,YAAYD,GAAM,KAC9B,oBAAkB,+BAAE,EAAAG,kBAAoBH,IACtCrD,SAAU,EAAAA,SACZ,aAAW,+BAAE,EAAA+C,OAASM,K,4CAGhB,oBAAL,EAAAH,O,iBAFR,eAKiD,GAJ9CnE,IAAG,qBAAuB,EAAAgB,OAEtB,YAAU,+BAAE,EAAAwD,UAAUF,KACpBrD,SAAU,EAAAA,SACVwD,kBAAmB,EAAAA,mB,kEAE9B,eAA+B,GAAhBN,MAAO,EAAAA,OAAK,oB,sDC/BhB,GACbO,UAAW,SAASC,EAAOC,GAAoB,IAAZC,EAAY,uDAAH,EACtC7F,EAAI,IAAI8F,KACZ9F,EAAE+F,QAAQ/F,EAAEgG,UAAqB,GAATH,EAAc,GAAK,GAAK,KAChD,IAAII,EAAU,WAAajG,EAAEkG,cAC7B3D,SAAS4D,OAASR,EAAQ,IAAMC,EAAS,IAAMK,EAAU,WAE3DG,UAAW,SAAST,GAGlB,IAFA,IAAI1F,EAAO0F,EAAQ,IACfU,EAAK9D,SAAS4D,OAAOG,MAAM,KACtBnI,EAAI,EAAGA,EAAIkI,EAAGhI,OAAQF,IAAK,CAClC,IAAI4B,EAAIsG,EAAGlI,GACX,MAAsB,KAAf4B,EAAEwG,OAAO,GACdxG,EAAIA,EAAEyG,UAAU,GAElB,GAAuB,GAAnBzG,EAAEmD,QAAQjD,GACZ,OAAOF,EAAEyG,UAAUvG,EAAK5B,OAAQ0B,EAAE1B,QAGtC,MAAO,IAEToI,aAAc,SAASd,GACrBpD,SAAS4D,OACPR,EAAQ,qDAEZe,OAAQ,WACN9D,SAAS8D,UAEXC,oBAAqB,SAAS3F,GAC5B,OAAO4F,mBACLrF,OAAOqB,SAASiE,OAAOrD,QACrB,IAAIsD,OACF,eACE7D,mBAAmBjC,GAAKwC,QAAQ,SAAU,QAC1C,uBACF,KAEF,QAINuD,QAAS,SAASC,GAChB,IACEC,EACAC,EAFEC,EAAeH,EAAM3I,OAKzB,MAAO,IAAM8I,EAEXD,EAAcE,KAAKC,MAAMD,KAAKE,SAAWH,GACzCA,GAAgB,EAGhBF,EAAiBD,EAAMG,GACvBH,EAAMG,GAAgBH,EAAME,GAC5BF,EAAME,GAAeD,EAGvB,OAAOD,GAETO,aAAc,SAASC,GACrB,OAAOJ,KAAKC,MAAY,IAANG,GAAa,KAEjCC,aAAc,SAASC,GACrB,GAAIA,EAAW,EAAG,CAChB,IAAIC,EAAOC,SAASF,EAAW,OAC3BG,EAAQD,SAASF,EAAW,MAAQ,GACpCI,EAAUF,SAASF,EAAW,IAAM,GAEpCxI,GACDyI,GAAQ,EAAIA,GAAiB,IAATA,EAAa,QAAU,UAAY,KACvDE,GAAS,EAAIA,GAAmB,IAAVA,EAAc,OAAS,SAAW,IAI3D,OAHIF,EAAO,IACTzI,GACE4I,GAAW,EAAIA,GAAuB,IAAZA,EAAgB,OAAS,SAAW,IAC3D5I,GAAkB,qBAE3B,MAAO,S,qBC3EP4F,EAAY,kBAEZvD,OAAOuD,UAAU5B,QAAQ,cAAgB,EAC3C4B,EAAY,0BACHvD,OAAOuD,UAAU5B,QAAQ,aAAe,IACjD4B,EAAY,4BAGd,IAAIiD,EAAQC,IAAMjH,OAAO,CACvBkH,QAASnD,IAGXiD,EAAMG,aAAaC,SAASC,KAC1B,SAASD,GACP,OAAOA,KAET,SAASE,GACP,IACEA,EAAMF,WACNE,EAAMF,SAASG,QACW,MAA1BD,EAAMF,SAASG,OAGZ,OAAOC,QAAQC,OAAOH,GADzB9G,OAAOqB,SAAS,QAKPmF,Q,GC5BNrD,MAAM,sB,gDAAX,eAIM,MAJN,EAIM,C,eAHF,eAA8E,KAAhD,QAAK,+BAAE,EAAA+D,eAAc,QAAQtE,GAAG,MAAK,UAAO,M,QAA/D,EAAAuE,WAAU,S,eACrB,eAA4E,KAA9C,QAAK,+BAAE,EAAAD,eAAc,QAAQtE,GAAG,MAAK,QAAK,M,QAA7D,EAAAuE,WAAU,S,eACrB,eAA6E,KAA/C,QAAK,+BAAE,EAAAD,eAAc,QAAQtE,GAAG,MAAK,SAAM,M,QAA9D,EAAAuE,WAAU,WAKd,OACXzI,KAAM,mBACN0I,MAAO,GACPC,MAAO,CAAC,oBACR/K,KAAM,WACF,MAAO,CACHgL,gBAAiB,OAGzBC,QAAS,WACLxF,KAAKuF,gBAAkBvF,KAAKyF,MAAMC,QAEtCC,QAAS,CACLP,WAAY,SAAUQ,GAClB,OAAI5F,KAAKuF,iBAAmBK,GAKhCT,eAAgB,SAASS,GACrB5F,KAAKuF,gBAAkBK,EACvB5F,KAAK6F,MAAM,mBAAoBD,GAC/B5F,KAAKyF,MAAMC,OAASE,KC5BhC,EAAOE,OAAS,EAED,Q,GCJL1E,MAAM,qC,GACHA,MAAM,W,GACFA,MAAM,Q,GACNA,MAAM,a,GAIVC,KAAK,gB,GAGLD,MAAM,Y,8GAVf,eAaO,OAbP,EAaO,CAZH,eAGM,MAHN,EAGM,CAFF,eAA0D,MAA1D,EAA0D,eAApC,EAAA2E,GAAE,4BACxB,eAAiE,MAAjE,EAAiE,eAAvC,EAAAA,GAAE,kCAEhC,eAAwE,GAAvD,QAAK,+BAAE,EAAAC,SAAShE,KAAc,UAAO,+BAAE,EAAAiE,iBACxD,eAAkE,GAAnDC,aAAc,EAAAA,aAAqBrE,MAAO,EAAAA,O,iCACzD,eAEM,MAFN,EAEM,CADF,eAA4P,SAArPsE,KAAK,SAAStF,GAAG,UAAUuF,IAAI,UAAUzJ,KAAK,QAAQyE,MAAM,uBAA8BhE,MAAO,EAAA2I,GAAE,2BAA8B,QAAK,wBAAE,EAAAM,YAAU,EAAS,EAAAR,MAAK,eAAiB,EAAAnH,UAAgB4H,UAAQ,KAAQC,eAAa,KAASF,Y,+BAElP,eAEM,MAFN,EAEM,CADF,eAAmE,QAA7DjF,MAAM,OAAOC,KAAK,MAAM,UAAQ,EAAA0E,GAAE,mB,iCCX3C3E,MAAM,8B,GACFA,MAAM,S,GACNA,MAAM,S,GAgBNA,MAAM,6BAA6BP,GAAG,2B,EAG3C,eAAuG,OAAlGO,MAAM,yBAAuB,CAAC,eAA8D,SAAvDP,GAAG,kBAAkBsF,KAAK,MAAM/I,MAAM,MAAMkJ,SAAA,O,mDArB1F,eAsBM,MAtBN,EAsBM,CArBF,eAAkE,MAAlE,EAAkE,eAA5C,EAAAP,GAAE,qCACxB,eAUM,MAVN,EAUM,C,eATF,eAO2C,SAPpCI,KAAK,MACRtF,GAAG,WACHlE,KAAK,W,qDACI,EAAA+B,OAAM,IACV,SAAM,+BAAE,EAAA8H,aACbC,UAAU,KACVC,IAAI,aACJC,SAAS,WAAWC,QAAQ,W,mBAJnB,EAAAlI,UAKb,eAAoH,SAA7GmI,IAAI,WAAWlK,KAAK,eAAsByE,MAAO,EAAA0F,W,eAAc,EAAAf,GAAE,uCAE5E,eACM,OADD3E,MAAM,6BAA6BP,GAAG,iBAAiB,UAAQ,EAAAkF,GAAE,8B,qCAEtE,eAEM,OAFmB3E,MAAM,6BAA6BP,GAAG,kB,eACzD,EAAAkF,GAAE,oC,QADK,EAAAS,cAGb,eAEM,MAFN,EAEM,eADA,EAAAT,GAAE,oCAER,IAKW,OACXpJ,KAAM,aACNpC,KAAM,WACF,MAAO,CACHmE,OAAQ,KACRqI,MAAO,OAGfzB,MAAO,CAAC,QAAQ,WAChBK,QAAS,CACLa,SAAU,WACN,OAAIxG,KAAKtB,QAAgC,IAAtBsB,KAAKtB,OAAO3D,QAAgBuJ,SAAStE,KAAKtB,SAAW,YAAc4F,SAAStE,KAAKtB,SAAW,YAC3GsB,KAAK6F,MAAM,QAAS7F,KAAKtB,SAClB,IAEXsB,KAAK6F,MAAM,YACJ,KAGfmB,MAAO,CACHtI,OADG,WAECsB,KAAKwG,aAGbS,SAAU,CACNH,UAAW,WACP,MAAO,CACH,UAAgC,MAArB9G,KAAKyF,MAAMC,WCnD1C,EAAOI,OAAS,EAED,Q,ECHX,eAA0D,OAArD1E,MAAM,0CAAwC,S,EACnD,eAA0D,OAArDA,MAAM,0CAAwC,S,8GAF7B,cAAL,EAAAS,O,iBAAjB,eAAqE,G,MAAtBlD,SAAU,EAAAA,U,4CACzD,EACA,G,WCFKyC,MAAM,0B,EACP,eAA4D,QAAtDA,MAAM,QAAM,CAAC,eAAkC,OAA7BG,IAAI,2B,MAGvBH,MAAM,yB,gDAJf,eAOM,MAPN,EAOM,CANF,EACA,eAAuG,OAAlGA,MAAM,OAAO,UAAQ,EAAA2E,GAAE,mCAAuC,EAAApH,SAASuI,QAAQC,Y,sBACpF,eAA2E,OAAtE/F,MAAM,YAAY,UAAQ,EAAA2E,GAAE,mC,sBACjC,eAEM,MAFN,EAEM,CADF,eAAqI,SAA9HI,KAAK,SAAS/E,MAAM,uBAAuBgG,QAAQ,qBAA4BhK,MAAO,EAAA2I,GAAE,mC,sBAMxF,OACXpJ,KAAM,YACN0I,MAAO,CAAC,aCXhB,EAAOS,OAAS,EAED,QFII,GACXnJ,KAAM,SACN0I,MAAO,CAAC,eAAgB,QAAS,YACjCgC,WAAY,CACRC,cGVZ,EAAOxB,OAAS,EAED,Q,qBNmBA,GACXnJ,KAAM,aACN0K,WAAY,CACRE,aACAC,UAEJnC,MAAO,CAAC,YACRC,MAAO,CAAC,aAAa,aAAc,gBACnC/K,KAAM,WACF,MAAO,CACH2L,aAAc,CAAC,GACfuB,SAAU,GACVlB,cAAe,KACf7H,OAAQ,KACRmD,MAAO,aACPwE,YAAY,IAGpBV,QAAS,CACLK,SAAU,SAAUtH,GAChBsB,KAAKuG,eAAgB,EACrBvG,KAAKtB,OAASA,EACdsB,KAAKqG,YAAa,EAClBrG,KAAK6F,MAAM,aAAc7F,KAAKtB,SAElCuH,WAAY,WACRjG,KAAKuG,eAAgB,EACrBvG,KAAKtB,OAAS,MAElBgJ,iBAAkB,WACd,OAAO1H,KAAKuG,gBAGpBf,QAAS,WACLmC,QAAQC,IAAI3J,OAAO4J,iBAAiB7H,KAAK8H,MAAMC,UAC/CJ,QAAQC,IAAI5H,KAAK8H,MAAMC,QAAQzG,SOxDvC,EAAOwE,OAAS,EAED,Q,GCJL1E,MAAM,qCAAqC4G,SAAS,iB,GACjD5G,MAAM,gB,IAKeA,MAAM,gB,IACvBA,MAAM,W,IAMWA,MAAM,W,IACvBA,MAAM,Q,IACNA,MAAM,a,IAeVP,GAAG,kB,GAE4C,eAAM,mB,GAClD,eAA+C,OAA1CA,GAAG,cAAcoH,MAAM,UAAU1G,IAAI,I,aAGpCD,MAAA,e,GACN,eAA8D,SAAvDT,GAAG,UAAUqH,YAAY,gBAAgB/B,KAAK,Q,aAIxB/E,MAAM,0B,IACeA,MAAM,Q,IAEvDA,MAAM,a,IACNA,MAAM,yB,qBAY+CA,MAAM,gB,GACxD,eAKM,OALDA,MAAM,yCAAuC,CAC9C,eAGM,OAHDA,MAAM,SAAO,CACd,eAA0D,SAAnD+E,KAAK,OAAOQ,SAAS,WAAWC,QAAQ,YAC/C,eAAe,a,kBAMdxF,MAAM,iB,GAAgD,eAA+B,QAAzBA,MAAM,YAAW,KAAC,G,IAC9EA,MAAM,gB,IAYIyF,IAAI,+B,IA6B9BxF,KAAK,kB,IAULD,MAAM,uB,IAIFP,GAAG,sBAAsBO,MAAM,mB,kFA3H5C,eA+HO,OA/HP,EA+HO,CA9HH,eAIM,MAJN,EAIM,C,eAHF,eAEM,OAFiBA,MAAM,YAAa,QAAK,+BAAE,EAAAyE,MAAK,8B,CAClD,eAA2G,OAA/FoC,MAAO,EAAAlC,GAAE,oBAA6BxE,IAAK,EAAA5C,SAASC,UAAU4C,UAAS,mB,uCAD1E,EAAA2G,c,eAIjB,eAMM,MANN,GAMM,CALF,eAEM,MAFN,GAEM,CADF,eAAgG,OAApF5G,IAAK,EAAA5C,SAASC,UAAU4C,UAAS,0BAA8BF,MAAA,gB,kBAE/E,eACM,OADDF,MAAM,YAAY,UAAQ,EAAA2E,GAAE,4CAAiD,EAAApH,SAASC,UAAU4C,a,qCAJ5F,EAAA4G,e,eAOb,eAGM,MAHN,GAGM,CAFF,eAAyD,MAAzD,GAAyD,eAApC,EAAArC,GAAE,6BACvB,eAAkE,MAAlE,GAAkE,eAAxC,EAAAA,GAAE,kC,cAFnB,EAAAsC,e,eAIb,eAA2F,GAAvD,QAAK,+BAAE,EAAAC,SAAStG,KAAc,UAAO,+BAAE,EAAAuG,gB,mBAAzD,EAAAC,e,eAClB,eAEM,OAFmBpH,MAAM,gBAAgBP,GAAG,a,eAC3C,EAAAkF,GAAE,kC,QADI,EAAA0C,c,eAYb,eASM,MATN,GASM,CARF,eAGM,YAFF,eAA4C,2BAAnC,EAAA1C,GAAE,2BAAiC,GAC5C,KAEJ,eAGM,YAFF,eAAqE,OAArE,GAAqE,eAAzC,EAAAA,GAAE,iCAC9B,M,cAPyB,EAAA2C,e,eAWjC,eAOM,MAPN,GAOM,C,eANF,eAAoJ,OAApJ,GAAoJ,CAAjF,eAA0E,OAA9DnH,IAAK,EAAA5C,SAASC,UAAU4C,UAAS,wB,+BAAlG,EAAAmH,yBAA2B,EAAAP,eACzC,eAAsF,OAAjFhH,MAAM,OAAO,UAAQ,EAAA2E,GAAE,mCAAuC,EAAAoB,Y,sBACnE,eAAuE,MAAvE,GAAuE,eAA7C,EAAApB,GAAE,sCAC5B,eAEM,MAFN,GAEM,CADF,eAAmI,SAA5HI,KAAK,SAAS/E,MAAM,uBAA8BhE,MAAO,EAAA2I,GAAE,kCAAqC,QAAK,+BAAE,EAAA6C,qB,mCALzG,EAAAD,0BASF,EAAAlB,SAAS1M,OAAM,GAAQ,EAAA8N,kB,iBAAlC,eAyDM,W,mBAxDF,eAuDM,2BArDiB,EAAApB,UAAQ,SAApBqB,G,wBAFX,eAuDM,OAtDF1H,MAAK,CAAC,kCAAiC,UAII,IAAjB0H,EAASC,SAAQ,SAAmC,IAAjBD,EAASC,SAAQ,SAAmC,IAAjBD,EAASC,WAFlGrL,IAAKoL,EAASjI,GAAE,KAAMiI,EAASE,QAC/BF,SAAUA,G,SAEElJ,QAAQkJ,EAASG,aAAU,G,iBAA9C,eAOM,MAPN,GAOM,CANF,M,4BAOarJ,QAAQkJ,EAASG,aAAU,G,iBAA5C,eAsBM,U,eArBF,eAoBM,OApBD7H,MAAK,CAAC,kBAAiB,WAA0B0H,EAASjI,GAAE,IAAKiI,EAASE,U,CAC3E,eAAgG,MAAhG,GAAgG,C,8BAAlEF,EAASA,SAASI,UAAQ,GAAG,KAC3D,eAgBM,MAhBN,GAgBM,E,mBAfF,eAcI,2BAZiBJ,EAASK,SAAO,SAA1BC,G,wCAFX,eAcI,KAXO1L,IAAKoL,EAASjI,GAAE,KAAMiI,EAASE,QAAO,KAAMI,EAAOvI,GAC1DO,MAAM,qDACL,QAAK,mBAAE,EAAAiI,KAAKP,EAASQ,MAAMR,EAASjI,GAAGuI,EAAOvI,GAAGiI,EAASG,c,CAC3D,eAI8E,SAH1E7H,MAAA,GACA+E,KAAK,WACEoD,QAASH,EAAOvI,IAAMiI,EAASU,OACrC,QAAK,mBAAE,EAAAH,KAAKP,EAASQ,MAAMR,EAASjI,GAAGuI,EAAOvI,GAAGiI,EAASG,c,8BAC/D,eAEQ,QAFR,GAEQ,CADOG,EAAOK,O,iBAAlB,eAAkG,O,MAAzErI,MAAM,UAAiBG,IAAK,EAAA5C,SAASC,UAAU4C,UAAY4H,EAAOK,O,qEAAUL,EAAOM,MAAI,M,0BAX3GN,EAAOO,W,uBAexB,eAAuM,SAArKvI,MAAM,gBAAgBP,GAAG,oDAAoDsF,KAAK,SAAU,QAAK,mBAAE,EAAAyD,KAAKd,EAASQ,MAAMR,EAASjI,GAAGiI,EAASE,UAAU5L,MAAM,Q,6BAA/K0L,EAASe,a,aAnBqEf,EAASa,W,0EA2CtH,eAGM,MAHN,GAGM,C,eAFF,eAAwK,SAAzIxD,KAAK,SAAStF,GAAG,UAAUuF,IAAI,UAAUhF,MAAM,uBAA8BhE,MAAO,EAAA2I,GAAE,uBAA0B,QAAK,+BAAE,EAAA+D,SAAO,M,2BAA9I,EAAAC,kB,eACf,eAAwK,SAAzI5D,KAAK,SAAStF,GAAG,UAAUuF,IAAI,UAAUhF,MAAM,uBAA8BhE,MAAO,EAAA2I,GAAE,uBAA0B,QAAK,+BAAE,EAAAiE,SAAO,M,2BAA9I,EAAAC,oB,eAQnB,eAOM,MAPN,GAOM,CANF,eAEM,OAFD7I,MAAM,YAAa,QAAK,+BAAE,EAAAyE,MAAK,8B,CAChC,eAA2C,2BAAlC,EAAAE,GAAE,4BAEf,eAEM,MAFN,GAEM,CADF,eAAsJ,SAA/II,KAAK,SAAStF,GAAG,aAAaO,MAAM,uBAA8BhE,MAAO,EAAA2I,GAAE,sBAAyB,QAAK,+BAAE,EAAAF,MAAK,aAAe,EAAArH,gB,mCALrG,EAAA0L,uB,sCCvHxCrJ,GAAG,mBAAmBO,MAAM,Y,IACxBA,MAAM,a,IACNA,MAAM,QAAQP,GAAG,a,iDAF1B,eAQM,MARN,GAQM,CAPF,eAA2E,MAA3E,GAA2E,eAAjD,EAAAkF,GAAE,0CAC5B,eAKM,MALN,GAKM,C,eAJF,eAAmF,SAA5EI,KAAK,SAASgE,IAAI,IAAIzD,IAAI,IAAIN,IAAI,SAASK,UAAU,I,qDAAa,EAAA2D,OAAM,K,mBAAN,EAAAA,U,eACzE,eAAmF,SAA5EjE,KAAK,SAASgE,IAAI,IAAIzD,IAAI,IAAIN,IAAI,SAASK,UAAU,I,qDAAa,EAAA4D,OAAM,K,mBAAN,EAAAA,U,eACzE,eAAmF,SAA5ElE,KAAK,SAASgE,IAAI,IAAIzD,IAAI,IAAIN,IAAI,SAASK,UAAU,I,qDAAa,EAAA6D,OAAM,K,mBAAN,EAAAA,U,eACzE,eAAmF,SAA5EnE,KAAK,SAASgE,IAAI,IAAIzD,IAAI,IAAIN,IAAI,SAASK,UAAU,I,qDAAa,EAAA8D,OAAM,K,mBAAN,EAAAA,cAMlE,QACX5N,KAAM,WACN2I,MAAO,CAAC,QAAQ,WAChB/K,KAAM,WACF,MAAO,CACHwM,MAAO,CAAC,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,KACrDqD,OAAQ,GACRC,OAAQ,GACRC,OAAQ,GACRC,OAAQ,GACRC,IAAK,KAIbhF,QAAS,WACLxF,KAAK8H,MAAMsC,OAAOK,SAEtB9E,QAAS,CACLa,SAAU,WACNxG,KAAKwK,IAAMxK,KAAKoK,OAAOpK,KAAKqK,OAAOrK,KAAKsK,OAAOtK,KAAKuK,OAC7B,GAAnBvK,KAAKwK,IAAIzP,QAAeuJ,SAAStE,KAAKwK,KAAO,KAAOlG,SAAStE,KAAKwK,KAAO,IACzExK,KAAK6F,MAAM,QAAS7F,KAAKwK,KAEzBxK,KAAK6F,MAAM,aAIvBmB,MAAO,CACHoD,OADG,WAEKpK,KAAKoK,OAAOrP,OAAS,IACrBiF,KAAKoK,OAASpK,KAAKoK,OAAOlH,UAAU,EAAG,IACjB,GAAtBlD,KAAKoK,OAAOrP,SACRiF,KAAK+G,MAAMnH,QAAQI,KAAKoK,QAAU,EAClCpK,KAAKoK,OAAS,GAEdpK,KAAK8H,MAAMuC,OAAOK,UAC1B1K,KAAKwG,YAET6D,OAXG,WAYKrK,KAAKqK,OAAOtP,OAAS,IACrBiF,KAAKqK,OAASrK,KAAKqK,OAAOnH,UAAU,EAAG,IACjB,GAAtBlD,KAAKqK,OAAOtP,SACRiF,KAAK+G,MAAMnH,QAAQI,KAAKqK,QAAU,EAClCrK,KAAKqK,OAAS,GAEdrK,KAAK8H,MAAMwC,OAAOI,UACA,GAAtB1K,KAAKqK,OAAOtP,QACZiF,KAAK8H,MAAMsC,OAAOM,SACtB1K,KAAKwG,YAET8D,OAvBG,WAwBKtK,KAAKsK,OAAOvP,OAAS,IACrBiF,KAAKsK,OAAStK,KAAKsK,OAAOpH,UAAU,EAAG,IACjB,GAAtBlD,KAAKsK,OAAOvP,SACRiF,KAAK+G,MAAMnH,QAAQI,KAAKsK,QAAU,EAClCtK,KAAKsK,OAAS,GAEdtK,KAAK8H,MAAMyC,OAAOG,UACA,GAAtB1K,KAAKsK,OAAOvP,QACZiF,KAAK8H,MAAMuC,OAAOK,SACtB1K,KAAKwG,YAET+D,OAnCG,WAoCKvK,KAAKuK,OAAOxP,OAAS,IACrBiF,KAAKuK,OAASvK,KAAKuK,OAAOrH,UAAU,EAAG,IACjB,GAAtBlD,KAAKuK,OAAOxP,QACRiF,KAAK+G,MAAMnH,QAAQI,KAAKuK,QAAU,IAClCvK,KAAKuK,OAAS,IACI,GAAtBvK,KAAKuK,OAAOxP,QACZiF,KAAK8H,MAAMwC,OAAOI,SACtB1K,KAAKwG,cChFrB,GAAOV,OAAS,GAED,UFoIA,IACXuB,WAAY,CACRsD,aAEJtF,MAAO,CAAC,YACRC,MAAO,CAAC,aAAc,aAAc,qBAAsB,eAC1DE,QAAS,WACLxF,KAAK4K,WAETrQ,KAAM,WACF,MAAO,CACHsQ,cAAe,CAAC,EAAE,GAClBC,MAAO,EACPC,IAAK,KACLvC,aAAa,EACb1G,iBAAkB,KAClB6G,wBAAwB,EACxBD,aAAa,EACbqB,gBAAgB,EAChBE,gBAAgB,EAChBxB,YAAY,EACZuC,eAAe,EACf5C,aAAa,EACb8B,mBAAmB,EACnBe,eAAe,EACfxD,SAAU,GACVoB,kBAAkB,EAClBV,UAAU,EACVE,aAAa,EACb6C,oBAAqB,GACrBzK,YAAa,KACb0K,WAAY,GACZhE,SAAU,GACViE,SAAU,KAGlBzF,QAAS,CACLiF,QAAS,WACD5K,KAAKrB,SAASC,UAAUC,aAAemB,KAAKrB,SAASuI,QAAQkB,aAAoD,QAArCpI,KAAKrB,SAASuI,QAAQkB,cAClGpI,KAAKqL,cACTrL,KAAKsL,eACLtL,KAAKuL,mBACLvL,KAAK6F,MAAM,eAAe,GAC1B8B,QAAQC,IAAI3J,OAAO4J,iBAAiB7H,KAAK8H,MAAMkC,UAC/CrC,QAAQC,IAAI3J,OAAO4J,iBAAiB7H,KAAK8H,MAAMgC,WAEnDuB,YAAa,WAKTrL,KAAKwI,aAAc,EACnBxI,KAAKkK,mBAAoB,EACzBlK,KAAKmI,UAAW,EAChBnI,KAAKqI,aAAc,EACnBrI,KAAKoI,aAAc,EACfpI,KAAKyH,SAAS1M,OAAS,IACvBiF,KAAK2I,wBAAyB,EAC9B3I,KAAKiK,gBAAiB,EACtBjK,KAAK+J,gBAAiB,EACtB/J,KAAKwI,aAAc,EACnBgD,YAAW,WAAcxL,KAAK4I,oBAAsB,OAG5DN,SAAU,SAASkC,GACfxK,KAAKyI,YAAa,EAClBzI,KAAK+K,IAAMP,GAEfjC,WAAY,WACRvI,KAAK+K,IAAM,MAEff,QAAS,WACLhK,KAAK6F,MAAM,eAAe,GAC1B,IAAI4F,EAAQzL,KACR0L,EAAU,SAAU7G,GA6BpB4G,EAAM5F,MAAM,eAAe,IAE3B8F,EAAU,SAAU9G,GAKpB4G,EAAM5F,MAAM,eAAe,GAC3B4F,EAAMG,SAAS/G,EAAStK,KAAKA,KAAKkG,cAGlCoL,EAAW,IAAMrN,UAAUwB,MACrBA,KAAK+K,IAEX/K,KAAKrB,SAASuI,QAAQkB,cACtBpI,KAAKtB,OAAS,aACdsB,KAAK+K,IAAM,QAcf,IAAgC5B,EAAU,GAAI2C,EAAS,GAIlD9L,KAAK+L,eAAe,IAChB/L,KAAK+L,eAAe,IACrB,EAAMC,KAAK,sCAAsC,EAAOlJ,UAAU,sBAAuB,CACrFpE,OAAQmN,EACRI,WAAY9C,EAAQ8C,WACpBC,MAAOJ,EAAOK,IACdC,SAAUN,EAAOhB,MACjBuB,UAAWP,EAAOO,UAClB7B,IAAKxK,KAAK+K,IACVpO,KAAMwM,EAAQxM,KACd2P,eAAgBnD,EAAQmD,gBAAkB,QAC1CC,QAASpD,EAAQoD,QACjBC,UAAWxM,KAAKyH,SAChBjH,QAA6B,OAApB2I,EAAQ3I,SAA0B2I,EAAQ3I,UAEtDiM,MAAK,SAAC5H,GACH8G,EAAQ9G,MAEX6H,OAAM,SAAC3H,GACJ2G,EAAQ3G,OAGxB+E,QAAS,WACL9J,KAAK6F,MAAM,eAAe,GAC1B,IAAI4F,EAAQzL,KACR2L,EAAU,WACVF,EAAM9C,wBAAyB,EAC/B8C,EAAMjD,aAAc,EACpBiD,EAAM1B,gBAAiB,EACvB0B,EAAMxB,gBAAiB,EACvBwB,EAAMpD,aAAc,EACpBoD,EAAMvB,mBAAoB,EAC1BsB,YAAW,WAAcC,EAAM7C,oBAAsB,KACrD6C,EAAM5F,MAAM,eAAe,IAE3B6F,EAAU,WAA2B,IAAjBiB,EAAiB,wDACrClB,EAAM9C,wBAAyB,EAC/B8C,EAAMjD,aAAc,EAEhBmE,IACAlB,EAAMhD,YAAa,EACnB+C,YAAW,WAAcC,EAAMhD,YAAY,IAAU,MAEzDgD,EAAM5F,MAAM,eAAe,IAI1B7F,KAAK+K,KACNpD,QAAQ+E,MAAM,kCAElB,EAAMV,KAAK,qCAAqC,EAAOlJ,UAAU,sBAAuB,CACpFpE,OAAQ,IAAMF,UAAUwB,MACxBwK,IAAKxK,KAAK+K,IACVpO,KAAM,GACN2P,eAAgB,QAChBC,QAAS,KAEZE,MAAK,SAAC5H,GACH,GAAIA,EAAStK,MAAgC,GAAxBsK,EAAStK,KAAKyK,OAC/B2G,QACG,CACH,IAAIgB,GAAU,EACdjB,EAAQiB,OAGfD,OAAM,SAAC3H,GACJ2G,QAGRJ,aAAc,WAAY,WAClBG,EAAQzL,KACZA,KAAK8B,iBAAmB,EAAOgB,UAAU,sBAEzC,IAAIvI,EAAO,CACPsQ,cAAe7K,KAAK6K,cACpBnM,OAAQ,IAAMF,UAAUwB,MAAM4M,WAC9BR,SAAUpM,KAAKrB,SAASC,UAAUkM,OAEtC,EAAMkB,KAAK,yCAAyChM,KAAK8B,iBAAkBvH,GAC1EkS,MAAM,SAAA5H,GACH,IAAI2H,EAAY,GAEhB,GADAA,EAAY3H,EAAStK,KAAKA,KACtBiS,EAAUzR,OAAS,GAAKyR,EAAU,GAIlC,GAHA,EAAKvB,eAAgB,EACrB,EAAKlB,gBAAiB,EACtB,EAAKE,gBAAiB,EACgB,IAAlC,EAAKY,cAAcjL,QAAQ,GAE3B,IAAK,IAAI/E,EAAI,EAAGA,EAAI2R,EAAUzR,OAAQF,IAAK,CAKvC,GAJA2R,EAAU3R,GAAGmO,QAAU,EACS,IAA5BwD,EAAU3R,GAAGoO,YAAoBuD,EAAU3R,GAAGgS,aAC9CL,EAAU3R,GAAGsO,QAAU,EAAO1F,QAAQ+I,EAAU3R,GAAGsO,UAEvB,IAA5BqD,EAAU3R,GAAGoO,YAAoBuD,EAAU3R,GAAGiS,cAC9CN,EAAU3R,GAAGmO,QAAUwD,EAAU3R,GAAGsO,QAAQpO,OAAS,EAGjDyR,EAAU3R,GAAGsO,QAAQpO,OAAS,GAE9B,IADA,IACS8B,EAAI,EAAGA,GAAK2P,EAAU3R,GAAGsO,QAAQpO,OAAQ8B,IAAK,CACnD,IAAIkQ,EAAOP,EAAU3R,GACrBkS,EAAK/D,QAAU+D,EAAK5D,QAAQpO,OAAS8B,EACrC2P,EAAYA,EAAUvQ,OAAOpB,EAAE,EAAG,EAAGkS,GAIjD,EAAKtF,SAASpM,KAAKmR,EAAU3R,IAC7B,EAAKmS,gBAAgBR,EAAU3R,GAAIA,EAAG2R,EAAUzR,aAGhDyR,EAAUzR,QAIN,EAAK4D,SAASC,UAAUC,aAAqD,IAAtC,EAAKF,SAASuI,QAAQkB,aAA8D,SAAtC,EAAKzJ,SAASuI,QAAQkB,YAQ3G,EAAK2B,gBAAiB,GAPtB,EAAKpB,wBAAyB,EAC9B,EAAKH,aAAc,EACnB,EAAKuB,gBAAiB,EACtB,EAAKE,gBAAiB,EAEtBuB,YAAW,WAAcC,EAAM7C,oBAAsB,OAKzD,EAAKqB,gBAAiB,GAClB,EAAKtL,SAASC,UAAUC,aAAqD,IAAtC,EAAKF,SAASuI,QAAQkB,aAA8D,SAAtC,EAAKzJ,SAASuI,QAAQkB,aAE3G,EAAK4B,SAAQ,IAGrB,EAAKa,cAAcoC,SAAQ,SAAUlE,GAIjC,IAHA,IAAImE,EAAOV,EAAUW,QAAO,SAAUC,GAClC,OAAOA,GAAKA,EAAErE,WAAaA,KAEtBlO,EAAI,EAAGA,EAAIqS,EAAKnS,OAAQF,IAAK,CAE9BA,IAAOqS,EAAKnS,OAAS,IACf,EACVmS,EAAKrS,GAAGmO,QAAU,EACdkE,EAAKrS,GAAGgS,aACRK,EAAKrS,GAAGsO,QAAU,EAAO1F,QAAQyJ,EAAKrS,GAAGsO,UAE7C+D,EAAKrS,GAAGmO,QAAU,EAClB,IAAIqE,EAAQH,EAAKrS,GAAGsO,QAAQpO,OAC5B,GAAImS,EAAKrS,GAAGiS,YAIR,GAHAI,EAAKrS,GAAGmO,QAAUqE,EAAQ,EAGtBH,EAAKrS,GAAGsO,QAAQpO,OAAS,EACzB,IAAK,IAAI8B,EAAI,EAAGA,GAAKwQ,EAAOxQ,IACxBqQ,EAAKrS,GAAGmO,QAAUqE,EAAQxQ,EAC1B4O,EAAMhE,SAASpM,KAAK,CAChBsO,MAAM,EACN9I,GAAIqM,EAAKrS,GAAGgG,GACZyM,WAAY7B,EAAM9M,SAASC,UAAU2O,WAAW,IAAM,EACtDvE,QAASkE,EAAKrS,GAAGmO,QACjBG,QAAS+D,EAAKrS,GAAGsO,QACjBqE,iBAAkB,GAClBhE,OAAQ,KACRiE,aAAc,GACdZ,WAAYK,EAAKrS,GAAGgS,WACpBC,YAAaI,EAAKrS,GAAGiS,YACrB7D,WAAYiE,EAAKrS,GAAGoO,WACpBH,SAAUoE,EAAKrS,GACfyO,MAAOmC,EAAMhE,SAAS1M,OACtB2S,cAAeL,EAAO,EACtBxD,UAAU,SAKlB4B,EAAMhE,SAASpM,KAAK,CAChBsO,MAAM,EACN9I,GAAIqM,EAAKrS,GAAGgG,GACZyM,WAAY7B,EAAM9M,SAASC,UAAU2O,WAAW,IAAM,EACtDvE,QAASkE,EAAKrS,GAAGmO,QACjBG,QAAS+D,EAAKrS,GAAGsO,QACjBqE,iBAAkB,GAClBhE,OAAQ,KACRiE,aAAc,GACdZ,WAAYK,EAAKrS,GAAGgS,WACpBC,YAAaI,EAAKrS,GAAGiS,YACrB/D,SAAUmE,EAAKrS,GAAGkO,SAClBE,WAAYiE,EAAKrS,GAAGoO,WACpBH,SAAUoE,EAAKrS,GACfyO,MAAOmC,EAAMhE,SAAS1M,OACtB2S,cAAe,EACf7D,UAAU,SAIlB4B,EAAMhE,SAASpM,KAAK,CAChBsO,MAAM,EACN9I,GAAIqM,EAAKrS,GAAGgG,GACZyM,WAAY7B,EAAM9M,SAASC,UAAU2O,WAAW,IAAM,EACtDvE,QAASkE,EAAKrS,GAAGmO,QACjBG,QAAS+D,EAAKrS,GAAGsO,QACjBqE,iBAAkB,GAClBhE,OAAQ,KACRiE,aAAc,GACdZ,WAAYK,EAAKrS,GAAGgS,WACpBC,YAAaI,EAAKrS,GAAGiS,YACrB/D,SAAUmE,EAAKrS,GAAGkO,SAClBE,WAAYiE,EAAKrS,GAAGoO,WACpBH,SAAUoE,EAAKrS,GACfyO,MAAOmC,EAAMhE,SAAS1M,OACtB2S,cAAe,EACf7D,UAAU,IAItB,IAAK,IAAItN,EAAI,EAAGA,EAAIkP,EAAMhE,SAAS1M,OAAQwB,IACvCkP,EAAMuB,gBAAgBvB,EAAMhE,SAASlL,GAAGuM,SAAUvM,EAAGkP,EAAMhE,SAASlL,GAAGmR,uBAKnF,EAAK3D,gBAAiB,EACtB,EAAKE,gBAAiB,KAE3ByC,OAAM,SAAC3H,GACN,EAAKgF,gBAAiB,EACtB,EAAKE,gBAAiB,MAG9BrB,gBAAiB,WACb,IAAI6C,EAAQzL,KACiB,GAAzBA,KAAK6I,mBACL7I,KAAK6I,kBAAmB,EACxB7I,KAAK2I,wBAAyB,EAC9B3I,KAAKqI,aAAc,EACnBrI,KAAKkK,mBAAoB,EACzBlK,KAAKyH,SAAS,GAAGkC,MAAO,EACpB3J,KAAKyH,UAAYzH,KAAKyH,SAAS1M,OAAS,IACxCiF,KAAKiK,gBAAiB,GACtBjK,KAAKyH,UAAYzH,KAAKyH,SAAS1M,SAC/BiF,KAAKyH,SAAS,GAAGkG,YAAcnL,KAAKoL,MACpC5N,KAAKyH,SAAS,GAAGgG,aAAe,GAChCzN,KAAKyH,SAAS,GAAG0B,QAAQ8D,SAAQ,SAAU7D,EAAQE,GAC3CmC,EAAMhE,SAAS,GAAGqF,aAAexD,GAAS,GAC1CmC,EAAMhE,SAAS,GAAG0B,QAAQG,GAAOK,MAAO,EACxC8B,EAAMhE,SAAS,GAAG+F,iBAAiBnS,KAAK+N,EAAOvI,KAE/C4K,EAAMhE,SAAS,GAAGgG,aAAapS,KAAK+N,EAAOvI,OAGnDb,KAAKmI,UAAW,EAEY,GAAxBnI,KAAKyH,SAAS1M,SAEdiF,KAAKyH,SAAS,GAAGkC,MAAO,KAG5B3J,KAAKrB,SAASC,UAAUF,SAAiD,IAAtCsB,KAAKrB,SAASuI,QAAQkB,aAA8D,SAAtCpI,KAAKrB,SAASuI,QAAQkB,cACvGpI,KAAK6N,iBAAkB,GAE3B7N,KAAKyH,SAAS,GAAGkC,MAAO,IAGhCN,KAAM,SAAUyE,EAAWhF,EAAUM,EAAQH,QACM8E,IAA3C/N,KAAKyH,SAASqG,GAAWE,gBACzBhO,KAAKyH,SAASqG,GAAWE,cAAgBxL,KAAKoL,OACtB,GAAxBtJ,SAAS2E,KAcVjJ,KAAKyH,SAASqG,GAAWtE,OAASJ,IAGzCQ,KAAM,SAAUkE,EAAWG,EAAYjF,GAA0B,IACzDyC,EAAQzL,KACRkO,EAAOJ,EAAY,EAAGT,EAAQ,EAC9Ba,GAAQlO,KAAKyH,SAAS1M,QAA8C,OAApCiF,KAAKyH,SAASqG,GAAWtE,aAAuDuE,IAApC/N,KAAKyH,SAASqG,GAAWtE,SAGrGxJ,KAAKyH,SAASyG,GAAMpB,YAChB9M,KAAKyH,SAASqG,GAAWjN,KAAOb,KAAKyH,SAASyG,GAAMrN,IAEpDb,KAAKyH,SAASyG,GAAM/E,QAAQ8D,SAAQ,SAAU7D,EAAQE,GAClDmC,EAAMhE,SAASyG,GAAM/E,QAAQG,GAAOK,MAAO,KAG/C3J,KAAKyH,SAASqG,GAAWN,iBAAiBnS,KAAK2E,KAAKyH,SAASqG,GAAWtE,QACxExJ,KAAKyH,SAASyG,GAAMT,aAAe,GACnCzN,KAAKyH,SAASyG,GAAM/E,QAAQ8D,SAAS,SAAU7D,EAAQE,GAC/C+D,EAAQ,GACJ5B,EAAMhE,SAASqG,GAAWN,iBAAiB5N,QAAQwJ,EAAOvI,KAAO,GACjE4K,EAAMhE,SAASyG,GAAM/E,QAAQG,GAAOK,MAAO,EAC3C8B,EAAMhE,SAASyG,GAAMT,aAAapS,KAAK+N,EAAOvI,IAC9CwM,GAAgB,GAKpB5B,EAAMhE,SAASyG,GAAMV,iBAAiBnS,KAAK+N,EAAOvI,SAI1Db,KAAKyH,SAASyG,GAAMT,aAAe,GACnCzN,KAAKyH,SAASyG,GAAM/E,QAAQ8D,SAAQ,SAAU7D,EAAQE,GAC9C+D,EAAQ,GACR5B,EAAMhE,SAASyG,GAAM/E,QAAQG,GAAOK,MAAO,EAC3C8B,EAAMhE,SAASyG,GAAMT,aAAapS,KAAK+N,EAAOvI,IAC9CwM,GAAgB,GAEhB5B,EAAMhE,SAASyG,GAAMV,iBAAiBnS,KAAK+N,EAAOvI,SAK9Db,KAAKyH,SAASyG,GAAMV,iBAAmB,GACvCxN,KAAKyH,SAASyG,GAAMT,aAAe,GACnCzN,KAAKyH,SAASyG,GAAM/E,QAAQ8D,SAAQ,SAAU7D,EAAQE,GAClDmC,EAAMhE,SAASyG,GAAMT,aAAapS,KAAK+N,EAAOvI,QAGtDb,KAAKyH,SAASqG,GAAWnE,MAAO,EAChC3J,KAAKyH,SAASyG,GAAMvE,MAAO,EACvBuE,GAASlO,KAAKyH,SAAS1M,OAAS,IAChCiF,KAAKiK,gBAAiB,GAE1BjK,KAAKyH,SAASyG,GAAMP,YAAcnL,KAAKoL,QAG3CZ,gBAAiB,SAAUlE,EAAUqF,EAAsBC,GACvD,OAAQtF,EAASG,YACb,KAAK,EACL,KAAK,EACL,KAAK,EAED,MAUJ,KAAK,EACL,KAAK,EAID,GAHAH,EAASK,QAAQ8D,SAAQ,SAAU7D,EAAQE,GACvCR,EAASK,QAAQG,GAAO+E,aAAe/E,EAAQ,KAE/CR,EAASE,QAAU,EACnBhJ,KAAKyH,SAAS0G,GAAsBtE,UAAW,MAC5C,CACH,IAAIqE,EAAOC,EAAuB,EAC9BD,EAAOlO,KAAKyH,SAAS1M,SACrBiF,KAAKyH,SAAS0G,GAAsBtE,UAAW,GAEvD,MAEJ,KAAK,EAOD,QAGZkC,eAAgB,SAAU7F,GACtB,IAAIoI,GAAY,EAChB,GAAItO,KAAKyH,SAAS1M,OAAQ,CACtB,IAAK,IAAIF,EAAI,EAAGA,EAAImF,KAAKyH,SAAS1M,OAAQF,IAAK,CAC3C,OAAQmF,KAAKyH,SAAS5M,GAAGoO,YACrB,KAAK,EACIjJ,KAAKyH,SAAS5M,GAAG2O,QAAqC,GAA3BxJ,KAAKyH,SAAS5M,GAAG2O,OAGzCxJ,KAAKyH,SAAS5M,GAAG4S,aACjBzN,KAAKyH,SAAS5M,GAAGwT,aAAerO,KAAKyH,SAAS5M,GAAG4S,aAAa7N,QAAQI,KAAKyH,SAAS5M,GAAG2O,QAAU,EAEjGxJ,KAAKyH,SAAS5M,GAAGwT,aAAe,EALpCC,GAAY,EAOhB,MACJ,KAAK,EACL,KAAK,EACL,KAAK,EACItO,KAAKyH,SAAS5M,GAAG2O,QAAqC,GAA3BxJ,KAAKyH,SAAS5M,GAAG2O,SAC7C8E,GAAY,GACZtO,KAAKyH,SAAS5M,GAAG4S,aACjBzN,KAAKyH,SAAS5M,GAAGwT,aAAerO,KAAKyH,SAAS5M,GAAG4S,aAAa7N,QAAQI,KAAKyH,SAAS5M,GAAG2O,QAAU,EAEjGxJ,KAAKyH,SAAS5M,GAAGwT,aAAe,EACe,UAA/CrO,KAAKyH,SAAS5M,GAAG0T,YAAY1O,gBAE7ByO,GAAY,GAIhBtO,KAAKyH,SAAS5M,GAAG2O,OAASxJ,KAAKyH,SAAS5M,GAAG2O,OAAOgF,OAClD,MACJ,KAAK,EACGxO,KAAKyH,SAAS5M,GAAG4S,aACjBzN,KAAKyH,SAAS5M,GAAGwT,aAAerO,KAAKyH,SAAS5M,GAAG4S,aAAa7N,QAAQI,KAAKyH,SAAS5M,GAAG2O,QAAU,EAEjGxJ,KAAKyH,SAAS5M,GAAGwT,aAAe,EACpC,MACJ,KAAK,EACD,IAAK,IAAItS,EAAI,EAAGA,EAAIiE,KAAKyH,SAAS5M,GAAGsO,QAAQpO,OAAQgB,IAI7CiE,KAAKyH,SAAS5M,GAAGmT,cAAgBhO,KAAKkL,oBAAoBlL,KAAKyH,SAAS5M,GAAGgG,IAC3Eb,KAAKyH,SAAS5M,GAAGsO,QAAQpN,GAAG0S,YAAa,EAEzCH,GAAY,EAGpB,MAER,GAAIA,EAAW,OAAOA,EAE1B,OAAOA,IAGf1C,SAAU,SAAUnL,EAAarB,GAC7BnB,OAAOwC,YAAcA,EACrBT,KAAK6F,MAAM,aAAa,qBAE5BrH,UAAW,WACP,OAAO,IAAMA,UAAUwB,OAE3BuL,iBAAkB,WAAY,WACtBvL,KAAKxB,aAAewB,KAAKxB,YAAYzD,OAAS,GAC9C,EAAMiR,KAAK,8CAA8C,EAAOlJ,UAAU,uBACzE2J,MAAK,SAAC5H,GACH,GAAIA,EAAStK,KAAKA,KAAM,CACpB,IAAImU,EAAO7J,EAAStK,KAAKA,KACzB,GAAImU,EAAKC,IACL,EAAKxH,SAAW,GAChB,EAAKiE,SAAW,OACb,CACyB,IAAIjE,EAAYuH,EAAKvH,UAAYuH,EAAKvH,UAAY,EAAKuH,EAAKvH,SAAW,EAC/FiE,EAAWsD,EAAKtD,SAEhBsD,EAAKvH,UAAY,GAAKA,GAAY,GAClCA,GAAY,WAEZA,EAAWyH,EAAO3K,aAAakD,EAAW,YAAc,MAEnDA,GAAY,QAEjBA,EAAWyH,EAAO3K,aAAakD,EAAW,SAAW,MAEhDA,GAAY,KAEjBA,EAAWyH,EAAO3K,aAAakD,EAAW,MAAQ,MAC9B,IAAbA,IACPA,EAAW,IAEX,EAAKA,SAAWA,GAGhB,EAAKA,SAAW,GAChBiE,GAAYA,GAAY,IACxBA,EAAWwD,EAAOzK,aAAaiH,GAE3B,EAAKA,SADO,GAAZA,EACgB,GAEAA,GAG5B,EAAKvF,MAAM,qBAAsB,CAAEsB,SAAU,EAAKA,SAAUiE,SAAU,EAAKA,eAGlFsB,OAAM,SAAC3H,GACJ,EAAKoC,SAAW,GAChB,EAAKiE,SAAW,SGlvBpC,GAAOtF,OAAS,GAED,U,ICJL1E,MAAM,2C,IACHA,MAAM,a,IACFP,GAAG,Y,GAQR,eAAM,mB,IACDO,MAAM,a,UAIVA,MAAM,U,GACP,eAA6B,OAAxBA,MAAM,aAAW,S,iDAhB9B,eAmBO,OAnBP,GAmBO,CAlBH,eAaM,MAbN,GAaM,CAZF,eAGM,MAHN,GAGM,CAFF,eAA6H,OAAjHG,IAAK,EAAA5C,SAASC,UAAU4C,UAAS,2BAA+BF,MAAA,gD,gBAC5E,eAAqD,2BAA5C,EAAAyE,GAAE,sCAEf,eACM,OADD3E,MAAM,cAAc,UAAQ,EAAA2E,GAAE,qCAAyC,EAAA5D,kBAAkBgF,Y,qCAE9F,eAAgK,OAA5G/F,MAAM,cAAc,UAAQ,EAAA2E,GAAE,qCAAyC,EAAA5D,kBAAkBiJ,Y,+BAAhI,EAAAjJ,kBAAkBiJ,SAASrQ,OAAM,K,eAC9C,eAA2I,OAAtFqG,MAAM,cAAc,UAAQ,EAAA2E,GAAE,6C,+BAArC,GAAjC,EAAA5D,kBAAkBiJ,SAASrQ,UACxC,GACA,eAEM,MAFN,GAEM,CADF,eAA4D,0BAApD,EAAAgL,GAAE,iDAGQ,EAAAoD,SAAW,EAAAA,QAAQ0F,QAAU,EAAA1F,QAAQ0F,OAAO9T,Q,iBAAtE,eAGM,MAHN,GAGM,CAFF,GACA,eAAgH,SAAzG8F,GAAG,eAAeO,MAAM,oCAAoC+E,KAAK,SAAS/I,MAAM,OAAQ,QAAK,+BAAE,EAAA0R,c,wBAWnG,QACXnS,KAAM,mBACN2I,MAAO,CAAC,aAAa,cACrBD,MAAO,CAAC,WAAY,UAAW,qBAC/BG,QAAS,WACLxF,KAAK4K,WAETrQ,KAAM,WACF,MAAO,CACH2L,aAAc,CAAC,GACfuB,SAAU,GACVlB,cAAe,KACf7H,OAAQ,KACRmD,MAAO,aACPwE,YAAY,IAGpBV,QAAS,CACLiF,QAAS,WACD5K,KAAKmJ,SAAWnJ,KAAKmJ,QAAQ0F,QAAU7O,KAAKmJ,QAAQ0F,OAAO9T,QAY3D,IAAMyF,WAIdsO,KAAM,WACF,IAAMtO,aC9DlB,GAAOsF,OAAS,GAED,U,iBCJN1E,MAAM,U,GACP,eAA8B,OAAzBA,MAAM,cAAY,S,IAElBA,MAAM,W,GAEP,eAEM,OAFDA,MAAM,aAAW,CAClB,eAAW,S,MAGf,eAEM,OAFDA,MAAM,aAAW,CAClB,eAAW,S,oDAVvB,eAeM,MAfN,GAeM,CAdF,GACA,eAA4D,QAAtD,UAAQ,EAAA2E,GAAE,oC,sBAChB,eAUM,MAVN,GAUM,CATF,eAA2E,OAAtE3E,MAAK,CAAC,OAAM,SAA+B,eAAL,EAAAS,S,QAC3C,GAGA,eAA4E,OAAvET,MAAK,CAAC,OAAM,SAA+B,gBAAL,EAAAS,S,QAC3C,GAGA,eAAmH,OAA9GT,MAAK,CAAC,OAAD,iBAAO,QAAgC,eAAL,EAAAS,OAAlC,UAAwE,qBAAL,EAAAA,S,YAO1E,QACXlF,KAAM,SACN0I,MAAO,CAAC,SACR9K,KAAM,WACF,MAAO,KCrBf,GAAOuL,OAAS,GAED,UpByCA,IACbnJ,KAAM,iBACN0K,WAAY,CACV0H,mBACAC,aACAC,eACAC,oBACAC,WAEF3J,QATa,WAUXxF,KAAKoP,eACLpP,KAAKqP,iBACLrP,KAAKsP,aAEP/U,KAAM,WACJ,MAAO,CACLsQ,cAAe,CAAC,GAChBnM,OAAQ,KACRoM,MAAO,EACPhJ,iBAAkB,KAClBD,MAAO,aACPlD,SAAU,CACR4Q,cAAe,KACfrI,QAAS,KACTtI,UAAW,MAEb4C,UAAW,KACXW,kBAAmB,CACjBgF,SAAU,GACViE,SAAU,IAEZzJ,KAAM,KACND,QAAQ,EACR8N,oBAAoB,IAGxB7J,QAAS,CACPyJ,aAAc,WACZpP,KAAK0B,QAAS,EACd1B,KAAKrB,SAAW,CACd4Q,cAAetR,OAAOsR,cACtBrI,QAASjJ,OAAOiJ,QAChBtI,UAAWX,OAAOW,WAEpBoB,KAAKrB,SAASC,UAAU4C,UAAYvD,OAAOuD,UAC3CxB,KAAK8B,iBAAmB,EAAOuB,oBAAoB,sBACnDrD,KAAKwB,UAAYvD,OAAOuD,UACpBxB,KAAK8B,kBAA6C,IAAzB9B,KAAK8B,mBAChC,EAAOM,UAAU,qBAAsBpC,KAAK8B,kBAC5C9B,KAAK8K,MAAQ9K,KAAK8B,iBAAiBkB,MAAM,KAAKhD,KAAK8B,iBAAiBkB,MAAM,KAAKjI,OAAS,GACxFiF,KAAKyP,gBACLzP,KAAK0B,QAAS,IAMlBK,UAAW,SAASrD,GAClBsB,KAAKtB,OAASA,EAEdsB,KAAKrB,SAASC,UAAUC,WAAaH,GAEvC+Q,cAAe,WACb,IAAIhE,EAAQzL,KACZ,IACEwL,YAAY,WACV,IAAI7J,EAAO1C,SAASyQ,qBAAqB,gBAAgB,GAAGC,kBAAkBC,aAAa,OAC/D,GAAxBjO,EAAK/B,QAAQ,QACf6L,EAAM9J,KAAO1C,SAASyQ,qBAAqB,gBAAgB,GAAGC,kBAAkBC,aAAa,OAE7FnE,EAAM9J,KAAO,kBAAoB1C,SAASyQ,qBAAqB,gBAAgB,GAAGC,kBAAkBC,aAAa,OAEnHjI,QAAQC,IAAI,aAAc6D,EAAM9J,QAC/B,KAEL,MAAMkO,GACJlI,QAAQC,IAAI,UAAWkI,KAAKC,UAAUF,IACtC7P,KAAK2B,KAAO3B,KAAKwB,UAAY,8BAGjCwO,iBAAkB,SAASC,GACzB,IAAK,IAAIpV,EAAE,EAAGA,EAAEoE,SAASiR,uBAAuB,wBAAwBnV,OAAOF,IAAK,CAClF,IAAIyG,EAAQrD,OAAO4J,iBAAiB5I,SAASiR,uBAAuB,wBAAwBrV,IAE5F,IAAK,IAAIsV,KAAQ7O,EACI,oBAAfA,EAAM6O,IACRC,EACiB,oBAAf9O,EAAM6O,IACRC,IAKRf,eAAgB,WACd,IAAI9U,EAAO,CAACsQ,cAAe7K,KAAK6K,cAAenM,OAAQ,IAAMF,UAAUwB,MAAOoM,SAAUpM,KAAK8K,OAC7F,EAAMkB,KAAK,yCAAyChM,KAAK8B,iBAAkBvH,GAC1EkS,MAAM,eACJC,OAAM,gBAGX4C,UAAW,SAASzN,GACd7B,KAAKrB,SAASuI,QAAQmJ,eACtBrQ,KAAKkC,UAAU,aAAc,CAAEoO,UAAW,iBAAkBC,IAAK,0BAA2BC,SAAU,iCAC/FxQ,KAAKrB,SAASuI,QAAQuJ,cAC7BzQ,KAAKkC,UAAU,aAAc,CAAEoO,UAAW,kBAAmBC,IAAK,0BAA2BC,SAAU,+BAChGxQ,KAAKrB,SAASuI,QAAQnI,eAAiB8C,IAAU7B,KAAKrB,SAAS4Q,cAAcmB,YACpC,eAA5C1Q,KAAKrB,SAASuI,QAAQnI,cAAcwR,MACpCvQ,KAAKrB,SAAS4Q,cAAcoB,iBAAmB,uBAC/C3Q,KAAKrB,SAAS4Q,cAAcqB,aAAe,mBAC3C5Q,KAAKrB,SAASuI,QAAQnI,cAAcxE,KAAK6E,aAAc,GAGpDyC,EACP7B,KAAKkC,UAAUL,GAEV7B,KAAKrB,SAASC,UAAUC,YAAoD,IAAtCmB,KAAKrB,SAASC,UAAUC,YACnEmB,KAAKtB,OAASsB,KAAKrB,SAASC,UAAUC,WACtCmB,KAAKkC,UAAU,gBAEflC,KAAKkC,UAAU,eAGrBA,UAAW,SAAS2O,GAClB7Q,KAAK6B,MAAQgP,GAEfC,mBAAoB,SAAUjM,GAE1B7E,KAAK0B,QAAS,GAElBqP,mBAAoB,SAAUlM,EAAUmM,GAChB,GAAnBnM,EAASG,QAGJH,EAAStK,KAAK0W,YAAYxQ,YAO3BuQ,GACHhR,KAAKkC,UAAU,eAEb8O,IACFhR,KAAK0B,QAAS,IAEpBO,YAAa,SAAU4J,EAAUmF,GAAQ,WACvChR,KAAK0B,QAAS,EAUd,EAAMsK,KAAK,wCAAwC,EAAOlJ,UAAU,sBAAuB,CACvFpE,OAAQmN,EACRqF,OAAQF,EAAS,SAAW,KAC5B1E,eAAgB,UAEnBG,MAAM,SAAA5H,GACCA,EAAStK,KACT,EAAKwW,mBAAmBlM,EAAStK,KAAMyW,GAEvC,EAAKF,mBAAmBjM,MAE/B6H,OAAO,SAAC3H,GACL,EAAK+L,mBAAmB/L,OAG9BoM,qBAAsB,SAAShP,GAC7BnC,KAAKmC,kBAAoBA,KqBxN/B,GAAO2D,OAAS,EAED,UtBIA,IACbnJ,KAAM,MACN0K,WAAY,CACV+J,oBuBTJ,GAAOtL,OAASA,EAED,U,aCEf,SAASuL,KACP,IAAMC,EAAUC,UAKVC,EAAW,GAQjB,OAPAF,EAAQpQ,OAAO+L,SAAQ,SAACvP,GACtB,IAAM+T,EAAU/T,EAAIgU,MAAM,uBAC1B,GAAID,GAAWA,EAAQ1W,OAAS,EAAG,CACjC,IAAM2K,EAAS+L,EAAQ,GACvBD,EAAS9L,GAAU4L,EAAQ5T,OAGxB8T,EAGT,IAAMG,GAAOC,gBAAW,CACtBlM,OAAQzH,OAAOiJ,QAAQ2K,UAAUC,QAAQ9O,MAAM,KAAK,GACpD+O,eAAgB,KAChBP,SAAUH,OAGNW,GAAMC,eAAUC,IAMtBF,GAAIlN,IAAI6M,IACRK,GAAIG,MAAM,S", "file": "js/app.ef44ce60.js", "sourcesContent": [" \t// install a JSONP callback for chunk loading\n \tfunction webpackJsonpCallback(data) {\n \t\tvar chunkIds = data[0];\n \t\tvar moreModules = data[1];\n \t\tvar executeModules = data[2];\n\n \t\t// add \"moreModules\" to the modules object,\n \t\t// then flag all \"chunkIds\" as loaded and fire callback\n \t\tvar moduleId, chunkId, i = 0, resolves = [];\n \t\tfor(;i < chunkIds.length; i++) {\n \t\t\tchunkId = chunkIds[i];\n \t\t\tif(Object.prototype.hasOwnProperty.call(installedChunks, chunkId) && installedChunks[chunkId]) {\n \t\t\t\tresolves.push(installedChunks[chunkId][0]);\n \t\t\t}\n \t\t\tinstalledChunks[chunkId] = 0;\n \t\t}\n \t\tfor(moduleId in moreModules) {\n \t\t\tif(Object.prototype.hasOwnProperty.call(moreModules, moduleId)) {\n \t\t\t\tmodules[moduleId] = moreModules[moduleId];\n \t\t\t}\n \t\t}\n \t\tif(parentJsonpFunction) parentJsonpFunction(data);\n\n \t\twhile(resolves.length) {\n \t\t\tresolves.shift()();\n \t\t}\n\n \t\t// add entry modules from loaded chunk to deferred list\n \t\tdeferredModules.push.apply(deferredModules, executeModules || []);\n\n \t\t// run deferred modules when all chunks ready\n \t\treturn checkDeferredModules();\n \t};\n \tfunction checkDeferredModules() {\n \t\tvar result;\n \t\tfor(var i = 0; i < deferredModules.length; i++) {\n \t\t\tvar deferredModule = deferredModules[i];\n \t\t\tvar fulfilled = true;\n \t\t\tfor(var j = 1; j < deferredModule.length; j++) {\n \t\t\t\tvar depId = deferredModule[j];\n \t\t\t\tif(installedChunks[depId] !== 0) fulfilled = false;\n \t\t\t}\n \t\t\tif(fulfilled) {\n \t\t\t\tdeferredModules.splice(i--, 1);\n \t\t\t\tresult = __webpack_require__(__webpack_require__.s = deferredModule[0]);\n \t\t\t}\n \t\t}\n\n \t\t\n \t}\n\n \t// The module cache\n \tvar installedModules = {};\n\n \t// object to store loaded and loading chunks\n \t// undefined = chunk not loaded, null = chunk preloaded/prefetched\n \t// Promise = chunk loading, 0 = chunk loaded\n \tvar installedChunks = {\n \t\t\"app\": 0\n \t};\n\n \tvar deferredModules = [];\n\n \t// The require function\n \tfunction __webpack_require__(moduleId) {\n\n \t\t// Check if module is in cache\n \t\tif(installedModules[moduleId]) {\n \t\t\treturn installedModules[moduleId].exports;\n \t\t}\n \t\t// Create a new module (and put it into the cache)\n \t\tvar module = installedModules[moduleId] = {\n \t\t\ti: moduleId,\n \t\t\tl: false,\n \t\t\texports: {}\n \t\t};\n\n \t\t// Execute the module function\n \t\tmodules[moduleId].call(module.exports, module, module.exports, __webpack_require__);\n\n \t\t// Flag the module as loaded\n \t\tmodule.l = true;\n\n \t\t// Return the exports of the module\n \t\treturn module.exports;\n \t}\n\n\n \t// expose the modules object (__webpack_modules__)\n \t__webpack_require__.m = modules;\n\n \t// expose the module cache\n \t__webpack_require__.c = installedModules;\n\n \t// define getter function for harmony exports\n \t__webpack_require__.d = function(exports, name, getter) {\n \t\tif(!__webpack_require__.o(exports, name)) {\n \t\t\tObject.defineProperty(exports, name, { enumerable: true, get: getter });\n \t\t}\n \t};\n\n \t// define __esModule on exports\n \t__webpack_require__.r = function(exports) {\n \t\tif(typeof Symbol !== 'undefined' && Symbol.toStringTag) {\n \t\t\tObject.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });\n \t\t}\n \t\tObject.defineProperty(exports, '__esModule', { value: true });\n \t};\n\n \t// create a fake namespace object\n \t// mode & 1: value is a module id, require it\n \t// mode & 2: merge all properties of value into the ns\n \t// mode & 4: return value when already ns object\n \t// mode & 8|1: behave like require\n \t__webpack_require__.t = function(value, mode) {\n \t\tif(mode & 1) value = __webpack_require__(value);\n \t\tif(mode & 8) return value;\n \t\tif((mode & 4) && typeof value === 'object' && value && value.__esModule) return value;\n \t\tvar ns = Object.create(null);\n \t\t__webpack_require__.r(ns);\n \t\tObject.defineProperty(ns, 'default', { enumerable: true, value: value });\n \t\tif(mode & 2 && typeof value != 'string') for(var key in value) __webpack_require__.d(ns, key, function(key) { return value[key]; }.bind(null, key));\n \t\treturn ns;\n \t};\n\n \t// getDefaultExport function for compatibility with non-harmony modules\n \t__webpack_require__.n = function(module) {\n \t\tvar getter = module && module.__esModule ?\n \t\t\tfunction getDefault() { return module['default']; } :\n \t\t\tfunction getModuleExports() { return module; };\n \t\t__webpack_require__.d(getter, 'a', getter);\n \t\treturn getter;\n \t};\n\n \t// Object.prototype.hasOwnProperty.call\n \t__webpack_require__.o = function(object, property) { return Object.prototype.hasOwnProperty.call(object, property); };\n\n \t// __webpack_public_path__\n \t__webpack_require__.p = \"/\";\n\n \tvar jsonpArray = window[\"webpackJsonp\"] = window[\"webpackJsonp\"] || [];\n \tvar oldJsonpFunction = jsonpArray.push.bind(jsonpArray);\n \tjsonpArray.push = webpackJsonpCallback;\n \tjsonpArray = jsonpArray.slice();\n \tfor(var i = 0; i < jsonpArray.length; i++) webpackJsonpCallback(jsonpArray[i]);\n \tvar parentJsonpFunction = oldJsonpFunction;\n\n\n \t// add entry module to deferred list\n \tdeferredModules.push([0,\"chunk-vendors\"]);\n \t// run deferred modules when ready\n \treturn checkDeferredModules();\n", "module.exports = {\r\n  _searchObjectInArray: function(name<PERSON><PERSON>, nameValue, myArray) {\r\n    for (var i = 0; i < myArray.length; i++) {\r\n      if (myArray[i][nameKey] === nameValue) {\r\n        return myArray[i];\r\n      }\r\n    }\r\n  },\r\n  getMobile: function(that) {\r\n    if (that.mobile && that.mobile != \"\") return that.mobile;\r\n    else if (that.i2e1Data.loginUser.tempmobile)\r\n      return that.i2e1Data.loginUser.tempmobile;\r\n    else return \"\";\r\n  },\r\n  makei2e1Login: function(loginResponse) {\r\n    var form = document.createElement(\"form\");\r\n    var url = loginResponse.url + \"?\";\r\n    var fullRedirect = false;\r\n\r\n    var autoLogin = loginResponse.isAutoLogin;\r\n\r\n    for (var i = 0; i < loginResponse.parameters.length; ++i) {\r\n      var value = loginResponse.parameters[i].value;\r\n\r\n      if (\r\n        window.location.hostname === \"localhost\" &&\r\n        (loginResponse.parameters[i].name === \"url\" ||\r\n          loginResponse.parameters[i].name === \"userurl\")\r\n      ) {\r\n        url = loginResponse.parameters[i].value;\r\n      } else {\r\n        var input = document.createElement(\"input\");\r\n        input.setAttribute(\"name\", loginResponse.parameters[i].name);\r\n        input.setAttribute(\"value\", value);\r\n        form.appendChild(input);\r\n        url +=\r\n          loginResponse.parameters[i].name +\r\n          \"=\" +\r\n          encodeURIComponent(value) +\r\n          \"&\";\r\n      }\r\n\r\n      if (\r\n        loginResponse.parameters[i].name === \"url\" ||\r\n        loginResponse.parameters[i].name === \"userurl\"\r\n      ) {\r\n        if (\r\n          loginResponse.parameters[i].value.indexOf(window.location.hostname) <\r\n            0 &&\r\n          loginResponse.parameters[i].value\r\n            .toLowerCase()\r\n            .indexOf(\"jsonlanding\") < 0\r\n        ) {\r\n          fullRedirect = loginResponse.parameters[i].value;\r\n        }\r\n      }\r\n    }\r\n\r\n    if (fullRedirect) {\r\n      //_loginSuccessFullEvent(fullRedirect, 1000)\r\n    }\r\n\r\n    if (window.location.href.indexOf(\"doTest\") > -1) {\r\n      var obj = this._searchObjectInArray(\r\n        \"name\",\r\n        \"userurl\",\r\n        loginResponse.parameters\r\n      );\r\n      window.location.href = obj.value;\r\n    } else {\r\n      if (window.location.protocol === \"https:\") {\r\n        window.location.href = url.replace(\"JsonLanding\", \"LoggedIn\");\r\n      } else if (\r\n        loginResponse.isGet ||\r\n        window.location.hostname === \"localhost\"\r\n      ) {\r\n        window.location.href = url;\r\n      } else {\r\n        form.attr(\"action\", loginResponse.url);\r\n        form.attr(\"method\", \"post\");\r\n        input = document.createElement(\"input\");\r\n        input.setAttribute(\"type\", \"submit\");\r\n        form.append(input);\r\n        document.body.appendChild(form);\r\n        form.submit();\r\n      }\r\n    }\r\n  },\r\n  doLogin: function() {\r\n    this.makei2e1Login(window.landingPage);\r\n  },\r\n};\r\n", "var map = {\n\t\"./en.json\": \"edd4\",\n\t\"./hi.json\": \"7a03\",\n\t\"./te.json\": \"143f\"\n};\n\n\nfunction webpackContext(req) {\n\tvar id = webpackContextResolve(req);\n\treturn __webpack_require__(id);\n}\nfunction webpackContextResolve(req) {\n\tif(!__webpack_require__.o(map, req)) {\n\t\tvar e = new Error(\"Cannot find module '\" + req + \"'\");\n\t\te.code = 'MODULE_NOT_FOUND';\n\t\tthrow e;\n\t}\n\treturn map[req];\n}\nwebpackContext.keys = function webpackContextKeys() {\n\treturn Object.keys(map);\n};\nwebpackContext.resolve = webpackContextResolve;\nmodule.exports = webpackContext;\nwebpackContext.id = \"49f8\";", "<template>\n  <div class=\"top-container\">\n    <LoginContainer/>\n  </div>\n</template>\n\n<script>\nimport LoginContainer from './components/LoginContainer.vue'\n\nexport default {\n  name: 'App',\n  components: {\n    LoginContainer\n  },\n}\n</script>", "<template>\r\n  <div class=\"login container\">\r\n    <div v-show=\"loader\" class=\"overlay\"><div class=\"overlay-content\"><img id=\"loader\" style=\"width:50px\" v-bind:src=\"cdnDomain + 'images/wiom/loading.svg'\" alt=\"Loading...\"/></div></div>\r\n    <LanguageSelector />\r\n    <div class=\"login-container\">\r\n      <div class=\"outer-card1\" cage=\"outer-header\" style=\"text-align: right;\">\r\n        <img v-if=\"logo\" id=\"outer-card-img\" onerror=\"this.src=window.loginUser.cdnDomain+'images/wiom/wiom_logo.png'\" class=\"pixelated\" v-bind:src=\"logo\" style=\"width:100px;height:auto;\" />\r\n      </div>\r\n      <FirstState \r\n        :key=\"'firstState::'+loginUserSession\"\r\n        v-if=\"state == 'firstState'\"\r\n        v-on:got-mobile=\"setMobile($event)\"\r\n        v-on:generate-otp=\"generateOTP($event)\"\r\n        v-on:swap-state=\"swapState($event)\"\r\n        v-bind:i2e1Data=\"i2e1Data\"\r\n        v-on:show-loader=\"loader = $event\" />\r\n      <SecondState \r\n        :key=\"'secondState::'+mobile\"\r\n        v-if=\"state == 'secondState'\"\r\n        v-on:swap-state=\"swapState($event)\"\r\n        v-on:resend-otp=\"generateOTP($event, true)\"\r\n        v-on:session-attributes=\"sessionAttributes = $event\"\r\n        v-bind:i2e1Data=\"i2e1Data\"\r\n        v-on:show-loader=\"loader = $event\" />\r\n      <RedirectionState \r\n        :key=\"'redirectionState::'+mobile\"\r\n        v-if=\"state == 'redirectionState'\"\r\n        v-on:swap-state=\"swapState($event)\"\r\n        v-bind:i2e1Data=\"i2e1Data\"\r\n        v-bind:sessionAttributes=\"sessionAttributes\" />\r\n    </div>\r\n    <Footer v-bind:state=\"state\" />\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport helper from './../utils/Helper';\r\nimport Axios from './../plugins/axios';\r\nimport LanguageSelector from './Common/LanguageSelector';\r\nimport FirstState from './FirstState/FirstState';\r\nimport SecondState from './SecondState/SecondState';\r\nimport RedirectionState from './RedirectionState/RedirectionState';\r\nimport Footer from './Common/Footer';\r\nimport Login from './../Libs/Login';\r\nimport Helper from './../utils/Helper';\r\n\r\nexport default {\r\n  name: 'LoginContainer',\r\n  components: {\r\n    LanguageSelector,\r\n    FirstState,\r\n    SecondState,\r\n    RedirectionState,\r\n    Footer\r\n  },\r\n  mounted() {\r\n    this.loadi2e1Data();\r\n    this.doSomethingBad();\r\n    this.initState();\r\n  },\r\n  data: function() {\r\n    return {\r\n      questionTypes: [3],\r\n      mobile: null,\r\n      nasid: 0,\r\n      loginUserSession: null,\r\n      state: \"firstState\",\r\n      i2e1Data: {\r\n        i2e1Constants: null,\r\n        viewBag: null,\r\n        loginUser: null\r\n      },\r\n      cdnDomain: null,\r\n      sessionAttributes: {\r\n        dataLeft: '',\r\n        timeLeft: ''\r\n      },\r\n      logo: null,\r\n      loader: false,\r\n      repaintSecondState: false\r\n    }\r\n  },\r\n  methods: {\r\n    loadi2e1Data: function () {\r\n      this.loader = true;\r\n      this.i2e1Data = {\r\n        i2e1Constants: window.i2e1Constants,\r\n        viewBag: window.viewBag,\r\n        loginUser: window.loginUser\r\n      }\r\n      this.i2e1Data.loginUser.cdnDomain = window.cdnDomain;\r\n      this.loginUserSession = helper.getQueryStringValue('login-user-session');\r\n      this.cdnDomain = window.cdnDomain;\r\n      if (this.loginUserSession && this.loginUserSession != '') {\r\n        helper.setCookie('login_user_session', this.loginUserSession);\r\n        this.nasid = this.loginUserSession.split('-')[this.loginUserSession.split('-').length - 1];\r\n        this.getCustomLogo();\r\n        this.loader = false;\r\n      }\r\n      else {\r\n        // Code to take you to the error page\r\n      }\r\n    },\r\n    setMobile: function(mobile) {\r\n      this.mobile = mobile;\r\n      //this.repaintSecondState = !this.repaintSecondState;\r\n      this.i2e1Data.loginUser.tempmobile = mobile;\r\n    },\r\n    getCustomLogo: function() {\r\n      let _self = this;\r\n      try {\r\n        setTimeout( function() {\r\n          var logo = document.getElementsByTagName('outer-header')[0].firstElementChild.getAttribute('src');\r\n          if (logo.indexOf('http') == 0) {\r\n            _self.logo = document.getElementsByTagName('outer-header')[0].firstElementChild.getAttribute('src');\r\n          } else {\r\n            _self.logo = 'https://i2e1.in' + document.getElementsByTagName('outer-header')[0].firstElementChild.getAttribute('src');\r\n          }\r\n          console.log(\"FOUND LOGO\", _self.logo);\r\n        }, 120);\r\n      }\r\n      catch(ex) {\r\n        console.log('Error :', JSON.stringify(ex));\r\n        this.logo = this.cdnDomain + 'images/wiom/wiom_logo.png';\r\n      }\r\n    },\r\n    overrideCTAColor: function(element) {\r\n      for (var i=0; i<document.getElementsByClassName('primary login_button').length;i++) {\r\n        var style = window.getComputedStyle(document.getElementsByClassName('primary login_button')[i]);\r\n        var found = 0;\r\n        for (var prop in style) {\r\n          if (style[prop] == 'background-color')\r\n            found++;\r\n          if (style[prop] == 'background-image')\r\n            found++;\r\n        }\r\n      }\r\n\r\n    },\r\n    doSomethingBad: function() {\r\n      var data = {questionTypes: this.questionTypes, mobile: Login.getMobile(this), routerId: this.nasid};\r\n      Axios.post(\"Login/GetQuestions?login-user-session=\"+this.loginUserSession, data)\r\n      .then( () => {\r\n      }).catch(() => {\r\n      });\r\n    },\r\n    initState: function(state) {\r\n      if (this.i2e1Data.viewBag.sessionExpired) {\r\n          this.swapState('errorState', { errorType: 'time-exhausted', msg: \"Your time limit is over\", errorImg: \"/images/session_time_out.png\" });\r\n      } else if (this.i2e1Data.viewBag.dataExhausted) {\r\n          this.swapState('errorState', { errorType: 'limit-exhausted', msg: \"Your data limit is over\", errorImg: \"/images/limitExhausted.png\" });\r\n      } else if (this.i2e1Data.viewBag.loginResponse && state !== this.i2e1Data.i2e1Constants.customState) {\r\n          if (this.i2e1Data.viewBag.loginResponse.msg === 'Auto Login') {\r\n              this.i2e1Data.i2e1Constants.redirectionState = 'AL Redirection State';\r\n              this.i2e1Data.i2e1Constants.landingState = 'AL Landing State';\r\n              this.i2e1Data.viewBag.loginResponse.data.isAutoLogin = true;\r\n          }\r\n          //_preLogin(_viewBag.loginResponse.data, true);\r\n      } else if (state) {\r\n          this.swapState(state);\r\n      }\r\n      else if (this.i2e1Data.loginUser.tempmobile && this.i2e1Data.loginUser.tempmobile != \"\") {\r\n          this.mobile = this.i2e1Data.loginUser.tempmobile;\r\n          this.swapState('secondState');\r\n      } else {\r\n          this.swapState('firstState');\r\n      }\r\n    },\r\n    swapState: function(nextState, data = null) {\r\n      this.state = nextState;\r\n    },\r\n    generateOTPFailure: function (response) {\r\n        //_handleOTPError(response.msg, 'username', _i2e1Constants.firstState);\r\n        this.loader = false;\r\n    },\r\n    generateOTPSuccess: function (response, resend) {\r\n      if(response.status == 1) {\r\n            //_swapState(_i2e1Constants.errorState, response.msg);\r\n      }\r\n      else if(response.data.otpResponse.landingPage){\r\n            //_swapState(_i2e1Constants.questionState, { userProfile: response.data.userProfile, otpResponse: response.data.otpResponse });\r\n            /*$(\"div[cage=connect-button] input\")[0].addEventListener('click', function () {\r\n                //_question_proceed(username.value, response.data.otpResponse);\r\n            });*/\r\n            \r\n        }\r\n        if (!resend)\r\n          this.swapState('secondState');\r\n        //_swapState(_i2e1Constants.secondState, { resend: resend, userProfile: response.data.userProfile, stateTransition: !resend });\r\n        if (resend)\r\n          this.loader = false;\r\n    },\r\n    generateOTP: function (username, resend) {\r\n      this.loader = true;\r\n      //var countryCode = '';\r\n      /*@if (ViewBag.GlobalOtp_Enabled != null && ViewBag.GlobalOtp_Enabled)\r\n      {\r\n          @:countryCode = $('.selected-flag').attr('title');\r\n          @:countryCode = countryCode.substring(countryCode.indexOf('+'));\r\n          @:countryCode = username.value.indexOf('+') == 0 ? '' : countryCode;\r\n          @:$('#username').val(countryCode + username.value.replace(/ /g,''))\r\n      }\r\n      */\r\n      Axios.post('Login/GenerateOTP?login-user-session='+helper.getCookie('login_user_session'), {\r\n          mobile: username,\r\n          smsApi: resend ? 'change' : null,\r\n          clientAuthType: 'PHONE'\r\n      })\r\n      .then( response => {\r\n          if (response.data)\r\n              this.generateOTPSuccess(response.data, resend);\r\n          else\r\n              this.generateOTPFailure(response);\r\n      })\r\n      .catch( (error) => {\r\n          this.generateOTPFailure(error);\r\n      });\r\n    },\r\n    setSessionAttributes: function(sessionAttributes) {\r\n      this.sessionAttributes = sessionAttributes;\r\n    }\r\n  }\r\n}\r\n</script>", "export default {\r\n  setCookie: function(cname, cvalue, exdays = 1) {\r\n    var d = new Date();\r\n    d.setTime(d.getTime() + exdays * 24 * 60 * 60 * 1000);\r\n    var expires = \"expires=\" + d.toUTCString();\r\n    document.cookie = cname + \"=\" + cvalue + \";\" + expires + \";path=/\";\r\n  },\r\n  getCookie: function(cname) {\r\n    var name = cname + \"=\";\r\n    var ca = document.cookie.split(\";\");\r\n    for (var i = 0; i < ca.length; i++) {\r\n      var c = ca[i];\r\n      while (c.charAt(0) == \" \") {\r\n        c = c.substring(1);\r\n      }\r\n      if (c.indexOf(name) == 0) {\r\n        return c.substring(name.length, c.length);\r\n      }\r\n    }\r\n    return \"\";\r\n  },\r\n  deleteCookie: function(cname) {\r\n    document.cookie =\r\n      cname + \"=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;\";\r\n  },\r\n  reload: function() {\r\n    location.reload();\r\n  },\r\n  getQueryStringValue: function(key) {\r\n    return decodeURIComponent(\r\n      window.location.search.replace(\r\n        new RegExp(\r\n          \"^(?:.*[&\\\\?]\" +\r\n            encodeURIComponent(key).replace(/[.+*]/g, \"\\\\$&\") +\r\n            \"(?:\\\\=([^&]*))?)?.*$\",\r\n          \"i\"\r\n        ),\r\n        \"$1\"\r\n      )\r\n    );\r\n  },\r\n  shuffle: function(array) {\r\n    var currentIndex = array.length,\r\n      temporaryValue,\r\n      randomIndex;\r\n\r\n    // While there remain elements to shuffle...\r\n    while (0 !== currentIndex) {\r\n      // Pick a remaining element...\r\n      randomIndex = Math.floor(Math.random() * currentIndex);\r\n      currentIndex -= 1;\r\n\r\n      // And swap it with the current element.\r\n      temporaryValue = array[currentIndex];\r\n      array[currentIndex] = array[randomIndex];\r\n      array[randomIndex] = temporaryValue;\r\n    }\r\n\r\n    return array;\r\n  },\r\n  roundOffData: function(val) {\r\n    return Math.floor(val * 100) / 100;\r\n  },\r\n  roundOffTime: function(totalSec) {\r\n    if (totalSec > 0) {\r\n      var days = parseInt(totalSec / 86400);\r\n      var hours = parseInt(totalSec / 3600) % 24;\r\n      var minutes = parseInt(totalSec / 60) % 60;\r\n      var seconds = totalSec % 60;\r\n      var result =\r\n        (days >= 1 ? days + (days === 1 ? \" day \" : \" days \") : \"\") +\r\n        (hours >= 1 ? hours + (hours === 1 ? \" hr \" : \" hrs \") : \"\");\r\n      if (days < 1)\r\n        result +=\r\n          minutes >= 1 ? minutes + (minutes === 1 ? \" min\" : \" mins\") : \"\";\r\n      return result ? result : \"less than a minute\";\r\n    }\r\n    return \"None\";\r\n  },\r\n};\r\n", "import axios from \"axios\";\r\n\r\nvar cdnDomain = \"https://i2e1.in\";\r\n\r\nif (window.cdnDomain.indexOf(\"localhost\") > -1) {\r\n  cdnDomain = \"https://localhost:44300\";\r\n} else if (window.cdnDomain.indexOf(\"testease\") > -1) {\r\n  cdnDomain = \"https://testease.i2e1.in\";\r\n}\r\n\r\nvar Axios = axios.create({\r\n  baseURL: cdnDomain,\r\n});\r\n\r\nAxios.interceptors.response.use(\r\n  function(response) {\r\n    return response;\r\n  },\r\n  function(error) {\r\n    if (\r\n      error.response &&\r\n      error.response.status &&\r\n      error.response.status === 403\r\n    )\r\n      window.location(\"/\");\r\n    else return Promise.reject(error);\r\n  }\r\n);\r\n\r\nexport default Axios;\r\n", "<template>\r\n    <div class=\"language-selectors\">\r\n        <a v-show=\"showOption('en')\" @click=\"changeLanguage('en')\" id=\"en\">English</a>\r\n        <a v-show=\"showOption('hi')\" @click=\"changeLanguage('hi')\" id=\"hi\">हिंदी</a>\r\n        <a v-show=\"showOption('te')\" @click=\"changeLanguage('te')\" id=\"te\">తెలుగు</a>\r\n    </div>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n    name: 'LanguageSelector',\r\n    props: [],\r\n    emits: ['current-language'],\r\n    data: function () {\r\n        return {\r\n            currentLanguage: null\r\n        }\r\n    },\r\n    mounted: function() {\r\n        this.currentLanguage = this.$i18n.locale;\r\n    },\r\n    methods: {\r\n        showOption: function (language) {\r\n            if (this.currentLanguage == language)\r\n                return false;\r\n            else\r\n                return true;\r\n        },\r\n        changeLanguage: function(language) {\r\n            this.currentLanguage = language;\r\n            this.$emit('current-language', language);\r\n            this.$i18n.locale = language;\r\n        }\r\n    }\r\n}\r\n</script>", "import { render } from \"./LanguageSelector.vue?vue&type=template&id=755315c2\"\nimport script from \"./LanguageSelector.vue?vue&type=script&lang=js\"\nexport * from \"./LanguageSelector.vue?vue&type=script&lang=js\"\nscript.render = render\n\nexport default script", "<template>\r\n    <form class=\"form first-state state-transition\">\r\n        <div class=\"barking\">\r\n            <div class=\"loud\"> {{ $t('firstState.bark_loud') }} </div>\r\n            <div class=\"less-loud\">{{ $t('firstState.bark_lessloud') }}</div>\r\n        </div>\r\n        <PhoneInput v-on:valid=\"setValid($event)\" v-on:invalid=\"setInvalid()\" />\r\n        <Survey v-bind:questionType=\"questionType\" v-bind:state=\"state\" />\r\n        <div cage=\"generate-otp\">\r\n            <input type=\"button\" id=\"get_otp\" ref=\"get_otp\" name=\"enter\" class=\"primary login_button\" v-bind:value=\"$t('firstState.generate_otp')\" @click=\"disableCTA = true; $emit('generate-otp', mobile)\" v-bind:disabled=\"!this.validUsername && this.disableCTA\" />\r\n        </div>\r\n        <div class=\"tnc-area\">\r\n            <span class=\"tnc1\" cage=\"tnc\" v-html=\"$t('firstState.tnc')\"></span>\r\n        </div>\r\n    </form>\r\n</template>\r\n\r\n<script>\r\nimport Axios from './../../plugins/axios';\r\nimport PhoneInput from './PhoneInput';\r\nimport Survey from './../Common/Survey/Survey';\r\nimport helper from './../../utils/Helper';\r\nimport Login from './../../Libs/Login';\r\n\r\nexport default {\r\n    name: 'FirstState',\r\n    components: {\r\n        PhoneInput,\r\n        Survey\r\n    },\r\n    props: ['i2e1Data'],\r\n    emits: ['swap-state','got-mobile', 'generate-otp'],\r\n    data: function () {\r\n        return {\r\n            questionType: [3],\r\n            i2e1Ques: [],\r\n            validUsername: null,\r\n            mobile: null,\r\n            state: 'firstState',\r\n            disableCTA: false\r\n        }\r\n    },\r\n    methods: {\r\n        setValid: function (mobile) {\r\n            this.validUsername = true;\r\n            this.mobile = mobile;\r\n            this.disableCTA = false;\r\n            this.$emit('got-mobile', this.mobile);\r\n        },\r\n        setInvalid: function () {\r\n            this.validUsername = false;\r\n            this.mobile = null;\r\n        },\r\n        validateUsername: function() {\r\n            return this.validUsername;\r\n        }\r\n    },\r\n    mounted: function () {\r\n        console.log(window.getComputedStyle(this.$refs.get_otp));\r\n        console.log(this.$refs.get_otp.style);\r\n    }\r\n}\r\n</script>", "<template>\r\n    <div class=\"username-area phone-number\">\r\n        <div class=\"title\">{{ $t('helpState.enter_mobile_number') }}</div>\r\n        <div class=\"group\">\r\n            <input type=\"tel\"\r\n                id=\"username\"\r\n                name=\"username\"\r\n                v-model=\"mobile\"\r\n                v-on:change=\"validate()\"\r\n                maxlength=\"10\"\r\n                max=\"9999999999\"\r\n                required=\"required\" pattern=\".*\\S.*\" />\r\n            <label for=\"username\" name=\"username_for\" v-bind:class=\"textAlign\">{{ $t('firstState.enter_phone_number') }}</label>\r\n        </div>\r\n        <div class=\"error-message display-none\" id=\"username-error\" v-html=\"$t('firstState.mobile_invalid')\">\r\n        </div>\r\n        <div v-show=\"validate()\" class=\"error-message display-none\" id=\"username-error\">\r\n            {{$t('firstState.mobile_invalid')}}\r\n        </div>\r\n        <div class=\"error-message display-none\" id=\"fdm-notauthorised-error\">\r\n            {{$t('firstState.fdm_notauthorised')}}\r\n        </div>\r\n        <div class=\"group username_prefix\"><input id=\"username_prefix\" type=\"tel\" value=\"+91\" disabled /></div>\r\n    </div>\r\n</template>\r\n\r\n<script>\r\n    export default {\r\n        name: 'PhoneInput',\r\n        data: function() {\r\n            return {\r\n                mobile: null,\r\n                valid: null\r\n            }\r\n        },\r\n        emits: ['valid','invalid'],\r\n        methods: {\r\n            validate: function() {\r\n                if (this.mobile && this.mobile.length == 10 && parseInt(this.mobile) <= 9999999999 && parseInt(this.mobile) >= 3333333333 ) {\r\n                    this.$emit('valid', this.mobile);\r\n                    return true;\r\n                }\r\n                this.$emit('invalid');\r\n                return false;\r\n            }\r\n        },\r\n        watch: {\r\n            mobile() {\r\n                this.validate();\r\n            }\r\n        },\r\n        computed: {\r\n            textAlign: function() {\r\n                return {\r\n                    'bump-up': this.$i18n.locale != 'en'\r\n                }\r\n            }\r\n        }\r\n    }\r\n</script>", "import { render } from \"./PhoneInput.vue?vue&type=template&id=6c4a91f6\"\nimport script from \"./PhoneInput.vue?vue&type=script&lang=js\"\nexport * from \"./PhoneInput.vue?vue&type=script&lang=js\"\nscript.render = render\n\nexport default script", "<template>\r\n    <PreSurvey v-if=\"state != 'firstState'\" v-bind:i2e1Data=\"i2e1Data\" />\r\n    <div class=\"question-area type-0 phone-number-flow\"></div>\r\n    <div class=\"question-area type-5 phone-number-flow\"></div>\r\n</template>\r\n\r\n<script>\r\n    import PreSurvey from './PreSurvey';\r\n\r\n    export default {\r\n        name: 'Survey',\r\n        props: ['questionType', 'state', 'i2e1Data'],\r\n        components: {\r\n            PreSurvey\r\n        }\r\n    }\r\n</script>", "<template>\r\n    <div class=\"questions-confirm-area\">\r\n        <span class=\"tick\"><img src=\"images/wiom/done.svg\" /></span>\r\n        <div class=\"loud\" v-html=\"$t('secondState.questions_loud', { data: i2e1Data.viewBag.dataLeft })\"></div>\r\n        <div class=\"less-loud\" v-html=\"$t('secondState.questions_lessloud')\"></div>\r\n        <div class=\"questions-confirm-cta\">\r\n            <input type=\"button\" class=\"primary login_button\" onclick=\"_answerQuestions()\" v-bind:value=\"$t('secondState.questions_cta_okay')\" />\r\n        </div>\r\n    </div>\r\n</template>\r\n\r\n<script>\r\n    export default {\r\n        name: 'PreSurvey',\r\n        props: ['i2e1Data']\r\n    }\r\n</script>", "import { render } from \"./PreSurvey.vue?vue&type=template&id=084fbf60\"\nimport script from \"./PreSurvey.vue?vue&type=script&lang=js\"\nexport * from \"./PreSurvey.vue?vue&type=script&lang=js\"\nscript.render = render\n\nexport default script", "import { render } from \"./Survey.vue?vue&type=template&id=d476ae88\"\nimport script from \"./Survey.vue?vue&type=script&lang=js\"\nexport * from \"./Survey.vue?vue&type=script&lang=js\"\nscript.render = render\n\nexport default script", "import { render } from \"./FirstState.vue?vue&type=template&id=2fe72fe6\"\nimport script from \"./FirstState.vue?vue&type=script&lang=js\"\nexport * from \"./FirstState.vue?vue&type=script&lang=js\"\nscript.render = render\n\nexport default script", "<template>\r\n    <form class=\"form second-state state-transition\" onsubmit=\"return false;\">\r\n        <div class=\"button-group\">\r\n            <div v-show=\"showBack\" class=\"back-area\" @click=\"$emit('swap-state','firstState')\">\r\n                <img v-bind:title=\"$t('secondState.back')\" v-bind:src=\"i2e1Data.loginUser.cdnDomain + 'images/back.png'\" />\r\n            </div>\r\n        </div>\r\n        <div v-show=\"welcomeBack\" class=\"welcome-back\">\r\n            <div class=\"namaste\">\r\n                <img v-bind:src=\"i2e1Data.loginUser.cdnDomain + 'images/wiom/namaste.svg'\" style=\"width:80px\" />\r\n            </div>\r\n            <div class=\"greetings\" v-html=\"$t('secondState.welcomeback_greetings', { prefix : i2e1Data.loginUser.cdnDomain })\">\r\n            </div>\r\n        </div>\r\n        <div v-show=\"showBarking\" class=\"barking\">\r\n            <div class=\"loud\">{{ $t('secondState.bark_loud') }}</div>\r\n            <div class=\"less-loud\">{{ $t('secondState.bark_lessloud') }}</div>\r\n        </div>\r\n        <OTPInput v-show=\"showOTPArea\" v-on:valid=\"ValidOTP($event)\" v-on:invalid=\"InvalidOTP()\" />\r\n        <div v-show=\"invalidOTP\" class=\"error-message\" id=\"otp-error\">\r\n            {{ $t('secondState.otp_invalid') }}\r\n        </div>\r\n        <!--\r\n        TODO: Enable Access Code\r\n        <div class=\"access-code-area\" style=\"display:none\">\r\n            <div class=\"group\">\r\n                <input type=\"text\" id=\"otp_access_code\" required=\"required\" pattern=\".*\\S.*\" />\r\n                <label for=\"otp_access_code\" i18n=\"secondState_enter_voucher_code\">Enter Voucher Code</label>\r\n            </div>\r\n        </div>-->\r\n\r\n        <div id=\"captcha-holder\" v-show=\"showCaptcha\">\r\n            <div>\r\n                <span>{{ $t('secondState.captcha') }}</span><br />\r\n                <img id=\"captcha-img\" title=\"captcha\" src=\"\" />\r\n            </div>\r\n            <div>\r\n                <span style=\"opacity:0;\">{{ $t('secondState.enter_captcha') }}</span>\r\n                <input id=\"captcha\" placeholder=\"Enter Captcha\" type=\"text\" />\r\n            </div>\r\n        </div>\r\n\r\n        <div v-show=\"showSurveyConfirmation\" class=\"questions-confirm-area\">\r\n            <span v-show=\"showSurveyConfirmation && !welcomeBack\" class=\"tick\"><img v-bind:src=\"i2e1Data.loginUser.cdnDomain + 'images/wiom/done.svg'\" /></span>\r\n            <div class=\"loud\" v-html=\"$t('secondState.questions_loud', { data: dataLeft })\"></div>\r\n            <div class=\"less-loud\">{{ $t('secondState.questions_lessloud') }}</div>\r\n            <div class=\"questions-confirm-cta\">\r\n                <input type=\"button\" class=\"primary login_button\" v-bind:value=\"$t('secondState.questions_cta_okay')\" @click=\"answerQuestions()\" />\r\n            </div>\r\n        </div>\r\n\r\n        <div v-if=\"i2e1Ques.length > 0 && startedAnswering\">\r\n            <div \r\n                class=\"question-area phone-number-flow\" \r\n                v-for=\"question in i2e1Ques\" \r\n                v-bind:key=\"question.id+'::'+question.journey\" \r\n                v-bind:question=\"question\" \r\n                v-bind:class=\"{ 'type-0': question.quesType === 0, 'type-5': question.quesType === 5, 'type-3': question.quesType === 3 }\">\r\n                <div v-if=\"[0,4,5].indexOf(question.answerType) > -1\" class=\"answerType-0\">\r\n                    <div class=\"material-input question text-question\">\r\n                        <div class=\"group\">\r\n                            <input type=\"text\" required=\"required\" pattern=\".*\\S.*\" />\r\n                            <label></label>\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n                <div v-if=\"[1,2].indexOf(question.answerType) > -1\">\r\n                    <div class=\"question select\" v-bind:class=\"'question'+question.id+'_'+question.journey\" v-show=\"!question.hide\">\r\n                        <div class=\"question-text\">{{ question.question.quesText }}<span class=\"required\">*</span></div>\r\n                        <div class=\"options-list\">\r\n                            <p  \r\n                                v-show=\"!option.hide\"\r\n                                v-for=\"option in question.options\"\r\n                                v-bind:key=\"question.id+'::'+question.journey+'::'+option.id\"\r\n                                class=\"{{ 'answer_'+option.id+'_'+currentQuestionIndex }}\" \r\n                                @click=\"mark(question.index,question.id,option.id,question.answerType)\">\r\n                                <input \r\n                                    class\r\n                                    type=\"checkbox\"\r\n                                    v-bind:checked=\"option.id == question.answer\"\r\n                                    @click=\"mark(question.index,question.id,option.id,question.answerType)\" />\r\n                                <label for=\"{{ 'option-' + option.id }}\">\r\n                                    <img v-if=\"option.image\" class=\"content\" v-bind:src=\"i2e1Data.loginUser.cdnDomain + option.image\">{{ option.text }}\r\n                                </label>\r\n                            </p>\r\n                        </div>\r\n                        <input v-show=\"question.showNext\" class=\"next-question\" id=\"done_' + question.id + '_' + question.journey + '\" type=\"button\" @click=\"done(question.index,question.id,question.journey)\" value=\"Next\" />\r\n                    </div>\r\n                </div>\r\n                <!--<div v-if=\"question.answerType == 3\" class='question question<%= data.id %> img-options rating-question'>\r\n                    <div class='question-text'><%= data.quesText %></div>\r\n                    <ul answer-type='<%= data.answerType %>' >\r\n                        <% for( var i=1;i<=5;i++) { %>\r\n                            <li class='img-option' >\t\r\n                                <input id='radio_rating<%= i %>' type='radio' class='option-answer' name='<%= data.id %>' value=<%= i %>  />\r\n                                <label for='radio_rating<%= i %>' onclick='_removeErrorClass(this)'>\r\n                                <div>\r\n                                    <div class='circle'>\r\n                                        <img class='content' src='/images/feedback-<%= i %>.png' />\r\n                                    </div>\r\n                                    <span></span>\r\n                                </div>\r\n                                </label>\r\n                            </li> \r\n                        <% } %> \r\n                    </ul>\r\n                </div>-->\r\n            </div>\r\n        </div>\r\n\r\n        <div cage=\"connect-button\">\r\n            <input v-show=\"showConfirmCTA\" type=\"button\" id=\"confirm\" ref=\"confirm\" class=\"primary login_button\" v-bind:value=\"$t('secondState.confirm')\" @click=\"confirm(false)\" />\r\n            <input v-show=\"showConnectCTA\" type=\"button\" id=\"connect\" ref=\"connect\" class=\"primary login_button\" v-bind:value=\"$t('secondState.connect')\" @click=\"connect(false)\" />\r\n        </div>\r\n        <!--\r\n        @if (Model.clientAuthType == AuthType.PHONE_OR_DATA_VOUCHER_WITHOUT_OTP)\r\n        {\r\n            <span class=\"change_auth_link\" i18n=\"secondState_not_getting_otp_click_here\">Not Getting OTP? <a onclick=\"$('#otp_access_code').show();$('#otp').val('1234').hide()\">Click here</a></span>\r\n        }\r\n        -->\r\n        <div class=\"bottom-button-group\" v-show=\"showBottomButtons\">\r\n            <div class=\"back-area\" @click=\"$emit('swap-state', 'firstState')\">\r\n                <span>{{ $t('secondState.change') }}</span>\r\n            </div>\r\n            <div id=\"primary-resend-area\" class=\"resend-otp-area\">\r\n                <input type=\"button\" id=\"resend-otp\" class=\"small_button primary\" v-bind:value=\"$t('secondState.resend')\" @click=\"$emit('resend-otp', getMobile())\" />\r\n            </div>\r\n        </div>\r\n    </form>\r\n</template>\r\n\r\n<script>\r\nimport Axios from './../../plugins/axios';\r\nimport helper from './../../utils/Helper';\r\nimport OTPInput from './OTPInput';\r\nimport Login from './../../Libs/Login';\r\nimport Helper from './../../utils/Helper';\r\nexport default {\r\n    components: {\r\n        OTPInput\r\n    },\r\n    props: ['i2e1Data'],\r\n    emits: ['swap-state', 'resend-otp', 'session-attributes', 'show-loader'],\r\n    mounted: function () {\r\n        this.onMount();\r\n    },\r\n    data: function () {\r\n        return {\r\n            questionTypes: [0,5],\r\n            nasid: 0,\r\n            OTP: null,\r\n            showOTPArea: true,\r\n            loginUserSession: null,\r\n            showSurveyConfirmation: false,\r\n            showCaptcha: false,\r\n            showConfirmCTA: false,\r\n            showConnectCTA: false,\r\n            invalidOTP: false,\r\n            surveyStarted: false,\r\n            welcomeBack: false,\r\n            showBottomButtons: true,\r\n            showQuestions: false,\r\n            i2e1Ques: [],\r\n            startedAnswering: false,\r\n            showBack: true,\r\n            showBarking: true,\r\n            i2e1FirstAnsweredAt: [],\r\n            landingPage: null,\r\n            dataPolicy: '',\r\n            dataLeft: '',\r\n            timeLeft: ''\r\n        }\r\n    },\r\n    methods: {\r\n        onMount: function () {\r\n            if (this.i2e1Data.loginUser.tempmobile && (this.i2e1Data.viewBag.welcomeBack || this.i2e1Data.viewBag.welcomeBack == \"true\"))\r\n                this.WelcomeBack();\r\n            this.getQuestions();\r\n            this.getNasAttributes();\r\n            this.$emit('show-loader', false);\r\n            console.log(window.getComputedStyle(this.$refs.connect));\r\n            console.log(window.getComputedStyle(this.$refs.confirm)); \r\n        },\r\n        WelcomeBack: function () {\r\n            //$('.profile-section').css(\"background-repeat\", \"no-repeat\");\r\n            //$('.profile-section').css(\"background-size\", \"contain\");\r\n            //$('.login.container.outer-card1>img').css(\"height\", \"1rem !important\");\r\n            //$('.outer-card1>img').css(\"height\", \"2.5rem\");\r\n            this.showOTPArea = false;\r\n            this.showBottomButtons = false;\r\n            this.showBack = false;\r\n            this.showBarking = false;\r\n            this.welcomeBack = true;\r\n            if (this.i2e1Ques.length > 0) {\r\n                this.showSurveyConfirmation = true;\r\n                this.showConnectCTA = false;\r\n                this.showConfirmCTA = false;\r\n                this.showOTPArea = false;\r\n                setTimeout(function () { this.answerQuestions(); }, 4000);\r\n            }\r\n        },\r\n        ValidOTP: function(otp) {\r\n            this.invalidOTP = false;\r\n            this.OTP = otp;\r\n        },\r\n        InvalidOTP: function() {\r\n            this.OTP = null;\r\n        },\r\n        connect: function () {\r\n            this.$emit('show-loader', true);\r\n            let _self = this;\r\n            var failure = function (response) {\r\n                /*\r\n                var m;\r\n                if (response.errMsg)\r\n                    m = response.errMsg;\r\n                else\r\n                    m = response.msg\r\n                var _showCaptcha = function (captchaUrl) {\r\n                    if (captchaUrl) {\r\n                        document.getElementById('captcha-holder').style.display = 'inline-block';\r\n                        captchaUrl && (document.getElementById('captcha-img').src = captchaUrl);\r\n                    } else {\r\n                        document.getElementById('captcha-holder').style.display = 'none';\r\n                    }\r\n                };\r\n                _showCaptcha(response.data);\r\n                if(m.indexOf(\"Access is Blocked By Administrator\")>-1){\r\n                    _handleOTPError(response.msg, 'otp_access_code', _i2e1Constants.secondState);\r\n                }\r\n                else if(m.indexOf('Access') > -1) {\r\n                    if (_previousState == '.second-state') {\r\n                        _handleOTPError( _loginUser.attributes.dataVoucherPlaceholder || 'Enter voucher code', 'data_voucher', _i2e1Constants.dataVoucherState);\r\n                    }\r\n                    _handleOTPError(_loginUser.attributes.dataVoucherPlaceholder || 'Enter bill access code', 'otp_access_code', _i2e1Constants.secondState);\r\n                }\r\n                else {\r\n                    _handleOTPError(_otpMsg, 'otp', _i2e1Constants.secondState);\r\n                }\r\n                */\r\n                _self.$emit('show-loader', false);\r\n            };\r\n            var success = function (response) {\r\n                //if (_previousState == '.first-state' && _loginUser.clientAuthType == 12 && _loginUser.attributes.freeDataPlan > 0 && _loginUser.attributes.freeSessionTime > 0) {\r\n                    //_swapState(_i2e1Constants.dataVoucherState);\r\n                //} else\r\n                //preLogin(response.data.landingPage);\r\n                _self.$emit('show-loader', false);\r\n                _self.preLogin(response.data.data.landingPage);\r\n            };\r\n            //_logMPEvent(_i2e1Constants.secondState, { event: _i2e1Constants.submitOTPPressed });\r\n            var username = Login.getMobile(this);\r\n            var otp = this.OTP;\r\n            //var accessCode = _getAccessCode();\r\n            if (this.i2e1Data.viewBag.welcomeBack) {\r\n                this.mobile = \"3333333333\";\r\n                this.OTP = \"1234\";\r\n            }\r\n            /*else {\r\n\r\n                //if (!_validateOTP(otp)) {\r\n                    //_handleOTPError(_otpMsg, 'otp', _i2e1Constants.secondState);\r\n                    //return;\r\n                //}\r\n\r\n                //if (!accessCode) {\r\n                    //return;\r\n                //}\r\n            }*/\r\n\r\n            var err1 = false, err2 = false, options = {}, params = {};\r\n            //err1 = processAnswers(0);\r\n            //err2 = processAnswers(5);\r\n\r\n            if (!this.processAnswers(0))\r\n                if (!this.processAnswers(5))\r\n                    Axios.post('Login/SubmitOTP?login-user-session='+helper.getCookie('login_user_session'), {\r\n                        mobile: username,\r\n                        accessCode: options.accessCode,\r\n                        macId: params.mac,\r\n                        routerId: params.nasid,\r\n                        challenge: params.challenge,\r\n                        otp: this.OTP,\r\n                        name: options.name,\r\n                        clientAuthType: options.clientAuthType || 'PHONE',\r\n                        captcha: options.captcha,\r\n                        questions: this.i2e1Ques,\r\n                        doLogin: options.doLogin === null ? true : options.doLogin\r\n                    })\r\n                    .then((response) => {\r\n                        success(response);\r\n                    })\r\n                    .catch((error) => {\r\n                        failure(error);\r\n                    });\r\n        },\r\n        confirm: function () {\r\n            this.$emit('show-loader', true);\r\n            let _self = this;\r\n            var success = function () {\r\n                _self.showSurveyConfirmation = true;\r\n                _self.showOTPArea = false;\r\n                _self.showConfirmCTA = false;\r\n                _self.showConnectCTA = false;\r\n                _self.showBarking = false;\r\n                _self.showBottomButtons = false;\r\n                setTimeout(function () { _self.answerQuestions(); }, 4000);\r\n                _self.$emit('show-loader', false);\r\n            };\r\n            var failure = function (invalid = false) {\r\n                _self.showSurveyConfirmation = false;\r\n                _self.showOTPArea = true;\r\n                //_self.connect.failure();\r\n                if (invalid) {\r\n                    _self.invalidOTP = true;\r\n                    setTimeout(function () { _self.invalidOTP =false; }, 4000);\r\n                }\r\n                _self.$emit('show-loader', false);\r\n            };\r\n\r\n            //var accessCode = _getAccessCode();\r\n            if (!this.OTP)\r\n                console.catch(\"Missing OTP while Checking OTP\");\r\n            \r\n            Axios.post('Login/CheckOTP?login-user-session='+helper.getCookie('login_user_session'), {\r\n                mobile: Login.getMobile(this),\r\n                otp: this.OTP,\r\n                name: '',\r\n                clientAuthType: 'PHONE',\r\n                captcha: ''\r\n            })\r\n            .then((response) => {\r\n                if (response.data && response.data.status == 0) {\r\n                    success();\r\n                } else {\r\n                    var invalid = true;\r\n                    failure(invalid);\r\n                }\r\n            })\r\n            .catch((error) => {\r\n                failure();\r\n            });\r\n        },\r\n        getQuestions: function () {\r\n            let _self = this;\r\n            this.loginUserSession = helper.getCookie('login_user_session');\r\n            \r\n            var data = {\r\n                questionTypes: this.questionTypes,\r\n                mobile: Login.getMobile(this).toString(),\r\n                routerId: this.i2e1Data.loginUser.nasid\r\n            };\r\n            Axios.post(\"Login/GetQuestions?login-user-session=\"+this.loginUserSession, data)\r\n            .then( response => {\r\n                var questions = [];\r\n                questions = response.data.data;\r\n                if (questions.length > 0 && questions[0]) {\r\n                    this.showQuestions = false;\r\n                    this.showConfirmCTA = true;\r\n                    this.showConnectCTA = false;\r\n                    if (this.questionTypes.indexOf(3) === 0) {\r\n                        //_logMPEvent(_i2e1Constants.firstState, { event: _i2e1Constants.questionAsked });\r\n                        for (var i = 0; i < questions.length; i++) {\r\n                            questions[i].journey = 0;\r\n                            if (questions[i].answerType === 2 && questions[i].randomized) {\r\n                                questions[i].options = helper.shuffle(questions[i].options);\r\n                            }\r\n                            if (questions[i].answerType === 2 && questions[i].binarySplit) {\r\n                                questions[i].journey = questions[i].options.length - 2;\r\n                                // If more than two options then we need to do splitting\r\n                                // If two or less options then we cant split into further journeys.\r\n                                if (questions[i].options.length > 2) {\r\n                                    var x = i;\r\n                                    for (var o = 2; o <= questions[i].options.length; o++) {\r\n                                        var temp = questions[i];\r\n                                        temp.journey = temp.options.length - o;\r\n                                        questions = questions.splice(i+1, 0, temp);\r\n                                    }\r\n                                }\r\n                            }\r\n                            this.i2e1Ques.push(questions[i]);\r\n                            this.prepareQuestion(questions[i], i, questions.length);\r\n                        }\r\n                    } else {\r\n                        if (questions.length) {\r\n                            //_logMPEvent(_i2e1Constants.secondState, { event: _i2e1Constants.questionAsked });\r\n                            //$('.question-area').addClass('display-none');\r\n                            //$('.question').addClass('display-none');\r\n                            if (this.i2e1Data.loginUser.tempmobile && (this.i2e1Data.viewBag.welcomeBack === true || this.i2e1Data.viewBag.welcomeBack === \"true\")) {\r\n                                this.showSurveyConfirmation = true;\r\n                                this.showOTPArea = false;\r\n                                this.showConfirmCTA = false;\r\n                                this.showConnectCTA = false;\r\n                                //$('.questions-confirm-area > span.tick').hide();\r\n                                setTimeout(function () { _self.answerQuestions(); }, 4000);\r\n                            } else {\r\n                                this.showConfirmCTA = true;\r\n                            }\r\n                        } else {\r\n                            this.showConnectCTA = true;\r\n                            if (this.i2e1Data.loginUser.tempmobile && (this.i2e1Data.viewBag.welcomeBack === true || this.i2e1Data.viewBag.welcomeBack === \"true\")) {\r\n                                //Automatically Connect\r\n                                this.connect(false);\r\n                            }\r\n                        }\r\n                        this.questionTypes.forEach(function (quesType) {\r\n                            var ques = questions.filter(function (q) {\r\n                                return q && q.quesType === quesType;\r\n                            });\r\n                            for (var i = 0; i < ques.length; i++) {\r\n                                var end = false;\r\n                                if (i === (ques.length - 1))\r\n                                    end = true;\r\n                                ques[i].journey = 0;\r\n                                if (ques[i].randomized) {\r\n                                    ques[i].options = helper.shuffle(ques[i].options);\r\n                                }\r\n                                ques[i].journey = 0;\r\n                                var count = ques[i].options.length;\r\n                                if (ques[i].binarySplit) {\r\n                                    ques[i].journey = count - 2;\r\n                                    // If more than two options then we need to do splitting\r\n                                    // If two or less options then we cant split into further journeys.\r\n                                    if (ques[i].options.length > 2) {\r\n                                        for (var o = 2; o <= count; o++) {\r\n                                            ques[i].journey = count - o;\r\n                                            _self.i2e1Ques.push({\r\n                                                hide: true,\r\n                                                id: ques[i].id,\r\n                                                templateId: _self.i2e1Data.loginUser.templateid[0] || 0,\r\n                                                journey: ques[i].journey,\r\n                                                options: ques[i].options,\r\n                                                remainingOptions: [],\r\n                                                answer: null,\r\n                                                optionsOrder: [],\r\n                                                randomized: ques[i].randomized,\r\n                                                binarySplit: ques[i].binarySplit,\r\n                                                answerType: ques[i].answerType,\r\n                                                question: ques[i],\r\n                                                index: _self.i2e1Ques.length,\r\n                                                totalJourneys: count -1,\r\n                                                showNext: false\r\n                                            });\r\n                                        }\r\n                                    }\r\n                                    else {\r\n                                        _self.i2e1Ques.push({\r\n                                            hide: true,\r\n                                            id: ques[i].id,\r\n                                            templateId: _self.i2e1Data.loginUser.templateid[0] || 0,\r\n                                            journey: ques[i].journey,\r\n                                            options: ques[i].options,\r\n                                            remainingOptions: [],\r\n                                            answer: null,\r\n                                            optionsOrder: [],\r\n                                            randomized: ques[i].randomized,\r\n                                            binarySplit: ques[i].binarySplit,\r\n                                            quesType: ques[i].quesType,\r\n                                            answerType: ques[i].answerType,\r\n                                            question: ques[i],\r\n                                            index: _self.i2e1Ques.length,\r\n                                            totalJourneys: 1,\r\n                                            showNext: false\r\n                                        });\r\n                                    }\r\n                                } else {\r\n                                    _self.i2e1Ques.push({\r\n                                        hide: true,\r\n                                        id: ques[i].id,\r\n                                        templateId: _self.i2e1Data.loginUser.templateid[0] || 0,\r\n                                        journey: ques[i].journey,\r\n                                        options: ques[i].options,\r\n                                        remainingOptions: [],\r\n                                        answer: null,\r\n                                        optionsOrder: [],\r\n                                        randomized: ques[i].randomized,\r\n                                        binarySplit: ques[i].binarySplit,\r\n                                        quesType: ques[i].quesType,\r\n                                        answerType: ques[i].answerType,\r\n                                        question: ques[i],\r\n                                        index: _self.i2e1Ques.length,\r\n                                        totalJourneys: 1,\r\n                                        showNext: false\r\n                                    });\r\n                                }\r\n                            }\r\n                            for (var l = 0; l < _self.i2e1Ques.length; l++) {\r\n                                _self.prepareQuestion(_self.i2e1Ques[l].question, l, _self.i2e1Ques[l].totalJourneys);\r\n                            }\r\n                        });\r\n                    }\r\n                } else {\r\n                    this.showConfirmCTA = false;\r\n                    this.showConnectCTA = true;\r\n                }\r\n            }).catch((error) => {\r\n                this.showConfirmCTA = false;\r\n                this.showConnectCTA = true;\r\n            });\r\n        },\r\n        answerQuestions: function () {\r\n            let _self = this;\r\n            if (this.startedAnswering == false) {\r\n                this.startedAnswering = true;\r\n                this.showSurveyConfirmation = false;\r\n                this.showBarking = false;\r\n                this.showBottomButtons = false;\r\n                this.i2e1Ques[0].hide = false;\r\n                if (this.i2e1Ques && this.i2e1Ques.length < 2)\r\n                    this.showConnectCTA = true;\r\n                if (this.i2e1Ques && this.i2e1Ques.length) {\r\n                    this.i2e1Ques[0].displayTime = Date.now();\r\n                    this.i2e1Ques[0].optionsOrder = [];\r\n                    this.i2e1Ques[0].options.forEach(function (option, index) {\r\n                        if (_self.i2e1Ques[0].binarySplit && index >= 2) {\r\n                            _self.i2e1Ques[0].options[index].hide = true;\r\n                            _self.i2e1Ques[0].remainingOptions.push(option.id);\r\n                        } else {\r\n                            _self.i2e1Ques[0].optionsOrder.push(option.id);\r\n                        }\r\n                    });\r\n                    this.showBack = false;\r\n                    //$('.back-area').addClass('display-none');\r\n                    if (this.i2e1Ques.length == 1) {\r\n                        //$('#done_' + _i2e1Ques[0].id + '_' + _i2e1Ques[0].journey).addClass('display-none');\r\n                        this.i2e1Ques[0].hide = true;\r\n                    }\r\n                }\r\n                if (this.i2e1Data.loginUser.mobile && (this.i2e1Data.viewBag.welcomeBack === true || this.i2e1Data.viewBag.welcomeBack === \"true\")) {\r\n                    this.showWelcomeBack = false;\r\n                }\r\n                this.i2e1Ques[0].hide = false;\r\n            }\r\n        },\r\n        mark: function (quesIndex, question, option, answerType) {\r\n            if (this.i2e1Ques[quesIndex].selectionTime === undefined)\r\n                this.i2e1Ques[quesIndex].selectionTime = Date.now();\r\n            if (parseInt(answerType) == 1) {\r\n                /*\r\n                var $box = //$(\"#option-\" + option + \"-\" + quesIndex);\r\n                if ($box.is(\":checked\")) {\r\n                    var group = \"input:checkbox[name='\" + $box.attr(\"name\") + \"']\";\r\n                    //$(group).prop(\"checked\", false);\r\n                    this.i2e1Ques[quesIndex].answer = null;\r\n                } else {\r\n                    var group = \"input:checkbox[name='\" + $box.attr(\"name\") + \"']\";\r\n                    //$(group).prop(\"checked\", false);\r\n                    $box.prop(\"checked\", true);\r\n                    this.i2e1Ques[quesIndex].answer = option;\r\n                }\r\n                */\r\n               this.i2e1Ques[quesIndex].answer = option;\r\n            }\r\n        },\r\n        done: function (quesIndex, questionId, journey, connect = false) {\r\n            let _self = this;\r\n            var next = quesIndex + 1, count = 0;\r\n            if (next == this.i2e1Ques.length || this.i2e1Ques[quesIndex].answer === null || this.i2e1Ques[quesIndex].answer === undefined)\r\n                return;\r\n\r\n            if (this.i2e1Ques[next].binarySplit) {\r\n                if (this.i2e1Ques[quesIndex].id === this.i2e1Ques[next].id) {\r\n                    //Hide All Options By Default If Next Question is a Continuation\r\n                    this.i2e1Ques[next].options.forEach(function (option, index) {\r\n                        _self.i2e1Ques[next].options[index].hide = true;\r\n                    });\r\n                    //Add Current Winner to Next Remaining Options\r\n                    this.i2e1Ques[quesIndex].remainingOptions.push(this.i2e1Ques[quesIndex].answer);\r\n                    this.i2e1Ques[next].optionsOrder = [];\r\n                    this.i2e1Ques[next].options.forEach( function (option, index) {\r\n                        if (count < 2) {\r\n                            if (_self.i2e1Ques[quesIndex].remainingOptions.indexOf(option.id) > -1) {\r\n                                _self.i2e1Ques[next].options[index].hide = false;\r\n                                _self.i2e1Ques[next].optionsOrder.push(option.id);\r\n                                count = count + 1;\r\n                            } else {\r\n                                _self.i2e1Ques[next].remainingOptions.push(option.id);\r\n                            }\r\n                        } else {\r\n                            _self.i2e1Ques[next].remainingOptions.push(option.id);\r\n                        }\r\n                    });\r\n                } else {\r\n                    this.i2e1Ques[next].optionsOrder = [];\r\n                    this.i2e1Ques[next].options.forEach(function (option, index) {\r\n                        if (count < 2) {\r\n                            _self.i2e1Ques[next].options[index].hide = false;\r\n                            _self.i2e1Ques[next].optionsOrder.push(option.id);\r\n                            count = count + 1;\r\n                        } else {\r\n                            _self.i2e1Ques[next].remainingOptions.push(option.id);\r\n                        }\r\n                    });\r\n                }\r\n            } else {\r\n                this.i2e1Ques[next].remainingOptions = [];\r\n                this.i2e1Ques[next].optionsOrder = [];\r\n                this.i2e1Ques[next].options.forEach(function (option, index) {\r\n                    _self.i2e1Ques[next].optionsOrder.push(option.id);\r\n                });\r\n            }\r\n            this.i2e1Ques[quesIndex].hide = true;\r\n            this.i2e1Ques[next].hide = false\r\n            if (next >= (this.i2e1Ques.length - 1)) {\r\n                this.showConnectCTA = true;\r\n            }\r\n            this.i2e1Ques[next].displayTime = Date.now();\r\n            return;\r\n        },\r\n        prepareQuestion: function (question, currentQuestionIndex, currentQuestionJourneys, end = false) {\r\n            switch (question.answerType) {\r\n                case 0:\r\n                case 4:\r\n                case 5:\r\n                    //\r\n                    break;\r\n                    /*\r\n                    questionSection.append($('#internal-templates .answerType-' + question.answerType)[0].innerHTML);\r\n                    var currentQuestion = questionSection.find('.current');\r\n                    currentQuestion.addClass('question' + question.id);\r\n                    currentQuestion.find('input').attr('id', 'qt' + question.id).attr('name', question.id).attr('value', question.answer);\r\n                    currentQuestion.find('label').attr('for', 'qt' + question.id).html(question.quesText);\r\n                    currentQuestion.removeClass('current');\r\n                    */\r\n                    //break;\r\n                case 1:\r\n                case 2:\r\n                    question.options.forEach(function (option, index) {\r\n                        question.options[index].displayIndex = index + 1;\r\n                    });\r\n                    if (question.journey > 0) {\r\n                        this.i2e1Ques[currentQuestionIndex].showNext = true;\r\n                    } else {\r\n                        var next = currentQuestionIndex + 1;\r\n                        if (next < this.i2e1Ques.length)\r\n                            this.i2e1Ques[currentQuestionIndex].showNext = true;\r\n                    }\r\n                    break;\r\n                \r\n                case 3:\r\n                    /*\r\n                    questionSection.append(_parserA3(question));\r\n                    if (question.options && question.options[0].image) {\r\n                        questionSection.find('.question.question' + question.id).addClass('img-options');\r\n                    }\r\n                    */\r\n                    break;\r\n            }\r\n        },\r\n        processAnswers: function (questionType) {\r\n            var errAnswer = false;\r\n            if (this.i2e1Ques.length) {\r\n                for (var i = 0; i < this.i2e1Ques.length; i++) {\r\n                    switch (this.i2e1Ques[i].answerType) {\r\n                        case 1:\r\n                            if (!this.i2e1Ques[i].answer || this.i2e1Ques[i].answer == 0)\r\n                                errAnswer = true;\r\n                            else {\r\n                                if (this.i2e1Ques[i].optionsOrder)\r\n                                    this.i2e1Ques[i].displayIndex = this.i2e1Ques[i].optionsOrder.indexOf(this.i2e1Ques[i].answer) + 1;\r\n                                else\r\n                                    this.i2e1Ques[i].displayIndex = 0;\r\n                            }                   \r\n                            break;\r\n                        case 0:\r\n                        case 4:\r\n                        case 5:\r\n                            if (!this.i2e1Ques[i].answer || this.i2e1Ques[i].answer == 0)\r\n                                errAnswer = true;\r\n                            if (this.i2e1Ques[i].optionsOrder)\r\n                                this.i2e1Ques[i].displayIndex = this.i2e1Ques[i].optionsOrder.indexOf(this.i2e1Ques[i].answer) + 1;\r\n                            else\r\n                                this.i2e1Ques[i].displayIndex = 0;\r\n                            if (this.i2e1Ques[i].questionKey.toLowerCase() === 'email') {\r\n                                //if (!_validateEmail(answered)) {\r\n                                errAnswer = false;\r\n                                //    _showError({ questionEl: questionArea.find('.question' + this.i2e1Ques[i].id), answerType: 0, questionId: this.i2e1Ques[i].id, state: state });\r\n                                //}\r\n                            }\r\n                            this.i2e1Ques[i].answer = this.i2e1Ques[i].answer.trim();\r\n                            break;\r\n                        case 3:\r\n                            if (this.i2e1Ques[i].optionsOrder)\r\n                                this.i2e1Ques[i].displayIndex = this.i2e1Ques[i].optionsOrder.indexOf(this.i2e1Ques[i].answer) + 1;\r\n                            else\r\n                                this.i2e1Ques[i].displayIndex = 0;\r\n                            break;\r\n                        case 2:\r\n                            for (var j = 0; j < this.i2e1Ques[i].options.length; j++) {\r\n                                //var answered = questions.find('[name=answer-' + this.i2e1Ques[i].options[j].id + ']:checked');\r\n                                //if (answered.length === 1)\r\n                                //{\r\n                                    this.i2e1Ques[i].selectionTime = this.i2e1FirstAnsweredAt[this.i2e1Ques[i].id];\r\n                                    this.i2e1Ques[i].options[j].isSelected = true;\r\n                                    //$(this).closest(\"div.options-list\").children('p').toggle('display-none');\r\n                                    errAnswer = false;\r\n                                //}\r\n                            }\r\n                            break;\r\n                    }\r\n                    if (errAnswer) return errAnswer;\r\n                }\r\n                return errAnswer;\r\n            }\r\n        },\r\n        preLogin: function (landingPage, isAutoLogin) {\r\n            window.landingPage = landingPage;\r\n            this.$emit('swap-state','redirectionState');\r\n        },\r\n        getMobile: function() {\r\n            return Login.getMobile(this);\r\n        },\r\n        getNasAttributes: function () {\r\n            if (this.getMobile() && this.getMobile().length > 0) {\r\n                Axios.post('/Login/GetNasAttributes?login-user-session='+helper.getCookie('login_user_session'))\r\n                .then((response) => {\r\n                    if (response.data.data) {\r\n                        var info = response.data.data;\r\n                        if (info.vip)  {\r\n                            this.dataLeft = \"\";\r\n                            this.timeLeft = \"\";\r\n                        } else {\r\n                                                        var dataLeft = (info.dataLeft && info.dataLeft >= 0) ? info.dataLeft : 0;\r\n                            var timeLeft = info.timeLeft;\r\n                            var isVip = false;\r\n                            if (info.dataLeft >= 0 && dataLeft >= 0) {\r\n                            if (dataLeft >= 1073741824) //converting to GB\r\n                            {\r\n                                dataLeft = Helper.roundOffData(dataLeft / 1073741824) + \" GB\";\r\n                            }\r\n                            else if (dataLeft >= 1048576) //converting to MB\r\n                            {\r\n                                dataLeft = Helper.roundOffData(dataLeft / 1048576) + \" MB\";\r\n                            }\r\n                            else if (dataLeft >= 1024) //converting to KB\r\n                            {\r\n                                dataLeft = Helper.roundOffData(dataLeft / 1024) + \" KB\";\r\n                            } else if (dataLeft === 0) {\r\n                                dataLeft = '';\r\n                            }\r\n                                this.dataLeft = dataLeft;\r\n                            } \r\n                            else\r\n                                this.dataLeft = \"\"; \r\n                            if (timeLeft && timeLeft >= 0) {\r\n                                timeLeft = Helper.roundOffTime(timeLeft);\r\n                                if (timeLeft == 0)\r\n                                    this.timeLeft = \"\";\r\n                                else\r\n                                    this.timeLeft = timeLeft;\r\n                            }\r\n                        }\r\n                        this.$emit('session-attributes', { dataLeft: this.dataLeft, timeLeft: this.timeLeft });\r\n                    }\r\n                })\r\n                .catch((error) => {\r\n                    this.dataLeft = \"\";\r\n                    this.timeLeft = \"\";\r\n                });\r\n            }\r\n            else {\r\n                //\r\n            }\r\n        }\r\n\r\n\r\n    }\r\n}\r\n</script>", "<template>\r\n    <div id=\"primary-otp-area\" class=\"otp-area\">\r\n        <div class=\"otp-title\">{{ $t('secondState.enter_4_digit_password') }}</div>\r\n        <div class=\"group\" id=\"otp-group\">\r\n            <input type=\"number\" min=\"0\" max=\"9\" ref=\"digit1\" maxlength=\"1\" v-model=\"digit1\" />\r\n            <input type=\"number\" min=\"0\" max=\"9\" ref=\"digit2\" maxlength=\"1\" v-model=\"digit2\" />\r\n            <input type=\"number\" min=\"0\" max=\"9\" ref=\"digit3\" maxlength=\"1\" v-model=\"digit3\" />\r\n            <input type=\"number\" min=\"0\" max=\"9\" ref=\"digit4\" maxlength=\"1\" v-model=\"digit4\" />\r\n        </div>\r\n    </div>\r\n</template>\r\n\r\n<script>\r\n    export default {\r\n        name: 'OTPInput',\r\n        emits: ['valid','invalid'],\r\n        data: function () {\r\n            return {\r\n                valid: [\"0\", \"1\", \"2\", \"3\", \"4\", \"5\", \"6\", \"7\", \"8\", \"9\"],\r\n                digit1: \"\",\r\n                digit2: \"\",\r\n                digit3: \"\",\r\n                digit4: \"\",\r\n                otp: \"\"\r\n            }\r\n\r\n        },\r\n        mounted: function() {\r\n            this.$refs.digit1.focus();\r\n        },\r\n        methods: {\r\n            validate: function () {\r\n                this.otp = this.digit1+this.digit2+this.digit3+this.digit4;\r\n                if (this.otp.length == 4 && parseInt(this.otp) > 999 && parseInt(this.otp) < 10000) {\r\n                    this.$emit('valid', this.otp);\r\n                } else {\r\n                    this.$emit('invalid');\r\n                }\r\n            }\r\n        },\r\n        watch: {\r\n            digit1() {\r\n                if (this.digit1.length > 1)\r\n                    this.digit1 = this.digit1.substring(0, 1);\r\n                if (this.digit1.length == 1)\r\n                    if (this.valid.indexOf(this.digit1) < 0)\r\n                        this.digit1 = \"\";\r\n                    else\r\n                        this.$refs.digit2.select();\r\n                this.validate();\r\n            },\r\n            digit2() {\r\n                if (this.digit2.length > 1)\r\n                    this.digit2 = this.digit2.substring(0, 1);\r\n                if (this.digit2.length == 1)\r\n                    if (this.valid.indexOf(this.digit2) < 0)\r\n                        this.digit2 = \"\";\r\n                    else\r\n                        this.$refs.digit3.select();\r\n                if (this.digit2.length == 0)\r\n                    this.$refs.digit1.select();\r\n                this.validate();\r\n            },\r\n            digit3() {\r\n                if (this.digit3.length > 1)\r\n                    this.digit3 = this.digit3.substring(0, 1);\r\n                if (this.digit3.length == 1)\r\n                    if (this.valid.indexOf(this.digit3) < 0)\r\n                        this.digit3 = \"\";\r\n                    else\r\n                        this.$refs.digit4.select();\r\n                if (this.digit3.length == 0)\r\n                    this.$refs.digit2.select();\r\n                this.validate();\r\n            },\r\n            digit4() {\r\n                if (this.digit4.length > 1)\r\n                    this.digit4 = this.digit4.substring(0, 1);\r\n                if (this.digit4.length == 1)\r\n                    if (this.valid.indexOf(this.digit4) < 0)\r\n                        this.digit4 = \"\";\r\n                if (this.digit4.length == 0)\r\n                    this.$refs.digit3.select();\r\n                this.validate();\r\n            },\r\n        }\r\n    }\r\n</script>", "import { render } from \"./OTPInput.vue?vue&type=template&id=35bde2f1\"\nimport script from \"./OTPInput.vue?vue&type=script&lang=js\"\nexport * from \"./OTPInput.vue?vue&type=script&lang=js\"\nscript.render = render\n\nexport default script", "import { render } from \"./SecondState.vue?vue&type=template&id=a661662a\"\nimport script from \"./SecondState.vue?vue&type=script&lang=js\"\nexport * from \"./SecondState.vue?vue&type=script&lang=js\"\nscript.render = render\n\nexport default script", "<template>\r\n    <form class=\"form redirecting-state state-transition\">\r\n        <div class=\"no-offers\">\r\n            <div id=\"greeting\">\r\n                <img v-bind:src=\"i2e1Data.loginUser.cdnDomain + 'images/wiom/welldone.png'\" style=\"vertical-align: text-bottom;height:30px;\">\r\n                <span>{{ $t('redirectionState.please_wait') }}</span>\r\n            </div>\r\n            <div class=\"datamessage\" v-html=\"$t('redirectionState.data_policy', { data: sessionAttributes.dataLeft })\">\r\n            </div>\r\n            <div v-show=\"sessionAttributes.timeLeft.length > 0\" class=\"timemessage\" v-html=\"$t('redirectionState.time_policy', { time: sessionAttributes.timeLeft })\"></div>\r\n            <div v-show=\"sessionAttributes.timeLeft.length == 0\" class=\"timemessage\" v-html=\"$t('redirectionState.time_policy', { time: 'NA' })\"></div>\r\n            <hr />\r\n            <div class=\"connected\">\r\n                <div>{{ $t('redirectionState.you_are_almost_there') }}</div>\r\n            </div>\r\n        </div>\r\n        <div class=\"offers\" v-if=\"options && options.offers && options.offers.length\">\r\n            <div class=\"campaigns\"></div>\r\n            <input id=\"skipVoucher2\" class=\"skip-voucher small_button primary\" type=\"button\" value=\"Skip\" @click=\"skip()\" />\r\n        </div>\r\n    </form>\r\n</template>\r\n\r\n<script>\r\nimport Axios from './../../plugins/axios';\r\nimport Survey from './../Common/Survey/Survey';\r\nimport helper from './../../utils/Helper';\r\nimport Login from './../../Libs/Login';\r\n\r\nexport default {\r\n    name: 'RedirectionState',\r\n    emits: ['swap-state','got-mobile'],\r\n    props: ['i2e1Data', 'options', 'sessionAttributes'],\r\n    mounted: function() {\r\n        this.onMount();\r\n    },\r\n    data: function () {\r\n        return {\r\n            questionType: [3],\r\n            i2e1Ques: [],\r\n            validUsername: null,\r\n            mobile: null,\r\n            state: 'firstState',\r\n            disableCTA: false\r\n        }\r\n    },\r\n    methods: {\r\n        onMount: function(){\r\n            if (this.options && this.options.offers && this.options.offers.length) {\r\n                //this._showVouchers(this.options.offers);\r\n                //document.getElementById('skipVoucher2').onclick = function () {\r\n                    //_logMPEvent(_i2e1Constants.offerState, {\r\n                    //    event: 'State Skipped'\r\n                    //});\r\n                    //_progress(progressBar, 90, 15000);\r\n\r\n                    //this._doLogin();\r\n                //}\r\n            } else {\r\n                //_progress(progressBar, 90, 15000);\r\n                Login.doLogin();\r\n            }\r\n\r\n        },\r\n        skip: function() {\r\n            Login.doLogin();\r\n        }\r\n    }\r\n}\r\n</script>", "import { render } from \"./RedirectionState.vue?vue&type=template&id=7a4be980\"\nimport script from \"./RedirectionState.vue?vue&type=script&lang=js\"\nexport * from \"./RedirectionState.vue?vue&type=script&lang=js\"\nscript.render = render\n\nexport default script", "<template>\r\n    <div class=\"footer\">\r\n        <div class=\"top-border\"></div>\r\n        <span v-html=\"$t('footer.facing_issues_call_us_at')\"></span>\r\n        <div class=\"stepper\">\r\n            <div class=\"step\" v-bind:class=\"{ visited: state === 'firstState' }\"></div>\r\n            <div class=\"connector\">\r\n                <div></div>\r\n            </div>\r\n            <div class=\"step\" v-bind:class=\"{ visited: state === 'secondState' }\"></div>\r\n            <div class=\"connector\">\r\n                <div></div>\r\n            </div>\r\n            <div class=\"step\"  v-bind:class=\"{ visited: state === 'thirdState', visited: state === 'redirectionState' }\"></div>\r\n        </div>\r\n        \r\n    </div>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n    name: 'Footer',\r\n    props: ['state'],\r\n    data: function() {\r\n        return {\r\n\r\n        }\r\n    }\r\n}\r\n</script>", "import { render } from \"./Footer.vue?vue&type=template&id=35b5a8c9\"\nimport script from \"./Footer.vue?vue&type=script&lang=js\"\nexport * from \"./Footer.vue?vue&type=script&lang=js\"\nscript.render = render\n\nexport default script", "import { render } from \"./LoginContainer.vue?vue&type=template&id=7690ab93\"\nimport script from \"./LoginContainer.vue?vue&type=script&lang=js\"\nexport * from \"./LoginContainer.vue?vue&type=script&lang=js\"\nscript.render = render\n\nexport default script", "import { render } from \"./App.vue?vue&type=template&id=2cf1f524\"\nimport script from \"./App.vue?vue&type=script&lang=js\"\nexport * from \"./App.vue?vue&type=script&lang=js\"\nscript.render = render\n\nexport default script", "import { createApp } from \"vue\";\nimport App from \"./App.vue\";\n//import Axios from \"./plugins/axios\";\n//import Async from \"async\";\nimport { createI18n } from \"vue-i18n\";\n//import locales from \"./localization/locales\";\n\nfunction loadLocaleMessages() {\n  const locales = require.context(\n    \"./locales\",\n    true,\n    /[A-Za-z0-9-_,\\s]+\\.json$/i\n  );\n  const messages = {};\n  locales.keys().forEach((key) => {\n    const matched = key.match(/([A-Za-z0-9-_]+)\\./i);\n    if (matched && matched.length > 1) {\n      const locale = matched[1];\n      messages[locale] = locales(key);\n    }\n  });\n  return messages;\n}\n\nconst i18n = createI18n({\n  locale: window.viewBag.resources.Culture.split(\"-\")[0],\n  fallbackLocale: \"en\",\n  messages: loadLocaleMessages(),\n});\n\nconst app = createApp(App);\n\n//app.Axios = Axios;\n//app.Async = Async;\n//app.i18n = i18n;\n\napp.use(i18n);\napp.mount(\"#app\");\n"], "sourceRoot": ""}