<svg xmlns="http://www.w3.org/2000/svg" width="58" height="58" viewBox="0 0 58 58">
    <defs>
        <filter id="Ellipse_850" width="58" height="58" x="0" y="0" filterUnits="userSpaceOnUse">
            <feOffset dy="3"/>
            <feGaussianBlur result="blur" stdDeviation="3"/>
            <feFlood flood-opacity=".161"/>
            <feComposite in2="blur" operator="in"/>
            <feComposite in="SourceGraphic"/>
        </filter>
        <style>
            .cls-2{fill:none}
        </style>
    </defs>
    <g id="Go_Button" transform="translate(-295 -153)">
        <g filter="url(#Ellipse_850)" transform="translate(295 153)">
            <g id="Ellipse_850-2" fill="#fbfbfb" stroke="#f36150" transform="translate(9 6)">
                <circle cx="20" cy="20" r="20" stroke="none"/>
                <circle cx="20" cy="20" r="19.5" class="cls-2"/>
            </g>
        </g>
        <g id="outline-arrow_back-24px" transform="rotate(180 168 95.5)">
            <path id="Path_5973" d="M0 0h24v24H0z" class="cls-2"/>
            <path id="Path_5974" fill="#f36150" d="M20 11H7.83l5.59-5.59L12 4l-8 8 8 8 1.41-1.41L7.83 13H20z"/>
        </g>
    </g>
</svg>
