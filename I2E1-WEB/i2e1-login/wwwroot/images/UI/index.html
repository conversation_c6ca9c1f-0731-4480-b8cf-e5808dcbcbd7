<!DOCTYPE html><html lang="en"><head><meta charset="utf-8"><meta http-equiv="X-UA-Compatible" content="IE=edge"><meta name="viewport" content="width=device-width,initial-scale=1"><meta name="theme-color" content="#ffffff"><link rel="icon" href="/favicon.ico"><link rel="stylesheet" href="/site.css"><title>i2e1 login</title><script>window.onerror = function (message, source, lineno, colno, error) {
          $.ajax({
              type: "POST",
              url: "/Login/LogJsError",
              data: {
                  message: message,
                  source: source,
                  lineno: lineno,
                  colno: colno,
                  error: btoa(error.stack),
                  url: window.location.href || document.referrer
              }
          })
      };
      /*
      var jsErrorLogger = function (message, source, data) {
          $.ajax({
              type: "POST",
              url: "/Login/LogJsError",
              data: {
                  message: message,
                  source: source,
                  lineno: 0,
                  colno: 0,
                  error: btoa(JSON.stringify(data)),
                  url: window.location.href || document.referrer
              }
          })
      };
      */
      var _i2e1Domain = 'https://' + window.location.hostname + (window.location.port ? (':' + window.location.port) : '') + '/';
      window.cdnDomain = 'https://' + window.location.hostname + ':44300/';
  
      window.i2e1Constants = {
          pageLoad: 'Page Load',
          firstState: 'First State',
          secondState: 'Second State',
          questionState: 'Question State',
          offerState: 'Offer State',
          socialState: 'Facebook State',
          fbLoginState: 'Facebook Login State',
          facebookRedirectedState: 'Facebook Redirected State',
          mobileConnectRedirectedState: 'Mobile Connect Error State',
          redirectionState: 'Redirection State',
          helpState: 'Help State',
          stateChange: 'State Change',
          errorState: 'Error State',
          customState: 'Custom State',
          paymentState: 'Payment State',
          landingState: 'Landing State',
          swappPromoState: 'Swapp State',
          userProfile: 'User Profile',
          stateOpened: 'State Opened',
          sharePressed: 'Share Pressed',
          doingLogin: 'Doing Login',
          generateOTPPressed: 'Generate OTP Pressed',
          submitOTPPressed: 'Submit OTP Pressed',
          resendOTPPressed: 'Resend OTP Pressed',
          error: 'Error Occured',
          invalidAnswer: 'Invalid Answer',
          questionAsked: 'Question Asked',
          offerShown: 'Offer Shown',
          offerClicked: 'Offer Clicked',
          submitPressed: 'Submit Pressed',
          generalEvent: 'i2e1 Website',
          nationalIdSignInClicked: 'National Id Sign In Clicked',
          lastNameRoomSigninClicked: 'Last Name & Room Sign In Clicked',
          djuboSignInPressed: 'Djubo Sign In Pressed',
          menuClicked: 'Menu Clicked',
          tncClicked: 'TnC Clicked',
          tileClicked: 'Tile Clicked',
          dataVoucherState: 'Data Voucher State',
          storeDetailState: 'Store Detail State',
          submitDIYREGPressed: 'Submit Detail pressed',
          macConfirmationState: 'Mac Confirm State'
      }
    /*
      var _logMPEvent = function (state, properties) {
          var getFlow = function () {
              var clientAuthType = _getUrlParams().clientAuthType
              switch (clientAuthType) {
                  case 0:
                  case 3:
                  case 5:
                  case 6:
                  case 12:
                  case 13:
                  case 14:
                      return 'PHONE'; break;
                  case 2: return 'NATIONAL_ID'; break;
                  case 8: return 'LAST_NAME_ROOM_NO'; break;
                  default: return 'N/A';
              }
          }
  
          properties = properties || {};
          var params = _getUrlParams();
  
          var bigQueryObject = [{
              id: 2222,
              session_id: params.sessionid,
              router_nas_id: params.nasid,
              mac_id: params.mac,
              mobile: _getMobile(),
              event_source: 'USER_INTERFACE',
              event_type: properties.event,
              response: state,
              added_time: new Date()
          }];
  
          try {
              window.socket_send_data(JSON.stringify({
                  sid: window.socket_id,
                  request: false,
                  bgtb: bigQueryObject
              }));
          }
          catch (err) { }
      }
  
  
  
  
      var wsDomain = 'www.i2e1.in';
      var _rowdata = null;
      window._wsUrl = 'wss://' + wsDomain + ':8081/wsee';
      window.socket_id = 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (c) {
          const r = Math.random() * 16 | 0,
              v = c === 'x' ? r : (r & 0x3 | 0x8);
          return v.toString(16);
      });
  
      var _wsCon = new WebSocket(window._wsUrl);
      window._wsStatus = false;
      _wsCon.onopen = function () {
          window._wsStatus = true;
          if (_rowdata) window.socket_send_data(_rowdata);
              _rowdata = null;
          console.log("new connection setup");
      };
      _wsCon.onerror = function (err) {
          console.log("Got error", err);
          _wsCon.close();
      };
  
      _wsCon.onclose = function (evt) {
          console.log("connection closed by server");
          window._wsStatus = false;
      };
  
      window.socket_send_data = function(rowdata){
          try {
              if (!window._wsStatus) {
                  _rowData = rowdata;
                   window._wsStatus = false;
                  console.log('socket reconnecting...');
                  _wsCon = new WebSocket(window._wsUrl);
                  _wsCon.onopen = function () {
                      window._wsStatus = true;
                      if (_rowdata) window.socket_send_data(_rowdata);
                      _rowdata = null;
                      console.log("new connection setup");
                  };
                  _wsCon.onerror = function (err) {
                      console.log("Got error", err);
                      _wsCon.close();
                  };
  
                  _wsCon.onclose = function (evt) {
                      console.log("connection closed by server");
                      window._wsStatus = false;
                  };
  
                  setTimeout(function () {
                      try {
                          _wsCon.send(rowdata);
                      }
                      catch (ex) {
                          console.log("Exception sending event to event collector : " + JSON.stringify(ex));
                      }
                  }, 500)
              }
              else
                  _wsCon.send(rowdata);
          }
          catch (ex) {
              console.log("Exception sending event to event collector : " + JSON.stringify(ex));
          }
                 
      };
    */</script><script>window.viewBag = {
          redirectUrl: '',
          loginResponse: null,
          initialState: '',
          mobile: '',
          sessionExpired: '',
          dataExhausted: '',
          facebookPage: '',
          facebookCheckin: '',
          facebookAppId: 585927451601407,
          loginLogo: '',
          swapLink: 'https://hg7q2.app.goo.gl/?link=http%3a%2f%2fgetlinq.in%3fmobile%3d%26name%3d%26ssid%3d&apn=com.i2e1.swapp&utm_source=i2e1&utm_medium=Browser&utm_campaign=LoginFlow&afl=https%3a%2f%2fplay.google.com%2fstore%2fapps%2fdetails%3fid%3dcom.i2e1.swapp%26referrer%3dutm_source%253di2e1%2526utm_medium%253dBrowser%2526utm_campaign%253dLoginFlow',
          resources:{"ResourceManager":null,"Culture":"hi","dataVoucherState_choose_your_plan":"अपना प्लान चुनें","dataVoucherState_connect":"जुड़ें","dataVoucherState_enter_voucher_code":"वाउचर कोड डालें","dataVoucherState_free_internet":"फ्री इंटरनेट","dataVoucherState_use_coupon":"उपयोगकर्ता का कूपन","errorState_try_logging_again":"दोबारा लॉगिन करें","firstState_10_digit_phone_error":"फोन नंबर में 10 अंक होने चाहिए","firstState_bark_lessloud":"मोबाइल नंबर से साइन इन करें","firstState_bark_loud":"आजीवन फ्री WiFi पाने के लिए","firstState_connect":"जुड़ें","firstState_continue_as":"जारी रखने के लिए अपना मोबाइल नंबर डालें","firstState_dont_have_national_id_click_here":"पासपोर्ट या सरकारी पहचान पत्र नहीं है? <a href=\"/Login/GetAuthenticationPage/?authType=PHONE\">यहाँ क्लिक करें</a>","firstState_dont_have_phno_click_here":"क्या आपके पास फ़ोन नंबर नहीं है? <a href=\"/Login/GetAuthenticationPage/?authType=NATIONAL_ID\">यहाँ क्लिक करें</a>","firstState_enter_national_id":"पासपोर्ट या सरकारी पहचान पत्र दें","firstState_enter_phone_number":"उदाहरण: 8880322222","firstState_fdm_notauthorised":"Please Contact Reception for Internet Access","firstState_generate_otp":"साइन इन करें","firstState_invalid_phno":"आपका नंबर अमान्य है","firstState_last_name":"अपना उपनाम डालें","firstState_mobile_invalid":"गलत मोबाइल नंबर","firstState_or":"या","firstState_room_no":"कमरा क्रमांक","firstState_sign_in_as_guest":"अथिति के रूप में लॉग इन करें","firstState_tnc":"जारी रखकर आप <a onclick='_onTileClick(\"/Templates/i2e1/tnc.html\")'>नियम और शर्तें</a> स्वीकार करते हैं","firstState_welcome_back":"पुनः पधारने पर आपका स्वागत है","footer_facing_issues_call_shopkeeper":"सहायता के लिए दूकानदार से संपर्क करें","footer_facing_issues_call_us_at":"मदद लें  - <b><a href=\"tel:8880322222\">8880322222</a></b>","help":"सहायता के लिए यहाँ क्लिक करें","helpState_enter_mobile_number":"मोबाइल नंबर डालें","helpState_enter_password":"चार अंकों का पासवर्ड डालें","helpState_help":"सहायता के लिए यहाँ क्लिक करें","helpState_i2e1_connects_you_to_wifi":"i2e1 आपको मुफ्त वाई-फाई से जोड़ता है","helpState_three_simple_steps_to_wifi":"वाई-फाई प्राप्त करने हेतु तीन आसान कदम","landingState_congratulations":"बधाई हो|","landingState_connected_msg":"आप इंटरनेट से जुड़ चुके हैं","landingState_data_left":"Data left","landingState_data_left_prefix":"","landingState_data_left_suffix":" फ्री WiFi का इस्तेमाल करें!","landingState_logout":"लॉग आउट","landingState_search":"खोजें","landingState_time_day":"दिन","landingState_time_days":"दिन","landingState_time_hr":"घंटा","landingState_time_hrs":"घंटा","landingState_time_left":"बचा हुआ समय","landingState_time_lessthan_a_minute":"एक मिनट से कम","landingState_time_min":"मिनट","landingState_time_mins":"मिनट","landingState_you_are_now_connected":"आप इंटरनेट से जुड़ चुके हैं","logout":"लॉग आउट","please_configure_nas_id":"कृपया वाई-फाई पहचानकर्ता को कॉन्फ़िगर करें","redirectionState_data_policy":"<span class=\"data\">1 GB</span> फ्री WiFi का इस्तेमाल करें","redirectionState_please_wait":"बहुत बढ़िया!","redirectionState_time_policy":"समय अवधि: <span class=\"time\">1 घंटा</span>","redirectionState_you_are_almost_there":"आपको 5 सेकंड में जोड़ा जा रहा है…","secondState_back":"फ़ोन नंबर बदलने के लिए पीछे जाएं","secondState_bark_lessloud":"आपके SMS पर भेजे गए (OTP) पासवर्ड से","secondState_bark_loud":"नंबर की पुष्टि करें","secondState_captcha":"कैप्चा भरें","secondState_change":"नंबर बदलें","secondState_confirm":"पुष्टि करें","secondState_connect":"फ्री WiFi से जुड़ें","secondState_enter_4_digit_password":"४ अंको का पासवर्ड डालें","secondState_enter_4_digit_password_sent_to_mobile":"{0} पे भेजा गया पासवर्ड डालें","secondState_enter_captcha":"कैप्चा भरें","secondState_enter_voucher_code":"वाउचर कोड डालें","secondState_not_getting_otp_click_here":"ओटीपी नहीं आने पर <a onclick=\"$('#otp_access_code').show();$('#otp').val('1234').hide()\">यहाँ क्लिक करें</a>","secondState_otp_invalid":"गलत OTP","secondState_questions_cta_okay":"ठीक है","secondState_questions_lessloud":"आसान सवालों के जवाब दे कर","secondState_questions_loud":"<b>1 GB</b> मुफ्त इंटरनेट प्राप्त करें","secondState_resend":"फिर से भेजें","secondState_welcomeback_greetings":"<img src=\"../../images/wiom/welcomeback.hi.svg\" />"},
          welcomeBack: false,
          posistNases: [8900, 11853]
      }
      /*
      if (_viewBag.sessionExpired) {
          window.parent.postMessage({
              action: '_sessionExpired'
          }, '*');
      } else if (_viewBag.dataExhausted) {
          window.parent.postMessage({
              action: '_dataExhausted'
          }, '*');
      }
      var _landingSearch = function () {
          if (!document.getElementById('search').value) return;
          if (window.postMessage)
              window.parent.postMessage({
                  action: '_fullRedirect',
                  link: 'https://www.google.com/search?q=' + encodeURIComponent(document.getElementById('search').value)
              }, '*');
          else window.open(event.data.link);
      }
      $(document).ready(function () {
          document.querySelector('meta[name="accept-language"]').setAttribute("content", _viewBag.resources.Culture);
          $(".language-selectors a").show();
          $('.language-selectors').find('#' + _viewBag.resources.Culture.split('-')[0]).hide();
      });
      */</script><script>var _otpMsg = "Enter 4 digit password";
      window.loginUser = {"mobile":"8130036980","name":"Anugrah Adams","gender":"","email":"","mac":"60:57:18:43:C0:BA","called":"C4:E9:84:D5:1D:22","storegroupid":15,"templateid":[479],"combinedSettingId":1006116,"userGroupId":0,"sessionid":"fd8d474a-169e-470d-adcd-9452b5265825","uamip":"********","uamport":"3990","appId":null,"controllertype":0,"smscount":0,"otpSubmitDate":"2020-12-09T12:58:03.517","isLoggedOut":false,"otp":"1234","res":4,"smsapi":null,"challenge":"bc16377fdc43d2125dac3532766a321b","power":0,"clientAuthType":0,"askaccesscode":false,"guestmodeswitched":false,"attributes":{},"planCharge":0,"locale":null,"welcomeBack":false,"controllerId":1,"backEndNasid":7311,"nasid":"7311"};
      var _questionSequence = {"479-14":{"DS":{"23":7,"24":7,"25":7},"FQ":14,"LQ":0,"NQ":7,"randomized":false,"binarySplit":false},"479-7":{"DS":{"2":334,"4":334,"1":334,"3":334},"FQ":14,"LQ":14,"NQ":334,"randomized":false,"binarySplit":false},"479-334":{"DS":{"945":249,"946":249,"947":249,"948":249},"FQ":7,"LQ":7,"NQ":249,"randomized":false,"binarySplit":false},"479-249":{"DS":{"726":335,"727":335,"729":335,"728":335},"FQ":7,"LQ":334,"NQ":335,"randomized":false,"binarySplit":false},"479-335":{"DS":{"949":336,"950":336,"951":336},"FQ":7,"LQ":249,"NQ":336,"randomized":false,"binarySplit":false},"479-336":{"DS":{"952":337,"953":337,"954":337},"FQ":7,"LQ":335,"NQ":337,"randomized":false,"binarySplit":false},"479-337":{"DS":{"955":234,"956":234,"957":234},"FQ":7,"LQ":336,"NQ":234,"randomized":false,"binarySplit":false},"479-234":{"DS":{"675":263,"676":263,"677":263,"678":263},"FQ":7,"LQ":337,"NQ":263,"randomized":false,"binarySplit":false},"479-263":{"DS":{"779":338,"780":338,"782":338,"781":338},"FQ":7,"LQ":234,"NQ":338,"randomized":false,"binarySplit":false},"479-338":{"DS":{"958":339,"959":339,"960":339},"FQ":7,"LQ":263,"NQ":339,"randomized":false,"binarySplit":false},"479-339":{"DS":{"961":340,"962":340,"963":340,"964":340},"FQ":7,"LQ":338,"NQ":340,"randomized":false,"binarySplit":false},"479-340":{"DS":{"965":341,"966":341,"967":341},"FQ":7,"LQ":339,"NQ":341,"randomized":false,"binarySplit":false},"479-341":{"DS":{"968":342,"969":342,"970":342},"FQ":7,"LQ":340,"NQ":342,"randomized":false,"binarySplit":false},"479-342":{"DS":{"971":0,"972":0,"973":0,"974":0},"FQ":7,"LQ":341,"NQ":0,"randomized":false,"binarySplit":false}};
      if (!_questionSequence) {
          _questionSequence = {"0-8":{"DS":{"506":14,"507":14,"508":14,"509":14},"FQ":8,"LQ":5,"NQ":14,"randomized":false,"binarySplit":false},"0-14":{"DS":{"23":7,"24":158,"25":159},"FQ":8,"LQ":5,"NQ":157,"randomized":false,"binarySplit":false},"0-159":{"DS":{"461":157,"462":157,"463":157,"464":157},"FQ":8,"LQ":5,"NQ":157,"randomized":false,"binarySplit":false},"0-157":{"DS":{"453":18,"454":15,"455":18,"456":18},"FQ":8,"LQ":5,"NQ":1,"randomized":false,"binarySplit":false},"0-15":{"DS":{"26":18,"27":18,"28":18,"29":18},"FQ":8,"LQ":5,"NQ":1,"randomized":false,"binarySplit":false},"0-1":{"DS":{"58":31,"59":31,"60":31,"61":31},"FQ":8,"LQ":5,"NQ":31,"randomized":false,"binarySplit":false},"0-31":{"DS":{"116":34,"117":34,"118":34,"119":34},"FQ":8,"LQ":5,"NQ":34,"randomized":false,"binarySplit":false},"0-34":{"DS":{"132":35,"133":35},"FQ":8,"LQ":5,"NQ":35,"randomized":false,"binarySplit":false},"0-35":{"DS":{"134":3,"135":3,"136":3,"137":3},"FQ":8,"LQ":5,"NQ":3,"randomized":false,"binarySplit":false},"0-3":{"DS":{"138":4,"139":4,"140":4,"141":4},"FQ":8,"LQ":5,"NQ":4,"randomized":false,"binarySplit":false},"0-4":{"DS":{"146":37,"147":37,"148":37,"149":37},"FQ":8,"LQ":5,"NQ":37,"randomized":false,"binarySplit":false},"0-37":{"DS":{"162":38,"163":38,"164":38,"165":38},"FQ":8,"LQ":5,"NQ":38,"randomized":false,"binarySplit":false},"0-38":{"DS":{"166":39,"167":39,"168":39},"FQ":8,"LQ":5,"NQ":39,"randomized":false,"binarySplit":false},"0-39":{"DS":{"169":40,"170":40},"FQ":8,"LQ":5,"NQ":40,"randomized":false,"binarySplit":false},"0-40":{"DS":{"171":41,"172":41,"173":41},"FQ":8,"LQ":5,"NQ":41,"randomized":false,"binarySplit":false},"0-41":{"DS":{"174":43,"175":43,"503":43},"FQ":8,"LQ":5,"NQ":43,"randomized":false,"binarySplit":false},"0-43":{"DS":{"179":5,"180":5,"181":5,"182":5},"FQ":8,"LQ":5,"NQ":5,"randomized":false,"binarySplit":false},"0-5":{"DS":{"195":0,"196":0,"197":0,"198":0},"FQ":8,"LQ":5,"NQ":0,"randomized":false,"binarySplit":false}};
      }
      // Code to Handle Case if Autologin Enabled
      window.viewBag.welcomeBack = false;
      window.viewBag.globalOtp_Enabled= 'False';
      window.viewBag.globalOTP_Enforce= '';
      window.viewBag.globalOTP_code= '';</script><link href="/js/app.ef44ce60.js" rel="preload" as="script"><link href="/js/chunk-vendors.f1c54fb9.js" rel="preload" as="script"></head><body><noscript><strong>We're sorry but i2e1-login-ui doesn't work properly without JavaScript enabled. Please enable it to continue.</strong></noscript><div id="app"></div><div class="display-none"><img src="../images/wiom/welldone.png"></div><div id="forignTemplates" class="display-none"><div id="forignTemplate_0"><style>body {

                background-color: #FFF600;
                background-size: cover;

                }

                .outer-card img {
                width: 45%;
                margin-bottom: 0rem;
                }
                .outer-card{
                width: 20rem;
                border-radius: 2px;
                margin-left: auto;
                margin-right: auto;
                padding: 0;
                margin-top: 2rem;
                height: 5.5rem;
                }
                .login.container{
                margin-top: 5rem;
                background-color: #ffffff !important;
                }

                .login.container .inner-header {
                text-align: left;
                }

                .login.container .inner-header img {
                height: 50px;
                }

                .login.container .login_button {
                background-color: #00a4e4;
                color: #fff;        
                border-radius: 4px;
                font-size: 1rem;
                border: 1px solid #00a4e4;
                width: 100%;
                font-weight: 600;
                }

                .login.container .tnc {
                font-weight: 700;
                font-size: .8rem;
                }

                .login.container .tnc > a {
                color: rgba(255, 0, 0, 0.79);
                }

                .login.container .tnc > a:hover {
                color: red;
                text-decoration: underline;
                }

                .footer span a {
                color: #2e2e2e !important;
                font-family: 'Open Sans', sans-serif !important;
                font-weight: 300;
                font-size: small;
                }
                .landing.container{
                background-color: rgba(255, 255, 255, 0.72) !important;
                }

                @media (max-width: 420px){

                body{
                background-position: -5.6rem;
                }
                }</style><outer-header><img src="https://s3.ap-south-1.amazonaws.com/i2e1-client-data/client-data/client_375/partner_1706/template/f_636746897050066922_Burger_Sing_MII.png"></outer-header><inner-header></inner-header><generate-otp><input type="button" class="login_button" value="Next" onclick="_generateOTP(false)"> <span class="tnc">By clicking "Next" you agree to our <a target="_blank" href="/Templates/i2e1/tnc.html">Terms &amp; Conditions</a></span></generate-otp><access-code-connect-button><input type="button" class="login_button" value="Sign in" onclick="_generateOTP(false)"> <span class="tnc">By clicking "Next" you agree to our <a target="_blank" href="/Templates/i2e1/tnc.html">Terms &amp; Conditions</a></span></access-code-connect-button><room-no-connect-button><input type="button" class="login_button" value="Sign in" onclick="_generateOTP(false)"> <span class="tnc">By clicking "Next" you agree to our <a target="_blank" href="/Templates/i2e1/tnc.html">Terms &amp; Conditions</a></span></room-no-connect-button><connect-button><input type="button" class="login_button" value="Sign in" onclick="_connect(false)"></connect-button></div></div><script src="/js/chunk-vendors.f1c54fb9.js"></script><script src="/js/app.ef44ce60.js"></script></body></html>