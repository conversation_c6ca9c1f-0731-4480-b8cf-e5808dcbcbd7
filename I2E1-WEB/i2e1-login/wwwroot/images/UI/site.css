@font-face {
  font-family: 'Roboto';
  src: url('fonts/Roboto/Roboto-Regular-webfont.eot');
  src: url('fonts/Roboto/Roboto-Regular-webfont.eot?#iefix') format('embedded-opentype'), url('fonts/Roboto/Roboto-Regular-webfont.woff') format('woff'), url('fonts/Roboto/Roboto-Regular-webfont.ttf') format('truetype'), url('fonts/Roboto/Roboto-Regular-webfont.svg#RobotoRegular') format('svg');
  font-weight: normal;
  font-style: normal;
}
@font-face {
  font-family: 'Roboto-Medium';
  src: url('fonts/Roboto/Roboto-Medium-webfont.eot');
  src: url('fonts/Roboto/Roboto-Medium-webfont.eot?#iefix') format('embedded-opentype'), url('fonts/Roboto/Roboto-Medium-webfont.woff') format('woff'), url('fonts/Roboto/Roboto-Medium-webfont.ttf') format('truetype'), url('fonts/Roboto/Roboto-Medium-webfont.svg#RobotoRegular') format('svg');
  font-weight: normal;
  font-style: normal;
}
@font-face {
    font-family: 'Noto Sans Devanagari';
    src: url('fonts/Noto/NotoSansDevanagari-Regular.ttf') format('truetype');
    font-weight: normal;
    font-style: normal;
}

#app {
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  color: #2c3e50;
  margin-top: 60px;
}
@-webkit-keyframes bg {
  0% { background-position: 0% 52% }
  50% { background-position: 100% 49% }
  100% { background-position: 0% 52% }
}
@-moz-keyframes bg {
  0% { background-position: 0% 52% }
  50% { background-position: 100% 49% }
  100% { background-position: 0% 52% }
}
@keyframes bg {
  0% { background-position: 0% 52% }
  50% { background-position: 100% 49% }
  100% { background-position: 0% 52% }
}
.overlay{
  position: absolute;left: 0; top: 0; right: 0; bottom: 0;z-index: 2;
  background-size: 400% 400%;
  background-color: rgba(255,255,255,0.5);
  background: linear-gradient(125deg, rgba(239,79,130,0.5), rgba(244,105,58,0.6));
  -webkit-animation: bg 6s ease infinite;
  -moz-animation: bg 6s ease infinite;
  animation: bg 6s ease infinite;
  
}
.overlay-content {
    position: absolute;
    transform: translateY(-50%);
     -webkit-transform: translateY(-50%);
     -ms-transform: translateY(-50%);
    top: 50%;
    left: 0;
    right: 0;
    text-align: center;
    color: #555;
}
#loader {
  width: 60px;
}
.bump-up {
  top: -3px;
}
.partition {
  position: relative;
  margin: 1rem 0;
}
.partition .left-part,
.partition .right-part {
  border-bottom: 1.5px dashed rgba(140, 133, 133, 0.8);
  width: 46%;
  top: 50%;
  position: absolute;
}
.partition .left-part {
  left: 0;
}
.partition .right-part {
  right: 0;
}
html {
  font-size: 100%;
  height: 100%;
}
@media only screen and (max-width: 360px) {
  html {
    font-size: 82%;
  }
}
body {
    font-family: 'Roboto', Fallback, sans-serif;
    letter-spacing: 0.3px;
    margin: 0;
    padding: 0;
    overflow-y: auto;
    overflow-x: hidden;
    text-align: center;
    background-color: #f3f3f3;
    width: 100%;
}
body .barking {
  margin-top: 30px;
}
body .barking .loud {
  font-size: 20px;
  font-weight: 600;
  font-family: Roboto-Medium, Fallback, sans-serif !important;
}
body .barking .less-loud {
  font-size: 16px;
  font-weight: 400;
  font-family: Roboto-Medium, Fallback, sans-serif !important;
}
body.landing .login.container {
  background-color: transparent !important;
  box-shadow: 0 0 0 transparent;
  padding: 1rem 0;
}
body.landing .login.container .outer-card1 > img {
  height: 2rem !important;
}
body.landing .login.container .outer-card1 .open {
  display: none;
}
body.landing .login.container .outer-card1 .user-attributes {
  display: block;
  background: linear-gradient(180deg, #f4693a, #ef4f82);
  color: white;
  font-size: 0.8rem;
  height: 3rem;
  padding: 0 1rem;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
}
body.landing .login.container .outer-card1 .user-attributes .data {
  text-align: center;
  line-height: 3rem;
  font-size: 1.3rem;
  font-family: Roboto-Medium, fallback, sans-serif;
}
body.landing .login.container .outer-card1 .user-attributes div:not(.data):not(.time):not(.logout) {
  line-height: 0.75rem;
  color: #244c9c;
}
body.landing .login.container .outer-card1 .user-attributes .time {
  position: fixed;
  left: 1rem;
  top: 1rem;
  cursor: pointer;
}
body.landing .login.container .outer-card1 .user-attributes .logout {
  position: fixed;
  right: 1rem;
  top: 3.6rem;
  cursor: pointer;
  color: #ef5374;
}
body.landing .login.container form.landing-state {
  margin-top: -3rem;
  width: 22rem;
}
body.landing .footer {
  display: none;
}
.swap-promotion {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 4rem;
  background: rgba(255, 255, 255, 0.8);
  -o-transition: bottom 0.3s;
  -moz-transition: bottom 0.3s;
  -webkit-transition: bottom 0.3s;
  transition: bottom 0.3s;
  z-index: 100;
}
.swap-promotion > div {
  position: relative;
  width: 100%;
  height: inherit;
}
.swap-promotion > div .icon {
  position: absolute;
  width: 2.5rem;
  left: 0;
  top: 0;
  margin: 0.75rem;
  margin-left: 1rem;
}
.swap-promotion > div .text {
  position: absolute;
  top: 1rem;
  left: 4rem;
  right: 8.5rem;
  font-size: 0.85rem;
  text-align: left;
}
.swap-promotion > div .text swapp {
  color: #1693e0;
}
.swap-promotion > div .gp-link {
  position: absolute;
  right: 0;
  top: 0;
  width: 7rem;
  float: right;
  margin: 0.9rem;
}
.swap-promotion.display-none {
  display: block;
  bottom: -4rem;
  visibility: hidden;
  z-index: 1;
}
.swap-promotion-2 {
  border-radius: 2px;
  background-color: rgba(255, 255, 255, 0.8) !important;
  box-shadow: 1px 1px 2px rgba(0, 0, 0, 0.4);
  padding: 1rem;
  z-index: 100;
  -o-transition: opacity 1s;
  -moz-transition: opacity 1s;
  -webkit-transition: opacity 1s;
  transition: opacity 1s;
}
.swap-promotion-2 form {
  text-align: center;
  width: 20rem;
  margin-bottom: 0;
}
@media only screen and (min-width: 361px) and (max-width: 540px) {
  .swap-promotion-2 form {
    width: 18rem;
  }
}
.swap-promotion-2 hr {
  width: 20rem;
  border: none;
  border-bottom: 1px solid rgba(204, 204, 204, 0.48);
}
@media only screen and (min-width: 361px) and (max-width: 540px) {
  .swap-promotion-2 hr {
    width: 16rem;
  }
}
.swap-promotion-2 swapp {
  color: #1693e0;
  margin-right: 0.2rem;
}
.swap-promotion-2 p:not(.swapp) {
  font-weight: bold;
  font-size: 0.8rem;
}
.swap-promotion-2 p.swapp img {
  width: 2.5rem;
  vertical-align: middle;
}
.swap-promotion-2 img.gp-link {
  width: 8rem;
}
.swap-promotion-2 div.img img {
  width: 3.4rem;
  margin: 0 -0.2rem;
}
.swap-promotion-2.display-none {
  display: block;
  opacity: 0;
  visibility: hidden;
  z-index: 1;
}
input[type=number]::-webkit-inner-spin-button,
input[type=number]::-webkit-outer-spin-button {
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  margin: 0;
}
input::-ms-clear {
  display: none;
  width: 0;
  height: 0;
}
.saturate {
  -webkit-filter: saturate(3);
  filter: saturate(3);
}
.grayscale {
  -webkit-filter: grayscale(100%);
  filter: grayscale(100%);
}
.contrast {
  -webkit-filter: contrast(160%);
  filter: contrast(160%);
}
.brightness {
  -webkit-filter: brightness(0.25);
  filter: brightness(0.25);
}
.blur {
  -webkit-filter: blur(3px);
  filter: blur(3px);
}
.invert {
  -webkit-filter: invert(100%);
  filter: invert(100%);
}
.sepia {
  -webkit-filter: sepia(100%);
  filter: sepia(100%);
}
.huerotate {
  -webkit-filter: hue-rotate(180deg);
  filter: hue-rotate(180deg);
}
.rss.opacity {
  -webkit-filter: opacity(50%);
  filter: opacity(50%);
}
.mac_octet {
  width: 2rem !important;
  margin-right: 3px;
  font-size: 1.2rem !important;
  background-color: #eee !important;
  border-radius: 5px !important;
  height: 3rem !important;
  text-align: center;
  box-shadow: inset 0 0 5px 4px #ddd;
}
.circle {
  border: 0.5rem solid #F6F6F6;
  border-radius: 100rem;
  box-shadow: 1px 1px 2px rgba(0, 0, 0, 0.4);
  display: inline-block;
  position: relative;
  height: 5rem;
  width: 5rem;
}
@media only screen and (min-width: 361px) and (max-width: 540px) {
  .circle {
    font-size: 1rem;
  }
}
.circle .content {
  position: absolute;
  width: 100%;
  height: 100%;
  text-align: center;
  display: table;
}
.circle .content span.data {
  display: table-cell;
  vertical-align: middle;
  font-weight: bold;
}
.square {
  border: 0.5rem solid #F6F6F6;
  box-shadow: 1px 1px 2px rgba(0, 0, 0, 0.4);
  display: inline-block;
  position: relative;
  height: 5rem;
  width: 5rem;
}
@media only screen and (min-width: 361px) and (max-width: 540px) {
  .square {
    font-size: 1rem;
  }
}
.square .content {
  position: absolute;
  width: 100%;
  height: 100%;
  text-align: center;
  display: table;
}
.square .content span.data {
  display: table-cell;
  vertical-align: middle;
  font-weight: bold;
}
#loader .deactivate,
.loader .deactivate {
  position: fixed;
  width: 100%;
  height: 100%;
  background-color: white;
  top: 0;
  left: 0;
  opacity: 0.1;
  z-index: 99;
}
#loader .img-section,
.loader .img-section {
  position: fixed;
  left: 50%;
  top: 40%;
  z-index: 100;
}
#loader .img-section > div,
.loader .img-section > div {
  position: relative;
  left: -50%;
}
button,
input[type=button] {
  cursor: pointer;
}
[type=button].primary,
[type=submit].primary,
button.primary {
  background: linear-gradient(180deg, #f4693a, #ef4f82);
  color: white;
  border: 0;
  cursor: pointer;
  font-size: 16px;
  border-radius: 3px;
  height: 2.4rem;
}
[type=button].primary:hover,
[type=submit].primary:hover,
button.primary:hover {
  background: linear-gradient(180deg, #f4693a, #ef4f82);
}
button.wiom_login_button,
input.wiom_login_button {
  background: linear-gradient(180deg, #f4693a, #ef4f82);
  height: auto;
}
button.wiom_login_button:hover,
input.wiom_login_button:hover {
  background: #f4693a;
}
input[type=number],
input[type=text],
input[type=password],
input[type=email],
input[type=tel] {
  box-sizing: border-box;
  -moz-box-sizing: border-box;
  -webkit-box-sizing: border-box;
  font-size: 1.5rem;
  padding: 0 0.1rem;
  height: 3rem;
  border-bottom: none;
  border-top: none;
  border-right: none;
  border-left: none;
  -webkit-appearance: none;
}
input[type=number]:focus,
input[type=text]:focus,
input[type=password]:focus,
input[type=email]:focus,
input[type=tel]:focus,
input[type=number].focus,
input[type=text].focus,
input[type=password].focus,
input[type=email].focus,
input[type=tel].focus {
  outline: none;
  border-bottom: none;
  background-color: transparent;
}
input[type=number]:visited,
input[type=text]:visited,
input[type=password]:visited,
input[type=email]:visited,
input[type=tel]:visited {
  background-color: transparent;
}
input[type=submit],
input[type=button],
button {
  box-sizing: border-box;
  -moz-box-sizing: border-box;
  -webkit-box-sizing: border-box;
  font-size: 1rem;
  padding: 1rem;
  height: 3rem;
  box-shadow: 1px 1px 2px rgba(0, 0, 0, 0.4);
  -webkit-appearance: none;
}
input:focus[type=number],
input:focus[type=text],
input:focus[type=password],
input:focus[type=email] {
  outline: 0;
}
input [type=number]::-webkit-inner-spin-button,
input [type=number]::-webkit-outer-spin-button {
  -webkit-appearance: none;
  margin: 0;
}
@-webkit-keyframes shaker {
  10%,
  90% {
    transform: translate3d(-1px, 0, 0);
  }
  20%,
  80% {
    transform: translate3d(2px, 0, 0);
  }
  30%,
  50%,
  70% {
    transform: translate3d(-4px, 0, 0);
  }
  40%,
  60% {
    transform: translate3d(4px, 0, 0);
  }
}
.shake {
  -webkit-animation-name: shaker;
  -webkit-animation-duration: 0.8s;
  -webkit-transform-origin: 50% 50%;
  -webkit-animation-iteration-count: 2s;
  -webkit-animation-timing-function: linear;
}
a {
  text-decoration: blink;
  cursor: pointer;
}
img.pixelated {
  image-rendering: auto;
  image-rendering: crisp-edges;
  margin: 10px 10px 0px 0px;
}
.display-none {
  display: none;
}
#i2e1-iframe {
  width: 100%;
  height: 100%;
  border: none;
}
.card {
  border-radius: 2px;
  background-color: rgba(255, 255, 255, 0.8) !important;
  box-shadow: 1px 1px 2px rgba(0, 0, 0, 0.4);
  padding: 1rem;
}
.card form {
  text-align: center;
  width: 20rem;
  margin-bottom: 0;
}
@media only screen and (min-width: 361px) and (max-width: 540px) {
  .card form {
    width: 18rem;
  }
}
.card hr {
  width: 20rem;
  border: none;
  border-bottom: 1px solid rgba(204, 204, 204, 0.48);
}
@media only screen and (min-width: 361px) and (max-width: 540px) {
  .card hr {
    width: 16rem;
  }
}
.material-input {
  font-size: 0.8rem;
}
.material-input .group {
  position: relative;
  text-align: center;
}
.material-input .group label {
  outline: none;
  position: absolute;
  transition: 0.25s ease;
  -webkit-transition: 0.25s ease;
  -moz-transition: 0.25s ease;
  -o-transition: 0.25s ease;
  color: #999999;
  left: 0;
  content: 'Enter phone number';
}
.material-input .group label.focus {
  top: -1.5rem;
  font-size: 0.8rem;
  color: #2196f3;
}
.material-input .group input {
  background: transparent;
  font-family: 'Roboto', Fallback, sans-serif !important;
  font-size: 14px;
  font-weight: 600;
}
.material-input .group input:focus ~ label {
  top: -1.5rem;
  font-size: 0.8rem;
  color: #2196f3;
}
.material-input .group input:valid ~ label {
  top: -1.5rem;
  font-size: 0.8rem;
}
.material-input .group.error {
  -webkit-animation-name: shaker;
  -webkit-animation-duration: 0.8s;
  -webkit-transform-origin: 50% 50%;
  -webkit-animation-iteration-count: 2s;
  -webkit-animation-timing-function: linear;
}
.material-input .group.error label {
  color: palevioletred !important;
}
.intl-tel-input .flag-dropdown {
  margin: 0.5rem 0;
}
.intl-tel-input .flag-dropdown .selected-flag1 {
  height: 2.5rem;
  width: 2.5rem;
}
.intl-tel-input .flag-dropdown .selected-flag1 .flag {
  height: 2.5rem;
  width: 2.5rem;
  background-size: 2rem;
}
.intl-tel-input .flag-dropdown .selected-flag1 .flag .down-arrow {
  top: 1rem;
  left: 2rem;
}
.intl-tel-input .flag-dropdown .country-list {
  text-align: justify;
  color: black;
}
.top-container {
  margin: 5rem 0 2rem 0;
}
@media only screen and (max-width: 360px) {
  .top-container {
    margin: 5rem 0 2rem 0;
  }
}
.progressBar {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  width: 100%;
  height: 0.2rem;
  background-color: transparent;
  border-radius: 2px;
}
.progressBar div {
  height: 100%;
  color: #fff;
  text-align: right;
  line-height: 22px;
  width: 0;
  max-width: inherit;
  background-color: rgba(12, 75, 158, 0.9);
}
body.landing .login.container .outer-card1 > img {
  margin-right: 0px !important;
}
.login.container {
  border-radius: 2px;
  background-color: rgba(255, 255, 255, 0.8) !important;
  box-shadow: 1px 1px 2px rgba(0, 0, 0, 0.4);
  padding: 1rem;
  margin-top: 0 !important;
  position: relative;
  display: inline-block;
  box-shadow: 0px 3px 6px 0px rgba(41, 0, 0, 0.5) !important;
}
.login.container form {
  text-align: center;
  width: 20rem;
  margin-bottom: 30px;
}
@media only screen and (min-width: 361px) and (max-width: 540px) {
  .login.container form {
    width: 18rem;
  }
}
.login.container hr {
  width: 20rem;
  border: none;
  border-bottom: 1px solid rgba(204, 204, 204, 0.48);
}
@media only screen and (min-width: 361px) and (max-width: 540px) {
  .login.container hr {
    width: 16rem;
  }
}
.login.container .outer-card1 {
  text-align: right;
  position: relative;
}
.login.container .outer-card1 > img {
  width: auto !important;
  height: 50px !important;
  margin-right: -10px;
  margin-top: -10px !important;
}
.login.container .outer-card1.handle-width div.middler {
  height: 4rem;
  display: inline-block;
  vertical-align: middle;
}
.login.container .outer-card1.handle-width > img {
  width: 11rem !important;
  height: unset !important;
  vertical-align: middle;
}
.login.container .outer-card1.handle-height > .img-sec img {
  width: unset !important;
  height: 6rem !important;
}
.login.container .outer-card1 .open {
  position: absolute;
  right: -1rem;
  top: 0;
  bottom: 0;
  text-align: center;
  padding-top: 1.5rem;
}
.login.container .outer-card1 .open img {
  width: unset !important;
  height: 1rem;
}
.login.container .outer-card1 .open:hover img {
  height: 1.1rem;
}
.login.container .outer-card1 .open > div {
  opacity: 1;
  z-index: 20;
  transition: 0.25s all ease-out;
  -webkit-transition: 0.25s all ease-out;
  -moz-transition: 0.25s all ease-out;
  -o-transition: 0.25s all ease-out;
}
.login.container .outer-card1 .open > div.display-none {
  opacity: 0;
  z-index: 0;
}
.login.container .outer-card1 .open .close {
  top: 6rem;
  right: 2rem;
  position: absolute;
}
.login.container .inline-error {
  text-align: left;
  font-size: 0.9rem;
  color: rgba(255, 0, 0, 0.5);
}
.login.container form {
  position: relative;
}
.login.container form .my-fb-error-class {
  margin-bottom: 30px;
}
.login.container form input {
  border-radius: 0;
}
.login.container form input.primary {
  border-radius: 3px !important;
}
.login.container form input,
.login.container form button {
  width: 100%;
  height: 100%;
}
.login.container form .small_button {
  padding: 0.5rem;
  font-size: 0.7rem;
}
.login.container form .small_button[disabled] {
  color: #afafaf;
}
.login.container form .small_button:focus:not([disabled]) {
  outline: none;
  cursor: pointer;
}
.login.container form .small_button:hover:not([disabled]) {
  cursor: pointer;
  box-shadow: 1px 1px 2px rgba(0, 0, 0, 0.4);
}
.login.container form .autologin {
  display: none !important;
}
.login.container form .username-area,
.login.container form .otp-area,
.login.container form .input-area {
  margin-bottom: 1rem;
}
.login.container form .otp-title {
  color: #000;
  text-align: left;
  font-size: 14px;
  font-weight: 600;
  margin-bottom: 1rem;
}
.login.container form input#username,
.login.container form input#otp {
  font-size: 0.8rem;
  font-weight: 600;
}
.login.container form input#otp {
  padding-left: 16px;
  letter-spacing: 48px;
  border: 0;
  background-image: linear-gradient(to left, #999 80%, rgba(255, 255, 255, 0) 0%);
  background-position: bottom;
  background-size: 67px 1px;
  background-repeat: repeat-x;
  background-position-x: 52px;
  width: 80%;
  font-size: 36px;
  text-align: center;
  color: #666;
}
.login.container form input[type=button] {
  margin-top: 2.4rem;
}
.login.container form .primary {
  font-weight: bold;
}
.login.container form .question-area {
  -webkit-transition: max-height 1.5s linear, opacity 0.4s step-end;
  /* Safari */
  -moz-transition: max-height 1.5s linear, opacity 0.4s step-end;
  -o-transition: max-height 1.5s linear, opacity 0.4s step-end;
  transition: max-height 1.5s linear, opacity 0.4s step-end;
  max-height: 30rem;
  opacity: 1;
  /* Begin New Input Styling changes */
  /* Base for label styling */
  /* checkbox aspect */
  /* checked mark aspect */
  /* checked mark aspect changes */
  /* disabled checkbox */
  /* accessibility */
  /* hover style just for information */
  /* End New Input Styling changes */
}
.login.container form .question-area.display-none {
  -webkit-transition: max-height 0.3s linear, opacity 0.4s linear;
  /* Safari */
  -moz-transition: max-height 0.3s linear, opacity 0.4s linear;
  -o-transition: max-height 0.3s linear, opacity 0.4s linear;
  transition: max-height 0.3s linear, opacity 0.4s linear;
  display: block;
  max-height: 0;
  opacity: 0;
  margin: 0;
  padding: 0;
}
.login.container form .question-area.error .question.img-options {
  box-shadow: 1px 1px 2px rgba(0, 0, 0, 0.4);
}
.login.container form .question-area .question {
  text-align: left;
}
.login.container form .question-area .question.text-question {
  margin-top: 2rem;
}
.login.container form .question-area .question.error.rating-question {
  -webkit-animation-name: shaker;
  -webkit-animation-duration: 0.8s;
  -webkit-transform-origin: 50% 50%;
  -webkit-animation-iteration-count: 2s;
  -webkit-animation-timing-function: linear;
}
.login.container form .question-area .question.error.select {
  -webkit-animation-name: shaker;
  -webkit-animation-duration: 0.8s;
  -webkit-transform-origin: 50% 50%;
  -webkit-animation-iteration-count: 2s;
  -webkit-animation-timing-function: linear;
}
.login.container form .question-area .question.error.select ul li.init {
  color: rgba(255, 0, 0, 0.5);
}
.login.container form .question-area .question.select .question-text {
  cursor: pointer;
  font-family: Roboto-Medium, fallback, sans-serif;
  font-weight: bold;
  color: black;
}
.login.container form .question-area .question.select .question-text span {
  color: red;
}
.login.container form .question-area .question .next-question {
  background-image: url(../../images/wiom/next.svg);
  background-repeat: no-repeat;
  border: 0px;
  background-color: transparent;
  box-shadow: none;
  background-position: right;
  color: transparent;
}
.login.container form .question-area .question.img-options ul {
  margin: 0.8rem 0;
  padding: 0;
  height: 5rem;
}
.login.container form .question-area .question.img-options li.img-option {
  display: inline;
  margin: 0.6rem;
}
.login.container form .question-area .question.img-options li.img-option .circle {
  box-shadow: 3px 3px 2px rgba(0, 0, 0, 0.4);
}
.login.container form .question-area .question.img-options li.img-option input:checked + label .circle {
  border: 0.1rem solid #333;
  box-shadow: none;
}
.login.container form .question-area .question.img-options li.img-option input:checked + label span {
  font-weight: bold;
  color: black;
}
.login.container form .question-area .question.img-options li.img-option input:checked + label img {
  -webkit-filter: saturate(3);
  filter: saturate(3);
}
.login.container form .question-area .question.img-options li.img-option input.error + label .circle {
  border: 0.15rem solid rgba(255, 0, 0, 0.5);
}
.login.container form .question-area .question.img-options li.img-option label {
  position: relative;
  margin: 0.5rem;
}
.login.container form .question-area .question.img-options li.img-option label > div {
  position: absolute;
  left: 50%;
  top: -1rem;
}
.login.container form .question-area .question.img-options li.img-option label .circle {
  width: 3.5rem;
  height: 3.5rem;
  position: relative;
  right: 50%;
  border: 0.1rem solid #F6F6F6;
}
.login.container form .question-area .question.img-options li.img-option label .circle img {
  cursor: pointer;
}
.login.container form .question-area .question.img-options li.img-option label .circle:hover img {
  -webkit-filter: saturate(3);
  filter: saturate(3);
}
.login.container form .question-area .question.img-options li.img-option label .circle:hover + span {
  font-weight: bold;
  color: black;
}
.login.container form .question-area .question.img-options li.img-option label span {
  top: -0.3rem;
  width: 5rem;
  white-space: nowrap;
  font-size: 0.8rem;
  position: relative;
  right: 50%;
}
.login.container form .question-area .question.img-options li.img-option label span:hover {
  font-weight: bold;
  color: black;
}
.login.container form .question-area .question.select ul {
  padding: 0;
  color: grey;
  margin-top: 0.3rem;
}
.login.container form .question-area .question.select ul li {
  width: -webkit-fill-available;
  width: -moz-available;
  padding: 0.7rem;
  z-index: 2;
  list-style: none;
  text-align: justify;
  -webkit-transition: max-height 1.5s linear, opacity 0.4s step-end;
  /* Safari */
  -moz-transition: max-height 1.5s linear, opacity 0.4s step-end;
  -o-transition: max-height 1.5s linear, opacity 0.4s step-end;
  transition: max-height 1.5s linear, opacity 0.4s step-end;
}
.login.container form .question-area .question.select ul li.display-none {
  display: block;
  max-height: 0;
  opacity: 0;
}
.login.container form .question-area .question.select ul li .circle {
  border: none;
  height: 1.5em;
  width: 1.5rem;
  vertical-align: middle;
  margin-right: 0.5rem;
}
.login.container form .question-area .question.select ul li .circle ~ span {
  vertical-align: middle;
}
.login.container form .question-area .question.select ul li.init {
  cursor: pointer;
  margin-bottom: 1rem;
  border: 1px solid #cccccc;
}
.login.container form .question-area .question.select ul li.init hr,
.login.container form .question-area .question.select ul li.init img.check-mark {
  display: none;
}
.login.container form .question-area .question.select ul li.init.error {
  border-color: rgba(255, 0, 0, 0.5);
}
.login.container form .question-area .question.select ul li:not(.init) {
  float: left;
  display: none;
  border-left: 1px solid #cccccc;
  border-right: 1px solid #cccccc;
  border-bottom: none;
  padding: 0.5rem 0.7rem;
  width: 18.5rem;
  line-height: 1.5rem;
  position: relative;
}
@media only screen and (min-width: 361px) and (max-width: 540px), only screen and (max-width: 360px) {
  .login.container form .question-area .question.select ul li:not(.init) {
    width: 16.5rem;
  }
}
.login.container form .question-area .question.select ul li:not(.init):nth-child(2) hr {
  width: 100%;
  left: 0;
  border-bottom: 1px solid #cccccc !important;
}
.login.container form .question-area .question.select ul li:not(.init) hr {
  border-bottom: 1px solid rgba(204, 204, 204, 0.5) !important;
  position: absolute;
  top: -0.5rem;
  left: 0.7rem;
  width: inherit;
  border: none;
}
.login.container form .question-area .question.select ul li:not(.init) img.check-mark {
  display: none;
  width: 1rem;
  position: absolute;
  right: 1rem;
  top: 0.8rem;
}
.login.container form .question-area .question.select ul li:not(.init):hover,
.login.container form .question-area .question.select ul li:not(.init).selected {
  background: #f3f3f3;
}
.login.container form .question-area .question.select ul li:not(.init)[multiple].selected {
  background: #f3f3f3;
}
.login.container form .question-area .question.select ul li:not(.init)[multiple].selected img {
  display: block;
}
.login.container form .question-area .question.select ul li:last-child {
  margin-bottom: 1rem;
  border-bottom: 1px solid #cccccc;
}
.login.container form .question-area .question .text-answer.error {
  -webkit-box-shadow: 0 0 2px 2px rgba(255, 0, 0, 0.5);
  -moz-box-shadow: 0 0 2px 2px rgba(255, 0, 0, 0.5);
  box-shadow: 0 0 2px 2px rgba(255, 0, 0, 0.5);
}
.login.container form .question-area .question.rating-question {
  /* CUSTOM RADIO AND CHECKBOX STYLES */
}
.login.container form .question-area .question.rating-question ul {
  text-align: center;
  height: 3rem;
}
.login.container form .question-area .question.rating-question ul li.img-option {
  margin: 0;
}
.login.container form .question-area .question.rating-question ul li.img-option .circle {
  height: 2.5rem;
  width: 2.5rem;
}
.login.container form .question-area .question.rating-question input[type=radio],
.login.container form .question-area .question.rating-question input[type=checkbox] {
  /* Hide original inputs */
  visibility: hidden;
  position: absolute;
}
.login.container form .question-area .question.rating-question input[type=radio] + label:before,
.login.container form .question-area .question.rating-question input[type=checkbox] + label:before {
  height: 0.9rem;
  width: 0.9rem;
  margin: 0.8rem 1rem -0.2rem 0;
  content: " ";
  display: inline-block;
  vertical-align: baseline;
  border: 0.1rem solid #eee;
  background-color: white;
}
.login.container form .question-area .question.rating-question input[type=radio]:checked + label:before,
.login.container form .question-area .question.rating-question input[type=checkbox]:checked + label:before {
  background: #00aeef !important;
}
.login.container form .question-area .question.rating-question input[type=radio] + label:before {
  border-radius: 50%;
}
.login.container form .question-area .question.rating-question input[type=checkbox] + label:before {
  border-radius: 0.2rem;
}
.login.container form .question-area [type="checkbox"]:not(:checked),
.login.container form .question-area [type="checkbox"]:checked {
  position: absolute;
  left: -9999px;
}
.login.container form .question-area [type="checkbox"]:not(:checked) + label,
.login.container form .question-area [type="checkbox"]:checked + label {
  position: relative;
  padding-left: 1.95em;
  cursor: pointer;
  text-align: center;
}
.login.container form .question-area [type="checkbox"]:checked + label {
  font-weight: bold;
  text-shadow: 1px 1px 1px #eee;
}
.login.container form .question-area [type="checkbox"]:checked + label img {
  border: 2px solid #f4693a;
  box-shadow: 1px 1px 1px #eee;
}
.login.container form .question-area [type="checkbox"]:checked + label:before {
  border: 2px solid #f4693a;
}
.login.container form .question-area [type="checkbox"]:not(:checked) + label:before,
.login.container form .question-area [type="checkbox"]:checked + label:before {
  content: '';
  position: absolute;
  left: 0;
  top: 2px;
  width: 20px;
  height: 20px;
  background: #fff;
  border-radius: 20px;
  box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.1);
}
.login.container form .question-area [type="checkbox"]:not(:checked) + label:before {
  border: 2px solid #333;
}
.login.container form .question-area [type="checkbox"]:not(:checked) + label:after,
.login.container form .question-area [type="checkbox"]:checked + label:after {
  content: '\2713\0020';
  position: absolute;
  top: -3px;
  left: 0.22em;
  font-size: 24px;
  line-height: 1;
  color: #f4693a;
  transition: all 0.2s;
  font-family: Arial;
  font-weight: 800;
  text-shadow: 1px 1px 1px #FFF;
  transform: rotate(30deg);
}
.login.container form .question-area [type="checkbox"]:not(:checked) + label:after {
  opacity: 0;
  transform: scale(0);
}
.login.container form .question-area [type="checkbox"]:checked + label:after {
  opacity: 1;
  transform: scale(1);
}
.login.container form .question-area [type="checkbox"]:disabled:not(:checked) + label:before,
.login.container form .question-area [type="checkbox"]:disabled:checked + label:before {
  box-shadow: none;
  border-color: #bbb;
  background-color: #ddd;
}
.login.container form .question-area [type="checkbox"]:disabled:checked + label:after {
  color: #999;
}
.login.container form .question-area [type="checkbox"]:disabled + label {
  color: #aaa;
}
.login.container form .question-area [type="checkbox"]:checked:focus + label:before,
.login.container form .question-area [type="checkbox"]:not(:checked):focus + label:before {
  /*border: 2px dotted #f4693a;*/
}
.login.container form .question-area label:hover:before {
  border: 2px solid #F2A063 !important;
}
.login.container form .question-area label {
  line-height: 32px;
  width: fit-content;
  overflow: hidden;
  display: block;
  text-align: left;
}
.login.container form .question-area label > img {
  display: block;
  line-height: 17px;
  vertical-align: text-bottom;
  object-fit: cover;
  border-radius: 50%;
  border: 2px solid #333;
  width: 42px;
}
.login.container form #captcha-holder {
  display: inline-block;
  text-align: justify;
  width: 100%;
  max-height: 3rem;
  margin-bottom: 2.5rem;
  font-size: 0.8rem;
}
.login.container form #captcha-holder div {
  width: 48%;
  display: inline-block;
  max-height: 3rem;
  vertical-align: middle;
}
.login.container form #captcha-holder div img {
  height: 100%;
  width: 100%;
}
.login.container form #captcha-holder div input {
  width: 100%;
}
.login.container .tnc-area {
  text-align: center;
}
.login.container .tnc-area .tnc1 {
  font-size: 11px;
  color: #999999;
}
.login.container .tnc-area .tnc1 a {
  color: inherit;
  text-decoration: underline;
  font-weight: bold;
}
.login.container .tnc-area.corner {
  width: 22rem;
  margin-top: -1rem;
  margin-bottom: 0.8rem;
}
.login.container .tnc-area.corner .tnc1 {
  font-size: 0.67rem;
}
@media only screen and (min-width: 361px) and (max-width: 540px), only screen and (max-width: 360px) {
  .login.container .tnc-area.corner {
    width: 20rem;
  }
  .login.container .tnc-area.corner .tnc1 {
    font-size: 0.6rem;
  }
}
.login.container .footer {
  text-align: left;
  color: #999999;
  font-size: 12px;
  position: relative;
  padding-top: 1.1rem;
}
.login.container .footer span {
  display: block;
  text-align: left;
  margin-bottom: 10px;
}
.login.container .footer b {
  font-weight: bold;
}
.login.container .footer b a {
  color: #f25f58;
}
.login.container .footer .top-border {
  border-top: 1px solid #cccccc;
  position: absolute;
  top: 0rem;
  left: -1rem;
  right: -1rem;
}
.login.container .footer .tnc-area {
  position: absolute;
  right: 0;
  bottom: 0;
}
.login.container .footer .tnc-area a {
  font-weight: normal;
  font-size: 0.8rem;
  text-decoration: none;
  font-style: italic;
  color: #f25f58;
}
.login.container .footer .tnc-area a:focus {
  outline: none;
}
.login.container .footer .i2e1_logo {
  margin-top: 0.2rem;
  text-align: center;
}
.login.container .footer .i2e1_logo img {
  width: 3rem;
}
.login.container .footer .stepper {
  height: 0.3rem;
  text-align: center;
  margin-bottom: 10px;
}
.login.container .footer .stepper div.step {
  height: 8px;
  width: 8px;
  border-radius: 4px;
  display: inline-block;
  background-color: transparent;
  border: 1px solid #757575;
  box-sizing: border-box;
}
.login.container .footer .stepper div.step.visited {
  border: none;
  background-color: #f4693a;
}
.login.container .footer .stepper div.connector {
  width: 0.5rem;
  height: 100%;
  display: inline-block;
}
.login.container .footer .stepper div.connector div {
  visibility: hidden !important;
  height: 50%;
  border-bottom: 1px solid #757575;
}
.login.container .footer .stepper div.connector.visited div {
  border-bottom: 1px solid #16befd;
}
.login.container .change_auth_link1 {
  font-size: 0.8rem;
}
.login.container .change_auth_link1 a {
  color: #291280;
}
.login.container .change_auth_link1 a:hover {
  text-decoration: underline;
}
.login.container .msg-section {
  text-align: justify;
}
.login.container span.issues-contact-us {
  font-size: 0.8rem;
}
.login.container span.issues-contact-us a {
  font-size: 0.8rem;
  vertical-align: bottom;
}
.login.container button.fb-share {
  background-color: #3b5998;
  padding: 0 0.5rem 0 0;
  width: 13rem;
}
.login.container button.fb-share img {
  width: 1.8rem;
  margin: 0.2rem;
  float: left;
}
.login.container button.fb-share div {
  padding: 0.6rem;
  font-size: 1rem;
  font-weight: 100;
  display: inline-block;
  border-left: 1px solid white;
}
.login.container .state-transition {
  padding: 1rem 0;
  opacity: 1;
  transition: 0.3s opacity linear, 0.3s left ease-in-out;
  -webkit-transition: 0.3s opacity linear, 0.3s left ease-in-out;
  -o-transition: 0.3s opacity linear, 0.3s left ease-in-out;
  -moz-transition: 0.3s opacity linear, 0.3s left ease-in-out;
  padding-bottom: 0px;
}
.login.container .state-transition.display-none {
  height: 0;
  opacity: 0;
  display: block;
  margin-bottom: 0;
  padding: 0;
  left: 20rem;
  pointer-events: none;
}
.login.container .first-state .username-area:not(.global-otp) {
  margin-top: 5rem;
  font-size: 0.8rem;
  position: relative;
  padding-bottom: 8px;
  border-bottom: 2px solid #d4d4d4;
}
.login.container .first-state .username-area:not(.global-otp) .group {
  position: relative;
  text-align: center;
}
.login.container .first-state .username-area:not(.global-otp) .group label {
  outline: none;
  position: absolute;
  transition: 0.25s ease;
  -webkit-transition: 0.25s ease;
  -moz-transition: 0.25s ease;
  -o-transition: 0.25s ease;
  color: #999999;
  left: 0;
  content: 'Enter phone number';
}
.login.container .first-state .username-area:not(.global-otp) .group label.focus {
  top: -1.5rem;
  font-size: 0.8rem;
  color: #2196f3;
}
.login.container .first-state .username-area:not(.global-otp) .group input {
  background: transparent;
  font-family: 'Roboto', Fallback, sans-serif !important;
  font-size: 14px;
  font-weight: 600;
}
.login.container .first-state .username-area:not(.global-otp) .group input:focus ~ label {
  top: -1.5rem;
  font-size: 0.8rem;
  color: #2196f3;
}
.login.container .first-state .username-area:not(.global-otp) .group input:valid ~ label {
  top: -1.5rem;
  font-size: 0.8rem;
}
.login.container .first-state .username-area:not(.global-otp) .group.error {
  -webkit-animation-name: shaker;
  -webkit-animation-duration: 0.8s;
  -webkit-transform-origin: 50% 50%;
  -webkit-animation-iteration-count: 2s;
  -webkit-animation-timing-function: linear;
}
.login.container .first-state .username-area:not(.global-otp) .group.error label {
  color: palevioletred !important;
}
.login.container .first-state .username-area:not(.global-otp) .error-message {
  position: relative;
  top: 20px;
  float: left;
  color: #ef4f82;
}
.login.container .first-state .username-area:not(.global-otp).phone-number .title {
  color: #484848;
  text-align: left;
  font-size: 14px;
  font-weight: 600;
}
.login.container .first-state .username-area:not(.global-otp).phone-number .group {
  margin-top: 0.8rem;
  margin-left: 3.5rem;
  width: -webkit-fill-available;
}
.login.container .first-state .username-area:not(.global-otp).phone-number .group label {
  font-weight: bold;
  font-size: 14px;
}
.login.container .first-state .username-area:not(.global-otp).phone-number .group #username:valid ~ a,
.login.container .first-state .username-area:not(.global-otp).phone-number .group #username:focus ~ a {
  display: inline-block;
  width: 1rem;
  height: 2rem;
  font-size: 0.9rem;
}
.login.container .first-state .username-area:not(.global-otp).phone-number .group #username:valid ~ label,
.login.container .first-state .username-area:not(.global-otp).phone-number .group #username:focus ~ label {
  visibility: hidden;
  font-size: 0px;
}
.login.container .first-state .username-area:not(.global-otp) a {
  display: none;
  position: absolute;
  top: 0;
  right: 0;
  font-size: 0.8rem;
  font-weight: bold;
}
.login.container .first-state .username-area:not(.global-otp) a:hover {
  text-shadow: 1px 1px 0 rgba(0, 0, 0, 0.4);
}
.login.container .first-state .username-area:not(.global-otp) .username_prefix {
  position: absolute;
  top: 18px;
  margin-left: 0 !important;
  width: 2.5rem !important;
  height: inherit;
  background: transparent;
}
.login.container .first-state .username-area:not(.global-otp) .username_prefix #username_prefix {
  font-size: 14px;
  font-weight: 600;
  position: relative;
}
.login.container .first-state .username-area.global-otp label,
.login.container .first-state .username-area.global-otp a {
  display: none;
}
.login.container .first-state .last-name-area,
.login.container .first-state .roomno-area {
  font-size: 0.8rem;
}
.login.container .first-state .last-name-area .group,
.login.container .first-state .roomno-area .group {
  position: relative;
  text-align: center;
}
.login.container .first-state .last-name-area .group label,
.login.container .first-state .roomno-area .group label {
  outline: none;
  position: absolute;
  transition: 0.25s ease;
  -webkit-transition: 0.25s ease;
  -moz-transition: 0.25s ease;
  -o-transition: 0.25s ease;
  color: #999999;
  left: 0;
  content: 'Enter phone number';
}
.login.container .first-state .last-name-area .group label.focus,
.login.container .first-state .roomno-area .group label.focus {
  top: -1.5rem;
  font-size: 0.8rem;
  color: #2196f3;
}
.login.container .first-state .last-name-area .group input,
.login.container .first-state .roomno-area .group input {
  background: transparent;
  font-family: 'Roboto', Fallback, sans-serif !important;
  font-size: 14px;
  font-weight: 600;
}
.login.container .first-state .last-name-area .group input:focus ~ label,
.login.container .first-state .roomno-area .group input:focus ~ label {
  top: -1.5rem;
  font-size: 0.8rem;
  color: #2196f3;
}
.login.container .first-state .last-name-area .group input:valid ~ label,
.login.container .first-state .roomno-area .group input:valid ~ label {
  top: -1.5rem;
  font-size: 0.8rem;
}
.login.container .first-state .last-name-area .group.error,
.login.container .first-state .roomno-area .group.error {
  -webkit-animation-name: shaker;
  -webkit-animation-duration: 0.8s;
  -webkit-transform-origin: 50% 50%;
  -webkit-animation-iteration-count: 2s;
  -webkit-animation-timing-function: linear;
}
.login.container .first-state .last-name-area .group.error label,
.login.container .first-state .roomno-area .group.error label {
  color: palevioletred !important;
}
.login.container .first-state .roomno-area {
  margin-bottom: 1.5rem;
}
.login.container .first-state .seperator {
  line-height: 2rem;
}
.login.container .first-state .general-msg {
  font-size: 0.9rem;
  text-align: left;
  margin: 0;
}
.login.container .first-state .general-msg:not(.display-none) {
  margin: 0.2rem 0;
  position: absolute;
  top: -1rem;
}
.login.container .first-state .general-msg:not(.display-none) ~ .input-area {
  margin-top: 1rem;
}
.login.container .first-state .general-msg:not(.display-none) ~ .username-area {
  margin-top: 1rem;
}
.login.container .first-state .general-msg.error {
  color: palevioletred !important;
}
.login.container .first-state [cage=generate-otp],
.login.container .first-state [cage=access-code-connect-button],
.login.container .first-state [cage=room-no-connect-button] {
  text-align: left;
}
.login.container .first-state [cage=generate-otp] input,
.login.container .first-state [cage=access-code-connect-button] input,
.login.container .first-state [cage=room-no-connect-button] input {
  margin-bottom: 1.2rem;
}
.login.container .first-state #facebook-btn {
  background-color: #3b5998;
  color: white;
  display: inline-block;
  border-radius: 0.2rem;
  margin: 1rem;
}
.login.container .first-state #facebook-btn span:first-child {
  border-right: 1px solid #fff;
  width: 3.012rem;
  display: inline-block;
  padding-top: 0.625rem;
  vertical-align: middle;
}
.login.container .first-state #facebook-btn span:first-child img {
  width: 1.25rem;
}
.login.container .first-state #facebook-btn span:nth-child(2) {
  padding: 0 1rem;
  vertical-align: baseline;
}
.login.container .open {
  position: absolute;
  top: 3rem;
  right: 1rem;
  width: 2rem;
  cursor: pointer;
  z-index: 15;
}
.login.container .open img {
  height: 1rem;
}
.login.container .open:hover img {
  height: 1.1rem;
}
.login.container .open > div {
  opacity: 1;
  z-index: 20;
  transition: 0.25s all ease-out;
  -webkit-transition: 0.25s all ease-out;
  -moz-transition: 0.25s all ease-out;
  -o-transition: 0.25s all ease-out;
}
.login.container .open > div.display-none {
  opacity: 0;
  z-index: 0;
}
.login.container .profile-section {
  text-align: justify;
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0;
  width: 100%;
  font-size: 1rem;
}
.login.container .profile-section.display-none {
  display: none;
}
.login.container .profile-section .backdrop {
  position: absolute;
  width: 100%;
  bottom: 0;
  top: 0;
  background-color: rgba(0, 0, 0, 0.3);
  z-index: 10;
  padding-top: 5rem;
}
.login.container .profile-section .backdrop .details {
  margin: 1rem auto;
  background-color: white;
  max-width: 20rem;
}
.login.container .profile-section .backdrop .profile-container {
  padding: 1rem 1rem 0 1rem;
}
.login.container .profile-section .backdrop .profile-container .gender img {
  width: 3rem;
  border-radius: 100%;
  border: 1px solid #cccccc;
}
.login.container .profile-section .backdrop .profile-container div:not(.gender) {
  margin: 0.3rem 0;
}
.login.container .profile-section .backdrop .profile-container div:not(.gender) img {
  width: 1rem;
  margin: 0 0.8rem 0 1rem;
  vertical-align: middle;
}
.login.container .profile-section .backdrop hr {
  border: none;
  border-bottom: 1px solid rgba(204, 204, 204, 0.48);
}
.login.container .profile-section .backdrop .help-container ul {
  list-style-type: none;
  padding-left: 0;
  margin: 0;
}
.login.container .profile-section .backdrop .help-container ul li {
  padding: 1rem;
  cursor: pointer;
}
.login.container .profile-section .backdrop .help-container ul li:hover {
  background: #f3f3f3;
}
.login.container .help-state {
  color: rgba(12, 75, 158, 0.9);
  text-align: justify;
  margin-bottom: 0;
}
.login.container .help-state .header {
  font-size: 0.8rem;
  margin: 0 1rem;
}
.login.container .help-state .support {
  color: #999999;
  font-size: 0.8rem;
  margin: 0 1rem 1rem 1rem;
}
.login.container .help-state .details {
  font-size: 0.9rem;
  margin: 0 1rem;
}
.login.container .help-state .details .row {
  height: 4rem;
}
.login.container .help-state .details .row .circle {
  width: 2rem;
  height: 2rem;
  border: none;
  box-shadow: none;
  color: white;
  background-color: rgba(12, 75, 158, 0.9);
}
.login.container .help-state .details .row .column {
  display: inline-block;
  height: 100%;
  vertical-align: middle;
  position: relative;
}
.login.container .help-state .details .row .column:first-child {
  width: 2rem;
  margin-right: 1rem;
  text-align: center;
}
.login.container .help-state .details .row .column:last-child {
  padding-top: 0.5rem;
  height: 50%;
  vertical-align: top;
}
.login.container .help-state .details .row .column .divider {
  position: absolute;
  top: 0;
  bottom: 0;
  right: 0;
  left: 0;
}
.login.container .help-state .details .row .column .divider > div {
  height: 100%;
  width: 50%;
  border-right: 1px dashed rgba(12, 75, 158, 0.9);
}
.login.container .second-state .questions-confirm-area {
  margin-bottom: 40px;
}
.login.container .second-state .questions-confirm-area .tick {
  text-align: center;
  margin: 0px auto;
  padding: 10px;
  border-radius: 50px;
  border: 2px solid red;
  width: 40px;
  height: 40px;
  display: block;
  margin-bottom: 40px;
}
.login.container .second-state .questions-confirm-area .loud {
  font-size: 20px;
  font-weight: 600;
  font-family: Roboto-Medium, Fallback, sans-serif !important;
}
.login.container .second-state .questions-confirm-area .less-loud {
  font-size: 16px;
  font-weight: 400;
  font-family: Roboto-Medium, Fallback, sans-serif !important;
}
.login.container .second-state .questions-confirm-area .questions-confirm-cta {
  margin: 0px auto;
  text-align: center;
  width: 80%;
}
.login.container .second-state .questions-confirm-area .questions-confirm-cta .cta-okay {
  float: right;
  padding: 5px 0px 5px 10px;
  color: #ef5374;
  font-weight: bold;
  margin: 30px;
}
.login.container .second-state .error-message {
  position: relative;
  top: 0px;
  float: left;
  color: #ef4f82;
  font-size: 12px;
  font-family: Roboto-Medium, fallback, sans-serif;
}
.login.container .second-state .barking {
  margin-top: 0px;
}
.login.container .second-state .welcome-back {
  margin-bottom: 30px;
}
.login.container .second-state .bottom-button-group {
  opacity: 1;
  position: relative;
  transition: 0.25s all ease-out;
  -webkit-transition: 0.25s all ease-out;
  -moz-transition: 0.25s all ease-out;
  -o-transition: 0.25s all ease-out;
  height: 1rem;
  top: 36px;
}
.login.container .second-state .bottom-button-group .back-area,
.login.container .second-state .bottom-button-group .resend-otp-area {
  position: absolute;
  height: auto;
  top: -1.5rem;
  cursor: pointer;
  background: none;
  font-family: Roboto-Medium, Fallback, sans-serif !important;
  color: #999;
}
.login.container .second-state .bottom-button-group .back-area img,
.login.container .second-state .bottom-button-group .resend-otp-area img {
  width: 1.2rem;
}
.login.container .second-state .bottom-button-group .back-area {
  font-size: 14px;
}
.login.container .second-state .bottom-button-group .resend-otp-area {
  right: 0;
  height: auto;
}
.login.container .second-state .bottom-button-group .resend-otp-area input.small_button {
  margin-top: 0;
  background: none !important;
  color: #999;
  padding: 0px;
  box-shadow: none;
  font-size: 14px;
  font-weight: 400;
  font-family: Roboto-Medium, Fallback, sans-serif !important;
}
.login.container .second-state .button-group {
  opacity: 1;
  position: relative;
  transition: 0.25s all ease-out;
  -webkit-transition: 0.25s all ease-out;
  -moz-transition: 0.25s all ease-out;
  -o-transition: 0.25s all ease-out;
  height: 1rem;
}
.login.container .second-state .button-group .back-area,
.login.container .second-state .button-group .resend-otp-area {
  position: absolute;
  height: inherit;
  top: -1.5rem;
  cursor: pointer;
}
.login.container .second-state .button-group .back-area img,
.login.container .second-state .button-group .resend-otp-area img {
  width: 1.2rem;
}
.login.container .second-state .button-group .back-area {
  top: -35px;
}
.login.container .second-state .button-group .resend-otp-area {
  right: 0;
  width: 5rem;
}
.login.container .second-state .button-group .resend-otp-area input.small_button {
  margin-top: 0;
  height: 1.5rem;
}
.login.container .second-state .otp-area {
  margin-top: 4.5rem;
  position: relative;
  font-size: 0.8rem;
}
.login.container .second-state .otp-area .group {
  position: relative;
  text-align: center;
}
.login.container .second-state .otp-area .group label {
  outline: none;
  position: absolute;
  transition: 0.25s ease;
  -webkit-transition: 0.25s ease;
  -moz-transition: 0.25s ease;
  -o-transition: 0.25s ease;
  color: #999999;
  left: 0;
  content: 'Enter phone number';
}
.login.container .second-state .otp-area .group label.focus {
  top: -1.5rem;
  font-size: 0.8rem;
  color: #2196f3;
}
.login.container .second-state .otp-area .group input {
  background: transparent;
  font-family: 'Roboto', Fallback, sans-serif !important;
  font-size: 14px;
  font-weight: 600;
}
.login.container .second-state .otp-area .group input:focus ~ label {
  top: -1.5rem;
  font-size: 0.8rem;
  color: #2196f3;
}
.login.container .second-state .otp-area .group input:valid ~ label {
  top: -1.5rem;
  font-size: 0.8rem;
}
.login.container .second-state .otp-area .group.error {
  -webkit-animation-name: shaker;
  -webkit-animation-duration: 0.8s;
  -webkit-transform-origin: 50% 50%;
  -webkit-animation-iteration-count: 2s;
  -webkit-animation-timing-function: linear;
}
.login.container .second-state .otp-area .group.error label {
  color: palevioletred !important;
}
.login.container .second-state .otp-area .group input {
  letter-spacing: 1rem;
  transition: 0.25s ease;
  -webkit-transition: 0.25s ease;
  -moz-transition: 0.25s ease;
  -o-transition: 0.25s ease;
  width: 70%;
}
.login.container .second-state .otp-area .group input::placeholder {
  padding-left: 1rem;
}
.login.container .second-state .otp-area #otp-group input {
  border-bottom: 2px solid #ddd;
  width: 4rem;
  line-height: 20px;
  text-align: center;
  font-size: 14px;
  font-weight: bold;
  margin: 0 2px;
  font-family: monospace;
  letter-spacing: normal;
  padding-bottom: 4px;
  font-family: 'Roboto', 'sans-serif', 'fallback';
}
.login.container .second-state .filler {
  height: 5rem;
  margin: 1rem 0;
  box-shadow: 1px 1px 2px rgba(0, 0, 0, 0.4);
}
.login.container .mac-confirmation-state .mac-area {
  font-size: 0.8rem;
  margin-bottom: 1.2rem;
}
.login.container .mac-confirmation-state .mac-area .group {
  position: relative;
  text-align: center;
}
.login.container .mac-confirmation-state .mac-area .group label {
  outline: none;
  position: absolute;
  transition: 0.25s ease;
  -webkit-transition: 0.25s ease;
  -moz-transition: 0.25s ease;
  -o-transition: 0.25s ease;
  color: #999999;
  left: 0;
  content: 'Enter phone number';
}
.login.container .mac-confirmation-state .mac-area .group label.focus {
  top: -1.5rem;
  font-size: 0.8rem;
  color: #2196f3;
}
.login.container .mac-confirmation-state .mac-area .group input {
  background: transparent;
  font-family: 'Roboto', Fallback, sans-serif !important;
  font-size: 14px;
  font-weight: 600;
}
.login.container .mac-confirmation-state .mac-area .group input:focus ~ label {
  top: -1.5rem;
  font-size: 0.8rem;
  color: #2196f3;
}
.login.container .mac-confirmation-state .mac-area .group input:valid ~ label {
  top: -1.5rem;
  font-size: 0.8rem;
}
.login.container .mac-confirmation-state .mac-area .group.error {
  -webkit-animation-name: shaker;
  -webkit-animation-duration: 0.8s;
  -webkit-transform-origin: 50% 50%;
  -webkit-animation-iteration-count: 2s;
  -webkit-animation-timing-function: linear;
}
.login.container .mac-confirmation-state .mac-area .group.error label {
  color: palevioletred !important;
}
.login.container .mac-confirmation-state ul {
  color: #757575;
  list-style: none;
  padding-left: 0;
  text-align: left;
  font-size: 0.9rem;
  margin-bottom: 2rem;
}
.login.container .mac-confirmation-state ul li {
  margin-bottom: 1rem;
}
.login.container .store-detail-state .shop-name-area {
  font-size: 0.8rem;
  margin-bottom: 1.2rem;
}
.login.container .store-detail-state .shop-name-area .group {
  position: relative;
  text-align: center;
}
.login.container .store-detail-state .shop-name-area .group label {
  outline: none;
  position: absolute;
  transition: 0.25s ease;
  -webkit-transition: 0.25s ease;
  -moz-transition: 0.25s ease;
  -o-transition: 0.25s ease;
  color: #999999;
  left: 0;
  content: 'Enter phone number';
}
.login.container .store-detail-state .shop-name-area .group label.focus {
  top: -1.5rem;
  font-size: 0.8rem;
  color: #2196f3;
}
.login.container .store-detail-state .shop-name-area .group input {
  background: transparent;
  font-family: 'Roboto', Fallback, sans-serif !important;
  font-size: 14px;
  font-weight: 600;
}
.login.container .store-detail-state .shop-name-area .group input:focus ~ label {
  top: -1.5rem;
  font-size: 0.8rem;
  color: #2196f3;
}
.login.container .store-detail-state .shop-name-area .group input:valid ~ label {
  top: -1.5rem;
  font-size: 0.8rem;
}
.login.container .store-detail-state .shop-name-area .group.error {
  -webkit-animation-name: shaker;
  -webkit-animation-duration: 0.8s;
  -webkit-transform-origin: 50% 50%;
  -webkit-animation-iteration-count: 2s;
  -webkit-animation-timing-function: linear;
}
.login.container .store-detail-state .shop-name-area .group.error label {
  color: palevioletred !important;
}
.login.container .store-detail-state .address-area {
  font-size: 0.8rem;
}
.login.container .store-detail-state .address-area .group {
  position: relative;
  text-align: center;
}
.login.container .store-detail-state .address-area .group label {
  outline: none;
  position: absolute;
  transition: 0.25s ease;
  -webkit-transition: 0.25s ease;
  -moz-transition: 0.25s ease;
  -o-transition: 0.25s ease;
  color: #999999;
  left: 0;
  content: 'Enter phone number';
}
.login.container .store-detail-state .address-area .group label.focus {
  top: -1.5rem;
  font-size: 0.8rem;
  color: #2196f3;
}
.login.container .store-detail-state .address-area .group input {
  background: transparent;
  font-family: 'Roboto', Fallback, sans-serif !important;
  font-size: 14px;
  font-weight: 600;
}
.login.container .store-detail-state .address-area .group input:focus ~ label {
  top: -1.5rem;
  font-size: 0.8rem;
  color: #2196f3;
}
.login.container .store-detail-state .address-area .group input:valid ~ label {
  top: -1.5rem;
  font-size: 0.8rem;
}
.login.container .store-detail-state .address-area .group.error {
  -webkit-animation-name: shaker;
  -webkit-animation-duration: 0.8s;
  -webkit-transform-origin: 50% 50%;
  -webkit-animation-iteration-count: 2s;
  -webkit-animation-timing-function: linear;
}
.login.container .store-detail-state .address-area .group.error label {
  color: palevioletred !important;
}
.login.container .store-detail-state .user-email-area {
  font-size: 0.8rem;
  margin-bottom: 1.2rem;
}
.login.container .store-detail-state .user-email-area .group {
  position: relative;
  text-align: center;
}
.login.container .store-detail-state .user-email-area .group label {
  outline: none;
  position: absolute;
  transition: 0.25s ease;
  -webkit-transition: 0.25s ease;
  -moz-transition: 0.25s ease;
  -o-transition: 0.25s ease;
  color: #999999;
  left: 0;
  content: 'Enter phone number';
}
.login.container .store-detail-state .user-email-area .group label.focus {
  top: -1.5rem;
  font-size: 0.8rem;
  color: #2196f3;
}
.login.container .store-detail-state .user-email-area .group input {
  background: transparent;
  font-family: 'Roboto', Fallback, sans-serif !important;
  font-size: 14px;
  font-weight: 600;
}
.login.container .store-detail-state .user-email-area .group input:focus ~ label {
  top: -1.5rem;
  font-size: 0.8rem;
  color: #2196f3;
}
.login.container .store-detail-state .user-email-area .group input:valid ~ label {
  top: -1.5rem;
  font-size: 0.8rem;
}
.login.container .store-detail-state .user-email-area .group.error {
  -webkit-animation-name: shaker;
  -webkit-animation-duration: 0.8s;
  -webkit-transform-origin: 50% 50%;
  -webkit-animation-iteration-count: 2s;
  -webkit-animation-timing-function: linear;
}
.login.container .store-detail-state .user-email-area .group.error label {
  color: palevioletred !important;
}
.login.container .store-detail-state [cage=connect-button] {
  margin-top: 2rem;
}
.login.container .voucher-state {
  display: inline-block;
}
.login.container .data-voucher-state {
  text-align: left;
}
.login.container .data-voucher-state h3 {
  margin-top: 0;
}
.login.container .data-voucher-state .chooser {
  list-style: none;
  padding: 0;
}
.login.container .data-voucher-state .chooser input {
  width: 1rem;
  height: 1rem;
}
.login.container .social-state {
  display: inline-block;
}
.login.container .social-state .social > img {
  width: 13rem;
  margin-bottom: 1rem;
}
.login.container .social-state .social .inline-error {
  display: inline;
}
.login.container .error-state {
  display: inline-block;
}
.login.container .error-state .limit-exhausted,
.login.container .error-state .time-exhausted,
.login.container .error-state .user-blocked {
  text-align: center;
  margin-bottom: -1.3rem;
}
.login.container .error-state .limit-exhausted img,
.login.container .error-state .time-exhausted img,
.login.container .error-state .user-blocked img {
  width: 80%;
  margin: 1rem 0;
}
.login.container .error-state .limit-exhausted img {
  width: 60%;
}
.login.container .error-state #errorMsg,
.login.container .error-state .go-back-to-login {
  color: black;
  margin: 1rem auto 0 auto;
  width: 80%;
  text-shadow: 1px 1px 1px rgba(158, 158, 158, 0.49);
}
.login.container .error-state #errorMsg a,
.login.container .error-state .go-back-to-login a {
  color: blue !important;
  cursor: pointer;
}
.login.container .error-state #errorMsg a:hover,
.login.container .error-state .go-back-to-login a:hover,
.login.container .error-state #errorMsg a:focus,
.login.container .error-state .go-back-to-login a:focus {
  text-decoration: underline;
}
.login.container .error-state .airtel-limit-exceeded h3 {
  margin: 0.8rem;
}
.login.container .error-state .airtel-limit-exceeded table {
  border: 2px solid #f18f93;
  margin-bottom: 1rem;
  width: 100%;
}
.login.container .error-state .airtel-limit-exceeded table tbody {
  background: #d9edf7;
}
.login.container .error-state .airtel-limit-exceeded table tr {
  height: 2rem;
}
.login.container .redirecting-state {
  display: inline-block;
}
.login.container .redirecting-state .connected {
  font-size: 0.9rem;
  text-align: center;
  font-family: Roboto, fallback, sans-serif;
}
.login.container .redirecting-state #greeting {
  margin-top: 36px;
  text-align: center;
  font-size: 1.5rem;
  color: black;
  line-height: 32px;
  font-family: Roboto-Medium, fallback, sans-serif;
}
.login.container .redirecting-state .timemessage {
  text-align: center;
  font-size: 0.9rem;
  color: black;
  font-family: Roboto-Medium, fallback, sans-serif;
}
.login.container .redirecting-state .datamessage {
  margin-top: 36px;
  text-align: center;
  font-size: 1.5rem;
  color: black;
  line-height: 32px;
  font-family: Roboto, fallback, sans-serif;
}
.login.container .redirecting-state .campaign {
  color: black;
  padding: 0.7rem;
  margin-bottom: 1rem;
  background-color: white;
}
.login.container .redirecting-state .campaign .close {
  display: none;
}
.login.container .redirecting-state .campaign.scale-up {
  -ms-transform: scale(1.5, 1.5);
  /* IE 9 */
  -webkit-transform: scale(1.5, 1.5);
  /* Safari */
  transform: scale(1.5, 1.5);
  /* Standard syntax */
  position: absolute;
  z-index: 10;
  top: 0;
}
.login.container .redirecting-state .campaign.scale-up .close {
  display: block;
  position: absolute;
  width: 1.5rem;
  height: 1.5rem;
  right: 0;
  top: 0;
  font-size: 1rem;
  font-weight: bold;
  padding: 0;
  border-radius: 10rem;
}
.login.container .redirecting-state .campaign.scale-up .close:focus {
  outline: none;
}
.login.container .redirecting-state .campaign .border {
  background: white;
  border: 2px solid #9d9d9d;
  padding: 0.5rem;
}
.login.container .redirecting-state .campaign .text {
  margin: 0;
}
.login.container .redirecting-state .campaign img {
  width: 100%;
}
.login.container .redirecting-state .campaign .offerCode {
  background-color: yellow;
}
.login.container .swap-promotion-state .greetings {
  color: #324b9b;
  font-style: italic;
  font-size: 0.8rem;
}
.login.container .swap-promotion-state .user-attributes {
  margin-top: 1rem;
  font-size: 0.9rem;
}
.login.container .swap-promotion-state .user-attributes > div {
  display: inline-block;
}
.login.container .swap-promotion-state .user-attributes > div.display-none {
  display: none;
}
.login.container .swap-promotion-state .user-attributes div:nth-child(1) {
  margin-right: 1rem;
}
.login.container .swap-promotion-state .user-attributes div:nth-child(2) {
  margin-left: 1rem;
}
.login.container .swap-promotion-state .user-attributes .data img,
.login.container .swap-promotion-state .user-attributes .time img {
  width: 1.5rem;
}
.login.container .landing-state .landing-page-message {
  font-size: 16px;
  padding: 30px 0px 30px 0px;
}
.login.container .landing-state .landing-page-message bigger {
  color: #2f4a9a;
  font-size: 1.8rem;
}
.login.container .landing-state div[action] {
  position: relative;
  margin-bottom: 1rem;
}
.login.container .landing-state div[action] img {
  position: absolute;
  width: 2rem;
  top: 0.5rem;
  right: 0.5rem;
}
.login.container .landing-state div[action] input {
  padding-left: 1rem;
  border: 1px solid #dad9d9;
  height: 3rem;
}
.login.container .landing-state div[action] input:focus {
  background: white;
}
.login.container .landing-state .landing-page-tiles-container div {
  width: 5rem;
  height: 5rem;
  display: inline-block;
  margin: 0.2rem;
  background-color: white;
  vertical-align: middle;
  border: 1px solid #2f4a9a;
}
.login.container .landing-state .landing-page-tiles-container div a {
  width: 100%;
}
.login.container .landing-state .landing-page-tiles-container div img {
  width: 100%;
}
.advertisement-campaign {
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: 29;
  background-color: rgba(0, 0, 0, 0.3);
}
.advertisement-campaign .login.container {
  z-index: 30;
}
.advertisement-campaign .login.container .back-area span {
  color: #999999;
}
.advertisement-campaign .login.container.transition {
  opacity: 1;
  transition: 0.3s opacity linear, 0.3s left ease-in-out;
  -webkit-transition: 0.3s opacity linear, 0.3s left ease-in-out;
  -o-transition: 0.3s opacity linear, 0.3s left ease-in-out;
  -moz-transition: 0.3s opacity linear, 0.3s left ease-in-out;
}
.advertisement-campaign .login.container.transition.display-none {
  height: 0;
  opacity: 0;
  display: block;
  margin-bottom: 0;
  padding: 0;
  left: 20rem;
  pointer-events: none;
}
.advertisement-campaign .campaign {
  position: relative;
}
.advertisement-campaign span.ctp {
  position: absolute;
  bottom: 0;
  right: 1rem;
  font-size: 0.9rem;
}
.advertisement-campaign img,
.advertisement-campaign video {
  width: inherit;
}
.advertisement-campaign .circle {
  width: 1.5rem;
  height: 1.5rem;
  border: none;
  position: absolute;
  top: -0.75rem;
  right: -0.75rem;
}
.advertisement-campaign .circle img {
  width: 1.5rem;
  cursor: pointer;
}
.advertisement-campaign .circle img.grayscale {
  pointer-events: none;
}
.language-selectors {
  width: 100%;
  padding: 1rem 0;
  position: absolute;
  left: 0;
  top: 0;
  text-align: right;
  transform: translateY(-100%);
  -ms-transform: translateY(-100%);
  /* IE 9 */
  -webkit-transform: translateY(-100%);
  /* Safari */
}
.language-selectors a {
  color: #484848;
  font-weight: bold;
  margin: 0 0 0 1rem;
  font-size: 0.875rem;
  line-height: 1.06rem;
}