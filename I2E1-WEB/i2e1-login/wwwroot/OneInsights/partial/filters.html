<div class="container-fluid">
    <form class="data-filter-form">
        <span class="error-msg" ng-show="filterErrors">{{filterErrors}}</span>
        <div class="row fiter-section widget">
        <div class="col-md-4 datepicker no-pad">
            <div class="btn btn-transparent {{$parent.filterFormData.editFilters ? 'disabled':''}}" ng-click="validateDates('filterStartDate')">
                <i class="fa fa-calendar" aria-hidden="true" ng-click="isFromDatePickerOpen = !isFromDatePickerOpen"></i>
                <input type="text"
                       class="form-control"
                       uib-datepicker-popup="{{$parent.filterFormData.dateFormat}}"
                       ng-model="$parent.filterFormData.startDate"
                       is-open="isFromDatePickerOpen"
                       ng-click="isFromDatePickerOpen = !isFromDatePickerOpen"
                       datepicker-options="startDateOptions"
                       ng-required="true"
                       show-button-bar="false"
                       on-open-focus="false"
                       alt-input-formats="altInputFormats"
                       ng-change="refreshParentFilter();validateDates('filterStartDate');"
                       id="filterStartDate" />
                <i class="fa fa-angle-down" aria-hidden="true" ng-click="isFromDatePickerOpen = !isFromDatePickerOpen"></i>
            </div>
            to
            <div class="btn btn-transparent {{$parent.filterFormData.editFilters ? 'disabled':''}}" ng-click="validateDates('filterEndDate')">
                <i class="fa fa-calendar" aria-hidden="true" ng-click="isToDatePickerOpen = !isToDatePickerOpen"></i>
                <input type="text" class="form-control"
                       uib-datepicker-popup="{{$parent.filterFormData.dateFormat}}"
                       ng-model="$parent.filterFormData.endDate"
                       is-open="isToDatePickerOpen"
                       ng-click="isToDatePickerOpen=!isToDatePickerOpen"
                       datepicker-options="endDateOptions"
                       ng-required="true"
                       show-button-bar="false"
                       on-open-focus="false"
                       alt-input-formats="altInputFormats"
                       ng-change="refreshParentFilter()"
                       id="filterEndDate" />
                <i class="fa fa-angle-down" aria-hidden="true" ng-click="isToDatePickerOpen = !isToDatePickerOpen"></i>
            </div>
        </div>
        <div class="col-md-8 text-right-align no-pad">

            <multiselect-dropdown class="multiselect-dropdown"
                                  disabled="$parent.filterFormData.editFilters"
                                  label-text="Select City"
                                  model="$parent.filters.cities"
                                  options="$parent.filterFormData.cities"
                                  onchangecallback="getLocationsForCity()"
                                  ng-click="refreshParentFilter()"></multiselect-dropdown>
      
            
            <multi-location-select-dropdown class="multiselect-dropdown"
                                            disabled="$parent.filterFormData.editFilters"
                                            label-text="Select Store"
                                            model="$parent.filters.nasids"
                                            options="$parent.allStores"
                                            ng-click="refreshParentFilter()"
                                            onchangecallback="getMinDateForStores()"></multi-location-select-dropdown>


            <button ng-show="$parent.filterFormData.refreshFilter" type="submit" class="btn btn-primary-green apply-data-filters pull-right" ng-click="applyFilters()">Refresh</button>
        </div>
    </div>
    </form>
</div>
<!--
{{$parent.filters}}
-->

