<div class="row info-section">
    <div class="pull-right" id="dropdown-hidden-container">
        <div class="btn-group" uib-dropdown auto-close="disabled">
            <button id="metric-info-btn" type="button" class="btn btn-primary btn-primary-green" uib-dropdown-toggle>
                Understand your metrics <i class="fa fa-angle-down" aria-hidden="true"></i><i class="fa fa-angle-up" aria-hidden="true"></i>
            </button>
            <ul class="dropdown-menu dropdown-menu-right" uib-dropdown-menu role="menu" aria-labelledby="btn-append-to">
                <li class="desc-section" role="menuitem">
                    <h6>Reading the metrics</h6>
                    <ul class="list-group">
                        <li class="list-group-item">
                            <div ng-if="locationsInsight.locationMetricAvgData.vicinity_footfall.store.current">
                                <h5 class="metric-stat bounceInText">{{locationsInsight.locationMetricAvgData.vicinity_footfall.store.current | number}}<sup ng-if="locationsInsight.locationMetricAvgData.vicinity_footfall.store.current" class="{{locationsInsight.locationMetricAvgData.vicinity_footfall.store.change > 0 ? 'increment' : 'decrement'}}">  <span ng-if="locationsInsight.locationMetricAvgData.vicinity_footfall.store.change > 0">+</span>{{locationsInsight.locationMetricAvgData.vicinity_footfall.store.change + '%'}}</sup></h5>
                            </div>
                            Your current metric/ Vs previous period
                        </li>
                        <li ng-if="locationsInsight.locationMetricAvgData.vicinity_footfall.industry.current" class="list-group-item">
                            <div>
                                <h5 class="regular-grey bounceInText">Industry: {{locationsInsight.locationMetricAvgData.vicinity_footfall.industry.current | number}} / <span ng-if="locationsInsight.locationMetricAvgData.vicinity_footfall.industry.change > 0">+</span>{{locationsInsight.locationMetricAvgData.vicinity_footfall.industry.change + '%'}} </h5>
                            </div>
                            Industry current metric/ Vs. previous period
                        </li>
                    </ul>
                </li>
                <li class="desc-section" role="menuitem">
                    <h6>Metric Description</h6>
                    <ul class="list-group">
                        <li class="list-group-item">
                            <span class="section-heading">Daily vicinity footfall:</span> Count of people detected in and around your store daily
                        </li>
                        <li class="list-group-item">
                            <span class="section-heading">Daily store footfall:</span> Count of people who stepped in your store – your customers
                        </li>
                        <li class="list-group-item">
                            <span class="section-heading">Walk in rate:</span> Percentage of people in vicinity who stepped in your store
                        </li>
                        <li class="list-group-item">
                            <span class="section-heading">Avg. dwell time:</span> Average time your customer spends in your store
                        </li>
                        <li class="list-group-item">
                            <span class="section-heading">Repeat rate:</span> Ratio of customers who have visited you again in the past 30 days
                        </li>
                    </ul>
                </li>
            </ul>
        </div>
    </div>
</div>


<!--{{locationsInsight.locationMetricAvgData}}-->
<div class="row widget widget-card">
    <div class="row no-margin">
        <table class="location-metrics">
            <tr>
                <td ng-repeat="(key, metricAvgData) in locationsInsight.locationMetricAvgData">
                    <label for="{{key}}">
                        <input type="checkbox" value="{{key}}" ng-model="locationsInsight.chartDataObject[key].isVisible" id="{{key}}" ng-click="getLocationMetricsGraph($event)">
                        <div class="metric-card custom-click-element"
                             title="{{metricAvgData.label}}"
                             data-action="{{locationsInsight.chartDataObject[key].isVisible ? 'Closing' : 'Opening' }} trend graph for {{metricAvgData.label}}">
                            <h5 class="metric-name" ng-class="key == 'vicinity_footfall' || key == 'instore_footfall' ? 'show-imp-after' : '' ">{{metricAvgData.label}}</h5>
                            <div class="metric-icon"><img src="{{metricAvgData.icon}}" alt="{{metricAvgData.label}}"></div>
                            <div ng-if="metricAvgData.store.current">
                                <h5 class="metric-stat bounceInText" ng-if="metricAvgData.store.current != 'n/a'">
                                    {{metricAvgData.store.current | number}}{{key == 'conversion_rate' || key == 'repeat_rate' ? '%' : ''}} {{key == 'dwell_time' ? 'min' : ''}}
                                    <sup ng-if="metricAvgData.store.change && metricAvgData.store.change != 0 && metricAvgData.store.change != 'n/a'" uib-tooltip-html="lastDateRangeHtml" tooltip-append-to-body="true" class="{{metricAvgData.store.change > 0 ? 'increment' : 'decrement'}}">  <span ng-if="metricAvgData.store.change > 0">+</span>{{metricAvgData.store.change + '%'}}</sup>
                                    <sup ng-if="!metricAvgData.store.change && metricAvgData.store.change == 0" uib-tooltip-html="lastDateRangeHtml" tooltip-append-to-body="true" class="increment">  0%</sup>
                                    <sup ng-if="metricAvgData.store.change && metricAvgData.store.change == 'n/a'" uib-tooltip-html="lastDateRangeHtml" tooltip-append-to-body="true" class="">  N/A</sup>
                                </h5>
                                <h5 class="metric-notavailable bounceInText" ng-if="metricAvgData.store.change == 'n/a'">Data not available</h5>
                            </div>
                            <div ng-if="!metricAvgData.store.current">
                                <img class="metric-stat" style="height:2.4rem;" src="images/data-loader.gif" alt="Loading....." />
                            </div>
                            <div class="industy_comparision_stats" ng-class="metric.get_industy_comparision == '1' ? '' :'display-none'" ng-show="metric.get_industy_comparision == '1'">
                                <div ng-if="metricAvgData.industry.current">
                                    <p class="success" ng-if="metricAvgData.store.current.percentChange(metricAvgData.industry.current) >= 0">{{metricAvgData.store.current.percentChange(metricAvgData.industry.current).round(1)}} % more than industry</p>
                                    <p class="error" ng-if="metricAvgData.store.current.percentChange(metricAvgData.industry.current) < 0">{{-metricAvgData.store.current.percentChange(metricAvgData.industry.current).round(1)}} % less than industry</p>
                                    <!--<p class="regular-grey bounceInText" ng-if="metricAvgData.industry.current != 'n/a'">
                                        Industry: {{metricAvgData.industry.current | number}}{{key == 'conversion_rate' || key == 'repeat_rate' ? '%' : ''}} {{key == 'dwell_time' ? 'min' : ''}}
                                        <span>/ </span>
                                        <span ng-if="metricAvgData.industry.change && metricAvgData.industry.change > 0">+</span>
                                        <span ng-if="metricAvgData.industry.change && metricAvgData.industry.change != 0 && metricAvgData.industry.change != 'n/a'" uib-tooltip-html=" lastdaterangehtml" tooltip-append-to-body="true">
                                            {{metricAvgData.industry.change + '%'}}
                                        </span>
                                        <span ng-if="!metricAvgData.industry.change && metricAvgData.industry.change == 0" uib-tooltip-html=" lastdaterangehtml" tooltip-append-to-body="true">
                                            0%
                                        </span>

                                        <span ng-if="metricAvgData.industry.change && metricAvgData.industry.change == 'n/a'" uib-tooltip-html=" lastdaterangehtml" tooltip-append-to-body="true">
                                            N/A
                                        </span>
                                    </p>
                                    <p class="regular-grey bounceInText" ng-if="metricAvgData.industry.current == 'n/a'">
                                        Industry: Data not available
                                    </p>-->
                                </div>
                                <div ng-if="!metricAvgData.industry.current">
                                    <img src="images/data-loader.gif" alt="Loading...." />
                                </div>
                            </div>
                        </div>
                    </label>
                </td>
            </tr>
        </table>
    </div>
    <footer class="widget-footer">
        <div class="form-group">
            <div class="checkbox">
                <label for="inlineCheckbox1" class="checkbox-inline">
                    <input type="checkbox" id="inlineCheckbox1"
                           ng-model="metric.get_industy_comparision"
                           ng-true-value="'1'" ng-false-value="'0'"
                           ng-change="getIndusrtyComparision()"
                           class="custom-click-element"
                           title="Get Industry Comparisions"
                           data-action="{{metric.get_industy_comparision == 0 ? 'Display' : 'Hide' }} industry comparisions"> Get Industry Comparisions
                </label>
                <div class="pull-right info-italic" style="margin-top: 5px;position:relative;">
                    Click on the cards above to get multiple charts below
                </div>

            </div>
        </div>
    </footer>
</div>


<div class="row no-margin" ng-if="!locationsInsight.chartDataObject.vicinity_footfall">
    <h1 class="loading-message" data-text="Please wait while we are loading your data…">Please wait while we are loading your data…</h1>
</div>

<!--<div class="row widget widget-card transparent-card" ng-if="countKey(locationsInsight.chartDataObject) > 0" style="margin-bottom:1.5rem;">
    <footer class="widget-footer" style="position:relative;left:0;padding:0">
        <div class="pull-right info-italic">
            Click on chart legends to toggle between store, brand and industry view
        </div>
    </footer>
</div>-->
<masonry class="row widget widget-card transparent-card" style="margin-bottom:0;" ng-if="locationsInsight.chartDataObject.conversion_rate || locationsInsight.chartDataObject.dwell_time || ocationsInsight.chartDataObject.repeat_rate" reload-on-resize reload-on-show preserve-order masonry-options="{ originTop: false }">
    <div class="graph-card masonry-brick" ng-repeat="(key, chartData) in locationsInsight.chartDataObject">
     
        <div class="graph-card-content" ng-if="key == 'conversion_rate' && chartData.isVisible">
            <header class="graph-card-title">
                <div class="graph-formaters">
                    <div class="metric-name">Walk In Rate</div>
                </div>
                <div class="graph-formaters right-aligned" data-ng-include="'graph-formaters.html'"></div>
            </header>
            <div class="graph-container" data-ng-include="'graph-container.html'"></div>
        </div>

        <div class="graph-card-content" ng-if="key == 'dwell_time' && chartData.isVisible">
            <header class="graph-card-title">
                <div class="graph-formaters">
                    <div class="metric-name">Avg Dwell Time (minutes)</div>
                </div>
                <div class="graph-formaters right-aligned" data-ng-include="'graph-formaters.html'"></div>
            </header>
            <div class="graph-container" data-ng-include="'graph-container.html'"></div>
        </div>

        <div class="graph-card-content" ng-if="key == 'repeat_rate' && chartData.isVisible">
            <header class="graph-card-title">
                <div class="graph-formaters">
                    <div class="metric-name">Repeat Rate</div>
                </div>
                <div class="graph-formaters right-aligned" data-ng-include="'graph-formaters.html'"></div>
            </header>
            <div class="graph-container" data-ng-include="'graph-container.html'"></div>
        </div>
    </div>
</masonry>

<!--{{locationsInsight.chartDataObject.vicinity_footfall}}-->
<masonry class="row widget widget-card transparent-card" reload-on-resize reload-on-show preserve-order masonry-options="{ originTop: false }">
    <div class="graph-card masonry-brick" style="margin-top:0;" ng-repeat="(key, chartData) in locationsInsight.chartDataObject">
        <div class="graph-card-content" ng-show="key=='vicinity_footfall' && chartData.isVisible">
            <header class="graph-card-title">
                <div class="graph-formaters">
                    <div class="metric-name">* Daily Vicinity Footfall</div>
                </div>
                <div class="graph-formaters right-aligned" data-ng-include="'graph-formaters.html'"></div>
            </header>
            <div class="graph-container" data-ng-include="'graph-container.html'"></div>
        </div>

        <div class="graph-card-content" ng-show="key == 'instore_footfall' && chartData.isVisible">
            <header class="graph-card-title">
                <div class="graph-formaters">
                    <div class="metric-name">* Daily Store Footfall</div>
                </div>
                <div class="graph-formaters right-aligned" data-ng-include="'graph-formaters.html'"></div>
            </header>
            <div class="graph-container" data-ng-include="'graph-container.html'"></div>
        </div>
        
    </div>
</masonry>
<div class="row widget widget-card transparent-card" ng-if="locationsInsight.chartDataObject.vicinity_footfall">
    <footer class="widget-footer" style="position:relative;left:0;padding:0">
        <div class="pull-right info-italic show-imp-before" style="margin-top: 5px;position:relative;">
            Note: Vicinity and instore footfall values are on a scale of 1000 and 100 respectively
        </div>
    </footer>
</div>



<div class="row widget widget-card transparent-card" ng-hide="true">
    <div class="col-md-6 graph-stats-card">
        <div class="col-md-6 stats-info">
            <h6 class="metric-name">Peak Day</h6>
            <h5 class="metric-stat"><span class="bounceInText" ng-show="locationsInsight.stores.top_day_data.top_day">{{locationsInsight.stores.top_day_data.top_day}}</span></h5>
            <p class="regular-grey"><span class="bounceInText" ng-show="locationsInsight.industry.top_day_data.top_day">Industry: {{locationsInsight.industry.top_day_data.top_day}} </span></p>
        </div>
        <div class="col-md-6 stats-info">
            <h6 class="metric-name">Peak Day Walk In</h6>
            <h5 class="metric-stat"><span class="bounceInText" ng-show="locationsInsight.stores.top_day_data.avg_walk_in">{{locationsInsight.stores.top_day_data.avg_walk_in}}</span></h5>
            <p class="regular-grey"><span class="bounceInText" ng-show="locationsInsight.industry.top_day_data.avg_walk_in">Industry: {{locationsInsight.industry.top_day_data.avg_walk_in}} </span></p>
        </div>
    </div>

    <div class="col-md-6 graph-stats-card">
        <div class="col-md-6 stats-info">
            <h6 class="metric-name">Peak Hours</h6>
            <h5 class="metric-stat"><span class="bounceInText" ng-show="locationsInsight.stores.top_hour_data.top_hour">{{locationsInsight.stores.top_hour_data.top_hour}}</span></h5>
            <p class="regular-grey"><span class="bounceInText" ng-show="locationsInsight.industry.top_hour_data.top_hour">Industry: {{locationsInsight.industry.top_hour_data.top_hour}} </span></p>
        </div>
        <div class="col-md-6 stats-info">
            <h6 class="metric-name">Peak Hours Walk In</h6>
            <h5 class="metric-stat"><span class="bounceInText" ng-show="locationsInsight.stores.top_hour_data.avg_walk_in">{{locationsInsight.stores.top_hour_data.avg_walk_in}}</span></h5>
            <p class="regular-grey"><span class="bounceInText" ng-show="locationsInsight.industry.top_hour_data.avg_walk_in">Industry: {{locationsInsight.industry.top_hour_data.avg_walk_in}} </span></p>
        </div>
    </div>
</div>


<script type="text/ng-template" id="graph-formaters.html">
    <form class="form-inline ng-pristine ng-valid" role="form">
        <div class="form-group">
            <div class="radio">
                <label for="{{key}}_hourly" class="radio-inline" style="margin-right:0.5rem;">
                     <input id="vicinity_footfall_hourly"
                                  type="radio" value="hourly" ng-change="creatGraphForLocationMetrics(key)"
                                  name="{{key}}_hourly"
                                  ng-model="locationsInsight.chartDataObject[key].tsType"
                                  class="custom-click-element"
                                  title="Type of trends"
                                  data-action="Changed daily {{key}} trend graph view to hourly">
                     Hourly
                </label>
            </div>
        </div>
        <div class="form-group">
            <div class="radio">
                <label for="{{key}}_daily" class="radio-inline" style="margin-right:0.5rem;">
                    <input id="{{key}}_daily" type="radio"
                                 ng-model="locationsInsight.chartDataObject[key].tsType"
                                 value="daily" ng-change="creatGraphForLocationMetrics(key)" name="{{key}}_daily"
                                 class="custom-click-element"
                                 title="Type of trends"
                                 data-action="Changed daily {{key}} trend graph view to daily">
                     Daily 
                </label>
            </div>
        </div>
        <div class="form-group">
            <div class="radio">
                <label for="{{key}}_weekly" class="radio-inline" style="margin-right:0.5rem;">
                    <input id="{{key}}_weekly" type="radio"
                                  ng-model="locationsInsight.chartDataObject[key].tsType"
                                  value="weekly" ng-change="creatGraphForLocationMetrics(key)" name="{{key}}_weekly"
                                  class="custom-click-element"
                                  title="Type of trends"
                                  data-action="Changed daily {{key}} trend graph view to weekly">
                     Weekly
                </label>
            </div>
        </div>
        <div class="form-group">
            <div class="radio">
                <label for="{{key}}_monthly" class="radio-inline" style="margin-right:0.5rem;">
                     <input id="{{key}}_monthly" type="radio"
                                   ng-model="locationsInsight.chartDataObject[key].tsType"
                                   value="monthly" ng-change="creatGraphForLocationMetrics(key)" name="{{key}}_monthly"
                                   class="custom-click-element"
                                   title="Type of trends"
                                   data-action="Changed daily {{key}} trend graph view to monthly">
                     Monthly
                </label>
            </div>
        </div>
        <div ng-if="locationsInsight.chartDataObject[key].data.rows.length > 0" class="form-group graph-export" uib-dropdown>
            <a class="" data-toggle="dropdown" href="#" uib-dropdown-toggle>
                <i class="glyphicon glyphicon-download"></i>
            </a>
            <ul class="dropdown-menu-right" uib-dropdown-menu role="menu">
                <li role="menuitem">
                    <a href=""
                       class="custom-click-element"
                       title="Download location metric graph data"
                       data-action="Export csv for {{key}} trend"
                       ng-click="googleDataTableToCSV(locationsInsight.chartDataObject[key],key)">Save as CSV</a>
                </li>
                <li class="divider"></li>
                <li role="menuitem">
                    <a href=""
                       class="custom-click-element"
                       title="Download location metric graph data"
                       data-action="Save png image for {{key}} trend"
                       ng-click="saveAsImg(key+'_chart_container', locationsInsight.chartDataObject[key].tsType, key)">Save as PNG</a>
                </li>
            </ul>
        </div>
    </form>
</script>

<script type="text/ng-template" id="graph-container.html">
    <div ng-if="locationsInsight.chartDataObject[key].data.rows.length > 0"
         id="{{key}}_chart_container"
         google-chart chart="locationsInsight.chartDataObject[key]"
         agc-reload-on-wrapper-resize="true"
         agc-on-select="hideSeries(selectedItem,key)"
         style="height:250px;"></div>
    <div ng-if="locationsInsight.chartDataObject[key].tsType =='daily' && locationsInsight.chartDataObject[key].data.rows.length > 0 " id="point_legend" style="text-align:center;">
        <div style="display:inline-block;margin:0px 5px;font-size: 11px;color: #222222;text-anchor: start;font-family: Arial;">
            <div style="width:10px; height:10px; border-radius:50%; background-color:#71c044; display:inline-block;"></div>
            Weekend
        </div>
        <div style="display:inline-block;margin:0px 5px;font-size: 11px;color: #222222;text-anchor: start;font-family: Arial;">
            <div style="width:10px; height:10px; border-radius:50%; background-color:#ffd503; display:inline-block;"></div>
            Holiday
        </div>
    </div>
    <div ng-if="locationsInsight.chartDataObject[key].data.rows.length == 0" style="height:250px;">
        <h5 class="metric-notavailable text-center">Data not available</h5>
    </div>
</script>