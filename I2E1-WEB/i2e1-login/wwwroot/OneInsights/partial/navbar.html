<div class="container-fluid">
    <!-- .navbar-header contains links seen on xs & sm screens -->
    <div class="navbar-header">
        <!--<ul class="nav navbar-nav">
            <li>
                <a class="visible-xs navbar-toggle toggle-navigation-collapse" nav-collapse-toggler type="click" href="#" title="Show/hide sidebar" data-placement="bottom" data-tooltip>
                    <span class="icon-bar"></span>
                    <span class="icon-bar"></span>
                    <span class="icon-bar"></span>
                </a>
            </li>
        </ul>-->
        <div class="navbar-header">
            <button class="navbar-toggle toggle-navigation-collapse" nav-collapse-toggler type="click" aria-expanded="true" aria-controls="navbar">
                <span class="sr-only">Toggle navigation</span>
                <span class="icon-bar"></span>
                <span class="icon-bar"></span>
                <span class="icon-bar"></span>
            </button>
            <a class="navbar-brand" href="/Client/Home#/landing/home">
                <img src="/images/logo-white.png" />
            </a>
        </div>
    </div>
    <!-- this part is hidden for xs screens -->
    <div class="collapse navbar-collapse">
        
        <ul class="nav navbar-nav navbar-right">
            <li style="min-width: 25rem;">
                <!--<select ng-change="setBrand()" ng-model="$parent.filters.partnerId" style="color:#fff">
                    <option value="">-- Select Brand --</option>
                    <option ng-repeat="partner in partnersForFilterList" value="{{partner.partnerId}}" ng-selected="$parent.filters.partnerId == partner.partnerId">{{partner.partnerName}}</option>
                </select>-->
                <div class="col-md-12">
                    <ui-select ng-model="$parent.filters.partnerId" theme="selectize" title="Choose a brand" ng-change="setBrand()">
                        <ui-select-match placeholder="Select a brand">{{$select.selected.partnerName}}</ui-select-match>
                        <ui-select-choices repeat="partner.partnerId as partner in partnersForFilterList | filter: $select.search">
                            <div ng-bind-html="partner.partnerName | highlight: $select.search"></div>
                        </ui-select-choices>
                    </ui-select>
                </div>
            </li>
            <li class="username">
                <a>
                    <i class="glyphicon glyphicon-user mr-xs"></i>
                    <span id="loggedInUserName">{{loggedInUser.name}}</span>
                </a>
            </li>
            <li class=""></li>
            <li>
                <a style="cursor:pointer" ng-click="logout()">
                    Logout
                </a>
            </li>
        </ul>
    </div>
</div>
