<div class="row info-section">
    <div class="pull-right" id="dropdown-hidden-container">
        <div class="btn-group" uib-dropdown auto-close="disabled">
            <button id="metric-info-btn" type="button" class="btn btn-primary btn-primary-green" uib-dropdown-toggle>
                Understand your metrics <i class="fa fa-angle-down" aria-hidden="true"></i><i class="fa fa-angle-up" aria-hidden="true"></i>
            </button>
            <ul class="dropdown-menu dropdown-menu-right" uib-dropdown-menu role="menu" aria-labelledby="btn-append-to">
                <li class="desc-section" role="menuitem">
                    <h6>Reading the metrics</h6>
                    <ul class="list-group">
                        <li class="list-group-item">
                            <div>
                                <h5 class="metric-stat" ng-show="!benfits.data_captured.t_customer_targeted"><img src="images/data-loader.gif" alt="Loading.."></h5>
                                <h5 class="metric-stat bounceInText" ng-show="benfits.data_captured.t_customer_targeted">{{benfits.data_captured.t_customer_targeted | number}}<sup ng-if="benfits.data_captured.t_customer_targeted_change" class="{{benfits.data_captured.t_customer_targeted_change > 0 ? 'increment' : 'decrement'}}">  <span ng-if="benfits.data_captured.t_customer_targeted_change > 0">+</span>{{benfits.data_captured.t_customer_targeted_change | round:1}}%</sup></h5>
                            </div>
                            Your current metric/ Vs previous period
                        </li>
                    </ul>
                </li>
                <li class="desc-section" role="menuitem">
                    <h6>Metric Description</h6>
                    <b>Data captured:</b>
                    <ul class="list-group">
                        <li class="list-group-item">
                            <span class="section-heading">Customers detected:</span> Count of unique visitors detected in your store
                        </li>
                        <li class="list-group-item">
                            <span class="section-heading">
                                Phone numbers captured:
                            </span> Count of unique customers who logged on to your Wi-Fi with
                            phone numbers
                        </li>
                        <li class="list-group-item">
                            <span class="section-heading">
                                Email IDs collected:
                            </span> Count of unique customers who logged on to your Wi-Fi and provided an
                            email ID at login
                        </li>
                    </ul>
                    <b>Campaign results:</b>
                    <ul class="list-group">
                        <li class="list-group-item">
                            <span class="section-heading">
                                Phone numbers captured:
                            </span> Count of unique customers who logged on to your Wi-Fi with their
                            phone numbers
                        </li>
                        <li class="list-group-item">
                            <span class="section-heading">Customers targeted:</span> Count of customers targeted via i2e1 promotion campaigns
                        </li>
                        <li class="list-group-item">
                            <span class="section-heading">
                                Conversion rates:
                            </span> Count of customers who walked in to your store post promotion
                            campaigns
                        </li>
                        <li class="list-group-item">
                            <span class="section-heading">
                                Revenue earned:
                            </span> Estimated billings amount from customers who walked in to your store
                            post promotion campaigns
                        </li>
                    </ul>
                </li>
            </ul>
        </div>
    </div>
</div>

<show-benfits-data-captured post-data="$parent.data_captured"></show-benfits-data-captured>
<!--<div class="row widget widget-card" ng-if="benefits.data_captured.t_active_campaign > 0">-->
<div class="row widget widget-card">
    <header class="widget-title">How well do your campaigns work?</header>
    <div class="campaign-info" ng-if="benfits.data_captured.t_customer_targeted > 0">
        <div class="col-md-2 campaign-items">
            <div class="metric-icon"><img src="images/people.svg" alt="Gender Distribution" /></div>
        </div>
        <div class="col-md-7 campaign-items">
            <table class="steps">
                <tr>
                    <td>
                        <h6 class="metric-name">Customers Targeted</h6>
                    </td>
                    <td>
                        <h6 class="metric-name">Conversion Rate</h6>
                    </td>
                    <td>
                        <h6 class="metric-name">Revenue Earned</h6>
                    </td>
                </tr>
                <tr class="tringualr-steps">
                    <td>
                        <span class="blue"></span>
                    </td>
                    <td>
                        <span class="green"></span>
                    </td>
                    <td>
                        <span class="yellow"></span>
                    </td>
                </tr>
                <tr>
                    <td>
                        <h5 class="metric-stat" ng-show="!benfits.data_captured.t_customer_targeted"><img src="images/data-loader.gif" alt="Loading.."></h5>
                        <h5 class="metric-stat bounceInText" ng-show="benfits.data_captured.t_customer_targeted">{{benfits.data_captured.t_customer_targeted | number}}<sup uib-tooltip-html="lastDateRangeHtml" tooltip-append-to-body="true" ng-if="benfits.data_captured.t_customer_targeted_change != 0" class="{{benfits.data_captured.t_customer_targeted_change > 0 ? 'increment' : 'decrement'}}">  <span ng-if="benfits.data_captured.t_customer_targeted_change > 0">+</span>{{benfits.data_captured.t_customer_targeted_change | round:1}}%</sup></h5>
                    </td>
                    <td>
                        <h5 class="metric-stat" ng-show="!benfits.data_captured.t_conversion_rate"><img src="images/data-loader.gif" alt="Loading.."></h5>
                        <h5 class="metric-stat bounceInText" ng-show="benfits.data_captured.t_conversion_rate">{{benfits.data_captured.t_conversion_rate | round:1}}%<sup uib-tooltip-html="lastDateRangeHtml" tooltip-append-to-body="true" ng-if="benfits.data_captured.t_conversion_rate_change" class="{{benfits.data_captured.t_conversion_rate_change > 0 ? 'increment' : 'decrement'}}">  <span ng-if="benfits.data_captured.t_conversion_rate_change > 0">+</span>{{benfits.data_captured.t_conversion_rate_change | round:1}}%</sup></h5>
                    </td>
                    <td>
                        <h5 class="metric-stat" ng-show="!benfits.data_captured.t_revenue_earned"><img src="images/data-loader.gif" alt="Loading.."></h5>
                        <h5 class="metric-stat bounceInText" ng-show="benfits.data_captured.t_revenue_earned">{{benfits.data_captured.t_revenue_earned | number}}<sup uib-tooltip-html="lastDateRangeHtml" tooltip-append-to-body="true" ng-if="benfits.data_captured.t_revenue_earned_change != 0" class="{{benfits.data_captured.t_revenue_earned_change > 0 ? 'increment' : 'decrement'}}">  <span ng-if="benfits.data_captured.t_revenue_earned_change > 0">+</span>{{benfits.data_captured.t_revenue_earned_change | round:1}}%</sup></h5>
                    </td>
                </tr>
            </table>
        </div>
        <div class="col-md-3 campaign-items">
            <button class="btn btn-primary custom-click-element"
                    title="View Campaigns Button"
                    data-action="View all campaigns for benefit articulation" 
                    ng-click="viewCampaigns()">View Campaigns</button>
        </div>
    </div>
    <div uib-collapse="!isCollapsed">
        <div class="campaign-history">
            <table class="table table-striped">
                <thead>
                    <tr>
                        <th>Campaign Type</th>
                        <th>#Targeted</th>
                        <th>Conversion</th>
                        <th>Revenue</th>
                    </tr>
                </thead>
                <tbody ng-repeat="report in benfits.data_captured.t_prime_reports">
                    <tr ng-init="toggle[$index] = false" 
                        ng-click="toggle[$index] = !toggle[$index]" 
                        style="cursor:pointer;"
                        class="custom-click-element"
                        title="View detailed campaign report"
                        data-action="View detailed campaign report for {{report.name}}">
                        <td>{{report.name}}</td>
                        <td>
                            <h5 class="metric-stat"><span class="bounceintext" ng-show="report.targeted">{{report.targeted | number}}<sup ng-if="report.last_period_targeted != '0'" uib-tooltip-html="lastDateRangeHtml" tooltip-append-to-body="true" class="{{report.targeted > report.last_period_targeted ? 'increment' : 'decrement'}}">  {{getPercentChange(report.targeted,report.last_period_targeted)}}%</sup></span></h5>
                            <h5 class="metric-stat"><span class="bounceintext" ng-show="!report.targeted"><img src="images/data-loader.gif" alt="Loading.."></span></h5>
                        </td>
                        <td>
                            <h5 class="metric-stat"><span class="bounceintext" ng-show="report.conv_rate">{{report.conv_rate | round:1 }}%<sup ng-if="report.last_period_conv_rate != '0'" uib-tooltip-html="lastDateRangeHtml" tooltip-append-to-body="true" class="{{report.conv_rate > report.last_period_conv_rate ? 'increment' : 'decrement'}}">  {{getPercentChange(report.conv_rate,report.last_period_conv_rate) | round:1 }}%</sup></span></h5>
                            <h5 class="metric-stat"><span class="bounceintext" ng-show="!report.conv_rate"><img src="images/data-loader.gif" alt="Loading.."></span></h5>
                        </td>
                        <td>
                            <h5 class="metric-stat"><span class="bounceintext" ng-show="report.revenue">{{report.revenue | number}}<sup ng-if="report.last_period_revenue != '0'" uib-tooltip-html="lastDateRangeHtml" tooltip-append-to-body="true" class="{{report.targeted > report.last_period_revenue ? 'increment' : 'decrement'}}">  {{getPercentChange(report.revenue,report.last_period_revenue)}}%</sup></span></h5>
                            <h5 class="metric-stat"><span class="bounceintext" ng-show="!report.revenue"><img src="images/data-loader.gif" alt="Loading.."></span></h5>
                        </td>
                    </tr>
                    <tr ng-if="toggle[$index]">
                        <td ng-if="report.detail_report.length > 0" colspan="4" style="padding: 1rem 0;">
                            <table class="table table-bordered">
                                <thead>
                                    <tr>
                                        <th>Campaign Name</th>
                                        <th>Date</th>
                                        <th>#Targeted</th>
                                        <th>Conversion</th>
                                        <th>Revenue</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr ng-repeat="campain in report.detail_report">
                                        <td>{{campain.campaign_name}}</td>
                                        <td ng-bind="formatDate(campain.date)"></td>
                                        <td>
                                            <h5 class="metric-stat"><span class="bounceintext" ng-show="campain.targeted">{{campain.targeted | number}}</span></h5>
                                            <h5 class="metric-stat"><span class="bounceintext" ng-show="!campain.targeted"><img src="images/data-loader.gif" alt="Loading.."></span></h5>
                                        </td>
                                        <td>
                                            <h5 class="metric-stat"><span class="bounceintext" ng-show="campain.conversion">{{campain.conversion | round:1 }}%</span></h5>
                                            <h5 class="metric-stat"><span class="bounceintext" ng-show="!campain.conversion"><img src="images/data-loader.gif" alt="Loading.."></span></h5>
                                        </td>
                                        <td>
                                            <h5 class="metric-stat"><span class="bounceintext" ng-show="campain.revenue">{{campain.revenue | number}}</span></h5>
                                            <h5 class="metric-stat"><span class="bounceintext" ng-show="!campain.revenue"><img src="images/data-loader.gif" alt="Loading.."></span></h5>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>

    <div class="campaign-info" ng-if="benfits.data_captured.t_customer_targeted == 0">
        <h1 class="no-campaigns-found" style="font-size: 1.6rem;margin: 5rem auto;">
            You are currently not running any campaigns on the i2e1 network. Log on to i2e1 admin portal
            <a href="https://www.i2e1.in/Client/Index#/login" 
               class="custom-click-element"
               title="Creat campaign report"
               data-action="https://www.i2e1.in/Client/Index#/login">here</a> to start campaigns
        </h1>
    </div>
</div>