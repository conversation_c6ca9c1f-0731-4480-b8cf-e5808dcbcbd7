<div class="row info-section">
    <div class="pull-right" id="dropdown-hidden-container">
        <div class="btn-group" uib-dropdown auto-close="disabled">
            <button id="metric-info-btn" type="button" class="btn btn-primary btn-primary-green" uib-dropdown-toggle>
                Understand your metrics <i class="fa fa-angle-down" aria-hidden="true"></i><i class="fa fa-angle-up" aria-hidden="true"></i>
            </button>
            <ul class="dropdown-menu dropdown-menu-right" uib-dropdown-menu role="menu" aria-labelledby="btn-append-to">
                <li class="desc-section" role="menuitem">
                    <h6>Metric Description</h6>
                    <b>Demographics</b>
                    <ul class="list-group">
                        <li class="list-group-item">
                            <span class="section-heading">Gender distribution:</span> Distribution of your customers across gender bands
                        </li>
                        <li class="list-group-item">
                            <span class="section-heading">Age groups:</span> Distribution of your customers across age bands
                        </li>
                        <li class="list-group-item">
                            <span class="section-heading">Spend capacity:</span> Distribution of your customers across their spend potential
                            <ul>
                                <li>
                                    Low – Customers who are value conscious and are low spenders.
                                    The disposable income is less than 15K per month.
                                </li>
                                <li>
                                    Medium – Customers who hold the potential to spend more but are budget conscious.
                                    The disposable income is around 15K-30K per month
                                </li>
                                <li>
                                    High – Premium customers who always tend to show a high spending pattern.
                                    The disposable income is more than 30K per month.
                                </li>
                            </ul>
                        </li>

                    </ul>
                    <b>Customer loyalty</b>
                    <ul class="list-group">
                        <li class="list-group-item">
                            <span class="section-heading">
                                Customer base:
                            </span> Distribution of your customers based on their loyalty to your brand
                            <ul>
                                <li>New – Customers acquired recently by your brand</li>
                                <li>Loyal – Customers who have been loyal to you and visited you frequently in the past</li>
                                <li>Attritors – Potentially lost customers who have not visited your brand since atleast a month</li>
                            </ul>
                        </li>
                        <li class="list-group-item">
                            <span class="section-heading">Days since last visit:</span> Average days that have passed since a customer visited you
                        </li>
                        <li class="list-group-item">
                            <span class="section-heading">Avg. customers visit in store:</span> Average visits a customer makes to your store
                        </li>
                        <li class="list-group-item">
                            <span class="section-heading">Avg. customers spend in store:</span> Average spend of your customer in your store
                        </li>
                        <li class="list-group-item">
                            <span class="section-heading">Avg. customer spend in industry:</span> Average spend of your customer in your industry
                        </li>
                    </ul>
                    <b>Psychographics</b>
                    <ul class="list-group">
                        <li class="list-group-item">
                            <span class="section-heading">Devices used:</span> Distribution of your customers basis the mobile device brand they use
                        </li>
                    </ul>
                </li>
            </ul>
        </div>
    </div>
</div>


    <div class="row widget widget-card">
        <header class="widget-title" style="margin-bottom: 2.5rem;">What is the profile of your customers?</header>
        <div class="col-md-4 graph-metric-stat">
            <div ng-if="customersInsight.genderDistribution" class="form-group graph-export" uib-dropdown>
                <a class="" data-toggle="dropdown" href="#" uib-dropdown-toggle>
                    <i class="glyphicon glyphicon-download"></i>
                </a>
                <ul class="dropdown-menu-right" uib-dropdown-menu role="menu">
                    <li role="menuitem">
                        <a href=""
                           class="custom-click-element"
                           title="Download demographics"
                           data-action="Export csv for gender distribution"
                           ng-click="donutDataTableToCSV(customersInsight.demographics.genderData,'genderData')">Export CSV</a>
                    </li>
                    <li class="divider"></li>
                    <li role="menuitem">
                        <a href=""
                           class="custom-click-element"
                           title="Download demographics"
                           data-action="Save png image for gender distribution"
                           ng-click="saveScreenshotAsImg('overview_gender_distribution', 'average', 'genderData')">Export PNG</a>
                    </li>
                </ul>
            </div>
            <div id="overview_gender_distribution">
                <h6 class="metric-name">Gender Distribution</h6>

                <div class="circle metric-icon donuts-overlay">
                    <img style="{{customersInsight.genderDistribution.data.rows ? '' : 'animation: pulsate 1s ease-out infinite;'}}" src="images/gender.svg" alt="Gender Distribution" style="margin-bottom:10px;" />
                </div>

                <div google-chart 
                     chart="customersInsight.genderDistribution" 
                     agc-on-ready="placeDonutIcons(chartWrapper)"
                     style="height:200px; width:100%; margin-top:-40px;"></div>
            </div>
        </div>
        <div class="col-md-4 graph-metric-stat">
            <div ng-if="customersInsight.ageDistribution" class="form-group graph-export" uib-dropdown>
                <a class="" data-toggle="dropdown" href="#" uib-dropdown-toggle>
                    <i class="glyphicon glyphicon-download"></i>
                </a>
                <ul class="dropdown-menu-right" uib-dropdown-menu role="menu">
                    nu
                    nu
                    <li role="menuitem">
                        <a href=""
                           class="custom-click-element"
                           title="Download demographics"
                           data-action="Export csv for age distribution"
                           ng-click="donutDataTableToCSV(customersInsight.demographics.ageData,'ageData')">Export CSV</a>
                    </li>
                    <li class="divider"></li>
                    <li role="menuitem">
                        <a href=""
                           class="custom-click-element"
                           title="Download demographics"
                           data-action="Save png image for age distribution"
                           ng-click="saveScreenshotAsImg('overview_age_distribution', 'average', 'ageData')">Export PNG</a>
                    </li>
                </ul>
            </div>
            <div id="overview_age_distribution">
                <h6 class="metric-name">Age Group <span>(years)</span></h6>
                <div class="circle metric-icon donuts-overlay">
                    <img style="{{customersInsight.ageDistribution.data.rows? '' : 'animation: pulsate 1s ease-out infinite;'}}" src="images/birthday.svg" alt="Age Group" />
                </div>

                <div google-chart 
                     chart="customersInsight.ageDistribution"
                     agc-on-ready="placeDonutIcons(chartWrapper)"
                     style="height:200px; width:100%; margin-top:-40px;"></div>
            </div>
        </div>
        <div class="col-md-4 graph-metric-stat">
            <div ng-if="customersInsight.spendDistribution" class="form-group graph-export" uib-dropdown>
                <a class="" data-toggle="dropdown" href="#" uib-dropdown-toggle>
                    <i class="glyphicon glyphicon-download"></i>
                </a>
                <ul class="dropdown-menu-right" uib-dropdown-menu role="menu">
                    <li role="menuitem">
                        <a href=""
                           class="custom-click-element"
                           title="Download demographics"
                           data-action="Export csv for spend capacity distribution"
                           ng-click="donutDataTableToCSV(customersInsight.demographics.spendCapacityData,'spendCapacityData')">Export CSV</a>
                    </li>
                    <li class="divider"></li>
                    <li role="menuitem">
                        <a href=""
                           class="custom-click-element"
                           title="Download demographics"
                           data-action="Save png image for spend capacity distribution"
                           ng-click="saveScreenshotAsImg('overview_spend_distribution', 'average', 'spendCapacityData')">Export PNG</a>
                    </li>
                </ul>
            </div>
            <div id="overview_spend_distribution">
                <h6 class="metric-name">Spend Capacity</h6>
                <div class="circle metric-icon donuts-overlay">
                    <img style="{{customersInsight.spendDistribution.data.rows? '' : 'animation: pulsate 1s ease-out infinite;'}}" src="images/money.svg" alt="Spend Capacity" />
                </div>

                <div google-chart 
                     chart="customersInsight.spendDistribution" 
                     agc-on-ready="placeDonutIcons(chartWrapper)"
                     style="height:200px; width:100%; margin-top:-40px;"></div>
            </div>
        </div>
    </div>

<!--{{customersInsight.psychoGraphics}}-->
<div class="row widget widget-card" ng-if="customersInsight.psychoGraphics">
    
    <header class="widget-title">What type of mobile handsets your customers use?</header>
    <div class="pscyco-graphics">
        <div class="row no-margin" ng-if="!customersInsight.psychoGraphics.data">
            <h1 class="loading-message" data-text="Please wait while we are loading your data…">Please wait while we are loading your data…</h1>
        </div>
        <div ng-if="customersInsight.psychoGraphics.data" class="form-group graph-export" uib-dropdown>
            <a class="" data-toggle="dropdown" href="#" uib-dropdown-toggle>
                <i class="glyphicon glyphicon-download"></i>
            </a>
            <ul class="dropdown-menu-right" uib-dropdown-menu role="menu">
                <li role="menuitem">
                    <a href=""
                       class="custom-click-element"
                       title="Download customer psychographics"
                       data-action="Export csv for customer psychographics"
                       ng-click="googleDataTableToCSV(customersInsight.psychoGraphics,'psychoGraphics')">Export CSV</a>
                </li>
                <li class="divider"></li>
                <li role="menuitem">
                    <a href=""
                       class="custom-click-element"
                       title="Download customer psychographics"
                       data-action="Save image for customer psychographics"
                       ng-click="saveAsImg('psychoGraphics_chart_container', 'average', 'psychoGraphics')">Export PNG</a>
                </li>
            </ul>
        </div>
        <div id="psychoGraphics_chart_container" 
             ng-if="customersInsight.psychoGraphics.data" 
             google-chart chart="customersInsight.psychoGraphics" style="height:150px; width:100%;"></div>
    </div>
</div>









