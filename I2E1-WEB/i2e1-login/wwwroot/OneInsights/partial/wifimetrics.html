<div class="row tile_count">
    <div class="col-md-4 col-sm-6 col-xs-12 tile_stats_count green_bg">
        <span class="count_top"><i class="fa fa-user"></i> Total Users Since Launch</span>
        <div class="count bounceInText" ng-if="wifiMetrics.wifi_users_sice_start && wifiMetrics.wifi_users_sice_start != 'n/a'">{{wifiMetrics.wifi_users_sice_start | number}}</div>
        <div class="count bounceInText metric-notavailable" ng-if="wifiMetrics.wifi_users_sice_start && wifiMetrics.wifi_users_sice_start == 'n/a'">Data not available</div>
        <div class="count" ng-if="!wifiMetrics.wifi_users_sice_start && wifiMetrics.wifi_users_sice_start != 'n/a'" style=""><img src="images/data-loader.gif"></div>
    </div>
    <div class="col-md-4 col-sm-6 col-xs-12 tile_stats_count yellow_bg">
        <span class="count_top"><i class="fa fa-user"></i> Total Users in period selected</span>
        <div class="count bounceInText" ng-if="wifiMetrics.wifi_users_in_time_period && wifiMetrics.wifi_users_in_time_period != 'n/a'">{{wifiMetrics.wifi_users_in_time_period | number}}</div>
        <div class="count bounceInText  metric-notavailable" ng-if="wifiMetrics.wifi_users_in_time_period && wifiMetrics.wifi_users_in_time_period == 'n/a'">Data not available</div>
        <div class="count" ng-if="!wifiMetrics.wifi_users_in_time_period && wifiMetrics.wifi_users_in_time_period != 'n/a'" style=""><img src="images/data-loader.gif"></div>
    </div>
    <div class="col-md-4 col-sm-6 col-xs-12 tile_stats_count blue_bg">
        <span class="count_top"><i class="fa fa-user"></i> Total New Users in period selected</span>
        <div class="count bounceintext" ng-if="wifiMetrics.new_wifi_users && wifiMetrics.new_wifi_users != 'n/a'">{{wifiMetrics.new_wifi_users | number}}</div>
        <div class="count  bounceInText  metric-notavailable" ng-if="wifiMetrics.new_wifi_users && wifiMetrics.new_wifi_users == 'n/a'">Data not available</div>
        <div class="count" ng-if="!wifiMetrics.new_wifi_users && wifiMetrics.new_wifi_users != 'n/a'" style=""><img src="images/data-loader.gif"></div>
    </div>
</div>
<!--{{wifiMetrics.trends}}-->
<div class="row widget widget-card">
    <div class="row no-margin">
        <table class="location-metrics">
            <tr>
                <td ng-repeat="(key, trendsData) in wifiMetrics.trends">
                    <label for="{{key}}">
                        <input type="checkbox" id="{{key}}" ng-model="wifiMetrics.chartDataObject[key].isVisible" value="{{key}}" ng-click="getWiFiTrendGraph($event)">
                        <div class="metric-card custom-click-element" 
                             title="{{trendsData.label}}" 
                             data-action="{{wifiMetrics.chartDataObject[key].isVisible ? 'Closing' : 'Opening' }} trend graph for {{trendsData.label}}">
                            <h5 class="metric-name">{{trendsData.label}}</h5>
                            <div class="metric-icon"><img src="{{trendsData.icon}}" alt="{{trendsData.label}}"></div>
                            <div ng-if="trendsData.store.current">
                                <h5 class="metric-stat bounceInText" ng-if="trendsData.store.current != 'n/a'">
                                    {{trendsData.store.current | number}}{{key == 'wifi_repeat_rate' ? '%' : ''}} {{key == 'users_time_spent' ? 'min' : ''}} {{key == 'wifi_data_usage' ? 'mb' : ''}}
                                    <sup ng-if="trendsData.store.change && trendsData.store.change != 0 && trendsData.store.change != 'n/a'" uib-tooltip-html="lastDateRangeHtml" tooltip-append-to-body="true" class="{{trendsData.store.change > 0 ? 'increment' : 'decrement'}}">  <span ng-if="trendsData.store.change > 0">+</span>{{trendsData.store.change + '%'}}</sup>
                                    <sup ng-if="!trendsData.store.change && trendsData.store.change == 0" uib-tooltip-html="lastDateRangeHtml" tooltip-append-to-body="true" class="increment">  0%</sup>
                                    <sup ng-if="trendsData.store.change && trendsData.store.change == 'n/a'" uib-tooltip-html="lastDateRangeHtml" tooltip-append-to-body="true" class="">  N/A</sup>
                                </h5>
                                <h5 class="metric-notavailable bounceInText" ng-if="trendsData.store.change == 'n/a'">Data not available</h5>
                            </div>
                            <div ng-if="!trendsData.store.current">
                                <img class="metric-stat" style="height:2.4rem;" src="images/data-loader.gif" alt="Loading....." />
                            </div>
                            <div class="industy_comparision_stats" ng-class="wifiMetrics.get_industy_comparision == '1' ? '' :'display-none'" ng-show="wifiMetrics.get_industy_comparision == '1'">
                                <div ng-if="trendsData.industry.current">
                                    <p class="success" ng-if="trendsData.store.current.percentChange(trendsData.industry.current) >= 0">{{trendsData.store.current.percentChange(trendsData.industry.current).round(1)}} % more than industry</p>
                                    <p class="error" ng-if="trendsData.store.current.percentChange(trendsData.industry.current) < 0">{{-trendsData.store.current.percentChange(trendsData.industry.current).round(1)}} % less than industry</p>
                                    <p class="regular-grey bounceInText" ng-if="trendsData.industry.current != 'n/a'">
                                        Industry: {{trendsData.industry.current | number}}{{key == 'wifi_repeat_rate' ? '%' : ''}} {{key == 'users_time_spent' ? 'min' : ''}} {{key == 'wifi_data_usage' ? 'mb' : ''}}
                                        <span>/ </span>
                                        <span ng-if="trendsData.industry.change && trendsData.industry.change > 0">+</span>
                                        <span ng-if="trendsData.industry.change && trendsData.industry.change != 0 && trendsData.industry.change != 'n/a'" uib-tooltip-html=" lastdaterangehtml" tooltip-append-to-body="true">
                                            {{trendsData.industry.change + '%'}}
                                        </span>
                                        <span ng-if="!trendsData.industry.change && trendsData.industry.change == 0" uib-tooltip-html=" lastdaterangehtml" tooltip-append-to-body="true">
                                            0%
                                        </span>

                                        <span ng-if="trendsData.industry.change && trendsData.industry.change == 'n/a'" uib-tooltip-html=" lastdaterangehtml" tooltip-append-to-body="true">
                                            N/A
                                        </span>
                                    </p>
                                    <p class="regular-grey bounceInText" ng-if="trendsData.industry.current == 'n/a'">
                                        Industry: Data not available
                                    </p>
                                </div>
                                <div ng-if="!trendsData.industry.current">
                                    <img src="images/data-loader.gif" alt="Loading...." />
                                </div>
                            </div>
                        </div>
                    </label>
                </td>
            </tr>
        </table>
    </div>
    <footer class="widget-footer">
        <div class="form-group">
            <div class="checkbox">
                <label for="inlineCheckbox1" class="checkbox-inline">
                    <input type="checkbox" 
                           id="inlineCheckbox1" 
                           ng-model="wifiMetrics.get_industy_comparision" 
                           ng-true-value="'1'" 
                           ng-false-value="'0'" 
                           ng-change="getIndusrtyComparision()"
                           class="custom-click-element" 
                           title="Get Industry Comparisions" 
                           data-action="{{wifiMetrics.get_industy_comparision == 0 ? 'Display' : 'Hide' }} industry comparisions"> Get Industry Comparisions
                </label>
                <div class="pull-right info-italic">
                    Click on above metrics to see multiple trends below.
                </div>
            </div>
        </div>
    </footer>
</div>

<!--{{wifiMetrics.chartDataObject}}-->

<masonry class="row widget widget-card transparent-card" reload-on-resize reload-on-show preserve-order masonry-options="{ originTop: false }">
    <div class="graph-card masonry-brick" ng-repeat="(key, chartData) in wifiMetrics.chartDataObject">
        <div class="graph-card-content" ng-show="key=='wifi_users' && chartData.isVisible">
            <header class="graph-card-title">
                <div class="graph-formaters">
                    <div class="metric-name">Daily Wi-Fi Users</div>
                </div>
                <div class="graph-formaters right-aligned" data-ng-include="'trend-graph-formaters.html'"></div>
            </header>
            <div class="graph-container" data-ng-include="'trend-graph-container.html'"></div>
        </div>

        <div class="graph-card-content" ng-show="key == 'users_time_spent' && chartData.isVisible">
            <header class="graph-card-title">
                <div class="graph-formaters">
                    <div class="metric-name">DAILY TIME SPENT BY USERS</div>
                </div>
                <div class="graph-formaters right-aligned" data-ng-include="'trend-graph-formaters.html'"></div>
            </header>
            <div class="graph-container" data-ng-include="'trend-graph-container.html'"></div>
        </div>

        <div class="graph-card-content" ng-show="key == 'wifi_data_usage' && chartData.isVisible">
            <header class="graph-card-title">
                <div class="graph-formaters">
                    <div class="metric-name">AVG. DAILY DATA USED BY USER</div>
                </div>
                <div class="graph-formaters right-aligned" data-ng-include="'trend-graph-formaters.html'"></div>
            </header>
            <div class="graph-container" data-ng-include="'trend-graph-container.html'"></div>
        </div>


        <div class="graph-card-content" ng-show="key == 'wifi_repeat_rate' && chartData.isVisible">
            <header class="graph-card-title">
                <div class="graph-formaters">
                    <div class="metric-name">Repeat Rate</div>
                </div>
                <div class="graph-formaters right-aligned" data-ng-include="'trend-graph-formaters.html'"></div>
            </header>
            <div class="graph-container" data-ng-include="'trend-graph-container.html'"></div>
        </div>
    </div>
</masonry>

<div class="row widget widget-card">
    
    <header class="widget-title">What is your Wi-Fi user's profile?</header>
    <div class="col-md-3 graph-metric-stat">
        <div ng-if="wifiMetrics.genderDistribution" class="form-group graph-export" uib-dropdown>
            <a class="" data-toggle="dropdown" href="#" uib-dropdown-toggle>
                <i class="glyphicon glyphicon-download"></i>
            </a>
            <ul class="dropdown-menu-right" uib-dropdown-menu role="menu">
                <li role="menuitem">
                    <a href=""
                       class="custom-click-element"
                       title="Download WiFi-Demographics Donut Data"
                       data-action="Export csv for demographic gender distribution"
                       ng-click="donutDataTableToCSV(wifiMetrics.demographics.genderData,'genderData')">Export CSV</a>
                </li>
                <li class="divider"></li>
                <li role="menuitem">
                    <a href="" 
                       class="custom-click-element"
                       title="Download WiFi-Demographics Donut Data"
                       data-action="Save png image for demographic gender distribution"
                       ng-click="saveScreenshotAsImg('wifi_demographics_genderData', 'average', 'genderData')">Export PNG</a>
                </li>
            </ul>
        </div>
        <div id="wifi_demographics_genderData">
            <h6 class="metric-name">Gender Distribution</h6>
            <div id="wifi_donuts" class="circle metric-icon donuts-overlay">
                <img style="{{wifiMetrics.genderDistribution.data.rows ? '' : 'animation: pulsate 1s ease-out infinite;'}}" src="images/gender.svg" alt="Gender Distribution" style="margin-bottom:10px;" />
            </div>

            <div google-chart 
                 chart="wifiMetrics.genderDistribution"  
                     agc-on-ready="placeWifiDonutIcons(chartWrapper)"
                 style="height:200px; width:100%; margin-top:-40px;"></div>
        </div>
    </div>
    <div class="col-md-3 graph-metric-stat">
        <div ng-if="wifiMetrics.ageDistribution" class="form-group graph-export" uib-dropdown>
            <a class="" data-toggle="dropdown" href="#" uib-dropdown-toggle>
                <i class="glyphicon glyphicon-download"></i>
            </a>
            <ul class="dropdown-menu-right" uib-dropdown-menu role="menu">
                <li role="menuitem">
                    <a href=""
                       class="custom-click-element"
                       title="Download WiFi-Demographics Donut Data"
                       data-action="Export csv for demographic age distribution"
                       ng-click="donutDataTableToCSV(wifiMetrics.demographics.ageData,'ageData')">Export CSV</a>
                </li>
                <li class="divider"></li>
                <li role="menuitem">
                    <a href="" 
                       class="custom-click-element"
                       title="Download WiFi-Demographics Donut Data"
                       data-action="Save png image for demographic age distribution"
                       ng-click="saveScreenshotAsImg('wifi_demographics_ageDistribution', 'average', 'ageData')">Export PNG</a>
                </li>
            </ul>
        </div>
        <div id="wifi_demographics_ageDistribution">
            <h6 class="metric-name">Age Group <span>(years)</span></h6>
            <div id="wifi_donuts" class="circle metric-icon donuts-overlay">
                <img style="{{wifiMetrics.ageDistribution.data.rows? '' : 'animation: pulsate 1s ease-out infinite;'}}" src="images/birthday.svg" alt="Age Group" />
            </div>

            <div google-chart 
                 chart="wifiMetrics.ageDistribution" 
                     agc-on-ready="placeWifiDonutIcons(chartWrapper)"
                  style="height:200px; width:100%; margin-top:-40px;"></div>
        </div>
    </div>
    <div class="col-md-3 graph-metric-stat">
        <div ng-if="wifiMetrics.spendDistribution" class="form-group graph-export" uib-dropdown>
            <a class="" data-toggle="dropdown" href="#" uib-dropdown-toggle>
                <i class="glyphicon glyphicon-download"></i>
            </a>
            <ul class="dropdown-menu-right" uib-dropdown-menu role="menu">
                <li role="menuitem">
                    <a href=""
                       class="custom-click-element"
                       title="Download WiFi-Demographics Donut Data"
                       data-action="Export csv for demographic spend capacity distribution"
                       ng-click="donutDataTableToCSV(wifiMetrics.demographics.spendCapacityData,'spendCapacityData')">Export CSV</a>
                </li>
                <li class="divider"></li>
                <li role="menuitem">
                    <a href="" 
                       class="custom-click-element"
                       title="Download WiFi-Demographics Donut Data"
                       data-action="Save png image for demographic spend capacity distribution"
                       ng-click="saveScreenshotAsImg('wifi_demographics_spendCapacityData', 'average', 'spendCapacityData')">Export PNG</a>
                </li>
            </ul>
        </div>
        <div id="wifi_demographics_spendCapacityData">
            <h6 class="metric-name">Spend Capacity</h6>
            <div id="wifi_donuts" class="circle metric-icon donuts-overlay" >
                <img style="{{wifiMetrics.spendDistribution.data.rows? '' : 'animation: pulsate 1s ease-out infinite;'}}" src="images/money.svg" alt="Spend Capacity" />
            </div>

            <div google-chart 
                 chart="wifiMetrics.spendDistribution" 
                     agc-on-ready="placeWifiDonutIcons(chartWrapper)"
                  style="height:200px; width:100%; margin-top:-40px;"></div>
        </div>
    </div>
    <div class="col-md-3 graph-metric-stat">
        <div ng-if="wifiMetrics.customerDistribution" class="form-group graph-export" uib-dropdown>
            <a class="" data-toggle="dropdown" href="#" uib-dropdown-toggle>
                <i class="glyphicon glyphicon-download"></i>
            </a>
            <ul class="dropdown-menu-right" uib-dropdown-menu role="menu">
                <li role="menuitem">
                    <a href=""
                       class="custom-click-element"
                       title="Download WiFi-Demographics Donut Data"
                       data-action="Export csv for demographic customer loyalty distribution"
                       ng-click="donutDataTableToCSV(wifiMetrics.demographics.loyaltySegment,'loyaltySegment')">Export CSV</a>
                </li>
                <li class="divider"></li>
                <li role="menuitem">
                    <a href="" 
                       class="custom-click-element"
                       title="Download WiFi-Demographics Donut Data"
                       data-action="Save png image for demographic customer loyalty distribution"
                       ng-click="saveScreenshotAsImg('wifi_demographics_loyaltySegment', 'average', 'loyaltySegment')">Export PNG</a>
                </li>
            </ul>
        </div>
        <div id="wifi_demographics_loyaltySegment">
            <h6 class="metric-name">Customer Base</h6>

            <div id="wifi_donuts" class="circle metric-icon donuts-overlay">
                <img style="{{wifiMetrics.customerDistribution.data.rows? '' : 'animation: pulsate 1s ease-out infinite;'}}" src="images/money.svg" alt="Spend Capacity" />
            </div>

            <div google-chart 
                 chart="wifiMetrics.customerDistribution" 
                     agc-on-ready="placeWifiDonutIcons(chartWrapper)"
                  style="height:200px; width:100%; margin-top:-40px;"></div>
        </div>
    </div>
</div>

<div class="row widget widget-card">
    <header class="widget-title">What type of mobile handsets your Wi-Fi users use? </header>
    <div class="pscyco-graphics">
        <div class="row no-margin" ng-if="!wifiMetrics.psychoGraphics.data">
            <h1 class="loading-message" data-text="Please wait while we are loading your data…">Please wait while we are loading your data…</h1>
        </div>
        <div ng-if="wifiMetrics.psychoGraphics.data" class="form-group graph-export" uib-dropdown style="position: absolute;display: inline-block;right: 20px;z-index: 1;">
            <a class="" data-toggle="dropdown" href="#" uib-dropdown-toggle>
                <i class="glyphicon glyphicon-download"></i>
            </a>
            <ul class="dropdown-menu-right" uib-dropdown-menu role="menu">
                <li role="menuitem">
                    <a href="" 
                       class="custom-click-element"
                       title="Download WiFi-Psychographics Grapg Data"
                       data-action="Export csv for psychographic graph data"
                       ng-click="googleDataTableToCSV(wifiMetrics.psychoGraphics,'psychoGraphics')">Export CSV</a>
                </li>
                <li class="divider"></li>
                <li role="menuitem">
                    <a href="" 
                       class="custom-click-element"
                       title="Download WiFi-Psychographics Grapg Data"
                       data-action="Save png image for psychographic graph data"
                       ng-click="saveAsImg('wifi_psychoGraphics_chart_container', 'average', 'psychoGraphics')">Export PNG</a>
                </li>
            </ul>
        </div>
        <div id="wifi_psychoGraphics_chart_container"
             ng-if="wifiMetrics.psychoGraphics.data"
             google-chart chart="wifiMetrics.psychoGraphics" style="height:150px; width:100%;"></div>
        
    </div>
</div>

<!--{{wifiMetrics.crossStoreComparison[selectAvgWiFiCscDataType]}}-->

<div class="row widget widget-card">
    <header class="widget-title">
        <select ng-model="selectAvgWiFiCscDataType" 
                ng-change="getAverageChartDataForWifiCsc()"  
                class="custom-click-element"
                title="Select for changing cross store wiFi bar grapg data"
                data-action="chaged to cross store wiFi bar grapg data for {{selectAvgWiFiCscDataType}}"
                style="width: 30rem;">
            <option value="wifi_users">Daily avg wifi users</option>
            <option value="daily_time_spent">Daily time spent</option>
            <option value="daily_data_used">Daily data used</option>
            <option value="repeat_rate">Repeat rate</option>
        </select>
        <div ng-if="wifiMetrics.crossStoreComparison[selectAvgWiFiCscDataType].data.rows.length > 0" class="form-group graph-export" uib-dropdown style="max-width: 4rem;float: right;right: 20px;">
            <a class="" data-toggle="dropdown" href="#" uib-dropdown-toggle>
                <i class="glyphicon glyphicon-download"></i>
            </a>
            <ul class="dropdown-menu-right" uib-dropdown-menu role="menu">
                <li role="menuitem">
                    <a href=""
                       class="custom-click-element"
                       title="Download Cross Store WiFi Grapg Data"
                       data-action="Export csv data for {{selectAvgWiFiCscDataType}} cross store wifi graph data"
                       ng-click="googleDataTableToCSV(wifiMetrics.crossStoreComparison[selectAvgWiFiCscDataType],selectAvgWiFiCscDataType)">Export CSV</a>
                </li>
                <li class="divider"></li>
                <li role="menuitem">
                    <a href="" 
                       class="custom-click-element"
                       title="Download Cross Store WiFi Grapg Data"
                       data-action="Save png image for {{selectAvgWiFiCscDataType}} cross store wifi graph data" 
                       ng-click="saveScreenshotAsImg('wifi_crossStore_distributions_chart_container', 'avergae', selectAvgWiFiCscDataType)">Export PNG</a>
                </li>
            </ul>
        </div>
    </header>
    <div class="graph-container cross-store">
        <div class="row" id="wifi_crossStore_distributions_chart_container" style="padding: 3rem;">
            <div ng-if="wifiMetrics.crossStoreComparison[selectAvgWiFiCscDataType].data.rows.length > 0" google-chart chart="wifiMetrics.crossStoreComparison[selectAvgWiFiCscDataType]" style="min-height:50rem"></div>
            <div ng-if="wifiMetrics.crossStoreComparison[selectAvgWiFiCscDataType].data.rows.length == 0">
                <h5 class="metric-notavailable text-center">Data not available</h5>
            </div>
            <div class="row no-margin" ng-if="!wifiMetrics.crossStoreComparison[selectAvgWiFiCscDataType].data">
                <h1 class="loading-message" data-text="Please wait while we are loading your data…">Please wait while we are loading your data…</h1>
            </div>
        </div>
        <div class="container-fluid button-container text-center-align">
            <button ng-if="viewMoreAveragesFlag" 
                    class="btn btn-primary-blue custom-click-element"
                    title="View More Button"
                    data-action="View more store's cross store wifi graph data for {{selectAvgWiFiCscDataType}}"
                    ng-click="viewMoreAverages(selectAvgWiFiCscDataType)">View More Stores</button>
            <button ng-if="viewLessAveragesFlag" 
                    class="btn btn-primary-blue custom-click-element"
                    title="View Less Button"
                    data-action="View Less store's cross store wifi graph data for {{selectAvgWiFiCscDataType}}"
                    ng-click="viewLessAverages(selectAvgWiFiCscDataType)">View Less Stores</button>
        </div>
    </div>
</div>

<script type="text/ng-template" id="trend-graph-formaters.html">
    <form class="form-inline ng-pristine ng-valid" role="form">
        <div class="form-group">
            <div class="radio">
                <label for="wifi_users_hourly" class="radio-inline">
                    Hourly <input id="wifi_users_hourly"
                                  type="radio"
                                  ng-model="wifiMetrics.chartDataObject[key].tsType"
                                  value="hourly"
                                  ng-change="creatWifiMetricGraph(key)"
                                  name="wifi_users_hourly"
                                  class="custom-click-element"
                                  title="Type of trends"
                                  data-action="Changed {{key}} trend graph view to hourly">
                </label>
            </div>
        </div>
        <div class="form-group">
            <div class="radio">
                <label for="wifi_users_daily" class="radio-inline">
                    Daily <input id="wifi_users_daily"
                                 type="radio"
                                 ng-model="wifiMetrics.chartDataObject[key].tsType"
                                 value="daily"
                                 ng-change="creatWifiMetricGraph(key)"
                                 name="wifi_users_daily"
                                 class="custom-click-element"
                                 title="Type of trends"
                                 data-action="Changed {{key}} trend graph view to daily">
                </label>
            </div>
        </div>
        <div class="form-group">
            <div class="radio">
                <label for="wifi_users_weekly" class="radio-inline">
                    Weekly <input id="wifi_users_weekly" type="radio"
                                  ng-model="wifiMetrics.chartDataObject[key].tsType"
                                  value="weekly" ng-change="creatWifiMetricGraph(key)" name="wifi_users_weekly"
                                  class="custom-click-element"
                                  title="Type of trends"
                                  data-action="Changed {{key}} trend graph view to weekly">
                </label>
            </div>
        </div>
        <div class="form-group">
            <div class="radio">
                <label for="wifi_users_monthly" class="radio-inline">
                    Monthly <input id="wifi_users_monthly" type="radio"
                                   ng-model="wifiMetrics.chartDataObject[key].tsType"
                                   value="monthly" ng-change="creatWifiMetricGraph(key)" name="wifi_users_monthly"
                                   class="custom-click-element"
                                   title="Type of trends"
                                   data-action="Changed {{key}} trend graph view to monthly">
                </label>
            </div>
        </div>
        <div ng-if="wifiMetrics.chartDataObject[key].data.rows.length > 0" class="form-group graph-export" uib-dropdown>
            <a class="" data-toggle="dropdown" href="#" uib-dropdown-toggle>
                <i class="glyphicon glyphicon-download"></i>
            </a>
            <ul class="dropdown-menu-right" uib-dropdown-menu role="menu">
                <li role="menuitem">
                    <a href=""
                       ng-click="googleDataTableToCSV(wifiMetrics.chartDataObject[key],key)"
                       class="custom-click-element"
                       title="Download WiFi-Metrics Graph Data"
                       data-action="Export csv for {{key}} trend graph">Export CSV</a>
                </li>
                <li class="divider"></li>
                <li role="menuitem">
                    <a class="custom-click-element"
                       title="Download WiFi-Metrics Graph Data"
                       data-action="Save png image for {{key}} trend graph"
                       href=""
                       ng-click="saveAsImg(key + '_chart_container', wifiMetrics.chartDataObject[key].tsType, key)">Export PNG</a>
                </li>
            </ul>
        </div>
    </form>
</script>

<script type="text/ng-template" id="trend-graph-container.html">
    <div id="{{key}}_chart_container"
         ng-if="wifiMetrics.chartDataObject[key].data.rows.length > 0"
         google-chart chart="wifiMetrics.chartDataObject[key]"
         agc-reload-on-wrapper-resize="true"
         agc-on-select="hideWifMrtricSeries(selectedItem,key)" style="height:250px;">
    </div>

    <div ng-if="wifiMetrics.chartDataObject[key].tsType =='daily' && wifiMetrics.chartDataObject[key].data.rows.length > 0 " id="point_legend" style="text-align:center;">
        <div style="display:inline-block; margin:0px 5px; font-size:14px; color:#222222;">
            <div style="width:10px; height:10px; border-radius:50%; background-color:#71c044; display:inline-block;"></div>
            Weekend
        </div>
        <div style="display:inline-block; margin:0px 5px; font-size:14px; color:#222222;">
            <div style="width:10px; height:10px; border-radius:50%; background-color:#ffd503; display:inline-block;"></div>
            Holiday
        </div>
    </div>

    <div ng-if="wifiMetrics.chartDataObject[key].data.rows.length == 0" style="height:250px;">
        <h5 class="metric-notavailable text-center">Data not available</h5>
    </div>
</script>

<style>
    .widget .circle-labellist li {
        font-size: 1rem;
        font-weight: 400;
        color: #929497;
        text-transform: capitalize;
    }

    .list-inline > li {
        display: inline-block;
        padding-left: 2px;
        padding-right: 2px;
    }

    .widget .graph-metric-stat .graph-export.graph-export {
        margin-top: 0;
    }
</style>
