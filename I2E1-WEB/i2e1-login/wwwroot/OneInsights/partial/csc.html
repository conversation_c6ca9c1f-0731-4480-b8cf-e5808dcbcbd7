<div class="row info-section">
    <div class="pull-right" id="dropdown-hidden-container">
        <div class="btn-group" uib-dropdown auto-close="disabled">
            <button id="metric-info-btn" type="button" class="btn btn-primary btn-primary-green" uib-dropdown-toggle>
                Understand your metrics <i class="fa fa-angle-down" aria-hidden="true"></i><i class="fa fa-angle-up" aria-hidden="true"></i>
            </button>
            <ul class="dropdown-menu dropdown-menu-right" uib-dropdown-menu role="menu" aria-labelledby="btn-append-to">
                <li class="desc-section" role="menuitem">
                    <h6>Metric Description</h6>
                    <ul class="list-group">
                        <li class="list-group-item">
                            <span class="section-heading">Daily vicinity footfall:</span> Count of people detected in and around your store daily
                        </li>
                        <li class="list-group-item">
                            <span class="section-heading">Daily store footfall:</span> Count of people who stepped in your store – your customers
                        </li>
                        <li class="list-group-item">
                            <span class="section-heading">Walk In rate:</span> Percentage of people in vicinity who stepped in your store
                        </li>
                        <li class="list-group-item">
                            <span class="section-heading">Avg. dwell time:</span> Average time your customer spends in your store
                        </li>
                        <li class="list-group-item">
                            <span class="section-heading">Repeat rate:</span> Ratio of customers who have visited you again in the past 30 days
                        </li>
                    </ul>
                </li>
            </ul>
        </div>
    </div>
</div>
<!--{{crossStore.averages[selectAvgDataType]}}-->
<div class="row" style="margin:0;"  ng-show="crossStoreAvgDataForTable.data.rows.length > 0">
    <div class="btn-group pull-right" role="group" ng-init="selected = 'bar_chart'" >
        <button class="btn btn-default" ng-click="selected = 'bar_chart'"><i class="glyphicon glyphicon-stats"></i> Bar Chart</button>
        <button class="btn btn-default" ng-click="selected = 'table_view'"><i class="glyphicon glyphicon-list"></i> Tabular View</button>
    </div>
</div>

<!--{{crossStore.averages.repeat_rate}}-->
<div class="row widget widget-card" ng-show="selected == 'bar_chart'">
    <header class="widget-title">
        <select ng-model="selectAvgDataType" ng-change="getAverageChartDataForCscs()" style="width: 30rem;">
            <option value="vicinity_footfall">daily vicinity footfall</option>
            <option value="instore_footfall">daily store footfall</option>
            <option value="walk_in_rate">walk in rate</option>
            <option value="avg_dwell_time">AVG. dwell time</option>
            <option value="repeat_rate">Repeat rate</option>
        </select>
        <div ng-if="crossStore.averages[selectAvgDataType].data.rows.length > 0" class="form-group graph-export" uib-dropdown style="max-width: 4rem;float: right;margin: 0;">
            <a class="" data-toggle="dropdown" href="#" uib-dropdown-toggle>
                <i class="glyphicon glyphicon-download"></i>
            </a>
            <ul class="dropdown-menu-right" uib-dropdown-menu role="menu">
                <li role="menuitem"><a href="" ng-click="googleDataTableToCSV(crossStore.averages[selectAvgDataType],selectAvgDataType)">Export CSV</a></li>
                <li class="divider"></li>
                <li role="menuitem"><a href="" ng-click="saveAsImg('selectAvgDataType_chart_container', 'avergae', selectAvgDataType)">Export PNG</a></li>
            </ul>
        </div>
    </header>
    <div class="graph-container cross-store">
        <div class="row no-margin" ng-if="crossStore.CSCchartLoading">
            <h1 class="loading-message" data-text="Please wait while we are loading your data…">Please wait while we are loading your data…</h1>
        </div>
        <div ng-if="crossStore.averages[selectAvgDataType].data.rows.length > 0"
             id="selectAvgDataType_chart_container"
             google-chart chart="crossStore.averages[selectAvgDataType]"
             style="min-height:50rem;"></div>
        <div ng-if="crossStore.averages[selectAvgDataType].data.rows.length == 0">
            <h5 class="metric-notavailable text-center">Data not available</h5>
        </div>

        <div class="container-fluid button-container text-center-align">
            <button ng-if="viewMoreAveragesFlag" class="btn btn-primary-blue" ng-click="viewMoreAverages(selectAvgDataType)">View More Stores</button>
            <button ng-if="viewLessAveragesFlag" class="btn btn-primary-blue" ng-click="viewLessAverages(selectAvgDataType)">View Less Stores</button>
        </div>
    </div>
</div>

<div class="row widget widget-card" ng-show="selected == 'table_view'" id="css_avg_key_metrics_graph_contanier">
    <header class="widget-title" style="display: -webkit-box;">
        <div class="col-md-10 csc-avg-widget-title">
            <span style="margin-bottom: 11px;display: inline-block;">Store Ranking Based on Brand Averages Across Key Metrics</span>
            <div class="row" style="margin: 0;">
                <div class="legend-avg-css pull-left" style="font-size: 1.4rem;font-weight: 400;color: #929497;font-style: italic;">
                    Values > Brand Average
                </div>
                <div class="legend-avg-css pull-right" style="font-size: 1.4rem;font-weight: 400;color: #929497;font-style: italic;">
                    Values < Brand Average
                </div>
            </div>
            <div class="row" style="margin: 0;">
                <div class="gradient-bar"></div>
            </div>
        </div>
        <div class="col-md-2">
            <div ng-if="crossStoreAvgDataForTable.data.rows.length > 0" class="form-group graph-export" uib-dropdown style="max-width: 4rem;float: right;margin: 0;">
                <a class="" data-toggle="dropdown" href="#" uib-dropdown-toggle>
                    <i class="glyphicon glyphicon-download"></i>
                </a>
                <ul class="dropdown-menu-right" uib-dropdown-menu role="menu">
                    <li role="menuitem">
                        <a href=""
                           class="custom-click-element"
                           title="Download store ranking based on brand averages across key metrics"
                           data-action="Export csv for store ranking based on brand averages across key metrics"
                           ng-click="googleDataTableToCSV(crossStoreAvgDataForTable,'cross_store_avg_data')">Export CSV</a>
                    </li>
                    <li class="divider"></li>
                    <li role="menuitem">
                        <a href=""
                           class="custom-click-element"
                           title="Download store ranking based on brand averages across key metrics"
                           data-action="Save png image for store ranking based on brand averages across key metrics"
                           ng-click="saveScreenshotAsImg('css_avg_key_metrics_graph_contanier', 'avergae', 'cross_store_avg_data')">Export PNG</a>
                    </li>
                </ul>
            </div>
        </div>
    </header>
    <div class="graph-container cross-store">
        <div ng-if="crossStoreAvgDataForTable.data.rows.length > 0"
             id="selectAvgDataType_chart_container"
             google-chart chart="crossStoreAvgDataForTable" agc-on-ready="formateBackGround(chartWrapper)" style="min-height:50rem"></div>
        <div class="row no-margin" ng-if="crossStoreAvgDataForTable.CSCchartLoading">
            <h1 class="loading-message" data-text="Please wait while we are loading your data…">Please wait while we are loading your data…</h1>
        </div>
    </div>
</div>

<!-{{crossStore.distributions}}-->
<div class="row widget widget-card">
    <header class="widget-title">
        <select ng-model="selectDistributionDataType"
                ng-change="getDistributionsChartDataForCscs()"
                class="custom-click-element"
                title="Select for changing cross store metric distribution bar grapg data"
                data-action="chaged to cross store metric distribution bar grapg data for {{selectDistributionDataType}}"
                style="width: 30rem;">
            <option value="visit_distribution">Visit Distribution</option>
            <option value="dwell_time_distribution">Dwell Time Distribution</option>
            <option value="age_distribution">Age Distribution</option>
            <option value="gender_distribution">Gender Distribution</option>
            <option value="spend_capacity_distribution">Spend Capacity Distribution</option>
        </select>
        <!--
        <div ng-if="crossStore.distributions[selectDistributionDataType].store.data.rows.length > 0" class="btn-toolbar right-header" role="toolbar">
            <div class="btn-group mr-2">
                <h3>Export</h3>
            </div>
            <div class="btn-group mr-2">
                <button type="button" class="btn btn-default" ng-click="googleDataTableToCSV(crossStore.distributions[selectDistributionDataType].store,selectDistributionDataType)"><i class="fa fa-file-excel-o" aria-hidden="true"></i>CSV</button>
            </div>
            <div class="btn-group">
                <button type="button" class="btn btn-default" ng-click="saveScreenshotAsImg('crossStore_distributions_chart_container', 'avergae', selectDistributionDataType)"><i class="fa fa-file-picture-o" aria-hidden="true"></i>PNG</button>
            </div>
        </div>-->
        <div ng-if="crossStore.distributions[selectDistributionDataType].store.data.rows.length > 0" class="form-group graph-export" uib-dropdown style="max-width: 4rem;float: right;margin: 0;">
            <a class="" data-toggle="dropdown" href="#" uib-dropdown-toggle>
                <i class="glyphicon glyphicon-download"></i>
            </a>
            <ul class="dropdown-menu-right" uib-dropdown-menu role="menu">
                <li role="menuitem">
                    <a href=""
                       class="custom-click-element"
                       title="Download cross store distributions graph data"
                       data-action="Export csv for cross store distributions graph data for {{selectDistributionDataType}}"
                       ng-click="googleDataTableToCSV(crossStore.distributions[selectDistributionDataType].store,selectDistributionDataType)">Export CSV</a>
                </li>
                <li class="divider"></li>
                <li role="menuitem">
                    <a href=""
                       class="custom-click-element"
                       title="Download cross store distributions graph data"
                       data-action="Save png image for cross store distributions graph data for {{selectDistributionDataType}}"
                       ng-click="saveScreenshotAsImg('crossStore_distributions_chart_container', 'avergae', selectDistributionDataType)">Export PNG</a>
                </li>
            </ul>
        </div>
    </header>
    <div class="graph-container cross-store">
        <div class="row" id="crossStore_distributions_chart_container" style="padding: 3rem;">
            <div class="row no-margin" ng-if="crossStore.CSCchartLoading">
                <h1 class="loading-message" data-text="Please wait while we are loading your data…">Please wait while we are loading your data…</h1>
            </div>

            <div ng-if="crossStore.distributions[selectDistributionDataType].industry.data.rows.length > 0"
                 google-chart chart="crossStore.distributions[selectDistributionDataType].industry"
                 style="height:85px; width:100%;"></div>
            <div ng-if="crossStore.distributions[selectDistributionDataType].brand.data.rows.length > 0"
                 google-chart chart="crossStore.distributions[selectDistributionDataType].brand"
                 style="height:85px; width:100%;"></div>
            <div ng-if="crossStore.distributions[selectDistributionDataType].store.data.rows.length > 0"
                 google-chart chart="crossStore.distributions[selectDistributionDataType].store"
                 style="width:100%;"></div>

            <div ng-if="!crossStore.CSCchartLoading && crossStore.distributions[selectDistributionDataType].industry.data.rows.length == 0 && crossStore.distributions[selectDistributionDataType].brand.data.rows.length == 0 && crossStore.distributions[selectDistributionDataType].store.data.rows.length == 0" style="min-height:50rem">
                <h5 class="metric-notavailable text-center">Data not available</h5>
            </div>
        </div>

        <div class="container-fluid button-container text-center-align">
            <button ng-if="viewMoreDistributionsFlag"
                    class="btn btn-primary-blue custom-click-element"
                    title="View More Button"
                    data-action="View more store's cross store distributions for {{selectDistributionDataType}}"
                    ng-click="viewMoreDistributions(selectDistributionDataType)">
                View More Stores
            </button>
            <button ng-if="viewLessDistributionsFlag"
                    class="btn btn-primary-blue custom-click-element"
                    title="View Less Button"
                    data-action="View Less store's cross store distributions for {{selectDistributionDataType}}"
                    ng-click="viewLessDistributions(selectDistributionDataType)">
                View Less Stores
            </button>
        </div>
    </div>
</div>
