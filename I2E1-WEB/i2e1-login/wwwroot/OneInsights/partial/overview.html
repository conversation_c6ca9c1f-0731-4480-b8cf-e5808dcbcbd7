<div class="row info-section">
    <div class="pull-right" id="dropdown-hidden-container">
        <div class="btn-group" uib-dropdown auto-close="disabled">
            <button id="metric-info-btn"
                    type="button"
                    class="btn btn-primary btn-primary-green"
                    uib-dropdown-toggle>
                Understand your metrics
                <i class="fa fa-angle-down" aria-hidden="true"></i><i class="fa fa-angle-up" aria-hidden="true"></i>
            </button>
            <ul class="dropdown-menu dropdown-menu-right"
                uib-dropdown-menu role="menu"
                aria-labelledby="btn-append-to">
                <li class="desc-section" role="menuitem">
                    <h6>Reading the metrics</h6>
                    <ul class="list-group">
                        <li class="list-group-item">
                            <div ng-if="locationsInsight.avg_daily_vicinity_footfall">
                                <h5 class="metric-stat bounceInText">
                                    {{locationsInsight.avg_daily_vicinity_footfall }}
                                    <sup ng-if="locationsInsight.avg_daily_vicinity_footfall_change" class="{{locationsInsight.avg_daily_vicinity_footfall_change > 0 ? 'increment' : 'decrement'}}">
                                        <span ng-if="locationsInsight.avg_daily_vicinity_footfall_change > 0">+</span>
                                        {{locationsInsight.avg_daily_vicinity_footfall_change | round:1}}%
                                    </sup>
                                </h5>
                            </div>
                            Your current metric/ Vs previous period
                        </li>
                        <li class="list-group-item">
                            <div ng-if="locationsInsight.avg_daily_vicinity_footfall">
                                <h5 class="regular-grey bounceInText">
                                    Industry: {{locationsInsight.avg_industry_daily_vicinity_footfall | number }} /
                                    <span ng-if="locationsInsight.avg_industry_daily_vicinity_footfall_change > 0">+</span>
                                    {{locationsInsight.avg_industry_daily_vicinity_footfall_change }}%
                                </h5>
                            </div>
                            Industry current metric/ Vs. previous period
                        </li>
                    </ul>
                </li>
                <li class="desc-section" role="menuitem">
                    <h6>Metric Description</h6>
                    <b>Data captured:</b>
                    <ul class="list-group">
                        <li class="list-group-item">
                            <span class="section-heading">Customers detected: </span>Count of unique visitors detected in your store
                        </li>
                        <li class="list-group-item">
                            <span class="section-heading">
                                Phone numbers captured:
                            </span> Count of unique customers who logged on to your Wi-Fi with
                            phone numbers
                        </li>
                        <li class="list-group-item">
                            <span class="section-heading">
                                Email IDs collected:
                            </span> Count of unique customers who logged on to your Wi-Fi and provided
                            an email ID at login
                        </li>
                    </ul>
                    <!--<b>Data captured and campaign results:</b>
                        <ul class="list-group">
                            <li class="list-group-item">
                                Phone numbers captured: Count of unique customers who logged on to your Wi-Fi with
                                their phone numbers
                            </li>
                            <li class="list-group-item">
                                Customers targeted: Count of customers targeted via i2e1 promotion campaigns
                            </li>
                            <li class="list-group-item">
                                Conversion rates: Count of customers who walked in to your store post promotion campaigns
                            </li>
                            <li class="list-group-item">
                                Revenue earned: Estimated billings amount from customers who walked in to your store
                                post promotion campaigns
                            </li>
                        </ul>-->
                    <b>Know your customers</b>
                    <ul class="list-group">
                        <li class="list-group-item">
                            <span class="section-heading">Gender distribution:</span> Distribution of your customers across gender bands
                        </li>
                        <li class="list-group-item">
                            <span class="section-heading">Age groups:</span> Distribution of your customers across age bands
                        </li>
                        <li class="list-group-item">
                            <span class="section-heading">
                                Spend capacity:
                            </span> Distribution of your customers across their spend potential
                            <ul>
                                <li>
                                    Low – Customers who are value conscious and are low spenders.
                                    The disposable income is less than 15K per month.
                                </li>
                                <li>
                                    Medium – Customers who hold the potential to spend more but are budget conscious.
                                    The disposable income is around 15K-30K per month
                                </li>
                                <li>
                                    High – Premium customers who always tend to show a high spending pattern.
                                    The disposable income is more than 30K per month.
                                </li>
                            </ul>
                        </li>
                    </ul>
                    <b>Know your locations</b>
                    <ul class="list-group">
                        <li class="list-group-item">
                            <span class="section-heading">Daily vicinity footfall:</span> Count of people detected in and around your store daily
                        </li>
                        <li class="list-group-item">
                            <span class="section-heading">Daily store footfall:</span> Count of people who stepped in your store – your customers
                        </li>
                        <li class="list-group-item">
                            <span class="section-heading">Walk in rate:</span> Percentage of people in vicinity who stepped in your store
                        </li>
                    </ul>
                </li>
            </ul>
        </div>
        <!--<div class="btn-group" uib-dropdown><button id="notes-btn" type="button" class="btn btn-primary btn-primary-green" uib-dropdown-toggle>
            Notes <i class="fa fa-angle-down" aria-hidden="true"></i></button><ul class="dropdown-menu dropdown-menu-right" uib-dropdown-menu role="menu" aria-labelledby="btn-append-to-to-body"><li role="menuitem"><a href="#">Action</a></li><li role="menuitem"><a href="#">Another action</a></li><li role="menuitem"><a href="#">Something else here</a></li><li class="divider"></li><li role="menuitem"><a href="#">Separated link</a></li></ul></div>-->
    </div>
</div>
<!--<show-benfits-data-captured post-data="$parent.data_captured"></show-benfits-data-captured>-->


<!--{{locationsInsight.chartDataObject}}-->

<div class="row no-margin" ng-if="!locationsInsight.chartDataObject[chartKey.LchartMetrics] && !locationsInsight.chartDataObject[chartKey.RchartMetrics]">
    <h1 class="loading-message" data-text="Please wait while we are loading your data…">Please wait while we are loading your data…</h1>
</div>

<!--{{chartKey.LchartMetrics}}
{{chartKey.RchartMetrics}}
{{locationsInsight.chartDataObject}}-->
<div class="row widget widget-card" style="margin-bottom:0;">
    <header class="widget-title" style="position:relative;">
        How do your stores perform over time?
        <span class="pull-right" style="position: relative;
                padding-right:5px;
                bottom: -15px;
                left: 1.5rem;
                font-size: 1.4rem;
                font-weight: 400;
                color: #929497;
                font-style: italic;">Click on chart legends to toggle between store, brand and industry view.</span>
    </header>
    <div class="col-md-6 graph-metric-location-stat" ng-if="locationsInsight.chartDataObject[chartKey.LchartMetrics]" style="border-left : 0.1rem solid rgba(204, 204, 204, 0.5)">
        <div class="graph-formaters">
            <form class="form-inline" role="form">
                <div class="form-group pull-left">
                    <select class="form-control i2e1-select" id="metrics" ng-model="chartKey.LchartMetrics" ng-change="chartsMetricsSelectionChange('left_chart')">
                        <option value="vicinity_footfall" ng-disabled ="(chartKey.RchartMetrics=='vicinity_footfall')? true: false">Daily Vicinity Footfall</option>
                        <option value="instore_footfall" ng-disabled ="(chartKey.RchartMetrics=='instore_footfall')? true: false">Daily Store Footfall</option>
                        <option value="conversion_rate" ng-disabled ="(chartKey.RchartMetrics=='conversion_rate')? true: false">Walk In Rate</option>
                        <option value="dwell_time" ng-disabled ="(chartKey.RchartMetrics=='dwell_time')? true: false">Dwell Time (minutes)</option>
                    </select>
                </div>
                <div class="form-group" style="padding-left:3px;">
                    <div class="radio">
                        <label for="hourly" class="radio-inline">
                            <input style="margin-right:0;" id="hourly" type="radio" ng-model="locationsInsight.chartDataObject[chartKey.LchartMetrics].tsType"
                                   value="hourly" ng-change="chartsMetricsSelectionChange('left_chart')" /> Hourly
                        </label>
                    </div>
                </div>

                <div class="form-group" style="padding-left:3px;">
                    <div class="radio">
                        <label for="daily" class="radio-inline">
                            <input style="margin-right:0;" id="daily" type="radio" ng-model="locationsInsight.chartDataObject[chartKey.LchartMetrics].tsType" value="daily"
                                   ng-change="chartsMetricsSelectionChange('left_chart')" /> Daily
                        </label>
                    </div>
                </div>
                <div class="form-group" style="padding-left:3px;">
                    <div class="radio">
                        <label for="daily" class="radio-inline">
                            <input style="margin-right:0;" id="daily" type="radio" ng-model="locationsInsight.chartDataObject[chartKey.LchartMetrics].tsType" value="weekly"
                                   ng-change="chartsMetricsSelectionChange('left_chart')" /> Weekly
                        </label>
                    </div>
                </div>
                <div class="form-group" style="padding-left:3px;">
                    <div class="radio">
                        <label for="monthly" class="radio-inline">
                            <input style="margin-right:0;" id="monthly" type="radio" ng-model="locationsInsight.chartDataObject[chartKey.LchartMetrics].tsType"
                                   value="monthly" ng-change="chartsMetricsSelectionChange('left_chart')" /> Monthly
                        </label>
                    </div>
                </div>
                <div ng-if="locationsInsight.chartDataObject[chartKey.LchartMetrics].data.rows" class="form-group graph-export" uib-dropdown>
                    <a class="" data-toggle="dropdown" href="#" uib-dropdown-toggle>
                        <i class="glyphicon glyphicon-download"></i>
                    </a>
                    <ul class="dropdown-menu-right" uib-dropdown-menu role="menu">
                        <li role="menuitem"><a href="" ng-click="googleDataTableToCSV(locationsInsight.chartDataObject[chartKey.LchartMetrics],chartKey.LchartMetrics)">Export CSV</a></li>
                        <li class="divider"></li>
                        <li role="menuitem"><a href="" ng-click="saveAsImg('chartMetrics_chart_container', locationsInsight.chartDataObject[chartKey.LchartMetrics].tsType, chartKey.LchartMetrics)">Export PNG</a></li>
                    </ul>
                </div>
            </form>
        </div>
        <div ng-if="locationsInsight.chartDataObject[chartKey.LchartMetrics].data.rows" class="graph-container" style="margin: 3rem -3.5rem 0;">
            <div id="chartMetrics_chart_container"
                 ng-if="locationsInsight.chartDataObject[chartKey.LchartMetrics].data.rows.length > 0"
                 google-chart chart="locationsInsight.chartDataObject[chartKey.LchartMetrics]"
                 agc-on-select="hideSeries(selectedItem, chartKey.LchartMetrics)"
                 style="height:260px;"></div>
            <div ng-if="locationsInsight.chartDataObject[chartKey.LchartMetrics].tsType =='daily' && locationsInsight.chartDataObject[chartKey.LchartMetrics].data.rows.length > 0" id="point_legend" style="text-align:center;">
                <div style="display:inline-block;margin:0px 5px;font-size: 11px;color: #222222;text-anchor: start;font-family: Arial;">
                    <div style="width:10px; height:10px; border-radius:50%; background-color:#71c044; display:inline-block;"></div>
                    Weekend
                </div>
                <div style="display:inline-block;margin:0px 5px;font-size: 11px;color: #222222;text-anchor: start;font-family: Arial;">
                    <div style="width:10px; height:10px; border-radius:50%; background-color:#ffd503; display:inline-block;"></div>
                    Holiday
                </div>
            </div>
            <h5 ng-if="locationsInsight.chartDataObject[chartKey.LchartMetrics].data.rows.length == 0" class="metric-notavailable">Data not available</h5>
        </div>
    </div>

    <div class="col-md-6 graph-metric-location-stat" ng-if="locationsInsight.chartDataObject[chartKey.RchartMetrics]" style="border-left : 0.1rem solid rgba(204, 204, 204, 0.5)">
        <div class="graph-formaters">
            <form class="form-inline" role="form">
                <div class="form-group pull-left">
                    <select class="form-control i2e1-select" id="metrics" ng-model="chartKey.RchartMetrics" ng-change="chartsMetricsSelectionChange('right_chart')">
                        <option value="vicinity_footfall" ng-disabled ="(chartKey.LchartMetrics=='vicinity_footfall')? true: false">Daily Vicinity Footfall</option>
                        <option value="instore_footfall" ng-disabled ="(chartKey.LchartMetrics=='instore_footfall')? true: false">Daily Store Footfall</option>
                        <option value="conversion_rate" ng-disabled ="(chartKey.LchartMetrics=='conversion_rate')? true: false">Walk In Rate</option>
                        <option value="dwell_time" ng-disabled ="(chartKey.LchartMetrics=='dwell_time')? true: false">Dwell Time (minutes)</option>
                    </select>
                </div>
                <div class="form-group" style="padding-left:3px;">
                    <div class="radio">
                        <label for="hourly" class="radio-inline">
                            <input style="margin-right:0;" id="hourly" type="radio" ng-model="locationsInsight.chartDataObject[chartKey.RchartMetrics].tsType"
                                   value="hourly" ng-change="chartsMetricsSelectionChange('right_chart')" /> Hourly
                        </label>
                    </div>
                </div>

                <div class="form-group" style="padding-left:3px;">
                    <div class="radio">
                        <label for="daily" class="radio-inline">
                            <input style="margin-right:0;" id="daily" type="radio" ng-model="locationsInsight.chartDataObject[chartKey.RchartMetrics].tsType" value="daily"
                                   ng-change="chartsMetricsSelectionChange('right_chart')" /> Daily
                        </label>
                    </div>
                </div>
                <div class="form-group" style="padding-left:3px;">
                    <div class="radio">
                        <label for="daily" class="radio-inline">
                            <input style="margin-right:0;" id="daily" type="radio" ng-model="locationsInsight.chartDataObject[chartKey.RchartMetrics].tsType" value="weekly"
                                   ng-change="chartsMetricsSelectionChange('right_chart')" /> Weekly
                        </label>
                    </div>
                </div>
                <div class="form-group" style="padding-left:3px;">
                    <div class="radio">
                        <label for="monthly" class="radio-inline">
                            <input style="margin-right:0;" id="monthly" type="radio" ng-model="locationsInsight.chartDataObject[chartKey.RchartMetrics].tsType"
                                   value="monthly" ng-change="chartsMetricsSelectionChange('right_chart')" /> Monthly
                        </label>
                    </div>
                </div>
                <div ng-if="locationsInsight.chartDataObject[chartKey.RchartMetrics].data.rows" class="form-group graph-export" uib-dropdown>
                    <a class="" data-toggle="dropdown" href="#" uib-dropdown-toggle>
                        <i class="glyphicon glyphicon-download"></i>
                    </a>
                    <ul class="dropdown-menu-right" uib-dropdown-menu role="menu">
                        <li role="menuitem"><a href="" ng-click="googleDataTableToCSV(locationsInsight.chartDataObject[chartKey.RchartMetrics],chartKey.RchartMetrics)">Export CSV</a></li>
                        <li class="divider"></li>
                        <li role="menuitem"><a href="" ng-click="saveAsImg('chartMetrics_chart_container', locationsInsight.chartDataObject[chartKey.RchartMetrics].tsType, chartKey.RchartMetrics)">Export PNG</a></li>
                    </ul>
                </div>
            </form>
        </div>
        <div ng-if="locationsInsight.chartDataObject[chartKey.RchartMetrics].data.rows" class="graph-container" style="margin: 3rem -3.5rem 0;">
            <div id="chartMetrics_chart_container"
                 ng-if="locationsInsight.chartDataObject[chartKey.RchartMetrics].data.rows.length > 0"
                 google-chart chart="locationsInsight.chartDataObject[chartKey.RchartMetrics]"
                 agc-on-select="hideSeries(selectedItem, chartKey.RchartMetrics)"
                 style="height:260px;"></div>
            <div ng-if="locationsInsight.chartDataObject[chartKey.RchartMetrics].tsType =='daily' && locationsInsight.chartDataObject[chartKey.RchartMetrics].data.rows.length > 0" id="point_legend" style="text-align:center;">
                <div style="display:inline-block;margin:0px 5px;font-size: 11px;color: #222222;text-anchor: start;font-family: Arial;">
                    <div style="width:10px; height:10px; border-radius:50%; background-color:#71c044; display:inline-block;"></div>
                    Weekend
                </div>
                <div style="display:inline-block;margin:0px 5px;font-size: 11px;color: #222222;text-anchor: start;font-family: Arial;">
                    <div style="width:10px; height:10px; border-radius:50%; background-color:#ffd503; display:inline-block;"></div>
                    Holiday
                </div>
            </div>
            <h5 ng-if="locationsInsight.chartDataObject[chartKey.RchartMetrics].data.rows.length == 0" class="metric-notavailable">Data not available</h5>
        </div>
        
    </div>
    
</div>
<div class="row widget widget-card transparent-card" ng-if="locationsInsight.chartDataObject.vicinity_footfall">
    <footer class="widget-footer" style="position:relative;left:0;padding:0">
        <div class="pull-right info-italic show-imp-before" style="margin-top: 5px;position:relative;">
            Note: Vicinity and instore footfall values are on a scale of 1000 and 100 respectively
        </div>
    </footer>
</div>

<!--{{customersInsight.demographics}}-->
<div data-id="donut_single"></div>

<div class="row widget widget-card">
    <header class="widget-title" style="margin-bottom: 2.5rem;">What is the profile of your customers?</header>
    <div class="col-md-4 graph-metric-stat">
        <div ng-if="customersInsight.genderDistribution" class="form-group graph-export" uib-dropdown>
            <a class="" data-toggle="dropdown" href="#" uib-dropdown-toggle>
                <i class="glyphicon glyphicon-download"></i>
            </a>
            <ul class="dropdown-menu-right" uib-dropdown-menu role="menu">
                <li role="menuitem">
                    <a href=""
                       class="custom-click-element"
                       title="Download demographics"
                       data-action="Export csv for gender demographics distribution"
                       ng-click="donutDataTableToCSV(customersInsight.demographics.genderData,'genderData')">Export CSV</a>
                </li>
                <li class="divider"></li>
                <li role="menuitem">
                    <a href=""
                       class="custom-click-element"
                       title="Download demographics"
                       data-action="Save png image for gender demographics distribution"
                       ng-click="saveScreenshotAsImg('overview_gender_distribution', 'average', 'genderData')">Export PNG</a>
                </li>
            </ul>
        </div>
        <div id="overview_gender_distribution">
            <h6 class="metric-name">Gender Distribution</h6>
            <div class="circle metric-icon donuts-overlay">
                <img style="{{customersInsight.genderDistribution.data.rows ? '' : 'animation: pulsate 1s ease-out infinite;'}}" src="images/gender.svg" alt="Gender Distribution" style="margin-bottom:10px;" />
            </div>

            <div google-chart
                 id="gender_pie_chart"
                 chart="customersInsight.genderDistribution"
                 agc-on-ready="placeDonutIcons(chartWrapper)"
                 style="height:200px; width:100%; margin-top:-40px;"></div>

            <!--<div class="circle {{customersInsight.demographics.genderData ? '' : 'donut_spinner'}}">
                <div class="metric-icon donuts-overlay">
                    <img src="images/gender.svg" alt="Gender Distribution" />
                </div>
                <div ng-if="customersInsight.demographics.genderData && customersInsight.demographics.genderData != 'n/a'" class="donut-card" draw-donut
                     donutdata="customersInsight.demographics.genderData"></div>
                <div class="donut-card" ng-if="customersInsight.demographics.genderData && customersInsight.demographics.genderData == 'n/a'">
                    <div class="donut-chart"></div>
                    <h5 class="metric-notavailable">Data not available</h5>
                </div>
                </div>
                <div class="circle-labellist">
                <ul class="list-inline" ng-show="customersInsight.demographics.genderData">
                    <li ng-repeat="element in customersInsight.demographics.genderData" class="bounceInText">
                        <div class="circle-label" style="background:{{element.color}}"></div>{{element.name}}

                    </li>
                </ul>
                </div>-->
        </div>
        <!--<div ng-if="customersInsight.demographics.genderData && customersInsight.demographics.genderData != 'n/a'" class="btn-toolbar" role="toolbar">
            <div class="btn-group mr-2">
                <h3>Export</h3>
            </div>
            <div class="btn-group mr-2">
                <button type="button" class="btn btn-default" ng-click="donutDataTableToCSV(customersInsight.demographics.genderData,'genderData')"><i class="fa fa-file-excel-o" aria-hidden="true"></i>CSV</button>
            </div>
            <div class="btn-group">
                <button type="button" class="btn btn-default" ng-click="saveScreenshotAsImg('overview_gender_distribution', 'average', 'genderData')"><i class="fa fa-file-picture-o" aria-hidden="true"></i>PNG</button>
            </div>
            </div>-->
    </div>
    <div class="col-md-4 graph-metric-stat">
        <!--<div ng-if="customersInsight.demographics.ageData && customersInsight.demographics.ageData != 'n/a'" class="form-group graph-export" uib-dropdown>
            <a class="" data-toggle="dropdown" href="#" uib-dropdown-toggle>
                <i class="glyphicon glyphicon-download"></i>
            </a>
            <ul class="dropdown-menu-right" uib-dropdown-menu role="menu">
                <li role="menuitem"><a href="" ng-click="donutDataTableToCSV(customersInsight.demographics.ageData,'ageData')">Export CSV</a></li>
                <li class="divider"></li>
                <li role="menuitem"><a href="" ng-click="saveScreenshotAsImg('overview_age_distribution', 'average', 'ageData')">Export PNG</a></li>
            </ul>
            </div>-->
        <div ng-if="customersInsight.ageDistribution" class="form-group graph-export" uib-dropdown>
            <a class="" data-toggle="dropdown" href="#" uib-dropdown-toggle>
                <i class="glyphicon glyphicon-download"></i>
            </a>
            <ul class="dropdown-menu-right" uib-dropdown-menu role="menu">
                <li role="menuitem">
                    <a href=""
                       class="custom-click-element"
                       title="Download demographics"
                       data-action="Export csv for age demographics distribution"
                       ng-click="donutDataTableToCSV(customersInsight.demographics.ageData,'ageData')">Export CSV</a>
                </li>
                <li class="divider"></li>
                <li role="menuitem">
                    <a href=""
                       class="custom-click-element"
                       title="Download demographics"
                       data-action="Save png image for age demographics distribution"
                       ng-click="saveScreenshotAsImg('overview_age_distribution', 'average', 'ageData')">Export PNG</a>
                </li>
            </ul>
        </div>
        <div id="overview_age_distribution">
            <h6 class="metric-name">Age Group <span>(years)</span></h6>
            <div class="circle metric-icon donuts-overlay">
                <img style="{{customersInsight.ageDistribution.data.rows? '' : 'animation: pulsate 1s ease-out infinite;'}}" src="images/birthday.svg" alt="Age Group" />
            </div>

            <div google-chart chart="customersInsight.ageDistribution"
                 agc-on-ready="placeDonutIcons(chartWrapper)"
                 style="height:200px; width:100%; margin-top:-40px;"></div>

            <!--<div class="circle {{customersInsight.demographics.ageData ? '' : 'donut_spinner'}}">
                <div class="metric-icon donuts-overlay">
                    <img src="images/birthday.svg" alt="Age Group" />
                </div>
                <div ng-if="customersInsight.demographics.ageData && customersInsight.demographics.ageData != 'n/a'" class="donut-card" draw-donut
                     donutdata="customersInsight.demographics.ageData"></div>
                <div class="donut-card" ng-if="customersInsight.demographics.ageData && customersInsight.demographics.ageData == 'n/a'">
                    <div class="donut-chart"></div>
                    <h5 class="metric-notavailable">Data not available</h5>
                </div>
                </div>
                <div class="circle-labellist">
                <ul class="list-inline" ng-show="customersInsight.demographics.ageData">
                    <li ng-repeat="element in customersInsight.demographics.ageData" class="bounceInText">
                        <div class="circle-label" style="background:{{element.color}}"></div>{{element.name}}

                    </li>
                </ul>
                </div>-->
        </div>
    </div>
    <div class="col-md-4 graph-metric-stat">
        <div ng-if="customersInsight.spendDistribution" class="form-group graph-export" uib-dropdown>
            <a class="" data-toggle="dropdown" href="#" uib-dropdown-toggle>
                <i class="glyphicon glyphicon-download"></i>
            </a>
            <ul class="dropdown-menu-right" uib-dropdown-menu role="menu">
                <li role="menuitem">
                    <a href=""
                       class="custom-click-element"
                       title="Download demographics"
                       data-action="Export csv for spend capacity demographics distribution"
                       ng-click="donutDataTableToCSV(customersInsight.demographics.spendCapacityData,'spendCapacityData')">Export CSV</a>
                </li>
                <li class="divider"></li>
                <li role="menuitem">
                    <a href=""
                       class="custom-click-element"
                       title="Download demographics"
                       data-action="Save png image for spend capacity demographics distribution"
                       ng-click="saveScreenshotAsImg('overview_spend_distribution', 'average', 'spendCapacityData')">Export PNG</a>
                </li>
            </ul>
        </div>
        <div id="overview_spend_distribution">
            <h6 class="metric-name">Spend Capacity</h6>
            <div class="circle metric-icon donuts-overlay">
                <img style="{{customersInsight.spendDistribution.data.rows? '' : 'animation: pulsate 1s ease-out infinite;'}}" src="images/money.svg" alt="Spend Capacity" />
            </div>
            <div google-chart chart="customersInsight.spendDistribution"
                 agc-on-ready="placeDonutIcons(chartWrapper)"
                 style="height:200px; width:100%; margin-top:-40px;"></div>

            <!--<div class="circle {{customersInsight.demographics.spendCapacityData ? '' : 'donut_spinner'}}">
                <div class="metric-icon donuts-overlay">
                    <img src="images/money.svg" alt="Spend Capacity" />
                </div>
                <div ng-if="customersInsight.demographics.spendCapacityData && customersInsight.demographics.spendCapacityData != 'n/a'" class="donut-card" draw-donut
                     donutdata="customersInsight.demographics.spendCapacityData"></div>
                <div class="donut-card" ng-if="customersInsight.demographics.spendCapacityData && customersInsight.demographics.spendCapacityData == 'n/a'">
                    <div class="donut-chart"></div>
                    <h5 class="metric-notavailable">Data not available</h5>
                </div>
                </div>
                <div class="circle-labellist">
                <ul class="list-inline" ng-show="customersInsight.demographics.spendCapacityData">
                    <li ng-repeat="element in customersInsight.demographics.spendCapacityData" class="bounceInText">
                        <div class="circle-label" style="background:{{element.color}}"></div>{{element.name}}

                    </li>
                </ul>
                </div>-->
        </div>
    </div>
</div>

<!--{{campaignInsight}}-->
<!--<div class="row widget widget-card" ng-if="campaignInsight.data_captured.t_active_campaign > 0">-->
<div class="row widget widget-card">
    <header class="widget-title">How well do your campaigns work?</header>
    <div class="campaign-info" ng-if="campaignInsight.data_captured.t_customer_targeted > 0">
        <div class="col-md-2 campaign-items">
            <div class="metric-icon"><img src="images/people.svg" alt="Gender Distribution" /></div>
        </div>
        <div class="col-md-7 campaign-items">
            <table class="steps">
                <tr>
                    <td>
                        <h6 class="metric-name">Customers Targeted</h6>
                    </td>
                    <td>
                        <h6 class="metric-name">Conversion Rate</h6>
                    </td>
                    <td>
                        <h6 class="metric-name">Revenue Earned</h6>
                    </td>
                </tr>
                <tr class="tringualr-steps">
                    <td>
                        <span class="blue"></span>
                    </td>
                    <td>
                        <span class="green"></span>
                    </td>
                    <td>
                        <span class="yellow"></span>
                    </td>
                </tr>
                <tr>
                    <td>
                        <h5 class="metric-stat" ng-show="!campaignInsight.data_captured.t_customer_targeted"><img src="images/data-loader.gif" alt="Loading.."></h5>
                        <h5 class="metric-stat bounceInText" ng-show="campaignInsight.data_captured.t_customer_targeted">{{campaignInsight.data_captured.t_customer_targeted | number}}<sup uib-tooltip-html="lastDateRangeHtml" tooltip-append-to-body="true" ng-if="campaignInsight.data_captured.t_customer_targeted_change != 0" class="{{campaignInsight.data_captured.t_customer_targeted_change > 0 ? 'increment' : 'decrement'}}">  <span ng-if="campaignInsight.data_captured.t_customer_targeted_change > 0">+</span>{{campaignInsight.data_captured.t_customer_targeted_change | round:1}}%</sup></h5>
                    </td>
                    <td>
                        <h5 class="metric-stat" ng-show="!campaignInsight.data_captured.t_conversion_rate"><img src="images/data-loader.gif" alt="Loading.."></h5>
                        <h5 class="metric-stat bounceInText" ng-show="campaignInsight.data_captured.t_conversion_rate">{{campaignInsight.data_captured.t_conversion_rate | round:1}}%<sup uib-tooltip-html="lastDateRangeHtml" tooltip-append-to-body="true" ng-if="campaignInsight.data_captured.t_conversion_rate_change" class="{{campaignInsight.data_captured.t_conversion_rate_change > 0 ? 'increment' : 'decrement'}}">  <span ng-if="campaignInsight.data_captured.t_conversion_rate_change > 0">+</span>{{campaignInsight.data_captured.t_conversion_rate_change | round:1}}%</sup></h5>
                    </td>
                    <td>
                        <h5 class="metric-stat" ng-show="!campaignInsight.data_captured.t_revenue_earned"><img src="images/data-loader.gif" alt="Loading.."></h5>
                        <h5 class="metric-stat bounceInText" ng-show="campaignInsight.data_captured.t_revenue_earned">{{campaignInsight.data_captured.t_revenue_earned | number}}<sup uib-tooltip-html="lastDateRangeHtml" tooltip-append-to-body="true" ng-if="campaignInsight.data_captured.t_revenue_earned_change != 0" class="{{campaignInsight.data_captured.t_revenue_earned_change > 0 ? 'increment' : 'decrement'}}">  <span ng-if="campaignInsight.data_captured.t_revenue_earned_change > 0">+</span>{{campaignInsight.data_captured.t_revenue_earned_change | round:1}}%</sup></h5>
                    </td>
                </tr>
            </table>
        </div>
        <div class="col-md-3 campaign-items">
            <button class="btn btn-primary custom-click-element"
                    title="View Campaigns Button"
                    data-action="View all campaigns for benefit articulation"
                    ng-click="viewCampaigns()">
                View Campaigns
            </button>
        </div>
    </div>
    <div uib-collapse="!isCollapsed">
        <div class="campaign-history">
            <table class="table table-striped">
                <thead>
                    <tr>
                        <th>Campaign Type</th>
                        <th>#Targeted</th>
                        <th>Conversion</th>
                        <th>Revenue</th>
                    </tr>
                </thead>
                <tbody ng-repeat="report in campaignInsight.data_captured.t_prime_reports">
                    <tr ng-init="toggle[$index] = false"
                        ng-click="toggle[$index] = !toggle[$index]"
                        style="cursor:pointer;"
                        class="custom-click-element"
                        title="View detailed campaign report"
                        data-action="View detailed campaign report for {{report.name}}">
                        <td>{{report.name}}</td>
                        <td>
                            <h5 class="metric-stat"><span class="bounceintext" ng-show="report.targeted">{{report.targeted | number}}<sup ng-if="report.last_period_targeted != '0'" uib-tooltip-html="lastDateRangeHtml" tooltip-append-to-body="true" class="{{report.targeted > report.last_period_targeted ? 'increment' : 'decrement'}}">  {{getPercentChange(report.targeted,report.last_period_targeted)}}%</sup></span></h5>
                            <h5 class="metric-stat"><span class="bounceintext" ng-show="!report.targeted"><img src="images/data-loader.gif" alt="Loading.."></span></h5>
                        </td>
                        <td>
                            <h5 class="metric-stat"><span class="bounceintext" ng-show="report.conv_rate">{{report.conv_rate | round:1 }}%<sup ng-if="report.last_period_conv_rate != '0'" uib-tooltip-html="lastDateRangeHtml" tooltip-append-to-body="true" class="{{report.conv_rate > report.last_period_conv_rate ? 'increment' : 'decrement'}}">  {{getPercentChange(report.conv_rate,report.last_period_conv_rate) | round:1 }}%</sup></span></h5>
                            <h5 class="metric-stat"><span class="bounceintext" ng-show="!report.conv_rate"><img src="images/data-loader.gif" alt="Loading.."></span></h5>
                        </td>
                        <td>
                            <h5 class="metric-stat"><span class="bounceintext" ng-show="report.revenue">{{report.revenue | number}}<sup ng-if="report.last_period_revenue != '0'" uib-tooltip-html="lastDateRangeHtml" tooltip-append-to-body="true" class="{{report.targeted > report.last_period_revenue ? 'increment' : 'decrement'}}">  {{getPercentChange(report.revenue,report.last_period_revenue)}}%</sup></span></h5>
                            <h5 class="metric-stat"><span class="bounceintext" ng-show="!report.revenue"><img src="images/data-loader.gif" alt="Loading.."></span></h5>
                        </td>
                    </tr>
                    <tr ng-if="toggle[$index]">
                        <td ng-if="report.detail_report.length > 0" colspan="4" style="padding: 1rem 0;">
                            <table class="table table-bordered">
                                <thead>
                                    <tr>
                                        <th>Campaign Name</th>
                                        <th>Date</th>
                                        <th>#Targeted</th>
                                        <th>Conversion</th>
                                        <th>Revenue</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr ng-repeat="campain in report.detail_report">
                                        <td>{{campain.campaign_name}}</td>
                                        <td ng-bind="formatDate(campain.date)"></td>
                                        <td>
                                            <h5 class="metric-stat"><span class="bounceintext" ng-show="campain.targeted">{{campain.targeted | number}}</span></h5>
                                            <h5 class="metric-stat"><span class="bounceintext" ng-show="!campain.targeted"><img src="images/data-loader.gif" alt="Loading.."></span></h5>
                                        </td>
                                        <td>
                                            <h5 class="metric-stat"><span class="bounceintext" ng-show="campain.conversion">{{campain.conversion | round:1 }}%</span></h5>
                                            <h5 class="metric-stat"><span class="bounceintext" ng-show="!campain.conversion"><img src="images/data-loader.gif" alt="Loading.."></span></h5>
                                        </td>
                                        <td>
                                            <h5 class="metric-stat"><span class="bounceintext" ng-show="campain.revenue">{{campain.revenue | number}}</span></h5>
                                            <h5 class="metric-stat"><span class="bounceintext" ng-show="!campain.revenue"><img src="images/data-loader.gif" alt="Loading.."></span></h5>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>

    <div class="campaign-info" ng-if="campaignInsight.data_captured.t_customer_targeted == 0">
        <h1 class="no-campaigns-found" style="font-size: 1.6rem;margin: 5rem auto;">
            You are currently not running any campaigns on the i2e1 network. Log on to i2e1 admin portal
            <a href="https://www.i2e1.in/Client/Index#/login"
               class="custom-click-element"
               title="Creat campaign report"
               data-action="https://www.i2e1.in/Client/Index#/login">here</a> to start campaigns
        </h1>
    </div>
</div>

<!--<div class="row widget widget-card">
    <header class="widget-title">Know Your Locations</header>
    <div class="col-md-6 graph-metric-location-stat" style="padding: 3.5rem 0;">
        <table>
            <tr>
                <td>
                    <div class="metric-icon">
                        <img src="images/location.svg" alt="Daily Vicinity Footfall" />
                    </div>
                </td>
                <td>
                    <h6 class="metric-name">Daily Vicinity Footfall</h6>
                </td>
                <td>
                    <div ng-if="locationsInsight.avg_daily_vicinity_footfall">
                        <h5 class="metric-stat bounceInText" ng-if="locationsInsight.avg_daily_vicinity_footfall != 'n/a'">
                            {{locationsInsight.avg_daily_vicinity_footfall | number }}
                            <sup ng-if="locationsInsight.avg_daily_vicinity_footfall_change" class="{{locationsInsight.avg_daily_vicinity_footfall_change > 0 ? 'increment' : 'decrement'}}">
                                <span ng-if="locationsInsight.avg_daily_vicinity_footfall_change > 0">+</span>
                                <span ng-if="locationsInsight.avg_daily_vicinity_footfall_change != 0" uib-tooltip-html="lastDateRangeHtml" tooltip-append-to-body="true">
                                    {{locationsInsight.avg_daily_vicinity_footfall_change + '%'}}
                                </span>
                                <span ng-if="locationsInsight.avg_daily_vicinity_footfall_change == 0" uib-tooltip-html="lastDateRangeHtml" tooltip-append-to-body="true" class="increment">
                                    0%
                                </span>
                                <span ng-if="locationsInsight.avg_daily_vicinity_footfall_change == 'n/a'" uib-tooltip-html="lastDateRangeHtml" tooltip-append-to-body="true">
                                    N/A
                                </span>
                            </sup>
                        </h5>
                        <h5 class="metric-notavailable bounceInText" ng-if="locationsInsight.avg_daily_vicinity_footfall == 'n/a'">
                            Data not available
                        </h5>
                        <p class="regular-grey bounceInText" ng-if="locationsInsight.avg_industry_daily_vicinity_footfall != 'n/a'">
                            Industry: {{locationsInsight.avg_industry_daily_vicinity_footfall | number}}
                            <span ng-if="locationsInsight.avg_industry_daily_vicinity_footfall_change != 0"> / </span>
                            <span ng-if="locationsInsight.avg_industry_daily_vicinity_footfall_change > 0"> +</span>
                            <span ng-if="locationsInsight.avg_industry_daily_vicinity_footfall_change != 0" uib-tooltip-html=" lastdaterangehtml" tooltip-append-to-body="true">
                                {{locationsInsight.avg_industry_daily_vicinity_footfall_change + '%'}}
                            </span>
                            <span ng-if="locationsInsight.avg_industry_daily_vicinity_footfall_change == 'n/a'" uib-tooltip-html=" lastdaterangehtml" tooltip-append-to-body="true">
                                N/A
                            </span>
                        </p>
                        <p class="regular-grey bounceInText" ng-if="locationsInsight.avg_industry_daily_vicinity_footfall == 'n/a'">
                            Industry: Data not available
                        </p>
                    </div>
                    <div ng-if="!locationsInsight.avg_daily_vicinity_footfall">
                        <img class="metric-stat" style="height:2.4rem;" src="images/data-loader.gif" alt="Loading.." />
                    </div>
                </td>
                <td>
                    <div ng-if="locationsInsight.avg_daily_vicinity_footfall && locationsInsight.avg_industry_daily_vicinity_footfall">
                        <p class="success" ng-if="locationsInsight.avg_daily_vicinity_footfall.percentChange(locationsInsight.avg_industry_daily_vicinity_footfall) >= 0">
                            {{locationsInsight.avg_daily_vicinity_footfall.percentChange(locationsInsight.avg_industry_daily_vicinity_footfall).round(1)}}
                            % more than industry
                        </p>
                        <p class="error" ng-if="locationsInsight.avg_daily_vicinity_footfall.percentChange(locationsInsight.avg_industry_daily_vicinity_footfall) < 0">
                            {{-locationsInsight.avg_daily_vicinity_footfall.percentChange(locationsInsight.avg_industry_daily_vicinity_footfall).round(1)}}
                            % less than industry
                        </p>
                    </div>
                    <div ng-if="!locationsInsight.avg_daily_vicinity_footfall || !locationsInsight.avg_industry_daily_vicinity_footfall">
                        <img class="metric-stat" style="height:2.4rem;" src="images/data-loader.gif" alt="Loading.." />
                    </div>
                </td>
            </tr>
            <tr>
                <td>
                    <div class="metric-icon">
                        <img src="images/feet.svg" alt="Daily Store Footfall" />
                    </div>
                </td>
                <td>
                    <h6 class="metric-name">Daily Store Footfall</h6>
                </td>
                <td>
                    <div ng-if="locationsInsight.avg_daily_instore_footfall">
                        <h5 class="metric-stat bounceInText" ng-if="locationsInsight.avg_daily_instore_footfall != 'n/a'">
                            {{locationsInsight.avg_daily_instore_footfall | number }}
                            <sup ng-if="locationsInsight.avg_daily_instore_footfall_change" class="{{locationsInsight.avg_daily_instore_footfall_change > 0 ? 'increment' : 'decrement'}}">
                                <span ng-if="locationsInsight.avg_daily_instore_footfall_change > 0">+</span>
                                <span ng-if="locationsInsight.avg_daily_instore_footfall_change != 0" uib-tooltip-html="lastDateRangeHtml" tooltip-append-to-body="true">
                                    {{locationsInsight.avg_daily_instore_footfall_change + '%'}}
                                </span>
                                <span ng-if="locationsInsight.avg_daily_instore_footfall_change == 0" uib-tooltip-html="lastDateRangeHtml" tooltip-append-to-body="true" class="increment">
                                    0%
                                </span>
                                <span ng-if="locationsInsight.avg_daily_instore_footfall_change == 'n/a'" uib-tooltip-html="lastDateRangeHtml" tooltip-append-to-body="true">
                                    N/A
                                </span>
                            </sup>
                        </h5>
                        <h5 class="metric-notavailable bounceInText" ng-if="locationsInsight.avg_daily_instore_footfall == 'n/a'">
                            Data not available
                        </h5>
                        <p class="regular-grey bounceInText" ng-if="locationsInsight.avg_industry_daily_instore_footfall != 'n/a'">
                            Industry: {{locationsInsight.avg_industry_daily_instore_footfall | number}}
                            <span ng-if="locationsInsight.avg_industry_daily_instore_footfall_change != 0"> / </span>
                            <span ng-if="locationsInsight.avg_industry_daily_instore_footfall_change > 0"> +</span>
                            <span ng-if="locationsInsight.avg_industry_daily_instore_footfall_change != 0" uib-tooltip-html="lastDateRangeHtml" tooltip-append-to-body="true">
                                {{locationsInsight.avg_industry_daily_instore_footfall_change + '%'}}
                            </span>
                            <span ng-if="locationsInsight.avg_industry_daily_instore_footfall_change == 0" uib-tooltip-html="lastDateRangeHtml" tooltip-append-to-body="true">
                                0
                            </span>
                            <span ng-if="locationsInsight.avg_industry_daily_instore_footfall_change == 'n/a'" uib-tooltip-html="lastDateRangeHtml" tooltip-append-to-body="true">
                                N/A
                            </span>
                        </p>
                        <p class="regular-grey bounceInText" ng-if="locationsInsight.avg_industry_daily_instore_footfall == 'n/a'">
                            Industry: Data not available
                        </p>
                    </div>
                    <div ng-if="!locationsInsight.avg_daily_instore_footfall">
                        <img class="metric-stat" style="height:2.4rem;" src="images/data-loader.gif" alt="Loading.." />
                    </div>
                </td>
                <td>
                    <div ng-if="locationsInsight.avg_daily_instore_footfall && locationsInsight.avg_industry_daily_instore_footfall">
                        <p class="success" ng-if="locationsInsight.avg_daily_instore_footfall.percentChange(locationsInsight.avg_industry_daily_instore_footfall) >= 0">
                            {{locationsInsight.avg_daily_instore_footfall.percentChange(locationsInsight.avg_industry_daily_instore_footfall).round(1)}}
                            % more than industry
                        </p>
                        <p class="error" ng-if="locationsInsight.avg_daily_instore_footfall.percentChange(locationsInsight.avg_industry_daily_instore_footfall) < 0">
                            {{-locationsInsight.avg_daily_instore_footfall.percentChange(locationsInsight.avg_industry_daily_instore_footfall).round(1)}}
                            % less than industry
                        </p>
                    </div>
                    <div ng-if="!locationsInsight.avg_daily_instore_footfall || !locationsInsight.avg_industry_daily_instore_footfall">
                        <img class="metric-stat" style="height:2.4rem;" src="images/data-loader.gif" alt="Loading.." />
                    </div>
                </td>
            </tr>
            <tr>
                <td>
                    <div class="metric-icon">
                        <img src="images/shopping.svg" alt="Coversion Rate" />
                    </div>
                </td>
                <td>
                    <h6 class="metric-name">Walk In </br>Rate</h6>
                </td>
                <td>
                    <div ng-if="locationsInsight.avg_daily_conversion_rate">
                        <h5 class="metric-stat bounceInText" ng-if="locationsInsight.avg_daily_conversion_rate != 'n/a'">
                            {{locationsInsight.avg_daily_conversion_rate + '%'}}
                            <sup ng-if="locationsInsight.avg_daily_conversion_rate_change" class="{{locationsInsight.avg_daily_conversion_rate_change > 0 ? 'increment' : 'decrement'}}">
                                <span ng-if="locationsInsight.avg_daily_conversion_rate_change > 0">+</span>
                                <span ng-if="locationsInsight.avg_daily_conversion_rate_change != 0" uib-tooltip-html="lastDateRangeHtml" tooltip-append-to-body="true">
                                    {{locationsInsight.avg_daily_conversion_rate_change + '%'}}
                                </span>
                                <span ng-if="locationsInsight.avg_daily_conversion_rate_change == 0" uib-tooltip-html="lastDateRangeHtml" tooltip-append-to-body="true" class="increment">
                                    0%
                                </span>
                                <span ng-if="locationsInsight.avg_daily_conversion_rate_change == 'n/a'" uib-tooltip-html="lastDateRangeHtml" tooltip-append-to-body="true">
                                    N/A
                                </span>
                            </sup>
                        </h5>
                        <h5 class="metric-notavailable bounceInText" ng-if="locationsInsight.avg_daily_conversion_rate == 'n/a'"> Data not available</h5>
                        <p class="regular-grey bounceInText" ng-if="locationsInsight.avg_industry_daily_conversion_rate != 'n/a'">
                            Industry: {{locationsInsight.avg_industry_daily_conversion_rate + '%'}}
                            <span ng-if="locationsInsight.avg_industry_daily_conversion_rate_change != 0"> / </span>
                            <span ng-if="locationsInsight.avg_industry_daily_conversion_rate_change > 0"> +</span>
                            <span ng-if="locationsInsight.avg_industry_daily_conversion_rate_change != 0" uib-tooltip-html="lastDateRangeHtml" tooltip-append-to-body="true">
                                {{locationsInsight.avg_industry_daily_conversion_rate_change + '%'}}
                            </span>
                            <span ng-if="locationsInsight.avg_industry_daily_conversion_rate_change == 0" uib-tooltip-html="lastDateRangeHtml" tooltip-append-to-body="true">
                                0%
                            </span>
                            <span ng-if="locationsInsight.avg_industry_daily_conversion_rate_change == 'n/a'" uib-tooltip-html="lastDateRangeHtml" tooltip-append-to-body="true">
                                N/A
                            </span>
                        </p>
                        <p class="regular-grey bounceInText" ng-if="locationsInsight.avg_industry_daily_conversion_rate == 'n/a'">
                            Industry: Data not available
                        </p>
                    </div>
                    <div ng-if="!locationsInsight.avg_daily_conversion_rate">
                        <img class="metric-stat" style="height:2.4rem;" src="images/data-loader.gif" alt="Loading.." />
                    </div>
                </td>
                <td>
                    <div ng-if="locationsInsight.avg_daily_conversion_rate && locationsInsight.avg_industry_daily_conversion_rate">
                        <p class="success" ng-if="locationsInsight.avg_daily_conversion_rate.percentChange(locationsInsight.avg_industry_daily_conversion_rate) >= 0">
                            {{locationsInsight.avg_daily_conversion_rate.percentChange(locationsInsight.avg_industry_daily_conversion_rate).round(1)}}
                            % more than industry
                        </p>
                        <p class="error" ng-if="locationsInsight.avg_daily_conversion_rate.percentChange(locationsInsight.avg_industry_daily_conversion_rate) < 0">
                            {{-locationsInsight.avg_daily_conversion_rate.percentChange(locationsInsight.avg_industry_daily_conversion_rate).round(1)}}
                            % less than industry
                        </p>
                    </div>
                    <div ng-if="!locationsInsight.avg_daily_conversion_rate || !locationsInsight.avg_industry_daily_conversion_rate">
                        <img class="metric-stat" style="height:2.4rem;" src="images/data-loader.gif" alt="Loading.." />
                    </div>
                </td>
            </tr>
        </table>
    </div>
    <div class="col-md-6 graph-metric-location-stat" style="border-left : 0.1rem solid rgba(204, 204, 204, 0.5)">
        <div class="graph-formaters">
            <form class="form-inline" role="form">
                <div class="form-group pull-left">
                    <select class="form-control i2e1-select" id="metrics" ng-model="chartMetrics" ng-change="chartsMetricsSelectionChange()">
                        <option value="vicinity_footfall">Daily Vicinity Footfall</option>
                        <option value="instore_footfall">Daily Store Footfall</option>
                        <option value="conversion_rate">Walk In Rate</option>
                        <option value="dwell_time">Dwell Time (minutes)</option>
                    </select>
                </div>
                <div class="form-group">
                    <div class="radio">
                        <label for="hourly" class="radio-inline">
                            <input id="hourly" type="radio" ng-model="locationsInsight.chartDataObject[chartMetrics].tsType"
                                   value="hourly" ng-change="chartsMetricsSelectionChange()" /> Hourly
                        </label>
                    </div>
                </div>

                <div class="form-group">
                    <div class="radio">
                        <label for="daily" class="radio-inline">
                            <input id="daily" type="radio" ng-model="locationsInsight.chartDataObject[chartMetrics].tsType" value="daily"
                                   ng-change="chartsMetricsSelectionChange()" /> Daily
                        </label>
                    </div>
                </div>
                <div class="form-group">
                    <div class="radio">
                        <label for="monthly" class="radio-inline">
                            <input id="monthly" type="radio" ng-model="locationsInsight.chartDataObject[chartMetrics].tsType"
                                   value="monthly" ng-change="chartsMetricsSelectionChange()" /> Monthly
                        </label>
                    </div>
                </div>
                <div ng-if="locationsInsight.chartDataObject[chartMetrics].data.rows" class="form-group graph-export" uib-dropdown>
                    <a class="" data-toggle="dropdown" href="#" uib-dropdown-toggle>
                        <i class="glyphicon glyphicon-download"></i>
                    </a>
                    <ul class="dropdown-menu-right" uib-dropdown-menu role="menu">
                        <li role="menuitem"><a href="" ng-click="googleDataTableToCSV(locationsInsight.chartDataObject[chartMetrics],chartMetrics)">Export CSV</a></li>
                        <li class="divider"></li>
                        <li role="menuitem"><a href="" ng-click="saveAsImg('chartMetrics_chart_container', locationsInsight.chartDataObject[chartMetrics].tsType, chartMetrics)">Export PNG</a></li>
                    </ul>
                </div>
            </form>
        </div>
        <div ng-if="locationsInsight.chartDataObject[chartMetrics].data.rows" class="graph-container" style="margin: 3rem -3.5rem 0;">
            <div id="chartMetrics_chart_container"
                 ng-if="locationsInsight.chartDataObject[chartMetrics].data.rows.length > 0"
                 google-chart chart="locationsInsight.chartDataObject[chartMetrics]"
                 agc-on-select="hideSeries(selectedItem, chartMetrics)"
                 style="height:260px;"></div>
            <div ng-if="locationsInsight.chartDataObject[chartMetrics].tsType =='daily' && locationsInsight.chartDataObject[chartMetrics].data.rows.length > 0" id="point_legend" style="text-align:center;">
                <div style="display:inline-block; margin:0px 5px; font-size:14px; color:#222222;">
                    <div style="width:10px; height:10px; border-radius:50%; background-color:#71c044; display:inline-block;"></div>
                    Weekend
                </div>
                <div style="display:inline-block; margin:0px 5px; font-size:14px; color:#222222;">
                    <div style="width:10px; height:10px; border-radius:50%; background-color:#ffd503; display:inline-block;"></div>
                    Holiday
                </div>
            </div>
            <h5 ng-if="locationsInsight.chartDataObject[chartMetrics].data.rows.length == 0" class="metric-notavailable">Data not available</h5>
        </div>
    </div>
</div>-->
