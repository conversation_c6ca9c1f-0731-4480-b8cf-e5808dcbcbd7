@import "fonts.less";
@transparent : transparent;
@white : #ffffff;
@light_alice_blue:#f8f9fb;
@black_haze: #e6e7e8;
@athens_grey :#E5E9EC;
@border-grey : rgba(204, 204, 204, 0.5);
@regular_grey : #929497;
@dark-grey:#58595b;
@dark-blue:#304799;
@light-blue:#3fa9f5;
@green : #72bd44;
@light-green: #75bf44;
@red: #eb2229;

@i2e1_green : #73bd44;
@i2e1_blue: #324999;
@i2e1_yellow: #fdd20c;
@i2e1_red: #e9262c;

/*Dark Blue - #304799
Light Blue - #3fa9f5
Red - #ec1c24
Yellow - #ffd300
Green - #72be44
Gray - #e6e6e6*/


html {
    font-size:8px;
}
body {
    font-size: 1.4rem;
    line-height: 1.428571429;
    color: #555;
    background-color: @light_alice_blue;
}

footer, header, main, menu, nav, section, summary {
    display: block;
}

.show-imp-before {
    &:before {
        content: "*";
        color: #304799;
        top: -4px;
        position: absolute;
        left: -5px;
        font-size: 12px;
    }
}
.show-imp-after {
    &:after {
        content:"*" ;
        color:@dark-blue; 
    }
}

input[type="radio"]:after, .radio input[type="radio"]:after, .radio-inline input[type="radio"]:after {
    border: 2px solid @border-grey;
}
input[type="radio"]:checked:after, .radio input[type="radio"]:checked:after, .radio-inline input[type="radio"]:checked:after {
    border-color: @border-grey;
}

input[type="radio"]:before, .radio input[type="radio"]:before, .radio-inline input[type="checkbox"]:before {
    background-color: @green; 
}

input[type="checkbox"]:after, .radio input[type="checkbox"]:after, .checkbox-inline input[type="checkbox"]:after {
    border: 2px solid @border-grey;
}
input[type="checkbox"]:checked:after, .checkbox input[type="checkbox"]:checked:after, .checkbox-inline input[type="checkbox"]:checked:after {
    border-color: rgba(114, 190, 68, 0.5);
    background-color: @green; 
}

input[type="checkbox"]:before, .checkbox input[type="checkbox"]:before, .checkbox-inline input[type="checkbox"]:before {
    background-color: @green; 
}

select,select.form-control.i2e1-select,select.i2e1-select {
    min-width: 15rem;
    text-align: left;
    background-color: transparent;
    font-size: 1.5rem;
    font-weight: 700;
    color: #304799;
    outline: 0;
    background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAAjklEQVQ4T+3SMQrCYAyG4ad6Aa/h4gVcBK9Q9JoODr2Ek4sIYic3ERHESeWHFkpt+w/dpBnzJS/JlyR6RtKz3x8Cpjjj1eHNDHt8Qk3dgzHW2ODZAJnjikOpNZk4KiBbPCqQBS44VsFtVwj5FTLcsUSOU32qrjMGLcUbuwLws1XsD4I+wa3N1Bgg+qgDgC+KjhQRlo1IvAAAAABJRU5ErkJggg==);
    background-repeat: no-repeat;
    background-size: 10%;
    background-position: 95% 40%;
    -webkit-box-shadow: none;
    box-shadow: none;
    border: 1px solid rgba(204, 204, 204, 0.5);
    border-radius: .3rem;
    //padding: .2rem 1.5rem;
    padding: .5rem 5rem .5rem 1.5rem;
    text-transform: uppercase;
    &:focus {
        width: auto;
        outline: none;
        box-shadow: none;
    }
    option {
        display: block;
        padding: .3rem 1rem;
        clear: both;
        font-weight: 400;
        font-size: 1.5rem;
        line-height: 1.846;
        color: #58595b;
        text-transform: capitalize;
    }
    option:disabled {
    color: lightgrey;
}
}


.text-center-align {
    text-align:center;
}

.text-right-align {
    text-align:right;
}

.no-pad {
    padding:0;
}


@keyframes createBox {
  from {
    transform: scale(0);
    opacity: 0; 
  }
  to {
    transform: scale(1);
    opacity: 1;
  }
}
@keyframes DestroyBox {
  from {
    transform: scale(1);
    opacity: 1;
  }
  to {
    transform: scale(0);
    opacity: 0; 
  }
}

.applyCreateBoxCss {
    animation: createBox .3s;
}

.applyDestroyBoxCss {
    animation: DestroyBox .3s;
}

/* DEMO-SPECIFIC STYLES */
/* The typing effect */

@keyframes typing {
    from {
        width: 0
    }
    to {
        width: 100%
    }
}
.typewriter {
    overflow: hidden;
    white-space: nowrap;
    /* Keeps the content on a single line */
    margin: 0 auto;
        display: inline-block;
    /* Gives that scrolling effect as the typing happens */
    animation: typing .3s steps(30, end)
}


@keyframes bounceIn{
  0%{
    opacity: 0;
    transform: scale(0.3) translate3d(0,0,0);
  }
  50%{
    opacity: 0.9;
    transform: scale(1.1);
  }
  80%{
    opacity: 1;
    transform: scale(0.89);
  }
  100%{
    opacity: 1;
    transform: scale(1) translate3d(0,0,0);
  }
}

@keyframes pulsate {
    0% {
      transform: scale(.1);
      opacity: 0.0;
    }
    50% {
      opacity: 1;
    }
    100% {
      transform: scale(1.2);
      opacity: 0;
    }
}

.bounceInCss {
    opacity: 0;
    animation-name: bounceIn;
    animation-duration: 450ms;
    animation-timing-function: linear;
    animation-fill-mode: forwards;
}

.bounceInText {
    overflow: hidden;
    margin: 0 auto;
    display: inline-block;
    .bounceInCss;
}

@keyframes fadein {
  from { opacity: 0}
  to   { opacity: 1}
}
@keyframes fadeout {
  from { opacity: 1}
  to   { opacity: 0}
}

@keyframes spinner {
  to {transform: rotate(360deg);}
}
 
@keyframes loadingMessageKeyframe {
    0% {
        max-width: 0;
    }
}

.loading-message {
    color: rgba(0, 0, 0, .3);
    font-size:2.4rem;
    text-align: center;
    &:before {
        content: attr(data-text);
        position: absolute;
        overflow: hidden;
        max-width: 100%;
        white-space: nowrap;
        color: @dark-grey;
        animation: loadingMessageKeyframe 3s linear infinite;
    }
}

.custom_scroll {
    &::-webkit-scrollbar {
        width: 1.6rem;
    }

    &::-webkit-scrollbar-track {
        -webkit-box-shadow: inset 0 0 .5rem rgba(204 , 204 , 204 , 0.75);
        -webkit-border-radius: 0;
        border-radius: 0;
    }

    &::-webkit-scrollbar-thumb {
        -webkit-border-radius: .5rem;
        border-radius: .5rem;
        background: @black_haze;
        -webkit-box-shadow: inset 0 0 .5rem rgba(204 , 204 , 204 , 0.75);
    }

    &::-webkit-scrollbar-thumb:window-inactive {
        background: @border-grey;
    }
}

.custom-blue-scroll {
    &::-webkit-scrollbar {
	    width: 1.6rem;
	    background-color: @white;
    }

    &::-webkit-scrollbar-track {
	    -webkit-box-shadow: inset 0 0 6px rgba(0,0,0,0.3);
	    background-color: #F5F5F5;
	    border-radius: 10px;
    }

    &::-webkit-scrollbar-thumb {
	    border-radius: 10px;
	    background-image: -webkit-gradient(linear,
									       left bottom,
									       left top,
									       color-stop(0.44, rgb(122,153,217)),
									       color-stop(0.72, rgb(73,125,189)),
									       color-stop(0.86, rgb(28,58,148)));
    }

}

.card-shadow {
    -moz-box-shadow: 0 2px 5px 0 rgba(0,0,0,.16), 0 2px 10px 0 rgba(0,0,0,.12);
    -webkit-box-shadow: 0 2px 5px 0 rgba(0,0,0,.16), 0 2px 10px 0 rgba(0,0,0,.12);
    box-shadow: 0 2px 5px 0 rgba(0,0,0,.16), 0 2px 10px 0 rgba(0,0,0,.12);
}

.card-hover-shadow {
    -moz-box-shadow: 0px 0px 1px 2px #3fa9f5, 0px 0px 0px 2px rgba(63, 169, 245, 0.25);
    -webkit-box-shadow: 0px 0px 1px 2px #3fa9f5, 0px 0px 0px 2px rgba(63, 169, 245, 0.25);
    box-shadow: 0px 0px 1px 2px #3fa9f5, 0px 0px 0px 2px rgba(63, 169, 245, 0.25);
}

.hoverable-card {
    .card-shadow;
    &:hover {
        .card-hover-shadow
    }
}


.app {
    height: 100%;
    color: #555;
}
.navigator {
    min-height: 6.4rem;
    height: 6.4rem;
    &.navbar {
        border: none;
        -webkit-box-shadow: none;
        box-shadow: none;
        background-color:@dark-blue;
        margin:0;
        border-radius:0;
        color:@white;
        height: 6.4rem;
        .navbar-header {
            .navbar-toggle {
                border-color: @white;
                .icon-bar {
                    background-color: @white;
                }
            }
            .navbar-brand {
                color:@white;
                max-height: 6.4rem;
                display: block;
                padding:0;
                margin-left: 0;
                img {
                    height: 100%;
                }
            }
        }

        ul.navbar-right {
            height: 6.4rem;
            margin: 0;
            li {
                display:flex;
                align-items:center;
                height: 100%;
                a {
                    color:@white;
                    padding: 1.5rem;
                    font-size:1.6rem;
                }
                select {
                    background-size: 5%;
                    background-image: url(data:image/svg+xml;utf8;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0iaXNvLTg4NTktMSI/Pgo8IS0tIEdlbmVyYXRvcjogQWRvYmUgSWxsdXN0cmF0b3IgMTYuMC4wLCBTVkcgRXhwb3J0IFBsdWctSW4gLiBTVkcgVmVyc2lvbjogNi4wMCBCdWlsZCAwKSAgLS0+CjwhRE9DVFlQRSBzdmcgUFVCTElDICItLy9XM0MvL0RURCBTVkcgMS4xLy9FTiIgImh0dHA6Ly93d3cudzMub3JnL0dyYXBoaWNzL1NWRy8xLjEvRFREL3N2ZzExLmR0ZCI+CjxzdmcgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB4bWxuczp4bGluaz0iaHR0cDovL3d3dy53My5vcmcvMTk5OS94bGluayIgdmVyc2lvbj0iMS4xIiBpZD0iQ2FwYV8xIiB4PSIwcHgiIHk9IjBweCIgd2lkdGg9IjE2cHgiIGhlaWdodD0iMTZweCIgdmlld0JveD0iMCAwIDI5Mi4zNjIgMjkyLjM2MiIgc3R5bGU9ImVuYWJsZS1iYWNrZ3JvdW5kOm5ldyAwIDAgMjkyLjM2MiAyOTIuMzYyOyIgeG1sOnNwYWNlPSJwcmVzZXJ2ZSI+CjxnPgoJPHBhdGggZD0iTTI4Ni45MzUsNjkuMzc3Yy0zLjYxNC0zLjYxNy03Ljg5OC01LjQyNC0xMi44NDgtNS40MjRIMTguMjc0Yy00Ljk1MiwwLTkuMjMzLDEuODA3LTEyLjg1LDUuNDI0ICAgQzEuODA3LDcyLjk5OCwwLDc3LjI3OSwwLDgyLjIyOGMwLDQuOTQ4LDEuODA3LDkuMjI5LDUuNDI0LDEyLjg0N2wxMjcuOTA3LDEyNy45MDdjMy42MjEsMy42MTcsNy45MDIsNS40MjgsMTIuODUsNS40MjggICBzOS4yMzMtMS44MTEsMTIuODQ3LTUuNDI4TDI4Ni45MzUsOTUuMDc0YzMuNjEzLTMuNjE3LDUuNDI3LTcuODk4LDUuNDI3LTEyLjg0N0MyOTIuMzYyLDc3LjI3OSwyOTAuNTQ4LDcyLjk5OCwyODYuOTM1LDY5LjM3N3oiIGZpbGw9IiNGRkZGRkYiLz4KPC9nPgo8Zz4KPC9nPgo8Zz4KPC9nPgo8Zz4KPC9nPgo8Zz4KPC9nPgo8Zz4KPC9nPgo8Zz4KPC9nPgo8Zz4KPC9nPgo8Zz4KPC9nPgo8Zz4KPC9nPgo8Zz4KPC9nPgo8Zz4KPC9nPgo8Zz4KPC9nPgo8Zz4KPC9nPgo8Zz4KPC9nPgo8Zz4KPC9nPgo8L3N2Zz4K);
                    width: 20rem;
                    max-width: 25rem;
                }
            }
        }
    }

}
.page-content {
    display: inline;
    width: 100%;
    min-height: 100%;
    position: absolute;
    top:6.4rem;
}

.sidebar {
    position: fixed;
    /*z-index: 0;
    left: 0;
    top: 0;
    bottom: 0;*/
    float:left;
    width: 32rem;
    background-color: @black_haze;
    color: #f8f8f8;
    border-right: 1px solid transparent;
    height: 100%;
    overflow-y:auto;
    .custom_scroll;
    .logo {
        align-items: center;
        font-weight: 700;
        display: flex;
        width: 100%;
        padding: 2rem 4.5rem;
        .brand-img {
            width:7.5rem;
            height:7.5rem;
            background:@white;
            display: flex;
            justify-content: center;
            align-items: center;
            font-size: 2rem;
            color: #ccc;
            border: 1px solid #ddd;
            img {
                width:100%;
            }
        }
        p {
            color: @dark-blue;
            font-weight: 700;
            font-size: 1.6rem;
            text-transform: uppercase;
            padding: 1rem;
        }
    }
    .sidebar-nav {
        padding: 0;
        margin: 0;
        list-style: none;
        li a {
            display: block;
            color: #304799;
            text-decoration: none;
            cursor: pointer;
            position: relative;
            border-top: 1px solid transparent;
            padding: 2rem 4.5rem;
            line-height: 1.6rem;
            font-weight: 700;
            font-size: 1.6rem;
            text-transform: uppercase;
            &.active {
                color:@white;
                background-color: @light-blue;
            }
        }
    }

    .sidebar-footer {
        position: absolute;
        bottom: 0;
        left: 0;
        right: 0;
        padding: 10px;
        background-color: @black_haze;
    }
}

.content-wrap {
    overflow: auto;
    position: absolute;
    z-index: 1;
    left: 0;
    width: 100%;
    transition: left .5s;
    background:@light_alice_blue;
        
    main {
        nav.filters {
             margin-top: 7rem;
             .container-fluid {
                padding: 0 4.5rem;
                .fiter-section {
                    margin: 0 0 2.5rem 0;
                    button.btn.btn-primary-green {
                        font-size: 1.4rem;
                        padding: .3rem 4rem;
                        margin-left: 2rem;
                        &.ng-hide-remove {
                            -webkit-animation:fadein .5s;
                             animation:fadein .5s;
                        }

                        &.ng-hide-add,.ng-hide {
                            -webkit-animation:fadeout 0;
                             animation:fadeout 0;
                        }
                    }

                    button.btn.btn-primary {
                        font-size: 1.4rem;
                        padding: .3rem 4rem;
                        position: absolute;
                        right: 0;
                        &.ng-hide-remove {
                            -webkit-animation:fadein .5s;
                             animation:fadein .5s;
                        }

                        &.ng-hide-add,.ng-hide {
                            -webkit-animation:fadeout 0;
                             animation:fadeout 0;
                        }
                    }

                    &.widget {
                        padding:1rem;
                    }
                    .btn-group {
                        &:first-child {
                            margin: 0 2rem;
                        }
                    }
                }
            }
        }
    }
    .content {
        min-height: 100%;
        padding: 0 4.5rem 4.5rem 4.5rem;
    }
}

.info-section {
    display:none;
    position: fixed !important;
    top: 6.4rem;
    right: 1.5rem;
    z-index: 99;
    .btn-group {
        &:first-child {
            margin: 0 2rem;
        }
        .fa.fa-angle-up{
            display:none;
        }
        &.open {
            .btn-primary-green {
                border-radius:0;
            }
            .fa.fa-angle-down{
                display:none;
            }
            .fa.fa-angle-up{
                display:block;
            }
        }
        .btn-primary-green {
            border-top-left-radius: 0;
            border-top-right-radius: 0;
            border-bottom-left-radius: 1rem;
            border-bottom-right-radius: 1rem;
            padding: .2rem 5rem;
            i.fa {
                position: absolute;
                right: 1rem;
                top: 25%;
            }
        }
        .dropdown-menu {
            margin: 0;
            min-width: 40rem;
            max-height:45rem;
            border-radius: 0;
            padding: 1rem 0;
            overflow-y:scroll;
            overflow-x:hidden;
            border: 1px solid #66afe9;
            .custom-blue-scroll;
            li.desc-section {
                padding: 1.5rem;
                border-bottom: 1px solid #66afe9;
                &:last-child {
                    border-bottom:none;
                    .list-group {
                        border-bottom: none;
                    }
                }
                
                h6 {
                    color:@dark-blue;
                    font-weight:500;
                    text-transform:uppercase;
                }
                .list-group {
                    margin: 0;
                    margin-bottom:2rem;
                    .list-group-item {
                        padding: 1rem 0;
                        border: none;
                        border-radius: 0;
                        border-bottom: 1px solid #dddddd;
                        margin: 0;
                        span.section-heading {
                            color:#000;
                        }
                        
                   h5.metric-stat {
                        color:@dark-blue;
                        font-weight:700;
                        font-size:2.4rem;
                        margin:0;
                        sup {
                            top:0;
                            font-size:1.4rem;
                            font-weight:700;
                            padding: 0 1rem;
                            &:before {
                                content: "/";
                                position: absolute;
                                left: .3rem;
                                color: @border-grey;
                                bottom: .8rem;
                                font-weight: 700;
                                font-size: 2rem;
                            }
                        }
                    }

                    .regular-grey {
                        font-weight:400;
                        font-size:1.5rem;
                        color:@regular_grey;
                    }

                    .success, .increment {
                        color:@light-green;
                    }
                    .error,.decrement {
                        color:@red;
                        font-weight:400;
                        font-size:1.5rem;
                    }
                    }
                }
            }
        }
    }
    /*.open > .dropdown-menu {
        -webkit-transform: scale(1, 1);
        transform: scale(1, 1);  
        opacity:1;
    }
  
    .dropdown-menu {
        opacity:.3;
        -webkit-transform-origin: top;
        transform-origin: top;
        -webkit-animation-fill-mode: forwards;  
        animation-fill-mode: forwards; 
        -webkit-transform: scale(1, 0);
        display: block; 
        transition: all 0.2s linear;
        -webkit-transition: all 0.2s linear;
    }*/
    
}

.nav-shown {
	.content-wrap {
		left: 32rem;
	}
}

.error-msg {
    color:#a94442;
}
.datepicker {
    color:@dark-blue;
    .btn-transparent {
        padding:0;
        position:relative;
        input[type="text"] {
            height: auto !important;
            max-width: 15rem;
            border: none;
            -webkit-box-shadow: none;
            font-size: 1.5rem;
            box-shadow: none;
            padding: .2rem 3rem;
            min-width: 15rem;
            text-align: left;
            background-color: transparent;
            font-weight: 700;
            color: @dark-blue;
            &:focus{
                outline:none;
                border:none;
                -webkit-box-shadow: none;
                box-shadow: none;
            }
            &.ng-invalid {
                border: 1px solid #a94442;
                border-radius: 3px;
            }
        }
        i.fa.fa-calendar {
            position: absolute;
            left: 1rem;
            top: .8rem;
            color: #929497;
        }
        i.fa.fa-angle-down {
            position: absolute;
            right: 1rem;
            top: .8rem;
            color:@regular_grey;
        }
    }
}

.multiselect-dropdown {
     display: inline-block;
     padding:0;
     margin:0;
     .btn-group.dropdown {
             margin: 0 0 0 2rem !important;
     }
    .btn-transparent {
        padding: .2rem 3.5rem .2rem 1.5rem;
        i.fa {
            position: absolute;
            right: 1.6rem;
            top: 30%;
            color:@regular_grey;
        }
    }
    .open>.dropdown-toggle {
        background-color:@light_alice_blue;
        -webkit-box-shadow: none;
        box-shadow: none;
    }

    .open>.dropdown-menu {
        display: block;
        max-height: 20rem;
        overflow: auto;
        padding: 0;
        li {
            border-bottom:1px solid @border-grey;
            a {
                display: block;
                padding: .3rem 1rem;
                clear: both;
                font-weight: 400;
                font-size:1.5rem;
                line-height: 1.846;
                color: @regular_grey;
                white-space: nowrap;
                &.selected,&:hover, &:focus {
                    font-weight:400;
                    color:@dark-grey;
                    background-color:@light_alice_blue;
                    outline:none;
                }
            }
        }
    }

    .glyphicon-uncheck {
        width: 1.4rem;
        height: 1.4rem;
        border: .1rem solid #cccccc;
        border-radius: .2rem;
        margin-right: 1rem;
        top: .2rem;
    }

    .glyphicon-ok {
        width: 1.4rem;
        height: 1.4rem;
        border: .1rem solid green;
        border-radius: .2rem;
        font-size: 10px;
        margin-right: 1rem;
        color: green;
        top: -.2rem;
    }
}

.rotate-transform (@value) {
    -ms-transform:rotate(@value);
    -moz-transform:rotate(@value);
    -o-transform:rotate(@value);
    -webkit-transform:rotate(@value);
     transform:rotate(@value);
}
.origin-transform (@value) {
    -ms-transform-origin:@value;
    -moz-transform-origin:@value;
    -o-transform-origin:@value;
    -webkit-transform-origin:@value;
    transform-origin:@value;
}

.skew-transform(@value) {
    -ms-transform:skew(@value);
    -moz-transform:skew(@value);
    -o-transform:skew(@value);
    -webkit-transform:skew(@value);
    transform:skew(@value);
}

.rotate-skew-transform(@rotate, @skew){
    @rotate1:@rotate - 90deg;
    @skew1:@skew - 90deg;
    -ms-transform:rotate(@rotate1) skew(@skew1);
    -moz-transform:rotate(@rotate1) skew(@skew1);
    -o-transform:rotate(@rotate1) skew(@skew1);
    -webkit-transform:rotate(@rotate1) skew(@skew1);
    transform:rotate(@rotate1) skew(@skew1);
}
.skew-90 {
    -ms-transform:rotate(0deg) skewY(-90deg);
    -moz-transform:rotate(0deg) skewY(-90deg);
    -o-transform:rotate(0deg) skewY(-90deg);
    -webkit-transform:rotate(0deg) skewY(-90deg);
    transform:rotate(0deg) skewY(-90deg);
}

.transition(@value) {
    -ms-transform:@value;
    -moz-transition:@value;
    -o-transition:@value;
    -webkit-transition:@value;
    transition:@value;
}

.create-donut-chart(@size, @width, @textSize: 3rem) {
    .donut-card {
        .donut-chart {
            position: relative;
            display: inline-block;
            -webkit-border-radius: 50%;
            -moz-border-radius: 50%;
            border-radius: 50%;
            overflow: hidden;
            width: @size;
            height: @size;
            background: white;
            margin: auto;
            -webkit-mask-image: -webkit-radial-gradient(circle, white, black); /* Added for Safari to make circular shape of div */
            -webkit-transform: translateZ(0); /* Added for Safari to make circular shape of div */
            .slice {
                position: absolute;
                width: 100%;
                height: 100%;
                top: -@size/2;
                left: @size/2;
                display: inline-block;
                .origin-transform(0 100%);
                .skew-90;
                &:hover {
                    
                }
            }
            
            .chart-center {
                top: @width;
                left: @width;
                width: @size - (@width * 2);
                height: @size - (@width * 2);
                background: white;
                position: absolute;
                border-radius: 50%;
                display: flex;
                align-items: center;
                justify-content: center;
                padding: 2rem;
                font-size: 1.6rem;
                font-weight: 700;
            }
        }
    }
}
.create-donut-chart(16rem, 2.5rem);

.widget {
    display: block;
    position: relative;
    background: #fff;
    border-radius: .3rem;
    border: .1rem solid @border-grey;
    margin: 0;

    &.widget-card {
        margin-bottom:2.5rem;
    }
    .widget-title {
        padding: 2rem 1.5rem;
        border-bottom: .1rem solid @border-grey;
        text-transform: inherit;
        font-size: 1.8rem;
        font-weight: 400;
        color:@regular_grey;
    }

    .widget-footer {
        padding: 0 1.5rem;
        font-size: 1.4rem;
        font-weight: 400;
        color:@regular_grey;
        .info-italic {
            font-style:italic;
        }
    }

    .no-margin {
        margin:0;
    }
    .align-left {
        text-align: left;
    }
    
    .align-right {
        text-align: right;
    }
    .metric-name {
        margin:0;
        margin-bottom: 5rem;
        font-size:1.4rem;
        font-weight:700;
        color:@dark-grey;
        text-transform:uppercase;
        span {
            text-transform:lowercase;
        }
    }

    .metric-icon {
        margin: 1rem auto;
        width: 7rem;
        height: 7rem;
        img {
            width: 100%;
        }
        &.donuts-overlay {
            display:none;
            width: 80px;
            height: 80px;
            //margin: 0 auto;
            position: absolute;
            //z-index: 9;
        }
    }

    .circle {
        margin:3rem auto;
        position: relative;
        text-align:center;
        
    }
    .circle-labellist {
        li {
            font-size: 1.5rem;
            font-weight: 400;
            color: @regular_grey;
            text-transform: capitalize;
        }
        .circle-label {
            width: 1rem;
            height: 1rem;
            display: inline-block;
            margin-right: 1rem;
            border-radius:50%;
        }
    }
    

    h5.metric-stat {
        color:@dark-blue;
        font-weight:700;
        font-size:2.4rem;
        margin:0;
        sup {
            top:0;
            font-size:1.4rem;
            font-weight:700;
            padding: 0 1rem;
            &:before {
                content: "/";
                position: absolute;
                left: .1rem;
                color: @border-grey;
                bottom: .8rem;
                font-weight: 700;
                font-size: 2rem;
            }
        }
    }

    .regular-grey {
        font-weight:400;
        font-size:1.5rem;
        color:@regular_grey;
    }

    .success, .increment {
        color:@light-green;
    }
    .error,.decrement {
        color:@red;
        font-weight:400;
        font-size:1.5rem;
    }

    .metric-stat-section {
        text-align: center;
        padding: 5rem 3rem;
        &:last-child:after{
            content: '';
            width: .05rem;
            height: 80%;
            position: absolute;
            top: 10%;
            background: rgba(204, 204, 204, 0.5);
            left: 0;
        }
        &:after {
            content: '';
            width: .1rem;
            height: 80%;
            position: absolute;
            top: 10%;
            background: rgba(204, 204, 204, 0.5);
            right: 0;
        }
    }

    .graph-metric-stat {
        text-align: center;
        padding: 2.5rem 3.5rem;
        border-right:0.1rem solid @border-grey;
        &:last-child{
            border-right:none;
        }
        .donut_spinner {
            min-height: 16rem;
            min-width: 16rem;
            &:before {
                content: '';
                box-sizing: border-box;
                width: 16rem;
                height: 16rem;
                border-radius: 50%;
                border: 20px solid #ccc;
                border-top-color: #333;
                animation: spinner 1s linear infinite;
                left: 7.8rem;
                top: 0rem;
                position: absolute;
            }

            .donuts-overlay{
                img {
                    animation: pulsate 1s ease-out infinite;
                }
            }
        }
        .circle-labellist {
            .list-inline {
                display: unset;
                margin:0;
            }
            min-height:2rem;
            display:block;
        }  
        &>div {
            background:#ffffff;
            h6.metric-name {
                margin-top: 1rem;
            }
        }  
        .graph-export {
            margin-right: -20px;
            margin-top: -20px;
            margin-bottom: 0;
        }
    }

    .graph-metric-location-stat{
        text-align: center;
        padding: 3.5rem;
        border-right:0.1rem solid @border-grey;
        &:last-child{
            border-right:none
        }
        table {
            tbody{
                tr {
                    td, th {
                        padding: 0 1rem;
                        h5.metric-stat {
                            display:block;
                        }
                    }
                }
            }
        }
        .graph-formaters {
            display: table;
            width: 100%;
            text-align: right;
            vertical-align: middle;
            .form-inline{
                display:table;
                width:100%;
                .form-group {
                    display: table-cell;
                    vertical-align: middle;
                    .radio{
                        .radio-inline {
                            font-size:1.5rem;
                            font-weight:400;
                            color:@regular_grey
                        }
                    }
                }
            }
            
        }

        .graph-container {
            margin : 3rem -3.5rem;
        }
    }

    .campaign-info {
        display: flex;
        align-items: center;
        padding: 2rem;
        border-bottom: .1rem solid @border-grey;
        .campaign-items {
            padding:2rem;
        }
    }

    .campaign-history {
        display: flex;
        align-items: center;
        padding: 2rem 5rem;
        font-size: 1.4rem;
        font-weight: 700;
        color: @dark-grey;
        animation:fadein 5s;
        table>tbody>tr>td,
        table>thead>tr>th {
            padding:0.5rem;
        }
        .metric-stat {
            color:@dark-grey;
            font-size: 2rem;
        }

        span.down-button {
            width: 3rem;
            height: 3rem;
            border: .1rem solid @border-grey;
            border-radius: 50%;
            display: flex;
            justify-content: center;
            align-items: center;
            color: #fff;
            background: @light-blue;
            font-weight: 700;
            font-size: 2rem;
        }
    }

    table {
        &.customer_loyalty_table {
            text-align: center;
            thead > tr > th {
                margin: 0;
                font-size: 1.4rem;
                font-weight: 700;
                color: #58595b;
                text-transform: uppercase;
                text-align: center;
            }
            tbody > tr > td > h5.metric-stat {
                font-size: 1.4rem;
            }
            div.table-separator {
                position: absolute;
                width: 100%;
                height: 33.9%;
                left: 0;
                top: 32.8%;
                background: #ffffff;
                display: flex;
                align-items: center;
                justify-content: center;
                border-top: 1px solid @border-grey;
                border-bottom: 1px solid @border-grey;
                span {
                    z-index:99999;
                }
                .metric-icon {
                    text-align: center;
                    z-index: 1; 
                    display: flex;
                    align-items: center;
                }
            }

            span.loyalty-label {
                margin-right: 1rem;
                display: inline-block;
                width: 1rem;
                height: 1rem;
                background: #3366cc;
                border-radius: 50%;
            }
        }
        &.location-metrics {
            width: 100%;
            tbody {
                tr {
                    td {
                        padding: 1.5rem;
                         width: 20%;
                        .metric-card {
                            .applyCreateBoxCss;
                            display: inline-block;
                            width: 100%;
                            text-align: center;
                            border-radius:2px;
                            cursor:pointer;
                            padding: 2rem 0;
                            p.regular-grey{
                                margin: 0;
                            }
                            .hoverable-card;
                            .metric-name {
                                margin:1rem 0;
                            }
                            .success {
                                color: @white;
                                background: @green;
                                margin: 1rem 0;
                            }
                            .error {
                                color: @white;
                                background: @red;
                                margin: 1rem 0;
                            }
                            .industy_comparision_stats {
                                transition: max-height .5s linear, opacity .5s step-end;
                                -webkit-transition: max-height .5s linear, opacity 1s step-end;
                                opacity: 1;
                                max-height: 5rem;
                                &.display-none {
                                    transition: max-height .5s linear, opacity .5s step-start;
                                    -webkit-transition: max-height .5s linear, opacity 1s step-start;
                                    opacity: 0;
                                    max-height: 0;
                                }
                            }
                            
                        }
                        label {
                            width: 100%;
                            margin: 0;

                            input[type="checkbox"] {
                                display:none;
                            }
                            input[type="checkbox"]:checked + .metric-card {
                                
                                .card-hover-shadow;
                                
                            }
                        }
                        
                    }
                }
            }
        }
    }

    
    .pscyco-graphics {
        margin: 0;
        display: inline-block;
        width: 100%;
        align-items: center;
        padding: 4rem;
        .row {
            padding:2rem 0;
            .circle-labellist .circle-label {
                border-radius:0;
            }
        }
        ul.list-inline.circle-labellist.no-margin {
            margin-top: 2rem;
        }
    }

    &.transparent-card {
        border: none;
        background: @light_alice_blue;
        position:relative;

        .graph-card {
            padding: 0;
            width: 48%;
            &:nth-child(odd) {
                margin-right:2%;
                &:last-of-type {
                    margin-right:0;
                    width: 100%;
                }
            }
            
             
            
            background: @white;
            margin-top:2%;
            .card-shadow;
            .graph-card-title {
                display: flex;
                align-items:center;
                width: 100%;
                padding: 1.5rem;
                color:@regular_grey;
                font-size:1.4rem;
                border-bottom:1px solid @border-grey;
                .graph-formaters {
                    &.right-aligned {
                        position:absolute;
                        right:1.5rem;
                    }
                    .form-inline{
                        display: table;
                        float: right;
                        .form-group {
                            display: table-cell;
                            vertical-align: middle;
                            .radio{
                                .radio-inline {
                                    font-size:1.5rem;
                                    font-weight:400;
                                    color:@regular_grey
                                }
                            }
                        }
                    }
            
                }
            }
            .graph-container {
                margin: 0;
                padding: 3.5rem 0;
            }
            .graph-card-content {
                
            }
        }

        .graph-stats-card {
            padding: 3rem;
            width: 49%;
            background: @white;
            margin-top:2%;
            &:nth-child(odd) {
                margin-right:2%;
            }
            .stats-info {
                display:inline-block;
                text-align:center;
                padding: 1.5rem;
                &:nth-child(odd) {
                    border-right:1px solid @border-grey;
                }
                h6.metric-name{
                    margin: 0 0 1rem;
                }

                h5.metric-stat {
                    margin: 0 0 2rem;
                }
            }
        }
    }

    .graph-container.cross-store {
        padding:3rem;
        min-height: 60.4rem;
        .button-container {
            height: 5rem;
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
            .btn.btn-primary-blue {
                position:absolute;
                &.ng-hide-remove {
                    -webkit-animation:fadein .5s;
                        animation:fadein .5s;
                }

                &.ng-hide-add,.ng-hide {
                    -webkit-animation:fadeout 0;
                        animation:fadeout 0;
                }
            }
        }
    }

}

.btn-transparent {
    min-width: 15rem;
    text-align: left;
    background-color:@transparent;
    font-size:1.5rem;
    font-weight:700;
    color:@dark-blue;
    outline: 0;
    background-image: none;
    -webkit-box-shadow: none;
    box-shadow: none;
    border:1px solid @border-grey;
    &:hover,&:focus,&:active {
        color:@dark-blue;
    }
}

.btn-primary-blue {
    font-size:1.4rem;
    font-weight: 400;
    color:@white;
    background-color:@light-blue;
    border-radius:0;
    &:hover,&:focus,&:active {
        color:@white;
        background-color: @light-blue;
        background-image: -webkit-radial-gradient(circle, @light-blue 10%, @light-blue 11%);
        background-image: -o-radial-gradient(circle, @light-blue 10%, @light-blue 11%);
        background-image: radial-gradient(circle, @light-blue 10%, @light-blue 11%);
        background-repeat: no-repeat;
        background-size: 1000%;
        -webkit-box-shadow: 2px 2px 2px rgba(0,0,0,0.3);
        box-shadow: 2px 2px 2px rgba(0,0,0,0.3);
    }
}

.btn-primary-green {
    font-size:1.4rem;
    font-weight: 400;
    color:@white;
    background-color:@green;
    &:hover,&:focus,&:active {
        background-color: #5ec023;
        background-image: -webkit-radial-gradient(circle, #5ec023 10%, #5ec023 11%);
        background-image: -o-radial-gradient(circle, #5ec023 10%, #5ec023 11%);
        background-image: radial-gradient(circle, #5ec023 10%, #5ec023 11%);
        background-repeat: no-repeat;
        background-size: 1000%;
        -webkit-box-shadow: 2px 2px 2px rgba(0,0,0,0.3);
        box-shadow: 2px 2px 2px rgba(0,0,0,0.3);
    }
}

@media screen and (min-width: 76.8rem) {
    .content-wrap {
        width: ~"calc(100% - 32rem)";
        left: 32rem;
    }
}

@media screen and (max-width: 76.8rem) {
    .page-controls .navbar-nav {
        position: relative;
        z-index: 1;
        margin: 0 0 0 15px;
    }
    .page-controls .navbar-brand {
        font-weight: 700;
        text-align: center;
    }
    .visible-xs {
        display: block!important;
    }
    .page-controls .navbar-toggle {
        float: left;
    }
    .graph-export {
        min-width: 4rem;
    }
}

h5.metric-notavailable {
    color:@dark-blue;
    font-weight:700;
    font-size:2rem;
    margin:0;
}

table.steps {
    width:100%;
    text-align: center;
    tr {
        td {
            h6.metric-name {
                margin:1rem 0 !important;
            }
            h5.metric-stat {
                margin:1rem 0 !important;
            }
        }
    }
    tr.tringualr-steps {
        overflow: hidden;
        width: 100%;
        list-style:none;
        td {
            padding: 0 .5rem 0 1rem;
            width:33%;
            span {
                background: #ddd;
                padding: .7rem 1rem;
                float: left;
                position: relative;
                width:100%;
                animation: fadein 2s;
                &::before {
                    content: "";
                    position: absolute;
                    top: 50%;
                    margin-top: -0.7rem;
                    border-width: .7rem 0 .7rem 1rem;
                    border-style: solid;
                    border-color: #ddd #ddd #ddd transparent;
                    left: -1rem;
                }
                &::after{
                  content: "";
                  position: absolute;
                  top: 50%; 
                  margin-top: -0.7rem;   
                  border-top: .7rem solid transparent;
                  border-bottom: .7rem solid transparent;
                  border-left: 1rem solid #ddd;
                  right: -1rem;
                }
                &.blue{
                    background:@light-blue;
                    &::before{
                        border-color: @light-blue @light-blue @light-blue transparent;
                    }
                    &::after{
                        border-left: 1rem solid @light-blue;
                    }
                }
                &.green{
                    background:@green;
                    &::before{
                        border-color: @green @green @green transparent;
                    }
                    &::after{
                        border-left: 1rem solid @green;
                    }
                }
                &.yellow{
                    background:yellow;
                    &::before{
                        border-color: yellow yellow yellow transparent;
                    }
                    &::after{
                        border-left: 1rem solid yellow;
                    }
                }
            }
        }
    }
}

.tile_count {
    margin: 2rem 0;
    color:#fff;
    .tile_stats_count {
            padding: 1rem 3rem;
        &.green_bg {
            background-color:@i2e1_green;
        }
        &.yellow_bg {
            background-color:@i2e1_yellow;
        }
        &.blue_bg {
            background-color:@i2e1_blue
        }
        
        span {
            font-size: 1.3rem;
            display: block;
        }

        .count {
            font-size: 3rem;
            font-weight: 700;
            &.bounceInText {
                display:block;
            }
        }

    }

    .tile_stats_count:first-child:before {
        border-left: 0;
    }

    .tile_stats_count:before {
        content: "";
        position: absolute;
        left: -1rem;
        height: 100%;
        border-left: 1rem solid #F5F5F5;
        top: 0;
    }
}

.btn-toolbar {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 0 0 3rem 0;
    &.right-header{
        padding: 0;
        float: right;
        width:35rem;
        .btn-group h3 {
            font-size: 2rem;
        }
        .btn-group .btn {
            padding: .5rem 1rem;
            font-size: 1rem
        }
    }
    .btn-group {
        margin:0 2rem;
        button {
            .fa {
                margin-right: 1rem;
            }
        }
                        
        h3{
            font-weight: 700;
            font-size: 2.4rem;
            margin: 0;
        }

    }
}

.graph-export {
    min-width: 4rem;
    .dropdown-menu {
        padding:2px;
        min-width: 100px;
        .divider {
            margin: 3.5px 0;
        }
    }
    .dropdown-menu>li>a {
        text-transform: capitalize;
        padding: 0 10px
    }
    .dropdown-toggle {
        /*visibility: hidden;*/
        font-size: 2.4rem;
        padding: 0 0.5rem;
        display: flex;
        justify-content: flex-end;
        font-weight: 400;
        color: #929497;
        align-items: center;
        &:hover, &:focus {
            text-decoration: none;
        }
        .glyphicon {
            &.glyphicon-download {
                top:0;
            }
        }
        
    }
}

.google-visualization-table-table {
    min-width:100%;
    font-size: 9pt !important;
    .cross-store-avg-datatable-header-tr {
        .cross-store-avg-datatable-header-td {
            display: table-cell;
            border: 1px solid #FFF;
            background-color: #167F92;
            color: #FFF;
            padding: 1rem 1.4rem;
            margin: .5rem 1rem;
            .google-visualization-table-sortind {
                color: #fff;
                //position: absolute;
                //right: 1rem;
                padding-left:1rem;
            }
        }
    }
    .cross-store-avg-datatable-body-td {
        display: table-cell;
        border: 1px solid #D9E4E6;
        padding: 1rem 1.4rem;
        margin: .5rem 1rem;
        min-width:15rem;
        color: #000;
        font-weight: 500;
        text-align: center;
        &:first-child {
            text-align: left;
        }
    }
}

.csc-avg-widget-title {
    .legend-avg-css {
        display: flex;
        align-items: center;
        font-size: 1.2rem;
        span {
            height: 16px;
            width: 16px;
            display: inline-block;
            border-radius: 4px;
                margin-right: 10px;
            &.green {
                background: #008000;
            }
            &.red {
                background: #b30000;
            }
            &.white {
                background: #ffffff;
                border: 1px solid;
            }
        }
    }

    .gradient-bar {
        height:20px;
        background: #004d40;
        background: -moz-linear-gradient(left, #004d40 0%, #b2dfdb 35%, #ffffff 50%, #ffcdd2 64%, #b71c1c 100%);
        background: -webkit-gradient(left top, right top, color-stop(0%, #004d40), color-stop(35%, #b2dfdb), color-stop(50%, #ffffff), color-stop(64%, #ffcdd2), color-stop(100%, #b71c1c));
        background: -webkit-linear-gradient(left, #004d40 0%, #b2dfdb 35%, #ffffff 50%, #ffcdd2 64%, #b71c1c 100%);
        background: -o-linear-gradient(left, #004d40 0%, #b2dfdb 35%, #ffffff 50%, #ffcdd2 64%, #b71c1c 100%);
        background: -ms-linear-gradient(left, #004d40 0%, #b2dfdb 35%, #ffffff 50%, #ffcdd2 64%, #b71c1c 100%);
        background: linear-gradient(to right, #004d40 0%, #b2dfdb 35%, #ffffff 50%, #ffcdd2 64%, #b71c1c 100%);
        filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#004d40', endColorstr='#b71c1c', GradientType=1 );
    }
        
}




