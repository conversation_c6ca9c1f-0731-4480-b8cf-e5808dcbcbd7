analyticsApp
.controller('appController', ['$rootScope', '$scope', '$state', 'service', 'ajaxCall', '$location', '$window', '$timeout', '$q', '$cookies', function ($rootScope, $scope, $state, service, ajaxCall, $location, $window, $timeout, $q, $cookies) {

    var yesterday = parseInt(new Date(Date.now() - 86400000).getTime() / 1000).toFixed(0);
    var fromday = moment(new Date((parseInt(new Date(Date.now()).getTime() / 1000).toFixed(0)) * 1000)).subtract(1, 'months');

    //$rootScope.selectedPartnerId = null;
    $rootScope.partnersForFilterList = [{ partnerId: 0, partnerCd: 0, partnerName: '-- All Brands --' }];

    $scope.$parent.filterFormData = {};
    $scope.$parent.filterFormData.startDate = new Date(fromday);
    $scope.$parent.filterFormData.endDate = new Date(yesterday * 1000);
    $scope.$parent.filterFormData.dateFormat = "dd-MM-yy";
    $scope.$parent.filterFormData.refreshFilter = false;

    $scope.$parent.filterOtions = {};
    $scope.$parent.filters = {};

    $scope.$parent.loggedInUser = {}
    $scope.$parent.filters.from = angular.copy($scope.$parent.filterFormData.startDate);//moment(fromday).format('MM/DD/YYYY');
    $scope.$parent.filters.to = angular.copy($scope.$parent.filterFormData.endDate);//moment(new Date(angular.copy($scope.$parent.filterFormData.endDate))).format('MM/DD/YYYY');
    $scope.$parent.filters.nasids = [];
    $scope.$parent.filters.cities = [];
    $scope.$parent.filters.sources = [1, 2];
    $scope.$parent.filters.partnerCode = null;
    $scope.$parent.filters.partnerId = null;
    $scope.$parent.filters.industries = [];

    $scope.$parent.data_captured = {};

    $scope.$parent.selectedLocationMetric = { row: null, column : 1 };

    $scope.locationsInsight = {};
    $scope.locationsInsight.chartDataObject = {};
    $scope.locationsInsight.stores = {};
    $scope.locationsInsight.industry = {};

    $scope.customersInsight = {};
    $scope.customersInsight.genderDistribution = donutDataObject.genderDistribution;
    $scope.customersInsight.ageDistribution = donutDataObject.ageDistribution;
    $scope.customersInsight.spendDistribution = donutDataObject.spendDistribution;

    $scope.crossStore = {};


    $scope.$parent.startDateOptions = {
        formatYear: 'yy',
        maxDate: new Date(),
        startingDay: 1,
        showWeeks: false
    };

    $scope.$parent.endDateOptions = {
        formatYear: 'yy',
        minDate: $scope.$parent.filterFormData.startDate,
        maxDate: new Date(),
        startingDay: 1,
        showWeeks: false
    };

    $rootScope.logout = function () {
        $cookies.remove('userId');
        service.logout();
    };

    $window.onbeforeunload = function () {
        $cookies.remove('userId');
        //service.logout();
    };;

    $rootScope.countKey = function (obj) {
        return Object.keys(obj).length;
    }

    $scope.loggedInUserData = service.whichUser().then(function (data) {
        if (data.userId && data.userId > 0) {
            $cookies.put('userId', data.userId);
            $scope.$parent.loggedInUser = data;
            if (data.name) {
                $scope.$parent.loggedInUser.name = data.name
                $scope.$parent.loggedInUser.userId = data.userId
            }
            else if (data.username) {
                $scope.$parent.loggedInUser.username = data.username
                $scope.$parent.loggedInUser.name = data.username
                $scope.$parent.loggedInUser.userId = data.userId
            }
            else {
                $scope.$parent.loggedInUser.userName = 'User';
                $scope.$parent.loggedInUser.name = 'User';
                $scope.$parent.loggedInUser.userId = data.userId
            }

            $rootScope.allowedFeatureList = data.features;
            $rootScope.isSuperAdmin = (data.userType == 2);
            $rootScope.isConfigAdminUser = (data.userType == 1) || $rootScope.isSuperAdmin;
            $rootScope.isSalesAdmin = (data.userType == 4) || $rootScope.isConfigAdminUser;
            $rootScope.isReadOnlyAdmin = (data.userType == 3) || $rootScope.isConfigAdminUser;
            $rootScope.isDistributorAdmin = (data.userType == 5) || false;
            Array.prototype.push.apply($rootScope.partnersForFilterList, data.partners);
        }
        return data;
    });


    $scope.userBrand = service.getUserBrand().then(function (brandId) {
        var deferred = $q.defer();
        var userBrand = { id:0, code:null };
        userBrand.id = brandId;
        $scope.loggedInUserData.then(function (user) {
            if (userBrand.id > 0) {
                $scope.$parent.filters.partnerId = userBrand.id;
                var selectedBrand = $rootScope.partnersForFilterList.find(function (brand) {
                    return brand.partnerId == userBrand.id;
                });
                userBrand.code = $scope.$parent.filters.partnerCode = selectedBrand.partnerCd;
                deferred.resolve(userBrand)
            } else {
                userBrand.id = $scope.$parent.filters.partnerId = $rootScope.partnersForFilterList[0].partnerId
                userBrand.code = $scope.$parent.filters.partnerCode = $rootScope.partnersForFilterList[0].partnerCd;
                service.setUserBrand({ partnerId: $scope.$parent.filters.partnerId });
                deferred.resolve(userBrand)
            }
            ajaxCall.post('/OneInsights/GetBrandDetails', $scope.$parent.filters).then(function (response) {
                if (response.data) {
                    $scope.brandDetails = response.data.data;
                }
            });
            $scope.getCitiesFilter();
        });

        return deferred.promise;
    });

    $scope.setBrand = function () {
        service.setUserBrand({ partnerId: $scope.$parent.filters.partnerId }).then(function () {
            window.location.reload();
        });
    }

    $scope.getCitiesFilter = function (filter) {
        return ajaxCall.get('/Client/GetFilterOptions?filter=shop_city').then(function (response) {
            $scope.$parent.filters.cities = response.data;
        });
    }

    $rootScope.featureEnabled = function (feature) {
        if ($rootScope.isConfigAdminUser) return 1;
        if ($rootScope.isSalesAdmin && feature == 23) return 1;
        if ($rootScope.isReadOnlyAdmin && feature == 23) return 1;
        if ($rootScope.allowedFeatureList) return $rootScope.allowedFeatureList[feature];
    }

    ajaxCall.get('/OneInsights/GetBrandIndustry').then(function (response) {
        $scope.$parent.filters.industries = [];
        if (response.data) {
            $scope.$parent.filters.industries.push(response.data.data.industry);
        } else {
            $scope.$parent.filters.industries.push('Retail');
        }
    });

    $scope.refreshParentFilter = function () {

        $scope.$parent.filterFormData.refreshFilter = true;
        $scope.$parent.data_captured = {};
        service.getPhoneDetectedInVicinity({
            queryObject: $scope.$parent.filters
        }, function (response) {
            $scope.$parent.data_captured.t_phone_detected_vicinity = response.data;
        });
        service.getPhoneDetectedInStore({
            queryObject: $scope.$parent.filters
        }, function (response) {
            $scope.$parent.data_captured.t_phone_detected_store = response.data;
        });
        service.getPhoneDetectedOnWiFi({
            queryObject: $scope.$parent.filters
        }, function (response) {
            $scope.$parent.data_captured.t_phone_detected_wifi = response.data;
        });

        
    }

    $scope.getMinDateForStores = function () {
        if ($scope.$parent.filters.nasids && $scope.$parent.filters.nasids.length > 0) {
            ajaxCall.post('/OneInsights/getMinDateForBrand', $scope.$parent.filters.nasids).then(function (response) {
                if (response.data) {
                    $scope.$parent.startDateOptions.minDate = response.data;
                }
            });
        }
    }

    $scope.applyFilters = function () {
        $scope.$parent.filterFormData.refreshFilter = false;
        //$scope.$parent.filters.from = moment(new Date($scope.$parent.filterFormData.startDate)).format('MM/DD/YYYY');
        //$scope.$parent.filters.to = moment(new Date($scope.$parent.filterFormData.endDate)).format('MM/DD/YYYY');

        $scope.$parent.filters.from = angular.copy($scope.$parent.filterFormData.startDate);
        $scope.$parent.filters.to = new Date(moment(angular.copy($scope.$parent.filterFormData.endDate)).add(1, 'days'));
        if (validateQueryObject($scope.$parent.filters)) {
            $rootScope.$broadcast('params-updated');
            window.dataLayer.push({
                event: 'applyFilters',
                userName: $scope.$parent.loggedInUser.name,
                attributes: {
                    // Return empty strings to prevent accidental inheritance of old data
                    'queryParameters': $scope.$parent.filters,
                }
            });
        }
    }

    $scope.getLocationsForCity = function () {
        $scope.userBrand.then(function () {
            if ($scope.$parent.filters.cities.length > 0) {
                $scope.$parent.allStores = [];
                $scope.$parent.filters.nasids = [];
                var qObj = angular.copy(locationQueryObject);
                if ($scope.$parent.filters.cities) {
                    qObj.city = '';

                    angular.forEach($scope.$parent.filters.cities, function (city) {
                        qObj.city += city + ',';
                    });
                    qObj.city = qObj.city.slice(0, -1)

                }

                if ($scope.$parent.filters.partnerId > 0) {
                    qObj.partnerId = $scope.$parent.filters.partnerId;
                }


                var deferred = $q.defer();

                qObj.storeTags = "%";
                service.getLocationsForAdmin(qObj).then(function (locations) {
                    $timeout(function () {
                        var arr = [];
                        $scope.$parent.allStores = locations.filter(function (store) {
                            if (store.storeName && store.nasid > 0) {
                                arr.push(store.nasid);
                                return true;
                            }
                        });
                        $scope.$parent.brandNases = arr;

                        if (arr.length > 0) {
                            ajaxCall.post('/OneInsights/getMinDateForBrand', $scope.$parent.brandNases).then(function (response) {
                                if (response.data) {
                                    //$scope.brandDate = response.data.data;
                                    $scope.$parent.startDateOptions.minDate = response.data;
                                }
                            });
                        }

                        //var newarray = $scope.$parent.allStores.filter(function (store) {
                        //    if ($scope.$parent.filters.partnerCode == null && store.partner != null && store.partner != "N/A") {
                        //        return true;
                        //    }
                        //});

                        //if (newarray.length > 0) {
                            //$scope.$parent.filters.partnerCode = newarray[0].partner;
                            //$scope.$parent.filters.partnerId = newarray[0].partnerId;
                            //$scope.$parent.filters.partnerId = $rootScope.selectedPartnerId;
                            //ajaxCall.post('/OneInsights/GetBrandDetails', $scope.$parent.filters).then(function (response) {
                            //    if (response.data) {
                            //        $scope.brandDetails = response.data.data;
                            //    }
                            //});
                        //}

                        $scope.$parent.filters.nasids = arr;
                        deferred.resolve($scope.$parent.filters.nasids);
                        if (!$scope.$parent.filterFormData.refreshFilter)
                            $rootScope.$broadcast('params-updated');
                    }, 500);

                });
                return deferred.promise;
            } else {
                $scope.$parent.filters.nasids = [];
            }
        });
    }

    $scope.getPercentChange = function (newFigure, oldFigure) {
        if (newFigure != null && newFigure > 0 && oldFigure != null)
            percentChange = (1 - oldFigure / newFigure) * 100;
        else
            percentChange = 0;
        return percentChange.round(1);
    }

    $scope.filterErrors = null;

    $scope.validateDates = function (id) {

        $scope.filterErrors = null;
        document.getElementById('filterEndDate').classList.remove('ng-invalid')
        document.getElementById('filterEndDate').classList.remove('ng-invalid')
        if (!$scope.$parent.filterFormData) return;
        var endDate = new Date($scope.$parent.filterFormData.endDate);
        var startDate = new Date($scope.$parent.filterFormData.startDate);
        if (startDate > endDate) {
            if (id == 'filterEndDate') {
                document.getElementById(id).className += ' ng-invalid';
                $scope.filterErrors = 'End date can not be less than start date';
                $scope.$parent.filterFormData.refreshFilter = false;
            }
            else if (id == 'filterStartDate') {
                document.getElementById(id).className += ' ng-invalid';
                $scope.filterErrors = 'Start date can not be greater than end date';
                $scope.$parent.filterFormData.refreshFilter = false;
            }
        } else if (angular.isDate($scope.$parent.filterFormData.startDate) == false) {
            $scope.filterErrors = 'Please enter a valid start date';
            $scope.$parent.filterFormData.refreshFilter = false;
        } else if (angular.isDate($scope.$parent.filterFormData.endDate) == false) {
            $scope.filterErrors = 'Please enter a valid end date';
            $scope.$parent.filterFormData.refreshFilter = false;
        }

        $scope.endDateOptions.minDate = $scope.$parent.filterFormData.startDate;
        
    
    }

    $rootScope.$on('$stateChangeSuccess', function () {
        window.dataLayer.push({
            event: 'pageView',
            userName: $scope.$parent.loggedInUser.name,
            pageView: $location.path(),
        });
    });

    $rootScope.googleDataTableToCSV = function (dataTable, metric) {
        var dt_cols = dataTable.data.cols;
        var dt_rows = dataTable.data.rows;

        var col_length = dt_cols.length;
        var row_length = dt_rows.length;

        var csv_cols = [];
        var csv_out;

        // Iterate columns
        for (var i = 0; i < col_length; i++) {
            // Replace any commas in column labels
            if (metric == "psychoGraphics" || dataTable.type == 'BarChart' || dataTable.type == 'Table') {
                csv_cols.push(dt_cols[i].label.replace(/,/g, ""));
            } else {
                if (i == 0 || i % 2 != 0) {
                    csv_cols.push(dt_cols[i].label.replace(/,/g, ""));
                }
            }

        }

        // Create column row of CSV
        csv_out = csv_cols.join(",") + "\r\n";

        // Iterate rows
        for (i = 0; i < row_length; i++) {
            var raw_col = [];
            for (var j = 0; j < col_length; j++) {

                //changes                
                if (dataTable.tsType == 'monthly') {
                    dt_rows[i]['c'][0]['v'] = moment(dt_rows[i]['c'][0]['v']).format("MMM");
                } else if (dataTable.tsType == 'daily') {
                    dt_rows[i]['c'][0]['v'] = moment(dt_rows[i]['c'][0]['v']).format("MMM DD YYYY");
                }
                //changes end
                if (metric == "psychoGraphics" || dataTable.type == 'BarChart' || dataTable.type == 'Table') {
                    if (dt_rows[i]['c'][j]) {
                        // Replace any commas in row values
                        if (dt_rows[i]['c'][j].hasOwnProperty('f') && dt_rows[i]['c'][j]['f'] != null && dt_rows[i]['c'][j]['f'] != '') {

                            raw_col.push(dt_rows[i]['c'][j]['f'].replace(/,/g, ""));
                        }
                        else {
                            if (typeof (dt_rows[i]['c'][j]['v']) == "string") {
                                raw_col.push(dt_rows[i]['c'][j]['v'].replace(/,/g, ""));
                            }
                            else {
                                raw_col.push(dt_rows[i]['c'][j]['v']);
                            }
                            //changes end
                        }
                    } else {
                        raw_col.push('');
                    }
                } else {
                    if (j == 0 || j % 2 != 0) { // filter colors code for lines
                        if (dt_rows[i]['c'][j]) {
                            // Replace any commas in row values
                            if (dt_rows[i]['c'][j].hasOwnProperty('f') && dt_rows[i]['c'][j]['f'] != null && dt_rows[i]['c'][j]['f'] != '') {

                                raw_col.push(dt_rows[i]['c'][j]['f'].replace(/,/g, ""));
                            }
                            else {
                                if (typeof (dt_rows[i]['c'][j]['v']) == "string") {
                                    raw_col.push(dt_rows[i]['c'][j]['v'].replace(/,/g, ""));
                                }
                                else {
                                    raw_col.push(dt_rows[i]['c'][j]['v']);
                                }
                                //changes end
                            }
                        } else {
                            raw_col.push('');
                        }
                    }
                }

            }
            // Add row to CSV text
            csv_out += raw_col.join(",") + "\r\n";
        }
        var metric_prefix = dataTable.tsType ? dataTable.tsType : 'average';
        var filename = metric_prefix + '_' + metric + '.csv'
        downloadCSV(csv_out, filename);
        return csv_out;
    }

    $rootScope.saveAsImg = function (chartContainerId, tsType, metric) {
        var chartContainer = document.getElementById(chartContainerId);
        var imgData = getImgData(chartContainer);
        var fileName = tsType + '_' + metric + '.png'
        download(imgData, fileName, "image/png");
    }

    $rootScope.donutDataTableToCSV = function (dataTable, metric) {
        var csv_out = "";
        var header_col = [];
        var value_col = []
        switch (metric) {
            case ('ageData'):
                for (i = 0; i < dataTable.length; i++) {
                    header_col.push(dataTable[i]['age'].replace(/,/g, ""));
                    value_col.push(dataTable[i]['frequency']);
                }
                break;
            case ('genderData'):
                for (i = 0; i < dataTable.length; i++) {
                    header_col.push(dataTable[i]['gender'].replace(/,/g, ""));
                    value_col.push(dataTable[i]['frequency']);
                }
                break;
            case ('spendCapacityData'):
                for (i = 0; i < dataTable.length; i++) {
                    header_col.push(dataTable[i]['type'].replace(/,/g, ""));
                    value_col.push(dataTable[i]['frequency']);
                }
                break;
            case ('loyaltySegment'):
                for (i = 0; i < dataTable.length; i++) {
                    header_col.push(dataTable[i]['segment'].replace(/,/g, ""));
                    value_col.push(dataTable[i]['frequency']);
                }
                break;
            default:
                for (i = 0; i < dataTable.length; i++) {
                    header_col.push(dataTable[i]['name'].replace(/,/g, ""));
                    value_col.push(dataTable[i]['value']);
                }
                break;
        }
        // Add row to CSV text
        csv_out += header_col.join(",") + "\r\n";
        csv_out += value_col.join(",") + "\r\n";
        var metric_prefix = dataTable.tsType ? dataTable.tsType : 'average';
        var filename = metric_prefix + '_' + metric + '.csv'
        downloadCSV(csv_out, filename);
    }

    $rootScope.saveScreenshotAsImg = function (chartContainerId, tsType, metric) {
        var chartContainer = document.getElementById(chartContainerId);
        var fileName = tsType + '_' + metric + '.png'
        //var filter = function (node) {
        //    return (node.tagName !== 'button');
        //}
        domtoimage.toPng(chartContainer, { /*filter: filter, height: 720, width: 1024 */ })
        .then(function (dataUrl) {
            download(dataUrl, fileName, "image/png");
        })
        .catch(function (error) {
            console.error('oops, something went wrong!', error);
        });
    }

    $scope.placeDonutIcons = function (chartWrapper) {
        var cli = chartWrapper.getChart().getChartLayoutInterface();
        var chartArea = cli.getChartAreaBoundingBox();
        chartWrapper.getContainerId().parentElement.querySelector('.donuts-overlay').style.top = Math.floor(chartArea.height / 2) + 9 /*+ Math.floor(cli.getChartAreaBoundingBox().top*/ + "px";
        chartWrapper.getContainerId().parentElement.querySelector('.donuts-overlay').style.left = Math.floor(chartArea.width / 2 + cli.getChartAreaBoundingBox().left) - 11 + "px";
        chartWrapper.getContainerId().parentElement.querySelector('.donuts-overlay').style.display = "block";
    };

    $scope.placeWifiDonutIcons = function (chartWrapper) {
        var cli = chartWrapper.getChart().getChartLayoutInterface();
        var chartArea = cli.getChartAreaBoundingBox();
        chartWrapper.getContainerId().parentElement.querySelector('.donuts-overlay').style.top = Math.floor(chartArea.height / 2) + 29 /*+ Math.floor(cli.getChartAreaBoundingBox().top*/ + "px";
        chartWrapper.getContainerId().parentElement.querySelector('.donuts-overlay').style.left = Math.floor(chartArea.width / 2 + cli.getChartAreaBoundingBox().left) - 11 + "px";
        chartWrapper.getContainerId().parentElement.querySelector('.donuts-overlay').style.display = "block";
    };

    var locationMetricGraphDataUrls = {
        vicinity_footfall: {
            store: '/OneInsights/GetVicinityFootfall',
            brand: '/OneInsights/GetBrandsVicinityFootfall',
            industry: '/OneInsights/GetIndustryVicinityFootfall'
        },
        instore_footfall: {
            store: '/OneInsights/GetInStoreFootfall',
            brand: '/OneInsights/GetBrandsInStoreFootfall',
            industry: '/OneInsights/GetIndustryInStoreFootfall'
        },
        conversion_rate: {
            store: '/OneInsights/GetStoreConversionRate',
            brand: '/OneInsights/GetBrandsConversionRate',
            industry: '/OneInsights/GetIndustryConversionRate'
        },
        dwell_time: {
            store: '/OneInsights/GetStoreDwellTime',
            brand: '/OneInsights/GetBrandsDwellTime',
            industry: '/OneInsights/GetIndustryDwellTime'
        },
        repeat_rate: {
            store: '/OneInsights/GetStoreRepeatRate',
            brand: '/OneInsights/GetBrandsRepeatRate',
            industry: '/OneInsights/GetIndustryRepeatRate'
        }
    }

    $scope.getDailyDataColorObject = function (tsType, dateLabel, default_color) {
        var obj = {};
        if (default_color == '#1f5076') {
            if (tsType == "daily" && (moment(dateLabel).format('dddd') == 'Saturday' || moment(dateLabel).format('dddd') == 'Sunday')) {
                obj.v = 'point { size: 3; shape-type: circle; visible:True; fill-color:#71c044;}'
            } else if (tsType == "daily" && $scope.locationsInsight.holidaysDates.indexOf(moment(dateLabel).format("YYYY-MM-DD")) > -1) {
                obj.v = 'point { size: 3; shape-type: circle; visible:True; fill-color:#ffd503;}'
            } else if (tsType == "daily" && $scope.locationsInsight.inCompleteDataDates.indexOf(dateLabel) > -1) {
                obj.v = incomplete_data_color
            } else {
                obj.v = default_color
            }
        } else {
            if (tsType == "daily" && (moment(dateLabel).format('dddd') == 'Saturday' || moment(dateLabel).format('dddd') == 'Sunday')) {
                obj.v = 'point { size: 3; shape-type: circle; visible:True; fill-color:#71c044;}'
            } else if (tsType == "daily" && $scope.locationsInsight.holidaysDates.indexOf(moment(dateLabel).format("YYYY-MM-DD")) > -1) {
                obj.v = 'point { size: 3; shape-type: circle; visible:True; fill-color:#ffd503;}'
            } else if (tsType == "daily" && $scope.locationsInsight.inCompleteDataDates.indexOf(dateLabel) > -1) {
                obj.v = incomplete_data_color
            } else {
                obj.v = default_color
            }
        }
        return obj;
    }

    $scope.creatGraphForLocationMetrics = function (metric) {
        $scope.$emit('masonry.reloaded');
        $scope.locationsInsight.chartDataObject[metric] = chartDataObject[metric];
        $scope.locationsInsight.chartDataObject[metric].data = {};
        var postData = {
            queryObject: angular.copy($scope.$parent.filters)
        };
        postData.queryObject.tsType = $scope.locationsInsight.chartDataObject[metric].tsType;
        if ($scope.locationsInsight.chartDataObject[metric].isVisible) {
            $scope.locationsInsight.chartDataObject[metric].data.cols = [];
            $scope.locationsInsight.chartDataObject[metric].data.rows = [];
            $scope.locationsInsight.chartDataObject[metric] = addColoumnInLineChartData($scope.locationsInsight.chartDataObject[metric]);
            
            if (metric == 'vicinity_footfall') {
                $scope.locationsInsight.chartDataObject[metric].options.vAxis = {
                    minValue: 0,
                    format: 'short',
                    textPosition: 'none'
                }
            } else if (metric == 'instore_footfall') {
                $scope.locationsInsight.chartDataObject[metric].options.vAxis = {
                    minValue: 0,
                    format: '#',
                    textPosition: 'none'
                }
            } else if (metric == 'conversion_rate') {
                $scope.locationsInsight.chartDataObject[metric].options.vAxis = {
                    minValue: 0,
                    maxValue: 5,
                    format: '#.#\'%\'',
                    textPosition: 'none'
                }
            } else if (metric == 'dwell_time') {
                $scope.locationsInsight.chartDataObject[metric].options.vAxis = {
                    minValue: 0,
                    textPosition: 'none'
                }
            } else if (metric == 'repeat_rate') {
                $scope.locationsInsight.chartDataObject[metric].options.vAxis = {
                    minValue: 0,
                    maxValue: 25,
                    format: '#.#\'%\'',
                    textPosition: 'none'
                }
            }

            ajaxCall.post(locationMetricGraphDataUrls[metric].store, postData).then(function (response) {
                if (response.data) {
                    var maxYaxis = 0;
                    var chartData = response.data.data.filter(function (obj) {
                        return obj.label != 255;
                    });

                    

                    if ($scope.locationsInsight.chartDataObject[metric].tsType == "daily") {
                        var chartDataMinMax = findMinMax(chartData, 'label');
                        $scope.locationsInsight.chartDataObject[metric].type = "LineChart";
                        $scope.locationsInsight.chartDataObject[metric].options.hAxis = {
                            format: 'd MMM',
                            minValue: chartDataMinMax == false ? new Date($scope.$parent.filters.from) : new Date(moment(chartDataMinMax[0])),
                            maxValue: chartDataMinMax == false ? new Date($scope.$parent.filters.to) : new Date(moment(chartDataMinMax[1])),
                            viewWindow: {
                                min: chartDataMinMax == false ? new Date($scope.$parent.filters.from) : new Date(moment(chartDataMinMax[0])),
                                max: chartDataMinMax == false ? new Date($scope.$parent.filters.to) : new Date(moment(chartDataMinMax[1])),
                            },
                            
                        }
                    } else if ($scope.locationsInsight.chartDataObject[metric].tsType == "monthly") {
                        var datapoint = new Date(chartData[0].label.substring(0, 4), chartData[0].label.substring(4, 6) - 1);
                        y = datapoint.getFullYear();
                        m = datapoint.getMonth();
                        $scope.locationsInsight.chartDataObject[metric].options.hAxis = {
                            gridlines: {
                                count: chartData.length + 2
                            },
                            minValue: new Date(y, m - 1, 1),
                            maxValue: new Date(y, m + chartData.length, 1)
                        }

                        $scope.locationsInsight.chartDataObject[metric].options.isStacked = false;

                        if (chartData.length <= 7) {
                            $scope.locationsInsight.chartDataObject[metric].type = "ColumnChart"
                            $scope.locationsInsight.chartDataObject[metric].options.bar = {
                                groupWidth: 60
                            }
                        } else {
                            $scope.locationsInsight.chartDataObject[metric].type = "LineChart"

                        }
                    } else {
                        $scope.locationsInsight.chartDataObject[metric].type = "ColumnChart"
                        $scope.locationsInsight.chartDataObject[metric].options.hAxis = {}
                    }

                    
                    var hTicks = [];
                    for (var i = 0, len = chartData.length; i < len; i++) {
                        obj = {};
                        obj.c = []
                        if ($scope.locationsInsight.chartDataObject[metric].tsType == "daily") {
                            obj.c.push({
                                'v': new Date(moment(chartData[i].label)) //new Date(moment(chartData[i].label))
                            });
                        } else if ($scope.locationsInsight.chartDataObject[metric].tsType == "weekly") {
                            var day_slot = day_slots.filter(function (obj) {
                                return obj.index == chartData[i].label;
                            });

                            obj.c.push({
                                'v': chartData[i].label,
                                'f': day_slot[0].value
                            });
                            hTicks.push({
                                v: chartData[i].label,
                                f: day_slot[0].value
                            });

                        } else if ($scope.locationsInsight.chartDataObject[metric].tsType == "monthly") {
                            obj.c.push({
                                'v': new Date(chartData[i].label.substring(0, 4), chartData[i].label.substring(4, 6) - 1)
                            });

                        } else {
                            var time_slot = time_slots.filter(function (obj) {
                                return obj.index == chartData[i].label;
                            });
                            obj.c.push({
                                'v': chartData[i].label,
                                'f': time_slot[0].value
                            });
                            hTicks.push({
                                v: chartData[i].label,
                                f: time_slot[0].value
                            });
                        }


                        if (metric == 'vicinity_footfall') {
                            obj.c.push({
                                'v': Math.ceil(chartData[i].value)
                            });
                        } else if (metric == 'instore_footfall') {
                            obj.c.push({
                                'v': Math.ceil(chartData[i].value)
                            });
                        } else if (metric == 'conversion_rate' || metric == 'repeat_rate') {
                            obj.c.push({
                                'v': parseFloat(chartData[i].value).round(1),
                                'f': parseFloat(chartData[i].value).round(1) + '%'
                            });

                            if (maxYaxis < parseFloat(chartData[i].value))
                                maxYaxis = Math.ceil(parseFloat(chartData[i].value));
                        } else if (metric == 'dwell_time') {
                            obj.c.push({
                                'v': parseFloat(chartData[i].value),
                                'f': parseFloat(chartData[i].value) + ' minutes'
                            });
                        }

                        //var tooltip_obj = $scope.getDailyDataColorObject($scope.locationsInsight.chartDataObject[metric].tsType, chartData[i].label, store_color)
                        //obj.c.push(tooltip_obj);

                        var clr_obj = $scope.getDailyDataColorObject($scope.locationsInsight.chartDataObject[metric].tsType, chartData[i].label, store_color)
                        obj.c.push(clr_obj);
                        
                        $scope.locationsInsight.chartDataObject[metric].data.rows.push(obj);
                    }

                    if ($scope.locationsInsight.chartDataObject[metric].tsType == "hourly" ||
                        $scope.locationsInsight.chartDataObject[metric].tsType == "weekly") {
                        $scope.locationsInsight.chartDataObject[metric].options.hAxis = {
                            ticks: hTicks
                        }
                    } else {
                        delete $scope.locationsInsight.chartDataObject[metric].options.hAxis.ticks;
                    }


                    var brandPostData = angular.copy(postData);
                    brandPostData.queryObject.nasids = $scope.$parent.brandNases;
                    ajaxCall.post(locationMetricGraphDataUrls[metric].brand, brandPostData).then(function (response) {
                        if (response.data) {
                            var chartData = response.data.data.filter(function (obj) {
                                return obj.label != 255;
                            });
                            for (var i = 0, len = chartData.length; i < len; i++) {
                                if ($scope.locationsInsight.chartDataObject[metric].data.rows[i] != undefined) {
                                
                                    if (metric == 'vicinity_footfall') {
                                        $scope.locationsInsight.chartDataObject[metric].data.rows[i].c.push({
                                            'v': Math.ceil(chartData[i].value)
                                        });
                                    } else if (metric == 'instore_footfall') {
                                        $scope.locationsInsight.chartDataObject[metric].data.rows[i].c.push({
                                            'v': Math.ceil(chartData[i].value)
                                        });
                                    } else if (metric == 'conversion_rate' || metric == 'repeat_rate') {
                                        $scope.locationsInsight.chartDataObject[metric].data.rows[i].c.push({
                                            'v': parseFloat(chartData[i].value).round(1),
                                            'f': parseFloat(chartData[i].value).round(1) + '%'
                                        });
                                    } else if (metric == 'dwell_time') {
                                        $scope.locationsInsight.chartDataObject[metric].data.rows[i].c.push({
                                            'v': parseFloat(chartData[i].value),
                                            'f': parseFloat(chartData[i].value) + ' minutes'
                                        });
                                    }
                                    //var tooltip_obj = $scope.getDailyDataColorObject($scope.locationsInsight.chartDataObject[metric].tsType, chartData[i].label, store_color)
                                    //obj.c.push(tooltip_obj);
                                    
                                    var clr_obj = $scope.getDailyDataColorObject($scope.locationsInsight.chartDataObject[metric].tsType, chartData[i].label, brand_color)
                                    $scope.locationsInsight.chartDataObject[metric].data.rows[i].c.push(clr_obj);
                                }
                            }
                        }
                        ajaxCall.post(locationMetricGraphDataUrls[metric].industry, brandPostData).then(function (newResponse) {
                            if (newResponse.data) {
                                var chartData = newResponse.data.data.filter(function (obj) {
                                    return obj.label != 255;
                                });
                                for (var i = 0, len = chartData.length; i < len; i++) {
                                    if ($scope.locationsInsight.chartDataObject[metric].data.rows[i]!= undefined) {
                                        if (metric == 'vicinity_footfall') {
                                            $scope.locationsInsight.chartDataObject[metric].data.rows[i].c.push({
                                                'v': Math.ceil(chartData[i].value)
                                            });
                                        } else if (metric == 'instore_footfall') {
                                            $scope.locationsInsight.chartDataObject[metric].data.rows[i].c.push({
                                                'v': Math.ceil(chartData[i].value)
                                            });
                                        } else if (metric == 'conversion_rate' || metric == 'repeat_rate') {
                                            $scope.locationsInsight.chartDataObject[metric].data.rows[i].c.push({
                                                'v': parseFloat(chartData[i].value).round(1),
                                                'f': parseFloat(chartData[i].value).round(1) + '%'
                                            });
                                        } else if (metric == 'dwell_time') {
                                            $scope.locationsInsight.chartDataObject[metric].data.rows[i].c.push({
                                                'v': parseFloat(chartData[i].value),
                                                'f': parseFloat(chartData[i].value) + ' minutes'
                                            });
                                        }
                                        //var tooltip_obj = $scope.getDailyDataColorObject($scope.locationsInsight.chartDataObject[metric].tsType, chartData[i].label, store_color)
                                        //obj.c.push(tooltip_obj);

                                        var clr_obj = $scope.getDailyDataColorObject($scope.locationsInsight.chartDataObject[metric].tsType, chartData[i].label, industry_color);
                                        $scope.locationsInsight.chartDataObject[metric].data.rows[i].c.push(clr_obj);
                                    }
                                }
                            }
                        });

                        
                    });
                    if (metric == 'conversion_rate' || metric == 'repeat_rate') {
                        $scope.locationsInsight.chartDataObject[metric].options.vAxis.maxValue = maxYaxis;
                    }
                }
                $scope.locationsInsight.chartDataObject[metric].options.hAxis.gridlines = {
                        color: '#ffffff'
                    }
                
                //if ($scope.$parent.filters.nasids.length == $scope.$parent.brandNases.length) {
                //    $scope.hideSeries($scope.$parent.selectedLocationMetric, metric)
                //} else if ($scope.$parent.filters.nasids.length != $scope.$parent.brandNases.length) {
                //    $scope.hideSeries($scope.$parent.selectedLocationMetric, metric)
                //}
            });
        } else {
            chartDataObject[metric].isVisible = true;
            delete $scope.locationsInsight.chartDataObject[metric];
        }
    }

    $scope.hideSeries = hideLocationMetricSeries;

    function hideLocationMetricSeries(selectedItem, chartType) {
        var col = selectedItem.column;

        if (selectedItem.row === null) {
            if ($scope.locationsInsight.chartDataObject[chartType].view.columns[col] == col){
                $scope.locationsInsight.chartDataObject[chartType].view.columns[col] = {
                    label: $scope.locationsInsight.chartDataObject[chartType].data.cols[col].label,
                    type: $scope.locationsInsight.chartDataObject[chartType].data.cols[col].type,
                    calc: function () {
                        return null;
                    }
                };
                if (col == 5) {
                    $scope.locationsInsight.chartDataObject[chartType].options.colors[col - 3] = '#cccccc';
                } else if (col == 3) {
                    $scope.locationsInsight.chartDataObject[chartType].options.colors[col - 2] = '#cccccc';
                } else {
                    $scope.locationsInsight.chartDataObject[chartType].options.colors[col - 1] = '#cccccc';
                }

            } else {
                $scope.locationsInsight.chartDataObject[chartType].view.columns[col] = col;
                if (col == 5) {
                    $scope.locationsInsight.chartDataObject[chartType].options.colors[col - 3] = $scope.locationsInsight.chartDataObject[chartType].options.defaultColors[col - 3];
                } else if (col == 3) {
                    $scope.locationsInsight.chartDataObject[chartType].options.colors[col - 2] = $scope.locationsInsight.chartDataObject[chartType].options.defaultColors[col - 2];
                } else {
                    $scope.locationsInsight.chartDataObject[chartType].options.colors[col - 1] = $scope.locationsInsight.chartDataObject[chartType].options.defaultColors[col - 1];
                }

            }
        }
    }

    
}])
.controller('overviewController', ['$rootScope', '$scope', 'service', 'messageService', 'ajaxCall', '$location', '$window', function ($rootScope, $scope, service, messageService, ajaxCall, $location, $window) {
    $scope.campaignInsight = {};
    //$scope.locationsInsight = {};
    //$scope.locationsInsight.chartDataObject = {};
    $scope.metric = {};
    $scope.locationsInsight.inCompleteDataDates = [];
    $scope.locationsInsight.holidaysDates = holidays;
    $scope.inCompleteDataDates = [];
    $scope.customersInsight.genderDistribution = donutDataObject.genderDistribution;
    $scope.customersInsight.ageDistribution = donutDataObject.ageDistribution;
    $scope.customersInsight.spendDistribution = donutDataObject.spendDistribution;
    $scope.customersInsight.demographics = {};
    
    $scope.holidaysDates = holidays;
    $scope.chartKey = {};
    $scope.chartsMetricsSelectionChange = function (chartkey) {
        if (chartkey == 'right_chart') {
            $scope.creatGraphForLocationMetrics($scope.chartKey.RchartMetrics);
        } else if (chartkey == 'left_chart') {
            $scope.creatGraphForLocationMetrics($scope.chartKey.LchartMetrics);
        }
    };
    
    $scope.$on('params-updated', function () {
        if (validateQueryObject($scope.$parent.filters)) {
            $scope.campaignInsight.data_captured = {};
            //$scope.locationsInsight.chartDataObject = {};
            $scope.inCompleteDataDates = [];
            $scope.customersInsight.demographics = {};
            $scope.chartKey.LchartMetrics = 'vicinity_footfall';
            $scope.chartKey.RchartMetrics = 'instore_footfall';
            var postData = {
                queryObject: $scope.$parent.filters
            };

            
            //ajaxCall.post('/OneInsights/GetActiveCampaign', postData).then(function (response) {
            //    $scope.campaignInsight.data_captured.t_active_campaign = response.data;
            //    if (response.data > 0) {
                    ajaxCall.post('/OneInsights/GetCustomersTargeted', postData).then(function (response) {
                        $scope.campaignInsight.data_captured.t_customer_targeted = response.data;
                    });
                    ajaxCall.post('/OneInsights/GetTotalConversionRate', postData).then(function (response) {
                        $scope.campaignInsight.data_captured.t_conversion_rate = response.data.round(2);
                    });
                    ajaxCall.post('/OneInsights/GetTotalRevenueEarned', postData).then(function (response) {
                        $scope.campaignInsight.data_captured.t_revenue_earned = response.data;
                    });
                //}
            //});

            ajaxCall.post('/OneInsights/GetDevicesLastDataPeriodDateRange', postData).then(function (response) {
                $scope.lasDataPeriodPostData = angular.copy(postData);
                $scope.lasDataPeriodPostData.queryObject.nasids = $scope.$parent.filters.nasids;
                if (response.data.data.from != "" && response.data.data.from != null && response.data.data.from != 'n/a')
                    $scope.lasDataPeriodPostData.queryObject.from = moment(response.data.data.from, 'MM/DD/YYYY HH:mm:ss a').format('MM/DD/YYYY'); //moment(response.data.data.from, 'MM/DD/YYYY').toDate();
                else
                    $scope.lasDataPeriodPostData.queryObject.from = null;
                if (response.data.data.to != "" && response.data.data.to != null && response.data.data.to != 'n/a')
                    $scope.lasDataPeriodPostData.queryObject.to = moment(response.data.data.to, 'MM/DD/YYYY HH:mm:ss a').format('MM/DD/YYYY'); //moment(response.data.data.to, 'MM/DD/YYYY').toDate();
                else
                    $scope.lasDataPeriodPostData.queryObject.to = null;

                $scope.lastDateRangeHtml = "Last Period:";
                $scope.lastDateRangeHtml += moment.utc($scope.lasDataPeriodPostData.queryObject.from, 'MM/DD/YYYY').format("Do MMM");
                $scope.lastDateRangeHtml += " To " + moment.utc($scope.lasDataPeriodPostData.queryObject.to, 'MM/DD/YYYY').format("Do MMM");

                if ($scope.lasDataPeriodPostData.queryObject.from
                    && $scope.lasDataPeriodPostData.queryObject.to
                    && $scope.campaignInsight.data_captured.t_active_campaign > 0) {
                    ajaxCall.post('/OneInsights/GetCustomersTargeted', $scope.lasDataPeriodPostData).then(function (response) {
                        if (response.data > 0)
                            $scope.campaignInsight.data_captured.t_customer_targeted_change = $scope.campaignInsight.data_captured.t_customer_targeted.percentChange(response.data);
                        else
                            $scope.campaignInsight.data_captured.t_customer_targeted_change = 0;
                    });

                    ajaxCall.post('/OneInsights/GetTotalConversionRate', $scope.lasDataPeriodPostData).then(function (response) {
                        if (response.data > 0)
                            $scope.campaignInsight.data_captured.t_conversion_rate_change = $scope.campaignInsight.data_captured.t_conversion_rate.percentChange(response.data);
                        else
                            $scope.campaignInsight.data_captured.t_conversion_rate_change = 0;
                    });
                    ajaxCall.post('/OneInsights/GetTotalRevenueEarned', $scope.lasDataPeriodPostData).then(function (response) {
                        if (response.data > 0)
                            $scope.campaignInsight.data_captured.t_revenue_earned_change = $scope.campaignInsight.data_captured.t_revenue_earned.percentChange(response.data);
                        else
                            $scope.campaignInsight.data_captured.t_revenue_earned_change = 0;
                    });
                }
            });

            ajaxCall.post('/OneInsights/GetInCompleteDataDates', {
                queryObject: $scope.$parent.filters
            }).then(function (response) {
                if (response.data && response.data.data && response.data.data.length > 0) {
                    angular.forEach(response.data.data, function (value, key) {
                        $scope.inCompleteDataDates.push(value)
                    })
                }
            });

            ajaxCall.post('/OneInsights/GetGenderDistribution', postData).then(function (response) {

                $scope.customersInsight.demographics.genderData = response.data.data;//for donutdatatable function
                $scope.customersInsight.genderDistribution = donutDataObject.genderDistribution;

                $scope.customersInsight.genderDistribution.data.rows = [];

                if (response.data && response.data.data) {
                    var genderArray = response.data.data;

                    for (var i = 0; i < genderArray.length; i++) {
                        var obj = {};
                        obj.c = [];
                        obj.c.push({
                            'v': genderArray[i].gender
                        });
                        obj.c.push({
                            'v': parseFloat(genderArray[i].frequency).round(1),
                            'f': parseFloat(genderArray[i].frequency).round(1) + ' %'
                        });

                        $scope.customersInsight.genderDistribution.data.rows.push(obj);
                    }
                }

            });


            ajaxCall.post('/OneInsights/GetAgeDistribution', postData).then(function (response) {

                $scope.customersInsight.demographics.ageData = response.data.data;
                $scope.customersInsight.ageDistribution = donutDataObject.ageDistribution;

                $scope.customersInsight.ageDistribution.data.rows = [];

                if (response.data && response.data.data) {
                    var ageArray = response.data.data;
                    for (var i = 0; i < ageArray.length; i++) {
                        var obj = {};
                        obj.c = [];
                        obj.c.push({
                            'v': ageArray[i].age
                        });
                        obj.c.push({
                            'v': parseFloat(ageArray[i].frequency).round(1),
                            'f': parseFloat(ageArray[i].frequency).round(1) + ' %'
                        });

                        $scope.customersInsight.ageDistribution.data.rows.push(obj);
                    }
                }

            });

            ajaxCall.post('/OneInsights/GetSpendCapacity', postData).then(function (response) {

                $scope.customersInsight.demographics.spendCapacityData = response.data.data;
                $scope.customersInsight.spendDistribution = donutDataObject.spendDistribution;
                $scope.customersInsight.spendDistribution.data.rows = [];

                if (response.data && response.data.data) {
                    var spendArray = response.data.data;
                    for (var i = 0; i < spendArray.length; i++) {
                        var obj = {};
                        obj.c = [];
                        obj.c.push({
                            'v': spendArray[i].type
                        });
                        obj.c.push({
                            'v': parseFloat(spendArray[i].frequency).round(1),
                            'f': parseFloat(spendArray[i].frequency).round(1) + ' %'
                        });

                        $scope.customersInsight.spendDistribution.data.rows.push(obj);
                    }
                }

            });

            $scope.chartsMetricsSelectionChange('left_chart');
            $scope.chartsMetricsSelectionChange('right_chart');
        }
    });

    $scope.viewCampaigns = function () {
        $scope.isCollapsed = !$scope.isCollapsed;
        $scope.fetchCampaignsReports();
    }

    $scope.fetchCampaignsReports = function () {
        $scope.campaignInsight.data_captured.t_prime_reports = {};
        $scope.campaignInsight.data_captured.t_prime_reports = {
            manual: {
                name: "Manual SMS",
                type: "1"
            },
            prime_scheduling: {
                name: "Prime Scheduling",
                type: "2"
            }
        };
        ajaxCall.post('/OneInsights/GetPrimeCampaignReports', { queryObject: $scope.$parent.filters }).then(function (response) {
            $scope.campaignInsight.data_captured.t_prime_reports = response.data;
        });
    }

    if (validateQueryObject($scope.$parent.filters)) {
        $rootScope.$broadcast('params-updated');
    }

    

}])
.controller('benefitsController', ['$rootScope', '$scope', 'ajaxCall', 'service', 'messageService', function ($rootScope, $scope, ajaxCall, service, messageService) {
    $scope.benfits = {};
    $scope.benfits.data_captured = {};
    $scope.formatDate = function (date) {
        var dateOut = moment(date).format('MMMM Do, YYYY');
        return dateOut;
    };

    var postData = {
        queryObject: $scope.$parent.filters
    };

    $scope.isCollapsed = false;

    $scope.$on('params-updated', function () {
        if (validateQueryObject($scope.$parent.filters)) {

            $scope.$parent.data_captured = {};
            service.getPhoneDetectedInVicinity({
                queryObject: $scope.$parent.filters
            }, function (response) {
                $scope.$parent.data_captured.t_phone_detected_vicinity = response.data;
            });
            service.getPhoneDetectedInStore({
                queryObject: $scope.$parent.filters
            }, function (response) {
                $scope.$parent.data_captured.t_phone_detected_store = response.data;
            });
            service.getPhoneDetectedOnWiFi({
                queryObject: $scope.$parent.filters
            }, function (response) {
                $scope.$parent.data_captured.t_phone_detected_wifi = response.data;
            });

            //ajaxCall.post('/OneInsights/GetActiveCampaign', postData).then(function (response) {
            //    $scope.benfits.data_captured.t_active_campaign = response.data;
            //    if (response.data > 0) {
                    ajaxCall.post('/OneInsights/GetCustomersTargeted', postData).then(function (response) {
                        $scope.benfits.data_captured.t_customer_targeted = response.data;
                    });
                    ajaxCall.post('/OneInsights/GetTotalConversionRate', postData).then(function (response) {
                        $scope.benfits.data_captured.t_conversion_rate = response.data.round(2);
                    });

                    ajaxCall.post('/OneInsights/GetTotalRevenueEarned', postData).then(function (response) {
                        $scope.benfits.data_captured.t_revenue_earned = response.data;
                    });
            //    }
            //});

            ajaxCall.post('/OneInsights/GetDevicesLastDataPeriodDateRange', postData).then(function (response) {
                $scope.lasDataPeriodPostData = angular.copy(postData);
                $scope.lasDataPeriodPostData.queryObject.nasids = $scope.$parent.filters.nasids;
                if (response.data.data.from != "" && response.data.data.from != null && response.data.data.from != 'n/a')
                    $scope.lasDataPeriodPostData.queryObject.from = moment(response.data.data.from, 'MM/DD/YYYY HH:mm:ss a').format('MM/DD/YYYY'); //moment(response.data.data.from, 'MM/DD/YYYY').toDate();
                else
                    $scope.lasDataPeriodPostData.queryObject.from = null;
                if (response.data.data.to != "" && response.data.data.to != null && response.data.data.to != 'n/a')
                    $scope.lasDataPeriodPostData.queryObject.to = moment(response.data.data.to, 'MM/DD/YYYY HH:mm:ss a').format('MM/DD/YYYY'); //moment(response.data.data.to, 'MM/DD/YYYY').toDate();
                else
                    $scope.lasDataPeriodPostData.queryObject.to = null;

                $scope.lastDateRangeHtml = "Last Period:";
                $scope.lastDateRangeHtml += moment.utc($scope.lasDataPeriodPostData.queryObject.from, 'MM/DD/YYYY').format("Do MMM");
                $scope.lastDateRangeHtml += " To " + moment.utc($scope.lasDataPeriodPostData.queryObject.to, 'MM/DD/YYYY').format("Do MMM");

                if ($scope.lasDataPeriodPostData.queryObject.from && $scope.lasDataPeriodPostData.queryObject.to) {
                    ajaxCall.post('/OneInsights/GetCustomersTargeted', $scope.lasDataPeriodPostData).then(function (response) {
                        if (response.data > 0)
                            $scope.benfits.data_captured.t_customer_targeted_change = $scope.benfits.data_captured.t_customer_targeted.percentChange(response.data);
                        else
                            $scope.benfits.data_captured.t_customer_targeted_change = 0;
                    });

                    ajaxCall.post('/OneInsights/GetTotalConversionRate', $scope.lasDataPeriodPostData).then(function (response) {
                        if (response.data > 0)
                            $scope.benfits.data_captured.t_conversion_rate_change = $scope.benfits.data_captured.t_conversion_rate.percentChange(response.data);
                        else
                            $scope.benfits.data_captured.t_conversion_rate_change = 0;
                    });

                    //if (newVal) {
                    ajaxCall.post('/OneInsights/GetTotalRevenueEarned', $scope.lasDataPeriodPostData).then(function (response) {
                        if (response.data > 0)
                            $scope.benfits.data_captured.t_revenue_earned_change = $scope.benfits.data_captured.t_revenue_earned.percentChange(response.data);
                        else
                            $scope.benfits.data_captured.t_revenue_earned_change = 0;
                    });
                    //}
                }
                //$scope.$watch('benfits.data_captured.t_customer_targeted', function (newVal, oldVal) {
                //    if (newVal) {
                //        
                //    }
                //});
                //$scope.$watch('benfits.data_captured.t_conversion_rate', function (newVal, oldVal) {
                //    if (newVal) {
                //        
                //    }
                //});
                //$scope.$watch('benfits.data_captured.t_revenue_earned', function (newVal, oldVal) {
                //    
                //});
            });

            if ($scope.isCollapsed) {
                $scope.fetchCampaignsReports();
            }
        }

    });

    $scope.viewCampaigns = function () {
        $scope.isCollapsed = !$scope.isCollapsed;
        $scope.fetchCampaignsReports();
    }

    $scope.fetchCampaignsReports = function () {
        $scope.benfits.data_captured.t_prime_reports = {};
        $scope.benfits.data_captured.t_prime_reports = {
            manual: {
                name: "Manual SMS",
                type: "1"
            },
            prime_scheduling: {
                name: "Prime Scheduling",
                type: "2"
            }
        };
        ajaxCall.post('/OneInsights/GetPrimeCampaignReports', postData).then(function (response) {
            $scope.benfits.data_captured.t_prime_reports = response.data;
        });
    }

    if (validateQueryObject($scope.$parent.filters)) {
        $rootScope.$broadcast('params-updated');
    }


}])
.controller('kycController', ['$rootScope', '$scope', 'ajaxCall', 'service', 'messageService', function ($rootScope, $scope, ajaxCall, service, messageService) {

    var postData = {
        queryObject: $scope.$parent.filters
    };

    $scope.Math = Math;
    $scope.customersInsight.genderDistribution = donutDataObject.genderDistribution;
    $scope.customersInsight.ageDistribution = donutDataObject.ageDistribution;
    $scope.customersInsight.spendDistribution = donutDataObject.spendDistribution;
    $scope.customersInsight.loyaltyDistribution = donutDataObject.loyaltyDistribution;
    $scope.$on('params-updated', function (event, args) {
        if (validateQueryObject($scope.$parent.filters)) {

            $scope.customersInsight.demographics = {};
            $scope.customersInsight.customer_loyalty = {};
            $scope.customersInsight.customer_loyalty_chartData = {};
            $scope.customersInsight.psychoGraphics = {};

            ajaxCall.post('/OneInsights/GetGenderDistribution', postData).then(function (response) {

                $scope.customersInsight.demographics.genderData = response.data.data;

                $scope.customersInsight.genderDistribution.data.rows = [];


                if (response.data && response.data.data) {
                    var genderArray = response.data.data;

                    for (var i = 0; i < genderArray.length; i++) {
                        var obj = {};
                        obj.c = [];
                        obj.c.push({
                            'v': genderArray[i].gender,
                            'f': genderArray[i].gender
                        });
                        obj.c.push({
                            'v': parseFloat(genderArray[i].frequency).round(1),
                            'f': parseFloat(genderArray[i].frequency).round(1) + ' %'
                        });

                        $scope.customersInsight.genderDistribution.data.rows.push(obj);
                    }
                }

            });

            ajaxCall.post('/OneInsights/GetAgeDistribution', postData).then(function (response) {
                $scope.customersInsight.demographics.ageData = response.data.data;
                $scope.customersInsight.ageDistribution.data.rows = [];


                if (response.data && response.data.data) {
                    var ageArray = response.data.data;
                    for (var i = 0; i < ageArray.length; i++) {
                        var obj = {};
                        obj.c = [];
                        obj.c.push({
                            'v': ageArray[i].age
                        });
                        obj.c.push({
                            'v': parseFloat(ageArray[i].frequency).round(1),
                            'f': parseFloat(ageArray[i].frequency).round(1) + ' %'
                        });

                        $scope.customersInsight.ageDistribution.data.rows.push(obj);
                    }
                }

            });

            ajaxCall.post('/OneInsights/GetSpendCapacity', postData).then(function (response) {

                $scope.customersInsight.demographics.spendCapacityData = response.data.data;
                $scope.customersInsight.spendDistribution.data.rows = [];


                if (response.data && response.data.data) {
                    var spendArray = response.data.data;
                    for (var i = 0; i < spendArray.length; i++) {
                        var obj = {};
                        obj.c = [];
                        obj.c.push({
                            'v': spendArray[i].type
                        });
                        obj.c.push({
                            'v': parseFloat(spendArray[i].frequency).round(1),
                            'f': parseFloat(spendArray[i].frequency).round(1) + ' %'
                        });

                        $scope.customersInsight.spendDistribution.data.rows.push(obj);
                    }
                }

            });

            

            //ajaxCall.post('/OneInsights/GetInstoreLoyalitySegStats', postData).then(function (response) {
            ajaxCall.post('/OneInsights/GetCustomersLoyalitySegStats', postData).then(function (response) {
                //var segArray = ["Loyal", "New", "Attriter"]
                $scope.customersInsight.customercolors = pieColor;
                var date_result = angular.copy(response.data.data);

                //var customer_loyalty_arr = date_result.map(function (o) {
                //    return o.customer_type;
                //});

                //angular.forEach(segArray, function (elem) {
                //    if (customer_loyalty_arr.indexOf(elem) == -1) {
                //        date_result.push({
                //            customer_type: elem,
                //            avg_instoe_footfall: 0,
                //            avg_spend_instore: 0,
                //            avg_spend_industry: 0,
                //            frequency: 0
                //        })
                //    }
                //});

                $scope.customersInsight.customer_loyalty = date_result;

                //var dataArray = [];
                //angular.forEach(response.data.data, function (elem, index) {
                //    var obj = {};
                //    obj.name = elem.customer_type;
                //    obj.value = elem.frequency;
                //    obj.color = colors[elem.customer_type];
                //    dataArray.push(obj);
                //});

                /*
                var customer_loyalty_chart_arr = dataArray.map(function (o) {
                    return o.name;
                });

                angular.forEach(segArray, function (elem) {
                    if (customer_loyalty_chart_arr.indexOf(elem) == -1) {
                        dataArray.push({
                            name: elem,
                            value: 0,
                            color: pieColor[dataArray.length],
                        })
                    }
                });
                 */

                $scope.customersInsight.loyaltyDistribution.data.rows = [];

                if (response.data && response.data.data) {
                    var loyaltyArray = response.data.data;
                    for (var i = 0; i < loyaltyArray.length; i++) {
                        var obj = {};
                        obj.c = [];
                        obj.c.push({
                            'v': loyaltyArray[i].customer_type
                        });
                        obj.c.push({
                            'v': parseFloat(loyaltyArray[i].frequency).round(1),
                            'f': parseFloat(loyaltyArray[i].frequency).round(1) + ' %'
                        });

                        $scope.customersInsight.loyaltyDistribution.data.rows.push(obj);
                    }
                }

                //$scope.customersInsight.customer_loyalty_chartData = dataArray;


                /*/ajaxCall.post('/OneInsights/GetIndustryLoyalitySegStats', postData).then(function (response) {
                    if (response.data) {
                        angular.forEach($scope.customersInsight.customer_loyalty, function (elem, index) {
                            var result = response.data.data.filter(function (obj) {
                                return obj.customer_type == elem.customer_type;
                            });
                            $scope.customersInsight.customer_loyalty[index].avg_spend_industry = (result.length > 0) ? result[0].avg_spend_industry : 0;
                        });
                    }
                });*/
            });

            ajaxCall.post('/OneInsights/GetPsychographics', postData).then(function (response) {
                if (response.data) {
                    $scope.customersInsight.psychoGraphics = {};

                    $scope.customersInsight.psychoGraphics.type = "BarChart";
                    /*$scope.customersInsight.psychoGraphics.formatters = {
                        pattern: [{
                            pattern: '#.##\'%\'',
                            fractionDigits: 1
                        }]
                    }*/
                    $scope.customersInsight.psychoGraphics.options = barChartOptions;
                    $scope.customersInsight.psychoGraphics.options.isStacked = 'percent';

                    $scope.customersInsight.psychoGraphics.options.vAxis = {
                        gridlines: {
                            count: 1,
                            color: 'transparent'
                        },
                        //format: '#.#\'%\''
                        //ticks:[]
                    }
                    $scope.customersInsight.psychoGraphics.options.hAxis = {
                        baselineColor: '#fff',
                        gridlineColor: '#fff',
                        textPosition: 'none',
                        gridlines: {
                            count: 1,
                            color: 'transparent'
                        },
                        maxValue: 1,
                        format: '#.#%'
                        //ticks: [],
                        //format: 'percent'
                    };
                    $scope.customersInsight.psychoGraphics.options.legend = {
                        position: 'bottom',
                        alignment: 'center',
                        textStyle: { fontSize: 10 }
                    };

                    $scope.customersInsight.psychoGraphics.data = {
                        "cols": []
                    };
                    $scope.customersInsight.psychoGraphics.data.rows = [];
                    var obj = {};
                    obj.c = []
                    var t = 0;
                    obj.c.push({
                        v: 'Devices Used',
                        f: 'Devices Used'
                    });
                    $scope.customersInsight.psychoGraphics.data.cols.push({
                        id: 'devices',
                        label: 'Devices Used',
                        type: 'string'
                    });
                    angular.forEach(response.data.data, function (elem, index) {
                        if (index == 9) {
                            $scope.customersInsight.psychoGraphics.data.cols.push({
                                id: 'other',
                                label: 'Other',
                                type: 'number'
                            });
                            t += parseInt(elem.frequency);
                        } else if (index < 9) {
                            $scope.customersInsight.psychoGraphics.data.cols.push({
                                id: elem.device,
                                label: elem.device,
                                type: 'number'
                            })
                            obj.c.push({
                                v: elem.frequency,
                                f: ''
                            });
                        } else if (index == (response.data.data.length - 1)) {
                            obj.c.push({
                                v: t,
                                f: ''
                            });
                            t += parseInt(elem.frequency);
                        } else if (index > 9) {
                            t += parseInt(elem.frequency);
                        }
                    });
                    $scope.customersInsight.psychoGraphics.data.rows.push(obj);

                }
            });

        }

    });

    if (validateQueryObject($scope.$parent.filters)) {
        $rootScope.$broadcast('params-updated');
    }

}])
.controller('kylController', ['$rootScope', '$scope', 'ajaxCall', 'service', 'messageService', function ($rootScope, $scope, ajaxCall, service, messageService) {
    $scope.metric = {};
    $scope.metric.get_industy_comparision = "0";
    //$scope.locationsInsight = {};
    //$scope.locationsInsight.chartDataObject = {};
    //$scope.locationsInsight.stores = {};
    //$scope.locationsInsight.industry = {};
    $scope.locationsInsight.locationMetricAvgData = {};

    //$scope.locationsInsight.locationMetricAvgData.vicinity_footfall = { label: "DAILY VICINITY FOOTFALL", icon: "images/location.svg", store: {}, industry: {} };
    //$scope.locationsInsight.locationMetricAvgData.instore_footfall = { label: "DAILY STORE FOOTFALL", icon: "images/feet2.svg", store: {}, industry: {} };
    $scope.locationsInsight.locationMetricAvgData.conversion_rate = { label: "WALK IN RATE", icon: "images/shopping2.svg", store: {}, industry: {} };
    $scope.locationsInsight.locationMetricAvgData.dwell_time = { label: "DWELL TIME", icon: "images/time.svg", store: {}, industry: {} };
    $scope.locationsInsight.locationMetricAvgData.repeat_rate = { label: "REPEAT RATE", icon: "images/repeat.svg", store: {}, industry: {} };

    $scope.locationsInsight.inCompleteDataDates = [];
    $scope.locationsInsight.holidaysDates = holidays;
    if ($scope.locationsInsight.hasOwnProperty("chartDataObject")) {
        if ($scope.locationsInsight.chartDataObject.hasOwnProperty("vicinity_footfall")) {
            $scope.locationsInsight.chartDataObject['vicinity_footfall'].tsType = 'daily';
            $scope.locationsInsight.chartDataObject['instore_footfall'].tsType = 'daily';
        }
    }
    var locationMetricAvgDataUrls = {
        vicinity_footfall: {
            store: '/OneInsights/GetAvgDailyVicinityFootfall',
            industry: '/OneInsights/GetAvgDailyVicinityFootfallForIndustry'
        },
        instore_footfall: {
            store: '/OneInsights/GetAvgDailyInStoreFootfall',
            industry: '/OneInsights/GetAvgDailyInStoreFootfallForIndustry'
        },
        conversion_rate: {
            store: '/OneInsights/GetAvgDailyConversionRate',
            industry: '/OneInsights/GetAvgDailyConversionRateForIndustry'
        },
        dwell_time: {
            store: '/OneInsights/GetAvgDailyDwellTime',
            industry: '/OneInsights/GetAvgDailyDwellTimeForIndustry'
        },
        repeat_rate: {
            store: '/OneInsights/GetAvgDailyRepeatRate',
            industry: '/OneInsights/GetAvgDailyRepeatRateForIndustry'
        }
    }

    var postData = {
        queryObject: $scope.$parent.filters
    };

    $scope.$on('params-updated', function (event, args) {
        $scope.metric.get_industy_comparision = "0";
        //$scope.locationsInsight = {};
        $scope.locationsInsight.chartDataObject = {};
        if (validateQueryObject($scope.$parent.filters)) {

            
            $scope.creatGraphForLocationMetrics('vicinity_footfall');
            $scope.creatGraphForLocationMetrics('instore_footfall');

            angular.forEach($scope.locationsInsight.locationMetricAvgData, function (value, key) {
                ajaxCall.post(locationMetricAvgDataUrls[key].store, postData).then(function (response) {
                    if (response.data && response.data > 0) {
                        $scope.locationsInsight.locationMetricAvgData[key].store.current = response.data.round(1);
                    } else {
                        $scope.locationsInsight.locationMetricAvgData[key].store.current = 'n/a';
                    }
                });
            });

            ajaxCall.post('/OneInsights/GetInCompleteDataDates', {
                queryObject: $scope.$parent.filters
            }).then(function (response) {
                if (response.data && response.data.data) {
                    angular.forEach(response.data.data, function (value, key) {
                        $scope.locationsInsight.inCompleteDataDates.push(value)
                    })
                }
            });

            ajaxCall.post('/OneInsights/GetDevicesLastDataPeriodDateRange', { queryObject: $scope.$parent.filters }).then(function (response) {
                $scope.lasDataPeriodPostData = angular.copy({ queryObject: $scope.$parent.filters });
                $scope.lasDataPeriodPostData.queryObject.nasids = $scope.$parent.filters.nasids;
                if (response.data.data.from != "" && response.data.data.from != 'n/a')
                    $scope.lasDataPeriodPostData.queryObject.from = moment(response.data.data.from, 'MM/DD/YYYY HH:mm:ss a').format('MM/DD/YYYY'); //moment(response.data.data.from, 'MM/DD/YYYY').toDate();
                else
                    $scope.lasDataPeriodPostData.queryObject.from = null;
                if (response.data.data.to != "" && response.data.data.to != 'n/a')
                    $scope.lasDataPeriodPostData.queryObject.to = moment(response.data.data.to, 'MM/DD/YYYY HH:mm:ss a').format('MM/DD/YYYY'); //moment(response.data.data.to, 'MM/DD/YYYY').toDate();
                else
                    $scope.lasDataPeriodPostData.queryObject.to = null;

                if ($scope.lasDataPeriodPostData.queryObject.from != null && $scope.lasDataPeriodPostData.queryObject.to != null) {
                    $scope.lastDateRangeHtml = "Last Period:";
                    $scope.lastDateRangeHtml += moment.utc($scope.lasDataPeriodPostData.queryObject.from, 'MM/DD/YYYY').format("Do MMM");
                    $scope.lastDateRangeHtml += " To " + moment.utc($scope.lasDataPeriodPostData.queryObject.to, 'MM/DD/YYYY').format("Do MMM");

                    angular.forEach($scope.locationsInsight.locationMetricAvgData, function (value, key) {
                        $scope.$watch($scope.locationsInsight.locationMetricAvgData[key].store.current, function (newVal, oldVal) {
                            if (validateQueryObject($scope.lasDataPeriodPostData.queryObject)) {
                                ajaxCall.post(locationMetricAvgDataUrls[key].store, $scope.lasDataPeriodPostData).then(function (response) {
                                    if (response.data && response.data > 0) {
                                        $scope.locationsInsight.locationMetricAvgData[key].store.last_period = response.data;
                                        $scope.locationsInsight.locationMetricAvgData[key].store.change = $scope.locationsInsight.locationMetricAvgData[key].store.current.percentChange(response.data).round(1)
                                    }
                                    else {
                                        $scope.locationsInsight.locationMetricAvgData[key].store.change = 'n/a';
                                    }
                                });
                            } else {
                                $scope.locationsInsight.locationMetricAvgData[key].store.change = 'n/a';
                            }
                        });
                    });

                } else {
                    $scope.lastDateRangeHtml = "Last period data unavailable for the date selection";
                }
            });

            /*
            ajaxCall.post('/OneInsights/GetStoresTopHour', postData).then(function (response) {
                $scope.locationsInsight.stores.top_hour_data = {};
                if (response.data && response.data.data) {
                    var resposneObject = response.data.data;
                    var result = time_slots.filter(function (obj) {
                        return obj.index == resposneObject.top_hour;
                    });

                    $scope.locationsInsight.stores.top_hour_data.top_hour = result[0].value;
                    $scope.locationsInsight.stores.top_hour_data.avg_walk_in = resposneObject.avg_walk_in;
                }
            });

            ajaxCall.post('/OneInsights/GetIndustryTopHour', postData).then(function (response) {
                $scope.locationsInsight.industry.top_hour_data = {};
                if (response.data && response.data.data) {
                    var resposneObject = response.data.data;
                    var result = time_slots.filter(function (obj) {
                        return obj.index == resposneObject.top_hour;
                    });

                    $scope.locationsInsight.industry.top_hour_data.top_hour = result[0].value;
                    $scope.locationsInsight.industry.top_hour_data.avg_walk_in = resposneObject.avg_walk_in;
                }
            });

            ajaxCall.post('/OneInsights/GetStoresTopDay', postData).then(function (response) {
                $scope.locationsInsight.stores.top_day_data = {};
                if (response.data && response.data.data) {
                    $scope.locationsInsight.stores.top_day_data.top_day = response.data.data.top_day;
                    $scope.locationsInsight.stores.top_day_data.avg_walk_in = response.data.data.avg_walk_in;
                }
            });

            ajaxCall.post('/OneInsights/GetIndustryTopDay', postData).then(function (response) {
                $scope.locationsInsight.industry.top_day_data = {};
                if (response.data && response.data.data) {
                    $scope.locationsInsight.industry.top_day_data.top_day = response.data.data.top_day;
                    $scope.locationsInsight.industry.top_day_data.avg_walk_in = response.data.data.avg_walk_in;
                }
            });
            */
            //if ($scope.locationsInsight.chartDataObject) {
            //    angular.forEach($scope.locationsInsight.chartDataObject, function (chartObject, metric) {
            //        if ($scope.locationsInsight.chartDataObject[metric] && $scope.locationsInsight.chartDataObject[metric].isVisible) {
            //            $scope.creatGraphForLocationMetrics(metric)
            //        }
            //    });
            //}
        }

    });

    if (validateQueryObject($scope.$parent.filters)) {
        $rootScope.$broadcast('params-updated');
    }

    $scope.getIndusrtyComparision = function () {
        if ($scope.metric.get_industy_comparision == "1") {
            var postDataForIndustry = angular.copy(postData);
            var lasDataPeriodPostDataForIndustry = angular.copy($scope.lasDataPeriodPostData);
            postDataForIndustry.queryObject.nasids = lasDataPeriodPostDataForIndustry.queryObject.nasids = $scope.$parent.brandNases;
            angular.forEach($scope.locationsInsight.locationMetricAvgData, function (value, key) {
                ajaxCall.post(locationMetricAvgDataUrls[key].industry, postDataForIndustry).then(function (response) {
                    if (response.data && response.data > 0) {
                        $scope.locationsInsight.locationMetricAvgData[key].industry.current = response.data.round(1);
                        if ($scope.lasDataPeriodPostData.queryObject.from != null && $scope.lasDataPeriodPostData.queryObject.to != null) {

                            ajaxCall.post(locationMetricAvgDataUrls[key].industry, lasDataPeriodPostDataForIndustry).then(function (new_response) {
                                if (new_response.data && new_response.data > 0) {
                                    $scope.locationsInsight.locationMetricAvgData[key].industry.last_period = new_response.data;
                                    $scope.locationsInsight.locationMetricAvgData[key].industry.change = $scope.locationsInsight.locationMetricAvgData[key].industry.current.percentChange(new_response.data).round(1)
                                } else {
                                    $scope.locationsInsight.locationMetricAvgData[key].industry.change = 'n/a';
                                }
                            });

                        } else {
                            $scope.locationsInsight.locationMetricAvgData[key].industry.change = 'n/a';
                        }
                    } else {
                        $scope.locationsInsight.locationMetricAvgData[key].industry.current = 'n/a';
                    }
                });
            });

        }
    }

    $scope.getLocationMetricsGraph = function ($event) {
        if ($event.target.id == 'vicinity_footfall') {
            $scope.creatGraphForLocationMetrics('vicinity_footfall');
        }
        if ($event.target.id == 'instore_footfall') {
            $scope.creatGraphForLocationMetrics('instore_footfall');
        }
        if ($event.target.id == 'conversion_rate') {
            $scope.creatGraphForLocationMetrics('conversion_rate');
        }
        if ($event.target.id == 'dwell_time') {
            $scope.creatGraphForLocationMetrics('dwell_time');
        }
        if ($event.target.id == 'repeat_rate') {
            $scope.creatGraphForLocationMetrics('repeat_rate');
        }
    }
}])
.controller('cscController', ['$rootScope', '$scope', '$document', 'service', 'ajaxCall', '$timeout', '$q', 'promise', function ($rootScope, $scope, $document, service, ajaxCall, $timeout, $q, promise) {
    var postData = {
        queryObject: $scope.$parent.filters
    };

    $scope.selectAvgDataType = 'vicinity_footfall';
    $scope.selectDistributionDataType = 'visit_distribution';

    $scope.$on('params-updated', function () {
        if (validateQueryObject($scope.$parent.filters)) {
            $scope.crossStore.averages = {};
            $scope.crossStore.distributions = {};
            $scope.getCrossStoreAvgDataForTable();
            $scope.getAveragesForCss($scope.selectAvgDataType);
            //$scope.getDistributionsForCss($scope.selectDistributionDataType);
            //$scope.crossStore.distributions[$scope.selectDistributionDataType] = {};
            //$scope.getIndustryDistributionsForCsc($scope.selectDistributionDataType);
            //$scope.getBrandsDistributionsForCsc($scope.selectDistributionDataType);
            //$scope.getStoresDistributionsForCsc($scope.selectDistributionDataType);
            $scope.getDistributionsChartDataForCscs();
        }
    });

    var crossStoreAvgDataUrls = {
        vicinity_footfall: { brand: "/OneInsights/GetAvgDailyVicinityFootfall", industry: "/OneInsights/GetAvgDailyVicinityFootfallForIndustry", store: "/OneInsights/GetVicinityFootfallForCsc" },
        instore_footfall: { brand: "/OneInsights/GetAvgDailyInStoreFootfall", industry: "/OneInsights/GetAvgDailyInStoreFootfallForIndustry", store: "/OneInsights/GetInstoreFootfallForCsc" },
        walk_in_rate: { brand: "/OneInsights/GetAvgDailyConversionRate", industry: "/OneInsights/GetAvgDailyConversionRateForIndustry", store: "/OneInsights/GetConversionRateForCsc" },
        avg_dwell_time: { brand: "/OneInsights/GetAvgDailyDwellTime", industry: "/OneInsights/GetAvgDailyDwellTimeForIndustry", store: "/OneInsights/GetAvgDwellTimeForCsc" },
        repeat_rate: { brand: "/OneInsights/GetAvgDailyRepeatRate", industry: "/OneInsights/GetAvgDailyRepeatRateForIndustry", store: "/OneInsights/GetRepeatRateForCsc" },
    }

    var crossStoreDistributionsDataUrls = {
        visit_distribution: "/OneInsights/GetVisitsDistributionsForCsc",
        dwell_time_distribution: "/OneInsights/GetDwellTimeForCsc",
        age_distribution: "/OneInsights/GetAgeDistributionForCsc",
        gender_distribution: "/OneInsights/GetGenderDistributionForCsc",
        spend_capacity_distribution: "/OneInsights/GetSpendCapacityDistributionForCsc"
    }
    

    $scope.getCrossStoreAvgDataForTable = function () {

        $scope.crossStoreAvgDataForTable = {
            type: "Table",
            options: {
                allowHtml: true,
                sortAscending: false,
                sort: 'enable',
                sortColumn: 1,
                cssClassNames: {
                    headerRow: 'cross-store-avg-datatable-header-tr',
                    headerCell: 'cross-store-avg-datatable-header-td',
                    tableCell: 'cross-store-avg-datatable-body-td'
                },
                //pageSize: 50
            },
            data: {},
            view: {
                columns: [0, 1, 2, 3, 4, 5]
            },
            CSCchartLoading : true
        }
        $scope.crossStoreAvgDataForTable.data.cols = [];
        $scope.crossStoreAvgDataForTable.data.rows = [];

        $scope.crossStoreAvgDataForTable.data.cols.push({ 'id': 'store', 'type': 'string', 'label': 'Store' });
        $scope.crossStoreAvgDataForTable.data.cols.push({ 'id': 'vicinity', 'type': 'number', 'label': 'Vicinity footfall' });
        $scope.crossStoreAvgDataForTable.data.cols.push({ 'id': 'instrore', 'type': 'number', 'label': 'Instore footfall' });
        $scope.crossStoreAvgDataForTable.data.cols.push({ 'id': 'conv_rate', 'type': 'number', 'label': 'Walk In Rate' });
        $scope.crossStoreAvgDataForTable.data.cols.push({ 'id': 'rep_rate', 'type': 'number', 'label': 'Repeat Rate' });
        $scope.crossStoreAvgDataForTable.data.cols.push({ 'id': 'time_instore', 'type': 'number', 'label': 'Dwell time' });
        var dataPoints = [];
        
        ajaxCall.post('/OneInsights/GetCrossStoresAvgDataMetrics', { queryObject: $scope.$parent.filters }).then(function (response) {
            var getPlaceHolderText = function (num, checkAvg) {
                var text = "";
                if (num >= 0 && checkAvg == null) {
                    text = '+ ' + Math.round(100 * ((num * 100) / 100)) + '%'
                } else if (num < 0 && checkAvg == null) {
                    text = '- ' + Math.round(100 * ((-num * 100) / 100)) + '%'
                } else if (checkAvg != null) {
                    text = 'Brand Average'
                }
                return text;
            }

            if (response.data) {
                dataPoints = response.data.data;
                if (dataPoints.length > 50) {
                    $scope.crossStoreAvgDataForTable.options.pageSize = 50;
                } else {
                    delete $scope.crossStoreAvgDataForTable.options.pageSize;
                }
                for (var i = 0; i < dataPoints.length; i++) {

                    //if (index == 0) {
                    obj = {};
                    obj.c = []
                    obj.c.push({
                        'v': dataPoints[i].store
                    });
                    obj.c.push({
                        'v': dataPoints[i].vicinity_avg != 1 ? Number(dataPoints[i].vicinity) : 0,
                        'f': getPlaceHolderText(Number(dataPoints[i].vicinity), dataPoints[i].vicinity_avg)
                    });
                    obj.c.push({
                        'v': dataPoints[i].instore_avg != 1 ? Number(dataPoints[i].instore) : 0,
                        'f': getPlaceHolderText(Number(dataPoints[i].instore), dataPoints[i].instore_avg)
                    });
                    obj.c.push({
                        'v': dataPoints[i].conv_rate_avg != 1 ? Number(dataPoints[i].conversion_rate) : 0,
                        'f': getPlaceHolderText(Number(dataPoints[i].conversion_rate), dataPoints[i].conv_rate_avg)
                    });
                    obj.c.push({
                        'v': dataPoints[i].rep_rate_avg != 1 ? Number(dataPoints[i].repeat_rate) : 0,
                        'f': getPlaceHolderText(Number(dataPoints[i].repeat_rate), dataPoints[i].rep_rate_avg)
                    });
                    obj.c.push({
                        'v': dataPoints[i].time_instore_avg != 1 ? Number(dataPoints[i].time_spent_instore) : 0,
                        'f': getPlaceHolderText(Number(dataPoints[i].time_spent_instore), dataPoints[i].time_instore_avg)
                    });
                    $scope.crossStoreAvgDataForTable.data.rows[i] = obj;
                }

                $scope.crossStoreAvgDataForTable.CSCchartLoading = false
            }

        });
    }

    $scope.formateBackGround = function (chartWrapper) {
        var dataTable = chartWrapper.getDataTable()
        
        var columnsLength = dataTable.getNumberOfColumns();
        var rowsLength = dataTable.getNumberOfRows();

        for (columnIndex = 1; columnIndex < columnsLength; columnIndex++) {
            var distinctColmunValues = dataTable.getDistinctValues(columnIndex);
            var maxColumnValue = arrayMax(distinctColmunValues);
            var minColumnValue = arrayMin(distinctColmunValues);
            for (rowIndex = 0; rowIndex < rowsLength; rowIndex++) {
                var cellValue = dataTable.getValue(rowIndex, columnIndex);
                var formattedValue = dataTable.getFormattedValue(rowIndex, columnIndex);
                if (cellValue < 0 && formattedValue != 'Brand Average') {
                    var scallingValue = Math.abs(cellValue) / Math.abs(minColumnValue);
                    var bgColor = shadeBlendConvert(scallingValue, '#ffcdd2', '#b71c1c')
                    dataTable.setProperty(rowIndex, columnIndex, 'style', 'background-color: ' + bgColor + ';')
                } else if (cellValue > 0 && formattedValue != 'Brand Average') {
                    var scallingValue = cellValue / maxColumnValue;
                    var bgColor = shadeBlendConvert(scallingValue, '#C8E6C9', '#1B5E20');
                    dataTable.setProperty(rowIndex, columnIndex, 'style', 'background-color: ' + bgColor + ';')
                } 
            }
        }

        var tableCells = $document.find('td').filter(function () {
            return angular.element(this).hasClass('cross-store-avg-datatable-body-td');
        });

        angular.forEach(tableCells, function (element, index) {
            $(element).find('span img').css({ height: '24px' });
        });

    }
    

    $scope.getAverageChartDataForCscs = function () {
        $scope.getAveragesForCss($scope.selectAvgDataType);
    }

    var brand_average = 0;
    var industry_average = 0;

    $scope.getAveragesForCss = function (elem) {

        $scope.crossStore.averages[elem] = {
            type: "BarChart",
            isVisible: true,
            options: {
                "colors": ["#304799", "#F3F3F3", "#00ADEE", "#75BF44"],
                "isStacked": "true",
                "legend": "none",
                "tooltip": {
                    "trigger": 'both',
                    "textStyle": {
                        "fontSize": 10
                    }
                },
                "vAxis": {
                    "format": 'short',
                    "textStyle": {
                        "fontSize": 7
                    }
                },
                "hAxis": {
                    "format": 'short',
                    "textStyle": {
                        "fontSize": 10,
                        "bold": true
                    },
                    "gridlines": {
                        //"count":4,
                        "color": "#f3f3f3"
                    },
                    "minValue": "0",
                    "viewWindow": {
                        "min": 0,
                        "max": 0
                    },
                    "textPosition" : 'none'
                },
                "bar": {
                    "groupWidth": 10
                },
                "chartArea": {},
                "seriesType": "bars",
                "series": {
                    1: {
                        "tooltip": false,
                        "enableInteractivity": false
                    },
                    2: {
                        "type": "line",
                        "lineWidth": 1
                    },
                    3: {
                        "type": "line",
                        "lineWidth": 1
                    }
                }
            },
            data: {},
            view: {
                columns: [0, 1, 2, 3, 4]
            }

        };
        if (elem == 'walk_in_rate') {
            $scope.crossStore.averages[elem].options.hAxis.minValue = 0;
            $scope.crossStore.averages[elem].options.hAxis.maxValue = 5;
            $scope.crossStore.averages[elem].options.hAxis.format = '#\'%\'';
        } else if (elem == 'repeat_rate') {
            $scope.crossStore.averages[elem].options.hAxis.minValue = 0;
            $scope.crossStore.averages[elem].options.hAxis.maxValue = 5;
            $scope.crossStore.averages[elem].options.hAxis.format = '#\'%\'';
        } else if (elem == 'avg_dwell_time') {
            $scope.crossStore.averages[elem].options.hAxis.minValue = 0;
        } else {
            $scope.crossStore.averages[elem].options.hAxis.format = 'short';


        }

        $scope.crossStore.averages[elem].data = {};

        var postData = {
            queryObject: $scope.$parent.filters
        };

        if ($scope.crossStore.averages[elem].isVisible) {

            $scope.crossStore.averages[elem].data.cols = [];
            $scope.crossStore.averages[elem].data.rows = [];

            $scope.crossStore.averages[elem].data.cols.push({
                'id': 'store',
                'type': 'string',
                'label': 'Store'
            });

            $scope.crossStore.averages[elem].data.cols.push({
                'id': elem,
                'type': 'number',
                'label': elem.split("_").join(" ").capitalize()
            });

            $scope.crossStore.averages[elem].data.cols.push({
                'id': '',
                'type': 'number',
                'label': ''
            });

            $scope.crossStore.averages[elem].data.cols.push({
                'id': 'brand',
                'type': 'number',
                'label': 'Brand'
            });

            $scope.crossStore.averages[elem].data.cols.push({
                'id': 'industry',
                'type': 'number',
                'label': 'Industry'
            });

            var brandPostData = angular.copy(postData);
            brandPostData.queryObject.nasids = $scope.$parent.brandNases;
            var maxHaxisValue = 0;

            ajaxCall.post(crossStoreAvgDataUrls[elem]['brand'], brandPostData).then(function (response) {
                if (response.data && response.data > 0) {
                    brand_average = response.data;
                    if (response.data > maxHaxisValue) {
                        maxHaxisValue = parseInt(response.data);
                    }
                } else {
                    brand_average = 0;
                }
                ajaxCall.post(crossStoreAvgDataUrls[elem]['industry'], brandPostData).then(function (response) {
                    if (response.data && response.data > 0) {
                        industry_average = response.data;
                        if (response.data > maxHaxisValue) {
                            maxHaxisValue = parseInt(response.data);
                        }
                    } else {
                        industry_average = 0;
                    }
                    ajaxCall.post(crossStoreAvgDataUrls[elem]['store'], postData).then(function (response) {
                        if (response.data) {
                            $scope.crossStore.averages.chartData = response.data.data;

                            if ($scope.crossStore.averages.chartData.length > 10) {
                                $scope.viewMoreAveragesFlag = true;
                                var len = 10;
                            } else {
                                $scope.viewMoreAveragesFlag = false;
                                $scope.viewLessAveragesFlag = false;
                                var len = $scope.crossStore.averages.chartData.length;
                            }

                            $scope.crossStore.averages[elem].data.rows.push({
                                c: [{
                                    v: ''
                                }, {
                                    v: 0
                                }, {
                                    v: 0
                                }, {
                                    v: brand_average
                                }, {
                                    v: industry_average
                                }]
                            });

                            var rowdata = []
                            for (var i = 0; i < len; i++) {

                                if (parseFloat($scope.crossStore.averages.chartData[i].count) > maxHaxisValue) {
                                    maxHaxisValue = parseFloat($scope.crossStore.averages.chartData[i].count);
                                }

                                obj = {};
                                obj.c = []

                                obj.c.push({
                                    'v': $scope.crossStore.averages.chartData[i].shop_name
                                });
                                if (elem == 'walk_in_rate' || elem == 'repeat_rate') {
                                    obj.c.push({
                                        'v': parseFloat($scope.crossStore.averages.chartData[i].count).round(1),
                                        'f': parseFloat($scope.crossStore.averages.chartData[i].count).round(1) + '%'
                                    });
                                    obj.c.push({
                                        'v': parseFloat($scope.crossStore.averages.chartData[0].count - $scope.crossStore.averages.chartData[i].count).round(1)
                                    });
                                    obj.c.push({
                                        'v': brand_average,
                                        'f': parseFloat(brand_average).round(1) + ' %'
                                    });

                                    obj.c.push({
                                        'v': industry_average,
                                        'f': parseFloat(industry_average).round(1) + ' %'
                                    });
                                } else if (elem == 'avg_dwell_time') {
                                    obj.c.push({
                                        'v': $scope.crossStore.averages.chartData[i].count,
                                        'f': $scope.crossStore.averages.chartData[i].count + ' min'
                                    });
                                    obj.c.push({
                                        'v': $scope.crossStore.averages.chartData[0].count - $scope.crossStore.averages.chartData[i].count
                                    });
                                    obj.c.push({
                                        'v': brand_average,
                                        'f': brand_average + ' min'
                                    });

                                    obj.c.push({
                                        'v': industry_average,
                                        'f': industry_average + ' min'
                                    });
                                } else {
                                    obj.c.push({
                                        'v': Math.ceil($scope.crossStore.averages.chartData[i].count)
                                    });
                                    obj.c.push({
                                        'v': $scope.crossStore.averages.chartData[0].count - $scope.crossStore.averages.chartData[i].count
                                    });
                                    obj.c.push({
                                        'v': brand_average
                                    });

                                    obj.c.push({
                                        'v': industry_average
                                    });
                                }

                                $scope.crossStore.averages[elem].data.rows.push(obj);
                            }

                            $scope.crossStore.averages[elem].data.rows.push({
                                c: [{
                                    v: ''
                                }, {
                                    v: 0
                                }, {
                                    v: 0
                                }, {
                                    v: brand_average
                                }, {
                                    v: industry_average
                                }]
                            });

                        }
                        $scope.crossStore.averages[elem].options.hAxis.maxValue = maxHaxisValue;
                        //$scope.crossStore.averages[elem].options.hAxis.viewWindowMode = 'explicit';
                        $scope.crossStore.averages[elem].options.hAxis.viewWindow.min = '0';
                        $scope.crossStore.averages[elem].options.hAxis.viewWindow.max = maxHaxisValue;
                        $scope.crossStore.averages[elem].options.height = (12 * 30) + 100;
                        $scope.crossStore.averages[elem].options.chartArea.height = 12 * 30;
                        $scope.crossStore.averages[elem].options.chartArea.width = 600;
                    });

                });
            });
        } else {
            chartDataObject[elem].isVisible = true;
            delete $scope.crossStore.averages[elem];
        }
    }

    $scope.viewMoreAverages = function (elem) {
        $scope.crossStore.averages[elem].data.rows.splice(-1, 1);
        var rowdata = []
        for (var i = 10, len = $scope.crossStore.averages.chartData.length; i < len; i++) {

            obj = {};
            obj.c = []

            obj.c.push({
                'v': $scope.crossStore.averages.chartData[i].shop_name
            });
            if (elem == 'walk_in_rate' || elem == 'repeat_rate') {
                obj.c.push({
                    'v': parseFloat($scope.crossStore.averages.chartData[i].count).round(1),
                    'f': parseFloat($scope.crossStore.averages.chartData[i].count).round(1) + '%'
                });
                obj.c.push({
                    'v': parseFloat($scope.crossStore.averages.chartData[0].count - $scope.crossStore.averages.chartData[i].count).round(1)
                });
                obj.c.push({
                    'v': brand_average.round(1),
                    'f': parseFloat(brand_average).round(1) + ' %'
                });

                obj.c.push({
                    'v': industry_average.round(1),
                    'f': parseFloat(industry_average).round(1) + ' %'
                });
            } else if (elem == 'avg_dwell_time') {
                obj.c.push({
                    'v': $scope.crossStore.averages.chartData[i].count,
                    'f': $scope.crossStore.averages.chartData[i].count + ' min'
                });
                obj.c.push({
                    'v': $scope.crossStore.averages.chartData[0].count - $scope.crossStore.averages.chartData[i].count
                });
                obj.c.push({
                    'v': brand_average,
                    'f': brand_average + ' min'
                });

                obj.c.push({
                    'v': industry_average,
                    'f': industry_average + ' min'
                });
            } else {
                obj.c.push({
                    'v': $scope.crossStore.averages.chartData[i].count
                });
                obj.c.push({
                    'v': $scope.crossStore.averages.chartData[0].count - $scope.crossStore.averages.chartData[i].count
                });
                obj.c.push({
                    'v': brand_average,
                });

                obj.c.push({
                    'v': industry_average,
                });
            }
            $scope.crossStore.averages[elem].data.rows.push(obj);
            $scope.crossStore.averages[elem].options.height += 30;
            $scope.crossStore.averages[elem].options.chartArea.height += 30;

        }

        $scope.crossStore.averages[elem].data.rows.push({
            c: [{
                v: ''
            }, {
                v: 0
            }, {
                v: 0
            }, {
                v: brand_average
            }, {
                v: industry_average
            }]
        });

        $scope.viewMoreAveragesFlag = false;
        $scope.viewLessAveragesFlag = true;
    }

    $scope.viewLessAverages = function (elem) {
        $scope.crossStore.averages[elem].data.rows = $scope.crossStore.averages[elem].data.rows.slice(0, 11);
        $scope.crossStore.averages[elem].data.rows.push({
            c: [{
                v: ''
            }, {
                v: 0
            }, {
                v: 0
            }, {
                v: brand_average
            }, {
                v: industry_average
            }]
        });
        $scope.crossStore.averages[elem].options.height = (12 * 30) + 100;
        $scope.crossStore.averages[elem].options.chartArea.height = 12 * 30;
        $scope.viewMoreAveragesFlag = true;
        $scope.viewLessAveragesFlag = false;
    }

    $scope.crossStore.CSCchartLoading = true;

    $scope.getDistributionsChartDataForCscs = function () {
        $scope.crossStore.distributions = [];
        $scope.crossStore.distributions[$scope.selectDistributionDataType] = {};
        $scope.getIndustryDistributionsForCsc($scope.selectDistributionDataType);
        $scope.getBrandsDistributionsForCsc($scope.selectDistributionDataType);
        $scope.getStoresDistributionsForCsc($scope.selectDistributionDataType);
    }

    $scope.addColoumnInCSCChartData = function (obj, elem) {
        if (elem == 'visit_distribution') {
            obj.view = {
                columns: [0, 1, 2, 3, 4]
            };
            obj.data.cols.push({
                'id': 'store',
                'type': 'string',
                'label': 'Store'
            });

            obj.data.cols.push({
                'id': 'one_visit',
                'type': 'number',
                'label': '1 Visit'
            });

            obj.data.cols.push({
                'id': 'two_visits',
                'type': 'number',
                'label': '2 Visits'
            });

            obj.data.cols.push({
                'id': 'upto_four_visits',
                'type': 'number',
                'label': '2-4 Visits'
            });

            obj.data.cols.push({
                'id': 'four_plus_visits',
                'type': 'number',
                'label': '> 4 Visits'
            });
        } else if (elem == 'dwell_time_distribution') {
            obj.view = {
                columns: [0, 1, 2, 3, 4, 5]
            };
            obj.data.cols.push({
                'id': 'store',
                'type': 'string',
                'label': 'Store'
            });

            obj.data.cols.push({
                'id': 'upto_15',
                'type': 'number',
                'label': ' < 15 Minutes'
            });

            obj.data.cols.push({
                'id': 'upto_30',
                'type': 'number',
                'label': '15 - 30 Minutes'
            });

            obj.data.cols.push({
                'id': 'upto_60',
                'type': 'number',
                'label': '30 - 60 Minutes'
            });

            obj.data.cols.push({
                'id': 'upto_120',
                'type': 'number',
                'label': '1 - 2 Hours'
            });

            obj.data.cols.push({
                'id': 'more_120',
                'type': 'number',
                'label': '1 - 2 Hours'
            });
        } else if (elem == 'age_distribution') {
            obj.view = {
                columns: [0, 1, 2, 3, 4]
            };
            obj.data.cols.push({
                'id': 'store',
                'type': 'string',
                'label': 'Store'
            });

            obj.data.cols.push({
                'id': 'below_18',
                'type': 'number',
                'label': 'Below 18 years'
            });

            obj.data.cols.push({
                'id': 'upto_25',
                'type': 'number',
                'label': '18-25 years'
            });

            obj.data.cols.push({
                'id': 'upto_35',
                'type': 'number',
                'label': '25-35 years'
            });

            obj.data.cols.push({
                'id': 'above_35',
                'type': 'number',
                'label': 'Above 35 years'
            });
        } else if (elem == 'gender_distribution') {
            obj.view = {
                columns: [0, 1, 2]
            };
            obj.data.cols.push({
                'id': 'store',
                'type': 'string',
                'label': 'Store'
            });

            obj.data.cols.push({
                'id': 'female',
                'type': 'number',
                'label': 'Female'
            });

            obj.data.cols.push({
                'id': 'male',
                'type': 'number',
                'label': 'Male'
            });
        } else if (elem == 'spend_capacity_distribution') {
            obj.view = {
                columns: [0, 1, 2, 3]
            };
            obj.data.cols.push({
                'id': 'store',
                'type': 'string',
                'label': 'Store'
            });

            obj.data.cols.push({
                'id': 'Low',
                'type': 'number',
                'label': 'Low'
            });

            obj.data.cols.push({
                'id': 'Medium',
                'type': 'number',
                'label': 'Medium'
            });

            obj.data.cols.push({
                'id': 'High',
                'type': 'number',
                'label': 'High'
            });
        }
        return obj;
    }

    $scope.getIndustryDistributionsForCsc = function (elem, type) {

        $scope.crossStore.distributions[elem].industry = {
            type: "BarChart",
            isVisible: true,
            options: {
                "colors": ["#304799", "#00ADEE", "#75BF44", "#99DDFF", '#f8f398'],
                "defaultColors": ["#304799", "#00ADEE", "#75BF44", "#99DDFF", '#f8f398'],
                "isStacked": "percent",
                "legend": "none",
                "tooltip": {
                    "trigger": 'both',
                    "textStyle": {
                        "fontSize": 10
                    },
                    "text": "percentage"
                },
                "vAxis": {
                    "textStyle": { "fontSize": 7 },
                    "gridlines": {
                        "count": 1,
                        "color": "transparent"
                    },
                    "ticks": []
                },
                "hAxis": {
                    "baselineColor": "#fff",
                    "gridlineColor": "#fff",
                    "textPosition": "none",
                    "gridlines": {
                        "count": 1,
                        "color": "transparent"
                    },
                    "ticks": [],
                    "maxValue": 1,
                    "format": '#.#%'
                },
                "legend": "none",
                "bar": {
                    "groupWidth": 10
                },
                "chartArea": {}
            },
            data: {},
            view: {},
        };

        $scope.crossStore.distributions[elem].industry.options.isStacked = 'percent';

        $scope.crossStore.distributions[elem].industry.options.legend = {
            position: 'top',
            alignment: 'center',
            textStyle: {
                fontSize: 10, //size of the legend 
                bold: true
            }
        };

        $scope.crossStore.distributions[elem].industry.data = {};

        if ($scope.crossStore.distributions[elem].industry.isVisible) {

            $scope.crossStore.distributions[elem].industry.data.cols = [];
            $scope.crossStore.distributions[elem].industry.data.rows = [];

            $scope.crossStore.distributions[elem].industry = $scope.addColoumnInCSCChartData($scope.crossStore.distributions[elem].industry, elem);

            var rowdata = [];
            var postData = {
                queryObject: $scope.$parent.filters,
                type: 'industry'
            };

            ajaxCall.post(crossStoreDistributionsDataUrls[elem], postData).then(function (response) {
                if (response.data) {
                    //$scope.crossStore.distributions.chartData = response.data.data;
                    var result = response.data.data;
                    obj = {};
                    obj.c = []

                    obj.c.push({
                        'v': result[0].shop_name
                    });
                    if (elem == 'visit_distribution') {
                        obj.c.push({
                            'v': result[0].one_visit,
                            'f': ''
                        });
                        obj.c.push({
                            'v': result[0].two_visits,
                            'f': ''
                        });
                        obj.c.push({
                            'v': result[0].upto_four_visits,
                            'f': ''
                        });
                        obj.c.push({
                            'v': result[0].four_plus_visits,
                            'f': ''
                        });

                    } else if (elem == 'dwell_time_distribution') {
                        obj.c.push({
                            'v': result[0].upto_15,
                            'f': ''
                        });
                        obj.c.push({
                            'v': result[0].upto_30,
                            'f': ''
                        });
                        obj.c.push({
                            'v': result[0].upto_60,
                            'f': ''
                        });
                        obj.c.push({
                            'v': result[0].upto_120,
                            'f': ''
                        });
                        obj.c.push({
                            'v': result[0].more_120,
                            'f': ''
                        });

                    } else if (elem == 'age_distribution') {
                        obj.c.push({
                            'v': result[0].below_18,
                            'f': ''
                        });
                        obj.c.push({
                            'v': result[0].upto_25,
                            'f': ''
                        });
                        obj.c.push({
                            'v': result[0].upto_35,
                            'f': ''
                        });
                        obj.c.push({
                            'v': result[0].above_35,
                            'f': ''
                        });
                    } else if (elem == 'gender_distribution') {
                        obj.c.push({
                            'v': result[0].female,
                            'f': ''
                        });
                        obj.c.push({
                            'v': result[0].male,
                            'f': ''
                        });
                    } else if (elem == 'spend_capacity_distribution') {
                        obj.c.push({
                            'v': result[0].Low,
                            'f': ''
                        });
                        obj.c.push({
                            'v': result[0].Medium,
                            'f': ''
                        });
                        obj.c.push({
                            'v': result[0].High,
                            'f': ''
                        });
                    }

                    $scope.crossStore.distributions[elem].industry.data.rows.push(obj);

                }
                $scope.crossStore.distributions[elem].industry.options.chartArea.width = 600;
            });
        } else {
            chartDataObject[elem].isVisible = true;
            delete $scope.crossStore.distributions[elem];
        }


    }

    $scope.getBrandsDistributionsForCsc = function (elem, type) {
        $scope.crossStore.distributions[elem].brand = {
            type: "BarChart",
            isVisible: true,
            options: {
                "colors": ["#304799", "#00ADEE", "#75BF44", "#99DDFF", '#f8f398'],
                "defaultColors": ["#304799", "#00ADEE", "#75BF44", "#99DDFF", '#f8f398'],
                "isStacked": "percent",
                "legend": "none",
                "tooltip": {
                    "trigger": 'both',
                    "textStyle": {
                        "fontSize": 10
                    },
                    "text": "percentage"
                },
                "vAxis": {
                    "textStyle": { "fontSize": 7 },
                    "gridlines": {
                        "count": 1,
                        "color": "transparent"
                    },
                    "ticks": []
                },
                "hAxis": {
                    "baselineColor": "#fff",
                    "gridlineColor": "#fff",
                    "textPosition": "none",
                    "gridlines": {
                        "count": 1,
                        "color": "transparent"
                    },
                    "ticks": [],
                    "maxValue": 1,
                    "format": '#.#%'
                },
                "legend": "none",
                "bar": {
                    "groupWidth": 10
                },
                "chartArea": {}
            },
            data: {},
            view: {},
        };

        if ($scope.crossStore.distributions[elem].brand.isVisible) {

            $scope.crossStore.distributions[elem].brand.data.cols = [];
            $scope.crossStore.distributions[elem].brand.data.rows = [];

            $scope.crossStore.distributions[elem].brand = $scope.addColoumnInCSCChartData($scope.crossStore.distributions[elem].brand, elem);

            var rowdata = [];
            var postData = {
                queryObject: $scope.$parent.filters,
                type: 'brand'
            };
            ajaxCall.post(crossStoreDistributionsDataUrls[elem], postData).then(function (response) {
                if (response.data) {
                    result = response.data.data;
                    obj = {};
                    obj.c = []

                    obj.c.push({
                        'v': result[0].shop_name
                    });
                    if (elem == 'visit_distribution') {
                        obj.c.push({
                            'v': result[0].one_visit,
                            'f': ''
                        });
                        obj.c.push({
                            'v': result[0].two_visits,
                            'f': ''
                        });
                        obj.c.push({
                            'v': result[0].upto_four_visits,
                            'f': ''
                        });
                        obj.c.push({
                            'v': result[0].four_plus_visits,
                            'f': ''
                        });

                    } else if (elem == 'dwell_time_distribution') {
                        obj.c.push({
                            'v': result[0].upto_15,
                            'f': ''
                        });
                        obj.c.push({
                            'v': result[0].upto_30,
                            'f': ''
                        });
                        obj.c.push({
                            'v': result[0].upto_60,
                            'f': ''
                        });
                        obj.c.push({
                            'v': result[0].upto_120,
                            'f': ''
                        });
                        obj.c.push({
                            'v': result[0].more_120,
                            'f': ''
                        });

                    } else if (elem == 'age_distribution') {
                        obj.c.push({
                            'v': result[0].below_18,
                            'f': ''
                        });
                        obj.c.push({
                            'v': result[0].upto_25,
                            'f': ''
                        });
                        obj.c.push({
                            'v': result[0].upto_35,
                            'f': ''
                        });
                        obj.c.push({
                            'v': result[0].above_35,
                            'f': ''
                        });
                    } else if (elem == 'gender_distribution') {
                        obj.c.push({
                            'v': result[0].female,
                            'f': ''
                        });
                        obj.c.push({
                            'v': result[0].male,
                            'f': ''
                        });
                    } else if (elem == 'spend_capacity_distribution') {
                        obj.c.push({
                            'v': result[0].Low,
                            'f': ''
                        });
                        obj.c.push({
                            'v': result[0].Medium,
                            'f': ''
                        });
                        obj.c.push({
                            'v': result[0].High,
                            'f': ''
                        });
                    }

                    $scope.crossStore.distributions[elem].brand.data.rows.push(obj);


                }
                $scope.crossStore.distributions[elem].brand.options.chartArea.width = 600;
                $scope.crossStore.distributions[elem].brand.options.chartArea.top = 0;
            });
        } else {
            chartDataObject[elem].isVisible = true;
            delete $scope.crossStore.distributions[elem];
        }
    }

    var storeToBeShownOnLoad = 10;

    $scope.getStoresDistributionsForCsc = function (elem, type) {
        
        $scope.crossStore.distributions[elem].store = {
            type: "BarChart",
            isVisible: true,
            options: {
                "colors": ["#304799", "#00ADEE", "#75BF44", "#99DDFF", '#f8f398'],
                "defaultColors": ["#304799", "#00ADEE", "#75BF44", "#99DDFF", '#f8f398'],
                "isStacked": "percent",
                "legend": "none",
                "tooltip": {
                    "trigger":'both',
                    "textStyle": {
                        "fontSize": 10
                    },
                    "text": "percentage"
                },
                "vAxis": {
                    "textStyle": { "fontSize": 7 },
                    "gridlines": {
                        //"count": 1,
                        "color": "transparent"
                    },
                    "ticks": []
                },
                "hAxis": {
                    "baselineColor": "#fff",
                    "gridlineColor": "#fff",
                    "textPosition": "none",
                    "gridlines": {
                        //"count": 1,
                        "color": "transparent"
                    },
                    "ticks": [],
                   
                },
                "legend": "none",
                "bar": {
                    "groupWidth": 10
                },
                "chartArea": {}
            },
            data: {},
            view: {},
        };

        $scope.crossStore.distributions[elem].store.options.isStacked = 'percent';

        $scope.crossStore.distributions[elem].store.options.hAxis = {
            minValue: 0,
            ticks: [0, .25, .5, .75, 1],
            maxValue: 1,
            format: '#.#%',
            gridlines: {
                color: 'transparent'
            },
            textPosition: 'none'
        }

        if ($scope.crossStore.distributions[elem].store.isVisible) {

            $scope.crossStore.distributions[elem].store.data.cols = [];
            $scope.crossStore.distributions[elem].store.data.rows = [];

            $scope.crossStore.distributions[elem].store = $scope.addColoumnInCSCChartData($scope.crossStore.distributions[elem].store, elem);

            var rowdata = [];
            var postData = {
                queryObject: $scope.$parent.filters,
                type: 'store'
            };
            ajaxCall.post(crossStoreDistributionsDataUrls[elem], postData).then(function (response) {
                $scope.crossStore.CSCchartLoading = false;

                if (response.data) {
                    $scope.crossStore.distributions.chartData = response.data.data;
                    $scope.viewLessDistributionsFlag = false;
                    if ($scope.crossStore.distributions.chartData.length > storeToBeShownOnLoad) {
                        $scope.viewMoreDistributionsFlag = true;

                    } else {
                        $scope.viewMoreDistributionsFlag = false;
                        $scope.viewLessDistributionsFlag = false;
                        storeToBeShownOnLoad = $scope.crossStore.distributions.chartData.length;
                    }

                    for (i = 0; i < storeToBeShownOnLoad; i++) {

                        obj = {};
                        obj.c = []

                        obj.c.push({
                            'v': $scope.crossStore.distributions.chartData[i].shop_name
                        });
                        if (elem == 'visit_distribution') {
                            obj.c.push({
                                'v': $scope.crossStore.distributions.chartData[i].one_visit,
                                'f': ''
                            });
                            obj.c.push({
                                'v': $scope.crossStore.distributions.chartData[i].two_visits,
                                'f': ''
                            });
                            obj.c.push({
                                'v': $scope.crossStore.distributions.chartData[i].upto_four_visits,
                                'f': ''
                            });
                            obj.c.push({
                                'v': $scope.crossStore.distributions.chartData[i].four_plus_visits,
                                'f': ''
                            });

                        } else if (elem == 'dwell_time_distribution') {
                            obj.c.push({
                                'v': $scope.crossStore.distributions.chartData[i].upto_15,
                                'f': ''
                            });
                            obj.c.push({
                                'v': $scope.crossStore.distributions.chartData[i].upto_30,
                                'f': ''
                            });
                            obj.c.push({
                                'v': $scope.crossStore.distributions.chartData[i].upto_60,
                                'f': ''
                            });
                            obj.c.push({
                                'v': $scope.crossStore.distributions.chartData[i].upto_120,
                                'f': ''
                            });
                            obj.c.push({
                                'v': $scope.crossStore.distributions.chartData[i].more_120,
                                'f': ''
                            });

                        } else if (elem == 'age_distribution') {
                            obj.c.push({
                                'v': $scope.crossStore.distributions.chartData[i].below_18,
                                'f': ''
                            });
                            obj.c.push({
                                'v': $scope.crossStore.distributions.chartData[i].upto_25,
                                'f': ''
                            });
                            obj.c.push({
                                'v': $scope.crossStore.distributions.chartData[i].upto_35,
                                'f': ''
                            });
                            obj.c.push({
                                'v': $scope.crossStore.distributions.chartData[i].above_35,
                                'f': ''
                            });
                        } else if (elem == 'gender_distribution') {
                            obj.c.push({
                                'v': $scope.crossStore.distributions.chartData[i].female,
                                'f': ''
                            });
                            obj.c.push({
                                'v': $scope.crossStore.distributions.chartData[i].male,
                                'f': ''
                            });
                        } else if (elem == 'spend_capacity_distribution') {
                            obj.c.push({
                                'v': $scope.crossStore.distributions.chartData[i].Low,
                                'f': ''
                            });
                            obj.c.push({
                                'v': $scope.crossStore.distributions.chartData[i].Medium,
                                'f': ''
                            });
                            obj.c.push({
                                'v': $scope.crossStore.distributions.chartData[i].High,
                                'f': ''
                            });
                        }

                        $scope.crossStore.distributions[elem].store.data.rows.push(obj);
                    }
                    $scope.crossStore.distributions[elem].store.options.height = (10 * 30) + 100;
                    $scope.crossStore.distributions[elem].store.options.chartArea.height = 10 * 30;
                    $scope.crossStore.distributions[elem].store.options.chartArea.width = 600;
                    $scope.crossStore.distributions[elem].store.options.chartArea.top = 0;
                }

            });
        } else {
            chartDataObject[elem].isVisible = true;
            delete $scope.crossStore.distributions[elem].store;
        }
    }

    $scope.viewMoreDistributions = function (elem) {
        var rowdata = []
        for (var i = storeToBeShownOnLoad, len = $scope.crossStore.distributions.chartData.length; i < len; i++) {

            obj = {};
            obj.c = []

            obj.c.push({
                'v': $scope.crossStore.distributions.chartData[i].shop_name
            });
            if (elem == 'visit_distribution') {
                obj.c.push({
                    'v': $scope.crossStore.distributions.chartData[i].one_visit
                });
                obj.c.push({
                    'v': $scope.crossStore.distributions.chartData[i].two_visits
                });
                obj.c.push({
                    'v': $scope.crossStore.distributions.chartData[i].upto_four_visits
                });
                obj.c.push({
                    'v': $scope.crossStore.distributions.chartData[i].four_plus_visits
                });

            } else if (elem == 'dwell_time_distribution') {
                obj.c.push({
                    'v': $scope.crossStore.distributions.chartData[i].upto_15
                });
                obj.c.push({
                    'v': $scope.crossStore.distributions.chartData[i].upto_30
                });
                obj.c.push({
                    'v': $scope.crossStore.distributions.chartData[i].upto_60
                });
                obj.c.push({
                    'v': $scope.crossStore.distributions.chartData[i].upto_120
                });
                obj.c.push({
                    'v': $scope.crossStore.distributions.chartData[i].more_120
                });

            } else if (elem == 'age_distribution') {
                obj.c.push({
                    'v': $scope.crossStore.distributions.chartData[i].below_18
                });
                obj.c.push({
                    'v': $scope.crossStore.distributions.chartData[i].upto_25
                });
                obj.c.push({
                    'v': $scope.crossStore.distributions.chartData[i].upto_35
                });
                obj.c.push({
                    'v': $scope.crossStore.distributions.chartData[i].above_35
                });
            } else if (elem == 'gender_distribution') {
                obj.c.push({
                    'v': $scope.crossStore.distributions.chartData[i].female
                });
                obj.c.push({
                    'v': $scope.crossStore.distributions.chartData[i].male
                });
            } else if (elem == 'spend_capacity_distribution') {
                obj.c.push({
                    'v': $scope.crossStore.distributions.chartData[i].Low,
                    'f': ''
                });
                obj.c.push({
                    'v': $scope.crossStore.distributions.chartData[i].Medium,
                    'f': ''
                });
                obj.c.push({
                    'v': $scope.crossStore.distributions.chartData[i].High,
                    'f': ''
                });
            }
            $scope.crossStore.distributions[elem].store.data.rows.push(obj);
            $scope.crossStore.distributions[elem].store.options.height += 30;
            $scope.crossStore.distributions[elem].store.options.chartArea.height += 30;
        }

        $scope.viewMoreDistributionsFlag = false;
        $scope.viewLessDistributionsFlag = true;
    }

    $scope.viewLessDistributions = function (elem) {
        $scope.crossStore.distributions[elem].store.data.rows = $scope.crossStore.distributions[elem].store.data.rows.slice(0, storeToBeShownOnLoad);
        $scope.crossStore.distributions[elem].store.options.height = (12 * 30) + 100;
        $scope.crossStore.distributions[elem].store.options.chartArea.height = 12 * 30;
        $scope.viewMoreDistributionsFlag = true;
        $scope.viewLessDistributionsFlag = false;
    }


    if (validateQueryObject($scope.$parent.filters)) {
        $rootScope.$broadcast('params-updated');
    }
}])
.controller('wifimetricsController', ['$rootScope', '$scope', 'service', 'ajaxCall', 'messageService', function ($rootScope, $scope, service, ajaxCall, messageService) {
    var postData = {
        queryObject: $scope.$parent.filters
    };


    var crossStoreAvgDataUrls = {
        wifi_users: "/OneInsights/GetDailyWifiUsersForCsc",
        daily_time_spent: "/OneInsights/GetDailyTimeSpentOnWifiForCsc",
        daily_data_used: "/OneInsights/GetDailyDataUsedOnWifiForCsc",
        repeat_rate: "/OneInsights/GetRepeatRatesOnWifiForCsc"
    }

    $scope.selectAvgWiFiCscDataType = 'wifi_users';

    $scope.Math = Math;

    $scope.wifiMetrics = {};
    $scope.wifiMetrics.genderDistribution = donutDataObject.genderDistribution;
    $scope.wifiMetrics.ageDistribution = donutDataObject.ageDistribution;
    $scope.wifiMetrics.spendDistribution = donutDataObject.spendDistribution;
    $scope.wifiMetrics.customerDistribution = donutDataObject.customerDistribution;
    $scope.wifiMetrics.get_industy_comparision = "0";
    $scope.wifiMetrics.demographics = {};
    $scope.wifiMetrics.crossStoreComparison = {};
    $scope.wifiMetrics.inCompleteDataDates = [];
    $scope.wifiMetrics.holidaysDates = holidays;


    $scope.wifiMetrics.trends = {};
    $scope.wifiMetrics.trends.wifi_users = { label: "DAILY WI-FI USERS", icon: "images/location.svg", store: {}, industry: {} };
    $scope.wifiMetrics.trends.users_time_spent = { label: "DAILY TIME SPENT BY USERS", icon: "images/feet2.svg", store: {}, industry: {} };
    $scope.wifiMetrics.trends.wifi_data_usage = { label: "AVG. DAILY DATA USED BY USER", icon: "images/shopping2.svg", store: {}, industry: {} };
    $scope.wifiMetrics.trends.wifi_repeat_rate = { label: "REPEAT RATE", icon: "images/repeat.svg", store: {}, industry: {} };

    $scope.$on('params-updated', function (event, args) {

        $scope.wifiMetrics.get_industy_comparision = "0";
        if (validateQueryObject($scope.$parent.filters)) {
            ajaxCall.post('/OneInsights/GetWifiUsersCountSinceStart', {
                queryObject: $scope.$parent.filters
            }).then(function (response) {
                if (response.data && response.data > 0) {
                    $scope.wifiMetrics.wifi_users_sice_start = response.data;
                } else {
                    $scope.wifiMetrics.wifi_users_sice_start = 'n/a';
                }
            });

            ajaxCall.post('/OneInsights/GetWifiUsersCountInPeriod', {
                queryObject: $scope.$parent.filters
            }).then(function (response) {
                if (response.data && response.data > 0) {
                    $scope.wifiMetrics.wifi_users_in_time_period = response.data;
                } else {
                    $scope.wifiMetrics.wifi_users_in_time_period = 'n/a';
                }
            });

            ajaxCall.post('/OneInsights/GetNewWifiUsersCountInPeriod', {
                queryObject: $scope.$parent.filters
            }).then(function (response) {
                if (response.data && response.data > 0) {
                    $scope.wifiMetrics.new_wifi_users = response.data;
                } else {
                    $scope.wifiMetrics.new_wifi_users = 'n/a';
                }
            });



            ajaxCall.post('/OneInsights/GetDailyAvglUsersCountForWifi', {
                queryObject: $scope.$parent.filters,
                type: 'stores'
            }).then(function (response) {
                if (response.data && response.data > 0) {
                    $scope.wifiMetrics.trends.wifi_users.store.current = response.data;
                    $scope.wifiMetrics.total_wifi_users_count = response.data;
                } else {
                    $scope.wifiMetrics.total_wifi_users_count = 'n/a';
                    $scope.wifiMetrics.trends.wifi_users.store.current = 'n/a';
                }
            });

            ajaxCall.post('/OneInsights/GetAvgTimeSpentByUsersForWifi', {
                queryObject: $scope.$parent.filters,
                type: 'stores'
            }).then(function (response) {
                if (response.data && response.data > 0) {
                    $scope.wifiMetrics.trends.users_time_spent.store.current = response.data;
                    $scope.wifiMetrics.wifi_users_avg_time_spent = response.data;
                } else {
                    $scope.wifiMetrics.wifi_users_avg_time_spent = 'n/a';
                    $scope.wifiMetrics.trends.users_time_spent.store.current = 'n/a';
                }
            });

            ajaxCall.post('/OneInsights/GetAvgDailyDataUsedByUsersForWifi', {
                queryObject: $scope.$parent.filters,
                type: 'stores'
            }).then(function (response) {
                if (response.data && response.data > 0) {
                    $scope.wifiMetrics.trends.wifi_data_usage.store.current = response.data;
                    $scope.wifiMetrics.avg_wifi_data_usage = response.data;
                } else {
                    $scope.wifiMetrics.avg_wifi_data_usage = 'n/a';
                    $scope.wifiMetrics.trends.wifi_data_usage.store.current = 'n/a';
                }
            });

            ajaxCall.post('/OneInsights/GetAvgRepeatRateForWifi', {
                queryObject: $scope.$parent.filters,
                type: 'stores'
            }).then(function (response) {
                if (response.data && response.data > 0) {
                    $scope.wifiMetrics.trends.wifi_repeat_rate.store.current = response.data.round(1);
                    $scope.wifiMetrics.avg_wifi_repeat_rate = response.data.round(1);
                } else {
                    $scope.wifiMetrics.avg_wifi_repeat_rate = 'n/a';
                    $scope.wifiMetrics.trends.wifi_repeat_rate.store.current = 'n/a';
                }
            });

            ajaxCall.post('/OneInsights/GetDevicesLastDataPeriodDateRange', postData).then(function (response) {
                $scope.lasDataPeriodPostData = angular.copy(postData);
                $scope.lasDataPeriodPostData.queryObject.nasids = $scope.$parent.filters.nasids;
                if (response.data.data.from != "" && response.data.data.from != 'n/a')
                    $scope.lasDataPeriodPostData.queryObject.from = moment(response.data.data.from, 'MM/DD/YYYY HH:mm:ss a').format('MM/DD/YYYY'); //moment(response.data.data.from, 'MM/DD/YYYY').toDate();
                else
                    $scope.lasDataPeriodPostData.queryObject.from = null;
                if (response.data.data.to != "" && response.data.data.to != 'n/a')
                    $scope.lasDataPeriodPostData.queryObject.to = moment(response.data.data.to, 'MM/DD/YYYY HH:mm:ss a').format('MM/DD/YYYY'); //moment(response.data.data.to, 'MM/DD/YYYY').toDate();
                else
                    $scope.lasDataPeriodPostData.queryObject.to = null;

                if ($scope.lasDataPeriodPostData.queryObject.from != null && $scope.lasDataPeriodPostData.queryObject.to != null) {
                    $scope.lastDateRangeHtml = "Last Period:";
                    $scope.lastDateRangeHtml += moment.utc($scope.lasDataPeriodPostData.queryObject.from, 'MM/DD/YYYY').format("Do MMM");
                    $scope.lastDateRangeHtml += " To " + moment.utc($scope.lasDataPeriodPostData.queryObject.to, 'MM/DD/YYYY').format("Do MMM");

                    $scope.$watch('wifiMetrics.total_wifi_users_count', function (newVal, oldVal) {
                        ajaxCall.post('/OneInsights/GetDailyAvglUsersCountForWifi', {
                            queryObject: $scope.lasDataPeriodPostData.queryObject,
                            type: 'stores'
                        }).then(function (response) {
                            if (response.data && response.data > 0) {
                                $scope.wifiMetrics.trends.wifi_users.store.last_period = response.data;
                                $scope.wifiMetrics.trends.wifi_users.store.change = $scope.wifiMetrics.trends.wifi_users.store.current.percentChange(response.data).round(1)
                                $scope.wifiMetrics.total_wifi_users_count_change = $scope.wifiMetrics.total_wifi_users_count.percentChange(response.data).round(1)
                            }
                            else
                                $scope.wifiMetrics.total_wifi_users_count_change = 'n/a';
                        });
                    });

                    $scope.$watch('$scope.wifiMetrics.wifi_users_avg_time_spent', function (newVal, oldVal) {
                        ajaxCall.post('/OneInsights/GetAvgTimeSpentByUsersForWifi', {
                            queryObject: $scope.lasDataPeriodPostData.queryObject,
                            type: 'stores'
                        }).then(function (response) {
                            if (response.data && response.data > 0) {
                                $scope.wifiMetrics.trends.users_time_spent.store.last_period = response.data;
                                $scope.wifiMetrics.trends.users_time_spent.store.change = $scope.wifiMetrics.trends.users_time_spent.store.current.percentChange(response.data).round(1)
                                $scope.wifiMetrics.wifi_users_avg_time_spent_change = $scope.wifiMetrics.wifi_users_avg_time_spent.percentChange(response.data).round(1)
                            }
                            else
                                $scope.wifiMetrics.wifi_users_avg_time_spent_change = 'n/a';
                        });
                    });

                    $scope.$watch('$scope.wifiMetrics.avg_wifi_data_usage', function (newVal, oldVal) {
                        ajaxCall.post('/OneInsights/GetAvgDailyDataUsedByUsersForWifi', {
                            queryObject: $scope.lasDataPeriodPostData.queryObject,
                            type: 'stores'
                        }).then(function (response) {
                            if (response.data && response.data > 0) {
                                $scope.wifiMetrics.trends.wifi_data_usage.store.last_period = response.data;
                                $scope.wifiMetrics.trends.wifi_data_usage.store.change = $scope.wifiMetrics.trends.wifi_data_usage.store.current.percentChange(response.data).round(1)
                                $scope.wifiMetrics.avg_wifi_data_usage_change = $scope.wifiMetrics.avg_wifi_data_usage.percentChange(response.data).round(1)
                            }
                            else
                                $scope.wifiMetrics.avg_wifi_data_usage_change = 'n/a';
                        });
                    });

                    $scope.$watch('$scope.wifiMetrics.avg_wifi_repeat_rate', function (newVal, oldVal) {
                        ajaxCall.post('/OneInsights/GetAvgRepeatRateForWifi', {
                            queryObject: $scope.lasDataPeriodPostData.queryObject,
                            type: 'stores'
                        }).then(function (response) {

                            if (response.data && response.data > 0) {
                                $scope.wifiMetrics.trends.wifi_repeat_rate.store.last_period = response.data;
                                $scope.wifiMetrics.trends.wifi_repeat_rate.store.change = $scope.wifiMetrics.trends.wifi_repeat_rate.store.current.percentChange(response.data).round(1)
                                $scope.wifiMetrics.avg_wifi_repeat_rate_change = $scope.wifiMetrics.avg_wifi_repeat_rate.percentChange(response.data).round(1)
                            }
                            else
                                $scope.wifiMetrics.avg_wifi_repeat_rate_change = 'n/a';
                        });
                    });
                } else {
                    $scope.lastDateRangeHtml = "Last period data unavailable for the date selection";
                }
            });

            ajaxCall.post('/OneInsights/GetPsychographics', postData).then(function (response) {
                if (response.data) {
                    $scope.wifiMetrics.psychoGraphics = {};

                    $scope.wifiMetrics.psychoGraphics.type = "BarChart";
                    $scope.wifiMetrics.psychoGraphics.options = barChartOptions;
                    $scope.wifiMetrics.psychoGraphics.options.isStacked = 'percent';

                    $scope.wifiMetrics.psychoGraphics.options.vAxis = {
                        gridlines: {
                            count: 1,
                            color: 'transparent'
                        }
                    }
                    $scope.wifiMetrics.psychoGraphics.options.hAxis = {
                        baselineColor: '#fff',
                        gridlineColor: '#fff',
                        textPosition: 'none',
                        gridlines: {
                            count: 1,
                            color: 'transparent'
                        },
                        maxValue: 1,
                        format: '#.#%'
                    };
                    $scope.wifiMetrics.psychoGraphics.options.legend = {
                        position: 'bottom',
                        alignment: 'center',
                        textStyle: { fontSize: 10 }
                    };

                    $scope.wifiMetrics.psychoGraphics.data = {
                        "cols": []
                    };
                    $scope.wifiMetrics.psychoGraphics.data.rows = [];
                    var obj = {};
                    obj.c = []
                    var t = 0;
                    obj.c.push({
                        v: 'Devices Used',
                        f: 'Devices Used'
                    });
                    $scope.wifiMetrics.psychoGraphics.data.cols.push({
                        id: 'devices',
                        label: 'Devices Used',
                        type: 'string'
                    });
                    angular.forEach(response.data.data, function (elem, index) {
                        if (index == 9) {
                            $scope.wifiMetrics.psychoGraphics.data.cols.push({
                                id: 'other',
                                label: 'Other',
                                type: 'number'
                            });
                            t += parseInt(elem.frequency);
                        } else if (index < 9) {
                            $scope.wifiMetrics.psychoGraphics.data.cols.push({
                                id: elem.device,
                                label: elem.device,
                                type: 'number'
                            })
                            obj.c.push({
                                v: elem.frequency,
                                f: ''
                            });
                        } else if (index == (response.data.data.length - 1)) {
                            obj.c.push({
                                v: t,
                                f: ''
                            });
                            t += parseInt(elem.frequency);
                        } else if (index > 9) {
                            t += parseInt(elem.frequency);
                        }
                    });
                    $scope.wifiMetrics.psychoGraphics.data.rows.push(obj);

                }
            });

            ajaxCall.post('/OneInsights/GetWifiUsersGenders', {
                queryObject: $scope.$parent.filters,
                type: 'stores'
            }).then(function (response) {

                $scope.wifiMetrics.demographics.genderData = response.data.data;
                $scope.wifiMetrics.genderDistribution.data.rows = [];

                if (response.data && response.data.data) {
                    var genderArray = response.data.data;

                    for (var i = 0; i < genderArray.length; i++) {
                        var obj = {};
                        obj.c = [];
                        obj.c.push({
                            'v': genderArray[i].gender
                        });
                        obj.c.push({
                            'v': parseFloat(genderArray[i].frequency).round(1),
                            'f': parseFloat(genderArray[i].frequency).round(1) + ' %'
                        });

                        $scope.wifiMetrics.genderDistribution.data.rows.push(obj);
                    }
                }

            });

            ajaxCall.post('/OneInsights/GetWifiUsersAgeGroup', {
                queryObject: $scope.$parent.filters,
                type: 'stores'
            }).then(function (response) {
                
                $scope.wifiMetrics.demographics.ageData = response.data.data;
                $scope.wifiMetrics.ageDistribution.data.rows = [];

                if (response.data) {
                    var ageArray = response.data.data;
                    for (var i = 0; i < ageArray.length; i++) {
                        var obj = {};
                        obj.c = [];
                        obj.c.push({
                            'v': ageArray[i].age
                        });
                        obj.c.push({
                            'v': parseFloat(ageArray[i].frequency).round(1),
                            'f': parseFloat(ageArray[i].frequency).round(1) + ' %'
                        });

                        $scope.wifiMetrics.ageDistribution.data.rows.push(obj);
                    }
                }

            });


            ajaxCall.post('/OneInsights/GetWifiUsersSpendCapacity', {
                queryObject: $scope.$parent.filters,
                type: 'stores'
            }).then(function (response) {
                
                $scope.wifiMetrics.spendDistribution.data.rows = [];
                $scope.wifiMetrics.demographics.spendCapacityData = response.data;

                if (response.data) {
                    var spendArray = response.data;
                    for (var i = 0; i < spendArray.length; i++) {
                        var obj = {};
                        obj.c = [];
                        obj.c.push({
                            'v': spendArray[i].type
                        });
                        obj.c.push({
                            'v': parseFloat(spendArray[i].frequency).round(1),
                            'f': parseFloat(spendArray[i].frequency).round(1) + ' %'
                        });

                        $scope.wifiMetrics.spendDistribution.data.rows.push(obj);
                    }
                }
            });



            ajaxCall.post('/OneInsights/GetWifiUsersLoyaltySegment', {
                queryObject: $scope.$parent.filters,
                type: 'stores'
            }).then(function (response) {
                
                $scope.wifiMetrics.demographics.loyaltySegment = response.data.data;
                $scope.wifiMetrics.customerDistribution.data.rows = [];

                if (response.data && response.data.data) {
                    var customerArray = response.data.data;
                    for (var i = 0; i < customerArray.length; i++) {
                        var obj = {};
                        obj.c = [];
                        obj.c.push({
                            'v': customerArray[i].segment
                        });
                        obj.c.push({
                            'v': parseFloat(customerArray[i].frequency).round(1),
                            'f': parseFloat(customerArray[i].frequency).round(1) + ' %'
                        });

                        $scope.wifiMetrics.customerDistribution.data.rows.push(obj);
                    }
                }

            });

            //if ($scope.wifiMetrics.chartDataObject) {
            //    if ($scope.wifiMetrics.chartDataObject.wifi_users && $scope.wifiMetrics.chartDataObject.wifi_users.isVisible) {
            //        $scope.getWiFiUsersCountTrend()
            //    }
            //    if ($scope.wifiMetrics.chartDataObject.users_time_spent && $scope.wifiMetrics.chartDataObject.users_time_spent.isVisible) {
            //        $scope.getWiFiUsersTimeSpent()
            //    }
            //    if ($scope.wifiMetrics.chartDataObject.wifi_data_usage && $scope.wifiMetrics.chartDataObject.wifi_data_usage.isVisible) {
            //        $scope.getWiFiDataUsedByUsers()
            //    }
            //    if ($scope.wifiMetrics.chartDataObject.wifi_repeat_rate && $scope.wifiMetrics.chartDataObject.wifi_repeat_rate.isVisible) {
            //        $scope.getWiFiRepeatRate()
            //    }
            //}

            if ($scope.wifiMetrics.chartDataObject) {
                angular.forEach($scope.wifiMetrics.chartDataObject, function (chartObject, metric) {
                    if ($scope.wifiMetrics.chartDataObject[metric] && $scope.wifiMetrics.chartDataObject[metric].isVisible) {
                        $scope.creatWifiMetricGraph(metric)
                    }
                });
            }

            $scope.getAveragesForCscWifi($scope.selectAvgWiFiCscDataType);
        }
    });


    $scope.getIndusrtyComparision = function () {
        if ($scope.wifiMetrics.get_industy_comparision == "1") {

            ajaxCall.post('/OneInsights/GetDailyAvglUsersCountForWifi', {
                queryObject: $scope.$parent.filters,
                type: 'industry'
            }).then(function (response) {
                if (response.data && response.data > 0) {
                    $scope.wifiMetrics.trends.wifi_users.industry.current = response.data;
                    $scope.wifiMetrics.total_industry_wifi_users_count = response.data;
                    if ($scope.lasDataPeriodPostData.queryObject.from != null && $scope.lasDataPeriodPostData.queryObject.to != null) {
                        ajaxCall.post('/OneInsights/GetDailyAvglUsersCountForWifi', {
                            queryObject: $scope.lasDataPeriodPostData.queryObject,
                            type: 'industry'
                        }).then(function (new_response) {
                            if (new_response.data && new_response.data > 0) {
                                $scope.wifiMetrics.trends.wifi_users.industry.last_period = new_response.data;
                                $scope.wifiMetrics.trends.wifi_users.industry.change = $scope.wifiMetrics.trends.wifi_users.industry.current.percentChange(new_response.data).round(1);
                                $scope.wifiMetrics.total_industry_wifi_users_count_change = $scope.wifiMetrics.total_industry_wifi_users_count.percentChange(new_response.data).round(1);
                            } else {
                                $scope.wifiMetrics.total_industry_wifi_users_count_change = 'n/a';
                            }
                        });
                    }
                } else {
                    $scope.wifiMetrics.total_industry_wifi_users_count = 'n/a';
                }
            });

            ajaxCall.post('/OneInsights/GetAvgTimeSpentByUsersForWifi', {
                queryObject: $scope.$parent.filters,
                type: 'industry'
            }).then(function (response) {
                if (response.data && response.data > 0) {
                    $scope.wifiMetrics.trends.users_time_spent.industry.current = response.data;
                    $scope.wifiMetrics.wifi_industry_users_avg_time_spent = response.data;
                    if ($scope.lasDataPeriodPostData.queryObject.from != null && $scope.lasDataPeriodPostData.queryObject.to != null) {
                        ajaxCall.post('/OneInsights/GetAvgTimeSpentByUsersForWifi', {
                            queryObject: $scope.lasDataPeriodPostData.queryObject,
                            type: 'industry'
                        }).then(function (new_response) {
                            if (new_response.data && new_response.data > 0) {
                                $scope.wifiMetrics.trends.users_time_spent.industry.last_period = new_response.data;
                                $scope.wifiMetrics.trends.users_time_spent.industry.change = $scope.wifiMetrics.trends.users_time_spent.industry.current.percentChange(new_response.data).round(1);
                                $scope.wifiMetrics.wifi_industry_users_avg_time_spent_change = $scope.wifiMetrics.wifi_industry_users_avg_time_spent.percentChange(new_response.data).round(1);
                            } else {
                                $scope.wifiMetrics.wifi_industry_users_avg_time_spent_change = 'n/a';
                            }
                        });
                    }
                } else {
                    $scope.wifiMetrics.wifi_industry_users_avg_time_spent = 'n/a';
                }
            });

            ajaxCall.post('/OneInsights/GetAvgDailyDataUsedByUsersForWifi', {
                queryObject: $scope.$parent.filters,
                type: 'industry'
            }).then(function (response) {
                if (response.data && response.data > 0) {
                    $scope.wifiMetrics.trends.wifi_data_usage.industry.current = response.data;
                    $scope.wifiMetrics.avg_wifi_industry_data_usage = response.data;
                    if ($scope.lasDataPeriodPostData.queryObject.from != null && $scope.lasDataPeriodPostData.queryObject.to != null) {
                        ajaxCall.post('/OneInsights/GetAvgDailyDataUsedByUsersForWifi', {
                            queryObject: $scope.lasDataPeriodPostData.queryObject,
                            type: 'industry'
                        }).then(function (new_response) {
                            if (new_response.data && new_response.data > 0) {
                                $scope.wifiMetrics.trends.wifi_data_usage.industry.last_period = new_response.data;
                                $scope.wifiMetrics.trends.wifi_data_usage.industry.change = $scope.wifiMetrics.trends.wifi_data_usage.industry.current.percentChange(new_response.data).round(1);
                                $scope.wifiMetrics.avg_wifi_industry_data_usage_change = $scope.wifiMetrics.avg_wifi_industry_data_usage.percentChange(new_response.data).round(1);
                            } else {
                                $scope.wifiMetrics.avg_wifi_industry_data_usage_change = 'n/a';
                            }
                        });
                    }
                } else {
                    $scope.wifiMetrics.avg_wifi_industry_data_usage = 'n/a';
                }
            });

            ajaxCall.post('/OneInsights/GetAvgRepeatRateForWifi', {
                queryObject: $scope.$parent.filters,
                type: 'industry'
            }).then(function (response) {
                if (response.data && response.data > 0) {
                    $scope.wifiMetrics.trends.wifi_repeat_rate.industry.current = parseFloat(response.data).round(1);
                    $scope.wifiMetrics.avg_wifi_industry_repeat_rate = parseFloat(response.data).round(1);
                    if ($scope.lasDataPeriodPostData.queryObject.from != null && $scope.lasDataPeriodPostData.queryObject.to != null) {
                        ajaxCall.post('/OneInsights/GetAvgRepeatRateForWifi', {
                            queryObject: $scope.lasDataPeriodPostData.queryObject,
                            type: 'industry'
                        }).then(function (new_response) {
                            if (new_response.data && new_response.data > 0) {
                                $scope.wifiMetrics.trends.wifi_repeat_rate.industry.last_period = new_response.data;
                                $scope.wifiMetrics.trends.wifi_repeat_rate.industry.change = $scope.wifiMetrics.trends.wifi_repeat_rate.industry.current.percentChange(new_response.data).round(1);
                                $scope.wifiMetrics.avg_wifi_industry_repeat_rate_change = $scope.wifiMetrics.avg_wifi_industry_repeat_rate.percentChange(parseFloat(new_response.data)).round(1);
                            } else {
                                $scope.wifiMetrics.avg_wifi_industry_repeat_rate_change = 'n/a';
                            }
                        });
                    }
                } else {
                    $scope.wifiMetrics.avg_wifi_industry_repeat_rate = 'n/a';
                }
            });

        }
    }

    $scope.getWiFiTrendGraph = function ($event) {
        if ($event.target.id == 'wifi_users') {
            $scope.creatWifiMetricGraph('wifi_users');
        }
        if ($event.target.id == 'users_time_spent') {
            $scope.creatWifiMetricGraph('users_time_spent');
        }
        if ($event.target.id == 'wifi_data_usage') {
            $scope.creatWifiMetricGraph('wifi_data_usage');
        }
        if ($event.target.id == 'wifi_repeat_rate') {
            $scope.creatWifiMetricGraph('wifi_repeat_rate');
        }
    }

   

    var wifiMetricGraphDataUrls = {
        wifi_users: {
            store: '/OneInsights/GetWiFiUsersCountTrend',
            brand: '/OneInsights/GetWiFiUsersCountTrend',
            industry: '/OneInsights/GetWiFiUsersCountTrendForIndustry'
        },
        users_time_spent: {
            store: '/OneInsights/GetTimeSpentByUsersForWifi',
            brand: '/OneInsights/GetTimeSpentByUsersForWifi',
            industry: '/OneInsights/GetTimeSpentByWiFiUsersForIndustry'
        },
        wifi_data_usage: {
            store: '/OneInsights/GetDataUsedByUsersForWifi',
            brand: '/OneInsights/GetDataUsedByUsersForWifi',
            industry: '/OneInsights/GetDataUsedByUsersForWifiForIndustry'
        },
        wifi_repeat_rate: {
            store: '/OneInsights/GetRepeatRateForWifi',
            brand: '/OneInsights/GetRepeatRateForWifi',
            industry: '/OneInsights/GetRepeatRateForWifiForIndustry'
        }
    }

    $scope.creatWifiMetricGraph = function (metric) {
        $scope.$emit('masonry.reloaded');
        $scope.wifiMetrics.chartDataObject[metric] = wifiMetricsChartDataObject[metric];
        $scope.wifiMetrics.chartDataObject[metric].data = {};
        var postData = {
            queryObject: angular.copy($scope.$parent.filters)
        };
        postData.queryObject.tsType = $scope.wifiMetrics.chartDataObject[metric].tsType;
        if ($scope.wifiMetrics.chartDataObject[metric].isVisible) {
            $scope.wifiMetrics.chartDataObject[metric].data.cols = [];
            $scope.wifiMetrics.chartDataObject[metric].data.rows = [];
            $scope.wifiMetrics.chartDataObject[metric] = addColoumnInLineChartData($scope.wifiMetrics.chartDataObject[metric]);
            if (metric == "wifi_repeat_rate") {
                $scope.wifiMetrics.chartDataObject[metric].options.vAxis = {
                    //minValue: 1,
                    //maxValue: 25,
                    format: '#.#\'%\''
                }
            } else {
                $scope.wifiMetrics.chartDataObject[metric].options.vAxis = {
                    minValue: 0,
                    format: 'short'
                }
            }
            var maxYaxis = 0;
            ajaxCall.post(wifiMetricGraphDataUrls[metric].store, postData).then(function (response) {
                if (response.data) {

                    var chartData = response.data.data.filter(function (obj) {
                        return obj.label != 255;
                    });

                    if ($scope.wifiMetrics.chartDataObject[metric].tsType == "monthly") {
                        var datapoint = new Date(chartData[0].label.substring(0, 4), chartData[0].label.substring(4, 6) - 1);
                        y = datapoint.getFullYear(), m = datapoint.getMonth();
                        $scope.wifiMetrics.chartDataObject[metric].options.hAxis = {
                            gridlines: { count: chartData.length + 2 },
                            minValue: new Date(y, m - 1, 1),
                            maxValue: new Date(y, m + chartData.length, 1)
                        }
                        $scope.wifiMetrics.chartDataObject[metric].options.isStacked = false;
                        if (chartData.length == 1) {
                            $scope.wifiMetrics.chartDataObject[metric].type = "ColumnChart"
                            $scope.wifiMetrics.chartDataObject[metric].options.bar = { groupWidth: 60 }
                        } else {
                            $scope.wifiMetrics.chartDataObject[metric].type = "LineChart"
                        }
                    } else {
                        $scope.wifiMetrics.chartDataObject[metric].options.hAxis = {};
                        $scope.wifiMetrics.chartDataObject[metric].options.isStacked = false;
                        $scope.wifiMetrics.chartDataObject[metric].type = "LineChart";
                    }

                    var hTicks = [];
                    for (var i = 0, len = chartData.length; i < len; i++) {
                        obj = {};
                        obj.c = []
                        if ($scope.wifiMetrics.chartDataObject[metric].tsType == "daily") {
                            obj.c.push({
                                'v': new Date(moment(chartData[i].label))
                            });
                        } else if ($scope.wifiMetrics.chartDataObject[metric].tsType == "weekly") {
                            var day_slot = day_slots.filter(function (obj) {
                                return obj.index == chartData[i].label;
                            });

                            obj.c.push({
                                'v': chartData[i].label,
                                'f': day_slot[0].value
                            });
                            hTicks.push({
                                v: chartData[i].label,
                                f: day_slot[0].value
                            });


                        } else if ($scope.wifiMetrics.chartDataObject[metric].tsType == "monthly") {
                            obj.c.push({
                                'v': new Date(chartData[i].label.substring(0, 4), chartData[i].label.substring(4, 6) - 1)
                            });

                        } else {
                            var time_slot = time_slots.filter(function (obj) {
                                return obj.index == chartData[i].label;
                            });
                            obj.c.push({
                                'v': chartData[i].label,
                                'f': time_slot[0].value
                            });
                            hTicks.push({
                                v: chartData[i].label,
                                f: time_slot[0].value
                            });
                        }

                        if (metric == "wifi_repeat_rate") {
                            obj.c.push({
                                'v': parseFloat(chartData[i].value).round(1),
                                'f': parseFloat(chartData[i].value).round(1) + '%'
                            });
                            if (maxYaxis < parseFloat(chartData[i].value))
                                maxYaxis = Math.ceil(parseFloat(chartData[i].value));
                        } else {
                            obj.c.push({
                                'v': parseFloat(chartData[i].value)
                            });
                        }

                        var clr_obj = $scope.getDailyDataColorObject($scope.wifiMetrics.chartDataObject[metric].tsType, chartData[i].label, store_color);
                        obj.c.push(clr_obj);

                        $scope.wifiMetrics.chartDataObject[metric].data.rows.push(obj);
                    }

                    if ($scope.wifiMetrics.chartDataObject[metric].tsType == "hourly" ||
                        $scope.wifiMetrics.chartDataObject[metric].tsType == "weekly") {
                        $scope.wifiMetrics.chartDataObject[metric].options.hAxis = {
                            ticks: hTicks
                        }
                    } else {
                        delete $scope.wifiMetrics.chartDataObject[metric].options.hAxis.ticks;
                    }

                    var brandPostData = angular.copy(postData);
                    brandPostData.queryObject.nasids = $scope.$parent.brandNases;
                    ajaxCall.post(wifiMetricGraphDataUrls[metric].brand, brandPostData).then(function (response) {
                        if (response.data) {
                            var chartDataForBrand = response.data.data.filter(function (obj) {
                                return obj.label != 255;
                            });
                            for (var i = 0, len = chartDataForBrand.length; i < len; i++) {
                                if (metric == "wifi_repeat_rate") {
                                    $scope.wifiMetrics.chartDataObject[metric].data.rows[i].c.push({
                                        'v': parseFloat(chartDataForBrand[i].value).round(1),
                                        'f': parseFloat(chartDataForBrand[i].value).round(1) + '%'
                                    });
                                } else {
                                    $scope.wifiMetrics.chartDataObject[metric].data.rows[i].c.push({
                                        'v': chartDataForBrand[i].value
                                    });
                                }

                                var clr_obj = $scope.getDailyDataColorObject($scope.wifiMetrics.chartDataObject[metric].tsType, chartData[i].label, brand_color);
                                $scope.wifiMetrics.chartDataObject[metric].data.rows[i].c.push(clr_obj);
                            }
                        }
                        ajaxCall.post(wifiMetricGraphDataUrls[metric].industry, postData).then(function (newResponse) {
                            if (newResponse.data) {
                                var chartDataForIndustry = newResponse.data.data.filter(function (obj) {
                                    return obj.label != 255;
                                });
                                for (var i = 0, len = chartDataForIndustry.length; i < len; i++) {
                                    
                                    if (metric == "wifi_repeat_rate") {
                                        $scope.wifiMetrics.chartDataObject[metric].data.rows[i].c.push({
                                            'v': parseFloat(chartDataForIndustry[i].value).round(1),
                                            'f': parseFloat(chartDataForIndustry[i].value).round(1) + '%'
                                        });
                                    } else {
                                        $scope.wifiMetrics.chartDataObject[metric].data.rows[i].c.push({
                                            'v': chartDataForIndustry[i].value
                                        });
                                    }
                                    var clr_obj = $scope.getDailyDataColorObject($scope.wifiMetrics.chartDataObject[metric].tsType, chartData[i].label, industry_color);
                                    $scope.wifiMetrics.chartDataObject[metric].data.rows[i].c.push(clr_obj);

                                }

                            }
                        });
                    });
                    if (metric == 'wifi_repeat_rate') {
                        $scope.wifiMetrics.chartDataObject[metric].options.vAxis.maxValue = maxYaxis;
                    }
                }
            });
        } else {
            wifiMetricsChartDataObject[metric].isVisible = true;
            delete $scope.wifiMetrics.chartDataObject[metric];
        }
    }

    $scope.getDailyDataColorObject = function (tsType, dateLabel, default_color) {
        var obj = {};
        if (default_color == '#1f5076') {
            if (tsType == "daily" && (moment(dateLabel).format('dddd') == 'Saturday'
                                            || moment(dateLabel).format('dddd') == 'Sunday')) {
                obj.v = 'point { size: 3; shape-type: circle; visible:True; fill-color:#71c044;}'
            } /*else if (tsType == "daily" && $scope.locationsInsight.inCompleteDataDates.indexOf(dateLabel) > -1) {
                obj.v = incomplete_data_color
            } */ else if (tsType == "daily"
                && holidays.indexOf(moment(dateLabel).format("YYYY-MM-DD")) > -1) {
                obj.v = 'point { size: 3; shape-type: circle; visible:True; fill-color:#ffd503;}'
            } else {
                obj.v = default_color
            }
        } else {
            obj.v = default_color
        }
        return obj;
    }

    $scope.hideWifMrtricSeries = hideWifMrtricSeries;

    function hideWifMrtricSeries(selectedItem, chartType) {
        var col = selectedItem.column;
        if (selectedItem.row === null) {
            if ($scope.wifiMetrics.chartDataObject[chartType].view.columns[col] == col) {
                $scope.wifiMetrics.chartDataObject[chartType].view.columns[col] = {
                    label: $scope.wifiMetrics.chartDataObject[chartType].data.cols[col].label,
                    type: $scope.wifiMetrics.chartDataObject[chartType].data.cols[col].type,
                    calc: function () {
                        return null;
                    }
                };
                if (col == 5) {
                    $scope.wifiMetrics.chartDataObject[chartType].options.colors[col - 3] = '#cccccc';
                } else if (col == 3) {
                    $scope.wifiMetrics.chartDataObject[chartType].options.colors[col - 2] = '#cccccc';
                } else {
                    $scope.wifiMetrics.chartDataObject[chartType].options.colors[col - 1] = '#cccccc';
                }
            } else {
                $scope.wifiMetrics.chartDataObject[chartType].view.columns[col] = col;
                if (col == 5) {
                    $scope.wifiMetrics.chartDataObject[chartType].options.colors[col - 3] = $scope.wifiMetrics.chartDataObject[chartType].options.defaultColors[col - 3];
                } else if (col == 3) {
                    $scope.wifiMetrics.chartDataObject[chartType].options.colors[col - 2] = $scope.wifiMetrics.chartDataObject[chartType].options.defaultColors[col - 2];
                } else {
                    $scope.wifiMetrics.chartDataObject[chartType].options.colors[col - 1] = $scope.wifiMetrics.chartDataObject[chartType].options.defaultColors[col - 1];
                }
            }
        }
    }


    $scope.getAverageChartDataForWifiCsc = function () {
        $scope.getAveragesForCscWifi($scope.selectAvgWiFiCscDataType);
    }

    var avg_brand_trend = 0;
    var avg_industry_trend = 0;


    $scope.getAveragesForCscWifi = function (elem) {
        var maxHaxisValue = 0;
        $scope.averageGraphData = {
            type: "BarChart",
            isVisible: true,
            options: {
                "colors": ["#304799", "#F3F3F3", "#00ADEE", "#75BF44"],
                "tooltip": {
                    "trigger": "none"
                },
                "isStacked": "true",
                "legend": "none",
                "tooltip": {
                    "textStyle": {
                        "fontSize": 10
                    }
                },
                "vAxis": {
                    "format": 'short',
                    "textStyle": {
                        "fontSize": 7,
                        "bold": true
                    },
                    "gridlines": {
                        "color": "f3f3f3",
                    }
                },
                "hAxis": {
                    "format": 'short',
                    "textStyle": {
                        "fontSize": 10,
                        "bold": true
                    },
                    "gridlines": {
                        "count": 5,
                        "color": "#f3f3f3"
                    },
                    "viewWindow": {
                        "min": 0,
                        "max": 20
                    }
                },
                "bar": {
                    "groupWidth": 10
                },
                "chartArea": {},
                "seriesType": "bars",
                "series": { 1: { "tooltip": false, "enableInteractivity": false }, 2: { "type": "line", "lineWidth": 1 }, 3: { "type": "line", "lineWidth": 1 } }
            },
            data: {},
            view: {
                columns: [0, 1, 2, 3, 4]
            }
        };

        if (elem == 'repeat_rate') {
            $scope.averageGraphData.options.hAxis.minValue = 0,
            $scope.averageGraphData.options.hAxis.maxValue = 25;
            $scope.averageGraphData.options.hAxis.format = '#\'%\'';
        } else {
            $scope.averageGraphData.options.hAxis.format = 'short'
        }

        $scope.averageGraphData.data = {};

        var postData = {
            //queryObject: angular.copy($scope.$parent.filters)
            queryObject: $scope.$parent.filters
        };
        //postData.queryObject.tsType = averageGraphDatatsType;
        if ($scope.averageGraphData.isVisible) {

            $scope.averageGraphData.data.cols = [];
            $scope.averageGraphData.data.rows = [];

            $scope.averageGraphData.data.cols.push({
                'id': 'store',
                'type': 'string',
                'label': 'Store'
            });

            $scope.averageGraphData.data.cols.push({
                'id': elem,
                'type': 'number',
                'label': elem.split("_").join(" ").capitalize()
            });

            $scope.averageGraphData.data.cols.push({
                'id': '',
                'type': 'number',
                'label': ''
            });


            $scope.averageGraphData.data.cols.push({
                'id': 'brand',
                'type': 'number',
                'label': 'Brand'
            });

            $scope.averageGraphData.data.cols.push({
                'id': 'industry',
                'type': 'number',
                'label': 'Industry'
            });



            ajaxCall.post(crossStoreAvgDataUrls[elem], {
                queryObject: $scope.$parent.filters,
                type: 'brand'
            }).then(function (response) {
                if (response.data && response.data.data) {
                    avg_brand_trend = response.data.data[0].count;
                    if (response.data.data[0].count > maxHaxisValue) {
                        maxHaxisValue = parseInt(response.data.data[0].count);
                    }
                } else {
                    avg_brand_trend = 0;
                }
                ajaxCall.post(crossStoreAvgDataUrls[elem], {
                    queryObject: $scope.$parent.filters,
                    type: 'industry'
                }).then(function (response) {
                    if (response.data && response.data.data) {
                        avg_industry_trend = response.data.data[0].count;
                        if (response.data.data[0].count > maxHaxisValue) {
                            maxHaxisValue = parseInt(response.data.data[0].count);
                        }
                    } else {
                        avg_industry_trend = 0;
                    }

                    ajaxCall.post(crossStoreAvgDataUrls[elem], postData).then(function (response) {
                        if (response.data) {
                            $scope.wifiMetrics.crossStoreComparison.chartData = response.data.data;
                            //$scope.averageGraphData.options.hAxis.maxValue = $scope.wifiMetrics.crossStoreComparison.chartData[0].count;

                            if ($scope.wifiMetrics.crossStoreComparison.chartData.length > 10) {
                                $scope.viewMoreAveragesFlag = true;
                                var len = 10;
                            } else {
                                $scope.viewMoreAveragesFlag = false;
                                $scope.viewLessAveragesFlag = false;
                                var len = $scope.wifiMetrics.crossStoreComparison.chartData.length;
                            }

                            $scope.averageGraphData.data.rows.push({ c: [{ v: '' }, { v: 0 }, { v: 0 }, { v: avg_brand_trend }, { v: avg_industry_trend }] });
                            var rowdata = []
                            for (var i = 0; i < len; i++) {

                                if (parseFloat($scope.wifiMetrics.crossStoreComparison.chartData[i].count) > maxHaxisValue) {
                                    maxHaxisValue = parseFloat($scope.wifiMetrics.crossStoreComparison.chartData[i].count);
                                }

                                obj = {};
                                obj.c = []

                                obj.c.push({
                                    'v': $scope.wifiMetrics.crossStoreComparison.chartData[i].shop_name
                                });
                                if (elem == 'repeat_rate') {
                                    obj.c.push({
                                        'v': parseFloat($scope.wifiMetrics.crossStoreComparison.chartData[i].count).round(1),
                                        'f': parseFloat($scope.wifiMetrics.crossStoreComparison.chartData[i].count).round(1) + ' %'
                                    });

                                    obj.c.push({
                                        'v': parseFloat($scope.wifiMetrics.crossStoreComparison.chartData[0].count).round(1) - parseFloat($scope.wifiMetrics.crossStoreComparison.chartData[i].count).round(1),
                                    });

                                    obj.c.push({
                                        'v': parseFloat(avg_brand_trend).round(1),
                                        'f': parseFloat(avg_brand_trend).round(1) + ' %'
                                    });

                                    obj.c.push({
                                        'v': parseFloat(avg_industry_trend).round(1),
                                        'f': parseFloat(avg_industry_trend).round(1) + ' %'
                                    });
                                } else if (elem == 'daily_time_spent') {
                                    obj.c.push({
                                        'v': $scope.wifiMetrics.crossStoreComparison.chartData[i].count,
                                        'f': $scope.wifiMetrics.crossStoreComparison.chartData[i].count + ' min'
                                    });

                                    obj.c.push({
                                        'v': $scope.wifiMetrics.crossStoreComparison.chartData[0].count - $scope.wifiMetrics.crossStoreComparison.chartData[i].count
                                    });

                                    obj.c.push({
                                        'v': avg_brand_trend,
                                        'f': avg_brand_trend + ' min'
                                    });

                                    obj.c.push({
                                        'v': avg_industry_trend,
                                        'f': avg_industry_trend + ' min'
                                    });
                                } else if (elem == 'daily_data_used') {
                                    obj.c.push({
                                        'v': $scope.wifiMetrics.crossStoreComparison.chartData[i].count,
                                        'f': $scope.wifiMetrics.crossStoreComparison.chartData[i].count + ' mb'
                                    });
                                    obj.c.push({
                                        'v': $scope.wifiMetrics.crossStoreComparison.chartData[0].count - $scope.wifiMetrics.crossStoreComparison.chartData[i].count
                                    });
                                    obj.c.push({
                                        'v': avg_brand_trend,
                                        'f': avg_brand_trend + ' mb'
                                    });

                                    obj.c.push({
                                        'v': avg_industry_trend,
                                        'f': avg_industry_trend + ' mb'
                                    });
                                } else {
                                    obj.c.push({
                                        'v': $scope.wifiMetrics.crossStoreComparison.chartData[i].count
                                    });
                                    obj.c.push({
                                        'v': $scope.wifiMetrics.crossStoreComparison.chartData[0].count - $scope.wifiMetrics.crossStoreComparison.chartData[i].count
                                    });
                                    obj.c.push({
                                        'v': avg_brand_trend
                                    });

                                    obj.c.push({
                                        'v': avg_industry_trend
                                    });
                                }

                                $scope.averageGraphData.data.rows.push(obj);

                            }
                            $scope.averageGraphData.data.rows.push({ c: [{ v: '' }, { v: 0 }, { v: 0 }, { v: avg_brand_trend }, { v: avg_industry_trend }] });

                        }

                        $scope.averageGraphData.options.hAxis.viewWindowMode = 'explicit';
                        $scope.averageGraphData.options.hAxis.viewWindow.min = '0';
                        $scope.averageGraphData.options.hAxis.viewWindow.max = maxHaxisValue;

                        $scope.averageGraphData.options.height = (12 * 30) + 100;
                        $scope.averageGraphData.options.chartArea.height = 12 * 30;
                    });
                    $scope.wifiMetrics.crossStoreComparison[elem] = $scope.averageGraphData;
                });
            });
        } else {
            chartDataObject[elem].isVisible = true;
            delete $scope.wifiMetrics.crossStoreComparison[elem];
        }
    }

    $scope.viewMoreAverages = function (elem) {
        $scope.wifiMetrics.crossStoreComparison[elem].data.rows.splice(-1, 1);
        var rowdata = []
        for (var i = 10, len = $scope.wifiMetrics.crossStoreComparison.chartData.length; i < len; i++) {

            obj = {};
            obj.c = []

            obj.c.push({
                'v': $scope.wifiMetrics.crossStoreComparison.chartData[i].shop_name
            });
            if (elem == 'repeat_rate') {
                obj.c.push({
                    'v': parseFloat($scope.wifiMetrics.crossStoreComparison.chartData[i].count).round(1),
                    'f': parseFloat($scope.wifiMetrics.crossStoreComparison.chartData[i].count).round(1) + ' %'
                });
                obj.c.push({
                    'v': parseFloat($scope.wifiMetrics.crossStoreComparison.chartData[0].count).round(1) - parseFloat($scope.wifiMetrics.crossStoreComparison.chartData[i].count).round(1),
                });
                obj.c.push({
                    'v': parseFloat(avg_brand_trend).round(1),
                    'f': parseFloat(avg_brand_trend).round(1) + ' %'
                });

                obj.c.push({
                    'v': parseFloat(avg_industry_trend).round(1),
                    'f': parseFloat(avg_industry_trend).round(1) + ' %'
                });
            } else if (elem == 'daily_time_spent') {
                obj.c.push({
                    'v': $scope.wifiMetrics.crossStoreComparison.chartData[i].count,
                    'f': $scope.wifiMetrics.crossStoreComparison.chartData[i].count + ' min'
                });
                obj.c.push({
                    'v': $scope.wifiMetrics.crossStoreComparison.chartData[0].count - $scope.wifiMetrics.crossStoreComparison.chartData[i].count
                });
                obj.c.push({
                    'v': avg_brand_trend,
                    'f': avg_brand_trend + ' min'
                });

                obj.c.push({
                    'v': avg_industry_trend,
                    'f': avg_industry_trend + ' min'
                });
            } else if (elem == 'daily_data_used') {
                obj.c.push({
                    'v': $scope.wifiMetrics.crossStoreComparison.chartData[i].count,
                    'f': $scope.wifiMetrics.crossStoreComparison.chartData[i].count + ' mb'
                });
                obj.c.push({
                    'v': $scope.wifiMetrics.crossStoreComparison.chartData[0].count - $scope.wifiMetrics.crossStoreComparison.chartData[i].count
                });
                obj.c.push({
                    'v': avg_brand_trend,
                    'f': avg_brand_trend + ' mb'
                });

                obj.c.push({
                    'v': avg_industry_trend,
                    'f': avg_industry_trend + ' mb'
                });
            } else {
                obj.c.push({
                    'v': $scope.wifiMetrics.crossStoreComparison.chartData[i].count
                });
                obj.c.push({
                    'v': $scope.wifiMetrics.crossStoreComparison.chartData[0].count - $scope.wifiMetrics.crossStoreComparison.chartData[i].count
                });
                obj.c.push({
                    'v': avg_brand_trend
                });

                obj.c.push({
                    'v': avg_industry_trend
                });
            }
            $scope.wifiMetrics.crossStoreComparison[elem].data.rows.push(obj);
            $scope.wifiMetrics.crossStoreComparison[elem].options.height += 30;
            $scope.wifiMetrics.crossStoreComparison[elem].options.chartArea.height += 30;

        }

        $scope.wifiMetrics.crossStoreComparison[elem].data.rows.push({ c: [{ v: '' }, { v: 0 }, { v: 0 }, { v: avg_brand_trend }, { v: avg_industry_trend }] });

        $scope.viewMoreAveragesFlag = false;
        $scope.viewLessAveragesFlag = true;
    }

    $scope.viewLessAverages = function (elem) {
        $scope.wifiMetrics.crossStoreComparison[elem].data.rows = $scope.wifiMetrics.crossStoreComparison[elem].data.rows.slice(0, 11);
        $scope.wifiMetrics.crossStoreComparison[elem].data.rows.push({ c: [{ v: '' }, { v: 0 }, { v: 0 }, { v: avg_brand_trend }, { v: avg_industry_trend }] });
        $scope.wifiMetrics.crossStoreComparison[elem].options.height = (12 * 30) + 100;
        $scope.wifiMetrics.crossStoreComparison[elem].options.chartArea.height = 12 * 30;
        $scope.viewMoreAveragesFlag = true;
        $scope.viewLessAveragesFlag = false;
    }

    if (validateQueryObject($scope.$parent.filters)) {
        $rootScope.$broadcast('params-updated');
    }
}])