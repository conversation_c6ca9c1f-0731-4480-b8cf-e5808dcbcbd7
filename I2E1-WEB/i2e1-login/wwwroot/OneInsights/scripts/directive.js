analyticsApp.directive('pageTitle', ['$rootScope', '$timeout',
  function ($rootScope, $timeout) {
      return {
          link: function (scope, element) {

              var listener = function (event, toState) {

                  var title = 'i2e1 - Analytics';
                  if (toState && toState.pageTitle) title = toState.pageTitle;

                  $timeout(function () {
                      element.text(title);
                  }, 0, false);
              };

              $rootScope.$on('$stateChangeSuccess', listener);
          }
      };
  }
]);
analyticsApp.directive('navCollapseToggler', function ($document) {

    function toggleNav(show) {
        $('body').toggleClass('nav-shown', show);
    }

    var toggleListeners = {
        'click': function ($el) {
            $el.on('click', function (evt) {
                toggleNav();
                evt.preventDefault();
            });
        },

        'swipe': function ($el) {

            // fixing unselectable text problem
            // see https://github.com/mattbryson/TouchSwipe-Jquery-Plugin/issues/149
            function isMobile() {
                try {
                    $document[0].createEvent('TouchEvent');
                    return true;
                } catch (e) {
                    return false;
                }
            }

            if (isMobile()) {
                $el.swipe({
                    swipeLeft: function () {
                        toggleNav(false);
                    },
                    swipeRight: function () {
                        toggleNav(true);
                    }
                });
            }
        }
    };

    return {
        restrict: 'A',
        scope: {
            type: '@'
        },
        link: function (scope, $el, attrs) {
            toggleListeners[scope.type]($el, scope);
        }
    }
});

analyticsApp.directive('multiselectDropdown', function ($timeout, ajaxCall) {
    return {
        restrict: 'E',
        scope: {
            model: '=',
            options: '=',
            disabled: "=",
            labelText: '@',
            onchangecallback: '&'
        },

        template: '<div class="btn-group" uib-dropdown>' +
            '<button type="button" class="btn btn-transparent {{disabled ? \'disabled\' : \'\'}}" uib-dropdown-toggle>' +
            '{{labelText}} <i class="fa fa-angle-down" aria-hidden="true"></i>' +
            '</button>' +
            '<ul class="dropdown-menu dropdown-menu-right custom_scroll" uib-dropdown-menu role="menu" aria-labelledby="btn-append-to-single-button" ng-click="$event.stopPropagation()">' +
            '<li ng-if="!isAllSelected" role="menuitem" style=\'cursor:pointer;\'><a data-ng-click=\'selectAll()\'><span class="glyphicon glyphicon-uncheck" aria-hidden=\'true\'></span> Select All</a></li>' +
            '<li ng-if="isAllSelected" role="menuitem" style=\'cursor:pointer;\'><a data-ng-click=\'deselectAll()\' class="selected"><span class="glyphicon glyphicon-ok" aria-hidden=\'true\'></span> Select All</a></li>' +
            '<li role="menuitem" style=\'cursor:pointer;\' data-ng-repeat=\'option in options track by $index\'><a data-ng-click=\'toggleSelectItem(option)\' data-ng-class=\'getSelectedClass(option)\' ><span data-ng-class=\'getClassName(option)\' aria-hidden=\'true\'></span> {{option}}</a></li>' +
            '</ul>' +
            '</div>',

        controller: function ($scope) {

            ajaxCall.get('/Client/GetFilterOptions?filter=shop_city').then(function (response) {
                $scope.options = angular.copy(response.data);
                $scope.$parent.filters.cities = angular.copy(response.data);
                console.log('loaded......');
            });

            $scope.model = [];
            $scope.openDropdown = function () {

                $scope.open = !$scope.open;

            };

            $scope.selectAll = function () {

                $scope.model = [];

                angular.forEach($scope.options, function (item, index) {

                    $scope.model.push(item);

                });

                $scope.isAllSelected = true;
                $scope.labelText = 'Multiple Cities';
            };

            $scope.deselectAll = function () {

                $scope.model = [];

                $scope.isAllSelected = false;

            };

            $scope.toggleSelectItem = function (option) {

                var intIndex = -1;

                angular.forEach($scope.model, function (item, index) {

                    if (item == option) {

                        intIndex = index;

                    }

                });

                if (intIndex >= 0) {
                    $scope.model.splice(intIndex, 1);
                    if ($scope.model.length == 1) {
                        $scope.labelText = $scope.model[0];
                    } else if ($scope.model.length > 1) {
                        $scope.labelText = 'Multiple Cities';
                    } else if ($scope.model.length <= 0) {
                        $scope.labelText = 'Select City';
                    }
                } else {
                    $scope.model.push(option);
                    if ($scope.model.length == 1) {
                        $scope.labelText = $scope.model[0];
                    } else if ($scope.model.length > 1) {
                        $scope.labelText = 'Multiple Cities';
                    } else if ($scope.model.length <= 0) {
                        $scope.labelText = 'Select City';
                    }
                }

            };

            $scope.getSelectedClass = function (option) {

                var varClassName = '';

                angular.forEach($scope.model, function (item, index) {

                    if (item == option) {

                        varClassName = 'selected';

                    }

                });

                if ($scope.model.length == $scope.options.length) {
                    $scope.isAllSelected = true;
                    $scope.labelText = 'All Cities';
                } else {
                    $scope.isAllSelected = false;
                    if ($scope.model.length == 0) {
                        $scope.labelText = 'Select City';
                    }
                }
                return (varClassName);
            };

            $scope.getClassName = function (option) {

                var varClassName = 'glyphicon glyphicon-uncheck';

                angular.forEach($scope.model, function (item, index) {

                    if (item == option) {

                        varClassName = 'glyphicon glyphicon-ok';

                    }

                });
                return (varClassName);
            };

        },
        link: function (scope, element, attrs) {
            console.log('Watch......');
            scope.$watch('model.length', function (newValue) {
                if (newValue)
                    scope.onchangecallback();
            }, true);
        }
            
        
    }

});

analyticsApp.directive('multiLocationSelectDropdown', function () {
    return {
        restrict: 'E',
        scope: {
            model: '=',
            options: '=',
            disabled: '=',
            labelText: '@',
            onchangecallback: '&'
        },

        template: '<div class="btn-group" uib-dropdown>' +
            '<button type="button" class="btn btn-transparent {{disabled ? \'disabled\' : \'\'}}" uib-dropdown-toggle>' +
            '{{labelText}} <i class="fa fa-angle-down" aria-hidden="true"></i>' +
            '</button>' +
            '<ul class="dropdown-menu dropdown-menu-right custom_scroll" uib-dropdown-menu role="menu" aria-labelledby="btn-append-to-single-button" ng-click="$event.stopPropagation()">' +
            '<li ng-if="!isAllSelected" role="menuitem" style=\'cursor:pointer;\'><a data-ng-click=\'selectAll()\'><span class="glyphicon glyphicon-uncheck" aria-hidden=\'true\'></span> Select All</a></li>' +
            '<li ng-if="isAllSelected" role="menuitem" style=\'cursor:pointer;\'><a data-ng-click=\'deselectAll()\' class="selected"><span class="glyphicon glyphicon-ok" aria-hidden=\'true\'></span> Select All</a></li>' +
            '<li role="menuitem" style=\'cursor:pointer;\' data-ng-repeat=\'option in options track by $index\'><a data-ng-click=\'toggleSelectItem(option.nasid)\' data-ng-class=\'getSelectedClass(option.nasid)\' ><span data-ng-class=\'getClassName(option.nasid)\' aria-hidden=\'true\'></span> {{option.storeName}}</a></li>' +
            '</ul>' +
            '</div>',

        controller: function ($scope) {
            $scope.model = [];
            $scope.openDropdown = function () {

                $scope.open = !$scope.open;

            };

            $scope.selectAll = function () {

                $scope.model = [];

                for (var i = 0, len = $scope.options.length; i < len; i++) {
                    $scope.model.push($scope.options[i].nasid);
                }

                $scope.isAllSelected = true;
                $scope.labelText = 'Multiple Stores';
            };

            $scope.deselectAll = function () {

                $scope.model = [];

                $scope.isAllSelected = false;

            };

            $scope.toggleSelectItem = function (option) {

                var intIndex = -1;

                for (var i = 0, len = $scope.model.length; i < len; i++) {
                    if ($scope.model[i] == option) {
                        intIndex = i;
                    }

                }

                if (intIndex >= 0) {
                    $scope.model.splice(intIndex, 1);
                    if ($scope.model.length == 1) {
                        $scope.labelText = $scope.options.filter(function (v) {
                            return v.nasid === $scope.model[0]; // Filter out the appropriate one
                        })[0].storeName;
                    } else if ($scope.model.length == $scope.options.length) {
                        $scope.labelText = 'All Stores';
                    } else if ($scope.model.length > 1) {
                        $scope.labelText = 'Multiple Stores';
                    } else if ($scope.model.length <= 0) {
                        $scope.labelText = 'Select Store';
                    }
                } else {
                    $scope.model.push(option);
                    if ($scope.model.length == 1) {
                        $scope.labelText = $scope.options.filter(function (v) {
                            return v.nasid === $scope.model[0]; // Filter out the appropriate one
                        })[0].storeName;
                    } else if ($scope.model.length == $scope.options.length) {
                        $scope.labelText = 'All Stores';
                    } else if ($scope.model.length > 1) {
                        $scope.labelText = 'Multiple Stores';
                    } else if ($scope.model.length <= 0) {
                        $scope.labelText = 'Select Store';
                    }
                }

                
                
            };

            $scope.getSelectedClass = function (option) {

                var varClassName = '';

                for (var i = 0, len = $scope.model.length; i < len; i++) {
                    if ($scope.model[i] == option) {
                        varClassName = 'selected';
                    }
                }

                if ($scope.model.length == $scope.options.length) {
                    $scope.isAllSelected = true;
                    $scope.labelText = 'All Stores';
                } else {
                    $scope.isAllSelected = false;
                    if ($scope.model.length == 0) {
                        $scope.labelText = 'Select Store';
                    }
                }
                return (varClassName);
            };

            $scope.getClassName = function (option) {

                var varClassName = 'glyphicon glyphicon-uncheck';

                for (var i = 0, len = $scope.model.length; i < len; i++) {
                    if ($scope.model[i] == option) {
                        varClassName = 'glyphicon glyphicon-ok';
                    }
                }

                return (varClassName);
            };

        },
        link: function (scope, element, attrs) {
            scope.$watch('model', function (newValue, oldValue) {
                if (newValue)
                    scope.onchangecallback();
            }, true);
        }

    }

});

analyticsApp.directive('drawstatscircle', function () {
    return {
        restrict: 'A',
        scope: {
            totaltext: '@',
            circleobject: '@'
        },
        link: function ($scope, el, attrs, ctrl) {
            var dataTable = JSON.parse(attrs.circleobject).data;
            var arr = dataTable.map(function (obj) {
                var rObj = [];
                rObj.push(obj.value);
                return rObj;
            });

            var total = arr.reduce(function (a, b) {
                return parseInt(a) + parseInt(b);
            });


            attrs.$observe('circleobject', function (totaltext) {
                setTimeout(function () {
                    el.html('');
                    var options = {
                        percent: attrs.percent || 0,
                        size: attrs.size || 100,
                        lineWidth: attrs.line || 20,
                        lineCap: attrs.linecap || 'square',
                        rotate: attrs.rotate || 0,
                        bgColor: attrs.bgcolor || '#f5f5f5',
                        fhColor: attrs.fhcolor || '#acacac',
                        percentText: attrs.percenttext || 0,
                        totalText: attrs.totaltext || 0,
                        legend: attrs.legend || 0
                    }

                    var canvas = document.createElement('canvas');

                    if (typeof (G_vmlCanvasManager) !== 'undefined') {
                        G_vmlCanvasManager.initElement(canvas);
                    }

                    var ctx = canvas.getContext('2d');

                    ctx.mozImageSmoothingEnabled = false;
                    ctx.webkitImageSmoothingEnabled = false;
                    ctx.msImageSmoothingEnabled = false;
                    ctx.imageSmoothingEnabled = false;

                    canvas.width = canvas.height = options.size;
                    el.append(canvas);


                    if (options.percentText) {
                        percentTextWidth = ctx.measureText(options.percentText).width;
                        totalTextWidth = ctx.measureText(options.totalText).width;
                        ctx.font = '24px Roboto';
                        ctx.fillText(options.percentText, canvas.width / 2 - percentTextWidth, canvas.height / 2 - 12);
                        ctx.font = '14px Roboto';
                        ctx.fillText(options.totalText, canvas.width / 2 - totalTextWidth / 2, canvas.height / 2 + 12);
                    }
                    if (options.totalText) {
                        totalTextWidth = ctx.measureText(options.totalText).width;
                        ctx.font = '24px Roboto';
                        ctx.fillText(options.totalText, canvas.width / 2 - totalTextWidth, canvas.height / 2);
                    }

                    if (options.legend) {
                        legendWidth = ctx.measureText(options.legend).width;
                        ctx.font = '10px Roboto';
                        ctx.fillText(options.legend, canvas.width / 2 - legendWidth / 2, canvas.height / 2 + 5);
                    }

                    ctx.translate(options.size / 2, options.size / 2);
                    ctx.rotate((-1 / 2 + options.rotate / 180) * Math.PI);

                    var radius = (options.size - options.lineWidth) / 2;

                    var drawCircle = function (color, lineWidth, startangle, percent) {
                        percent = Math.min(Math.max(0, percent || 1), 1);
                        ctx.beginPath();
                        ctx.arc(0, 0, radius, startangle, Math.PI * 2 * percent, false);
                        ctx.lineCap = options.lineCap //'round'; // butt, round or square
                        ctx.lineWidth = lineWidth;
                        ctx.strokeStyle = color;
                        ctx.stroke();
                    };

                    var startangle = 0;
                    var hexColor = ['#ff9900', '#109618', '#3366cc', '#dc3912', '#000000']
                    angular.forEach(arr, function (value, index) {
                        var hex = '#' + Math.floor(Math.random() * 16777215).toString(16);
                        percent = (value * 100) / total;
                        drawCircle(dataTable[index].color || '#' + Math.floor(Math.random() * 16777215).toString(16), options.lineWidth, startangle, percent);
                        startangle += (Math.PI * 2 * percent / 100);
                    })

                }, 10);
            });
        }
    };
});

analyticsApp.directive('drawstackedbar', function ($timeout) {
    return {
        restrict: 'A',
        scope: {
            baroject: '='
        },
        link: function ($scope, el, attrs, ctrl) {
            $scope.$watch('baroject', function (newValue, oldValue) {
                if (newValue) {
                    var dataTable = $scope.baroject;
                    var arr = dataTable.map(function (obj) {
                        var rObj = [];
                        rObj.push(obj.value);
                        return rObj;
                    });

                    var total = arr.reduce(function (a, b) {
                        return parseInt(a) + parseInt(b);
                    });

                    if (attrs.orientation == "horizontal")
                        var stackedBarSize = attrs.width;
                    else if (attrs.orientation == "vertical")
                        var stackedBarSize = attrs.height;
                    else
                        var stackedBarSize = 100;
                    setTimeout(function () {
                        el.html('');
                        var options = {
                            barWidth: attrs.width || 10,
                            barHeight: attrs.height || 10,
                            lineCap: attrs.linecap || 'square',
                            bgColor: attrs.bgcolor || '#f5f5f5',
                            fhColor: attrs.fhcolor || '#acacac',
                        }

                        var canvas = document.createElement('canvas');

                        if (typeof (G_vmlCanvasManager) !== 'undefined') {
                            G_vmlCanvasManager.initElement(canvas);
                        }

                        var ctx = canvas.getContext('2d');
                        canvas.width = options.barWidth;
                        canvas.height = options.barHeight;
                        el.append(canvas);

                        var drawBar = function (color, startX, startY, endX, endY) {
                            ctx.beginPath();
                            ctx.strokeStyle = color;
                            ctx.moveTo(startX, startY);
                            ctx.lineTo(endX, endY);
                            ctx.stroke();
                            ctx.fillStyle = color;

                            if (attrs.orientation == "horizontal")
                                ctx.fillRect(startX, startY, endX, options.barHeight);
                            else if (attrs.orientation == "vertical")
                                ctx.fillRect(startX, startY, options.barWidth, endY);
                            else
                                ctx.fillRect(startX, startY, options.barWidth, endY);
                        };

                        var startX = 0;
                        var startY = 0;
                        var endX = 0;
                        var endY = 0;
                        var promise = $timeout();
                        angular.forEach(arr, function (value, index) {
                            promise = promise.then(function() {
                                var hex = '#' + Math.floor(Math.random() * 16777215).toString(16);
                                percent = (parseInt(value) * 100) / parseInt(total);
                                increase = (stackedBarSize * percent) / 100;

                                if (attrs.orientation == "horizontal")
                                    endX = endX + increase;
                                else if (attrs.orientation == "vertical")
                                    endY = endY + increase;
                                else
                                    endY = endY + increase;
                                
                                drawBar(dataTable[index].color || '#' + Math.floor(Math.random() * 16777215).toString(16), startX, startY, endX, endY)
                                if (attrs.orientation == "horizontal")
                                    startX = startX + increase;
                                else if (attrs.orientation == "vertical")
                                    startY = startY + increase;
                                else
                                    startY = startY + increase;
                                return $timeout(10);
                            });

                        })

                    }, 10);
                }
            });
        }
    };
});

analyticsApp.directive('drawDonuta', function () {
    return {
        restrict: 'A',

        link: function ($scope, el, attrs, ctrl) {
            setTimeout(function () {
                var rotate = 0,
                    skew;
                angular.forEach($(el).find('.slice'), function (slice) {
                    skew = $(slice).data('percent') - 90;
                    slice.style['background-color'] = $(slice).data('color');

                    slice.style['-ms-transition'] = '-ms-transform 2s ease';
                    slice.style['-moz-transition'] = '-moz-transform 2s ease';
                    slice.style['-o-transition'] = '-o-transform 2s ease';
                    slice.style['-webkit-transition'] = '-webkit-transform 2s ease';
                    slice.style['transition'] = 'transform 2s ease';

                    slice.style['-ms-transform'] = 'rotate(' + rotate + 'deg) skewY(' + skew + 'deg)';
                    slice.style['-moz-transform'] = 'rotate(' + rotate + 'deg) skewY(' + skew + 'deg)';
                    slice.style['-o-transform'] = 'rotate(' + rotate + 'deg) skewY(' + skew + 'deg)';
                    slice.style['-webkit-transform'] = 'rotate(' + rotate + 'deg) skewY(' + skew + 'deg)';
                    slice.style['transform'] = 'rotate(' + rotate + 'deg) skewY(' + skew + 'deg)';

                    slice.style['-webkit-backface-visibility'] = 'hidden';  /* Chrome, Safari, Opera */
                    slice.style['backface-visibility'] = 'hidden';

                    rotate = rotate + $(slice).data('percent');
                });
            }, 50);
        }
    }
});

analyticsApp.directive('drawDonut', function () {
    var setSlice = function (slice, rotate, skew) {
        slice.style['background-color'] = $(slice).data('color');
        slice.style['border'] = '2px solid ' + $(slice).data('color');
        
        slice.style['-ms-transition'] = '-ms-transform 2s ease';
        slice.style['-moz-transition'] = '-moz-transform 2s ease';
        slice.style['-o-transition'] = '-o-transform 2s ease';
        slice.style['-webkit-transition'] = '-webkit-transform 2s ease';
        slice.style['transition'] = 'transform 2s ease';
        
        slice.style['-ms-transform'] = 'rotate(' + rotate + 'deg) skewY(' + skew + 'deg)';
        slice.style['-moz-transform'] = 'rotate(' + rotate + 'deg) skewY(' + skew + 'deg)';
        slice.style['-o-transform'] = 'rotate(' + rotate + 'deg) skewY(' + skew + 'deg)';
        slice.style['-webkit-transform'] = 'rotate(' + rotate + 'deg) skewY(' + skew + 'deg)';
        slice.style['transform'] = 'rotate(' + rotate + 'deg) skewY(' + skew + 'deg)';

        slice.style['-webkit-backface-visibility'] = 'hidden';  /* Chrome, Safari, Opera */
        slice.style['backface-visibility'] = 'hidden';

    }

    return {
        restrict: 'A',
        scope: {
            donutdata: '=',
        },
        template: '<div class="donut-chart"><div ng-repeat="obj in donutdata" class="slice {{obj.name}}" ' +
            'data-name="{{obj.name}}" data-degree="{{obj.deg}}" ' +
            'data-color="{{obj.color}}" ' +
            'data-legend="{{obj.precent | round:1}}%"></div>' +
            '<div class="chart-center"><span ></span></div></div>',
        controller: function ($scope) {
            $scope.$watch('donutdata', function (value) {
                var donutData = $scope.donutdata;
                var total = 0;
                angular.forEach(donutData, function (obj) {
                    total += parseInt(obj.value);
                });

                angular.forEach(donutData, function (obj) {
                    obj.deg = parseInt(obj.value) / total * 360;
                    obj.precent = parseInt(obj.value) / total * 100;
                });
            });
        },
        link: function ($scope, el, attrs, ctrl) {
            $scope.$watch('donutdata', function (value) {
                setTimeout(function () {
                    var rotate = 0,
                        skew;
                    angular.forEach($(el).find('.slice'), function (slice) {
                        var degree = $(slice).data('degree');
                        skew = degree - 90;
                        if (degree > 300) {
                            var nextSlice = $('<div class="slice ' +
                                $(slice).data('name') + '" data-degree="' +
                                (degree - 300) + '" data-color="' +
                                $(slice).data('color') + '"></div><div class="slice ' +
                                $(slice).data('name') + '" data-degree="150" data-name="'+
                                $(slice).data('name') + '" data-color="' +
                                $(slice).data('color') + '" data-legend="' +
                                $(slice).data('legend') + '"></div>');

                            $(slice).before(nextSlice); // enforcing initial state of transform
                            nextSlice.css('transform');
                            setSlice(nextSlice[0], rotate, degree - 300 - 90);
                            rotate = rotate + degree - 300;
                            setSlice(nextSlice[1], rotate, 150 - 90);
                            rotate = rotate + 150;
                            $(slice).attr('data-degree', 150);
                            degree = 150;
                            skew = 150 - 90;
                        } else if (degree > 150) {
                            var nextSlice = $('<div class="slice ' +
                                $(slice).data('name') + '" data-degree="' +
                                (degree - 150) + '" data-name="' +
                                $(slice).data('name') + '"  data-color="' +
                                $(slice).data('color') + '" data-legend="' +
                                $(slice).data('legend') + '"></div>');
                            $(slice).before(nextSlice); // enforcing initial state of transform
                            nextSlice.css('transform');
                            setSlice(nextSlice[0], rotate, degree - 150 - 90);

                            rotate = rotate + degree - 150;
                            $(slice).attr('data-degree', 150);
                            degree = 150;
                            skew = 150 - 90;
                        }
                        $('.slice').bind('mouseenter', function (e) {
                            var v = $(this).data('name') + ': ' + $(this).data('legend');
                            $(this).siblings(".chart-center").html(v);
                            $(this).parent().parent().siblings(".metric-icon").css('display', 'none');
                            $(this).closest("td").find(".table-separator").find('.metric-icon').css('display', 'none');
                        });

                        $('.slice').bind('mouseleave', function () {
                            $(this).siblings(".chart-center").html('');
                            $(this).parent().parent().siblings(".metric-icon").css('display', 'block');
                            $(this).closest("td").find(".table-separator").find('.metric-icon').css('display', 'block');
                        });

                        setSlice(slice, rotate, skew);
                        rotate = rotate + degree;
                    });
                }, 50);
            });
        }
    }
});

analyticsApp.directive('showBenfitsDataCaptured', ['service', 'ajaxCall', function (service, ajaxCall) {
    return {
        restrict: 'E',
        transclude: true,
        scope: {
            data_captured: '=',
            postData: '@'
        },

        template: '<div class="row widget widget-card">' +
            '    <header class="widget-title">How good is your marketing potential through i2e1?</header>' +
            '    <div class="col-md-4 metric-stat-section">' +
            '        <h6 class="one-third-heading metric-name"><span>i2e1</span> users in brand\'s vicninty</h6>' +
            '        <div class="metric-icon"><img src="images/mobile.svg" alt="Phone Detected In Vicinity" /></div>' +
            '        <h5 class="metric-stat bounceInText {{data_captured.t_phone_detected_vicinity == \'Data not available\' ? \'metric-notavailable\':\'\'}}" ng-show="data_captured.t_phone_detected_vicinity || data_captured.t_phone_detected_vicinity == 0">{{data_captured.t_phone_detected_vicinity}}</h5>' +
            '        <h5 class="metric-stat" ng-show="!data_captured.t_phone_detected_vicinity && data_captured.t_phone_detected_vicinity != 0"><img src="images/data-loader.gif" /></h5>' +
            '    </div>' +
            '    <div class="col-md-4 metric-stat-section">' +
            '        <h6 class="one-third-heading metric-name"><span>i2e1</span> users that visited your brand</h6>' +
            '        <div class="metric-icon"><img src="images/mobile.svg" alt="Phone numbers captured" /></div>' +
            '        <h5 class="metric-stat bounceInText {{data_captured.t_phone_detected_store == \'Data not available\' ? \'metric-notavailable\':\'\'}}" ng-show="data_captured.t_phone_detected_store || data_captured.t_phone_detected_store == 0">{{data_captured.t_phone_detected_store}}</h5>' +
            '        <h5 class="metric-stat" ng-show="!data_captured.t_phone_detected_store && data_captured.t_phone_detected_store != 0"><img src="images/data-loader.gif" /></h5>' +
            '    </div>' +
            '    <div class="col-md-4 metric-stat-section">' +
            '        <h6 class="one-third-heading metric-name"><span>i2e1</span> users that logged on your Wi-Fi</h6>' +
            '        <div class="metric-icon"><img src="images/mobile.svg" alt="Phone numbers captured" /></div>' +
            '        <h5 class="metric-stat bounceInText {{data_captured.t_phone_detected_wifi == \'Data not available\' ? \'metric-notavailable\':\'\'}}" ng-show="data_captured.t_phone_detected_wifi || data_captured.t_phone_detected_wifi == 0">{{data_captured.t_phone_detected_wifi}}</h5>' +
            '        <h5 class="metric-stat" ng-show="!data_captured.t_phone_detected_wifi && data_captured.t_phone_detected_wifi != 0"><img src="images/data-loader.gif" /></h5>' +
            '    </div> ' +
            '    <!--<div class="col-md-4 metric-stat-section">' +
            '        <h6 class="metric-name">Email Ids Collected</h6>' +
            '        <div class="metric-icon"><img src="images/mail.svg" alt="Email Ids Collected" /></div>' +
            '        <h5 class="metric-stat bounceInText {{data_captured.t_emails_captured == \'Data not available\' ? \'metric-notavailable\':\'\'}}" ng-show="data_captured.t_emails_captured || data_captured.t_emails_captured == 0">{{data_captured.t_emails_captured}}</h5>' +
            '        <h5 class="metric-stat" ng-show="!data_captured.t_emails_captured && data_captured.t_emails_captured != 0"><img src="images/data-loader.gif" /></h5>' +
            '    </div>-->' +
            '</div>',

        link: function (scope, element, attrs) {
            scope.$watch(attrs.postData, function (newValue, oldValue) {
                //if(!scope.data_captured)
                    scope.data_captured = {};
                if (newValue && newValue.t_phone_detected_vicinity > 0) {
                    scope.data_captured.t_phone_detected_vicinity = newValue.t_phone_detected_vicinity.toLocaleString();
                } else if (newValue && newValue.t_phone_detected_vicinity == 0) {
                    scope.data_captured.t_phone_detected_vicinity = 'Data not available';
                }
                if (newValue && newValue.t_phone_detected_store > 0) {
                    scope.data_captured.t_phone_detected_store = newValue.t_phone_detected_store.toLocaleString();
                } else if (newValue && newValue.t_phone_detected_store == 0) {
                    scope.data_captured.t_phone_detected_store = 'Data not available';
                }
                if (newValue && newValue.t_phone_detected_wifi > 0) {
                    scope.data_captured.t_phone_detected_wifi = newValue.t_phone_detected_wifi.toLocaleString();
                } else if (newValue && newValue.t_phone_detected_wifi == 0) {
                    scope.data_captured.t_phone_detected_wifi = 'Data not available';
                }

            }, true);
        }
    }
}]);
