analyticsApp
.service('messageService', ['$rootScope', '$timeout', function ($rootScope, $timeout) {
	var showMessage = function (text, type, timeout) {
		timeout = timeout || 5000;
		$('.messege .' + type).text(text)
		$('.messege .' + type).addClass('appear');
		$('.messege').addClass('popup');
		$timeout(function () {
			$('.messege .' + type).text('');
			$('.messege .' + type).remove('appear');
			$('.messege').removeClass('popup');
		}, timeout);
	}

	this.showError = function (text, timeout) {
		showMessage(text, 'error', timeout);
	}

	this.showSuccess = function (text, timeout) {
		showMessage(text, 'success', timeout);
	}

	this.showInfo = function (text, timeout) {
		showMessage(text, 'info', timeout);
	}
}])
.service('promise', ['$q', '$timeout', function ($q, $timeout) {
    return {
        getResult: function () {
            var deferral = $q.defer();
            $timeout(function () {
                deferral.resolve({
                    status: "promise"
                });
            }, 1000);
            return deferral.promise;
        }
    };
}])
.service('ajaxCall', ['$http', '$state', '$q', 'messageService',
	function ($http, $state, $q, messageService) {
		var makeCall = function (options) {
			var deferred = $q.defer();
			//if (options.data && options.data.queryObject)
			    //gtag('event', options.url.substring(options.url.lastIndexOf('/') + 1), options.data.queryObject);

			$http(options).
			success(function (response) {
				if (response && response.status === 1) {
					messageService.showError(response.msg);
					deferred.reject();
				}
				else
					deferred.resolve(response);
			}).error(function (jqXHR, exception) {
			    console.log(exception);
			});
			return deferred.promise;
		}
		this.get = function (url, loading) {
		    return makeCall({
		        method: 'GET', url: url, cache: false, headers: {
		            'Cache-Control': 'no-cache'
		        }
		    });
		}
		this.post = function (url, data, loading) {
		    return makeCall({
		        method: 'POST', data: data, url: url, cache: false, headers: {
		            'Cache-Control': 'no-cache'
		        }
		    });
		}
}])
.service('service', ['ajaxCall', '$q', function (ajaxCall, $q) {
	this.whichUser = function () {
	    return ajaxCall.get('/Client/GetUser').then(function (response) {
			return response.data;
		});
	};

	this.getLocationsForAdmin = function (options) {
	    //var query = createQueryForLocationFilter(options);
	    return ajaxCall.post('/Client/GetLocationsForAdmin', { 'query': options }, {
	        loading: false
	    }).then(function (response) {
	        return response.data;
	    });
	};

	this.demofizeStores = function (stores) {
	    return stores.filter(function (store) {
	        store.storeName = 'Demo Outlet';
	        store.contactPerson = 'Demo Store Keeper';
	        store.contactNumber = 'Demo Store Keeper';
	        return true;
	    });
	}

	this.GetMMNas = function () {
		return ajaxCall.get('/AdminPortal/GetMMNas').then(function (response) {
			return response.data;
		});
	};

	this.logout = function () {
		ajaxCall.get('/Admin/Logout').then(function () {
			window.location.href = "/AdminPortal/Index";
		});
	};

	this.getPhoneDetectedInVicinity = function (postData, callback) {
	    ajaxCall.post('/OneInsights/GetPhoneDetectedInVicinity', postData).then(function (response) {
	        callback(response);
	    });
	}

	this.getPhoneDetectedInStore = function (postData, callback) {
	    ajaxCall.post('/OneInsights/GetPhoneDetectedInStore', postData).then(function (response) {
	        callback(response);
	    });
	}

	this.getPhoneDetectedOnWiFi = function (postData, callback) {
	    ajaxCall.post('/OneInsights/GetPhoneDetectedOnWiFi', postData).then(function (response) {
	        callback(response);
	    });
	}

	this.logout = function () {
	    ajaxCall.get('/Client/Logout').then(function () {
	        window.location.href = '/Client/Index#/login';
	    });
	}

	this.getUserBrand = function () {
	    return ajaxCall.get('/Client/getUserBrand').then(function (response) {
	        if (response.status == 0) return response.data;
	        else return 0;
	    });
	}

	this.setUserBrand = function (param) {
	    var deferred = $q.defer();
	    ajaxCall.post('/Client/setUserBrand', param).then(function (response) {
	        deferred.resolve(response.data);
	    });
	    return deferred.promise;
	}

}]);