analyticsApp.filter('orderObjectBy', function () {
    return function (items, field, reverse) {
        var filtered = [];
        angular.forEach(items, function (item) {
            filtered.push(item);
        });
        filtered.sort(function (a, b) {
            return (a[field] > b[field] ? 1 : -1);
        });
        if (reverse) filtered.reverse();
        return filtered;
    };
});

analyticsApp.filter('propsFilter', function () {
    return function (items, props) {
        var out = [];

        if (angular.isArray(items)) {
            items.forEach(function (item) {
                var itemMatches = false;

                var keys = Object.keys(props);
                for (var i = 0; i < keys.length; i++) {
                    var prop = keys[i];
                    var text = props[prop].toString().toLowerCase();
                    if (item[prop].toString().toLowerCase().indexOf(text) !== -1) {
                        itemMatches = true;
                        break;
                    }
                }

                if (itemMatches) {
                    out.push(item);
                }
            });
        } else {
            // Let the output be the input untouched
            out = items;
        }

        return out;
    }
});

analyticsApp.filter('exactFilter', function () {
    return function (items, props) {
        var out = [];

        if (angular.isArray(items)) {
            items.forEach(function (item) {
                var itemMatches = true;

                var keys = Object.keys(props);
                for (var i = 0; i < keys.length; i++) {
                    var prop = keys[i];
                    var vals = props[prop],
                        found = false;
                    for (var j = 0; j < vals.length; ++j) {
                        if (item[prop] && item[prop].toString() === vals[j].toString()) {
                            found = true;
                            break;
                        }
                    }
                    if (!found && vals.length) {
                        itemMatches = false;
                        break;
                    }
                }

                if (itemMatches) {
                    out.push(item);
                }
            });
        } else {
            // Let the output be the input untouched
            out = items;
        }

        return out;
    }
});


analyticsApp.filter("round", function () {
    return function (input, precision) {
        return input ?
            parseFloat(input).toFixed(precision) :
            0;
    };
});

analyticsApp.filter('numberWithSuffix', function () {
    return function (input, decimals) {
        var exp, rounded,
            suffixes = ['K', 'M', 'G', 'T', 'P', 'E'];

        if (window.isNaN(input)) {
            return null;
        }

        if (input < 1000) {
            return input;
        }

        exp = Math.floor(Math.log(input) / Math.log(1000));

        rounded = (input / Math.pow(1000, exp)).toFixed(decimals) + suffixes[exp - 1];
        return rounded;

    };
});