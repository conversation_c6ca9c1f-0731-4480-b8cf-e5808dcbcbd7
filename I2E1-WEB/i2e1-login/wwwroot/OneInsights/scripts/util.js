var holidays = ["2017-01-01", "2017-01-26", "2017-02-14", "2017-02-24",
    "2017-03-13", "2017-04-04", "2017-04-09", "2017-04-14", "2017-05-10",
    "2017-06-26", "2017-08-07", "2017-08-15", "2017-08-15", "2017-08-25",
    "2017-09-02", "2017-09-30", "2017-10-02", "2017-10-08", "2017-10-17",
    "2017-10-19", "2017-11-04", "2017-12-02", "2017-12-25", "2017-12-31",
    "2018-01-01", "2018-01-26", "2018-02-14", "2018-03-02", "2018-03-25",
    "2018-03-29", "2018-03-30", "2018-04-30", "2018-06-16", "2018-08-15",
    "2018-08-22", "2018-08-26", "2018-09-03", "2018-09-13", "2018-10-02",
    "2018-10-19", "2018-10-27", "2018-11-05", "2018-11-07", "2018-11-21",
    "2018-11-23", "2018-12-25", "2018-12-31"];

var getHolidaysLists = function () {
    var holidays_date_object = [];

    for (i = 0; i < holidays.length; i++) {
        var dateobject = new Date(holidays[i]);
        dateobject.toString("yyyy/mm/dd");
        holidays_date_object.push(dateobject)
    }

    return holidays_date_object;
}

var store_color = '#1f5076';// store rgb(31, 80, 118)
var brand_color = '#00b0f0';// brand rgb(0, 176 240);
var industry_color = '#b4c7e7';// industry rgb(180,199,231);
var incomplete_data_color = '#ed1c24'; // incomplete data rgb(237, 28, 36);
var holiday_color = '#ffd503'; // holiday rgb(255, 213, 3);
var weekend_color = '#71c044'; // weekend rgb(113, 192, 68);
var promo_events_color = '#f97e39'; // promo_events rgb(249,126,57);

var pieColor = ['#324999', '#00ADEE', '#73bd44', "#fdd20c", '#CBF078', '#F8F398', '#F1B963', '#E46161', '#DED5C4', '#EF7E56'];

var metric_for = [{ name: 'industry' }, { name: 'brand' }, { name: 'store' }];
var gender = { cols: [{ id: 'gender', label: 'Gender', type: 'string' }, { id: 'frequency', label: 'Frequency', type: 'number' }], rows: [] };
var age = { cols: [{ id: 'age', label: 'Age', type: 'string' }, { id: 'frequency', label: 'Frequency', type: 'number' }], rows: [] };
var spend = { cols: [{ id: 'spend', label: 'Spend', type: 'string' }, { id: 'frequency', label: 'Frequency', type: 'number' }], rows: [] };
var customer = { cols: [{ id: 'customer', label: 'Customer', type: 'string' }, { id: 'frequency', label: 'Frequency', type: 'number' }], rows: [] };
var loyalty = { cols: [{ id: 'segment', label: 'Segment', type: 'string' }, { id: 'frequency', label: 'Frequency', type: 'number' }], rows: [] };

var addColoumnInLineChartData = function (jsonObject) {
    if (jsonObject.tsType == "daily") {
        jsonObject.data.cols.push({
            'id': 'day',
            'type': 'date',
            'label': 'Date'
        });
    } else if (jsonObject.tsType == "weekly") {
        jsonObject.data.cols.push({
            'id': 'weekly',
            'type': 'number',
            'label': 'Week Day'
        });
    } else if (jsonObject.tsType == "monthly") {
        jsonObject.data.cols.push({
            'id': 'month',
            'type': 'date',
            'label': 'Month'
        });
        jsonObject.options.hAxis = {
            format: "MMM-yy"
        }
    } else if (jsonObject.tsType == "hourly") {
        jsonObject.data.cols.push({
            'id': 'hour',
            'type': 'number',
            'label': 'Hour'
        });
    }
    jsonObject.data.cols.push({
        'id': 'store',
        'type': 'number',
        'label': 'Store'
    });
    jsonObject.data.cols.push({
        'id': 'store_clor',
        'type': 'string',
        'label': 'Store Color',
        'role': 'style'
    });
    jsonObject.data.cols.push({
        'id': 'brand',
        'type': 'number',
        'label': 'Brand'
    });
    jsonObject.data.cols.push({
        'id': 'brand_clor',
        'type': 'string',
        'label': 'Brand Color',
        'role': 'style'
    });
    jsonObject.data.cols.push({
        'id': 'industry',
        'type': 'number',
        'label': 'Industry'
    });
    jsonObject.data.cols.push({
        'id': 'industry_clor',
        'type': 'string',
        'label': 'Industry Color',
        'role': 'style'
    });
    
    return jsonObject;
}

var lineChartOptions = {
    "colors": [store_color, brand_color, industry_color],
    "defaultColors": [store_color, brand_color, industry_color],
    "curveType":"function",
    "legend": "bottom",
    "chartArea": {
        "top": 5,
        "height": "70%"
    }
};

var barChartOptions = {
    //"theme": "material",
    "colors": pieColor,
    "defaultColors": pieColor,
    "isStacked": "true",
    "legend": "none",
    "tooltip": {
        "textStyle": {
            "fontSize": 10
        }
    },
    "vAxis": {
        "format": 'short',
        "textStyle": {
            "fontSize": 7 // or the number you want
        }
    },
    "hAxis": {
        "format": 'short',
        "textStyle": {
            "fontSize": 10, // or the number you want
            "bold": true
        },
        "gridlines": { "count": 4 },
        "minValue": "0"
    },
    "bar": { "groupWidth": 20 }
};

var chartDataObject = {
    vicinity_footfall: {
        type: "LineChart",
        tsType: "daily",
        isVisible: true,
        displayed: false,
        data: {},
        options: angular.copy(lineChartOptions),
        view: {
            columns: [0, 1, 2, 3, 4, 5, 6]
        }
    },
    instore_footfall: {
        type: "LineChart",
        tsType: "daily",
        isVisible: true,
        displayed: false,
        data: {},
        options: angular.copy(lineChartOptions),
        view: {
            columns: [0, 1, 2, 3, 4, 5, 6]
        }
    },
    conversion_rate: {
        type: "LineChart",
        tsType: "daily",
        isVisible: true,
        displayed: false,
        data: {},
        options: angular.copy(lineChartOptions),
        view: {
            columns: [0, 1, 2, 3, 4, 5, 6]
        }
    },
    dwell_time: {
        type: "LineChart",
        tsType: "daily",
        isVisible: true,
        displayed: false,
        data: {},
        options: angular.copy(lineChartOptions),
        view: {
            columns: [0, 1, 2, 3, 4, 5, 6]
        }
    },
    repeat_rate: {
        type: "LineChart",
        tsType: "daily",
        isVisible: true,
        displayed: false,
        data: {},
        options: angular.copy(lineChartOptions),
        view: {
            columns: [0, 1, 2, 3, 4, 5, 6]
        }
    }
}

var wifiMetricsChartDataObject = {
    wifi_users: {
        type: "LineChart",
        tsType: "hourly",
        isVisible: true,
        displayed: false,
        data: {},
        options: angular.copy(lineChartOptions),
        view: {
            columns: [0, 1, 2, 3, 4, 5, 6]
        }
    },
    users_time_spent: {
        type: "LineChart",
        tsType: "hourly",
        isVisible: true,
        displayed: false,
        data: {},
        options: angular.copy(lineChartOptions),
        view: {
            columns: [0, 1, 2, 3, 4, 5, 6]
        }
    },
    wifi_data_usage: {
        type: "LineChart",
        tsType: "hourly",
        isVisible: true,
        displayed: false,
        data: {},
        options: angular.copy(lineChartOptions),
        view: {
            columns: [0, 1, 2, 3, 4, 5, 6]
        }
    },
    wifi_repeat_rate: {
        type: "LineChart",
        tsType: "hourly",
        isVisible: true,
        displayed: false,
        data: {},
        options: angular.copy(lineChartOptions),
        view: {
            columns: [0, 1, 2, 3, 4, 5, 6]
        }
    }
}

var locationQueryObject = {
    'pageNumber': 1,
    'pageSize': 5000,
    'storeName': null,
    'storeNameAlias': null,
    'address': null,
    'city': null,
    'state': null,
    'category': null,
    'installedState': null,
    'storeTags': null
};

var day_slots = [
    { index: 1, value: "Monday" },
    { index: 2, value: "Tuesday" },
    { index: 3, value: "Wednesday" },
    { index: 4, value: "Thursday" },
    { index: 5, value: "Friday" },
    { index: 6, value: "Saturday" },
    { index: 7, value: "Sunday" },
    { index: 8, value: "Sunday" },
    { index: 255, value: "not defined" },
]

var time_slots = [
    { index: 1, value: "9am - 11am" },
    { index: 2, value: "11 am - 1pm" },
    { index: 3, value: "1pm - 3pm" },
    { index: 4, value: "3pm - 5pm" },
    { index: 5, value: "5pm - 7pm" },
    { index: 6, value: "7pm - 9pm" },
    { index: 7, value: "9pm - 11pm" },
    { index: 8, value: "11pm - 1am" },
    { index: 255, value: "non operating" }
]


var validateQueryObject = function (queryObject) {
    var startDate = queryObject.from; //moment(queryObject.from, 'MM/DD/YYYY');
    var endDate = queryObject.to; //moment(queryObject.to, 'MM/DD/YYYY')
    console.log(startDate <= endDate);
    if (
            (
                startDate != null &&
                endDate != null &&
                queryObject.nasids.length > 0 && queryObject.industries.length > 0
            ) &&
            (
                startDate <= endDate
            )
       ) {
        return true
    } else {
        return false;
    }
}

var createFilterQuery = function (queryObject) {
    var query = ''
    queryObject = queryObject || {};

    angular.forEach(queryObject, function (value, key) {
        if (key == 'from' && value != null) {
            query += '?from=' + value;
        } else if (key == 'locations' && value != null) {
            //nasids = value.split(",");
            for (i = 0; i < value.length; i++) {
                query += '&nasids=' + value[i];
            }
        } else if (key != 'nasids' && value != null && value != '') {
            query += '&' + key + '=' + value;
        }

    });
    return query;
}

var createQueryForLocationFilter = function (options) {
    var query = ''
    options = options || {};
    var queryObjectCopy = angular.copy(locationQueryObject);
    queryObjectCopy = angular.extend(queryObjectCopy, options)
    angular.forEach(queryObjectCopy, function (value, key) {
        if (key == 'pageNumber' && value != null)
            query += '?pageNumber=' + value;
        else if (value != null && value != '')
            query += '&' + key + '=' + value;
    });
    return query;
}

Number.prototype.round = function (places) {
    places = Math.pow(10, places);
    return Math.round(this * places) / places;
}

Number.prototype.percentChange = function (oldFigure) {
    if (oldFigure != null) {
        percentChange = (1 - this / oldFigure) * 100;
        percentChange = (-1) * percentChange;
    }
    else {
        percentChange = null;
    }
    return percentChange;
}

String.prototype.capitalize = function () {
    return this.charAt(0).toUpperCase() + this.slice(1);
}

var downloadCSV = function (csv_out, filename) {
    var blob = new Blob([csv_out], {
        type: 'text/csv;charset=utf-8'
    });
    var url = window.URL || window.webkitURL;
    var link = document.createElementNS("http://www.w3.org/1999/xhtml", "a");
    link.href = url.createObjectURL(blob);
    link.download = filename;

    var event = document.createEvent("MouseEvents");
    event.initEvent("click", true, false);
    link.dispatchEvent(event);
}

function getImgData(chartContainer) {
    var chartArea = chartContainer.getElementsByTagName('svg')[0].parentNode;
    var svg = chartArea.innerHTML;
    var doc = chartContainer.ownerDocument;
    var canvas = doc.createElement('canvas');
    canvas.setAttribute('width', chartArea.offsetWidth);
    canvas.setAttribute('height', chartArea.offsetHeight);
    canvas.setAttribute(
        'style',
        'position: absolute; ' +
        'top: ' + (-chartArea.offsetHeight * 2) + 'px;' +
        'left: ' + (-chartArea.offsetWidth * 2) + 'px;');
    doc.body.appendChild(canvas);
    canvg(canvas, svg);
    var imgData = canvas.toDataURL("image/png");
    canvas.parentNode.removeChild(canvas);
    return imgData;
}

function downloadURI(uri, name) {
    var link = document.createElement("a");

    link.download = name;
    link.href = uri;
    document.body.appendChild(link);
    link.click();
}

var donutChartOptions = {
    "colors": pieColor,
    "defaultColors": pieColor,
    "chartArea": { width: "100%", height: 135 },
    "legend": "bottom",
    "tooltip": {
        //"text": 'percentage',
        "text": 'value',
        "textStyle": {
            "fontSize": 10
        }
    },
    "animation": {
        "duration": 1000,
        "easing": "linear",
        "startup": true
    },
    //"tooltip": { isHtml: true },
    "pieHole": 0.7,
    "pieSliceBorderColor": "transparent",
    "backgroundColor": 'transparent',
    "pieSliceTextStyle": {
        color: 'transparent',
    },

}

var donutDataObject = {

    genderDistribution: {
        type: "PieChart",
        data: gender,
        options: angular.copy(donutChartOptions),
    },

    ageDistribution: {
        type: "PieChart",
        data: age,
        options: angular.copy(donutChartOptions),
    },

    spendDistribution: {
        type: "PieChart",
        data: spend,
        options: angular.copy(donutChartOptions),

    },
    customerDistribution: {
        type: "PieChart",
        data: customer,
        options: angular.copy(donutChartOptions),
    },
    loyaltyDistribution: {
        type: "PieChart",
        data: loyalty,
        options: angular.copy(donutChartOptions),
    }

}

function arrayMin(arr) {
    var len = arr.length, min = Infinity;
    while (len--) {
        if (arr[len] < min) {
            min = arr[len];
        }
    }
    return min;
};

function arrayMax(arr) {
    var len = arr.length, max = -Infinity;
    while (len--) {
        if (Number(arr[len]) > max) {
            max = Number(arr[len]);
        }
    }
    return max;
};

function getColorLuminance(hex, opacity) {
    hex = hex.replace('#', '');
    r = parseInt(hex.substring(0, 2), 16);
    g = parseInt(hex.substring(2, 4), 16);
    b = parseInt(hex.substring(4, 6), 16);

    result = 'rgba(' + r + ',' + g + ',' + b + ',' + opacity + ')';
    
}

function getBackGroundColorStyle(opacity) {
    var hex = '#008000'
    hex = hex.replace('#', '');
    r = parseInt(hex.substring(0, 2), 16);
    g = parseInt(hex.substring(2, 4), 16);
    b = parseInt(hex.substring(4, 6), 16);
    rgb = 'rgb(' + r + ',' + g + ',' + b + ')';
    rgba = 'rgba(' + r + ',' + g + ',' + b + ',' + opacity + ')';
    var style = '';
    style += 'background: + ' + rgba + ';'; /* Old browsers */
    style += 'background: -moz-linear-gradient(left, ' + rgba + ' 0%, ' + rgba + ' 100%);'; /* FF3.6-15 */
    style += 'background: -webkit-linear-gradient(left, ' + rgba + ' 0%,' + rgba + ' 100%);'; /* Chrome10-25,Safari5.1-6 */
    style += 'background: linear-gradient(to right, ' + rgb + ' 0%,' + rgba + ' 100%);'; /* W3C, IE10+, FF16+, Chrome26+, Opera12+, Safari7+ */
    return style;
}

var shadeBlendConvert = function (p, from, to) {
    if (typeof (p) != "number" || p < -1 || p > 1 || typeof (from) != "string" || (from[0] != 'r' && from[0] != '#') || (to && typeof (to) != "string")) return null; //ErrorCheck
    if (!this.sbcRip) this.sbcRip = (d) => {
        let l = d.length,
            RGB = {};
        if (l > 9) {
            d = d.split(",");
            if (d.length < 3 || d.length > 4) return null; //ErrorCheck
            RGB[0] = i(d[0].split("(")[1]), RGB[1] = i(d[1]), RGB[2] = i(d[2]), RGB[3] = d[3] ? parseFloat(d[3]) : -1;
        } else {
            if (l == 8 || l == 6 || l < 4) return null; //ErrorCheck
            if (l < 6) d = "#" + d[1] + d[1] + d[2] + d[2] + d[3] + d[3] + (l > 4 ? d[4] + "" + d[4] : ""); //3 or 4 digit
            d = i(d.slice(1), 16), RGB[0] = d >> 16 & 255, RGB[1] = d >> 8 & 255, RGB[2] = d & 255, RGB[3] = -1;
            if (l == 9 || l == 5) RGB[3] = r((RGB[2] / 255) * 10000) / 10000, RGB[2] = RGB[1], RGB[1] = RGB[0], RGB[0] = d >> 24 & 255;
        }
        return RGB;
    }
    var i = parseInt,
        r = Math.round,
        h = from.length > 9,
        h = typeof (to) == "string" ? to.length > 9 ? true : to == "c" ? !h : false : h,
        b = p < 0,
        p = b ? p * -1 : p,
        to = to && to != "c" ? to : b ? "#000000" : "#FFFFFF",
        f = this.sbcRip(from),
        t = this.sbcRip(to);
    if (!f || !t) return null; //ErrorCheck
    if (h) return "rgb" + (f[3] > -1 || t[3] > -1 ? "a(" : "(") + r((t[0] - f[0]) * p + f[0]) + "," + r((t[1] - f[1]) * p + f[1]) + "," + r((t[2] - f[2]) * p + f[2]) + (f[3] < 0 && t[3] < 0 ? ")" : "," + (f[3] > -1 && t[3] > -1 ? r(((t[3] - f[3]) * p + f[3]) * 10000) / 10000 : t[3] < 0 ? f[3] : t[3]) + ")");
    else return "#" + (0x100000000 + r((t[0] - f[0]) * p + f[0]) * 0x1000000 + r((t[1] - f[1]) * p + f[1]) * 0x10000 + r((t[2] - f[2]) * p + f[2]) * 0x100 + (f[3] > -1 && t[3] > -1 ? r(((t[3] - f[3]) * p + f[3]) * 255) : t[3] > -1 ? r(t[3] * 255) : f[3] > -1 ? r(f[3] * 255) : 255)).toString(16).slice(1, f[3] > -1 || t[3] > -1 ? undefined : -2);
}


function findMinMax(obj, key) {
    if (obj && obj.length > 0) {

        let min = obj[0][key], max = obj[0][key];

        for (let i = 1, len = obj.length; i < len; i++) {
            let v = obj[i][key];
            min = (v < min) ? v : min;
            max = (v > max) ? v : max;
        }
        return [min, max];
    } else {
        return false
    }
}