'use strict';
var analyticsApp = angular.module('oneInsightsApp', ['ngAnimate', 'ngSanitize','ui.select', 'ui.router', 'ui.bootstrap', 'googlechart', 'wu.masonry', 'ngCookies'])
.config(function ($stateProvider, $urlRouterProvider) {
    $urlRouterProvider.otherwise('overview');
    $stateProvider
        /*.state('landing', {
            url: '/landing',
            templateUrl: getUrlWithVersion('partial/landing.html'),
            controller: 'landingController'
        })*/
        .state('overview', {
            pageTitle: 'Overview',
            url: '/overview',
            templateUrl: getUrlWithVersion('partial/overview.html'),
            controller: 'overviewController'
        })
        .state('benefits', {
            pageTitle: 'Benefits',
            url: '/benefits',
            templateUrl: getUrlWithVersion('partial/benefits.html'),
            controller: 'benefitsController'
        })
        .state('kyc', {
            pageTitle: 'Know your customers',
            url: '/kyc',
            templateUrl: getUrlWithVersion('partial/kyc.html'),
            controller: 'kycController'
        })
        .state('kyl', {
            pageTitle: 'Know your locations',
            url: '/kyl',
            templateUrl: getUrlWithVersion('partial/kyl.html'),
            controller: 'kylController'
        })
        .state('csc', {
            pageTitle: 'Cross Store Comparisions',
            url: '/csc',
            templateUrl: getUrlWithVersion('partial/csc.html'),
            controller: 'cscController'
        })
        .state('insights', {
            url: '/insights',
            templateUrl: getUrlWithVersion('partial/insights.html'),
            controller: 'insightsController'
        })
        .state('wifimetrics', {
            pageTitle: 'WiFi-Metrics',
            url: '/wifimetrics',
            templateUrl: getUrlWithVersion('partial/wifimetrics.html'),
            controller: 'wifimetricsController'
        });
});


