/**
 * API Metrics Utility
 * Tracks API call timing and request size
 */

function trackApiMetrics() {
    // Store original fetch function
    const originalFetch = window.fetch;
    
    // Specific endpoint to always ignore
    const IGNORE_ENDPOINT = 'SendCaptivePortalFrontendEvent';

    // Function to extract endpoint from URL
    function extractEndpoint(url) {
        try {
            // Handle null or undefined URL
            if (!url) return 'unknown';
            
            const urlString = url.toString();
            
            // Handle empty URL
            if (!urlString || urlString.trim() === '') return 'unknown';
            
            try {
                const urlObj = new URL(urlString);
                const pathParts = urlObj.pathname.split('/').filter(part => part.length > 0);
                return pathParts.length > 0 ? pathParts[pathParts.length - 1] : 'root';
            } catch (e) {
                // If URL parsing fails, try to extract endpoint from string
                const parts = urlString.split('/');
                if (parts.length === 0) return 'unknown';
                
                const lastPart = parts[parts.length - 1];
                if (!lastPart) return 'unknown';
                
                const queryParamIndex = lastPart.indexOf('?');
                return queryParamIndex >= 0 ? lastPart.substring(0, queryParamIndex) : lastPart;
            }
        } catch (e) {
            console.error('Error extracting endpoint:', e);
            return 'unknown';
        }
    }

    // Function to calculate request size
    function calculateRequestSize(url, options) {
        try {
            let size = 0;
            
            // Handle null or undefined URL
            if (!url) return 0;
            
            // Add URL size
            try {
                size += url.toString().length;
            } catch (e) {
                console.error('Error calculating URL size:', e);
                // Default URL size if toString fails
                size += 50;
            }
            
            // Add headers size if present
            if (options && options.headers) {
                try {
                    if (options.headers instanceof Headers) {
                        // Handle Headers object
                        let headerSize = 0;
                        options.headers.forEach((value, name) => {
                            headerSize += name.length + value.length + 2; // +2 for ': '
                        });
                        size += headerSize;
                    } else {
                        // Handle plain object headers
                        const headerSize = JSON.stringify(options.headers).length;
                        size += headerSize;
                    }
                } catch (e) {
                    console.error('Error calculating headers size:', e);
                    // Default header size estimate
                    size += 100;
                }
            }
            
            // Add body size if present
            if (options && options.body) {
                try {
                    if (typeof options.body === 'string') {
                        size += options.body.length;
                    } else if (options.body instanceof FormData) {
                        // Try to estimate FormData size
                        let formDataSize = 0;
                        try {
                            // Modern browsers might support FormData.entries()
                            if (typeof options.body.entries === 'function') {
                                for (let pair of options.body.entries()) {
                                    const key = pair[0];
                                    const value = pair[1];
                                    formDataSize += key.length;
                                    
                                    if (typeof value === 'string') {
                                        formDataSize += value.length;
                                    } else if (value instanceof Blob) {
                                        formDataSize += value.size;
                                    } else {
                                        formDataSize += 100; // Default size for unknown types
                                    }
                                }
                                size += formDataSize;
                            } else {
                                // Fallback for browsers without FormData.entries()
                                size += 500; // Default FormData size estimate
                            }
                        } catch (e) {
                            console.error('Error calculating FormData size:', e);
                            size += 500; // Default FormData size estimate
                        }
                    } else if (options.body instanceof URLSearchParams) {
                        size += options.body.toString().length;
                    } else if (options.body instanceof Blob) {
                        size += options.body.size;
                    } else if (options.body instanceof ArrayBuffer) {
                        size += options.body.byteLength;
                    } else if (ArrayBuffer.isView(options.body)) {
                        // Handle typed arrays and DataView
                        size += options.body.byteLength;
                    } else {
                        // For other types, try to stringify
                        size += JSON.stringify(options.body).length;
                    }
                } catch (e) {
                    console.error('Error calculating body size:', e);
                    // Default body size estimate
                    size += 200;
                }
            }
            
            return size;
        } catch (e) {
            console.error('Error in calculateRequestSize:', e);
            return 250; // Return a default size if anything goes wrong
        }
    }

    // Override fetch with our instrumented version
    window.fetch = function(...args) {
        try {
            const url = args[0];
            const options = args[1] || {};
            
            // Handle invalid URL
            if (!url) {
                console.error('Invalid URL in fetch call');
                return originalFetch.apply(this, args);
            }
            
            // Skip tracking for the specific endpoint
            try {
                const urlString = url.toString();
                if (urlString.includes(IGNORE_ENDPOINT)) {
                    return originalFetch.apply(this, args);
                }
            } catch (e) {
                console.error('Error checking URL for ignored endpoint:', e);
                return originalFetch.apply(this, args);
            }
            
            // Record start time with high precision (Milliseconds since page load)
            const startTime = performance.now();
            const sendTime = new Date().toISOString();
            
            // Extract endpoint from URL
            let endpoint = 'unknown';
            try {
                endpoint = extractEndpoint(url);
            } catch (e) {
                console.error('Error extracting endpoint:', e);
            }
            
            // Calculate request size
            let requestSize = 0;
            try {
                requestSize = calculateRequestSize(url, options);
            } catch (e) {
                console.error('Error calculating request size:', e);
            }
            
            // Log the API call initiation
            try {
                console.log(`API Call Initiated: ${url} (Endpoint: ${endpoint})`);
                console.log(`- Request size: ${requestSize} bytes`);
            } catch (e) {
                console.error('Error logging API call initiation:', e);
            }
            
            // Call the original fetch
            return originalFetch.apply(this, args)
                .then(response => {
                    try {
                        // Clone the response so we can read it multiple times
                        const clonedResponse = response.clone();
                        
                        // Calculate time taken with high precision
                        const endTime = performance.now();
                        const timeTaken = endTime - startTime;
                        
                        // Process the response
                        return clonedResponse.text()
                            .then(text => {
                                try {
                                    // Log the metrics
                                    console.log(`API Metrics for ${url}:`);
                                    console.log(`- Endpoint: ${endpoint}`);
                                    console.log(`- Time taken: ${timeTaken.toFixed(2)} ms`);
                                    console.log(`- Request size: ${requestSize} bytes`);
                                    
                                    // Add custom event for tracking
                                    if (typeof logger === 'function') {
                                        try {
                                            logger("api_metrics_" + endpoint, "", {
                                                url: url.toString(),
                                                sendTime: sendTime,
                                                timeTaken: timeTaken.toFixed(2),
                                                requestSize: requestSize,
                                                method: options.method || 'GET',
                                                status: response.status
                                            });
                                        } catch (e) {
                                            console.error('Error logging metrics:', e);
                                        }
                                    }
                                } catch (e) {
                                    console.error('Error processing response metrics:', e);
                                }
                                
                                // Return the original response
                                return response;
                            })
                            .catch(error => {
                                console.error('Error reading response text:', error);
                                return response; // Still return the response even if we can't read it
                            });
                    } catch (e) {
                        console.error('Error in fetch response handling:', e);
                        return response; // Return the original response if anything goes wrong
                    }
                })
                .catch(error => {
                    try {
                        // Calculate time taken even for errors
                        const endTime = performance.now();
                        const timeTaken = endTime - startTime;
                        
                        // Log error metrics
                        console.log(`API Error for ${url}:`);
                        console.log(`- Endpoint: ${endpoint}`);
                        console.log(`- Time taken until error: ${timeTaken.toFixed(2)} ms`);
                        console.log(`- Request size: ${requestSize} bytes`);
                        console.log(`- Error: ${error.message || 'Unknown error'}`);
                        
                        // Add custom event for tracking errors
                        if (typeof logger === 'function') {
                            try {
                                logger("api_metrics_error_" + endpoint, "", {
                                    url: url.toString(),
                                    sendTime: sendTime,
                                    timeTaken: timeTaken.toFixed(2),
                                    requestSize: requestSize,
                                    error: error.message || 'Unknown error',
                                    method: options.method || 'GET'
                                });
                            } catch (e) {
                                console.error('Error logging error metrics:', e);
                            }
                        }
                    } catch (e) {
                        console.error('Error in fetch error handling:', e);
                    }
                    
                    // Re-throw the error
                    throw error;
                });
        } catch (e) {
            console.error('Critical error in fetch override:', e);
            // Fall back to original fetch if our instrumentation fails
            return originalFetch.apply(this, args);
        }
    };

    console.log("API metrics tracking initialized (fetch only)");
}

// Initialize tracking when the script loads
try {
    trackApiMetrics();
} catch (e) {
    console.error('Failed to initialize API metrics tracking:', e);
}
