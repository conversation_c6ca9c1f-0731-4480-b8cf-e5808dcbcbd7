
    let paymentDomain = null;

    function setPaymentDomain(paymentDom) {
        paymentDomain = paymentDom;
    }

    function LogError(message) {
        const jsonString = JSON.stringify(message);

        fetch(paymentDomain + `/api/CommonAPI/LogUIConsoleErrors?logError=${encodeURIComponent(message)}`, {
                method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: jsonString
        }).then(response => {
            response.json();
        }).then(response => {
            console.log("Response after handling error: " + response);
        }).catch(err => {
            console.error("error while logging error to betterstack: " + err);
        })
    }

    window.onerror = function (error) {
        console.error(error);
        LogError("onError: " + error);
        return true;
    };

    // Capturing unhandled promise rejections
    window.addEventListener('unhandledrejection', function (event) {
        console.error('Reason: ' + event.reason);
        if (event.reason.stack == null || event.reason.stack == '') {
            LogError("eventListener: " + event.reason.message);
        } else {
            LogError("eventListener: " + event.reason.stack);
        }
        event.preventDefault(); // Prevents the browser default rejection handling.
    });