function WIOMUPIIntentCollectSDK(loginDomain) {
    this.loginDomain = loginDomain;
    this.saved_sdk_params = null;
    this.dynamicintentlink = "";
}

WIOMUPIIntentCollectSDK.prototype.postUPIRequest = function (txnType, targetedUpiApp, mobile, planId, upiId, nasid, deviceId, uniqueIdentifier) {
    var self = this;
    return new Promise(function (resolve, reject) {
        var api_createDirectBookingTransaction = "/WiomNet/Payment/CreateTransaction";
        var queryString = "txnType=" + txnType +
            "&targetedUpiApp=" + targetedUpiApp +
            "&mobile=" + mobile +
            "&planId=" + planId +
            "&upiId=" + upiId +
            "&nasid=" + nasid +
            "&deviceId=" + deviceId +
            "&uniqueIdentifier=" + uniqueIdentifier;

        var url = self.loginDomain + api_createDirectBookingTransaction + "?" + queryString;

        fetch(url, {
            method: "POST",
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
                txnType: txnType,
                targetedUpiApp: targetedUpiApp,
                mobile: mobile,
                planId: planId,
                upiId: upiId,
                nasid: nasid,
                deviceId: deviceId,
                UniqueIdentifier: UniqueIdentifier
            })
        })
            .then(function (response) {
                return response.json();
            })
            .then(function (data) {
                resolve(data);
            })
            .catch(function (error) {
                reject(error);
            });
    });
};

WIOMUPIIntentCollectSDK.prototype.constructURLForUPIIntent = function (sdk_params, upiapp) {
    var trParamString = this.constructQueryParameter(WIOMUPIIntentCollectSDK.upiIntentQueryParam.TR, sdk_params && sdk_params.tr);
    var tidParamString = "";

    if (sdk_params && sdk_params.tid) {
        tidParamString = this.constructQueryParameter(WIOMUPIIntentCollectSDK.upiIntentQueryParam.TID, sdk_params.tid);
    }

    var paParamString = this.constructQueryParameter(WIOMUPIIntentCollectSDK.upiIntentQueryParam.PA, sdk_params && sdk_params.merchant_vpa);
    var mcParamString = this.constructQueryParameter(WIOMUPIIntentCollectSDK.upiIntentQueryParam.MC, sdk_params && sdk_params.mcc);
    var pnParamString = this.constructQueryParameter(WIOMUPIIntentCollectSDK.upiIntentQueryParam.PN, sdk_params && sdk_params.merchant_name);
    var amParamString = this.constructQueryParameter(WIOMUPIIntentCollectSDK.upiIntentQueryParam.AM, sdk_params && sdk_params.amount);
    var cuParamString = this.constructQueryParameter(WIOMUPIIntentCollectSDK.upiIntentQueryParam.CU, "INR");
    var tnParamString = this.constructQueryParameter(WIOMUPIIntentCollectSDK.upiIntentQueryParam.TN, "Paying for unlimited internet as per selected plan");

    return WIOMUPIIntentCollectSDK.upiIntentAppsInfo[upiapp] + "?" + trParamString + tidParamString + paParamString + mcParamString + pnParamString + amParamString + cuParamString + tnParamString;
};

WIOMUPIIntentCollectSDK.prototype.constructQueryParameter = function (paramKey, paramValue) {
    return paramKey + "=" + paramValue + "&";
};

WIOMUPIIntentCollectSDK.prototype.getPromiseOfVerifyCustomerVPA = function (vpa, jsonString) {
    return fetch(this.loginDomain + "/WiomNet/Payment/VerifyVPA?upiId=" + vpa, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: jsonString
    });
};

WIOMUPIIntentCollectSDK.prototype.openUPIIntentApp = function (sdk_params, upiapp, callbackfun) {
    this.dynamicintentlink = this.constructURLForUPIIntent(sdk_params, upiapp);
    window.open(this.dynamicintentlink);
    callbackfun();
};

// Static properties
WIOMUPIIntentCollectSDK.UPITxnTypes = {
    txnTypeCollect: 'upi_collect',
    txnTypeIntent: 'upi_intent'
};

WIOMUPIIntentCollectSDK.UPIIntentApp = {
    all: 'all',
    phonepe: 'phonepe',
    gpay: 'gpay',
    paytm: 'paytm'
};

WIOMUPIIntentCollectSDK.upiIntentAppsInfo = {};
WIOMUPIIntentCollectSDK.upiIntentAppsInfo[WIOMUPIIntentCollectSDK.UPIIntentApp.all] = "upi://pay";
WIOMUPIIntentCollectSDK.upiIntentAppsInfo[WIOMUPIIntentCollectSDK.UPIIntentApp.phonepe] = "phonepe://pay";
WIOMUPIIntentCollectSDK.upiIntentAppsInfo[WIOMUPIIntentCollectSDK.UPIIntentApp.gpay] = "tez://upi/pay";
WIOMUPIIntentCollectSDK.upiIntentAppsInfo[WIOMUPIIntentCollectSDK.UPIIntentApp.paytm] = "paytmmp://pay";

WIOMUPIIntentCollectSDK.upiIntentQueryParam = {
    TR: 'tr',
    TID: 'tid',
    PA: 'pa',
    MC: 'mc',
    PN: 'pn',
    AM: 'am',
    CU: 'cu',
    TN: 'tn'
};

WIOMUPIIntentCollectSDK.checkPaymentAPIStatusTypes = {
    SUCCESS: 'success',
    FAILURE: 'failure',
    PENDING: 'pending',
    STILL_PROCESSING: 'still_processing'
};