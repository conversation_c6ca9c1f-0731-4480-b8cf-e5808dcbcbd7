/**
 * payment-websocket.js
 * Handles WebSocket connections for real-time payment status updates
 */

class PaymentWebSocket {
    /**
     * Initialize the WebSocket connection for payment status updates
     * @param {string} orderId - The order ID to track
     * @param {Object} options - Configuration options
     * @param {string} options.wsUrl - WebSocket server URL (default: derived from window.location)
     * @param {number} options.reconnectInterval - Time in ms between reconnection attempts (default: 3000)
     * @param {number} options.maxReconnectAttempts - Maximum number of reconnection attempts (default: 5)
     * @param {Function} options.onStatusUpdate - Callback for payment status updates
     * @param {Function} options.onConnectionError - Callback for connection errors
     */
    constructor(orderId, options = {}) {
        // Required parameter
        if (!orderId) {
            throw new Error('Order ID is required to initialize PaymentWebSocket');
        }
        this.orderId = orderId;
        
        // Configuration with defaults
        this.wsUrl = options.wsUrl || this._getDefaultWsUrl();
        this.reconnectInterval = options.reconnectInterval || 3000;
        this.maxReconnectAttempts = options.maxReconnectAttempts || 5;
        this.reconnectAttempts = 0;
        
        // Callbacks
        this.onStatusUpdate = options.onStatusUpdate || this._defaultStatusUpdateCallback;
        this.onConnectionError = options.onConnectionError || this._defaultErrorCallback;
        
        // WebSocket state
        this.socket = null;
        this.isConnected = false;
        this.isConnecting = false;
        this.intentionalClose = false;
        
        // Bind methods to preserve 'this' context
        this._onOpen = this._onOpen.bind(this);
        this._onMessage = this._onMessage.bind(this);
        this._onClose = this._onClose.bind(this);
        this._onError = this._onError.bind(this);
        
        // Initialize connection
        this.connect();
        
        // Handle page unload to properly close connection
        window.addEventListener('beforeunload', () => this.disconnect(true));
    }
    
    /**
     * Generate default WebSocket URL based on current location
     * @private
     * @returns {string} WebSocket URL
     */
    _getDefaultWsUrl() {
        const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
        const host = window.location.host;
        return `${protocol}//${host}/ws/payment`;
    }
    
    /**
     * Default callback for payment status updates
     * @private
     * @param {Object} data - Payment status data
     */
    _defaultStatusUpdateCallback(data) {
        console.log('Payment status update received:', data);
    }
    
    /**
     * Default callback for connection errors
     * @private
     * @param {Error} error - Error object
     */
    _defaultErrorCallback(error) {
        console.error('WebSocket connection error:', error);
    }
    
    /**
     * Connect to the WebSocket server
     * @returns {Promise} Resolves when connected, rejects on failure
     */
    connect() {
        return new Promise((resolve, reject) => {
            if (this.isConnected) {
                resolve();
                return;
            }
            
            if (this.isConnecting) {
                reject(new Error('Connection already in progress'));
                return;
            }
            
            this.isConnecting = true;
            this.intentionalClose = false;
            
            try {
                // Create WebSocket with order ID as query parameter
                const wsUrlWithParams = `${this.wsUrl}?orderId=${this.orderId}`;
                this.socket = new WebSocket(wsUrlWithParams);
                
                // Set up event handlers
                this.socket.addEventListener('open', (event) => {
                    this._onOpen(event);
                    resolve();
                });
                
                this.socket.addEventListener('message', this._onMessage);
                this.socket.addEventListener('close', this._onClose);
                this.socket.addEventListener('error', (error) => {
                    this._onError(error);
                    reject(error);
                });
            } catch (error) {
                this.isConnecting = false;
                reject(error);
            }
        });
    }
    
    /**
     * Disconnect from the WebSocket server
     * @param {boolean} intentional - Whether the disconnect was intentional
     */
    disconnect(intentional = false) {
        this.intentionalClose = intentional;
        
        if (this.socket && (this.isConnected || this.isConnecting)) {
            this.socket.close();
            this.isConnected = false;
            this.isConnecting = false;
            console.log('WebSocket disconnected');
        }
    }
    
    /**
     * Handle WebSocket open event
     * @private
     * @param {Event} event - WebSocket open event
     */
    _onOpen(event) {
        this.isConnected = true;
        this.isConnecting = false;
        this.reconnectAttempts = 0;
        console.log('WebSocket connection established');
        
        // Send initial message to register for payment updates
        this._sendMessage({
            type: 'register',
            orderId: this.orderId
        });
    }
    
    /**
     * Handle WebSocket message event
     * @private
     * @param {MessageEvent} event - WebSocket message event
     */
    _onMessage(event) {
        try {
            const data = JSON.parse(event.data);
            
            // Handle different message types
            switch (data.type) {
                case 'payment_status':
                    this._handlePaymentStatusUpdate(data.status);
                    break;
                    
                case 'ping':
                    // Respond to keep-alive pings
                    this._sendMessage({ type: 'pong' });
                    break;
                    
                default:
                    console.log('Received message:', data);
            }
        } catch (error) {
            console.error('Error processing message:', error);
        }
    }
    
    /**
     * Handle WebSocket close event
     * @private
     * @param {CloseEvent} event - WebSocket close event
     */
    _onClose(event) {
        this.isConnected = false;
        this.isConnecting = false;
        
        console.log(`WebSocket connection closed: ${event.code} ${event.reason}`);
        
        // Don't attempt to reconnect if the closure was intentional
        if (!this.intentionalClose && this.reconnectAttempts < this.maxReconnectAttempts) {
            this.reconnectAttempts++;
            console.log(`Attempting to reconnect (${this.reconnectAttempts}/${this.maxReconnectAttempts})...`);
            
            setTimeout(() => {
                this.connect().catch(error => {
                    console.error('Reconnection attempt failed:', error);
                });
            }, this.reconnectInterval);
        }
    }
    
    /**
     * Handle WebSocket error event
     * @private
     * @param {Event} event - WebSocket error event
     */
    _onError(event) {
        this.onConnectionError(new Error('WebSocket connection error'));
    }
    
    /**
     * Send a message through the WebSocket
     * @private
     * @param {Object} data - Data to send
     * @returns {boolean} Success status
     */
    _sendMessage(data) {
        if (!this.isConnected || !this.socket) {
            return false;
        }
        
        try {
            this.socket.send(JSON.stringify(data));
            return true;
        } catch (error) {
            console.error('Error sending message:', error);
            return false;
        }
    }
    
    /**
     * Handle payment status update messages
     * @private
     * @param {Object} data - Payment status data
     */
    _handlePaymentStatusUpdate(data) {
        // Map WebSocket status to WIOMUPIIntentCollectSDK status format
        const statusMapping = {
            'success': WIOMUPIIntentCollectSDK.checkPaymentAPIStatusTypes.SUCCESS,
            'failure': WIOMUPIIntentCollectSDK.checkPaymentAPIStatusTypes.FAILURE,
            'pending': WIOMUPIIntentCollectSDK.checkPaymentAPIStatusTypes.PENDING,
            'processing': WIOMUPIIntentCollectSDK.checkPaymentAPIStatusTypes.STILL_PROCESSING
        };
        
        data.data.status = statusMapping[data.data.status] || data.data.status;
        // Call the status update callback
        this.onStatusUpdate(data);
    }
    
    /**
     * Check if the connection is currently active
     * @returns {boolean} Connection status
     */
    isActive() {
        return this.isConnected;
    }
    
    /**
     * Manually request a payment status update
     * @returns {boolean} Success status
     */
    requestStatusUpdate() {
        return this._sendMessage({
            type: 'check_status',
            orderId: this.orderId
        });
    }
}

/**
 * Helper function to replace the existing verifyPaymentStatusFromPG function
 * @param {string} orderId - Order ID to check
 * @param {Object} options - Configuration options
 * @returns {PaymentWebSocket} WebSocket instance
 */
function createPaymentWebSocketConnection(orderId, options = {}) {
    // Default callback to match existing behavior
    const defaultOptions = {
        onStatusUpdate: function(response) {
            if (response && response.status === 0 && response.data.status != 'pending') {
                if (isProccessingCompleted(response)) {
                    isTransactionCompleted = true;
                    notifyParent(response.data.status);
                } else if (response.data.status === WIOMUPIIntentCollectSDK.checkPaymentAPIStatusTypes.STILL_PROCESSING) {
                    isTransactionCompleted = false;
                }
            } else {
                isTransactionCompleted = false;
                if (response && response.data && response.data.error_info) {
                    console.log(response.data.error_info);
                }
            }
        }
    };
    
    // Merge default options with provided options
    const mergedOptions = Object.assign({}, defaultOptions, options);
    
    // Create and return the WebSocket instance
    return new PaymentWebSocket(orderId, mergedOptions);
}

// Global variable to store the active WebSocket connection
let activePaymentWebSocket = null;

/**
 * WebSocket-based implementation of verifyPaymentStatusFromPG
 * Drop-in replacement for the existing function
 * @param {string} orderIdFromParent - Order ID to check
 */
function verifyPaymentStatusFromPGWebSocket(orderIdFromParent) {
    // Close any existing connection
    if (activePaymentWebSocket) {
        activePaymentWebSocket.disconnect(true);
        activePaymentWebSocket = null;
    }
    
    // Set loading state
    isResponseWaiting_Txn = true;
    
    // Create new WebSocket connection
    activePaymentWebSocket = createPaymentWebSocketConnection(orderIdFromParent, {
        onStatusUpdate: function(response) {
            // Clear loading state
            isResponseWaiting_Txn = false;
            
            // Process response (same logic as original function)
            if (response && response.status === 0 && response.data.status != 'pending') {
                if (isProccessingCompleted(response)) {
                    isTransactionCompleted = true;
                    notifyParent(response.data.status);
                } else if (response.data.status === WIOMUPIIntentCollectSDK.checkPaymentAPIStatusTypes.STILL_PROCESSING) {
                    isTransactionCompleted = false;
                }
            } else {
                isTransactionCompleted = false;
                if (response && response.data && response.data.error_info) {
                    console.log(response.data.error_info);
                }
            }
        },
        onConnectionError: function(error) {
            console.error('Payment WebSocket error:', error);
            isResponseWaiting_Txn = false;
            isTransactionCompleted = false;
            
            // Fallback to HTTP method if WebSocket fails
            console.log('Falling back to HTTP method for payment status check');
            checkPaymentStatus(orderIdFromParent).then(function(response) {
                isResponseWaiting_Txn = false;
                if (response && response.status === 0 && response.data.status != 'pending') {
                    if (isProccessingCompleted(response)) {
                        isTransactionCompleted = true;
                        notifyParent(response.data.status);
                    } else if (response.data.status === WIOMUPIIntentCollectSDK.checkPaymentAPIStatusTypes.STILL_PROCESSING) {
                        isTransactionCompleted = false;
                    }
                } else {
                    isTransactionCompleted = false;
                    console.log(response.data.error_info);
                }
            });
        }
    });
}
