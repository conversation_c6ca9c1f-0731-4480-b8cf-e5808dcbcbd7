<!doctype html>
<html lang="en">
	<head>
    	<meta http-equiv="X-UA-Compatible" content="IE=Edge, Chrome=1" />
    	<meta charset="utf-8" />
        <meta name="viewport" content="width=device-width, height=device-height, maximum-scale=1.0, minimum-scale=1.0, initial-scale=1.0"/>
        <title>Vodafone India</title>
        <link rel="stylesheet" href="/Templates/vodafone/css/style.css?ver=1">
        <link rel="stylesheet" href="/Templates/vodafone/css/fonts.css">
        <script src="/jsLibs/i2e1-sdk.js?ver=5"></script>
        <script src="/jsLibs/jquery.js"></script>
        <style>
            span.errorSpan{
                color:red;
            }
        </style>
        <script>
            $(document).ready(function () {
                $('#submitOTPError, #captchaDiv, #invalidOTP').hide();
                $('#invalidCaptcha, #invalidOTP').text('');
                i2e1Api.initialize(function () { $('#frmMobile').show(); }, function () { $('.content1').hide(); $('.content2').show(); });
            });
            function generateOTP(change) {
                i2e1Api.generateOTP($('#mobile').val(), change,
                    function () {
                        $('#frmMobile').hide();
                        $('#frmOtp').show();
                    },
                    function () { alert('Some error occurred. Please try again'); })
            }
            function submitOTP() {
                $('#submitOTPError, #captchaDiv, #invalidOTP').hide();
                i2e1Api.submitOTP($('#mobile').val(), $('#otp').val(), $('#name').val(), $('#captchaText').val(), function () {
                    $('#frmOtp').hide();
                    $('.background').attr('src', "/Templates/vodafone/images/coupons.jpg");
                    $('.promotional_offer').show();
                    $('.content1').hide();
                }, function (response) {
                    if(response.data){
                        $('#captchaImg').attr('src', response.data);
                        if(response.msg)
                            $('#invalidCaptcha').text(response.msg);
                        $('#submitOTPError, #captchaDiv').show();
                        $('#invalidOTP').hide();
                    }
                    else {
                        if (response.msg)
                            $('#invalidOTP').text(response.msg);
                        $('#submitOTPError, #invalidOTP').show();
                        $('#captchaDiv').hide();
                    }
                }, 5);
            }
        </script>
    </head>
    <body>
        <div id="loader" style="display:none;">
            <div class="loader-overlay" style="z-index:10;position: fixed; width: 100%; height: 100%;background-color: white; top: 0;left: 0; opacity: 0.6;"></div>
            <div style="position: fixed; left: 50%;top: 32%;z-index: 100;">
                <div style="position: relative; left: -50%;">
                    <img src="/images/Loader.gif" width="100px">
                </div>
            </div>
        </div>
    	<div class="container">
            <img src="/Templates/vodafone/images/background.jpg" class="background" alt="Vodafone">
        	<a href="javascript:;" class="logo" target="_self">
                <img src="/Templates/vodafone/images/logo.png" alt="Vodafone India">
            </a>
            <div class="main_content">
            	<div class="content1">
                    <h4>Enter the world of</h4>
                    <h1>Vodafone SuperNet<sup>&trade;</sup> 4G<br>for FREE</h1>
                    <div id="frmMobile">
                        <p>Enter your mobile number</p>
                        <input type="text" id="mobile" maxlength="10">
                        <a href="javascript:;" id="btnMobile" class="button" onclick="generateOTP()">Get OTP</a>
                        <div style="font-size: 14px;text-align: center;padding: 5px 0;" class="firstStateLabel">
                            <label for="option" style="font-size: 14px;">By Clicking "Get OTP" you agree to our <a style="font-weight:bold;" href="http://i2e1storage.blob.core.windows.net/uploadedresources/T_and_C_vodafone.htm">Terms & Conditions</a></label>
                        </div>
                    </div>
                    <div id="frmOtp">
                        <input style="position: absolute;right: 70px;margin-top: -49px;font-size: 12px;height: 24px;line-height: 22px;width: 60px;" type="button" value="Resend" class="button" onclick="generateOTP(true)" />
                        <p>Input the OTP you have received<br>on your mobile number</p>
                        <input type="text" id="otp" maxlength="6">
                        <div id="submitOTPError">
                            <span class="errorSpan" id="invalidOTP">Invalid OTP</span>
                            <div id="captchaDiv" style="text-align:center;">
                                <p>Please validate you are not bot.</p>
                                <img style="margin:10px 0;display:block;" id="captchaImg" />
                                <br/>
                                <span class="errorSpan" id="invalidCaptcha">Invalid Captcha</span>
                                <br/>
                                <input placeholder="Enter Captcha" id="captchaText" class="mobile-input" type="text" />
                            </div>
                        </div>
                        <a href="javascript:;" id="btnOtp" class="button" onclick="submitOTP()">Submit</a>
                    </div>
                   
				</div>
                <div class="promotional_offer" style="position: relative;top: 100px;">
                    <p>You will be connected in <span class="redColor">05</span> seconds.</p>
                    
                </div>
                <div class="content2" style="position: relative;top: 100px;">
                	<h3>Your </h3>
                    <h1>Vodafone SuperNet<sup>&trade;</sup> 4G</h1>
                    <h3>experience is here!</h3>
                    <p>This internet session lasts for <span class="redColor">10 Mins</span> only.</p>
                </div>
                <div class="content3">
                	<h3>We hope you enjoyed the</h3>
                    <h1>Vodafone SuperNet<sup>&trade;</sup> 4G</h1>
                    <h2>experience!</h2>
                    <h5>Do come back soon.</h5>
                </div>
                <div class="content4">
                	<h3>We are working hard to bring the</h3>
                    <h1>Vodafone SuperNet<sup>&trade;</sup> 4G</h1>
                    <h2>experience to you!</h2>
                    <h5>Please try again soon.</h5>
                </div>
            </div>
            <div class="footer">
                <img src="/Templates/vodafone/images/i2e1-logo.png" alt="Powered By i2e1">
            </div>
        </div>
    </body>
</html>