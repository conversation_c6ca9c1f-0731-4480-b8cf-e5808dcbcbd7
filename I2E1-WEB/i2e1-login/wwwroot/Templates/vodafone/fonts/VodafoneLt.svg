<?xml version="1.0" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd" >
<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1">
<metadata>
Created by FontForge 20150318 at Wed Aug 26 10:15:18 2015
 By uniteet7
Copyright (c) 2006 Dalton Maag Ltd. All rights reserved. This font may not be altered in any way without prior consent of Dalton Maag Ltd. DaMa is a trademark of Dalton Maag Ltd
</metadata>
<defs>
<font id="VodafoneLt" horiz-adv-x="501" >
  <font-face 
    font-family="VodafoneLt"
    font-weight="400"
    font-stretch="normal"
    units-per-em="1000"
    panose-1="2 11 6 6 4 2 2 2 2 4"
    ascent="800"
    descent="-200"
    x-height="477"
    cap-height="667"
    bbox="-207 -220 1000 899"
    underline-thickness="70"
    underline-position="-115"
    unicode-range="U+000D-FB02"
  />
<missing-glyph horiz-adv-x="205" 
 />
    <glyph glyph-name=".notdef" horiz-adv-x="205" 
 />
    <glyph glyph-name=".null" horiz-adv-x="950" 
 />
    <glyph glyph-name=".null" horiz-adv-x="950" 
 />
    <glyph glyph-name="nonmarkingreturn" unicode="&#xd;" horiz-adv-x="500" 
 />
    <glyph glyph-name="space" unicode=" " horiz-adv-x="205" 
 />
    <glyph glyph-name="exclam" unicode="!" horiz-adv-x="167" 
d="M84 -11q-17 0 -28 11.5t-11 31.5t11 31t28 11t28.5 -11t11.5 -31t-11.5 -31.5t-28.5 -11.5zM59 207v460h50v-460h-50z" />
    <glyph glyph-name="quotedbl" unicode="&#x22;" horiz-adv-x="274" 
d="M51 371v296h47v-296h-47zM176 371v296h48v-296h-48z" />
    <glyph glyph-name="numbersign" unicode="#" horiz-adv-x="553" 
d="M165 74v173h-160v47h160v149h-160v46h160v178h45v-178h133v178h46v-178h159v-46h-159v-149h159v-47h-159v-173h-46v173h-133v-173h-45zM210 294h133v149h-133v-149z" />
    <glyph glyph-name="dollar" unicode="$" 
d="M226 -134v124q-32 1 -59 8.5t-49 19t-39 25t-29 26.5l33 41q23 -29 62 -50t81 -22v297q-66 27 -103 59t-46.5 63.5t-9.5 61.5q0 37 12.5 66t34.5 48.5t51 30.5t61 12v123h44v-123q58 -2 98 -27.5t63 -57.5l-33 -32q-23 29 -54.5 48.5t-73.5 21.5v-254q43 -20 74.5 -39.5
t52 -43t30.5 -53.5t10 -70q0 -75 -43 -123.5t-124 -55.5v-124h-44zM270 38q54 5 84.5 38.5t30.5 90.5q1 31 -7.5 54t-24.5 40.5t-37.5 31.5t-45.5 26v-281zM226 390v239q-51 -2 -79.5 -30t-28.5 -73q0 -44 20 -73.5t44 -41.5t44 -21z" />
    <glyph glyph-name="percent" unicode="%" horiz-adv-x="681" 
d="M59 -7l-31 35l591 644l31 -33zM517 -7q-28 0 -52.5 11.5t-42.5 31.5t-28.5 46.5t-10.5 57.5q-1 34 12 62t33.5 47t44.5 28t44 9q47 0 79 -27.5t44.5 -62t12.5 -56.5q0 -45 -23 -83t-53.5 -51t-59.5 -13zM517 36q29 0 52.5 20t31 44.5t7.5 39.5q0 38 -18 63.5t-39 33.5
t-34 8q-32 0 -54.5 -21t-29 -44.5t-6.5 -39.5q0 -21 7 -40t19 -33.5t28.5 -22.5t35.5 -8zM166 380q-29 0 -53.5 11.5t-42.5 31.5t-28.5 46.5t-10.5 57.5q0 34 12.5 62t33 47t44.5 28t45 9q46 0 78.5 -29t44 -62.5t11.5 -54.5q0 -45 -23 -83q-23 -37 -53.5 -50.5t-57.5 -13.5
zM165 422q32 0 53.5 19.5t29.5 44t8 41.5q0 38 -19.5 63.5t-39 33t-32.5 7.5q-33 0 -56 -21.5t-29 -44t-6 -38.5q0 -22 7 -41t19 -33.5t28.5 -22.5t36.5 -8z" />
    <glyph glyph-name="ampersand" unicode="&#x26;" horiz-adv-x="554" 
d="M201 -11q-43 0 -75.5 14t-55 39t-34 58t-11.5 71q0 72 38.5 125t104.5 90l30 17q-23 32 -43 69.5t-20 75.5q0 57 38.5 91t102.5 34q48 0 85 -22t59 -77l-36 -25q-20 45 -46.5 64t-61.5 19q-42 0 -66.5 -23.5t-24.5 -58.5q0 -36 21 -72t36 -55l146 -218l105 182l35 -26
l-110 -194l114 -167h-55l-83 123q-37 -66 -83.5 -100t-109.5 -34zM203 33q49 0 88 33t72 94l-143 208l-27 -15q-55 -31 -87 -74t-32 -106q0 -29 8.5 -54.5t25 -44.5t40.5 -30t55 -11z" />
    <glyph glyph-name="quotesingle" unicode="'" horiz-adv-x="148" 
d="M51 371v296h46v-296h-46z" />
    <glyph glyph-name="parenleft" unicode="(" horiz-adv-x="254" 
d="M226 -49q-41 26 -74 65.5t-57 89.5t-37 109.5t-13 126.5q0 137 49 236t130 153l25 -32q-72 -57 -113 -148t-41 -209t41.5 -208t110.5 -147z" />
    <glyph glyph-name="parenright" unicode=")" horiz-adv-x="253" 
d="M28 -49l-21 36q69 57 110.5 147t41.5 208t-41 209t-113 148l25 32q81 -54 130 -153t49 -236q0 -100 -29 -184q-28 -82 -70 -131t-82 -76z" />
    <glyph glyph-name="asterisk" unicode="*" horiz-adv-x="390" 
d="M175 321v152l-118 -84l-21 36l127 87l-127 90l22 35l117 -85v145h39v-145l119 84l21 -33l-127 -90l127 -89l-20 -35l-120 84v-152h-39z" />
    <glyph glyph-name="plus" unicode="+" 
d="M227 85v223h-191v48h191v222h47v-222h190v-48h-190v-223h-47z" />
    <glyph glyph-name="comma" unicode="," horiz-adv-x="158" 
d="M57 -97l-17 22q13 13 25.5 31t14.5 35q-18 0 -30.5 13.5t-12.5 32.5t11 31t30 12q22 0 33.5 -16.5t11.5 -39.5q0 -35 -20 -67.5t-46 -53.5z" />
    <glyph glyph-name="hyphen" unicode="-" horiz-adv-x="292" 
d="M46 239v53h201v-53h-201z" />
    <glyph glyph-name="period" unicode="." horiz-adv-x="161" 
d="M84 -11q-17 0 -29 12t-12 33q0 20 12 32t29 12t29.5 -12t12.5 -32q0 -21 -12.5 -33t-29.5 -12z" />
    <glyph glyph-name="slash" unicode="/" horiz-adv-x="450" 
d="M45 -23l-40 25l401 756l39 -23z" />
    <glyph glyph-name="zero" unicode="0" 
d="M251 -14q-56 0 -96.5 30.5t-66.5 79.5t-38.5 110t-12.5 123t12.5 123.5t38.5 110.5t66.5 79.5t96.5 30.5q78 0 129 -62q51 -60 68 -141q16 -77 16 -141q0 -99 -30 -184.5t-78.5 -122t-104.5 -36.5zM251 34q59 0 96 55q39 55 51 123q12 69 12 118q0 85 -21 155.5
t-58.5 104.5t-79.5 34q-43 0 -73 -26.5t-49.5 -69t-28.5 -95t-9 -103.5q0 -49 9 -101.5t28.5 -95.5t49.5 -71t73 -28z" />
    <glyph glyph-name="one" unicode="1" 
d="M263 0v598l-148 -106l-25 39l177 129h48v-660h-52z" />
    <glyph glyph-name="two" unicode="2" 
d="M59 0v44q0 54 16 90t59 75l122 105q61 51 82 73q43 45 43 110q0 60 -34.5 94t-97.5 34q-53 0 -92.5 -24.5t-76.5 -61.5l-26 40q22 27 57.5 49t73 33.5t67.5 11.5q72 0 116.5 -33.5t56.5 -73.5t12 -67q0 -65 -31 -106q-37 -46 -104 -102l-130 -110q-35 -29 -47 -59
t-12 -74h329v-48h-383z" />
    <glyph glyph-name="three" unicode="3" 
d="M228 -14q-56 0 -100.5 14.5t-63.5 30.5t-28 25l24 48q32 -33 75 -51.5t91 -18.5q72 0 114.5 40t42.5 112q1 59 -29.5 99t-70.5 52t-73 11q-33 0 -52 -3v43q26 43 98 119q69 74 110 104h-307v48h367v-58q-39 -25 -110 -96t-101 -116h15q79 0 128.5 -35t63.5 -81
q15 -47 15 -82q0 -71 -33 -120.5t-80.5 -67t-95.5 -17.5z" />
    <glyph glyph-name="four" unicode="4" 
d="M339 0v163h-298v56l311 441h36v-449h69v-48h-69v-163h-49zM88 211h251v357z" />
    <glyph glyph-name="five" unicode="5" 
d="M237 -14q-51 0 -94.5 13.5t-65 27.5t-29.5 22l22 49q26 -23 70 -43t97 -20t88 24.5t50.5 64.5t15.5 79q0 68 -32 109q-31 41 -78.5 55t-87.5 14q-35 0 -63 -3.5t-42 -8.5v291h323v-48h-273v-189q52 5 64 5q81 0 140 -33q57 -31 79 -85q22 -51 22 -107q0 -49 -15 -89
t-42.5 -68.5t-65.5 -44t-83 -15.5z" />
    <glyph glyph-name="six" unicode="6" 
d="M265 -14q-50 0 -88.5 19t-65 54.5t-40 85t-13.5 110.5q0 91 28 171q28 81 68 127.5t85 72.5q33 20 73 31t82 11l11 -42q-49 -2 -91.5 -16t-77.5 -43q-45 -36 -73.5 -88t-40.5 -115q28 23 64 39t87 16q63 0 109 -35q32 -26 50 -58.5t23.5 -64.5t5.5 -55q0 -76 -30 -128
q-22 -38 -53 -58t-61.5 -27t-51.5 -7zM265 32q66 0 106 47t40 125q0 63 -26 102.5t-60.5 53t-62.5 13.5q-45 0 -84.5 -20t-64.5 -44q-4 -24 -4 -61q0 -46 9 -85.5t28.5 -68.5t48.5 -45.5t70 -16.5z" />
    <glyph glyph-name="seven" unicode="7" 
d="M157 0q18 174 79.5 331t155.5 281h-337v48h398v-39q-41 -55 -74.5 -111.5t-61.5 -115.5q-37 -80 -60.5 -155t-33 -136t-13.5 -103h-53z" />
    <glyph glyph-name="eight" unicode="8" 
d="M250 -14q-48 0 -85.5 14t-63.5 39t-39.5 60t-13.5 76q0 70 34 110.5t85 59.5q-28 9 -49 26.5t-35 39.5t-20.5 45.5t-4.5 45.5q-1 43 17 76t46.5 54.5t62.5 31t66 9.5q69 0 115.5 -32t62.5 -71.5t14 -67.5q1 -43 -19.5 -81t-47.5 -55t-42 -21q51 -19 85 -59.5t34 -110.5
q0 -67 -36 -114.5t-84 -61t-82 -13.5zM250 33q69 0 110.5 35.5t41.5 107.5q0 49 -29 85.5t-64 49t-59 12.5q-57 0 -95.5 -30.5t-47.5 -63t-9 -53.5q0 -72 41.5 -107.5t110.5 -35.5zM250 368q48 0 81.5 20.5t47.5 51.5t14 63q0 57 -38.5 91t-104.5 34q-64 0 -103 -34t-39 -91
q0 -63 39.5 -99t102.5 -36z" />
    <glyph glyph-name="nine" unicode="9" 
d="M86 -11l9 47q26 0 66 8q38 8 64.5 21.5t44.5 30.5q55 49 77.5 92t43.5 113q-24 -19 -65 -36.5t-89 -17.5q-44 0 -79.5 16t-60.5 44.5t-38.5 67t-13.5 84.5q0 77 35 129.5t80.5 68.5t80.5 16q78 0 129 -46.5t65.5 -109.5t14.5 -106q0 -75 -18.5 -145.5t-52 -119
t-68.5 -77.5q-43 -36 -98 -58t-127 -22zM243 293q48 0 86 20.5t65 43.5q3 24 3 55q0 79 -26 130.5t-63.5 68t-66.5 16.5q-67 0 -106.5 -45.5t-39.5 -120.5q0 -76 39 -122t109 -46z" />
    <glyph glyph-name="colon" unicode=":" horiz-adv-x="161" 
d="M89 392q-17 0 -29 12t-12 33q0 20 12 32t29 12t29.5 -12t12.5 -32q0 -21 -12.5 -33t-29.5 -12zM89 -11q-17 0 -29 12t-12 33q0 20 12 32t29 12t29.5 -12t12.5 -32q0 -21 -12.5 -33t-29.5 -12z" />
    <glyph glyph-name="semicolon" unicode=";" horiz-adv-x="164" 
d="M68 -97l-17 22q13 13 25.5 31t14.5 35q-18 0 -30.5 13.5t-12.5 32.5t11 31t30 12q22 0 33.5 -16.5t11.5 -39.5q0 -35 -20 -67.5t-46 -53.5zM84 392q-17 0 -29 12t-12 33q0 20 12 32t29 12t29.5 -12t12.5 -32q0 -21 -12.5 -33t-29.5 -12z" />
    <glyph glyph-name="less" unicode="&#x3c;" 
d="M435 151l-380 160v44l374 155l18 -46l-317 -130l323 -135z" />
    <glyph glyph-name="equal" unicode="=" 
d="M36 196v48h429v-48h-429zM36 379v48h429v-48h-429z" />
    <glyph glyph-name="greater" unicode="&#x3e;" 
d="M72 151l-17 48l322 135l-316 130l19 46l373 -155v-44z" />
    <glyph glyph-name="question" unicode="?" horiz-adv-x="341" 
d="M164 -11q-17 0 -28 11.5t-11 31.5t11 31t28 11t28.5 -11t11.5 -31t-11.5 -31.5t-28.5 -11.5zM139 192v30q0 45 20 77t46 63q29 33 45 58.5t23.5 50.5t7.5 50q0 51 -30 78t-81 27q-45 0 -75 -23t-54 -55l-32 32q26 38 68 65.5t95 27.5q78 0 120 -41t42 -113
q0 -49 -23.5 -92t-62.5 -86q-41 -46 -50 -68q-11 -24 -11 -55v-26h-48z" />
    <glyph glyph-name="at" unicode="@" horiz-adv-x="719" 
d="M435 -157q-81 0 -141.5 19t-103.5 51t-70.5 73t-43.5 84t-22 86t-6 78q0 96 21.5 173t63.5 131.5t104 83.5t144 29q93 0 151 -37q59 -37 79 -87.5t20 -91.5v-415h-48v48q-24 -20 -57.5 -34.5t-75.5 -14.5q-36 0 -67 15t-53.5 42t-35.5 65.5t-13 85.5q-2 51 14 89.5
t41.5 64t56 37.5t57.5 10q45 0 76 -12t57 -29v38q0 42 -12.5 75.5t-38.5 54.5q-28 24 -65 37.5t-86 13.5q-106 0 -173.5 -61t-88.5 -145t-21 -166q0 -95 39 -179q39 -83 120 -124.5t184 -41.5q99 0 214 43l14 -45q-32 -15 -88.5 -29.5t-145.5 -14.5zM452 66q42 0 73.5 16
t56.5 36v219q-26 18 -55.5 30.5t-71.5 12.5q-60 0 -91 -42.5t-31 -111.5q0 -32 8 -61t23 -51t37 -35t51 -13z" />
    <glyph glyph-name="A" unicode="A" horiz-adv-x="533" 
d="M5 0l236 667h48l239 -667h-55l-78 214h-260l-76 -214h-54zM151 264h228l-115 340z" />
    <glyph glyph-name="B" unicode="B" horiz-adv-x="500" 
d="M76 0v667h172q91 0 138 -40.5t47 -125.5q0 -123 -122 -147q69 -5 111.5 -46t42.5 -123q0 -44 -13.5 -78t-37.5 -58t-57 -36.5t-73 -12.5h-208zM128 49h152q63 0 96.5 37t33.5 100q0 75 -43.5 108t-121.5 33h-117v-278zM128 376h114q67 0 102 34.5t35 91.5q0 60 -33.5 88
t-96.5 28h-121v-242z" />
    <glyph glyph-name="C" unicode="C" horiz-adv-x="542" 
d="M312 -12q-62 0 -110.5 24t-82.5 68.5t-51.5 108t-17.5 142.5q0 85 19.5 150t54 109t81.5 67t102 23q70 0 122 -35.5t79 -92.5l-42 -33q-21 42 -53.5 68t-59.5 35t-45 8q-68 0 -118 -44q-47 -43 -67 -114q-18 -69 -18 -142q0 -65 14.5 -119t42 -92.5t66.5 -60t88 -21.5
q54 0 94 23t72 65l30 -42q-18 -23 -50 -48q-27 -21 -68.5 -34t-81.5 -13z" />
    <glyph glyph-name="D" unicode="D" horiz-adv-x="583" 
d="M76 0v667h156q42 0 80 -7t70 -24q72 -35 112.5 -108t40.5 -185q0 -138 -52.5 -220t-145.5 -109q-48 -14 -105 -14h-156zM128 49h95q26 0 53.5 3t50.5 12q70 26 112 93t42 182q0 88 -27 147.5t-78 92.5q-30 20 -68.5 29.5t-80.5 9.5h-99v-569z" />
    <glyph glyph-name="E" unicode="E" horiz-adv-x="476" 
d="M76 0v667h356v-49h-305v-240h221v-49h-221v-280h323v-49h-374z" />
    <glyph glyph-name="F" unicode="F" horiz-adv-x="430" 
d="M76 0v667h349v-49h-298v-250h220v-49h-220v-319h-51z" />
    <glyph glyph-name="G" unicode="G" horiz-adv-x="568" 
d="M318 -13q-60 0 -109 22.5t-84.5 66t-55 107.5t-19.5 148q0 121 44 207q43 84 102 113q60 29 114 29q42 0 74.5 -12.5t57.5 -32t41.5 -42t25.5 -39.5l-39 -31q-24 49 -67.5 78.5t-92.5 29.5q-42 0 -79 -19t-65 -56.5t-44.5 -94t-16.5 -131.5q0 -67 15.5 -121.5t43.5 -93
t67.5 -59.5t87.5 -21q51 0 85.5 7.5t56.5 16.5v209h-119v49h171v-293q-32 -14 -83.5 -25.5t-111.5 -11.5z" />
    <glyph glyph-name="H" unicode="H" horiz-adv-x="601" 
d="M76 0v667h52v-297h346v297h51v-667h-51v320h-346v-320h-52z" />
    <glyph glyph-name="I" unicode="I" horiz-adv-x="204" 
d="M76 0v667h52v-667h-52z" />
    <glyph glyph-name="J" unicode="J" horiz-adv-x="353" 
d="M142 -11q-44 0 -81.5 13t-48.5 25l18 47q7 -10 31.5 -20t43.5 -14t39 -4q51 0 69.5 27t18.5 79v525h52v-533q0 -51 -13 -77q-14 -30 -46 -49t-83 -19z" />
    <glyph glyph-name="K" unicode="K" horiz-adv-x="519" 
d="M76 0v667h52v-300h105q28 0 47.5 17.5t40.5 64.5l97 218h56l-101 -227q-16 -35 -31.5 -58t-35.5 -31q28 -8 43.5 -24.5t29.5 -51.5l110 -275h-56l-84 206q-25 58 -36.5 77.5t-28.5 27.5t-39 8h-117v-319h-52z" />
    <glyph glyph-name="L" unicode="L" horiz-adv-x="450" 
d="M76 0v667h52v-618h307v-49h-359z" />
    <glyph glyph-name="M" unicode="M" horiz-adv-x="698" 
d="M333 -11l-192 580l-24 -569h-51l30 667h67l198 -605l184 605h64l30 -667h-50l-25 561l-178 -572h-53z" />
    <glyph glyph-name="N" unicode="N" horiz-adv-x="613" 
d="M76 0v667h55l355 -562v562h51v-667h-45l-365 575v-575h-51z" />
    <glyph glyph-name="O" unicode="O" horiz-adv-x="617" 
d="M309 -13q-66 0 -114.5 29.5t-81 78t-48 111t-15.5 128.5q0 104 37 188q24 59 65 96.5t83 49.5t74 12q94 0 154 -60q61 -58 82 -140q22 -79 22 -146q0 -108 -37 -191q-26 -60 -67 -96.5t-81 -48t-73 -11.5zM309 36q72 0 121.5 52.5t66.5 122.5q16 67 16 123q0 88 -28 157
q-27 72 -74.5 106t-101.5 34q-53 0 -92 -26t-64 -68.5t-37 -95.5t-12 -107q0 -85 29 -161q30 -75 77 -106t99 -31z" />
    <glyph glyph-name="P" unicode="P" horiz-adv-x="504" 
d="M76 0v667h198q96 0 148 -49.5t52 -146.5q0 -47 -15 -83t-41.5 -60t-63 -36t-79.5 -12h-147v-280h-52zM128 329h147q66 0 104.5 38.5t38.5 104.5q0 69 -35.5 107.5t-108.5 38.5h-146v-289z" />
    <glyph glyph-name="Q" unicode="Q" horiz-adv-x="617" 
d="M584 -33q-40 51 -100 102q-31 -38 -74.5 -60t-100.5 -22q-66 0 -114.5 29.5t-81 78t-48 111t-15.5 128.5q0 104 37 188q24 59 65 96.5t83 49.5t74 12q94 0 154 -60q61 -58 82 -140q22 -79 22 -146q0 -63 -13.5 -121t-42.5 -106q53 -41 109 -111zM309 36q42 0 75 16.5
t57 45.5q-47 27 -120 51l15 45q75 -26 131 -57q23 42 34.5 93.5t11.5 103.5q0 88 -28 157q-27 72 -74.5 106t-101.5 34q-53 0 -92 -26t-64 -68.5t-37 -95.5t-12 -107q0 -85 29 -161q30 -75 77 -106t99 -31z" />
    <glyph glyph-name="R" unicode="R" horiz-adv-x="533" 
d="M76 0v667h200q97 0 147 -47t50 -134q1 -51 -20.5 -88t-47 -53t-42.5 -21q32 -10 46 -38.5t27 -73.5q4 -21 30 -109l27 -103h-55l-28 104q-35 117 -42 138q-10 28 -30 44t-50 16h-160v-302h-52zM128 351h144q72 0 109 33t37 102q0 63 -36.5 97.5t-106.5 34.5h-147v-267z
" />
    <glyph glyph-name="S" unicode="S" horiz-adv-x="480" 
d="M236 -13q-48 2 -98.5 20t-74 36.5t-31.5 28.5l29 48q14 -15 33.5 -30t43 -27t49 -19.5t51.5 -7.5q37 -2 65 9t46.5 29.5t28 43t8.5 50.5q0 30 -8.5 53t-25.5 41t-43.5 32.5t-62.5 28.5l-68 24q-60 21 -95.5 60.5t-35.5 102.5q0 55 30 96q31 42 75.5 58t86.5 16
q70 0 118.5 -26t77.5 -62l-32 -39q-24 30 -63.5 54t-96.5 24q-60 1 -99 -28.5t-40 -86.5q0 -48 28.5 -76t74.5 -45l65 -24q48 -17 80.5 -37t51.5 -44t27.5 -52.5t8.5 -61.5q0 -94 -55 -141.5t-149 -47.5z" />
    <glyph glyph-name="T" unicode="T" horiz-adv-x="455" 
d="M201 0v618h-196v49h445v-49h-197v-618h-52z" />
    <glyph glyph-name="U" unicode="U" horiz-adv-x="591" 
d="M296 -13q-48 0 -89.5 15.5t-72 45.5t-48 73.5t-17.5 100.5v445h51v-430q0 -91 45 -145.5t130 -54.5t130.5 53t45.5 147v430h52v-445q0 -57 -18 -100.5t-48.5 -73.5t-72 -45.5t-88.5 -15.5z" />
    <glyph glyph-name="V" unicode="V" horiz-adv-x="521" 
d="M231 0l-226 667h58l196 -596l204 596h53l-233 -667h-52z" />
    <glyph glyph-name="W" unicode="W" horiz-adv-x="718" 
d="M161 0l-151 667h53l126 -556l145 556h49l152 -559l121 559h52l-149 -667h-45l-156 566l-153 -566h-44z" />
    <glyph glyph-name="X" unicode="X" horiz-adv-x="465" 
d="M5 0l196 346l-179 321h61l149 -277l150 277h59l-179 -318l198 -349h-63l-166 306l-168 -306h-58z" />
    <glyph glyph-name="Y" unicode="Y" horiz-adv-x="499" 
d="M220 0v321l-215 346h59l183 -295l190 295h57l-222 -345v-322h-52z" />
    <glyph glyph-name="Z" unicode="Z" horiz-adv-x="518" 
d="M42 0v51l372 567h-356v49h408v-55l-369 -563h383v-49h-438z" />
    <glyph glyph-name="bracketleft" unicode="[" horiz-adv-x="194" 
d="M70 -143v879h119v-44h-72v-790h72v-45h-119z" />
    <glyph glyph-name="backslash" unicode="\" horiz-adv-x="447" 
d="M402 -18l-397 753l41 24l396 -751z" />
    <glyph glyph-name="bracketright" unicode="]" horiz-adv-x="194" 
d="M5 -143v45h72v790h-72v44h120v-879h-120z" />
    <glyph glyph-name="asciicircum" unicode="^" horiz-adv-x="476" 
d="M104 298l-43 19l154 352h44l157 -345l-44 -20l-135 301z" />
    <glyph glyph-name="underscore" unicode="_" horiz-adv-x="488" 
d="M0 -220v70h500v-70h-500z" />
    <glyph glyph-name="grave" unicode="`" horiz-adv-x="290" 
d="M172 545l-167 127l37 41l155 -139z" />
    <glyph glyph-name="a" unicode="a" horiz-adv-x="438" 
d="M196 -11q-32 0 -59.5 9.5t-47.5 28t-31.5 45t-11.5 61.5q0 70 50 112q48 42 114 53q64 10 117 12v19q0 56 -24 85q-27 31 -79 31q-40 0 -72.5 -16t-55.5 -33l-18 40q8 7 23 16t34.5 17.5t42.5 14t48 5.5q76 0 115 -44q35 -41 35 -116v-329h-44l-3 42q-21 -22 -54 -37.5
t-79 -15.5zM203 33q38 0 70 17t54 41v175q-118 -3 -174 -35.5t-56 -98.5q0 -29 11 -48.5t27.5 -31.5t35 -16.5t32.5 -2.5z" />
    <glyph glyph-name="b" unicode="b" horiz-adv-x="488" 
d="M222 -11q-48 0 -87.5 9.5t-65.5 21.5v647h49v-213q21 13 51 24t69 11q74 0 123.5 -44.5t64.5 -101.5t15 -102q0 -58 -17 -104.5t-46.5 -79.5t-69.5 -50.5t-86 -17.5zM213 33q46 0 80.5 19t57 50t32 68.5t9.5 71.5q0 66 -27 116q-26 50 -64 68t-73 18q-33 0 -60.5 -11
t-49.5 -28v-353q22 -9 48 -14t47 -5z" />
    <glyph glyph-name="c" unicode="c" horiz-adv-x="438" 
d="M251 -11q-44 0 -81.5 16.5t-64.5 48.5t-42.5 78t-15.5 106q0 91 33 150q23 40 53.5 63.5t60 30.5t50.5 7q45 0 84 -21.5t57.5 -45t21.5 -33.5l-36 -25q-26 40 -54 60.5t-73 20.5q-48 0 -85 -34t-49.5 -80.5t-12.5 -91.5q0 -47 11 -84.5t32 -64.5t50 -41.5t65 -14.5
q37 0 67 13t62 46l19 -43q-21 -22 -59 -41.5t-93 -19.5z" />
    <glyph glyph-name="d" unicode="d" horiz-adv-x="490" 
d="M244 -11q-53 0 -91 23t-62 60t-34 81.5t-10 86.5q0 54 15 99.5t42 79t65.5 52t86.5 18.5q42 0 70 -10t45 -21v209h50v-667h-45l-5 42q-22 -23 -53.5 -38t-73.5 -15zM250 33q38 0 69 18t52 44v312q-16 14 -45.5 26t-63.5 12q-44 0 -76 -20t-52.5 -50.5t-29 -67.5t-8.5 -69
q0 -93 40 -149t114 -56z" />
    <glyph glyph-name="e" unicode="e" horiz-adv-x="468" 
d="M255 -11q-100 0 -154 66t-54 184q0 85 30 144q31 60 76.5 83t88.5 23q73 0 118.5 -46.5t58.5 -101.5t13 -94h-336q0 -102 40.5 -158t119.5 -56q85 0 143 61l15 -45q-14 -14 -35 -27q-19 -12 -54.5 -22.5t-69.5 -10.5zM100 291h276q-3 55 -27 92q-24 38 -53.5 50t-53.5 12
q-54 0 -92.5 -36.5t-49.5 -117.5z" />
    <glyph glyph-name="f" unicode="f" horiz-adv-x="257" 
d="M89 0v433h-67v44h68v54q0 77 34 114q32 35 87 35q30 0 51.5 -6.5t33.5 -12.5l-11 -42q-12 6 -31.5 11.5t-42.5 5.5q-33 0 -50 -20q-22 -25 -22 -88v-51h105v-44h-105v-433h-50z" />
    <glyph glyph-name="g" unicode="g" horiz-adv-x="475" 
d="M231 -198q-48 0 -99 14q-47 13 -60 23l15 46q22 -14 60 -26.5t86 -12.5q72 0 106 34.5t34 109.5v34q-19 -13 -50 -24t-73 -11q-43 0 -79.5 17t-63.5 48.5t-42.5 77.5t-15.5 105q0 81 35 143q25 43 62.5 69.5t71.5 33t60 6.5q45 0 83 -6t62 -12v-493q0 -79 -46 -127.5
t-146 -48.5zM261 33q35 0 65 13.5t47 29.5v357q-17 5 -40.5 8.5t-52.5 3.5q-36 0 -69 -14t-58 -41t-39.5 -66t-14.5 -89t13 -87.5t35.5 -63t51.5 -38.5t62 -13z" />
    <glyph glyph-name="h" unicode="h" horiz-adv-x="495" 
d="M69 0v667h49v-232q26 21 62.5 37.5t84.5 16.5q66 0 105 -27t54 -78q9 -32 9 -86v-298h-50v295q0 49 -9 75q-26 74 -116 74q-42 0 -76.5 -17.5t-63.5 -40.5v-386h-49z" />
    <glyph glyph-name="i" unicode="i" horiz-adv-x="187" 
d="M69 0v477h49v-477h-49zM93 578q-15 0 -25.5 10t-10.5 30q0 18 10.5 28.5t25.5 10.5t25.5 -10.5t10.5 -28.5q0 -20 -10.5 -30t-25.5 -10z" />
    <glyph glyph-name="j" unicode="j" horiz-adv-x="187" 
d="M18 -196l-20 41q40 27 55.5 62t15.5 90v480h49v-480q0 -74 -25 -118.5t-75 -74.5zM96 578q-15 0 -25.5 10t-10.5 30q0 18 10.5 28.5t25.5 10.5t25.5 -10.5t10.5 -28.5q0 -20 -10.5 -30t-25.5 -10z" />
    <glyph glyph-name="k" unicode="k" horiz-adv-x="439" 
d="M69 0v667h49v-377h78q29 0 50 19t42 67l44 101h53l-50 -110q-16 -35 -33 -59t-37 -32q28 -8 44.5 -23.5t30.5 -50.5l80 -202h-52l-57 141q-26 56 -35 72t-28 24t-40 8h-90v-245h-49z" />
    <glyph glyph-name="l" unicode="l" horiz-adv-x="219" 
d="M148 -11q-39 0 -59 17.5t-22 39.5t-2 36v585h49v-573q0 -35 7.5 -49.5t33.5 -14.5q20 0 50 15l9 -40q-13 -7 -31 -11.5t-35 -4.5z" />
    <glyph glyph-name="m" unicode="m" horiz-adv-x="757" 
d="M69 0v477h45l3 -45q23 19 59 38t84 19q82 0 120 -57q26 23 64 40t89 17q84 0 123.5 -48t39.5 -139v-302h-51v306q0 67 -30 102t-87 35q-42 0 -74.5 -16t-55.5 -36q6 -15 10 -36.5t4 -47.5v-307h-50v311q0 52 -17.5 81t-44.5 40t-49 11q-37 0 -71.5 -18.5t-61.5 -43.5
v-381h-49z" />
    <glyph glyph-name="n" unicode="n" horiz-adv-x="495" 
d="M69 0v477h44l5 -45q26 23 62.5 40t84.5 17q67 0 106 -26.5t53 -78.5q10 -31 10 -86v-298h-50v295q0 49 -9 75q-26 74 -116 74q-43 0 -78 -18t-63 -44v-382h-49z" />
    <glyph glyph-name="o" unicode="o" horiz-adv-x="497" 
d="M248 -11q-72 0 -121 45q-44 39 -62 94.5t-18 113.5q0 75 29 135t75 86t97 26q70 0 120 -44q33 -30 52 -68t25 -75.5t6 -61.5q0 -79 -31 -140q-30 -60 -75.5 -85.5t-96.5 -25.5zM248 33q43 -2 72.5 20.5t48 55t26 69t6.5 63.5q1 66 -25 116t-62.5 70t-65.5 18
q-55 0 -93 -38q-35 -35 -47 -78t-12 -87q0 -39 9.5 -76.5t28.5 -67t47.5 -47.5t66.5 -18z" />
    <glyph glyph-name="p" unicode="p" horiz-adv-x="488" 
d="M69 -193v670h44l5 -41q23 21 55 37t77 16q66 0 113 -39q34 -29 50 -66t22 -75.5t6 -65.5q0 -86 -34 -147q-26 -47 -60.5 -71t-66 -30t-55.5 -6q-35 0 -61.5 8t-45.5 19v-209h-49zM220 33q78 0 124.5 55t46.5 152q1 73 -24 122.5t-61 66.5t-66 16q-38 0 -68.5 -18
t-53.5 -41v-319q18 -14 43.5 -24t58.5 -10z" />
    <glyph glyph-name="q" unicode="q" horiz-adv-x="490" 
d="M371 -193v217q-19 -13 -50 -24t-73 -11q-43 0 -79.5 17t-63.5 48.5t-42.5 77.5t-15.5 105q0 81 35 143q25 43 62.5 69.5t71.5 33t60 6.5q45 0 83 -6t62 -12v-664h-50zM259 33q35 0 65 13.5t47 29.5v357q-17 5 -40.5 8.5t-52.5 3.5q-36 0 -69 -14t-58 -41t-39.5 -66
t-14.5 -89t13 -87.5t35.5 -63t51.5 -38.5t62 -13z" />
    <glyph glyph-name="r" unicode="r" horiz-adv-x="292" 
d="M69 0v477h43l4 -49q21 21 55.5 40.5t61.5 19.5q25 0 54 -7l-7 -48q-18 7 -42 7q-32 0 -62.5 -18.5t-57.5 -45.5v-376h-49z" />
    <glyph glyph-name="s" unicode="s" horiz-adv-x="377" 
d="M188 -11q-59 0 -95 18t-52 34l19 45q17 -17 50 -35t78 -18q51 0 78.5 22.5t27.5 64.5t-26 68q-27 27 -95 52q-65 23 -93 52.5t-28 79.5q0 40 24.5 68t58.5 38.5t60 10.5q51 0 88 -18t56 -38l-21 -39q-29 25 -59 38.5t-68 13.5q-45 0 -67 -20t-22 -51q0 -18 5.5 -31
t18.5 -24.5t35.5 -21.5t55.5 -22q63 -23 95 -56.5t32 -95.5q0 -37 -14.5 -63t-38 -42.5t-51.5 -23t-52 -6.5z" />
    <glyph glyph-name="t" unicode="t" horiz-adv-x="291" 
d="M194 -11q-66 0 -86 53q-9 21 -9 55v336h-78v44h78v127h49v-127h119v-44h-119v-332q0 -38 14.5 -53t40.5 -15q23 0 58 22l20 -37q-14 -11 -33 -20t-54 -9z" />
    <glyph glyph-name="u" unicode="u" horiz-adv-x="494" 
d="M229 -12q-126 0 -158 104q-10 31 -10 85v300h49v-298q0 -50 10 -74q14 -38 42.5 -55t73.5 -17q43 0 77 18t63 44v382h49v-477h-45l-4 45q-26 -23 -61 -40t-86 -17z" />
    <glyph glyph-name="v" unicode="v" horiz-adv-x="413" 
d="M181 0l-176 477h54l149 -414l149 414h51l-174 -477h-53z" />
    <glyph glyph-name="w" unicode="w" horiz-adv-x="628" 
d="M144 0l-139 477h54l113 -410l120 410h44l120 -408l115 408h52l-143 -477h-51l-116 393l-119 -393h-50z" />
    <glyph glyph-name="x" unicode="x" horiz-adv-x="371" 
d="M5 0l154 244l-146 233h55l118 -192l119 192h53l-146 -232l154 -245h-54l-126 204l-127 -204h-54z" />
    <glyph glyph-name="y" unicode="y" horiz-adv-x="448" 
d="M-4 -192l-6 44h8q63 0 116 36t81 99l-186 490h55l163 -438q42 88 84 201.5t75 236.5h52q-35 -138 -76.5 -243t-79.5 -187q-43 -90 -78 -137.5t-90 -74.5t-118 -27z" />
    <glyph glyph-name="z" unicode="z" horiz-adv-x="426" 
d="M41 0v52l290 381h-279v44h329v-53l-290 -380h297v-44h-347z" />
    <glyph glyph-name="braceleft" unicode="{" horiz-adv-x="221" 
d="M206 -65q-37 0 -63 15.5t-36 43.5t-10 61v150q0 57 -18.5 79.5t-59.5 29.5v38q41 7 59.5 29.5t18.5 79.5v150q0 49 19.5 78t45 35.5t44.5 6.5l5 -38q-40 -2 -52 -24t-12 -67v-141q0 -55 -16 -84.5t-59 -43.5q43 -14 59 -43.5t16 -84.5v-141q0 -45 12 -67t52 -24z" />
    <glyph glyph-name="bar" unicode="|" horiz-adv-x="187" 
d="M70 -74v812h47v-812h-47z" />
    <glyph glyph-name="braceright" unicode="}" horiz-adv-x="221" 
d="M15 -65l-5 38q40 2 52 24t12 67v141q0 55 16 84.5t59 43.5q-43 14 -59 43.5t-16 84.5v141q0 45 -12 67t-52 24l5 38q43 0 68 -20.5t33 -48t8 -51.5v-150q0 -57 18.5 -79.5t59.5 -29.5v-38q-41 -7 -59.5 -29.5t-18.5 -79.5v-150q0 -48 -17 -74.5t-42.5 -36t-49.5 -9.5z
" />
    <glyph glyph-name="asciitilde" unicode="~" horiz-adv-x="412" 
d="M295 522q-26 0 -49.5 14.5t-49.5 34.5q-42 33 -69 33q-38 0 -57 -24t-24 -55h-42q8 60 39.5 91.5t85.5 31.5q30 0 52 -11.5t40 -25.5q52 -43 77 -43q32 0 47 23t19 58h44q-3 -52 -28 -83t-49 -37.5t-36 -6.5z" />
    <glyph glyph-name="uni00A0" unicode="&#xa0;" horiz-adv-x="246" 
 />
    <glyph glyph-name="exclamdown" unicode="&#xa1;" horiz-adv-x="167" 
d="M61 -194v460h50v-460h-50zM86 399q-17 0 -28 11t-11 31t11 31.5t28 11.5t28.5 -11.5t11.5 -31.5t-11.5 -31t-28.5 -11z" />
    <glyph glyph-name="cent" unicode="&#xa2;" 
d="M243 -4v145q-65 11 -106.5 60t-41.5 136q0 59 24 103q25 44 59 61.5t65 22.5v143h38v-142q37 -2 66 -17t46 -32l-30 -34q-15 15 -35 25t-47 12v-292q47 4 83 40l28 -35q-19 -21 -49.5 -35.5t-61.5 -16.5v-144h-38zM243 188v290q-42 -6 -70 -42t-28 -99q0 -69 26.5 -104.5
t71.5 -44.5z" />
    <glyph glyph-name="sterling" unicode="&#xa3;" 
d="M111 0v261h-64v47h64v166q0 97 44 148t125 51q54 0 96 -27.5t65 -65.5l-39 -32q-24 37 -50.5 57t-70.5 20q-117 0 -117 -155v-162h208v-47h-208v-213h278v-48h-331z" />
    <glyph glyph-name="currency" unicode="&#xa4;" horiz-adv-x="645" 
d="M53 37l-20 23l146 167q-25 38 -36.5 73t-11.5 70t10.5 68t33.5 71l-142 160l25 25l139 -159q35 29 66.5 41.5t63.5 12.5t61.5 -11.5t63.5 -38.5l134 155l26 -25l-137 -154q26 -39 37 -73.5t11 -71.5q0 -38 -12 -74t-40 -75l141 -162l-21 -22l-141 161q-35 -25 -64 -36
t-59 -11q-62 0 -126 51zM327 184q51 0 91 31q42 32 58 74t16 80q0 58 -28 102q-27 45 -65 64.5t-72 19.5q-51 0 -92 -32q-40 -31 -56.5 -73t-16.5 -81q0 -38 13 -72t35.5 -59t52.5 -39.5t64 -14.5z" />
    <glyph glyph-name="yen" unicode="&#xa5;" 
d="M216 0v217h-145v47h145v51l-27 48h-118v47h93l-139 249h58l160 -295l170 295h55l-146 -249h91v-47h-117l-28 -47v-52h144v-47h-144v-217h-52z" />
    <glyph glyph-name="brokenbar" unicode="&#xa6;" horiz-adv-x="187" 
d="M70 -74v276h47v-276h-47zM70 462v276h47v-276h-47z" />
    <glyph glyph-name="section" unicode="&#xa7;" horiz-adv-x="400" 
d="M190 -146q-45 0 -76 13.5t-54 34.5l25 42q21 -22 47 -34t55 -12q45 0 69.5 20t24.5 58q0 37 -26 68t-94 83q-63 45 -83 78t-20 75q0 57 34 92.5t85 50.5q-42 35 -65.5 75t-20.5 75q0 51 33.5 80t93.5 29q45 0 73.5 -15.5t45.5 -35.5l-26 -36q-16 19 -39.5 31.5
t-55.5 12.5q-78 0 -78 -67q0 -36 26 -67.5t77 -76.5q66 -61 88 -94q23 -32 23 -82q0 -37 -20.5 -68.5t-45.5 -46.5t-39 -18q44 -36 64 -68t20 -76q1 -31 -13 -54t-35 -38t-46.5 -22t-46.5 -7zM212 145q38 14 65 41t27 66q0 31 -20 62q-19 29 -77 80q-45 -11 -72.5 -39.5
t-27.5 -73.5q0 -37 18.5 -59t43.5 -42z" />
    <glyph glyph-name="dieresis" unicode="&#xa8;" horiz-adv-x="290" 
d="M59 601q-15 0 -25.5 10t-10.5 30q0 18 10.5 28.5t25.5 10.5t25.5 -10.5t10.5 -28.5q0 -20 -10.5 -30t-25.5 -10zM230 601q-15 0 -25.5 10t-10.5 30q0 18 10.5 28.5t25.5 10.5t25.5 -10.5t10.5 -28.5q0 -20 -10.5 -30t-25.5 -10z" />
    <glyph glyph-name="copyright" unicode="&#xa9;" horiz-adv-x="786" 
d="M393 -77q-81 0 -145.5 30.5t-109.5 81.5t-68.5 119t-23.5 143t23.5 142.5t68.5 118.5t109.5 81t145.5 30q115 0 200 -62q84 -61 115 -148q32 -88 32 -162q0 -75 -23.5 -143t-68.5 -119t-109.5 -81.5t-145.5 -30.5zM393 -35q100 0 173.5 56.5t100.5 134.5q27 76 27 141
q0 97 -45 178q-45 83 -115 117q-71 36 -141 36q-71 0 -127 -27.5t-94.5 -73.5t-59 -106t-20.5 -124t20.5 -124t59 -106t94.5 -74t127 -28zM399 72q-36 0 -68 13t-56 40.5t-38 69.5t-14 100q0 84 29 137q20 37 50 59t54.5 27t43.5 5q51 0 83.5 -24t49.5 -54l-36 -21
q-16 26 -37.5 42t-61.5 16q-57 0 -93 -48t-36 -139q0 -88 37 -135t94 -47q35 0 59.5 16.5t41.5 43.5l34 -25q-21 -34 -53 -55t-83 -21z" />
    <glyph glyph-name="ordfeminine" unicode="&#xaa;" horiz-adv-x="313" 
d="M145 364q-27 0 -48.5 9t-36.5 24.5t-21 35.5t-6 40q0 39 19 64.5t49.5 41t66 21.5t64.5 6v19q0 37 -19 54.5t-52 17.5q-24 0 -45 -8t-44 -27l-18 31q18 16 46 29.5t64 13.5q54 0 83 -30t29 -93v-240h-39l-3 29q-12 -11 -34 -24.5t-55 -13.5zM147 405q27 0 49 11.5
t36 28.5v123q-57 0 -95 -16t-49.5 -38t-11.5 -41q0 -35 21 -51.5t50 -16.5z" />
    <glyph glyph-name="guillemotleft" unicode="&#xab;" horiz-adv-x="467" 
d="M221 59l-183 191l167 199l31 -29l-140 -166l154 -165zM395 59l-187 191l171 199l30 -29l-145 -166l159 -164z" />
    <glyph glyph-name="logicalnot" unicode="&#xac;" 
d="M417 85v223h-381v48h428v-271h-47z" />
    <glyph glyph-name="uni00AD" unicode="&#xad;" horiz-adv-x="292" 
d="M46 239v53h201v-53h-201z" />
    <glyph glyph-name="registered" unicode="&#xae;" horiz-adv-x="786" 
d="M393 -77q-81 0 -145.5 30.5t-109.5 81.5t-68.5 119t-23.5 143t23.5 142.5t68.5 118.5t109.5 81t145.5 30q115 0 200 -62q84 -61 115 -148q32 -88 32 -162q0 -75 -23.5 -143t-68.5 -119t-109.5 -81.5t-145.5 -30.5zM393 -35q100 0 173.5 56.5t100.5 134.5q27 76 27 141
q0 97 -45 178q-45 83 -115 117q-71 36 -141 36q-71 0 -127 -27.5t-94.5 -73.5t-59 -106t-20.5 -124t20.5 -124t59 -106t94.5 -74t127 -28zM269 84v434h135q66 0 101.5 -31.5t35.5 -86.5q0 -29 -8 -50t-22.5 -35t-33.5 -22.5t-40 -12.5l112 -196h-51l-109 193h-77v-193h-43z
M312 317h89q46 0 70.5 19t24.5 64q0 38 -24 58.5t-67 20.5h-93v-162z" />
    <glyph glyph-name="macron" unicode="&#xaf;" horiz-adv-x="290" 
d="M5 607v46h279v-46h-279z" />
    <glyph glyph-name="degree" unicode="&#xb0;" horiz-adv-x="343" 
d="M180 388q-28 0 -53 11t-43.5 30t-29.5 44t-11 54q0 30 13 56.5t34 45.5t45 28.5t45 9.5q33 0 59 -14t43.5 -35.5t26 -46t8.5 -44.5q0 -46 -27 -81.5t-59.5 -46.5t-50.5 -11zM180 430q32 0 56 19t31.5 41.5t7.5 36.5q0 33 -18 55t-39.5 32t-37.5 10q-40 0 -67 -28.5
t-27 -68.5q0 -31 17 -57q18 -24 38 -32t39 -8z" />
    <glyph glyph-name="plusminus" unicode="&#xb1;" 
d="M36 0v48h428v-48h-428zM227 85v223h-191v48h191v222h47v-222h190v-48h-190v-223h-47z" />
    <glyph glyph-name="twosuperior" unicode="&#xb2;" horiz-adv-x="276" 
d="M40 335v39q0 24 7.5 44t32.5 39l59 45q31 23 43 39.5t12 41.5q0 21 -12.5 34.5t-43.5 13.5q-28 0 -50.5 -13.5t-36.5 -28.5l-20 36q7 12 42.5 30t68.5 18q52 0 75.5 -25.5t23.5 -60.5t-16 -57.5t-48 -46.5l-65 -50q-18 -14 -22 -27t-4 -30h160v-41h-206z" />
    <glyph glyph-name="threesuperior" unicode="&#xb3;" horiz-adv-x="276" 
d="M127 332q-38 0 -65 12t-36 21l16 38q11 -11 34 -22t52 -11q34 0 54.5 17t20.5 47q0 37 -26 53.5t-68 16.5q-15 0 -27 -2v33q15 20 41 44t61 48h-137v42h198v-44q-36 -22 -66.5 -45t-46.5 -43q58 0 87.5 -29t29.5 -68q0 -48 -32.5 -78t-89.5 -30z" />
    <glyph glyph-name="acute" unicode="&#xb4;" horiz-adv-x="290" 
d="M73 545l-24 29l155 139l37 -41z" />
    <glyph glyph-name="mu" unicode="&#xb5;" horiz-adv-x="505" 
d="M71 -193v670h49v-318q0 -63 36 -94.5t96 -31.5q45 0 76 19.5t57 49.5v375h49v-477h-44l-5 45q-26 -27 -56.5 -41.5t-81.5 -14.5q-53 0 -81.5 15t-45.5 37v-234h-49z" />
    <glyph glyph-name="paragraph" unicode="&#xb6;" horiz-adv-x="464" 
d="M221 -145v434q-59 0 -106 32q-23 16 -38.5 35.5t-24.5 40t-13 41t-4 40.5q0 39 13.5 73.5t37.5 60t55.5 40.5t67.5 15h58v-812h-46zM350 -145v812h47v-812h-47z" />
    <glyph glyph-name="periodcentered" unicode="&#xb7;" horiz-adv-x="272" 
d="M136 325q-17 0 -29 12t-12 33q0 20 12 32t29 12t29.5 -12t12.5 -32q0 -21 -12.5 -33t-29.5 -12z" />
    <glyph glyph-name="cedilla" unicode="&#xb8;" horiz-adv-x="290" 
d="M91 -174l48 130h59l-68 -130h-39z" />
    <glyph glyph-name="onesuperior" unicode="&#xb9;" horiz-adv-x="276" 
d="M137 335v282l-76 -45l-20 36l100 61h41v-334h-45z" />
    <glyph glyph-name="ordmasculine" unicode="&#xba;" horiz-adv-x="362" 
d="M181 366q-37 0 -64.5 16t-46 42.5t-28 60t-9.5 69.5q0 40 11.5 75t32.5 60t48 37t56 12q55 0 91.5 -37.5t47 -80t10.5 -67.5q0 -57 -22 -102.5t-56.5 -65t-70.5 -19.5zM181 405q28 0 48.5 14.5t33 38t18 50t5.5 46.5q0 43 -16.5 79t-43 51t-45.5 15q-37 0 -64 -27
q-26 -26 -33.5 -58.5t-7.5 -59.5q0 -47 16 -83.5t39 -51t50 -14.5z" />
    <glyph glyph-name="guillemotright" unicode="&#xbb;" horiz-adv-x="467" 
d="M88 59l-30 29l145 167l-159 163l28 31l187 -191zM262 60l-31 28l140 167l-154 164l29 30l183 -191z" />
    <glyph glyph-name="onequarter" unicode="&#xbc;" horiz-adv-x="680" 
d="M67 -7l-31 35l591 644l31 -33zM132 335v282l-76 -45l-20 36l100 61h41v-334h-45zM553 0v77h-159v40l166 217h37v-217h42v-40h-42v-77h-44zM441 117h112v147z" />
    <glyph glyph-name="onehalf" unicode="&#xbd;" horiz-adv-x="680" 
d="M43 -7l-31 35l591 644l31 -33zM128 335v282l-76 -45l-20 36l100 61h41v-334h-45zM426 3v39q0 24 7.5 44t32.5 39l59 45q31 23 43 39.5t12 41.5q0 21 -12.5 34.5t-43.5 13.5q-28 0 -50.5 -13.5t-36.5 -28.5l-20 36q7 12 42.5 30t68.5 18q52 0 75.5 -25.5t23.5 -60.5
t-16 -57.5t-48 -46.5l-65 -50q-18 -14 -22 -27t-4 -30h160v-41h-206z" />
    <glyph glyph-name="threequarters" unicode="&#xbe;" horiz-adv-x="680" 
d="M107 -7l-31 35l591 644l31 -33zM155 332q-38 0 -65 12t-36 21l16 38q11 -11 34 -22t52 -11q34 0 54.5 17t20.5 47q0 37 -26 53.5t-68 16.5q-15 0 -27 -2v33q15 20 41 44t61 48h-137v42h198v-44q-36 -22 -66.5 -45t-46.5 -43q58 0 87.5 -29t29.5 -68q0 -48 -32.5 -78
t-89.5 -30zM568 0v77h-159v40l166 217h37v-217h42v-40h-42v-77h-44zM456 117h112v147z" />
    <glyph glyph-name="questiondown" unicode="&#xbf;" horiz-adv-x="341" 
d="M173.05 490.997q16.9951 0 27.9912 -11.4971q10.9971 -11.4961 10.9971 -31.4902t-10.9971 -30.9902q-10.9961 -10.9971 -27.9912 -10.9971q-16.9941 0 -28.4912 10.9971q-11.4961 10.9961 -11.4961 30.9902t11.4961 31.4902q11.4971 11.4971 28.4912 11.4971z
M198.042 288.059v-29.9912q0 -44.9863 -19.9932 -76.9766q-19.9941 -31.9902 -45.9863 -62.9805q-28.9912 -32.9902 -44.9863 -58.4824t-23.4932 -50.4844q-7.49707 -24.9922 -7.49707 -49.9844q0 -50.9844 29.9902 -77.9766q29.9912 -26.9912 80.9756 -26.9912
q44.9863 0 74.9775 22.9922q29.9902 22.9932 53.9834 54.9834l31.9902 -31.9902q-25.9922 -37.9883 -67.9795 -65.4795q-41.9873 -27.4922 -94.9707 -27.4922q-77.9766 0 -119.964 40.9883q-41.9873 40.9873 -41.9873 112.965q0 48.9854 23.4932 91.9717
q23.4932 42.9873 62.4814 85.9746q40.9873 45.9854 49.9844 67.9785q10.9961 23.9932 10.9961 54.9834v25.9922h47.9854z" />
    <glyph glyph-name="Agrave" unicode="&#xc0;" horiz-adv-x="533" 
d="M5 0l236 667h48l239 -667h-55l-78 214h-260l-76 -214h-54zM151 264h228l-115 340zM340 711l-167 127l37 41l155 -139z" />
    <glyph glyph-name="Aacute" unicode="&#xc1;" horiz-adv-x="533" 
d="M5 0l236 667h48l239 -667h-55l-78 214h-260l-76 -214h-54zM151 264h228l-115 340zM202 711l-24 29l155 139l37 -41z" />
    <glyph glyph-name="Acircumflex" unicode="&#xc2;" horiz-adv-x="533" 
d="M5 0l236 667h48l239 -667h-55l-78 214h-260l-76 -214h-54zM151 264h228l-115 340zM165 708l-29 26l113 150h31l113 -150l-29 -26l-100 129z" />
    <glyph glyph-name="Atilde" unicode="&#xc3;" horiz-adv-x="533" 
d="M5 0l236 667h48l239 -667h-55l-78 214h-260l-76 -214h-54zM151 264h228l-115 340zM330 740q-20 0 -39.5 10t-33.5 21q-41 29 -59 29q-17 0 -31 -13t-27 -44l-35 15q16 43 41 62.5t58 19.5q20 0 38 -9t36 -22q34 -27 56 -27q15 0 29.5 14t28.5 49l35 -17q-18 -48 -44 -68
t-53 -20z" />
    <glyph glyph-name="Adieresis" unicode="&#xc4;" horiz-adv-x="533" 
d="M5 0l236 667h48l239 -667h-55l-78 214h-260l-76 -214h-54zM151 264h228l-115 340zM178 767q-15 0 -25.5 10t-10.5 30q0 18 10.5 28.5t25.5 10.5t25.5 -10.5t10.5 -28.5q0 -20 -10.5 -30t-25.5 -10zM349 767q-15 0 -25.5 10t-10.5 30q0 18 10.5 28.5t25.5 10.5t25.5 -10.5
t10.5 -28.5q0 -20 -10.5 -30t-25.5 -10z" />
    <glyph glyph-name="Aring" unicode="&#xc5;" horiz-adv-x="533" 
d="M5 0l236 667h48l239 -667h-55l-78 214h-260l-76 -214h-54zM151 264h228l-115 340zM262 700q-26 0 -44 10t-29.5 25.5t-15.5 32.5t-4 31q0 34 16 58t37.5 33t39.5 9q33 0 57 -21t30.5 -44t6.5 -35q0 -33 -17.5 -60t-38.5 -33t-38 -6zM263 735q25 0 39.5 17.5t14.5 46.5
q0 27 -16 46t-38 19t-38 -19t-16 -46q0 -29 15 -46.5t39 -17.5z" />
    <glyph glyph-name="AE" unicode="&#xc6;" horiz-adv-x="793" 
d="M-5 0l386 667h368v-49h-305v-240h221v-49h-221v-280h323v-49h-373v214h-222l-124 -214h-53zM199 264h195v340z" />
    <glyph glyph-name="Ccedilla" unicode="&#xc7;" horiz-adv-x="542" 
d="M312 -12q-62 0 -110.5 24t-82.5 68.5t-51.5 108t-17.5 142.5q0 85 19.5 150t54 109t81.5 67t102 23q70 0 122 -35.5t79 -92.5l-42 -33q-21 42 -53.5 68t-59.5 35t-45 8q-68 0 -118 -44q-47 -43 -67 -114q-18 -69 -18 -142q0 -65 14.5 -119t42 -92.5t66.5 -60t88 -21.5
q54 0 94 23t72 65l30 -42q-18 -23 -50 -48q-27 -21 -68.5 -34t-81.5 -13zM236 -174l48 130h59l-68 -130h-39z" />
    <glyph glyph-name="Egrave" unicode="&#xc8;" horiz-adv-x="476" 
d="M76 0v667h356v-49h-305v-240h221v-49h-221v-280h323v-49h-374zM314 711l-167 127l37 41l155 -139z" />
    <glyph glyph-name="Eacute" unicode="&#xc9;" horiz-adv-x="476" 
d="M76 0v667h356v-49h-305v-240h221v-49h-221v-280h323v-49h-374zM193 711l-24 29l155 139l37 -41z" />
    <glyph glyph-name="Ecircumflex" unicode="&#xca;" horiz-adv-x="476" 
d="M76 0v667h356v-49h-305v-240h221v-49h-221v-280h323v-49h-374zM154 708l-29 26l113 150h31l113 -150l-29 -26l-100 129z" />
    <glyph glyph-name="Edieresis" unicode="&#xcb;" horiz-adv-x="476" 
d="M76 0v667h356v-49h-305v-240h221v-49h-221v-280h323v-49h-374zM173 767q-15 0 -25.5 10t-10.5 30q0 18 10.5 28.5t25.5 10.5t25.5 -10.5t10.5 -28.5q0 -20 -10.5 -30t-25.5 -10zM344 767q-15 0 -25.5 10t-10.5 30q0 18 10.5 28.5t25.5 10.5t25.5 -10.5t10.5 -28.5
q0 -20 -10.5 -30t-25.5 -10z" />
    <glyph glyph-name="Igrave" unicode="&#xcc;" horiz-adv-x="204" 
d="M76 0v667h52v-667h-52zM172 711l-167 127l37 41l155 -139z" />
    <glyph glyph-name="Iacute" unicode="&#xcd;" horiz-adv-x="204" 
d="M76 0v667h52v-667h-52zM49 711l-24 29l155 139l37 -41z" />
    <glyph glyph-name="Icircumflex" unicode="&#xce;" horiz-adv-x="204" 
d="M76 0v667h52v-667h-52zM8 708l-29 26l113 150h31l113 -150l-29 -26l-100 129z" />
    <glyph glyph-name="Idieresis" unicode="&#xcf;" horiz-adv-x="204" 
d="M76 0v667h52v-667h-52zM21 767q-15 0 -25.5 10t-10.5 30q0 18 10.5 28.5t25.5 10.5t25.5 -10.5t10.5 -28.5q0 -20 -10.5 -30t-25.5 -10zM192 767q-15 0 -25.5 10t-10.5 30q0 18 10.5 28.5t25.5 10.5t25.5 -10.5t10.5 -28.5q0 -20 -10.5 -30t-25.5 -10z" />
    <glyph glyph-name="Eth" unicode="&#xd0;" horiz-adv-x="579" 
d="M72 0v313h-68v49h68v305h156q42 0 80 -7t70 -24q72 -35 112.5 -108t40.5 -185q0 -138 -52.5 -220t-145.5 -109q-48 -14 -105 -14h-156zM124 49h95q26 0 53.5 3t50.5 12q70 26 112 93t42 182q0 88 -27 147.5t-78 92.5q-30 20 -68.5 29.5t-80.5 9.5h-99v-256h167v-49h-167
v-264z" />
    <glyph glyph-name="Ntilde" unicode="&#xd1;" horiz-adv-x="613" 
d="M76 0v667h55l355 -562v562h51v-667h-45l-365 575v-575h-51zM385 740q-20 0 -39.5 10t-33.5 21q-41 29 -59 29q-17 0 -31 -13t-27 -44l-35 15q16 43 41 62.5t58 19.5q20 0 38 -9t36 -22q34 -27 56 -27q15 0 29.5 14t28.5 49l35 -17q-18 -48 -44 -68t-53 -20z" />
    <glyph glyph-name="Ograve" unicode="&#xd2;" horiz-adv-x="617" 
d="M309 -13q-66 0 -114.5 29.5t-81 78t-48 111t-15.5 128.5q0 104 37 188q24 59 65 96.5t83 49.5t74 12q94 0 154 -60q61 -58 82 -140q22 -79 22 -146q0 -108 -37 -191q-26 -60 -67 -96.5t-81 -48t-73 -11.5zM309 36q72 0 121.5 52.5t66.5 122.5q16 67 16 123q0 88 -28 157
q-27 72 -74.5 106t-101.5 34q-53 0 -92 -26t-64 -68.5t-37 -95.5t-12 -107q0 -85 29 -161q30 -75 77 -106t99 -31zM394 711l-167 127l37 41l155 -139z" />
    <glyph glyph-name="Oacute" unicode="&#xd3;" horiz-adv-x="617" 
d="M309 -13q-66 0 -114.5 29.5t-81 78t-48 111t-15.5 128.5q0 104 37 188q24 59 65 96.5t83 49.5t74 12q94 0 154 -60q61 -58 82 -140q22 -79 22 -146q0 -108 -37 -191q-26 -60 -67 -96.5t-81 -48t-73 -11.5zM309 36q72 0 121.5 52.5t66.5 122.5q16 67 16 123q0 88 -28 157
q-27 72 -74.5 106t-101.5 34q-53 0 -92 -26t-64 -68.5t-37 -95.5t-12 -107q0 -85 29 -161q30 -75 77 -106t99 -31zM247 711l-24 29l155 139l37 -41z" />
    <glyph glyph-name="Ocircumflex" unicode="&#xd4;" horiz-adv-x="617" 
d="M309 -13q-66 0 -114.5 29.5t-81 78t-48 111t-15.5 128.5q0 104 37 188q24 59 65 96.5t83 49.5t74 12q94 0 154 -60q61 -58 82 -140q22 -79 22 -146q0 -108 -37 -191q-26 -60 -67 -96.5t-81 -48t-73 -11.5zM309 36q72 0 121.5 52.5t66.5 122.5q16 67 16 123q0 88 -28 157
q-27 72 -74.5 106t-101.5 34q-53 0 -92 -26t-64 -68.5t-37 -95.5t-12 -107q0 -85 29 -161q30 -75 77 -106t99 -31zM215 708l-29 26l113 150h31l113 -150l-29 -26l-100 129z" />
    <glyph glyph-name="Otilde" unicode="&#xd5;" horiz-adv-x="617" 
d="M309 -13q-66 0 -114.5 29.5t-81 78t-48 111t-15.5 128.5q0 104 37 188q24 59 65 96.5t83 49.5t74 12q94 0 154 -60q61 -58 82 -140q22 -79 22 -146q0 -108 -37 -191q-26 -60 -67 -96.5t-81 -48t-73 -11.5zM309 36q72 0 121.5 52.5t66.5 122.5q16 67 16 123q0 88 -28 157
q-27 72 -74.5 106t-101.5 34q-53 0 -92 -26t-64 -68.5t-37 -95.5t-12 -107q0 -85 29 -161q30 -75 77 -106t99 -31zM378 740q-20 0 -39.5 10t-33.5 21q-41 29 -59 29q-17 0 -31 -13t-27 -44l-35 15q16 43 41 62.5t58 19.5q20 0 38 -9t36 -22q34 -27 56 -27q15 0 29.5 14
t28.5 49l35 -17q-18 -48 -44 -68t-53 -20z" />
    <glyph glyph-name="Odieresis" unicode="&#xd6;" horiz-adv-x="617" 
d="M309 -13q-66 0 -114.5 29.5t-81 78t-48 111t-15.5 128.5q0 104 37 188q24 59 65 96.5t83 49.5t74 12q94 0 154 -60q61 -58 82 -140q22 -79 22 -146q0 -108 -37 -191q-26 -60 -67 -96.5t-81 -48t-73 -11.5zM309 36q72 0 121.5 52.5t66.5 122.5q16 67 16 123q0 88 -28 157
q-27 72 -74.5 106t-101.5 34q-53 0 -92 -26t-64 -68.5t-37 -95.5t-12 -107q0 -85 29 -161q30 -75 77 -106t99 -31zM224 767q-15 0 -25.5 10t-10.5 30q0 18 10.5 28.5t25.5 10.5t25.5 -10.5t10.5 -28.5q0 -20 -10.5 -30t-25.5 -10zM395 767q-15 0 -25.5 10t-10.5 30
q0 18 10.5 28.5t25.5 10.5t25.5 -10.5t10.5 -28.5q0 -20 -10.5 -30t-25.5 -10z" />
    <glyph glyph-name="multiply" unicode="&#xd7;" 
d="M102 140l-33 35l149 156l-149 157l33 35l148 -157l149 157l34 -35l-150 -157l150 -156l-34 -35l-149 157z" />
    <glyph glyph-name="Oslash" unicode="&#xd8;" horiz-adv-x="618" 
d="M85 -22l-37 31l65 87q-32 49 -47 110.5t-15 127.5q0 104 37 188q24 59 65 96.5t82.5 49.5t74.5 12q57 0 101 -22t76 -60l64 87l39 -31l-74 -99q26 -48 39 -104.5t13 -116.5q0 -65 -15.5 -127.5t-47 -111t-80.5 -78.5t-115 -30q-52 0 -93 19t-73 51zM310 36
q72 0 121.5 52.5t66.5 122.5q16 67 16 123q0 45 -8.5 89.5t-25.5 83.5l-303 -406q24 -30 57.5 -47.5t75.5 -17.5zM149 144l304 410q-25 35 -60 56t-83 21q-53 0 -92 -26t-64 -68.5t-37 -95.5t-12 -107q0 -51 10.5 -99.5t33.5 -90.5z" />
    <glyph glyph-name="Ugrave" unicode="&#xd9;" horiz-adv-x="591" 
d="M296 -13q-48 0 -89.5 15.5t-72 45.5t-48 73.5t-17.5 100.5v445h51v-430q0 -91 45 -145.5t130 -54.5t130.5 53t45.5 147v430h52v-445q0 -57 -18 -100.5t-48.5 -73.5t-72 -45.5t-88.5 -15.5zM369 711l-167 127l37 41l155 -139z" />
    <glyph glyph-name="Uacute" unicode="&#xda;" horiz-adv-x="591" 
d="M296 -13q-48 0 -89.5 15.5t-72 45.5t-48 73.5t-17.5 100.5v445h51v-430q0 -91 45 -145.5t130 -54.5t130.5 53t45.5 147v430h52v-445q0 -57 -18 -100.5t-48.5 -73.5t-72 -45.5t-88.5 -15.5zM255 711l-24 29l155 139l37 -41z" />
    <glyph glyph-name="Ucircumflex" unicode="&#xdb;" horiz-adv-x="591" 
d="M296 -13q-48 0 -89.5 15.5t-72 45.5t-48 73.5t-17.5 100.5v445h51v-430q0 -91 45 -145.5t130 -54.5t130.5 53t45.5 147v430h52v-445q0 -57 -18 -100.5t-48.5 -73.5t-72 -45.5t-88.5 -15.5zM203 708l-29 26l113 150h31l113 -150l-29 -26l-100 129z" />
    <glyph glyph-name="Udieresis" unicode="&#xdc;" horiz-adv-x="591" 
d="M296 -13q-48 0 -89.5 15.5t-72 45.5t-48 73.5t-17.5 100.5v445h51v-430q0 -91 45 -145.5t130 -54.5t130.5 53t45.5 147v430h52v-445q0 -57 -18 -100.5t-48.5 -73.5t-72 -45.5t-88.5 -15.5zM213 767q-15 0 -25.5 10t-10.5 30q0 18 10.5 28.5t25.5 10.5t25.5 -10.5
t10.5 -28.5q0 -20 -10.5 -30t-25.5 -10zM384 767q-15 0 -25.5 10t-10.5 30q0 18 10.5 28.5t25.5 10.5t25.5 -10.5t10.5 -28.5q0 -20 -10.5 -30t-25.5 -10z" />
    <glyph glyph-name="Yacute" unicode="&#xdd;" horiz-adv-x="499" 
d="M220 0v321l-215 346h59l183 -295l190 295h57l-222 -345v-322h-52zM234 711l-24 29l155 139l37 -41z" />
    <glyph glyph-name="Thorn" unicode="&#xde;" horiz-adv-x="494" 
d="M72 0v667h52v-141h146q96 0 148 -49.5t52 -146.5q0 -47 -15 -83t-41.5 -60t-63 -36t-79.5 -12h-147v-139h-52zM124 188h147q66 0 104.5 38.5t38.5 104.5q0 69 -35.5 107.5t-108.5 38.5h-146v-289z" />
    <glyph glyph-name="germandbls" unicode="&#xdf;" horiz-adv-x="510" 
d="M338 -11q-39 0 -75 11q-34 12 -46 20.5t-18 16.5l18 44q20 -18 52 -33t69 -15q42 0 66 23t24 63q0 46 -29 70.5t-83 45.5q-49 18 -78.5 45.5t-29.5 79.5q0 28 12.5 50.5t32.5 38t45 22.5t48 6h27q-2 79 -33 119t-83 40q-54 0 -86.5 -34.5t-32.5 -109.5v-492h-49v433h-67
v44h67v21q-1 47 14.5 82t40 58t55 32.5t58.5 9.5q73 0 118 -53t45 -150v-44h-73q-45 0 -67.5 -19.5t-22.5 -52.5q0 -26 11.5 -42t35.5 -27t54 -21q66 -25 92.5 -59.5t26.5 -90.5q0 -61 -38 -96.5t-101 -35.5z" />
    <glyph glyph-name="agrave" unicode="&#xe0;" horiz-adv-x="438" 
d="M196 -11q-32 0 -59.5 9.5t-47.5 28t-31.5 45t-11.5 61.5q0 70 50 112q48 42 114 53q64 10 117 12v19q0 56 -24 85q-27 31 -79 31q-40 0 -72.5 -16t-55.5 -33l-18 40q8 7 23 16t34.5 17.5t42.5 14t48 5.5q76 0 115 -44q35 -41 35 -116v-329h-44l-3 42q-21 -22 -54 -37.5
t-79 -15.5zM203 33q38 0 70 17t54 41v175q-118 -3 -174 -35.5t-56 -98.5q0 -29 11 -48.5t27.5 -31.5t35 -16.5t32.5 -2.5zM296 545l-167 127l37 41l155 -139z" />
    <glyph glyph-name="aacute" unicode="&#xe1;" horiz-adv-x="438" 
d="M196 -11q-32 0 -59.5 9.5t-47.5 28t-31.5 45t-11.5 61.5q0 70 50 112q48 42 114 53q64 10 117 12v19q0 56 -24 85q-27 31 -79 31q-40 0 -72.5 -16t-55.5 -33l-18 40q8 7 23 16t34.5 17.5t42.5 14t48 5.5q76 0 115 -44q35 -41 35 -116v-329h-44l-3 42q-21 -22 -54 -37.5
t-79 -15.5zM203 33q38 0 70 17t54 41v175q-118 -3 -174 -35.5t-56 -98.5q0 -29 11 -48.5t27.5 -31.5t35 -16.5t32.5 -2.5zM168 545l-24 29l155 139l37 -41z" />
    <glyph glyph-name="acircumflex" unicode="&#xe2;" horiz-adv-x="438" 
d="M196 -11q-32 0 -59.5 9.5t-47.5 28t-31.5 45t-11.5 61.5q0 70 50 112q48 42 114 53q64 10 117 12v19q0 56 -24 85q-27 31 -79 31q-40 0 -72.5 -16t-55.5 -33l-18 40q8 7 23 16t34.5 17.5t42.5 14t48 5.5q76 0 115 -44q35 -41 35 -116v-329h-44l-3 42q-21 -22 -54 -37.5
t-79 -15.5zM203 33q38 0 70 17t54 41v175q-118 -3 -174 -35.5t-56 -98.5q0 -29 11 -48.5t27.5 -31.5t35 -16.5t32.5 -2.5zM124 542l-29 26l113 150h31l113 -150l-29 -26l-100 129z" />
    <glyph glyph-name="atilde" unicode="&#xe3;" horiz-adv-x="438" 
d="M196 -11q-32 0 -59.5 9.5t-47.5 28t-31.5 45t-11.5 61.5q0 70 50 112q48 42 114 53q64 10 117 12v19q0 56 -24 85q-27 31 -79 31q-40 0 -72.5 -16t-55.5 -33l-18 40q8 7 23 16t34.5 17.5t42.5 14t48 5.5q76 0 115 -44q35 -41 35 -116v-329h-44l-3 42q-21 -22 -54 -37.5
t-79 -15.5zM203 33q38 0 70 17t54 41v175q-118 -3 -174 -35.5t-56 -98.5q0 -29 11 -48.5t27.5 -31.5t35 -16.5t32.5 -2.5zM290 574q-20 0 -39.5 10t-33.5 21q-41 29 -59 29q-17 0 -31 -13t-27 -44l-35 15q16 43 41 62.5t58 19.5q20 0 38 -9t36 -22q34 -27 56 -27
q15 0 29.5 14t28.5 49l35 -17q-18 -48 -44 -68t-53 -20z" />
    <glyph glyph-name="adieresis" unicode="&#xe4;" horiz-adv-x="438" 
d="M196 -11q-32 0 -59.5 9.5t-47.5 28t-31.5 45t-11.5 61.5q0 70 50 112q48 42 114 53q64 10 117 12v19q0 56 -24 85q-27 31 -79 31q-40 0 -72.5 -16t-55.5 -33l-18 40q8 7 23 16t34.5 17.5t42.5 14t48 5.5q76 0 115 -44q35 -41 35 -116v-329h-44l-3 42q-21 -22 -54 -37.5
t-79 -15.5zM203 33q38 0 70 17t54 41v175q-118 -3 -174 -35.5t-56 -98.5q0 -29 11 -48.5t27.5 -31.5t35 -16.5t32.5 -2.5zM137 601q-15 0 -25.5 10t-10.5 30q0 18 10.5 28.5t25.5 10.5t25.5 -10.5t10.5 -28.5q0 -20 -10.5 -30t-25.5 -10zM308 601q-15 0 -25.5 10t-10.5 30
q0 18 10.5 28.5t25.5 10.5t25.5 -10.5t10.5 -28.5q0 -20 -10.5 -30t-25.5 -10z" />
    <glyph glyph-name="aring" unicode="&#xe5;" horiz-adv-x="438" 
d="M196 -11q-32 0 -59.5 9.5t-47.5 28t-31.5 45t-11.5 61.5q0 70 50 112q48 42 114 53q64 10 117 12v19q0 56 -24 85q-27 31 -79 31q-40 0 -72.5 -16t-55.5 -33l-18 40q8 7 23 16t34.5 17.5t42.5 14t48 5.5q76 0 115 -44q35 -41 35 -116v-329h-44l-3 42q-21 -22 -54 -37.5
t-79 -15.5zM203 33q38 0 70 17t54 41v175q-118 -3 -174 -35.5t-56 -98.5q0 -29 11 -48.5t27.5 -31.5t35 -16.5t32.5 -2.5zM231 534q-26 0 -44 10t-29.5 25.5t-15.5 32.5t-4 31q0 34 16 58t37.5 33t39.5 9q33 0 57 -21t30.5 -44t6.5 -35q0 -33 -17.5 -60t-38.5 -33t-38 -6z
M232 569q25 0 39.5 17.5t14.5 46.5q0 27 -16 46t-38 19t-38 -19t-16 -46q0 -29 15 -46.5t39 -17.5z" />
    <glyph glyph-name="ae" unicode="&#xe6;" horiz-adv-x="756" 
d="M202 -11q-75 0 -115.5 39.5t-40.5 108.5q0 49 27.5 82.5t59 47t52.5 18.5q57 11 122 11h32v33q0 26 -8 49.5t-19 34.5q-30 32 -85 32q-40 0 -74.5 -16t-56.5 -33l-18 41q23 15 41 25q20 10 52 18.5t58 8.5q64 0 96.5 -24t49.5 -66q25 45 64.5 67.5t90.5 22.5
q93 0 140.5 -64t49.5 -173h-334q0 -102 39.5 -160.5t118.5 -58.5q85 0 143 61l15 -45q-14 -14 -35 -27q-19 -12 -54.5 -22.5t-69.5 -10.5q-125 0 -174 98q-19 -50 -65 -74t-102 -24zM208 33q45 0 76.5 20t43.5 47t12 47v105h-31q-67 0 -110 -8q-20 -4 -38 -12.5t-32 -21.5
t-22.5 -31.5t-8.5 -42.5q0 -54 31 -78.5t79 -24.5zM390 296h274q-3 57 -30.5 93.5t-57.5 46.5t-46 9q-53 0 -92.5 -37t-47.5 -112z" />
    <glyph glyph-name="ccedilla" unicode="&#xe7;" horiz-adv-x="438" 
d="M251 -11q-44 0 -81.5 16.5t-64.5 48.5t-42.5 78t-15.5 106q0 91 33 150q23 40 53.5 63.5t60 30.5t50.5 7q45 0 84 -21.5t57.5 -45t21.5 -33.5l-36 -25q-26 40 -54 60.5t-73 20.5q-48 0 -85 -34t-49.5 -80.5t-12.5 -91.5q0 -47 11 -84.5t32 -64.5t50 -41.5t65 -14.5
q37 0 67 13t62 46l19 -43q-21 -22 -59 -41.5t-93 -19.5zM177 -174l48 130h59l-68 -130h-39z" />
    <glyph glyph-name="egrave" unicode="&#xe8;" horiz-adv-x="468" 
d="M255 -11q-100 0 -154 66t-54 184q0 85 30 144q31 60 76.5 83t88.5 23q73 0 118.5 -46.5t58.5 -101.5t13 -94h-336q0 -102 40.5 -158t119.5 -56q85 0 143 61l15 -45q-14 -14 -35 -27q-19 -12 -54.5 -22.5t-69.5 -10.5zM100 291h276q-3 55 -27 92q-24 38 -53.5 50t-53.5 12
q-54 0 -92.5 -36.5t-49.5 -117.5zM317 545l-167 127l37 41l155 -139z" />
    <glyph glyph-name="eacute" unicode="&#xe9;" horiz-adv-x="468" 
d="M255 -11q-100 0 -154 66t-54 184q0 85 30 144q31 60 76.5 83t88.5 23q73 0 118.5 -46.5t58.5 -101.5t13 -94h-336q0 -102 40.5 -158t119.5 -56q85 0 143 61l15 -45q-14 -14 -35 -27q-19 -12 -54.5 -22.5t-69.5 -10.5zM100 291h276q-3 55 -27 92q-24 38 -53.5 50t-53.5 12
q-54 0 -92.5 -36.5t-49.5 -117.5zM187 545l-24 29l155 139l37 -41z" />
    <glyph glyph-name="ecircumflex" unicode="&#xea;" horiz-adv-x="468" 
d="M255 -11q-100 0 -154 66t-54 184q0 85 30 144q31 60 76.5 83t88.5 23q73 0 118.5 -46.5t58.5 -101.5t13 -94h-336q0 -102 40.5 -158t119.5 -56q85 0 143 61l15 -45q-14 -14 -35 -27q-19 -12 -54.5 -22.5t-69.5 -10.5zM100 291h276q-3 55 -27 92q-24 38 -53.5 50t-53.5 12
q-54 0 -92.5 -36.5t-49.5 -117.5zM147 542l-29 26l113 150h31l113 -150l-29 -26l-100 129z" />
    <glyph glyph-name="edieresis" unicode="&#xeb;" horiz-adv-x="468" 
d="M255 -11q-100 0 -154 66t-54 184q0 85 30 144q31 60 76.5 83t88.5 23q73 0 118.5 -46.5t58.5 -101.5t13 -94h-336q0 -102 40.5 -158t119.5 -56q85 0 143 61l15 -45q-14 -14 -35 -27q-19 -12 -54.5 -22.5t-69.5 -10.5zM100 291h276q-3 55 -27 92q-24 38 -53.5 50t-53.5 12
q-54 0 -92.5 -36.5t-49.5 -117.5zM159 601q-15 0 -25.5 10t-10.5 30q0 18 10.5 28.5t25.5 10.5t25.5 -10.5t10.5 -28.5q0 -20 -10.5 -30t-25.5 -10zM330 601q-15 0 -25.5 10t-10.5 30q0 18 10.5 28.5t25.5 10.5t25.5 -10.5t10.5 -28.5q0 -20 -10.5 -30t-25.5 -10z" />
    <glyph glyph-name="igrave" unicode="&#xec;" horiz-adv-x="187" 
d="M74 0v477h49v-477h-49zM173 545l-167 127l37 41l155 -139z" />
    <glyph glyph-name="iacute" unicode="&#xed;" horiz-adv-x="187" 
d="M74 0v477h49v-477h-49zM39 545l-24 29l155 139l37 -41z" />
    <glyph glyph-name="icircumflex" unicode="&#xee;" horiz-adv-x="187" 
d="M74 0v477h49v-477h-49zM5 542l-29 26l113 150h31l113 -150l-29 -26l-100 129z" />
    <glyph glyph-name="idieresis" unicode="&#xef;" horiz-adv-x="187" 
d="M74 0v477h49v-477h-49zM18 601q-15 0 -25.5 10t-10.5 30q0 18 10.5 28.5t25.5 10.5t25.5 -10.5t10.5 -28.5q0 -20 -10.5 -30t-25.5 -10zM189 601q-15 0 -25.5 10t-10.5 30q0 18 10.5 28.5t25.5 10.5t25.5 -10.5t10.5 -28.5q0 -20 -10.5 -30t-25.5 -10z" />
    <glyph glyph-name="eth" unicode="&#xf0;" horiz-adv-x="494" 
d="M143 562l107 49q-39 30 -75 44l42 22q20 -10 38.5 -21t35.5 -25l108 49l26 -28l-104 -47q50 -48 77 -106q49 -102 49 -234q0 -69 -14.5 -121t-41 -86.5t-63 -51.5t-80.5 -17q-42 0 -78.5 15.5t-64 47t-43 78.5t-15.5 110q0 58 14.5 104.5t41 78.5t63 49t80.5 17
q41 0 70 -8.5t52 -24.5q-14 35 -36 68t-52 61l-111 -51zM399 263q0 46 -5.5 82.5t-12.5 56.5q-25 20 -56.5 31.5t-76.5 11.5q-67 0 -110 -53.5t-43 -151.5q0 -51 12 -89.5t33 -64.5t48.5 -39.5t59.5 -13.5q31 0 59 13t48 41t32 71.5t12 104.5z" />
    <glyph glyph-name="ntilde" unicode="&#xf1;" horiz-adv-x="495" 
d="M69 0v477h44l5 -45q26 23 62.5 40t84.5 17q67 0 106 -26.5t53 -78.5q10 -31 10 -86v-298h-50v295q0 49 -9 75q-26 74 -116 74q-43 0 -78 -18t-63 -44v-382h-49zM321 574q-20 0 -39.5 10t-33.5 21q-41 29 -59 29q-17 0 -31 -13t-27 -44l-35 15q16 43 41 62.5t58 19.5
q20 0 38 -9t36 -22q34 -27 56 -27q15 0 29.5 14t28.5 49l35 -17q-18 -48 -44 -68t-53 -20z" />
    <glyph glyph-name="ograve" unicode="&#xf2;" horiz-adv-x="497" 
d="M248 -11q-72 0 -121 45q-44 39 -62 94.5t-18 113.5q0 75 29 135t75 86t97 26q70 0 120 -44q33 -30 52 -68t25 -75.5t6 -61.5q0 -79 -31 -140q-30 -60 -75.5 -85.5t-96.5 -25.5zM248 33q43 -2 72.5 20.5t48 55t26 69t6.5 63.5q1 66 -25 116t-62.5 70t-65.5 18
q-55 0 -93 -38q-35 -35 -47 -78t-12 -87q0 -39 9.5 -76.5t28.5 -67t47.5 -47.5t66.5 -18zM332 545l-167 127l37 41l155 -139z" />
    <glyph glyph-name="oacute" unicode="&#xf3;" horiz-adv-x="497" 
d="M248 -11q-72 0 -121 45q-44 39 -62 94.5t-18 113.5q0 75 29 135t75 86t97 26q70 0 120 -44q33 -30 52 -68t25 -75.5t6 -61.5q0 -79 -31 -140q-30 -60 -75.5 -85.5t-96.5 -25.5zM248 33q43 -2 72.5 20.5t48 55t26 69t6.5 63.5q1 66 -25 116t-62.5 70t-65.5 18
q-55 0 -93 -38q-35 -35 -47 -78t-12 -87q0 -39 9.5 -76.5t28.5 -67t47.5 -47.5t66.5 -18zM188 545l-24 29l155 139l37 -41z" />
    <glyph glyph-name="ocircumflex" unicode="&#xf4;" horiz-adv-x="497" 
d="M248 -11q-72 0 -121 45q-44 39 -62 94.5t-18 113.5q0 75 29 135t75 86t97 26q70 0 120 -44q33 -30 52 -68t25 -75.5t6 -61.5q0 -79 -31 -140q-30 -60 -75.5 -85.5t-96.5 -25.5zM248 33q43 -2 72.5 20.5t48 55t26 69t6.5 63.5q1 66 -25 116t-62.5 70t-65.5 18
q-55 0 -93 -38q-35 -35 -47 -78t-12 -87q0 -39 9.5 -76.5t28.5 -67t47.5 -47.5t66.5 -18zM154 542l-29 26l113 150h31l113 -150l-29 -26l-100 129z" />
    <glyph glyph-name="otilde" unicode="&#xf5;" horiz-adv-x="497" 
d="M248 -11q-72 0 -121 45q-44 39 -62 94.5t-18 113.5q0 75 29 135t75 86t97 26q70 0 120 -44q33 -30 52 -68t25 -75.5t6 -61.5q0 -79 -31 -140q-30 -60 -75.5 -85.5t-96.5 -25.5zM248 33q43 -2 72.5 20.5t48 55t26 69t6.5 63.5q1 66 -25 116t-62.5 70t-65.5 18
q-55 0 -93 -38q-35 -35 -47 -78t-12 -87q0 -39 9.5 -76.5t28.5 -67t47.5 -47.5t66.5 -18zM321 575q-20 0 -39.5 10t-33.5 21q-41 29 -59 29q-17 0 -31 -13t-27 -44l-35 15q16 43 41 62.5t58 19.5q20 0 38 -9t36 -22q34 -27 56 -27q15 0 29.5 14t28.5 49l35 -17
q-18 -48 -44 -68t-53 -20z" />
    <glyph glyph-name="odieresis" unicode="&#xf6;" horiz-adv-x="497" 
d="M248 -11q-72 0 -121 45q-44 39 -62 94.5t-18 113.5q0 75 29 135t75 86t97 26q70 0 120 -44q33 -30 52 -68t25 -75.5t6 -61.5q0 -79 -31 -140q-30 -60 -75.5 -85.5t-96.5 -25.5zM248 33q43 -2 72.5 20.5t48 55t26 69t6.5 63.5q1 66 -25 116t-62.5 70t-65.5 18
q-55 0 -93 -38q-35 -35 -47 -78t-12 -87q0 -39 9.5 -76.5t28.5 -67t47.5 -47.5t66.5 -18zM168 601q-15 0 -25.5 10t-10.5 30q0 18 10.5 28.5t25.5 10.5t25.5 -10.5t10.5 -28.5q0 -20 -10.5 -30t-25.5 -10zM339 601q-15 0 -25.5 10t-10.5 30q0 18 10.5 28.5t25.5 10.5
t25.5 -10.5t10.5 -28.5q0 -20 -10.5 -30t-25.5 -10z" />
    <glyph glyph-name="divide" unicode="&#xf7;" 
d="M251 108q-18 0 -31 13.5t-13 34.5q0 20 13 33.5t31 13.5t31 -13.5t13 -33.5q0 -21 -13 -34.5t-31 -13.5zM36 308v48h428v-48h-428zM251 459q-18 0 -31 14t-13 34q0 21 13 34.5t31 13.5t31 -13.5t13 -34.5q0 -20 -13 -34t-31 -14z" />
    <glyph glyph-name="oslash" unicode="&#xf8;" horiz-adv-x="497" 
d="M83 -21l-31 26l46 62q-26 35 -38.5 81t-12.5 94q0 75 29 135t75 86t97 26q39 0 71 -13t57 -36l41 56l34 -26l-48 -63q23 -35 35.5 -77t12.5 -90q0 -79 -31 -140q-30 -60 -75.5 -85.5t-96.5 -25.5q-38 0 -68.5 12.5t-53.5 34.5zM248 33q43 -2 72.5 20.5t48 55t26 69
t6.5 63.5q0 33 -7 65t-21 60l-219 -292q18 -19 41.5 -30t52.5 -11zM129 108l218 294q-19 20 -43 31.5t-56 11.5q-55 0 -93 -38q-35 -35 -47 -78t-12 -87q0 -36 8 -70.5t25 -63.5z" />
    <glyph glyph-name="ugrave" unicode="&#xf9;" horiz-adv-x="494" 
d="M229 -12q-126 0 -158 104q-10 31 -10 85v300h49v-298q0 -50 10 -74q14 -38 42.5 -55t73.5 -17q43 0 77 18t63 44v382h49v-477h-45l-4 45q-26 -23 -61 -40t-86 -17zM323 545l-167 127l37 41l155 -139z" />
    <glyph glyph-name="uacute" unicode="&#xfa;" horiz-adv-x="494" 
d="M229 -12q-126 0 -158 104q-10 31 -10 85v300h49v-298q0 -50 10 -74q14 -38 42.5 -55t73.5 -17q43 0 77 18t63 44v382h49v-477h-45l-4 45q-26 -23 -61 -40t-86 -17zM198 545l-24 29l155 139l37 -41z" />
    <glyph glyph-name="ucircumflex" unicode="&#xfb;" horiz-adv-x="494" 
d="M229 -12q-126 0 -158 104q-10 31 -10 85v300h49v-298q0 -50 10 -74q14 -38 42.5 -55t73.5 -17q43 0 77 18t63 44v382h49v-477h-45l-4 45q-26 -23 -61 -40t-86 -17zM150 542l-29 26l113 150h31l113 -150l-29 -26l-100 129z" />
    <glyph glyph-name="udieresis" unicode="&#xfc;" horiz-adv-x="494" 
d="M229 -12q-126 0 -158 104q-10 31 -10 85v300h49v-298q0 -50 10 -74q14 -38 42.5 -55t73.5 -17q43 0 77 18t63 44v382h49v-477h-45l-4 45q-26 -23 -61 -40t-86 -17zM166 601q-15 0 -25.5 10t-10.5 30q0 18 10.5 28.5t25.5 10.5t25.5 -10.5t10.5 -28.5q0 -20 -10.5 -30
t-25.5 -10zM337 601q-15 0 -25.5 10t-10.5 30q0 18 10.5 28.5t25.5 10.5t25.5 -10.5t10.5 -28.5q0 -20 -10.5 -30t-25.5 -10z" />
    <glyph glyph-name="yacute" unicode="&#xfd;" horiz-adv-x="448" 
d="M-4 -192l-6 44h8q63 0 116 36t81 99l-186 490h55l163 -438q42 88 84 201.5t75 236.5h52q-35 -138 -76.5 -243t-79.5 -187q-43 -90 -78 -137.5t-90 -74.5t-118 -27zM185 545l-24 29l155 139l37 -41z" />
    <glyph glyph-name="thorn" unicode="&#xfe;" horiz-adv-x="490" 
d="M71 -193v860h49v-225q21 19 51 33t81 14q66 0 113 -39q34 -29 50 -66t22 -75.5t6 -65.5q0 -86 -34 -147q-26 -46 -59 -68.5t-66 -30.5t-57 -8q-35 0 -61.5 8t-45.5 19v-209h-49zM222 33q78 0 124.5 55t46.5 152q1 75 -24.5 123t-61.5 65.5t-65 16.5q-38 0 -69.5 -15.5
t-52.5 -37.5v-325q18 -14 43.5 -24t58.5 -10z" />
    <glyph glyph-name="ydieresis" unicode="&#xff;" horiz-adv-x="448" 
d="M-4 -192l-6 44h8q63 0 116 36t81 99l-186 490h55l163 -438q42 88 84 201.5t75 236.5h52q-35 -138 -76.5 -243t-79.5 -187q-43 -90 -78 -137.5t-90 -74.5t-118 -27zM144 601q-15 0 -25.5 10t-10.5 30q0 18 10.5 28.5t25.5 10.5t25.5 -10.5t10.5 -28.5q0 -20 -10.5 -30
t-25.5 -10zM315 601q-15 0 -25.5 10t-10.5 30q0 18 10.5 28.5t25.5 10.5t25.5 -10.5t10.5 -28.5q0 -20 -10.5 -30t-25.5 -10z" />
    <glyph glyph-name="Amacron" unicode="&#x100;" horiz-adv-x="533" 
d="M5 0l236 667h48l239 -667h-55l-78 214h-260l-76 -214h-54zM151 264h228l-115 340zM127 773v46h279v-46h-279z" />
    <glyph glyph-name="amacron" unicode="&#x101;" horiz-adv-x="438" 
d="M196 -11q-32 0 -59.5 9.5t-47.5 28t-31.5 45t-11.5 61.5q0 70 50 112q48 42 114 53q64 10 117 12v19q0 56 -24 85q-27 31 -79 31q-40 0 -72.5 -16t-55.5 -33l-18 40q8 7 23 16t34.5 17.5t42.5 14t48 5.5q76 0 115 -44q35 -41 35 -116v-329h-44l-3 42q-21 -22 -54 -37.5
t-79 -15.5zM203 33q38 0 70 17t54 41v175q-118 -3 -174 -35.5t-56 -98.5q0 -29 11 -48.5t27.5 -31.5t35 -16.5t32.5 -2.5zM87 607v46h279v-46h-279z" />
    <glyph glyph-name="Abreve" unicode="&#x102;" horiz-adv-x="533" 
d="M5 0l236 667h48l239 -667h-55l-78 214h-260l-76 -214h-54zM151 264h228l-115 340zM265 718q-26 0 -51.5 8t-46.5 25.5t-34.5 45t-14.5 66.5h40q4 -31 15 -51t26 -31.5t32 -16t34 -4.5q43 0 71 26t35 77h40q0 -43 -16.5 -71.5t-39 -45.5t-47.5 -22.5t-43 -5.5z" />
    <glyph glyph-name="abreve" unicode="&#x103;" horiz-adv-x="438" 
d="M196 -11q-32 0 -59.5 9.5t-47.5 28t-31.5 45t-11.5 61.5q0 70 50 112q48 42 114 53q64 10 117 12v19q0 56 -24 85q-27 31 -79 31q-40 0 -72.5 -16t-55.5 -33l-18 40q8 7 23 16t34.5 17.5t42.5 14t48 5.5q76 0 115 -44q35 -41 35 -116v-329h-44l-3 42q-21 -22 -54 -37.5
t-79 -15.5zM203 33q38 0 70 17t54 41v175q-118 -3 -174 -35.5t-56 -98.5q0 -29 11 -48.5t27.5 -31.5t35 -16.5t32.5 -2.5zM230 552q-26 0 -51.5 8t-46.5 25.5t-34.5 45t-14.5 66.5h40q4 -31 15 -51t26 -31.5t32 -16t34 -4.5q43 0 71 26t35 77h40q0 -43 -16.5 -71.5
t-39 -45.5t-47.5 -22.5t-43 -5.5z" />
    <glyph glyph-name="Aogonek" unicode="&#x104;" horiz-adv-x="533" 
d="M5 0l236 667h48l239 -667h-55l-78 214h-260l-76 -214h-54zM151 264h228l-115 340zM466 -191q-34 0 -55 19t-21 57q0 26 10.5 49t25.5 41t30 30.5t23 18.5l44 -10q-39 -29 -60.5 -57.5t-21.5 -60.5q0 -42 34 -42q17 0 40 14l12 -42q-28 -17 -61 -17z" />
    <glyph glyph-name="aogonek" unicode="&#x105;" horiz-adv-x="438" 
d="M196 -11q-32 0 -59.5 9.5t-47.5 28t-31.5 45t-11.5 61.5q0 70 50 112q48 42 114 53q64 10 117 12v19q0 56 -24 85q-27 31 -79 31q-40 0 -72.5 -16t-55.5 -33l-18 40q8 7 23 16t34.5 17.5t42.5 14t48 5.5q76 0 115 -44q35 -41 35 -116v-329h-44l-3 42q-21 -22 -54 -37.5
t-79 -15.5zM203 33q38 0 70 17t54 41v175q-118 -3 -174 -35.5t-56 -98.5q0 -29 11 -48.5t27.5 -31.5t35 -16.5t32.5 -2.5zM319 -193q-34 0 -55 19t-21 57q0 26 10.5 49t25.5 41t30 30.5t23 18.5l44 -10q-39 -29 -60.5 -57.5t-21.5 -60.5q0 -42 34 -42q17 0 40 14l12 -42
q-28 -17 -61 -17z" />
    <glyph glyph-name="Cacute" unicode="&#x106;" horiz-adv-x="542" 
d="M312 -12q-62 0 -110.5 24t-82.5 68.5t-51.5 108t-17.5 142.5q0 85 19.5 150t54 109t81.5 67t102 23q70 0 122 -35.5t79 -92.5l-42 -33q-21 42 -53.5 68t-59.5 35t-45 8q-68 0 -118 -44q-47 -43 -67 -114q-18 -69 -18 -142q0 -65 14.5 -119t42 -92.5t66.5 -60t88 -21.5
q54 0 94 23t72 65l30 -42q-18 -23 -50 -48q-27 -21 -68.5 -34t-81.5 -13zM236 709l-24 29l155 139l37 -41z" />
    <glyph glyph-name="cacute" unicode="&#x107;" horiz-adv-x="438" 
d="M251 -11q-44 0 -81.5 16.5t-64.5 48.5t-42.5 78t-15.5 106q0 91 33 150q23 40 53.5 63.5t60 30.5t50.5 7q45 0 84 -21.5t57.5 -45t21.5 -33.5l-36 -25q-26 40 -54 60.5t-73 20.5q-48 0 -85 -34t-49.5 -80.5t-12.5 -91.5q0 -47 11 -84.5t32 -64.5t50 -41.5t65 -14.5
q37 0 67 13t62 46l19 -43q-21 -22 -59 -41.5t-93 -19.5zM177 533l-24 29l155 139l37 -41z" />
    <glyph glyph-name="Ccircumflex" unicode="&#x108;" horiz-adv-x="542" 
d="M312 -12q-62 0 -110.5 24t-82.5 68.5t-51.5 108t-17.5 142.5q0 85 19.5 150t54 109t81.5 67t102 23q70 0 122 -35.5t79 -92.5l-42 -33q-21 42 -53.5 68t-59.5 35t-45 8q-68 0 -118 -44q-47 -43 -67 -114q-18 -69 -18 -142q0 -65 14.5 -119t42 -92.5t66.5 -60t88 -21.5
q54 0 94 23t72 65l30 -42q-18 -23 -50 -48q-27 -21 -68.5 -34t-81.5 -13zM204 708l-29 26l113 150h31l113 -150l-29 -26l-100 129z" />
    <glyph glyph-name="ccircumflex" unicode="&#x109;" horiz-adv-x="438" 
d="M251 -11q-44 0 -81.5 16.5t-64.5 48.5t-42.5 78t-15.5 106q0 91 33 150q23 40 53.5 63.5t60 30.5t50.5 7q45 0 84 -21.5t57.5 -45t21.5 -33.5l-36 -25q-26 40 -54 60.5t-73 20.5q-48 0 -85 -34t-49.5 -80.5t-12.5 -91.5q0 -47 11 -84.5t32 -64.5t50 -41.5t65 -14.5
q37 0 67 13t62 46l19 -43q-21 -22 -59 -41.5t-93 -19.5zM147 542l-29 26l113 150h31l113 -150l-29 -26l-100 129z" />
    <glyph glyph-name="Cdotaccent" unicode="&#x10a;" horiz-adv-x="542" 
d="M312 -12q-62 0 -110.5 24t-82.5 68.5t-51.5 108t-17.5 142.5q0 85 19.5 150t54 109t81.5 67t102 23q70 0 122 -35.5t79 -92.5l-42 -33q-21 42 -53.5 68t-59.5 35t-45 8q-68 0 -118 -44q-47 -43 -67 -114q-18 -69 -18 -142q0 -65 14.5 -119t42 -92.5t66.5 -60t88 -21.5
q54 0 94 23t72 65l30 -42q-18 -23 -50 -48q-27 -21 -68.5 -34t-81.5 -13zM314 767q-15 0 -25.5 10t-10.5 30q0 18 10.5 28.5t25.5 10.5t25.5 -10.5t10.5 -28.5q0 -20 -10.5 -30t-25.5 -10z" />
    <glyph glyph-name="cdotaccent" unicode="&#x10b;" horiz-adv-x="438" 
d="M251 -11q-44 0 -81.5 16.5t-64.5 48.5t-42.5 78t-15.5 106q0 91 33 150q23 40 53.5 63.5t60 30.5t50.5 7q45 0 84 -21.5t57.5 -45t21.5 -33.5l-36 -25q-26 40 -54 60.5t-73 20.5q-48 0 -85 -34t-49.5 -80.5t-12.5 -91.5q0 -47 11 -84.5t32 -64.5t50 -41.5t65 -14.5
q37 0 67 13t62 46l19 -43q-21 -22 -59 -41.5t-93 -19.5zM250 601q-15 0 -25.5 10t-10.5 30q0 18 10.5 28.5t25.5 10.5t25.5 -10.5t10.5 -28.5q0 -20 -10.5 -30t-25.5 -10z" />
    <glyph glyph-name="Ccaron" unicode="&#x10c;" horiz-adv-x="542" 
d="M312 -12q-62 0 -110.5 24t-82.5 68.5t-51.5 108t-17.5 142.5q0 85 19.5 150t54 109t81.5 67t102 23q70 0 122 -35.5t79 -92.5l-42 -33q-21 42 -53.5 68t-59.5 35t-45 8q-68 0 -118 -44q-47 -43 -67 -114q-18 -69 -18 -142q0 -65 14.5 -119t42 -92.5t66.5 -60t88 -21.5
q54 0 94 23t72 65l30 -42q-18 -23 -50 -48q-27 -21 -68.5 -34t-81.5 -13zM290 708l-113 150l29 26l99 -129l100 129l29 -26l-113 -150h-31z" />
    <glyph glyph-name="ccaron" unicode="&#x10d;" horiz-adv-x="438" 
d="M251 -11q-44 0 -81.5 16.5t-64.5 48.5t-42.5 78t-15.5 106q0 91 33 150q23 40 53.5 63.5t60 30.5t50.5 7q45 0 84 -21.5t57.5 -45t21.5 -33.5l-36 -25q-26 40 -54 60.5t-73 20.5q-48 0 -85 -34t-49.5 -80.5t-12.5 -91.5q0 -47 11 -84.5t32 -64.5t50 -41.5t65 -14.5
q37 0 67 13t62 46l19 -43q-21 -22 -59 -41.5t-93 -19.5zM235 542l-113 150l29 26l99 -129l100 129l29 -26l-113 -150h-31z" />
    <glyph glyph-name="Dcaron" unicode="&#x10e;" horiz-adv-x="583" 
d="M76 0v667h156q42 0 80 -7t70 -24q72 -35 112.5 -108t40.5 -185q0 -138 -52.5 -220t-145.5 -109q-48 -14 -105 -14h-156zM128 49h95q26 0 53.5 3t50.5 12q70 26 112 93t42 182q0 88 -27 147.5t-78 92.5q-30 20 -68.5 29.5t-80.5 9.5h-99v-569zM233 708l-113 150l29 26
l99 -129l100 129l29 -26l-113 -150h-31z" />
    <glyph glyph-name="dcaron" unicode="&#x10f;" horiz-adv-x="507" 
d="M244 -11q-53 0 -91 23t-62 60t-34 81.5t-10 86.5q0 54 15 99.5t42 79t65.5 52t86.5 18.5q42 0 70 -10t45 -21v209h50v-667h-45l-5 42q-22 -23 -53.5 -38t-73.5 -15zM250 33q38 0 69 18t52 44v312q-16 14 -45.5 26t-63.5 12q-44 0 -76 -20t-52.5 -50.5t-29 -67.5t-8.5 -69
q0 -93 40 -149t114 -56zM483 497v170h64l-24 -170h-40z" />
    <glyph glyph-name="Dcroat" unicode="&#x110;" horiz-adv-x="583" 
d="M76 0v325h-74v43h74v299h156q42 0 80 -7t70 -24q72 -35 112.5 -108t40.5 -185q0 -138 -52.5 -220t-145.5 -109q-48 -14 -105 -14h-156zM128 49h95q26 0 53.5 3t50.5 12q70 26 112 93t42 182q0 88 -27 147.5t-78 92.5q-30 20 -68.5 29.5t-80.5 9.5h-99v-250h183v-43h-183
v-276z" />
    <glyph glyph-name="dcroat" unicode="&#x111;" horiz-adv-x="490" 
d="M244 -11q-53 0 -91 23t-62 60t-34 81.5t-10 86.5q0 54 15 99.5t42 79t65.5 52t86.5 18.5q42 0 70 -10t45 -21v99h-163v43h163v67h50v-67h96v-43h-96v-557h-45l-5 42q-22 -23 -53.5 -38t-73.5 -15zM250 33q38 0 69 18t52 44v312q-16 14 -45.5 26t-63.5 12q-44 0 -76 -20
t-52.5 -50.5t-29 -67.5t-8.5 -69q0 -93 40 -149t114 -56z" />
    <glyph glyph-name="Emacron" unicode="&#x112;" horiz-adv-x="476" 
d="M76 0v667h356v-49h-305v-240h221v-49h-221v-280h323v-49h-374zM117 773v46h279v-46h-279z" />
    <glyph glyph-name="emacron" unicode="&#x113;" horiz-adv-x="468" 
d="M255 -11q-100 0 -154 66t-54 184q0 85 30 144q31 60 76.5 83t88.5 23q73 0 118.5 -46.5t58.5 -101.5t13 -94h-336q0 -102 40.5 -158t119.5 -56q85 0 143 61l15 -45q-14 -14 -35 -27q-19 -12 -54.5 -22.5t-69.5 -10.5zM100 291h276q-3 55 -27 92q-24 38 -53.5 50t-53.5 12
q-54 0 -92.5 -36.5t-49.5 -117.5zM109 607v46h279v-46h-279z" />
    <glyph glyph-name="Ebreve" unicode="&#x114;" horiz-adv-x="476" 
d="M76 0v667h356v-49h-305v-240h221v-49h-221v-280h323v-49h-374zM261 718q-26 0 -51.5 8t-46.5 25.5t-34.5 45t-14.5 66.5h40q4 -31 15 -51t26 -31.5t32 -16t34 -4.5q43 0 71 26t35 77h40q0 -43 -16.5 -71.5t-39 -45.5t-47.5 -22.5t-43 -5.5z" />
    <glyph glyph-name="ebreve" unicode="&#x115;" horiz-adv-x="468" 
d="M255 -11q-100 0 -154 66t-54 184q0 85 30 144q31 60 76.5 83t88.5 23q73 0 118.5 -46.5t58.5 -101.5t13 -94h-336q0 -102 40.5 -158t119.5 -56q85 0 143 61l15 -45q-14 -14 -35 -27q-19 -12 -54.5 -22.5t-69.5 -10.5zM100 291h276q-3 55 -27 92q-24 38 -53.5 50t-53.5 12
q-54 0 -92.5 -36.5t-49.5 -117.5zM248 552q-26 0 -51.5 8t-46.5 25.5t-34.5 45t-14.5 66.5h40q4 -31 15 -51t26 -31.5t32 -16t34 -4.5q43 0 71 26t35 77h40q0 -43 -16.5 -71.5t-39 -45.5t-47.5 -22.5t-43 -5.5z" />
    <glyph glyph-name="Edotaccent" unicode="&#x116;" horiz-adv-x="476" 
d="M76 0v667h356v-49h-305v-240h221v-49h-221v-280h323v-49h-374zM262 767q-15 0 -25.5 10t-10.5 30q0 18 10.5 28.5t25.5 10.5t25.5 -10.5t10.5 -28.5q0 -20 -10.5 -30t-25.5 -10z" />
    <glyph glyph-name="edotaccent" unicode="&#x117;" horiz-adv-x="468" 
d="M255 -11q-100 0 -154 66t-54 184q0 85 30 144q31 60 76.5 83t88.5 23q73 0 118.5 -46.5t58.5 -101.5t13 -94h-336q0 -102 40.5 -158t119.5 -56q85 0 143 61l15 -45q-14 -14 -35 -27q-19 -12 -54.5 -22.5t-69.5 -10.5zM100 291h276q-3 55 -27 92q-24 38 -53.5 50t-53.5 12
q-54 0 -92.5 -36.5t-49.5 -117.5zM246 601q-15 0 -25.5 10t-10.5 30q0 18 10.5 28.5t25.5 10.5t25.5 -10.5t10.5 -28.5q0 -20 -10.5 -30t-25.5 -10z" />
    <glyph glyph-name="Eogonek" unicode="&#x118;" horiz-adv-x="476" 
d="M76 0v667h356v-49h-305v-240h221v-49h-221v-280h323v-49h-374zM381 -193q-34 0 -55 19t-21 57q0 26 10.5 49t25.5 41t30 30.5t23 18.5l44 -10q-39 -29 -60.5 -57.5t-21.5 -60.5q0 -42 34 -42q17 0 40 14l12 -42q-28 -17 -61 -17z" />
    <glyph glyph-name="eogonek" unicode="&#x119;" horiz-adv-x="468" 
d="M255 -11q-100 0 -154 66t-54 184q0 85 30 144q31 60 76.5 83t88.5 23q73 0 118.5 -46.5t58.5 -101.5t13 -94h-336q0 -102 40.5 -158t119.5 -56q85 0 143 61l15 -45q-14 -14 -35 -27q-19 -12 -54.5 -22.5t-69.5 -10.5zM100 291h276q-3 55 -27 92q-24 38 -53.5 50t-53.5 12
q-54 0 -92.5 -36.5t-49.5 -117.5zM302 -193q-34 0 -55 19t-21 57q0 26 10.5 49t25.5 41t30 30.5t23 18.5l44 -10q-39 -29 -60.5 -57.5t-21.5 -60.5q0 -42 34 -42q17 0 40 14l12 -42q-28 -17 -61 -17z" />
    <glyph glyph-name="Ecaron" unicode="&#x11a;" horiz-adv-x="476" 
d="M76 0v667h356v-49h-305v-240h221v-49h-221v-280h323v-49h-374zM246 708l-113 150l29 26l99 -129l100 129l29 -26l-113 -150h-31z" />
    <glyph glyph-name="ecaron" unicode="&#x11b;" horiz-adv-x="468" 
d="M255 -11q-100 0 -154 66t-54 184q0 85 30 144q31 60 76.5 83t88.5 23q73 0 118.5 -46.5t58.5 -101.5t13 -94h-336q0 -102 40.5 -158t119.5 -56q85 0 143 61l15 -45q-14 -14 -35 -27q-19 -12 -54.5 -22.5t-69.5 -10.5zM100 291h276q-3 55 -27 92q-24 38 -53.5 50t-53.5 12
q-54 0 -92.5 -36.5t-49.5 -117.5zM239 542l-113 150l29 26l99 -129l100 129l29 -26l-113 -150h-31z" />
    <glyph glyph-name="Gcircumflex" unicode="&#x11c;" horiz-adv-x="568" 
d="M318 -13q-60 0 -109 22.5t-84.5 66t-55 107.5t-19.5 148q0 121 44 207q43 84 102 113q60 29 114 29q42 0 74.5 -12.5t57.5 -32t41.5 -42t25.5 -39.5l-39 -31q-24 49 -67.5 78.5t-92.5 29.5q-42 0 -79 -19t-65 -56.5t-44.5 -94t-16.5 -131.5q0 -67 15.5 -121.5t43.5 -93
t67.5 -59.5t87.5 -21q51 0 85.5 7.5t56.5 16.5v209h-119v49h171v-293q-32 -14 -83.5 -25.5t-111.5 -11.5zM213 708l-29 26l113 150h31l113 -150l-29 -26l-100 129z" />
    <glyph glyph-name="gcircumflex" unicode="&#x11d;" horiz-adv-x="475" 
d="M231 -198q-48 0 -99 14q-47 13 -60 23l15 46q22 -14 60 -26.5t86 -12.5q72 0 106 34.5t34 109.5v34q-19 -13 -50 -24t-73 -11q-43 0 -79.5 17t-63.5 48.5t-42.5 77.5t-15.5 105q0 81 35 143q25 43 62.5 69.5t71.5 33t60 6.5q45 0 83 -6t62 -12v-493q0 -79 -46 -127.5
t-146 -48.5zM261 33q35 0 65 13.5t47 29.5v357q-17 5 -40.5 8.5t-52.5 3.5q-36 0 -69 -14t-58 -41t-39.5 -66t-14.5 -89t13 -87.5t35.5 -63t51.5 -38.5t62 -13zM175 542l-29 26l113 150h31l113 -150l-29 -26l-100 129z" />
    <glyph glyph-name="Gbreve" unicode="&#x11e;" horiz-adv-x="568" 
d="M318 -13q-60 0 -109 22.5t-84.5 66t-55 107.5t-19.5 148q0 121 44 207q43 84 102 113q60 29 114 29q42 0 74.5 -12.5t57.5 -32t41.5 -42t25.5 -39.5l-39 -31q-24 49 -67.5 78.5t-92.5 29.5q-42 0 -79 -19t-65 -56.5t-44.5 -94t-16.5 -131.5q0 -67 15.5 -121.5t43.5 -93
t67.5 -59.5t87.5 -21q51 0 85.5 7.5t56.5 16.5v209h-119v49h171v-293q-32 -14 -83.5 -25.5t-111.5 -11.5zM313 718q-26 0 -51.5 8t-46.5 25.5t-34.5 45t-14.5 66.5h40q4 -31 15 -51t26 -31.5t32 -16t34 -4.5q43 0 71 26t35 77h40q0 -43 -16.5 -71.5t-39 -45.5t-47.5 -22.5
t-43 -5.5z" />
    <glyph glyph-name="gbreve" unicode="&#x11f;" horiz-adv-x="475" 
d="M231 -198q-48 0 -99 14q-47 13 -60 23l15 46q22 -14 60 -26.5t86 -12.5q72 0 106 34.5t34 109.5v34q-19 -13 -50 -24t-73 -11q-43 0 -79.5 17t-63.5 48.5t-42.5 77.5t-15.5 105q0 81 35 143q25 43 62.5 69.5t71.5 33t60 6.5q45 0 83 -6t62 -12v-493q0 -79 -46 -127.5
t-146 -48.5zM261 33q35 0 65 13.5t47 29.5v357q-17 5 -40.5 8.5t-52.5 3.5q-36 0 -69 -14t-58 -41t-39.5 -66t-14.5 -89t13 -87.5t35.5 -63t51.5 -38.5t62 -13zM275 552q-26 0 -51.5 8t-46.5 25.5t-34.5 45t-14.5 66.5h40q4 -31 15 -51t26 -31.5t32 -16t34 -4.5q43 0 71 26
t35 77h40q0 -43 -16.5 -71.5t-39 -45.5t-47.5 -22.5t-43 -5.5z" />
    <glyph glyph-name="Gdotaccent" unicode="&#x120;" horiz-adv-x="568" 
d="M318 -13q-60 0 -109 22.5t-84.5 66t-55 107.5t-19.5 148q0 121 44 207q43 84 102 113q60 29 114 29q42 0 74.5 -12.5t57.5 -32t41.5 -42t25.5 -39.5l-39 -31q-24 49 -67.5 78.5t-92.5 29.5q-42 0 -79 -19t-65 -56.5t-44.5 -94t-16.5 -131.5q0 -67 15.5 -121.5t43.5 -93
t67.5 -59.5t87.5 -21q51 0 85.5 7.5t56.5 16.5v209h-119v49h171v-293q-32 -14 -83.5 -25.5t-111.5 -11.5zM318 767q-15 0 -25.5 10t-10.5 30q0 18 10.5 28.5t25.5 10.5t25.5 -10.5t10.5 -28.5q0 -20 -10.5 -30t-25.5 -10z" />
    <glyph glyph-name="gdotaccent" unicode="&#x121;" horiz-adv-x="475" 
d="M231 -198q-48 0 -99 14q-47 13 -60 23l15 46q22 -14 60 -26.5t86 -12.5q72 0 106 34.5t34 109.5v34q-19 -13 -50 -24t-73 -11q-43 0 -79.5 17t-63.5 48.5t-42.5 77.5t-15.5 105q0 81 35 143q25 43 62.5 69.5t71.5 33t60 6.5q45 0 83 -6t62 -12v-493q0 -79 -46 -127.5
t-146 -48.5zM261 33q35 0 65 13.5t47 29.5v357q-17 5 -40.5 8.5t-52.5 3.5q-36 0 -69 -14t-58 -41t-39.5 -66t-14.5 -89t13 -87.5t35.5 -63t51.5 -38.5t62 -13zM274 601q-15 0 -25.5 10t-10.5 30q0 18 10.5 28.5t25.5 10.5t25.5 -10.5t10.5 -28.5q0 -20 -10.5 -30t-25.5 -10
z" />
    <glyph glyph-name="Gcommaaccent" unicode="&#x122;" horiz-adv-x="568" 
d="M318 -13q-60 0 -109 22.5t-84.5 66t-55 107.5t-19.5 148q0 121 44 207q43 84 102 113q60 29 114 29q42 0 74.5 -12.5t57.5 -32t41.5 -42t25.5 -39.5l-39 -31q-24 49 -67.5 78.5t-92.5 29.5q-42 0 -79 -19t-65 -56.5t-44.5 -94t-16.5 -131.5q0 -67 15.5 -121.5t43.5 -93
t67.5 -59.5t87.5 -21q51 0 85.5 7.5t56.5 16.5v209h-119v49h171v-293q-32 -14 -83.5 -25.5t-111.5 -11.5zM243 -174l48 130h59l-68 -130h-39z" />
    <glyph glyph-name="gcommaaccent" unicode="&#x123;" horiz-adv-x="475" 
d="M231 -198q-48 0 -99 14q-47 13 -60 23l15 46q22 -14 60 -26.5t86 -12.5q72 0 106 34.5t34 109.5v34q-19 -13 -50 -24t-73 -11q-43 0 -79.5 17t-63.5 48.5t-42.5 77.5t-15.5 105q0 81 35 143q25 43 62.5 69.5t71.5 33t60 6.5q45 0 83 -6t62 -12v-493q0 -79 -46 -127.5
t-146 -48.5zM261 33q35 0 65 13.5t47 29.5v357q-17 5 -40.5 8.5t-52.5 3.5q-36 0 -69 -14t-58 -41t-39.5 -66t-14.5 -89t13 -87.5t35.5 -63t51.5 -38.5t62 -13zM253 557l68 130h42l-48 -130h-62z" />
    <glyph glyph-name="Hcircumflex" unicode="&#x124;" horiz-adv-x="601" 
d="M76 0v667h52v-297h346v297h51v-667h-51v320h-346v-320h-52zM207 708l-29 26l113 150h31l113 -150l-29 -26l-100 129z" />
    <glyph glyph-name="hcircumflex" unicode="&#x125;" horiz-adv-x="495" 
d="M69 0v667h49v-232q26 21 62.5 37.5t84.5 16.5q66 0 105 -27t54 -78q9 -32 9 -86v-298h-50v295q0 49 -9 75q-26 74 -116 74q-42 0 -76.5 -17.5t-63.5 -40.5v-386h-49zM66 708l-29 26l113 150h31l113 -150l-29 -26l-100 129z" />
    <glyph glyph-name="Hbar" unicode="&#x126;" horiz-adv-x="601" 
d="M76 0v508h-99v47h99v112h52v-112h346v112h51v-112h119v-47h-119v-508h-51v320h-346v-320h-52zM128 370h346v138h-346v-138z" />
    <glyph glyph-name="hbar" unicode="&#x127;" horiz-adv-x="495" 
d="M69 0v557h-76v43h76v67h49v-67h184v-43h-184v-122q26 21 62.5 37.5t84.5 16.5q66 0 105 -27t54 -78q9 -32 9 -86v-298h-50v295q0 49 -9 75q-26 74 -116 74q-42 0 -76.5 -17.5t-63.5 -40.5v-386h-49z" />
    <glyph glyph-name="Itilde" unicode="&#x128;" horiz-adv-x="204" 
d="M76 0v667h52v-667h-52zM170 740q-20 0 -39.5 10t-33.5 21q-41 29 -59 29q-17 0 -31 -13t-27 -44l-35 15q16 43 41 62.5t58 19.5q20 0 38 -9t36 -22q34 -27 56 -27q15 0 29.5 14t28.5 49l35 -17q-18 -48 -44 -68t-53 -20z" />
    <glyph glyph-name="itilde" unicode="&#x129;" horiz-adv-x="204" 
d="M69 0v477h49v-477h-49zM161 574q-20 0 -39.5 10t-33.5 21q-41 29 -59 29q-17 0 -31 -13t-27 -44l-35 15q16 43 41 62.5t58 19.5q20 0 38 -9t36 -22q34 -27 56 -27q15 0 29.5 14t28.5 49l35 -17q-18 -48 -44 -68t-53 -20z" />
    <glyph glyph-name="Imacron" unicode="&#x12a;" horiz-adv-x="204" 
d="M76 0v667h52v-667h-52zM-33 773v46h279v-46h-279z" />
    <glyph glyph-name="imacron" unicode="&#x12b;" horiz-adv-x="204" 
d="M69 0v477h49v-477h-49zM-43 607v46h279v-46h-279z" />
    <glyph glyph-name="Ibreve" unicode="&#x12c;" horiz-adv-x="204" 
d="M76 0v667h52v-667h-52zM108 718q-26 0 -51.5 8t-46.5 25.5t-34.5 45t-14.5 66.5h40q4 -31 15 -51t26 -31.5t32 -16t34 -4.5q43 0 71 26t35 77h40q0 -43 -16.5 -71.5t-39 -45.5t-47.5 -22.5t-43 -5.5z" />
    <glyph glyph-name="ibreve" unicode="&#x12d;" horiz-adv-x="204" 
d="M69 0v477h49v-477h-49zM98 552q-26 0 -51.5 8t-46.5 25.5t-34.5 45t-14.5 66.5h40q4 -31 15 -51t26 -31.5t32 -16t34 -4.5q43 0 71 26t35 77h40q0 -43 -16.5 -71.5t-39 -45.5t-47.5 -22.5t-43 -5.5z" />
    <glyph glyph-name="Iogonek" unicode="&#x12e;" horiz-adv-x="204" 
d="M76 0v667h52v-667h-52zM76 -193q-34 0 -55 19t-21 57q0 26 10.5 49t25.5 41t30 30.5t23 18.5l44 -10q-39 -29 -60.5 -57.5t-21.5 -60.5q0 -42 34 -42q17 0 40 14l12 -42q-28 -17 -61 -17z" />
    <glyph glyph-name="iogonek" unicode="&#x12f;" horiz-adv-x="187" 
d="M69 0v477h49v-477h-49zM93 578q-15 0 -25.5 10t-10.5 30q0 18 10.5 28.5t25.5 10.5t25.5 -10.5t10.5 -28.5q0 -20 -10.5 -30t-25.5 -10zM64 -193q-34 0 -55 19t-21 57q0 26 10.5 49t25.5 41t30 30.5t23 18.5l44 -10q-39 -29 -60.5 -57.5t-21.5 -60.5q0 -42 34 -42
q17 0 40 14l12 -42q-28 -17 -61 -17z" />
    <glyph glyph-name="Idotaccent" unicode="&#x130;" horiz-adv-x="204" 
d="M76 0v667h52v-667h-52zM105 767q-15 0 -25.5 10t-10.5 30q0 18 10.5 28.5t25.5 10.5t25.5 -10.5t10.5 -28.5q0 -20 -10.5 -30t-25.5 -10z" />
    <glyph glyph-name="dotlessi" unicode="&#x131;" horiz-adv-x="187" 
d="M69 0v477h49v-477h-49z" />
    <glyph glyph-name="IJ" unicode="&#x132;" horiz-adv-x="555" 
d="M76 0v667h52v-667h-52zM344 -11q-44 0 -81.5 13t-48.5 25l18 47q7 -10 31.5 -20t43.5 -14t39 -4q51 0 69.5 27t18.5 79v525h52v-533q0 -51 -13 -77q-14 -30 -46 -49t-83 -19z" />
    <glyph glyph-name="ij" unicode="&#x133;" horiz-adv-x="378" 
d="M69 0v477h49v-477h-49zM93 578q-15 0 -25.5 10t-10.5 30q0 18 10.5 28.5t25.5 10.5t25.5 -10.5t10.5 -28.5q0 -20 -10.5 -30t-25.5 -10zM209 -196l-20 41q40 27 55.5 62t15.5 90v480h49v-480q0 -74 -25 -118.5t-75 -74.5zM287 578q-15 0 -25.5 10t-10.5 30
q0 18 10.5 28.5t25.5 10.5t25.5 -10.5t10.5 -28.5q0 -20 -10.5 -30t-25.5 -10z" />
    <glyph glyph-name="Jcircumflex" unicode="&#x134;" horiz-adv-x="353" 
d="M142 -11q-44 0 -81.5 13t-48.5 25l18 47q7 -10 31.5 -20t43.5 -14t39 -4q51 0 69.5 27t18.5 79v525h52v-533q0 -51 -13 -77q-14 -30 -46 -49t-83 -19zM160 708l-29 26l113 150h31l113 -150l-29 -26l-100 129z" />
    <glyph glyph-name="jcircumflex" unicode="&#x135;" horiz-adv-x="187" 
d="M18 -196l-20 41q40 27 55.5 62t15.5 90v480h49v-480q0 -74 -25 -118.5t-75 -74.5zM-7 542l-29 26l113 150h31l113 -150l-29 -26l-100 129z" />
    <glyph glyph-name="Kcommaaccent" unicode="&#x136;" horiz-adv-x="519" 
d="M76 0v667h52v-300h105q28 0 47.5 17.5t40.5 64.5l97 218h56l-101 -227q-16 -35 -31.5 -58t-35.5 -31q28 -8 43.5 -24.5t29.5 -51.5l110 -275h-56l-84 206q-25 58 -36.5 77.5t-28.5 27.5t-39 8h-117v-319h-52zM187 -174l48 130h59l-68 -130h-39z" />
    <glyph glyph-name="kcommaaccent" unicode="&#x137;" horiz-adv-x="439" 
d="M69 0v667h49v-377h78q29 0 50 19t42 67l44 101h53l-50 -110q-16 -35 -33 -59t-37 -32q28 -8 44.5 -23.5t30.5 -50.5l80 -202h-52l-57 141q-26 56 -35 72t-28 24t-40 8h-90v-245h-49zM152 -174l48 130h59l-68 -130h-39z" />
    <glyph glyph-name="kgreenlandic" unicode="&#x138;" horiz-adv-x="439" 
d="M69 0v477h49v-209h78q29 0 50 19t42 67l54 123h53l-60 -132q-16 -35 -33 -59t-37 -32q28 -8 44.5 -23.5t30.5 -50.5l72 -180h-52l-49 119q-26 56 -35 72t-28 24t-40 8h-90v-223h-49z" />
    <glyph glyph-name="Lacute" unicode="&#x139;" horiz-adv-x="450" 
d="M76 0v667h52v-618h307v-49h-359zM31 711l-24 29l155 139l37 -41z" />
    <glyph glyph-name="lacute" unicode="&#x13a;" horiz-adv-x="219" 
d="M148 -11q-39 0 -59 17.5t-22 39.5t-2 36v585h49v-573q0 -35 7.5 -49.5t33.5 -14.5q20 0 50 15l9 -40q-13 -7 -31 -11.5t-35 -4.5zM22 711l-24 29l155 139l37 -41z" />
    <glyph glyph-name="Lcommaaccent" unicode="&#x13b;" horiz-adv-x="450" 
d="M76 0v667h52v-618h307v-49h-359zM185 -174l48 130h59l-68 -130h-39z" />
    <glyph glyph-name="lcommaaccent" unicode="&#x13c;" horiz-adv-x="219" 
d="M148 -11q-39 0 -59 17.5t-22 39.5t-2 36v585h49v-573q0 -35 7.5 -49.5t33.5 -14.5q20 0 50 15l9 -40q-13 -7 -31 -11.5t-35 -4.5zM65 -174l48 130h59l-68 -130h-39z" />
    <glyph glyph-name="Lcaron" unicode="&#x13d;" horiz-adv-x="450" 
d="M76 0v667h52v-618h307v-49h-359zM231 497v170h64l-24 -170h-40z" />
    <glyph glyph-name="lcaron" unicode="&#x13e;" horiz-adv-x="219" 
d="M148 -11q-39 0 -59 17.5t-22 39.5t-2 36v585h49v-573q0 -35 7.5 -49.5t33.5 -14.5q20 0 50 15l9 -40q-13 -7 -31 -11.5t-35 -4.5zM176 497v170h64l-24 -170h-40z" />
    <glyph glyph-name="Ldot" unicode="&#x13f;" horiz-adv-x="450" 
d="M76 0v667h52v-618h307v-49h-359zM335 321q-15 0 -25.5 10t-10.5 30q0 18 10.5 28.5t25.5 10.5t25.5 -10.5t10.5 -28.5q0 -20 -10.5 -30t-25.5 -10z" />
    <glyph glyph-name="ldot" unicode="&#x140;" horiz-adv-x="240" 
d="M148 -11q-39 0 -59 17.5t-22 39.5t-2 36v585h49v-573q0 -35 7.5 -49.5t33.5 -14.5q20 0 50 15l9 -40q-13 -7 -31 -11.5t-35 -4.5zM214 321q-15 0 -25.5 10t-10.5 30q0 18 10.5 28.5t25.5 10.5t25.5 -10.5t10.5 -28.5q0 -20 -10.5 -30t-25.5 -10z" />
    <glyph glyph-name="Lslash" unicode="&#x141;" horiz-adv-x="450" 
d="M76 0v295l-78 -53l-25 37l103 73v315h52v-285l130 91l25 -39l-155 -110v-275h307v-49h-359z" />
    <glyph glyph-name="lslash" unicode="&#x142;" horiz-adv-x="232" 
d="M161 -11q-39 0 -59 17.5t-22 39.5t-2 36v213l-59 -39l-24 36l83 57v318h49v-290l76 55l25 -39l-101 -71v-228q0 -35 7.5 -49.5t33.5 -14.5q20 0 50 15l9 -40q-13 -7 -31 -11.5t-35 -4.5z" />
    <glyph glyph-name="Nacute" unicode="&#x143;" horiz-adv-x="613" 
d="M76 0v667h55l355 -562v562h51v-667h-45l-365 575v-575h-51zM257 717l-24 29l155 139l37 -41z" />
    <glyph glyph-name="nacute" unicode="&#x144;" horiz-adv-x="495" 
d="M69 0v477h44l5 -45q26 23 62.5 40t84.5 17q67 0 106 -26.5t53 -78.5q10 -31 10 -86v-298h-50v295q0 49 -9 75q-26 74 -116 74q-43 0 -78 -18t-63 -44v-382h-49zM185 545l-24 29l155 139l37 -41z" />
    <glyph glyph-name="Ncommaaccent" unicode="&#x145;" horiz-adv-x="613" 
d="M76 0v667h55l355 -562v562h51v-667h-45l-365 575v-575h-51zM240 -174l48 130h59l-68 -130h-39z" />
    <glyph glyph-name="ncommaaccent" unicode="&#x146;" horiz-adv-x="495" 
d="M69 0v477h44l5 -45q26 23 62.5 40t84.5 17q67 0 106 -26.5t53 -78.5q10 -31 10 -86v-298h-50v295q0 49 -9 75q-26 74 -116 74q-43 0 -78 -18t-63 -44v-382h-49zM185 -174l48 130h59l-68 -130h-39z" />
    <glyph glyph-name="Ncaron" unicode="&#x147;" horiz-adv-x="613" 
d="M76 0v667h55l355 -562v562h51v-667h-45l-365 575v-575h-51zM297 706l-113 150l29 26l99 -129l100 129l29 -26l-113 -150h-31z" />
    <glyph glyph-name="ncaron" unicode="&#x148;" horiz-adv-x="495" 
d="M69 0v477h44l5 -45q26 23 62.5 40t84.5 17q67 0 106 -26.5t53 -78.5q10 -31 10 -86v-298h-50v295q0 49 -9 75q-26 74 -116 74q-43 0 -78 -18t-63 -44v-382h-49zM244 542l-113 150l29 26l99 -129l100 129l29 -26l-113 -150h-31z" />
    <glyph glyph-name="napostrophe" unicode="&#x149;" horiz-adv-x="495" 
d="M69 0v477h44l5 -45q26 23 62.5 40t84.5 17q67 0 106 -26.5t53 -78.5q10 -31 10 -86v-298h-50v295q0 49 -9 75q-26 74 -116 74q-43 0 -78 -18t-63 -44v-382h-49zM42 529l-17 22q13 13 25.5 31t14.5 35q-18 0 -30.5 13.5t-12.5 32.5t11 31t30 12q22 0 33.5 -16.5
t11.5 -39.5q0 -35 -20 -67.5t-46 -53.5z" />
    <glyph glyph-name="Eng" unicode="&#x14a;" horiz-adv-x="613" 
d="M437 -196l-20 41q39 26 54.5 60t16.5 87v12l-361 571v-575h-51v667h55l355 -564v564h51v-670q0 -74 -25 -118.5t-75 -74.5z" />
    <glyph glyph-name="eng" unicode="&#x14b;" horiz-adv-x="495" 
d="M334 -196l-21 41q39 26 55 60t16 87v303q0 49 -9 75q-26 74 -116 74q-43 0 -78 -18t-63 -44v-382h-49v477h44l5 -45q26 23 62.5 40t84.5 17q67 0 106 -26.5t53 -78.5q10 -31 10 -86v-301q0 -74 -25 -118.5t-75 -74.5z" />
    <glyph glyph-name="Omacron" unicode="&#x14c;" horiz-adv-x="617" 
d="M309 -13q-66 0 -114.5 29.5t-81 78t-48 111t-15.5 128.5q0 104 37 188q24 59 65 96.5t83 49.5t74 12q94 0 154 -60q61 -58 82 -140q22 -79 22 -146q0 -108 -37 -191q-26 -60 -67 -96.5t-81 -48t-73 -11.5zM309 36q72 0 121.5 52.5t66.5 122.5q16 67 16 123q0 88 -28 157
q-27 72 -74.5 106t-101.5 34q-53 0 -92 -26t-64 -68.5t-37 -95.5t-12 -107q0 -85 29 -161q30 -75 77 -106t99 -31zM169 773v46h279v-46h-279z" />
    <glyph glyph-name="omacron" unicode="&#x14d;" horiz-adv-x="497" 
d="M248 -11q-72 0 -121 45q-44 39 -62 94.5t-18 113.5q0 75 29 135t75 86t97 26q70 0 120 -44q33 -30 52 -68t25 -75.5t6 -61.5q0 -79 -31 -140q-30 -60 -75.5 -85.5t-96.5 -25.5zM248 33q43 -2 72.5 20.5t48 55t26 69t6.5 63.5q1 66 -25 116t-62.5 70t-65.5 18
q-55 0 -93 -38q-35 -35 -47 -78t-12 -87q0 -39 9.5 -76.5t28.5 -67t47.5 -47.5t66.5 -18zM115 610v46h279v-46h-279z" />
    <glyph glyph-name="Obreve" unicode="&#x14e;" horiz-adv-x="617" 
d="M309 -13q-66 0 -114.5 29.5t-81 78t-48 111t-15.5 128.5q0 104 37 188q24 59 65 96.5t83 49.5t74 12q94 0 154 -60q61 -58 82 -140q22 -79 22 -146q0 -108 -37 -191q-26 -60 -67 -96.5t-81 -48t-73 -11.5zM309 36q72 0 121.5 52.5t66.5 122.5q16 67 16 123q0 88 -28 157
q-27 72 -74.5 106t-101.5 34q-53 0 -92 -26t-64 -68.5t-37 -95.5t-12 -107q0 -85 29 -161q30 -75 77 -106t99 -31zM312 718q-26 0 -51.5 8t-46.5 25.5t-34.5 45t-14.5 66.5h40q4 -31 15 -51t26 -31.5t32 -16t34 -4.5q43 0 71 26t35 77h40q0 -43 -16.5 -71.5t-39 -45.5
t-47.5 -22.5t-43 -5.5z" />
    <glyph glyph-name="obreve" unicode="&#x14f;" horiz-adv-x="497" 
d="M248 -11q-72 0 -121 45q-44 39 -62 94.5t-18 113.5q0 75 29 135t75 86t97 26q70 0 120 -44q33 -30 52 -68t25 -75.5t6 -61.5q0 -79 -31 -140q-30 -60 -75.5 -85.5t-96.5 -25.5zM248 33q43 -2 72.5 20.5t48 55t26 69t6.5 63.5q1 66 -25 116t-62.5 70t-65.5 18
q-55 0 -93 -38q-35 -35 -47 -78t-12 -87q0 -39 9.5 -76.5t28.5 -67t47.5 -47.5t66.5 -18zM252 552q-26 0 -51.5 8t-46.5 25.5t-34.5 45t-14.5 66.5h40q4 -31 15 -51t26 -31.5t32 -16t34 -4.5q43 0 71 26t35 77h40q0 -43 -16.5 -71.5t-39 -45.5t-47.5 -22.5t-43 -5.5z" />
    <glyph glyph-name="Ohungarumlaut" unicode="&#x150;" horiz-adv-x="617" 
d="M309 -13q-66 0 -114.5 29.5t-81 78t-48 111t-15.5 128.5q0 104 37 188q24 59 65 96.5t83 49.5t74 12q94 0 154 -60q61 -58 82 -140q22 -79 22 -146q0 -108 -37 -191q-26 -60 -67 -96.5t-81 -48t-73 -11.5zM309 36q72 0 121.5 52.5t66.5 122.5q16 67 16 123q0 88 -28 157
q-27 72 -74.5 106t-101.5 34q-53 0 -92 -26t-64 -68.5t-37 -95.5t-12 -107q0 -85 29 -161q30 -75 77 -106t99 -31zM177 712l-22 28l155 139l35 -40zM338 712l-21 28l154 139l36 -40z" />
    <glyph glyph-name="ohungarumlaut" unicode="&#x151;" horiz-adv-x="497" 
d="M248 -11q-72 0 -121 45q-44 39 -62 94.5t-18 113.5q0 75 29 135t75 86t97 26q70 0 120 -44q33 -30 52 -68t25 -75.5t6 -61.5q0 -79 -31 -140q-30 -60 -75.5 -85.5t-96.5 -25.5zM248 33q43 -2 72.5 20.5t48 55t26 69t6.5 63.5q1 66 -25 116t-62.5 70t-65.5 18
q-55 0 -93 -38q-35 -35 -47 -78t-12 -87q0 -39 9.5 -76.5t28.5 -67t47.5 -47.5t66.5 -18zM122 546l-22 28l155 139l35 -40zM283 546l-21 28l154 139l36 -40z" />
    <glyph glyph-name="OE" unicode="&#x152;" horiz-adv-x="847" 
d="M331 0q-65 0 -117 25.5t-88.5 70.5t-56 106t-19.5 132q0 110 44 190q45 81 108.5 112t128.5 31h472v-49h-305v-240h221v-49h-221v-280h323v-49h-490zM331 49h117v569h-117q-77 0 -132 -48q-54 -45 -74 -113q-19 -63 -19 -123t15.5 -112t45 -90.5t71 -60.5t93.5 -22z" />
    <glyph glyph-name="oe" unicode="&#x153;" horiz-adv-x="818" 
d="M248 -11q-72 0 -121 45q-44 39 -62 94.5t-18 113.5q0 78 28 135q29 60 76 86t98 26q68 0 109 -33t67 -88q23 58 65 89.5t102 31.5q53 0 89.5 -22.5t59 -58t32 -79t9.5 -82.5h-336q0 -102 40.5 -158t119.5 -56q85 0 143 61l15 -45q-14 -14 -35 -27q-19 -12 -54.5 -22.5
t-69.5 -10.5q-69 0 -116.5 32t-66.5 87q-26 -61 -73 -90t-101 -29zM248 33q43 0 72.5 21.5t48 54t26 69t7.5 63.5q0 75 -26.5 122t-63 64.5t-64.5 17.5q-55 0 -93 -38q-35 -35 -47 -78t-12 -87q0 -39 9.5 -76.5t28.5 -67t47.5 -47.5t66.5 -18zM450 291h276q-3 57 -30 95.5
t-57 48.5t-46 10q-54 0 -93 -36.5t-50 -117.5z" />
    <glyph glyph-name="Racute" unicode="&#x154;" horiz-adv-x="533" 
d="M76 0v667h200q97 0 147 -47t50 -134q1 -51 -20.5 -88t-47 -53t-42.5 -21q32 -10 46 -38.5t27 -73.5q4 -21 30 -109l27 -103h-55l-28 104q-35 117 -42 138q-10 28 -30 44t-50 16h-160v-302h-52zM128 351h144q72 0 109 33t37 102q0 63 -36.5 97.5t-106.5 34.5h-147v-267z
M182 711l-24 29l155 139l37 -41z" />
    <glyph glyph-name="racute" unicode="&#x155;" horiz-adv-x="292" 
d="M69 0v477h43l4 -49q21 21 55.5 40.5t61.5 19.5q25 0 54 -7l-7 -48q-18 7 -42 7q-32 0 -62.5 -18.5t-57.5 -45.5v-376h-49zM117 545l-24 29l155 139l37 -41z" />
    <glyph glyph-name="Rcommaaccent" unicode="&#x156;" horiz-adv-x="533" 
d="M76 0v667h200q97 0 147 -47t50 -134q1 -51 -20.5 -88t-47 -53t-42.5 -21q32 -10 46 -38.5t27 -73.5q4 -21 30 -109l27 -103h-55l-28 104q-35 117 -42 138q-10 28 -30 44t-50 16h-160v-302h-52zM128 351h144q72 0 109 33t37 102q0 63 -36.5 97.5t-106.5 34.5h-147v-267z
M204 -174l48 130h59l-68 -130h-39z" />
    <glyph glyph-name="rcommaaccent" unicode="&#x157;" horiz-adv-x="292" 
d="M69 0v477h43l4 -49q21 21 55.5 40.5t61.5 19.5q25 0 54 -7l-7 -48q-18 7 -42 7q-32 0 -62.5 -18.5t-57.5 -45.5v-376h-49zM16 -174l48 130h59l-68 -130h-39z" />
    <glyph glyph-name="Rcaron" unicode="&#x158;" horiz-adv-x="533" 
d="M76 0v667h200q97 0 147 -47t50 -134q1 -51 -20.5 -88t-47 -53t-42.5 -21q32 -10 46 -38.5t27 -73.5q4 -21 30 -109l27 -103h-55l-28 104q-35 117 -42 138q-10 28 -30 44t-50 16h-160v-302h-52zM128 351h144q72 0 109 33t37 102q0 63 -36.5 97.5t-106.5 34.5h-147v-267z
M226 708l-113 150l29 26l99 -129l100 129l29 -26l-113 -150h-31z" />
    <glyph glyph-name="rcaron" unicode="&#x159;" horiz-adv-x="292" 
d="M69 0v477h43l4 -49q21 21 55.5 40.5t61.5 19.5q25 0 54 -7l-7 -48q-18 7 -42 7q-32 0 -62.5 -18.5t-57.5 -45.5v-376h-49zM162 542l-113 150l29 26l99 -129l100 129l29 -26l-113 -150h-31z" />
    <glyph glyph-name="Sacute" unicode="&#x15a;" horiz-adv-x="480" 
d="M236 -13q-48 2 -98.5 20t-74 36.5t-31.5 28.5l29 48q14 -15 33.5 -30t43 -27t49 -19.5t51.5 -7.5q37 -2 65 9t46.5 29.5t28 43t8.5 50.5q0 30 -8.5 53t-25.5 41t-43.5 32.5t-62.5 28.5l-68 24q-60 21 -95.5 60.5t-35.5 102.5q0 55 30 96q31 42 75.5 58t86.5 16
q70 0 118.5 -26t77.5 -62l-32 -39q-24 30 -63.5 54t-96.5 24q-60 1 -99 -28.5t-40 -86.5q0 -48 28.5 -76t74.5 -45l65 -24q48 -17 80.5 -37t51.5 -44t27.5 -52.5t8.5 -61.5q0 -94 -55 -141.5t-149 -47.5zM171 711l-24 29l155 139l37 -41z" />
    <glyph glyph-name="sacute" unicode="&#x15b;" horiz-adv-x="377" 
d="M188 -11q-59 0 -95 18t-52 34l19 45q17 -17 50 -35t78 -18q51 0 78.5 22.5t27.5 64.5t-26 68q-27 27 -95 52q-65 23 -93 52.5t-28 79.5q0 40 24.5 68t58.5 38.5t60 10.5q51 0 88 -18t56 -38l-21 -39q-29 25 -59 38.5t-68 13.5q-45 0 -67 -20t-22 -51q0 -18 5.5 -31
t18.5 -24.5t35.5 -21.5t55.5 -22q63 -23 95 -56.5t32 -95.5q0 -37 -14.5 -63t-38 -42.5t-51.5 -23t-52 -6.5zM129 545l-24 29l155 139l37 -41z" />
    <glyph glyph-name="Scircumflex" unicode="&#x15c;" horiz-adv-x="480" 
d="M236 -13q-48 2 -98.5 20t-74 36.5t-31.5 28.5l29 48q14 -15 33.5 -30t43 -27t49 -19.5t51.5 -7.5q37 -2 65 9t46.5 29.5t28 43t8.5 50.5q0 30 -8.5 53t-25.5 41t-43.5 32.5t-62.5 28.5l-68 24q-60 21 -95.5 60.5t-35.5 102.5q0 55 30 96q31 42 75.5 58t86.5 16
q70 0 118.5 -26t77.5 -62l-32 -39q-24 30 -63.5 54t-96.5 24q-60 1 -99 -28.5t-40 -86.5q0 -48 28.5 -76t74.5 -45l65 -24q48 -17 80.5 -37t51.5 -44t27.5 -52.5t8.5 -61.5q0 -94 -55 -141.5t-149 -47.5zM151 708l-29 26l113 150h31l113 -150l-29 -26l-100 129z" />
    <glyph glyph-name="scircumflex" unicode="&#x15d;" horiz-adv-x="377" 
d="M188 -11q-59 0 -95 18t-52 34l19 45q17 -17 50 -35t78 -18q51 0 78.5 22.5t27.5 64.5t-26 68q-27 27 -95 52q-65 23 -93 52.5t-28 79.5q0 40 24.5 68t58.5 38.5t60 10.5q51 0 88 -18t56 -38l-21 -39q-29 25 -59 38.5t-68 13.5q-45 0 -67 -20t-22 -51q0 -18 5.5 -31
t18.5 -24.5t35.5 -21.5t55.5 -22q63 -23 95 -56.5t32 -95.5q0 -37 -14.5 -63t-38 -42.5t-51.5 -23t-52 -6.5zM98 542l-29 26l113 150h31l113 -150l-29 -26l-100 129z" />
    <glyph glyph-name="Scedilla" unicode="&#x15e;" horiz-adv-x="480" 
d="M236 -13q-48 2 -98.5 20t-74 36.5t-31.5 28.5l29 48q14 -15 33.5 -30t43 -27t49 -19.5t51.5 -7.5q37 -2 65 9t46.5 29.5t28 43t8.5 50.5q0 30 -8.5 53t-25.5 41t-43.5 32.5t-62.5 28.5l-68 24q-60 21 -95.5 60.5t-35.5 102.5q0 55 30 96q31 42 75.5 58t86.5 16
q70 0 118.5 -26t77.5 -62l-32 -39q-24 30 -63.5 54t-96.5 24q-60 1 -99 -28.5t-40 -86.5q0 -48 28.5 -76t74.5 -45l65 -24q48 -17 80.5 -37t51.5 -44t27.5 -52.5t8.5 -61.5q0 -94 -55 -141.5t-149 -47.5zM168 -174l48 130h59l-68 -130h-39z" />
    <glyph glyph-name="scedilla" unicode="&#x15f;" horiz-adv-x="377" 
d="M188 -11q-59 0 -95 18t-52 34l19 45q17 -17 50 -35t78 -18q51 0 78.5 22.5t27.5 64.5t-26 68q-27 27 -95 52q-65 23 -93 52.5t-28 79.5q0 40 24.5 68t58.5 38.5t60 10.5q51 0 88 -18t56 -38l-21 -39q-29 25 -59 38.5t-68 13.5q-45 0 -67 -20t-22 -51q0 -18 5.5 -31
t18.5 -24.5t35.5 -21.5t55.5 -22q63 -23 95 -56.5t32 -95.5q0 -37 -14.5 -63t-38 -42.5t-51.5 -23t-52 -6.5zM117 -174l48 130h59l-68 -130h-39z" />
    <glyph glyph-name="Scaron" unicode="&#x160;" horiz-adv-x="480" 
d="M236 -13q-48 2 -98.5 20t-74 36.5t-31.5 28.5l29 48q14 -15 33.5 -30t43 -27t49 -19.5t51.5 -7.5q37 -2 65 9t46.5 29.5t28 43t8.5 50.5q0 30 -8.5 53t-25.5 41t-43.5 32.5t-62.5 28.5l-68 24q-60 21 -95.5 60.5t-35.5 102.5q0 55 30 96q31 42 75.5 58t86.5 16
q70 0 118.5 -26t77.5 -62l-32 -39q-24 30 -63.5 54t-96.5 24q-60 1 -99 -28.5t-40 -86.5q0 -48 28.5 -76t74.5 -45l65 -24q48 -17 80.5 -37t51.5 -44t27.5 -52.5t8.5 -61.5q0 -94 -55 -141.5t-149 -47.5zM235 708l-113 150l29 26l99 -129l100 129l29 -26l-113 -150h-31z" />
    <glyph glyph-name="scaron" unicode="&#x161;" horiz-adv-x="377" 
d="M188 -11q-59 0 -95 18t-52 34l19 45q17 -17 50 -35t78 -18q51 0 78.5 22.5t27.5 64.5t-26 68q-27 27 -95 52q-65 23 -93 52.5t-28 79.5q0 40 24.5 68t58.5 38.5t60 10.5q51 0 88 -18t56 -38l-21 -39q-29 25 -59 38.5t-68 13.5q-45 0 -67 -20t-22 -51q0 -18 5.5 -31
t18.5 -24.5t35.5 -21.5t55.5 -22q63 -23 95 -56.5t32 -95.5q0 -37 -14.5 -63t-38 -42.5t-51.5 -23t-52 -6.5zM189 542l-113 150l29 26l99 -129l100 129l29 -26l-113 -150h-31z" />
    <glyph glyph-name="Tcommaaccent" unicode="&#x162;" horiz-adv-x="455" 
d="M201 0v618h-196v49h445v-49h-197v-618h-52zM146 -174l48 130h59l-68 -130h-39z" />
    <glyph glyph-name="tcommaaccent" unicode="&#x163;" horiz-adv-x="291" 
d="M194 -11q-66 0 -86 53q-9 21 -9 55v336h-78v44h78v127h49v-127h119v-44h-119v-332q0 -38 14.5 -53t40.5 -15q23 0 58 22l20 -37q-14 -11 -33 -20t-54 -9zM99 -174l48 130h59l-68 -130h-39z" />
    <glyph glyph-name="Tcaron" unicode="&#x164;" horiz-adv-x="455" 
d="M201 0v618h-196v49h445v-49h-197v-618h-52zM209 708l-113 150l29 26l99 -129l100 129l29 -26l-113 -150h-31z" />
    <glyph glyph-name="tcaron" unicode="&#x165;" horiz-adv-x="291" 
d="M194 -11q-66 0 -86 53q-9 21 -9 55v336h-78v44h78v127h49v-127h119v-44h-119v-332q0 -38 14.5 -53t40.5 -15q23 0 58 22l20 -37q-14 -11 -33 -20t-54 -9zM218 530v170h64l-24 -170h-40z" />
    <glyph glyph-name="Tbar" unicode="&#x166;" horiz-adv-x="455" 
d="M201 0v310h-148v47h148v261h-196v49h445v-49h-197v-261h157v-47h-157v-310h-52z" />
    <glyph glyph-name="tbar" unicode="&#x167;" horiz-adv-x="291" 
d="M194 -11q-66 0 -86 53q-9 21 -9 55v142h-78v44h78v150h-78v44h78v127h49v-127h119v-44h-119v-150h119v-44h-119v-138q0 -38 14.5 -53t40.5 -15q23 0 58 22l20 -37q-14 -11 -33 -20t-54 -9z" />
    <glyph glyph-name="Utilde" unicode="&#x168;" horiz-adv-x="591" 
d="M296 -13q-48 0 -89.5 15.5t-72 45.5t-48 73.5t-17.5 100.5v445h51v-430q0 -91 45 -145.5t130 -54.5t130.5 53t45.5 147v430h52v-445q0 -57 -18 -100.5t-48.5 -73.5t-72 -45.5t-88.5 -15.5zM356 740q-20 0 -39.5 10t-33.5 21q-41 29 -59 29q-17 0 -31 -13t-27 -44l-35 15
q16 43 41 62.5t58 19.5q20 0 38 -9t36 -22q34 -27 56 -27q15 0 29.5 14t28.5 49l35 -17q-18 -48 -44 -68t-53 -20z" />
    <glyph glyph-name="utilde" unicode="&#x169;" horiz-adv-x="494" 
d="M229 -12q-126 0 -158 104q-10 31 -10 85v300h49v-298q0 -50 10 -74q14 -38 42.5 -55t73.5 -17q43 0 77 18t63 44v382h49v-477h-45l-4 45q-26 -23 -61 -40t-86 -17zM306 574q-20 0 -39.5 10t-33.5 21q-41 29 -59 29q-17 0 -31 -13t-27 -44l-35 15q16 43 41 62.5t58 19.5
q20 0 38 -9t36 -22q34 -27 56 -27q15 0 29.5 14t28.5 49l35 -17q-18 -48 -44 -68t-53 -20z" />
    <glyph glyph-name="Umacron" unicode="&#x16a;" horiz-adv-x="591" 
d="M296 -13q-48 0 -89.5 15.5t-72 45.5t-48 73.5t-17.5 100.5v445h51v-430q0 -91 45 -145.5t130 -54.5t130.5 53t45.5 147v430h52v-445q0 -57 -18 -100.5t-48.5 -73.5t-72 -45.5t-88.5 -15.5zM154 773v46h279v-46h-279z" />
    <glyph glyph-name="umacron" unicode="&#x16b;" horiz-adv-x="494" 
d="M229 -12q-126 0 -158 104q-10 31 -10 85v300h49v-298q0 -50 10 -74q14 -38 42.5 -55t73.5 -17q43 0 77 18t63 44v382h49v-477h-45l-4 45q-26 -23 -61 -40t-86 -17zM102 591v46h279v-46h-279z" />
    <glyph glyph-name="Ubreve" unicode="&#x16c;" horiz-adv-x="591" 
d="M296 -13q-48 0 -89.5 15.5t-72 45.5t-48 73.5t-17.5 100.5v445h51v-430q0 -91 45 -145.5t130 -54.5t130.5 53t45.5 147v430h52v-445q0 -57 -18 -100.5t-48.5 -73.5t-72 -45.5t-88.5 -15.5zM299 721q-26 0 -51.5 8t-46.5 25.5t-34.5 45t-14.5 66.5h40q4 -31 15 -51
t26 -31.5t32 -16t34 -4.5q43 0 71 26t35 77h40q0 -43 -16.5 -71.5t-39 -45.5t-47.5 -22.5t-43 -5.5z" />
    <glyph glyph-name="ubreve" unicode="&#x16d;" horiz-adv-x="494" 
d="M229 -12q-126 0 -158 104q-10 31 -10 85v300h49v-298q0 -50 10 -74q14 -38 42.5 -55t73.5 -17q43 0 77 18t63 44v382h49v-477h-45l-4 45q-26 -23 -61 -40t-86 -17zM244 559q-26 0 -51.5 8t-46.5 25.5t-34.5 45t-14.5 66.5h40q4 -31 15 -51t26 -31.5t32 -16t34 -4.5
q43 0 71 26t35 77h40q0 -43 -16.5 -71.5t-39 -45.5t-47.5 -22.5t-43 -5.5z" />
    <glyph glyph-name="Uring" unicode="&#x16e;" horiz-adv-x="591" 
d="M296 -13q-48 0 -89.5 15.5t-72 45.5t-48 73.5t-17.5 100.5v445h51v-430q0 -91 45 -145.5t130 -54.5t130.5 53t45.5 147v430h52v-445q0 -57 -18 -100.5t-48.5 -73.5t-72 -45.5t-88.5 -15.5zM298 700q-26 0 -44 10t-29.5 25.5t-15.5 32.5t-4 31q0 34 16 58t37.5 33t39.5 9
q33 0 57 -21t30.5 -44t6.5 -35q0 -33 -17.5 -60t-38.5 -33t-38 -6zM299 735q25 0 39.5 17.5t14.5 46.5q0 27 -16 46t-38 19t-38 -19t-16 -46q0 -29 15 -46.5t39 -17.5z" />
    <glyph glyph-name="uring" unicode="&#x16f;" horiz-adv-x="494" 
d="M229 -12q-126 0 -158 104q-10 31 -10 85v300h49v-298q0 -50 10 -74q14 -38 42.5 -55t73.5 -17q43 0 77 18t63 44v382h49v-477h-45l-4 45q-26 -23 -61 -40t-86 -17zM247 534q-26 0 -44 10t-29.5 25.5t-15.5 32.5t-4 31q0 34 16 58t37.5 33t39.5 9q33 0 57 -21t30.5 -44
t6.5 -35q0 -33 -17.5 -60t-38.5 -33t-38 -6zM248 569q25 0 39.5 17.5t14.5 46.5q0 27 -16 46t-38 19t-38 -19t-16 -46q0 -29 15 -46.5t39 -17.5z" />
    <glyph glyph-name="Uhungarumlaut" unicode="&#x170;" horiz-adv-x="591" 
d="M296 -13q-48 0 -89.5 15.5t-72 45.5t-48 73.5t-17.5 100.5v445h51v-430q0 -91 45 -145.5t130 -54.5t130.5 53t45.5 147v430h52v-445q0 -57 -18 -100.5t-48.5 -73.5t-72 -45.5t-88.5 -15.5zM179 712l-22 28l155 139l35 -40zM340 712l-21 28l154 139l36 -40z" />
    <glyph glyph-name="uhungarumlaut" unicode="&#x171;" horiz-adv-x="494" 
d="M229 -12q-126 0 -158 104q-10 31 -10 85v300h49v-298q0 -50 10 -74q14 -38 42.5 -55t73.5 -17q43 0 77 18t63 44v382h49v-477h-45l-4 45q-26 -23 -61 -40t-86 -17zM112 549l-22 28l155 139l35 -40zM273 549l-21 28l154 139l36 -40z" />
    <glyph glyph-name="Uogonek" unicode="&#x172;" horiz-adv-x="591" 
d="M296 -13q-48 0 -89.5 15.5t-72 45.5t-48 73.5t-17.5 100.5v445h51v-430q0 -91 45 -145.5t130 -54.5t130.5 53t45.5 147v430h52v-445q0 -57 -18 -100.5t-48.5 -73.5t-72 -45.5t-88.5 -15.5zM349 -193q-34 0 -55 19t-21 57q0 26 10.5 49t25.5 41t30 30.5t23 18.5l44 -10
q-39 -29 -60.5 -57.5t-21.5 -60.5q0 -42 34 -42q17 0 40 14l12 -42q-28 -17 -61 -17z" />
    <glyph glyph-name="uogonek" unicode="&#x173;" horiz-adv-x="494" 
d="M229 -12q-126 0 -158 104q-10 31 -10 85v300h49v-298q0 -50 10 -74q14 -38 42.5 -55t73.5 -17q43 0 77 18t63 44v382h49v-477h-45l-4 45q-26 -23 -61 -40t-86 -17zM367 -193q-34 0 -55 19t-21 57q0 26 10.5 49t25.5 41t30 30.5t23 18.5l44 -10q-39 -29 -60.5 -57.5
t-21.5 -60.5q0 -42 34 -42q17 0 40 14l12 -42q-28 -17 -61 -17z" />
    <glyph glyph-name="Wcircumflex" unicode="&#x174;" horiz-adv-x="718" 
d="M161 0l-151 667h53l126 -556l145 556h49l152 -559l121 559h52l-149 -667h-45l-156 566l-153 -566h-44zM261 708l-29 26l113 150h31l113 -150l-29 -26l-100 129z" />
    <glyph glyph-name="wcircumflex" unicode="&#x175;" horiz-adv-x="628" 
d="M144 0l-139 477h54l113 -410l120 410h44l120 -408l115 408h52l-143 -477h-51l-116 393l-119 -393h-50zM214 542l-29 26l113 150h31l113 -150l-29 -26l-100 129z" />
    <glyph glyph-name="Ycircumflex" unicode="&#x176;" horiz-adv-x="499" 
d="M220 0v321l-215 346h59l183 -295l190 295h57l-222 -345v-322h-52zM148 708l-29 26l113 150h31l113 -150l-29 -26l-100 129z" />
    <glyph glyph-name="ycircumflex" unicode="&#x177;" horiz-adv-x="448" 
d="M-4 -192l-6 44h8q63 0 116 36t81 99l-186 490h55l163 -438q42 88 84 201.5t75 236.5h52q-35 -138 -76.5 -243t-79.5 -187q-43 -90 -78 -137.5t-90 -74.5t-118 -27zM130 542l-29 26l113 150h31l113 -150l-29 -26l-100 129z" />
    <glyph glyph-name="Ydieresis" unicode="&#x178;" horiz-adv-x="499" 
d="M220 0v321l-215 346h59l183 -295l190 295h57l-222 -345v-322h-52zM166 767q-15 0 -25.5 10t-10.5 30q0 18 10.5 28.5t25.5 10.5t25.5 -10.5t10.5 -28.5q0 -20 -10.5 -30t-25.5 -10zM337 767q-15 0 -25.5 10t-10.5 30q0 18 10.5 28.5t25.5 10.5t25.5 -10.5t10.5 -28.5
q0 -20 -10.5 -30t-25.5 -10z" />
    <glyph glyph-name="Zacute" unicode="&#x179;" horiz-adv-x="518" 
d="M42 0v51l372 567h-356v49h408v-55l-369 -563h383v-49h-438zM194 709l-24 29l155 139l37 -41z" />
    <glyph glyph-name="zacute" unicode="&#x17a;" horiz-adv-x="426" 
d="M41 0v52l290 381h-279v44h329v-53l-290 -380h297v-44h-347zM156 545l-24 29l155 139l37 -41z" />
    <glyph glyph-name="Zdotaccent" unicode="&#x17b;" horiz-adv-x="518" 
d="M42 0v51l372 567h-356v49h408v-55l-369 -563h383v-49h-438zM263 751q-15 0 -25.5 10t-10.5 30q0 18 10.5 28.5t25.5 10.5t25.5 -10.5t10.5 -28.5q0 -20 -10.5 -30t-25.5 -10z" />
    <glyph glyph-name="zdotaccent" unicode="&#x17c;" horiz-adv-x="426" 
d="M41 0v52l290 381h-279v44h329v-53l-290 -380h297v-44h-347zM221 601q-15 0 -25.5 10t-10.5 30q0 18 10.5 28.5t25.5 10.5t25.5 -10.5t10.5 -28.5q0 -20 -10.5 -30t-25.5 -10z" />
    <glyph glyph-name="Zcaron" unicode="&#x17d;" horiz-adv-x="518" 
d="M42 0v51l372 567h-356v49h408v-55l-369 -563h383v-49h-438zM256 705l-113 150l29 26l99 -129l100 129l29 -26l-113 -150h-31z" />
    <glyph glyph-name="zcaron" unicode="&#x17e;" horiz-adv-x="426" 
d="M41 0v52l290 381h-279v44h329v-53l-290 -380h297v-44h-347zM219 542l-113 150l29 26l99 -129l100 129l29 -26l-113 -150h-31z" />
    <glyph glyph-name="longs" unicode="&#x17f;" horiz-adv-x="257" 
d="M89 0v531q0 77 35 114q32 35 87 35q30 0 51.5 -6.5t33.5 -12.5l-11 -42q-12 6 -31.5 11.5t-42.5 5.5q-33 0 -50 -20q-22 -25 -22 -88v-528h-50z" />
    <glyph glyph-name="florin" unicode="&#x192;" 
d="M-19 -148v47q76 4 112 31q23 18 35.5 44t19.5 61l69 352h-104v46h113l9 47q12 68 45.5 110.5t76.5 55.5t82 14v-47q-54 -3 -87.5 -24.5t-46 -53t-18.5 -61.5l-9 -41h161v-46h-170l-72 -363q-10 -49 -27.5 -80.5t-48.5 -54.5q-27 -20 -64.5 -28.5t-75.5 -8.5z" />
    <glyph glyph-name="Scommaaccent" unicode="&#x218;" horiz-adv-x="480" 
d="M236 -13q-48 2 -98.5 20t-74 36.5t-31.5 28.5l29 48q14 -15 33.5 -30t43 -27t49 -19.5t51.5 -7.5q37 -2 65 9t46.5 29.5t28 43t8.5 50.5q0 30 -8.5 53t-25.5 41t-43.5 32.5t-62.5 28.5l-68 24q-60 21 -95.5 60.5t-35.5 102.5q0 55 30 96q31 42 75.5 58t86.5 16
q70 0 118.5 -26t77.5 -62l-32 -39q-24 30 -63.5 54t-96.5 24q-60 1 -99 -28.5t-40 -86.5q0 -48 28.5 -76t74.5 -45l65 -24q48 -17 80.5 -37t51.5 -44t27.5 -52.5t8.5 -61.5q0 -94 -55 -141.5t-149 -47.5zM168 -174l48 130h59l-68 -130h-39z" />
    <glyph glyph-name="scommaaccent" unicode="&#x219;" horiz-adv-x="377" 
d="M188 -11q-59 0 -95 18t-52 34l19 45q17 -17 50 -35t78 -18q51 0 78.5 22.5t27.5 64.5t-26 68q-27 27 -95 52q-65 23 -93 52.5t-28 79.5q0 40 24.5 68t58.5 38.5t60 10.5q51 0 88 -18t56 -38l-21 -39q-29 25 -59 38.5t-68 13.5q-45 0 -67 -20t-22 -51q0 -18 5.5 -31
t18.5 -24.5t35.5 -21.5t55.5 -22q63 -23 95 -56.5t32 -95.5q0 -37 -14.5 -63t-38 -42.5t-51.5 -23t-52 -6.5zM117 -174l48 130h59l-68 -130h-39z" />
    <glyph glyph-name="uni021A" unicode="&#x21a;" horiz-adv-x="455" 
d="M201 0v618h-196v49h445v-49h-197v-618h-52zM146 -174l48 130h59l-68 -130h-39z" />
    <glyph glyph-name="uni021B" unicode="&#x21b;" horiz-adv-x="291" 
d="M194 -11q-66 0 -86 53q-9 21 -9 55v336h-78v44h78v127h49v-127h119v-44h-119v-332q0 -38 14.5 -53t40.5 -15q23 0 58 22l20 -37q-14 -11 -33 -20t-54 -9zM99 -174l48 130h59l-68 -130h-39z" />
    <glyph glyph-name="dotlessj" unicode="&#x237;" horiz-adv-x="187" 
d="M18 -196l-20 41q40 27 55.5 62t15.5 90v480h49v-480q0 -74 -25 -118.5t-75 -74.5z" />
    <glyph glyph-name="circumflex" unicode="&#x2c6;" horiz-adv-x="290" 
d="M45 542l-29 26l113 150h31l113 -150l-29 -26l-100 129z" />
    <glyph glyph-name="caron" unicode="&#x2c7;" horiz-adv-x="290" 
d="M129 542l-113 150l29 26l99 -129l100 129l29 -26l-113 -150h-31z" />
    <glyph glyph-name="uni02C9" unicode="&#x2c9;" horiz-adv-x="290" 
d="M5 607v46h279v-46h-279z" />
    <glyph glyph-name="breve" unicode="&#x2d8;" horiz-adv-x="290" 
d="M146 552q-26 0 -51.5 8t-46.5 25.5t-34.5 45t-14.5 66.5h40q4 -31 15 -51t26 -31.5t32 -16t34 -4.5q43 0 71 26t35 77h40q0 -43 -16.5 -71.5t-39 -45.5t-47.5 -22.5t-43 -5.5z" />
    <glyph glyph-name="dotaccent" unicode="&#x2d9;" horiz-adv-x="290" 
d="M145 601q-15 0 -25.5 10t-10.5 30q0 18 10.5 28.5t25.5 10.5t25.5 -10.5t10.5 -28.5q0 -20 -10.5 -30t-25.5 -10z" />
    <glyph glyph-name="ring" unicode="&#x2da;" horiz-adv-x="290" 
d="M144 534q-26 0 -44 10t-29.5 25.5t-15.5 32.5t-4 31q0 34 16 58t37.5 33t39.5 9q33 0 57 -21t30.5 -44t6.5 -35q0 -33 -17.5 -60t-38.5 -33t-38 -6zM145 569q25 0 39.5 17.5t14.5 46.5q0 27 -16 46t-38 19t-38 -19t-16 -46q0 -29 15 -46.5t39 -17.5z" />
    <glyph glyph-name="ogonek" unicode="&#x2db;" horiz-adv-x="290" 
d="M138 -193q-34 0 -55 19t-21 57q0 26 10.5 49t25.5 41t30 30.5t23 18.5l44 -10q-39 -29 -60.5 -57.5t-21.5 -60.5q0 -42 34 -42q17 0 40 14l12 -42q-28 -17 -61 -17z" />
    <glyph glyph-name="tilde" unicode="&#x2dc;" horiz-adv-x="290" 
d="M209 574q-20 0 -39.5 10t-33.5 21q-41 29 -59 29q-17 0 -31 -13t-27 -44l-35 15q16 43 41 62.5t58 19.5q20 0 38 -9t36 -22q34 -27 56 -27q15 0 29.5 14t28.5 49l35 -17q-18 -48 -44 -68t-53 -20z" />
    <glyph glyph-name="hungarumlaut" unicode="&#x2dd;" horiz-adv-x="290" 
d="M-9 546l-22 28l155 139l35 -40zM152 546l-21 28l154 139l36 -40z" />
    <glyph glyph-name="tonos" unicode="&#x384;" horiz-adv-x="442" 
d="M184 539l-35 16l94 194l52 -21z" />
    <glyph glyph-name="dieresistonos" unicode="&#x385;" horiz-adv-x="342" 
d="M184 539l-35 16l94 194l52 -21zM105 601q-16 0 -27.5 12.5t-11.5 31.5q0 18 11.5 31t27.5 13t27 -13t11 -31q0 -19 -11 -31.5t-27 -12.5zM337 601q-16 0 -27.5 12.5t-11.5 31.5q0 18 11.5 31t27.5 13t27 -13t11 -31q0 -19 -11 -31.5t-27 -12.5z" />
    <glyph glyph-name="Alphatonos" unicode="&#x386;" horiz-adv-x="533" 
d="M82 452l-37 11l62 205l55 -13zM5 0l236 667h48l239 -667h-55l-78 214h-260l-76 -214h-54zM151 264h228l-115 340z" />
    <glyph glyph-name="Epsilontonos" unicode="&#x388;" horiz-adv-x="467" 
d="M-23 452l-37 11l62 205l55 -13zM76 0v667h356v-49h-305v-240h221v-49h-221v-280h323v-49h-374z" />
    <glyph glyph-name="Etatonos" unicode="&#x389;" horiz-adv-x="603" 
d="M76 0v667h52v-297h346v297h51v-667h-51v320h-346v-320h-52zM-26 452l-37 11l62 205l55 -13z" />
    <glyph glyph-name="Iotatonos" unicode="&#x38a;" horiz-adv-x="206" 
d="M76 0v667h52v-667h-52zM-25 452l-37 11l62 205l55 -13z" />
    <glyph glyph-name="Omicrontonos" unicode="&#x38c;" horiz-adv-x="611" 
d="M309 -13q-66 0 -114.5 29.5t-81 78t-48 111t-15.5 128.5q0 104 37 188q24 59 65 96.5t83 49.5t74 12q94 0 154 -60q61 -58 82 -140q22 -79 22 -146q0 -108 -37 -191q-26 -60 -67 -96.5t-81 -48t-73 -11.5zM309 36q72 0 121.5 52.5t66.5 122.5q16 67 16 123q0 88 -28 157
q-27 72 -74.5 106t-101.5 34q-53 0 -92 -26t-64 -68.5t-37 -95.5t-12 -107q0 -85 29 -161q30 -75 77 -106t99 -31zM-2 452l-37 11l62 205l55 -13z" />
    <glyph glyph-name="Upsilontonos" unicode="&#x38e;" horiz-adv-x="521" 
d="M233 0v160q0 44 -3 73q-9 79 -47.5 165.5t-85 147t-83.5 92.5l33 39q35 -30 70 -73t65 -93t51.5 -103t28.5 -102q17 77 61 161.5t85.5 133.5t72.5 76l30 -37q-52 -44 -103.5 -122.5t-80 -151.5t-35.5 -127q-7 -49 -7 -96v-143h-52zM-79 452l-37 11l62 205l55 -13z" />
    <glyph glyph-name="Omegatonos" unicode="&#x38f;" horiz-adv-x="657" 
d="M56 0v49h143q-27 22 -52 50.5t-44.5 65.5t-31 83.5t-11.5 103.5q0 105 39.5 184t102 112t127.5 33q96 0 159 -52.5t86 -128t23 -143.5q0 -83 -24.5 -148t-56.5 -102t-58 -58h143v-49h-211v51q67 45 109.5 121.5t42.5 180.5q0 93 -30.5 159t-80.5 93t-101 27
q-52 0 -92.5 -21.5t-68 -59.5t-41.5 -89t-14 -111q0 -52 12 -96.5t32.5 -82t48.5 -68t61 -53.5v-51h-212zM5 452l-37 11l62 205l55 -13z" />
    <glyph glyph-name="iotadieresistonos" unicode="&#x390;" horiz-adv-x="235" 
d="M164 -11q-43 0 -68 24.5t-25 80.5v382h50v-374q0 -36 12 -52t38 -16t53 12l8 -43q-31 -14 -68 -14zM87 539l-35 16l94 194l52 -21zM8 601q-16 0 -27.5 12.5t-11.5 31.5q0 18 11.5 31t27.5 13t27 -13t11 -31q0 -19 -11 -31.5t-27 -12.5zM240 601q-16 0 -27.5 12.5
t-11.5 31.5q0 18 11.5 31t27.5 13t27 -13t11 -31q0 -19 -11 -31.5t-27 -12.5z" />
    <glyph glyph-name="Alpha" unicode="&#x391;" horiz-adv-x="533" 
d="M5 0l236 667h48l239 -667h-55l-78 214h-260l-76 -214h-54zM151 264h228l-115 340z" />
    <glyph glyph-name="Beta" unicode="&#x392;" horiz-adv-x="500" 
d="M76 0v667h172q91 0 138 -40.5t47 -125.5q0 -123 -122 -147q69 -5 111.5 -46t42.5 -123q0 -44 -13.5 -78t-37.5 -58t-57 -36.5t-73 -12.5h-208zM128 49h152q63 0 96.5 37t33.5 100q0 75 -43.5 108t-121.5 33h-117v-278zM128 376h114q67 0 102 34.5t35 91.5q0 60 -33.5 88
t-96.5 28h-121v-242z" />
    <glyph glyph-name="Gamma" unicode="&#x393;" horiz-adv-x="454" 
d="M76 0v667h344v-49h-292v-618h-52z" />
    <glyph glyph-name="uni0394" unicode="&#x394;" horiz-adv-x="541" 
d="M5 0l242 667h42l247 -667h-531zM77 48h386l-196 543z" />
    <glyph glyph-name="Epsilon" unicode="&#x395;" horiz-adv-x="476" 
d="M76 0v667h356v-49h-305v-240h221v-49h-221v-280h323v-49h-374z" />
    <glyph glyph-name="Zeta" unicode="&#x396;" horiz-adv-x="518" 
d="M42 0v51l372 567h-356v49h408v-55l-369 -563h383v-49h-438z" />
    <glyph glyph-name="Eta" unicode="&#x397;" horiz-adv-x="601" 
d="M76 0v667h52v-297h346v297h51v-667h-51v320h-346v-320h-52z" />
    <glyph glyph-name="Theta" unicode="&#x398;" horiz-adv-x="617" 
d="M310 -13q-66 0 -115 29.5t-81.5 78t-48 111t-15.5 128.5q0 103 37 188.5t97.5 121.5t125.5 36q93 0 153.5 -59.5t82 -140.5t21.5 -146t-15.5 -127.5t-47 -111t-80 -78.5t-114.5 -30zM310 36q72 0 122 53t65.5 122t15.5 123q0 87 -27.5 157.5t-75.5 105t-100 34.5
q-54 0 -93 -26t-64 -68.5t-37 -95.5t-12 -107q0 -86 29.5 -161t76.5 -106t100 -31zM184 311v49h251v-49h-251z" />
    <glyph glyph-name="Iota" unicode="&#x399;" horiz-adv-x="204" 
d="M76 0v667h52v-667h-52z" />
    <glyph glyph-name="Kappa" unicode="&#x39a;" horiz-adv-x="516" 
d="M76 0v667h52v-324l301 324h63l-243 -263l262 -404h-62l-236 364l-85 -88v-276h-52z" />
    <glyph glyph-name="Lambda" unicode="&#x39b;" horiz-adv-x="516" 
d="M5 0l231 667h40l235 -667h-55l-201 580l-195 -580h-55z" />
    <glyph glyph-name="Mu" unicode="&#x39c;" horiz-adv-x="699" 
d="M333 -11l-192 580l-24 -569h-51l30 667h67l198 -605l184 605h64l30 -667h-50l-25 561l-178 -572h-53z" />
    <glyph glyph-name="Nu" unicode="&#x39d;" horiz-adv-x="613" 
d="M76 0v667h55l355 -562v562h51v-667h-45l-365 575v-575h-51z" />
    <glyph glyph-name="Xi" unicode="&#x39e;" horiz-adv-x="523" 
d="M62 0v49h399v-49h-399zM114 325v48h295v-48h-295zM67 618v49h387v-49h-387z" />
    <glyph glyph-name="Omicron" unicode="&#x39f;" horiz-adv-x="617" 
d="M309 -13q-66 0 -114.5 29.5t-81 78t-48 111t-15.5 128.5q0 104 37 188q24 59 65 96.5t83 49.5t74 12q94 0 154 -60q61 -58 82 -140q22 -79 22 -146q0 -108 -37 -191q-26 -60 -67 -96.5t-81 -48t-73 -11.5zM309 36q72 0 121.5 52.5t66.5 122.5q16 67 16 123q0 88 -28 157
q-27 72 -74.5 106t-101.5 34q-53 0 -92 -26t-64 -68.5t-37 -95.5t-12 -107q0 -85 29 -161q30 -75 77 -106t99 -31z" />
    <glyph glyph-name="Pi" unicode="&#x3a0;" horiz-adv-x="591" 
d="M76 0v667h439v-667h-52v618h-335v-618h-52z" />
    <glyph glyph-name="Rho" unicode="&#x3a1;" horiz-adv-x="504" 
d="M77 0v667h199q95 0 146.5 -49.5t51.5 -145.5q0 -71 -32 -116.5t-78 -60.5t-88 -15h-147v-280h-52zM129 330h147q67 0 105.5 37.5t38.5 103.5q0 69 -36 108t-108 39h-147v-288z" />
    <glyph glyph-name="Sigma" unicode="&#x3a3;" horiz-adv-x="490" 
d="M37 0v51l266 281l-268 283v52h417v-49h-351l270 -286l-269 -283h369v-49h-434z" />
    <glyph glyph-name="Tau" unicode="&#x3a4;" horiz-adv-x="455" 
d="M201 0v618h-196v49h445v-49h-197v-618h-52z" />
    <glyph glyph-name="Upsilon" unicode="&#x3a5;" horiz-adv-x="517" 
d="M229 0v160q0 44 -3 73q-9 79 -47.5 165.5t-85 147t-83.5 92.5l33 39q35 -30 70 -73t65 -93t51.5 -103t28.5 -102q17 77 61 161.5t85.5 133.5t72.5 76l30 -37q-52 -44 -103.5 -122.5t-80 -151.5t-35.5 -127q-7 -49 -7 -96v-143h-52z" />
    <glyph glyph-name="Phi" unicode="&#x3a6;" horiz-adv-x="686" 
d="M320 -11v96q-127 5 -197 69t-70 176q0 85 41.5 144t102 81.5t123.5 25.5v98h52v-98q128 -6 198.5 -70.5t70.5 -175.5q0 -114 -70.5 -179.5t-198.5 -70.5v-96h-52zM320 133v400q-71 -2 -122 -30.5t-71 -75t-20 -96.5q0 -48 16 -85t44.5 -62t67.5 -38t85 -13zM372 133
q73 1 123 29t71 74.5t21 97.5q0 69 -34 116.5t-85 65t-96 17.5v-400z" />
    <glyph glyph-name="Chi" unicode="&#x3a7;" horiz-adv-x="465" 
d="M5 0l196 346l-179 321h61l149 -277l150 277h59l-179 -318l198 -349h-63l-166 306l-168 -306h-58z" />
    <glyph glyph-name="Psi" unicode="&#x3a8;" horiz-adv-x="674" 
d="M312 0v202q-129 5 -190.5 66t-61.5 191v208h52v-209q0 -57 15 -96.5t41.5 -64t63 -35t80.5 -10.5v415h52v-415q62 0 109 25t67.5 73t20.5 103v214h53v-214q0 -115 -61.5 -179.5t-188.5 -71.5v-202h-52z" />
    <glyph glyph-name="uni03A9" unicode="&#x3a9;" horiz-adv-x="657" 
d="M56 0v49h143q-27 22 -52 50.5t-44.5 65.5t-31 83.5t-11.5 103.5q0 105 39.5 184t102 112t127.5 33q96 0 159 -52.5t86 -128t23 -143.5q0 -83 -24.5 -148t-56.5 -102t-58 -58h143v-49h-211v51q67 45 109.5 121.5t42.5 180.5q0 93 -30.5 159t-80.5 93t-101 27
q-52 0 -92.5 -21.5t-68 -59.5t-41.5 -89t-14 -111q0 -52 12 -96.5t32.5 -82t48.5 -68t61 -53.5v-51h-212z" />
    <glyph glyph-name="Iotadieresis" unicode="&#x3aa;" horiz-adv-x="206" 
d="M76 0v667h52v-667h-52zM19 767q-15 0 -25.5 10t-10.5 30q0 18 10.5 28.5t25.5 10.5t25.5 -10.5t10.5 -28.5q0 -20 -10.5 -30t-25.5 -10zM190 767q-15 0 -25.5 10t-10.5 30q0 18 10.5 28.5t25.5 10.5t25.5 -10.5t10.5 -28.5q0 -20 -10.5 -30t-25.5 -10z" />
    <glyph glyph-name="Upsilondieresis" unicode="&#x3ab;" horiz-adv-x="517" 
d="M176 752q-15 0 -25.5 10t-10.5 30q0 18 10.5 28.5t25.5 10.5t25.5 -10.5t10.5 -28.5q0 -20 -10.5 -30t-25.5 -10zM347 752q-15 0 -25.5 10t-10.5 30q0 18 10.5 28.5t25.5 10.5t25.5 -10.5t10.5 -28.5q0 -20 -10.5 -30t-25.5 -10zM229 0v160q0 44 -3 73q-9 79 -47.5 165.5
t-85 147t-83.5 92.5l33 39q35 -30 70 -73t65 -93t51.5 -103t28.5 -102q17 77 61 161.5t85.5 133.5t72.5 76l30 -37q-52 -44 -103.5 -122.5t-80 -151.5t-35.5 -127q-7 -49 -7 -96v-143h-52z" />
    <glyph glyph-name="alphatonos" unicode="&#x3ac;" horiz-adv-x="521" 
d="M226 -11q-38 0 -71 15t-57 45t-38 74.5t-14 103.5q0 82 27 145t71.5 90t95.5 27q61 0 98.5 -33t53.5 -80q3 29 11.5 60t19.5 53l49 -14q-24 -57 -37 -124.5t-15 -155.5q0 -68 5 -96q5 -32 17.5 -48t37.5 -16q9 0 21 3l4 -45q-16 -4 -32 -4q-42 0 -66.5 25t-27.5 71
q-10 -20 -23 -35q-29 -32 -60.5 -46.5t-69.5 -14.5zM233 33q34 0 63 16.5t46 44.5t23 59q10 54 10 103q0 51 -14.5 94t-47.5 67t-74 24q-48 0 -82 -31t-47.5 -81t-13.5 -104q0 -95 39 -143.5t98 -48.5zM265 539l-35 16l94 194l52 -21z" />
    <glyph glyph-name="epsilontonos" unicode="&#x3ad;" horiz-adv-x="446" 
d="M244 -11q-87 0 -137 34t-50 106q0 46 29 78t73 43q-41 11 -65 40t-24 69q0 60 44 95t119 35q57 0 95 -15t65 -32l-16 -43q-35 23 -71.5 34.5t-68.5 11.5q-59 0 -87.5 -24.5t-28.5 -63.5q0 -32 21 -53t59 -28q30 -4 96 -4h44v-43h-52q-43 0 -85 -3q-45 -4 -71 -30.5
t-26 -62.5q0 -45 33 -73t109 -28q41 0 76 11t65 26l8 -44q-27 -17 -70 -26.5t-84 -9.5zM238 539l-35 16l94 194l52 -21z" />
    <glyph glyph-name="etatonos" unicode="&#x3ae;" horiz-adv-x="512" 
d="M401 -189v483q0 33 -4 59q-7 41 -35 65.5t-86 24.5q-51 0 -90 -23.5t-51 -58.5q-7 -19 -7 -66v-295h-49v389q0 26 -8 37t-23 11q-12 0 -29 -6l-9 43q22 11 50 11q55 0 66 -65q57 69 160 69q77 0 117 -37t45 -99q2 -17 2 -61v-481h-49zM267 539l-35 16l94 194l52 -21z" />
    <glyph glyph-name="iotatonos" unicode="&#x3af;" horiz-adv-x="235" 
d="M164 -11q-43 0 -68 24.5t-25 80.5v382h50v-374q0 -36 12 -52t38 -16t53 12l8 -43q-31 -14 -68 -14zM88 539l-35 16l94 194l52 -21z" />
    <glyph glyph-name="upsilondieresistonos" unicode="&#x3b0;" horiz-adv-x="496" 
d="M244 -11q-81 0 -122 47.5t-41 147.5v162q0 33 -1 43q-1 47 -37 47q-9 0 -26 -5l-7 45q24 9 44 9q39 0 54 -22.5t20 -63.5q2 -20 2 -77v-132q0 -76 27.5 -116t91.5 -40q60 0 89.5 37.5t36.5 100.5q5 43 5 106q0 70 -19 196l51 7q17 -109 17 -202q0 -77 -4 -109
q-11 -83 -53 -132t-128 -49zM196 539l-35 16l94 194l52 -21zM117 601q-16 0 -27.5 12.5t-11.5 31.5q0 18 11.5 31t27.5 13t27 -13t11 -31q0 -19 -11 -31.5t-27 -12.5zM349 601q-16 0 -27.5 12.5t-11.5 31.5q0 18 11.5 31t27.5 13t27 -13t11 -31q0 -19 -11 -31.5t-27 -12.5z
" />
    <glyph glyph-name="alpha" unicode="&#x3b1;" horiz-adv-x="521" 
d="M226 -11q-38 0 -71 15t-57 45t-38 74.5t-14 103.5q0 82 27 145t71.5 90t95.5 27q61 0 98.5 -33t53.5 -80q3 29 11.5 60t19.5 53l49 -14q-24 -57 -37 -124.5t-15 -155.5q0 -68 5 -96q5 -32 17.5 -48t37.5 -16q9 0 21 3l4 -45q-16 -4 -32 -4q-42 0 -66.5 25t-27.5 71
q-10 -20 -23 -35q-29 -32 -60.5 -46.5t-69.5 -14.5zM233 33q34 0 63 16.5t46 44.5t23 59q10 54 10 103q0 51 -14.5 94t-47.5 67t-74 24q-48 0 -82 -31t-47.5 -81t-13.5 -104q0 -95 39 -143.5t98 -48.5z" />
    <glyph glyph-name="beta" unicode="&#x3b2;" horiz-adv-x="542" 
d="M76 -190v646q0 29 4 59q7 48 31 84.5t66 56.5t91 20q58 0 98.5 -25t55.5 -60.5t15 -69.5q0 -46 -23 -81t-51 -52t-46 -22q49 -5 92 -30.5t64 -68t21 -88.5q0 -63 -28 -109.5t-71.5 -63.5t-87.5 -17q-48 0 -88.5 17t-63 35.5t-32.5 30.5v-262h-47zM306 33q45 0 78 19
t46.5 53.5t13.5 74.5q0 53 -31.5 92t-76.5 53t-88 14v47q40 0 75 19t50.5 48.5t15.5 62.5q0 36 -16.5 63t-46 40t-60.5 13q-38 0 -63 -12.5t-41 -32t-23.5 -43.5t-10.5 -46q-3 -18 -3 -51v-323q24 -27 55 -49t62.5 -32t63.5 -10z" />
    <glyph glyph-name="gamma" unicode="&#x3b3;" horiz-adv-x="456" 
d="M194 -189v51q0 91 -9 148q-13 77 -50 202t-67 183q-11 23 -22 31t-25 8q-13 0 -34 -8l-9 46q11 5 26 8.5t28 3.5q26 0 45 -15t36 -47q25 -49 61 -168t49 -217q16 92 67.5 230.5t94.5 215.5l41 -17q-36 -71 -71.5 -160.5t-59 -160t-33.5 -115.5q-14 -63 -16 -98
q-2 -24 -2 -76v-45h-50z" />
    <glyph glyph-name="delta" unicode="&#x3b4;" horiz-adv-x="502" 
d="M248 -11q-50 0 -87.5 20t-62.5 53.5t-37.5 76.5t-12.5 90q0 72 31.5 130.5t85.5 86.5t112 30q-83 77 -160 133l18 58h300v-44h-258q100 -77 193 -169q47 -50 68 -102t21 -119q0 -72 -28.5 -131.5t-77 -86t-105.5 -26.5zM252 35q56 0 94 33t50.5 78t12.5 86
q0 69 -24.5 115.5t-70.5 93.5q-26 0 -54 -5q-55 -10 -93.5 -42.5t-53.5 -76.5t-15 -87q0 -44 11.5 -80t31.5 -61.5t48.5 -39.5t62.5 -14z" />
    <glyph glyph-name="epsilon" unicode="&#x3b5;" horiz-adv-x="446" 
d="M244 -11q-87 0 -137 34t-50 106q0 46 29 78t73 43q-41 11 -65 40t-24 69q0 60 44 95t119 35q57 0 95 -15t65 -32l-16 -43q-35 23 -71.5 34.5t-68.5 11.5q-59 0 -87.5 -24.5t-28.5 -63.5q0 -32 21 -53t59 -28q30 -4 96 -4h44v-43h-52q-43 0 -85 -3q-45 -4 -71 -30.5
t-26 -62.5q0 -45 33 -73t109 -28q41 0 76 11t65 26l8 -44q-27 -17 -70 -26.5t-84 -9.5z" />
    <glyph glyph-name="zeta" unicode="&#x3b6;" horiz-adv-x="412" 
d="M345 -174l-38 12q9 26 21 73.5t16 77.5q-40 0 -54 1q-51 2 -95.5 12t-77.5 32t-51.5 58.5t-18.5 92.5q0 75 31 151.5t75.5 131t87.5 94.5q46 41 77 62q-29 -3 -80 -3q-97 0 -154 10l9 42q75 -9 164 -9q69 0 139 6v-39q-36 -22 -72 -51t-66 -59q-80 -81 -120.5 -167
t-40.5 -165q0 -82 52.5 -119t164.5 -37h44q17 0 26 -6.5t9 -26.5q0 -46 -48 -174z" />
    <glyph glyph-name="eta" unicode="&#x3b7;" horiz-adv-x="512" 
d="M401 -189v483q0 33 -4 59q-7 41 -35 65.5t-86 24.5q-51 0 -90 -23.5t-51 -58.5q-7 -19 -7 -66v-295h-49v389q0 26 -8 37t-23 11q-12 0 -29 -6l-9 43q22 11 50 11q55 0 66 -65q57 69 160 69q77 0 117 -37t45 -99q2 -17 2 -61v-481h-49z" />
    <glyph glyph-name="theta" unicode="&#x3b8;" horiz-adv-x="485" 
d="M241 -11q-60 0 -98 30t-59.5 78t-30 109t-8.5 124q0 107 24.5 189t71.5 119.5t106 37.5q80 0 124.5 -58.5t55.5 -139.5t11 -143q0 -112 -22.5 -193.5t-69 -117t-105.5 -35.5zM243 34q61 0 95 48.5t43 112.5t9 117h-294q0 -92 18 -158t52.5 -93t76.5 -27zM96 354h294
q-2 95 -19 156t-50.5 91.5t-75.5 30.5q-59 0 -93.5 -49t-44.5 -113t-11 -116z" />
    <glyph glyph-name="iota" unicode="&#x3b9;" horiz-adv-x="235" 
d="M164 -11q-43 0 -68 24.5t-25 80.5v382h50v-374q0 -36 12 -52t38 -16t53 12l8 -43q-31 -14 -68 -14z" />
    <glyph glyph-name="kappa" unicode="&#x3ba;" horiz-adv-x="507" 
d="M72 0v476h49v-263q22 28 87 106t97 112q26 28 48.5 38.5t47.5 10.5q14 0 30 -3.5t27 -10.5l-10 -46q-10 5 -24 8.5t-25 3.5q-18 0 -32.5 -7t-31.5 -26.5t-39 -44t-45 -52.5l221 -302h-59l-194 262l-98 -115v-147h-49z" />
    <glyph glyph-name="lambda" unicode="&#x3bb;" horiz-adv-x="466" 
d="M433 -11q-44 0 -65.5 28t-43.5 98l-93 285l-149 -400h-53l179 466l-32 89q-11 32 -26.5 43.5t-39.5 11.5q-18 0 -37.5 -6.5t-30.5 -11.5l-12 42q15 8 40 15.5t47 7.5q42 0 67.5 -22t40.5 -67l159 -468q22 -64 56 -64q8 0 29 6l6 -43q-9 -5 -21 -7.5t-21 -2.5z" />
    <glyph glyph-name="uni03BC" unicode="&#x3bc;" horiz-adv-x="528" 
d="M76 -189v666h50v-301q0 -51 21.5 -85t50.5 -46t57 -12q35 0 65 13t46.5 34t22.5 37q6 21 6 66v294h51v-373q0 -34 6 -52.5t31 -18.5q6 0 28 7l9 -42q-21 -9 -45 -9q-33 0 -52 18t-24 51q-24 -31 -59 -50t-86 -19q-44 0 -78 18t-52 52v-248h-48z" />
    <glyph glyph-name="nu" unicode="&#x3bd;" horiz-adv-x="465" 
d="M197 -7q-19 89 -50 186t-47 143.5t-28 73.5q-11 23 -21 31.5t-24 8.5q-13 0 -34 -7l-9 43q10 5 25 8.5t29 3.5q26 0 44.5 -12.5t32.5 -44.5q20 -44 47 -127t44 -146t28 -111q16 50 35.5 108.5t39 117.5t38 115t34.5 101l45 -16q-47 -129 -161 -476h-68z" />
    <glyph glyph-name="xi" unicode="&#x3be;" horiz-adv-x="422" 
d="M359 -174l-37 12q9 26 21 73.5t16 77.5q-56 1 -112 7t-100 25.5t-71.5 55.5t-27.5 98q0 47 21.5 86t51 59.5t55.5 27.5q-35 8 -61 29t-34.5 44t-8.5 50q0 40 23 74t56 54t68 27v1q-16 -2 -38 -2q-33 0 -70 2t-54 7l10 43q80 -11 173 -11q37 0 143 6v-43q-78 -9 -123 -20
q-48 -12 -81 -34t-44.5 -48t-11.5 -53q0 -44 27.5 -68t73.5 -29q17 -2 81 -2h51v-44q-54 0 -121 -4q-63 -5 -100 -47t-37 -103q0 -76 63 -109.5t197 -35.5h14q17 0 26.5 -6t9.5 -26q0 -27 -15.5 -79t-33.5 -95z" />
    <glyph glyph-name="omicron" unicode="&#x3bf;" horiz-adv-x="494" 
d="M248 -11q-72 0 -121 45q-44 39 -62 94.5t-18 113.5q0 75 29 135t75 86t97 26q70 0 120 -44q33 -30 52 -68t25 -75.5t6 -61.5q0 -79 -31 -140q-30 -60 -75.5 -85.5t-96.5 -25.5zM248 33q43 -2 72.5 20.5t48 55t26 69t6.5 63.5q1 66 -25 116t-62.5 70t-65.5 18
q-55 0 -93 -38q-35 -35 -47 -78t-12 -87q0 -39 9.5 -76.5t28.5 -67t47.5 -47.5t66.5 -18z" />
    <glyph glyph-name="pi" unicode="&#x3c0;" horiz-adv-x="604" 
d="M504 -11q-40 0 -62.5 23.5t-22.5 77.5v343h-219v-433h-48v433h-10q-17 0 -35 -6q-17 -7 -29.5 -18t-24.5 -29l-34 24q14 24 31 39t38 24q23 10 72 10h406v-44h-99v-332q0 -36 10.5 -52t35.5 -16q18 0 41 9l6 -42q-25 -11 -56 -11z" />
    <glyph glyph-name="rho" unicode="&#x3c1;" horiz-adv-x="510" 
d="M71 -189v423q0 41 1 55q3 63 32 111.5t73 68.5t90 20q76 0 122.5 -42.5t60 -99t13.5 -99.5q0 -77 -28 -141.5t-73.5 -91t-94.5 -26.5q-52 0 -89 22.5t-59 51.5v-252h-48zM267 33q47 0 82.5 32t49.5 80t14 93q0 62 -17.5 109.5t-52 72.5t-76.5 25q-51 0 -84 -26t-45 -60.5
t-15 -61.5q-2 -14 -2 -56v-122q22 -28 44 -47t49.5 -29t52.5 -10z" />
    <glyph glyph-name="sigma1" unicode="&#x3c2;" horiz-adv-x="430" 
d="M329 -169l-39 11q27 81 36 149q-60 1 -111.5 16.5t-90 46.5t-60.5 78t-22 112q0 86 33 144.5t79 79.5t87 21q42 0 73.5 -16t54.5 -40t33 -43l-37 -26q-23 36 -51 58.5t-74 22.5q-49 0 -86.5 -33t-49.5 -77t-12 -86q0 -98 61.5 -154.5t188.5 -59.5q17 0 25 -6.5t8 -25.5
q0 -29 -13 -73.5t-33 -98.5z" />
    <glyph glyph-name="sigma" unicode="&#x3c3;" horiz-adv-x="543" 
d="M244 -11q-50 0 -87 21t-61 55t-36 75.5t-12 83.5q0 76 28.5 133.5t77.5 84t99 30.5q67 5 104 5h138v-43h-146q44 -32 71 -85t27 -119q0 -73 -27 -131t-74.5 -84t-101.5 -26zM251 33q49 0 85 31t49 77.5t13 87.5q0 54 -19.5 97t-45 68.5t-42.5 36.5h-7q-84 0 -135 -52.5
t-51 -154.5q0 -41 10.5 -75.5t30.5 -60.5t48.5 -40.5t63.5 -14.5z" />
    <glyph glyph-name="tau" unicode="&#x3c4;" horiz-adv-x="476" 
d="M298 -11q-51 0 -78 25t-27 80v339h-53q-33 0 -55 -8q-15 -6 -27 -18.5t-21 -27.5l-34 25q8 15 22 32t39 28q27 13 81 13h279v-44h-182v-326q0 -41 15 -57t50 -16q27 0 66 16l7 -44q-14 -6 -36.5 -11.5t-45.5 -5.5z" />
    <glyph glyph-name="upsilon" unicode="&#x3c5;" horiz-adv-x="496" 
d="M244 -11q-81 0 -122 47.5t-41 147.5v162q0 33 -1 43q-1 47 -37 47q-9 0 -26 -5l-7 45q24 9 44 9q39 0 54 -22.5t20 -63.5q2 -20 2 -77v-132q0 -76 27.5 -116t91.5 -40q60 0 89.5 37.5t36.5 100.5q5 43 5 106q0 70 -19 196l51 7q17 -109 17 -202q0 -77 -4 -109
q-11 -83 -53 -132t-128 -49z" />
    <glyph glyph-name="phi" unicode="&#x3c6;" horiz-adv-x="617" 
d="M285 -189v189q-50 1 -93 17.5t-75 46.5t-50.5 73.5t-18.5 99.5q0 79 38 135.5t94.5 79t104.5 25.5v190h48v-190q78 -2 136 -37t81.5 -87.5t23.5 -108.5q0 -59 -20.5 -104t-54.5 -76t-77 -47t-89 -17v-189h-48zM285 44v389q-66 -4 -110 -34t-61 -74t-17 -89
q0 -44 14.5 -79t40 -60t60 -38.5t73.5 -14.5zM333 44q61 1 108 32t65 75.5t18 89.5q0 67 -30.5 111t-75 62t-85.5 19v-389z" />
    <glyph glyph-name="chi" unicode="&#x3c7;" horiz-adv-x="465" 
d="M76 -198l-44 18l169 333l-84 212q-30 75 -63 75q-17 0 -30 -6l-10 45q22 10 47 10q37 0 56 -22.5t46 -85.5l71 -180l138 285l45 -19l-158 -315l110 -236q14 -30 29.5 -47.5t39.5 -17.5q19 0 33 7l8 -43q-22 -10 -46 -10q-47 0 -73 30.5t-48 81.5l-83 185z" />
    <glyph glyph-name="psi" unicode="&#x3c8;" horiz-adv-x="662" 
d="M303 -190v180q-125 7 -181.5 67t-56.5 178v121q0 72 -8 119l49 7q8 -38 8 -125v-117q0 -103 48.5 -152t140.5 -51v569h49v-569q81 2 124 32t58 86q11 40 11 121q0 52 -4.5 99t-13.5 101l50 7q19 -122 19 -206q0 -66 -10 -116q-16 -75 -71 -121t-163 -51v-179h-49z" />
    <glyph glyph-name="omega" unicode="&#x3c9;" horiz-adv-x="723" 
d="M219 -11q-44 0 -75.5 17.5t-52 47.5t-30 71t-9.5 88q0 57 16.5 118t32.5 95.5t30 54.5l44 -15q-20 -32 -39 -80.5t-26.5 -92t-7.5 -79.5q0 -47 9.5 -81t26 -55.5t38.5 -31.5t48 -10q52 0 78.5 44.5t31.5 116.5q3 24 3 101v50h50v-41q0 -80 3 -113q3 -32 10.5 -61t21 -51
t33.5 -34.5t49 -12.5q57 0 89 52.5t32 139.5q0 60 -21.5 128.5t-54.5 110.5l44 14q22 -28 43 -75t30 -92t9 -76q0 -78 -23 -139t-64 -85t-84 -24t-74.5 21t-46.5 50t-21 55q-14 -43 -35 -71t-50 -41.5t-58 -13.5z" />
    <glyph glyph-name="iotadieresis" unicode="&#x3ca;" horiz-adv-x="235" 
d="M164 -11q-43 0 -68 24.5t-25 80.5v382h50v-374q0 -36 12 -52t38 -16t53 12l8 -43q-31 -14 -68 -14zM14 601q-15 0 -25.5 10t-10.5 30q0 18 10.5 28.5t25.5 10.5t25.5 -10.5t10.5 -28.5q0 -20 -10.5 -30t-25.5 -10zM185 601q-15 0 -25.5 10t-10.5 30q0 18 10.5 28.5
t25.5 10.5t25.5 -10.5t10.5 -28.5q0 -20 -10.5 -30t-25.5 -10z" />
    <glyph glyph-name="upsilondieresis" unicode="&#x3cb;" horiz-adv-x="496" 
d="M244 -11q-81 0 -122 47.5t-41 147.5v162q0 33 -1 43q-1 47 -37 47q-9 0 -26 -5l-7 45q24 9 44 9q39 0 54 -22.5t20 -63.5q2 -20 2 -77v-132q0 -76 27.5 -116t91.5 -40q60 0 89.5 37.5t36.5 100.5q5 43 5 106q0 70 -19 196l51 7q17 -109 17 -202q0 -77 -4 -109
q-11 -83 -53 -132t-128 -49zM154 601q-15 0 -25.5 10t-10.5 30q0 18 10.5 28.5t25.5 10.5t25.5 -10.5t10.5 -28.5q0 -20 -10.5 -30t-25.5 -10zM325 601q-15 0 -25.5 10t-10.5 30q0 18 10.5 28.5t25.5 10.5t25.5 -10.5t10.5 -28.5q0 -20 -10.5 -30t-25.5 -10z" />
    <glyph glyph-name="omicrontonos" unicode="&#x3cc;" horiz-adv-x="494" 
d="M243 539l-35 16l94 194l52 -21zM248 -11q-72 0 -121 45q-44 39 -62 94.5t-18 113.5q0 75 29 135t75 86t97 26q70 0 120 -44q33 -30 52 -68t25 -75.5t6 -61.5q0 -79 -31 -140q-30 -60 -75.5 -85.5t-96.5 -25.5zM248 33q43 -2 72.5 20.5t48 55t26 69t6.5 63.5q1 66 -25 116
t-62.5 70t-65.5 18q-55 0 -93 -38q-35 -35 -47 -78t-12 -87q0 -39 9.5 -76.5t28.5 -67t47.5 -47.5t66.5 -18z" />
    <glyph glyph-name="upsilontonos" unicode="&#x3cd;" horiz-adv-x="496" 
d="M244 -11q-81 0 -122 47.5t-41 147.5v162q0 33 -1 43q-1 47 -37 47q-9 0 -26 -5l-7 45q24 9 44 9q39 0 54 -22.5t20 -63.5q2 -20 2 -77v-132q0 -76 27.5 -116t91.5 -40q60 0 89.5 37.5t36.5 100.5q5 43 5 106q0 70 -19 196l51 7q17 -109 17 -202q0 -77 -4 -109
q-11 -83 -53 -132t-128 -49zM247 539l-35 16l94 194l52 -21z" />
    <glyph glyph-name="omegatonos" unicode="&#x3ce;" horiz-adv-x="723" 
d="M219 -11q-44 0 -75.5 17.5t-52 47.5t-30 71t-9.5 88q0 57 16.5 118t32.5 95.5t30 54.5l44 -15q-20 -32 -39 -80.5t-26.5 -92t-7.5 -79.5q0 -47 9.5 -81t26 -55.5t38.5 -31.5t48 -10q52 0 78.5 44.5t31.5 116.5q3 24 3 101v50h50v-41q0 -80 3 -113q3 -32 10.5 -61t21 -51
t33.5 -34.5t49 -12.5q57 0 89 52.5t32 139.5q0 60 -21.5 128.5t-54.5 110.5l44 14q22 -28 43 -75t30 -92t9 -76q0 -78 -23 -139t-64 -85t-84 -24t-74.5 21t-46.5 50t-21 55q-14 -43 -35 -71t-50 -41.5t-58 -13.5zM366 539l-35 16l94 194l52 -21z" />
    <glyph glyph-name="afii10023" unicode="&#x401;" horiz-adv-x="476" 
d="M82 0v667h356v-49h-305v-240h221v-49h-221v-280h323v-49h-374zM170 767q-15 0 -25.5 10t-10.5 30q0 18 10.5 28.5t25.5 10.5t25.5 -10.5t10.5 -28.5q0 -20 -10.5 -30t-25.5 -10zM341 767q-15 0 -25.5 10t-10.5 30q0 18 10.5 28.5t25.5 10.5t25.5 -10.5t10.5 -28.5
q0 -20 -10.5 -30t-25.5 -10z" />
    <glyph glyph-name="afii10051" unicode="&#x402;" horiz-adv-x="635" 
d="M167 0v618h-161v49h386v-49h-173v-230q28 20 55.5 33.5t57.5 21t56 7.5q69 0 118 -32.5t68 -85.5t19 -108q0 -57 -16 -99t-44 -70t-66.5 -41.5t-84.5 -13.5h-38v49h41q51 0 87 21.5t51.5 63t15.5 88.5q0 84 -41 130.5t-113 46.5q-39 0 -73 -13t-58 -28.5t-35 -25.5v-332
h-52z" />
    <glyph glyph-name="afii10052" unicode="&#x403;" horiz-adv-x="445" 
d="M82 -1v668h354v-50h-302v-617zM195 710l-24 29l155 139l37 -41z" />
    <glyph glyph-name="afii10053" unicode="&#x404;" horiz-adv-x="537" 
d="M312 -12q-62 0 -110.5 24t-82.5 68.5t-51.5 108t-17.5 142.5q0 85 19.5 150t54 109t81.5 67t102 23q70 0 122 -35.5t79 -92.5l-42 -33q-21 42 -53.5 68t-59.5 35t-45 8q-69 0 -117.5 -44t-67.5 -114q-14 -55 -17 -108h242v-49h-243q2 -66 14 -104q23 -81 73 -127.5
t124 -46.5q54 0 94 23t72 65l30 -42q-18 -23 -50 -48q-27 -21 -68.5 -34t-81.5 -13z" />
    <glyph glyph-name="afii10054" unicode="&#x405;" horiz-adv-x="483" 
d="M236 -13q-48 2 -98.5 20t-74 36.5t-31.5 28.5l29 48q14 -15 33.5 -30t43 -27t49 -19.5t51.5 -7.5q37 -2 65 9t46.5 29.5t28 43t8.5 50.5q0 30 -8.5 53t-25.5 41t-43.5 32.5t-62.5 28.5l-68 24q-60 21 -95.5 60.5t-35.5 102.5q0 55 30 96q31 42 75.5 58t86.5 16
q70 0 118.5 -26t77.5 -62l-32 -39q-24 30 -63.5 54t-96.5 24q-60 1 -99 -28.5t-40 -86.5q0 -48 28.5 -76t74.5 -45l65 -24q48 -17 80.5 -37t51.5 -44t27.5 -52.5t8.5 -61.5q0 -94 -55 -141.5t-149 -47.5z" />
    <glyph glyph-name="uni0406" unicode="&#x406;" horiz-adv-x="216" 
d="M82 0v667h52v-667h-52z" />
    <glyph glyph-name="afii10056" unicode="&#x407;" horiz-adv-x="216" 
d="M82 0v667h52v-667h-52zM27 767q-15 0 -25.5 10t-10.5 30q0 18 10.5 28.5t25.5 10.5t25.5 -10.5t10.5 -28.5q0 -20 -10.5 -30t-25.5 -10zM198 767q-15 0 -25.5 10t-10.5 30q0 18 10.5 28.5t25.5 10.5t25.5 -10.5t10.5 -28.5q0 -20 -10.5 -30t-25.5 -10z" />
    <glyph glyph-name="afii10057" unicode="&#x408;" horiz-adv-x="353" 
d="M142 -11q-44 0 -81.5 13t-48.5 25l18 47q7 -10 31.5 -20t43.5 -14t39 -4q51 0 69.5 27t18.5 79v525h52v-533q0 -51 -13 -77q-14 -30 -46 -49t-83 -19z" />
    <glyph glyph-name="afii10058" unicode="&#x409;" horiz-adv-x="884" 
d="M9 0v48h17q28 0 52 14t42 57q34 80 44 193t10 267v88h321v-257h139q100 0 154.5 -53t54.5 -146q0 -47 -14 -86t-41 -67t-66.5 -43t-90.5 -15h-188v618h-217v-80q0 -174 -19 -288.5t-48 -166.5q-26 -46 -58.5 -64.5t-79.5 -18.5h-12zM495 48h134q80 0 120.5 44t40.5 119
q0 72 -42 111.5t-117 39.5h-136v-314z" />
    <glyph glyph-name="afii10059" unicode="&#x40a;" horiz-adv-x="887" 
d="M82 0v667h52v-266h312v266h52v-266h134q102 0 158 -50t56 -145q0 -98 -58.5 -152t-160.5 -54h-181v352h-312v-352h-52zM498 49h129q78 0 121 40.5t43 116.5q0 75 -42.5 110.5t-120.5 35.5h-130v-303z" />
    <glyph glyph-name="afii10060" unicode="&#x40b;" horiz-adv-x="653" 
d="M168 0v618h-162v49h386v-49h-172v-230q30 21 56 34t56 20.5t57 7.5q88 0 137.5 -51t49.5 -158v-241h-53v241q0 55 -17.5 91t-51 51.5t-70.5 15.5q-46 0 -90.5 -21.5t-73.5 -47.5v-330h-52z" />
    <glyph glyph-name="afii10061" unicode="&#x40c;" horiz-adv-x="515" 
d="M82 0v667h52v-303h75q43 0 65.5 34t57.5 138q23 67 50.5 99t80.5 32h39v-49h-30q-38 0 -55 -20.5t-39 -82.5q-25 -73 -45 -112.5t-43 -60.5l212 -342h-60l-199 316h-109v-316h-52zM230 711l-24 29l155 139l37 -41z" />
    <glyph glyph-name="afii10062" unicode="&#x40e;" horiz-adv-x="509" 
d="M86 -6v50h21q33 0 57.5 12t46 45.5t42.5 82.5l-240 483h59l210 -428q35 74 84 197.5t82 230.5h54q-42 -136 -96 -268.5t-114 -250.5q-30 -60 -52.5 -91.5t-53 -47t-64.5 -15.5h-36zM266 715q-27 0 -52.5 8.5t-45.5 25t-32.5 41.5t-13.5 57h47q8 -46 34 -67t64 -21
q35 0 63 20.5t36 67.5h45q-3 -46 -30 -79.5t-58 -43t-57 -9.5z" />
    <glyph glyph-name="afii10145" unicode="&#x40f;" horiz-adv-x="607" 
d="M281 -144v144h-199v667h53v-618h338v618h52v-667h-192v-144h-52z" />
    <glyph glyph-name="afii10017" unicode="&#x410;" horiz-adv-x="527" 
d="M5 0l236 667h48l239 -667h-55l-78 214h-260l-76 -214h-54zM151 264h228l-115 340z" />
    <glyph glyph-name="afii10018" unicode="&#x411;" horiz-adv-x="517" 
d="M82 0v667h327v-49h-275v-227h130q103 0 157.5 -49t54.5 -144q0 -98 -52.5 -148t-158.5 -50h-183zM134 49h135q75 0 114 35t39 112q0 76 -42.5 111.5t-109.5 35.5h-136v-294z" />
    <glyph glyph-name="afii10019" unicode="&#x412;" horiz-adv-x="512" 
d="M84 0v667h172q91 0 138 -40.5t47 -125.5q0 -123 -122 -147q69 -5 111.5 -46t42.5 -123q0 -44 -13.5 -78t-37.5 -58t-57 -36.5t-73 -12.5h-208zM136 49h152q63 0 96.5 37t33.5 100q0 75 -43.5 108t-121.5 33h-117v-278zM136 376h114q67 0 102 34.5t35 91.5q0 60 -33.5 88
t-96.5 28h-121v-242z" />
    <glyph glyph-name="afii10020" unicode="&#x413;" horiz-adv-x="445" 
d="M82 -1v668h354v-50h-302v-617z" />
    <glyph glyph-name="afii10021" unicode="&#x414;" horiz-adv-x="578" 
d="M4 -144v192h36q41 64 69 138t45 149q12 53 21 146.5t9 185.5h308v-619h52v-192h-52v144h-436v-144h-52zM100 48h340v571h-205q0 -58 -9.5 -150t-22.5 -148q-17 -74 -45.5 -148t-57.5 -125z" />
    <glyph glyph-name="afii10022" unicode="&#x415;" horiz-adv-x="476" 
d="M82 0v667h356v-49h-305v-240h221v-49h-221v-280h323v-49h-374z" />
    <glyph glyph-name="afii10024" unicode="&#x416;" horiz-adv-x="745" 
d="M24 0l198 334q-29 21 -47.5 61.5t-42.5 106.5q-28 71 -38 88t-27.5 22.5t-37.5 5.5h-13v49h32q51 0 74.5 -28.5t60.5 -125.5q29 -79 45.5 -108.5t33.5 -38.5t38 -9h48v310h49v-310h47q40 0 61.5 31.5t57.5 124.5q25 68 43.5 102t41 43t50.5 9h30v-49h-15q-44 0 -59 -18.5
t-43 -97.5q-25 -69 -45 -108.5t-43 -56.5l197 -337h-61l-179 308h-83v-308h-49v308h-82l-183 -308h-59z" />
    <glyph glyph-name="afii10025" unicode="&#x417;" horiz-adv-x="497" 
d="M234 -12q-63 0 -118 22.5t-93 69.5l33 46q32 -41 74.5 -65t109.5 -24q73 0 116 36t43 108q0 48 -23.5 78.5t-56.5 43.5t-64.5 15t-86.5 2v47h41q30 0 58 5q35 7 65 29t42 49.5t12 54.5q0 62 -36.5 94t-107.5 32q-44 0 -77 -15t-53 -38t-33 -43l-35 30q29 57 84 86t117 29
q95 0 145.5 -45.5t50.5 -122.5q0 -49 -22 -86.5t-51.5 -55t-54.5 -24.5q69 -7 105 -52t36 -114q0 -94 -59.5 -143t-160.5 -49z" />
    <glyph glyph-name="afii10026" unicode="&#x418;" horiz-adv-x="620" 
d="M82 0v667h52v-521l352 451v70h52v-667h-52v520l-352 -451v-69h-52z" />
    <glyph glyph-name="afii10027" unicode="&#x419;" horiz-adv-x="620" 
d="M82 0v667h52v-521l352 451v70h52v-667h-52v520l-352 -451v-69h-52zM317 719q-27 0 -52.5 8.5t-45.5 25t-32.5 41.5t-13.5 57h47q8 -46 34 -67t64 -21q35 0 63 20.5t36 67.5h45q-3 -46 -30 -79.5t-58 -43t-57 -9.5z" />
    <glyph glyph-name="afii10028" unicode="&#x41a;" horiz-adv-x="515" 
d="M82 0v667h52v-303h75q43 0 65.5 34t57.5 138q23 67 50.5 99t80.5 32h39v-49h-30q-38 0 -55 -20.5t-39 -82.5q-25 -73 -45 -112.5t-43 -60.5l212 -342h-60l-199 316h-109v-316h-52z" />
    <glyph glyph-name="afii10029" unicode="&#x41b;" horiz-adv-x="600" 
d="M9 0v48h7q29 0 49 7t34.5 22.5t24.5 38.5q27 63 41.5 159t14.5 219v173h336v-667h-52v619h-233v-95q0 -160 -19 -274.5t-48 -166.5q-25 -46 -58.5 -64.5t-79.5 -18.5h-17z" />
    <glyph glyph-name="afii10030" unicode="&#x41c;" horiz-adv-x="705" 
d="M333 -11l-192 580l-24 -569h-51l30 667h67l198 -605l184 605h64l30 -667h-50l-25 561l-178 -572h-53z" />
    <glyph glyph-name="afii10031" unicode="&#x41d;" horiz-adv-x="613" 
d="M82 0v667h52v-297h346v297h51v-667h-51v320h-346v-320h-52z" />
    <glyph glyph-name="afii10032" unicode="&#x41e;" horiz-adv-x="617" 
d="M309 -13q-66 0 -114.5 29.5t-81 78t-48 111t-15.5 128.5q0 104 37 188q24 59 65 96.5t83 49.5t74 12q94 0 154 -60q61 -58 82 -140q22 -79 22 -146q0 -108 -37 -191q-26 -60 -67 -96.5t-81 -48t-73 -11.5zM309 36q72 0 121.5 52.5t66.5 122.5q16 67 16 123q0 88 -28 157
q-27 72 -74.5 106t-101.5 34q-53 0 -92 -26t-64 -68.5t-37 -95.5t-12 -107q0 -85 29 -161q30 -75 77 -106t99 -31z" />
    <glyph glyph-name="afii10033" unicode="&#x41f;" horiz-adv-x="612" 
d="M82 0v667h448v-667h-52v618h-344v-618h-52z" />
    <glyph glyph-name="afii10034" unicode="&#x420;" horiz-adv-x="507" 
d="M83 0v667h198q96 0 148 -49.5t52 -146.5q0 -47 -15 -83t-41.5 -60t-63 -36t-79.5 -12h-147v-280h-52zM135 329h147q66 0 104.5 38.5t38.5 104.5q0 69 -35.5 107.5t-108.5 38.5h-146v-289z" />
    <glyph glyph-name="afii10035" unicode="&#x421;" horiz-adv-x="537" 
d="M312 -12q-62 0 -110.5 24t-82.5 68.5t-51.5 108t-17.5 142.5q0 85 19.5 150t54 109t81.5 67t102 23q70 0 122 -35.5t79 -92.5l-42 -33q-21 42 -53.5 68t-59.5 35t-45 8q-68 0 -118 -44q-47 -43 -67 -114q-18 -69 -18 -142q0 -65 14.5 -119t42 -92.5t66.5 -60t88 -21.5
q54 0 94 23t72 65l30 -42q-18 -23 -50 -48q-27 -21 -68.5 -34t-81.5 -13z" />
    <glyph glyph-name="afii10036" unicode="&#x422;" horiz-adv-x="462" 
d="M204 0v618h-196v49h445v-49h-197v-618h-52z" />
    <glyph glyph-name="afii10037" unicode="&#x423;" horiz-adv-x="509" 
d="M86 -6v50h21q33 0 57.5 12t46 45.5t42.5 82.5l-240 483h59l210 -428q35 74 84 197.5t82 230.5h54q-42 -136 -96 -268.5t-114 -250.5q-30 -60 -52.5 -91.5t-53 -47t-64.5 -15.5h-36z" />
    <glyph glyph-name="afii10038" unicode="&#x424;" horiz-adv-x="675" 
d="M311 -11v96q-129 5 -197.5 70t-68.5 178q0 114 69 178t197 70v98h52v-98q131 -6 199.5 -70t68.5 -178q0 -112 -68.5 -177.5t-199.5 -70.5v-96h-52zM311 133v400q-48 0 -87 -12.5t-66.5 -37.5t-43 -62.5t-15.5 -87.5q0 -49 15.5 -86t43.5 -62.5t67 -38.5t86 -13zM363 133
q70 0 122 27.5t72 74.5t20 98q0 70 -33 117.5t-84.5 65t-96.5 17.5v-400z" />
    <glyph glyph-name="afii10039" unicode="&#x425;" horiz-adv-x="459" 
d="M4 0l196 346l-179 321h61l149 -277l150 277h59l-179 -318l198 -349h-63l-166 306l-168 -306h-58z" />
    <glyph glyph-name="afii10040" unicode="&#x426;" horiz-adv-x="607" 
d="M521 -144v144h-439v667h52v-619h335v619h52v-619h52v-192h-52z" />
    <glyph glyph-name="afii10041" unicode="&#x427;" horiz-adv-x="571" 
d="M437 0v303q-37 -15 -89 -23t-103 -8q-92 0 -134.5 37.5t-51.5 115.5q-4 36 -4 77v165h52v-156q0 -45 3 -66q4 -50 24.5 -80t52 -36.5t69.5 -6.5t93 8.5t88 24.5v312h52v-667h-52z" />
    <glyph glyph-name="afii10042" unicode="&#x428;" horiz-adv-x="821" 
d="M82 0v667h53v-619h251v619h51v-619h250v619h52v-667h-657z" />
    <glyph glyph-name="afii10043" unicode="&#x429;" horiz-adv-x="812" 
d="M725 -144v144h-643v667h53v-619h242v619h50v-619h247v619h50v-619h54v-192h-53z" />
    <glyph glyph-name="afii10044" unicode="&#x42a;" horiz-adv-x="570" 
d="M112 0v618h-98v49h150v-240h150q80 0 129 -32t67.5 -81.5t18.5 -97.5q0 -74 -29.5 -126t-78.5 -71t-103 -19h-206zM164 49h150q82 0 121.5 46t39.5 121t-42.5 118.5t-118.5 43.5h-150v-329z" />
    <glyph glyph-name="afii10045" unicode="&#x42b;" horiz-adv-x="730" 
d="M82 0v667h52v-240h134q81 0 129.5 -32.5t67 -81.5t18.5 -97t-12.5 -88t-39 -68.5t-66.5 -44t-94 -15.5h-189zM597 0v667h51v-667h-51zM134 49h134q81 0 120 46t39 121t-41.5 118.5t-116.5 43.5h-135v-329z" />
    <glyph glyph-name="afii10046" unicode="&#x42c;" horiz-adv-x="539" 
d="M82 0v667h52v-261h151q107 0 160 -51t53 -146q0 -97 -54 -153t-163 -56h-199zM134 50h151q77 0 118 41.5t41 117.5q0 75 -43 111.5t-117 36.5h-150v-307z" />
    <glyph glyph-name="afii10047" unicode="&#x42d;" horiz-adv-x="544" 
d="M232 -12q-55 0 -102 21t-70 45t-35 44l34 43q28 -45 71 -75t106 -30q98 0 150 72.5t53 207.5h-245v49h244q-4 92 -32.5 153.5t-73 86t-89.5 24.5q-47 0 -86 -20.5t-60.5 -45.5t-32.5 -44l-37 31q33 58 91 94t128 36q82 0 140 -46.5t83 -126t25 -172.5t-20.5 -159
t-56 -107.5t-83 -61t-102.5 -19.5z" />
    <glyph glyph-name="afii10048" unicode="&#x42e;" horiz-adv-x="848" 
d="M543 -12q-62 0 -108 26.5t-77 71.5t-47.5 104t-19.5 124h-155v-314h-52v667h52v-303h155q4 62 21 119.5t48.5 101t77 69.5t107.5 26q94 0 154 -60.5t79.5 -140.5t19.5 -146q0 -108 -36.5 -191.5t-95 -118.5t-123.5 -35zM546 39q69 0 116.5 49t64.5 118t17 127
q0 86 -27.5 158.5t-75.5 104.5t-98 32q-71 0 -118 -50.5t-62.5 -119t-15.5 -125.5q0 -56 12 -108.5t36.5 -94t62 -66.5t88.5 -25z" />
    <glyph glyph-name="afii10049" unicode="&#x42f;" horiz-adv-x="528" 
d="M38 0v50h18q12 0 28 3.5t33.5 27t35 73.5t31.5 143q-67 21 -101.5 65t-34.5 119q0 60 28 104.5t75.5 63t99.5 18.5h193v-667h-52v290h-157q-15 -99 -36 -156.5t-44.5 -87t-47.5 -38t-44 -8.5h-25zM245 338h147v280h-151q-67 0 -102.5 -37t-35.5 -98q0 -64 36 -104.5
t106 -40.5z" />
    <glyph glyph-name="afii10065" unicode="&#x430;" horiz-adv-x="431" 
d="M189 -11q-32 0 -59.5 9.5t-47.5 28t-31.5 45t-11.5 61.5q0 70 50 112q48 42 114 53q64 10 117 12v19q0 56 -24 85q-27 31 -79 31q-40 0 -72.5 -16t-55.5 -33l-18 40q8 7 23 16t34.5 17.5t42.5 14t48 5.5q76 0 115 -44q35 -41 35 -116v-329h-44l-3 42q-21 -22 -54 -37.5
t-79 -15.5zM196 33q38 0 70 17t54 41v175q-118 -3 -174 -35.5t-56 -98.5q0 -29 11 -48.5t27.5 -31.5t35 -16.5t32.5 -2.5z" />
    <glyph glyph-name="afii10066" unicode="&#x431;" horiz-adv-x="495" 
d="M249 -12q-52 0 -90 21.5t-63 59.5t-37 90.5t-12 114.5q0 177 49 265t157 88h39q32 0 51.5 11t24.5 40l42 -8q-6 -50 -36.5 -68.5t-77.5 -20.5l-47 -2q-62 -3 -96 -43.5t-43 -90t-13 -87.5q52 100 166 100q66 0 112.5 -39t63 -92.5t16.5 -103.5q0 -72 -31.5 -129.5
t-77 -81.5t-97.5 -24zM250 34q55 0 92 33.5t50 78t13 79.5q0 55 -21.5 101t-57.5 66.5t-73 20.5q-49 0 -86.5 -30t-51 -70t-13.5 -76q0 -40 10 -77t28.5 -65t46 -44.5t63.5 -16.5z" />
    <glyph glyph-name="afii10067" unicode="&#x432;" horiz-adv-x="451" 
d="M70 0v476h187q68 0 102 -29.5t34 -86.5q0 -47 -21 -75.5t-53 -39.5q40 -8 67 -34t27 -81q0 -63 -38.5 -96.5t-96.5 -33.5h-208zM119 44h152q28 0 50.5 10.5t32.5 31t10 45.5q0 38 -26.5 63t-76.5 25h-142v-175zM119 264h135q47 0 68.5 27t21.5 63q0 38 -21 58t-63 20
h-141v-168z" />
    <glyph glyph-name="afii10068" unicode="&#x433;" horiz-adv-x="351" 
d="M70 0v476h278v-44h-228v-432h-50z" />
    <glyph glyph-name="afii10069" unicode="&#x434;" horiz-adv-x="503" 
d="M2 -111v155h26q43 28 66.5 82.5t29 114t5.5 101.5v134h301v-432h53v-155h-50v111h-382v-111h-49zM95 44h285v388h-201v-115q0 -58 -11 -115.5t-30.5 -95.5t-42.5 -62z" />
    <glyph glyph-name="afii10070" unicode="&#x435;" horiz-adv-x="464" 
d="M253 -11q-100 0 -154 66t-54 184q0 85 30 144q31 60 76.5 83t88.5 23q73 0 118.5 -46.5t58.5 -101.5t13 -94h-336q0 -102 40.5 -158t119.5 -56q85 0 143 61l15 -45q-14 -14 -35 -27q-19 -12 -54.5 -22.5t-69.5 -10.5zM98 291h276q-3 55 -27 92q-24 38 -53.5 50t-53.5 12
q-54 0 -92.5 -36.5t-49.5 -117.5z" />
    <glyph glyph-name="afii10072" unicode="&#x436;" horiz-adv-x="634" 
d="M35 0l141 255q-20 14 -53 79q-12 23 -20.5 41.5t-17 31t-19 19t-27.5 6.5h-19v44h28q52 0 107 -105q21 -46 37 -68.5t29 -27.5t27 -5h45v206h47v-206h45q28 0 49.5 28.5t50.5 88.5q21 45 33.5 61t29 22t37.5 6h28v-44h-19q-37 0 -76 -79q-45 -84 -63 -96l141 -257h-55
l-119 226h-82v-226h-47v226h-79l-125 -226h-54z" />
    <glyph glyph-name="afii10073" unicode="&#x437;" horiz-adv-x="406" 
d="M192 -12q-47 0 -88.5 16.5t-58.5 32t-24 26.5l26 41q25 -32 58.5 -52.5t88.5 -20.5q63 0 93 25.5t30 68.5q0 45 -25.5 67.5t-70.5 27.5q-11 2 -71 2v41q55 0 69 2q42 6 64.5 34.5t22.5 63.5q0 43 -31 63t-76 20q-47 0 -76.5 -22.5t-56.5 -68.5l-36 24q23 48 64 79t106 31
q72 0 113 -32t41 -87q0 -44 -22 -78.5t-63 -46.5q44 -8 70.5 -35.5t26.5 -75.5q0 -72 -46.5 -109t-127.5 -37z" />
    <glyph glyph-name="afii10074" unicode="&#x438;" horiz-adv-x="495" 
d="M70 0v476h49v-360l257 308v52h49v-476h-49v359l-257 -308v-51h-49z" />
    <glyph glyph-name="afii10075" unicode="&#x439;" horiz-adv-x="495" 
d="M70 0v476h49v-360l257 308v52h49v-476h-49v359l-257 -308v-51h-49zM260 549q-27 0 -52.5 8.5t-45.5 25t-32.5 41.5t-13.5 57h47q8 -46 34 -67t64 -21q35 0 63 20.5t36 67.5h45q-3 -46 -30 -79.5t-58 -43t-57 -9.5z" />
    <glyph glyph-name="afii10076" unicode="&#x43a;" horiz-adv-x="437" 
d="M70 0v476h50v-208h63q25 0 40 19.5t46 92.5q19 43 38.5 64.5t39.5 26.5t40 5h28v-44h-23q-29 0 -46.5 -13.5t-35.5 -56.5q-39 -90 -58 -106l167 -256h-60l-146 224h-93v-224h-50z" />
    <glyph glyph-name="afii10077" unicode="&#x43b;" horiz-adv-x="485" 
d="M5 0v45q24 0 32 2q20 5 36.5 20t27.5 56q8 38 12.5 86.5t6 116.5t1.5 150h294v-476h-49v432h-196q-5 -241 -21 -316q-11 -54 -41 -85t-85 -31h-18z" />
    <glyph glyph-name="afii10078" unicode="&#x43c;" horiz-adv-x="566" 
d="M265 -3l-136 402l-22 -399h-47l27 477h65l132 -403l131 403h64l27 -477h-48l-22 400l-136 -403h-35z" />
    <glyph glyph-name="afii10079" unicode="&#x43d;" horiz-adv-x="491" 
d="M70 0v477h49v-205h253v205h49v-477h-49v226h-253v-226h-49z" />
    <glyph glyph-name="afii10080" unicode="&#x43e;" horiz-adv-x="490" 
d="M246 -11q-72 0 -121 45q-44 39 -62 94.5t-18 113.5q0 75 29 135t75 86t97 26q70 0 120 -44q33 -30 52 -68t25 -75.5t6 -61.5q0 -79 -31 -140q-30 -60 -75.5 -85.5t-96.5 -25.5zM246 33q43 -2 72.5 20.5t48 55t26 69t6.5 63.5q1 66 -25 116t-62.5 70t-65.5 18
q-55 0 -93 -38q-35 -35 -47 -78t-12 -87q0 -39 9.5 -76.5t28.5 -67t47.5 -47.5t66.5 -18z" />
    <glyph glyph-name="afii10081" unicode="&#x43f;" horiz-adv-x="490" 
d="M70 0v477h350v-477h-49v433h-252v-433h-49z" />
    <glyph glyph-name="afii10082" unicode="&#x440;" horiz-adv-x="485" 
d="M69 -193v670h44l5 -41q23 21 55 37t77 16q66 0 113 -39q34 -29 50 -66t22 -75.5t6 -65.5q0 -86 -34 -147q-26 -47 -60.5 -71t-66 -30t-55.5 -6q-35 0 -61.5 8t-45.5 19v-209h-49zM220 33q78 0 124.5 55t46.5 152q1 73 -24 122.5t-61 66.5t-66 16q-38 0 -68.5 -18
t-53.5 -41v-319q18 -14 43.5 -24t58.5 -10z" />
    <glyph glyph-name="afii10083" unicode="&#x441;" horiz-adv-x="428" 
d="M249 -11q-44 0 -81.5 16.5t-64.5 48.5t-42.5 78t-15.5 106q0 91 33 150q23 40 53.5 63.5t60 30.5t50.5 7q45 0 84 -21.5t57.5 -45t21.5 -33.5l-36 -25q-26 40 -54 60.5t-73 20.5q-48 0 -85 -34t-49.5 -80.5t-12.5 -91.5q0 -47 11 -84.5t32 -64.5t50 -41.5t65 -14.5
q37 0 67 13t62 46l19 -43q-21 -22 -59 -41.5t-93 -19.5z" />
    <glyph glyph-name="afii10084" unicode="&#x442;" horiz-adv-x="371" 
d="M161 0v433h-151v44h351v-44h-151v-433h-49z" />
    <glyph glyph-name="afii10085" unicode="&#x443;" horiz-adv-x="448" 
d="M-4 -192l-6 44h8q63 0 116 36t81 99l-186 490h55l163 -438q42 88 84 201.5t75 236.5h52q-35 -138 -76.5 -243t-79.5 -187q-43 -90 -78 -137.5t-90 -74.5t-118 -27z" />
    <glyph glyph-name="afii10086" unicode="&#x444;" horiz-adv-x="619" 
d="M286 -194v193q-52 2 -96.5 18.5t-77 46.5t-51 73.5t-18.5 100.5q0 81 39.5 135.5t96 75t107.5 22.5v195h46v-195q81 -4 138.5 -36t82 -86t24.5 -111q0 -58 -18.5 -102t-51.5 -74t-78 -45.5t-97 -17.5v-193h-46zM286 43v384q-61 -2 -109 -30t-66 -70.5t-18 -88.5
q0 -47 15 -82t41.5 -59.5t61.5 -37.5t75 -16zM332 43q64 3 109.5 29.5t65.5 71t20 94.5q0 68 -32 111.5t-77.5 59.5t-85.5 18v-384z" />
    <glyph glyph-name="afii10087" unicode="&#x445;" horiz-adv-x="371" 
d="M5 0l154 244l-146 233h55l118 -192l119 192h53l-146 -232l154 -245h-54l-126 204l-127 -204h-54z" />
    <glyph glyph-name="afii10088" unicode="&#x446;" horiz-adv-x="484" 
d="M419 -111v111h-349v476h51v-433h241v433h50v-433h52v-154h-45z" />
    <glyph glyph-name="afii10089" unicode="&#x447;" horiz-adv-x="479" 
d="M359 0v185q-38 -11 -76.5 -18t-76.5 -7q-69 0 -105.5 23.5t-46.5 77.5q-4 26 -4 60v155h50v-151q0 -34 3 -49q7 -38 34.5 -54t76.5 -16q39 0 77 7.5t68 18.5v244h50v-476h-50z" />
    <glyph glyph-name="afii10090" unicode="&#x448;" horiz-adv-x="716" 
d="M70 0v476h50v-432h214v432h49v-432h213v432h50v-476h-576z" />
    <glyph glyph-name="afii10091" unicode="&#x449;" horiz-adv-x="710" 
d="M645 -111v111h-575v476h50v-432h210v432h48v-432h210v432h50v-432h52v-155h-45z" />
    <glyph glyph-name="afii10092" unicode="&#x44a;" horiz-adv-x="492" 
d="M102 0v431h-97v45h148v-155h123q64 0 106 -24.5t57 -61.5t15 -70q0 -53 -24.5 -93t-66 -56t-87.5 -16h-174zM153 44h119q130 0 130 121q0 59 -34 85t-95 26h-120v-232z" />
    <glyph glyph-name="afii10093" unicode="&#x44b;" horiz-adv-x="624" 
d="M70 0v476h51v-155h118q64 0 107 -24.5t56 -62t13 -70.5q0 -52 -25 -92t-65.5 -56t-86.5 -16h-168zM504 0v476h50v-476h-50zM121 44h116q62 0 93.5 31t31.5 89t-32 85t-93 27h-116v-232z" />
    <glyph glyph-name="afii10094" unicode="&#x44c;" horiz-adv-x="464" 
d="M70 0v476h51v-155h127q65 0 107 -24t56.5 -62t14.5 -71q0 -52 -25 -92.5t-66 -56t-87 -15.5h-178zM121 44h123q65 0 97.5 30.5t32.5 89.5q0 58 -33 85t-96 27h-124v-232z" />
    <glyph glyph-name="afii10095" unicode="&#x44d;" horiz-adv-x="440" 
d="M187 -12q-40 0 -75 12t-56 29.5t-31 32.5l27 41q23 -35 58.5 -53t79.5 -18q69 0 112.5 48t45.5 145h-214v45h213q-5 65 -30.5 105.5t-59.5 55t-60 14.5q-45 0 -77.5 -19.5t-58.5 -63.5l-36 24q12 26 38.5 51t61 38.5t71.5 13.5q66 0 113.5 -33t67.5 -92t20 -126
q0 -62 -15.5 -109t-43 -78.5t-66.5 -47t-85 -15.5z" />
    <glyph glyph-name="afii10096" unicode="&#x44e;" horiz-adv-x="691" 
d="M453 -12q-49 0 -85 18.5t-59.5 50.5t-36 75t-15.5 91h-137v-223h-50v476h50v-207h138q6 70 35 122t71 72.5t89 20.5q74 0 120 -41.5t60.5 -98t14.5 -105.5q0 -83 -27.5 -143t-72 -84t-95.5 -24zM453 33q53 0 89 34t46.5 81t10.5 89q0 65 -20 113t-53.5 68.5t-72.5 20.5
q-54 0 -90 -34.5t-46 -80t-10 -86.5q0 -42 9 -79.5t27.5 -65t46 -44t63.5 -16.5z" />
    <glyph glyph-name="afii10097" unicode="&#x44f;" horiz-adv-x="470" 
d="M40 0v46h17q19 0 35 10.5t32 44.5t25 85q-15 5 -32 15t-31.5 26.5t-24 41t-9.5 58.5q0 69 46 109t128 40h174v-476h-49v177h-155q-16 -83 -42.5 -124t-48 -47t-35.5 -6h-30zM217 222h134v209h-131q-54 0 -86 -27.5t-32 -75.5q0 -51 30 -78.5t85 -27.5z" />
    <glyph glyph-name="afii10071" unicode="&#x451;" horiz-adv-x="464" 
d="M253 -11q-100 0 -154 66t-54 184q0 85 30 144q31 60 76.5 83t88.5 23q73 0 118.5 -46.5t58.5 -101.5t13 -94h-336q0 -102 40.5 -158t119.5 -56q85 0 143 61l15 -45q-14 -14 -35 -27q-19 -12 -54.5 -22.5t-69.5 -10.5zM98 291h276q-3 55 -27 92q-24 38 -53.5 50t-53.5 12
q-54 0 -92.5 -36.5t-49.5 -117.5zM155 601q-15 0 -25.5 10t-10.5 30q0 18 10.5 28.5t25.5 10.5t25.5 -10.5t10.5 -28.5q0 -20 -10.5 -30t-25.5 -10zM326 601q-15 0 -25.5 10t-10.5 30q0 18 10.5 28.5t25.5 10.5t25.5 -10.5t10.5 -28.5q0 -20 -10.5 -30t-25.5 -10z" />
    <glyph glyph-name="afii10099" unicode="&#x452;" horiz-adv-x="499" 
d="M334 -196l-24 40q33 26 50 53t22.5 60.5t5.5 66.5v240q0 72 -29 106.5t-91 34.5q-35 0 -74 -18.5t-74 -48.5v-338h-50v569h-77v41h77v57h50v-57h145v-41h-145v-181q31 25 71 43.5t86 18.5q89 0 125.5 -48t36.5 -139v-239q0 -67 -15.5 -108.5t-40.5 -68t-49 -43.5z" />
    <glyph glyph-name="afii10100" unicode="&#x453;" horiz-adv-x="351" 
d="M70 0v476h278v-44h-228v-432h-50zM149 545l-24 29l155 139l37 -41z" />
    <glyph glyph-name="afii10101" unicode="&#x454;" horiz-adv-x="428" 
d="M247 -11q-44 0 -81.5 16.5t-64.5 48.5t-42.5 78t-15.5 106q0 91 33 150q23 40 53.5 63.5t60.5 30.5t50 7q45 0 84 -21.5t57.5 -45.5t21.5 -33l-36 -25q-26 40 -54 60.5t-73 20.5q-36 0 -65.5 -19.5t-47 -45.5t-22.5 -50q-7 -25 -11 -63h212v-44h-213q1 -35 11 -69
q17 -56 55 -88t92 -32q37 0 67 13t62 46l19 -43q-21 -22 -59 -41.5t-93 -19.5z" />
    <glyph glyph-name="afii10102" unicode="&#x455;" horiz-adv-x="378" 
d="M184 -11q-59 0 -95 18t-52 34l19 45q17 -17 50 -35t78 -18q51 0 78.5 22.5t27.5 64.5t-26 68q-27 27 -95 52q-65 23 -93 52.5t-28 79.5q0 40 24.5 68t58.5 38.5t60 10.5q51 0 88 -18t56 -38l-21 -39q-29 25 -59 38.5t-68 13.5q-45 0 -67 -20t-22 -51q0 -18 5.5 -31
t18.5 -24.5t35.5 -21.5t55.5 -22q63 -23 95 -56.5t32 -95.5q0 -37 -14.5 -63t-38 -42.5t-51.5 -23t-52 -6.5z" />
    <glyph glyph-name="afii10103" unicode="&#x456;" horiz-adv-x="181" 
d="M69 0v477h49v-477h-49zM93 578q-15 0 -25.5 10t-10.5 30q0 18 10.5 28.5t25.5 10.5t25.5 -10.5t10.5 -28.5q0 -20 -10.5 -30t-25.5 -10z" />
    <glyph glyph-name="afii10104" unicode="&#x457;" horiz-adv-x="181" 
d="M68 0v477h49v-477h-49zM8 601q-15 0 -25.5 10t-10.5 30q0 18 10.5 28.5t25.5 10.5t25.5 -10.5t10.5 -28.5q0 -20 -10.5 -30t-25.5 -10zM179 601q-15 0 -25.5 10t-10.5 30q0 18 10.5 28.5t25.5 10.5t25.5 -10.5t10.5 -28.5q0 -20 -10.5 -30t-25.5 -10z" />
    <glyph glyph-name="afii10105" unicode="&#x458;" horiz-adv-x="185" 
d="M18 -196l-20 41q40 27 55.5 62t15.5 90v480h49v-480q0 -74 -25 -118.5t-75 -74.5zM96 578q-15 0 -25.5 10t-10.5 30q0 18 10.5 28.5t25.5 10.5t25.5 -10.5t10.5 -28.5q0 -20 -10.5 -30t-25.5 -10z" />
    <glyph glyph-name="afii10106" unicode="&#x459;" horiz-adv-x="721" 
d="M5 0v46h16q19 0 34 6.5t27.5 26.5t18.5 46q20 88 20 351h269v-155h114q93 0 137 -43.5t44 -110.5q0 -53 -26 -95t-66 -57t-87 -15h-166v432h-169q-5 -240 -21 -315q-12 -54 -41 -85.5t-80 -31.5h-24zM390 44h113q65 0 97 32t32 89q0 59 -34 85t-94 26h-114v-232z" />
    <glyph glyph-name="afii10107" unicode="&#x45a;" horiz-adv-x="735" 
d="M70 0v477h50v-187h240v187h50v-187h122q84 0 125.5 -37t41.5 -103q0 -70 -42 -110t-127 -40h-170v246h-240v-246h-50zM410 44h118q58 0 89 24t31 80q0 54 -32 76t-87 22h-119v-202z" />
    <glyph glyph-name="afii10108" unicode="&#x45b;" horiz-adv-x="499" 
d="M70 0v568h-78v42h78v57h50v-57h146v-42h-146v-180q35 27 74.5 44.5t82.5 17.5q89 0 125.5 -48t36.5 -139v-263h-50v264q0 75 -29 108t-91 33q-38 0 -74.5 -19.5t-74.5 -48.5v-337h-50z" />
    <glyph glyph-name="afii10109" unicode="&#x45c;" horiz-adv-x="437" 
d="M70 0v476h50v-208h63q25 0 40 19.5t46 92.5q19 43 38.5 64.5t39.5 26.5t40 5h28v-44h-23q-29 0 -46.5 -13.5t-35.5 -56.5q-39 -90 -58 -106l167 -256h-60l-146 224h-93v-224h-50zM186 545l-24 29l155 139l37 -41z" />
    <glyph glyph-name="afii10110" unicode="&#x45e;" horiz-adv-x="448" 
d="M-4 -192l-6 44h8q63 0 116 36t81 99l-186 490h55l163 -438q42 88 84 201.5t75 236.5h52q-35 -138 -76.5 -243t-79.5 -187q-43 -90 -78 -137.5t-90 -74.5t-118 -27zM232 549q-27 0 -52.5 8.5t-45.5 25t-32.5 41.5t-13.5 57h47q8 -46 34 -67t64 -21q35 0 63 20.5t36 67.5
h45q-3 -46 -30 -79.5t-58 -43t-57 -9.5z" />
    <glyph glyph-name="afii10193" unicode="&#x45f;" horiz-adv-x="487" 
d="M219 -111v111h-149v476h51v-433h246v433h50v-476h-149v-111h-49z" />
    <glyph glyph-name="afii10050" unicode="&#x490;" horiz-adv-x="445" 
d="M82 -1v668h303v82h51v-131h-302v-618z" />
    <glyph glyph-name="afii10098" unicode="&#x491;" horiz-adv-x="351" 
d="M70 0v476h229v60h49v-105h-228v-431h-50z" />
    <glyph glyph-name="Wgrave" unicode="&#x1e80;" horiz-adv-x="718" 
d="M161 0l-151 667h53l126 -556l145 556h49l152 -559l121 559h52l-149 -667h-45l-156 566l-153 -566h-44zM400 711l-167 127l37 41l155 -139z" />
    <glyph glyph-name="wgrave" unicode="&#x1e81;" horiz-adv-x="628" 
d="M144 0l-139 477h54l113 -410l120 410h44l120 -408l115 408h52l-143 -477h-51l-116 393l-119 -393h-50zM355 545l-167 127l37 41l155 -139z" />
    <glyph glyph-name="Wacute" unicode="&#x1e82;" horiz-adv-x="718" 
d="M161 0l-151 667h53l126 -556l145 556h49l152 -559l121 559h52l-149 -667h-45l-156 566l-153 -566h-44zM317 711l-24 29l155 139l37 -41z" />
    <glyph glyph-name="wacute" unicode="&#x1e83;" horiz-adv-x="628" 
d="M144 0l-139 477h54l113 -410l120 410h44l120 -408l115 408h52l-143 -477h-51l-116 393l-119 -393h-50zM271 545l-24 29l155 139l37 -41z" />
    <glyph glyph-name="Wdieresis" unicode="&#x1e84;" horiz-adv-x="718" 
d="M161 0l-151 667h53l126 -556l145 556h49l152 -559l121 559h52l-149 -667h-45l-156 566l-153 -566h-44zM273 767q-15 0 -25.5 10t-10.5 30q0 18 10.5 28.5t25.5 10.5t25.5 -10.5t10.5 -28.5q0 -20 -10.5 -30t-25.5 -10zM444 767q-15 0 -25.5 10t-10.5 30q0 18 10.5 28.5
t25.5 10.5t25.5 -10.5t10.5 -28.5q0 -20 -10.5 -30t-25.5 -10z" />
    <glyph glyph-name="wdieresis" unicode="&#x1e85;" horiz-adv-x="628" 
d="M144 0l-139 477h54l113 -410l120 410h44l120 -408l115 408h52l-143 -477h-51l-116 393l-119 -393h-50zM228 601q-15 0 -25.5 10t-10.5 30q0 18 10.5 28.5t25.5 10.5t25.5 -10.5t10.5 -28.5q0 -20 -10.5 -30t-25.5 -10zM399 601q-15 0 -25.5 10t-10.5 30q0 18 10.5 28.5
t25.5 10.5t25.5 -10.5t10.5 -28.5q0 -20 -10.5 -30t-25.5 -10z" />
    <glyph glyph-name="Ygrave" unicode="&#x1ef2;" horiz-adv-x="499" 
d="M220 0v321l-215 346h59l183 -295l190 295h57l-222 -345v-322h-52zM283 711l-167 127l37 41l155 -139z" />
    <glyph glyph-name="ygrave" unicode="&#x1ef3;" horiz-adv-x="448" 
d="M-4 -192l-6 44h8q63 0 116 36t81 99l-186 490h55l163 -438q42 88 84 201.5t75 236.5h52q-35 -138 -76.5 -243t-79.5 -187q-43 -90 -78 -137.5t-90 -74.5t-118 -27zM259 545l-167 127l37 41l155 -139z" />
    <glyph glyph-name="endash" unicode="&#x2013;" horiz-adv-x="500" 
d="M0 239v45h500v-45h-500z" />
    <glyph glyph-name="emdash" unicode="&#x2014;" horiz-adv-x="1000" 
d="M0 239v45h1000v-45h-1000z" />
    <glyph glyph-name="afii00208" unicode="&#x2015;" horiz-adv-x="1000" 
d="M0 239v45h1000v-45h-1000z" />
    <glyph glyph-name="quoteleft" unicode="&#x2018;" horiz-adv-x="166" 
d="M111 496q-26 21 -46 53.5t-20 67.5q0 23 11.5 39.5t33.5 16.5q19 0 30 -12t11 -31t-12.5 -32.5t-30.5 -13.5q2 -17 14.5 -35t25.5 -31z" />
    <glyph glyph-name="quoteright" unicode="&#x2019;" horiz-adv-x="166" 
d="M60 496l-17 22q13 13 25.5 31t14.5 35q-18 0 -30.5 13.5t-12.5 32.5t11 31t30 12q22 0 33.5 -16.5t11.5 -39.5q0 -35 -20 -67.5t-46 -53.5z" />
    <glyph glyph-name="quotesinglbase" unicode="&#x201a;" horiz-adv-x="158" 
d="M57 -87l-17 22q13 13 25.5 31t14.5 35q-18 0 -30.5 13.5t-12.5 32.5t11 31t30 12q22 0 33.5 -16.5t11.5 -39.5q0 -35 -20 -67.5t-46 -53.5z" />
    <glyph glyph-name="quotedblleft" unicode="&#x201c;" horiz-adv-x="316" 
d="M266 496q-26 21 -46 53.5t-20 67.5q0 23 11.5 39.5t33.5 16.5q19 0 30 -12t11 -31t-12.5 -32.5t-30.5 -13.5q2 -17 14.5 -35t25.5 -31zM116 496q-26 21 -46 53.5t-20 67.5q0 23 11.5 39.5t33.5 16.5q19 0 30 -12t11 -31t-12.5 -32.5t-30.5 -13.5q2 -17 14.5 -35t25.5 -31
z" />
    <glyph glyph-name="quotedblright" unicode="&#x201d;" horiz-adv-x="316" 
d="M215 496l-17 22q13 13 25.5 31t14.5 35q-18 0 -30.5 13.5t-12.5 32.5t11 31t30 12q22 0 33.5 -16.5t11.5 -39.5q0 -35 -20 -67.5t-46 -53.5zM65 496l-17 22q13 13 25.5 31t14.5 35q-18 0 -30.5 13.5t-12.5 32.5t11 31t30 12q22 0 33.5 -16.5t11.5 -39.5q0 -35 -20 -67.5
t-46 -53.5z" />
    <glyph glyph-name="quotedblbase" unicode="&#x201e;" horiz-adv-x="308" 
d="M212 -87l-17 22q13 13 25.5 31t14.5 35q-18 0 -30.5 13.5t-12.5 32.5t11 31t30 12q22 0 33.5 -16.5t11.5 -39.5q0 -35 -20 -67.5t-46 -53.5zM62 -87l-17 22q13 13 25.5 31t14.5 35q-18 0 -30.5 13.5t-12.5 32.5t11 31t30 12q22 0 33.5 -16.5t11.5 -39.5q0 -35 -20 -67.5
t-46 -53.5z" />
    <glyph glyph-name="dagger" unicode="&#x2020;" horiz-adv-x="412" 
d="M181 -146v532h-149v49h149v232h51v-232h148v-49h-148v-532h-51z" />
    <glyph glyph-name="daggerdbl" unicode="&#x2021;" horiz-adv-x="412" 
d="M181 -146v232h-149v49h149v251h-149v49h149v232h51v-232h148v-49h-148v-251h148v-49h-148v-232h-51z" />
    <glyph glyph-name="bullet" unicode="&#x2022;" horiz-adv-x="473" 
d="M237 181q-39 0 -72.5 15t-58 41t-39 61t-14.5 76q0 60 30 107t72.5 67t81.5 20q61 0 107 -39t61 -83.5t15 -71.5q0 -57 -31 -106q-30 -49 -72 -68t-80 -19z" />
    <glyph glyph-name="ellipsis" unicode="&#x2026;" horiz-adv-x="537" 
d="M259 -11q-17 0 -29 12t-12 33q0 20 12 32t29 12t29.5 -12t12.5 -32q0 -21 -12.5 -33t-29.5 -12zM435 -11q-17 0 -29 12t-12 33q0 20 12 32t29 12t29.5 -12t12.5 -32q0 -21 -12.5 -33t-29.5 -12zM84 -11q-17 0 -29 12t-12 33q0 20 12 32t29 12t29.5 -12t12.5 -32
q0 -21 -12.5 -33t-29.5 -12z" />
    <glyph glyph-name="perthousand" unicode="&#x2030;" horiz-adv-x="1011" 
d="M59 -7l-31 35l591 644l31 -33zM517 -7q-28 0 -52.5 11.5t-42.5 31.5t-28.5 46.5t-10.5 57.5q0 34 12.5 62t33 47t44.5 28t44 9q47 0 79 -27.5t44.5 -62t12.5 -56.5q0 -45 -23 -83t-53.5 -51t-59.5 -13zM848 -7q-28 0 -52.5 11.5t-42.5 31.5t-28.5 46.5t-10.5 57.5
q0 34 12.5 62t33 47t44.5 28t44 9q47 0 79 -27.5t44.5 -62t12.5 -56.5q0 -45 -23 -83t-53.5 -51t-59.5 -13zM517 36q29 0 52.5 20t31 44.5t7.5 39.5q0 38 -18 63.5t-39 33.5t-34 8q-32 0 -54.5 -21t-29 -44.5t-6.5 -39.5q0 -21 7 -40t19 -33.5t28.5 -22.5t35.5 -8zM848 36
q29 0 52.5 20t31 44.5t7.5 39.5q0 38 -18 63.5t-39 33.5t-34 8q-45 0 -68 -34.5t-23 -70.5q0 -21 7 -40t19 -33.5t28.5 -22.5t36.5 -8zM166 380q-29 0 -53.5 11.5t-42.5 31.5t-28.5 46.5t-10.5 57.5q0 34 12.5 62t33 47t44.5 28t45 9q46 0 78.5 -29t44 -62.5t11.5 -54.5
q0 -45 -23 -83q-23 -37 -53.5 -50.5t-57.5 -13.5zM165 422q32 0 53.5 19.5t29.5 44t8 41.5q0 38 -19.5 63.5t-39 33t-32.5 7.5q-33 0 -56 -21.5t-29 -44t-6 -38.5q0 -22 7 -41t19 -33.5t28.5 -22.5t36.5 -8z" />
    <glyph glyph-name="guilsinglleft" unicode="&#x2039;" horiz-adv-x="298" 
d="M225 59l-187 191l172 199l30 -29l-145 -166l159 -164z" />
    <glyph glyph-name="guilsinglright" unicode="&#x203a;" horiz-adv-x="298" 
d="M88 59l-30 29l145 167l-159 163l28 31l187 -191z" />
    <glyph glyph-name="fraction" unicode="&#x2044;" horiz-adv-x="209" 
d="M-176 -7l-31 35l591 644l31 -33z" />
    <glyph glyph-name="fourinferior" unicode="&#x2084;" horiz-adv-x="276" 
d="M179 0v77h-159v40l166 217h37v-217h42v-40h-42v-77h-44zM67 117h112v147z" />
    <glyph glyph-name="Euro" unicode="&#x20ac;" 
d="M316 -11q-41 0 -76.5 13t-62.5 43t-45 78t-24 119h-83v48h294v-48h-157q4 -58 17.5 -97t34 -63t46.5 -34.5t55 -10.5q43 0 72 15.5t61 52.5l27 -42q-24 -29 -63 -51.5t-96 -22.5zM25 365v48h82q8 133 62.5 196.5t138.5 63.5q59 0 97 -25t64 -59l-34 -36q-26 32 -54.5 52
t-72.5 20q-63 0 -101 -49.5t-46 -162.5h158v-48h-294z" />
    <glyph glyph-name="afii61289" unicode="&#x2113;" horiz-adv-x="456" 
d="M150 250q20 18 48.5 49.5t54 72.5t43.5 89.5t18 99.5q0 45 -18.5 70.5t-54.5 25.5q-26 0 -43.5 -19t-28 -48.5t-15 -64.5t-4.5 -68v-207zM406 77q-23 -35 -65 -60.5t-97 -25.5q-66 0 -103 41.5t-37 125.5q-13 -10 -24.5 -18t-24.5 -17l-20 32l68 58v224q0 129 37.5 191
t106.5 62q51 0 84 -32.5t33 -101.5q0 -45 -17 -92.5t-46.5 -95t-68.5 -92.5t-82 -86v-20q0 -65 23 -103t76 -38q22 0 41 6.5t35 16t28 21.5t20 24z" />
    <glyph glyph-name="afii61352" unicode="&#x2116;" horiz-adv-x="960" 
d="M79 0v667h59l346 -546v546h52v-667h-37l-369 578v-578h-51zM783 294q-39 0 -68 16t-47.5 42.5t-27.5 60.5t-9 69q0 55 21.5 100t56.5 64.5t74 19.5q54 0 89.5 -31.5t49 -74.5t13.5 -80q0 -54 -21.5 -100t-57 -66t-73.5 -20zM783 338q38 0 63 25t32.5 57.5t7.5 60.5
q0 42 -13 75t-38 49t-52 16q-38 0 -63 -23.5t-32.5 -56t-7.5 -59.5q0 -47 15 -81.5t38.5 -48.5t49.5 -14z" />
    <glyph glyph-name="trademark" unicode="&#x2122;" horiz-adv-x="555" 
d="M377 378l-77 207v-205h-38v287h46l84 -234l78 234h48v-287h-39v207l-71 -209h-31zM106 380v250h-88v37h214v-37h-87v-250h-39z" />
    <glyph glyph-name="Omega" unicode="&#x2126;" horiz-adv-x="657" 
d="M56 0v49h143q-27 22 -52 50.5t-44.5 65.5t-31 83.5t-11.5 103.5q0 105 39.5 184t102 112t127.5 33q96 0 159 -52.5t86 -128t23 -143.5q0 -83 -24.5 -148t-56.5 -102t-58 -58h143v-49h-211v51q67 45 109.5 121.5t42.5 180.5q0 93 -30.5 159t-80.5 93t-101 27
q-52 0 -92.5 -21.5t-68 -59.5t-41.5 -89t-14 -111q0 -52 12 -96.5t32.5 -82t48.5 -68t61 -53.5v-51h-212z" />
    <glyph glyph-name="estimated" unicode="&#x212e;" horiz-adv-x="742" 
d="M187 340v-230q75 -75 183 -75q79 0 137.5 35.5t106.5 116.5l44 -26q-29 -45 -59 -77.5t-64 -53.5t-74.5 -30.5t-90.5 -9.5q-75 0 -135 27.5t-102.5 74.5t-65.5 111t-23 137t23 137t65.5 111.5t102.5 74.5t135 27q73 0 133 -26t103 -72.5t67 -111t25 -140.5h-511zM555 386
v183q-79 76 -186 76q-109 0 -182 -76v-183h368z" />
    <glyph glyph-name="partialdiff" unicode="&#x2202;" horiz-adv-x="524" 
d="M260 -11q-44 0 -82.5 15.5t-66.5 47t-44 78.5t-16 109q0 59 15 105.5t43 78.5t66 49t85 17q45 0 75 -10t53 -24q-16 40 -45 70.5t-68 53.5q-38 22 -83.5 36t-90.5 19l9 39q63 -5 114.5 -22t102.5 -52q23 -17 46 -39q45 -48 72 -118.5t27 -161.5q0 -71 -16 -125.5
t-44.5 -91t-67.5 -55.5t-84 -19zM260 33q39 -2 70 20t51.5 57t30.5 79.5t9 89.5q0 58 -18 121q-30 20 -64 32.5t-77 12.5q-72 0 -115 -53t-43 -153q0 -51 12 -89.5t33 -64.5t49.5 -39t61.5 -13z" />
    <glyph glyph-name="Delta" unicode="&#x2206;" horiz-adv-x="541" 
d="M5 0l242 667h42l247 -667h-531zM77 48h386l-196 543z" />
    <glyph glyph-name="product" unicode="&#x220f;" horiz-adv-x="626" 
d="M98 0v618h-82v49h594v-49h-84v-618h-53v618h-322v-618h-53z" />
    <glyph glyph-name="summation" unicode="&#x2211;" horiz-adv-x="489" 
d="M41 0v51l280 285l-278 279v52h412v-49h-345l282 -282l-285 -287h356v-49h-422z" />
    <glyph glyph-name="minus" unicode="&#x2212;" 
d="M36 308v48h428v-48h-428z" />
    <glyph glyph-name="uni2215" unicode="&#x2215;" horiz-adv-x="209" 
d="M-176 -7l-31 35l591 644l31 -33z" />
    <glyph glyph-name="uni2219" unicode="&#x2219;" horiz-adv-x="272" 
d="M136 325q-17 0 -29 12t-12 33q0 20 12 32t29 12t29.5 -12t12.5 -32q0 -21 -12.5 -33t-29.5 -12z" />
    <glyph glyph-name="radical" unicode="&#x221a;" horiz-adv-x="527" 
d="M210 0l-112 234h-71v47h107l95 -205l221 641l48 -16l-242 -701h-46z" />
    <glyph glyph-name="infinity" unicode="&#x221e;" horiz-adv-x="542" 
d="M147 207q-33 0 -56.5 12.5t-39 33.5t-23 48.5t-7.5 57.5q0 75 34.5 113.5t91.5 38.5q37 0 67.5 -21.5t56.5 -82.5q25 57 52.5 80t72.5 23q54 0 89.5 -39.5t35.5 -111.5q0 -36 -10.5 -64t-27.5 -47.5t-40 -30t-47 -10.5q-34 0 -64.5 20.5t-61.5 79.5q-28 -59 -60.5 -79.5
t-62.5 -20.5zM148 248q35 0 58 34t41 77q-25 54 -46.5 82.5t-52.5 28.5t-51 -23t-25 -48.5t-4 -39.5q0 -54 22 -82.5t58 -28.5zM395 248q29 0 48.5 22.5t25.5 49t6 40.5q0 52 -24 81.5t-58 29.5q-36 0 -58 -32.5t-43 -80.5q23 -56 50 -85t53 -25z" />
    <glyph glyph-name="integral" unicode="&#x222b;" horiz-adv-x="356" 
d="M12 -194v46q39 -2 65 8t42.5 29t23.5 45.5t7 59.5v495q0 100 48.5 141.5t145.5 42.5v-46q-60 0 -93.5 -21.5t-41 -54.5t-7.5 -66v-494q0 -101 -46.5 -143t-143.5 -42z" />
    <glyph glyph-name="approxequal" unicode="&#x2248;" horiz-adv-x="527" 
d="M373 180q-26 0 -53.5 5t-53.5 13q-63 19 -113 19q-41 0 -95 -27l-21 41q21 13 52.5 23.5t62.5 10.5q50 0 122 -20q63 -17 98 -17q29 0 51.5 8t45.5 21l22 -40q-19 -14 -51 -25.5t-67 -11.5zM373 361q-26 0 -53.5 5t-53.5 13q-63 19 -113 19q-41 0 -95 -27l-21 41
q21 13 52.5 23.5t62.5 10.5q50 0 122 -20q63 -17 98 -17q29 0 51.5 8t45.5 21l22 -40q-19 -14 -51 -25.5t-67 -11.5z" />
    <glyph glyph-name="notequal" unicode="&#x2260;" 
d="M102 98l-33 31l56 67h-89v48h128l112 135h-240v48h280l80 98l34 -31l-56 -67h91v-48h-130l-112 -135h242v-48h-282z" />
    <glyph glyph-name="lessequal" unicode="&#x2264;" 
d="M36 0v48h428v-48h-428zM435 151l-380 160v44l374 155l18 -46l-317 -130l323 -135z" />
    <glyph glyph-name="greaterequal" unicode="&#x2265;" 
d="M36 0v48h428v-48h-428zM72 151l-17 48l322 135l-316 130l19 46l373 -155v-44z" />
    <glyph glyph-name="lozenge" unicode="&#x25ca;" horiz-adv-x="542" 
d="M270 -16l-210 373l210 363l212 -363zM274 78l150 280l-154 266l-152 -265z" />
    <glyph glyph-name="fi" unicode="&#xfb01;" horiz-adv-x="446" 
d="M89 0v433h-67v44h68v49q0 59 23.5 95t57.5 47.5t65 11.5q45 0 76.5 -13.5t47.5 -25.5l-16 -42q-21 15 -53 26t-58 11q-53 0 -73.5 -31t-20.5 -78v-50h105v-44h-105v-433h-50zM327 0v477h50v-477h-50z" />
    <glyph glyph-name="fl" unicode="&#xfb02;" horiz-adv-x="481" 
d="M410 -11q-39 0 -59 17.5t-22 39.5t-2 36v543q-35 11 -78 11q-58 0 -84 -28t-26 -85v-46h105v-44h-105v-433h-50v433h-67v44h68v53q0 75 42.5 112.5t114.5 37.5q37 0 71.5 -7.5t57.5 -16.5v-562q0 -35 7.5 -49.5t33.5 -14.5q20 0 50 15l9 -40q-13 -7 -31 -11.5t-35 -4.5z
" />
    <glyph glyph-name="cedilla.top" horiz-adv-x="420" 
d="M230 557l68 130h42l-48 -130h-62z" />
    <glyph glyph-name="ascendercaron" horiz-adv-x="500" 
d="M231 497v170h64l-24 -170h-40z" />
    <glyph glyph-name="kratka" horiz-adv-x="396" 
d="M197 549q-27 0 -52.5 8.5t-45.5 25t-32.5 41.5t-13.5 57h47q8 -46 34 -67t64 -21q35 0 63 20.5t36 67.5h45q-3 -46 -30 -79.5t-58 -43t-57 -9.5z" />
    <glyph glyph-name="tonos.cap" horiz-adv-x="241" 
d="M101 452l-37 11l62 205l55 -13z" />
    <glyph glyph-name="commaaccent" horiz-adv-x="290" 
d="M91 -174l48 130h59l-68 -130h-39z" />
    <hkern u1="&#x26;" u2="&#x178;" k="51" />
    <hkern u1="&#x26;" u2="&#x176;" k="51" />
    <hkern u1="&#x26;" u2="&#x166;" k="46" />
    <hkern u1="&#x26;" u2="&#x164;" k="46" />
    <hkern u1="&#x26;" u2="&#x162;" k="46" />
    <hkern u1="&#x26;" u2="&#x153;" k="10" />
    <hkern u1="&#x26;" u2="&#xf0;" k="10" />
    <hkern u1="&#x26;" u2="&#xdd;" k="51" />
    <hkern u1="&#x26;" u2="q" k="10" />
    <hkern u1="&#x26;" u2="o" k="10" />
    <hkern u1="&#x26;" u2="g" k="10" />
    <hkern u1="&#x26;" u2="e" k="10" />
    <hkern u1="&#x26;" u2="d" k="10" />
    <hkern u1="&#x26;" u2="c" k="10" />
    <hkern u1="&#x26;" u2="Y" k="51" />
    <hkern u1="&#x26;" u2="V" k="18" />
    <hkern u1="&#x26;" u2="T" k="46" />
    <hkern u1="&#x2a;" u2="&#x104;" k="34" />
    <hkern u1="&#x2a;" u2="&#x102;" k="34" />
    <hkern u1="&#x2a;" u2="&#x100;" k="34" />
    <hkern u1="&#x2a;" u2="&#xc5;" k="34" />
    <hkern u1="&#x2a;" u2="&#xc4;" k="34" />
    <hkern u1="&#x2a;" u2="&#xc3;" k="34" />
    <hkern u1="&#x2a;" u2="&#xc2;" k="34" />
    <hkern u1="&#x2a;" u2="&#xc1;" k="34" />
    <hkern u1="&#x2a;" u2="&#xc0;" k="34" />
    <hkern u1="&#x2a;" u2="&#xb8;" k="75" />
    <hkern u1="&#x2a;" u2="A" k="34" />
    <hkern u1="&#x2c;" u2="&#x45e;" k="45" />
    <hkern u1="&#x2c;" u2="&#x447;" k="73" />
    <hkern u1="&#x2c;" u2="&#x444;" k="30" />
    <hkern u1="&#x2c;" u2="&#x443;" k="45" />
    <hkern u1="&#x2c;" u2="&#x442;" k="54" />
    <hkern u1="&#x2c;" u2="&#x427;" k="84" />
    <hkern u1="&#x2c;" u2="&#x424;" k="60" />
    <hkern u1="&#x2c;" u2="&#x422;" k="59" />
    <hkern u1="&#x2c;" u2="&#x3c7;" k="43" />
    <hkern u1="&#x2c;" u2="&#x3c4;" k="68" />
    <hkern u1="&#x2c;" u2="&#x3c0;" k="47" />
    <hkern u1="&#x2c;" u2="&#x3bd;" k="60" />
    <hkern u1="&#x2c;" u2="&#x3b3;" k="55" />
    <hkern u1="&#x2c;" u2="&#x3ab;" k="100" />
    <hkern u1="&#x2c;" u2="&#x3a8;" k="77" />
    <hkern u1="&#x2c;" u2="&#x3a6;" k="40" />
    <hkern u1="&#x2c;" u2="&#x3a5;" k="100" />
    <hkern u1="&#x2c;" u2="&#x3a4;" k="64" />
    <hkern u1="&#x2c;" u2="&#x178;" k="79" />
    <hkern u1="&#x2c;" u2="&#x176;" k="79" />
    <hkern u1="&#x2c;" u2="&#x166;" k="75" />
    <hkern u1="&#x2c;" u2="&#x164;" k="75" />
    <hkern u1="&#x2c;" u2="&#x162;" k="75" />
    <hkern u1="&#x2c;" u2="&#x153;" k="27" />
    <hkern u1="&#x2c;" u2="&#x152;" k="41" />
    <hkern u1="&#x2c;" u2="&#xdd;" k="79" />
    <hkern u1="&#x2c;" u2="y" k="23" />
    <hkern u1="&#x2c;" u2="w" k="28" />
    <hkern u1="&#x2c;" u2="v" k="42" />
    <hkern u1="&#x2c;" u2="o" k="27" />
    <hkern u1="&#x2c;" u2="e" k="27" />
    <hkern u1="&#x2c;" u2="c" k="27" />
    <hkern u1="&#x2c;" u2="Y" k="79" />
    <hkern u1="&#x2c;" u2="W" k="25" />
    <hkern u1="&#x2c;" u2="V" k="50" />
    <hkern u1="&#x2c;" u2="T" k="75" />
    <hkern u1="&#x2c;" u2="Q" k="41" />
    <hkern u1="&#x2c;" u2="O" k="41" />
    <hkern u1="&#x2c;" u2="G" k="41" />
    <hkern u1="&#x2c;" u2="C" k="41" />
    <hkern u1="&#x2d;" u2="&#x422;" k="66" />
    <hkern u1="&#x2d;" u2="&#x3c7;" k="52" />
    <hkern u1="&#x2d;" u2="&#x3bb;" k="30" />
    <hkern u1="&#x2d;" u2="&#x3ab;" k="56" />
    <hkern u1="&#x2d;" u2="&#x3a5;" k="56" />
    <hkern u1="&#x2d;" u2="&#x3a4;" k="70" />
    <hkern u1="&#x2d;" u2="&#x178;" k="32" />
    <hkern u1="&#x2d;" u2="&#x176;" k="32" />
    <hkern u1="&#x2d;" u2="&#x166;" k="24" />
    <hkern u1="&#x2d;" u2="&#x164;" k="24" />
    <hkern u1="&#x2d;" u2="&#x162;" k="24" />
    <hkern u1="&#x2d;" u2="&#xdd;" k="32" />
    <hkern u1="&#x2d;" u2="Y" k="32" />
    <hkern u1="&#x2d;" u2="X" k="3" />
    <hkern u1="&#x2d;" u2="V" k="7" />
    <hkern u1="&#x2d;" u2="T" k="24" />
    <hkern u1="&#x2e;" u2="&#x45e;" k="45" />
    <hkern u1="&#x2e;" u2="&#x447;" k="73" />
    <hkern u1="&#x2e;" u2="&#x444;" k="30" />
    <hkern u1="&#x2e;" u2="&#x443;" k="45" />
    <hkern u1="&#x2e;" u2="&#x442;" k="54" />
    <hkern u1="&#x2e;" u2="&#x427;" k="84" />
    <hkern u1="&#x2e;" u2="&#x424;" k="60" />
    <hkern u1="&#x2e;" u2="&#x422;" k="59" />
    <hkern u1="&#x2e;" u2="&#x3c7;" k="43" />
    <hkern u1="&#x2e;" u2="&#x3c4;" k="68" />
    <hkern u1="&#x2e;" u2="&#x3c0;" k="47" />
    <hkern u1="&#x2e;" u2="&#x3bd;" k="60" />
    <hkern u1="&#x2e;" u2="&#x3b3;" k="55" />
    <hkern u1="&#x2e;" u2="&#x3ab;" k="100" />
    <hkern u1="&#x2e;" u2="&#x3a8;" k="77" />
    <hkern u1="&#x2e;" u2="&#x3a6;" k="40" />
    <hkern u1="&#x2e;" u2="&#x3a5;" k="100" />
    <hkern u1="&#x2e;" u2="&#x3a4;" k="64" />
    <hkern u1="&#x2e;" u2="&#x178;" k="79" />
    <hkern u1="&#x2e;" u2="&#x176;" k="79" />
    <hkern u1="&#x2e;" u2="&#x166;" k="75" />
    <hkern u1="&#x2e;" u2="&#x164;" k="75" />
    <hkern u1="&#x2e;" u2="&#x162;" k="75" />
    <hkern u1="&#x2e;" u2="&#x153;" k="27" />
    <hkern u1="&#x2e;" u2="&#x152;" k="41" />
    <hkern u1="&#x2e;" u2="&#xdd;" k="79" />
    <hkern u1="&#x2e;" u2="y" k="23" />
    <hkern u1="&#x2e;" u2="w" k="28" />
    <hkern u1="&#x2e;" u2="v" k="42" />
    <hkern u1="&#x2e;" u2="o" k="27" />
    <hkern u1="&#x2e;" u2="e" k="27" />
    <hkern u1="&#x2e;" u2="c" k="27" />
    <hkern u1="&#x2e;" u2="Y" k="79" />
    <hkern u1="&#x2e;" u2="W" k="25" />
    <hkern u1="&#x2e;" u2="V" k="50" />
    <hkern u1="&#x2e;" u2="T" k="75" />
    <hkern u1="&#x2e;" u2="Q" k="41" />
    <hkern u1="&#x2e;" u2="O" k="41" />
    <hkern u1="&#x2e;" u2="G" k="41" />
    <hkern u1="&#x2e;" u2="C" k="41" />
    <hkern u1="&#x2f;" u2="&#x153;" k="93" />
    <hkern u1="&#x2f;" u2="&#x14b;" k="51" />
    <hkern u1="&#x2f;" u2="&#x104;" k="102" />
    <hkern u1="&#x2f;" u2="&#x102;" k="102" />
    <hkern u1="&#x2f;" u2="&#x100;" k="102" />
    <hkern u1="&#x2f;" u2="&#xf0;" k="93" />
    <hkern u1="&#x2f;" u2="&#xe6;" k="84" />
    <hkern u1="&#x2f;" u2="&#xc6;" k="159" />
    <hkern u1="&#x2f;" u2="&#xc5;" k="102" />
    <hkern u1="&#x2f;" u2="&#xc4;" k="102" />
    <hkern u1="&#x2f;" u2="&#xc3;" k="102" />
    <hkern u1="&#x2f;" u2="&#xc2;" k="102" />
    <hkern u1="&#x2f;" u2="&#xc1;" k="102" />
    <hkern u1="&#x2f;" u2="&#xc0;" k="102" />
    <hkern u1="&#x2f;" u2="z" k="60" />
    <hkern u1="&#x2f;" u2="y" k="23" />
    <hkern u1="&#x2f;" u2="x" k="32" />
    <hkern u1="&#x2f;" u2="u" k="60" />
    <hkern u1="&#x2f;" u2="s" k="79" />
    <hkern u1="&#x2f;" u2="r" k="46" />
    <hkern u1="&#x2f;" u2="q" k="93" />
    <hkern u1="&#x2f;" u2="p" k="46" />
    <hkern u1="&#x2f;" u2="o" k="93" />
    <hkern u1="&#x2f;" u2="n" k="46" />
    <hkern u1="&#x2f;" u2="m" k="46" />
    <hkern u1="&#x2f;" u2="g" k="93" />
    <hkern u1="&#x2f;" u2="e" k="93" />
    <hkern u1="&#x2f;" u2="d" k="93" />
    <hkern u1="&#x2f;" u2="c" k="93" />
    <hkern u1="&#x2f;" u2="a" k="84" />
    <hkern u1="&#x2f;" u2="A" k="102" />
    <hkern u1="&#x40;" u2="y" k="-28" />
    <hkern u1="&#x40;" u2="p" k="-13" />
    <hkern u1="&#x40;" u2="j" k="-40" />
    <hkern u1="&#x40;" u2="g" k="-13" />
    <hkern u1="&#x40;" u2="Y" k="10" />
    <hkern u1="&#x40;" u2="W" k="5" />
    <hkern u1="&#x40;" u2="V" k="3" />
    <hkern u1="&#x40;" u2="J" k="-56" />
    <hkern u1="A" u2="&#x201d;" k="27" />
    <hkern u1="A" u2="&#x201c;" k="84" />
    <hkern u1="A" u2="&#x2019;" k="27" />
    <hkern u1="A" u2="&#x2018;" k="84" />
    <hkern u1="A" u2="&#x178;" k="90" />
    <hkern u1="A" u2="&#x177;" k="25" />
    <hkern u1="A" u2="&#x176;" k="90" />
    <hkern u1="A" u2="&#x175;" k="20" />
    <hkern u1="A" u2="&#x174;" k="35" />
    <hkern u1="A" u2="&#x172;" k="20" />
    <hkern u1="A" u2="&#x170;" k="20" />
    <hkern u1="A" u2="&#x16e;" k="20" />
    <hkern u1="A" u2="&#x16a;" k="20" />
    <hkern u1="A" u2="&#x167;" k="20" />
    <hkern u1="A" u2="&#x166;" k="75" />
    <hkern u1="A" u2="&#x165;" k="20" />
    <hkern u1="A" u2="&#x164;" k="75" />
    <hkern u1="A" u2="&#x163;" k="20" />
    <hkern u1="A" u2="&#x162;" k="75" />
    <hkern u1="A" u2="&#x152;" k="20" />
    <hkern u1="A" u2="&#x150;" k="20" />
    <hkern u1="A" u2="&#x14c;" k="20" />
    <hkern u1="A" u2="&#x134;" k="-47" />
    <hkern u1="A" u2="&#x122;" k="20" />
    <hkern u1="A" u2="&#x120;" k="20" />
    <hkern u1="A" u2="&#x11e;" k="20" />
    <hkern u1="A" u2="&#x11c;" k="20" />
    <hkern u1="A" u2="&#x10c;" k="20" />
    <hkern u1="A" u2="&#x108;" k="20" />
    <hkern u1="A" u2="&#x106;" k="20" />
    <hkern u1="A" u2="&#xff;" k="25" />
    <hkern u1="A" u2="&#xfd;" k="25" />
    <hkern u1="A" u2="&#xdd;" k="90" />
    <hkern u1="A" u2="&#xdc;" k="20" />
    <hkern u1="A" u2="&#xdb;" k="20" />
    <hkern u1="A" u2="&#xda;" k="20" />
    <hkern u1="A" u2="&#xd9;" k="20" />
    <hkern u1="A" u2="&#xd8;" k="20" />
    <hkern u1="A" u2="&#xd6;" k="20" />
    <hkern u1="A" u2="&#xd5;" k="20" />
    <hkern u1="A" u2="&#xd4;" k="20" />
    <hkern u1="A" u2="&#xd3;" k="20" />
    <hkern u1="A" u2="&#xd2;" k="20" />
    <hkern u1="A" u2="&#xc7;" k="20" />
    <hkern u1="A" u2="y" k="25" />
    <hkern u1="A" u2="w" k="20" />
    <hkern u1="A" u2="v" k="25" />
    <hkern u1="A" u2="t" k="20" />
    <hkern u1="A" u2="\" k="75" />
    <hkern u1="A" u2="Y" k="90" />
    <hkern u1="A" u2="W" k="35" />
    <hkern u1="A" u2="V" k="70" />
    <hkern u1="A" u2="U" k="20" />
    <hkern u1="A" u2="T" k="75" />
    <hkern u1="A" u2="Q" k="20" />
    <hkern u1="A" u2="O" k="20" />
    <hkern u1="A" u2="J" k="-47" />
    <hkern u1="A" u2="G" k="20" />
    <hkern u1="A" u2="C" k="20" />
    <hkern u1="A" u2="&#x2a;" k="34" />
    <hkern u1="B" u2="&#x178;" k="20" />
    <hkern u1="B" u2="&#x176;" k="20" />
    <hkern u1="B" u2="&#x166;" k="20" />
    <hkern u1="B" u2="&#x164;" k="20" />
    <hkern u1="B" u2="&#x162;" k="20" />
    <hkern u1="B" u2="&#xdd;" k="20" />
    <hkern u1="B" u2="z" k="5" />
    <hkern u1="B" u2="y" k="5" />
    <hkern u1="B" u2="t" k="5" />
    <hkern u1="B" u2="s" k="5" />
    <hkern u1="B" u2="Y" k="20" />
    <hkern u1="B" u2="V" k="5" />
    <hkern u1="B" u2="T" k="20" />
    <hkern u1="C" u2="&#x152;" k="10" />
    <hkern u1="C" u2="&#x150;" k="10" />
    <hkern u1="C" u2="&#x14c;" k="10" />
    <hkern u1="C" u2="&#x122;" k="10" />
    <hkern u1="C" u2="&#x120;" k="10" />
    <hkern u1="C" u2="&#x11e;" k="10" />
    <hkern u1="C" u2="&#x11c;" k="10" />
    <hkern u1="C" u2="&#x10c;" k="10" />
    <hkern u1="C" u2="&#x10a;" k="10" />
    <hkern u1="C" u2="&#x108;" k="10" />
    <hkern u1="C" u2="&#x106;" k="10" />
    <hkern u1="C" u2="&#xd8;" k="10" />
    <hkern u1="C" u2="&#xd6;" k="10" />
    <hkern u1="C" u2="&#xd5;" k="10" />
    <hkern u1="C" u2="&#xd4;" k="10" />
    <hkern u1="C" u2="&#xd3;" k="10" />
    <hkern u1="C" u2="&#xd2;" k="10" />
    <hkern u1="C" u2="&#xc7;" k="10" />
    <hkern u1="C" u2="Q" k="10" />
    <hkern u1="C" u2="O" k="10" />
    <hkern u1="C" u2="G" k="10" />
    <hkern u1="C" u2="C" k="10" />
    <hkern u1="D" u2="&#x2026;" k="50" />
    <hkern u1="D" u2="&#x178;" k="35" />
    <hkern u1="D" u2="&#x176;" k="35" />
    <hkern u1="D" u2="&#x166;" k="35" />
    <hkern u1="D" u2="&#x164;" k="35" />
    <hkern u1="D" u2="&#x162;" k="35" />
    <hkern u1="D" u2="&#x134;" k="41" />
    <hkern u1="D" u2="&#x104;" k="20" />
    <hkern u1="D" u2="&#x102;" k="20" />
    <hkern u1="D" u2="&#x100;" k="20" />
    <hkern u1="D" u2="&#xdd;" k="35" />
    <hkern u1="D" u2="&#xc6;" k="47" />
    <hkern u1="D" u2="&#xc5;" k="20" />
    <hkern u1="D" u2="&#xc4;" k="20" />
    <hkern u1="D" u2="&#xc3;" k="20" />
    <hkern u1="D" u2="&#xc2;" k="20" />
    <hkern u1="D" u2="&#xc1;" k="20" />
    <hkern u1="D" u2="&#xc0;" k="20" />
    <hkern u1="D" u2="z" k="5" />
    <hkern u1="D" u2="y" k="-10" />
    <hkern u1="D" u2="Z" k="25" />
    <hkern u1="D" u2="Y" k="35" />
    <hkern u1="D" u2="X" k="20" />
    <hkern u1="D" u2="V" k="10" />
    <hkern u1="D" u2="T" k="35" />
    <hkern u1="D" u2="J" k="41" />
    <hkern u1="D" u2="A" k="20" />
    <hkern u1="D" u2="&#x2e;" k="50" />
    <hkern u1="D" u2="&#x2c;" k="50" />
    <hkern u1="E" u2="&#x153;" k="25" />
    <hkern u1="E" u2="&#x152;" k="30" />
    <hkern u1="E" u2="&#xf0;" k="15" />
    <hkern u1="E" u2="y" k="15" />
    <hkern u1="E" u2="w" k="15" />
    <hkern u1="E" u2="v" k="20" />
    <hkern u1="E" u2="u" k="10" />
    <hkern u1="E" u2="t" k="15" />
    <hkern u1="E" u2="q" k="25" />
    <hkern u1="E" u2="o" k="25" />
    <hkern u1="E" u2="g" k="25" />
    <hkern u1="E" u2="e" k="25" />
    <hkern u1="E" u2="d" k="25" />
    <hkern u1="E" u2="c" k="25" />
    <hkern u1="E" u2="Q" k="30" />
    <hkern u1="E" u2="O" k="30" />
    <hkern u1="E" u2="G" k="30" />
    <hkern u1="E" u2="C" k="30" />
    <hkern u1="F" u2="&#x2026;" k="38" />
    <hkern u1="F" u2="&#x201c;" k="-13" />
    <hkern u1="F" u2="&#x2018;" k="-13" />
    <hkern u1="F" u2="&#x173;" k="15" />
    <hkern u1="F" u2="&#x171;" k="15" />
    <hkern u1="F" u2="&#x16f;" k="15" />
    <hkern u1="F" u2="&#x16d;" k="15" />
    <hkern u1="F" u2="&#x16b;" k="15" />
    <hkern u1="F" u2="&#x159;" k="37" />
    <hkern u1="F" u2="&#x157;" k="37" />
    <hkern u1="F" u2="&#x155;" k="37" />
    <hkern u1="F" u2="&#x153;" k="40" />
    <hkern u1="F" u2="&#x152;" k="15" />
    <hkern u1="F" u2="&#x151;" k="40" />
    <hkern u1="F" u2="&#x150;" k="15" />
    <hkern u1="F" u2="&#x14f;" k="40" />
    <hkern u1="F" u2="&#x14d;" k="40" />
    <hkern u1="F" u2="&#x14c;" k="15" />
    <hkern u1="F" u2="&#x134;" k="80" />
    <hkern u1="F" u2="&#x122;" k="15" />
    <hkern u1="F" u2="&#x120;" k="15" />
    <hkern u1="F" u2="&#x11e;" k="15" />
    <hkern u1="F" u2="&#x11c;" k="15" />
    <hkern u1="F" u2="&#x11b;" k="40" />
    <hkern u1="F" u2="&#x117;" k="40" />
    <hkern u1="F" u2="&#x115;" k="40" />
    <hkern u1="F" u2="&#x111;" k="40" />
    <hkern u1="F" u2="&#x10f;" k="40" />
    <hkern u1="F" u2="&#x10d;" k="40" />
    <hkern u1="F" u2="&#x10c;" k="15" />
    <hkern u1="F" u2="&#x10b;" k="40" />
    <hkern u1="F" u2="&#x10a;" k="15" />
    <hkern u1="F" u2="&#x109;" k="40" />
    <hkern u1="F" u2="&#x108;" k="15" />
    <hkern u1="F" u2="&#x107;" k="40" />
    <hkern u1="F" u2="&#x106;" k="15" />
    <hkern u1="F" u2="&#x105;" k="50" />
    <hkern u1="F" u2="&#x104;" k="75" />
    <hkern u1="F" u2="&#x103;" k="50" />
    <hkern u1="F" u2="&#x102;" k="75" />
    <hkern u1="F" u2="&#x101;" k="50" />
    <hkern u1="F" u2="&#x100;" k="75" />
    <hkern u1="F" u2="&#xfc;" k="15" />
    <hkern u1="F" u2="&#xfb;" k="15" />
    <hkern u1="F" u2="&#xfa;" k="15" />
    <hkern u1="F" u2="&#xf9;" k="15" />
    <hkern u1="F" u2="&#xf8;" k="40" />
    <hkern u1="F" u2="&#xf6;" k="40" />
    <hkern u1="F" u2="&#xf5;" k="40" />
    <hkern u1="F" u2="&#xf4;" k="40" />
    <hkern u1="F" u2="&#xf3;" k="40" />
    <hkern u1="F" u2="&#xf2;" k="40" />
    <hkern u1="F" u2="&#xf0;" k="30" />
    <hkern u1="F" u2="&#xeb;" k="40" />
    <hkern u1="F" u2="&#xea;" k="40" />
    <hkern u1="F" u2="&#xe9;" k="40" />
    <hkern u1="F" u2="&#xe8;" k="40" />
    <hkern u1="F" u2="&#xe7;" k="40" />
    <hkern u1="F" u2="&#xe6;" k="50" />
    <hkern u1="F" u2="&#xe5;" k="50" />
    <hkern u1="F" u2="&#xe4;" k="50" />
    <hkern u1="F" u2="&#xe3;" k="50" />
    <hkern u1="F" u2="&#xe2;" k="50" />
    <hkern u1="F" u2="&#xe1;" k="50" />
    <hkern u1="F" u2="&#xe0;" k="50" />
    <hkern u1="F" u2="&#xd8;" k="15" />
    <hkern u1="F" u2="&#xd6;" k="15" />
    <hkern u1="F" u2="&#xd5;" k="15" />
    <hkern u1="F" u2="&#xd4;" k="15" />
    <hkern u1="F" u2="&#xd3;" k="15" />
    <hkern u1="F" u2="&#xd2;" k="15" />
    <hkern u1="F" u2="&#xc7;" k="15" />
    <hkern u1="F" u2="&#xc6;" k="95" />
    <hkern u1="F" u2="&#xc5;" k="75" />
    <hkern u1="F" u2="&#xc4;" k="75" />
    <hkern u1="F" u2="&#xc3;" k="75" />
    <hkern u1="F" u2="&#xc2;" k="75" />
    <hkern u1="F" u2="&#xc1;" k="75" />
    <hkern u1="F" u2="&#xc0;" k="75" />
    <hkern u1="F" u2="&#xbf;" k="31" />
    <hkern u1="F" u2="z" k="15" />
    <hkern u1="F" u2="y" k="20" />
    <hkern u1="F" u2="w" k="20" />
    <hkern u1="F" u2="v" k="20" />
    <hkern u1="F" u2="u" k="15" />
    <hkern u1="F" u2="s" k="20" />
    <hkern u1="F" u2="r" k="37" />
    <hkern u1="F" u2="q" k="40" />
    <hkern u1="F" u2="p" k="37" />
    <hkern u1="F" u2="o" k="40" />
    <hkern u1="F" u2="n" k="37" />
    <hkern u1="F" u2="m" k="37" />
    <hkern u1="F" u2="g" k="40" />
    <hkern u1="F" u2="e" k="40" />
    <hkern u1="F" u2="d" k="40" />
    <hkern u1="F" u2="c" k="40" />
    <hkern u1="F" u2="a" k="50" />
    <hkern u1="F" u2="Q" k="15" />
    <hkern u1="F" u2="O" k="15" />
    <hkern u1="F" u2="J" k="80" />
    <hkern u1="F" u2="G" k="15" />
    <hkern u1="F" u2="C" k="15" />
    <hkern u1="F" u2="A" k="75" />
    <hkern u1="F" u2="&#x2e;" k="38" />
    <hkern u1="F" u2="&#x2c;" k="38" />
    <hkern u1="F" u2="&#x26;" k="28" />
    <hkern u1="G" u2="&#x178;" k="20" />
    <hkern u1="G" u2="&#x176;" k="20" />
    <hkern u1="G" u2="&#xdd;" k="20" />
    <hkern u1="G" u2="Y" k="20" />
    <hkern u1="G" u2="V" k="5" />
    <hkern u1="J" u2="&#x153;" k="5" />
    <hkern u1="J" u2="&#x104;" k="10" />
    <hkern u1="J" u2="&#x102;" k="10" />
    <hkern u1="J" u2="&#x100;" k="10" />
    <hkern u1="J" u2="&#xf0;" k="5" />
    <hkern u1="J" u2="&#xc5;" k="10" />
    <hkern u1="J" u2="&#xc4;" k="10" />
    <hkern u1="J" u2="&#xc3;" k="10" />
    <hkern u1="J" u2="&#xc2;" k="10" />
    <hkern u1="J" u2="&#xc1;" k="10" />
    <hkern u1="J" u2="&#xc0;" k="10" />
    <hkern u1="J" u2="q" k="5" />
    <hkern u1="J" u2="o" k="5" />
    <hkern u1="J" u2="g" k="5" />
    <hkern u1="J" u2="e" k="5" />
    <hkern u1="J" u2="d" k="5" />
    <hkern u1="J" u2="c" k="5" />
    <hkern u1="J" u2="A" k="10" />
    <hkern u1="K" u2="&#x2039;" k="30" />
    <hkern u1="K" u2="&#x172;" k="15" />
    <hkern u1="K" u2="&#x170;" k="15" />
    <hkern u1="K" u2="&#x16e;" k="15" />
    <hkern u1="K" u2="&#x16a;" k="15" />
    <hkern u1="K" u2="&#x153;" k="20" />
    <hkern u1="K" u2="&#x152;" k="25" />
    <hkern u1="K" u2="&#x150;" k="25" />
    <hkern u1="K" u2="&#x14e;" k="25" />
    <hkern u1="K" u2="&#x14c;" k="25" />
    <hkern u1="K" u2="&#x122;" k="25" />
    <hkern u1="K" u2="&#x120;" k="25" />
    <hkern u1="K" u2="&#x11e;" k="25" />
    <hkern u1="K" u2="&#x11c;" k="25" />
    <hkern u1="K" u2="&#x10c;" k="25" />
    <hkern u1="K" u2="&#x10a;" k="25" />
    <hkern u1="K" u2="&#x108;" k="25" />
    <hkern u1="K" u2="&#x106;" k="25" />
    <hkern u1="K" u2="&#xf0;" k="20" />
    <hkern u1="K" u2="&#xdc;" k="15" />
    <hkern u1="K" u2="&#xdb;" k="15" />
    <hkern u1="K" u2="&#xda;" k="15" />
    <hkern u1="K" u2="&#xd9;" k="15" />
    <hkern u1="K" u2="&#xd8;" k="25" />
    <hkern u1="K" u2="&#xd6;" k="25" />
    <hkern u1="K" u2="&#xd5;" k="25" />
    <hkern u1="K" u2="&#xd4;" k="25" />
    <hkern u1="K" u2="&#xd3;" k="25" />
    <hkern u1="K" u2="&#xd2;" k="25" />
    <hkern u1="K" u2="&#xc7;" k="25" />
    <hkern u1="K" u2="&#xab;" k="30" />
    <hkern u1="K" u2="y" k="35" />
    <hkern u1="K" u2="w" k="30" />
    <hkern u1="K" u2="v" k="30" />
    <hkern u1="K" u2="u" k="10" />
    <hkern u1="K" u2="t" k="20" />
    <hkern u1="K" u2="q" k="20" />
    <hkern u1="K" u2="o" k="20" />
    <hkern u1="K" u2="g" k="20" />
    <hkern u1="K" u2="e" k="20" />
    <hkern u1="K" u2="d" k="20" />
    <hkern u1="K" u2="c" k="20" />
    <hkern u1="K" u2="U" k="15" />
    <hkern u1="K" u2="Q" k="25" />
    <hkern u1="K" u2="O" k="25" />
    <hkern u1="K" u2="G" k="25" />
    <hkern u1="K" u2="C" k="25" />
    <hkern u1="L" u2="&#x201d;" k="27" />
    <hkern u1="L" u2="&#x201c;" k="90" />
    <hkern u1="L" u2="&#x2019;" k="27" />
    <hkern u1="L" u2="&#x2018;" k="90" />
    <hkern u1="L" u2="&#x178;" k="94" />
    <hkern u1="L" u2="&#x176;" k="94" />
    <hkern u1="L" u2="&#x174;" k="35" />
    <hkern u1="L" u2="&#x172;" k="25" />
    <hkern u1="L" u2="&#x170;" k="25" />
    <hkern u1="L" u2="&#x16e;" k="25" />
    <hkern u1="L" u2="&#x16c;" k="25" />
    <hkern u1="L" u2="&#x16a;" k="25" />
    <hkern u1="L" u2="&#x166;" k="90" />
    <hkern u1="L" u2="&#x164;" k="90" />
    <hkern u1="L" u2="&#x162;" k="90" />
    <hkern u1="L" u2="&#x153;" k="15" />
    <hkern u1="L" u2="&#x152;" k="30" />
    <hkern u1="L" u2="&#x150;" k="30" />
    <hkern u1="L" u2="&#x14e;" k="30" />
    <hkern u1="L" u2="&#x14c;" k="30" />
    <hkern u1="L" u2="&#x134;" k="-33" />
    <hkern u1="L" u2="&#x122;" k="30" />
    <hkern u1="L" u2="&#x120;" k="30" />
    <hkern u1="L" u2="&#x11e;" k="30" />
    <hkern u1="L" u2="&#x11c;" k="30" />
    <hkern u1="L" u2="&#x10c;" k="30" />
    <hkern u1="L" u2="&#x10a;" k="30" />
    <hkern u1="L" u2="&#x108;" k="30" />
    <hkern u1="L" u2="&#x106;" k="30" />
    <hkern u1="L" u2="&#xf0;" k="15" />
    <hkern u1="L" u2="&#xdd;" k="94" />
    <hkern u1="L" u2="&#xdc;" k="25" />
    <hkern u1="L" u2="&#xdb;" k="25" />
    <hkern u1="L" u2="&#xda;" k="25" />
    <hkern u1="L" u2="&#xd9;" k="25" />
    <hkern u1="L" u2="&#xd8;" k="30" />
    <hkern u1="L" u2="&#xd6;" k="30" />
    <hkern u1="L" u2="&#xd5;" k="30" />
    <hkern u1="L" u2="&#xd4;" k="30" />
    <hkern u1="L" u2="&#xd3;" k="30" />
    <hkern u1="L" u2="&#xd2;" k="30" />
    <hkern u1="L" u2="&#xc7;" k="30" />
    <hkern u1="L" u2="y" k="40" />
    <hkern u1="L" u2="w" k="28" />
    <hkern u1="L" u2="v" k="51" />
    <hkern u1="L" u2="u" k="15" />
    <hkern u1="L" u2="t" k="25" />
    <hkern u1="L" u2="q" k="15" />
    <hkern u1="L" u2="o" k="15" />
    <hkern u1="L" u2="g" k="15" />
    <hkern u1="L" u2="e" k="15" />
    <hkern u1="L" u2="d" k="15" />
    <hkern u1="L" u2="c" k="15" />
    <hkern u1="L" u2="\" k="108" />
    <hkern u1="L" u2="Y" k="94" />
    <hkern u1="L" u2="W" k="35" />
    <hkern u1="L" u2="V" k="70" />
    <hkern u1="L" u2="U" k="25" />
    <hkern u1="L" u2="T" k="90" />
    <hkern u1="L" u2="Q" k="30" />
    <hkern u1="L" u2="O" k="30" />
    <hkern u1="L" u2="J" k="-33" />
    <hkern u1="L" u2="G" k="30" />
    <hkern u1="L" u2="C" k="30" />
    <hkern u1="L" u2="&#x2a;" k="85" />
    <hkern u1="O" u2="&#x2026;" k="36" />
    <hkern u1="O" u2="&#x178;" k="40" />
    <hkern u1="O" u2="&#x176;" k="40" />
    <hkern u1="O" u2="&#x166;" k="25" />
    <hkern u1="O" u2="&#x164;" k="25" />
    <hkern u1="O" u2="&#x162;" k="25" />
    <hkern u1="O" u2="&#x134;" k="10" />
    <hkern u1="O" u2="&#x104;" k="20" />
    <hkern u1="O" u2="&#x102;" k="20" />
    <hkern u1="O" u2="&#x100;" k="20" />
    <hkern u1="O" u2="&#xe6;" k="5" />
    <hkern u1="O" u2="&#xdd;" k="40" />
    <hkern u1="O" u2="&#xc6;" k="33" />
    <hkern u1="O" u2="&#xc5;" k="20" />
    <hkern u1="O" u2="&#xc4;" k="20" />
    <hkern u1="O" u2="&#xc3;" k="20" />
    <hkern u1="O" u2="&#xc2;" k="20" />
    <hkern u1="O" u2="&#xc1;" k="20" />
    <hkern u1="O" u2="&#xc0;" k="20" />
    <hkern u1="O" u2="z" k="15" />
    <hkern u1="O" u2="s" k="10" />
    <hkern u1="O" u2="a" k="5" />
    <hkern u1="O" u2="Z" k="25" />
    <hkern u1="O" u2="Y" k="40" />
    <hkern u1="O" u2="X" k="25" />
    <hkern u1="O" u2="V" k="15" />
    <hkern u1="O" u2="T" k="25" />
    <hkern u1="O" u2="S" k="10" />
    <hkern u1="O" u2="J" k="10" />
    <hkern u1="O" u2="A" k="20" />
    <hkern u1="O" u2="&#x2e;" k="36" />
    <hkern u1="O" u2="&#x2c;" k="36" />
    <hkern u1="P" u2="&#x2026;" k="58" />
    <hkern u1="P" u2="&#x178;" k="5" />
    <hkern u1="P" u2="&#x176;" k="5" />
    <hkern u1="P" u2="&#x153;" k="10" />
    <hkern u1="P" u2="&#x151;" k="10" />
    <hkern u1="P" u2="&#x14f;" k="10" />
    <hkern u1="P" u2="&#x14d;" k="10" />
    <hkern u1="P" u2="&#x134;" k="90" />
    <hkern u1="P" u2="&#x11b;" k="10" />
    <hkern u1="P" u2="&#x119;" k="10" />
    <hkern u1="P" u2="&#x117;" k="10" />
    <hkern u1="P" u2="&#x115;" k="10" />
    <hkern u1="P" u2="&#x113;" k="10" />
    <hkern u1="P" u2="&#x105;" k="20" />
    <hkern u1="P" u2="&#x104;" k="65" />
    <hkern u1="P" u2="&#x102;" k="65" />
    <hkern u1="P" u2="&#x101;" k="20" />
    <hkern u1="P" u2="&#x100;" k="65" />
    <hkern u1="P" u2="&#xf8;" k="10" />
    <hkern u1="P" u2="&#xf6;" k="10" />
    <hkern u1="P" u2="&#xf5;" k="10" />
    <hkern u1="P" u2="&#xf4;" k="10" />
    <hkern u1="P" u2="&#xf3;" k="10" />
    <hkern u1="P" u2="&#xf2;" k="10" />
    <hkern u1="P" u2="&#xf0;" k="10" />
    <hkern u1="P" u2="&#xeb;" k="10" />
    <hkern u1="P" u2="&#xea;" k="10" />
    <hkern u1="P" u2="&#xe9;" k="10" />
    <hkern u1="P" u2="&#xe8;" k="10" />
    <hkern u1="P" u2="&#xe6;" k="20" />
    <hkern u1="P" u2="&#xe5;" k="20" />
    <hkern u1="P" u2="&#xe4;" k="20" />
    <hkern u1="P" u2="&#xe3;" k="20" />
    <hkern u1="P" u2="&#xe2;" k="20" />
    <hkern u1="P" u2="&#xe1;" k="20" />
    <hkern u1="P" u2="&#xe0;" k="20" />
    <hkern u1="P" u2="&#xdd;" k="5" />
    <hkern u1="P" u2="&#xc6;" k="88" />
    <hkern u1="P" u2="&#xc5;" k="65" />
    <hkern u1="P" u2="&#xc4;" k="65" />
    <hkern u1="P" u2="&#xc3;" k="65" />
    <hkern u1="P" u2="&#xc2;" k="65" />
    <hkern u1="P" u2="&#xc1;" k="65" />
    <hkern u1="P" u2="&#xc0;" k="65" />
    <hkern u1="P" u2="&#xbf;" k="25" />
    <hkern u1="P" u2="y" k="-20" />
    <hkern u1="P" u2="w" k="-25" />
    <hkern u1="P" u2="v" k="-20" />
    <hkern u1="P" u2="q" k="10" />
    <hkern u1="P" u2="o" k="10" />
    <hkern u1="P" u2="g" k="10" />
    <hkern u1="P" u2="e" k="10" />
    <hkern u1="P" u2="d" k="10" />
    <hkern u1="P" u2="c" k="10" />
    <hkern u1="P" u2="a" k="20" />
    <hkern u1="P" u2="Z" k="40" />
    <hkern u1="P" u2="Y" k="5" />
    <hkern u1="P" u2="X" k="10" />
    <hkern u1="P" u2="J" k="90" />
    <hkern u1="P" u2="A" k="65" />
    <hkern u1="P" u2="&#x2e;" k="58" />
    <hkern u1="P" u2="&#x2c;" k="58" />
    <hkern u1="P" u2="&#x26;" k="32" />
    <hkern u1="Q" u2="&#x178;" k="40" />
    <hkern u1="Q" u2="&#x176;" k="40" />
    <hkern u1="Q" u2="&#x166;" k="25" />
    <hkern u1="Q" u2="&#x164;" k="25" />
    <hkern u1="Q" u2="&#x162;" k="25" />
    <hkern u1="Q" u2="&#x104;" k="20" />
    <hkern u1="Q" u2="&#x102;" k="20" />
    <hkern u1="Q" u2="&#x100;" k="20" />
    <hkern u1="Q" u2="&#xe6;" k="5" />
    <hkern u1="Q" u2="&#xdd;" k="40" />
    <hkern u1="Q" u2="&#xc6;" k="33" />
    <hkern u1="Q" u2="&#xc5;" k="20" />
    <hkern u1="Q" u2="&#xc4;" k="20" />
    <hkern u1="Q" u2="&#xc3;" k="20" />
    <hkern u1="Q" u2="&#xc2;" k="20" />
    <hkern u1="Q" u2="&#xc1;" k="20" />
    <hkern u1="Q" u2="&#xc0;" k="20" />
    <hkern u1="Q" u2="z" k="15" />
    <hkern u1="Q" u2="s" k="10" />
    <hkern u1="Q" u2="a" k="5" />
    <hkern u1="Q" u2="Z" k="25" />
    <hkern u1="Q" u2="Y" k="40" />
    <hkern u1="Q" u2="X" k="25" />
    <hkern u1="Q" u2="V" k="15" />
    <hkern u1="Q" u2="T" k="25" />
    <hkern u1="Q" u2="S" k="10" />
    <hkern u1="Q" u2="J" k="10" />
    <hkern u1="Q" u2="A" k="20" />
    <hkern u1="R" u2="&#x178;" k="10" />
    <hkern u1="R" u2="&#x176;" k="10" />
    <hkern u1="R" u2="&#x173;" k="5" />
    <hkern u1="R" u2="&#x172;" k="10" />
    <hkern u1="R" u2="&#x171;" k="5" />
    <hkern u1="R" u2="&#x170;" k="10" />
    <hkern u1="R" u2="&#x16f;" k="5" />
    <hkern u1="R" u2="&#x16e;" k="10" />
    <hkern u1="R" u2="&#x16c;" k="10" />
    <hkern u1="R" u2="&#x16b;" k="5" />
    <hkern u1="R" u2="&#x16a;" k="10" />
    <hkern u1="R" u2="&#x166;" k="10" />
    <hkern u1="R" u2="&#x164;" k="10" />
    <hkern u1="R" u2="&#x162;" k="10" />
    <hkern u1="R" u2="&#x153;" k="15" />
    <hkern u1="R" u2="&#x152;" k="10" />
    <hkern u1="R" u2="&#x151;" k="15" />
    <hkern u1="R" u2="&#x150;" k="10" />
    <hkern u1="R" u2="&#x14f;" k="15" />
    <hkern u1="R" u2="&#x14d;" k="15" />
    <hkern u1="R" u2="&#x14c;" k="10" />
    <hkern u1="R" u2="&#x122;" k="10" />
    <hkern u1="R" u2="&#x120;" k="10" />
    <hkern u1="R" u2="&#x11e;" k="10" />
    <hkern u1="R" u2="&#x11c;" k="10" />
    <hkern u1="R" u2="&#x11b;" k="15" />
    <hkern u1="R" u2="&#x119;" k="15" />
    <hkern u1="R" u2="&#x117;" k="15" />
    <hkern u1="R" u2="&#x115;" k="15" />
    <hkern u1="R" u2="&#x113;" k="15" />
    <hkern u1="R" u2="&#x10c;" k="10" />
    <hkern u1="R" u2="&#x10a;" k="10" />
    <hkern u1="R" u2="&#x108;" k="10" />
    <hkern u1="R" u2="&#x106;" k="10" />
    <hkern u1="R" u2="&#xfc;" k="5" />
    <hkern u1="R" u2="&#xfb;" k="5" />
    <hkern u1="R" u2="&#xfa;" k="5" />
    <hkern u1="R" u2="&#xf9;" k="5" />
    <hkern u1="R" u2="&#xf8;" k="15" />
    <hkern u1="R" u2="&#xf6;" k="15" />
    <hkern u1="R" u2="&#xf5;" k="15" />
    <hkern u1="R" u2="&#xf4;" k="15" />
    <hkern u1="R" u2="&#xf3;" k="15" />
    <hkern u1="R" u2="&#xf2;" k="15" />
    <hkern u1="R" u2="&#xf0;" k="15" />
    <hkern u1="R" u2="&#xeb;" k="15" />
    <hkern u1="R" u2="&#xea;" k="15" />
    <hkern u1="R" u2="&#xe9;" k="15" />
    <hkern u1="R" u2="&#xe8;" k="15" />
    <hkern u1="R" u2="&#xdd;" k="10" />
    <hkern u1="R" u2="&#xdc;" k="10" />
    <hkern u1="R" u2="&#xdb;" k="10" />
    <hkern u1="R" u2="&#xda;" k="10" />
    <hkern u1="R" u2="&#xd9;" k="10" />
    <hkern u1="R" u2="&#xd8;" k="10" />
    <hkern u1="R" u2="&#xd6;" k="10" />
    <hkern u1="R" u2="&#xd5;" k="10" />
    <hkern u1="R" u2="&#xd4;" k="10" />
    <hkern u1="R" u2="&#xd3;" k="10" />
    <hkern u1="R" u2="&#xd2;" k="10" />
    <hkern u1="R" u2="&#xc7;" k="10" />
    <hkern u1="R" u2="y" k="-10" />
    <hkern u1="R" u2="u" k="5" />
    <hkern u1="R" u2="q" k="15" />
    <hkern u1="R" u2="o" k="15" />
    <hkern u1="R" u2="g" k="15" />
    <hkern u1="R" u2="e" k="15" />
    <hkern u1="R" u2="d" k="15" />
    <hkern u1="R" u2="c" k="15" />
    <hkern u1="R" u2="Y" k="10" />
    <hkern u1="R" u2="V" k="5" />
    <hkern u1="R" u2="U" k="10" />
    <hkern u1="R" u2="T" k="10" />
    <hkern u1="R" u2="Q" k="10" />
    <hkern u1="R" u2="O" k="10" />
    <hkern u1="R" u2="G" k="10" />
    <hkern u1="R" u2="C" k="10" />
    <hkern u1="S" u2="z" k="5" />
    <hkern u1="S" u2="y" k="10" />
    <hkern u1="S" u2="x" k="5" />
    <hkern u1="S" u2="w" k="5" />
    <hkern u1="S" u2="v" k="10" />
    <hkern u1="S" u2="t" k="10" />
    <hkern u1="S" u2="S" k="5" />
    <hkern u1="T" u2="&#x203a;" k="45" />
    <hkern u1="T" u2="&#x2039;" k="51" />
    <hkern u1="T" u2="&#x2026;" k="75" />
    <hkern u1="T" u2="&#x2014;" k="24" />
    <hkern u1="T" u2="&#x2013;" k="24" />
    <hkern u1="T" u2="&#x219;" k="75" />
    <hkern u1="T" u2="&#x177;" k="51" />
    <hkern u1="T" u2="&#x175;" k="47" />
    <hkern u1="T" u2="&#x173;" k="75" />
    <hkern u1="T" u2="&#x171;" k="75" />
    <hkern u1="T" u2="&#x16f;" k="75" />
    <hkern u1="T" u2="&#x16d;" k="75" />
    <hkern u1="T" u2="&#x16b;" k="75" />
    <hkern u1="T" u2="&#x161;" k="55" />
    <hkern u1="T" u2="&#x15f;" k="75" />
    <hkern u1="T" u2="&#x15d;" k="75" />
    <hkern u1="T" u2="&#x15b;" k="75" />
    <hkern u1="T" u2="&#x159;" k="40" />
    <hkern u1="T" u2="&#x157;" k="75" />
    <hkern u1="T" u2="&#x155;" k="75" />
    <hkern u1="T" u2="&#x153;" k="70" />
    <hkern u1="T" u2="&#x152;" k="40" />
    <hkern u1="T" u2="&#x151;" k="70" />
    <hkern u1="T" u2="&#x150;" k="40" />
    <hkern u1="T" u2="&#x14f;" k="70" />
    <hkern u1="T" u2="&#x14e;" k="40" />
    <hkern u1="T" u2="&#x14d;" k="70" />
    <hkern u1="T" u2="&#x14c;" k="40" />
    <hkern u1="T" u2="&#x14b;" k="80" />
    <hkern u1="T" u2="&#x134;" k="90" />
    <hkern u1="T" u2="&#x122;" k="40" />
    <hkern u1="T" u2="&#x120;" k="40" />
    <hkern u1="T" u2="&#x11e;" k="40" />
    <hkern u1="T" u2="&#x11c;" k="40" />
    <hkern u1="T" u2="&#x11b;" k="70" />
    <hkern u1="T" u2="&#x117;" k="70" />
    <hkern u1="T" u2="&#x115;" k="70" />
    <hkern u1="T" u2="&#x10c;" k="40" />
    <hkern u1="T" u2="&#x10a;" k="40" />
    <hkern u1="T" u2="&#x109;" k="70" />
    <hkern u1="T" u2="&#x108;" k="40" />
    <hkern u1="T" u2="&#x107;" k="70" />
    <hkern u1="T" u2="&#x106;" k="40" />
    <hkern u1="T" u2="&#x105;" k="84" />
    <hkern u1="T" u2="&#x104;" k="75" />
    <hkern u1="T" u2="&#x103;" k="50" />
    <hkern u1="T" u2="&#x102;" k="75" />
    <hkern u1="T" u2="&#x101;" k="54" />
    <hkern u1="T" u2="&#x100;" k="75" />
    <hkern u1="T" u2="&#xff;" k="51" />
    <hkern u1="T" u2="&#xfd;" k="51" />
    <hkern u1="T" u2="&#xfc;" k="75" />
    <hkern u1="T" u2="&#xfb;" k="75" />
    <hkern u1="T" u2="&#xfa;" k="75" />
    <hkern u1="T" u2="&#xf9;" k="75" />
    <hkern u1="T" u2="&#xf8;" k="70" />
    <hkern u1="T" u2="&#xf6;" k="70" />
    <hkern u1="T" u2="&#xf5;" k="70" />
    <hkern u1="T" u2="&#xf4;" k="70" />
    <hkern u1="T" u2="&#xf3;" k="70" />
    <hkern u1="T" u2="&#xf2;" k="70" />
    <hkern u1="T" u2="&#xf0;" k="70" />
    <hkern u1="T" u2="&#xeb;" k="70" />
    <hkern u1="T" u2="&#xea;" k="70" />
    <hkern u1="T" u2="&#xe9;" k="70" />
    <hkern u1="T" u2="&#xe8;" k="70" />
    <hkern u1="T" u2="&#xe7;" k="70" />
    <hkern u1="T" u2="&#xe6;" k="84" />
    <hkern u1="T" u2="&#xe5;" k="84" />
    <hkern u1="T" u2="&#xe4;" k="54" />
    <hkern u1="T" u2="&#xe3;" k="54" />
    <hkern u1="T" u2="&#xe2;" k="84" />
    <hkern u1="T" u2="&#xe1;" k="84" />
    <hkern u1="T" u2="&#xe0;" k="84" />
    <hkern u1="T" u2="&#xd8;" k="40" />
    <hkern u1="T" u2="&#xd6;" k="40" />
    <hkern u1="T" u2="&#xd5;" k="40" />
    <hkern u1="T" u2="&#xd4;" k="40" />
    <hkern u1="T" u2="&#xd3;" k="40" />
    <hkern u1="T" u2="&#xd2;" k="40" />
    <hkern u1="T" u2="&#xc7;" k="40" />
    <hkern u1="T" u2="&#xc6;" k="79" />
    <hkern u1="T" u2="&#xc5;" k="75" />
    <hkern u1="T" u2="&#xc4;" k="75" />
    <hkern u1="T" u2="&#xc3;" k="75" />
    <hkern u1="T" u2="&#xc2;" k="75" />
    <hkern u1="T" u2="&#xc1;" k="75" />
    <hkern u1="T" u2="&#xc0;" k="75" />
    <hkern u1="T" u2="&#xbf;" k="85" />
    <hkern u1="T" u2="&#xbb;" k="45" />
    <hkern u1="T" u2="&#xab;" k="51" />
    <hkern u1="T" u2="z" k="70" />
    <hkern u1="T" u2="y" k="51" />
    <hkern u1="T" u2="x" k="47" />
    <hkern u1="T" u2="w" k="47" />
    <hkern u1="T" u2="v" k="37" />
    <hkern u1="T" u2="u" k="75" />
    <hkern u1="T" u2="s" k="75" />
    <hkern u1="T" u2="r" k="75" />
    <hkern u1="T" u2="q" k="70" />
    <hkern u1="T" u2="p" k="75" />
    <hkern u1="T" u2="o" k="70" />
    <hkern u1="T" u2="n" k="75" />
    <hkern u1="T" u2="m" k="75" />
    <hkern u1="T" u2="g" k="70" />
    <hkern u1="T" u2="e" k="70" />
    <hkern u1="T" u2="d" k="70" />
    <hkern u1="T" u2="c" k="70" />
    <hkern u1="T" u2="a" k="84" />
    <hkern u1="T" u2="Q" k="40" />
    <hkern u1="T" u2="O" k="40" />
    <hkern u1="T" u2="J" k="90" />
    <hkern u1="T" u2="G" k="40" />
    <hkern u1="T" u2="C" k="40" />
    <hkern u1="T" u2="A" k="75" />
    <hkern u1="T" u2="&#x40;" k="18" />
    <hkern u1="T" u2="&#x3b;" k="15" />
    <hkern u1="T" u2="&#x3a;" k="15" />
    <hkern u1="T" u2="&#x2e;" k="75" />
    <hkern u1="T" u2="&#x2d;" k="24" />
    <hkern u1="T" u2="&#x2c;" k="75" />
    <hkern u1="T" u2="&#x26;" k="42" />
    <hkern u1="U" u2="&#x153;" k="10" />
    <hkern u1="U" u2="&#x104;" k="20" />
    <hkern u1="U" u2="&#x102;" k="20" />
    <hkern u1="U" u2="&#x100;" k="20" />
    <hkern u1="U" u2="&#xf0;" k="10" />
    <hkern u1="U" u2="&#xe6;" k="15" />
    <hkern u1="U" u2="&#xc6;" k="33" />
    <hkern u1="U" u2="&#xc5;" k="20" />
    <hkern u1="U" u2="&#xc4;" k="20" />
    <hkern u1="U" u2="&#xc3;" k="20" />
    <hkern u1="U" u2="&#xc2;" k="20" />
    <hkern u1="U" u2="&#xc1;" k="20" />
    <hkern u1="U" u2="&#xc0;" k="20" />
    <hkern u1="U" u2="z" k="25" />
    <hkern u1="U" u2="y" k="5" />
    <hkern u1="U" u2="x" k="10" />
    <hkern u1="U" u2="t" k="10" />
    <hkern u1="U" u2="s" k="15" />
    <hkern u1="U" u2="q" k="10" />
    <hkern u1="U" u2="o" k="10" />
    <hkern u1="U" u2="g" k="10" />
    <hkern u1="U" u2="e" k="10" />
    <hkern u1="U" u2="d" k="10" />
    <hkern u1="U" u2="c" k="10" />
    <hkern u1="U" u2="a" k="15" />
    <hkern u1="U" u2="Z" k="20" />
    <hkern u1="U" u2="J" k="30" />
    <hkern u1="U" u2="A" k="20" />
    <hkern u1="V" u2="&#x2039;" k="28" />
    <hkern u1="V" u2="&#x2026;" k="51" />
    <hkern u1="V" u2="&#x2014;" k="7" />
    <hkern u1="V" u2="&#x2013;" k="7" />
    <hkern u1="V" u2="&#x219;" k="37" />
    <hkern u1="V" u2="&#x173;" k="30" />
    <hkern u1="V" u2="&#x171;" k="30" />
    <hkern u1="V" u2="&#x16f;" k="30" />
    <hkern u1="V" u2="&#x16d;" k="30" />
    <hkern u1="V" u2="&#x16b;" k="30" />
    <hkern u1="V" u2="&#x161;" k="37" />
    <hkern u1="V" u2="&#x15f;" k="37" />
    <hkern u1="V" u2="&#x15d;" k="37" />
    <hkern u1="V" u2="&#x15b;" k="37" />
    <hkern u1="V" u2="&#x159;" k="28" />
    <hkern u1="V" u2="&#x157;" k="28" />
    <hkern u1="V" u2="&#x155;" k="28" />
    <hkern u1="V" u2="&#x153;" k="42" />
    <hkern u1="V" u2="&#x152;" k="25" />
    <hkern u1="V" u2="&#x151;" k="42" />
    <hkern u1="V" u2="&#x150;" k="25" />
    <hkern u1="V" u2="&#x14f;" k="42" />
    <hkern u1="V" u2="&#x14e;" k="25" />
    <hkern u1="V" u2="&#x14d;" k="42" />
    <hkern u1="V" u2="&#x14c;" k="25" />
    <hkern u1="V" u2="&#x14b;" k="33" />
    <hkern u1="V" u2="&#x134;" k="85" />
    <hkern u1="V" u2="&#x122;" k="25" />
    <hkern u1="V" u2="&#x120;" k="25" />
    <hkern u1="V" u2="&#x11e;" k="25" />
    <hkern u1="V" u2="&#x11c;" k="25" />
    <hkern u1="V" u2="&#x11b;" k="42" />
    <hkern u1="V" u2="&#x119;" k="42" />
    <hkern u1="V" u2="&#x117;" k="42" />
    <hkern u1="V" u2="&#x115;" k="42" />
    <hkern u1="V" u2="&#x113;" k="42" />
    <hkern u1="V" u2="&#x10c;" k="25" />
    <hkern u1="V" u2="&#x10a;" k="25" />
    <hkern u1="V" u2="&#x108;" k="25" />
    <hkern u1="V" u2="&#x106;" k="25" />
    <hkern u1="V" u2="&#x105;" k="47" />
    <hkern u1="V" u2="&#x104;" k="70" />
    <hkern u1="V" u2="&#x103;" k="47" />
    <hkern u1="V" u2="&#x102;" k="70" />
    <hkern u1="V" u2="&#x101;" k="47" />
    <hkern u1="V" u2="&#x100;" k="70" />
    <hkern u1="V" u2="&#xfc;" k="30" />
    <hkern u1="V" u2="&#xfb;" k="30" />
    <hkern u1="V" u2="&#xfa;" k="30" />
    <hkern u1="V" u2="&#xf9;" k="30" />
    <hkern u1="V" u2="&#xf8;" k="42" />
    <hkern u1="V" u2="&#xf6;" k="42" />
    <hkern u1="V" u2="&#xf5;" k="42" />
    <hkern u1="V" u2="&#xf4;" k="42" />
    <hkern u1="V" u2="&#xf3;" k="42" />
    <hkern u1="V" u2="&#xf2;" k="42" />
    <hkern u1="V" u2="&#xf0;" k="42" />
    <hkern u1="V" u2="&#xeb;" k="42" />
    <hkern u1="V" u2="&#xea;" k="42" />
    <hkern u1="V" u2="&#xe9;" k="42" />
    <hkern u1="V" u2="&#xe8;" k="42" />
    <hkern u1="V" u2="&#xe6;" k="47" />
    <hkern u1="V" u2="&#xe5;" k="47" />
    <hkern u1="V" u2="&#xe4;" k="47" />
    <hkern u1="V" u2="&#xe3;" k="47" />
    <hkern u1="V" u2="&#xe2;" k="47" />
    <hkern u1="V" u2="&#xe1;" k="47" />
    <hkern u1="V" u2="&#xe0;" k="47" />
    <hkern u1="V" u2="&#xd8;" k="25" />
    <hkern u1="V" u2="&#xd6;" k="25" />
    <hkern u1="V" u2="&#xd5;" k="25" />
    <hkern u1="V" u2="&#xd4;" k="25" />
    <hkern u1="V" u2="&#xd3;" k="25" />
    <hkern u1="V" u2="&#xd2;" k="25" />
    <hkern u1="V" u2="&#xc7;" k="25" />
    <hkern u1="V" u2="&#xc6;" k="89" />
    <hkern u1="V" u2="&#xc5;" k="70" />
    <hkern u1="V" u2="&#xc4;" k="70" />
    <hkern u1="V" u2="&#xc3;" k="70" />
    <hkern u1="V" u2="&#xc2;" k="70" />
    <hkern u1="V" u2="&#xc1;" k="70" />
    <hkern u1="V" u2="&#xc0;" k="70" />
    <hkern u1="V" u2="&#xbf;" k="55" />
    <hkern u1="V" u2="&#xab;" k="28" />
    <hkern u1="V" u2="z" k="23" />
    <hkern u1="V" u2="u" k="30" />
    <hkern u1="V" u2="s" k="37" />
    <hkern u1="V" u2="r" k="28" />
    <hkern u1="V" u2="q" k="42" />
    <hkern u1="V" u2="p" k="28" />
    <hkern u1="V" u2="o" k="42" />
    <hkern u1="V" u2="n" k="28" />
    <hkern u1="V" u2="m" k="28" />
    <hkern u1="V" u2="g" k="42" />
    <hkern u1="V" u2="e" k="42" />
    <hkern u1="V" u2="d" k="42" />
    <hkern u1="V" u2="c" k="42" />
    <hkern u1="V" u2="a" k="47" />
    <hkern u1="V" u2="Q" k="25" />
    <hkern u1="V" u2="O" k="25" />
    <hkern u1="V" u2="J" k="85" />
    <hkern u1="V" u2="G" k="25" />
    <hkern u1="V" u2="C" k="25" />
    <hkern u1="V" u2="A" k="70" />
    <hkern u1="V" u2="&#x40;" k="15" />
    <hkern u1="V" u2="&#x3f;" k="-18" />
    <hkern u1="V" u2="&#x2e;" k="51" />
    <hkern u1="V" u2="&#x2d;" k="7" />
    <hkern u1="V" u2="&#x2c;" k="51" />
    <hkern u1="V" u2="&#x26;" k="47" />
    <hkern u1="W" u2="&#x2026;" k="25" />
    <hkern u1="W" u2="&#x219;" k="25" />
    <hkern u1="W" u2="&#x173;" k="15" />
    <hkern u1="W" u2="&#x171;" k="15" />
    <hkern u1="W" u2="&#x16f;" k="15" />
    <hkern u1="W" u2="&#x16d;" k="15" />
    <hkern u1="W" u2="&#x16b;" k="15" />
    <hkern u1="W" u2="&#x161;" k="25" />
    <hkern u1="W" u2="&#x15f;" k="25" />
    <hkern u1="W" u2="&#x15d;" k="25" />
    <hkern u1="W" u2="&#x15b;" k="25" />
    <hkern u1="W" u2="&#x159;" k="15" />
    <hkern u1="W" u2="&#x157;" k="15" />
    <hkern u1="W" u2="&#x155;" k="15" />
    <hkern u1="W" u2="&#x153;" k="20" />
    <hkern u1="W" u2="&#x151;" k="20" />
    <hkern u1="W" u2="&#x14f;" k="20" />
    <hkern u1="W" u2="&#x14d;" k="20" />
    <hkern u1="W" u2="&#x14b;" k="20" />
    <hkern u1="W" u2="&#x134;" k="40" />
    <hkern u1="W" u2="&#x11b;" k="20" />
    <hkern u1="W" u2="&#x119;" k="20" />
    <hkern u1="W" u2="&#x117;" k="20" />
    <hkern u1="W" u2="&#x115;" k="20" />
    <hkern u1="W" u2="&#x113;" k="20" />
    <hkern u1="W" u2="&#x105;" k="25" />
    <hkern u1="W" u2="&#x104;" k="35" />
    <hkern u1="W" u2="&#x103;" k="25" />
    <hkern u1="W" u2="&#x102;" k="35" />
    <hkern u1="W" u2="&#x101;" k="25" />
    <hkern u1="W" u2="&#x100;" k="35" />
    <hkern u1="W" u2="&#xfc;" k="15" />
    <hkern u1="W" u2="&#xfb;" k="15" />
    <hkern u1="W" u2="&#xfa;" k="15" />
    <hkern u1="W" u2="&#xf9;" k="15" />
    <hkern u1="W" u2="&#xf8;" k="20" />
    <hkern u1="W" u2="&#xf6;" k="20" />
    <hkern u1="W" u2="&#xf5;" k="20" />
    <hkern u1="W" u2="&#xf4;" k="20" />
    <hkern u1="W" u2="&#xf3;" k="20" />
    <hkern u1="W" u2="&#xf2;" k="20" />
    <hkern u1="W" u2="&#xf0;" k="20" />
    <hkern u1="W" u2="&#xeb;" k="20" />
    <hkern u1="W" u2="&#xea;" k="20" />
    <hkern u1="W" u2="&#xe9;" k="20" />
    <hkern u1="W" u2="&#xe8;" k="20" />
    <hkern u1="W" u2="&#xe6;" k="25" />
    <hkern u1="W" u2="&#xe5;" k="25" />
    <hkern u1="W" u2="&#xe4;" k="25" />
    <hkern u1="W" u2="&#xe3;" k="25" />
    <hkern u1="W" u2="&#xe2;" k="25" />
    <hkern u1="W" u2="&#xe1;" k="25" />
    <hkern u1="W" u2="&#xe0;" k="25" />
    <hkern u1="W" u2="&#xc6;" k="45" />
    <hkern u1="W" u2="&#xc5;" k="35" />
    <hkern u1="W" u2="&#xc4;" k="35" />
    <hkern u1="W" u2="&#xc3;" k="35" />
    <hkern u1="W" u2="&#xc2;" k="35" />
    <hkern u1="W" u2="&#xc1;" k="35" />
    <hkern u1="W" u2="&#xc0;" k="35" />
    <hkern u1="W" u2="&#xbf;" k="35" />
    <hkern u1="W" u2="z" k="10" />
    <hkern u1="W" u2="u" k="15" />
    <hkern u1="W" u2="s" k="25" />
    <hkern u1="W" u2="r" k="15" />
    <hkern u1="W" u2="q" k="20" />
    <hkern u1="W" u2="p" k="15" />
    <hkern u1="W" u2="o" k="20" />
    <hkern u1="W" u2="n" k="15" />
    <hkern u1="W" u2="m" k="15" />
    <hkern u1="W" u2="g" k="20" />
    <hkern u1="W" u2="e" k="20" />
    <hkern u1="W" u2="d" k="20" />
    <hkern u1="W" u2="c" k="20" />
    <hkern u1="W" u2="a" k="25" />
    <hkern u1="W" u2="J" k="40" />
    <hkern u1="W" u2="A" k="35" />
    <hkern u1="W" u2="&#x40;" k="5" />
    <hkern u1="W" u2="&#x3f;" k="-27" />
    <hkern u1="W" u2="&#x2e;" k="25" />
    <hkern u1="W" u2="&#x2c;" k="25" />
    <hkern u1="W" u2="&#x26;" k="19" />
    <hkern u1="X" u2="&#x2014;" k="3" />
    <hkern u1="X" u2="&#x2013;" k="3" />
    <hkern u1="X" u2="&#x153;" k="15" />
    <hkern u1="X" u2="&#x152;" k="30" />
    <hkern u1="X" u2="&#x150;" k="30" />
    <hkern u1="X" u2="&#x14e;" k="30" />
    <hkern u1="X" u2="&#x14c;" k="30" />
    <hkern u1="X" u2="&#x122;" k="30" />
    <hkern u1="X" u2="&#x120;" k="30" />
    <hkern u1="X" u2="&#x11e;" k="30" />
    <hkern u1="X" u2="&#x11c;" k="30" />
    <hkern u1="X" u2="&#x10c;" k="30" />
    <hkern u1="X" u2="&#x10a;" k="30" />
    <hkern u1="X" u2="&#x108;" k="30" />
    <hkern u1="X" u2="&#x106;" k="30" />
    <hkern u1="X" u2="&#xf0;" k="15" />
    <hkern u1="X" u2="&#xd8;" k="30" />
    <hkern u1="X" u2="&#xd6;" k="30" />
    <hkern u1="X" u2="&#xd5;" k="30" />
    <hkern u1="X" u2="&#xd4;" k="30" />
    <hkern u1="X" u2="&#xd3;" k="30" />
    <hkern u1="X" u2="&#xd2;" k="30" />
    <hkern u1="X" u2="&#xc7;" k="30" />
    <hkern u1="X" u2="&#xbf;" k="20" />
    <hkern u1="X" u2="y" k="30" />
    <hkern u1="X" u2="w" k="25" />
    <hkern u1="X" u2="v" k="25" />
    <hkern u1="X" u2="u" k="15" />
    <hkern u1="X" u2="t" k="25" />
    <hkern u1="X" u2="q" k="15" />
    <hkern u1="X" u2="o" k="15" />
    <hkern u1="X" u2="g" k="15" />
    <hkern u1="X" u2="e" k="15" />
    <hkern u1="X" u2="d" k="15" />
    <hkern u1="X" u2="c" k="15" />
    <hkern u1="X" u2="Q" k="30" />
    <hkern u1="X" u2="O" k="30" />
    <hkern u1="X" u2="J" k="-42" />
    <hkern u1="X" u2="G" k="30" />
    <hkern u1="X" u2="C" k="30" />
    <hkern u1="X" u2="&#x2d;" k="3" />
    <hkern u1="Y" u2="&#x2039;" k="52" />
    <hkern u1="Y" u2="&#x2026;" k="79" />
    <hkern u1="Y" u2="&#x2014;" k="32" />
    <hkern u1="Y" u2="&#x2013;" k="32" />
    <hkern u1="Y" u2="&#x218;" k="10" />
    <hkern u1="Y" u2="&#x175;" k="19" />
    <hkern u1="Y" u2="&#x173;" k="61" />
    <hkern u1="Y" u2="&#x171;" k="61" />
    <hkern u1="Y" u2="&#x16f;" k="61" />
    <hkern u1="Y" u2="&#x16d;" k="61" />
    <hkern u1="Y" u2="&#x16b;" k="61" />
    <hkern u1="Y" u2="&#x167;" k="28" />
    <hkern u1="Y" u2="&#x165;" k="28" />
    <hkern u1="Y" u2="&#x163;" k="28" />
    <hkern u1="Y" u2="&#x160;" k="10" />
    <hkern u1="Y" u2="&#x15e;" k="10" />
    <hkern u1="Y" u2="&#x15c;" k="10" />
    <hkern u1="Y" u2="&#x15a;" k="10" />
    <hkern u1="Y" u2="&#x153;" k="90" />
    <hkern u1="Y" u2="&#x152;" k="50" />
    <hkern u1="Y" u2="&#x150;" k="50" />
    <hkern u1="Y" u2="&#x14f;" k="60" />
    <hkern u1="Y" u2="&#x14e;" k="50" />
    <hkern u1="Y" u2="&#x14d;" k="60" />
    <hkern u1="Y" u2="&#x14c;" k="50" />
    <hkern u1="Y" u2="&#x14b;" k="75" />
    <hkern u1="Y" u2="&#x148;" k="70" />
    <hkern u1="Y" u2="&#x146;" k="70" />
    <hkern u1="Y" u2="&#x144;" k="70" />
    <hkern u1="Y" u2="&#x134;" k="105" />
    <hkern u1="Y" u2="&#x123;" k="90" />
    <hkern u1="Y" u2="&#x122;" k="50" />
    <hkern u1="Y" u2="&#x121;" k="90" />
    <hkern u1="Y" u2="&#x120;" k="50" />
    <hkern u1="Y" u2="&#x11f;" k="90" />
    <hkern u1="Y" u2="&#x11e;" k="50" />
    <hkern u1="Y" u2="&#x11c;" k="50" />
    <hkern u1="Y" u2="&#x11b;" k="90" />
    <hkern u1="Y" u2="&#x119;" k="90" />
    <hkern u1="Y" u2="&#x117;" k="90" />
    <hkern u1="Y" u2="&#x115;" k="60" />
    <hkern u1="Y" u2="&#x113;" k="60" />
    <hkern u1="Y" u2="&#x111;" k="90" />
    <hkern u1="Y" u2="&#x10f;" k="90" />
    <hkern u1="Y" u2="&#x10d;" k="90" />
    <hkern u1="Y" u2="&#x10c;" k="50" />
    <hkern u1="Y" u2="&#x10b;" k="90" />
    <hkern u1="Y" u2="&#x10a;" k="50" />
    <hkern u1="Y" u2="&#x109;" k="90" />
    <hkern u1="Y" u2="&#x108;" k="50" />
    <hkern u1="Y" u2="&#x107;" k="90" />
    <hkern u1="Y" u2="&#x106;" k="50" />
    <hkern u1="Y" u2="&#x105;" k="75" />
    <hkern u1="Y" u2="&#x104;" k="90" />
    <hkern u1="Y" u2="&#x103;" k="55" />
    <hkern u1="Y" u2="&#x102;" k="90" />
    <hkern u1="Y" u2="&#x101;" k="55" />
    <hkern u1="Y" u2="&#x100;" k="90" />
    <hkern u1="Y" u2="&#xfc;" k="61" />
    <hkern u1="Y" u2="&#xfb;" k="61" />
    <hkern u1="Y" u2="&#xfa;" k="61" />
    <hkern u1="Y" u2="&#xf9;" k="61" />
    <hkern u1="Y" u2="&#xf8;" k="90" />
    <hkern u1="Y" u2="&#xf6;" k="90" />
    <hkern u1="Y" u2="&#xf5;" k="90" />
    <hkern u1="Y" u2="&#xf4;" k="90" />
    <hkern u1="Y" u2="&#xf3;" k="90" />
    <hkern u1="Y" u2="&#xf2;" k="90" />
    <hkern u1="Y" u2="&#xf0;" k="90" />
    <hkern u1="Y" u2="&#xeb;" k="60" />
    <hkern u1="Y" u2="&#xea;" k="90" />
    <hkern u1="Y" u2="&#xe9;" k="90" />
    <hkern u1="Y" u2="&#xe8;" k="90" />
    <hkern u1="Y" u2="&#xe7;" k="90" />
    <hkern u1="Y" u2="&#xe6;" k="75" />
    <hkern u1="Y" u2="&#xe5;" k="75" />
    <hkern u1="Y" u2="&#xe4;" k="55" />
    <hkern u1="Y" u2="&#xe3;" k="55" />
    <hkern u1="Y" u2="&#xe2;" k="55" />
    <hkern u1="Y" u2="&#xe1;" k="75" />
    <hkern u1="Y" u2="&#xe0;" k="75" />
    <hkern u1="Y" u2="&#xd8;" k="50" />
    <hkern u1="Y" u2="&#xd6;" k="50" />
    <hkern u1="Y" u2="&#xd5;" k="50" />
    <hkern u1="Y" u2="&#xd4;" k="50" />
    <hkern u1="Y" u2="&#xd3;" k="50" />
    <hkern u1="Y" u2="&#xd2;" k="50" />
    <hkern u1="Y" u2="&#xc7;" k="50" />
    <hkern u1="Y" u2="&#xc6;" k="105" />
    <hkern u1="Y" u2="&#xc5;" k="90" />
    <hkern u1="Y" u2="&#xc4;" k="90" />
    <hkern u1="Y" u2="&#xc3;" k="90" />
    <hkern u1="Y" u2="&#xc2;" k="90" />
    <hkern u1="Y" u2="&#xc1;" k="90" />
    <hkern u1="Y" u2="&#xc0;" k="90" />
    <hkern u1="Y" u2="&#xbf;" k="85" />
    <hkern u1="Y" u2="&#xbb;" k="45" />
    <hkern u1="Y" u2="&#xab;" k="52" />
    <hkern u1="Y" u2="z" k="56" />
    <hkern u1="Y" u2="x" k="28" />
    <hkern u1="Y" u2="w" k="19" />
    <hkern u1="Y" u2="v" k="23" />
    <hkern u1="Y" u2="u" k="61" />
    <hkern u1="Y" u2="t" k="28" />
    <hkern u1="Y" u2="s" k="75" />
    <hkern u1="Y" u2="r" k="70" />
    <hkern u1="Y" u2="q" k="90" />
    <hkern u1="Y" u2="p" k="70" />
    <hkern u1="Y" u2="o" k="90" />
    <hkern u1="Y" u2="n" k="70" />
    <hkern u1="Y" u2="m" k="70" />
    <hkern u1="Y" u2="g" k="90" />
    <hkern u1="Y" u2="f" k="33" />
    <hkern u1="Y" u2="e" k="90" />
    <hkern u1="Y" u2="d" k="90" />
    <hkern u1="Y" u2="c" k="90" />
    <hkern u1="Y" u2="a" k="75" />
    <hkern u1="Y" u2="S" k="10" />
    <hkern u1="Y" u2="Q" k="50" />
    <hkern u1="Y" u2="O" k="50" />
    <hkern u1="Y" u2="J" k="105" />
    <hkern u1="Y" u2="G" k="50" />
    <hkern u1="Y" u2="C" k="50" />
    <hkern u1="Y" u2="A" k="90" />
    <hkern u1="Y" u2="&#x40;" k="45" />
    <hkern u1="Y" u2="&#x3b;" k="26" />
    <hkern u1="Y" u2="&#x3a;" k="26" />
    <hkern u1="Y" u2="&#x2f;" k="56" />
    <hkern u1="Y" u2="&#x2e;" k="79" />
    <hkern u1="Y" u2="&#x2d;" k="32" />
    <hkern u1="Y" u2="&#x2c;" k="79" />
    <hkern u1="Y" u2="&#x26;" k="66" />
    <hkern u1="Z" u2="&#x177;" k="25" />
    <hkern u1="Z" u2="&#x153;" k="30" />
    <hkern u1="Z" u2="&#x152;" k="50" />
    <hkern u1="Z" u2="&#xff;" k="25" />
    <hkern u1="Z" u2="&#xfd;" k="25" />
    <hkern u1="Z" u2="&#xf0;" k="20" />
    <hkern u1="Z" u2="y" k="25" />
    <hkern u1="Z" u2="w" k="15" />
    <hkern u1="Z" u2="v" k="20" />
    <hkern u1="Z" u2="u" k="15" />
    <hkern u1="Z" u2="t" k="15" />
    <hkern u1="Z" u2="q" k="30" />
    <hkern u1="Z" u2="o" k="30" />
    <hkern u1="Z" u2="g" k="30" />
    <hkern u1="Z" u2="f" k="28" />
    <hkern u1="Z" u2="e" k="30" />
    <hkern u1="Z" u2="d" k="30" />
    <hkern u1="Z" u2="c" k="30" />
    <hkern u1="Z" u2="Q" k="50" />
    <hkern u1="Z" u2="O" k="50" />
    <hkern u1="Z" u2="G" k="50" />
    <hkern u1="Z" u2="C" k="50" />
    <hkern u1="[" u2="J" k="-73" />
    <hkern u1="\" u2="&#x26;" k="51" />
    <hkern u1="a" u2="&#x201c;" k="65" />
    <hkern u1="a" u2="&#x2018;" k="65" />
    <hkern u1="a" u2="&#x177;" k="3" />
    <hkern u1="a" u2="&#xff;" k="3" />
    <hkern u1="a" u2="&#xfd;" k="3" />
    <hkern u1="a" u2="y" k="3" />
    <hkern u1="a" u2="\" k="60" />
    <hkern u1="b" u2="&#x2026;" k="27" />
    <hkern u1="b" u2="&#x201c;" k="70" />
    <hkern u1="b" u2="&#x2018;" k="70" />
    <hkern u1="b" u2="z" k="15" />
    <hkern u1="b" u2="y" k="5" />
    <hkern u1="b" u2="\" k="65" />
    <hkern u1="b" u2="&#x2e;" k="27" />
    <hkern u1="b" u2="&#x2c;" k="27" />
    <hkern u1="c" u2="&#x201c;" k="37" />
    <hkern u1="c" u2="&#x2018;" k="37" />
    <hkern u1="c" u2="&#x153;" k="10" />
    <hkern u1="c" u2="&#x10d;" k="10" />
    <hkern u1="c" u2="&#x10b;" k="10" />
    <hkern u1="c" u2="&#x109;" k="10" />
    <hkern u1="c" u2="&#x107;" k="10" />
    <hkern u1="c" u2="&#xf0;" k="10" />
    <hkern u1="c" u2="&#xe7;" k="10" />
    <hkern u1="c" u2="&#xe6;" k="-6" />
    <hkern u1="c" u2="q" k="10" />
    <hkern u1="c" u2="o" k="10" />
    <hkern u1="c" u2="g" k="10" />
    <hkern u1="c" u2="e" k="10" />
    <hkern u1="c" u2="d" k="10" />
    <hkern u1="c" u2="c" k="10" />
    <hkern u1="c" u2="\" k="56" />
    <hkern u1="e" u2="&#x201c;" k="55" />
    <hkern u1="e" u2="&#x2018;" k="55" />
    <hkern u1="e" u2="x" k="2" />
    <hkern u1="e" u2="v" k="5" />
    <hkern u1="e" u2="\" k="56" />
    <hkern u1="f" u2="&#x201d;" k="-35" />
    <hkern u1="f" u2="&#x201c;" k="-47" />
    <hkern u1="f" u2="&#x2019;" k="-35" />
    <hkern u1="f" u2="&#x2018;" k="-47" />
    <hkern u1="f" u2="&#x153;" k="10" />
    <hkern u1="f" u2="&#x14f;" k="10" />
    <hkern u1="f" u2="&#x14d;" k="10" />
    <hkern u1="f" u2="&#x11b;" k="10" />
    <hkern u1="f" u2="&#x117;" k="10" />
    <hkern u1="f" u2="&#x115;" k="10" />
    <hkern u1="f" u2="&#x113;" k="10" />
    <hkern u1="f" u2="&#x10d;" k="10" />
    <hkern u1="f" u2="&#x10b;" k="10" />
    <hkern u1="f" u2="&#x109;" k="10" />
    <hkern u1="f" u2="&#x107;" k="10" />
    <hkern u1="f" u2="&#x105;" k="15" />
    <hkern u1="f" u2="&#x103;" k="15" />
    <hkern u1="f" u2="&#x101;" k="15" />
    <hkern u1="f" u2="&#xf8;" k="10" />
    <hkern u1="f" u2="&#xf6;" k="10" />
    <hkern u1="f" u2="&#xf5;" k="10" />
    <hkern u1="f" u2="&#xf4;" k="10" />
    <hkern u1="f" u2="&#xf3;" k="10" />
    <hkern u1="f" u2="&#xf2;" k="10" />
    <hkern u1="f" u2="&#xf0;" k="10" />
    <hkern u1="f" u2="&#xeb;" k="10" />
    <hkern u1="f" u2="&#xea;" k="10" />
    <hkern u1="f" u2="&#xe9;" k="10" />
    <hkern u1="f" u2="&#xe8;" k="10" />
    <hkern u1="f" u2="&#xe7;" k="10" />
    <hkern u1="f" u2="&#xe6;" k="15" />
    <hkern u1="f" u2="&#xe5;" k="15" />
    <hkern u1="f" u2="&#xe4;" k="15" />
    <hkern u1="f" u2="&#xe3;" k="15" />
    <hkern u1="f" u2="&#xe2;" k="15" />
    <hkern u1="f" u2="&#xe1;" k="15" />
    <hkern u1="f" u2="&#xe0;" k="15" />
    <hkern u1="f" u2="&#xbf;" k="15" />
    <hkern u1="f" u2="&#x7d;" k="-27" />
    <hkern u1="f" u2="s" k="10" />
    <hkern u1="f" u2="q" k="10" />
    <hkern u1="f" u2="o" k="10" />
    <hkern u1="f" u2="g" k="10" />
    <hkern u1="f" u2="e" k="10" />
    <hkern u1="f" u2="d" k="10" />
    <hkern u1="f" u2="c" k="10" />
    <hkern u1="f" u2="a" k="15" />
    <hkern u1="f" u2="]" k="-27" />
    <hkern u1="f" u2="\" k="-52" />
    <hkern u1="f" u2="&#x3f;" k="-46" />
    <hkern u1="f" u2="&#x2a;" k="-32" />
    <hkern u1="f" u2="&#x29;" k="-32" />
    <hkern u1="f" u2="&#x21;" k="-18" />
    <hkern u1="g" u2="\" k="60" />
    <hkern u1="h" u2="&#x201c;" k="55" />
    <hkern u1="h" u2="&#x2018;" k="55" />
    <hkern u1="h" u2="y" k="10" />
    <hkern u1="h" u2="\" k="65" />
    <hkern u1="k" u2="&#x2c7;" k="25" />
    <hkern u1="k" u2="&#x153;" k="10" />
    <hkern u1="k" u2="&#x151;" k="10" />
    <hkern u1="k" u2="&#x14f;" k="10" />
    <hkern u1="k" u2="&#x14d;" k="10" />
    <hkern u1="k" u2="&#x123;" k="7" />
    <hkern u1="k" u2="&#x121;" k="7" />
    <hkern u1="k" u2="&#x11f;" k="7" />
    <hkern u1="k" u2="&#x11d;" k="7" />
    <hkern u1="k" u2="&#x11b;" k="10" />
    <hkern u1="k" u2="&#x119;" k="10" />
    <hkern u1="k" u2="&#x117;" k="10" />
    <hkern u1="k" u2="&#x115;" k="10" />
    <hkern u1="k" u2="&#x113;" k="10" />
    <hkern u1="k" u2="&#x10b;" k="10" />
    <hkern u1="k" u2="&#x109;" k="10" />
    <hkern u1="k" u2="&#x107;" k="10" />
    <hkern u1="k" u2="&#xf8;" k="10" />
    <hkern u1="k" u2="&#xf6;" k="10" />
    <hkern u1="k" u2="&#xf5;" k="10" />
    <hkern u1="k" u2="&#xf4;" k="10" />
    <hkern u1="k" u2="&#xf3;" k="10" />
    <hkern u1="k" u2="&#xf2;" k="10" />
    <hkern u1="k" u2="&#xf0;" k="7" />
    <hkern u1="k" u2="&#xeb;" k="10" />
    <hkern u1="k" u2="&#xea;" k="10" />
    <hkern u1="k" u2="&#xe9;" k="10" />
    <hkern u1="k" u2="&#xe8;" k="10" />
    <hkern u1="k" u2="&#xe7;" k="10" />
    <hkern u1="k" u2="&#xe6;" k="2" />
    <hkern u1="k" u2="&#xbf;" k="25" />
    <hkern u1="k" u2="q" k="7" />
    <hkern u1="k" u2="o" k="10" />
    <hkern u1="k" u2="g" k="7" />
    <hkern u1="k" u2="e" k="10" />
    <hkern u1="k" u2="d" k="7" />
    <hkern u1="k" u2="c" k="10" />
    <hkern u1="k" u2="a" k="2" />
    <hkern u1="k" u2="\" k="46" />
    <hkern u1="k" u2="&#x40;" k="4" />
    <hkern u1="l" u2="&#x153;" k="15" />
    <hkern u1="l" u2="y" k="20" />
    <hkern u1="l" u2="v" k="5" />
    <hkern u1="l" u2="u" k="10" />
    <hkern u1="l" u2="t" k="25" />
    <hkern u1="l" u2="q" k="15" />
    <hkern u1="l" u2="o" k="15" />
    <hkern u1="l" u2="g" k="15" />
    <hkern u1="l" u2="e" k="15" />
    <hkern u1="l" u2="d" k="15" />
    <hkern u1="l" u2="c" k="15" />
    <hkern u1="m" u2="&#x201c;" k="55" />
    <hkern u1="m" u2="&#x2018;" k="55" />
    <hkern u1="m" u2="\" k="65" />
    <hkern u1="n" u2="&#x201c;" k="55" />
    <hkern u1="n" u2="&#x2018;" k="55" />
    <hkern u1="n" u2="\" k="65" />
    <hkern u1="o" u2="&#x2026;" k="27" />
    <hkern u1="o" u2="&#x201c;" k="61" />
    <hkern u1="o" u2="&#x2018;" k="61" />
    <hkern u1="o" u2="&#x177;" k="10" />
    <hkern u1="o" u2="&#x175;" k="-3" />
    <hkern u1="o" u2="&#xff;" k="10" />
    <hkern u1="o" u2="&#xfd;" k="10" />
    <hkern u1="o" u2="z" k="20" />
    <hkern u1="o" u2="y" k="10" />
    <hkern u1="o" u2="x" k="2" />
    <hkern u1="o" u2="w" k="-3" />
    <hkern u1="o" u2="v" k="5" />
    <hkern u1="o" u2="\" k="89" />
    <hkern u1="o" u2="&#x2e;" k="27" />
    <hkern u1="o" u2="&#x2c;" k="27" />
    <hkern u1="p" u2="&#x2026;" k="27" />
    <hkern u1="p" u2="&#x201c;" k="70" />
    <hkern u1="p" u2="&#x2018;" k="70" />
    <hkern u1="p" u2="z" k="15" />
    <hkern u1="p" u2="y" k="5" />
    <hkern u1="p" u2="\" k="65" />
    <hkern u1="p" u2="&#x2e;" k="27" />
    <hkern u1="p" u2="&#x2c;" k="27" />
    <hkern u1="q" u2="&#x201c;" k="28" />
    <hkern u1="q" u2="&#x2018;" k="28" />
    <hkern u1="q" u2="\" k="61" />
    <hkern u1="r" u2="&#x2026;" k="27" />
    <hkern u1="r" u2="&#x2014;" k="7" />
    <hkern u1="r" u2="&#x2013;" k="7" />
    <hkern u1="r" u2="&#x219;" k="5" />
    <hkern u1="r" u2="&#x161;" k="5" />
    <hkern u1="r" u2="&#x15f;" k="5" />
    <hkern u1="r" u2="&#x15d;" k="5" />
    <hkern u1="r" u2="&#x15b;" k="5" />
    <hkern u1="r" u2="&#x153;" k="20" />
    <hkern u1="r" u2="&#x151;" k="20" />
    <hkern u1="r" u2="&#x14f;" k="20" />
    <hkern u1="r" u2="&#x14d;" k="20" />
    <hkern u1="r" u2="&#x123;" k="20" />
    <hkern u1="r" u2="&#x121;" k="20" />
    <hkern u1="r" u2="&#x11f;" k="20" />
    <hkern u1="r" u2="&#x11d;" k="20" />
    <hkern u1="r" u2="&#x11b;" k="20" />
    <hkern u1="r" u2="&#x119;" k="20" />
    <hkern u1="r" u2="&#x117;" k="20" />
    <hkern u1="r" u2="&#x115;" k="20" />
    <hkern u1="r" u2="&#x113;" k="20" />
    <hkern u1="r" u2="&#x111;" k="20" />
    <hkern u1="r" u2="&#x10f;" k="20" />
    <hkern u1="r" u2="&#x10d;" k="20" />
    <hkern u1="r" u2="&#x10b;" k="20" />
    <hkern u1="r" u2="&#x109;" k="20" />
    <hkern u1="r" u2="&#x107;" k="20" />
    <hkern u1="r" u2="&#x105;" k="15" />
    <hkern u1="r" u2="&#x103;" k="15" />
    <hkern u1="r" u2="&#x101;" k="15" />
    <hkern u1="r" u2="&#xf8;" k="20" />
    <hkern u1="r" u2="&#xf6;" k="20" />
    <hkern u1="r" u2="&#xf5;" k="20" />
    <hkern u1="r" u2="&#xf4;" k="20" />
    <hkern u1="r" u2="&#xf3;" k="20" />
    <hkern u1="r" u2="&#xf2;" k="20" />
    <hkern u1="r" u2="&#xf0;" k="5" />
    <hkern u1="r" u2="&#xeb;" k="20" />
    <hkern u1="r" u2="&#xea;" k="20" />
    <hkern u1="r" u2="&#xe9;" k="20" />
    <hkern u1="r" u2="&#xe8;" k="20" />
    <hkern u1="r" u2="&#xe7;" k="20" />
    <hkern u1="r" u2="&#xe6;" k="15" />
    <hkern u1="r" u2="&#xe5;" k="15" />
    <hkern u1="r" u2="&#xe4;" k="15" />
    <hkern u1="r" u2="&#xe3;" k="15" />
    <hkern u1="r" u2="&#xe2;" k="15" />
    <hkern u1="r" u2="&#xe1;" k="15" />
    <hkern u1="r" u2="&#xe0;" k="15" />
    <hkern u1="r" u2="&#xbf;" k="20" />
    <hkern u1="r" u2="y" k="-30" />
    <hkern u1="r" u2="x" k="-30" />
    <hkern u1="r" u2="w" k="-30" />
    <hkern u1="r" u2="v" k="-30" />
    <hkern u1="r" u2="t" k="-25" />
    <hkern u1="r" u2="s" k="5" />
    <hkern u1="r" u2="q" k="20" />
    <hkern u1="r" u2="o" k="20" />
    <hkern u1="r" u2="g" k="20" />
    <hkern u1="r" u2="e" k="20" />
    <hkern u1="r" u2="d" k="20" />
    <hkern u1="r" u2="c" k="20" />
    <hkern u1="r" u2="a" k="15" />
    <hkern u1="r" u2="&#x2e;" k="27" />
    <hkern u1="r" u2="&#x2d;" k="7" />
    <hkern u1="r" u2="&#x2c;" k="27" />
    <hkern u1="s" u2="&#x201c;" k="28" />
    <hkern u1="s" u2="&#x2018;" k="28" />
    <hkern u1="s" u2="\" k="51" />
    <hkern u1="t" u2="&#x153;" k="10" />
    <hkern u1="t" u2="&#x151;" k="10" />
    <hkern u1="t" u2="&#x14f;" k="10" />
    <hkern u1="t" u2="&#x14d;" k="10" />
    <hkern u1="t" u2="&#x11b;" k="10" />
    <hkern u1="t" u2="&#x119;" k="10" />
    <hkern u1="t" u2="&#x117;" k="10" />
    <hkern u1="t" u2="&#x115;" k="10" />
    <hkern u1="t" u2="&#x113;" k="10" />
    <hkern u1="t" u2="&#x10d;" k="10" />
    <hkern u1="t" u2="&#x10b;" k="10" />
    <hkern u1="t" u2="&#x109;" k="10" />
    <hkern u1="t" u2="&#x107;" k="10" />
    <hkern u1="t" u2="&#xf8;" k="10" />
    <hkern u1="t" u2="&#xf6;" k="10" />
    <hkern u1="t" u2="&#xf5;" k="10" />
    <hkern u1="t" u2="&#xf4;" k="10" />
    <hkern u1="t" u2="&#xf3;" k="10" />
    <hkern u1="t" u2="&#xf2;" k="10" />
    <hkern u1="t" u2="&#xeb;" k="10" />
    <hkern u1="t" u2="&#xea;" k="10" />
    <hkern u1="t" u2="&#xe9;" k="10" />
    <hkern u1="t" u2="&#xe8;" k="10" />
    <hkern u1="t" u2="&#xe7;" k="10" />
    <hkern u1="t" u2="q" k="10" />
    <hkern u1="t" u2="o" k="10" />
    <hkern u1="t" u2="e" k="10" />
    <hkern u1="t" u2="d" k="10" />
    <hkern u1="t" u2="c" k="10" />
    <hkern u1="u" u2="&#x201c;" k="9" />
    <hkern u1="u" u2="&#x2018;" k="9" />
    <hkern u1="u" u2="\" k="51" />
    <hkern u1="v" u2="&#x2026;" k="42" />
    <hkern u1="v" u2="&#x2c7;" k="10" />
    <hkern u1="v" u2="&#x219;" k="-3" />
    <hkern u1="v" u2="&#x161;" k="-3" />
    <hkern u1="v" u2="&#x15f;" k="-3" />
    <hkern u1="v" u2="&#x15b;" k="-3" />
    <hkern u1="v" u2="&#x105;" k="10" />
    <hkern u1="v" u2="&#x103;" k="10" />
    <hkern u1="v" u2="&#x101;" k="10" />
    <hkern u1="v" u2="&#xe6;" k="10" />
    <hkern u1="v" u2="&#xe5;" k="10" />
    <hkern u1="v" u2="&#xe4;" k="10" />
    <hkern u1="v" u2="&#xe3;" k="10" />
    <hkern u1="v" u2="&#xe2;" k="10" />
    <hkern u1="v" u2="&#xe1;" k="10" />
    <hkern u1="v" u2="&#xe0;" k="10" />
    <hkern u1="v" u2="&#xbf;" k="10" />
    <hkern u1="v" u2="s" k="-3" />
    <hkern u1="v" u2="o" k="5" />
    <hkern u1="v" u2="e" k="5" />
    <hkern u1="v" u2="a" k="10" />
    <hkern u1="v" u2="&#x2e;" k="42" />
    <hkern u1="v" u2="&#x2c;" k="42" />
    <hkern u1="v" u2="&#x26;" k="15" />
    <hkern u1="w" u2="&#x2026;" k="23" />
    <hkern u1="w" u2="&#x2c7;" k="5" />
    <hkern u1="w" u2="&#x153;" k="-3" />
    <hkern u1="w" u2="&#x151;" k="-3" />
    <hkern u1="w" u2="&#x14f;" k="-3" />
    <hkern u1="w" u2="&#x14d;" k="-3" />
    <hkern u1="w" u2="&#x11b;" k="-3" />
    <hkern u1="w" u2="&#x119;" k="-3" />
    <hkern u1="w" u2="&#x117;" k="-3" />
    <hkern u1="w" u2="&#x115;" k="-3" />
    <hkern u1="w" u2="&#x113;" k="-3" />
    <hkern u1="w" u2="&#xf8;" k="-3" />
    <hkern u1="w" u2="&#xf6;" k="-3" />
    <hkern u1="w" u2="&#xf5;" k="-3" />
    <hkern u1="w" u2="&#xf4;" k="-3" />
    <hkern u1="w" u2="&#xf3;" k="-3" />
    <hkern u1="w" u2="&#xf2;" k="-3" />
    <hkern u1="w" u2="&#xeb;" k="-3" />
    <hkern u1="w" u2="&#xea;" k="-3" />
    <hkern u1="w" u2="&#xe9;" k="-3" />
    <hkern u1="w" u2="&#xe8;" k="-3" />
    <hkern u1="w" u2="&#xbf;" k="5" />
    <hkern u1="w" u2="s" k="5" />
    <hkern u1="w" u2="o" k="-3" />
    <hkern u1="w" u2="e" k="-3" />
    <hkern u1="w" u2="&#x2e;" k="23" />
    <hkern u1="w" u2="&#x2c;" k="23" />
    <hkern u1="w" u2="&#x26;" k="15" />
    <hkern u1="x" u2="&#x2c7;" k="15" />
    <hkern u1="x" u2="&#x153;" k="10" />
    <hkern u1="x" u2="&#x151;" k="10" />
    <hkern u1="x" u2="&#x14f;" k="10" />
    <hkern u1="x" u2="&#x14d;" k="10" />
    <hkern u1="x" u2="&#x11b;" k="10" />
    <hkern u1="x" u2="&#x119;" k="10" />
    <hkern u1="x" u2="&#x117;" k="10" />
    <hkern u1="x" u2="&#x115;" k="10" />
    <hkern u1="x" u2="&#x113;" k="10" />
    <hkern u1="x" u2="&#x10b;" k="10" />
    <hkern u1="x" u2="&#x109;" k="10" />
    <hkern u1="x" u2="&#x107;" k="10" />
    <hkern u1="x" u2="&#x105;" k="-2" />
    <hkern u1="x" u2="&#x103;" k="-2" />
    <hkern u1="x" u2="&#x101;" k="-2" />
    <hkern u1="x" u2="&#xf8;" k="10" />
    <hkern u1="x" u2="&#xf6;" k="10" />
    <hkern u1="x" u2="&#xf5;" k="10" />
    <hkern u1="x" u2="&#xf4;" k="10" />
    <hkern u1="x" u2="&#xf3;" k="10" />
    <hkern u1="x" u2="&#xf2;" k="10" />
    <hkern u1="x" u2="&#xf0;" k="10" />
    <hkern u1="x" u2="&#xeb;" k="10" />
    <hkern u1="x" u2="&#xea;" k="10" />
    <hkern u1="x" u2="&#xe9;" k="10" />
    <hkern u1="x" u2="&#xe8;" k="10" />
    <hkern u1="x" u2="&#xe7;" k="10" />
    <hkern u1="x" u2="&#xe6;" k="-2" />
    <hkern u1="x" u2="&#xe5;" k="-2" />
    <hkern u1="x" u2="&#xe4;" k="-2" />
    <hkern u1="x" u2="&#xe3;" k="-2" />
    <hkern u1="x" u2="&#xe2;" k="-2" />
    <hkern u1="x" u2="&#xe1;" k="-2" />
    <hkern u1="x" u2="&#xe0;" k="-2" />
    <hkern u1="x" u2="&#xbf;" k="15" />
    <hkern u1="x" u2="q" k="10" />
    <hkern u1="x" u2="o" k="10" />
    <hkern u1="x" u2="g" k="10" />
    <hkern u1="x" u2="e" k="10" />
    <hkern u1="x" u2="d" k="10" />
    <hkern u1="x" u2="c" k="10" />
    <hkern u1="x" u2="a" k="-2" />
    <hkern u1="y" u2="&#x2026;" k="45" />
    <hkern u1="y" u2="&#x2014;" k="10" />
    <hkern u1="y" u2="&#x2013;" k="10" />
    <hkern u1="y" u2="&#x2c7;" k="10" />
    <hkern u1="y" u2="&#x153;" k="5" />
    <hkern u1="y" u2="&#x105;" k="5" />
    <hkern u1="y" u2="&#x103;" k="5" />
    <hkern u1="y" u2="&#x101;" k="5" />
    <hkern u1="y" u2="&#xf0;" k="5" />
    <hkern u1="y" u2="&#xe6;" k="5" />
    <hkern u1="y" u2="&#xe5;" k="5" />
    <hkern u1="y" u2="&#xe4;" k="5" />
    <hkern u1="y" u2="&#xe3;" k="5" />
    <hkern u1="y" u2="&#xe2;" k="5" />
    <hkern u1="y" u2="&#xe1;" k="5" />
    <hkern u1="y" u2="&#xe0;" k="5" />
    <hkern u1="y" u2="&#xbf;" k="10" />
    <hkern u1="y" u2="s" k="1" />
    <hkern u1="y" u2="q" k="5" />
    <hkern u1="y" u2="o" k="5" />
    <hkern u1="y" u2="g" k="5" />
    <hkern u1="y" u2="e" k="5" />
    <hkern u1="y" u2="d" k="5" />
    <hkern u1="y" u2="c" k="5" />
    <hkern u1="y" u2="a" k="5" />
    <hkern u1="y" u2="&#x2e;" k="45" />
    <hkern u1="y" u2="&#x2d;" k="10" />
    <hkern u1="y" u2="&#x2c;" k="45" />
    <hkern u1="y" u2="&#x26;" k="20" />
    <hkern u1="z" u2="&#x201c;" k="28" />
    <hkern u1="z" u2="&#x2018;" k="28" />
    <hkern u1="z" u2="&#x153;" k="25" />
    <hkern u1="z" u2="&#xf0;" k="25" />
    <hkern u1="z" u2="&#xe6;" k="10" />
    <hkern u1="z" u2="q" k="25" />
    <hkern u1="z" u2="o" k="25" />
    <hkern u1="z" u2="g" k="25" />
    <hkern u1="z" u2="e" k="25" />
    <hkern u1="z" u2="d" k="25" />
    <hkern u1="z" u2="c" k="25" />
    <hkern u1="z" u2="\" k="51" />
    <hkern u1="z" u2="&#x26;" k="19" />
    <hkern u1="&#xab;" u2="&#x422;" k="50" />
    <hkern u1="&#xab;" u2="&#x3ab;" k="40" />
    <hkern u1="&#xab;" u2="&#x3a5;" k="40" />
    <hkern u1="&#xab;" u2="&#x3a4;" k="30" />
    <hkern u1="&#xab;" u2="&#x178;" k="45" />
    <hkern u1="&#xab;" u2="&#x176;" k="45" />
    <hkern u1="&#xab;" u2="&#x166;" k="45" />
    <hkern u1="&#xab;" u2="&#x164;" k="45" />
    <hkern u1="&#xab;" u2="&#x162;" k="45" />
    <hkern u1="&#xab;" u2="&#xdd;" k="45" />
    <hkern u1="&#xab;" u2="Y" k="45" />
    <hkern u1="&#xab;" u2="T" k="45" />
    <hkern u1="&#xb5;" u2="&#x3c4;" k="20" />
    <hkern u1="&#xb5;" u2="&#x3c0;" k="15" />
    <hkern u1="&#xbb;" u2="Y" k="51" />
    <hkern u1="&#xbb;" u2="V" k="27" />
    <hkern u1="&#xbb;" u2="T" k="56" />
    <hkern u1="&#xbf;" u2="y" k="10" />
    <hkern u1="&#xbf;" u2="x" k="10" />
    <hkern u1="&#xbf;" u2="w" k="5" />
    <hkern u1="&#xbf;" u2="v" k="5" />
    <hkern u1="&#xc0;" u2="&#x201d;" k="27" />
    <hkern u1="&#xc0;" u2="&#x2019;" k="27" />
    <hkern u1="&#xc0;" u2="y" k="25" />
    <hkern u1="&#xc0;" u2="w" k="20" />
    <hkern u1="&#xc0;" u2="v" k="25" />
    <hkern u1="&#xc0;" u2="t" k="20" />
    <hkern u1="&#xc0;" u2="\" k="75" />
    <hkern u1="&#xc0;" u2="Y" k="90" />
    <hkern u1="&#xc0;" u2="W" k="35" />
    <hkern u1="&#xc0;" u2="V" k="70" />
    <hkern u1="&#xc0;" u2="U" k="20" />
    <hkern u1="&#xc0;" u2="T" k="75" />
    <hkern u1="&#xc0;" u2="Q" k="20" />
    <hkern u1="&#xc0;" u2="O" k="20" />
    <hkern u1="&#xc0;" u2="J" k="-47" />
    <hkern u1="&#xc0;" u2="G" k="20" />
    <hkern u1="&#xc0;" u2="C" k="20" />
    <hkern u1="&#xc0;" u2="&#x2a;" k="34" />
    <hkern u1="&#xc1;" u2="&#x201d;" k="27" />
    <hkern u1="&#xc1;" u2="&#x2019;" k="27" />
    <hkern u1="&#xc1;" u2="y" k="25" />
    <hkern u1="&#xc1;" u2="w" k="20" />
    <hkern u1="&#xc1;" u2="v" k="25" />
    <hkern u1="&#xc1;" u2="t" k="20" />
    <hkern u1="&#xc1;" u2="\" k="75" />
    <hkern u1="&#xc1;" u2="Y" k="90" />
    <hkern u1="&#xc1;" u2="W" k="35" />
    <hkern u1="&#xc1;" u2="V" k="70" />
    <hkern u1="&#xc1;" u2="U" k="20" />
    <hkern u1="&#xc1;" u2="T" k="75" />
    <hkern u1="&#xc1;" u2="Q" k="20" />
    <hkern u1="&#xc1;" u2="O" k="20" />
    <hkern u1="&#xc1;" u2="J" k="-47" />
    <hkern u1="&#xc1;" u2="G" k="20" />
    <hkern u1="&#xc1;" u2="C" k="20" />
    <hkern u1="&#xc1;" u2="&#x2a;" k="34" />
    <hkern u1="&#xc2;" u2="&#x201d;" k="27" />
    <hkern u1="&#xc2;" u2="&#x2019;" k="27" />
    <hkern u1="&#xc2;" u2="y" k="25" />
    <hkern u1="&#xc2;" u2="w" k="20" />
    <hkern u1="&#xc2;" u2="v" k="25" />
    <hkern u1="&#xc2;" u2="t" k="20" />
    <hkern u1="&#xc2;" u2="\" k="75" />
    <hkern u1="&#xc2;" u2="Y" k="90" />
    <hkern u1="&#xc2;" u2="W" k="35" />
    <hkern u1="&#xc2;" u2="V" k="70" />
    <hkern u1="&#xc2;" u2="U" k="20" />
    <hkern u1="&#xc2;" u2="T" k="75" />
    <hkern u1="&#xc2;" u2="Q" k="20" />
    <hkern u1="&#xc2;" u2="O" k="20" />
    <hkern u1="&#xc2;" u2="J" k="-47" />
    <hkern u1="&#xc2;" u2="G" k="20" />
    <hkern u1="&#xc2;" u2="C" k="20" />
    <hkern u1="&#xc2;" u2="&#x2a;" k="34" />
    <hkern u1="&#xc3;" u2="&#x201d;" k="27" />
    <hkern u1="&#xc3;" u2="&#x2019;" k="27" />
    <hkern u1="&#xc3;" u2="y" k="25" />
    <hkern u1="&#xc3;" u2="w" k="20" />
    <hkern u1="&#xc3;" u2="v" k="25" />
    <hkern u1="&#xc3;" u2="t" k="20" />
    <hkern u1="&#xc3;" u2="\" k="75" />
    <hkern u1="&#xc3;" u2="Y" k="90" />
    <hkern u1="&#xc3;" u2="W" k="35" />
    <hkern u1="&#xc3;" u2="V" k="70" />
    <hkern u1="&#xc3;" u2="U" k="20" />
    <hkern u1="&#xc3;" u2="T" k="75" />
    <hkern u1="&#xc3;" u2="Q" k="20" />
    <hkern u1="&#xc3;" u2="O" k="20" />
    <hkern u1="&#xc3;" u2="J" k="-47" />
    <hkern u1="&#xc3;" u2="G" k="20" />
    <hkern u1="&#xc3;" u2="C" k="20" />
    <hkern u1="&#xc3;" u2="&#x2a;" k="34" />
    <hkern u1="&#xc4;" u2="&#x201d;" k="27" />
    <hkern u1="&#xc4;" u2="&#x2019;" k="27" />
    <hkern u1="&#xc4;" u2="y" k="25" />
    <hkern u1="&#xc4;" u2="w" k="20" />
    <hkern u1="&#xc4;" u2="v" k="25" />
    <hkern u1="&#xc4;" u2="t" k="20" />
    <hkern u1="&#xc4;" u2="\" k="75" />
    <hkern u1="&#xc4;" u2="Y" k="90" />
    <hkern u1="&#xc4;" u2="W" k="35" />
    <hkern u1="&#xc4;" u2="V" k="70" />
    <hkern u1="&#xc4;" u2="U" k="20" />
    <hkern u1="&#xc4;" u2="T" k="75" />
    <hkern u1="&#xc4;" u2="Q" k="20" />
    <hkern u1="&#xc4;" u2="O" k="20" />
    <hkern u1="&#xc4;" u2="J" k="-47" />
    <hkern u1="&#xc4;" u2="G" k="20" />
    <hkern u1="&#xc4;" u2="C" k="20" />
    <hkern u1="&#xc4;" u2="&#x2a;" k="34" />
    <hkern u1="&#xc5;" u2="&#x201d;" k="27" />
    <hkern u1="&#xc5;" u2="&#x2019;" k="27" />
    <hkern u1="&#xc5;" u2="y" k="25" />
    <hkern u1="&#xc5;" u2="w" k="20" />
    <hkern u1="&#xc5;" u2="v" k="25" />
    <hkern u1="&#xc5;" u2="t" k="20" />
    <hkern u1="&#xc5;" u2="\" k="75" />
    <hkern u1="&#xc5;" u2="Y" k="90" />
    <hkern u1="&#xc5;" u2="W" k="35" />
    <hkern u1="&#xc5;" u2="V" k="70" />
    <hkern u1="&#xc5;" u2="U" k="20" />
    <hkern u1="&#xc5;" u2="T" k="75" />
    <hkern u1="&#xc5;" u2="Q" k="20" />
    <hkern u1="&#xc5;" u2="O" k="20" />
    <hkern u1="&#xc5;" u2="J" k="-47" />
    <hkern u1="&#xc5;" u2="G" k="20" />
    <hkern u1="&#xc5;" u2="C" k="20" />
    <hkern u1="&#xc5;" u2="&#x2a;" k="34" />
    <hkern u1="&#xc6;" u2="&#x153;" k="25" />
    <hkern u1="&#xc6;" u2="&#x152;" k="30" />
    <hkern u1="&#xc6;" u2="&#xf0;" k="15" />
    <hkern u1="&#xc6;" u2="y" k="15" />
    <hkern u1="&#xc6;" u2="w" k="15" />
    <hkern u1="&#xc6;" u2="v" k="20" />
    <hkern u1="&#xc6;" u2="u" k="10" />
    <hkern u1="&#xc6;" u2="t" k="15" />
    <hkern u1="&#xc6;" u2="q" k="25" />
    <hkern u1="&#xc6;" u2="o" k="25" />
    <hkern u1="&#xc6;" u2="g" k="25" />
    <hkern u1="&#xc6;" u2="e" k="25" />
    <hkern u1="&#xc6;" u2="d" k="25" />
    <hkern u1="&#xc6;" u2="c" k="25" />
    <hkern u1="&#xc6;" u2="Q" k="30" />
    <hkern u1="&#xc6;" u2="O" k="30" />
    <hkern u1="&#xc6;" u2="G" k="30" />
    <hkern u1="&#xc6;" u2="C" k="30" />
    <hkern u1="&#xc7;" u2="Q" k="10" />
    <hkern u1="&#xc7;" u2="O" k="10" />
    <hkern u1="&#xc7;" u2="G" k="10" />
    <hkern u1="&#xc7;" u2="C" k="10" />
    <hkern u1="&#xd2;" u2="&#xc6;" k="33" />
    <hkern u1="&#xd2;" u2="Y" k="40" />
    <hkern u1="&#xd2;" u2="X" k="25" />
    <hkern u1="&#xd2;" u2="V" k="15" />
    <hkern u1="&#xd2;" u2="T" k="25" />
    <hkern u1="&#xd2;" u2="J" k="10" />
    <hkern u1="&#xd2;" u2="A" k="20" />
    <hkern u1="&#xd3;" u2="&#xc6;" k="33" />
    <hkern u1="&#xd3;" u2="Y" k="40" />
    <hkern u1="&#xd3;" u2="X" k="25" />
    <hkern u1="&#xd3;" u2="V" k="15" />
    <hkern u1="&#xd3;" u2="T" k="25" />
    <hkern u1="&#xd3;" u2="J" k="10" />
    <hkern u1="&#xd3;" u2="A" k="20" />
    <hkern u1="&#xd4;" u2="&#xc6;" k="33" />
    <hkern u1="&#xd4;" u2="Y" k="40" />
    <hkern u1="&#xd4;" u2="X" k="25" />
    <hkern u1="&#xd4;" u2="V" k="15" />
    <hkern u1="&#xd4;" u2="T" k="25" />
    <hkern u1="&#xd4;" u2="J" k="10" />
    <hkern u1="&#xd4;" u2="A" k="20" />
    <hkern u1="&#xd5;" u2="&#xc6;" k="33" />
    <hkern u1="&#xd5;" u2="Y" k="40" />
    <hkern u1="&#xd5;" u2="X" k="25" />
    <hkern u1="&#xd5;" u2="V" k="15" />
    <hkern u1="&#xd5;" u2="T" k="25" />
    <hkern u1="&#xd5;" u2="J" k="10" />
    <hkern u1="&#xd5;" u2="A" k="20" />
    <hkern u1="&#xd6;" u2="&#xc6;" k="33" />
    <hkern u1="&#xd6;" u2="Y" k="40" />
    <hkern u1="&#xd6;" u2="X" k="25" />
    <hkern u1="&#xd6;" u2="V" k="15" />
    <hkern u1="&#xd6;" u2="T" k="25" />
    <hkern u1="&#xd6;" u2="J" k="10" />
    <hkern u1="&#xd6;" u2="A" k="20" />
    <hkern u1="&#xd8;" u2="&#xc6;" k="33" />
    <hkern u1="&#xd8;" u2="Y" k="40" />
    <hkern u1="&#xd8;" u2="X" k="25" />
    <hkern u1="&#xd8;" u2="V" k="15" />
    <hkern u1="&#xd8;" u2="T" k="25" />
    <hkern u1="&#xd8;" u2="J" k="10" />
    <hkern u1="&#xd8;" u2="A" k="20" />
    <hkern u1="&#xd9;" u2="&#xc6;" k="33" />
    <hkern u1="&#xd9;" u2="A" k="20" />
    <hkern u1="&#xda;" u2="&#xc6;" k="33" />
    <hkern u1="&#xda;" u2="A" k="20" />
    <hkern u1="&#xdb;" u2="&#xc6;" k="33" />
    <hkern u1="&#xdb;" u2="A" k="20" />
    <hkern u1="&#xdc;" u2="&#xc6;" k="33" />
    <hkern u1="&#xdc;" u2="A" k="20" />
    <hkern u1="&#xdd;" u2="&#x153;" k="90" />
    <hkern u1="&#xdd;" u2="&#x152;" k="50" />
    <hkern u1="&#xdd;" u2="&#xe6;" k="75" />
    <hkern u1="&#xdd;" u2="&#xc6;" k="105" />
    <hkern u1="&#xdd;" u2="&#xbb;" k="45" />
    <hkern u1="&#xdd;" u2="w" k="19" />
    <hkern u1="&#xdd;" u2="v" k="23" />
    <hkern u1="&#xdd;" u2="u" k="61" />
    <hkern u1="&#xdd;" u2="t" k="28" />
    <hkern u1="&#xdd;" u2="p" k="70" />
    <hkern u1="&#xdd;" u2="o" k="90" />
    <hkern u1="&#xdd;" u2="n" k="70" />
    <hkern u1="&#xdd;" u2="m" k="70" />
    <hkern u1="&#xdd;" u2="g" k="90" />
    <hkern u1="&#xdd;" u2="f" k="33" />
    <hkern u1="&#xdd;" u2="e" k="90" />
    <hkern u1="&#xdd;" u2="d" k="90" />
    <hkern u1="&#xdd;" u2="c" k="90" />
    <hkern u1="&#xdd;" u2="a" k="75" />
    <hkern u1="&#xdd;" u2="S" k="10" />
    <hkern u1="&#xdd;" u2="Q" k="50" />
    <hkern u1="&#xdd;" u2="O" k="50" />
    <hkern u1="&#xdd;" u2="J" k="105" />
    <hkern u1="&#xdd;" u2="G" k="50" />
    <hkern u1="&#xdd;" u2="C" k="50" />
    <hkern u1="&#xdd;" u2="A" k="90" />
    <hkern u1="&#xdd;" u2="&#x3b;" k="26" />
    <hkern u1="&#xdd;" u2="&#x3a;" k="26" />
    <hkern u1="&#xdd;" u2="&#x2f;" k="56" />
    <hkern u1="&#xdd;" u2="&#x2e;" k="79" />
    <hkern u1="&#xdd;" u2="&#x2d;" k="32" />
    <hkern u1="&#xdd;" u2="&#x2c;" k="79" />
    <hkern u1="&#xdd;" u2="&#x26;" k="66" />
    <hkern u1="&#xe0;" u2="y" k="3" />
    <hkern u1="&#xe1;" u2="y" k="3" />
    <hkern u1="&#xe2;" u2="y" k="3" />
    <hkern u1="&#xe3;" u2="y" k="3" />
    <hkern u1="&#xe4;" u2="y" k="3" />
    <hkern u1="&#xe5;" u2="y" k="3" />
    <hkern u1="&#xe6;" u2="&#x201c;" k="55" />
    <hkern u1="&#xe6;" u2="&#x2018;" k="55" />
    <hkern u1="&#xe6;" u2="x" k="2" />
    <hkern u1="&#xe6;" u2="v" k="5" />
    <hkern u1="&#xe6;" u2="\" k="56" />
    <hkern u1="&#xe8;" u2="x" k="2" />
    <hkern u1="&#xe9;" u2="x" k="2" />
    <hkern u1="&#xea;" u2="x" k="2" />
    <hkern u1="&#xeb;" u2="x" k="2" />
    <hkern u1="&#xf2;" u2="y" k="10" />
    <hkern u1="&#xf2;" u2="w" k="-3" />
    <hkern u1="&#xf3;" u2="y" k="10" />
    <hkern u1="&#xf3;" u2="w" k="-3" />
    <hkern u1="&#xf4;" u2="y" k="10" />
    <hkern u1="&#xf4;" u2="w" k="-3" />
    <hkern u1="&#xf5;" u2="y" k="10" />
    <hkern u1="&#xf5;" u2="w" k="-3" />
    <hkern u1="&#xf6;" u2="y" k="10" />
    <hkern u1="&#xf6;" u2="w" k="-3" />
    <hkern u1="&#xf8;" u2="y" k="10" />
    <hkern u1="&#xf8;" u2="w" k="-3" />
    <hkern u1="&#xfd;" u2="a" k="5" />
    <hkern u1="&#xfd;" u2="&#x2e;" k="45" />
    <hkern u1="&#xfd;" u2="&#x2c;" k="45" />
    <hkern u1="&#xfe;" u2="&#x2026;" k="27" />
    <hkern u1="&#xfe;" u2="&#x201c;" k="70" />
    <hkern u1="&#xfe;" u2="&#x2018;" k="70" />
    <hkern u1="&#xfe;" u2="z" k="15" />
    <hkern u1="&#xfe;" u2="y" k="5" />
    <hkern u1="&#xfe;" u2="\" k="65" />
    <hkern u1="&#xfe;" u2="&#x2e;" k="27" />
    <hkern u1="&#xfe;" u2="&#x2c;" k="27" />
    <hkern u1="&#xff;" u2="a" k="5" />
    <hkern u1="&#xff;" u2="&#x2e;" k="45" />
    <hkern u1="&#xff;" u2="&#x2c;" k="45" />
    <hkern u1="&#x100;" u2="&#x201d;" k="27" />
    <hkern u1="&#x100;" u2="&#x2019;" k="27" />
    <hkern u1="&#x100;" u2="y" k="25" />
    <hkern u1="&#x100;" u2="w" k="20" />
    <hkern u1="&#x100;" u2="v" k="25" />
    <hkern u1="&#x100;" u2="t" k="20" />
    <hkern u1="&#x100;" u2="\" k="75" />
    <hkern u1="&#x100;" u2="Y" k="90" />
    <hkern u1="&#x100;" u2="W" k="35" />
    <hkern u1="&#x100;" u2="V" k="70" />
    <hkern u1="&#x100;" u2="U" k="20" />
    <hkern u1="&#x100;" u2="T" k="75" />
    <hkern u1="&#x100;" u2="Q" k="20" />
    <hkern u1="&#x100;" u2="O" k="20" />
    <hkern u1="&#x100;" u2="J" k="-47" />
    <hkern u1="&#x100;" u2="G" k="20" />
    <hkern u1="&#x100;" u2="C" k="20" />
    <hkern u1="&#x100;" u2="&#x2a;" k="34" />
    <hkern u1="&#x101;" u2="y" k="3" />
    <hkern u1="&#x102;" u2="&#x201d;" k="27" />
    <hkern u1="&#x102;" u2="&#x2019;" k="27" />
    <hkern u1="&#x102;" u2="y" k="25" />
    <hkern u1="&#x102;" u2="w" k="20" />
    <hkern u1="&#x102;" u2="v" k="25" />
    <hkern u1="&#x102;" u2="t" k="20" />
    <hkern u1="&#x102;" u2="\" k="75" />
    <hkern u1="&#x102;" u2="Y" k="90" />
    <hkern u1="&#x102;" u2="W" k="35" />
    <hkern u1="&#x102;" u2="V" k="70" />
    <hkern u1="&#x102;" u2="U" k="20" />
    <hkern u1="&#x102;" u2="T" k="75" />
    <hkern u1="&#x102;" u2="Q" k="20" />
    <hkern u1="&#x102;" u2="O" k="20" />
    <hkern u1="&#x102;" u2="J" k="-47" />
    <hkern u1="&#x102;" u2="G" k="20" />
    <hkern u1="&#x102;" u2="C" k="20" />
    <hkern u1="&#x102;" u2="&#x2a;" k="34" />
    <hkern u1="&#x103;" u2="y" k="3" />
    <hkern u1="&#x104;" u2="&#x201d;" k="27" />
    <hkern u1="&#x104;" u2="&#x2019;" k="27" />
    <hkern u1="&#x104;" u2="y" k="-10" />
    <hkern u1="&#x104;" u2="w" k="20" />
    <hkern u1="&#x104;" u2="v" k="25" />
    <hkern u1="&#x104;" u2="t" k="20" />
    <hkern u1="&#x104;" u2="\" k="75" />
    <hkern u1="&#x104;" u2="Y" k="90" />
    <hkern u1="&#x104;" u2="W" k="35" />
    <hkern u1="&#x104;" u2="V" k="70" />
    <hkern u1="&#x104;" u2="U" k="20" />
    <hkern u1="&#x104;" u2="T" k="75" />
    <hkern u1="&#x104;" u2="Q" k="20" />
    <hkern u1="&#x104;" u2="O" k="20" />
    <hkern u1="&#x104;" u2="J" k="-47" />
    <hkern u1="&#x104;" u2="G" k="20" />
    <hkern u1="&#x104;" u2="C" k="20" />
    <hkern u1="&#x104;" u2="&#x2a;" k="34" />
    <hkern u1="&#x106;" u2="Q" k="10" />
    <hkern u1="&#x106;" u2="O" k="10" />
    <hkern u1="&#x106;" u2="G" k="10" />
    <hkern u1="&#x106;" u2="C" k="10" />
    <hkern u1="&#x107;" u2="c" k="10" />
    <hkern u1="&#x108;" u2="Q" k="10" />
    <hkern u1="&#x108;" u2="O" k="10" />
    <hkern u1="&#x108;" u2="G" k="10" />
    <hkern u1="&#x108;" u2="C" k="10" />
    <hkern u1="&#x109;" u2="c" k="10" />
    <hkern u1="&#x10a;" u2="Q" k="10" />
    <hkern u1="&#x10a;" u2="C" k="10" />
    <hkern u1="&#x10b;" u2="c" k="10" />
    <hkern u1="&#x10c;" u2="Q" k="10" />
    <hkern u1="&#x10c;" u2="O" k="10" />
    <hkern u1="&#x10c;" u2="G" k="10" />
    <hkern u1="&#x10c;" u2="C" k="10" />
    <hkern u1="&#x10d;" u2="c" k="10" />
    <hkern u1="&#x10e;" u2="&#xc6;" k="47" />
    <hkern u1="&#x10e;" u2="Y" k="35" />
    <hkern u1="&#x10e;" u2="X" k="20" />
    <hkern u1="&#x10e;" u2="V" k="10" />
    <hkern u1="&#x10e;" u2="T" k="35" />
    <hkern u1="&#x10e;" u2="J" k="41" />
    <hkern u1="&#x10e;" u2="A" k="20" />
    <hkern u1="&#x110;" u2="&#xc6;" k="47" />
    <hkern u1="&#x110;" u2="Y" k="35" />
    <hkern u1="&#x110;" u2="X" k="20" />
    <hkern u1="&#x110;" u2="V" k="10" />
    <hkern u1="&#x110;" u2="T" k="35" />
    <hkern u1="&#x110;" u2="J" k="41" />
    <hkern u1="&#x110;" u2="A" k="20" />
    <hkern u1="&#x113;" u2="x" k="2" />
    <hkern u1="&#x115;" u2="x" k="2" />
    <hkern u1="&#x117;" u2="x" k="2" />
    <hkern u1="&#x119;" u2="x" k="2" />
    <hkern u1="&#x11b;" u2="x" k="2" />
    <hkern u1="&#x11c;" u2="Y" k="20" />
    <hkern u1="&#x11e;" u2="Y" k="20" />
    <hkern u1="&#x120;" u2="Y" k="20" />
    <hkern u1="&#x122;" u2="Y" k="20" />
    <hkern u1="&#x134;" u2="A" k="10" />
    <hkern u1="&#x136;" u2="U" k="15" />
    <hkern u1="&#x136;" u2="Q" k="25" />
    <hkern u1="&#x136;" u2="O" k="25" />
    <hkern u1="&#x136;" u2="G" k="25" />
    <hkern u1="&#x136;" u2="C" k="25" />
    <hkern u1="&#x137;" u2="o" k="10" />
    <hkern u1="&#x137;" u2="g" k="7" />
    <hkern u1="&#x137;" u2="e" k="10" />
    <hkern u1="&#x137;" u2="c" k="10" />
    <hkern u1="&#x139;" u2="&#x201d;" k="27" />
    <hkern u1="&#x139;" u2="&#x2019;" k="27" />
    <hkern u1="&#x139;" u2="&#x152;" k="30" />
    <hkern u1="&#x139;" u2="\" k="108" />
    <hkern u1="&#x139;" u2="Y" k="94" />
    <hkern u1="&#x139;" u2="W" k="35" />
    <hkern u1="&#x139;" u2="V" k="70" />
    <hkern u1="&#x139;" u2="U" k="25" />
    <hkern u1="&#x139;" u2="T" k="90" />
    <hkern u1="&#x139;" u2="Q" k="30" />
    <hkern u1="&#x139;" u2="O" k="30" />
    <hkern u1="&#x139;" u2="J" k="-33" />
    <hkern u1="&#x139;" u2="G" k="30" />
    <hkern u1="&#x139;" u2="C" k="30" />
    <hkern u1="&#x139;" u2="&#x2a;" k="85" />
    <hkern u1="&#x13b;" u2="&#x201d;" k="27" />
    <hkern u1="&#x13b;" u2="&#x2019;" k="27" />
    <hkern u1="&#x13b;" u2="&#x152;" k="30" />
    <hkern u1="&#x13b;" u2="\" k="108" />
    <hkern u1="&#x13b;" u2="Y" k="94" />
    <hkern u1="&#x13b;" u2="W" k="35" />
    <hkern u1="&#x13b;" u2="V" k="70" />
    <hkern u1="&#x13b;" u2="U" k="25" />
    <hkern u1="&#x13b;" u2="T" k="90" />
    <hkern u1="&#x13b;" u2="Q" k="30" />
    <hkern u1="&#x13b;" u2="O" k="30" />
    <hkern u1="&#x13b;" u2="J" k="-33" />
    <hkern u1="&#x13b;" u2="G" k="30" />
    <hkern u1="&#x13b;" u2="C" k="30" />
    <hkern u1="&#x13b;" u2="&#x2a;" k="85" />
    <hkern u1="&#x13d;" u2="&#x201d;" k="27" />
    <hkern u1="&#x13d;" u2="&#x2019;" k="27" />
    <hkern u1="&#x13d;" u2="&#x152;" k="30" />
    <hkern u1="&#x13d;" u2="\" k="108" />
    <hkern u1="&#x13d;" u2="Y" k="94" />
    <hkern u1="&#x13d;" u2="W" k="35" />
    <hkern u1="&#x13d;" u2="V" k="70" />
    <hkern u1="&#x13d;" u2="U" k="25" />
    <hkern u1="&#x13d;" u2="T" k="90" />
    <hkern u1="&#x13d;" u2="Q" k="30" />
    <hkern u1="&#x13d;" u2="O" k="30" />
    <hkern u1="&#x13d;" u2="J" k="-33" />
    <hkern u1="&#x13d;" u2="G" k="30" />
    <hkern u1="&#x13d;" u2="C" k="30" />
    <hkern u1="&#x13d;" u2="&#x2a;" k="85" />
    <hkern u1="&#x13f;" u2="&#x201d;" k="27" />
    <hkern u1="&#x13f;" u2="&#x2019;" k="27" />
    <hkern u1="&#x13f;" u2="&#x152;" k="30" />
    <hkern u1="&#x13f;" u2="\" k="108" />
    <hkern u1="&#x13f;" u2="Y" k="94" />
    <hkern u1="&#x13f;" u2="W" k="35" />
    <hkern u1="&#x13f;" u2="V" k="70" />
    <hkern u1="&#x13f;" u2="U" k="25" />
    <hkern u1="&#x13f;" u2="T" k="90" />
    <hkern u1="&#x13f;" u2="Q" k="30" />
    <hkern u1="&#x13f;" u2="O" k="30" />
    <hkern u1="&#x13f;" u2="J" k="-33" />
    <hkern u1="&#x13f;" u2="G" k="30" />
    <hkern u1="&#x13f;" u2="C" k="30" />
    <hkern u1="&#x13f;" u2="&#x2a;" k="85" />
    <hkern u1="&#x141;" u2="&#x201d;" k="27" />
    <hkern u1="&#x141;" u2="&#x2019;" k="27" />
    <hkern u1="&#x141;" u2="&#x152;" k="30" />
    <hkern u1="&#x141;" u2="\" k="108" />
    <hkern u1="&#x141;" u2="Y" k="94" />
    <hkern u1="&#x141;" u2="W" k="35" />
    <hkern u1="&#x141;" u2="V" k="70" />
    <hkern u1="&#x141;" u2="U" k="25" />
    <hkern u1="&#x141;" u2="T" k="90" />
    <hkern u1="&#x141;" u2="Q" k="30" />
    <hkern u1="&#x141;" u2="O" k="30" />
    <hkern u1="&#x141;" u2="J" k="-33" />
    <hkern u1="&#x141;" u2="G" k="30" />
    <hkern u1="&#x141;" u2="C" k="30" />
    <hkern u1="&#x141;" u2="&#x2a;" k="85" />
    <hkern u1="&#x14b;" u2="&#x201c;" k="60" />
    <hkern u1="&#x14b;" u2="&#x2018;" k="60" />
    <hkern u1="&#x14b;" u2="\" k="70" />
    <hkern u1="&#x14c;" u2="&#xc6;" k="33" />
    <hkern u1="&#x14c;" u2="Y" k="40" />
    <hkern u1="&#x14c;" u2="X" k="25" />
    <hkern u1="&#x14c;" u2="V" k="15" />
    <hkern u1="&#x14c;" u2="T" k="25" />
    <hkern u1="&#x14c;" u2="J" k="10" />
    <hkern u1="&#x14c;" u2="A" k="20" />
    <hkern u1="&#x14d;" u2="y" k="10" />
    <hkern u1="&#x14d;" u2="w" k="-3" />
    <hkern u1="&#x14e;" u2="&#xc6;" k="33" />
    <hkern u1="&#x14e;" u2="X" k="25" />
    <hkern u1="&#x14e;" u2="V" k="15" />
    <hkern u1="&#x14e;" u2="T" k="25" />
    <hkern u1="&#x14e;" u2="J" k="10" />
    <hkern u1="&#x14f;" u2="y" k="10" />
    <hkern u1="&#x14f;" u2="w" k="-3" />
    <hkern u1="&#x150;" u2="&#xc6;" k="33" />
    <hkern u1="&#x150;" u2="Y" k="40" />
    <hkern u1="&#x150;" u2="X" k="25" />
    <hkern u1="&#x150;" u2="V" k="15" />
    <hkern u1="&#x150;" u2="T" k="25" />
    <hkern u1="&#x150;" u2="J" k="10" />
    <hkern u1="&#x150;" u2="A" k="20" />
    <hkern u1="&#x151;" u2="y" k="10" />
    <hkern u1="&#x151;" u2="w" k="-3" />
    <hkern u1="&#x152;" u2="&#x153;" k="25" />
    <hkern u1="&#x152;" u2="&#x152;" k="30" />
    <hkern u1="&#x152;" u2="&#xf0;" k="15" />
    <hkern u1="&#x152;" u2="y" k="15" />
    <hkern u1="&#x152;" u2="w" k="15" />
    <hkern u1="&#x152;" u2="v" k="20" />
    <hkern u1="&#x152;" u2="u" k="10" />
    <hkern u1="&#x152;" u2="t" k="15" />
    <hkern u1="&#x152;" u2="q" k="25" />
    <hkern u1="&#x152;" u2="o" k="25" />
    <hkern u1="&#x152;" u2="g" k="25" />
    <hkern u1="&#x152;" u2="e" k="25" />
    <hkern u1="&#x152;" u2="d" k="25" />
    <hkern u1="&#x152;" u2="c" k="25" />
    <hkern u1="&#x152;" u2="Q" k="30" />
    <hkern u1="&#x152;" u2="O" k="30" />
    <hkern u1="&#x152;" u2="G" k="30" />
    <hkern u1="&#x152;" u2="C" k="30" />
    <hkern u1="&#x153;" u2="&#x201c;" k="55" />
    <hkern u1="&#x153;" u2="&#x2018;" k="55" />
    <hkern u1="&#x153;" u2="x" k="2" />
    <hkern u1="&#x153;" u2="v" k="5" />
    <hkern u1="&#x153;" u2="\" k="56" />
    <hkern u1="&#x154;" u2="Y" k="10" />
    <hkern u1="&#x154;" u2="T" k="10" />
    <hkern u1="&#x154;" u2="O" k="10" />
    <hkern u1="&#x154;" u2="G" k="10" />
    <hkern u1="&#x154;" u2="C" k="10" />
    <hkern u1="&#x155;" u2="s" k="5" />
    <hkern u1="&#x155;" u2="q" k="20" />
    <hkern u1="&#x155;" u2="o" k="20" />
    <hkern u1="&#x155;" u2="g" k="20" />
    <hkern u1="&#x155;" u2="e" k="20" />
    <hkern u1="&#x155;" u2="d" k="20" />
    <hkern u1="&#x155;" u2="c" k="20" />
    <hkern u1="&#x155;" u2="a" k="15" />
    <hkern u1="&#x156;" u2="Y" k="10" />
    <hkern u1="&#x156;" u2="T" k="10" />
    <hkern u1="&#x156;" u2="O" k="10" />
    <hkern u1="&#x156;" u2="G" k="10" />
    <hkern u1="&#x156;" u2="C" k="10" />
    <hkern u1="&#x157;" u2="s" k="5" />
    <hkern u1="&#x157;" u2="q" k="20" />
    <hkern u1="&#x157;" u2="o" k="20" />
    <hkern u1="&#x157;" u2="g" k="20" />
    <hkern u1="&#x157;" u2="e" k="20" />
    <hkern u1="&#x157;" u2="d" k="20" />
    <hkern u1="&#x157;" u2="c" k="20" />
    <hkern u1="&#x157;" u2="a" k="15" />
    <hkern u1="&#x158;" u2="Y" k="10" />
    <hkern u1="&#x158;" u2="T" k="10" />
    <hkern u1="&#x158;" u2="O" k="10" />
    <hkern u1="&#x158;" u2="G" k="10" />
    <hkern u1="&#x158;" u2="C" k="10" />
    <hkern u1="&#x159;" u2="s" k="5" />
    <hkern u1="&#x159;" u2="q" k="20" />
    <hkern u1="&#x159;" u2="o" k="20" />
    <hkern u1="&#x159;" u2="g" k="20" />
    <hkern u1="&#x159;" u2="e" k="20" />
    <hkern u1="&#x159;" u2="d" k="20" />
    <hkern u1="&#x159;" u2="c" k="20" />
    <hkern u1="&#x159;" u2="a" k="15" />
    <hkern u1="&#x162;" u2="&#x203a;" k="45" />
    <hkern u1="&#x162;" u2="&#x2026;" k="75" />
    <hkern u1="&#x162;" u2="&#x152;" k="40" />
    <hkern u1="&#x162;" u2="&#xc6;" k="79" />
    <hkern u1="&#x162;" u2="&#xbb;" k="45" />
    <hkern u1="&#x162;" u2="y" k="51" />
    <hkern u1="&#x162;" u2="w" k="47" />
    <hkern u1="&#x162;" u2="v" k="37" />
    <hkern u1="&#x162;" u2="u" k="75" />
    <hkern u1="&#x162;" u2="s" k="75" />
    <hkern u1="&#x162;" u2="r" k="75" />
    <hkern u1="&#x162;" u2="o" k="70" />
    <hkern u1="&#x162;" u2="e" k="70" />
    <hkern u1="&#x162;" u2="c" k="70" />
    <hkern u1="&#x162;" u2="a" k="84" />
    <hkern u1="&#x162;" u2="Q" k="40" />
    <hkern u1="&#x162;" u2="O" k="40" />
    <hkern u1="&#x162;" u2="J" k="90" />
    <hkern u1="&#x162;" u2="G" k="40" />
    <hkern u1="&#x162;" u2="C" k="40" />
    <hkern u1="&#x162;" u2="A" k="75" />
    <hkern u1="&#x162;" u2="&#x3b;" k="15" />
    <hkern u1="&#x162;" u2="&#x3a;" k="15" />
    <hkern u1="&#x162;" u2="&#x2e;" k="75" />
    <hkern u1="&#x162;" u2="&#x2d;" k="24" />
    <hkern u1="&#x162;" u2="&#x2c;" k="75" />
    <hkern u1="&#x163;" u2="o" k="10" />
    <hkern u1="&#x163;" u2="e" k="10" />
    <hkern u1="&#x163;" u2="c" k="10" />
    <hkern u1="&#x164;" u2="&#x203a;" k="45" />
    <hkern u1="&#x164;" u2="&#x2026;" k="75" />
    <hkern u1="&#x164;" u2="&#x153;" k="70" />
    <hkern u1="&#x164;" u2="&#x152;" k="40" />
    <hkern u1="&#x164;" u2="&#xc6;" k="79" />
    <hkern u1="&#x164;" u2="&#xbb;" k="45" />
    <hkern u1="&#x164;" u2="y" k="51" />
    <hkern u1="&#x164;" u2="w" k="47" />
    <hkern u1="&#x164;" u2="v" k="37" />
    <hkern u1="&#x164;" u2="u" k="75" />
    <hkern u1="&#x164;" u2="s" k="75" />
    <hkern u1="&#x164;" u2="r" k="75" />
    <hkern u1="&#x164;" u2="o" k="70" />
    <hkern u1="&#x164;" u2="e" k="70" />
    <hkern u1="&#x164;" u2="c" k="70" />
    <hkern u1="&#x164;" u2="a" k="84" />
    <hkern u1="&#x164;" u2="Q" k="40" />
    <hkern u1="&#x164;" u2="O" k="40" />
    <hkern u1="&#x164;" u2="J" k="90" />
    <hkern u1="&#x164;" u2="G" k="40" />
    <hkern u1="&#x164;" u2="C" k="40" />
    <hkern u1="&#x164;" u2="A" k="75" />
    <hkern u1="&#x164;" u2="&#x3b;" k="15" />
    <hkern u1="&#x164;" u2="&#x3a;" k="15" />
    <hkern u1="&#x164;" u2="&#x2e;" k="75" />
    <hkern u1="&#x164;" u2="&#x2d;" k="24" />
    <hkern u1="&#x164;" u2="&#x2c;" k="75" />
    <hkern u1="&#x165;" u2="o" k="10" />
    <hkern u1="&#x165;" u2="e" k="10" />
    <hkern u1="&#x165;" u2="c" k="10" />
    <hkern u1="&#x166;" u2="&#x203a;" k="45" />
    <hkern u1="&#x166;" u2="&#x2026;" k="75" />
    <hkern u1="&#x166;" u2="&#x153;" k="70" />
    <hkern u1="&#x166;" u2="&#x152;" k="40" />
    <hkern u1="&#x166;" u2="&#xc6;" k="79" />
    <hkern u1="&#x166;" u2="&#xbb;" k="45" />
    <hkern u1="&#x166;" u2="y" k="51" />
    <hkern u1="&#x166;" u2="w" k="47" />
    <hkern u1="&#x166;" u2="v" k="37" />
    <hkern u1="&#x166;" u2="u" k="75" />
    <hkern u1="&#x166;" u2="s" k="55" />
    <hkern u1="&#x166;" u2="r" k="75" />
    <hkern u1="&#x166;" u2="o" k="70" />
    <hkern u1="&#x166;" u2="e" k="70" />
    <hkern u1="&#x166;" u2="c" k="70" />
    <hkern u1="&#x166;" u2="a" k="84" />
    <hkern u1="&#x166;" u2="Q" k="40" />
    <hkern u1="&#x166;" u2="O" k="40" />
    <hkern u1="&#x166;" u2="J" k="90" />
    <hkern u1="&#x166;" u2="G" k="40" />
    <hkern u1="&#x166;" u2="C" k="40" />
    <hkern u1="&#x166;" u2="A" k="75" />
    <hkern u1="&#x166;" u2="&#x3b;" k="15" />
    <hkern u1="&#x166;" u2="&#x3a;" k="15" />
    <hkern u1="&#x166;" u2="&#x2e;" k="75" />
    <hkern u1="&#x166;" u2="&#x2d;" k="24" />
    <hkern u1="&#x166;" u2="&#x2c;" k="75" />
    <hkern u1="&#x167;" u2="o" k="10" />
    <hkern u1="&#x167;" u2="e" k="10" />
    <hkern u1="&#x167;" u2="c" k="10" />
    <hkern u1="&#x16a;" u2="&#xc6;" k="33" />
    <hkern u1="&#x16a;" u2="A" k="20" />
    <hkern u1="&#x16c;" u2="&#xc6;" k="33" />
    <hkern u1="&#x16c;" u2="A" k="20" />
    <hkern u1="&#x16e;" u2="&#xc6;" k="33" />
    <hkern u1="&#x16e;" u2="A" k="20" />
    <hkern u1="&#x170;" u2="&#xc6;" k="33" />
    <hkern u1="&#x170;" u2="A" k="20" />
    <hkern u1="&#x172;" u2="&#xc6;" k="33" />
    <hkern u1="&#x172;" u2="A" k="20" />
    <hkern u1="&#x174;" u2="&#xc6;" k="45" />
    <hkern u1="&#x174;" u2="u" k="15" />
    <hkern u1="&#x174;" u2="s" k="25" />
    <hkern u1="&#x174;" u2="r" k="15" />
    <hkern u1="&#x174;" u2="o" k="20" />
    <hkern u1="&#x174;" u2="e" k="20" />
    <hkern u1="&#x174;" u2="a" k="25" />
    <hkern u1="&#x174;" u2="J" k="40" />
    <hkern u1="&#x174;" u2="A" k="35" />
    <hkern u1="&#x174;" u2="&#x26;" k="19" />
    <hkern u1="&#x175;" u2="o" k="-3" />
    <hkern u1="&#x175;" u2="e" k="-3" />
    <hkern u1="&#x176;" u2="&#x153;" k="90" />
    <hkern u1="&#x176;" u2="&#x152;" k="50" />
    <hkern u1="&#x176;" u2="&#xe6;" k="75" />
    <hkern u1="&#x176;" u2="&#xc6;" k="105" />
    <hkern u1="&#x176;" u2="&#xbb;" k="45" />
    <hkern u1="&#x176;" u2="w" k="19" />
    <hkern u1="&#x176;" u2="v" k="23" />
    <hkern u1="&#x176;" u2="u" k="61" />
    <hkern u1="&#x176;" u2="t" k="28" />
    <hkern u1="&#x176;" u2="p" k="70" />
    <hkern u1="&#x176;" u2="o" k="90" />
    <hkern u1="&#x176;" u2="n" k="70" />
    <hkern u1="&#x176;" u2="m" k="70" />
    <hkern u1="&#x176;" u2="g" k="90" />
    <hkern u1="&#x176;" u2="f" k="33" />
    <hkern u1="&#x176;" u2="e" k="90" />
    <hkern u1="&#x176;" u2="d" k="90" />
    <hkern u1="&#x176;" u2="c" k="90" />
    <hkern u1="&#x176;" u2="a" k="75" />
    <hkern u1="&#x176;" u2="S" k="10" />
    <hkern u1="&#x176;" u2="Q" k="50" />
    <hkern u1="&#x176;" u2="O" k="50" />
    <hkern u1="&#x176;" u2="J" k="105" />
    <hkern u1="&#x176;" u2="G" k="50" />
    <hkern u1="&#x176;" u2="C" k="50" />
    <hkern u1="&#x176;" u2="A" k="90" />
    <hkern u1="&#x176;" u2="&#x3b;" k="26" />
    <hkern u1="&#x176;" u2="&#x3a;" k="26" />
    <hkern u1="&#x176;" u2="&#x2f;" k="56" />
    <hkern u1="&#x176;" u2="&#x2e;" k="79" />
    <hkern u1="&#x176;" u2="&#x2d;" k="32" />
    <hkern u1="&#x176;" u2="&#x2c;" k="79" />
    <hkern u1="&#x176;" u2="&#x26;" k="66" />
    <hkern u1="&#x177;" u2="a" k="5" />
    <hkern u1="&#x177;" u2="&#x2e;" k="45" />
    <hkern u1="&#x177;" u2="&#x2c;" k="45" />
    <hkern u1="&#x178;" u2="&#x153;" k="90" />
    <hkern u1="&#x178;" u2="&#x152;" k="50" />
    <hkern u1="&#x178;" u2="&#xe6;" k="75" />
    <hkern u1="&#x178;" u2="&#xc6;" k="105" />
    <hkern u1="&#x178;" u2="&#xbb;" k="45" />
    <hkern u1="&#x178;" u2="w" k="19" />
    <hkern u1="&#x178;" u2="v" k="23" />
    <hkern u1="&#x178;" u2="u" k="61" />
    <hkern u1="&#x178;" u2="t" k="28" />
    <hkern u1="&#x178;" u2="p" k="70" />
    <hkern u1="&#x178;" u2="o" k="90" />
    <hkern u1="&#x178;" u2="n" k="70" />
    <hkern u1="&#x178;" u2="m" k="70" />
    <hkern u1="&#x178;" u2="g" k="90" />
    <hkern u1="&#x178;" u2="f" k="33" />
    <hkern u1="&#x178;" u2="e" k="90" />
    <hkern u1="&#x178;" u2="d" k="90" />
    <hkern u1="&#x178;" u2="c" k="90" />
    <hkern u1="&#x178;" u2="a" k="75" />
    <hkern u1="&#x178;" u2="S" k="10" />
    <hkern u1="&#x178;" u2="Q" k="50" />
    <hkern u1="&#x178;" u2="O" k="50" />
    <hkern u1="&#x178;" u2="J" k="105" />
    <hkern u1="&#x178;" u2="G" k="50" />
    <hkern u1="&#x178;" u2="C" k="50" />
    <hkern u1="&#x178;" u2="A" k="90" />
    <hkern u1="&#x178;" u2="&#x3b;" k="26" />
    <hkern u1="&#x178;" u2="&#x3a;" k="26" />
    <hkern u1="&#x178;" u2="&#x2f;" k="56" />
    <hkern u1="&#x178;" u2="&#x2e;" k="79" />
    <hkern u1="&#x178;" u2="&#x2d;" k="32" />
    <hkern u1="&#x178;" u2="&#x2c;" k="79" />
    <hkern u1="&#x178;" u2="&#x26;" k="66" />
    <hkern u1="&#x386;" u2="&#x201d;" k="34" />
    <hkern u1="&#x386;" u2="&#x2019;" k="80" />
    <hkern u1="&#x386;" u2="&#x3cd;" k="15" />
    <hkern u1="&#x386;" u2="&#x3cb;" k="15" />
    <hkern u1="&#x386;" u2="&#x3c7;" k="30" />
    <hkern u1="&#x386;" u2="&#x3c6;" k="10" />
    <hkern u1="&#x386;" u2="&#x3c4;" k="50" />
    <hkern u1="&#x386;" u2="&#x3c0;" k="25" />
    <hkern u1="&#x386;" u2="&#x3bd;" k="35" />
    <hkern u1="&#x386;" u2="&#x3b3;" k="30" />
    <hkern u1="&#x386;" u2="&#x3b0;" k="15" />
    <hkern u1="&#x386;" u2="&#x3ab;" k="90" />
    <hkern u1="&#x386;" u2="&#x3a8;" k="65" />
    <hkern u1="&#x386;" u2="&#x3a6;" k="10" />
    <hkern u1="&#x386;" u2="&#x3a5;" k="90" />
    <hkern u1="&#x386;" u2="&#x3a4;" k="80" />
    <hkern u1="&#x386;" u2="&#x39f;" k="10" />
    <hkern u1="&#x386;" u2="&#x398;" k="10" />
    <hkern u1="&#x388;" u2="&#x3cd;" k="20" />
    <hkern u1="&#x388;" u2="&#x3cc;" k="15" />
    <hkern u1="&#x388;" u2="&#x3cb;" k="20" />
    <hkern u1="&#x388;" u2="&#x3ca;" k="10" />
    <hkern u1="&#x388;" u2="&#x3c8;" k="20" />
    <hkern u1="&#x388;" u2="&#x3c7;" k="30" />
    <hkern u1="&#x388;" u2="&#x3c6;" k="20" />
    <hkern u1="&#x388;" u2="&#x3c5;" k="20" />
    <hkern u1="&#x388;" u2="&#x3c4;" k="40" />
    <hkern u1="&#x388;" u2="&#x3c3;" k="15" />
    <hkern u1="&#x388;" u2="&#x3c0;" k="30" />
    <hkern u1="&#x388;" u2="&#x3bf;" k="15" />
    <hkern u1="&#x388;" u2="&#x3bd;" k="30" />
    <hkern u1="&#x388;" u2="&#x3b9;" k="10" />
    <hkern u1="&#x388;" u2="&#x3b8;" k="15" />
    <hkern u1="&#x388;" u2="&#x3b4;" k="20" />
    <hkern u1="&#x388;" u2="&#x3b3;" k="25" />
    <hkern u1="&#x388;" u2="&#x3b1;" k="20" />
    <hkern u1="&#x388;" u2="&#x3b0;" k="20" />
    <hkern u1="&#x388;" u2="&#x3af;" k="10" />
    <hkern u1="&#x388;" u2="&#x3ac;" k="20" />
    <hkern u1="&#x388;" u2="&#x3a6;" k="20" />
    <hkern u1="&#x388;" u2="&#x39f;" k="15" />
    <hkern u1="&#x388;" u2="&#x398;" k="15" />
    <hkern u1="&#x388;" u2="&#x390;" k="10" />
    <hkern u1="&#x38c;" u2="&#x3bb;" k="30" />
    <hkern u1="&#x38c;" u2="&#x3ab;" k="30" />
    <hkern u1="&#x38c;" u2="&#x3a7;" k="15" />
    <hkern u1="&#x38c;" u2="&#x3a5;" k="30" />
    <hkern u1="&#x38c;" u2="&#x3a4;" k="25" />
    <hkern u1="&#x38c;" u2="&#x3a3;" k="35" />
    <hkern u1="&#x38c;" u2="&#x39e;" k="25" />
    <hkern u1="&#x38c;" u2="&#x39b;" k="10" />
    <hkern u1="&#x38c;" u2="&#x394;" k="10" />
    <hkern u1="&#x38c;" u2="&#x391;" k="10" />
    <hkern u1="&#x38e;" u2="&#x203a;" k="45" />
    <hkern u1="&#x38e;" u2="&#x2026;" k="100" />
    <hkern u1="&#x38e;" u2="&#x2014;" k="56" />
    <hkern u1="&#x38e;" u2="&#x2013;" k="56" />
    <hkern u1="&#x38e;" u2="&#x3ce;" k="70" />
    <hkern u1="&#x38e;" u2="&#x3cd;" k="20" />
    <hkern u1="&#x38e;" u2="&#x3cc;" k="80" />
    <hkern u1="&#x38e;" u2="&#x3cb;" k="20" />
    <hkern u1="&#x38e;" u2="&#x3ca;" k="40" />
    <hkern u1="&#x38e;" u2="&#x3c9;" k="70" />
    <hkern u1="&#x38e;" u2="&#x3c8;" k="30" />
    <hkern u1="&#x38e;" u2="&#x3c6;" k="90" />
    <hkern u1="&#x38e;" u2="&#x3c5;" k="20" />
    <hkern u1="&#x38e;" u2="&#x3c4;" k="30" />
    <hkern u1="&#x38e;" u2="&#x3c3;" k="80" />
    <hkern u1="&#x38e;" u2="&#x3c1;" k="100" />
    <hkern u1="&#x38e;" u2="&#x3c0;" k="30" />
    <hkern u1="&#x38e;" u2="&#x3bf;" k="80" />
    <hkern u1="&#x38e;" u2="&#x3bc;" k="60" />
    <hkern u1="&#x38e;" u2="&#x3ba;" k="40" />
    <hkern u1="&#x38e;" u2="&#x3b9;" k="40" />
    <hkern u1="&#x38e;" u2="&#x3b5;" k="80" />
    <hkern u1="&#x38e;" u2="&#x3b4;" k="40" />
    <hkern u1="&#x38e;" u2="&#x3b2;" k="20" />
    <hkern u1="&#x38e;" u2="&#x3b1;" k="75" />
    <hkern u1="&#x38e;" u2="&#x3b0;" k="20" />
    <hkern u1="&#x38e;" u2="&#x3af;" k="40" />
    <hkern u1="&#x38e;" u2="&#x3ad;" k="80" />
    <hkern u1="&#x38e;" u2="&#x3ac;" k="75" />
    <hkern u1="&#x38e;" u2="&#x3a9;" k="40" />
    <hkern u1="&#x38e;" u2="&#x3a6;" k="70" />
    <hkern u1="&#x38e;" u2="&#x39f;" k="40" />
    <hkern u1="&#x38e;" u2="&#x39b;" k="85" />
    <hkern u1="&#x38e;" u2="&#x398;" k="40" />
    <hkern u1="&#x38e;" u2="&#x394;" k="85" />
    <hkern u1="&#x38e;" u2="&#x391;" k="85" />
    <hkern u1="&#x38e;" u2="&#x390;" k="40" />
    <hkern u1="&#x38e;" u2="&#xbb;" k="45" />
    <hkern u1="&#x38e;" u2="&#xb5;" k="50" />
    <hkern u1="&#x38e;" u2="&#x2e;" k="100" />
    <hkern u1="&#x38e;" u2="&#x2d;" k="56" />
    <hkern u1="&#x38e;" u2="&#x2c;" k="100" />
    <hkern u1="&#x38f;" u2="&#x3ab;" k="40" />
    <hkern u1="&#x38f;" u2="&#x3a5;" k="40" />
    <hkern u1="&#x38f;" u2="&#x3a4;" k="30" />
    <hkern u1="&#x390;" u2="&#x3cc;" k="15" />
    <hkern u1="&#x390;" u2="&#x3c6;" k="20" />
    <hkern u1="&#x390;" u2="&#x3c4;" k="20" />
    <hkern u1="&#x390;" u2="&#x3c3;" k="15" />
    <hkern u1="&#x390;" u2="&#x3c0;" k="20" />
    <hkern u1="&#x390;" u2="&#x3bf;" k="15" />
    <hkern u1="&#x390;" u2="&#x3b8;" k="10" />
    <hkern u1="&#x390;" u2="&#x3b4;" k="10" />
    <hkern u1="&#x390;" u2="&#x3b1;" k="7" />
    <hkern u1="&#x390;" u2="&#x3ac;" k="7" />
    <hkern u1="&#x391;" u2="&#x201d;" k="34" />
    <hkern u1="&#x391;" u2="&#x2019;" k="80" />
    <hkern u1="&#x391;" u2="&#x3cd;" k="15" />
    <hkern u1="&#x391;" u2="&#x3cb;" k="15" />
    <hkern u1="&#x391;" u2="&#x3c7;" k="30" />
    <hkern u1="&#x391;" u2="&#x3c6;" k="10" />
    <hkern u1="&#x391;" u2="&#x3c4;" k="50" />
    <hkern u1="&#x391;" u2="&#x3c0;" k="25" />
    <hkern u1="&#x391;" u2="&#x3bd;" k="35" />
    <hkern u1="&#x391;" u2="&#x3b3;" k="30" />
    <hkern u1="&#x391;" u2="&#x3b0;" k="15" />
    <hkern u1="&#x391;" u2="&#x3ab;" k="90" />
    <hkern u1="&#x391;" u2="&#x3a8;" k="65" />
    <hkern u1="&#x391;" u2="&#x3a6;" k="10" />
    <hkern u1="&#x391;" u2="&#x3a5;" k="90" />
    <hkern u1="&#x391;" u2="&#x3a4;" k="80" />
    <hkern u1="&#x391;" u2="&#x39f;" k="10" />
    <hkern u1="&#x391;" u2="&#x398;" k="10" />
    <hkern u1="&#x393;" u2="&#x203a;" k="59" />
    <hkern u1="&#x393;" u2="&#x2026;" k="120" />
    <hkern u1="&#x393;" u2="&#x2014;" k="99" />
    <hkern u1="&#x393;" u2="&#x2013;" k="99" />
    <hkern u1="&#x393;" u2="&#x3ce;" k="120" />
    <hkern u1="&#x393;" u2="&#x3cd;" k="70" />
    <hkern u1="&#x393;" u2="&#x3cc;" k="130" />
    <hkern u1="&#x393;" u2="&#x3cb;" k="70" />
    <hkern u1="&#x393;" u2="&#x3ca;" k="90" />
    <hkern u1="&#x393;" u2="&#x3c9;" k="120" />
    <hkern u1="&#x393;" u2="&#x3c8;" k="117" />
    <hkern u1="&#x393;" u2="&#x3c7;" k="84" />
    <hkern u1="&#x393;" u2="&#x3c6;" k="128" />
    <hkern u1="&#x393;" u2="&#x3c5;" k="70" />
    <hkern u1="&#x393;" u2="&#x3c4;" k="100" />
    <hkern u1="&#x393;" u2="&#x3c3;" k="130" />
    <hkern u1="&#x393;" u2="&#x3c2;" k="85" />
    <hkern u1="&#x393;" u2="&#x3c1;" k="164" />
    <hkern u1="&#x393;" u2="&#x3c0;" k="114" />
    <hkern u1="&#x393;" u2="&#x3bf;" k="130" />
    <hkern u1="&#x393;" u2="&#x3bd;" k="40" />
    <hkern u1="&#x393;" u2="&#x3bc;" k="108" />
    <hkern u1="&#x393;" u2="&#x3ba;" k="100" />
    <hkern u1="&#x393;" u2="&#x3b9;" k="90" />
    <hkern u1="&#x393;" u2="&#x3b8;" k="55" />
    <hkern u1="&#x393;" u2="&#x3b7;" k="70" />
    <hkern u1="&#x393;" u2="&#x3b5;" k="146" />
    <hkern u1="&#x393;" u2="&#x3b4;" k="50" />
    <hkern u1="&#x393;" u2="&#x3b2;" k="46" />
    <hkern u1="&#x393;" u2="&#x3b1;" k="130" />
    <hkern u1="&#x393;" u2="&#x3b0;" k="70" />
    <hkern u1="&#x393;" u2="&#x3af;" k="90" />
    <hkern u1="&#x393;" u2="&#x3ae;" k="70" />
    <hkern u1="&#x393;" u2="&#x3ad;" k="146" />
    <hkern u1="&#x393;" u2="&#x3ac;" k="130" />
    <hkern u1="&#x393;" u2="&#x3a9;" k="65" />
    <hkern u1="&#x393;" u2="&#x3a6;" k="65" />
    <hkern u1="&#x393;" u2="&#x39f;" k="45" />
    <hkern u1="&#x393;" u2="&#x39b;" k="100" />
    <hkern u1="&#x393;" u2="&#x398;" k="45" />
    <hkern u1="&#x393;" u2="&#x394;" k="100" />
    <hkern u1="&#x393;" u2="&#x391;" k="100" />
    <hkern u1="&#x393;" u2="&#x390;" k="90" />
    <hkern u1="&#x393;" u2="&#xbb;" k="59" />
    <hkern u1="&#x393;" u2="&#xb5;" k="119" />
    <hkern u1="&#x393;" u2="&#x2e;" k="120" />
    <hkern u1="&#x393;" u2="&#x2d;" k="99" />
    <hkern u1="&#x393;" u2="&#x2c;" k="120" />
    <hkern u1="&#x394;" u2="&#x201d;" k="34" />
    <hkern u1="&#x394;" u2="&#x2019;" k="80" />
    <hkern u1="&#x394;" u2="&#x3cd;" k="15" />
    <hkern u1="&#x394;" u2="&#x3cb;" k="15" />
    <hkern u1="&#x394;" u2="&#x3c7;" k="30" />
    <hkern u1="&#x394;" u2="&#x3c6;" k="10" />
    <hkern u1="&#x394;" u2="&#x3c4;" k="50" />
    <hkern u1="&#x394;" u2="&#x3c0;" k="25" />
    <hkern u1="&#x394;" u2="&#x3bd;" k="35" />
    <hkern u1="&#x394;" u2="&#x3b3;" k="30" />
    <hkern u1="&#x394;" u2="&#x3b0;" k="15" />
    <hkern u1="&#x394;" u2="&#x3ab;" k="90" />
    <hkern u1="&#x394;" u2="&#x3a8;" k="65" />
    <hkern u1="&#x394;" u2="&#x3a6;" k="10" />
    <hkern u1="&#x394;" u2="&#x3a5;" k="90" />
    <hkern u1="&#x394;" u2="&#x3a4;" k="80" />
    <hkern u1="&#x394;" u2="&#x39f;" k="10" />
    <hkern u1="&#x394;" u2="&#x398;" k="10" />
    <hkern u1="&#x395;" u2="&#x3cd;" k="20" />
    <hkern u1="&#x395;" u2="&#x3cc;" k="15" />
    <hkern u1="&#x395;" u2="&#x3cb;" k="20" />
    <hkern u1="&#x395;" u2="&#x3ca;" k="10" />
    <hkern u1="&#x395;" u2="&#x3c8;" k="20" />
    <hkern u1="&#x395;" u2="&#x3c7;" k="30" />
    <hkern u1="&#x395;" u2="&#x3c6;" k="20" />
    <hkern u1="&#x395;" u2="&#x3c5;" k="20" />
    <hkern u1="&#x395;" u2="&#x3c4;" k="40" />
    <hkern u1="&#x395;" u2="&#x3c3;" k="15" />
    <hkern u1="&#x395;" u2="&#x3c0;" k="30" />
    <hkern u1="&#x395;" u2="&#x3bf;" k="15" />
    <hkern u1="&#x395;" u2="&#x3bd;" k="30" />
    <hkern u1="&#x395;" u2="&#x3b9;" k="10" />
    <hkern u1="&#x395;" u2="&#x3b8;" k="15" />
    <hkern u1="&#x395;" u2="&#x3b4;" k="20" />
    <hkern u1="&#x395;" u2="&#x3b3;" k="25" />
    <hkern u1="&#x395;" u2="&#x3b1;" k="20" />
    <hkern u1="&#x395;" u2="&#x3b0;" k="20" />
    <hkern u1="&#x395;" u2="&#x3af;" k="10" />
    <hkern u1="&#x395;" u2="&#x3ac;" k="20" />
    <hkern u1="&#x395;" u2="&#x3a6;" k="20" />
    <hkern u1="&#x395;" u2="&#x39f;" k="15" />
    <hkern u1="&#x395;" u2="&#x398;" k="15" />
    <hkern u1="&#x395;" u2="&#x390;" k="10" />
    <hkern u1="&#x396;" u2="&#x3ce;" k="25" />
    <hkern u1="&#x396;" u2="&#x3cd;" k="20" />
    <hkern u1="&#x396;" u2="&#x3cc;" k="25" />
    <hkern u1="&#x396;" u2="&#x3cb;" k="20" />
    <hkern u1="&#x396;" u2="&#x3ca;" k="20" />
    <hkern u1="&#x396;" u2="&#x3c9;" k="25" />
    <hkern u1="&#x396;" u2="&#x3c6;" k="20" />
    <hkern u1="&#x396;" u2="&#x3c5;" k="20" />
    <hkern u1="&#x396;" u2="&#x3c4;" k="30" />
    <hkern u1="&#x396;" u2="&#x3c3;" k="25" />
    <hkern u1="&#x396;" u2="&#x3bf;" k="25" />
    <hkern u1="&#x396;" u2="&#x3b9;" k="20" />
    <hkern u1="&#x396;" u2="&#x3b5;" k="20" />
    <hkern u1="&#x396;" u2="&#x3b2;" k="20" />
    <hkern u1="&#x396;" u2="&#x3b1;" k="20" />
    <hkern u1="&#x396;" u2="&#x3b0;" k="20" />
    <hkern u1="&#x396;" u2="&#x3af;" k="20" />
    <hkern u1="&#x396;" u2="&#x3ad;" k="20" />
    <hkern u1="&#x396;" u2="&#x3ac;" k="20" />
    <hkern u1="&#x396;" u2="&#x390;" k="20" />
    <hkern u1="&#x398;" u2="&#x3bb;" k="30" />
    <hkern u1="&#x398;" u2="&#x3ab;" k="30" />
    <hkern u1="&#x398;" u2="&#x3a7;" k="15" />
    <hkern u1="&#x398;" u2="&#x3a5;" k="30" />
    <hkern u1="&#x398;" u2="&#x3a4;" k="25" />
    <hkern u1="&#x398;" u2="&#x3a3;" k="35" />
    <hkern u1="&#x398;" u2="&#x39e;" k="25" />
    <hkern u1="&#x398;" u2="&#x39b;" k="10" />
    <hkern u1="&#x398;" u2="&#x394;" k="10" />
    <hkern u1="&#x398;" u2="&#x391;" k="10" />
    <hkern u1="&#x39a;" u2="&#x3cd;" k="15" />
    <hkern u1="&#x39a;" u2="&#x3cc;" k="15" />
    <hkern u1="&#x39a;" u2="&#x3cb;" k="15" />
    <hkern u1="&#x39a;" u2="&#x3c7;" k="25" />
    <hkern u1="&#x39a;" u2="&#x3c6;" k="15" />
    <hkern u1="&#x39a;" u2="&#x3c5;" k="15" />
    <hkern u1="&#x39a;" u2="&#x3c4;" k="65" />
    <hkern u1="&#x39a;" u2="&#x3c3;" k="15" />
    <hkern u1="&#x39a;" u2="&#x3c0;" k="15" />
    <hkern u1="&#x39a;" u2="&#x3bf;" k="15" />
    <hkern u1="&#x39a;" u2="&#x3bd;" k="30" />
    <hkern u1="&#x39a;" u2="&#x3b5;" k="15" />
    <hkern u1="&#x39a;" u2="&#x3b4;" k="15" />
    <hkern u1="&#x39a;" u2="&#x3b3;" k="30" />
    <hkern u1="&#x39a;" u2="&#x3b1;" k="15" />
    <hkern u1="&#x39a;" u2="&#x3b0;" k="15" />
    <hkern u1="&#x39a;" u2="&#x3ad;" k="15" />
    <hkern u1="&#x39a;" u2="&#x3ac;" k="15" />
    <hkern u1="&#x39a;" u2="&#x3a6;" k="55" />
    <hkern u1="&#x39a;" u2="&#x39f;" k="25" />
    <hkern u1="&#x39a;" u2="&#x398;" k="25" />
    <hkern u1="&#x39b;" u2="&#x201d;" k="34" />
    <hkern u1="&#x39b;" u2="&#x2019;" k="80" />
    <hkern u1="&#x39b;" u2="&#x3cd;" k="15" />
    <hkern u1="&#x39b;" u2="&#x3cb;" k="15" />
    <hkern u1="&#x39b;" u2="&#x3c7;" k="30" />
    <hkern u1="&#x39b;" u2="&#x3c6;" k="10" />
    <hkern u1="&#x39b;" u2="&#x3c4;" k="50" />
    <hkern u1="&#x39b;" u2="&#x3c0;" k="25" />
    <hkern u1="&#x39b;" u2="&#x3bd;" k="35" />
    <hkern u1="&#x39b;" u2="&#x3b3;" k="30" />
    <hkern u1="&#x39b;" u2="&#x3b0;" k="15" />
    <hkern u1="&#x39b;" u2="&#x3ab;" k="90" />
    <hkern u1="&#x39b;" u2="&#x3a8;" k="65" />
    <hkern u1="&#x39b;" u2="&#x3a6;" k="10" />
    <hkern u1="&#x39b;" u2="&#x3a5;" k="90" />
    <hkern u1="&#x39b;" u2="&#x3a4;" k="80" />
    <hkern u1="&#x39b;" u2="&#x39f;" k="10" />
    <hkern u1="&#x39b;" u2="&#x398;" k="10" />
    <hkern u1="&#x39e;" u2="&#x3ce;" k="20" />
    <hkern u1="&#x39e;" u2="&#x3cd;" k="20" />
    <hkern u1="&#x39e;" u2="&#x3cc;" k="25" />
    <hkern u1="&#x39e;" u2="&#x3cb;" k="20" />
    <hkern u1="&#x39e;" u2="&#x3ca;" k="20" />
    <hkern u1="&#x39e;" u2="&#x3c9;" k="20" />
    <hkern u1="&#x39e;" u2="&#x3c5;" k="20" />
    <hkern u1="&#x39e;" u2="&#x3c3;" k="25" />
    <hkern u1="&#x39e;" u2="&#x3bf;" k="25" />
    <hkern u1="&#x39e;" u2="&#x3b9;" k="20" />
    <hkern u1="&#x39e;" u2="&#x3b5;" k="25" />
    <hkern u1="&#x39e;" u2="&#x3b1;" k="20" />
    <hkern u1="&#x39e;" u2="&#x3b0;" k="20" />
    <hkern u1="&#x39e;" u2="&#x3af;" k="20" />
    <hkern u1="&#x39e;" u2="&#x3ad;" k="25" />
    <hkern u1="&#x39e;" u2="&#x3ac;" k="20" />
    <hkern u1="&#x39e;" u2="&#x3a6;" k="20" />
    <hkern u1="&#x39e;" u2="&#x39f;" k="20" />
    <hkern u1="&#x39e;" u2="&#x398;" k="20" />
    <hkern u1="&#x39e;" u2="&#x390;" k="20" />
    <hkern u1="&#x39f;" u2="&#x3bb;" k="30" />
    <hkern u1="&#x39f;" u2="&#x3ab;" k="30" />
    <hkern u1="&#x39f;" u2="&#x3a7;" k="15" />
    <hkern u1="&#x39f;" u2="&#x3a5;" k="30" />
    <hkern u1="&#x39f;" u2="&#x3a4;" k="25" />
    <hkern u1="&#x39f;" u2="&#x3a3;" k="35" />
    <hkern u1="&#x39f;" u2="&#x39e;" k="25" />
    <hkern u1="&#x39f;" u2="&#x39b;" k="10" />
    <hkern u1="&#x39f;" u2="&#x394;" k="10" />
    <hkern u1="&#x39f;" u2="&#x391;" k="10" />
    <hkern u1="&#x3a1;" u2="&#x2026;" k="107" />
    <hkern u1="&#x3a1;" u2="&#x3ce;" k="20" />
    <hkern u1="&#x3a1;" u2="&#x3cc;" k="25" />
    <hkern u1="&#x3a1;" u2="&#x3ca;" k="15" />
    <hkern u1="&#x3a1;" u2="&#x3c9;" k="20" />
    <hkern u1="&#x3a1;" u2="&#x3c6;" k="25" />
    <hkern u1="&#x3a1;" u2="&#x3c3;" k="30" />
    <hkern u1="&#x3a1;" u2="&#x3c1;" k="30" />
    <hkern u1="&#x3a1;" u2="&#x3bf;" k="25" />
    <hkern u1="&#x3a1;" u2="&#x3bb;" k="30" />
    <hkern u1="&#x3a1;" u2="&#x3b9;" k="15" />
    <hkern u1="&#x3a1;" u2="&#x3b6;" k="20" />
    <hkern u1="&#x3a1;" u2="&#x3b5;" k="25" />
    <hkern u1="&#x3a1;" u2="&#x3b4;" k="20" />
    <hkern u1="&#x3a1;" u2="&#x3b1;" k="20" />
    <hkern u1="&#x3a1;" u2="&#x3af;" k="15" />
    <hkern u1="&#x3a1;" u2="&#x3ad;" k="25" />
    <hkern u1="&#x3a1;" u2="&#x3ac;" k="20" />
    <hkern u1="&#x3a1;" u2="&#x39b;" k="50" />
    <hkern u1="&#x3a1;" u2="&#x396;" k="30" />
    <hkern u1="&#x3a1;" u2="&#x394;" k="50" />
    <hkern u1="&#x3a1;" u2="&#x391;" k="50" />
    <hkern u1="&#x3a1;" u2="&#x390;" k="15" />
    <hkern u1="&#x3a1;" u2="&#x2e;" k="107" />
    <hkern u1="&#x3a1;" u2="&#x2c;" k="107" />
    <hkern u1="&#x3a3;" u2="&#x3c4;" k="44" />
    <hkern u1="&#x3a3;" u2="&#x3c0;" k="20" />
    <hkern u1="&#x3a3;" u2="&#x3bd;" k="25" />
    <hkern u1="&#x3a3;" u2="&#x3b4;" k="10" />
    <hkern u1="&#x3a3;" u2="&#x3b3;" k="20" />
    <hkern u1="&#x3a3;" u2="&#x3a6;" k="40" />
    <hkern u1="&#x3a3;" u2="&#x39f;" k="35" />
    <hkern u1="&#x3a3;" u2="&#x398;" k="35" />
    <hkern u1="&#x3a4;" u2="&#x203a;" k="34" />
    <hkern u1="&#x3a4;" u2="&#x2026;" k="65" />
    <hkern u1="&#x3a4;" u2="&#x2014;" k="70" />
    <hkern u1="&#x3a4;" u2="&#x2013;" k="70" />
    <hkern u1="&#x3a4;" u2="&#x3ce;" k="81" />
    <hkern u1="&#x3a4;" u2="&#x3cd;" k="43" />
    <hkern u1="&#x3a4;" u2="&#x3cc;" k="77" />
    <hkern u1="&#x3a4;" u2="&#x3cb;" k="43" />
    <hkern u1="&#x3a4;" u2="&#x3ca;" k="68" />
    <hkern u1="&#x3a4;" u2="&#x3c9;" k="81" />
    <hkern u1="&#x3a4;" u2="&#x3c8;" k="73" />
    <hkern u1="&#x3a4;" u2="&#x3c7;" k="67" />
    <hkern u1="&#x3a4;" u2="&#x3c6;" k="93" />
    <hkern u1="&#x3a4;" u2="&#x3c5;" k="43" />
    <hkern u1="&#x3a4;" u2="&#x3c3;" k="77" />
    <hkern u1="&#x3a4;" u2="&#x3c1;" k="103" />
    <hkern u1="&#x3a4;" u2="&#x3c0;" k="73" />
    <hkern u1="&#x3a4;" u2="&#x3bf;" k="77" />
    <hkern u1="&#x3a4;" u2="&#x3bc;" k="60" />
    <hkern u1="&#x3a4;" u2="&#x3ba;" k="78" />
    <hkern u1="&#x3a4;" u2="&#x3b9;" k="68" />
    <hkern u1="&#x3a4;" u2="&#x3b7;" k="51" />
    <hkern u1="&#x3a4;" u2="&#x3b6;" k="22" />
    <hkern u1="&#x3a4;" u2="&#x3b5;" k="94" />
    <hkern u1="&#x3a4;" u2="&#x3b4;" k="44" />
    <hkern u1="&#x3a4;" u2="&#x3b1;" k="85" />
    <hkern u1="&#x3a4;" u2="&#x3b0;" k="43" />
    <hkern u1="&#x3a4;" u2="&#x3af;" k="68" />
    <hkern u1="&#x3a4;" u2="&#x3ae;" k="51" />
    <hkern u1="&#x3a4;" u2="&#x3ad;" k="94" />
    <hkern u1="&#x3a4;" u2="&#x3ac;" k="85" />
    <hkern u1="&#x3a4;" u2="&#x3a9;" k="30" />
    <hkern u1="&#x3a4;" u2="&#x3a6;" k="65" />
    <hkern u1="&#x3a4;" u2="&#x39f;" k="30" />
    <hkern u1="&#x3a4;" u2="&#x39b;" k="80" />
    <hkern u1="&#x3a4;" u2="&#x398;" k="30" />
    <hkern u1="&#x3a4;" u2="&#x394;" k="80" />
    <hkern u1="&#x3a4;" u2="&#x391;" k="80" />
    <hkern u1="&#x3a4;" u2="&#x390;" k="68" />
    <hkern u1="&#x3a4;" u2="&#xbb;" k="34" />
    <hkern u1="&#x3a4;" u2="&#xb5;" k="64" />
    <hkern u1="&#x3a4;" u2="&#x2e;" k="65" />
    <hkern u1="&#x3a4;" u2="&#x2d;" k="70" />
    <hkern u1="&#x3a4;" u2="&#x2c;" k="65" />
    <hkern u1="&#x3a5;" u2="&#x203a;" k="45" />
    <hkern u1="&#x3a5;" u2="&#x2026;" k="100" />
    <hkern u1="&#x3a5;" u2="&#x2014;" k="56" />
    <hkern u1="&#x3a5;" u2="&#x2013;" k="56" />
    <hkern u1="&#x3a5;" u2="&#x3ce;" k="70" />
    <hkern u1="&#x3a5;" u2="&#x3cd;" k="20" />
    <hkern u1="&#x3a5;" u2="&#x3cc;" k="80" />
    <hkern u1="&#x3a5;" u2="&#x3cb;" k="20" />
    <hkern u1="&#x3a5;" u2="&#x3ca;" k="40" />
    <hkern u1="&#x3a5;" u2="&#x3c9;" k="70" />
    <hkern u1="&#x3a5;" u2="&#x3c8;" k="30" />
    <hkern u1="&#x3a5;" u2="&#x3c6;" k="90" />
    <hkern u1="&#x3a5;" u2="&#x3c5;" k="20" />
    <hkern u1="&#x3a5;" u2="&#x3c4;" k="30" />
    <hkern u1="&#x3a5;" u2="&#x3c3;" k="80" />
    <hkern u1="&#x3a5;" u2="&#x3c1;" k="100" />
    <hkern u1="&#x3a5;" u2="&#x3c0;" k="30" />
    <hkern u1="&#x3a5;" u2="&#x3bf;" k="80" />
    <hkern u1="&#x3a5;" u2="&#x3bc;" k="60" />
    <hkern u1="&#x3a5;" u2="&#x3ba;" k="40" />
    <hkern u1="&#x3a5;" u2="&#x3b9;" k="40" />
    <hkern u1="&#x3a5;" u2="&#x3b5;" k="80" />
    <hkern u1="&#x3a5;" u2="&#x3b4;" k="40" />
    <hkern u1="&#x3a5;" u2="&#x3b2;" k="20" />
    <hkern u1="&#x3a5;" u2="&#x3b1;" k="75" />
    <hkern u1="&#x3a5;" u2="&#x3b0;" k="20" />
    <hkern u1="&#x3a5;" u2="&#x3af;" k="40" />
    <hkern u1="&#x3a5;" u2="&#x3ad;" k="80" />
    <hkern u1="&#x3a5;" u2="&#x3ac;" k="75" />
    <hkern u1="&#x3a5;" u2="&#x3a9;" k="40" />
    <hkern u1="&#x3a5;" u2="&#x3a6;" k="70" />
    <hkern u1="&#x3a5;" u2="&#x39f;" k="40" />
    <hkern u1="&#x3a5;" u2="&#x39b;" k="85" />
    <hkern u1="&#x3a5;" u2="&#x398;" k="40" />
    <hkern u1="&#x3a5;" u2="&#x394;" k="85" />
    <hkern u1="&#x3a5;" u2="&#x391;" k="85" />
    <hkern u1="&#x3a5;" u2="&#x390;" k="40" />
    <hkern u1="&#x3a5;" u2="&#xbb;" k="45" />
    <hkern u1="&#x3a5;" u2="&#xb5;" k="50" />
    <hkern u1="&#x3a5;" u2="&#x2e;" k="100" />
    <hkern u1="&#x3a5;" u2="&#x2d;" k="56" />
    <hkern u1="&#x3a5;" u2="&#x2c;" k="100" />
    <hkern u1="&#x3a6;" u2="&#x2026;" k="40" />
    <hkern u1="&#x3a6;" u2="&#x3c1;" k="15" />
    <hkern u1="&#x3a6;" u2="&#x3bb;" k="35" />
    <hkern u1="&#x3a6;" u2="&#x3b5;" k="10" />
    <hkern u1="&#x3a6;" u2="&#x3ad;" k="10" />
    <hkern u1="&#x3a6;" u2="&#x3ab;" k="50" />
    <hkern u1="&#x3a6;" u2="&#x3a7;" k="20" />
    <hkern u1="&#x3a6;" u2="&#x3a5;" k="50" />
    <hkern u1="&#x3a6;" u2="&#x3a4;" k="55" />
    <hkern u1="&#x3a6;" u2="&#x3a3;" k="50" />
    <hkern u1="&#x3a6;" u2="&#x39e;" k="20" />
    <hkern u1="&#x3a6;" u2="&#x39b;" k="10" />
    <hkern u1="&#x3a6;" u2="&#x394;" k="10" />
    <hkern u1="&#x3a6;" u2="&#x391;" k="10" />
    <hkern u1="&#x3a6;" u2="&#x2e;" k="40" />
    <hkern u1="&#x3a6;" u2="&#x2c;" k="40" />
    <hkern u1="&#x3a7;" u2="&#x3ce;" k="15" />
    <hkern u1="&#x3a7;" u2="&#x3cd;" k="15" />
    <hkern u1="&#x3a7;" u2="&#x3cc;" k="20" />
    <hkern u1="&#x3a7;" u2="&#x3cb;" k="15" />
    <hkern u1="&#x3a7;" u2="&#x3c9;" k="15" />
    <hkern u1="&#x3a7;" u2="&#x3c7;" k="20" />
    <hkern u1="&#x3a7;" u2="&#x3c6;" k="15" />
    <hkern u1="&#x3a7;" u2="&#x3c5;" k="15" />
    <hkern u1="&#x3a7;" u2="&#x3c4;" k="40" />
    <hkern u1="&#x3a7;" u2="&#x3c3;" k="20" />
    <hkern u1="&#x3a7;" u2="&#x3c0;" k="35" />
    <hkern u1="&#x3a7;" u2="&#x3bf;" k="20" />
    <hkern u1="&#x3a7;" u2="&#x3bd;" k="30" />
    <hkern u1="&#x3a7;" u2="&#x3b0;" k="15" />
    <hkern u1="&#x3a7;" u2="&#x3a6;" k="20" />
    <hkern u1="&#x3a7;" u2="&#x39f;" k="15" />
    <hkern u1="&#x3a7;" u2="&#x398;" k="15" />
    <hkern u1="&#x3a8;" u2="&#x2026;" k="77" />
    <hkern u1="&#x3a8;" u2="&#x3ce;" k="40" />
    <hkern u1="&#x3a8;" u2="&#x3cc;" k="40" />
    <hkern u1="&#x3a8;" u2="&#x3ca;" k="20" />
    <hkern u1="&#x3a8;" u2="&#x3c9;" k="40" />
    <hkern u1="&#x3a8;" u2="&#x3c6;" k="50" />
    <hkern u1="&#x3a8;" u2="&#x3c3;" k="40" />
    <hkern u1="&#x3a8;" u2="&#x3c1;" k="55" />
    <hkern u1="&#x3a8;" u2="&#x3bf;" k="40" />
    <hkern u1="&#x3a8;" u2="&#x3b9;" k="20" />
    <hkern u1="&#x3a8;" u2="&#x3b6;" k="20" />
    <hkern u1="&#x3a8;" u2="&#x3b5;" k="45" />
    <hkern u1="&#x3a8;" u2="&#x3b4;" k="35" />
    <hkern u1="&#x3a8;" u2="&#x3b2;" k="30" />
    <hkern u1="&#x3a8;" u2="&#x3b1;" k="40" />
    <hkern u1="&#x3a8;" u2="&#x3af;" k="20" />
    <hkern u1="&#x3a8;" u2="&#x3ad;" k="45" />
    <hkern u1="&#x3a8;" u2="&#x3ac;" k="40" />
    <hkern u1="&#x3a8;" u2="&#x39b;" k="60" />
    <hkern u1="&#x3a8;" u2="&#x394;" k="60" />
    <hkern u1="&#x3a8;" u2="&#x391;" k="60" />
    <hkern u1="&#x3a8;" u2="&#x390;" k="20" />
    <hkern u1="&#x3a8;" u2="&#x2e;" k="77" />
    <hkern u1="&#x3a8;" u2="&#x2c;" k="77" />
    <hkern u1="&#x3a9;" u2="&#x3ab;" k="40" />
    <hkern u1="&#x3a9;" u2="&#x3a5;" k="40" />
    <hkern u1="&#x3a9;" u2="&#x3a4;" k="30" />
    <hkern u1="&#x3ab;" u2="&#x203a;" k="45" />
    <hkern u1="&#x3ab;" u2="&#x2026;" k="100" />
    <hkern u1="&#x3ab;" u2="&#x2014;" k="56" />
    <hkern u1="&#x3ab;" u2="&#x2013;" k="56" />
    <hkern u1="&#x3ab;" u2="&#x3ce;" k="70" />
    <hkern u1="&#x3ab;" u2="&#x3cd;" k="20" />
    <hkern u1="&#x3ab;" u2="&#x3cc;" k="80" />
    <hkern u1="&#x3ab;" u2="&#x3cb;" k="20" />
    <hkern u1="&#x3ab;" u2="&#x3ca;" k="40" />
    <hkern u1="&#x3ab;" u2="&#x3c9;" k="70" />
    <hkern u1="&#x3ab;" u2="&#x3c8;" k="30" />
    <hkern u1="&#x3ab;" u2="&#x3c6;" k="90" />
    <hkern u1="&#x3ab;" u2="&#x3c5;" k="20" />
    <hkern u1="&#x3ab;" u2="&#x3c4;" k="30" />
    <hkern u1="&#x3ab;" u2="&#x3c3;" k="80" />
    <hkern u1="&#x3ab;" u2="&#x3c1;" k="100" />
    <hkern u1="&#x3ab;" u2="&#x3c0;" k="30" />
    <hkern u1="&#x3ab;" u2="&#x3bf;" k="80" />
    <hkern u1="&#x3ab;" u2="&#x3bc;" k="60" />
    <hkern u1="&#x3ab;" u2="&#x3ba;" k="40" />
    <hkern u1="&#x3ab;" u2="&#x3b9;" k="40" />
    <hkern u1="&#x3ab;" u2="&#x3b5;" k="80" />
    <hkern u1="&#x3ab;" u2="&#x3b4;" k="40" />
    <hkern u1="&#x3ab;" u2="&#x3b2;" k="20" />
    <hkern u1="&#x3ab;" u2="&#x3b1;" k="75" />
    <hkern u1="&#x3ab;" u2="&#x3b0;" k="20" />
    <hkern u1="&#x3ab;" u2="&#x3af;" k="40" />
    <hkern u1="&#x3ab;" u2="&#x3ad;" k="80" />
    <hkern u1="&#x3ab;" u2="&#x3ac;" k="75" />
    <hkern u1="&#x3ab;" u2="&#x3a9;" k="40" />
    <hkern u1="&#x3ab;" u2="&#x3a6;" k="70" />
    <hkern u1="&#x3ab;" u2="&#x39f;" k="40" />
    <hkern u1="&#x3ab;" u2="&#x39b;" k="85" />
    <hkern u1="&#x3ab;" u2="&#x398;" k="40" />
    <hkern u1="&#x3ab;" u2="&#x394;" k="85" />
    <hkern u1="&#x3ab;" u2="&#x391;" k="85" />
    <hkern u1="&#x3ab;" u2="&#x390;" k="40" />
    <hkern u1="&#x3ab;" u2="&#xbb;" k="45" />
    <hkern u1="&#x3ab;" u2="&#xb5;" k="50" />
    <hkern u1="&#x3ab;" u2="&#x2e;" k="100" />
    <hkern u1="&#x3ab;" u2="&#x2d;" k="56" />
    <hkern u1="&#x3ab;" u2="&#x2c;" k="100" />
    <hkern u1="&#x3ac;" u2="&#x3ce;" k="10" />
    <hkern u1="&#x3ac;" u2="&#x3cc;" k="15" />
    <hkern u1="&#x3ac;" u2="&#x3c9;" k="10" />
    <hkern u1="&#x3ac;" u2="&#x3c8;" k="10" />
    <hkern u1="&#x3ac;" u2="&#x3c6;" k="25" />
    <hkern u1="&#x3ac;" u2="&#x3c4;" k="7" />
    <hkern u1="&#x3ac;" u2="&#x3c3;" k="15" />
    <hkern u1="&#x3ac;" u2="&#x3c2;" k="15" />
    <hkern u1="&#x3ac;" u2="&#x3c0;" k="10" />
    <hkern u1="&#x3ac;" u2="&#x3bf;" k="15" />
    <hkern u1="&#x3ac;" u2="&#x3b6;" k="10" />
    <hkern u1="&#x3ac;" u2="&#x3b4;" k="15" />
    <hkern u1="&#x3ad;" u2="&#x3ce;" k="20" />
    <hkern u1="&#x3ad;" u2="&#x3cc;" k="35" />
    <hkern u1="&#x3ad;" u2="&#x3ca;" k="20" />
    <hkern u1="&#x3ad;" u2="&#x3c9;" k="20" />
    <hkern u1="&#x3ad;" u2="&#x3c8;" k="20" />
    <hkern u1="&#x3ad;" u2="&#x3c6;" k="35" />
    <hkern u1="&#x3ad;" u2="&#x3c3;" k="35" />
    <hkern u1="&#x3ad;" u2="&#x3c2;" k="25" />
    <hkern u1="&#x3ad;" u2="&#x3c1;" k="25" />
    <hkern u1="&#x3ad;" u2="&#x3bf;" k="35" />
    <hkern u1="&#x3ad;" u2="&#x3be;" k="20" />
    <hkern u1="&#x3ad;" u2="&#x3ba;" k="20" />
    <hkern u1="&#x3ad;" u2="&#x3b9;" k="20" />
    <hkern u1="&#x3ad;" u2="&#x3b8;" k="15" />
    <hkern u1="&#x3ad;" u2="&#x3b6;" k="25" />
    <hkern u1="&#x3ad;" u2="&#x3b5;" k="25" />
    <hkern u1="&#x3ad;" u2="&#x3b4;" k="25" />
    <hkern u1="&#x3ad;" u2="&#x3b2;" k="20" />
    <hkern u1="&#x3ad;" u2="&#x3b1;" k="20" />
    <hkern u1="&#x3ad;" u2="&#x3af;" k="20" />
    <hkern u1="&#x3ad;" u2="&#x3ad;" k="25" />
    <hkern u1="&#x3ad;" u2="&#x3ac;" k="20" />
    <hkern u1="&#x3ad;" u2="&#x390;" k="20" />
    <hkern u1="&#x3ae;" u2="&#x3c4;" k="10" />
    <hkern u1="&#x3af;" u2="&#x3cc;" k="15" />
    <hkern u1="&#x3af;" u2="&#x3c6;" k="20" />
    <hkern u1="&#x3af;" u2="&#x3c4;" k="20" />
    <hkern u1="&#x3af;" u2="&#x3c3;" k="15" />
    <hkern u1="&#x3af;" u2="&#x3c0;" k="20" />
    <hkern u1="&#x3af;" u2="&#x3bf;" k="15" />
    <hkern u1="&#x3af;" u2="&#x3b8;" k="10" />
    <hkern u1="&#x3af;" u2="&#x3b4;" k="10" />
    <hkern u1="&#x3af;" u2="&#x3b1;" k="7" />
    <hkern u1="&#x3af;" u2="&#x3ac;" k="7" />
    <hkern u1="&#x3b0;" u2="&#x3ce;" k="10" />
    <hkern u1="&#x3b0;" u2="&#x3c9;" k="10" />
    <hkern u1="&#x3b0;" u2="&#x3c1;" k="25" />
    <hkern u1="&#x3b0;" u2="&#x3bb;" k="25" />
    <hkern u1="&#x3b0;" u2="&#x3b5;" k="13" />
    <hkern u1="&#x3b0;" u2="&#x3b1;" k="10" />
    <hkern u1="&#x3b0;" u2="&#x3ad;" k="13" />
    <hkern u1="&#x3b0;" u2="&#x3ac;" k="10" />
    <hkern u1="&#x3b1;" u2="&#x3ce;" k="10" />
    <hkern u1="&#x3b1;" u2="&#x3cc;" k="15" />
    <hkern u1="&#x3b1;" u2="&#x3c9;" k="10" />
    <hkern u1="&#x3b1;" u2="&#x3c8;" k="10" />
    <hkern u1="&#x3b1;" u2="&#x3c6;" k="25" />
    <hkern u1="&#x3b1;" u2="&#x3c4;" k="7" />
    <hkern u1="&#x3b1;" u2="&#x3c3;" k="15" />
    <hkern u1="&#x3b1;" u2="&#x3c2;" k="15" />
    <hkern u1="&#x3b1;" u2="&#x3c0;" k="10" />
    <hkern u1="&#x3b1;" u2="&#x3bf;" k="15" />
    <hkern u1="&#x3b1;" u2="&#x3b6;" k="10" />
    <hkern u1="&#x3b1;" u2="&#x3b4;" k="15" />
    <hkern u1="&#x3b2;" u2="&#x3ca;" k="10" />
    <hkern u1="&#x3b2;" u2="&#x3c7;" k="25" />
    <hkern u1="&#x3b2;" u2="&#x3c4;" k="20" />
    <hkern u1="&#x3b2;" u2="&#x3c0;" k="30" />
    <hkern u1="&#x3b2;" u2="&#x3bd;" k="10" />
    <hkern u1="&#x3b2;" u2="&#x3bb;" k="7" />
    <hkern u1="&#x3b2;" u2="&#x3b9;" k="10" />
    <hkern u1="&#x3b2;" u2="&#x3b3;" k="7" />
    <hkern u1="&#x3b2;" u2="&#x3af;" k="10" />
    <hkern u1="&#x3b2;" u2="&#x390;" k="10" />
    <hkern u1="&#x3b3;" u2="&#x2026;" k="60" />
    <hkern u1="&#x3b3;" u2="&#x2014;" k="30" />
    <hkern u1="&#x3b3;" u2="&#x2013;" k="30" />
    <hkern u1="&#x3b3;" u2="&#x3ce;" k="40" />
    <hkern u1="&#x3b3;" u2="&#x3cc;" k="35" />
    <hkern u1="&#x3b3;" u2="&#x3ca;" k="25" />
    <hkern u1="&#x3b3;" u2="&#x3c9;" k="40" />
    <hkern u1="&#x3b3;" u2="&#x3c6;" k="35" />
    <hkern u1="&#x3b3;" u2="&#x3c3;" k="35" />
    <hkern u1="&#x3b3;" u2="&#x3c2;" k="10" />
    <hkern u1="&#x3b3;" u2="&#x3c1;" k="50" />
    <hkern u1="&#x3b3;" u2="&#x3bf;" k="35" />
    <hkern u1="&#x3b3;" u2="&#x3be;" k="25" />
    <hkern u1="&#x3b3;" u2="&#x3bb;" k="60" />
    <hkern u1="&#x3b3;" u2="&#x3ba;" k="25" />
    <hkern u1="&#x3b3;" u2="&#x3b9;" k="25" />
    <hkern u1="&#x3b3;" u2="&#x3b6;" k="20" />
    <hkern u1="&#x3b3;" u2="&#x3b5;" k="40" />
    <hkern u1="&#x3b3;" u2="&#x3b4;" k="45" />
    <hkern u1="&#x3b3;" u2="&#x3b2;" k="15" />
    <hkern u1="&#x3b3;" u2="&#x3b1;" k="40" />
    <hkern u1="&#x3b3;" u2="&#x3af;" k="25" />
    <hkern u1="&#x3b3;" u2="&#x3ad;" k="40" />
    <hkern u1="&#x3b3;" u2="&#x3ac;" k="40" />
    <hkern u1="&#x3b3;" u2="&#x390;" k="25" />
    <hkern u1="&#x3b3;" u2="&#xb5;" k="15" />
    <hkern u1="&#x3b3;" u2="&#x2e;" k="60" />
    <hkern u1="&#x3b3;" u2="&#x2d;" k="30" />
    <hkern u1="&#x3b3;" u2="&#x2c;" k="60" />
    <hkern u1="&#x3b4;" u2="&#x3c7;" k="30" />
    <hkern u1="&#x3b4;" u2="&#x3c0;" k="20" />
    <hkern u1="&#x3b4;" u2="&#x3b7;" k="5" />
    <hkern u1="&#x3b4;" u2="&#x3ae;" k="5" />
    <hkern u1="&#x3b5;" u2="&#x3ce;" k="20" />
    <hkern u1="&#x3b5;" u2="&#x3cc;" k="35" />
    <hkern u1="&#x3b5;" u2="&#x3ca;" k="20" />
    <hkern u1="&#x3b5;" u2="&#x3c9;" k="20" />
    <hkern u1="&#x3b5;" u2="&#x3c8;" k="20" />
    <hkern u1="&#x3b5;" u2="&#x3c6;" k="35" />
    <hkern u1="&#x3b5;" u2="&#x3c3;" k="35" />
    <hkern u1="&#x3b5;" u2="&#x3c2;" k="25" />
    <hkern u1="&#x3b5;" u2="&#x3c1;" k="25" />
    <hkern u1="&#x3b5;" u2="&#x3bf;" k="35" />
    <hkern u1="&#x3b5;" u2="&#x3be;" k="20" />
    <hkern u1="&#x3b5;" u2="&#x3ba;" k="20" />
    <hkern u1="&#x3b5;" u2="&#x3b9;" k="20" />
    <hkern u1="&#x3b5;" u2="&#x3b8;" k="15" />
    <hkern u1="&#x3b5;" u2="&#x3b6;" k="25" />
    <hkern u1="&#x3b5;" u2="&#x3b5;" k="25" />
    <hkern u1="&#x3b5;" u2="&#x3b4;" k="25" />
    <hkern u1="&#x3b5;" u2="&#x3b2;" k="20" />
    <hkern u1="&#x3b5;" u2="&#x3b1;" k="20" />
    <hkern u1="&#x3b5;" u2="&#x3af;" k="20" />
    <hkern u1="&#x3b5;" u2="&#x3ad;" k="25" />
    <hkern u1="&#x3b5;" u2="&#x3ac;" k="20" />
    <hkern u1="&#x3b5;" u2="&#x390;" k="20" />
    <hkern u1="&#x3b6;" u2="&#x2014;" k="48" />
    <hkern u1="&#x3b6;" u2="&#x2013;" k="48" />
    <hkern u1="&#x3b6;" u2="&#x3ce;" k="15" />
    <hkern u1="&#x3b6;" u2="&#x3cd;" k="10" />
    <hkern u1="&#x3b6;" u2="&#x3cc;" k="25" />
    <hkern u1="&#x3b6;" u2="&#x3cb;" k="10" />
    <hkern u1="&#x3b6;" u2="&#x3c9;" k="15" />
    <hkern u1="&#x3b6;" u2="&#x3c8;" k="25" />
    <hkern u1="&#x3b6;" u2="&#x3c6;" k="35" />
    <hkern u1="&#x3b6;" u2="&#x3c5;" k="10" />
    <hkern u1="&#x3b6;" u2="&#x3c4;" k="50" />
    <hkern u1="&#x3b6;" u2="&#x3c3;" k="25" />
    <hkern u1="&#x3b6;" u2="&#x3c2;" k="25" />
    <hkern u1="&#x3b6;" u2="&#x3c0;" k="25" />
    <hkern u1="&#x3b6;" u2="&#x3bf;" k="25" />
    <hkern u1="&#x3b6;" u2="&#x3b4;" k="20" />
    <hkern u1="&#x3b6;" u2="&#x3b1;" k="20" />
    <hkern u1="&#x3b6;" u2="&#x3b0;" k="10" />
    <hkern u1="&#x3b6;" u2="&#x3ac;" k="20" />
    <hkern u1="&#x3b6;" u2="&#x2d;" k="48" />
    <hkern u1="&#x3b7;" u2="&#x3c4;" k="10" />
    <hkern u1="&#x3b8;" u2="&#x3bb;" k="20" />
    <hkern u1="&#x3b9;" u2="&#x3cc;" k="15" />
    <hkern u1="&#x3b9;" u2="&#x3c6;" k="20" />
    <hkern u1="&#x3b9;" u2="&#x3c4;" k="20" />
    <hkern u1="&#x3b9;" u2="&#x3c3;" k="15" />
    <hkern u1="&#x3b9;" u2="&#x3c0;" k="20" />
    <hkern u1="&#x3b9;" u2="&#x3bf;" k="15" />
    <hkern u1="&#x3b9;" u2="&#x3b8;" k="10" />
    <hkern u1="&#x3b9;" u2="&#x3b4;" k="10" />
    <hkern u1="&#x3b9;" u2="&#x3b1;" k="7" />
    <hkern u1="&#x3b9;" u2="&#x3ac;" k="7" />
    <hkern u1="&#x3ba;" u2="&#x2014;" k="43" />
    <hkern u1="&#x3ba;" u2="&#x2013;" k="43" />
    <hkern u1="&#x3ba;" u2="&#x3ce;" k="35" />
    <hkern u1="&#x3ba;" u2="&#x3cc;" k="45" />
    <hkern u1="&#x3ba;" u2="&#x3ca;" k="20" />
    <hkern u1="&#x3ba;" u2="&#x3c9;" k="35" />
    <hkern u1="&#x3ba;" u2="&#x3c8;" k="20" />
    <hkern u1="&#x3ba;" u2="&#x3c6;" k="50" />
    <hkern u1="&#x3ba;" u2="&#x3c4;" k="15" />
    <hkern u1="&#x3ba;" u2="&#x3c3;" k="45" />
    <hkern u1="&#x3ba;" u2="&#x3c2;" k="45" />
    <hkern u1="&#x3ba;" u2="&#x3c1;" k="35" />
    <hkern u1="&#x3ba;" u2="&#x3c0;" k="15" />
    <hkern u1="&#x3ba;" u2="&#x3bf;" k="45" />
    <hkern u1="&#x3ba;" u2="&#x3be;" k="30" />
    <hkern u1="&#x3ba;" u2="&#x3ba;" k="25" />
    <hkern u1="&#x3ba;" u2="&#x3b9;" k="20" />
    <hkern u1="&#x3ba;" u2="&#x3b8;" k="25" />
    <hkern u1="&#x3ba;" u2="&#x3b6;" k="30" />
    <hkern u1="&#x3ba;" u2="&#x3b5;" k="25" />
    <hkern u1="&#x3ba;" u2="&#x3b4;" k="40" />
    <hkern u1="&#x3ba;" u2="&#x3b2;" k="25" />
    <hkern u1="&#x3ba;" u2="&#x3b1;" k="45" />
    <hkern u1="&#x3ba;" u2="&#x3af;" k="20" />
    <hkern u1="&#x3ba;" u2="&#x3ad;" k="25" />
    <hkern u1="&#x3ba;" u2="&#x3ac;" k="45" />
    <hkern u1="&#x3ba;" u2="&#x390;" k="20" />
    <hkern u1="&#x3ba;" u2="&#xb5;" k="15" />
    <hkern u1="&#x3ba;" u2="&#x2d;" k="43" />
    <hkern u1="&#x3bb;" u2="&#x201d;" k="40" />
    <hkern u1="&#x3bb;" u2="&#x2019;" k="40" />
    <hkern u1="&#x3bb;" u2="&#x2014;" k="38" />
    <hkern u1="&#x3bb;" u2="&#x2013;" k="38" />
    <hkern u1="&#x3bb;" u2="&#x3cd;" k="20" />
    <hkern u1="&#x3bb;" u2="&#x3cc;" k="15" />
    <hkern u1="&#x3bb;" u2="&#x3cb;" k="20" />
    <hkern u1="&#x3bb;" u2="&#x3c7;" k="20" />
    <hkern u1="&#x3bb;" u2="&#x3c6;" k="20" />
    <hkern u1="&#x3bb;" u2="&#x3c5;" k="20" />
    <hkern u1="&#x3bb;" u2="&#x3c4;" k="75" />
    <hkern u1="&#x3bb;" u2="&#x3c3;" k="15" />
    <hkern u1="&#x3bb;" u2="&#x3c2;" k="20" />
    <hkern u1="&#x3bb;" u2="&#x3c0;" k="30" />
    <hkern u1="&#x3bb;" u2="&#x3bf;" k="15" />
    <hkern u1="&#x3bb;" u2="&#x3bd;" k="45" />
    <hkern u1="&#x3bb;" u2="&#x3b8;" k="20" />
    <hkern u1="&#x3bb;" u2="&#x3b4;" k="20" />
    <hkern u1="&#x3bb;" u2="&#x3b3;" k="60" />
    <hkern u1="&#x3bb;" u2="&#x3b1;" k="15" />
    <hkern u1="&#x3bb;" u2="&#x3b0;" k="20" />
    <hkern u1="&#x3bb;" u2="&#x3ac;" k="15" />
    <hkern u1="&#x3bb;" u2="&#x2d;" k="38" />
    <hkern u1="&#x3bd;" u2="&#x2026;" k="69" />
    <hkern u1="&#x3bd;" u2="&#x2014;" k="26" />
    <hkern u1="&#x3bd;" u2="&#x2013;" k="26" />
    <hkern u1="&#x3bd;" u2="&#x3ce;" k="30" />
    <hkern u1="&#x3bd;" u2="&#x3cc;" k="45" />
    <hkern u1="&#x3bd;" u2="&#x3ca;" k="30" />
    <hkern u1="&#x3bd;" u2="&#x3c9;" k="30" />
    <hkern u1="&#x3bd;" u2="&#x3c6;" k="45" />
    <hkern u1="&#x3bd;" u2="&#x3c3;" k="45" />
    <hkern u1="&#x3bd;" u2="&#x3c2;" k="40" />
    <hkern u1="&#x3bd;" u2="&#x3c1;" k="55" />
    <hkern u1="&#x3bd;" u2="&#x3bf;" k="45" />
    <hkern u1="&#x3bd;" u2="&#x3be;" k="25" />
    <hkern u1="&#x3bd;" u2="&#x3bb;" k="50" />
    <hkern u1="&#x3bd;" u2="&#x3ba;" k="20" />
    <hkern u1="&#x3bd;" u2="&#x3b9;" k="30" />
    <hkern u1="&#x3bd;" u2="&#x3b8;" k="15" />
    <hkern u1="&#x3bd;" u2="&#x3b6;" k="35" />
    <hkern u1="&#x3bd;" u2="&#x3b5;" k="35" />
    <hkern u1="&#x3bd;" u2="&#x3b4;" k="45" />
    <hkern u1="&#x3bd;" u2="&#x3b2;" k="30" />
    <hkern u1="&#x3bd;" u2="&#x3b1;" k="30" />
    <hkern u1="&#x3bd;" u2="&#x3af;" k="30" />
    <hkern u1="&#x3bd;" u2="&#x3ad;" k="35" />
    <hkern u1="&#x3bd;" u2="&#x3ac;" k="30" />
    <hkern u1="&#x3bd;" u2="&#x390;" k="30" />
    <hkern u1="&#x3bd;" u2="&#x2e;" k="69" />
    <hkern u1="&#x3bd;" u2="&#x2d;" k="26" />
    <hkern u1="&#x3bd;" u2="&#x2c;" k="69" />
    <hkern u1="&#x3be;" u2="&#x3cc;" k="20" />
    <hkern u1="&#x3be;" u2="&#x3c6;" k="10" />
    <hkern u1="&#x3be;" u2="&#x3c3;" k="15" />
    <hkern u1="&#x3be;" u2="&#x3bf;" k="20" />
    <hkern u1="&#x3be;" u2="&#x3b4;" k="20" />
    <hkern u1="&#x3be;" u2="&#x3b1;" k="15" />
    <hkern u1="&#x3be;" u2="&#x3ac;" k="15" />
    <hkern u1="&#x3bf;" u2="&#x201d;" k="20" />
    <hkern u1="&#x3bf;" u2="&#x2019;" k="20" />
    <hkern u1="&#x3bf;" u2="&#x3c7;" k="30" />
    <hkern u1="&#x3bf;" u2="&#x3bb;" k="20" />
    <hkern u1="&#x3bf;" u2="&#x3b5;" k="10" />
    <hkern u1="&#x3bf;" u2="&#x3ad;" k="10" />
    <hkern u1="&#x3c0;" u2="&#x2014;" k="34" />
    <hkern u1="&#x3c0;" u2="&#x2013;" k="34" />
    <hkern u1="&#x3c0;" u2="&#x3ce;" k="30" />
    <hkern u1="&#x3c0;" u2="&#x3cc;" k="35" />
    <hkern u1="&#x3c0;" u2="&#x3ca;" k="15" />
    <hkern u1="&#x3c0;" u2="&#x3c9;" k="30" />
    <hkern u1="&#x3c0;" u2="&#x3c6;" k="30" />
    <hkern u1="&#x3c0;" u2="&#x3c3;" k="35" />
    <hkern u1="&#x3c0;" u2="&#x3c2;" k="30" />
    <hkern u1="&#x3c0;" u2="&#x3c1;" k="25" />
    <hkern u1="&#x3c0;" u2="&#x3bf;" k="35" />
    <hkern u1="&#x3c0;" u2="&#x3b9;" k="15" />
    <hkern u1="&#x3c0;" u2="&#x3b5;" k="30" />
    <hkern u1="&#x3c0;" u2="&#x3b4;" k="10" />
    <hkern u1="&#x3c0;" u2="&#x3b2;" k="15" />
    <hkern u1="&#x3c0;" u2="&#x3b1;" k="35" />
    <hkern u1="&#x3c0;" u2="&#x3af;" k="15" />
    <hkern u1="&#x3c0;" u2="&#x3ad;" k="30" />
    <hkern u1="&#x3c0;" u2="&#x3ac;" k="35" />
    <hkern u1="&#x3c0;" u2="&#x390;" k="15" />
    <hkern u1="&#x3c0;" u2="&#x2d;" k="34" />
    <hkern u1="&#x3c1;" u2="&#x3c7;" k="15" />
    <hkern u1="&#x3c1;" u2="&#x3c0;" k="20" />
    <hkern u1="&#x3c1;" u2="&#x3bb;" k="15" />
    <hkern u1="&#x3c1;" u2="&#x3b5;" k="7" />
    <hkern u1="&#x3c1;" u2="&#x3ad;" k="7" />
    <hkern u1="&#x3c3;" u2="&#x2026;" k="34" />
    <hkern u1="&#x3c3;" u2="&#x3ce;" k="35" />
    <hkern u1="&#x3c3;" u2="&#x3cc;" k="30" />
    <hkern u1="&#x3c3;" u2="&#x3ca;" k="15" />
    <hkern u1="&#x3c3;" u2="&#x3c9;" k="35" />
    <hkern u1="&#x3c3;" u2="&#x3c8;" k="15" />
    <hkern u1="&#x3c3;" u2="&#x3c6;" k="25" />
    <hkern u1="&#x3c3;" u2="&#x3c3;" k="30" />
    <hkern u1="&#x3c3;" u2="&#x3c2;" k="40" />
    <hkern u1="&#x3c3;" u2="&#x3c1;" k="45" />
    <hkern u1="&#x3c3;" u2="&#x3c0;" k="20" />
    <hkern u1="&#x3c3;" u2="&#x3bf;" k="30" />
    <hkern u1="&#x3c3;" u2="&#x3be;" k="20" />
    <hkern u1="&#x3c3;" u2="&#x3bc;" k="25" />
    <hkern u1="&#x3c3;" u2="&#x3bb;" k="55" />
    <hkern u1="&#x3c3;" u2="&#x3ba;" k="20" />
    <hkern u1="&#x3c3;" u2="&#x3b9;" k="15" />
    <hkern u1="&#x3c3;" u2="&#x3b6;" k="30" />
    <hkern u1="&#x3c3;" u2="&#x3b5;" k="30" />
    <hkern u1="&#x3c3;" u2="&#x3b4;" k="35" />
    <hkern u1="&#x3c3;" u2="&#x3b2;" k="25" />
    <hkern u1="&#x3c3;" u2="&#x3b1;" k="45" />
    <hkern u1="&#x3c3;" u2="&#x3af;" k="15" />
    <hkern u1="&#x3c3;" u2="&#x3ad;" k="30" />
    <hkern u1="&#x3c3;" u2="&#x3ac;" k="45" />
    <hkern u1="&#x3c3;" u2="&#x390;" k="15" />
    <hkern u1="&#x3c3;" u2="&#x2e;" k="34" />
    <hkern u1="&#x3c3;" u2="&#x2c;" k="34" />
    <hkern u1="&#x3c4;" u2="&#x2014;" k="55" />
    <hkern u1="&#x3c4;" u2="&#x2013;" k="55" />
    <hkern u1="&#x3c4;" u2="&#x3ce;" k="55" />
    <hkern u1="&#x3c4;" u2="&#x3cc;" k="65" />
    <hkern u1="&#x3c4;" u2="&#x3ca;" k="20" />
    <hkern u1="&#x3c4;" u2="&#x3c9;" k="55" />
    <hkern u1="&#x3c4;" u2="&#x3c8;" k="30" />
    <hkern u1="&#x3c4;" u2="&#x3c6;" k="70" />
    <hkern u1="&#x3c4;" u2="&#x3c4;" k="15" />
    <hkern u1="&#x3c4;" u2="&#x3c3;" k="70" />
    <hkern u1="&#x3c4;" u2="&#x3c2;" k="60" />
    <hkern u1="&#x3c4;" u2="&#x3c1;" k="70" />
    <hkern u1="&#x3c4;" u2="&#x3c0;" k="30" />
    <hkern u1="&#x3c4;" u2="&#x3bf;" k="65" />
    <hkern u1="&#x3c4;" u2="&#x3be;" k="35" />
    <hkern u1="&#x3c4;" u2="&#x3bc;" k="30" />
    <hkern u1="&#x3c4;" u2="&#x3bb;" k="50" />
    <hkern u1="&#x3c4;" u2="&#x3ba;" k="20" />
    <hkern u1="&#x3c4;" u2="&#x3b9;" k="20" />
    <hkern u1="&#x3c4;" u2="&#x3b8;" k="15" />
    <hkern u1="&#x3c4;" u2="&#x3b6;" k="35" />
    <hkern u1="&#x3c4;" u2="&#x3b5;" k="50" />
    <hkern u1="&#x3c4;" u2="&#x3b4;" k="65" />
    <hkern u1="&#x3c4;" u2="&#x3b2;" k="35" />
    <hkern u1="&#x3c4;" u2="&#x3b1;" k="65" />
    <hkern u1="&#x3c4;" u2="&#x3af;" k="20" />
    <hkern u1="&#x3c4;" u2="&#x3ad;" k="50" />
    <hkern u1="&#x3c4;" u2="&#x3ac;" k="65" />
    <hkern u1="&#x3c4;" u2="&#x390;" k="20" />
    <hkern u1="&#x3c4;" u2="&#xb5;" k="25" />
    <hkern u1="&#x3c4;" u2="&#x2d;" k="55" />
    <hkern u1="&#x3c5;" u2="&#x3ce;" k="10" />
    <hkern u1="&#x3c5;" u2="&#x3c9;" k="10" />
    <hkern u1="&#x3c5;" u2="&#x3c1;" k="25" />
    <hkern u1="&#x3c5;" u2="&#x3bb;" k="25" />
    <hkern u1="&#x3c5;" u2="&#x3b5;" k="13" />
    <hkern u1="&#x3c5;" u2="&#x3b1;" k="10" />
    <hkern u1="&#x3c5;" u2="&#x3ad;" k="13" />
    <hkern u1="&#x3c5;" u2="&#x3ac;" k="10" />
    <hkern u1="&#x3c6;" u2="&#x3c7;" k="25" />
    <hkern u1="&#x3c6;" u2="&#x3c0;" k="15" />
    <hkern u1="&#x3c6;" u2="&#x3bb;" k="15" />
    <hkern u1="&#x3c7;" u2="&#x2026;" k="34" />
    <hkern u1="&#x3c7;" u2="&#x2014;" k="39" />
    <hkern u1="&#x3c7;" u2="&#x2013;" k="39" />
    <hkern u1="&#x3c7;" u2="&#x3ce;" k="50" />
    <hkern u1="&#x3c7;" u2="&#x3cc;" k="55" />
    <hkern u1="&#x3c7;" u2="&#x3ca;" k="35" />
    <hkern u1="&#x3c7;" u2="&#x3c9;" k="50" />
    <hkern u1="&#x3c7;" u2="&#x3c8;" k="20" />
    <hkern u1="&#x3c7;" u2="&#x3c6;" k="50" />
    <hkern u1="&#x3c7;" u2="&#x3c4;" k="15" />
    <hkern u1="&#x3c7;" u2="&#x3c3;" k="55" />
    <hkern u1="&#x3c7;" u2="&#x3c2;" k="35" />
    <hkern u1="&#x3c7;" u2="&#x3c0;" k="15" />
    <hkern u1="&#x3c7;" u2="&#x3bf;" k="55" />
    <hkern u1="&#x3c7;" u2="&#x3be;" k="25" />
    <hkern u1="&#x3c7;" u2="&#x3bb;" k="55" />
    <hkern u1="&#x3c7;" u2="&#x3ba;" k="25" />
    <hkern u1="&#x3c7;" u2="&#x3b9;" k="35" />
    <hkern u1="&#x3c7;" u2="&#x3b8;" k="25" />
    <hkern u1="&#x3c7;" u2="&#x3b6;" k="25" />
    <hkern u1="&#x3c7;" u2="&#x3b5;" k="40" />
    <hkern u1="&#x3c7;" u2="&#x3b4;" k="55" />
    <hkern u1="&#x3c7;" u2="&#x3b1;" k="50" />
    <hkern u1="&#x3c7;" u2="&#x3af;" k="35" />
    <hkern u1="&#x3c7;" u2="&#x3ad;" k="40" />
    <hkern u1="&#x3c7;" u2="&#x3ac;" k="50" />
    <hkern u1="&#x3c7;" u2="&#x390;" k="35" />
    <hkern u1="&#x3c7;" u2="&#x2e;" k="34" />
    <hkern u1="&#x3c7;" u2="&#x2d;" k="39" />
    <hkern u1="&#x3c7;" u2="&#x2c;" k="34" />
    <hkern u1="&#x3c8;" u2="&#x3bb;" k="25" />
    <hkern u1="&#x3c8;" u2="&#x3b1;" k="5" />
    <hkern u1="&#x3c8;" u2="&#x3ac;" k="5" />
    <hkern u1="&#x3c9;" u2="&#x3c4;" k="10" />
    <hkern u1="&#x3c9;" u2="&#x3c0;" k="10" />
    <hkern u1="&#x3c9;" u2="&#x3bb;" k="15" />
    <hkern u1="&#x3ca;" u2="&#x3cc;" k="15" />
    <hkern u1="&#x3ca;" u2="&#x3c6;" k="20" />
    <hkern u1="&#x3ca;" u2="&#x3c4;" k="20" />
    <hkern u1="&#x3ca;" u2="&#x3c3;" k="15" />
    <hkern u1="&#x3ca;" u2="&#x3c0;" k="20" />
    <hkern u1="&#x3ca;" u2="&#x3bf;" k="15" />
    <hkern u1="&#x3ca;" u2="&#x3b8;" k="10" />
    <hkern u1="&#x3ca;" u2="&#x3b4;" k="10" />
    <hkern u1="&#x3ca;" u2="&#x3b1;" k="7" />
    <hkern u1="&#x3ca;" u2="&#x3ac;" k="7" />
    <hkern u1="&#x3cb;" u2="&#x3ce;" k="10" />
    <hkern u1="&#x3cb;" u2="&#x3c9;" k="10" />
    <hkern u1="&#x3cb;" u2="&#x3c1;" k="25" />
    <hkern u1="&#x3cb;" u2="&#x3bb;" k="25" />
    <hkern u1="&#x3cb;" u2="&#x3b5;" k="13" />
    <hkern u1="&#x3cb;" u2="&#x3b1;" k="10" />
    <hkern u1="&#x3cb;" u2="&#x3ad;" k="13" />
    <hkern u1="&#x3cb;" u2="&#x3ac;" k="10" />
    <hkern u1="&#x3cc;" u2="&#x201d;" k="20" />
    <hkern u1="&#x3cc;" u2="&#x2019;" k="20" />
    <hkern u1="&#x3cc;" u2="&#x3c7;" k="30" />
    <hkern u1="&#x3cc;" u2="&#x3bb;" k="20" />
    <hkern u1="&#x3cc;" u2="&#x3b5;" k="10" />
    <hkern u1="&#x3cc;" u2="&#x3ad;" k="10" />
    <hkern u1="&#x3cd;" u2="&#x3ce;" k="10" />
    <hkern u1="&#x3cd;" u2="&#x3c9;" k="10" />
    <hkern u1="&#x3cd;" u2="&#x3c1;" k="25" />
    <hkern u1="&#x3cd;" u2="&#x3bb;" k="25" />
    <hkern u1="&#x3cd;" u2="&#x3b5;" k="13" />
    <hkern u1="&#x3cd;" u2="&#x3b1;" k="10" />
    <hkern u1="&#x3cd;" u2="&#x3ad;" k="13" />
    <hkern u1="&#x3cd;" u2="&#x3ac;" k="10" />
    <hkern u1="&#x3ce;" u2="&#x3c4;" k="10" />
    <hkern u1="&#x3ce;" u2="&#x3c0;" k="10" />
    <hkern u1="&#x3ce;" u2="&#x3bb;" k="15" />
    <hkern u1="&#x401;" u2="&#x424;" k="41" />
    <hkern u1="&#x401;" u2="&#x421;" k="20" />
    <hkern u1="&#x401;" u2="&#x41e;" k="20" />
    <hkern u1="&#x401;" u2="&#x404;" k="20" />
    <hkern u1="&#x402;" u2="&#x427;" k="20" />
    <hkern u1="&#x402;" u2="&#x423;" k="30" />
    <hkern u1="&#x402;" u2="&#x422;" k="70" />
    <hkern u1="&#x402;" u2="&#x416;" k="20" />
    <hkern u1="&#x402;" u2="&#x40e;" k="30" />
    <hkern u1="&#x402;" u2="&#x40b;" k="40" />
    <hkern u1="&#x402;" u2="&#x402;" k="65" />
    <hkern u1="&#x403;" u2="&#x203a;" k="72" />
    <hkern u1="&#x403;" u2="&#x2026;" k="128" />
    <hkern u1="&#x403;" u2="&#x2014;" k="95" />
    <hkern u1="&#x403;" u2="&#x2013;" k="95" />
    <hkern u1="&#x403;" u2="&#x491;" k="136" />
    <hkern u1="&#x403;" u2="&#x45f;" k="80" />
    <hkern u1="&#x403;" u2="&#x45e;" k="105" />
    <hkern u1="&#x403;" u2="&#x45c;" k="136" />
    <hkern u1="&#x403;" u2="&#x45a;" k="80" />
    <hkern u1="&#x403;" u2="&#x459;" k="159" />
    <hkern u1="&#x403;" u2="&#x455;" k="118" />
    <hkern u1="&#x403;" u2="&#x454;" k="164" />
    <hkern u1="&#x403;" u2="&#x453;" k="136" />
    <hkern u1="&#x403;" u2="&#x451;" k="164" />
    <hkern u1="&#x403;" u2="&#x44f;" k="127" />
    <hkern u1="&#x403;" u2="&#x44e;" k="136" />
    <hkern u1="&#x403;" u2="&#x44d;" k="127" />
    <hkern u1="&#x403;" u2="&#x44c;" k="136" />
    <hkern u1="&#x403;" u2="&#x44b;" k="136" />
    <hkern u1="&#x403;" u2="&#x44a;" k="105" />
    <hkern u1="&#x403;" u2="&#x449;" k="136" />
    <hkern u1="&#x403;" u2="&#x448;" k="136" />
    <hkern u1="&#x403;" u2="&#x447;" k="127" />
    <hkern u1="&#x403;" u2="&#x446;" k="136" />
    <hkern u1="&#x403;" u2="&#x445;" k="100" />
    <hkern u1="&#x403;" u2="&#x444;" k="155" />
    <hkern u1="&#x403;" u2="&#x443;" k="105" />
    <hkern u1="&#x403;" u2="&#x442;" k="118" />
    <hkern u1="&#x403;" u2="&#x441;" k="164" />
    <hkern u1="&#x403;" u2="&#x440;" k="136" />
    <hkern u1="&#x403;" u2="&#x43f;" k="136" />
    <hkern u1="&#x403;" u2="&#x43e;" k="164" />
    <hkern u1="&#x403;" u2="&#x43d;" k="136" />
    <hkern u1="&#x403;" u2="&#x43c;" k="136" />
    <hkern u1="&#x403;" u2="&#x43b;" k="159" />
    <hkern u1="&#x403;" u2="&#x43a;" k="136" />
    <hkern u1="&#x403;" u2="&#x439;" k="136" />
    <hkern u1="&#x403;" u2="&#x438;" k="136" />
    <hkern u1="&#x403;" u2="&#x437;" k="123" />
    <hkern u1="&#x403;" u2="&#x436;" k="132" />
    <hkern u1="&#x403;" u2="&#x435;" k="164" />
    <hkern u1="&#x403;" u2="&#x434;" k="177" />
    <hkern u1="&#x403;" u2="&#x433;" k="136" />
    <hkern u1="&#x403;" u2="&#x432;" k="136" />
    <hkern u1="&#x403;" u2="&#x431;" k="59" />
    <hkern u1="&#x403;" u2="&#x430;" k="146" />
    <hkern u1="&#x403;" u2="&#x42f;" k="16" />
    <hkern u1="&#x403;" u2="&#x424;" k="65" />
    <hkern u1="&#x403;" u2="&#x421;" k="25" />
    <hkern u1="&#x403;" u2="&#x41e;" k="25" />
    <hkern u1="&#x403;" u2="&#x41b;" k="70" />
    <hkern u1="&#x403;" u2="&#x414;" k="70" />
    <hkern u1="&#x403;" u2="&#x410;" k="100" />
    <hkern u1="&#x403;" u2="&#x409;" k="70" />
    <hkern u1="&#x403;" u2="&#x408;" k="130" />
    <hkern u1="&#x403;" u2="&#x404;" k="25" />
    <hkern u1="&#x403;" u2="&#xbb;" k="72" />
    <hkern u1="&#x403;" u2="&#x2e;" k="128" />
    <hkern u1="&#x403;" u2="&#x2d;" k="95" />
    <hkern u1="&#x403;" u2="&#x2c;" k="128" />
    <hkern u1="&#x404;" u2="&#x424;" k="20" />
    <hkern u1="&#x409;" u2="&#x201d;" k="25" />
    <hkern u1="&#x409;" u2="&#x2019;" k="25" />
    <hkern u1="&#x409;" u2="&#x427;" k="35" />
    <hkern u1="&#x409;" u2="&#x423;" k="60" />
    <hkern u1="&#x409;" u2="&#x422;" k="65" />
    <hkern u1="&#x409;" u2="&#x416;" k="20" />
    <hkern u1="&#x409;" u2="&#x40e;" k="60" />
    <hkern u1="&#x409;" u2="&#x40b;" k="45" />
    <hkern u1="&#x409;" u2="&#x402;" k="45" />
    <hkern u1="&#x40a;" u2="&#x201d;" k="25" />
    <hkern u1="&#x40a;" u2="&#x2019;" k="25" />
    <hkern u1="&#x40a;" u2="&#x427;" k="35" />
    <hkern u1="&#x40a;" u2="&#x423;" k="60" />
    <hkern u1="&#x40a;" u2="&#x422;" k="65" />
    <hkern u1="&#x40a;" u2="&#x416;" k="20" />
    <hkern u1="&#x40a;" u2="&#x40e;" k="60" />
    <hkern u1="&#x40a;" u2="&#x40b;" k="45" />
    <hkern u1="&#x40a;" u2="&#x402;" k="45" />
    <hkern u1="&#x40b;" u2="&#x427;" k="25" />
    <hkern u1="&#x40b;" u2="&#x423;" k="73" />
    <hkern u1="&#x40b;" u2="&#x40e;" k="73" />
    <hkern u1="&#x40b;" u2="&#x402;" k="85" />
    <hkern u1="&#x40c;" u2="&#x454;" k="25" />
    <hkern u1="&#x40c;" u2="&#x451;" k="25" />
    <hkern u1="&#x40c;" u2="&#x447;" k="55" />
    <hkern u1="&#x40c;" u2="&#x444;" k="25" />
    <hkern u1="&#x40c;" u2="&#x441;" k="25" />
    <hkern u1="&#x40c;" u2="&#x43e;" k="25" />
    <hkern u1="&#x40c;" u2="&#x435;" k="25" />
    <hkern u1="&#x40c;" u2="&#x431;" k="20" />
    <hkern u1="&#x40c;" u2="&#x424;" k="20" />
    <hkern u1="&#x40c;" u2="&#x421;" k="20" />
    <hkern u1="&#x40c;" u2="&#x41e;" k="20" />
    <hkern u1="&#x40c;" u2="&#x404;" k="20" />
    <hkern u1="&#x40e;" u2="&#x203a;" k="33" />
    <hkern u1="&#x40e;" u2="&#x2026;" k="113" />
    <hkern u1="&#x40e;" u2="&#x491;" k="44" />
    <hkern u1="&#x40e;" u2="&#x45f;" k="44" />
    <hkern u1="&#x40e;" u2="&#x45c;" k="44" />
    <hkern u1="&#x40e;" u2="&#x45a;" k="44" />
    <hkern u1="&#x40e;" u2="&#x459;" k="78" />
    <hkern u1="&#x40e;" u2="&#x455;" k="30" />
    <hkern u1="&#x40e;" u2="&#x454;" k="55" />
    <hkern u1="&#x40e;" u2="&#x453;" k="44" />
    <hkern u1="&#x40e;" u2="&#x451;" k="55" />
    <hkern u1="&#x40e;" u2="&#x44f;" k="30" />
    <hkern u1="&#x40e;" u2="&#x44e;" k="44" />
    <hkern u1="&#x40e;" u2="&#x44d;" k="40" />
    <hkern u1="&#x40e;" u2="&#x44c;" k="44" />
    <hkern u1="&#x40e;" u2="&#x44b;" k="44" />
    <hkern u1="&#x40e;" u2="&#x449;" k="44" />
    <hkern u1="&#x40e;" u2="&#x448;" k="44" />
    <hkern u1="&#x40e;" u2="&#x447;" k="34" />
    <hkern u1="&#x40e;" u2="&#x446;" k="44" />
    <hkern u1="&#x40e;" u2="&#x444;" k="59" />
    <hkern u1="&#x40e;" u2="&#x441;" k="55" />
    <hkern u1="&#x40e;" u2="&#x440;" k="44" />
    <hkern u1="&#x40e;" u2="&#x43f;" k="44" />
    <hkern u1="&#x40e;" u2="&#x43e;" k="55" />
    <hkern u1="&#x40e;" u2="&#x43d;" k="44" />
    <hkern u1="&#x40e;" u2="&#x43c;" k="44" />
    <hkern u1="&#x40e;" u2="&#x43b;" k="78" />
    <hkern u1="&#x40e;" u2="&#x43a;" k="44" />
    <hkern u1="&#x40e;" u2="&#x439;" k="44" />
    <hkern u1="&#x40e;" u2="&#x438;" k="44" />
    <hkern u1="&#x40e;" u2="&#x437;" k="44" />
    <hkern u1="&#x40e;" u2="&#x436;" k="29" />
    <hkern u1="&#x40e;" u2="&#x435;" k="55" />
    <hkern u1="&#x40e;" u2="&#x434;" k="70" />
    <hkern u1="&#x40e;" u2="&#x433;" k="44" />
    <hkern u1="&#x40e;" u2="&#x432;" k="44" />
    <hkern u1="&#x40e;" u2="&#x431;" k="49" />
    <hkern u1="&#x40e;" u2="&#x430;" k="59" />
    <hkern u1="&#x40e;" u2="&#x424;" k="39" />
    <hkern u1="&#x40e;" u2="&#x421;" k="29" />
    <hkern u1="&#x40e;" u2="&#x41e;" k="29" />
    <hkern u1="&#x40e;" u2="&#x41b;" k="80" />
    <hkern u1="&#x40e;" u2="&#x414;" k="70" />
    <hkern u1="&#x40e;" u2="&#x410;" k="65" />
    <hkern u1="&#x40e;" u2="&#x409;" k="80" />
    <hkern u1="&#x40e;" u2="&#x408;" k="100" />
    <hkern u1="&#x40e;" u2="&#x404;" k="29" />
    <hkern u1="&#x40e;" u2="&#xbb;" k="33" />
    <hkern u1="&#x40e;" u2="&#x2e;" k="113" />
    <hkern u1="&#x40e;" u2="&#x2c;" k="113" />
    <hkern u1="&#x410;" u2="&#x201d;" k="54" />
    <hkern u1="&#x410;" u2="&#x2019;" k="54" />
    <hkern u1="&#x410;" u2="&#x45e;" k="45" />
    <hkern u1="&#x410;" u2="&#x447;" k="45" />
    <hkern u1="&#x410;" u2="&#x443;" k="45" />
    <hkern u1="&#x410;" u2="&#x442;" k="30" />
    <hkern u1="&#x410;" u2="&#x427;" k="60" />
    <hkern u1="&#x410;" u2="&#x424;" k="15" />
    <hkern u1="&#x410;" u2="&#x423;" k="32" />
    <hkern u1="&#x410;" u2="&#x422;" k="70" />
    <hkern u1="&#x410;" u2="&#x421;" k="10" />
    <hkern u1="&#x410;" u2="&#x41e;" k="10" />
    <hkern u1="&#x410;" u2="&#x417;" k="12" />
    <hkern u1="&#x410;" u2="&#x40e;" k="32" />
    <hkern u1="&#x410;" u2="&#x40b;" k="60" />
    <hkern u1="&#x410;" u2="&#x404;" k="10" />
    <hkern u1="&#x410;" u2="&#x402;" k="64" />
    <hkern u1="&#x411;" u2="&#x442;" k="30" />
    <hkern u1="&#x411;" u2="&#x427;" k="30" />
    <hkern u1="&#x411;" u2="&#x423;" k="25" />
    <hkern u1="&#x411;" u2="&#x40e;" k="25" />
    <hkern u1="&#x412;" u2="&#x423;" k="30" />
    <hkern u1="&#x412;" u2="&#x422;" k="10" />
    <hkern u1="&#x412;" u2="&#x40e;" k="30" />
    <hkern u1="&#x412;" u2="&#x40b;" k="10" />
    <hkern u1="&#x412;" u2="&#x402;" k="10" />
    <hkern u1="&#x413;" u2="&#x203a;" k="72" />
    <hkern u1="&#x413;" u2="&#x2026;" k="128" />
    <hkern u1="&#x413;" u2="&#x2014;" k="95" />
    <hkern u1="&#x413;" u2="&#x2013;" k="95" />
    <hkern u1="&#x413;" u2="&#x491;" k="136" />
    <hkern u1="&#x413;" u2="&#x45f;" k="80" />
    <hkern u1="&#x413;" u2="&#x45e;" k="105" />
    <hkern u1="&#x413;" u2="&#x45c;" k="136" />
    <hkern u1="&#x413;" u2="&#x45a;" k="80" />
    <hkern u1="&#x413;" u2="&#x459;" k="159" />
    <hkern u1="&#x413;" u2="&#x455;" k="118" />
    <hkern u1="&#x413;" u2="&#x454;" k="164" />
    <hkern u1="&#x413;" u2="&#x453;" k="136" />
    <hkern u1="&#x413;" u2="&#x451;" k="164" />
    <hkern u1="&#x413;" u2="&#x44f;" k="127" />
    <hkern u1="&#x413;" u2="&#x44e;" k="136" />
    <hkern u1="&#x413;" u2="&#x44d;" k="127" />
    <hkern u1="&#x413;" u2="&#x44c;" k="136" />
    <hkern u1="&#x413;" u2="&#x44b;" k="136" />
    <hkern u1="&#x413;" u2="&#x44a;" k="105" />
    <hkern u1="&#x413;" u2="&#x449;" k="136" />
    <hkern u1="&#x413;" u2="&#x448;" k="136" />
    <hkern u1="&#x413;" u2="&#x447;" k="127" />
    <hkern u1="&#x413;" u2="&#x446;" k="136" />
    <hkern u1="&#x413;" u2="&#x445;" k="100" />
    <hkern u1="&#x413;" u2="&#x444;" k="155" />
    <hkern u1="&#x413;" u2="&#x443;" k="105" />
    <hkern u1="&#x413;" u2="&#x442;" k="118" />
    <hkern u1="&#x413;" u2="&#x441;" k="164" />
    <hkern u1="&#x413;" u2="&#x440;" k="136" />
    <hkern u1="&#x413;" u2="&#x43f;" k="136" />
    <hkern u1="&#x413;" u2="&#x43e;" k="164" />
    <hkern u1="&#x413;" u2="&#x43d;" k="136" />
    <hkern u1="&#x413;" u2="&#x43c;" k="136" />
    <hkern u1="&#x413;" u2="&#x43b;" k="159" />
    <hkern u1="&#x413;" u2="&#x43a;" k="136" />
    <hkern u1="&#x413;" u2="&#x439;" k="136" />
    <hkern u1="&#x413;" u2="&#x438;" k="136" />
    <hkern u1="&#x413;" u2="&#x437;" k="123" />
    <hkern u1="&#x413;" u2="&#x436;" k="132" />
    <hkern u1="&#x413;" u2="&#x435;" k="164" />
    <hkern u1="&#x413;" u2="&#x434;" k="177" />
    <hkern u1="&#x413;" u2="&#x433;" k="136" />
    <hkern u1="&#x413;" u2="&#x432;" k="136" />
    <hkern u1="&#x413;" u2="&#x431;" k="59" />
    <hkern u1="&#x413;" u2="&#x430;" k="146" />
    <hkern u1="&#x413;" u2="&#x42f;" k="16" />
    <hkern u1="&#x413;" u2="&#x424;" k="65" />
    <hkern u1="&#x413;" u2="&#x421;" k="25" />
    <hkern u1="&#x413;" u2="&#x41e;" k="25" />
    <hkern u1="&#x413;" u2="&#x41b;" k="70" />
    <hkern u1="&#x413;" u2="&#x414;" k="70" />
    <hkern u1="&#x413;" u2="&#x410;" k="100" />
    <hkern u1="&#x413;" u2="&#x409;" k="70" />
    <hkern u1="&#x413;" u2="&#x408;" k="130" />
    <hkern u1="&#x413;" u2="&#x404;" k="25" />
    <hkern u1="&#x413;" u2="&#xbb;" k="72" />
    <hkern u1="&#x413;" u2="&#x2e;" k="128" />
    <hkern u1="&#x413;" u2="&#x2d;" k="95" />
    <hkern u1="&#x413;" u2="&#x2c;" k="128" />
    <hkern u1="&#x415;" u2="&#x424;" k="41" />
    <hkern u1="&#x415;" u2="&#x421;" k="20" />
    <hkern u1="&#x415;" u2="&#x41e;" k="20" />
    <hkern u1="&#x415;" u2="&#x404;" k="20" />
    <hkern u1="&#x416;" u2="&#x454;" k="25" />
    <hkern u1="&#x416;" u2="&#x451;" k="25" />
    <hkern u1="&#x416;" u2="&#x447;" k="61" />
    <hkern u1="&#x416;" u2="&#x444;" k="25" />
    <hkern u1="&#x416;" u2="&#x442;" k="30" />
    <hkern u1="&#x416;" u2="&#x441;" k="25" />
    <hkern u1="&#x416;" u2="&#x43e;" k="25" />
    <hkern u1="&#x416;" u2="&#x435;" k="25" />
    <hkern u1="&#x416;" u2="&#x431;" k="20" />
    <hkern u1="&#x416;" u2="&#x424;" k="35" />
    <hkern u1="&#x416;" u2="&#x421;" k="20" />
    <hkern u1="&#x416;" u2="&#x41e;" k="20" />
    <hkern u1="&#x416;" u2="&#x404;" k="20" />
    <hkern u1="&#x417;" u2="&#x422;" k="17" />
    <hkern u1="&#x41a;" u2="&#x454;" k="25" />
    <hkern u1="&#x41a;" u2="&#x451;" k="25" />
    <hkern u1="&#x41a;" u2="&#x447;" k="55" />
    <hkern u1="&#x41a;" u2="&#x444;" k="25" />
    <hkern u1="&#x41a;" u2="&#x441;" k="25" />
    <hkern u1="&#x41a;" u2="&#x43e;" k="25" />
    <hkern u1="&#x41a;" u2="&#x435;" k="25" />
    <hkern u1="&#x41a;" u2="&#x431;" k="20" />
    <hkern u1="&#x41a;" u2="&#x424;" k="20" />
    <hkern u1="&#x41a;" u2="&#x421;" k="20" />
    <hkern u1="&#x41a;" u2="&#x41e;" k="20" />
    <hkern u1="&#x41a;" u2="&#x404;" k="20" />
    <hkern u1="&#x41e;" u2="&#x459;" k="20" />
    <hkern u1="&#x41e;" u2="&#x43b;" k="20" />
    <hkern u1="&#x41e;" u2="&#x434;" k="25" />
    <hkern u1="&#x41e;" u2="&#x425;" k="30" />
    <hkern u1="&#x41e;" u2="&#x423;" k="30" />
    <hkern u1="&#x41e;" u2="&#x422;" k="25" />
    <hkern u1="&#x41e;" u2="&#x41b;" k="25" />
    <hkern u1="&#x41e;" u2="&#x417;" k="20" />
    <hkern u1="&#x41e;" u2="&#x416;" k="20" />
    <hkern u1="&#x41e;" u2="&#x414;" k="30" />
    <hkern u1="&#x41e;" u2="&#x410;" k="10" />
    <hkern u1="&#x41e;" u2="&#x40e;" k="30" />
    <hkern u1="&#x41e;" u2="&#x40b;" k="20" />
    <hkern u1="&#x41e;" u2="&#x409;" k="20" />
    <hkern u1="&#x41e;" u2="&#x408;" k="35" />
    <hkern u1="&#x41e;" u2="&#x402;" k="20" />
    <hkern u1="&#x420;" u2="&#x2026;" k="122" />
    <hkern u1="&#x420;" u2="&#x459;" k="50" />
    <hkern u1="&#x420;" u2="&#x444;" k="15" />
    <hkern u1="&#x420;" u2="&#x43b;" k="50" />
    <hkern u1="&#x420;" u2="&#x434;" k="50" />
    <hkern u1="&#x420;" u2="&#x41b;" k="70" />
    <hkern u1="&#x420;" u2="&#x414;" k="65" />
    <hkern u1="&#x420;" u2="&#x410;" k="40" />
    <hkern u1="&#x420;" u2="&#x409;" k="70" />
    <hkern u1="&#x420;" u2="&#x408;" k="95" />
    <hkern u1="&#x420;" u2="&#x2e;" k="122" />
    <hkern u1="&#x420;" u2="&#x2c;" k="122" />
    <hkern u1="&#x421;" u2="&#x447;" k="45" />
    <hkern u1="&#x421;" u2="&#x424;" k="20" />
    <hkern u1="&#x422;" u2="&#x203a;" k="67" />
    <hkern u1="&#x422;" u2="&#x2026;" k="64" />
    <hkern u1="&#x422;" u2="&#x2014;" k="67" />
    <hkern u1="&#x422;" u2="&#x2013;" k="67" />
    <hkern u1="&#x422;" u2="&#x491;" k="64" />
    <hkern u1="&#x422;" u2="&#x45f;" k="64" />
    <hkern u1="&#x422;" u2="&#x45e;" k="44" />
    <hkern u1="&#x422;" u2="&#x45c;" k="64" />
    <hkern u1="&#x422;" u2="&#x45a;" k="64" />
    <hkern u1="&#x422;" u2="&#x459;" k="78" />
    <hkern u1="&#x422;" u2="&#x455;" k="73" />
    <hkern u1="&#x422;" u2="&#x454;" k="94" />
    <hkern u1="&#x422;" u2="&#x453;" k="64" />
    <hkern u1="&#x422;" u2="&#x451;" k="94" />
    <hkern u1="&#x422;" u2="&#x44f;" k="73" />
    <hkern u1="&#x422;" u2="&#x44e;" k="64" />
    <hkern u1="&#x422;" u2="&#x44d;" k="44" />
    <hkern u1="&#x422;" u2="&#x44c;" k="64" />
    <hkern u1="&#x422;" u2="&#x44b;" k="64" />
    <hkern u1="&#x422;" u2="&#x44a;" k="24" />
    <hkern u1="&#x422;" u2="&#x449;" k="64" />
    <hkern u1="&#x422;" u2="&#x448;" k="64" />
    <hkern u1="&#x422;" u2="&#x447;" k="78" />
    <hkern u1="&#x422;" u2="&#x446;" k="64" />
    <hkern u1="&#x422;" u2="&#x445;" k="34" />
    <hkern u1="&#x422;" u2="&#x444;" k="73" />
    <hkern u1="&#x422;" u2="&#x443;" k="44" />
    <hkern u1="&#x422;" u2="&#x441;" k="94" />
    <hkern u1="&#x422;" u2="&#x440;" k="64" />
    <hkern u1="&#x422;" u2="&#x43f;" k="64" />
    <hkern u1="&#x422;" u2="&#x43e;" k="94" />
    <hkern u1="&#x422;" u2="&#x43d;" k="64" />
    <hkern u1="&#x422;" u2="&#x43c;" k="64" />
    <hkern u1="&#x422;" u2="&#x43b;" k="78" />
    <hkern u1="&#x422;" u2="&#x43a;" k="64" />
    <hkern u1="&#x422;" u2="&#x439;" k="64" />
    <hkern u1="&#x422;" u2="&#x438;" k="64" />
    <hkern u1="&#x422;" u2="&#x437;" k="44" />
    <hkern u1="&#x422;" u2="&#x436;" k="54" />
    <hkern u1="&#x422;" u2="&#x435;" k="94" />
    <hkern u1="&#x422;" u2="&#x434;" k="103" />
    <hkern u1="&#x422;" u2="&#x433;" k="64" />
    <hkern u1="&#x422;" u2="&#x432;" k="64" />
    <hkern u1="&#x422;" u2="&#x431;" k="29" />
    <hkern u1="&#x422;" u2="&#x430;" k="42" />
    <hkern u1="&#x422;" u2="&#x424;" k="50" />
    <hkern u1="&#x422;" u2="&#x421;" k="30" />
    <hkern u1="&#x422;" u2="&#x41e;" k="30" />
    <hkern u1="&#x422;" u2="&#x41b;" k="60" />
    <hkern u1="&#x422;" u2="&#x414;" k="60" />
    <hkern u1="&#x422;" u2="&#x410;" k="60" />
    <hkern u1="&#x422;" u2="&#x409;" k="60" />
    <hkern u1="&#x422;" u2="&#x408;" k="87" />
    <hkern u1="&#x422;" u2="&#x404;" k="30" />
    <hkern u1="&#x422;" u2="&#xbb;" k="67" />
    <hkern u1="&#x422;" u2="&#x2e;" k="64" />
    <hkern u1="&#x422;" u2="&#x2d;" k="67" />
    <hkern u1="&#x422;" u2="&#x2c;" k="64" />
    <hkern u1="&#x423;" u2="&#x203a;" k="33" />
    <hkern u1="&#x423;" u2="&#x2026;" k="113" />
    <hkern u1="&#x423;" u2="&#x491;" k="44" />
    <hkern u1="&#x423;" u2="&#x45f;" k="44" />
    <hkern u1="&#x423;" u2="&#x45c;" k="44" />
    <hkern u1="&#x423;" u2="&#x45a;" k="44" />
    <hkern u1="&#x423;" u2="&#x459;" k="78" />
    <hkern u1="&#x423;" u2="&#x455;" k="30" />
    <hkern u1="&#x423;" u2="&#x454;" k="55" />
    <hkern u1="&#x423;" u2="&#x453;" k="44" />
    <hkern u1="&#x423;" u2="&#x451;" k="55" />
    <hkern u1="&#x423;" u2="&#x44f;" k="30" />
    <hkern u1="&#x423;" u2="&#x44e;" k="44" />
    <hkern u1="&#x423;" u2="&#x44d;" k="40" />
    <hkern u1="&#x423;" u2="&#x44c;" k="44" />
    <hkern u1="&#x423;" u2="&#x44b;" k="44" />
    <hkern u1="&#x423;" u2="&#x449;" k="44" />
    <hkern u1="&#x423;" u2="&#x448;" k="44" />
    <hkern u1="&#x423;" u2="&#x447;" k="34" />
    <hkern u1="&#x423;" u2="&#x446;" k="44" />
    <hkern u1="&#x423;" u2="&#x444;" k="59" />
    <hkern u1="&#x423;" u2="&#x441;" k="55" />
    <hkern u1="&#x423;" u2="&#x440;" k="44" />
    <hkern u1="&#x423;" u2="&#x43f;" k="44" />
    <hkern u1="&#x423;" u2="&#x43e;" k="55" />
    <hkern u1="&#x423;" u2="&#x43d;" k="44" />
    <hkern u1="&#x423;" u2="&#x43c;" k="44" />
    <hkern u1="&#x423;" u2="&#x43b;" k="78" />
    <hkern u1="&#x423;" u2="&#x43a;" k="44" />
    <hkern u1="&#x423;" u2="&#x439;" k="44" />
    <hkern u1="&#x423;" u2="&#x438;" k="44" />
    <hkern u1="&#x423;" u2="&#x437;" k="44" />
    <hkern u1="&#x423;" u2="&#x436;" k="29" />
    <hkern u1="&#x423;" u2="&#x435;" k="55" />
    <hkern u1="&#x423;" u2="&#x434;" k="70" />
    <hkern u1="&#x423;" u2="&#x433;" k="44" />
    <hkern u1="&#x423;" u2="&#x432;" k="44" />
    <hkern u1="&#x423;" u2="&#x431;" k="49" />
    <hkern u1="&#x423;" u2="&#x430;" k="59" />
    <hkern u1="&#x423;" u2="&#x424;" k="39" />
    <hkern u1="&#x423;" u2="&#x421;" k="29" />
    <hkern u1="&#x423;" u2="&#x41e;" k="29" />
    <hkern u1="&#x423;" u2="&#x41b;" k="80" />
    <hkern u1="&#x423;" u2="&#x414;" k="70" />
    <hkern u1="&#x423;" u2="&#x410;" k="65" />
    <hkern u1="&#x423;" u2="&#x409;" k="80" />
    <hkern u1="&#x423;" u2="&#x408;" k="100" />
    <hkern u1="&#x423;" u2="&#x404;" k="29" />
    <hkern u1="&#x423;" u2="&#xbb;" k="33" />
    <hkern u1="&#x423;" u2="&#x2e;" k="113" />
    <hkern u1="&#x423;" u2="&#x2c;" k="113" />
    <hkern u1="&#x424;" u2="&#x2026;" k="68" />
    <hkern u1="&#x424;" u2="&#x459;" k="25" />
    <hkern u1="&#x424;" u2="&#x43b;" k="25" />
    <hkern u1="&#x424;" u2="&#x434;" k="45" />
    <hkern u1="&#x424;" u2="&#x425;" k="30" />
    <hkern u1="&#x424;" u2="&#x423;" k="40" />
    <hkern u1="&#x424;" u2="&#x422;" k="50" />
    <hkern u1="&#x424;" u2="&#x41b;" k="45" />
    <hkern u1="&#x424;" u2="&#x417;" k="23" />
    <hkern u1="&#x424;" u2="&#x416;" k="35" />
    <hkern u1="&#x424;" u2="&#x414;" k="30" />
    <hkern u1="&#x424;" u2="&#x410;" k="15" />
    <hkern u1="&#x424;" u2="&#x40e;" k="40" />
    <hkern u1="&#x424;" u2="&#x40b;" k="30" />
    <hkern u1="&#x424;" u2="&#x409;" k="45" />
    <hkern u1="&#x424;" u2="&#x408;" k="50" />
    <hkern u1="&#x424;" u2="&#x402;" k="30" />
    <hkern u1="&#x424;" u2="&#x2e;" k="68" />
    <hkern u1="&#x424;" u2="&#x2c;" k="68" />
    <hkern u1="&#x425;" u2="&#x447;" k="50" />
    <hkern u1="&#x425;" u2="&#x424;" k="30" />
    <hkern u1="&#x425;" u2="&#x421;" k="30" />
    <hkern u1="&#x425;" u2="&#x41e;" k="30" />
    <hkern u1="&#x425;" u2="&#x404;" k="30" />
    <hkern u1="&#x426;" u2="&#x408;" k="-41" />
    <hkern u1="&#x42a;" u2="&#x201d;" k="25" />
    <hkern u1="&#x42a;" u2="&#x2019;" k="25" />
    <hkern u1="&#x42a;" u2="&#x427;" k="35" />
    <hkern u1="&#x42a;" u2="&#x423;" k="60" />
    <hkern u1="&#x42a;" u2="&#x422;" k="65" />
    <hkern u1="&#x42a;" u2="&#x416;" k="20" />
    <hkern u1="&#x42a;" u2="&#x40e;" k="60" />
    <hkern u1="&#x42a;" u2="&#x40b;" k="45" />
    <hkern u1="&#x42a;" u2="&#x402;" k="45" />
    <hkern u1="&#x42c;" u2="&#x201d;" k="25" />
    <hkern u1="&#x42c;" u2="&#x2019;" k="25" />
    <hkern u1="&#x42c;" u2="&#x427;" k="35" />
    <hkern u1="&#x42c;" u2="&#x423;" k="60" />
    <hkern u1="&#x42c;" u2="&#x422;" k="65" />
    <hkern u1="&#x42c;" u2="&#x416;" k="20" />
    <hkern u1="&#x42c;" u2="&#x40e;" k="60" />
    <hkern u1="&#x42c;" u2="&#x40b;" k="45" />
    <hkern u1="&#x42c;" u2="&#x402;" k="45" />
    <hkern u1="&#x42d;" u2="&#x459;" k="20" />
    <hkern u1="&#x42d;" u2="&#x43b;" k="20" />
    <hkern u1="&#x42d;" u2="&#x434;" k="25" />
    <hkern u1="&#x42d;" u2="&#x425;" k="30" />
    <hkern u1="&#x42d;" u2="&#x423;" k="30" />
    <hkern u1="&#x42d;" u2="&#x422;" k="25" />
    <hkern u1="&#x42d;" u2="&#x41b;" k="25" />
    <hkern u1="&#x42d;" u2="&#x417;" k="20" />
    <hkern u1="&#x42d;" u2="&#x416;" k="20" />
    <hkern u1="&#x42d;" u2="&#x414;" k="30" />
    <hkern u1="&#x42d;" u2="&#x410;" k="10" />
    <hkern u1="&#x42d;" u2="&#x40e;" k="30" />
    <hkern u1="&#x42d;" u2="&#x40b;" k="20" />
    <hkern u1="&#x42d;" u2="&#x409;" k="20" />
    <hkern u1="&#x42d;" u2="&#x408;" k="35" />
    <hkern u1="&#x42d;" u2="&#x402;" k="20" />
    <hkern u1="&#x42e;" u2="&#x459;" k="20" />
    <hkern u1="&#x42e;" u2="&#x43b;" k="20" />
    <hkern u1="&#x42e;" u2="&#x434;" k="25" />
    <hkern u1="&#x42e;" u2="&#x425;" k="30" />
    <hkern u1="&#x42e;" u2="&#x423;" k="30" />
    <hkern u1="&#x42e;" u2="&#x422;" k="25" />
    <hkern u1="&#x42e;" u2="&#x41b;" k="25" />
    <hkern u1="&#x42e;" u2="&#x417;" k="20" />
    <hkern u1="&#x42e;" u2="&#x416;" k="20" />
    <hkern u1="&#x42e;" u2="&#x414;" k="30" />
    <hkern u1="&#x42e;" u2="&#x410;" k="10" />
    <hkern u1="&#x42e;" u2="&#x40e;" k="30" />
    <hkern u1="&#x42e;" u2="&#x40b;" k="20" />
    <hkern u1="&#x42e;" u2="&#x409;" k="20" />
    <hkern u1="&#x42e;" u2="&#x408;" k="35" />
    <hkern u1="&#x42e;" u2="&#x402;" k="20" />
    <hkern u1="&#x431;" u2="&#x459;" k="10" />
    <hkern u1="&#x431;" u2="&#x445;" k="10" />
    <hkern u1="&#x431;" u2="&#x442;" k="25" />
    <hkern u1="&#x431;" u2="&#x43b;" k="10" />
    <hkern u1="&#x431;" u2="&#x436;" k="25" />
    <hkern u1="&#x431;" u2="&#x434;" k="20" />
    <hkern u1="&#x433;" u2="&#x2026;" k="88" />
    <hkern u1="&#x433;" u2="&#x459;" k="30" />
    <hkern u1="&#x433;" u2="&#x454;" k="15" />
    <hkern u1="&#x433;" u2="&#x451;" k="15" />
    <hkern u1="&#x433;" u2="&#x44a;" k="-10" />
    <hkern u1="&#x433;" u2="&#x444;" k="25" />
    <hkern u1="&#x433;" u2="&#x441;" k="15" />
    <hkern u1="&#x433;" u2="&#x43e;" k="15" />
    <hkern u1="&#x433;" u2="&#x43b;" k="30" />
    <hkern u1="&#x433;" u2="&#x435;" k="15" />
    <hkern u1="&#x433;" u2="&#x434;" k="40" />
    <hkern u1="&#x433;" u2="&#x2e;" k="88" />
    <hkern u1="&#x433;" u2="&#x2c;" k="88" />
    <hkern u1="&#x436;" u2="&#x454;" k="20" />
    <hkern u1="&#x436;" u2="&#x451;" k="20" />
    <hkern u1="&#x436;" u2="&#x444;" k="20" />
    <hkern u1="&#x436;" u2="&#x441;" k="20" />
    <hkern u1="&#x436;" u2="&#x43e;" k="20" />
    <hkern u1="&#x436;" u2="&#x435;" k="20" />
    <hkern u1="&#x436;" u2="&#x430;" k="15" />
    <hkern u1="&#x43a;" u2="&#x454;" k="20" />
    <hkern u1="&#x43a;" u2="&#x451;" k="20" />
    <hkern u1="&#x43a;" u2="&#x444;" k="20" />
    <hkern u1="&#x43a;" u2="&#x441;" k="20" />
    <hkern u1="&#x43a;" u2="&#x43e;" k="20" />
    <hkern u1="&#x43a;" u2="&#x435;" k="20" />
    <hkern u1="&#x43e;" u2="&#x459;" k="10" />
    <hkern u1="&#x43e;" u2="&#x445;" k="10" />
    <hkern u1="&#x43e;" u2="&#x442;" k="15" />
    <hkern u1="&#x43e;" u2="&#x43b;" k="10" />
    <hkern u1="&#x43e;" u2="&#x436;" k="20" />
    <hkern u1="&#x43e;" u2="&#x434;" k="20" />
    <hkern u1="&#x440;" u2="&#x459;" k="10" />
    <hkern u1="&#x440;" u2="&#x445;" k="10" />
    <hkern u1="&#x440;" u2="&#x442;" k="10" />
    <hkern u1="&#x440;" u2="&#x43b;" k="10" />
    <hkern u1="&#x440;" u2="&#x436;" k="10" />
    <hkern u1="&#x440;" u2="&#x434;" k="20" />
    <hkern u1="&#x442;" u2="&#x2026;" k="49" />
    <hkern u1="&#x442;" u2="&#x459;" k="25" />
    <hkern u1="&#x442;" u2="&#x454;" k="15" />
    <hkern u1="&#x442;" u2="&#x451;" k="10" />
    <hkern u1="&#x442;" u2="&#x444;" k="25" />
    <hkern u1="&#x442;" u2="&#x441;" k="15" />
    <hkern u1="&#x442;" u2="&#x43e;" k="15" />
    <hkern u1="&#x442;" u2="&#x43b;" k="25" />
    <hkern u1="&#x442;" u2="&#x435;" k="10" />
    <hkern u1="&#x442;" u2="&#x434;" k="35" />
    <hkern u1="&#x442;" u2="&#x2e;" k="49" />
    <hkern u1="&#x442;" u2="&#x2c;" k="49" />
    <hkern u1="&#x443;" u2="&#x2026;" k="54" />
    <hkern u1="&#x443;" u2="&#x459;" k="25" />
    <hkern u1="&#x443;" u2="&#x454;" k="5" />
    <hkern u1="&#x443;" u2="&#x441;" k="5" />
    <hkern u1="&#x443;" u2="&#x43e;" k="5" />
    <hkern u1="&#x443;" u2="&#x43b;" k="25" />
    <hkern u1="&#x443;" u2="&#x434;" k="40" />
    <hkern u1="&#x443;" u2="&#x2e;" k="54" />
    <hkern u1="&#x443;" u2="&#x2c;" k="54" />
    <hkern u1="&#x444;" u2="&#x2026;" k="30" />
    <hkern u1="&#x444;" u2="&#x459;" k="10" />
    <hkern u1="&#x444;" u2="&#x445;" k="15" />
    <hkern u1="&#x444;" u2="&#x442;" k="15" />
    <hkern u1="&#x444;" u2="&#x43b;" k="10" />
    <hkern u1="&#x444;" u2="&#x436;" k="20" />
    <hkern u1="&#x444;" u2="&#x434;" k="20" />
    <hkern u1="&#x444;" u2="&#x2e;" k="30" />
    <hkern u1="&#x444;" u2="&#x2c;" k="30" />
    <hkern u1="&#x445;" u2="&#x454;" k="10" />
    <hkern u1="&#x445;" u2="&#x451;" k="10" />
    <hkern u1="&#x445;" u2="&#x444;" k="15" />
    <hkern u1="&#x445;" u2="&#x441;" k="10" />
    <hkern u1="&#x445;" u2="&#x43e;" k="10" />
    <hkern u1="&#x445;" u2="&#x435;" k="10" />
    <hkern u1="&#x44a;" u2="&#x45e;" k="40" />
    <hkern u1="&#x44a;" u2="&#x459;" k="10" />
    <hkern u1="&#x44a;" u2="&#x455;" k="10" />
    <hkern u1="&#x44a;" u2="&#x44f;" k="10" />
    <hkern u1="&#x44a;" u2="&#x44a;" k="25" />
    <hkern u1="&#x44a;" u2="&#x447;" k="30" />
    <hkern u1="&#x44a;" u2="&#x445;" k="15" />
    <hkern u1="&#x44a;" u2="&#x443;" k="40" />
    <hkern u1="&#x44a;" u2="&#x442;" k="55" />
    <hkern u1="&#x44a;" u2="&#x43b;" k="10" />
    <hkern u1="&#x44a;" u2="&#x436;" k="35" />
    <hkern u1="&#x44a;" u2="&#x434;" k="20" />
    <hkern u1="&#x44c;" u2="&#x45e;" k="40" />
    <hkern u1="&#x44c;" u2="&#x459;" k="10" />
    <hkern u1="&#x44c;" u2="&#x455;" k="10" />
    <hkern u1="&#x44c;" u2="&#x44f;" k="10" />
    <hkern u1="&#x44c;" u2="&#x44a;" k="25" />
    <hkern u1="&#x44c;" u2="&#x447;" k="30" />
    <hkern u1="&#x44c;" u2="&#x445;" k="15" />
    <hkern u1="&#x44c;" u2="&#x443;" k="40" />
    <hkern u1="&#x44c;" u2="&#x442;" k="55" />
    <hkern u1="&#x44c;" u2="&#x43b;" k="10" />
    <hkern u1="&#x44c;" u2="&#x436;" k="35" />
    <hkern u1="&#x44c;" u2="&#x434;" k="20" />
    <hkern u1="&#x44d;" u2="&#x459;" k="10" />
    <hkern u1="&#x44d;" u2="&#x445;" k="10" />
    <hkern u1="&#x44d;" u2="&#x43b;" k="10" />
    <hkern u1="&#x44d;" u2="&#x436;" k="15" />
    <hkern u1="&#x44d;" u2="&#x434;" k="20" />
    <hkern u1="&#x44e;" u2="&#x459;" k="10" />
    <hkern u1="&#x44e;" u2="&#x445;" k="10" />
    <hkern u1="&#x44e;" u2="&#x442;" k="15" />
    <hkern u1="&#x44e;" u2="&#x43b;" k="10" />
    <hkern u1="&#x44e;" u2="&#x436;" k="20" />
    <hkern u1="&#x44e;" u2="&#x434;" k="20" />
    <hkern u1="&#x452;" u2="&#x447;" k="20" />
    <hkern u1="&#x453;" u2="&#x2026;" k="88" />
    <hkern u1="&#x453;" u2="&#x459;" k="30" />
    <hkern u1="&#x453;" u2="&#x454;" k="15" />
    <hkern u1="&#x453;" u2="&#x451;" k="15" />
    <hkern u1="&#x453;" u2="&#x44a;" k="-10" />
    <hkern u1="&#x453;" u2="&#x444;" k="25" />
    <hkern u1="&#x453;" u2="&#x441;" k="15" />
    <hkern u1="&#x453;" u2="&#x43e;" k="15" />
    <hkern u1="&#x453;" u2="&#x43b;" k="30" />
    <hkern u1="&#x453;" u2="&#x435;" k="15" />
    <hkern u1="&#x453;" u2="&#x434;" k="40" />
    <hkern u1="&#x453;" u2="&#x2e;" k="88" />
    <hkern u1="&#x453;" u2="&#x2c;" k="88" />
    <hkern u1="&#x459;" u2="&#x45e;" k="40" />
    <hkern u1="&#x459;" u2="&#x459;" k="10" />
    <hkern u1="&#x459;" u2="&#x455;" k="10" />
    <hkern u1="&#x459;" u2="&#x44f;" k="10" />
    <hkern u1="&#x459;" u2="&#x44a;" k="25" />
    <hkern u1="&#x459;" u2="&#x447;" k="30" />
    <hkern u1="&#x459;" u2="&#x445;" k="15" />
    <hkern u1="&#x459;" u2="&#x443;" k="40" />
    <hkern u1="&#x459;" u2="&#x442;" k="55" />
    <hkern u1="&#x459;" u2="&#x43b;" k="10" />
    <hkern u1="&#x459;" u2="&#x436;" k="35" />
    <hkern u1="&#x459;" u2="&#x434;" k="20" />
    <hkern u1="&#x45a;" u2="&#x45e;" k="40" />
    <hkern u1="&#x45a;" u2="&#x459;" k="10" />
    <hkern u1="&#x45a;" u2="&#x455;" k="10" />
    <hkern u1="&#x45a;" u2="&#x44f;" k="10" />
    <hkern u1="&#x45a;" u2="&#x44a;" k="25" />
    <hkern u1="&#x45a;" u2="&#x447;" k="30" />
    <hkern u1="&#x45a;" u2="&#x445;" k="15" />
    <hkern u1="&#x45a;" u2="&#x443;" k="40" />
    <hkern u1="&#x45a;" u2="&#x442;" k="55" />
    <hkern u1="&#x45a;" u2="&#x43b;" k="10" />
    <hkern u1="&#x45a;" u2="&#x436;" k="35" />
    <hkern u1="&#x45a;" u2="&#x434;" k="20" />
    <hkern u1="&#x45c;" u2="&#x454;" k="20" />
    <hkern u1="&#x45c;" u2="&#x451;" k="20" />
    <hkern u1="&#x45c;" u2="&#x444;" k="20" />
    <hkern u1="&#x45c;" u2="&#x441;" k="20" />
    <hkern u1="&#x45c;" u2="&#x43e;" k="20" />
    <hkern u1="&#x45c;" u2="&#x435;" k="20" />
    <hkern u1="&#x45e;" u2="&#x2026;" k="54" />
    <hkern u1="&#x45e;" u2="&#x459;" k="25" />
    <hkern u1="&#x45e;" u2="&#x454;" k="5" />
    <hkern u1="&#x45e;" u2="&#x441;" k="5" />
    <hkern u1="&#x45e;" u2="&#x43e;" k="5" />
    <hkern u1="&#x45e;" u2="&#x43b;" k="25" />
    <hkern u1="&#x45e;" u2="&#x434;" k="40" />
    <hkern u1="&#x45e;" u2="&#x2e;" k="54" />
    <hkern u1="&#x45e;" u2="&#x2c;" k="54" />
    <hkern u1="&#x490;" u2="&#x203a;" k="72" />
    <hkern u1="&#x490;" u2="&#x2026;" k="128" />
    <hkern u1="&#x490;" u2="&#x2014;" k="95" />
    <hkern u1="&#x490;" u2="&#x2013;" k="95" />
    <hkern u1="&#x490;" u2="&#x491;" k="136" />
    <hkern u1="&#x490;" u2="&#x45f;" k="80" />
    <hkern u1="&#x490;" u2="&#x45e;" k="105" />
    <hkern u1="&#x490;" u2="&#x45c;" k="136" />
    <hkern u1="&#x490;" u2="&#x45a;" k="80" />
    <hkern u1="&#x490;" u2="&#x459;" k="159" />
    <hkern u1="&#x490;" u2="&#x455;" k="118" />
    <hkern u1="&#x490;" u2="&#x454;" k="164" />
    <hkern u1="&#x490;" u2="&#x453;" k="136" />
    <hkern u1="&#x490;" u2="&#x451;" k="164" />
    <hkern u1="&#x490;" u2="&#x44f;" k="127" />
    <hkern u1="&#x490;" u2="&#x44e;" k="136" />
    <hkern u1="&#x490;" u2="&#x44d;" k="127" />
    <hkern u1="&#x490;" u2="&#x44c;" k="136" />
    <hkern u1="&#x490;" u2="&#x44b;" k="136" />
    <hkern u1="&#x490;" u2="&#x44a;" k="105" />
    <hkern u1="&#x490;" u2="&#x449;" k="136" />
    <hkern u1="&#x490;" u2="&#x448;" k="136" />
    <hkern u1="&#x490;" u2="&#x447;" k="127" />
    <hkern u1="&#x490;" u2="&#x446;" k="136" />
    <hkern u1="&#x490;" u2="&#x445;" k="100" />
    <hkern u1="&#x490;" u2="&#x444;" k="155" />
    <hkern u1="&#x490;" u2="&#x443;" k="105" />
    <hkern u1="&#x490;" u2="&#x442;" k="118" />
    <hkern u1="&#x490;" u2="&#x441;" k="164" />
    <hkern u1="&#x490;" u2="&#x440;" k="136" />
    <hkern u1="&#x490;" u2="&#x43f;" k="136" />
    <hkern u1="&#x490;" u2="&#x43e;" k="164" />
    <hkern u1="&#x490;" u2="&#x43d;" k="136" />
    <hkern u1="&#x490;" u2="&#x43c;" k="136" />
    <hkern u1="&#x490;" u2="&#x43b;" k="159" />
    <hkern u1="&#x490;" u2="&#x43a;" k="136" />
    <hkern u1="&#x490;" u2="&#x439;" k="136" />
    <hkern u1="&#x490;" u2="&#x438;" k="136" />
    <hkern u1="&#x490;" u2="&#x437;" k="123" />
    <hkern u1="&#x490;" u2="&#x436;" k="132" />
    <hkern u1="&#x490;" u2="&#x435;" k="164" />
    <hkern u1="&#x490;" u2="&#x434;" k="177" />
    <hkern u1="&#x490;" u2="&#x433;" k="136" />
    <hkern u1="&#x490;" u2="&#x432;" k="136" />
    <hkern u1="&#x490;" u2="&#x431;" k="59" />
    <hkern u1="&#x490;" u2="&#x430;" k="146" />
    <hkern u1="&#x490;" u2="&#x42f;" k="16" />
    <hkern u1="&#x490;" u2="&#x424;" k="65" />
    <hkern u1="&#x490;" u2="&#x421;" k="25" />
    <hkern u1="&#x490;" u2="&#x41e;" k="25" />
    <hkern u1="&#x490;" u2="&#x41b;" k="70" />
    <hkern u1="&#x490;" u2="&#x414;" k="70" />
    <hkern u1="&#x490;" u2="&#x410;" k="100" />
    <hkern u1="&#x490;" u2="&#x409;" k="70" />
    <hkern u1="&#x490;" u2="&#x408;" k="109" />
    <hkern u1="&#x490;" u2="&#x404;" k="25" />
    <hkern u1="&#x490;" u2="&#xbb;" k="72" />
    <hkern u1="&#x490;" u2="&#x2e;" k="128" />
    <hkern u1="&#x490;" u2="&#x2d;" k="95" />
    <hkern u1="&#x490;" u2="&#x2c;" k="128" />
    <hkern u1="&#x491;" u2="&#x2026;" k="79" />
    <hkern u1="&#x491;" u2="&#x459;" k="35" />
    <hkern u1="&#x491;" u2="&#x454;" k="10" />
    <hkern u1="&#x491;" u2="&#x451;" k="10" />
    <hkern u1="&#x491;" u2="&#x444;" k="20" />
    <hkern u1="&#x491;" u2="&#x441;" k="10" />
    <hkern u1="&#x491;" u2="&#x43e;" k="10" />
    <hkern u1="&#x491;" u2="&#x43b;" k="35" />
    <hkern u1="&#x491;" u2="&#x435;" k="10" />
    <hkern u1="&#x491;" u2="&#x434;" k="40" />
    <hkern u1="&#x491;" u2="&#x2e;" k="79" />
    <hkern u1="&#x491;" u2="&#x2c;" k="79" />
    <hkern u1="&#x2013;" u2="&#x422;" k="66" />
    <hkern u1="&#x2013;" u2="&#x3c7;" k="52" />
    <hkern u1="&#x2013;" u2="&#x3bb;" k="30" />
    <hkern u1="&#x2013;" u2="&#x3ab;" k="56" />
    <hkern u1="&#x2013;" u2="&#x3a5;" k="56" />
    <hkern u1="&#x2013;" u2="&#x3a4;" k="70" />
    <hkern u1="&#x2013;" u2="Y" k="32" />
    <hkern u1="&#x2013;" u2="X" k="3" />
    <hkern u1="&#x2013;" u2="V" k="7" />
    <hkern u1="&#x2013;" u2="T" k="24" />
    <hkern u1="&#x2014;" u2="&#x422;" k="66" />
    <hkern u1="&#x2014;" u2="&#x3c7;" k="52" />
    <hkern u1="&#x2014;" u2="&#x3bb;" k="30" />
    <hkern u1="&#x2014;" u2="&#x3ab;" k="56" />
    <hkern u1="&#x2014;" u2="&#x3a5;" k="56" />
    <hkern u1="&#x2014;" u2="&#x3a4;" k="70" />
    <hkern u1="&#x2014;" u2="Y" k="32" />
    <hkern u1="&#x2014;" u2="X" k="3" />
    <hkern u1="&#x2014;" u2="V" k="7" />
    <hkern u1="&#x2014;" u2="T" k="24" />
    <hkern u1="&#x2018;" u2="&#x434;" k="30" />
    <hkern u1="&#x2018;" u2="&#x414;" k="44" />
    <hkern u1="&#x2018;" u2="&#x410;" k="54" />
    <hkern u1="&#x2018;" u2="&#x104;" k="27" />
    <hkern u1="&#x2018;" u2="&#x102;" k="27" />
    <hkern u1="&#x2018;" u2="&#x100;" k="27" />
    <hkern u1="&#x2018;" u2="&#xc6;" k="146" />
    <hkern u1="&#x2018;" u2="&#xc5;" k="27" />
    <hkern u1="&#x2018;" u2="&#xc4;" k="27" />
    <hkern u1="&#x2018;" u2="&#xc3;" k="27" />
    <hkern u1="&#x2018;" u2="&#xc2;" k="27" />
    <hkern u1="&#x2018;" u2="&#xc1;" k="27" />
    <hkern u1="&#x2018;" u2="&#xc0;" k="27" />
    <hkern u1="&#x2018;" u2="A" k="27" />
    <hkern u1="&#x2019;" u2="&#x3ce;" k="90" />
    <hkern u1="&#x2019;" u2="&#x3cc;" k="80" />
    <hkern u1="&#x2019;" u2="&#x3ca;" k="40" />
    <hkern u1="&#x2019;" u2="&#x3c9;" k="90" />
    <hkern u1="&#x2019;" u2="&#x3c8;" k="40" />
    <hkern u1="&#x2019;" u2="&#x3c6;" k="90" />
    <hkern u1="&#x2019;" u2="&#x3c4;" k="20" />
    <hkern u1="&#x2019;" u2="&#x3c3;" k="80" />
    <hkern u1="&#x2019;" u2="&#x3c2;" k="60" />
    <hkern u1="&#x2019;" u2="&#x3c1;" k="100" />
    <hkern u1="&#x2019;" u2="&#x3c0;" k="30" />
    <hkern u1="&#x2019;" u2="&#x3bf;" k="80" />
    <hkern u1="&#x2019;" u2="&#x3ba;" k="30" />
    <hkern u1="&#x2019;" u2="&#x3b9;" k="40" />
    <hkern u1="&#x2019;" u2="&#x3b8;" k="30" />
    <hkern u1="&#x2019;" u2="&#x3b6;" k="30" />
    <hkern u1="&#x2019;" u2="&#x3b5;" k="90" />
    <hkern u1="&#x2019;" u2="&#x3b4;" k="55" />
    <hkern u1="&#x2019;" u2="&#x3b2;" k="25" />
    <hkern u1="&#x2019;" u2="&#x3b1;" k="100" />
    <hkern u1="&#x2019;" u2="&#x3af;" k="40" />
    <hkern u1="&#x2019;" u2="&#x3ad;" k="90" />
    <hkern u1="&#x2019;" u2="&#x3ac;" k="100" />
    <hkern u1="&#x2019;" u2="&#x3a6;" k="61" />
    <hkern u1="&#x2019;" u2="&#x39f;" k="30" />
    <hkern u1="&#x2019;" u2="&#x39b;" k="100" />
    <hkern u1="&#x2019;" u2="&#x398;" k="30" />
    <hkern u1="&#x2019;" u2="&#x394;" k="100" />
    <hkern u1="&#x2019;" u2="&#x391;" k="100" />
    <hkern u1="&#x2019;" u2="&#x390;" k="40" />
    <hkern u1="&#x2019;" u2="&#xb5;" k="55" />
    <hkern u1="&#x2019;" u2="s" k="45" />
    <hkern u1="&#x2019;" u2="r" k="32" />
    <hkern u1="&#x2019;" u2="n" k="32" />
    <hkern u1="&#x201a;" u2="y" k="37" />
    <hkern u1="&#x201a;" u2="w" k="28" />
    <hkern u1="&#x201a;" u2="v" k="32" />
    <hkern u1="&#x201a;" u2="t" k="23" />
    <hkern u1="&#x201c;" u2="&#x434;" k="30" />
    <hkern u1="&#x201c;" u2="&#x414;" k="44" />
    <hkern u1="&#x201c;" u2="&#x410;" k="54" />
    <hkern u1="&#x201c;" u2="&#x104;" k="27" />
    <hkern u1="&#x201c;" u2="&#x102;" k="27" />
    <hkern u1="&#x201c;" u2="&#x100;" k="27" />
    <hkern u1="&#x201c;" u2="&#xc6;" k="146" />
    <hkern u1="&#x201c;" u2="&#xc5;" k="27" />
    <hkern u1="&#x201c;" u2="&#xc4;" k="27" />
    <hkern u1="&#x201c;" u2="&#xc3;" k="27" />
    <hkern u1="&#x201c;" u2="&#xc2;" k="27" />
    <hkern u1="&#x201c;" u2="&#xc1;" k="27" />
    <hkern u1="&#x201c;" u2="&#xc0;" k="27" />
    <hkern u1="&#x201c;" u2="A" k="27" />
    <hkern u1="&#x201d;" u2="&#x3ce;" k="90" />
    <hkern u1="&#x201d;" u2="&#x3cc;" k="80" />
    <hkern u1="&#x201d;" u2="&#x3ca;" k="40" />
    <hkern u1="&#x201d;" u2="&#x3c9;" k="90" />
    <hkern u1="&#x201d;" u2="&#x3c8;" k="40" />
    <hkern u1="&#x201d;" u2="&#x3c6;" k="90" />
    <hkern u1="&#x201d;" u2="&#x3c4;" k="20" />
    <hkern u1="&#x201d;" u2="&#x3c3;" k="80" />
    <hkern u1="&#x201d;" u2="&#x3c2;" k="60" />
    <hkern u1="&#x201d;" u2="&#x3c1;" k="100" />
    <hkern u1="&#x201d;" u2="&#x3c0;" k="30" />
    <hkern u1="&#x201d;" u2="&#x3bf;" k="80" />
    <hkern u1="&#x201d;" u2="&#x3ba;" k="30" />
    <hkern u1="&#x201d;" u2="&#x3b9;" k="40" />
    <hkern u1="&#x201d;" u2="&#x3b8;" k="30" />
    <hkern u1="&#x201d;" u2="&#x3b6;" k="30" />
    <hkern u1="&#x201d;" u2="&#x3b5;" k="90" />
    <hkern u1="&#x201d;" u2="&#x3b4;" k="55" />
    <hkern u1="&#x201d;" u2="&#x3b2;" k="25" />
    <hkern u1="&#x201d;" u2="&#x3b1;" k="100" />
    <hkern u1="&#x201d;" u2="&#x3af;" k="40" />
    <hkern u1="&#x201d;" u2="&#x3ad;" k="90" />
    <hkern u1="&#x201d;" u2="&#x3ac;" k="100" />
    <hkern u1="&#x201d;" u2="&#x3a6;" k="61" />
    <hkern u1="&#x201d;" u2="&#x39f;" k="30" />
    <hkern u1="&#x201d;" u2="&#x39b;" k="100" />
    <hkern u1="&#x201d;" u2="&#x398;" k="30" />
    <hkern u1="&#x201d;" u2="&#x394;" k="100" />
    <hkern u1="&#x201d;" u2="&#x391;" k="100" />
    <hkern u1="&#x201d;" u2="&#x390;" k="40" />
    <hkern u1="&#x201d;" u2="&#xb5;" k="55" />
    <hkern u1="&#x201d;" u2="s" k="45" />
    <hkern u1="&#x201d;" u2="r" k="32" />
    <hkern u1="&#x201d;" u2="n" k="32" />
    <hkern u1="&#x201e;" u2="y" k="37" />
    <hkern u1="&#x201e;" u2="w" k="28" />
    <hkern u1="&#x201e;" u2="v" k="32" />
    <hkern u1="&#x201e;" u2="t" k="23" />
    <hkern u1="&#x2026;" u2="&#x45e;" k="45" />
    <hkern u1="&#x2026;" u2="&#x447;" k="73" />
    <hkern u1="&#x2026;" u2="&#x444;" k="30" />
    <hkern u1="&#x2026;" u2="&#x443;" k="45" />
    <hkern u1="&#x2026;" u2="&#x442;" k="54" />
    <hkern u1="&#x2026;" u2="&#x427;" k="84" />
    <hkern u1="&#x2026;" u2="&#x424;" k="60" />
    <hkern u1="&#x2026;" u2="&#x422;" k="59" />
    <hkern u1="&#x2026;" u2="&#x3c7;" k="43" />
    <hkern u1="&#x2026;" u2="&#x3c4;" k="68" />
    <hkern u1="&#x2026;" u2="&#x3c0;" k="47" />
    <hkern u1="&#x2026;" u2="&#x3bd;" k="60" />
    <hkern u1="&#x2026;" u2="&#x3b3;" k="55" />
    <hkern u1="&#x2026;" u2="&#x3ab;" k="100" />
    <hkern u1="&#x2026;" u2="&#x3a8;" k="77" />
    <hkern u1="&#x2026;" u2="&#x3a6;" k="40" />
    <hkern u1="&#x2026;" u2="&#x3a5;" k="100" />
    <hkern u1="&#x2026;" u2="&#x3a4;" k="64" />
    <hkern u1="&#x2026;" u2="&#x153;" k="27" />
    <hkern u1="&#x2026;" u2="&#x152;" k="41" />
    <hkern u1="&#x2026;" u2="y" k="23" />
    <hkern u1="&#x2026;" u2="w" k="28" />
    <hkern u1="&#x2026;" u2="v" k="42" />
    <hkern u1="&#x2026;" u2="o" k="27" />
    <hkern u1="&#x2026;" u2="e" k="27" />
    <hkern u1="&#x2026;" u2="c" k="27" />
    <hkern u1="&#x2026;" u2="Y" k="79" />
    <hkern u1="&#x2026;" u2="W" k="25" />
    <hkern u1="&#x2026;" u2="V" k="50" />
    <hkern u1="&#x2026;" u2="T" k="75" />
    <hkern u1="&#x2026;" u2="Q" k="41" />
    <hkern u1="&#x2026;" u2="O" k="41" />
    <hkern u1="&#x2026;" u2="G" k="41" />
    <hkern u1="&#x2026;" u2="C" k="41" />
    <hkern u1="&#x2039;" u2="&#x422;" k="50" />
    <hkern u1="&#x2039;" u2="&#x3ab;" k="40" />
    <hkern u1="&#x2039;" u2="&#x3a5;" k="40" />
    <hkern u1="&#x2039;" u2="&#x3a4;" k="30" />
    <hkern u1="&#x203a;" u2="Y" k="51" />
    <hkern u1="&#x203a;" u2="V" k="27" />
    <hkern u1="&#x203a;" u2="T" k="56" />
  </font>
</defs></svg>
