<?xml version="1.0" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd" >
<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1">
<metadata>
Created by FontForge 20150318 at Wed Aug 26 10:01:45 2015
 By uniteet7
Copyright (c) 2006 Dalton Maag Ltd. All rights reserved. This font is for exclusive use by Vodafone only. This font may not be altered in any way without prior consent of Dalton Maag Ltd. DaMa is a trademark of Dalton Maag Ltd
</metadata>
<defs>
<font id="VodafoneRgBd" horiz-adv-x="513" >
  <font-face 
    font-family="VodafoneRgBd"
    font-weight="700"
    font-stretch="normal"
    units-per-em="1000"
    panose-1="2 11 8 3 8 2 2 2 2 4"
    ascent="800"
    descent="-200"
    x-height="482"
    cap-height="667"
    bbox="-239 -220 1040 912"
    underline-thickness="70"
    underline-position="-115"
    unicode-range="U+000D-FB02"
  />
<missing-glyph horiz-adv-x="190" 
 />
    <glyph glyph-name=".notdef" horiz-adv-x="190" 
 />
    <glyph glyph-name=".null" horiz-adv-x="201" 
 />
    <glyph glyph-name=".null" horiz-adv-x="201" 
 />
    <glyph glyph-name="nonmarkingreturn" unicode="&#xd;" horiz-adv-x="190" 
 />
    <glyph glyph-name="space" unicode=" " horiz-adv-x="190" 
 />
    <glyph glyph-name="exclam" unicode="!" horiz-adv-x="229" 
d="M115 -11q-35 0 -56.5 24.5t-21.5 56.5t21.5 56.5t56.5 24.5q34 0 55.5 -24.5t21.5 -56.5t-21.5 -56.5t-55.5 -24.5zM53 211v456h126v-456h-126z" />
    <glyph glyph-name="quotedbl" unicode="&#x22;" horiz-adv-x="430" 
d="M60 371v296h118v-296h-118zM251 371v296h119v-296h-119z" />
    <glyph glyph-name="numbersign" unicode="#" horiz-adv-x="547" 
d="M132 74v132h-132v119h132v91h-132v118h132v133h113v-133h86v133h112v-133h133v-118h-133v-91h133v-119h-133v-132h-112v132h-86v-132h-113zM245 325h86v91h-86v-91z" />
    <glyph glyph-name="dollar" unicode="$" 
d="M211 -139v128q-63 8 -113.5 34t-81.5 66l80 97q32 -39 70 -60.5t81 -21.5q45 0 72 19.5t27 55.5q0 37 -24 57.5t-69 39.5q-41 16 -80 32t-70 39.5t-50 58t-19 87.5q0 61 31 101q31 42 71 59.5t75 22.5v147h87v-144q52 -7 98 -33t72 -68l-80 -82q-34 38 -65.5 54t-66.5 16
q-43 0 -68 -18t-25 -50q0 -17 8.5 -30t21 -23t27.5 -17t27 -12q40 -17 80.5 -34t73.5 -42t54 -62t21 -94q0 -59 -30 -104q-27 -42 -66.5 -62.5t-81.5 -27.5v-129h-87z" />
    <glyph glyph-name="percent" unicode="%" horiz-adv-x="703" 
d="M541 -8q-40 0 -70 13.5t-50 35.5t-30 50t-10 57q0 36 13 65.5t35.5 50t52 31t60.5 10.5q58 0 98 -32q30 -28 45 -61.5t15 -62.5q0 -30 -10 -58t-30 -50t-49.5 -35.5t-69.5 -13.5zM76 -6l-66 72l624 609l66 -69zM540 78q29 0 43 22.5t14 48.5t-14 48.5t-43 22.5t-43 -22.5
t-14 -48.5t14 -48.5t43 -22.5zM163 362q-40 0 -69.5 13.5t-49.5 35.5t-30 50t-10 57q0 37 13 66.5t35.5 50t51.5 30.5t59 10q59 0 99 -32q41 -32 51 -68q10 -42 10 -56q0 -30 -10 -58t-30 -50t-50 -35.5t-70 -13.5zM162 448q22 0 34 13.5t17.5 30.5t5.5 28q0 31 -17 51
t-40 20q-27 0 -42 -21.5t-15 -49.5q0 -13 3.5 -26t10.5 -23t17.5 -16.5t25.5 -6.5z" />
    <glyph glyph-name="ampersand" unicode="&#x26;" horiz-adv-x="624" 
d="M218 -11q-54 0 -93 16t-64 43t-36.5 61.5t-11.5 72.5q0 60 32 113.5t101 95.5l27 16q-19 25 -34 53.5t-15 64.5q0 37 15.5 64.5t40 45t54.5 25t56 7.5q61 0 100.5 -22.5t76.5 -71.5l-80 -72q-23 32 -45.5 50.5t-49.5 18.5q-23 0 -36 -11t-13 -30q0 -26 28 -60l133 -183
l102 149l85 -77l-116 -168l139 -190h-145l-62 86q-37 -45 -70.5 -66.5t-64.5 -26t-54 -4.5zM222 96q34 0 63 23.5t53 59.5l-104 142l-21 -12q-36 -21 -56.5 -52t-20.5 -69q0 -42 22 -67t64 -25z" />
    <glyph glyph-name="quotesingle" unicode="'" horiz-adv-x="238" 
d="M60 371v296h118v-296h-118z" />
    <glyph glyph-name="parenleft" unicode="(" horiz-adv-x="315" 
d="M250 -65q-48 27 -87 67t-66.5 90.5t-43 111.5t-15.5 130q0 105 34 188q34 85 82 134.5t96 76.5l55 -71q-69 -51 -105.5 -132t-36.5 -196t36.5 -196t105.5 -132z" />
    <glyph glyph-name="parenright" unicode=")" horiz-adv-x="315" 
d="M65 -65l-55 71q69 51 105.5 132t36.5 196t-36.5 196t-105.5 132l55 71q40 -22 72.5 -52t57.5 -70q46 -73 64 -142.5t18 -134.5q0 -105 -34 -189t-82 -133.5t-96 -76.5z" />
    <glyph glyph-name="asterisk" unicode="*" horiz-adv-x="432" 
d="M167 339v105l-91 -61l-48 86l103 66l-105 68l54 81l87 -59v100h94v-100l91 59l55 -81l-105 -67l102 -67l-49 -86l-94 61v-105h-94z" />
    <glyph glyph-name="plus" unicode="+" 
d="M204 77v198h-182v110h182v201h104v-201h182v-110h-182v-198h-104z" />
    <glyph glyph-name="comma" unicode="," horiz-adv-x="204" 
d="M71 -120l-33 45q17 14 32 33.5t17 36.5q-24 2 -42.5 22.5t-18.5 50.5q0 28 18 49.5t52 21.5q35 0 54 -25t19 -64q0 -37 -22 -82.5t-76 -87.5z" />
    <glyph glyph-name="hyphen" unicode="-" horiz-adv-x="314" 
d="M36 201v119h241v-119h-241z" />
    <glyph glyph-name="period" unicode="." horiz-adv-x="211" 
d="M106 -11q-35 0 -52.5 22.5t-17.5 49.5q0 28 17.5 50.5t52.5 22.5t52 -22.5t17 -50.5q0 -27 -17 -49.5t-52 -22.5z" />
    <glyph glyph-name="slash" unicode="/" horiz-adv-x="516" 
d="M96 -32l-96 57l422 741l94 -53z" />
    <glyph glyph-name="zero" unicode="0" 
d="M257 -14q-68 0 -113.5 31.5t-73.5 81t-40 110.5t-12 121q0 99 29 183q30 82 86.5 121.5t123.5 39.5q92 0 149 -64q58 -61 73 -142q16 -74 16 -138q0 -60 -12 -121t-40 -110.5t-73.5 -81t-112.5 -31.5zM257 101q32 -1 53.5 22t34 58t17 75.5t3.5 74.5q0 60 -9 103
t-24 70.5t-34.5 41t-40.5 13.5q-45 1 -73 -45t-32 -97q-2 -26 -3.5 -47.5t-0.5 -38.5q0 -48 6.5 -89.5t19.5 -73t33.5 -49.5t49.5 -18z" />
    <glyph glyph-name="one" unicode="1" 
d="M241 0v508l-119 -76l-59 97l209 133h99v-662h-130z" />
    <glyph glyph-name="two" unicode="2" 
d="M41 0v79q-2 48 18.5 93t58.5 70l139 107q40 28 55 54t15 64q0 42 -25 65.5t-70 23.5q-28 0 -51.5 -9.5t-42 -22t-31 -25l-18.5 -18.5l-61 96q23 26 67.5 51.5t85.5 35.5t70 10q71 0 120 -31q51 -31 70.5 -76.5t19.5 -90.5q0 -65 -24.5 -109.5t-77.5 -83.5l-100 -74
q-52 -39 -68.5 -55.5t-16.5 -37.5h295v-116h-428z" />
    <glyph glyph-name="three" unicode="3" 
d="M236 -14q-72 0 -127 21.5t-94 55.5l56 105q35 -31 75 -50t88 -19q61 0 88.5 31t27.5 76q0 60 -38.5 85.5t-102.5 25.5q-29 0 -57 -4v82q20 35 34 52q20 26 48 56q20 20 33.5 32.5t18.5 17.5h-255v114h417v-111q-39 -27 -72 -55q-42 -34 -61.5 -56.5t-27.5 -35.5
q68 2 115 -32q41 -30 59 -73t18 -92q0 -57 -19.5 -99t-52.5 -70.5t-77.5 -42.5t-93.5 -14z" />
    <glyph glyph-name="four" unicode="4" 
d="M303 0v130h-277v99l299 438h103v-422h65v-115h-65v-130h-125zM162 245h141v214z" />
    <glyph glyph-name="five" unicode="5" 
d="M223 -14q-55 0 -100 12.5t-69 26t-37 26.5l52 111q35 -27 72.5 -43.5t82.5 -16.5q31 -1 54.5 7.5t39 24.5t23 37t6.5 45q0 61 -38 89t-110 28q-44 0 -123 -17v351h370v-114h-257v-111q8 2 29 2q121 0 186.5 -54.5t65.5 -169.5q0 -91 -42 -146q-41 -56 -98 -72
q-52 -16 -107 -16z" />
    <glyph glyph-name="six" unicode="6" 
d="M262 -14q-64 0 -120 35q-54 35 -77 95t-23 128q0 67 15.5 130t50.5 123q19 34 44 60.5t56 46.5q63 40 121.5 51.5t104.5 11.5l29 -100h-7q-92 0 -151 -32q-58 -31 -79 -69q-20 -38 -27 -60q19 12 41.5 19t50.5 7q49 1 85.5 -20.5t60 -55.5t33 -74t9.5 -76
q0 -41 -13.5 -80.5t-40.5 -70.5t-68 -50t-95 -19zM264 101q38 0 61 28t23 76q0 54 -24.5 84.5t-63.5 30.5q-23 0 -44.5 -7.5t-34.5 -15.5q-6 -18 -6 -65q0 -51 15 -84q15 -32 36 -39.5t38 -7.5z" />
    <glyph glyph-name="seven" unicode="7" 
d="M101 0q20 140 80.5 277t157.5 276h-298v114h441v-109q-101 -141 -162 -276.5t-85 -281.5h-134z" />
    <glyph glyph-name="eight" unicode="8" 
d="M257 -14q-52 0 -92.5 17t-68.5 45.5t-42.5 64.5t-14.5 74q0 33 14 69q15 36 33.5 56t34.5 30q-38 29 -55 67.5t-17 74.5q0 56 33 103q33 48 79.5 67.5t95.5 19.5q68 0 118 -33q37 -24 58 -58.5t26.5 -58.5t5.5 -38q0 -36 -18 -76.5t-54 -66.5q28 -17 47.5 -45t26.5 -58.5
t7 -51.5q0 -38 -14.5 -74t-42 -64.5t-68 -46t-92.5 -17.5zM257 99q45 0 68.5 28t23.5 66t-24 66t-69 28q-43 0 -67 -27.5t-24 -64.5q-1 -22 7 -40.5t21.5 -31.5t30 -19.5t33.5 -4.5zM257 391q30 0 50.5 22t26 42t4.5 27q0 28 -16.5 49t-35.5 28t-29 7q-25 0 -48 -17
q-21 -16 -27 -34.5t-6 -33.5q-1 -19 5.5 -36t18 -30t26.5 -19.5t31 -4.5z" />
    <glyph glyph-name="nine" unicode="9" 
d="M116 -11q-24 0 -36 2l13 107q9 -2 21 -2q85 0 134 44t68 125q-17 -12 -42 -19t-50 -7q-46 0 -81 16t-59 45t-36 68t-12 86q0 69 32 122q32 54 81.5 76t103.5 22q75 0 128 -44.5t71 -106.5q10 -31 13.5 -59t3.5 -54q0 -89 -23 -165.5t-67.5 -133.5t-110.5 -89.5
t-152 -32.5zM255 351q22 0 41.5 4.5t37.5 15.5q6 30 6 55q0 57 -20 87.5t-40 38t-28 7.5q-34 0 -55 -23t-25 -45.5t-4 -35.5q0 -45 22.5 -74.5t64.5 -29.5z" />
    <glyph glyph-name="colon" unicode=":" horiz-adv-x="211" 
d="M106 344q-35 0 -52.5 22.5t-17.5 49.5q0 28 17.5 50.5t52.5 22.5t52 -22.5t17 -50.5q0 -27 -17 -49.5t-52 -22.5zM106 -11q-35 0 -52.5 22.5t-17.5 49.5q0 28 17.5 50.5t52.5 22.5t52 -22.5t17 -50.5q0 -27 -17 -49.5t-52 -22.5z" />
    <glyph glyph-name="semicolon" unicode=";" horiz-adv-x="207" 
d="M71 -120l-33 45q17 14 32 33.5t17 36.5q-24 2 -42.5 22.5t-18.5 50.5q0 28 18 49.5t52 21.5q35 0 54 -25t19 -64q0 -37 -22 -82.5t-76 -87.5zM106 344q-35 0 -52.5 22.5t-17.5 49.5q0 28 17.5 50.5t52.5 22.5t52 -22.5t17 -50.5q0 -27 -17 -49.5t-52 -22.5z" />
    <glyph glyph-name="less" unicode="&#x3c;" 
d="M466 129l-444 162v83l431 153l37 -108l-250 -85l260 -94z" />
    <glyph glyph-name="equal" unicode="=" 
d="M22 155v110h468v-110h-468zM22 370v110h468v-110h-468z" />
    <glyph glyph-name="greater" unicode="&#x3e;" 
d="M47 129l-34 111l260 94l-250 85l38 108l430 -153v-83z" />
    <glyph glyph-name="question" unicode="?" horiz-adv-x="401" 
d="M190 -11q-34 0 -55.5 24.5t-21.5 56.5t21.5 56.5t55.5 24.5q35 0 56.5 -24.5t21.5 -56.5t-21.5 -56.5t-56.5 -24.5zM130 211v42q0 41 19.5 73t59.5 72q24 24 42.5 48.5t18.5 59.5q0 31 -20 49t-57 18q-27 0 -58 -18.5t-64 -60.5l-76 78q28 38 67 62q42 27 77.5 37
t65.5 10q72 0 118 -32q33 -26 49 -52.5t20 -50.5t4 -37q0 -48 -21 -86t-60 -80q-64 -67 -64 -108v-24h-121z" />
    <glyph glyph-name="at" unicode="@" horiz-adv-x="721" 
d="M432 -157q-122 0 -201.5 43t-120.5 111q-40 69 -51 128.5t-11 108.5q0 130 51 231q53 103 133 143q82 43 170 43q103 0 163 -42q61 -40 77 -94q17 -56 17 -92v-389h-114v24q-20 -17 -47.5 -25.5t-51.5 -8.5q-85 0 -133 56.5t-48 156.5q0 71 29 121q22 36 51.5 55t56 25
t40.5 6q35 0 60 -11t42 -21q0 60 -34 97.5t-113 37.5q-60 0 -104 -26.5t-72.5 -70t-42.5 -100t-14 -116.5q0 -26 4 -57t15 -62t30.5 -60.5t50.5 -52t75 -36t103 -13.5q75 0 132.5 9.5t100.5 25.5l26 -109q-39 -14 -111 -25t-158 -11zM459 127q28 0 48 7.5t37 25.5v152
q-50 29 -86 29q-79 0 -79 -100q0 -58 22.5 -86t57.5 -28z" />
    <glyph glyph-name="A" unicode="A" horiz-adv-x="608" 
d="M0 0l240 667h127l241 -667h-131l-46 134h-255l-45 -134h-131zM215 247h178l-89 263z" />
    <glyph glyph-name="B" unicode="B" horiz-adv-x="568" 
d="M73 0v667h226q73 0 126 -33q51 -32 68 -75q15 -41 15 -72q0 -45 -20 -78t-57 -56q51 -19 82 -60.5t31 -104.5q0 -53 -28.5 -101t-86.5 -71q-21 -8 -46.5 -12t-49.5 -4h-260zM200 114h91q33 0 52 3t31 10q21 12 30.5 29.5t9.5 36.5q0 22 -12 43t-35 34q-15 8 -35 11.5
t-43 3.5h-89v-171zM200 394h62q54 0 71 9q24 12 35 30.5t11 38.5q0 24 -15 44.5t-44 30.5q-2 0 -9.5 1t-17.5 2t-21 2t-20 1h-52v-159z" />
    <glyph glyph-name="C" unicode="C" horiz-adv-x="571" 
d="M330 -14q-63 0 -115.5 22t-91 65.5t-59.5 108t-21 149.5q0 90 24.5 155.5t64.5 109t91.5 64.5t105.5 21q68 0 119 -25t74 -53t29 -40l-94 -79q-5 9 -14.5 22.5t-25 26t-37 21.5t-49.5 9q-32 0 -60.5 -13.5t-50.5 -42t-34.5 -72t-12.5 -104.5q0 -109 47 -167.5t125 -58.5
q57 0 98 19.5t59 37.5l41 -111q-23 -27 -92.5 -46t-120.5 -19z" />
    <glyph glyph-name="D" unicode="D" horiz-adv-x="628" 
d="M73 0v667h188q36 0 80 -6t84 -24q63 -31 102 -92q40 -60 50 -117q10 -67 10 -91q0 -87 -34.5 -161.5t-71 -104.5t-57.5 -37q-68 -34 -158 -34h-193zM201 120h52q70 0 114 25q46 25 68 76.5t22 109.5q0 57 -20 107.5t-62 78.5q-22 15 -54.5 22.5t-64.5 7.5h-55v-427z" />
    <glyph glyph-name="E" unicode="E" horiz-adv-x="557" 
d="M73 0v667h435v-121h-307v-152h254v-121h-254v-152h329v-121h-457z" />
    <glyph glyph-name="F" unicode="F" horiz-adv-x="509" 
d="M73 0v667h431v-121h-303v-158h242v-119h-242v-269h-128z" />
    <glyph glyph-name="G" unicode="G" horiz-adv-x="608" 
d="M338 -14q-65 0 -119.5 23t-93.5 67.5t-60.5 108.5t-21.5 146q0 90 23 155.5t62 109t89.5 64.5t106.5 21q88 0 142 -32.5t82 -83.5l-88 -81q-19 33 -54 56t-81 23q-53 1 -90 -39q-38 -39 -51 -94q-7 -28 -9.5 -52.5t-1.5 -46.5q0 -76 27 -133q15 -32 34.5 -50.5t40 -28
t39 -12t31.5 -2.5q40 0 64.5 4.5t28.5 6.5v120h-104v115h224v-321q-25 -16 -82 -30t-138 -14z" />
    <glyph glyph-name="H" unicode="H" horiz-adv-x="655" 
d="M73 0v667h128v-264h252v264h128v-667h-128v282h-252v-282h-128z" />
    <glyph glyph-name="I" unicode="I" horiz-adv-x="275" 
d="M73 0v667h128v-667h-128z" />
    <glyph glyph-name="J" unicode="J" horiz-adv-x="425" 
d="M171 -14q-53 0 -93 14.5t-58 25.5l37 107q9 -11 32.5 -20t39 -11t27.5 -2q35 0 55.5 17.5t20.5 66.5v483h128v-484q0 -100 -47.5 -148.5t-141.5 -48.5z" />
    <glyph glyph-name="K" unicode="K" horiz-adv-x="589" 
d="M73 0v667h128v-269h67q19 0 33 9.5t29 44.5l95 215h135l-105 -233q-14 -30 -27 -46t-32 -27q23 -12 37.5 -30t25.5 -47l113 -284h-139l-82 208q-16 40 -31 59t-44 19h-75v-286h-128z" />
    <glyph glyph-name="L" unicode="L" horiz-adv-x="531" 
d="M73 0v667h128v-546h310v-121h-438z" />
    <glyph glyph-name="M" unicode="M" horiz-adv-x="750" 
d="M340 -4l-138 401l-18 -397h-127l32 667h131l163 -474l146 474h132l32 -667h-127l-18 393l-123 -397h-85z" />
    <glyph glyph-name="N" unicode="N" horiz-adv-x="660" 
d="M73 0v667h131l257 -416v416h126v-667h-112l-275 441v-441h-127z" />
    <glyph glyph-name="O" unicode="O" horiz-adv-x="672" 
d="M334 -14q-73 0 -127.5 30.5t-91 79.5t-54.5 111.5t-18 126.5t18 126.5t54.5 111.5t91 79t127.5 30q102 0 173 -60t96 -142q26 -86 26 -145q0 -64 -18.5 -126.5t-55 -111.5t-92 -79.5t-129.5 -30.5zM334 105q40 0 74 21.5t51 51t25 57.5q15 55 15 99q0 66 -24 123
q-23 57 -62 81.5t-79 24.5q-39 0 -69.5 -19t-51 -51t-31 -73.5t-10.5 -85.5t10.5 -85.5t31 -73.5t51 -51t69.5 -19z" />
    <glyph glyph-name="P" unicode="P" horiz-adv-x="549" 
d="M73 0v667h200q48 0 89 -6.5t79 -25.5q48 -24 71 -71.5t23 -101.5q0 -53 -23 -101.5t-49.5 -70.5t-39.5 -25q-34 -14 -68 -19.5t-69 -5.5h-87v-240h-126zM199 355h71q27 0 49.5 3t38.5 11q27 13 39 36t12 49q0 28 -13.5 52t-40.5 34q-35 12 -80 12h-76v-197z" />
    <glyph glyph-name="Q" unicode="Q" horiz-adv-x="682" 
d="M629 -30q-5 9 -30 36.5t-64 64.5q-36 -39 -85.5 -62t-115.5 -23q-73 0 -127.5 30.5t-91 79.5t-54.5 111.5t-18 126.5t18 126.5t54.5 111.5t91 79t127.5 30q102 0 173 -60t96 -142q26 -86 26 -145q0 -45 -9 -89t-27 -84q48 -42 75.5 -74t33.5 -42zM334 105q59 0 101 42
q-35 23 -104 53l40 94q32 -11 61.5 -26t52.5 -31q14 48 14 97q0 66 -24 123q-23 57 -62 81.5t-79 24.5q-39 0 -69.5 -19t-51 -51t-31 -73.5t-10.5 -85.5t10.5 -85.5t31 -73.5t51 -51t69.5 -19z" />
    <glyph glyph-name="R" unicode="R" horiz-adv-x="588" 
d="M73 0v667h233q74 0 131 -31t76 -77q17 -44 17 -90q0 -51 -23 -94t-71 -72q28 -14 45 -40t34 -81l50 -182h-133l-50 167q-12 33 -22 54q-7 15 -24.5 22t-39.5 7h-97v-250h-126zM199 365h90q114 0 114 91q0 48 -27 71.5t-87 23.5h-90v-186z" />
    <glyph glyph-name="S" unicode="S" horiz-adv-x="521" 
d="M257 -14q-45 0 -83 8t-67 18.5t-48.5 21.5t-28.5 18l54 111q9 -8 25 -19t37.5 -21t49 -16.5t60.5 -6.5q48 0 75.5 21t27.5 56q0 42 -27 65q-29 24 -118 55q-74 21 -117 56q-30 25 -44.5 54t-15.5 50.5t0 37.5q0 63 31 106q31 45 84 62.5t108 17.5q54 0 99.5 -15t76.5 -37
t46 -39l-67 -89q-19 21 -57 39t-64 24.5t-39 5.5q-52 0 -73 -20.5t-21 -49.5q0 -35 24 -53q13 -9 38.5 -21t65.5 -26q89 -31 133 -69q44 -37 53 -75q8 -35 11 -74q0 -68 -41 -118q-41 -48 -94 -63t-94 -15z" />
    <glyph glyph-name="T" unicode="T" horiz-adv-x="516" 
d="M194 0v546h-189v121h506v-121h-189v-546h-128z" />
    <glyph glyph-name="U" unicode="U" horiz-adv-x="653" 
d="M326 -14q-85 0 -146 37q-62 38 -86.5 93.5t-24.5 112.5v438h128v-425q0 -60 33.5 -98.5t96.5 -38.5t95.5 38.5t32.5 98.5v425h128v-438q0 -57 -20 -102t-55 -76.5t-81.5 -48t-100.5 -16.5z" />
    <glyph glyph-name="V" unicode="V" horiz-adv-x="598" 
d="M246 0l-246 667h137l169 -505l166 505h126l-233 -667h-119z" />
    <glyph glyph-name="W" unicode="W" horiz-adv-x="759" 
d="M136 0l-126 667h127l76 -450l108 450h117l113 -450l75 450h123l-127 -667h-127l-118 480l-113 -480h-128z" />
    <glyph glyph-name="X" unicode="X" horiz-adv-x="550" 
d="M0 0l206 347l-192 320h145l116 -203l115 203h145l-191 -320l206 -347h-143l-132 230l-132 -230h-143z" />
    <glyph glyph-name="Y" unicode="Y" horiz-adv-x="552" 
d="M214 0v268l-214 399h143l137 -265l131 265h141l-210 -399v-268h-128z" />
    <glyph glyph-name="Z" unicode="Z" horiz-adv-x="538" 
d="M25 0v110l323 436h-296v121h453v-114l-320 -432h326v-121h-486z" />
    <glyph glyph-name="bracketleft" unicode="[" horiz-adv-x="300" 
d="M63 -143v879h222v-110h-104v-659h104v-110h-222z" />
    <glyph glyph-name="backslash" unicode="\" horiz-adv-x="516" 
d="M420 -32l-420 745l94 53l422 -741z" />
    <glyph glyph-name="bracketright" unicode="]" horiz-adv-x="300" 
d="M15 -143v110h105v659h-105v110h222v-879h-222z" />
    <glyph glyph-name="asciicircum" unicode="^" horiz-adv-x="524" 
d="M173 283l-115 39l157 345h92l160 -330l-111 -45l-95 213z" />
    <glyph glyph-name="underscore" unicode="_" horiz-adv-x="500" 
d="M0 -220v70h500v-70h-500z" />
    <glyph glyph-name="grave" unicode="`" horiz-adv-x="420" 
d="M302 538l-217 112l59 83l191 -141z" />
    <glyph glyph-name="a" unicode="a" horiz-adv-x="488" 
d="M191 -11q-31 0 -59.5 10.5t-50.5 30t-35.5 48t-13.5 64.5q0 39 16.5 67.5t43 48t57 30t55.5 13.5q14 2 100 12v7q0 40 -20.5 57t-56.5 17t-69 -15.5t-50 -30.5l-41 87q21 21 78 40q60 19 103 19q46 0 80.5 -14t57 -39.5t33 -59.5t10.5 -71v-310h-114l-5 36
q-20 -22 -51 -34.5t-68 -12.5zM220 92q24 0 46.5 10t38.5 25v98q-61 -7 -81 -10q-40 -8 -55.5 -27.5t-15.5 -42.5q0 -24 17.5 -38.5t49.5 -14.5z" />
    <glyph glyph-name="b" unicode="b" horiz-adv-x="525" 
d="M243 -11q-48 0 -92 11t-89 30v637h124v-184q49 11 81 11q83 0 135 -43q53 -44 71 -99q19 -53 19 -102q0 -70 -20 -119.5t-54 -81t-79.5 -46t-95.5 -14.5zM238 94q60 0 96.5 39t36.5 114q0 71 -36 106.5t-88 35.5q-25 0 -61 -11v-275q39 -9 52 -9z" />
    <glyph glyph-name="c" unicode="c" horiz-adv-x="463" 
d="M257 -11q-44 0 -84.5 15t-71.5 46t-49.5 78t-18.5 111q0 90 36 152q37 61 89 82t96 21q76 0 122 -36t67 -77l-92 -62q-15 26 -37.5 48.5t-58.5 22.5q-40 0 -69.5 -35.5t-29.5 -110.5q0 -72 30 -110t87 -38q51 0 81.5 15.5t46.5 31.5l36 -102q-27 -20 -73.5 -36
t-106.5 -16z" />
    <glyph glyph-name="d" unicode="d" horiz-adv-x="529" 
d="M238 -11q-50 0 -88 21t-64 56t-39.5 81t-13.5 96q0 77 31 135q23 47 63.5 73.5t77 35t57.5 7.5q35 0 82 -14v187h123v-667h-113l-5 37q-17 -19 -45 -33.5t-66 -14.5zM263 95q23 0 47 12.5t34 29.5v238q-39 14 -67 14q-52 0 -83.5 -33.5t-36.5 -65.5t-3 -44
q0 -30 6.5 -57.5t20 -48t34 -33t48.5 -12.5z" />
    <glyph glyph-name="e" unicode="e" horiz-adv-x="499" 
d="M267 -11q-47 0 -89.5 15t-74.5 46.5t-51 78.5t-19 112q0 61 17.5 108t48 79t71 49t86.5 17q81 0 132 -45q52 -45 67 -106q15 -60 15 -111v-22h-312q4 -61 38 -88t88 -27q48 0 84 15t49 28l33 -101q-22 -16 -42 -23q-12 -6 -59 -15q-46 -10 -82 -10zM161 299h183
q-3 41 -23.5 63t-39.5 27.5t-25 5.5q-32 0 -61 -24t-34 -72z" />
    <glyph glyph-name="f" unicode="f" horiz-adv-x="318" 
d="M79 0v382h-64v100h64v26q0 70 29 111q28 42 66 51q36 10 64 10q45 0 74.5 -8.5t34.5 -11.5l-14 -103q-6 3 -23.5 8.5t-46.5 5.5q-26 0 -43.5 -15.5t-17.5 -50.5v-23h104v-100h-103v-382h-124z" />
    <glyph glyph-name="g" unicode="g" horiz-adv-x="526" 
d="M246 -198q-32 0 -61.5 3.5t-57.5 10.5q-52 13 -70 22l26 109q25 -14 66 -26t87 -12q54 0 79 20t25 52v28q-35 -9 -68 -9q-59 0 -103.5 19.5t-74.5 53t-45.5 78.5t-15.5 96q0 79 36 138q37 60 96.5 84.5t126.5 24.5q52 0 99 -5.5t74 -10.5v-466q0 -103 -56.5 -156.5
t-162.5 -53.5zM290 107q18 0 29.5 4t20.5 9v266q-26 4 -47 4q-59 0 -93 -30q-21 -19 -32.5 -43t-12.5 -42t-1 -28q0 -63 33.5 -101.5t102.5 -38.5z" />
    <glyph glyph-name="h" unicode="h" horiz-adv-x="548" 
d="M62 0v667h124v-217q23 18 54.5 31t76.5 13q70 1 111.5 -37t50.5 -87q10 -47 10 -84v-286h-124v277q0 60 -23.5 86.5t-53.5 26.5t-54.5 -13.5t-47.5 -33.5v-343h-124z" />
    <glyph glyph-name="i" unicode="i" horiz-adv-x="247" 
d="M62 0v482h123v-482h-123zM124 527q-38 0 -56.5 22t-18.5 49q0 26 18.5 48t56.5 22q37 0 55.5 -22t18.5 -48q0 -27 -18.5 -49t-55.5 -22z" />
    <glyph glyph-name="j" unicode="j" horiz-adv-x="257" 
d="M51 -203l-55 97q42 28 58.5 57t16.5 68v463h124v-463q0 -83 -34.5 -135t-109.5 -87zM134 527q-37 0 -55.5 22t-18.5 49q0 26 18.5 48t55.5 22q36 0 54.5 -22t18.5 -48q0 -27 -18.5 -49t-54.5 -22z" />
    <glyph glyph-name="k" unicode="k" horiz-adv-x="500" 
d="M62 0v667h124v-348h30q18 0 32 10.5t29 45.5l44 107h134l-58 -135q-20 -45 -56 -68q22 -11 36.5 -26.5t24.5 -39.5l88 -213h-135l-55 130q-19 45 -33.5 63t-41.5 18h-39v-211h-124z" />
    <glyph glyph-name="l" unicode="l" horiz-adv-x="308" 
d="M190 -11q-41 0 -66.5 12.5t-39.5 32t-18 42.5t-4 41v550h124v-526q0 -26 10.5 -35.5t29.5 -9.5t40 9l22 -94q-4 -4 -36 -13q-30 -9 -62 -9z" />
    <glyph glyph-name="m" unicode="m" horiz-adv-x="837" 
d="M62 0v482h114l5 -36q22 20 54.5 34t65.5 14q34 0 67.5 -10.5t62.5 -36.5q33 20 72.5 33.5t84.5 13.5q82 0 126 -36q45 -38 54 -85q10 -46 10 -84v-289h-123v283q0 48 -18 77.5t-67 29.5q-24 0 -51.5 -13t-43.5 -26q6 -36 6 -60v-291h-124v283q0 54 -20 80.5t-56 26.5
q-28 0 -53 -15t-42 -32v-343h-124z" />
    <glyph glyph-name="n" unicode="n" horiz-adv-x="552" 
d="M62 0v482h113l6 -38q29 24 65.5 37t73.5 13q58 0 99 -26t57.5 -76.5t16.5 -106.5v-285h-124v275q0 55 -22.5 85t-64.5 30q-29 0 -53.5 -15t-42.5 -33v-342h-124z" />
    <glyph glyph-name="o" unicode="o" horiz-adv-x="528" 
d="M264 -11q-58 0 -101.5 22t-72 58t-43 81.5t-14.5 92.5q0 73 32 135q33 62 87 89t112 27q81 0 137 -44q55 -43 74 -102q20 -62 20 -105q0 -73 -33 -136q-25 -49 -65.5 -77t-75.5 -34.5t-57 -6.5zM264 96q32 -2 54 15.5t34.5 41.5t17.5 49.5t3 40.5q0 54 -22.5 91t-48 46.5
t-38.5 8.5q-42 0 -70 -30q-23 -25 -31 -55.5t-8 -60.5q0 -31 7 -57.5t20.5 -46.5t34 -31.5t47.5 -11.5z" />
    <glyph glyph-name="p" unicode="p" horiz-adv-x="526" 
d="M62 -189v671h113l6 -36q17 17 45.5 32.5t67.5 15.5q69 0 117 -42t65 -102q17 -62 17 -108q0 -76 -34 -138.5t-90 -88.5t-119 -26q-30 0 -64 14v-192h-124zM243 97q37 -1 62.5 14t40.5 37t21 47t5 45q1 44 -17 83q-17 39 -43 54t-44 13q-26 0 -49 -14.5t-34 -31.5v-233
q33 -14 58 -14z" />
    <glyph glyph-name="q" unicode="q" horiz-adv-x="526" 
d="M340 -189v187q-35 -9 -68 -9q-59 0 -103.5 20.5t-74.5 55t-45.5 80.5t-15.5 97q0 76 35 138q36 62 96.5 88t127.5 26q37 0 80.5 -4.5t92.5 -11.5v-667h-125zM290 96q26 0 50 12v278q-26 4 -47 4q-41 1 -68 -15t-43.5 -39t-23 -49t-4.5 -45q0 -31 8.5 -57.5t25.5 -46
t42.5 -31t59.5 -11.5z" />
    <glyph glyph-name="r" unicode="r" horiz-adv-x="356" 
d="M62 0v482h113l8 -51q17 20 37.5 32.5t42.5 20.5t39 8q39 0 54 -7l-18 -108q-13 4 -42 4q-35 0 -63 -18.5t-47 -38.5v-324h-124z" />
    <glyph glyph-name="s" unicode="s" horiz-adv-x="461" 
d="M238 -11q-26 0 -52.5 3.5t-54.5 10.5q-54 14 -76 28t-27 22l45 95q19 -20 54 -37q35 -15 63.5 -20t47.5 -5q45 0 62.5 13t17.5 35q0 19 -23 34t-89 32q-47 10 -77.5 25t-48.5 34t-24.5 42t-5.5 49q0 64 47.5 104t144.5 40q63 0 113 -18q51 -17 71 -34l-42 -90
q-19 15 -56 31t-85 16q-44 0 -59.5 -14t-15.5 -34q0 -24 18 -34q19 -10 80 -22q74 -13 113 -44t47 -61t8 -50q0 -60 -37 -97q-38 -36 -84.5 -45t-74.5 -9z" />
    <glyph glyph-name="t" unicode="t" horiz-adv-x="350" 
d="M208 -11q-64 0 -95 36t-31 87v270h-68v100h68v125h123v-125h111v-100h-111v-245q0 -23 11 -32t26 -9q37 0 64 21l32 -89q-23 -17 -57.5 -28t-72.5 -11z" />
    <glyph glyph-name="u" unicode="u" horiz-adv-x="550" 
d="M232 -10q-85 0 -129 51t-44 154v287h124v-274q0 -50 18 -81t62 -31q36 0 61 14.5t40 27.5v344h124v-482h-114l-4 38q-33 -28 -68.5 -38t-69.5 -10z" />
    <glyph glyph-name="v" unicode="v" horiz-adv-x="506" 
d="M202 0l-202 482h132l126 -320l122 320h126l-196 -482h-108z" />
    <glyph glyph-name="w" unicode="w" horiz-adv-x="681" 
d="M158 0l-158 482h129l83 -305l78 305h100l79 -305l83 305h129l-158 -482h-100l-83 314l-81 -314h-101z" />
    <glyph glyph-name="x" unicode="x" horiz-adv-x="485" 
d="M3 0l172 238l-175 244h140l105 -149l106 149h127l-167 -235l174 -247h-139l-107 152l-105 -152h-131z" />
    <glyph glyph-name="y" unicode="y" horiz-adv-x="503" 
d="M37 -191l-11 111q54 0 96.5 13t65.5 42q4 5 7 11t7 12l-198 484h133l134 -370q43 114 58 171q38 140 52 199h122q-37 -147 -78.5 -266t-83.5 -204q-40 -76 -72 -118q-32 -39 -93 -61.5t-139 -23.5z" />
    <glyph glyph-name="z" unicode="z" horiz-adv-x="483" 
d="M34 0v89l262 293h-251v100h394v-94l-258 -289h266v-99h-413z" />
    <glyph glyph-name="braceleft" unicode="{" horiz-adv-x="279" 
d="M255 -62q-85 1 -124 37.5t-39 112.5v128q0 37 -18.5 56t-58.5 25v74q40 6 58.5 25t18.5 56v129q0 76 39 112t124 38l9 -87q-42 -2 -53 -22.5t-11 -61.5v-108q0 -90 -64 -118q33 -17 48.5 -44.5t15.5 -73.5v-110q0 -17 2 -31.5t8.5 -25.5t19 -17.5t34.5 -7.5z" />
    <glyph glyph-name="bar" unicode="|" horiz-adv-x="248" 
d="M67 -74v812h113v-812h-113z" />
    <glyph glyph-name="braceright" unicode="}" horiz-adv-x="275" 
d="M20 -62l-10 86q39 1 52 20.5t13 61.5v110q0 46 15.5 73.5t48.5 44.5q-64 28 -64 118v108q1 46 -14 62.5t-51 21.5l10 87q85 -2 124 -38t39 -112v-129q0 -37 18.5 -56t58.5 -25v-74q-40 -6 -58.5 -25t-18.5 -56v-128q0 -149 -163 -150z" />
    <glyph glyph-name="asciitilde" unicode="~" horiz-adv-x="428" 
d="M301 503q-50 0 -90 27q-40 28 -63 28q-18 0 -30 -11.5t-16 -40.5h-104q9 72 44 108t90 36q30 0 51.5 -8.5t38 -18.5t30 -18.5t27.5 -8.5q15 0 28.5 10t18.5 46h103q-3 -59 -25 -94t-50.5 -45t-52.5 -10z" />
    <glyph glyph-name="uni00A0" unicode="&#xa0;" horiz-adv-x="190" 
 />
    <glyph glyph-name="exclamdown" unicode="&#xa1;" horiz-adv-x="241" 
d="M122 492q34 0 55.5 -24.5t21.5 -56.5t-21.5 -56.5t-55.5 -24.5q-35 0 -56.5 24.5t-21.5 56.5t21.5 56.5t56.5 24.5zM186 270v-456h-126v456h126z" />
    <glyph glyph-name="cent" unicode="&#xa2;" 
d="M238 -11v147q-62 9 -102.5 52.5t-48.5 83.5t-8 63q0 41 12.5 75t35 59.5t51.5 40.5t60 18v139h80v-138q47 -8 109 -37l-36 -90q-35 17 -59.5 23t-47.5 6q-36 0 -58 -22t-26 -43t-4 -32q0 -17 5.5 -35t17 -33t28 -24.5t37.5 -9.5q45 0 102 30l40 -89q-32 -19 -58 -27.5
t-50 -11.5v-145h-80z" />
    <glyph glyph-name="sterling" unicode="&#xa3;" 
d="M96 0v246h-67v109h67v104q0 75 33 128q22 36 56.5 57t64 25.5t45.5 4.5q63 0 111.5 -31t77.5 -92l-98 -70q-22 40 -44 57.5t-47 17.5q-30 0 -52.5 -22.5t-22.5 -80.5v-98h189v-109h-189v-125h260v-121h-384z" />
    <glyph glyph-name="currency" unicode="&#xa4;" horiz-adv-x="588" 
d="M108 68l-96 96l66 66q-14 26 -22 54.5t-8 60.5t8 60.5t22 54.5l-66 66l96 96l68 -67q26 13 54 20t59 7q30 0 58.5 -7.5t54.5 -20.5l68 68l96 -96l-66 -66q14 -26 22 -54.5t8 -60.5q0 -31 -8 -60t-22 -55l66 -66l-96 -96l-67 68q-26 -13 -54 -20.5t-60 -7.5q-31 0 -59 7.5
t-54 20.5zM289 226q30 -1 53 13t38 33.5t21.5 40t3.5 32.5q1 41 -23.5 70.5t-52.5 39.5t-40 9q-38 1 -71 -25q-30 -25 -38.5 -50.5t-6.5 -43.5q0 -25 9 -46.5t24.5 -37.5t37 -25.5t45.5 -9.5z" />
    <glyph glyph-name="yen" unicode="&#xa5;" horiz-adv-x="487" 
d="M185 0v237h-140v108h101l-163 317h135l131 -262l123 262h132l-160 -317h102v-108h-139v-237h-122z" />
    <glyph glyph-name="brokenbar" unicode="&#xa6;" horiz-adv-x="261" 
d="M70 -74v308h119v-308h-119zM70 429v309h119v-309h-119z" />
    <glyph glyph-name="section" unicode="&#xa7;" horiz-adv-x="467" 
d="M222 -144q-57 0 -99 18.5t-74 44.5l54 96q26 -22 55.5 -38.5t58.5 -16.5t41 14t12 31q0 16 -8.5 29t-24.5 25.5t-35.5 24.5t-38.5 26q-49 32 -76.5 68t-27.5 95q0 54 25.5 88t67.5 57q-31 28 -45 57t-14 69q0 66 44 101t116 35q48 0 88 -16t76 -52l-64 -85
q-17 18 -42.5 33.5t-53.5 15.5q-19 0 -31 -10.5t-12 -29.5q0 -26 23.5 -47.5t55.5 -44.5q34 -27 68 -56q26 -24 41 -56t15 -65q0 -48 -21.5 -81.5t-59.5 -58.5q26 -23 43 -51t17 -73q0 -51 -35 -89q-34 -37 -76 -48q-47 -10 -63 -10zM262 186q19 14 26.5 31.5t7.5 34.5
q0 21 -16 45t-58 56q-24 -11 -32.5 -30.5t-8.5 -38.5q0 -27 19.5 -49t61.5 -49z" />
    <glyph glyph-name="dieresis" unicode="&#xa8;" horiz-adv-x="442" 
d="M114 560q-35 0 -52.5 20.5t-17.5 45.5q0 24 17.5 44.5t52.5 20.5q34 0 51 -20.5t17 -44.5q0 -25 -17 -45.5t-51 -20.5zM322 560q-35 0 -52.5 20.5t-17.5 45.5q0 24 17.5 44.5t52.5 20.5q34 0 51 -20.5t17 -44.5q0 -25 -17 -45.5t-51 -20.5z" />
    <glyph glyph-name="copyright" unicode="&#xa9;" horiz-adv-x="775" 
d="M388 -77q-86 0 -150 32t-107.5 84.5t-65 119.5t-21.5 138q0 110 50 202q36 69 94.5 109t113 50.5t86.5 10.5q119 0 202 -65q84 -66 112 -151q29 -89 29 -156q0 -112 -49.5 -203t-128.5 -131t-165 -40zM388 3q90 0 152 50q61 51 84 119q22 65 22 125q0 88 -37 158
q-25 51 -72 85t-87.5 42t-61.5 8q-64 0 -112.5 -24.5t-81 -65.5t-49 -94t-16.5 -109q0 -57 16.5 -110t49.5 -94t81.5 -65.5t111.5 -24.5zM396 67q-69 0 -119 35q-50 36 -67 87.5t-17 105.5q0 83 34 138q35 57 80 74q46 18 85 18q63 0 103.5 -26t65.5 -80l-84 -46
q-15 31 -33.5 48.5t-49.5 17.5q-33 0 -60 -26q-26 -26 -32.5 -58.5t-6.5 -59.5q0 -66 30.5 -103.5t73.5 -37.5q23 0 43 12t41 50l82 -49q-32 -57 -73 -78.5t-96 -21.5z" />
    <glyph glyph-name="ordfeminine" unicode="&#xaa;" horiz-adv-x="360" 
d="M143 295q-51 0 -84.5 29t-33.5 81q0 53 40 81q42 30 87 37q46 7 73 9v10q0 20 -14 33t-34 13q-21 0 -41.5 -9t-43.5 -26l-39 63q35 27 68.5 37.5t63.5 10.5q63 0 99 -32t36 -105v-225h-86l-3 28q-41 -35 -88 -35zM165 372q20 0 35 7t26 18v65q-34 -3 -58 -7
q-29 -6 -40 -18t-11 -27q0 -38 48 -38z" />
    <glyph glyph-name="guillemotleft" unicode="&#xab;" horiz-adv-x="546" 
d="M230 45l-216 205l197 212l82 -68l-136 -138l149 -135zM447 45l-216 205l197 212l81 -68l-136 -138l150 -135z" />
    <glyph glyph-name="logicalnot" unicode="&#xac;" 
d="M380 146v260h-360v109h468v-369h-108z" />
    <glyph glyph-name="uni00AD" unicode="&#xad;" horiz-adv-x="314" 
d="M36 201v119h241v-119h-241z" />
    <glyph glyph-name="registered" unicode="&#xae;" horiz-adv-x="776" 
d="M388 -77q-86 0 -150 32t-107 84.5t-64.5 119.5t-21.5 138q0 110 50 202q36 71 96.5 110t113 49.5t84.5 10.5q118 0 201 -65q84 -66 112 -151q29 -89 29 -156q0 -112 -49.5 -203t-128.5 -131t-165 -40zM389 3q90 0 151 49q63 50 85 119q22 66 22 126q0 88 -37 158
q-25 51 -72 85t-87.5 42t-61.5 8q-92 0 -157 -53q-63 -52 -83 -118q-20 -69 -20 -122q0 -57 16.5 -110t49.5 -94t81.5 -65.5t112.5 -24.5zM245 102v406h152q81 0 120 -32.5t39 -95.5q0 -51 -23 -79.5t-58 -40.5l85 -158h-99l-77 147h-46v-147h-93zM338 334h52q38 0 54 10
t16 39q0 24 -14.5 32t-51.5 8h-56v-89z" />
    <glyph glyph-name="macron" unicode="&#xaf;" horiz-adv-x="442" 
d="M38 578v89h345v-89h-345z" />
    <glyph glyph-name="degree" unicode="&#xb0;" horiz-adv-x="396" 
d="M199 368q-46 0 -86 24t-56.5 57t-16.5 69q0 40 16.5 69t41.5 47t52.5 25.5t47.5 7.5q42 0 83 -23q40 -22 57.5 -56.5t17.5 -70.5q0 -41 -17 -70t-42 -47t-52 -25t-46 -7zM198 455q20 0 37 15.5t17 46.5q0 32 -17 47.5t-37 15.5t-37 -15.5t-17 -47.5q0 -31 17 -46.5
t37 -15.5z" />
    <glyph glyph-name="plusminus" unicode="&#xb1;" 
d="M23 0v110h468v-110h-468zM205 158v198h-182v110h182v201h104v-201h182v-110h-182v-198h-104z" />
    <glyph glyph-name="twosuperior" unicode="&#xb2;" horiz-adv-x="315" 
d="M44 337v64q0 43 35 69l72 55q29 23 29 41q0 17 -10 23t-24 6q-26 0 -47 -12t-32 -20l-34 67q11 11 53.5 27.5t75.5 16.5q54 0 86 -26.5t32 -74.5q0 -31 -14.5 -52t-40.5 -41l-86 -64h142v-79h-237z" />
    <glyph glyph-name="threesuperior" unicode="&#xb3;" horiz-adv-x="315" 
d="M154 324q-36 0 -66.5 11t-50.5 28l31 69q17 -14 37.5 -23.5t43.5 -9.5q49 0 49 39q0 20 -12.5 30t-44.5 10q-15 0 -29 -2v53q10 14 24 28.5t32 29.5h-116v78h220v-77q-46 -26 -72 -56q39 -5 64 -29t25 -66q0 -57 -38.5 -85t-96.5 -28z" />
    <glyph glyph-name="acute" unicode="&#xb4;" horiz-adv-x="442" 
d="M125 538l-33 54l191 141l59 -83z" />
    <glyph glyph-name="mu" unicode="&#xb5;" horiz-adv-x="582" 
d="M67 -189v671h124v-293q0 -42 20 -70t65 -28q32 0 56.5 14.5t35.5 41.5q7 15 7 73v262h124v-349q0 -26 6 -36t20 -10q13 0 31 7l16 -91q-36 -14 -73 -14q-76 0 -93 65q-23 -33 -56 -49t-72 -16q-29 0 -55.5 10.5t-40.5 26.5v-215h-115z" />
    <glyph glyph-name="paragraph" unicode="&#xb6;" horiz-adv-x="504" 
d="M190 -145v397q-39 4 -71 22.5t-55.5 46t-36 63t-12.5 74.5q0 59 31 111q31 54 78 76t92 22h76v-812h-102zM337 -145v812h104v-812h-104z" />
    <glyph glyph-name="periodcentered" unicode="&#xb7;" horiz-adv-x="326" 
d="M163 297q-35 0 -52.5 22.5t-17.5 49.5q0 28 17.5 50.5t52.5 22.5t52 -22.5t17 -50.5q0 -27 -17 -49.5t-52 -22.5z" />
    <glyph glyph-name="cedilla" unicode="&#xb8;" horiz-adv-x="442" 
d="M128 -181l59 137h110l-81 -137h-88z" />
    <glyph glyph-name="onesuperior" unicode="&#xb9;" horiz-adv-x="315" 
d="M154 337v218l-65 -36l-38 72l126 74h70v-328h-93z" />
    <glyph glyph-name="ordmasculine" unicode="&#xba;" horiz-adv-x="392" 
d="M196 295q-48 0 -80.5 19t-52.5 48t-27 61.5t-7 56.5q0 50 23 99q24 46 63 65.5t81 19.5q61 0 103 -37.5t53 -79.5q12 -44 12 -67q0 -52 -24 -98q-23 -46 -62.5 -66.5t-81.5 -20.5zM196 383q35 0 51.5 28.5t16.5 68.5q0 42 -16.5 70.5t-50.5 28.5q-35 0 -52 -28.5
t-17 -70.5q0 -40 16.5 -68.5t51.5 -28.5z" />
    <glyph glyph-name="guillemotright" unicode="&#xbb;" horiz-adv-x="545" 
d="M99 45l-76 76l149 135l-136 138l82 68l196 -212zM314 45l-75 76l150 135l-136 138l81 68l197 -212z" />
    <glyph glyph-name="onequarter" unicode="&#xbc;" horiz-adv-x="774" 
d="M106 337v218l-65 -36l-38 72l126 74h70v-328h-93zM594 0v71h-146v73l153 184h85v-187h39v-70h-39v-71h-92zM523 141h71v86zM101 -13l-64 71l622 610l67 -70z" />
    <glyph glyph-name="onehalf" unicode="&#xbd;" horiz-adv-x="774" 
d="M121 337v218l-65 -36l-38 72l126 74h70v-328h-93zM492 6v64q0 43 35 69l72 55q29 23 29 41q0 17 -10 23t-24 6q-26 0 -47 -12t-32 -20l-34 67q11 11 53.5 27.5t75.5 16.5q54 0 86 -26.5t32 -74.5q0 -31 -14.5 -52t-40.5 -41l-86 -64h142v-79h-237zM82 -13l-64 71l622 610
l67 -70z" />
    <glyph glyph-name="threequarters" unicode="&#xbe;" horiz-adv-x="774" 
d="M125 -13l-64 71l622 610l67 -70zM161 324q-36 0 -66.5 11t-50.5 28l31 69q17 -14 37.5 -23.5t43.5 -9.5q49 0 49 39q0 20 -12.5 30t-44.5 10q-15 0 -29 -2v53q10 14 24 28.5t32 29.5h-116v78h220v-77q-46 -26 -72 -56q39 -5 64 -29t25 -66q0 -57 -38.5 -85t-96.5 -28z
M586 0v71h-146v73l153 184h85v-187h39v-70h-39v-71h-92zM515 141h71v86z" />
    <glyph glyph-name="questiondown" unicode="&#xbf;" horiz-adv-x="422" 
d="M222 495q34 0 55.5 -24.5t21.5 -56.5t-21.5 -56.5t-55.5 -24.5q-35 0 -56.5 24.5t-21.5 56.5t21.5 56.5t56.5 24.5zM282 273v-42q0 -41 -19.5 -73t-59.5 -72q-24 -24 -42.5 -48.5t-18.5 -59.5q0 -31 20 -49t57 -18q27 0 58 18.5t64 60.5l76 -78q-28 -38 -67 -62
q-42 -27 -77.5 -37t-65.5 -10q-72 0 -118 32q-33 26 -49 52.5t-20 50.5t-4 37q0 48 21 86t60 80q64 67 64 108v24h121z" />
    <glyph glyph-name="Agrave" unicode="&#xc0;" horiz-adv-x="608" 
d="M0 0l240 667h127l241 -667h-131l-46 134h-255l-45 -134h-131zM215 247h178l-89 263zM382 704l-217 112l59 83l191 -141z" />
    <glyph glyph-name="Aacute" unicode="&#xc1;" horiz-adv-x="608" 
d="M0 0l240 667h127l241 -667h-131l-46 134h-255l-45 -134h-131zM215 247h178l-89 263zM228 704l-33 54l191 141l59 -83z" />
    <glyph glyph-name="Acircumflex" unicode="&#xc2;" horiz-adv-x="608" 
d="M0 0l240 667h127l241 -667h-131l-46 134h-255l-45 -134h-131zM215 247h178l-89 263zM209 710l-64 45l115 130h92l113 -129l-64 -45l-96 96z" />
    <glyph glyph-name="Atilde" unicode="&#xc3;" horiz-adv-x="608" 
d="M0 0l240 667h127l241 -667h-131l-46 134h-255l-45 -134h-131zM215 247h178l-89 263zM177 706l-68 38q18 51 49.5 82t73.5 31q26 0 73 -29q40 -28 56 -28q9 0 26 11.5t33 51.5l69 -37q-14 -51 -48 -82.5t-77 -31.5q-28 0 -74 30q-41 27 -55 27q-11 0 -26.5 -11.5
t-31.5 -51.5z" />
    <glyph glyph-name="Adieresis" unicode="&#xc4;" horiz-adv-x="608" 
d="M0 0l240 667h127l241 -667h-131l-46 134h-255l-45 -134h-131zM215 247h178l-89 263zM201 726q-35 0 -52.5 20.5t-17.5 45.5q0 24 17.5 44.5t52.5 20.5q34 0 51 -20.5t17 -44.5q0 -25 -17 -45.5t-51 -20.5zM409 726q-35 0 -52.5 20.5t-17.5 45.5q0 24 17.5 44.5t52.5 20.5
q34 0 51 -20.5t17 -44.5q0 -25 -17 -45.5t-51 -20.5z" />
    <glyph glyph-name="Aring" unicode="&#xc5;" horiz-adv-x="608" 
d="M0 0l240 667h127l241 -667h-131l-46 134h-255l-45 -134h-131zM215 247h178l-89 263zM305 695q-45 0 -74.5 30t-29.5 78q0 26 9.5 46.5t25 34.5t34.5 21t39 7q44 0 72.5 -31t28.5 -77q0 -22 -7.5 -42t-21.5 -35t-33.5 -23.5t-42.5 -8.5zM305 758q19 0 29.5 13t10.5 33
t-10.5 33t-29.5 13q-18 0 -28.5 -13t-10.5 -33t10.5 -33t28.5 -13z" />
    <glyph glyph-name="AE" unicode="&#xc6;" horiz-adv-x="907" 
d="M0 0l386 667h472v-121h-290v-151h236v-120h-236v-154h312v-121h-440v176h-194l-102 -176h-144zM305 290h135v239z" />
    <glyph glyph-name="Ccedilla" unicode="&#xc7;" horiz-adv-x="571" 
d="M330 -14q-63 0 -115.5 22t-91 65.5t-59.5 108t-21 149.5q0 90 24.5 155.5t64.5 109t91.5 64.5t105.5 21q68 0 119 -25t74 -53t29 -40l-94 -79q-5 9 -14.5 22.5t-25 26t-37 21.5t-49.5 9q-32 0 -60.5 -13.5t-50.5 -42t-34.5 -72t-12.5 -104.5q0 -109 47 -167.5t125 -58.5
q57 0 98 19.5t59 37.5l41 -111q-23 -27 -92.5 -46t-120.5 -19zM228 -181l59 137h110l-81 -137h-88z" />
    <glyph glyph-name="Egrave" unicode="&#xc8;" horiz-adv-x="557" 
d="M73 0v667h435v-121h-307v-152h254v-121h-254v-152h329v-121h-457zM371 704l-217 112l59 83l191 -141z" />
    <glyph glyph-name="Eacute" unicode="&#xc9;" horiz-adv-x="557" 
d="M73 0v667h435v-121h-307v-152h254v-121h-254v-152h329v-121h-457zM238 704l-33 54l191 141l59 -83z" />
    <glyph glyph-name="Ecircumflex" unicode="&#xca;" horiz-adv-x="557" 
d="M73 0v667h435v-121h-307v-152h254v-121h-254v-152h329v-121h-457zM196 710l-64 45l115 130h92l113 -129l-64 -45l-96 96z" />
    <glyph glyph-name="Edieresis" unicode="&#xcb;" horiz-adv-x="557" 
d="M73 0v667h435v-121h-307v-152h254v-121h-254v-152h329v-121h-457zM190 726q-35 0 -52.5 20.5t-17.5 45.5q0 24 17.5 44.5t52.5 20.5q34 0 51 -20.5t17 -44.5q0 -25 -17 -45.5t-51 -20.5zM398 726q-35 0 -52.5 20.5t-17.5 45.5q0 24 17.5 44.5t52.5 20.5q34 0 51 -20.5
t17 -44.5q0 -25 -17 -45.5t-51 -20.5z" />
    <glyph glyph-name="Igrave" unicode="&#xcc;" horiz-adv-x="275" 
d="M73 0v667h128v-667h-128zM225 704l-217 112l59 83l191 -141z" />
    <glyph glyph-name="Iacute" unicode="&#xcd;" horiz-adv-x="275" 
d="M73 0v667h128v-667h-128zM72 704l-33 54l191 141l59 -83z" />
    <glyph glyph-name="Icircumflex" unicode="&#xce;" horiz-adv-x="275" 
d="M73 0v667h128v-667h-128zM47 710l-64 45l115 130h92l113 -129l-64 -45l-96 96z" />
    <glyph glyph-name="Idieresis" unicode="&#xcf;" horiz-adv-x="275" 
d="M73 0v667h128v-667h-128zM44 726q-35 0 -52.5 20.5t-17.5 45.5q0 24 17.5 44.5t52.5 20.5q34 0 51 -20.5t17 -44.5q0 -25 -17 -45.5t-51 -20.5zM252 726q-35 0 -52.5 20.5t-17.5 45.5q0 24 17.5 44.5t52.5 20.5q34 0 51 -20.5t17 -44.5q0 -25 -17 -45.5t-51 -20.5z" />
    <glyph glyph-name="Eth" unicode="&#xd0;" horiz-adv-x="628" 
d="M73 0v291h-73v105h73v271h188q36 0 80 -6t84 -24q63 -31 102 -92q40 -60 50 -117q10 -67 10 -91q0 -87 -34.5 -161.5t-71 -104.5t-57.5 -37q-68 -34 -158 -34h-193zM201 120h52q70 0 114 25q46 25 68 76.5t22 109.5q0 57 -20 107.5t-62 78.5q-22 15 -54.5 22.5t-64.5 7.5
h-55v-151h147v-105h-147v-171z" />
    <glyph glyph-name="Ntilde" unicode="&#xd1;" horiz-adv-x="660" 
d="M73 0v667h131l257 -416v416h126v-667h-112l-275 441v-441h-127zM213 706l-68 38q18 51 49.5 82t73.5 31q26 0 73 -29q40 -28 56 -28q9 0 26 11.5t33 51.5l69 -37q-14 -51 -48 -82.5t-77 -31.5q-28 0 -74 30q-41 27 -55 27q-11 0 -26.5 -11.5t-31.5 -51.5z" />
    <glyph glyph-name="Ograve" unicode="&#xd2;" horiz-adv-x="672" 
d="M334 -14q-73 0 -127.5 30.5t-91 79.5t-54.5 111.5t-18 126.5t18 126.5t54.5 111.5t91 79t127.5 30q102 0 173 -60t96 -142q26 -86 26 -145q0 -64 -18.5 -126.5t-55 -111.5t-92 -79.5t-129.5 -30.5zM334 105q40 0 74 21.5t51 51t25 57.5q15 55 15 99q0 66 -24 123
q-23 57 -62 81.5t-79 24.5q-39 0 -69.5 -19t-51 -51t-31 -73.5t-10.5 -85.5t10.5 -85.5t31 -73.5t51 -51t69.5 -19zM423 704l-217 112l59 83l191 -141z" />
    <glyph glyph-name="Oacute" unicode="&#xd3;" horiz-adv-x="672" 
d="M334 -14q-73 0 -127.5 30.5t-91 79.5t-54.5 111.5t-18 126.5t18 126.5t54.5 111.5t91 79t127.5 30q102 0 173 -60t96 -142q26 -86 26 -145q0 -64 -18.5 -126.5t-55 -111.5t-92 -79.5t-129.5 -30.5zM334 105q40 0 74 21.5t51 51t25 57.5q15 55 15 99q0 66 -24 123
q-23 57 -62 81.5t-79 24.5q-39 0 -69.5 -19t-51 -51t-31 -73.5t-10.5 -85.5t10.5 -85.5t31 -73.5t51 -51t69.5 -19zM279 704l-33 54l191 141l59 -83z" />
    <glyph glyph-name="Ocircumflex" unicode="&#xd4;" horiz-adv-x="672" 
d="M334 -14q-73 0 -127.5 30.5t-91 79.5t-54.5 111.5t-18 126.5t18 126.5t54.5 111.5t91 79t127.5 30q102 0 173 -60t96 -142q26 -86 26 -145q0 -64 -18.5 -126.5t-55 -111.5t-92 -79.5t-129.5 -30.5zM334 105q40 0 74 21.5t51 51t25 57.5q15 55 15 99q0 66 -24 123
q-23 57 -62 81.5t-79 24.5q-39 0 -69.5 -19t-51 -51t-31 -73.5t-10.5 -85.5t10.5 -85.5t31 -73.5t51 -51t69.5 -19zM243 710l-64 45l115 130h92l113 -129l-64 -45l-96 96z" />
    <glyph glyph-name="Otilde" unicode="&#xd5;" horiz-adv-x="672" 
d="M334 -14q-73 0 -127.5 30.5t-91 79.5t-54.5 111.5t-18 126.5t18 126.5t54.5 111.5t91 79t127.5 30q102 0 173 -60t96 -142q26 -86 26 -145q0 -64 -18.5 -126.5t-55 -111.5t-92 -79.5t-129.5 -30.5zM334 105q40 0 74 21.5t51 51t25 57.5q15 55 15 99q0 66 -24 123
q-23 57 -62 81.5t-79 24.5q-39 0 -69.5 -19t-51 -51t-31 -73.5t-10.5 -85.5t10.5 -85.5t31 -73.5t51 -51t69.5 -19zM214 706l-68 38q18 51 49.5 82t73.5 31q26 0 73 -29q40 -28 56 -28q9 0 26 11.5t33 51.5l69 -37q-14 -51 -48 -82.5t-77 -31.5q-28 0 -74 30q-41 27 -55 27
q-11 0 -26.5 -11.5t-31.5 -51.5z" />
    <glyph glyph-name="Odieresis" unicode="&#xd6;" horiz-adv-x="672" 
d="M334 -14q-73 0 -127.5 30.5t-91 79.5t-54.5 111.5t-18 126.5t18 126.5t54.5 111.5t91 79t127.5 30q102 0 173 -60t96 -142q26 -86 26 -145q0 -64 -18.5 -126.5t-55 -111.5t-92 -79.5t-129.5 -30.5zM334 105q40 0 74 21.5t51 51t25 57.5q15 55 15 99q0 66 -24 123
q-23 57 -62 81.5t-79 24.5q-39 0 -69.5 -19t-51 -51t-31 -73.5t-10.5 -85.5t10.5 -85.5t31 -73.5t51 -51t69.5 -19zM239 726q-35 0 -52.5 20.5t-17.5 45.5q0 24 17.5 44.5t52.5 20.5q34 0 51 -20.5t17 -44.5q0 -25 -17 -45.5t-51 -20.5zM447 726q-35 0 -52.5 20.5
t-17.5 45.5q0 24 17.5 44.5t52.5 20.5q34 0 51 -20.5t17 -44.5q0 -25 -17 -45.5t-51 -20.5z" />
    <glyph glyph-name="multiply" unicode="&#xd7;" horiz-adv-x="515" 
d="M136 125l-73 77l120 128l-120 128l73 77l121 -127l122 127l73 -77l-120 -128l120 -128l-73 -77l-122 127z" />
    <glyph glyph-name="Oslash" unicode="&#xd8;" horiz-adv-x="673" 
d="M112 -29l-81 73l67 80q-27 46 -40.5 99.5t-13.5 110.5q0 64 18 126t54.5 111t91 79t127.5 30q54 0 96.5 -15.5t76.5 -42.5l60 73l82 -72l-72 -87q26 -45 39 -98t13 -104q0 -102 -43 -187q-30 -64 -83 -104.5t-98.5 -48t-70.5 -7.5q-95 0 -165 55zM335 105q40 0 74 21
t51.5 51.5t24.5 57.5q15 55 15 99q0 47 -13 96l-242 -294q17 -12 42 -21.5t48 -9.5zM188 234l241 293q-38 36 -94 36q-39 0 -69.5 -19t-51 -51t-31 -73.5t-10.5 -85.5q0 -54 15 -100z" />
    <glyph glyph-name="Ugrave" unicode="&#xd9;" horiz-adv-x="653" 
d="M326 -14q-85 0 -146 37q-62 38 -86.5 93.5t-24.5 112.5v438h128v-425q0 -60 33.5 -98.5t96.5 -38.5t95.5 38.5t32.5 98.5v425h128v-438q0 -57 -20 -102t-55 -76.5t-81.5 -48t-100.5 -16.5zM404 704l-217 112l59 83l191 -141z" />
    <glyph glyph-name="Uacute" unicode="&#xda;" horiz-adv-x="653" 
d="M326 -14q-85 0 -146 37q-62 38 -86.5 93.5t-24.5 112.5v438h128v-425q0 -60 33.5 -98.5t96.5 -38.5t95.5 38.5t32.5 98.5v425h128v-438q0 -57 -20 -102t-55 -76.5t-81.5 -48t-100.5 -16.5zM289 704l-33 54l191 141l59 -83z" />
    <glyph glyph-name="Ucircumflex" unicode="&#xdb;" horiz-adv-x="653" 
d="M326 -14q-85 0 -146 37q-62 38 -86.5 93.5t-24.5 112.5v438h128v-425q0 -60 33.5 -98.5t96.5 -38.5t95.5 38.5t32.5 98.5v425h128v-438q0 -57 -20 -102t-55 -76.5t-81.5 -48t-100.5 -16.5zM234 710l-64 45l115 130h92l113 -129l-64 -45l-96 96z" />
    <glyph glyph-name="Udieresis" unicode="&#xdc;" horiz-adv-x="653" 
d="M326 -14q-85 0 -146 37q-62 38 -86.5 93.5t-24.5 112.5v438h128v-425q0 -60 33.5 -98.5t96.5 -38.5t95.5 38.5t32.5 98.5v425h128v-438q0 -57 -20 -102t-55 -76.5t-81.5 -48t-100.5 -16.5zM232 726q-35 0 -52.5 20.5t-17.5 45.5q0 24 17.5 44.5t52.5 20.5q34 0 51 -20.5
t17 -44.5q0 -25 -17 -45.5t-51 -20.5zM440 726q-35 0 -52.5 20.5t-17.5 45.5q0 24 17.5 44.5t52.5 20.5q34 0 51 -20.5t17 -44.5q0 -25 -17 -45.5t-51 -20.5z" />
    <glyph glyph-name="Yacute" unicode="&#xdd;" horiz-adv-x="552" 
d="M214 0v268l-214 399h143l137 -265l131 265h141l-210 -399v-268h-128zM235 704l-33 54l191 141l59 -83z" />
    <glyph glyph-name="Thorn" unicode="&#xde;" horiz-adv-x="551" 
d="M73 0v667h128v-103h73q48 0 88.5 -6.5t78.5 -25.5q48 -24 71 -71t23 -101q0 -57 -22 -102q-21 -45 -45.5 -66t-44.5 -30q-33 -14 -67.5 -19.5t-69.5 -5.5h-85v-137h-128zM201 252h69q27 0 49 3t38 11q27 13 39.5 36t12.5 49q0 28 -14 52t-41 34q-35 12 -81 12h-72v-197z
" />
    <glyph glyph-name="germandbls" unicode="&#xdf;" horiz-adv-x="598" 
d="M410 -11q-48 0 -90 15.5t-68 38.5l48 96q19 -16 47 -30t55 -14q49 0 49 42q0 25 -16.5 41.5t-54.5 35.5q-61 30 -84 60.5t-23 85.5q0 30 10.5 55t28.5 43.5t41.5 29t47.5 11.5q-3 34 -24 49.5t-43 20.5t-28 5q-51 0 -76 -33.5t-25 -81.5v-459h-124v382h-66v100h67
q2 71 38 118q39 48 87.5 64t92.5 16q53 0 95 -15.5t71 -43t43.5 -63.5t14.5 -73v-86h-85q-21 0 -33.5 -9.5t-12.5 -26.5q0 -29 64 -63q41 -24 68 -45q19 -16 32.5 -45.5t13.5 -64.5q0 -56 -32 -96q-32 -38 -70.5 -49t-58.5 -11z" />
    <glyph glyph-name="agrave" unicode="&#xe0;" horiz-adv-x="488" 
d="M191 -11q-31 0 -59.5 10.5t-50.5 30t-35.5 48t-13.5 64.5q0 39 16.5 67.5t43 48t57 30t55.5 13.5q14 2 100 12v7q0 40 -20.5 57t-56.5 17t-69 -15.5t-50 -30.5l-41 87q21 21 78 40q60 19 103 19q46 0 80.5 -14t57 -39.5t33 -59.5t10.5 -71v-310h-114l-5 36
q-20 -22 -51 -34.5t-68 -12.5zM220 92q24 0 46.5 10t38.5 25v98q-61 -7 -81 -10q-40 -8 -55.5 -27.5t-15.5 -42.5q0 -24 17.5 -38.5t49.5 -14.5zM325 538l-217 112l59 83l191 -141z" />
    <glyph glyph-name="aacute" unicode="&#xe1;" horiz-adv-x="488" 
d="M191 -11q-31 0 -59.5 10.5t-50.5 30t-35.5 48t-13.5 64.5q0 39 16.5 67.5t43 48t57 30t55.5 13.5q14 2 100 12v7q0 40 -20.5 57t-56.5 17t-69 -15.5t-50 -30.5l-41 87q21 21 78 40q60 19 103 19q46 0 80.5 -14t57 -39.5t33 -59.5t10.5 -71v-310h-114l-5 36
q-20 -22 -51 -34.5t-68 -12.5zM220 92q24 0 46.5 10t38.5 25v98q-61 -7 -81 -10q-40 -8 -55.5 -27.5t-15.5 -42.5q0 -24 17.5 -38.5t49.5 -14.5zM175 538l-33 54l191 141l59 -83z" />
    <glyph glyph-name="acircumflex" unicode="&#xe2;" horiz-adv-x="488" 
d="M191 -11q-31 0 -59.5 10.5t-50.5 30t-35.5 48t-13.5 64.5q0 39 16.5 67.5t43 48t57 30t55.5 13.5q14 2 100 12v7q0 40 -20.5 57t-56.5 17t-69 -15.5t-50 -30.5l-41 87q21 21 78 40q60 19 103 19q46 0 80.5 -14t57 -39.5t33 -59.5t10.5 -71v-310h-114l-5 36
q-20 -22 -51 -34.5t-68 -12.5zM220 92q24 0 46.5 10t38.5 25v98q-61 -7 -81 -10q-40 -8 -55.5 -27.5t-15.5 -42.5q0 -24 17.5 -38.5t49.5 -14.5zM150 544l-64 45l115 130h92l113 -129l-64 -45l-96 96z" />
    <glyph glyph-name="atilde" unicode="&#xe3;" horiz-adv-x="488" 
d="M191 -11q-31 0 -59.5 10.5t-50.5 30t-35.5 48t-13.5 64.5q0 39 16.5 67.5t43 48t57 30t55.5 13.5q14 2 100 12v7q0 40 -20.5 57t-56.5 17t-69 -15.5t-50 -30.5l-41 87q21 21 78 40q60 19 103 19q46 0 80.5 -14t57 -39.5t33 -59.5t10.5 -71v-310h-114l-5 36
q-20 -22 -51 -34.5t-68 -12.5zM220 92q24 0 46.5 10t38.5 25v98q-61 -7 -81 -10q-40 -8 -55.5 -27.5t-15.5 -42.5q0 -24 17.5 -38.5t49.5 -14.5zM119 540l-68 38q18 51 49.5 82t73.5 31q26 0 73 -29q40 -28 56 -28q9 0 26 11.5t33 51.5l69 -37q-14 -51 -48 -82.5t-77 -31.5
q-28 0 -74 30q-41 27 -55 27q-11 0 -26.5 -11.5t-31.5 -51.5z" />
    <glyph glyph-name="adieresis" unicode="&#xe4;" horiz-adv-x="488" 
d="M191 -11q-31 0 -59.5 10.5t-50.5 30t-35.5 48t-13.5 64.5q0 39 16.5 67.5t43 48t57 30t55.5 13.5q14 2 100 12v7q0 40 -20.5 57t-56.5 17t-69 -15.5t-50 -30.5l-41 87q21 21 78 40q60 19 103 19q46 0 80.5 -14t57 -39.5t33 -59.5t10.5 -71v-310h-114l-5 36
q-20 -22 -51 -34.5t-68 -12.5zM220 92q24 0 46.5 10t38.5 25v98q-61 -7 -81 -10q-40 -8 -55.5 -27.5t-15.5 -42.5q0 -24 17.5 -38.5t49.5 -14.5zM148 560q-35 0 -52.5 20.5t-17.5 45.5q0 24 17.5 44.5t52.5 20.5q34 0 51 -20.5t17 -44.5q0 -25 -17 -45.5t-51 -20.5zM356 560
q-35 0 -52.5 20.5t-17.5 45.5q0 24 17.5 44.5t52.5 20.5q34 0 51 -20.5t17 -44.5q0 -25 -17 -45.5t-51 -20.5z" />
    <glyph glyph-name="aring" unicode="&#xe5;" horiz-adv-x="488" 
d="M191 -11q-31 0 -59.5 10.5t-50.5 30t-35.5 48t-13.5 64.5q0 39 16.5 67.5t43 48t57 30t55.5 13.5q14 2 100 12v7q0 40 -20.5 57t-56.5 17t-69 -15.5t-50 -30.5l-41 87q21 21 78 40q60 19 103 19q46 0 80.5 -14t57 -39.5t33 -59.5t10.5 -71v-310h-114l-5 36
q-20 -22 -51 -34.5t-68 -12.5zM220 92q24 0 46.5 10t38.5 25v98q-61 -7 -81 -10q-40 -8 -55.5 -27.5t-15.5 -42.5q0 -24 17.5 -38.5t49.5 -14.5zM257 529q-45 0 -74.5 30t-29.5 78q0 26 9.5 46.5t25 34.5t34.5 21t39 7q44 0 72.5 -31t28.5 -77q0 -22 -7.5 -42t-21.5 -35
t-33.5 -23.5t-42.5 -8.5zM257 592q19 0 29.5 13t10.5 33t-10.5 33t-29.5 13q-18 0 -28.5 -13t-10.5 -33t10.5 -33t28.5 -13z" />
    <glyph glyph-name="ae" unicode="&#xe6;" horiz-adv-x="770" 
d="M200 -11q-34 0 -64.5 10t-53.5 29.5t-36.5 48.5t-13.5 67q0 63 45 101q48 39 102 49q63 11 95 11h29v27q0 28 -19 45t-51 17q-36 0 -65.5 -11.5t-59.5 -34.5l-41 88q30 24 80 41q51 17 91 17q96 0 141 -60q60 60 148 60q81 0 132 -45q52 -45 67 -106q15 -60 15 -111v-22
h-312q4 -61 38 -88t88 -27q48 0 84 15t49 28l33 -101q-22 -16 -42 -23q-15 -7 -59 -16q-42 -9 -82 -9q-51 0 -94 17.5t-74 52.5q-29 -33 -67 -51.5t-103 -18.5zM217 90q27 0 49 13.5t30 31t8 29.5v52h-31q-57 0 -87.5 -21.5t-30.5 -50.5q0 -26 16.5 -40t45.5 -14zM432 299
h183q-3 41 -23.5 63t-39 27.5t-25.5 5.5q-32 0 -61 -24t-34 -72z" />
    <glyph glyph-name="ccedilla" unicode="&#xe7;" horiz-adv-x="463" 
d="M257 -11q-44 0 -84.5 15t-71.5 46t-49.5 78t-18.5 111q0 90 36 152q37 61 89 82t96 21q76 0 122 -36t67 -77l-92 -62q-15 26 -37.5 48.5t-58.5 22.5q-40 0 -69.5 -35.5t-29.5 -110.5q0 -72 30 -110t87 -38q51 0 81.5 15.5t46.5 31.5l36 -102q-27 -20 -73.5 -36
t-106.5 -16zM150 -181l59 137h110l-81 -137h-88z" />
    <glyph glyph-name="egrave" unicode="&#xe8;" horiz-adv-x="499" 
d="M267 -11q-47 0 -89.5 15t-74.5 46.5t-51 78.5t-19 112q0 61 17.5 108t48 79t71 49t86.5 17q81 0 132 -45q52 -45 67 -106q15 -60 15 -111v-22h-312q4 -61 38 -88t88 -27q48 0 84 15t49 28l33 -101q-22 -16 -42 -23q-12 -6 -59 -15q-46 -10 -82 -10zM161 299h183
q-3 41 -23.5 63t-39.5 27.5t-25 5.5q-32 0 -61 -24t-34 -72zM344 538l-217 112l59 83l191 -141z" />
    <glyph glyph-name="eacute" unicode="&#xe9;" horiz-adv-x="499" 
d="M267 -11q-47 0 -89.5 15t-74.5 46.5t-51 78.5t-19 112q0 61 17.5 108t48 79t71 49t86.5 17q81 0 132 -45q52 -45 67 -106q15 -60 15 -111v-22h-312q4 -61 38 -88t88 -27q48 0 84 15t49 28l33 -101q-22 -16 -42 -23q-12 -6 -59 -15q-46 -10 -82 -10zM161 299h183
q-3 41 -23.5 63t-39.5 27.5t-25 5.5q-32 0 -61 -24t-34 -72zM193 538l-33 54l191 141l59 -83z" />
    <glyph glyph-name="ecircumflex" unicode="&#xea;" horiz-adv-x="499" 
d="M267 -11q-47 0 -89.5 15t-74.5 46.5t-51 78.5t-19 112q0 61 17.5 108t48 79t71 49t86.5 17q81 0 132 -45q52 -45 67 -106q15 -60 15 -111v-22h-312q4 -61 38 -88t88 -27q48 0 84 15t49 28l33 -101q-22 -16 -42 -23q-12 -6 -59 -15q-46 -10 -82 -10zM161 299h183
q-3 41 -23.5 63t-39.5 27.5t-25 5.5q-32 0 -61 -24t-34 -72zM156 544l-64 45l115 130h92l113 -129l-64 -45l-96 96z" />
    <glyph glyph-name="edieresis" unicode="&#xeb;" horiz-adv-x="499" 
d="M267 -11q-47 0 -89.5 15t-74.5 46.5t-51 78.5t-19 112q0 61 17.5 108t48 79t71 49t86.5 17q81 0 132 -45q52 -45 67 -106q15 -60 15 -111v-22h-312q4 -61 38 -88t88 -27q48 0 84 15t49 28l33 -101q-22 -16 -42 -23q-12 -6 -59 -15q-46 -10 -82 -10zM161 299h183
q-3 41 -23.5 63t-39.5 27.5t-25 5.5q-32 0 -61 -24t-34 -72zM163 560q-35 0 -52.5 20.5t-17.5 45.5q0 24 17.5 44.5t52.5 20.5q34 0 51 -20.5t17 -44.5q0 -25 -17 -45.5t-51 -20.5zM371 560q-35 0 -52.5 20.5t-17.5 45.5q0 24 17.5 44.5t52.5 20.5q34 0 51 -20.5t17 -44.5
q0 -25 -17 -45.5t-51 -20.5z" />
    <glyph glyph-name="igrave" unicode="&#xec;" horiz-adv-x="247" 
d="M62 0v482h123v-482h-123zM217 538l-217 112l59 83l191 -141z" />
    <glyph glyph-name="iacute" unicode="&#xed;" horiz-adv-x="247" 
d="M62 0v482h123v-482h-123zM32 538l-33 54l191 141l59 -83z" />
    <glyph glyph-name="icircumflex" unicode="&#xee;" horiz-adv-x="247" 
d="M62 0v482h123v-482h-123zM32 544l-64 45l115 130h92l113 -129l-64 -45l-96 96z" />
    <glyph glyph-name="idieresis" unicode="&#xef;" horiz-adv-x="247" 
d="M62 0v482h123v-482h-123zM25 560q-35 0 -52.5 20.5t-17.5 45.5q0 24 17.5 44.5t52.5 20.5q34 0 51 -20.5t17 -44.5q0 -25 -17 -45.5t-51 -20.5zM233 560q-35 0 -52.5 20.5t-17.5 45.5q0 24 17.5 44.5t52.5 20.5q34 0 51 -20.5t17 -44.5q0 -25 -17 -45.5t-51 -20.5z" />
    <glyph glyph-name="eth" unicode="&#xf0;" horiz-adv-x="541" 
d="M119 564l99 45q-13 10 -25 18.5t-23 15.5l105 47q11 -7 22 -15t23 -19l96 44l46 -48l-94 -44q26 -29 50.5 -66t43.5 -80.5t30.5 -93t11.5 -104.5q0 -62 -15 -112.5t-44 -86.5t-73 -55.5t-101 -19.5q-47 0 -89.5 15.5t-74.5 47t-51 78.5t-19 109q0 51 13.5 96t39.5 79
t64.5 54t89.5 20q24 0 46.5 -5.5t41.5 -18.5q-11 26 -27.5 50.5t-36.5 46.5l-103 -48zM376 245q0 29 -6 62t-14 52q-44 25 -87 25q-48 0 -79.5 -35.5t-31.5 -107.5q0 -36 9.5 -63t25 -45.5t35 -27.5t40.5 -9q20 0 39.5 9t34.5 27.5t24.5 46.5t9.5 66z" />
    <glyph glyph-name="ntilde" unicode="&#xf1;" horiz-adv-x="552" 
d="M62 0v482h113l6 -38q29 24 65.5 37t73.5 13q58 0 99 -26t57.5 -76.5t16.5 -106.5v-285h-124v275q0 55 -22.5 85t-64.5 30q-29 0 -53.5 -15t-42.5 -33v-342h-124zM153 540l-68 38q18 51 49.5 82t73.5 31q26 0 73 -29q40 -28 56 -28q9 0 26 11.5t33 51.5l69 -37
q-14 -51 -48 -82.5t-77 -31.5q-28 0 -74 30q-41 27 -55 27q-11 0 -26.5 -11.5t-31.5 -51.5z" />
    <glyph glyph-name="ograve" unicode="&#xf2;" horiz-adv-x="528" 
d="M264 -11q-58 0 -101.5 22t-72 58t-43 81.5t-14.5 92.5q0 73 32 135q33 62 87 89t112 27q81 0 137 -44q55 -43 74 -102q20 -62 20 -105q0 -73 -33 -136q-25 -49 -65.5 -77t-75.5 -34.5t-57 -6.5zM264 96q32 -2 54 15.5t34.5 41.5t17.5 49.5t3 40.5q0 54 -22.5 91t-48 46.5
t-38.5 8.5q-42 0 -70 -30q-23 -25 -31 -55.5t-8 -60.5q0 -31 7 -57.5t20.5 -46.5t34 -31.5t47.5 -11.5zM357 538l-217 112l59 83l191 -141z" />
    <glyph glyph-name="oacute" unicode="&#xf3;" horiz-adv-x="528" 
d="M264 -11q-58 0 -101.5 22t-72 58t-43 81.5t-14.5 92.5q0 73 32 135q33 62 87 89t112 27q81 0 137 -44q55 -43 74 -102q20 -62 20 -105q0 -73 -33 -136q-25 -49 -65.5 -77t-75.5 -34.5t-57 -6.5zM264 96q32 -2 54 15.5t34.5 41.5t17.5 49.5t3 40.5q0 54 -22.5 91t-48 46.5
t-38.5 8.5q-42 0 -70 -30q-23 -25 -31 -55.5t-8 -60.5q0 -31 7 -57.5t20.5 -46.5t34 -31.5t47.5 -11.5zM194 538l-33 54l191 141l59 -83z" />
    <glyph glyph-name="ocircumflex" unicode="&#xf4;" horiz-adv-x="528" 
d="M264 -11q-58 0 -101.5 22t-72 58t-43 81.5t-14.5 92.5q0 73 32 135q33 62 87 89t112 27q81 0 137 -44q55 -43 74 -102q20 -62 20 -105q0 -73 -33 -136q-25 -49 -65.5 -77t-75.5 -34.5t-57 -6.5zM264 96q32 -2 54 15.5t34.5 41.5t17.5 49.5t3 40.5q0 54 -22.5 91t-48 46.5
t-38.5 8.5q-42 0 -70 -30q-23 -25 -31 -55.5t-8 -60.5q0 -31 7 -57.5t20.5 -46.5t34 -31.5t47.5 -11.5zM166 544l-64 45l115 130h92l113 -129l-64 -45l-96 96z" />
    <glyph glyph-name="otilde" unicode="&#xf5;" horiz-adv-x="528" 
d="M264 -11q-58 0 -101.5 22t-72 58t-43 81.5t-14.5 92.5q0 73 32 135q33 62 87 89t112 27q81 0 137 -44q55 -43 74 -102q20 -62 20 -105q0 -73 -33 -136q-25 -49 -65.5 -77t-75.5 -34.5t-57 -6.5zM264 96q32 -2 54 15.5t34.5 41.5t17.5 49.5t3 40.5q0 54 -22.5 91t-48 46.5
t-38.5 8.5q-42 0 -70 -30q-23 -25 -31 -55.5t-8 -60.5q0 -31 7 -57.5t20.5 -46.5t34 -31.5t47.5 -11.5zM136 540l-68 38q18 51 49.5 82t73.5 31q26 0 73 -29q40 -28 56 -28q9 0 26 11.5t33 51.5l69 -37q-14 -51 -48 -82.5t-77 -31.5q-28 0 -74 30q-41 27 -55 27
q-11 0 -26.5 -11.5t-31.5 -51.5z" />
    <glyph glyph-name="odieresis" unicode="&#xf6;" horiz-adv-x="528" 
d="M264 -11q-58 0 -101.5 22t-72 58t-43 81.5t-14.5 92.5q0 73 32 135q33 62 87 89t112 27q81 0 137 -44q55 -43 74 -102q20 -62 20 -105q0 -73 -33 -136q-25 -49 -65.5 -77t-75.5 -34.5t-57 -6.5zM264 96q32 -2 54 15.5t34.5 41.5t17.5 49.5t3 40.5q0 54 -22.5 91t-48 46.5
t-38.5 8.5q-42 0 -70 -30q-23 -25 -31 -55.5t-8 -60.5q0 -31 7 -57.5t20.5 -46.5t34 -31.5t47.5 -11.5zM167 560q-35 0 -52.5 20.5t-17.5 45.5q0 24 17.5 44.5t52.5 20.5q34 0 51 -20.5t17 -44.5q0 -25 -17 -45.5t-51 -20.5zM375 560q-35 0 -52.5 20.5t-17.5 45.5
q0 24 17.5 44.5t52.5 20.5q34 0 51 -20.5t17 -44.5q0 -25 -17 -45.5t-51 -20.5z" />
    <glyph glyph-name="divide" unicode="&#xf7;" 
d="M257 72q-33 0 -54.5 23.5t-21.5 55.5q0 31 21.5 54.5t54.5 23.5t54.5 -23.5t21.5 -54.5q0 -32 -21.5 -55.5t-54.5 -23.5zM22 275v110h468v-110h-468zM257 443q-33 0 -54.5 24t-21.5 55t21.5 55t54.5 24t54.5 -24t21.5 -55t-21.5 -55t-54.5 -24z" />
    <glyph glyph-name="oslash" unicode="&#xf8;" horiz-adv-x="528" 
d="M129 -53l-65 47l39 61q-35 37 -52.5 86.5t-17.5 101.5q0 73 32 135q33 62 87 89t112 27q52 0 104 -23l36 56l65 -47l-37 -58q32 -36 47.5 -82t15.5 -97q0 -73 -33 -136q-25 -49 -65.5 -77t-75.5 -34.5t-57 -6.5q-55 0 -96 19zM264 94q31 -1 52.5 14t34.5 37.5t18.5 49
t4.5 48.5q0 36 -9 71l-136 -215q18 -5 35 -5zM168 158l143 223h1q-19 10 -48 10q-44 2 -71 -29q-27 -30 -33.5 -64t-5.5 -55q0 -45 14 -85z" />
    <glyph glyph-name="ugrave" unicode="&#xf9;" horiz-adv-x="550" 
d="M232 -10q-85 0 -129 51t-44 154v287h124v-274q0 -50 18 -81t62 -31q36 0 61 14.5t40 27.5v344h124v-482h-114l-4 38q-33 -28 -68.5 -38t-69.5 -10zM360 538l-217 112l59 83l191 -141z" />
    <glyph glyph-name="uacute" unicode="&#xfa;" horiz-adv-x="550" 
d="M232 -10q-85 0 -129 51t-44 154v287h124v-274q0 -50 18 -81t62 -31q36 0 61 14.5t40 27.5v344h124v-482h-114l-4 38q-33 -28 -68.5 -38t-69.5 -10zM216 538l-33 54l191 141l59 -83z" />
    <glyph glyph-name="ucircumflex" unicode="&#xfb;" horiz-adv-x="550" 
d="M232 -10q-85 0 -129 51t-44 154v287h124v-274q0 -50 18 -81t62 -31q36 0 61 14.5t40 27.5v344h124v-482h-114l-4 38q-33 -28 -68.5 -38t-69.5 -10zM183 544l-64 45l115 130h92l113 -129l-64 -45l-96 96z" />
    <glyph glyph-name="udieresis" unicode="&#xfc;" horiz-adv-x="550" 
d="M232 -10q-85 0 -129 51t-44 154v287h124v-274q0 -50 18 -81t62 -31q36 0 61 14.5t40 27.5v344h124v-482h-114l-4 38q-33 -28 -68.5 -38t-69.5 -10zM172 560q-35 0 -52.5 20.5t-17.5 45.5q0 24 17.5 44.5t52.5 20.5q34 0 51 -20.5t17 -44.5q0 -25 -17 -45.5t-51 -20.5z
M380 560q-35 0 -52.5 20.5t-17.5 45.5q0 24 17.5 44.5t52.5 20.5q34 0 51 -20.5t17 -44.5q0 -25 -17 -45.5t-51 -20.5z" />
    <glyph glyph-name="yacute" unicode="&#xfd;" horiz-adv-x="503" 
d="M37 -191l-11 111q54 0 96.5 13t65.5 42q4 5 7 11t7 12l-198 484h133l134 -370q43 114 58 171q38 140 52 199h122q-37 -147 -78.5 -266t-83.5 -204q-40 -76 -72 -118q-32 -39 -93 -61.5t-139 -23.5zM197 538l-33 54l191 141l59 -83z" />
    <glyph glyph-name="thorn" unicode="&#xfe;" horiz-adv-x="552" 
d="M69 -189v856h124v-217q17 16 44.5 30t64.5 14q69 0 117 -42t65 -102q17 -63 17 -108q0 -76 -34 -138.5t-90 -88.5t-119 -26q-30 0 -64 14v-192h-125zM251 97q37 -1 62.5 14t40.5 37t21 47t5 45q1 44 -17 83.5t-42.5 54t-44.5 12.5q-26 0 -49 -14.5t-34 -31.5v-233
q33 -14 58 -14z" />
    <glyph glyph-name="ydieresis" unicode="&#xff;" horiz-adv-x="503" 
d="M37 -191l-11 111q54 0 96.5 13t65.5 42q4 5 7 11t7 12l-198 484h133l134 -370q43 114 58 171q38 140 52 199h122q-37 -147 -78.5 -266t-83.5 -204q-40 -76 -72 -118q-32 -39 -93 -61.5t-139 -23.5zM165 560q-35 0 -52.5 20.5t-17.5 45.5q0 24 17.5 44.5t52.5 20.5
q34 0 51 -20.5t17 -44.5q0 -25 -17 -45.5t-51 -20.5zM373 560q-35 0 -52.5 20.5t-17.5 45.5q0 24 17.5 44.5t52.5 20.5q34 0 51 -20.5t17 -44.5q0 -25 -17 -45.5t-51 -20.5z" />
    <glyph glyph-name="Amacron" unicode="&#x100;" horiz-adv-x="608" 
d="M0 0l240 667h127l241 -667h-131l-46 134h-255l-45 -134h-131zM215 247h178l-89 263zM134 744v89h345v-89h-345z" />
    <glyph glyph-name="amacron" unicode="&#x101;" horiz-adv-x="488" 
d="M191 -11q-31 0 -59.5 10.5t-50.5 30t-35.5 48t-13.5 64.5q0 39 16.5 67.5t43 48t57 30t55.5 13.5q14 2 100 12v7q0 40 -20.5 57t-56.5 17t-69 -15.5t-50 -30.5l-41 87q21 21 78 40q60 19 103 19q46 0 80.5 -14t57 -39.5t33 -59.5t10.5 -71v-310h-114l-5 36
q-20 -22 -51 -34.5t-68 -12.5zM220 92q24 0 46.5 10t38.5 25v98q-61 -7 -81 -10q-40 -8 -55.5 -27.5t-15.5 -42.5q0 -24 17.5 -38.5t49.5 -14.5zM77 578v89h345v-89h-345z" />
    <glyph glyph-name="Abreve" unicode="&#x102;" horiz-adv-x="608" 
d="M0 0l240 667h127l241 -667h-131l-46 134h-255l-45 -134h-131zM215 247h178l-89 263zM306 705q-37 0 -67.5 11.5t-51.5 31.5t-31 46t-10 52h98q6 -28 21 -44.5t41 -16.5t42 16.5t22 44.5h95q-1 -34 -15 -60t-36.5 -44t-50.5 -27.5t-57 -9.5z" />
    <glyph glyph-name="abreve" unicode="&#x103;" horiz-adv-x="488" 
d="M191 -11q-31 0 -59.5 10.5t-50.5 30t-35.5 48t-13.5 64.5q0 39 16.5 67.5t43 48t57 30t55.5 13.5q14 2 100 12v7q0 40 -20.5 57t-56.5 17t-69 -15.5t-50 -30.5l-41 87q21 21 78 40q60 19 103 19q46 0 80.5 -14t57 -39.5t33 -59.5t10.5 -71v-310h-114l-5 36
q-20 -22 -51 -34.5t-68 -12.5zM220 92q24 0 46.5 10t38.5 25v98q-61 -7 -81 -10q-40 -8 -55.5 -27.5t-15.5 -42.5q0 -24 17.5 -38.5t49.5 -14.5zM248 539q-37 0 -67.5 11.5t-51.5 31.5t-31 46t-10 52h98q6 -28 21 -44.5t41 -16.5t42 16.5t22 44.5h95q-1 -34 -15 -60
t-36.5 -44t-50.5 -27.5t-57 -9.5z" />
    <glyph glyph-name="Aogonek" unicode="&#x104;" horiz-adv-x="608" 
d="M0 0l240 667h127l241 -667h-131l-46 134h-255l-45 -134h-131zM215 247h178l-89 263zM529 -198q-51 0 -76 24.5t-25 62.5q0 27 11.5 49.5t28.5 40t34 28.5t25 15l72 -12q-29 -20 -52.5 -44t-23.5 -48q0 -13 6.5 -21t21.5 -8q18 0 33 9l23 -75q-19 -11 -38.5 -16t-39.5 -5z
" />
    <glyph glyph-name="aogonek" unicode="&#x105;" horiz-adv-x="488" 
d="M191 -11q-31 0 -59.5 10.5t-50.5 30t-35.5 48t-13.5 64.5q0 39 16.5 67.5t43 48t57 30t55.5 13.5q14 2 100 12v7q0 40 -20.5 57t-56.5 17t-69 -15.5t-50 -30.5l-41 87q21 21 78 40q60 19 103 19q46 0 80.5 -14t57 -39.5t33 -59.5t10.5 -71v-310h-114l-5 36
q-20 -22 -51 -34.5t-68 -12.5zM220 92q24 0 46.5 10t38.5 25v98q-61 -7 -81 -10q-40 -8 -55.5 -27.5t-15.5 -42.5q0 -24 17.5 -38.5t49.5 -14.5zM350 -198q-51 0 -76 24.5t-25 62.5q0 27 11.5 49.5t28.5 40t34 28.5t25 15l72 -12q-29 -20 -52.5 -44t-23.5 -48q0 -13 6.5 -21
t21.5 -8q18 0 33 9l23 -75q-19 -11 -38.5 -16t-39.5 -5z" />
    <glyph glyph-name="Cacute" unicode="&#x106;" horiz-adv-x="571" 
d="M330 -14q-63 0 -115.5 22t-91 65.5t-59.5 108t-21 149.5q0 90 24.5 155.5t64.5 109t91.5 64.5t105.5 21q68 0 119 -25t74 -53t29 -40l-94 -79q-5 9 -14.5 22.5t-25 26t-37 21.5t-49.5 9q-32 0 -60.5 -13.5t-50.5 -42t-34.5 -72t-12.5 -104.5q0 -109 47 -167.5t125 -58.5
q57 0 98 19.5t59 37.5l41 -111q-23 -27 -92.5 -46t-120.5 -19zM241 705l-33 54l191 141l59 -83z" />
    <glyph glyph-name="cacute" unicode="&#x107;" horiz-adv-x="463" 
d="M257 -11q-44 0 -84.5 15t-71.5 46t-49.5 78t-18.5 111q0 90 36 152q37 61 89 82t96 21q76 0 122 -36t67 -77l-92 -62q-15 26 -37.5 48.5t-58.5 22.5q-40 0 -69.5 -35.5t-29.5 -110.5q0 -72 30 -110t87 -38q51 0 81.5 15.5t46.5 31.5l36 -102q-27 -20 -73.5 -36
t-106.5 -16zM166 538l-33 54l191 141l59 -83z" />
    <glyph glyph-name="Ccircumflex" unicode="&#x108;" horiz-adv-x="571" 
d="M330 -14q-63 0 -115.5 22t-91 65.5t-59.5 108t-21 149.5q0 90 24.5 155.5t64.5 109t91.5 64.5t105.5 21q68 0 119 -25t74 -53t29 -40l-94 -79q-5 9 -14.5 22.5t-25 26t-37 21.5t-49.5 9q-32 0 -60.5 -13.5t-50.5 -42t-34.5 -72t-12.5 -104.5q0 -109 47 -167.5t125 -58.5
q57 0 98 19.5t59 37.5l41 -111q-23 -27 -92.5 -46t-120.5 -19zM245 710l-64 45l115 130h92l113 -129l-64 -45l-96 96z" />
    <glyph glyph-name="ccircumflex" unicode="&#x109;" horiz-adv-x="463" 
d="M257 -11q-44 0 -84.5 15t-71.5 46t-49.5 78t-18.5 111q0 90 36 152q37 61 89 82t96 21q76 0 122 -36t67 -77l-92 -62q-15 26 -37.5 48.5t-58.5 22.5q-40 0 -69.5 -35.5t-29.5 -110.5q0 -72 30 -110t87 -38q51 0 81.5 15.5t46.5 31.5l36 -102q-27 -20 -73.5 -36
t-106.5 -16zM151 544l-64 45l115 130h92l113 -129l-64 -45l-96 96z" />
    <glyph glyph-name="Cdotaccent" unicode="&#x10a;" horiz-adv-x="571" 
d="M330 -14q-63 0 -115.5 22t-91 65.5t-59.5 108t-21 149.5q0 90 24.5 155.5t64.5 109t91.5 64.5t105.5 21q68 0 119 -25t74 -53t29 -40l-94 -79q-5 9 -14.5 22.5t-25 26t-37 21.5t-49.5 9q-32 0 -60.5 -13.5t-50.5 -42t-34.5 -72t-12.5 -104.5q0 -109 47 -167.5t125 -58.5
q57 0 98 19.5t59 37.5l41 -111q-23 -27 -92.5 -46t-120.5 -19zM335 720q-30 0 -50 23t-20 54t20 54t50 23q31 0 51 -23t20 -54t-20 -54t-51 -23z" />
    <glyph glyph-name="cdotaccent" unicode="&#x10b;" horiz-adv-x="463" 
d="M257 -11q-44 0 -84.5 15t-71.5 46t-49.5 78t-18.5 111q0 90 36 152q37 61 89 82t96 21q76 0 122 -36t67 -77l-92 -62q-15 26 -37.5 48.5t-58.5 22.5q-40 0 -69.5 -35.5t-29.5 -110.5q0 -72 30 -110t87 -38q51 0 81.5 15.5t46.5 31.5l36 -102q-27 -20 -73.5 -36
t-106.5 -16zM259 554q-30 0 -50 23t-20 54t20 54t50 23q31 0 51 -23t20 -54t-20 -54t-51 -23z" />
    <glyph glyph-name="Ccaron" unicode="&#x10c;" horiz-adv-x="571" 
d="M330 -14q-63 0 -115.5 22t-91 65.5t-59.5 108t-21 149.5q0 90 24.5 155.5t64.5 109t91.5 64.5t105.5 21q68 0 119 -25t74 -53t29 -40l-94 -79q-5 9 -14.5 22.5t-25 26t-37 21.5t-49.5 9q-32 0 -60.5 -13.5t-50.5 -42t-34.5 -72t-12.5 -104.5q0 -109 47 -167.5t125 -58.5
q57 0 98 19.5t59 37.5l41 -111q-23 -27 -92.5 -46t-120.5 -19zM283 710l-115 126l68 48l92 -95l96 95l66 -41l-115 -133h-92z" />
    <glyph glyph-name="ccaron" unicode="&#x10d;" horiz-adv-x="463" 
d="M257 -11q-44 0 -84.5 15t-71.5 46t-49.5 78t-18.5 111q0 90 36 152q37 61 89 82t96 21q76 0 122 -36t67 -77l-92 -62q-15 26 -37.5 48.5t-58.5 22.5q-40 0 -69.5 -35.5t-29.5 -110.5q0 -72 30 -110t87 -38q51 0 81.5 15.5t46.5 31.5l36 -102q-27 -20 -73.5 -36
t-106.5 -16zM210 544l-115 126l68 48l92 -95l96 95l66 -41l-115 -133h-92z" />
    <glyph glyph-name="Dcaron" unicode="&#x10e;" horiz-adv-x="628" 
d="M73 0v667h188q36 0 80 -6t84 -24q63 -31 102 -92q40 -60 50 -117q10 -67 10 -91q0 -87 -34.5 -161.5t-71 -104.5t-57.5 -37q-68 -34 -158 -34h-193zM201 120h52q70 0 114 25q46 25 68 76.5t22 109.5q0 57 -20 107.5t-62 78.5q-22 15 -54.5 22.5t-64.5 7.5h-55v-427z
M227 710l-115 126l68 48l92 -95l96 95l66 -41l-115 -133h-92z" />
    <glyph glyph-name="dcaron" unicode="&#x10f;" horiz-adv-x="569" 
d="M238 -11q-50 0 -88 21t-64 56t-39.5 81t-13.5 96q0 77 31 135q23 47 63.5 73.5t77 35t57.5 7.5q35 0 82 -14v187h123v-667h-113l-5 37q-17 -19 -45 -33.5t-66 -14.5zM263 95q23 0 47 12.5t34 29.5v238q-39 14 -67 14q-52 0 -83.5 -33.5t-36.5 -65.5t-3 -44
q0 -30 6.5 -57.5t20 -48t34 -33t48.5 -12.5zM522 497v170h107l-34 -170h-73z" />
    <glyph glyph-name="Dcroat" unicode="&#x110;" horiz-adv-x="628" 
d="M73 0v291h-73v105h73v271h188q36 0 80 -6t84 -24q63 -31 102 -92q40 -60 50 -117q10 -67 10 -91q0 -87 -34.5 -161.5t-71 -104.5t-57.5 -37q-68 -34 -158 -34h-193zM201 120h52q70 0 114 25q46 25 68 76.5t22 109.5q0 57 -20 107.5t-62 78.5q-22 15 -54.5 22.5t-64.5 7.5
h-55v-151h147v-105h-147v-171z" />
    <glyph glyph-name="dcroat" unicode="&#x111;" horiz-adv-x="529" 
d="M238 -11q-50 0 -88 21t-64 56t-39.5 81t-13.5 96q0 77 31 135q23 47 63.5 73.5t77 35t57.5 7.5q35 0 82 -14v52h-116v83h116v52h123v-52h59v-83h-59v-532h-113l-5 37q-17 -19 -45 -33.5t-66 -14.5zM263 95q23 0 47 12.5t34 29.5v238q-39 14 -67 14q-52 0 -83.5 -33.5
t-36.5 -65.5t-3 -44q0 -30 6.5 -57.5t20 -48t34 -33t48.5 -12.5z" />
    <glyph glyph-name="Emacron" unicode="&#x112;" horiz-adv-x="557" 
d="M73 0v667h435v-121h-307v-152h254v-121h-254v-152h329v-121h-457zM116 744v89h345v-89h-345z" />
    <glyph glyph-name="emacron" unicode="&#x113;" horiz-adv-x="499" 
d="M267 -11q-47 0 -89.5 15t-74.5 46.5t-51 78.5t-19 112q0 61 17.5 108t48 79t71 49t86.5 17q81 0 132 -45q52 -45 67 -106q15 -60 15 -111v-22h-312q4 -61 38 -88t88 -27q48 0 84 15t49 28l33 -101q-22 -16 -42 -23q-12 -6 -59 -15q-46 -10 -82 -10zM161 299h183
q-3 41 -23.5 63t-39.5 27.5t-25 5.5q-32 0 -61 -24t-34 -72zM88 578v89h345v-89h-345z" />
    <glyph glyph-name="Ebreve" unicode="&#x114;" horiz-adv-x="557" 
d="M73 0v667h435v-121h-307v-152h254v-121h-254v-152h329v-121h-457zM296 703q-37 0 -67.5 11.5t-51.5 31.5t-31 46t-10 52h98q6 -28 21 -44.5t41 -16.5t42 16.5t22 44.5h95q-1 -34 -15 -60t-36.5 -44t-50.5 -27.5t-57 -9.5z" />
    <glyph glyph-name="ebreve" unicode="&#x115;" horiz-adv-x="499" 
d="M267 -11q-47 0 -89.5 15t-74.5 46.5t-51 78.5t-19 112q0 61 17.5 108t48 79t71 49t86.5 17q81 0 132 -45q52 -45 67 -106q15 -60 15 -111v-22h-312q4 -61 38 -88t88 -27q48 0 84 15t49 28l33 -101q-22 -16 -42 -23q-12 -6 -59 -15q-46 -10 -82 -10zM161 299h183
q-3 41 -23.5 63t-39.5 27.5t-25 5.5q-32 0 -61 -24t-34 -72zM255 539q-37 0 -67.5 11.5t-51.5 31.5t-31 46t-10 52h98q6 -28 21 -44.5t41 -16.5t42 16.5t22 44.5h95q-1 -34 -15 -60t-36.5 -44t-50.5 -27.5t-57 -9.5z" />
    <glyph glyph-name="Edotaccent" unicode="&#x116;" horiz-adv-x="557" 
d="M73 0v667h435v-121h-307v-152h254v-121h-254v-152h329v-121h-457zM296 721q-30 0 -50 23t-20 54t20 54t50 23q31 0 51 -23t20 -54t-20 -54t-51 -23z" />
    <glyph glyph-name="edotaccent" unicode="&#x117;" horiz-adv-x="499" 
d="M267 -11q-47 0 -89.5 15t-74.5 46.5t-51 78.5t-19 112q0 61 17.5 108t48 79t71 49t86.5 17q81 0 132 -45q52 -45 67 -106q15 -60 15 -111v-22h-312q4 -61 38 -88t88 -27q48 0 84 15t49 28l33 -101q-22 -16 -42 -23q-12 -6 -59 -15q-46 -10 -82 -10zM161 299h183
q-3 41 -23.5 63t-39.5 27.5t-25 5.5q-32 0 -61 -24t-34 -72zM263 554q-30 0 -50 23t-20 54t20 54t50 23q31 0 51 -23t20 -54t-20 -54t-51 -23z" />
    <glyph glyph-name="Eogonek" unicode="&#x118;" horiz-adv-x="557" 
d="M73 0v667h435v-121h-307v-152h254v-121h-254v-152h329v-121h-457zM451 -198q-51 0 -76 24.5t-25 62.5q0 27 11.5 49.5t28.5 40t34 28.5t25 15l72 -12q-29 -20 -52.5 -44t-23.5 -48q0 -13 6.5 -21t21.5 -8q18 0 33 9l23 -75q-19 -11 -38.5 -16t-39.5 -5z" />
    <glyph glyph-name="eogonek" unicode="&#x119;" horiz-adv-x="499" 
d="M267 -11q-47 0 -89.5 15t-74.5 46.5t-51 78.5t-19 112q0 61 17.5 108t48 79t71 49t86.5 17q81 0 132 -45q52 -45 67 -106q15 -60 15 -111v-22h-312q4 -61 38 -88t88 -27q48 0 84 15t49 28l33 -101q-22 -16 -42 -23q-12 -6 -59 -15q-46 -10 -82 -10zM161 299h183
q-3 41 -23.5 63t-39.5 27.5t-25 5.5q-32 0 -61 -24t-34 -72zM324 -198q-51 0 -76 24.5t-25 62.5q0 27 11.5 49.5t28.5 40t34 28.5t25 15l72 -12q-29 -20 -52.5 -44t-23.5 -48q0 -13 6.5 -21t21.5 -8q18 0 33 9l23 -75q-19 -11 -38.5 -16t-39.5 -5z" />
    <glyph glyph-name="Ecaron" unicode="&#x11a;" horiz-adv-x="557" 
d="M73 0v667h435v-121h-307v-152h254v-121h-254v-152h329v-121h-457zM247 710l-115 126l68 48l92 -95l96 95l66 -41l-115 -133h-92z" />
    <glyph glyph-name="ecaron" unicode="&#x11b;" horiz-adv-x="499" 
d="M267 -11q-47 0 -89.5 15t-74.5 46.5t-51 78.5t-19 112q0 61 17.5 108t48 79t71 49t86.5 17q81 0 132 -45q52 -45 67 -106q15 -60 15 -111v-22h-312q4 -61 38 -88t88 -27q48 0 84 15t49 28l33 -101q-22 -16 -42 -23q-12 -6 -59 -15q-46 -10 -82 -10zM161 299h183
q-3 41 -23.5 63t-39.5 27.5t-25 5.5q-32 0 -61 -24t-34 -72zM211 544l-115 126l68 48l92 -95l96 95l66 -41l-115 -133h-92z" />
    <glyph glyph-name="Gcircumflex" unicode="&#x11c;" horiz-adv-x="608" 
d="M338 -14q-65 0 -119.5 23t-93.5 67.5t-60.5 108.5t-21.5 146q0 90 23 155.5t62 109t89.5 64.5t106.5 21q88 0 142 -32.5t82 -83.5l-88 -81q-19 33 -54 56t-81 23q-53 1 -90 -39q-38 -39 -51 -94q-7 -28 -9.5 -52.5t-1.5 -46.5q0 -76 27 -133q15 -32 34.5 -50.5t40 -28
t39 -12t31.5 -2.5q40 0 64.5 4.5t28.5 6.5v120h-104v115h224v-321q-25 -16 -82 -30t-138 -14zM220 710l-64 45l115 130h92l113 -129l-64 -45l-96 96z" />
    <glyph glyph-name="gcircumflex" unicode="&#x11d;" horiz-adv-x="526" 
d="M246 -198q-32 0 -61.5 3.5t-57.5 10.5q-52 13 -70 22l26 109q25 -14 66 -26t87 -12q54 0 79 20t25 52v28q-35 -9 -68 -9q-59 0 -103.5 19.5t-74.5 53t-45.5 78.5t-15.5 96q0 79 36 138q37 60 96.5 84.5t126.5 24.5q52 0 99 -5.5t74 -10.5v-466q0 -103 -56.5 -156.5
t-162.5 -53.5zM290 107q18 0 29.5 4t20.5 9v266q-26 4 -47 4q-59 0 -93 -30q-21 -19 -32.5 -43t-12.5 -42t-1 -28q0 -63 33.5 -101.5t102.5 -38.5zM179 544l-64 45l115 130h92l113 -129l-64 -45l-96 96z" />
    <glyph glyph-name="Gbreve" unicode="&#x11e;" horiz-adv-x="608" 
d="M338 -14q-65 0 -119.5 23t-93.5 67.5t-60.5 108.5t-21.5 146q0 90 23 155.5t62 109t89.5 64.5t106.5 21q88 0 142 -32.5t82 -83.5l-88 -81q-19 33 -54 56t-81 23q-53 1 -90 -39q-38 -39 -51 -94q-7 -28 -9.5 -52.5t-1.5 -46.5q0 -76 27 -133q15 -32 34.5 -50.5t40 -28
t39 -12t31.5 -2.5q40 0 64.5 4.5t28.5 6.5v120h-104v115h224v-321q-25 -16 -82 -30t-138 -14zM327 705q-37 0 -67.5 11.5t-51.5 31.5t-31 46t-10 52h98q6 -28 21 -44.5t41 -16.5t42 16.5t22 44.5h95q-1 -34 -15 -60t-36.5 -44t-50.5 -27.5t-57 -9.5z" />
    <glyph glyph-name="gbreve" unicode="&#x11f;" horiz-adv-x="526" 
d="M246 -198q-32 0 -61.5 3.5t-57.5 10.5q-52 13 -70 22l26 109q25 -14 66 -26t87 -12q54 0 79 20t25 52v28q-35 -9 -68 -9q-59 0 -103.5 19.5t-74.5 53t-45.5 78.5t-15.5 96q0 79 36 138q37 60 96.5 84.5t126.5 24.5q52 0 99 -5.5t74 -10.5v-466q0 -103 -56.5 -156.5
t-162.5 -53.5zM290 107q18 0 29.5 4t20.5 9v266q-26 4 -47 4q-59 0 -93 -30q-21 -19 -32.5 -43t-12.5 -42t-1 -28q0 -63 33.5 -101.5t102.5 -38.5zM291 542q-37 0 -67.5 11.5t-51.5 31.5t-31 46t-10 52h98q6 -28 21 -44.5t41 -16.5t42 16.5t22 44.5h95q-1 -34 -15 -60
t-36.5 -44t-50.5 -27.5t-57 -9.5z" />
    <glyph glyph-name="Gdotaccent" unicode="&#x120;" horiz-adv-x="608" 
d="M338 -14q-65 0 -119.5 23t-93.5 67.5t-60.5 108.5t-21.5 146q0 90 23 155.5t62 109t89.5 64.5t106.5 21q88 0 142 -32.5t82 -83.5l-88 -81q-19 33 -54 56t-81 23q-53 1 -90 -39q-38 -39 -51 -94q-7 -28 -9.5 -52.5t-1.5 -46.5q0 -76 27 -133q15 -32 34.5 -50.5t40 -28
t39 -12t31.5 -2.5q40 0 64.5 4.5t28.5 6.5v120h-104v115h224v-321q-25 -16 -82 -30t-138 -14zM328 720q-30 0 -50 23t-20 54t20 54t50 23q31 0 51 -23t20 -54t-20 -54t-51 -23z" />
    <glyph glyph-name="gdotaccent" unicode="&#x121;" horiz-adv-x="526" 
d="M246 -198q-32 0 -61.5 3.5t-57.5 10.5q-52 13 -70 22l26 109q25 -14 66 -26t87 -12q54 0 79 20t25 52v28q-35 -9 -68 -9q-59 0 -103.5 19.5t-74.5 53t-45.5 78.5t-15.5 96q0 79 36 138q37 60 96.5 84.5t126.5 24.5q52 0 99 -5.5t74 -10.5v-466q0 -103 -56.5 -156.5
t-162.5 -53.5zM290 107q18 0 29.5 4t20.5 9v266q-26 4 -47 4q-59 0 -93 -30q-21 -19 -32.5 -43t-12.5 -42t-1 -28q0 -63 33.5 -101.5t102.5 -38.5zM284 554q-30 0 -50 23t-20 54t20 54t50 23q31 0 51 -23t20 -54t-20 -54t-51 -23z" />
    <glyph glyph-name="Gcommaaccent" unicode="&#x122;" horiz-adv-x="608" 
d="M338 -14q-65 0 -119.5 23t-93.5 67.5t-60.5 108.5t-21.5 146q0 90 23 155.5t62 109t89.5 64.5t106.5 21q88 0 142 -32.5t82 -83.5l-88 -81q-19 33 -54 56t-81 23q-53 1 -90 -39q-38 -39 -51 -94q-7 -28 -9.5 -52.5t-1.5 -46.5q0 -76 27 -133q15 -32 34.5 -50.5t40 -28
t39 -12t31.5 -2.5q40 0 64.5 4.5t28.5 6.5v120h-104v115h224v-321q-25 -16 -82 -30t-138 -14zM230 -181l59 137h110l-81 -137h-88z" />
    <glyph glyph-name="gcommaaccent" unicode="&#x123;" horiz-adv-x="526" 
d="M246 -198q-32 0 -61.5 3.5t-57.5 10.5q-52 13 -70 22l26 109q25 -14 66 -26t87 -12q54 0 79 20t25 52v28q-35 -9 -68 -9q-59 0 -103.5 19.5t-74.5 53t-45.5 78.5t-15.5 96q0 79 36 138q37 60 96.5 84.5t126.5 24.5q52 0 99 -5.5t74 -10.5v-466q0 -103 -56.5 -156.5
t-162.5 -53.5zM290 107q18 0 29.5 4t20.5 9v266q-26 4 -47 4q-59 0 -93 -30q-21 -19 -32.5 -43t-12.5 -42t-1 -28q0 -63 33.5 -101.5t102.5 -38.5zM231 559l85 130h83l-59 -130h-109z" />
    <glyph glyph-name="Hcircumflex" unicode="&#x124;" horiz-adv-x="655" 
d="M73 0v667h128v-264h252v264h128v-667h-128v282h-252v-282h-128zM241 710l-64 45l115 130h92l113 -129l-64 -45l-96 96z" />
    <glyph glyph-name="hcircumflex" unicode="&#x125;" horiz-adv-x="548" 
d="M62 0v667h124v-217q23 18 54.5 31t76.5 13q70 1 111.5 -37t50.5 -87q10 -47 10 -84v-286h-124v277q0 60 -23.5 86.5t-53.5 26.5t-54.5 -13.5t-47.5 -33.5v-343h-124zM134 710l-64 45l115 130h92l113 -129l-64 -45l-96 96z" />
    <glyph glyph-name="Hbar" unicode="&#x126;" horiz-adv-x="655" 
d="M73 0v483h-66v97h66v87h128v-87h252v87h128v-87h66v-97h-66v-483h-128v282h-252v-282h-128zM201 403h252v80h-252v-80z" />
    <glyph glyph-name="hbar" unicode="&#x127;" horiz-adv-x="548" 
d="M62 0v534h-59v87h59v46h124v-46h159v-87h-159v-84q23 18 54.5 31t76.5 13q70 1 111.5 -37t50.5 -87q10 -47 10 -84v-286h-124v277q0 60 -23.5 86.5t-53.5 26.5t-54.5 -13.5t-47.5 -33.5v-343h-124z" />
    <glyph glyph-name="Itilde" unicode="&#x128;" horiz-adv-x="275" 
d="M73 0v667h128v-667h-128zM19 706l-68 38q18 51 49.5 82t73.5 31q26 0 73 -29q40 -28 56 -28q9 0 26 11.5t33 51.5l69 -37q-14 -51 -48 -82.5t-77 -31.5q-28 0 -74 30q-41 27 -55 27q-11 0 -26.5 -11.5t-31.5 -51.5z" />
    <glyph glyph-name="itilde" unicode="&#x129;" horiz-adv-x="247" 
d="M62 0v482h123v-482h-123zM9 543l-68 38q18 51 49.5 82t73.5 31q26 0 73 -29q40 -28 56 -28q9 0 26 11.5t33 51.5l69 -37q-14 -51 -48 -82.5t-77 -31.5q-28 0 -74 30q-41 27 -55 27q-11 0 -26.5 -11.5t-31.5 -51.5z" />
    <glyph glyph-name="Imacron" unicode="&#x12a;" horiz-adv-x="275" 
d="M73 0v667h128v-667h-128zM-27 744v89h345v-89h-345z" />
    <glyph glyph-name="imacron" unicode="&#x12b;" horiz-adv-x="247" 
d="M62 0v482h123v-482h-123zM-43 578v89h345v-89h-345z" />
    <glyph glyph-name="Ibreve" unicode="&#x12c;" horiz-adv-x="275" 
d="M73 0v667h128v-667h-128zM144 705q-37 0 -67.5 11.5t-51.5 31.5t-31 46t-10 52h98q6 -28 21 -44.5t41 -16.5t42 16.5t22 44.5h95q-1 -34 -15 -60t-36.5 -44t-50.5 -27.5t-57 -9.5z" />
    <glyph glyph-name="ibreve" unicode="&#x12d;" horiz-adv-x="247" 
d="M62 0v482h123v-482h-123zM133 539q-37 0 -67.5 11.5t-51.5 31.5t-31 46t-10 52h98q6 -28 21 -44.5t41 -16.5t42 16.5t22 44.5h95q-1 -34 -15 -60t-36.5 -44t-50.5 -27.5t-57 -9.5z" />
    <glyph glyph-name="Iogonek" unicode="&#x12e;" horiz-adv-x="275" 
d="M73 0v667h128v-667h-128zM137 -198q-51 0 -76 24.5t-25 62.5q0 27 11.5 49.5t28.5 40t34 28.5t25 15l72 -12q-29 -20 -52.5 -44t-23.5 -48q0 -13 6.5 -21t21.5 -8q18 0 33 9l23 -75q-19 -11 -38.5 -16t-39.5 -5z" />
    <glyph glyph-name="iogonek" unicode="&#x12f;" horiz-adv-x="247" 
d="M62 0v482h123v-482h-123zM124 527q-38 0 -56.5 22t-18.5 49q0 26 18.5 48t56.5 22q37 0 55.5 -22t18.5 -48q0 -27 -18.5 -49t-55.5 -22zM119 -198q-51 0 -76 24.5t-25 62.5q0 27 11.5 49.5t28.5 40t34 28.5t25 15l72 -12q-29 -20 -52.5 -44t-23.5 -48q0 -13 6.5 -21
t21.5 -8q18 0 33 9l23 -75q-19 -11 -38.5 -16t-39.5 -5z" />
    <glyph glyph-name="Idotaccent" unicode="&#x130;" horiz-adv-x="275" 
d="M73 0v667h128v-667h-128zM144 723q-30 0 -50 23t-20 54t20 54t50 23q31 0 51 -23t20 -54t-20 -54t-51 -23z" />
    <glyph glyph-name="dotlessi" unicode="&#x131;" horiz-adv-x="247" 
d="M62 0v482h123v-482h-123z" />
    <glyph glyph-name="IJ" unicode="&#x132;" horiz-adv-x="657" 
d="M73 0v667h128v-667h-128zM403 -14q-53 0 -93 14.5t-58 25.5l37 107q9 -11 32.5 -20t39 -11t27.5 -2q35 0 55.5 17.5t20.5 66.5v483h128v-484q0 -100 -47.5 -148.5t-141.5 -48.5z" />
    <glyph glyph-name="ij" unicode="&#x133;" horiz-adv-x="479" 
d="M62 0v482h123v-482h-123zM124 527q-38 0 -56.5 22t-18.5 49q0 26 18.5 48t56.5 22q37 0 55.5 -22t18.5 -48q0 -27 -18.5 -49t-55.5 -22zM273 -203l-55 97q42 28 58.5 57t16.5 68v463h124v-463q0 -83 -34.5 -135t-109.5 -87zM356 527q-37 0 -55.5 22t-18.5 49
q0 26 18.5 48t55.5 22q36 0 54.5 -22t18.5 -48q0 -27 -18.5 -49t-54.5 -22z" />
    <glyph glyph-name="Jcircumflex" unicode="&#x134;" horiz-adv-x="425" 
d="M171 -14q-53 0 -93 14.5t-58 25.5l37 107q9 -11 32.5 -20t39 -11t27.5 -2q35 0 55.5 17.5t20.5 66.5v483h128v-484q0 -100 -47.5 -148.5t-141.5 -48.5zM197 710l-64 45l115 130h92l113 -129l-64 -45l-96 96z" />
    <glyph glyph-name="jcircumflex" unicode="&#x135;" horiz-adv-x="257" 
d="M51 -203l-55 97q42 28 58.5 57t16.5 68v463h124v-463q0 -83 -34.5 -135t-109.5 -87zM36 544l-64 45l115 130h92l113 -129l-64 -45l-96 96z" />
    <glyph glyph-name="Kcommaaccent" unicode="&#x136;" horiz-adv-x="589" 
d="M73 0v667h128v-269h67q19 0 33 9.5t29 44.5l95 215h135l-105 -233q-14 -30 -27 -46t-32 -27q23 -12 37.5 -30t25.5 -47l113 -284h-139l-82 208q-16 40 -31 59t-44 19h-75v-286h-128zM197 -181l59 137h110l-81 -137h-88z" />
    <glyph glyph-name="kcommaaccent" unicode="&#x137;" horiz-adv-x="500" 
d="M62 0v667h124v-348h30q18 0 32 10.5t29 45.5l44 107h134l-58 -135q-20 -45 -56 -68q22 -11 36.5 -26.5t24.5 -39.5l88 -213h-135l-55 130q-19 45 -33.5 63t-41.5 18h-39v-211h-124zM155 -181l59 137h110l-81 -137h-88z" />
    <glyph glyph-name="kgreenlandic" unicode="&#x138;" horiz-adv-x="500" 
d="M62 0v482h124v-193h30q18 0 32 10.5t29 45.5l56 137h134l-70 -165q-20 -45 -56 -68q22 -11 36.5 -26.5t24.5 -39.5l77 -183h-139l-40 100q-26 52 -26 54q-7 13 -20.5 20t-28.5 7h-39v-181h-124z" />
    <glyph glyph-name="Lacute" unicode="&#x139;" horiz-adv-x="531" 
d="M73 0v667h128v-546h310v-121h-438zM54 704l-33 54l191 141l59 -83z" />
    <glyph glyph-name="lacute" unicode="&#x13a;" horiz-adv-x="308" 
d="M190 -11q-41 0 -66.5 12.5t-39.5 32t-18 42.5t-4 41v550h124v-526q0 -26 10.5 -35.5t29.5 -9.5t40 9l22 -94q-4 -4 -36 -13q-30 -9 -62 -9zM37 704l-33 54l191 141l59 -83z" />
    <glyph glyph-name="Lcommaaccent" unicode="&#x13b;" horiz-adv-x="531" 
d="M73 0v667h128v-546h310v-121h-438zM187 -181l59 137h110l-81 -137h-88z" />
    <glyph glyph-name="lcommaaccent" unicode="&#x13c;" horiz-adv-x="308" 
d="M190 -11q-41 0 -66.5 12.5t-39.5 32t-18 42.5t-4 41v550h124v-526q0 -26 10.5 -35.5t29.5 -9.5t40 9l22 -94q-4 -4 -36 -13q-30 -9 -62 -9zM65 -181l59 137h110l-81 -137h-88z" />
    <glyph glyph-name="Lcaron" unicode="&#x13d;" horiz-adv-x="531" 
d="M73 0v667h128v-546h310v-121h-438zM274 497v170h107l-34 -170h-73z" />
    <glyph glyph-name="lcaron" unicode="&#x13e;" horiz-adv-x="299" 
d="M190 -11q-41 0 -66.5 12.5t-39.5 32t-18 42.5t-4 41v550h124v-526q0 -26 10.5 -35.5t29.5 -9.5t40 9l22 -94q-4 -4 -36 -13q-30 -9 -62 -9zM242 497v170h107l-34 -170h-73z" />
    <glyph glyph-name="Ldot" unicode="&#x13f;" horiz-adv-x="531" 
d="M73 0v667h128v-546h310v-121h-438zM409 277q-30 0 -50 23t-20 54t20 54t50 23q31 0 51 -23t20 -54t-20 -54t-51 -23z" />
    <glyph glyph-name="ldot" unicode="&#x140;" horiz-adv-x="365" 
d="M190 -11q-41 0 -66.5 12.5t-39.5 32t-18 42.5t-4 41v550h124v-526q0 -26 10.5 -35.5t29.5 -9.5t40 9l22 -94q-4 -4 -36 -13q-30 -9 -62 -9zM308 277q-30 0 -50 23t-20 54t20 54t50 23q31 0 51 -23t20 -54t-20 -54t-51 -23z" />
    <glyph glyph-name="Lslash" unicode="&#x141;" horiz-adv-x="531" 
d="M73 0v251l-55 -32l-48 86l103 62v300h128v-230l92 59l55 -84l-147 -92v-199h310v-121h-438z" />
    <glyph glyph-name="lslash" unicode="&#x142;" horiz-adv-x="331" 
d="M213 -11q-41 0 -66.5 12.5t-39.5 32t-18 42.5t-4 41v136l-58 -33l-42 79l100 59v309h124v-244l85 54l44 -78l-129 -80v-178q0 -26 10.5 -35.5t29.5 -9.5t40 9l22 -94q-4 -4 -36 -13q-30 -9 -62 -9z" />
    <glyph glyph-name="Nacute" unicode="&#x143;" horiz-adv-x="660" 
d="M73 0v667h131l257 -416v416h126v-667h-112l-275 441v-441h-127zM234 704l-33 54l191 141l59 -83z" />
    <glyph glyph-name="nacute" unicode="&#x144;" horiz-adv-x="552" 
d="M62 0v482h113l6 -38q29 24 65.5 37t73.5 13q58 0 99 -26t57.5 -76.5t16.5 -106.5v-285h-124v275q0 55 -22.5 85t-64.5 30q-29 0 -53.5 -15t-42.5 -33v-342h-124zM187 538l-33 54l191 141l59 -83z" />
    <glyph glyph-name="Ncommaaccent" unicode="&#x145;" horiz-adv-x="660" 
d="M73 0v667h131l257 -416v416h126v-667h-112l-275 441v-441h-127zM243 -181l59 137h110l-81 -137h-88z" />
    <glyph glyph-name="ncommaaccent" unicode="&#x146;" horiz-adv-x="552" 
d="M62 0v482h113l6 -38q29 24 65.5 37t73.5 13q58 0 99 -26t57.5 -76.5t16.5 -106.5v-285h-124v275q0 55 -22.5 85t-64.5 30q-29 0 -53.5 -15t-42.5 -33v-342h-124zM175 -181l59 137h110l-81 -137h-88z" />
    <glyph glyph-name="Ncaron" unicode="&#x147;" horiz-adv-x="660" 
d="M73 0v667h131l257 -416v416h126v-667h-112l-275 441v-441h-127zM292 708l-115 126l68 48l92 -95l96 95l66 -41l-115 -133h-92z" />
    <glyph glyph-name="ncaron" unicode="&#x148;" horiz-adv-x="552" 
d="M62 0v482h113l6 -38q29 24 65.5 37t73.5 13q58 0 99 -26t57.5 -76.5t16.5 -106.5v-285h-124v275q0 55 -22.5 85t-64.5 30q-29 0 -53.5 -15t-42.5 -33v-342h-124zM243 544l-115 126l68 48l92 -95l96 95l66 -41l-115 -133h-92z" />
    <glyph glyph-name="napostrophe" unicode="&#x149;" horiz-adv-x="552" 
d="M62 0v482h113l6 -38q29 24 65.5 37t73.5 13q58 0 99 -26t57.5 -76.5t16.5 -106.5v-285h-124v275q0 55 -22.5 85t-64.5 30q-29 0 -53.5 -15t-42.5 -33v-342h-124zM-7 436l-33 45q17 14 32 33.5t17 36.5q-24 2 -42.5 22.5t-18.5 50.5q0 28 18 49.5t52 21.5q35 0 54 -25
t19 -64q0 -37 -22 -82.5t-76 -87.5z" />
    <glyph glyph-name="Eng" unicode="&#x14a;" horiz-adv-x="660" 
d="M441 -203l-53 96q49 32 59 54q11 24 17 71l-264 423v-441h-127v667h131l257 -416v416h126v-648q0 -71 -27 -117.5t-63.5 -70t-55.5 -34.5z" />
    <glyph glyph-name="eng" unicode="&#x14b;" horiz-adv-x="552" 
d="M348 -202l-54 94q42 26 58.5 49t16.5 59v275q0 55 -22.5 85t-64.5 30q-29 0 -53.5 -15t-42.5 -33v-342h-124v482h113l6 -38q29 24 65.5 37t73.5 13q58 0 99 -26t57.5 -76.5t16.5 -106.5v-285q0 -71 -37 -117t-108 -85z" />
    <glyph glyph-name="Omacron" unicode="&#x14c;" horiz-adv-x="672" 
d="M334 -14q-73 0 -127.5 30.5t-91 79.5t-54.5 111.5t-18 126.5t18 126.5t54.5 111.5t91 79t127.5 30q102 0 173 -60t96 -142q26 -86 26 -145q0 -64 -18.5 -126.5t-55 -111.5t-92 -79.5t-129.5 -30.5zM334 105q40 0 74 21.5t51 51t25 57.5q15 55 15 99q0 66 -24 123
q-23 57 -62 81.5t-79 24.5q-39 0 -69.5 -19t-51 -51t-31 -73.5t-10.5 -85.5t10.5 -85.5t31 -73.5t51 -51t69.5 -19zM164 744v89h345v-89h-345z" />
    <glyph glyph-name="omacron" unicode="&#x14d;" horiz-adv-x="528" 
d="M264 -11q-58 0 -101.5 22t-72 58t-43 81.5t-14.5 92.5q0 73 32 135q33 62 87 89t112 27q81 0 137 -44q55 -43 74 -102q20 -62 20 -105q0 -73 -33 -136q-25 -49 -65.5 -77t-75.5 -34.5t-57 -6.5zM264 96q32 -2 54 15.5t34.5 41.5t17.5 49.5t3 40.5q0 54 -22.5 91t-48 46.5
t-38.5 8.5q-42 0 -70 -30q-23 -25 -31 -55.5t-8 -60.5q0 -31 7 -57.5t20.5 -46.5t34 -31.5t47.5 -11.5zM91 578v89h345v-89h-345z" />
    <glyph glyph-name="Obreve" unicode="&#x14e;" horiz-adv-x="672" 
d="M334 -14q-73 0 -127.5 30.5t-91 79.5t-54.5 111.5t-18 126.5t18 126.5t54.5 111.5t91 79t127.5 30q102 0 173 -60t96 -142q26 -86 26 -145q0 -64 -18.5 -126.5t-55 -111.5t-92 -79.5t-129.5 -30.5zM334 105q40 0 74 21.5t51 51t25 57.5q15 55 15 99q0 66 -24 123
q-23 57 -62 81.5t-79 24.5q-39 0 -69.5 -19t-51 -51t-31 -73.5t-10.5 -85.5t10.5 -85.5t31 -73.5t51 -51t69.5 -19zM337 707q-37 0 -67.5 11.5t-51.5 31.5t-31 46t-10 52h98q6 -28 21 -44.5t41 -16.5t42 16.5t22 44.5h95q-1 -34 -15 -60t-36.5 -44t-50.5 -27.5t-57 -9.5z
" />
    <glyph glyph-name="obreve" unicode="&#x14f;" horiz-adv-x="528" 
d="M264 -11q-58 0 -101.5 22t-72 58t-43 81.5t-14.5 92.5q0 73 32 135q33 62 87 89t112 27q81 0 137 -44q55 -43 74 -102q20 -62 20 -105q0 -73 -33 -136q-25 -49 -65.5 -77t-75.5 -34.5t-57 -6.5zM264 96q32 -2 54 15.5t34.5 41.5t17.5 49.5t3 40.5q0 54 -22.5 91t-48 46.5
t-38.5 8.5q-42 0 -70 -30q-23 -25 -31 -55.5t-8 -60.5q0 -31 7 -57.5t20.5 -46.5t34 -31.5t47.5 -11.5zM264 539q-37 0 -67.5 11.5t-51.5 31.5t-31 46t-10 52h98q6 -28 21 -44.5t41 -16.5t42 16.5t22 44.5h95q-1 -34 -15 -60t-36.5 -44t-50.5 -27.5t-57 -9.5z" />
    <glyph glyph-name="Ohungarumlaut" unicode="&#x150;" horiz-adv-x="672" 
d="M334 -14q-73 0 -127.5 30.5t-91 79.5t-54.5 111.5t-18 126.5t18 126.5t54.5 111.5t91 79t127.5 30q102 0 173 -60t96 -142q26 -86 26 -145q0 -64 -18.5 -126.5t-55 -111.5t-92 -79.5t-129.5 -30.5zM334 105q40 0 74 21.5t51 51t25 57.5q15 55 15 99q0 66 -24 123
q-23 57 -62 81.5t-79 24.5q-39 0 -69.5 -19t-51 -51t-31 -73.5t-10.5 -85.5t10.5 -85.5t31 -73.5t51 -51t69.5 -19zM202 707l-37 43l156 151l61 -72zM378 707l-38 43l155 151l63 -72z" />
    <glyph glyph-name="ohungarumlaut" unicode="&#x151;" horiz-adv-x="528" 
d="M264 -11q-58 0 -101.5 22t-72 58t-43 81.5t-14.5 92.5q0 73 32 135q33 62 87 89t112 27q81 0 137 -44q55 -43 74 -102q20 -62 20 -105q0 -73 -33 -136q-25 -49 -65.5 -77t-75.5 -34.5t-57 -6.5zM264 96q32 -2 54 15.5t34.5 41.5t17.5 49.5t3 40.5q0 54 -22.5 91t-48 46.5
t-38.5 8.5q-42 0 -70 -30q-23 -25 -31 -55.5t-8 -60.5q0 -31 7 -57.5t20.5 -46.5t34 -31.5t47.5 -11.5zM134 541l-37 43l156 151l61 -72zM310 541l-38 43l155 151l63 -72z" />
    <glyph glyph-name="OE" unicode="&#x152;" horiz-adv-x="920" 
d="M370 0q-75 0 -136 23.5t-104.5 67t-67 105t-23.5 137.5q0 78 25 139.5t69.5 105t105 66.5t131.5 23h501v-121h-307v-152h254v-121h-254v-152h329v-121h-523zM363 121h73v425h-73q-47 1 -83.5 -18.5t-61 -50t-36.5 -69t-10 -74.5q0 -47 15 -86t41 -67.5t60.5 -44
t74.5 -15.5z" />
    <glyph glyph-name="oe" unicode="&#x153;" horiz-adv-x="828" 
d="M258 -11q-56 0 -98 22t-70.5 58t-42.5 81.5t-14 92.5q0 73 32 135q33 62 87 89t112 27q57 0 97.5 -22t66.5 -60q28 38 67 60t90 22q81 0 132 -45q52 -45 67 -106q15 -60 15 -111v-22h-312q4 -61 38 -88t88 -27q48 0 84 15t49 28l33 -101q-22 -16 -42 -23q-13 -6 -59 -16
q-44 -9 -82 -9q-54 0 -100.5 20.5t-71.5 59.5q-25 -32 -64 -56t-102 -24zM264 96q32 0 54 16.5t34.5 40.5t16.5 49.5t4 40.5q0 54 -22.5 91t-48 46.5t-38.5 8.5q-42 0 -70 -30q-23 -25 -31 -55.5t-8 -60.5q0 -31 7 -57.5t20.5 -46.5t34 -31.5t47.5 -11.5zM490 299h183
q-3 38 -22 60t-38.5 29t-27.5 7q-32 0 -61 -24t-34 -72z" />
    <glyph glyph-name="Racute" unicode="&#x154;" horiz-adv-x="588" 
d="M73 0v667h233q74 0 131 -31t76 -77q17 -44 17 -90q0 -51 -23 -94t-71 -72q28 -14 45 -40t34 -81l50 -182h-133l-50 167q-12 33 -22 54q-7 15 -24.5 22t-39.5 7h-97v-250h-126zM199 365h90q114 0 114 91q0 48 -27 71.5t-87 23.5h-90v-186zM177 704l-33 54l191 141l59 -83z
" />
    <glyph glyph-name="racute" unicode="&#x155;" horiz-adv-x="356" 
d="M62 0v482h113l8 -51q17 20 37.5 32.5t42.5 20.5t39 8q39 0 54 -7l-18 -108q-13 4 -42 4q-35 0 -63 -18.5t-47 -38.5v-324h-124zM125 538l-33 54l191 141l59 -83z" />
    <glyph glyph-name="Rcommaaccent" unicode="&#x156;" horiz-adv-x="588" 
d="M73 0v667h233q74 0 131 -31t76 -77q17 -44 17 -90q0 -51 -23 -94t-71 -72q28 -14 45 -40t34 -81l50 -182h-133l-50 167q-12 33 -22 54q-7 15 -24.5 22t-39.5 7h-97v-250h-126zM199 365h90q114 0 114 91q0 48 -27 71.5t-87 23.5h-90v-186zM204 -181l59 137h110l-81 -137
h-88z" />
    <glyph glyph-name="rcommaaccent" unicode="&#x157;" horiz-adv-x="356" 
d="M62 0v482h113l8 -51q17 20 37.5 32.5t42.5 20.5t39 8q39 0 54 -7l-18 -108q-13 4 -42 4q-35 0 -63 -18.5t-47 -38.5v-324h-124zM17 -181l59 137h110l-81 -137h-88z" />
    <glyph glyph-name="Rcaron" unicode="&#x158;" horiz-adv-x="588" 
d="M73 0v667h233q74 0 131 -31t76 -77q17 -44 17 -90q0 -51 -23 -94t-71 -72q28 -14 45 -40t34 -81l50 -182h-133l-50 167q-12 33 -22 54q-7 15 -24.5 22t-39.5 7h-97v-250h-126zM199 365h90q114 0 114 91q0 48 -27 71.5t-87 23.5h-90v-186zM233 710l-115 126l68 48l92 -95
l96 95l66 -41l-115 -133h-92z" />
    <glyph glyph-name="rcaron" unicode="&#x159;" horiz-adv-x="356" 
d="M62 0v482h113l8 -51q17 20 37.5 32.5t42.5 20.5t39 8q39 0 54 -7l-18 -108q-13 4 -42 4q-35 0 -63 -18.5t-47 -38.5v-324h-124zM158 544l-115 126l68 48l92 -95l96 95l66 -41l-115 -133h-92z" />
    <glyph glyph-name="Sacute" unicode="&#x15a;" horiz-adv-x="521" 
d="M257 -14q-45 0 -83 8t-67 18.5t-48.5 21.5t-28.5 18l54 111q9 -8 25 -19t37.5 -21t49 -16.5t60.5 -6.5q48 0 75.5 21t27.5 56q0 42 -27 65q-29 24 -118 55q-74 21 -117 56q-30 25 -44.5 54t-15.5 50.5t0 37.5q0 63 31 106q31 45 84 62.5t108 17.5q54 0 99.5 -15t76.5 -37
t46 -39l-67 -89q-19 21 -57 39t-64 24.5t-39 5.5q-52 0 -73 -20.5t-21 -49.5q0 -35 24 -53q13 -9 38.5 -21t65.5 -26q89 -31 133 -69q44 -37 53 -75q8 -35 11 -74q0 -68 -41 -118q-41 -48 -94 -63t-94 -15zM171 704l-33 54l191 141l59 -83z" />
    <glyph glyph-name="sacute" unicode="&#x15b;" horiz-adv-x="461" 
d="M238 -11q-26 0 -52.5 3.5t-54.5 10.5q-54 14 -76 28t-27 22l45 95q19 -20 54 -37q35 -15 63.5 -20t47.5 -5q45 0 62.5 13t17.5 35q0 19 -23 34t-89 32q-47 10 -77.5 25t-48.5 34t-24.5 42t-5.5 49q0 64 47.5 104t144.5 40q63 0 113 -18q51 -17 71 -34l-42 -90
q-19 15 -56 31t-85 16q-44 0 -59.5 -14t-15.5 -34q0 -24 18 -34q19 -10 80 -22q74 -13 113 -44t47 -61t8 -50q0 -60 -37 -97q-38 -36 -84.5 -45t-74.5 -9zM162 538l-33 54l191 141l59 -83z" />
    <glyph glyph-name="Scircumflex" unicode="&#x15c;" horiz-adv-x="521" 
d="M257 -14q-45 0 -83 8t-67 18.5t-48.5 21.5t-28.5 18l54 111q9 -8 25 -19t37.5 -21t49 -16.5t60.5 -6.5q48 0 75.5 21t27.5 56q0 42 -27 65q-29 24 -118 55q-74 21 -117 56q-30 25 -44.5 54t-15.5 50.5t0 37.5q0 63 31 106q31 45 84 62.5t108 17.5q54 0 99.5 -15t76.5 -37
t46 -39l-67 -89q-19 21 -57 39t-64 24.5t-39 5.5q-52 0 -73 -20.5t-21 -49.5q0 -35 24 -53q13 -9 38.5 -21t65.5 -26q89 -31 133 -69q44 -37 53 -75q8 -35 11 -74q0 -68 -41 -118q-41 -48 -94 -63t-94 -15zM168 711l-64 45l115 130h92l113 -129l-64 -45l-96 96z" />
    <glyph glyph-name="scircumflex" unicode="&#x15d;" horiz-adv-x="461" 
d="M238 -11q-26 0 -52.5 3.5t-54.5 10.5q-54 14 -76 28t-27 22l45 95q19 -20 54 -37q35 -15 63.5 -20t47.5 -5q45 0 62.5 13t17.5 35q0 19 -23 34t-89 32q-47 10 -77.5 25t-48.5 34t-24.5 42t-5.5 49q0 64 47.5 104t144.5 40q63 0 113 -18q51 -17 71 -34l-42 -90
q-19 15 -56 31t-85 16q-44 0 -59.5 -14t-15.5 -34q0 -24 18 -34q19 -10 80 -22q74 -13 113 -44t47 -61t8 -50q0 -60 -37 -97q-38 -36 -84.5 -45t-74.5 -9zM152 544l-64 45l115 130h92l113 -129l-64 -45l-96 96z" />
    <glyph glyph-name="Scedilla" unicode="&#x15e;" horiz-adv-x="521" 
d="M257 -14q-45 0 -83 8t-67 18.5t-48.5 21.5t-28.5 18l54 111q9 -8 25 -19t37.5 -21t49 -16.5t60.5 -6.5q48 0 75.5 21t27.5 56q0 42 -27 65q-29 24 -118 55q-74 21 -117 56q-30 25 -44.5 54t-15.5 50.5t0 37.5q0 63 31 106q31 45 84 62.5t108 17.5q54 0 99.5 -15t76.5 -37
t46 -39l-67 -89q-19 21 -57 39t-64 24.5t-39 5.5q-52 0 -73 -20.5t-21 -49.5q0 -35 24 -53q13 -9 38.5 -21t65.5 -26q89 -31 133 -69q44 -37 53 -75q8 -35 11 -74q0 -68 -41 -118q-41 -48 -94 -63t-94 -15zM148 -181l59 137h110l-81 -137h-88z" />
    <glyph glyph-name="scedilla" unicode="&#x15f;" horiz-adv-x="461" 
d="M238 -11q-26 0 -52.5 3.5t-54.5 10.5q-54 14 -76 28t-27 22l45 95q19 -20 54 -37q35 -15 63.5 -20t47.5 -5q45 0 62.5 13t17.5 35q0 19 -23 34t-89 32q-47 10 -77.5 25t-48.5 34t-24.5 42t-5.5 49q0 64 47.5 104t144.5 40q63 0 113 -18q51 -17 71 -34l-42 -90
q-19 15 -56 31t-85 16q-44 0 -59.5 -14t-15.5 -34q0 -24 18 -34q19 -10 80 -22q74 -13 113 -44t47 -61t8 -50q0 -60 -37 -97q-38 -36 -84.5 -45t-74.5 -9zM121 -181l59 137h110l-81 -137h-88z" />
    <glyph glyph-name="Scaron" unicode="&#x160;" horiz-adv-x="521" 
d="M257 -14q-45 0 -83 8t-67 18.5t-48.5 21.5t-28.5 18l54 111q9 -8 25 -19t37.5 -21t49 -16.5t60.5 -6.5q48 0 75.5 21t27.5 56q0 42 -27 65q-29 24 -118 55q-74 21 -117 56q-30 25 -44.5 54t-15.5 50.5t0 37.5q0 63 31 106q31 45 84 62.5t108 17.5q54 0 99.5 -15t76.5 -37
t46 -39l-67 -89q-19 21 -57 39t-64 24.5t-39 5.5q-52 0 -73 -20.5t-21 -49.5q0 -35 24 -53q13 -9 38.5 -21t65.5 -26q89 -31 133 -69q44 -37 53 -75q8 -35 11 -74q0 -68 -41 -118q-41 -48 -94 -63t-94 -15zM224 710l-115 126l68 48l92 -95l96 95l66 -41l-115 -133h-92z" />
    <glyph glyph-name="scaron" unicode="&#x161;" horiz-adv-x="461" 
d="M238 -11q-26 0 -52.5 3.5t-54.5 10.5q-54 14 -76 28t-27 22l45 95q19 -20 54 -37q35 -15 63.5 -20t47.5 -5q45 0 62.5 13t17.5 35q0 19 -23 34t-89 32q-47 10 -77.5 25t-48.5 34t-24.5 42t-5.5 49q0 64 47.5 104t144.5 40q63 0 113 -18q51 -17 71 -34l-42 -90
q-19 15 -56 31t-85 16q-44 0 -59.5 -14t-15.5 -34q0 -24 18 -34q19 -10 80 -22q74 -13 113 -44t47 -61t8 -50q0 -60 -37 -97q-38 -36 -84.5 -45t-74.5 -9zM200 544l-115 126l68 48l92 -95l96 95l66 -41l-115 -133h-92z" />
    <glyph glyph-name="Tcommaaccent" unicode="&#x162;" horiz-adv-x="516" 
d="M194 0v546h-189v121h506v-121h-189v-546h-128zM140 -181l59 137h110l-81 -137h-88z" />
    <glyph glyph-name="tcommaaccent" unicode="&#x163;" horiz-adv-x="350" 
d="M208 -11q-64 0 -95 36t-31 87v270h-68v100h68v125h123v-125h111v-100h-111v-245q0 -23 11 -32t26 -9q37 0 64 21l32 -89q-23 -17 -57.5 -28t-72.5 -11zM96 -181l59 137h110l-81 -137h-88z" />
    <glyph glyph-name="Tcaron" unicode="&#x164;" horiz-adv-x="516" 
d="M194 0v546h-189v121h506v-121h-189v-546h-128zM214 710l-115 126l68 48l92 -95l96 95l66 -41l-115 -133h-92z" />
    <glyph glyph-name="tcaron" unicode="&#x165;" horiz-adv-x="350" 
d="M208 -11q-64 0 -95 36t-31 87v270h-68v100h68v125h123v-125h111v-100h-111v-245q0 -23 11 -32t26 -9q37 0 64 21l32 -89q-23 -17 -57.5 -28t-72.5 -11zM265 538v170h107l-34 -170h-73z" />
    <glyph glyph-name="Tbar" unicode="&#x166;" horiz-adv-x="516" 
d="M194 0v276h-146v97h146v173h-189v121h506v-121h-188v-173h143v-97h-143v-276h-129z" />
    <glyph glyph-name="tbar" unicode="&#x167;" horiz-adv-x="350" 
d="M208 -11q-33 0 -57 10t-39 27.5t-22.5 41t-7.5 49.5v90h-68v101h68v74h-68v100h68v125h123v-125h111v-100h-111v-74h111v-101h-111v-71q0 -23 11.5 -31.5t24.5 -8.5q36 0 65 21l32 -90q-27 -17 -58.5 -27.5t-71.5 -10.5z" />
    <glyph glyph-name="Utilde" unicode="&#x168;" horiz-adv-x="653" 
d="M326 -14q-85 0 -146 37q-62 38 -86.5 93.5t-24.5 112.5v438h128v-425q0 -60 33.5 -98.5t96.5 -38.5t95.5 38.5t32.5 98.5v425h128v-438q0 -57 -20 -102t-55 -76.5t-81.5 -48t-100.5 -16.5zM205 706l-68 38q18 51 49.5 82t73.5 31q26 0 73 -29q40 -28 56 -28q9 0 26 11.5
t33 51.5l69 -37q-14 -51 -48 -82.5t-77 -31.5q-28 0 -74 30q-41 27 -55 27q-11 0 -26.5 -11.5t-31.5 -51.5z" />
    <glyph glyph-name="utilde" unicode="&#x169;" horiz-adv-x="550" 
d="M232 -10q-85 0 -129 51t-44 154v287h124v-274q0 -50 18 -81t62 -31q36 0 61 14.5t40 27.5v344h124v-482h-114l-4 38q-33 -28 -68.5 -38t-69.5 -10zM152 540l-68 38q18 51 49.5 82t73.5 31q26 0 73 -29q40 -28 56 -28q9 0 26 11.5t33 51.5l69 -37q-14 -51 -48 -82.5
t-77 -31.5q-28 0 -74 30q-41 27 -55 27q-11 0 -26.5 -11.5t-31.5 -51.5z" />
    <glyph glyph-name="Umacron" unicode="&#x16a;" horiz-adv-x="653" 
d="M326 -14q-85 0 -146 37q-62 38 -86.5 93.5t-24.5 112.5v438h128v-425q0 -60 33.5 -98.5t96.5 -38.5t95.5 38.5t32.5 98.5v425h128v-438q0 -57 -20 -102t-55 -76.5t-81.5 -48t-100.5 -16.5zM155 744v89h345v-89h-345z" />
    <glyph glyph-name="umacron" unicode="&#x16b;" horiz-adv-x="550" 
d="M232 -10q-85 0 -129 51t-44 154v287h124v-274q0 -50 18 -81t62 -31q36 0 61 14.5t40 27.5v344h124v-482h-114l-4 38q-33 -28 -68.5 -38t-69.5 -10zM103 578v89h345v-89h-345z" />
    <glyph glyph-name="Ubreve" unicode="&#x16c;" horiz-adv-x="653" 
d="M326 -14q-85 0 -146 37q-62 38 -86.5 93.5t-24.5 112.5v438h128v-425q0 -60 33.5 -98.5t96.5 -38.5t95.5 38.5t32.5 98.5v425h128v-438q0 -57 -20 -102t-55 -76.5t-81.5 -48t-100.5 -16.5zM335 705q-37 0 -67.5 11.5t-51.5 31.5t-31 46t-10 52h98q6 -28 21 -44.5
t41 -16.5t42 16.5t22 44.5h95q-1 -34 -15 -60t-36.5 -44t-50.5 -27.5t-57 -9.5z" />
    <glyph glyph-name="ubreve" unicode="&#x16d;" horiz-adv-x="550" 
d="M232 -10q-85 0 -129 51t-44 154v287h124v-274q0 -50 18 -81t62 -31q36 0 61 14.5t40 27.5v344h124v-482h-114l-4 38q-33 -28 -68.5 -38t-69.5 -10zM281 539q-37 0 -67.5 11.5t-51.5 31.5t-31 46t-10 52h98q6 -28 21 -44.5t41 -16.5t42 16.5t22 44.5h95q-1 -34 -15 -60
t-36.5 -44t-50.5 -27.5t-57 -9.5z" />
    <glyph glyph-name="Uring" unicode="&#x16e;" horiz-adv-x="653" 
d="M326 -14q-85 0 -146 37q-62 38 -86.5 93.5t-24.5 112.5v438h128v-425q0 -60 33.5 -98.5t96.5 -38.5t95.5 38.5t32.5 98.5v425h128v-438q0 -57 -20 -102t-55 -76.5t-81.5 -48t-100.5 -16.5zM332 695q-45 0 -74.5 30t-29.5 78q0 26 9.5 46.5t25 34.5t34.5 21t39 7
q44 0 72.5 -31t28.5 -77q0 -22 -7.5 -42t-21.5 -35t-33.5 -23.5t-42.5 -8.5zM332 758q19 0 29.5 13t10.5 33t-10.5 33t-29.5 13q-18 0 -28.5 -13t-10.5 -33t10.5 -33t28.5 -13z" />
    <glyph glyph-name="uring" unicode="&#x16f;" horiz-adv-x="550" 
d="M232 -10q-85 0 -129 51t-44 154v287h124v-274q0 -50 18 -81t62 -31q36 0 61 14.5t40 27.5v344h124v-482h-114l-4 38q-33 -28 -68.5 -38t-69.5 -10zM281 529q-45 0 -74.5 30t-29.5 78q0 26 9.5 46.5t25 34.5t34.5 21t39 7q44 0 72.5 -31t28.5 -77q0 -22 -7.5 -42
t-21.5 -35t-33.5 -23.5t-42.5 -8.5zM281 592q19 0 29.5 13t10.5 33t-10.5 33t-29.5 13q-18 0 -28.5 -13t-10.5 -33t10.5 -33t28.5 -13z" />
    <glyph glyph-name="Uhungarumlaut" unicode="&#x170;" horiz-adv-x="653" 
d="M326 -14q-85 0 -146 37q-62 38 -86.5 93.5t-24.5 112.5v438h128v-425q0 -60 33.5 -98.5t96.5 -38.5t95.5 38.5t32.5 98.5v425h128v-438q0 -57 -20 -102t-55 -76.5t-81.5 -48t-100.5 -16.5zM199 707l-37 43l156 151l61 -72zM375 707l-38 43l155 151l63 -72z" />
    <glyph glyph-name="uhungarumlaut" unicode="&#x171;" horiz-adv-x="550" 
d="M232 -10q-85 0 -129 51t-44 154v287h124v-274q0 -50 18 -81t62 -31q36 0 61 14.5t40 27.5v344h124v-482h-114l-4 38q-33 -28 -68.5 -38t-69.5 -10zM140 541l-37 43l156 151l61 -72zM316 541l-38 43l155 151l63 -72z" />
    <glyph glyph-name="Uogonek" unicode="&#x172;" horiz-adv-x="653" 
d="M326 -14q-85 0 -146 37q-62 38 -86.5 93.5t-24.5 112.5v438h128v-425q0 -60 33.5 -98.5t96.5 -38.5t95.5 38.5t32.5 98.5v425h128v-438q0 -57 -20 -102t-55 -76.5t-81.5 -48t-100.5 -16.5zM376 -198q-51 0 -76 24.5t-25 62.5q0 27 11.5 49.5t28.5 40t34 28.5t25 15
l72 -12q-29 -20 -52.5 -44t-23.5 -48q0 -13 6.5 -21t21.5 -8q18 0 33 9l23 -75q-19 -11 -38.5 -16t-39.5 -5z" />
    <glyph glyph-name="uogonek" unicode="&#x173;" horiz-adv-x="550" 
d="M232 -10q-85 0 -129 51t-44 154v287h124v-274q0 -50 18 -81t62 -31q36 0 61 14.5t40 27.5v344h124v-482h-114l-4 38q-33 -28 -68.5 -38t-69.5 -10zM410 -198q-51 0 -76 24.5t-25 62.5q0 27 11.5 49.5t28.5 40t34 28.5t25 15l72 -12q-29 -20 -52.5 -44t-23.5 -48
q0 -13 6.5 -21t21.5 -8q18 0 33 9l23 -75q-19 -11 -38.5 -16t-39.5 -5z" />
    <glyph glyph-name="Wcircumflex" unicode="&#x174;" horiz-adv-x="759" 
d="M136 0l-126 667h127l76 -450l108 450h117l113 -450l75 450h123l-127 -667h-127l-118 480l-113 -480h-128zM283 710l-64 45l115 130h92l113 -129l-64 -45l-96 96z" />
    <glyph glyph-name="wcircumflex" unicode="&#x175;" horiz-adv-x="681" 
d="M158 0l-158 482h129l83 -305l78 305h100l79 -305l83 305h129l-158 -482h-100l-83 314l-81 -314h-101zM246 544l-64 45l115 130h92l113 -129l-64 -45l-96 96z" />
    <glyph glyph-name="Ycircumflex" unicode="&#x176;" horiz-adv-x="552" 
d="M214 0v268l-214 399h143l137 -265l131 265h141l-210 -399v-268h-128zM186 710l-64 45l115 130h92l113 -129l-64 -45l-96 96z" />
    <glyph glyph-name="ycircumflex" unicode="&#x177;" horiz-adv-x="503" 
d="M37 -191l-11 111q54 0 96.5 13t65.5 42q4 5 7 11t7 12l-198 484h133l134 -370q43 114 58 171q38 140 52 199h122q-37 -147 -78.5 -266t-83.5 -204q-40 -76 -72 -118q-32 -39 -93 -61.5t-139 -23.5zM175 544l-64 45l115 130h92l113 -129l-64 -45l-96 96z" />
    <glyph glyph-name="Ydieresis" unicode="&#x178;" horiz-adv-x="552" 
d="M214 0v268l-214 399h143l137 -265l131 265h141l-210 -399v-268h-128zM189 726q-35 0 -52.5 20.5t-17.5 45.5q0 24 17.5 44.5t52.5 20.5q34 0 51 -20.5t17 -44.5q0 -25 -17 -45.5t-51 -20.5zM397 726q-35 0 -52.5 20.5t-17.5 45.5q0 24 17.5 44.5t52.5 20.5q34 0 51 -20.5
t17 -44.5q0 -25 -17 -45.5t-51 -20.5z" />
    <glyph glyph-name="Zacute" unicode="&#x179;" horiz-adv-x="538" 
d="M25 0v110l323 436h-296v121h453v-114l-320 -432h326v-121h-486zM203 704l-33 54l191 141l59 -83z" />
    <glyph glyph-name="zacute" unicode="&#x17a;" horiz-adv-x="483" 
d="M34 0v89l262 293h-251v100h394v-94l-258 -289h266v-99h-413zM174 538l-33 54l191 141l59 -83z" />
    <glyph glyph-name="Zdotaccent" unicode="&#x17b;" horiz-adv-x="538" 
d="M25 0v110l323 436h-296v121h453v-114l-320 -432h326v-121h-486zM288 720q-30 0 -50 23t-20 54t20 54t50 23q31 0 51 -23t20 -54t-20 -54t-51 -23z" />
    <glyph glyph-name="zdotaccent" unicode="&#x17c;" horiz-adv-x="483" 
d="M34 0v89l262 293h-251v100h394v-94l-258 -289h266v-99h-413zM250 554q-30 0 -50 23t-20 54t20 54t50 23q31 0 51 -23t20 -54t-20 -54t-51 -23z" />
    <glyph glyph-name="Zcaron" unicode="&#x17d;" horiz-adv-x="538" 
d="M25 0v110l323 436h-296v121h453v-114l-320 -432h326v-121h-486zM240 710l-115 126l68 48l92 -95l96 95l66 -41l-115 -133h-92z" />
    <glyph glyph-name="zcaron" unicode="&#x17e;" horiz-adv-x="483" 
d="M34 0v89l262 293h-251v100h394v-94l-258 -289h266v-99h-413zM207 544l-115 126l68 48l92 -95l96 95l66 -41l-115 -133h-92z" />
    <glyph glyph-name="longs" unicode="&#x17f;" horiz-adv-x="318" 
d="M78 0v508q0 70 30 111q23 33 57 47t73 14q45 0 74.5 -8.5t34.5 -11.5l-14 -103q-6 3 -23.5 8.5t-46.5 5.5q-26 0 -43 -15.5t-17 -50.5v-505h-125z" />
    <glyph glyph-name="florin" unicode="&#x192;" 
d="M-20 -149v116q51 2 86.5 26.5t48.5 81.5l57 249h-77v114h102q18 97 63 148t98 63q54 12 96 12v-115q-67 0 -93 -26q-24 -24 -41 -82h134v-114h-161l-51 -236q-19 -89 -55 -142q-25 -39 -68 -61.5t-81 -28t-58 -5.5z" />
    <glyph glyph-name="Scommaaccent" unicode="&#x218;" horiz-adv-x="521" 
d="M257 -14q-45 0 -83 8t-67 18.5t-48.5 21.5t-28.5 18l54 111q9 -8 25 -19t37.5 -21t49 -16.5t60.5 -6.5q48 0 75.5 21t27.5 56q0 42 -27 65q-29 24 -118 55q-74 21 -117 56q-30 25 -44.5 54t-15.5 50.5t0 37.5q0 63 31 106q31 45 84 62.5t108 17.5q54 0 99.5 -15t76.5 -37
t46 -39l-67 -89q-19 21 -57 39t-64 24.5t-39 5.5q-52 0 -73 -20.5t-21 -49.5q0 -35 24 -53q13 -9 38.5 -21t65.5 -26q89 -31 133 -69q44 -37 53 -75q8 -35 11 -74q0 -68 -41 -118q-41 -48 -94 -63t-94 -15zM148 -181l59 137h110l-81 -137h-88z" />
    <glyph glyph-name="scommaaccent" unicode="&#x219;" horiz-adv-x="461" 
d="M238 -11q-26 0 -52.5 3.5t-54.5 10.5q-54 14 -76 28t-27 22l45 95q19 -20 54 -37q35 -15 63.5 -20t47.5 -5q45 0 62.5 13t17.5 35q0 19 -23 34t-89 32q-47 10 -77.5 25t-48.5 34t-24.5 42t-5.5 49q0 64 47.5 104t144.5 40q63 0 113 -18q51 -17 71 -34l-42 -90
q-19 15 -56 31t-85 16q-44 0 -59.5 -14t-15.5 -34q0 -24 18 -34q19 -10 80 -22q74 -13 113 -44t47 -61t8 -50q0 -60 -37 -97q-38 -36 -84.5 -45t-74.5 -9zM121 -181l59 137h110l-81 -137h-88z" />
    <glyph glyph-name="uni021A" unicode="&#x21a;" horiz-adv-x="516" 
d="M194 0v546h-189v121h506v-121h-189v-546h-128zM140 -181l59 137h110l-81 -137h-88z" />
    <glyph glyph-name="uni021B" unicode="&#x21b;" horiz-adv-x="350" 
d="M208 -11q-64 0 -95 36t-31 87v270h-68v100h68v125h123v-125h111v-100h-111v-245q0 -23 11 -32t26 -9q37 0 64 21l32 -89q-23 -17 -57.5 -28t-72.5 -11zM96 -181l59 137h110l-81 -137h-88z" />
    <glyph glyph-name="dotlessj" unicode="&#x237;" horiz-adv-x="257" 
d="M51 -203l-55 97q42 28 58.5 57t16.5 68v463h124v-463q0 -83 -34.5 -135t-109.5 -87z" />
    <glyph glyph-name="circumflex" unicode="&#x2c6;" horiz-adv-x="442" 
d="M113 544l-64 45l115 130h92l113 -129l-64 -45l-96 96z" />
    <glyph glyph-name="caron" unicode="&#x2c7;" horiz-adv-x="442" 
d="M164 544l-115 126l68 48l92 -95l96 95l66 -41l-115 -133h-92z" />
    <glyph glyph-name="uni02C9" unicode="&#x2c9;" horiz-adv-x="442" 
d="M38 578v89h345v-89h-345z" />
    <glyph glyph-name="breve" unicode="&#x2d8;" horiz-adv-x="442" 
d="M210 539q-37 0 -67.5 11.5t-51.5 31.5t-31 46t-10 52h98q6 -28 21 -44.5t41 -16.5t42 16.5t22 44.5h95q-1 -34 -15 -60t-36.5 -44t-50.5 -27.5t-57 -9.5z" />
    <glyph glyph-name="dotaccent" unicode="&#x2d9;" horiz-adv-x="442" 
d="M221 554q-30 0 -50 23t-20 54t20 54t50 23q31 0 51 -23t20 -54t-20 -54t-51 -23z" />
    <glyph glyph-name="ring" unicode="&#x2da;" horiz-adv-x="442" 
d="M221 529q-45 0 -74.5 30t-29.5 78q0 26 9.5 46.5t25 34.5t34.5 21t39 7q44 0 72.5 -31t28.5 -77q0 -22 -7.5 -42t-21.5 -35t-33.5 -23.5t-42.5 -8.5zM221 592q19 0 29.5 13t10.5 33t-10.5 33t-29.5 13q-18 0 -28.5 -13t-10.5 -33t10.5 -33t28.5 -13z" />
    <glyph glyph-name="ogonek" unicode="&#x2db;" horiz-adv-x="442" 
d="M186 -198q-51 0 -76 24.5t-25 62.5q0 27 11.5 49.5t28.5 40t34 28.5t25 15l72 -12q-29 -20 -52.5 -44t-23.5 -48q0 -13 6.5 -21t21.5 -8q18 0 33 9l23 -75q-19 -11 -38.5 -16t-39.5 -5z" />
    <glyph glyph-name="tilde" unicode="&#x2dc;" horiz-adv-x="442" 
d="M89 540l-68 38q18 51 49.5 82t73.5 31q26 0 73 -29q40 -28 56 -28q9 0 26 11.5t33 51.5l69 -37q-14 -51 -48 -82.5t-77 -31.5q-28 0 -74 30q-41 27 -55 27q-11 0 -26.5 -11.5t-31.5 -51.5z" />
    <glyph glyph-name="hungarumlaut" unicode="&#x2dd;" horiz-adv-x="442" 
d="M51 541l-37 43l156 151l61 -72zM227 541l-38 43l155 151l63 -72z" />
    <glyph glyph-name="tonos" unicode="&#x384;" horiz-adv-x="442" 
d="M207 523l-68 34l101 228l103 -47z" />
    <glyph glyph-name="dieresistonos" unicode="&#x385;" horiz-adv-x="442" 
d="M206 540l-59 28l100 225l96 -39zM57 560q-29 0 -47 21.5t-18 49.5q0 29 18 50t47 21q28 0 46.5 -21t18.5 -50q0 -28 -18.5 -49.5t-46.5 -21.5zM393 560q-28 0 -46.5 21.5t-18.5 49.5q0 29 18.5 50t46.5 21q29 0 47 -21t18 -50q0 -28 -18 -49.5t-47 -21.5z" />
    <glyph glyph-name="Alphatonos" unicode="&#x386;" horiz-adv-x="608" 
d="M53 403l-74 24l64 241l110 -32zM0 0l240 667h127l241 -667h-131l-46 134h-255l-45 -134h-131zM215 247h178l-89 263z" />
    <glyph glyph-name="Epsilontonos" unicode="&#x388;" horiz-adv-x="625" 
d="M14 403l-74 24l64 241l110 -32zM141 0v667h435v-121h-307v-152h254v-121h-254v-152h329v-121h-457z" />
    <glyph glyph-name="Etatonos" unicode="&#x389;" horiz-adv-x="739" 
d="M12 403l-74 24l64 241l110 -32zM157 0v667h128v-264h252v264h128v-667h-128v282h-252v-282h-128z" />
    <glyph glyph-name="Iotatonos" unicode="&#x38a;" horiz-adv-x="349" 
d="M13 403l-74 24l64 241l110 -32zM147 0v667h128v-667h-128z" />
    <glyph glyph-name="Omicrontonos" unicode="&#x38c;" horiz-adv-x="724" 
d="M13 403l-74 24l64 241l110 -32zM386 -14q-73 0 -127.5 30.5t-91 79.5t-54.5 111.5t-18 126.5t18 126.5t54.5 111.5t91 79t127.5 30q102 0 173 -60t96 -142q26 -86 26 -145q0 -64 -18.5 -126.5t-55 -111.5t-92 -79.5t-129.5 -30.5zM386 105q40 0 74 21.5t51 51t25 57.5
q15 55 15 99q0 66 -24 123q-23 57 -62 81.5t-79 24.5q-39 0 -69.5 -19t-51 -51t-31 -73.5t-10.5 -85.5t10.5 -85.5t31 -73.5t51 -51t69.5 -19z" />
    <glyph glyph-name="Upsilontonos" unicode="&#x38e;" horiz-adv-x="701" 
d="M15 403l-74 24l64 241l110 -32zM349 0v156q0 43 -5 81q-6 43 -27 91t-52 94.5t-68 87t-76 69.5l84 94q52 -37 99.5 -94.5t75.5 -113t36 -89.5q16 59 58 127q43 69 82 109.5t70 61.5l75 -86q-34 -28 -62.5 -61.5t-60.5 -78.5q-32 -44 -54 -90q-23 -45 -34 -77t-13 -57
q-5 -53 -5 -94v-130h-123z" />
    <glyph glyph-name="Omegatonos" unicode="&#x38f;" horiz-adv-x="718" 
d="M15 403l-74 24l64 241l110 -32zM109 0v120h86q-39 38 -67 100.5t-28 150.5q0 102 44 176q46 75 112 104q67 30 132 30q99 0 165 -48q66 -45 92 -119q26 -73 26 -140q0 -63 -21.5 -123t-45 -91t-36.5 -40h94v-120h-226v114q54 43 82.5 103t28.5 135q0 103 -44.5 157
t-116.5 54q-73 0 -117 -54t-44 -157q0 -45 10.5 -82.5t27 -66.5t36 -51.5t36.5 -37.5v-114h-226z" />
    <glyph glyph-name="iotadieresistonos" unicode="&#x390;" horiz-adv-x="293" 
d="M183 -11q-57 0 -89 31t-32 94v368h123v-336q0 -33 10 -44t28 -11q24 0 51 13l14 -95q-17 -8 -48 -14t-57 -6zM121 540l-59 28l100 225l96 -39zM-28 560q-29 0 -47 21.5t-18 49.5q0 29 18 50t47 21q28 0 46.5 -21t18.5 -50q0 -28 -18.5 -49.5t-46.5 -21.5zM308 560
q-28 0 -46.5 21.5t-18.5 49.5q0 29 18.5 50t46.5 21q29 0 47 -21t18 -50q0 -28 -18 -49.5t-47 -21.5z" />
    <glyph glyph-name="Alpha" unicode="&#x391;" horiz-adv-x="608" 
d="M0 0l240 667h127l241 -667h-131l-46 134h-255l-45 -134h-131zM215 247h178l-89 263z" />
    <glyph glyph-name="Beta" unicode="&#x392;" horiz-adv-x="568" 
d="M73 0v667h226q73 0 126 -33q51 -32 68 -75q15 -41 15 -72q0 -45 -20 -78t-57 -56q51 -19 82 -60.5t31 -104.5q0 -53 -28.5 -101t-86.5 -71q-21 -8 -46.5 -12t-49.5 -4h-260zM200 114h91q33 0 52 3t31 10q21 12 30.5 29.5t9.5 36.5q0 22 -12 43t-35 34q-15 8 -35 11.5
t-43 3.5h-89v-171zM200 394h62q54 0 71 9q24 12 35 30.5t11 38.5q0 24 -15 44.5t-44 30.5q-2 0 -9.5 1t-17.5 2t-21 2t-20 1h-52v-159z" />
    <glyph glyph-name="Gamma" unicode="&#x393;" horiz-adv-x="484" 
d="M73 0v667h406v-118h-278v-549h-128z" />
    <glyph glyph-name="uni0394" unicode="&#x394;" horiz-adv-x="594" 
d="M0 0l236 667h118l240 -667h-594zM163 107h262l-132 407z" />
    <glyph glyph-name="Epsilon" unicode="&#x395;" horiz-adv-x="557" 
d="M73 0v667h435v-121h-307v-152h254v-121h-254v-152h329v-121h-457z" />
    <glyph glyph-name="Zeta" unicode="&#x396;" horiz-adv-x="538" 
d="M25 0v110l323 436h-296v121h453v-114l-320 -432h326v-121h-486z" />
    <glyph glyph-name="Eta" unicode="&#x397;" horiz-adv-x="655" 
d="M73 0v667h128v-264h252v264h128v-667h-128v282h-252v-282h-128z" />
    <glyph glyph-name="Theta" unicode="&#x398;" horiz-adv-x="672" 
d="M334 -14q-73 0 -127.5 30.5t-91 79.5t-54.5 111.5t-18 126.5t18 126.5t54.5 111.5t91 79t127.5 30q102 0 173 -60t96 -142q26 -86 26 -145q0 -64 -18.5 -126.5t-55 -111.5t-92 -79.5t-129.5 -30.5zM334 105q40 0 74 21.5t51 51t25 57.5q15 55 15 99q0 66 -24 123
q-23 57 -62 81.5t-79 24.5q-39 0 -69.5 -19t-51 -51t-31 -73.5t-10.5 -85.5t10.5 -85.5t31 -73.5t51 -51t69.5 -19zM217 276v115h235v-115h-235z" />
    <glyph glyph-name="Iota" unicode="&#x399;" horiz-adv-x="275" 
d="M73 0v667h128v-667h-128z" />
    <glyph glyph-name="Kappa" unicode="&#x39a;" horiz-adv-x="609" 
d="M73 0v667h128v-255l244 255h152l-249 -260l261 -407h-144l-205 316l-59 -62v-254h-128z" />
    <glyph glyph-name="Lambda" unicode="&#x39b;" horiz-adv-x="600" 
d="M0 0l244 667h108l248 -667h-131l-171 491l-167 -491h-131z" />
    <glyph glyph-name="Mu" unicode="&#x39c;" horiz-adv-x="750" 
d="M340 -4l-138 401l-18 -397h-127l32 667h131l163 -474l146 474h132l32 -667h-127l-18 393l-123 -397h-85z" />
    <glyph glyph-name="Nu" unicode="&#x39d;" horiz-adv-x="660" 
d="M73 0v667h131l257 -416v416h126v-667h-112l-275 441v-441h-127z" />
    <glyph glyph-name="Xi" unicode="&#x39e;" horiz-adv-x="531" 
d="M55 0v118h421v-118h-421zM107 285v117h316v-117h-316zM60 549v118h411v-118h-411z" />
    <glyph glyph-name="Omicron" unicode="&#x39f;" horiz-adv-x="672" 
d="M334 -14q-73 0 -127.5 30.5t-91 79.5t-54.5 111.5t-18 126.5t18 126.5t54.5 111.5t91 79t127.5 30q102 0 173 -60t96 -142q26 -86 26 -145q0 -64 -18.5 -126.5t-55 -111.5t-92 -79.5t-129.5 -30.5zM334 105q40 0 74 21.5t51 51t25 57.5q15 55 15 99q0 66 -24 123
q-23 57 -62 81.5t-79 24.5q-39 0 -69.5 -19t-51 -51t-31 -73.5t-10.5 -85.5t10.5 -85.5t31 -73.5t51 -51t69.5 -19z" />
    <glyph glyph-name="Pi" unicode="&#x3a0;" horiz-adv-x="643" 
d="M73 0v667h497v-667h-128v549h-241v-549h-128z" />
    <glyph glyph-name="Rho" unicode="&#x3a1;" horiz-adv-x="549" 
d="M73 0v667h200q48 0 89 -6.5t79 -25.5q48 -24 71 -71.5t23 -101.5q0 -53 -23 -101.5t-49.5 -70.5t-39.5 -25q-34 -14 -68 -19.5t-69 -5.5h-87v-240h-126zM199 355h71q27 0 49.5 3t38.5 11q27 13 39 36t12 49q0 28 -13.5 52t-40.5 34q-35 12 -80 12h-76v-197z" />
    <glyph glyph-name="Sigma" unicode="&#x3a3;" horiz-adv-x="511" 
d="M39 0v108l213 227l-214 223v109h441v-118h-270l199 -213l-204 -218h279v-118h-444z" />
    <glyph glyph-name="Tau" unicode="&#x3a4;" horiz-adv-x="516" 
d="M194 0v546h-189v121h506v-121h-189v-546h-128z" />
    <glyph glyph-name="Upsilon" unicode="&#x3a5;" horiz-adv-x="580" 
d="M228 0v156q0 43 -5 81q-6 43 -27 91t-52 94.5t-68 87t-76 69.5l84 94q52 -37 99.5 -94.5t75.5 -113t36 -89.5q16 59 58 127q43 69 82 109.5t70 61.5l75 -86q-34 -28 -62.5 -61.5t-60.5 -78.5q-32 -44 -54 -90q-23 -45 -34 -77t-13 -57q-5 -53 -5 -94v-130h-123z" />
    <glyph glyph-name="Phi" unicode="&#x3a6;" horiz-adv-x="710" 
d="M291 -12v91q-126 9 -194 76t-68 179t68 179t194 76v91h128v-91q125 -9 193 -76t68 -179t-68 -179t-193 -76v-91h-128zM291 185v297q-63 -5 -98 -42t-35 -106t35 -106.5t98 -42.5zM419 185q63 5 98 42.5t35 106.5t-35 106t-98 42v-297z" />
    <glyph glyph-name="Chi" unicode="&#x3a7;" horiz-adv-x="550" 
d="M0 0l206 347l-192 320h145l116 -203l115 203h145l-191 -320l206 -347h-143l-132 230l-132 -230h-143z" />
    <glyph glyph-name="Psi" unicode="&#x3a8;" horiz-adv-x="732" 
d="M304 0v196q-129 5 -192.5 69t-63.5 194v208h129v-190q0 -43 6 -74t20.5 -51.5t39 -30t61.5 -9.5v355h128v-355q43 -1 68 11t37.5 34t16.5 52t4 64v194h126v-214q0 -115 -61.5 -182.5t-190.5 -74.5v-196h-128z" />
    <glyph glyph-name="uni03A9" unicode="&#x3a9;" horiz-adv-x="665" 
d="M56 0v120h86q-39 38 -67 100.5t-28 150.5q0 102 44 176q46 75 112 104q67 30 132 30q99 0 165 -48q66 -45 92 -119q26 -73 26 -140q0 -63 -21.5 -123t-45 -91t-36.5 -40h94v-120h-226v114q54 43 82.5 103t28.5 135q0 103 -44.5 157t-116.5 54q-73 0 -117 -54t-44 -157
q0 -45 10.5 -82.5t27 -66.5t36 -51.5t36.5 -37.5v-114h-226z" />
    <glyph glyph-name="Iotadieresis" unicode="&#x3aa;" horiz-adv-x="275" 
d="M73 0v667h128v-667h-128zM44 726q-35 0 -52.5 20.5t-17.5 45.5q0 24 17.5 44.5t52.5 20.5q34 0 51 -20.5t17 -44.5q0 -25 -17 -45.5t-51 -20.5zM252 726q-35 0 -52.5 20.5t-17.5 45.5q0 24 17.5 44.5t52.5 20.5q34 0 51 -20.5t17 -44.5q0 -25 -17 -45.5t-51 -20.5z" />
    <glyph glyph-name="Upsilondieresis" unicode="&#x3ab;" horiz-adv-x="580" 
d="M228 0v156q0 43 -5 81q-6 43 -27 91t-52 94.5t-68 87t-76 69.5l84 94q52 -37 99.5 -94.5t75.5 -113t36 -89.5q16 59 58 127q43 69 82 109.5t70 61.5l75 -86q-34 -28 -62.5 -61.5t-60.5 -78.5q-32 -44 -54 -90q-23 -45 -34 -77t-13 -57q-5 -53 -5 -94v-130h-123zM194 726
q-35 0 -52.5 20.5t-17.5 45.5q0 24 17.5 44.5t52.5 20.5q34 0 51 -20.5t17 -44.5q0 -25 -17 -45.5t-51 -20.5zM402 726q-35 0 -52.5 20.5t-17.5 45.5q0 24 17.5 44.5t52.5 20.5q34 0 51 -20.5t17 -44.5q0 -25 -17 -45.5t-51 -20.5z" />
    <glyph glyph-name="alphatonos" unicode="&#x3ac;" horiz-adv-x="585" 
d="M251 -11q-44 0 -82 15t-66 45t-44 76t-16 108q0 78 32 144q21 43 55 70.5t67.5 37t60.5 9.5q51 0 82 -18.5t55 -47.5q6 39 29 77l108 -30q-23 -56 -36.5 -113.5t-13.5 -137.5q0 -69 10 -105t49 -36q13 0 30 5l7 -88q-24 -8 -65 -11q-50 0 -74.5 18t-38.5 56
q-30 -42 -69.5 -57.5t-79.5 -16.5zM261 94q48 0 76 36.5t28 114.5q0 71 -27 107t-72 36q-37 0 -62.5 -25.5t-31.5 -58.5t-6 -63q0 -69 23 -108t72 -39zM321 523l-68 34l101 228l103 -47z" />
    <glyph glyph-name="epsilontonos" unicode="&#x3ad;" horiz-adv-x="459" 
d="M243 -11q-97 0 -148.5 35.5t-51.5 104.5q0 46 24 77.5t69 44.5q-38 16 -59.5 44.5t-21.5 66.5q0 63 50.5 97.5t138.5 34.5q51 0 104.5 -13t82.5 -29l-45 -90q-31 15 -64.5 23.5t-63.5 8.5q-31 0 -55 -10t-24 -37q0 -20 10 -31t26.5 -17t37.5 -7.5t44 -1.5h70v-95h-31.5
t-32 -0.5t-28 -0.5h-18.5q-91 0 -91 -51q0 -28 29.5 -40.5t71.5 -12.5q35 0 72.5 8.5t70.5 25.5l27 -97q-27 -16 -85 -27t-109 -11zM271 523l-68 34l101 228l103 -47z" />
    <glyph glyph-name="etatonos" unicode="&#x3ae;" horiz-adv-x="584" 
d="M399 -189v452q0 38 -3 55q-8 75 -85 75q-38 0 -64 -19t-37 -48q-7 -18 -7 -61v-265h-123v345q0 23 -5 35t-21 12q-14 0 -29 -6l-15 89q12 5 36.5 9.5t43.5 4.5q66 0 83 -65q22 32 63.5 51t93.5 19q69 0 114 -27.5t60 -63.5t17 -60q2 -16 2 -63v-469h-124zM335 523l-68 34
l101 228l103 -47z" />
    <glyph glyph-name="iotatonos" unicode="&#x3af;" horiz-adv-x="293" 
d="M183 -11q-57 0 -89 31t-32 94v368h123v-336q0 -33 10 -44t28 -11q24 0 51 13l14 -95q-17 -8 -48 -14t-57 -6zM125 523l-68 34l101 228l103 -47z" />
    <glyph glyph-name="upsilondieresistonos" unicode="&#x3b0;" horiz-adv-x="561" 
d="M284 -11q-90 0 -144 48.5t-54 152.5v131q0 33 -5 49.5t-25 16.5q-15 0 -35 -7l-16 91q11 5 37 11t56 6q38 0 67 -21t38 -63q6 -24 6 -70v-113q0 -71 22.5 -98t61.5 -27q43 0 60.5 23t25.5 71q5 47 5 86q0 73 -21 198l124 16q9 -48 14.5 -102t5.5 -108q0 -68 -5 -105
q-14 -91 -70.5 -138.5t-147.5 -47.5zM271 540l-59 28l100 225l96 -39zM122 560q-29 0 -47 21.5t-18 49.5q0 29 18 50t47 21q28 0 46.5 -21t18.5 -50q0 -28 -18.5 -49.5t-46.5 -21.5zM458 560q-28 0 -46.5 21.5t-18.5 49.5q0 29 18.5 50t46.5 21q29 0 47 -21t18 -50
q0 -28 -18 -49.5t-47 -21.5z" />
    <glyph glyph-name="alpha" unicode="&#x3b1;" horiz-adv-x="582" 
d="M248 -11q-44 0 -82 15t-66 45t-44 76t-16 108q0 78 32 144q21 43 55 70.5t67.5 37t60.5 9.5q51 0 82 -18.5t55 -47.5q6 39 29 77l108 -30q-23 -56 -36.5 -113.5t-13.5 -137.5q0 -69 10 -105t49 -36q13 0 30 5l7 -88q-24 -8 -65 -11q-50 0 -74.5 18t-38.5 56
q-30 -42 -69.5 -57.5t-79.5 -16.5zM258 94q48 0 76 36.5t28 114.5q0 71 -27 107t-72 36q-37 0 -62.5 -25.5t-31.5 -58.5t-6 -63q0 -69 23 -108t72 -39z" />
    <glyph glyph-name="beta" unicode="&#x3b2;" horiz-adv-x="565" 
d="M62 -190v641q0 11 5 59q6 56 41 95.5t80 55t87 15.5q91 0 144.5 -42.5t53.5 -123.5q0 -47 -22.5 -81t-44 -46t-31.5 -16q57 -9 94 -40t50 -71t13 -73q0 -72 -34 -118q-25 -34 -57 -49.5t-64 -21t-54 -5.5q-42 0 -78 11.5t-65 30.5v-221h-118zM305 89q45 0 74.5 23
t29.5 74q0 65 -44 87.5t-123 28.5v101q51 0 80.5 23.5t29.5 68.5q0 34 -18.5 57t-60.5 23q-39 0 -59.5 -22.5t-25.5 -53.5q-3 -18 -3 -51v-316q20 -15 51 -29t69 -14z" />
    <glyph glyph-name="gamma" unicode="&#x3b3;" horiz-adv-x="541" 
d="M214 -189q0 100 -7 159q-10 82 -26 144q-25 91 -44 142q-31 80 -38 94q-10 20 -20 26.5t-21 6.5q-14 0 -32 -9l-31 96q12 6 38 14t52 8q32 0 62 -13t53 -56q26 -50 48 -133q24 -84 34 -154q13 77 56 188q42 109 80 173l106 -44q-42 -79 -89 -193q-45 -109 -69.5 -195
t-28.5 -141q-4 -44 -4 -113h-119z" />
    <glyph glyph-name="delta" unicode="&#x3b4;" horiz-adv-x="538" 
d="M258 -11q-52 0 -92.5 16.5t-69 47.5t-43.5 73.5t-15 95.5q0 87 47 150t134 93q-35 32 -72.5 56.5t-70.5 47.5l26 98h345v-100h-208q59 -36 137 -95q57 -46 90.5 -103.5t33.5 -135.5q0 -75 -36 -135q-35 -60 -90.5 -84.5t-115.5 -24.5zM268 93q26 -1 47 12.5t35 34.5
t20.5 46.5t5.5 48.5q0 51 -21.5 89.5t-60.5 67.5q-63 -23 -97.5 -66.5t-34.5 -101.5q0 -24 6 -47.5t19 -42t33 -30t48 -11.5z" />
    <glyph glyph-name="epsilon" unicode="&#x3b5;" horiz-adv-x="459" 
d="M240 -11q-97 0 -148.5 35.5t-51.5 104.5q0 46 24 77.5t69 44.5q-38 16 -59.5 44.5t-21.5 66.5q0 63 50.5 97.5t138.5 34.5q51 0 104.5 -13t82.5 -29l-45 -90q-31 15 -64.5 23.5t-63.5 8.5q-31 0 -55 -10t-24 -37q0 -20 10 -31t26.5 -17t37.5 -7.5t44 -1.5h70v-95h-31.5
t-32 -0.5t-28 -0.5h-18.5q-91 0 -91 -51q0 -28 29.5 -40.5t71.5 -12.5q35 0 72.5 8.5t70.5 25.5l27 -97q-27 -16 -85 -27t-109 -11z" />
    <glyph glyph-name="zeta" unicode="&#x3b6;" horiz-adv-x="454" 
d="M375 -185l-99 27q27 73 36 146q-63 0 -113.5 8.5t-86 30.5t-55 60.5t-19.5 98.5q0 69 29 140.5t69.5 122t80.5 88.5q40 35 50 41q-26 -3 -65 -3q-38 0 -75.5 4.5t-58.5 9.5l17 95q89 -14 170 -14t177 10v-95q-33 -21 -63 -45q-71 -56 -121 -123q-49 -66 -67.5 -117
t-18.5 -86q-2 -47 15 -71.5t48.5 -35.5t75 -13.5t94.5 -4.5q20 0 32.5 -10t12.5 -33q0 -44 -23 -119.5t-42 -111.5z" />
    <glyph glyph-name="eta" unicode="&#x3b7;" horiz-adv-x="584" 
d="M399 -189v452q0 38 -3 55q-8 75 -85 75q-38 0 -64 -19t-37 -48q-7 -18 -7 -61v-265h-123v345q0 23 -5 35t-21 12q-14 0 -29 -6l-15 89q12 5 36.5 9.5t43.5 4.5q66 0 83 -65q22 32 63.5 51t93.5 19q69 0 114 -27.5t60 -63.5t17 -60q2 -16 2 -63v-469h-124z" />
    <glyph glyph-name="theta" unicode="&#x3b8;" horiz-adv-x="530" 
d="M259 -11q-63 0 -105.5 29t-69 76.5t-38 109t-11.5 126.5q0 68 13.5 130.5t42 110.5t72.5 76.5t105 28.5q85 0 138 -56t68 -134q16 -76 16 -148q0 -71 -12.5 -134.5t-40 -111t-71.5 -75.5t-107 -28zM262 93q33 -2 53.5 17t32.5 48t16 63t4 62h-211q0 -32 5 -66t17.5 -61.5
t32.5 -45t50 -17.5zM157 383h211q1 59 -17.5 105.5t-43.5 66t-44 17.5q-32 1 -52.5 -20.5t-32.5 -52t-16.5 -63t-4.5 -53.5z" />
    <glyph glyph-name="iota" unicode="&#x3b9;" horiz-adv-x="293" 
d="M183 -11q-57 0 -89 31t-32 94v368h123v-336q0 -33 10 -44t28 -11q24 0 51 13l14 -95q-17 -8 -48 -14t-57 -6z" />
    <glyph glyph-name="kappa" unicode="&#x3ba;" horiz-adv-x="549" 
d="M67 0v482h119v-192q94 106 127 141q27 30 55.5 44.5t63.5 14.5q19 0 42 -6t37 -14l-34 -99q-19 11 -45 11q-20 0 -36.5 -13.5t-36.5 -36.5l-27 -32l203 -300h-143l-137 214l-69 -80v-134h-119z" />
    <glyph glyph-name="lambda" unicode="&#x3bb;" horiz-adv-x="526" 
d="M444 -11q-49 0 -83 28.5t-46 61.5t-19 60l-55 183l-102 -322h-125l169 479l-12 31q-11 26 -22 37.5t-37 11.5q-14 0 -31.5 -5.5t-31.5 -12.5l-26 98q17 10 56.5 19t68.5 9q42 0 77 -21.5t54 -78.5l137 -410q21 -64 60 -64q15 0 36 8l17 -97q-10 -5 -34 -10t-51 -5z" />
    <glyph glyph-name="uni03BC" unicode="&#x3bc;" horiz-adv-x="582" 
d="M67 -189v671h124v-293q0 -42 20 -70t65 -28q32 0 56.5 14.5t35.5 41.5q7 15 7 73v262h124v-349q0 -26 6 -36t20 -10q13 0 31 7l16 -91q-36 -14 -73 -14q-76 0 -93 65q-23 -33 -56 -49t-72 -16q-29 0 -55.5 10.5t-40.5 26.5v-215h-115z" />
    <glyph glyph-name="nu" unicode="&#x3bd;" horiz-adv-x="536" 
d="M217 0q-26 92 -48 161q-38 123 -65 189q-9 20 -20 26.5t-23 6.5q-13 0 -37 -11l-25 98q9 5 41 13t52 8q31 0 59.5 -12.5t51.5 -55.5q19 -33 42 -112q25 -90 41 -160q92 296 110 345l107 -36q-9 -23 -158 -460h-128z" />
    <glyph glyph-name="xi" unicode="&#x3be;" horiz-adv-x="479" 
d="M406 -182l-95 28q26 79 34 141q-54 1 -108 7t-97 25t-70 55t-27 97q0 58 33.5 103t90.5 62q-32 8 -56 31.5t-30 47t-6 41.5q0 48 29 81.5t92 49.5q-44 -4 -71 -4q-41 0 -81 8l16 94q106 -15 207 -15q91 0 167 8v-98q-58 -10 -118 -23q-57 -14 -87 -34.5t-30 -55.5
q0 -33 24 -51t66 -23q10 -2 69 -2h44v-108q-67 0 -112 -4q-53 -4 -88.5 -27t-35.5 -67q0 -40 32 -63q27 -20 81 -27t139 -7q25 0 38 -8.5t13 -31.5q0 -33 -21 -111q-20 -77 -42 -119z" />
    <glyph glyph-name="omicron" unicode="&#x3bf;" horiz-adv-x="528" 
d="M264 -11q-58 0 -101.5 22t-72 58t-43 81.5t-14.5 92.5q0 73 32 135q33 62 87 89t112 27q81 0 137 -44q55 -43 74 -102q20 -62 20 -105q0 -73 -33 -136q-25 -49 -65.5 -77t-75.5 -34.5t-57 -6.5zM264 96q32 -2 54 15.5t34.5 41.5t17.5 49.5t3 40.5q0 54 -22.5 91t-48 46.5
t-38.5 8.5q-42 0 -70 -30q-23 -25 -31 -55.5t-8 -60.5q0 -31 7 -57.5t20.5 -46.5t34 -31.5t47.5 -11.5z" />
    <glyph glyph-name="pi" unicode="&#x3c0;" horiz-adv-x="700" 
d="M554 -11q-57 0 -89 31t-32 94v266h-128v-380h-124v379h-8q-24 0 -37 -6q-21 -8 -56 -48l-70 71q17 23 44.5 47.5t69.5 33.5q32 5 52 5h477v-99h-101v-246q0 -30 7.5 -39.5t25.5 -9.5q20 0 39 8l19 -92q-41 -15 -89 -15z" />
    <glyph glyph-name="rho" unicode="&#x3c1;" horiz-adv-x="554" 
d="M62 -189v404q0 41 2 55q1 76 41 131.5t92.5 74t98.5 18.5q79 0 132 -38q36 -25 58 -64.5t27.5 -74.5t5.5 -61q0 -83 -33 -149q-33 -65 -83.5 -91.5t-105.5 -26.5q-61 0 -112 39v-217h-123zM287 92q33 0 54 15t33.5 38.5t17 50.5t4.5 50q0 61 -26 101t-79 40
q-51 0 -75 -35t-29 -86q-2 -14 -2 -56v-73q13 -12 40 -28.5t62 -16.5z" />
    <glyph glyph-name="sigma1" unicode="&#x3c2;" horiz-adv-x="497" 
d="M359 -179l-95 29q11 31 20 69.5t12 69.5q-40 3 -85 15t-83.5 40.5t-64 76.5t-25.5 122q0 84 36 146q37 62 88 83q51 22 100 22q78 0 122 -34.5t65 -79.5l-93 -60q-14 26 -36.5 47.5t-59.5 21.5q-48 0 -73.5 -38.5t-25.5 -101.5q0 -45 14.5 -75.5t41.5 -49.5t65.5 -28
t86.5 -10q30 -1 42.5 -11.5t12.5 -36.5q0 -33 -14 -80q-25 -82 -51 -137z" />
    <glyph glyph-name="sigma" unicode="&#x3c3;" horiz-adv-x="596" 
d="M257 -11q-58 0 -100 20.5t-69.5 53.5t-41 75.5t-13.5 87.5q0 75 35 134q35 60 86 86t104 31q80 5 165 5h125v-104h-109q25 -26 40 -68t15 -87q0 -65 -32 -124q-32 -57 -87 -83.5t-118 -26.5zM267 94q29 -1 49 13t33 35.5t18.5 46.5t4.5 45q0 48 -18 87.5t-50 58.5
q-26 0 -42 -3q-51 -10 -78.5 -51.5t-27.5 -93.5t18.5 -85t44 -43t48.5 -10z" />
    <glyph glyph-name="tau" unicode="&#x3c4;" horiz-adv-x="539" 
d="M342 -11q-72 0 -103 35.5t-31 98.5v260h-19q-36 0 -54 -7q-17 -7 -31.5 -21t-24.5 -29l-74 63q11 17 25 33t35 32q24 18 52 23t54 5h316v-99h-161v-244q0 -30 12 -40t32 -10t50 13l21 -93q-20 -9 -46.5 -14.5t-52.5 -5.5z" />
    <glyph glyph-name="upsilon" unicode="&#x3c5;" horiz-adv-x="561" 
d="M284 -11q-90 0 -144 48.5t-54 152.5v131q0 33 -5 49.5t-25 16.5q-15 0 -35 -7l-16 91q11 5 37 11t56 6q38 0 67 -21t38 -63q6 -24 6 -70v-113q0 -71 22.5 -98t61.5 -27q43 0 60.5 23t25.5 71q5 47 5 86q0 73 -21 198l124 16q9 -48 14.5 -102t5.5 -108q0 -68 -5 -105
q-14 -91 -70.5 -138.5t-147.5 -47.5z" />
    <glyph glyph-name="phi" unicode="&#x3c6;" horiz-adv-x="671" 
d="M276 -189v189q-50 1 -94 17t-76.5 46.5t-51 75t-18.5 102.5t18.5 102t51 74t76.5 46t94 18v186h119v-186q88 -3 145 -45.5t76 -97t19 -97.5q0 -84 -39.5 -142t-95 -78t-105.5 -21v-189h-119zM276 105v271q-51 -5 -84 -38.5t-33 -96.5t33 -97t84 -39zM395 105q51 5 84 39
t33 97t-33 96.5t-84 38.5v-271z" />
    <glyph glyph-name="chi" unicode="&#x3c7;" horiz-adv-x="523" 
d="M127 -206l-104 44l165 321l-68 157q-29 62 -53 62q-16 0 -39 -10l-23 100q11 5 37 13t53 8q23 0 40 -6t30.5 -18t25 -31t23.5 -45l47 -111l113 220l101 -48l-152 -283l94 -203q15 -33 28.5 -46.5t33.5 -13.5q14 0 34 7l21 -97q-13 -5 -35 -9.5t-43 -4.5q-40 0 -66.5 15
t-45 41t-30.5 53l-61 132z" />
    <glyph glyph-name="psi" unicode="&#x3c8;" horiz-adv-x="725" 
d="M303 -190v180q-122 10 -183.5 70t-61.5 185v106q0 84 -9 122l124 11q9 -42 9 -127v-102q0 -69 27.5 -109.5t93.5 -44.5v505h119v-505q45 0 72.5 20t37.5 56q7 23 8.5 50.5t1.5 57.5q0 36 -4.5 83t-14.5 106l118 10q11 -51 16.5 -98t5.5 -101q0 -31 -2.5 -60.5t-7.5 -53.5
q-16 -78 -75 -125.5t-156 -55.5v-180h-119z" />
    <glyph glyph-name="omega" unicode="&#x3c9;" horiz-adv-x="797" 
d="M238 -11q-46 0 -81.5 16.5t-59.5 47t-36.5 73t-12.5 94.5q0 43 9 86t22.5 79.5t28.5 64.5t27 42l108 -46q-27 -45 -47.5 -101t-20.5 -125q0 -58 20 -89.5t62 -31.5q32 0 54 27.5t26 81.5q3 27 3 140h122q0 -113 3 -140q4 -55 27 -81.5t58 -26.5q36 0 58.5 28.5t22.5 99.5
q0 56 -24 119t-49 99l111 45q28 -35 48.5 -84.5t28.5 -96.5t8 -73q0 -120 -51 -184t-148 -64q-52 0 -91.5 18.5t-63.5 63.5q-33 -48 -73.5 -65t-88.5 -17z" />
    <glyph glyph-name="iotadieresis" unicode="&#x3ca;" horiz-adv-x="293" 
d="M183 -11q-57 0 -89 31t-32 94v368h123v-336q0 -33 10 -44t28 -11q24 0 51 13l14 -95q-17 -8 -48 -14t-57 -6zM27 560q-35 0 -52.5 20.5t-17.5 45.5q0 24 17.5 44.5t52.5 20.5q34 0 51 -20.5t17 -44.5q0 -25 -17 -45.5t-51 -20.5zM235 560q-35 0 -52.5 20.5t-17.5 45.5
q0 24 17.5 44.5t52.5 20.5q34 0 51 -20.5t17 -44.5q0 -25 -17 -45.5t-51 -20.5z" />
    <glyph glyph-name="upsilondieresis" unicode="&#x3cb;" horiz-adv-x="561" 
d="M284 -11q-90 0 -144 48.5t-54 152.5v131q0 33 -5 49.5t-25 16.5q-15 0 -35 -7l-16 91q11 5 37 11t56 6q38 0 67 -21t38 -63q6 -24 6 -70v-113q0 -71 22.5 -98t61.5 -27q43 0 60.5 23t25.5 71q5 47 5 86q0 73 -21 198l124 16q9 -48 14.5 -102t5.5 -108q0 -68 -5 -105
q-14 -91 -70.5 -138.5t-147.5 -47.5zM182 560q-35 0 -52.5 20.5t-17.5 45.5q0 24 17.5 44.5t52.5 20.5q34 0 51 -20.5t17 -44.5q0 -25 -17 -45.5t-51 -20.5zM390 560q-35 0 -52.5 20.5t-17.5 45.5q0 24 17.5 44.5t52.5 20.5q34 0 51 -20.5t17 -44.5q0 -25 -17 -45.5
t-51 -20.5z" />
    <glyph glyph-name="omicrontonos" unicode="&#x3cc;" horiz-adv-x="528" 
d="M276 523l-68 34l101 228l103 -47zM264 -11q-58 0 -101.5 22t-72 58t-43 81.5t-14.5 92.5q0 73 32 135q33 62 87 89t112 27q81 0 137 -44q55 -43 74 -102q20 -62 20 -105q0 -73 -33 -136q-25 -49 -65.5 -77t-75.5 -34.5t-57 -6.5zM264 96q32 -2 54 15.5t34.5 41.5
t17.5 49.5t3 40.5q0 54 -22.5 91t-48 46.5t-38.5 8.5q-42 0 -70 -30q-23 -25 -31 -55.5t-8 -60.5q0 -31 7 -57.5t20.5 -46.5t34 -31.5t47.5 -11.5z" />
    <glyph glyph-name="upsilontonos" unicode="&#x3cd;" horiz-adv-x="561" 
d="M284 -11q-90 0 -144 48.5t-54 152.5v131q0 33 -5 49.5t-25 16.5q-15 0 -35 -7l-16 91q11 5 37 11t56 6q38 0 67 -21t38 -63q6 -24 6 -70v-113q0 -71 22.5 -98t61.5 -27q43 0 60.5 23t25.5 71q5 47 5 86q0 73 -21 198l124 16q9 -48 14.5 -102t5.5 -108q0 -68 -5 -105
q-14 -91 -70.5 -138.5t-147.5 -47.5zM309 523l-68 34l101 228l103 -47z" />
    <glyph glyph-name="omegatonos" unicode="&#x3ce;" horiz-adv-x="797" 
d="M238 -11q-46 0 -81.5 16.5t-59.5 47t-36.5 73t-12.5 94.5q0 43 9 86t22.5 79.5t28.5 64.5t27 42l108 -46q-27 -45 -47.5 -101t-20.5 -125q0 -58 20 -89.5t62 -31.5q32 0 54 27.5t26 81.5q3 27 3 140h122q0 -113 3 -140q4 -55 27 -81.5t58 -26.5q36 0 58.5 28.5t22.5 99.5
q0 56 -24 119t-49 99l111 45q28 -35 48.5 -84.5t28.5 -96.5t8 -73q0 -120 -51 -184t-148 -64q-52 0 -91.5 18.5t-63.5 63.5q-33 -48 -73.5 -65t-88.5 -17zM417 523l-68 34l101 228l103 -47z" />
    <glyph glyph-name="afii10023" unicode="&#x401;" horiz-adv-x="557" 
d="M73 0v667h435v-121h-307v-152h254v-121h-254v-152h329v-121h-457zM188 726q-35 0 -52.5 20.5t-17.5 45.5q0 24 17.5 44.5t52.5 20.5q34 0 51 -20.5t17 -44.5q0 -25 -17 -45.5t-51 -20.5zM396 726q-35 0 -52.5 20.5t-17.5 45.5q0 24 17.5 44.5t52.5 20.5q34 0 51 -20.5
t17 -44.5q0 -25 -17 -45.5t-51 -20.5z" />
    <glyph glyph-name="afii10051" unicode="&#x402;" horiz-adv-x="683" 
d="M157 0v546h-157v121h456v-121h-171v-111q23 10 63 20t88 10q71 0 125 -33q36 -22 57.5 -55t29 -71t7.5 -65q0 -59 -16.5 -103.5t-46.5 -75.5t-72 -46.5t-94 -15.5h-67v117h47q57 0 87.5 28t30.5 91q0 58 -30.5 87t-84.5 29q-35 0 -68.5 -10.5t-54.5 -21.5v-320h-129z" />
    <glyph glyph-name="afii10052" unicode="&#x403;" horiz-adv-x="480" 
d="M69 0v667h406v-121h-277v-546h-129zM199 704l-33 54l191 141l59 -83z" />
    <glyph glyph-name="afii10053" unicode="&#x404;" horiz-adv-x="576" 
d="M325 -14q-65 0 -118 22.5t-90.5 66t-58 107t-20.5 146.5q0 91 23.5 157.5t63 110t91.5 64.5t109 21q85 0 140 -33.5t88 -97.5l-97 -73q-20 38 -52.5 62t-77.5 24q-55 0 -96 -40t-55 -119h232v-121h-235q3 -47 18 -81t37.5 -56t50.5 -32.5t57 -10.5q51 0 89 16t75 51
l43 -110q-38 -35 -94.5 -54.5t-122.5 -19.5z" />
    <glyph glyph-name="afii10054" unicode="&#x405;" horiz-adv-x="528" 
d="M257 -14q-45 0 -83 8t-67 18.5t-48.5 21.5t-28.5 18l54 111q9 -8 25 -19t37.5 -21t49 -16.5t60.5 -6.5q48 0 75.5 21t27.5 56q0 42 -27 65q-29 24 -118 55q-74 21 -117 56q-30 25 -44.5 54t-15.5 50.5t0 37.5q0 63 31 106q31 45 84 62.5t108 17.5q54 0 99.5 -15t76.5 -37
t46 -39l-67 -89q-19 21 -57 39t-64 24.5t-39 5.5q-52 0 -73 -20.5t-21 -49.5q0 -35 24 -53q13 -9 38.5 -21t65.5 -26q89 -31 133 -69q44 -37 53 -75q8 -35 11 -74q0 -68 -41 -118q-41 -48 -94 -63t-94 -15z" />
    <glyph glyph-name="uni0406" unicode="&#x406;" horiz-adv-x="289" 
d="M73 0v667h128v-667h-128z" />
    <glyph glyph-name="afii10056" unicode="&#x407;" horiz-adv-x="275" 
d="M73 0v667h128v-667h-128zM45 726q-35 0 -52.5 20.5t-17.5 45.5q0 24 17.5 44.5t52.5 20.5q34 0 51 -20.5t17 -44.5q0 -25 -17 -45.5t-51 -20.5zM253 726q-35 0 -52.5 20.5t-17.5 45.5q0 24 17.5 44.5t52.5 20.5q34 0 51 -20.5t17 -44.5q0 -25 -17 -45.5t-51 -20.5z" />
    <glyph glyph-name="afii10057" unicode="&#x408;" horiz-adv-x="425" 
d="M171 -14q-53 0 -93 14.5t-58 25.5l37 107q9 -11 32.5 -20t39 -11t27.5 -2q35 0 55.5 17.5t20.5 66.5v483h128v-484q0 -100 -47.5 -148.5t-141.5 -48.5z" />
    <glyph glyph-name="afii10058" unicode="&#x409;" horiz-adv-x="923" 
d="M17 0v121h22q27 0 53 9.5t41 39.5q31 58 31 356v141h393v-224h99q86 0 141 -34q41 -26 62.5 -59t28.5 -67.5t7 -58.5q0 -74 -36 -129q-34 -55 -91 -75t-112 -20h-226v548h-139v-14q0 -158 -7 -238q-3 -45 -8 -78t-12 -55q-15 -46 -29 -71q-30 -51 -78 -71.5t-105 -20.5
h-35zM557 114h91q54 0 83.5 28t29.5 82q0 51 -29 78t-83 27h-92v-215z" />
    <glyph glyph-name="afii10059" unicode="&#x40a;" horiz-adv-x="920" 
d="M69 0v667h129v-244h227v244h128v-244h100q115 0 176.5 -53.5t61.5 -153.5t-61.5 -158t-176.5 -58h-228v309h-227v-309h-129zM553 114h91q54 0 85 22t31 76q0 51 -30.5 74t-84.5 23h-92v-195z" />
    <glyph glyph-name="afii10060" unicode="&#x40b;" horiz-adv-x="680" 
d="M151 0v546h-151v121h438v-121h-159v-121q24 14 56 23.5t74 9.5q76 0 124.5 -33t64 -81t15.5 -89v-255h-128v243q0 45 -22.5 70.5t-71.5 25.5q-34 0 -63.5 -12.5t-48.5 -25.5v-301h-128z" />
    <glyph glyph-name="afii10061" unicode="&#x40c;" horiz-adv-x="591" 
d="M69 0v667h129v-267h36q44 0 91 125q20 72 56.5 107t96.5 35h80v-121h-58q-24 0 -36 -19t-27 -61q-18 -48 -35 -77.5t-34 -46.5l218 -342h-152l-179 280h-57v-280h-129zM247 704l-33 54l191 141l59 -83z" />
    <glyph glyph-name="afii10062" unicode="&#x40e;" horiz-adv-x="576" 
d="M95 -6v121h28q57 0 78.5 14.5t38.5 58.5l-250 479h144l175 -350q33 81 63.5 170t58.5 180h130q-62 -189 -105.5 -303t-95.5 -219q-33 -68 -71.5 -102t-76.5 -41.5t-69 -7.5h-48zM290 705q-53 0 -101 23q-46 23 -67 60t-23 79h117q8 -33 29.5 -50t44.5 -17t44.5 17
t29.5 50h117q-3 -58 -38 -98q-34 -39 -76.5 -51.5t-76.5 -12.5z" />
    <glyph glyph-name="afii10145" unicode="&#x40f;" horiz-adv-x="639" 
d="M257 -135v135h-183v667h128v-548h235v548h128v-667h-183v-135h-125z" />
    <glyph glyph-name="afii10017" unicode="&#x410;" horiz-adv-x="608" 
d="M0 0l240 667h127l241 -667h-131l-46 134h-255l-45 -134h-131zM215 247h178l-89 263z" />
    <glyph glyph-name="afii10018" unicode="&#x411;" horiz-adv-x="569" 
d="M69 0v667h402v-121h-273v-123h99q115 0 177 -55.5t62 -155.5t-62 -156t-177 -56h-228zM198 114h91q54 0 83.5 22t29.5 76q0 51 -29 74t-83 23h-92v-195z" />
    <glyph glyph-name="afii10019" unicode="&#x412;" horiz-adv-x="564" 
d="M73 0v667h226q73 0 126 -33q51 -32 68 -75q15 -41 15 -72q0 -45 -20 -78t-57 -56q51 -19 82 -60.5t31 -104.5q0 -53 -28.5 -101t-86.5 -71q-21 -8 -46.5 -12t-49.5 -4h-260zM200 114h91q33 0 52 3t31 10q21 12 30.5 29.5t9.5 36.5q0 22 -12 43t-35 34q-15 8 -35 11.5
t-43 3.5h-89v-171zM200 394h62q54 0 71 9q24 12 35 30.5t11 38.5q0 24 -15 44.5t-44 30.5q-2 0 -9.5 1t-17.5 2t-21 2t-20 1h-52v-159z" />
    <glyph glyph-name="afii10020" unicode="&#x413;" horiz-adv-x="480" 
d="M69 0v667h406v-121h-277v-546h-129z" />
    <glyph glyph-name="afii10021" unicode="&#x414;" horiz-adv-x="632" 
d="M5 -135v256h53q51 83 78 184q28 103 34.5 196t6.5 166h379v-546h51v-256h-125v135h-352v-135h-125zM197 119h231v429h-132q0 -94 -17 -183t-40.5 -150.5t-41.5 -95.5z" />
    <glyph glyph-name="afii10022" unicode="&#x415;" horiz-adv-x="553" 
d="M73 0v667h435v-121h-307v-152h254v-121h-254v-152h329v-121h-457z" />
    <glyph glyph-name="afii10024" unicode="&#x416;" horiz-adv-x="853" 
d="M18 0l175 333q-16 19 -29 45.5t-29 68.5q-12 32 -25 60q-7 16 -18.5 27.5t-22.5 11.5h-46v121h68q72 0 104 -30q33 -31 53 -85q9 -23 16 -50.5t16.5 -51.5t23 -40.5t34.5 -16.5h26v274h126v-274h23q21 -2 35 15t24 40.5t17.5 49t13.5 42.5q25 68 61 97.5t96 29.5h67v-121
h-38q-27 0 -43.5 -22.5t-35.5 -76.5q-15 -42 -29 -69t-26 -40l172 -338h-156l-131 278h-50v-278h-126v278h-54l-138 -278h-154z" />
    <glyph glyph-name="afii10025" unicode="&#x417;" horiz-adv-x="543" 
d="M257 -14q-75 0 -131.5 23t-101.5 74l74 104q27 -43 70.5 -65.5t88.5 -22.5q51 0 81.5 24t30.5 72q0 45 -34 69t-84 24h-73v107h71q51 0 79 27.5t28 64.5q0 34 -23.5 57.5t-71.5 23.5q-52 0 -83.5 -21t-60.5 -63l-85 72q43 65 99.5 95t140.5 30q103 0 158.5 -48.5
t55.5 -129.5q0 -58 -24 -97.5t-59 -61.5q48 -19 73.5 -57t25.5 -95q0 -102 -65.5 -154t-179.5 -52z" />
    <glyph glyph-name="afii10026" unicode="&#x418;" horiz-adv-x="647" 
d="M69 0v667h129v-401l251 306v95h129v-667h-129v401l-251 -306v-95h-129z" />
    <glyph glyph-name="afii10027" unicode="&#x419;" horiz-adv-x="647" 
d="M69 0v667h129v-401l251 306v95h129v-667h-129v401l-251 -306v-95h-129zM327 705q-53 0 -101 23q-46 23 -67 60t-23 79h117q8 -33 29.5 -50t44.5 -17t44.5 17t29.5 50h117q-3 -58 -38 -98q-34 -39 -76.5 -51.5t-76.5 -12.5z" />
    <glyph glyph-name="afii10028" unicode="&#x41a;" horiz-adv-x="591" 
d="M69 0v667h129v-267h36q44 0 91 125q20 72 56.5 107t96.5 35h80v-121h-58q-24 0 -36 -19t-27 -61q-18 -48 -35 -77.5t-34 -46.5l218 -342h-152l-179 280h-57v-280h-129z" />
    <glyph glyph-name="afii10029" unicode="&#x41b;" horiz-adv-x="649" 
d="M20 0v121h22q27 0 53 9.5t41 39.5q31 58 31 356v141h413v-667h-129v548h-157v-14q0 -158 -7 -238q-6 -91 -19.5 -133.5t-29.5 -70.5q-30 -51 -78 -71.5t-105 -20.5h-35z" />
    <glyph glyph-name="afii10030" unicode="&#x41c;" horiz-adv-x="750" 
d="M340 -4l-138 401l-18 -397h-127l32 667h131l163 -474l146 474h132l32 -667h-127l-18 393l-123 -397h-85z" />
    <glyph glyph-name="afii10031" unicode="&#x41d;" horiz-adv-x="646" 
d="M73 0v667h128v-264h252v264h128v-667h-128v282h-252v-282h-128z" />
    <glyph glyph-name="afii10032" unicode="&#x41e;" horiz-adv-x="672" 
d="M334 -14q-73 0 -127.5 30.5t-91 79.5t-54.5 111.5t-18 126.5t18 126.5t54.5 111.5t91 79t127.5 30q102 0 173 -60t96 -142q26 -86 26 -145q0 -64 -18.5 -126.5t-55 -111.5t-92 -79.5t-129.5 -30.5zM334 105q40 0 74 21.5t51 51t25 57.5q15 55 15 99q0 66 -24 123
q-23 57 -62 81.5t-79 24.5q-39 0 -69.5 -19t-51 -51t-31 -73.5t-10.5 -85.5t10.5 -85.5t31 -73.5t51 -51t69.5 -19z" />
    <glyph glyph-name="afii10033" unicode="&#x41f;" horiz-adv-x="635" 
d="M69 0v667h497v-667h-128v546h-240v-546h-129z" />
    <glyph glyph-name="afii10034" unicode="&#x420;" horiz-adv-x="551" 
d="M73 0v667h200q48 0 89 -6.5t79 -25.5q48 -24 71 -71.5t23 -101.5q0 -53 -23 -101.5t-49.5 -70.5t-39.5 -25q-34 -14 -68 -19.5t-69 -5.5h-87v-240h-126zM199 355h71q27 0 49.5 3t38.5 11q27 13 39 36t12 49q0 28 -13.5 52t-40.5 34q-35 12 -80 12h-76v-197z" />
    <glyph glyph-name="afii10035" unicode="&#x421;" horiz-adv-x="578" 
d="M330 -14q-63 0 -115.5 22t-91 65.5t-59.5 108t-21 149.5q0 90 24.5 155.5t64.5 109t91.5 64.5t105.5 21q68 0 119 -25t74 -53t29 -40l-94 -79q-5 9 -14.5 22.5t-25 26t-37 21.5t-49.5 9q-32 0 -60.5 -13.5t-50.5 -42t-34.5 -72t-12.5 -104.5q0 -109 47 -167.5t125 -58.5
q57 0 98 19.5t59 37.5l41 -111q-23 -27 -92.5 -46t-120.5 -19z" />
    <glyph glyph-name="afii10036" unicode="&#x422;" horiz-adv-x="516" 
d="M194 0v546h-189v121h506v-121h-189v-546h-128z" />
    <glyph glyph-name="afii10037" unicode="&#x423;" horiz-adv-x="576" 
d="M95 -6v121h28q57 0 78.5 14.5t38.5 58.5l-250 479h144l175 -350q33 81 63.5 170t58.5 180h130q-62 -189 -105.5 -303t-95.5 -219q-33 -68 -71.5 -102t-76.5 -41.5t-69 -7.5h-48z" />
    <glyph glyph-name="afii10038" unicode="&#x424;" horiz-adv-x="710" 
d="M291 -12v91q-126 9 -194 75t-68 178q0 115 68 181.5t194 75.5v91h128v-91q124 -9 193 -75t69 -175q0 -117 -68.5 -184t-193.5 -76v-91h-128zM291 185v297q-63 -5 -98 -42.5t-35 -103.5q0 -69 35 -107.5t98 -43.5zM419 185q63 5 98 42.5t35 106.5t-35 106t-98 42v-297z
" />
    <glyph glyph-name="afii10039" unicode="&#x425;" horiz-adv-x="550" 
d="M0 0l206 347l-192 320h145l116 -203l115 203h145l-191 -320l206 -347h-143l-132 230l-132 -230h-143z" />
    <glyph glyph-name="afii10040" unicode="&#x426;" horiz-adv-x="640" 
d="M482 -135v135h-413v667h128v-548h229v548h128v-548h54v-254h-126z" />
    <glyph glyph-name="afii10041" unicode="&#x427;" horiz-adv-x="611" 
d="M413 0v262q-25 -10 -68.5 -14.5t-78.5 -4.5q-103 0 -156 45t-61 142q-1 6 -1 52v185h128v-173q0 -27 2 -49q4 -48 32 -67.5t82 -19.5q31 0 65 5t56 14v290h129v-667h-129z" />
    <glyph glyph-name="afii10042" unicode="&#x428;" horiz-adv-x="865" 
d="M69 0v667h128v-549h173v549h125v-549h173v549h128v-667h-727z" />
    <glyph glyph-name="afii10043" unicode="&#x429;" horiz-adv-x="875" 
d="M716 -135v135h-647v667h128v-549h169v549h125v-549h169v549h128v-549h53v-253h-125z" />
    <glyph glyph-name="afii10044" unicode="&#x42a;" horiz-adv-x="608" 
d="M99 0v546h-94v121h222v-224h114q84 0 140 -34q39 -25 62 -57.5t30 -68t7 -59.5q0 -50 -16 -91t-47 -70.5t-75 -46t-101 -16.5h-242zM227 114h106q54 0 83.5 28t29.5 82q0 51 -29.5 78t-83.5 27h-106v-215z" />
    <glyph glyph-name="afii10045" unicode="&#x42b;" horiz-adv-x="806" 
d="M69 0v667h129v-224h99q86 0 141 -34q41 -26 62.5 -59t28.5 -67.5t7 -58.5q0 -74 -36 -129q-34 -55 -91 -75t-112 -20h-228zM608 0v667h128v-667h-128zM198 114h91q54 0 83.5 28t29.5 82q0 51 -29 78t-83 27h-92v-215z" />
    <glyph glyph-name="afii10046" unicode="&#x42c;" horiz-adv-x="583" 
d="M69 0v667h129v-224h118q86 0 141 -34q41 -26 62.5 -59t28.5 -67.5t7 -58.5q0 -74 -36 -129q-34 -55 -91 -75t-112 -20h-247zM198 114h110q54 0 83.5 28t29.5 82q0 51 -29 78t-83 27h-111v-215z" />
    <glyph glyph-name="afii10047" unicode="&#x42d;" horiz-adv-x="576" 
d="M263 -14q-42 0 -78.5 8t-66 20t-51.5 27t-36 29l52 116q34 -39 80 -60t101 -21q34 0 60 14t44 38t27.5 56t9.5 67h-236v121h232q-3 44 -17.5 75.5t-35.5 51t-44 27.5t-42 8q-49 0 -82 -23t-61 -59l-85 78q31 46 72.5 73t85 38t79.5 11q86 0 150 -47q64 -46 90 -128
q26 -81 26 -169q0 -87 -20 -153t-56 -110t-86.5 -66t-111.5 -22z" />
    <glyph glyph-name="afii10048" unicode="&#x42e;" horiz-adv-x="924" 
d="M598 -14q-69 0 -120 24.5t-85.5 65.5t-54 94.5t-25.5 110.5h-115v-281h-129v667h129v-271h116q11 86 53 155q29 50 72 80.5t84.5 40t74.5 9.5q102 0 170 -60q69 -60 93 -142q24 -81 24 -145t-17.5 -126.5t-53 -111.5t-89.5 -79.5t-127 -30.5zM598 104q56 0 94 39
q37 40 50.5 92.5t13.5 98.5q0 69 -24 125q-23 57 -59 80.5t-75 23.5q-59 0 -96.5 -40.5t-49.5 -92.5q-11 -55 -11 -96q0 -44 9.5 -85.5t29 -73.5t49 -51.5t69.5 -19.5z" />
    <glyph glyph-name="afii10049" unicode="&#x42f;" horiz-adv-x="580" 
d="M38 0v121h39q34 0 55 21.5t29 84.5q1 18 3 39q-54 26 -86.5 72.5t-32.5 121.5q0 100 61.5 153.5t176.5 53.5h228v-667h-126v244h-95q-2 -14 -3 -27t-3 -25q-9 -63 -28.5 -100.5t-45 -58t-55.5 -27t-59 -6.5h-58zM293 360h92v193h-93q-54 0 -83.5 -20.5t-29.5 -69.5
q0 -48 30 -75.5t84 -27.5z" />
    <glyph glyph-name="afii10065" unicode="&#x430;" horiz-adv-x="488" 
d="M191 -11q-31 0 -59.5 10.5t-50.5 30t-35.5 48t-13.5 64.5q0 39 16.5 67.5t43 48t57 30t55.5 13.5q14 2 100 12v7q0 40 -20.5 57t-56.5 17t-69 -15.5t-50 -30.5l-41 87q21 21 78 40q60 19 103 19q46 0 80.5 -14t57 -39.5t33 -59.5t10.5 -71v-310h-114l-5 36
q-20 -22 -51 -34.5t-68 -12.5zM220 92q24 0 46.5 10t38.5 25v98q-61 -7 -81 -10q-40 -8 -55.5 -27.5t-15.5 -42.5q0 -24 17.5 -38.5t49.5 -14.5z" />
    <glyph glyph-name="afii10066" unicode="&#x431;" horiz-adv-x="542" 
d="M270 -11q-60 0 -103.5 21.5t-72 60t-42.5 91.5t-14 116q0 60 11.5 121t38.5 111.5t72 84.5t111 39l38 3q24 2 36 12t17 40l100 -17q-6 -55 -26.5 -84.5t-49.5 -38t-57 -11.5l-49 -5q-46 -5 -74.5 -35.5t-39.5 -93.5q14 23 51 38.5t78 15.5q78 1 128 -43t64.5 -96.5
t14.5 -87.5q0 -75 -35 -134t-88 -83.5t-109 -24.5zM271 97q54 0 81.5 36.5t27.5 91.5q0 54 -27.5 90.5t-81.5 36.5t-81.5 -36.5t-27.5 -90.5q0 -55 27.5 -91.5t81.5 -36.5z" />
    <glyph glyph-name="afii10067" unicode="&#x432;" horiz-adv-x="523" 
d="M62 0v482h250q73 0 114.5 -33.5t41.5 -93.5q0 -69 -51 -100q36 -16 54.5 -41t18.5 -67q0 -43 -16 -71.5t-42 -46t-56 -23.5t-56 -6h-258zM181 97h126q28 0 41.5 13.5t13.5 36.5q0 49 -55 49h-126v-99zM181 291h111q31 0 41.5 17t10.5 34t-10.5 30t-41.5 13h-111v-94z" />
    <glyph glyph-name="afii10068" unicode="&#x433;" horiz-adv-x="404" 
d="M62 0v482h337v-105h-214v-377h-123z" />
    <glyph glyph-name="afii10069" unicode="&#x434;" horiz-adv-x="576" 
d="M10 -105v210h48q48 60 70 141.5t22 180.5v55h350v-377h46v-210h-123v105h-290v-105h-123zM191 103h187v276h-113q-2 -85 -22 -156.5t-52 -119.5z" />
    <glyph glyph-name="afii10070" unicode="&#x435;" horiz-adv-x="499" 
d="M267 -11q-47 0 -89.5 15t-74.5 46.5t-51 78.5t-19 112q0 61 17.5 108t48 79t71 49t86.5 17q81 0 132 -45q52 -45 67 -106q15 -60 15 -111v-22h-312q4 -61 38 -88t88 -27q48 0 84 15t49 28l33 -101q-22 -16 -42 -23q-12 -6 -59 -15q-46 -10 -82 -10zM161 299h183
q-3 41 -23.5 63t-39.5 27.5t-25 5.5q-32 0 -61 -24t-34 -72z" />
    <glyph glyph-name="afii10072" unicode="&#x436;" horiz-adv-x="710" 
d="M30 0l124 254q-26 30 -50 82q-24 46 -50 46h-25v100h56q37 0 62 -15.5t39 -41.5t24 -43q40 -85 67 -85h18v185h124v-185h22q21 0 63 84q30 56 38 66q28 35 88 35h51v-100h-25q-18 0 -29 -11.5t-22 -34.5q-28 -57 -45 -76l118 -260h-128l-90 195h-41v-195h-124v195h-43
l-91 -195h-131z" />
    <glyph glyph-name="afii10073" unicode="&#x437;" horiz-adv-x="454" 
d="M217 -11q-40 0 -71.5 7.5t-55.5 18t-41 23t-27 22.5l60 92q21 -24 54 -42t81 -18q38 0 60.5 12t22.5 39q0 22 -18 36.5t-66 14.5h-45v93h46q43 0 58 16.5t15 40.5q0 29 -21.5 39.5t-50.5 10.5q-35 0 -60 -17t-45 -56l-87 56q30 62 78.5 89.5t120.5 27.5q88 0 137.5 -35
t49.5 -101q0 -32 -15.5 -62t-53.5 -46q45 -14 63 -41t18 -70q0 -54 -35 -92q-35 -36 -81.5 -47t-90.5 -11z" />
    <glyph glyph-name="afii10074" unicode="&#x438;" horiz-adv-x="555" 
d="M62 0v482h123v-261l185 184v77h123v-482h-123v261l-185 -184v-77h-123z" />
    <glyph glyph-name="afii10075" unicode="&#x439;" horiz-adv-x="555" 
d="M62 0v482h123v-261l185 184v77h123v-482h-123v261l-185 -184v-77h-123zM280 543q-53 0 -101 23q-46 23 -67 60t-23 79h117q8 -33 29.5 -50t44.5 -17t44.5 17t29.5 50h117q-3 -58 -38 -98q-34 -39 -76.5 -51.5t-76.5 -12.5z" />
    <glyph glyph-name="afii10076" unicode="&#x43a;" 
d="M62 0v482h123v-183h36q12 0 23 14q6 7 11 18.5t10.5 24t10 24.5t9.5 21q20 43 46 62t69 19h76v-103h-37q-22 0 -34.5 -10.5t-22.5 -33.5q-22 -55 -40 -76l170 -259h-151l-115 189h-61v-189h-123z" />
    <glyph glyph-name="afii10077" unicode="&#x43b;" horiz-adv-x="547" 
d="M14 0v106h29q19 0 36.5 10t25.5 39q9 36 9 190v137h371v-482h-125v379h-123v-51q0 -133 -15 -200q-11 -43 -35.5 -74.5t-56.5 -42.5t-67 -11h-49z" />
    <glyph glyph-name="afii10078" unicode="&#x43c;" horiz-adv-x="628" 
d="M55 0l20 482h124l118 -314l112 314h124l20 -482h-116l-10 278l-99 -278h-61l-106 278l-11 -278h-115z" />
    <glyph glyph-name="afii10079" unicode="&#x43d;" horiz-adv-x="549" 
d="M62 0v482h123v-183h179v183h123v-482h-123v194h-179v-194h-123z" />
    <glyph glyph-name="afii10080" unicode="&#x43e;" horiz-adv-x="528" 
d="M264 -11q-58 0 -101.5 22t-72 58t-43 81.5t-14.5 92.5q0 73 32 135q33 62 87 89t112 27q81 0 137 -44q55 -43 74 -102q20 -62 20 -105q0 -73 -33 -136q-25 -49 -65.5 -77t-75.5 -34.5t-57 -6.5zM264 96q32 -2 54 15.5t34.5 41.5t17.5 49.5t3 40.5q0 54 -22.5 91t-48 46.5
t-38.5 8.5q-42 0 -70 -30q-23 -25 -31 -55.5t-8 -60.5q0 -31 7 -57.5t20.5 -46.5t34 -31.5t47.5 -11.5z" />
    <glyph glyph-name="afii10081" unicode="&#x43f;" horiz-adv-x="544" 
d="M62 0v482h420v-482h-123v379h-173v-379h-124z" />
    <glyph glyph-name="afii10082" unicode="&#x440;" horiz-adv-x="526" 
d="M62 -189v671h113l6 -36q17 17 45.5 32.5t67.5 15.5q69 0 117 -42t65 -102q17 -62 17 -108q0 -76 -34 -138.5t-90 -88.5t-119 -26q-30 0 -64 14v-192h-124zM243 97q37 -1 62.5 14t40.5 37t21 47t5 45q1 44 -17 83q-17 39 -43 54t-44 13q-26 0 -49 -14.5t-34 -31.5v-233
q33 -14 58 -14z" />
    <glyph glyph-name="afii10083" unicode="&#x441;" horiz-adv-x="453" 
d="M257 -11q-44 0 -84.5 15t-71.5 46t-49.5 78t-18.5 111q0 90 36 152q37 61 89 82t96 21q76 0 122 -36t67 -77l-92 -62q-15 26 -37.5 48.5t-58.5 22.5q-40 0 -69.5 -35.5t-29.5 -110.5q0 -72 30 -110t87 -38q51 0 81.5 15.5t46.5 31.5l36 -102q-27 -20 -73.5 -36
t-106.5 -16z" />
    <glyph glyph-name="afii10084" unicode="&#x442;" horiz-adv-x="427" 
d="M151 0v377h-146v105h417v-105h-146v-377h-125z" />
    <glyph glyph-name="afii10085" unicode="&#x443;" horiz-adv-x="494" 
d="M32 -191l-11 111q54 0 96.5 13t65.5 42q4 5 7 11t7 12l-198 484h133l134 -370q43 114 58 171q38 140 52 199h122q-37 -147 -78.5 -266t-83.5 -204q-40 -76 -72 -118q-32 -39 -93 -61.5t-139 -23.5z" />
    <glyph glyph-name="afii10086" unicode="&#x444;" horiz-adv-x="667" 
d="M274 -189v189q-50 1 -94 17t-76.5 46.5t-51 75t-18.5 102.5t18.5 102t51 74t76.5 46t94 18v186h119v-186q88 -3 145 -45.5t76 -97t19 -97.5q0 -84 -39.5 -142t-95 -78t-105.5 -21v-189h-119zM274 105v271q-51 -5 -84 -38.5t-33 -96.5t33 -97t84 -39zM393 105q51 5 84 39
t33 97t-33 96.5t-84 38.5v-271z" />
    <glyph glyph-name="afii10087" unicode="&#x445;" horiz-adv-x="485" 
d="M3 0l172 238l-175 244h140l105 -149l106 149h127l-167 -235l174 -247h-139l-107 152l-105 -152h-131z" />
    <glyph glyph-name="afii10088" unicode="&#x446;" horiz-adv-x="546" 
d="M399 -105v105h-337v482h124v-379h161v379h124v-377h45v-210h-117z" />
    <glyph glyph-name="afii10089" unicode="&#x447;" horiz-adv-x="530" 
d="M344 0v141q-59 -15 -130 -15q-75 0 -119 35.5t-55 93.5q-5 42 -5 70v157h124v-151q0 -27 4 -40q6 -26 24.5 -39t55.5 -13q29 0 56 3t45 11v229h124v-482h-124z" />
    <glyph glyph-name="afii10090" unicode="&#x448;" horiz-adv-x="802" 
d="M62 0v482h123v-379h156v379h120v-379h156v379h123v-482h-678z" />
    <glyph glyph-name="afii10091" unicode="&#x449;" horiz-adv-x="810" 
d="M663 -105v105h-601v482h123v-379h153v379h120v-379h153v379h123v-377h46v-210h-117z" />
    <glyph glyph-name="afii10092" unicode="&#x44a;" horiz-adv-x="540" 
d="M85 0v379h-80v103h203v-122h96q77 0 125 -30q39 -25 58 -64.5t19 -83.5q0 -68 -34 -111t-80 -57t-88 -14h-219zM208 95h83q45 0 63.5 25t18.5 62q0 35 -18.5 58t-63.5 23h-83v-168z" />
    <glyph glyph-name="afii10093" unicode="&#x44b;" horiz-adv-x="732" 
d="M62 0v482h123v-122h96q78 0 126 -30q39 -25 58 -64.5t19 -83.5q0 -68 -34 -112t-81.5 -57t-87.5 -13h-219zM546 0v482h124v-482h-124zM185 95h83q45 0 64 25t19 62q0 35 -19 58t-64 23h-83v-168z" />
    <glyph glyph-name="afii10094" unicode="&#x44c;" horiz-adv-x="529" 
d="M62 0v482h123v-122h109q77 0 125 -30q39 -25 58 -64.5t19 -83.5q0 -68 -34 -111t-80 -57t-88 -14h-232zM185 95h95q45 0 64 25t19 62q0 35 -19 58t-64 23h-95v-168z" />
    <glyph glyph-name="afii10095" unicode="&#x44d;" horiz-adv-x="459" 
d="M198 -11q-66 0 -108.5 16t-66.5 34l38 102q20 -20 54 -34t79 -14t75 23t33 74h-187v94h187q-3 51 -33 76.5t-68 25.5t-61 -17.5t-40 -42.5l-90 60q30 51 80 79.5t118 28.5q73 0 123 -34q36 -26 56.5 -61t29.5 -79.5t9 -75.5q0 -68 -18.5 -116t-49.5 -79t-72.5 -45.5
t-87.5 -14.5z" />
    <glyph glyph-name="afii10096" unicode="&#x44e;" horiz-adv-x="751" 
d="M490 -11q-52 0 -90 16.5t-64.5 45t-41.5 65.5t-21 79h-88v-195h-123v482h123v-185h89q7 68 46 116.5t85.5 64.5t84.5 16q82 0 134 -43q54 -43 70 -101q18 -59 18 -106q0 -48 -13 -94t-40 -81.5t-69 -57.5t-100 -22zM490 96q41 0 63 26t29 62t7 59q0 74 -28.5 109.5
t-70.5 35.5q-57 0 -77 -44.5t-20 -100.5q0 -31 5 -57.5t16.5 -46.5t30 -31.5t45.5 -11.5z" />
    <glyph glyph-name="afii10097" unicode="&#x44f;" 
d="M33 0v100h29q20 0 32 13.5t25 63.5q-51 28 -69.5 64.5t-18.5 78.5q0 38 11.5 68.5t35.5 51.5t59 31.5t78 10.5h236v-482h-121v162h-93q-18 -85 -45 -121t-53 -38.5t-45 -2.5h-61zM229 257h101v130h-101q-42 0 -57.5 -18.5t-15.5 -48.5q0 -63 73 -63z" />
    <glyph glyph-name="afii10071" unicode="&#x451;" horiz-adv-x="499" 
d="M267 -11q-47 0 -89.5 15t-74.5 46.5t-51 78.5t-19 112q0 61 17.5 108t48 79t71 49t86.5 17q81 0 132 -45q52 -45 67 -106q15 -60 15 -111v-22h-312q4 -61 38 -88t88 -27q48 0 84 15t49 28l33 -101q-22 -16 -42 -23q-12 -6 -59 -15q-46 -10 -82 -10zM161 299h183
q-3 41 -23.5 63t-39.5 27.5t-25 5.5q-32 0 -61 -24t-34 -72zM156 560q-35 0 -52.5 20.5t-17.5 45.5q0 24 17.5 44.5t52.5 20.5q34 0 51 -20.5t17 -44.5q0 -25 -17 -45.5t-51 -20.5zM364 560q-35 0 -52.5 20.5t-17.5 45.5q0 24 17.5 44.5t52.5 20.5q34 0 51 -20.5t17 -44.5
q0 -25 -17 -45.5t-51 -20.5z" />
    <glyph glyph-name="afii10099" unicode="&#x452;" horiz-adv-x="543" 
d="M350 -194l-65 83q42 23 61 54.5t19 88.5v208q0 45 -5 62q-8 29 -27.5 39t-43.5 10q-30 0 -58 -15.5t-44 -29.5v-306h-125v524h-61v83h61v60h125v-60h143v-83h-143v-111q23 19 56 33t78 14q63 0 100 -26.5t54 -69.5q15 -42 15 -115v-223q0 -83 -38 -135t-102 -85z" />
    <glyph glyph-name="afii10100" unicode="&#x453;" horiz-adv-x="404" 
d="M62 0v482h337v-105h-214v-377h-123zM152 538l-33 54l191 141l59 -83z" />
    <glyph glyph-name="afii10101" unicode="&#x454;" horiz-adv-x="453" 
d="M257 -11q-44 0 -84.5 15t-71.5 46t-49.5 78t-18.5 111q0 90 36 152q37 61 89 82t96 21q76 0 122 -36t67 -77l-92 -62q-15 26 -37.5 48.5t-58.5 22.5q-40 0 -70 -36q-21 -25 -27 -70h186v-94h-183q7 -34 25 -56q30 -38 87 -38q51 0 81.5 15.5t46.5 31.5l36 -102
q-27 -20 -73.5 -36t-106.5 -16z" />
    <glyph glyph-name="afii10102" unicode="&#x455;" horiz-adv-x="461" 
d="M238 -11q-26 0 -52.5 3.5t-54.5 10.5q-54 14 -76 28t-27 22l45 95q19 -20 54 -37q35 -15 63.5 -20t47.5 -5q45 0 62.5 13t17.5 35q0 19 -23 34t-89 32q-47 10 -77.5 25t-48.5 34t-24.5 42t-5.5 49q0 64 47.5 104t144.5 40q63 0 113 -18q51 -17 71 -34l-42 -90
q-19 15 -56 31t-85 16q-44 0 -59.5 -14t-15.5 -34q0 -24 18 -34q19 -10 80 -22q74 -13 113 -44t47 -61t8 -50q0 -60 -37 -97q-38 -36 -84.5 -45t-74.5 -9z" />
    <glyph glyph-name="afii10103" unicode="&#x456;" horiz-adv-x="247" 
d="M62 0v482h123v-482h-123zM124 527q-38 0 -56.5 22t-18.5 49q0 26 18.5 48t56.5 22q37 0 55.5 -22t18.5 -48q0 -27 -18.5 -49t-55.5 -22z" />
    <glyph glyph-name="afii10104" unicode="&#x457;" horiz-adv-x="247" 
d="M60 0v482h123v-482h-123zM25 560q-35 0 -52.5 20.5t-17.5 45.5q0 24 17.5 44.5t52.5 20.5q34 0 51 -20.5t17 -44.5q0 -25 -17 -45.5t-51 -20.5zM233 560q-35 0 -52.5 20.5t-17.5 45.5q0 24 17.5 44.5t52.5 20.5q34 0 51 -20.5t17 -44.5q0 -25 -17 -45.5t-51 -20.5z" />
    <glyph glyph-name="afii10105" unicode="&#x458;" horiz-adv-x="257" 
d="M51 -203l-55 97q42 28 58.5 57t16.5 68v463h124v-463q0 -83 -34.5 -135t-109.5 -87zM134 527q-37 0 -55.5 22t-18.5 49q0 26 18.5 48t55.5 22q36 0 54.5 -22t18.5 -48q0 -27 -18.5 -49t-54.5 -22z" />
    <glyph glyph-name="afii10106" unicode="&#x459;" horiz-adv-x="776" 
d="M14 0v100h31q23 0 39 11.5t24 38.5q6 19 12 70t6 166v96h338v-122h80q77 0 125 -30q39 -25 58 -64.5t19 -83.5q0 -68 -34 -111t-80 -57t-88 -14h-200v382h-101v-38q0 -69 -5.5 -123t-14.5 -93q-6 -25 -17.5 -48t-30.5 -41t-46 -28.5t-63 -10.5h-52zM464 95h67
q45 0 63.5 25t18.5 62q0 35 -18.5 59t-63.5 24h-67v-170z" />
    <glyph glyph-name="afii10107" unicode="&#x45a;" horiz-adv-x="792" 
d="M62 0v482h121v-162h160v162h122v-162h115q75 0 118 -28q44 -29 54 -65q11 -39 11 -65q0 -57 -28 -98t-70.5 -52.5t-84.5 -11.5h-237v221h-160v-221h-121zM465 95h96q43 0 58.5 18.5t15.5 44.5q0 24 -15.5 43.5t-58.5 19.5h-96v-126z" />
    <glyph glyph-name="afii10108" unicode="&#x45b;" horiz-adv-x="543" 
d="M62 0v524h-61v83h61v60h125v-60h143v-83h-143v-111q21 17 54.5 31.5t78.5 14.5q63 0 100.5 -25t54.5 -70q8 -23 11.5 -53t3.5 -62v-249h-125v240q0 45 -5 62q-8 24 -23.5 36.5t-47.5 12.5q-24 0 -52.5 -13.5t-49.5 -31.5v-306h-125z" />
    <glyph glyph-name="afii10109" unicode="&#x45c;" 
d="M62 0v482h123v-183h36q12 0 23 14q6 7 11 18.5t10.5 24t10 24.5t9.5 21q20 43 46 62t69 19h76v-103h-37q-22 0 -34.5 -10.5t-22.5 -33.5q-22 -55 -40 -76l170 -259h-151l-115 189h-61v-189h-123zM191 538l-33 54l191 141l59 -83z" />
    <glyph glyph-name="afii10110" unicode="&#x45e;" horiz-adv-x="491" 
d="M37 -191l-11 111q54 0 96.5 13t65.5 42q4 5 7 11t7 12l-198 484h133l134 -370q43 114 58 171q38 140 52 199h122q-37 -147 -78.5 -266t-83.5 -204q-40 -76 -72 -118q-32 -39 -93 -61.5t-139 -23.5zM261 542q-53 0 -101 23q-46 23 -67 60t-23 79h117q8 -33 29.5 -50
t44.5 -17t44.5 17t29.5 50h117q-3 -58 -38 -98q-34 -39 -76.5 -51.5t-76.5 -12.5z" />
    <glyph glyph-name="afii10193" unicode="&#x45f;" horiz-adv-x="539" 
d="M211 -105v105h-149v482h124v-377h167v377h124v-482h-149v-105h-117z" />
    <glyph glyph-name="afii10050" unicode="&#x490;" horiz-adv-x="480" 
d="M69 0v667h284v97h122v-218h-277v-546h-129z" />
    <glyph glyph-name="afii10098" unicode="&#x491;" horiz-adv-x="404" 
d="M62 0v482h231v76h106v-181h-214v-377h-123z" />
    <glyph glyph-name="Wgrave" unicode="&#x1e80;" horiz-adv-x="759" 
d="M136 0l-126 667h127l76 -450l108 450h117l113 -450l75 450h123l-127 -667h-127l-118 480l-113 -480h-128zM451 704l-217 112l59 83l191 -141z" />
    <glyph glyph-name="wgrave" unicode="&#x1e81;" horiz-adv-x="681" 
d="M158 0l-158 482h129l83 -305l78 305h100l79 -305l83 305h129l-158 -482h-100l-83 314l-81 -314h-101zM412 538l-217 112l59 83l191 -141z" />
    <glyph glyph-name="Wacute" unicode="&#x1e82;" horiz-adv-x="759" 
d="M136 0l-126 667h127l76 -450l108 450h117l113 -450l75 450h123l-127 -667h-127l-118 480l-113 -480h-128zM307 704l-33 54l191 141l59 -83z" />
    <glyph glyph-name="wacute" unicode="&#x1e83;" horiz-adv-x="681" 
d="M158 0l-158 482h129l83 -305l78 305h100l79 -305l83 305h129l-158 -482h-100l-83 314l-81 -314h-101zM268 538l-33 54l191 141l59 -83z" />
    <glyph glyph-name="Wdieresis" unicode="&#x1e84;" horiz-adv-x="759" 
d="M136 0l-126 667h127l76 -450l108 450h117l113 -450l75 450h123l-127 -667h-127l-118 480l-113 -480h-128zM276 726q-35 0 -52.5 20.5t-17.5 45.5q0 24 17.5 44.5t52.5 20.5q34 0 51 -20.5t17 -44.5q0 -25 -17 -45.5t-51 -20.5zM484 726q-35 0 -52.5 20.5t-17.5 45.5
q0 24 17.5 44.5t52.5 20.5q34 0 51 -20.5t17 -44.5q0 -25 -17 -45.5t-51 -20.5z" />
    <glyph glyph-name="wdieresis" unicode="&#x1e85;" horiz-adv-x="681" 
d="M158 0l-158 482h129l83 -305l78 305h100l79 -305l83 305h129l-158 -482h-100l-83 314l-81 -314h-101zM237 560q-35 0 -52.5 20.5t-17.5 45.5q0 24 17.5 44.5t52.5 20.5q34 0 51 -20.5t17 -44.5q0 -25 -17 -45.5t-51 -20.5zM445 560q-35 0 -52.5 20.5t-17.5 45.5
q0 24 17.5 44.5t52.5 20.5q34 0 51 -20.5t17 -44.5q0 -25 -17 -45.5t-51 -20.5z" />
    <glyph glyph-name="Ygrave" unicode="&#x1ef2;" horiz-adv-x="552" 
d="M214 0v268l-214 399h143l137 -265l131 265h141l-210 -399v-268h-128zM338 704l-217 112l59 83l191 -141z" />
    <glyph glyph-name="ygrave" unicode="&#x1ef3;" horiz-adv-x="503" 
d="M37 -191l-11 111q54 0 96.5 13t65.5 42q4 5 7 11t7 12l-198 484h133l134 -370q43 114 58 171q38 140 52 199h122q-37 -147 -78.5 -266t-83.5 -204q-40 -76 -72 -118q-32 -39 -93 -61.5t-139 -23.5zM313 538l-217 112l59 83l191 -141z" />
    <glyph glyph-name="endash" unicode="&#x2013;" horiz-adv-x="500" 
d="M0 204v94h501v-94h-501z" />
    <glyph glyph-name="emdash" unicode="&#x2014;" horiz-adv-x="1000" 
d="M0 204v94h1000v-94h-1000z" />
    <glyph glyph-name="afii00208" unicode="&#x2015;" horiz-adv-x="1000" 
d="M0 204v94h1000v-94h-1000z" />
    <glyph glyph-name="quoteleft" unicode="&#x2018;" horiz-adv-x="216" 
d="M131 410q-54 42 -76 87.5t-22 82.5q0 39 19 64t54 25q34 0 52 -21.5t18 -49.5q0 -30 -18.5 -50.5t-42.5 -22.5q2 -17 17 -36.5t32 -33.5z" />
    <glyph glyph-name="quoteright" unicode="&#x2019;" horiz-adv-x="221" 
d="M90 408l-33 45q17 14 32 33.5t17 36.5q-24 2 -42.5 22.5t-18.5 50.5q0 28 18 49.5t52 21.5q35 0 54 -25t19 -64q0 -37 -22 -82.5t-76 -87.5z" />
    <glyph glyph-name="quotesinglbase" unicode="&#x201a;" horiz-adv-x="204" 
d="M71 -110l-33 45q17 14 32 33.5t17 36.5q-24 2 -42.5 22.5t-18.5 50.5q0 28 18 49.5t52 21.5q35 0 54 -25t19 -64q0 -37 -22 -82.5t-76 -87.5z" />
    <glyph glyph-name="quotedblleft" unicode="&#x201c;" horiz-adv-x="415" 
d="M330 410q-54 42 -76 87.5t-22 82.5q0 39 19 64t54 25q34 0 52 -21.5t18 -49.5q0 -30 -18.5 -50.5t-42.5 -22.5q2 -17 17 -36.5t32 -33.5zM131 410q-54 42 -76 87.5t-22 82.5q0 39 19 64t54 25q34 0 52 -21.5t18 -49.5q0 -30 -18.5 -50.5t-42.5 -22.5q2 -17 17 -36.5
t32 -33.5z" />
    <glyph glyph-name="quotedblright" unicode="&#x201d;" horiz-adv-x="420" 
d="M289 408l-33 45q17 14 32 33.5t17 36.5q-24 2 -42.5 22.5t-18.5 50.5q0 28 18 49.5t52 21.5q35 0 54 -25t19 -64q0 -37 -22 -82.5t-76 -87.5zM90 408l-33 45q17 14 32 33.5t17 36.5q-24 2 -42.5 22.5t-18.5 50.5q0 28 18 49.5t52 21.5q35 0 54 -25t19 -64
q0 -37 -22 -82.5t-76 -87.5z" />
    <glyph glyph-name="quotedblbase" unicode="&#x201e;" horiz-adv-x="403" 
d="M270 -110l-33 45q17 14 32 33.5t17 36.5q-24 2 -42.5 22.5t-18.5 50.5q0 28 18 49.5t52 21.5q35 0 54 -25t19 -64q0 -37 -22 -82.5t-76 -87.5zM71 -110l-33 45q17 14 32 33.5t17 36.5q-24 2 -42.5 22.5t-18.5 50.5q0 28 18 49.5t52 21.5q35 0 54 -25t19 -64
q0 -37 -22 -82.5t-76 -87.5z" />
    <glyph glyph-name="dagger" unicode="&#x2020;" horiz-adv-x="481" 
d="M170 -146v484h-127v118h127v211h119v-211h130v-118h-130v-484h-119z" />
    <glyph glyph-name="daggerdbl" unicode="&#x2021;" horiz-adv-x="461" 
d="M171 -146v216h-133v109h133v159h-132v118h132v211h119v-211h130v-118h-130v-159h133v-109h-133v-216h-119z" />
    <glyph glyph-name="bullet" unicode="&#x2022;" horiz-adv-x="514" 
d="M257 179q-42 0 -80 16.5t-66 44.5t-44.5 65.5t-16.5 79.5q0 62 34 111q34 51 82 73t91 22q62 0 111 -34q51 -34 73.5 -82t22.5 -90t-16.5 -79.5t-44.5 -65.5t-66 -44.5t-80 -16.5z" />
    <glyph glyph-name="ellipsis" unicode="&#x2026;" horiz-adv-x="652" 
d="M327 -11q-35 0 -52.5 22.5t-17.5 49.5q0 28 17.5 50.5t52.5 22.5t52 -22.5t17 -50.5q0 -27 -17 -49.5t-52 -22.5zM548 -11q-35 0 -52.5 22.5t-17.5 49.5q0 28 17.5 50.5t52.5 22.5t52 -22.5t17 -50.5q0 -27 -17 -49.5t-52 -22.5zM106 -11q-35 0 -52.5 22.5t-17.5 49.5
q0 28 17.5 50.5t52.5 22.5t52 -22.5t17 -50.5q0 -27 -17 -49.5t-52 -22.5z" />
    <glyph glyph-name="perthousand" unicode="&#x2030;" horiz-adv-x="1045" 
d="M525 -8q-40 0 -69.5 13.5t-49.5 35.5t-30 50t-10 57q0 37 13 66.5t35.5 50t51.5 30.5t59 10q59 0 99 -32q41 -32 51 -68q10 -42 10 -56q0 -30 -10 -58t-30 -50t-50 -35.5t-70 -13.5zM881 -8q-40 0 -70 13.5t-50 35.5t-30 50t-10 57q0 36 12.5 65.5t36 50t52.5 31t59 10.5
q57 0 94 -28q39 -28 52 -63.5t13 -64.5q0 -30 -10 -58t-30 -50t-49.5 -35.5t-69.5 -13.5zM70 -6l-65 72l622 609l67 -69zM524 78q29 0 43 22.5t14 48.5t-14 48.5t-43 22.5t-43 -22.5t-14 -48.5t14 -48.5t43 -22.5zM880 78q29 0 43 22.5t14 48.5t-14 48.5t-43 22.5t-43 -22.5
t-14 -48.5t14 -48.5t43 -22.5zM166 362q-40 0 -69.5 13.5t-49.5 35.5t-30 50t-10 57q0 37 13 66.5t35.5 50t51.5 30.5t60 10q60 0 99 -31q40 -32 50 -69q10 -42 10 -56q0 -30 -10 -58t-30 -50t-50 -35.5t-70 -13.5zM165 448q27 0 42 25t15 47q0 26 -14 48.5t-43 22.5
q-27 0 -42 -21.5t-15 -49.5q0 -13 3.5 -26t10.5 -23t17.5 -16.5t25.5 -6.5z" />
    <glyph glyph-name="guilsinglleft" unicode="&#x2039;" horiz-adv-x="329" 
d="M230 45l-216 205l197 212l82 -68l-136 -138l149 -135z" />
    <glyph glyph-name="guilsinglright" unicode="&#x203a;" horiz-adv-x="329" 
d="M99 45l-76 76l149 135l-136 138l82 68l196 -212z" />
    <glyph glyph-name="fraction" unicode="&#x2044;" horiz-adv-x="210" 
d="M-175 -13l-64 71l622 610l67 -70z" />
    <glyph glyph-name="fourinferior" unicode="&#x2084;" horiz-adv-x="315" 
d="M157 0v71h-146v73l153 184h85v-187h39v-70h-39v-71h-92zM86 141h71v86z" />
    <glyph glyph-name="Euro" unicode="&#x20ac;" 
d="M328 -11q-62 0 -105.5 22.5t-72 58t-43 79.5t-19.5 86h-56v79h329v-79h-132q3 -23 8 -46.5t17 -42.5t32.5 -30.5t55.5 -11.5q44 0 73.5 19.5t49.5 38.5l48 -104q-41 -38 -83 -53.5t-102 -15.5zM32 365v79h58q6 61 38 121q33 57 82 83t109 26q59 0 102 -21t65 -46t28 -31
l-75 -89q-22 38 -46.5 54t-62.5 16q-42 0 -64 -25q-27 -29 -35 -88h130v-79h-329z" />
    <glyph glyph-name="afii61289" unicode="&#x2113;" horiz-adv-x="502" 
d="M215 318q48 52 75 105t27 111q0 42 -13.5 60.5t-33.5 18.5q-23 0 -39 -30.5t-16 -102.5v-162zM457 97q-34 -54 -81.5 -80.5t-104.5 -26.5q-72 0 -111.5 35t-50.5 113q0 8 -1 9q-12 -9 -23.5 -17t-24.5 -16q-8 16 -15.5 31.5t-15.5 30.5q20 14 39 28.5t38 29.5v201
q0 69 11.5 117.5t33 79t52 44.5t68.5 14q60 0 98 -41.5t38 -113.5q0 -45 -13 -86t-37.5 -80t-60.5 -77.5t-81 -79.5v-16q0 -60 19.5 -84.5t51.5 -24.5q42 0 69.5 19.5t47.5 42.5z" />
    <glyph glyph-name="afii61352" unicode="&#x2116;" horiz-adv-x="1031" 
d="M69 0v667h131l257 -416v416h126v-667h-112l-275 441v-441h-127zM825 267q-66 0 -113 35q-47 36 -60 82q-14 48 -14 83q0 45 15 82.5t41 64.5t60.5 40t70.5 13q72 0 117 -42t57 -87t12 -71q0 -37 -11 -72.5t-34 -64t-58 -46t-83 -17.5zM825 360q32 0 52 28t20 79t-20 79
t-52 28q-33 0 -53 -28t-20 -79t20 -79t53 -28z" />
    <glyph glyph-name="trademark" unicode="&#x2122;" horiz-adv-x="624" 
d="M406 378l-54 147v-145h-72v287h88l60 -183l57 183h86v-287h-71v148l-51 -150h-43zM94 380v208h-76v79h225v-79h-76v-208h-73z" />
    <glyph glyph-name="Omega" unicode="&#x2126;" horiz-adv-x="665" 
d="M56 0v120h86q-39 38 -67 100.5t-28 150.5q0 102 44 176q46 75 112 104q67 30 132 30q99 0 165 -48q66 -45 92 -119q26 -73 26 -140q0 -63 -21.5 -123t-45 -91t-36.5 -40h94v-120h-226v114q54 43 82.5 103t28.5 135q0 103 -44.5 157t-116.5 54q-73 0 -117 -54t-44 -157
q0 -45 10.5 -82.5t27 -66.5t36 -51.5t36.5 -37.5v-114h-226z" />
    <glyph glyph-name="estimated" unicode="&#x212e;" horiz-adv-x="742" 
d="M187 340v-230q75 -75 183 -75q79 0 137.5 35.5t106.5 116.5l44 -26q-29 -45 -59 -77.5t-64 -53.5t-74.5 -30.5t-90.5 -9.5q-75 0 -135 27.5t-102.5 74.5t-65.5 111t-23 137t23 137t65.5 111.5t102.5 74.5t135 27q73 0 133 -26t103 -72.5t67 -111t25 -140.5h-511zM555 386
v183q-79 76 -186 76q-109 0 -182 -76v-183h368z" />
    <glyph glyph-name="partialdiff" unicode="&#x2202;" horiz-adv-x="541" 
d="M269 -10q-50 0 -92.5 17.5t-73.5 49.5t-48.5 78.5t-17.5 104.5q0 51 13.5 96.5t40 79.5t64.5 53.5t87 19.5q25 0 46.5 -5.5t43.5 -19.5q-30 48 -86.5 82.5t-122.5 45.5l16 85q60 -8 125 -32.5t118 -73.5t87.5 -125t34.5 -187q0 -59 -16.5 -108t-47 -85t-74 -56t-97.5 -20
zM266 96q24 -2 44.5 11.5t35.5 37t23 52.5t7 58q0 29 -6 57t-14 47q-17 11 -40 18t-49 7q-48 0 -78.5 -35.5t-30.5 -107.5q0 -36 9 -63t24 -45.5t34.5 -27.5t40.5 -9z" />
    <glyph glyph-name="Delta" unicode="&#x2206;" horiz-adv-x="594" 
d="M0 0l236 667h118l240 -667h-594zM163 107h262l-132 407z" />
    <glyph glyph-name="product" unicode="&#x220f;" horiz-adv-x="646" 
d="M95 0v546h-71v121h598v-121h-71v-546h-128v546h-200v-546h-128z" />
    <glyph glyph-name="summation" unicode="&#x2211;" horiz-adv-x="511" 
d="M39 0v111l196 225l-197 219v112h441v-121h-275l190 -210l-193 -215h282v-121h-444z" />
    <glyph glyph-name="minus" unicode="&#x2212;" horiz-adv-x="515" 
d="M24 275v110h467v-110h-467z" />
    <glyph glyph-name="uni2215" unicode="&#x2215;" horiz-adv-x="210" 
d="M-175 -13l-64 71l622 610l67 -70z" />
    <glyph glyph-name="uni2219" unicode="&#x2219;" horiz-adv-x="326" 
d="M163 297q-35 0 -52.5 22.5t-17.5 49.5q0 28 17.5 50.5t52.5 22.5t52 -22.5t17 -50.5q0 -27 -17 -49.5t-52 -22.5z" />
    <glyph glyph-name="radical" unicode="&#x221a;" 
d="M203 0l-118 243h-70v104h135l85 -172l173 565l104 -33l-223 -707h-86z" />
    <glyph glyph-name="infinity" unicode="&#x221e;" horiz-adv-x="542" 
d="M155 207q-33 0 -56.5 12.5t-38.5 33.5t-22.5 48.5t-7.5 57.5q0 75 35 113.5t90 38.5q42 0 69 -24t48 -72q24 55 51 75t68 20q57 0 89 -39.5t32 -111.5q0 -44 -16 -80q-15 -38 -43 -55t-62 -17q-16 0 -31 3.5t-29.5 14t-29 29t-29.5 48.5q-25 -57 -54 -76t-63 -19z
M387 284q24 0 35.5 23t11.5 53q0 35 -13.5 55t-35.5 20q-20 0 -34.5 -21.5t-28.5 -55.5q13 -30 28.5 -52t36.5 -22zM160 285q22 0 36 21t27 53q-17 38 -31 56.5t-32 18.5q-26 0 -38.5 -23.5t-12.5 -51.5q0 -34 13 -54t38 -20z" />
    <glyph glyph-name="integral" unicode="&#x222b;" horiz-adv-x="392" 
d="M17 -191v105q51 2 77 23.5t31.5 47t5.5 45.5v428q0 112 61 163t182 53v-105q-39 -2 -61.5 -13t-34 -27t-15 -36t-3.5 -40v-428q0 -112 -61 -163t-182 -53z" />
    <glyph glyph-name="approxequal" unicode="&#x2248;" 
d="M364 133q-41 0 -108 19t-106 19q-37 0 -98 -37l-49 98q35 24 72 39t72 15q37 0 107 -19t108 -19q36 0 98 38l50 -96q-33 -26 -70.5 -41.5t-75.5 -15.5zM364 348q-41 0 -108 19t-106 19q-37 0 -98 -37l-49 98q35 24 72 39t72 15q37 0 107 -19t108 -19q36 0 98 38l50 -96
q-33 -26 -70.5 -41.5t-75.5 -15.5z" />
    <glyph glyph-name="notequal" unicode="&#x2260;" 
d="M106 62l-69 58l29 35h-44v110h132l85 105h-217v110h306l78 96l72 -60l-30 -36h42v-110h-132l-85 -105h217v-110h-308z" />
    <glyph glyph-name="lessequal" unicode="&#x2264;" 
d="M23 0v110h468v-110h-468zM466 129l-444 162v83l431 153l37 -108l-250 -85l260 -94z" />
    <glyph glyph-name="greaterequal" unicode="&#x2265;" 
d="M22 0v110h468v-110h-468zM47 129l-34 111l260 94l-250 85l37 108l431 -153v-83z" />
    <glyph glyph-name="lozenge" unicode="&#x25ca;" horiz-adv-x="505" 
d="M252 -16l-200 373l200 363l201 -363zM255 151l99 205l-102 192l-101 -191z" />
    <glyph glyph-name="fi" unicode="&#xfb01;" horiz-adv-x="548" 
d="M79 0v382h-64v100h64v17q0 72 32.5 113.5t80.5 55.5q43 13 85 13q62 0 101.5 -13t65.5 -29l-47 -98q-19 13 -48 21.5t-63 8.5t-59 -17t-25 -60v-12h104v-100h-103v-382h-124zM362 0v482h124v-482h-124z" />
    <glyph glyph-name="fl" unicode="&#xfb02;" horiz-adv-x="605" 
d="M487 -11q-41 0 -66.5 12.5t-39.5 32t-18 42.5t-4 41v455q-24 3 -53 3q-45 0 -74.5 -18t-29.5 -60v-15h97v-100h-96v-382h-124v382h-64v100h65v15q0 77 44 119q43 43 99 53q63 11 97 11q4 0 22 -1.5t50 -3.5q50 -4 91 -14v-520q0 -26 10.5 -35.5t29.5 -9.5t40 9l22 -94
q-4 -4 -36 -13q-30 -9 -62 -9z" />
    <glyph glyph-name="cedilla.top" horiz-adv-x="442" 
d="M197 559l85 130h83l-59 -130h-109z" />
    <glyph glyph-name="tonos.cap" horiz-adv-x="241" 
d="M99 403l-74 24l64 241l110 -32z" />
    <glyph glyph-name="commaaccent" horiz-adv-x="442" 
d="M128 -181l59 137h110l-81 -137h-88z" />
    <glyph glyph-name="ascendercaron" horiz-adv-x="442" 
d="M168 497v170h107l-34 -170h-73z" />
    <glyph glyph-name="kratka" horiz-adv-x="500" 
d="M254 705q-53 0 -101 23q-46 23 -67 60t-23 79h117q8 -33 29.5 -50t44.5 -17t44.5 17t29.5 50h117q-3 -58 -38 -98q-34 -39 -76.5 -51.5t-76.5 -12.5z" />
    <hkern u1="&#x26;" u2="&#x178;" k="56" />
    <hkern u1="&#x26;" u2="&#x176;" k="56" />
    <hkern u1="&#x26;" u2="&#x166;" k="51" />
    <hkern u1="&#x26;" u2="&#x164;" k="51" />
    <hkern u1="&#x26;" u2="&#x162;" k="51" />
    <hkern u1="&#x26;" u2="&#x153;" k="25" />
    <hkern u1="&#x26;" u2="&#x151;" k="25" />
    <hkern u1="&#x26;" u2="&#x14f;" k="25" />
    <hkern u1="&#x26;" u2="&#x14d;" k="25" />
    <hkern u1="&#x26;" u2="&#x123;" k="25" />
    <hkern u1="&#x26;" u2="&#x121;" k="25" />
    <hkern u1="&#x26;" u2="&#x11f;" k="25" />
    <hkern u1="&#x26;" u2="&#x11d;" k="25" />
    <hkern u1="&#x26;" u2="&#x11b;" k="25" />
    <hkern u1="&#x26;" u2="&#x119;" k="25" />
    <hkern u1="&#x26;" u2="&#x117;" k="25" />
    <hkern u1="&#x26;" u2="&#x115;" k="25" />
    <hkern u1="&#x26;" u2="&#x113;" k="25" />
    <hkern u1="&#x26;" u2="&#x111;" k="25" />
    <hkern u1="&#x26;" u2="&#x10f;" k="25" />
    <hkern u1="&#x26;" u2="&#x10d;" k="25" />
    <hkern u1="&#x26;" u2="&#x10b;" k="25" />
    <hkern u1="&#x26;" u2="&#x109;" k="25" />
    <hkern u1="&#x26;" u2="&#x107;" k="25" />
    <hkern u1="&#x26;" u2="&#xf6;" k="25" />
    <hkern u1="&#x26;" u2="&#xf5;" k="25" />
    <hkern u1="&#x26;" u2="&#xf4;" k="25" />
    <hkern u1="&#x26;" u2="&#xf3;" k="25" />
    <hkern u1="&#x26;" u2="&#xf2;" k="25" />
    <hkern u1="&#x26;" u2="&#xf0;" k="25" />
    <hkern u1="&#x26;" u2="&#xeb;" k="25" />
    <hkern u1="&#x26;" u2="&#xea;" k="25" />
    <hkern u1="&#x26;" u2="&#xe9;" k="25" />
    <hkern u1="&#x26;" u2="&#xe8;" k="25" />
    <hkern u1="&#x26;" u2="&#xe7;" k="25" />
    <hkern u1="&#x26;" u2="&#xdd;" k="56" />
    <hkern u1="&#x26;" u2="q" k="25" />
    <hkern u1="&#x26;" u2="o" k="25" />
    <hkern u1="&#x26;" u2="g" k="25" />
    <hkern u1="&#x26;" u2="e" k="25" />
    <hkern u1="&#x26;" u2="d" k="25" />
    <hkern u1="&#x26;" u2="c" k="25" />
    <hkern u1="&#x26;" u2="Y" k="56" />
    <hkern u1="&#x26;" u2="V" k="23" />
    <hkern u1="&#x26;" u2="T" k="51" />
    <hkern u1="&#x2a;" u2="&#x104;" k="39" />
    <hkern u1="&#x2a;" u2="&#x102;" k="39" />
    <hkern u1="&#x2a;" u2="&#x100;" k="39" />
    <hkern u1="&#x2a;" u2="&#xc5;" k="39" />
    <hkern u1="&#x2a;" u2="&#xc4;" k="39" />
    <hkern u1="&#x2a;" u2="&#xc3;" k="39" />
    <hkern u1="&#x2a;" u2="&#xc2;" k="39" />
    <hkern u1="&#x2a;" u2="&#xc1;" k="39" />
    <hkern u1="&#x2a;" u2="&#xc0;" k="39" />
    <hkern u1="&#x2a;" u2="&#xb8;" k="80" />
    <hkern u1="&#x2a;" u2="A" k="39" />
    <hkern u1="&#x2c;" u2="&#x45e;" k="45" />
    <hkern u1="&#x2c;" u2="&#x447;" k="45" />
    <hkern u1="&#x2c;" u2="&#x443;" k="45" />
    <hkern u1="&#x2c;" u2="&#x442;" k="54" />
    <hkern u1="&#x2c;" u2="&#x427;" k="84" />
    <hkern u1="&#x2c;" u2="&#x424;" k="30" />
    <hkern u1="&#x2c;" u2="&#x422;" k="59" />
    <hkern u1="&#x2c;" u2="&#x3c7;" k="43" />
    <hkern u1="&#x2c;" u2="&#x3c4;" k="68" />
    <hkern u1="&#x2c;" u2="&#x3c0;" k="47" />
    <hkern u1="&#x2c;" u2="&#x3bd;" k="60" />
    <hkern u1="&#x2c;" u2="&#x3b3;" k="55" />
    <hkern u1="&#x2c;" u2="&#x3ab;" k="100" />
    <hkern u1="&#x2c;" u2="&#x3a8;" k="77" />
    <hkern u1="&#x2c;" u2="&#x3a6;" k="40" />
    <hkern u1="&#x2c;" u2="&#x3a5;" k="100" />
    <hkern u1="&#x2c;" u2="&#x3a4;" k="64" />
    <hkern u1="&#x2c;" u2="&#x178;" k="84" />
    <hkern u1="&#x2c;" u2="&#x177;" k="28" />
    <hkern u1="&#x2c;" u2="&#x176;" k="84" />
    <hkern u1="&#x2c;" u2="&#x175;" k="33" />
    <hkern u1="&#x2c;" u2="&#x174;" k="30" />
    <hkern u1="&#x2c;" u2="&#x166;" k="80" />
    <hkern u1="&#x2c;" u2="&#x164;" k="80" />
    <hkern u1="&#x2c;" u2="&#x162;" k="80" />
    <hkern u1="&#x2c;" u2="&#xff;" k="28" />
    <hkern u1="&#x2c;" u2="&#xfd;" k="28" />
    <hkern u1="&#x2c;" u2="&#xdd;" k="84" />
    <hkern u1="&#x2c;" u2="y" k="28" />
    <hkern u1="&#x2c;" u2="w" k="33" />
    <hkern u1="&#x2c;" u2="v" k="47" />
    <hkern u1="&#x2c;" u2="Y" k="84" />
    <hkern u1="&#x2c;" u2="W" k="30" />
    <hkern u1="&#x2c;" u2="V" k="55" />
    <hkern u1="&#x2c;" u2="T" k="80" />
    <hkern u1="&#x2d;" u2="&#x422;" k="66" />
    <hkern u1="&#x2d;" u2="&#x3c7;" k="52" />
    <hkern u1="&#x2d;" u2="&#x3bb;" k="30" />
    <hkern u1="&#x2d;" u2="&#x3ab;" k="56" />
    <hkern u1="&#x2d;" u2="&#x3a5;" k="56" />
    <hkern u1="&#x2d;" u2="&#x3a4;" k="70" />
    <hkern u1="&#x2d;" u2="&#x178;" k="37" />
    <hkern u1="&#x2d;" u2="&#x176;" k="37" />
    <hkern u1="&#x2d;" u2="&#x166;" k="29" />
    <hkern u1="&#x2d;" u2="&#x164;" k="29" />
    <hkern u1="&#x2d;" u2="&#x162;" k="29" />
    <hkern u1="&#x2d;" u2="&#xdd;" k="37" />
    <hkern u1="&#x2d;" u2="Y" k="37" />
    <hkern u1="&#x2d;" u2="X" k="8" />
    <hkern u1="&#x2d;" u2="V" k="12" />
    <hkern u1="&#x2d;" u2="T" k="29" />
    <hkern u1="&#x2e;" u2="&#x45e;" k="45" />
    <hkern u1="&#x2e;" u2="&#x447;" k="45" />
    <hkern u1="&#x2e;" u2="&#x443;" k="45" />
    <hkern u1="&#x2e;" u2="&#x442;" k="54" />
    <hkern u1="&#x2e;" u2="&#x427;" k="84" />
    <hkern u1="&#x2e;" u2="&#x424;" k="30" />
    <hkern u1="&#x2e;" u2="&#x422;" k="59" />
    <hkern u1="&#x2e;" u2="&#x3c7;" k="43" />
    <hkern u1="&#x2e;" u2="&#x3c4;" k="68" />
    <hkern u1="&#x2e;" u2="&#x3c0;" k="47" />
    <hkern u1="&#x2e;" u2="&#x3bd;" k="60" />
    <hkern u1="&#x2e;" u2="&#x3b3;" k="55" />
    <hkern u1="&#x2e;" u2="&#x3ab;" k="100" />
    <hkern u1="&#x2e;" u2="&#x3a8;" k="77" />
    <hkern u1="&#x2e;" u2="&#x3a6;" k="40" />
    <hkern u1="&#x2e;" u2="&#x3a5;" k="100" />
    <hkern u1="&#x2e;" u2="&#x3a4;" k="64" />
    <hkern u1="&#x2e;" u2="&#x178;" k="84" />
    <hkern u1="&#x2e;" u2="&#x177;" k="28" />
    <hkern u1="&#x2e;" u2="&#x176;" k="84" />
    <hkern u1="&#x2e;" u2="&#x175;" k="33" />
    <hkern u1="&#x2e;" u2="&#x174;" k="30" />
    <hkern u1="&#x2e;" u2="&#x166;" k="80" />
    <hkern u1="&#x2e;" u2="&#x164;" k="80" />
    <hkern u1="&#x2e;" u2="&#x162;" k="80" />
    <hkern u1="&#x2e;" u2="&#xff;" k="28" />
    <hkern u1="&#x2e;" u2="&#xfd;" k="28" />
    <hkern u1="&#x2e;" u2="&#xdd;" k="84" />
    <hkern u1="&#x2e;" u2="y" k="28" />
    <hkern u1="&#x2e;" u2="w" k="33" />
    <hkern u1="&#x2e;" u2="v" k="47" />
    <hkern u1="&#x2e;" u2="Y" k="84" />
    <hkern u1="&#x2e;" u2="W" k="30" />
    <hkern u1="&#x2e;" u2="V" k="55" />
    <hkern u1="&#x2e;" u2="T" k="80" />
    <hkern u1="&#x2f;" u2="&#x219;" k="84" />
    <hkern u1="&#x2f;" u2="&#x17e;" k="65" />
    <hkern u1="&#x2f;" u2="&#x17c;" k="65" />
    <hkern u1="&#x2f;" u2="&#x17a;" k="65" />
    <hkern u1="&#x2f;" u2="&#x177;" k="28" />
    <hkern u1="&#x2f;" u2="&#x173;" k="65" />
    <hkern u1="&#x2f;" u2="&#x171;" k="65" />
    <hkern u1="&#x2f;" u2="&#x16f;" k="65" />
    <hkern u1="&#x2f;" u2="&#x16d;" k="65" />
    <hkern u1="&#x2f;" u2="&#x16b;" k="65" />
    <hkern u1="&#x2f;" u2="&#x169;" k="65" />
    <hkern u1="&#x2f;" u2="&#x161;" k="84" />
    <hkern u1="&#x2f;" u2="&#x15f;" k="84" />
    <hkern u1="&#x2f;" u2="&#x15d;" k="84" />
    <hkern u1="&#x2f;" u2="&#x15b;" k="84" />
    <hkern u1="&#x2f;" u2="&#x159;" k="31" />
    <hkern u1="&#x2f;" u2="&#x157;" k="51" />
    <hkern u1="&#x2f;" u2="&#x155;" k="51" />
    <hkern u1="&#x2f;" u2="&#x153;" k="98" />
    <hkern u1="&#x2f;" u2="&#x151;" k="98" />
    <hkern u1="&#x2f;" u2="&#x14f;" k="98" />
    <hkern u1="&#x2f;" u2="&#x14d;" k="98" />
    <hkern u1="&#x2f;" u2="&#x14b;" k="51" />
    <hkern u1="&#x2f;" u2="&#x148;" k="51" />
    <hkern u1="&#x2f;" u2="&#x146;" k="51" />
    <hkern u1="&#x2f;" u2="&#x144;" k="51" />
    <hkern u1="&#x2f;" u2="&#x134;" k="110" />
    <hkern u1="&#x2f;" u2="&#x123;" k="98" />
    <hkern u1="&#x2f;" u2="&#x121;" k="98" />
    <hkern u1="&#x2f;" u2="&#x11f;" k="98" />
    <hkern u1="&#x2f;" u2="&#x11d;" k="98" />
    <hkern u1="&#x2f;" u2="&#x11b;" k="98" />
    <hkern u1="&#x2f;" u2="&#x119;" k="98" />
    <hkern u1="&#x2f;" u2="&#x117;" k="98" />
    <hkern u1="&#x2f;" u2="&#x115;" k="98" />
    <hkern u1="&#x2f;" u2="&#x113;" k="98" />
    <hkern u1="&#x2f;" u2="&#x111;" k="98" />
    <hkern u1="&#x2f;" u2="&#x10f;" k="98" />
    <hkern u1="&#x2f;" u2="&#x10d;" k="98" />
    <hkern u1="&#x2f;" u2="&#x10b;" k="98" />
    <hkern u1="&#x2f;" u2="&#x109;" k="98" />
    <hkern u1="&#x2f;" u2="&#x107;" k="98" />
    <hkern u1="&#x2f;" u2="&#x105;" k="89" />
    <hkern u1="&#x2f;" u2="&#x104;" k="107" />
    <hkern u1="&#x2f;" u2="&#x103;" k="89" />
    <hkern u1="&#x2f;" u2="&#x102;" k="107" />
    <hkern u1="&#x2f;" u2="&#x101;" k="69" />
    <hkern u1="&#x2f;" u2="&#x100;" k="107" />
    <hkern u1="&#x2f;" u2="&#xff;" k="28" />
    <hkern u1="&#x2f;" u2="&#xfd;" k="28" />
    <hkern u1="&#x2f;" u2="&#xfc;" k="65" />
    <hkern u1="&#x2f;" u2="&#xfb;" k="65" />
    <hkern u1="&#x2f;" u2="&#xfa;" k="65" />
    <hkern u1="&#x2f;" u2="&#xf9;" k="65" />
    <hkern u1="&#x2f;" u2="&#xf6;" k="98" />
    <hkern u1="&#x2f;" u2="&#xf5;" k="98" />
    <hkern u1="&#x2f;" u2="&#xf4;" k="98" />
    <hkern u1="&#x2f;" u2="&#xf3;" k="98" />
    <hkern u1="&#x2f;" u2="&#xf2;" k="98" />
    <hkern u1="&#x2f;" u2="&#xf1;" k="51" />
    <hkern u1="&#x2f;" u2="&#xf0;" k="98" />
    <hkern u1="&#x2f;" u2="&#xeb;" k="98" />
    <hkern u1="&#x2f;" u2="&#xea;" k="98" />
    <hkern u1="&#x2f;" u2="&#xe9;" k="98" />
    <hkern u1="&#x2f;" u2="&#xe8;" k="98" />
    <hkern u1="&#x2f;" u2="&#xe7;" k="98" />
    <hkern u1="&#x2f;" u2="&#xe6;" k="89" />
    <hkern u1="&#x2f;" u2="&#xe5;" k="89" />
    <hkern u1="&#x2f;" u2="&#xe4;" k="69" />
    <hkern u1="&#x2f;" u2="&#xe3;" k="89" />
    <hkern u1="&#x2f;" u2="&#xe2;" k="89" />
    <hkern u1="&#x2f;" u2="&#xe1;" k="89" />
    <hkern u1="&#x2f;" u2="&#xe0;" k="89" />
    <hkern u1="&#x2f;" u2="&#xc6;" k="164" />
    <hkern u1="&#x2f;" u2="&#xc5;" k="107" />
    <hkern u1="&#x2f;" u2="&#xc4;" k="107" />
    <hkern u1="&#x2f;" u2="&#xc3;" k="107" />
    <hkern u1="&#x2f;" u2="&#xc2;" k="107" />
    <hkern u1="&#x2f;" u2="&#xc1;" k="107" />
    <hkern u1="&#x2f;" u2="&#xc0;" k="107" />
    <hkern u1="&#x2f;" u2="z" k="65" />
    <hkern u1="&#x2f;" u2="y" k="28" />
    <hkern u1="&#x2f;" u2="x" k="37" />
    <hkern u1="&#x2f;" u2="u" k="65" />
    <hkern u1="&#x2f;" u2="s" k="84" />
    <hkern u1="&#x2f;" u2="r" k="51" />
    <hkern u1="&#x2f;" u2="q" k="98" />
    <hkern u1="&#x2f;" u2="p" k="51" />
    <hkern u1="&#x2f;" u2="o" k="98" />
    <hkern u1="&#x2f;" u2="n" k="51" />
    <hkern u1="&#x2f;" u2="m" k="51" />
    <hkern u1="&#x2f;" u2="g" k="84" />
    <hkern u1="&#x2f;" u2="e" k="98" />
    <hkern u1="&#x2f;" u2="d" k="98" />
    <hkern u1="&#x2f;" u2="c" k="98" />
    <hkern u1="&#x2f;" u2="a" k="89" />
    <hkern u1="&#x2f;" u2="J" k="110" />
    <hkern u1="&#x2f;" u2="A" k="107" />
    <hkern u1="&#x40;" u2="&#x178;" k="28" />
    <hkern u1="&#x40;" u2="&#x177;" k="-23" />
    <hkern u1="&#x40;" u2="&#x176;" k="28" />
    <hkern u1="&#x40;" u2="&#x174;" k="5" />
    <hkern u1="&#x40;" u2="&#x135;" k="-35" />
    <hkern u1="&#x40;" u2="&#x123;" k="-8" />
    <hkern u1="&#x40;" u2="&#x121;" k="-8" />
    <hkern u1="&#x40;" u2="&#x11f;" k="-8" />
    <hkern u1="&#x40;" u2="&#x11d;" k="-8" />
    <hkern u1="&#x40;" u2="&#xff;" k="-23" />
    <hkern u1="&#x40;" u2="&#xfd;" k="-23" />
    <hkern u1="&#x40;" u2="&#xdd;" k="28" />
    <hkern u1="&#x40;" u2="y" k="-23" />
    <hkern u1="&#x40;" u2="p" k="-8" />
    <hkern u1="&#x40;" u2="j" k="-35" />
    <hkern u1="&#x40;" u2="g" k="-8" />
    <hkern u1="&#x40;" u2="Y" k="15" />
    <hkern u1="&#x40;" u2="W" k="5" />
    <hkern u1="&#x40;" u2="V" k="8" />
    <hkern u1="&#x40;" u2="J" k="-51" />
    <hkern u1="A" u2="&#x201d;" k="32" />
    <hkern u1="A" u2="&#x201c;" k="89" />
    <hkern u1="A" u2="&#x2019;" k="32" />
    <hkern u1="A" u2="&#x2018;" k="89" />
    <hkern u1="A" u2="&#x178;" k="95" />
    <hkern u1="A" u2="&#x177;" k="30" />
    <hkern u1="A" u2="&#x176;" k="95" />
    <hkern u1="A" u2="&#x175;" k="25" />
    <hkern u1="A" u2="&#x174;" k="40" />
    <hkern u1="A" u2="&#x172;" k="25" />
    <hkern u1="A" u2="&#x170;" k="25" />
    <hkern u1="A" u2="&#x16e;" k="25" />
    <hkern u1="A" u2="&#x16c;" k="25" />
    <hkern u1="A" u2="&#x16a;" k="25" />
    <hkern u1="A" u2="&#x168;" k="25" />
    <hkern u1="A" u2="&#x167;" k="25" />
    <hkern u1="A" u2="&#x166;" k="80" />
    <hkern u1="A" u2="&#x165;" k="25" />
    <hkern u1="A" u2="&#x164;" k="80" />
    <hkern u1="A" u2="&#x163;" k="25" />
    <hkern u1="A" u2="&#x162;" k="80" />
    <hkern u1="A" u2="&#x152;" k="25" />
    <hkern u1="A" u2="&#x150;" k="25" />
    <hkern u1="A" u2="&#x14e;" k="25" />
    <hkern u1="A" u2="&#x14c;" k="25" />
    <hkern u1="A" u2="&#x122;" k="25" />
    <hkern u1="A" u2="&#x120;" k="25" />
    <hkern u1="A" u2="&#x11e;" k="25" />
    <hkern u1="A" u2="&#x11c;" k="25" />
    <hkern u1="A" u2="&#x10c;" k="25" />
    <hkern u1="A" u2="&#x10a;" k="25" />
    <hkern u1="A" u2="&#x108;" k="25" />
    <hkern u1="A" u2="&#x106;" k="25" />
    <hkern u1="A" u2="&#xff;" k="30" />
    <hkern u1="A" u2="&#xfd;" k="30" />
    <hkern u1="A" u2="&#xdd;" k="95" />
    <hkern u1="A" u2="&#xdc;" k="25" />
    <hkern u1="A" u2="&#xdb;" k="25" />
    <hkern u1="A" u2="&#xda;" k="25" />
    <hkern u1="A" u2="&#xd9;" k="25" />
    <hkern u1="A" u2="&#xd8;" k="25" />
    <hkern u1="A" u2="&#xd6;" k="25" />
    <hkern u1="A" u2="&#xd5;" k="25" />
    <hkern u1="A" u2="&#xd4;" k="25" />
    <hkern u1="A" u2="&#xd3;" k="25" />
    <hkern u1="A" u2="&#xd2;" k="25" />
    <hkern u1="A" u2="&#xc7;" k="25" />
    <hkern u1="A" u2="y" k="30" />
    <hkern u1="A" u2="w" k="25" />
    <hkern u1="A" u2="v" k="30" />
    <hkern u1="A" u2="t" k="25" />
    <hkern u1="A" u2="\" k="80" />
    <hkern u1="A" u2="Y" k="95" />
    <hkern u1="A" u2="W" k="40" />
    <hkern u1="A" u2="V" k="85" />
    <hkern u1="A" u2="U" k="25" />
    <hkern u1="A" u2="T" k="80" />
    <hkern u1="A" u2="Q" k="25" />
    <hkern u1="A" u2="O" k="25" />
    <hkern u1="A" u2="G" k="25" />
    <hkern u1="A" u2="C" k="25" />
    <hkern u1="A" u2="&#x2a;" k="39" />
    <hkern u1="B" u2="&#x219;" k="5" />
    <hkern u1="B" u2="&#x17e;" k="5" />
    <hkern u1="B" u2="&#x17c;" k="5" />
    <hkern u1="B" u2="&#x17a;" k="5" />
    <hkern u1="B" u2="&#x178;" k="35" />
    <hkern u1="B" u2="&#x177;" k="5" />
    <hkern u1="B" u2="&#x176;" k="35" />
    <hkern u1="B" u2="&#x167;" k="10" />
    <hkern u1="B" u2="&#x166;" k="15" />
    <hkern u1="B" u2="&#x165;" k="10" />
    <hkern u1="B" u2="&#x164;" k="15" />
    <hkern u1="B" u2="&#x163;" k="10" />
    <hkern u1="B" u2="&#x162;" k="15" />
    <hkern u1="B" u2="&#x161;" k="5" />
    <hkern u1="B" u2="&#x15f;" k="5" />
    <hkern u1="B" u2="&#x15d;" k="5" />
    <hkern u1="B" u2="&#x15b;" k="5" />
    <hkern u1="B" u2="&#xff;" k="5" />
    <hkern u1="B" u2="&#xfd;" k="5" />
    <hkern u1="B" u2="&#xdd;" k="35" />
    <hkern u1="B" u2="z" k="5" />
    <hkern u1="B" u2="y" k="5" />
    <hkern u1="B" u2="t" k="10" />
    <hkern u1="B" u2="s" k="5" />
    <hkern u1="B" u2="Y" k="35" />
    <hkern u1="B" u2="V" k="20" />
    <hkern u1="B" u2="T" k="15" />
    <hkern u1="C" u2="&#x177;" k="19" />
    <hkern u1="C" u2="&#x173;" k="20" />
    <hkern u1="C" u2="&#x171;" k="20" />
    <hkern u1="C" u2="&#x16f;" k="20" />
    <hkern u1="C" u2="&#x16d;" k="20" />
    <hkern u1="C" u2="&#x16b;" k="20" />
    <hkern u1="C" u2="&#x169;" k="20" />
    <hkern u1="C" u2="&#x153;" k="15" />
    <hkern u1="C" u2="&#x152;" k="10" />
    <hkern u1="C" u2="&#x151;" k="15" />
    <hkern u1="C" u2="&#x150;" k="20" />
    <hkern u1="C" u2="&#x14f;" k="15" />
    <hkern u1="C" u2="&#x14e;" k="20" />
    <hkern u1="C" u2="&#x14d;" k="15" />
    <hkern u1="C" u2="&#x14c;" k="20" />
    <hkern u1="C" u2="&#x123;" k="10" />
    <hkern u1="C" u2="&#x122;" k="20" />
    <hkern u1="C" u2="&#x121;" k="10" />
    <hkern u1="C" u2="&#x120;" k="20" />
    <hkern u1="C" u2="&#x11f;" k="10" />
    <hkern u1="C" u2="&#x11e;" k="20" />
    <hkern u1="C" u2="&#x11d;" k="10" />
    <hkern u1="C" u2="&#x11c;" k="20" />
    <hkern u1="C" u2="&#x11b;" k="15" />
    <hkern u1="C" u2="&#x119;" k="15" />
    <hkern u1="C" u2="&#x117;" k="15" />
    <hkern u1="C" u2="&#x115;" k="15" />
    <hkern u1="C" u2="&#x113;" k="15" />
    <hkern u1="C" u2="&#x111;" k="10" />
    <hkern u1="C" u2="&#x10f;" k="10" />
    <hkern u1="C" u2="&#x10d;" k="15" />
    <hkern u1="C" u2="&#x10c;" k="20" />
    <hkern u1="C" u2="&#x10b;" k="15" />
    <hkern u1="C" u2="&#x10a;" k="20" />
    <hkern u1="C" u2="&#x109;" k="15" />
    <hkern u1="C" u2="&#x108;" k="20" />
    <hkern u1="C" u2="&#x107;" k="15" />
    <hkern u1="C" u2="&#x106;" k="20" />
    <hkern u1="C" u2="&#xff;" k="19" />
    <hkern u1="C" u2="&#xfd;" k="19" />
    <hkern u1="C" u2="&#xfc;" k="20" />
    <hkern u1="C" u2="&#xfb;" k="20" />
    <hkern u1="C" u2="&#xfa;" k="20" />
    <hkern u1="C" u2="&#xf9;" k="20" />
    <hkern u1="C" u2="&#xf6;" k="15" />
    <hkern u1="C" u2="&#xf5;" k="15" />
    <hkern u1="C" u2="&#xf4;" k="15" />
    <hkern u1="C" u2="&#xf3;" k="15" />
    <hkern u1="C" u2="&#xf2;" k="15" />
    <hkern u1="C" u2="&#xf0;" k="15" />
    <hkern u1="C" u2="&#xeb;" k="15" />
    <hkern u1="C" u2="&#xea;" k="15" />
    <hkern u1="C" u2="&#xe9;" k="15" />
    <hkern u1="C" u2="&#xe8;" k="15" />
    <hkern u1="C" u2="&#xe7;" k="15" />
    <hkern u1="C" u2="&#xd8;" k="20" />
    <hkern u1="C" u2="&#xd6;" k="20" />
    <hkern u1="C" u2="&#xd5;" k="20" />
    <hkern u1="C" u2="&#xd4;" k="20" />
    <hkern u1="C" u2="&#xd3;" k="20" />
    <hkern u1="C" u2="&#xd2;" k="20" />
    <hkern u1="C" u2="&#xc7;" k="20" />
    <hkern u1="C" u2="y" k="19" />
    <hkern u1="C" u2="u" k="20" />
    <hkern u1="C" u2="q" k="10" />
    <hkern u1="C" u2="o" k="15" />
    <hkern u1="C" u2="g" k="10" />
    <hkern u1="C" u2="e" k="15" />
    <hkern u1="C" u2="d" k="10" />
    <hkern u1="C" u2="c" k="15" />
    <hkern u1="C" u2="Q" k="10" />
    <hkern u1="C" u2="O" k="10" />
    <hkern u1="C" u2="G" k="10" />
    <hkern u1="C" u2="C" k="10" />
    <hkern u1="D" u2="&#x218;" k="10" />
    <hkern u1="D" u2="&#x17e;" k="10" />
    <hkern u1="D" u2="&#x17d;" k="30" />
    <hkern u1="D" u2="&#x17c;" k="10" />
    <hkern u1="D" u2="&#x17b;" k="30" />
    <hkern u1="D" u2="&#x17a;" k="10" />
    <hkern u1="D" u2="&#x179;" k="30" />
    <hkern u1="D" u2="&#x178;" k="40" />
    <hkern u1="D" u2="&#x176;" k="40" />
    <hkern u1="D" u2="&#x166;" k="30" />
    <hkern u1="D" u2="&#x164;" k="30" />
    <hkern u1="D" u2="&#x162;" k="30" />
    <hkern u1="D" u2="&#x160;" k="10" />
    <hkern u1="D" u2="&#x15e;" k="10" />
    <hkern u1="D" u2="&#x15c;" k="10" />
    <hkern u1="D" u2="&#x15a;" k="10" />
    <hkern u1="D" u2="&#x134;" k="45" />
    <hkern u1="D" u2="&#x104;" k="25" />
    <hkern u1="D" u2="&#x102;" k="25" />
    <hkern u1="D" u2="&#x100;" k="25" />
    <hkern u1="D" u2="&#xdd;" k="40" />
    <hkern u1="D" u2="&#xc6;" k="52" />
    <hkern u1="D" u2="&#xc5;" k="25" />
    <hkern u1="D" u2="&#xc4;" k="25" />
    <hkern u1="D" u2="&#xc3;" k="25" />
    <hkern u1="D" u2="&#xc2;" k="25" />
    <hkern u1="D" u2="&#xc1;" k="25" />
    <hkern u1="D" u2="&#xc0;" k="25" />
    <hkern u1="D" u2="&#xb8;" k="20" />
    <hkern u1="D" u2="z" k="10" />
    <hkern u1="D" u2="Z" k="30" />
    <hkern u1="D" u2="Y" k="40" />
    <hkern u1="D" u2="X" k="25" />
    <hkern u1="D" u2="V" k="15" />
    <hkern u1="D" u2="T" k="30" />
    <hkern u1="D" u2="S" k="10" />
    <hkern u1="D" u2="J" k="45" />
    <hkern u1="D" u2="A" k="25" />
    <hkern u1="E" u2="&#x177;" k="20" />
    <hkern u1="E" u2="&#x175;" k="20" />
    <hkern u1="E" u2="&#x173;" k="15" />
    <hkern u1="E" u2="&#x171;" k="15" />
    <hkern u1="E" u2="&#x16f;" k="15" />
    <hkern u1="E" u2="&#x16d;" k="15" />
    <hkern u1="E" u2="&#x16b;" k="15" />
    <hkern u1="E" u2="&#x169;" k="15" />
    <hkern u1="E" u2="&#x167;" k="20" />
    <hkern u1="E" u2="&#x165;" k="20" />
    <hkern u1="E" u2="&#x163;" k="20" />
    <hkern u1="E" u2="&#x153;" k="20" />
    <hkern u1="E" u2="&#x152;" k="20" />
    <hkern u1="E" u2="&#x151;" k="20" />
    <hkern u1="E" u2="&#x150;" k="20" />
    <hkern u1="E" u2="&#x14f;" k="20" />
    <hkern u1="E" u2="&#x14e;" k="20" />
    <hkern u1="E" u2="&#x14d;" k="20" />
    <hkern u1="E" u2="&#x14c;" k="20" />
    <hkern u1="E" u2="&#x123;" k="20" />
    <hkern u1="E" u2="&#x122;" k="20" />
    <hkern u1="E" u2="&#x121;" k="20" />
    <hkern u1="E" u2="&#x120;" k="20" />
    <hkern u1="E" u2="&#x11f;" k="20" />
    <hkern u1="E" u2="&#x11e;" k="20" />
    <hkern u1="E" u2="&#x11d;" k="20" />
    <hkern u1="E" u2="&#x11c;" k="20" />
    <hkern u1="E" u2="&#x11b;" k="20" />
    <hkern u1="E" u2="&#x119;" k="20" />
    <hkern u1="E" u2="&#x117;" k="20" />
    <hkern u1="E" u2="&#x115;" k="20" />
    <hkern u1="E" u2="&#x113;" k="20" />
    <hkern u1="E" u2="&#x111;" k="20" />
    <hkern u1="E" u2="&#x10f;" k="20" />
    <hkern u1="E" u2="&#x10d;" k="20" />
    <hkern u1="E" u2="&#x10c;" k="20" />
    <hkern u1="E" u2="&#x10b;" k="20" />
    <hkern u1="E" u2="&#x10a;" k="20" />
    <hkern u1="E" u2="&#x109;" k="20" />
    <hkern u1="E" u2="&#x108;" k="20" />
    <hkern u1="E" u2="&#x107;" k="20" />
    <hkern u1="E" u2="&#x106;" k="20" />
    <hkern u1="E" u2="&#xff;" k="20" />
    <hkern u1="E" u2="&#xfd;" k="20" />
    <hkern u1="E" u2="&#xfc;" k="15" />
    <hkern u1="E" u2="&#xfb;" k="15" />
    <hkern u1="E" u2="&#xfa;" k="15" />
    <hkern u1="E" u2="&#xf9;" k="15" />
    <hkern u1="E" u2="&#xf6;" k="20" />
    <hkern u1="E" u2="&#xf5;" k="20" />
    <hkern u1="E" u2="&#xf4;" k="20" />
    <hkern u1="E" u2="&#xf3;" k="20" />
    <hkern u1="E" u2="&#xf2;" k="20" />
    <hkern u1="E" u2="&#xf0;" k="20" />
    <hkern u1="E" u2="&#xeb;" k="20" />
    <hkern u1="E" u2="&#xea;" k="20" />
    <hkern u1="E" u2="&#xe9;" k="20" />
    <hkern u1="E" u2="&#xe8;" k="20" />
    <hkern u1="E" u2="&#xe7;" k="20" />
    <hkern u1="E" u2="&#xd6;" k="20" />
    <hkern u1="E" u2="&#xd5;" k="20" />
    <hkern u1="E" u2="&#xd4;" k="20" />
    <hkern u1="E" u2="&#xd3;" k="20" />
    <hkern u1="E" u2="&#xd2;" k="20" />
    <hkern u1="E" u2="&#xc7;" k="20" />
    <hkern u1="E" u2="y" k="20" />
    <hkern u1="E" u2="w" k="20" />
    <hkern u1="E" u2="v" k="25" />
    <hkern u1="E" u2="u" k="15" />
    <hkern u1="E" u2="t" k="20" />
    <hkern u1="E" u2="q" k="20" />
    <hkern u1="E" u2="o" k="20" />
    <hkern u1="E" u2="g" k="20" />
    <hkern u1="E" u2="e" k="20" />
    <hkern u1="E" u2="d" k="20" />
    <hkern u1="E" u2="c" k="20" />
    <hkern u1="E" u2="Q" k="20" />
    <hkern u1="E" u2="O" k="20" />
    <hkern u1="E" u2="G" k="20" />
    <hkern u1="E" u2="C" k="20" />
    <hkern u1="F" u2="&#x2026;" k="43" />
    <hkern u1="F" u2="&#x219;" k="25" />
    <hkern u1="F" u2="&#x17e;" k="20" />
    <hkern u1="F" u2="&#x17c;" k="20" />
    <hkern u1="F" u2="&#x17a;" k="20" />
    <hkern u1="F" u2="&#x173;" k="20" />
    <hkern u1="F" u2="&#x171;" k="20" />
    <hkern u1="F" u2="&#x16f;" k="20" />
    <hkern u1="F" u2="&#x16d;" k="20" />
    <hkern u1="F" u2="&#x16b;" k="20" />
    <hkern u1="F" u2="&#x169;" k="20" />
    <hkern u1="F" u2="&#x161;" k="25" />
    <hkern u1="F" u2="&#x15f;" k="25" />
    <hkern u1="F" u2="&#x15d;" k="25" />
    <hkern u1="F" u2="&#x15b;" k="25" />
    <hkern u1="F" u2="&#x159;" k="25" />
    <hkern u1="F" u2="&#x157;" k="25" />
    <hkern u1="F" u2="&#x155;" k="25" />
    <hkern u1="F" u2="&#x153;" k="35" />
    <hkern u1="F" u2="&#x152;" k="10" />
    <hkern u1="F" u2="&#x151;" k="35" />
    <hkern u1="F" u2="&#x150;" k="10" />
    <hkern u1="F" u2="&#x14f;" k="35" />
    <hkern u1="F" u2="&#x14e;" k="10" />
    <hkern u1="F" u2="&#x14d;" k="35" />
    <hkern u1="F" u2="&#x14c;" k="10" />
    <hkern u1="F" u2="&#x14b;" k="25" />
    <hkern u1="F" u2="&#x148;" k="25" />
    <hkern u1="F" u2="&#x146;" k="25" />
    <hkern u1="F" u2="&#x144;" k="25" />
    <hkern u1="F" u2="&#x134;" k="100" />
    <hkern u1="F" u2="&#x123;" k="35" />
    <hkern u1="F" u2="&#x122;" k="10" />
    <hkern u1="F" u2="&#x121;" k="35" />
    <hkern u1="F" u2="&#x120;" k="10" />
    <hkern u1="F" u2="&#x11f;" k="35" />
    <hkern u1="F" u2="&#x11e;" k="10" />
    <hkern u1="F" u2="&#x11d;" k="35" />
    <hkern u1="F" u2="&#x11c;" k="10" />
    <hkern u1="F" u2="&#x11b;" k="35" />
    <hkern u1="F" u2="&#x119;" k="35" />
    <hkern u1="F" u2="&#x117;" k="35" />
    <hkern u1="F" u2="&#x115;" k="35" />
    <hkern u1="F" u2="&#x113;" k="35" />
    <hkern u1="F" u2="&#x111;" k="35" />
    <hkern u1="F" u2="&#x10f;" k="35" />
    <hkern u1="F" u2="&#x10d;" k="35" />
    <hkern u1="F" u2="&#x10c;" k="10" />
    <hkern u1="F" u2="&#x10b;" k="35" />
    <hkern u1="F" u2="&#x10a;" k="10" />
    <hkern u1="F" u2="&#x109;" k="35" />
    <hkern u1="F" u2="&#x108;" k="10" />
    <hkern u1="F" u2="&#x107;" k="35" />
    <hkern u1="F" u2="&#x106;" k="10" />
    <hkern u1="F" u2="&#x105;" k="42" />
    <hkern u1="F" u2="&#x104;" k="80" />
    <hkern u1="F" u2="&#x103;" k="42" />
    <hkern u1="F" u2="&#x102;" k="80" />
    <hkern u1="F" u2="&#x101;" k="42" />
    <hkern u1="F" u2="&#x100;" k="80" />
    <hkern u1="F" u2="&#xfc;" k="20" />
    <hkern u1="F" u2="&#xfb;" k="20" />
    <hkern u1="F" u2="&#xfa;" k="20" />
    <hkern u1="F" u2="&#xf9;" k="20" />
    <hkern u1="F" u2="&#xf8;" k="35" />
    <hkern u1="F" u2="&#xf6;" k="35" />
    <hkern u1="F" u2="&#xf5;" k="35" />
    <hkern u1="F" u2="&#xf4;" k="35" />
    <hkern u1="F" u2="&#xf3;" k="35" />
    <hkern u1="F" u2="&#xf2;" k="35" />
    <hkern u1="F" u2="&#xf1;" k="25" />
    <hkern u1="F" u2="&#xf0;" k="35" />
    <hkern u1="F" u2="&#xeb;" k="35" />
    <hkern u1="F" u2="&#xea;" k="35" />
    <hkern u1="F" u2="&#xe9;" k="35" />
    <hkern u1="F" u2="&#xe8;" k="35" />
    <hkern u1="F" u2="&#xe7;" k="35" />
    <hkern u1="F" u2="&#xe6;" k="42" />
    <hkern u1="F" u2="&#xe5;" k="42" />
    <hkern u1="F" u2="&#xe4;" k="42" />
    <hkern u1="F" u2="&#xe3;" k="12" />
    <hkern u1="F" u2="&#xe2;" k="42" />
    <hkern u1="F" u2="&#xe1;" k="42" />
    <hkern u1="F" u2="&#xe0;" k="42" />
    <hkern u1="F" u2="&#xd8;" k="10" />
    <hkern u1="F" u2="&#xd6;" k="10" />
    <hkern u1="F" u2="&#xd5;" k="10" />
    <hkern u1="F" u2="&#xd4;" k="10" />
    <hkern u1="F" u2="&#xd3;" k="10" />
    <hkern u1="F" u2="&#xd2;" k="10" />
    <hkern u1="F" u2="&#xc7;" k="10" />
    <hkern u1="F" u2="&#xc6;" k="89" />
    <hkern u1="F" u2="&#xc5;" k="80" />
    <hkern u1="F" u2="&#xc4;" k="80" />
    <hkern u1="F" u2="&#xc3;" k="80" />
    <hkern u1="F" u2="&#xc2;" k="80" />
    <hkern u1="F" u2="&#xc1;" k="80" />
    <hkern u1="F" u2="&#xc0;" k="80" />
    <hkern u1="F" u2="&#xbf;" k="36" />
    <hkern u1="F" u2="&#x7d;" k="-22" />
    <hkern u1="F" u2="z" k="20" />
    <hkern u1="F" u2="u" k="20" />
    <hkern u1="F" u2="s" k="25" />
    <hkern u1="F" u2="r" k="25" />
    <hkern u1="F" u2="q" k="35" />
    <hkern u1="F" u2="p" k="25" />
    <hkern u1="F" u2="o" k="35" />
    <hkern u1="F" u2="n" k="25" />
    <hkern u1="F" u2="m" k="25" />
    <hkern u1="F" u2="g" k="28" />
    <hkern u1="F" u2="e" k="35" />
    <hkern u1="F" u2="d" k="35" />
    <hkern u1="F" u2="c" k="35" />
    <hkern u1="F" u2="a" k="42" />
    <hkern u1="F" u2="]" k="-27" />
    <hkern u1="F" u2="Q" k="10" />
    <hkern u1="F" u2="O" k="10" />
    <hkern u1="F" u2="J" k="100" />
    <hkern u1="F" u2="G" k="10" />
    <hkern u1="F" u2="C" k="10" />
    <hkern u1="F" u2="A" k="80" />
    <hkern u1="F" u2="&#x3f;" k="-41" />
    <hkern u1="F" u2="&#x2e;" k="43" />
    <hkern u1="F" u2="&#x2c;" k="43" />
    <hkern u1="F" u2="&#x29;" k="-18" />
    <hkern u1="F" u2="&#x26;" k="33" />
    <hkern u1="G" u2="&#x178;" k="25" />
    <hkern u1="G" u2="&#x176;" k="25" />
    <hkern u1="G" u2="&#xdd;" k="25" />
    <hkern u1="G" u2="Y" k="25" />
    <hkern u1="G" u2="V" k="10" />
    <hkern u1="J" u2="&#x153;" k="10" />
    <hkern u1="J" u2="&#x151;" k="10" />
    <hkern u1="J" u2="&#x14f;" k="10" />
    <hkern u1="J" u2="&#x14d;" k="10" />
    <hkern u1="J" u2="&#x123;" k="10" />
    <hkern u1="J" u2="&#x121;" k="10" />
    <hkern u1="J" u2="&#x11f;" k="10" />
    <hkern u1="J" u2="&#x11d;" k="10" />
    <hkern u1="J" u2="&#x11b;" k="10" />
    <hkern u1="J" u2="&#x119;" k="10" />
    <hkern u1="J" u2="&#x117;" k="10" />
    <hkern u1="J" u2="&#x115;" k="10" />
    <hkern u1="J" u2="&#x113;" k="10" />
    <hkern u1="J" u2="&#x111;" k="10" />
    <hkern u1="J" u2="&#x10f;" k="10" />
    <hkern u1="J" u2="&#x10d;" k="10" />
    <hkern u1="J" u2="&#x10b;" k="10" />
    <hkern u1="J" u2="&#x109;" k="10" />
    <hkern u1="J" u2="&#x107;" k="10" />
    <hkern u1="J" u2="&#x104;" k="15" />
    <hkern u1="J" u2="&#x102;" k="15" />
    <hkern u1="J" u2="&#x100;" k="15" />
    <hkern u1="J" u2="&#xf6;" k="10" />
    <hkern u1="J" u2="&#xf5;" k="10" />
    <hkern u1="J" u2="&#xf4;" k="10" />
    <hkern u1="J" u2="&#xf3;" k="10" />
    <hkern u1="J" u2="&#xf2;" k="10" />
    <hkern u1="J" u2="&#xf0;" k="10" />
    <hkern u1="J" u2="&#xeb;" k="10" />
    <hkern u1="J" u2="&#xea;" k="10" />
    <hkern u1="J" u2="&#xe9;" k="10" />
    <hkern u1="J" u2="&#xe8;" k="10" />
    <hkern u1="J" u2="&#xe7;" k="10" />
    <hkern u1="J" u2="&#xc5;" k="15" />
    <hkern u1="J" u2="&#xc4;" k="15" />
    <hkern u1="J" u2="&#xc3;" k="15" />
    <hkern u1="J" u2="&#xc2;" k="15" />
    <hkern u1="J" u2="&#xc1;" k="15" />
    <hkern u1="J" u2="&#xc0;" k="15" />
    <hkern u1="J" u2="q" k="10" />
    <hkern u1="J" u2="o" k="10" />
    <hkern u1="J" u2="g" k="10" />
    <hkern u1="J" u2="e" k="10" />
    <hkern u1="J" u2="d" k="10" />
    <hkern u1="J" u2="c" k="10" />
    <hkern u1="J" u2="A" k="15" />
    <hkern u1="K" u2="&#x2039;" k="43" />
    <hkern u1="K" u2="&#x177;" k="40" />
    <hkern u1="K" u2="&#x175;" k="35" />
    <hkern u1="K" u2="&#x173;" k="25" />
    <hkern u1="K" u2="&#x172;" k="15" />
    <hkern u1="K" u2="&#x171;" k="25" />
    <hkern u1="K" u2="&#x170;" k="15" />
    <hkern u1="K" u2="&#x16f;" k="25" />
    <hkern u1="K" u2="&#x16e;" k="15" />
    <hkern u1="K" u2="&#x16d;" k="25" />
    <hkern u1="K" u2="&#x16c;" k="15" />
    <hkern u1="K" u2="&#x16b;" k="25" />
    <hkern u1="K" u2="&#x16a;" k="15" />
    <hkern u1="K" u2="&#x169;" k="25" />
    <hkern u1="K" u2="&#x168;" k="15" />
    <hkern u1="K" u2="&#x167;" k="25" />
    <hkern u1="K" u2="&#x165;" k="25" />
    <hkern u1="K" u2="&#x163;" k="25" />
    <hkern u1="K" u2="&#x153;" k="25" />
    <hkern u1="K" u2="&#x152;" k="35" />
    <hkern u1="K" u2="&#x151;" k="25" />
    <hkern u1="K" u2="&#x150;" k="30" />
    <hkern u1="K" u2="&#x14f;" k="25" />
    <hkern u1="K" u2="&#x14e;" k="30" />
    <hkern u1="K" u2="&#x14d;" k="25" />
    <hkern u1="K" u2="&#x14c;" k="30" />
    <hkern u1="K" u2="&#x123;" k="25" />
    <hkern u1="K" u2="&#x122;" k="30" />
    <hkern u1="K" u2="&#x121;" k="25" />
    <hkern u1="K" u2="&#x120;" k="30" />
    <hkern u1="K" u2="&#x11f;" k="25" />
    <hkern u1="K" u2="&#x11e;" k="30" />
    <hkern u1="K" u2="&#x11d;" k="25" />
    <hkern u1="K" u2="&#x11c;" k="30" />
    <hkern u1="K" u2="&#x11b;" k="25" />
    <hkern u1="K" u2="&#x119;" k="25" />
    <hkern u1="K" u2="&#x117;" k="25" />
    <hkern u1="K" u2="&#x115;" k="25" />
    <hkern u1="K" u2="&#x113;" k="25" />
    <hkern u1="K" u2="&#x111;" k="25" />
    <hkern u1="K" u2="&#x10f;" k="25" />
    <hkern u1="K" u2="&#x10d;" k="25" />
    <hkern u1="K" u2="&#x10c;" k="30" />
    <hkern u1="K" u2="&#x10b;" k="25" />
    <hkern u1="K" u2="&#x10a;" k="30" />
    <hkern u1="K" u2="&#x109;" k="25" />
    <hkern u1="K" u2="&#x108;" k="30" />
    <hkern u1="K" u2="&#x107;" k="25" />
    <hkern u1="K" u2="&#x106;" k="30" />
    <hkern u1="K" u2="&#xff;" k="40" />
    <hkern u1="K" u2="&#xfd;" k="40" />
    <hkern u1="K" u2="&#xfc;" k="25" />
    <hkern u1="K" u2="&#xfb;" k="25" />
    <hkern u1="K" u2="&#xfa;" k="25" />
    <hkern u1="K" u2="&#xf9;" k="25" />
    <hkern u1="K" u2="&#xf6;" k="25" />
    <hkern u1="K" u2="&#xf5;" k="25" />
    <hkern u1="K" u2="&#xf4;" k="25" />
    <hkern u1="K" u2="&#xf3;" k="25" />
    <hkern u1="K" u2="&#xf2;" k="25" />
    <hkern u1="K" u2="&#xf0;" k="25" />
    <hkern u1="K" u2="&#xeb;" k="25" />
    <hkern u1="K" u2="&#xea;" k="25" />
    <hkern u1="K" u2="&#xe9;" k="25" />
    <hkern u1="K" u2="&#xe8;" k="25" />
    <hkern u1="K" u2="&#xe7;" k="25" />
    <hkern u1="K" u2="&#xdc;" k="15" />
    <hkern u1="K" u2="&#xdb;" k="15" />
    <hkern u1="K" u2="&#xda;" k="15" />
    <hkern u1="K" u2="&#xd9;" k="15" />
    <hkern u1="K" u2="&#xd8;" k="30" />
    <hkern u1="K" u2="&#xd6;" k="30" />
    <hkern u1="K" u2="&#xd5;" k="30" />
    <hkern u1="K" u2="&#xd4;" k="30" />
    <hkern u1="K" u2="&#xd3;" k="30" />
    <hkern u1="K" u2="&#xd2;" k="30" />
    <hkern u1="K" u2="&#xc7;" k="30" />
    <hkern u1="K" u2="&#xbf;" k="15" />
    <hkern u1="K" u2="&#xab;" k="43" />
    <hkern u1="K" u2="y" k="40" />
    <hkern u1="K" u2="w" k="35" />
    <hkern u1="K" u2="v" k="35" />
    <hkern u1="K" u2="u" k="15" />
    <hkern u1="K" u2="t" k="25" />
    <hkern u1="K" u2="q" k="25" />
    <hkern u1="K" u2="o" k="25" />
    <hkern u1="K" u2="g" k="25" />
    <hkern u1="K" u2="e" k="25" />
    <hkern u1="K" u2="d" k="25" />
    <hkern u1="K" u2="c" k="25" />
    <hkern u1="K" u2="U" k="15" />
    <hkern u1="K" u2="Q" k="35" />
    <hkern u1="K" u2="O" k="35" />
    <hkern u1="K" u2="G" k="35" />
    <hkern u1="K" u2="C" k="35" />
    <hkern u1="L" u2="&#x201d;" k="32" />
    <hkern u1="L" u2="&#x2019;" k="32" />
    <hkern u1="L" u2="&#x178;" k="92" />
    <hkern u1="L" u2="&#x177;" k="27" />
    <hkern u1="L" u2="&#x176;" k="92" />
    <hkern u1="L" u2="&#x175;" k="33" />
    <hkern u1="L" u2="&#x174;" k="25" />
    <hkern u1="L" u2="&#x173;" k="20" />
    <hkern u1="L" u2="&#x172;" k="7" />
    <hkern u1="L" u2="&#x171;" k="20" />
    <hkern u1="L" u2="&#x170;" k="7" />
    <hkern u1="L" u2="&#x16f;" k="20" />
    <hkern u1="L" u2="&#x16e;" k="7" />
    <hkern u1="L" u2="&#x16d;" k="20" />
    <hkern u1="L" u2="&#x16c;" k="7" />
    <hkern u1="L" u2="&#x16b;" k="20" />
    <hkern u1="L" u2="&#x16a;" k="7" />
    <hkern u1="L" u2="&#x169;" k="20" />
    <hkern u1="L" u2="&#x168;" k="7" />
    <hkern u1="L" u2="&#x167;" k="30" />
    <hkern u1="L" u2="&#x166;" k="83" />
    <hkern u1="L" u2="&#x165;" k="30" />
    <hkern u1="L" u2="&#x164;" k="83" />
    <hkern u1="L" u2="&#x163;" k="30" />
    <hkern u1="L" u2="&#x162;" k="83" />
    <hkern u1="L" u2="&#x153;" k="20" />
    <hkern u1="L" u2="&#x152;" k="10" />
    <hkern u1="L" u2="&#x151;" k="10" />
    <hkern u1="L" u2="&#x150;" k="10" />
    <hkern u1="L" u2="&#x14f;" k="10" />
    <hkern u1="L" u2="&#x14e;" k="10" />
    <hkern u1="L" u2="&#x14d;" k="10" />
    <hkern u1="L" u2="&#x14c;" k="10" />
    <hkern u1="L" u2="&#x123;" k="20" />
    <hkern u1="L" u2="&#x122;" k="10" />
    <hkern u1="L" u2="&#x121;" k="20" />
    <hkern u1="L" u2="&#x120;" k="10" />
    <hkern u1="L" u2="&#x11f;" k="20" />
    <hkern u1="L" u2="&#x11e;" k="10" />
    <hkern u1="L" u2="&#x11d;" k="20" />
    <hkern u1="L" u2="&#x11c;" k="10" />
    <hkern u1="L" u2="&#x11b;" k="10" />
    <hkern u1="L" u2="&#x119;" k="10" />
    <hkern u1="L" u2="&#x117;" k="10" />
    <hkern u1="L" u2="&#x115;" k="10" />
    <hkern u1="L" u2="&#x113;" k="10" />
    <hkern u1="L" u2="&#x111;" k="20" />
    <hkern u1="L" u2="&#x10f;" k="20" />
    <hkern u1="L" u2="&#x10d;" k="10" />
    <hkern u1="L" u2="&#x10c;" k="10" />
    <hkern u1="L" u2="&#x10b;" k="10" />
    <hkern u1="L" u2="&#x10a;" k="10" />
    <hkern u1="L" u2="&#x109;" k="10" />
    <hkern u1="L" u2="&#x108;" k="10" />
    <hkern u1="L" u2="&#x107;" k="10" />
    <hkern u1="L" u2="&#x106;" k="10" />
    <hkern u1="L" u2="&#xff;" k="27" />
    <hkern u1="L" u2="&#xfd;" k="27" />
    <hkern u1="L" u2="&#xfc;" k="20" />
    <hkern u1="L" u2="&#xfb;" k="20" />
    <hkern u1="L" u2="&#xfa;" k="20" />
    <hkern u1="L" u2="&#xf9;" k="20" />
    <hkern u1="L" u2="&#xf6;" k="10" />
    <hkern u1="L" u2="&#xf5;" k="10" />
    <hkern u1="L" u2="&#xf4;" k="10" />
    <hkern u1="L" u2="&#xf3;" k="10" />
    <hkern u1="L" u2="&#xf2;" k="10" />
    <hkern u1="L" u2="&#xf0;" k="20" />
    <hkern u1="L" u2="&#xeb;" k="10" />
    <hkern u1="L" u2="&#xea;" k="10" />
    <hkern u1="L" u2="&#xe9;" k="10" />
    <hkern u1="L" u2="&#xe8;" k="10" />
    <hkern u1="L" u2="&#xe7;" k="10" />
    <hkern u1="L" u2="&#xdd;" k="92" />
    <hkern u1="L" u2="&#xdc;" k="7" />
    <hkern u1="L" u2="&#xdb;" k="7" />
    <hkern u1="L" u2="&#xda;" k="7" />
    <hkern u1="L" u2="&#xd9;" k="7" />
    <hkern u1="L" u2="&#xd8;" k="10" />
    <hkern u1="L" u2="&#xd6;" k="10" />
    <hkern u1="L" u2="&#xd5;" k="10" />
    <hkern u1="L" u2="&#xd4;" k="10" />
    <hkern u1="L" u2="&#xd3;" k="10" />
    <hkern u1="L" u2="&#xd2;" k="10" />
    <hkern u1="L" u2="&#xc7;" k="10" />
    <hkern u1="L" u2="y" k="27" />
    <hkern u1="L" u2="w" k="33" />
    <hkern u1="L" u2="v" k="56" />
    <hkern u1="L" u2="u" k="20" />
    <hkern u1="L" u2="t" k="30" />
    <hkern u1="L" u2="q" k="20" />
    <hkern u1="L" u2="o" k="20" />
    <hkern u1="L" u2="g" k="20" />
    <hkern u1="L" u2="e" k="20" />
    <hkern u1="L" u2="d" k="20" />
    <hkern u1="L" u2="c" k="20" />
    <hkern u1="L" u2="\" k="113" />
    <hkern u1="L" u2="Y" k="70" />
    <hkern u1="L" u2="W" k="25" />
    <hkern u1="L" u2="V" k="80" />
    <hkern u1="L" u2="U" k="7" />
    <hkern u1="L" u2="T" k="48" />
    <hkern u1="L" u2="Q" k="10" />
    <hkern u1="L" u2="O" k="10" />
    <hkern u1="L" u2="G" k="10" />
    <hkern u1="L" u2="C" k="10" />
    <hkern u1="L" u2="&#x2a;" k="23" />
    <hkern u1="O" u2="&#x219;" k="15" />
    <hkern u1="O" u2="&#x218;" k="15" />
    <hkern u1="O" u2="&#x17e;" k="20" />
    <hkern u1="O" u2="&#x17d;" k="30" />
    <hkern u1="O" u2="&#x17c;" k="20" />
    <hkern u1="O" u2="&#x17b;" k="30" />
    <hkern u1="O" u2="&#x17a;" k="20" />
    <hkern u1="O" u2="&#x179;" k="30" />
    <hkern u1="O" u2="&#x178;" k="55" />
    <hkern u1="O" u2="&#x176;" k="55" />
    <hkern u1="O" u2="&#x166;" k="30" />
    <hkern u1="O" u2="&#x164;" k="30" />
    <hkern u1="O" u2="&#x162;" k="30" />
    <hkern u1="O" u2="&#x161;" k="15" />
    <hkern u1="O" u2="&#x160;" k="15" />
    <hkern u1="O" u2="&#x15f;" k="15" />
    <hkern u1="O" u2="&#x15e;" k="15" />
    <hkern u1="O" u2="&#x15d;" k="15" />
    <hkern u1="O" u2="&#x15c;" k="15" />
    <hkern u1="O" u2="&#x15b;" k="15" />
    <hkern u1="O" u2="&#x15a;" k="15" />
    <hkern u1="O" u2="&#x134;" k="41" />
    <hkern u1="O" u2="&#x105;" k="10" />
    <hkern u1="O" u2="&#x104;" k="25" />
    <hkern u1="O" u2="&#x103;" k="10" />
    <hkern u1="O" u2="&#x102;" k="25" />
    <hkern u1="O" u2="&#x101;" k="10" />
    <hkern u1="O" u2="&#x100;" k="25" />
    <hkern u1="O" u2="&#xe6;" k="10" />
    <hkern u1="O" u2="&#xe5;" k="10" />
    <hkern u1="O" u2="&#xe4;" k="10" />
    <hkern u1="O" u2="&#xe3;" k="10" />
    <hkern u1="O" u2="&#xe2;" k="10" />
    <hkern u1="O" u2="&#xe1;" k="10" />
    <hkern u1="O" u2="&#xe0;" k="10" />
    <hkern u1="O" u2="&#xdd;" k="55" />
    <hkern u1="O" u2="&#xc6;" k="38" />
    <hkern u1="O" u2="&#xc5;" k="25" />
    <hkern u1="O" u2="&#xc4;" k="25" />
    <hkern u1="O" u2="&#xc3;" k="25" />
    <hkern u1="O" u2="&#xc2;" k="25" />
    <hkern u1="O" u2="&#xc1;" k="25" />
    <hkern u1="O" u2="&#xc0;" k="25" />
    <hkern u1="O" u2="&#xb8;" k="25" />
    <hkern u1="O" u2="z" k="20" />
    <hkern u1="O" u2="s" k="15" />
    <hkern u1="O" u2="a" k="10" />
    <hkern u1="O" u2="Z" k="30" />
    <hkern u1="O" u2="Y" k="55" />
    <hkern u1="O" u2="X" k="35" />
    <hkern u1="O" u2="V" k="30" />
    <hkern u1="O" u2="T" k="30" />
    <hkern u1="O" u2="S" k="15" />
    <hkern u1="O" u2="J" k="41" />
    <hkern u1="O" u2="A" k="25" />
    <hkern u1="P" u2="&#x2026;" k="63" />
    <hkern u1="P" u2="&#x17d;" k="25" />
    <hkern u1="P" u2="&#x17b;" k="25" />
    <hkern u1="P" u2="&#x179;" k="25" />
    <hkern u1="P" u2="&#x177;" k="-18" />
    <hkern u1="P" u2="&#x175;" k="-32" />
    <hkern u1="P" u2="&#x174;" k="-15" />
    <hkern u1="P" u2="&#x153;" k="15" />
    <hkern u1="P" u2="&#x151;" k="15" />
    <hkern u1="P" u2="&#x14f;" k="15" />
    <hkern u1="P" u2="&#x14d;" k="15" />
    <hkern u1="P" u2="&#x134;" k="120" />
    <hkern u1="P" u2="&#x123;" k="15" />
    <hkern u1="P" u2="&#x121;" k="15" />
    <hkern u1="P" u2="&#x11f;" k="15" />
    <hkern u1="P" u2="&#x11d;" k="15" />
    <hkern u1="P" u2="&#x11b;" k="15" />
    <hkern u1="P" u2="&#x119;" k="15" />
    <hkern u1="P" u2="&#x117;" k="15" />
    <hkern u1="P" u2="&#x115;" k="15" />
    <hkern u1="P" u2="&#x113;" k="15" />
    <hkern u1="P" u2="&#x111;" k="15" />
    <hkern u1="P" u2="&#x10f;" k="15" />
    <hkern u1="P" u2="&#x10d;" k="15" />
    <hkern u1="P" u2="&#x10b;" k="15" />
    <hkern u1="P" u2="&#x109;" k="15" />
    <hkern u1="P" u2="&#x107;" k="15" />
    <hkern u1="P" u2="&#x105;" k="25" />
    <hkern u1="P" u2="&#x104;" k="70" />
    <hkern u1="P" u2="&#x103;" k="25" />
    <hkern u1="P" u2="&#x102;" k="70" />
    <hkern u1="P" u2="&#x101;" k="25" />
    <hkern u1="P" u2="&#x100;" k="70" />
    <hkern u1="P" u2="&#xff;" k="-18" />
    <hkern u1="P" u2="&#xfd;" k="-18" />
    <hkern u1="P" u2="&#xf8;" k="15" />
    <hkern u1="P" u2="&#xf6;" k="15" />
    <hkern u1="P" u2="&#xf5;" k="15" />
    <hkern u1="P" u2="&#xf4;" k="15" />
    <hkern u1="P" u2="&#xf3;" k="15" />
    <hkern u1="P" u2="&#xf2;" k="15" />
    <hkern u1="P" u2="&#xf0;" k="15" />
    <hkern u1="P" u2="&#xeb;" k="15" />
    <hkern u1="P" u2="&#xea;" k="15" />
    <hkern u1="P" u2="&#xe9;" k="15" />
    <hkern u1="P" u2="&#xe8;" k="15" />
    <hkern u1="P" u2="&#xe7;" k="15" />
    <hkern u1="P" u2="&#xe6;" k="25" />
    <hkern u1="P" u2="&#xe5;" k="25" />
    <hkern u1="P" u2="&#xe4;" k="25" />
    <hkern u1="P" u2="&#xe3;" k="25" />
    <hkern u1="P" u2="&#xe2;" k="25" />
    <hkern u1="P" u2="&#xe1;" k="25" />
    <hkern u1="P" u2="&#xe0;" k="25" />
    <hkern u1="P" u2="&#xc6;" k="93" />
    <hkern u1="P" u2="&#xc5;" k="70" />
    <hkern u1="P" u2="&#xc4;" k="70" />
    <hkern u1="P" u2="&#xc3;" k="70" />
    <hkern u1="P" u2="&#xc2;" k="70" />
    <hkern u1="P" u2="&#xc1;" k="70" />
    <hkern u1="P" u2="&#xc0;" k="70" />
    <hkern u1="P" u2="&#xbf;" k="30" />
    <hkern u1="P" u2="y" k="-18" />
    <hkern u1="P" u2="w" k="-32" />
    <hkern u1="P" u2="v" k="-27" />
    <hkern u1="P" u2="q" k="15" />
    <hkern u1="P" u2="o" k="15" />
    <hkern u1="P" u2="g" k="15" />
    <hkern u1="P" u2="e" k="15" />
    <hkern u1="P" u2="d" k="15" />
    <hkern u1="P" u2="c" k="15" />
    <hkern u1="P" u2="a" k="25" />
    <hkern u1="P" u2="Z" k="25" />
    <hkern u1="P" u2="Y" k="10" />
    <hkern u1="P" u2="X" k="20" />
    <hkern u1="P" u2="W" k="-15" />
    <hkern u1="P" u2="J" k="120" />
    <hkern u1="P" u2="A" k="70" />
    <hkern u1="P" u2="&#x2e;" k="63" />
    <hkern u1="P" u2="&#x2c;" k="63" />
    <hkern u1="Q" u2="&#x17d;" k="-23" />
    <hkern u1="Q" u2="&#x17b;" k="-23" />
    <hkern u1="Q" u2="&#x179;" k="-23" />
    <hkern u1="Q" u2="&#x178;" k="55" />
    <hkern u1="Q" u2="&#x176;" k="55" />
    <hkern u1="Q" u2="&#x166;" k="30" />
    <hkern u1="Q" u2="&#x164;" k="30" />
    <hkern u1="Q" u2="&#x162;" k="30" />
    <hkern u1="Q" u2="&#x134;" k="-24" />
    <hkern u1="Q" u2="&#x104;" k="-41" />
    <hkern u1="Q" u2="&#x102;" k="-41" />
    <hkern u1="Q" u2="&#x100;" k="-41" />
    <hkern u1="Q" u2="&#xe6;" k="10" />
    <hkern u1="Q" u2="&#xdd;" k="55" />
    <hkern u1="Q" u2="&#xc6;" k="38" />
    <hkern u1="Q" u2="&#xc5;" k="-41" />
    <hkern u1="Q" u2="&#xc4;" k="-41" />
    <hkern u1="Q" u2="&#xc3;" k="-41" />
    <hkern u1="Q" u2="&#xc2;" k="-41" />
    <hkern u1="Q" u2="&#xc1;" k="-41" />
    <hkern u1="Q" u2="&#xc0;" k="-41" />
    <hkern u1="Q" u2="&#xb8;" k="25" />
    <hkern u1="Q" u2="z" k="20" />
    <hkern u1="Q" u2="s" k="15" />
    <hkern u1="Q" u2="a" k="10" />
    <hkern u1="Q" u2="\" k="46" />
    <hkern u1="Q" u2="Z" k="30" />
    <hkern u1="Q" u2="Y" k="55" />
    <hkern u1="Q" u2="X" k="35" />
    <hkern u1="Q" u2="V" k="30" />
    <hkern u1="Q" u2="T" k="30" />
    <hkern u1="Q" u2="S" k="15" />
    <hkern u1="Q" u2="J" k="-24" />
    <hkern u1="Q" u2="A" k="25" />
    <hkern u1="Q" u2="&#x2f;" k="-45" />
    <hkern u1="R" u2="&#x178;" k="30" />
    <hkern u1="R" u2="&#x176;" k="30" />
    <hkern u1="R" u2="&#x173;" k="15" />
    <hkern u1="R" u2="&#x172;" k="10" />
    <hkern u1="R" u2="&#x171;" k="15" />
    <hkern u1="R" u2="&#x170;" k="10" />
    <hkern u1="R" u2="&#x16f;" k="15" />
    <hkern u1="R" u2="&#x16e;" k="10" />
    <hkern u1="R" u2="&#x16d;" k="15" />
    <hkern u1="R" u2="&#x16c;" k="10" />
    <hkern u1="R" u2="&#x16b;" k="15" />
    <hkern u1="R" u2="&#x16a;" k="10" />
    <hkern u1="R" u2="&#x169;" k="15" />
    <hkern u1="R" u2="&#x168;" k="10" />
    <hkern u1="R" u2="&#x166;" k="10" />
    <hkern u1="R" u2="&#x164;" k="10" />
    <hkern u1="R" u2="&#x162;" k="10" />
    <hkern u1="R" u2="&#x153;" k="20" />
    <hkern u1="R" u2="&#x152;" k="15" />
    <hkern u1="R" u2="&#x151;" k="20" />
    <hkern u1="R" u2="&#x150;" k="15" />
    <hkern u1="R" u2="&#x14f;" k="20" />
    <hkern u1="R" u2="&#x14e;" k="15" />
    <hkern u1="R" u2="&#x14d;" k="20" />
    <hkern u1="R" u2="&#x14c;" k="15" />
    <hkern u1="R" u2="&#x123;" k="20" />
    <hkern u1="R" u2="&#x122;" k="15" />
    <hkern u1="R" u2="&#x121;" k="20" />
    <hkern u1="R" u2="&#x120;" k="15" />
    <hkern u1="R" u2="&#x11f;" k="20" />
    <hkern u1="R" u2="&#x11e;" k="15" />
    <hkern u1="R" u2="&#x11d;" k="20" />
    <hkern u1="R" u2="&#x11c;" k="15" />
    <hkern u1="R" u2="&#x11b;" k="20" />
    <hkern u1="R" u2="&#x119;" k="20" />
    <hkern u1="R" u2="&#x117;" k="20" />
    <hkern u1="R" u2="&#x115;" k="20" />
    <hkern u1="R" u2="&#x113;" k="20" />
    <hkern u1="R" u2="&#x111;" k="20" />
    <hkern u1="R" u2="&#x10f;" k="20" />
    <hkern u1="R" u2="&#x10d;" k="20" />
    <hkern u1="R" u2="&#x10c;" k="15" />
    <hkern u1="R" u2="&#x10b;" k="20" />
    <hkern u1="R" u2="&#x10a;" k="15" />
    <hkern u1="R" u2="&#x109;" k="20" />
    <hkern u1="R" u2="&#x108;" k="15" />
    <hkern u1="R" u2="&#x107;" k="20" />
    <hkern u1="R" u2="&#x106;" k="15" />
    <hkern u1="R" u2="&#xfc;" k="15" />
    <hkern u1="R" u2="&#xfb;" k="15" />
    <hkern u1="R" u2="&#xfa;" k="15" />
    <hkern u1="R" u2="&#xf9;" k="15" />
    <hkern u1="R" u2="&#xf8;" k="20" />
    <hkern u1="R" u2="&#xf6;" k="20" />
    <hkern u1="R" u2="&#xf5;" k="20" />
    <hkern u1="R" u2="&#xf4;" k="20" />
    <hkern u1="R" u2="&#xf3;" k="20" />
    <hkern u1="R" u2="&#xf2;" k="20" />
    <hkern u1="R" u2="&#xf0;" k="20" />
    <hkern u1="R" u2="&#xeb;" k="20" />
    <hkern u1="R" u2="&#xea;" k="20" />
    <hkern u1="R" u2="&#xe9;" k="20" />
    <hkern u1="R" u2="&#xe8;" k="20" />
    <hkern u1="R" u2="&#xe7;" k="20" />
    <hkern u1="R" u2="&#xdd;" k="30" />
    <hkern u1="R" u2="&#xdc;" k="10" />
    <hkern u1="R" u2="&#xdb;" k="10" />
    <hkern u1="R" u2="&#xda;" k="10" />
    <hkern u1="R" u2="&#xd9;" k="10" />
    <hkern u1="R" u2="&#xd8;" k="15" />
    <hkern u1="R" u2="&#xd6;" k="15" />
    <hkern u1="R" u2="&#xd5;" k="15" />
    <hkern u1="R" u2="&#xd4;" k="15" />
    <hkern u1="R" u2="&#xd3;" k="15" />
    <hkern u1="R" u2="&#xd2;" k="15" />
    <hkern u1="R" u2="&#xc7;" k="15" />
    <hkern u1="R" u2="u" k="10" />
    <hkern u1="R" u2="q" k="20" />
    <hkern u1="R" u2="o" k="20" />
    <hkern u1="R" u2="g" k="20" />
    <hkern u1="R" u2="e" k="20" />
    <hkern u1="R" u2="d" k="20" />
    <hkern u1="R" u2="c" k="20" />
    <hkern u1="R" u2="Y" k="10" />
    <hkern u1="R" u2="V" k="20" />
    <hkern u1="R" u2="U" k="10" />
    <hkern u1="R" u2="T" k="10" />
    <hkern u1="R" u2="Q" k="15" />
    <hkern u1="R" u2="O" k="15" />
    <hkern u1="R" u2="G" k="15" />
    <hkern u1="R" u2="C" k="15" />
    <hkern u1="S" u2="&#x218;" k="10" />
    <hkern u1="S" u2="&#x17e;" k="10" />
    <hkern u1="S" u2="&#x17c;" k="10" />
    <hkern u1="S" u2="&#x17a;" k="10" />
    <hkern u1="S" u2="&#x177;" k="15" />
    <hkern u1="S" u2="&#x175;" k="10" />
    <hkern u1="S" u2="&#x167;" k="15" />
    <hkern u1="S" u2="&#x165;" k="15" />
    <hkern u1="S" u2="&#x163;" k="15" />
    <hkern u1="S" u2="&#x160;" k="10" />
    <hkern u1="S" u2="&#x15e;" k="10" />
    <hkern u1="S" u2="&#x15c;" k="10" />
    <hkern u1="S" u2="&#x15a;" k="10" />
    <hkern u1="S" u2="&#xff;" k="15" />
    <hkern u1="S" u2="&#xfd;" k="15" />
    <hkern u1="S" u2="z" k="10" />
    <hkern u1="S" u2="y" k="15" />
    <hkern u1="S" u2="x" k="10" />
    <hkern u1="S" u2="w" k="10" />
    <hkern u1="S" u2="v" k="15" />
    <hkern u1="S" u2="t" k="15" />
    <hkern u1="S" u2="S" k="10" />
    <hkern u1="T" u2="&#x2039;" k="56" />
    <hkern u1="T" u2="&#x2026;" k="46" />
    <hkern u1="T" u2="&#x2014;" k="29" />
    <hkern u1="T" u2="&#x2013;" k="29" />
    <hkern u1="T" u2="&#x219;" k="80" />
    <hkern u1="T" u2="&#x17e;" k="75" />
    <hkern u1="T" u2="&#x17c;" k="75" />
    <hkern u1="T" u2="&#x17a;" k="75" />
    <hkern u1="T" u2="&#x177;" k="56" />
    <hkern u1="T" u2="&#x175;" k="52" />
    <hkern u1="T" u2="&#x173;" k="80" />
    <hkern u1="T" u2="&#x171;" k="80" />
    <hkern u1="T" u2="&#x16f;" k="80" />
    <hkern u1="T" u2="&#x16d;" k="80" />
    <hkern u1="T" u2="&#x16b;" k="80" />
    <hkern u1="T" u2="&#x169;" k="40" />
    <hkern u1="T" u2="&#x161;" k="60" />
    <hkern u1="T" u2="&#x15f;" k="80" />
    <hkern u1="T" u2="&#x15d;" k="40" />
    <hkern u1="T" u2="&#x15b;" k="80" />
    <hkern u1="T" u2="&#x159;" k="80" />
    <hkern u1="T" u2="&#x157;" k="80" />
    <hkern u1="T" u2="&#x155;" k="80" />
    <hkern u1="T" u2="&#x153;" k="75" />
    <hkern u1="T" u2="&#x152;" k="35" />
    <hkern u1="T" u2="&#x151;" k="75" />
    <hkern u1="T" u2="&#x150;" k="35" />
    <hkern u1="T" u2="&#x14f;" k="75" />
    <hkern u1="T" u2="&#x14e;" k="35" />
    <hkern u1="T" u2="&#x14d;" k="75" />
    <hkern u1="T" u2="&#x14c;" k="35" />
    <hkern u1="T" u2="&#x14b;" k="80" />
    <hkern u1="T" u2="&#x148;" k="80" />
    <hkern u1="T" u2="&#x146;" k="80" />
    <hkern u1="T" u2="&#x144;" k="80" />
    <hkern u1="T" u2="&#x135;" k="-46" />
    <hkern u1="T" u2="&#x134;" k="92" />
    <hkern u1="T" u2="&#x123;" k="75" />
    <hkern u1="T" u2="&#x122;" k="35" />
    <hkern u1="T" u2="&#x121;" k="75" />
    <hkern u1="T" u2="&#x120;" k="35" />
    <hkern u1="T" u2="&#x11f;" k="75" />
    <hkern u1="T" u2="&#x11e;" k="35" />
    <hkern u1="T" u2="&#x11d;" k="75" />
    <hkern u1="T" u2="&#x11c;" k="35" />
    <hkern u1="T" u2="&#x11b;" k="75" />
    <hkern u1="T" u2="&#x119;" k="75" />
    <hkern u1="T" u2="&#x117;" k="75" />
    <hkern u1="T" u2="&#x115;" k="75" />
    <hkern u1="T" u2="&#x113;" k="50" />
    <hkern u1="T" u2="&#x111;" k="75" />
    <hkern u1="T" u2="&#x10f;" k="75" />
    <hkern u1="T" u2="&#x10d;" k="75" />
    <hkern u1="T" u2="&#x10c;" k="35" />
    <hkern u1="T" u2="&#x10b;" k="75" />
    <hkern u1="T" u2="&#x10a;" k="35" />
    <hkern u1="T" u2="&#x109;" k="75" />
    <hkern u1="T" u2="&#x108;" k="35" />
    <hkern u1="T" u2="&#x107;" k="75" />
    <hkern u1="T" u2="&#x106;" k="35" />
    <hkern u1="T" u2="&#x105;" k="89" />
    <hkern u1="T" u2="&#x104;" k="80" />
    <hkern u1="T" u2="&#x103;" k="89" />
    <hkern u1="T" u2="&#x102;" k="80" />
    <hkern u1="T" u2="&#x101;" k="49" />
    <hkern u1="T" u2="&#x100;" k="80" />
    <hkern u1="T" u2="&#xff;" k="56" />
    <hkern u1="T" u2="&#xfd;" k="56" />
    <hkern u1="T" u2="&#xfc;" k="80" />
    <hkern u1="T" u2="&#xfb;" k="80" />
    <hkern u1="T" u2="&#xfa;" k="80" />
    <hkern u1="T" u2="&#xf9;" k="80" />
    <hkern u1="T" u2="&#xf8;" k="75" />
    <hkern u1="T" u2="&#xf6;" k="75" />
    <hkern u1="T" u2="&#xf5;" k="35" />
    <hkern u1="T" u2="&#xf4;" k="75" />
    <hkern u1="T" u2="&#xf3;" k="75" />
    <hkern u1="T" u2="&#xf2;" k="75" />
    <hkern u1="T" u2="&#xf1;" k="80" />
    <hkern u1="T" u2="&#xf0;" k="75" />
    <hkern u1="T" u2="&#xeb;" k="75" />
    <hkern u1="T" u2="&#xea;" k="75" />
    <hkern u1="T" u2="&#xe9;" k="75" />
    <hkern u1="T" u2="&#xe8;" k="75" />
    <hkern u1="T" u2="&#xe7;" k="75" />
    <hkern u1="T" u2="&#xe6;" k="89" />
    <hkern u1="T" u2="&#xe5;" k="89" />
    <hkern u1="T" u2="&#xe4;" k="89" />
    <hkern u1="T" u2="&#xe3;" k="40" />
    <hkern u1="T" u2="&#xe2;" k="89" />
    <hkern u1="T" u2="&#xe1;" k="89" />
    <hkern u1="T" u2="&#xe0;" k="89" />
    <hkern u1="T" u2="&#xd8;" k="35" />
    <hkern u1="T" u2="&#xd6;" k="35" />
    <hkern u1="T" u2="&#xd5;" k="35" />
    <hkern u1="T" u2="&#xd4;" k="35" />
    <hkern u1="T" u2="&#xd3;" k="35" />
    <hkern u1="T" u2="&#xd2;" k="35" />
    <hkern u1="T" u2="&#xc7;" k="35" />
    <hkern u1="T" u2="&#xc6;" k="84" />
    <hkern u1="T" u2="&#xc5;" k="80" />
    <hkern u1="T" u2="&#xc4;" k="80" />
    <hkern u1="T" u2="&#xc3;" k="80" />
    <hkern u1="T" u2="&#xc2;" k="80" />
    <hkern u1="T" u2="&#xc1;" k="80" />
    <hkern u1="T" u2="&#xc0;" k="80" />
    <hkern u1="T" u2="&#xbf;" k="90" />
    <hkern u1="T" u2="&#xbb;" k="60" />
    <hkern u1="T" u2="&#xab;" k="56" />
    <hkern u1="T" u2="z" k="75" />
    <hkern u1="T" u2="y" k="56" />
    <hkern u1="T" u2="x" k="52" />
    <hkern u1="T" u2="w" k="52" />
    <hkern u1="T" u2="v" k="42" />
    <hkern u1="T" u2="u" k="80" />
    <hkern u1="T" u2="s" k="80" />
    <hkern u1="T" u2="r" k="80" />
    <hkern u1="T" u2="q" k="75" />
    <hkern u1="T" u2="p" k="80" />
    <hkern u1="T" u2="o" k="75" />
    <hkern u1="T" u2="n" k="80" />
    <hkern u1="T" u2="m" k="80" />
    <hkern u1="T" u2="g" k="75" />
    <hkern u1="T" u2="e" k="75" />
    <hkern u1="T" u2="d" k="75" />
    <hkern u1="T" u2="c" k="75" />
    <hkern u1="T" u2="a" k="89" />
    <hkern u1="T" u2="Q" k="35" />
    <hkern u1="T" u2="O" k="35" />
    <hkern u1="T" u2="J" k="92" />
    <hkern u1="T" u2="G" k="35" />
    <hkern u1="T" u2="C" k="35" />
    <hkern u1="T" u2="A" k="80" />
    <hkern u1="T" u2="&#x40;" k="23" />
    <hkern u1="T" u2="&#x3f;" k="-46" />
    <hkern u1="T" u2="&#x3b;" k="20" />
    <hkern u1="T" u2="&#x3a;" k="20" />
    <hkern u1="T" u2="&#x2e;" k="46" />
    <hkern u1="T" u2="&#x2d;" k="29" />
    <hkern u1="T" u2="&#x2c;" k="46" />
    <hkern u1="T" u2="&#x26;" k="47" />
    <hkern u1="U" u2="&#x219;" k="20" />
    <hkern u1="U" u2="&#x218;" k="10" />
    <hkern u1="U" u2="&#x17e;" k="30" />
    <hkern u1="U" u2="&#x17d;" k="25" />
    <hkern u1="U" u2="&#x17c;" k="30" />
    <hkern u1="U" u2="&#x17b;" k="25" />
    <hkern u1="U" u2="&#x17a;" k="30" />
    <hkern u1="U" u2="&#x179;" k="25" />
    <hkern u1="U" u2="&#x177;" k="10" />
    <hkern u1="U" u2="&#x167;" k="15" />
    <hkern u1="U" u2="&#x165;" k="15" />
    <hkern u1="U" u2="&#x163;" k="15" />
    <hkern u1="U" u2="&#x161;" k="20" />
    <hkern u1="U" u2="&#x160;" k="10" />
    <hkern u1="U" u2="&#x15f;" k="20" />
    <hkern u1="U" u2="&#x15e;" k="10" />
    <hkern u1="U" u2="&#x15d;" k="20" />
    <hkern u1="U" u2="&#x15c;" k="10" />
    <hkern u1="U" u2="&#x15b;" k="20" />
    <hkern u1="U" u2="&#x15a;" k="10" />
    <hkern u1="U" u2="&#x153;" k="15" />
    <hkern u1="U" u2="&#x151;" k="15" />
    <hkern u1="U" u2="&#x14f;" k="15" />
    <hkern u1="U" u2="&#x14d;" k="15" />
    <hkern u1="U" u2="&#x134;" k="40" />
    <hkern u1="U" u2="&#x123;" k="15" />
    <hkern u1="U" u2="&#x121;" k="15" />
    <hkern u1="U" u2="&#x11f;" k="15" />
    <hkern u1="U" u2="&#x11d;" k="15" />
    <hkern u1="U" u2="&#x11b;" k="15" />
    <hkern u1="U" u2="&#x119;" k="15" />
    <hkern u1="U" u2="&#x117;" k="15" />
    <hkern u1="U" u2="&#x115;" k="15" />
    <hkern u1="U" u2="&#x113;" k="15" />
    <hkern u1="U" u2="&#x111;" k="15" />
    <hkern u1="U" u2="&#x10f;" k="15" />
    <hkern u1="U" u2="&#x10d;" k="15" />
    <hkern u1="U" u2="&#x10b;" k="15" />
    <hkern u1="U" u2="&#x109;" k="15" />
    <hkern u1="U" u2="&#x107;" k="15" />
    <hkern u1="U" u2="&#x105;" k="20" />
    <hkern u1="U" u2="&#x104;" k="25" />
    <hkern u1="U" u2="&#x103;" k="20" />
    <hkern u1="U" u2="&#x102;" k="25" />
    <hkern u1="U" u2="&#x101;" k="20" />
    <hkern u1="U" u2="&#x100;" k="25" />
    <hkern u1="U" u2="&#xff;" k="10" />
    <hkern u1="U" u2="&#xfd;" k="10" />
    <hkern u1="U" u2="&#xf6;" k="15" />
    <hkern u1="U" u2="&#xf5;" k="15" />
    <hkern u1="U" u2="&#xf4;" k="15" />
    <hkern u1="U" u2="&#xf3;" k="15" />
    <hkern u1="U" u2="&#xf2;" k="15" />
    <hkern u1="U" u2="&#xf0;" k="15" />
    <hkern u1="U" u2="&#xeb;" k="15" />
    <hkern u1="U" u2="&#xea;" k="15" />
    <hkern u1="U" u2="&#xe9;" k="15" />
    <hkern u1="U" u2="&#xe8;" k="15" />
    <hkern u1="U" u2="&#xe7;" k="15" />
    <hkern u1="U" u2="&#xe6;" k="20" />
    <hkern u1="U" u2="&#xe5;" k="20" />
    <hkern u1="U" u2="&#xe4;" k="20" />
    <hkern u1="U" u2="&#xe3;" k="20" />
    <hkern u1="U" u2="&#xe2;" k="20" />
    <hkern u1="U" u2="&#xe1;" k="20" />
    <hkern u1="U" u2="&#xe0;" k="20" />
    <hkern u1="U" u2="&#xc6;" k="38" />
    <hkern u1="U" u2="&#xc5;" k="25" />
    <hkern u1="U" u2="&#xc4;" k="25" />
    <hkern u1="U" u2="&#xc3;" k="25" />
    <hkern u1="U" u2="&#xc2;" k="25" />
    <hkern u1="U" u2="&#xc1;" k="25" />
    <hkern u1="U" u2="&#xc0;" k="25" />
    <hkern u1="U" u2="z" k="30" />
    <hkern u1="U" u2="y" k="10" />
    <hkern u1="U" u2="x" k="15" />
    <hkern u1="U" u2="t" k="15" />
    <hkern u1="U" u2="s" k="20" />
    <hkern u1="U" u2="q" k="15" />
    <hkern u1="U" u2="o" k="15" />
    <hkern u1="U" u2="g" k="33" />
    <hkern u1="U" u2="e" k="15" />
    <hkern u1="U" u2="d" k="15" />
    <hkern u1="U" u2="c" k="15" />
    <hkern u1="U" u2="a" k="20" />
    <hkern u1="U" u2="Z" k="25" />
    <hkern u1="U" u2="S" k="10" />
    <hkern u1="U" u2="J" k="40" />
    <hkern u1="U" u2="A" k="25" />
    <hkern u1="V" u2="&#x2039;" k="33" />
    <hkern u1="V" u2="&#x2026;" k="32" />
    <hkern u1="V" u2="&#x2014;" k="12" />
    <hkern u1="V" u2="&#x2013;" k="12" />
    <hkern u1="V" u2="&#x219;" k="42" />
    <hkern u1="V" u2="&#x17e;" k="28" />
    <hkern u1="V" u2="&#x17c;" k="28" />
    <hkern u1="V" u2="&#x17a;" k="28" />
    <hkern u1="V" u2="&#x173;" k="35" />
    <hkern u1="V" u2="&#x171;" k="35" />
    <hkern u1="V" u2="&#x16f;" k="35" />
    <hkern u1="V" u2="&#x16d;" k="35" />
    <hkern u1="V" u2="&#x16b;" k="35" />
    <hkern u1="V" u2="&#x169;" k="35" />
    <hkern u1="V" u2="&#x161;" k="42" />
    <hkern u1="V" u2="&#x15f;" k="42" />
    <hkern u1="V" u2="&#x15d;" k="42" />
    <hkern u1="V" u2="&#x15b;" k="42" />
    <hkern u1="V" u2="&#x159;" k="33" />
    <hkern u1="V" u2="&#x157;" k="33" />
    <hkern u1="V" u2="&#x155;" k="33" />
    <hkern u1="V" u2="&#x153;" k="47" />
    <hkern u1="V" u2="&#x152;" k="20" />
    <hkern u1="V" u2="&#x151;" k="47" />
    <hkern u1="V" u2="&#x150;" k="20" />
    <hkern u1="V" u2="&#x14f;" k="47" />
    <hkern u1="V" u2="&#x14e;" k="20" />
    <hkern u1="V" u2="&#x14d;" k="47" />
    <hkern u1="V" u2="&#x14c;" k="20" />
    <hkern u1="V" u2="&#x14b;" k="33" />
    <hkern u1="V" u2="&#x148;" k="33" />
    <hkern u1="V" u2="&#x146;" k="33" />
    <hkern u1="V" u2="&#x144;" k="33" />
    <hkern u1="V" u2="&#x134;" k="106" />
    <hkern u1="V" u2="&#x123;" k="47" />
    <hkern u1="V" u2="&#x122;" k="20" />
    <hkern u1="V" u2="&#x121;" k="47" />
    <hkern u1="V" u2="&#x120;" k="20" />
    <hkern u1="V" u2="&#x11f;" k="47" />
    <hkern u1="V" u2="&#x11e;" k="20" />
    <hkern u1="V" u2="&#x11d;" k="47" />
    <hkern u1="V" u2="&#x11c;" k="20" />
    <hkern u1="V" u2="&#x11b;" k="47" />
    <hkern u1="V" u2="&#x119;" k="47" />
    <hkern u1="V" u2="&#x117;" k="47" />
    <hkern u1="V" u2="&#x115;" k="47" />
    <hkern u1="V" u2="&#x113;" k="47" />
    <hkern u1="V" u2="&#x111;" k="47" />
    <hkern u1="V" u2="&#x10f;" k="47" />
    <hkern u1="V" u2="&#x10d;" k="47" />
    <hkern u1="V" u2="&#x10c;" k="20" />
    <hkern u1="V" u2="&#x10b;" k="47" />
    <hkern u1="V" u2="&#x10a;" k="20" />
    <hkern u1="V" u2="&#x109;" k="47" />
    <hkern u1="V" u2="&#x108;" k="20" />
    <hkern u1="V" u2="&#x107;" k="47" />
    <hkern u1="V" u2="&#x106;" k="20" />
    <hkern u1="V" u2="&#x105;" k="65" />
    <hkern u1="V" u2="&#x104;" k="75" />
    <hkern u1="V" u2="&#x103;" k="65" />
    <hkern u1="V" u2="&#x102;" k="75" />
    <hkern u1="V" u2="&#x101;" k="45" />
    <hkern u1="V" u2="&#x100;" k="75" />
    <hkern u1="V" u2="&#xfc;" k="35" />
    <hkern u1="V" u2="&#xfb;" k="35" />
    <hkern u1="V" u2="&#xfa;" k="35" />
    <hkern u1="V" u2="&#xf9;" k="35" />
    <hkern u1="V" u2="&#xf8;" k="47" />
    <hkern u1="V" u2="&#xf6;" k="47" />
    <hkern u1="V" u2="&#xf5;" k="47" />
    <hkern u1="V" u2="&#xf4;" k="47" />
    <hkern u1="V" u2="&#xf3;" k="47" />
    <hkern u1="V" u2="&#xf2;" k="47" />
    <hkern u1="V" u2="&#xf1;" k="33" />
    <hkern u1="V" u2="&#xf0;" k="47" />
    <hkern u1="V" u2="&#xeb;" k="47" />
    <hkern u1="V" u2="&#xea;" k="47" />
    <hkern u1="V" u2="&#xe9;" k="47" />
    <hkern u1="V" u2="&#xe8;" k="47" />
    <hkern u1="V" u2="&#xe7;" k="47" />
    <hkern u1="V" u2="&#xe6;" k="65" />
    <hkern u1="V" u2="&#xe5;" k="65" />
    <hkern u1="V" u2="&#xe4;" k="65" />
    <hkern u1="V" u2="&#xe3;" k="65" />
    <hkern u1="V" u2="&#xe2;" k="65" />
    <hkern u1="V" u2="&#xe1;" k="65" />
    <hkern u1="V" u2="&#xe0;" k="65" />
    <hkern u1="V" u2="&#xd8;" k="20" />
    <hkern u1="V" u2="&#xd6;" k="20" />
    <hkern u1="V" u2="&#xd5;" k="20" />
    <hkern u1="V" u2="&#xd4;" k="20" />
    <hkern u1="V" u2="&#xd3;" k="20" />
    <hkern u1="V" u2="&#xd2;" k="20" />
    <hkern u1="V" u2="&#xc7;" k="20" />
    <hkern u1="V" u2="&#xc6;" k="94" />
    <hkern u1="V" u2="&#xc5;" k="75" />
    <hkern u1="V" u2="&#xc4;" k="75" />
    <hkern u1="V" u2="&#xc3;" k="75" />
    <hkern u1="V" u2="&#xc2;" k="75" />
    <hkern u1="V" u2="&#xc1;" k="75" />
    <hkern u1="V" u2="&#xc0;" k="75" />
    <hkern u1="V" u2="&#xbf;" k="60" />
    <hkern u1="V" u2="&#xbb;" k="40" />
    <hkern u1="V" u2="&#xab;" k="33" />
    <hkern u1="V" u2="z" k="28" />
    <hkern u1="V" u2="u" k="35" />
    <hkern u1="V" u2="s" k="42" />
    <hkern u1="V" u2="r" k="33" />
    <hkern u1="V" u2="q" k="47" />
    <hkern u1="V" u2="p" k="33" />
    <hkern u1="V" u2="o" k="47" />
    <hkern u1="V" u2="n" k="33" />
    <hkern u1="V" u2="m" k="33" />
    <hkern u1="V" u2="g" k="47" />
    <hkern u1="V" u2="e" k="47" />
    <hkern u1="V" u2="d" k="47" />
    <hkern u1="V" u2="c" k="47" />
    <hkern u1="V" u2="a" k="65" />
    <hkern u1="V" u2="Q" k="20" />
    <hkern u1="V" u2="O" k="20" />
    <hkern u1="V" u2="J" k="106" />
    <hkern u1="V" u2="G" k="20" />
    <hkern u1="V" u2="C" k="20" />
    <hkern u1="V" u2="A" k="75" />
    <hkern u1="V" u2="&#x40;" k="8" />
    <hkern u1="V" u2="&#x3f;" k="-32" />
    <hkern u1="V" u2="&#x2e;" k="32" />
    <hkern u1="V" u2="&#x2d;" k="12" />
    <hkern u1="V" u2="&#x2c;" k="32" />
    <hkern u1="V" u2="&#x26;" k="52" />
    <hkern u1="W" u2="&#x2026;" k="10" />
    <hkern u1="W" u2="&#x219;" k="30" />
    <hkern u1="W" u2="&#x17e;" k="15" />
    <hkern u1="W" u2="&#x17c;" k="15" />
    <hkern u1="W" u2="&#x17a;" k="15" />
    <hkern u1="W" u2="&#x173;" k="20" />
    <hkern u1="W" u2="&#x171;" k="20" />
    <hkern u1="W" u2="&#x16f;" k="20" />
    <hkern u1="W" u2="&#x16d;" k="20" />
    <hkern u1="W" u2="&#x16b;" k="20" />
    <hkern u1="W" u2="&#x169;" k="20" />
    <hkern u1="W" u2="&#x161;" k="30" />
    <hkern u1="W" u2="&#x15f;" k="30" />
    <hkern u1="W" u2="&#x15d;" k="30" />
    <hkern u1="W" u2="&#x15b;" k="30" />
    <hkern u1="W" u2="&#x159;" k="20" />
    <hkern u1="W" u2="&#x157;" k="20" />
    <hkern u1="W" u2="&#x155;" k="20" />
    <hkern u1="W" u2="&#x153;" k="25" />
    <hkern u1="W" u2="&#x151;" k="25" />
    <hkern u1="W" u2="&#x14f;" k="25" />
    <hkern u1="W" u2="&#x14d;" k="25" />
    <hkern u1="W" u2="&#x14b;" k="20" />
    <hkern u1="W" u2="&#x148;" k="20" />
    <hkern u1="W" u2="&#x146;" k="20" />
    <hkern u1="W" u2="&#x144;" k="20" />
    <hkern u1="W" u2="&#x134;" k="35" />
    <hkern u1="W" u2="&#x123;" k="25" />
    <hkern u1="W" u2="&#x121;" k="25" />
    <hkern u1="W" u2="&#x11f;" k="25" />
    <hkern u1="W" u2="&#x11d;" k="25" />
    <hkern u1="W" u2="&#x11b;" k="25" />
    <hkern u1="W" u2="&#x119;" k="25" />
    <hkern u1="W" u2="&#x117;" k="25" />
    <hkern u1="W" u2="&#x115;" k="25" />
    <hkern u1="W" u2="&#x113;" k="25" />
    <hkern u1="W" u2="&#x111;" k="25" />
    <hkern u1="W" u2="&#x10f;" k="25" />
    <hkern u1="W" u2="&#x10d;" k="25" />
    <hkern u1="W" u2="&#x10b;" k="25" />
    <hkern u1="W" u2="&#x109;" k="25" />
    <hkern u1="W" u2="&#x107;" k="25" />
    <hkern u1="W" u2="&#x105;" k="30" />
    <hkern u1="W" u2="&#x104;" k="40" />
    <hkern u1="W" u2="&#x103;" k="30" />
    <hkern u1="W" u2="&#x102;" k="40" />
    <hkern u1="W" u2="&#x101;" k="30" />
    <hkern u1="W" u2="&#x100;" k="40" />
    <hkern u1="W" u2="&#xfc;" k="20" />
    <hkern u1="W" u2="&#xfb;" k="20" />
    <hkern u1="W" u2="&#xfa;" k="20" />
    <hkern u1="W" u2="&#xf9;" k="20" />
    <hkern u1="W" u2="&#xf8;" k="25" />
    <hkern u1="W" u2="&#xf6;" k="25" />
    <hkern u1="W" u2="&#xf5;" k="25" />
    <hkern u1="W" u2="&#xf4;" k="25" />
    <hkern u1="W" u2="&#xf3;" k="25" />
    <hkern u1="W" u2="&#xf2;" k="25" />
    <hkern u1="W" u2="&#xf1;" k="20" />
    <hkern u1="W" u2="&#xf0;" k="25" />
    <hkern u1="W" u2="&#xeb;" k="25" />
    <hkern u1="W" u2="&#xea;" k="25" />
    <hkern u1="W" u2="&#xe9;" k="25" />
    <hkern u1="W" u2="&#xe8;" k="25" />
    <hkern u1="W" u2="&#xe7;" k="25" />
    <hkern u1="W" u2="&#xe6;" k="30" />
    <hkern u1="W" u2="&#xe5;" k="30" />
    <hkern u1="W" u2="&#xe4;" k="30" />
    <hkern u1="W" u2="&#xe3;" k="30" />
    <hkern u1="W" u2="&#xe2;" k="30" />
    <hkern u1="W" u2="&#xe1;" k="30" />
    <hkern u1="W" u2="&#xe0;" k="30" />
    <hkern u1="W" u2="&#xc6;" k="50" />
    <hkern u1="W" u2="&#xc5;" k="40" />
    <hkern u1="W" u2="&#xc4;" k="40" />
    <hkern u1="W" u2="&#xc3;" k="40" />
    <hkern u1="W" u2="&#xc2;" k="40" />
    <hkern u1="W" u2="&#xc1;" k="40" />
    <hkern u1="W" u2="&#xc0;" k="40" />
    <hkern u1="W" u2="&#xbf;" k="40" />
    <hkern u1="W" u2="z" k="15" />
    <hkern u1="W" u2="u" k="20" />
    <hkern u1="W" u2="s" k="30" />
    <hkern u1="W" u2="r" k="20" />
    <hkern u1="W" u2="q" k="25" />
    <hkern u1="W" u2="p" k="20" />
    <hkern u1="W" u2="o" k="25" />
    <hkern u1="W" u2="n" k="20" />
    <hkern u1="W" u2="m" k="20" />
    <hkern u1="W" u2="g" k="25" />
    <hkern u1="W" u2="e" k="25" />
    <hkern u1="W" u2="d" k="25" />
    <hkern u1="W" u2="c" k="25" />
    <hkern u1="W" u2="a" k="30" />
    <hkern u1="W" u2="J" k="35" />
    <hkern u1="W" u2="A" k="40" />
    <hkern u1="W" u2="&#x40;" k="5" />
    <hkern u1="W" u2="&#x3f;" k="-41" />
    <hkern u1="W" u2="&#x2e;" k="10" />
    <hkern u1="W" u2="&#x2c;" k="10" />
    <hkern u1="W" u2="&#x26;" k="24" />
    <hkern u1="X" u2="&#x2014;" k="8" />
    <hkern u1="X" u2="&#x2013;" k="8" />
    <hkern u1="X" u2="&#x177;" k="35" />
    <hkern u1="X" u2="&#x175;" k="30" />
    <hkern u1="X" u2="&#x173;" k="20" />
    <hkern u1="X" u2="&#x171;" k="20" />
    <hkern u1="X" u2="&#x16f;" k="20" />
    <hkern u1="X" u2="&#x16d;" k="20" />
    <hkern u1="X" u2="&#x16b;" k="20" />
    <hkern u1="X" u2="&#x169;" k="20" />
    <hkern u1="X" u2="&#x167;" k="30" />
    <hkern u1="X" u2="&#x165;" k="30" />
    <hkern u1="X" u2="&#x163;" k="30" />
    <hkern u1="X" u2="&#x153;" k="20" />
    <hkern u1="X" u2="&#x152;" k="30" />
    <hkern u1="X" u2="&#x151;" k="20" />
    <hkern u1="X" u2="&#x150;" k="30" />
    <hkern u1="X" u2="&#x14f;" k="20" />
    <hkern u1="X" u2="&#x14e;" k="30" />
    <hkern u1="X" u2="&#x14d;" k="20" />
    <hkern u1="X" u2="&#x14c;" k="30" />
    <hkern u1="X" u2="&#x123;" k="20" />
    <hkern u1="X" u2="&#x122;" k="30" />
    <hkern u1="X" u2="&#x121;" k="20" />
    <hkern u1="X" u2="&#x120;" k="30" />
    <hkern u1="X" u2="&#x11f;" k="20" />
    <hkern u1="X" u2="&#x11e;" k="30" />
    <hkern u1="X" u2="&#x11d;" k="20" />
    <hkern u1="X" u2="&#x11c;" k="30" />
    <hkern u1="X" u2="&#x11b;" k="20" />
    <hkern u1="X" u2="&#x119;" k="20" />
    <hkern u1="X" u2="&#x117;" k="20" />
    <hkern u1="X" u2="&#x115;" k="20" />
    <hkern u1="X" u2="&#x113;" k="20" />
    <hkern u1="X" u2="&#x111;" k="20" />
    <hkern u1="X" u2="&#x10f;" k="20" />
    <hkern u1="X" u2="&#x10d;" k="20" />
    <hkern u1="X" u2="&#x10c;" k="30" />
    <hkern u1="X" u2="&#x10b;" k="20" />
    <hkern u1="X" u2="&#x10a;" k="30" />
    <hkern u1="X" u2="&#x109;" k="20" />
    <hkern u1="X" u2="&#x108;" k="30" />
    <hkern u1="X" u2="&#x107;" k="20" />
    <hkern u1="X" u2="&#x106;" k="30" />
    <hkern u1="X" u2="&#xff;" k="35" />
    <hkern u1="X" u2="&#xfd;" k="35" />
    <hkern u1="X" u2="&#xfc;" k="20" />
    <hkern u1="X" u2="&#xfb;" k="20" />
    <hkern u1="X" u2="&#xfa;" k="20" />
    <hkern u1="X" u2="&#xf9;" k="20" />
    <hkern u1="X" u2="&#xf6;" k="20" />
    <hkern u1="X" u2="&#xf5;" k="20" />
    <hkern u1="X" u2="&#xf4;" k="20" />
    <hkern u1="X" u2="&#xf3;" k="20" />
    <hkern u1="X" u2="&#xf2;" k="20" />
    <hkern u1="X" u2="&#xf0;" k="20" />
    <hkern u1="X" u2="&#xeb;" k="20" />
    <hkern u1="X" u2="&#xea;" k="20" />
    <hkern u1="X" u2="&#xe9;" k="20" />
    <hkern u1="X" u2="&#xe8;" k="20" />
    <hkern u1="X" u2="&#xe7;" k="20" />
    <hkern u1="X" u2="&#xd8;" k="30" />
    <hkern u1="X" u2="&#xd6;" k="30" />
    <hkern u1="X" u2="&#xd5;" k="30" />
    <hkern u1="X" u2="&#xd4;" k="30" />
    <hkern u1="X" u2="&#xd3;" k="30" />
    <hkern u1="X" u2="&#xd2;" k="30" />
    <hkern u1="X" u2="&#xc7;" k="30" />
    <hkern u1="X" u2="&#xbf;" k="20" />
    <hkern u1="X" u2="y" k="35" />
    <hkern u1="X" u2="w" k="30" />
    <hkern u1="X" u2="v" k="30" />
    <hkern u1="X" u2="u" k="20" />
    <hkern u1="X" u2="t" k="30" />
    <hkern u1="X" u2="q" k="20" />
    <hkern u1="X" u2="o" k="20" />
    <hkern u1="X" u2="g" k="20" />
    <hkern u1="X" u2="e" k="20" />
    <hkern u1="X" u2="d" k="20" />
    <hkern u1="X" u2="c" k="20" />
    <hkern u1="X" u2="Q" k="30" />
    <hkern u1="X" u2="O" k="30" />
    <hkern u1="X" u2="G" k="30" />
    <hkern u1="X" u2="C" k="30" />
    <hkern u1="X" u2="&#x2d;" k="8" />
    <hkern u1="Y" u2="&#x2039;" k="57" />
    <hkern u1="Y" u2="&#x2026;" k="39" />
    <hkern u1="Y" u2="&#x2014;" k="37" />
    <hkern u1="Y" u2="&#x2013;" k="37" />
    <hkern u1="Y" u2="&#x2dd;" k="95" />
    <hkern u1="Y" u2="&#x219;" k="80" />
    <hkern u1="Y" u2="&#x218;" k="15" />
    <hkern u1="Y" u2="&#x17e;" k="61" />
    <hkern u1="Y" u2="&#x17c;" k="61" />
    <hkern u1="Y" u2="&#x17a;" k="61" />
    <hkern u1="Y" u2="&#x175;" k="24" />
    <hkern u1="Y" u2="&#x173;" k="66" />
    <hkern u1="Y" u2="&#x171;" k="66" />
    <hkern u1="Y" u2="&#x16f;" k="66" />
    <hkern u1="Y" u2="&#x16d;" k="66" />
    <hkern u1="Y" u2="&#x16b;" k="66" />
    <hkern u1="Y" u2="&#x169;" k="66" />
    <hkern u1="Y" u2="&#x167;" k="33" />
    <hkern u1="Y" u2="&#x165;" k="33" />
    <hkern u1="Y" u2="&#x163;" k="33" />
    <hkern u1="Y" u2="&#x161;" k="80" />
    <hkern u1="Y" u2="&#x160;" k="15" />
    <hkern u1="Y" u2="&#x15f;" k="80" />
    <hkern u1="Y" u2="&#x15e;" k="15" />
    <hkern u1="Y" u2="&#x15d;" k="80" />
    <hkern u1="Y" u2="&#x15c;" k="15" />
    <hkern u1="Y" u2="&#x15b;" k="80" />
    <hkern u1="Y" u2="&#x15a;" k="15" />
    <hkern u1="Y" u2="&#x159;" k="75" />
    <hkern u1="Y" u2="&#x157;" k="75" />
    <hkern u1="Y" u2="&#x155;" k="75" />
    <hkern u1="Y" u2="&#x153;" k="95" />
    <hkern u1="Y" u2="&#x152;" k="50" />
    <hkern u1="Y" u2="&#x151;" k="95" />
    <hkern u1="Y" u2="&#x150;" k="50" />
    <hkern u1="Y" u2="&#x14f;" k="95" />
    <hkern u1="Y" u2="&#x14e;" k="50" />
    <hkern u1="Y" u2="&#x14d;" k="95" />
    <hkern u1="Y" u2="&#x14c;" k="50" />
    <hkern u1="Y" u2="&#x14b;" k="75" />
    <hkern u1="Y" u2="&#x148;" k="75" />
    <hkern u1="Y" u2="&#x146;" k="75" />
    <hkern u1="Y" u2="&#x144;" k="75" />
    <hkern u1="Y" u2="&#x134;" k="115" />
    <hkern u1="Y" u2="&#x123;" k="95" />
    <hkern u1="Y" u2="&#x122;" k="50" />
    <hkern u1="Y" u2="&#x121;" k="95" />
    <hkern u1="Y" u2="&#x120;" k="50" />
    <hkern u1="Y" u2="&#x11f;" k="95" />
    <hkern u1="Y" u2="&#x11e;" k="50" />
    <hkern u1="Y" u2="&#x11d;" k="95" />
    <hkern u1="Y" u2="&#x11c;" k="50" />
    <hkern u1="Y" u2="&#x11b;" k="95" />
    <hkern u1="Y" u2="&#x119;" k="95" />
    <hkern u1="Y" u2="&#x117;" k="95" />
    <hkern u1="Y" u2="&#x115;" k="95" />
    <hkern u1="Y" u2="&#x113;" k="95" />
    <hkern u1="Y" u2="&#x111;" k="95" />
    <hkern u1="Y" u2="&#x10f;" k="95" />
    <hkern u1="Y" u2="&#x10d;" k="95" />
    <hkern u1="Y" u2="&#x10c;" k="50" />
    <hkern u1="Y" u2="&#x10b;" k="95" />
    <hkern u1="Y" u2="&#x10a;" k="50" />
    <hkern u1="Y" u2="&#x109;" k="95" />
    <hkern u1="Y" u2="&#x108;" k="50" />
    <hkern u1="Y" u2="&#x107;" k="95" />
    <hkern u1="Y" u2="&#x106;" k="50" />
    <hkern u1="Y" u2="&#x105;" k="80" />
    <hkern u1="Y" u2="&#x104;" k="95" />
    <hkern u1="Y" u2="&#x103;" k="80" />
    <hkern u1="Y" u2="&#x102;" k="95" />
    <hkern u1="Y" u2="&#x101;" k="80" />
    <hkern u1="Y" u2="&#x100;" k="95" />
    <hkern u1="Y" u2="&#xfc;" k="66" />
    <hkern u1="Y" u2="&#xfb;" k="66" />
    <hkern u1="Y" u2="&#xfa;" k="66" />
    <hkern u1="Y" u2="&#xf9;" k="66" />
    <hkern u1="Y" u2="&#xf8;" k="95" />
    <hkern u1="Y" u2="&#xf6;" k="95" />
    <hkern u1="Y" u2="&#xf5;" k="95" />
    <hkern u1="Y" u2="&#xf4;" k="95" />
    <hkern u1="Y" u2="&#xf3;" k="95" />
    <hkern u1="Y" u2="&#xf2;" k="95" />
    <hkern u1="Y" u2="&#xf1;" k="75" />
    <hkern u1="Y" u2="&#xf0;" k="95" />
    <hkern u1="Y" u2="&#xeb;" k="95" />
    <hkern u1="Y" u2="&#xea;" k="95" />
    <hkern u1="Y" u2="&#xe9;" k="95" />
    <hkern u1="Y" u2="&#xe8;" k="95" />
    <hkern u1="Y" u2="&#xe7;" k="95" />
    <hkern u1="Y" u2="&#xe6;" k="80" />
    <hkern u1="Y" u2="&#xe5;" k="80" />
    <hkern u1="Y" u2="&#xe4;" k="80" />
    <hkern u1="Y" u2="&#xe3;" k="80" />
    <hkern u1="Y" u2="&#xe2;" k="80" />
    <hkern u1="Y" u2="&#xe1;" k="80" />
    <hkern u1="Y" u2="&#xe0;" k="80" />
    <hkern u1="Y" u2="&#xd8;" k="50" />
    <hkern u1="Y" u2="&#xd6;" k="50" />
    <hkern u1="Y" u2="&#xd5;" k="50" />
    <hkern u1="Y" u2="&#xd4;" k="50" />
    <hkern u1="Y" u2="&#xd3;" k="50" />
    <hkern u1="Y" u2="&#xd2;" k="50" />
    <hkern u1="Y" u2="&#xc7;" k="50" />
    <hkern u1="Y" u2="&#xc6;" k="110" />
    <hkern u1="Y" u2="&#xc5;" k="95" />
    <hkern u1="Y" u2="&#xc4;" k="95" />
    <hkern u1="Y" u2="&#xc3;" k="95" />
    <hkern u1="Y" u2="&#xc2;" k="95" />
    <hkern u1="Y" u2="&#xc1;" k="95" />
    <hkern u1="Y" u2="&#xc0;" k="95" />
    <hkern u1="Y" u2="&#xbf;" k="90" />
    <hkern u1="Y" u2="&#xbb;" k="50" />
    <hkern u1="Y" u2="&#xab;" k="57" />
    <hkern u1="Y" u2="z" k="61" />
    <hkern u1="Y" u2="x" k="33" />
    <hkern u1="Y" u2="w" k="24" />
    <hkern u1="Y" u2="v" k="28" />
    <hkern u1="Y" u2="u" k="66" />
    <hkern u1="Y" u2="t" k="33" />
    <hkern u1="Y" u2="s" k="80" />
    <hkern u1="Y" u2="r" k="75" />
    <hkern u1="Y" u2="q" k="95" />
    <hkern u1="Y" u2="p" k="75" />
    <hkern u1="Y" u2="o" k="95" />
    <hkern u1="Y" u2="n" k="75" />
    <hkern u1="Y" u2="m" k="75" />
    <hkern u1="Y" u2="g" k="80" />
    <hkern u1="Y" u2="f" k="38" />
    <hkern u1="Y" u2="e" k="95" />
    <hkern u1="Y" u2="d" k="95" />
    <hkern u1="Y" u2="c" k="95" />
    <hkern u1="Y" u2="a" k="80" />
    <hkern u1="Y" u2="S" k="15" />
    <hkern u1="Y" u2="Q" k="50" />
    <hkern u1="Y" u2="O" k="50" />
    <hkern u1="Y" u2="J" k="115" />
    <hkern u1="Y" u2="G" k="50" />
    <hkern u1="Y" u2="C" k="50" />
    <hkern u1="Y" u2="A" k="95" />
    <hkern u1="Y" u2="&#x40;" k="22" />
    <hkern u1="Y" u2="&#x3f;" k="-22" />
    <hkern u1="Y" u2="&#x3b;" k="31" />
    <hkern u1="Y" u2="&#x3a;" k="31" />
    <hkern u1="Y" u2="&#x2f;" k="61" />
    <hkern u1="Y" u2="&#x2e;" k="39" />
    <hkern u1="Y" u2="&#x2d;" k="37" />
    <hkern u1="Y" u2="&#x2c;" k="39" />
    <hkern u1="Y" u2="&#x26;" k="71" />
    <hkern u1="Z" u2="&#x177;" k="30" />
    <hkern u1="Z" u2="&#x175;" k="20" />
    <hkern u1="Z" u2="&#x173;" k="20" />
    <hkern u1="Z" u2="&#x171;" k="20" />
    <hkern u1="Z" u2="&#x16f;" k="20" />
    <hkern u1="Z" u2="&#x16d;" k="20" />
    <hkern u1="Z" u2="&#x16b;" k="20" />
    <hkern u1="Z" u2="&#x169;" k="20" />
    <hkern u1="Z" u2="&#x167;" k="20" />
    <hkern u1="Z" u2="&#x165;" k="20" />
    <hkern u1="Z" u2="&#x163;" k="20" />
    <hkern u1="Z" u2="&#x153;" k="25" />
    <hkern u1="Z" u2="&#x152;" k="25" />
    <hkern u1="Z" u2="&#x151;" k="25" />
    <hkern u1="Z" u2="&#x150;" k="25" />
    <hkern u1="Z" u2="&#x14f;" k="25" />
    <hkern u1="Z" u2="&#x14e;" k="25" />
    <hkern u1="Z" u2="&#x14d;" k="25" />
    <hkern u1="Z" u2="&#x14c;" k="25" />
    <hkern u1="Z" u2="&#x123;" k="25" />
    <hkern u1="Z" u2="&#x122;" k="25" />
    <hkern u1="Z" u2="&#x121;" k="25" />
    <hkern u1="Z" u2="&#x120;" k="25" />
    <hkern u1="Z" u2="&#x11f;" k="25" />
    <hkern u1="Z" u2="&#x11e;" k="25" />
    <hkern u1="Z" u2="&#x11d;" k="25" />
    <hkern u1="Z" u2="&#x11c;" k="25" />
    <hkern u1="Z" u2="&#x11b;" k="25" />
    <hkern u1="Z" u2="&#x119;" k="25" />
    <hkern u1="Z" u2="&#x117;" k="25" />
    <hkern u1="Z" u2="&#x115;" k="25" />
    <hkern u1="Z" u2="&#x113;" k="25" />
    <hkern u1="Z" u2="&#x111;" k="25" />
    <hkern u1="Z" u2="&#x10f;" k="25" />
    <hkern u1="Z" u2="&#x10d;" k="25" />
    <hkern u1="Z" u2="&#x10c;" k="25" />
    <hkern u1="Z" u2="&#x10b;" k="25" />
    <hkern u1="Z" u2="&#x10a;" k="25" />
    <hkern u1="Z" u2="&#x109;" k="25" />
    <hkern u1="Z" u2="&#x108;" k="25" />
    <hkern u1="Z" u2="&#x107;" k="25" />
    <hkern u1="Z" u2="&#x106;" k="25" />
    <hkern u1="Z" u2="&#xff;" k="30" />
    <hkern u1="Z" u2="&#xfd;" k="30" />
    <hkern u1="Z" u2="&#xfc;" k="20" />
    <hkern u1="Z" u2="&#xfb;" k="20" />
    <hkern u1="Z" u2="&#xfa;" k="20" />
    <hkern u1="Z" u2="&#xf9;" k="20" />
    <hkern u1="Z" u2="&#xf6;" k="25" />
    <hkern u1="Z" u2="&#xf5;" k="25" />
    <hkern u1="Z" u2="&#xf4;" k="25" />
    <hkern u1="Z" u2="&#xf3;" k="25" />
    <hkern u1="Z" u2="&#xf2;" k="25" />
    <hkern u1="Z" u2="&#xf0;" k="25" />
    <hkern u1="Z" u2="&#xeb;" k="25" />
    <hkern u1="Z" u2="&#xea;" k="25" />
    <hkern u1="Z" u2="&#xe9;" k="25" />
    <hkern u1="Z" u2="&#xe8;" k="25" />
    <hkern u1="Z" u2="&#xe7;" k="25" />
    <hkern u1="Z" u2="&#xd6;" k="25" />
    <hkern u1="Z" u2="&#xd5;" k="25" />
    <hkern u1="Z" u2="&#xd4;" k="25" />
    <hkern u1="Z" u2="&#xd3;" k="25" />
    <hkern u1="Z" u2="&#xd2;" k="25" />
    <hkern u1="Z" u2="&#xc7;" k="25" />
    <hkern u1="Z" u2="y" k="30" />
    <hkern u1="Z" u2="w" k="20" />
    <hkern u1="Z" u2="v" k="25" />
    <hkern u1="Z" u2="u" k="20" />
    <hkern u1="Z" u2="t" k="20" />
    <hkern u1="Z" u2="q" k="25" />
    <hkern u1="Z" u2="o" k="25" />
    <hkern u1="Z" u2="g" k="25" />
    <hkern u1="Z" u2="e" k="25" />
    <hkern u1="Z" u2="d" k="25" />
    <hkern u1="Z" u2="c" k="25" />
    <hkern u1="Z" u2="Q" k="25" />
    <hkern u1="Z" u2="O" k="25" />
    <hkern u1="Z" u2="G" k="25" />
    <hkern u1="Z" u2="C" k="25" />
    <hkern u1="[" u2="&#x135;" k="-35" />
    <hkern u1="[" u2="j" k="-35" />
    <hkern u1="\" u2="&#x26;" k="56" />
    <hkern u1="a" u2="&#x201c;" k="70" />
    <hkern u1="a" u2="&#x2018;" k="70" />
    <hkern u1="a" u2="&#x177;" k="20" />
    <hkern u1="a" u2="&#xff;" k="20" />
    <hkern u1="a" u2="&#xfd;" k="20" />
    <hkern u1="a" u2="y" k="20" />
    <hkern u1="a" u2="v" k="15" />
    <hkern u1="a" u2="\" k="65" />
    <hkern u1="b" u2="&#x201c;" k="75" />
    <hkern u1="b" u2="&#x2018;" k="75" />
    <hkern u1="b" u2="&#x17e;" k="20" />
    <hkern u1="b" u2="&#x17c;" k="20" />
    <hkern u1="b" u2="&#x17a;" k="20" />
    <hkern u1="b" u2="&#x177;" k="15" />
    <hkern u1="b" u2="&#xff;" k="15" />
    <hkern u1="b" u2="&#xfd;" k="15" />
    <hkern u1="b" u2="z" k="20" />
    <hkern u1="b" u2="y" k="15" />
    <hkern u1="b" u2="x" k="20" />
    <hkern u1="b" u2="v" k="10" />
    <hkern u1="b" u2="\" k="70" />
    <hkern u1="c" u2="&#x201c;" k="42" />
    <hkern u1="c" u2="&#x2018;" k="42" />
    <hkern u1="c" u2="&#x153;" k="6" />
    <hkern u1="c" u2="&#x151;" k="6" />
    <hkern u1="c" u2="&#x14f;" k="6" />
    <hkern u1="c" u2="&#x14d;" k="6" />
    <hkern u1="c" u2="&#x123;" k="6" />
    <hkern u1="c" u2="&#x121;" k="6" />
    <hkern u1="c" u2="&#x11f;" k="6" />
    <hkern u1="c" u2="&#x11d;" k="6" />
    <hkern u1="c" u2="&#x11b;" k="6" />
    <hkern u1="c" u2="&#x119;" k="6" />
    <hkern u1="c" u2="&#x117;" k="6" />
    <hkern u1="c" u2="&#x115;" k="6" />
    <hkern u1="c" u2="&#x113;" k="6" />
    <hkern u1="c" u2="&#x111;" k="6" />
    <hkern u1="c" u2="&#x10f;" k="6" />
    <hkern u1="c" u2="&#x10d;" k="6" />
    <hkern u1="c" u2="&#x10b;" k="6" />
    <hkern u1="c" u2="&#x109;" k="6" />
    <hkern u1="c" u2="&#x107;" k="6" />
    <hkern u1="c" u2="&#x105;" k="-6" />
    <hkern u1="c" u2="&#x103;" k="-6" />
    <hkern u1="c" u2="&#x101;" k="-6" />
    <hkern u1="c" u2="&#xf6;" k="6" />
    <hkern u1="c" u2="&#xf5;" k="6" />
    <hkern u1="c" u2="&#xf4;" k="6" />
    <hkern u1="c" u2="&#xf3;" k="6" />
    <hkern u1="c" u2="&#xf2;" k="6" />
    <hkern u1="c" u2="&#xf0;" k="6" />
    <hkern u1="c" u2="&#xeb;" k="6" />
    <hkern u1="c" u2="&#xea;" k="6" />
    <hkern u1="c" u2="&#xe9;" k="6" />
    <hkern u1="c" u2="&#xe8;" k="6" />
    <hkern u1="c" u2="&#xe7;" k="6" />
    <hkern u1="c" u2="&#xe6;" k="-6" />
    <hkern u1="c" u2="&#xe5;" k="-6" />
    <hkern u1="c" u2="&#xe4;" k="-6" />
    <hkern u1="c" u2="&#xe3;" k="-6" />
    <hkern u1="c" u2="&#xe2;" k="-6" />
    <hkern u1="c" u2="&#xe1;" k="-6" />
    <hkern u1="c" u2="&#xe0;" k="-6" />
    <hkern u1="c" u2="q" k="6" />
    <hkern u1="c" u2="o" k="6" />
    <hkern u1="c" u2="g" k="6" />
    <hkern u1="c" u2="e" k="6" />
    <hkern u1="c" u2="d" k="6" />
    <hkern u1="c" u2="c" k="6" />
    <hkern u1="c" u2="a" k="-6" />
    <hkern u1="c" u2="\" k="61" />
    <hkern u1="e" u2="&#x177;" k="10" />
    <hkern u1="e" u2="&#xff;" k="10" />
    <hkern u1="e" u2="&#xfd;" k="10" />
    <hkern u1="e" u2="y" k="10" />
    <hkern u1="e" u2="x" k="7" />
    <hkern u1="e" u2="v" k="5" />
    <hkern u1="e" u2="\" k="61" />
    <hkern u1="f" u2="&#x201d;" k="-10" />
    <hkern u1="f" u2="&#x201c;" k="-42" />
    <hkern u1="f" u2="&#x2019;" k="-10" />
    <hkern u1="f" u2="&#x2018;" k="-42" />
    <hkern u1="f" u2="&#x2dd;" k="15" />
    <hkern u1="f" u2="&#x219;" k="5" />
    <hkern u1="f" u2="&#x161;" k="5" />
    <hkern u1="f" u2="&#x15f;" k="5" />
    <hkern u1="f" u2="&#x15d;" k="5" />
    <hkern u1="f" u2="&#x15b;" k="5" />
    <hkern u1="f" u2="&#x153;" k="15" />
    <hkern u1="f" u2="&#x151;" k="15" />
    <hkern u1="f" u2="&#x14f;" k="15" />
    <hkern u1="f" u2="&#x14d;" k="15" />
    <hkern u1="f" u2="&#x123;" k="15" />
    <hkern u1="f" u2="&#x121;" k="15" />
    <hkern u1="f" u2="&#x11f;" k="15" />
    <hkern u1="f" u2="&#x11d;" k="15" />
    <hkern u1="f" u2="&#x11b;" k="15" />
    <hkern u1="f" u2="&#x119;" k="15" />
    <hkern u1="f" u2="&#x117;" k="15" />
    <hkern u1="f" u2="&#x115;" k="15" />
    <hkern u1="f" u2="&#x113;" k="15" />
    <hkern u1="f" u2="&#x111;" k="15" />
    <hkern u1="f" u2="&#x10f;" k="15" />
    <hkern u1="f" u2="&#x10d;" k="15" />
    <hkern u1="f" u2="&#x10b;" k="15" />
    <hkern u1="f" u2="&#x109;" k="15" />
    <hkern u1="f" u2="&#x107;" k="15" />
    <hkern u1="f" u2="&#x105;" k="10" />
    <hkern u1="f" u2="&#x103;" k="10" />
    <hkern u1="f" u2="&#x101;" k="10" />
    <hkern u1="f" u2="&#xf8;" k="15" />
    <hkern u1="f" u2="&#xf6;" k="15" />
    <hkern u1="f" u2="&#xf5;" k="15" />
    <hkern u1="f" u2="&#xf4;" k="15" />
    <hkern u1="f" u2="&#xf3;" k="15" />
    <hkern u1="f" u2="&#xf2;" k="15" />
    <hkern u1="f" u2="&#xf0;" k="15" />
    <hkern u1="f" u2="&#xeb;" k="15" />
    <hkern u1="f" u2="&#xea;" k="15" />
    <hkern u1="f" u2="&#xe9;" k="15" />
    <hkern u1="f" u2="&#xe8;" k="15" />
    <hkern u1="f" u2="&#xe7;" k="15" />
    <hkern u1="f" u2="&#xe6;" k="10" />
    <hkern u1="f" u2="&#xe5;" k="10" />
    <hkern u1="f" u2="&#xe4;" k="10" />
    <hkern u1="f" u2="&#xe3;" k="10" />
    <hkern u1="f" u2="&#xe2;" k="10" />
    <hkern u1="f" u2="&#xe1;" k="10" />
    <hkern u1="f" u2="&#xe0;" k="10" />
    <hkern u1="f" u2="&#xbf;" k="20" />
    <hkern u1="f" u2="&#x7d;" k="-102" />
    <hkern u1="f" u2="s" k="5" />
    <hkern u1="f" u2="q" k="15" />
    <hkern u1="f" u2="o" k="15" />
    <hkern u1="f" u2="g" k="15" />
    <hkern u1="f" u2="e" k="15" />
    <hkern u1="f" u2="d" k="15" />
    <hkern u1="f" u2="c" k="15" />
    <hkern u1="f" u2="a" k="10" />
    <hkern u1="f" u2="]" k="-102" />
    <hkern u1="f" u2="\" k="-47" />
    <hkern u1="f" u2="&#x3f;" k="-55" />
    <hkern u1="f" u2="&#x2a;" k="-37" />
    <hkern u1="f" u2="&#x29;" k="-102" />
    <hkern u1="g" u2="&#x201c;" k="33" />
    <hkern u1="g" u2="&#x2018;" k="33" />
    <hkern u1="g" u2="\" k="66" />
    <hkern u1="h" u2="&#x201c;" k="60" />
    <hkern u1="h" u2="&#x2018;" k="60" />
    <hkern u1="h" u2="&#x177;" k="20" />
    <hkern u1="h" u2="&#x175;" k="10" />
    <hkern u1="h" u2="&#xff;" k="20" />
    <hkern u1="h" u2="&#xfd;" k="20" />
    <hkern u1="h" u2="y" k="20" />
    <hkern u1="h" u2="w" k="10" />
    <hkern u1="h" u2="v" k="20" />
    <hkern u1="h" u2="\" k="70" />
    <hkern u1="k" u2="&#x2c7;" k="25" />
    <hkern u1="k" u2="&#x173;" k="10" />
    <hkern u1="k" u2="&#x171;" k="10" />
    <hkern u1="k" u2="&#x16f;" k="10" />
    <hkern u1="k" u2="&#x16d;" k="10" />
    <hkern u1="k" u2="&#x16b;" k="10" />
    <hkern u1="k" u2="&#x169;" k="10" />
    <hkern u1="k" u2="&#x153;" k="12" />
    <hkern u1="k" u2="&#x151;" k="12" />
    <hkern u1="k" u2="&#x14f;" k="12" />
    <hkern u1="k" u2="&#x14d;" k="12" />
    <hkern u1="k" u2="&#x140;" k="10" />
    <hkern u1="k" u2="&#x13e;" k="10" />
    <hkern u1="k" u2="&#x13c;" k="10" />
    <hkern u1="k" u2="&#x13a;" k="10" />
    <hkern u1="k" u2="&#x123;" k="12" />
    <hkern u1="k" u2="&#x121;" k="12" />
    <hkern u1="k" u2="&#x11f;" k="12" />
    <hkern u1="k" u2="&#x11d;" k="12" />
    <hkern u1="k" u2="&#x11b;" k="12" />
    <hkern u1="k" u2="&#x119;" k="12" />
    <hkern u1="k" u2="&#x117;" k="12" />
    <hkern u1="k" u2="&#x115;" k="12" />
    <hkern u1="k" u2="&#x113;" k="12" />
    <hkern u1="k" u2="&#x111;" k="12" />
    <hkern u1="k" u2="&#x10f;" k="12" />
    <hkern u1="k" u2="&#x10d;" k="12" />
    <hkern u1="k" u2="&#x10b;" k="12" />
    <hkern u1="k" u2="&#x109;" k="12" />
    <hkern u1="k" u2="&#x107;" k="12" />
    <hkern u1="k" u2="&#xfc;" k="10" />
    <hkern u1="k" u2="&#xfb;" k="10" />
    <hkern u1="k" u2="&#xfa;" k="10" />
    <hkern u1="k" u2="&#xf9;" k="10" />
    <hkern u1="k" u2="&#xf8;" k="12" />
    <hkern u1="k" u2="&#xf6;" k="12" />
    <hkern u1="k" u2="&#xf5;" k="12" />
    <hkern u1="k" u2="&#xf4;" k="12" />
    <hkern u1="k" u2="&#xf3;" k="12" />
    <hkern u1="k" u2="&#xf2;" k="12" />
    <hkern u1="k" u2="&#xf0;" k="12" />
    <hkern u1="k" u2="&#xeb;" k="12" />
    <hkern u1="k" u2="&#xea;" k="12" />
    <hkern u1="k" u2="&#xe9;" k="12" />
    <hkern u1="k" u2="&#xe8;" k="12" />
    <hkern u1="k" u2="&#xe7;" k="12" />
    <hkern u1="k" u2="&#xe6;" k="2" />
    <hkern u1="k" u2="&#xbf;" k="25" />
    <hkern u1="k" u2="u" k="10" />
    <hkern u1="k" u2="q" k="12" />
    <hkern u1="k" u2="o" k="12" />
    <hkern u1="k" u2="l" k="10" />
    <hkern u1="k" u2="g" k="12" />
    <hkern u1="k" u2="e" k="12" />
    <hkern u1="k" u2="d" k="12" />
    <hkern u1="k" u2="c" k="12" />
    <hkern u1="k" u2="a" k="2" />
    <hkern u1="k" u2="\" k="51" />
    <hkern u1="k" u2="&#x40;" k="4" />
    <hkern u1="l" u2="&#x177;" k="35" />
    <hkern u1="l" u2="&#x175;" k="23" />
    <hkern u1="l" u2="&#x173;" k="15" />
    <hkern u1="l" u2="&#x171;" k="15" />
    <hkern u1="l" u2="&#x16f;" k="15" />
    <hkern u1="l" u2="&#x16d;" k="15" />
    <hkern u1="l" u2="&#x16b;" k="15" />
    <hkern u1="l" u2="&#x169;" k="15" />
    <hkern u1="l" u2="&#x167;" k="25" />
    <hkern u1="l" u2="&#x165;" k="25" />
    <hkern u1="l" u2="&#x163;" k="25" />
    <hkern u1="l" u2="&#x153;" k="10" />
    <hkern u1="l" u2="&#x151;" k="10" />
    <hkern u1="l" u2="&#x14f;" k="10" />
    <hkern u1="l" u2="&#x14d;" k="10" />
    <hkern u1="l" u2="&#x142;" k="10" />
    <hkern u1="l" u2="&#x140;" k="10" />
    <hkern u1="l" u2="&#x13e;" k="10" />
    <hkern u1="l" u2="&#x13c;" k="10" />
    <hkern u1="l" u2="&#x13a;" k="10" />
    <hkern u1="l" u2="&#x123;" k="10" />
    <hkern u1="l" u2="&#x121;" k="10" />
    <hkern u1="l" u2="&#x11f;" k="10" />
    <hkern u1="l" u2="&#x11d;" k="10" />
    <hkern u1="l" u2="&#x11b;" k="10" />
    <hkern u1="l" u2="&#x119;" k="10" />
    <hkern u1="l" u2="&#x117;" k="10" />
    <hkern u1="l" u2="&#x115;" k="10" />
    <hkern u1="l" u2="&#x113;" k="10" />
    <hkern u1="l" u2="&#x111;" k="10" />
    <hkern u1="l" u2="&#x10f;" k="10" />
    <hkern u1="l" u2="&#x10d;" k="10" />
    <hkern u1="l" u2="&#x10b;" k="10" />
    <hkern u1="l" u2="&#x109;" k="10" />
    <hkern u1="l" u2="&#x107;" k="10" />
    <hkern u1="l" u2="&#xff;" k="35" />
    <hkern u1="l" u2="&#xfd;" k="35" />
    <hkern u1="l" u2="&#xfc;" k="15" />
    <hkern u1="l" u2="&#xfb;" k="15" />
    <hkern u1="l" u2="&#xfa;" k="15" />
    <hkern u1="l" u2="&#xf9;" k="15" />
    <hkern u1="l" u2="&#xf6;" k="10" />
    <hkern u1="l" u2="&#xf5;" k="10" />
    <hkern u1="l" u2="&#xf4;" k="10" />
    <hkern u1="l" u2="&#xf3;" k="10" />
    <hkern u1="l" u2="&#xf2;" k="10" />
    <hkern u1="l" u2="&#xf0;" k="10" />
    <hkern u1="l" u2="&#xeb;" k="10" />
    <hkern u1="l" u2="&#xea;" k="10" />
    <hkern u1="l" u2="&#xe9;" k="10" />
    <hkern u1="l" u2="&#xe8;" k="10" />
    <hkern u1="l" u2="&#xe7;" k="10" />
    <hkern u1="l" u2="y" k="35" />
    <hkern u1="l" u2="w" k="23" />
    <hkern u1="l" u2="v" k="32" />
    <hkern u1="l" u2="u" k="15" />
    <hkern u1="l" u2="t" k="25" />
    <hkern u1="l" u2="q" k="10" />
    <hkern u1="l" u2="o" k="10" />
    <hkern u1="l" u2="l" k="10" />
    <hkern u1="l" u2="g" k="10" />
    <hkern u1="l" u2="e" k="10" />
    <hkern u1="l" u2="d" k="10" />
    <hkern u1="l" u2="c" k="10" />
    <hkern u1="m" u2="&#x201c;" k="60" />
    <hkern u1="m" u2="&#x2018;" k="60" />
    <hkern u1="m" u2="&#x177;" k="20" />
    <hkern u1="m" u2="&#x175;" k="10" />
    <hkern u1="m" u2="&#xff;" k="20" />
    <hkern u1="m" u2="&#xfd;" k="20" />
    <hkern u1="m" u2="y" k="20" />
    <hkern u1="m" u2="w" k="10" />
    <hkern u1="m" u2="v" k="20" />
    <hkern u1="m" u2="\" k="70" />
    <hkern u1="n" u2="&#x201c;" k="60" />
    <hkern u1="n" u2="&#x2018;" k="60" />
    <hkern u1="n" u2="&#x177;" k="20" />
    <hkern u1="n" u2="&#x175;" k="10" />
    <hkern u1="n" u2="&#xff;" k="20" />
    <hkern u1="n" u2="&#xfd;" k="20" />
    <hkern u1="n" u2="y" k="20" />
    <hkern u1="n" u2="w" k="10" />
    <hkern u1="n" u2="v" k="20" />
    <hkern u1="n" u2="\" k="70" />
    <hkern u1="o" u2="&#x201c;" k="66" />
    <hkern u1="o" u2="&#x2018;" k="66" />
    <hkern u1="o" u2="&#x17e;" k="25" />
    <hkern u1="o" u2="&#x17c;" k="25" />
    <hkern u1="o" u2="&#x17a;" k="25" />
    <hkern u1="o" u2="&#x177;" k="15" />
    <hkern u1="o" u2="&#x175;" k="2" />
    <hkern u1="o" u2="&#xff;" k="15" />
    <hkern u1="o" u2="&#xfd;" k="15" />
    <hkern u1="o" u2="z" k="25" />
    <hkern u1="o" u2="y" k="5" />
    <hkern u1="o" u2="x" k="20" />
    <hkern u1="o" u2="w" k="2" />
    <hkern u1="o" u2="v" k="10" />
    <hkern u1="o" u2="\" k="94" />
    <hkern u1="p" u2="&#x201c;" k="56" />
    <hkern u1="p" u2="&#x2018;" k="56" />
    <hkern u1="p" u2="&#x17e;" k="20" />
    <hkern u1="p" u2="&#x17c;" k="20" />
    <hkern u1="p" u2="&#x17a;" k="20" />
    <hkern u1="p" u2="&#x177;" k="15" />
    <hkern u1="p" u2="&#xff;" k="15" />
    <hkern u1="p" u2="&#xfd;" k="15" />
    <hkern u1="p" u2="z" k="25" />
    <hkern u1="p" u2="y" k="15" />
    <hkern u1="p" u2="x" k="20" />
    <hkern u1="p" u2="v" k="5" />
    <hkern u1="p" u2="\" k="84" />
    <hkern u1="q" u2="&#x201c;" k="33" />
    <hkern u1="q" u2="&#x2018;" k="33" />
    <hkern u1="q" u2="\" k="66" />
    <hkern u1="r" u2="&#x2026;" k="32" />
    <hkern u1="r" u2="&#x2014;" k="12" />
    <hkern u1="r" u2="&#x2013;" k="12" />
    <hkern u1="r" u2="&#x219;" k="10" />
    <hkern u1="r" u2="&#x177;" k="-15" />
    <hkern u1="r" u2="&#x175;" k="-15" />
    <hkern u1="r" u2="&#x167;" k="-7" />
    <hkern u1="r" u2="&#x165;" k="-7" />
    <hkern u1="r" u2="&#x163;" k="-7" />
    <hkern u1="r" u2="&#x161;" k="10" />
    <hkern u1="r" u2="&#x15f;" k="10" />
    <hkern u1="r" u2="&#x15d;" k="10" />
    <hkern u1="r" u2="&#x15b;" k="10" />
    <hkern u1="r" u2="&#x153;" k="10" />
    <hkern u1="r" u2="&#x151;" k="10" />
    <hkern u1="r" u2="&#x14f;" k="10" />
    <hkern u1="r" u2="&#x14d;" k="10" />
    <hkern u1="r" u2="&#x123;" k="10" />
    <hkern u1="r" u2="&#x121;" k="10" />
    <hkern u1="r" u2="&#x11f;" k="10" />
    <hkern u1="r" u2="&#x11d;" k="10" />
    <hkern u1="r" u2="&#x11b;" k="10" />
    <hkern u1="r" u2="&#x119;" k="10" />
    <hkern u1="r" u2="&#x117;" k="10" />
    <hkern u1="r" u2="&#x115;" k="10" />
    <hkern u1="r" u2="&#x113;" k="10" />
    <hkern u1="r" u2="&#x111;" k="10" />
    <hkern u1="r" u2="&#x10f;" k="10" />
    <hkern u1="r" u2="&#x10d;" k="10" />
    <hkern u1="r" u2="&#x10b;" k="10" />
    <hkern u1="r" u2="&#x109;" k="10" />
    <hkern u1="r" u2="&#x107;" k="10" />
    <hkern u1="r" u2="&#x105;" k="15" />
    <hkern u1="r" u2="&#x103;" k="15" />
    <hkern u1="r" u2="&#x101;" k="15" />
    <hkern u1="r" u2="&#xff;" k="-15" />
    <hkern u1="r" u2="&#xfd;" k="-15" />
    <hkern u1="r" u2="&#xf8;" k="10" />
    <hkern u1="r" u2="&#xf6;" k="10" />
    <hkern u1="r" u2="&#xf5;" k="10" />
    <hkern u1="r" u2="&#xf4;" k="10" />
    <hkern u1="r" u2="&#xf3;" k="10" />
    <hkern u1="r" u2="&#xf2;" k="10" />
    <hkern u1="r" u2="&#xf0;" k="10" />
    <hkern u1="r" u2="&#xeb;" k="10" />
    <hkern u1="r" u2="&#xea;" k="10" />
    <hkern u1="r" u2="&#xe9;" k="10" />
    <hkern u1="r" u2="&#xe8;" k="10" />
    <hkern u1="r" u2="&#xe7;" k="10" />
    <hkern u1="r" u2="&#xe6;" k="10" />
    <hkern u1="r" u2="&#xe5;" k="15" />
    <hkern u1="r" u2="&#xe4;" k="15" />
    <hkern u1="r" u2="&#xe3;" k="15" />
    <hkern u1="r" u2="&#xe2;" k="15" />
    <hkern u1="r" u2="&#xe1;" k="15" />
    <hkern u1="r" u2="&#xe0;" k="15" />
    <hkern u1="r" u2="&#xbf;" k="20" />
    <hkern u1="r" u2="y" k="-7" />
    <hkern u1="r" u2="x" k="-7" />
    <hkern u1="r" u2="w" k="-7" />
    <hkern u1="r" u2="v" k="-7" />
    <hkern u1="r" u2="t" k="-7" />
    <hkern u1="r" u2="s" k="10" />
    <hkern u1="r" u2="q" k="10" />
    <hkern u1="r" u2="o" k="10" />
    <hkern u1="r" u2="g" k="7" />
    <hkern u1="r" u2="f" k="-10" />
    <hkern u1="r" u2="e" k="10" />
    <hkern u1="r" u2="d" k="10" />
    <hkern u1="r" u2="c" k="10" />
    <hkern u1="r" u2="a" k="10" />
    <hkern u1="r" u2="&#x2e;" k="32" />
    <hkern u1="r" u2="&#x2d;" k="12" />
    <hkern u1="r" u2="&#x2c;" k="32" />
    <hkern u1="s" u2="&#x201c;" k="33" />
    <hkern u1="s" u2="&#x2018;" k="33" />
    <hkern u1="s" u2="\" k="56" />
    <hkern u1="t" u2="&#x29;" k="-36" />
    <hkern u1="u" u2="&#x201c;" k="14" />
    <hkern u1="u" u2="&#x2018;" k="14" />
    <hkern u1="u" u2="\" k="56" />
    <hkern u1="v" u2="&#x2026;" k="47" />
    <hkern u1="v" u2="&#x2c7;" k="10" />
    <hkern u1="v" u2="&#x219;" k="2" />
    <hkern u1="v" u2="&#x161;" k="2" />
    <hkern u1="v" u2="&#x15f;" k="2" />
    <hkern u1="v" u2="&#x15d;" k="2" />
    <hkern u1="v" u2="&#x15b;" k="2" />
    <hkern u1="v" u2="&#x153;" k="10" />
    <hkern u1="v" u2="&#x151;" k="10" />
    <hkern u1="v" u2="&#x14f;" k="10" />
    <hkern u1="v" u2="&#x14d;" k="10" />
    <hkern u1="v" u2="&#x123;" k="10" />
    <hkern u1="v" u2="&#x121;" k="10" />
    <hkern u1="v" u2="&#x11f;" k="10" />
    <hkern u1="v" u2="&#x11d;" k="10" />
    <hkern u1="v" u2="&#x11b;" k="10" />
    <hkern u1="v" u2="&#x119;" k="10" />
    <hkern u1="v" u2="&#x117;" k="10" />
    <hkern u1="v" u2="&#x115;" k="10" />
    <hkern u1="v" u2="&#x113;" k="10" />
    <hkern u1="v" u2="&#x111;" k="10" />
    <hkern u1="v" u2="&#x10f;" k="10" />
    <hkern u1="v" u2="&#x10d;" k="10" />
    <hkern u1="v" u2="&#x10b;" k="10" />
    <hkern u1="v" u2="&#x109;" k="10" />
    <hkern u1="v" u2="&#x107;" k="10" />
    <hkern u1="v" u2="&#x105;" k="15" />
    <hkern u1="v" u2="&#x103;" k="15" />
    <hkern u1="v" u2="&#x101;" k="15" />
    <hkern u1="v" u2="&#xf8;" k="10" />
    <hkern u1="v" u2="&#xf6;" k="10" />
    <hkern u1="v" u2="&#xf5;" k="10" />
    <hkern u1="v" u2="&#xf4;" k="10" />
    <hkern u1="v" u2="&#xf3;" k="10" />
    <hkern u1="v" u2="&#xf2;" k="10" />
    <hkern u1="v" u2="&#xf0;" k="10" />
    <hkern u1="v" u2="&#xeb;" k="10" />
    <hkern u1="v" u2="&#xea;" k="10" />
    <hkern u1="v" u2="&#xe9;" k="10" />
    <hkern u1="v" u2="&#xe8;" k="10" />
    <hkern u1="v" u2="&#xe7;" k="10" />
    <hkern u1="v" u2="&#xe6;" k="15" />
    <hkern u1="v" u2="&#xe5;" k="15" />
    <hkern u1="v" u2="&#xe4;" k="15" />
    <hkern u1="v" u2="&#xe3;" k="15" />
    <hkern u1="v" u2="&#xe2;" k="15" />
    <hkern u1="v" u2="&#xe1;" k="15" />
    <hkern u1="v" u2="&#xe0;" k="15" />
    <hkern u1="v" u2="&#xbf;" k="10" />
    <hkern u1="v" u2="s" k="2" />
    <hkern u1="v" u2="q" k="10" />
    <hkern u1="v" u2="o" k="10" />
    <hkern u1="v" u2="g" k="10" />
    <hkern u1="v" u2="e" k="10" />
    <hkern u1="v" u2="d" k="10" />
    <hkern u1="v" u2="c" k="10" />
    <hkern u1="v" u2="a" k="15" />
    <hkern u1="v" u2="&#x2e;" k="47" />
    <hkern u1="v" u2="&#x2c;" k="47" />
    <hkern u1="v" u2="&#x26;" k="20" />
    <hkern u1="w" u2="&#x2026;" k="28" />
    <hkern u1="w" u2="&#x2c7;" k="5" />
    <hkern u1="w" u2="&#x219;" k="5" />
    <hkern u1="w" u2="&#x161;" k="5" />
    <hkern u1="w" u2="&#x15f;" k="5" />
    <hkern u1="w" u2="&#x15d;" k="5" />
    <hkern u1="w" u2="&#x15b;" k="5" />
    <hkern u1="w" u2="&#x153;" k="2" />
    <hkern u1="w" u2="&#x151;" k="2" />
    <hkern u1="w" u2="&#x14f;" k="2" />
    <hkern u1="w" u2="&#x14d;" k="2" />
    <hkern u1="w" u2="&#x11b;" k="2" />
    <hkern u1="w" u2="&#x119;" k="2" />
    <hkern u1="w" u2="&#x117;" k="2" />
    <hkern u1="w" u2="&#x115;" k="2" />
    <hkern u1="w" u2="&#x113;" k="2" />
    <hkern u1="w" u2="&#x105;" k="10" />
    <hkern u1="w" u2="&#x103;" k="10" />
    <hkern u1="w" u2="&#x101;" k="10" />
    <hkern u1="w" u2="&#xf8;" k="2" />
    <hkern u1="w" u2="&#xf6;" k="2" />
    <hkern u1="w" u2="&#xf5;" k="2" />
    <hkern u1="w" u2="&#xf4;" k="2" />
    <hkern u1="w" u2="&#xf3;" k="2" />
    <hkern u1="w" u2="&#xf2;" k="2" />
    <hkern u1="w" u2="&#xf0;" k="2" />
    <hkern u1="w" u2="&#xeb;" k="2" />
    <hkern u1="w" u2="&#xea;" k="2" />
    <hkern u1="w" u2="&#xe9;" k="2" />
    <hkern u1="w" u2="&#xe8;" k="2" />
    <hkern u1="w" u2="&#xe6;" k="10" />
    <hkern u1="w" u2="&#xe5;" k="10" />
    <hkern u1="w" u2="&#xe4;" k="10" />
    <hkern u1="w" u2="&#xe3;" k="10" />
    <hkern u1="w" u2="&#xe2;" k="10" />
    <hkern u1="w" u2="&#xe1;" k="10" />
    <hkern u1="w" u2="&#xe0;" k="10" />
    <hkern u1="w" u2="&#xbf;" k="5" />
    <hkern u1="w" u2="s" k="5" />
    <hkern u1="w" u2="o" k="2" />
    <hkern u1="w" u2="e" k="2" />
    <hkern u1="w" u2="a" k="10" />
    <hkern u1="w" u2="&#x2e;" k="28" />
    <hkern u1="w" u2="&#x2c;" k="28" />
    <hkern u1="w" u2="&#x26;" k="20" />
    <hkern u1="x" u2="&#x2c7;" k="15" />
    <hkern u1="x" u2="&#x173;" k="10" />
    <hkern u1="x" u2="&#x171;" k="10" />
    <hkern u1="x" u2="&#x16f;" k="10" />
    <hkern u1="x" u2="&#x16d;" k="10" />
    <hkern u1="x" u2="&#x16b;" k="10" />
    <hkern u1="x" u2="&#x169;" k="10" />
    <hkern u1="x" u2="&#x153;" k="15" />
    <hkern u1="x" u2="&#x151;" k="15" />
    <hkern u1="x" u2="&#x14f;" k="15" />
    <hkern u1="x" u2="&#x14d;" k="15" />
    <hkern u1="x" u2="&#x123;" k="15" />
    <hkern u1="x" u2="&#x121;" k="15" />
    <hkern u1="x" u2="&#x11f;" k="15" />
    <hkern u1="x" u2="&#x11d;" k="15" />
    <hkern u1="x" u2="&#x11b;" k="15" />
    <hkern u1="x" u2="&#x119;" k="15" />
    <hkern u1="x" u2="&#x117;" k="15" />
    <hkern u1="x" u2="&#x115;" k="15" />
    <hkern u1="x" u2="&#x113;" k="15" />
    <hkern u1="x" u2="&#x111;" k="15" />
    <hkern u1="x" u2="&#x10f;" k="15" />
    <hkern u1="x" u2="&#x10d;" k="15" />
    <hkern u1="x" u2="&#x10b;" k="15" />
    <hkern u1="x" u2="&#x109;" k="15" />
    <hkern u1="x" u2="&#x107;" k="15" />
    <hkern u1="x" u2="&#x105;" k="3" />
    <hkern u1="x" u2="&#x103;" k="3" />
    <hkern u1="x" u2="&#x101;" k="3" />
    <hkern u1="x" u2="&#xfc;" k="10" />
    <hkern u1="x" u2="&#xfb;" k="10" />
    <hkern u1="x" u2="&#xfa;" k="10" />
    <hkern u1="x" u2="&#xf9;" k="10" />
    <hkern u1="x" u2="&#xf8;" k="15" />
    <hkern u1="x" u2="&#xf6;" k="15" />
    <hkern u1="x" u2="&#xf5;" k="15" />
    <hkern u1="x" u2="&#xf4;" k="15" />
    <hkern u1="x" u2="&#xf3;" k="15" />
    <hkern u1="x" u2="&#xf2;" k="15" />
    <hkern u1="x" u2="&#xf0;" k="15" />
    <hkern u1="x" u2="&#xeb;" k="15" />
    <hkern u1="x" u2="&#xea;" k="15" />
    <hkern u1="x" u2="&#xe9;" k="15" />
    <hkern u1="x" u2="&#xe8;" k="15" />
    <hkern u1="x" u2="&#xe7;" k="15" />
    <hkern u1="x" u2="&#xe6;" k="3" />
    <hkern u1="x" u2="&#xe5;" k="3" />
    <hkern u1="x" u2="&#xe4;" k="3" />
    <hkern u1="x" u2="&#xe3;" k="3" />
    <hkern u1="x" u2="&#xe2;" k="3" />
    <hkern u1="x" u2="&#xe1;" k="3" />
    <hkern u1="x" u2="&#xe0;" k="3" />
    <hkern u1="x" u2="&#xbf;" k="15" />
    <hkern u1="x" u2="u" k="10" />
    <hkern u1="x" u2="q" k="15" />
    <hkern u1="x" u2="o" k="15" />
    <hkern u1="x" u2="g" k="15" />
    <hkern u1="x" u2="e" k="15" />
    <hkern u1="x" u2="d" k="15" />
    <hkern u1="x" u2="c" k="15" />
    <hkern u1="x" u2="a" k="3" />
    <hkern u1="y" u2="&#x2026;" k="32" />
    <hkern u1="y" u2="&#x2014;" k="15" />
    <hkern u1="y" u2="&#x2013;" k="15" />
    <hkern u1="y" u2="&#x2c7;" k="10" />
    <hkern u1="y" u2="&#x219;" k="6" />
    <hkern u1="y" u2="&#x167;" k="-15" />
    <hkern u1="y" u2="&#x165;" k="-15" />
    <hkern u1="y" u2="&#x163;" k="-15" />
    <hkern u1="y" u2="&#x161;" k="6" />
    <hkern u1="y" u2="&#x15f;" k="6" />
    <hkern u1="y" u2="&#x15d;" k="6" />
    <hkern u1="y" u2="&#x15b;" k="6" />
    <hkern u1="y" u2="&#x153;" k="5" />
    <hkern u1="y" u2="&#x151;" k="5" />
    <hkern u1="y" u2="&#x14f;" k="5" />
    <hkern u1="y" u2="&#x14d;" k="5" />
    <hkern u1="y" u2="&#x123;" k="5" />
    <hkern u1="y" u2="&#x121;" k="5" />
    <hkern u1="y" u2="&#x11f;" k="5" />
    <hkern u1="y" u2="&#x11d;" k="5" />
    <hkern u1="y" u2="&#x11b;" k="5" />
    <hkern u1="y" u2="&#x119;" k="5" />
    <hkern u1="y" u2="&#x117;" k="5" />
    <hkern u1="y" u2="&#x115;" k="5" />
    <hkern u1="y" u2="&#x113;" k="5" />
    <hkern u1="y" u2="&#x111;" k="5" />
    <hkern u1="y" u2="&#x10f;" k="5" />
    <hkern u1="y" u2="&#x10d;" k="5" />
    <hkern u1="y" u2="&#x10b;" k="5" />
    <hkern u1="y" u2="&#x109;" k="5" />
    <hkern u1="y" u2="&#x107;" k="5" />
    <hkern u1="y" u2="&#x105;" k="15" />
    <hkern u1="y" u2="&#x103;" k="15" />
    <hkern u1="y" u2="&#x101;" k="15" />
    <hkern u1="y" u2="&#xf8;" k="5" />
    <hkern u1="y" u2="&#xf6;" k="5" />
    <hkern u1="y" u2="&#xf5;" k="5" />
    <hkern u1="y" u2="&#xf4;" k="5" />
    <hkern u1="y" u2="&#xf3;" k="5" />
    <hkern u1="y" u2="&#xf2;" k="5" />
    <hkern u1="y" u2="&#xf0;" k="5" />
    <hkern u1="y" u2="&#xeb;" k="5" />
    <hkern u1="y" u2="&#xea;" k="5" />
    <hkern u1="y" u2="&#xe9;" k="5" />
    <hkern u1="y" u2="&#xe8;" k="5" />
    <hkern u1="y" u2="&#xe7;" k="5" />
    <hkern u1="y" u2="&#xe6;" k="15" />
    <hkern u1="y" u2="&#xe5;" k="15" />
    <hkern u1="y" u2="&#xe4;" k="15" />
    <hkern u1="y" u2="&#xe3;" k="15" />
    <hkern u1="y" u2="&#xe2;" k="15" />
    <hkern u1="y" u2="&#xe1;" k="15" />
    <hkern u1="y" u2="&#xe0;" k="15" />
    <hkern u1="y" u2="&#xbf;" k="10" />
    <hkern u1="y" u2="t" k="-15" />
    <hkern u1="y" u2="s" k="6" />
    <hkern u1="y" u2="q" k="5" />
    <hkern u1="y" u2="o" k="5" />
    <hkern u1="y" u2="g" k="5" />
    <hkern u1="y" u2="e" k="5" />
    <hkern u1="y" u2="d" k="5" />
    <hkern u1="y" u2="c" k="5" />
    <hkern u1="y" u2="a" k="15" />
    <hkern u1="y" u2="&#x2e;" k="32" />
    <hkern u1="y" u2="&#x2d;" k="15" />
    <hkern u1="y" u2="&#x2c;" k="32" />
    <hkern u1="y" u2="&#x26;" k="35" />
    <hkern u1="z" u2="&#x201c;" k="33" />
    <hkern u1="z" u2="&#x2018;" k="33" />
    <hkern u1="z" u2="&#x219;" k="7" />
    <hkern u1="z" u2="&#x173;" k="15" />
    <hkern u1="z" u2="&#x171;" k="15" />
    <hkern u1="z" u2="&#x16f;" k="15" />
    <hkern u1="z" u2="&#x16d;" k="15" />
    <hkern u1="z" u2="&#x16b;" k="15" />
    <hkern u1="z" u2="&#x169;" k="15" />
    <hkern u1="z" u2="&#x161;" k="7" />
    <hkern u1="z" u2="&#x15f;" k="7" />
    <hkern u1="z" u2="&#x15d;" k="7" />
    <hkern u1="z" u2="&#x15b;" k="7" />
    <hkern u1="z" u2="&#x153;" k="30" />
    <hkern u1="z" u2="&#x151;" k="30" />
    <hkern u1="z" u2="&#x14f;" k="30" />
    <hkern u1="z" u2="&#x14d;" k="30" />
    <hkern u1="z" u2="&#x123;" k="30" />
    <hkern u1="z" u2="&#x121;" k="30" />
    <hkern u1="z" u2="&#x11f;" k="30" />
    <hkern u1="z" u2="&#x11d;" k="30" />
    <hkern u1="z" u2="&#x11b;" k="30" />
    <hkern u1="z" u2="&#x119;" k="30" />
    <hkern u1="z" u2="&#x117;" k="30" />
    <hkern u1="z" u2="&#x115;" k="30" />
    <hkern u1="z" u2="&#x113;" k="30" />
    <hkern u1="z" u2="&#x111;" k="30" />
    <hkern u1="z" u2="&#x10f;" k="30" />
    <hkern u1="z" u2="&#x10d;" k="30" />
    <hkern u1="z" u2="&#x10b;" k="30" />
    <hkern u1="z" u2="&#x109;" k="30" />
    <hkern u1="z" u2="&#x107;" k="30" />
    <hkern u1="z" u2="&#xfc;" k="15" />
    <hkern u1="z" u2="&#xfb;" k="15" />
    <hkern u1="z" u2="&#xfa;" k="15" />
    <hkern u1="z" u2="&#xf9;" k="15" />
    <hkern u1="z" u2="&#xf6;" k="30" />
    <hkern u1="z" u2="&#xf5;" k="30" />
    <hkern u1="z" u2="&#xf4;" k="30" />
    <hkern u1="z" u2="&#xf3;" k="30" />
    <hkern u1="z" u2="&#xf2;" k="30" />
    <hkern u1="z" u2="&#xf0;" k="30" />
    <hkern u1="z" u2="&#xeb;" k="30" />
    <hkern u1="z" u2="&#xea;" k="30" />
    <hkern u1="z" u2="&#xe9;" k="30" />
    <hkern u1="z" u2="&#xe8;" k="30" />
    <hkern u1="z" u2="&#xe7;" k="30" />
    <hkern u1="z" u2="u" k="10" />
    <hkern u1="z" u2="s" k="7" />
    <hkern u1="z" u2="q" k="30" />
    <hkern u1="z" u2="o" k="30" />
    <hkern u1="z" u2="g" k="30" />
    <hkern u1="z" u2="e" k="30" />
    <hkern u1="z" u2="d" k="30" />
    <hkern u1="z" u2="c" k="30" />
    <hkern u1="z" u2="\" k="56" />
    <hkern u1="z" u2="&#x26;" k="24" />
    <hkern u1="&#xab;" u2="&#x422;" k="50" />
    <hkern u1="&#xab;" u2="&#x3ab;" k="40" />
    <hkern u1="&#xab;" u2="&#x3a5;" k="40" />
    <hkern u1="&#xab;" u2="&#x3a4;" k="30" />
    <hkern u1="&#xab;" u2="&#x178;" k="50" />
    <hkern u1="&#xab;" u2="&#x176;" k="50" />
    <hkern u1="&#xab;" u2="&#x166;" k="60" />
    <hkern u1="&#xab;" u2="&#x164;" k="60" />
    <hkern u1="&#xab;" u2="&#x162;" k="60" />
    <hkern u1="&#xab;" u2="&#xdd;" k="50" />
    <hkern u1="&#xab;" u2="Y" k="50" />
    <hkern u1="&#xab;" u2="V" k="40" />
    <hkern u1="&#xab;" u2="T" k="60" />
    <hkern u1="&#xb5;" u2="&#x3c4;" k="20" />
    <hkern u1="&#xb5;" u2="&#x3c0;" k="15" />
    <hkern u1="&#xbb;" u2="&#x178;" k="56" />
    <hkern u1="&#xbb;" u2="&#x176;" k="56" />
    <hkern u1="&#xbb;" u2="&#x166;" k="61" />
    <hkern u1="&#xbb;" u2="&#x164;" k="61" />
    <hkern u1="&#xbb;" u2="&#x162;" k="61" />
    <hkern u1="&#xbb;" u2="&#xdd;" k="56" />
    <hkern u1="&#xbb;" u2="Y" k="56" />
    <hkern u1="&#xbb;" u2="V" k="32" />
    <hkern u1="&#xbb;" u2="T" k="61" />
    <hkern u1="&#xbf;" u2="&#x177;" k="10" />
    <hkern u1="&#xbf;" u2="&#x175;" k="5" />
    <hkern u1="&#xbf;" u2="&#xff;" k="10" />
    <hkern u1="&#xbf;" u2="&#xfd;" k="10" />
    <hkern u1="&#xbf;" u2="y" k="10" />
    <hkern u1="&#xbf;" u2="x" k="15" />
    <hkern u1="&#xbf;" u2="w" k="5" />
    <hkern u1="&#xbf;" u2="v" k="10" />
    <hkern u1="&#xc0;" u2="&#x201d;" k="32" />
    <hkern u1="&#xc0;" u2="&#x201c;" k="89" />
    <hkern u1="&#xc0;" u2="&#x2019;" k="32" />
    <hkern u1="&#xc0;" u2="&#x2018;" k="89" />
    <hkern u1="&#xc0;" u2="&#x152;" k="25" />
    <hkern u1="&#xc0;" u2="y" k="30" />
    <hkern u1="&#xc0;" u2="w" k="25" />
    <hkern u1="&#xc0;" u2="v" k="30" />
    <hkern u1="&#xc0;" u2="t" k="25" />
    <hkern u1="&#xc0;" u2="\" k="80" />
    <hkern u1="&#xc0;" u2="Y" k="95" />
    <hkern u1="&#xc0;" u2="W" k="40" />
    <hkern u1="&#xc0;" u2="V" k="85" />
    <hkern u1="&#xc0;" u2="U" k="25" />
    <hkern u1="&#xc0;" u2="T" k="80" />
    <hkern u1="&#xc0;" u2="Q" k="25" />
    <hkern u1="&#xc0;" u2="O" k="25" />
    <hkern u1="&#xc0;" u2="G" k="25" />
    <hkern u1="&#xc0;" u2="C" k="25" />
    <hkern u1="&#xc0;" u2="&#x2a;" k="39" />
    <hkern u1="&#xc1;" u2="&#x201d;" k="32" />
    <hkern u1="&#xc1;" u2="&#x201c;" k="89" />
    <hkern u1="&#xc1;" u2="&#x2019;" k="32" />
    <hkern u1="&#xc1;" u2="&#x2018;" k="89" />
    <hkern u1="&#xc1;" u2="&#x152;" k="25" />
    <hkern u1="&#xc1;" u2="y" k="30" />
    <hkern u1="&#xc1;" u2="w" k="25" />
    <hkern u1="&#xc1;" u2="v" k="30" />
    <hkern u1="&#xc1;" u2="t" k="25" />
    <hkern u1="&#xc1;" u2="\" k="80" />
    <hkern u1="&#xc1;" u2="Y" k="95" />
    <hkern u1="&#xc1;" u2="W" k="40" />
    <hkern u1="&#xc1;" u2="V" k="85" />
    <hkern u1="&#xc1;" u2="U" k="25" />
    <hkern u1="&#xc1;" u2="T" k="80" />
    <hkern u1="&#xc1;" u2="Q" k="25" />
    <hkern u1="&#xc1;" u2="O" k="25" />
    <hkern u1="&#xc1;" u2="G" k="25" />
    <hkern u1="&#xc1;" u2="C" k="25" />
    <hkern u1="&#xc1;" u2="&#x2a;" k="39" />
    <hkern u1="&#xc2;" u2="&#x201d;" k="32" />
    <hkern u1="&#xc2;" u2="&#x201c;" k="89" />
    <hkern u1="&#xc2;" u2="&#x2019;" k="32" />
    <hkern u1="&#xc2;" u2="&#x2018;" k="89" />
    <hkern u1="&#xc2;" u2="&#x152;" k="25" />
    <hkern u1="&#xc2;" u2="y" k="30" />
    <hkern u1="&#xc2;" u2="w" k="25" />
    <hkern u1="&#xc2;" u2="v" k="30" />
    <hkern u1="&#xc2;" u2="t" k="25" />
    <hkern u1="&#xc2;" u2="\" k="80" />
    <hkern u1="&#xc2;" u2="Y" k="95" />
    <hkern u1="&#xc2;" u2="W" k="40" />
    <hkern u1="&#xc2;" u2="V" k="85" />
    <hkern u1="&#xc2;" u2="U" k="25" />
    <hkern u1="&#xc2;" u2="T" k="80" />
    <hkern u1="&#xc2;" u2="Q" k="25" />
    <hkern u1="&#xc2;" u2="O" k="25" />
    <hkern u1="&#xc2;" u2="G" k="25" />
    <hkern u1="&#xc2;" u2="C" k="25" />
    <hkern u1="&#xc2;" u2="&#x2a;" k="39" />
    <hkern u1="&#xc3;" u2="&#x201d;" k="32" />
    <hkern u1="&#xc3;" u2="&#x201c;" k="89" />
    <hkern u1="&#xc3;" u2="&#x2019;" k="32" />
    <hkern u1="&#xc3;" u2="&#x2018;" k="89" />
    <hkern u1="&#xc3;" u2="&#x152;" k="25" />
    <hkern u1="&#xc3;" u2="y" k="30" />
    <hkern u1="&#xc3;" u2="w" k="25" />
    <hkern u1="&#xc3;" u2="v" k="30" />
    <hkern u1="&#xc3;" u2="t" k="25" />
    <hkern u1="&#xc3;" u2="\" k="80" />
    <hkern u1="&#xc3;" u2="Y" k="95" />
    <hkern u1="&#xc3;" u2="W" k="40" />
    <hkern u1="&#xc3;" u2="V" k="85" />
    <hkern u1="&#xc3;" u2="U" k="25" />
    <hkern u1="&#xc3;" u2="T" k="80" />
    <hkern u1="&#xc3;" u2="Q" k="25" />
    <hkern u1="&#xc3;" u2="O" k="25" />
    <hkern u1="&#xc3;" u2="G" k="25" />
    <hkern u1="&#xc3;" u2="C" k="25" />
    <hkern u1="&#xc3;" u2="&#x2a;" k="39" />
    <hkern u1="&#xc4;" u2="&#x201d;" k="32" />
    <hkern u1="&#xc4;" u2="&#x201c;" k="89" />
    <hkern u1="&#xc4;" u2="&#x2019;" k="32" />
    <hkern u1="&#xc4;" u2="&#x2018;" k="89" />
    <hkern u1="&#xc4;" u2="&#x152;" k="25" />
    <hkern u1="&#xc4;" u2="y" k="30" />
    <hkern u1="&#xc4;" u2="w" k="25" />
    <hkern u1="&#xc4;" u2="v" k="30" />
    <hkern u1="&#xc4;" u2="t" k="25" />
    <hkern u1="&#xc4;" u2="\" k="80" />
    <hkern u1="&#xc4;" u2="Y" k="95" />
    <hkern u1="&#xc4;" u2="W" k="40" />
    <hkern u1="&#xc4;" u2="V" k="85" />
    <hkern u1="&#xc4;" u2="U" k="25" />
    <hkern u1="&#xc4;" u2="T" k="80" />
    <hkern u1="&#xc4;" u2="Q" k="25" />
    <hkern u1="&#xc4;" u2="O" k="25" />
    <hkern u1="&#xc4;" u2="G" k="25" />
    <hkern u1="&#xc4;" u2="C" k="25" />
    <hkern u1="&#xc4;" u2="&#x2a;" k="39" />
    <hkern u1="&#xc5;" u2="&#x201d;" k="32" />
    <hkern u1="&#xc5;" u2="&#x201c;" k="89" />
    <hkern u1="&#xc5;" u2="&#x2019;" k="32" />
    <hkern u1="&#xc5;" u2="&#x2018;" k="89" />
    <hkern u1="&#xc5;" u2="&#x152;" k="25" />
    <hkern u1="&#xc5;" u2="y" k="30" />
    <hkern u1="&#xc5;" u2="w" k="25" />
    <hkern u1="&#xc5;" u2="v" k="30" />
    <hkern u1="&#xc5;" u2="t" k="25" />
    <hkern u1="&#xc5;" u2="\" k="80" />
    <hkern u1="&#xc5;" u2="Y" k="95" />
    <hkern u1="&#xc5;" u2="W" k="40" />
    <hkern u1="&#xc5;" u2="V" k="85" />
    <hkern u1="&#xc5;" u2="U" k="25" />
    <hkern u1="&#xc5;" u2="T" k="80" />
    <hkern u1="&#xc5;" u2="Q" k="25" />
    <hkern u1="&#xc5;" u2="O" k="25" />
    <hkern u1="&#xc5;" u2="G" k="25" />
    <hkern u1="&#xc5;" u2="C" k="25" />
    <hkern u1="&#xc5;" u2="&#x2a;" k="39" />
    <hkern u1="&#xc6;" u2="&#x177;" k="20" />
    <hkern u1="&#xc6;" u2="&#x175;" k="20" />
    <hkern u1="&#xc6;" u2="&#x173;" k="15" />
    <hkern u1="&#xc6;" u2="&#x171;" k="15" />
    <hkern u1="&#xc6;" u2="&#x16f;" k="15" />
    <hkern u1="&#xc6;" u2="&#x16d;" k="15" />
    <hkern u1="&#xc6;" u2="&#x16b;" k="15" />
    <hkern u1="&#xc6;" u2="&#x169;" k="15" />
    <hkern u1="&#xc6;" u2="&#x167;" k="20" />
    <hkern u1="&#xc6;" u2="&#x165;" k="20" />
    <hkern u1="&#xc6;" u2="&#x163;" k="20" />
    <hkern u1="&#xc6;" u2="&#x153;" k="20" />
    <hkern u1="&#xc6;" u2="&#x152;" k="20" />
    <hkern u1="&#xc6;" u2="&#x151;" k="20" />
    <hkern u1="&#xc6;" u2="&#x150;" k="20" />
    <hkern u1="&#xc6;" u2="&#x14f;" k="20" />
    <hkern u1="&#xc6;" u2="&#x14e;" k="20" />
    <hkern u1="&#xc6;" u2="&#x14d;" k="20" />
    <hkern u1="&#xc6;" u2="&#x14c;" k="20" />
    <hkern u1="&#xc6;" u2="&#x123;" k="20" />
    <hkern u1="&#xc6;" u2="&#x122;" k="20" />
    <hkern u1="&#xc6;" u2="&#x121;" k="20" />
    <hkern u1="&#xc6;" u2="&#x120;" k="20" />
    <hkern u1="&#xc6;" u2="&#x11f;" k="20" />
    <hkern u1="&#xc6;" u2="&#x11e;" k="20" />
    <hkern u1="&#xc6;" u2="&#x11d;" k="20" />
    <hkern u1="&#xc6;" u2="&#x11c;" k="20" />
    <hkern u1="&#xc6;" u2="&#x11b;" k="20" />
    <hkern u1="&#xc6;" u2="&#x119;" k="20" />
    <hkern u1="&#xc6;" u2="&#x117;" k="20" />
    <hkern u1="&#xc6;" u2="&#x115;" k="20" />
    <hkern u1="&#xc6;" u2="&#x113;" k="20" />
    <hkern u1="&#xc6;" u2="&#x111;" k="20" />
    <hkern u1="&#xc6;" u2="&#x10f;" k="20" />
    <hkern u1="&#xc6;" u2="&#x10d;" k="20" />
    <hkern u1="&#xc6;" u2="&#x10c;" k="20" />
    <hkern u1="&#xc6;" u2="&#x10b;" k="20" />
    <hkern u1="&#xc6;" u2="&#x10a;" k="20" />
    <hkern u1="&#xc6;" u2="&#x109;" k="20" />
    <hkern u1="&#xc6;" u2="&#x108;" k="20" />
    <hkern u1="&#xc6;" u2="&#x107;" k="20" />
    <hkern u1="&#xc6;" u2="&#x106;" k="20" />
    <hkern u1="&#xc6;" u2="&#xff;" k="20" />
    <hkern u1="&#xc6;" u2="&#xfd;" k="20" />
    <hkern u1="&#xc6;" u2="&#xfc;" k="15" />
    <hkern u1="&#xc6;" u2="&#xfb;" k="15" />
    <hkern u1="&#xc6;" u2="&#xfa;" k="15" />
    <hkern u1="&#xc6;" u2="&#xf9;" k="15" />
    <hkern u1="&#xc6;" u2="&#xf6;" k="20" />
    <hkern u1="&#xc6;" u2="&#xf5;" k="20" />
    <hkern u1="&#xc6;" u2="&#xf4;" k="20" />
    <hkern u1="&#xc6;" u2="&#xf3;" k="20" />
    <hkern u1="&#xc6;" u2="&#xf2;" k="20" />
    <hkern u1="&#xc6;" u2="&#xf0;" k="20" />
    <hkern u1="&#xc6;" u2="&#xeb;" k="20" />
    <hkern u1="&#xc6;" u2="&#xea;" k="20" />
    <hkern u1="&#xc6;" u2="&#xe9;" k="20" />
    <hkern u1="&#xc6;" u2="&#xe8;" k="20" />
    <hkern u1="&#xc6;" u2="&#xe7;" k="20" />
    <hkern u1="&#xc6;" u2="&#xd6;" k="20" />
    <hkern u1="&#xc6;" u2="&#xd5;" k="20" />
    <hkern u1="&#xc6;" u2="&#xd4;" k="20" />
    <hkern u1="&#xc6;" u2="&#xd3;" k="20" />
    <hkern u1="&#xc6;" u2="&#xd2;" k="20" />
    <hkern u1="&#xc6;" u2="&#xc7;" k="20" />
    <hkern u1="&#xc6;" u2="y" k="20" />
    <hkern u1="&#xc6;" u2="w" k="20" />
    <hkern u1="&#xc6;" u2="v" k="25" />
    <hkern u1="&#xc6;" u2="u" k="15" />
    <hkern u1="&#xc6;" u2="t" k="20" />
    <hkern u1="&#xc6;" u2="q" k="20" />
    <hkern u1="&#xc6;" u2="o" k="20" />
    <hkern u1="&#xc6;" u2="g" k="20" />
    <hkern u1="&#xc6;" u2="e" k="20" />
    <hkern u1="&#xc6;" u2="d" k="20" />
    <hkern u1="&#xc6;" u2="c" k="20" />
    <hkern u1="&#xc6;" u2="Q" k="20" />
    <hkern u1="&#xc6;" u2="O" k="20" />
    <hkern u1="&#xc6;" u2="G" k="20" />
    <hkern u1="&#xc6;" u2="C" k="20" />
    <hkern u1="&#xc7;" u2="&#x153;" k="15" />
    <hkern u1="&#xc7;" u2="&#x152;" k="20" />
    <hkern u1="&#xc7;" u2="&#xf0;" k="15" />
    <hkern u1="&#xc7;" u2="y" k="19" />
    <hkern u1="&#xc7;" u2="u" k="20" />
    <hkern u1="&#xc7;" u2="q" k="10" />
    <hkern u1="&#xc7;" u2="o" k="15" />
    <hkern u1="&#xc7;" u2="g" k="10" />
    <hkern u1="&#xc7;" u2="e" k="15" />
    <hkern u1="&#xc7;" u2="d" k="10" />
    <hkern u1="&#xc7;" u2="c" k="15" />
    <hkern u1="&#xc7;" u2="Q" k="20" />
    <hkern u1="&#xc7;" u2="O" k="20" />
    <hkern u1="&#xc7;" u2="G" k="20" />
    <hkern u1="&#xc7;" u2="C" k="20" />
    <hkern u1="&#xc8;" u2="&#x153;" k="20" />
    <hkern u1="&#xc8;" u2="&#x152;" k="20" />
    <hkern u1="&#xc8;" u2="&#xf0;" k="20" />
    <hkern u1="&#xc8;" u2="y" k="20" />
    <hkern u1="&#xc8;" u2="w" k="20" />
    <hkern u1="&#xc8;" u2="v" k="25" />
    <hkern u1="&#xc8;" u2="u" k="15" />
    <hkern u1="&#xc8;" u2="t" k="20" />
    <hkern u1="&#xc8;" u2="q" k="20" />
    <hkern u1="&#xc8;" u2="o" k="20" />
    <hkern u1="&#xc8;" u2="g" k="20" />
    <hkern u1="&#xc8;" u2="e" k="20" />
    <hkern u1="&#xc8;" u2="d" k="20" />
    <hkern u1="&#xc8;" u2="c" k="20" />
    <hkern u1="&#xc8;" u2="Q" k="20" />
    <hkern u1="&#xc8;" u2="O" k="20" />
    <hkern u1="&#xc8;" u2="G" k="20" />
    <hkern u1="&#xc8;" u2="C" k="20" />
    <hkern u1="&#xc9;" u2="&#x153;" k="20" />
    <hkern u1="&#xc9;" u2="&#x152;" k="20" />
    <hkern u1="&#xc9;" u2="&#xf0;" k="20" />
    <hkern u1="&#xc9;" u2="y" k="20" />
    <hkern u1="&#xc9;" u2="w" k="20" />
    <hkern u1="&#xc9;" u2="v" k="25" />
    <hkern u1="&#xc9;" u2="u" k="15" />
    <hkern u1="&#xc9;" u2="t" k="20" />
    <hkern u1="&#xc9;" u2="q" k="20" />
    <hkern u1="&#xc9;" u2="o" k="20" />
    <hkern u1="&#xc9;" u2="g" k="20" />
    <hkern u1="&#xc9;" u2="e" k="20" />
    <hkern u1="&#xc9;" u2="d" k="20" />
    <hkern u1="&#xc9;" u2="c" k="20" />
    <hkern u1="&#xc9;" u2="Q" k="20" />
    <hkern u1="&#xc9;" u2="O" k="20" />
    <hkern u1="&#xc9;" u2="G" k="20" />
    <hkern u1="&#xc9;" u2="C" k="20" />
    <hkern u1="&#xca;" u2="&#x153;" k="20" />
    <hkern u1="&#xca;" u2="&#x152;" k="20" />
    <hkern u1="&#xca;" u2="&#xf0;" k="20" />
    <hkern u1="&#xca;" u2="y" k="20" />
    <hkern u1="&#xca;" u2="w" k="20" />
    <hkern u1="&#xca;" u2="v" k="25" />
    <hkern u1="&#xca;" u2="u" k="15" />
    <hkern u1="&#xca;" u2="t" k="20" />
    <hkern u1="&#xca;" u2="q" k="20" />
    <hkern u1="&#xca;" u2="o" k="20" />
    <hkern u1="&#xca;" u2="g" k="20" />
    <hkern u1="&#xca;" u2="e" k="20" />
    <hkern u1="&#xca;" u2="d" k="20" />
    <hkern u1="&#xca;" u2="c" k="20" />
    <hkern u1="&#xca;" u2="Q" k="20" />
    <hkern u1="&#xca;" u2="O" k="20" />
    <hkern u1="&#xca;" u2="G" k="20" />
    <hkern u1="&#xca;" u2="C" k="20" />
    <hkern u1="&#xcb;" u2="&#x153;" k="20" />
    <hkern u1="&#xcb;" u2="&#x152;" k="20" />
    <hkern u1="&#xcb;" u2="&#xf0;" k="20" />
    <hkern u1="&#xcb;" u2="y" k="20" />
    <hkern u1="&#xcb;" u2="w" k="20" />
    <hkern u1="&#xcb;" u2="v" k="25" />
    <hkern u1="&#xcb;" u2="u" k="15" />
    <hkern u1="&#xcb;" u2="t" k="20" />
    <hkern u1="&#xcb;" u2="q" k="20" />
    <hkern u1="&#xcb;" u2="o" k="20" />
    <hkern u1="&#xcb;" u2="g" k="20" />
    <hkern u1="&#xcb;" u2="e" k="20" />
    <hkern u1="&#xcb;" u2="d" k="20" />
    <hkern u1="&#xcb;" u2="c" k="20" />
    <hkern u1="&#xcb;" u2="Q" k="20" />
    <hkern u1="&#xcb;" u2="O" k="20" />
    <hkern u1="&#xcb;" u2="G" k="20" />
    <hkern u1="&#xcb;" u2="C" k="20" />
    <hkern u1="&#xd2;" u2="&#xe6;" k="10" />
    <hkern u1="&#xd2;" u2="&#xc6;" k="38" />
    <hkern u1="&#xd2;" u2="&#xb8;" k="25" />
    <hkern u1="&#xd2;" u2="z" k="20" />
    <hkern u1="&#xd2;" u2="s" k="15" />
    <hkern u1="&#xd2;" u2="a" k="10" />
    <hkern u1="&#xd2;" u2="Z" k="30" />
    <hkern u1="&#xd2;" u2="Y" k="55" />
    <hkern u1="&#xd2;" u2="X" k="35" />
    <hkern u1="&#xd2;" u2="V" k="30" />
    <hkern u1="&#xd2;" u2="T" k="30" />
    <hkern u1="&#xd2;" u2="S" k="15" />
    <hkern u1="&#xd2;" u2="J" k="41" />
    <hkern u1="&#xd2;" u2="A" k="25" />
    <hkern u1="&#xd3;" u2="&#xe6;" k="10" />
    <hkern u1="&#xd3;" u2="&#xc6;" k="38" />
    <hkern u1="&#xd3;" u2="&#xb8;" k="25" />
    <hkern u1="&#xd3;" u2="z" k="20" />
    <hkern u1="&#xd3;" u2="s" k="15" />
    <hkern u1="&#xd3;" u2="a" k="10" />
    <hkern u1="&#xd3;" u2="Z" k="30" />
    <hkern u1="&#xd3;" u2="Y" k="55" />
    <hkern u1="&#xd3;" u2="X" k="35" />
    <hkern u1="&#xd3;" u2="V" k="30" />
    <hkern u1="&#xd3;" u2="T" k="30" />
    <hkern u1="&#xd3;" u2="S" k="15" />
    <hkern u1="&#xd3;" u2="J" k="41" />
    <hkern u1="&#xd3;" u2="A" k="25" />
    <hkern u1="&#xd4;" u2="&#xe6;" k="10" />
    <hkern u1="&#xd4;" u2="&#xc6;" k="38" />
    <hkern u1="&#xd4;" u2="&#xb8;" k="25" />
    <hkern u1="&#xd4;" u2="z" k="20" />
    <hkern u1="&#xd4;" u2="s" k="15" />
    <hkern u1="&#xd4;" u2="a" k="10" />
    <hkern u1="&#xd4;" u2="Z" k="30" />
    <hkern u1="&#xd4;" u2="Y" k="55" />
    <hkern u1="&#xd4;" u2="X" k="35" />
    <hkern u1="&#xd4;" u2="V" k="30" />
    <hkern u1="&#xd4;" u2="T" k="30" />
    <hkern u1="&#xd4;" u2="S" k="15" />
    <hkern u1="&#xd4;" u2="J" k="41" />
    <hkern u1="&#xd4;" u2="A" k="25" />
    <hkern u1="&#xd5;" u2="&#xe6;" k="10" />
    <hkern u1="&#xd5;" u2="&#xc6;" k="38" />
    <hkern u1="&#xd5;" u2="&#xb8;" k="25" />
    <hkern u1="&#xd5;" u2="z" k="20" />
    <hkern u1="&#xd5;" u2="s" k="15" />
    <hkern u1="&#xd5;" u2="a" k="10" />
    <hkern u1="&#xd5;" u2="Z" k="30" />
    <hkern u1="&#xd5;" u2="Y" k="55" />
    <hkern u1="&#xd5;" u2="X" k="35" />
    <hkern u1="&#xd5;" u2="V" k="30" />
    <hkern u1="&#xd5;" u2="T" k="30" />
    <hkern u1="&#xd5;" u2="S" k="15" />
    <hkern u1="&#xd5;" u2="J" k="41" />
    <hkern u1="&#xd5;" u2="A" k="25" />
    <hkern u1="&#xd6;" u2="&#xe6;" k="10" />
    <hkern u1="&#xd6;" u2="&#xc6;" k="38" />
    <hkern u1="&#xd6;" u2="&#xb8;" k="25" />
    <hkern u1="&#xd6;" u2="z" k="20" />
    <hkern u1="&#xd6;" u2="s" k="15" />
    <hkern u1="&#xd6;" u2="a" k="10" />
    <hkern u1="&#xd6;" u2="Z" k="30" />
    <hkern u1="&#xd6;" u2="Y" k="55" />
    <hkern u1="&#xd6;" u2="X" k="35" />
    <hkern u1="&#xd6;" u2="V" k="30" />
    <hkern u1="&#xd6;" u2="T" k="30" />
    <hkern u1="&#xd6;" u2="S" k="15" />
    <hkern u1="&#xd6;" u2="J" k="41" />
    <hkern u1="&#xd6;" u2="A" k="25" />
    <hkern u1="&#xd8;" u2="&#xc6;" k="38" />
    <hkern u1="&#xd8;" u2="Y" k="55" />
    <hkern u1="&#xd8;" u2="X" k="35" />
    <hkern u1="&#xd8;" u2="V" k="30" />
    <hkern u1="&#xd8;" u2="T" k="30" />
    <hkern u1="&#xd8;" u2="J" k="41" />
    <hkern u1="&#xd8;" u2="A" k="25" />
    <hkern u1="&#xd9;" u2="&#x153;" k="15" />
    <hkern u1="&#xd9;" u2="&#xf0;" k="15" />
    <hkern u1="&#xd9;" u2="&#xe6;" k="20" />
    <hkern u1="&#xd9;" u2="&#xc6;" k="38" />
    <hkern u1="&#xd9;" u2="z" k="30" />
    <hkern u1="&#xd9;" u2="y" k="10" />
    <hkern u1="&#xd9;" u2="x" k="15" />
    <hkern u1="&#xd9;" u2="t" k="15" />
    <hkern u1="&#xd9;" u2="s" k="20" />
    <hkern u1="&#xd9;" u2="q" k="15" />
    <hkern u1="&#xd9;" u2="o" k="15" />
    <hkern u1="&#xd9;" u2="g" k="15" />
    <hkern u1="&#xd9;" u2="e" k="15" />
    <hkern u1="&#xd9;" u2="d" k="15" />
    <hkern u1="&#xd9;" u2="c" k="15" />
    <hkern u1="&#xd9;" u2="a" k="20" />
    <hkern u1="&#xd9;" u2="Z" k="25" />
    <hkern u1="&#xd9;" u2="S" k="10" />
    <hkern u1="&#xd9;" u2="J" k="40" />
    <hkern u1="&#xd9;" u2="A" k="25" />
    <hkern u1="&#xda;" u2="&#x153;" k="15" />
    <hkern u1="&#xda;" u2="&#xf0;" k="15" />
    <hkern u1="&#xda;" u2="&#xe6;" k="20" />
    <hkern u1="&#xda;" u2="&#xc6;" k="38" />
    <hkern u1="&#xda;" u2="z" k="30" />
    <hkern u1="&#xda;" u2="y" k="10" />
    <hkern u1="&#xda;" u2="x" k="15" />
    <hkern u1="&#xda;" u2="t" k="15" />
    <hkern u1="&#xda;" u2="s" k="20" />
    <hkern u1="&#xda;" u2="q" k="15" />
    <hkern u1="&#xda;" u2="o" k="15" />
    <hkern u1="&#xda;" u2="g" k="15" />
    <hkern u1="&#xda;" u2="e" k="15" />
    <hkern u1="&#xda;" u2="d" k="15" />
    <hkern u1="&#xda;" u2="c" k="15" />
    <hkern u1="&#xda;" u2="a" k="20" />
    <hkern u1="&#xda;" u2="Z" k="25" />
    <hkern u1="&#xda;" u2="S" k="10" />
    <hkern u1="&#xda;" u2="J" k="40" />
    <hkern u1="&#xda;" u2="A" k="25" />
    <hkern u1="&#xdb;" u2="&#x153;" k="15" />
    <hkern u1="&#xdb;" u2="&#xf0;" k="15" />
    <hkern u1="&#xdb;" u2="&#xe6;" k="20" />
    <hkern u1="&#xdb;" u2="&#xc6;" k="38" />
    <hkern u1="&#xdb;" u2="z" k="30" />
    <hkern u1="&#xdb;" u2="y" k="10" />
    <hkern u1="&#xdb;" u2="x" k="15" />
    <hkern u1="&#xdb;" u2="t" k="15" />
    <hkern u1="&#xdb;" u2="s" k="20" />
    <hkern u1="&#xdb;" u2="q" k="15" />
    <hkern u1="&#xdb;" u2="o" k="15" />
    <hkern u1="&#xdb;" u2="g" k="15" />
    <hkern u1="&#xdb;" u2="e" k="15" />
    <hkern u1="&#xdb;" u2="d" k="15" />
    <hkern u1="&#xdb;" u2="c" k="15" />
    <hkern u1="&#xdb;" u2="a" k="20" />
    <hkern u1="&#xdb;" u2="Z" k="25" />
    <hkern u1="&#xdb;" u2="S" k="10" />
    <hkern u1="&#xdb;" u2="J" k="40" />
    <hkern u1="&#xdb;" u2="A" k="25" />
    <hkern u1="&#xdc;" u2="&#x153;" k="15" />
    <hkern u1="&#xdc;" u2="&#xf0;" k="15" />
    <hkern u1="&#xdc;" u2="&#xe6;" k="20" />
    <hkern u1="&#xdc;" u2="&#xc6;" k="38" />
    <hkern u1="&#xdc;" u2="z" k="30" />
    <hkern u1="&#xdc;" u2="y" k="10" />
    <hkern u1="&#xdc;" u2="x" k="15" />
    <hkern u1="&#xdc;" u2="t" k="15" />
    <hkern u1="&#xdc;" u2="s" k="20" />
    <hkern u1="&#xdc;" u2="q" k="15" />
    <hkern u1="&#xdc;" u2="o" k="15" />
    <hkern u1="&#xdc;" u2="g" k="15" />
    <hkern u1="&#xdc;" u2="e" k="15" />
    <hkern u1="&#xdc;" u2="d" k="15" />
    <hkern u1="&#xdc;" u2="c" k="15" />
    <hkern u1="&#xdc;" u2="a" k="20" />
    <hkern u1="&#xdc;" u2="Z" k="25" />
    <hkern u1="&#xdc;" u2="S" k="10" />
    <hkern u1="&#xdc;" u2="J" k="40" />
    <hkern u1="&#xdc;" u2="A" k="25" />
    <hkern u1="&#xdd;" u2="&#x2039;" k="57" />
    <hkern u1="&#xdd;" u2="&#x2026;" k="39" />
    <hkern u1="&#xdd;" u2="&#x2014;" k="37" />
    <hkern u1="&#xdd;" u2="&#x2013;" k="37" />
    <hkern u1="&#xdd;" u2="&#x153;" k="95" />
    <hkern u1="&#xdd;" u2="&#x152;" k="50" />
    <hkern u1="&#xdd;" u2="&#x14b;" k="75" />
    <hkern u1="&#xdd;" u2="&#xf0;" k="95" />
    <hkern u1="&#xdd;" u2="&#xe6;" k="80" />
    <hkern u1="&#xdd;" u2="&#xc6;" k="110" />
    <hkern u1="&#xdd;" u2="&#xbf;" k="90" />
    <hkern u1="&#xdd;" u2="&#xbb;" k="50" />
    <hkern u1="&#xdd;" u2="&#xab;" k="57" />
    <hkern u1="&#xdd;" u2="z" k="61" />
    <hkern u1="&#xdd;" u2="x" k="33" />
    <hkern u1="&#xdd;" u2="w" k="24" />
    <hkern u1="&#xdd;" u2="v" k="28" />
    <hkern u1="&#xdd;" u2="u" k="66" />
    <hkern u1="&#xdd;" u2="t" k="33" />
    <hkern u1="&#xdd;" u2="s" k="80" />
    <hkern u1="&#xdd;" u2="r" k="75" />
    <hkern u1="&#xdd;" u2="q" k="95" />
    <hkern u1="&#xdd;" u2="p" k="75" />
    <hkern u1="&#xdd;" u2="o" k="95" />
    <hkern u1="&#xdd;" u2="n" k="75" />
    <hkern u1="&#xdd;" u2="m" k="75" />
    <hkern u1="&#xdd;" u2="g" k="95" />
    <hkern u1="&#xdd;" u2="f" k="38" />
    <hkern u1="&#xdd;" u2="e" k="95" />
    <hkern u1="&#xdd;" u2="d" k="95" />
    <hkern u1="&#xdd;" u2="c" k="95" />
    <hkern u1="&#xdd;" u2="a" k="80" />
    <hkern u1="&#xdd;" u2="S" k="15" />
    <hkern u1="&#xdd;" u2="Q" k="50" />
    <hkern u1="&#xdd;" u2="O" k="50" />
    <hkern u1="&#xdd;" u2="J" k="115" />
    <hkern u1="&#xdd;" u2="G" k="50" />
    <hkern u1="&#xdd;" u2="C" k="50" />
    <hkern u1="&#xdd;" u2="A" k="95" />
    <hkern u1="&#xdd;" u2="&#x40;" k="55" />
    <hkern u1="&#xdd;" u2="&#x3f;" k="-22" />
    <hkern u1="&#xdd;" u2="&#x3b;" k="31" />
    <hkern u1="&#xdd;" u2="&#x3a;" k="31" />
    <hkern u1="&#xdd;" u2="&#x2f;" k="61" />
    <hkern u1="&#xdd;" u2="&#x2e;" k="39" />
    <hkern u1="&#xdd;" u2="&#x2d;" k="37" />
    <hkern u1="&#xdd;" u2="&#x2c;" k="39" />
    <hkern u1="&#xdd;" u2="&#x26;" k="71" />
    <hkern u1="&#xe0;" u2="&#x201c;" k="70" />
    <hkern u1="&#xe0;" u2="&#x2018;" k="70" />
    <hkern u1="&#xe0;" u2="y" k="20" />
    <hkern u1="&#xe0;" u2="v" k="15" />
    <hkern u1="&#xe0;" u2="\" k="65" />
    <hkern u1="&#xe1;" u2="&#x201c;" k="70" />
    <hkern u1="&#xe1;" u2="&#x2018;" k="70" />
    <hkern u1="&#xe1;" u2="y" k="20" />
    <hkern u1="&#xe1;" u2="v" k="15" />
    <hkern u1="&#xe1;" u2="\" k="65" />
    <hkern u1="&#xe2;" u2="&#x201c;" k="70" />
    <hkern u1="&#xe2;" u2="&#x2018;" k="70" />
    <hkern u1="&#xe2;" u2="y" k="20" />
    <hkern u1="&#xe2;" u2="v" k="15" />
    <hkern u1="&#xe2;" u2="\" k="65" />
    <hkern u1="&#xe3;" u2="&#x201c;" k="70" />
    <hkern u1="&#xe3;" u2="&#x2018;" k="70" />
    <hkern u1="&#xe3;" u2="y" k="20" />
    <hkern u1="&#xe3;" u2="v" k="15" />
    <hkern u1="&#xe3;" u2="\" k="65" />
    <hkern u1="&#xe4;" u2="&#x201c;" k="70" />
    <hkern u1="&#xe4;" u2="&#x2018;" k="70" />
    <hkern u1="&#xe4;" u2="y" k="20" />
    <hkern u1="&#xe4;" u2="v" k="15" />
    <hkern u1="&#xe4;" u2="\" k="65" />
    <hkern u1="&#xe5;" u2="&#x201c;" k="70" />
    <hkern u1="&#xe5;" u2="&#x2018;" k="70" />
    <hkern u1="&#xe5;" u2="y" k="20" />
    <hkern u1="&#xe5;" u2="v" k="15" />
    <hkern u1="&#xe5;" u2="\" k="65" />
    <hkern u1="&#xe6;" u2="&#x177;" k="10" />
    <hkern u1="&#xe6;" u2="&#xff;" k="10" />
    <hkern u1="&#xe6;" u2="&#xfd;" k="10" />
    <hkern u1="&#xe6;" u2="y" k="10" />
    <hkern u1="&#xe6;" u2="x" k="7" />
    <hkern u1="&#xe6;" u2="v" k="5" />
    <hkern u1="&#xe6;" u2="\" k="61" />
    <hkern u1="&#xe7;" u2="&#x201c;" k="42" />
    <hkern u1="&#xe7;" u2="&#x2018;" k="42" />
    <hkern u1="&#xe7;" u2="&#x153;" k="6" />
    <hkern u1="&#xe7;" u2="&#xf0;" k="6" />
    <hkern u1="&#xe7;" u2="&#xe6;" k="-6" />
    <hkern u1="&#xe7;" u2="q" k="6" />
    <hkern u1="&#xe7;" u2="o" k="6" />
    <hkern u1="&#xe7;" u2="g" k="6" />
    <hkern u1="&#xe7;" u2="e" k="6" />
    <hkern u1="&#xe7;" u2="d" k="6" />
    <hkern u1="&#xe7;" u2="c" k="6" />
    <hkern u1="&#xe7;" u2="a" k="-6" />
    <hkern u1="&#xe7;" u2="\" k="61" />
    <hkern u1="&#xe8;" u2="y" k="10" />
    <hkern u1="&#xe8;" u2="x" k="7" />
    <hkern u1="&#xe8;" u2="v" k="5" />
    <hkern u1="&#xe8;" u2="\" k="61" />
    <hkern u1="&#xe9;" u2="y" k="10" />
    <hkern u1="&#xe9;" u2="x" k="7" />
    <hkern u1="&#xe9;" u2="v" k="5" />
    <hkern u1="&#xe9;" u2="\" k="61" />
    <hkern u1="&#xea;" u2="y" k="10" />
    <hkern u1="&#xea;" u2="x" k="7" />
    <hkern u1="&#xea;" u2="v" k="5" />
    <hkern u1="&#xea;" u2="\" k="61" />
    <hkern u1="&#xeb;" u2="y" k="10" />
    <hkern u1="&#xeb;" u2="x" k="7" />
    <hkern u1="&#xeb;" u2="v" k="5" />
    <hkern u1="&#xeb;" u2="\" k="61" />
    <hkern u1="&#xf0;" u2="&#x201c;" k="66" />
    <hkern u1="&#xf0;" u2="&#x2018;" k="66" />
    <hkern u1="&#xf0;" u2="&#x17e;" k="25" />
    <hkern u1="&#xf0;" u2="&#x17c;" k="25" />
    <hkern u1="&#xf0;" u2="&#x17a;" k="25" />
    <hkern u1="&#xf0;" u2="&#x177;" k="15" />
    <hkern u1="&#xf0;" u2="&#x175;" k="2" />
    <hkern u1="&#xf0;" u2="&#xff;" k="15" />
    <hkern u1="&#xf0;" u2="&#xfd;" k="15" />
    <hkern u1="&#xf0;" u2="z" k="25" />
    <hkern u1="&#xf0;" u2="y" k="5" />
    <hkern u1="&#xf0;" u2="x" k="20" />
    <hkern u1="&#xf0;" u2="w" k="2" />
    <hkern u1="&#xf0;" u2="v" k="10" />
    <hkern u1="&#xf0;" u2="\" k="94" />
    <hkern u1="&#xf1;" u2="&#x201c;" k="60" />
    <hkern u1="&#xf1;" u2="&#x2018;" k="60" />
    <hkern u1="&#xf1;" u2="y" k="20" />
    <hkern u1="&#xf1;" u2="w" k="10" />
    <hkern u1="&#xf1;" u2="v" k="20" />
    <hkern u1="&#xf1;" u2="\" k="70" />
    <hkern u1="&#xf2;" u2="&#x201c;" k="66" />
    <hkern u1="&#xf2;" u2="&#x2018;" k="66" />
    <hkern u1="&#xf2;" u2="z" k="25" />
    <hkern u1="&#xf2;" u2="y" k="15" />
    <hkern u1="&#xf2;" u2="x" k="20" />
    <hkern u1="&#xf2;" u2="w" k="2" />
    <hkern u1="&#xf2;" u2="v" k="10" />
    <hkern u1="&#xf2;" u2="\" k="94" />
    <hkern u1="&#xf3;" u2="&#x201c;" k="66" />
    <hkern u1="&#xf3;" u2="&#x2018;" k="66" />
    <hkern u1="&#xf3;" u2="z" k="25" />
    <hkern u1="&#xf3;" u2="y" k="15" />
    <hkern u1="&#xf3;" u2="x" k="20" />
    <hkern u1="&#xf3;" u2="w" k="2" />
    <hkern u1="&#xf3;" u2="v" k="10" />
    <hkern u1="&#xf3;" u2="\" k="94" />
    <hkern u1="&#xf4;" u2="&#x201c;" k="66" />
    <hkern u1="&#xf4;" u2="&#x2018;" k="66" />
    <hkern u1="&#xf4;" u2="z" k="25" />
    <hkern u1="&#xf4;" u2="y" k="15" />
    <hkern u1="&#xf4;" u2="x" k="20" />
    <hkern u1="&#xf4;" u2="w" k="2" />
    <hkern u1="&#xf4;" u2="v" k="10" />
    <hkern u1="&#xf4;" u2="\" k="94" />
    <hkern u1="&#xf5;" u2="&#x201c;" k="66" />
    <hkern u1="&#xf5;" u2="&#x2018;" k="66" />
    <hkern u1="&#xf5;" u2="z" k="25" />
    <hkern u1="&#xf5;" u2="y" k="15" />
    <hkern u1="&#xf5;" u2="x" k="20" />
    <hkern u1="&#xf5;" u2="w" k="2" />
    <hkern u1="&#xf5;" u2="v" k="10" />
    <hkern u1="&#xf5;" u2="\" k="94" />
    <hkern u1="&#xf6;" u2="&#x201c;" k="66" />
    <hkern u1="&#xf6;" u2="&#x2018;" k="66" />
    <hkern u1="&#xf6;" u2="z" k="25" />
    <hkern u1="&#xf6;" u2="y" k="15" />
    <hkern u1="&#xf6;" u2="x" k="20" />
    <hkern u1="&#xf6;" u2="w" k="2" />
    <hkern u1="&#xf6;" u2="v" k="10" />
    <hkern u1="&#xf6;" u2="\" k="94" />
    <hkern u1="&#xf8;" u2="y" k="15" />
    <hkern u1="&#xf8;" u2="w" k="2" />
    <hkern u1="&#xf8;" u2="v" k="10" />
    <hkern u1="&#xf9;" u2="&#x201c;" k="14" />
    <hkern u1="&#xf9;" u2="&#x2018;" k="14" />
    <hkern u1="&#xf9;" u2="\" k="56" />
    <hkern u1="&#xfa;" u2="&#x201c;" k="14" />
    <hkern u1="&#xfa;" u2="&#x2018;" k="14" />
    <hkern u1="&#xfa;" u2="\" k="56" />
    <hkern u1="&#xfb;" u2="&#x201c;" k="14" />
    <hkern u1="&#xfb;" u2="&#x2018;" k="14" />
    <hkern u1="&#xfb;" u2="\" k="56" />
    <hkern u1="&#xfc;" u2="&#x201c;" k="14" />
    <hkern u1="&#xfc;" u2="&#x2018;" k="14" />
    <hkern u1="&#xfc;" u2="\" k="56" />
    <hkern u1="&#xfd;" u2="&#x2026;" k="32" />
    <hkern u1="&#xfd;" u2="&#x2014;" k="15" />
    <hkern u1="&#xfd;" u2="&#x2013;" k="15" />
    <hkern u1="&#xfd;" u2="&#x2c7;" k="10" />
    <hkern u1="&#xfd;" u2="&#x153;" k="5" />
    <hkern u1="&#xfd;" u2="&#xf0;" k="5" />
    <hkern u1="&#xfd;" u2="&#xe6;" k="15" />
    <hkern u1="&#xfd;" u2="&#xbf;" k="10" />
    <hkern u1="&#xfd;" u2="t" k="-15" />
    <hkern u1="&#xfd;" u2="s" k="6" />
    <hkern u1="&#xfd;" u2="q" k="5" />
    <hkern u1="&#xfd;" u2="o" k="5" />
    <hkern u1="&#xfd;" u2="g" k="5" />
    <hkern u1="&#xfd;" u2="e" k="5" />
    <hkern u1="&#xfd;" u2="d" k="5" />
    <hkern u1="&#xfd;" u2="c" k="5" />
    <hkern u1="&#xfd;" u2="a" k="15" />
    <hkern u1="&#xfd;" u2="&#x2e;" k="32" />
    <hkern u1="&#xfd;" u2="&#x2d;" k="15" />
    <hkern u1="&#xfd;" u2="&#x2c;" k="32" />
    <hkern u1="&#xfe;" u2="&#x201c;" k="75" />
    <hkern u1="&#xfe;" u2="&#x2018;" k="75" />
    <hkern u1="&#xfe;" u2="&#x17e;" k="20" />
    <hkern u1="&#xfe;" u2="&#x17c;" k="20" />
    <hkern u1="&#xfe;" u2="&#x17a;" k="20" />
    <hkern u1="&#xfe;" u2="&#x177;" k="15" />
    <hkern u1="&#xfe;" u2="&#xff;" k="15" />
    <hkern u1="&#xfe;" u2="&#xfd;" k="15" />
    <hkern u1="&#xfe;" u2="z" k="20" />
    <hkern u1="&#xfe;" u2="y" k="15" />
    <hkern u1="&#xfe;" u2="x" k="20" />
    <hkern u1="&#xfe;" u2="v" k="10" />
    <hkern u1="&#xfe;" u2="\" k="70" />
    <hkern u1="&#xff;" u2="&#x2026;" k="32" />
    <hkern u1="&#xff;" u2="&#x2014;" k="15" />
    <hkern u1="&#xff;" u2="&#x2013;" k="15" />
    <hkern u1="&#xff;" u2="&#x2c7;" k="10" />
    <hkern u1="&#xff;" u2="&#x153;" k="5" />
    <hkern u1="&#xff;" u2="&#xf0;" k="5" />
    <hkern u1="&#xff;" u2="&#xe6;" k="15" />
    <hkern u1="&#xff;" u2="&#xbf;" k="10" />
    <hkern u1="&#xff;" u2="t" k="-15" />
    <hkern u1="&#xff;" u2="s" k="6" />
    <hkern u1="&#xff;" u2="q" k="5" />
    <hkern u1="&#xff;" u2="o" k="5" />
    <hkern u1="&#xff;" u2="g" k="5" />
    <hkern u1="&#xff;" u2="e" k="5" />
    <hkern u1="&#xff;" u2="d" k="5" />
    <hkern u1="&#xff;" u2="c" k="5" />
    <hkern u1="&#xff;" u2="a" k="15" />
    <hkern u1="&#xff;" u2="&#x2e;" k="32" />
    <hkern u1="&#xff;" u2="&#x2d;" k="15" />
    <hkern u1="&#xff;" u2="&#x2c;" k="32" />
    <hkern u1="&#x100;" u2="&#x201d;" k="32" />
    <hkern u1="&#x100;" u2="&#x201c;" k="89" />
    <hkern u1="&#x100;" u2="&#x2019;" k="32" />
    <hkern u1="&#x100;" u2="&#x2018;" k="89" />
    <hkern u1="&#x100;" u2="&#x152;" k="25" />
    <hkern u1="&#x100;" u2="y" k="30" />
    <hkern u1="&#x100;" u2="w" k="25" />
    <hkern u1="&#x100;" u2="v" k="30" />
    <hkern u1="&#x100;" u2="t" k="25" />
    <hkern u1="&#x100;" u2="\" k="80" />
    <hkern u1="&#x100;" u2="Y" k="95" />
    <hkern u1="&#x100;" u2="W" k="40" />
    <hkern u1="&#x100;" u2="V" k="85" />
    <hkern u1="&#x100;" u2="U" k="25" />
    <hkern u1="&#x100;" u2="T" k="80" />
    <hkern u1="&#x100;" u2="Q" k="25" />
    <hkern u1="&#x100;" u2="O" k="25" />
    <hkern u1="&#x100;" u2="G" k="25" />
    <hkern u1="&#x100;" u2="C" k="25" />
    <hkern u1="&#x100;" u2="&#x2a;" k="39" />
    <hkern u1="&#x101;" u2="&#x201c;" k="70" />
    <hkern u1="&#x101;" u2="&#x2018;" k="70" />
    <hkern u1="&#x101;" u2="y" k="20" />
    <hkern u1="&#x101;" u2="v" k="15" />
    <hkern u1="&#x101;" u2="\" k="65" />
    <hkern u1="&#x102;" u2="&#x201d;" k="32" />
    <hkern u1="&#x102;" u2="&#x201c;" k="89" />
    <hkern u1="&#x102;" u2="&#x2019;" k="32" />
    <hkern u1="&#x102;" u2="&#x2018;" k="89" />
    <hkern u1="&#x102;" u2="&#x152;" k="25" />
    <hkern u1="&#x102;" u2="y" k="30" />
    <hkern u1="&#x102;" u2="w" k="25" />
    <hkern u1="&#x102;" u2="v" k="30" />
    <hkern u1="&#x102;" u2="t" k="25" />
    <hkern u1="&#x102;" u2="\" k="80" />
    <hkern u1="&#x102;" u2="Y" k="95" />
    <hkern u1="&#x102;" u2="W" k="40" />
    <hkern u1="&#x102;" u2="V" k="85" />
    <hkern u1="&#x102;" u2="U" k="25" />
    <hkern u1="&#x102;" u2="T" k="80" />
    <hkern u1="&#x102;" u2="Q" k="25" />
    <hkern u1="&#x102;" u2="O" k="25" />
    <hkern u1="&#x102;" u2="G" k="25" />
    <hkern u1="&#x102;" u2="C" k="25" />
    <hkern u1="&#x102;" u2="&#x2a;" k="39" />
    <hkern u1="&#x103;" u2="&#x201c;" k="70" />
    <hkern u1="&#x103;" u2="&#x2018;" k="70" />
    <hkern u1="&#x103;" u2="y" k="20" />
    <hkern u1="&#x103;" u2="v" k="15" />
    <hkern u1="&#x103;" u2="\" k="65" />
    <hkern u1="&#x104;" u2="&#x201d;" k="32" />
    <hkern u1="&#x104;" u2="&#x201c;" k="89" />
    <hkern u1="&#x104;" u2="&#x2019;" k="32" />
    <hkern u1="&#x104;" u2="&#x2018;" k="89" />
    <hkern u1="&#x104;" u2="&#x152;" k="25" />
    <hkern u1="&#x104;" u2="y" k="10" />
    <hkern u1="&#x104;" u2="w" k="25" />
    <hkern u1="&#x104;" u2="v" k="30" />
    <hkern u1="&#x104;" u2="t" k="25" />
    <hkern u1="&#x104;" u2="\" k="80" />
    <hkern u1="&#x104;" u2="Y" k="95" />
    <hkern u1="&#x104;" u2="W" k="40" />
    <hkern u1="&#x104;" u2="V" k="85" />
    <hkern u1="&#x104;" u2="U" k="25" />
    <hkern u1="&#x104;" u2="T" k="80" />
    <hkern u1="&#x104;" u2="Q" k="25" />
    <hkern u1="&#x104;" u2="O" k="25" />
    <hkern u1="&#x104;" u2="G" k="25" />
    <hkern u1="&#x104;" u2="C" k="25" />
    <hkern u1="&#x104;" u2="&#x2a;" k="39" />
    <hkern u1="&#x105;" u2="&#x201c;" k="70" />
    <hkern u1="&#x105;" u2="&#x2018;" k="70" />
    <hkern u1="&#x105;" u2="y" k="20" />
    <hkern u1="&#x105;" u2="v" k="15" />
    <hkern u1="&#x105;" u2="\" k="65" />
    <hkern u1="&#x106;" u2="&#x153;" k="15" />
    <hkern u1="&#x106;" u2="&#x152;" k="20" />
    <hkern u1="&#x106;" u2="&#xf0;" k="15" />
    <hkern u1="&#x106;" u2="y" k="19" />
    <hkern u1="&#x106;" u2="u" k="20" />
    <hkern u1="&#x106;" u2="q" k="10" />
    <hkern u1="&#x106;" u2="o" k="15" />
    <hkern u1="&#x106;" u2="g" k="10" />
    <hkern u1="&#x106;" u2="e" k="15" />
    <hkern u1="&#x106;" u2="d" k="10" />
    <hkern u1="&#x106;" u2="c" k="15" />
    <hkern u1="&#x106;" u2="Q" k="20" />
    <hkern u1="&#x106;" u2="O" k="20" />
    <hkern u1="&#x106;" u2="G" k="20" />
    <hkern u1="&#x106;" u2="C" k="20" />
    <hkern u1="&#x107;" u2="&#x201c;" k="42" />
    <hkern u1="&#x107;" u2="&#x2018;" k="42" />
    <hkern u1="&#x107;" u2="&#x153;" k="6" />
    <hkern u1="&#x107;" u2="&#xf0;" k="6" />
    <hkern u1="&#x107;" u2="&#xe6;" k="-6" />
    <hkern u1="&#x107;" u2="q" k="6" />
    <hkern u1="&#x107;" u2="o" k="6" />
    <hkern u1="&#x107;" u2="g" k="6" />
    <hkern u1="&#x107;" u2="e" k="6" />
    <hkern u1="&#x107;" u2="d" k="6" />
    <hkern u1="&#x107;" u2="c" k="6" />
    <hkern u1="&#x107;" u2="a" k="-6" />
    <hkern u1="&#x107;" u2="\" k="61" />
    <hkern u1="&#x108;" u2="&#x153;" k="15" />
    <hkern u1="&#x108;" u2="&#x152;" k="20" />
    <hkern u1="&#x108;" u2="&#xf0;" k="15" />
    <hkern u1="&#x108;" u2="y" k="19" />
    <hkern u1="&#x108;" u2="u" k="20" />
    <hkern u1="&#x108;" u2="q" k="10" />
    <hkern u1="&#x108;" u2="o" k="15" />
    <hkern u1="&#x108;" u2="g" k="10" />
    <hkern u1="&#x108;" u2="e" k="15" />
    <hkern u1="&#x108;" u2="d" k="10" />
    <hkern u1="&#x108;" u2="c" k="15" />
    <hkern u1="&#x108;" u2="Q" k="20" />
    <hkern u1="&#x108;" u2="O" k="20" />
    <hkern u1="&#x108;" u2="G" k="20" />
    <hkern u1="&#x108;" u2="C" k="20" />
    <hkern u1="&#x109;" u2="&#x201c;" k="42" />
    <hkern u1="&#x109;" u2="&#x2018;" k="42" />
    <hkern u1="&#x109;" u2="&#x153;" k="6" />
    <hkern u1="&#x109;" u2="&#xf0;" k="6" />
    <hkern u1="&#x109;" u2="&#xe6;" k="-6" />
    <hkern u1="&#x109;" u2="q" k="6" />
    <hkern u1="&#x109;" u2="o" k="6" />
    <hkern u1="&#x109;" u2="g" k="6" />
    <hkern u1="&#x109;" u2="e" k="6" />
    <hkern u1="&#x109;" u2="d" k="6" />
    <hkern u1="&#x109;" u2="c" k="6" />
    <hkern u1="&#x109;" u2="a" k="-6" />
    <hkern u1="&#x109;" u2="\" k="61" />
    <hkern u1="&#x10a;" u2="&#x153;" k="15" />
    <hkern u1="&#x10a;" u2="&#x152;" k="20" />
    <hkern u1="&#x10a;" u2="&#xf0;" k="15" />
    <hkern u1="&#x10a;" u2="y" k="19" />
    <hkern u1="&#x10a;" u2="u" k="20" />
    <hkern u1="&#x10a;" u2="q" k="10" />
    <hkern u1="&#x10a;" u2="o" k="15" />
    <hkern u1="&#x10a;" u2="g" k="10" />
    <hkern u1="&#x10a;" u2="e" k="15" />
    <hkern u1="&#x10a;" u2="d" k="10" />
    <hkern u1="&#x10a;" u2="c" k="15" />
    <hkern u1="&#x10a;" u2="Q" k="20" />
    <hkern u1="&#x10a;" u2="O" k="20" />
    <hkern u1="&#x10a;" u2="G" k="20" />
    <hkern u1="&#x10a;" u2="C" k="20" />
    <hkern u1="&#x10b;" u2="&#x201c;" k="42" />
    <hkern u1="&#x10b;" u2="&#x2018;" k="42" />
    <hkern u1="&#x10b;" u2="&#x153;" k="6" />
    <hkern u1="&#x10b;" u2="&#xf0;" k="6" />
    <hkern u1="&#x10b;" u2="&#xe6;" k="-6" />
    <hkern u1="&#x10b;" u2="q" k="6" />
    <hkern u1="&#x10b;" u2="o" k="6" />
    <hkern u1="&#x10b;" u2="g" k="6" />
    <hkern u1="&#x10b;" u2="e" k="6" />
    <hkern u1="&#x10b;" u2="d" k="6" />
    <hkern u1="&#x10b;" u2="c" k="6" />
    <hkern u1="&#x10b;" u2="a" k="-6" />
    <hkern u1="&#x10b;" u2="\" k="61" />
    <hkern u1="&#x10c;" u2="&#x153;" k="15" />
    <hkern u1="&#x10c;" u2="&#x152;" k="20" />
    <hkern u1="&#x10c;" u2="&#xf0;" k="15" />
    <hkern u1="&#x10c;" u2="y" k="19" />
    <hkern u1="&#x10c;" u2="u" k="20" />
    <hkern u1="&#x10c;" u2="q" k="10" />
    <hkern u1="&#x10c;" u2="o" k="15" />
    <hkern u1="&#x10c;" u2="g" k="10" />
    <hkern u1="&#x10c;" u2="e" k="15" />
    <hkern u1="&#x10c;" u2="d" k="10" />
    <hkern u1="&#x10c;" u2="c" k="15" />
    <hkern u1="&#x10c;" u2="Q" k="20" />
    <hkern u1="&#x10c;" u2="O" k="20" />
    <hkern u1="&#x10c;" u2="G" k="20" />
    <hkern u1="&#x10c;" u2="C" k="20" />
    <hkern u1="&#x10d;" u2="&#x201c;" k="42" />
    <hkern u1="&#x10d;" u2="&#x2018;" k="42" />
    <hkern u1="&#x10d;" u2="&#x153;" k="6" />
    <hkern u1="&#x10d;" u2="&#xf0;" k="6" />
    <hkern u1="&#x10d;" u2="&#xe6;" k="-6" />
    <hkern u1="&#x10d;" u2="q" k="6" />
    <hkern u1="&#x10d;" u2="o" k="6" />
    <hkern u1="&#x10d;" u2="g" k="6" />
    <hkern u1="&#x10d;" u2="e" k="6" />
    <hkern u1="&#x10d;" u2="d" k="6" />
    <hkern u1="&#x10d;" u2="c" k="6" />
    <hkern u1="&#x10d;" u2="a" k="-6" />
    <hkern u1="&#x10d;" u2="\" k="61" />
    <hkern u1="&#x10e;" u2="&#xc6;" k="52" />
    <hkern u1="&#x10e;" u2="&#xb8;" k="20" />
    <hkern u1="&#x10e;" u2="z" k="10" />
    <hkern u1="&#x10e;" u2="Z" k="30" />
    <hkern u1="&#x10e;" u2="Y" k="40" />
    <hkern u1="&#x10e;" u2="X" k="25" />
    <hkern u1="&#x10e;" u2="V" k="15" />
    <hkern u1="&#x10e;" u2="T" k="30" />
    <hkern u1="&#x10e;" u2="S" k="10" />
    <hkern u1="&#x10e;" u2="J" k="45" />
    <hkern u1="&#x10e;" u2="A" k="25" />
    <hkern u1="&#x110;" u2="&#xc6;" k="52" />
    <hkern u1="&#x110;" u2="&#xb8;" k="20" />
    <hkern u1="&#x110;" u2="z" k="10" />
    <hkern u1="&#x110;" u2="Z" k="30" />
    <hkern u1="&#x110;" u2="Y" k="40" />
    <hkern u1="&#x110;" u2="X" k="25" />
    <hkern u1="&#x110;" u2="V" k="15" />
    <hkern u1="&#x110;" u2="T" k="30" />
    <hkern u1="&#x110;" u2="S" k="10" />
    <hkern u1="&#x110;" u2="J" k="45" />
    <hkern u1="&#x110;" u2="A" k="25" />
    <hkern u1="&#x112;" u2="&#x153;" k="20" />
    <hkern u1="&#x112;" u2="&#x152;" k="20" />
    <hkern u1="&#x112;" u2="&#xf0;" k="20" />
    <hkern u1="&#x112;" u2="y" k="20" />
    <hkern u1="&#x112;" u2="w" k="20" />
    <hkern u1="&#x112;" u2="v" k="25" />
    <hkern u1="&#x112;" u2="u" k="15" />
    <hkern u1="&#x112;" u2="t" k="20" />
    <hkern u1="&#x112;" u2="q" k="20" />
    <hkern u1="&#x112;" u2="o" k="20" />
    <hkern u1="&#x112;" u2="g" k="20" />
    <hkern u1="&#x112;" u2="e" k="20" />
    <hkern u1="&#x112;" u2="d" k="20" />
    <hkern u1="&#x112;" u2="c" k="20" />
    <hkern u1="&#x112;" u2="Q" k="20" />
    <hkern u1="&#x112;" u2="O" k="20" />
    <hkern u1="&#x112;" u2="G" k="20" />
    <hkern u1="&#x112;" u2="C" k="20" />
    <hkern u1="&#x113;" u2="y" k="10" />
    <hkern u1="&#x113;" u2="x" k="7" />
    <hkern u1="&#x113;" u2="v" k="5" />
    <hkern u1="&#x113;" u2="\" k="61" />
    <hkern u1="&#x114;" u2="&#x153;" k="20" />
    <hkern u1="&#x114;" u2="&#x152;" k="20" />
    <hkern u1="&#x114;" u2="&#xf0;" k="20" />
    <hkern u1="&#x114;" u2="y" k="20" />
    <hkern u1="&#x114;" u2="w" k="20" />
    <hkern u1="&#x114;" u2="v" k="25" />
    <hkern u1="&#x114;" u2="u" k="15" />
    <hkern u1="&#x114;" u2="t" k="20" />
    <hkern u1="&#x114;" u2="q" k="20" />
    <hkern u1="&#x114;" u2="o" k="20" />
    <hkern u1="&#x114;" u2="g" k="20" />
    <hkern u1="&#x114;" u2="e" k="20" />
    <hkern u1="&#x114;" u2="d" k="20" />
    <hkern u1="&#x114;" u2="c" k="20" />
    <hkern u1="&#x114;" u2="Q" k="20" />
    <hkern u1="&#x114;" u2="O" k="20" />
    <hkern u1="&#x114;" u2="G" k="20" />
    <hkern u1="&#x114;" u2="C" k="20" />
    <hkern u1="&#x115;" u2="y" k="10" />
    <hkern u1="&#x115;" u2="x" k="7" />
    <hkern u1="&#x115;" u2="v" k="5" />
    <hkern u1="&#x115;" u2="\" k="61" />
    <hkern u1="&#x116;" u2="&#x153;" k="20" />
    <hkern u1="&#x116;" u2="&#x152;" k="20" />
    <hkern u1="&#x116;" u2="&#xf0;" k="20" />
    <hkern u1="&#x116;" u2="y" k="20" />
    <hkern u1="&#x116;" u2="w" k="20" />
    <hkern u1="&#x116;" u2="v" k="25" />
    <hkern u1="&#x116;" u2="u" k="15" />
    <hkern u1="&#x116;" u2="t" k="20" />
    <hkern u1="&#x116;" u2="q" k="20" />
    <hkern u1="&#x116;" u2="o" k="20" />
    <hkern u1="&#x116;" u2="g" k="20" />
    <hkern u1="&#x116;" u2="e" k="20" />
    <hkern u1="&#x116;" u2="d" k="20" />
    <hkern u1="&#x116;" u2="c" k="20" />
    <hkern u1="&#x116;" u2="Q" k="20" />
    <hkern u1="&#x116;" u2="O" k="20" />
    <hkern u1="&#x116;" u2="G" k="20" />
    <hkern u1="&#x116;" u2="C" k="20" />
    <hkern u1="&#x117;" u2="y" k="10" />
    <hkern u1="&#x117;" u2="x" k="7" />
    <hkern u1="&#x117;" u2="v" k="5" />
    <hkern u1="&#x117;" u2="\" k="61" />
    <hkern u1="&#x118;" u2="&#x153;" k="20" />
    <hkern u1="&#x118;" u2="&#x152;" k="20" />
    <hkern u1="&#x118;" u2="&#xf0;" k="20" />
    <hkern u1="&#x118;" u2="y" k="20" />
    <hkern u1="&#x118;" u2="w" k="20" />
    <hkern u1="&#x118;" u2="v" k="25" />
    <hkern u1="&#x118;" u2="u" k="15" />
    <hkern u1="&#x118;" u2="t" k="20" />
    <hkern u1="&#x118;" u2="q" k="20" />
    <hkern u1="&#x118;" u2="o" k="20" />
    <hkern u1="&#x118;" u2="g" k="20" />
    <hkern u1="&#x118;" u2="e" k="20" />
    <hkern u1="&#x118;" u2="d" k="20" />
    <hkern u1="&#x118;" u2="c" k="20" />
    <hkern u1="&#x118;" u2="Q" k="20" />
    <hkern u1="&#x118;" u2="O" k="20" />
    <hkern u1="&#x118;" u2="G" k="20" />
    <hkern u1="&#x118;" u2="C" k="20" />
    <hkern u1="&#x119;" u2="y" k="10" />
    <hkern u1="&#x119;" u2="x" k="7" />
    <hkern u1="&#x119;" u2="v" k="5" />
    <hkern u1="&#x119;" u2="\" k="61" />
    <hkern u1="&#x11a;" u2="&#x153;" k="20" />
    <hkern u1="&#x11a;" u2="&#x152;" k="20" />
    <hkern u1="&#x11a;" u2="&#xf0;" k="20" />
    <hkern u1="&#x11a;" u2="y" k="20" />
    <hkern u1="&#x11a;" u2="w" k="20" />
    <hkern u1="&#x11a;" u2="v" k="25" />
    <hkern u1="&#x11a;" u2="u" k="15" />
    <hkern u1="&#x11a;" u2="t" k="20" />
    <hkern u1="&#x11a;" u2="q" k="20" />
    <hkern u1="&#x11a;" u2="o" k="20" />
    <hkern u1="&#x11a;" u2="g" k="20" />
    <hkern u1="&#x11a;" u2="e" k="20" />
    <hkern u1="&#x11a;" u2="d" k="20" />
    <hkern u1="&#x11a;" u2="c" k="20" />
    <hkern u1="&#x11a;" u2="Q" k="20" />
    <hkern u1="&#x11a;" u2="O" k="20" />
    <hkern u1="&#x11a;" u2="G" k="20" />
    <hkern u1="&#x11a;" u2="C" k="20" />
    <hkern u1="&#x11b;" u2="y" k="10" />
    <hkern u1="&#x11b;" u2="x" k="7" />
    <hkern u1="&#x11b;" u2="v" k="5" />
    <hkern u1="&#x11b;" u2="\" k="61" />
    <hkern u1="&#x11c;" u2="Y" k="25" />
    <hkern u1="&#x11c;" u2="V" k="10" />
    <hkern u1="&#x11d;" u2="&#x201c;" k="33" />
    <hkern u1="&#x11d;" u2="&#x2018;" k="33" />
    <hkern u1="&#x11d;" u2="\" k="66" />
    <hkern u1="&#x11e;" u2="Y" k="25" />
    <hkern u1="&#x11e;" u2="V" k="10" />
    <hkern u1="&#x11f;" u2="&#x201c;" k="33" />
    <hkern u1="&#x11f;" u2="&#x2018;" k="33" />
    <hkern u1="&#x11f;" u2="\" k="66" />
    <hkern u1="&#x120;" u2="Y" k="25" />
    <hkern u1="&#x120;" u2="V" k="10" />
    <hkern u1="&#x121;" u2="&#x201c;" k="33" />
    <hkern u1="&#x121;" u2="&#x2018;" k="33" />
    <hkern u1="&#x121;" u2="\" k="66" />
    <hkern u1="&#x122;" u2="Y" k="25" />
    <hkern u1="&#x122;" u2="V" k="10" />
    <hkern u1="&#x123;" u2="&#x201c;" k="33" />
    <hkern u1="&#x123;" u2="&#x2018;" k="33" />
    <hkern u1="&#x123;" u2="\" k="66" />
    <hkern u1="&#x125;" u2="&#x201c;" k="60" />
    <hkern u1="&#x125;" u2="&#x2018;" k="60" />
    <hkern u1="&#x125;" u2="y" k="20" />
    <hkern u1="&#x125;" u2="w" k="10" />
    <hkern u1="&#x125;" u2="v" k="20" />
    <hkern u1="&#x125;" u2="\" k="70" />
    <hkern u1="&#x127;" u2="&#x201c;" k="60" />
    <hkern u1="&#x127;" u2="&#x2018;" k="60" />
    <hkern u1="&#x127;" u2="y" k="20" />
    <hkern u1="&#x127;" u2="w" k="10" />
    <hkern u1="&#x127;" u2="v" k="20" />
    <hkern u1="&#x127;" u2="\" k="70" />
    <hkern u1="&#x134;" u2="&#x153;" k="10" />
    <hkern u1="&#x134;" u2="&#xf0;" k="10" />
    <hkern u1="&#x134;" u2="q" k="10" />
    <hkern u1="&#x134;" u2="o" k="10" />
    <hkern u1="&#x134;" u2="g" k="10" />
    <hkern u1="&#x134;" u2="e" k="10" />
    <hkern u1="&#x134;" u2="d" k="10" />
    <hkern u1="&#x134;" u2="c" k="10" />
    <hkern u1="&#x134;" u2="A" k="15" />
    <hkern u1="&#x136;" u2="&#x2039;" k="43" />
    <hkern u1="&#x136;" u2="&#x153;" k="25" />
    <hkern u1="&#x136;" u2="&#x152;" k="30" />
    <hkern u1="&#x136;" u2="&#xf0;" k="25" />
    <hkern u1="&#x136;" u2="&#xbf;" k="15" />
    <hkern u1="&#x136;" u2="&#xab;" k="43" />
    <hkern u1="&#x136;" u2="y" k="40" />
    <hkern u1="&#x136;" u2="w" k="35" />
    <hkern u1="&#x136;" u2="v" k="35" />
    <hkern u1="&#x136;" u2="u" k="25" />
    <hkern u1="&#x136;" u2="t" k="25" />
    <hkern u1="&#x136;" u2="q" k="25" />
    <hkern u1="&#x136;" u2="o" k="25" />
    <hkern u1="&#x136;" u2="g" k="25" />
    <hkern u1="&#x136;" u2="e" k="25" />
    <hkern u1="&#x136;" u2="d" k="25" />
    <hkern u1="&#x136;" u2="c" k="25" />
    <hkern u1="&#x136;" u2="U" k="15" />
    <hkern u1="&#x136;" u2="Q" k="30" />
    <hkern u1="&#x136;" u2="O" k="30" />
    <hkern u1="&#x136;" u2="G" k="30" />
    <hkern u1="&#x136;" u2="C" k="30" />
    <hkern u1="&#x137;" u2="&#x2c7;" k="25" />
    <hkern u1="&#x137;" u2="&#x153;" k="12" />
    <hkern u1="&#x137;" u2="&#xf0;" k="12" />
    <hkern u1="&#x137;" u2="&#xe6;" k="2" />
    <hkern u1="&#x137;" u2="&#xbf;" k="25" />
    <hkern u1="&#x137;" u2="u" k="10" />
    <hkern u1="&#x137;" u2="q" k="12" />
    <hkern u1="&#x137;" u2="o" k="12" />
    <hkern u1="&#x137;" u2="l" k="10" />
    <hkern u1="&#x137;" u2="g" k="12" />
    <hkern u1="&#x137;" u2="e" k="12" />
    <hkern u1="&#x137;" u2="d" k="12" />
    <hkern u1="&#x137;" u2="c" k="12" />
    <hkern u1="&#x137;" u2="\" k="51" />
    <hkern u1="&#x137;" u2="&#x40;" k="4" />
    <hkern u1="&#x139;" u2="&#x201d;" k="32" />
    <hkern u1="&#x139;" u2="&#x2019;" k="32" />
    <hkern u1="&#x139;" u2="&#x153;" k="10" />
    <hkern u1="&#x139;" u2="&#x152;" k="10" />
    <hkern u1="&#x139;" u2="&#xf0;" k="10" />
    <hkern u1="&#x139;" u2="y" k="27" />
    <hkern u1="&#x139;" u2="w" k="33" />
    <hkern u1="&#x139;" u2="v" k="56" />
    <hkern u1="&#x139;" u2="u" k="20" />
    <hkern u1="&#x139;" u2="t" k="30" />
    <hkern u1="&#x139;" u2="q" k="20" />
    <hkern u1="&#x139;" u2="o" k="10" />
    <hkern u1="&#x139;" u2="g" k="20" />
    <hkern u1="&#x139;" u2="e" k="10" />
    <hkern u1="&#x139;" u2="d" k="20" />
    <hkern u1="&#x139;" u2="c" k="10" />
    <hkern u1="&#x139;" u2="\" k="113" />
    <hkern u1="&#x139;" u2="Y" k="92" />
    <hkern u1="&#x139;" u2="W" k="25" />
    <hkern u1="&#x139;" u2="V" k="80" />
    <hkern u1="&#x139;" u2="U" k="7" />
    <hkern u1="&#x139;" u2="T" k="83" />
    <hkern u1="&#x139;" u2="Q" k="10" />
    <hkern u1="&#x139;" u2="O" k="10" />
    <hkern u1="&#x139;" u2="G" k="10" />
    <hkern u1="&#x139;" u2="C" k="10" />
    <hkern u1="&#x139;" u2="&#x2a;" k="23" />
    <hkern u1="&#x13a;" u2="&#x153;" k="10" />
    <hkern u1="&#x13a;" u2="&#xf0;" k="10" />
    <hkern u1="&#x13a;" u2="y" k="35" />
    <hkern u1="&#x13a;" u2="w" k="23" />
    <hkern u1="&#x13a;" u2="v" k="32" />
    <hkern u1="&#x13a;" u2="u" k="15" />
    <hkern u1="&#x13a;" u2="t" k="25" />
    <hkern u1="&#x13a;" u2="q" k="10" />
    <hkern u1="&#x13a;" u2="o" k="10" />
    <hkern u1="&#x13a;" u2="l" k="10" />
    <hkern u1="&#x13a;" u2="g" k="10" />
    <hkern u1="&#x13a;" u2="e" k="10" />
    <hkern u1="&#x13a;" u2="d" k="10" />
    <hkern u1="&#x13a;" u2="c" k="10" />
    <hkern u1="&#x13b;" u2="&#x201d;" k="32" />
    <hkern u1="&#x13b;" u2="&#x2019;" k="32" />
    <hkern u1="&#x13b;" u2="&#x153;" k="10" />
    <hkern u1="&#x13b;" u2="&#x152;" k="10" />
    <hkern u1="&#x13b;" u2="&#xf0;" k="10" />
    <hkern u1="&#x13b;" u2="y" k="27" />
    <hkern u1="&#x13b;" u2="w" k="33" />
    <hkern u1="&#x13b;" u2="v" k="56" />
    <hkern u1="&#x13b;" u2="u" k="20" />
    <hkern u1="&#x13b;" u2="t" k="30" />
    <hkern u1="&#x13b;" u2="q" k="20" />
    <hkern u1="&#x13b;" u2="o" k="10" />
    <hkern u1="&#x13b;" u2="g" k="20" />
    <hkern u1="&#x13b;" u2="e" k="10" />
    <hkern u1="&#x13b;" u2="d" k="20" />
    <hkern u1="&#x13b;" u2="c" k="10" />
    <hkern u1="&#x13b;" u2="\" k="113" />
    <hkern u1="&#x13b;" u2="Y" k="92" />
    <hkern u1="&#x13b;" u2="W" k="25" />
    <hkern u1="&#x13b;" u2="V" k="80" />
    <hkern u1="&#x13b;" u2="U" k="7" />
    <hkern u1="&#x13b;" u2="T" k="83" />
    <hkern u1="&#x13b;" u2="Q" k="10" />
    <hkern u1="&#x13b;" u2="O" k="10" />
    <hkern u1="&#x13b;" u2="G" k="10" />
    <hkern u1="&#x13b;" u2="C" k="10" />
    <hkern u1="&#x13b;" u2="&#x2a;" k="23" />
    <hkern u1="&#x13c;" u2="&#x153;" k="10" />
    <hkern u1="&#x13c;" u2="&#xf0;" k="10" />
    <hkern u1="&#x13c;" u2="y" k="35" />
    <hkern u1="&#x13c;" u2="w" k="23" />
    <hkern u1="&#x13c;" u2="v" k="32" />
    <hkern u1="&#x13c;" u2="u" k="15" />
    <hkern u1="&#x13c;" u2="t" k="25" />
    <hkern u1="&#x13c;" u2="q" k="10" />
    <hkern u1="&#x13c;" u2="o" k="10" />
    <hkern u1="&#x13c;" u2="l" k="10" />
    <hkern u1="&#x13c;" u2="g" k="10" />
    <hkern u1="&#x13c;" u2="e" k="10" />
    <hkern u1="&#x13c;" u2="d" k="10" />
    <hkern u1="&#x13c;" u2="c" k="10" />
    <hkern u1="&#x13d;" u2="&#x201d;" k="32" />
    <hkern u1="&#x13d;" u2="&#x2019;" k="32" />
    <hkern u1="&#x13d;" u2="&#x153;" k="10" />
    <hkern u1="&#x13d;" u2="&#x152;" k="10" />
    <hkern u1="&#x13d;" u2="&#xf0;" k="10" />
    <hkern u1="&#x13d;" u2="y" k="27" />
    <hkern u1="&#x13d;" u2="w" k="33" />
    <hkern u1="&#x13d;" u2="v" k="56" />
    <hkern u1="&#x13d;" u2="u" k="20" />
    <hkern u1="&#x13d;" u2="t" k="30" />
    <hkern u1="&#x13d;" u2="q" k="20" />
    <hkern u1="&#x13d;" u2="o" k="10" />
    <hkern u1="&#x13d;" u2="g" k="20" />
    <hkern u1="&#x13d;" u2="e" k="10" />
    <hkern u1="&#x13d;" u2="d" k="20" />
    <hkern u1="&#x13d;" u2="c" k="10" />
    <hkern u1="&#x13d;" u2="\" k="113" />
    <hkern u1="&#x13d;" u2="Y" k="92" />
    <hkern u1="&#x13d;" u2="W" k="25" />
    <hkern u1="&#x13d;" u2="V" k="80" />
    <hkern u1="&#x13d;" u2="U" k="7" />
    <hkern u1="&#x13d;" u2="T" k="83" />
    <hkern u1="&#x13d;" u2="Q" k="10" />
    <hkern u1="&#x13d;" u2="O" k="10" />
    <hkern u1="&#x13d;" u2="G" k="10" />
    <hkern u1="&#x13d;" u2="C" k="10" />
    <hkern u1="&#x13d;" u2="&#x2a;" k="23" />
    <hkern u1="&#x13e;" u2="&#x153;" k="10" />
    <hkern u1="&#x13e;" u2="&#xf0;" k="10" />
    <hkern u1="&#x13e;" u2="y" k="35" />
    <hkern u1="&#x13e;" u2="w" k="23" />
    <hkern u1="&#x13e;" u2="v" k="32" />
    <hkern u1="&#x13e;" u2="u" k="15" />
    <hkern u1="&#x13e;" u2="t" k="25" />
    <hkern u1="&#x13e;" u2="q" k="10" />
    <hkern u1="&#x13e;" u2="o" k="10" />
    <hkern u1="&#x13e;" u2="l" k="10" />
    <hkern u1="&#x13e;" u2="g" k="10" />
    <hkern u1="&#x13e;" u2="e" k="10" />
    <hkern u1="&#x13e;" u2="d" k="10" />
    <hkern u1="&#x13e;" u2="c" k="10" />
    <hkern u1="&#x13f;" u2="&#x201d;" k="32" />
    <hkern u1="&#x13f;" u2="&#x2019;" k="32" />
    <hkern u1="&#x13f;" u2="&#x153;" k="10" />
    <hkern u1="&#x13f;" u2="&#x152;" k="10" />
    <hkern u1="&#x13f;" u2="&#xf0;" k="10" />
    <hkern u1="&#x13f;" u2="y" k="27" />
    <hkern u1="&#x13f;" u2="w" k="33" />
    <hkern u1="&#x13f;" u2="v" k="56" />
    <hkern u1="&#x13f;" u2="u" k="20" />
    <hkern u1="&#x13f;" u2="t" k="30" />
    <hkern u1="&#x13f;" u2="q" k="20" />
    <hkern u1="&#x13f;" u2="o" k="10" />
    <hkern u1="&#x13f;" u2="g" k="20" />
    <hkern u1="&#x13f;" u2="e" k="10" />
    <hkern u1="&#x13f;" u2="d" k="20" />
    <hkern u1="&#x13f;" u2="c" k="10" />
    <hkern u1="&#x13f;" u2="\" k="113" />
    <hkern u1="&#x13f;" u2="Y" k="92" />
    <hkern u1="&#x13f;" u2="W" k="25" />
    <hkern u1="&#x13f;" u2="V" k="80" />
    <hkern u1="&#x13f;" u2="U" k="7" />
    <hkern u1="&#x13f;" u2="T" k="83" />
    <hkern u1="&#x13f;" u2="Q" k="10" />
    <hkern u1="&#x13f;" u2="O" k="10" />
    <hkern u1="&#x13f;" u2="G" k="10" />
    <hkern u1="&#x13f;" u2="C" k="10" />
    <hkern u1="&#x13f;" u2="&#x2a;" k="23" />
    <hkern u1="&#x140;" u2="&#x153;" k="10" />
    <hkern u1="&#x140;" u2="&#xf0;" k="10" />
    <hkern u1="&#x140;" u2="y" k="35" />
    <hkern u1="&#x140;" u2="w" k="23" />
    <hkern u1="&#x140;" u2="v" k="32" />
    <hkern u1="&#x140;" u2="u" k="15" />
    <hkern u1="&#x140;" u2="t" k="25" />
    <hkern u1="&#x140;" u2="q" k="10" />
    <hkern u1="&#x140;" u2="o" k="10" />
    <hkern u1="&#x140;" u2="l" k="10" />
    <hkern u1="&#x140;" u2="g" k="10" />
    <hkern u1="&#x140;" u2="e" k="10" />
    <hkern u1="&#x140;" u2="d" k="10" />
    <hkern u1="&#x140;" u2="c" k="10" />
    <hkern u1="&#x141;" u2="&#x201d;" k="32" />
    <hkern u1="&#x141;" u2="&#x2019;" k="32" />
    <hkern u1="&#x141;" u2="&#x152;" k="10" />
    <hkern u1="&#x141;" u2="\" k="113" />
    <hkern u1="&#x141;" u2="Y" k="92" />
    <hkern u1="&#x141;" u2="W" k="25" />
    <hkern u1="&#x141;" u2="V" k="80" />
    <hkern u1="&#x141;" u2="U" k="7" />
    <hkern u1="&#x141;" u2="T" k="83" />
    <hkern u1="&#x141;" u2="Q" k="10" />
    <hkern u1="&#x141;" u2="O" k="10" />
    <hkern u1="&#x141;" u2="G" k="10" />
    <hkern u1="&#x141;" u2="C" k="10" />
    <hkern u1="&#x141;" u2="&#x2a;" k="23" />
    <hkern u1="&#x142;" u2="l" k="10" />
    <hkern u1="&#x144;" u2="&#x201c;" k="60" />
    <hkern u1="&#x144;" u2="&#x2018;" k="60" />
    <hkern u1="&#x144;" u2="y" k="20" />
    <hkern u1="&#x144;" u2="w" k="10" />
    <hkern u1="&#x144;" u2="v" k="20" />
    <hkern u1="&#x144;" u2="\" k="70" />
    <hkern u1="&#x146;" u2="&#x201c;" k="60" />
    <hkern u1="&#x146;" u2="&#x2018;" k="60" />
    <hkern u1="&#x146;" u2="y" k="20" />
    <hkern u1="&#x146;" u2="w" k="10" />
    <hkern u1="&#x146;" u2="v" k="20" />
    <hkern u1="&#x146;" u2="\" k="70" />
    <hkern u1="&#x148;" u2="&#x201c;" k="60" />
    <hkern u1="&#x148;" u2="&#x2018;" k="60" />
    <hkern u1="&#x148;" u2="y" k="20" />
    <hkern u1="&#x148;" u2="w" k="10" />
    <hkern u1="&#x148;" u2="v" k="20" />
    <hkern u1="&#x148;" u2="\" k="70" />
    <hkern u1="&#x14b;" u2="&#x201c;" k="60" />
    <hkern u1="&#x14b;" u2="&#x2018;" k="60" />
    <hkern u1="&#x14b;" u2="\" k="70" />
    <hkern u1="&#x14c;" u2="&#xe6;" k="10" />
    <hkern u1="&#x14c;" u2="&#xc6;" k="38" />
    <hkern u1="&#x14c;" u2="&#xb8;" k="25" />
    <hkern u1="&#x14c;" u2="z" k="20" />
    <hkern u1="&#x14c;" u2="s" k="15" />
    <hkern u1="&#x14c;" u2="a" k="10" />
    <hkern u1="&#x14c;" u2="Z" k="30" />
    <hkern u1="&#x14c;" u2="Y" k="55" />
    <hkern u1="&#x14c;" u2="X" k="35" />
    <hkern u1="&#x14c;" u2="V" k="30" />
    <hkern u1="&#x14c;" u2="T" k="30" />
    <hkern u1="&#x14c;" u2="S" k="15" />
    <hkern u1="&#x14c;" u2="J" k="41" />
    <hkern u1="&#x14c;" u2="A" k="25" />
    <hkern u1="&#x14d;" u2="&#x201c;" k="66" />
    <hkern u1="&#x14d;" u2="&#x2018;" k="66" />
    <hkern u1="&#x14d;" u2="z" k="25" />
    <hkern u1="&#x14d;" u2="y" k="15" />
    <hkern u1="&#x14d;" u2="x" k="20" />
    <hkern u1="&#x14d;" u2="w" k="2" />
    <hkern u1="&#x14d;" u2="v" k="10" />
    <hkern u1="&#x14d;" u2="\" k="94" />
    <hkern u1="&#x14e;" u2="&#xe6;" k="10" />
    <hkern u1="&#x14e;" u2="&#xc6;" k="38" />
    <hkern u1="&#x14e;" u2="&#xb8;" k="25" />
    <hkern u1="&#x14e;" u2="z" k="20" />
    <hkern u1="&#x14e;" u2="s" k="15" />
    <hkern u1="&#x14e;" u2="a" k="10" />
    <hkern u1="&#x14e;" u2="Z" k="30" />
    <hkern u1="&#x14e;" u2="Y" k="55" />
    <hkern u1="&#x14e;" u2="X" k="35" />
    <hkern u1="&#x14e;" u2="V" k="30" />
    <hkern u1="&#x14e;" u2="T" k="30" />
    <hkern u1="&#x14e;" u2="S" k="15" />
    <hkern u1="&#x14e;" u2="J" k="41" />
    <hkern u1="&#x14e;" u2="A" k="25" />
    <hkern u1="&#x14f;" u2="&#x201c;" k="66" />
    <hkern u1="&#x14f;" u2="&#x2018;" k="66" />
    <hkern u1="&#x14f;" u2="z" k="25" />
    <hkern u1="&#x14f;" u2="y" k="15" />
    <hkern u1="&#x14f;" u2="x" k="20" />
    <hkern u1="&#x14f;" u2="w" k="2" />
    <hkern u1="&#x14f;" u2="v" k="10" />
    <hkern u1="&#x14f;" u2="\" k="94" />
    <hkern u1="&#x150;" u2="&#xe6;" k="10" />
    <hkern u1="&#x150;" u2="&#xc6;" k="38" />
    <hkern u1="&#x150;" u2="&#xb8;" k="25" />
    <hkern u1="&#x150;" u2="z" k="20" />
    <hkern u1="&#x150;" u2="s" k="15" />
    <hkern u1="&#x150;" u2="a" k="10" />
    <hkern u1="&#x150;" u2="Z" k="30" />
    <hkern u1="&#x150;" u2="Y" k="55" />
    <hkern u1="&#x150;" u2="X" k="35" />
    <hkern u1="&#x150;" u2="V" k="30" />
    <hkern u1="&#x150;" u2="T" k="30" />
    <hkern u1="&#x150;" u2="S" k="15" />
    <hkern u1="&#x150;" u2="J" k="41" />
    <hkern u1="&#x150;" u2="A" k="25" />
    <hkern u1="&#x151;" u2="&#x201c;" k="66" />
    <hkern u1="&#x151;" u2="&#x2018;" k="66" />
    <hkern u1="&#x151;" u2="z" k="25" />
    <hkern u1="&#x151;" u2="y" k="15" />
    <hkern u1="&#x151;" u2="x" k="20" />
    <hkern u1="&#x151;" u2="w" k="2" />
    <hkern u1="&#x151;" u2="v" k="10" />
    <hkern u1="&#x151;" u2="\" k="44" />
    <hkern u1="&#x152;" u2="&#x177;" k="20" />
    <hkern u1="&#x152;" u2="&#x175;" k="20" />
    <hkern u1="&#x152;" u2="&#x173;" k="15" />
    <hkern u1="&#x152;" u2="&#x171;" k="15" />
    <hkern u1="&#x152;" u2="&#x16f;" k="15" />
    <hkern u1="&#x152;" u2="&#x16d;" k="15" />
    <hkern u1="&#x152;" u2="&#x16b;" k="15" />
    <hkern u1="&#x152;" u2="&#x169;" k="15" />
    <hkern u1="&#x152;" u2="&#x167;" k="20" />
    <hkern u1="&#x152;" u2="&#x165;" k="20" />
    <hkern u1="&#x152;" u2="&#x163;" k="20" />
    <hkern u1="&#x152;" u2="&#x153;" k="20" />
    <hkern u1="&#x152;" u2="&#x152;" k="20" />
    <hkern u1="&#x152;" u2="&#x151;" k="20" />
    <hkern u1="&#x152;" u2="&#x150;" k="20" />
    <hkern u1="&#x152;" u2="&#x14f;" k="20" />
    <hkern u1="&#x152;" u2="&#x14e;" k="20" />
    <hkern u1="&#x152;" u2="&#x14d;" k="20" />
    <hkern u1="&#x152;" u2="&#x14c;" k="20" />
    <hkern u1="&#x152;" u2="&#x123;" k="20" />
    <hkern u1="&#x152;" u2="&#x122;" k="20" />
    <hkern u1="&#x152;" u2="&#x121;" k="20" />
    <hkern u1="&#x152;" u2="&#x120;" k="20" />
    <hkern u1="&#x152;" u2="&#x11f;" k="20" />
    <hkern u1="&#x152;" u2="&#x11e;" k="20" />
    <hkern u1="&#x152;" u2="&#x11d;" k="20" />
    <hkern u1="&#x152;" u2="&#x11c;" k="20" />
    <hkern u1="&#x152;" u2="&#x11b;" k="20" />
    <hkern u1="&#x152;" u2="&#x119;" k="20" />
    <hkern u1="&#x152;" u2="&#x117;" k="20" />
    <hkern u1="&#x152;" u2="&#x115;" k="20" />
    <hkern u1="&#x152;" u2="&#x113;" k="20" />
    <hkern u1="&#x152;" u2="&#x111;" k="20" />
    <hkern u1="&#x152;" u2="&#x10f;" k="20" />
    <hkern u1="&#x152;" u2="&#x10d;" k="20" />
    <hkern u1="&#x152;" u2="&#x10c;" k="20" />
    <hkern u1="&#x152;" u2="&#x10b;" k="20" />
    <hkern u1="&#x152;" u2="&#x10a;" k="20" />
    <hkern u1="&#x152;" u2="&#x109;" k="20" />
    <hkern u1="&#x152;" u2="&#x108;" k="20" />
    <hkern u1="&#x152;" u2="&#x107;" k="20" />
    <hkern u1="&#x152;" u2="&#x106;" k="20" />
    <hkern u1="&#x152;" u2="&#xff;" k="20" />
    <hkern u1="&#x152;" u2="&#xfd;" k="20" />
    <hkern u1="&#x152;" u2="&#xfc;" k="15" />
    <hkern u1="&#x152;" u2="&#xfb;" k="15" />
    <hkern u1="&#x152;" u2="&#xfa;" k="15" />
    <hkern u1="&#x152;" u2="&#xf9;" k="15" />
    <hkern u1="&#x152;" u2="&#xf6;" k="20" />
    <hkern u1="&#x152;" u2="&#xf5;" k="20" />
    <hkern u1="&#x152;" u2="&#xf4;" k="20" />
    <hkern u1="&#x152;" u2="&#xf3;" k="20" />
    <hkern u1="&#x152;" u2="&#xf2;" k="20" />
    <hkern u1="&#x152;" u2="&#xf0;" k="20" />
    <hkern u1="&#x152;" u2="&#xeb;" k="20" />
    <hkern u1="&#x152;" u2="&#xea;" k="20" />
    <hkern u1="&#x152;" u2="&#xe9;" k="20" />
    <hkern u1="&#x152;" u2="&#xe8;" k="20" />
    <hkern u1="&#x152;" u2="&#xe7;" k="20" />
    <hkern u1="&#x152;" u2="&#xd6;" k="20" />
    <hkern u1="&#x152;" u2="&#xd5;" k="20" />
    <hkern u1="&#x152;" u2="&#xd4;" k="20" />
    <hkern u1="&#x152;" u2="&#xd3;" k="20" />
    <hkern u1="&#x152;" u2="&#xd2;" k="20" />
    <hkern u1="&#x152;" u2="&#xc7;" k="20" />
    <hkern u1="&#x152;" u2="y" k="20" />
    <hkern u1="&#x152;" u2="w" k="20" />
    <hkern u1="&#x152;" u2="v" k="25" />
    <hkern u1="&#x152;" u2="u" k="15" />
    <hkern u1="&#x152;" u2="t" k="20" />
    <hkern u1="&#x152;" u2="q" k="20" />
    <hkern u1="&#x152;" u2="o" k="20" />
    <hkern u1="&#x152;" u2="g" k="20" />
    <hkern u1="&#x152;" u2="e" k="20" />
    <hkern u1="&#x152;" u2="d" k="20" />
    <hkern u1="&#x152;" u2="c" k="20" />
    <hkern u1="&#x152;" u2="Q" k="20" />
    <hkern u1="&#x152;" u2="O" k="20" />
    <hkern u1="&#x152;" u2="G" k="20" />
    <hkern u1="&#x152;" u2="C" k="20" />
    <hkern u1="&#x153;" u2="&#x177;" k="10" />
    <hkern u1="&#x153;" u2="&#xff;" k="10" />
    <hkern u1="&#x153;" u2="&#xfd;" k="10" />
    <hkern u1="&#x153;" u2="y" k="10" />
    <hkern u1="&#x153;" u2="x" k="7" />
    <hkern u1="&#x153;" u2="v" k="5" />
    <hkern u1="&#x153;" u2="\" k="61" />
    <hkern u1="&#x154;" u2="&#x153;" k="20" />
    <hkern u1="&#x154;" u2="&#x152;" k="15" />
    <hkern u1="&#x154;" u2="&#xf0;" k="20" />
    <hkern u1="&#x154;" u2="u" k="15" />
    <hkern u1="&#x154;" u2="q" k="20" />
    <hkern u1="&#x154;" u2="o" k="20" />
    <hkern u1="&#x154;" u2="g" k="20" />
    <hkern u1="&#x154;" u2="e" k="20" />
    <hkern u1="&#x154;" u2="d" k="20" />
    <hkern u1="&#x154;" u2="c" k="20" />
    <hkern u1="&#x154;" u2="Y" k="30" />
    <hkern u1="&#x154;" u2="V" k="20" />
    <hkern u1="&#x154;" u2="U" k="10" />
    <hkern u1="&#x154;" u2="T" k="10" />
    <hkern u1="&#x154;" u2="Q" k="15" />
    <hkern u1="&#x154;" u2="O" k="15" />
    <hkern u1="&#x154;" u2="G" k="15" />
    <hkern u1="&#x154;" u2="C" k="15" />
    <hkern u1="&#x155;" u2="&#x2026;" k="32" />
    <hkern u1="&#x155;" u2="&#x2014;" k="12" />
    <hkern u1="&#x155;" u2="&#x2013;" k="12" />
    <hkern u1="&#x155;" u2="&#x153;" k="10" />
    <hkern u1="&#x155;" u2="&#xf0;" k="10" />
    <hkern u1="&#x155;" u2="&#xe6;" k="15" />
    <hkern u1="&#x155;" u2="&#xbf;" k="20" />
    <hkern u1="&#x155;" u2="y" k="-15" />
    <hkern u1="&#x155;" u2="x" k="-15" />
    <hkern u1="&#x155;" u2="w" k="-15" />
    <hkern u1="&#x155;" u2="v" k="-15" />
    <hkern u1="&#x155;" u2="t" k="-7" />
    <hkern u1="&#x155;" u2="s" k="10" />
    <hkern u1="&#x155;" u2="q" k="10" />
    <hkern u1="&#x155;" u2="o" k="10" />
    <hkern u1="&#x155;" u2="g" k="10" />
    <hkern u1="&#x155;" u2="f" k="-10" />
    <hkern u1="&#x155;" u2="e" k="10" />
    <hkern u1="&#x155;" u2="d" k="10" />
    <hkern u1="&#x155;" u2="c" k="10" />
    <hkern u1="&#x155;" u2="a" k="15" />
    <hkern u1="&#x155;" u2="&#x2e;" k="32" />
    <hkern u1="&#x155;" u2="&#x2d;" k="12" />
    <hkern u1="&#x155;" u2="&#x2c;" k="32" />
    <hkern u1="&#x156;" u2="&#x153;" k="20" />
    <hkern u1="&#x156;" u2="&#x152;" k="15" />
    <hkern u1="&#x156;" u2="&#xf0;" k="20" />
    <hkern u1="&#x156;" u2="u" k="15" />
    <hkern u1="&#x156;" u2="q" k="20" />
    <hkern u1="&#x156;" u2="o" k="20" />
    <hkern u1="&#x156;" u2="g" k="20" />
    <hkern u1="&#x156;" u2="e" k="20" />
    <hkern u1="&#x156;" u2="d" k="20" />
    <hkern u1="&#x156;" u2="c" k="20" />
    <hkern u1="&#x156;" u2="Y" k="30" />
    <hkern u1="&#x156;" u2="V" k="20" />
    <hkern u1="&#x156;" u2="U" k="10" />
    <hkern u1="&#x156;" u2="T" k="10" />
    <hkern u1="&#x156;" u2="Q" k="15" />
    <hkern u1="&#x156;" u2="O" k="15" />
    <hkern u1="&#x156;" u2="G" k="15" />
    <hkern u1="&#x156;" u2="C" k="15" />
    <hkern u1="&#x157;" u2="&#x2026;" k="32" />
    <hkern u1="&#x157;" u2="&#x2014;" k="12" />
    <hkern u1="&#x157;" u2="&#x2013;" k="12" />
    <hkern u1="&#x157;" u2="&#x153;" k="10" />
    <hkern u1="&#x157;" u2="&#xf0;" k="10" />
    <hkern u1="&#x157;" u2="&#xe6;" k="15" />
    <hkern u1="&#x157;" u2="&#xbf;" k="20" />
    <hkern u1="&#x157;" u2="y" k="-15" />
    <hkern u1="&#x157;" u2="x" k="-15" />
    <hkern u1="&#x157;" u2="w" k="-15" />
    <hkern u1="&#x157;" u2="v" k="-15" />
    <hkern u1="&#x157;" u2="t" k="-7" />
    <hkern u1="&#x157;" u2="s" k="10" />
    <hkern u1="&#x157;" u2="q" k="10" />
    <hkern u1="&#x157;" u2="o" k="10" />
    <hkern u1="&#x157;" u2="g" k="10" />
    <hkern u1="&#x157;" u2="f" k="-10" />
    <hkern u1="&#x157;" u2="e" k="10" />
    <hkern u1="&#x157;" u2="d" k="10" />
    <hkern u1="&#x157;" u2="c" k="10" />
    <hkern u1="&#x157;" u2="a" k="15" />
    <hkern u1="&#x157;" u2="&#x2e;" k="32" />
    <hkern u1="&#x157;" u2="&#x2d;" k="12" />
    <hkern u1="&#x157;" u2="&#x2c;" k="32" />
    <hkern u1="&#x158;" u2="&#x153;" k="20" />
    <hkern u1="&#x158;" u2="&#x152;" k="15" />
    <hkern u1="&#x158;" u2="&#xf0;" k="20" />
    <hkern u1="&#x158;" u2="u" k="15" />
    <hkern u1="&#x158;" u2="q" k="20" />
    <hkern u1="&#x158;" u2="o" k="20" />
    <hkern u1="&#x158;" u2="g" k="20" />
    <hkern u1="&#x158;" u2="e" k="20" />
    <hkern u1="&#x158;" u2="d" k="20" />
    <hkern u1="&#x158;" u2="c" k="20" />
    <hkern u1="&#x158;" u2="Y" k="30" />
    <hkern u1="&#x158;" u2="V" k="20" />
    <hkern u1="&#x158;" u2="U" k="10" />
    <hkern u1="&#x158;" u2="T" k="10" />
    <hkern u1="&#x158;" u2="Q" k="15" />
    <hkern u1="&#x158;" u2="O" k="15" />
    <hkern u1="&#x158;" u2="G" k="15" />
    <hkern u1="&#x158;" u2="C" k="15" />
    <hkern u1="&#x159;" u2="&#x2026;" k="32" />
    <hkern u1="&#x159;" u2="&#x2014;" k="12" />
    <hkern u1="&#x159;" u2="&#x2013;" k="12" />
    <hkern u1="&#x159;" u2="&#x153;" k="10" />
    <hkern u1="&#x159;" u2="&#xf0;" k="10" />
    <hkern u1="&#x159;" u2="&#xe6;" k="15" />
    <hkern u1="&#x159;" u2="&#xbf;" k="20" />
    <hkern u1="&#x159;" u2="y" k="-15" />
    <hkern u1="&#x159;" u2="x" k="-15" />
    <hkern u1="&#x159;" u2="w" k="-15" />
    <hkern u1="&#x159;" u2="v" k="-15" />
    <hkern u1="&#x159;" u2="t" k="-7" />
    <hkern u1="&#x159;" u2="s" k="10" />
    <hkern u1="&#x159;" u2="q" k="10" />
    <hkern u1="&#x159;" u2="o" k="10" />
    <hkern u1="&#x159;" u2="g" k="10" />
    <hkern u1="&#x159;" u2="f" k="-10" />
    <hkern u1="&#x159;" u2="e" k="10" />
    <hkern u1="&#x159;" u2="d" k="10" />
    <hkern u1="&#x159;" u2="c" k="10" />
    <hkern u1="&#x159;" u2="a" k="15" />
    <hkern u1="&#x159;" u2="&#x2e;" k="32" />
    <hkern u1="&#x159;" u2="&#x2d;" k="12" />
    <hkern u1="&#x159;" u2="&#x2c;" k="32" />
    <hkern u1="&#x15a;" u2="z" k="10" />
    <hkern u1="&#x15a;" u2="y" k="15" />
    <hkern u1="&#x15a;" u2="x" k="10" />
    <hkern u1="&#x15a;" u2="w" k="10" />
    <hkern u1="&#x15a;" u2="v" k="15" />
    <hkern u1="&#x15a;" u2="t" k="15" />
    <hkern u1="&#x15a;" u2="S" k="10" />
    <hkern u1="&#x15b;" u2="&#x201c;" k="33" />
    <hkern u1="&#x15b;" u2="&#x2018;" k="33" />
    <hkern u1="&#x15b;" u2="\" k="56" />
    <hkern u1="&#x15c;" u2="z" k="10" />
    <hkern u1="&#x15c;" u2="y" k="15" />
    <hkern u1="&#x15c;" u2="x" k="10" />
    <hkern u1="&#x15c;" u2="w" k="10" />
    <hkern u1="&#x15c;" u2="v" k="15" />
    <hkern u1="&#x15c;" u2="t" k="15" />
    <hkern u1="&#x15c;" u2="S" k="10" />
    <hkern u1="&#x15d;" u2="&#x201c;" k="33" />
    <hkern u1="&#x15d;" u2="&#x2018;" k="33" />
    <hkern u1="&#x15d;" u2="\" k="56" />
    <hkern u1="&#x15e;" u2="z" k="10" />
    <hkern u1="&#x15e;" u2="y" k="15" />
    <hkern u1="&#x15e;" u2="x" k="10" />
    <hkern u1="&#x15e;" u2="w" k="10" />
    <hkern u1="&#x15e;" u2="v" k="15" />
    <hkern u1="&#x15e;" u2="t" k="15" />
    <hkern u1="&#x15e;" u2="S" k="10" />
    <hkern u1="&#x15f;" u2="&#x201c;" k="33" />
    <hkern u1="&#x15f;" u2="&#x2018;" k="33" />
    <hkern u1="&#x15f;" u2="\" k="56" />
    <hkern u1="&#x160;" u2="z" k="10" />
    <hkern u1="&#x160;" u2="y" k="15" />
    <hkern u1="&#x160;" u2="x" k="10" />
    <hkern u1="&#x160;" u2="w" k="10" />
    <hkern u1="&#x160;" u2="v" k="15" />
    <hkern u1="&#x160;" u2="t" k="15" />
    <hkern u1="&#x160;" u2="S" k="10" />
    <hkern u1="&#x161;" u2="&#x201c;" k="33" />
    <hkern u1="&#x161;" u2="&#x2018;" k="33" />
    <hkern u1="&#x161;" u2="\" k="56" />
    <hkern u1="&#x162;" u2="&#x2039;" k="56" />
    <hkern u1="&#x162;" u2="&#x2026;" k="46" />
    <hkern u1="&#x162;" u2="&#x2014;" k="29" />
    <hkern u1="&#x162;" u2="&#x2013;" k="29" />
    <hkern u1="&#x162;" u2="&#x153;" k="75" />
    <hkern u1="&#x162;" u2="&#x152;" k="35" />
    <hkern u1="&#x162;" u2="&#x14b;" k="80" />
    <hkern u1="&#x162;" u2="&#xf0;" k="75" />
    <hkern u1="&#x162;" u2="&#xe6;" k="89" />
    <hkern u1="&#x162;" u2="&#xc6;" k="84" />
    <hkern u1="&#x162;" u2="&#xbf;" k="90" />
    <hkern u1="&#x162;" u2="&#xbb;" k="60" />
    <hkern u1="&#x162;" u2="&#xab;" k="56" />
    <hkern u1="&#x162;" u2="z" k="75" />
    <hkern u1="&#x162;" u2="y" k="56" />
    <hkern u1="&#x162;" u2="x" k="52" />
    <hkern u1="&#x162;" u2="w" k="52" />
    <hkern u1="&#x162;" u2="v" k="42" />
    <hkern u1="&#x162;" u2="u" k="80" />
    <hkern u1="&#x162;" u2="s" k="80" />
    <hkern u1="&#x162;" u2="r" k="80" />
    <hkern u1="&#x162;" u2="q" k="75" />
    <hkern u1="&#x162;" u2="p" k="80" />
    <hkern u1="&#x162;" u2="o" k="75" />
    <hkern u1="&#x162;" u2="n" k="80" />
    <hkern u1="&#x162;" u2="m" k="80" />
    <hkern u1="&#x162;" u2="g" k="75" />
    <hkern u1="&#x162;" u2="e" k="75" />
    <hkern u1="&#x162;" u2="d" k="75" />
    <hkern u1="&#x162;" u2="c" k="75" />
    <hkern u1="&#x162;" u2="a" k="89" />
    <hkern u1="&#x162;" u2="Q" k="35" />
    <hkern u1="&#x162;" u2="O" k="35" />
    <hkern u1="&#x162;" u2="J" k="92" />
    <hkern u1="&#x162;" u2="G" k="35" />
    <hkern u1="&#x162;" u2="C" k="35" />
    <hkern u1="&#x162;" u2="A" k="80" />
    <hkern u1="&#x162;" u2="&#x40;" k="55" />
    <hkern u1="&#x162;" u2="&#x3f;" k="-46" />
    <hkern u1="&#x162;" u2="&#x3b;" k="20" />
    <hkern u1="&#x162;" u2="&#x3a;" k="20" />
    <hkern u1="&#x162;" u2="&#x2e;" k="46" />
    <hkern u1="&#x162;" u2="&#x2d;" k="29" />
    <hkern u1="&#x162;" u2="&#x2c;" k="46" />
    <hkern u1="&#x162;" u2="&#x26;" k="47" />
    <hkern u1="&#x163;" u2="&#x29;" k="-36" />
    <hkern u1="&#x164;" u2="&#x2039;" k="56" />
    <hkern u1="&#x164;" u2="&#x2026;" k="46" />
    <hkern u1="&#x164;" u2="&#x2014;" k="29" />
    <hkern u1="&#x164;" u2="&#x2013;" k="29" />
    <hkern u1="&#x164;" u2="&#x153;" k="75" />
    <hkern u1="&#x164;" u2="&#x152;" k="35" />
    <hkern u1="&#x164;" u2="&#x14b;" k="80" />
    <hkern u1="&#x164;" u2="&#xf0;" k="75" />
    <hkern u1="&#x164;" u2="&#xe6;" k="89" />
    <hkern u1="&#x164;" u2="&#xc6;" k="84" />
    <hkern u1="&#x164;" u2="&#xbf;" k="90" />
    <hkern u1="&#x164;" u2="&#xbb;" k="60" />
    <hkern u1="&#x164;" u2="&#xab;" k="56" />
    <hkern u1="&#x164;" u2="z" k="75" />
    <hkern u1="&#x164;" u2="y" k="56" />
    <hkern u1="&#x164;" u2="x" k="52" />
    <hkern u1="&#x164;" u2="w" k="52" />
    <hkern u1="&#x164;" u2="v" k="42" />
    <hkern u1="&#x164;" u2="u" k="80" />
    <hkern u1="&#x164;" u2="s" k="80" />
    <hkern u1="&#x164;" u2="r" k="80" />
    <hkern u1="&#x164;" u2="q" k="75" />
    <hkern u1="&#x164;" u2="p" k="80" />
    <hkern u1="&#x164;" u2="o" k="75" />
    <hkern u1="&#x164;" u2="n" k="80" />
    <hkern u1="&#x164;" u2="m" k="80" />
    <hkern u1="&#x164;" u2="g" k="75" />
    <hkern u1="&#x164;" u2="e" k="75" />
    <hkern u1="&#x164;" u2="d" k="75" />
    <hkern u1="&#x164;" u2="c" k="75" />
    <hkern u1="&#x164;" u2="a" k="89" />
    <hkern u1="&#x164;" u2="Q" k="35" />
    <hkern u1="&#x164;" u2="O" k="35" />
    <hkern u1="&#x164;" u2="J" k="92" />
    <hkern u1="&#x164;" u2="G" k="35" />
    <hkern u1="&#x164;" u2="C" k="35" />
    <hkern u1="&#x164;" u2="A" k="80" />
    <hkern u1="&#x164;" u2="&#x40;" k="55" />
    <hkern u1="&#x164;" u2="&#x3f;" k="-46" />
    <hkern u1="&#x164;" u2="&#x3b;" k="20" />
    <hkern u1="&#x164;" u2="&#x3a;" k="20" />
    <hkern u1="&#x164;" u2="&#x2e;" k="46" />
    <hkern u1="&#x164;" u2="&#x2d;" k="29" />
    <hkern u1="&#x164;" u2="&#x2c;" k="46" />
    <hkern u1="&#x164;" u2="&#x26;" k="47" />
    <hkern u1="&#x165;" u2="&#x29;" k="-36" />
    <hkern u1="&#x166;" u2="&#x2039;" k="56" />
    <hkern u1="&#x166;" u2="&#x2026;" k="46" />
    <hkern u1="&#x166;" u2="&#x2014;" k="29" />
    <hkern u1="&#x166;" u2="&#x2013;" k="29" />
    <hkern u1="&#x166;" u2="&#x153;" k="75" />
    <hkern u1="&#x166;" u2="&#x152;" k="35" />
    <hkern u1="&#x166;" u2="&#x14b;" k="80" />
    <hkern u1="&#x166;" u2="&#xf0;" k="75" />
    <hkern u1="&#x166;" u2="&#xe6;" k="89" />
    <hkern u1="&#x166;" u2="&#xc6;" k="84" />
    <hkern u1="&#x166;" u2="&#xbf;" k="90" />
    <hkern u1="&#x166;" u2="&#xbb;" k="60" />
    <hkern u1="&#x166;" u2="&#xab;" k="56" />
    <hkern u1="&#x166;" u2="z" k="75" />
    <hkern u1="&#x166;" u2="y" k="56" />
    <hkern u1="&#x166;" u2="x" k="52" />
    <hkern u1="&#x166;" u2="w" k="52" />
    <hkern u1="&#x166;" u2="v" k="42" />
    <hkern u1="&#x166;" u2="u" k="80" />
    <hkern u1="&#x166;" u2="s" k="80" />
    <hkern u1="&#x166;" u2="r" k="80" />
    <hkern u1="&#x166;" u2="q" k="75" />
    <hkern u1="&#x166;" u2="p" k="80" />
    <hkern u1="&#x166;" u2="o" k="75" />
    <hkern u1="&#x166;" u2="n" k="80" />
    <hkern u1="&#x166;" u2="m" k="80" />
    <hkern u1="&#x166;" u2="g" k="75" />
    <hkern u1="&#x166;" u2="e" k="75" />
    <hkern u1="&#x166;" u2="d" k="75" />
    <hkern u1="&#x166;" u2="c" k="75" />
    <hkern u1="&#x166;" u2="a" k="89" />
    <hkern u1="&#x166;" u2="Q" k="35" />
    <hkern u1="&#x166;" u2="O" k="35" />
    <hkern u1="&#x166;" u2="J" k="92" />
    <hkern u1="&#x166;" u2="G" k="35" />
    <hkern u1="&#x166;" u2="C" k="35" />
    <hkern u1="&#x166;" u2="A" k="80" />
    <hkern u1="&#x166;" u2="&#x40;" k="55" />
    <hkern u1="&#x166;" u2="&#x3f;" k="-46" />
    <hkern u1="&#x166;" u2="&#x3b;" k="20" />
    <hkern u1="&#x166;" u2="&#x3a;" k="20" />
    <hkern u1="&#x166;" u2="&#x2e;" k="46" />
    <hkern u1="&#x166;" u2="&#x2d;" k="29" />
    <hkern u1="&#x166;" u2="&#x2c;" k="46" />
    <hkern u1="&#x166;" u2="&#x26;" k="47" />
    <hkern u1="&#x167;" u2="&#x29;" k="-36" />
    <hkern u1="&#x168;" u2="&#x153;" k="15" />
    <hkern u1="&#x168;" u2="&#xf0;" k="15" />
    <hkern u1="&#x168;" u2="&#xe6;" k="20" />
    <hkern u1="&#x168;" u2="&#xc6;" k="38" />
    <hkern u1="&#x168;" u2="z" k="30" />
    <hkern u1="&#x168;" u2="y" k="10" />
    <hkern u1="&#x168;" u2="x" k="15" />
    <hkern u1="&#x168;" u2="t" k="15" />
    <hkern u1="&#x168;" u2="s" k="20" />
    <hkern u1="&#x168;" u2="q" k="15" />
    <hkern u1="&#x168;" u2="o" k="15" />
    <hkern u1="&#x168;" u2="g" k="15" />
    <hkern u1="&#x168;" u2="e" k="15" />
    <hkern u1="&#x168;" u2="d" k="15" />
    <hkern u1="&#x168;" u2="c" k="15" />
    <hkern u1="&#x168;" u2="a" k="20" />
    <hkern u1="&#x168;" u2="Z" k="25" />
    <hkern u1="&#x168;" u2="S" k="10" />
    <hkern u1="&#x168;" u2="J" k="40" />
    <hkern u1="&#x168;" u2="A" k="25" />
    <hkern u1="&#x169;" u2="&#x201c;" k="14" />
    <hkern u1="&#x169;" u2="&#x2018;" k="14" />
    <hkern u1="&#x169;" u2="\" k="56" />
    <hkern u1="&#x16a;" u2="&#x153;" k="15" />
    <hkern u1="&#x16a;" u2="&#xf0;" k="15" />
    <hkern u1="&#x16a;" u2="&#xe6;" k="20" />
    <hkern u1="&#x16a;" u2="&#xc6;" k="38" />
    <hkern u1="&#x16a;" u2="z" k="30" />
    <hkern u1="&#x16a;" u2="y" k="10" />
    <hkern u1="&#x16a;" u2="x" k="15" />
    <hkern u1="&#x16a;" u2="t" k="15" />
    <hkern u1="&#x16a;" u2="s" k="20" />
    <hkern u1="&#x16a;" u2="q" k="15" />
    <hkern u1="&#x16a;" u2="o" k="15" />
    <hkern u1="&#x16a;" u2="g" k="15" />
    <hkern u1="&#x16a;" u2="e" k="15" />
    <hkern u1="&#x16a;" u2="d" k="15" />
    <hkern u1="&#x16a;" u2="c" k="15" />
    <hkern u1="&#x16a;" u2="a" k="20" />
    <hkern u1="&#x16a;" u2="Z" k="25" />
    <hkern u1="&#x16a;" u2="S" k="10" />
    <hkern u1="&#x16a;" u2="J" k="40" />
    <hkern u1="&#x16a;" u2="A" k="25" />
    <hkern u1="&#x16b;" u2="&#x201c;" k="14" />
    <hkern u1="&#x16b;" u2="&#x2018;" k="14" />
    <hkern u1="&#x16b;" u2="\" k="56" />
    <hkern u1="&#x16c;" u2="&#x153;" k="15" />
    <hkern u1="&#x16c;" u2="&#xf0;" k="15" />
    <hkern u1="&#x16c;" u2="&#xe6;" k="20" />
    <hkern u1="&#x16c;" u2="&#xc6;" k="38" />
    <hkern u1="&#x16c;" u2="z" k="30" />
    <hkern u1="&#x16c;" u2="y" k="10" />
    <hkern u1="&#x16c;" u2="x" k="15" />
    <hkern u1="&#x16c;" u2="t" k="15" />
    <hkern u1="&#x16c;" u2="s" k="20" />
    <hkern u1="&#x16c;" u2="q" k="15" />
    <hkern u1="&#x16c;" u2="o" k="15" />
    <hkern u1="&#x16c;" u2="g" k="15" />
    <hkern u1="&#x16c;" u2="e" k="15" />
    <hkern u1="&#x16c;" u2="d" k="15" />
    <hkern u1="&#x16c;" u2="c" k="15" />
    <hkern u1="&#x16c;" u2="a" k="20" />
    <hkern u1="&#x16c;" u2="Z" k="25" />
    <hkern u1="&#x16c;" u2="S" k="10" />
    <hkern u1="&#x16c;" u2="J" k="40" />
    <hkern u1="&#x16c;" u2="A" k="25" />
    <hkern u1="&#x16d;" u2="&#x201c;" k="14" />
    <hkern u1="&#x16d;" u2="&#x2018;" k="14" />
    <hkern u1="&#x16d;" u2="\" k="56" />
    <hkern u1="&#x16e;" u2="&#x153;" k="15" />
    <hkern u1="&#x16e;" u2="&#xf0;" k="15" />
    <hkern u1="&#x16e;" u2="&#xe6;" k="20" />
    <hkern u1="&#x16e;" u2="&#xc6;" k="38" />
    <hkern u1="&#x16e;" u2="z" k="30" />
    <hkern u1="&#x16e;" u2="y" k="10" />
    <hkern u1="&#x16e;" u2="x" k="15" />
    <hkern u1="&#x16e;" u2="t" k="15" />
    <hkern u1="&#x16e;" u2="s" k="20" />
    <hkern u1="&#x16e;" u2="q" k="15" />
    <hkern u1="&#x16e;" u2="o" k="15" />
    <hkern u1="&#x16e;" u2="g" k="15" />
    <hkern u1="&#x16e;" u2="e" k="15" />
    <hkern u1="&#x16e;" u2="d" k="15" />
    <hkern u1="&#x16e;" u2="c" k="15" />
    <hkern u1="&#x16e;" u2="a" k="20" />
    <hkern u1="&#x16e;" u2="Z" k="25" />
    <hkern u1="&#x16e;" u2="S" k="10" />
    <hkern u1="&#x16e;" u2="J" k="40" />
    <hkern u1="&#x16e;" u2="A" k="25" />
    <hkern u1="&#x16f;" u2="&#x201c;" k="14" />
    <hkern u1="&#x16f;" u2="&#x2018;" k="14" />
    <hkern u1="&#x16f;" u2="\" k="56" />
    <hkern u1="&#x170;" u2="&#x153;" k="15" />
    <hkern u1="&#x170;" u2="&#xf0;" k="15" />
    <hkern u1="&#x170;" u2="&#xe6;" k="20" />
    <hkern u1="&#x170;" u2="&#xc6;" k="38" />
    <hkern u1="&#x170;" u2="z" k="30" />
    <hkern u1="&#x170;" u2="y" k="10" />
    <hkern u1="&#x170;" u2="x" k="15" />
    <hkern u1="&#x170;" u2="t" k="15" />
    <hkern u1="&#x170;" u2="s" k="20" />
    <hkern u1="&#x170;" u2="q" k="15" />
    <hkern u1="&#x170;" u2="o" k="15" />
    <hkern u1="&#x170;" u2="g" k="15" />
    <hkern u1="&#x170;" u2="e" k="15" />
    <hkern u1="&#x170;" u2="d" k="15" />
    <hkern u1="&#x170;" u2="c" k="15" />
    <hkern u1="&#x170;" u2="a" k="20" />
    <hkern u1="&#x170;" u2="Z" k="25" />
    <hkern u1="&#x170;" u2="S" k="10" />
    <hkern u1="&#x170;" u2="J" k="40" />
    <hkern u1="&#x170;" u2="A" k="25" />
    <hkern u1="&#x171;" u2="&#x201c;" k="14" />
    <hkern u1="&#x171;" u2="&#x2018;" k="14" />
    <hkern u1="&#x171;" u2="\" k="56" />
    <hkern u1="&#x172;" u2="&#x153;" k="15" />
    <hkern u1="&#x172;" u2="&#xf0;" k="15" />
    <hkern u1="&#x172;" u2="&#xe6;" k="20" />
    <hkern u1="&#x172;" u2="&#xc6;" k="38" />
    <hkern u1="&#x172;" u2="z" k="30" />
    <hkern u1="&#x172;" u2="y" k="10" />
    <hkern u1="&#x172;" u2="x" k="15" />
    <hkern u1="&#x172;" u2="t" k="15" />
    <hkern u1="&#x172;" u2="s" k="20" />
    <hkern u1="&#x172;" u2="q" k="15" />
    <hkern u1="&#x172;" u2="o" k="15" />
    <hkern u1="&#x172;" u2="g" k="15" />
    <hkern u1="&#x172;" u2="e" k="15" />
    <hkern u1="&#x172;" u2="d" k="15" />
    <hkern u1="&#x172;" u2="c" k="15" />
    <hkern u1="&#x172;" u2="a" k="20" />
    <hkern u1="&#x172;" u2="Z" k="25" />
    <hkern u1="&#x172;" u2="S" k="10" />
    <hkern u1="&#x172;" u2="J" k="40" />
    <hkern u1="&#x172;" u2="A" k="25" />
    <hkern u1="&#x173;" u2="&#x201c;" k="14" />
    <hkern u1="&#x173;" u2="&#x2018;" k="14" />
    <hkern u1="&#x173;" u2="\" k="56" />
    <hkern u1="&#x174;" u2="&#x2026;" k="10" />
    <hkern u1="&#x174;" u2="&#x153;" k="25" />
    <hkern u1="&#x174;" u2="&#x14b;" k="20" />
    <hkern u1="&#x174;" u2="&#xf0;" k="25" />
    <hkern u1="&#x174;" u2="&#xe6;" k="30" />
    <hkern u1="&#x174;" u2="&#xc6;" k="50" />
    <hkern u1="&#x174;" u2="&#xbf;" k="40" />
    <hkern u1="&#x174;" u2="z" k="15" />
    <hkern u1="&#x174;" u2="u" k="20" />
    <hkern u1="&#x174;" u2="s" k="30" />
    <hkern u1="&#x174;" u2="r" k="20" />
    <hkern u1="&#x174;" u2="q" k="25" />
    <hkern u1="&#x174;" u2="p" k="20" />
    <hkern u1="&#x174;" u2="o" k="25" />
    <hkern u1="&#x174;" u2="n" k="20" />
    <hkern u1="&#x174;" u2="m" k="20" />
    <hkern u1="&#x174;" u2="g" k="25" />
    <hkern u1="&#x174;" u2="e" k="25" />
    <hkern u1="&#x174;" u2="d" k="25" />
    <hkern u1="&#x174;" u2="c" k="25" />
    <hkern u1="&#x174;" u2="a" k="30" />
    <hkern u1="&#x174;" u2="J" k="35" />
    <hkern u1="&#x174;" u2="A" k="40" />
    <hkern u1="&#x174;" u2="&#x40;" k="5" />
    <hkern u1="&#x174;" u2="&#x3f;" k="-41" />
    <hkern u1="&#x174;" u2="&#x2e;" k="10" />
    <hkern u1="&#x174;" u2="&#x2c;" k="10" />
    <hkern u1="&#x174;" u2="&#x26;" k="24" />
    <hkern u1="&#x175;" u2="&#x2026;" k="28" />
    <hkern u1="&#x175;" u2="&#x2c7;" k="5" />
    <hkern u1="&#x175;" u2="&#xf0;" k="2" />
    <hkern u1="&#x175;" u2="&#xe6;" k="10" />
    <hkern u1="&#x175;" u2="&#xbf;" k="5" />
    <hkern u1="&#x175;" u2="s" k="5" />
    <hkern u1="&#x175;" u2="o" k="2" />
    <hkern u1="&#x175;" u2="e" k="2" />
    <hkern u1="&#x175;" u2="a" k="10" />
    <hkern u1="&#x175;" u2="&#x2e;" k="28" />
    <hkern u1="&#x175;" u2="&#x2c;" k="28" />
    <hkern u1="&#x175;" u2="&#x26;" k="20" />
    <hkern u1="&#x176;" u2="&#x2039;" k="57" />
    <hkern u1="&#x176;" u2="&#x2026;" k="39" />
    <hkern u1="&#x176;" u2="&#x2014;" k="37" />
    <hkern u1="&#x176;" u2="&#x2013;" k="37" />
    <hkern u1="&#x176;" u2="&#x153;" k="95" />
    <hkern u1="&#x176;" u2="&#x152;" k="50" />
    <hkern u1="&#x176;" u2="&#x14b;" k="75" />
    <hkern u1="&#x176;" u2="&#xf0;" k="95" />
    <hkern u1="&#x176;" u2="&#xe6;" k="80" />
    <hkern u1="&#x176;" u2="&#xc6;" k="110" />
    <hkern u1="&#x176;" u2="&#xbf;" k="90" />
    <hkern u1="&#x176;" u2="&#xbb;" k="50" />
    <hkern u1="&#x176;" u2="&#xab;" k="57" />
    <hkern u1="&#x176;" u2="z" k="61" />
    <hkern u1="&#x176;" u2="x" k="33" />
    <hkern u1="&#x176;" u2="w" k="24" />
    <hkern u1="&#x176;" u2="v" k="28" />
    <hkern u1="&#x176;" u2="u" k="66" />
    <hkern u1="&#x176;" u2="t" k="33" />
    <hkern u1="&#x176;" u2="s" k="80" />
    <hkern u1="&#x176;" u2="r" k="75" />
    <hkern u1="&#x176;" u2="q" k="95" />
    <hkern u1="&#x176;" u2="p" k="75" />
    <hkern u1="&#x176;" u2="o" k="95" />
    <hkern u1="&#x176;" u2="n" k="75" />
    <hkern u1="&#x176;" u2="m" k="75" />
    <hkern u1="&#x176;" u2="g" k="95" />
    <hkern u1="&#x176;" u2="f" k="38" />
    <hkern u1="&#x176;" u2="e" k="95" />
    <hkern u1="&#x176;" u2="d" k="95" />
    <hkern u1="&#x176;" u2="c" k="95" />
    <hkern u1="&#x176;" u2="a" k="80" />
    <hkern u1="&#x176;" u2="S" k="15" />
    <hkern u1="&#x176;" u2="Q" k="50" />
    <hkern u1="&#x176;" u2="O" k="50" />
    <hkern u1="&#x176;" u2="J" k="115" />
    <hkern u1="&#x176;" u2="G" k="50" />
    <hkern u1="&#x176;" u2="C" k="50" />
    <hkern u1="&#x176;" u2="A" k="95" />
    <hkern u1="&#x176;" u2="&#x40;" k="55" />
    <hkern u1="&#x176;" u2="&#x3f;" k="-22" />
    <hkern u1="&#x176;" u2="&#x3b;" k="31" />
    <hkern u1="&#x176;" u2="&#x3a;" k="31" />
    <hkern u1="&#x176;" u2="&#x2f;" k="61" />
    <hkern u1="&#x176;" u2="&#x2e;" k="39" />
    <hkern u1="&#x176;" u2="&#x2d;" k="37" />
    <hkern u1="&#x176;" u2="&#x2c;" k="39" />
    <hkern u1="&#x176;" u2="&#x26;" k="71" />
    <hkern u1="&#x177;" u2="&#x2026;" k="32" />
    <hkern u1="&#x177;" u2="&#x2014;" k="15" />
    <hkern u1="&#x177;" u2="&#x2013;" k="15" />
    <hkern u1="&#x177;" u2="&#x2c7;" k="10" />
    <hkern u1="&#x177;" u2="&#x153;" k="5" />
    <hkern u1="&#x177;" u2="&#xf0;" k="5" />
    <hkern u1="&#x177;" u2="&#xe6;" k="15" />
    <hkern u1="&#x177;" u2="&#xbf;" k="10" />
    <hkern u1="&#x177;" u2="t" k="-15" />
    <hkern u1="&#x177;" u2="s" k="6" />
    <hkern u1="&#x177;" u2="q" k="5" />
    <hkern u1="&#x177;" u2="o" k="5" />
    <hkern u1="&#x177;" u2="g" k="5" />
    <hkern u1="&#x177;" u2="e" k="5" />
    <hkern u1="&#x177;" u2="d" k="5" />
    <hkern u1="&#x177;" u2="c" k="5" />
    <hkern u1="&#x177;" u2="a" k="15" />
    <hkern u1="&#x177;" u2="&#x2e;" k="32" />
    <hkern u1="&#x177;" u2="&#x2d;" k="15" />
    <hkern u1="&#x177;" u2="&#x2c;" k="32" />
    <hkern u1="&#x178;" u2="&#x2039;" k="57" />
    <hkern u1="&#x178;" u2="&#x2026;" k="39" />
    <hkern u1="&#x178;" u2="&#x2014;" k="37" />
    <hkern u1="&#x178;" u2="&#x2013;" k="37" />
    <hkern u1="&#x178;" u2="&#x153;" k="95" />
    <hkern u1="&#x178;" u2="&#x152;" k="50" />
    <hkern u1="&#x178;" u2="&#x14b;" k="75" />
    <hkern u1="&#x178;" u2="&#xf0;" k="95" />
    <hkern u1="&#x178;" u2="&#xe6;" k="80" />
    <hkern u1="&#x178;" u2="&#xc6;" k="110" />
    <hkern u1="&#x178;" u2="&#xbf;" k="90" />
    <hkern u1="&#x178;" u2="&#xbb;" k="50" />
    <hkern u1="&#x178;" u2="&#xab;" k="57" />
    <hkern u1="&#x178;" u2="z" k="61" />
    <hkern u1="&#x178;" u2="x" k="33" />
    <hkern u1="&#x178;" u2="w" k="24" />
    <hkern u1="&#x178;" u2="v" k="28" />
    <hkern u1="&#x178;" u2="u" k="66" />
    <hkern u1="&#x178;" u2="t" k="33" />
    <hkern u1="&#x178;" u2="s" k="80" />
    <hkern u1="&#x178;" u2="r" k="75" />
    <hkern u1="&#x178;" u2="q" k="95" />
    <hkern u1="&#x178;" u2="p" k="75" />
    <hkern u1="&#x178;" u2="o" k="95" />
    <hkern u1="&#x178;" u2="n" k="75" />
    <hkern u1="&#x178;" u2="m" k="75" />
    <hkern u1="&#x178;" u2="g" k="95" />
    <hkern u1="&#x178;" u2="f" k="38" />
    <hkern u1="&#x178;" u2="e" k="95" />
    <hkern u1="&#x178;" u2="d" k="95" />
    <hkern u1="&#x178;" u2="c" k="95" />
    <hkern u1="&#x178;" u2="a" k="80" />
    <hkern u1="&#x178;" u2="S" k="15" />
    <hkern u1="&#x178;" u2="Q" k="50" />
    <hkern u1="&#x178;" u2="O" k="50" />
    <hkern u1="&#x178;" u2="J" k="115" />
    <hkern u1="&#x178;" u2="G" k="50" />
    <hkern u1="&#x178;" u2="C" k="50" />
    <hkern u1="&#x178;" u2="A" k="95" />
    <hkern u1="&#x178;" u2="&#x40;" k="55" />
    <hkern u1="&#x178;" u2="&#x3f;" k="-22" />
    <hkern u1="&#x178;" u2="&#x3b;" k="31" />
    <hkern u1="&#x178;" u2="&#x3a;" k="31" />
    <hkern u1="&#x178;" u2="&#x2f;" k="61" />
    <hkern u1="&#x178;" u2="&#x2e;" k="39" />
    <hkern u1="&#x178;" u2="&#x2d;" k="37" />
    <hkern u1="&#x178;" u2="&#x2c;" k="39" />
    <hkern u1="&#x178;" u2="&#x26;" k="71" />
    <hkern u1="&#x179;" u2="&#x153;" k="25" />
    <hkern u1="&#x179;" u2="&#x152;" k="25" />
    <hkern u1="&#x179;" u2="&#xf0;" k="25" />
    <hkern u1="&#x179;" u2="y" k="30" />
    <hkern u1="&#x179;" u2="w" k="20" />
    <hkern u1="&#x179;" u2="v" k="25" />
    <hkern u1="&#x179;" u2="u" k="20" />
    <hkern u1="&#x179;" u2="t" k="20" />
    <hkern u1="&#x179;" u2="q" k="25" />
    <hkern u1="&#x179;" u2="o" k="25" />
    <hkern u1="&#x179;" u2="g" k="25" />
    <hkern u1="&#x179;" u2="e" k="25" />
    <hkern u1="&#x179;" u2="d" k="25" />
    <hkern u1="&#x179;" u2="c" k="25" />
    <hkern u1="&#x179;" u2="Q" k="25" />
    <hkern u1="&#x179;" u2="O" k="25" />
    <hkern u1="&#x179;" u2="G" k="25" />
    <hkern u1="&#x179;" u2="C" k="25" />
    <hkern u1="&#x17a;" u2="&#x201c;" k="33" />
    <hkern u1="&#x17a;" u2="&#x2018;" k="33" />
    <hkern u1="&#x17a;" u2="&#x153;" k="30" />
    <hkern u1="&#x17a;" u2="&#xf0;" k="30" />
    <hkern u1="&#x17a;" u2="u" k="15" />
    <hkern u1="&#x17a;" u2="s" k="7" />
    <hkern u1="&#x17a;" u2="q" k="30" />
    <hkern u1="&#x17a;" u2="o" k="30" />
    <hkern u1="&#x17a;" u2="g" k="30" />
    <hkern u1="&#x17a;" u2="e" k="30" />
    <hkern u1="&#x17a;" u2="d" k="30" />
    <hkern u1="&#x17a;" u2="c" k="30" />
    <hkern u1="&#x17a;" u2="\" k="56" />
    <hkern u1="&#x17a;" u2="&#x26;" k="24" />
    <hkern u1="&#x17b;" u2="&#x153;" k="25" />
    <hkern u1="&#x17b;" u2="&#x152;" k="25" />
    <hkern u1="&#x17b;" u2="&#xf0;" k="25" />
    <hkern u1="&#x17b;" u2="y" k="30" />
    <hkern u1="&#x17b;" u2="w" k="20" />
    <hkern u1="&#x17b;" u2="v" k="25" />
    <hkern u1="&#x17b;" u2="u" k="20" />
    <hkern u1="&#x17b;" u2="t" k="20" />
    <hkern u1="&#x17b;" u2="q" k="25" />
    <hkern u1="&#x17b;" u2="o" k="25" />
    <hkern u1="&#x17b;" u2="g" k="25" />
    <hkern u1="&#x17b;" u2="e" k="25" />
    <hkern u1="&#x17b;" u2="d" k="25" />
    <hkern u1="&#x17b;" u2="c" k="25" />
    <hkern u1="&#x17b;" u2="Q" k="25" />
    <hkern u1="&#x17b;" u2="O" k="25" />
    <hkern u1="&#x17b;" u2="G" k="25" />
    <hkern u1="&#x17b;" u2="C" k="25" />
    <hkern u1="&#x17c;" u2="&#x201c;" k="33" />
    <hkern u1="&#x17c;" u2="&#x2018;" k="33" />
    <hkern u1="&#x17c;" u2="&#x153;" k="30" />
    <hkern u1="&#x17c;" u2="&#xf0;" k="30" />
    <hkern u1="&#x17c;" u2="u" k="15" />
    <hkern u1="&#x17c;" u2="s" k="7" />
    <hkern u1="&#x17c;" u2="q" k="30" />
    <hkern u1="&#x17c;" u2="o" k="30" />
    <hkern u1="&#x17c;" u2="g" k="30" />
    <hkern u1="&#x17c;" u2="e" k="30" />
    <hkern u1="&#x17c;" u2="d" k="30" />
    <hkern u1="&#x17c;" u2="c" k="30" />
    <hkern u1="&#x17c;" u2="\" k="56" />
    <hkern u1="&#x17c;" u2="&#x26;" k="24" />
    <hkern u1="&#x17d;" u2="&#x153;" k="25" />
    <hkern u1="&#x17d;" u2="&#x152;" k="25" />
    <hkern u1="&#x17d;" u2="&#xf0;" k="25" />
    <hkern u1="&#x17d;" u2="y" k="30" />
    <hkern u1="&#x17d;" u2="w" k="20" />
    <hkern u1="&#x17d;" u2="v" k="25" />
    <hkern u1="&#x17d;" u2="u" k="20" />
    <hkern u1="&#x17d;" u2="t" k="20" />
    <hkern u1="&#x17d;" u2="q" k="25" />
    <hkern u1="&#x17d;" u2="o" k="25" />
    <hkern u1="&#x17d;" u2="g" k="25" />
    <hkern u1="&#x17d;" u2="e" k="25" />
    <hkern u1="&#x17d;" u2="d" k="25" />
    <hkern u1="&#x17d;" u2="c" k="25" />
    <hkern u1="&#x17d;" u2="Q" k="25" />
    <hkern u1="&#x17d;" u2="O" k="25" />
    <hkern u1="&#x17d;" u2="G" k="25" />
    <hkern u1="&#x17d;" u2="C" k="25" />
    <hkern u1="&#x17e;" u2="&#x201c;" k="33" />
    <hkern u1="&#x17e;" u2="&#x2018;" k="33" />
    <hkern u1="&#x17e;" u2="&#x153;" k="30" />
    <hkern u1="&#x17e;" u2="&#xf0;" k="30" />
    <hkern u1="&#x17e;" u2="u" k="15" />
    <hkern u1="&#x17e;" u2="s" k="7" />
    <hkern u1="&#x17e;" u2="q" k="30" />
    <hkern u1="&#x17e;" u2="o" k="30" />
    <hkern u1="&#x17e;" u2="g" k="30" />
    <hkern u1="&#x17e;" u2="e" k="30" />
    <hkern u1="&#x17e;" u2="d" k="30" />
    <hkern u1="&#x17e;" u2="c" k="30" />
    <hkern u1="&#x17e;" u2="\" k="56" />
    <hkern u1="&#x17e;" u2="&#x26;" k="24" />
    <hkern u1="&#x218;" u2="z" k="10" />
    <hkern u1="&#x218;" u2="y" k="15" />
    <hkern u1="&#x218;" u2="x" k="10" />
    <hkern u1="&#x218;" u2="w" k="10" />
    <hkern u1="&#x218;" u2="v" k="15" />
    <hkern u1="&#x218;" u2="t" k="15" />
    <hkern u1="&#x218;" u2="S" k="10" />
    <hkern u1="&#x219;" u2="&#x201c;" k="33" />
    <hkern u1="&#x219;" u2="&#x2018;" k="33" />
    <hkern u1="&#x219;" u2="\" k="56" />
    <hkern u1="&#x2dd;" u2="&#x17d;" k="16" />
    <hkern u1="&#x2dd;" u2="&#x17b;" k="16" />
    <hkern u1="&#x2dd;" u2="&#x179;" k="16" />
    <hkern u1="&#x2dd;" u2="&#x178;" k="40" />
    <hkern u1="&#x2dd;" u2="&#x176;" k="40" />
    <hkern u1="&#x2dd;" u2="&#x174;" k="5" />
    <hkern u1="&#x2dd;" u2="&#x166;" k="41" />
    <hkern u1="&#x2dd;" u2="&#x164;" k="41" />
    <hkern u1="&#x2dd;" u2="&#x162;" k="41" />
    <hkern u1="&#x2dd;" u2="&#x104;" k="25" />
    <hkern u1="&#x2dd;" u2="&#x102;" k="25" />
    <hkern u1="&#x2dd;" u2="&#x100;" k="25" />
    <hkern u1="&#x2dd;" u2="&#xdd;" k="40" />
    <hkern u1="&#x2dd;" u2="&#xc5;" k="25" />
    <hkern u1="&#x2dd;" u2="&#xc4;" k="25" />
    <hkern u1="&#x2dd;" u2="&#xc3;" k="25" />
    <hkern u1="&#x2dd;" u2="&#xc2;" k="25" />
    <hkern u1="&#x2dd;" u2="&#xc1;" k="25" />
    <hkern u1="&#x2dd;" u2="&#xc0;" k="25" />
    <hkern u1="&#x2dd;" u2="Z" k="16" />
    <hkern u1="&#x2dd;" u2="Y" k="40" />
    <hkern u1="&#x2dd;" u2="X" k="25" />
    <hkern u1="&#x2dd;" u2="W" k="5" />
    <hkern u1="&#x2dd;" u2="V" k="23" />
    <hkern u1="&#x2dd;" u2="T" k="41" />
    <hkern u1="&#x2dd;" u2="A" k="25" />
    <hkern u1="&#x386;" u2="&#x201d;" k="34" />
    <hkern u1="&#x386;" u2="&#x2019;" k="85" />
    <hkern u1="&#x386;" u2="&#x3cd;" k="15" />
    <hkern u1="&#x386;" u2="&#x3cb;" k="15" />
    <hkern u1="&#x386;" u2="&#x3c7;" k="30" />
    <hkern u1="&#x386;" u2="&#x3c6;" k="10" />
    <hkern u1="&#x386;" u2="&#x3c5;" k="15" />
    <hkern u1="&#x386;" u2="&#x3c4;" k="88" />
    <hkern u1="&#x386;" u2="&#x3c0;" k="63" />
    <hkern u1="&#x386;" u2="&#x3bd;" k="40" />
    <hkern u1="&#x386;" u2="&#x3b3;" k="40" />
    <hkern u1="&#x386;" u2="&#x3b0;" k="15" />
    <hkern u1="&#x386;" u2="&#x3ab;" k="70" />
    <hkern u1="&#x386;" u2="&#x3a8;" k="40" />
    <hkern u1="&#x386;" u2="&#x3a6;" k="10" />
    <hkern u1="&#x386;" u2="&#x3a5;" k="70" />
    <hkern u1="&#x386;" u2="&#x3a4;" k="80" />
    <hkern u1="&#x386;" u2="&#x39f;" k="10" />
    <hkern u1="&#x386;" u2="&#x398;" k="10" />
    <hkern u1="&#x38c;" u2="&#x3bb;" k="30" />
    <hkern u1="&#x38c;" u2="&#x3ab;" k="20" />
    <hkern u1="&#x38c;" u2="&#x3a7;" k="15" />
    <hkern u1="&#x38c;" u2="&#x3a5;" k="20" />
    <hkern u1="&#x38c;" u2="&#x3a4;" k="15" />
    <hkern u1="&#x38c;" u2="&#x3a3;" k="30" />
    <hkern u1="&#x38c;" u2="&#x39e;" k="25" />
    <hkern u1="&#x38c;" u2="&#x39b;" k="10" />
    <hkern u1="&#x38c;" u2="&#x394;" k="10" />
    <hkern u1="&#x38c;" u2="&#x391;" k="10" />
    <hkern u1="&#x38e;" u2="&#x203a;" k="45" />
    <hkern u1="&#x38e;" u2="&#x2026;" k="100" />
    <hkern u1="&#x38e;" u2="&#x2014;" k="56" />
    <hkern u1="&#x38e;" u2="&#x2013;" k="56" />
    <hkern u1="&#x38e;" u2="&#x3ce;" k="70" />
    <hkern u1="&#x38e;" u2="&#x3cd;" k="20" />
    <hkern u1="&#x38e;" u2="&#x3cc;" k="80" />
    <hkern u1="&#x38e;" u2="&#x3cb;" k="20" />
    <hkern u1="&#x38e;" u2="&#x3ca;" k="40" />
    <hkern u1="&#x38e;" u2="&#x3c9;" k="70" />
    <hkern u1="&#x38e;" u2="&#x3c8;" k="30" />
    <hkern u1="&#x38e;" u2="&#x3c6;" k="90" />
    <hkern u1="&#x38e;" u2="&#x3c5;" k="20" />
    <hkern u1="&#x38e;" u2="&#x3c4;" k="59" />
    <hkern u1="&#x38e;" u2="&#x3c3;" k="90" />
    <hkern u1="&#x38e;" u2="&#x3c1;" k="100" />
    <hkern u1="&#x38e;" u2="&#x3c0;" k="54" />
    <hkern u1="&#x38e;" u2="&#x3bf;" k="80" />
    <hkern u1="&#x38e;" u2="&#x3ba;" k="40" />
    <hkern u1="&#x38e;" u2="&#x3b9;" k="40" />
    <hkern u1="&#x38e;" u2="&#x3b5;" k="80" />
    <hkern u1="&#x38e;" u2="&#x3b4;" k="20" />
    <hkern u1="&#x38e;" u2="&#x3b2;" k="20" />
    <hkern u1="&#x38e;" u2="&#x3b1;" k="75" />
    <hkern u1="&#x38e;" u2="&#x3b0;" k="20" />
    <hkern u1="&#x38e;" u2="&#x3af;" k="40" />
    <hkern u1="&#x38e;" u2="&#x3ad;" k="80" />
    <hkern u1="&#x38e;" u2="&#x3ac;" k="75" />
    <hkern u1="&#x38e;" u2="&#x3a9;" k="20" />
    <hkern u1="&#x38e;" u2="&#x3a6;" k="40" />
    <hkern u1="&#x38e;" u2="&#x3a4;" k="-20" />
    <hkern u1="&#x38e;" u2="&#x39f;" k="30" />
    <hkern u1="&#x38e;" u2="&#x39b;" k="90" />
    <hkern u1="&#x38e;" u2="&#x398;" k="30" />
    <hkern u1="&#x38e;" u2="&#x394;" k="90" />
    <hkern u1="&#x38e;" u2="&#x391;" k="90" />
    <hkern u1="&#x38e;" u2="&#x390;" k="40" />
    <hkern u1="&#x38e;" u2="&#xbb;" k="45" />
    <hkern u1="&#x38e;" u2="&#xb5;" k="50" />
    <hkern u1="&#x38e;" u2="&#x2e;" k="100" />
    <hkern u1="&#x38e;" u2="&#x2d;" k="56" />
    <hkern u1="&#x38e;" u2="&#x2c;" k="100" />
    <hkern u1="&#x38f;" u2="&#x3ab;" k="20" />
    <hkern u1="&#x38f;" u2="&#x3a5;" k="20" />
    <hkern u1="&#x38f;" u2="&#x3a4;" k="10" />
    <hkern u1="&#x390;" u2="&#x3cc;" k="10" />
    <hkern u1="&#x390;" u2="&#x3c6;" k="10" />
    <hkern u1="&#x390;" u2="&#x3c4;" k="20" />
    <hkern u1="&#x390;" u2="&#x3c3;" k="5" />
    <hkern u1="&#x390;" u2="&#x3c0;" k="20" />
    <hkern u1="&#x390;" u2="&#x3bf;" k="10" />
    <hkern u1="&#x390;" u2="&#x3b8;" k="5" />
    <hkern u1="&#x390;" u2="&#x3b4;" k="5" />
    <hkern u1="&#x390;" u2="&#x3b1;" k="7" />
    <hkern u1="&#x390;" u2="&#x3ac;" k="7" />
    <hkern u1="&#x391;" u2="&#x201d;" k="34" />
    <hkern u1="&#x391;" u2="&#x2019;" k="85" />
    <hkern u1="&#x391;" u2="&#x3cd;" k="15" />
    <hkern u1="&#x391;" u2="&#x3cb;" k="15" />
    <hkern u1="&#x391;" u2="&#x3c7;" k="30" />
    <hkern u1="&#x391;" u2="&#x3c6;" k="10" />
    <hkern u1="&#x391;" u2="&#x3c5;" k="15" />
    <hkern u1="&#x391;" u2="&#x3c4;" k="88" />
    <hkern u1="&#x391;" u2="&#x3c0;" k="63" />
    <hkern u1="&#x391;" u2="&#x3bd;" k="40" />
    <hkern u1="&#x391;" u2="&#x3b3;" k="40" />
    <hkern u1="&#x391;" u2="&#x3b0;" k="15" />
    <hkern u1="&#x391;" u2="&#x3ab;" k="80" />
    <hkern u1="&#x391;" u2="&#x3a8;" k="40" />
    <hkern u1="&#x391;" u2="&#x3a6;" k="10" />
    <hkern u1="&#x391;" u2="&#x3a5;" k="80" />
    <hkern u1="&#x391;" u2="&#x3a4;" k="80" />
    <hkern u1="&#x391;" u2="&#x39f;" k="10" />
    <hkern u1="&#x391;" u2="&#x398;" k="10" />
    <hkern u1="&#x393;" u2="&#x203a;" k="59" />
    <hkern u1="&#x393;" u2="&#x2026;" k="120" />
    <hkern u1="&#x393;" u2="&#x2014;" k="99" />
    <hkern u1="&#x393;" u2="&#x2013;" k="99" />
    <hkern u1="&#x393;" u2="&#x3ce;" k="70" />
    <hkern u1="&#x393;" u2="&#x3cd;" k="30" />
    <hkern u1="&#x393;" u2="&#x3cc;" k="90" />
    <hkern u1="&#x393;" u2="&#x3cb;" k="30" />
    <hkern u1="&#x393;" u2="&#x3ca;" k="70" />
    <hkern u1="&#x393;" u2="&#x3c9;" k="70" />
    <hkern u1="&#x393;" u2="&#x3c8;" k="84" />
    <hkern u1="&#x393;" u2="&#x3c7;" k="30" />
    <hkern u1="&#x393;" u2="&#x3c6;" k="70" />
    <hkern u1="&#x393;" u2="&#x3c5;" k="30" />
    <hkern u1="&#x393;" u2="&#x3c4;" k="93" />
    <hkern u1="&#x393;" u2="&#x3c3;" k="95" />
    <hkern u1="&#x393;" u2="&#x3c2;" k="85" />
    <hkern u1="&#x393;" u2="&#x3c1;" k="85" />
    <hkern u1="&#x393;" u2="&#x3c0;" k="84" />
    <hkern u1="&#x393;" u2="&#x3bf;" k="90" />
    <hkern u1="&#x393;" u2="&#x3bd;" k="40" />
    <hkern u1="&#x393;" u2="&#x3ba;" k="50" />
    <hkern u1="&#x393;" u2="&#x3b9;" k="70" />
    <hkern u1="&#x393;" u2="&#x3b7;" k="70" />
    <hkern u1="&#x393;" u2="&#x3b5;" k="80" />
    <hkern u1="&#x393;" u2="&#x3b4;" k="30" />
    <hkern u1="&#x393;" u2="&#x3b1;" k="100" />
    <hkern u1="&#x393;" u2="&#x3b0;" k="30" />
    <hkern u1="&#x393;" u2="&#x3af;" k="70" />
    <hkern u1="&#x393;" u2="&#x3ae;" k="70" />
    <hkern u1="&#x393;" u2="&#x3ad;" k="80" />
    <hkern u1="&#x393;" u2="&#x3ac;" k="100" />
    <hkern u1="&#x393;" u2="&#x3ab;" k="-25" />
    <hkern u1="&#x393;" u2="&#x3a9;" k="25" />
    <hkern u1="&#x393;" u2="&#x3a6;" k="50" />
    <hkern u1="&#x393;" u2="&#x3a5;" k="-25" />
    <hkern u1="&#x393;" u2="&#x39f;" k="20" />
    <hkern u1="&#x393;" u2="&#x39b;" k="80" />
    <hkern u1="&#x393;" u2="&#x398;" k="20" />
    <hkern u1="&#x393;" u2="&#x394;" k="80" />
    <hkern u1="&#x393;" u2="&#x391;" k="80" />
    <hkern u1="&#x393;" u2="&#x390;" k="70" />
    <hkern u1="&#x393;" u2="&#xbb;" k="59" />
    <hkern u1="&#x393;" u2="&#xb5;" k="45" />
    <hkern u1="&#x393;" u2="&#x2e;" k="120" />
    <hkern u1="&#x393;" u2="&#x2d;" k="99" />
    <hkern u1="&#x393;" u2="&#x2c;" k="120" />
    <hkern u1="&#x394;" u2="&#x201d;" k="34" />
    <hkern u1="&#x394;" u2="&#x2019;" k="85" />
    <hkern u1="&#x394;" u2="&#x3cd;" k="15" />
    <hkern u1="&#x394;" u2="&#x3cb;" k="15" />
    <hkern u1="&#x394;" u2="&#x3c7;" k="30" />
    <hkern u1="&#x394;" u2="&#x3c6;" k="10" />
    <hkern u1="&#x394;" u2="&#x3c5;" k="15" />
    <hkern u1="&#x394;" u2="&#x3c4;" k="88" />
    <hkern u1="&#x394;" u2="&#x3c0;" k="63" />
    <hkern u1="&#x394;" u2="&#x3bd;" k="40" />
    <hkern u1="&#x394;" u2="&#x3b3;" k="40" />
    <hkern u1="&#x394;" u2="&#x3b0;" k="15" />
    <hkern u1="&#x394;" u2="&#x3ab;" k="80" />
    <hkern u1="&#x394;" u2="&#x3a8;" k="40" />
    <hkern u1="&#x394;" u2="&#x3a6;" k="10" />
    <hkern u1="&#x394;" u2="&#x3a5;" k="80" />
    <hkern u1="&#x394;" u2="&#x3a4;" k="80" />
    <hkern u1="&#x394;" u2="&#x39f;" k="10" />
    <hkern u1="&#x394;" u2="&#x398;" k="10" />
    <hkern u1="&#x395;" u2="&#x3cd;" k="20" />
    <hkern u1="&#x395;" u2="&#x3cc;" k="15" />
    <hkern u1="&#x395;" u2="&#x3cb;" k="20" />
    <hkern u1="&#x395;" u2="&#x3ca;" k="10" />
    <hkern u1="&#x395;" u2="&#x3c8;" k="20" />
    <hkern u1="&#x395;" u2="&#x3c7;" k="30" />
    <hkern u1="&#x395;" u2="&#x3c6;" k="20" />
    <hkern u1="&#x395;" u2="&#x3c5;" k="20" />
    <hkern u1="&#x395;" u2="&#x3c4;" k="20" />
    <hkern u1="&#x395;" u2="&#x3c3;" k="15" />
    <hkern u1="&#x395;" u2="&#x3c0;" k="30" />
    <hkern u1="&#x395;" u2="&#x3bf;" k="15" />
    <hkern u1="&#x395;" u2="&#x3bd;" k="30" />
    <hkern u1="&#x395;" u2="&#x3b9;" k="10" />
    <hkern u1="&#x395;" u2="&#x3b8;" k="15" />
    <hkern u1="&#x395;" u2="&#x3b4;" k="20" />
    <hkern u1="&#x395;" u2="&#x3b3;" k="25" />
    <hkern u1="&#x395;" u2="&#x3b1;" k="20" />
    <hkern u1="&#x395;" u2="&#x3b0;" k="20" />
    <hkern u1="&#x395;" u2="&#x3af;" k="10" />
    <hkern u1="&#x395;" u2="&#x3ac;" k="20" />
    <hkern u1="&#x395;" u2="&#x3a6;" k="20" />
    <hkern u1="&#x395;" u2="&#x39f;" k="15" />
    <hkern u1="&#x395;" u2="&#x398;" k="15" />
    <hkern u1="&#x395;" u2="&#x390;" k="10" />
    <hkern u1="&#x396;" u2="&#x3ce;" k="25" />
    <hkern u1="&#x396;" u2="&#x3cd;" k="20" />
    <hkern u1="&#x396;" u2="&#x3cc;" k="25" />
    <hkern u1="&#x396;" u2="&#x3cb;" k="20" />
    <hkern u1="&#x396;" u2="&#x3ca;" k="20" />
    <hkern u1="&#x396;" u2="&#x3c9;" k="25" />
    <hkern u1="&#x396;" u2="&#x3c6;" k="20" />
    <hkern u1="&#x396;" u2="&#x3c5;" k="20" />
    <hkern u1="&#x396;" u2="&#x3c4;" k="49" />
    <hkern u1="&#x396;" u2="&#x3c3;" k="25" />
    <hkern u1="&#x396;" u2="&#x3bf;" k="25" />
    <hkern u1="&#x396;" u2="&#x3b9;" k="20" />
    <hkern u1="&#x396;" u2="&#x3b5;" k="20" />
    <hkern u1="&#x396;" u2="&#x3b2;" k="20" />
    <hkern u1="&#x396;" u2="&#x3b1;" k="20" />
    <hkern u1="&#x396;" u2="&#x3b0;" k="20" />
    <hkern u1="&#x396;" u2="&#x3af;" k="20" />
    <hkern u1="&#x396;" u2="&#x3ad;" k="20" />
    <hkern u1="&#x396;" u2="&#x3ac;" k="20" />
    <hkern u1="&#x396;" u2="&#x390;" k="20" />
    <hkern u1="&#x398;" u2="&#x3bb;" k="30" />
    <hkern u1="&#x398;" u2="&#x3ab;" k="20" />
    <hkern u1="&#x398;" u2="&#x3a7;" k="15" />
    <hkern u1="&#x398;" u2="&#x3a5;" k="20" />
    <hkern u1="&#x398;" u2="&#x3a4;" k="15" />
    <hkern u1="&#x398;" u2="&#x3a3;" k="30" />
    <hkern u1="&#x398;" u2="&#x39e;" k="25" />
    <hkern u1="&#x398;" u2="&#x39b;" k="10" />
    <hkern u1="&#x398;" u2="&#x394;" k="10" />
    <hkern u1="&#x398;" u2="&#x391;" k="10" />
    <hkern u1="&#x39a;" u2="&#x3cd;" k="15" />
    <hkern u1="&#x39a;" u2="&#x3cc;" k="15" />
    <hkern u1="&#x39a;" u2="&#x3cb;" k="15" />
    <hkern u1="&#x39a;" u2="&#x3c7;" k="25" />
    <hkern u1="&#x39a;" u2="&#x3c6;" k="15" />
    <hkern u1="&#x39a;" u2="&#x3c5;" k="15" />
    <hkern u1="&#x39a;" u2="&#x3c4;" k="65" />
    <hkern u1="&#x39a;" u2="&#x3c3;" k="15" />
    <hkern u1="&#x39a;" u2="&#x3c0;" k="68" />
    <hkern u1="&#x39a;" u2="&#x3bf;" k="15" />
    <hkern u1="&#x39a;" u2="&#x3bd;" k="30" />
    <hkern u1="&#x39a;" u2="&#x3b5;" k="15" />
    <hkern u1="&#x39a;" u2="&#x3b4;" k="15" />
    <hkern u1="&#x39a;" u2="&#x3b3;" k="30" />
    <hkern u1="&#x39a;" u2="&#x3b1;" k="15" />
    <hkern u1="&#x39a;" u2="&#x3b0;" k="15" />
    <hkern u1="&#x39a;" u2="&#x3ad;" k="15" />
    <hkern u1="&#x39a;" u2="&#x3ac;" k="15" />
    <hkern u1="&#x39a;" u2="&#x3a6;" k="55" />
    <hkern u1="&#x39a;" u2="&#x39f;" k="25" />
    <hkern u1="&#x39a;" u2="&#x398;" k="25" />
    <hkern u1="&#x39b;" u2="&#x201d;" k="34" />
    <hkern u1="&#x39b;" u2="&#x2019;" k="85" />
    <hkern u1="&#x39b;" u2="&#x3cd;" k="15" />
    <hkern u1="&#x39b;" u2="&#x3cb;" k="15" />
    <hkern u1="&#x39b;" u2="&#x3c7;" k="30" />
    <hkern u1="&#x39b;" u2="&#x3c6;" k="10" />
    <hkern u1="&#x39b;" u2="&#x3c5;" k="15" />
    <hkern u1="&#x39b;" u2="&#x3c4;" k="88" />
    <hkern u1="&#x39b;" u2="&#x3c0;" k="63" />
    <hkern u1="&#x39b;" u2="&#x3bd;" k="40" />
    <hkern u1="&#x39b;" u2="&#x3b3;" k="40" />
    <hkern u1="&#x39b;" u2="&#x3b0;" k="15" />
    <hkern u1="&#x39b;" u2="&#x3ab;" k="80" />
    <hkern u1="&#x39b;" u2="&#x3a8;" k="40" />
    <hkern u1="&#x39b;" u2="&#x3a6;" k="10" />
    <hkern u1="&#x39b;" u2="&#x3a5;" k="80" />
    <hkern u1="&#x39b;" u2="&#x3a4;" k="80" />
    <hkern u1="&#x39b;" u2="&#x39f;" k="10" />
    <hkern u1="&#x39b;" u2="&#x398;" k="10" />
    <hkern u1="&#x39e;" u2="&#x3ce;" k="20" />
    <hkern u1="&#x39e;" u2="&#x3cd;" k="20" />
    <hkern u1="&#x39e;" u2="&#x3cc;" k="25" />
    <hkern u1="&#x39e;" u2="&#x3cb;" k="20" />
    <hkern u1="&#x39e;" u2="&#x3ca;" k="20" />
    <hkern u1="&#x39e;" u2="&#x3c9;" k="20" />
    <hkern u1="&#x39e;" u2="&#x3c5;" k="20" />
    <hkern u1="&#x39e;" u2="&#x3c3;" k="25" />
    <hkern u1="&#x39e;" u2="&#x3bf;" k="25" />
    <hkern u1="&#x39e;" u2="&#x3b9;" k="20" />
    <hkern u1="&#x39e;" u2="&#x3b5;" k="25" />
    <hkern u1="&#x39e;" u2="&#x3b1;" k="20" />
    <hkern u1="&#x39e;" u2="&#x3b0;" k="20" />
    <hkern u1="&#x39e;" u2="&#x3af;" k="20" />
    <hkern u1="&#x39e;" u2="&#x3ad;" k="25" />
    <hkern u1="&#x39e;" u2="&#x3ac;" k="20" />
    <hkern u1="&#x39e;" u2="&#x3a6;" k="20" />
    <hkern u1="&#x39e;" u2="&#x39f;" k="20" />
    <hkern u1="&#x39e;" u2="&#x398;" k="20" />
    <hkern u1="&#x39e;" u2="&#x390;" k="20" />
    <hkern u1="&#x39f;" u2="&#x3bb;" k="30" />
    <hkern u1="&#x39f;" u2="&#x3ab;" k="20" />
    <hkern u1="&#x39f;" u2="&#x3a7;" k="15" />
    <hkern u1="&#x39f;" u2="&#x3a5;" k="20" />
    <hkern u1="&#x39f;" u2="&#x3a4;" k="15" />
    <hkern u1="&#x39f;" u2="&#x3a3;" k="30" />
    <hkern u1="&#x39f;" u2="&#x39e;" k="25" />
    <hkern u1="&#x39f;" u2="&#x39b;" k="10" />
    <hkern u1="&#x39f;" u2="&#x394;" k="10" />
    <hkern u1="&#x39f;" u2="&#x391;" k="10" />
    <hkern u1="&#x3a1;" u2="&#x2026;" k="107" />
    <hkern u1="&#x3a1;" u2="&#x3ce;" k="20" />
    <hkern u1="&#x3a1;" u2="&#x3cc;" k="20" />
    <hkern u1="&#x3a1;" u2="&#x3ca;" k="15" />
    <hkern u1="&#x3a1;" u2="&#x3c9;" k="20" />
    <hkern u1="&#x3a1;" u2="&#x3c6;" k="25" />
    <hkern u1="&#x3a1;" u2="&#x3c3;" k="25" />
    <hkern u1="&#x3a1;" u2="&#x3c1;" k="30" />
    <hkern u1="&#x3a1;" u2="&#x3bf;" k="20" />
    <hkern u1="&#x3a1;" u2="&#x3bb;" k="20" />
    <hkern u1="&#x3a1;" u2="&#x3b9;" k="15" />
    <hkern u1="&#x3a1;" u2="&#x3b6;" k="20" />
    <hkern u1="&#x3a1;" u2="&#x3b5;" k="25" />
    <hkern u1="&#x3a1;" u2="&#x3b4;" k="20" />
    <hkern u1="&#x3a1;" u2="&#x3b1;" k="20" />
    <hkern u1="&#x3a1;" u2="&#x3af;" k="15" />
    <hkern u1="&#x3a1;" u2="&#x3ad;" k="25" />
    <hkern u1="&#x3a1;" u2="&#x3ac;" k="20" />
    <hkern u1="&#x3a1;" u2="&#x39b;" k="40" />
    <hkern u1="&#x3a1;" u2="&#x396;" k="30" />
    <hkern u1="&#x3a1;" u2="&#x394;" k="40" />
    <hkern u1="&#x3a1;" u2="&#x391;" k="40" />
    <hkern u1="&#x3a1;" u2="&#x390;" k="15" />
    <hkern u1="&#x3a1;" u2="&#x2e;" k="107" />
    <hkern u1="&#x3a1;" u2="&#x2c;" k="107" />
    <hkern u1="&#x3a3;" u2="&#x3c4;" k="44" />
    <hkern u1="&#x3a3;" u2="&#x3c0;" k="20" />
    <hkern u1="&#x3a3;" u2="&#x3bd;" k="25" />
    <hkern u1="&#x3a3;" u2="&#x3b4;" k="10" />
    <hkern u1="&#x3a3;" u2="&#x3b3;" k="20" />
    <hkern u1="&#x3a3;" u2="&#x3a6;" k="25" />
    <hkern u1="&#x3a3;" u2="&#x39f;" k="25" />
    <hkern u1="&#x3a3;" u2="&#x398;" k="25" />
    <hkern u1="&#x3a4;" u2="&#x203a;" k="34" />
    <hkern u1="&#x3a4;" u2="&#x2026;" k="65" />
    <hkern u1="&#x3a4;" u2="&#x2014;" k="70" />
    <hkern u1="&#x3a4;" u2="&#x2013;" k="70" />
    <hkern u1="&#x3a4;" u2="&#x3ce;" k="81" />
    <hkern u1="&#x3a4;" u2="&#x3cd;" k="43" />
    <hkern u1="&#x3a4;" u2="&#x3cc;" k="77" />
    <hkern u1="&#x3a4;" u2="&#x3cb;" k="43" />
    <hkern u1="&#x3a4;" u2="&#x3ca;" k="86" />
    <hkern u1="&#x3a4;" u2="&#x3c9;" k="81" />
    <hkern u1="&#x3a4;" u2="&#x3c8;" k="73" />
    <hkern u1="&#x3a4;" u2="&#x3c7;" k="67" />
    <hkern u1="&#x3a4;" u2="&#x3c6;" k="93" />
    <hkern u1="&#x3a4;" u2="&#x3c5;" k="43" />
    <hkern u1="&#x3a4;" u2="&#x3c3;" k="81" />
    <hkern u1="&#x3a4;" u2="&#x3c1;" k="103" />
    <hkern u1="&#x3a4;" u2="&#x3c0;" k="73" />
    <hkern u1="&#x3a4;" u2="&#x3bf;" k="77" />
    <hkern u1="&#x3a4;" u2="&#x3bc;" k="74" />
    <hkern u1="&#x3a4;" u2="&#x3ba;" k="78" />
    <hkern u1="&#x3a4;" u2="&#x3b9;" k="86" />
    <hkern u1="&#x3a4;" u2="&#x3b7;" k="51" />
    <hkern u1="&#x3a4;" u2="&#x3b6;" k="22" />
    <hkern u1="&#x3a4;" u2="&#x3b5;" k="83" />
    <hkern u1="&#x3a4;" u2="&#x3b4;" k="44" />
    <hkern u1="&#x3a4;" u2="&#x3b1;" k="85" />
    <hkern u1="&#x3a4;" u2="&#x3b0;" k="43" />
    <hkern u1="&#x3a4;" u2="&#x3af;" k="86" />
    <hkern u1="&#x3a4;" u2="&#x3ae;" k="51" />
    <hkern u1="&#x3a4;" u2="&#x3ad;" k="83" />
    <hkern u1="&#x3a4;" u2="&#x3ac;" k="85" />
    <hkern u1="&#x3a4;" u2="&#x3ab;" k="-25" />
    <hkern u1="&#x3a4;" u2="&#x3a9;" k="10" />
    <hkern u1="&#x3a4;" u2="&#x3a6;" k="65" />
    <hkern u1="&#x3a4;" u2="&#x3a5;" k="-25" />
    <hkern u1="&#x3a4;" u2="&#x39f;" k="15" />
    <hkern u1="&#x3a4;" u2="&#x39b;" k="80" />
    <hkern u1="&#x3a4;" u2="&#x398;" k="15" />
    <hkern u1="&#x3a4;" u2="&#x394;" k="80" />
    <hkern u1="&#x3a4;" u2="&#x391;" k="80" />
    <hkern u1="&#x3a4;" u2="&#x390;" k="86" />
    <hkern u1="&#x3a4;" u2="&#xbb;" k="34" />
    <hkern u1="&#x3a4;" u2="&#xb5;" k="64" />
    <hkern u1="&#x3a4;" u2="&#x2e;" k="65" />
    <hkern u1="&#x3a4;" u2="&#x2d;" k="70" />
    <hkern u1="&#x3a4;" u2="&#x2c;" k="65" />
    <hkern u1="&#x3a5;" u2="&#x203a;" k="45" />
    <hkern u1="&#x3a5;" u2="&#x2026;" k="100" />
    <hkern u1="&#x3a5;" u2="&#x2014;" k="56" />
    <hkern u1="&#x3a5;" u2="&#x2013;" k="56" />
    <hkern u1="&#x3a5;" u2="&#x3ce;" k="70" />
    <hkern u1="&#x3a5;" u2="&#x3cd;" k="20" />
    <hkern u1="&#x3a5;" u2="&#x3cc;" k="80" />
    <hkern u1="&#x3a5;" u2="&#x3cb;" k="20" />
    <hkern u1="&#x3a5;" u2="&#x3ca;" k="40" />
    <hkern u1="&#x3a5;" u2="&#x3c9;" k="70" />
    <hkern u1="&#x3a5;" u2="&#x3c8;" k="30" />
    <hkern u1="&#x3a5;" u2="&#x3c6;" k="90" />
    <hkern u1="&#x3a5;" u2="&#x3c5;" k="20" />
    <hkern u1="&#x3a5;" u2="&#x3c4;" k="59" />
    <hkern u1="&#x3a5;" u2="&#x3c3;" k="90" />
    <hkern u1="&#x3a5;" u2="&#x3c1;" k="100" />
    <hkern u1="&#x3a5;" u2="&#x3c0;" k="54" />
    <hkern u1="&#x3a5;" u2="&#x3bf;" k="80" />
    <hkern u1="&#x3a5;" u2="&#x3ba;" k="40" />
    <hkern u1="&#x3a5;" u2="&#x3b9;" k="40" />
    <hkern u1="&#x3a5;" u2="&#x3b5;" k="80" />
    <hkern u1="&#x3a5;" u2="&#x3b4;" k="20" />
    <hkern u1="&#x3a5;" u2="&#x3b2;" k="20" />
    <hkern u1="&#x3a5;" u2="&#x3b1;" k="75" />
    <hkern u1="&#x3a5;" u2="&#x3b0;" k="20" />
    <hkern u1="&#x3a5;" u2="&#x3af;" k="40" />
    <hkern u1="&#x3a5;" u2="&#x3ad;" k="80" />
    <hkern u1="&#x3a5;" u2="&#x3ac;" k="75" />
    <hkern u1="&#x3a5;" u2="&#x3a9;" k="20" />
    <hkern u1="&#x3a5;" u2="&#x3a6;" k="40" />
    <hkern u1="&#x3a5;" u2="&#x3a4;" k="-20" />
    <hkern u1="&#x3a5;" u2="&#x39f;" k="30" />
    <hkern u1="&#x3a5;" u2="&#x39b;" k="90" />
    <hkern u1="&#x3a5;" u2="&#x398;" k="30" />
    <hkern u1="&#x3a5;" u2="&#x394;" k="90" />
    <hkern u1="&#x3a5;" u2="&#x391;" k="90" />
    <hkern u1="&#x3a5;" u2="&#x390;" k="40" />
    <hkern u1="&#x3a5;" u2="&#xbb;" k="45" />
    <hkern u1="&#x3a5;" u2="&#xb5;" k="50" />
    <hkern u1="&#x3a5;" u2="&#x2e;" k="100" />
    <hkern u1="&#x3a5;" u2="&#x2d;" k="56" />
    <hkern u1="&#x3a5;" u2="&#x2c;" k="100" />
    <hkern u1="&#x3a6;" u2="&#x2026;" k="40" />
    <hkern u1="&#x3a6;" u2="&#x3c1;" k="15" />
    <hkern u1="&#x3a6;" u2="&#x3bb;" k="35" />
    <hkern u1="&#x3a6;" u2="&#x3b5;" k="10" />
    <hkern u1="&#x3a6;" u2="&#x3ad;" k="10" />
    <hkern u1="&#x3a6;" u2="&#x3ab;" k="30" />
    <hkern u1="&#x3a6;" u2="&#x3a7;" k="20" />
    <hkern u1="&#x3a6;" u2="&#x3a5;" k="30" />
    <hkern u1="&#x3a6;" u2="&#x3a4;" k="55" />
    <hkern u1="&#x3a6;" u2="&#x3a3;" k="35" />
    <hkern u1="&#x3a6;" u2="&#x39e;" k="20" />
    <hkern u1="&#x3a6;" u2="&#x39b;" k="10" />
    <hkern u1="&#x3a6;" u2="&#x394;" k="10" />
    <hkern u1="&#x3a6;" u2="&#x391;" k="10" />
    <hkern u1="&#x3a6;" u2="&#x2e;" k="40" />
    <hkern u1="&#x3a6;" u2="&#x2c;" k="40" />
    <hkern u1="&#x3a7;" u2="&#x3ce;" k="15" />
    <hkern u1="&#x3a7;" u2="&#x3cd;" k="25" />
    <hkern u1="&#x3a7;" u2="&#x3cc;" k="20" />
    <hkern u1="&#x3a7;" u2="&#x3cb;" k="25" />
    <hkern u1="&#x3a7;" u2="&#x3c9;" k="15" />
    <hkern u1="&#x3a7;" u2="&#x3c7;" k="20" />
    <hkern u1="&#x3a7;" u2="&#x3c6;" k="20" />
    <hkern u1="&#x3a7;" u2="&#x3c5;" k="25" />
    <hkern u1="&#x3a7;" u2="&#x3c4;" k="50" />
    <hkern u1="&#x3a7;" u2="&#x3c3;" k="20" />
    <hkern u1="&#x3a7;" u2="&#x3c0;" k="69" />
    <hkern u1="&#x3a7;" u2="&#x3bf;" k="20" />
    <hkern u1="&#x3a7;" u2="&#x3bd;" k="30" />
    <hkern u1="&#x3a7;" u2="&#x3b0;" k="25" />
    <hkern u1="&#x3a7;" u2="&#x3a6;" k="20" />
    <hkern u1="&#x3a7;" u2="&#x39f;" k="15" />
    <hkern u1="&#x3a7;" u2="&#x398;" k="15" />
    <hkern u1="&#x3a8;" u2="&#x2026;" k="77" />
    <hkern u1="&#x3a8;" u2="&#x3ce;" k="25" />
    <hkern u1="&#x3a8;" u2="&#x3cc;" k="25" />
    <hkern u1="&#x3a8;" u2="&#x3ca;" k="20" />
    <hkern u1="&#x3a8;" u2="&#x3c9;" k="25" />
    <hkern u1="&#x3a8;" u2="&#x3c6;" k="25" />
    <hkern u1="&#x3a8;" u2="&#x3c3;" k="25" />
    <hkern u1="&#x3a8;" u2="&#x3c1;" k="55" />
    <hkern u1="&#x3a8;" u2="&#x3bf;" k="25" />
    <hkern u1="&#x3a8;" u2="&#x3b9;" k="20" />
    <hkern u1="&#x3a8;" u2="&#x3b6;" k="20" />
    <hkern u1="&#x3a8;" u2="&#x3b5;" k="25" />
    <hkern u1="&#x3a8;" u2="&#x3b4;" k="25" />
    <hkern u1="&#x3a8;" u2="&#x3b2;" k="30" />
    <hkern u1="&#x3a8;" u2="&#x3b1;" k="25" />
    <hkern u1="&#x3a8;" u2="&#x3af;" k="20" />
    <hkern u1="&#x3a8;" u2="&#x3ad;" k="25" />
    <hkern u1="&#x3a8;" u2="&#x3ac;" k="25" />
    <hkern u1="&#x3a8;" u2="&#x39b;" k="45" />
    <hkern u1="&#x3a8;" u2="&#x394;" k="45" />
    <hkern u1="&#x3a8;" u2="&#x391;" k="45" />
    <hkern u1="&#x3a8;" u2="&#x390;" k="20" />
    <hkern u1="&#x3a8;" u2="&#x2e;" k="77" />
    <hkern u1="&#x3a8;" u2="&#x2c;" k="77" />
    <hkern u1="&#x3a9;" u2="&#x3ab;" k="20" />
    <hkern u1="&#x3a9;" u2="&#x3a5;" k="20" />
    <hkern u1="&#x3a9;" u2="&#x3a4;" k="10" />
    <hkern u1="&#x3ab;" u2="&#x203a;" k="45" />
    <hkern u1="&#x3ab;" u2="&#x2026;" k="100" />
    <hkern u1="&#x3ab;" u2="&#x2014;" k="56" />
    <hkern u1="&#x3ab;" u2="&#x2013;" k="56" />
    <hkern u1="&#x3ab;" u2="&#x3ce;" k="70" />
    <hkern u1="&#x3ab;" u2="&#x3cd;" k="20" />
    <hkern u1="&#x3ab;" u2="&#x3cc;" k="80" />
    <hkern u1="&#x3ab;" u2="&#x3cb;" k="20" />
    <hkern u1="&#x3ab;" u2="&#x3ca;" k="40" />
    <hkern u1="&#x3ab;" u2="&#x3c9;" k="70" />
    <hkern u1="&#x3ab;" u2="&#x3c8;" k="30" />
    <hkern u1="&#x3ab;" u2="&#x3c6;" k="90" />
    <hkern u1="&#x3ab;" u2="&#x3c5;" k="20" />
    <hkern u1="&#x3ab;" u2="&#x3c4;" k="59" />
    <hkern u1="&#x3ab;" u2="&#x3c3;" k="90" />
    <hkern u1="&#x3ab;" u2="&#x3c1;" k="100" />
    <hkern u1="&#x3ab;" u2="&#x3c0;" k="54" />
    <hkern u1="&#x3ab;" u2="&#x3bf;" k="80" />
    <hkern u1="&#x3ab;" u2="&#x3ba;" k="40" />
    <hkern u1="&#x3ab;" u2="&#x3b9;" k="40" />
    <hkern u1="&#x3ab;" u2="&#x3b5;" k="80" />
    <hkern u1="&#x3ab;" u2="&#x3b4;" k="20" />
    <hkern u1="&#x3ab;" u2="&#x3b2;" k="20" />
    <hkern u1="&#x3ab;" u2="&#x3b1;" k="75" />
    <hkern u1="&#x3ab;" u2="&#x3b0;" k="20" />
    <hkern u1="&#x3ab;" u2="&#x3af;" k="40" />
    <hkern u1="&#x3ab;" u2="&#x3ad;" k="80" />
    <hkern u1="&#x3ab;" u2="&#x3ac;" k="75" />
    <hkern u1="&#x3ab;" u2="&#x3a9;" k="20" />
    <hkern u1="&#x3ab;" u2="&#x3a6;" k="40" />
    <hkern u1="&#x3ab;" u2="&#x3a4;" k="-20" />
    <hkern u1="&#x3ab;" u2="&#x39f;" k="30" />
    <hkern u1="&#x3ab;" u2="&#x39b;" k="90" />
    <hkern u1="&#x3ab;" u2="&#x398;" k="30" />
    <hkern u1="&#x3ab;" u2="&#x394;" k="90" />
    <hkern u1="&#x3ab;" u2="&#x391;" k="90" />
    <hkern u1="&#x3ab;" u2="&#x390;" k="40" />
    <hkern u1="&#x3ab;" u2="&#xbb;" k="45" />
    <hkern u1="&#x3ab;" u2="&#xb5;" k="50" />
    <hkern u1="&#x3ab;" u2="&#x2e;" k="100" />
    <hkern u1="&#x3ab;" u2="&#x2d;" k="56" />
    <hkern u1="&#x3ab;" u2="&#x2c;" k="100" />
    <hkern u1="&#x3ac;" u2="&#x3ce;" k="10" />
    <hkern u1="&#x3ac;" u2="&#x3cc;" k="10" />
    <hkern u1="&#x3ac;" u2="&#x3c9;" k="10" />
    <hkern u1="&#x3ac;" u2="&#x3c8;" k="10" />
    <hkern u1="&#x3ac;" u2="&#x3c6;" k="25" />
    <hkern u1="&#x3ac;" u2="&#x3c4;" k="20" />
    <hkern u1="&#x3ac;" u2="&#x3c3;" k="15" />
    <hkern u1="&#x3ac;" u2="&#x3c2;" k="15" />
    <hkern u1="&#x3ac;" u2="&#x3c0;" k="10" />
    <hkern u1="&#x3ac;" u2="&#x3bf;" k="10" />
    <hkern u1="&#x3ac;" u2="&#x3b6;" k="10" />
    <hkern u1="&#x3ac;" u2="&#x3b4;" k="15" />
    <hkern u1="&#x3ad;" u2="&#x3ce;" k="20" />
    <hkern u1="&#x3ad;" u2="&#x3cc;" k="15" />
    <hkern u1="&#x3ad;" u2="&#x3ca;" k="20" />
    <hkern u1="&#x3ad;" u2="&#x3c9;" k="20" />
    <hkern u1="&#x3ad;" u2="&#x3c8;" k="20" />
    <hkern u1="&#x3ad;" u2="&#x3c6;" k="25" />
    <hkern u1="&#x3ad;" u2="&#x3c3;" k="15" />
    <hkern u1="&#x3ad;" u2="&#x3c2;" k="25" />
    <hkern u1="&#x3ad;" u2="&#x3c1;" k="25" />
    <hkern u1="&#x3ad;" u2="&#x3bf;" k="15" />
    <hkern u1="&#x3ad;" u2="&#x3be;" k="20" />
    <hkern u1="&#x3ad;" u2="&#x3ba;" k="20" />
    <hkern u1="&#x3ad;" u2="&#x3b9;" k="20" />
    <hkern u1="&#x3ad;" u2="&#x3b8;" k="15" />
    <hkern u1="&#x3ad;" u2="&#x3b6;" k="25" />
    <hkern u1="&#x3ad;" u2="&#x3b4;" k="20" />
    <hkern u1="&#x3ad;" u2="&#x3b2;" k="10" />
    <hkern u1="&#x3ad;" u2="&#x3b1;" k="15" />
    <hkern u1="&#x3ad;" u2="&#x3af;" k="20" />
    <hkern u1="&#x3ad;" u2="&#x3ac;" k="15" />
    <hkern u1="&#x3ad;" u2="&#x390;" k="20" />
    <hkern u1="&#x3ae;" u2="&#x3c4;" k="10" />
    <hkern u1="&#x3ae;" u2="&#x3c0;" k="15" />
    <hkern u1="&#x3ae;" u2="&#x3bd;" k="10" />
    <hkern u1="&#x3ae;" u2="&#x3b3;" k="10" />
    <hkern u1="&#x3af;" u2="&#x3cc;" k="10" />
    <hkern u1="&#x3af;" u2="&#x3c6;" k="10" />
    <hkern u1="&#x3af;" u2="&#x3c4;" k="20" />
    <hkern u1="&#x3af;" u2="&#x3c3;" k="5" />
    <hkern u1="&#x3af;" u2="&#x3c0;" k="20" />
    <hkern u1="&#x3af;" u2="&#x3bf;" k="10" />
    <hkern u1="&#x3af;" u2="&#x3b8;" k="5" />
    <hkern u1="&#x3af;" u2="&#x3b4;" k="5" />
    <hkern u1="&#x3af;" u2="&#x3b1;" k="7" />
    <hkern u1="&#x3af;" u2="&#x3ac;" k="7" />
    <hkern u1="&#x3b0;" u2="&#x3ce;" k="5" />
    <hkern u1="&#x3b0;" u2="&#x3c9;" k="5" />
    <hkern u1="&#x3b0;" u2="&#x3c1;" k="30" />
    <hkern u1="&#x3b0;" u2="&#x3bb;" k="15" />
    <hkern u1="&#x3b0;" u2="&#x3b5;" k="13" />
    <hkern u1="&#x3b0;" u2="&#x3b1;" k="10" />
    <hkern u1="&#x3b0;" u2="&#x3ad;" k="13" />
    <hkern u1="&#x3b0;" u2="&#x3ac;" k="10" />
    <hkern u1="&#x3b1;" u2="&#x3ce;" k="10" />
    <hkern u1="&#x3b1;" u2="&#x3cc;" k="10" />
    <hkern u1="&#x3b1;" u2="&#x3c9;" k="10" />
    <hkern u1="&#x3b1;" u2="&#x3c8;" k="10" />
    <hkern u1="&#x3b1;" u2="&#x3c6;" k="25" />
    <hkern u1="&#x3b1;" u2="&#x3c4;" k="20" />
    <hkern u1="&#x3b1;" u2="&#x3c3;" k="15" />
    <hkern u1="&#x3b1;" u2="&#x3c2;" k="15" />
    <hkern u1="&#x3b1;" u2="&#x3c0;" k="10" />
    <hkern u1="&#x3b1;" u2="&#x3bf;" k="10" />
    <hkern u1="&#x3b1;" u2="&#x3b6;" k="10" />
    <hkern u1="&#x3b1;" u2="&#x3b4;" k="15" />
    <hkern u1="&#x3b2;" u2="&#x3ca;" k="10" />
    <hkern u1="&#x3b2;" u2="&#x3c7;" k="10" />
    <hkern u1="&#x3b2;" u2="&#x3c4;" k="35" />
    <hkern u1="&#x3b2;" u2="&#x3c0;" k="30" />
    <hkern u1="&#x3b2;" u2="&#x3bd;" k="20" />
    <hkern u1="&#x3b2;" u2="&#x3bb;" k="7" />
    <hkern u1="&#x3b2;" u2="&#x3b9;" k="10" />
    <hkern u1="&#x3b2;" u2="&#x3b3;" k="20" />
    <hkern u1="&#x3b2;" u2="&#x3af;" k="10" />
    <hkern u1="&#x3b2;" u2="&#x390;" k="10" />
    <hkern u1="&#x3b3;" u2="&#x2026;" k="60" />
    <hkern u1="&#x3b3;" u2="&#x2014;" k="30" />
    <hkern u1="&#x3b3;" u2="&#x2013;" k="30" />
    <hkern u1="&#x3b3;" u2="&#x3ce;" k="20" />
    <hkern u1="&#x3b3;" u2="&#x3cc;" k="20" />
    <hkern u1="&#x3b3;" u2="&#x3ca;" k="25" />
    <hkern u1="&#x3b3;" u2="&#x3c9;" k="20" />
    <hkern u1="&#x3b3;" u2="&#x3c6;" k="20" />
    <hkern u1="&#x3b3;" u2="&#x3c3;" k="20" />
    <hkern u1="&#x3b3;" u2="&#x3c2;" k="10" />
    <hkern u1="&#x3b3;" u2="&#x3c1;" k="35" />
    <hkern u1="&#x3b3;" u2="&#x3bf;" k="20" />
    <hkern u1="&#x3b3;" u2="&#x3be;" k="20" />
    <hkern u1="&#x3b3;" u2="&#x3bb;" k="50" />
    <hkern u1="&#x3b3;" u2="&#x3ba;" k="10" />
    <hkern u1="&#x3b3;" u2="&#x3b9;" k="25" />
    <hkern u1="&#x3b3;" u2="&#x3b6;" k="20" />
    <hkern u1="&#x3b3;" u2="&#x3b5;" k="10" />
    <hkern u1="&#x3b3;" u2="&#x3b4;" k="30" />
    <hkern u1="&#x3b3;" u2="&#x3b2;" k="15" />
    <hkern u1="&#x3b3;" u2="&#x3b1;" k="30" />
    <hkern u1="&#x3b3;" u2="&#x3af;" k="25" />
    <hkern u1="&#x3b3;" u2="&#x3ad;" k="10" />
    <hkern u1="&#x3b3;" u2="&#x3ac;" k="30" />
    <hkern u1="&#x3b3;" u2="&#x390;" k="25" />
    <hkern u1="&#x3b3;" u2="&#xb5;" k="15" />
    <hkern u1="&#x3b3;" u2="&#x2e;" k="60" />
    <hkern u1="&#x3b3;" u2="&#x2d;" k="30" />
    <hkern u1="&#x3b3;" u2="&#x2c;" k="60" />
    <hkern u1="&#x3b4;" u2="&#x3c0;" k="10" />
    <hkern u1="&#x3b4;" u2="&#x3b7;" k="10" />
    <hkern u1="&#x3b4;" u2="&#x3b3;" k="10" />
    <hkern u1="&#x3b4;" u2="&#x3ae;" k="10" />
    <hkern u1="&#x3b5;" u2="&#x3ce;" k="20" />
    <hkern u1="&#x3b5;" u2="&#x3cc;" k="15" />
    <hkern u1="&#x3b5;" u2="&#x3ca;" k="20" />
    <hkern u1="&#x3b5;" u2="&#x3c9;" k="20" />
    <hkern u1="&#x3b5;" u2="&#x3c8;" k="20" />
    <hkern u1="&#x3b5;" u2="&#x3c6;" k="25" />
    <hkern u1="&#x3b5;" u2="&#x3c3;" k="15" />
    <hkern u1="&#x3b5;" u2="&#x3c2;" k="25" />
    <hkern u1="&#x3b5;" u2="&#x3c1;" k="25" />
    <hkern u1="&#x3b5;" u2="&#x3bf;" k="15" />
    <hkern u1="&#x3b5;" u2="&#x3be;" k="20" />
    <hkern u1="&#x3b5;" u2="&#x3ba;" k="20" />
    <hkern u1="&#x3b5;" u2="&#x3b9;" k="20" />
    <hkern u1="&#x3b5;" u2="&#x3b8;" k="15" />
    <hkern u1="&#x3b5;" u2="&#x3b6;" k="25" />
    <hkern u1="&#x3b5;" u2="&#x3b4;" k="20" />
    <hkern u1="&#x3b5;" u2="&#x3b2;" k="10" />
    <hkern u1="&#x3b5;" u2="&#x3b1;" k="15" />
    <hkern u1="&#x3b5;" u2="&#x3af;" k="20" />
    <hkern u1="&#x3b5;" u2="&#x3ac;" k="15" />
    <hkern u1="&#x3b5;" u2="&#x390;" k="20" />
    <hkern u1="&#x3b6;" u2="&#x2014;" k="48" />
    <hkern u1="&#x3b6;" u2="&#x2013;" k="48" />
    <hkern u1="&#x3b6;" u2="&#x3ce;" k="15" />
    <hkern u1="&#x3b6;" u2="&#x3cd;" k="10" />
    <hkern u1="&#x3b6;" u2="&#x3cc;" k="15" />
    <hkern u1="&#x3b6;" u2="&#x3cb;" k="10" />
    <hkern u1="&#x3b6;" u2="&#x3c9;" k="15" />
    <hkern u1="&#x3b6;" u2="&#x3c8;" k="25" />
    <hkern u1="&#x3b6;" u2="&#x3c6;" k="20" />
    <hkern u1="&#x3b6;" u2="&#x3c5;" k="10" />
    <hkern u1="&#x3b6;" u2="&#x3c4;" k="50" />
    <hkern u1="&#x3b6;" u2="&#x3c3;" k="25" />
    <hkern u1="&#x3b6;" u2="&#x3c2;" k="25" />
    <hkern u1="&#x3b6;" u2="&#x3c0;" k="25" />
    <hkern u1="&#x3b6;" u2="&#x3bf;" k="15" />
    <hkern u1="&#x3b6;" u2="&#x3b1;" k="20" />
    <hkern u1="&#x3b6;" u2="&#x3b0;" k="10" />
    <hkern u1="&#x3b6;" u2="&#x3ac;" k="20" />
    <hkern u1="&#x3b6;" u2="&#x2d;" k="48" />
    <hkern u1="&#x3b7;" u2="&#x3c4;" k="10" />
    <hkern u1="&#x3b7;" u2="&#x3c0;" k="15" />
    <hkern u1="&#x3b7;" u2="&#x3bd;" k="10" />
    <hkern u1="&#x3b7;" u2="&#x3b3;" k="10" />
    <hkern u1="&#x3b8;" u2="&#x3bb;" k="20" />
    <hkern u1="&#x3b9;" u2="&#x3cc;" k="10" />
    <hkern u1="&#x3b9;" u2="&#x3c6;" k="10" />
    <hkern u1="&#x3b9;" u2="&#x3c4;" k="20" />
    <hkern u1="&#x3b9;" u2="&#x3c3;" k="5" />
    <hkern u1="&#x3b9;" u2="&#x3c0;" k="20" />
    <hkern u1="&#x3b9;" u2="&#x3bf;" k="10" />
    <hkern u1="&#x3b9;" u2="&#x3b8;" k="5" />
    <hkern u1="&#x3b9;" u2="&#x3b4;" k="5" />
    <hkern u1="&#x3b9;" u2="&#x3b1;" k="7" />
    <hkern u1="&#x3b9;" u2="&#x3ac;" k="7" />
    <hkern u1="&#x3ba;" u2="&#x2014;" k="43" />
    <hkern u1="&#x3ba;" u2="&#x2013;" k="43" />
    <hkern u1="&#x3ba;" u2="&#x3ce;" k="25" />
    <hkern u1="&#x3ba;" u2="&#x3cc;" k="25" />
    <hkern u1="&#x3ba;" u2="&#x3c9;" k="25" />
    <hkern u1="&#x3ba;" u2="&#x3c6;" k="35" />
    <hkern u1="&#x3ba;" u2="&#x3c4;" k="15" />
    <hkern u1="&#x3ba;" u2="&#x3c3;" k="25" />
    <hkern u1="&#x3ba;" u2="&#x3c2;" k="25" />
    <hkern u1="&#x3ba;" u2="&#x3c1;" k="20" />
    <hkern u1="&#x3ba;" u2="&#x3c0;" k="15" />
    <hkern u1="&#x3ba;" u2="&#x3bf;" k="25" />
    <hkern u1="&#x3ba;" u2="&#x3ba;" k="10" />
    <hkern u1="&#x3ba;" u2="&#x3b8;" k="15" />
    <hkern u1="&#x3ba;" u2="&#x3b6;" k="20" />
    <hkern u1="&#x3ba;" u2="&#x3b5;" k="15" />
    <hkern u1="&#x3ba;" u2="&#x3b4;" k="30" />
    <hkern u1="&#x3ba;" u2="&#x3b2;" k="20" />
    <hkern u1="&#x3ba;" u2="&#x3b1;" k="35" />
    <hkern u1="&#x3ba;" u2="&#x3ad;" k="15" />
    <hkern u1="&#x3ba;" u2="&#x3ac;" k="35" />
    <hkern u1="&#x3ba;" u2="&#xb5;" k="15" />
    <hkern u1="&#x3ba;" u2="&#x2d;" k="43" />
    <hkern u1="&#x3bb;" u2="&#x201d;" k="40" />
    <hkern u1="&#x3bb;" u2="&#x2019;" k="40" />
    <hkern u1="&#x3bb;" u2="&#x2014;" k="38" />
    <hkern u1="&#x3bb;" u2="&#x2013;" k="38" />
    <hkern u1="&#x3bb;" u2="&#x3cd;" k="20" />
    <hkern u1="&#x3bb;" u2="&#x3cc;" k="20" />
    <hkern u1="&#x3bb;" u2="&#x3cb;" k="20" />
    <hkern u1="&#x3bb;" u2="&#x3c7;" k="40" />
    <hkern u1="&#x3bb;" u2="&#x3c6;" k="20" />
    <hkern u1="&#x3bb;" u2="&#x3c5;" k="20" />
    <hkern u1="&#x3bb;" u2="&#x3c4;" k="85" />
    <hkern u1="&#x3bb;" u2="&#x3c3;" k="15" />
    <hkern u1="&#x3bb;" u2="&#x3c2;" k="20" />
    <hkern u1="&#x3bb;" u2="&#x3c0;" k="55" />
    <hkern u1="&#x3bb;" u2="&#x3bf;" k="20" />
    <hkern u1="&#x3bb;" u2="&#x3bd;" k="65" />
    <hkern u1="&#x3bb;" u2="&#x3b8;" k="20" />
    <hkern u1="&#x3bb;" u2="&#x3b4;" k="20" />
    <hkern u1="&#x3bb;" u2="&#x3b3;" k="60" />
    <hkern u1="&#x3bb;" u2="&#x3b1;" k="15" />
    <hkern u1="&#x3bb;" u2="&#x3b0;" k="20" />
    <hkern u1="&#x3bb;" u2="&#x3ac;" k="15" />
    <hkern u1="&#x3bb;" u2="&#x2d;" k="38" />
    <hkern u1="&#x3bc;" u2="&#x3c4;" k="10" />
    <hkern u1="&#x3bc;" u2="&#x3c0;" k="20" />
    <hkern u1="&#x3bc;" u2="&#x3b1;" k="10" />
    <hkern u1="&#x3bc;" u2="&#x3ac;" k="10" />
    <hkern u1="&#x3bd;" u2="&#x2026;" k="69" />
    <hkern u1="&#x3bd;" u2="&#x2014;" k="26" />
    <hkern u1="&#x3bd;" u2="&#x2013;" k="26" />
    <hkern u1="&#x3bd;" u2="&#x3ce;" k="30" />
    <hkern u1="&#x3bd;" u2="&#x3cc;" k="20" />
    <hkern u1="&#x3bd;" u2="&#x3ca;" k="15" />
    <hkern u1="&#x3bd;" u2="&#x3c9;" k="30" />
    <hkern u1="&#x3bd;" u2="&#x3c6;" k="25" />
    <hkern u1="&#x3bd;" u2="&#x3c3;" k="25" />
    <hkern u1="&#x3bd;" u2="&#x3c2;" k="20" />
    <hkern u1="&#x3bd;" u2="&#x3c1;" k="45" />
    <hkern u1="&#x3bd;" u2="&#x3bf;" k="20" />
    <hkern u1="&#x3bd;" u2="&#x3be;" k="35" />
    <hkern u1="&#x3bd;" u2="&#x3bb;" k="50" />
    <hkern u1="&#x3bd;" u2="&#x3ba;" k="10" />
    <hkern u1="&#x3bd;" u2="&#x3b9;" k="15" />
    <hkern u1="&#x3bd;" u2="&#x3b8;" k="15" />
    <hkern u1="&#x3bd;" u2="&#x3b6;" k="25" />
    <hkern u1="&#x3bd;" u2="&#x3b5;" k="20" />
    <hkern u1="&#x3bd;" u2="&#x3b4;" k="25" />
    <hkern u1="&#x3bd;" u2="&#x3b2;" k="30" />
    <hkern u1="&#x3bd;" u2="&#x3b1;" k="35" />
    <hkern u1="&#x3bd;" u2="&#x3af;" k="15" />
    <hkern u1="&#x3bd;" u2="&#x3ad;" k="20" />
    <hkern u1="&#x3bd;" u2="&#x3ac;" k="35" />
    <hkern u1="&#x3bd;" u2="&#x390;" k="15" />
    <hkern u1="&#x3bd;" u2="&#x2e;" k="69" />
    <hkern u1="&#x3bd;" u2="&#x2d;" k="26" />
    <hkern u1="&#x3bd;" u2="&#x2c;" k="69" />
    <hkern u1="&#x3be;" u2="&#x3c6;" k="10" />
    <hkern u1="&#x3be;" u2="&#x3b1;" k="15" />
    <hkern u1="&#x3be;" u2="&#x3ac;" k="15" />
    <hkern u1="&#x3bf;" u2="&#x201d;" k="20" />
    <hkern u1="&#x3bf;" u2="&#x2019;" k="20" />
    <hkern u1="&#x3bf;" u2="&#x3c7;" k="10" />
    <hkern u1="&#x3bf;" u2="&#x3c4;" k="10" />
    <hkern u1="&#x3bf;" u2="&#x3bb;" k="10" />
    <hkern u1="&#x3bf;" u2="&#x3b5;" k="5" />
    <hkern u1="&#x3bf;" u2="&#x3ad;" k="5" />
    <hkern u1="&#x3c0;" u2="&#x2014;" k="34" />
    <hkern u1="&#x3c0;" u2="&#x2013;" k="34" />
    <hkern u1="&#x3c0;" u2="&#x3ce;" k="20" />
    <hkern u1="&#x3c0;" u2="&#x3cc;" k="25" />
    <hkern u1="&#x3c0;" u2="&#x3ca;" k="15" />
    <hkern u1="&#x3c0;" u2="&#x3c9;" k="20" />
    <hkern u1="&#x3c0;" u2="&#x3c6;" k="30" />
    <hkern u1="&#x3c0;" u2="&#x3c3;" k="25" />
    <hkern u1="&#x3c0;" u2="&#x3c2;" k="30" />
    <hkern u1="&#x3c0;" u2="&#x3c1;" k="25" />
    <hkern u1="&#x3c0;" u2="&#x3bf;" k="25" />
    <hkern u1="&#x3c0;" u2="&#x3b9;" k="15" />
    <hkern u1="&#x3c0;" u2="&#x3b6;" k="10" />
    <hkern u1="&#x3c0;" u2="&#x3b5;" k="10" />
    <hkern u1="&#x3c0;" u2="&#x3b4;" k="10" />
    <hkern u1="&#x3c0;" u2="&#x3b2;" k="15" />
    <hkern u1="&#x3c0;" u2="&#x3b1;" k="25" />
    <hkern u1="&#x3c0;" u2="&#x3af;" k="15" />
    <hkern u1="&#x3c0;" u2="&#x3ad;" k="10" />
    <hkern u1="&#x3c0;" u2="&#x3ac;" k="25" />
    <hkern u1="&#x3c0;" u2="&#x390;" k="15" />
    <hkern u1="&#x3c0;" u2="&#x2d;" k="34" />
    <hkern u1="&#x3c1;" u2="&#x3c7;" k="15" />
    <hkern u1="&#x3c1;" u2="&#x3bb;" k="15" />
    <hkern u1="&#x3c1;" u2="&#x3b5;" k="7" />
    <hkern u1="&#x3c1;" u2="&#x3ad;" k="7" />
    <hkern u1="&#x3c3;" u2="&#x2026;" k="34" />
    <hkern u1="&#x3c3;" u2="&#x3ce;" k="20" />
    <hkern u1="&#x3c3;" u2="&#x3cc;" k="30" />
    <hkern u1="&#x3c3;" u2="&#x3ca;" k="10" />
    <hkern u1="&#x3c3;" u2="&#x3c9;" k="20" />
    <hkern u1="&#x3c3;" u2="&#x3c6;" k="25" />
    <hkern u1="&#x3c3;" u2="&#x3c3;" k="25" />
    <hkern u1="&#x3c3;" u2="&#x3c2;" k="40" />
    <hkern u1="&#x3c3;" u2="&#x3c1;" k="35" />
    <hkern u1="&#x3c3;" u2="&#x3bf;" k="30" />
    <hkern u1="&#x3c3;" u2="&#x3be;" k="20" />
    <hkern u1="&#x3c3;" u2="&#x3bb;" k="35" />
    <hkern u1="&#x3c3;" u2="&#x3ba;" k="20" />
    <hkern u1="&#x3c3;" u2="&#x3b9;" k="10" />
    <hkern u1="&#x3c3;" u2="&#x3b6;" k="30" />
    <hkern u1="&#x3c3;" u2="&#x3b5;" k="20" />
    <hkern u1="&#x3c3;" u2="&#x3b4;" k="25" />
    <hkern u1="&#x3c3;" u2="&#x3b2;" k="25" />
    <hkern u1="&#x3c3;" u2="&#x3b1;" k="30" />
    <hkern u1="&#x3c3;" u2="&#x3af;" k="10" />
    <hkern u1="&#x3c3;" u2="&#x3ad;" k="20" />
    <hkern u1="&#x3c3;" u2="&#x3ac;" k="30" />
    <hkern u1="&#x3c3;" u2="&#x390;" k="10" />
    <hkern u1="&#x3c3;" u2="&#x2e;" k="34" />
    <hkern u1="&#x3c3;" u2="&#x2c;" k="34" />
    <hkern u1="&#x3c4;" u2="&#x2014;" k="55" />
    <hkern u1="&#x3c4;" u2="&#x2013;" k="55" />
    <hkern u1="&#x3c4;" u2="&#x3ce;" k="45" />
    <hkern u1="&#x3c4;" u2="&#x3cc;" k="45" />
    <hkern u1="&#x3c4;" u2="&#x3ca;" k="20" />
    <hkern u1="&#x3c4;" u2="&#x3c9;" k="45" />
    <hkern u1="&#x3c4;" u2="&#x3c8;" k="30" />
    <hkern u1="&#x3c4;" u2="&#x3c6;" k="50" />
    <hkern u1="&#x3c4;" u2="&#x3c3;" k="45" />
    <hkern u1="&#x3c4;" u2="&#x3c2;" k="40" />
    <hkern u1="&#x3c4;" u2="&#x3c1;" k="50" />
    <hkern u1="&#x3c4;" u2="&#x3bf;" k="45" />
    <hkern u1="&#x3c4;" u2="&#x3be;" k="30" />
    <hkern u1="&#x3c4;" u2="&#x3bc;" k="20" />
    <hkern u1="&#x3c4;" u2="&#x3bb;" k="30" />
    <hkern u1="&#x3c4;" u2="&#x3ba;" k="20" />
    <hkern u1="&#x3c4;" u2="&#x3b9;" k="20" />
    <hkern u1="&#x3c4;" u2="&#x3b8;" k="15" />
    <hkern u1="&#x3c4;" u2="&#x3b6;" k="35" />
    <hkern u1="&#x3c4;" u2="&#x3b5;" k="40" />
    <hkern u1="&#x3c4;" u2="&#x3b4;" k="40" />
    <hkern u1="&#x3c4;" u2="&#x3b2;" k="35" />
    <hkern u1="&#x3c4;" u2="&#x3b1;" k="55" />
    <hkern u1="&#x3c4;" u2="&#x3af;" k="20" />
    <hkern u1="&#x3c4;" u2="&#x3ad;" k="40" />
    <hkern u1="&#x3c4;" u2="&#x3ac;" k="55" />
    <hkern u1="&#x3c4;" u2="&#x390;" k="20" />
    <hkern u1="&#x3c4;" u2="&#xb5;" k="25" />
    <hkern u1="&#x3c4;" u2="&#x2d;" k="55" />
    <hkern u1="&#x3c5;" u2="&#x3ce;" k="5" />
    <hkern u1="&#x3c5;" u2="&#x3c9;" k="5" />
    <hkern u1="&#x3c5;" u2="&#x3c1;" k="30" />
    <hkern u1="&#x3c5;" u2="&#x3bb;" k="15" />
    <hkern u1="&#x3c5;" u2="&#x3b5;" k="13" />
    <hkern u1="&#x3c5;" u2="&#x3b1;" k="10" />
    <hkern u1="&#x3c5;" u2="&#x3ad;" k="13" />
    <hkern u1="&#x3c5;" u2="&#x3ac;" k="10" />
    <hkern u1="&#x3c6;" u2="&#x3c7;" k="20" />
    <hkern u1="&#x3c6;" u2="&#x3bb;" k="15" />
    <hkern u1="&#x3c7;" u2="&#x2026;" k="34" />
    <hkern u1="&#x3c7;" u2="&#x2014;" k="39" />
    <hkern u1="&#x3c7;" u2="&#x2013;" k="39" />
    <hkern u1="&#x3c7;" u2="&#x3ce;" k="50" />
    <hkern u1="&#x3c7;" u2="&#x3cc;" k="45" />
    <hkern u1="&#x3c7;" u2="&#x3ca;" k="35" />
    <hkern u1="&#x3c7;" u2="&#x3c9;" k="50" />
    <hkern u1="&#x3c7;" u2="&#x3c8;" k="20" />
    <hkern u1="&#x3c7;" u2="&#x3c6;" k="40" />
    <hkern u1="&#x3c7;" u2="&#x3c4;" k="15" />
    <hkern u1="&#x3c7;" u2="&#x3c3;" k="35" />
    <hkern u1="&#x3c7;" u2="&#x3c2;" k="35" />
    <hkern u1="&#x3c7;" u2="&#x3c0;" k="10" />
    <hkern u1="&#x3c7;" u2="&#x3bf;" k="45" />
    <hkern u1="&#x3c7;" u2="&#x3be;" k="25" />
    <hkern u1="&#x3c7;" u2="&#x3bb;" k="35" />
    <hkern u1="&#x3c7;" u2="&#x3ba;" k="25" />
    <hkern u1="&#x3c7;" u2="&#x3b9;" k="35" />
    <hkern u1="&#x3c7;" u2="&#x3b8;" k="20" />
    <hkern u1="&#x3c7;" u2="&#x3b6;" k="25" />
    <hkern u1="&#x3c7;" u2="&#x3b5;" k="40" />
    <hkern u1="&#x3c7;" u2="&#x3b4;" k="50" />
    <hkern u1="&#x3c7;" u2="&#x3b1;" k="55" />
    <hkern u1="&#x3c7;" u2="&#x3af;" k="35" />
    <hkern u1="&#x3c7;" u2="&#x3ad;" k="40" />
    <hkern u1="&#x3c7;" u2="&#x3ac;" k="55" />
    <hkern u1="&#x3c7;" u2="&#x390;" k="35" />
    <hkern u1="&#x3c7;" u2="&#x2e;" k="34" />
    <hkern u1="&#x3c7;" u2="&#x2d;" k="39" />
    <hkern u1="&#x3c7;" u2="&#x2c;" k="34" />
    <hkern u1="&#x3c8;" u2="&#x3bb;" k="25" />
    <hkern u1="&#x3c8;" u2="&#x3b1;" k="5" />
    <hkern u1="&#x3c8;" u2="&#x3ac;" k="5" />
    <hkern u1="&#x3c9;" u2="&#x3c4;" k="10" />
    <hkern u1="&#x3c9;" u2="&#x3bb;" k="15" />
    <hkern u1="&#x3ca;" u2="&#x3cc;" k="10" />
    <hkern u1="&#x3ca;" u2="&#x3c6;" k="10" />
    <hkern u1="&#x3ca;" u2="&#x3c4;" k="20" />
    <hkern u1="&#x3ca;" u2="&#x3c3;" k="5" />
    <hkern u1="&#x3ca;" u2="&#x3c0;" k="20" />
    <hkern u1="&#x3ca;" u2="&#x3bf;" k="10" />
    <hkern u1="&#x3ca;" u2="&#x3b8;" k="5" />
    <hkern u1="&#x3ca;" u2="&#x3b4;" k="5" />
    <hkern u1="&#x3ca;" u2="&#x3b1;" k="7" />
    <hkern u1="&#x3ca;" u2="&#x3ac;" k="7" />
    <hkern u1="&#x3cb;" u2="&#x3ce;" k="5" />
    <hkern u1="&#x3cb;" u2="&#x3c9;" k="5" />
    <hkern u1="&#x3cb;" u2="&#x3c1;" k="30" />
    <hkern u1="&#x3cb;" u2="&#x3bb;" k="15" />
    <hkern u1="&#x3cb;" u2="&#x3b5;" k="13" />
    <hkern u1="&#x3cb;" u2="&#x3b1;" k="10" />
    <hkern u1="&#x3cb;" u2="&#x3ad;" k="13" />
    <hkern u1="&#x3cb;" u2="&#x3ac;" k="10" />
    <hkern u1="&#x3cc;" u2="&#x201d;" k="20" />
    <hkern u1="&#x3cc;" u2="&#x2019;" k="20" />
    <hkern u1="&#x3cc;" u2="&#x3c7;" k="10" />
    <hkern u1="&#x3cc;" u2="&#x3c4;" k="10" />
    <hkern u1="&#x3cc;" u2="&#x3bb;" k="10" />
    <hkern u1="&#x3cc;" u2="&#x3b5;" k="5" />
    <hkern u1="&#x3cc;" u2="&#x3ad;" k="5" />
    <hkern u1="&#x3cd;" u2="&#x3ce;" k="5" />
    <hkern u1="&#x3cd;" u2="&#x3c9;" k="5" />
    <hkern u1="&#x3cd;" u2="&#x3c1;" k="30" />
    <hkern u1="&#x3cd;" u2="&#x3bb;" k="15" />
    <hkern u1="&#x3cd;" u2="&#x3b5;" k="13" />
    <hkern u1="&#x3cd;" u2="&#x3b1;" k="10" />
    <hkern u1="&#x3cd;" u2="&#x3ad;" k="13" />
    <hkern u1="&#x3cd;" u2="&#x3ac;" k="10" />
    <hkern u1="&#x3ce;" u2="&#x3c4;" k="10" />
    <hkern u1="&#x3ce;" u2="&#x3bb;" k="15" />
    <hkern u1="&#x401;" u2="&#x424;" k="20" />
    <hkern u1="&#x402;" u2="&#x427;" k="30" />
    <hkern u1="&#x402;" u2="&#x423;" k="40" />
    <hkern u1="&#x402;" u2="&#x422;" k="60" />
    <hkern u1="&#x402;" u2="&#x416;" k="30" />
    <hkern u1="&#x402;" u2="&#x40e;" k="40" />
    <hkern u1="&#x402;" u2="&#x40b;" k="40" />
    <hkern u1="&#x402;" u2="&#x402;" k="75" />
    <hkern u1="&#x403;" u2="&#x203a;" k="82" />
    <hkern u1="&#x403;" u2="&#x2026;" k="138" />
    <hkern u1="&#x403;" u2="&#x2014;" k="105" />
    <hkern u1="&#x403;" u2="&#x2013;" k="105" />
    <hkern u1="&#x403;" u2="&#x491;" k="90" />
    <hkern u1="&#x403;" u2="&#x45f;" k="90" />
    <hkern u1="&#x403;" u2="&#x45e;" k="60" />
    <hkern u1="&#x403;" u2="&#x45c;" k="90" />
    <hkern u1="&#x403;" u2="&#x45a;" k="70" />
    <hkern u1="&#x403;" u2="&#x459;" k="110" />
    <hkern u1="&#x403;" u2="&#x455;" k="80" />
    <hkern u1="&#x403;" u2="&#x454;" k="110" />
    <hkern u1="&#x403;" u2="&#x453;" k="90" />
    <hkern u1="&#x403;" u2="&#x451;" k="110" />
    <hkern u1="&#x403;" u2="&#x44f;" k="90" />
    <hkern u1="&#x403;" u2="&#x44e;" k="90" />
    <hkern u1="&#x403;" u2="&#x44d;" k="80" />
    <hkern u1="&#x403;" u2="&#x44c;" k="90" />
    <hkern u1="&#x403;" u2="&#x44b;" k="90" />
    <hkern u1="&#x403;" u2="&#x44a;" k="60" />
    <hkern u1="&#x403;" u2="&#x449;" k="90" />
    <hkern u1="&#x403;" u2="&#x448;" k="90" />
    <hkern u1="&#x403;" u2="&#x447;" k="90" />
    <hkern u1="&#x403;" u2="&#x446;" k="90" />
    <hkern u1="&#x403;" u2="&#x445;" k="60" />
    <hkern u1="&#x403;" u2="&#x444;" k="110" />
    <hkern u1="&#x403;" u2="&#x443;" k="60" />
    <hkern u1="&#x403;" u2="&#x442;" k="60" />
    <hkern u1="&#x403;" u2="&#x441;" k="110" />
    <hkern u1="&#x403;" u2="&#x440;" k="90" />
    <hkern u1="&#x403;" u2="&#x43f;" k="90" />
    <hkern u1="&#x403;" u2="&#x43e;" k="110" />
    <hkern u1="&#x403;" u2="&#x43d;" k="90" />
    <hkern u1="&#x403;" u2="&#x43c;" k="90" />
    <hkern u1="&#x403;" u2="&#x43b;" k="110" />
    <hkern u1="&#x403;" u2="&#x43a;" k="90" />
    <hkern u1="&#x403;" u2="&#x439;" k="90" />
    <hkern u1="&#x403;" u2="&#x438;" k="90" />
    <hkern u1="&#x403;" u2="&#x437;" k="80" />
    <hkern u1="&#x403;" u2="&#x436;" k="70" />
    <hkern u1="&#x403;" u2="&#x435;" k="110" />
    <hkern u1="&#x403;" u2="&#x434;" k="110" />
    <hkern u1="&#x403;" u2="&#x433;" k="90" />
    <hkern u1="&#x403;" u2="&#x432;" k="90" />
    <hkern u1="&#x403;" u2="&#x431;" k="35" />
    <hkern u1="&#x403;" u2="&#x430;" k="80" />
    <hkern u1="&#x403;" u2="&#x42f;" k="26" />
    <hkern u1="&#x403;" u2="&#x424;" k="64" />
    <hkern u1="&#x403;" u2="&#x421;" k="36" />
    <hkern u1="&#x403;" u2="&#x41e;" k="35" />
    <hkern u1="&#x403;" u2="&#x41b;" k="80" />
    <hkern u1="&#x403;" u2="&#x414;" k="80" />
    <hkern u1="&#x403;" u2="&#x410;" k="90" />
    <hkern u1="&#x403;" u2="&#x409;" k="80" />
    <hkern u1="&#x403;" u2="&#x408;" k="115" />
    <hkern u1="&#x403;" u2="&#x404;" k="36" />
    <hkern u1="&#x403;" u2="&#xbb;" k="82" />
    <hkern u1="&#x403;" u2="&#x2e;" k="138" />
    <hkern u1="&#x403;" u2="&#x2d;" k="105" />
    <hkern u1="&#x403;" u2="&#x2c;" k="138" />
    <hkern u1="&#x409;" u2="&#x201d;" k="35" />
    <hkern u1="&#x409;" u2="&#x2019;" k="35" />
    <hkern u1="&#x409;" u2="&#x427;" k="35" />
    <hkern u1="&#x409;" u2="&#x423;" k="50" />
    <hkern u1="&#x409;" u2="&#x422;" k="75" />
    <hkern u1="&#x409;" u2="&#x416;" k="30" />
    <hkern u1="&#x409;" u2="&#x40e;" k="50" />
    <hkern u1="&#x409;" u2="&#x40b;" k="80" />
    <hkern u1="&#x409;" u2="&#x402;" k="80" />
    <hkern u1="&#x40a;" u2="&#x201d;" k="35" />
    <hkern u1="&#x40a;" u2="&#x2019;" k="35" />
    <hkern u1="&#x40a;" u2="&#x427;" k="35" />
    <hkern u1="&#x40a;" u2="&#x423;" k="50" />
    <hkern u1="&#x40a;" u2="&#x422;" k="75" />
    <hkern u1="&#x40a;" u2="&#x416;" k="30" />
    <hkern u1="&#x40a;" u2="&#x40e;" k="50" />
    <hkern u1="&#x40a;" u2="&#x40b;" k="80" />
    <hkern u1="&#x40a;" u2="&#x402;" k="80" />
    <hkern u1="&#x40b;" u2="&#x427;" k="69" />
    <hkern u1="&#x40b;" u2="&#x423;" k="55" />
    <hkern u1="&#x40b;" u2="&#x422;" k="90" />
    <hkern u1="&#x40b;" u2="&#x40e;" k="55" />
    <hkern u1="&#x40b;" u2="&#x402;" k="95" />
    <hkern u1="&#x40c;" u2="&#x454;" k="35" />
    <hkern u1="&#x40c;" u2="&#x451;" k="35" />
    <hkern u1="&#x40c;" u2="&#x447;" k="65" />
    <hkern u1="&#x40c;" u2="&#x444;" k="35" />
    <hkern u1="&#x40c;" u2="&#x441;" k="35" />
    <hkern u1="&#x40c;" u2="&#x43e;" k="35" />
    <hkern u1="&#x40c;" u2="&#x435;" k="35" />
    <hkern u1="&#x40c;" u2="&#x431;" k="30" />
    <hkern u1="&#x40c;" u2="&#x424;" k="30" />
    <hkern u1="&#x40c;" u2="&#x421;" k="30" />
    <hkern u1="&#x40c;" u2="&#x41e;" k="30" />
    <hkern u1="&#x40c;" u2="&#x404;" k="30" />
    <hkern u1="&#x40e;" u2="&#x203a;" k="43" />
    <hkern u1="&#x40e;" u2="&#x2026;" k="123" />
    <hkern u1="&#x40e;" u2="&#x491;" k="54" />
    <hkern u1="&#x40e;" u2="&#x45f;" k="54" />
    <hkern u1="&#x40e;" u2="&#x45c;" k="54" />
    <hkern u1="&#x40e;" u2="&#x45a;" k="54" />
    <hkern u1="&#x40e;" u2="&#x459;" k="98" />
    <hkern u1="&#x40e;" u2="&#x455;" k="60" />
    <hkern u1="&#x40e;" u2="&#x454;" k="70" />
    <hkern u1="&#x40e;" u2="&#x453;" k="54" />
    <hkern u1="&#x40e;" u2="&#x451;" k="70" />
    <hkern u1="&#x40e;" u2="&#x44f;" k="60" />
    <hkern u1="&#x40e;" u2="&#x44e;" k="54" />
    <hkern u1="&#x40e;" u2="&#x44d;" k="50" />
    <hkern u1="&#x40e;" u2="&#x44c;" k="54" />
    <hkern u1="&#x40e;" u2="&#x44b;" k="54" />
    <hkern u1="&#x40e;" u2="&#x449;" k="54" />
    <hkern u1="&#x40e;" u2="&#x448;" k="54" />
    <hkern u1="&#x40e;" u2="&#x447;" k="44" />
    <hkern u1="&#x40e;" u2="&#x446;" k="54" />
    <hkern u1="&#x40e;" u2="&#x444;" k="70" />
    <hkern u1="&#x40e;" u2="&#x441;" k="70" />
    <hkern u1="&#x40e;" u2="&#x440;" k="59" />
    <hkern u1="&#x40e;" u2="&#x43f;" k="54" />
    <hkern u1="&#x40e;" u2="&#x43e;" k="70" />
    <hkern u1="&#x40e;" u2="&#x43d;" k="54" />
    <hkern u1="&#x40e;" u2="&#x43c;" k="54" />
    <hkern u1="&#x40e;" u2="&#x43b;" k="98" />
    <hkern u1="&#x40e;" u2="&#x43a;" k="54" />
    <hkern u1="&#x40e;" u2="&#x439;" k="54" />
    <hkern u1="&#x40e;" u2="&#x438;" k="54" />
    <hkern u1="&#x40e;" u2="&#x437;" k="54" />
    <hkern u1="&#x40e;" u2="&#x436;" k="39" />
    <hkern u1="&#x40e;" u2="&#x435;" k="70" />
    <hkern u1="&#x40e;" u2="&#x434;" k="118" />
    <hkern u1="&#x40e;" u2="&#x433;" k="54" />
    <hkern u1="&#x40e;" u2="&#x432;" k="54" />
    <hkern u1="&#x40e;" u2="&#x431;" k="59" />
    <hkern u1="&#x40e;" u2="&#x430;" k="83" />
    <hkern u1="&#x40e;" u2="&#x424;" k="49" />
    <hkern u1="&#x40e;" u2="&#x421;" k="39" />
    <hkern u1="&#x40e;" u2="&#x41e;" k="39" />
    <hkern u1="&#x40e;" u2="&#x41b;" k="108" />
    <hkern u1="&#x40e;" u2="&#x414;" k="80" />
    <hkern u1="&#x40e;" u2="&#x410;" k="110" />
    <hkern u1="&#x40e;" u2="&#x409;" k="108" />
    <hkern u1="&#x40e;" u2="&#x408;" k="145" />
    <hkern u1="&#x40e;" u2="&#x404;" k="39" />
    <hkern u1="&#x40e;" u2="&#xbb;" k="43" />
    <hkern u1="&#x40e;" u2="&#x2e;" k="123" />
    <hkern u1="&#x40e;" u2="&#x2c;" k="123" />
    <hkern u1="&#x410;" u2="&#x201d;" k="64" />
    <hkern u1="&#x410;" u2="&#x2019;" k="64" />
    <hkern u1="&#x410;" u2="&#x45e;" k="55" />
    <hkern u1="&#x410;" u2="&#x447;" k="55" />
    <hkern u1="&#x410;" u2="&#x443;" k="55" />
    <hkern u1="&#x410;" u2="&#x442;" k="40" />
    <hkern u1="&#x410;" u2="&#x427;" k="70" />
    <hkern u1="&#x410;" u2="&#x424;" k="25" />
    <hkern u1="&#x410;" u2="&#x423;" k="42" />
    <hkern u1="&#x410;" u2="&#x422;" k="80" />
    <hkern u1="&#x410;" u2="&#x421;" k="20" />
    <hkern u1="&#x410;" u2="&#x41e;" k="20" />
    <hkern u1="&#x410;" u2="&#x417;" k="22" />
    <hkern u1="&#x410;" u2="&#x40e;" k="42" />
    <hkern u1="&#x410;" u2="&#x40b;" k="70" />
    <hkern u1="&#x410;" u2="&#x404;" k="20" />
    <hkern u1="&#x410;" u2="&#x402;" k="74" />
    <hkern u1="&#x411;" u2="&#x427;" k="40" />
    <hkern u1="&#x411;" u2="&#x423;" k="35" />
    <hkern u1="&#x411;" u2="&#x40e;" k="35" />
    <hkern u1="&#x412;" u2="&#x423;" k="20" />
    <hkern u1="&#x412;" u2="&#x40e;" k="20" />
    <hkern u1="&#x413;" u2="&#x203a;" k="82" />
    <hkern u1="&#x413;" u2="&#x2026;" k="138" />
    <hkern u1="&#x413;" u2="&#x2014;" k="105" />
    <hkern u1="&#x413;" u2="&#x2013;" k="105" />
    <hkern u1="&#x413;" u2="&#x491;" k="90" />
    <hkern u1="&#x413;" u2="&#x45f;" k="90" />
    <hkern u1="&#x413;" u2="&#x45e;" k="60" />
    <hkern u1="&#x413;" u2="&#x45c;" k="90" />
    <hkern u1="&#x413;" u2="&#x45a;" k="70" />
    <hkern u1="&#x413;" u2="&#x459;" k="110" />
    <hkern u1="&#x413;" u2="&#x455;" k="80" />
    <hkern u1="&#x413;" u2="&#x454;" k="110" />
    <hkern u1="&#x413;" u2="&#x453;" k="90" />
    <hkern u1="&#x413;" u2="&#x451;" k="110" />
    <hkern u1="&#x413;" u2="&#x44f;" k="90" />
    <hkern u1="&#x413;" u2="&#x44e;" k="90" />
    <hkern u1="&#x413;" u2="&#x44d;" k="80" />
    <hkern u1="&#x413;" u2="&#x44c;" k="90" />
    <hkern u1="&#x413;" u2="&#x44b;" k="90" />
    <hkern u1="&#x413;" u2="&#x44a;" k="60" />
    <hkern u1="&#x413;" u2="&#x449;" k="90" />
    <hkern u1="&#x413;" u2="&#x448;" k="90" />
    <hkern u1="&#x413;" u2="&#x447;" k="90" />
    <hkern u1="&#x413;" u2="&#x446;" k="90" />
    <hkern u1="&#x413;" u2="&#x445;" k="60" />
    <hkern u1="&#x413;" u2="&#x444;" k="110" />
    <hkern u1="&#x413;" u2="&#x443;" k="60" />
    <hkern u1="&#x413;" u2="&#x442;" k="60" />
    <hkern u1="&#x413;" u2="&#x441;" k="110" />
    <hkern u1="&#x413;" u2="&#x440;" k="90" />
    <hkern u1="&#x413;" u2="&#x43f;" k="90" />
    <hkern u1="&#x413;" u2="&#x43e;" k="110" />
    <hkern u1="&#x413;" u2="&#x43d;" k="90" />
    <hkern u1="&#x413;" u2="&#x43c;" k="90" />
    <hkern u1="&#x413;" u2="&#x43b;" k="110" />
    <hkern u1="&#x413;" u2="&#x43a;" k="90" />
    <hkern u1="&#x413;" u2="&#x439;" k="90" />
    <hkern u1="&#x413;" u2="&#x438;" k="90" />
    <hkern u1="&#x413;" u2="&#x437;" k="80" />
    <hkern u1="&#x413;" u2="&#x436;" k="70" />
    <hkern u1="&#x413;" u2="&#x435;" k="110" />
    <hkern u1="&#x413;" u2="&#x434;" k="110" />
    <hkern u1="&#x413;" u2="&#x433;" k="90" />
    <hkern u1="&#x413;" u2="&#x432;" k="90" />
    <hkern u1="&#x413;" u2="&#x431;" k="35" />
    <hkern u1="&#x413;" u2="&#x430;" k="80" />
    <hkern u1="&#x413;" u2="&#x42f;" k="26" />
    <hkern u1="&#x413;" u2="&#x424;" k="64" />
    <hkern u1="&#x413;" u2="&#x421;" k="36" />
    <hkern u1="&#x413;" u2="&#x41e;" k="35" />
    <hkern u1="&#x413;" u2="&#x41b;" k="80" />
    <hkern u1="&#x413;" u2="&#x414;" k="80" />
    <hkern u1="&#x413;" u2="&#x410;" k="90" />
    <hkern u1="&#x413;" u2="&#x409;" k="80" />
    <hkern u1="&#x413;" u2="&#x408;" k="115" />
    <hkern u1="&#x413;" u2="&#x404;" k="36" />
    <hkern u1="&#x413;" u2="&#xbb;" k="82" />
    <hkern u1="&#x413;" u2="&#x2e;" k="138" />
    <hkern u1="&#x413;" u2="&#x2d;" k="105" />
    <hkern u1="&#x413;" u2="&#x2c;" k="138" />
    <hkern u1="&#x415;" u2="&#x424;" k="20" />
    <hkern u1="&#x416;" u2="&#x454;" k="35" />
    <hkern u1="&#x416;" u2="&#x451;" k="35" />
    <hkern u1="&#x416;" u2="&#x447;" k="55" />
    <hkern u1="&#x416;" u2="&#x444;" k="35" />
    <hkern u1="&#x416;" u2="&#x442;" k="40" />
    <hkern u1="&#x416;" u2="&#x441;" k="35" />
    <hkern u1="&#x416;" u2="&#x43e;" k="35" />
    <hkern u1="&#x416;" u2="&#x435;" k="35" />
    <hkern u1="&#x416;" u2="&#x431;" k="30" />
    <hkern u1="&#x416;" u2="&#x424;" k="30" />
    <hkern u1="&#x416;" u2="&#x421;" k="30" />
    <hkern u1="&#x416;" u2="&#x41e;" k="30" />
    <hkern u1="&#x416;" u2="&#x404;" k="30" />
    <hkern u1="&#x417;" u2="&#x422;" k="27" />
    <hkern u1="&#x41a;" u2="&#x454;" k="35" />
    <hkern u1="&#x41a;" u2="&#x451;" k="35" />
    <hkern u1="&#x41a;" u2="&#x447;" k="65" />
    <hkern u1="&#x41a;" u2="&#x444;" k="35" />
    <hkern u1="&#x41a;" u2="&#x441;" k="35" />
    <hkern u1="&#x41a;" u2="&#x43e;" k="35" />
    <hkern u1="&#x41a;" u2="&#x435;" k="35" />
    <hkern u1="&#x41a;" u2="&#x431;" k="30" />
    <hkern u1="&#x41a;" u2="&#x424;" k="30" />
    <hkern u1="&#x41a;" u2="&#x421;" k="30" />
    <hkern u1="&#x41a;" u2="&#x41e;" k="30" />
    <hkern u1="&#x41a;" u2="&#x404;" k="30" />
    <hkern u1="&#x41e;" u2="&#x459;" k="30" />
    <hkern u1="&#x41e;" u2="&#x43b;" k="30" />
    <hkern u1="&#x41e;" u2="&#x434;" k="35" />
    <hkern u1="&#x41e;" u2="&#x425;" k="40" />
    <hkern u1="&#x41e;" u2="&#x423;" k="40" />
    <hkern u1="&#x41e;" u2="&#x422;" k="35" />
    <hkern u1="&#x41e;" u2="&#x41b;" k="40" />
    <hkern u1="&#x41e;" u2="&#x417;" k="20" />
    <hkern u1="&#x41e;" u2="&#x416;" k="30" />
    <hkern u1="&#x41e;" u2="&#x414;" k="30" />
    <hkern u1="&#x41e;" u2="&#x410;" k="20" />
    <hkern u1="&#x41e;" u2="&#x40e;" k="40" />
    <hkern u1="&#x41e;" u2="&#x40b;" k="30" />
    <hkern u1="&#x41e;" u2="&#x409;" k="40" />
    <hkern u1="&#x41e;" u2="&#x408;" k="30" />
    <hkern u1="&#x41e;" u2="&#x402;" k="30" />
    <hkern u1="&#x420;" u2="&#x2026;" k="132" />
    <hkern u1="&#x420;" u2="&#x459;" k="40" />
    <hkern u1="&#x420;" u2="&#x444;" k="25" />
    <hkern u1="&#x420;" u2="&#x43b;" k="40" />
    <hkern u1="&#x420;" u2="&#x434;" k="60" />
    <hkern u1="&#x420;" u2="&#x41b;" k="60" />
    <hkern u1="&#x420;" u2="&#x414;" k="60" />
    <hkern u1="&#x420;" u2="&#x410;" k="50" />
    <hkern u1="&#x420;" u2="&#x409;" k="60" />
    <hkern u1="&#x420;" u2="&#x408;" k="120" />
    <hkern u1="&#x420;" u2="&#x2e;" k="132" />
    <hkern u1="&#x420;" u2="&#x2c;" k="132" />
    <hkern u1="&#x421;" u2="&#x454;" k="20" />
    <hkern u1="&#x421;" u2="&#x451;" k="20" />
    <hkern u1="&#x421;" u2="&#x441;" k="20" />
    <hkern u1="&#x421;" u2="&#x43e;" k="20" />
    <hkern u1="&#x421;" u2="&#x435;" k="20" />
    <hkern u1="&#x421;" u2="&#x431;" k="30" />
    <hkern u1="&#x421;" u2="&#x421;" k="20" />
    <hkern u1="&#x421;" u2="&#x41e;" k="20" />
    <hkern u1="&#x421;" u2="&#x404;" k="20" />
    <hkern u1="&#x422;" u2="&#x203a;" k="77" />
    <hkern u1="&#x422;" u2="&#x2026;" k="74" />
    <hkern u1="&#x422;" u2="&#x2014;" k="77" />
    <hkern u1="&#x422;" u2="&#x2013;" k="77" />
    <hkern u1="&#x422;" u2="&#x491;" k="74" />
    <hkern u1="&#x422;" u2="&#x45f;" k="74" />
    <hkern u1="&#x422;" u2="&#x45e;" k="54" />
    <hkern u1="&#x422;" u2="&#x45c;" k="74" />
    <hkern u1="&#x422;" u2="&#x45a;" k="74" />
    <hkern u1="&#x422;" u2="&#x459;" k="88" />
    <hkern u1="&#x422;" u2="&#x455;" k="83" />
    <hkern u1="&#x422;" u2="&#x454;" k="80" />
    <hkern u1="&#x422;" u2="&#x453;" k="74" />
    <hkern u1="&#x422;" u2="&#x451;" k="80" />
    <hkern u1="&#x422;" u2="&#x44f;" k="83" />
    <hkern u1="&#x422;" u2="&#x44e;" k="74" />
    <hkern u1="&#x422;" u2="&#x44d;" k="80" />
    <hkern u1="&#x422;" u2="&#x44c;" k="74" />
    <hkern u1="&#x422;" u2="&#x44b;" k="74" />
    <hkern u1="&#x422;" u2="&#x44a;" k="34" />
    <hkern u1="&#x422;" u2="&#x449;" k="74" />
    <hkern u1="&#x422;" u2="&#x448;" k="74" />
    <hkern u1="&#x422;" u2="&#x447;" k="88" />
    <hkern u1="&#x422;" u2="&#x446;" k="74" />
    <hkern u1="&#x422;" u2="&#x445;" k="44" />
    <hkern u1="&#x422;" u2="&#x444;" k="83" />
    <hkern u1="&#x422;" u2="&#x443;" k="54" />
    <hkern u1="&#x422;" u2="&#x441;" k="80" />
    <hkern u1="&#x422;" u2="&#x440;" k="83" />
    <hkern u1="&#x422;" u2="&#x43f;" k="74" />
    <hkern u1="&#x422;" u2="&#x43e;" k="80" />
    <hkern u1="&#x422;" u2="&#x43d;" k="74" />
    <hkern u1="&#x422;" u2="&#x43c;" k="74" />
    <hkern u1="&#x422;" u2="&#x43b;" k="88" />
    <hkern u1="&#x422;" u2="&#x43a;" k="74" />
    <hkern u1="&#x422;" u2="&#x439;" k="74" />
    <hkern u1="&#x422;" u2="&#x438;" k="74" />
    <hkern u1="&#x422;" u2="&#x437;" k="54" />
    <hkern u1="&#x422;" u2="&#x436;" k="64" />
    <hkern u1="&#x422;" u2="&#x435;" k="80" />
    <hkern u1="&#x422;" u2="&#x434;" k="113" />
    <hkern u1="&#x422;" u2="&#x433;" k="74" />
    <hkern u1="&#x422;" u2="&#x432;" k="74" />
    <hkern u1="&#x422;" u2="&#x431;" k="39" />
    <hkern u1="&#x422;" u2="&#x430;" k="93" />
    <hkern u1="&#x422;" u2="&#x424;" k="50" />
    <hkern u1="&#x422;" u2="&#x421;" k="30" />
    <hkern u1="&#x422;" u2="&#x41e;" k="30" />
    <hkern u1="&#x422;" u2="&#x41b;" k="70" />
    <hkern u1="&#x422;" u2="&#x414;" k="70" />
    <hkern u1="&#x422;" u2="&#x410;" k="70" />
    <hkern u1="&#x422;" u2="&#x409;" k="70" />
    <hkern u1="&#x422;" u2="&#x408;" k="92" />
    <hkern u1="&#x422;" u2="&#x404;" k="30" />
    <hkern u1="&#x422;" u2="&#xbb;" k="77" />
    <hkern u1="&#x422;" u2="&#x2e;" k="74" />
    <hkern u1="&#x422;" u2="&#x2d;" k="77" />
    <hkern u1="&#x422;" u2="&#x2c;" k="74" />
    <hkern u1="&#x423;" u2="&#x203a;" k="43" />
    <hkern u1="&#x423;" u2="&#x2026;" k="123" />
    <hkern u1="&#x423;" u2="&#x491;" k="54" />
    <hkern u1="&#x423;" u2="&#x45f;" k="54" />
    <hkern u1="&#x423;" u2="&#x45c;" k="54" />
    <hkern u1="&#x423;" u2="&#x45a;" k="54" />
    <hkern u1="&#x423;" u2="&#x459;" k="98" />
    <hkern u1="&#x423;" u2="&#x455;" k="60" />
    <hkern u1="&#x423;" u2="&#x454;" k="70" />
    <hkern u1="&#x423;" u2="&#x453;" k="54" />
    <hkern u1="&#x423;" u2="&#x451;" k="70" />
    <hkern u1="&#x423;" u2="&#x44f;" k="60" />
    <hkern u1="&#x423;" u2="&#x44e;" k="54" />
    <hkern u1="&#x423;" u2="&#x44d;" k="50" />
    <hkern u1="&#x423;" u2="&#x44c;" k="54" />
    <hkern u1="&#x423;" u2="&#x44b;" k="54" />
    <hkern u1="&#x423;" u2="&#x449;" k="54" />
    <hkern u1="&#x423;" u2="&#x448;" k="54" />
    <hkern u1="&#x423;" u2="&#x447;" k="44" />
    <hkern u1="&#x423;" u2="&#x446;" k="54" />
    <hkern u1="&#x423;" u2="&#x444;" k="70" />
    <hkern u1="&#x423;" u2="&#x441;" k="70" />
    <hkern u1="&#x423;" u2="&#x440;" k="59" />
    <hkern u1="&#x423;" u2="&#x43f;" k="54" />
    <hkern u1="&#x423;" u2="&#x43e;" k="70" />
    <hkern u1="&#x423;" u2="&#x43d;" k="54" />
    <hkern u1="&#x423;" u2="&#x43c;" k="54" />
    <hkern u1="&#x423;" u2="&#x43b;" k="98" />
    <hkern u1="&#x423;" u2="&#x43a;" k="54" />
    <hkern u1="&#x423;" u2="&#x439;" k="54" />
    <hkern u1="&#x423;" u2="&#x438;" k="54" />
    <hkern u1="&#x423;" u2="&#x437;" k="54" />
    <hkern u1="&#x423;" u2="&#x436;" k="39" />
    <hkern u1="&#x423;" u2="&#x435;" k="70" />
    <hkern u1="&#x423;" u2="&#x434;" k="118" />
    <hkern u1="&#x423;" u2="&#x433;" k="54" />
    <hkern u1="&#x423;" u2="&#x432;" k="54" />
    <hkern u1="&#x423;" u2="&#x431;" k="59" />
    <hkern u1="&#x423;" u2="&#x430;" k="83" />
    <hkern u1="&#x423;" u2="&#x424;" k="49" />
    <hkern u1="&#x423;" u2="&#x421;" k="39" />
    <hkern u1="&#x423;" u2="&#x41e;" k="39" />
    <hkern u1="&#x423;" u2="&#x41b;" k="108" />
    <hkern u1="&#x423;" u2="&#x414;" k="80" />
    <hkern u1="&#x423;" u2="&#x410;" k="110" />
    <hkern u1="&#x423;" u2="&#x409;" k="108" />
    <hkern u1="&#x423;" u2="&#x408;" k="145" />
    <hkern u1="&#x423;" u2="&#x404;" k="39" />
    <hkern u1="&#x423;" u2="&#xbb;" k="43" />
    <hkern u1="&#x423;" u2="&#x2e;" k="123" />
    <hkern u1="&#x423;" u2="&#x2c;" k="123" />
    <hkern u1="&#x424;" u2="&#x2026;" k="49" />
    <hkern u1="&#x424;" u2="&#x459;" k="35" />
    <hkern u1="&#x424;" u2="&#x43b;" k="35" />
    <hkern u1="&#x424;" u2="&#x434;" k="40" />
    <hkern u1="&#x424;" u2="&#x425;" k="40" />
    <hkern u1="&#x424;" u2="&#x423;" k="40" />
    <hkern u1="&#x424;" u2="&#x422;" k="50" />
    <hkern u1="&#x424;" u2="&#x41b;" k="30" />
    <hkern u1="&#x424;" u2="&#x416;" k="30" />
    <hkern u1="&#x424;" u2="&#x414;" k="30" />
    <hkern u1="&#x424;" u2="&#x410;" k="25" />
    <hkern u1="&#x424;" u2="&#x40e;" k="40" />
    <hkern u1="&#x424;" u2="&#x40b;" k="40" />
    <hkern u1="&#x424;" u2="&#x409;" k="30" />
    <hkern u1="&#x424;" u2="&#x408;" k="65" />
    <hkern u1="&#x424;" u2="&#x402;" k="40" />
    <hkern u1="&#x424;" u2="&#x2e;" k="49" />
    <hkern u1="&#x424;" u2="&#x2c;" k="49" />
    <hkern u1="&#x425;" u2="&#x447;" k="60" />
    <hkern u1="&#x425;" u2="&#x444;" k="20" />
    <hkern u1="&#x425;" u2="&#x431;" k="20" />
    <hkern u1="&#x425;" u2="&#x424;" k="40" />
    <hkern u1="&#x425;" u2="&#x421;" k="40" />
    <hkern u1="&#x425;" u2="&#x41e;" k="40" />
    <hkern u1="&#x425;" u2="&#x404;" k="40" />
    <hkern u1="&#x42a;" u2="&#x201d;" k="35" />
    <hkern u1="&#x42a;" u2="&#x2019;" k="35" />
    <hkern u1="&#x42a;" u2="&#x427;" k="35" />
    <hkern u1="&#x42a;" u2="&#x423;" k="50" />
    <hkern u1="&#x42a;" u2="&#x422;" k="75" />
    <hkern u1="&#x42a;" u2="&#x416;" k="30" />
    <hkern u1="&#x42a;" u2="&#x40e;" k="50" />
    <hkern u1="&#x42a;" u2="&#x40b;" k="80" />
    <hkern u1="&#x42a;" u2="&#x402;" k="80" />
    <hkern u1="&#x42c;" u2="&#x201d;" k="35" />
    <hkern u1="&#x42c;" u2="&#x2019;" k="35" />
    <hkern u1="&#x42c;" u2="&#x427;" k="35" />
    <hkern u1="&#x42c;" u2="&#x423;" k="50" />
    <hkern u1="&#x42c;" u2="&#x422;" k="75" />
    <hkern u1="&#x42c;" u2="&#x416;" k="30" />
    <hkern u1="&#x42c;" u2="&#x40e;" k="50" />
    <hkern u1="&#x42c;" u2="&#x40b;" k="80" />
    <hkern u1="&#x42c;" u2="&#x402;" k="80" />
    <hkern u1="&#x42d;" u2="&#x459;" k="30" />
    <hkern u1="&#x42d;" u2="&#x43b;" k="30" />
    <hkern u1="&#x42d;" u2="&#x434;" k="35" />
    <hkern u1="&#x42d;" u2="&#x425;" k="40" />
    <hkern u1="&#x42d;" u2="&#x423;" k="40" />
    <hkern u1="&#x42d;" u2="&#x422;" k="35" />
    <hkern u1="&#x42d;" u2="&#x41b;" k="40" />
    <hkern u1="&#x42d;" u2="&#x417;" k="20" />
    <hkern u1="&#x42d;" u2="&#x416;" k="30" />
    <hkern u1="&#x42d;" u2="&#x414;" k="30" />
    <hkern u1="&#x42d;" u2="&#x410;" k="20" />
    <hkern u1="&#x42d;" u2="&#x40e;" k="40" />
    <hkern u1="&#x42d;" u2="&#x40b;" k="30" />
    <hkern u1="&#x42d;" u2="&#x409;" k="40" />
    <hkern u1="&#x42d;" u2="&#x408;" k="30" />
    <hkern u1="&#x42d;" u2="&#x402;" k="30" />
    <hkern u1="&#x42e;" u2="&#x459;" k="30" />
    <hkern u1="&#x42e;" u2="&#x43b;" k="30" />
    <hkern u1="&#x42e;" u2="&#x434;" k="35" />
    <hkern u1="&#x42e;" u2="&#x425;" k="40" />
    <hkern u1="&#x42e;" u2="&#x423;" k="40" />
    <hkern u1="&#x42e;" u2="&#x422;" k="35" />
    <hkern u1="&#x42e;" u2="&#x41b;" k="40" />
    <hkern u1="&#x42e;" u2="&#x417;" k="20" />
    <hkern u1="&#x42e;" u2="&#x416;" k="30" />
    <hkern u1="&#x42e;" u2="&#x414;" k="30" />
    <hkern u1="&#x42e;" u2="&#x410;" k="20" />
    <hkern u1="&#x42e;" u2="&#x40e;" k="40" />
    <hkern u1="&#x42e;" u2="&#x40b;" k="30" />
    <hkern u1="&#x42e;" u2="&#x409;" k="40" />
    <hkern u1="&#x42e;" u2="&#x408;" k="30" />
    <hkern u1="&#x42e;" u2="&#x402;" k="30" />
    <hkern u1="&#x431;" u2="&#x459;" k="20" />
    <hkern u1="&#x431;" u2="&#x445;" k="20" />
    <hkern u1="&#x431;" u2="&#x43b;" k="20" />
    <hkern u1="&#x431;" u2="&#x436;" k="35" />
    <hkern u1="&#x431;" u2="&#x434;" k="30" />
    <hkern u1="&#x433;" u2="&#x2026;" k="98" />
    <hkern u1="&#x433;" u2="&#x459;" k="40" />
    <hkern u1="&#x433;" u2="&#x43b;" k="40" />
    <hkern u1="&#x433;" u2="&#x434;" k="50" />
    <hkern u1="&#x433;" u2="&#x2e;" k="98" />
    <hkern u1="&#x433;" u2="&#x2c;" k="98" />
    <hkern u1="&#x436;" u2="&#x454;" k="30" />
    <hkern u1="&#x436;" u2="&#x451;" k="30" />
    <hkern u1="&#x436;" u2="&#x444;" k="30" />
    <hkern u1="&#x436;" u2="&#x441;" k="30" />
    <hkern u1="&#x436;" u2="&#x43e;" k="30" />
    <hkern u1="&#x436;" u2="&#x435;" k="30" />
    <hkern u1="&#x436;" u2="&#x430;" k="25" />
    <hkern u1="&#x43a;" u2="&#x454;" k="30" />
    <hkern u1="&#x43a;" u2="&#x451;" k="30" />
    <hkern u1="&#x43a;" u2="&#x444;" k="30" />
    <hkern u1="&#x43a;" u2="&#x441;" k="30" />
    <hkern u1="&#x43a;" u2="&#x43e;" k="30" />
    <hkern u1="&#x43a;" u2="&#x435;" k="30" />
    <hkern u1="&#x43e;" u2="&#x45e;" k="15" />
    <hkern u1="&#x43e;" u2="&#x459;" k="20" />
    <hkern u1="&#x43e;" u2="&#x445;" k="20" />
    <hkern u1="&#x43e;" u2="&#x443;" k="15" />
    <hkern u1="&#x43e;" u2="&#x43b;" k="20" />
    <hkern u1="&#x43e;" u2="&#x437;" k="20" />
    <hkern u1="&#x43e;" u2="&#x436;" k="30" />
    <hkern u1="&#x43e;" u2="&#x434;" k="30" />
    <hkern u1="&#x440;" u2="&#x45e;" k="10" />
    <hkern u1="&#x440;" u2="&#x459;" k="20" />
    <hkern u1="&#x440;" u2="&#x445;" k="20" />
    <hkern u1="&#x440;" u2="&#x443;" k="10" />
    <hkern u1="&#x440;" u2="&#x43b;" k="20" />
    <hkern u1="&#x440;" u2="&#x436;" k="20" />
    <hkern u1="&#x440;" u2="&#x434;" k="30" />
    <hkern u1="&#x442;" u2="&#x2026;" k="59" />
    <hkern u1="&#x442;" u2="&#x459;" k="35" />
    <hkern u1="&#x442;" u2="&#x454;" k="10" />
    <hkern u1="&#x442;" u2="&#x451;" k="10" />
    <hkern u1="&#x442;" u2="&#x444;" k="30" />
    <hkern u1="&#x442;" u2="&#x441;" k="10" />
    <hkern u1="&#x442;" u2="&#x43e;" k="10" />
    <hkern u1="&#x442;" u2="&#x43b;" k="35" />
    <hkern u1="&#x442;" u2="&#x435;" k="10" />
    <hkern u1="&#x442;" u2="&#x434;" k="45" />
    <hkern u1="&#x442;" u2="&#x2e;" k="59" />
    <hkern u1="&#x442;" u2="&#x2c;" k="59" />
    <hkern u1="&#x443;" u2="&#x2026;" k="64" />
    <hkern u1="&#x443;" u2="&#x459;" k="35" />
    <hkern u1="&#x443;" u2="&#x43b;" k="35" />
    <hkern u1="&#x443;" u2="&#x434;" k="50" />
    <hkern u1="&#x443;" u2="&#x2e;" k="64" />
    <hkern u1="&#x443;" u2="&#x2c;" k="64" />
    <hkern u1="&#x444;" u2="&#x459;" k="20" />
    <hkern u1="&#x444;" u2="&#x445;" k="25" />
    <hkern u1="&#x444;" u2="&#x442;" k="25" />
    <hkern u1="&#x444;" u2="&#x43b;" k="20" />
    <hkern u1="&#x444;" u2="&#x437;" k="20" />
    <hkern u1="&#x444;" u2="&#x436;" k="30" />
    <hkern u1="&#x444;" u2="&#x434;" k="30" />
    <hkern u1="&#x445;" u2="&#x454;" k="20" />
    <hkern u1="&#x445;" u2="&#x451;" k="20" />
    <hkern u1="&#x445;" u2="&#x444;" k="25" />
    <hkern u1="&#x445;" u2="&#x441;" k="20" />
    <hkern u1="&#x445;" u2="&#x43e;" k="20" />
    <hkern u1="&#x445;" u2="&#x435;" k="20" />
    <hkern u1="&#x44a;" u2="&#x45e;" k="40" />
    <hkern u1="&#x44a;" u2="&#x459;" k="20" />
    <hkern u1="&#x44a;" u2="&#x447;" k="30" />
    <hkern u1="&#x44a;" u2="&#x445;" k="25" />
    <hkern u1="&#x44a;" u2="&#x443;" k="40" />
    <hkern u1="&#x44a;" u2="&#x442;" k="50" />
    <hkern u1="&#x44a;" u2="&#x43b;" k="20" />
    <hkern u1="&#x44a;" u2="&#x436;" k="45" />
    <hkern u1="&#x44a;" u2="&#x434;" k="30" />
    <hkern u1="&#x44c;" u2="&#x45e;" k="40" />
    <hkern u1="&#x44c;" u2="&#x459;" k="20" />
    <hkern u1="&#x44c;" u2="&#x447;" k="30" />
    <hkern u1="&#x44c;" u2="&#x445;" k="25" />
    <hkern u1="&#x44c;" u2="&#x443;" k="40" />
    <hkern u1="&#x44c;" u2="&#x442;" k="50" />
    <hkern u1="&#x44c;" u2="&#x43b;" k="20" />
    <hkern u1="&#x44c;" u2="&#x436;" k="45" />
    <hkern u1="&#x44c;" u2="&#x434;" k="30" />
    <hkern u1="&#x44d;" u2="&#x459;" k="20" />
    <hkern u1="&#x44d;" u2="&#x445;" k="20" />
    <hkern u1="&#x44d;" u2="&#x43b;" k="20" />
    <hkern u1="&#x44d;" u2="&#x436;" k="25" />
    <hkern u1="&#x44d;" u2="&#x434;" k="30" />
    <hkern u1="&#x44e;" u2="&#x459;" k="20" />
    <hkern u1="&#x44e;" u2="&#x445;" k="20" />
    <hkern u1="&#x44e;" u2="&#x43b;" k="20" />
    <hkern u1="&#x44e;" u2="&#x436;" k="30" />
    <hkern u1="&#x44e;" u2="&#x434;" k="30" />
    <hkern u1="&#x453;" u2="&#x2026;" k="98" />
    <hkern u1="&#x453;" u2="&#x459;" k="40" />
    <hkern u1="&#x453;" u2="&#x43b;" k="40" />
    <hkern u1="&#x453;" u2="&#x434;" k="50" />
    <hkern u1="&#x453;" u2="&#x2e;" k="98" />
    <hkern u1="&#x453;" u2="&#x2c;" k="98" />
    <hkern u1="&#x459;" u2="&#x45e;" k="40" />
    <hkern u1="&#x459;" u2="&#x459;" k="20" />
    <hkern u1="&#x459;" u2="&#x447;" k="30" />
    <hkern u1="&#x459;" u2="&#x445;" k="25" />
    <hkern u1="&#x459;" u2="&#x443;" k="40" />
    <hkern u1="&#x459;" u2="&#x442;" k="50" />
    <hkern u1="&#x459;" u2="&#x43b;" k="20" />
    <hkern u1="&#x459;" u2="&#x436;" k="45" />
    <hkern u1="&#x459;" u2="&#x434;" k="30" />
    <hkern u1="&#x45a;" u2="&#x45e;" k="40" />
    <hkern u1="&#x45a;" u2="&#x459;" k="20" />
    <hkern u1="&#x45a;" u2="&#x447;" k="30" />
    <hkern u1="&#x45a;" u2="&#x445;" k="25" />
    <hkern u1="&#x45a;" u2="&#x443;" k="40" />
    <hkern u1="&#x45a;" u2="&#x442;" k="50" />
    <hkern u1="&#x45a;" u2="&#x43b;" k="20" />
    <hkern u1="&#x45a;" u2="&#x436;" k="45" />
    <hkern u1="&#x45a;" u2="&#x434;" k="30" />
    <hkern u1="&#x45c;" u2="&#x454;" k="30" />
    <hkern u1="&#x45c;" u2="&#x451;" k="30" />
    <hkern u1="&#x45c;" u2="&#x444;" k="30" />
    <hkern u1="&#x45c;" u2="&#x441;" k="30" />
    <hkern u1="&#x45c;" u2="&#x43e;" k="30" />
    <hkern u1="&#x45c;" u2="&#x435;" k="30" />
    <hkern u1="&#x45e;" u2="&#x2026;" k="64" />
    <hkern u1="&#x45e;" u2="&#x459;" k="35" />
    <hkern u1="&#x45e;" u2="&#x43b;" k="35" />
    <hkern u1="&#x45e;" u2="&#x434;" k="50" />
    <hkern u1="&#x45e;" u2="&#x2e;" k="64" />
    <hkern u1="&#x45e;" u2="&#x2c;" k="64" />
    <hkern u1="&#x490;" u2="&#x203a;" k="93" />
    <hkern u1="&#x490;" u2="&#x2026;" k="74" />
    <hkern u1="&#x490;" u2="&#x2014;" k="93" />
    <hkern u1="&#x490;" u2="&#x2013;" k="93" />
    <hkern u1="&#x490;" u2="&#x491;" k="74" />
    <hkern u1="&#x490;" u2="&#x45f;" k="74" />
    <hkern u1="&#x490;" u2="&#x45e;" k="54" />
    <hkern u1="&#x490;" u2="&#x45c;" k="74" />
    <hkern u1="&#x490;" u2="&#x45a;" k="105" />
    <hkern u1="&#x490;" u2="&#x459;" k="88" />
    <hkern u1="&#x490;" u2="&#x455;" k="83" />
    <hkern u1="&#x490;" u2="&#x454;" k="80" />
    <hkern u1="&#x490;" u2="&#x453;" k="74" />
    <hkern u1="&#x490;" u2="&#x451;" k="80" />
    <hkern u1="&#x490;" u2="&#x44f;" k="83" />
    <hkern u1="&#x490;" u2="&#x44e;" k="74" />
    <hkern u1="&#x490;" u2="&#x44d;" k="80" />
    <hkern u1="&#x490;" u2="&#x44c;" k="105" />
    <hkern u1="&#x490;" u2="&#x44b;" k="74" />
    <hkern u1="&#x490;" u2="&#x44a;" k="34" />
    <hkern u1="&#x490;" u2="&#x449;" k="74" />
    <hkern u1="&#x490;" u2="&#x448;" k="74" />
    <hkern u1="&#x490;" u2="&#x447;" k="88" />
    <hkern u1="&#x490;" u2="&#x446;" k="74" />
    <hkern u1="&#x490;" u2="&#x445;" k="44" />
    <hkern u1="&#x490;" u2="&#x444;" k="83" />
    <hkern u1="&#x490;" u2="&#x443;" k="54" />
    <hkern u1="&#x490;" u2="&#x441;" k="80" />
    <hkern u1="&#x490;" u2="&#x440;" k="83" />
    <hkern u1="&#x490;" u2="&#x43f;" k="74" />
    <hkern u1="&#x490;" u2="&#x43e;" k="80" />
    <hkern u1="&#x490;" u2="&#x43d;" k="74" />
    <hkern u1="&#x490;" u2="&#x43c;" k="74" />
    <hkern u1="&#x490;" u2="&#x43b;" k="88" />
    <hkern u1="&#x490;" u2="&#x43a;" k="74" />
    <hkern u1="&#x490;" u2="&#x439;" k="74" />
    <hkern u1="&#x490;" u2="&#x438;" k="74" />
    <hkern u1="&#x490;" u2="&#x437;" k="54" />
    <hkern u1="&#x490;" u2="&#x436;" k="64" />
    <hkern u1="&#x490;" u2="&#x435;" k="80" />
    <hkern u1="&#x490;" u2="&#x434;" k="113" />
    <hkern u1="&#x490;" u2="&#x433;" k="74" />
    <hkern u1="&#x490;" u2="&#x432;" k="74" />
    <hkern u1="&#x490;" u2="&#x431;" k="39" />
    <hkern u1="&#x490;" u2="&#x430;" k="93" />
    <hkern u1="&#x490;" u2="&#x424;" k="50" />
    <hkern u1="&#x490;" u2="&#x421;" k="30" />
    <hkern u1="&#x490;" u2="&#x41e;" k="30" />
    <hkern u1="&#x490;" u2="&#x41b;" k="70" />
    <hkern u1="&#x490;" u2="&#x414;" k="70" />
    <hkern u1="&#x490;" u2="&#x410;" k="70" />
    <hkern u1="&#x490;" u2="&#x409;" k="70" />
    <hkern u1="&#x490;" u2="&#x408;" k="121" />
    <hkern u1="&#x490;" u2="&#x404;" k="30" />
    <hkern u1="&#x490;" u2="&#xbb;" k="93" />
    <hkern u1="&#x490;" u2="&#x2e;" k="74" />
    <hkern u1="&#x490;" u2="&#x2d;" k="93" />
    <hkern u1="&#x490;" u2="&#x2c;" k="74" />
    <hkern u1="&#x491;" u2="&#x2026;" k="89" />
    <hkern u1="&#x491;" u2="&#x459;" k="40" />
    <hkern u1="&#x491;" u2="&#x43b;" k="40" />
    <hkern u1="&#x491;" u2="&#x434;" k="50" />
    <hkern u1="&#x491;" u2="&#x2e;" k="89" />
    <hkern u1="&#x491;" u2="&#x2c;" k="89" />
    <hkern u1="&#x2013;" u2="&#x422;" k="66" />
    <hkern u1="&#x2013;" u2="&#x3c7;" k="52" />
    <hkern u1="&#x2013;" u2="&#x3bb;" k="30" />
    <hkern u1="&#x2013;" u2="&#x3ab;" k="56" />
    <hkern u1="&#x2013;" u2="&#x3a5;" k="56" />
    <hkern u1="&#x2013;" u2="&#x3a4;" k="70" />
    <hkern u1="&#x2013;" u2="&#x178;" k="37" />
    <hkern u1="&#x2013;" u2="&#x176;" k="37" />
    <hkern u1="&#x2013;" u2="&#x166;" k="29" />
    <hkern u1="&#x2013;" u2="&#x164;" k="29" />
    <hkern u1="&#x2013;" u2="&#x162;" k="29" />
    <hkern u1="&#x2013;" u2="&#xdd;" k="37" />
    <hkern u1="&#x2013;" u2="Y" k="37" />
    <hkern u1="&#x2013;" u2="X" k="8" />
    <hkern u1="&#x2013;" u2="V" k="12" />
    <hkern u1="&#x2013;" u2="T" k="29" />
    <hkern u1="&#x2014;" u2="&#x422;" k="66" />
    <hkern u1="&#x2014;" u2="&#x3c7;" k="52" />
    <hkern u1="&#x2014;" u2="&#x3bb;" k="30" />
    <hkern u1="&#x2014;" u2="&#x3ab;" k="56" />
    <hkern u1="&#x2014;" u2="&#x3a5;" k="56" />
    <hkern u1="&#x2014;" u2="&#x3a4;" k="70" />
    <hkern u1="&#x2014;" u2="&#x178;" k="37" />
    <hkern u1="&#x2014;" u2="&#x176;" k="37" />
    <hkern u1="&#x2014;" u2="&#x166;" k="29" />
    <hkern u1="&#x2014;" u2="&#x164;" k="29" />
    <hkern u1="&#x2014;" u2="&#x162;" k="29" />
    <hkern u1="&#x2014;" u2="&#xdd;" k="37" />
    <hkern u1="&#x2014;" u2="Y" k="37" />
    <hkern u1="&#x2014;" u2="X" k="8" />
    <hkern u1="&#x2014;" u2="V" k="12" />
    <hkern u1="&#x2014;" u2="T" k="29" />
    <hkern u1="&#x2018;" u2="&#x434;" k="30" />
    <hkern u1="&#x2018;" u2="&#x414;" k="44" />
    <hkern u1="&#x2018;" u2="&#x410;" k="54" />
    <hkern u1="&#x2018;" u2="&#x134;" k="86" />
    <hkern u1="&#x2018;" u2="&#x104;" k="32" />
    <hkern u1="&#x2018;" u2="&#x102;" k="32" />
    <hkern u1="&#x2018;" u2="&#x100;" k="32" />
    <hkern u1="&#x2018;" u2="&#xc5;" k="32" />
    <hkern u1="&#x2018;" u2="&#xc4;" k="32" />
    <hkern u1="&#x2018;" u2="&#xc3;" k="32" />
    <hkern u1="&#x2018;" u2="&#xc2;" k="32" />
    <hkern u1="&#x2018;" u2="&#xc1;" k="32" />
    <hkern u1="&#x2018;" u2="&#xc0;" k="32" />
    <hkern u1="&#x2018;" u2="J" k="86" />
    <hkern u1="&#x2018;" u2="A" k="32" />
    <hkern u1="&#x2019;" u2="&#x3ce;" k="90" />
    <hkern u1="&#x2019;" u2="&#x3cc;" k="80" />
    <hkern u1="&#x2019;" u2="&#x3ca;" k="40" />
    <hkern u1="&#x2019;" u2="&#x3c9;" k="90" />
    <hkern u1="&#x2019;" u2="&#x3c8;" k="40" />
    <hkern u1="&#x2019;" u2="&#x3c6;" k="90" />
    <hkern u1="&#x2019;" u2="&#x3c4;" k="20" />
    <hkern u1="&#x2019;" u2="&#x3c3;" k="80" />
    <hkern u1="&#x2019;" u2="&#x3c2;" k="60" />
    <hkern u1="&#x2019;" u2="&#x3c1;" k="100" />
    <hkern u1="&#x2019;" u2="&#x3c0;" k="30" />
    <hkern u1="&#x2019;" u2="&#x3bf;" k="80" />
    <hkern u1="&#x2019;" u2="&#x3ba;" k="30" />
    <hkern u1="&#x2019;" u2="&#x3b9;" k="40" />
    <hkern u1="&#x2019;" u2="&#x3b8;" k="30" />
    <hkern u1="&#x2019;" u2="&#x3b6;" k="30" />
    <hkern u1="&#x2019;" u2="&#x3b5;" k="90" />
    <hkern u1="&#x2019;" u2="&#x3b4;" k="55" />
    <hkern u1="&#x2019;" u2="&#x3b2;" k="25" />
    <hkern u1="&#x2019;" u2="&#x3b1;" k="100" />
    <hkern u1="&#x2019;" u2="&#x3af;" k="40" />
    <hkern u1="&#x2019;" u2="&#x3ad;" k="90" />
    <hkern u1="&#x2019;" u2="&#x3ac;" k="100" />
    <hkern u1="&#x2019;" u2="&#x3a6;" k="61" />
    <hkern u1="&#x2019;" u2="&#x39f;" k="30" />
    <hkern u1="&#x2019;" u2="&#x39b;" k="100" />
    <hkern u1="&#x2019;" u2="&#x398;" k="30" />
    <hkern u1="&#x2019;" u2="&#x394;" k="100" />
    <hkern u1="&#x2019;" u2="&#x391;" k="100" />
    <hkern u1="&#x2019;" u2="&#x390;" k="40" />
    <hkern u1="&#x2019;" u2="&#x219;" k="32" />
    <hkern u1="&#x2019;" u2="&#x161;" k="32" />
    <hkern u1="&#x2019;" u2="&#x15f;" k="32" />
    <hkern u1="&#x2019;" u2="&#x15d;" k="32" />
    <hkern u1="&#x2019;" u2="&#x15b;" k="32" />
    <hkern u1="&#x2019;" u2="&#x159;" k="23" />
    <hkern u1="&#x2019;" u2="&#x157;" k="23" />
    <hkern u1="&#x2019;" u2="&#x155;" k="23" />
    <hkern u1="&#x2019;" u2="&#x148;" k="19" />
    <hkern u1="&#x2019;" u2="&#x146;" k="19" />
    <hkern u1="&#x2019;" u2="&#x144;" k="19" />
    <hkern u1="&#x2019;" u2="&#xf1;" k="19" />
    <hkern u1="&#x2019;" u2="&#xb5;" k="55" />
    <hkern u1="&#x2019;" u2="s" k="32" />
    <hkern u1="&#x2019;" u2="r" k="23" />
    <hkern u1="&#x2019;" u2="n" k="19" />
    <hkern u1="&#x201a;" u2="&#x177;" k="42" />
    <hkern u1="&#x201a;" u2="&#x175;" k="33" />
    <hkern u1="&#x201a;" u2="&#x167;" k="28" />
    <hkern u1="&#x201a;" u2="&#x165;" k="28" />
    <hkern u1="&#x201a;" u2="&#x163;" k="28" />
    <hkern u1="&#x201a;" u2="&#xff;" k="42" />
    <hkern u1="&#x201a;" u2="&#xfd;" k="42" />
    <hkern u1="&#x201a;" u2="y" k="42" />
    <hkern u1="&#x201a;" u2="w" k="33" />
    <hkern u1="&#x201a;" u2="v" k="37" />
    <hkern u1="&#x201a;" u2="t" k="28" />
    <hkern u1="&#x201c;" u2="&#x434;" k="30" />
    <hkern u1="&#x201c;" u2="&#x414;" k="44" />
    <hkern u1="&#x201c;" u2="&#x410;" k="54" />
    <hkern u1="&#x201c;" u2="&#x134;" k="86" />
    <hkern u1="&#x201c;" u2="&#x104;" k="32" />
    <hkern u1="&#x201c;" u2="&#x102;" k="32" />
    <hkern u1="&#x201c;" u2="&#x100;" k="32" />
    <hkern u1="&#x201c;" u2="&#xc5;" k="32" />
    <hkern u1="&#x201c;" u2="&#xc4;" k="32" />
    <hkern u1="&#x201c;" u2="&#xc3;" k="32" />
    <hkern u1="&#x201c;" u2="&#xc2;" k="32" />
    <hkern u1="&#x201c;" u2="&#xc1;" k="32" />
    <hkern u1="&#x201c;" u2="&#xc0;" k="32" />
    <hkern u1="&#x201c;" u2="J" k="86" />
    <hkern u1="&#x201c;" u2="A" k="32" />
    <hkern u1="&#x201d;" u2="&#x3ce;" k="90" />
    <hkern u1="&#x201d;" u2="&#x3cc;" k="80" />
    <hkern u1="&#x201d;" u2="&#x3ca;" k="40" />
    <hkern u1="&#x201d;" u2="&#x3c9;" k="90" />
    <hkern u1="&#x201d;" u2="&#x3c8;" k="40" />
    <hkern u1="&#x201d;" u2="&#x3c6;" k="90" />
    <hkern u1="&#x201d;" u2="&#x3c4;" k="20" />
    <hkern u1="&#x201d;" u2="&#x3c3;" k="80" />
    <hkern u1="&#x201d;" u2="&#x3c2;" k="60" />
    <hkern u1="&#x201d;" u2="&#x3c1;" k="100" />
    <hkern u1="&#x201d;" u2="&#x3c0;" k="30" />
    <hkern u1="&#x201d;" u2="&#x3bf;" k="80" />
    <hkern u1="&#x201d;" u2="&#x3ba;" k="30" />
    <hkern u1="&#x201d;" u2="&#x3b9;" k="40" />
    <hkern u1="&#x201d;" u2="&#x3b8;" k="30" />
    <hkern u1="&#x201d;" u2="&#x3b6;" k="30" />
    <hkern u1="&#x201d;" u2="&#x3b5;" k="90" />
    <hkern u1="&#x201d;" u2="&#x3b4;" k="55" />
    <hkern u1="&#x201d;" u2="&#x3b2;" k="25" />
    <hkern u1="&#x201d;" u2="&#x3b1;" k="100" />
    <hkern u1="&#x201d;" u2="&#x3af;" k="40" />
    <hkern u1="&#x201d;" u2="&#x3ad;" k="90" />
    <hkern u1="&#x201d;" u2="&#x3ac;" k="100" />
    <hkern u1="&#x201d;" u2="&#x3a6;" k="61" />
    <hkern u1="&#x201d;" u2="&#x39f;" k="30" />
    <hkern u1="&#x201d;" u2="&#x39b;" k="100" />
    <hkern u1="&#x201d;" u2="&#x398;" k="30" />
    <hkern u1="&#x201d;" u2="&#x394;" k="100" />
    <hkern u1="&#x201d;" u2="&#x391;" k="100" />
    <hkern u1="&#x201d;" u2="&#x390;" k="40" />
    <hkern u1="&#x201d;" u2="&#x219;" k="32" />
    <hkern u1="&#x201d;" u2="&#x161;" k="32" />
    <hkern u1="&#x201d;" u2="&#x15f;" k="32" />
    <hkern u1="&#x201d;" u2="&#x15d;" k="32" />
    <hkern u1="&#x201d;" u2="&#x15b;" k="32" />
    <hkern u1="&#x201d;" u2="&#x159;" k="23" />
    <hkern u1="&#x201d;" u2="&#x157;" k="23" />
    <hkern u1="&#x201d;" u2="&#x155;" k="23" />
    <hkern u1="&#x201d;" u2="&#x148;" k="19" />
    <hkern u1="&#x201d;" u2="&#x146;" k="19" />
    <hkern u1="&#x201d;" u2="&#x144;" k="19" />
    <hkern u1="&#x201d;" u2="&#xf1;" k="19" />
    <hkern u1="&#x201d;" u2="&#xb5;" k="55" />
    <hkern u1="&#x201d;" u2="s" k="32" />
    <hkern u1="&#x201d;" u2="r" k="23" />
    <hkern u1="&#x201d;" u2="n" k="19" />
    <hkern u1="&#x201e;" u2="&#x177;" k="42" />
    <hkern u1="&#x201e;" u2="&#x175;" k="33" />
    <hkern u1="&#x201e;" u2="&#x167;" k="28" />
    <hkern u1="&#x201e;" u2="&#x165;" k="28" />
    <hkern u1="&#x201e;" u2="&#x163;" k="28" />
    <hkern u1="&#x201e;" u2="&#xff;" k="42" />
    <hkern u1="&#x201e;" u2="&#xfd;" k="42" />
    <hkern u1="&#x201e;" u2="y" k="42" />
    <hkern u1="&#x201e;" u2="w" k="33" />
    <hkern u1="&#x201e;" u2="v" k="37" />
    <hkern u1="&#x201e;" u2="t" k="28" />
    <hkern u1="&#x2026;" u2="&#x45e;" k="45" />
    <hkern u1="&#x2026;" u2="&#x447;" k="45" />
    <hkern u1="&#x2026;" u2="&#x443;" k="45" />
    <hkern u1="&#x2026;" u2="&#x442;" k="54" />
    <hkern u1="&#x2026;" u2="&#x427;" k="84" />
    <hkern u1="&#x2026;" u2="&#x424;" k="30" />
    <hkern u1="&#x2026;" u2="&#x422;" k="59" />
    <hkern u1="&#x2026;" u2="&#x3c7;" k="43" />
    <hkern u1="&#x2026;" u2="&#x3c4;" k="68" />
    <hkern u1="&#x2026;" u2="&#x3c0;" k="47" />
    <hkern u1="&#x2026;" u2="&#x3bd;" k="60" />
    <hkern u1="&#x2026;" u2="&#x3b3;" k="55" />
    <hkern u1="&#x2026;" u2="&#x3ab;" k="100" />
    <hkern u1="&#x2026;" u2="&#x3a8;" k="77" />
    <hkern u1="&#x2026;" u2="&#x3a6;" k="40" />
    <hkern u1="&#x2026;" u2="&#x3a5;" k="100" />
    <hkern u1="&#x2026;" u2="&#x3a4;" k="64" />
    <hkern u1="&#x2026;" u2="&#x178;" k="84" />
    <hkern u1="&#x2026;" u2="&#x177;" k="28" />
    <hkern u1="&#x2026;" u2="&#x176;" k="84" />
    <hkern u1="&#x2026;" u2="&#x175;" k="33" />
    <hkern u1="&#x2026;" u2="&#x174;" k="30" />
    <hkern u1="&#x2026;" u2="&#x166;" k="80" />
    <hkern u1="&#x2026;" u2="&#x164;" k="80" />
    <hkern u1="&#x2026;" u2="&#x162;" k="80" />
    <hkern u1="&#x2026;" u2="&#xff;" k="28" />
    <hkern u1="&#x2026;" u2="&#xfd;" k="28" />
    <hkern u1="&#x2026;" u2="&#xdd;" k="84" />
    <hkern u1="&#x2026;" u2="y" k="28" />
    <hkern u1="&#x2026;" u2="w" k="33" />
    <hkern u1="&#x2026;" u2="v" k="47" />
    <hkern u1="&#x2026;" u2="Y" k="84" />
    <hkern u1="&#x2026;" u2="W" k="30" />
    <hkern u1="&#x2026;" u2="V" k="55" />
    <hkern u1="&#x2026;" u2="T" k="80" />
    <hkern u1="&#x2039;" u2="&#x422;" k="50" />
    <hkern u1="&#x2039;" u2="&#x3ab;" k="40" />
    <hkern u1="&#x2039;" u2="&#x3a5;" k="40" />
    <hkern u1="&#x2039;" u2="&#x3a4;" k="30" />
    <hkern u1="&#x203a;" u2="&#x178;" k="56" />
    <hkern u1="&#x203a;" u2="&#x176;" k="56" />
    <hkern u1="&#x203a;" u2="&#x166;" k="61" />
    <hkern u1="&#x203a;" u2="&#x164;" k="61" />
    <hkern u1="&#x203a;" u2="&#x162;" k="61" />
    <hkern u1="&#x203a;" u2="&#xdd;" k="56" />
    <hkern u1="&#x203a;" u2="Y" k="56" />
    <hkern u1="&#x203a;" u2="V" k="32" />
    <hkern u1="&#x203a;" u2="T" k="61" />
    <hkern u1="&#xfb02;" u2="&#x17d;" k="16" />
    <hkern u1="&#xfb02;" u2="&#x17b;" k="16" />
    <hkern u1="&#xfb02;" u2="&#x179;" k="16" />
    <hkern u1="&#xfb02;" u2="&#x178;" k="40" />
    <hkern u1="&#xfb02;" u2="&#x176;" k="40" />
    <hkern u1="&#xfb02;" u2="&#x174;" k="5" />
    <hkern u1="&#xfb02;" u2="&#x166;" k="41" />
    <hkern u1="&#xfb02;" u2="&#x164;" k="41" />
    <hkern u1="&#xfb02;" u2="&#x162;" k="41" />
    <hkern u1="&#xfb02;" u2="&#x104;" k="25" />
    <hkern u1="&#xfb02;" u2="&#x102;" k="25" />
    <hkern u1="&#xfb02;" u2="&#x100;" k="25" />
    <hkern u1="&#xfb02;" u2="&#xdd;" k="40" />
    <hkern u1="&#xfb02;" u2="&#xc5;" k="25" />
    <hkern u1="&#xfb02;" u2="&#xc4;" k="25" />
    <hkern u1="&#xfb02;" u2="&#xc3;" k="25" />
    <hkern u1="&#xfb02;" u2="&#xc2;" k="25" />
    <hkern u1="&#xfb02;" u2="&#xc1;" k="25" />
    <hkern u1="&#xfb02;" u2="&#xc0;" k="25" />
    <hkern u1="&#xfb02;" u2="Z" k="16" />
    <hkern u1="&#xfb02;" u2="Y" k="40" />
    <hkern u1="&#xfb02;" u2="X" k="25" />
    <hkern u1="&#xfb02;" u2="W" k="5" />
    <hkern u1="&#xfb02;" u2="V" k="23" />
    <hkern u1="&#xfb02;" u2="T" k="41" />
    <hkern u1="&#xfb02;" u2="A" k="25" />
  </font>
</defs></svg>
