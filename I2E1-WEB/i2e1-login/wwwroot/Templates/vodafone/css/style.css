*, body { margin:0; padding:0; }

.container { margin:0 auto; position:absolute; left:0; right:0; width:100%; max-width:640px; max-height:1136px; }
.background, .logo img, .footer img, .offer img { width:100%; }
.logo { position:absolute; left:0; top:0; width:21.875%; max-width:140px; max-height:190px; }
.footer { position:absolute; bottom:0; left:0; right:0; margin:0 auto 10px auto; width:26.25%; max-width:168px; max-height:38px; }

.main_content { text-align:center; margin:0 auto; font-family:VodafoneLt; position:absolute; left:0; right:0; top:10%; width:96%; padding:0 2%; }

.redColor { color:#e60000; }
h4, h5, p, input[type="text"] { color:#33342e; }
p { font-family:Arial, Helvetica, sans-serif; }

h1 { font-size:3.2em; }
h1 sup { font-weight:100; font-size:0.6em; }
.content1 h1 { margin-bottom:8%; }
.content2 h1, .content3 h1, .content4 h1 { margin:5px 0; }
h2 { font-size:2.8em; }
h3 { font-size:2.2em; }
h4 { font-size:2em; }
h5 { font-family:VodafoneRgBd; font-size:2em; font-weight:normal; margin-top:15%; }
p { font-size:1.4em; margin-top:15%; }
.content1 p { margin-top:0; }

.content2, .content3, .content4, #frmMobile, #frmOtp, .promotional_offer { display:none; }

input[type="text"] { margin-top:4%; border:1px solid #555; padding:15px 20px; width:49.6875%; max-width:360px; font-size:1.2em; text-align:center; }

.button { display:block; width:140px; height:48px; line-height:48px; border-radius:5px; margin:5% auto 0 auto; text-decoration:none; color:#fff; background-color:#b12fad; text-transform:uppercase; font-size:1.2em; font-family:VodafoneRgBd; }

.offer { width:58.5938%; max-width:360px; max-height:150px; display:block; margin:0 auto 8% auto; }
.offer span { display:table-cell; vertical-align:middle; font-size:1em; }
.redColor { font-weight:bold; }

@media (min-width:300px) and (max-width:450px){
	h1 { font-size:1.8em; }
	h2 { font-size:1.6em; }
	h3 { font-size:1.4em; }
	h4 { font-size:1.3em; }
	h5 { font-size:1.2em; }
	p { font-size:0.9em; }
	
	input[type="text"] { padding:8px 15px; }
	.button { width:100px; font-size:1em; height:40px; line-height:40px; }
	.offer span { font-size:0.8em; }
}

@media (min-width:451px) and (max-width:570px){
	h1 { font-size:2.1em; }
	h2 { font-size:1.9em; }
	h3 { font-size:1.7em; }
	h4 { font-size:1.5em; }
	h5 { font-size:1.4em; }
	p { font-size:1.1em; }
	
	input[type="text"] { padding:10px 15px; }
	.button { width:120px; font-size:1.1em; height:44px; line-height:44px; }
	.offer span { font-size:0.9em; }
}
