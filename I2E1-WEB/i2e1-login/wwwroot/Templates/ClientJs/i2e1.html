<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.0 Transitional//EN">
<html>
<head>
    <title>i2e1</title>
    <meta name="viewport" content="width=device-width" />
    <style>
        img {
            image-rendering: auto;
            image-rendering: crisp-edges;
            image-rendering: pixelated;

        }
    </style>
    <script type="text/javascript" language="Javascript">

function getURLParam(n) {
  var s = "";
  var href = window.location.href;
  var idx = href.indexOf("?");
  if (idx > -1) {
    var qs = href.substr(idx + 1);
    idx = qs.indexOf(n + "=");
    if (idx > -1) {
      s = qs.substr(idx + n.length + 1);
      if (s.charAt(4) == '%' || s.charAt(5) == '%') {
        s = s.replace(/%3a/g, ":");
        s = s.replace(/%2f/g, "/");
        s = s.replace(/%3f/g, "?");
        s = s.replace(/%3d/g, "=");
        s = s.replace(/%26/g, "&");
        s = s.replace(/%25/g, "%");
        s = s.replace(/\+/g, " ");
      }
      return s;
    }
  }
}


var loginUrl, nasId, macId;
function redirect() { window.location = loginUrl+"&utm_source=NAS-"+nasId; return false; }

function logEvent () {
    var xhr = new XMLHttpRequest();
    xhr.open('POST', 'https://www.i2e1.in/Login/ls');
    xhr.setRequestHeader("Content-type", "application/x-www-form-urlencoded");
    xhr.send("nasid="+nasId+"&macId="+macId);
}
window.onload = function() {
	loginUrl = getURLParam("loginurl");
	nasId = (((new RegExp('[?|&]' + 'nasid' + '=' + '([^&;]+?)(&|#|;|$)').exec(loginUrl))||[,""])[1].replace(/\+/g, '%20'))||"null";
	macId = (((new RegExp('[?|&]' + 'mac' + '=' + '([^&;]+?)(&|#|;|$)').exec(loginUrl))||[,""])[1].replace(/\+/g, '%20'))||"null";
	logEvent(nasId, macId);
	setTimeout(redirect, 300);
}

</script>
<meta http-equiv="refresh" content="3; URL=/prelogin">
</head>
<body style="margin: 0pt auto; height:100%;">
    <div style="width:100%;height:80%;position:fixed;display:table;">
        <p style="display: table-cell; line-height: 2.5em;vertical-align:middle;text-align:center;color:grey;">
            <a href="#" onclick="javascript:return redirect();">
                <img src="logo.png" width="180px" border="0" />
            </a><br>
            <small><img src="wait.gif" /> You are about to enjoy our free wi-fi</small>
        </p>
        <br><br>
    </div>
</body>
</html>
