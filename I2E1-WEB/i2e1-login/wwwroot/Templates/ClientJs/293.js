console.dir('template validator loaded');
var _i2e1TemplateUtils = _i2e1TemplateUtils || {};

_i2e1TemplateUtils.postload = function(options){

    if (options.state == _i2e1Constants.landingState) 
        _stateFunctions.landingState();
    
}

_i2e1TemplateUtils.postLogin = function () {
}

_stateFunctions.landingState = function(options) {
	$('.landing-page-tiles-container').empty();
	i=j=1;
	var intv = setInterval(function() {
		i++;
		j+=500;
		
		if(i == 99) i = 1;
		if(j==49999999) j = 1;
		$('.landing-page-tiles-container').html("<div class='card'>Your shopping rank is <p>" 
									+ formatNumber(j) + "</p> amongst 5 crore shoppers</div> <div class='card'>which makes you amongst top<p>" 
									+ i + "%</p> spenders in India</div>");
	}, 30);
	
	i2e1Api.doAjax('/Login/GetGlobalSpendxRank', {}, function(response) {
		console.log(response);
		var index;
		for(var i = 0; i<_percenttileList.length; i++) {
			if(_percenttileList[i].devicePrice >= response.data) {
				index = i;
				break;
			}
		}
		
		if (index < 19 ) index = index + 1;
		
		var extra = (response.data - _percenttileList[index - 1].devicePrice)/(_percenttileList[index].devicePrice - _percenttileList[index - 1].devicePrice);
		var percentile = _percenttileList[index - 1].percentile + (extra * (_percenttileList[index].percentile - _percenttileList[index - 1].percentile));
		var rank = Math.floor(50000000 - (Math.random() * (_percenttileList[index].rank - _percenttileList[index - 1].rank)) - _percenttileList[index - 1].rank);
		
		if (rank < 100000) rank = 100000 + (Math.random()* 1005);
		
		console.log('percentile -- ' + percentile);
		console.log('rank -- ' + rank);
		clearInterval(intv);
		
		$('.landing-page-tiles-container').html("<div class='card'>Your shopping rank is <p>" 
									+ formatNumber(rank) + "</p> amongst 5 crore shoppers</div> <div class='card'>which makes you amongst top<p>" 
									+ Math.round((100 - percentile) * 100) / 100 + "%</p> spenders in India</div> <div class='proceed'><input type='button' onclick='func()' value='Enable Internet'/></div>");
		
	}, null, { hideLoading: true });
	
}
var func = function () {
	
	 window.parent.postMessage({
        action: '_fullRedirect',
        link: 'https://www.i2e1.com'
    }, '*');
	
}
var _percenttileList = [
	{ devicePrice: 7630		, percentile: 5	 	, rank:  2500000 },
	{ devicePrice: 9100		, percentile: 10	, rank:  5000000 },
	{ devicePrice: 9870		, percentile: 15	, rank:  7500000 },
	{ devicePrice: 11060	, percentile: 20	, rank: 10000000 },
	{ devicePrice: 12180	, percentile: 25	, rank: 12500000 },
	{ devicePrice: 13440	, percentile: 30	, rank: 15000000 },
	{ devicePrice: 14140	, percentile: 35	, rank: 17500000 },
	{ devicePrice: 14910	, percentile: 40	, rank: 20000000 },
	{ devicePrice: 15190	, percentile: 45	, rank: 22500000 },
	{ devicePrice: 15960	, percentile: 50	, rank: 25000000 },
	{ devicePrice: 16590	, percentile: 55	, rank: 27500000 },
	{ devicePrice: 17430	, percentile: 60	, rank: 30000000 },
	{ devicePrice: 17920	, percentile: 65	, rank: 32500000 },
	{ devicePrice: 18620	, percentile: 70	, rank: 35000000 },
	{ devicePrice: 22330	, percentile: 75	, rank: 37500000 },
	{ devicePrice: 27090	, percentile: 80	, rank: 40000000 },
	{ devicePrice: 32760	, percentile: 85	, rank: 42500000 },
	{ devicePrice: 34300	, percentile: 90	, rank: 45000000 },
	{ devicePrice: 38010	, percentile: 95	, rank: 47500000 },
	{ devicePrice: 100000	, percentile: 100	, rank: 50000000 }
]

var formatNumber = function (number) {
    var x1 = number;
	var ld = number%10;
    x1 = x1.toString().replace(/(\d)(?=(\d\d)+\d$)/g, "$1,");
   	
	switch(ld){
		case 1: x1 += '<span>st</span>'; break;
		case 2: x1 += '<span>nd</span>'; break;
		case 3: x1 += '<span>rd</span>'; break;
		default : x1 += '<span>th</span>'; break;
	}
	
    return x1 ? x1 : 0;
};
