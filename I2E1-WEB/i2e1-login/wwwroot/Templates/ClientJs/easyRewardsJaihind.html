<style>
body{
background-image: url('https://i2e1storage.blob.core.windows.net/images/easyrewardBackground.png')
}
.signup-form input{
height: 3rem !important;
}
.login.container{
background-color: rgba(242, 214, 214, 0.4);
}
a{
color: #1515b9;
}
</style>
<script>
var _templateBag = _templateBag || {};
_templateBag.otpLength = 6;

function generateOTPClick(){
 _generateOTP(false, function(response){ 
     if(response.msg === "SignUp"){  
  $('.custom-state').html($('.signup-state').html()); 
  $('.signup-form #signup-mobile').val($('#username').val());
  _swapState(_i2e1Constants.customState);
  }
     else _swapState(_i2e1Constants.secondState);

 });
}
</script>

<outer-header>
<img style="width: 11rem;" src="https://i2e1storage.blob.core.windows.net/images/JaiHind_Logo.jpg"/>
</outer-header>
<inner-header>
Get <img class="wifi-img" src="/images/grey_wifi.png" /><span class="rainbow" style="display:none;"></span> with Easy Rewards
</inner-header>

                <generate-otp>
                    <input type="button" id="get_otp" name="enter" class="primary" value="Next"  onclick="generateOTPClick()"/>
                    <span class="tnc">By clicking "Get OTP" you agree to our <a target="_blank" href="/Templates/i2e1/tnc.html">Terms & Conditions</a></span>
                </generate-otp>

<div class="signup-state" style="display: none">
<form class="form signup-form" action="/Login/Signup" method="post">
<p style="text-align:left">Enter details to Sign up</p>
 <input name="mobile" id="signup-mobile" type="text" maxlength="10" title="Mobile No" placeholder="Mobile No*" required/>
 <input name="name" type="text" maxlength="32" title="First Name" placeholder="Name" required/>
 <input name="email" type="text" maxlength="64" title="Email" placeholder="Email" required/>
<select name="gender" style="display: inline-block;padding: 12px 10px;width: 100%;font-size: 1rem;" required>
  <option value="0">Select Gender</option>
  <option value="male">Male</option>
  <option value="female">Female</option>
</select>
<br/>
<p style="text-align:left;">DOB</p>
<select name="date" style="display: inline-block;padding: 12px 10px;width: 30%;font-size: 1rem;">
  <option value="0">DD</option>
  <option value="01">01</option>
  <option value="02">02</option>
  <option value="03">03</option>
  <option value="04">04</option>
  <option value="05">05</option>
  <option value="06">06</option>
  <option value="07">07</option>
  <option value="08">08</option>
  <option value="09">09</option>
  <option value="10">10</option>
  <option value="11">11</option>
  <option value="12">12</option>
  <option value="13">13</option>
  <option value="14">14</option>
  <option value="15">15</option>
  <option value="16">16</option>
  <option value="17">17</option>
  <option value="18">18</option>
  <option value="19">19</option>
  <option value="20">20</option>
  <option value="21">21</option>
  <option value="22">22</option>
  <option value="23">23</option>
  <option value="24">24</option>
  <option value="25">25</option>
  <option value="26">26</option>
  <option value="27">27</option>
  <option value="28">28</option>
  <option value="29">29</option>
  <option value="30">30</option>
  <option value="31">31</option>
 </select>
<select name="month" style="display: inline-block;padding: 12px 10px;width: 30%;font-size: 1rem;">
  <option value="0">MMM</option>
  <option value="01">Jan</option>
  <option value="02">Feb</option>
  <option value="03">Mar</option>
  <option value="04">Apr</option>
  <option value="05">May</option>
  <option value="06">Jun</option>
  <option value="07">Jul</option>
  <option value="08">Aug</option>
  <option value="09">Sep</option>
  <option value="10">Oct</option>
  <option value="11">Nov</option>
  <option value="12">Dec</option>

 </select>
<select name="year" style="display: inline-block;padding: 12px 10px;width: 37%;font-size: 1rem;">
  <option value="0" title="YYYY">YYYY</option>
  <option value="2017" title="2017">2017</option>
  <option value="2016" title="2016">2016</option>
  <option value="2015" title="2015">2015</option>
  <option value="2014" title="2014">2014</option>
  <option value="2013" title="2013">2013</option>
  <option value="2012" title="2012">2012</option>
  <option value="2011" title="2011">2011</option>
  <option value="2010" title="2010">2010</option>
  <option value="2009" title="2009">2009</option>
  <option value="2008" title="2008">2008</option>
  <option value="2007" title="2007">2007</option>
  <option value="2006" title="2006">2006</option>
  <option value="2005" title="2005">2005</option>
  <option value="2004" title="2004">2004</option>
  <option value="2003" title="2003">2003</option>
  <option value="2002" title="2002">2002</option>
  <option value="2001" title="2001">2001</option>
  <option value="2000" title="2000">2000</option>
  <option value="1999" title="1999">1999</option>
  <option value="1998" title="1998">1998</option>
  <option value="1997" title="1997">1997</option>
  <option value="1996" title="1996">1996</option>
  <option value="1995" title="1995">1995</option>
  <option value="1994" title="1994">1994</option>
  <option value="1993" title="1993">1993</option>
  <option value="1992" title="1992">1992</option>
  <option value="1991" title="1991">1991</option>
  <option value="1990" title="1990">1990</option>
  <option value="1989" title="1989">1989</option>
  <option value="1988" title="1988">1988</option>
  <option value="1987" title="1987">1987</option>
  <option value="1986" title="1986">1986</option>
  <option value="1985" title="1985">1985</option>
  <option value="1984" title="1984">1984</option>
  <option value="1983" title="1983">1983</option>
  <option value="1982" title="1982">1982</option>
  <option value="1981" title="1981">1981</option>
  <option value="1980" title="1980">1980</option>
  <option value="1979" title="1979">1979</option>
  <option value="1978" title="1978">1978</option>
  <option value="1977" title="1977">1977</option>
  <option value="1976" title="1976">1976</option>
  <option value="1975" title="1975">1975</option>
  <option value="1974" title="1974">1974</option>
  <option value="1973" title="1973">1973</option>
  <option value="1972" title="1972">1972</option>
  <option value="1971" title="1971">1971</option>
  <option value="1970" title="1970">1970</option>
  <option value="1969" title="1969">1969</option>
  <option value="1968" title="1968">1968</option>
  <option value="1967" title="1967">1967</option>
  <option value="1966" title="1966">1966</option>
  <option value="1965" title="1965">1965</option>
  <option value="1964" title="1964">1964</option>
  <option value="1963" title="1963">1963</option>
  <option value="1962" title="1962">1962</option>
  <option value="1961" title="1961">1961</option>
  <option value="1960" title="1960">1960</option>
  <option value="1959" title="1959">1959</option>
  <option value="1958" title="1958">1958</option>
  <option value="1957" title="1957">1957</option>
  <option value="1956" title="1956">1956</option>
  <option value="1955" title="1955">1955</option>
  <option value="1954" title="1954">1954</option>

 </select> 
<button class="primary" type="submit" style="margin:1rem 0;">Sign up</button>
 </form>
</div>