console.dir('template validator loaded');
var _i2e1TemplateUtils = _i2e1TemplateUtils || {};

_i2e1TemplateUtils.postload = function() {
	var p = $('[cage=outer-header] p');
	var msg = 'Have a Pleasant Stay At Hotel Mansingh Palace';
	switch(_loginUser.nasid) {
		case '602':
		case '3983':
			msg = 'Have a Pleasant Stay At Mansingh Palace Agra';		
		break;
		case '2335':
		case '2467':
			msg = 'Have a Pleasant Stay At Mansingh Palace, Jaipur';		
		break;
		case '2334':
			msg = 'Have a Pleasant Stay At Mansingh Palace, Ajmer';		
		break;			
	}	
	console.dir('updating header at nasid-'+ _loginUser.nasid + ' with - ' + msg);
	console.dir(p);
	p.text(msg);
}

var dataPlan = [
        {
            type: '0',
            details: [
                { 'name': 'Plan1', 'quality': "Fast", 'Speed': "2", 'Duration': "2", 'Charges': "150", 'color': "info", 'id': 'p1' },
                { 'name': 'Plan2', 'quality': "Fast", 'Speed': "2", 'Duration': "4", 'Charges': "250", 'color': "info", 'id': 'p2' },
                { 'name': 'Plan3', 'quality': "Fast", 'Speed': "2", 'Duration': "24", 'Charges': "500", 'color': "info", 'id': 'p3' }
            ]
        },
        {
            type: '1',
            details: [
                { 'name': 'Plan1', 'quality': "Fast", 'Speed': "2", 'Duration': "2", 'Charges': "150", 'color': "info", 'id': 'p4' },
                { 'name': 'Plan2', 'quality': "Fast", 'Speed': "2", 'Duration': "4", 'Charges': "250", 'color': "info", 'id': 'p5' },
                { 'name': 'Plan3', 'quality': "Fast", 'Speed': "2", 'Duration': "24", 'Charges': "500", 'color': "info", 'id': 'p6' },
                { 'name': 'Plan4', 'quality': "Normal", 'Speed': "1", 'Duration': "2", 'Charges': "0", 'color': "info", 'id': 'p7' }
            ]
        },
        {
            type: '2',
            details: [
                { 'name': 'Plan5', 'quality': "Normal", 'Speed': "1", 'Duration': "24", 'Charges': "0", 'color': "info", 'id': 'p8' }
            ]
        },
        {
            type: '3',
            details: [
                { 'name': 'Plan6', 'quality': "Fast", 'Speed': "2", 'Duration': "24", 'Charges': "0", 'color': "info", 'id': 'p9' }
            ]
        },
        {
            type: '4',
            details: [
                { 'name': 'Plan1', 'quality': "Fast", 'Speed': "2", 'Duration': "2", 'Charges': "150", 'color': "info", 'id': 'p10' },
                { 'name': 'Plan2', 'quality': "Fast", 'Speed': "2", 'Duration': "4", 'Charges': "250", 'color': "info", 'id': 'p11' },
                { 'name': 'Plan3', 'quality': "Fast", 'Speed': "2", 'Duration': "24", 'Charges': "500", 'color': "info", 'id': 'p12' },
                { 'name': 'Plan4', 'quality': "Normal", 'Speed': "1", 'Duration': "4", 'Charges': "0", 'color': "info", 'id': 'p13' }
            ]
        }

    ];
	
var dataPlan2 = [
        {
            type: '0',
            details: [
                { 'name': 'Plan1', 'quality': "Fast", 'Speed': "2", 'Duration': "2", 'Charges': "150", 'color': "info", 'id': 'p1' },
                { 'name': 'Plan2', 'quality': "Fast", 'Speed': "2", 'Duration': "4", 'Charges': "250", 'color': "info", 'id': 'p2' },
                { 'name': 'Plan3', 'quality': "Fast", 'Speed': "2", 'Duration': "24", 'Charges': "500", 'color': "info", 'id': 'p3' }
            ]
        },
        {
            type: '1',
            details: [
               { 'name': 'Plan1', 'quality': "Fast", 'Speed': "2", 'Duration': "2", 'Charges': "150", 'color': "info", 'id': 'p4' },
                { 'name': 'Plan2', 'quality': "Fast", 'Speed': "2", 'Duration': "4", 'Charges': "250", 'color': "info", 'id': 'p5' },
                { 'name': 'Plan3', 'quality': "Fast", 'Speed': "2", 'Duration': "24", 'Charges': "500", 'color': "info", 'id': 'p6' }
            ]
        },
        {
            type: '2',
            details: [
                { 'name': 'Plan5', 'quality': "Normal", 'Speed': "1", 'Duration': "24", 'Charges': "0", 'color': "info", 'id': 'p8' }
            ]
        },
        {
            type: '3',
            details: [
                 { 'name': 'Plan6', 'quality': "Fast", 'Speed': "2", 'Duration': "24", 'Charges': "0", 'color': "info", 'id': 'p9' }
            ]
        },
        {
            type: '4',
            details: [
                { 'name': 'Plan1', 'quality': "Fast", 'Speed': "2", 'Duration': "2", 'Charges': "150", 'color': "info", 'id': 'p10' },
                { 'name': 'Plan2', 'quality': "Fast", 'Speed': "2", 'Duration': "4", 'Charges': "250", 'color': "info", 'id': 'p11' },
                { 'name': 'Plan3', 'quality': "Fast", 'Speed': "2", 'Duration': "24", 'Charges': "500", 'color': "info", 'id': 'p12' }
            ]
        }

    ];

var submitObj = {};
if (_loginUser.clientAuthType == 8) {
	var _idsSubmit = function(planType) {
		if($('[name=radioplan]:checked').length == 0) return;
		
		var planId = $('[name=radioplan]:checked')[0].value;
		var selectedPlan = null;
		for(var i=0;i<dataPlan.length;i++) {
			for(var j=0;j<dataPlan[i].details.length;j++) {
				if (planId == dataPlan[i].details[j].id) {
					selectedPlan = dataPlan[i].details[j];
					break;
				}
			}
		}
		
		if(!selectedPlan)
		for(var i=0;i<dataPlan2.length;i++) {
			for(var j=0;j<dataPlan2[i].details.length;j++) {
				if (planId == dataPlan2[i].details[j].id) {
					selectedPlan = dataPlan2[i].details[j];
					break;
				}
			}
		}
			
		if(selectedPlan) {
			i2e1Api.doAjax('/Login/SubmitIDS', {
				mobile: _getMobile(),
				dataPlan: parseInt(planType),
				speed: selectedPlan.Speed,
				duration: selectedPlan.Duration,
				charges: selectedPlan.Charges,
				planid: $('[name=radioplan]:checked')[0].value,
				clientAuthType: _mapClientType(_loginUser.clientAuthType)
			}, function (response) {
				_doLogin({
					parameter: response.data.landingPage
				});
			});
		}	
	}
	
	if (_loginUser.clientAuthType == 8) {
		
		var success2 = function (response) {
                if (response.status == 1) {
                    _swapState(_i2e1Constants.errorState, response.msg);
                } else {
                    _preLogin(response.data.otpResponse.landingPage);
                }
            }
		
		
		var _connect = function () {
            _logMPEvent(_i2e1Constants.firstState, { event: _i2e1Constants.lastNameRoomSigninClicked, last_name_room_no: true });
            _reset();
            var username = _getMobile();
            var success = function (response) {
				if(response.data == 10) {
					i2e1Api.generateOTP(username, {
						clientAuthType: _mapClientType(_loginUser.clientAuthType),
						onSuccess: success2,
						onFailure: failure
					});
				} else if (response.status == 0) {
					var data = 	{ 
					options : {
						free: response.msg, 
						planType: "" + response.data
					},
					dataPlan: dataPlan, 
					dataPlan2: dataPlan2
					}		
					var html = _idsPackageParser(data);
					_swapState(_i2e1Constants.customState);
					$(".custom-state").empty().append(html)
                    
                } else if (response.status == 1){
					_handleOTPError(response.msg, null, _i2e1Constants.firstState);
					
                } else if (response.status == 2){
					_handleOTPError('You are not authorized', null, _i2e1Constants.firstState);
				}
            }

            var failure = function (response) {
                _handleOTPError(response.msg, null, _i2e1Constants.firstState);
                    
            }

            if(_processAnswers(3)) return;

            if (username) {
                
				i2e1Api.doAjax("/Login/GetPlans", {
					mobile: username,
					clientAuthType:  _mapClientType(_loginUser.clientAuthType)
				}, success, failure);
				
			} else {
                failure({msg: 'Invalid user'});
            }
        };
	}
}

	
	
	
var _idsPackageParser = eval(_compile(
"            <h3 style='margin: -1rem 0 0 0;'>Choose your internet plan</h3>                                                                                    " +
"            <div style='padding: 1rem 0;'>                                                                                  " +
"                <% if(data.options.free != 0) { for(var i=0; i < data.dataPlan2.length ; i++) {  if (data.options.planType == data.dataPlan2[i].type) { %>                                                       " +
"                    <table style='width:100%; margin-bottom: 1rem;background: #ededed;font-size: .8rem;'>                                                                                             " +
"                        <thead>                                                                                                       " +
"                            <tr>                                                                                                      " +
"                                <th></th>                                                                                             " +
"                                <th>Plan</th>                                                               " +
"                                <th>Speed</th>                                                              " +
"                                <th>Duration</th>                                                           " +
"                                <th>Charges</th>                                                            " +
"                            </tr>                                                                                                     " +
"                        </thead>                                                                                                      " +
"                		 <% for(var j=0; j < data.dataPlan2[i].details.length ; j++) {   var plan = data.dataPlan2[i].details[j];  %>              " +
"                            <tr class='info'>                                                                                         " +
"                                <td>                                                                                                  " +
"                                    <input style='width:1rem;height:1rem;' type='radio' name='radioplan' value='<%= plan.id %>'/>           										       " +
"                                </td>                                                                                                 " +
"                                <td><%= plan.name %> </td>                                                                                " +
"                                <td><%= plan.quality %> </td>                                                                             " +
"                                <td><%= plan.Duration %> Hours</td>                                                                      " +
"                                <td>INR <%= plan.Charges %> </td>                                                                 " +
"                            </tr>                                                                                                     " +
"                        <% } %>                                                                                               " +
"                    </table>                                                                                                          " +
"                                                                                                                                      " +

"                <% } } } else { for(var i=0; i < data.dataPlan.length ; i++) { if (data.options.planType == data.dataPlan[i].type) { %>                                                       " +
"                        <table style='width:100%; margin-bottom: 1rem;background: #ededed;font-size: .8rem;'>                                                                                         " +
"                            <thead>                                                                                                   " +
"                                <tr>                                                                                                  " +
"                                    <th></th>                                                                                         " +
"                                    <th>Plan</th>                                                           " +
"                                    <th>Speed</th>                                                          " +
"                                    <th>Duration</th>                                                       " +
"                                    <th>Charges</th>                                                        " +
"                                </tr>                                                                                                 " +
"                            </thead>                                                                                                  " +
"                		 <% for(var j=0; j < data.dataPlan[i].details.length ; j++) {   var plan = data.dataPlan[i].details[j];  %>              " +
"                        	<tr class='info'>                                                                                     " +
"                        	    <td>                                                                                              " +
"                        	        <input style='width:1rem;height:1rem;' type='radio' name='radioplan' value='<%= plan.id %>'  />         											     " +
"                        	    </td>                                                                                             " +
"                        		 <td><%= plan.name %> </td>                                                                                " +
"                        		 <td><%= plan.quality %> </td>                                                                             " +
"                        		 <td><%= plan.Duration %> Hours</td>                                                                      " +
"                        		 <td>INR <%= plan.Charges %> + taxes</td>                                                                 " +
"                        	</tr>                                                                                                 " +
"                        <% } %>                                                                                               " +
"                        </table>                                                                                                      " +
"                                                                                                                                      " +
"                <%} } } %>                                                                                                 " +
"                <input type='button' class='login_button primary' onclick='_idsSubmit(<%=data.options.planType%>)' value='Purchase plan' />                                           " +
"           </div> "
));


