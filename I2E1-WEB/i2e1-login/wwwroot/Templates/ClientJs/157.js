console.dir('template validator loaded');
var otpStarPlaceholder = '******';
_otpMsg = 'Enter 6 digit password';
var _i2e1TemplateUtils = _i2e1TemplateUtils || {};
_i2e1TemplateUtils.postload = function() {
	$('[for=otp]').text(_otpMsg);
	
}
var _handleOTPKeyPress = function(event, elem) {
    if (elem.value.length >= 6
        && event.which != 46 // delete
        && event.which != 8 // backspace
    ) {
        event.preventDefault();
    }
}