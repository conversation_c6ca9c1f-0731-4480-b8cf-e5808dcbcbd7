<style>
    body {
        background-image: url(https://i2e1storage.blob.core.windows.net/sawan-images/classic-background.png);
        background-color: #ffffff;
        background-size: cover;
        background-repeat: repeat;
    }

    .user-header img {
        width: 10rem;
        margin-bottom: 1rem;
        margin-top: 1rem;
    }
    .user-header {
        background-color: #f3d8a3;
		margin-left: auto;
		margin-right: auto;
		height: 11rem;
		position: absolute;
		width: inherit;
		top: 0;
		left: 0;
		right: 0;
		border-top-left-radius: inherit;
		border-bottom: 1px solid #9e191d;
    }
    .login.container{
        background-color: rgba(248, 231, 202, 0.8) !important;
        border-radius: 2rem 0;
        border: 1px solid #9e191d;
    }
	
	.login.container #progressBar {
		left: 1rem;
	}
	
	.login.container #progressBar div {
	    background-color: rgb(158, 25, 29)
	}
	
	.login.container form.form input {
	    border-radius: 1rem .1rem;
	}
	
	.login.container .first-state:not(.display-none),
	.login.container .second-state:not(.display-none) {
        margin-top: 11rem;
    }

    .login.container .login_button {
        background-color: #9e191d;
        color: #fff;
        font-size: 1rem;
        border: 1px solid #9e191d;
        width: 100%;
        font-weight: 600;
    }

    .login.container .tnc {
        font-weight: 700;
        font-size: .8rem;
    }

    .login.container .tnc > a {
        color: rgba(255, 0, 0, 0.79);
    }

    .login.container .tnc > a:hover {
        color: red;
        text-decoration: underline;
    }

    .footer span a {
        color: #2e2e2e !important;
    }
    .landing.container {
        background-color: rgba(255, 255, 255, 0.72) !important;
		border-radius: 5rem .5rem;
		margin-top: 40px;
    }
	.landing.container input[placeholder=SEARCH]{
		border-radius: 2rem 0;
	}
	.icons-container .small {
		border-radius: 2rem;
	}
    .icons-container .small img{
		border-radius: inherit;
	}
	.time-usage {
		background-color: #EFCF9C;
		color: #851116;
	}
    
</style>

<outer-header></outer-header>
<user-header>
    <img src="https://i2e1storage.blob.core.windows.net/sawan-images/classic-user.png">
</user-header>

<inner-header></inner-header>

<generate-otp>
    <input type="button" class="login_button" value="Next" onclick="_generateOTP(false)" />
    <span class="tnc">By clicking "Next" you agree to our <a target="_blank" href="/Templates/i2e1/tnc.html">Terms & Conditions</a></span>
</generate-otp>

<access-code-connect-button>
    <input type="button" class="login_button" value="Sign in" onclick="_generateOTP(false)" />
    <span class="tnc">By clicking "Next" you agree to our <a target="_blank" href="/Templates/i2e1/tnc.html">Terms & Conditions</a></span>
</access-code-connect-button>

<room-no-connect-button>
    <input type="button" class="login_button" value="Sign in" onclick="_generateOTP(false)" />
    <span class="tnc">By clicking "Next" you agree to our <a target="_blank" href="/Templates/i2e1/tnc.html">Terms & Conditions</a></span>
</room-no-connect-button>

<connect-button>
    <input type="button" class="login_button" value="Sign in" onclick="_connect(false)" />
</connect-button>