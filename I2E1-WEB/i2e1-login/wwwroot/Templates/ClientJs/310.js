console.dir('template validator loaded');
_generateOTP.failure = function (response) {
        _logMPEvent(_i2e1Constants.firstState, { event: _i2e1Constants.error, message: response.msg });
		if (response.msg == "Please contact reception for internet access")
			_handleOTPError("Please contact IT Team for internet access", 'username', _i2e1Constants.firstState);
        _handleOTPError(response.msg, 'username', _i2e1Constants.firstState);
    }