<style>
    body {
        font-family: 'GillSansMTStd-Light';
        color: #333;
        background-image: url("http://i2e1storage.blob.core.windows.net/sawan-images/mamagoto_back.jpg");
        background-size: cover;
    }
    .login.container {
        background-color: rgba(255, 255, 255, 0.81);    
    }
    
    .outer-card img {
        width: 12.5rem; 
    }
    .inner-header{
        font-family: roboto;
        text-align: center;
        color: #1d59a9;
        font-size: 1.4rem;
        margin-top: 0;
        margin-bottom: 0;
        font-weight: 400;
        line-height: 1.5rem;
    }
    .login.container .login_button {
        background-color: #1D59A9;
        color: #fff;
        padding: 8px 20px;
        border-radius: 4px;
        font-size: 16px;
        border: 1px solid #1D59A9;
        width: 100%;
        font-weight: 600;
    }

    .login.container .tnc {
        font-weight: 700;
        font-size: .8rem;
    }

    .login.container .tnc > a {
        color: rgba(255, 0, 0, 0.79);
    }

    .login.container .tnc > a:hover {
        color: red;
        text-decoration: underline;
    }
    @media (max-width: 420px){     
        body{
            background-position-x: -210px;
        }
    }

</style>

<outer-header>
    <img src="http://i2e1storage.blob.core.windows.net/sawan-images/mamalogo_white.png">
</outer-header>

<inner-header>Get <img class="wifi-img" src="/images/grey_wifi.png" /><span class="rainbow" style="display:none;"></span> with i2e1</inner-header>

<generate-otp>
    <input type="button" class="login_button" value="Next" onclick="_generateOTP(false)" />
    <span class="tnc">By clicking "Next" you agree to our <a target="_blank" href="/Templates/i2e1/tnc.html">Terms & Conditions</a></span>
</generate-otp>

<access-code-connect-button>
    <input type="button" class="login_button" value="Sign in" onclick="_generateOTP(false)" />
    <span class="tnc">By clicking "Next" you agree to our <a target="_blank" href="/Templates/i2e1/tnc.html">Terms & Conditions</a></span>
</access-code-connect-button>

<room-no-connect-button>
    <input type="button" class="login_button" value="Sign in" onclick="_generateOTP(false)" />
    <span class="tnc">By clicking "Next" you agree to our <a target="_blank" href="/Templates/i2e1/tnc.html">Terms & Conditions</a></span>
</room-no-connect-button>

<connect-button>
    <input type="button" class="login_button" value="Sign in" onclick="_connect(false)" />
</connect-button>
