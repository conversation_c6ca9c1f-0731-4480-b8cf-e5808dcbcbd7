<style>
    body {
        background-image: url(https://i2e1storage.blob.core.windows.net/images/HP_header_bg.jpg);
		background-size: cover;
    }

	.login.container .inner-header {
	    text-align: left;
		margin-bottom: .5rem;
	}
	
	.login.container .inner-header img{
	        width: 7rem;
			background: #21797d;
		padding: .1rem;
	}
	
    .outer-card img {
        width: 50%;
    }
    .outer-card{
        width: 20rem;
        border-radius: 5px;
        margin-left: auto;
        margin-right: auto;
        padding: 0;
        margin-top: 4rem;
        height: 5.5rem;
    }
    .login.container{
		background-color: rgb(205, 218, 235);
		border: 1px solid #21797d;
    }

    .login.container .login_button {
        background-color: #21797d;
		color: #fff;
		border-radius: 4px;
		font-size: 1rem;
		border: 1px solid #21797d;
		width: 100%;
		font-weight: 600;
    }

    .login.container .tnc {
        font-weight: 700;
        font-size: .8rem;
    }

    .login.container .tnc > a {
        color: rgba(255, 0, 0, 0.79);
    }

    .login.container .tnc > a:hover {
        color: red;
        text-decoration: underline;
    }

	.small_button {
	    background: #20777c;
    color: white;
	}
	
    .footer span a {
        color: #2e2e2e !important;
        font-family: 'Open Sans', sans-serif !important;
        font-weight: 300;
        font-size: small;
    }
    .landing.container{
        background-color: rgba(255, 255, 255, 0.72) !important;
    }
    
    @media (max-width: 420px){
     
        body{
            background-position: -20.6rem;
        }
    }
    
    
</style>

<outer-header>
    <img src="https://i2e1storage.blob.core.windows.net/images/header_logo.png">
</outer-header>

<inner-header>
	<img src="https://i2e1storage.blob.core.windows.net/images/img-easyrewardz-logo.png">
</inner-header>

<generate-otp>
    <input type="button" class="login_button" value="Next" onclick="_generateOTP(false)" />
    <span class="tnc">By clicking "Next" you agree to our <a target="_blank" href="/Templates/i2e1/tnc.html">Terms & Conditions</a></span>
</generate-otp>

<access-code-connect-button>
    <input type="button" class="login_button" value="Sign in" onclick="_generateOTP(false)" />
    <span class="tnc">By clicking "Next" you agree to our <a target="_blank" href="/Templates/i2e1/tnc.html">Terms & Conditions</a></span>
</access-code-connect-button>

<room-no-connect-button>
    <input type="button" class="login_button" value="Sign in" onclick="_generateOTP(false)" />
    <span class="tnc">By clicking "Next" you agree to our <a target="_blank" href="/Templates/i2e1/tnc.html">Terms & Conditions</a></span>
</room-no-connect-button>

<connect-button>
    <input type="button" class="login_button" value="Sign in" onclick="_connect(false)" />
</connect-button>