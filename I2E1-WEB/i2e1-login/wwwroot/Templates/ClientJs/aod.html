<style>
    body{
		background-image: url("http://i2e1storage.blob.core.windows.net/images/aod2.jpg");
        font-family : 'GillSansMTStd-Light';
        color: #333;
    }
    .outer-card img {
        
        width: 11.5rem;
    }
	
	.login.container {
		background-color: #caa776;
	}
	
    .login.container .inner-header {
        text-align: left;
    }
    .login.container .inner-header img {
        height: 3.125rem;
    }

    .login.container .login_button {
        background-color: #794604;
        color: #fff;
        padding: .5rem 1.25rem;
        border-radius: 4px;
        font-size: 1rem;
        border: 1px solid #794604;
        width: 100%;
        font-weight: 600;
    }
    .login.container .tnc{
        font-weight: 700;
        font-size: .8rem;
    }
    .login.container .tnc>a {
        color: rgba(255, 0, 0, 0.79);
    }
    .login.container .tnc>a:hover {
        color: red;
        text-decoration: underline;
    }

</style>

<outer-header>
 <img src="http://i2e1storage.blob.core.windows.net/images/aod.jpg" />
</outer-header>

<inner-header>
</inner-header>
<generate-otp>

    <input type="button" class="login_button" value="Next" onclick="_generateOTP(false)" />
    <span class="tnc">By clicking "Next" you agree to our <a target="_blank" href="/Templates/i2e1/tnc.html">Terms & Conditions</a></span>
</generate-otp>

<access-code-connect-button>
    <input type="button" class="login_button" value="Sign in" onclick="_generateOTP(false)" />
    <span class="tnc">By clicking "Next" you agree to our <a target="_blank" href="/Templates/i2e1/tnc.html">Terms & Conditions</a></span>
</access-code-connect-button>

<room-no-connect-button>
    <input type="button" class="login_button" value="Sign in" onclick="_generateOTP(false)" />
    <span class="tnc">By clicking "Next" you agree to our <a target="_blank" href="/Templates/i2e1/tnc.html">Terms & Conditions</a></span>
</room-no-connect-button>

<connect-button>
    <input type="button" class="login_button" value="Sign in" onclick="_connect(false)" />
</connect-button>
