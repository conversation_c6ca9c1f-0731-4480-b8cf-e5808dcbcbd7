<style>
    body {
        background-image: url("https://i2e1storage.blob.core.windows.net/sawan-images/dhaba-back.jpg");
        background-repeat: no-repeat;
        background-size: cover;
        background-color: ffffff;
        
    }

    .login.container {
        background-color: rgba(255, 255, 255, 0.72) !important; 
    }

    .outer-card img {
        width: 50%;
        margin-top: 5%;
    }
    .outer-card{
        background-color: #ffffff;
        width: 20rem;
        border-radius: 5px;
        margin-left: auto;
        margin-right: auto;
        padding: 0;
        margin-top: 3rem;
    }
    .login.container{
        margin-top: 2rem;
    }

    .login.container .inner-header {
        text-align: left;
    }

    .login.container .inner-header img {
        height: 50px;
    }

    .login.container .login_button {
        background-color: #1D59A9;
        color: #fff;
        padding: .5rem 1.25rem;
        border-radius: 4px;
        font-size: 1rem;
        border: 1px solid #1D59A9;
        width: 100%;
        font-weight: 600;
        margin-top: ;
    }

    .login.container .tnc {
        font-weight: 700;
        font-size: .8rem;
    }

    .login.container .tnc > a {
        color: rgba(255, 0, 0, 0.79);
    }

    .login.container .tnc > a:hover {
        color: red;
        text-decoration: underline;
    }

    .footer span a {
        color: #2e2e2e !important;
        font-family: 'Open Sans', sans-serif !important;
        font-weight: 300;
        font-size: small;
    }
    .landing.container{
        background-color: rgba(255, 255, 255, 0.72) !important;
    }
    
    @media (max-width: 420px){
     
        body{
            
            background-position-x: -31.5rem !important;
        }
    }
    
    
</style>

<outer-header>
    <img src="https://i2e1storage.blob.core.windows.net/sawan-images/dhaba-logo.jpg">
</outer-header>

<inner-header></inner-header>

<generate-otp>
    <input type="button" class="login_button" value="Next" onclick="_generateOTP(false)" />
    <span class="tnc">By clicking "Next" you agree to our <a target="_blank" href="/Templates/i2e1/tnc.html">Terms & Conditions</a></span>
</generate-otp>

<access-code-connect-button>
    <input type="button" class="login_button" value="Sign in" onclick="_generateOTP(false)" />
    <span class="tnc">By clicking "Next" you agree to our <a target="_blank" href="/Templates/i2e1/tnc.html">Terms & Conditions</a></span>
</access-code-connect-button>

<room-no-connect-button>
    <input type="button" class="login_button" value="Sign in" onclick="_generateOTP(false)" />
    <span class="tnc">By clicking "Next" you agree to our <a target="_blank" href="/Templates/i2e1/tnc.html">Terms & Conditions</a></span>
</room-no-connect-button>

<connect-button>
    <input type="button" class="login_button" value="Sign in" onclick="_connect(false)" />
</connect-button>