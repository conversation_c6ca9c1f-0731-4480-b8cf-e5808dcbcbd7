console.dir('template validator loaded');

var _i2e1TemplateUtils = _i2e1TemplateUtils || {};

var _validateUsername = function () {
    var email = $('#username').val();
    if (/^\w+([\.-]?\w+)*@\w+([\.-]?\w+)*(\.\w{2,3})+$/.test(email.trim())) {
        return (true)
    }
    _handleOTPError('Invalid Email Id', 'username', _i2e1Constants.firstState);
    return false;
}

_i2e1TemplateUtils.postload = function () {
    $('[for=username]').text('Enter Email Id');
}
