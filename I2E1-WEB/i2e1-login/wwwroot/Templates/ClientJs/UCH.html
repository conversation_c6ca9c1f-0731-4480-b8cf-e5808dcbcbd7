<style>


    body{background-image: url("https://i2e1storage.blob.core.windows.net/images/UCHbackground.jpg ");
     filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#ffffff', endColorstr='#d2d925', GradientType=1 );
     font-family : Roboto !important;
     background-repeat: no-repeat;
     background-position: center;
     background-attachment: fixed;
     background-size: cover;
     color: #1a3840;
    }
    .outer-card img {
        width:12rem;
    }

    .landing.container,.login.container{
        background-color: #f5b908;
    }

    .errorSpan {
        color: #1a3840 !important;
        border-color: #1a3840 !important;
    }

    .login.container .inline-error {
        color: #1a3840 !important;
        margin-bottom: 0rem;
    }

    .login.container .input-error {
        color: #1a3840 !important;
        border: 2px solid #1a3840 !important;
    }

    .login.container .login_button {
        background-color: #1a3840;
        border: 0px solid rgba(0, 0, 0, 0.24);
        color: #fff;
        padding: .7rem 1.25rem;
        border-radius: 0px;
        font-size: 1rem;
        width: 100%;
        font-weight: 600;
    }

    .wifilogo {
        width: 3rem;
        margin-right: 1rem;
        margin-top: -.7rem;
        margin-left: .5rem;
    }

    .login.container #progressBar div
{
        height: 100%;
        color: #1a3840;
        text-align: right;
        line-height: 22px;
        width: 0;
        max-width: inherit;
        background-color: #1a3840 !important;

}
    input#resend-otp {
		color: #fff;
		background-color: #1a3840;
		border: none;
           font-size: .5rem !important;

   .button-group�{
           color: #1a3840
           font-size: .7rem;
}

	}
    .mobile-input {
        letter-spacing: 0.1px !important;
        font-size: 1rem !important;
        margin-bottom: 1.25rem !important;
        margin-top: -.25125rem;
    }


    .login.container .tnc {
        font-size: .7rem !important;
        font-weight: 500;
        color: #1a3840;
    }
    .login.container .tnc > a {
        color:#1a3840;
        font-weight: bold;
        font-family: Roboto !important;
    }
    .login.container .tnc > a:hover {
        color: #1a3840;
        text-decoration:underline;
    }
    .login.container .footer a{
        color: #1a3840;
    }

    .login.container .change_auth_link {
        font-size: 1rem;
        margin: 1rem auto 0 auto;
        display: block;
    }
    .login.container .change_auth_link a{
        color: #1a3840;
        font-weight: 700;
        font-family: Roboto !important;
    }

    @media (max-width: 400px){

        .footer {
            div#footer span a{
                color: #1a3840;
                font-weight: 700;
                font-family: Roboto !important;
            }

        }
    }
</style>

<outer-header>
    <img src="https://i2e1storage.blob.core.windows.net/images/UCHlogo.png" />
</outer-header>

<inner-header></inner-header>
<generate-otp>
    <input type="button" class="login_button" value="Next" onclick="_generateOTP(false)" />
    <span class="tnc">By clicking "Next" you agree to our <br>
<a target="_blank" href="/Templates/i2e1/tnc.html">Terms & Conditions</a></span>
</generate-otp>

<access-code-connect-button>
    <input type="button" class="login_button" value="Sign in" onclick="_generateOTP(false)" />
    <span class="tnc">By clicking "Next" you agree to our <br>
<a target="_blank" href="/Templates/i2e1/tnc.html">Terms & Conditions</a></span>
</access-code-connect-button>

<room-no-connect-button>
    <input type="button" class="login_button" value="Sign in" onclick="_generateOTP(false)" />
    <span class="tnc">By clicking "Next" you agree to our <br>
<a target="_blank" href="/Templates/i2e1/tnc.html">Terms & Conditions</a></span>
</room-no-connect-button>

<connect-button>
    <input type="button" class="login_button" value="Sign in" onclick="_connect(false)" />
</connect-button>
