<style>
    body{
        background-image: url("https://portalvhdssj0j2s17m9gl9.blob.core.windows.net/sawan/barback.png");
        font-family : Roboto !important;
    }
    .login.container {
        background-color: #ffffff; 
        border: 3px solid #ee3120; 
    }
    .outer-card img {
        height: 9.375rem;
        width:15.625rem;
    }

    .login_button {
        background-color: #1D59A9;
        color: #fff;
        padding: .5rem 1.25rem;
        border-radius: 4px;
        font-size: 1rem;
        border: 1px solid #1D59A9;
        width: 100%;
        font-weight: 600;
    }

    .login.container .tnc{
        font-weight: 700;
        font-size: .8rem;
    }
    .login.container .tnc>a {
        color: rgba(255, 0, 0, 0.79);
    }
    .login.container .tnc>a:hover {
        color: red;
        text-decoration: underline;
    }

</style>

<outer-header>
    <img src="https://portalvhdssj0j2s17m9gl9.blob.core.windows.net/sawan/logotext2.png">
</outer-header>

<inner-header></inner-header>

<generate-otp>
    <input type="button" class="login_button" value="Next" onclick="_generateOTP(false)" />
    <span class="tnc">By clicking "Next" you agree to our <a target="_blank" href="/Templates/i2e1/tnc.html">Terms & Conditions</a></span>
</generate-otp>

<access-code-connect-button>
    <input type="button" class="login_button" value="Sign in" onclick="_generateOTP(false)" />
    <span class="tnc">By clicking "Next" you agree to our <a target="_blank" href="/Templates/i2e1/tnc.html">Terms & Conditions</a></span>
</access-code-connect-button>

<room-no-connect-button>
    <input type="button" class="login_button" value="Sign in" onclick="_generateOTP(false)" />
    <span class="tnc">By clicking "Next" you agree to our <a target="_blank" href="/Templates/i2e1/tnc.html">Terms & Conditions</a></span>
</room-no-connect-button>

<connect-button>
    <input type="button" class="login_button" value="Sign in" onclick="_connect(false)" />
</connect-button>
