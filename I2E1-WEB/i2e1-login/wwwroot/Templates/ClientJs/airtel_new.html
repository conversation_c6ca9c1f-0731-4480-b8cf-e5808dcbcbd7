<style>
     body{
        font-family : Robot<PERSON> !important;
        background-repeat: no-repeat;
        background-position: center;
        background-attachment: fixed;
        background-size: cover;
    }
    .outer-card img {
        width: 10rem;
		border: .0125rem solid white;
    }

    .login.container form .question-area .question{
	  text-align:left;
	}

    .errorSpan {
        border-color: #CD7327 !important;
    }
	
    .login.container .inline-error {
        margin-bottom: 0rem;
    }

    .login.container .input-error {
        border: 2px solid #CD7327 !important;
    }

    .login.container .login_button {
        background-color: #eb232b;
        border: 0px solid rgba(0, 0, 0, 0.24);
        color: #fff;
        padding: .7rem 1.25rem;
        border-radius: 0px;
        font-size: 1rem;
        width: 100%;
        font-weight: 600;
    }
	
	.login.container .inner-header img {
		width: 6rem;
	}
	
	.login.container .inner-header {
		margin-bottom: .6rem;
		text-align: left;
	}
	
	.login.container form #back {
		box-shadow: 1px 1px 2px rgba(0, 0, 0, 0.4);
	}
    .wifilogo {
        width: 3rem;
        margin-right: 1rem;
        margin-top: -.7rem;
        margin-left: .5rem;
    }
	
    .login.container #progressBar div
	{
        height: 100%;
        color: #CD7327;
        text-align: right;
        line-height: 22px;
        width: 0;
        max-width: inherit;
        background-color: #b72538 !important;

	}
    input#resend-otp {
        font-size: .5rem !important;

   .button-group {
        font-size: .7rem;
	}
	
    div#show_username {
        color: #fff
	}
	
    .mobile-input {
        letter-spacing: 0.1px !important;
        font-size: 1rem !important;
        margin-bottom: 1.25rem !important;
        margin-top: -.25125rem;
    }


    .login.container .tnc {
        font-size: .7rem !important;
        font-weight: 500;
    }
    .login.container .tnc > a {
        font-weight: bold;
        font-family: Roboto !important;
    }
    .login.container .tnc > a:hover {

        text-decoration:underline;
    }

    .login.container .change_auth_link {
        font-size: 1rem;
        margin: 1rem auto 0 auto;
        display: block;
    }
    .login.container .change_auth_link a{
        font-weight: 700;
        font-family: Roboto !important;
    }

    @media (max-width: 400px){
        
        .footer {
            div#footer span a {
                font-weight: 700;
                font-family: Roboto !important;            
            }
    
        }
    }
</style>


<outer-header>
	<img src="https://i2e1storage.blob.core.windows.net/images/telangana.jpg">
</outer-header>

<inner-header>
	<img class="header-1" src="https://portalvhdssj0j2s17m9gl9.blob.core.windows.net/images/airtel_logo.png">
</inner-header>
<generate-otp>
    <input type="button" class="login_button" value="Next" onclick="_generateOTP(false)" />
    <span class="tnc">By clicking "Next" you agree to our <br>
<a target="_blank" href="/Templates/i2e1/tnc.html">Terms & Conditions</a></span>
</generate-otp>

<access-code-connect-button>
    <input type="button" class="login_button" value="Sign in" onclick="_generateOTP(false)" />
    <span class="tnc">By clicking "Next" you agree to our <br>
<a target="_blank" href="/Templates/i2e1/tnc.html">Terms & Conditions</a></span>
</access-code-connect-button>

<room-no-connect-button>
    <input type="button" class="login_button" value="Sign in" onclick="_generateOTP(false)" />
    <span class="tnc">By clicking "Next" you agree to our <br>
<a target="_blank" href="/Templates/i2e1/tnc.html">Terms & Conditions</a></span>
</room-no-connect-button>

<connect-button>
    <input type="button" class="login_button" value="Sign in" onclick="_connect(false)" />
</connect-button>

<landing-page-message>
	<p style="font-weight:600;">{{title}}</p>
</landing-page-message>

<landing-page-tiles-container>
    <a ng-click="navigate('http://www.airtel.in/myairtelapp/')"><img src="images/lp_icons/myairtel.png" class="small" /></a>
	<a ng-click="navigate('http://www.airtel.in/wynkmovies/')"><img src="images/lp_icons/airtelmovies.png" class="small" /></a>
	<a ng-click="navigate('https://get.hike.in/')"><img src="images/lp_icons/hike.png" class="small" /></a>
	<a ng-click="navigate('http://games.wynk.in/')"><img src="images/lp_icons/airtelgames.png" class="small" /></a>
	<a ng-click="navigate('http://www.airtel.in/wynkmusic/')"><img src="images/lp_icons/wynk.png" class="small" /></a>
	<a ng-click="navigate('http://www.facebook.com')"><img src="images/lp_icons/fb.png" class="small" /></a>
	<a ng-click="navigate('http://tracking.payoom.com/aff_c?offer_id=62&aff_id=23633')"><img src="images/lp_icons/jabong.png" class="small" /></a>
	<a ng-click="navigate('http://www.amazon.in/ref=as_li_ss_tl?_encoding=UTF8&camp=3626&creative=24822&linkCode=ur2&tag=i2e1-21')"><img src="images/lp_icons/amazon.png" class="small" /></a>
	<a ng-click="navigate('http://www.youtube.com')"><img src="images/lp_icons/youtube.png" class="small" /></a>
</landing-page-tiles-container>

