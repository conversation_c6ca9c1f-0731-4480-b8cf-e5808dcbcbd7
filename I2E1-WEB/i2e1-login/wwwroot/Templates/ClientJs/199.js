console.dir('template validator loaded');

var _i2e1TemplateUtils = _i2e1TemplateUtils || {};
_i2e1TemplateUtils.postload = function () {
	 if (_loginUser.templateid.length == 2 && _loginUser.templateid[1] == 15) {
		 _questionToggle = function () {
          $('.question-area.type-3').toggleClass('display-none');
          if ($('.question-area.type-3').hasClass('display-none'))
              $('.uncube-question a').text('Have booking email?');
          else $('.uncube-question a').text('Don\'t have booking email?');
      }
		
	  
		$('.tnc-area').after('<div class="change_auth_link1 uncube-question"> <a onclick="_questionToggle()"> Don\'t have booking email? </a> </div><div class="seperator">  <span>&nbsp;&nbsp;</span> </div>');

	 }
}
