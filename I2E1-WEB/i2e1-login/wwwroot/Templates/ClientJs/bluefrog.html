<style>
    body {
        background-color: white;
        padding: 0;
    }

    .swap-promotion, .footer, .open, .tnc-area {
        display: none !important;
    }
    .top-container {
        margin: 0 !important;
    }

    .login.container {
        box-shadow: 0px 0px 0px rgba(0, 0, 0, 0.4) !important;
        position: absolute;
        left: 0;
        right: 0;
        text-align: center;
        text-align: -webkit-center;
    }

    .login.container .outer-card1 > img {
        position: absolute;
        right: 1rem;
        top: 1rem;
    }

    .outer-card1 {
    }

    .outer-card1 img {
        width: 5rem;
        display: block;
    }

    .outer-card1 .logo {
        position: absolute;
        right: 1rem;
        top: -1rem;
    }

    .line_bg {
        background: rgb(193,95,152);
        background: linear-gradient(90deg, rgba(193,95,152,1) 0%, rgba(149,125,175,1) 24%, rgba(35,205,238,1) 56%, rgba(25,171,198,1) 99%);
        /* background: url(../images/line.jpg) no-repeat; */
        position: absolute;
        width: 100%;
        display: block;
        height: 8px;
        top: 30%;
    }

</style>

<outer-header>
    <span class="line_bg"></span>
    <div class="logo">
        <a href="#">
            <img src="https://i2e1storage.blob.core.windows.net/images/vman.jpg">
            <img src="https://i2e1storage.blob.core.windows.net/images/slogan.jpg">
        </a>
    </div>
    
</outer-header>

<inner-header></inner-header>
<generate-otp>
    <input type="button" class="login_button" value="Next" onclick="_generateOTP(false)" />
    <span class="tnc">
        By clicking "Next" you agree to our <br>
        <a target="_blank" href="/Templates/i2e1/tnc.html">Terms & Conditions</a>
    </span>
</generate-otp>


<connect-button>
    <input type="button" class="login_button" value="Sign in" onclick="_connect(false)" />
</connect-button>
