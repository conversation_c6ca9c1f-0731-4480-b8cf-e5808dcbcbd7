<style>
    body{
       background-image: url("https://i2e1storage.blob.core.windows.net/images/ccd_background.jpg");
        filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#ffffff', endColorstr='#d2d925', GradientType=1 );
        font-family : Roboto !important;
        background-repeat: no-repeat;
        background-position: center;
        background-attachment: fixed;
        background-size: cover;
        color: #fff;
    }
    .outer-card img {
        width: 10rem;
		border: .0125rem solid white;
    }

    .landing.container,.login.container{
        background-color: rgba(200, 116, 20, .7);
    }
	.landing.container {
	     padding: 10px 15px 20px 15px;
	}
	.login.container form .question-area .question{
	  text-align:left;
	  color:white;
	}


    .errorSpan {
        color: #fff !important;
        border-color: #CD7327 !important;
    }

	.login.container {
		color: white;
	}
    .login.container .inline-error {
        color: #fff !important;
        margin-bottom: 0rem;
    }

    .login.container .input-error {
        color: #fff !important;
        border: 2px solid #CD7327 !important;
    }

    .login.container .login_button {
        background-color: #fff;
        border: 0px solid rgba(0, 0, 0, 0.24);
        color: #000;
        padding: .7rem 1.25rem;
        border-radius: 0px;
        font-size: 1rem;
        width: 100%;
        font-weight: 600;
    }
	
	.login.container .inner-header img {
		width: 6rem;
		background: #eacab5;
		padding: 0 .4rem;
	}
	.login.container .inner-header {
		text-align: left;
	}
	
	.login.container form #back {
		background: #cd7327;
		box-shadow: 1px 1px 2px rgba(0, 0, 0, 0.4);
	}
    .wifilogo {
        width: 3rem;
        margin-right: 1rem;
        margin-top: -.7rem;
        margin-left: .5rem;
    }
	
    .login.container #progressBar div
{
        height: 100%;
        color: #CD7327;
        text-align: right;
        line-height: 22px;
        width: 0;
        max-width: inherit;
        background-color: #b72538 !important;

}
    input#resend-otp {
		color: #fff;
		background-color: #CD7327;
		border: none;
           font-size: .5rem !important;

   .button-groupÿ{
           color: #fff;
           font-size: .7rem;

 }
   div#show_username {
           color: #fff
}
	}
    .mobile-input {
        letter-spacing: 0.1px !important;
        font-size: 1rem !important;
        margin-bottom: 1.25rem !important;
        margin-top: -.25125rem;
    }


    .login.container .tnc {
        font-size: .7rem !important;
        font-weight: 500;
        color: #fff;
    }
    .login.container .tnc > a {
        color:#fff;
        font-weight: bold;
        font-family: Roboto !important;
    }
    .login.container .tnc > a:hover {
        color: #fff;
        text-decoration:underline;
    }
    .login.container .footer a{
        color: #fff;
    }

    .login.container .change_auth_link {
        font-size: 1rem;
        margin: 1rem auto 0 auto;
        display: block;
    }
    .login.container .change_auth_link a{
        color: #fff;
        font-weight: 700;
        font-family: Roboto !important;
    }

    @media (max-width: 400px){
        
        .footer {
            div#footer span a{
                color: #fff;
                font-weight: 700;
                font-family: Roboto !important;            
            }
    
        }
    }
</style>

<outer-header>
    <img src="https://i2e1storage.blob.core.windows.net/images/ccd_logo.jpg" />
</outer-header>

<inner-header>
	<img src="https://i2e1storage.blob.core.windows.net/images/treya_logo.png" />
</inner-header>
<generate-otp>
    <input type="button" class="login_button" value="Next" onclick="_generateOTP(false)" />
    <span class="tnc">By clicking "Next" you agree to our <br>
<a target="_blank" href="/Templates/i2e1/tnc.html">Terms & Conditions</a></span>
</generate-otp>

<access-code-connect-button>
    <input type="button" class="login_button" value="Sign in" onclick="_generateOTP(false)" />
    <span class="tnc">By clicking "Next" you agree to our <br>
<a target="_blank" href="/Templates/i2e1/tnc.html">Terms & Conditions</a></span>
</access-code-connect-button>

<room-no-connect-button>
    <input type="button" class="login_button" value="Sign in" onclick="_generateOTP(false)" />
    <span class="tnc">By clicking "Next" you agree to our <br>
<a target="_blank" href="/Templates/i2e1/tnc.html">Terms & Conditions</a></span>
</room-no-connect-button>

<connect-button>
    <input type="button" class="login_button" value="Sign in" onclick="_connect(false)" />
</connect-button>

<landing-page-tiles-container>
    <a target="_blank" href="https://youtube.com"><img width="100%" src="http://i2e1storage.blob.core.windows.net/images/youtube-logo-png-20.png"></a>
</landing-page-tiles-container>

