<style>
body{
   background-image: url("https://i2e1storage.blob.core.windows.net/images/biergartenback.jpg");
    background-repeat: no-repeat;
    background-position: right;
    background-attachment: fixed;
    background-size: cover;
    color: #000;
}

    body{

    }
    .outer-card img {
        width:10rem;
    }

    .landing.container,.login.container{
        background-color: #f4f4f4;
    }

    .errorSpan {
        color: #a35109 !important;
        border-color: #AB2930 !important;
    }

    .login.container .inline-error {
        color: #a35109 !important;
        margin-bottom: 0rem;
    }

    .login.container .input-error {
        color: #a35109 !important;
        border: 2px solid #AB2930 !important;
    }

    .login.container .login_button {
        background-color: #000000;
        border: 0px solid rgba(0, 0, 0, 0.24);
        color: #fff;
        padding: .7rem 1.25rem;
        border-radius: 0px;
        font-size: 1rem;
        width: 100%;
        font-weight: 600;
    }

    .wifilogo {
        width: 3rem;
        margin-right: 1rem;
        margin-top: -.7rem;
        margin-left: .5rem;
    }

    .login.container #progressBar div
{
        height: 100%;
        color: #fff;
        text-align: right;
        line-height: 22px;
        width: 0;
        max-width: inherit;
        background-color: #a35109 !important;

}
    input#resend-otp {
		color: #fff;
		background-color: #AB2930;
		border: none;
           font-size: .5rem !important;

   .button-group�{
           color: #a35109
           font-size: .7rem;
}

	}
    .mobile-input {
        letter-spacing: 0.1px !important;
        font-size: 1rem !important;
        margin-bottom: 1.25rem !important;
        margin-top: -.25125rem;
    }


    .login.container .tnc {
        font-size: .7rem !important;
        font-weight: 500;
        color: #a35109;
    }
    .login.container .tnc > a {
        color:#AB2930;
        font-weight: bold;
        font-family: Roboto !important;
    }
    .login.container .tnc > a:hover {
        color: #AB2930;
        text-decoration:underline;
    }
    .login.container .footer a{
        color: #a35109;
    }

    .login.container .change_auth_link {
        font-size: 1rem;
        margin: 1rem auto 0 auto;
        display: block;
    }
    .login.container .change_auth_link a{
        color: #a35109;
        font-weight: 700;
        font-family: Roboto !important;
    }

    @media (max-width: 400px){

        .footer {
            div#footer span a{
                color: #a35109;
                font-weight: 700;
                font-family: Roboto !important;
            }

        }
    }
</style>

<outer-header>
    <img src="https://i2e1storage.blob.core.windows.net/images/biergarten.jpg" />
</outer-header>

<inner-header></inner-header>
