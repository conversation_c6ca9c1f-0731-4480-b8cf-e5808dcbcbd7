<style>
    body {
        font-family: 'GillSansMTStd-Light';
        font-size: 1.1rem;
        line-height: 1.42857143;
        color: #333;
        background-image: url("https://portalvhdssj0j2s17m9gl9.blob.core.windows.net/sawan/azaadback.gif");
        background-size: cover;
    }


    .login.container .inner-header {
        margin-bottom: 1rem;
    }

    .inner-header img {
        height: 3.125rem;
    }

    .login_button {
        background-color: #1D59A9;
        color: #fff;
        padding: .7rem 1.25rem !important;
        border-radius: 4px !important;
        font-size: 1rem;
        border: 1px solid #1D59A9;
        width: 100%;
        font-weight: 600;
    }

    .login.container .tnc {
        font-size: .8rem;
        font-weight: 700;
    }

        .login.container .tnc > a {
            color: rgba(255, 0, 0, 0.79);
        }

    .login.container .change_auth_link {
        font-size: 1rem;
        margin: 1rem auto;
        display: block;
    }

        .login.container .change_auth_link > a {
            color: #337ab7;
            text-decoration: none;
        }
</style>


<inner-header>
    Happy Republic Day
</inner-header>

<generate-otp>
    <input type="button" class="login_button" value="Next" onclick="_generateOTP(false)" />
    <span class="tnc">By clicking "Next" you agree to our <a target="_blank" href="/Templates/i2e1/tnc.html">Terms &amp; Conditions</a></span>
</generate-otp>

<access-code-connect-button>
    <input type="button" class="login_button" value="Next" onclick="_connect(false)" />
    <span class="tnc">By clicking "Next" you agree to our <a target="_blank" href="/Templates/i2e1/tnc.html">Terms &amp; Conditions</a></span>
</access-code-connect-button>

<room-no-connect-button>
    <input type="button" class="login_button" value="Next" onclick="_connect(false)" />
    <span class="tnc">By clicking "Next" you agree to our <a target="_blank" href="/Templates/i2e1/tnc.html">Terms &amp; Conditions</a></span>
</room-no-connect-button>

<connect-button>
    <input type="button" class="login_button" value="Sign In" onclick="_connect(false)">
</connect-button>
