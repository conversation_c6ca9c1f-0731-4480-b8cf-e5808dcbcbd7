<style>
    @font-face {
        font-family: 'Roboto';
        font-style: normal;
        font-weight: 400;
        src: url(https://i2e1storage.blob.core.windows.net/fonts/Roboto-Regular.woff2) format('woff2');
    }

    body {
        background-image: url("https://portalvhdssj0j2s17m9gl9.blob.core.windows.net/sawan/wood.jpg");
        font-family: Roboto !important;
        background-position-x: 25rem;
    }
    .outer-card img {
        width:12.5rem;
    }

    .landing.container, .login.container {
        background-color: rgba(0, 0, 0, 0.18);
        border-radius: 0 !important;
    }

    .errorSpan {
        color: #000 !important;
        border-color: #000 !important;
    }

    .login.container .inline-error {
        color: white !important;
        margin-bottom: 0rem;
    }

    .login.container .input-error {
        color: #000 !important;
        border: 2px solid #000 !important;
    }

    .login.container .login_button {
        background-color: #EE1225;
        border: 1px solid #EE1225;
        color: #fff;
        padding: .65rem 1.25rem;
        border-radius: .25rem;
        font-size: 1rem;
        width: 100%;
        font-weight: 600;
    }

    .wifilogo {
        width: 3rem;
        margin-right: 1rem;
        margin-top: -.7rem;
        margin-left: .5rem;
    }

    .mobile-input {
        letter-spacing: 0.1px !important;
        font-size: 15px !important;
        margin-bottom: 20px !important;
        margin-top: -5px;
    }


    .login.container .tnc {
        font-size: .7rem !important;
        font-weight: 500;
        color: white;
    }
    .login.container .tnc > a {
        color: #EE1225;
        font-weight: 700;
        font-family: roboto !important;
    }
    .login.container .footer a{
        color: #fff;
    }
</style>

<outer-header>
    <img src="http://i2e1storage.blob.core.windows.net/images/beercafe.png"/>
</outer-header>

<inner-header></inner-header>
<generate-otp>
    <input type="button" class="login_button" value="Next" onclick="_generateOTP(false)" />
    <span class="tnc">By clicking "Next" you agree to our <a target="_blank" href="/Templates/i2e1/tnc.html">Terms & Conditions</a></span>
</generate-otp>

<access-code-connect-button>
    <input type="button" class="login_button" value="Sign in" onclick="_generateOTP(false)" />
    <span class="tnc">By clicking "Next" you agree to our <a target="_blank" href="/Templates/i2e1/tnc.html">Terms & Conditions</a></span>
</access-code-connect-button>

<room-no-connect-button>
    <input type="button" class="login_button" value="Sign in" onclick="_generateOTP(false)" />
    <span class="tnc">By clicking "Next" you agree to our <a target="_blank" href="/Templates/i2e1/tnc.html">Terms & Conditions</a></span>
</room-no-connect-button>

<connect-button>
    <input type="button" class="login_button" value="Sign in" onclick="_connect(false)" />
</connect-button>

<guest-mode>
If you are a Co-worker/Foreigner! Click Here
</guest-mode>
