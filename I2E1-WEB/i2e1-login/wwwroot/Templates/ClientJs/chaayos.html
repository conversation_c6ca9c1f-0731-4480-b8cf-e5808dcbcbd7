<style>
    @font-face
    {
      font-family: 'Roboto';
      font-style: normal;
      font-weight: 400;
      src: url(https://i2e1storage.blob.core.windows.net/fonts/Roboto-Regular.woff2) format('woff2');
    }

    body{
        background-image: url("https://portalvhdssj0j2s17m9gl9.blob.core.windows.net/sawan/chaayosbackwhite.jpg");
        filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#ffffff', endColorstr='#f02f17', GradientType=1 );
        font-family : Roboto !important;
        background-repeat: no-repeat;
        background-position: center;
        background-attachment: fixed;
        background-size: cover;
        color: #333;
    }
    .outer-card img {
        width:10rem;
    }

    .landing.container,.login.container{
        background-color: rgba(255, 255, 255, 0.9);
    }

    .errorSpan {
      color: #AC4012 !important;
      border-color: #AC4012 !important;
    }

    .login.container .inline-error {
        color: #AC4012 !important;
        margin-bottom: 0rem;
    }

    .login.container .input-error {
        color: #AC4012 !important;
        border: 1px solid #AC4012 !important;
    }

    .login.container .login_button {
        background-color: #50773E;
        color: #fff;
        padding: .5rem 1.25rem;
        border-radius: 4px;
        font-size: 1rem;
        width: 100%;
        font-weight: 600;
    }

    .wifilogo {
        width: 3rem;
        margin-right: 1rem;
        margin-top: -.7rem;
        margin-left: .5rem;
    }

.login.container #progressBar div
{
        height: 100%;
        color: #50773E;
        text-align: right;
        line-height: 22px;
        width: 0;
        max-width: inherit;
        background-color: #50773E !important;

}
    input#resend-otp {
		color: #fff;
		background-color: #AC4012;
		border: none;
           font-size: .5rem !important;
}
   .button-group {
           color: #50773E
           font-size: .7rem;
}

div#show_username {
        color: #AC4012;
}

    .mobile-input {
        letter-spacing: 0.1px !important;
        font-size: 1rem !important;
        margin-bottom: 1.25rem !important;
        margin-top: -.25125rem;
    }
    
    .login.container .tnc {
        font-size: .7rem !important;
        font-weight: 500;
        color: #50773E !important;
    }
    .login.container .tnc > a {
        color: #50773E !important;
        font-weight: bold;
        font-family: Roboto !important;
    }
    .login.container .tnc > a:hover {
        color: #50773E !important;
        text-decoration:underline;
    }
    .login.container .footer a{
        color: #50773E !important;
    }

    .login.container .change_auth_link {
        font-size: 1rem;
        margin: 1rem auto 0 auto;
        display: block;
    }
    .login.container .change_auth_link a{
        color: #50773E !important;
        font-weight: 700;
        font-family: roboto !important;
    }

    @media (max-width: 400px){

        .footer {
            div#footer span a{
                color: #939598;
                font-weight: 700;
                font-family: roboto !important;
            }

        }
    }
</style>

<outer-header>
    <img src="https://portalvhdssj0j2s17m9gl9.blob.core.windows.net/sawan/chaayoslogo.png" />
</outer-header>

<inner-header></inner-header>
<generate-otp>
    <input type="button" class="login_button" value="Next" onclick="_generateOTP(false)" />
    <span class="tnc">By clicking "Next" you agree to our <a target="_blank" href="/Templates/i2e1/tnc.html">Terms & Conditions</a></span>
</generate-otp>

<access-code-connect-button>
    <input type="button" class="login_button" value="Sign in" onclick="_generateOTP(false)" />
    <span class="tnc">By clicking "Next" you agree to our <a target="_blank" href="/Templates/i2e1/tnc.html">Terms & Conditions</a></span>
</access-code-connect-button>

<room-no-connect-button>
    <input type="button" class="login_button" value="Sign in" onclick="_generateOTP(false)" />
    <span class="tnc">By clicking "Next" you agree to our <a target="_blank" href="/Templates/i2e1/tnc.html">Terms & Conditions</a></span>
</room-no-connect-button>

<connect-button>
    <input type="button" class="login_button" value="Sign in" onclick="_connect(false)" />
</connect-button>
