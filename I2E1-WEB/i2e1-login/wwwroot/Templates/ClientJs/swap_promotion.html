<script>

var _mytoggle = function(showProceed){
	if (showProceed) {
		$('.custom-page .connect-button').hide();  
		$('.custom-page .post-proceed').show();  	
		$('.custom-page .manual-otp').hide();  
		$('.custom-page .imgs').show();  		
	} else {
		$('.custom-page .connect-button').show();  
		$('.custom-page .post-proceed').hide(); 
		$('.custom-page .manual-otp').show();  
		$('.custom-page .imgs').hide();  
	}
};
var _sendLinqSMS = function(type) {
	console.log('Sending Linq Promotion sms');
	type = type || 'only_linq';
	i2e1Api.doAjax('/Login/SendLinqPromotion', {                    
		sessionid: _loginUser.sessionid,                            
		routerTempId: _loginUser.nasid,
		type: type
	}, function (response) { 
		if (type == 'only_linq'){
			$('.custom-page .bottom-line').text('SMS with download link has been sent to your phone number ' + _getMobile());
		} else if (type == 'otp') {
			$('.custom-state').empty();
			_swapState(_i2e1Constants.secondState);
		}
		_mytoggle(true);  
		console.log('Linq Promotion sms sent');    		
	});
}       

var _resendLinqSMS = function() {
	console.log('Resending Linq Promotion sms');
	
	i2e1Api.doAjax('/Login/SendLinqPromotion', {                    
		sessionid: _loginUser.sessionid,                            
		routerTempId: _loginUser.nasid,
		type: 'only_linq'		
	}, function (response) { 
		_mytoggle(true);  
		console.log('Linq Promotion sms resent');    		
	});
}

var _hidecheck = function () {
    if (_loginUser.nasid == 6582) {
        console.log('removing manual');
        $('.custom-page .manual-otp').hide();
    }
}
_hidecheck();
</script>

<div class="custom-page" style="text-align: left; position: relative;">
	<img style="width:9rem;" src="../../images/swapp/linq.png" />
	<div class="linq-div">
		<p style="margin-bottom: 0;">Auto-connect via Linq</p>
	</div>
	
	<div class="post-proceed" style="margin-top:2.5rem; display:none;">
		<div class="bottom-line" style=" padding-top: 1.2rem;font-size:.9rem;border-top:1px solid #ababab4a;">
			SMS with download link has been sent to your phone number
		</div>
		<div class="action-buttons" style="width: 100%;">
			<div style=" position: absolute; bottom: -2rem;font-size: .9rem;"><a href="#"  onclick="_swapState(_i2e1Constants.firstState, {reverse: true});$('.custom-state').empty();">Change Number</a></div>
			<div style="position: absolute; right:0; bottom: -2.5rem;font-size: .9rem;">
				<input type="button" style="margin:0;" id="resen" class="primary small_button" value="Resend" onclick="_resendLinqSMS()" />
			</div>
		</div>
	</div>
	
	<div class="connect-button">
		<p style="font-size:.8rem;margin-top: .5rem;">Download Linq &amp; get <linq style="color: #439cd6;font-size:1rem;">1GB Free</linq> at 10,000+ hotspots</p>
		<input type="button" id="proceed" class="primary login_button" value="Proceed" onclick="_sendLinqSMS()" />
	</div>
	<div class="otp-div" style="position: absolute; bottom: -9rem;left: -1rem;" >
		<div class="manual-otp">
			<p style="margin-bottom: 0;" onclick="_sendLinqSMS('otp')">Manual Login via OTP</p>
			<p style="font-size:.8rem;margin-top: .5rem;">Get your phone number verified to get 100MB Free</p>
		</div>
		<div class="imgs" style="display:none;">
			<img src="../../images/swapp/ic_local_cafe.png" />
            <img src="../../images/swapp/ic_local_airport.png" />
            <img src="../../images/swapp/ic_directions_subway.png" />
            <img src="../../images/swapp/ic_restaurant.png" />
            <img src="../../images/swapp/ic_local_bar.png" />
		</div>
	</div> 

</div>

<style>
.login.container .outer-card1 {
	position: absolute;
	top: -4.5rem;
	left: 0;
}

.login.container .outer-card1 > img {
	width: 5rem !important;
    height: unset !important;
 }

.login.container .outer-card1 .open {
	display: none;
	position: absolute;
}

.login.container .footer {
	display: none;
}

.login.container .state-transition .custom-page .imgs img {
	width: 4rem;
}
</style>


