console.dir('template validator loaded');
var _i2e1TemplateUtils = _i2e1TemplateUtils || {};

_i2e1TemplateUtils.postload = function(options){

    if (options.state == _i2e1Constants.customState) 
        init_customState();
    
}

var _plantoggle = function (plan) {
    if (plan == '1') {
        document.getElementById("accesscode").setAttribute("hidden", "true");
        document.getElementById("code").value = 'FREE';
    }
    if (plan == '2') {
        document.getElementById("code").value = '';
        document.getElementById("accesscode").removeAttribute("hidden");
        $("#code")[0].focus();
    }
    if (plan == '3') {
        _generatepaymentview();
    }
     if (plan == '4') {
        _generatenormalview();
    }
}


var _enablepayment=function(charges){
    console.log(charges);
    document.getElementById("hiddencharge").value=charges;
}


var _generatepaymentview=function(){
    document.getElementById("code").value = '';
    document.getElementById("accesscode").setAttribute("hidden", "true");
    document.getElementById("radio").style.display='none';
    document.getElementById("paidplans").style.display='block';
    document.getElementById("connect").style.display='none';
    document.getElementById("buybutton").style.display='block';
}

var _generatenormalview=function(){
    document.getElementById("code").value = '';
    document.getElementById("radio").style.display='block';
    document.getElementById("paidplans").style.display='none';
    document.getElementById("buybutton").style.display='none';
    document.getElementById("connect").style.display='block';
    document.getElementById("coupon").checked = true;
    _plantoggle(2);
}

var _makepayment = function () {
    var username = _getMobile() || _viewBag.mobile;
    var otpField = "";
    var accessCode = document.getElementById("hiddencharge").value;
        
    var success = function (response) {
        _doLogin(response.data);
    };
    
    var failure = function (response) {
        _handleOTPError(response.msg, 'code', _i2e1Constants.customState);
    }

    if(_processAnswers(3)) return;

    if (username) {
        i2e1Api.submitDataPlan(username, otpField.value, {
            accessCode: accessCode,
            name: '',
            questions: [],
            onSuccess: success,
            onFailure: failure,
        });
    } else {
        failure({msg: 'Payment Failed'});
    }
};

var init_customState = function() {
    i2e1Api.doAjax("/Login/isTraiFreeSession", {}, function(res) {
        var html = _packageParser(res.data);
        $(".custom-state").empty().append(html);
        _swapState(_i2e1Constants.customState);
        if(!res.data.traiFreeSession) _plantoggle(2);
    }, function(){
        
    });
    
}

_connect.success = function(response) {
    init_customState()
}
    
var _submitTrai = function () {
    var username = _getMobile() || _viewBag.mobile;
    var otpField = "";
    var accessCode = document.getElementById("code").value;
        
    var success = function (response) {
        _preLogin(response.data.landingPage);
    };
    
    var failure = function (response) {
        _handleOTPError(response.msg, 'code', _i2e1Constants.customState);
    }

    if(_processAnswers(3)) return;

    if (username) {
        i2e1Api.submitOTP(username, otpField.value, {
            accessCode: accessCode,
            name: '',
            questions: [],
            onSuccess: success,
            onFailure: failure,
        });
    } else {
        failure({msg: 'Invalid user'});
    }
};

var _packageParser = eval(_compile(
"<input type='hidden' id='hiddencharge'>"+
"<div id='paidplans' style='text-align:left;padding: 0 1rem;display:none'>"+
"<div><a href='#' onclick='_plantoggle(4)'>Back...</a></div>"+
"<div ><input id='1' type='radio' name='internet'  value='1' onclick='_enablepayment(1)' style='width: 1rem;height:1rem' type='radio' name='internet' required>"+
"            <label for='free'>&#x20b9; 1 for 5 min</label>  </div> "+
"<div><input id='2' type='radio' name='internet' value='2' onclick='_enablepayment(2)' style='width: 1rem;height:1rem' type='radio' name='internet' required>"+
"            <label for='free'>&#x20b9; 2 for 10 min</label>  </div> "+
"<div><input id='5' type='radio' name='internet' value='5' onclick='_enablepayment(5)' style='width: 1rem;height:1rem' type='radio' name='internet' required>"+
"            <label for='free'>&#x20b9; 5 for 30 min</label>  </div> "+
"<div><input id='100' type='radio' name='internet' value='100' onclick='_enablepayment(100)' style='width: 1rem;height:1rem' type='radio' name='internet' required>"+
"            <label for='free'>&#x20b9; 100 for 1month/10GB(500 MB/day max)</label>  </div>"+
"  <div cage='connect-button'>" +
"           <input type='button' id='buybutton' class='primary login_button' value='Buy Online' onclick='_makepayment()'/>" +
"         </div>"+
"</div><div id='radio' style='text-align:left;padding: 0 1rem;'>"+
"       <% if(data.traiFreeSession == 1) { %>" +
"            <div ><input style='width: 1rem;height:1rem' type='radio' name='internet' id='free' value='1' onclick='_plantoggle(1)' checked>" +
"            <label for='free'>Free Internet (5 Minutes)</label>  </div>" +

"            <% } %>" +
"            <div  ><input  id='2' width type='radio' name='internet' value='2' onclick='_plantoggle(3)' style='width: 1rem;height:1rem' type='radio' name='internet' >"+
"            <label for='free'>Buy Online</label>  </div> "+
"            <div> <input style='width: 1rem;height:1rem' type='radio' name='internet' id='coupon' value='2' onclick='_plantoggle(2)' checked>" +
"            <label for='coupon'>Use Coupons</label></div>" +
"            <div class='material-input' id='accesscode' style='margin-top:-1rem' hidden>" +
"            <p style='text-align:left;color: #757575'>Buy Access Code From Shopkeeper</p>" +
"            <div class='group'>" +
"            <input type='text' id='code' value='FREE' required='required' pattern='.*\S.*' />" +
"            <label for='code'>Enter access code here</label>" +
"        </div></div>" +
"        <div cage='connect-button'>" +
"           <input type='button' id='connect' class='primary login_button' value='Connect' onclick='' />" +
"         </div></div>" 
));