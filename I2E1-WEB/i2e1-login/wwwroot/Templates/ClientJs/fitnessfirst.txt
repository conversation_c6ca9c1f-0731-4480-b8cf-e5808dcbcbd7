<style>
    html {
        font-size: 90%;
    }

    body {
        background-color: #d31533;
        font-family: 'GillSansMTStd-Light';
    }

    .login.container {
        background-color: #fff;
    }

    

    .login.container .inner-header {
        margin-bottom: 1rem;
        max-height: 6rem;
        text-align: left;
        position: relative;
    }

        .login.container .inner-header > h5 {
            margin-top: 1.4rem;
            position: relative;
            top: -1rem;
            text-align: left;
            font-size: 1rem;
            font-weight: bold;
        }

        .login.container .inner-header > img {
            height: 3.5rem;
            /* float: left; */
            top: -1rem;
            position: relative;
        }



    .followUs, .followUs a, div#footer span a {
        color: #fff;
    }

    .login_button {
        background-color: #1D59A9;
        color: #fff;
        padding: .8rem 2rem;
        border-radius: .4rem;
        font-size: 1.6rem;
        border: 1px solid #1D59A9;
        width: 100%;
        font-weight: 600;
    }

    .login.container .tnc {
        font-size: .8rem;
        font-weight: bold;
    }
</style>
<outer-header>
    <img src="https://portalvhdsmkkpzyw9y198d.blob.core.windows.net/splashimages/fitness.png">
</outer-header>

<generate-otp>
    <input type="button" class="login_button" value="Next" onclick="_generateOTP(false)" />
    <span class="tnc">By clicking the button you agree to i2e1's <a target="_blank" href="/Templates/i2e1/tnc.html">Terms & Conditions</a></span>
</generate-otp>

<access-code-connect-button>
    <input type="button" class="login_button" value="Sign in" onclick="_generateOTP(false)" />
    <span class="tnc">By clicking the button you agree to i2e1's <a target="_blank" href="/Templates/i2e1/tnc.html">Terms & Conditions</a></span>
</access-code-connect-button>

<room-no-connect-button>
    <input type="button" class="login_button" value="Sign in" onclick="_generateOTP(false)" />
    <span class="tnc">By clicking the button you agree to i2e1's <a target="_blank" href="/Templates/i2e1/tnc.html">Terms & Conditions</a></span>
</room-no-connect-button>

<connect-button>
    <input type="button" class="login_button" value="Sign in" onclick="_connect(false)" />
</connect-button>