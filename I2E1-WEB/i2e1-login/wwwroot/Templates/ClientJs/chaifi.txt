<style>
    body{
        font-family : 'GillSansMTStd-Light';
        color: #333;
    }
    .outer-card img {
        height: 5.7rem;
        width: 20.5rem;
    }
    .login.container .inner-header {
        text-align: left;
    }
    .login.container .inner-header img {
        height: 3.125rem;
    }

    .login.container .login_button {
        background-color: #1D59A9;
        color: #fff;
        padding: .5rem 1.25rem;
        border-radius: 4px;
        font-size: 1rem;
        border: 1px solid #1D59A9;
        width: 100%;
        font-weight: 600;
    }
    .login.container .tnc{
        font-weight: 700;
        font-size: .8rem;
    }
    .login.container .tnc>a {
        color: rgba(255, 0, 0, 0.79);
    }
    .login.container .tnc>a:hover {
        color: red;
        text-decoration: underline;
    }

</style>

<outer-header>
    <img src="http://portalvhdssj0j2s17m9gl9.blob.core.windows.net/images/Chai-Fi_Logo.png" />
</outer-header>

<inner-header>
    <img src="https://portalvhdsmkkpzyw9y198d.blob.core.windows.net/splashimages/801/96336937-941a-477a-8afd-a984ac114df1.png">
</inner-header>
<generate-otp>
    <input type="button" class="login_button" value="Next" onclick="_generateOTP(false)" />
    <span class="tnc">By clicking "Next" you agree to our <a target="_blank" href="/Templates/i2e1/tnc.html">Terms & Conditions</a></span>
</generate-otp>

<access-code-connect-button>
    <input type="button" class="login_button" value="Sign in" onclick="_generateOTP(false)" />
    <span class="tnc">By clicking "Next" you agree to our <a target="_blank" href="/Templates/i2e1/tnc.html">Terms & Conditions</a></span>
</access-code-connect-button>

<room-no-connect-button>
    <input type="button" class="login_button" value="Sign in" onclick="_generateOTP(false)" />
    <span class="tnc">By clicking "Next" you agree to our <a target="_blank" href="/Templates/i2e1/tnc.html">Terms & Conditions</a></span>
</room-no-connect-button>

<connect-button>
    <input type="button" class="login_button" value="Sign in" onclick="_connect(false)" />
</connect-button>
