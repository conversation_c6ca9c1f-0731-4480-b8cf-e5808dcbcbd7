<style>
    body {
        font-family: 'GillSansMTStd-Light';
        color: #333;
        background-color: white;
    }
    .login.container {
        background-color: #ededed;  
    }
    
    .outer-card img {
        height: 7.8125rem;
        width: 11.6275rem; 
    }
    
    .login.container .login_button {
        background-color: #1D59A9;
        color: #fff;
        padding: 8px 20px;
        border-radius: 4px;
        font-size: 1rem;
        border: 1px solid #1D59A9;
        width: 100%;
        font-weight: 600;
        margin-top: .7rem;
    }

    .login.container .tnc {
        font-weight: 700;
        font-size: .8rem;
    }

    .login.container .tnc > a {
        color: rgba(255, 0, 0, 0.79);
    }

    .login.container .tnc > a:hover {
        color: red;
        text-decoration: underline;
    }
    @media (max-width: 420px){     
        body{
            background-position-x: -210px;
        }
    }

</style>

<outer-header>
    <img src="http://i2e1storage.blob.core.windows.net/sawan-images/speedychow_logo.png">
</outer-header>

<inner-header></inner-header>

<generate-otp>
    <input type="button" class="login_button" value="Next" onclick="_generateOTP(false)" />
    <span class="tnc">By clicking "Next" you agree to our <a target="_blank" href="/Templates/i2e1/tnc.html">Terms & Conditions</a></span>
</generate-otp>

<access-code-connect-button>
    <input type="button" class="login_button" value="Sign in" onclick="_generateOTP(false)" />
    <span class="tnc">By clicking "Next" you agree to our <a target="_blank" href="/Templates/i2e1/tnc.html">Terms & Conditions</a></span>
</access-code-connect-button>

<room-no-connect-button>
    <input type="button" class="login_button" value="Sign in" onclick="_generateOTP(false)" />
    <span class="tnc">By clicking "Next" you agree to our <a target="_blank" href="/Templates/i2e1/tnc.html">Terms & Conditions</a></span>
</room-no-connect-button>

<connect-button>
    <input type="button" class="login_button" value="Sign in" onclick="_connect(false)" />
</connect-button>
