console.dir('template validator loaded');
var _i2e1TemplateUtils = _i2e1TemplateUtils || {};
_otpMsg = 'Enter 6 digit password';
_i2e1TemplateUtils.postload = function() {
	$('[for=otp]').text(_otpMsg);
	
}
otpStarPlaceholder = '******';
	 _handleOTPKeyPress = function(event, elem) {
		if (elem.value.length >= 6
			&& event.which != 46 // delete
			&& event.which != 8 // backspace
		) {
			event.preventDefault();
		}
	}


var _easyRwdSignUp = function() {
	i2e1Api.doAjax('/Login/EasyRewardsSignUp', {
        mobile: document.getElementById('signup-mobile').value,
        name: document.getElementById('signup-name').value,
		email: document.getElementById('signup-email').value,
		gender: document.getElementById('signup-gender').value,
		date: document.getElementById('signup-date').value,
		month: document.getElementById('signup-month').value,
		year: document.getElementById('signup-year').value
    }, function (response) {
        if (response.status == 0) {
			_swapState(_i2e1Constants.secondState);
		} else {
			_handleOTPError('signup-mobile', 'Please try after some time', 'custom-state');
		}
    }, function(){
		_handleOTPError('signup-mobile', 'Please try after some time', 'custom-state');
	});
}

_generateOTP.success = function(response) { 
     if(response.msg === "SignUp"){  
		
		$('.signup-form #signup-mobile').val(_getMobile());
		_swapState(_i2e1Constants.customState);
	 }
     else _swapState(_i2e1Constants.secondState);

}




