<style>
    body {
        background-image: url(https://i2e1storage.blob.core.windows.net/sawan-images/goldflake-background.png);
        background-color: ffffff;
        background-size: cover;
        background-repeat: repeat;
        
    }

    .user-header img {
        width: 10rem;
        margin-bottom: 1rem;
        margin-top: 1rem;
    }
    .user-header {
        background-color: #f3d8a3;
		margin-left: auto;
		margin-right: auto;
		height: 11rem;
		position: absolute;
		width: inherit;
		top: 0;
		left: 0;
		right: 0;
		border-top-left-radius: inherit;
		border-top-right-radius: inherit;
		border-bottom: 1px solid #9e191d;
    }
    .login.container{
        background-color: rgba(255, 255, 255, 0.59) !important;
		box-shadow: none;
		border-top-right-radius: 2rem;
		border-top-left-radius: 2rem;
		padding: 1rem 1rem 0 1rem;
    }
	
	.login.container:after {
		content: "";
		position: absolute;
		bottom: -2rem;
		left: 0;
		right: 0;
		border-top: 2rem solid rgba(255, 255, 255, 0.59) !important;
		border-left: 2rem solid transparent;
		border-right: 2rem solid transparent;
		
	}
		
	.login.container .first-state:not(.display-none),
	.login.container .second-state:not(.display-none) {
        margin-top: 11rem;
    }

    .login.container .login_button {
        background-color: #da2c26;
        color: #fff;
        border-radius: 4px;
        font-size: 1rem;
        border: 1px solid #da2c26;
        width: 100%;
        font-weight: 600;
    }
	
    .login.container .tnc {
        font-weight: 700;
        font-size: .8rem;
    }

    .login.container .tnc > a {
        color: rgba(255, 0, 0, 0.79);
    }

    .login.container .tnc > a:hover {
        color: red;
        text-decoration: underline;
    }
	.login.container #progressBar {
		left: 2rem;
		right: 2rem;
		max-width: 18rem;
	}
	
	.login.container #progressBar div {
	    background-color: rgb(158, 25, 29)
	}
    .footer span a {
        color: #2e2e2e !important;
    }
    
	.icons-container .small {
		border-radius: 2rem;
	}
    .icons-container .small img{
		border-radius: inherit;
	}
	
	.landing.container {
        background-color: rgba(255, 255, 255, 0.72) !important;
		border-radius: 5rem;
		margin-top: 40px;
    }
	
	.icons-container .small {
		border-radius: 2rem;
	}
    .icons-container .small img{
		border-radius: inherit;
	}
	.time-usage {
		background-color: white;
		color: #D30606;
	}
    
    
    
</style>

<outer-header></outer-header>
<user-header>
	<img src="https://i2e1storage.blob.core.windows.net/sawan-images/user.png">
</user-header>
<inner-header></inner-header>

<generate-otp>
    <input type="button" class="login_button" value="Next" onclick="_generateOTP(false)" />
    <span class="tnc">By clicking "Next" you agree to our <a target="_blank" href="/Templates/i2e1/tnc.html">Terms & Conditions</a></span>
</generate-otp>

<access-code-connect-button>
    <input type="button" class="login_button" value="Sign in" onclick="_generateOTP(false)" />
    <span class="tnc">By clicking "Next" you agree to our <a target="_blank" href="/Templates/i2e1/tnc.html">Terms & Conditions</a></span>
</access-code-connect-button>

<room-no-connect-button>
    <input type="button" class="login_button" value="Sign in" onclick="_generateOTP(false)" />
    <span class="tnc">By clicking "Next" you agree to our <a target="_blank" href="/Templates/i2e1/tnc.html">Terms & Conditions</a></span>
</room-no-connect-button>

<connect-button>
    <input type="button" class="login_button" value="Sign in" onclick="_connect(false)" />
</connect-button>