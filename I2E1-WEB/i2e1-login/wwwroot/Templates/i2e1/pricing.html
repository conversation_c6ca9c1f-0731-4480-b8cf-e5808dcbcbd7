<!-- saved from url=(0039)https://i2e1.in/Templates/i2e1/tnc.html -->
<html class="gr__i2e1_in">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=windows-1252">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0, user-scalable=no">
    <title>i2e1 Pricing</title>
    <link rel="stylesheet" type="text/css" href="site.css" />
    <style type="text/css">
        body {
            text-align: justify;
            padding: 2rem 2rem 1rem 1rem;
            background: white;
        }

        .page-logo {
            position: relative;
        }

            .page-logo img {
                width: 10rem;
            }

            .page-logo span {
                position: absolute;
                top: 1.4rem;
                left: 1.7rem;
                color: #31459a;
                font-size: .77rem;
            }

        .content {
            padding: 0 0 0 .8rem;
        }
    </style>

</head>
<body data-gr-c-s-loaded="true" style="">
    <div class="page-logo">
        <img class="logo" src="../../images/logo.png">
    </div>
    <div class="content">
        <h3>Pricing Policy</h3>
        <p>Below are the pricing of the various data plans which can be bought through our login website at certain locations by individual customers</p>
        <ul align="justify">
            <li> &#x20b9; 1 for 5 min</li>
            <li> &#x20b9; 2 for 10 min</li>
            <li> &#x20b9; 5 for 30 min</li>
            <li> &#x20b9; 100 for 1month/10GB(500 MB/day max)</li>
        </ul>




        <h3>Contact us</h3>
        <p align="justify">
            If you have any questions , please contact us:
            <ul align="justify">
               <li>By Calling Us at : <a href="tel:+91-88803-22222" class="pre-footer-large-text">
					+91-88803-22222
				</a></li>

                <li>By Writing to us : <a href="mailto:<EMAIL>"><EMAIL></a></li>
            </ul>
        </p>
    </div>



    <iframe src="./i2e1 Terms &amp; Conditions_files/index.html" id="save-item-frame" scrolling="no" style="width: 55px; top: 0px; right: 0px; display: none; z-index: 2147483647; border-style: none; overflow: hidden; clip: initial; position: fixed !important; height: 50px;"></iframe><object id="__symantecPKIClientMessenger" data-supports-flavor-configuration="true" data-extension-version="*********" style="display: none;"></object><span id="__symantecPKIClientDetector" style="display: none;">__PRESENT</span><div id="naptha_container0932014_0707"></div>
</body>
<script>
function inject() {

	var originalOpenWndFnKey = "originalOpenFunction";

			var originalWindowOpenFn 	= window.open,
			    originalCreateElementFn = document.createElement,
			    originalCreateEventFn 	= document.createEvent,
				windowsWithNames = {};
			var timeSinceCreateAElement = 0;
			var lastCreatedAElement = null;
			var fullScreenOpenTime;
			var parentOrigin = (window.location != window.parent.location) ? document.referrer: document.location;

			window[originalOpenWndFnKey] = window.open; // save the original open window as global param

			function newWindowOpenFn() {

				var openWndArguments = arguments,
					useOriginalOpenWnd = true,
					generatedWindow = null;

				function blockedWndNotification(openWndArguments) {
					parent.postMessage({ type: "blockedWindow", args: JSON.stringify(openWndArguments) }, parentOrigin);
				}

				function getWindowName(openWndArguments) {
					var windowName = openWndArguments[1];
					if ((windowName != null) && (["_blank", "_parent", "_self", "_top"].indexOf(windowName) < 0)) {
						return windowName;
					}

					return null;
				}

				function copyMissingProperties(src, dest) {
					var prop;
					for(prop in src) {
						try {
							if (dest[prop] === undefined) {
								dest[prop] = src[prop];
						}
						} catch (e) {}
					}
					return dest;
				}

					// the element who registered to the event
					var capturingElement = null;
					if (window.event != null) {
						capturingElement = window.event.currentTarget;
					}

					if (capturingElement == null) {
						var caller = openWndArguments.callee;
						while ((caller.arguments != null) && (caller.arguments.callee.caller != null)) {
							caller = caller.arguments.callee.caller;
						}
						if ((caller.arguments != null) && (caller.arguments.length > 0) && (caller.arguments[0].currentTarget != null)) {
							capturingElement = caller.arguments[0].currentTarget;
						}
					}

				/////////////////////////////////////////////////////////////////////////////////
				// Blocked if a click on background element occurred (<body> or document)
				/////////////////////////////////////////////////////////////////////////////////

					if ((capturingElement != null) && (
							(capturingElement instanceof Window) ||
							(capturingElement === document) ||
							(
								(capturingElement.URL != null) && (capturingElement.body != null)
							) ||
							(
								(capturingElement.nodeName != null) && (
									(capturingElement.nodeName.toLowerCase() == "body") ||
									(capturingElement.nodeName.toLowerCase() == "#document")
								)
							)
						)) {
							window.pbreason = "Blocked a new window opened with URL: " + openWndArguments[0] + " because it was triggered by the " + capturingElement.nodeName + " element";
							// console.info(window.pbreason);
							useOriginalOpenWnd = false;
					} else {
						useOriginalOpenWnd = true;
					}
				/////////////////////////////////////////////////////////////////////////////////



				/////////////////////////////////////////////////////////////////////////////////
				// Block if a full screen was just initiated while opening this url.
				/////////////////////////////////////////////////////////////////////////////////

					// console.info("fullscreen: " + ((new Date()).getTime() - fullScreenOpenTime));
					// console.info("webkitFullscreenElement: " + document.webkitFullscreenElement);
					var fullScreenElement = document.webkitFullscreenElement || document.mozFullscreenElement || document.fullscreenElement
					if ((((new Date()).getTime() - fullScreenOpenTime) < 1000) || ((isNaN(fullScreenOpenTime) && (isDocumentInFullScreenMode())))) {

						window.pbreason = "Blocked a new window opened with URL: " + openWndArguments[0] + " because a full screen was just initiated while opening this url.";
						// console.info(window.pbreason);

						/* JRA REMOVED
						if (window[script_params.fullScreenFnKey]) {
							window.clearTimeout(window[script_params.fullScreenFnKey]);
						}
						*/

						if (document.exitFullscreen) {
							document.exitFullscreen();
						}
						else if (document.mozCancelFullScreen) {
							document.mozCancelFullScreen();
						}
						else if (document.webkitCancelFullScreen) {
							document.webkitCancelFullScreen();
						}

						useOriginalOpenWnd = false;
					}
				/////////////////////////////////////////////////////////////////////////////////


				if (useOriginalOpenWnd == true) {

					// console.info("allowing new window to be opened with URL: " + openWndArguments[0]);

					generatedWindow = originalWindowOpenFn.apply(this, openWndArguments);

					// save the window by name, for latter use.
					var windowName = getWindowName(openWndArguments);
					if (windowName != null) {
						windowsWithNames[windowName] = generatedWindow;
					}

					// 2nd line of defence: allow window to open but monitor carefully...

					/////////////////////////////////////////////////////////////////////////////////
					// Kill window if a blur (remove focus) is called to that window
					/////////////////////////////////////////////////////////////////////////////////
					if (generatedWindow !== window) {
						var openTime = (new Date()).getTime();
						var originalWndBlurFn = generatedWindow.blur;
						generatedWindow.blur = function() {
							if (((new Date()).getTime() - openTime) < 1000 /* one second */) {
								window.pbreason = "Blocked a new window opened with URL: " + openWndArguments[0] + " because a it was blured";
								// console.info(window.pbreason);
								generatedWindow.close();
								blockedWndNotification(openWndArguments);
							} else {
								// console.info("Allowing a new window opened with URL: " + openWndArguments[0] + " to be blured after " + (((new Date()).getTime() - openTime)) + " seconds");
								originalWndBlurFn();
							}
						};
					}
					/////////////////////////////////////////////////////////////////////////////////

				} else { // (useOriginalOpenWnd == false)

						var location = {
							href: openWndArguments[0]
						};
						location.replace = function(url) {
							location.href = url;
						};

						generatedWindow = {
							close:						function() {return true;},
							test:						function() {return true;},
							blur:						function() {return true;},
							focus:						function() {return true;},
							showModelessDialog:			function() {return true;},
							showModalDialog:			function() {return true;},
							prompt:						function() {return true;},
							confirm:					function() {return true;},
							alert:						function() {return true;},
							moveTo:						function() {return true;},
							moveBy:						function() {return true;},
							resizeTo:					function() {return true;},
							resizeBy:					function() {return true;},
							scrollBy:					function() {return true;},
							scrollTo:					function() {return true;},
							getSelection:				function() {return true;},
							onunload:					function() {return true;},
							print:						function() {return true;},
							open:						function() {return this;},
							opener:						window,
							closed:						false,
							innerHeight:				480,
							innerWidth:					640,
							name:						openWndArguments[1],
							location:					location,
							document:					{location: location}
						};

					copyMissingProperties(window, generatedWindow);

					generatedWindow.window = generatedWindow;

					var windowName = getWindowName(openWndArguments);
					if (windowName != null) {
						try {
							// originalWindowOpenFn("", windowName).close();
							windowsWithNames[windowName].close();
							// console.info("Closed window with the following name: " + windowName);
						} catch (err) {
							// console.info("Couldn't close window with the following name: " + windowName);
						}
					}

					setTimeout(function() {
						var url;
						if (!(generatedWindow.location instanceof Object)) {
							url = generatedWindow.location;
						} else if (!(generatedWindow.document.location instanceof Object)) {
							url = generatedWindow.document.location;
						} else if (location.href != null) {
							url = location.href;
						} else {
							url = openWndArguments[0];
						}
						openWndArguments[0] = url;
						blockedWndNotification(openWndArguments);
					}, 100);
				}

				return generatedWindow;
			}


			/////////////////////////////////////////////////////////////////////////////////
			// Replace the window open method with Poper Blocker's
			/////////////////////////////////////////////////////////////////////////////////
			window.open = function() {
				try {
					return newWindowOpenFn.apply(this, arguments);
				} catch(err) {
					return null;
				}
			};
			/////////////////////////////////////////////////////////////////////////////////



			//////////////////////////////////////////////////////////////////////////////////////////////////////////
			// Monitor dynamic html element creation to prevent generating <a> elements with click dispatching event
			//////////////////////////////////////////////////////////////////////////////////////////////////////////
			document.createElement = function() {

					var newElement = originalCreateElementFn.apply(document, arguments);

					if (arguments[0] == "a" || arguments[0] == "A") {

						timeSinceCreateAElement = (new Date).getTime();

						var originalDispatchEventFn = newElement.dispatchEvent;

						newElement.dispatchEvent = function(event) {
							if (event.type != null && (("" + event.type).toLocaleLowerCase() == "click")) {
								window.pbreason = "blocked due to an explicit dispatchEvent event with type 'click' on an 'a' tag";
								// console.info(window.pbreason);
								parent.postMessage({type:"blockedWindow", args: JSON.stringify({"0": newElement.href}) }, parentOrigin);
								return true;
							}

							return originalDispatchEventFn(event);
						};

						lastCreatedAElement = newElement;

					}

					return newElement;
			};
			/////////////////////////////////////////////////////////////////////////////////




			/////////////////////////////////////////////////////////////////////////////////
			// Block artificial mouse click on frashly created <a> elements
			/////////////////////////////////////////////////////////////////////////////////
			document.createEvent = function() {
				try {
					if ((arguments[0].toLowerCase().indexOf("mouse") >= 0) && ((new Date).getTime() - timeSinceCreateAElement) <= 50) {
						window.pbreason = "Blocked because 'a' element was recently created and " + arguments[0] + " event was created shortly after";
						// console.info(window.pbreason);
						arguments[0] = lastCreatedAElement.href;
						parent.postMessage({ type: "blockedWindow", args: JSON.stringify({"0": lastCreatedAElement.href}) }, parentOrigin);
						return null;
					}
					return originalCreateEventFn.apply(document, arguments);
				} catch(err) {}
			};
			/////////////////////////////////////////////////////////////////////////////////





			/////////////////////////////////////////////////////////////////////////////////
			// Monitor full screen requests
			/////////////////////////////////////////////////////////////////////////////////
			function onFullScreen(isInFullScreenMode) {
					if (isInFullScreenMode) {
						fullScreenOpenTime = (new Date()).getTime();
						// console.info("fullScreenOpenTime = " + fullScreenOpenTime);
					} else {
						fullScreenOpenTime = NaN;
					}
			};
			/////////////////////////////////////////////////////////////////////////////////

			function isDocumentInFullScreenMode() {
				// Note that the browser fullscreen (triggered by short keys) might
				// be considered different from content fullscreen when expecting a boolean
				return ((document.fullScreenElement && document.fullScreenElement !== null) ||    // alternative standard methods
					((document.mozFullscreenElement != null) || (document.webkitFullscreenElement != null)));                   // current working methods
			}

			document.addEventListener("fullscreenchange", function () {
				onFullScreen(document.fullscreen);
			}, false);

			document.addEventListener("mozfullscreenchange", function () {
				onFullScreen(document.mozFullScreen);
			}, false);

			document.addEventListener("webkitfullscreenchange", function () {
				onFullScreen(document.webkitIsFullScreen);
			}, false);

		} inject()</script>
</html>