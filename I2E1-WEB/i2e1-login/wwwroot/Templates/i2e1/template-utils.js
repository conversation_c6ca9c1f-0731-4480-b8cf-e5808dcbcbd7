var resendTimeout, landingPage;
var iOS = /iPhone/.test(navigator.userAgent) && !window.MSStream;
var mac = /Mac OS/.test(navigator.userAgent) && !window.MSStream && !iOS;
var _isHttpContainerEnabled = window.location.href.indexOf('UserLogin') > 0;
var _campaigns = null;
if (!String.prototype.format) {
    String.prototype.format = function () {
        var args = arguments;
        return this.replace(/{(\d+)}/g, function (match, number) {
            return typeof args[number] !== 'undefined'
              ? args[number]
              : match
            ;
        });
    };
}

var showPopUp = function (message, isSuccess) {
    $('#message').text(message);
    $('#header-success,#header-failed').hide();
    if (isSuccess)
        $('#header-success').show();
    else
        $('#header-failed').show();

    $('#message-pop-up').show();
}

var _copy = function(data) {
    var dummy = $('<textarea>').val(data).appendTo('body').select();
    document.execCommand('copy');
    dummy.remove();
    console.log('copied', data);
}

var callNearestShop = function () {
    i2e1Api.logEvent("nearestshop_call", _loginUser.nasid, { mobile: _loginUser.mobile });
}

var _i2e1Ques = [];

var _i2e1FirstAnsweredAt = {
    'paint': Date.now()
};

var _i2e1TemplateUtils = _i2e1TemplateUtils || {}
String.prototype.replaceAll = function (target, replacement) {
    return this.split(target).join(replacement);
};

var roundoff = function (val) {
    return Math.floor(val * 100) / 100;
}
var roundoffTime = function (totalSec) {
    if (totalSec > 0) {
        var days = parseInt(totalSec / 86400);
        var hours = parseInt(totalSec / 3600) % 24;
        var minutes = parseInt(totalSec / 60) % 60;
        var seconds = totalSec % 60;
        var result = (days >= 1 ? days + (days === 1 ? ' day ' : ' days ') : '') +
            (hours >= 1 ? hours + (hours === 1 ? ' hr ' : ' hrs ') : '');
        if (days < 1)
            result += (minutes >= 1 ? minutes + (minutes === 1 ? ' min' : ' mins') : '');
        return result ? result : 'less than a minute';
    }
    return 'None';
}





var _previousState;
function _setCookie(cname, cvalue, exSeconds) {
    var d = new Date();
    d.setTime(d.getTime() + (exSeconds * 1000));
    var expires = "expires=" + d.toUTCString();
    document.cookie = cname + "=" + cvalue + "; " + expires;
}

function _getCookie(cname) {
    var name = cname + "=";
    var ca = document.cookie.split(';');
    for (var i = 0; i < ca.length; i++) {
        var c = ca[i];
        while (c.charAt(0) === ' ') c = c.substring(1);
        if (c.indexOf(name) !== -1) return c.substring(name.length, c.length);
    }
    return "";
}

var _generateOTP = function (resend) {
    _connect(resend);
}

var _resetStates = function (state, options) {
    options = options || {};
    if (typeof options.stateTransition !== 'undefined' && !options.stateTransition) return;
    var currentState = $('.state-transition:not(.display-none)');
    _previousState = !currentState.length ? null : '.' + currentState[0].className.replace(/state-transition/g, '').replace('form', '').replace(/ /g,'');
    var states = $('.state-transition');
    if (currentState.length > 0 && currentState[0].className.indexOf(state.substring(1)) > -1) {
        return;
    }
    state = $(state);
    !options.skipEvent && _logMPEvent(options.state, {
        event: _i2e1Constants.stateOpened,
        isReverse: options.reverse
    });

    states.each(function (s, s1) {
        if (currentState.length > 0 && s1.className == currentState[0].className) {
            if (!options.reverse) {
                $(s1).css('left', '-20rem').addClass('display-none');
            } else {
                $(s1).css('left', '20rem').addClass('display-none');
            }
        } else if (state && state.length && s1.className == state[0].className) {
            s1.classList.remove('display-none');
            s1.style.left = '0';
        }
    });
}

var _onTileClick = function (link) {
    if (link.indexOf('tnc') > 0) {
        _logMPEvent(_i2e1Constants.tncClicked, {
            event: _i2e1Constants.tncClicked
        });
    } else {
        _logMPEvent(_i2e1Constants.landingState, {
            event: _i2e1Constants.tileClicked
        });
    }
    if (_isHttpContainerEnabled) {
        window.parent.postMessage({
            action: '_fullRedirect',
            link: link
        }, '*');
    }
    else {
        window.open(link);
    }
}
var _init_app = {
    init_template: function (state) {
        $('.top-container').removeClass('display-none');
        $('#loader').addClass('display-none');
        if (_viewBag.loginLogo) {
            $('#outer-card-img').attr('src', _viewBag.loginLogo);
        }
        if (_loginUser.templateid && _loginUser.templateid.length) {
            $.getScript('/Proxy/GetContent.ashx?url=' + encodeURIComponent('http://i2e1storage.blob.core.windows.net/clienttemplates/' + _loginUser.templateid[0] + '.js'), function () {
                try {
                    if (_i2e1TemplateUtils)
                        _i2e1TemplateUtils.postload && _i2e1TemplateUtils.postload({ state: _viewBag.initialState || state });
                } catch (err) {
                    console.log(err)
                }
            });
        }
        _loadTemplate();

        if (_loginUser.askaccesscode) {
            $(".access-code-area").css('display', '');
            $("[for=otp_access_code]").text(_loginUser.attributes.dataVoucherPlaceholder || 'Enter voucher code');
        } else {
            $(".access-code-area").css('display', 'none');
        }
    },
    init_state: function (state) {
        if (_viewBag.sessionExpired) {
            _swapState(_i2e1Constants.errorState, { errorType: 'time-exhausted', msg: "Your time limit is over", errorImg: "/images/session_time_out.png" });
        } else if (_viewBag.dataExhausted) {
            _swapState(_i2e1Constants.errorState, { errorType: 'limit-exhausted', msg: "Your data limit is over", errorImg: "/images/limitExhausted.png" });
        } else if (_viewBag.loginResponse && state !== _i2e1Constants.customState) {
            if (_viewBag.loginResponse.msg === 'Auto Login') {
                _i2e1Constants.redirectionState = 'AL Redirection State';
                _i2e1Constants.landingState = 'AL Landing State';
                _viewBag.loginResponse.data.isAutoLogin = true;
            }
            _preLogin(_viewBag.loginResponse.data, 'autologin');
        } else if (state) {
            _swapState(state);
        }
        else
            _swapState(_i2e1Constants.firstState);
    },
    init_globalOTP: function () {
        if (_loginUser.clientAuthType === 0 ||
            _loginUser.clientAuthType === 3 ||
            _loginUser.clientAuthType === 5 ||
            _loginUser.clientAuthType === 6 ||
            _loginUser.clientAuthType === 12 ||
            _loginUser.clientAuthType === 13 ||
            _loginUser.clientAuthType === 14) {
            if (_viewBag.globalOtp_Enabled === "True") {
                if (_viewBag.globalOTP_code) {
                    $("#username").intlTelInput({
                        initialCountry: _viewBag.globalOTP_code ? _viewBag.globalOTP_code : "in",
                        allowDropdown: _viewBag.globalOTP_Enforce === "True" ? false : true
                    });
                    $("#username")[0].placeholder = _viewBag.resources.firstState_enter_phone_number;
                    $('.username-area').addClass('global-otp');
                }
            }
        }
    },
    init_social: function() {
        if (_viewBag.facebookPage) {
            document.getElementById("client_logo") && (document.getElementById("client_logo").src = "/images/facebook_share.png");
        }
    },
    init_OTPFields: function () {
    },
    template_hacks: function () {
        /*
     *  Hacks and Fixes for 3rd party templates
     */
        $('.login.container .login_button').css('border', 'none');
        if ($('.login.container .login_button').css('background-color') !== 'rgb(29, 89, 169)') {
            if ($('.login.container .login_button').css('background-color') === 'rgba(0, 0, 0, 0)')
                $('.login.container .login_button').css('background', 'linear-gradient(180deg, #f4693a, #ef4f82)');
            else
                $('.login.container .login_button').css('background', $('.login.container .login_button').css('background-color'));
        }

        $('#resend-otp').attr('style', 'font-size: 14px !important; color: #999');
        if ($('input#resend-otp').css('padding-top') === "6.4px") {
            $('input#resend-otp').attr('style', 'padding-top:0px !important');
        }
        if ($('body').css('color') === 'rgb(255, 255, 255)') {
            $('body').css('color', '#000');
        }

        if ($('.login.container').css('color') === "rgb(255, 255, 255)") {
            if ($('.login.container').css('background-color').substring(0, 4) == "rgba") {
                if ($('.login.container').css('background-color').split(", ").length == 4) {
                    if ($('.login.container').css('background-color').substring(5, 18) == "255, 255, 255")
                        $('.login.container').css('color', "#000000");
                }
            }
        }
    }
}

var init_customState = function(){}

var _bodyOnload = function (state) {
    _reset();
    _init_app.init_template(state);
    //_init_app.init_social();
    //_init_app.init_globalOTP();
    //_init_app.init_OTPFields();
    _init_app.init_state(state || _viewBag.initialState);
    if (_viewBag.initialState == 'Data Voucher State') {
        _showPopup('<h3>Your plan is expired. Kindly purchase new plan</h3>', "OK");
    }

    if (_loginUser.templateId && _loginUser.templateId.length) {
        _init_app.template_hacks();
    }
    i2e1Api.logEvent("signin_pgload", _loginUser.nasid, { mobile: _loginUser.mobile });
};

var _mapClientType = function (authType) {
    switch (authType) {
        case 5:
            return 0;
        case 7:
            return 2;
        case 6:
            return 3;
        default: 
            return authType;
    }
}

var _facebook_share = function () {
    $('.fb-share').disabled = true;

    var share = function () {
        var url = 'https://www.facebook.com/dialog/share?' +
            'app_id=' + _viewBag.facebookAppId +
            '&display=popup' +
            '&href=' + encodeURIComponent(_viewBag.facebookPage) +
            '&redirect_uri=' + encodeURIComponent(_i2e1Domain + 'Login/FacebookLogin');
        _logMPEvent(_i2e1Constants.socialState, {
            event: _i2e1Constants.sharePressed,
            reShare: false,
            fbShareUrl: url
        });
        window.parent.location.href = url;
    }
    share();
}

var _facebook_login = function () {
    $('.fb-login').disabled = true;

    var url = 'https://www.facebook.com/v3.1/dialog/oauth?' +
    'client_id=' + _viewBag.facebookAppId +
    '&display=popup&state=' + _loginUser.sessionid +
    '&redirect_uri=' + encodeURIComponent(_i2e1Domain + 'Login/FacebookLogin') + '&scope=public_profile,email';
    _logMPEvent(_i2e1Constants.fbLoginState, {
        event: 'FB check'
    });
    window.parent.location.href = url;
}
var _reset = function () {
    document.getElementById("errorMsg") && (document.getElementById("errorMsg").innerText = '');
    $('.inline-error').css('display', 'none');
    $('.input-error').removeClass('input-error');
};


var _validateOTP = function (otp) {
    otp = otp || _getOTP();
    if (/^[0-9]{4,6}$/.test(otp)) {
        return true;
    }
};

var _handleOTPError = function (msg, fieldId, state) {
    var fieldError = function () {
        var field = $('[for=' + fieldId + ']');
        if (field.length) {
            field.attr('o-msg', field.text());
            field.closest('.group').addClass('error');
            field.text(msg);
            $(fieldId).focus();
            setTimeout(function () {
                field.text(field.attr('o-msg')).closest('.group').removeClass('error');
                $('.general-msg').text('').addClass('display-none').removeClass('error')
            }, 5000);
        }

        switch (fieldId) {
            case 'otp':
                $('#otp-error').show();
                setTimeout(function () {
                    $('#otp-error').hide();
                }, 5000);
                break;
            case 'username':
                $('#username-error').show();
                setTimeout(function () {
                    $('#username-error').hide();
                }, 5000);
                break;
        }
    }


    if (msg === "Your session is over") {
        _swapState(_i2e1Constants.errorState, { errorType: 'time-exhausted', msg: msg, errorImg: "/images/session_time_out.png" });
    } else if (msg === "Your data limit is over") {
        _swapState(_i2e1Constants.errorState, { errorType: 'limit-exhausted', msg: msg, errorImg: "/images/limitExhausted.png" });
    } else if (msg === "Access is Blocked By Administrator") {
        _swapState(_i2e1Constants.errorState, { errorType: 'user-blocked', msg: msg, errorImg: "/images/not-authorized.png" });
    } else if (msg === "Please contact reception for internet access") {
        $('.general-msg').text(msg).removeClass('display-none').addClass('error');
        $('[for=username]').text(msg).show().addClass('error');
        setTimeout(function () { $('[for=' + fieldId + ']').hide(); }, 4000);
    } else if (fieldId) {
        fieldError();
    } else {
        $('.general-msg').text(msg).removeClass('display-none').addClass('error');
    }

    _logMPEvent(state, {
        event: _i2e1Constants.error,
        message: msg,
        errorInField: fieldId
    });
}

var _templateParser = function (node) {
    if (!node) return false;
    var caged = false;
    for (var child in node.childNodes) {
        if (node.childNodes[child].nodeType === 1) {
            var child = node.childNodes[child];
            var tag = child.tagName;
            var cage = $('[cage=' + tag.toLowerCase() + ']');
            switch (tag) {
                case 'STYLE':
                    caged = true;
                    break;
                case 'OUTER-HEADER':
                    if (cage.length) {
                        cage.find('>img').remove();
                        cage.prepend($('<div class="middler"></div>' + child.innerHTML));
                        var logoImg = child.getElementsByTagName('img')[0];
                        if (logoImg) {
                            var ratio = logoImg.width / logoImg.height;

                            if (ratio > 4) {
                                child.className = child.className + " handle-width";
                            } else if (ratio < .25) {
                                child.className = child.className + " handle-height";
                            }
                        }
                        cage.addClass(child.className);
                    }
                    caged = true;
                    break;
                case 'USER-HEADER':
                case 'INNER-HEADER':
                case 'MOBILE-HELPER':
                case 'PASSPORT-HELPER':
                case 'LANDING-PAGE-MESSAGE':
                case 'LANDING-PAGE-TILES-CONTAINER':
                case 'GUEST-MODE':
                case 'TNC':
                case 'CUSTOM-STATE':
                    if (cage.length) {
                        cage.html(child.innerHTML);
                        cage.addClass(child.className);
                    }

                    $.each(child.attributes, function (key, attr) {
                        cage.attr(attr.name, attr.value);
                    });

                    caged = true;
                    break;
            }
        }
    }
    
    
    return caged;
}

var _loadTemplate = function () {
    $('#forignTemplates').children('div').each(function (i, element) {
        if (!_templateParser(element)) {
            $(element).remove();
        }
    });
    
    _localise()

    if (!_loginUser.templateid || _loginUser.templateid.length === 0) {
        $(".footer .i2e1_logo").remove();
        $(".footer .stepper").css({ 'padding': '1rem' });
    }
}

var _localise = function () {
    if (_viewBag && _viewBag.resources) {
        $.each(_viewBag.resources, function (key, value) {
            $('[i18n=' + key + ']').html(value);
            $('[i18nInputValue=' + key + ']').val(value);
            $('[i18nTitle=' + key + ']').attr('title', value);
            $('[i18nPlaceHolder=' + key + ']').attr('placeholder', value);
        });
    }
}

var _prepareLandingPage = function() {
    var applyTemplate = true;
    
    for(var i=0;i< loginUser.templateid.length; ++i){
        for (var j = 0; j < oldTemplates.length; ++j) {
            if (loginUser.templateid[i] === oldTemplates[j]) applyTemplate = false;
        }
    }

    if (applyTemplate) {
        if (templateUrls.length > 0) {
            for(var i=0;i< templateUrls.length; ++i){
                _templateParser($('#templateContent' + i)[0]);
            }
        }
    }
}

var _hideByClassName = function (classArray) {
    for (var i=0; i< classArray.length; i++) {
        document.getElementsByClassName(classArray[i])[0] 
        && 
        (document.getElementsByClassName(classArray[i])[0].style.display = "none");
    }
}

var _hideAllForms = function () {
    _hideByClassName([
        "limit-exhausted",
        "time-exhausted",
        "user-blocked"
    ]);
}

var _fieldHandler = function (event, options) {
    options = options || {};
    var field = options.field || event.currentTarget.id;
    var value = document.getElementById(field).value;
    if (field) {
        var f = $('#' + field);
        switch (field) {
            case 'username':
                if (value) {
                    f.closest('.group').removeClass('error')
                }
                var valid = false;
                if (value == "3333333333") valid = true;
                else if (/^[6-9]{1}$/.test(value.charAt(0)) && /^[6-9][0-9]{9}$/.test(value)) {
                    valid = true;
                }
                if (valid) {
                    $('#get_otp').removeAttr('disabled');
                } else {
                    $('#get_otp').attr('disabled', '');
                }
                break;
            default:
                if (value) {
                    f.closest('.group').removeClass('error')
                } else {
                    f.closest('.group').addClass('error')
                }
        }
    }
    
}

var _submitThis = function (fn) {
    if (typeof event != 'undefined' && event) {
        if (event.which === 13) {
            window[fn]();
            event.stopPropagation();
        }
        if (event.target) {
            $(event.target).removeClass('input-error');
        } else {
            _reset();
        }
    }
}

var _personalise = function (userProfile) {
        var gender = "";
        if (userProfile.gender && (userProfile.gender.toLowerCase().charAt(0) === "f")) {
            gender = "female";
        } else if (userProfile.gender && (userProfile.gender.toLowerCase().charAt(0) === "m")) {
            gender = "male";
        }
        userProfile.mobile = userProfile.mobile || _getMobile() || '';
        var html = "";
        if (gender) html += "<div class='gender'><img src='../../images/option-" + gender + ".png' /></div>";
        if (userProfile.mobile) html += "<div><img src='../../images/phone.png' /><span class='mobile'>" + userProfile.mobile + "</span></div>";
        if (userProfile.email) html += "<div><img src='../../images/email.png' /><span class='email'>" + userProfile.email + "</span></div>";

        $("#userprofile").html(html);
}

var _question_proceed = function (mobile, response) {
    var err1 = false, err2;
    _logMPEvent(_i2e1Constants.questionState, { event: _i2e1Constants.submitPressed, nationalId: true });
    err1 = _processAnswers(5);
    err2 = _processAnswers(0);

    !err1 && !err2 && i2e1Api.submitAnswers(mobile.replace(/ /g, ''), {
        questions: _i2e1Ques,
        onSuccess: function () {
            _preLogin(response.landingPage);
        }
    });
}

var _progress = function (progressbar, percent, time) {
    var progressBarWidth = percent * progressbar.width() / 100;
    var factor = $('html').css('font-size').substring(0,2);
    progressBarWidth = (progressBarWidth / factor) + 'rem';
    progressbar.find('div').stop().animate({ width: progressBarWidth }, time);
};

var getPlanDetails = function (callback) {
    i2e1Api.getAttributes({
        onSuccess: function (response) {
            var info = response.data;
            if (info) {
                var dataLeft = (info.dataLeft && info.dataLeft >= 0) ? info.dataLeft : 0;
                var timeLeft = info.timeLeft;
                if (info.dataLeft >= 0 && dataLeft >= 0) {
                    if (dataLeft >= 1073741824) //converting to GB
                    {
                        dataLeft = roundoff(dataLeft / 1073741824) + " GB";
                    }
                    else if (dataLeft >= 1048576) //converting to MB
                    {
                        dataLeft = roundoff(dataLeft / 1048576) + " MB";
                    }
                    else if (dataLeft >= 1024) //converting to KB
                    {
                        dataLeft = roundoff(dataLeft / 1024) + " KB";
                    } else if (dataLeft === 0) {
                        dataLeft = "";
                    }
                    _viewBag.dataLeft = dataLeft;
                }
                else {
                    dataLeft = 'UNLIMITED';
                    _viewBag.dataLeft = dataLeft;
                }
                if (timeLeft && timeLeft >= 0) {
                    timeLeft = roundoffTime(timeLeft);
                    _viewBag.timeLeft = timeLeft;
                }
                else {
                    _viewBag.timeLeft = '';
                }
                callback && callback(dataLeft, timeLeft);
            }
        }
    });
}

var _questionXHR = null;

var _swapState = function (state, options) {
    _backFN = null;
    _hideAllForms();
    options = options || {};
    options.state = state;

    var mobile = _getMobile() || '';
    switch (state) {
        case _i2e1Constants.firstState:
            _i2e1Ques = [];
            _stateFunctions.firstState(options);
            break;
        case _i2e1Constants.secondState:
            _i2e1Ques = [];
            _backFN = function () {
                _i2e1Ques = [];
                _swapState(_i2e1Constants.firstState, { resend: false, reverse: true })
            }

            _otpMsg = _viewBag.resources.secondState_enter_4_digit_password;
            if (_loginUser.nasid === 660) {
                _resetStates('.error-state', options);
                document.getElementsByClassName("go-back-to-login")[0].style.display = "none";
                $(".airtel-limit-exceeded").show();
            } else {
                if (_campaigns && _campaigns.length > 0) {
                    _processCampaign(_campaigns[0]);
                    _campaigns = null;
                    return;
                }
                _questionXHR && _questionXHR.abort();
                if (!window.DIY || (window.DIY && window.DIY == false))
                    _questionXHR = _getQuestions({ questionType: [0, 5], stateSelector: '.second-state', mobile: mobile }, { hideLoading: true });
                _resetStates('.second-state', options);
                _reset();
                setTimeout(function () {
                    $('#resend-otp').attr('disabled', false);
                }, 15000);

                setTimeout(function () {
                    if ($("#otp").length) {
                        $("#otp")[0].focus();
                    }
                }, 300);

                $('.stepper > div').removeClass('visited');
                $($('.stepper .connector')[0]).addClass('visited');
                $($('.stepper .step')[0]).addClass('visited');
                $($('.stepper .step')[1]).addClass('visited');
                $('.second-state .barking, #primary-otp-area').removeClass('display-none');
                $('.questions-confirm-area,.question-area').addClass('display-none');
                $('.second-state').show();
            }
            i2e1Api.logEvent("otp_pgload", _loginUser.nasid, { mobile: mobile });
            break;
        case _i2e1Constants.dataVoucherState:
            var paidPlans = [];
            if (_viewBag.plans) {
                paidPlans = _viewBag.plans.filter(function (p) {
                    return p.price > 0
                });
            }
            if (!paidPlans.length) {
                _swapState(_i2e1Constants.freePlanState)
            }
            else {
                _stateFunctions.dataVoucherState(options);
                _resetStates('.data-voucher-state', options);
                _reset();
            }
            i2e1Api.logEvent("coupon_pgload", _loginUser.nasid, { mobile: mobile });
            break;
        case _i2e1Constants.internetOverState:
            _resetStates('.internet-over-state', options);
            _reset();
            i2e1Api.logEvent("internet_over_pgload", _loginUser.nasid, { mobile: mobile });
            break;
        case _i2e1Constants.questionState:
            _getQuestions({
                questionType: [0, 5], stateSelector: '.second-state', mobile: mobile, callback: function (questions) {
                    if (!questions.length && options.otpResponse && !_loginUser.askaccesscode) {
                        _preLogin(options.otpResponse.landingPage);
                    } else {
                        $('html,body').animate({ scrollTop: $(".question-area").offset().top }, 'slow');
                        _resetStates('.second-state', options);
                    }
                }
            });
            _reset();
            break;
        case _i2e1Constants.freePlanState:
            _backFN = function () {
                _swapState(_i2e1Constants.dataVoucherState, { noBack: true })
            }
            _resetStates('.free-plan-state', options);
            _reset();
            break;
        case _i2e1Constants.planSelectionState:
            i2e1Api.logEvent("plan_pgload", _loginUser.nasid, { mobile: _loginUser.mobile });
            _backFN = function () {
                _swapState(_i2e1Constants.dataVoucherState, { noBack : true})
            }
            _stateFunctions.planSelectionState(options)
            break;
        case _i2e1Constants.customerProfileState:
            _resetStates('.customer-profile-state', options);
            break;
        case _i2e1Constants.offerState:
            _resetStates('.voucher-state', options);
            break;
        case _i2e1Constants.storeDetailState:
            _resetStates('.store-detail-state', options);
            break;
        case _i2e1Constants.socialState:
            _resetStates('.social-state', options);
            document.getElementById("skipSocial").style.display = 'none';
            if (!_viewBag.facebookCheckin) {
                setTimeout(function() {
                    document.getElementById("skipSocial").style.display = 'inline';
                }, 2000);
            }

            break;
        case _i2e1Constants.errorState:
            _stateFunctions.errorState(options);
            break;

        case _i2e1Constants.redirectionState:
            i2e1Api.logEvent("connect_pgload", _loginUser.nasid, { mobile: _loginUser.mobile });
            var progressBar = $('<div class="progressBar"><div></div></div>');
            var progressBar = $('<div class="progressBar"><div></div></div>');
            $('body').append(progressBar);
            var _processRedirection = function () {
                $('#redirectionState_use_free_wifi_now').hide();
                getPlanDetails(function (dataLeft, timeLeft) {
                    $('#redirect_state_data_left').text(dataLeft);
                    $('#redirect_state_time_left').text(timeLeft);
                    $('#redirectionState_use_free_wifi_now').show();
                    if (options.source == 'payment') {
                        $('#redirectionState_use_free_wifi_now').html('Taking you to the payment');
                    }
                    setTimeout(function () {
                        _doLogin({
                            parameter: options.landingPage,
                            url: options.userDestinationUrl
                        });
                    }, 2000);
                });
            }
            if (options.offers && options.offers.length) {
                _showVouchers(options.offers);
                document.getElementById('skipVoucher2').onclick = function () {
                    _logMPEvent(_i2e1Constants.offerState, {
                        event: 'State Skipped'
                    });
                    _progress(progressBar, 90, 15000);
                    _processRedirection();
                }
            } else {
                _progress(progressBar, 90, 15000);
                _processRedirection();
            }

            _resetStates('.redirecting-state', options);
            $('.stepper > div').removeClass('visited');
            $('.stepper > div').addClass('visited');
            break;
        case _i2e1Constants.customState:
            _resetStates('.custom-state', options);
            break;
        case _i2e1Constants.paymentState:
            $('.payment-state').html(options.paymentResponse);
            _resetStates('.payment-state', options);
            break;
        case _i2e1Constants.landingState:
            var progressBar = $('.progressBar');
            if(progressBar.length) _progress(progressBar, 100, 200);
            $('body').addClass('landing');
            if (options.landingResponse) options.landingResponse = JSON.parse(options.landingResponse);
            else options.landingResponse = JSON.parse(_viewBag.landingObject);
            if (options.landingResponse.pageTitle.trim() === "You are now connnected") {
                var lpcm = "You are now Connected";
                if (_viewBag.resources && _viewBag.resources.landingState_connected_msg
                    && (_viewBag.resources.landingState_connected_msg !== ""))
                    lpcm = _viewBag.resources.landingState_connected_msg;
                $('.landing-page-message > p').html(lpcm);
            } else {
                $('.landing-page-message > p').text(options.landingResponse.pageTitle);
            }
            _stateFunctions.landingState(options);
            _resetStates('.landing-state', options);
            break;
    }
	if (options.postHook) {
		options.postHook();
	}
};
var _enablepaymentCtas = function () {
    if (_viewBag.paymentModes) {
        $('#pay_via_upi,#pay_via_online').css({ 'display': 'none' });
        if (_viewBag.paymentModes.upiId) {
            $('[name=upi_id]').text(_viewBag.paymentModes.upiId)
            $('#pay_via_upi').css({ 'display': 'block' });
        }
        if (_viewBag.paymentModes.cash && _viewBag.paymentModes.cash == "true") {
            $('#pay_via_cash').css({ 'display': 'block' });
        }
        if (_viewBag.paymentModes.acctInfo) {
            $('#pay_via_online').css({ 'display': 'block' });
        }
    }
}

var fetchPaymentModes = function (callback) {
    if (_viewBag.paymentModes)
        callback && callback();
    else {
        i2e1Api.doAjax("/Login/getPaymentModes", { nasid: _loginUser.nasid, combinedSettingId: _loginUser.combinedSettingId }, function (res) {
            if (res.status == 0) {
                _viewBag.paymentModes = {};
                var modeCount = 0;
                if (res.data) {
                    var spl = res.data.split(';');
                    spl.forEach(function (p) {
                        var x = '=';
                        if (p.indexOf(':') > 0) {
                            x = ':'
                        }
                        var s = p.split(x);
                        _viewBag.paymentModes[s[0]] = s[1];
                        modeCount++;
                    })
                }
                if (modeCount > 0) {
                    $('#buy-online-container').show();
                }
                _enablepaymentCtas();
                callback && callback();
            }
        })
    }
}
var fetchFreeAllowed = function (callback) {
    if (typeof (_viewBag.allowFree) == "undefined") {
        i2e1Api.doAjax("/Login/isFreeSessionAllowed", {
        }, function (res) {
            _viewBag.allowFree = res.data.allowFree;

            if (!_viewBag.allowFree) {
                _viewBag.plans = _viewBag.plans.filter(function (p) {
                    return p.price > 0
                })
            }
            else {
                var freePlans = _viewBag.plans.filter(function (p) { return p.price == 0 });
                if (!freePlans.length)
                    _viewBag.allowFree = false;
            }
            callback(_viewBag.allowFree);
        });
    }
    else {
        callback(_viewBag.allowFree);
    }
}
function calculateDistance(lat1, lon1, lat2, lon2, unit) {
    var radlat1 = Math.PI * lat1 / 180
    var radlat2 = Math.PI * lat2 / 180
    var radlon1 = Math.PI * lon1 / 180
    var radlon2 = Math.PI * lon2 / 180
    var theta = lon1 - lon2
    var radtheta = Math.PI * theta / 180
    var dist = Math.sin(radlat1) * Math.sin(radlat2) + Math.cos(radlat1) * Math.cos(radlat2) * Math.cos(radtheta);
    dist = Math.acos(dist)
    dist = dist * 180 / Math.PI
    dist = dist * 60 * 1.1515
    if (unit == "K") { dist = dist * 1.609344 }
    if (unit == "N") { dist = dist * 0.8684 }
    return dist
}

var _stateFunctions = {
    firstState: function (options) {
        if (!window.DIY || (window.DIY && window.DIY == false))
            _questionXHR = _getQuestions({ questionType: [3], stateSelector: '.first-state' }, { hideLoading: true });
        _resetStates('.first-state', options);
        _reset();
        setTimeout(function () {
            $("#username").length && $("#username")[0].focus();
            $("#room_no").length && $("#room_no")[0].focus();
            _fieldHandler(null, { field: 'username' });
        }, 300);
        $('.stepper > div').removeClass('visited');
        $($('.stepper .step')[0]).addClass('visited');
    },
    dataVoucherState: function (options) {
        fetchPaymentModes(function () {
            if (_viewBag.paymentModes.coupon && _viewBag.paymentModes.coupon == "false")
                _swapState(_i2e1Constants.planSelectionState);

        });
        i2e1Api.getPlanMacMapping(function (response) {
            var planMacMappings = response.planMacMappings;
            var fdmConfigs = response.fdmConfigs;
            if (planMacMappings && planMacMappings.length && fdmConfigs && fdmConfigs.length) {
                for (var i = 0; i < planMacMappings.length; ++i) {
                    var div = $('<div></div>');
                    div.append('<div class="float-div" style="width:50%;">Device<br/><b>' + planMacMappings[i].device + '</b></div>');
                    var logout = $('<img title="Logout" style="width: 1.2rem;position: absolute;margin:-0.5rem 0 0 1rem;" src="/images/power.png"/>');
                    logout.click(function () {
                        i2e1Api.logout({});
                    });
                    var statusDiv = $('<div class="float-div" style="width:50%;">Status<br/><b>Connected</b></div>');
                    statusDiv.append(logout);
                    div.append(statusDiv)
                    $('#devices_connected').append(div).append('<hr/>');
                }
                $('#device_plan').text(response.plan);
                $('.planMacMappingDiv').show();
                
                var deviceAllowed = 0;
                for (var i = 0; i < fdmConfigs.length; ++i)
                    deviceAllowed += fdmConfigs[0].deviceAllowed;

                var date = new Date(fdmConfigs[0].otpIssuedTime)
                $('#devices_allowed').text(deviceAllowed);
                $('#plan_start_date').text(date.toLocaleDateString());
            }
        });
        fetchFreeAllowed(function () {
            if (!_viewBag.allowFree) {
                $('.plan-chooser').find('[charges=0]').remove();
            }
        });
        i2e1Api.getPDOList({
            onSuccess: function (response) {
                if (response.data && response.data.pdo_list) {
                    var pdoList = JSON.parse(response.data.pdo_list);
                    var div = $('#coupon_sellers_div');
                    div.empty();
                    for (var i = 0; i < pdoList.length; ++i)
                        pdoList[i].dist = calculateDistance(response.data.lat, response.data.lng, pdoList[i].lat, pdoList[i].lng, "K");

                    pdoList.sort(function (a, b) { return a.dist - b.dist; });
                    var appendToList = function (start) {
                        var i = start;
                        for (; i < pdoList.length && i < start + 5; ++i) {
                            div.append("<div style='border-bottom: 1px solid #eee;padding: 1em 0.5em;'><a onclick='callNearestShop()' style='float:right;margin-top: 0.8em;' href='tel:" + pdoList[i].mobile +
                                "'><img src='/images/call.svg'/></a><div style='width:90%'><b>" + pdoList[i].shop_name + "</b><br/>" + pdoList[i].address + "</div></div>");
                        }
                        if (i == start + 5 && i < pdoList.length) {
                            var anchor = $('<a>Load more</a>');
                            anchor.click(function () {
                                $('.load-more-pdo').remove();
                                appendToList(start + 5);
                            });
                            var newDiv = $('<div class="load-more-pdo" style="padding: 1em 0.5em;color: #F4693A;">');
                            newDiv.append(anchor);
                            div.append(newDiv);
                        }
                    }
                    appendToList(0);
                    $('#buy_coupon_from').removeClass('display-none');
                }
            },
            nasid: _loginUser.nasid
        }); 
    },
    planSelectionState: function (options) {
        fetchPaymentModes();
        var _psFN = function () {

            if (_viewBag.plans.length == 1 && _viewBag.plans[0].price == 0) {
                _submitPlan(_viewBag.plans[0].id, 'FREE')
            }
            else if (_viewBag.plans.length > 0) {
                _resetStates('.plan-selection-state', options);
                _toPlanSelectionInnerState('.plan-chooser');
            } else {
                _swapState(_i2e1Constants.dataVoucherState, { noBack: true })
            }
        }

        fetchFreeAllowed(_psFN);
    },
    errorState: function (options) {
        _resetStates('.error-state', options);
        if (_loginUser.templateid[0] === 20) {
            _logMPEvent(_i2e1Constants.errorState, {
                event: 'Airtel Plans Shown'
            });
            document.getElementsByClassName("go-back-to-login")[0].style.display = "none";
            $(".airtel-limit-exceeded").show();
        } else {
            $("." + options.errorType).empty().show().append($('<img>').attr('src', options.errorImg));
            $("#errorMsg").text(options.msg);
        }
    },
    landingState: function (options) {
        if ($('.landing-page-tiles-container')[0] && !$('.landing-page-tiles-container')[0].innerText.trim()) {
            var roundoff = function (val) {
                return Math.floor(val * 100) / 100;
            }
            var roundoffData = function (dataLeft) {
                if (dataLeft >= 1073741824) //converting to GB
                {
                    dataLeft = roundoff(dataLeft / 1073741824) + " GB";
                }
                else if (dataLeft >= 1048576) //converting to MB
                {
                    dataLeft = roundoff(dataLeft / 1048576) + " MB";
                }
                else if (dataLeft >= 1024) //converting to KB
                {
                    dataLeft = roundoff(dataLeft / 1024) + " KB";
                }
                return dataLeft;
            }

            var roundoffTime = function (totalSec) {
                if (totalSec > 0) {
                    var days = parseInt(totalSec / 86400);
                    var hours = parseInt(totalSec / 3600) % 24;
                    var minutes = parseInt(totalSec / 60) % 60;
                    var seconds = totalSec % 60;
                    var result = (days >= 1 ? days + (days === 1 ? ' ' + _viewBag.resources.landingState_time_day + ' ' : ' ' + _viewBag.resources.landingState_time_days + ' ') : '') +
                                 (hours >= 1 ? hours + (hours === 1 ? ' ' + _viewBag.resources.landingState_time_hr + ' ' : ' ' + _viewBag.resources.landingState_time_hrs + ' ') : '');
                    if (days < 1)
                        result += (minutes >= 1 ? minutes + (minutes === 1 ? ' ' + _viewBag.resources.landingState_time_min + ' ' : ' ' + _viewBag.resources.landingState_time_mins + ' ') : '');
                    return result ? result : ' ' + _viewBag.resources.landingState_time_lessthan_a_minute + ' ';
                }
                return 'None';
            }

            i2e1Api.getAttributes({
                onSuccess: function (response) {
                    var info = response.data;
                    if (info) {
                        var dataLeft = info.dataLeft;
                        var timeLeft = info.timeLeft;
                        if (dataLeft) {
                            dataLeft = roundoffData(dataLeft);
                            $(".user-attributes .data").html(_viewBag.resources.landingState_data_left_prefix + " " + dataLeft + " " + _viewBag.resources.landingState_data_left_suffix);
                            window.dataLeft = dataLeft;
                        }
                        if (timeLeft) {
                            timeLeft = roundoffTime(timeLeft);
                            $(".user-attributes .time").html((_viewBag.resources.landingState_data_left !== "" ? _viewBag.resources.landingState_time_left : 'Time left') + ": " + timeLeft);
                            window.timeLeft = timeLeft;
                        }
                    }
                }
            });

            var i = 0;
            options.landingResponse.icons.forEach(function (site) {
                if (i === 4) {
                    $('.landing-page-tiles-container').append($('<div/>').
                        html('<img style="padding-top:.5rem; cursor: pointer;" title="i2e1" onclick="_onTileClick(\'http://www.i2e1.com\')" src="/images/logo.png" />'));
                }
                else {
                    $('.landing-page-tiles-container').append($('<div/>').
                        html('<img style="cursor: pointer;" onclick="_onTileClick(\'' + site.url + '\')" title="' + site.title + '" src="/' + site.imageUrl + '" />'));
                }
                i++;
            });
        }
    }
}

var _doLogin = function (options) {
    _makei2e1Login(options.parameter, options.url);
}

var _preLogin = function (landingPage, source) {
    window.landingPage = landingPage;

    var jumpToFB = function () {
        if (_viewBag.facebookPage) {
            document.getElementById('skipSocial').onclick = function () {
                _logMPEvent(_i2e1Constants.socialState, {
                    event: 'State Skipped'
                });
                _viewBag.facebookPage = '';
                var success = function () {
                    _swapState(_i2e1Constants.redirectionState, {
                        landingPage: landingPage
                    });

                };
                var failure = function (response) {
                };
                i2e1Api.doUserLogin({
                    onSuccess: success,
                    onFailure : failure
                });
            }
            _swapState(_i2e1Constants.socialState);
        } else {
            _swapState(_i2e1Constants.redirectionState, {
                landingPage: landingPage,
                source: source
            });
        }
    }

    if (source == 'autologin') {
        _swapState(_i2e1Constants.redirectionState, {
            landingPage: landingPage,
            source: source
        });
    }
    else jumpToFB();
}

var _doLoginRequired = function () {
    if (_viewBag.facebookPage || _viewBag.doFbLogin || (_viewBag.plans && _viewBag.plans.length))
        return false;
    return true;
}

var _receiveMessage = function (event) {
    if ((event.origin.indexOf(window.location.hostname) === 7 || event.origin.indexOf(window.location.hostname) === 8)) {
        switch (event.data.action) {
            case '_paymentState':
                _swapState(_i2e1Constants.paymentState, { paymentResponse: event.data.landingBag.paymentHtml });
                break;
            case '_swapState':
                if (_viewBag.isSwappPromotionOn)
                    _swapState(_i2e1Constants.swappPromoState, { landingResponse: event.data.landingBag.landingObject });
                else _swapState(_i2e1Constants.landingState, { landingResponse: event.data.landingBag.landingObject });

                if (_i2e1TemplateUtils.postLogin) {
                    _i2e1TemplateUtils.postLogin(event.data.landingBag.landingObject);
                } else {
                    window.parent.postMessage({
                        action: '_executeFunc',
                        func: "(" + (function (url, time) {
                            setTimeout(function () {
                                window.location.href = url;
                            }, time || 1000);
                        }).toString() + ")('" + event.data.url + "'," + event.data.time + ")"
                    }, '*');
                }
                break;
            case '_sessionExpired':
                _swapState(_i2e1Constants.errorState, { errorType: 'time-exhausted', msg: "Your time limit is over", errorImg: "/images/session_time_out.png" });
                break;
            case '_dataExhausted':
                _swapState(_i2e1Constants.errorState, { errorType: 'limit-exhausted', msg: "Your data limit is over", errorImg: "/images/limitExhausted.png" });
                break;
            case '_unknownError':
                _swapState(_i2e1Constants.errorState, { errorType: 'unknown-error', msg: "Something wrong happened", errorImg: "/images/error-icon.png" });
                break;
            case '_retringLogin':
            case '_retringFail':
                _logMPEvent(_i2e1Constants.redirectionState, {
                    event: event.data.action,
                });
                $('.redirecting-state .connected').html(event.data.msg);
                break;
        }
    }
}

window.addEventListener("message", _receiveMessage, false);

var _logout = function () {
    if (_isHttpContainerEnabled) {
        window.parent.postMessage({
            action: '_logout'
        }, '*');
    } else {
        window.location.href = 'http://logout.i2e1.com?ip=' + _loginUser.uamip;
    }
}


var _compile = function (template){
    var evalExpr = /\<\%\=(.+?)\%\>/g;
    var expr = /\<\%([\s\S]+?)\%\>/g;
    var empty = /echo\(\"\"\);/g;

    template = template
      .replace(evalExpr, '"); \n  echo( $1 ); \n  echo("')
      .replace(expr, '"); \n $1 \n  echo("');

    template = 'echo("' + template + '");';
    template = template
      .replace(empty, "");

    var script =
    '(function _parser(data){ var output = ""; function echo(html){ output += html; } ' + template + ' return output; })'; 
    return script;
}
                                                                                                                                             
var _parserA3 = eval(_compile("<div class='question question<%= data.id %> img-options rating-question'><div class='question-text'><%= data.quesText %></div> " +
    "<ul answer-type='<%= data.answerType %>' > <% for( var i=1;i<=5;i++) { %> " +
    "<li class='img-option' >	<input id='radio_rating<%= i %>' type='radio' class='option-answer' name='<%= data.id %>' value=<%= i %>  />" +
    "<label for='radio_rating<%= i %>' onclick='_removeErrorClass(this)'><div> <div class='circle'> <img class='content' src='/images/feedback-<%= i %>.png' /> </div> <span></span> </div></label></li> <% } %> </ul></div>"));


var _parseVoucher = eval(_compile(
"<div class='campaign campaign-<%= data.campaignId%>' data-link='<%= data.image_link%>' onclick='_voucherRedeem(<%= data.campaignId%>, true)'>                                  " +
"	<input class='close' title='close' value='X' onclick='_voucherRedeem(<%= data.campaignId%>)' type='button'/>                                                        " +
"	<div class='border'>                                                                                                                                        " +
"		<% if(data.image) {%>                                                                                                                         " +
"		<img src='/Proxy/GetContent.ashx?url=<%= encodeURIComponent(data.image) %>' />                                                                          " +
"		<% if(data.offerCode) {%>   <div class='offerCode'>                                                                                                                                 " +
"			<%= data.offerCode %>                                                                                                                               " +
"		</div>    <% } %>                                                                                                                                              " +
"		<% } else if(data.videoPath) { %>                                                                                                                 " +
"		<video width='100%' autoplay='true' preload>                                                                                                            " +
"			<source src='<%= data.videoPath %>' type='video/mp4'>                                                                                               " +
"			<source src='<%= data.videoPath %>' type='video/ogg'>                                                                                               " +
"			<source src='<%= data.videoPath %>' type='video/avi'>                                                                                               " +
"			<source src='<%= data.videoPath %>' type='video/flv'>                                                                                               " +
"			<source src='<%= data.videoPath %>' type='video/mov'>                                                                                               " +
"			<source src='<%= data.videoPath %>' type='video/webm'>                                                                                               " +
"			<source src='<%= data.videoPath %>' type='video/3gp'>                                                                                               " +
"			Your browser does not support HTML5 video.                                                                                                          " +
"		</video>                                                                                                                                                " +
"		<% } %>                                                                                                                                                 " +
"	</div>                                                                                                                                                      " +
"</div>                                                                                                                                                         "));

var _parseVoucherCustomHTML = eval(_compile("<div class='campaign campaign-<%= data.id%>' data-link='<%= data.image_link%>' onclick='_voucherRedeem(<%= data.id%>, true)'><%= data.voucherHTML%></div>"));

var _showError = function (errOptions) {
    //$('body').animate({ scrollTop: errOptions.questionEl.offset().top }, 'slow');

    var elOffset = errOptions.questionEl.offset().top;
    var elHeight = errOptions.questionEl.height();
    var windowHeight = $('body').height();
    var offset;

    if (elHeight < windowHeight) {
        offset = elOffset - ((windowHeight / 2) - (elHeight / 2));
    }
    else {
        offset = elOffset;
    }
    var speed = 700;
    $('html, body').animate({ scrollTop: offset }, speed);


    switch (errOptions.answerType) {
        case 0:
            errOptions.questionEl.children('.group').addClass('error');
            errOptions.questionEl.find('.group input').focus();
            setTimeout(function () {
                errOptions.questionEl.children('.group').removeClass('error');
            }, 2000);
            break;
        case 1:
        case 2:
            errOptions.questionEl.addClass('error');
            errOptions.questionEl.find('li:not(.init)').toggle('display-none');
            setTimeout(function () {
                errOptions.questionEl.removeClass('error');
            }, 2000);
            _logMPEvent(errOptions.state, {
                event: _i2e1Constants.invalidAnswer,
                questionId: errOptions.questionId
            });
            break;
        case 3:
            errOptions.questionEl.addClass('error').find('input').addClass('error');
            break;
    }
}

var _processCampaign = function (campaign) {
    console.log(campaign)
    switch (campaign.voucherPlacement) {
        case 45:
        case 'BEFORE_PHONE_NUMBER':
        case 43:
        case 'AFTER_PHONE_BEFORE_OTP':
            var div = '';
            if (campaign.videoPath)
                div = $('#internal-templates .campaign-template.video')[0].innerHTML;
            else if (campaign.voucherHTML)
                $('.custom-state').html(campaign.voucherHTML);
            else 
                div = $('#internal-templates .campaign-template.image')[0].innerHTML;
            $('body').append(div);
            div = $('body > .advertisement-campaign')
            if (campaign.videoPath) {
                div.find('video source').attr('src', "/Proxy/GetContent.ashx?url=" + encodeURIComponent(campaign.videoPath));
                
                var video = div.find('video')[0];
                _progress(div.find('.progressBar'), 0, 1);
                var play = function () {
                    video.play();
                    _progress(div.find('.progressBar'), 100, video.duration * 1000);
                
                    setTimeout(function () {
                        div.find('.circle img').removeClass('grayscale');
                        div.find('.circle').on('click', function (evt) {
                            div.remove();
                            evt.stopPropagation();
                        });
                    }, 5000);
                }
                div[0].onclick = function () {
                    i2e1Api.updatePromotionReport({
                        campaignId : campaign.campaignId, 
                        clicked : true
                    });
                    play();
                };
                var videoLoadCheck = setInterval(function () {
                    if (video.readyState === 4) {
                        div.removeClass('display-none').find('.transition').removeClass('display-none').css('left', '0');
                        try {
                            video.click();
                        } catch (err) { }
                        clearInterval(videoLoadCheck);
                    }
                }, 500);
            } else if (campaign.voucherHTML) {
                _swapState(_i2e1Constants.customState);
            } else {
                div.find('.campaign img').attr('src', "/Proxy/GetContent.ashx?url=" + encodeURIComponent(campaign.image));
                if (campaign.image_link) {
                    div.find('.campaign')[0].onclick = function () {
                        _doLogin({
                            parameter: window.landingPage,
                            url: campaign.image_link
                        });
                    }
                }

                var image = div.find('.campaign img')[0];
                var imageLoadCheck = setInterval(function () {
                    if (image.complete) {
                        div.removeClass('display-none').find('.transition').removeClass('display-none').css('left', '0');
                        image.onclick = function () { }
                        clearInterval(imageLoadCheck);
                    }
                }, 500);
                div.find('.circle img').removeClass('grayscale');
                div.find('.circle').on('click', function (evt) {
                    div.remove();
                    evt.stopPropagation();
                });
            }

            if (div.find('.skip').length > 0) {
                div.find('.skip')[0].onclick = function () {
                    i2e1Api.updatePromotionReport({
                        campaignId: campaign.campaignId,
                        skipped: true,
                        secondsWatched: 5
                    });
                }
            }

            break;
    }
}

var _processAnswers = function (questionType) {
    var errAnswer = false;
    if (_i2e1Ques.length) {
        var questionArea = _getQuesArea(questionType);
        
        var state = _i2e1Constants.secondState;
        if (questionType.length === 1) {
            state = _i2e1Constants.firstState;
        }

        for (var i = 0; i < _i2e1Ques.length; i++) {
            if (_i2e1Ques[i].quesType !== questionType)
                continue;
            var answered = null;
            switch (_i2e1Ques[i].answerType) {
                case 1:
                    if (_i2e1Ques[i].optionsOrder)
                        _i2e1Ques[i].displayIndex = _i2e1Ques[i].optionsOrder.indexOf(_i2e1Ques[i].answer) + 1;
                    else
                        _i2e1Ques[i].displayIndex = 0;
                    break;
                case 0:
                case 4:
                case 5:
                    if (_i2e1Ques[i].optionsOrder)
                        _i2e1Ques[i].displayIndex = _i2e1Ques[i].optionsOrder.indexOf(answered) + 1;
                    else
                        _i2e1Ques[i].displayIndex = 0;

                    answered = questionArea.find('[name=answer-' + _i2e1Ques[i].id + ']');
                    if (answered && answered.length) {
                        _i2e1Ques[i].answer = answered[0].value && answered[0].value.trim();
                    }

                    if (_i2e1Ques[i].questionKey.toLowerCase() === 'email' && !_validateEmail(_i2e1Ques[i].answer)) {
                        errAnswer = true;
                        _showError({ questionEl: questionArea.find('.question' + _i2e1Ques[i].id), answerType: 0, questionId: _i2e1Ques[i].id, state: state });
                        $('#label-' + _i2e1Ques[i].id).html('Invalid Email Address').removeClass('display-none');
                        setTimeout(function () {
                            $('#label-' + _i2e1Ques[i].id).html('').addClass('display-none');
                        }, 3000);
                    }
                    else if (_i2e1Ques[i].answer == null || _i2e1Ques[i].answer == '') {
                        errAnswer = true;
                        _showError({ questionEl: questionArea.find('.question' + _i2e1Ques[i].id), answerType: 0, questionId: _i2e1Ques[i].id, state: state });
                        $('#label-' + _i2e1Ques[i].id).html('Invalid Answer').removeClass('display-none');
                        setTimeout(function () {
                            $('#label-' + _i2e1Ques[i].id).html('').addClass('display-none');
                        }, 3000);
                    }
                    break;
                case 3:
                    _i2e1Ques[i].answer = parseInt(answered[0].value);
                    if (_i2e1Ques[i].optionsOrder)
                        _i2e1Ques[i].displayIndex = _i2e1Ques[i].optionsOrder.indexOf(_i2e1Ques[i].answer) + 1;
                    else
                        _i2e1Ques[i].displayIndex = 0;
                    break;
                case 2:
                    for (var j = 0; j < _i2e1Ques[i].options.length; j++) {
                        answered = questionArea.find('[name=answer-' + _i2e1Ques[i].options[j].id + ']:checked');
                        if (answered.length === 1) {
                            _i2e1Ques[i].selectionTime = _i2e1FirstAnsweredAt[_i2e1Ques[i].id];
                            _i2e1Ques[i].options[j].isSelected = true;
                            $(this).closest("div.options-list").children('p').toggle('display-none');
                            errAnswer = false;
                        }
                    }
                    break;
            }
            if (errAnswer) return errAnswer;
        }
    }
    return errAnswer;
}

var _validateEmail = function (email) {
    if (email && email.toLowerCase() !== '<EMAIL>' && /^\w+([\.-]?\w+)*@\w+([\.-]?\w+)*(\.\w{2,3})+$/.test(email.trim())) {
        return (true)
    }
    return false;
}

var _getQuesArea = function (questionType) {
    var stateSelector = '.first-state .question-area';
    switch (questionType) {
        case 0: stateSelector = '.second-state .question-area.type-0'; break;
        case 5: stateSelector = '.second-state .question-area.type-5'; break;
        case 3:
        default: stateSelector = '.first-state .question-area'; break;
    }
    return $(stateSelector);
}

var _mergeByProperty = function (arr1, arr2, prop) {
    $.each(arr2, function (arr2key, arr2obj) {
        var arr1obj = $.grep(arr1, function (arr1obj, ar1key) {
            return arr1obj[prop] === arr2obj[prop];
        });

        arr1obj[0] && ($.extend(arr1obj[0], arr2obj));
    });
}

var _payAirtel = function () {
    var params = _getUrlParams();
    var mobile = $("#username").val();
    _logMPEvent('Redirecting to Airtel Payment Page', { mobile: mobile });
    i2e1Api.doAjax('Login/MakePayment', {
        nasid: params.nasid,
        name: mobile
    }, function (response) {
        window.location.href = response.data;
        _logMPEvent('Airtel Payment Done', { mobile: mobile });
    });
}

var shuffle = function(array) {
    var currentIndex = array.length, temporaryValue, randomIndex;

    // While there remain elements to shuffle...
    while (0 !== currentIndex) {

        // Pick a remaining element...
        randomIndex = Math.floor(Math.random() * currentIndex);
        currentIndex -= 1;

        // And swap it with the current element.
        temporaryValue = array[currentIndex];
        array[currentIndex] = array[randomIndex];
        array[randomIndex] = temporaryValue;
    }

    return array;
}

var _answerQuestions = function () {
    $('.questions-confirm-area').addClass('display-none');
    $('.question-area').removeClass('display-none');
    $('.question-area init').removeClass('display-none');
    $(".question").first().removeClass('display-none');
    if (_i2e1Ques && _i2e1Ques.length < 2)
        $('#connect').removeClass('display-none');
    if (_i2e1Ques && _i2e1Ques.length) {
        _i2e1Ques[0].displayTime = Date.now();
        _i2e1Ques[0].optionsOrder = [];
        if (_i2e1Ques[0].options)
            _i2e1Ques[0].options.forEach(function (option, index) {
                if (_i2e1Ques[0].binarySplit && index >= 2) {
                    $('.answer_' + option.id + '_0').addClass('display-none');
                    _i2e1Ques[0].remainingOptions.push(option.id);
                } else {
                    _i2e1Ques[0].optionsOrder.push(option.id);
                }
            });
        $('.back-area').addClass('display-none');
        if (_i2e1Ques.length == 1)
            $('#done_' + _i2e1Ques[0].id + '_' + _i2e1Ques[0].journey).addClass('display-none');
    }
}

var _mark = function (quesIndex, question, option, answerType) {
    if (_i2e1Ques[quesIndex].selectionTime === undefined)
        _i2e1Ques[quesIndex].selectionTime = Date.now();
    if (parseInt(answerType) == 1) {
        var $box = $("#option-" + option + "-" + quesIndex);
        if ($box.is(":checked")) {
            var group = "input:checkbox[name='" + $box.attr("name") + "']";
            $(group).prop("checked", false);
            _i2e1Ques[quesIndex].answer = null;
        } else {
            var group = "input:checkbox[name='" + $box.attr("name") + "']";
            $(group).prop("checked", false);
            $box.prop("checked", true);
            _i2e1Ques[quesIndex].answer = option;
        }
    }
}

var done = function (quesIndex, questionId, journey) {
    var next = quesIndex + 1, count = 0;
    if (next == _i2e1Ques.length || _i2e1Ques[quesIndex].answer === null || _i2e1Ques[quesIndex].answer === undefined) {
        showPopUp("Select an option to proceed", false);
        return;
    }

    if (_i2e1Ques[next].binarySplit) {
        //Hide All Options By Default If Next Question is a Continuation
        var options = _i2e1Ques[quesIndex].remainingOptions;
        _i2e1Ques[next].options.forEach(function (option, index) {
            $('.answer_' + option.id + '_' + next).addClass('display-none');
        });
        if (_i2e1Ques[quesIndex].id === _i2e1Ques[next].id) {
            //Add Current Winner to Next Remaining Options
            _i2e1Ques[quesIndex].remainingOptions.push(_i2e1Ques[quesIndex].answer);
            _i2e1Ques[next].optionsOrder = [];
            _i2e1Ques[quesIndex].remainingOptions.forEach(function (option, index) {
                if (count < 2) {
                    $('.answer_' + option + '_' + next).removeClass('display-none');
                    _i2e1Ques[next].optionsOrder.push(option);
                    count = count + 1;
                } else {
                    _i2e1Ques[next].remainingOptions.push(option);
                }
            });
        } else {
            _i2e1Ques[next].optionsOrder = [];
            _i2e1Ques[next].options.forEach(function (option, index) {
                if (count < 2) {
                    $('.answer_' + option.id + '_' + next).removeClass('display-none');
                    _i2e1Ques[next].optionsOrder.push(option.id);
                    count = count + 1;
                } else {
                    _i2e1Ques[next].remainingOptions.push(option.id);
                }
            });
        }
    } else {
        _i2e1Ques[next].remainingOptions = [];
        _i2e1Ques[next].optionsOrder = [];
        _i2e1Ques[next].options.forEach(function (option, index) {
            console.log(3, option, index);
            _i2e1Ques[next].optionsOrder.push(option.id);
        });
    }
    //Hide Current Question
    $('.question' + questionId + '_' + journey).remove();
    //Un-Hide Next Question
    $('.question' + _i2e1Ques[next].id + '_' + _i2e1Ques[next].journey).removeClass('display-none');
    if (next >= (_i2e1Ques.length - 1)) {
        $('#connect').removeClass('display-none');
    }
    _i2e1Ques[next].displayTime = Date.now();
    return;
}

var _prepareQuestion = function (questionSection, question, currentQuestionIndex) {
    switch (question.answerType) {
        case 0:
        case 4:
        case 5:
            questionSection.append($('#internal-templates .answerType-' + question.answerType)[0].innerHTML);
            var currentQuestion = questionSection.find('.current');
            currentQuestion.addClass('question' + question.id);
            currentQuestion.find('input').attr('id', 'qt' + question.id).attr('name', 'answer-' + question.id).attr('value', question.answer);
            if (question.questionKey && question.questionKey.toLowerCase() == 'email') {
                var title = $('<div class="title">Enter Email Address</div>');
                currentQuestion.prepend(title);
                currentQuestion.find('input').attr('placeholder', 'eg. <EMAIL>');
                currentQuestion.find('label').attr('for', 'qt' + question.id).attr('id', 'label-' + question.id);
            } else {
                currentQuestion.find('label').attr('for', 'qt' + question.id).html(question.quesText);
            }
            currentQuestion.removeClass('current');
            break;
        case 1:
        case 2:
            questionSection.append($('#internal-templates .answerType-' + question.answerType)[0].innerHTML);
            var currentQuestion = questionSection.find('.current');
            currentQuestion.addClass('question' + question.id + '_' + question.journey);
            currentQuestion.find('.question-text').html(question.quesText + '<span class="required">*</span>');
            question.optionsOrder = [];
            question.remainingOptions = [];
            question.options.forEach(function (option, index) {
                question.options[index].displayIndex = index + 1;
                var p = $('<p question data-value journey class onclick=_mark(' + currentQuestionIndex + ',' + question.id + ',' + option.id + ',' + question.answerType + ')></p>');
                p.attr('question', question.id);
                p.attr('journey', question.journey);
                p.attr('data-value', option.id);
                p.attr('class', 'answer_' + option.id + '_' + currentQuestionIndex);
                if (option.image) {
                    p.html('<input class type="checkbox" id="option-' + option.id + '-' + currentQuestionIndex + '" name="answer-' + question.id + '-' + currentQuestionIndex + '" value="' + option.id + '" onclick=_mark(' + currentQuestionIndex + ',' + question.id + ',' + option.id + ',' + question.answerType + ') /><label for="option-' + option.id + '"><img class="content" src="' + option.image + '">' + option.text + '</label>');
                } else {
                    p.html('<input class type="checkbox" id="option-' + option.id + '-' + currentQuestionIndex + '" name="answer-' + question.id + '-' + currentQuestionIndex + '" value="' + option.id + '" onclick=_mark(' + currentQuestionIndex + ',' + question.id + ',' + option.id + ',' + question.answerType + ') /><label for="option-' + option.id + '">' + option.text + '</label>');
                }
                currentQuestion.find('.options-list').append(p);
            });
            if (question.journey > 0) {
                currentQuestion.append($('<input class="next-question" id="done_' + question.id + '_' + question.journey + '" type="button" onclick="done(' + currentQuestionIndex + ', ' + question.id + ', ' + question.journey + ')" value="Next" />'));
            } else {
                var next = currentQuestionIndex + 1;
                if (next < _i2e1Ques.length)
                    currentQuestion.append($('<input class="next-question" id="done_' + question.id + '_' + question.journey + '" type="button" onclick="done(' + currentQuestionIndex + ', ' + question.id + ', ' + question.journey + ')" value="Next" />'));
            }
            currentQuestion.removeClass('current');
            _associateAnswer(question);
            break;
        
        case 3:
            questionSection.append(_parserA3(question));
            if (question.options && question.options[0].image) {
                questionSection.find('.question.question' + question.id).addClass('img-options');
            }
            _associateAnswer(question);
            break;
    }
}

var _associateAnswer = function (ques) {
    var question = $(".question" + ques.id);
    switch (ques.answerType) {
        case 1:
            break;
        case 2:
            break;
    }

}

var _showConfirmButton = function () {
    $('#connect').addClass('display-none');
    $('#confirm').removeClass('display-none');
    $('#confirm').show();
}

var _showConnectButton = function () {
    $('#confirm').addClass('display-none');
    $('#connect').removeClass('display-none');
    $('#connect').show();
}

var _getQuestions = function (options, ajaxOptions) {
    var callback = null;
    if (options.questionType.indexOf(3) === 0) {
        var questionSection = $(options.stateSelector + ' .question-area.type-3');
        questionSection.addClass('display-none');
        callback = function (questions) {
            questionSection.empty();

            if (questions.length) {
                questionSection.removeClass('display-none');
                _logMPEvent(_i2e1Constants.firstState, { event: _i2e1Constants.questionAsked });
            }

            for (var i = 0; i < questions.length; i++) {
                questions[i].journey = 0;
                if (questions[i].answerType === 2 && questions[i].randomized) {
                    questions[i].options = shuffle(questions[i].options);
                }
                if (questions[i].answerType === 2 && questions[i].binarySplit) {
                    questions[i].journey = questions[i].options.length - 2;
                    // If more than two options then we need to do splitting
                    // If two or less options then we cant split into further journeys.
                    if (questions[i].options.length > 2) {
                        var x = i;
                        for (var o = 2; o <= questions[i].options.length; o++) {
                            var temp = questions[i];
                            temp.journey = temp.options.length - o;
                            questions = questions.splice(i+1, 0, temp);
                        }
                    }
                }
                if (questions[i].options.length > 0)
                    _prepareQuestion(questionSection, questions[i], i, questions.length);
            }
            if (options.callback) options.callback(questions);
        }
    } else {
        var questionSection = $(options.stateSelector + ' .question-area');
        questionSection.addClass('display-none');
        callback = function (questions) {
            if (window.DIY || (_viewBag.plans && _viewBag.plans.length)) {
                _showConnectButton();
            }
            if (questions.length) {
                _logMPEvent(_i2e1Constants.secondState, { event: _i2e1Constants.questionAsked });
                $('.question-area').addClass('display-none');
                $('.question').addClass('display-none');
                _showConfirmButton();
            } else {
                _showConnectButton();
            }

            options.questionType.forEach(function (quesType) {
                var questionSection = $(options.stateSelector + ' .question-area.type-' + quesType);
                questionSection.empty();
                if (questions.length) {
                    var ques = questions.filter(function (q) {
                        return q && q.quesType === quesType;
                    });
                    for (var i = 0; i < ques.length; i++) {
                        var end = false;
                        if (i === (ques.length - 1))
                            end = true;

                        ques[i].journey = 0;
                        if (ques[i].randomized) {
                            ques[i].options = shuffle(ques[i].options);
                        }
                        ques[i].journey = 0;
                        console.log(ques[i]);
                        var count = 0;
                        if (ques[i].answerType == 0 || ques[i].options == null) {
                            _i2e1Ques.push({
                                id: ques[i].id,
                                questionKey: ques[i].questionKey,
                                templateId: _loginUser.templateid[0] || 0,
                                journey: 0,
                                options: null,
                                remainingOptions: null,
                                answer: null,
                                optionsOrder: null,
                                randomized: false,
                                binarySplit: false,
                                quesType: ques[i].quesType,
                                answerType: ques[i].answerType,
                                question: ques[i],
                                index: _i2e1Ques.length - 1,
                                totalJourneys: 1
                            });
                        } else {
                            count = ques[i].options.length;
                            if (ques[i].binarySplit) {
                                ques[i].journey = count - 2;
                                // If more than two options then we need to do splitting
                                // If two or less options then we cant split into further journeys.
                                if (ques[i].options.length > 2) {
                                    for (var o = 2; o <= count; o++) {
                                        ques[i].journey = count - o;
                                        _i2e1Ques.push({
                                            id: ques[i].id,
                                            templateId: _loginUser.templateid[0] || 0,
                                            journey: ques[i].journey,
                                            options: ques[i].options,
                                            remainingOptions: [],
                                            answer: null,
                                            optionsOrder: [],
                                            randomized: ques[i].randomized,
                                            answerType: ques[i].answerType,
                                            question: ques[i],
                                            index: _i2e1Ques.length - 1,
                                            totalJourneys: count - 1
                                        });
                                    }
                                }
                                else {
                                    _i2e1Ques.push({
                                        id: ques[i].id,
                                        templateId: _loginUser.templateid[0] || 0,
                                        journey: ques[i].journey,
                                        options: ques[i].options,
                                        remainingOptions: [],
                                        answer: null,
                                        optionsOrder: [],
                                        randomized: ques[i].randomized,
                                        binarySplit: ques[i].binarySplit,
                                        quesType: ques[i].quesType,
                                        answerType: ques[i].answerType,
                                        question: ques[i],
                                        index: _i2e1Ques.length - 1,
                                        totalJourneys: 1
                                    });
                                }
                            } else {
                                _i2e1Ques.push({
                                    id: ques[i].id,
                                    templateId: _loginUser.templateid[0] || 0,
                                    journey: ques[i].journey,
                                    options: ques[i].options,
                                    remainingOptions: [],
                                    answer: null,
                                    optionsOrder: [],
                                    randomized: ques[i].randomized,
                                    binarySplit: ques[i].binarySplit,
                                    quesType: ques[i].quesType,
                                    answerType: ques[i].answerType,
                                    question: ques[i],
                                    index: _i2e1Ques.length - 1,
                                    totalJourneys: 1
                                });
                            }
                        }
                    }
                    for (var l = 0; l < _i2e1Ques.length; l++) {
                        _prepareQuestion(questionSection, _i2e1Ques[l].question, l, _i2e1Ques[l].totalJourneys);
                    }
                }
            });

            if (options.callback) options.callback(questions);
        }
    }

    return i2e1Api.getQuestions({
        questionType: options.questionType,
        mobile: options.mobile,
        callback: callback
    }, ajaxOptions);
}

var _voucherRedeem = function (id, scaleUp) {
    link = $('.campaign.campaign-' + id).data('link');
    if (link) {
        var append = link.indexOf('://') > 0 ? '' : 'https://';
        _doLogin({
            parameter: window.landingPage,
            url: append + link
        });
    }
    i2e1Api.updatePromotionReport({
        campaignId: id,
        clicked: true
    });
}

var _showVouchers = function (vouchers) {
    var voucherArea = $('.redirecting-state .campaigns');
    voucherArea.empty();
    var voucherHtml;
    for (var i = 0; i < vouchers.length; i++) {
        if (vouchers[i].voucherHTML){
            $('#skipVoucher1, #skipVoucher2').hide();
            voucherHtml = _parseVoucherCustomHTML(vouchers[i])
        }
        else
            voucherHtml = _parseVoucher(vouchers[i])
        voucherArea.append(voucherHtml);
        _logMPEvent(_i2e1Constants.offerState, { event: _i2e1Constants.offerShown, offerId: vouchers[i].id });
    }

    if (vouchers.length) {
        $('.redirecting-state > div').toggleClass('display-none');
    }
}

var _removeErrorClass = function (elem) {
    $(elem).parents('.question').find('.error').removeClass('error');
}

var _changeLanguage = function (language) {
    $.post("/Login/GetResources",{lp:language}, function(data){
        _viewBag = _viewBag || {};
        _viewBag.resources = data;
        $(".language-selectors a").show();
        $('.language-selectors').find('#'+ data.Culture).hide();
        _localise();
    });
    i2e1Api.logEvent("language_click_" + language, _loginUser.nasid, { mobile: _loginUser.mobile });
}

var _showPopup = function (msgHtml, okBtnText, onOk, onClose) {
    $('#popup-inner-html').html(msgHtml);
    $('#popup-ok-btn').text(okBtnText).off("click").click(function () {
        $('#generic-popup').hide();
        onOk && onOk();
    });
    $('#popup-close-btn').off('click').click(function () {
        $('#generic-popup').hide();
        onClose && onClose();
    });
    $('#generic-popup').show();
}

