body {
	font-family: sans-serif;
	margin: 0;
}

.display-none {
    display: none;
}

#loader {
    .deactivate {
        position: fixed; 
        width: 100%; 
        height: 100%;
        background-color: white; 
        top: 0;
        left: 0; 
        opacity: 0.6;
        z-index: 99;
    }

    .img-section {
        position: fixed; 
        left: 50%;
        top: 32%;
        z-index: 100;
        > div {
            position: relative; 
            left: -50%;
        }
    }
}

[type=button].primary, 
[type=submit].primary, 
button.primary {
	background-color: #4f4ff5;
	color: white;
	border: 0;
	cursor: pointer;
    &:hover {
        background-color: #3c3cef;
    }
}

input, button {
    font-size: 1rem;
	border-radius: 2px;
    padding: 1rem;
    height: 3rem;
    border: 1px solid #cccccc;
    box-shadow: 1px 1px 1px rgba(0, 0, 0, 0.1);
}

input{
    &:focus {
        &[type=number], 
        &[type=text], 
        &[type=password],
        &[type=email] {
        	outline: 0;
        }
    }

    &:hover {
        &[type=number], 
        &[type=text], 
        &[type=password],
        &[type=email] {
        	box-shadow: 1px 1px 1px rgba(0,0,0,0.2);
        }
    }
    
    [type=number]::-webkit-inner-spin-button, 
    [type=number]::-webkit-outer-spin-button { 
      -webkit-appearance: none; 
      margin: 0; 
    }

}
.left { float: left; }
.right { float: right; }
a { text-decoration: blink; }

img.pixelated {
  image-rendering: auto;
  image-rendering: crisp-edges;
}

.i2e1-coupon {
    text-align: justify;
    position: relative;

    .coupon-img {
        width: 4rem;
        max-height: 4rem;
    }
    .coupon-content {
        margin: 0;
        padding-left: 1.6rem;
        float: right;
        width: 14rem;
    }
}

.i2e1-card {
	display: block;
	background-color: rgba(222,222,222,0.16);
	box-shadow: 1px 1px 2px rgba(0,0,0,0.4);
	border-radius: 2px;
	padding: 1rem;
    margin: 1rem auto 0 auto;
    width: 20rem;

    &.no-background {
        background-color: transparent;
	    box-shadow: 0 0 0 transparent;
    }

    &.logged-in-state {
        margin: 0 auto 1rem auto;
    }

    form {
        margin-top: 4rem;
        position: relative;
        img {
            position: absolute;
            margin: 1rem 1rem;
            width: 1.2rem;
        }
        input {
            width: 100%;
            padding: 1rem 1rem 1rem 2.5rem;
        }
    }
}

.greetings {
    font-weight: bold;
}

.connected-message {
    font-size: 1.2rem;
    font-weight: bold;
}

.policy {
    margin: 1rem 0;
}

.logout {
    position:absolute;
    top:1rem;
    right:1rem; 
    width:1rem;
    image-rendering: auto;
    cursor:pointer;
        &:hover {
        width:1.1rem;
    }
}


