<html>
<head>
    <title>i2e1 Template Tester</title>
    <!--<meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0, user-scalable=no" />-->
    <style>
        .iframe {
            height: 30em;
            width: 25rem;
        }
        .landing-state.iframe {
            display: block;
            height: 60rem;
            width: 100%;
        }
    </style>
    <script type="text/javascript" src="track.js"></script>
</head>
<body onload="loaddata()">
   
    <iframe id="first-state" class="first-state iframe"> </iframe>
    <iframe id="second-state" class="second-state iframe"> </iframe>
    <iframe id="social-state" class="social-state iframe"></iframe>
    <iframe id="error-state-session-time-out" class="error-state iframe"> </iframe>
    <iframe id="error-state-data-over" class="error-state iframe"> </iframe>
    <iframe id="error-state-blocked" class="error-state iframe"> </iframe>
    
    <iframe id="voucher-state" class="voucher-state iframe"></iframe>
    <iframe id="redirecting-state" class="redirecting-state iframe"></iframe>
    <iframe id="landing-state" class="landing-state iframe"></iframe>
</body>

</html>
<script type="text/javascript">
    var fontSize = '60%';

    var firstStateWindow = document.getElementById("first-state");
    firstStateWindow.onload = function () {
        firstStateWindow.contentWindow.document.getElementsByTagName('html')[0].style['font-size'] = fontSize;
    }


    var secondStateWindow = document.getElementById("second-state");
    secondStateWindow.onload = function () {
        secondStateWindow.contentWindow.document.getElementsByTagName('html')[0].style['font-size'] = fontSize;
        secondStateWindow.contentWindow.document.getElementById("username").value = '2222222222';
        secondStateWindow.contentWindow.document.getElementById("get_otp") && secondStateWindow.contentWindow.document.getElementById("get_otp").click();
    }
    var sessionTimeError = document.getElementById("error-state-session-time-out");
    sessionTimeError.onload = function () {
        sessionTimeError.contentWindow.document.getElementsByTagName('html')[0].style['font-size'] = fontSize;
        sessionTimeError.contentWindow._swapState(_i2e1Constants.errorState, { errorType: 'time-exhausted', msg: "Your time limit is over", errorImg: "/images/session_time_out.png" });
    }

    var dataOverError = document.getElementById("error-state-data-over");
    dataOverError.onload = function () {
        dataOverError.contentWindow.document.getElementsByTagName('html')[0].style['font-size'] = fontSize;
        dataOverError.contentWindow._swapState(_i2e1Constants.errorState, { errorType: 'limit-exhausted', msg: "Your data limit is over", errorImg: "/images/limitExhausted.png" });
    }

    var blockedError = document.getElementById("error-state-blocked");
    blockedError.onload = function () {
        blockedError.contentWindow.document.getElementsByTagName('html')[0].style['font-size'] = fontSize;
        blockedError.contentWindow._swapState(_i2e1Constants.errorState, { errorType: 'user-blocked', msg: "Access is Blocked By Administrator", errorImg: "/images/not-authorized.png" });
    }

    var landingState = document.getElementById("landing-state");
    landingState.onload = function () {
        landingState.contentWindow._swapState(_i2e1Constants.landingState);
    }

    var socialStateWindow = document.getElementById("social-state");
    socialStateWindow.onload = function () {
        socialStateWindow.contentWindow.document.getElementsByTagName('html')[0].style['font-size'] = fontSize;
        socialStateWindow.contentWindow._viewBag.facebookPage = 'test facebook';
        socialStateWindow.contentWindow.document.getElementById('client_logo').src = "/images/facebook_share.png";
        socialStateWindow.contentWindow._preLogin();
    }

    var redirectingStateWindow = document.getElementById("redirecting-state");
    redirectingStateWindow.onload = function () {
        redirectingStateWindow.contentWindow.document.getElementsByTagName('html')[0].style['font-size'] = fontSize;
        redirectingStateWindow.contentWindow._swapState(_i2e1Constants.redirectionState);
    }

    var voucherStateWindow = document.getElementById("voucher-state");
    voucherStateWindow.onload = function () {
        voucherStateWindow.contentWindow.document.getElementsByTagName('html')[0].style['font-size'] = fontSize;
        if (voucherStateWindow.contentWindow.i2e1Api) {
            voucherStateWindow.contentWindow._viewBag.facebookPage = '';
            voucherStateWindow.contentWindow._setCookie("offers-shown", "");
            voucherStateWindow.contentWindow._preLogin();
        }
    }


    var loaddata = function () {
        var params = window.location.search.substring(1);
        params = params.split('&');
        var queryParams = {};
        for (var i = 0; i < params.length; i++) {
            queryParams[params[i].substring(0, params[i].indexOf('='))] = params[i].substring(params[i].indexOf('=')+1);
        }
        console.log(queryParams);

        var src = window.location.protocol + '//' + window.location.hostname + (window.location.port ? (':' + window.location.port) : '') + '/' + 
            "?res=logoff&" +
            "userid=" + queryParams.userid +
            "&uamip=********&uamport=80&challenge=30f8c06d61d8142d81fbdb394640fc13&" +
            "called=18-A6-F7-60-C5-E4&" +
            "mac=" + queryParams.mac +
            "&power=32&ip=********&" +
            "nasid=" + queryParams.nasid + "&" +
            "sessionid=" + queryParams.sessionid + "&" +
            "userurl=";

        

        firstStateWindow.src = src;
        secondStateWindow.src = src;
        sessionTimeError.src = src;
        dataOverError.src = src;
        blockedError.src = src;
        socialStateWindow.src = src;
        voucherStateWindow.src = src;
        redirectingStateWindow.src = src;


        var landingSrc = window.location.protocol + '//' + window.location.hostname + (window.location.port ? (':' + window.location.port) : '') + '/' +
            "NewLanding?res=success&utm_source=NAS-" + queryParams.nasid +
            "&nasid=" + queryParams.nasid +
            "&userid=" + queryParams.userid +
            "&macid=" + queryParams.mac +
            "&mode=login" +
            "&sessionid=" + queryParams.sessionid +
            "#/home/<USER>";

        landingState.src = landingSrc;
    }   

</script>

