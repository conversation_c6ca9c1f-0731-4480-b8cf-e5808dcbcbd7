window.onerror = function (message, source, lineno, colno, error) {
    $.ajax({
        type: "POST",
        url: "/Login/LogJsError",
        data: {
            message: message,
            source: source,
            lineno: lineno,
            colno: colno,
            error: btoa(error.stack),
            url: window.location.href || document.referrer
        }
    })
};

var _i2e1Domain = 'https://' + window.location.hostname + (window.location.port ? (':' + window.location.port) : '') + '/';
var _i2e1AjaxCounter = 0;

var getLogger = function (defaultData, domain) {
    return function (keyName, err = "", data) {
        // Define the request body
        data = Object.assign({}, data, defaultData);
        var requestBody = {
            keyName: keyName,
            eventData: data,
            uniqueIdentifier: data.uniqueIdentifier,
            sendToPostHog:true
        };

        if (err !== "") {
            requestBody.eventData.error = err;
        }

        // Define fetch options
        const requestOptions = {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(requestBody)
        };

        // Send the POST request
        fetch((domain ? domain : _i2e1Domain) + 'Login/SendCaptivePortalFrontendEvent', requestOptions)
            .then(response => response.json())
            .then(resp => {
                console.log(resp);
                if (resp.status == 0) {
                    console.log("SendCaptivePortalFrontendEvent success:", JSON.stringify(requestBody));
                }
                else {
                    console.log("SendCaptivePortalFrontendEvent error:", JSON.stringify(requestBody));
                }
            })
            .catch(err => console.log(err));
    }
}

var _roundoff = function (val) {
    return Math.floor(val * 100) / 100;
}

var _roundOffData = function (dataLeft) {
    if (!dataLeft) return 'NA';
    if (dataLeft >= 1073741824) //converting to GB
        dataLeft = _roundoff(dataLeft / 1073741824) + " GB";
    else if (dataLeft >= 1048576) //converting to MB
        dataLeft = _roundoff(dataLeft / 1048576) + " MB";
    else if (dataLeft >= 1024) //converting to MB
        dataLeft = _roundoff(dataLeft / 1024) + " KB";

    return dataLeft;
}

var _roundoffTime = function (totalSec) {
    var days = parseInt(totalSec / 86400);
    var hours = parseInt(totalSec / 3600) % 24;
    var minutes = parseInt(totalSec / 60) % 60;
    var seconds = totalSec % 60;
    var result = (days >= 1 ? days + (days === 1 ? ' day ' : ' days ') : '') +
                 (hours >= 1 ? hours + (hours === 1 ? ' hr ' : ' hrs ') : '') +
                 (minutes >= 1 ? minutes + (minutes === 1 ? ' min' : ' mins') : '');
    return result ? result : 'less than a minute';
}

var _getUrlParams = function() {
    return _loginUser;
}

var _searchObjectInArray = function(nameKey, nameValue, myArray) {
    for (var i = 0; i < myArray.length; i++) {
        if (myArray[i][nameKey] === nameValue) {
            return myArray[i];
        }
    }
}

var autoLogin = false;
var _makei2e1Login = function (loginResponse, landingPage) {
    var form = document.createElement('form');
    var url = loginResponse.url.indexOf('?') == -1 ? loginResponse.url + "?" : loginResponse.url + "&";

    autoLogin = loginResponse.isAutoLogin;

    for (var i = 0; i < loginResponse.parameters.length; ++i) {

        if (landingPage && (loginResponse.parameters[i].name === "url" || loginResponse.parameters[i].name === "userurl"))
            var value = landingPage;
        else
            var value = loginResponse.parameters[i].value;

        if (window.location.hostname === 'localhost' && (loginResponse.parameters[i].name === "url" || loginResponse.parameters[i].name === "userurl")) {
            url = loginResponse.parameters[i].value;
        } else {
            var input = document.createElement('input');
            input.setAttribute('name', loginResponse.parameters[i].name);
            input.setAttribute('value', value);
            form.appendChild(input);
            url += loginResponse.parameters[i].name + "=" + encodeURIComponent(value) + "&";
        }
    }

    if (window.location.href.indexOf('doTest') > -1 || window.location.href.indexOf("PurchasePlan") > -1) {
        var obj = _searchObjectInArray('name', 'userurl', loginResponse.parameters);
        window.location.href = obj.value;
    }
    else {

        if (window.location.protocol === "https:") {
            window.location.href = url;
        } else if (loginResponse.isGet || window.location.hostname === 'localhost') {
            window.location.href = url;
        } else {
            form.attr('action', loginResponse.url);
            form.attr('method', 'post');
            var input = $('<input>').attr('type', 'submit');
            form.append(input);
            $('body').append(form);
            form.submit();
        }
    }
}

var _loginSuccessFullEvent = function (url, time) {
    var _properties = {};
    if (!autoLogin) {
        _properties.event = 'Redirecting shortly';
        _properties.state = 'Login Successful';
    } else {
        _properties.event = 'AL Redirecting shortly';
        _properties.state = 'AL Successful';
    }
    
    _logMPEvent(_properties);
    setTimeout(function () {
        window.location.href = url;
    }, time || 5000);
}


var i2e1Api = {
    initialize : function(login, success){
        var params = _getUrlParams();
        switch (params.res) {
            case 'notyet':
            case 'failed':
                login();
                break;
            case 'success':
            case 'already':
                success();
        }
    },
    doAjax: function (url, data, onSuccess, onFailure, ajaxOptions) {
        ajaxOptions = ajaxOptions || {};

        if (!ajaxOptions.hideLoading) {
            _i2e1AjaxCounter++;
            $('#loader').removeClass('display-none');
        }

        var hideLoader = function () {
            if (!ajaxOptions.hideLoading && _i2e1AjaxCounter > 0) {
                _i2e1AjaxCounter--;
                _i2e1AjaxCounter <= 0 && $('#loader').addClass('display-none');
            }
        }

        var params = _getUrlParams();
        var rand = Math.random();
        if (url.indexOf('?') === -1)
        {
            url += '?login-user-session=' + (params.sessionid) + '&' + rand;
        }
        else {
            url += '&login-user-session=' + (params.sessionid) + '&' + rand;
        } 
        return $.ajax({
            type: "POST",
            url: url,
            data: JSON.stringify(data),
            contentType: "application/json; charset=utf-8",
            dataType: "json",
            success: function (data) {
                hideLoader();
                if (onSuccess) onSuccess(data);
            }
        }).error(function (obj, status, errMsg) {
            hideLoader();
            if (onFailure) onFailure({ errMsg: errMsg });
        });
    },
    generateOTP: function(mobile, options){
        var params = _getUrlParams();
        var options = options || {};
        console.log('generating otp...');
        var api = '/Login/GenerateOTP/';
        mobile.indexOf('+91') === 0 ? options.globalOTP = false : (options.globalOTP = options.globalOTP);
        options.globalOTP ? api = '/Login/GenerateGlobalOTP/' : (api = api);

        i2e1Api.doAjax(api, {
            user1: {
                mobile: mobile,
                macId: params.mac,
                routerId: params.nasid,
                routerMac: params.called,
                challenge: params.challenge,
                nasid: params.nasid,
                attributes: options.attributes,
                smsApi: options.isresend ? 'change' : null,
                clientAuthType: options.clientAuthType || 0
            },
            questions: options.questions
        }, function (response) {
            var url=response.msg;
            console.log(url);
            if(url && url.indexOf("openid+mc_mnv_validate+mc_identity")>-1){
                window.location=url;
            }
            else if (response.status === 0) {
                if (options.onSuccess)
                    options.onSuccess(response);
                else
                    _makei2e1Login(response.data.otpResponse.landingPage);
            }
            else {
                if (options.onFailure)
                    options.onFailure(response);
            }
        }
        , options.onFailure);
    },
    submitOTP: function(mobile, otp, options){
        var params = _getUrlParams();
        i2e1Api.doAjax('/Login/SubmitOTP/', {
            user1: {
                mobile: mobile,
                macId: params.mac,
                routerId: params.nasid,
                challenge: params.challenge,
                otp: otp,
                name: options.name,
                clientAuthType: options.clientAuthType || 0
            },
            questions: options.questions,
            captcha: options.captcha,
            accessCode: options.accessCode,
            doLogin: options.doLogin === null ? true : options.doLogin
        }, function (response) {
            if (response.status === 0) {
                if (options.onSuccess)
                    options.onSuccess(response);
                else
                    _makei2e1Login(response.data.landingPage);
            }
            else {
                if (options.onFailure)
                    options.onFailure(response);
            }
        }, function (response) {
            if (options.onFailure && response.data)
                options.onFailure(response.data);
            else
                options.onFailure(response);
        });
    },
    submitPlan: function (mobile, planId, options) {
        var params = _getUrlParams();
        i2e1Api.doAjax('/Login/SubmitPlan/', {
            user1: {
                mobile: mobile,
                macId: params.mac,
                routerId: params.nasid,
                challenge: params.challenge,
                name: options.name,
                clientAuthType: options.clientAuthType || 0
            },
            captcha: options.captcha,
            accessCode: options.accessCode,
            planId: planId || 0
        }, function (response) {
            if (response.status === 0) {
                if (options.onSuccess)
                    options.onSuccess(response);
                else
                    _makei2e1Login(response.data.landingPage);
            }
            else {
                if (options.onFailure)
                    options.onFailure(response);
            }
        }, function (response) {
            if (options.onFailure && response.data)
                options.onFailure(response.data);
            else
                options.onFailure(response);
        });
    },
    doUserLogin: function (options) {
        var options = options || {};
        i2e1Api.doAjax('/Login/DoUserLogin/', {}, function (response) {
            if (response.status === 0) {
                if (options.onSuccess)
                    options.onSuccess(response);
                else
                    _makei2e1Login(response.data.landingPage);
            }
            else {
                if (options.onFailure)
                    options.onFailure(response);
            }
        }, function (response) {
            if (options.onFailure && response.data)
                options.onFailure(response.data);
            else
                options.onFailure(response);
        });
    },
    submitToken: function (token, provider, options) {
        var params = _getUrlParams();
        i2e1Api.doAjax('/Login/SubmitToken', {
            code: token,
            provider: provider
        }, function (response) {
            if (response.status === 0) {
                if (options.onSuccess)
                    options.onSuccess(response);
                else
                    _makei2e1Login(response.data.landingPage);
            }
            else {
                if (options.onFailure)
                    options.onFailure(response);
            }
        }, function (response) {
            if (options.onFailure && response.data)
                options.onFailure(response.data);
            else
                options.onFailure(response);
        });
    },
    getAttributes: function (options) {
        i2e1Api.doAjax('/Login/GetUserAttributes', {}, options.onSuccess, options.onFailure, { hideLoading: true });
    },
    getPDOList: function (options) {
        i2e1Api.doAjax('/Login/GetPDOList', {
            nasid: options.nasid
        }, options.onSuccess, options.onFailure, { hideLoading: true });
    },
    logout: function (options) {
        i2e1Api.doAjax('/Login/Logout', {}, function () {
            if (options.callback) options.callback();
            else window.location.reload();
        });
    },
    getQuestions: function (options, ajaxOptions) {
        var params = _getUrlParams();
        var handler = function (response) {
            if (response.data && response.data.length) {
                options.callback(response.data);
            } else {
                options.callback([]);
            }
        };
        return i2e1Api.doAjax('/Login/GetQuestions', {
            questionTypes: options.questionType,
            mobile: options.mobile || params.userid || params.mobile,
            routerId: params.nasid
        }, handler, function (data) {
            jsErrorLogger("getQuestions error", options.mobile, data.errMsg);
        }, ajaxOptions);
    },
    doSocialLogin: function (options) {
    
    },
    getLandingPage: function (options) {
        var params = _getUrlParams();
        i2e1Api.doAjax('/Login/GetLandingPage', {
            nasid: params.nasid
        }, function (response) {
            options.onSuccess(response)
        });
    },
    submitAnswers: function (mobile, options) {
        var params = _getUrlParams();
        i2e1Api.doAjax('/Login/SubmitAnswers', {
            user1: {
                mobile: mobile,
                macId: params.mac,
                routerId: params.nasid
            },
            questions: options.questions
        }, function (response) {
            options.onSuccess && options.onSuccess(response)
        });
    },
    updatePromotionReport: function (options) {
        i2e1Api.doAjax('/Kelp/UpdatePromotionReport', {
            campaignId: options.campaignId,
            clicked: options.clicked,
            skipped: options.skipped,
            secondsWatched: options.secondsWatched
        }, function (response) {

        });
    },
    verifyDIYMAC: function (mac, user, options) {
        i2e1Api.doAjax('/DeviceConfig/DIYRegistration', {
            deviceMac: mac
        }, function (response) {
            if (response.status === 0) {
                if (options.onSuccess)
                    options.onSuccess(response);
            }
            else {
                if (options.onFailure)
                    options.onFailure(response);
            }
        }, options.onFailure);
    },
    generateOTPForRegistration: function (mobile, options) {
        var params = _getUrlParams();
        var options = options || {};
        console.log('generating diy otp...');
        var api = '/DeviceConfig/GenerateDIYOTP/';

        i2e1Api.doAjax(api, {
            mobile: mobile,
            routerId: params.nasid,
            nasid: params.nasid,
            smsApi: options.isresend ? 'change' : null
        }, function (response) {
            var url = response.msg;
            console.log(url);
            if (response.status === 0) {
                if (options.onSuccess)
                    options.onSuccess(response);
            }
            else {
                if (options.onFailure)
                    options.onFailure(response);
            }
        }, options.onFailure);
    },
    submitOTPForRegistration: function (mobile, otp, options) {
        var params = _getUrlParams();
        var options = options || {};
        console.log('generating diy otp...');
        var api = '/DeviceConfig/SubmitDIYOTP/';

        i2e1Api.doAjax(api, {
            mobile: mobile,
            otp: otp,
            routerId: params.nasid,
            nasid: params.nasid,
            smsApi: options.isresend ? 'change' : null
        }, function (response) {
            var url = response.msg;
            console.log(url);
            if (response.status === 0) {
                if (options.onSuccess)
                    options.onSuccess(response);
            }
            else {
                if (options.onFailure)
                    options.onFailure(response);
            }
        }, options.onFailure);
    },
    submitDetailsForRegistration: function (deviceId, shopName, address, pincode, email, user, options) {
        var params = _getUrlParams();
        options = options || {};
        console.log('generating diy otp...');
        var api = '/DeviceConfig/SubmitDIYREG/';

        i2e1Api.doAjax(api, {
            deviceId: deviceId,
            user: user,
            shopName: shopName,
            address: address,
            pincode: pincode,
            email: email
        }, function (response) {
            var url = response.msg;
            console.log(options, response);
            if (response.status === 0) {
                if (options.onSuccess)
                    options.onSuccess(response);
            }
            else {
                if (options.onFailure)
                    options.onFailure(response);
            }
        }, options.onFailure);
    },
    resendDIYCredentials: function (user, options) {
        var params = _getUrlParams();
        var options = options || {};
        var api = '/DeviceConfig/ResendDIYCredentials';
        i2e1Api.doAjax(api, {
            user: user
        }, function (response) {
            var url = response.msg;
            console.log(url);
            if (response.status === 0) {
                if (options.onSuccess)
                    options.onSuccess(response);
            }
            else {
                if (options.onFailure)
                    options.onFailure(response);
            }
        }, options.onFailure);
    },
    checkOTP: function (mobile, otp, options) {
        var params = _getUrlParams();
        if (!otp)
            console.error("Missing OTP while Checking OTP");
        i2e1Api.doAjax('/Login/CheckOTP/', {
            user1: {
                mobile: mobile,
                macId: params.mac,
                routerId: params.nasid,
                challenge: params.challenge,
                otp: otp,
                name: options.name,
                clientAuthType: options.clientAuthType || 0
            },
            accessCode: options.accessCode,
            captcha: options.captcha,
            doLogin: false
        }, function (response) {
            if (response.status === 0) {
                if (options.onSuccess)
                    options.onSuccess(response);
            }
            else {
                if (options.onFailure)
                    options.onFailure(response);
            }
        }, function (response) {
            if (options.onFailure && response.data)
                options.onFailure(response.data);
            else
                options.onFailure(response);
        });
    },
    notifyOperator: function (plan, options) {
        var params = _getUrlParams();
        var options = options || {};
        var api = '/Login/NotifyOperator/';
        plan.nasid = params.nasid;
        plan.payMode = options.payMode || 'cash';
        plan.upiId = options.upiId;
        plan.isMobileOrTablet = mobileAndTabletCheck();
        i2e1Api.doAjax(api, plan, function (response) {
            if (response.status === 0) {
                if (options.onSuccess)
                    options.onSuccess(response);
            } else {
                if (options.onFailure)
                    options.onFailure(response);
            }
        }, options.onFailure);
    },
    getOperator: function (callback) {
        i2e1Api.doAjax('/Login/GetMerchantDetails', {}, function (response) {
            callback(response.data);
        });
    },
    getPlanMacMapping: function (callback) {
        i2e1Api.doAjax('/Login/GetPlanMacMapping', {}, function (response) {
            callback(response.data);
        });
    },
    logEvent: function (eventLabel, eventValue, attributes) {
        attributes = attributes || {};
        attributes.sessionid = window._loginUser && _loginUser.sessionid;
        //return $.ajax({
        //    type: "POST",
        //    url: "https://stats.i2e1.in:444/Stats/Log",
        //    data: JSON.stringify({
        //        "eventLabel": eventLabel,
        //        "eventValue": eventValue,
        //        "attributes": JSON.stringify(attributes),
        //        "project": "i2e1-login"
        //    }),
        //    contentType: "application/json; charset=utf-8",
        //    dataType: "json",
        //    timeout: 20 * 1000,
        //    success: function (data) {
        //    }
        //}).error(function (obj, status, errMsg) {
        //});
    }
}

function getBrowserName() {
    var isChrome = !!window.chrome && !!window.chrome.webstore;
    if (isChrome)
        return 'CHROME';

    var isOpera = (!!window.opr && !!opr.addons) || !!window.opera || navigator.userAgent.indexOf(' OPR/') >= 0;
    if (isOpera)
        return 'OPERA';

    var isFirefox = typeof InstallTrigger !== 'undefined';
    if (isFirefox)
        return 'FIREFOX';

    var isSafari = /constructor/i.test(window.HTMLElement) || (function (p) { return p.toString() === "[object SafariRemoteNotification]"; })(!window['safari'] || safari.pushNotification);
    if (isSafari)
        return 'SAFARI';

    var isIE = /*@cc_on!@*/false || !!document.documentMode;
    if (isIE)
        return 'IE';

    var isEdge = !isIE && !!window.StyleMedia;
    if (isEdge)
        return 'EDGE';

    var isBlink = (isChrome || isOpera) && !!window.CSS;
    if (isBlink)
        return 'BLINK';
}

function mobileAndTabletCheck() {
    var check = false;
    (function (a) { if (/(android|bb\d+|meego).+mobile|avantgo|bada\/|blackberry|blazer|compal|elaine|fennec|hiptop|iemobile|ip(hone|od)|iris|kindle|lge |maemo|midp|mmp|mobile.+firefox|netfront|opera m(ob|in)i|palm( os)?|phone|p(ixi|re)\/|plucker|pocket|psp|series(4|6)0|symbian|treo|up\.(browser|link)|vodafone|wap|windows ce|xda|xiino|android|ipad|playbook|silk/i.test(a) || /1207|6310|6590|3gso|4thp|50[1-6]i|770s|802s|a wa|abac|ac(er|oo|s\-)|ai(ko|rn)|al(av|ca|co)|amoi|an(ex|ny|yw)|aptu|ar(ch|go)|as(te|us)|attw|au(di|\-m|r |s )|avan|be(ck|ll|nq)|bi(lb|rd)|bl(ac|az)|br(e|v)w|bumb|bw\-(n|u)|c55\/|capi|ccwa|cdm\-|cell|chtm|cldc|cmd\-|co(mp|nd)|craw|da(it|ll|ng)|dbte|dc\-s|devi|dica|dmob|do(c|p)o|ds(12|\-d)|el(49|ai)|em(l2|ul)|er(ic|k0)|esl8|ez([4-7]0|os|wa|ze)|fetc|fly(\-|_)|g1 u|g560|gene|gf\-5|g\-mo|go(\.w|od)|gr(ad|un)|haie|hcit|hd\-(m|p|t)|hei\-|hi(pt|ta)|hp( i|ip)|hs\-c|ht(c(\-| |_|a|g|p|s|t)|tp)|hu(aw|tc)|i\-(20|go|ma)|i230|iac( |\-|\/)|ibro|idea|ig01|ikom|im1k|inno|ipaq|iris|ja(t|v)a|jbro|jemu|jigs|kddi|keji|kgt( |\/)|klon|kpt |kwc\-|kyo(c|k)|le(no|xi)|lg( g|\/(k|l|u)|50|54|\-[a-w])|libw|lynx|m1\-w|m3ga|m50\/|ma(te|ui|xo)|mc(01|21|ca)|m\-cr|me(rc|ri)|mi(o8|oa|ts)|mmef|mo(01|02|bi|de|do|t(\-| |o|v)|zz)|mt(50|p1|v )|mwbp|mywa|n10[0-2]|n20[2-3]|n30(0|2)|n50(0|2|5)|n7(0(0|1)|10)|ne((c|m)\-|on|tf|wf|wg|wt)|nok(6|i)|nzph|o2im|op(ti|wv)|oran|owg1|p800|pan(a|d|t)|pdxg|pg(13|\-([1-8]|c))|phil|pire|pl(ay|uc)|pn\-2|po(ck|rt|se)|prox|psio|pt\-g|qa\-a|qc(07|12|21|32|60|\-[2-7]|i\-)|qtek|r380|r600|raks|rim9|ro(ve|zo)|s55\/|sa(ge|ma|mm|ms|ny|va)|sc(01|h\-|oo|p\-)|sdk\/|se(c(\-|0|1)|47|mc|nd|ri)|sgh\-|shar|sie(\-|m)|sk\-0|sl(45|id)|sm(al|ar|b3|it|t5)|so(ft|ny)|sp(01|h\-|v\-|v )|sy(01|mb)|t2(18|50)|t6(00|10|18)|ta(gt|lk)|tcl\-|tdg\-|tel(i|m)|tim\-|t\-mo|to(pl|sh)|ts(70|m\-|m3|m5)|tx\-9|up(\.b|g1|si)|utst|v400|v750|veri|vi(rg|te)|vk(40|5[0-3]|\-v)|vm40|voda|vulc|vx(52|53|60|61|70|80|81|83|85|98)|w3c(\-| )|webc|whit|wi(g |nc|nw)|wmlb|wonu|x700|yas\-|your|zeto|zte\-/i.test(a.substr(0, 4))) check = true; })(navigator.userAgent || navigator.vendor || window.opera);
    return check;
};