@smartphones: ~"only screen and (min-width: 361px) and (max-width: 540px)";
@iPhone5: ~"only screen and (max-width: 360px)";
@tablets: ~"only screen and (min-width: 540px) and (max-width: 1080px)";

@ldpi: ~"only screen and (max-width: 270px)";
@mdpi: ~"only screen and (min-width: 271px) and (max-width: 360px)";
@hdpi: ~"only screen and (min-width: 361px) and (max-width: 540px)";
@xxhdpi: ~"only screen and (min-width: 1080px)";
@xxxhdpi: ~"only screen and (max-width: 1440px)";

@font-face {
    font-family: 'Roboto';
    src: url('../../jsLibs/fonts/Roboto/Roboto-Regular-webfont.eot');
    src: url('../../jsLibs/fonts/Roboto/Roboto-Regular-webfont.eot?#iefix') format('embedded-opentype'),
         url('../../jsLibs/fonts/Roboto/Roboto-Regular-webfont.woff') format('woff'),
         url('../../jsLibs/fonts/Roboto/Roboto-Regular-webfont.ttf') format('truetype'),
         url('../../jsLibs/fonts/Roboto/Roboto-Regular-webfont.svg#RobotoRegular') format('svg');
    font-weight: normal;
    font-style: normal;
}

@font-face {
    font-family: 'Roboto-Medium';
    src: url('../../jsLibs/fonts/Roboto/Roboto-Medium-webfont.eot');
    src: url('../../jsLibs/fonts/Roboto/Roboto-Medium-webfont.eot?#iefix') format('embedded-opentype'),
        url('../../jsLibs/fonts/Roboto/Roboto-Medium-webfont.woff') format('woff'),
        url('../../jsLibs/fonts/Roboto/Roboto-Medium-webfont.ttf') format('truetype'),
        url('../../jsLibs/fonts/Roboto/Roboto-Medium-webfont.svg#RobotoRegular') format('svg');
    font-weight: normal;
    font-style: normal;
}

@card-background-color: rgba(255, 255, 255, 0.8);
@form-width: 20rem;
@form-width-smartphone: 18rem;
@common-shadow: 1px 1px 2px rgba(0, 0, 0, 0.4);
@text-shadow: 1px 1px 0 rgba(0, 0, 0, 0.4);
@placeholder-grey: #757575;
@login-container-padding: 1rem;
@input-border-bottom: 1px solid #f4f4f4;
@body-grey: #f3f3f3;
@i2e1-blue: rgba(12, 75, 158, .9);
@font-size-standard: 1rem;
@font-size-field-superscript: .8rem;
@font-size-phone-number: 0.9rem;
@font-weight-phone-number: 600;
@font-size-substandard: .8rem;
@font-color-grey: #999999;
@font-color-common: #484848;
@i2e1-stepper-visited: #16befd;

@wiom-cta-gradient: linear-gradient(180deg, #f4693a, #ef4f82);
@wiom-cta-highlight: #f4693a;
@wiom-gradient: linear-gradient(180deg, #f4693a, #ef4f82);
@wiom-stepper-visited: #f4693a;

::placeholder {
    color: #bcbcbc;
    font-weight: 500;
}

:-ms-input-placeholder { /* Internet Explorer 10-11 */
    color: #bcbcbc;
    font-weight: 500;
}

::-ms-input-placeholder { /* Microsoft Edge */
    color: #bcbcbc;
    font-weight: 500;
}

.partition {
    position: relative;
    margin: 1rem 0;

    .left-part, .right-part {
        border-bottom: 1.5px dashed rgba(140,133,133,0.8);
        width: 46%;
        top: 50%;
        position: absolute;
    }

    .left-part {
        left: 0;
    }

    .right-part {
        right: 0;
    } 
}


html 
{ 
    font-size: 100%;
    height:100%;
    @media @iPhone5 {
        font-size: 82%;
    }
    
}
body {
    font-family: 'Roboto', Fallback, sans-serif !important;
    letter-spacing: .3px;
    margin: 0;
    padding: 0;
    overflow-y: auto;
    overflow-x: hidden;
    text-align: center;
    background-color: @body-grey;
    color: @font-color-common;
    width: 100%;

    .fs-12 {
        font-size: 0.75rem;
    }

    .fs-14 {
        font-size: 0.875rem
    }

    .fw-700 {
        font-weight: 700;
    }

    .light-grey {
        color: #7d7d7d;
    }

    &.landing {
        .login.container {
            background-color: #ffffff !important;

            .outer-card1 {
                > img:not(.wiom-logo) {
                    height: 80px !important;
                }
            }

            form.landing-state {
                margin-top: -3rem;
                max-width: 22rem;
            }

            .customer-profile-state {
                .current-plan-div {
                    margin-bottom: 0.5rem;
                }

                .balance-div {
                    display: flex;
                    justify-content: space-between;
                    padding: 0 1rem;
                    text-align: center;
                    margin-bottom: 1.5rem;
                }

                .tabs {
                    .payment-tab {
                        display: inline-block;
                        width: 50%;
                        float: left;
                        height: 1.5rem;
                        cursor: pointer;
                        position: sticky;
                        top: 0;
                        background: white;
                        box-shadow: 0 2px 6px #9f9f9f;

                        p {
                            margin: 0;
                            padding-top: 0.25rem;
                        }
                    }

                    .usage-tab {
                        display: inline-block;
                        width: 50%;
                        float: right;
                        height: 1.5rem;
                        cursor: pointer;
                        position: sticky;
                        top: 0;
                        background: white;
                        box-shadow: 0 2px 6px #9f9f9f;

                        p {
                            margin: 0;
                            padding-top: 0.25rem;
                        }
                    }

                    .active {
                        background: #f06b69;
                        color: white;
                    }

                    thead {
                        color: white;
                        font-size: 0.75rem;
                        display: table;
                        width: 100%;
                        background: #214294;

                        tr {
                            height: 1.5rem;

                            th {
                                font-weight: normal;
                            }
                        }
                    }

                    .payment-body, .usage-body {
                        font-size: 0.75rem;
                        max-height: 15rem;
                        overflow-y: scroll;
                        display: block;
                        width: 100%;
                        scroll-behavior: smooth;

                        tr {
                            width: 100%;
                            display: table;
                            box-sizing: border-box;

                            td {
                                text-align: center;
                            }

                            @media @iPhone5 {
                                td:first-child {
                                    font-size: 0.6rem;
                                }
                            }
                        }
                    }
                }
            }
        }

        .footer {
            display: none;
        }
    }
}

.truncate {
    width: calc(100% - 4vw);
    text-overflow: ellipsis;
    white-space: nowrap;
    overflow: hidden;
    margin: 0.5rem auto;
    max-width: 7rem;
}

input[type=number]::-webkit-inner-spin-button, 
input[type=number]::-webkit-outer-spin-button { 
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
    margin: 0; 
}



input::-ms-clear {
    display: none;
    width: 0;
    height: 0;
}

.saturate {-webkit-filter: saturate(3); filter: saturate(3);}
.grayscale {-webkit-filter: grayscale(100%); filter: grayscale(100%);}
.contrast {-webkit-filter: contrast(160%); filter: contrast(160%);}
.brightness {-webkit-filter: brightness(0.25); filter: brightness(0.25);}
.blur {-webkit-filter: blur(3px); filter: blur(3px);}
.invert {-webkit-filter: invert(100%); filter: invert(100%);}
.sepia {-webkit-filter: sepia(100%); filter: sepia(100%);}
.huerotate {-webkit-filter: hue-rotate(180deg); filter: hue-rotate(180deg);}
.rss.opacity {-webkit-filter: opacity(50%); filter: opacity(50%);}

.mac_octet {
    width: 2rem !important;
    margin-right: 3px;
    font-size: 1.2rem !important;
    background-color: #eee !important;
    border-radius: 5px !important;
    height: 3rem !important;
    text-align: center;
    box-shadow: inset 0 0 5px 4px #ddd;
}

.circle {
    border: 0.5rem solid #F6F6F6;
    border-radius: 100rem;  
    box-shadow: @common-shadow;
    display: inline-block;
    position: relative;
    height: 5rem;
    width: 5rem;
    
    @media @smartphones {
        font-size: 1rem;
    }

    .content {
        position: absolute;
        width: 100%;
        height:100%;
        text-align: center;
        display: table;

        span.data {
            display: table-cell;
            vertical-align: middle;
            font-weight: bold;
        }
    }
}

.square {
    border: 0.5rem solid #F6F6F6;
    box-shadow: @common-shadow;
    display: inline-block;
    position: relative;
    height: 5rem;
    width: 5rem;
    
    @media @smartphones {
        font-size: 1rem;
    }

    .content {
        position: absolute;
        width: 100%;
        height:100%;
        text-align: center;
        display: table;

        span.data {
            display: table-cell;
            vertical-align: middle;
            font-weight: bold;
        }
    }
}

#loader, .loader {
    .deactivate {
        position: fixed; 
        width: 100%; 
        height: 100%;
        background-color: white; 
        top: 0;
        left: 0; 
        opacity: 0.1;
        z-index: 99;
    }

    .img-section {
        position: fixed; 
        left: 50%;
        top: 40%;
        z-index: 100;
        > div {
            position: relative; 
            left: -50%;
        }
    }
}
button, input[type=button] {
    cursor: pointer;
}
[type=button].primary,
[type=submit].primary,
button.primary {
    background: @wiom-cta-highlight;
    background: @wiom-cta-gradient;
    color: white;
    border: 0;
    cursor: pointer;
    font-size: 1rem;
    border-radius: 3px;

    &:hover {
        background: @wiom-cta-gradient;
    }

    &[disabled], &:disabled {
        background-color: #f68888 !important;
        background: #f68888 !important;
        pointer-events: none;
    }
}
button.wiom_login_button, input.wiom_login_button {
    background: @wiom-cta-gradient;
    height: auto;

    &:hover {
        background: @wiom-cta-highlight;
    }
}

input[type=number],
input[type=text],
input[type=password],
input[type=email],
input[type=tel] {
    box-sizing: border-box;
    -moz-box-sizing: border-box;
    -webkit-box-sizing: border-box;
    font-size: 1.5rem;
    padding: 0 0.1rem;
    height: 3rem;
    border-bottom: none;
    border-top: none;
    border-right: none;
    border-left: none;
    -webkit-appearance: none;

    &:required {
        outline: none;
    }

    &:focus, &.focus {
        outline: none;
        border-bottom: none;
        background-color: transparent;
    }

    &:visited {
        background-color: transparent;
    }
}

input[type=submit], 
input[type=button], 
button {
    box-sizing: border-box;
   -moz-box-sizing: border-box;
   -webkit-box-sizing: border-box;
    font-size: 1rem;
    padding: 1rem;
    height: 3rem;
    box-shadow: @common-shadow;
    -webkit-appearance: none;
}

input {
    &:focus {
        &[type=number], 
        &[type=text], 
        &[type=password],
        &[type=email] {
        	outline: 0;
        }
    }

    &:hover {
        &[type=number], 
        &[type=text], 
        &[type=password],
        &[type=email] {
        	//box-shadow: @common-shadow;
        }
    }
    
    [type=number]::-webkit-inner-spin-button, 
    [type=number]::-webkit-outer-spin-button { 
      -webkit-appearance: none; 
      margin: 0; 
    }

}

@-webkit-keyframes shaker {
  10%, 90% {
    transform: translate3d(-1px, 0, 0);
  }
  
  20%, 80% {
    transform: translate3d(2px, 0, 0);
  }

  30%, 50%, 70% {
    transform: translate3d(-4px, 0, 0);
  }

  40%, 60% {
    transform: translate3d(4px, 0, 0);
  }
}

.shake {
    -webkit-animation-name: shaker;
	-webkit-animation-duration: 0.8s;
	-webkit-transform-origin:50% 50%;
	-webkit-animation-iteration-count: 2s;
	-webkit-animation-timing-function: linear;
}



a { text-decoration: blink; cursor: pointer; }

img.pixelated {
  image-rendering: auto;
  image-rendering: crisp-edges;
  margin: 10px 10px 0px 0px;
}

.display-none {
    display: none;
}

#i2e1-iframe {
	width: 100%;
	height: 100%;
	border: none;
}

.card {
    border-radius: 2px;
    background-color: @card-background-color !important;
    padding: @login-container-padding;

    form {
        text-align: center;
        margin-bottom: 0;
    }

    hr {
        width: @form-width;
        border: none;
        border-bottom: 1px solid rgba(204, 204, 204, 0.48);
        
        @media @smartphones {
            width: 16rem;
        }
    }
}

.material-input {
    font-size: @font-size-phone-number;
    border-bottom: 2px solid #d4d4d4;
    padding-bottom: .5rem;

    .title {
        text-align: left;
        font-size: 1rem;
        font-weight: 600;
        margin-bottom: 1rem;

        &.mandatory {
            &::after {
                content: '*';
                color: red;
                top: -3px;
                position: relative;
            }
        }
    }

    .group {
        position: relative;
        text-align: center;

        label {
            outline: none;
            position: absolute;
            transition: .25s ease;
            -webkit-transition: .25s ease;
            -moz-transition: .25s ease;
            -o-transition: .25s ease;
            color: #999999;
            left: 0;
            content: 'Enter phone number';
            top: 1.2rem;

            &.focus {
                font-size: @font-size-field-superscript;
                color: #2196f3;
            }
        }

        label.diy {
            top: 0px !important;
        }

        label#label-username, label[for=username] {
            top: 1.8rem;
        }

         input {
            background: transparent;
            font-family: 'Roboto', Fallback, sans-serif !important;
            font-size: @font-size-phone-number;
            font-weight: 600;

            &:focus {
                & ~ label {
                    font-size: @font-size-field-superscript;
                    color: #2196f3;
                }
            }

            &:valid {
                & ~ label {
                    display: none;
                }
            }

            &[valid] {
                & ~ label {
                    display: none;
                }
            }
        }

        &.error {
            .shake;

            input {
                & ~ label {
                    color: palevioletred !important;
                    bottom: -1.8rem !important;
                    display: block;
                }
            }
        }
    }
}

.intl-tel-input .flag-dropdown {
    margin: .5rem 0;

    .selected-flag1 {
        height: 2.5rem;
        width: 2.5rem;
        .flag {
            height: 2.5rem;
            width: 2.5rem;
            background-size: 2rem;

            .down-arrow {
                top: 1rem;
                left: 2rem;
            }
        }
    }
    .country-list {
        text-align: justify;
        color: black;
    }
}

::-webkit-scrollbar {
    width: 5px;
}

::-webkit-scrollbar-track {
    -webkit-box-shadow: inset 0 0 3px rgba(0,0,0,0.1);
}

::-webkit-scrollbar-thumb {
    -webkit-box-shadow: inset 0 0 3px rgba(0,0,0,0.3);
}


.top-container {
    margin: 5rem 0 2rem 0;

    @media @iPhone5 {
        margin: 5rem 0 2rem 0;
    }

    #coupon_sellers {
        color: #6e6e6e;
        font-size: 0.9rem;
        padding: 0;

        .list {
            height: 22rem;
            overflow: auto;
            text-align: left;
            padding: 1rem;
            display: flex;
            flex-direction: column;

            > div {
                padding: .5rem 0;
            }
        }
    }

    .display-none {
        display: none !important;
    }
}

.progressBar {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        width: 100%;
        height: .2rem;
        background-color: transparent;
        border-radius: 2px;
        
        div {
            height: 100%;
            color: #fff;
            text-align: right;
            line-height: 22px;
            width: 0;
            max-width: inherit;
            background-color: rgba(12, 75, 158, .9);
        }
    }

body.landing {
    .login.container {
        .outer-card1 {
            > img {
                margin-right: 0px !important;
                height: 80px;
            }
        }
    }
}

.popup {
    position: fixed;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    z-index: 3;
    background: rgba(0,0,0,0.5);

    .popup-inner-div {
        width: 20rem;
        background: white;
        padding: 1rem;
        margin: 0 auto;
        font-size: 0.75rem;
        border-radius: 4px;
        position:relative;
        top: 25%; 
    }
}

.login.container {
    .card;
    width: 78%;
    max-width: 22rem;
    min-width: 18rem;
    margin-top: 0 !important;
    position: relative;
    display: inline-block;
    box-shadow: 0px 3px 6px 0px rgba(41,0,0,0.2) !important;

    .outer-card1 {
        text-align: right;
        position: relative;
        margin-bottom: 3rem;
        display: flex;
        justify-content: space-between;
        height: 80px;

        > img.wiom-logo {
            width: auto !important;
            height: 2.4rem !important;
            margin: -0.625rem -0.625rem 0 0;
        }

        &.handle-width div.middler {
            height: 4rem;
            display: inline-block;
            vertical-align: middle;
        }

        &.handle-width > img {
            width: 11rem !important;
            height: unset !important;
            vertical-align: middle;
        }

        &.handle-height > .img-sec img {
            width: unset !important;
            height: 6rem !important;
        }

        .back-button {
            height: 1rem !important;
        }

        .back-button.silent {
            opacity: 0;
            pointer-events: none;
        }
    }

    .inline-error {
        text-align: left;
        font-size: 0.9rem;
        color: rgba(255,0,0,.5);
    }

    form, .form {
        position: relative;

        .barking {
            margin-top: 1.875rem;

            .loud {
                font-size: 1.5rem;
                font-weight: 600;
            }

            .less-loud {
                font-size: 1.2rem;
                font-weight: 400;
            }
        }

        .my-fb-error-class {
            margin-bottom: 30px;
        }

        input {
            border-radius: 0;
        }

        input.primary {
            border-radius: 3px !important;
        }

        input, button {
            width: 100%;
            height: 100%;
        }

        .small_button {
            padding: .5rem;
            font-size: 0.7rem;

            &[disabled] {
                color: #afafaf;
            }

            &:focus:not([disabled]) {
                outline: none;
                cursor: pointer;
            }

            &:hover:not([disabled]) {
                cursor: pointer;
                box-shadow: @common-shadow;
            }
        }

        .autologin {
            display: none !important;
        }

        .username-area, .otp-area, .input-area {
            margin-bottom: 1rem;
        }

        input#username, input#otp {
            font-weight: @font-weight-phone-number;
        }

        input#otp {
            padding-left: 16px;
            letter-spacing: 48px;
            border: 0;
            background-image: linear-gradient(to left, #999 80%, rgba(255, 255, 255, 0) 0%);
            background-position: bottom;
            background-size: 67px 1px;
            background-repeat: repeat-x;
            background-position-x: 52px;
            width: 80%;
            font-size: 36px;
            text-align: center;
            color: #666
        }

        button.btn-grp {
            display: flex;
            justify-content: center;
            padding: .5rem;
            margin-top: 4rem;

            > * {
                margin: auto .5rem;
            }

            img {
                height: 2rem;
                filter: invert(1);
            }
        }

        input[type=button] {
            margin-top: 2.4rem;
        }


        .primary {
            font-weight: bold;
        }

        .question-area {
            margin-top: 2rem;
            -webkit-transition: max-height 1.5s linear, opacity .4s step-end; /* Safari */
            -moz-transition: max-height 1.5s linear, opacity .4s step-end;
            -o-transition: max-height 1.5s linear, opacity .4s step-end;
            transition: max-height 1.5s linear, opacity .4s step-end;
            max-height: 30rem;
            opacity: 1;


            &.display-none {
                -webkit-transition: max-height .3s linear, opacity .4s linear; /* Safari */
                -moz-transition: max-height .3s linear, opacity .4s linear;
                -o-transition: max-height .3s linear, opacity .4s linear;
                transition: max-height .3s linear, opacity .4s linear;
                display: block;
                max-height: 0;
                opacity: 0;
                margin: 0;
                padding: 0;
            }

            &.error .question.img-options {
                box-shadow: 1px 1px 2px rgba(0, 0, 0, 0.4);
            }

            .question {
                text-align: left;

                &.text-question {
                    margin-top: 2rem;
                }

                &.error.rating-question {
                    .shake;
                }

                &.error.select {
                    .shake;

                    ul li.init {
                        color: rgba(255, 0, 0, 0.5);
                    }
                }

                &.select .question-text {
                    cursor: pointer;
                    font-family: Roboto-Medium, fallback, sans-serif;
                    font-weight: bold;
                    color: black;

                    span {
                        color: red;
                    }
                }

                .next-question {
                    background-image: url(../../images/wiom/next.svg);
                    background-repeat: no-repeat;
                    border: 0px;
                    background-color: transparent;
                    box-shadow: none;
                    background-position: right;
                    color: transparent;
                }

                &.img-options {
                    ul {
                        margin: .8rem 0;
                        padding: 0;
                        height: 5rem;
                    }

                    li.img-option {
                        display: inline;
                        margin: .6rem;

                        .circle {
                            box-shadow: 3px 3px 2px rgba(0, 0, 0, 0.4);
                        }

                        input:checked + label {
                            .circle {
                                border: 0.1rem solid #333;
                                box-shadow: none;
                            }

                            span {
                                font-weight: bold;
                                color: black;
                            }

                            img {
                                .saturate;
                            }
                        }

                        input.error + label {
                            .circle {
                                border: 0.15rem solid rgba(255, 0, 0, 0.5);
                            }
                        }

                        label {
                            position: relative;
                            margin: .5rem;

                            > div {
                                position: absolute;
                                left: 50%;
                                top: -1rem;
                            }

                            .circle {
                                width: 3.5rem;
                                height: 3.5rem;
                                position: relative;
                                right: 50%;
                                border: 0.1rem solid #F6F6F6;

                                img {
                                    cursor: pointer;
                                }

                                &:hover {
                                    img {
                                        .saturate;
                                    }

                                    & + span {
                                        font-weight: bold;
                                        color: black;
                                    }
                                }
                            }

                            span {
                                top: -.3rem;
                                width: 5rem;
                                white-space: nowrap;
                                font-size: .8rem;
                                position: relative;
                                right: 50%;

                                &:hover {
                                    font-weight: bold;
                                    color: black;
                                }
                            }
                        }
                    }
                }


                &.select ul {
                    padding: 0;
                    color: grey;
                    margin-top: .3rem;

                    li {
                        width: -webkit-fill-available;
                        width: -moz-available;
                        padding: .7rem;
                        z-index: 2;
                        list-style: none;
                        text-align: justify;
                        -webkit-transition: max-height 1.5s linear, opacity .4s step-end; /* Safari */
                        -moz-transition: max-height 1.5s linear, opacity .4s step-end;
                        -o-transition: max-height 1.5s linear, opacity .4s step-end;
                        transition: max-height 1.5s linear, opacity .4s step-end;

                        &.display-none {
                            display: block;
                            max-height: 0;
                            opacity: 0;
                        }

                        .circle {
                            border: none;
                            height: 1.5em;
                            width: 1.5rem;
                            vertical-align: middle;
                            margin-right: .5rem;

                            ~ span {
                                vertical-align: middle;
                            }
                        }

                        &.init {
                            cursor: pointer;
                            margin-bottom: 1rem;
                            border: 1px solid #cccccc;

                            hr, img.check-mark {
                                display: none;
                            }

                            &.error {
                                border-color: rgba(255, 0, 0, 0.5);
                            }
                        }

                        &:not(.init) {
                            float: left;
                            display: none;
                            border-left: 1px solid #cccccc;
                            border-right: 1px solid #cccccc;
                            border-bottom: none;
                            padding: .5rem .7rem;
                            width: 18.5rem;
                            line-height: 1.5rem;
                            position: relative;

                            @media @smartphones, @iPhone5 {
                                width: 16.5rem;
                            }

                            &:nth-child(2) hr {
                                width: 100%;
                                left: 0;
                                border-bottom: 1px solid #cccccc !important
                            }

                            hr {
                                border-bottom: 1px solid rgba(204, 204, 204, 0.5) !important;
                                position: absolute;
                                top: -.5rem;
                                left: .7rem;
                                width: inherit;
                                border: none;
                            }

                            img.check-mark {
                                display: none;
                                width: 1rem;
                                position: absolute;
                                right: 1rem;
                                top: .8rem;
                            }

                            &:hover, &.selected {
                                background: @body-grey;
                            }

                            &[multiple].selected {
                                background: @body-grey;

                                img {
                                    display: block;
                                }
                            }
                        }

                        &:last-child {
                            margin-bottom: 1rem;
                            border-bottom: 1px solid #cccccc;
                        }
                    }
                }

                .text-answer {
                    &.error {
                        -webkit-box-shadow: 0 0 2px 2px rgba(255, 0, 0, 0.5);
                        -moz-box-shadow: 0 0 2px 2px rgba(255, 0, 0, 0.5);
                        box-shadow: 0 0 2px 2px rgba(255, 0, 0, 0.5);
                    }
                }

                &.rating-question {
                    ul {
                        text-align: center;
                        height: 3rem;

                        li.img-option {
                            margin: 0;

                            .circle {
                                height: 2.5rem;
                                width: 2.5rem;
                            }
                        }
                    }

                    input[type=radio],
                    input[type=checkbox] {
                        /* Hide original inputs */
                        visibility: hidden;
                        position: absolute;

                        & + label:before {
                            height: .9rem;
                            width: .9rem;
                            margin: 0.8rem 1rem -0.2rem 0;
                            content: " ";
                            display: inline-block;
                            vertical-align: baseline;
                            border: .1rem solid #eee;
                            background-color: white;
                        }

                        &:checked + label:before {
                            background: #00aeef !important;
                        }
                    }
                    /* CUSTOM RADIO AND CHECKBOX STYLES */
                    input[type=radio] + label:before {
                        border-radius: 50%;
                    }

                    input[type=checkbox] + label:before {
                        border-radius: .2rem;
                    }
                }
            }
            /* Begin New Input Styling changes */
            /* Base for label styling */
            [type="checkbox"]:not(:checked),
            [type="checkbox"]:checked {
                position: absolute;
                left: -9999px;
            }

            [type="checkbox"]:not(:checked) + label,
            [type="checkbox"]:checked + label {
                position: relative;
                padding-left: 1.95em;
                cursor: pointer;
                text-align: center;
            }

            [type="checkbox"]:checked + label {
                font-weight: bold;
                text-shadow: 1px 1px 1px #eee;


                img {
                    border: 2px solid #f4693a;
                    box-shadow: 1px 1px 1px #eee;
                }
            }

            [type="checkbox"]:checked + label:before {
                border: 2px solid #f4693a;
            }
            /* checkbox aspect */
            [type="checkbox"]:not(:checked) + label:before,
            [type="checkbox"]:checked + label:before {
                content: '';
                position: absolute;
                left: 0;
                top: 2px;
                width: 20px;
                height: 20px;
                background: #fff;
                border-radius: 20px;
                box-shadow: inset 0 1px 3px rgba(0,0,0,.1);
            }

            [type="checkbox"]:not(:checked) + label:before {
                border: 2px solid #333;
            }
            /* checked mark aspect */
            [type="checkbox"]:not(:checked) + label:after,
            [type="checkbox"]:checked + label:after {
                content: '\2713\0020';
                position: absolute;
                top: -3px;
                left: .22em;
                font-size: 24px;
                line-height: 1;
                color: #f4693a;
                transition: all .2s;
                font-family: Arial;
                font-weight: 800;
                text-shadow: 1px 1px 1px #FFF;
                transform: rotate(30deg);
            }
            /* checked mark aspect changes */
            [type="checkbox"]:not(:checked) + label:after {
                opacity: 0;
                transform: scale(0);
            }

            [type="checkbox"]:checked + label:after {
                opacity: 1;
                transform: scale(1);
            }
            /* disabled checkbox */
            [type="checkbox"]:disabled:not(:checked) + label:before,
            [type="checkbox"]:disabled:checked + label:before {
                box-shadow: none;
                border-color: #bbb;
                background-color: #ddd;
            }

            [type="checkbox"]:disabled:checked + label:after {
                color: #999;
            }

            [type="checkbox"]:disabled + label {
                color: #aaa;
            }
            /* accessibility */
            [type="checkbox"]:checked:focus + label:before,
            [type="checkbox"]:not(:checked):focus + label:before {
                /*border: 2px dotted #f4693a;*/
            }
            /* hover style just for information */
            label:hover:before {
                border: 2px solid #F2A063 !important;
            }

            label {
                line-height: 32px;
                width: fit-content;
                overflow: hidden;
                display: block;
                text-align: left;
            }

            label > img {
                display: block;
                line-height: 17px;
                vertical-align: text-bottom;
                object-fit: cover;
                border-radius: 50%;
                border: 2px solid #333;
                width: 42px;
            }
            /* End New Input Styling changes */
        }

        #captcha-holder {
            display: inline-block;
            text-align: justify;
            width: 100%;
            max-height: 3rem;
            margin-bottom: 2.5rem;
            font-size: 0.8rem;

            div {
                width: 48%;
                display: inline-block;
                max-height: 3rem;
                vertical-align: middle;

                img {
                    height: 100%;
                    width: 100%;
                }

                input {
                    width: 100%;
                }
            }
        }
    }



    .tnc-area {
        text-align: center;

        .tnc1 {
            font-size: 11px;
            color: @font-color-grey;

            a {
                color: inherit;
                text-decoration: underline;
                font-weight: bold;
            }
        }

        &.corner {
            width: 22rem;
            margin-top: -1.5rem;
            margin-bottom: .8rem;

            .tnc1 {
                font-size: .67rem;
            }

            @media @smartphones, @iPhone5 {
                width: 20rem;

                .tnc1 {
                    font-size: .6rem;
                }
            }
        }
    }

    .footer {
        text-align: left;
        color: @font-color-grey;
        font-size: 12px;
        position: relative;
        padding-top: 1.1rem;
        margin-top: 1rem;

        span {
            display: block;
            text-align: left;
        }

        b {
            font-weight: bold;

            a {
                color: #f25f58;
            }
        }

        .top-border {
            border-top: 1px solid #cccccc;
            position: absolute;
            top: 0rem;
            left: -1rem;
            right: -1rem;
        }

        .tnc-area {
            position: absolute;
            right: 0;
            bottom: 0;

            a {
                &:focus {
                    outline: none;
                }

                font-weight: normal;
                font-size: @font-size-field-superscript;
                text-decoration: none;
                font-style: italic;
                color: #f25f58;
            }
        }

        .i2e1_logo {
            margin-top: 0.2rem;
            text-align: center;

            img {
                width: 3rem;
            }
        }

        .stepper {
            height: .3rem;
            text-align: center;

            div.step {
                height: 8px;
                width: 8px;
                border-radius: 4px;
                display: inline-block;
                background-color: transparent;
                border: 1px solid @placeholder-grey;
                box-sizing: border-box;

                &.visited {
                    border: none;
                    background-color: @wiom-stepper-visited;
                }
            }

            div.connector {
                width: 0.5rem;
                height: 100%;
                display: inline-block;

                div {
                    visibility: hidden !important;
                    height: 50%;
                    border-bottom: 1px solid @placeholder-grey;
                }

                &.visited div {
                    border-bottom: 1px solid #16befd;
                }
            }
        }
    }

    .change_auth_link1 {
        font-size: @font-size-substandard;

        a {
            color: #291280;

            &:hover {
                text-decoration: underline;
            }
        }
    }

    .msg-section {
        text-align: justify;
    }

    span.issues-contact-us {
        font-size: .8rem;

        a {
            font-size: .8rem;
            vertical-align: bottom;
        }
    }

    button.fb-share {
        background-color: #3b5998;
        padding: 0 .5rem 0 0;
        width: 13rem;

        img {
            width: 1.8rem;
            margin: .2rem;
            float: left;
        }

        div {
            padding: .6rem;
            font-size: 1rem;
            font-weight: 100;
            display: inline-block;
            border-left: 1px solid white;
        }
    }

    .state-transition {
        margin: 1rem 0;
        opacity: 1;
        transition: .3s opacity linear, .3s left ease-in-out;
        -webkit-transition: .3s opacity linear, .3s left ease-in-out;
        -o-transition: .3s opacity linear, .3s left ease-in-out;
        -moz-transition: .3s opacity linear, .3s left ease-in-out;
        padding-bottom: 0px;

        &.display-none {
            height: 0;
            opacity: 0;
            display: block;
            margin-bottom: 0;
            padding: 0;
            left: 20rem;
            pointer-events: none;
        }
    }

    .first-state {
        .username-area:not(.global-otp) {
            margin-top: 5rem;
            .material-input;
            position: relative;

            .groups {
                display: flex;
                justify-content: space-between;
            }

            &.phone-number .group {
                width: -webkit-fill-available;
                width: -moz-available;

                #username:valid, #username:focus {
                    & ~ a {
                        display: inline-block;
                        width: 1rem;
                        height: 2rem;
                        font-size: .9rem;
                    }

                    & ~ label {
                        //visibility: hidden;
                        //font-size: 0px;
                    }
                }
            }

            a {
                display: none;
                position: absolute;
                top: 0;
                right: 0;
                font-size: .8rem;
                font-weight: bold;

                &:hover {
                    text-shadow: @text-shadow;
                }
            }

            .username_prefix {
                #username_prefix {
                    font-weight: 600;
                    position: relative;
                }

                width: 3.5rem !important;
                background: transparent;
            }
        }

        .username-area.global-otp {
            label, a {
                display: none;
            }
        }

        .last-name-area, .roomno-area {
            .material-input;
        }

        .roomno-area {
            margin-bottom: 1.5rem;
        }

        .seperator {
            line-height: 2rem;
        }

        .general-msg {
            font-size: @font-size-substandard + .1rem;
            text-align: left;
            margin: 0;

            &:not(.display-none) {
                margin: .2rem 0;

                ~ .input-area {
                    margin-top: 1rem;
                }

                ~ .username-area {
                    margin-top: 1rem;
                }
            }

            &.error {
                color: palevioletred !important;
            }
        }

        [cage=generate-otp], [cage=access-code-connect-button], [cage=room-no-connect-button] {
            text-align: left;

            input {
                margin-bottom: 1.2rem;
            }
        }

        #facebook-btn {
            background-color: #3b5998;
            color: white;
            display: inline-block;
            border-radius: 0.2rem;
            margin: 1rem;

            span:first-child {
                border-right: 1px solid #fff;
                width: 3.012rem;
                display: inline-block;
                padding-top: .625rem;
                vertical-align: middle;

                img {
                    width: 1.25rem;
                }
            }

            span:nth-child(2) {
                padding: 0 1rem;
                vertical-align: baseline;
            }
        }
    }

    .second-state {
        .questions-confirm-area {
            margin-bottom: 40px;

            .tick {
                text-align: center;
                margin: 0px auto;
                padding: 10px;
                border-radius: 50px;
                border: 2px solid red;
                width: 40px;
                height: 40px;
                display: block;
                margin-bottom: 40px;
            }

            .questions-confirm-cta {
                margin: 0px auto;
                text-align: center;
                width: 80%;

                .cta-okay {
                    float: right;
                    padding: 5px 0px 5px 10px;
                    color: #ef5374;
                    font-weight: bold;
                    margin: 30px;
                }
            }
        }

        .error-message {
            position: relative;
            top: 0px;
            float: left;
            color: #ef4f82;
            font-size: .75rem;
            font-family: Roboto-Medium, fallback, sans-serif;
        }

        .barking {
            margin-top: 0px;
        }

        .welcome-back {
            display: none;

            .namaste {
            }

            .greetings {
            }
        }

        .bottom-button-group {
            opacity: 1;
            transition: .25s all ease-out;
            -webkit-transition: .25s all ease-out;
            -moz-transition: .25s all ease-out;
            -o-transition: .25s all ease-out;
            display: flex;
            justify-content: space-between;
            margin-top: .5rem;

            .back-area, .resend-otp-area {
                height: auto;
                cursor: pointer;
                background: none;
                font-family: Roboto-Medium, Fallback, sans-serif !important;
                color: #999;

                img {
                    width: 1.2rem;
                }
            }

            .back-area {
                font-size: .875rem;
            }

            .resend-otp-area {
                right: 0;
                height: auto;

                input.small_button {
                    margin-top: 0;
                    background: none !important;
                    color: #999;
                    padding: 0px;
                    box-shadow: none;
                    font-size: 14px;
                    font-weight: 400;
                    font-family: Roboto-Medium, Fallback, sans-serif !important;
                }
            }
        }

        .button-group {
            opacity: 1;
            transition: .25s all ease-out;
            -webkit-transition: .25s all ease-out;
            -moz-transition: .25s all ease-out;
            -o-transition: .25s all ease-out;

            .back-area {
                position: absolute;
                height: inherit;
                top: -3.5rem;
                cursor: pointer;

                img {
                    width: 1.2rem;
                }
            }
        }

        .otp-area {
            margin-top: 4.5rem;
            position: relative;
            border-bottom: none !important;
            .material-input;

            .group input {
                letter-spacing: 1rem;
                transition: .25s ease;
                -webkit-transition: .25s ease;
                -moz-transition: .25s ease;
                -o-transition: .25s ease;
                width: 70%;

                &::placeholder {
                    padding-left: 1rem;
                }
            }

            #otp-group {
                display: flex;
                justify-content: space-between;

                input {
                    border-bottom: 1px solid #333333;
                    width: 22%;
                    text-align: center;
                    font-weight: bold;
                    letter-spacing: normal;
                    padding-bottom: .5rem;
                    font-family: 'Roboto','sans-serif','fallback';
                }
            }
        }

        .filler {
            height: 5rem;
            margin: 1rem 0;
            box-shadow: @common-shadow;
        }
    }

    .mac-confirmation-state {
        .mac-area {
            .material-input;
            margin-bottom: 1.2rem;
        }

        ul {
            color: @placeholder-grey;
            list-style: none;
            padding-left: 0;
            text-align: left;
            font-size: 0.9rem;
            margin-bottom: 2rem;

            li {
                margin-bottom: 1rem;
            }
        }
    }

    .store-detail-state {
        .shop-name-area {
            .material-input;
            margin-bottom: 1.2rem;
        }

        .address-area {
            .material-input;
        }

        .user-email-area {
            .material-input;
            margin-bottom: 1.2rem;
        }

        [cage=connect-button] {
            margin-top: 2rem;
        }
    }

    .voucher-state {
        display: inline-block;
    }

    .data-voucher-state {
        text-align: left;

        h3 {
            margin-top: 0;
        }

        .chooser {
            list-style: none;
            padding: 0;

            input {
                width: 1rem;
                height: 1rem;
            }
        }
    }


    .plan-selection-state, .free-plan-state {
        .chooser {
            list-style: none;
            text-align: left;
            font-size: 1rem;
            font-weight: 600;
            padding: 0 2rem;

            li {
                display: flex;
                margin: 0.5rem 0;
            }

            input {
                min-width: 1rem;
                width: 1rem;
                height: 1rem;
                margin: auto .5rem;
            }

            label {
                margin: auto 0;
            }
        }

        .plans {
            h3 {
                margin-top: 0;
            }

            &.plan-chooser .login_button {
                margin-top: 1rem;
            }
        }

        a.goto-coupon {
            color: rgb(244, 105, 58);
            margin-top: 1rem;
        }
    }

    .social-state {
        display: inline-block;

        .social {
            > img {
                width: 13rem;
                margin-bottom: 1rem;
            }

            .inline-error {
                display: inline;
            }
        }
    }

    .error-state {
        display: inline-block;

        .limit-exhausted, .time-exhausted, .user-blocked {
            text-align: center;
            margin-bottom: -1.3rem;

            img {
                width: 80%;
                margin: 1rem 0;
            }
        }

        .limit-exhausted img {
            width: 60%;
        }

        #errorMsg, .go-back-to-login {
            a {
                color: blue !important;
                cursor: pointer;

                &:hover, &:focus {
                    text-decoration: underline;
                }
            }

            color: black;
            margin: 1rem auto 0 auto;
            width: 80%;
            text-shadow: 1px 1px 1px rgba(158, 158, 158, 0.49);
        }

        .airtel-limit-exceeded {
            h3 {
                margin: .8rem;
            }

            table {
                border: 2px solid #f18f93;
                margin-bottom: 1rem;
                width: 100%;

                tbody {
                    background: #d9edf7;
                }

                tr {
                    height: 2rem;
                }
            }
        }
    }

    .redirecting-state {
        display: inline-block;

        .connected {
            font-size: 0.9rem;
            text-align: center;
            font-family: Roboto, fallback, sans-serif;
        }

        #greeting {
            margin-top: 36px;
            text-align: center;
            font-size: 1.5rem;
            color: black;
            line-height: 32px;
            font-family: Roboto-Medium, fallback, sans-serif;
        }

        .timemessage {
            text-align: center;
            font-size: 0.9rem;
            color: black;
            font-family: Roboto-Medium, fallback, sans-serif;
        }

        .datamessage {
            margin-top: 36px;
            text-align: center;
            font-size: 1.5rem;
            color: black;
            line-height: 32px;
            font-family: Roboto, fallback, sans-serif;
        }

        .campaign {
            color: black;
            padding: .7rem;
            margin-bottom: 1rem;
            background-color: white;

            .close {
                display: none;
            }

            &.scale-up {
                -ms-transform: scale(1.5,1.5); /* IE 9 */
                -webkit-transform: scale(1.5,1.5); /* Safari */
                transform: scale(1.5,1.5); /* Standard syntax */
                position: absolute;
                z-index: 10;
                top: 0;

                .close {
                    display: block;
                    position: absolute;
                    width: 1.5rem;
                    height: 1.5rem;
                    right: 0;
                    top: 0;
                    font-size: 1rem;
                    font-weight: bold;
                    padding: 0;
                    border-radius: 10rem;

                    &:focus {
                        outline: none;
                    }
                }
            }

            .border {
                background: white;
                border: 2px solid #9d9d9d;
                padding: .5rem;
            }

            .text {
                margin: 0;
            }

            img {
                width: 100%;
            }

            .offerCode {
                background-color: yellow;
            }
        }
    }

    .landing-state {
        .landing-page-message {
            font-size: 1.2rem;
            font-weight: bold;
            padding: 2rem 0 0 0;
        }

        .user-attributes {
            margin: 1rem;
            font-size: .9rem;
            font-weight: bold;

            > div {
                display: inline-block;

                &.display-none {
                    display: none;
                }
            }

            div:nth-child(1) {
                margin-right: 1rem;
            }

            div:nth-child(2) {
                margin-right: 1rem;
                color: #f36150;
            }

            .data img, .time img {
                width: 1.5rem;
            }
        }

        div[action] {
            position: relative;
            margin-bottom: 1rem;

            img {
                position: absolute;
                width: 2rem;
                top: .5rem;
                right: .5rem;
            }

            input {
                padding-left: 1rem;
                border: 1px solid #dad9d9;
                height: 3rem;

                &:focus {
                    background: white;
                }
            }
        }

        .landing-page-tiles-container {
            div {
                width: 5rem;
                height: 5rem;
                display: inline-block;
                margin: .2rem;
                background-color: white;
                vertical-align: middle;
                border: 1px solid #2f4a9a;

                a {
                    width: 100%;
                }

                img {
                    width: 100%;
                }
            }
        }
    }
}



.advertisement-campaign {
    position: absolute;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    z-index: 29;
    background-color: rgba(0, 0, 0, 0.30);
    .login.container {
         z-index: 30;

         .back-area {
             span {
                color: #999999;
             }
         }

         &.transition {
            opacity: 1;
            transition: .3s opacity linear, .3s left ease-in-out;
            -webkit-transition: .3s opacity linear, .3s left ease-in-out;
            -o-transition: .3s opacity linear, .3s left ease-in-out; 
            -moz-transition: .3s opacity linear, .3s left ease-in-out;

            &.display-none {
                height: 0;
                opacity: 0;
                display: block;
                margin-bottom: 0;
                padding: 0;
                left: 20rem;
                pointer-events: none;
            }
        }
    }
    
    .campaign {
        position: relative;
    }
    span.ctp {
        position: absolute;
        bottom: 0;
        right: 1rem;
        font-size: .9rem;
    }
    img, video {
        width: inherit;
    }

    .circle {
            width: 1.5rem;
            height: 1.5rem;
            border: none;
            position: absolute;
            top: -.75rem;
            right: -.75rem;
            img {
                width: 1.5rem;
                cursor: pointer;
                &.grayscale {
                    pointer-events: none;
                }
            }
        }
}


.language-selectors {
    width: 100%;
    padding: 1rem 0;
    position: absolute;
    left: 0;
    top: 0;
    text-align: right;
    transform: translateY(-100%);
    -ms-transform: translateY(-100%); /* IE 9 */
    -webkit-transform: translateY(-100%); /* Safari */
    a {
        display: none;
        color: #484848;
        font-weight: 800;
        margin: 0 0 0 1rem;
        font-size: 0.875rem;
        line-height: 1.06rem;

    }
}

.plan-success {
    .success-img {
        height: 10rem;
        width: 10rem;
    }

    .enjoy {
        font-size: 1.5rem;
        color: #f4693a;
        text-align: center;
        font-weight: 600;
        margin-top: 1rem;
    }

    .title {
        font-size: 1.25rem;
        text-align: center;
        font-weight: 600;
        margin-top: 1rem;
    }

    .sub-title {
        font-size: 1rem;
        text-align: center;
    }
}

#existing-plan-overlay {
    display: none;
    position: fixed;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    background-color: #00000080;
    z-index: 1;

    .float-div {
        float: left;
        padding: 0.5rem 0;
    }
}


.help-video {
    position: fixed;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    background: rgba(0, 0, 0, 0.5);

    .video {
        position: fixed;
        top: 50%;
        left: 50%;
        width: 90%;
        transform: translate(-50%, -50%);
        display: flex;
        flex-direction: column;

        img {
            width: 1rem;
            margin: 1rem auto;
            filter: brightness(100);
        }

        video {
            width: -webkit-fill-available;
            width: -moz-available;
            border: 5px solid black;
            border-radius: 5px;
        }
    }
}
