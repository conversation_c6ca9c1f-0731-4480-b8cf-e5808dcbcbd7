textarea, select {
        background-color: #F8F8F8;
}
.300 {
	width: 300px !important;
}
form {
	-webkit-box-shadow: 0px 0px 7px 2px rgba(226,226,226,0.87);
	-moz-box-shadow: 0px 0px 7px 2px rgba(226,226,226,0.87);
	box-shadow: 0px 0px 7px 2px rgba(226,226,226,0.87);
}

h1#login {
	font-size: 24px;
	float: right;
	padding-top: 20px;
	color: #333;
}

button[type=submit] {
	margin-top: 10px;
}

.btn-primary {
    width: 160px;
}

.blue {
	background-color: #0C4DA2 !important;
}

.red {
	background-color: #FA1E25 !important;
}

.yellow {
	background-color: #FFE703 !important;
}

.green {
	background-color: #7FD74C !important;
}

div#navigation {
	-webkit-box-shadow: 0px 0px 16px 3px rgba(226,226,226,1);
	-moz-box-shadow: 0px 0px 16px 3px rgba(226,226,226,1);
	box-shadow: 0px 0px 16px 3px rgba(226,226,226,1);
}
.navbar-brand {
	padding: 7px 20px 0px 10px;
}

.navbar-nav {
	float: right !important;
	margin: 0px;
}

.input-group, select {
}

.row-centered {
    text-align:center;
}
.col-centered {
    display:inline-block;
    float:none;
    /* reset the text-align */
    text-align:left;
    /* inline-block space fix */
    margin-right:-4px;
}

form {
	background-color: #f6f6f6;
	padding: 15px;
	border-radius: 3px;
}

.navbar-nav li a {
	font-weight: 500;
    font-size: 14px;
}
.navbar-nav li a:hover {
	color: #0C4DA2 !important;
	text-decoration: underline;
}

textarea, textarea.form-control, input.form-control, input[type="text"], input[type="password"], input[type="email"], input[type="number"], .form-control[type="text"], .form-control[type="password"], .form-control[type="email"], .form-control[type="tel"], .form-control[contenteditable], select, select.form-control {
	box-shadow: 0px -1px 0px rgba(33, 75, 161, 0.5) inset;
}

textarea:focus, textarea.form-control:focus, input.form-control:focus, input[type="text"]:focus, input[type="password"]:focus, input[type="email"]:focus, input[type="number"]:focus, .form-control[type="text"]:focus, .form-control[type="password"]:focus, .form-control[type="email"]:focus, .form-control[type="tel"]:focus, .form-control[contenteditable]:focus, select:focus, select.form-control:focus {
	box-shadow: 0px -2px 0px #2196F3 inset !important;
}

legend {
	border-bottom:1px solid rgba(251, 214, 13, 0.5);
}

.autowidth {
	width:auto !important;
}


.panel {
	margin-top: 10px !important;
}

.input-group-addon {
	font-size: 14px;
}

.input-group-addon {
    padding: 6px 8px;
}

#nas-dropdown {
	border-bottom: solid 2px #e2e2e2;
}

#nas-dropdown > select {
	border-bottom: 0px;
	box-shadow: none !important;
}

.bg-white {
	background-color: #fff !important;
}

.font-medium {
	font-size: 14px !important;
}

.margin-top-small {
	margin-top: 5px;
}
.margin-top-medium {
	margin-top: 15px;
}
.margin-top-large {
	margin-top: 15px;
}

a{
    cursor: pointer;
}
div.success, span.success{
    background-color: #25BEA7;
}

div.failure, span.failure{
    background-color: #D93030;
}

div.info, span.info{
    background-color: #2FC0E9;
}

.card {
    position: relative;
    margin-bottom: 24px;
    background-color: #fff;
    color: #313534;
    border-radius: 2px;
    -webkit-box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.33);
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.33);
    padding-left: 10px;
}
.card h4 {
    padding-top: 7px;
}


.i2e1-dialog .modal-dialog {
    height: 700px;
    min-width: 200px;
    text-transform: none;
}
.i2e1-dialog .modal-dialog h3 {
    font-size: 24px;
	font-weight: bold;
}

.i2e1-dialog .modal-dialog p {
	align:justify;
}

.i2e1-dialog .content{
	overflow-y: scroll; 
	padding-right: 10px;
	height: 500px;
}

.slider-value {
    display: block;
    text-align: center;
    font-size: 16px;
}

.entry-seperator {
    text-align:center;
    line-height: .5;
}

.entry-seperator span {
    position: relative;
    top: 26px;
    background-color: #F6F6F6;
    padding: 0 5px;
}

.entry-seperator hr {
    border-top: 1px solid #ddd;
}

.display-none {
    display: none;
}

form input.error {
            background: rgba(255, 0, 0, 0.13);
        }