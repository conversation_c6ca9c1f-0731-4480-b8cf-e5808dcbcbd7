/**
 * selectize.default.css (v0.8.5) - Default Theme
 * Copyright (c) 2013 <PERSON> & contributors
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not use this
 * file except in compliance with the License. You may obtain a copy of the License at:
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software distributed under
 * the License is distributed on an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF
 * ANY KIND, either express or implied. See the License for the specific language
 * governing permissions and limitations under the License.
 *
 * <AUTHOR> <<EMAIL>>
 */

.selectize-control.plugin-drag_drop.multi > .selectize-input > div.ui-sortable-placeholder {
  background: #f2f2f2 !important;
  background: rgba(0, 0, 0, 0.06) !important;
  border: 0 none !important;
  visibility: visible !important;
  -webkit-box-shadow: inset 0 0 12px 4px #ffffff;
          box-shadow: inset 0 0 12px 4px #ffffff;
}

.selectize-control.plugin-drag_drop .ui-sortable-placeholder::after {
  content: '!';
  visibility: hidden;
}

.selectize-control.plugin-drag_drop .ui-sortable-helper {
  -webkit-box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
          box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
}

.selectize-dropdown-header {
  position: relative;
  padding: 5px 8px;
  background: #f8f8f8;
  border-bottom: 1px solid #d0d0d0;
  -webkit-border-radius: 3px 3px 0 0;
     -moz-border-radius: 3px 3px 0 0;
          border-radius: 3px 3px 0 0;
}

.selectize-dropdown-header-close {
  position: absolute;
  top: 50%;
  right: 8px;
  margin-top: -12px;
  font-size: 20px !important;
  line-height: 20px;
  color: #303030;
  opacity: 0.4;
}

.selectize-dropdown-header-close:hover {
  color: #000000;
}

.selectize-dropdown.plugin-optgroup_columns .optgroup {
  float: left;
  border-top: 0 none;
  border-right: 1px solid #f2f2f2;
  -webkit-box-sizing: border-box;
     -moz-box-sizing: border-box;
          box-sizing: border-box;
}

.selectize-dropdown.plugin-optgroup_columns .optgroup:last-child {
  border-right: 0 none;
}

.selectize-dropdown.plugin-optgroup_columns .optgroup:before {
  display: none;
}

.selectize-dropdown.plugin-optgroup_columns .optgroup-header {
  border-top: 0 none;
}

.selectize-control.plugin-remove_button [data-value] {
  position: relative;
  padding-right: 24px !important;
}

.selectize-control.plugin-remove_button [data-value] .remove {
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  display: inline-block;
  width: 17px;
  padding: 2px 0 0 0;
  font-size: 12px;
  font-weight: bold;
  color: inherit;
  text-align: center;
  text-decoration: none;
  vertical-align: middle;
  border-left: 1px solid #0073bb;
  -webkit-border-radius: 0 2px 2px 0;
     -moz-border-radius: 0 2px 2px 0;
          border-radius: 0 2px 2px 0;
  -webkit-box-sizing: border-box;
     -moz-box-sizing: border-box;
          box-sizing: border-box;
}

.selectize-control.plugin-remove_button [data-value] .remove:hover {
  background: rgba(0, 0, 0, 0.05);
}

.selectize-control.plugin-remove_button [data-value].active .remove {
  border-left-color: #00578d;
}

.selectize-control.plugin-remove_button .disabled [data-value] .remove:hover {
  background: none;
}

.selectize-control.plugin-remove_button .disabled [data-value] .remove {
  border-left-color: #aaaaaa;
}

.selectize-control {
  position: relative;
}

.selectize-dropdown,
.selectize-input,
.selectize-input input {
  font-family: inherit;
  font-size: 13px;
  -webkit-font-smoothing: inherit;
  line-height: 18px;
  color: #303030;
}

.selectize-input,
.selectize-control.single .selectize-input.input-active {
  display: inline-block;
  cursor: text;
  background: #ffffff;
}

.selectize-input {
  position: relative;
  z-index: 1;
  display: inline-block;
  width: 100%;
  padding: 8px 8px;
  overflow: hidden;
  border: 1px solid #d0d0d0;
  -webkit-border-radius: 3px;
     -moz-border-radius: 3px;
          border-radius: 3px;
  -webkit-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.1);
          box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.1);
  -webkit-box-sizing: border-box;
     -moz-box-sizing: border-box;
          box-sizing: border-box;
}

.selectize-control.multi .selectize-input.has-items {
  padding: 5px 8px 2px;
}

.selectize-input.full {
  background-color: #ffffff;
}

.selectize-input.disabled,
.selectize-input.disabled * {
  cursor: default !important;
}

.selectize-input.focus {
  -webkit-box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.15);
          box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.15);
}

.selectize-input.dropdown-active {
  -webkit-border-radius: 3px 3px 0 0;
     -moz-border-radius: 3px 3px 0 0;
          border-radius: 3px 3px 0 0;
}

.selectize-input > * {
  display: -moz-inline-stack;
  display: inline-block;
  *display: inline;
  vertical-align: baseline;
  zoom: 1;
}

.selectize-control.multi .selectize-input > div {
  padding: 2px 6px;
  margin: 0 3px 3px 0;
  color: #ffffff;
  cursor: pointer;
  background: #1da7ee;
  border: 1px solid #0073bb;
}

.selectize-control.multi .selectize-input > div.active {
  color: #ffffff;
  background: #92c836;
  border: 1px solid #00578d;
}

.selectize-control.multi .selectize-input.disabled > div,
.selectize-control.multi .selectize-input.disabled > div.active {
  color: #ffffff;
  background: #d2d2d2;
  border: 1px solid #aaaaaa;
}

.selectize-input > input {
  max-width: 100% !important;
  max-height: none !important;
  min-height: 0 !important;
  padding: 0 !important;
  margin: 0 1px !important;
  line-height: inherit !important;
  text-indent: 0 !important;
  background: none !important;
  border: 0 none !important;
  -webkit-box-shadow: none !important;
          box-shadow: none !important;
  -webkit-user-select: auto !important;
}

.selectize-input > input:focus {
  outline: none !important;
}

.selectize-input::after {
  display: block;
  clear: left;
  content: ' ';
}

.selectize-input.dropdown-active::before {
  position: absolute;
  right: 0;
  bottom: 0;
  left: 0;
  display: block;
  height: 1px;
  background: #f0f0f0;
  content: ' ';
}

.selectize-dropdown {
  position: absolute;
  z-index: 95;
  margin: -1px 0 0 0;
  background: #ffffff;
  border: 1px solid #d0d0d0;
  border-top: 0 none;
  -webkit-border-radius: 0 0 3px 3px;
     -moz-border-radius: 0 0 3px 3px;
          border-radius: 0 0 3px 3px;
  -webkit-box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
          box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  -webkit-box-sizing: border-box;
     -moz-box-sizing: border-box;
          box-sizing: border-box;
}

.selectize-dropdown [data-selectable] {
  overflow: hidden;
  cursor: pointer;
}

.selectize-dropdown [data-selectable] .highlight {
  background: rgba(125, 168, 208, 0.2);
  -webkit-border-radius: 1px;
     -moz-border-radius: 1px;
          border-radius: 1px;
}

.selectize-dropdown [data-selectable],
.selectize-dropdown .optgroup-header {
  padding: 5px 8px;
}

.selectize-dropdown .optgroup:first-child .optgroup-header {
  border-top: 0 none;
}

.selectize-dropdown .optgroup-header {
  color: #303030;
  cursor: default;
  background: #ffffff;
}

.selectize-dropdown .active {
  color: #495c68;
  background-color: #f5fafd;
}

.selectize-dropdown .active.create {
  color: #495c68;
}

.selectize-dropdown .create {
  color: rgba(48, 48, 48, 0.5);
}

.selectize-dropdown-content {
  max-height: 200px;
  overflow-x: hidden;
  overflow-y: auto;
}

.selectize-control.single .selectize-input,
.selectize-control.single .selectize-input input {
  cursor: pointer;
}

.selectize-control.single .selectize-input.input-active,
.selectize-control.single .selectize-input.input-active input {
  cursor: text;
}

.selectize-control.single .selectize-input:after {
  position: absolute;
  top: 50%;
  right: 15px;
  display: block;
  width: 0;
  height: 0;
  margin-top: -3px;
  border-color: #808080 transparent transparent transparent;
  border-style: solid;
  border-width: 5px 5px 0 5px;
  content: ' ';
}

.selectize-control.single .selectize-input.dropdown-active:after {
  margin-top: -4px;
  border-color: transparent transparent #808080 transparent;
  border-width: 0 5px 5px 5px;
}

.selectize-control.rtl.single .selectize-input:after {
  right: auto;
  left: 15px;
}

.selectize-control.rtl .selectize-input > input {
  margin: 0 4px 0 -2px !important;
}

.selectize-control .selectize-input.disabled {
  background-color: #fafafa;
  opacity: 0.5;
}

.selectize-control.multi .selectize-input.has-items {
  padding-right: 5px;
  padding-left: 5px;
}

.selectize-control.multi .selectize-input.disabled [data-value] {
  color: #999;
  text-shadow: none;
  background: none;
  -webkit-box-shadow: none;
          box-shadow: none;
}

.selectize-control.multi .selectize-input.disabled [data-value],
.selectize-control.multi .selectize-input.disabled [data-value] .remove {
  border-color: #e6e6e6;
}

.selectize-control.multi .selectize-input.disabled [data-value] .remove {
  background: none;
}

.selectize-control.multi .selectize-input [data-value] {
  text-shadow: 0 1px 0 rgba(0, 51, 83, 0.3);
  background-color: #1b9dec;
  background-image: -moz-linear-gradient(top, #1da7ee, #178ee9);
  background-image: -webkit-gradient(linear, 0 0, 0 100%, from(#1da7ee), to(#178ee9));
  background-image: -webkit-linear-gradient(top, #1da7ee, #178ee9);
  background-image: -o-linear-gradient(top, #1da7ee, #178ee9);
  background-image: linear-gradient(to bottom, #1da7ee, #178ee9);
  background-repeat: repeat-x;
  -webkit-border-radius: 3px;
     -moz-border-radius: 3px;
          border-radius: 3px;
  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#ff1da7ee', endColorstr='#ff178ee9', GradientType=0);
  -webkit-box-shadow: 0 1px 0 rgba(0, 0, 0, 0.2), inset 0 1px rgba(255, 255, 255, 0.03);
          box-shadow: 0 1px 0 rgba(0, 0, 0, 0.2), inset 0 1px rgba(255, 255, 255, 0.03);
}

.selectize-control.multi .selectize-input [data-value].active {
  background-color: #0085d4;
  background-image: -moz-linear-gradient(top, #008fd8, #0075cf);
  background-image: -webkit-gradient(linear, 0 0, 0 100%, from(#008fd8), to(#0075cf));
  background-image: -webkit-linear-gradient(top, #008fd8, #0075cf);
  background-image: -o-linear-gradient(top, #008fd8, #0075cf);
  background-image: linear-gradient(to bottom, #008fd8, #0075cf);
  background-repeat: repeat-x;
  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#ff008fd8', endColorstr='#ff0075cf', GradientType=0);
}

.selectize-control.single .selectize-input {
  background-color: #f9f9f9;
  background-image: -moz-linear-gradient(top, #fefefe, #f2f2f2);
  background-image: -webkit-gradient(linear, 0 0, 0 100%, from(#fefefe), to(#f2f2f2));
  background-image: -webkit-linear-gradient(top, #fefefe, #f2f2f2);
  background-image: -o-linear-gradient(top, #fefefe, #f2f2f2);
  background-image: linear-gradient(to bottom, #fefefe, #f2f2f2);
  background-repeat: repeat-x;
  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#fffefefe', endColorstr='#fff2f2f2', GradientType=0);
  -webkit-box-shadow: 0 1px 0 rgba(0, 0, 0, 0.05), inset 0 1px 0 rgba(255, 255, 255, 0.8);
          box-shadow: 0 1px 0 rgba(0, 0, 0, 0.05), inset 0 1px 0 rgba(255, 255, 255, 0.8);
}

.selectize-control.single .selectize-input,
.selectize-dropdown.single {
  border-color: #b8b8b8;
}

.selectize-dropdown .optgroup-header {
  padding-top: 7px;
  font-size: 0.85em;
  font-weight: bold;
}

.selectize-dropdown .optgroup {
  border-top: 1px solid #f0f0f0;
}

.selectize-dropdown .optgroup:first-child {
  border-top: 0 none;
}