.saturate {
  filter: saturate(3);
}
.grayscale {
  filter: grayscale(100%);
}
.contrast {
  filter: contrast(160%);
}
.brightness {
  filter: brightness(0.25);
}
.blur {
  filter: blur(3px);
}
.invert {
  filter: invert(100%);
}
.sepia {
  filter: sepia(100%);
}
.huerotate {
  filter: hue-rotate(180deg);
}
.rss.opacity {
  filter: opacity(50%);
}
.display-none {
  display: none !important;
}
.left-side-icon-bar {
  width: 5rem;
  height: 100%;
  position: fixed;
  top: 4.6rem;
  z-index: 15;
  text-align: center;
  background-color: white;
  border-right: 1px solid rgba(136, 136, 136, 0.21);
  transition: width 0.2s ease-in-out;
}
.left-side-icon-bar .row {
  line-height: 5rem;
  height: 5rem;
  margin: 0;
  position: relative;
}
.left-side-icon-bar .row.user-row {
  background-color: #F6F6F6;
  cursor: auto !important;
  border-bottom: 1px solid rgba(136, 136, 136, 0.21);
}
.left-side-icon-bar .row .action-area {
  font-size: 1rem;
  float: right;
  padding: 0 1rem;
}
.left-side-icon-bar .row .label-area,
.left-side-icon-bar .row .action-area {
  visibility: hidden;
  transition: all 0.1s ease-out;
  opacity: 0;
}
.left-side-icon-bar .row .label-area {
  display: inline-block;
  position: absolute;
  left: 5rem;
  white-space: nowrap;
}
.left-side-icon-bar .row .icon-area {
  width: 5rem;
  display: inline-block;
  position: absolute;
  left: 0;
}
.left-side-icon-bar .row .icon-area img {
  width: 2.5rem;
}
.left-side-icon-bar .row:hover {
  background-color: #F6F6F6;
  color: black;
  cursor: pointer;
}
.left-side-icon-bar .row.active {
  background-color: #e6e6e6;
  color: black;
  text-shadow: 1px 1px 1px rgba(158, 158, 158, 0.49);
}
.left-side-icon-bar:hover,
.left-side-icon-bar.hover-this {
  width: 20rem;
}
.left-side-icon-bar:hover .row .label-area,
.left-side-icon-bar.hover-this .row .label-area,
.left-side-icon-bar:hover .row .action-area,
.left-side-icon-bar.hover-this .row .action-area {
  z-index: 12;
  opacity: 1;
  visibility: visible;
  transition: all 0.4s ease-in;
}
@media (pointer: coarse) {
  .left-side-icon-bar:hover {
    width: 5rem;
  }
  .left-side-icon-bar:hover .row .label-area {
    display: none;
  }
}
@media only screen and (max-width: 479px) {
  .left-side-icon-bar {
    width: 0;
  }
  .left-side-icon-bar .row .icon-area,
  .left-side-icon-bar .row .action-area {
    opacity: 0;
  }
  .left-side-icon-bar:hover {
    width: 0;
  }
  .left-side-icon-bar:hover .row {
    z-index: 3;
    display: none;
  }
}
@media only screen and (max-width: 479px) and (pointer: coarse) {
  .left-side-icon-bar.hover-this {
    width: 20rem;
  }
  .left-side-icon-bar.hover-this .row .label-area,
  .left-side-icon-bar.hover-this .row .action-area {
    opacity: 1;
    transition: opacity 1s ease-in;
  }
}
.left-side-icon-bar a.list-group-item {
  text-align: left;
  padding-left: 5rem;
}
/************** This is used for multiple line ellipsis i.e number of line .... */
body {
  background-color: #f6f6f6;
  width: 100%;
}
.display-none {
  display: none;
}
input {
  border: 0;
  border-bottom: 1.5px solid rgba(140, 133, 133, 0.2);
  width: 100%;
  padding: .2rem 1rem;
}
input:focus {
  border-bottom: 1.5px solid rgba(140, 133, 133, 0.2) !important;
  outline: white;
  box-shadow: none !important;
  border-color: rgba(140, 133, 133, 0.2) !important;
}
button.btn,
a.btn {
  border-radius: 1px;
}
.message {
  position: absolute;
  width: 100%;
  top: 0;
  opacity: 0;
  transition: all 0.3s ease-in-out;
}
.message.popup {
  top: 18rem;
  opacity: 1;
  text-align: center;
}
.message.popup .appear {
  display: inline;
}
.message span {
  white-space: nowrap;
  padding: 0 1rem;
  border-radius: 2px;
  display: none;
  transition: top 0.5s ease-in-out;
}
.message .success {
  background-color: #b6ff00;
}
.message .error {
  background-color: #ec7979;
}
.message .info {
  background-color: #ffd800;
}
.i2e1-container {
  width: 100%;
  text-align: center;
  margin: auto;
}
.i2e1-container .logo {
  width: 10rem;
  margin: 3rem;
}
.i2e1-container .i2e1-form .details {
  background-color: white;
  width: 30%;
  margin: 0 auto;
  padding: 1.5rem;
  box-shadow: 1px 1px 2px 0 #888888;
}
@media only screen and (max-width: 479px) {
  .i2e1-container .i2e1-form .details {
    width: 90%;
  }
}
@media only screen and (min-width: 479px) and (max-width: 959px) {
  .i2e1-container .i2e1-form .details {
    width: 60%;
  }
}
.i2e1-container .i2e1-form .activate-email {
  line-height: 1.5rem;
}
.i2e1-container .i2e1-form input.error {
  background-color: #f7c2c2;
}
.i2e1-container .i2e1-form .header {
  margin-bottom: 4rem;
  font-size: 1.8rem;
  color: rgba(136, 136, 136, 0.8);
}
.i2e1-container .i2e1-form .entry-seperator .header {
  margin: 2rem;
  font-size: 1.8rem;
  color: rgba(136, 136, 136, 0.8);
}
.i2e1-container .i2e1-form .login {
  background-color: #4ab04d;
  color: white;
}
.i2e1-container .i2e1-form .login :hover {
  background-color: #39a63c;
}
.i2e1-container .i2e1-form .sign-up {
  background-color: #31459b;
  color: white;
}
.i2e1-container .i2e1-form .sign-up:hover {
  background-color: #293c8d;
  color: white;
}
.i2e1-container .i2e1-form .cell {
  height: 3rem;
  margin-bottom: 2rem;
}
.i2e1-container .i2e1-form .cell button,
.i2e1-container .i2e1-form .cell a.btn {
  width: 100%;
  padding: .5rem;
  font-size: 1.2rem;
}
.i2e1-container .i2e1-form .partition {
  position: relative;
  margin: 1rem 0;
}
.i2e1-container .i2e1-form .partition .left-part,
.i2e1-container .i2e1-form .partition .right-part {
  border-bottom: 1.5px dashed rgba(140, 133, 133, 0.8);
  width: 46%;
  top: 50%;
  position: absolute;
}
.i2e1-container .i2e1-form .partition .left-part {
  left: 0;
}
.i2e1-container .i2e1-form .partition .right-part {
  right: 0;
}
#loginWebsite.footer {
  top: 0;
  background: rgba(0, 0, 0, 0.3);
}
.footer {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  text-align: center;
  background: white;
  border-top: 1px solid #c4c4c4;
  color: black;
  line-height: 2;
  font-weight: bold;
  font-size: 1.5rem;
}
