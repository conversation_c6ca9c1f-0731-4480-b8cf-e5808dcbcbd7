@smartphones: ~"only screen and (max-width: 479px)";
@tablets: ~"only screen and (min-width: 479px) and (max-width: 959px)";

.position-tlbr(@position, @top, @left, @bottom, @right) {
    position: @position;
    top: @top;
    left: @left;
    bottom: @bottom;
    right: @right;
}


.saturate {-webkit-filter: saturate(3); filter: saturate(3);}
.grayscale {-webkit-filter: grayscale(100%); filter: grayscale(100%);}
.contrast {-webkit-filter: contrast(160%); filter: contrast(160%);}
.brightness {-webkit-filter: brightness(0.25); filter: brightness(0.25);}
.blur {-webkit-filter: blur(3px); filter: blur(3px);}
.invert {-webkit-filter: invert(100%); filter: invert(100%);}
.sepia {-webkit-filter: sepia(100%); filter: sepia(100%);}
.huerotate {-webkit-filter: hue-rotate(180deg); filter: hue-rotate(180deg);}
.rss.opacity {-webkit-filter: opacity(50%); filter: opacity(50%);}

.display-none { display: none !important; }

@icon-width: 5rem;
@title-height: 4.5rem;
@border-color: 1px solid rgba(136, 136, 136, 0.21);
@background-color: #F6F6F6;
@background-active-color: rgb(230,230,230);
@active-text-shadow: 1px 1px 1px rgba(158, 158, 158, 0.49);
@hover-card-shadow: 1px 0px 0px 0px rgba(136, 136, 136, 0.5);
@connector-line: 1px solid rgba(140, 133, 133, 0.5);

.left-side-icon-bar {
    width: @icon-width;
    height: 100%;
    position: fixed;
    top: @title-height + .1rem;
    z-index:15;
    text-align: center;
    background-color: white;
    border-right: @border-color;
    -webkit-transition: width .2s ease-in-out;
    -moz-transition: width .2s ease-in-out;
    -o-transition: width .2s ease-in-out;
    transition: width .2s ease-in-out;

    .row {
        line-height: @icon-width;
        height: @icon-width;
        margin: 0;
        position: relative;

        &.user-row { 
            background-color: @background-color;
            cursor: auto !important;
            border-bottom: @border-color;
        }

        .action-area {
            font-size: 1rem;
            float: right;
            padding: 0 1rem;
        }

        .label-area, .action-area {
            
            visibility: hidden;
            -webkit-transition: all .1s ease-out;
            -moz-transition: all .1s ease-out;
            -o-transition: all .1s ease-out;
            transition: all .1s ease-out;
            opacity: 0;
        }

        .label-area { 
            display: inline-block;
            position: absolute;
            left: @icon-width;
            white-space: nowrap;
            
        }

        .icon-area {
            width: @icon-width;
            display: inline-block;
            position: absolute;
            left: 0;

            img {
                width: 2.5rem;
            }
        }

        &:hover {
            background-color: @background-color;
            color:black;
            cursor:pointer;
        }

        &.active {
            background-color: @background-active-color;
            color:black;
            text-shadow: @active-text-shadow;
        }
    }
  
    &:hover,
    &.hover-this {
        width: 20rem;
        .row {
            .label-area, .action-area {
                z-index: 12;
                opacity: 1;
                visibility: visible;
                -webkit-transition: all  .4s ease-in;
                -moz-transition: all  .4s ease-in;
                -o-transition: all  .4s ease-in;
                transition: all  .4s ease-in;
            }
        }
    }

    &:hover {
         @media (pointer:coarse) {
            width: @icon-width;
            .row .label-area {
                display: none;
            }
        }
    }

    @media @smartphones {
        width: 0;
        .row {
            .icon-area, .action-area {
                opacity: 0;
            }
        }
        &:hover { 
            width: 0;
            .row {
                z-index: 3;
                display: none;
            }
        }
        &.hover-this {
            @media (pointer:coarse) {
                width: 20rem;
                .row {
                    .label-area, .action-area {
                        opacity: 1;
                        -webkit-transition: opacity 1s ease-in;
                        -moz-transition: opacity 1s ease-in;
                        -o-transition: opacity 1s ease-in;
                        transition: opacity 1s ease-in;
                    }
                }
            }
        }
    }

    a.list-group-item {
        text-align: left;
        padding-left: @icon-width;
    }
}

/************** This is used for multiple line ellipsis i.e number of line .... */
.multiLineEllipsis(@lineHeight: 2.2rem, @lineCount: 2, @bgColor: white, @padding-right: 0.3125rem, @width: 1rem, @ellipsis-right: 0) {
  overflow: hidden; /* hide text if it is more than $lineCount lines  */
  position: relative; /* for set '...' in absolute position */
  line-height: @lineHeight; /* use this value to count block height */
  max-height: @lineHeight * @lineCount; /* max-height = line-height * lines max number */
  padding-right: @padding-right; /* place for '...' */
  white-space: normal; /* overwrite any white-space styles */
  word-break: break-all; /* will break each letter in word */
  text-overflow: ellipsis; /* show ellipsis if text is broken */

  &::before {
    content: '...'; /* create the '...'' points in the end */
    position: absolute;
    right: @ellipsis-right;
    bottom: 0;
  }

  &::after {
    content: ''; /* hide '...'' if we have text, which is less than or equal to max lines and add $bgColor */
    position: absolute;
    right: 0;
    width: @width;
    height: 1rem * @lineCount;
    margin-top: 0.2rem;
    background: @bgColor; /* because we are cutting off the diff we need to add the color back. */
  }
}