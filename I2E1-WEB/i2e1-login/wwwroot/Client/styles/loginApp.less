@import 'commons';

body {
    background-color: #f6f6f6;
    width: 100%;
}

.display-none { display: none; }

input {
    border: 0;
    border-bottom: 1.5px solid rgba(140,133,133,0.2);
    width: 100%;
    padding: .2rem 1rem;
    &:focus {
        border-bottom: 1.5px solid rgba(140,133,133,0.2) !important;
        outline: white;
        box-shadow: none !important;
        border-color: rgba(140,133,133,0.2) !important;
    }
}

button.btn, a.btn {
    border-radius: 1px;
}





.message {
    position: absolute;
    width: 100%;
    top: 0;
    opacity: 0;
    -webkit-transition: all .3s ease-in-out;
    -moz-transition: all .3s ease-in-out;
    -o-transition: all .3s ease-in-out;
    transition: all .3s ease-in-out;
    
    &.popup { 
        top: 18rem;
        opacity: 1;
        text-align: center;
    
        .appear {
            display: inline;
        }
    }
    span {
        white-space: nowrap;
        padding: 0 1rem;
        border-radius: 2px;
        display: none;
        -webkit-transition: top .5s ease-in-out;
        -moz-transition: top .5s ease-in-out;
        -o-transition: top .5s ease-in-out;
        transition: top .5s ease-in-out;
    }
    .success {
        background-color: #b6ff00;
    }
    .error {
        background-color: #ec7979;
    }
    .info {
        background-color: #ffd800;
    }
}    

.i2e1-container {
    width: 100%;
    text-align: center;
    margin: auto;

    .logo {
        width: 10rem;
        margin: 3rem;
    }

    .i2e1-form {
        .details {
            background-color: white;
            width: 30%;
            margin: 0 auto;
            padding: 1.5rem;
            box-shadow: 1px 1px 2px 0 #888888;
            
            @media @smartphones {
                width: 90%;
            }

            @media @tablets {
                width: 60%;
            }

        }

        .activate-email {
            line-height: 1.5rem;
        }

        input.error {
            background-color:#f7c2c2;
        }
    
        .header {
            margin-bottom: 4rem;
            font-size: 1.8rem;
            color: rgba(136, 136, 136, 0.8);
        }
    
        .entry-seperator .header {
            margin: 2rem;
            font-size: 1.8rem;
            color: rgba(136, 136, 136, 0.8);
        }

        .login {
            background-color: #4ab04d;
            color: white;
            :hover {
                background-color: #39a63c;
            }
        }
    
        .sign-up {
            background-color: #31459b;
            color: white;
            &:hover {
                background-color: #293c8d;
                color: white;
            }
        }
    
        .row .cell {
            display: inline-block;
        }
    
        .row-2 .cell {
            width: 48%;
        }
    
        .cell {
            height: 3rem;
            margin-bottom: 2rem;
            button, a.btn {
                width: 80%;
                height: 3.5rem;
                padding: .5rem;
                font-size: 1.2rem;
            }
        }
    
        .partition {
            position: relative;
            margin: 1rem 0;
        
            .left-part, .right-part {
                border-bottom: 1.5px dashed rgba(140,133,133,0.8);
                width: 46%;
                top: 50%;
                position: absolute;
            }
            .left-part {
                left: 0;
            }
            .right-part {
                right: 0;
            }
        }
    }
}