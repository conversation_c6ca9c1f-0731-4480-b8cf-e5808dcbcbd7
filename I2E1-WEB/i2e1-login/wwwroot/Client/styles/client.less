@import 'commons';

html, body {
    height: 100%;
}

body {
    background-color: @background-color;
    font-size: 16px;
    margin: 0;

    .no-margin {
        margin: 0 !important;
    }

    .no-pad {
        padding: 0;
    }
    .no-top-pad {
        padding-top:0
    }

    .btn {
        border-radius: 0;
    }

    .left-align {
        text-align: left;
    }

    .right-align {
        text-align: right;
    }

    .center-align {
        text-align: center;
    }

    .btn-primary-green {
        background-color: #51b564;
        color: #fff !important;
        border-radius: 0;
        font-size: 1.2rem;
        padding: .4rem 2rem;
        text-transform: capitalize;

        &:hover {
            color: #fff;
            background-color: #52d36a;
            //border: .1rem solid #70c474;
        }
    }

    .active:focus, .active:active {
        outline: none;
    }
    .text-danger {
        color: #a94442;
    }
}

.bootstrap-dialog-header {
    &.alert-success {
        background-color: #dff0d8;
        border-color: #d0e9c6;
        color: #3c763d;

        h4, button {
            color: #3c763d;
            margin: 0;
        }
    }

    &.alert-info {
        background-color: #d9edf7;
        border-color: #bcdff1;
        color: #31708f;

        h4, button {
            color: #31708f;
            margin: 0;
        }
    }

    &.alert-warning {
        background-color: #fcf8e3;
        border-color: #faf2cc;
        color: #8a6d3b;

        h4, button {
            color: #8a6d3b;
            margin: 0;
        }
    }

    &.alert-danger {
        background-color: #f2dede;
        border-color: #ebcccc;
        color: #a94442;

        h4, button {
            color: #a94442;
            margin: 0;
        }
    }
}

.modal.fade .modal-dialog {
    -moz-transition: none !important;
    -o-transition: none !important;
    -webkit-transition: none !important;
    transition: none !important;
    -moz-transform: none !important;
    -ms-transform: none !important;
    -o-transform: none !important;
    -webkit-transform: none !important;
    transform: none !important;
}

.angular-drop-down-auto-complete {
    .angucomplete-holder {
        position: relative;
    }

    .angucomplete-dropdown {
        border-width: 1px;
        border-style: solid;
        border-radius: 2px;
        width: 100%;
        max-height: 20rem;
        padding: 6px;
        cursor: pointer;
        z-index: 9999;
        position: absolute;
        overflow-y: scroll;
        margin-top: -6px;
        background-color: #ffffff;
        border: 1px solid rgba(136, 136, 136, 0.5) !important;
        box-shadow: 1px 1px 0 0 rgba(136, 136, 136, 0.21);
    }

    .angucomplete-searching {
        color: #acacac;
        font-size: 14px;
    }

    .angucomplete-title {
        font-size: 1.4rem;
        font-weight: 500;
        color: #666666;
        padding: 1rem;
    }

    .angucomplete-description {
        font-style: italic;
        font-size: smaller;
        font-family: monospace;
        color: #303030;
    }

    .angucomplete-row {
        padding: 5px;
        margin-bottom: 4px;
    }

    .angucomplete-selected-row, .angucomplete-row:hover {
        background-color: lightblue;
        color: #ffffff;
    }

    .angucomplete-image-holder {
        padding-top: 2px;
        float: left;
        margin-right: 10px;
        margin-left: 5px;
    }

    .angucomplete-image {
        height: 34px;
        width: 34px;
        border-radius: 50%;
        border-color: #ececec;
        border-style: solid;
        border-width: 1px;
    }

    .angucomplete-image-default {
        /* Add your own default image here
         background-image: url('/assets/default.png');
        */
        background-position: center;
        background-size: contain;
        height: 34px;
        width: 34px;
    }
}

ul.tags {
    list-style: none;
    margin: 0;
    overflow: hidden;
    padding: 0;

    li {
        float: left;
    }

    .tag {
        background: #eee;
        border-radius: 3px 0 0 3px;
        color: #999;
        font-size: 1.2rem;
        display: inline-block;
        height: 26px;
        line-height: 26px;
        padding: 0 20px 0 23px;
        position: relative;
        margin: 0 10px 10px 0;
        text-decoration: none;
        -webkit-transition: color 0.2s;

        &:before {
            background: #fff;
            border-radius: 10px;
            box-shadow: inset 0 1px rgba(0, 0, 0, 0.25);
            content: '';
            height: 6px;
            left: 10px;
            position: absolute;
            width: 6px;
            top: 10px;
        }

        &:after {
            background: #fff;
            border-bottom: 13px solid transparent;
            border-left: 10px solid #eee;
            border-top: 13px solid transparent;
            content: '';
            position: absolute;
            right: 0;
            top: 0;
        }

        &:hover {
            background-color: crimson;
            color: white;

            &:after {
                border-left-color: crimson;
            }
        }
    }
}

a:focus {
    text-decoration: none;
    outline: unset;
}

button[disabled], html input[disabled] {
    cursor: not-allowed;
    opacity: .65;
}



#loader, .card-loader {
    .deactivate {
        position: fixed;
        width: 100%;
        height: 100%;
        background-color: white;
        top: 0;
        left: 0;
        opacity: 0.6;
        z-index: 99;
    }

    .img-section {
        @media @smartphones, @tablets {
            height: 75%;
        }

        position: fixed;
        left: 50%;
        top: 32%;
        z-index: 100;

        > div {
            position: relative;
            left: -50%;
        }
    }
}

.selectize-input {
    background: white !important;
    border-radius: 0.1rem !important;
    border: 1px solid rgba(136, 136, 136, 0.5) !important;
    box-shadow: 1px 1px 0 0 rgba(136, 136, 136, 0.21);
    max-height: 12rem;
    overflow: auto;

    &.selectize-focus {
        border: 1px solid rgba(136, 136, 136, 0.5) !important;
    }

    &.focus {
        box-shadow: 1px 1px 0 0 rgba(136, 136, 136, 0.21);
    }
    input.ui-select-search {
        width: 100% !important;
    }
}

.custom_scroll(@marginTop : 8rem, @maxHeight:30rem, @minHeight:30rem, @height: 1.2rem) {
    margin-top: @marginTop;
    max-height: @maxHeight;
    min-height: @minHeight;
    overflow-y: auto;
    width: 100%;

    &::-webkit-scrollbar {
        width: 12px;
        height:@height;
    }

    &::-webkit-scrollbar-track {
        -webkit-box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.3);
        -webkit-border-radius: 10px;
        border-radius: 10px;
    }

    &::-webkit-scrollbar-thumb {
        -webkit-border-radius: 10px;
        border-radius: 10px;
        background: #3e5b9d;
        -webkit-box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.5);
    }

    &::-webkit-scrollbar-thumb:window-inactive {
        background: #658be4;
    }
}


.blue_white_table {
    &table, & table {
        background-color: white;

        thead {
            background-color: #31459a;
            color: #fff;
        }
    }
}

.overlay {
    .position-tlbr(absolute, 0,0,0,0);
    background-color: rgba(251, 255, 0, 0.13);
    border-radius: 2px;
    z-index: 90;
    margin: -2px -5px;
    min-height: 40px;

    > div {
        position: absolute;
        top: 50%;
        text-align: center;
        margin-top: -35px;
        background-color: rgba(213, 251, 207, 0.8);
        border-radius: 10px;
        left: 49%;
        height: 70px;
        padding-top: 5px;

        > span {
            font-size: 24px;
        }
    }

    .overlayText {
        font-size: 10px;
        background-color: #fff;
        padding: 3px 8px;
    }
}



#admin_container {
    padding-bottom: 10rem;

    .editabe_box {
        input {
            &[type=text] {
                border: none;
                border-bottom: 2px solid rgba(205, 205, 205,1);
                padding: 0;
                font-size: 1.3rem;
                color: #010101;

                &:focus, &:active {
                    border-bottom: 2px solid #307ed6;
                }
            }
        }

        .btn-add-options {
            border-radius: 2px;
            background-color: #e1e1e1;
            box-shadow: 0px 1px 1px 0px rgba(7, 7, 7, 0.41);
            padding: .5rem 1rem;
            font-size: 1.2rem;
            color: #242424;
        }
    }

    input {
        &[type=text], &[type=number], &[type=password] {
            height: 3.2rem;
            box-shadow: none;
            background-color: #ffffff;
            border: solid 1px #b7b7b7;
            padding: 0.5rem 1rem;

            &:focus {
                box-shadow: none !important;
            }
        }
    }

    textarea, textarea.form-control {
        height: 10rem;
        box-shadow: none;
        background-color: #ffffff;
        border: solid 1px #b7b7b7;
        padding: 1rem;

        &:focus {
            box-shadow: none !important;
        }
    }
    input.setting-name-filter-input {
        border-color: #b8b8b8;
        height:3.6rem;
        font-size:1.3rem;
        &::-webkit-input-placeholder { /* Chrome/Opera/Safari */
          color: #666;
        }
        &::-moz-placeholder { /* Firefox 19+ */
          color: #666;
        }
        &:-ms-input-placeholder { /* IE 10+ */
          color: #666;
        }
        &:-moz-placeholder { /* Firefox 18- */
          color: #666;
        }
    }
}

.sub-container-background {
    height: 100%;
    min-height: 30rem;
    background-color: white;
    text-align: center;
    line-height: 1;
    padding: 1rem;
    margin-top: 1rem;
    overflow-x: auto;
    width: 100%;
    margin-bottom: 2rem;
    font-size: 1.8rem;
    border: @border-color;

    input {
        &[type=text], &[type=number], &[type=password] {
            height: 3.2rem;
            box-shadow: none;
            background-color: #ffffff;
            border: solid 1px #b7b7b7;
            padding: 1rem;

            &:focus {
                box-shadow: none !important;
            }
        }
    }

    h5, h6 {
        text-align: justify;

        i.glyphicon {
            color: rgba(136,136,136,0.80);
            vertical-align: text-top;
        }
    }

    h4.header {
        color: rgba(136,136,136,0.8);

        @media @smartphones, @tablets {
            font-size: 1.5rem;
        }
    }

    .sub-header {
        text-align: justify;
        position: relative;

        h5 {
            display: inline-block;

            @media @smartphones {
                margin: 0;
            }

            @media @smartphones, @tablets {
                font-size: 1.3rem;
            }
        }

        .define {
            font-size: 1.5rem;
            color: #ababab;
            display: inline-block;
            text-align: left;
            line-height: 1.4;
            width: 50rem;
        }

        .selectors {
            position: absolute;
            right: 0;
            top: 2.5rem;

            > div {
                padding: 0 1rem;
                width: 20rem;
                display: inline-block;

                @media @smartphones {
                    width: 9rem;

                    .selectize-input {
                        height: 2.5rem;
                        line-height: 1rem;

                        &:after {
                            top: 60%;
                            right: .25rem;
                        }
                    }
                }
            }

            .selectize-input {
                box-shadow: 1px 1px 0 0 #888888;

                &.selectize-focus {
                    border: 1px solid rgba(136, 136, 136, 0.5) !important;
                }
            }
        }
    }
}

.messege {
        position: absolute;
        width: 100%;
        top: 0;
        opacity: 0;
        -webkit-transition: all .3s ease-in-out;
        -moz-transition: all .3s ease-in-out;
        -o-transition: all .3s ease-in-out;
        transition: all .3s ease-in-out;
        text-align:center;

        &.popup {
            top: @title-height;
            opacity: 1;
            z-index: 9999;
            .appear {
                display: inline;
                padding: 1rem 2rem;
                border-radius: 1rem;
                font-size: 1.2rem;
                color: #333;
                font-weight: 500;
            }
        }

        span {
            white-space: nowrap;
            padding: 0 1rem;
            border-radius: 2px;
            display: none;
            -webkit-transition: top .5s ease-in-out;
            -moz-transition: top .5s ease-in-out;
            -o-transition: top .5s ease-in-out;
            transition: top .5s ease-in-out;
        }

        .success {
            background-color: rgb(110, 214, 114);
        }

        .error {
            background-color: #ec7979;
        }

        .info {
            background-color: #ffd800;
        }
    }

.title {
    .position-tlbr(fixed, 0, 0, 0, 0);
    background-color: white;
    text-align: center;
    height: @title-height;
    z-index: 10;
    box-shadow: 0 0.0625rem 0.3125rem 0 #888888;

    img {
        position: absolute;
        top: .5rem;
        left: 47%;
        height: 3.5rem;
        image-rendering: -moz-crisp-edges;
        image-rendering: -o-crisp-edges;
        image-rendering: -webkit-optimize-contrast;
        -ms-interpolation-mode: nearest-neighbor;
    }

    .signout {
        position: absolute;
        top: 0;
        right: 0;
        padding: .5rem 1rem;
        cursor: pointer;
        -webkit-transition: font-size .2s ease-in-out;
        -moz-transition: font-size .2s ease-in-out;
        -o-transition: font-size .2s ease-in-out;
        transition: font-size .2s ease-in-out;

        @media (pointer:coarse) {
            color: black;
            font-size: 2rem;
        }

        &:hover {
            color: black;
            font-size: 2rem;
        }
    }

    .user-row {
        line-height: 4.5rem;
        height: inherit;
        width: 20rem;
        float: left;
        color: black;

        .icon-area {
            width: @icon-width;
            position: absolute;
            left: 0;
            cursor: pointer;
        }

        .label-name {
            position: absolute;
            left: @icon-width;
            -webkit-transition: line-height .4s ease-in-out;
            -moz-transition: line-height .4s ease-in-out;
            -o-transition: line-height .4s ease-in-out;
            transition: line-height .4s ease-in-out;
        }

        .label-username {
            opacity: 0;
            position: absolute;
            left: 10rem;
            font-size: 1.2rem;
            top: 1rem;
            -webkit-transition: all .5s ease-in-out;
            -moz-transition: all .5s ease-in-out;
            -o-transition: all .5s ease-in-out;
            transition: all .5s ease-in-out;
        }

        &:hover {
            .label-username {
                opacity: 1;
                left: @icon-width;
                color: #666666;
            }

            .label-name {
                line-height: 3rem;
            }
        }
    }

    

    .links-group {
        @media @smartphones {
            display: none;
        }

        height: @title-height;
        line-height: 4.5rem;
        position: absolute;
        right: 5rem;
        background: rgba(136,136,136,0.80);

        .link {
            color: rgba(255, 255, 255, 0.75);
            margin: 0 1rem;

            &:hover {
                color: white;
                text-decoration: none;
                display: inline-block;
                padding: 0 1rem;
            }
        }
    }

    .brand-selector {
        height: 4.5rem;
        line-height: 1.5rem;
        position: absolute;
        right:20rem;
        width:20rem;
        text-align: left;
        .selectize-input{
            overflow-x: hidden;
            height: 4.5rem;
            display: flex;
            align-items: center;
        }
        &.has-error {
            .selectize-control.single .selectize-input {
                border:1px solid rgba(220, 53, 69, 0.75) !important;
                -webkit-box-shadow: 0 0 0 0.1rem rgba(220,53,69,.25);
                box-shadow: 0 0 0 0.1rem rgba(220,53,69,.25);
            }
        }
    }
    
        @media @smartphones {
            .brand-selector {
                right: 4rem;
                width: 10rem;
            }
        }
}

hr {
    border-top: @border-color;
}

//button.btn { border-radius: 1px; }

.backdrop {
    .position-tlbr(absolute, 0, 0, 0, 0);
    z-index: 5;
    background-color: rgba(0, 0, 0, 0.25);
}

.screen-detector {
    span {
        display: none;
        opacity: 0;
    }

    .normal {
        opacity: 1;
        display: inline-block;
    }

    @media @smartphones {
        span.smartphones {
            opacity: 1;
            display: inline-block;
        }

        span.normal {
            display: none;
            opacity: 0;
        }
    }

    @media @tablets {
        span.tablets {
            opacity: 1;
            display: inline-block;
        }

        span.normal {
            display: none !important;
            opacity: 0 !important;
        }
    }
}

.menu {
    display: none;
    position: absolute;
    top: 4.8rem;
    left: 6rem;
    z-index: 12;
    cursor: pointer;

    .icon-area {
        width: 100%;
        height: 100%;

        span {
            vertical-align: bottom;
        }
    }

    &:hover {
        color: black;
    }

    &.move-this {
        display: none;
    }

    @media (pointer:coarse) {
        display: inline-block;
    }

    @media @smartphones {
        left: 1.7rem;
        z-index: 25;
    }
}



.daterangepicker {
    background-color: @background-active-color;
    padding: 1rem;

    .ranges {
        background-color: white;
        padding: 1rem;
        line-height: 2.5rem;
        border-radius: 4px;

        .applyBtn {
            color: #4ab04d;
        }

        .btn-sm {
            width: 47%;
            margin: 0 1px;
            border-radius: 0;
            color: white;
        }

        .cancelBtn {
            background-color: grey;
        }

        li {
            border-radius: 0;
            border: none;
            color: unset;

            &.active {
                border: none;
                color: black;
                text-shadow: @active-text-shadow;
                background-color: @background-active-color;
            }

            &:hover {
                border: none;
                background-color: @background-color;
                color: black;
            }
        }
    }

    .daterangepicker_input {
        background-color: white !important;
        margin-bottom: 1rem;
    }

    .input-mini {
        border: 0;
        border-radius: 0;
        background-color: white;
        -webkit-transition: unset;
        -moz-transition: unset;
        -o-transition: unset;
        transition: unset;

        &.active {
            border: 0;
            border-radius: 0;
            background-color: white;
        }

        &:focus {
            box-shadow: none;
        }
    }

    .calendar-table {
        border: none !important;
        border-radius: 4px !important;
    }

    td {
        &.in-range {
            background-color: @background-color;
            color: black;
            text-shadow: @active-text-shadow;
        }

        &.active, &.active:hover {
            background-color: @background-active-color;
            color: black;
            text-shadow: @active-text-shadow;
        }
    }

    .calendar.left {
        margin-right: 1rem !important;
    }
}

.container {
    position: absolute;
    top: @title-height + .1rem;
    //margin-top: @title-height + .1rem;
    left: 0;
    bottom: 0;
    overflow-y: auto;
    padding: 2rem 6rem 0 10rem;
    width: 100%;
    min-width: 36rem;
    z-index: 4;

    @media @smartphones {
        padding: 2rem 3rem 0 6rem;
    }

    .no-location-selected {
        text-align: center;
    }

    .date-range-picker {
        height: 6rem;
        width: 100%;
        margin-bottom: 2rem;

        .date-range-btn {
            .btn-primary {
                background-color: red;
                border-radius: 0.1rem;

                &:active {
                    background-color: rgba(255, 0, 0, 0.5);
                    background-image: none;
                }
            }

            @media @smartphones {
                .btn-primary {
                    width: inherit;
                    font-size: 1.15rem;
                }
            }
        }

        > div {
            display: inline-block;
            height: 95%;
            vertical-align: bottom;
        }

        .from-to-dates {
            width: 17rem;
            margin-left: 4rem;
            vertical-align: super;

            .date {
                float: right;
                width: 12rem;
                color: green;
                font-style: italic;
                font-size: 1.3rem;
                font-family: monospace;
            }

            @media @smartphones {
                width: 14rem;

                .date {
                    width: 9rem;
                }
            }

            > div {
                line-height: 3rem;
            }
        }

        .about-period {
            float: right;
            vertical-align: top;

            > div {
                width: 22rem;
            }

            span {
                float: right;
                width: 3rem;
                color: green;
                font-style: italic;
                font-family: monospace;
            }
        }

        @media @smartphones {
            text-align: justify;
        }
    }
}


.selects {
    width: 100%;

    textarea:focus, textarea.form-control:focus, input.form-control:focus, input[type="text"]:focus, input[type="password"]:focus, input[type="email"]:focus, input[type="number"]:focus, .form-control[type="text"]:focus, .form-control[type="password"]:focus, .form-control[type="email"]:focus, .form-control[type="tel"]:focus, .form-control[contenteditable]:focus, select:focus, select.form-control:focus {
        box-shadow: none !important;
    }

    i.glyphicon {
        opacity: .5;
        position: absolute;
        z-index: 999;
        left: .5rem;
        top: 1rem;

        &:hover {
            opacity: 0.8;
        }

        @media @smartphones {
            left: 7rem;
        }
    }

    > .ui-select-container {
        width: 100%;
        margin-bottom: 1rem;

        .caret {
            display: none !important;
        }

        .selectize-input {
            padding-left: 3rem;
        }

        .selectize-input:after {
            position: unset;
            margin-top: 0;
            border-style: unset;
        }
    }

    > .selectize-control {
        &.single .selectize-input [data-value] {
            background-color: transparent;
            border: none;
            padding-right: 0 !important;
        }

        .selectize-input {
            padding-left: 3rem !important;

            [data-value] {
                background-image: none;
                background-color: @background-color;
                color: #666666;
                text-shadow: unset;
                border-radius: 2px;
                border: @border-color;
                box-shadow: none;

                &.item {
                    .storeName {
                        font-weight: bold;
                    }

                    .city {
                        font-style: italic;
                        font-size: smaller;
                        font-family: monospace;
                    }
                }

                &.active {
                    background-image: none;
                    background-color: @background-active-color;
                    border: @border-color;
                    color: black;
                }

                .remove {
                    border-left: @border-color;
                }
            }

            &::after {
                content: none;
            }
        }

        .selectize-dropdown {
            .selectize-dropdown-content {
                .create.active,
                [data-value].active {
                    background-image: none;
                    background-color: @background-color;
                }

                [data-value] {
                    .primary {
                    }

                    .secondary {
                        font-size: 1rem;
                        opacity: 0.8;

                        span {
                            margin-right: 2rem;
                        }
                    }
                }
            }
        }
    }

    .location {
        > .ui-select-container {
            display: inline-block;
            margin-right: 2rem;
            width: 30rem;
        }
    }
}

.filters {
    .filter-row-1 .input-group {
        margin-top: 2rem;
        background: #fff;
        border: 1px solid #ccc;
    }

    .filter-row-1 .form-control {
        border: none;
        box-shadow: none;
    }

    .filter-row-2 {
        margin-top: 20px;
        .apply-to-select;

        .select2-container-multi .select2-choices .select2-search-field input {
            padding: 2px 5px;
        }
    }

    .filter-row-2 .col-md-3:nth-of-type(1) {
        padding: 0;
    }

    .filter-row-2 .col-md-3 {
        padding-right: 0;
    }

    .filter-row-2 .col-md-3 > label:nth-of-type(1) {
        margin-right: 25px;
    }

    .filter-row-2 .form-control {
        border: 1px solid #ccc;
        padding: 5px;
        background-color: #fff;
        box-shadow: none;
    }

    .filter-row-stats {
        margin-top: 2rem;

        .devices_info {
            padding: 1.5rem;

            h3 {
                margin: 0 0 15px 0;
                font-size: 2.4rem;
            }

            background-color: #fff;
        }

        .devices {
            text-align: center;

            h4 {
                font-size: 1.6rem;
            }
        }
    }
}

.setting-menu, .question-menu {
    position: absolute;
    top: 0;
    right: 1rem;

    .transfer-input input {
        width: 14rem;
        height: 100%;
        font-size: 1.2rem;
        padding-left: 1rem;
    }

    button {
        background: transparent;
        box-shadow: none;
        border-radius: 1.5rem;
        border: none;
        outline: none;
        font-weight: 100;
        font-size: 1.2rem;
    }

    ul {
        list-style: none;
        padding-left: 1rem;
        margin-bottom: 0;
        width: 10rem;

        li {
            width: inherit;
        }
    }
}

.select-filters {
    .row {
        margin: 0;
    }

    &.top-filter {
        .selectize-input input[type=text] {
            height: auto !important;
        }
    }

    .selectize-dropdown {
        border: 1px solid rgba(136, 136, 136, 0.5) !important;
        box-shadow: 1px 1px 0 0 rgba(136, 136, 136, 0.21);
    }

    .input-group-addon {
        position: absolute;
        left: 0;
        z-index: 1;
        top: .7rem;
    }


    .locations-selector {
        font-size: 1.2rem;
        .apply-to-select;
        .select2-container-multi .select2-choices .select2-search-field input {
            min-height: 3.4rem;
        }
    }
}

.message-container {
    background-color: transparent;

    div {
        font-weight: bold;
        font-size: 1.2rem;
        padding: .2rem 1rem;
        margin-top: 1rem;

        &.failure {
            background-color: white;
            color: #e75349;
            border: .2rem solid #e75349;
        }

        &.success {
            color: white;
            background-color: #5bb05d;
            border: .2rem solid #5bb05d;
        }

        img {
            width: 1.5rem;
            margin-right: 1rem;
            vertical-align: sub;
        }
    }
}

.impersonate-input {
    width: 20rem;
    height: 3rem;
    margin-left: 6.5rem;

    input {
        height: 100%;
        width: 100%;
        font-size: 1.2rem;
        padding-left: 1rem;
    }
}

.apply-to-select {
    .ui-select-input-group {
        background: white !important;
        border-radius: 0 !important;
        border: .1rem solid rgba(136, 136, 136, 0.5) !important;
        //box-shadow: 1px 1px 0 0 rgba(136, 136, 136, 0.21);
        &.error {
            border: .1rem solid #a94442 !important;
        }

        .input-group-addon {
            opacity: 0.8;
            position: absolute;
            z-index: 999;
            top: .4rem;
            color: #000000;
        }

        .select2-search-field:focus {
            outline: none;
        }

        .select2-drop-active {
            border: .125rem solid rgba(136, 136, 136, 0.5) !important;
            box-shadow: .125rem .125rem 0 0 rgba(136, 136, 136, 0.21);
            border-radius: 0;
        }

        .select2-drop.select2-drop-above.select2-drop-active {
            border-top: .125rem solid rgba(136, 136, 136, 0.5) !important;
        }

        .select2-results {
            max-height: 20rem;
            padding: 0;
            margin: 0;
            position: relative;
            overflow-x: hidden;
            overflow-y: auto;

            .select2-highlighted {
                background: #f5f5f5;
                color: #000;
            }

            .select2-result-label {
                outline: none;
            }
        }

        .ui-select-choices-row {
            &:hover {
                background-color: #f5f5f5;
            }

            &:focus {
                outline: none;
            }
        }
        .select2-container-multi {
            &.select2-container-disabled .select2-choices {
                background-color: #f4f4f4;
                background-image: none;
                cursor: not-allowed;
            }

             .select2-choices {
                height: auto !important;
                height: 1%;
                margin: 0;
                padding: 0;
                position: relative;
                border: none;
                cursor: text;
                overflow: hidden;
                background-color: rgb(255,255,255);
                border-radius: 0;
                max-height: 90px;
                overflow-y: auto;
                min-height: 3.4rem;

                .select2-search-choice {
                    background-image: none;
                    background-color: #f6f6f6;
                    color: #666666;
                    text-shadow: unset;
                    border-radius: 0;
                    border: 1px solid rgba(136, 136, 136, 0.21);
                    padding: .25rem;

                    span span {
                        font-weight: 500;
                        height: 100%;
                        position: relative;
                        border-right: 1px solid rgba(136, 136, 136, 0.21);
                        display: inline-block;
                        padding: .4rem .6rem;
                    }

                    .select2-search-choice-close {
                        position: relative;
                        float: right;
                        display: block;
                        //top: .7rem;
                        //left: .7rem;
                    }

                    .select2-search-field {
                        width: 10rem;

                        input {
                            width: 10rem !important;
                        }
                    }
                }
            }
        }
    }
}


@-webkit-keyframes webkit-refresh-spin {
    from { -webkit-transform: rotate(0deg);}
    to { -webkit-transform: rotate(360deg);}
}

@keyframes refresh-spin {
    from { transform: scale(1) rotate(0deg);}
    to { transform: scale(1) rotate(360deg);}
}

.locations-selector {
    .apply-to-select;
    .cell.location {
        position: relative; 
        .select2-search-field {
            //width:100%;
            font-size: 1.3rem;
            .select2-input.ui-select-search {
                //width: 100% !important;
                font-size: 1.3rem;
            }
        }
        .ui-select-input-group .select2-drop-active {
            font-size:1.3rem;
        }
        .glyphicon-refresh-animate {
            position: absolute;
            top: 12px;
            right: 25px;
            -animation: refresh-spin .7s infinite linear;
            -webkit-animation: webkit-refresh-spin .7s infinite linear;
        }
        .ui-select-all-and-remove-all-button-group {
            width: 100%;
            padding: 1rem;
            .btn {
                width:50%;
                color: #292b2c;
                background-color: #fff;
                border-color: #ccc;
            }
        }
    }
}
 
.page-header {
    padding-bottom: 0;
    margin: 1rem 0 2.5rem;
    border-bottom: none;

    h4 {
        color: #525252;
        font-size: 2.4rem;
    }
}

.config-container {
    padding: 1.5rem;
    border: .1rem solid #ccc;
    background: rgba(204,204,204,0.50);
    margin-bottom: 20px;
    box-shadow: 0px 1px 2.9px 0.1px rgba(7, 7, 7, 0.26);
}

.home-container {
    height: auto !important;
    position: relative;
    padding: 0 !important;
    .sub-container-background;

    .label {
        color: inherit;
    }

    #router_details {
        overflow: hidden;
    }

    #nas-list {
        font-size: 1.2rem;
    }

    .fixed {
        top: 0;
        position: fixed;
        width: auto;
        display: none;
        border: none;
    }

    #nas-list > thead {
        background: #31459a;
        color: #fff;
    }

    #nas-list > tbody {
        text-align: left;
    }

    .circle_success {
        width: 15px;
        height: 15px;
        background: -moz-linear-gradient(top, #4caf50, #4aa921);
        background: -webkit-gradient(linear, left top, left bottom, from(#4caf50), to(#4aa921));
        -moz-border-radius: 8px;
        -webkit-border-radius: 8px;
        border-radius: 8px;
        border: 0px solid #000000;
        -moz-box-shadow: 0px 1px 3px rgba(0, 0, 0, 0.5), inset 0px 0px 1px rgba(255, 255, 255, 0.7);
        -webkit-box-shadow: 0px 1px 3px rgba(0, 0, 0, 0.5), inset 0px 0px 1px rgba(255, 255, 255, 0.7);
        box-shadow: 0px 1px 3px rgba(0, 0, 0, 0.5), inset 0px 0px 1px rgba(255, 255, 255, 0.7);
        margin: auto;
        display: inline-block;
        vertical-align: middle;
    }

    .row_danger {
        background: rgba(229, 28, 35, 0.23);
    }

    .row_warning {
        background: #fbd69196
    }

    .circle_danger {
        height: 15px;
        width: 15px;
        background: -moz-linear-gradient(top, #d4113b, #bf0b23);
        background: -webkit-gradient(linear, left top, left bottom, from(#d4113b), to(#bf0b23));
        -moz-border-radius: 5px;
        -webkit-border-radius: 5px;
        border-radius: 7px;
        border: 0 solid #000000;
        -moz-box-shadow: 0 1px 3px rgba(0, 0, 0, 0.5), inset 0 0 1px rgba(255, 255, 255, 0.7);
        -webkit-box-shadow: 0px 1px 3px rgba(0, 0, 0, 0.5), inset 0 0 1px rgba(255, 255, 255, 0.7);
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.5), inset 0 0 1px rgba(255, 255, 255, 0.7);
        display: inline-block;
        vertical-align: middle;
    }

    .center_aligned {
        text-align: center;
    }

    a.edit_link {
        color: #666666;

        &:hover {
            color: black;
            font-size: 1.5rem;
            text-decoration: none;
        }
    }
}

.modal-content {
    .storeEditHeader {
        padding: .7rem 3rem;
        background-color: #0c4da2;

        h4 {
            font-size: 2rem;
            font-weight: 300;
            font-style: normal;
            font-stretch: normal;
            text-align: left;
            color: #ffffff;
        }
    }

    .storeEditBody {
        background-color: #fff;
        padding-bottom: 2.5rem;

        ul.nav.nav-tabs > li {
            text-align: center;
            background-color: #ededed;
            box-shadow: inset 0 0 4px 0 rgba(0, 0, 0, 0.45);

            a {
                font-size: 1.5rem;
                font-weight: normal;
                font-style: normal;
                font-stretch: normal;
                color: #7b7b7b;
            }
        }

        ul.nav.nav-tabs > li.active {
            background-color: #ffffff;
            box-shadow: none;
            border: none;

            a {
                color: #000000;
                border: none;
                -webkit-box-shadow: none;
                box-shadow: none;
            }
        }

        .tab-content {
            /*.apply-to-select;*/
            .ui-select-container {
                .selectize-input {
                    overflow: hidden;
                    &:after {
                        //border-style: none;
                    }
                }

                .selectize-dropdown {
                    margin: -7px 0 0 0;
                    -webkit-border-radius: 0;
                    -moz-border-radius: 0;
                    border-radius: 0;
                    -webkit-box-shadow: none;
                    box-shadow: none;
                    -webkit-box-sizing: border-box;
                    .active {
                        background-color:#f5f5f5;
                    }
                }
            }
            padding: 2rem 6rem;

            .half-container {
                padding: 1.5rem 3rem;
            }

            .form-group {
                margin-bottom: 1rem;
            }

            .control-label {
                font-size: 1.2rem;
                font-weight: normal;
                font-style: normal;
                font-stretch: normal;
                line-height: 0.38;
                text-align: left;
                color: #000000;
            }

            input.form-control {
                height: 3.2rem;
                background-color: #fff;
                border: solid 1px #b7b7b7;
                -webkit-appearance: none;
                -webkit-box-shadow: none;
                box-shadow: none;
                font-size: 1.2rem;
                padding: 1rem;
                border-radius: .2rem;
            }

            select.form-control {
                height: 3.2rem;
                background-color: #fff;
                border: solid 1px #b7b7b7;
                -webkit-appearance: none;
                -webkit-box-shadow: none;
                box-shadow: none;
                font-size: 1.2rem;
                padding: 0 1rem;
                border-radius: .2rem;
            }

            select.form-control:disabled {
                background-color: #eee !important;
                opacity: .7;
                font-weight: bold;
            }

            textarea.form-control {
                height: 10.5rem;
                background-color: #fff;
                border: solid 1px #b7b7b7;
                -webkit-appearance: none;
                -webkit-box-shadow: none;
                box-shadow: none;
                font-size: 1.2rem;
                padding: 0 1rem;
                border-radius: .2rem;
            }

            textarea:focus, textarea.form-control:focus, input.form-control:focus, input[type="text"]:focus, input[type="password"]:focus, input[type="email"]:focus, input[type="number"]:focus, .form-control[type="text"]:focus, .form-control[type="password"]:focus, .form-control[type="email"]:focus, .form-control[type="tel"]:focus, .form-control[contenteditable]:focus, select:focus, select.form-control:focus {
                background-color: #fff;
                border: .1rem solid #0c4da2;
                box-shadow: 0 0 3px 0.1px #0c4da2 !important;
            }

            .has-error .help-block, .has-error .control-label, .has-error .radio, .has-error .checkbox, .has-error .radio-inline, .has-error .checkbox-inline, .has-error.radio label, .has-error.checkbox label, .has-error.radio-inline label, .has-error.checkbox-inline label {
                color: #a94442;
            }

            .has-error .form-control {
                border-color: #a94442;
                -webkit-box-shadow: inset 0 1px 1px rgba(0,0,0,0.075);
                box-shadow: inset 0 1px 1px rgba(0,0,0,0.075);
            }
        }
    }

    .storeFooter {
        padding: 5rem 9rem;
    }

    .btn-primary {
        /*width: 10rem;*/
    }

    .btn-warning {
        width: 10rem;
        color: #4a4a4a;
        background-color: #ededed;
        box-shadow: 0px 1px 1px 0px rgba(7, 7, 7, 0.26);
    }

    .auto-file-upload.preview-section {
        .preview-container {
            .partner_logo_result_container {
                text-align: left;
                img.partnerLogoImage {
                    max-width:50%;
                    margin:auto;
                }
            }
        }
        .upload_buttons_hidden {
            display:none;
        }
        /*.upload_buttons {
            margin: 1rem auto;
        }*/
    }
}



.settings-container {
    max-width: 100%;
    margin: auto;

    .selectize-input {
        padding: 0 1.5rem !important;
    }

    .page-header;

    .row {
        margin: 0;
    }

    .custom-btn {
        background: transparent;
        width: 100%;
        border: .1rem solid #ccc;
        text-align: right;
    }

    .custom-input {
        border: .1rem solid #ccc;
        border-radius: 0;
        box-shadow: none;
        text-align: right;
        padding: 1rem;
    }

    .custom-input:focus, select:focus {
        box-shadow: none !important;
    }

    .no_data_found {
        text-align: center;
    }

    .settings.container-fluid {
        .config-container;

        h5 {
            color: #525252;
            margin: 1rem 0 1.5rem 0;
            font-size: 1.6rem;
        }

        .settings-group {
            margin: 0 0 3rem;

            .settings-panel-heading {
                border-color: #dddddd;
                padding: 0;
                border-bottom: 1px solid #ddd;

                .panel-title {
                    a {
                        width: 100%;
                        display: inherit;
                        color: #525252;
                        font-size: 1.6rem;
                        line-height: 1.2rem;
                        position: relative;
                        padding: 1.5rem;

                        &:hover {
                            text-decoration: none;
                        }

                        .icon-arrow {
                            width: 3rem;
                            height: 3rem;
                            position: absolute;
                            right: .5rem;
                            top: .6rem;

                            &:before {
                                background-size: 3rem 3rem;
                                display: block;
                                width: 3rem;
                                height: 3rem;
                                content: "";
                                background: transparent url(../../images/up_arrow_16.png) scroll no-repeat center;
                            }
                        }

                        &.collapsed {
                            .icon-arrow:before {
                                background-size: 3rem 3rem;
                                display: block;
                                width: 3rem;
                                height: 3rem;
                                content: "";
                                background: transparent url(../../images/down_arrow_16.png) scroll no-repeat center;
                            }
                        }
                    }
                }
            }

            .settings-panel-body.panel-body {
                box-shadow: 0 1px 5.9px 0.1px rgba(7, 7, 7, 0.26);
                padding: 0;
                background: #fff;
                margin: 0;

                ul.nav.nav-tabs {
                    li {
                        height: 5rem;

                        a {
                            font-size: 1.5rem;
                            font-weight: normal;
                            font-style: normal;
                            font-stretch: normal;
                            text-align: left;
                            color: #7b7b7b;
                            border: none;
                            color: #666666;
                            -webkit-box-shadow: none;
                            box-shadow: none;
                        }

                        &.active {
                            height: 5rem;
                            background-color: #49b14f;
                            border: solid 1px #49b14f;

                            a {
                                font-size: 1.5rem;
                                font-weight: normal;
                                font-style: normal;
                                font-stretch: normal;
                                text-align: left;
                                color: #fff;
                                border: none;
                                -webkit-box-shadow: none;
                                box-shadow: none;
                            }
                        }
                    }
                }

                .tab-content .tab-pane {
                    padding: 3rem;
                    position: relative;
                    float: left;
                    box-shadow: 0 2px 5.8px 0.2px rgba(7, 7, 7, 0.3);
                    width: 100%;

                    .template-uiselect {
                        .apply-to-select;
                    }

                    .config-row {
                        font-size: 1.31rem;
                        margin-bottom: 2rem;

                        .info-question-sign {
                            top: .2rem;
                            cursor: help;
                        }

                        .groupname {
                            width: inherit;
                            display: inline;
                        }

                        button.b1 {
                            width: 4rem;
                            height: 4rem;
                            padding: 0;
                            outline: none;
                            border-radius: 2rem;
                            margin-left: 1rem;
                            font-size: 1.6rem;
                            font-weight: 600;
                            line-height: 1rem;
                        }
                    }

                    .previous-version {
                        .board .board-inner {
                            .advance-config-row, .save-row {
                                display: none;
                            }

                            .nav-tabs {
                                position: relative;
                                padding: 0 40rem;
                                margin-bottom: 0;
                                box-sizing: border-box;

                                .liner {
                                    height: 2px;
                                    background: #ddd;
                                    position: absolute;
                                    width: 80%;
                                    margin: 0 auto;
                                    left: 0;
                                    right: 0;
                                    top: 1.4rem;
                                    z-index: 1;
                                }

                                > li {
                                    height: 3rem;
                                    margin: 0 .5rem;
                                    background: transparent;
                                    border: none;

                                    a {
                                        width: 3rem;
                                        height: 3rem;
                                        margin: auto;
                                        border-radius: 100%;
                                        padding: 0;

                                        &:hover {
                                            background: transparent;
                                        }
                                    }
                                }

                                > li.active > a, > li.active > a:hover, > li.active > a:focus {
                                    color: #555555;
                                    cursor: default;
                                    border: 0;
                                    border-bottom-color: transparent;

                                    span.round-tabs {
                                        font-size: 2rem;
                                        color: rgb(34, 194, 34);
                                    }
                                }

                                span.round-tabs {
                                    width: 3rem;
                                    height: 3rem;
                                    line-height: 2.8rem;
                                    display: inline-block;
                                    background: white;
                                    z-index: 2;
                                    position: absolute;
                                    left: 0;
                                    text-align: center;
                                    font-size: 1.5rem;
                                    color: rgb(34, 194, 34);
                                    text-decoration: underline;
                                }
                            }
                        }
                    }
                }
            }
        }
    }


    select {
        height: 3.2rem;
        padding: .5rem;
        font-size: 1.3rem;
        box-shadow: 0px 1px 1px 0px rgba(7, 7, 7, 0.41);
        border: solid 1px rgba(205, 205, 205, 0.55);
    }

    .config-alerts, .ops-hours {
        select:required:invalid {
            color: #c3c3c3;
        }

        option {
            color: #000000;
        }

        option[value=""][disabled] {
            display: none;
        }

        h6 {
            font-family: Roboto;
            font-size: 1.51rem;
            font-weight: normal;
            font-style: normal;
            font-stretch: normal;
            line-height: 0.33;
            text-align: left;
            color: #4c4c4c;
            margin: 4.5rem 0;
        }

        .row {
            margin: 0 -15px;

            label {
                font-family: Roboto;
                font-size: 1.31rem;
                font-weight: 500;
                font-style: normal;
                font-stretch: normal;
                line-height: 3;
                text-align: left;
                color: #000000;
            }
        }

        select {
            min-width: 48%;
            background-color: #fff;
            box-shadow: 0px 1px 1px 0px rgba(7, 7, 7, 0.41);
            border: solid 1px rgba(205, 205, 205, 0.55);
            margin-right: -0.3rem;
            height: 3rem;
            font-family: Roboto;
            font-size: 13px;
            font-weight: normal;
            font-style: normal;
            font-stretch: normal;
            line-height: 1.62;
            text-align: left;
            color: #000000;
        }

        .label_info {
            color: #ea4438;
        }
    }

    .time-selector {
        margin: .5rem 0;
        line-height: 3.2rem;
    }

    .config-box {
        background: #fff;
        padding: 1.5rem;
        color: #000;
        font-size: 1.2rem;
        border: none;
        border-radius: .2rem;
        -webkit-box-shadow: 0 .1rem .4rem rgba(0,0,0,0.3);
        box-shadow: 0 .1rem .4rem rgba(0,0,0,0.3);
    }


    .col-md-6.config-box {
        width: 49%;
    }

    .config-row > h5 {
        font-size: 1.6rem;
    }

    .config-box .row {
        margin: 0;
        padding: .5rem 0;
    }

    .config-box.access-list {
        padding: 0;
    }

    .config-box.access-list .settings {
        padding: 1.2rem 0;
        border-bottom: .1rem solid #ccc;
    }

    .pad-6 {
        padding: 0 6rem;
    }

    .seperator {
        height: .1rem;
        background: #E1E1E1;
        margin-top: 1.5rem;
    }

    .form-element {
        background-color: #fff;
        padding: .5rem 1.0rem;
    }

    .collapse, .collapsing {
        //border-bottom: .1rem solid #ccc;
        //padding:1.5rem;
        //background-color: #f5f5f5;
    }

    .add-btn {
        background-color: #2fd42f;
        color: #fff;
    }

    .btn-container {
        padding: 2.0rem;
        text-align: center;
    }

    .btn-danger {
        margin-left: 2.0rem;
    }



    .no_data_used_p {
        text-align: center;
        margin: .5rem;
    }

    .circle_success {
        width: 1.5rem;
        height: 1.5rem;
        background: -moz-linear-gradient(top, #4caf50, #4aa921);
        background: -webkit-gradient(linear, left top, left bottom, from(#4caf50), to(#4aa921));
        -moz-border-radius: .75rem;
        -webkit-border-radius: .75rem;
        border-radius: .75rem;
        border: 0 solid #000000;
        -moz-box-shadow: 0px 1px 3px rgba(0, 0, 0, 0.5), inset 0px 0px 1px rgba(255, 255, 255, 0.7);
        -webkit-box-shadow: 0px 1px 3px rgba(0, 0, 0, 0.5), inset 0px 0px 1px rgba(255, 255, 255, 0.7);
        box-shadow: 0px 1px 3px rgba(0, 0, 0, 0.5), inset 0px 0px 1px rgba(255, 255, 255, 0.7);
        display: inline-block;
        vertical-align: middle;
    }

    .circle_danger {
        width: 2rem;
        height: 2rem;
        background: -moz-linear-gradient( top, #e1e1e1, #f6f6f6);
        background: -webkit-gradient(linear, left top, left bottom, from(#e1e1e1), to(#f6f6f6));
        -moz-border-radius: 1rem;
        -webkit-border-radius: 1rem;
        border-radius: 1rem;
        border: 0 solid #000000;
        -moz-box-shadow: 0px 1px 3px rgba(000,000,000,0.5), inset 0px 0px 1px rgba(255,255,255,0.7);
        -webkit-box-shadow: 0px 1px 3px rgba(000,000,000,0.5), inset 0px 0px 1px rgba(255,255,255,0.7);
        box-shadow: 0px 1px 3px rgba(000,000,000,0.5), inset 0px 0px 1px rgba(255,255,255,0.7);
    }

    .center_aligned {
        text-align: center;
    }

    .authentication_group {
        select {
            border: .1rem solid #ccc;
            background-color: #fff;
            box-shadow: none;
            font-size: 1.4rem;
            padding: 1rem;
        }
    }

    .admin-ops-selection {
        select {
            border: .1rem solid #ccc;
            background-color: #fff;
            box-shadow: none;
            font-size: 1.4rem;
            padding: 1rem;
        }

        input[type=text] {
            background-color: #fff;
            border: .1rem solid #ccc;
            padding: 1rem;
            box-shadow: none;
            font-size: 1.4rem;
        }
    }

    .add-new-group {
        display: inline-block;
    }

    .add-new-group .dropdown-menu {
        max-width: 30rem;
        min-width: 20rem;
        padding: 1.5rem 1.5rem 1rem 1.5rem;

        legend {
            box-sizing: border-box;
            color: inherit;
            display: table;
            max-width: 100%;
            padding: 0;
            white-space: normal;
            border-bottom: none;
            margin-bottom: 1rem;
        }

        .btn-primary-b1 {
            width: 3rem;
            height: 3rem;
            padding: 0;
            border-radius: 1.5rem;
        }

        .btn-primary-green.g1 {
            width: 3rem;
            height: 3rem;
            padding: 0;
            border-radius: 1.5rem;
            margin-top: -0.3rem;
            margin-left: 1rem;
        }

        .footer-btn-group {
            margin: 2rem 0;
        }

        .form-inline {
            width: 30rem;

            &:first-child {
                margin-right: 10%;
            }

            .form-group-textfield {
                display: inline-block;
                width: 12rem;
                padding: 0;
                vertical-align: top;
            }
        }

        .form-group-textfield {
            display: block;
            padding-top: 1.5rem;
            margin-bottom: 2rem;
            position: relative;

            > input, > textarea {
                display: block;
                background-color: transparent;
                color: rgba(0,0,0,.87);
                border: none;
                border-bottom: 1px solid rgba(0,0,0,.26);
                outline: 0;
                width: 90%;
                font-size: 1.2rem;
                padding: 0;
                box-shadow: none;
                border-radius: 0;
                background-image: none;

                &:focus {
                    border-color: #2196F3;
                    border-width: 2px;
                }
            }
        }
    }

    .question-area {
        button:not(.btn) {
            background: transparent;
            box-shadow: none;
            border-radius: 1.5rem;
            border: none;
            outline: none;
            font-weight: 100;
            font-size: 1.2rem;
        }

        .editabe_box {
            padding: 1rem 1.5rem;
            margin: 2rem 0 0 0;
            box-shadow: 0 1px 1px 0 rgba(7, 7, 7, 0.41);
            border: solid 1px rgba(205, 205, 205, 0.55);
            cursor: pointer;

            &:hover {
                box-shadow: 0 1px 1px 0 rgba(7, 7, 7, 0.8);

                .question-bar {
                    font-weight: bold;
                }
            }

            &.template-0 {
                background: #f6f6f6;
            }

            &.moved-true {
                background: #dcdbdb;
            }

            &.moved-false {
                -webkit-transition: background .5s; /* Safari */
                -o-transition: background .5s;
                -moz-transition: background .5s;
                transition: background .5s;
            }

            &.dependent-question {
                width: 95%;
                float: right;
                margin: 0.5rem 0 0 0;
            }

            .question-bar {
                .question-menu {
                    position: unset;

                    button {
                        padding: 0;
                    }
                }
            }

            .option {
                margin: .5rem 1rem;

                input {
                    padding: 0 .5rem !important;
                    border: 1px solid #c6c6c6 !important;
                }
            }

            h5 {
                margin: 0 0 !important;
                font-family: Roboto;
                font-size: 1.3rem !important;
                font-weight: normal;
                font-style: normal;
                font-stretch: normal;
                line-height: normal;
                letter-spacing: normal;
                color: #4b4b4b;
            }

            h6 {
                font-family: Roboto;
                font-size: 1.3rem;
                font-weight: normal;
                font-style: normal;
                font-stretch: normal;
                line-height: normal;
                letter-spacing: normal;
                color: #4b4b4b;
                margin: 1.5rem 0;
            }
        }
    }


    .splashImage {
        width: 13rem;
    }

    .uploader {
        border: @border-color;
        padding: 1rem;


        .browser {
            cursor: pointer;
        }
    }
}

.basiconfig-container {
    max-width: 100%;
    margin: auto;
    .page-header;

    .row {
        margin: 0;
    }

    .custom-btn {
        background: transparent;
        width: 100%;
        border: .1rem solid #ccc;
        text-align: right;
    }

    .custom-input {
        border: .1rem solid #ccc;
        border-radius: 0;
        box-shadow: none;
        text-align: right;
        padding: 1rem;
    }

    .custom-input:focus, select:focus {
        box-shadow: none !important;
    }

    .container-fluid {
        .config-container;
    }

    .config-box {
        background: #fff;
        padding: 1.5rem;
        color: #000;
        font-size: 1.2rem;
        border: none;
        border-radius: .2rem;
        -webkit-box-shadow: 0 .1rem .4rem rgba(0,0,0,0.3);
        box-shadow: 0 .1rem .4rem rgba(0,0,0,0.3);
    }


    .col-md-6.config-box {
        width: 49%;
    }

    .config-row > h5 {
        font-size: 1.6rem;
    }

    .config-box .row {
        margin: 0;
        padding: .5rem 0;
    }

    .config-box.access-list {
        padding: 0;
    }

    .config-box.access-list .settings {
        padding: 1.2rem 0;
        border-bottom: .1rem solid #ccc;
    }

    .seperator {
        height: .1rem;
        background: #ccc;
        margin-top: 1.5rem;
    }

    .form-element {
        background-color: #fff;
        padding: .5rem 1.0rem;
    }

    .collapse, .collapsing {
        border-bottom: .1rem solid #ccc;
        padding: 1.5rem;
    }

    .add-btn {
        background-color: #2fd42f;
        color: #fff;
    }

    .btn-container {
        padding: 2.0rem;
        text-align: center;
    }

    .btn-danger {
        margin-left: 2.0rem;
    }

    input[type=range] {
        -webkit-appearance: none;
        margin: 1rem 0;
        width: 100%;

        &:focus {
            outline: none;

            &::-webkit-slider-runnable-track {
                background: #e51c23;
            }

            &::-ms-fill-lower {
                background: #e51c23;
            }

            &::-ms-fill-upper {
                background: #e51c23;
            }
        }

        &::-webkit-slider-runnable-track {
            width: 100%;
            height: .5rem;
            cursor: pointer;
            animate: 0.2s;
            box-shadow: 0 0 0 #000000;
            background: #e51c23;
            border-radius: .1rem;
            border: 0 solid #000000;
        }

        &::-webkit-slider-thumb {
            box-shadow: 0 0 0 #000000;
            width: 0;
            height: 0;
            border-style: solid;
            border-width: 0 1rem 2rem 1rem;
            border-color: transparent transparent #e51c23 transparent;
            cursor: pointer;
            -webkit-appearance: none;
            margin-top: .5rem;
        }

        &::-moz-range-track {
            width: 100%;
            height: .5rem;
            cursor: pointer;
            animate: 0.2s;
            box-shadow: 0 0 0 #000000;
            background: #e51c23;
            border-radius: .1rem;
            border: 0 solid #000000;
        }

        &::-moz-range-thumb {
            box-shadow: 0 0 0 #000000;
            border: .1rem solid #e51c23;
            height: 1.8rem;
            width: 1.8rem;
            border-radius: 2.5rem;
            background: #A1D0FF;
            cursor: pointer;
        }

        &::-ms-track {
            width: 100%;
            height: .5rem;
            cursor: pointer;
            animate: 0.2s;
            background: transparent;
            border-color: transparent;
            color: transparent;
        }

        &::-ms-fill-lower {
            background: #e51c23;
            border: 0 solid #000000;
            border-radius: .2rem;
            box-shadow: 0 0 0 #000000;
        }

        &::-ms-fill-upper {
            background: #2497E3;
            border: 0 solid #000000;
            border-radius: .2rem;
            box-shadow: 0 0 0 #000000;
        }

        &::-ms-thumb {
            box-shadow: 0 0 0 #000000;
            border: .1rem solid #e51c23;
            height: 1.8rem;
            width: 1.8rem;
            border-radius: 2.5rem;
            background: #A1D0FF;
            cursor: pointer;
        }
    }

    #usage-list {
        background-color: #ffffff;
    }

    #usage-list > thead {
        background: #31459a;
        color: #fff;
    }

    #usage-list > tbody {
        text-align: left;
    }

    #detailed-data-usage-list {
        background-color: #ffffff;
        font-size: 1.2rem;
    }

    #detailed-data-usage-list > thead {
        background: #31459a;
        color: #fff;
    }

    #detailed-data-usage-list > tbody {
        text-align: left;
    }

    .no_data_used_p {
        text-align: center;
        margin: .5rem;
    }




    .circle_success {
        width: 1.5rem;
        height: 1.5rem;
        background: -moz-linear-gradient(top, #4caf50, #4aa921);
        background: -webkit-gradient(linear, left top, left bottom, from(#4caf50), to(#4aa921));
        -moz-border-radius: .75rem;
        -webkit-border-radius: .75rem;
        border-radius: .75rem;
        border: 0 solid #000000;
        -moz-box-shadow: 0px 1px 3px rgba(0, 0, 0, 0.5), inset 0px 0px 1px rgba(255, 255, 255, 0.7);
        -webkit-box-shadow: 0px 1px 3px rgba(0, 0, 0, 0.5), inset 0px 0px 1px rgba(255, 255, 255, 0.7);
        box-shadow: 0px 1px 3px rgba(0, 0, 0, 0.5), inset 0px 0px 1px rgba(255, 255, 255, 0.7);
        display: inline-block;
        vertical-align: middle;
    }

    .circle_danger {
        width: 2rem;
        height: 2rem;
        background: -moz-linear-gradient( top, #e1e1e1, #f6f6f6);
        background: -webkit-gradient(linear, left top, left bottom, from(#e1e1e1), to(#f6f6f6));
        -moz-border-radius: 1rem;
        -webkit-border-radius: 1rem;
        border-radius: 1rem;
        border: 0 solid #000000;
        -moz-box-shadow: 0px 1px 3px rgba(000,000,000,0.5), inset 0px 0px 1px rgba(255,255,255,0.7);
        -webkit-box-shadow: 0px 1px 3px rgba(000,000,000,0.5), inset 0px 0px 1px rgba(255,255,255,0.7);
        box-shadow: 0px 1px 3px rgba(000,000,000,0.5), inset 0px 0px 1px rgba(255,255,255,0.7);
    }

    .center_aligned {
        text-align: center;
    }

    .admin-ops-selection {
        select {
            border: .1rem solid #ccc;
            background-color: #fff;
            box-shadow: none;
            font-size: 1.4rem;
            padding: 1rem;
        }

        input[type=text] {
            background-color: #fff;
            border: .1rem solid #ccc;
            padding: 1rem;
            box-shadow: none;
            font-size: 1.4rem;
        }

        .info-span {
            position: absolute;
            width: 100%;
            height: 20px;
            bottom: -15px;
            right: 0;
            text-align: right;
            font-size: 1.2rem;
            line-height: 3.2rem;
        }
    }
}

.authentication_group {
    select {
        border: .1rem solid #ccc;
        background-color: #fff;
        box-shadow: none;
        font-size: 1.4rem;
        padding: 1rem;
    }
}

.credential-check {
    width: 100%;
    height: 100%;
    .backdrop;

    .abs-div {
        position: absolute;
        left: 50%;
    }

    .rel-div {
        position: relative;
        right: 50%;
    }
}

.packages-container {
    .credentialCheck {
        top: 4rem;
    }

    .craete-packages {
        background: #fff;
        margin: 1rem 0;
        padding: 1rem;

        h4.header {
            color: rgba(136, 136, 136, 0.8);
            text-align: center;
        }

        .package-from {
            position: relative;
            text-align: left;

            .form-element {
                margin: 1rem 0;

                select {
                    height: 3.2rem;
                    box-shadow: none;
                    background-color: #ffffff;
                    border: solid 1px #b7b7b7;
                    padding: 0.5rem 1rem;
                    outline: none;
                }
            }

            .selectize-input > input[type=text] {
                height: 1.8rem !important;
            }
        }
    }

    .adhoc-features {
        .sub-container-background;
        min-height: 18rem;
    }

    .package-features {
        .sub-container-background;
    }

    .packages-details-container {
        margin: auto;

        .package-details {
            display: inline-block;
            margin: 1.6%;
            background-color: @background-color;
            border-radius: 2px;
            padding: 1.5rem;
            width: 30%;

            h5 {
                font-size: 16px;
                font-weight: 500;
                text-align: center;
            }

            .feature-list {
                text-align: left;
                font-size: 1.2rem;
                display: block;
            }

            &:hover {
                background-color: #ffd800;
                box-shadow: @hover-card-shadow;
            }
        }
    }

    .segments-container {
        width: 60rem;
        margin: auto;

        .segment {
            display: inline-block;
            margin: 1rem;
            background-color: @background-color;
            border-radius: 2px;

            img {
                width: 15rem;
            }

            &:hover {
                background-color: #ffd800;
                box-shadow: @hover-card-shadow;

                img {
                    width: 16rem;
                    .contrast;
                }
            }
        }
    }

    .packages {
        //background-color: #eee;
        .package {
            display: inline-block;
            width: 25rem;
            height: 40rem;
            background-color: white;
            margin: 1rem;
            position: relative;
            vertical-align: top;
            //box-shadow: 0 0.0625rem 0.3125rem 0 #888888;
            box-shadow: 0 4px 8px 0 rgba(0,0,0,0.2);
            transition: 0.3s;

            .btn-div {
                bottom: 1rem;
                position: absolute;
                left: 50%;

                .btn {
                    position: relative;
                    right: 50%;
                    border-radius: 2px !important;
                }
            }

            .price {
                background-color: #bbbbbb;
                height: 8rem;
                padding: 1rem;
                font-size: 2.5rem;
            }

            .features {
                padding: 1rem;
                text-align: left;

                &.custom_scroll {
                    margin-top: 1rem;
                    min-height: 25rem;
                }

                .feature-heading {
                    font-size: 1.2rem;
                    font-weight: bold;

                    &:hover {
                        font-size: 1.25rem;
                    }
                }

                .feature {
                    margin: 1rem;
                    font-size: 1.2rem;

                    &:hover {
                        font-size: 1.25rem;
                    }
                }
            }

            &:hover {
                //width: 20rem;
                //height: 42rem;
                //box-shadow: @hover-card-shadow;
                text-shadow: @active-text-shadow;
                box-shadow: 0 8px 16px 0 rgba(0,0,0,0.2);

                .price {
                    background-color: #e75349;
                    color: white;
                }

                .feature-heading {
                    color: #047ad8;
                }
            }
        }
    }
}

.askCredentials {
    background-color: white;
    width: 40rem;
    margin-top: 10rem;
    box-shadow: @hover-card-shadow;

    .askCredentials-header {
        background-color: #e75349;
        text-align: center;
        width: 100%;
        line-height: 2rem;
        padding: 1rem;
        font-weight: bold;
    }

    .askCredentials-error {
        position: absolute;
        font-size: 1.4rem;
        color: red;
        left: 3rem;
        top: 4.5rem;
    }

    .credential-details {
        background-color: white !important;
        box-shadow: none !important;

        input {
            border: 0;
            border-bottom: 1.5px solid rgba(140,133,133,0.2);
            width: 100%;
            padding: .2rem 1rem;

            &:focus {
                border-bottom: 1.5px solid rgba(140,133,133,0.2) !important;
                outline: white;
                box-shadow: none !important;
                border-color: rgba(140,133,133,0.2) !important;
            }
        }

        button.btn {
            border-radius: 1px;
        }

        #verifyBtn {
            display: inline-block;
            background-color: #dc483b;
            border-radius: 1px;
            color: white;
            cursor: pointer;
        }

        span {
            &.label {
                font-family: serif;
                font-weight: normal;
            }

            &.icon {
                background: url('/images/g-plus.png') transparent 5px 50% no-repeat;
                display: inline-block;
                vertical-align: middle;
                width: 4.5rem;
                height: 3.4rem;
                border-right: 1px solid #fff;
                background-size: 3rem;
            }

            &.buttonText {
                display: inline-block;
                vertical-align: middle;
                padding-left: 2rem;
                padding-right: 2rem;
                font-size: 1.3rem;
                font-weight: bold;
                font-family: 'Roboto', sans-serif;
            }
        }

        .login-details {

            .login {
                background-color: #4ab04d;
                color: white;

                &:hover {
                    background-color: #39a63c;
                }
            }
        }

        .cell {
            height: 3rem;
            margin-bottom: 2rem;

            button {
                width: 80%;
                height: 3.5rem;
                padding: .5rem;
                font-size: 1.2rem;
            }
        }

        .partition {
            position: relative;
            margin: 1rem 0;

            .left-part, .right-part {
                border-bottom: 1.5px dashed rgba(140,133,133,0.8);
                width: 46%;
                top: 50%;
                position: absolute;
            }

            .left-part {
                left: 0;
            }

            .right-part {
                right: 0;
            }
        }
    }
}

.sms-container {
    font-size:1.3rem;
    .inner-container {
        position: relative;
        height: 100%;
        width: 100%;
        margin: 0;
    }

    hr.or-separator {
	    border-top: 1px dashed #7b7b7b;
        text-align: center;
        margin-top: 2.5rem;
        display: flex;
        justify-content: center;
    }
    hr.or-separator:after {
        content: 'OR';
        display: flex;
        position: relative;
        top: -2rem;
        /* padding: .2rem 1rem; */
        background: #f5f5f5;
        color: #8c8b8b;
        font-size: 1.5rem;
        border-radius: 50%;
        width: 4rem;
        height: 4rem;
        align-items: center;
        justify-content: center;
    }

    .block-header {
        background-color: #51b564;
        cursor: pointer;

        &.collapsed {
            background-color: #c1c1c1;
        }

        h3 {
            color: #fff;
            margin: 1rem;
            font-weight: 400;
            font-size: 2rem;

            span {
                float: right;
                /*font-size: 1.2rem;
                margin-top: .3rem;*/
            }
        }
    }

    .buttons {
        margin: 1rem 0 3rem;
    }

    .message-text-area {
        height: 12rem !important;
        box-shadow: none;
        background-color: #ffffff;
        border: solid 1px #b7b7b7;
        padding: 2rem 1rem 1rem !important;
        font-size: 1.3rem;
        box-shadow: 0 -0.1rem 0 rgba(207, 208, 210, 0.5) inset;
        resize: none;
        &:focus {
            box-shadow: 0 -0.2rem 0 #c1c1c1 inset !important;
        }
    }

    .sms_size {
        background: #f5f5f5;
        height: 2rem;
        font-size: 1.1rem;
        border-left: 1px solid #b7b7b7;
        border-right: .1rem solid #b7b7b7;
        border-bottom: .1rem solid #b7b7b7;
    }

    .border {
        background: white;
        border: 1px solid #ccc;
        padding: 1rem 1rem;
    }

    .sms-history {
        background-color: #fff;
        position: relative;
        margin-right: 2%;
        box-shadow: 0 0.0625rem 0.3125rem 0 #888888;

        .sms-items {
            top: 1rem;
            width: 100%;
            height: 86%;
            position: relative;
            .sms-filter {
                position: absolute;
                right: 1.5rem;
                top: -1rem;
                z-index: 9;
                height: 4rem;
                background: transparent;
                display: flex;
                align-items: center;
                color: #333;
                select {
                    color: #333;
                    font-size: 1.2rem;
                    padding: .2rem 2rem;
                }
            }
            textarea.form-control {
                height: 15rem !important;
            }

            .sms-details.collapsed {
                background: #fff;
            }

            .sms-details {
                padding: 1.5rem;
                border-bottom: .1rem solid #ccc;
                cursor: pointer;
                background: #f5f5f5;

                &:first-child {
                    border-top: .1rem solid #ccc;
                    border-bottom: none;
                }

                &:hover {
                    z-index: 999;
                    background: rgba(204, 204, 204, 0.21);
                    box-shadow: @hover-card-shadow;
                    text-shadow: @active-text-shadow;
                }

                .sms-icon > img {
                    width: 3rem;
                }

                .sms-content {
                    h4 {
                        font-size: 1.4rem;
                        font-weight: 700;
                        margin: 0;
                    }

                    p {
                        font-size: 1.1rem;
                        margin: 0;
                    }
                }

                .sms-timings {
                    top: -0.4rem;
                    font-size: 1.1rem;
                    margin: 0;
                }

                .icon-arrow {
                    width: 30px;
                    height: 30px;
                    position: absolute;
                    right: 5px;
                    top: 20px;
                }

                .icon-arrow:before {
                    background-size: 30px 30px;
                    display: block;
                    width: 30px;
                    height: 30px;
                    content: "";
                    background: transparent url('../../images/up_arrow_16.png') scroll no-repeat center;
                }
            }

            .sms-details.collapsed .sms-timings .icon-arrow:before {
                background-size: 30px 30px;
                display: block;
                width: 30px;
                height: 30px;
                content: "";
                background: transparent url('../../images/down_arrow_16.png') scroll no-repeat center;
            }

            .collapse {
                padding: 2rem 0;
                border-bottom: .1rem solid #ccc;
            }

            span.btn-clipboard {
                position: absolute;
                top: 0;
                right: 0;
                z-index: 10;
                display: block;
                padding: .5rem 1rem;
                font-size: 1.2rem;
                color: #767676;
                cursor: pointer;
                background-color: #fff;
                border: 1px solid #e1e1e8;
                border-radius: 0 4px 0 4px;
            }

            .all_locations h3 {
                font-size: 1.4rem;
                font-weight: 500;
                margin: .5rem 0;
            }
        }
    }
    
    
    .create-sms {
        background-color: #fff;
        position: relative;
        overflow: hidden;
        box-shadow: 0 0.0625rem 0.3125rem 0 #888888;
        margin-bottom: 2rem;
        
        .create_sms_form {
            top: 0;
            width: 100%;
            height: 92%;
            //overflow-y: auto;
            position: relative;
            padding: 0 0 3rem;

            #show-message-box-error {
                color: #b94a48;
                margin: 0;
            }

            textarea.has-error {
                border: 1px solid #b94a48 !important;
            }

            .campaign-selection {
                .has-error {
                    a.select2-choice {
                        border: 1px solid #b94a48 !important;
                    }
                }

                #show-campaign-error {
                    color: #b94a48;
                    margin: 0;
                }

                margin: 3rem 0 0 0;

                .ui-select-container {
                    .selectize-input {
                        overflow: hidden;
                        &:after {
                            border-style: none;
                        }
                    }

                    .selectize-dropdown {
                        margin: -7px 0 0 0;
                        -webkit-border-radius: 0;
                        -moz-border-radius: 0;
                        border-radius: 0;
                        -webkit-box-shadow: none;
                        box-shadow: none;
                        -webkit-box-sizing: border-box;
                        .active {
                            background-color:#f5f5f5;
                        }
                    }
                }
            }

            select {
                height: 3.5rem;
                box-shadow: none;
                background-color: #ffffff;
                border: solid 1px #b7b7b7;
                padding: 0.5rem 1rem;
                margin: 1rem 0;
                font-size: 1.4rem;
                line-height: 1.4rem;
                color: #666666;
                background-image:none;
                &:focus {
                    box-shadow: none !important;
                }
            }

            .message-box {
                margin-top: 1rem;
            }

            .nav-tabs > li > a {
                //margin-right: 2px;
                line-height: 0.846;
                border: 1px solid transparent;
                border-radius: 3px 3px 0 0;
                font-size: 1.5rem;
                font-weight: 500;
            }
         }

        

        .p-margin-10 {
            margin: 1rem 0;
        }

        .red-text {
            color: red;
        }

        #rules {
            padding: 1.5rem;
            background: #eee;
            /*.sender_details {
                 margin-bottom:1rem;
             }*/
            .form-group {
                display: flex;
                align-items: center;
            }
            /*.sender_title > h6 {
                font-weight: 600;
                margin: .5rem 0;
             }
            .rule_title {
                font-size:1.2rem;
            }*/
            .form-control {
                font-size:1.3rem;
                color:#666666;
                /*font-size: 1rem;
                max-height: 16px;
                box-shadow: none;
                border: .1rem solid #646363;
                padding: 0 .2rem;
                background-color: #fff;*/
                &:disabled {
                    background-color: #dddddd;
                }

                &:focus {
                    box-shadow: none !important;
                }
            }

            .rules_error {
                font-size: 1.2rem;
                margin-bottom: .8rem;
                color: red;
            }
        }

        #my_numbers {
            padding: 1.5rem;
            display: none;
            background: #eee;

            .total_numbers {
                display: block;
                width: 100%;
                min-height: 20px;
                margin: 1rem -15px;
            }
        }
    }

    .btn-primary-green {
        background-color: #51b564;
        color: #fff;
        border-radius: 0;
        font-size: 1.5rem;
        padding: .5rem 5rem;
        text-transform: uppercase;
    }

    .message_sender_details {
        position: absolute;
        padding: 0;
        margin: 0;
        width: 100%;
        background: #f5f5f5;
        top: 0rem;
        height: 2rem;
        font-size: 1.1rem;
        border: 1px solid #ccc;
        z-index:99;
        
        select {
            height: 2rem;
            background-color: rgb(255, 255, 255);
            -webkit-appearance: none;
            box-shadow: none;
            font-size: 1.2rem;
            border-width: 1px;
            border-style: solid;
            border-color: rgb(183, 183, 183);
            border-image: initial;
            padding: 0px 1rem;
            border-radius: 0;
            margin-top: -1px;
            margin-left: -1px;
        }
        
    }

    .message_tracking_details {
        position: absolute;
        padding: 0;
        margin: 0;
        width: 100%;
        background: #f5f5f5;
        bottom: -3rem;
        height: 3rem;
        font-size: 1.1rem;
        border: 1px solid #ccc;
        border-top: none;
        display: flex;
        align-items: center;
        input[type=text] {
            height: 3rem;
            border-top: none;
            border-bottom: none;
            width: 100%;
            border-right: none;
            font-size: 1.2rem;
        }
        
    }

    .has-error input:not([type=checkbox]), 
    .has-error .form-control, 
    .has-error input:not([type=checkbox]):focus, 
    .has-error .form-control:focus {
        border:1px solid #a94442 !important;
        outline: none;
    }

    

    .bootstrap-datetimepicker-widget {
        .datepicker,.timepicker {
            color:#333;
        }
    }

    .schedule-campaign-form {
        .row {
            margin:0;
            .container-fluid{
                margin-bottom:2rem;
            }
            &.target-filters {
                padding: 1.5rem;
                margin-right: 0;
                margin-left: 0;
                border-width: .2rem;
                border: solid #f7f7f9;
                &.panel-default>.panel-heading {
                    display: none;
                }
                 .input-group {
                    display: inline-table;
                    vertical-align: middle;
                    .input-group-addon {
                        /* padding: 6px 12px; */
                        font-size: 14px;
                        font-weight: 400;
                        line-height: 1;
                        color: #555;
                        text-align: center;
                        background-color: #eee;
                        border: 1px solid #ccc;
                        border-radius: 4px;
                        &:first-child {
                            border-top-right-radius: 0;
                            border-bottom-right-radius: 0;
                        }
                    }
                        input[type=text].custom-input {
                            border-top-left-radius: 0 !important;
                            border-bottom-left-radius: 0 !important;
                            border-left: transparent;
                        }
                }

                input[type=text].custom-input {
                    height: 3.66rem !important;
                    border-radius:0.4rem !important;
                    &:focus {
                        border: 1px solid #5897fb !important;
                        outline: none !important;
                        -webkit-box-shadow: 0 0 5px rgba(0, 0, 0, .3) !important;
                        box-shadow: 0 0 5px rgba(0, 0, 0, .3) !important;
                    }
                }
                input[type=time].custom-input {
                    height: 3.66rem !important;
                    border-radius:0.4rem !important;
                    box-shadow: none;
                    background-color: #ffffff;
                    border: solid 1px #b7b7b7;
                    padding: 0.5rem 1rem;
                    &:focus {
                        border: 1px solid #5897fb !important;
                        outline: none !important;
                        -webkit-box-shadow: 0 0 5px rgba(0, 0, 0, .3) !important;
                        box-shadow: 0 0 5px rgba(0, 0, 0, .3) !important;
                    }
                }
                .control-label {
                    text-align:left
                }
            }
            
        }
        .nav.nav-tabs {
            li>a {
                text-transform: capitalize;
                font-size: 1.6rem;
                font-weight: 500;
                padding: 1rem 5rem;
            }
            border: none; 
        }
        .section-head {
            margin: 2rem 0 1rem 0;
        }

        .schedule-tpl-container {
            label {
                text-transform:capitalize;
                width: 100%;
            }
            .horizontal-scroll {
                overflow-x: auto;
                white-space: nowrap;
                .custom_scroll(0,8rem,8rem,0.5rem);
                
            }

            .tpl-content {
                margin-right: 1rem;
                display: inline-block;
                float: none;
                padding: .1rem;
                .card {
                    //box-shadow: 0 4px 8px 0 rgba(0,0,0,0.2);
                    //transition: 0.3s;
                    cursor:pointer;
                    position: relative;
                    display: -webkit-box;
                    display: -ms-flexbox;
                    display: flex;
                    -webkit-box-orient: vertical;
                    -webkit-box-direction: normal;
                    -ms-flex-direction: column;
                    flex-direction: column;
                    min-width: 0;
                    word-wrap: break-word;
                    background-color: #fff;
                    background-clip: border-box;
                    //border: 1px solid rgba(0,0,0,.125);
                    border-radius: .25rem;
                    box-shadow: 0px 1px 5px 2px rgba(204, 204, 204, 0.50);
                    &:hover {
                        box-shadow: 0 8px 16px 0 rgba(0,0,0,0.2);
                    }
                    .card-body {
                        -webkit-box-flex: 1;
                        -ms-flex: 1 1 auto;
                        flex: 1 1 auto;
                        padding: 1rem;
                        margin: 0;
                        p {
                            margin:0;
                            .multiLineEllipsis;
                        }
                    }
                }
            }
        }

        .sms_preview_container {
            margin:auto;
            position:relative;
            width: 26rem;
            span.sms_sender_id {
                position: absolute;
                top: 6.7rem;
                left: 5rem;
                color: #fff;
            }
            div.sms_preview_content {
                width: 21rem;
                position: absolute;
                top: 12rem;
                left: 2.5rem;
                .sms_item {
                    .user_icon {
                        position: relative;
                        padding: 3px 8px;
                        width: 30px;
                        height: 30px;
                        display: inline-block;
                        border-radius: 50%;
                        background: #2757c3;
                        color: #fff;
                        -webkit-box-shadow: 0px 0px 5px 0px rgba(0, 0, 0, 0.3);
                        -moz-box-shadow: 0px 0px 5px 0px rgba(0, 0, 0, 0.3);
                        box-shadow: 0px 0px 5px 0px rgba(0, 0, 0, 0.3);
                        
                    }
                    .sms_content {
                        width: 170px;
                        display: inline-block;
                        position: relative;
                        right: 0;
                        top: 0;
                        background: #E8E8E8;
                        padding: 5px;
                        border-radius: 10px;
                        font-size: 10px;
                        color: #000;
                        font-weight: 400;
                        -webkit-box-shadow: 0px 0px 5px 0px rgba(0,0,0,0.3);
                        -moz-box-shadow: 0px 0px 5px 0px rgba(0,0,0,0.3);
                        box-shadow: 0px 0px 5px 0px rgba(0,0,0,0.3);
                        word-wrap:break-word;
                    }
                    .sms_recieved_time {
                        text-align: right;
                        font-size: 1rem;
                        color: #8a8a8a;
                        padding: 1px 0;
                    }

                }
            }
        }

        
    }

    .box-radio-buttons {
        .btn {
            color: #292b2c;
            background-color: #fff;
            border-color: #ccc;
            background-color: #e8e8e8;
            &.active {
                color: #292b2c;
                background-color: white;
                font-weight: bold;
                background-image: none;
                -webkit-box-shadow: 2px 2px 5px rgba(0, 0, 0, 0.3);

                box-shadow: 2px 2px 5px rgba(0, 0, 0, 0.3);
                -webkit-transition: all 0.2s;
                -o-transition: all 0.2s;
                transition: all 0.2s;
            }
            &:hover {
                color: #fff;
                background-color: #52d36a;
                -webkit-box-shadow: 1px 1px 2px rgba(0,0,0,0.3);
                box-shadow: 1px 1px 2px rgba(0,0,0,0.3);
                -webkit-transition: all 0.2s;
                -o-transition: all 0.2s;
                transition: all 0.2s;
            }

            &:first-child {
                border-top-left-radius: 4px;
                border-bottom-left-radius: 4px;
            }
            &:last-child {
                border-top-right-radius: 4px;
                border-bottom-right-radius: 4px;
            }
            &.disabled {
                border: 1px solid rgba(204, 204, 204, 0.67);
                pointer-events:none;
                &:hover {
                    color: #292b2c;
                    background-color: #fff;
                    -webkit-box-shadow: none;
                    box-shadow: none;
                    -webkit-transition: all 0.2s;
                    -o-transition: all 0.2s;
                    transition: all 0.2s;
                }
            }

            input[type=checkbox] {
                position: absolute;
                opacity: 0;
                cursor: pointer;
                height: 0;
                width: 0;
            }

            
            
        }

        

        &.source-selector {
            margin: 0 0 3rem 0;
            width: 100%;
            .btn {
                width:33.333333%;
            }
        }
    }

    .single-radio-buttons {
         .btn {
        &:first-child {
                border-top-left-radius: 0;
                border-bottom-left-radius:0;
            }
            &:last-child {
                border-top-right-radius: 0;
                border-bottom-right-radius: 0;
            }
            }
    }

    #estimation {
        .alert {
            padding: 15px;
            margin-bottom: 20px;
            border: 1px solid transparent;
            border-radius: 4px;
            &.alert-success {
                color: #3c763d;
                background-color: #dff0d8;
                border-color: #d6e9c6;
            }
            &.alert-dismissible {
                padding-right: 35px;
                .close {
                    position: relative;
                    top: -2px;
                    right: -21px;
                    color: inherit;
                    float: right;
                    font-size: 21px;
                    font-weight: 700;
                    line-height: 1;
                    color: #3c763d;
                    text-shadow: 0 1px 0 #fff;
                    filter: alpha(opacity=20);
                    opacity: .2;
                }
            }
        }
    }

    .form-group {
        margin:0;
    }

    
}

.review-modal {
    .container-fluid {
        margin-bottom:2rem;
    }
    input[type=text], input[type=number], input[type=password] {
    height: 3.2rem;
    box-shadow: none;
    background-color: #ffffff;
    border: solid 1px #b7b7b7;
    padding: 0.5rem 1rem;
}
    input[type=text].custom-input {
        height: 3.66rem !important;
        border-radius:0.4rem !important;
        &:focus {
            border: 1px solid #5897fb !important;
            outline: none !important;
            -webkit-box-shadow: 0 0 5px rgba(0, 0, 0, .3) !important;
            box-shadow: 0 0 5px rgba(0, 0, 0, .3) !important;
        }
    }
    input[type=time].custom-input {
        height: 3.66rem !important;
        border-radius:0.4rem !important;
        box-shadow: none;
        background-color: #ffffff;
        border: solid 1px #b7b7b7;
        padding: 0.5rem 1rem;
        &:focus {
            border: 1px solid #5897fb !important;
            outline: none !important;
            -webkit-box-shadow: 0 0 5px rgba(0, 0, 0, .3) !important;
            box-shadow: 0 0 5px rgba(0, 0, 0, .3) !important;
        }
    }
    .control-label {
        text-align:left
    }
        
}

.campaign-reports-table {
    &.table {
        width: 100%;
        max-width: 100%;
        margin-bottom: 20px;
        background:#fff;
        color: #333;
        font-size:1.2rem;
        &.table>thead>tr.filters>td{
            vertical-align:middle;
            padding:1.5rem;
            font-size:16px;
        }
        &.table>thead>tr.filters>td>div{
            height:32px;
            img.icons{
            width:20px;
            height:auto;
            margin:5px;
            }
        }
        input#search-input{
            display:inline-block!important;
            width:135px;
            height:32px;
            -webkit-transition:all linear 0.2s;
            transition:all linear 0.2s;
            //display: block!important;
        }
        input#search-input.ng-hide{
            opacity:0;
        }

        &>tbody {
            border: 1px solid #dddddd;
            &+tbody {
                border-top: none !important;
            }
            &.inactive-campaign {
                color: rgba(0,0,0,.5) !important;
                .table {
                    color: #333 !important;
                }
            }
        }
        >tbody>tr>td {
            .textarea-header {
                border: solid 1px #e4e4e4 !important;
                border-bottom: none !important;
                background: #f6f6f6;
                width: 100%;
                height: 2rem;
                border-bottom: none;
                font-size: 1rem;
                padding: 0 .5rem;
            }
            .textarea {
                border: solid 1px #e4e4e4 !important;
                width: 100%;
                height: 100% !important;
                font-size: 1.2rem;
                outline: none;
                padding: 0 .5rem;
            }
        }

        >tbody>tr>td, 
        >tbody>tr>th, 
        >tfoot>tr>td, 
        >tfoot>tr>th, 
        >thead>tr>td, 
        >thead>tr>th {
            padding: 2rem;
            /*line-height: 1.42857143;*/
            vertical-align: top;
            border-top: none;
             cursor: pointer;
          -webkit-user-select: none;
          /* Chrome all / Safari all */
          -moz-user-select: none;
          /* Firefox all */
          -ms-user-select: none;
          /* IE 10+ */
          user-select: none;
            .list-unstyled {
                padding-left: 0;
                list-style: none;
                margin: 0;
                li {
                    text-overflow: ellipsis;
                    line-height: 1.846;
                    overflow: hidden;
                    padding-right: 1rem;
                    max-width: 30rem;
                    white-space: nowrap;
                }
            }

            .campaign {
                background: white;
                margin-right: 1rem;
                display: inline-block;
                position: relative;
                text-align: center;
                padding: 0;
                width: 50%;

                &.inactive {
                    opacity: 0.3;
                }

                .border {
                    background: white;
                    border: .2rem solid #9d9d9d;
                    padding: .5rem;
                    img { 
                        width: 100%;
                    }
                }

                .actions {
                    display: none;
                    position: absolute;
                    top: 0;
                    bottom: 0;
                    right: 0;
                    left: 0;
                    height: 100%;
                    color: white;
                    font-size: 2rem;
                }

                .offerCode {
                    text-transform: uppercase;
                    font-size: 1.2rem;
                }
            }
        }
    }
}

.custome-tooltip-class {
    width:30rem;
    .tooltip-inner {
        max-width:100%;
    }
}

.offers-container {
    position: relative;
    height: 100%;
    width: 100%;
    margin-top: 1rem;

    .page-content {
        position: relative;
        height: 100%;
        width: 100%;
        margin: 0;
    }

    .header-container {
        position: relative;
        width: 100%;
        margin: 0;
        height: 15%;

        .stats {
            padding: 1.5rem 2.5rem;
            background: #fff;
            width: 30%;
            margin-right: 5%;
            position: relative;
            height: 100%;

            &:last-child {
                margin-right: 0;
            }

            h3 {
                font-size: 2.4rem;
                margin: 0 0 1rem 0;
                color: #2196f3;
            }

            p {
                margin: 0;
                font-size: 1.3rem;
                color: #000;
                font-weight: 500;
            }

            @media @smartphones {
                width: 100%;
                margin-bottom: 1rem;
            }
        }

        .campaign:before {
            content: '';
            width: 1rem;
            height: 100%;
            background-color: #e75349;
            display: inline-block;
            position: absolute;
            left: 0;
            top: 0;
        }

        .sms:before {
            content: '';
            width: 1rem;
            height: 100%;
            background-color: #ffd800;
            display: inline-block;
            position: absolute;
            left: 0;
            top: 0;
        }

        .footfall:before {
            content: '';
            width: 1rem;
            height: 100%;
            background-color: #658be4;
            display: inline-block;
            position: absolute;
            left: 0;
            top: 0;
        }
    }

    .campaign_content {
        position: relative;
        background: #fff;
        width: 100%;
        top: 3%;
        margin: 0;

        ul.nav.nav-pills {
            position: relative;
            height: 10%;
            width: 100%;

            li {
                & + li {
                    margin-left: 0;
                }

                &.active {
                    a {
                        color: #fff;
                        background-color: #51b564;
                    }
                }

                a {
                    color: #a29f9f;
                    font-size: 1.5rem;
                    background-color: #fff;
                    border-radius: 0;

                    &:hover, &:focus {
                        color: #fff;
                        background-color: #51b564;
                    }
                }
            }
        }

        .tab-content {
            padding: 1.5rem;
            position: relative;
            width: 100%;
            top: 0;

            .tab-pane {
                width: 100%;
                font-size: 1.3rem;
                position: relative;
                clear: both;
                min-height: 20rem;
            }

            .tab-pane:before {
                display: table;
                content: " ";
            }

            .tab-pane:after {
                display: table;
                content: " ";
                clear: both;
            }
        }

        #create_campaign {

            p.info_text {
                color: rgba(120, 120, 120, 0.5);
                font-size: 1.4rem;
                font-weight: 500;
                margin: 1rem 0;
            }
            /* Let's get this party started */
            .locations-selector {
                .select2-choices {
                    padding-left: 7rem !important;
                }
            }

            .custom_content {
                margin-top: 2rem;
                border: .1rem solid #ccc;
                padding: 1.5rem;

                h3 {
                    font-size: 1.6rem;
                    margin: 0 0 1rem 0;
                }

                h5 {
                    font-size: 1.2rem;
                    color: #2196f3;
                    font-style: italic;
                }

                .custom_content_title {
                    font-size: 1.2rem;
                }

                .custom_content_item {
                    margin-bottom: .5rem;
                }

                input.custom_content_input {
                    border: .1rem solid #ccc;
                    height: 2.4rem;
                    box-shadow: none !important;
                    padding: .5rem;
                    font-size: 1.2rem;

                    &:active, &:hover, &:focus {
                        box-shadow: none !important;
                    }

                    &:focus {
                        border: .1rem solid #ccc;
                    }
                }
            }

            .rules {
                margin-top: 2rem;
                background: #eee;
                padding: 1.5rem;

                h3 {
                    font-size: 1.6rem;
                    margin: 0 0 1rem 0;
                }

                .form-control {
                    font-size: 1rem;
                    max-height: 16px;
                    box-shadow: none;
                    border: .1rem solid #646363;
                    padding: 0 .2rem;
                    background-color: #fff;

                    &:disabled {
                        background-color: #dddddd;
                    }

                    &:focus {
                        box-shadow: none !important;
                    }
                }

                .rules_error {
                    font-size: 1.2rem;
                    margin-bottom: .8rem;
                    color: red;
                }
            }

            .buttons {
                margin-top: 2rem;
                margin-bottom: 2rem;
            }

            .preview-section {
                h6 {
                    font-size: 2rem;
                    font-weight: 500;
                }
                .preview-container {
                    text-align: center;
                }
                .preview_campaign_image_container {
                    text-align: center;
                    margin: auto;
                    border: .1px dashed #666666;
                    padding: .5rem;

                    .text {
                        color: #000;
                        font-weight: 500;
                    }

                    .splashImage {
                        max-height: 35rem;
                        max-width: 38rem;
                    }

                    hr {
                        margin-top: 1rem;
                        margin-bottom: 1rem;
                    }

                    .offerCode {
                        color: #000;
                        font-weight: 500;
                        background: yellow;
                    }
                }

                .upload_buttons_hidden {
                    display: none;
                }

                .upload_buttons {
                    margin-top: 2rem;

                    .file_name {
                        height: 3rem;
                        width: 100%;
                        border: .1rem dashed #2497E3;
                        color: #2497E3;
                        display: block;
                    }

                    h5 {
                        font-size: 1.2rem;
                        color: #2196f3;
                        font-style: italic;
                    }
                }
            }
        }

        #this-campaign-history {
            padding: 0 5rem;
        }

        #campaign_history {
            .text {
                font-size: 1.2rem;
                font-weight: bold;
                text-transform: none;
                text-align: center;
            }

            .no_offers_present {
                text-align: center;
                font-size: 1.5rem;
                margin: 6rem;
            }

            .campaign-container {
                background-color: white;
                padding: 1rem 0;
                width: 100%;
                border-bottom: .1rem solid #d6d3d3;

                .campaign-details {
                    .locations {
                        display: inline-block;
                        width: 85%;
                        margin-left: 2rem;
                        float: right;

                        .select2-choices {
                            border: none;
                        }

                        .location {
                            font-size: 1.2rem;
                            padding: .1rem .5rem !important;
                            border-radius: .1rem !important;
                            margin: .1rem !important;
                            background-image: none;
                        }
                    }
                }

                .campaign {
                    background: white;
                    margin-right: 1rem;
                    display: inline-block;
                    position: relative;
                    text-align: center;
                    padding: 0;
                    width: 100%;

                    &.inactive {
                        opacity: 0.3;
                    }

                    .border {
                        background: white;
                        border: .2rem solid #9d9d9d;
                        padding: .5rem;
                        img { 
                            width: 100%;
                        }
                    }

                    .actions {
                        display: none;
                        position: absolute;
                        top: 0;
                        bottom: 0;
                        right: 0;
                        left: 0;
                        height: 100%;
                        color: white;
                        font-size: 2rem;
                    }

                    .offerCode {
                        text-transform: uppercase;
                        font-size: 1.2rem;
                    }
                }
            }


            .glyphicon-play,
            .glyphicon-pause,
            .glyphicon-edit,
            .glyphicon-trash {
                cursor: pointer;
                top: 50%;
                transform: translate(0, -50%);
                -o-transform: translate(0, -50%);
                -moz-transform: translate(0, -50%);
                -ms-transform: translate(0, -50%);
                -webkit-transform: translate(0, -50%);
            }

            .glyphicon-play:hover,
            .glyphicon-pause:hover,
            .glyphicon-edit:hover {
                font-size: 2.2rem;
            }

            .campaign:hover .actions {
                display: block;
                background-color: rgba(0, 0, 0, 0.2);
            }
        }
    }
}

.reports-container {
    h5 {
        font-size: 1.6rem;
    }

    .stats_container {
        background: #fff;
        padding: 5rem 1.5rem;
    }

    .data-usage-row {
        td {
            vertical-align: middle;
        }
    }

    #mobile-detailed-report, #usage-list, #detailed-data-usage-list, .dns-report {
        background-color: #ffffff;

        thead {
            background: #31459a;
            color: #fff;
        }

        tbody {
            text-align: left;
        }
    }

    .mobile-detailed-report {
        position: relative;
        width: 100%;

        .glyphicon-remove {
            position: absolute;
            right: 0;
            color: white;
            opacity: 1;
            font-size: 2rem;
        }

        div {
            .position-tlbr(absolute, -8rem, 0, -30rem, 0);
            background: rgba(124, 114, 118, 0.39);
            max-height: 30rem;
            overflow-y: scroll;

            table {
                background-color: white;
                margin: 0;
            }
        }
    }

    .stats_card {
        height: 15rem;
        width: 15rem;
        margin: auto;
        border-radius: 7.5rem;
        text-align: center;

        &.green_border {
            border: 4px solid green;
        }

        &.red_border {
            border: 4px solid red;
        }

        &.yellow_border {
            border: 4px solid yellow;
        }

        .stats_figure {
            h4 {
                font-size: 1.6rem;
                margin: 0;
            }

            span {
                font-size: 1.2rem;
            }

            position: relative;
            top: 50%;
            -ms-transform: translateY(-50%);
            -webkit-transform: translateY(-50%);
            transform: translateY(-50%);
        }
    }

    .chart_container {
        background: #fff;
    }
}

.sms-analytics {
    margin: auto;
    margin-bottom: 8rem;

    .row {
        margin: 0;
    }

    .page-header;

    .headings {
        font-family: Roboto;
        font-weight: 500;
        font-style: normal;
        font-stretch: normal;
        letter-spacing: normal;
        text-align: left;
    }

    .paragraphs {
        font-family: Roboto;
        font-weight: 500;
        font-style: normal;
        font-stretch: normal;
        line-height: normal;
        letter-spacing: normal;
        text-align: left;
    }

    .detailed_report_link_container, .overview_report_link_container {
        text-align: center;
        margin: 3rem auto;

        a {
            text-decoration: underline;
            color: #2e84dd;
            font-size: 1.6rem;
            font-weight: 500;
            font-style: normal;
            font-stretch: normal;
            line-height: 0.8;
            letter-spacing: normal;
        }
    }

    .sms-stats-container {
        background: #fff;
        box-shadow: 0px 4px 4px 0 rgba(7, 8, 8, 0.41);
        padding: 1.5rem;

        h5 {
            .headings;
            font-size: 2.4rem;
            line-height: 0.61;
            color: #525252;
        }

        h6 {
            .headings;
            font-size: 1.6rem;
            color: #525252;
        }

        .sms-info-card {
            display: block;
            height: 15rem;
            width: 15rem;
            border-radius: 7.5rem;
            border: 4px solid #ccc;
            margin: auto;

            img {
                vertical-align: middle;
                height: 10rem;
                width: 10rem;
                position: relative;
                top: 2.5rem;
                left: 1rem;
            }

            h3 {
                .headings;
                font-size: 3rem;
                font-weight: 300;
                color: #030303;
            }

            p {
                .paragraphs;
                font-size: 2rem;
                color: #797979;
            }
        }

        .auto-sms {
            .auto-sms-container {
                margin: 2rem auto;
                border-bottom: 1px solid #ccc;
            }
        }

        .custom-sms {
            .custom-sms-container {
                margin: 2rem auto;
            }
        }
    }

    .sms-report-container {
        background: #fff;
        box-shadow: 0px 4px 4px 0 rgba(7, 8, 8, 0.41);
        padding: 15px;

        h5 {
            .headings;
            font-size: 2.4rem;
            line-height: 0.61;
            letter-spacing: normal;
            color: #525252;
            margin: 3rem .7rem;
        }

        .sms_report_table_final {
            width: 100%;
            text-align: center;

            .twenty_percent_final {
                width: 23%;
            }

            .forty_percent_final {
                width: 31%;
            }
        }

        .sms_report_table {
            position: relative;

            tbody > tr {
                position: relative;

                &:last-child > td {
                    box-shadow: 0 1rem .5rem -1rem #333333;
                }
            }

            width: 100%;

            .twenty_percent {
                width: 23%;
                position: relative;
                box-shadow: 0 .7rem .5rem -1rem #333;

                &:before {
                    box-shadow: -1rem 0 1.5rem -1.5rem inset;
                    content: " ";
                    height: 100%;
                    left: 0;
                    position: absolute;
                    top: 0;
                    width: 1rem;
                }

                &:after {
                    box-shadow: 1rem 0 1.5rem -1.5rem inset;
                    content: " ";
                    height: 100%;
                    right: 0;
                    position: absolute;
                    top: 0;
                    width: 1rem;
                }
            }

            .forty_percent {
                width: 31%;
                position: relative;
                box-shadow: 0 7px 5px -10px #333;

                &:before {
                    box-shadow: -1rem 0 1.5rem -1.5rem inset;
                    content: " ";
                    height: 100%;
                    left: 0;
                    position: absolute;
                    top: 0;
                    width: 1rem;
                }

                &:after {
                    box-shadow: 1rem 0 1.5rem -1.5rem inset;
                    content: " ";
                    height: 100%;
                    right: 0;
                    position: absolute;
                    top: 0;
                    width: 1rem;
                }
            }

            thead > tr > th {
                padding: 0 1rem;

                .table_header_cell {
                    background-color: #f2f2f2;
                    text-align: center;
                    padding: 1rem;
                    /*border-top-left-radius: 4px;
                    border-top-right-radius: 4px;*/
                    img {
                        height: 6rem;
                        width: 6rem;
                    }

                    p {
                        .paragraphs;
                        font-family: OpenSans;
                        font-size: 1.4rem;
                        text-align: center;
                        color: #000000;
                    }
                }
            }

            tbody > tr > td {
                padding: 0 1rem;

                .table_body_cell {
                    padding: 1rem;

                    h6 {
                        .headings;
                        font-size: 1.5rem;
                        letter-spacing: 0.4px;
                        text-align: center;
                        color: #000000;
                        margin: 0;

                        span {
                            .headings;
                            font-size: 1.2rem;
                            letter-spacing: 0.4px;
                            color: #787777;
                        }
                    }

                    p {
                        .paragraphs;
                        font-size: 1.7rem;
                        text-align: center;
                        color: #000000;
                        margin: 0;
                    }
                }
            }
        }
    }
}

.sms-prime {
    .page-header;
    .block-header {
        background-color: #51b564;
        cursor: pointer;

        &.collapsed {
            background-color: #c1c1c1;
        }

        h3 {
            color: #fff;
            margin: 1rem;
            font-weight: 400;
            font-size: 2rem;

            span {
                float: right;
                /*font-size: 1.2rem;
                margin-top: .3rem;*/
            }
        }
    }

    .container-fluid {
        //.config-container;

        .important-notes {
            p {
                margin: 0;
                font-size: 1.4rem;
                color: #535353;
            }
        }
    }

    form.form-horizontal.prime-sms-configurator-form {
        background-color: #fff;
        padding: 5rem;
        border-radius: 3px;
        .p-error {
            color: #b94a48;
            margin: 2rem 2rem 0;
        }
         .form-group {
            margin: 0;
            margin:2rem;
            label {
                font-size: 1.4rem;
                font-weight: normal;
                font-style: normal;
                font-stretch: normal;
                text-align: left;
                color: #000000;
                &.disabled {
                    opacity:0.5;
                    color:#555555;
                }
            }

            input.form-control {
                height: 4rem !important;
                padding: 0.6rem 1.2rem;
                font-size: 1.4rem;
                line-height: 1.42857143;
                background-color: #ffffff;
                background-image: none;
                border: 1px solid #cccccc;
                border-radius: 0.4rem;
                -webkit-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075);
                box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075);
                -webkit-transition: border-color ease-in-out 0.15s, box-shadow ease-in-out 0.15s;
                -o-transition: border-color ease-in-out 0.15s, box-shadow ease-in-out 0.15s;
                transition: border-color ease-in-out 0.15s, box-shadow ease-in-out 0.15s;
                &:focus {
                    box-shadow: none !important;
                }
            }
            select.form-control {
                height: 4rem;
                padding: .6rem 1.2rem;
                font-size: 1.4rem;
                line-height: 1.42857143;
                color: #555555;
                background-color: #ffffff;
                border: 1px solid #cccccc;
                border-radius: 0.4rem;
                -webkit-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075);
                box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075);
                -webkit-transition: border-color ease-in-out 0.15s, box-shadow ease-in-out 0.15s;
                -o-transition: border-color ease-in-out 0.15s, box-shadow ease-in-out 0.15s;
                transition: border-color ease-in-out 0.15s, box-shadow ease-in-out 0.15s;
                &:focus {
                    box-shadow: none !important;
                }
            }

            .form-control[disabled], fieldset[disabled] .form-control {
                -webkit-box-shadow: none;
                box-shadow: none;
                //background-color: #eee !important;
                opacity: .5;
            }
             .sms_size {
                background: #f5f5f5;
                top: 0rem;
                height: 2rem;
                font-size: 1.1rem;
                border-left: 1px solid #ccc;
                border-right: .1rem solid #ccc;
                border-bottom: .1rem solid #ccc;
            }
             .input-group {
                 .form-control:not(:last-child) {
                    border-bottom-right-radius: 0;
                    border-top-right-radius: 0;
                    border-right: 0 !important;
                 }
             }
            .input-group-addon {
                padding: .1rem 1rem;
                margin-bottom: 0;
                font-size: 1.5rem;
                font-weight: 400;
                line-height: 1.25;
                color: #464a4c;
                text-align: center;
                background-color: #eceeef;
                border: 1px solid rgba(0,0,0,.15);
                border-bottom-right-radius: .4rem;
                border-top-right-radius: .4rem;
            }
            /*.form-control.has-error {
                border-color: #b94a48 !important;
                -webkit-box-shadow: inset 0 1px 1px rgba(0,0,0,0.075) !important;
                box-shadow: inset 0 1px 1px rgba(0,0,0,0.075) !important;
            }*/
            
        }
    }
    .prime-sms-history {
        background-color: #fff;
        padding-bottom:1rem;
        margin-top: 5rem;
        border-radius: 3px;
        -webkit-box-shadow: 0px 0px 7px 2px rgba(226,226,226,0.87);
        -moz-box-shadow: 0px 0px 7px 2px rgba(226,226,226,0.87);
        box-shadow: 0px 0px 7px 2px rgba(226,226,226,0.87);

        .prime-sms-table {
            font-size: 1.4rem;
            
            thead {
                background: cadetblue;
                color: #fff;
                font-weight:500;
            }

            tbody {
                background: #fff;

                tr {
                    td {
                        padding:1.6rem;
                        input[type=text] {
                            height: 3.2rem;
                            box-shadow: none;
                            background: #fff;
                            border: solid 1px #fff;
                            padding: 1rem;
                            width: 10rem;
                        }

                        input[disabled], select[disabled] {
                            cursor: not-allowed;
                            opacity: .65;
                            filter: alpha(opacity=65);
                            -webkit-box-shadow: none;
                            box-shadow: none;
                        }
                        .comm-delete{
                            display:flex;
                            align-items:center;
                            justify-content:center;
                            margin:0;
                        }

                    }
                }
            }
            /*.discount_input {
                width: 5rem;
            }
            .preview-column {
                width: 30rem;
            }*/
            select {
                height: 3rem;
                /*width: 5rem;*/
                width: 10rem;
                padding: .5rem;
                font-size: 1.3rem;
                box-shadow: 0px 1px 1px 0px rgba(7, 7, 7, 0.41);
                border: solid 1px rgba(205, 205, 205, 0.55);
                background-color: #fff;
                /*&#valid_upto {
                    width: 7rem;
                }*/
            }

            .info-question-sign {
                top: 0;
                cursor: help;
            }
        }
    }
    
}

.store-operations-container {
    .basiconfig-container;

    #operations-list {
        margin-top: 1rem;
        font-size: 1.2rem;
        background-color: #ffffff;
        margin-bottom: 0;

        thead {
            background: #31459a;
            color: #fff;
        }

        tbody {
            text-align: left;
        }
    }

    .container-fluid {
        .config-container;
    }
}

.feature-toggle-container {
    .sub-container-background;
    .blue_white_table;
    font-size: 1.2rem;

    table {
        thead th {
            text-align: center;
        }
    }
}

.user-management {
    background: #fff;
    min-height: 10rem;
    padding: 1.5rem;
    font-size: 1.2rem;
    position: relative;



    h4.header {
        color: rgba(136, 136, 136, 0.8);
        text-align: center;
    }

    .row {
        margin: 2rem 0;

        .label-column {
            font-size: 1.6rem;
            font-weight: 500;
            color: #000;
        }
    }

    .locations-selector {
        
        .select2-container-multi .select2-choices .select2-search-field input {
            min-height: 3.4rem;
        }

        .ui-select-input-group .select2-results .select2-result-label {
            padding: 1rem;
        }

        .glyphicon-refresh-animate {
            position: absolute;
            top: 12px;
            right: 25px;
           -animation: spin .7s infinite linear;
           -webkit-animation: webkit-spin .7s infinite linear;
        }
    }

    .parent-selector {
        input[type=text], input[type=number], input[type=password] {
            height: auto !important;
        }

        .change-user.form-control {
            border: 1px solid #b8b8b8;
            outline: none;
            box-shadow: none;
            padding: .5rem;
            font-size: 1.2rem;
        }
    }
}

.no_data_found {
    height: 10em;
    position: relative;
}

.no_data_found p {
    margin: 0;
    //background: yellow;
    position: absolute;
    top: 50%;
    left: 50%;
    margin-right: -50%;
    transform: translate(-50%, -50%);
    font-weight: 500;
}

.dialogdemoBasicUsage #popupContainer {
    position: relative;
}

.dialogdemoBasicUsage .footer {
    width: 100%;
    text-align: center;
    margin-left: 20px;
}

.dialogdemoBasicUsage .footer,
.dialogdemoBasicUsage .footer > code {
    font-size: 0.8em;
    margin-top: 50px;
}

.dialogdemoBasicUsage button {
    width: 200px;
}

.dialogdemoBasicUsage div#status {
    color: #c60008;
}

.dialogdemoBasicUsage .dialog-demo-prerendered md-checkbox {
    margin-bottom: 0;
}



