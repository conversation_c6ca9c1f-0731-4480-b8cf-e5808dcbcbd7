.select2-container-multi .select2-search-choice-close {
    top: 6px;
    color: black;
    background: url('select2.png') right top no-repeat !important;
}

.select2-container-multi .select2-search-choice-close::before {
    content: "\e014";
}

.select2-container-multi .select2-choices .select2-search-choice {
    line-height: 2rem !important;
}

.overlay{
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(251, 255, 0, 0.13);
    border-radius: 2px;
    z-index: 90;
    margin: -2px -5px;
    min-height:40px;
}

.overlay > div{
    position: absolute;
    top: 50%;
    text-align: center;
    margin-top: -35px;
    background-color: rgba(213, 251, 207, 0.8);
    border-radius: 10px;
    left: 49%;
    height: 70px;
    padding-top: 5px;
}

.overlayText {
    font-size: 10px;
    background-color: #fff;
    padding: 3px 8px;
}

.btn-circle {
    width: 49px;
    height: 49px;
    text-align: center;
    padding: 5px 0;
    font-size: 20px;
    line-height: 2.00;
    border-radius: 30px;
}

.btn-circle-micro {
    width: 19px;
    height: 19px;
    text-align: center;
    padding: 1px 0;
    font-size: 13px;
    line-height: 0.1;
    border-radius: 30px;
}

.btn-circle-sm {
    width: 35px;
    height: 35px;
    text-align: center;
    padding: 2px 0;
    font-size: 20px;
    line-height: 1.65;
    border-radius: 30px;
}

.btn-circle-lg {
    width: 79px;
    height: 79px;
    text-align: center;
    padding: 13px 0;
    font-size: 30px;
    line-height: 2.00;
    border-radius: 70px;
}

.uploader
{
	border: 1px solid #ddd;
	width: 100%;
	color: #92AAB0;
	text-align: center;
	vertical-align: middle;
	padding: 20px 0px;
	margin-bottom: 10px;
	font-size: 200%;

	cursor: default;

	-webkit-touch-callout: none;
	-webkit-user-select: none;
	-khtml-user-select: none;
	-moz-user-select: none;
	-ms-user-select: none;
	user-select: none;
}

.uploader div.or {
	font-size: 50%;
	font-weight: bold;
	color: #C0C0C0;
}

.uploader div.browser label {
	background-color: #5a7bc2;
	padding: 5px 15px;
	color: white;
	padding: 6px 0px;
	font-size: 40%;
	font-weight: bold;
	cursor: pointer;
	border-radius: 2px;
	position: relative;
	overflow: hidden;
	display: block;
	width: 300px;
	margin: 20px auto 0px auto;

	box-shadow: 2px 2px 2px #888888;
}

.uploader div.browser span {
	cursor: pointer;
}


.uploader div.browser input {
	position: absolute;
	top: 0;
	right: 0;
	margin: 0;
	border: solid transparent;
	border-width: 0 0 100px 200px;
	opacity: .0;
	filter: alpha(opacity= 0);
	-o-transform: translate(250px,-50px) scale(1);
	-moz-transform: translate(-300px,0) scale(4);
	direction: ltr;
	cursor: pointer;
}

.uploader div.browser label:hover {
	background-color: #427fed;
}
.fileList {
  float:right;
  width: 330px;
  height:100px;
	margin: 0px;
	padding: 0px;
	list-style-type: none;
	color: gray;
  
  font-size: 12px;
  
  overflow: auto;
}

.fileList .file {
  width: 290px;
  margin-bottom: 15px;
}

.fileList .info {
  width: 290px;
  height: 26px;
  display: block;
  overflow: hidden;
  line-height: 13px;
}

.fileList .filename {
  font-weight: bold;
}

.fileList .bar {
  border: solid 1px #C0C0C0;
  height: 12px;
  margin-top: 5px;
  padding: 1px;
  width: 290px;
}

.fileList .progress {
  height: 12px;
  background-color: #00CCFF;
}

.fileList span.success {
  color: #009900;
}

.fileList span.error {
  color: #990000;
}