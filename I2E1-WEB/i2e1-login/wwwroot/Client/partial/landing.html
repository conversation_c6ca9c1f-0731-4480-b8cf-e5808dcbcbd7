<div class="messege">
    <span class="success"></span>
    <span class="error"></span>
    <span class="info"></span>
</div>
<div class="title">
    <img src="../images/logo.png" />
    <div class=" row user-row">
        <div class="icon-area" title="Click to impersonate" ng-click="openImpersonateInput()"><span class="glyphicon glyphicon-user"></span></div>
        <div id="impersonate_input" class="impersonate-input display-none">
            <input placeholder="View As..." ng-model="viewAs" ng-keypress="impersonate($event, viewAs)" />
        </div>
        <span class="label-name">{{name}}</span>
        <span class="label-username">{{username}}</span>
    </div>
    <div class="brand-selector">
        <select style="width: 17rem; height: 3.5rem; border: 1px solid #bcbcbc; margin-top: 0.5rem;" ng-options="partner.partnerId as partner.partnerName for partner in partnersForFilterList"
               ng-model="partnerId" ng-change="setBrand()"></select>

    </div>

    <div class="links-group">
        <a ng-if="featureEnabled(20) === 1" class="link" 
           href="{{featureEnabled(35) === 1 && featureEnabled(36) === 1 ? '/OneInsights/Index#/overview':'/OneInsights/Index#/wifimetrics'}}" 
           title="Analytics Portal">Analytics Portal</a>
    </div>
    <div class="signout" ng-click="logout()" title="Signout"><span class="glyphicon glyphicon-off"></span></div>
    
</div>

<div class="left-side-icon-bar" ng-click="hideMenu()">
    <div ng-if="row.feature == '' || featureEnabled(row.feature) === 1" ng-class="data.selected===row.label?'active':''"
         ng-repeat="row in leftRail track by $index" ng-click="data.selected=row.label;">
        <div ng-if="row.sref" class="row" ui-sref="{{row.sref}}">
            <div class="icon-area"><img src="../images/{{row.icon}}.png" /></div>
            <span class="label-area">{{row.label}}</span>
            <span ng-if="row.submenu" class="action-area"
                  href="#SubMenu{{$index}}" data-toggle="collapse" collapse-toggler>
                <i class="glyphicon glyphicon-chevron-down"></i>
            </span>
        </div>

        <div ng-if="!row.sref" class="row">
            <div class="icon-area"><img src="../images/{{row.icon}}.png" /></div>
            <span class="label-area">{{row.label}}</span>
            <span ng-if="row.submenu" class="action-area"
                  href="#SubMenu{{$index}}" data-toggle="collapse" collapse-toggler>
                <i class="glyphicon glyphicon-chevron-down"></i>
            </span>
        </div>

        <div ng-if="row.submenu" class="collapse list-group-submenu" id="SubMenu{{$index}}">
            <a ng-repeat="menu in row.submenu"
               ng-click="data.selected=menu.label;"
               ng-if="menu.feature == '' || featureEnabled(menu.feature) === 1"
               ui-sref="{{menu.sref}}" class="label-area list-group-item"
               data-parent="#SubMenu{{$index}}">{{menu.label}}</a>
        </div>
    </div>
</div>

<div class="menu" ng-click="openMenu()">
    <div class="icon-area"><span class="glyphicon glyphicon-menu-hamburger"></span></div>
</div>

<div id="admin_container" class="container">
    <div id="message-container" class="message-container message.type display-none">
        <div ng-class="msg.type" ng-repeat="msg in message.data">
            <img ng-if="msg.type=='success'" src="../../images/tick-right.png" />
            <img ng-if="msg.type=='failure'" src="../../images/cross.png" />
            {{msg.msg}}
        </div>
    </div>
    <h5 ng-if="accountInfo"> Account - {{accountInfo.clientName}}</h5>
    <h6> Account balance - Rs. {{finalBalance ? finalBalance : 0}}</h6>
    <h6 ng-if="accountInfo"> Rs. {{finalBalance}} can be utilised to send {{finalBalance * 5}} sms</h6>
    <div ng-if="isSalesAdmin">
        View as client to see client's properties.<br />
        To view properties of client's click user icon on top left of the screen and enter username of client and press enter.
    </div>
    <div ui-view></div>
</div>
<div class="footer" style="position: fixed;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 100;
    text-align: center;
    background: white;
    border-top: 1px solid #c4c4c4;
    color: black;
    line-height: 2;
    font-weight: bold;
    font-size: 1.5rem;">
    For any assistance, call us immediately on +91-********** or
    mail us at: <EMAIL>
</div>