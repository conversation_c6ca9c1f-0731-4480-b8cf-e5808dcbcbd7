<div class="login-container i2e1-container">
    <img class="logo" src="../../images/logo.png" />

    <div ng-if="add_device_state==1">
        ﻿<form id="addDeviceForm1" method="post" class="i2e1-form">
            <h6 class="header">Enter device unique key</h6>
            <div class="details">
                <div class="cell">
                    <input type="text" placeholder="Device MAC" ng-model="storeDetails.deviceId" class="form-control" />
                </div>
                <div class="cell">
                    <button type="submit" ng-click="validateKey()" class="btn btn-primary btn-lg blue">Next</button>
                </div>
            </div>
        </form>
    </div>
    <div ng-if="add_device_state==2">
        ﻿<form novalidate id="addDeviceForm2" method="post" class="i2e1-form">
             <div class="details">
                 <div class="entry-seperator">
                     <h6 class="header">Business Details</h6>
                 </div>

                 <div class="cell">
                     <input placeholder="Establishment Name" ng-model="storeDetails.storeName" name="storeName" type="text" class="form-control" />
                 </div>

                 <div class="cell">
                     <input placeholder="Establishment Type" ng-model="storeDetails.category" name="category" class="form-control" />
                 </div>
                 
                 <div class="entry-seperator">
                     <h6 class="header">Address details</h6>
                 </div>

                 <div class="cell">
                     <input placeholder="Establishment Address" ng-model="storeDetails.address" name="address" class="form-control" />
                 </div>
                 <div class="cell">
                     <input placeholder="Establishment City" ng-model="storeDetails.city" name="city" class="form-control" />
                 </div>
                 <div class="cell">
                     <input placeholder="Establishment State" ng-model="storeDetails.state" name="state" class="form-control" />
                 </div>

                 
                 <div class="entry-seperator">
                     <h6 class="header">Contact person details</h6>
                 </div>

                 <div class="cell">
                     <input placeholder="Contact Person Name" ng-model="storeDetails.contactPerson" name="contactPerson" class="form-control" />
                 </div>
                 <div class="cell">
                     <input placeholder="Contact Person Phone" ng-model="storeDetails.contactNumber" name="contactNumber" type="number" class="form-control" />
                 </div>
                 <div class="cell">
                     <input placeholder="Contact Person email" ng-model="storeDetails.emailId" type="email" name="email" class="form-control" />
                 </div>

                 <div class="cell">
                     <button type="submit" class="btn btn-primary blue" ng-click="submitLocationDetails()">Done</button>
                 </div>

             </div>
        </form>
    </div>
</div>

