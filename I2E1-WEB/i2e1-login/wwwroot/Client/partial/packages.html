<div class="row no-margin packages-container">
    <div class="craete-packages col-md-12 no-pad" ng-if="isConfigAdminUser">
        <h4 class="header">Create New Package</h4>
        <hr />
        <div class="package-from">
            <div class="col-md-12 form-element">
                <div class="form-group">
                    <div class="col-md-2">
                        <label class="control-label">Package name</label>
                    </div>
                    <div class="col-md-6">
                        <input type="text" class="form-control" ng-model="newpackage.packageName" placeholder="Package Name" />
                    </div>
                </div>
            </div>

            <div class="col-md-12 form-element">
                <div class="form-group">
                    <div class="col-md-2">
                        <label class="control-label">SMS count</label>
                    </div>
                    <div class="col-md-6">
                        <input type="number" class="form-control" ng-model="newpackage.packageJsonData.smsCount" placeholder="Sms count" />
                    </div>
                </div>
            </div>

            <div class="col-md-12 form-element">
                <div class="form-group">
                    <div class="col-md-2">
                        <label class="control-label">Price</label>
                    </div>
                    <div class="col-md-6">
                        <input type="number" class="form-control" ng-model="newpackage.price" placeholder="Price" />
                    </div>
                </div>
            </div>

            <div class="col-md-12 form-element">
                <div class="form-group">
                    <div class="col-md-2">
                        <label class="control-label">Validity</label>
                    </div>
                    <div class="col-md-6">
                        <select class="form-control" ng-model="newpackage.packageValidity">
                            <option value="3" ng-selected="selected">3 Months</option>
                            <option value="6">6 Months</option>
                            <option value="9">9 Months</option>
                            <option value="12">12 Months</option>
                        </select>
                    </div>
                </div>
            </div>

            <div class="col-md-12 form-element">
                <div class="form-group">
                    <div class="col-md-2">
                        <label class="control-label">Add features</label>
                    </div>
                    <div class="col-md-6">
                        <div class="col-md-12 selects input-group">
                            <i class="glyphicon glyphicon-search"></i>
                            <selectize config='featureListConfig'
                                       options='featureList'
                                       ng-model="newpackage.features"
                                       title="Select Feature, type to search"></selectize>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-md-12 form-element">
                <div class="form-group">
                    <button class="btn btn-primary pull-right" ng-click="createPackages()">Create</button>
                </div>
            </div>
        </div>
    </div>

    <div class="package-features col-md-12 no-pad" ng-if="featureEnabled(22) == 1">
        <h4 class="header">Packages</h4>
        <hr />
        <div class="packages-details-container packages row">
            <div id="credentialCheck" class="credential-check display-none">
                <div class="abs-div">
                    <div class="rel-div" ng-include="'/askCredentials'"></div>
                </div>
            </div>
            <div class="package" ng-repeat="package in myPackages">
                <div class="price">Rs {{package.price}}</br><span style="font-size: 1.2rem;">Valid for {{package.packageValidity}} months</span></div>
                <div class="features custom_scroll" style="max-height: 26rem;overflow: auto;">
                    <h4 class="feature-heading">{{package.packageName}}</h4>
                    <div class="feature" ng-repeat="feature in package.features">{{feature.name}}</div>
                </div>
                <div class="btn-div">
                    <button class="btn btn-primary" ng-click="upgrade(package)">Upgrade</button>
                </div>
            </div>
        </div>
    </div>

  
</div>
<script type="text/ng-template" id="/askCredentials">
    <div class="askCredentials">
        <h6 class="askCredentials-header">Please verify credentials to proceed</h6>
        <span ng-if="askCredentials.error" class="askCredentials-error">{{askCredentials.error}}</span>
        <form id="askCredentials" class="credential-details" method="post" action="{{askCredentials.action}}">
            <div class="login-details">
                <div class="cell">
                    <input placeholder="Username" name="username" type="text" class="form-control" />
                </div>

                <div class="cell">
                    <input placeholder="Password" name="password" type="password" class="form-control" />
                    <input type="hidden" name="googleToken" id="googleToken" />
                    <input type="hidden" name="authType" id="authType" />
                    <input type="hidden" name="{{field.name}}" value="{{field.value}}" ng-repeat="field in askCredentials.formData" />
                </div>

                <div class="row">
                    <div class="cell">
                        <button type="button" class="btn btn-primary btn-lg login" ng-click="askCredentials.OK(askCredentials)">{{askCredentials.OKText}}</button>
                    </div>
                </div>
                <div class="partition">
                    <div class="left-part">

                    </div>
                    <div class="text">Or</div>
                    <div class="right-part">

                    </div>
                </div>
                <div class="cell">
                    <div id="gSignInWrapper" style="text-align: center;">
                        <div id="verifyBtn" class="customGPlusSignIn">
                            <span class="icon"></span>
                            <span class="buttonText">Verify Using Google</span>
                        </div>
                    </div>
                    <div ng-init="askGoogleCredentials(askCredentials)"></div>
                </div>
            </div>
        </form>
    </div>
</script>