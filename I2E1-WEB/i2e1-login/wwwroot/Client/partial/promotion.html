<div class="offers-container" feature-toggle="8">
    <div class="page-content">
        <div class="row campaign_content">
            <ul class="col-md-12 no-pad nav nav-pills">
                <li class="col-md-6 no-pad active">
                    <a showtab href="#create_campaign" data-toggle="tab" id="create_campaign_nav">Create New Promotion</a>
                </li>
                <li class="col-md-6 no-pad">
                    <a showtab href="#campaign_history" data-toggle="tab" ng-click="checkOffers()">Existing Promotions</a>
                </li>
            </ul>
            <div class="tab-content clearfix col-md-12">
                <div class="tab-pane active" id="create_campaign" ng-init="createOffer()">
                    <div class="row" style="font-size: 2rem; color:red;padding-left: 1rem;">
                        Last {{data.offer.dater}} unique users : {{data.reach || 0}}
                    </div>
                    <div class="row">
                        <div class="col-md-3 preview-section">
                            <div class="preview-container">
                                <div class="preview_campaign_image_container" ng-if="data.offerPreviewAvailable(data.offer)">
                                    <div class="img" ng-if="data.offer.offer_type == 0">
                                        <a ng-if="data.offer.image && data.offer.image_link" target="_blank" ng-href="{{data.offer.image_link}}">
                                            <img ng-if="!data.offer.offerCode" ng-src="{{data.offer.image}}" class="splashImage" />
                                            <img ng-if="data.offer.offerCode" ng-src="{{data.offer.image}}" class="splashImage" />
                                        </a>
                                        <img ng-if="data.offer.image && !data.offer.image_link && !data.offer.offerCode"
                                             ng-src="{{data.offer.image}}" class="splashImage" />
                                        <img ng-if="data.offer.image && !data.offer.image_link && data.offer.offerCode"
                                             ng-src="{{data.offer.image}}" class="splashImage" />
                                    </div>
                                    <div class="video" ng-if="data.offer.offer_type == 1" >
                                        <video width='100%' autoplay="true" onclick="this.play()">
                                            <source ng-src="{{data.offer.videoPath | trustUrl}}" type='video/mp4'>
                                            <source ng-src="{{data.offer.videoPath | trustUrl}}" type='video/ogg'>
                                            <source ng-src="{{data.offer.videoPath | trustUrl}}" type='video/avi'>
                                            <source ng-src="{{data.offer.videoPath | trustUrl}}" type='video/mov'>
                                            <source ng-src="{{data.offer.videoPath | trustUrl}}" type='video/webm'>
                                            Your browser does not support HTML5 video.
                                        </video>";
                                    </div>
                                </div>
                                <div id="result_container" class="campaign" ng-if="!data.offerPreviewAvailable(data.offer)">
                                    <img class="splashImage" style="max-height: 35rem;" src="../../images/coupon_placeholder.png" />
                                </div>
                            </div>
                            <div class="upload_buttons_hidden">
                                <div id="upload_file" class="campaign-img">
                                    <div class="browser">
                                        <label>
                                            <input type="file" name="file" id="file_upload" title="Click to add File">
                                        </label>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-12 upload_buttons no-pad">
                                <div class="col-md-3 col-md-offset-1 no-pad">
                                    <button class="btn btn-primary-green" ng-click="uploadCouponImage()">Upload</button>
                                </div>
                                <div class="col-md-7 no-pad">
                                    <span class="file_name" id="file_name"></span>
                                    <h5>(Image size should be less then 50KB)</h5>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-1"></div>
                        <div class="col-md-7">
                            <div class="col-md-12 no-pad locations-selector">
                                <div class="ui-select-input-group">
                                    <div class="input-group-addon">Apply To : </div>
                                    <div class="cell location">
                                        <ui-select multiple reset-search-input="true" ng-model="data.offer.locations" theme="select2" close-on-select="false" ng-change="data.checkMultiselect(data.offer)">
                                            <ui-select-match placeholder="Locations">
                                                {{$item.storeName}}
                                            </ui-select-match>
                                            <ui-select-choices repeat="router in positiveNases |
                                                           propsFilter: {nasid: $select.search, storeName: $select.search} |
                                                           limitTo: 10">
                                                <span ng-bind-html="router.storeName | highlight: $select.search"></span>
                                                <span ng-bind-html="router.city | highlight: $select.search"></span>
                                                <span ng-bind-html="router.state | highlight: $select.search"></span>
                                                <span ng-bind-html="router.nasid | highlight: $select.search"></span>
                                            </ui-select-choices>
                                        </ui-select>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-12 custom_content">
                                <h3>Customize content</h3>
                                <h5>Please fill atleast one of the following if not using an image</h5>
                                <div class="col-md-12 custom_content_item no-pad">
                                    <div class="col-md-4 no-pad custom_content_title">SMS <i class="glyphicon glyphicon-question-sign" title="SMS is sent to user at time promotion is shown. Promotion disables if SMS balance is 0"></i>(Optional)</div>
                                    <div class="col-md-4 no-pad"><input type="text" ng-model="data.offer.text" class="form-control custom_content_input" /></div>
                                </div>
                                <div class="col-md-12 custom_content_item no-pad">
                                    <div class="col-md-4 no-pad custom_content_title">Sender id <i class="glyphicon glyphicon-question-sign" title="SMS is sent via sender id"></i>(Optional)</div>
                                    <div class="col-md-4 no-pad">
                                        <select ng-disabled="!data.offer.text && !$parent.senderIds.length" ng-model="data.offer.senderId" theme="select2" class="form-control">
                                            <option value="BULKSMS" ng-selected="(!data || !data.offer || !data.offer.senderId || data.offer.senderId == 'BULKSMS')">BULKSMS</option>
                                            <option ng-repeat="senderId in $parent.senderIds" value="{{senderId}}" ng-selected="data.offer.senderId == senderId">{{senderId}}</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-12 custom_content_item no-pad">
                                    <div class="col-md-4 no-pad custom_content_title">Image Link <i class="glyphicon glyphicon-question-sign" title="on click of image user will be taken to that link in new tab"></i> (Optional)</div>
                                    <div class="col-md-4 no-pad"><input type="text" ng-model="data.offer.image_link" class="form-control custom_content_input" /></div>
                                </div>
                            </div>
                            <div class="col-md-12 rules">
                                <h3>Rules</h3>
                                <div class="row">
                                    <div class="col-md-3 rule_title">Date range</div>
                                    <div class="col-md-4" title="choose from date">
                                        <input placeholder="from" class="form-control" type="text" ng-model="data.offer.fDate" date-range-picker options="data.startDateOptions"  /> <!--date-picker="data.startDateOptions"-->
                                    </div>
                                    <div class="col-md-4" title="choose to date">
                                        <input placeholder="to" class="form-control" type="text" ng-model="data.offer.tDate" date-range-picker options="data.endDateOptions"  /> <!--date-picker="data.endDateOptions"-->
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-md-3 rule_title">Placement</div>
                                    <div class="col-md-4" title="where to show promotion">
                                        <select class="form-control" ng-model="data.offer.voucherPlacement">
                                            <option value="41">Coupon page</option>
                                            <option value="45">Before login</option>
                                            <option value="43">After phone number before otp</option>
                                        </select>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-3 rule_title">Resting rules</div>
                                    <div class="col-md-4" title="target a customer repeatly">
                                        <select class="form-control" ng-model="data.offer.dater" ng-change="data.checkMultiselect(data.offer)">
                                            <option value="day">Once everyday</option>
                                            <option value="week">Once in a week</option>
                                            <option value="month">Once in a month</option>
                                        </select>
                                    </div>
                                </div>
                                <!--<div class="row">
                                    <div class="col-md-3 rule_title">Is New</div>
                                    <div class="col-md-9" title="choose from date">
                                        <label>
                                            <input type="radio" ng-model="data.offer.promotionParameters.analyticsFilter.isNew" value="true" /> Yes
                                        </label>
                                        <label>
                                            <input type="radio" ng-model="data.offer.promotionParameters.analyticsFilter.isNew" value="false" /> No
                                        </label>
                                    </div>
                                </div>-->
                                <div class="row">
                                    <div class="col-md-3 rule_title">Gender</div>
                                    <div class="col-md-9" title="choose from date">
                                        <label>
                                            <input type="radio" ng-model="data.offer.promotionParameters.analyticsFilter.gender" value="M" /> Male
                                        </label>
                                        <label>
                                            <input type="radio" ng-model="data.offer.promotionParameters.analyticsFilter.gender" value="F" /> Female
                                        </label>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-md-3 rule_title">Affluence</div>
                                    <div class="col-md-9" title="choose from date">
                                        <label>
                                            <input type="radio" ng-model="data.offer.promotionParameters.analyticsFilter.affluence" value="L" /> Low
                                        </label>
                                        <label>
                                            <input type="radio" ng-model="data.offer.promotionParameters.analyticsFilter.affluence" value="M" /> Medium
                                        </label>
                                        <label>
                                            <input type="radio" ng-model="data.offer.promotionParameters.analyticsFilter.affluence" value="H" /> High
                                        </label>
                                    </div>
                                </div>

                                <p class="info_text">If no rules are choosen then promotion will be shown once daily.</p>
                            </div>
                            <div class="buttons col-md-12 no-pad">
                                <button ng-click="saveOffer(data.offer, 'save')" class="btn btn-primary pull-right">Save</button>
                            </div>
                        </div>
                    </div>

                    <!--<div class="row">
                        <div id="this-campaign-history" class="this-campaign-history col-md-12" ng-if="data.offer.dailyReports">
                            <h3>Daily report</h3>
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>Date</th>
                                        <th>Views</th>
                                        <th>Clicks</th>
                                        <th>SMS delivered</th>
                                        <th>Burn</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr ng-repeat="(key,report) in data.offer.dailyReports">
                                        <td>{{getDateString(key)}}</td>
                                        <td>{{report.views}}</td>
                                        <td>{{report.clicks}}</td>
                                        <td>{{report.sent_sms}}</td>
                                        <td><i>calculating...</i></td>
                                    </tr>
                                    <tr>
                                        <td><b>Total</b></td>
                                        <td><b>{{data.offer.report.views}}</b></td>
                                        <td><b>{{data.offer.report.clicks}}</b></td>
                                        <td><b>{{data.offer.report.sent_sms}}</b></td>
                                        <td><i>calculating...</i></td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>-->


                </div>
                <div class="tab-pane" id="campaign_history">
                    <div class="table-responsive campaign-reports">
                        <table class="table campaign-reports-table" infinite-scroll='loadMoreOffers()' infinite-scroll-container='"#admin_container"'>
                            <caption>SMS campaign reports.</caption>
                            <tbody ng-repeat="offer in offers">
                                <tr data-toggle="collapse" data-target="#collapsibleRow{{offer.id}}" ng-click="getOffersDailyReport(offer,1,100)">
                                    <td width="25%">
                                        <div class="campaign" ng-class="offer.active?'':'inactive'" title="Id: {{offer.id}}">
                                            <div class="actions">
                                                <div ng-if="offer.active == 0" title="Run" class="glyphicon glyphicon-play" ng-click="activateOffer(offer)"></div>
                                                <div ng-if="offer.active == 1" title="Stop" class="glyphicon glyphicon-pause" ng-click="deActivateOffer(offer)"></div>
                                                <!--<div title="edit" class="glyphicon glyphicon-edit" ng-click="editOffer(offer)"></div>-->
                                            </div>
                                            <div class="border">
                                                <img ng-if="offer.image" ng-src="{{offer.image}}" />
                                                <img ng-if="!offer.image" src="../../images/coupon_placeholder.png" />
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        <ul class="list-unstyled">
                                            <li>
                                                <strong>SMS:</strong> {{offer.text || 'Not set'}}
                                            </li>
                                            <li>
                                                <strong>Sender Id:</strong> {{offer.senderApiParameters[0] || 'Not set'}}
                                            </li>
                                            <li><strong>From:</strong> {{getDateString(offer.fDate)}}</li>
                                            <li><strong>To:</strong> {{getDateString(offer.tDate)}}</li>
                                            <li tooltip-append-to-body="true"
                                                tooltip=""
                                                tooltip-class="custome-tooltip-class"><strong>Cities:</strong><span ng-repeat="location in offer.locations | unique:'city'"> {{location}}{{$last ? '' : ', '}}</span></li>
                                            <li tooltip-append-to-body="true"
                                                tooltip=""
                                                tooltip-class="custome-tooltip-class"><strong>Locations:</strong><span ng-repeat="location in offer.locations | unique:'storeName'"> {{location}}<b style="font-size:14px;">{{$last ? '' : ',  '}}</b></span></li>
                                        </ul>
                                    </td>
                                    <td>
                                        <ul class="list-unstyled">
                                            <li>
                                                <strong>SMS Delivered:</strong> {{offer.smsDelivered || 0}}
                                            </li>
                                            <li><strong>SMS Clicked:</strong> {{offer.smsClicks || 0}}</li>
                                            <li>
                                                <strong>Coupons Viewed:</strong> {{offer.couponsViewed || 0}}
                                            </li>
                                            <li ng-if="isSalesAdmin || isSuperAdmin || isConfigAdminUser || isOpsAdmin || isReadOnlyAdmin">
                                                <strong>Coupons Clicked:</strong> {{offer.couponClicks || 0}}
                                            </li>
                                            <li>
                                                <strong>Burn:</strong> {{offer.consumption || 0}}
                                            </li>
                                        </ul>
                                    </td>
                                    <td>
                                        <ul class="list-unstyled">
                                            <li><a href="" class="pull-right" ng-click="consolidateNow($event, offer)"> Refresh</a></li>
                                        </ul>
                                    </td>
                                </tr>
                                <tr class="collapse" id="collapsibleRow{{offer.id}}">
                                    <td colspan="4">
                                        <table class="table table-bordered table-striped">
                                            <thead>
                                                <tr>
                                                    <th>Date</th>
                                                    <th>Coupon Viewed</th>
                                                    <th>Coupon Clicked</th>
                                                    <th>SMS delivered</th>
                                                    <th>SMS Clicked</th>
                                                    <th>Burn</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <tr ng-repeat="(key,report) in offer.dailyReports">
                                                    <td>{{getDateString(key)}}</td>
                                                    <td>{{report.coupons_viewed}}</td>
                                                    <td>{{report.coupon_clicks}}</td>
                                                    <td>{{report.sms_delivered}}</td>
                                                    <td>{{report.sms_clicks}}</td>
                                                    <td>{{report.campaign_consumption}}</td>
                                                </tr>
                                            </tbody>
                                        </table>
                                        <div class="pull-right">
                                            <!--<pagination class="pagination-sm"
                                                        total-items="100"
                                                        ng-model="pagination.currentPage"
                                                        max-size="pagination.maxSize"
                                                        items-per-page="10"
                                                        ng-change="getOffersDailyReport(offer,1,10)"></pagination>-->
                                            <pagination total-items="offer.pagination.totalItems" ng-model="offer.pagination.currentPage"
                                                        max-size="offer.pagination.maxSize" boundary-links="true"
                                                        items-per-page="offer.pagination.numPerPage" class="pagination-sm"
                                                        ng-change="getOffersDailyReport(offer)">
                                            </pagination>
                                        </div>
                                    </td>
                                </tr>

                            </tbody>
                        </table>
                        
                        <div class="no_offers_present" ng-if="!offers.length">
                            <span>You are yet to create a promotion<br />To create one <a onclick="document.getElementById('create_campaign_nav').click()">click here</a></span>
                            <span ng-if="isSalesAdmin"><br />Or</span>
                            <p ng-if="isSalesAdmin">Impersonate to see client's promotions</p>
                        </div>
                    </div>
                    <br style="clear:both" />
                </div>
            </div>
            <br style="clear:both" />
        </div>
    </div>
</div>

<script>
    document.getElementById('file_upload').onchange = function () {
        var uploadingFile = this.files[0];
        document.getElementById('file_name').innerHTML = uploadingFile.name;
    };

</script>