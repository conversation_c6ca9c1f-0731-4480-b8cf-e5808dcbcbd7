<div class="sms-campaign-reports" feature-toggle="7">
    <p><strong>Remaining Balance:</strong> Rs. {{roundToTwo(finalBalance)}}</p>

    <div class="table-responsive">
        <table id="rateTable" class="table table-sm table-bordered table-striped table-condensed" style="font-size: 1.2rem;background: #fff;">
            <thead>
                <tr>
                    <th>Product</th>
                    <th>Price per delivery(Rs)</th>
                    <th>Price per click(Rs)</th>
                </tr>
            </thead>
            <tbody>
                <tr ng-repeat="product in products">
                    <td>{{product.name}}</td>
                    <td>{{product.costPerDelivery}}</td>
                    <td>{{product.costPerClick}}</td>
                </tr>
            </tbody>
        </table>
        
    </div>
    <div class="table-responsive">
        <table id="balanceTable" class="table table-sm table-bordered table-striped table-condensed" style="font-size: 1.2rem;background: #fff;">
            <thead>
                <tr>
                    <th>Date</th>
                    <th>Ammount</th>
                    <th>Remark</th>
                </tr>
            </thead>
            <tbody>
                  <tr  ng-repeat="balance in pagination.filteredItems track by $index">
                      <td>{{balance.date}}</td>
                      <td>{{balance.ammount}} {{balance.tr_type}}</td>
                      <td>{{balance.remark}}</td>
                  </tr>
            </tbody>
        </table>
        <!--<pagination total-items="totalItems"
                    ng-model="currentPage"
                    ng-change="pageChanged()"></pagination>-->
        <div class="pull-right">
            <pagination class="pagination-sm"
                        total-items="pagination.totalItems"
                        ng-model="pagination.currentPage"
                        max-size="pagination.maxSize"
                        items-per-page="pagination.itemsPerPage"
                        ng-change="pageChanged()"></pagination>
        </div>
    </div>
</div>

