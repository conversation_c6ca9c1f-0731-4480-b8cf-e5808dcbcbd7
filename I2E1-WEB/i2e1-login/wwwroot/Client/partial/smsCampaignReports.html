<div class="sms-campaign-reports" feature-toggle="7">
    <div class="btn-group pull-right">
        <a class="btn btn-primary" href="#/landing/smsSender" style="position: absolute;right: 0;top: -5rem;">Create new</a>
    </div>
    <div class="table-responsive campaign-reports" infinite-scroll='loadMoreSMS(true)' infinite-scroll-container='"#admin_container"' infinite-scroll-parent='true' infinite-scroll-disabled='smsHistory.infiniteScrollDisabled'>
        <table class="table campaign-reports-table">
            <thead>
                <tr class="filters">
                    <td valign="middle" colspan="3">
                        SMS campaigns
                    </td>
                    <td valign="middle" align="right" colspan="2">
                        <div>
                            <input ng-show="showSearchField" id="search-input" placeholder="--search by name--" class="form-control" ng-model="smsHistory.filters.name" ng-keyup="$event.keyCode == 13 ? loadMoreSMS() : null" />
                            <img class="icons" src="../images/filter.png" ng-click="showSearchField=!showSearchField" />
                            <span dropdown on-toggle="toggled(open)">
                                <a href id="simple-dropdown" dropdown-toggle>
                                    <img class="icons" src="../images/sort.png" />
                                </a>
                                <ul ng-click="loadMoreSMS()" style="top: 230px; left: 92%; width: 75px; min-width: 0;" class="dropdown-menu" aria-labelledby="simple-dropdown">
                                    <li ng-repeat="choice in smsHistory.sortbylist" ng-click="smsHistory.sortBy = choice.value">
                                        <a href>{{choice.name}}</a>
                                    </li>
                                </ul>
                            </span>
                        </div>
                    </td>
                </tr>
            </thead>
            
            <tbody ng-repeat="sms in smsHistory.list track by $index " class='{{sms.enabled?"":"inactive-campaign"}}'>
                <tr data-toggle="collapse" id="camp_report_{{sms.id}}" data-target="#collapsibleRow{{$index}}" ng-click="getSmsDetailedReport(sms)" title="{{sms.id}}">
                    <td width="30%;" ng-mouseover="sms.showEditIcon = true" ng-mouseleave="sms.showEditIcon = false">
                        <div class="textarea-header message_sender_details">
                            <span>{{sms.contentParameters.sender_id}}</span>
                        </div>
                        <div class="textarea">{{sms.message}}</div>
                    </td>
                    <td>
                        <ul class="list-unstyled" ng-if="sms.parameters.location.full_data_base != 'upload'">
                            <li><strong>Name :</strong> {{sms.campaignName}}</li>
                            <li tooltip-append-to-body="true"
                                tooltip="{{sms.parameters.location.cities.join(', ')}}"
                                tooltip-class="custome-tooltip-class"><strong>City:</strong>{{sms.parameters.location.cities.join(', ')}}</li>
                            <li tooltip-append-to-body="true"
                                tooltip=""
                                tooltip-class="custome-tooltip-class" ng-if="sms.campaignType == '10'"><strong>Locations:</strong><span ng-repeat="loction in sms.locations" ng-bind-html="loction.shop_name"></span></li>
                            <li ng-if="sms.parameters.notVisitedSince && sms.campaignType == '10'"><strong>Not Visted Since:</strong> {{sms.parameters.notVisitedSinceDate}}</li>
                            <li ng-if="sms.parameters.visitedSinceDate && sms.campaignType == '10'"><strong>Visted Since:</strong> {{sms.parameters.visitedSinceDate}}</li>
                            <li ng-if="sms.campaignType == '10'"><strong>Scheduled for:</strong> {{sms.scheduleStartTimeFormatted}}</li>
                            <li tooltip-append-to-body="true"
                                tooltip="{{sms.parameters.schedule.runtime}} on {{sms.parameters.schedule.rundays}}"
                                tooltip-class="custome-tooltip-class"
                                ng-if="sms.campaignType == '11'"><strong>Scheduled for:</strong>{{sms.parameters.schedule.runtime}}on{{sms.parameters.schedule.rundays}}</li>
                        </ul>

                        <ul class="list-unstyled" ng-if="sms.parameters.location.full_data_base == 'upload'">
                            <li><strong>Name :</strong> {{sms.campaignName}}</li>
                            <li ng-if="sms.campaignType == '10'"><strong>CSV method for/on {{sms.parameters.scheduledAt}}</strong> </li>
                        </ul>
                    </td>
                    <td>
                        <ul class="list-unstyled">
                            <li><strong>SMS Triggered:</strong> {{sms.smsTriggered}}</li>
                            <li><strong>SMS Delivered:</strong> {{sms.smsDelivered}}</li>
                            <li ng-if="isSalesAdmin"><strong>SMS Clicked:</strong> {{sms.smsClicks}}</li>
                        </ul>
                    </td>
                    <td>
                        <!--{{sms.campaignType}}-->
                        <ul class="list-unstyled">
                            <li ng-if="sms.campaignType == '10'"><strong>Estimated Burn:</strong>Rs. {{sms.parameters.estimated_cost ? sms.parameters.estimated_cost : 0 }}</li>
                            <li><strong>Unbilled amount:</strong>Rs. {{sms.parameters.unbilled_amount ? sms.parameters.unbilled_amount : 0 }}</li>
                            <li ng-if="sms.campaignType == '11'"><strong>Daily Estimation:</strong>Rs. {{sms.parameters.daily_estimation ? sms.parameters.daily_estimation : 0 }}</li>
                            <li><strong>Actual Burn:</strong>Rs. {{sms.consumption}}</li>
                        </ul>
                    </td>
                    <td>
                        <ul class="list-unstyled">
                            <li><a href="" class="pull-right" ng-click="consolidateNow($event, sms)"> Refresh</a></li>
                            <li><a href="" class="pull-right" ng-show="!sms.hideEnableAction && sms.enabled" ng-click="enableCampaign($event, sms, false)">Disable</a></li>
                            <li><a href="" class="pull-right" ng-show="!sms.hideEnableAction && !sms.enabled" ng-click="enableCampaign($event, sms, true)">Enable</a></li>
                        </ul>
                    </td>
                </tr>
                <tr class="collapse" id="collapsibleRow{{$index}}">
                    <td colspan="5">
                        <table class="table table-bordered table-striped" ng-if="sms.detailedReport">
                            <thead>
                                <tr>
                                    <th>Date</th>
                                    <th>SMS triggered</th>
                                    <th>SMS delivered</th>
                                    <th ng-if="isSalesAdmin">SMS clicked</th>
                                    <th>Burn</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr ng-repeat="(key,report) in sms.detailedReport">
                                    <td>{{getDateString(key)}}</td>
                                    <td>{{report.sms_sent}}</td>
                                    <td>{{report.sms_delivered}}</td>
                                    <td ng-if="isSalesAdmin">{{report.sms_clicks}}</td>
                                    <td>Rs. {{report.campaign_consumption}}</td>
                                </tr>
                            </tbody>
                        </table>
                        <div class="pull-right">
                            <pagination total-items="sms.totalItems" ng-model="sms.currentPage"
                                        max-size="sms.maxSize" boundary-links="true"
                                        items-per-page="sms.numPerPage" class="pagination-sm"
                                        ng-change="getSmsDetailedReport(sms)">
                            </pagination>
                        </div>
                    </td>
                </tr>
            </tbody>
        </table>
    </div>
</div>