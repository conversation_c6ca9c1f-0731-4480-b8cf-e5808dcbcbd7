<div class="select-filters top-filter">
    <div class="row">
        <div class="col-md-12 selects input-group">
            <i class="glyphicon glyphicon-search"></i>
            <input type="text" class="form-control" placeholder="Search Locations" ng-model="searchTerm"
                   ng-change="filterRouters()" ng-model-options="{debounce:1000}" style="padding-left: 30px;font-weight: 800;">
        </div>
    </div>
    <div class="row">
        <div id="nas-dropdown" class="col-md-12 selects input-group">
            <i class="glyphicon glyphicon-search"></i>
            <selectize config="singleLocationSelectorConfig"
                       options='storeList'
                       ng-model="location"
                       ng-change="getAllReportsForStore()"
                       class="multiple-location select-loaction"
                       title="Select multiple locations, type to search"></selectize>
        </div>
    </div>
</div>
<div class="basiconfig-container reports-container">
    <div class="row">
        <div class="page-header">
            <h4>Reports</h4>
            <!--<div><a class="btn btn-primary" href="#" download="report.csv" ng-click="exportDailyCSVReport($event)" style="width: 175px;">Export Daily Report</a></div>
            <p ng-if="router.selected.length == 1 && logs && featureEnabled(39) == 1"><b>Users in Past Hour : </b>{{usersPastHour ? usersPastHour : '0'}}</p>
            <p ng-if="router.selected.length == 1 && logs && featureEnabled(39) == 1"><b>Users in Past 24 Hours : </b>{{usersPastDay ? usersPastDay : '0' }}</p>-->
        </div>
        <div ng-if="router.selected.length == 1 && logs && featureEnabled(39) == 1" class="container-fluid config-container">
            <div class="col-md-12 config-row">
                <h5><i class="fa fa-key" aria-hidden="true"></i> Reports</h5>
                <div class="row date-range-picker">
                    <div class="date-range-btn" title="Change date ranges">
                        <button id="date-range-picker-for-users-count" class="btn btn-primary btn-lg" ng-click="showDatePicker()">Select Range</button>
                    </div>
                    <div class="from-to-dates">
                        <div class="from" title="{{dates.userCount.completeFromDate}}">
                            <span>From:</span>
                            <div class="date">
                                {{dates.userCount.fromShowDate}}
                            </div>
                        </div>
                        <div class="to" title="{{dates.userCount.completeToDate}}">
                            <span>To:</span>
                            <div class="date">
                                {{dates.userCount.toShowDate}}
                            </div>
                        </div>
                    </div>
                    <div ng-show="logs.length > 0" class="about-period single-location">
                        <a class="btn btn-primary" href="#" download="report.csv" ng-click="exportUserLoginCSVReport($event)" style="width: 175px;">Export Login Report</a>
                        <a class="btn btn-primary" href="#" download="numbers.csv" ng-click="exportUserCountCSV($event)">Export Numbers</a>
                    </div>
                </div>

                <div class="col-md-12 config-row no-pad">
                    <p>Users in This Duration {{usersThisDuration}}</p>
                    <div class="row">
                        <div class="col-md-2 col-md-offset-8 col-xs-6" style="display:none;">
                            <h2><a class="btn btn-info btn-md" ng-click="showDetails = !showDetails">{{ showDetails ? 'Hide Details' : 'Show Details'}}</a></h2>
                        </div>
                    </div>
                    <div class="row" style="margin-top:10px">
                        <div class="col-md-8 col-md-offset-1">
                            <table class="table table-hover" ng-show="showDetails">
                                <tr><th>Mobile</th><th>Device count</th><th>Login count</th></tr>
                                <tr ng-repeat="log in logs">
                                    <td>{{log.mobile}}</td>
                                    <td>{{log.device}}</td>
                                    <td>{{log.count}}</td>
                                </tr>
                            </table>
                        </div>
                    </div>
                    <div class="row" style="display:none;">
                        <div class="col-md-12 col-xs-12 no-pad">
                            <!--
                            <div id="login_chart_div" class="visible-xs chart_container"></div>
                            <div id="login_chart_sm_div" class="visible-sm chart_container"></div>
                            <div id="login_chart_md_div" class="visible-md chart_container"></div>
                            <div id="login_chart_lg_div" class="visible-lg chart_container"></div>
                            -->
                            <div id="login_chart_div" class="chart_container"></div>
                        </div>
                    </div>
                    <div class="row" ng-if="featureEnabled(55) == 1">
                        <div class="col-md-12 col-xs-12 no-pad">
                            <h5><i class="fa fa-key" aria-hidden="true"></i>Bandwidth Report</h5>
                            <table class="table table-hover" ng-show="bandwidth.length > 0">
                                <tr><th>Timestamp</th><th>Download(Mbps)</th></tr>
                                <tr ng-repeat="row in bandwidth">
                                    <td>{{row.timestamp}}</td>
                                    <td>{{row.download_bw}}</td>
                                </tr>
                            </table>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-12 col-xs-12 no-pad">
                            <h5><i class="fa fa-key" aria-hidden="true"></i> User Distribution</h5>
                            <!--
                            <div id="user_chart_div" class="visible-xs chart_container"></div>
                            <div id="user_chart_sm_div" class="visible-sm chart_container"></div>
                            <div id="user_chart_md_div" class="visible-md chart_container"></div>
                            <div id="user_chart_lg_div" class="visible-lg chart_container"></div>
                            -->
                            <div id="user_chart_div" class="chart_container"></div>
                        </div>
                    </div>
                    <div class="panel panel-default col-md-8 col-md-offset-2 col-xs-12" style="display:none;">
                        <div class="panel-body">
                            <div class="row">
                                <div class="col-md-6 col-sm-6 col-xs-12">
                                    Show
                                    <select class="form-control" ng-model="chart3" ng-change="changeChart(chart3)">
                                        <option value="1">Line Chart</option>
                                        <option value="2">Bar Chart</option>
                                    </select>
                                </div>
                                <div class="col-md-6 col-sm-6 col-xs-12">
                                    Group By
                                    <select class="form-control" ng-model="groupBy" ng-change="addGroupBy(groupBy)">
                                        <option value="1">Day</option>
                                        <option value="2">Week</option>
                                        <option value="3">Month</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-12 col-xs-12 no-pad" style="display:none;">
                            <!--
                            <div id="line_chart_div" class="visible-xs chart_container"></div>
                            <div id="line_chart_sm_div" class="visible-sm chart_container"></div>
                            <div id="line_chart_md_div" class="visible-md chart_container"></div>
                            <div id="line_chart_lg_div" class="visible-lg chart_container"></div>
                            -->
                            <div id="line_chart_div" class="chart_container"></div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-lg-12 col-xs-12 no-pad">
                            <h5><i class="fa fa-key" aria-hidden="true"></i> Users Per Day</h5>
                            <!--
                            <div id="users_line_chart_div" class="visible-xs chart_container"></div>
                            <div id="users_line_chart_sm_div" class="visible-sm chart_container"></div>
                            <div id="users_line_chart_md_div" class="visible-md chart_container"></div>
                            <div id="users_line_chart_lg_div" class="visible-lg chart_container"></div>
                            -->
                            <div id="users_line_chart_div" class="chart_container"></div>
                        </div>
                    </div>
                </div>
                <p ng-show="logs.length == 0" class="no_data_used_p">No data present, please choose wider date range</p>
            </div>
        </div>

        <div ng-if="router.selected.length == 1 && detailedDataUsages && featureEnabled(49) == 1" class="container-fluid config-container" feature-toggle="14">
            <div class="col-md-12 config-row">
                <h5><i class="fa fa-key" aria-hidden="true"></i> Detailed Reports</h5>
                <div class="row date-range-picker">
                    <div class="date-range-btn" title="Change date ranges">
                        <button id="date-range-picker-for-detailed-data-usage" class="btn btn-primary btn-lg" ng-click="showDatePicker()">Select Range</button>
                    </div>
                    <div class="from-to-dates">
                        <div class="from" title="{{dates.detailedReport.completeFromDate}}">
                            <span>From:</span>
                            <div class="date">
                                {{dates.detailedReport.fromShowDate}}
                            </div>
                        </div>
                        <div class="to" title="{{dates.detailedReport.completeToDate}}">
                            <span>To:</span>
                            <div class="date">
                                {{dates.detailedReport.toShowDate}}
                            </div>
                        </div>
                    </div>
                    <div ng-show="detailedDataUsages.length > 0" class="about-period single-location">
                        <button ng-click="exportDetailedDataUsage($event)" class="btn btn-primary">Export</button>
                    </div>
                </div>
                <div ng-if="detailedDataUsages.length > 0">
                    <p ng-show="detailedDataUsages.length > 0">
                        Total: <b>{{detailedSum|limitTo:10}} MB</b>
                    </p>
                    <div class="custom_scroll" style="margin-top: 0">
                        <table class="table table-responsive" id="detailed-data-usage-list">
                            <thead>
                                <tr>
                                    <th>Nasid</th>
                                    <th>Mobile</th>
                                    <th>Device MAC</th>
                                    <th>Login Time</th>
                                    <th>Logout Time</th>
                                    <th>Data Upload</th>
                                    <th>Data Download</th>
                                    <th>Location Name</th>
                                    <th>City</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr ng-repeat="used in detailedDataUsages">
                                    <td>{{used.nasid}}</td>
                                    <td>{{used.mobile}}</td>
                                    <td>{{used.macId}}</td>
                                    <td>{{used.sessionStart}}</td>
                                    <td>{{used.sessionEnd}}</td>
                                    <td>{{convertToMB(used.dataUpload)}} MB</td>
                                    <td>{{convertToMB(used.dataDownload)}} MB</td>
                                    <td>{{used.locationName}}</td>
                                    <td>{{used.city}}</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
                <p ng-show="detailedDataUsages.length == 0" class="no_data_used_p">No data present, please choose wider date range</p>
            </div>
        </div>


        <div ng-if="router.selected.length == 1 && dataUsages && featureEnabled(14) == 1" class="container-fluid config-container">
            <div class="col-md-12 config-row">
                <h5><i class="fa fa-key" aria-hidden="true"></i> Data Usage</h5>
                <div class="row date-range-picker">
                    <div class="date-range-btn" title="Change date ranges">
                        <button id="date-range-picker-for-data-usage" class="btn btn-primary btn-lg" ng-click="showDatePicker()">Select Range</button>
                    </div>
                    <div class="from-to-dates">
                        <div class="from" title="{{dates.dataUsage.completeFromDate}}">
                            <span>From:</span>
                            <div class="date">
                                {{dates.dataUsage.fromShowDate}}
                            </div>
                        </div>
                        <div class="to" title="{{dates.dataUsage.completeToDate}}">
                            <span>To:</span>
                            <div class="date">
                                {{dates.dataUsage.toShowDate}}
                            </div>
                        </div>
                    </div>
                    <div ng-show="dataUsages.length > 0" class="about-period single-location">
                        <button ng-click="exportDataUsage($event)" class="btn btn-primary">Export</button>
                    </div>
                </div>
                <div ng-if="dataUsages.length > 0">
                    <p ng-show="dataUsages.length > 0">
                        Total: <b>{{dataSum}} MB</b>
                    </p>
                    <div class="display-none mobile-detailed-report" id="mobile-detailed-report">
                        <div class="custom_scroll">
                            <span class="close glyphicon glyphicon-remove" ng-click="removeMobileDetailedReport()"></span>
                            <table class="table table-responsive">
                                <thead>
                                <th>Device Mac</th>
                                <th>Session Start</th>
                                <th>Session End</th>
                                <th>Data Upload</th>
                                <th>Data Download</th>
                                </thead>
                                <tbody>
                                    <tr ng-repeat="used in detailedMobileDataUsages">
                                        <td ng-bind="used.macId"></td>
                                        <td ng-bind="used.sessionStart"></td>
                                        <td ng-bind="used.sessionEnd"></td>
                                        <td>{{convertToMB(used.dataUpload)}} MB</td>
                                        <td>{{convertToMB(used.dataDownload)}} MB</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>

                    </div>

                    <div class="custom_scroll" style="margin-top: 0">
                        <table class="table table-responsive" id="usage-list">
                            <thead>
                                <tr>
                                    <th></th>
                                    <th>Mobile</th>
                                    <th>email</th>
                                    <th>Data Used</th>
                                    <th></th>
                                    <th style="text-align: right;padding-right: 1rem;">Operations</th>
                                </tr>
                            </thead>
                            <tbody infinite-scroll='loadMore()' infinite-scroll-container='"#admin_container"' infinite-scroll-disabled='infiniteScrollDisabled'>
                                <tr ng-repeat="dataUsage in dataUsages | orderBy: 'online' : 'reverse'" class="data-usage-row">
                                    <td><div class="circle_{{dataUsage.online ? 'success' : 'danger'}}"></div></td>
                                    <td>{{dataUsage.mobile}}</td>
                                    <td>{{dataUsage.email}}</td>
                                    <td><b>{{convertToMB(dataUsage.dataUpload + dataUsage.dataDownload)}} MB</b></tdng-init>
                                    <td ng-click="fetchDataUsageReportForUser(dataUsage)" style="text-decoration:underline; cursor: pointer">Fetch Details</td>
                                    <td style="text-align: right;padding-right: 1rem;">
                                        <button type="button" ng-if="dataUsage.blocked" class="btn btn-success" data-toggle="tooltip" ng-click="unblockNumber(dataUsage.mkey)" data-placement="top">Unblock</button>
                                        <button type="button" ng-if="!dataUsage.blocked" class="btn btn-danger" data-toggle="tooltip" ng-click="blockNumber(dataUsage.mkey)" data-placement="top">Block</button>
                                        <button type="button" ng-if="dataUsage.online" class="btn btn-danger" data-toggle="tooltip" ng-click="logoutUser(dataUsage)" data-placement="top">Logout</button>
                                    </td>
                                </tr>
                                <tr ng-show='infiniteScrollDisabled'><td colspan="8">Loading data...</td></tr>
                            </tbody>
                        </table>
                    </div>
                </div>
                <p ng-show="dataUsages.length == 0" class="no_data_used_p">No data present, please choose wider date range</p>
            </div>
        </div>

        <!-- Depricated -->
        <!-- 
            <div ng-if="router.selected.length == 1 && featureEnabled(27) == 1" class="container-fluid">
            <div class="col-md-12 config-row">
                <h5><i class="fa fa-key" aria-hidden="true"></i> DNS Reports</h5>
                <div class="row date-range-picker">
                    <div class="date-range-btn" title="Change date ranges">
                        <button id="date-range-picker-for-dns-report" class="btn btn-primary btn-lg" ng-click="showDatePicker()">Select Range</button>
                    </div>
                    <div class="from-to-dates">
                        <div class="from" title="{{dnsReportdata.dates.completeFromDate}}">
                            <span>From:</span>
                            <div class="date">
                                {{dnsReportdata.dates.fromShowDate}}
                            </div>
                        </div>
                        <div class="to" title="{{dnsReportdata.dates.completeToDate}}">
                            <span>To:</span>
                            <div class="date">
                                {{dnsReportdata.dates.toShowDate}}
                            </div>
                        </div>
                    </div>
                    <div ng-show="dnsReportdata.dnsReports.length > 0" class="about-period single-location">
                        <button ng-click="exportDNSReport($event)" class="btn btn-primary">Export</button>
                    </div>
                </div>
                <div ng-if="dnsReportdata.dnsReports.length > 0">
                    <div class="dns-report">
                        <div class="custom_scroll">
                            <table class="table table-responsive">
                                <thead>
                                <th>Username</th>
                                <th>Domain</th>
                                <th>Time</th>
                                </thead>
                                <tbody>
                                    <tr ng-repeat="report in dnsReportdata.dnsReports">
                                        <td ng-bind="report.username"></td>
                                        <td ng-bind="report.domain"></td>
                                        <td ng-bind="moment.utc(report.time).local().format(Constants.competeDateFormat)"></td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>

                    </div>
                </div>

                <p ng-show="dnsReportdata.dnsReports.length == 0" class="no_data_used_p">No data present, please choose wider date range</p>
            </div>
        </div>
        -->
    </div>
</div>

