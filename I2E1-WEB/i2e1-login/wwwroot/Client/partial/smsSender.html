<div class="sms-container" feature-toggle="7">
    <div class="btn-group pull-right">
        <a class="btn btn-primary" href="#/landing/smsCampaignReports" style="position: absolute;right: 0;top: -5rem;">Go to reports</a>
    </div>
    <div class="container-fluid no-pad">
        <div class="col-sm-offset-8 col-sm-4 no-pad">
            <div class="btn-group pull-right box-radio-buttons source-selector">
                <label class="btn" ng-model="data.sms.dataBase" ng-change="setMessageDefaultParameters(10)" btn-radio="'account'">Engage </label>
                <!--<label class="btn" ng-model="data.sms.dataBase" ng-change="setMessageDefaultParameters()" btn-radio="'i2e1'">Acquire</label>-->
                <label class="btn {{(isConfigAdminUser || username == 'admin.glynk' ) ? '' : 'disabled'}}" ng-model="data.sms.dataBase" ng-change="setMessageDefaultParameters(11)" btn-radio="'i2e1'">Acquire</label>
                <label class="btn" ng-model="data.sms.dataBase" ng-change="setMessageDefaultParameters(10)" btn-radio="'upload'">Upload</label>
            </div>
        </div>
    </div>

    <div class="panel panel-default" ng-init="data">
        <div class="panel-body no-top-pad">
            <form class="form-horizontal schedule-campaign-form" name="creatSMSForm">
                <div class="row">
                    <div class="container-fluid" style="position:relative;display: flex;align-items: flex-end;">
                        <div class="col-sm-8 no-pad" style="margin-bottom: 3rem;">

                            <div class="row no-margin">
                                <h6 class="section-head">Choose template</h6>
                                <div class="schedule-tpl-container" ng-repeat="(tabKey, tabData) in smsTemplates">
                                    <label>{{tabKey}}</label>
                                    <div class="col-sm-12 no-pad" style="box-shadow: inset -7px 0 9px -7px rgba(0,0,0,0.4);">
                                        <div class="horizontal-scroll">
                                            <div class="col-sm-4 tpl-content no-pad" ng-repeat="content in tabData">
                                                <div class="card"
                                                     tooltip-append-to-body="true"
                                                     tooltip="{{content}}"
                                                     tooltip-class="custome-tooltip-class"
                                                     ng-click="data.setMessageContent(content)">
                                                    <div class="card-body">
                                                        <p>{{content}}</p>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>


                            <div class="row no-margin">
                                <h6 class="section-head">Set sms</h6>
                                <div class="message-box-container" style="position: relative;">
                                    <div class="message_sender_details">
                                        <div class="col-sm-3 form-group no-pad">
                                            <select class="form-control" ng-model="data.sms.senderId" name="senderId">
                                                <option value="" disabled selected>Select a sender ID</option>
                                                <option value="BULKSMS">Promotional sms</option>
                                                <option ng-if="senderId != 'BULKSMS'" ng-repeat="senderId in data.staticDetails.senderIds track by $index" value="{{senderId}}">{{senderId}}</option>
                                            </select>
                                        </div>
                                        <div class="col-sm-6">
                                            <span ng-if="data.sms.senderId == 'BULKSMS'" style="font-size: 8px;">
                                                Character length of 160 will be counted as 1.
                                            </span>
                                            <span ng-if="data.sms.senderId != 'BULKSMS'" style="font-size: 8px;">
                                                Character length of {{160-data.trackingCode.length}} plus {{data.trackingCode.length}} character for tracking will be counted as 1.
                                            </span>
                                        </div>
                                        <div class="right-align" style="padding-right: 1.5rem;">
                                            Size-{{data.sms.chrLength}}&nbsp;&nbsp;&nbsp;Messages-{{data.sms.unit}}
                                        </div>
                                    </div>

                                    <div class="form-group">
                                        <textarea id="message-text-area" rows="5" name="messageText" required
                                                  class="form-control message-text-area message-text"
                                                  placeholder="Enter message here"
                                                  ng-change="data.lengthCheck()"
                                                  ng-model="data.sms.message"></textarea>
                                    </div>
                                    <div class="message_tracking_details">
                                        <div class="col-sm-3 form-group">

                                            <input type="checkbox" name="isTrackingEnabled" 
                                                   ng-model="data.sms.isTrackingEnabled" ng-disabled="data.sms.senderId=='BULKSMS'"
                                                   ng-change="data.lengthCheck();!data.sms.isTrackingEnabled?data.sms.trackingUrl='':data.sms.trackingUrl='http://interaction.i2e1.com/Home/sms/{0}'"  />
                                            Enable Tracking

                                        </div>
                                        <div class="col-sm-5 no-pad" style="line-height: 8px;">
                                            <span ng-if="data.sms.isTrackingEnabled" style="font-size: 8px;">
                                                {{data.trackingCode.length}} characters will be added extra in your message content for tracking purpose.
                                            </span>
                                        </div>
                                        <div class="col-sm-4 form-group no-pad" ng-class="{'has-error': data.sms.isTrackingEnabled && creatSMSForm.trackingUrl.$dirty && creatSMSForm.trackingUrl.$error.url}">
                                            <input type="text" name="trackingUrl" placeholder="Enter tracking link" ng-model="data.sms.trackingUrl"
                                                    ng-disabled="!data.sms.isTrackingEnabled" validate-url />
                                        </div>
                                    </div>
                                </div>
                            </div>

                        </div>
                        <div class="col-sm-4">
                            <div class="sms_preview_container pull-right">
                                <img class="sms_preview_screen" alt="message screen" src="../../images/android_message_screen.svg" />
                                <span class="sms_sender_id" ng-bind-html="data.sms.senderId"></span>
                                <div class="sms_preview_content" ng-if="data.sms.message.length > 0">
                                    <div class="sms_item">
                                        <div class="clearfix">
                                            <div class="user_icon pull-left">
                                                <span class="glyphicon glyphicon-user"></span>
                                            </div>
                                            <div class="sms_content pull-right">
                                                {{data.sms.message}}
                                                <span ng-if="data.sms.isTrackingEnabled">{{data.trackingCode}}</span>
                                            </div>
                                        </div>
                                        <div class="sms_recieved_time">
                                            {{data.creat_date.formated_time}}
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                </div>
                <div class="row">
                    <div class="container-fluid">
                        <div class="col-md-6 no-pad">
                            <div class="text-danger" ng-if="creatSMSForm.messageText.$error.error">
                                {{creatSMSForm.messageText.$error.error}}
                            </div>
                            <div class="text-danger" ng-if="creatSMSForm.senderId.$error.error">
                                {{creatSMSForm.senderId.$error.error}}
                            </div>
                            <div class="text-danger"
                                 ng-if="data.sms.isTrackingEnabled && creatSMSForm.trackingUrl.$error.url && creatSMSForm.trackingUrl.$dirty">
                                Please enter a valid url
                            </div>
                            <div class="text-danger" ng-if="creatSMSForm.isTrackingEnabled.$error.error">
                                {{creatSMSForm.isTrackingEnabled.$error.error}}
                            </div>
                            <div class="text-danger" ng-if="creatSMSForm.trackingUrl.$error.error">
                                {{creatSMSForm.trackingUrl.$error.error}}
                            </div>
                        </div>
                        <div class="col-sm-6">
                            <div class="btn-group pull-right box-radio-buttons single-radio-buttons" style="z-index: 999;">
                                <label ng-show="data.sms.message.length > 0 && data.sms.dataBase != 'i2e1'" class="btn {{data.sms.message.length > 0 && data.sms.dataBase != 'i2e1' ? '' : 'disabled'}}"
                                       ng-model="data.sms.campaignType"
                                       btn-radio="10"
                                       href="#targetFilters"
                                       aria-expanded="false"
                                       aria-controls="targetFilters" ng-change="setMessageDefaultParameters(10)">Proceed</label>
                                <label ng-show="data.sms.message.length > 0 && data.sms.dataBase == 'i2e1'" class="btn  {{data.sms.message.length > 0 && data.sms.dataBase == 'i2e1' ? '' : 'disabled'}}"
                                       ng-model="data.sms.campaignType"
                                       btn-radio="11"
                                       href="#targetFilters"
                                       aria-expanded="false"
                                       aria-controls="targetFilters" ng-change="setMessageDefaultParameters(11)">Proceed</label>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="row target-filters" id="targetFilters" ng-show="data.sms.campaignType" is-open="data.sms.campaignType">
                    <div class="container-fluid" ng-if="data.sms.dataBase != 'upload'">
                        <div class="control-label col-sm-2">
                            Cities
                            <span class="glyphicon glyphicon-question-sign"
                                  tooltip-append-to-body="true"
                                  tooltip="This is for filtering your stores by city"
                                  tooltip-class="custome-tooltip-class"></span>
                        </div>
                        <div class="col-sm-10 locations-selector">
                            <div class="ui-select-input-group">
                                <div class="cell location">
                                    <ui-select id="city_filter"
                                               name="city_filter"
                                               multiple ng-model="data.sms.cities"
                                               ui-select-all-and-remove-all
                                               theme="select2"
                                               close-on-select="false"
                                               reset-search-input="true"
                                               remove-selected="false"
                                               ng-change="getLocationsForCity()">
                                        <ui-select-match placeholder="Select City">
                                            {{$item.city}}
                                        </ui-select-match>

                                        <ui-select-choices repeat="elem.city as elem in availableCities | propsFilter: {city: $select.search}">
                                            <label>
                                                <input type="checkbox" ng-checked="$select.isDisabled(this)" ng-click="$select.isDisabled(this) && $selectMultiple.removeChoice($select.selected.indexOf(this[$select.itemProperty])) && $event.stopPropagation()" />
                                                <span ng-bind-html="elem.city | highlight: $select.search"></span>
                                            </label>
                                        </ui-select-choices>
                                    </ui-select>
                                </div>
                            </div>
                            <div class="text-danger" ng-if="creatSMSForm.city_filter.$error.error">
                                {{creatSMSForm.city_filter.$error.error}}
                            </div>
                        </div>
                    </div>


                    <div class="container-fluid" ng-if="data.sms.campaignType=='10' &&  data.sms.dataBase == 'account'">
                        <div class="control-label col-sm-2">
                            Locations
                            <span class="glyphicon glyphicon-question-sign"
                                  tooltip-append-to-body="true"
                                  tooltip="This is for filtering your stores situated across different city"
                                  tooltip-class="custome-tooltip-class"></span>
                        </div>
                        <div class="col-sm-10 locations-selector">
                            <div class="ui-select-input-group">
                                <div class="cell location">
                                    <ui-select id="store_filter" name="store_filter" ng-disabled="!data.sms.cities.length"
                                               multiple ng-model="data.sms.locations"
                                               ui-select-all-and-remove-all
                                               theme="select2"
                                               close-on-select="false"
                                               reset-search-input="true"
                                               remove-selected="false"
                                               ng-change="data.loadEstimates(data.sms)">
                                        <ui-select-match allow-clear="true" placeholder="Select Store">
                                            {{$item.storeName}}
                                        </ui-select-match>
                                        <ui-select-choices repeat="router.nasid as router in $parent.positiveNases | propsFilter: {nasid: $select.search, storeName: $select.search}">
                                            <label>
                                                <!--<input type="checkbox" ng-checked="$select.isDisabled(this)" ng-click="$select.isDisabled(this) && $selectMultiple.removeChoice($select.selected.indexOf(this[$select.itemProperty])) && $event.stopPropagation()" />-->
                                                <span ng-bind-html="router.storeName | highlight: $select.search" title="{{router.nasid}}"></span>
                                                <small>{{router.city}}, {{router.state}}</small>
                                            </label>
                                        </ui-select-choices>
                                    </ui-select>
                                </div>
                            </div>
                            <div class="text-danger" ng-if="creatSMSForm.store_filter.$error.error">
                                {{creatSMSForm.store_filter.$error.error}}
                            </div>
                        </div>
                    </div>

                    <div class="container-fluid" ng-if="1==0">
                        <div class="control-label col-sm-2">
                            New
                            <span class="glyphicon glyphicon-question-sign"
                                  tooltip-append-to-body="true"
                                  tooltip="Select yes, if you want to target your new customers"
                                  tooltip-class="custome-tooltip-class"></span>
                        </div>
                        <div class="col-sm-10">
                            <div class="btn-group pull-left box-radio-buttons">
                                <label class="btn" ng-model="data.sms.analytics.isNew" btn-radio="true">Yes</label>
                                <label class="btn" ng-model="data.sms.analytics.isNew" btn-radio="false">No</label>
                            </div>
                        </div>
                    </div>

                    <div class="container-fluid" ng-if="data.sms.campaignType=='10' && data.sms.dataBase != 'upload'">
                        <div class="col-sm-offset-2 col-sm-10">
                            <div class="btn-group pull-left box-radio-buttons">
                                <label class="btn" ng-model="data.sms.notVisitedSince" btn-radio="false" ng-click="setCohortData(false)">Visited</label>
                                <label class="btn" ng-model="data.sms.notVisitedSince" btn-radio="true" ng-click="setCohortData(true)">Not Visted</label>
                            </div>

                            <div class="col-sm-1 text-center" style="padding: 8px 15px;">Since</div>
                            <div class="col-sm-3">
                                <input name="dateRangePicker1" date-range-picker id="dateRangePicker1" class="custom-input date-picker" type="text"
                                       ng-model="data.cohort_date" options="dateRangePickerOpt1" />
                            </div>
                        </div>
                    </div>
                    <div class="container-fluid" ng-if="data.sms.dataBase != 'upload'">
                        <div class="control-label col-sm-2">
                            Gender
                            <span class="glyphicon glyphicon-question-sign"
                                  tooltip-append-to-body="true"
                                  tooltip="Select specific gender to filter out the users you want to target"
                                  tooltip-class="custome-tooltip-class"></span>
                        </div>
                        <div class="col-sm-10">
                            <div class="btn-group pull-left box-radio-buttons checkbox-list">
                                <label class="btn" ng-repeat="gender in genders" for="{{gender.key}}">
                                    <input type="checkbox" id="{{gender.key}}" checklist-model="data.sms.analytics.gender" checklist-value="gender.value">{{gender.key}}
                                </label>
                            </div>
                        </div>
                    </div>
                    <div class="container-fluid" ng-if="data.sms.dataBase != 'upload'">
                        <div class="control-label col-sm-2">
                            Affluence
                            <span class="glyphicon glyphicon-question-sign"
                                  tooltip-append-to-body="true"
                                  tooltip="This is to filter users basis their spend capacity"
                                  tooltip-class="custome-tooltip-class"></span>
                        </div>
                        <div class="col-sm-10">
                            <div class="btn-group pull-left box-radio-buttons checkbox-list">
                                <label class="btn" for="{{affluence.key}}" ng-repeat="affluence in affluences">
                                    <input type="checkbox" id="{{affluence.key}}" checklist-model="data.sms.analytics.affluence" checklist-value="affluence.value">{{affluence.key}}
                                </label>
                            </div>
                        </div>
                    </div>
                    <div class="container-fluid" ng-if="data.sms.dataBase=='upload'">

                        <div class="control-label col-sm-2">
                            CSV File
                            <span class="glyphicon glyphicon-question-sign"
                                  tooltip-append-to-body="true"
                                  tooltip-html-unsafe='<h6 style="color:#fff;">Please use csv as in below format only.</h6><img width="280" height="173" src="/images/upload_mobile_numbers.png">'
                                  tooltip-class="custome-tooltip-class"></span>
                        </div>
                        <div class="col-sm-5">
                            <csv-file-upload result="data.sms.numbers" separator="','" header="false" mobile="true"></csv-file-upload>
                            <div class="text-danger" ng-if="creatSMSForm.csvFileName.$error.error">
                                {{creatSMSForm.csvFileName.$error.error}}
                            </div>
                            <a title="smaple_mobile_numbers.csv" href="/Client/download/smaple_mobile_numbers.csv">Download Sample</a>
                        </div>
                    </div>
                    <!--<div class="container-fluid" ng-if="data.sms.campaignType=='10'">
                        <div class="control-label col-sm-2">
                            Send at
                            <span class="glyphicon glyphicon-question-sign"
                                  tooltip-append-to-body="true"
                                  tooltip="Select the time of the day you want your message to be triggered"
                                  tooltip-class="custome-tooltip-class"></span>
                        </div>
                        <div class="col-sm-10">
                            <input name="sendNowDate" class="custom-input date-picker" date-range-picker id="dateRangePicker2" name="dateRangePicker2" type="text"
                                   ng-model="data.sms.send_on" options="dateRangePickerOpt2" required />
                            <div class="text-danger" ng-if="creatSMSForm.sendNowDate.$error.error">
                                {{creatSMSForm.sendNowDate.$error.error}}
                            </div>
                        </div>
                    </div>-->

                    <div class="container-fluid" ng-if="data.sms.campaignType=='11'">
                        <div class="control-label col-sm-2">
                            Send at
                            <span class="glyphicon glyphicon-question-sign"
                                  tooltip-append-to-body="true"
                                  tooltip="Select the time of the day you want your message to be triggered"
                                  tooltip-class="custome-tooltip-class"></span>
                        </div>
                        <div class="col-sm-10">
                            <input class="custom-input" type="time" id="dateTimePicker" name="runningTime" ng-model="data.sms.schedule.runningTime">
                            <div class="text-danger" ng-if="creatSMSForm.runningTime.$error.error">
                                {{creatSMSForm.runningTime.$error.error}}
                            </div>
                        </div>
                    </div>
                    <div class="container-fluid" ng-if="data.sms.campaignType=='11' && data.sms.dataBase != 'upload'">
                        <div class="control-label col-sm-2">
                            Repeat
                            <span class="glyphicon glyphicon-question-sign"
                                  tooltip-append-to-body="true"
                                  tooltip="Select the day of week you want your message to be triggered"
                                  tooltip-class="custome-tooltip-class"></span>
                        </div>
                        <div class="col-sm-10">
                            <div id="repeat-day-selector" class="btn-group pull-left box-radio-buttons">
                                <label class="btn" ng-model="data.sms.schedule.runningDays.monday" name="runningDays" btn-checkbox>Mon</label>
                                <label class="btn" ng-model="data.sms.schedule.runningDays.tuesday" btn-checkbox>Tue</label>
                                <label class="btn" ng-model="data.sms.schedule.runningDays.wednesday" btn-checkbox>Wed</label>
                                <label class="btn" ng-model="data.sms.schedule.runningDays.thursday" btn-checkbox>Thu</label>
                                <label class="btn" ng-model="data.sms.schedule.runningDays.friday" btn-checkbox>Fri</label>
                                <label class="btn" ng-model="data.sms.schedule.runningDays.saturday" btn-checkbox>Sat</label>
                                <label class="btn" ng-model="data.sms.schedule.runningDays.sunday" btn-checkbox>Sun</label>
                            </div>
                            <div class="text-danger" style="width:fit-content;" ng-if="creatSMSForm.runningDays.$error.error">
                                {{creatSMSForm.runningDays.$error.error}}
                            </div>
                        </div>
                    </div>
                    <div class="container-fluid" ng-if="data.sms.campaignType=='11' && data.sms.dataBase != 'upload'">
                        <div class="control-label col-sm-2">
                            Run this campaign till
                            <span class="glyphicon glyphicon-question-sign"
                                  tooltip-append-to-body="true"
                                  tooltip="Select the vilidity of your campaign, after your validity this campaign will stop automatically"
                                  tooltip-class="custome-tooltip-class"></span>
                        </div>

                        <div class="col-sm-10">
                            <input class="custom-input date-picker" date-range-picker id="dateRangePicker4" name="dateRangePicker4" type="text"
                                   ng-model="data.sms.schedule.runTill" options="dateRangePickerOpt3" required />

                        </div>
                    </div>

                    <div class="container-fluid" ng-if="data.sms.campaignType=='11' && data.sms.dataBase != 'upload'">
                        <div class="control-label col-sm-2">
                            Budget/Day
                            <span class="glyphicon glyphicon-question-sign"
                                  tooltip-append-to-body="true"
                                  tooltip="Maximum alloted Budget per day to be consumed by this campaign"
                                  tooltip-class="custome-tooltip-class"></span>
                        </div>

                        <div class="col-sm-10">

                            <div class="input-group">
                                <span class="input-group-addon">₹</span>
                                <input class="custom-input" id="dailyBudget" name="dailyBudget"
                                       ng-model="data.sms.dailyBudget" required type=text pattern='[0-9]{1,5}' />
                            </div>
                            <div class="text-danger" ng-if="creatSMSForm.dailyBudget.$error.error">
                                {{creatSMSForm.dailyBudget.$error.error}}
                            </div>
                        </div>
                    </div>
                    <div class="container-fluid">
                        <div class="col-md-12" id="estimation"></div>
                    </div>
                    <div class="container-fluid">
                        <div class="col-sm-offset-6 col-sm-6 pull-right">
                            <div class="pull-right">
                                <!--
                            <button class="btn btn-primary"
                                ng-click="getSMSEstimates(data.sms)"
                                ng-disabled="!data.sms.cities.length || (data.sms.campaignType == '10' && data.sms.dataBase == 'upload')
                                || (data.sms.campaignType == '11' && data.sms.dataBase != 'upload')">
                            Check Esmtimated cost
                        </button>
                         -->
                                <button class="btn btn-primary"
                                        ng-click="getSMSEstimates(data.sms)"
                                        ng-disabled="(data.sms.campaignType == '10' && data.sms.dataBase == 'account' && !data.sms.cities.length) || (data.sms.campaignType == '10' && data.sms.dataBase == 'upload' && data.sms.numbers.length == 0)">
                                    Check Esmtimated cost
                                </button>
                                <button class="btn btn-primary-green" ng-click="saveSMS(data.sms,   creatSMSForm)">{{data.sms.campaignType=='10' ? 'Send' : 'Schedule'}}</button>
                            </div>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
    $(document).on('click', ".checkbox-list label.btn", function () {
        if ($(this).find('input[type="checkbox"]').is(":checked"))
            $(this).addClass("active")
        else
            $(this).removeClass('active');
    });
</script>

<script type="text/ng-template" id="/reviewModalForm.html">
    <form name="reviewModalForm">
        <div class="modal-header bootstrap-dialog-header alert-warning">
            <button class="close" ng-click="cancel()">×</button>
            <h4>Please review</h4>
        </div>
        <div class="modal-body review-modal">
            <div class="bootstrap-dialog-body">

                <div class="container-fluid" ng-if="data.sms.campaignType=='10'">
                    <div class="control-label col-sm-4">
                        Send at
                        <span class="glyphicon glyphicon-question-sign"
                              tooltip-append-to-body="true"
                              tooltip="Select the time of the day you want your message to be triggered"
                              tooltip-class="custome-tooltip-class"></span>
                    </div>
                    <div class="col-sm-8">
                        <input class="custom-input date-picker" date-range-picker
                               id="modelSendNowDate" name="modelSendNowDate" type="text"
                               ng-model="data.sms.send_on" options="data.modelSendNowDate" required />
                        <div class="text-danger" ng-if="reviewModalForm.modelSendNowDate.$error.error">
                            {{reviewModalForm.modelSendNowDate.$error.error}}
                        </div>
                    </div>
                </div>
                <div class="container-fluid">
                    <div class="control-label col-sm-4">
                        Campaign Name
                        <span class="glyphicon glyphicon-question-sign"
                              tooltip-append-to-body="true"
                              tooltip="Please enter a name for you campaign"
                              tooltip-class="custome-tooltip-class"></span>
                    </div>
                    <div class="col-sm-8">
                        <input class="custom-input"
                               id="campaignName" name="campaignName" type="text"
                               ng-model="data.sms.campaignName" required />
                        <div class="text-danger" ng-if="reviewModalForm.campaignName.$error.error">
                            {{reviewModalForm.campaignName.$error.error}}
                        </div>
                    </div>
                </div>

            </div>
        </div>
        <div class="modal-footer">
            <div class="bootstrap-dialog-footer">
                <button name="send" ng-click="saveReviewForm(data.sms,reviewModalForm)" class="btn btn-primary">Send</button>
                <button name="cancel" ng-click="cancel()" class="btn btn-warning">Cancel</button>
            </div>
        </div>
    </form>
</script>
