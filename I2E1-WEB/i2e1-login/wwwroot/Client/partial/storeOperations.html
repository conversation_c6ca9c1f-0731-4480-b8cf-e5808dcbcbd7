<div class="select-filters top-filter">
    <div class="row">
        <div class="col-md-12 selects input-group">
                <i class="glyphicon glyphicon-search"></i>
            <input type="text" class="form-control" placeholder="Search Locations" ng-model="searchTerm"
                    ng-change="filterRouters({scrollTo: 'router_details'})" ng-model-options="{debounce:1000}" style="padding-left: 30px;font-weight: 800;">
        </div>
    </div>
    <div class="row">
        <div id="nas-dropdown" class="col-md-12 selects input-group">
            <i class="glyphicon glyphicon-filter"></i>
            <selectize config="singleLocationSelectorConfig"
                       options='storeList'
                       ng-model="location"
                       ng-change="getWhiteAndBlckListsForStore()"
                       class="multiple-location select-loaction"
                       title="Select multiple locations, type to search"></selectize>
        </div>
    </div>
</div>
<div class="store-operations-container">
    <div class="row">
        <div class="page-header">
            <h4>
                Store Operations
                <div class="add-new-group pull-right">
                    <a class="btn btn-primary" href="https://www.i2e1.in/Passwordgenerator" target="_blank">Go to fdm portal</a>
                </div>
            </h4>
        </div>

        <!-- Username and paswword -->
        <div ng-if="router.selected.length == 1 && (isConfigAdminUser || username=='<EMAIL>')" class="container-fluid config-container">
            <div class="col-md-12 config-row">
                <h5><i class="fa fa-key" aria-hidden="true"></i> Authentication Mode Helper</h5>
                <div class="col-md-12 no-pad admin-ops-selection">
                    <div class="col-md-4">
                        <label>
                            Username&nbsp;&nbsp;<span class="glyphicon glyphicon-question-sign info-question-sign"
                                                      tooltip="Username for Registration portal"
                                                      tooltip-trigger
                                                      tooltip-animation="false"
                                                      tooltip-placement="top"></span>
                        </label>
                        <input type="text" class="form-control" placeholder="Username" ng-model="data.authentiCationHelper[1]" />
                    </div>
                    <div class="col-md-4">
                        <label>
                            Password&nbsp;&nbsp;<span class="glyphicon glyphicon-question-sign info-question-sign"
                                                      tooltip="Password for Registration portal"
                                                      tooltip-trigger
                                                      tooltip-animation="false"
                                                      tooltip-placement="top"></span>
                        </label>
                        <input type="text" class="form-control" placeholder="*******" ng-model="data.authentiCationHelper[2]" />
                    </div>
                    <div class="col-md-12" style="margin: 3rem 0 2rem 0;">
                        <input type="checkbox" ng-model="data.authentiCationHelper[3]" ng-checked="data.authentiCationHelper[3] === 'True'" ng-true-value="'True'" ng-false-value="'False'" />
                        Ask Room Number
                        <input style="margin-left:1.5rem;" type="checkbox" ng-model="data.authentiCationHelper[4]" ng-checked="data.authentiCationHelper[4] === 'True'" ng-true-value="'True'" ng-false-value="'False'" />
                        Allow Data plan change
                        <input style="margin-left:1.5rem;" type="checkbox" ng-model="data.authentiCationHelper[5]" ng-checked="data.authentiCationHelper[5] === 'True'" ng-true-value="'True'" ng-false-value="'False'" />
                        Ask Name
                        <input style="margin-left:1.5rem;" type="checkbox" ng-model="data.authentiCationHelper[6]" ng-checked="data.authentiCationHelper[6] === 'True'" ng-true-value="'True'" ng-false-value="'False'" />
                        Ask Email
                        <input style="margin-left:1.5rem;" type="checkbox" ng-model="data.authentiCationHelper[8]" ng-checked="data.authentiCationHelper[8] === 'True'" ng-true-value="'True'" ng-false-value="'False'" />
                        Ask Access Code
                    </div>
                </div>
            </div>

            <div class="col-md-12 btn-container">
                <button class="btn btn-primary" ng-click="saveAuthenticationModeHelper()">Save</button>
            </div>
        </div>

        <div ng-if="router.selected.length == 1 && featureEnabled(52) == 1" class="container-fluid">
            <div class="col-md-12 config-row">
                <h5><i class="fa fa-key" aria-hidden="true"></i> Admin Operations<span class="pull-right glyphicon glyphicon-refresh" ng-click="fetchHistory()"></span></h5>

                <div class="col-md-12 no-pad admin-ops-selection">
                    <div class="col-md-2 no-pad">
                        <select class="form-control" ng-model="operation.operationType" ng-change="changeme(operation.operationType);showCustom($event)">
                            <option selected value="">--Operations--</option>
                            <option ng-repeat="op in operations" value="{{op.key}}">{{op.value}}</option>
                        </select>
                    </div>
                    <div class="col-md-2 col-md-offset-1 no-pad pull-right">
                        <button id="submit" class="btn btn-primary pull-right" ng-click="submitOp()">Submit</button>
                    </div>

                </div>

                <div ng-show="secret.showall" class="col-md-12">
                    <div class="row col-md-12">
                        <div class="col-md-3">Controller</div>
                        <div class="col-md-4"><input type="text" class="form-control" placeholder="1" ng-model="operation.controllerId" /></div>
                    </div>
                    <div class="row col-md-12" ng-show="operation.operationType == 20">
                        <div class="col-md-3">Brand Name</div>
                        <div class="col-md-4">
                            <input type="text" class="form-control" placeholder="Not Set" ng-model="operation.operationParameter" />(13 characters max,Remaining:{{13-operation.operationParameter.length}})
                            (Free_WiFi_{{operation.operationParameter}}_i2e1) {{operation.operationType == 20&&operation.operationParameter.length>13?operation.operationParameter=operation.operationParameter.slice(0,13):""}}

                        </div>
                    </div>

                    <div class="row col-md-12" ng-show="operation.operationType == 2">
                        <div class="col-md-3">SSID</div>
                        <div class="col-md-4">
                            <input type="text" class="form-control" placeholder="Not Set" ng-model="operation.operationParameter" />(28 characters max, Remaining:{{28-operation.operationParameter.length}})
                            {{operation.operationType == 2&&operation.operationParameter.length>28?operation.operationParameter=operation.operationParameter.slice(0,28):""}}

                        </div>
                        <div ng-if="isConfigAdminUser">
                            <div class="col-md-3">List of nasids comma seperated</div>
                            <div class="col-md-4">
                                <input type="text" class="form-control" placeholder="Not Set" ng-model="operation.operationNasids" />
                                <span class="info-span">Enter multiple nasid comma seperated</span>
                            </div>
                        </div>
                    </div>

                    <div class="row col-md-12" ng-show="operation.operationType == 30">
                        <div class="col-md-3">ipsets</div>
                        <div class="col-md-3">
                            <div class="col-md-2 no-pad admin-ops-selection">
                                <select class="form-control" ng-model="operation.operationParameter">
                                    <option selected value="">ipsetlist</option>
                                    <option ng-repeat="ipset in ipsets" value="{{ipset.id}}:{{ipset.ipsetname}}:{{ipset.domains}}">{{ipset.ipsetname}}</option>
                                </select>
                            </div>
                            <span class="info-span">{{"      "+operation.operationParameter.split(':')[2]}}</span>
                        </div>
                    </div>

                    <div class="row col-md-12" ng-show="operation.operationType == 3 || operation.operationType == 14">
                        <div class="col-md-3">Parameters</div>
                        <div class="col-md-4">
                            <input type="text" class="form-control" placeholder="Not Set" ng-model="operation.operationParameter" />
                            <span ng-show="operation.operationType == 3" class="info-span">Password must be between 8-20 characters</span>
                        </div>
                    </div>

                    <div class="row col-md-12" ng-show="operation.operationType == 9 || operation.operationType == 25">
                        <div class="col-md-3">Parameter</div>
                        <div class="col-md-4">
                            <textarea class="form-control" placeholder="Not Set" ng-model="operation.operationParameter"></textarea>
                        </div>
                    </div>
                    <div class="row col-md-12" ng-show="operation.operationType == 21">
                        <div class="col-md-3">IP List (comma separated)</div>
                        <div class="col-md-4">
                            <textarea class="form-control" placeholder="Not Set" ng-model="operation.operationParameter"></textarea>
                        </div>
                    </div>

                    <div class="row col-md-12" ng-show="operation.operationType == 22">
                        <div class="col-md-3">Check One</div>
                        <div class="col-md-4">
                            <input type="radio" name="minutes" ng-model="operation.operationParameter" value="0">Enable Wifi
                            <input type="radio" name="minutes" ng-model="operation.operationParameter" value="1">Disable Wifi
                        </div>
                    </div>


                    <div class="row col-md-12" ng-show="operation.operationType == 10">
                        <div class="col-md-3">Parameter</div>
                        <div class="col-md-4">
                            <span class="info-span">
                                <input type="radio" name="minutes" value="1" ng-model="operation.operationParameter" checked>30 Mins
                                <input type="radio" name="minutes" ng-model="operation.operationParameter" value="2">1 Hour
                                <input type="radio" name="minutes" ng-model="operation.operationParameter" value="3">4 Hrs
                                <input type="radio" name="minutes" ng-model="operation.operationParameter" value="4">24 Hrs
                            </span>

                        </div>
                    </div>

                    <div class="row col-md-12" ng-show="operation.operationType == 13">
                        <div class="col-md-3">Parameter</div>
                        <div class="col-md-4">
                            <input type="text" class="form-control" placeholder="Not Set" ng-model="operation.operationParameter" />
                            <span class="info-span">Provide a http url which points to archive in .tar format</span>
                        </div>
                    </div>

                    <div class="row col-md-12" ng-show="operation.operationType == 15">
                        <div class="col-md-3">Parameter</div>
                        <div class="col-md-4">
                            <input type="text" class="form-control" placeholder="Not Set" ng-model="operation.operationParameter" />
                            <span class="info-span">domains to whitelist (e.g. .facebook.com,.facebook.net)</span>
                        </div>
                    </div>
                    <div class="row col-md-12" ng-show="operation.operationType == 18">
                        <div class="col-md-3">Parameter</div>
                        <div class="col-md-4">
                            <input type="text" class="form-control" placeholder="Not Set" ng-model="operation.operationParameter" />
                            <span class="info-span">Enter Timestamp of the config to restore from</span>
                        </div>
                    </div>
                    <div class="row col-md-12" ng-show="operation.operationType == 24">
                        <div class="col-md-3">List of nasids comma seperated</div>
                        <div class="col-md-4">
                            <input type="text" class="form-control" placeholder="Not Set" ng-model="operation.operationParameter" />
                            <span class="info-span">Enter multiple nasid comma seperated</span>
                        </div>
                    </div>
                    <div class="row col-md-12" ng-show="operation.operationType == 25 || operation.operationType == 8 || operation.operationType == 32 || operation.operationType == 14">
                        <div class="col-md-3">List of nasids comma seperated</div>
                        <div class="col-md-4">
                            <input type="text" class="form-control" placeholder="Not Set" ng-model="operation.operationNasids" />
                            <span class="info-span">Enter multiple nasid comma seperated</span>
                        </div>
                    </div>
                </div>

                <div ng-if="operationList.length > 0" class="custom_scroll">
                    <table class="table table-responsive" id="operations-list">
                        <thead>
                            <tr>
                                <th>Operation</th>
                                <th>Parameter</th>
                                <th>Submitted On</th>
                                <th>Status</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr ng-repeat="ops in operationList">
                                <td>{{ops.operationTypeText}}</td>
                                <td>{{ops.operationParameter}}</td>
                                <td>{{ops.operationPublishTime}}</td>
                                <td>{{ops.status == 3 ? 'Failed' : (ops.status==1 ? 'Finished at ' + ops.operationFinishTime : (ops.status == 2 ? 'Expired' : 'Still Pending'))}}</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>


        <div ng-if="router.selected.length == 1" class="container-fluid">
            <div class="col-md-12 config-row">
                <h5><i class="fa fa-key" aria-hidden="true"></i> Access List</h5>
                <div class="col-md-12 config-box access-list">
                    <div ng-if="accessList.BLOCKED_LIST && featureEnabled(1)==1">
                        <div class="row settings">
                            <div class="col-md-10">Blocked Number List</div>
                            <div class="col-md-2 right-align">
                                <a data-toggle="collapse" data-target="#blockNumbers" class="edit-link" title="Click for edit blocked numbers"><i class="fa fa-pencil" aria-hidden="true"></i>  Edit</a>
                            </div>
                        </div>
                        <div id="blockNumbers" class="collapse">
                            <div ng-repeat="tempValue in accessList.BLOCKED_LIST">
                                <div class="row">
                                    <div class="col-md-4"><input class="form-control input-group" placeholder="Mobile Number" ng-model="tempValue.Key" /></div>
                                    <div class="col-md-4"><input class="form-control input-group" placeholder="Name" ng-model="tempValue.Value" /></div>
                                    <div ng-if='(accessList.BLOCKED_LIST.length > 0)' class="col-md-4 right-align">
                                        <a ng-click="accessList.BLOCKED_LIST.splice(accessList.BLOCKED_LIST.indexOf(tempValue), 1);" title="click to remove">X</a>
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-4 col-md-offset-8 right-align">
                                    <input class="btn add-btn" type="submit" value="Add" ng-click="accessList.BLOCKED_LIST.push({Key:'',Value:''})" />
                                    <input id="blocked_num_upld_btn" style="display:none;" type="file" on-read-file="readConetent($fileContent, accessList.BLOCKED_LIST)" accept=".csv" />
                                    <button class="btn add-btn" onclick="document.getElementById('blocked_num_upld_btn').click()">Upload CSV</button>
                                    <button ng-if="(accessList.BLOCKED_LIST.length > 0)" class="btn add-btn" ng-click="removeAll(accessList.BLOCKED_LIST)">Remove All</button>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div ng-if="accessList.WHITE_LIST && featureEnabled(3)==1">
                        <div class="row settings">
                            <div class="col-md-10">Whitelist Phone Number List</div>
                            <div class="col-md-2 right-align">
                                <a data-toggle="collapse" data-target="#whiteListNumbers" class="edit-link" title="Click for edit blocked numbers"><i class="fa fa-pencil" aria-hidden="true"></i>  Edit</a>
                            </div>
                        </div>
                        <div id="whiteListNumbers" class="collapse">
                            <div ng-repeat="tempValue in accessList.WHITE_LIST">
                                <div class="row">
                                    <div class="col-md-4"><input class="form-control input-group" placeholder="Mobile Number" ng-model="tempValue.Key" /></div>
                                    <div class="col-md-4"><input class="form-control input-group" placeholder="Name" ng-model="tempValue.Value" /></div>
                                    <div ng-if='(accessList.WHITE_LIST.length > 0)' class="col-md-4 right-align">
                                        <a ng-click="accessList.WHITE_LIST.splice(accessList.WHITE_LIST.indexOf(tempValue), 1);" title="click to remove">X</a>
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-4 col-md-offset-8 right-align">
                                    <input class="btn add-btn" type="submit" value="Add" ng-click="accessList.WHITE_LIST.push({Key:'',Value:''})" />
                                    <input id="white_num_upld_btn" style="display:none;" type="file" on-read-file="readConetent($fileContent, accessList.WHITE_LIST)" accept=".csv"/>
                                    <button class="btn add-btn" onclick="document.getElementById('white_num_upld_btn').click()">Upload CSV</button>
                                    <button ng-if="(accessList.WHITE_LIST.length > 0)" class="btn add-btn" ng-click="removeAll(accessList.WHITE_LIST)">Remove All</button>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div ng-if="accessList.VIP_LIST && featureEnabled(4)==1">
                        <div class="row settings">
                            <div class="col-md-10">VIP Phone Number List</div>
                            <div class="col-md-2 right-align">
                                <a data-toggle="collapse" data-target="#vipListNumbers" class="edit-link" title="Click for edit blocked numbers"><i class="fa fa-pencil" aria-hidden="true"></i>  Edit</a>
                            </div>
                        </div>
                        <div id="vipListNumbers" class="collapse">
                            <div ng-repeat="tempValue in accessList.VIP_LIST">
                                <div class="row">
                                    <div class="col-md-4"><input class="form-control input-group" placeholder="Mobile Number" ng-model="tempValue.Key" /></div>
                                    <div class="col-md-4"><input class="form-control input-group" placeholder="Name" ng-model="tempValue.Value" /></div>
                                    <div ng-if='(accessList.VIP_LIST.length > 0)' class="col-md-4 right-align">
                                        <a ng-click="accessList.VIP_LIST.splice(accessList.VIP_LIST.indexOf(tempValue), 1);" title="click to remove">X</a>
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-4 col-md-offset-8 right-align">
                                    <input class="btn add-btn" type="submit" value="Add" ng-click="accessList.VIP_LIST.push({Key:'',Value:''})" />
                                    <input id="vip_num_upld_btn" style="display:none;" type="file" on-read-file="readConetent($fileContent, accessList.VIP_LIST)" accept=".csv" />
                                    <button class="btn add-btn" onclick="document.getElementById('vip_num_upld_btn').click()">Upload CSV</button>
                                    <button ng-if="(accessList.VIP_LIST.length > 0)" class="btn add-btn" ng-click="removeAll(accessList.VIP_LIST)">Remove All</button>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div ng-if="accessList.MAC_WHITELISTING && featureEnabled(12)==1">
                        <div class="row settings">
                            <div class="col-md-10">VIP Mac Id List</div>
                            <div class="col-md-2 right-align">
                                <a data-toggle="collapse" data-target="#vipMacIds" class="edit-link" title="Click for edit blocked numbers"><i class="fa fa-pencil" aria-hidden="true"></i>  Edit</a>
                            </div>
                        </div>
                        <div id="vipMacIds" class="collapse">
                            <div ng-repeat="tempValue in accessList.MAC_WHITELISTING">
                                <div class="row">
                                    <div class="col-md-4"><input class="form-control input-group" placeholder="MAC id" ng-model="tempValue.Key" /></div>
                                    <div class="col-md-4"><input class="form-control input-group" placeholder="Name" ng-model="tempValue.Value" /></div>
                                    <div ng-if='(accessList.MAC_WHITELISTING.length > 0)' class="col-md-4 right-align">
                                        <a ng-click="accessList.MAC_WHITELISTING.splice(accessList.MAC_WHITELISTING.indexOf(tempValue), 1);" title="click to remove">X</a>
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-4 col-md-offset-8 right-align">
                                    <input class="btn add-btn" type="submit" value="Add" ng-click="accessList.MAC_WHITELISTING.push({Key:'',Value:''})" />
                                    <input id="vip_mcid_upld_btn" style="display:none;" type="file" on-read-file="readConetent($fileContent, accessList.MAC_WHITELISTING)" accept=".csv" />
                                    <button class="btn add-btn" onclick="document.getElementById('vip_mcid_upld_btn').click()">Upload CSV</button>
                                    <button ng-if="(accessList.MAC_WHITELISTING.length > 0)" class="btn add-btn" ng-click="removeAll(accessList.MAC_WHITELISTING)">Remove All</button>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div ng-if="accessList.MAC_BLACKLISTING && featureEnabled(29)==1">
                        <div class="row settings">
                            <div class="col-md-10">Mac Id Black List</div>
                            <div class="col-md-2 right-align">
                                <a data-toggle="collapse" data-target="#blacklistMacIds" class="edit-link" title="Click for edit blacklist mac ids"><i class="fa fa-pencil" aria-hidden="true"></i>  Edit</a>
                            </div>
                        </div>
                        <div id="blacklistMacIds" class="collapse">
                            <div ng-repeat="tempValue in accessList.MAC_BLACKLISTING">
                                <div class="row">
                                    <div class="col-md-4"><input class="form-control input-group" placeholder="MAC id" ng-model="tempValue.Key" /></div>
                                    <div class="col-md-4"><input class="form-control input-group" placeholder="Name" ng-model="tempValue.Value" /></div>
                                    <div ng-if='(accessList.MAC_BLACKLISTING.length > 0)' class="col-md-4 right-align">
                                        <a ng-click="accessList.MAC_BLACKLISTING.splice(accessList.MAC_BLACKLISTING.indexOf(tempValue), 1);" title="click to remove">X</a>
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-4 col-md-offset-8 right-align">
                                    <input class="btn add-btn" type="submit" value="Add" ng-click="accessList.MAC_BLACKLISTING.push({Key:'',Value:''})" />
                                    <input id="balck_mcid_upld_btn" style="display:none;" type="file" on-read-file="readConetent($fileContent, accessList.MAC_BLACKLISTING)" accept=".csv" />
                                    <button class="btn add-btn" onclick="document.getElementById('balck_mcid_upld_btn').click()">Upload CSV</button>
                                    <button ng-if="(accessList.MAC_BLACKLISTING.length > 0)" class="btn add-btn" ng-click="removeAll(accessList.MAC_BLACKLISTING)">Remove All</button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-md-12 btn-container">
                <button class="btn btn-primary" id="saveAccessList" ng-click="submitAccessList()">Save</button>
            </div>

        </div>

        <div ng-if="router.selected.length == 1 && blockedWebsites && featureEnabled(18) == 1" class="container-fluid">
            <div class="col-md-12 config-row">
                <h5><i class="fa fa-key" aria-hidden="true"></i> Blocked Website</h5>
                <div class="col-md-12 config-box access-list">
                    <div class="row settings">
                        <div class="col-md-10">List of blocked websites</div>
                        <div class="col-md-2 right-align">
                            <a data-toggle="collapse" data-target="#blockedWebsite" class="edit-link" title="Click for edit blocked numbers"><i class="fa fa-pencil" aria-hidden="true"></i>  Edit</a>
                        </div>
                    </div>
                    <div id="blockedWebsite" class="collapse">
                        <div ng-repeat="blocked in blockedWebsites">
                            <div class="row">
                                <div class="col-md-4"><input ng-model="blocked.siteName" class="form-control input-group" placeholder="Block Website" ng-model="tempValue.config" /></div>
                                <div ng-if='(tempValue.config.length > 0)' class="col-md-4 right-align"><a ng-click="blockedWebsites.splice(blockedWebsites.indexOf(blocked), 1)" title="click to remove">X</a></div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-4 col-md-offset-8 right-align"><input class="btn add-btn" type="submit" value="Add" ng-click="blockedWebsites.push({})" /></div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-md-12 btn-container">
                <button class="btn btn-primary" ng-click="saveBlockedWebsite()">Save</button>
            </div>
        </div>


        <div ng-if="router.selected.length == 1" feature-toggle="38" class="container-fluid">
            <div class="col-md-12 config-row">
                <h5><i class="fa fa-key" aria-hidden="true"></i>Set Authentication group</h5>
                <div clas="row authentication_group">
                    <div class="col-md-4">
                        <label>
                            Authentication Group&nbsp;&nbsp;<span class="glyphicon glyphicon-question-sign info-question-sign"
                                                                  tooltip="User will not be asked to login on group of nases set to same store group"
                                                                  tooltip-trigger
                                                                  tooltip-animation="false"
                                                                  tooltip-placement="top"></span>
                        </label>
                    </div>
                    <div class="col-md-4" style="background-color: white;">
                        <select class="form-control" ng-model="data.storeGroupPolicy">
                            <option ng-repeat="storeGroup in storeGroups" value="{{storeGroup.id}}" ng-selected="storeGroup.id == data.storeGroupPolicy">{{storeGroup.storeGroupName}}</option>
                        </select>
                    </div>
                    <div class="col-md-4" style="text-align:right;">
                        <button class="btn btn-primary" ng-click="saveStorePolicyGroup()">Save</button>
                    </div>
                </div>

            </div>
        </div>

    </div>
</div>
<!--</form>-->
<script type="text/ng-template" id="/editGroup.html">
    <div style="padding: 30px 20px">
        <button class="btn" style="float:right;background: transparent;" ng-click="cancel()"><b>X</b></button>
        <div>
            <div>
                <img width="75px" src="../images/logo.png" />
                <p style="color:#0c4da2;padding:10px 0;">{{data.group.groupName ? data.group.groupName : 'New Group'}}</p>
            </div>
        </div>
        <div>
            <input class="form-control" ng-model="data.group.groupName" placeholder="Group name" />
            <h5 style="margin-top:20px;">Members</h5>
            <div ng-repeat="user in data.group.values">
                <input class="form-control input-group" style="width:170px;display:inline-block;" placeholder="Mobile" ng-model="user.config" />
                <input class="form-control input-group" style="margin-left:10px;width:170px;display:inline-block;" placeholder="Name" ng-model="user.name" />
                <a ng-click="data.group.values.splice(data.group.values.indexOf(user), 1)">X</a>
            </div>
            <p><a ng-click="data.group.values.push({mobile:''})" class="glyphicon glyphicon-plus"></a></p>
        </div>
        <div style="text-align:right;">
            <button ng-click="ok()" class="btn-primary blue">Done</button>
            <button ng-click="cancel()" class="btn-warning">Cancel</button>
        </div>
    </div>
</script>
<script type="text/ng-template" id="/confirmationDialog.html">
    <div style="padding: 30px 20px">
        <button class="btn" style="float:right;background: transparent;" ng-click="cancel()"><b>X</b></button>
        <div>
            <div>
                <img width="75px" src="../images/logo.png" />
                <p style="color:#0c4da2;padding:10px 0;">Information to Everyone</p>
            </div>
        </div>
        <div>
            <p>{{data.msg}}</p>
        </div>
        <div style="text-align:right;">
            <button ng-click="ok()" class="btn-primary blue">Yes</button>
            <button ng-click="cancel()" class="btn-warning">Cancel</button>
        </div>
    </div>
</script>