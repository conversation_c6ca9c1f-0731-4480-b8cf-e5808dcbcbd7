<div class="select-filters">
    <div class="row locations-selector">
        <div class="ui-select-input-group">
            <span class="input-group-addon"><i class="glyphicon glyphicon-search"></i></span>
            <div class="cell location">
                <ui-select multiple ng-model="location.selected" theme="select2" close-on-select="false" ng-change="getSmsOverview()">
                    <ui-select-match placeholder="Locations">
                        {{$item.storeName}}
                    </ui-select-match>
                    <ui-select-choices repeat="router in $parent.positiveNases track by $index | propsFilter: {nasid: $select.search, storeName: $select.search}">
                        <span ng-bind-html="router.storeName | highlight: $select.search"></span>
                        <span ng-bind-html="router.city | highlight: $select.search"></span>
                        <span ng-bind-html="router.state | highlight: $select.search"></span>
                        <span ng-if="router.nasid != 'Select All'" ng-bind-html="router.nasid | highlight: $select.search"></span>
                    </ui-select-choices>
                </ui-select>
            </div>
        </div>
    </div>
</div>
    <div class="sms-analytics">
        <div class="row">
            <div ng-if="nasIds.length >= 1" class="page-header">
                <date-range-widget id="date-range-picker-for-report"></date-range-widget>
            </div>

            <div ng-if="nasIds.length >= 1 && !smsImpactReport && featureEnabled(7) === 1" class="container-fluid sms-stats-container">
                <!--<div class="container-fluid sms-stats-container">-->
                <h5><i class="fa fa-key" aria-hidden="true"></i> SMS Overview</h5>
                <div class="col-md-12 auto-sms">
                    <h6>Automated SMS</h6>
                    <div class="col-md-12 auto-sms-container no-pad">
                        <div class="col-md-4 center-align">
                            <div class="sms-info-card">
                                <img src="../../images/smssent_wc.png" alt="Sent smses">
                            </div>
                            <h3>{{formatNumber(smsOverview.auto.sent_sms_count)}}</h3>
                            <p>SMS sent</p>
                        </div>
                        <div class="col-md-4 center-align">
                            <div class="sms-info-card">
                                <img src="../../images/repeatuser_wc.png" alt="Returning customers">
                            </div>
                            <h3>{{formatNumber(smsOverview.auto.returning_customer_count)}}</h3>
                            <p>Customers returned</p>
                        </div>
                        <div class="col-md-4 center-align">
                            <div class="sms-info-card">
                                <img src="../../images/increasebusiness_wc.png" alt="Increase in business">
                            </div>
                            <h3>Rs {{formatNumber(smsOverview.auto.money_earned)}}</h3>
                            <p>Increase in business</p>
                        </div>
                        <div class="col-md-12 detailed_report_link_container">
                            <a href="" ng-click="fetchSmsImpactReport()">Click here view detailed report</a>
                        </div>
                    </div>
                </div>
                <div class="col-md-12 custom-sms">
                    <h6>Custom SMS</h6>
                    <div class="col-md-12 custom-sms-container no-pad">
                        <div class="col-md-4 center-align">
                            <div class="sms-info-card">
                                <img src="../../images/smssent_wc.png" alt="Sent smses">
                            </div>
                            <h3>{{formatNumber(smsOverview.custom.sent_sms_count)}}</h3>
                            <p>SMS sent</p>
                        </div>
                        <div class="col-md-4 center-align">
                            <div class="sms-info-card">
                                <img src="../../images/repeatuser_wc.png" alt="Returning customers">
                            </div>
                            <h3>{{formatNumber(smsOverview.custom.returning_customer_count)}}</h3>
                            <p>Customer returned</p>
                        </div>
                        <div class="col-md-4 center-align">
                            <div class="sms-info-card">
                                <img src="../../images/increasebusiness_wc.png" alt="Increase in business">
                            </div>
                            <h3>Rs {{formatNumber(smsOverview.custom.money_earned)}}</h3>
                            <p>Increase in business</p>
                        </div>
                        <!--<div class="col-md-12 detailed_report_link_container">
                    <a href="">Click here view detailed report</a>
                </div>-->
                    </div>

                </div>
            </div>
            <div ng-if="smsImpactReport && featureEnabled(7)" class="container-fluid sms-report-container">
                <h5><i class="fa fa-key" aria-hidden="true"></i> SMS Report</h5>
                <table class="sms_report_table">
                    <thead>
                        <tr>
                            <th class="twenty_percent">
                                <div class="table_header_cell">
                                    <img src="../../images/smstype_wc.png" alt="SMS types">
                                    <p>SMS Type</p>
                                </div>
                            </th>
                            <th class="twenty_percent">
                                <div class="table_header_cell">
                                    <img src="../../images/smssent_wc.png" alt="Sent smses">
                                    <p>SMS Delivered</p>
                                </div>
                            </th>
                            <th class="twenty_percent">
                                <div class="table_header_cell">
                                    <img src="../../images/repeatuser_wc.png" alt="Returning customers">
                                    <p>Customers returned</p>
                                </div>
                            </th>
                            <th class="forty_percent">
                                <div class="table_header_cell">
                                    <img src="../../images/increasebusiness_wc.png" alt="Increase in business">
                                    <p>Increase in business</p>
                                </div>
                            </th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr ng-repeat="report in smsImpactReport">
                            <td class="twenty_percent">
                                <div class="row table_body_cell">
                                    <div class="col-md-12 no-pad">
                                        <h6 ng-if="report.sms_category == 1 && report.sms_segment == 1">Offer <span>(Loyal Customers)</span></h6>
                                        <h6 ng-if="report.sms_category == 1 && report.sms_segment == 2">Offer <span>(New Customers)</span></h6>
                                        <h6 ng-if="report.sms_category == 1 && report.sms_segment == 3">Offer <span>(Attritor Customers)</span></h6>
                                        <h6 ng-if="report.sms_category == 2 && report.sms_segment == 1">Greetings <span>(Loyal Customers)</span></h6>
                                        <h6 ng-if="report.sms_category == 2 && report.sms_segment == 2">Greetings <span>(New Customers)</span></h6>
                                        <h6 ng-if="report.sms_category == 2 && report.sms_segment == 3">Greetings <span>(Attritor Customers)</span></h6>
                                    </div>
                                </div>
                            </td>
                            <td class="twenty_percent">
                                <div class="row table_body_cell">
                                    <div class="col-md-12">
                                        <p>{{formatNumber(report.sent_sms_count)}}</p>
                                    </div>
                                </div>
                            </td>
                            <td class="twenty_percent">
                                <div class="row table_body_cell">
                                    <div class="col-md-12">
                                        <p>{{formatNumber(report.returning_customer_count)}}</p>
                                    </div>
                                </div>
                            </td>
                            <td class="forty_percent">
                                <div class="row table_body_cell">
                                    <div class="col-md-12">
                                        <p>Rs {{formatNumber(report.money_earned)}}</p>
                                    </div>
                                </div>
                            </td>
                        </tr>
                    </tbody>
                </table>
                <div class="col-md-12 overview_report_link_container">
                    <a href="" ng-click="getSmsOverview()">Click here to return on overview</a>
                </div>
                <!--<table class="sms_report_table_final">
            <tbody>
                <tr>
                    <td class="twenty_percent_final">
                        <div class="row table_body_cell">
                            <div class="col-md-12">
                                <p>Total</p>
                            </div>
                        </div>
                    </td>
                    <td class="twenty_percent_final">
                        <div class="row table_body_cell">
                            <div class="col-md-12">
                                <p>{{smsOverview.auto.sent_sms_count}}</p>
                            </div>
                        </div>
                    </td>
                    <td class="twenty_percent_final">
                        <div class="row table_body_cell">
                            <div class="col-md-12">
                                <p>{{smsOverview.auto.returning_customer_count}}</p>
                            </div>
                        </div>
                    </td>
                    <td class="forty_percent_final">
                        <div class="row table_body_cell">
                            <div class="col-md-12">
                                <p>Rs {{smsOverview.auto.money_earned}}</p>
                            </div>
                        </div>
                    </td>
                </tr>
            </tbody>
        </table>-->
            </div>
        </div>
    </div>
