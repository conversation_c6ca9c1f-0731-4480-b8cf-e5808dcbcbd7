    <div class="select-filters">
        <div class="row">
            <div id="nas-dropdown" class="col-md-12 selects input-group">
                <i class="glyphicon glyphicon-search"></i>
                <selectize config="multipleLocationSelectorConfig"
                           options='routerDetails'
                           ng-model="locations"
                           ng-change="getConfigueForStores()"
                           class="multiple-location select-loaction"
                           title="Select multiple locations, type to search"></selectize>
            </div>
        </div>
    </div>
    <div class="basiconfig-container">
        <div class="row">
            <div class="page-header">
                <h4>Base Configuration</h4>
            </div>
            <div ng-if="router.selected.length == 1" class="container-fluid config-container">
                <div ng-if="router.selected.length == 1">
                    <div ng-if="router.selected.length == 1" class="col-md-12 config-row">
                        <h5><i class="fa fa-users" aria-hidden="true"></i> Group</h5>
                        <div class="row">
                            <div class="col-md-4 no-pad">
                                <ui-select ng-model="currentGroup.selected" theme="selectize">
                                    <ui-select-match placeholder="Select a Group in the list">{{$select.selected.groupName}}</ui-select-match>
                                    <ui-select-choices repeat="group in groups | propsFilter: {groupName: $select.search}">
                                        <span ng-bind-html="group.groupName | highlight: $select.search"></span>
                                    </ui-select-choices>
                                </ui-select>
                            </div>
                            <div ng-if="currentGroup.selected.groupId !== 0 && currentGroup.selected.groupId !== -1" class="col-md-6">
                                <div class="btn-group">
                                    <a class="btn btn-primary" title="Edit Group" ng-if="currentGroup.selected.groupId !== 0" ng-click="editGroup(currentGroup.selected)">Edit Group</a>
                                    <a class="btn btn-primary" title="Delete Group" style="margin:0 10px;" ng-if="currentGroup.selected.groupId !== 0" ng-click="deleteGroup(currentGroup.selected)">Delet Group</a>
                                </div>
                            </div>
                            <div class="col-md-2">
                                <a class="btn btn-primary" title="Create new Group" ng-click="createNewGroup()">Create New Group</a>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-12">
                        <div class="seperator"></div>
                    </div>
                </div>
                <div ng-repeat="group in [currentGroup.selected]">
                    <div ng-if="group.radiusConfig">
                        <div class="col-md-12 config-row">
                            <h5><i class="fa fa-pie-chart" aria-hidden="true"></i> Session Timeout</h5>
                            <div class="col-md-6 config-box">
                                <div ng-if="config.text" ng-repeat="config in group.radiusConfig">
                                    <div ng-if="config.dataType === 0">
                                        <div class="row">
                                            <div class="col-md-9 no-pad">Timeout Limit</div>
                                            <div class="col-md-3 no-pad right-align"></div>
                                        </div>
                                        <div class="row">
                                            <div class="col-md-9 no-pad">Idle</div>
                                            <div class="col-md-3 no-pad right-align">
                                                <select class="form-control" ng-model="config.value.value" ng-change="changeDetector(config)">
                                                    <option value="600">10 Minutes</option>
                                                    <option value="900">15 Minutes</option>
                                                    <option value="1200">20 Minutes</option>
                                                    <option value="1800">Half an hour</option>
                                                    <option value="3600">1 hour</option>
                                                    <option value="7200">2 hours</option>
                                                    <option value="10800">3 hours</option>
                                                    <option value="14400">4 hours</option>
                                                    <option value="21600">6 hours</option>
                                                    <option value="43200">12 hours</option>
                                                    <option value="86400">24 hours</option>
                                                    <option value="259200">3 Days</option>
                                                    <option value="604800">1 Week</option>
                                                    <option value="2419200">4 Weeks</option>
                                                </select>
                                            </div>
                                        </div>
                                    </div>
                                    <div ng-if="config.dataType === 3" class="row">
                                        <div class="col-md-9 no-pad">Session</div>
                                        <div class="col-md-3 no-pad right-align">
                                            <select class="form-control" ng-model="config.value.value" ng-change="changeDetector(config)">
                                                <option value="600">10 Minutes</option>
                                                <option value="900">15 Minutes</option>
                                                <option value="1200">20 Minutes</option>
                                                <option value="1800">Half an hour</option>
                                                <option value="3600">1 hour</option>
                                                <option value="7200">2 hours</option>
                                                <option value="10800">3 hours</option>
                                                <option value="14400">4 hours</option>
                                                <option value="21600">6 hours</option>
                                                <option value="43200">12 hours</option>
                                                <option value="86400">24 hours</option>
                                                <option value="259200">3 Days</option>
                                                <option value="604800">1 Week</option>
                                                <option value="2419200">4 Weeks</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-12">
                            <div class="seperator"></div>
                        </div>
                        <div class=" col-md-12 config-row">
                            <h5><i class="fa fa-pie-chart" aria-hidden="true"></i> Data</h5>
                            <div class="col-md-6 config-box">
                                <div class="row">
                                    <div class="col-md-9 no-pad">Maximum Data Usage</div>
                                    <div class="col-md-3 no-pad right-align"><input class="pull-left" type="checkbox" />Unlimited</div>
                                </div>
                                <div ng-if="config.text" ng-repeat="config in group.radiusConfig">
                                    <div ng-if="(config.dataType === 2 && config.text == 'Maximum data usage / day')">
                                        <div class="row">
                                            <div class="col-md-9 no-pad">Per Day</div>
                                            <div class="col-md-3 no-pad right-align"><input class="form-control custom-input" type="text" value="{{config.value.name}}"></div>
                                        </div>
                                        <div class="row">
                                            <input ng-if="config.text == 'Maximum data usage / day'" type="range" min="0" max="2048" step="{{step}}" ng-model="config.value.value" ng-change="stepDetection(config.value); changeDetector(config)">
                                        </div>
                                    </div>
                                    <div ng-if="(config.dataType === 2 && config.text == 'Maximum data usage / month')">
                                        <div class="row">
                                            <div class="col-md-9 no-pad">Per Month</div>
                                            <div class="col-md-3 no-pad right-align"><input class="form-control custom-input" type="text" value="{{config.value.name}}"></div>
                                        </div>
                                        <div class="row">
                                            <input ng-if="config.text == 'Maximum data usage / month'" type="range" min="0" max="51200" step="{{step}}" ng-model="config.value.value" ng-change="stepDetection(config.value); changeDetector(config)">
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-12">
                            <div class="seperator"></div>
                        </div>
                        <div class="col-md-12 config-row">
                            <h5><i class="fa fa-tachometer" aria-hidden="true"></i> Speed</h5>
                            <div ng-if="config.text" ng-repeat="config in group.radiusConfig">
                                <div ng-if="(config.dataType === 1 && config.text === 'Maximum Download Bandwidth')" class="col-md-6 config-box">
                                    <div class="row">
                                        <div class="col-md-9 no-pad">Maximum Download Speed</div>
                                        <div class="col-md-3 no-pad right-align"><input class="pull-left" type="checkbox" />Unlimited</div>
                                    </div>
                                    <div class="row">
                                        <div class="col-md-9 no-pad">Default</div>
                                        <div class="col-md-3 no-pad right-align"><button class="btn custom-btn">Unlimited</button></div>
                                    </div>
                                    <div class="row">
                                        <div class="col-md-9 no-pad">After data exhausted</div>
                                        <div class="col-md-3 no-pad right-align"><input class="form-control custom-input" type="text" value="{{config.value.name}}"></div>
                                    </div>
                                    <div class="row">
                                        <input type="range" min="0" max="20000" step="100" ng-model="config.value.value" ng-change="dataStepsDetection(config.value,config.text); changeDetector(config)" ng-click="showAlert(config.value,config.text)">
                                    </div>
                                </div>
                                <div ng-if="(config.dataType === 1 && config.text === 'Maximum Upload Bandwidth')" class="col-md-6 config-box pull-right">
                                    <div class="row">
                                        <div class="col-md-9 no-pad">Maximum Upload Speed</div>
                                        <div class="col-md-3 no-pad right-align"><input class="pull-left" type="checkbox" />Unlimited</div>
                                    </div>
                                    <div class="row">
                                        <div class="col-md-9 no-pad">Default</div>
                                        <div class="col-md-3 no-pad right-align"><button class="btn custom-btn">Unlimited</button></div>
                                    </div>
                                    <div class="row">
                                        <div class="col-md-9 no-pad">After data exhausted</div>
                                        <div class="col-md-3 no-pad right-align"><input class="form-control custom-input" type="text" value="{{config.value.name}}"></div>
                                    </div>
                                    <div class="row">
                                        <input type="range" min="0" max="20000" step="100" ng-model="config.value.value" ng-change="dataStepsDetection(config.value,config.text); changeDetector(config)" ng-click="showAlert(config.value,config.text)">
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-12">
                            <div class="seperator"></div>
                        </div>
                    </div>
                    <!--<div ng-if="group.routerConfig">
                        <div class="col-md-12 config-row">
                            <h5><i class="fa fa-key" aria-hidden="true"></i> Access List</h5>
                            <div class="col-md-12 config-box access-list">
                                <div ng-if="config.text" ng-repeat="config in group.routerConfig">
                                    <div ng-if="(config.dataType === 4 && config.text === 'Blocked Phone Number List')">
                                        <div class="row settings">
                                            <div class="col-md-10">Blocked Number List</div>
                                            <div class="col-md-2 right-align">
                                                <a data-toggle="collapse" data-target="#blockNumbers" class="edit-link" title="Click for edit blocked numbers"><i class="fa fa-pencil" aria-hidden="true"></i>  Edit</a>
                                            </div>
                                        </div>
                                        <div id="blockNumbers" class="collapse">
                                            <div ng-repeat="tempValue in config.values">
                                                <div class="row">
                                                    <div class="col-md-4"><input ng-change="changeDetector(config)" class="form-control input-group" placeholder="Mobile Number" ng-model="tempValue.config" /></div>
                                                    <div class="col-md-4"><input ng-change="changeDetector(config)" class="form-control input-group" placeholder="Name" ng-model="tempValue.name" /></div>
                                                    <div ng-if='(tempValue.config.length > 0)' class="col-md-4 right-align"><a ng-click="config.values.splice(config.values.indexOf(tempValue), 1);changeDetector(config)" title="click to remove">X</a></div>
                                                </div>
                                            </div>
                                            <div class="row">
                                                <div class="col-md-4 col-md-offset-8 right-align"><input class="btn add-btn" type="submit" value="Add" ng-click="config.values.push({config:''})" /></div>
                                            </div>
                                        </div>
                                    </div>
                                    <div ng-if="config.dataType === 4 && config.text === 'Whitelist Phone Numbers'">
                                        <div class="row settings">
                                            <div class="col-md-10">Whitelist Phone Number List</div>
                                            <div class="col-md-2 right-align">
                                                <a data-toggle="collapse" data-target="#whiteListNumbers" class="edit-link" title="Click for edit blocked numbers"><i class="fa fa-pencil" aria-hidden="true"></i>  Edit</a>
                                            </div>
                                        </div>
                                        <div id="whiteListNumbers" class="collapse">
                                            <div ng-repeat="tempValue in config.values">
                                                <div class="row">
                                                    <div class="col-md-4"><input ng-change="changeDetector(config)" class="form-control input-group" placeholder="Mobile Number" ng-model="tempValue.config" /></div>
                                                    <div class="col-md-4"><input ng-change="changeDetector(config)" class="form-control input-group" placeholder="Name" ng-model="tempValue.name" /></div>
                                                    <div ng-if='(tempValue.config.length > 0)' class="col-md-4 right-align"><a ng-click="config.values.splice(config.values.indexOf(tempValue), 1);changeDetector(config)" title="click to remove">X</a></div>
                                                </div>
                                            </div>
                                            <div class="row">
                                                <div class="col-md-4 col-md-offset-8 right-align"><input class="btn add-btn" type="submit" value="Add" ng-click="config.values.push({config:''})" /></div>
                                            </div>
                                        </div>
                                    </div>
                                    <div ng-if="config.dataType === 4 && config.text === 'VIP Phone Number List'">
                                        <div class="row settings">
                                            <div class="col-md-10">VIP Phone Number List</div>
                                            <div class="col-md-2 right-align">
                                                <a data-toggle="collapse" data-target="#vipListNumbers" class="edit-link" title="Click for edit blocked numbers"><i class="fa fa-pencil" aria-hidden="true"></i>  Edit</a>
                                            </div>
                                        </div>
                                        <div id="vipListNumbers" class="collapse">
                                            <div ng-repeat="tempValue in config.values">
                                                <div class="row">
                                                    <div class="col-md-4"><input ng-change="changeDetector(config)" class="form-control input-group" placeholder="Mobile Number" ng-model="tempValue.config" /></div>
                                                    <div class="col-md-4"><input ng-change="changeDetector(config)" class="form-control input-group" placeholder="Name" ng-model="tempValue.name" /></div>
                                                    <div ng-if='(tempValue.config.length > 0)' class="col-md-4 right-align"><a ng-click="config.values.splice(config.values.indexOf(tempValue), 1);changeDetector(config)" title="click to remove">X</a></div>
                                                </div>
                                            </div>
                                            <div class="row">
                                                <div class="col-md-4 col-md-offset-8 right-align"><input class="btn add-btn" type="submit" value="Add" ng-click="config.values.push({config:''})" /></div>
                                            </div>
                                        </div>
                                    </div>
                                    <div ng-if="config.dataType === 4 && config.text === 'VIP MAC Id List'">
                                        <div class="row settings">
                                            <div class="col-md-10">VIP Mac Id List</div>
                                            <div class="col-md-2 right-align">
                                                <a data-toggle="collapse" data-target="#vipMacIds" class="edit-link" title="Click for edit blocked numbers"><i class="fa fa-pencil" aria-hidden="true"></i>  Edit</a>
                                            </div>
                                        </div>
                                        <div id="vipMacIds" class="collapse">
                                            <div ng-repeat="tempValue in config.values">
                                                <div class="row">
                                                    <div class="col-md-4"><input ng-change="changeDetector(config)" class="form-control input-group" placeholder="Mobile Number" ng-model="tempValue.config" /></div>
                                                    <div class="col-md-4"><input ng-change="changeDetector(config)" class="form-control input-group" placeholder="Name" ng-model="tempValue.name" /></div>
                                                    <div ng-if='(tempValue.config.length > 0)' class="col-md-4 right-align"><a ng-click="config.values.splice(config.values.indexOf(tempValue), 1);changeDetector(config)" title="click to remove">X</a></div>
                                                </div>
                                            </div>
                                            <div class="row">
                                                <div class="col-md-4 col-md-offset-8 right-align"><input class="btn add-btn" type="submit" value="Add" ng-click="config.values.push({config:''})" /></div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>-->
                    <div class="col-md-12 btn-container">
                        <button class="btn btn-primary" ng-click="submit()">Save</button>
                        <button class="btn btn-danger">Cancel</button>
                    </div>
                </div>
            </div>
        </div>
    </div>
<!--</form>-->
<script type="text/ng-template" id="/editGroup.html">
    <div style="padding: 30px 20px">
        <button class="btn" style="float:right;background: transparent;" ng-click="cancel()"><b>X</b></button>
        <div>
            <div>
                <img width="75px" src="../images/logo.png" />
                <p style="color:#0c4da2;padding:10px 0;">{{data.group.groupName ? data.group.groupName : 'New Group'}}</p>
            </div>
        </div>
        <div>
            <input class="form-control" ng-model="data.group.groupName" placeholder="Group name" />
            <h5 style="margin-top:20px;">Members</h5>
            <div ng-repeat="user in data.group.values">
                <input class="form-control input-group" style="width:170px;display:inline-block;" placeholder="Mobile" ng-model="user.config" />
                <input class="form-control input-group" style="margin-left:10px;width:170px;display:inline-block;" placeholder="Name" ng-model="user.name" />
                <a ng-click="data.group.values.splice(data.group.values.indexOf(user), 1)">X</a>
            </div>
            <p><a ng-click="data.group.values.push({mobile:''})" class="glyphicon glyphicon-plus"></a></p>
        </div>
        <div style="text-align:right;">
            <button ng-click="ok()" class="btn-primary blue">Done</button>
            <button ng-click="cancel()" class="btn-warning">Cancel</button>
        </div>
    </div>
</script>
<script type="text/ng-template" id="/confirmationDialog.html">
    <div style="padding: 30px 20px">
        <button class="btn" style="float:right;background: transparent;" ng-click="cancel()"><b>X</b></button>
        <div>
            <div>
                <img width="75px" src="../images/logo.png" />
                <p style="color:#0c4da2;padding:10px 0;">Information to Everyone</p>
            </div>
        </div>
        <div>
            <p>{{data.msg}}</p>
        </div>
        <div style="text-align:right;">
            <button ng-click="ok()" class="btn-primary blue">Yes</button>
            <button ng-click="cancel()" class="btn-warning">Cancel</button>
        </div>
    </div>
</script>