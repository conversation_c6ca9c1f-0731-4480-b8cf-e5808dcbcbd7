<div class="login-container i2e1-container">
    <img class="logo" src="../../images/logo.png" />
    <form id="loginForm" method="post" action="/Client/LoginWeb" class="i2e1-form">
        <div class="details">
            <h6 class="header">Device Management Portal</h6>
            <div class="cell">
                <input placeholder="Username" name="username" class="form-control" />
            </div>

            <div class="cell">
                <input placeholder="Password" name="password" type="password" class="form-control" />
                <input type="hidden" name="googleToken" id="googleToken" />
                <input type="hidden" name="authType" id="authType" />
            </div>

            <div class="row row-2">
                <div class="cell">
                    <button id="submit" type="submit" class="btn btn-primary btn-lg login">LOGIN</button>
                </div>
                
            </div>
            <div class="partition">
                <div class="left-part">

                </div>
                <div class="text">Or</div>
                <div class="right-part">

                </div>
            </div>
            <div class="cell">
                <div id="gSignInWrapper" style="text-align: center;">
                    <div id="customBtn" class="customGPlusSignIn">
                        <span class="icon"></span>
                        <span class="buttonText">Sign in Using Google</span>
                    </div>
                </div>
                <div ng-init="startApp()"></div>
            </div>
        </div>
        <div class="cell">
            <a href="#/forgetPassword">Forget Password ?</a>
        </div>
    </form>
</div>

<div class="footer" style="position: fixed;
    left: 0;
    right: 0;
    bottom: 0;
    text-align: center;
    background: white;
    border-top: 1px solid #c4c4c4;
    color: black;
    line-height: 2;
    font-weight: bold;
    font-size: 1.5rem;">
    For any assistance, call us immediately on +91-8880322222 or
    mail us at: <EMAIL>
</div>

<style>

    #customBtn {
        display: inline-block;
        background-color: #dc483b;
        border-radius: 1px;
        color: white;
    }
    
    #customBtn:hover {
        cursor: pointer;
    }
    
    span.label {
        font-family: serif;
        font-weight: normal;
    }
    
    span.icon {
        background: url('/images/g-plus.png') transparent 5px 50% no-repeat;
        display: inline-block;
        vertical-align: middle;
        width: 4.5rem;
        height: 3.4rem;
        border-right: 1px solid #fff;
        background-size: 3rem;
    }
    
    span.buttonText {
        display: inline-block;
        vertical-align: middle;
        padding-left: 2rem;
        padding-right: 2rem;
        font-size: 1.3rem;
        font-weight: bold;
        font-family: 'Roboto', sans-serif;
    }

</style>