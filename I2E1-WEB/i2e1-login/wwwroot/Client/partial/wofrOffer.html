<style>
.wofrRow {
    padding: 5px;
    line-height: 36px;
    vertical-align: top;
    border-top: 1px solid #dddddd;
    //background-color: #31459a;
    //color: white;
}
.editStoreProfileButton {
    background-color: #ffffff;
    padding: 5px;
    -moz-border-radius: 7px;
    border-radius: 7px;
    color: #000000;
}
.editStoreProfileButton:hover {
    text-decoration: none;
    color: #000000;
}
.storeConfigButton {
    background-color: #87319a;
    padding: 5px;
    -moz-border-radius: 7px;
    border-radius: 7px;
    color: #ffffff;
}
.storeConfigButton:hover {
    text-decoration: none;
    color: #ffffff;
    background-color: #449a31;
    box-shadow: ;
}
.wofrStartButton {
    background-color: #449a31;
    padding: 5px;
    -moz-border-radius: 7px;
    border-radius: 7px;
    color: #ffffff;
}
.wofrStopButton {
    background-color: red;
    padding: 5px;
    -moz-border-radius: 7px;
    border-radius: 7px;
    color: #fff;
}
.wofrStartButton:hover {
    text-decoration: none;
    color: white;
    background-color: #31459a;
}
.wofrStopButton:hover {
    text-decoration: none;
    color: #000000;
    background-color: #ffffff;
}
.wofrParentPlace {
    background: -moz-linear-gradient(-45deg, rgba(135, 49, 154, 0.5) 0, rgba(135, 49, 154, 0) 80%, rgba(135, 49, 154, 0) 100%);
    /* FF3.6-15 */
    
    background: -webkit-linear-gradient(-45deg, rgba(135, 49, 154, 0.5) 0, rgba(135, 49, 154, 0) 80%, rgba(135, 49, 154, 0) 100%);
    /* Chrome10-25,Safari5.1-6 */
    
    background: linear-gradient(135deg, rgba(135, 49, 154, 0.5) 0, rgba(135, 49, 154, 0) 80%, rgba(135, 49, 154, 0) 100%);
    /* W3C, IE10+, FF16+, Chrome26+, Opera12+, Safari7+ */
}
.wofr-modal-body {
    font-size: 1.2rem;
}
.wofr-modal-body .form-group {
    margin: 0;
    padding: 1rem 0 0;
}
.wofr-modal-body .form-group .control-label {
    display: block;
    font-size: 1.2rem;
    font-weight: normal;
    font-style: normal;
    font-stretch: normal;
    line-height: 5rem;
    text-align: left;
    color: #000000;
}
.wofr-modal-body .form-group input.form-control,
.wofr-modal-body .form-group input.custom-input {
    height: 3.2rem;
    background-color: #fff;
    border: solid 1px #b7b7b7;
    -webkit-appearance: none;
    -webkit-box-shadow: none;
    box-shadow: none;
    font-size: 1.2rem;
    padding: 1rem;
    border-radius: .2rem;
}
.wofr-modal-body select.form-control {
    height: 3.2rem;
    background-color: #fff;
    border: solid 1px #b7b7b7;
    -webkit-appearance: none;
    -webkit-box-shadow: none;
    box-shadow: none;
    font-size: 1.2rem;
    padding: 0 2rem 0 1rem;
    border-radius: .2rem;
}
textarea:focus,
textarea.form-control:focus,
input.form-control:focus,
input[type="text"]:focus,
.form-control[type="text"]:focus,
select:focus,
select.form-control:focus {
    box-shadow: none !important;
}

.form-control[disabled], 
.form-control[readonly], 
fieldset[disabled] .form-control {
    background-color: transparent;
    opacity: 0.5;
}

.custom_offerPeriod {
    padding: 1.5rem;
}
.custom_offerPeriod .row {
    padding: 1.5rem;
    position:relative;
}
.custom_offerPeriod .row label {
    text-transform: capitalize;
}
.custom_offerPeriod .row .col-md-offset-2.container-fluid,
.container-fluid.offers {
    margin-bottom: 1rem;
}
.control-label .clear {
    text-decoration: underline;
    cursor: pointer;
    color: #2196f3;
}
.form-check-label {
    padding-left: 1.25rem;
    margin-bottom: 0;
    cursor: pointer;
}
.form-check-inline {
    display: flex;
}
.form-check-inline .form-check-label {
    align-items: center;
}
.switch {
    position: absolute;
    display: inline-block;
    width: 3.9rem;
    height: 2rem;
    top: 1.5rem;
    right: 0;
}
.switch input {
    display: none;
}
.switch .slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #ccc;
    -webkit-transition: .4s;
    transition: .4s;
}
.switch .slider:before {
    position: absolute;
    content: "";
    height: 1.6rem;
    width: 1.6rem;
    left: 0.4rem;
    bottom: 0.2rem;
    background-color: white;
    -webkit-transition: .4s;
    transition: .4s;
}
input:checked + .slider {
    background-color: #2196F3;
}
input:focus + .slider {
    box-shadow: 0 0 1px #2196F3;
}
input:checked + .slider:before {
    -webkit-transform: translateX(1.6rem);
    -ms-transform: translateX(1.6rem);
    transform: translateX(1.6rem);
}
/* Rounded sliders */

.switch .slider.round {
    border-radius: 2rem;
}
.switch .slider.round:before {
    border-radius: 50%;
}
</style>
<div class="offers-container" feature-toggle="8">
    <div class="page-content">
        <div class="row campaign_content">
            <ul class="col-md-12 no-pad nav nav-pills">
                <li class="col-md-6 no-pad active">
                    <a showtab href="#manage_offers" data-toggle="tab" id="create_campaign_nav" ng-click="GetWofrData()">Manage Offers</a>
                </li>
                <li class="col-md-6 no-pad">
                    <a showtab href="#wofr_history" data-toggle="tab" ng-click="GetPastWofrOffers()">Past Offers</a>
                </li>
            </ul>
            <div class="tab-content clearfix col-md-12">
                <div class="tab-pane active" id="manage_offers" ng-init="GetWofrData()">
                    <div ng-if="WofrData.length" class="campaign-container container-fluid">
                        <div ng-if="market.stores.length" ng-repeat="market in WofrData track by market.parentPlaceId" class="campaign-container col-md-12" style="display:block;padding:5px; margin-right: 10px; margin-bottom: 10px;">
                            <h5 style="color:#333; text-shadow: 1px 1px 1px #999">{{::market.parentPlaceName}}</h5>
                            <table cols="6" style="padding:5px;" width="100%">
                                <tr style="padding-right:25px;" ng-repeat="store in market.stores track by $index" class="wofrRow" ng-style="{'background': getStoreBg(store), 'color': getStoreDisplayFg(store)}">
                                    <td width="20%" style="padding-left:25px;">{{::store.shopName | limitTo:45}}</td>
                                    <td width="20%">{{::store.storeNameAlias}}</td>
                                    <td width="40%" ng-if="!hasCompleteProfile(store)">
                                        <a class="storeConfigButton" ng-click="showStoreProfileEditDialog(store)">Click to claim your WOFR wifi profile and launch offers.&nbsp;&nbsp;<img height="20px" src="../images/storeops64.png"></a>
                                    </td>
                                    <td width="40%" ng-if="hasCompleteProfile(store)">
                                        <a class="editStoreProfileButton" ng-click="showStoreProfileEditDialog(store)">Edit Profile&nbsp;&nbsp;<img height="20px" src="../images/wofrlogo.png"></a>
                                    </td>
                                    <td width="20%" ng-if="hasCompleteProfile(store)">
                                        <a ng-if="!hasRunningCampaign(store)" class="wofrStartButton" ng-click="showWofrOfferCreateDialog(store)"><img height="25px" src="../images/promotions64.png">Start</a>
                                        <a ng-if="hasRunningCampaign(store)" class="wofrStopButton" ng-click="stopWofrOffer(store)"><img height="25px" src="../images/promotions64.png">Stop</a>
                                    </td>
                                    <td width="20%" ng-if="!hasCompleteProfile(store)">
                                        <span ng-disabled="true" ng-if="!hasRunningCampaign(store)" class="wofrStartButton" ng-click="" style="background-color:#ccc"><img height="25px" src="../images/promotions64.png">Start</span>
                                    </td>
                                </tr>
                            </table>
                        </div>
                        <br style="clear:both" />
                    </div>

                    <div class="no_offers_present" ng-if="!WofrData.length">
                        <span>
                            WOFR is currently inactive.<br />
                            <!--<span ng-if="isSalesAdmin"><br />-->
                        </span>
                        <p ng-if="isSalesAdmin">Impersonate to see client's promotions</p>
                    </div>
                </div>
                <div class="tab-pane" id="wofr_history">
                    <div ng-if="PastOffers.length" class="campaign-container col-md-8" ng-repeat="offer in PastOffers" style="display:block;padding:5px;margin-right: 10px; margin-bottom: 10px;">
                        <span><b>{{::offer.name}} </b></span><br>
                        <span><b>From: </b> {{::offer.start}}</span><br>
                        <span><b>To: </b> {{::offer.stop}}</span><br>
                        <span><b>Short Text: </b>{{::offer.short_text}}</span><br>
                        <span><b>Long Text: </b>{{::offer.long_text}}</span><br>
                    </div>
                    <div class="no_offers_present" ng-if="!PastOffers.length">
                        <span>
                            You are yet to create an Offer<br />
                            <span ng-if="isSalesAdmin"><br />Or</span>
                            <p ng-if="isSalesAdmin">Impersonate to see client's promotions</p>
                    </div>
                    <br style="clear:both" />
                </div>
                <br style="clear:both" />
            </div>
            <br style="clear:both" />
        </div>
    </div>
</div>
<script type="text/ng-template" id="/storeEdit.html">
    <div class="modal-header storeEditHeader" style="background:linear-gradient(90deg,rgba(135,49,154,0.90),rgba(68,154,49,0.75)),url({{data.store.profile.shop_image}});color:white">
        <h4 class="modal-title" id="storeEditLabel">WOFR WiFi Profile</h4>
    </div>
    <div class="modal-body col-md-12 no-pad storeEditBody">
        <div class="col-md-12 half-container">
            <div class="form-group" ng-disabled="data.store.profile.parentplaceId">
                <label for="parentplaceId" class="control-label">Marketplace<span class="man">*</span></label>
                <select ng-required="true" id="parentplaceId" class="form-control" ng-model="data.store.profile.parentplaceId" convert-to-number>
                    <option ng-repeat="(k,v) in data.parentPlaces" value="{{k}}" ng-selected="data.store.profile.parentplaceId == k">{{v.parentPlaceName}}</option>
                </select>
            </div>
            <div class="form-group">
                <label for="brand_name" class="control-label">Brand Name <span class="man">*</span></label>
                <input ng-required="true" class="form-control" id="brand_name" ng-model="data.store.profile.brand_name" placeholder="Brand Name" />
            </div>
            <div class="form-group">
                <label for="shop_image" class="control-label">Shop Image <span class="man">*</span></label>
                <input ng-required="true" class="form-control" id="shop_image" ng-model="data.store.profile.shop_image" placeholder="Shop Image URL" />
            </div>
        </div>
    </div>
    <div class="modal-footer storeFooter">
        <button ng-disabled="metadata.input.$error.required" ng-click="ok()" class="btn btn-primary">Save</button>
        <button ng-click="cancel()" class="btn btn-warning">Cancel</button>
    </div>
</script>

<script type="text/ng-template" id="/offerCreate.html">
    <div class="modal-header storeEditHeader" style="background:{{data.background}};color:{{data.color}}">
        <h4 class=" modal-title" id="storeEditLabel">
            Offer Campaign {{data.newOffer.offerName}} at {{data.store.shopName}}, {{data.store.storeNameAlias}}.
        </h4>
    </div>
    <div class="modal-body wofr-modal-body container-fluid">
        <div class="col-md-12">
            <div class="form-group">
                <div class="row">
                    <div class="col-md-6">
                        <label for="offerName" class="control-label">Campaign Name<span class="man">*</span></label>
                        <div class="container-fluid">
                            <input ng-required="true" class="form-control" id="offerName" ng-model="data.newOffer.offerName" placeholder="Campaign Name" />
                        </div>
                    </div>
                    <div class="col-md-6">
                        <label class="control-label">Scheduled Stop<span class="man">*</span></label>
                        <div class="form-check form-check-inline">
                            <label for="isScheduledNo" class="form-check-label">
                                <input class="form-check-input" type="radio" ng-model="data.newOffer.isScheduled" id="isScheduledNo" ng-value="false" name="isScheduled" ng-change="deleteScheduleStopDateProperty()"> No
                            </label>
                            <label for="isScheduledYes" class="form-check-label">
                                <input class="form-check-input" type="radio" ng-model="data.newOffer.isScheduled" id="isScheduledYes" ng-value="true" name="isScheduled" ng-change="deleteScheduleStopDateProperty()"> Yes
                            </label>
                            <label ng-if="data.newOffer.isScheduled == true" class="form-check-label" for="scheduledStopDate" style="margin: 0 1rem 0;vertical-align: middle;">Choose Date</label>
                            <input ng-if="data.newOffer.isScheduled == true" class="custom-input" type="text" ng-model="data.newOffer.scheduledStopDate" id="scheduledStopDate" date-picker />
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-6">
                        <label class="control-label">Premium Listing<span class="man">*</span></label>
                        <div class="form-check form-check-inline">
                            <label for="isPremiumListingNo" class="form-check-label">
                                <input class="form-check-input" type="radio" ng-model="data.newOffer.isPremiumListing" id="isPremiumListingNo" ng-value="false" name="isPremiumListing"> No
                            </label>
                            <label for="isPremiumListingYes" class="form-check-label">
                                <input class="form-check-input" type="radio" ng-model="data.newOffer.isPremiumListing" id="isPremiumListingYes" ng-value="true" name="isPremiumListing"> Yes
                            </label>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <label class="control-label">Redmption Notification<span class="man">*</span></label>
                        <div class="form-check form-check-inline">
                            <label for="notifyOnRedemptionNo" class="form-check-label">
                                <input class="form-check-input" type="radio" ng-model="data.newOffer.notifyOnRedemption" id="notifyOnRedemptionNo" ng-value="false" name="notifyOnRedemption"> No
                            </label>
                            <label for="notifyOnRedemptionYes" class="form-check-label">
                                <input class="form-check-input" type="radio" ng-model="data.newOffer.notifyOnRedemption" id="notifyOnRedemptionYes" ng-value="true" name="notifyOnRedemption"> Yes
                            </label>
                        </div>
                    </div>
                </div>
            </div>
            <div class="form-group">
                <label class="control-label">
                    Offers <span class="man">*</span>
                    <span class="clear pull-right"
                          ng-if="data.newOffer.offers[0]['name'] != ''
                          && data.newOffer.offers[0]['short_text'] != ''
                          && data.newOffer.offers[0]['long_text'] != ''"
                          ng-click="resetOffers()">Clear All</span>
                </label>
                <div class="container-fluid offers" ng-repeat="offer in data.newOffer.offers">
                    <div class="col-md-2 no-pad">
                        <input class="form-control" type="text" placeholder="Name your offer" ng-model="offer.name" />
                    </div>
                    <div class="col-md-4">
                        <input class="form-control" type="text" placeholder="Write a short text for your offer" ng-model="offer.short_text" />
                    </div>
                    <div class="col-md-4">
                        <input class="form-control" type="text" placeholder="Write a long text for your offer" ng-model="offer.long_text" />
                    </div>
                    <div class="col-md-2" ng-if="offer.name != '' && offer.short_text != '' && offer.long_text != ''">
                        <button type="button"
                                class="btn btn-default"
                                ng-click="data.newOffer.offers.push({
                                        name: '',
                                        short_text: '',
                                        long_text: '',
                                    });remove($event)">
                            <span class="glyphicon glyphicon-plus"></span> Add New
                        </button>
                    </div>
                </div>
            </div>
            <div class="form-group" ng-if="data.newOffer.offers[0]['name'] != '' && data.newOffer.offers[0]['short_text'] != '' && data.newOffer.offers[0]['long_text'] != ''">
                <label class="control-label">Offer Timings <span class="man">*</span></label>
                <div class="container-fluid" style="min-height: 5rem;display: flex;align-items: center;">
                    <div class="col-md-2">
                        <label for="offerPeriod1" class="form-check-label">
                            <input class="form-check-input" type="radio" ng-model="data.newOffer.offerPeriod" id="offerPeriod1" value="everyday" ng-change="resetOfferContent()"> Every Day
                        </label>
                    </div>
                    <div class="col-md-10" ng-if="data.newOffer.offerPeriod == 'everyday'">
                        <div class="form-inline">
                            <label>Select an offer <span class="man">*</span></label>
                            <select convert-to-number class="form-control" style="min-width:17.6rem;" ng-model="data.newOffer.everdayOffer" ng-change="setEverydayOffer()">
                                <option ng-repeat="offer in data.newOffer.offers track by $index" ng-if="offer.name != '' && offer.short_text != '' && offer.long_text != ''" value="{{$index}}" ng-selected="$index == 0">{{offer.name}}</option>
                            </select>
                        </div>
                    </div>
                </div>
                <div class="container-fluid">
                    <div class="col-md-12">
                        <label for="offerPeriod2" class="form-check-label">
                            <input class="form-check-input" type="radio" ng-model="data.newOffer.offerPeriod" id="offerPeriod2" value="custom" ng-change="resetOfferContent()"> Custome
                        </label>
                    </div>
                    <div class="col-md-12 custom_offerPeriod" ng-if="data.newOffer.offerPeriod == 'custom'">
                        <div class="row" ng-repeat="(day, rules) in data.newOffer.content">
                            <div class="col-md-2">
                                <label>{{day}}</label>
                            </div>
                            <div class="col-md-offset-2 container-fluid" ng-repeat="rule in rules track by $index" ng-init="ruleIndex = $index">
                                <div class="col-md-2">
                                    <label class="sr-only">Start time</label>
                                    <select ng-disabled="!data.newOffer.enabledDay[day]" class="form-control" ng-model="rule.start" convert-to-number>
                                        <option ng-repeat="option in range(23)" value="{{option}}" ng-selected="{{option == rule.start}}" ng-if="ruleIndex == 0 || (ruleIndex > 0 && option > rules[ruleIndex-1]['stop'])">{{option}}:00</option>
                                    </select>
                                </div>
                                <div class="col-md-2">
                                    <label class="sr-only">Stop time</label>
                                    <select ng-disabled="!data.newOffer.enabledDay[day]" class="form-control" ng-model="rule.stop" convert-to-number ng-change="checkForNextOfferSlot(day,ruleIndex)">
                                        <option ng-repeat="n in range(23)" value="{{n}}" ng-selected="{{n == rule.stop}}" ng-if="n >= rule.start || n == 23">{{n}}:59</option>
                                    </select>
                                </div>
                                <div class="col-md-4">
                                    <select ng-disabled="!data.newOffer.enabledDay[day]" class="form-control" ng-model="rule.payload" convert-to-number>
                                        <option ng-repeat="offer in data.newOffer.offers track by $index" ng-if="offer.name != '' && offer.short_text != '' && offer.long_text != ''" value="{{$index}}" ng-selected="$index == 0">{{offer.name}}</option>
                                    </select>
                                </div>
                                <div class="col-md-2" ng-if="rules[$index]['start'] != '23' && rules[$index]['stop'] != '23'">
                                    <button type="button"
                                            class="btn btn-default btn-sm"
                                            ng-click="rules.push({
                                                        start: (parseIntFromStr(rules[$index]['stop']) + 1).toString(),
                                                        stop: '23',
                                                        payload: 0
                                                    });remove($event)">
                                        <span class="glyphicon glyphicon-plus"></span> Add New
                                    </button>
                                </div>
                            </div>
                            <label class="switch">
                                <input type="checkbox" ng-model="data.newOffer.enabledDay[day]">
                                <span class="slider round"></span>
                            </label>
                        </div>

                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="modal-footer container-fluid">
        <button ng-disabled="metadata.input.$error.required" ng-click="ok()" class="btn btn-primary">Save</button>
        <button ng-click="cancel()" class="btn btn-warning">Cancel</button>
    </div>
</script>