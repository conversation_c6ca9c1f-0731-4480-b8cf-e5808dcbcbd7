<div class="sms-prime">
    <div class="row">
        <div class="col-md-12 no-pad block-header">
            <h3>Create Prime Campaign </h3>
        </div>
        <div class="prime-sms-configurator">
            <form name="primeConfigForm" class="form-horizontal prime-sms-configurator-form">
                <div class="container-fluid" ng-if="primeSmsDisabled">
                    <div class="col-md-12">
                        <p class="p-error text-center" ng-bind-html="primeSmsWarning"></p>
                    </div>
                </div>
                <div class="container-fluid">
                    <div class="col-md-4">
                        <div class="form-group">
                            <label class="{{(primeSmsDisabled ? 'disabled': '')}}" for="customer_segment">Select Customer Segment</label>
                            <select id="customer_segment" class="form-control" ng-disabled="primeSmsDisabled" ng-model="primeSmsConfigue.segment" ng-options="segment.value as segment.name for segment  in primalTemplates track by segment.value">
                                <option style="display:none" value="">-- Select Segment --</option>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="form-group">
                            <label class="{{(!primeSmsConfigue.segment ? 'disabled': '')}}" for="sms_type">Select SMS Type</label>
                            <select id="sms_type" class="form-control" ng-disabled="!primeSmsConfigue.segment" ng-model="primeSmsConfigue.category" ng-options="category.value as category.name for category in ((primalTemplates | filter:{'value':primeSmsConfigue.segment})[0].categories) track by category.value">
                                <option style="display:none" value="">-- Select SMS Type --</option>
                            </select>
                        </div>
                    </div>

                    <div class="col-md-4">
                        <div class="form-group">
                            <label class="{{(!primeSmsConfigue.category ? 'disabled': '')}} for="sms_template">Select SMS Template</label>
                            <select id="sms_template" class="form-control" ng-model="primeSmsConfigue.template" ng-disabled="!primeSmsConfigue.category" ng-model="primeSmsConfigue.template" ng-options="template.id as template.value for template in ((((primalTemplates | filter:{'value':primeSmsConfigue.segment})[0].categories) | filter:{'value':primeSmsConfigue.category})[0].templates) track by template.id" ng-change="preview(primeSmsConfigue)">
                                <option style="display:none" value="">-- Select Template --</option>
                            </select>
                        </div>
                    </div>
                </div>
                <div class="container-fluid">
                    <div class="col-md-4" ng-if="primeSmsConfigue.template && primeSmsConfigue.isCustom == 0">
                        <div class="form-group">
                            <label for="store_name">Store name</label>
                            <input type="text" id="store_name" class="form-control" ng-required="true" ng-model="primeSmsConfigue.storeName" ng-disabled="prime.enabled == 1 && !primeSmsConfigue.template" placeholder="Store Name" ng-change="preview(primeSmsConfigue)" />
                        </div>
                    </div>
                    <div class="col-md-4" ng-if="primeSmsConfigue.category == 1 && primeSmsConfigue.template && primeSmsConfigue.isCustom == 0">
                        <div class="form-group">
                            <label for="offer_discount">Offer Discount</label>
                            <div class="input-group">
                                <input aria-describedby="basic-addon1" type="number" id="offer_discount" class="form-control discount_input" min="1" max="99" ng-required="true" ng-model="primeSmsConfigue.discount" ng-disabled="primeSmsConfigue.enabled == 1 && !primeSmsConfigue.segment" placeholder="Enter Discount" ng-change="preview(primeSmsConfigue)" />
                                <span class="input-group-addon" id="basic-addon1">%</span>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-4" ng-if="primeSmsConfigue.category == 1 && primeSmsConfigue.template && primeSmsConfigue.isCustom == 0">
                        <div class="form-group">
                            <label for="sms_validity">Select Validity</label>
                            <select class="form-control" id="sms_validity" ng-disabled="primeSmsConfigue.enabled == 1" id="sms_valid_upto" ng-model="primeSmsConfigue.valid_upto" ng-change="preview(primeSmsConfigue)">
                                <option style="display:none" value="">-- Valid Till --</option>
                                <option ng-repeat="n in range(1,30)" value="{{n}}" ng-selected="primeSmsConfigue.valid_upto == {{n}}">{{n}} Day</option>
                            </select>
                        </div>
                    </div>
                </div>
                <div class="container-fluid" ng-if="primeSmsConfigue.template != 'custom'">
                    <div class="col-md-12">
                        <div class="form-group">
                            <label class="{{(!primeSmsConfigue.template ? 'disabled': '')}} for=" primesmspreview">Preview</label>
                            <textarea class="form-control" ng-model="primeSmsConfigue.preview" disabled id="primeSmsPreview">{{primeSmsConfigue.preview}}</textarea>
                            <div class="col-md-12 right-align sms_size">
                                Size-{{primeSmsConfigue.preview.length || 0}}&nbsp;&nbsp;&nbsp;Messages-{{messageCount(primeSmsConfigue.preview.length) || 0}}
                            </div>
                        </div>
                    </div>
                </div>
                <div class="container-fluid" ng-if="primeSmsConfigue.template == 'custom'">
                    <div class="col-md-12">
                        <div class="form-group">
                            <label for="primeSmsPreview">Create Message</label>
                            <textarea class="form-control" ng-model="primeSmsConfigue.preview" id="primeSmsPreview">{{primeSmsConfigue.preview}}</textarea>
                            <div class="col-md-12 right-align sms_size">
                                Size-{{primeSmsConfigue.preview.length || 0}}&nbsp;&nbsp;&nbsp;Messages-{{messageCount(primeSmsConfigue.preview.length) || 0}}
                            </div>
                        </div>
                    </div>
                </div>
                <div class="container-fluid">
                    <div class="form-group">
                        <div class="col-md-12">
                            <!--<button class="btn btn-primary-green pull-right" ng-model="primeSmsConfigue.enabled" ng-disabled="!validate(primeSmsConfigue)" ng-click="saveComm(primeSmsConfigue)" ng-true-value="1" ng-false-value="0">Enable</button>-->
                            <label class="btn pull-right btn-primary-green {{validate(primeSmsConfigue) ? '': 'disabled'}}">
                                <input type="checkbox" autocomplete="off" ng-model="primeSmsConfigue.enabled" ng-disabled="!validate(primeSmsConfigue)" ng-change="saveComm(primeSmsConfigue)" ng-true-value="1" ng-false-value="0" style="display:none;"> Enable
                            </label>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>
    <div ng-if="primeSmsHistory.length > 0" class="row">
        <div class="prime-sms-history">
            <table class="table table-bordered prime-sms-table">
                <thead>
                    <tr>
                        <th>Segment</th>
                        <th>Category</th>
                        <th>Store Name</th>
                        <th>Discount</th>
                        <th>Valid Till</th>
                        <th width="30%">Preview</th>
                        <th></th>
                    </tr>
                </thead>
                <tbody>
                    <tr ng-repeat="comm in primeSmsHistory">
                        <td>{{((primalTemplates | filter:{'value':comm.segment})[0].name)}}</td>
                        <td>{{(((primalTemplates | filter:{'value':comm.segment})[0].categories) | filter:{'value':comm.smsCategory})[0].name}}</td>
                        <td>{{comm.storeName}}</td>
                        <td><span ng-if="comm.discount > 0">{{comm.discount}}%</span></td>
                        <td>{{comm.validTillDate}}</td>
                        <td>{{comm.preview}}</td>
                        <td>
                            <p class="comm-delete" popover-placement="top" popover-trigger="mouseenter" popover="Diactivate this campaign">
                                <label class="btn btn-danger">
                                    <input type="checkbox" autocomplete="off" ng-model="comm.enabled" ng-disabled="!validate(comm)" ng-change="saveComm(comm)" ng-true-value="1" ng-false-value="0" style="display:none;"> Disable
                                </label>
                            </p>
                        </td>
                    </tr>
                </tbody>
            </table>

        </div>
    </div>
</div>