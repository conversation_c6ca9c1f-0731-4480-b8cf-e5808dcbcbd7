<div class="select-filters top-filter">
    <div class="row">
        <div class="col-md-6 nas-selector" style="padding-left: 0;">
            <!--<div class="selects input-group">
                <i class="glyphicon glyphicon-search"></i>
                <selectize config="singleLocationSelectorConfig"
                           options='$parent.routerDetails'
                           ng-model="data.location"
                           ng-change="searchNasSettings()"
                           class="select-loaction"
                           title="Select multiple locations, type to search"></selectize>
            </div>-->

            <div class="locations-selector">
                <div class="ui-select-input-group">
                    <div class="cell location">
                        <ui-select multiple limit="1"
                                   reset-search-input="true"
                                   remove-selected="false"
                                   ng-model="data.location"
                                   on-select="searchNasSettings($item, $model, 'location')"
                                   theme="select2"
                                   close-on-select="true">
                            <ui-select-match allow-clear="true" placeholder="Search or Enter Location">
                                {{$item.storeName}}
                            </ui-select-match>
                            <ui-select-choices position="down" repeat="router.nasid as router in $parent.storesList |
                                                   propsFilter: {nasid: $select.search, storeName: $select.search} |
                                                   limitTo:$parent.storesList track by $index"
                                               select-with-pagination
                                               choices-paging-list="$parent.storesList"
                                               search-term="$select.search"
                                               current-items="$parent.storesList.length"
                                               infinite-scroll-distance="2"
                                               page-number="1"
                                               page-size="100"
                                               search-funcation="$parent.searchLocations(theDirFn)"
                                               refresh="directiveFn()"
                                               refrsh-delay="1000">
                                <span ng-bind-html="router.storeName | highlight: $select.search"></span>
                                <span ng-bind-html="router.city | highlight: $select.search"></span>
                                <span ng-bind-html="router.state | highlight: $select.search"></span>
                                <span ng-bind-html="router.nasid | highlight: $select.search"></span>
                            </ui-select-choices>
                        </ui-select>
                        <span class="glyphicon glyphicon-refresh glyphicon-refresh-animate" style="display:none;"></span>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-6" style="padding-right: 0;">
            <input class="form-control setting-name-filter-input" type="text" placeholder="Search settings by name and press enter" ng-model="data.settingSearchString" ng-keydown="searchSettingsByName($event)" />
        </div>
    </div>
</div>

<div class="settings-container" infinite-scroll='loadMoreSettings()' infinite-scroll-container='"#admin_container"'>
    <div class="row">
        <div ng-if="isSalesAdmin || isReadOnlyAdmin || isOpsAdmin">
            <h5 ng-if="nasAdmins.length">Nas is associated to below clients</h5>
            <div style="padding-left:3rem;" ng-repeat="admin in nasAdmins">{{admin.name}}</div>
        </div>
        <div class="page-header">
            <h4>
                Settings
                <div class="dropdown add-new-group pull-right">
                    <button class="btn btn-primary b1 dropdown-toggle" type="button" id="addGroupBtn" data-toggle="dropdown" aria-haspopup="true" aria-expanded="true">Create New</button>
                    <div class="dropdown-menu dropdown-menu-right" id="popoverdiv" ng-click="$event.stopPropagation();">
                        <legend>Setting Name</legend>
                        <div class="form-group-textfield">
                            <input id="newSetting" type="text"
                                   ng-model="newSettingName"
                                   placeholder="Setting Name"
                                   class="form-control ng-pristine ng-untouched ng-valid">
                        </div>

                        <div class="pull-right footer-btn-group" id="footer-btn-group">
                            <button class="btn btn-primary" ng-click="createNewSetting()">Create</button>
                        </div>
                    </div>

                    <a class="btn btn-primary" href="https://www.i2e1.in/Passwordgenerator" target="_blank">Go to fdm portal</a>
                </div>
            </h4>
        </div>

        <div ng-if="data.settings.length == 0" class="no_data_found">
            No setting found. Create New
        </div>

        <div ng-repeat="setting in data.settings track by $index" class="settings container-fluid config-container" style="position: relative;">
            <h5 title="{{setting.settingId}}">{{setting.name.capitalize()}}</h5>
            <h5 ng-if="(isSalesAdmin || isReadOnlyAdmin) && setting.owner">Setting owner: {{setting.owner}}</h5>
            <div class="panel-group settings-group">
                <div class="panel panel-default">
                    <div class="panel-heading settings-panel-heading">
                        <h4 class="panel-title">
                            <a class="collapsed settings-dropdown" ng-click="fetchAllConfiguration(setting)" data-toggle="collapse" data-target="#defaultSettings{{setting.settingId}}" aria-expanded="false" aria-controls="collapseExample">
                                Settings
                                <span class="icon-arrow"></span>
                            </a>
                        </h4>
                    </div>
                    <div id="defaultSettings{{setting.settingId}}" class="panel-collapse collapse">
                        <div class="settings-panel-body panel-body">
                            <ul class="nav nav-tabs">
                                <li class="col-md-3 no-pad active">
                                    <a showtab data-toggle="tab" href="#basicConfig{{setting.settingId}}" class="basicTab" ng-click="fetchBasicConfiguration(setting)">
                                        Basic Configuration
                                    </a>
                                </li>
                                <li class="col-md-3 no-pad"  ng-if="isSalesAdmin || isOpsAdmin || username=='<EMAIL>'">
                                    <a class="advanceTab" showtab data-toggle="tab" href="#advanceConfig{{setting.settingId}}" ng-click="fetchAdvanceConfiguration(setting)">
                                        Advance Configuration
                                    </a>
                                </li>
                                <li class="col-md-3 no-pad"  ng-if="isSalesAdmin || isOpsAdmin || username=='<EMAIL>'">
                                    <a class="customeTab" showtab data-toggle="tab" href="#customised{{setting.settingId}}" ng-click="fetchTemplates(setting)">
                                        Customisation
                                    </a>
                                </li>
                                <li class="col-md-3 no-pad"  ng-if="isSalesAdmin || isOpsAdmin || username=='<EMAIL>'">
                                    <a class="healthTab" showtab data-toggle="tab" href="#healthAlerts{{setting.settingId}}" ng-click="fetchHealthAlerts(setting)">
                                        Health Alerts
                                    </a>
                                </li>
                            </ul>
                            <div class="tab-content">
                                <div class="tab-pane fade in active" id="basicConfig{{setting.settingId}}">
                                    <a ng-if="isOpsAdmin" ng-click="toggleHistory($event, setting, 'basic')" data-history="false">previous policies</a>
                                    <div class="history-toggler" ng-include="'/basic_config.htm'"></div>
                                    <div ng-if="isOpsAdmin" class="history-toggler previous-version display-none">
                                        <div class="row">
                                            <div class="board">
                                                <div class="board-inner">
                                                    <ul class="nav nav-tabs">
                                                        <li ng-repeat="version in setting.versions.basic" ng-click="getVersion(setting, version, $index, 'basic')">
                                                            <a showtab href="#{{setting.settingId}}_basic_history" data-toggle="tab" title="version.value">
                                                                <span class="round-tabs one" title="updated by {{version.key}} on {{version.value}}">
                                                                    {{$index + 1}}
                                                                </span>
                                                            </a>
                                                        </li>
                                                    </ul>
                                                </div>

                                                <div class="tab-content">
                                                    <div class="tab-pane fade" id="{{setting.settingId}}_basic_history" ng-include="'/basic_config_history.htm'">
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="tab-pane fade" id="advanceConfig{{setting.settingId}}">
                                    <a ng-if="isOpsAdmin" ng-click="toggleHistory($event, setting, 'advance')" data-history="false">previous policies</a>
                                    <div class="history-toggler" ng-include="'/advance_config.htm'"></div>
                                    <div ng-if="isOpsAdmin" class="history-toggler previous-version display-none">
                                        <div class="row">
                                            <div class="board">
                                                <div class="board-inner">
                                                    <ul class="nav nav-tabs">
                                                        <li ng-repeat="version in setting.versions.advance" ng-click="getVersion(setting, version, $index, 'advance')">
                                                            <a showtab href="#{{setting.settingId}}_advance_history" data-toggle="tab" title="version.value">
                                                                <span class="round-tabs one" title="updated by {{version.key}} on {{version.value}}">
                                                                    {{$index + 1}}
                                                                </span>
                                                            </a>
                                                        </li>
                                                    </ul>
                                                </div>

                                                <div class="tab-content">
                                                    <div class="tab-pane fade" id="{{setting.settingId}}_advance_history" ng-include="'/advance_config_history.htm'">
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="tab-pane fade" id="customised{{setting.settingId}}">
                                    <a ng-if="isOpsAdmin" ng-click="toggleHistory($event, setting, 'advance')" data-history="false">previous policies</a>
                                    <div class="history-toggler" ng-include="'/customise_config.htm'"></div>
                                    <div ng-if="isOpsAdmin" class="history-toggler previous-version display-none">
                                        <div class="row">
                                            <div class="board">
                                                <div class="board-inner">
                                                    <ul class="nav nav-tabs">
                                                        <li ng-repeat="version in setting.versions.advance" ng-click="getVersion(setting, version, $index, 'advance')">
                                                            <a showtab href="#{{setting.settingId}}_customise_history" data-toggle="tab" title="version.value">
                                                                <span class="round-tabs one" title="updated by {{version.key}} on {{version.value}}">
                                                                    {{$index + 1}}
                                                                </span>
                                                            </a>
                                                        </li>
                                                    </ul>
                                                </div>

                                                <div class="tab-content">
                                                    <div class="tab-pane fade" id="{{setting.settingId}}_customise_history" ng-include="'/customise_history_config.htm'">
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="tab-pane fade" id="healthAlerts{{setting.settingId}}">
                                    <div class="col-md-12 config-alerts pad-6" feature-toggle="43">
                                        <h6>Configure Alerts</h6>
                                        <div class="row config-row ">
                                            <div class="col-md-4">
                                                <label>
                                                    Alert Type&nbsp;&nbsp;<span class="glyphicon glyphicon-question-sign info-question-sign"
                                                                                tooltip=""
                                                                                tooltip-trigger
                                                                                tooltip-animation="false"
                                                                                tooltip-placement="top"></span>
                                                </label>
                                                <select class="form-control" ng-model="alert.alertType" convert-to-number required>
                                                    <option disabled selected value="">Select Alert Type</option>
                                                    <option value="0">Single Property Alert</option>
                                                    <option value="1">Cumulative Alert</option>
                                                </select>
                                            </div>
                                            <div class="col-md-4">
                                                <label>
                                                    Notification Type&nbsp;&nbsp;<span class="glyphicon glyphicon-question-sign info-question-sign"
                                                                                       tooltip=""
                                                                                       tooltip-trigger
                                                                                       tooltip-animation="false"
                                                                                       tooltip-placement="top"></span>
                                                </label>
                                                <select class="form-control" ng-model="alert.notificationType" convert-to-number required>
                                                    <option disabled selected value="">Select Notification Type</option>
                                                    <option value="2">Router Down</option>
                                                    <option value="6">Router Idle</option>
                                                </select>
                                            </div>
                                            <div class="col-md-4">
                                                <label ng-if="alert.alertType == 0 || !alert.alertType">
                                                    Delay&nbsp;&nbsp;<span class="glyphicon glyphicon-question-sign info-question-sign"
                                                                           tooltip="Alerts will be sent after selected time delay"
                                                                           tooltip-trigger
                                                                           tooltip-animation="false"
                                                                           tooltip-placement="top"></span>
                                                </label>
                                                <label ng-if="alert.alertType == 1">
                                                    Select time of day&nbsp;&nbsp;<span class="glyphicon glyphicon-question-sign info-question-sign"
                                                                                        tooltip="Alerts will be sent on next day at selected time"
                                                                                        tooltip-trigger
                                                                                        tooltip-animation="false"
                                                                                        tooltip-placement="top"></span>
                                                </label>
                                                <select ng-model="alert.delay" placeholder="Select Time of day" class="form-control" convert-to-number required>
                                                    <option ng-if="alert.alertType == 0 || !alert.alertType" disabled selected value="">Select Delay Time</option>
                                                    <option ng-if="alert.alertType == 1" disabled selected value="">Select time of day</option>
                                                    <option ng-repeat="interval in intervals track by $index" ng-value="interval.value" ng-selected="interval.value == (alert.delay * 30)">{{interval.name + ' Hours'}}</option>
                                                </select>
                                            </div>
                                        </div>
                                        <div class="row config-row">
                                            <div class="col-md-4">
                                                <label>
                                                    Email Id&nbsp;&nbsp;<span class="glyphicon glyphicon-question-sign info-question-sign"
                                                                              tooltip="Send email to. Put comma separated email ids"
                                                                              tooltip-trigger
                                                                              tooltip-animation="false"
                                                                              tooltip-placement="top"></span>
                                                </label>
                                                <input class="form-control" placeholder="<EMAIL>,<EMAIL>" ng-model="alert.sendEmailTo" type="text" name="" placeholder="Email Id" />
                                            </div>
                                            <div class="col-md-8">
                                                <label>
                                                    Email Body&nbsp;&nbsp;<span class="glyphicon glyphicon-question-sign info-question-sign"
                                                                                tooltip="Custom alert email content. Leave blank to set default content"
                                                                                tooltip-trigger
                                                                                tooltip-animation="false"
                                                                                tooltip-placement="top"></span>
                                                </label>
                                                <!--<input ng-model="alert.emailBody" class="form-control" type="text" name="" placeholder="Email Body(Content)" />-->

                                                <textarea ng-model="alert.emailBody" class="form-control" placeholder="Email Body(Content)"></textarea>
                                            </div>
                                        </div>
                                        <div class="row config-row" style="padding: 1.5rem;">
                                            <button class="btn btn-primary" ng-confirm-click='{"title":"Are you sure?", "textContent" : "Saving settings will affect behavior at all locations in setting. If you want only to update locations press apply instead save.", "type":"alert-warning", "ok" : "Ok", "cancel" : "Cancel"}' ng-click="saveHealthAlerts(setting)">Save Alerts</button>
                                        </div>
                                    </div>
                                    <div class="col-md-12 pad-6">
                                        <div class="seperator"></div>
                                    </div>
                                    <div ng-if="setting.healthAlerts.length > 0" class="col-md-12 config-alerts pad-6" feature-toggle="43">
                                        <table class="table table-striped">
                                            <thead>
                                                <tr>
                                                    <th></th>
                                                    <th>Alert Type</th>
                                                    <th>Notication Type</th>
                                                    <th>Delay (min)</th>
                                                    <th>Email Id</th>
                                                    <th>Email Body</th>
                                                    <th></th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <tr ng-repeat="row in setting.healthAlerts track by $index">
                                                    <th scope="row">{{$index + 1}}</th>
                                                    <td>{{row.alertType == 0 ? 'Single Property Alert':'Cumulative Alert'}}</td>
                                                    <td>{{row.notificationType == 2 ? 'Router Down':'Router Idle'}}</td>
                                                    <td>{{row.delay * 30}}</td>
                                                    <td><span ng-repeat="email in row.sendEmailTo">{{email}}</br></span></td>
                                                    <td>{{row.emailBody}}</td>
                                                    <td>
                                                        <span style="cursor:pointer;" class="glyphicon glyphicon-edit" ng-click="editHealthAlert(row)"></span>
                                                        <span style="cursor:pointer;" class="glyphicon glyphicon-trash" ng-click="deleteHealthAlert(setting, row.healthAlertId)"></span>
                                                    </td>
                                                </tr>
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="select-filters">
                <div class="row">
                    <div class="col-md-2 no-pad">Go to settings page:</div>
                        <!--<div class="col-md-5 no-pad locations-selector">
                        <div class="ui-select-input-group">
                            <div class="cell location">
                                <ui-select multiple
                                           ng-model="data.settings[$index].nases"
                                           theme="select2"
                                           close-on-select="false"
                                           reset-search-input="false"
                                           remove-selected="true">
                                    <ui-select-match allow-clear="true" placeholder="Search or Enter Location">
                                        {{$item.storeName}}
                                    </ui-select-match>
                                    <ui-select-choices position="down" repeat="router.nasid as router in data.settings[$index].nasDetails |
                                                   propsFilter: {nasid: $select.search, storeName: $select.search} |
                                                   limitTo:data.settings[$index].nasDetails"
                                                       select-with-pagination
                                                       choices-paging-list="data.settings[$index].nasDetails"
                                                       search-term="$select.search"
                                                       current-items="data.settings[$index].nasDetails.length"
                                                       infinite-scroll-distance="2"
                                                       page-number="1"
                                                       page-size="100"
                                                       search-funcation="$parent.searchLocations(theDirFn)"
                                                       refresh="directiveFn()"
                                                       refrsh-delay="1000">
                                        <span ng-bind-html="router.storeName | highlight: $select.search"></span>
                                        <span ng-bind-html="router.city | highlight: $select.search"></span>
                                        <span ng-bind-html="router.state | highlight: $select.search"></span>
                                        <span ng-bind-html="router.nasid | highlight: $select.search"></span>
                                    </ui-select-choices>
                                </ui-select>
                                <span class="glyphicon glyphicon-refresh glyphicon-refresh-animate" style="display:none;"></span>
                            </div>
                        </div>
                        <!--
                        <div class="selects">
                            <selectize config="multipleLocationSelectorConfig"
                                       options="data.settings[$index].nasDetails"
                                       ng-model="data.settings[$index].nases"
                                       ng-change="checkForSelectAll($index)"
                                       class="multiple-location select-loaction"
                                       title="Select multiple locations, type to search"></selectize>
                        </div>
                    </div>-->
                    <div class="col-md-5 right-align no-pad pull-right">
                        <!-- <button class="btn btn-primary" ng-click="applySettingsToLocations($index)">Apply</button>
                        <button ng-if="featureEnabled(44) == 1" class="btn btn-primary" ng-click="applySettingsToLocations($index, true)">Force Apply</button>-->
                        <button ng-if="featureEnabled(44) == 1" class="btn btn-primary" ng-click="gotoSettingsPage($index)">Settings page</button>
                    </div>

                </div>
            </div>


            <div ng-if="featureEnabled(44) == 1" class="dropdown setting-menu setting-menu-{{$index}}">
                <button class="dropdown-toggle menu-btn" type="button" id="addGroupBtn" data-toggle="dropdown" aria-haspopup="true" aria-expanded="true"><span class="glyphicon glyphicon-menu-hamburger"></span></button>
                <div class="dropdown-menu dropdown-menu-right" id="popoverdiv" ng-click="$event.stopPropagation();">
                    <ul>
                        <li>
                            <button title="Delete Setting" class="btn-close" ng-confirm-click='{"title":"Are you sure want to delete ?", "textContent" : "Deleting setting will be removed from all your locations.", "type":"alert-danger", "ok" : "Ok", "cancel" : "Cancel"}' ng-click="deleteSettings($index)"><span class="glyphicon glyphicon-trash"></span></button>
                        </li>
                        <li>
                            <button title="Transfer Setting" class="transfer-input" ng-click="transferSettingToggle($index)">Transfer Setting</button>
                            <div class="transfer-input display-none"><input type="text" placeholder="Transfer To" ng-model="data.transferTo" ng-keypress="transferSetting($event, $index)" /></div>
                        </li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>

<script type="text/ng-template" id="/basic_config.htm">
    <div class="col-md-12">
        <div class="row">
            <div class="col-md-12 config-row">
                <div class="col-md-12">
                    <div class="col-md-6"></div>
                    <div class="col-md-6 form-inline right-align">

                        <select class="form-control" style="width:inherit;"
                                ng-options="option.groupName for option in setting.userGroups track by option.groupId"
                                ng-model="setting.selectedGroup"
                                ng-change="fetchBasicConfiguration(setting)"></select>
                        <div class="dropdown add-new-group" ng-if="setting.selectedGroup.groupId > 0">
                            <button title="Edit Group" class="btn btn-primary b1 dropdown-toggle" type="button" data-toggle="dropdown" aria-haspopup="true" aria-expanded="true">
                                <span class="glyphicon glyphicon-pencil"></span>
                            </button>
                            <div class="dropdown-menu dropdown-menu-right" id="popoverdiv" ng-click="$event.stopPropagation();">
                                <div ng-data="{{ setting.selectedGroup.values }}" class="form-group-textfield">
                                    <input id="usergroup" type="text" ng-model="setting.selectedGroup.groupName" placeholder="Group Name" class="form-control groupname" />
                                    <button class="btn btn-primary b1" ng-click="updateGroup(setting, setting.selectedGroup, { delete: true })"><span class="glyphicon glyphicon-trash"></span></button>
                                </div>
                                <legend>Members <button title="Add Members" ng-click="setting.selectedGroup.values.unshift({mobile:''})" type="button" class="btn btn-primary-green g1">+</button></legend>
                                <div ng-repeat="user in setting.selectedGroup.values track by $index" class="form-inline">
                                    <div class="form-group-textfield">
                                        <input type="text" ng-model="user.mobile" placeholder="Mobile/Passport No" />
                                    </div>
                                    <div class="form-group-textfield">
                                        <input type="text" ng-model="user.name" placeholder="Name" />
                                    </div>
                                    <button class="btn btn-primary-b1" ng-click="setting.selectedGroup.values.splice(setting.selectedGroup.values.indexOf(user), 1)">
                                        x
                                    </button>
                                </div>
                                <div class="pull-right footer-btn-group" id="footer-btn-group">
                                    <button class="btn btn-primary" ng-click="updateGroup(setting, setting.selectedGroup)">Done</button>
                                </div>
                            </div>
                        </div>

                        <div class="dropdown add-new-group">
                            <button title="Add Group" class="btn btn-primary b1 dropdown-toggle" type="button" id="addGroupBtn" data-toggle="dropdown" aria-haspopup="true" aria-expanded="true">+</button>
                            <div class="dropdown-menu dropdown-menu-right" id="popoverdiv" ng-click="$event.stopPropagation();">
                                <div ng-data="{{ group.values }}" class="form-group-textfield">
                                    <input id="usergroup" type="text" ng-model="group.groupName" placeholder="Group Name" class="form-control" />
                                </div>
                                <legend>Members <button ng-click="group.values.unshift({mobile:''})" type="button" class="btn btn-primary-green g1">+</button></legend>
                                <div ng-repeat="user in group.values track by $index" class="form-inline">
                                    <div class="form-group-textfield">
                                        <input type="text" ng-model="user.mobile" placeholder="Mobile/Passport No" />
                                    </div>
                                    <div class="form-group-textfield">
                                        <input type="text" ng-model="user.name" placeholder="Name" />
                                    </div>
                                    <button class="btn btn-primary-b1" ng-click="group.values.splice(group.values.indexOf(user), 1)">
                                        x
                                    </button>
                                </div>
                                <div class="pull-right footer-btn-group" id="footer-btn-group">
                                    <button class="btn btn-primary" ng-click="createNewGroup(setting)">Done</button>
                                </div>
                            </div>
                        </div>

                    </div>
                </div>
            </div>
        </div>
    </div>
    <div ng-if="setting.userGroupBasicConfiguration">
        <div class="col-md-12">
            <div class="row">
                <div class="col-md-12 config-row">
                    <div class="col-md-6" feature-toggle="53">
                        <div class=" col-md-12">
                            Maximum data usage/ session&nbsp;&nbsp;<span class="glyphicon glyphicon-question-sign info-question-sign"
                                                                         tooltip="Maximum data usage per session at chosen locations can not exceed {{convertDataFormate(setting.userGroupBasicConfiguration.DATA_USAGE_PER_SESSION.value)}}"
                                                                         tooltip-trigger
                                                                         tooltip-animation="false"
                                                                         tooltip-placement="top"></span>
                        </div>
                        <div class="col-md-12">
                            <input type="hidden" name="" value="{{setting.userGroupBasicConfiguration.DATA_USAGE_PER_SESSION.value}}" />
                            <rzslider tooltip="Maximum data usage per session at chosen locations can not exceed {{convertDataFormate(setting.userGroupBasicConfiguration.DATA_USAGE_PER_SESSION.value)}}"
                                      tooltip-trigger
                                      tooltip-animation="false"
                                      tooltip-placement="top"
                                      rz-slider-model="setting.userGroupBasicConfiguration.DATA_USAGE_PER_SESSION.value"
                                      rz-slider-options="{showSelectionBar: true,floor: 0,ceil:10485760,hideLimitLabels: true, step: 1024*5, translate:translateDataValue}"></rzslider>
                        </div>
                    </div>
                    <div class="col-md-6" feature-toggle="10">
                        <div class="col-md-12">
                            Maximum data usage/ month&nbsp;&nbsp;<span class="glyphicon glyphicon-question-sign info-question-sign"
                                                                       tooltip="Maximum data usage per month at chosen locations can not exceed {{convertDataFormate(setting.userGroupBasicConfiguration.DATA_USAGE_CONTROL_MONTH.value)}}
                                                                       tooltip-trigger
                                                                       tooltip-animation="false"
                                                                       tooltip-placement="top"></span>
                        </div>
                        <div class="col-md-12">
                            <input type="hidden" name="" value="{{setting.userGroupBasicConfiguration.DATA_USAGE_CONTROL_MONTH.value}}" />
                            <rzslider tooltip="Maximum data usage per month at chosen locations can not exceed {{convertDataFormate(setting.userGroupBasicConfiguration.DATA_USAGE_CONTROL_MONTH.value)}}"
                                      tooltip-trigger
                                      tooltip-animation="false"
                                      tooltip-placement="top"
                                      rz-slider-model="setting.userGroupBasicConfiguration.DATA_USAGE_CONTROL_MONTH.value"
                                      rz-slider-options="{showSelectionBar: true,floor: 0,ceil:104857600,hideLimitLabels: true, step: 1024*5, translate:translateDataValue}"></rzslider>
                        </div>
                    </div>
                </div>
                <!-- This is radius configuration part-->
                <div class="col-md-12 config-row">
                    <div class="col-md-6" feature-toggle="24">
                        <div class="col-md-12">
                            Maximum Data Usage / Day&nbsp;&nbsp;<span class="glyphicon glyphicon-question-sign info-question-sign"
                                                                      tooltip="Download bandwidth after data exhausted at chosen locations can not exceed {{convertDataFormate(setting.userGroupBasicConfiguration.DOWNLOAD_BANDWIDTH_AFTER_EXHAUSTED.value)}}"
                                                                      tooltip-trigger
                                                                      tooltip-animation="false"
                                                                      tooltip-placement="top"></span>
                        </div>
                        <div class="col-md-12">
                            <input type="hidden" name="" value="{{setting.userGroupBasicConfiguration.CHILLISPOT_MAX_TOTAL_OCTETS.value}}" />
                            <rzslider tooltip="Download bandwidth after data exhausted at chosen locations can not exceed {{convertDataFormate(setting.userGroupBasicConfiguration.CHILLISPOT_MAX_TOTAL_OCTETS.value)}}"
                                      tooltip-trigger
                                      tooltip-animation="false"
                                      tooltip-placement="top"
                                      rz-slider-model="setting.userGroupBasicConfiguration.CHILLISPOT_MAX_TOTAL_OCTETS.value"
                                      rz-slider-options="{showSelectionBar: true,floor: 0,ceil:10485760,hideLimitLabels: true, step: 1024*5, translate:translateDataValue}"></rzslider>
                        </div>
                    </div>
                </div>

                <div class="col-md-12 config-row">
                    <!-- This is radius configuration part-->
                    <div class="col-md-6" feature-toggle="25">
                        <div class="col-md-12">
                            <label>
                                Session Timeout&nbsp;&nbsp;<span class="glyphicon glyphicon-question-sign info-question-sign"
                                                                 tooltip="Session Timeout"
                                                                 tooltip-trigger
                                                                 tooltip-animation="false"
                                                                 tooltip-placement="top"></span>
                            </label>
                        </div>
                        <div class="col-md-12">
                            <select ng-model="setting.userGroupBasicConfiguration.SESSION_TIMEOUT.value" class="form-control">
                                <option value="600">10 Minutes</option>
                                <option value="900">15 Minutes</option>
                                <option value="1200">20 Minutes</option>
                                <option value="1800">Half an hour</option>
                                <option value="3600">1 hour</option>
                                <option value="7200">2 hours</option>
                                <option value="10800">3 hours</option>
                                <option value="14400">4 hours</option>
                                <option value="21600">6 hours</option>
                                <option value="43200">12 hours</option>
                                <option value="86400">24 hours</option>
                                <option value="259200">3 Days</option>
                                <option value="604800">1 Week</option>
                                <option value="2419200">4 Weeks</option>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-6" feature-toggle="25">
                        <div class="col-md-12">
                            <label>
                                No of sessions in a day&nbsp;&nbsp;<span class="glyphicon glyphicon-question-sign info-question-sign"
                                                                         tooltip="Select multiple in case login has to be allowed after session timeout"
                                                                         tooltip-trigger
                                                                         tooltip-animation="false"
                                                                         tooltip-placement="top"></span>
                            </label>
                        </div>
                        <div class="col-md-12">
                            <select ng-model="setting.userGroupBasicConfiguration.NO_OF_SESSIONS.value" class="form-control">
                                <option value="1">Single</option>
                                <option value="10">Multiple</option>
                            </select>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-12 pad-6">
            <div class="seperator"></div>
        </div>
        <div class="col-md-12 advance-config-row" style="margin-top:2rem">
            <div class="col-md-12 config-row">
                <div class="col-md-6" feature-toggle="40">
                    <div class="col-md-12">
                        <label>
                            Hide Questions&nbsp;&nbsp;<span class="glyphicon glyphicon-question-sign info-question-sign"
                                                            tooltip="Download bandwidth after data exhausted at chosen locations can not exceed {{convertDataFormate(slider.value)}}"
                                                            tooltip-trigger
                                                            tooltip-animation="false"
                                                            tooltip-placement="top"></span>
                        </label>
                    </div>
                    <div class="col-md-12">
                        <select class="form-control" ng-model="setting.advanceConfiguration.HIDE_QUESTION.parameters[0]">
                            <option value="0">Ask all questions one by one</option>
                            <option value="1">Hide all questions</option>
                            <option value="2">Ask email and demographic questions only</option>
                            <option value="3">Ask only email</option>
                            <option value="4">Ask non-PDO demographic questions</option>
                        </select>
                    </div>
                </div>
                <div class="col-md-6" feature-toggle="42">
                    <div class=" col-md-12">
                        <label>
                            Number of devices per user&nbsp;&nbsp;<span class="glyphicon glyphicon-question-sign info-question-sign"
                                                                        tooltip="Download bandwidth after data exhausted at chosen locations can not exceed {{convertDataFormate(slider.value)}}"
                                                                        tooltip-trigger
                                                                        tooltip-animation="false"
                                                                        tooltip-placement="top"></span>
                        </label>
                    </div>
                    <div class="col-md-12">
                        <input class="form-control" type="text" placeholder="5" ng-model="setting.advanceConfiguration.NO_OF_DEVICES_PER_USER.parameters[0]" />
                    </div>


                </div>
            </div>
        </div>

        <div class="col-md-12 pull-right save-row">
            <button class="btn btn-primary"
                    ng-confirm-click='{"title":"Are you sure?", "textContent" : "Saving settings will affect behavior at all locations in setting. If you want only to update locations press apply instead save.", "type":"alert-warning", "ok" : "Ok", "cancel" : "Cancel"}'
                    ng-click="saveCustomisation(setting)">
                Save
            </button>
        </div>
    </div>
</script>

<script type="text/ng-template" id="/basic_config_history.htm">

    <div class="col-md-12">
        <div class="row">
            <div class="col-md-12 config-row">
                <div class="col-md-6">
                    Updated by {{setting.userGroupBasicConfiguration.version.key}} on {{setting.userGroupBasicConfiguration.version.value}}
                </div>
                <div class="col-md-6 form-inline">
                    <select disabled class=" form-control" style="width:inherit;"
                            ng-options="option.groupName for option in setting.userGroups track by option.groupId"
                            ng-model="setting.selectedGroup" disabled></select>
                </div>
            </div>
        </div>
    </div>
    <div ng-if="setting.userGroupBasicConfiguration">
        <div class="col-md-12">
            <div class="row">
                <div class="col-md-12 config-row">
                    <div class="col-md-6" feature-toggle="53">
                        <div class=" col-md-12">
                            Maximum data usage/ session&nbsp;&nbsp;<span class="glyphicon glyphicon-question-sign info-question-sign"
                                                                         tooltip="Maximum data usage per session at chosen locations can not exceed {{convertDataFormate(setting.userGroupBasicConfiguration.DATA_USAGE_PER_SESSION.value)}}"
                                                                         tooltip-trigger
                                                                         tooltip-animation="false"
                                                                         tooltip-placement="top"></span>
                        </div>
                        <div class="col-md-12">
                            <input type="hidden" name="" value="{{setting.userGroupBasicConfiguration.DATA_USAGE_PER_SESSION.value}}" />
                            <rzslider tooltip="Maximum data usage per session at chosen locations can not exceed {{convertDataFormate(setting.userGroupBasicConfiguration.DATA_USAGE_PER_SESSION.value)}}"
                                      tooltip-trigger
                                      tooltip-animation="false"
                                      tooltip-placement="top"
                                      rz-slider-model="setting.userGroupBasicConfiguration.DATA_USAGE_PER_SESSION.value"
                                      rz-slider-options="{disabled: true, showSelectionBar: true,floor: 0,ceil:10485760,hideLimitLabels: true, step: 1024*5, translate:translateDataValue}"></rzslider>
                        </div>
                    </div>
                    <div class="col-md-6" feature-toggle="10">
                        <div class=" col-md-12">
                            Maximum data usage/ month&nbsp;&nbsp;<span class="glyphicon glyphicon-question-sign info-question-sign"
                                                                       tooltip="Maximum data usage per month at chosen locations can not exceed {{convertDataFormate(setting.userGroupBasicConfiguration.DATA_USAGE_CONTROL_MONTH.value)}}
                                                                       tooltip-trigger
                                                                       tooltip-animation="false"
                                                                       tooltip-placement="top"></span>
                        </div>
                        <div class="col-md-12">
                            <input type="hidden" name="" value="{{setting.userGroupBasicConfiguration.DATA_USAGE_CONTROL_MONTH.value}}" />
                            <rzslider tooltip="Maximum data usage per month at chosen locations can not exceed {{convertDataFormate(setting.userGroupBasicConfiguration.DATA_USAGE_CONTROL_MONTH.value)}}"
                                      tooltip-trigger
                                      tooltip-animation="false"
                                      tooltip-placement="top"
                                      rz-slider-model="setting.userGroupBasicConfiguration.DATA_USAGE_CONTROL_MONTH.value"
                                      rz-slider-options="{disabled: true, showSelectionBar: true,floor: 0,ceil:104857600,hideLimitLabels: true, step: 1024*5, translate:translateDataValue}"></rzslider>
                        </div>
                    </div>
                </div>
                <!-- This is radius configuration part-->
                <div class="col-md-12 config-row" feature-toggle="5">
                    <div class="col-md-6">
                        <div class="col-md-12">
                            Maximum upload bandwidth&nbsp;&nbsp;<span class="glyphicon glyphicon-question-sign info-question-sign"
                                                                      tooltip="Maximum upload bandwidth at chosen locations can not exceed {{convertDataFormate(setting.userGroupBasicConfiguration.CHILLISPOT_BANDWIDTH_MAX_UP.value)}}"
                                                                      tooltip-trigger
                                                                      tooltip-animation="false"
                                                                      tooltip-placement="top"></span>
                        </div>
                        <div class="col-md-12">
                            <input type="hidden" name="" value="{{setting.userGroupBasicConfiguration.CHILLISPOT_BANDWIDTH_MAX_UP.value}}" />
                            <rzslider tooltip="Maximum upload bandwidth at chosen locations can not exceed {{convertDataFormate(setting.userGroupBasicConfiguration.CHILLISPOT_BANDWIDTH_MAX_UP.value)}}"
                                      tooltip-trigger
                                      tooltip-animation="false"
                                      tooltip-placement="top"
                                      rz-slider-model="setting.userGroupBasicConfiguration.CHILLISPOT_BANDWIDTH_MAX_UP.value"
                                      rz-slider-options="{disabled: true, showSelectionBar: true,floor: 0,ceil:100000,hideLimitLabels: true,translate:translateSpeedValue}"></rzslider>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="col-md-12">
                            Maximum download bandwidth&nbsp;&nbsp;<span class="glyphicon glyphicon-question-sign info-question-sign"
                                                                        tooltip="Maximum download bandwidth at chosen locations can not exceed {{convertDataFormate(setting.userGroupBasicConfiguration.CHILLISPOT_BANDWIDTH_MAX_DOWN.value)}}"
                                                                        tooltip-trigger
                                                                        tooltip-animation="false"
                                                                        tooltip-placement="top"></span>
                        </div>
                        <div class="col-md-12">
                            <input type="hidden" name="" value="{{setting.userGroupBasicConfiguration.CHILLISPOT_BANDWIDTH_MAX_DOWN.value}}" />
                            <rzslider tooltip="Maximum download bandwidth at chosen locations can not exceed {{convertDataFormate(setting.userGroupBasicConfiguration.CHILLISPOT_BANDWIDTH_MAX_DOWN.value)}}"
                                      tooltip-trigger
                                      tooltip-animation="false"
                                      tooltip-placement="top"
                                      rz-slider-model="setting.userGroupBasicConfiguration.CHILLISPOT_BANDWIDTH_MAX_DOWN.value"
                                      rz-slider-options="{disabled: true, showSelectionBar: true,floor: 0,ceil:100000,hideLimitLabels: true,translate:translateSpeedValue}"></rzslider>
                        </div>
                    </div>
                </div>
                <div class="col-md-12 config-row" feature-toggle="11">
                    <div class="col-md-6">
                        <div class="col-md-12">
                            Upload bandwidth after data exhausted&nbsp;&nbsp;<span class="glyphicon glyphicon-question-sign info-question-sign"
                                                                                   tooltip="Upload bandwidth after data exhausted at chosen locations can not exceed {{convertDataFormate(setting.userGroupBasicConfiguration.UPLOAD_BANDWIDTH_AFTER_EXHAUSTED.value)}}"
                                                                                   tooltip-trigger
                                                                                   tooltip-animation="false"
                                                                                   tooltip-placement="top"></span>
                        </div>
                        <div class="col-md-12">
                            <input type="hidden" name="" value="{{setting.userGroupBasicConfiguration.UPLOAD_BANDWIDTH_AFTER_EXHAUSTED.value}}" />
                            <rzslider tooltip="Upload bandwidth after data exhausted at chosen locations can not exceed {{convertDataFormate(setting.userGroupBasicConfiguration.UPLOAD_BANDWIDTH_AFTER_EXHAUSTED.value)}}"
                                      tooltip-trigger
                                      tooltip-animation="false"
                                      tooltip-placement="top"
                                      rz-slider-model="setting.userGroupBasicConfiguration.UPLOAD_BANDWIDTH_AFTER_EXHAUSTED.value"
                                      rz-slider-options="{disabled: true, showSelectionBar: true,floor: 0,ceil:100000,hideLimitLabels: true,translate:translateSpeedValue}"></rzslider>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="col-md-12">
                            Download bandwidth after data exhausted&nbsp;&nbsp;<span class="glyphicon glyphicon-question-sign info-question-sign"
                                                                                     tooltip="Download bandwidth after data exhausted at chosen locations can not exceed {{convertDataFormate(setting.userGroupBasicConfiguration.DOWNLOAD_BANDWIDTH_AFTER_EXHAUSTED.value)}}"
                                                                                     tooltip-trigger
                                                                                     tooltip-animation="false"
                                                                                     tooltip-placement="top"></span>
                        </div>
                        <div class="col-md-12">
                            <input type="hidden" name="" value="{{setting.userGroupBasicConfiguration.DOWNLOAD_BANDWIDTH_AFTER_EXHAUSTED.value}}" />
                            <rzslider tooltip="Download bandwidth after data exhausted at chosen locations can not exceed {{convertDataFormate(setting.userGroupBasicConfiguration.DOWNLOAD_BANDWIDTH_AFTER_EXHAUSTED.value)}}"
                                      tooltip-trigger
                                      tooltip-animation="false"
                                      tooltip-placement="top"
                                      rz-slider-model="setting.userGroupBasicConfiguration.DOWNLOAD_BANDWIDTH_AFTER_EXHAUSTED.value"
                                      rz-slider-options="{disabled: true, showSelectionBar: true,floor: 0,ceil:100000,hideLimitLabels: true,translate:translateSpeedValue}"></rzslider>
                        </div>
                    </div>
                </div>

                <div class="col-md-12 config-row">

                    <div class="col-md-6" feature-toggle="24">
                        <div class="col-md-12">
                            Maximum Data Usage / Day&nbsp;&nbsp;<span class="glyphicon glyphicon-question-sign info-question-sign"
                                                                      tooltip="Download bandwidth after data exhausted at chosen locations can not exceed {{convertDataFormate(setting.userGroupBasicConfiguration.DOWNLOAD_BANDWIDTH_AFTER_EXHAUSTED.value)}}"
                                                                      tooltip-trigger
                                                                      tooltip-animation="false"
                                                                      tooltip-placement="top"></span>
                        </div>
                        <div class="col-md-12">
                            <input type="hidden" name="" value="{{setting.userGroupBasicConfiguration.CHILLISPOT_MAX_TOTAL_OCTETS.value}}" />
                            <rzslider tooltip="Download bandwidth after data exhausted at chosen locations can not exceed {{convertDataFormate(setting.userGroupBasicConfiguration.CHILLISPOT_MAX_TOTAL_OCTETS.value)}}"
                                      tooltip-trigger
                                      tooltip-animation="false"
                                      tooltip-placement="top"
                                      rz-slider-model="setting.userGroupBasicConfiguration.CHILLISPOT_MAX_TOTAL_OCTETS.value"
                                      rz-slider-options="{disabled: true, showSelectionBar: true,floor: 0,ceil:10485760,hideLimitLabels: true, step: 1024*5, translate:translateDataValue}"></rzslider>
                        </div>
                    </div>

                    <div class="col-md-6" feature-toggle="25">
                        <div class="col-md-12">
                            <label>
                                Session Timeout&nbsp;&nbsp;<span class="glyphicon glyphicon-question-sign info-question-sign"
                                                                 tooltip="Session Timeout"
                                                                 tooltip-trigger
                                                                 tooltip-animation="false"
                                                                 tooltip-placement="top"></span>
                            </label>
                        </div>
                        <div class="col-md-12">
                            <select ng-model="setting.userGroupBasicConfiguration.SESSION_TIMEOUT.value" class="form-control" disabled>
                                <option value="600">10 Minutes</option>
                                <option value="900">15 Minutes</option>
                                <option value="1200">20 Minutes</option>
                                <option value="1800">Half an hour</option>
                                <option value="3600">1 hour</option>
                                <option value="7200">2 hours</option>
                                <option value="10800">3 hours</option>
                                <option value="14400">4 hours</option>
                                <option value="21600">6 hours</option>
                                <option value="43200">12 hours</option>
                                <option value="86400">24 hours</option>
                                <option value="259200">3 Days</option>
                                <option value="604800">1 Week</option>
                                <option value="2419200">4 Weeks</option>
                            </select>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</script>

<script type="text/ng-template" id="/advance_config.htm">

    <div class="col-md-12 config-alerts pad-6" feature-toggle="32">
        <h6>Configure Authentication Process</h6>
        <div class="row config-row">
            <div class="col-md-6">
                <label>
                    Select Athentication Type&nbsp;&nbsp;<span class="glyphicon glyphicon-question-sign info-question-sign"
                                                               tooltip="Select primary authentication type"
                                                               tooltip-trigger
                                                               tooltip-animation="false"
                                                               tooltip-placement="top"></span>
                </label>
                <select class="form-control" ng-model="setting.advanceConfiguration.AUTH_TYPE.parameters[0]">
                    <option value="0">Phone Number Authentication</option>
                    <option value="2">National ID Authentication</option>
                    <option value="3">Only Registered Phone Number</option>
                    <option value="4">Automatic login after first time</option>
                    <option value="5">Phone Number or National ID</option>
                    <option value="6">Registered Phone Number or National ID</option>
                    <option value="7">National ID or Phone Number</option>
                    <option value="8">Last Name and Room Number</option>
                    <option value="9">Only Access Code Validation</option>
                    <option value="11">Social Login</option>
                    <option value="12">Data Voucher Based Login</option>
                    <option value="13">Data Voucher Without OTP</option>
                    <option value="14">Phone Number or Data Voucher Without OTP</option>
                    <option value="16">Phone Number Without OTP</option>
                    <option value="15">WANI Login</option>
                </select>
            </div>

            <div class="col-md-6">
                <label>
                    Guest Wi Fi Mode&nbsp;&nbsp;<span class="glyphicon glyphicon-question-sign info-question-sign"
                                                      tooltip="Select authentication mode for guest users"
                                                      tooltip-trigger
                                                      tooltip-animation="false"
                                                      tooltip-placement="top"></span>
                </label>
                <select class="form-control" ng-init="setting.advanceConfiguration.AUTH_TYPE.parameters[7] = setting.advanceConfiguration.AUTH_TYPE.parameters[7] ? setting.advanceConfiguration.AUTH_TYPE.parameters[7] : -1" ng-model="setting.advanceConfiguration.AUTH_TYPE.parameters[7]">
                    <option value="-1">Disable</option>
                    <option value="0">Phone Number Authentication</option>
                    <option value="2">National ID Authentication</option>
                    <option value="3">Only Registered Phone Number</option>
                    <option value="4">Automatic login after first time</option>
                    <option value="5">Phone Number or National ID</option>
                    <option value="6">Registered Phone Number or National ID</option>
                    <option value="7">National ID or Phone Number</option>
                    <option value="10">Facebook Authentication</option>
                </select>
            </div>
        </div>

        <div class="row config-row" ng-show="setting.advanceConfiguration.AUTH_TYPE.parameters[0] == 12">
            <div class="col-md-6">
                <label>
                    Voucher code placeholder&nbsp;&nbsp;<span class="glyphicon glyphicon-question-sign info-question-sign"
                                                              tooltip-trigger
                                                              tooltip-animation="false"
                                                              tooltip-placement="top"></span>
                </label>
                <input type="text" name="" class="form-control" placeholder="Enter voucher code" value="Enter voucher code" ng-model="setting.advanceConfiguration.DATA_VOUCHER_DETAILS.parameters[0]" />
            </div>
            <div class="col-md-3">
                <label>
                    Free data plan(MB)&nbsp;&nbsp;<span class="glyphicon glyphicon-question-sign info-question-sign"
                                                        tooltip-trigger
                                                        tooltip-animation="false"
                                                        tooltip-placement="top"></span>
                </label>
                <input type="text" name="" class="form-control" placeholder="0" value="0" ng-model="setting.advanceConfiguration.DATA_VOUCHER_DETAILS.parameters[1]" />
            </div>
            <div class="col-md-3">
                <label>
                    Free time(mins)&nbsp;&nbsp;<span class="glyphicon glyphicon-question-sign info-question-sign"
                                                     tooltip-trigger
                                                     tooltip-animation="false"
                                                     tooltip-placement="top"></span>
                </label>
                <input type="text" name="" class="form-control" placeholder="0" value="0" ng-model="setting.advanceConfiguration.DATA_VOUCHER_DETAILS.parameters[2]" />
            </div>
        </div>
    </div>

    <div class="col-md-12 pad-6">
        <div class="seperator"></div>
    </div>

    <div class="col-md-12 config-alerts pad-6" ng-if="isConfigAdminUser">
        <!--<h6>Auto login configuration</h6>-->
        <div class="row config-row">
            <div class="col-md-6">
                <label>
                    Auto login span&nbsp;&nbsp;<span class="glyphicon glyphicon-question-sign info-question-sign"
                                                     tooltip="Users will be autologin based on depending on this time period"
                                                     tooltip-trigger
                                                     tooltip-animation="false"
                                                     tooltip-placement="top"></span>
                </label>
                <select class="form-control" ng-model="setting.advanceConfiguration.AUTO_LOGIN_SPAN.parameters[0]">
                    <option value="0">0 Days</option>
                    <option value="1">1 Day</option>
                    <option value="30" selected>30 Days</option>
                </select>
            </div>

            <div class="col-md-6">
                <label>
                    Language Preference&nbsp;&nbsp;<span class="glyphicon glyphicon-question-sign info-question-sign"
                                                     tooltip="You can select langauges based on your preference for users.Default will be english!"
                                                     tooltip-trigger
                                                     tooltip-animation="false"
                                                     tooltip-placement="top"></span>
                </label>
                <select class="form-control" ng-model="setting.advanceConfiguration.LANGUAGE_PREFERENCE.parameters[0]">
                    <option value="en">English</option>
                    <option value="hi">Hindi</option>
                    <option value="te">Telugu</option>
                </select>
            </div>

        </div>
    </div>

    <div class="col-md-12 pad-6">
        <div class="seperator"></div>
    </div>



    <div class="col-md-12 config-alerts pad-6" feature-toggle="33">
        <h6>Facebook Share</h6>
        <div class="row config-row ">
            <div class="col-md-6">
                <label>
                    Facebook Page Link&nbsp;&nbsp;<span class="glyphicon glyphicon-question-sign info-question-sign"
                                                        tooltip="This facebook page will be shared if user share on facebook while connecting your wifi"
                                                        tooltip-trigger
                                                        tooltip-animation="false"
                                                        tooltip-placement="top"></span>
                </label>
                <input type="text" name="" class="form-control" value="" ng-model="setting.advanceConfiguration.FACEBOOK_PAGE.parameters[0]" placeholder="https://facebook.com/example" />
            </div>
            <div class="col-md-6">
                <label>
                    Mandatory Facebook Share&nbsp;&nbsp;<span class="glyphicon glyphicon-question-sign info-question-sign"
                                                              tooltip="Is facebook share mandatory?"
                                                              tooltip-trigger
                                                              tooltip-animation="false"
                                                              tooltip-placement="top"></span>
                </label>
                <select class="form-control" ng-model="setting.advanceConfiguration.FACEBOOK_CHECKIN.parameters[0]">
                    <option value="1">Yes</option>
                    <option value="0">No</option>
                </select>
            </div>
        </div>
    </div>

    <div class="col-md-12 pad-6">
        <div class="seperator"></div>
    </div>

    <div class="col-md-12 config-alerts pad-6" ng-if="isConfigAdminUser">
        <h6>Validate user login</h6>
        <div class="row config-row">
            <div class="col-md-6">
                <label>
                    Api&nbsp;&nbsp;<span class="glyphicon glyphicon-question-sign info-question-sign"
                                         tooltip-trigger
                                         tooltip-animation="false"
                                         tooltip-placement="top"></span>
                </label>
                <input type="text" name="" class="form-control" value="" ng-model="setting.advanceConfiguration.VALIDATE_USER_LOGIN.parameters[0]" placeholder="https://example.com/authorise_user" />
            </div>
            <div class="col-md-3">
                <label>
                    Method&nbsp;&nbsp;<span class="glyphicon glyphicon-question-sign info-question-sign"
                                            tooltip-trigger
                                            tooltip-animation="false"
                                            tooltip-placement="top"></span>
                </label>
                <select class="form-control" ng-model="setting.advanceConfiguration.VALIDATE_USER_LOGIN.parameters[1]">
                    <option value="0">POST</option>
                    <option value="1">GET</option>
                </select>
            </div>
            <div class="col-md-3">
                <label>
                    Payload&nbsp;&nbsp;<span class="glyphicon glyphicon-question-sign info-question-sign"
                                             tooltip-trigger
                                             tooltip-animation="false"
                                             tooltip-placement="top"></span>
                </label>
                <input type="text" name="" class="form-control" value="" ng-model="setting.advanceConfiguration.VALIDATE_USER_LOGIN.parameters[2]" />
            </div>
        </div>

        <div class="row config-row">
            <div class="col-md-6">
                <label>
                    Api Request&nbsp;&nbsp;<span class="glyphicon glyphicon-question-sign info-question-sign"
                                                 tooltip-trigger
                                                 tooltip-animation="false"
                                                 tooltip-placement="top"></span>
                </label>
                <div><i>{ 'mobile' : 'xxxxxx4567', 'token' : 'bafe2ece-eccd-48f3-bf82-153f92648905' }</i></div>
            </div>
            <div class="col-md-6">
                <label>
                    Expected Api Response&nbsp;&nbsp;<span class="glyphicon glyphicon-question-sign info-question-sign"
                                                           tooltip-trigger
                                                           tooltip-animation="false"
                                                           tooltip-placement="top"></span>
                </label>
                <div>
                    <p>Success response <i>{ 'status' : 'success' , 'message': 'Please grant access'}</i></p>
                    <p>Failure response <i>{ 'status' : 'failure' , 'message': 'Forbidden: Please contact manager'}</i></p>
                </div>

            </div>
        </div>
    </div>

    <div class="col-md-12 pad-6">
        <div class="seperator"></div>
    </div>
    <div class="col-md-12 config-alerts pad-6">
        <h6></h6>
        <div class="row config-row ">
            <div class="col-md-6" feature-toggle="37">
                <label>
                    Shine Plus&nbsp;&nbsp;<span class="glyphicon glyphicon-question-sign info-question-sign"
                                                tooltip="Shine plus allows users to connect seemlessly in subsequent logins"
                                                tooltip-trigger
                                                tooltip-animation="false"
                                                tooltip-placement="top"></span>
                </label>
                <select class="form-control" ng-model="setting.advanceConfiguration.SHINE_PLUS_ACTIVATION.parameters[0]">
                    <option value="1">Yes</option>
                    <option value="0">No</option>
                </select>
            </div>
            <div class="col-md-6" feature-toggle="46">
                <label>
                    Device Active&nbsp;&nbsp;<span class="glyphicon glyphicon-question-sign info-question-sign"
                                                   tooltip="Device Active"
                                                   tooltip-trigger
                                                   tooltip-animation="false"
                                                   tooltip-placement="top"></span>
                </label>
                <select class="form-control" ng-model="setting.advanceConfiguration.DEVICE_ACTIVATION.parameters[0]">
                    <option value="1">Yes</option>
                    <option value="0">No</option>
                </select>
            </div>

        </div>
    </div>
    

    <div class="col-md-12 pull-right">
        <button class="btn btn-primary" ng-confirm-click='{"title":"Are you sure?", "textContent" : "Saving settings will affect behavior at all locations in setting. If you want only to update locations press apply instead save.", "type":"alert-warning", "ok" : "Ok", "cancel" : "Cancel"}' ng-click="saveAdvanceConfig(setting)">Save</button>
    </div>

</script>

<script type="text/ng-template" id="/advance_config_history.htm">

    <div class="col-md-12 config-alerts pad-6" feature-toggle="32">
        <h6>Configure Authentication Mode</h6>
        <div class="row config-row">
            <div class="col-md-4">
                <label>
                    Select Athentication Type&nbsp;&nbsp;<span class="glyphicon glyphicon-question-sign info-question-sign"
                                                               tooltip="Select primary authentication type"
                                                               tooltip-trigger
                                                               tooltip-animation="false"
                                                               tooltip-placement="top"></span>
                </label>
                <select disabled class="form-control" ng-model="setting.advanceConfiguration.AUTH_TYPE.parameters[0]">
                    <option value="0">Phone Number Authentication</option>
                    <option value="2">National ID Authentication</option>
                    <option value="3">Only Registered Phone Number</option>
                    <option value="4">Automatic login after first time</option>
                    <option value="5">Phone Number or National ID</option>
                    <option value="6">Registered Phone Number or National ID</option>
                    <option value="7">National ID or Phone Number</option>
                    <option value="8">Last Name and Room Number</option>
                    <option value="9">Only Access Code Validation</option>
                    <option value="10">Facebook Authentication</option>
                    <option value="11">Social Login</option>
                    <option value="12">Data Voucher Based Login</option>
                    <option value="13">Data Voucher Without OTP</option>
                    <option value="14">Phone Number or Data Voucher Without OTP</option>
                    <option value="15">WANI Login</option>
                </select>
            </div>

            <div class="col-md-4">
                <label>
                    Guest Wi Fi Mode&nbsp;&nbsp;<span class="glyphicon glyphicon-question-sign info-question-sign"
                                                      tooltip="Select authentication mode for guest users"
                                                      tooltip-trigger
                                                      tooltip-animation="false"
                                                      tooltip-placement="top"></span>
                </label>
                <select disabled class="form-control" ng-init="setting.advanceConfiguration.AUTH_TYPE.parameters[7] = setting.advanceConfiguration.AUTH_TYPE.parameters[7] ? setting.advanceConfiguration.AUTH_TYPE.parameters[7] : -1" ng-model="setting.advanceConfiguration.AUTH_TYPE.parameters[7]">
                    <option value="-1">Disable</option>
                    <option value="0">Phone Number Authentication</option>
                    <option value="2">National ID Authentication</option>
                    <option value="3">Only Registered Phone Number</option>
                    <option value="4">Automatic login after first time</option>
                    <option value="5">Phone Number or National ID</option>
                    <option value="6">Registered Phone Number or National ID</option>
                    <option value="7">National ID or Phone Number</option>
                    <option value="10">Facebook Authentication</option>
                </select>
            </div>
        </div>
    </div>
    <div class="col-md-12 pad-6">
        <div class="seperator"></div>
    </div>
    <div class="col-md-12 config-alerts pad-6" feature-toggle="33">
        <h6>Facebook Share</h6>
        <div class="row config-row ">
            <div class="col-md-4">
                <label>
                    Facebook Page Link&nbsp;&nbsp;<span class="glyphicon glyphicon-question-sign info-question-sign"
                                                        tooltip="This facebook page will be shared if user share on facebook while connecting your wifi"
                                                        tooltip-trigger
                                                        tooltip-animation="false"
                                                        tooltip-placement="top"></span>
                </label>
                <input disabled type="text" name="" value="" title="setting.advanceConfiguration.FACEBOOK_PAGE.parameters[0]"
                       ng-model="setting.advanceConfiguration.FACEBOOK_PAGE.parameters[0]" placeholder="https://facebook.com/example.com" />
            </div>
            <div class="col-md-4">
                <label>
                    Mandatory Facebook Share&nbsp;&nbsp;<span class="glyphicon glyphicon-question-sign info-question-sign"
                                                              tooltip="Is facebook share mandatory?"
                                                              tooltip-trigger
                                                              tooltip-animation="false"
                                                              tooltip-placement="top"></span>
                </label>
                <select disabled class="form-control" ng-model="setting.advanceConfiguration.FACEBOOK_CHECKIN.parameters[0]">
                    <option value="1">Yes</option>
                    <option value="0">No</option>
                </select>
            </div>
        </div>
    </div>
    <div class="col-md-12 pad-6">
        <div class="seperator"></div>
    </div>
    <div class="col-md-12 config-alerts pad-6">
        <h6></h6>
        <div class="row config-row ">
            <div class="col-md-6" feature-toggle="37">
                <label>
                    Shine Plus&nbsp;&nbsp;<span class="glyphicon glyphicon-question-sign info-question-sign"
                                                tooltip="Shine plus allows users to connect seemlessly in subsequent logins"
                                                tooltip-trigger
                                                tooltip-animation="false"
                                                tooltip-placement="top"></span>
                </label>
                <select disabled class="form-control" ng-model="setting.advanceConfiguration.SHINE_PLUS_ACTIVATION.parameters[0]">
                    <option value="1">Yes</option>
                    <option value="0">No</option>
                </select>
            </div>
            <div class="col-md-6" feature-toggle="46">
                <label>
                    Device Active&nbsp;&nbsp;<span class="glyphicon glyphicon-question-sign info-question-sign"
                                                   tooltip="Device Active"
                                                   tooltip-trigger
                                                   tooltip-animation="false"
                                                   tooltip-placement="top"></span>
                </label>
                <select disabled class="form-control" ng-model="setting.advanceConfiguration.DEVICE_ACTIVATION.parameters[0]">
                    <option value="1">Yes</option>
                    <option value="0">No</option>
                </select>
            </div>

        </div>
    </div>

</script>

<script id="/customise_config.htm" type="text/ng-template">
    <div class="col-md-12 config-alerts pad-6" feature-toggle="2">
        <h6>Configure Landing Page</h6>
        <div class="row config-row ">
            <div class="col-md-6">
                <label>
                    Welcome Message&nbsp;&nbsp;<span class="glyphicon glyphicon-question-sign info-question-sign"
                                                     tooltip="This message will replace 'You are now connected' message in landing page"
                                                     tooltip-trigger
                                                     tooltip-animation="false"
                                                     tooltip-placement="top"></span>
                </label>
                <textarea class="form-control" ng-model="setting.advanceConfiguration.LANDING_PAGE_CONFIG.parameters[0]"></textarea>
            </div>
        </div>
        <div class="row config-row">
            <div class="col-md-6">
                <label>
                    Custom Landing Page (path)&nbsp;&nbsp;<span class="glyphicon glyphicon-question-sign info-question-sign"
                                                                tooltip="Set your custom landing page"
                                                                tooltip-trigger
                                                                tooltip-animation="false"
                                                                tooltip-placement="top"></span>
                </label>
                <input type="text" class="form-control" placeholder="www.example.com" ng-model="setting.userGroupBasicConfiguration.LANDING_PAGE.value"></input>
            </div>
        </div>

        <div class="col-md-12 no-pad">
            <div class="seperator"></div>
        </div>
    </div>


    <div class="col-md-12 config-alerts pad-6" feature-toggle="45">
        <h6>Splash Page</h6>
        <div class="row config-row ">
            <div class="col-md-6 uploader">

                <div id="uploaderZone{{setting.settingId}}">
                    <div class="fileList" id="fileList{{setting.settingId}}"></div>
                    <img id="splashImage{{setting.settingId}}" class="col-md-6 splashImage"
                         ng-src="{{setting.advanceConfiguration.ROUTER_SPLASH_IMAGE.parameters && setting.advanceConfiguration.ROUTER_SPLASH_IMAGE.parameters.length > 0 && setting.advanceConfiguration.ROUTER_SPLASH_IMAGE.parameters[0] ? setting.advanceConfiguration.ROUTER_SPLASH_IMAGE.parameters[0] : '/images/placeholder.png'}}" />
                    <div class="col-md-6">
                        <div>Drag &amp; Drop Splash image</div>
                        <div class="or">-or-</div>
                        <div class="browser">
                            <span>Click to open the file Browser</span>
                            <input type="file" class="btn btn-primary-green" name="file" multiple="multiple" title="Click to add Files">
                        </div>
                    </div>

                </div>
            </div>

            <div class="col-md-6">
                <label>
                    Splash page text&nbsp;&nbsp;<span class="glyphicon glyphicon-question-sign info-question-sign"
                                                      tooltip="Custom splash page text"
                                                      tooltip-trigger
                                                      tooltip-animation="false"
                                                      tooltip-placement="top"></span>
                </label>
                <input type="text" class="form-control" placeholder="You are about to enjoy free wifi" ng-model="setting.advanceConfiguration.ROUTER_SPLASH_IMAGE.parameters[1]"></input>
            </div>
        </div>

        <div class="col-md-12 no-pad">
            <div class="seperator"></div>
        </div>
    </div>



    <div class="col-md-12 config-alerts pad-6" feature-toggle="28">
        <h6>Header Image</h6>
        <div class="row config-row ">
            <div class="col-md-6 uploader">

                <div id="uploaderZone28{{setting.settingId}}">
                    <div class="fileList" id="fileList28{{setting.settingId}}"></div>
                    <img id="splashImage28{{setting.settingId}}" class="col-md-6 splashImage"
                         ng-src="{{setting.advanceConfiguration.LOGIN_PAGE_IMAGE.parameters && setting.advanceConfiguration.LOGIN_PAGE_IMAGE.parameters.length > 0 && setting.advanceConfiguration.LOGIN_PAGE_IMAGE.parameters[0] ? setting.advanceConfiguration.LOGIN_PAGE_IMAGE.parameters[0] : '/images/placeholder.png'}}" />
                    <div class="col-md-6">
                        <div>Drag &amp; Drop Splash image</div>
                        <div class="or">-or-</div>
                        <div class="browser">
                            <span>Click to open the file Browser</span>
                            <input type="file" class="btn btn-primary-green" name="file" multiple="multiple" title="Click to add Files">
                        </div>
                    </div>

                </div>
            </div>


        </div>

        <div class="col-md-12 no-pad">
            <div class="seperator"></div>
        </div>
    </div>



    <div class="col-md-12 config-alerts pad-6 template-uiselect" ng-if="isOpsAdmin || (FeatureEnabled(16) == 1)">
        <h6>Wi-Fi Login Page Template</h6>
        <div class="row config-row">
            <div class="col-md-5">
                <label>
                    Set Template&nbsp;&nbsp;<span class="glyphicon glyphicon-question-sign info-question-sign"
                                                  tooltip="Set custom look and feel for your login website"
                                                  tooltip-trigger
                                                  tooltip-animation="false"
                                                  tooltip-placement="top"></span>
                </label>

                <div class="col-md-12 selects input-group">
                    <i class="glyphicon glyphicon-search"></i>
                    <selectize config='multipleTemplateSelectorConfig'
                               options='templates'
                               ng-model="setting.advanceConfiguration.TEMPLATE_CONFIG.parameters"
                               title="Select template, type to search"></selectize>
                </div>
            </div>
            <div class="col-md-3 col-md-offset-4">
                <label>
                    Create/Edit branding template&nbsp;&nbsp;<span class="glyphicon glyphicon-question-sign info-question-sign"
                                                                   tooltip="Please select brand to proceed"
                                                                   tooltip-trigger
                                                                   tooltip-animation="false"
                                                                   tooltip-placement="top"></span>
                </label>
                <div class="col-md-12 input-group">
                    <a class="btn btn-primary-green" href="/Client/templateCreator" target="_blank" title="create a new template">Click</a>
                </div>


            </div>
        </div>

        <div class="row config-row" ng-if="isSuperAdmin">
            <div class="col-md-12">
                <label>
                    Create/Edit existing template&nbsp;&nbsp;<span class="glyphicon glyphicon-question-sign info-question-sign"
                                                                   tooltip="Create new templates or edit existing ones"
                                                                   tooltip-trigger
                                                                   tooltip-animation="false"
                                                                   tooltip-placement="top"></span>
                </label>
                <div class="row">
                    <div class="col-md-2">
                        <select class="form-control" placeholder="Select template" ng-model="template.selected" ng-change="fetchTemplateDetails(setting.settingId)">
                            <option ng-repeat="template in templates" value="{{template.id}}">{{template.templateName}}</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <input class="col-md-4 form-control" type="text" ng-model="templateDetail.templateName" value="templateDetail.templateName" placeholder="Template Name" />
                        <input type="hidden" ng-model="templateDetail.id">
                    </div>
                    <div class="col-md-4">
                        <input class="col-md-4 form-control" type="text" ng-model="templateDetail.templatePath" value="templateDetail.templatePath" placeholder="Template Path" />
                    </div>
                    <div class="col-md-1">
                        <button class="btn btn-primary-green" ng-click="saveTemplate(setting)" title="save existing template">Save</button>
                    </div>
                    <div class="col-md-1">
                        <button class="btn btn-primary-green" ng-click="saveTemplate(setting, 'new')" title="create a new template">Save as new</button>
                    </div>

                </div>
            </div>
        </div>

        <div class="row config-row" ng-if="template.selected && isConfigAdminUser">
            <div class="col-md-12">
                <label>
                    Question&nbsp;&nbsp;<span class="glyphicon glyphicon-question-sign info-question-sign"
                                              tooltip="Drag drop to change sequence"
                                              tooltip-trigger
                                              tooltip-animation="false"
                                              tooltip-placement="top"></span>
                </label>
                <div class="row no-margin question-area">

                    <div ui-draggable="true" drag="question" drag-channel="A"
                         drop-channel="A" ui-on-drop="onDrop(question, $data)" drop-validate="dropValidate(question, $data)"
                         ng-repeat="question in questions track by $index| propsFilter : { hidden:false }"
                         class="question-set" title="question id: {{question.id}} | next question id: {{question.nextQuestionId}}">
                        <div class="col-md-12 editabe_box question-box template-{{question.templateId}} moved-{{question.moved}}">
                            <div class="collapsed question-bar" data-toggle="collapse" data-target=".question_{{setting.settingId}}_{{question.id}}">
                                {{question.quesText ? question.quesText : 'Question Text'}}
                                <div class="pull-right question-menu">
                                    <button title="Add question below" ng-if="isSuperAdmin" ng-click="$event.stopPropagation();addTemplateQuestion(setting, $index)">
                                        <span class="glyphicon glyphicon-share-alt"></span>
                                    </button>

                                    <button title="Delete Question" ng-if="isSuperAdmin"
                                            ng-confirm-click='{"title":"Are you sure?", "textContent" : "This will delete all dependent questions, Do you want to proceed?", "type":"alert-warning", "ok" : "Ok", "cancel" : "Cancel"}'
                                            ng-click="$event.stopPropagation();deleteTemplateQuestion(setting, question, $index)">
                                        <span class="glyphicon glyphicon-remove"></span>
                                    </button>
                                </div>
                            </div>

                            <div aria-expanded="false" class="collapse question_{{setting.settingId}}_{{question.id}}">
                                <div class="row no-margin">
                                    <input placeholder="Question Text" ng-model="question.quesText" class="form-control" />
                                    <div class="row question_type">
                                        <div class="col-md-6">
                                            <label>Question Type</label>
                                            <select class="form-control" ng-model="question.quesType">
                                                <option selected value="0">Second Page Question</option>
                                                <option value="5">Second Page Upper question</option>
                                            </select>
                                        </div>
                                        <div class="col-md-6">
                                            <label>Answer type</label>
                                            <select class="form-control" ng-model="question.answerType"
                                                    ng-change="updateQuestion($event, question, setting)"
                                                    ng-options="opt.value as opt.type for opt in answerTypes"></select>
                                        </div>
                                    </div>
                                    <div class="row no-margin question_options" ng-if="question.answerType == 1 || question.answerType == 2">
                                        <h6>Options <button title="Add more option" class="pull-right" ng-click="addOption(setting, question)"><span class="glyphicon glyphicon-plus"></span></button></h6>
                                        <div class="form-inline">
                                            <div class="form-group option" ng-repeat="option in question.options track by $index">
                                                <input type="text" placeholder="Option text" ng-model="option.text" class="form-control" />
                                                <input type="text" placeholder="Image link" ng-model="option.image" class="form-control" />

                                                <button ng-if="question.answerType == 1 && isSuperAdmin" title="Create dependent question" ng-click="addDependentQuestion(setting, question, option)">
                                                    <span class="glyphicon glyphicon-share-alt"></span>
                                                </button>

                                                <button ng-if="question.answerType == 1 && isSuperAdmin" title="Pull dependent question from existing questions"
                                                        data-toggle="dropdown" aria-haspopup="true" aria-expanded="true">
                                                    <span class="glyphicon glyphicon-share-alt"></span>
                                                </button>

                                                <div class="dropdown-menu dropdown-menu-right && isSuperAdmin" id="popoverdiv" ng-click="$event.stopPropagation();">
                                                    <input ng-keydown="pullDependentQuestion($event, setting, question, option)" />
                                                </div>

                                                <button title="remove" ng-if="isSuperAdmin" ng-click="deleteOption(question, option, setting, $index)">
                                                    <span class="glyphicon glyphicon-remove"></span>
                                                </button>
                                            </div>

                                        </div>
                                    </div>
                                </div>
                            </div>


                        </div>

                        <div class="col-md-12 editabe_box question-box dependent-question template-{{question.templateId}}"
                             ng-repeat="depQue in question.options" ng-if="depQue.nextQuestion != null"
                             title="question id: {{depQue.nextQuestion.id}} | next question id: {{depQue.nextQuestion.nextQuestionId}}">
                            <div class="collapsed question-bar" data-toggle="collapse" data-target="#question_{{setting.settingId}}_{{depQue.nextQuestion.id}}">
                                <span ng-if="depQue.text">If user selects {{depQue.text}} as answer next question will be - </span> {{depQue.nextQuestion.quesText ? depQue.nextQuestion.quesText : 'Question Text'}}
                                <div class="pull-right question-menu">
                                    <button title="Delete Question" ng-if="isSuperAdmin" confirm-click="This will delete all dependent questions, Do you want to proceed?"
                                            ng-click="$event.stopPropagation();
                                                                            deleteTemplateQuestion(setting, depQue.nextQuestion);
                                                                            depQue.nextQuestion = null;
                                                                            depQue.nextQuestionId = 0;">
                                        <span class="glyphicon glyphicon-remove"></span>
                                    </button>
                                </div>
                            </div>

                            <div id="question_{{setting.settingId}}_{{depQue.nextQuestion.id}}" aria-expanded="false" class="collapse">
                                <div class="row no-margin">
                                    <input placeholder="Question Text" ng-model="depQue.nextQuestion.quesText" class="form-control" />
                                    <div class="row question_type">
                                        <div class="col-md-6">
                                            <label>Question Type</label>
                                            <select class="form-control" ng-model="depQue.nextQuestion.quesType">
                                                <option selected value="0">Second Page Question</option>
                                                <option value="5">Second Page Upper question</option>
                                            </select>
                                        </div>
                                        <div class="col-md-6">
                                            <label>Answer type</label>
                                            <select class="form-control" ng-model="depQue.nextQuestion.answerType" ng-change="updateQuestion(depQue.nextQuestion, setting)">
                                                <option ng-repeat="opt in answerTypes" value="{{opt.value}}">{{opt.type}}</option>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="row no-margin question_options" ng-if="depQue.nextQuestion.answerType == 1 || depQue.nextQuestion.answerType == 2">
                                        <h6>Options <button ng-if="isSuperAdmin" title="Add more options" class="pull-right" ng-click="addOption(setting, depQue.nextQuestion)"><span class="glyphicon glyphicon-plus"></span></button></h6>
                                        <div class="form-inline">
                                            <div class="form-group option" ng-repeat="option in depQue.nextQuestion.options track by $index">
                                                <input type="text" placeholder="Option Text" ng-model="option.text" class="form-control" />
                                                <input type="text" placeholder="Image link" ng-model="option.image" class="form-control" />
                                                <button ng-if="isSuperAdmin" title="remove" ng-click="deleteOption(depQue.nextQuestion, option, setting, $index)">
                                                    <span class="glyphicon glyphicon-remove"></span>
                                                </button>
                                            </div>

                                        </div>
                                    </div>
                                </div>
                            </div>

                        </div>
                    </div>
                    <button style="margin-top: 2rem;" ng-if="template.selected && isSuperAdmin" class="btn pull-right" ng-click="addTemplateQuestion(setting, questions.length)">
                        Add Question
                    </button>
                    <button style="margin-top: 2rem;" ng-if="template.selected  && isSuperAdmin" class="btn pull-right" ng-click="updateQuestions(setting)">
                        Update Questions
                    </button>
                </div>
            </div>
        </div>
    </div>

    <div class="col-md-12 pull-right">
        <button class="btn btn-primary" ng-confirm-click='{"title":"Are you sure?", "textContent" : "Saving settings will affect behavior at all locations in setting. If you want only to update locations press apply instead save.", "type":"alert-warning", "ok" : "Ok", "cancel" : "Cancel"}' ng-click="saveCustomisation(setting)">Save</button>
    </div>
</script>

<script id="/customise_history_config.htm" type="text/ng-template">
    <div class="col-md-12 config-alerts pad-6" feature-toggle="2">
        <h6>Configure Landing Page</h6>
        <div class="row config-row ">
            <div class="col-md-6">
                <label>
                    Welcome Message&nbsp;&nbsp;<span class="glyphicon glyphicon-question-sign info-question-sign"
                                                     tooltip="This message will replace 'You are now connected' message in landing page"
                                                     tooltip-trigger
                                                     tooltip-animation="false"
                                                     tooltip-placement="top"></span>
                </label>
                <textarea disabled class="form-control" ng-model="setting.advanceConfiguration.LANDING_PAGE_CONFIG.parameters[0]"></textarea>
            </div>
        </div>
        <div class="row config-row">
            <div class="col-md-6">
                <label>
                    Custom Landing Page (path)&nbsp;&nbsp;<span class="glyphicon glyphicon-question-sign info-question-sign"
                                                                tooltip="Set your custom landing page"
                                                                tooltip-trigger
                                                                tooltip-animation="false"
                                                                tooltip-placement="top"></span>
                </label>
                <input disabled type="text" class="form-control" placeholder="www.example.com" ng-model="setting.userGroupBasicConfiguration.LANDING_PAGE.value"></input>
            </div>
        </div>

        <div class="col-md-12 no-pad">
            <div class="seperator"></div>
        </div>
    </div>


    <div class="col-md-12 config-alerts pad-6" feature-toggle="45">
        <h6>Splash Page</h6>
        <div class="row config-row ">
            <div class="col-md-6 uploader">

                <div id="uploaderZone{{setting.settingId}}">
                    <div class="fileList" id="fileList{{setting.settingId}}"></div>
                    <img id="splashImage{{setting.settingId}}" class="col-md-6 splashImage"
                         ng-src="{{setting.advanceConfiguration.ROUTER_SPLASH_IMAGE.parameters && setting.advanceConfiguration.ROUTER_SPLASH_IMAGE.parameters.length > 0 && setting.advanceConfiguration.ROUTER_SPLASH_IMAGE.parameters[0] ? setting.advanceConfiguration.ROUTER_SPLASH_IMAGE.parameters[0] : '/images/placeholder.png'}}" />
                    <div class="col-md-6">
                        <div>Drag &amp; Drop Splash image</div>
                        <div class="or">-or-</div>
                        <div class="browser">
                            <span>Click to open the file Browser</span>
                            <input disabled type="file" class="btn btn-primary-green" name="file" multiple="multiple" title="Click to add Files">
                        </div>
                    </div>

                </div>
            </div>

            <div class="col-md-6">
                <label>
                    Splash page text&nbsp;&nbsp;<span class="glyphicon glyphicon-question-sign info-question-sign"
                                                      tooltip="Custom splash page text"
                                                      tooltip-trigger
                                                      tooltip-animation="false"
                                                      tooltip-placement="top"></span>
                </label>
                <input disabled type="text" class="form-control" placeholder="You are about to enjoy free wifi" ng-model="setting.advanceConfiguration.ROUTER_SPLASH_IMAGE.parameters[1]"></input>
            </div>
        </div>

        <div class="col-md-12 no-pad">
            <div class="seperator"></div>
        </div>
    </div>



    <div class="col-md-12 config-alerts pad-6" feature-toggle="28">
        <h6>Header Image</h6>
        <div class="row config-row ">
            <div class="col-md-6 uploader">

                <div id="uploaderZone28{{setting.settingId}}">
                    <div class="fileList" id="fileList28{{setting.settingId}}"></div>
                    <img id="splashImage28{{setting.settingId}}" class="col-md-6 splashImage"
                         ng-src="{{setting.advanceConfiguration.LOGIN_PAGE_IMAGE.parameters && setting.advanceConfiguration.LOGIN_PAGE_IMAGE.parameters.length > 0 && setting.advanceConfiguration.LOGIN_PAGE_IMAGE.parameters[0] ? setting.advanceConfiguration.LOGIN_PAGE_IMAGE.parameters[0] : '/images/placeholder.png'}}" />
                    <div class="col-md-6">
                        <div>Drag &amp; Drop Splash image</div>
                        <div class="or">-or-</div>
                        <div class="browser">
                            <span>Click to open the file Browser</span>
                            <input disabled type="file" class="btn btn-primary-green" name="file" multiple="multiple" title="Click to add Files">
                        </div>
                    </div>

                </div>
            </div>


        </div>

        <div class="col-md-12 no-pad">
            <div class="seperator"></div>
        </div>
    </div>



    <div class="col-md-12 config-alerts pad-6 template-uiselect" feature-toggle="16">
        <h6>Wi-Fi Login Page Template</h6>
        <div class="row config-row" ng-if="isOpsAdmin && setting.advanceConfiguration.TEMPLATE_CONFIG">
            <div class=" col-md-8">
                <label>
                    Set Template&nbsp;&nbsp;<span class="glyphicon glyphicon-question-sign info-question-sign"
                                                  tooltip="Set custom look and feel for your login website"
                                                  tooltip-trigger
                                                  tooltip-animation="false"
                                                  tooltip-placement="top"></span>
                </label>
                <div class="col-md-12 selects input-group">
                    <select disabled>
                        <option value="{{template.id}}" ng-repeat="template in templates" ng-selected="setting.advanceConfiguration.TEMPLATE_CONFIG.parameters == template.id">{{template.templateName}}</option>
                    </select>

                </div>
            </div>
        </div>
    </div>

</script>


<script>
    $(function () {
        $('html').on('click', function (e) {
            if (typeof $(e.target).data('original-title') == 'undefined'
                && !$(e.target).parents().is('.popover.in')) {
                $('[data-original-title]').popover('hide');
            }
        });
    });
</script>

<style>
    .loading .selectize-dropdown-content:after {
        content: 'loading...';
        height: 30px;
        display: block;
        text-align: center;
    }
</style>
