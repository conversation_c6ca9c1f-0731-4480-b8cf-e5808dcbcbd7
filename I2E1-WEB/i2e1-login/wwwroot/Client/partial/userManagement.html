<div class="select-filters top-filter">
    <div class="row">
        <div id="nas-dropdown" class="col-md-12 selects input-group">
            <i class="glyphicon glyphicon-search"></i>
            <selectize config='userConfig'
                       options='data.myusers'
                       ng-model="data.selected"
                       ng-change="fetchUserManagementSettings()"
                       title="Select user, type to search"></selectize>
        </div>
    </div>
</div>

<div class="no_data_found" ng-if="!data.selected">
    <p>Select a user to proceed</p>
</div>
<div ng-if="featureEnabled(50) === 1">
    <div class="user-management" ng-if="data.selected">
        <h4 class="header">User management</h4>
        <hr />
        <div class="row left-align" ng-if="userDetials.parentId == 0">
            <div class="col-md-3 label-column">Make my lead</div>
            <div class="col-md-7"><button class="btn btn-primary" ng-click="manageLeads(1)">Make my lead</button></div>
        </div>

        <div class="row left-align" ng-if="userDetials.parentId > 0">
            <div class="col-md-3 label-column">Drop lead</div>
            <div class="col-md-7"><button class="btn btn-primary" ng-click="manageLeads(0)">Drop lead</button></div>
        </div>

        <div class="row left-align" ng-if="isConfigAdminUser && userDetials.username">
            <div class="col-md-3 label-column">User Type</div>
            <div class="col-md-7 parent-selector">
                <input name="username" type="hidden" value="{{userDetials.userid}}">
                <select convert-to-number class="change-user form-control" ng-model="userDetials.usertype" ng-change="updateTAdmin()">
                    <optgroup label="Interna i2e1 users">
                        <option value="1">Admin</option>
                        <option value="3">Read Only</option>
                        <option value="4">Sales Admin</option>
                        <option value="5">Distributor</option>
                        <option value="5">Ops Admin</option>
                        <option value="0">Standard</option>
                    </optgroup>
                    <optgroup label="External i2e1 users">
                        <option value="100">Client Contact</option>
                        <option value="101">Partner Contact</option>
                        <option value="0">Location Contact</option>
                    </optgroup>
                </select>
            </div>

        </div>

        <div class="row left-align" ng-if="(userDetials.username  && userDetials.usertype < 100) || (userDetials.username && userDetials.usertype >= 100)">
            <div class="col-md-3 label-column">Selected user's Clients</div>
            <div class="col-md-7 nas-selector">
                <input name="username" type="hidden" value="{{userDetials.username}}">
                <div class="locations-selector">
                    <div class="ui-select-input-group">
                        <div class="cell location">
                            <ui-select multiple reset-search-input="true"
                                       ng-model="userDetials.userclients"
                                       on-select="addMappingForUser($item, $model, 'client')"
                                       on-remove="removeMappingForUser($item, $model, 'client')"
                                       theme="select2"
                                       close-on-select="false"
                                       style="{{userDetials.usertype > 100 ? 'pointer-events: none;opacity: 0.7;' : ''}}">
                                <ui-select-match allow-clear="true" placeholder="Clients">
                                    {{$item.clientName}}
                                </ui-select-match>
                                <ui-select-choices position="down" repeat="client in clientsList |
                                                   propsFilter: {clientId: $select.search, clientName: $select.search} | limitTo: 10">
                                    <span ng-bind-html="client.clientName | highlight: $select.search"></span>
                                    <span ng-bind-html="client.clientLegalBusinessName | highlight: $select.search"></span>
                                    <span ng-bind-html="client.clientId | highlight: $select.search"></span>
                                </ui-select-choices>
                            </ui-select>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="row left-align" ng-if="(userDetials.username  && userDetials.usertype < 100) || (userDetials.username  && userDetials.usertype >= 101) ">
            <div class="col-md-3 label-column">Selected user's Partners</div>
            <div class="col-md-7 nas-selector">
                <input name="username" type="hidden" value="{{userDetials.username}}">
                <div class="locations-selector">
                    <div class="ui-select-input-group">
                        <div class="cell location">
                            <ui-select multiple reset-search-input="true"
                                       ng-model="userDetials.userpartners"
                                       on-select="addMappingForUser($item, $model, 'partner')"
                                       on-remove="removeMappingForUser($item, $model, 'partner')"
                                       theme="select2"
                                       close-on-select="false"
                                       style="{{userDetials.usertype > 101 ? 'pointer-events: none;opacity: 0.7;' : ''}}">
                                <ui-select-match allow-clear="true" placeholder="Partners">
                                    {{$item.partnerName}}
                                </ui-select-match>
                                <ui-select-choices position="down" repeat="partner in partnersList |
                                                   propsFilter: {partnerId: $select.search, partnerName: $select.search} | limitTo: 10">
                                    <span ng-bind-html="partner.partnerName | highlight: $select.search"></span>
                                    <span ng-bind-html="partner.clientName | highlight: $select.search"></span>
                                    <span ng-bind-html="partner.partnerId | highlight: $select.search"></span>
                                </ui-select-choices>
                            </ui-select>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="row left-align" ng-if="(userDetials.username  && userDetials.usertype < 100) || (userDetials.username  && userDetials.usertype >= 102)">
            <div class="col-md-3 label-column">Selected user's locations</div>
            <div class="col-md-7 nas-selector">
                <input name="username" type="hidden" value="{{userDetials.username}}">
                <div class="locations-selector">
                    <div class="ui-select-input-group">
                        <div class="cell location">
                            <ui-select multiple reset-search-input="true"
                                       remove-selected="false"
                                       ng-model="userDetials.userlocations"
                                       on-select="addMappingForUser($item, $model, 'location')"
                                       on-remove="removeMappingForUser($item, $model, 'location')"
                                       theme="select2"
                                       close-on-select="false">
                                <ui-select-match allow-clear="true" placeholder="Locations">
                                    {{$item.storeName}}

                                </ui-select-match>
                                
                                    <ui-select-choices position="down" repeat="router in $parent.storesList |
                                                   propsFilter: {nasid: $select.search, storeName: $select.search} |
                                                   limitTo:$parent.storesList track by $index"
                                                   select-with-pagination 
                                                   choices-paging-list="$parent.storesList"
                                                   search-term = "$select.search"
                                                   current-items="$parent.storesList.length"
                                                   infinite-scroll-distance="2"
                                                   page-number="1"
                                                   page-size="100"
                                                   search-funcation="$parent.searchLocations(theDirFn)"
                                                   refresh="directiveFn()"
                                                   refrsh-delay="1000">
                                    <span ng-bind-html="router.storeName | highlight: $select.search"></span>
                                    <span ng-bind-html="router.city | highlight: $select.search"></span>
                                    <span ng-bind-html="router.state | highlight: $select.search"></span>
                                    <span ng-bind-html="router.nasid | highlight: $select.search"></span>
                                </ui-select-choices>
                                                               
                            </ui-select>
                            <span class="glyphicon glyphicon-refresh glyphicon-refresh-animate" style="display:none;"></span>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-2" ng-if="userDetials.userlocations.length > 0">
                <a confirm-click ng-click="removeAllNases()" href="">Remove All</a>
            </div>
        </div>

        <div class="row left-align" ng-if="isConfigAdminUser && userDetials.username">
            <div class="col-md-3 label-column">Selected user is lead of</div>
            <div class="col-md-7 parent-selector">
                <input name="username" type="hidden" value="{{userDetials.userid}}">
                <div class="col-md-12 selects input-group">
                    <i class="glyphicon glyphicon-search"></i>
                    <selectize config='userConfig'
                               options='data.parentUsers.users'
                               ng-model="data.parentUsers.selected"
                               title="Select user, type to search"
                               ng-change="assignNewParent()"></selectize>
                </div>
            </div>
        </div>



        <div class="row left-align" ng-if="isConfigAdminUser">
            <div class="col-md-3 label-column">Product</div>
            <div class="col-md-7 parent-selector">
                <input name="username" type="hidden" value="{{userDetials.product}}">

                <select class="change-user form-control" ng-model="userDetials.product" ng-change="updateTAdmin()">
                    <option value="Plus" ng-selected='userDetials.product == "Plus"'>Plus</option>
                    <option value="Prime" ng-selected='userDetials.product == "Prime"'>Prime</option>
                    <option value="One" ng-selected='userDetials.product == "One"'>One</option>
                </select>
            </div>
        </div>

        <div class="row left-align">
            <div class="col-md-3 label-column">Add SMS</div>
            <div class="col-md-7 centered  parent-selector">
                <input class="col-lg-7 col-md-6 col-sm-7 col-xs-12 " style="font-size:inherit;" type="number" ng-model="smsPackage.smsCount" placeholder="Sms count" />
                <div class="col-lg-3 col-md-4 col-sm-4" id="addSmsButton"><input class=" col-md-offset-1 btn btn-primary" ng-click="smsPackage.addSMS(data.selected, smsPackage.smsCount)" type="button" value="Add sms" /></div>
            </div>
        </div>

        <div class="row left-align">
            <div class="col-md-3 label-column">Sender Id</div>
            <div class="col-md-7 centered  parent-selector">
                <input class="col-lg-7 col-md-6 col-sm-7 col-xs-12 " style="font-size:inherit;" type="text" ng-model="smsPackage.senderId" placeholder="Sender Id" />
                <div class="col-lg-3 col-md-4 col-sm-4" id="addSenderIdButton"><input class=" col-md-offset-1 btn btn-primary" ng-click="smsPackage.addSenderId(data.selected, smsPackage.senderId)" type="button" value="Add Sender Id" /></div>
            </div>
        </div>

        <div class="row left-align">
            <div class="col-md-3 label-column">Portal Access</div>
            <div class="col-md-7 centered  parent-selector">
                <input type="checkbox" id="contact_active" ng-model="userDetials.active" ng-true-value="1" ng-false-value="0" ng-change="updatePortalAcess()">
            </div>
        </div>

    </div>
</div>

<div ng-if="featureEnabled(9) === 1">
    <div class="feature-toggle-container" ng-if="data.selected">
        <h4 class="header">Feature List</h4>
        <hr />
        <div style="margin-bottom:23px;"><input class="btn btn-primary" type="submit" value="Update" ng-click="updateFeatureList()" /></div>
        <table class="table table-responsive">
            <thead>
                <tr>
                    <th>Id</th>
                    <th>Name</th>
                    <th><input title="Hide All" id="hideAllCheck" type="checkbox" ng-model="userDetials.hideAll" ng-true-value="true" ng-false-value="" />Hide</th>
                    <th>Revoke</th>
                    <th>Grant</th>
                </tr>
            </thead>
            <tbody>
                <tr ng-repeat="feature in allFeatureList">
                    <td ng-style="{ 'background-color': feature.status != '0' ? (feature.status == '1' ? '#7FD74C' : '#FA1E25') : ''}">{{$index}}</td>
                    <td>{{feature.value}}</td>
                    <td>
                        <input type="radio" name="{{feature.value}}" ng-model="feature.status" value="-1" ng-checked="userDetials.hideAll || feature.status==-1" /> Hide
                    </td>
                    <td>
                        <input type="radio" name="{{feature.value}}" ng-model="feature.status" value="0" ng-checked="!userDetials.hideAll && feature.status==0" /> OFF
                    </td>
                    <td>
                        <input type="radio" name="{{feature.value}}" ng-model="feature.status" value="1" ng-checked="!userDetials.hideAll && feature.status==1" /> ON
                    </td>
                </tr>
            </tbody>
        </table>
        <div><input class="btn btn-primary" type="submit" value="Update" ng-click="updateFeatureList()" /></div>
    </div>
</div>

