<div class="filters">
    <div class="row">
        <div class="col-md-12 filter-row-2">
            <div class="col-md-3">
                <select class="form-control" ng-model="selectedCity" ng-options="x for x in selectFilters['shop_city']" ng-change="filterRouters({stats: true})">
                    <option value="" selected>-- All Locations --</option>
                </select>
            </div>
            <div class="col-md-3">
                <select class="form-control" ng-model="selectedState">
                    <option value="" selected>Active Inactive both</option>
                    <option value="0">Active</option>
                    <option value="1">In Warning</option>
                    <option value="2">Inactive</option>
                </select>
            </div>
            <!--<div class="col-md-3">
                <ui-select ng-model="routerFilters.selected_PartnerId" name="123" theme="selectize" on-select="filterRouters({ stats: true })">
                    <ui-select-match placeholder="-- All Brands --">
                        {{$select.selected.partnerName}}
                        <a class="glyphicon glyphicon-remove" ng-click="$select.clear($event);" style="position: absolute;right: 3rem;top: 1rem;color: #666666;"></a>
                    </ui-select-match>

                    <ui-select-choices repeat="partner.partnerId as partner in partnersForFilterList | filter: $select.search">
                        <div ng-bind-html="partner.partnerName | highlight: $select.search"></div>
                    </ui-select-choices>
                </ui-select>
            </div>-->
            <div class="col-md-3">
                <select class="form-control" ng-model="selectedInsState" ng-change="filterRouters({stats: true})">
                    <option ng-repeat="option in installationsStates" ng-show="option.isVisible" value="{{option.value}}" ng-selected="option.value == selectedInsState">{{option.display}}</option>
                </select>
            </div>
            <div class="col-md-3" ng-show="isSalesAdmin || isReadOnlyAdmin">
                <select id="selected_PartnerAccountType" class="form-control" ng-model="routerFilters.selected_PartnerAccountType" ng-change="filterRouters({stats: true})">
                    <option value="" ng-selected="true">-- Select account type --</option>
                    <option value="Retail">Retail Account</option>
                    <option value="Strategic">Strategic Account</option>
                    <option value="Enterprise pilot">Pilot</option>
                </select>
            </div>
        </div>
        <div class="col-md-12 filter-row-2">

            <div class="col-md-3" ng-show="isSalesAdmin || isReadOnlyAdmin">
                <div class="ui-select-input-group">
                    <div class="cell location">
                        <ui-select multiple reset-search-input="true" ng-model="filter.selectedTags" ng-model-options="{allowInvalid:true}" theme="select2" close-on-select="true" id="selectedTags" ng-change="filterRoutersByTags({stats: true})">
                            <ui-select-match allow-clear="true" placeholder="Tags">
                                {{$item}}
                            </ui-select-match>
                            <ui-select-choices position="down" repeat="tag in $parent.storeTags | limitTo: 10">
                                <span ng-bind-html="tag | highlight: $select.search"></span>
                            </ui-select-choices>
                        </ui-select>
                    </div>
                </div>
            </div>

            <div class="col-md-3" ng-show="isSalesAdmin || isReadOnlyAdmin">
                <select class="form-control" ng-model="selectedCategory" ng-options="x for x in selectFilters['category']" ng-change="filterRouters({stats: true})">
                    <option value="" selected>-- All Categories --</option>
                </select>
            </div>


        </div>
        <div class="col-md-12 filter-row-stats" ng-if="deviceStats">
            <div class="container-fluid devices_info">
                <h3>Devices</h3>
                <div class="col-md-4 devices">
                    <div drawstatscircle
                         id="active_devices"
                         data-size="150"
                         data-percent="{{deviceStats.activeDevicesPerc}}"
                         data-percenttext="{{deviceStats.activeDevicesPerc}}%"
                         data-totaltext="{{deviceStats.activeDeviceCount}}"
                         data-bgcolor="#49b14f"></div>
                    <h4>Active Devices</h4>
                </div>
                <div class="col-md-4 devices">
                    <div drawstatscircle
                         id="inactive_devices"
                         data-size="150"
                         data-percent="{{deviceStats.inActiveDevicesPerc}}"
                         data-percenttext="{{deviceStats.inActiveDevicesPerc}}%"
                         data-totaltext="{{deviceStats.inActiveDeviceCount}}"
                         data-bgcolor="#e9453a"></div>
                    <h4>Inactive Devices</h4>
                </div>
                <div class="col-md-4 devices">
                    <div drawstatscircle
                         id="total"
                         data-size="150"
                         data-totaltext="{{deviceStats.totalDeviceCount}}"
                         data-bgcolor="#fac027"></div>
                    <h4>Total Devices</h4>
                </div>
            </div>
        </div>
        <div class="col-md-12 filter-row-1">
            <div class="input-group">
                <span class="input-group-addon" id="sizing-addon2"><i class="glyphicon glyphicon-search"></i></span>
                <input type="text" class="form-control" placeholder="Search" ng-model="searchTerm" ng-change="filterRouters({scrollTo: 'router_details'})" ng-model-options="{debounce:1000}">
            </div>
        </div>
    </div>
</div>
<hr />

<div class="home-container">
    <div class="" id="router_details">
        <table class="table table-responsive" id="nas-list">
            <thead>
                <tr>
                    <th>Nas ID</th>
                    <th>Name</th>
                    <th>Contact</th>
                    <th class="center-align">Last Seen</th>
                    <th class="center-align">Last activity</th>
                    <th class="center-align">Users (24h)</th>
                    <th class="center-align" ng-if="featureEnabled(14) != -1">Data used (30 days)</th>
                    <th class="center-align" ng-if="isConfigAdminUser">Lat</th>
                    <th class="center-align" ng-if="isConfigAdminUser">Lng</th>
                    <th class="center-align" width="60px">Operations</th>
                </tr>
            </thead>
            <tbody infinite-scroll='loadMore()' infinite-scroll-container='"#admin_container"' infinite-scroll-disabled='infiniteScrollDisabled' infinite-scroll-distance="3">
                <tr ng-repeat="store in storeList | filter: { active: selectedState }" class="{{store.css}}">
                    <td>{{store.nasid}}</td>
                    <td>{{store.shopDpName ? store.shopDpName: store.storeName}} ({{store.city}},{{store.state}})</td>
                    <td ng-if="store.contactDetail"><a href="tel:{{store.contactDetail}}">{{store.contactDetail}}</a></td>
                    <td ng-if="!store.contactDetail"><a ng-click="getStoreContact(store.nasid)">View</a></td>
                    <!--<td class="center-align"><div class="circle_{{store.active ? 'success' : 'danger'}}"></div></td>-->
                    <td class="center-align" title="Last seen before {{store.lastPingDelay}}" ng-bind-html="store.lastPingDelay"></td>
                    <td class="center-align" title="Last activity was recorded before {{store.lastUsed}}" ng-bind-html="store.lastUsed"></td>
                    <td class="center-align" title="{{store.inLast24Hours}} users logged in last 24 hours" ng-bind-html="store.inLast24Hours"></td>
                    <td class="center-align" ng-if="featureEnabled(14) != -1">{{store.dataUsedInGB ? store.dataUsedInGB + " GB" : "NA" }}</td>
                    <td class="center-align" ng-if="isConfigAdminUser">{{store.latitude ? store.latitude : "NA" }}</td>
                    <td class="center-align" ng-if="isConfigAdminUser">{{store.longitude ? store.longitude : "NA" }}</td>
                    <td class="center-align">
                        <a ng-show="isSalesAdmin" class="edit_link glyphicon glyphicon-edit" title="Google sync" ng-click="showBrandEditDialog(store)" />&nbsp;&nbsp;&nbsp;&nbsp;
                    </td>
                </tr>
            </tbody>
        </table>
        <div ng-if="!data.infiniteScrollDisabled" style="font-size: 1.2rem"><span colspan="8">Loading data...</span></div>
        <div ng-if="!storeList.length" style="font-size: 1.2rem"><span colspan="8">No data present</span></div>
    </div>
</div>
<script type="text/ng-template" id="/brandEdit.html">
    <div class="modal-header storeEditHeader">
        <h4 class="modal-title" id="storeEditLabel">{{data.tab_head ? data.tab_head : 'Store' }}</h4>
        <button class="close-modal" ng-click="cancel()">
            <span aria-hidden="true">&times;</span>
        </button>
    </div>
    <div class="modal-body col-md-12 no-pad storeEditBody">
        <!-- Client Info tab -->
        <div class="tab-content" ng-show="data.tab === 'client_info'">
            <div ng-show="!data.showClientForm">
                <div class="ui-select-input-group">
                    <div class="cell location form-group">
                        <label for="create_client" class="control-label">Select Client <span class="man">*</span></label>
                        <ui-select select id="create_client" ng-model=" data.client" theme="selectize" ng-disabled="ctrl.disabled" title="Choose a client">
                            <ui-select-match placeholder="Select a client in the list or search ...">{{$select.selected.clientName || $select.search}}</ui-select-match>
                            <ui-select-choices repeat="client in clientsList | propsFilter: {clientName: $select.search}">
                                <div ng-bind-html="client.clientName | highlight: $select.search"></div>
                            </ui-select-choices>
                        </ui-select>
                        <p id="show-campaign-error" style="display:none;">Please give a name for your client -or- select an existing one.</p>
                    </div>
                </div>
                <div class="form-group" style="margin-top:2rem;">
                    <div class="pull-right">
                        <button ng-disabled="metadata.input.$error.required" class="btn btn-primary" ng-click="data.showClientForm = true">Edit</button>
                        <button ng-disabled="metadata.input.$error.required" class="btn btn-primary" ng-click="data.showClientForm = true; data.client = {}">Add New</button>
                        <button ng-disabled="metadata.input.$error.required" ng-click="data.updateClientDetails(data,data.client)" class="btn btn-primary">Next <span class="glyphicon glyphicon-arrow-right"></span></button>
                    </div>
                </div>
            </div>

            <div id="clientForm" ng-show="data.showClientForm">
                <div class="form-group">
                    <label for="client_name" class="control-label">Client Name <span class="man">*</span></label>
                    <input class="form-control" id="client_name" ng-model="data.client.clientName" />
                </div>
                <div class="form-group">
                    <label for="legal_business_name" class="control-label">
                        Legal Business Name <!--<span class="man">*</span>-->
                    </label>
                    <input class="form-control" id="legal_business_name" ng-model="data.client.clientLegalBusinessName" />
                </div>
                <div class="form-group">
                    <label for="client_gst_number" class="control-label">
                        GST Number <!--<span class="man">*</span>-->
                    </label>
                    <input class="form-control" id="client_gst_number" ng-model="data.client.clientGSTNumber" placeholder="07ABCDE1234F1Z1" />
                </div>
                <div class="form-group">
                    <label for="client_contract_signed" class="control-label">Contract Signed</label>
                    <input type="checkbox" id="client_contract_signed" ng-model="data.client.clientContractSigned" ng-true-value="1" ng-false-value="0" />
                </div>
                <div class="form-group">
                    <label for="billing_address" class="control-label">
                        Billing Address <!--<span class="man">*</span>-->
                    </label>
                    <textarea class="form-control" id="billing_address" ng-model="data.client.clientBillingAddress"></textarea>
                </div>
                <div class="form-group" style="margin-top:2rem;">
                    <div class="pull-right">
                        <button ng-disabled="metadata.input.$error.required" ng-click="data.showClientForm = false" class="btn btn-primary"><span class="glyphicon glyphicon-arrow-up"></span> Cancel</button>
                        <button ng-disabled="metadata.input.$error.required" ng-click="data.updateClientDetails(data,data.client)" class="btn btn-primary">Save & Continue <span class="glyphicon glyphicon-arrow-right"></span></button>
                    </div>
                </div>
            </div>
        </div>
        <!-- Client contacts tab -->
        <div class="tab-content" ng-show="data.tab === 'client_contacts'">
            <div ng-if="data.clientContactList.length> 0" class="row">
                <table class="table table-striped">
                    <thead>
                        <tr>
                            <th>Name</th>
                            <th>Email</th>
                            <th>Mobile</th>
                            <th>Portal Access</th>
                            <th></th>
                            <th></th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr ng-repeat="contact in data.clientContactList">
                            <td>{{contact.name}}</td>
                            <td>{{contact.username}}</td>
                            <td>{{contact.mobile}}</td>
                            <td>{{contact.active ? 'Yes':'No'}}</td>
                            <td>
                                <a class="edit_link glyphicon glyphicon-edit" title="Edit" ng-click="data.selectedClientContact = contact"></a>
                            </td>
                            <td>
                                <a class="edit_link glyphicon glyphicon-remove" title="Delete" confirm-click ng-click="data.removeClientContact(contact,data,data.client.clientId)"></a>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>

            <div class="row" ng-show="data.selectedClientContact.userId">
                <div class="form-group">
                    <label for="contact_name" class="control-label">Contact Name <span class="man">*</span></label>
                    <input class="form-control" id="contact_name" ng-model="data.selectedClientContact.name" />
                </div>
                <div class="form-group">
                    <label for="contact_email" class="control-label">Contact Email <span class="man">*</span></label>
                    <input class="form-control" id="contact_email" ng-change="data.changed_user(data)" ng-model="data.selectedClientContact.username" />
                </div>
                <div class="form-group">
                    <label for="contact_mobile" class="control-label">Contact Mobile <span class="man">*</span></label>
                    <input class="form-control" id="contact_mobile" ng-model="data.selectedClientContact.mobile" />
                </div>
                <div class="form-group">
                    <label for="contact_active" class="control-label">Portal Access </label>
                    <input type="checkbox" id="contact_active" ng-change="data.changed_access(data)" ng-model="data.selectedClientContact.active" ng-true-value="1" ng-false-value="0" />
                </div>
                <div class="form-group" style="margin-top:2rem;">
                    <div class="pull-right">
                        <button ng-disabled="metadata.input.$error.required" ng-click="data.updateClientContactPersons(data,data.client.clientId)" class="btn btn-primary">Update</button>
                    </div>
                </div>
            </div>

            <div class="row">
                <div class="form-group" style="margin-top:2rem;">
                    <div class="pull-right">
                        <button ng-disabled="metadata.input.$error.required" ng-click="data.tab='client_info';data.tab_head='Client Info'" class="btn btn-primary"><span class="glyphicon glyphicon-arrow-left"></span> Back</button>
                        <button ng-disabled="metadata.input.$error.required" ng-click="data.selectedClientContact = data.getNewContactObj()" class="btn btn-primary">Add New</button>
                        <button ng-disabled="metadata.input.$error.required" ng-click="data.tab='partner_info';data.tab_head='Partner'" class="btn btn-primary">Next <span class="glyphicon glyphicon-arrow-right"></span></button>
                    </div>
                </div>
            </div>
        </div>
        <!-- partner Info tab  -->
        <div class="tab-content" ng-show="data.tab === 'partner_info'">
            <div class="row" ng-show="!showpartnerform">
                <div class="col-md-12">
                    <div class="ui-select-input-group">
                        <div class="cell location form-group">
                            <label for="create_partner" class="control-label">Partner <span class="man">*</span></label>
                            <ui-select id="create_partner" ng-required="true" ng-model="data.partner" theme="selectize">
                                <ui-select-match placeholder="Enter a partner name or choose from existing">
                                    {{$select.selected.partnerName || $select.search}}
                                </ui-select-match>
                                <ui-select-choices repeat="partner in data.partnerList | propsFilter: {partnerName: $select.search}">
                                    <span ng-bind-html="partner.partnerName | highlight: $select.search"></span>
                                </ui-select-choices>
                            </ui-select>
                        </div>
                    </div>
                </div>
                <div class="col-md-12">
                    <div class="pull-right">
                        <button ng-disabled="metadata.input.$error.required" ng-click="data.tab = 'client_contacts';data.tab_head='Client Contacts'" class="btn btn-primary"><span class="glyphicon glyphicon-arrow-left"></span>  Back</button>
                        <button ng-disabled="metadata.input.$error.required" class="btn btn-primary" ng-click="showpartnerform = true">Edit</button>
                        <button ng-disabled="metadata.input.$error.required" class="btn btn-primary" ng-click="showpartnerform = true; data.partner = {};data.partner.clientId = data.client.clientId">Add New</button>
                        <button ng-disabled="metadata.input.$error.required" ng-click="data.updateClientsPartnerDetails(data,data.partner)" class="btn btn-primary">Next  <span class="glyphicon glyphicon-arrow-right"></span></button>
                    </div>
                </div>
            </div>
            <dvi ng-show="showpartnerform">
                <div class="row">
                    <div class="col-md-12">
                        <div class=" form-group">
                            <label for="partner_name" class="control-label">Partner Name <span class="man">*</span></label>
                            <input class="form-control" id="partner_name" ng-model="data.partner.partnerName" />
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-6">
                        <label for="partner_logo_file_upload" class="control-label">Partner Logo </label>
                        <div id="partner_logo_result_container"></div>
                        <div class=" form-group">
                            <div class="col-md-6 no-pad auto-file-upload preview-section">
                                <div class="preview-container">
                                    <div class="partner_logo_result_container">
                                        <img ng-if="!data.partner.partnerLogo" id="#partnerLogoImage" class="partnerLogoImage" src="../../images/coupon_placeholder.png" />
                                        <img ng-if="data.partner.partnerLogo" id="#partnerLogoImage" class="partnerLogoImage" src="{{data.partner.partnerLogo}}" />
                                    </div>
                                </div>
                                <div class="upload_buttons_hidden">
                                    <div id="upload_partner_logo">
                                        <div class="browser">
                                            <label>
                                                <input type="file" name="file" id="partner_logo_file_upload" title="Click to add File">
                                            </label>
                                        </div>
                                    </div>
                                </div>
                                <div class="upload_buttons">
                                    <button class="btn btn-primary-green" ng-click="data.uploadPartnerLogo()">Upload</button>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <label for="partner_image_file_upload" class="control-label">Partner Image </label>
                        <div id="partner_image_result_container"></div>
                        <div class=" form-group">
                            <div class="col-md-6 no-pad auto-file-upload preview-section">
                                <div class="preview-container">
                                    <div class="partner_logo_result_container">
                                        <img ng-if="!data.partner.partnerImage" id="#partnerImage" class="partnerLogoImage" src="../../images/coupon_placeholder.png" />
                                        <img ng-if="data.partner.partnerImage" id="#partnerImage" class="partnerLogoImage" src="{{data.partner.partnerImage}}" />
                                    </div>
                                </div>
                                <div class="upload_buttons_hidden">
                                    <div id="upload_partner_image" class="campaign-img">
                                        <div class="browser">
                                            <label>
                                                <input type="file" name="file" id="partner_image_file_upload" title="Click to add File">
                                            </label>
                                        </div>
                                    </div>
                                </div>
                                <div class="upload_buttons">
                                    <button class="btn btn-primary-green" ng-click="data.uploadPartnerImage()">Upload</button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <!--<pre>{{data.partner}}</pre>-->
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="partner_category" class="control-label">Category <span class="man">*</span></label>
                            <select ng-required="true" id="partner_category" class="form-control" ng-model="data.partner.category" ng-change="data.partner.subCategory = null;data.partner.microCategory = null">
                                <option value="" ng-selected="true">-- Select a category --</option>
                                <option value='{{key}}' ng-selected="data.partner.category.toLowerCase() == key" ng-repeat="(key, value) in data.i2e1Categories.categories">{{value.display}}</option>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="partner_subcategory" class="control-label">Subcategory <span class="man">*</span></label>
                            <select id="partner_subcategory"
                                    class="form-control"
                                    ng-model="data.partner.subCategory"
                                    ng-disabled="data.i2e1Categories.categories[data.partner.category].subcategories.length == 0 || data.partner.category == null || data.partner.category == ''"
                                    ng-change="data.partner.microCategory = null">
                                <option value="" ng-selected="true">-- Select a subcategory --</option>
                                <option ng-selected="data.partner.subCategory.toLowerCase() == key" value='{{key}}' ng-repeat="(key, value) in data.i2e1Categories.categories[data.partner.category].subcategories">{{value.display}}</option>
                            </select>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="partner_microcategory" class="control-label">Micro Category <span class="man">*</span></label>
                            <select id="partner_microcategory"
                                    class="form-control"
                                    ng-model="data.partner.microCategory"
                                    ng-disabled="!data.i2e1Categories.categories[data.partner.category].subcategories[data.partner.subCategory].microcategories || data.partner.subCategory == null || data.partner.subCategory == '' ">
                                <option value="" ng-selected="true">-- Select a micro category --</option>
                                <option ng-selected="data.partner.microCategory.toLowerCase() == key"
                                        value='{{key}}'
                                        ng-repeat="(key, value) in data.i2e1Categories.categories[data.partner.category].subcategories[data.partner.subCategory].microcategories">
                                    {{value.display}}
                                </option>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="partner_accountType" class="control-label">Account Type <span class="man">*</span></label>
                            <select id="partner_accountType" class="form-control" ng-model="data.partner.accountType">
                                <option value="" ng-selected="true">-- Select account type --</option>
                                <option value="Retail">Retail Account</option>
                                <option value="Strategic">Strategic Account</option>
                                <option value="Enterprise pilot">Pilot</option>
                            </select>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="partner_productType" class="control-label">Product Type <span class="man">*</span></label>
                            <select id="partner_productType" class="form-control" ng-model="data.partner.productType">
                                <option value="" ng-selected="true">-- Select product type --</option>
                                <option value="Plus">Plus</option>
                                <option value="Prime">Prime</option>
                                <option value="One">One</option>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <label for="partner_discount" class="control-label">Discount </label>
                        <input class="form-control" id="partner_discount" ng-model="data.partner.discount" />
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="partner_subscriptionType" class="control-label">
                                Subscription Type <!--<span class="man">*</span>-->
                            </label>
                            <select id="partner_subscriptionType" class="form-control" ng-model="data.partner.subscriptionType">
                                <option value="" ng-selected="true">-- Select subscription type --</option>
                                <option value="monthly">Monthly</option>
                                <option value="quarterly">Quarterly</option>
                                <option value="annually">Annually</option>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <label for="partner_subscriptionAmt" class="control-label">Subscription Ammount </label>
                        <input class="form-control" id="partner_subscriptionAmt" ng-model="data.partner.subscriptionAmt" />
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-6">
                        <label for="partner_subscriptionStartDate" class="control-label">Subscription Start Date </label>
                        <input type="text" id="partner_subscriptionStartDate"
                               class="form-control"
                               datepicker-popup="dd-MM-yyyy"
                               show-button-bar="false"
                               show-weeks="false"
                               ng-model="data.partner.subscriptionStartDate"
                               is-open="isStartDatePickerOpen"
                               ng-click="isStartDatePickerOpen = !isStartDatePickerOpen"
                               close-text="Close"
                               placeholder="dd-MM-yyyy" />
                    </div>
                    <div class="col-md-6">
                        <label for="partner_subscriptionEndDate" class="control-label">Subscription Renewal Date </label>
                        <input type="text" id="partner_subscriptionRenewalDate"
                               class="form-control"
                               datepicker-popup="dd-MM-yyyy"
                               show-button-bar="false"
                               show-weeks="false"
                               ng-model="data.partner.subscriptionRenewalDate"
                               init-date="data.partner.subscriptionRenewalDate"
                               is-open="isRenewalDatePickerOpen"
                               ng-click="isRenewalDatePickerOpen = !isRenewalDatePickerOpen"
                               close-text="Close"
                               placeholder="dd-MM-yyyy" />
                    </div>
                </div>
                <div class="row" style="margin-top:2rem;">
                    <div class="col-md-12">
                        <div class="form-group pull-right">
                            <button ng-disabled="metadata.input.$error.required" ng-click="showpartnerform = false" class="btn btn-primary"><span class="glyphicon glyphicon-arrow-up"></span> Cancel</button>
                            <button ng-disabled="metadata.input.$error.required" ng-click="data.tab = 'client_contacts';data.tab_head='Client Contacts'" class="btn btn-primary"><span class="glyphicon glyphicon-arrow-left"></span>  Back</button>
                            <button ng-disabled="metadata.input.$error.required" ng-click="data.updateClientsPartnerDetails(data,data.partner)" class="btn btn-primary">Save & Continue  <span class="glyphicon glyphicon-arrow-right"></span></button>
                        </div>
                    </div>
                </div>
        </div>
        <!-- partner contacts tab -->
        <div class="tab-content" ng-show="data.tab === 'partner_contacts'">
            <div ng-if="data.partnerContactList.length> 0" class="row">
                <table class="table table-striped">
                    <thead>
                        <tr>
                            <th>Name</th>
                            <th>Email</th>
                            <th>Mobile</th>
                            <th>Portal Access</th>
                            <th></th>
                            <th></th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr ng-repeat="contact in data.partnerContactList">
                            <td>{{contact.name}}</td>
                            <td>{{contact.username}}</td>
                            <td>{{contact.mobile}}</td>
                            <td>{{contact.active ? 'Yes':'No'}}</td>
                            <td>
                                <a class="edit_link glyphicon glyphicon-edit" title="Edit" ng-click="data.selectedPartnerContact = contact"></a>
                            </td>
                            <td>
                                <a class="edit_link glyphicon glyphicon-remove" title="Delete" confirm-click ng-click="data.removePartnerContact(contact,data,data.partner.partnerId)"></a>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>

            <div class="row" ng-show="data.selectedPartnerContact.userId">
                <div class="form-group">
                    <label for="partner_contact_name" class="control-label">Contact Name <span class="man">*</span></label>
                    <input class="form-control" id="partner_contact_name" ng-model="data.selectedPartnerContact.name" />
                </div>
                <div class="form-group">
                    <label for="partner_contact_email" class="control-label">Contact Email <span class="man">*</span></label>
                    <input class="form-control" id="partner_contact_email" ng-change="data.changed_user(data)" ng-model="data.selectedPartnerContact.username" />
                </div>
                <div class="form-group">
                    <label for="partner_contact_mobile" class="control-label">Contact Mobile <span class="man">*</span></label>
                    <input class="form-control" id="partner_contact_mobile" ng-model="data.selectedPartnerContact.mobile" />
                </div>
                <div class="form-group">
                    <label for="partner_contact_active" class="control-label">Portal Access </label>
                    <input type="checkbox" id="partner_contact_active" ng-change="data.changed_access(data)" ng-model="data.selectedPartnerContact.active" ng-true-value="1" ng-false-value="0" />
                </div>
                <div class="form-group" style="margin-top:2rem;">
                    <div class="pull-right">
                        <button ng-disabled="metadata.input.$error.required" ng-click="data.updatePartnerContactPersons(data,data.partner.partnerId)" class="btn btn-primary">Update</button>
                    </div>
                </div>
            </div>

            <div class="row">
                <div class="form-group" style="margin-top:2rem;">
                    <div class="pull-right">
                        <button ng-disabled="metadata.input.$error.required" ng-click="data.tab='partner_info';data.tab_head='Partner Info'" class="btn btn-primary"><span class="glyphicon glyphicon-arrow-left"></span> Back</button>
                        <button ng-disabled="metadata.input.$error.required" ng-click="data.selectedPartnerContact = data.getNewContactObj()" class="btn btn-primary">Add New</button>
                        <button ng-disabled="metadata.input.$error.required" ng-click="data.tab='store_info';data.tab_head='Store Info'" class="btn btn-primary">Next <span class="glyphicon glyphicon-arrow-right"></span></button>
                    </div>
                </div>
            </div>
        </div>
        <!-- store info tab -->
        <div class="tab-content" ng-show="data.tab === 'store_info'">
            <div class="{{isConfigAdminUser ?  'col-md-6 half-container' : 'col-md-12'}}">
                <div class="angular-drop-down-auto-complete form-group{{isConfigAdminUser ?  '' : ' col-md-6'}}">
                    <label for="address" class="control-label">Market Place</label>
                    <div angular-auto-complete id="ex1" exclude-click='ex1' placeholder="Search Place" pause="300" selectedobject="place" url="/Client/GetMarketPlaces?marketplace=" titlefield="marketPlaceName" minlength="2" inputclass="form-control form-control-small" inputtype="locationfinder" callback-fn="data.updateMarketPlace(place,data)" inputvalue="data.store.marketPlaceName" />
                </div>
                <div class="angular-drop-down-auto-complete form-group{{isConfigAdminUser ?  '' : ' col-md-6'}}">
                    <label for="storeName" class="control-label">Brand name <span class="man">*</span></label>
                    <div angular-auto-complete id="ex2" exclude-click='ex2' placeholder="Search Brand" pause="400"
                         selectedobject="place" url="/Client/GetBarndStoresByMarketPlace?marketPlace={{data.store.marketPlaceName}}&brand="
                         titlefield="brandName" minlength="2" inputclass="form-control form-control-small" inputtype="brandName"
                         callback-fn="data.updateBrandName(place,data)" inputvalue="data.store.brandName" ng-keydown="data.updateSoreNameOnBrandChange(data.store,$event)" />
                </div>
                <!--
        <div class="form-group{{isConfigAdminUser ?  '' : ' col-md-6'}}">
            <label for="storeName" class="control-label">Store name</label>
            <input disabled ng-required="true" class="form-control" id="storeName" ng-model="data.store.storeName" placeholder="Store Name" style="background-color: #eee !important;opacity: .7;font-weight:bold;" />
        </div>
    -->
                <div class="form-group{{isConfigAdminUser ?  '' : ' col-md-6'}}">
                    <label for="shopDpName" class="control-label">Shop display name</label>
                    <input disabled ng-required="true"
                           class="form-control" id="shopDpName"
                           ng-model="data.store.shopDpName"
                           placeholder="Store Name"
                           style="background-color: #eee !important;opacity: .7;font-weight:bold;" />
                </div>
                <!--
        <div ng-if="isConfigAdminUser" class="form-group{{isConfigAdminUser ?  '' : ' col-md-6'}}">
            <label for="storeNameAlias" class="control-label">External Store Idenitfier </label>
            <input class="form-control" id="storeNameAlias" ng-model="data.store.storeNameAlias" placeholder="Analytics Alias" />
        </div>
    -->
                <div ng-if="isConfigAdminUser" class="form-group{{isConfigAdminUser ?  '' : ' col-md-6'}}">
                    <label for="mktPlaceAlias" class="control-label">Market Place Idenitfier </label>
                    <input class="form-control" id="mktPlaceAlias"
                           ng-model="data.store.mktPlaceAlias"
                           placeholder="Market Place Alias"
                           ng-change="data.updateSoreName(data.store)" />
                </div>
                <div class="form-group{{isConfigAdminUser ?  '' : ' col-md-6'}}">
                    <label for="address" class="control-label">Address <span class="man">*</span></label>
                    <textarea ng-required="true" class="form-control" id="address" ng-model="data.store.address"></textarea>
                    <!--<input ng-required="true" class="form-control" id="address" ng-model="data.store.address" placeholder="Address" />-->
                </div>
                <div class="form-group{{isConfigAdminUser ?  '' : ' col-md-6'}}">
                    <label for="address" class="control-label">Sub Locality</label>
                    <input ng-required="true" class="form-control" id="sublocalty" ng-model="data.store.subLocality" placeholder="Address" />
                </div>
                <div class="form-group{{isConfigAdminUser ?  '' : ' col-md-6'}}">
                    <label for="address" class="control-label">Locality </label>
                    <input ng-required="true" class="form-control" id="locality" ng-model="data.store.locality" placeholder="Address" />
                </div>
                <div class="form-group{{isConfigAdminUser ?  '' : ' col-md-6'}}">
                    <label for="city" class="control-label">City <span class="man">*</span></label>
                    <input ng-required="true" class="form-control" id="city" ng-model="data.store.city" placeholder="City" googleplace callback-fn="data.updateCityName(place,data)" />
                </div>
                <div class="form-group{{isConfigAdminUser ?  '' : ' col-md-6'}}">
                    <label for="state" class="control-label">State <span class="man">*</span></label>
                    <input ng-required="true" class="form-control" id="state" ng-model="data.store.state" placeholder="State" />
                </div>
                <div class="form-group{{isConfigAdminUser ?  '' : ' col-md-6'}}">
                    <label for="pincode" class="control-label">Pincode </label>
                    <input ng-required="true" id="pinCode" class="form-control" ng-model="data.store.pinCode" placeholder="110016" />
                </div>
                <div class="form-group{{isConfigAdminUser ?  '' : ' col-md-6'}}">
                    <label for="category" class="control-label">Google Category <span class="man">*</span></label>
                    <create-tags tag-data="data.store.googleCategories"></create-tags>
                </div>
                <div class="form-group">
                    <label for="category" class="control-label">Category <span class="man">*</span></label>
                    <select ng-required="true" id="category" class="form-control" ng-model="data.store.category" ng-change="data.store.subCategory = null;data.store.microCategory = null">
                        <option value="">-- Select a category --</option>
                        <option value='{{key}}' ng-selected="data.store.category.toLowerCase() == key" ng-repeat="(key, value) in data.i2e1Categories.categories">{{value.display}}</option>
                    </select>
                </div>
                <div class="form-group">
                    <label for="subCategory" class="control-label">Subcategory <span class="man">*</span></label>
                    <select id="subCategory"
                            class="form-control"
                            ng-model="data.store.subCategory"
                            ng-disabled="data.i2e1Categories.categories[data.store.category].subcategories.length == 0 || data.store.category == null || data.store.category == ''"
                            ng-change="data.store.microCategory = null">
                        <option value="" ng-selected="true">-- Select a sub category --</option>
                        <option ng-selected="data.store.subCategory.toLowerCase() == key" value='{{key}}' ng-repeat="(key, value) in data.i2e1Categories.categories[data.store.category].subcategories">{{value.display}}</option>
                    </select>
                </div>
                <div class="form-group">
                    <label for="partner_microcategory" class="control-label">Micro Category <span class="man">*</span></label>
                    <select id="partner_microcategory"
                            class="form-control"
                            ng-model="data.store.microCategory"
                            ng-disabled="!data.i2e1Categories.categories[data.store.category].subcategories[data.store.subCategory].microcategories || data.store.subCategory == null || data.store.subCategory == '' ">
                        <option value="" ng-selected="true">-- Select a micro category --</option>
                        <option ng-selected="data.store.microCategory.toLowerCase() == key"
                                value='{{key}}'
                                ng-repeat="(key, value) in data.i2e1Categories.categories[data.store.category].subcategories[data.store.subCategory].microcategories">
                            {{value.display}}
                        </option>
                    </select>
                </div>
                <div ng-show="isSalesAdmin" class="form-group{{isConfigAdminUser ?  '' : ' col-md-6'}}">
                    <label for="storetags" class="control-label">Tags</label>
                    <div class="ui-select-input-group">
                        <div class="cell location">
                            <ui-select multiple reset-search-input="true" ng-model="data.store.storeTags" theme="select2" close-on-select="false" id="storetags">
                                <ui-select-match allow-clear="true" placeholder="Tags">
                                    {{$item}}
                                </ui-select-match>
                                <ui-select-choices position="down" repeat="tag in $parent.storeTags | limitTo: 10">
                                    <span ng-bind-html="tag | highlight: $select.search"></span>
                                </ui-select-choices>
                            </ui-select>
                        </div>
                    </div>
                </div>
                <div ng-show="isSalesAdmin" class="form-group{{isConfigAdminUser ?  '' : ' col-md-6'}}">
                    <label for="locationRetagDate" class="control-label">Retag date</label>
                    <input type="date" class="form-control" id="locationRetagDate" ng-model="data.store.locationRetagDate" />
                </div>
            </div>
            <div class="col-md-6 half-container">
                <div ng-if="isConfigAdminUser">
                    <div class="form-group">
                        <label for="routerState" class="control-label">Status <span class="man">*</span></label>
                        <select class="form-control" id="routerState" ng-model="data.store.routerState">
                            <option ng-repeat="option in installationsStates" ng-show="option.isVisible && option.value" value="{{option.value}}" ng-selected="data.store.routerState == option.value">{{option.display}}</option>
                        </select>

                    </div>
                    <div class="form-group">
                        <label for="mode" class="control-label">Mode <span class="man">*</span></label>
                        <select ng-required="isConfigAdminUser" id="mode" class="form-control" ng-model="data.store.mode">
                            <option value="">-- Select a Mode --</option>
                            <option value="0">Monitor</option>
                            <option value="1">Controller</option>
                            <option value="2">Both</option>
                        </select>
                    </div>
                    <div ng-if="isConfigAdminUser" class="form-group">
                        <label for="mmNasId" class="control-label">Monitor Mode Nas Id <span class="man">*</span></label>
                        <input class="form-control" id="mmNasId" ng-model="data.store.mmNasId" placeholder="Monitor Mode Nas Id" />
                    </div>
                    <div class="form-group">
                        <div class="ui-select-input-group">
                            <div class="cell location form-group">
                                <label for="salesId" class="control-label">Sales person<span class="man">*</span></label>
                                <ui-select id="salesId" ng-required="true" ng-model="data.store.salesId" theme="selectize">
                                    <ui-select-match placeholder="Select a sales person name">
                                        {{$select.selected.name || $select.search}}
                                    </ui-select-match>
                                    <ui-select-choices repeat="salesPerson.userName as salesPerson in data.i2e1SalesPersons | propsFilter: {name: $select.search}"
                                                       value="{{salesPerson.userName}}"
                                                       refresh-delay="0">
                                        <span ng-bind-html="salesPerson.name | highlight: $select.search"></span>
                                        &nbsp;&nbsp;-&nbsp;&nbsp;
                                        <small ng-bind-html="salesPerson.userName | highlight: $select.search"></small>
                                    </ui-select-choices>
                                </ui-select>
                            </div>
                        </div>
                    </div>
                    <div class="form-group">
                        <label for="locationStartDate" class="control-label">Start date<span class="man">*</span></label>
                        <input ng-required="isConfigAdminUser" type="date" class="form-control" id="locationStartDate" ng-model="data.store.locationStartDate" />
                    </div>
                    <div class="form-group">
                        <label for="locationOpeningTime" class="control-label">Opening time</label>
                        <select id="locationOpeningTime" class="form-control" ng-model="data.store.locationOpeningTime">
                            <option value="">-- Select opening time --</option>
                            <option value="00:00:00">00:00</option>
                            <option value="01:00:00">01:00</option>
                            <option value="02:00:00">02:00</option>
                            <option value="03:00:00">03:00</option>
                            <option value="04:00:00">04:00</option>
                            <option value="05:00:00">05:00</option>
                            <option value="06:00:00">06:00</option>
                            <option value="07:00:00">07:00</option>
                            <option value="08:00:00">08:00</option>
                            <option value="09:00:00">09:00</option>
                            <option value="10:00:00">10:00</option>
                            <option value="11:00:00">11:00</option>
                            <option value="12:00:00">12:00</option>
                            <option value="13:00:00">13:00</option>
                            <option value="14:00:00">14:00</option>
                            <option value="15:00:00">15:00</option>
                            <option value="16:00:00">16:00</option>
                            <option value="17:00:00">17:00</option>
                            <option value="18:00:00">18:00</option>
                            <option value="19:00:00">19:00</option>
                            <option value="20:00:00">20:00</option>
                            <option value="21:00:00">21:00</option>
                            <option value="22:00:00">22:00</option>
                            <option value="23:00:00">23:00</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="locationClosingTime" class="control-label">Closing time</label>
                        <select id="locationClosingTime" class="form-control" ng-model="data.store.locationClosingTime">
                            <option value="">-- Select closing time --</option>
                            <option value="00:00:00">00:00</option>
                            <option value="01:00:00">01:00</option>
                            <option value="02:00:00">02:00</option>
                            <option value="03:00:00">03:00</option>
                            <option value="04:00:00">04:00</option>
                            <option value="05:00:00">05:00</option>
                            <option value="06:00:00">06:00</option>
                            <option value="07:00:00">07:00</option>
                            <option value="08:00:00">08:00</option>
                            <option value="09:00:00">09:00</option>
                            <option value="10:00:00">10:00</option>
                            <option value="11:00:00">11:00</option>
                            <option value="12:00:00">12:00</option>
                            <option value="13:00:00">13:00</option>
                            <option value="14:00:00">14:00</option>
                            <option value="15:00:00">15:00</option>
                            <option value="16:00:00">16:00</option>
                            <option value="17:00:00">17:00</option>
                            <option value="18:00:00">18:00</option>
                            <option value="19:00:00">19:00</option>
                            <option value="20:00:00">20:00</option>
                            <option value="21:00:00">21:00</option>
                            <option value="22:00:00">22:00</option>
                            <option value="23:00:00">23:00</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="deviceType" class="control-label">Device type <span class="man">*</span></label>
                        <select ng-required="isConfigAdminUser" id="deviceType" class="form-control" ng-model="data.store.deviceType">
                            <option value="">-- Select device type --</option>
                            <option value="0">TP 3020</option>
                            <option value="1">TP 841</option>
                            <option value="2">TP 841</option>
                            <option value="3">TP 841</option>
                            <option value="4">TP 1043</option>
                            <option value="5">TP 1043</option>
                            <option value="6">TP 3600</option>
                            <option value="7">TP Archer C7</option>
                            <option value="8">DIR 505</option>
                            <option value="9">Thin Client Giada</option>
                            <option value="10">Thin Client GB</option>
                            <option value="11">Thin Client MSI</option>
                            <option value="12">Thin Client Zotac</option>
                            <option value="13">TP 8968</option>
                            <option value="14">TP 3420</option>
                            <option value="15">TP Archer C20(US)</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <div class="ui-select-input-group">
                            <div class="cell location form-group">
                                <label for="installerId" class="control-label">Installer person<span class="man">*</span></label>
                                <ui-select id="installerId" ng-required="true" ng-model="data.store.installerId" theme="selectize">
                                    <ui-select-match placeholder="Select a installer person name">
                                        {{$select.selected.name || $select.search}}
                                    </ui-select-match>
                                    <ui-select-choices repeat="salesPerson.userName as salesPerson in data.i2e1SalesPersons | propsFilter: {name: $select.search}"
                                                       value="{{salesPerson.userName}}"
                                                       refresh-delay="0">
                                        <span ng-bind-html="salesPerson.name | highlight: $select.search"></span>
                                        &nbsp;&nbsp;-&nbsp;&nbsp;
                                        <small ng-bind-html="salesPerson.userName | highlight: $select.search"></small>
                                    </ui-select-choices>
                                </ui-select>
                            </div>
                        </div>
                    </div>
                    <div class="form-group">
                        <label for="locationCloseDate" class="control-label">Close date</label>
                        <input type="date" class="form-control" id="locationCloseDate" ng-model="data.store.locationCloseDate" />
                    </div>
                    <div class="form-group">
                        <label for="closeReason" class="control-label">Closure reason</label>
                        <textarea class="form-control" rows="3" placeholder="Closure Reason" id="closeReason" ng-model="data.store.closeReason"></textarea>
                    </div>
                    <div class="form-group">
                        <label for="position" class="control-label">Position</label>
                        <input class="form-control" id="position" ng-model="data.store.location" placeholder="Position" />
                    </div>
                    <div class="form-group">
                        <label for="deviceMac" class="control-label">Device MAC</label>
                        <input class="form-control" id="deviceMac" ng-model="data.store.deviceMac" placeholder="00:00:00:00:00" />
                    </div>
                    <div class="form-group">
                        <label for="deviceVersion" class="control-label">Device version</label>
                        <select id="deviceVersion" class="form-control" ng-model="data.store.deviceVersion">
                            <option value="">-- Select device version --</option>
                            <option value="0">Testing/Development</option>
                            <option value="1">1</option>
                            <option value="1.5">1.5</option>
                            <option value="2">2</option>
                            <option value="2.4">2.4</option>
                            <option value="3">3</option>
                            <option value="4">4</option>
                            <option value="5">5</option>
                            <option value="6">6</option>
                            <option value="7">7</option>
                            <option value="8">8</option>
                            <option value="9">9</option>
                            <option value="10">10</option>
                            <option value="11">11</option>
                            <option value="12">12</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="productName" class="control-label">Product</label>
                        <select id="productName" class="form-control" ng-model="data.store.productName">
                            <option value="1">i2e1</option>
                            <option value="10001">WIOM Basic</option>
                            <option value="10002">WIOM Magic(DIY)</option>
                            <option value="10003">WIOM PDO</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="salesChannel" class="control-label">Sales Channel</label>
                        <select id="salesChannel" class="form-control" ng-model="data.store.salesChannel">
                            <option value="">-- Select Channel --</option>
                            <option value="1">Direct Offline Sales</option>
                            <option value="2">Online</option>
                            <option value="3">Distributor</option>
                            <option value="4">Strategic Partner</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="salesChannelName" class="control-label">Sales Channel Name</label>
                        <input class="form-control" id="salesChannelName" ng-model="data.store.salesChannelName" placeholder="Amazon" />
                    </div>
                    <div class="form-group">
                        <label for="legalBusinessName" class="control-label">Legal Business Name</label>
                        <input class="form-control" id="legalBusinessName" ng-model="data.store.legalBusinessName" placeholder="Legal Business Name" />
                    </div>
                    <div class="form-group">
                        <label for="legalBusinessAddress" class="control-label">Legal Business Address</label>
                        <input class="form-control" id="legalBusinessAddress" ng-model="data.store.legalBusinessAddress" placeholder="Legal Business Address" />
                    </div>
                    <div class="form-group">
                        <label for="gstNumber" class="control-label">GST Number</label>
                        <input class="form-control" id="gstNumber" ng-model="data.store.gstNumber" placeholder="GST Number" />
                    </div>
                </div>
            </div>
            <div class="form-group">
                <div class="pull-right">
                    <button ng-disabled="metadata.input.$error.required" ng-click="data.tab = 'partner_contacts';data.tab_head='Partner Contacts'" class="btn btn-primary"><span class="glyphicon glyphicon-arrow-left"></span>  Back</button>
                    <button ng-disabled="metadata.input.$error.required" ng-click="data.saveStoreDetails(data)" class="btn btn-primary">Save & Next <span class="glyphicon glyphicon-arrow-right"></span></button>
                </div>
            </div>
        </div>
        <!-- store contacts -->
        <div class="tab-content" ng-show="data.tab === 'store_contacts'">
            <div ng-if="data.storeContactList.length> 0" class="row">
                <table class="table table-striped">
                    <thead>
                        <tr>
                            <th>Type</th>
                            <th>Name</th>
                            <th>Email</th>
                            <th>Mobile</th>
                            <th>Portal Access</th>
                            <th></th>
                            <th></th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr ng-repeat="contact in data.storeContactList">
                            <td>{{contact.classification}}</td>
                            <td>{{contact.name}}</td>
                            <td>{{contact.classification == null || contact.classification == "" ? contact.username : contact.email}}</td>
                            <td>{{contact.mobile}}</td>
                            <td>{{contact.active ? 'Yes':'No'}}</td>
                            <td>
                                <a ng-hide="contact.classification == 'manager' || contact.classification == 'owner'" class="edit_link glyphicon glyphicon-edit" title="Edit" ng-click="data.selectedSotreContact = contact"></a>
                            </td>
                            <td>
                                <a ng-hide="contact.classification == 'manager' || contact.classification == 'owner'" class="edit_link glyphicon glyphicon-remove" title="Delete" confirm-click ng-click="data.removeStoreContact(contact,data,data.store.nasid)"></a>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>

            <div class="row" ng-show="data.selectedSotreContact.userId">
                <div class="form-group">
                    <label for="contact_name" class="control-label">Classification <span class="man">*</span></label>
                    <input class="form-control" id="contact_name" ng-model="data.selectedSotreContact.classification" placeholder="Generic" disabled/>
                </div>
                <div class="form-group">
                    <label for="contact_name" class="control-label">Contact Name <span class="man">*</span></label>
                    <input class="form-control" id="contact_name" ng-model="data.selectedSotreContact.name" placeholder="Enter a valid Name" />
                </div>
                <div class="form-group">
                    <label for="contact_email" class="control-label">Contact Email <span class="man">*</span></label>
                    <input class="form-control" id="contact_email" ng-change="data.changed_user(data)" ng-model="data.selectedSotreContact.username" placeholder="Enter a valid email or N/A" />
                </div>
                <div class="form-group">
                    <label for="contact_mobile" class="control-label">Contact Mobile <span class="man">*</span></label>
                    <input class="form-control" id="contact_mobile" ng-model="data.selectedSotreContact.mobile" placeholder="Enter 10 digit valid mobile number" />
                </div>
                <div class="form-group">
                    <label for="contact_active" class="control-label">Portal Access </label>
                    <input type="checkbox" id="contact_active" ng-change="data.changed_access(data)" ng-model="data.selectedSotreContact.active" ng-true-value="1" ng-false-value="0" />
                </div>
                <div class="form-group" style="margin-top:2rem;">
                    <div class="pull-right">
                        <button ng-disabled="metadata.input.$error.required" ng-click="data.updateStoreContactPersons(data,data.store.nasid)" class="btn btn-primary">Update</button>
                    </div>
                </div>
            </div>

            <div class="row">
                <div class="form-group" style="margin-top:2rem;">
                    <div class="pull-right">
                        <button ng-disabled="metadata.input.$error.required" ng-click="data.tab='store_info';data.tab_head='Store Info'" class="btn btn-primary"><span class="glyphicon glyphicon-arrow-left"></span> Back</button>
                        <button ng-disabled="metadata.input.$error.required" ng-click="data.selectedSotreContact = {userId: -1,username: 'N/A',name: '', mobile: '',active: 0}" class="btn btn-primary">Add New</button>
                        <button ng-disabled="metadata.input.$error.required" ng-click="cancel()" class="btn btn-primary">Close</button>
                    </div>
                </div>
            </div>
        </div>

    </div>
</script>

<style type="text/css">
    .filters .ui-select-container .ui-select-choices.ui-select-dropdown.selectize-dropdown {
        margin-top: -10px;
    }

    .filters .ui-select-container .selectize-input {
        overflow: hidden;
    }

    .pac-container {
        z-index: 99999;
    }

    .auto-file-upload.preview-section {
        max-width: 50%;
    }

        .auto-file-upload.preview-section .upload_buttons {
            position: absolute;
            /*left: 0;*/
            right: 0;
            top: 0;
            opacity: 0.7;
            -webkit-transition: visibility 0s, opacity 0.5s linear;
            transition: visibility 0s, opacity 0.5s linear;
        }

        .auto-file-upload.preview-section:hover {
            cursor: pointer;
        }

            .auto-file-upload.preview-section:hover div.upload_buttons {
                width: 100%;
                height: 100%;
                background: #000;
                display: flex;
                align-items: center;
                justify-content: center;
            }

    .modal-content .auto-file-upload.preview-section .preview-container .partner_logo_result_container img.partnerLogoImage {
        max-width: 100%;
        margin: auto;
    }

    button.close-modal {
        border-radius: 50%;
        border: none;
        position: absolute;
        top: 2px;
        right: 10px;
        background: transparent;
        color: #fff;
        font-size: 2.4rem;
        font-weight: 100;
    }
</style>
<script type="text/javascript">
    $(function () {
        $("table").stickyTableHeaders({ fixedOffset: 0, scrollableArea: $("#admin_container")[0] });
    });
</script>
