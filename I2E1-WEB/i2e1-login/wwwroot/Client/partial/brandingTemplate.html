<div class="login-container i2e1-container">
    <img class="logo" src="../../images/logo.png" />
    <form id="loginForm" class="i2e1-form" onsubmit="return false;">
        <div class="details">
            <h6 class="header row">Branding template setting</h6>
            <div class="row">
                <div class="col-md-5">
                    <div class="row cell">
                        <select placeholder="Select client" class="form-control" ng-model="selectedClient" ng-change="fetchAllPartners()">
                            <option ng-repeat="client in clients | orderBy:client.clientName:true" value="{{client.clientId}}">{{client.clientName}}</option>
                        </select>
                    </div>
                    <div class="row cell">
                        <select placeholder="Select brand" class="form-control" ng-model="selectedBrand" ng-change="initializeUpload()">
                            <option ng-repeat="partner in partners" value="{{partner.partnerId}}">{{partner.partnerName}}</option>
                        </select>
                    </div>
                    <div class="row cell">
                        <select placeholder="Select template" class="form-control" ng-model="selectedTemplate" ng-change="fetchTemplateDetails(setting.settingId)">
                            <option ng-repeat="template in templates" value="{{template.id}}">{{template.templateName}}</option>
                        </select>
                    </div>
                    <div class="row cell" title="template id: {{templateDetail.id}}">
                        <input placeholder="Name" name="template_name" type="text" class="form-control" ng-model="templateDetail.templateName" />
                    </div>
                    <div class="row cell">
                        <input placeholder="Path" name="template_path" type="text" disabled class="form-control" ng-model="templateDetail.templatePath" />
                    </div>
                    <div class="row">
                        <div class="partition">
                            <div class="left-part"></div>
                            <div class="text">Images</div>
                            <div class="right-part"></div>
                        </div>
                    </div>
                    <div class="row">
                        <div ng-repeat="image in templateImages" class="col-md-2 cell templateImages" title="click to copy">
                            <img src="{{image}}" ng-click="copyToClipboard($event, $index)" />
                            <input type="text" id="temp_img_{{$index}}" value="/Proxy/GetContent.ashx?url={{image | encodeURIComponent}}" style="opacity:0;"/>
                        </div>
                    </div>
                    <div class="result_container"></div>
                    <div class="row cell add-image">
                        <div class="col-md-5">Add image</div>

                        <div class="result_container"></div>
                        <div class="upload-here col-md-5 col-md-offset-2">
                            <div class="browser">
                                <label>
                                    <input type="file" name="file" id="file_upload" title="Click to add File">
                                </label>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-6 col-md-offset-1">
                    <div class="row content">
                        <div class="partition">
                            <div class="left-part"></div>
                            <div class="text">Template Code</div>
                            <div class="right-part"></div>
                        </div>
                        <textarea placeholder="Content" name="template_content" class="form-control" ng-model="templateDetail.templateContent" />
                    </div>
                    
                </div>
            </div>

            <div class="row row-2">
                <div class="cell col-md-offset-8 col-md-4">
                    <button ng-click="preview($event)" class="btn btn-primary btn-lg login">Preview</button>
                </div>
            </div>

            <div class="row">
                <div class="cell col-md-offset-8 col-md-2">
                    <button class="btn btn-primary btn-lg" 
                            ng-confirm-click='{"title":"Are you sure?", "textContent" : "", "type":"alert-warning", "ok" : "Update", "cancel" : "Cancel"}'
                            ng-click="updateTemplate()">Update</button>
                </div>
                <div class="cell col-md-2">
                    <button ng-click="createTemplate()" class="btn btn-primary btn-lg">Save as new</button>
                </div>
            </div>
        </div>
    </form>
    
</div>

<div class="footer">
    For any assistance, call us immediately on +91-8880322222 or
    mail us at: <EMAIL>
</div>

<div class="footer" id="loginWebsite" style="display:none" ng-click="previewOff()">
    <iframe  width="1080" height="2160"></iframe>
</div>

<style>

    #file_upload {
        border-bottom: none;
    }
    .row {
        margin: 0;
    }

    .row-2 {
        margin-top: 2rem;
    }

    .add-image {
        margin-top: 3rem;
    }
    .templateImages {
        cursor: pointer;
        height: 7.2rem !important;
    }

    .templateImages img {
        width: 7rem;
        height: 7rem;
    }

    .templateImages img:hover {
        width: 7.5rem;
        height: 7.5rem;
    }

    .i2e1-container .i2e1-form .details {
        width: 80%;
        padding: 1rem;
    }
    .i2e1-container .i2e1-form .content {
        height: fit-content;
    }
    .i2e1-container .i2e1-form .content textarea {
        resize: vertical;
        height: 30rem;
        background: #f6f6f6;
        padding: 1rem;
    }

    .i2e1-container .i2e1-form .partition .left-part, .i2e1-container .i2e1-form .partition .right-part {
        width: 40%;
    }

</style>