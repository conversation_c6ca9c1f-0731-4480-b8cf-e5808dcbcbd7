<style>
    .editReportTable{
        width: 100%;
    }
    .editReportTable td{
        padding: 8px;
    }
</style>
<div ng-if="featureEnabled(54)" class="container-fluid">
    <div class="col-md-12 config-row">
        <h5><i class="fa fa-key" aria-hidden="true"></i>Subscribe for Reports</h5>
        <table class="table">
            <tr>
                <th>Scheduled At</th>
                <th>Report Name</th>
                <th>Receivers</th>
                <th></th>
            </tr>
            <tr ng-repeat="report in customReports">
                <td>{{report.startDate.yyyymmdd()}}</td>
                <td>{{report.reportName}}</td>
                <td><div style="width: 400px;white-space: nowrap;overflow: hidden;text-overflow: ellipsis;" title="{{report.receivers}}">{{report.receivers}}</div></td>
                <td>
                    <a title="Edit Trigger" ng-click="editCustomReport(report)" class="glyphicon glyphicon-edit"></a>
                    <a title="Execute Now" ng-click="executeNow(report)" class="glyphicon glyphicon-play-circle"></a>
                </td>
            </tr>
        </table>
        <a ng-click="customReports.push({})">Add more</a>
    </div>

    <div class="col-md-12 config-row">
        <h5><i class="fa fa-key" aria-hidden="true"></i>Subscribe for SMS</h5>
        <table class="table">
            <tr>
                <th>Scheduled At</th>
                <th>Report Name</th>
                <th>Receivers</th>
                <th></th>
            </tr>
            <tr ng-repeat="report in customSMSReports">
                <td>{{report.startDate.yyyymmdd()}}</td>
                <td>{{report.reportName}}</td>
                <td><div style="width: 400px;white-space: nowrap;overflow: hidden;text-overflow: ellipsis;" title="{{report.receivers}}">{{report.receivers}}</div></td>
                <td>
                    <a title="Edit Trigger" ng-click="editCustomSmsReport(report)" class="glyphicon glyphicon-edit"></a>
                    <a title="Execute Now" ng-click="executeSmsNow(report)" class="glyphicon glyphicon-play-circle"></a>
                </td>
            </tr>
        </table>
        <a ng-click="customSMSReports.push({})">Add more</a>
    </div>
</div>
<script type="text/ng-template" id="/editReport.html">
    <div style="padding: 30px 20px">
        <button class="btn" style="float:right;background: transparent;" ng-click="cancel()"><b>X</b></button>
        <div>
            <div>
                <img width="75px" src="../images/logo.png" />
                <p style="color:#0c4da2;padding:10px 0;">{{data.report.reportName ? data.report.reportName : 'New Report'}}</p>
            </div>
        </div>
        <div>
            <table class="editReportTable">
                <tr>
                    <td>Execution Time</td><td><select ng-model="data.report.executionTime" placeholder="Time of day" class="form-control" required ng-options="interval as interval.name + ' Hours' for interval in data.intervals track by interval.value"></select></td>
                </tr>
                <tr>
                    <td>Scheduled At</td><td><div class="input-group">
                    <input type="text" class="form-control" datepicker-popup="yyyy/MM/dd" ng-model="data.report.startDate" is-open="toOpened" datepicker-options="data.dateOptions" ng-required="true" close-text="Close" />
                    <span class="input-group-btn">
                        <button style="height: 30px;padding: 3px 10px;" type="button" class="btn btn-default" ng-click="fromOpened=false;toOpened=true;data.openDatePicker($event)">
                            <i class="glyphicon glyphicon-calendar"></i>
                        </button>
                    </span>
                </div></td>
                </tr>
                <tr>
                    <td>Report Name</td><td><input ng-model="data.report.reportName" class="form-control" /></td>
                </tr>
                <tr>
                    <td>Subject</td>
                    <td><input ng-model="data.report.subject" class="form-control" /></td>
                </tr>
                <tr>
                    <td>Query</td><td><textarea ng-model="data.report.query" class="form-control"></textarea></td>
                </tr>
                <tr>
                    <td>Receivers</td>
                    <td><textarea ng-model="data.report.receivers" class="form-control"></textarea></td>
                </tr>
                <tr>
                    <td>Sheet Name</td>
                    <td><input ng-model="data.report.sheetName" class="form-control" /></td>
                </tr>
                <tr>
                    <td>Template Path</td>
                    <td><input ng-model="data.report.templatePath" class="form-control" /></td>
                </tr>
                <tr>
                    <td>Template Filler</td>
                    <td><textarea ng-model="data.report.templateFillerQuery" class="form-control"></textarea></td>
                </tr>
                <tr>
                    <td>Repeat Cycle</td>
                    <td><select ng-model="data.report.cycle" class="form-control">
                    <option value="30">Half Hourly</option>
                    <option value="60">Hourly</option>
                    <option value="1440">Daily</option>
                    <option value="10080">Weekly</option>
                    <option value="21600">Fort-nightly</option>
                    <option value="43200">Monthly</option>
                </select></td>
                </tr>
                <tr>
                    <td>Query Type</td>
                    <td>
                        <select ng-model="data.report.queryType" class="form-control">
                            <option value="0">SQL</option>
                            <option value="1">Big Query Legacy SQL</option>
                            <option value="2">Big Query Standard SQL</option>
                            <option value="3">ClickHouse</option>
                            <option value="4">Athena</option>
                        </select>
                    </td>
                </tr>
            </table>
        </div>
        <div style="text-align:right;margin-top: 15px;">
            <button ng-click="ok()" class="btn-primary blue">Done</button>
            <button ng-click="cancel()" class="btn-warning">Cancel</button>
        </div>
    </div>
</script>
<script type="text/ng-template" id="/editSmsReport.html">
    <div style="padding: 30px 20px">
        <button class="btn" style="float:right;background: transparent;" ng-click="cancel()"><b>X</b></button>
        <div>
            <div>
                <img width="75px" src="../images/logo.png" />
                <p style="color:#0c4da2;padding:10px 0;">{{data.report.reportName ? data.report.reportName : 'New Report'}}</p>
            </div>
        </div>
        <div>
            <table class="editReportTable">
                <tr>
                    <td>Execution Time</td>
                    <td><select ng-model="data.report.executionTime" placeholder="Time of day" class="form-control" required ng-options="interval as interval.name + ' Hours' for interval in data.intervals track by interval.value"></select></td>
                </tr>
                <tr>
                    <td>Scheduled At</td>
                    <td>
                        <div class="input-group">
                            <input type="text" class="form-control" datepicker-popup="yyyy/MM/dd" ng-model="data.report.startDate" is-open="toOpened" datepicker-options="data.dateOptions" ng-required="true" close-text="Close" />
                            <span class="input-group-btn">
                                <button style="height: 30px;padding: 3px 10px;" type="button" class="btn btn-default" ng-click="fromOpened=false;toOpened=true;data.openDatePicker($event)">
                                    <i class="glyphicon glyphicon-calendar"></i>
                                </button>
                            </span>
                        </div>
                    </td>
                </tr>
                <tr>
                    <td>Report Name</td>
                    <td><input ng-model="data.report.reportName" class="form-control" /></td>
                </tr>
                <tr>
                    <td>Sms Text</td>
                    <td><textarea ng-model="data.report.smsText" class="form-control"></textarea></td>
                </tr>
                <tr>
                    <td>Query</td>
                    <td><textarea ng-model="data.report.query" class="form-control"></textarea></td>
                </tr>
                <tr>
                    <td>Receivers</td>
                    <td><textarea ng-model="data.report.receivers" class="form-control"></textarea></td>
                </tr>
                <tr>
                    <td>Repeat Cycle</td>
                    <td>
                        <select ng-model="data.report.cycle" class="form-control">
                            <option value="30">Half Hourly</option>
                            <option value="60">Hourly</option>
                            <option value="1440">Daily</option>
                            <option value="10080">Weekly</option>
                            <option value="21600">Fort-nightly</option>
                            <option value="43200">Monthly</option>
                        </select>
                    </td>
                </tr>
                <tr>
                    <td>Query Type</td>
                    <td>
                        <select ng-model="data.report.queryType" class="form-control">
                            <option value="0">SQL</option>
                            <option value="1">Big Query Legacy SQL</option>
                            <option value="2">Big Query Standard SQL</option>
                            <option value="3">ClickHouse</option>
                            <option value="4">Athena</option>
                        </select>
                    </td>
                </tr>
                <tr>
                    <td>Send Via</td>
                    <td>
                        <select ng-model="data.report.sendVia" class="form-control">
                            <option value="0">SMS</option>
                            <option value="1">WhatsApp Customer</option>
                            <option value="2">Voice</option>
                            <option value="3">WhatsApp Partner</option>
                        </select>
                    </td>
                </tr>
                <tr>
                    <td>Header</td>
                    <td><input ng-model="data.report.header" class="form-control" /></td>
                </tr>
                <tr>
                    <td>Footer</td>
                    <td><input ng-model="data.report.footer" class="form-control" /></td>
                </tr>
                <tr>
                    <td>Sender Id</td>
                    <td><input ng-model="data.report.senderId" class="form-control" /></td>
                </tr>
                <tr>
                    <td>Dont Resend for Days</td>
                    <td><input ng-model="data.report.restingDays" class="form-control" /></td>
                </tr>
            </table>
        </div>
        <div style="text-align:right;margin-top: 15px;">
            <button ng-click="ok()" class="btn-primary blue">Done</button>
            <button ng-click="cancel()" class="btn-warning">Cancel</button>
        </div>
    </div>
</script>