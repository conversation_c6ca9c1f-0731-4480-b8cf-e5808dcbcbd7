<div class="sms-container" feature-toggle="7">
    <div class="row inner-container panel-group" id="accordion">
        <div class="col-md-12 no-pad create-sms panel panel-default" ng-init="createSMS()">
            <div class="col-md-12 no-pad block-header collapsed" data-parent="#accordion" data-toggle="collapse" data-target="#create_sms_form">
                <h3>Create New SMS Campaign <span ng-if="staticDetails.smses_left">{{staticDetails.smses_left ? staticDetails.smses_left : 0}} sms left</span></h3>
            </div>
            <div class="col-md-12 no-pad create_sms_form panel-collapse collapse" id="create_sms_form">
                <div class="pull-right buttons col-md-4 ng-scope" ng-if="!data.smsSent">
                    <button ng-click="saveSMS(data.sms)" class="btn btn-primary-green pull-right">Send</button>
                </div>
                <div class="col-md-8 campaign-selection">
                    <div class="ui-select-input-group">
                        <div class="cell location form-group">
                            <label for="create_campaign_select">Campaign Name</label>
                            <ui-select id="create_campaign_select" ng-required="true" ng-disabled="data.smsSent" ng-model="data.sms.campaign" theme="selectize" ng-change="data.selectCamapaign(data.sms,$select)">
                                <ui-select-match placeholder="Enter a campaign name or choose from existing">
                                    {{$select.selected.name || $select.search}}
                                </ui-select-match>
                                <ui-select-choices repeat="campaign in campaigns | propsFilter: {name: $select.search}" refresh="data.refreshResults($select)" refresh-delay="0">
                                    <span ng-bind-html="campaign.name | highlight: $select.search"></span>
                                </ui-select-choices>
                            </ui-select>
                            <p id="show-campaign-error" style="display:none;">Please give a name for your campaign -or- select an existing one to copy the message content.</p>
                        </div>
                    </div>
                </div>


                <div class="col-md-8 message-box">
                    <label for="message-text-area">SMS Text</label>
                    <textarea id="message-text-area" class="form-control message-text-area message-text"
                              ng-disabled="data.smsSent"
                              placeholder="Enter message here"
                              ng-chnage="data.lengthCheck()"
                              ng-model="data.sms.message"></textarea>
                </div>
                <div class="col-md-8">
                    <div class="col-md-12 right-align sms_size">
                        Size-{{(data.sms.message.length) || 0}}&nbsp;&nbsp;&nbsp;Messages-{{Math.ceil(data.sms.message.length/160) || 0}}
                    </div>
                </div>
                <div class="col-md-8">
                    <p id="show-message-box-error" style="display:none;">Message can not be empty.</p>
                </div>

                <div class="col-lg-12">
                    <ul class="nav nav-tabs get-numbers">
                        <li class="active rules" ng-click="data.openTab('rules_tab')"><a href="">Target Wi-Fi Phone Numbers</a></li>
                        <li class="my_numbers"><a href="" ng-click="data.openTab('my_numbers')">Upload Your Phone Numbers</a></li>
                    </ul>
                    <div class="tab-content">
                        <div class="col-md-12 no-pad tab-pane active" id="rules_tab">
                            <div class="block rules" id="rules">
                                <div class="row form-group">
                                    <div class="col-md-2 sender_title">
                                        <h6>City Filter</h6>
                                    </div>
                                    <div class="col-md-10 locations-selector">
                                        <div class="ui-select-input-group">
                                            <div class="cell location">
                                                <ui-select ng-disabled="data.smsSent" multiple ng-model="data.selectedCity" theme="select2" close-on-select="false" reset-search-input="true" ng-change="getLocationsForCity()">
                                                    <ui-select-match allow-clear="true" placeholder="Select City">
                                                        {{$item.city}}
                                                    </ui-select-match>
                                                    <ui-select-choices repeat="elem in availableCities | propsFilter: {city: $select.search}">
                                                        <span ng-bind-html="elem.city | highlight: $select.search"></span>
                                                    </ui-select-choices>
                                                </ui-select>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="row form-group">
                                    <div class="col-md-2 sender_title">
                                        <h6>Store Filter</h6>
                                    </div>
                                    <div class="col-md-10 locations-selector">
                                        <div class="ui-select-input-group">
                                            <div class="cell location">
                                                <ui-select ng-disabled="data.smsSent" multiple ng-model="data.sms.locations" theme="select2" close-on-select="false" reset-search-input="true" ng-change="data.loadNumbers(data.sms)">
                                                    <ui-select-match allow-clear="true" placeholder="Select Store">
                                                        {{$item.storeName}}
                                                    </ui-select-match>
                                                    <ui-select-choices repeat="router in $parent.positiveNases | propsFilter: {nasid: $select.search, storeName: $select.search} | limitTo: 10">
                                                        <span ng-bind-html="router.storeName | highlight: $select.search"></span>
                                                        <span ng-bind-html="router.city | highlight: $select.search"></span>
                                                        <span ng-bind-html="router.state | highlight: $select.search"></span>
                                                        <span ng-bind-html="router.nasid | highlight: $select.search"></span>
                                                    </ui-select-choices>
                                                </ui-select>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="row sender_details form-group" ng-if="data.sms.rules">
                                    <div class="col-md-2 sender_title">
                                        <h6>Sender Id</h6>
                                    </div>
                                    <div class="col-md-10">
                                        <div class="col-md-4 no-pad">
                                            <select class="form-control" ng-if="!data.smsSent && staticDetails.senderIds.length" ng-model="data.sms.senderId" theme="select2">
                                                <option value="" ng-selected="true">Promotional sms</option>
                                                <option ng-if="senderId != 'BULKSMS'" ng-repeat="senderId in staticDetails.senderIds" value="{{senderId}}">{{senderId}}</option>
                                            </select>
                                            <input type="text" ng-disabled="data.smsSent" ng-if="data.smsSent && staticDetails.senderIds.length" ng-model="data.sms.senderId" />
                                            <span ng-if="!staticDetails.senderIds.length && data.smsSent">Sent through promotional gateway</span>
                                            <span ng-if="!staticDetails.senderIds.length && !data.smsSent" title="If no sender ids confiured then sms will be sent through promotional gateway">Promotional sms</span>
                                        </div>
                                    </div>
                                </div>
                                <div class="row form-group">
                                    <div class="col-md-12" style="font-size: 2rem;font-weight: 500;">Target customers who</div>
                                </div>
                                <div class="row form-group" ng-repeat="rule in data.sms.rules" ng-if="rule.title != 'Not Visited Since'">
                                    <div class="col-md-2 rule_title" ng-if="rule.title != 'Visit frequency'"><h6>{{rule.title}}</h6></div>
                                    <div class="col-md-2 rule_title" ng-if="rule.title == 'Visit frequency'"><h6>With {{rule.title}}</h6></div>
                                    <div class="col-md-5" ng-if="rule.title == 'Visited between'"><input class="form-control" type="text" ng-disabled="data.smsSent || data.sms.notVisitedSince" ng-model="rule.from" date-picker="data.startDateOptions" ng-change="data.loadNumbers(data.sms)" /></div>
                                    <div class="col-md-5" ng-if="rule.title == 'Visited between'"><input class="form-control" type="text" ng-disabled="data.smsSent || data.sms.notVisitedSince" ng-model=" rule.to" date-picker="data.endDateOptions" ng-change="data.loadNumbers(data.sms)" /></div>
                                    <div class="col-md-5" ng-if="rule.title == 'Visit frequency'"><input class="form-control" type="text" ng-disabled="data.smsSent || data.sms.notVisitedSince" ng-model="rule.from" ng-change="data.loadNumbers(data.sms)" placeholder="From(Ex. 5)" /></div>
                                    <div class="col-md-5" ng-if="rule.title == 'Visit frequency'"><input class="form-control" type="text" ng-disabled="data.smsSent || data.sms.notVisitedSince" ng-model="rule.to" ng-change="data.loadNumbers(data.sms)" placeholder="To(Ex. 10)" /></div>
                                </div>
                                <hr class="or-separator">
                                <div class="row form-group">
                                    <div class="col-md-12" style="font-size: 2rem;font-weight: 500;">Target customers who</div>
                                </div>
                                <div class="row form-group" ng-repeat="rule in data.sms.rules" ng-if="rule.title == 'Not Visited Since'">
                                    <div class="col-md-2 rule_title"><h6>Did {{rule.title}}</h6></div>
                                    <div class="col-md-5" ng-if="rule.title == 'Not Visited Since'"><input class="form-control" type="text" ng-disabled="data.smsSent || !data.sms.notVisitedSince" ng-change="data.loadNumbers(data.sms)" ng-model="rule.from" date-picker="data.startDateOptions" /></div>
                                    <div class="col-md-5" ng-if="rule.title == 'Not Visited Since'"><input type="checkbox" ng-if="!data.smsSent" ng-model="data.sms.notVisitedSince" ng-change="data.loadNumbers(data.sms)" /></div>
                                    <div ng-if="rule.error" class="col-md-8 col-md-offset-3 rules_error" ng-bind-html="rule.error"></div>
                                </div>
                                <div class="row" ng-if="!data.sms.rules">
                                    message was sent using upload numbers
                                </div>
                                <div ng-if="data.sms.selectedNumberCount >= 0" class="row col-md-12" style="float:none;">
                                    <h6 class="p-margin-10">Total Mobile numbers in selected locations : <span class="red-text">{{data.sms.selectedNumberCount}}</span> <b ng-if="staticDetails.smses_left">({{staticDetails.smses_left ? staticDetails.smses_left : 0}} sms left)</b></h6>
                                </div>
                            </div>

                        </div>
                        <div id="my_numbers" class="col-md-12 no-pad tab-pane">
                            <div class="block" id="readNumberCSVContainer">
                                <div class="row sender_details form-group">
                                    <div class="col-md-2 sender_title">
                                        <h6>Sender Id</h6>
                                    </div>
                                    <div class="col-md-5">
                                        <select class="form-control" ng-if="!data.smsSent && staticDetails.senderIds.length" ng-model="data.sms.senderId" theme="select2">
                                            <option value="" ng-selected="true">Promotional sms</option>
                                            <option ng-if="senderId != 'OPEN-BEERCF' && senderId != 'BULKSMS'" ng-repeat="senderId in staticDetails.senderIds" value="{{senderId}}">{{senderId}}</option>
                                        </select>
                                        <input type="text" ng-disabled="data.smsSent" ng-if="data.smsSent && staticDetails.senderIds.length" ng-model="data.sms.senderId" />
                                        <span ng-if="!staticDetails.senderIds.length && data.smsSent">Sent through promotional gateway</span>
                                        <span ng-if="!staticDetails.senderIds.length && !data.smsSent" title="If no sender ids confiured then sms will be sent through promotional gateway">Promotional sms</span>
                                    </div>
                                </div>
                                <div class="row form-group">
                                    <div class="col-md-12">
                                        <label for="csvreader">
                                            Please upload only csv files having phone numbers in one column Maximun limit at once is 5000 numbers.
                                        </label>
                                        <input type="file" id="csvreader" file-reader="data.readNumberCSV">
                                    </div>
                                </div>
                                <div ng-if="data.sms.selectedNumberCount >= 0" class="row total_numbers form-group">
                                    <div class="col-md-12">
                                        <h6 class="p-margin-10">Total Mobile numbers in selected locations : <span class="red-text">{{data.sms.selectedNumberCount}}</span> <b ng-if="staticDetails.smses_left">({{staticDetails.smses_left ? staticDetails.smses_left : 0}} sms left)</b></h6>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="buttons col-md-12 no-pad" ng-if="!data.smsSent">
                        <button ng-click="saveSMS(data.sms)" class="btn btn-primary-green pull-right">Send</button>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-12 no-pad panel panel-default sms-history">
            <div class="col-md-12 no-pad block-header" data-parent="#accordion" data-toggle="collapse" data-target="#sms-history-items">
                <h3>
                    SMS Campaign History
                </h3>
            </div>

            <div class="sms-items panel-collapse collapse in" id="sms-history-items">
                <div class="sms-filter">
                    <select ng-model="data.smsHistoryTarget" ng-change="getFilterSmsHistory()">
                        <!--<option value="" selected>All</option>-->
                        <option value="wifi" selected>Wi-Fi SMS History</option>
                        <option value="uploadlist">Upload SMS History</option>
                    </select>
                </div>
                <div class="col-md-12" infinite-scroll='loadMoreSMS()' infinite-scroll-container='"#admin_container"' infinite-scroll-parent='true' infinite-scroll-disabled='infiniteScrollDisabled'>
                    <div class="sms-row">
                        <div ng-repeat="sms in smses">
                            <div ng-click="createGraph(sms,$event)" data-toggle="collapse" data-target="#collapssibleDiv{{sms.id}}" aria-expanded="false" aria-controls="collapseExample" class="col-md-12 sms-details collapsed">
                                <div class="col-md-1 no-pad sms-icon"><img class="sms-img" src="../../images/yellow-sms-icon-32.png" /></div>
                                <div class="col-md-7 sms-content">
                                    <!--<h4 title="{{sms.senderId == 'BULKSMS' ? 'Promotinal SMS' : 'Transactional SMS'}}">{{sms.senderId == 'BULKSMS' ? 'XX-XXXXXX' : 'XX-'+sms.senderId}}</h4>-->
                                    <h4 title="{{sms.campaign.nam
                                    <h4 title="{{sms.campaign.nam

                                    <h4 title="{{sms.campaign.name}}">
                                        {{sms.campaign.name}}
                                    </h4>
                                    <p>{{truncate(sms.message, 80)}}..</p>
                                    <p ng-repeat="rule in sms.rules" ng-if="rule.from">
                                        <span class="rule_title" ng-bind="rule.title"></span>
                                        <span ng-if="rule.title == 'Visited between'">{{rule.from}}</span>
                                        <span ng-if="rule.title == 'Visited between'"> to {{rule.to}}</span>
                                        <span ng-if="rule.title == 'Visit frequency'">{{rule.from}}</span>
                                        <span ng-if="rule.title == 'Visit frequency'"> to {{rule.to}}</span>
                                        <span ng-if="rule.title == 'Not Visited Since'">{{rule.from}}</span>
                                    </p>
                                </div>
                                <div class="col-md-4 no-pad sms-timings right-align">
                                    <span>Sent on {{sms.scheduleDateView}} at {{sms.scheduleTime}}</span>
                                    <span class="icon-arrow"></span>
                                </div>
                            </div>
                            <div class="col-md-12 no-pad collapse" id="collapssibleDiv{{sms.id}}">
                                <div class="col-md-6">
                                    <div class="col-md-12 no-pad">
                                        <textarea class="form-control message-text-area"
                                                  ng-disabled="true"
                                                  placeholder="Enter message here"
                                                  class="message-text"
                                                  ng-model="sms.message"
                                                  disabled></textarea>
                                        <div class="col-md-12 right-align sms_size">
                                            Size-{{sms.message.length || 0}}&nbsp;&nbsp;&nbsp;Messages-{{Math.ceil(sms.message.length/160) || 0}}
                                        </div>
                                        <span tooltip="{{tooltipText}}"
                                              tooltip-trigger
                                              tooltip-animation="false"
                                              tooltip-placement="left"
                                              class="btn-clipboard"
                                              ngclipboard
                                              data-clipboard-text="{{sms.message}}"
                                              ngclipboard-success="onCopySuccess()">Copy</span>
                                    </div>
                                    <div class="col-md-12 no-pad all_locations" ng-if="sms.locations.length">
                                        <div class="col-md-2 no-pad">
                                            <h3>Locations:</h3>
                                        </div>
                                        <div class="col-md-10 no-pad">
                                            <ul class="list-inline">
                                                <li ng-repeat="location in sms.locations">
                                                    {{location.storeName}}
                                                </li>
                                            </ul>
                                        </div>
                                    </div>
                                    <div class="col-md-12 no-pad all_locations" ng-if="sms.rules.length">
                                        <div class="col-md-2 no-pad">
                                            <h3>Rules:</h3>
                                        </div>
                                        <ul class="col-md-10 no-pad">
                                            <li ng-repeat="rule in sms.rules" ng-if="rule.from">
                                                <span class="rule_title" ng-if="rule.title == 'Visit frequency' && rule.from">Customers with {{rule.title.toLowerCase()}}</span>
                                                <span class="rule_title" ng-if="rule.title != 'Not Visited Since' && rule.title != 'Visit frequency'">Customers who {{rule.title.toLowerCase()}}</span>
                                                <span class="rule_title" ng-if="rule.title == 'Not Visited Since'">Customers who did {{rule.title.toLowerCase()}}</span>
                                                <span ng-if="rule.title == 'Visited between'">{{rule.from}}</span>
                                                <span ng-if="rule.title == 'Visited between'"> to {{rule.to}}</span>
                                                <span ng-if="rule.title == 'Visit frequency' && rule.from">{{rule.from}} visits</span>
                                                <span ng-if="rule.title == 'Visit frequency' && rule.to"> to {{rule.to}} visits</span>
                                                <span ng-if="rule.title == 'Not Visited Since'">{{rule.from}}</span>
                                            </li>
                                        </ul>
                                    </div>
                                </div>
                                <div class="col-md-6 ">
                                    <div class="border preview ">
                                        <canvas id="{{'sms_' + sms.id}}" height="100" class="graph"></canvas>
                                    </div>

                                </div>

                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>