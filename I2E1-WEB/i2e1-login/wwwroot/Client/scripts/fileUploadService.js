app.service('fileUploadService', function () {
    this.initializeUpload = function (containerId, resultContainer, nameAppender, success, options) {
        var options = options || {};
        var extraData = {
            nameAppender: nameAppender,
            uploadFolder: ''
        }
        for (var k in options) extraData[k] = options[k];
       
        function add_file(id, file) {
            var template = '' +
              '<div class="file uploadFile">' +
                '<div class="info">' +
                  '<!--#1 - <span class="filename" title="Size: ' + file.size + 'bytes - Mimetype: ' + file.type + '">' + file.name + '</span><br />--><small>Status: <span class="status">Waiting</span></small>' +
                '</div>' +
                '<div class="bar">' +
                  '<div class="progress" style="width:0%"></div>' +
                '</div>' +
              '</div>';
            $(resultContainer).html(template);
        }

        function bytesToSize(bytes) {
            var sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
            if (bytes == 0) return 'n/a';
            var i = parseInt(Math.floor(Math.log(bytes) / Math.log(1024)));
            if (i == 0) return bytes + ' ' + sizes[i];
            return (bytes / Math.pow(1024, i)).toFixed(1) + ' ' + sizes[i];
        }

        function update_file_status(id, status, message) {
            $(resultContainer + ' .uploadFile').find('span.status').html(message).addClass(status);
        }

        function update_file_progress(id, percent) {
            $(resultContainer + ' .uploadFile').find('div.progress').width(percent);
        }

        $(containerId).dmUploader({
            maxFileSize: options.maxSize,
            url: options.hasOwnProperty('uploadUrl') ? options.uploadUrl : '/Client/UploadFile',
            dataType: 'json',
            extraData: extraData,
            onInit: function () {
            },
            onBeforeUpload: function (id) {
                $(resultContainer).show();
                update_file_status(id, '', 'Uploading...');
            },
            onNewFile: function (id, file) {
                if (!options.maxSize) add_file(id, file);
                else if (file.size <= options.maxSize)
                    add_file(id, file);
                else alert("ERR: file exceeding max size " + bytesToSize(options.maxSize))
            },
            onComplete: function () {
            },
            onUploadProgress: function (id, percent) {
                var percentStr = percent + '%';

                update_file_progress(id, percentStr);
            },
            onUploadSuccess: function (id, data) {
                update_file_status(id, 'success', 'Upload Complete');
                success(data);
                update_file_progress(id, '100%');
                $(resultContainer).hide();
            },
            onUploadError: function (id, message) {
                update_file_status(id, 'error', message);
                alert(message);
            },
            onFileTypeError: function (file) {
            },
            onFileSizeError: function (file) {
                alert('File \'' + file.name + '\' cannot be added: size excess limit');
            },
            onFallbackMode: function (message) {
            }
        });
    }
});