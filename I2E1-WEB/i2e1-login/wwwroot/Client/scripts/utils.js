var contactDetails = {
    userId: -1,
    username: '',
    name: '',
    mobile: '',
    active: 0
}

var smsTemplates = {
    'Engage existing consumers': [
        "It was a pleasure hosting you! Look forward to seeing you again :) Here's a special deal for you as a token of our appreciation",
        "Thanks for making our day special by giving us chance to host you. Click here to unlock a special offer for next visit",
        "It's been some time since <brand name> hosted you. Sending a special gift to you to revive our friendship. Click here to unlock your gift",
        "We miss you. Get a complimentary <item name> on the house on your visit before <dd-mmm> Click here to receive the offer",
        "We haven’t seen you in a while. Here's a discount voucher that you can enjoy on your next visit. Click here to save the voucher"
    ],
    'Acquire new ones': [
        "<brand name> is having a Buy 3 Get 1 Free on <item> before <end date> for limited guest only. Click here to save your voucher",
        "For limited guests only - Enjoy 10% off on <item> before <end date> at <brand/outlet name>. Click here to save your offer",
        "Hurry! <brand name> has an awesome deal for you - buy a <item> and get 10% off your next <item>! To avail, save this coupon",
        "Don’t miss out - <Brand name> is offering 20% off on billing of 100 Rs. To become eligible for the offer, click here",
        "Enjoy Speical Happy Hours at <brand name> on this Friday. Click here to unlock your additional discount",
        "This weekend, have a blast at <brand name> with a special offer for only selected guests. Click here to unlock your offer",
        "Shhh! <brand name> is offering 20% off on a billing of 100 Rs to selected customers. Click here to join the lucky list",
        "Buy <item> and get a complimentary <item>! Offer available till <date>. Click here to get your voucher to redeem the offer",
        "Shop for <amount> and get a 10% off immediately! Offer available till <date>. Click here to get your voucher to redeem the offer"
    ]
}

String.prototype.isEmpty = function () {
    return (this.length === 0 || !this.trim());
};

Array.prototype.move = function (old_index, new_index) {
    while (old_index < 0) {
        old_index += this.length;
    }
    while (new_index < 0) {
        new_index += this.length;
    }
    //if (new_index >= this.length) {
    //    var k = new_index - this.length;
    //    while ((k--) + 1) {
    //        this.push(undefined);
    //    }
    //}
    this.splice(new_index, 0, this.splice(old_index, 1)[0]);
    return this; // for testing purposes
};

var getDateString = function (dateString) {
    var scheduleTimeView = new moment.utc(dateString).local().format('Do MMM YYYY')
    return scheduleTimeView;
}

var getTimeString = function (dateString) {
    var scheduleTime = new moment.utc(dateString).local().format('h:mm:ss a')
    return scheduleTime;
}

function removeElementFromArray(array, element) {
    return array.filter(e => e !== element);
}

Array.prototype.insert = function (index, item) {
    this.splice(index, 0, item);
};

function getGooglePlaceDetailsService(place) {
    this.isGooglePlace = function (place) {
        if (!place)
            return false;
        return !!place.place_id;
    }

    this.isContainTypes = function (place, types) {
        var placeTypes,
            placeType,
            type;
        if (!this.isGooglePlace(place))
            return false;
        placeTypes = place.types;
        for (var i = 0; i < types.length; i++) {
            type = types[i];
            for (var j = 0; j < placeTypes.length; j++) {
                placeType = placeTypes[j];
                if (placeType === type) {
                    return true;
                }
            }
        }
        return false;
    }

    this.getAddrComponent = function (place, componentTemplate) {
        var result;
        if (!this.isGooglePlace(place))
            return;
        for (var i = 0; i < place.address_components.length; i++) {
            var addressType = place.address_components[i].types[0];
            if (componentTemplate[addressType]) {
                result = place.address_components[i][componentTemplate[addressType]];
                return result;
            }
        }
        return;
    }

    this.getPlaceId = function (place) {
        if (!this.isGooglePlace(place))
            return;
        return place.place_id;
    }

    this.getVicinity = function (place) {
        if (!this.isGooglePlace(place))
            return;
        return place.vicinity;
    }

    this.getStreetNumber = function (place) {
        var COMPONENT_TEMPLATE = { street_number: 'short_name' },
            streetNumber = this.getAddrComponent(place, COMPONENT_TEMPLATE);
        return streetNumber;
    }

    this.getStreet = function (place) {
        var COMPONENT_TEMPLATE = { route: 'long_name' },
            street = this.getAddrComponent(place, COMPONENT_TEMPLATE);
        return street;
    }

    this.getSubLocality = function (place) {
        var COMPONENT_TEMPLATE = { sublocality_level_2: 'long_name' },
            subLocality = this.getAddrComponent(place, COMPONENT_TEMPLATE);
        return subLocality;
    }

    this.getLocality = function (place) {
        var COMPONENT_TEMPLATE = { sublocality_level_1: 'long_name' },
            locality = this.getAddrComponent(place, COMPONENT_TEMPLATE);
        return locality;
    }

    this.getCity = function (place) {
        var COMPONENT_TEMPLATE = { locality: 'long_name' },
            city = this.getAddrComponent(place, COMPONENT_TEMPLATE);
        return city;
    }

    this.getState = function (place) {
        var COMPONENT_TEMPLATE = { administrative_area_level_1: 'long_name' },
            state = this.getAddrComponent(place, COMPONENT_TEMPLATE);
        return state;
    }

    this.getStateCode = function (place) {
        var COMPONENT_TEMPLATE = { administrative_area_level_1: 'short_name' },
            stateCode = this.getAddrComponent(place, COMPONENT_TEMPLATE);
        return stateCode;
    }

    this.getDistrict = function (place) {
        var COMPONENT_TEMPLATE = { administrative_area_level_2: 'short_name' },
            state = this.getAddrComponent(place, COMPONENT_TEMPLATE);
        return state;
    }

    this.getCountryShort = function (place) {
        var COMPONENT_TEMPLATE = { country: 'short_name' },
            countryShort = this.getAddrComponent(place, COMPONENT_TEMPLATE);
        return countryShort;
    }

    this.getCountry = function (place) {
        var COMPONENT_TEMPLATE = { country: 'long_name' },
            country = this.getAddrComponent(place, COMPONENT_TEMPLATE);
        return country;
    }
    this.getPostCode = function (place) {
        var COMPONENT_TEMPLATE = { postal_code: 'long_name' },
            postCode = this.getAddrComponent(place, COMPONENT_TEMPLATE);
        return postCode;
    }

    this.isGeometryExist = function (place) {
        return angular.isObject(place) && angular.isObject(place.geometry);
    }

    this.getLatitude = function (place) {
        if (!this.isGeometryExist(place)) return;
        return place.geometry.location.lat();
    }

    this.getLongitude = function (place) {
        if (!this.isGeometryExist(place)) return;
        return place.geometry.location.lng();
    }

    this.googleCategories = function (place) {
        if (!this.isGooglePlace(place))
            return;
        return place.types.join(',');
    }

};

function setCookie(cname, cvalue, exSeconds) {
    var d = new Date();
    d.setTime(d.getTime() + (exSeconds * 1000));
    var expires = "expires=" + d.toUTCString();
    document.cookie = cname + "=" + cvalue + "; " + expires;
}

function getCookie(cname) {
    var name = cname + "=";
    var ca = document.cookie.split(';');
    for (var i = 0; i < ca.length; i++) {
        var c = ca[i];
        while (c.charAt(0) == ' ') c = c.substring(1);
        if (c.indexOf(name) != -1) return c.substring(name.length, c.length);
    }
    return "";
}

function loadScript(url, type, charset) {
    if (type === undefined) type = 'text/javascript';
    if (url) {
        var script = document.querySelector("script[src*='" + url + "']");
        if (!script) {
            var heads = document.getElementsByTagName("head");
            if (heads && heads.length) {
                var head = heads[0];
                if (head) {
                    script = document.createElement('script');
                    script.setAttribute('src', url);
                    script.setAttribute('type', type);
                    if (charset) script.setAttribute('charset', charset);
                    head.appendChild(script);
                }
            }
        }
        return script;
    }
}

var createObjects = function (value, keyName, valueName) {
    var resultValues = [];
    var values = value ? value.split(',') : [];
    for (var j = 0; j < values.length; ++j) {
        var tempValue = values[j].split(';');
        if (tempValue.length == 1)
            tempValue.push('');
        obj = {};
        obj[keyName] = tempValue[0];
        obj[valueName] = tempValue[1];

        resultValues.push(obj);
    }
    return resultValues;
}
var createTextFromObject = function (values) {
    var value = '';
    values = values ? values : [];
    for (var j = 0; j < values.length; ++j) {
        value += values[j].config;
        if (values[j].name)
            value += ';' + values[j].name;
        if (j != values.length - 1)
            value += ',';
    }
    return value;
}

var addObjectToArray = function (array, obj, attribute) {
    var temp = getObjectFromArray(array, attribute, obj[attribute] || '');
    if (temp)
        return temp;
    else {
        array.push(obj);
        return obj;
    }
}


var queryObject = {
    'pageNumber': 1,
    'pageSize': 20,
    'storeName': null,
    'storeNameAlias': null,
    'address': null,
    'city': null,
    'state': null,
    'category': null,
    'installedState': null,
    'storeTags': null
}

var labelMap = {
    "INV_NUMBER": { label: "Invalid number", color: "#FF5390" },
    "DNDNUMB": { label: "DND", color: "#FF6400" },
    "DELIVRD": { label: "Delivered", color: "#00FF00" },
    "AWATING": { label: "Awaiting", color: "#FF6410" },
    "OTHERS": { label: "Others", color: "#FF7384" },
    "AWAITED_DLR": { label: "Awaiting", color: "#FF6410" }
}

var featureMap = [
    { enumCode: 'NO_FEATURE', code: 0, name: 'No feature' },
    { enumCode: 'BLOCKED_LIST', code: 1, name: 'Blocked list' },
    { enumCode: 'LANDING_PAGE', code: 2, name: 'Landing page' },
    { enumCode: 'WHITE_LIST', code: 3, name: 'White list' },
    { enumCode: 'VIP_LIST', code: 4, name: 'Vip list' },
    { enumCode: 'BANDWIDTH_CONTROL', code: 5, name: 'Bandwidth control' },
    { enumCode: 'DATA_USAGE_CONTROL', code: 6, name: 'Data Usage control' },
    { enumCode: 'BULK_PROMOTIONAL_SMS', code: 7, name: 'Bulk Promotional sms', field: 'smsCount' },
    { enumCode: 'COUPON_CONTROL', code: 8, name: 'Coupon control' },
    { enumCode: 'TOGGLING_FEATURE', code: 9, name: 'Toggling feature' },
    { enumCode: 'DATA_USAGE_CONTROL_MONTH', code: 10, name: 'Data usage control per month' },
    { enumCode: 'BANDWIDTH_AFTER_EXHAUSTED', code: 11, name: 'Bandwidth After exhausted' },
    { enumCode: 'MAC_WHITELISTING', code: 12, name: 'Mac whitelisting' },
    { enumCode: 'PROMOTIONAL_SMS_TAB', code: 13, name: 'Promotional Sms tab' },
    { enumCode: 'DATA_USAGE_TAB', code: 14, name: 'Data Usage tab' },
    { enumCode: 'ADVANCE_CONFIG', code: 15, name: 'Advance config' },
    { enumCode: 'TEMPLATE_CONFIG', code: 16, name: 'Template config' },
    { enumCode: 'USER_GROUP', code: 17, name: 'User group' },
    { enumCode: 'BLOCK_WEBSITE', code: 18, name: 'Block website' },
    { enumCode: 'SHOW_PHONE_NUMBER', code: 19, name: 'Show Phone number' },
    { enumCode: 'ADVANCE_ANALYTICS', code: 20, name: 'Advance analytics' },
    { enumCode: 'THIRTY_MIN_CHECK', code: 21, name: 'Thirty Min check' },
    { enumCode: 'MAX_DOWNLOAD_BANDWIDTH', code: 22, name: 'Max Download bandwidth' },
    { enumCode: 'MAX_UPLOAD_BANDWIDTH', code: 23, name: 'Max Upload bandwidth' },
    { enumCode: 'MAX_DATA_USAGE_PER_DAY', code: 24, name: 'Max data usage per per day' },
    { enumCode: 'SESSION_TIMEOUT', code: 25, name: 'Session timeout' },
    { enumCode: 'NUMBER_OF_SESSIONS_PER_DAY', code: 26, name: 'Number of sessions per per day' },
    { enumCode: 'VIP_PHONE_NUMBER_LIST', code: 27, name: 'Vip phone number per list' },
    { enumCode: 'VIP_MAC_LIST', code: 28, name: 'Vip Mac list' },
    { enumCode: 'CUSTOM_LANDING_PAGE', code: 29, name: 'Custom Landing page' },
    { enumCode: 'PHONE_NUMBER_WHITELISTING', code: 30, name: 'Phone Number whitelisting' },
    { enumCode: 'CONFIGURE_ROUTER_SPLASH_IMAGE', code: 31, name: 'Configure router splash per image' },
    { enumCode: 'CHOOSE_AUTHENTICATION_TYPE', code: 32, name: 'Choose Authentication type' },
    { enumCode: 'FACEBOOK_CHECKIN', code: 33, name: 'Facebook checkin' },
    { enumCode: 'FACEBOOK_LIKE', code: 34, name: 'Facebook like' },
    { enumCode: 'CONFIGURE_LANDING_PAGE', code: 35, name: 'Configure Landing page' },
    { enumCode: 'CONFIGURE_LOGIN_PAGE', code: 36, name: 'Configure Login page' },
    { enumCode: 'SHINE_PLUS', code: 37, name: 'Shine plus' },
    { enumCode: 'SET_AUTHENTICATION_GROUP', code: 38, name: 'Set Authentication group' },
    { enumCode: 'ANALYTICS_MONTHLY_REPORTS', code: 39, name: 'Analytics Monthly reports' },
    { enumCode: 'WOFR', code: 56, name: 'WOFR Campaign Dashboard' }
]

var getFeatureObject = function (enumCode, packageJson) {
    var ret = null;
    $.each(featureMap, function (key, feature) {
        if (feature.enumCode == enumCode || feature.code == enumCode) {
            if (feature.field && packageJson[feature.field]) {
                feature.name = packageJson[feature.field] + ' sms';
            }
            ret = feature;
        }
    });
    return ret;
}

var newSelectAllItem = {
    "deviceId": null,
    "mmNasId": -1,
    "lastPingDelay": null,
    "active": false,
    "smsLimit": 0,
    "routerState": 0,
    "nasid": -1,
    "controllerid": 1,
    "storeName": "Select All",
    "storeNameAlias": '',
    "address": '',
    "location": '',
    "latitude": '',
    "longitude": '',
    "city": '',
    "state": '',
    "category": '',
    "contactPerson": '',
    "contactNumber": '',
    "emailId": '',
    "internetBillingStartDay": 0,
    "internetPlan": 0,
    "uploadKpbs": 0,
    "downloadKbps": 0
};

function getValueFromObjectArray(listItem, response) {
    var ret, value;
    for (var i = 0; i < response.length; i++) {
        value = response[i];
        if (value.hasOwnProperty('Key') && value['Key'] == listItem['nasid']) {
            ret = value.Value;
            break;
        } else {
            ret = null;
        }
    }
    return ret;
}

var setLastUsed = function (list, response) {
    if (list) {
        var date = new Date();
        angular.forEach(list, function (listItem) {
            val = getValueFromObjectArray(listItem, response);
            if (val) {
                listItem.lastUsed = roundOffTime(val * 60)
            } else {
                listItem.lastUsed = 'N/A';
            }
        });
    }
}

var roundOffTime = function (timeInSec) {
    var days = parseInt(timeInSec / 86400);
    var hours = parseInt(timeInSec / 3600) % 24;
    var minutes = parseInt(timeInSec / 60) % 60;
    var seconds = timeInSec % 60;
    var result = (days >= 1 ? days + 'd ' : '') +
        (hours >= 1 ? hours + 'h ' : '') +
        (minutes >= 1 ? minutes + 'm' : '');
    return result ? result : 'Just now';
}

var setLast24Hours = function (list, response) {
    if (list) {
        angular.forEach(list, function (listItem) {
            val = getValueFromObjectArray(listItem, response);
            if (val) {
                listItem.inLast24Hours = val;
            } else {
                listItem.inLast24Hours = '0';
            }
        });
    }
}

var createQueryForLocationFilter = function (options) {
    var query = ''
    options = options || {};
    var queryObjectCopy = angular.copy(queryObject);
    queryObjectCopy = angular.extend(queryObjectCopy, options)
    angular.forEach(queryObjectCopy, function (value, key) {
        if (key == 'pageNumber' && value != null)
            query += '?pageNumber=' + value;
        else if (value != null && value != '')
            query += '&' + key + '=' + value;
    });
    return query;
}

var modalDefaults = {
    controller: function ($scope, $modalInstance) {
        $scope.cancel = function () {
            $modalInstance.dismiss('cancel');
        }
    },
    ok: function () {

    },
    cancel: function () {
        $modalInstance.dismiss('cancel');
    },
    windowClass: 'i2e1-dialog'
};

function showDialog(model, templateUrl, data) {
    return model.open({
        animation: 'true',
        //backdrop: 'static',
        //keyboard: false,
        templateUrl: templateUrl,
        controller: 'modelController',
        size: data.size,
        resolve: {
            data: function () {
                return data;
            }
        }
    });
}

function showConfirmationDialog(model, options, callback) {
    return model.open({
        animation: 'true',
        templateUrl: '/confirmationDialog.html',
        controller: 'modelController',
        resolve: {
            data: function () {
                return {
                    data: {
                        msg: options.textContent ? options.textContent : null,
                        title: options.title ? options.title : null,
                        type: options.type ? options.type : null, // alert-success, alert-info, alert-warning, alert-danger
                        ok: options.ok ? options.ok : null,
                        cancel: options.cancel ? options.cancel : null
                    },
                    ok: callback
                };
            }
        }
    });
}

function getUrlParams() {
    var result = {};
    var str = window.location.search;
    if (str) {
        str = str.substring(1);
        if (str) {
            var pairs = str.split('&');
            for (var i = 0; i < pairs.length; ++i) {
                var keyValue = pairs[i].split('=');
                result[keyValue[0]] = keyValue[1];
            }
        }
    } else return null;
    return result;
}

function drawChart(elementId, array, title, columns, windowMax, width, height) {
    if (array) {
        var newArray = [columns];
        for (var i = array.length - 1; i >= 0; i--) {
            array[i][2] = 'color: #78BFB9';
        };
        newArray = newArray.concat(array)
        var data = google.visualization.arrayToDataTable(newArray);

        // Set chart options
        var options = {
            'title': title,
            'width': width,
            'height': height,
            "colors": ["#78C9C3"],
            legend: { position: 'bottom' },
            vAxis: {
                viewWindowMode: 'explicit',
                viewWindow: {
                    max: windowMax,
                    min: 0
                }
            }
        };

        if (array.length > 0) {
            // Instantiate and draw our chart, passing in some options.
            var chart = new google.visualization.ColumnChart(document.getElementById(elementId));
            chart.draw(data, options);
        } else {
            document.getElementById(elementId).innerHTML = '<p style="padding: 1.5rem;text-align: center;">No Data Present</p>'
        }

    }

}

function drawLineChart(elementId, array, title, columns, windowMax, width, height) {
    var newArray = [columns];
    for (var i = array.length - 1; i >= 0; i--) {
        array[i][2] = array[i][2];
    };
    newArray = newArray.concat(array)
    var data = google.visualization.arrayToDataTable(newArray);

    var options = {
        title: title,
        curveType: 'function',
        legend: { position: 'bottom' },
        'width': width,
        'height': height,
        'backgroundColor': '#fff',
        "colors": ["#78C9C3", 'red'],
        series: {
            0: { lineDashStyle: [1, 0], lineWidth: 2 },
            1: { lineDashStyle: [4, 4], lineWidth: 2 }
        },
        vAxis: {
            viewWindowMode: 'explicit',
            viewWindow: {
                max: windowMax,
                min: 0
            }
        }
    };
    if (array.length > 0) {
        var chart = new google.visualization.LineChart(document.getElementById(elementId));
        chart.draw(data, options);
    } else {
        document.getElementById(elementId).innerHTML = '<p style="padding: 1.5rem;text-align: center;">No Data Present</p>'
    }
}

function drawPieChart(elementId, array, title, columns, windowMax, width, height, colors) {
    var newArray = [columns];
    newArray = newArray.concat(array)
    var data = google.visualization.arrayToDataTable(newArray);
    var options = {
        title: title,
        'width': width,
        'height': height,
        'backgroundColor': '#fff',
        vAxis: {
            viewWindowMode: 'explicit',
            viewWindow: {
                max: windowMax,
                min: 0
            }
        },
        colors: colors //['#e0440e', '#e6693e', '#ec8f6e', '#f3b49f', '#f6c7b6','red','yellow','black']
    };
    if (array.length > 0) {
        var chart = new google.visualization.PieChart(document.getElementById(elementId));
        chart.draw(data, options);
    } else {
        document.getElementById(elementId).innerHTML = '<p style="padding: 1.5rem;text-align: center;">No Data Present</p>'
    }
}

function sort(list, index) {
    return list.sort(function (a, b) {
        var aVal = a[index];
        var bVal = b[index];
        if (aVal > bVal)
            return 1;
        else if (aVal < bVal)
            return -1;
        return 0;
    });
}

function mapTime(hour) {
    var mer = 'AM';
    if (hour >= 12)
        mer = 'PM';
    if (hour > 12) {
        hour -= 12
    }
    if (hour === 0)
        hour = 12;
    return hour + mer;
}

Date.prototype.yyyymmdd = function () {
    var yyyy = this.getFullYear().toString();
    var mm = (this.getMonth() + 1).toString(); // getMonth() is zero-based
    var dd = this.getDate().toString();
    var hh = this.getHours();
    var min = this.getMinutes();
    var sec = this.getSeconds();
    return yyyy + '-' + (mm[1] ? mm : "0" + mm[0]) + '-' + (dd[1] ? dd : "0" + dd[0]) + " " + hh + ":" + min + ":" + sec; // padding
};

Date.prototype.ddmmyyyy = function () {
    var yyyy = this.getFullYear().toString();
    var mm = (this.getMonth() + 1).toString(); // getMonth() is zero-based
    var dd = this.getDate().toString();
    return (dd[1] ? dd : "0" + dd[0]) + '/' + (mm[1] ? mm : "0" + mm[0]) + '/' + yyyy
};

function getCalculateDateWithNumberOfDays(days, creat_date) {
    if (days > 0) {
        var monthNames = ["January", "February", "March", "April", "May", "June", "July", "August", "September", "October", "November", "December"];
        var v = new Date();
        var today = new Date();
        //v.setDate(moment(creat_date).toDate().getDate() + parseInt(validity_days));
        v.setDate(today.getDate() + parseInt(days));
        var valid = v.getDate() + " " + monthNames[v.getMonth()];
        return valid;
    } else {
        return "";
    }
}

function getObjectFromArray(list, propertyName, propertyValue) {
    var ret, value;
    for (var i = 0; i < list.length; i++) {
        value = list[i];
        if (value.hasOwnProperty(propertyName) && value[propertyName] == propertyValue) {
            ret = value;
            break;
        }
    }
    return ret;
}


function getObjectIndexFromArray(list, propertyName, propertyValue) {
    var ret, value;
    for (var i = 0; i < list.length; i++) {
        value = list[i];
        if (value.hasOwnProperty(propertyName) && value[propertyName] == propertyValue) {
            ret = i;
            break;
        }
    }
    return ret;
}

function convertBackendTimeToJsTime(time) {
    return new Date(parseInt(time.replace("/Date(", "").replace(")/", "")));
}

Array.prototype.partialJoin = function (start, end, text) {
    var joinedText = '';
    for (var i = start; i < end; ++i) {
        if (this[i]) {
            joinedText += this[i];
            if (i != end - 1)
                joinedText += text;
        }
    }
    return joinedText;
}

String.prototype.convertToDate = function () {
    return new Date(this.substr(this.indexOf("(") + 1, 13) - 0);
}

var Constants = {
    dateFormat: 'DD-MMM-YYYY',
    competeDateFormat: 'DD-MMM-YYYY : hh:mm',
    englishDate: 'Do MMM YYYY hh.mm A'
};


var roundToTwo = function (num) {
    return +(Math.round(num + "e+2") + "e-2");
}

String.prototype.capitalize = function () {
    return this.charAt(0).toUpperCase() + this.slice(1);
}

var partners = [{ key: "N/A", value: "Non Partner" }, { key: "14S", value: "14 Square" },
{ key: "AIR", value: "Airtel" }, { key: "ARU", value: "Aarusha homes" },
{ key: "BAR", value: "Barshala" }, { key: "BRW", value: "Brewberry" },
{ key: "CHA", value: "Chaayos" }, { key: "CKP", value: "Chai Kaapi" },
{ key: "CPT", value: "Chai Point" }, { key: "DAL", value: "Dalmia" },
{ key: "DIN", value: "Diner out" }, { key: "EGG", value: "Egg Factory" },
{ key: "FAB", value: "Fab hotels" }, { key: "FIT", value: "Fitness First" },
{ key: "HAR", value: "Harry's" }, { key: "HOM", value: "Homigo" },
{ key: "INF", value: "Infospace" }, { key: "INT", value: "Intellicap" },
{ key: "KHA", value: "Khan Chacha" }, { key: "LOC", value: "L'occitane" },
{ key: "MAM", value: "Mamagoto" }, { key: "MAN", value: "Maan Singh" },
{ key: "MHQ", value: "My Headquarters" }, { key: "MOC", value: "Mocha" },
{ key: "NEP", value: "Nephroplus" }, { key: "NES", value: "Nestaway" },
{ key: "NEU", value: "Neu Salonz" }, { key: "OLA", value: "Ola" },
{ key: "ORX", value: "Orix" }, { key: "OSC", value: "Oscope" },
{ key: "OYO", value: "Oyo" }, { key: "man", value: "Red Bus" },
{ key: "RYD", value: "Ryder Sports" }, { key: "SHU", value: "Shuttle" },
{ key: "SID", value: "Sidewok" }, { key: "SOC", value: "Social" },
{ key: "STR", value: "Strikers" }, { key: "TAL", value: "Talwalkars" },
{ key: "TBC", value: "The Beer Cafe" }, { key: "THA", value: "Tea Halt" },
{ key: "TPO", value: "Tpot" }, { key: "TRE", value: "Treebo" },
{ key: "VOD", value: "Vodafone" }, { key: "ZEL", value: "Zelo" },
{ key: "ZIF", value: "Ziffy" }, { key: "ZOC", value: "Zocalo" },
{ key: "ZOO", value: "Zoom Car" }];

var states = [{ key: 'AP', value: 'Andhra Pradesh' }, { key: 'AR', value: 'Arunachal Pradesh' },
{ key: 'AS', value: 'Assam' }, { key: 'BH', value: 'Bihar' }, { key: 'CH', value: 'Chhattisgarh' },
{ key: 'DEL', value: 'Delhi' }, { key: 'GO', value: 'Goa' }, { key: 'GJ', value: 'Gujarat' },
{ key: 'HR', value: 'Haryana' }, { key: 'HP', value: 'Himachal Pradesh' }, { key: 'J&K', value: 'Jammu & Kashmir' },
{ key: 'JK', value: 'Jharkhand' }, { key: 'KN', value: 'Karnataka' }, { key: 'KR', value: 'Kerala' },
{ key: 'MP', value: 'Madhya Pradesh' }, { key: 'MH', value: 'Maharashtra' }, { key: 'MN', value: 'Manipur' },
{ key: 'MG', value: 'Meghalaya' }, { key: 'MZ', value: 'Mizoram' }, { key: 'NG', value: 'Nagaland' },
{ key: 'OD', value: 'Odisha (Orissa)' }, { key: 'PUN', value: 'Punjab' }, { key: 'RJ', value: 'Rajasthan' },
{ key: 'SK', value: 'Sikkim' }, { key: 'TN', value: 'Tamil Nadu' }, { key: 'TG', value: 'Telangana' },
{ key: 'TR', value: 'Tripura' }, { key: 'UP', value: 'Uttar Pradesh' }, { key: 'UK', value: 'Uttarakhand' },
{ key: 'WB', value: 'West Bengal' }];

var availableSubCategories = [

    { display: 'Bank/NBFC', value: 'bank', category: 'merchant' },
    { display: 'Cafe', value: 'cafe', category: 'merchant' },
    { display: 'Restaurant', value: 'restaurant', category: 'merchant' },
    { display: 'Clinic/Pathlab', value: 'clinic', category: 'merchant' },
    { display: 'Gym', value: 'gym', category: 'merchant' },
    { display: 'Hospital', value: 'hospital', category: 'merchant' },
    { display: 'Mall/Hypermart', value: 'mall', category: 'merchant' },
    { display: 'Movie Theater', value: 'movie', category: 'merchant' },
    { display: 'Open Market', value: 'market', category: 'merchant' },
    { display: 'Pub/Club', value: 'pub', category: 'merchant' },
    { display: 'Retail', value: 'retail', category: 'merchant' },
    { display: 'Salon/Spa', value: 'salon', category: 'merchant' },
    { display: 'School/College', value: 'school', category: 'merchant' },
    { display: 'Budget', value: 'budget', category: 'hotel' },
    { display: 'Premium', value: 'premium', category: 'hotel' },
    { display: 'Hostel', value: 'hostel', category: 'longstay' },
    { display: 'Housing Society', value: 'society', category: 'longstay' },
    { display: 'Shared Apartment', value: 'apt', category: 'longstay' },
    { display: 'Bus/Shuttle', value: 'bus', category: 'transport' },
    { display: 'Cab', value: 'cab', category: 'transport' },
    { display: 'Office/Coworking space', value: 'office', category: 'office' }
];

var getSingleLocationSelectorConfig = function () {
    return angular.copy({
        valueField: 'nasid',
        labelField: 'storeName',
        searchField: ['storeName', 'nasid', 'state', 'city', 'address', 'category', 'emailId', 'mmNasId'],
        delimiter: '|',
        placeholder: 'Filter Searched Locations',
        sortField: 'city',
        render: {
            item: function (item) {
                var ret = '<div class="item" title="' + item.nasid + ',' + item.mmNasId + '"><span class="storeName">' + item.storeName + '</span> - ';
                ret += item.city ? '<span class="city">' + item.city + '</span>' : '<span class="city">No City</span>';
                ret += '</div>';
                return ret;
            },
            option: function (item, escape) {
                var label = escape(item.storeName) || escape(item.emailId);
                var caption = '<span>Location: ' + escape(item.city) + '</span>';

                caption += (item.contactNumber || item.emailId) ? '<span>Contact: ' + escape(item.contactNumber || item.emailId) + '</span>' : '';

                return '<div title="' + item.nasid + ',' + item.mmNasId + '">' +
                    '<div class="primary">' + label + '</div>' +
                    (caption ? '<div class="secondary">' + caption + '</div>' : '') +
                    '</div>';
            }
        },
        maxItems: 1
    });
};

var getMultipleLocationSelectorConfig = function () {
    return angular.copy({
        valueField: 'nasid',
        labelField: 'storeName',
        searchField: ['storeName', 'nasid', 'state', 'city', 'address', 'category', 'emailId'],
        plugins: ['remove_button'],
        delimiter: '|',
        placeholder: 'Locations',
        sortField: 'city',
        render: {
            item: function (item) {
                var ret = '<div class="item" title="' + item.nasid + ',' + item.mmNasId + '"><span class="storeName">' + item.storeName + '</span> - ';
                ret += item.city ? '<span class="city">' + item.city + '</span>' : '<span class="city">No City</span>';
                ret += '</div>';
                return ret;
            },
            option: function (item, escape) {
                var label = escape(item.storeName) || escape(item.emailId);
                var caption = '<span>Location: ' + escape(item.city) + '</span>';

                caption += (item.contactNumber || item.emailId) ? '<span>Contact: ' + escape(item.contactNumber || item.emailId) + '</span>' : '';

                return '<div title="' + item.nasid + ',' + item.mmNasId + '">' +
                    '<div class="primary">' + label + '</div>' +
                    (caption && item.nasid != '-1' ? '<div class="secondary">' + caption + '</div>' : '') +
                    '</div>';
            }
        },
        maxItems: 9999
    });
};


Selectize.define('infinite_scroll', function (options) {
    var self = this,
        page = 1;

    self.infinitescroll = {
        onScroll: function () {
            var scrollBottom = self.$dropdown_content[0].scrollHeight - (self.$dropdown_content.scrollTop() + self.$dropdown_content.height())
            if (scrollBottom < 300) {
                var query = JSON.stringify({
                    search: self.lastValue,
                    page: page
                })

                self.$dropdown_content.off('scroll')
                self.onSearchChange(query)
            }
        }
    };

    self.onFocus = (function () {
        var original = self.onFocus;

        return function () {
            var query = JSON.stringify({
                search: self.lastValue,
                page: page
            })

            original.apply(self, arguments);
            self.onSearchChange(query)
        };
    })();

    self.onKeyUp = function (e) {
        var self = this;

        if (self.isLocked) return e && e.preventDefault();
        var value = self.$control_input.val() || '';

        if (self.lastValue !== value) {
            var query = JSON.stringify({
                search: value,
                page: page = 1
            });

            self.lastValue = value;
            self.onSearchChange(query);
            self.refreshOptions();
            self.clearOptions();
            self.trigger('type', value);
        }
    };

    self.on('load', function () {
        page++
        self.$dropdown_content.on('scroll', self.infinitescroll.onScroll);
    });

});

var totalCount, page, perPage = 100;

var getPaginatedMultiStoreSelectorConfig = function () {
    return angular.copy({
        persist: false,
        valueField: 'nasid',
        labelField: 'storeName',
        searchField: ['storeName', 'nasid', 'state', 'city', 'address', 'category', 'emailId'],
        plugins: ['remove_button', 'infinite_scroll'],
        delimiter: '|',
        placeholder: 'Locations',
        sortField: [{ field: 'nasid', direction: 'asc' }],
        render: {
            item: function (item) {
                var ret = '<div class="item" title="' + item.nasid + ',' + item.mmNasId + '"><span class="storeName">' + item.storeName + '</span> - ';
                ret += item.city ? '<span class="city">' + item.city + '</span>' : '<span class="city">No City</span>';
                ret += item.state ? '<span class="city">' + item.state + '</span>' : '';
                ret += '</div>';
                return ret;
            },
            option: function (item, escape) {
                var label = escape(item.storeName) + ' - <small>' + escape(item.nasid) + '</small>' || escape(item.nasid);
                var caption = '<span>Location: ' + escape(item.city) + '</span>';

                caption += (item.contactNumber || item.emailId) ? '<span>Contact: ' + escape(item.contactNumber || item.emailId) + '</span>' : '';

                return '<div title="' + item.nasid + ',' + item.mmNasId + '">' +
                    '<div class="primary">' + label + '</div>' +
                    (caption && item.nasid != '-1' ? '<div class="secondary">' + caption + '</div>' : '') +
                    '</div>';

            }
        },
        //score: function() {
        //    return function(search) {
        //        return 1;
        //    }
        //},
        load: function (query, callback) {
            query = JSON.parse(query)
            page = query.page || 1
            if (!totalCount || totalCount > ((page - 1) * perPage)) {
                if (query.search != "" && isNaN(query.search)) {
                    var sreachurl = '/Client/GetLocationsForAdmin?pageNumber=' + query.page + '&pageSize=' + perPage + '&storeName=' + query.search + '&storeNameAlias=' + query.search + '&address=' + query.search + '&state=' + query.search + '&storeTags=%';
                } else {
                    var sreachurl = '/Client/GetLocationsForAdmin?pageNumber=' + query.page + '&pageSize=' + perPage + '&nasid=' + query.search;
                }


                $.ajax({
                    url: sreachurl,
                    error: function () {
                        callback();
                    },
                    success: function (res) {
                        totalCount = res.total_count;
                        //callback(res.data);

                        var newArray = res.data.filter(function (item) {
                            return item.storeName && item.nasid > 0;
                            //if (item.storeName && item.nasid > 0) {
                            //    callback([item]);
                            //}
                        });
                        callback(newArray);
                    }
                });
            } else {
                callback();
            }
        }
    });
};


var _portal_static = {
    report_chartArray: {
        "login": {
            "id": "login_chart_div",
            "width": $('.graph_div').width(),
            "height": $(window).height() * 0.45
        },
        "user": {
            "id": "user_chart_div",
            "width": $('.graph_div').width(),
            "height": $(window).height() * 0.45
        },
        "login_line": {
            "id": "line_chart_div",
            "width": $('.graph_div').width(),
            "height": $(window).height() * 0.45
        },
        "user_line": {
            "id": "users_line_chart_div",
            "width": $('.graph_div').width(),
            "height": $(window).height() * 0.45
        }
    },


    bandwidths: [{
        value: "",
        name: "Not Set"
    }, {
        value: "100",
        name: "100 Kbps"
    }, {
        value: "200",
        name: "200 Kbps"
    }, {
        value: "500",
        name: "500 Kbps"
    }, {
        value: "1000",
        name: "1 Mbps"
    }, {
        value: "2000",
        name: "2 Mbps"
    }, {
        value: "4000",
        name: "4 Mbps"
    }, {
        value: "8000",
        name: "8 Mbps"
    }, {
        value: "10000",
        name: "10 Mbps"
    }, {
        value: "15000",
        name: "15 Mbps"
    }, {
        value: "20000",
        name: "20 Mbps"
    }, {
        value: "30000",
        name: "30 Mbps"
    }, {
        value: "50000",
        name: "50 Mbps"
    }],


    dataLimits: [{
        value: "",
        name: "Not Set"
    }, {
        value: "50",
        name: "50 MB"
    }, {
        value: "100",
        name: "100 MB"
    }, {
        value: "200",
        name: "200 MB"
    }, {
        value: "500",
        name: "500 MB"
    }, {
        value: "1024",
        name: "1 GB"
    }, {
        value: "2048",
        name: "2 GB"
    }, {
        value: "4096",
        name: "4 GB"
    }, {
        value: "5120",
        name: "5 GB"
    }, {
        value: "8192",
        name: "8 GB"
    }, {
        value: "10240",
        name: "10 GB"
    }, {
        value: "20480",
        name: "20 GB"
    }, {
        value: "30720",
        name: "30 GB"
    }, {
        value: "40960",
        name: "40 GB"
    }, {
        value: "51200",
        name: "50 GB"
    }]
}


/* For creating heat map of locations on google map */

var createHeatMap = function (citymap) {

    var mapStyle = [{
        "featureType": "administrative.land_parcel",
        "stylers": [{
            "visibility": "off"
        }]
    },
    {
        "featureType": "administrative.neighborhood",
        "stylers": [{
            "visibility": "off"
        }]
    },
    {
        "featureType": "landscape",
        "stylers": [{
            "visibility": "off"
        }]
    },
    {
        "featureType": "poi",
        "stylers": [{
            "visibility": "simplified"
        }]
    },
    {
        "featureType": "poi.attraction",
        "stylers": [{
            "visibility": "off"
        }]
    },
    {
        "featureType": "poi.business",
        "stylers": [{
            "visibility": "off"
        }]
    },
    {
        "featureType": "poi.government",
        "stylers": [{
            "visibility": "off"
        }]
    },
    {
        "featureType": "poi.medical",
        "stylers": [{
            "visibility": "off"
        }]
    },
    {
        "featureType": "poi.park",
        "stylers": [{
            "visibility": "simplified"
        }]
    },
    {
        "featureType": "poi.place_of_worship",
        "stylers": [{
            "visibility": "off"
        }]
    },
    {
        "featureType": "poi.school",
        "stylers": [{
            "visibility": "off"
        }]
    },
    {
        "featureType": "poi.sports_complex",
        "stylers": [{
            "visibility": "off"
        }]
    },
    {
        "featureType": "road",
        "stylers": [{
            "visibility": "off"
        }]
    },
    {
        "featureType": "transit",
        "stylers": [{
            "visibility": "off"
        }]
    },
    {
        "featureType": "water",
        "stylers": [{
            "visibility": "simplified"
        }]
    }
    ];

    var map;
    var array_shapes_object = new Array();
    var cricles_radius_object = new Array();

    map = new google.maps.Map(document.getElementById('map_div'), {
        zoom: 11,
        center: {
            lat: 28.6315,
            lng: 77.2001368
        },
        mapTypeId: 'roadmap',
        showTooltip: true,
        showInfoWindow: true,
        mapTypeControl: false,
        maxZoom: 15
    });

    map.set('styles', mapStyle);

    var getFormatedTime = function (hours) {
        suffix = (hours >= 12) ? 'PM' : 'AM';
        hours = (hours > 12) ? hours - 12 : hours;
        hours = (hours == '00') ? 12 : hours;
        return hours + ':00 ' + suffix;
    }

    for (var city in citymap) {

        var cityCircle = new google.maps.Circle({
            strokeColor: citymap[city].color,
            clickable: true,
            strokeOpacity: citymap[city].opacity,
            strokeWeight: 0,
            fillColor: citymap[city].color,
            fillOpacity: citymap[city].opacity,
            map: map,
            center: citymap[city].center,
            radius: citymap[city].radius,
            content: "<div id=\"google-popup\">" +
                "<span class='label_haed'>Market Place:</span> " + city.replace('_', ' ') +
                "</br> <span class='label_haed'>Shoppers Density:</span> " + citymap[city].density +
                "</br> <span class='label_haed'>Busiest Day:</span> " + citymap[city].busiestday +
                "</br> <span class='label_haed'>Peak Hour:</span> " + getFormatedTime(citymap[city].peakhour) + ' to ' + getFormatedTime(citymap[city].peakhour + 1) +
                "</div>"
        });

        array_shapes_object.push(cityCircle);
        cricles_radius_object.push(citymap[city].radius);

        var infoWindow = new google.maps.InfoWindow({
            content: ''
        });

        google.maps.event.addListener(cityCircle, 'mouseover', function () {
            infoWindow.setContent(this.content);
            infoWindow.setPosition(this.getCenter());
            infoWindow.open(map);
            $('#google-popup').parent().parent().parent().parent().addClass("google-popup-container");
        });

        google.maps.event.addListener(cityCircle, 'mouseout', function () {
            infoWindow.close();
        });
    }

    google.maps.event.addListener(map, 'zoom_changed', function () {
        var zoomlevel = map.getZoom();
        if (zoomlevel > 12) {
            for (var i = 0; i < array_shapes_object.length; i++) {
                var p = Math.pow(2, (21 - map.getZoom()));
                var r = cricles_radius_object[i];
                array_shapes_object[i].setRadius(p * r * 0.0027);
            }
        }

    });
}

function processCsvFileData(csv) {
    var allTextLines = csv.split(/\r\n|\n/);
    var lines = [];
    while (allTextLines.length) {
        lines.push(allTextLines.shift().split(','));
    }
    //console.log(lines);
    //drawOutput(lines);
    return lines
}

function isValidMac(mystring) {
    var regex = /^(([A-Fa-f0-9]{2}[:]){5}[A-Fa-f0-9]{2}[,]?)+$/i;
    return regex.test(mystring);
}

function isValidPhoneNumber(inputtxt) {
    var phoneno = /^\d{10}$/;
    if (inputtxt.match(phoneno)) {
        return true;
    }
    else {
        return false;
    }
}


function isValidIndianMobile(mystring) {
    var regex = /^(?:(?:\+|0{0,2})91(\s*[\-]\s*)?|[0]?)?[789]\d{9}$/;
    return regex.test(mystring);
}

function isValidEmail(inputText) {
    var mailformat = /^(([^<>()[\]\\.,;:\s@\"]+(\.[^<>()[\]\\.,;:\s@\"]+)*)|(\".+\"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/;
    if (inputText.match(mailformat)) {
        return true;
    } else {
        return false;
    }
}

function isValidUrl(str) {
    regexp = /^(?:(?:https?|ftp):\/\/)?(?:(?!(?:10|127)(?:\.\d{1,3}){3})(?!(?:169\.254|192\.168)(?:\.\d{1,3}){2})(?!172\.(?:1[6-9]|2\d|3[0-1])(?:\.\d{1,3}){2})(?:[1-9]\d?|1\d\d|2[01]\d|22[0-3])(?:\.(?:1?\d{1,2}|2[0-4]\d|25[0-5])){2}(?:\.(?:[1-9]\d?|1\d\d|2[0-4]\d|25[0-4]))|(?:(?:[a-z\u00a1-\uffff0-9]-*)*[a-z\u00a1-\uffff0-9]+)(?:\.(?:[a-z\u00a1-\uffff0-9]-*)*[a-z\u00a1-\uffff0-9]+)*(?:\.(?:[a-z\u00a1-\uffff]{2,})))(?::\d{2,5})?(?:\/\S*)?$/;
    if (regexp.test(str)) {
        return true;
    }
    else {
        return false;
    }
}

function isValidSenderId(str) {
    regexp = /^[a-zA-Z0-9]{6,}$/;
    if (regexp.test(str)) {
        return true;
    }
    else {
        return false;
    }
}

