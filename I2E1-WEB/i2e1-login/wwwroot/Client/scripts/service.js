app.service('ajaxCall', function ($http, $state, $q, messageService) {
    var count = 0;
    var makeCall = function (options) {
        typeof options.loading === "undefined" ? options.loading = true : options.loading = false;
        count++;
        var deferred = $q.defer();
        if (options.loading) {
            $('#loader').show();
        } else {
            $('#loader').hide();
        }
		$http(options).
		success(function (response) {
		    count--;
            if(count==0)
		        $('#loader').hide();
		    if (response && response.status === 1) {
		        messageService.showError(response.msg);
		        deferred.reject();
		    }
		    else
		        deferred.resolve(response);
		}).error(function (response, errorCode) {
		    count--;
            if(count==0)
                $('#loader').hide();
            if (errorCode == 403) {
                window.location.href = "/Client/Index?reply=" + encodeURIComponent(window.location.href);
            }
            else if (options.failRedirectUrl) {
                window.location.href = options.failRedirectUrl;
            }
            
		});
		return deferred.promise;
	}
    
	this.get = function (url, options) {
	    var rand = Math.random();
	    if (url.indexOf('?') == -1)
	        url += '?' + rand;
	    else
	        url += '&' + rand;

	    var getData = { method: 'GET', url: url, cache: false };
        return makeCall(angular.extend(getData, options));
	}
	this.post = function (url, data, options) {
	    var postData = { method: 'POST', data: data, url: url }
	    return makeCall(angular.extend(postData, options));
	}
});

app.service('servicesAjaxCall', function ($http, $state, $q, messageService) {
    var count = 0;
    var makeCall = function (options) {
        typeof options.loading === "undefined" ? options.loading = true : options.loading = false;
        count++;
        var deferred = $q.defer();
        if (options.loading) {
            $('#loader').show();
        } else {
            $('#loader').hide();
        }
        if (window.location.origin.indexOf("localhost") >= 0) {
            //options.url = "https://localhost:44330" + options.url
            options.url = "https://services.testease.i2e1.in" + options.url
        } else if (window.location.origin == "https://www.i2e1.in" ||
            window.location.origin == "https://i2e1.in" ||
            window.location.origin == "https://i2e1.in") {
            options.url = "https://services.i2e1.in" + options.url
        } else {
            options.url = "https://services.testease.i2e1.in" + options.url
        }
        if (window._user) {
            var userToken = window._user.token;
            if (userToken) {
                options.headers = {
                    'i2e1-admin-token': userToken,
                    'i2e1-admin-view-token': userToken
                }
            }
        }
        options.withCredentials = true;
        $http(options).
            success(function (response) {
                count--;
                if (count == 0)
                    $('#loader').hide();
                if (response && response.status === 1) {
                    messageService.showError(response.msg);
                    deferred.reject();
                }
                else
                    deferred.resolve(response);
            }).error(function (response, errorCode) {
                count--;
                if (count == 0)
                    $('#loader').hide();
                if (errorCode == 403) {
                    window.location.href = "/Client/Index?reply=" + encodeURIComponent(window.location.href);
                }
                else if (options.failRedirectUrl) {
                    window.location.href = options.failRedirectUrl;
                }

            });
        return deferred.promise;
    }

    this.get = function (url, options) {
        var rand = Math.random();
        if (url.indexOf('?') == -1)
            url += '?' + rand;
        else
            url += '&' + rand;

        var getData = { method: 'GET', url: url, cache: false };
        return makeCall(angular.extend(getData, options));
    }
    this.post = function (url, data, options) {
        var postData = { method: 'POST', data: data, url: url }
        return makeCall(angular.extend(postData, options));
    }
});


app.service('messageService', ['$timeout', function ($timeout) {
    var showMessage = function (text, type, timeout) {
        timeout = timeout || 5000;
        $('.messege').addClass('popup');
        $('.messege .' + type).text(text)
        $('.messege .' + type).addClass('appear');
        
        $timeout(function () {
            $('.messege .' + type).text('');
            $('.messege .' + type).removeClass('appear');
            $('.messege').removeClass('popup');
        }, timeout);
    }

    this.showError = function (text, timeout) {
        showMessage(text, 'error', timeout);
    }

    this.showSuccess = function (text, timeout) {
        showMessage(text, 'success', timeout);
    }

    this.showInfo = function (text, timeout) {
        showMessage(text, 'info', timeout);
    }
}]);

app.service('service', function ($q, ajaxCall, $state) {
    var loggedInUser;

    this.demofizeStores = function (store) {
        store.storeName = 'Demo Outlet';
        store.contactPerson = 'Demo Store Keeper';
        store.contactNumber = 'Demo Store Keeper';
    }

    this.getAllFeatureList = function () {
        var deferred = $q.defer();
        ajaxCall.get('/Admin/getAllFeatureList').then(function (response) {
            deferred.resolve(response.data);
        });
        return deferred.promise;
    }

    this.fetchFeatures = function (user) {
        var deferred = $q.defer();
        ajaxCall.get('/Admin/GetFeatureList?userid=' + encodeURIComponent(user)).then(function (response) {
            deferred.resolve(response.data);
        });
        return deferred.promise;
    }

    this.getStoreFromDevice = function (deviceID) {
        var deferred = $q.defer();
        ajaxCall.get('/Admin/GetStoreFromDevice?deviceId='+deviceID).then(function (response) {
            deferred.resolve(response.data);
        });
        return deferred.promise;
    }

    this.submitLocationDetails = function (storeDetails) {
        var deferred = $q.defer();
        ajaxCall.post('/Admin/SubmitLocationDetails', storeDetails).then(function (response) {
            deferred.resolve(response.data);
        });
        return deferred.promise;
    }

    this.submitDeviceSettings = function (nasid, deviceDetails) {
        var deferred = $q.defer();
        ajaxCall.post('/Admin/SubmitDeviceDetails', {
            'nasid': nasid,
            'ssid': deviceDetails.ssid,
            'download_limit': deviceDetails.download_limit,
            'session_timeout': deviceDetails.session_timeout
        }).then(function (response) {
            deferred.resolve(response.data);
        });
        return deferred.promise;
    }

    this.getSMSStaticDetails = function () {
        var deferred = $q.defer();
        ajaxCall.post('/Kelp/GetSMSStaticDetails').then(function (response) {
            deferred.resolve(response.data);
        });
        return deferred.promise;
    }

    this.getStores = function () {
        var deferred = $q.defer();
        ajaxCall.get('/Admin/GetStores').then(function (response) {
            deferred.resolve(response.data);
        });
        return deferred.promise;
    }

    this.setFeature = function (router, feature, param) {
        var deferred = $q.defer();
        ajaxCall.post('/Admin/SetFeature', {
            'router': router, 'featureId': feature, 'allow': param
        }).then(function (response) {
            deferred.resolve(response.data);
        });
        return deferred.promise;
    }
    this.sendSMS = function (nasid, message, from, to) {
        var deferred = $q.defer();
        ajaxCall.post('/Admin/SendSMS?nasid='+nasid, {
            message: message,
            from: from,
            to: to
        }).then(function (response) {
            deferred.resolve(response.data)
        });
        return deferred.promise;
    }
    this.getLastLoginTime = function (nases) {
        var deferred = $q.defer();
        if (nases) {
            ajaxCall.post('/Client/GetLastLoginTime', {
                nases: nases.split(',')
            }, {
                loading: false
            }).then(function (response) {
                deferred.resolve(response.data);
            });

        } else {
            deferred.resolve([]);
        }
            
        return deferred.promise;
    }
    this.getUsersInXHours = function (nases, x) {
        var deferred = $q.defer();
        if (nases) {
            ajaxCall.post('/Client/GetUsersInXHours?', {
                nases: nases.split(','),
                hours: x
            }, {
                loading: false
            }).then(function (response) {
                deferred.resolve(response.data);
            });
        } else {
            deferred.resolve([]);
        }
        
        return deferred.promise;
    }
    this.getDataUsed = function (nases) {
        var deferred = $q.defer();
        if (nases) {
            ajaxCall.post('/Client/GetDataUsed', {
                nases: nases.split(',')
            }, {
                loading: false
            }).then(function (response) {
                deferred.resolve(response.data);
            });
        } else {
            deferred.resolve([]);
        }
        return deferred.promise;
    }
    this.getStoreContact = function (nasid) {
        var deferred = $q.defer();
        if (nasid) {
            ajaxCall.post('/Client/GetStoreContact', {
                nasid: nasid
            }, {
                loading: false
            }).then(function (response) {
                deferred.resolve(response.data);
            });
        } else {
            deferred.resolve([]);
        }
        return deferred.promise;
    }
    this.login = function (credentials) {
        var deferred = $q.defer();
        ajaxCall.post('/Admin/Login', credentials).then(function (response) {
            deferred.resolve(response)
        }, function () {
            deferred.reject();
        });
        return deferred.promise;
    }

    this.logout = function () {
        ajaxCall.get('/Client/Logout').then(function () {
            window.location.href = '/Client/Index#/login';
        });
    }

    this.whichUser = function () {
        return ajaxCall.get('/Client/GetUser', { failRedirectUrl: '/Client/Index#/login' }).then(function (response) {
            if (response.status == 0) {
                window._user = response.data;
                return response.data;
            } else window.location.href = '/Client/Index';
        });
    }

    this.loadCampaignReports = function (campaignids) {
        return ajaxCall.get('/Kelp/GetCampaignReports?campaignIds=' + campaignids.toString(), {
            loading: false,
        }).then(function (response) {
            return response.data;
        });
    }

    this.loadPromotionDailyReports = function (campaignid, pageNum, pageSize) {
        return ajaxCall.get('/Kelp/GetCampaignReport?campaignId=' + campaignid + '&pageNum=' + pageNum + '&pageSize=' + pageSize, {
            loading: false,
        }).then(function (response) {
            return response.data;
        });
    }

    this.getLocationsForAdmin = function (options) {
        var query = createQueryForLocationFilter(options);
        return ajaxCall.get('/Client/GetLocationsForAdmin' + query, {
            loading: false
        }).then(function (response) {
            return response.data;
        });
    };
    this.getActiveInactiveDevicesCount = function (options) {
        var query = createQueryForLocationFilter(options);
        return ajaxCall.get('/Client/getActiveInactiveDevicesCount' + query).then(function (response) {
            return response.data;
        });
    };

    this.GetCombinedSetting = function (queryObject) {
        return ajaxCall.get('/Client/GetCombinedSetting?pageNumber=' + queryObject.pageNumber + '&pageSize=' + queryObject.pageSize).then(function (response) {
            return response.data;
        });
    }

    this.GetAllUserGroups = function () {
        return ajaxCall.get('/Client/GetAllUserGroups').then(function (response) {
            return response.data;
        });
    }
    this.checkIfContactExists = function (username) {
        return ajaxCall.get('/Client/checkIfContactExists?username=' + username).then(function (response) {
            return response.data;
        });
    }

    this.checkIfUsernameExist = function (username) {
        return ajaxCall.get('/Client/checkIfUsernameExist?username=' + username).then(function (response) {
            return response.data;
        });
    }

    this.getUserBrand = function () {
        return ajaxCall.get('/Client/getUserBrand').then(function (response) {
            if (response.status == 0) return response.data;
            else return 0;
        });
    }

    this.setUserBrand = function (param) {
        var deferred = $q.defer();
        ajaxCall.post('/Client/setUserBrand', param).then(function (response) {
            deferred.resolve(response.data);
        });
        return deferred.promise;
    }
});




