var defer = {
    resolve: function () { }
};
var _selectAllNas = "Select All";
var _selectAllObj = {};
_selectAllObj.storeName = 'Select All';
_selectAllObj.nasid = _selectAllNas;
var remoteEndpoint = 'https://remote.i2e1.in'

app.controller('landingController', ['$rootScope', '$scope', '$state', 'service', 'ajaxCall', 'messageService', '$location', '$anchorScroll', function ($rootScope, $scope, $state, service, ajaxCall, messageService, $location, $anchorScroll) {
    $rootScope.moment = moment;
    $rootScope.Constants = Constants;
    $rootScope.getDateString = getDateString;
    $rootScope.getTimeString = getTimeString;
    $scope.routerFilters = {};
    $scope.partnerId = 0;
    $rootScope.askGoogleCredentials = function (askCredentials) {
        gapi.load('auth2', function () {
            // Retrieve the singleton for the GoogleAuth library and set up the client.
            auth2 = gapi.auth2.init({
                client_id: '303939939579-a02pc4b6m9hno2a2qggc2kjrdi5c3rko.apps.googleusercontent.com',
                cookiepolicy: 'single_host_origin',
                // Request scopes in addition to 'profile' and 'email'
                //scope: 'additional_scope'
            });
            attachVerify(document.getElementById('verifyBtn'), askCredentials);
        });
    };

    function attachVerify(element, askCredentials) {
        auth2.attachClickHandler(element, {},
            function (googleUser) {
                onVerify(googleUser, askCredentials);
            }, function (error) {
                askCredentials.error = 'error';
            });
    }

    function onVerify(googleUser, askCredentials) {
        document.getElementById('googleToken').value = googleUser.getAuthResponse().id_token;
        document.getElementById('authType').value = 'GMAIL';
        $('#askCredentials [type=button]').click();
    };
    
    $scope.openImpersonateInput = function () {
        if ($rootScope.featureEnabled(23)) {
            $scope.viewAs = "";
            $('.user-row .label-name').toggleClass('display-none');
            $('.user-row .label-username').toggleClass('display-none');
            $('.user-row .impersonate-input').toggleClass('display-none');
        }
    }

    $scope.impersonate = function ($event, adminUserName) {
        if ($event.which == 13) {
            ajaxCall.post('/Client/Impersonate', {
                adminUserName: adminUserName
            }).then(function (response) {
                if (response.status == 0) window.location.reload();
                else if (response.status == 2) {
                    messageService.showError(response.msg);
                }
            });
        }
    }

    $rootScope.scrollTo = function (id) {
        var old = $location.hash();
        $location.hash(id);
        $anchorScroll();
        $location.hash(old);
    }

    $scope.openMenu = function () {
        $('.left-side-icon-bar').toggleClass('hover-this');
        $('.menu').toggleClass('move-this');
        $('.screen-backdrop').toggleClass('backdrop');
    };

    $scope.hideMenu = function () {
        $('.left-side-icon-bar').removeClass('hover-this');
        $('.menu').removeClass('move-this');
        $('.screen-backdrop').removeClass('backdrop');
    };

    $rootScope.partnersForFilterList = [{ partnerId: 0, partnerCd: 0, partnerName: '-- All Brands --' }]

    $rootScope.userDara = service.whichUser().then(function (user) {
        $rootScope.allowedFeatureList = user.features;
        $rootScope.isSuperAdmin = (user.userType == 2);
        $rootScope.isConfigAdminUser = (user.userType == 1) || $rootScope.isSuperAdmin;
        $rootScope.isInternalUser = user.userType == 5;
        $rootScope.isSalesAdmin = (user.userType == 4) || $rootScope.isConfigAdminUser;
        $rootScope.isOpsAdmin = (user.userType == 6) || $rootScope.isConfigAdminUser;
        $rootScope.isReadOnlyAdmin = (user.userType == 3) || $rootScope.isConfigAdminUser;
        $rootScope.isDistributorAdmin = user.userType == 5;
        $rootScope.name = user.name ? user.name : user.username;
        $rootScope.username = user.username ? user.username : user.name;
        $rootScope.LoggedInUserId = user.userId;
        angular.forEach(user.partners, function (partner) {
            $rootScope.partnersForFilterList.push(partner);
        });
        $rootScope.accountInfo = user.accountInfo && user.accountInfo[0] ? user.accountInfo[0] : null;
        if (user.userType < 100) {
            ajaxCall.get('/Client/getAllClients', { contentType: "application/json; charset=utf-8", dataType: "json" }).then(function (response) {
                if (response.status == 0)
                    $rootScope.clientsList = response.data.data;
            });
            ajaxCall.get('/Client/GetInternalAccounts').then(function (response) {
                $rootScope.internalAccounts = response.data;
            });
        }
        ajaxCall.get('/Kelp/getAccountStatement').then(function (response) {
            if (response.status == 0)
                $rootScope.finalBalance = response.data.finalBalance;
        });
        
    });

    $scope.userBrand = service.getUserBrand().then(function (brandId) {
        $scope.routerFilters.selected_PartnerId = brandId;
        $scope.partnerId = brandId;
    });

    $scope.setBrand = function () {
        service.setUserBrand({ partnerId: $scope.partnerId }).then(function () {
            service.getSMSStaticDetails().then(function (response) {
                if (response && response.senderIds)
                    $rootScope.senderIds = response.senderIds;
            });
        });
        $state.reload();
    }
    
    $rootScope.$on('InfoMessage', function (event, data) {
        var msgContainer = $('#message-container');
        $scope.message = data;
        msgContainer.removeClass('display-none');
        msgContainer.addClass(data.type);
        setTimeout(function () {
            msgContainer.addClass('display-none');
        }, 60000);
    });

    $rootScope.router = {
        selected: null
    }

    $scope.leftRail = [
        { icon: 'home_low', label: 'Home', sref: 'landing.home', feature:'' },
        { icon: 'config64', label: 'Settings', sref: 'landing.settings', feature: 30 },
        { icon: 'storeops64', label: 'Store Operations', sref: 'landing.storeOperations', feature: 31 },
        { icon: 'sms64', label: 'SMS', sref: 'landing.smsSender', feature: 7 },
        { icon: 'sms_schedule48', label: 'Prime Schedule', sref: 'landing.primeSms', feature: 13 },
        { icon: 'promotions64', label: 'Promotion', sref: 'landing.promotion', feature: 8 },
        { icon: 'reports_rail_icon', label: 'Reports', sref: 'landing.reports', feature: '' },
        { icon: 'usrmngmt64', label: 'User Management', sref: 'landing.userManagement', feature: 50 },
        { icon: 'globalreport64', label: 'Global Reports', sref: 'landing.globalReport', feature: 54 },
        { icon: 'wofrlogo', label: 'WOFR', sref: 'landing.wofrOffer', feature: 56 }
    ];

    $scope.data = {
        selected: ''
    }

    $scope.hideMenu = function () {
        $('.left-side-icon-bar').removeClass('hover-this');
        $('.menu').removeClass('move-this');
        $('.screen-backdrop').removeClass('backdrop');
    };

    $rootScope.featureEnabled = function (feature) {
        if ($rootScope.isConfigAdminUser) return 1;
        if ($rootScope.isInternalUser && feature == 54) return 1;
        if ($rootScope.isSalesAdmin && feature == 23) return 1;
        if ($rootScope.isReadOnlyAdmin && feature == 23) return 1;
        if ($rootScope.allowedFeatureList) return $rootScope.allowedFeatureList[feature];
    }

    $rootScope.logout = function () {
        service.logout();
    };

    $rootScope.getNasid = function () {
        return $rootScope.router.selected.nasid;
    }

    $scope.cities = ["New Delhi", "Lucknow", "Pune", "Darjeeling", "Cochin", "Haridwar", "Udaipur", "Baroda", "Rishikesh", "Gandhinagar", "Sambhalpur", "Hyderabad", "Gandhidham", "Bhavnagar", "Goa", "juhapura", "Raipur", "Guwahati", "Rajkot", "Trichy", "Faridabad", "Mehsana", "Ahmdabad", "Vapi", "Ludhiana", "Jodhpur", "Chandigarh", "Ahmedabad", "Indore", "Bikaner", "Bangalore", "Mumbai", "Ooty", "Jamnagar", "Nashik", "Tanjore", "Ajmer", "Bengaluru", "Hyderbad", "Noida", "Varanasi", "New Delh", "Agra", "Gurugram", "Jabalpur", "Mysore", "Gurgaon", "Surat", "HIMMATNAGAR", "Chennai", "Zirakpur", "Roorkee", "Rohtak", "Nagpur", "Kolkata", "Pondicherry", "Ghaziabad", "Banglore", "Coimbatore", "Shambhalpur", "Amritsar", "Porbandar", "Mohali", "Secunderabad", "Valsad", "Mangalore", "Hisar", "Jalandhar", "Delhi", "Palanpur", "Vadodara", "Kanpur", "Dehradun", "Bhopal", "Jaipur", "Salem", "Angul", "Anagul", "Siliguri", "Tirunelveli"];

    $scope.availableCategories = [
        { display: 'Merchant', value: 'merchant' },
        { display: 'Hotel', value: 'hotel' },
        { display: 'Longterm Stays', value: 'longstay' },
        { display: 'Transport', value: 'transport' },
        { display: 'Office', value: 'office' }
    ];
        
    $rootScope.pageNumberForLoc = 0;
    $rootScope.pageSizeForLoc = 500;
    $scope.$parent.routerDetails = [];
    $scope.$parent.positiveNases = [];
    $scope.$parent.allLocationNases = [];
    $rootScope.loadLocationsForSelect = function () {
        $scope.userBrand.then( function() {
            queryObjectCopy = angular.copy(queryObject);
            queryObjectCopy.pageNumber = $rootScope.pageNumberForLoc + 1;
            queryObjectCopy.pageSize = $rootScope.pageSizeForLoc;
            queryObjectCopy.storeTags = queryObjectCopy.storeTags ? queryObjectCopy.storeTags : "%";
            if ($scope.routerFilters.selected_PartnerId && $scope.routerFilters.selected_PartnerId > 0)
                queryObjectCopy.partnerId = $scope.routerFilters.selected_PartnerId;
            $rootScope.allLocationData = service.getLocationsForAdmin(queryObjectCopy).then(function (locations) {
                $rootScope.pageNumberForLoc = $rootScope.pageNumberForLoc + 1;
                var routerDetails = locations.filter(function (store,index) {
                    //if (store.storeName && store.mmNasId) $scope.$parent.routerDetails.push(store);
                    if (store.storeName) {
                        $scope.$parent.routerDetails.push(store);
                        $scope.$parent.allLocationNases.push(store.nasid);
                    }
                });
                var positiveNases = locations.filter(function (store) {
                    if (store.storeName && store.nasid > 0) $scope.$parent.positiveNases.push(store);
                });
            });
        });
    }

    //$scope.$parent.storeTags = [{ tagname: 'Pilot'}, { tagname: 'Defaulter'}]
    $scope.$parent.storeTags = [
        'Zoho_Inactive',
        'Defaulter', 
        'Pilot',
        'Swap_Promo_off',
        'i2e1_Owned_Internet',
        'Facebook',
        'Connect_Bharat'
    ];

    /*************************** Code block added for loading stores by scrolling in ui- select ******************/
    $scope.$parent.storesList = [];

    $scope.$parent.searchLocations = function (directiveFn) {
        $scope.directiveFn = directiveFn;
    };

    

    $scope.roundToTwo = function (num) {
        return roundToTwo(num);
    }

}]);

app.controller('homeController', ['$rootScope', '$scope', '$state', '$modal', '$filter', 'ajaxCall', 'service', 'messageService', 'fileUploadService', function ($rootScope, $scope, $state, $modal, $filter, ajaxCall, service, messageService, fileUploadService) {
    $scope.partners = partners;

    $scope.states = states;

    //$scope.routerFilters = {};

    //$scope.availableSubCategories = availableSubCategories;
    $scope.selectedInsState = 1;

    $rootScope.userDara.then(function (result) {
        $rootScope.installationsStates = [
            { display: "----All-----", value: "", isVisible: ($rootScope.isSalesAdmin) ? true : false },
            { display: "Closed", value: "2", isVisible: ($rootScope.isSalesAdmin) ? true : false },
            { display: "Uninstalled", value: "0", isVisible: ($rootScope.isSalesAdmin) ? true : false },
            { display: "Installed", value: "1", isVisible: true },
            { display: "In Transition", value: "3", isVisible: true }
        ];
    });

    $scope.filter = {};

    $scope.getStoreContact = function (nasid) {
        service.getStoreContact(nasid).then(function (data) {
            var obj = getObjectFromArray($scope.storeList, 'nasid', nasid);
            if (obj)
                obj.contactDetail = data ? data : 'Not Found';
        });
    }

    $scope.$on('params-updated', function () {
        $scope.activeRouters = 0;
        $scope.inactiveRouters = 0;
        $scope.activeRoutersPercent = parseInt($scope.activeRouters / ($scope.activeRouters + $scope.inactiveRouters) * 100);

        service.getLastLoginTime($scope.groupOfNases).then(function (data) {
            for (var property in data) {
                if (data.hasOwnProperty(property)) {
                   
                    obj = getObjectFromArray($scope.storeList, 'nasid', property);
                    if (obj.active == 0 || data[property] < 10) 
                        obj.css = 'row_success'
                    else if (obj.active == 1 || data[property] < 30)
                        obj.css = 'row_warning';
                    else
                        obj.css = 'row_danger';
                    if (obj && data[property] !== '-')
                        obj.lastUsed = roundOffTime(data[property] * 60);
                    else if (obj)
                        obj.lastUsed = '-';
                   
                   
                }
            }
        });

        service.getUsersInXHours($scope.groupOfNases, 24).then(function (data) {
            for (var property in data) {
                if (data.hasOwnProperty(property)) {
                    obj = getObjectFromArray($scope.storeList, 'nasid', property);
                    if (obj)
                        obj.inLast24Hours = data[property];
                }
            }
        });

        service.getDataUsed($scope.groupOfNases).then(function (data) {
            for (var property in data) {
                if (data.hasOwnProperty(property)) {
                    obj = getObjectFromArray($scope.storeList, 'nasid', property);
                    if (obj)
                        obj.dataUsedInGB = Math.floor(1000 * data[property] / 1024) / 1000;
                }
            }
        });
    });

    var getLocationForAdmin = function (qObj) {
        $scope.userBrand.then(function () {
            $scope.groupOfNases = '';
            //qObj.installedState = $scope.selectedInsState ? $scope.selectedInsState : 1;
            qObj.installedState = $scope.selectedInsState;
            qObj.city = $scope.selectedCity;
            qObj.category = $scope.selectedCategory;
            //qObj.partnerCode = $scope.selected_PartnerCode;
            qObj.partnerId = $scope.routerFilters.selected_PartnerId;
            qObj.partnerAccountType = $scope.routerFilters.selected_PartnerAccountType;
            qObj.storeTags = qObj.storeTags ? qObj.storeTags : ($scope.filter.selectedTags ? $scope.filter.selectedTags : "%");
            if (!isNaN($scope.searchTerm)) {
                qObj.nasid = $scope.searchTerm;
            } else {
                qObj.storeName = qObj.storeNameAlias = qObj.address = qObj.state = $scope.searchTerm;
            }
            //console.log('loading page ' + qObj.pageNumber);
            service.getLocationsForAdmin(qObj).then(function (locations) {
                angular.forEach(locations, function (value, index) {
                    if ($rootScope.username === '<EMAIL>' || $rootScope.name === '<EMAIL>') {
                        service.demofizeStores(value)
                    }
                    value.lastUsed = '<img src="../images/ellipsis.gif">';
                    value.inLast24Hours = '<img src="../images/ellipsis.gif">';

                    if (value.category == '')
                        value.category = "N/A"
                    else if (value.category.indexOf(',') > -1)
                        value.category = value.category.split(',')[0];

                    value.routerState = value.routerState.toString();

                    if (value.routerState != "2") {
                        if (value.lastPingDelay < 0)
                            value.lastPingDelay = -value.lastPingDelay;
                        if (value.lastPingDelay > 35) value.downSinceHalfHour = true;
                        value.lastPingDelay = roundOffTime(value.lastPingDelay * 60);
                    }

                    $scope.storeList.push(value);

                    if (index < locations.length - 1) {
                        $scope.groupOfNases += value.nasid + ','
                    } else {
                        $scope.groupOfNases += value.nasid;
                    }
                });
                //console.log('loaded page ' + qObj.pageNumber);
                if (locations.length < $scope.pageSize)
                    $scope.data.infiniteScrollDisabled = true;
                else
                    $scope.data.infiniteScrollDisabled = false;
                $scope.$broadcast('params-updated');
            });
        });
    }

    
    $scope.pageNumber = 0;
    $scope.pageSize = 50;

    $scope.storeList = [];
    $scope.data = $scope.data || {};
    $scope.data.infiniteScrollDisabled = false;
    $scope.data.showClientForm = false;

    $scope.loadMore = function () {
        if ($scope.data.infiniteScrollDisabled) return;
        
        $scope.pageNumber = $scope.pageNumber + 1;

        queryObjectCopy = angular.copy(queryObject);
        queryObjectCopy.pageNumber = $scope.pageNumber;
        queryObjectCopy.pageSize = $scope.pageSize;
        $scope.data.infiniteScrollDisabled = true;
        getLocationForAdmin(queryObjectCopy);
    }

    $scope.filterRouters = function (options) {
        options = options || {};
        var queryObjectCopy = angular.copy(queryObject);
        queryObjectCopy.pageNumber = $scope.pageNumber = 1;
        queryObjectCopy.pageSize = 50;
        $scope.storeList = [];
        getLocationForAdmin(queryObjectCopy);
        options.stats && getDevicesStats(queryObjectCopy);
        if (queryObjectCopy.hasOwnProperty('partnerId') && typeof queryObjectCopy.partnerId != "undefined") {
            service.setUserBrand({ partnerId: queryObjectCopy.partnerId });
        } else if (queryObjectCopy.hasOwnProperty('partnerId') && queryObjectCopy.partnerId == "undefined") {
            service.setUserBrand({ partnerId: 0 });
        } else {
            service.setUserBrand({ partnerId: 0 });
        }
        options.scrollTo && $rootScope.scrollTo(options.scrollTo);
    }

    $scope.filterRoutersByTags = function (options) {
        options = options || {};
        var queryObjectCopy = angular.copy(queryObject);
        queryObjectCopy.pageNumber = $scope.pageNumber = 1;
        queryObjectCopy.pageSize = 50;
        $scope.storeList = [];
        var stortagsstr = "";
        if ($scope.filter.selectedTags && $scope.filter.selectedTags.length > 0) {
            angular.forEach($scope.$parent.storeTags, function (value, index) {
                if ($scope.filter.selectedTags.indexOf(value) > -1) {
                    stortagsstr = stortagsstr + "1,";
                }
                else {
                    stortagsstr = stortagsstr + "_,";
                }
            })
            
        } else {
            angular.forEach($scope.$parent.storeTags, function (value, index) {
                stortagsstr = stortagsstr + "_,";
            })
            
        }
        queryObjectCopy.storeTags = stortagsstr;
        getLocationForAdmin(queryObjectCopy);
        options.stats && getDevicesStats(queryObjectCopy);
        options.scrollTo && $rootScope.scrollTo(options.scrollTo);
    }

    var fetchFilters = function () {
        var getFilter = function (filter) {
            ajaxCall.get('/Client/GetFilterOptions?filter='+filter).then(function (response) {
                $scope.selectFilters = $scope.selectFilters || {};
                $scope.selectFilters[filter] = response.data;
            });
        }
        //getFilter('partner_cd');
        getFilter('shop_city');
        getFilter('category');
    }

    fetchFilters();

    function getDevicesStats(qObj) {
        $scope.deviceStats = null;
        qObj = qObj || {};
        qObj.pageNumber = 1;
        qObj.pageSize = 9999;
        //qObj.installedState = $scope.selectedInsState ? $scope.selectedInsState : 1;
        qObj.installedState = $scope.selectedInsState;
        qObj.city = $scope.selectedCity;
        qObj.category = $scope.selectedCategory;
        //qObj.partnerCode = $scope.selected_PartnerCode;
        qObj.partnerId = $scope.routerFilters.selected_PartnerId;
        qObj.partnerAccountType = $scope.routerFilters.selected_PartnerAccountType;

        service.getActiveInactiveDevicesCount(qObj).then(function (stats) {
            $scope.deviceStats = stats;
            $scope.deviceStats.activeDevicesPerc = Math.round((stats.activeDeviceCount / stats.totalDeviceCount) * 100);
            $scope.deviceStats.inActiveDevicesPerc = Math.round((stats.totalDeviceCount - stats.activeDeviceCount) / stats.totalDeviceCount * 100);
            $scope.deviceStats.inActiveDeviceCount = stats.totalDeviceCount - stats.activeDeviceCount;
        });
    }

    $scope.userBrand.then(function () {
        getDevicesStats();
    })

    $scope.deleteStoreController = function (store) {
        ajaxCall.get('/Admin/DeleteStoreController?nasid=' + store.nasid + '&controllerid=' + store.controllerid).then(function (response) {
            //$state.go('landing.home', {}, { reload: true });
            $scope.storeList.splice($scope.storeList.indexOf(store), 1);
        });
    }


    $scope.updateFeaturesForUser = function (userId, userType, product) {
        var postData = {
            userId: userId,
            userType: userType,
            product: product
        };
        ajaxCall.post('/Client/UpdateTAdmin', postData).then(function (response) {
        });
    }

    //$scope.marketPlace;
    $scope.storeAdress;
    $scope.i2e1Categories = i2e1Categories;
   
    $scope.showBrandEditDialog = function (location) {
        ajaxCall.get('/Client/GetStoreDetails?nasid=' + location.nasid).then(function (response) {
            //store = angular.copy(store);
            if (response && response.data){
                var store = angular.copy(response.data);
                store.locationRetagDate = new Date(store.locationRetagDate);
                store.locationStartDate = new Date(store.locationStartDate);
                store.locationCloseDate = new Date(store.locationCloseDate);
                var modalInstance = showDialog($modal, '/brandEdit.html', {
                    data: {
                        send_email: false,

                        username_changed: false,

                        access_changed: false,

                        store: store,

                        client: $scope.clientsList.filter(function (item) {
                            if (item.clientId == store.clientId)
                                return item;
                        })[0],

                        states: $scope.states,

                        i2e1Categories: i2e1Categories,

                        expand: false,

                        tab: 'client_info',

                        tab_head: 'Client Info',

                        i2e1Installers: $rootScope.internalAccounts,

                        i2e1SalesPersons: $rootScope.internalAccounts,

                        uploadPartnerLogo: function () {
                            setTimeout(function () {
                                document.getElementById('partner_logo_file_upload').click()
                                $scope.clicked = true;
                            }, 0);
                        },

                        uploadPartnerImage: function () {
                            setTimeout(function () {
                                document.getElementById('partner_image_file_upload').click()
                                $scope.clicked = true;
                            }, 0);
                        },

                        changed_access: function (data) {
                            data.access_changed = true;
                        },

                        changed_user: function (data) {
                            data.username_changed = true;
                        },

                        updateClientDetails: function (data, client) {
                            $('.form-group').removeClass('has-error');
                            if (client.clientName == null || client.clientName == '') {
                                var element = angular.element('[ng-model="data.client.clientName"]');
                                element[0].parentNode.className += " has-error";
                                return;
                            }

                            ajaxCall.post('/Client/updateClientDetails', {
                                'client': client
                            }).then(function (response) {
                                data.client = response.data;
                                if (data.client.clientId > 0) {
                                    ajaxCall.get('/Client/getAllClients', { contentType: "application/json; charset=utf-8", dataType: "json" }).then(function (response) {
                                        $rootScope.clientsList = response.data.data;
                                    });

                                    ajaxCall.post('/Client/getClientPartners', {
                                        'client': client
                                    }).then(function (response) {
                                        data.partnerList = response.data.data;
                                        angular.forEach(data.partnerList, function (partner, index) {
                                            if (partner.subscriptionStartDate != "") {
                                                data.partnerList[index].subscriptionStartDate = moment(partner.subscriptionStartDate, 'MM/DD/YYYY hh:mm:ss a').toISOString();
                                            }

                                            if (partner.subscriptionRenewalDate != "") {
                                                data.partnerList[index].subscriptionRenewalDate = moment(partner.subscriptionRenewalDate, 'MM/DD/YYYY hh:mm:ss a').toISOString();
                                            }
                                        });
                                        data.partner = data.partnerList.filter(function (item) {
                                            if (item.partnerId == store.partnerId) {
                                                return item;
                                            }
                                        })[0];
                                        $scope.oldPartner = angular.copy(data.partner);
                                        data.tab = 'client_contacts';
                                        data.tab_head = 'Client Contacts';
                                        data.getClientContacts(data, client.clientId);
                                        var partnerNameForFileUpload = Math.round((new Date()).getTime() / 1000);
                                        if (data.partner && data.partner.partnerName) {
                                            partnerNameForFileUpload = data.partner.partnerName;
                                        }

                                        fileUploadService.initializeUpload('#upload_partner_logo', '#partner_logo_result_container', partnerNameForFileUpload + '/logo', function (response) {
                                            $scope.$apply(function () {
                                                data.partner.partnerLogo = response.msg;
                                                $('#partnerLogoImage').attr('src', response.msg);
                                            });
                                        }, {
                                            maxSize: 2e+6,
                                            'uploadUrl': '/Client/UploadFile',
                                            'uploadFolder': 'partners/'
                                        });

                                        fileUploadService.initializeUpload('#upload_partner_image', '#partner_image_result_container', partnerNameForFileUpload + '/images', function (response) {
                                            $scope.$apply(function () {
                                                data.partner.partnerImage = response.msg;
                                                $('#partnerImage').attr('src', response.msg);
                                            });
                                        }, {
                                            maxSize: 2e+6,
                                            'uploadUrl': '/Client/UploadFile',
                                            'uploadFolder': 'partners/'
                                        });
                                    });
                                }
                            });
                        },

                        getClientContacts: function (data, clientId) {
                            data.selectedClientContact = {};
                            ajaxCall.post('/Client/getClientContactPersons', {
                                'clientId': clientId
                            }).then(function (response) {
                                data.clientContactList = response.data.data;

                                //data.selectedClientContact = response.data.data[0];
                            });
                        },

                        updateClientContactPersons: function (data, clientId) {
                            $('.form-group').removeClass('has-error');

                            if (!data.selectedClientContact || data.selectedClientContact.name == null || data.selectedClientContact.name == '') {
                                var element = angular.element('[ng-model="data.selectedClientContact.name"]');
                                element[0].parentNode.className += " has-error";
                                return;
                            }
                            if (data.selectedClientContact.username == null || data.selectedClientContact.username == '' || !isValidEmail(data.selectedClientContact.username)) {
                                var element = angular.element('[ng-model="data.selectedClientContact.username"]');
                                element[0].parentNode.className += " has-error";
                                return;
                            }

                            if (data.selectedClientContact.mobile == null || data.selectedClientContact.mobile == '' || !isValidPhoneNumber(data.selectedClientContact.mobile)) {
                                var element = angular.element('[ng-model="data.selectedClientContact.mobile"]');
                                element[0].parentNode.className += " has-error";
                                return;
                            }

                            //if (data.selectedClientContact.hasOwnProperty('userId')) {
                            //    data.selectedClientContact.userId = -1;
                            //    data.selectedClientContact.userType = 100;
                            //}
                            if (data.selectedClientContact.userId == -1) {
                                $scope.ifUserNameExists = service.checkIfContactExists(data.selectedClientContact.username).then(function (result) {
                                    if (result == false) {
                                        //email condition
                                        if (data.selectedClientContact.active == true) {
                                            if (!data.selectedClientContact.isGreeted) {
                                                if ((isValidEmail(data.selectedClientContact.username))) {
                                                    console.log("new user with access, send welcome email", data.selectedClientContact);
                                                    data.send_email = true;
                                                    //data.selectedClientContact.isGreeted = 1;
                                                    //set emailsender true
                                                }
                                            }
                                        }
                                        //email condition end
                                        ajaxCall.post('/Client/updateClientContactPersons', {
                                            'user': data.selectedClientContact,
                                            'clientId': clientId,
                                            'emailCheck': data.send_email,
                                            'emailChanged': data.username_changed
                                        }).then(function (response) {
                                            data.selectedClientContact = {};
                                            data.getClientContacts(data, clientId);
                                            messageService.showSuccess('Contact is updated successfully.', 3000);
                                            ajaxCall.post('/Client/updateClientContactPersonsFeatures', {
                                                'userId': response.data.userId,
                                                'userType': response.data.userType,
                                                'clientId': clientId
                                            }).then(function (response) {
                                                console.log(response);
                                            });
                                        });
                                    } else {
                                        messageService.showError('Contact already exists. Please go to user management to allocate more clients.', 3000);
                                        var element = angular.element('[ng-model="data.selectedClientContact.username"]');
                                        element[0].parentNode.className += " has-error";
                                        return;
                                    }

                                })

                            } else {
                                //email condition
                                if (data.selectedClientContact.active == true) {
                                    if (!data.access_changed) {
                                        if (data.username_changed) {
                                            $scope.ifUserNameExists = service.checkIfContactExists(data.selectedClientContact.username).then(function (result) {
                                                if (result == false) {
                                                    //if (!data.selectedClientContact.isGreeted) {
                                                    if ((isValidEmail(data.selectedClientContact.username))) {
                                                        console.log("email changed, send welcome email", data.selectedClientContact)
                                                        data.send_email = true;
                                                        //data.selectedClientContact.isGreeted = 1;
                                                        //set emailsender true 
                                                    }
                                                    ajaxCall.post('/Client/updateClientContactPersons', {
                                                        'user': data.selectedClientContact,
                                                        'clientId': clientId,
                                                        'emailCheck': data.send_email,
                                                        'emailChanged': data.username_changed
                                                    }).then(function (response) {
                                                        data.selectedClientContact = {};
                                                        data.getClientContacts(data, clientId);
                                                        messageService.showSuccess('Contact is updated successfully.', 3000);
                                                        data.username_changed = false;
                                                        data.access_changed = false;
                                                        data.send_email = false;
                                                        ajaxCall.post('/Client/updateClientContactPersonsFeatures', {
                                                            'userId': response.data.userId,
                                                            'userType': response.data.userType,
                                                            'clientId': clientId
                                                        }).then(function (response) {
                                                            console.log(response);
                                                        });
                                                        //data.newContactObj = {};
                                                        //data.clientContactForm.$setPristine();
                                                    });
                                                    //}
                                                } else {
                                                    messageService.showError('Contact already exists. Please go to user management to allocate more clients.', 3000);
                                                    var element = angular.element('[ng-model="data.selectedClientContact.username"]');
                                                    element[0].parentNode.className += " has-error";
                                                    return;
                                                }
                                            })
                                        } else {
                                            ajaxCall.post('/Client/updateClientContactPersons', {
                                                'user': data.selectedClientContact,
                                                'clientId': clientId,
                                                'emailCheck': data.send_email,
                                                'emailChanged': data.username_changed
                                            }).then(function (response) {
                                                data.selectedClientContact = {};
                                                data.getClientContacts(data, clientId);
                                                messageService.showSuccess('Contact is updated successfully.', 3000);
                                                data.username_changed = false;
                                                data.access_changed = false;
                                                data.send_email = false;
                                                ajaxCall.post('/Client/updateClientContactPersonsFeatures', {
                                                    'userId': response.data.userId,
                                                    'userType': response.data.userType,
                                                    'clientId': clientId
                                                }).then(function (response) {
                                                    console.log(response);
                                                });
                                                //data.newContactObj = {};
                                                //data.clientContactForm.$setPristine();
                                            });
                                        }
                                    } else {
                                        if (!data.selectedClientContact.isGreeted) {
                                            if ((isValidEmail(data.selectedClientContact.username))) {
                                                console.log("access changed, send welcome email", data.selectedClientContact)
                                                data.send_email = true;
                                                //set emailSender  true
                                                //data.selectedClientContact.isGreeted = 1;  
                                            }
                                        }
                                        ajaxCall.post('/Client/updateClientContactPersons', {
                                            'user': data.selectedClientContact,
                                            'clientId': clientId,
                                            'emailCheck': data.send_email,
                                            'emailChanged': data.username_changed
                                        }).then(function (response) {
                                            data.selectedClientContact = {};
                                            data.getClientContacts(data, clientId);
                                            messageService.showSuccess('Contact is updated successfully.', 3000);
                                            //data.newContactObj = {};
                                            //data.clientContactForm.$setPristine();
                                            data.username_changed = false;
                                            data.access_changed = false;
                                            data.send_email = false;
                                            ajaxCall.post('/Client/updateClientContactPersonsFeatures', {
                                                'userId': response.data.userId,
                                                'userType': response.data.userType,
                                                'clientId': clientId
                                            }).then(function (response) {
                                                console.log(response);
                                            });
                                        });
                                    }
                                }
                                    //email condition end
                                else {
                                    ajaxCall.post('/Client/updateClientContactPersons', {
                                        'user': data.selectedClientContact,
                                        'clientId': clientId,
                                        'emailCheck': data.send_email,
                                        'emailChanged': data.username_changed
                                    }).then(function (response) {
                                        data.selectedClientContact = {};
                                        data.getClientContacts(data, clientId);
                                        messageService.showSuccess('Contact is updated successfully.', 3000);
                                        data.username_changed = false;
                                        data.access_changed = false;
                                        data.send_email = false;
                                        ajaxCall.post('/Client/updateClientContactPersonsFeatures', {
                                            'userId': response.data.userId,
                                            'userType': response.data.userType,
                                            'clientId': clientId
                                        }).then(function (response) {
                                            console.log(response);
                                        });
                                        //data.newContactObj = {};
                                        //data.clientContactForm.$setPristine();
                                    });
                                }
                            }

                        },

                        removeClientContact: function (contact, data, clientId) {
                            ajaxCall.post('/Client/removeContact', {
                                'user': contact,
                                'mappingId': clientId,
                                type: 'client'
                            }).then(function (response) {
                                data.getClientContacts(data, clientId);
                                messageService.showSuccess('Contact is removed successfully.', 3000);
                            });
                        },
                
                        updateClientsPartnerDetails: function (data, partner) {
                            $('.form-group').removeClass('has-error');
                            if (!partner || partner.partnerName == null || partner.partnerName == '') {
                                var element = angular.element('[ng-model="data.partner.partnerName"]');
                                element[0].parentNode.className += " has-error";
                                messageService.showError('Please add partner name.', 3000);
                                return;
                            }
                            if (partner.category == null || partner.category == '') {
                                var element = angular.element('[ng-model="data.partner.category"]');
                                element[0].parentNode.className += " has-error";
                                messageService.showError('Please choose partner category.', 3000);
                                return;
                            }

                            if (partner.subCategory == null || partner.subCategory == '') {
                                var element = angular.element('[ng-model="data.partner.subCategory"]');
                                element[0].parentNode.className += " has-error";
                                messageService.showError('Please choose partner sub category.', 3000);
                                return;
                            }

                            if (partner.accountType == null || partner.accountType == '') {
                                var element = angular.element('[ng-model="data.partner.accountType"]');
                                element[0].parentNode.className += " has-error";
                                messageService.showError('Please choose partner account type.', 3000);
                                return;
                            }

                            if (partner.productType == null || partner.productType == '') {
                                var element = angular.element('[ng-model="data.partner.productType"]');
                                element[0].parentNode.className += " has-error";
                                messageService.showError('Please choose partner product type.', 3000);
                                return;
                            }

                            ajaxCall.post('/Client/getPartnersDetails', {
                                'partnerName': partner.partnerName
                            }).then(function (response) {
                                if (partner.clientId == 0) {
                                    data.tab = 'client_info';
                                    data.tab_head = 'Client Info';
                                    data.showClientForm = false;
                                } else {
                                    if ((response.data && response.data.partnerId == partner.partnerId) || (partner.partnerId && !response.data.partnerId) || response.data.partnerId == 0) {
                                        ajaxCall.post('/Client/updateClientsPartnerDetails', {
                                            'partner': partner
                                        }).then(function (response) {
                                            //alert(partner.productType);
                                            if ($scope.oldPartner && $scope.oldPartner.productType && $scope.oldPartner.productType != partner.productType) {
                                                $scope.oldPartner.productType = angular.copy(partner.productType);
                                                ajaxCall.post('/Client/UpdatePartnerContactFeatures', {
                                                    'partnerId': partner.partnerId,
                                                    'product': partner.productType
                                                }).then(function (response) {
                                                    //console.log(response);
                                                });
                                            }

                                            data.partner = response.data;
                                            if (data.partner.subscriptionStartDate != "") {
                                                data.partner.subscriptionStartDate = moment(data.partner.subscriptionStartDate, 'MM/DD/YYYY hh:mm:ss a').toISOString();
                                            }

                                            if (partner.subscriptionRenewalDate != "") {
                                                data.partner.subscriptionRenewalDate = moment(data.partner.subscriptionRenewalDate, 'MM/DD/YYYY hh:mm:ss a').toISOString();
                                            }
                                            data.partnerList.push(data.partner);

                                            data.store.clientId = data.client.clientId;
                                            data.store.partner = data.partner.partnerCd;
                                            data.store.partnerId = data.partner.partnerId;
                                            if (data.store.category == '' || data.store.category == null || data.store.category == 'N/A') {
                                                data.store.category = data.partner.category;
                                                if (data.store.subCategory == '' || data.store.subCategory == null || data.store.category == 'N/A') {
                                                    data.store.subCategory = data.partner.subCategory;
                                                }
                                            }
                                            data.tab = 'partner_contacts';
                                            data.tab_head = 'Partner Contacts';
                                            data.getPartnerContacts(data, data.partner.partnerId);
                                        });
                                    } else {
                                        messageService.showError('Partner name is already exist.', 3000);
                                    }
                                }
                            });
                        },

                        getPartnerContacts: function (data, partnerId) {
                            ajaxCall.post('/Client/getPartnerContactPersons', {
                                'partnerId': partnerId
                            }).then(function (response) {
                                data.partnerContactList = response.data.data;
                                data.selectedPartnerContact = {};
                            });
                        },

                        updatePartnerContactPersons: function (data, partnerId) {
                            $('.form-group').removeClass('has-error');
                            if (!data.selectedPartnerContact || data.selectedPartnerContact.name == null || data.selectedPartnerContact.name == '') {
                                var element = angular.element('[ng-model="data.selectedPartnerContact.name"]');
                                element[0].parentNode.className += " has-error";
                                return;
                            }

                            if (data.selectedPartnerContact.username == null || data.selectedPartnerContact.username == '') {
                                var element = angular.element('[ng-model="data.selectedPartnerContact.username"]');
                                element[0].parentNode.className += " has-error";
                                return;
                            }

                            if (data.selectedPartnerContact.mobile == null || data.selectedPartnerContact.mobile == '' || !isValidPhoneNumber(data.selectedPartnerContact.mobile)) {
                                var element = angular.element('[ng-model="data.selectedPartnerContact.mobile"]');
                                element[0].parentNode.className += " has-error";
                                return;
                            }

                            if (data.selectedPartnerContact.userId == -1 && (isValidEmail(data.selectedPartnerContact.username) || isValidIndianMobile(data.selectedPartnerContact.username))) {
                                service.checkIfContactExists(data.selectedPartnerContact.username).then(function (result) {
                                    if (result == false) {
                                        //email condition
                                        if (data.selectedPartnerContact.active == true) {
                                            if ((isValidEmail(data.selectedPartnerContact.username))) {
                                                console.log("new user with access, send welcome email", data.selectedPartnerContact);
                                                data.send_email = true;
                                                //data.selectedPartnerContact.isGreeted = 1;
                                                //set emailsender true
                                            }
                                        }
                                        //email condition end

                                        ajaxCall.post('/Client/updatePartnerContactPersons', {
                                            'user': data.selectedPartnerContact,
                                            'partnerId': partnerId,
                                            'emailCheck': data.send_email,
                                            'emailChanged': data.username_changed
                                        }).then(function (response) {
                                            data.selectedPartnerContact = {};
                                            data.getPartnerContacts(data, partnerId);
                                            $scope.updateFeaturesForUser(response.data.userId, response.data.userType, data.partner.productType);
                                            messageService.showSuccess('Contact is updated successfully.', 3000);
                                        });
                                    } else {
                                        messageService.showError('Contact already exists. Please go to user management to allocate more partners.', 3000);
                                        var element = angular.element('[ng-model="data.selectedPartnerContact.username"]');
                                        element[0].parentNode.className += " has-error";
                                        return;
                                    }

                                })

                            } else {
                                //email condition
                                if (data.selectedPartnerContact.active == true) {
                                    if (!data.access_changed) {
                                        if (data.username_changed) {
                                            service.checkIfUsernameExist(data.selectedPartnerContact.username).then(function (result) {
                                                if (result == 0 || result == data.selectedPartnerContact.userId) {
                                                    if ((isValidEmail(data.selectedPartnerContact.username))) {
                                                        console.log("email changed, send welcome email", data.selectedClientContact)
                                                        data.send_email = true;
                                                        //data.selectedPartnerContact.isGreeted = 1;
                                                    }
                                                    ajaxCall.post('/Client/updatePartnerContactPersons', {
                                                        'user': data.selectedPartnerContact,
                                                        'partnerId': partnerId,
                                                        'emailCheck': data.send_email,
                                                        'emailChanged': data.username_changed
                                                    }).then(function (response) {
                                                        data.selectedPartnerContact = {};
                                                        data.getPartnerContacts(data, partnerId);
                                                        messageService.showSuccess('Contact is updated successfully.', 3000);
                                                        data.username_changed = false;
                                                        data.access_changed = false;
                                                        data.send_email = false;
                                                    });
                                                } else {
                                                    messageService.showError('Contact already exists. Please go to user management to allocate more locations.', 3000);
                                                    var element = angular.element('[ng-model="data.selectedPartnerContact.username"]');
                                                    element[0].parentNode.className += " has-error";
                                                    return;
                                                }
                                            });
                                        } else {
                                            ajaxCall.post('/Client/updatePartnerContactPersons', {
                                                'user': data.selectedPartnerContact,
                                                'partnerId': partnerId,
                                                'emailCheck': data.send_email,
                                                'emailChanged': data.username_changed
                                            }).then(function (response) {
                                                data.selectedPartnerContact = {};
                                                data.getPartnerContacts(data, partnerId);
                                                messageService.showSuccess('Contact is updated successfully.', 3000);
                                                data.username_changed = false;
                                                data.access_changed = false;
                                                data.send_email = false;
                                            });
                                        }
                                    } else {
                                        if (!data.selectedPartnerContact.isGreeted) {
                                            if ((isValidEmail(data.selectedPartnerContact.username))) {
                                                console.log("access changed, send welcome email", data.selectedPartnerContact)
                                                data.send_email = true;
                                                //set emailSender  true
                                                //data.selectedPartnerContact.isGreeted = 1;  
                                            }
                                        }
                                        ajaxCall.post('/Client/updatePartnerContactPersons', {
                                            'user': data.selectedPartnerContact,
                                            'partnerId': partnerId,
                                            'emailCheck': data.send_email,
                                            'emailChanged': data.username_changed
                                        }).then(function (response) {
                                            data.selectedPartnerContact = {};
                                            data.getPartnerContacts(data, partnerId);
                                            $scope.updateFeaturesForUser(response.data.userId, response.data.userType, data.partner.productType);
                                            messageService.showSuccess('Contact is updated successfully.', 3000);
                                            data.username_changed = false;
                                            data.access_changed = false;
                                            data.send_email = false;
                                        });
                                    }
                                } else {
                                    ajaxCall.post('/Client/updatePartnerContactPersons', {
                                        'user': data.selectedPartnerContact,
                                        'partnerId': partnerId,
                                        'emailCheck': data.send_email,
                                        'emailChanged': data.username_changed
                                    }).then(function (response) {
                                        data.selectedPartnerContact = {};
                                        data.getPartnerContacts(data, partnerId);
                                        $scope.updateFeaturesForUser(response.data.userId, response.data.userType, data.partner.productType);
                                        messageService.showSuccess('Contact is updated successfully.', 3000);
                                        data.username_changed = false;
                                        data.access_changed = false;
                                        data.send_email = false;
                                    });
                                }

                            }

                        },

                        removePartnerContact: function (contact, data, partnerId) {
                            ajaxCall.post('/Client/removeContact', {
                                'user': contact,
                                'mappingId': partnerId,
                                type: 'partner'
                            }).then(function (response) {
                                data.getPartnerContacts(data, partnerId);
                                messageService.showSuccess('Contact is removed successfully.', 3000);
                            });
                        },

                        updateMarketPlace: function (place, data) {
                            data.marketPlace = place.originalObject;
                            data.store.marketPlaceName = data.marketPlace.marketPlaceName;
                            data.store.marketPlaceId = data.marketPlace.marketPlaceId;
                            data.store.marketPlaceCity = data.marketPlace.city;
                            
                        },

                        updateBrandName: function (place, data) {
                            data.storeAdress = place.originalObject;
                            data.store.city = place.originalObject.city;
                            data.store.state = place.originalObject.state;
                            data.store.latitude = place.originalObject.latitude;
                            data.store.longitude = place.originalObject.longitude;
                            data.store.brandName = place.originalObject.brandName;
                            data.store.googleCategories = place.originalObject.googleCategories;
                            data.store.googlePlaceId = place.originalObject.googlePlaceId;
                            data.store.subLocality = place.originalObject.subLocality;
                            data.store.locality = place.originalObject.locality;
                            data.store.pinCode = place.originalObject.pinCode;

                            data.store.address = place.originalObject.vicinity;
                            if (data.marketPlace && data.marketPlace.hasOwnProperty('marketPlaceName'))
                                data.store.marketPlaceName = data.marketPlace.marketPlaceName;
                            if (data.marketPlace && data.marketPlace.hasOwnProperty('googlePlaceId'))
                                data.store.marketPlaceGoogleId = data.marketPlace.googlePlaceId;

                            var googleFields = ["address", "sublocalty", "locality", "city", "state", "pinCode"];
                            var l = googleFields.length;
                            for (var i = 0; i < l; i++) {
                                var inputElem = document.getElementById(googleFields[i]);
                                if (inputElem.value != "" && inputElem.value != null) {
                                    inputElem.style.backgroundColor = '#fff3e0';
                                }
                            }

                            data.updateSoreName(data.store);
                        },

                        updateCityName: function (place, data) {
                            data.store.city = place[0];
                            data.store.state = place[1];
                        },

                        getStoreContacts: function (data, nasid) {
                            ajaxCall.post('/Client/getStoreContactPersons', {
                                'nasid': nasid
                            }).then(function (response) {
                                data.storeContactList = response.data.data;
                                data.selectedSotreContact = {};
                            });
                        },

                        removeStoreContact: function (contact, data, nasid) {
                            ajaxCall.post('/Client/removeContact', {
                                'user': contact,
                                'mappingId': nasid,
                                type: 'location'
                            }).then(function (response) {
                                data.getStoreContacts(data, nasid);
                                messageService.showSuccess('Contact is removed successfully.', 3000);
                            });
                        },
                
                        updateStoreContactPersons: function (data, nasid) {
                            $('.form-group').removeClass('has-error');
                            if (!data.selectedSotreContact || data.selectedSotreContact.name == null || data.selectedSotreContact.name == '') {
                                var element = angular.element('[ng-model="data.selectedSotreContact.name"]');
                                element[0].parentNode.className += " has-error";
                                return;
                            }
                            //if (data.selectedSotreContact.username == null || data.selectedSotreContact.username == '' || !isValidEmail(data.selectedSotreContact.username)) {
                            //    var element = angular.element('[ng-model="data.selectedSotreContact.username"]');
                            //    element[0].parentNode.className += " has-error";
                            //    return;
                            //}

                            if (data.selectedSotreContact.username == null || data.selectedSotreContact.username == '') {
                                var element = angular.element('[ng-model="data.selectedSotreContact.username"]');
                                element[0].parentNode.className += " has-error";
                                return;
                            }

                            if (data.selectedSotreContact.mobile == null || data.selectedSotreContact.mobile == '' || !isValidPhoneNumber(data.selectedSotreContact.mobile)) {
                                var element = angular.element('[ng-model="data.selectedSotreContact.mobile"]');
                                element[0].parentNode.className += " has-error";
                                return;
                            }

                            if (data.selectedSotreContact.userId == -1 && (isValidEmail(data.selectedSotreContact.username))) {
                                service.checkIfContactExists(data.selectedSotreContact.username).then(function (result) {
                                    if (result == false) {
                                        //email condition
                                        if (data.selectedSotreContact.active == true) {
                                            if ((isValidEmail(data.selectedSotreContact.username))) {
                                                console.log("new user with access, send welcome email", data.selectedSotreContact);
                                                data.send_email = true;
                                                //set emailsender true
                                                //data.selectedSotreContact.isGreeted = 1;
                                            }

                                        }
                                        //email condition end

                                        ajaxCall.post('/Client/updateStoreContactPersons', {
                                            'user': data.selectedSotreContact,
                                            'nasid': nasid,
                                            'emailCheck': data.send_email,
                                            'emailChanged': data.username_changed
                                        }).then(function (response) {
                                            data.selectedSotreContact = {};
                                            //data.storeContactList.push(data.selectedSotreContact);
                                            data.getStoreContacts(data, nasid);
                                            $scope.updateFeaturesForUser(response.data.userId, response.data.userType, data.partner.productType);
                                            messageService.showSuccess('Contact is updated successfully.', 3000);
                                        });
                                    } else {
                                        messageService.showError('Contact already exists. Please go to user management to allocate more locations.', 3000);
                                        var element = angular.element('[ng-model="data.selectedSotreContact.username"]');
                                        element[0].parentNode.className += " has-error";
                                        return;
                                    }

                                })
                            } else {
                                if (data.selectedSotreContact.active == true) {
                                    if (!data.access_changed) {
                                        if (data.username_changed) {
                                            service.checkIfUsernameExist(data.selectedSotreContact.username).then(function (result) {
                                                if (result == 0 || result == data.selectedSotreContact.userId) {
                                                    if ((isValidEmail(data.selectedSotreContact.username))) {
                                                        console.log("email changed, send welcome email", data.selectedSotreContact)
                                                        data.send_email = true;
                                                    }
                                                    //data.selectedSotreContact.isGreeted = 1;
                                                    ajaxCall.post('/Client/updateStoreContactPersons', {
                                                        'user': data.selectedSotreContact,
                                                        'nasid': nasid,
                                                        'emailCheck': data.send_email,
                                                        'emailChanged': data.username_changed
                                                    }).then(function (response) {
                                                        data.selectedSotreContact = {};
                                                        //data.storeContactList.push(data.selectedSotreContact);
                                                        data.getStoreContacts(data, nasid);
                                                        messageService.showSuccess('Contact is updated successfully.', 3000);
                                                        data.username_changed = false;
                                                        data.access_changed = false;
                                                        data.send_email = false;
                                                    });
                                                } else {
                                                    messageService.showError('Contact already exists. Please go to user management to allocate more locations.', 3000);
                                                    var element = angular.element('[ng-model="data.selectedSotreContact.username"]');
                                                    element[0].parentNode.className += " has-error";
                                                    return;
                                                }
                                            }); //
                                        } else {
                                            ajaxCall.post('/Client/updateStoreContactPersons', {
                                                'user': data.selectedSotreContact,
                                                'nasid': nasid,
                                                'emailCheck': data.send_email,
                                                'emailChanged': data.username_changed
                                            }).then(function (response) {
                                                data.selectedSotreContact = {};
                                                //data.storeContactList.push(data.selectedSotreContact);
                                                data.getStoreContacts(data, nasid);
                                                messageService.showSuccess('Contact is updated successfully.', 3000);
                                                data.username_changed = false;
                                                data.access_changed = false;
                                                data.send_email = false;
                                            });
                                        }
                                    } else {
                                        if (!data.selectedSotreContact.isGreeted) {
                                            if ((isValidEmail(data.selectedSotreContact.username))) {
                                                console.log("access changed, send welcome email", data.selectedSotreContact)
                                                data.send_email = true;
                                                //set emailSender  true
                                                //data.selectedSotreContact.isGreeted = 1;
                                            }
                                        }
                                        ajaxCall.post('/Client/updateStoreContactPersons', {
                                            'user': data.selectedSotreContact,
                                            'nasid': nasid,
                                            'emailCheck': data.send_email,
                                            'emailChanged': data.username_changed
                                        }).then(function (response) {
                                            data.selectedSotreContact = {};
                                            //data.storeContactList.push(data.selectedSotreContact);
                                            data.getStoreContacts(data, nasid);
                                            $scope.updateFeaturesForUser(response.data.userId, response.data.userType, data.partner.productType);
                                            messageService.showSuccess('Contact is updated successfully.', 3000);
                                            data.username_changed = false;
                                            data.access_changed = false;
                                            data.send_email = false;
                                        });
                                    }
                                } else {
                                    ajaxCall.post('/Client/updateStoreContactPersons', {
                                        'user': data.selectedSotreContact,
                                        'nasid': nasid,
                                        'emailCheck': data.send_email,
                                        'emailChanged': data.username_changed
                                    }).then(function (response) {
                                        data.selectedSotreContact = {};
                                        //data.storeContactList.push(data.selectedSotreContact);
                                        data.getStoreContacts(data, nasid);
                                        $scope.updateFeaturesForUser(response.data.userId, response.data.userType, data.partner.productType);
                                        messageService.showSuccess('Contact is updated successfully.', 3000);
                                        data.username_changed = false;
                                        data.access_changed = false;
                                        data.send_email = false;
                                    });
                                }
                            }
                        },

                        getNewContactObj: function () {
                            var contactobject = angular.copy(contactDetails);
                            return contactobject;
                        },

                        updateSoreNameOnBrandChange: function (store, $event) {
                            setTimeout(function () {
                                if ($event.target.value && store.mktPlaceAlias) {
                                    store.shopDpName = $event.target.value + ', ' + store.mktPlaceAlias;
                                } else if ($event.target.value && store.marketPlaceName) {
                                    store.shopDpName = $event.target.value + ', ' + store.marketPlaceName;
                                }
                            }, 500);
                        },

                        updateSoreName: function (store) {
                            if (store.brandName && store.mktPlaceAlias) {
                                store.shopDpName = store.brandName + ', ' + store.mktPlaceAlias;
                            } else if (store.brandName && store.marketPlaceName) {
                                store.shopDpName = store.brandName + ', ' + store.marketPlaceName;
                            }
                        },

                        saveStoreDetails: function (data) {
                            function valid(s, ss) {
                                $('.form-group').removeClass('has-error');
                                var result = true;
                                req_s = [
                                    "address", "city", "state", "category", "routerState",
                                    "mode", "salesId", "deviceType", "partner"
                                ];
                                req_ss = [];
                                var l = req_s.length;
                                for (var i = 0; i < l; i++) {
                                    if (s[req_s[i]] == null || s[req_s[i]] == "") {
                                        document.getElementById(req_s[i]).parentNode.className += " has-error";
                                        result = false;
                                    }
                                }
                                return result;
                            }
                            if (valid(store)) {
                                var market = document.getElementById('ex1_value').value;
                                if (market == '') {
                                    messageService.showError('Please update the market place name');
                                    return;
                                } else {
                                    store.marketPlaceName = market;
                                }

                                var brand = document.getElementById('ex2_value').value;
                                if (brand == '') {
                                    messageService.showError('Please update the brand name');
                                    return;
                                } else {
                                    store.brandName = brand
                                }

                                if (!data.marketPlace) {
                                    data.marketPlace = {};
                                    data.marketPlace.marketPlaceName = store.marketPlaceName;
                                    data.marketPlace.marketPlaceId = store.marketPlaceId;
                                    data.marketPlace.city = store.marketPlaceCity;
                                    data.marketPlace.googlePlaceId = store.marketPlaceGoogleId;
                                } else {
                                    data.marketPlace.marketPlaceName = store.marketPlaceName;
                                }

                                if (data.marketPlace.marketPlaceName != null && data.marketPlace.marketPlaceName != '' &&
                                    data.marketPlace.city != null && data.marketPlace.city != '') {
                                    ajaxCall.post('/Client/updateMarketPlace', {
                                        'marketplace': data.marketPlace
                                    }).then(function (response) {
                                        if (response.data > 0 || response.data == null) {
                                            store.marketPlaceId = response.data;
                                            store.zohoAccountId = data.partner.zohoAccountId;
                                            ajaxCall.post('/Client/saveStoreDetails?nasid=' + store.nasid, store).then(function (response) {
                                                if (store.salesId != '' && store.salesId != null && store.salesId != 'N/A') {
                                                    ajaxCall.post('/Client/addMappingForUser', {
                                                        'username': store.salesId,
                                                        'mappedId': store.nasid,
                                                        'type': 'location'
                                                    }).then(function (response) {
                                                        if (response.data) {
                                                            //console.log('Location with nasid - ' + store.nasid + ' is assisgned to : ' + store.salesId);
                                                        }
                                                    });
                                                }
                                                
                                                data.tab = 'store_contacts';
                                                data.tab_head = 'Store Contacts';
                                                data.getStoreContacts(data, store.nasid);

                                                $scope.filterRouters();
                                            });
                                        } else {
                                            messageService.showError('Something went wrong while updating market place!');
                                        }

                                    });
                                } else {
                                    messageService.showError('Market place city and name can not be empty!');
                                }
                            } else {
                                messageService.showError("Form Fields Missing/Blank. Start Again.");
                                dontCloseThisDialog();
                            }

                           
                        }

                    },
                    size: 'lg'
                });
            } else {
                messageService.showError('Something went wron while fetching data of this location. Please try to refresh page and start again');
            }
        })
    }
}]);

app.controller('settingsController', ['$window', '$rootScope', '$scope', '$state', '$modal', '$filter', '$compile', 'ajaxCall', 'servicesAjaxCall', 'service', 'messageService', 'fileUploadService', '$modal', function ($window, $rootScope, $scope, $state, $modal, $filter, $compile, ajaxCall, servicesAjaxCall, service, messageService, fileUploadService, $modal) {
    $scope.data = {};
    $scope.group = {};
    $scope.group.values = [];
    $scope.storeGroups = [];
    $scope.alert = {};
    $scope.templates = [];
    $scope.questions = [];
    $scope.template = {
        selected: null
    }

    $scope.bandwidths = [{ value: "", name: "Not Set" }, { value: "100", name: "100 Kbps" }, { value: "200", name: "200 Kbps" }, { value: "500", name: "500 Kbps" }, { value: "1000", name: "1 Mbps" }, { value: "2000", name: "2 Mbps" }, { value: "4000", name: "4 Mbps" }, { value: "8000", name: "8 Mbps" }, { value: "10000", name: "10 Mbps" }, { value: "15000", name: "15 Mbps" }, { value: "20000", name: "20 Mbps" }, { value: "30000", name: "30 Mbps" }, { value: "50000", name: "50 Mbps" }];
    $scope.dataLimits = [{ value: "", name: "Not Set" }, { value: "50", name: "50 MB" }, { value: "100", name: "100 MB" }, { value: "200", name: "200 MB" }, { value: "500", name: "500 MB" }, { value: "1024", name: "1 GB" }, { value: "2048", name: "2 GB" }, { value: "4096", name: "4 GB" }, { value: "5120", name: "5 GB" }, { value: "8192", name: "8 GB" }, { value: "10240", name: "10 GB" }, { value: "20480", name: "20 GB" }, { value: "30720", name: "30 GB" }, { value: "40960", name: "40 GB" }, { value: "51200", name: "50 GB" }];
    $scope.timeObjects = [
        { value: "600", name: "10 Minutes" }, { value: "900", name: "15 Minutes" },
        { value: "1200", name: "20 Minutes" },{ value: "1800", name: "Half an hour" },
        { value: "3600", name: "1 hour" },{ value: "7200", name: "2 hours" },
        { value: "10800", name: "3 hours" },{ value: "14400", name: "4 hours" },
        { value: "21600", name: "6 hours" },{ value: "43200", name: "12 hours" },
        { value: "86400", name: "24 hours" },{ value: "259200", name: "3 Days" },
        { value: "604800", name: "1 Week" },{ value: "2419200", name: "4 Weeks" }
    ];

    $scope.timeObject = { selected: $scope.timeObjects[0] };
    
    $scope.intervals = [];
    for (var i = 0; i < 24; ++i) {
        $scope.intervals.push({ name: i + ":" + "00", value: (i * 60).toString() });
        $scope.intervals.push({ name: i + ":" + "15", value: (i * 60 + 15).toString() });
        $scope.intervals.push({ name: i + ":" + "30", value: (i * 60 + 30).toString() });
        $scope.intervals.push({ name: i + ":" + "45", value: (i * 60 + 45).toString() });
    }
    
    /******************** This need to change after changing nases array in combined setting fetch call *********************/

    //if ($scope.$parent.positiveNases.length == 0) {
    //    $rootScope.loadLocationsForSelect();
    //    if ($scope.$parent.positiveNases.length > 0 && $scope.$parent.positiveNases[0].storeName != _selectAllObj.storeName)
    //        $scope.$parent.positiveNases.insert(0, _selectAllObj);
    //    else
    //        $scope.$parent.positiveNases.push(_selectAllObj);
    //} else {
    //    if ($scope.$parent.positiveNases.length > 0 && $scope.$parent.positiveNases[0].storeName != _selectAllObj.storeName)
    //        $scope.$parent.positiveNases.insert(0, _selectAllObj);
    //    else
    //        $scope.$parent.positiveNases.push(_selectAllObj);
    //}

    $scope.data = $scope.data || {};
    $scope.singleLocationSelectorConfig = getSingleLocationSelectorConfig();
    //$scope.multipleLocationSelectorConfig = getMultipleLocationSelectorConfig();
    $scope.multipleLocationSelectorConfig = getPaginatedMultiStoreSelectorConfig();
    /* Above line of code will be used for paginated nas selection later */

    $scope.checkForSelectAll = function ($index) {
        //console.log($scope.data.settings[$index].nases[$scope.data.settings[$index].nases.length - 1]);
        if ($scope.data.settings[$index].nases[$scope.data.settings[$index].nases.length - 1] == _selectAllNas) {
            var nasLocations = [];
            var nasIds = [];
            angular.forEach($scope.$parent.routerDetails, function (location, index) {
                if (nasIds.indexOf(location.nasid) == -1 && location.nasid != _selectAllNas) {
                    nasLocations.push(location.nasid);
                    nasIds.push(location.nasid);
                }
            });
            $scope.data.settings[$index].nases = nasLocations;
        }
    }

    $scope.convertDataFormate = function (value) {
        var result = '';
        if (value) {
            result = "Not Set";
        } else {
            if (value >= 10240) {
                result = roundToTwo(value / 1024) + " GB";
            } else if (value >= 1024) {
                result = roundToTwo(value / 1024) + " GB";
            } else if (value >= 50) {
                result = roundToTwo(value) + " MB";
            } else {
                result = roundToTwo(value) + " MB";
            }
        }
        return result;
    };

    //$scope.data.loadMoreSettings = true;
    //$scope.data.settingsPageNumber = 0;
    //$scope.data.settingsPageSizeSmsPageSize = 5;
    //
    //$scope.loadMoreSettings = function () {
    //    $scope.data.settingsPageNumber++;
    //    $scope.data.loadMoreSettings && service.GetCombinedSetting({
    //        pageNumber: $scope.data.settingsPageNumber,
    //        pageSize: $scope.data.settingsPageSizeSmsPageSize
    //    }).then(function (settings) {
    //        $scope.data.settings = $scope.data.settings || [];
    //        if (settings.length < $scope.data.settingsPageSizeSmsPageSize) $scope.data.loadMoreSettings = false;
    //        $scope.data.settings = $scope.data.settings.concat(settings);
    //    });
    //}

    var closeAllSettings = function () {
        var openedSettings = $('.settings-dropdown[aria-expanded]');
        angular.forEach($scope.data.settings, function (setting) {
            setting.fetchConfigs = true;
            $('#defaultSettings' + setting.settingId).removeClass('in');
        });
    }

    $scope.createNewSetting = function () {
        closeAllSettings();
        var newsetting = {};
        newsetting.name = $scope.newSettingName;
        ajaxCall.post('/Client/createNewSetting', newsetting).then(function (response) {
            newsetting.settingId = response.data;
            $scope.data.settings.splice(0, 0, newsetting);
        });
    }

    $scope.searchNasSettings = function () {
        var query = {
            nasid: $scope.data.location.length > 0 ? $scope.data.location[0] : 0
        };
        closeAllSettings();
        $scope.data.loadMoreSettings = false;
        query.nasid && ajaxCall.post('/Client/GetNasUser', query).then(function (response) {
            $scope.nasAdmins = response.data;
        });

        query.nasid && servicesAjaxCall.get('/v1/Setting/SearchSettings?nasid=' + query.nasid).then(function (response) {
            $scope.data.settings = response.data;
        });
    }

    $scope.searchSettingsByName = function ($event) {
        if ($event.which == 13) {
            var query = {
                nasid: 0,
                setting: $scope.data.settingSearchString
            };
            closeAllSettings();
            $scope.data.loadMoreSettings = false;
            $scope.nasAdmins = {};
            query.setting && servicesAjaxCall.get('/v1/Setting/SearchSettings?term=' + query.setting).then(function (response) {
                $scope.data.settings = response.data;

            });
        }
    }

    var getVersions = function (setting, type, callback) {
        ajaxCall.get('/Client/GetVersions?settingId=' + setting.settingId + '&type=' + type).then(function (response) {
            setting.versions = setting.versions || {};
            setting.versions[type] = response.data;
            angular.forEach(setting.versions, function (version) {
                version.value = moment.utc(version.value).local().format(Constants.competeDateFormat);
            });
            callback();
        });
    }

    $scope.getVersion = function (setting, version, index, type) {
        switch (type) {
            case 'basic':
                setting.selectedGroup = setting.selectedGroup || { groupId: 0 };
                ajaxCall.get('/Client/GetUserGroupBasicConfiguration?settingId='
                    + setting.settingId + '&groupId=' + setting.selectedGroup.groupId + '&version=' + index).then(function (response) {
                        response.data = response.data || {};
                        setting.userGroupBasicConfiguration = response.data.basicConfigs;
                        setting.userGroupBasicConfiguration.version = version;
                    });
                break;
            case 'advance':
                ajaxCall.get('/Client/GetAdvanceConfiguration?settingId=' + setting.settingId + '&version=' + index).then(function (response) {
                    setting.advanceConfiguration = response.data;
                    setting.advanceConfiguration.version = version;
                    if (setting.advanceConfiguration.HIDE_QUESTION.parameters[0] == "") setting.advanceConfiguration.HIDE_QUESTION.parameters[0] = "0";

                    initializeUpload('#uploaderZone' + setting.settingId, '#fileList' + setting.settingId, '#splashImage' + setting.settingId, setting, function (url) {
                        if (setting.advanceConfiguration.ROUTER_SPLASH_IMAGE.parameters)
                            setting.advanceConfiguration.ROUTER_SPLASH_IMAGE.parameters[0] = url;
                        else
                            setting.advanceConfiguration.ROUTER_SPLASH_IMAGE.parameters = [url];
                    });
                    initializeUpload('#uploaderZone28' + setting.settingId, '#fileList28' + setting.settingId, '#splashImage28' + setting.settingId, setting, function (url) {
                        if (setting.advanceConfiguration.LOGIN_PAGE_IMAGE.parameters)
                            setting.advanceConfiguration.LOGIN_PAGE_IMAGE.parameters[0] = url;
                        else
                            setting.advanceConfiguration.LOGIN_PAGE_IMAGE.parameters = [url];
                    });
                });
                break;
        }
    }

    $scope.toggleHistory = function ($event, setting, type) {
        $($event.target).parent().find(".history-toggler").toggleClass('display-none');
        if ($($event.target).data('history')) {
            $scope.getVersion(setting, null, 0, type);
            $($event.target).data('history', false);
        } else {
            getVersions(setting, type, function () {
                $($event.target).data('history', true);
                $('.history-toggler.previous-version')[0].click();
            });
        }
        
    }

    $scope.fetchBasicConfiguration = function (setting) {
        $scope.getVersion(setting, null, 0, 'basic');
    }

    $scope.fetchAllConfiguration = function (setting) {
        if (typeof setting.fetchConfigs == "undefined") setting.fetchConfigs = true;

        if (setting.fetchConfigs) {
            $('.nav-tabs a:first').tab('show');
            $scope.fetchUserGroups(setting);
            $scope.fetchBasicConfiguration(setting);
            $scope.fetchAdvanceConfiguration(setting);
            setting.fetchConfigs = false;
        } else {
            setting.fetchConfigs = true;
            setting.selectedGroup = { groupId: 0 };
        } 
    }
    
    $scope.fetchUserGroups = function (setting) {
        ajaxCall.get('/Client/GetUserGroups?settingId=' + setting.settingId).then(function (response) {
            setting.userGroups = response.data;
            setting.selectedGroup = response.data[0];
            angular.forEach(setting.userGroups, function (group) {
                group.values = createObjects(group.values, 'mobile', 'name');
            });
        });
    }

    $scope.fetchAdvanceConfiguration = function (setting) {
        $scope.getVersion(setting, null, 0, 'advance');
    }

    $scope.selectInstance;
    $scope.multipleTemplateSelectorConfig = {
        valueField: 'id',
        labelField: 'templateName',
        searchField: ['id', 'templateName'],
        plugins: ['remove_button'],
        delimiter: '|',
        placeholder: 'Select or search from the list',
        sortField: 'templateName',
        onInitialize: function(selectsize){
            $scope.selectInstance = selectsize;
        },
        render: {
            item: function (item) {
                var ret = '<div class="item" title="' + item.templateName + '">';
                ret += '<span class="storeName">' + item.templateName + '</span>';
                ret += '</div>';
                return ret;
            },
            option: function (item, escape) {
                var label = escape(item.templateName);
                var caption = '<span>Template Name: ' + escape(item.templateName) + '</span>';

                return '<div title="' + item.templateName + '">' +
                    '<div class="primary">' + label + '</div>' +
                '</div>';
            }
            
        },
        maxItems: 10
    };

    $scope.fetchTemplates = function (setting) {
        ajaxCall.get('/Client/GetAllTemplates').then(function (response) {
            $scope.templates = response.data;
            $scope.templateDetail = {};
        });
    }

    $scope.onDrop = function (target, source) {
        var sourceIndex = getObjectIndexFromArray($scope.questions, 'id', source.id);
        $scope.questions.splice(sourceIndex, 1);
        var targetIndex = getObjectIndexFromArray($scope.questions, 'id', target.id);
        $scope.questions.splice(targetIndex + 1, 0, source);

        source.moved = true;
        setTimeout(function () {
            source.moved = false;
        }, 1000);
    };

    $scope.dropValidate = function (target, source) {
        return target !== source;
    };


    var makeSequence = function (questions, nextQuestionId) {
        var thisQuestion = getObjectFromArray(questions, 'nextQuestionId', nextQuestionId);

        if (thisQuestion) {
            $scope.questions = $scope.questions || [];
            $scope.questions.unshift(thisQuestion);
            makeSequence(questions, thisQuestion.id);
        } else if (questions.length != $scope.questions.length) {
            $scope.questions = questions;
        }
    };

    $scope.fetchTemplateDetails = function () {
        ajaxCall.get('/Client/fetchTemplateDetails?templateId=' + $scope.template.selected).then(function (response) {
            $('.question-box .in[aria-expanded]').prev().click();
            $scope.templateDetail = response.data.template;
            $scope.questions = [];
            var  questions = response.data.qlist;
            var sequence = [];
            var dependentQuestions = [];
            
            if (response.data.template.questionSequence) {
                angular.forEach(response.data.template.questionSequence, function (seq, key) {
                    var templateId = key.substring(0, key.indexOf('-'));
                    var questionId = key.substring(key.indexOf('-') + 1);
                    var ques = getObjectFromArray(questions, 'id', questionId);

                    if (ques) {
                        ques.quesType = "" + ques.quesType;
                    }

                    ques && (ques.nextQuestionId = seq.nq);

                    if (ques && questionId == ques.id && ques.options) {

                        angular.forEach(seq.ds, function (b, a) {
                            var option = getObjectFromArray(ques.options, 'id', a);
                            if (option) {
                                if (!b || b == seq.nq) {
                                    option.nextQuestionId = 0;
                                }
                                else {
                                    option.nextQuestionId = b;
                                    option.nextQuestion = getObjectFromArray(questions, 'id', b);
                                    if (dependentQuestions.indexOf(option.nextQuestionId) < 0)
                                        dependentQuestions.push(option.nextQuestionId);
                                }
                            }
                        });
                    }
                });

                angular.forEach(dependentQuestions, function (quesId) {
                    questions.splice(getObjectIndexFromArray(questions, 'id', quesId), 1);
                });
                makeSequence(questions, 0);
            } else {
                $scope.questions = questions;
            }

        });
    }

    $scope.saveTemplate = function (setting, isNew) {
        var template = {
            templateName: $scope.templateDetail.templateName,
            templatePath: $scope.templateDetail.templatePath,
            templateContent: $scope.templateDetail.templateContent,
            id: $scope.templateDetail.id
        }

        if (isNew) template.id = 0;

        ajaxCall.post('/Client/SaveTemplate?settingId=' + setting.settingId, template).then(function (response) {
            $scope.fetchTemplates(setting);
            $scope.template.selected = response.data.templateId;

            if (response.status == 0) messageService.showSuccess(response.msg);
            else messageService.showError(response.msg);
        });
    }

    $scope.updateQuestions = function (setting) {
        updateQuestionSequence(setting);
    }

    var updateTemplateQuestions = function (settingId) {
        var ques = [];
        angular.forEach($scope.questions, function (question) {
            ques.push(question);
            angular.forEach(question.options, function (option) {
                if (option.nextQuestion && option.nextQuestion.id != question.nextQuestionId) {
                    ques.push(option.nextQuestion);
                }
            });
        });


        if (ques.length > 0) {
            var firstQuestionId = $scope.questions[0].id;
            var lastQuestionId = $scope.questions[$scope.questions.length - 1].id;
            ajaxCall.post('/Client/UpdateTemplateQuestions?settingId=' + settingId +
                '&templateId=' + $scope.template.selected + '&firstQuestionId=' + firstQuestionId + '&lastQuestionId=' + lastQuestionId, ques)
                .then(function (response) {
                    if (response.status == 0) messageService.showSuccess(response.msg);
                    else messageService.showError(response.msg);
            });
        }
    }



    $scope.answerTypes = [
        { value: 0, type: 'Text' },
        { value: 1, type: 'Multiple Option' },
        { value: 2, type: 'Checkbox Multi Select' },
        { value: 3, type: 'Rating' },
        { value: 4, type: 'Multiple line Text' },
        { value: 5, type: 'Text with seperate label' }
    ];

    
    var updateQuestionSequence = function (setting) {
        var nextQuestion = {};
        var thisQuestion = {};

        for (var i = $scope.questions.length - 2; i >= 0; i--) {
            nextQuestion = $scope.questions[i + 1];
            thisQuestion = $scope.questions[i];
            thisQuestion.nextQuestionId = nextQuestion.id;
            angular.forEach(thisQuestion.options, function (option) {
                if (!option.nextQuestionId) {
                    option.nextQuestionId = nextQuestion.id;
                }

                if (option.nextQuestion) {
                    option.nextQuestion.nextQuestionId = nextQuestion.id;
                }
            });
        }
        $scope.questions[$scope.questions.length - 1].nextQuestionId = 0;

        updateTemplateQuestions(setting.settingId);
    }

    

    $scope.updateQuestion = function ($event, question, setting) {
        var hasDependentQuestions = false;
        if (question.options) {
            angular.forEach(question.options, function (option) {
                if (option.nextQuestion) {
                    hasDependentQuestions = true;
                }
            });
        }

        if (hasDependentQuestions) {
            question.answerType = 1;
            alert ("Please delete dependent question before updating question")
        } else {
            updateQuestionSequence(setting);
        }
    }

    $scope.makeGlobalQuestion = function (setting, ques) {
        ques.templateId = 0;
        ajaxCall.post('/Client/UpdateQuestion?templateId=' +
            $scope.template.selected, { question: ques }).then(function (response) {
                messageService.showSuccess('Question converted to global');
                updateQuestionSequence(setting);
            });
    }

    $scope.addTemplateQuestion = function (options, index) {
        options = options || {};
        var ques = {
            id: 0,
            quesText: options.text || 'Question Text',
            hidden: false,
            quesType: "0",
            answerType: 0
        };
        ajaxCall.post('/Client/AddTemplateQuestion?templateId=' +
            $scope.template.selected, { question: ques }).then(function (response) {
                ques.id = response.data;
                ques.templateId = $scope.template.selected;
                messageService.showSuccess('Question added');
                if (!options.dependent) {
                    if (typeof (index) != 'undefined') {
                        $scope.questions.splice(index + 1, 0, ques);
                    } else $scope.questions.push(ques);
                    updateQuestionSequence(options);
                } 
                
                if (options.callback) options.callback(ques);
            });
    }

    $scope.deleteTemplateQuestion = function (setting, ques, index) {
        ques.hidden = true;
        ajaxCall.post('/Client/UpdateQuestion?templateId=' +
            $scope.template.selected, { question: ques }).then(function (response) {
                messageService.showSuccess('Question deleted');
                (typeof index != 'undefined') && $scope.questions.splice(index, 1);
                updateQuestionSequence(setting);
            });
    }

    $scope.addDependentQuestion = function (setting, question, option) {
         $scope.addTemplateQuestion({
             settingId: setting.settingId,
             dependent: true,
             callback: function (ques) {
                option.nextQuestion = ques;
                option.nextQuestionId = ques.id;
                updateQuestionSequence(setting);
            }});
    }

    $scope.pullDependentQuestion = function ($event, setting, question, option) {
        if ($event.which == 13) {
            var nextQuestionId = $($event.currentTarget).val();
            var ques = getObjectFromArray($scope.questions, 'id', nextQuestionId);
            if (ques) {
                var makeDependent = true;
                angular.forEach(ques.options, function (option) {
                    if (option.nextQuestion) {
                        makeDependent = false;
                    }
                });

                if (makeDependent) {
                    showConfirmationDialog($modal, {
                            title: 'Are you sure?',
                            textContent: 'This question will now be asked if user selects ' + option.text + ' as answer',
                            type: 'alert-warning',
                            ok: 'proceed'
                        },
                        function () {
                            option.nextQuestion = ques;
                            option.nextQuestionId = nextQuestionId;
                            var index = getObjectIndexFromArray($scope.questions, 'id', nextQuestionId);
                            $scope.questions.splice(index, 1);
                            updateQuestionSequence(setting);
                        }
                    );
                } else {
                    showConfirmationDialog($modal, {
                        title: 'Question has dependent questions',
                        textContent: 'Question has dependent questions, hence cannot be moved to dependent questions',
                        type: 'alert-warning'
                    });
                }
            } else {
                showConfirmationDialog($modal, {
                    title: 'Question does not exists',
                    textContent: 'Question does not exists, choose other qquestion',
                    type: 'alert-info'
                });
            }
        }
    }

    $scope.deleteOption = function (question, option, setting, index) {
        if (option.nextQuestion) {
            alert("Delete dependent question before deleting option");
        } else {
            question.options.splice(index, 1);
            updateQuestionSequence(setting);
        }
    }

    $scope.addOption = function (setting, question) {
        question.options = question.options || [];
        question.options.push({ id: 0, text: '' });
        ajaxCall.post('/Client/UpdateQuestionOptions?templateId=' + $scope.template.selected, { question: question }).then(function (response) {

            if (response.status == 0) {
                messageService.showSuccess(response.msg);
                angular.forEach(response.data, function (option) {
                    var obj = getObjectFromArray(question.options, 'id', option.id);
                    option = angular.extend(option, obj);
                });
                question.options = response.data;
                updateQuestionSequence(setting);
            }
               
            else
                messageService.showError(response.msg);
               
       });
    }

    $scope.saveHealthAlerts = function (setting) {
        if (!$scope.alert.hasOwnProperty('alertType') || $scope.alert.alertType == null) {
            messageService.showError('Please select alert type');
            return;
        } else if (!$scope.alert.hasOwnProperty('notificationType') || $scope.alert.notificationType == null) {
            messageService.showError('Please select notification type');
            return;
        } else if (!$scope.alert.hasOwnProperty('delay') || $scope.alert.delay == null) {
            messageService.showError('Please select delay');
            return;
        } else if (!$scope.alert.hasOwnProperty('sendEmailTo') || $scope.alert.sendEmailTo == null || $scope.alert.sendEmailTo == '') {
            messageService.showError('Please enter email id');
            return;
        } else {
            $scope.alert.delay = parseInt($scope.alert.delay) / 30;
            var alerts = [];
            alerts.push($scope.alert);
                return ajaxCall.post('/Client/saveHealthAlerts?settingId=' + setting.settingId, alerts).then(function (response) {
                messageService.showSuccess('Saved Successfully');
                $scope.alert = {};
                $scope.fetchHealthAlerts(setting);
            });
        }
    }

    $scope.fetchHealthAlerts = function (setting) {
        return ajaxCall.get('/Client/GetHealthAlerts?settingId=' + setting.settingId).then(function (response) {
            setting.healthAlerts = (response.data);
            angular.forEach(setting.healthAlerts, function(alert) {
                alert.sendEmailTo = alert.sendEmailTo.split(',');
            });
        });
    }

    $scope.deleteHealthAlert = function (setting, healthAlertId) {
        return ajaxCall.get('/Client/DeleteHealthAlert?settingId=' + setting.settingId + '&healthAlertId=' + healthAlertId).then(function (response) {
            messageService.showSuccess('Deleted Successfully');
            $scope.fetchHealthAlerts(setting);
        });
    }

    $scope.editHealthAlert = function (rowAlert) {
        $(".settings-panel-body .active")[0].scrollIntoView();
        rowAlert.sendEmailTo = rowAlert.sendEmailTo.join(',');
        $scope.alert = rowAlert;
    }

    var createMobileTextFromObject = function (values) {
        var value = '';
        values = values ? values : [];
        if (typeof (values) == 'object') {
            for (var j = 0; j < values.length; ++j) {
                value += values[j].mobile;
                if (values[j].name)
                    value += ';' + values[j].name;
                if (j != values.length - 1)
                    value += ',';
            }
            return value;
        }
    }

    $scope.updateGroup = function (setting, group, options) {
        var options = options || {};
        var data = {};

        data.groupName = group.groupName;
        data.groupId = group.groupId;

        if (!options.delete)
            data.values = createMobileTextFromObject(group.values);

        ajaxCall.post('/Client/SaveGroup', {
            group: data,
            settingId: setting.settingId
        }).then(function () {
            messageService.showSuccess('Group updated successfully');
            window.location.reload();
        });
    }
    
    $scope.createNewGroup = function (setting) {
        var data = {};

        if (!$scope.group.groupName) {
            messageService.showError('Group Name can not be empty');
            return;
        }

        if ($scope.group.values && $scope.group.values[0].mobile == "") {
            messageService.showError('Please add at least one member to this group');
            return;
        }

        data.groupName = $scope.group.groupName;
        data.values = createMobileTextFromObject($scope.group.values);

        ajaxCall.post('/Client/SaveGroup', {
            group: data,
            settingId: setting.settingId
        }).then(function () {
            messageService.showSuccess('Group created successfully');
            $scope.fetchUserGroups(setting);
        });       
    }

    $scope.translateDataValue = function (value) {
        var result = '';
        if (!value) {
            result = "Not Set";
        } else {
            if (value >= 1024 * 1024) {
               result = roundToTwo(value / (1024 * 1024)) + " GB";
            } else if (value > 1024) {
                result = roundToTwo(value / 1024) + " MB";
            }  else {
                result = roundToTwo(value) + " KB";
            }
        }
            
        return result;
    }

    $scope.translateSpeedValue = function (value) {
        //value in kbps

        var result = '';
        if (!value) {
            result = "Not Set";
        } else {
            if (value > 1000000) {
                result = roundToTwo(value / 1000000) + " Gbps";
            } else if (value > 1000) {
                result = roundToTwo(value / 1000) + " Mbps";
            } else {
                result = roundToTwo(value) + " Kbps";
            }
        }
        return result;
    }
    
    $scope.saveBasicConfig = function (setting) {
        var obj = {};
        obj.settingId = setting.settingId;
        obj.userGroups = [];
        obj.userGroups.push({ basicConfigs: setting.userGroupBasicConfiguration, groupId: setting.selectedGroup.groupId });
        delete obj.userGroups[0].basicConfigs.version;
        servicesAjaxCall.post('/v1/setting/SetUserGroupBasicConfiguration', obj).then(function (response) {
            messageService.showSuccess(response.msg);
        });
    }

    $scope.saveAdvanceConfig = function (setting) {
        var advConfigObj = {};
        advConfigObj.settingId = setting.settingId;
        advConfigObj.advanceConfigs = setting.advanceConfiguration;

        
        delete advConfigObj.advanceConfigs.version;
        servicesAjaxCall.post('/v1/setting/SetAdvanceConfiguration', advConfigObj).then(function (response) {
            //$modalInstance.dismiss('cancel');
            messageService.showSuccess('Settings saved successfully');
        });
    }

    $scope.saveCustomisation = function (setting) {
        $scope.saveBasicConfig(setting);
        $scope.saveAdvanceConfig(setting);
    }

    $scope.deleteSettings = function ($index) {
        var settingId = $scope.data.settings[$index].settingId;
        var obj = {};
        obj.settingId = settingId;
        ajaxCall.post('/Client/DeleteSettings', obj).then(function (response) {
            messageService.showSuccess(response.msg);
            $scope.data.settings.splice($index, 1);
        });
    }

    $scope.applySettingsToLocations = function ($index, force) {
        var locations = $scope.data.settings[$index].nases;
        var settingId = $scope.data.settings[$index].settingId;
        var obj = {};
        obj.settingId = settingId;
        obj.nases = locations.filter(function (item, index) {
            return locations.indexOf(item) >= index;
        });
        obj.force = force ? true: false;
        ajaxCall.post('/Client/UpdateLocations', obj).then(function (response) {
            messageService.showSuccess("Setting applied to locations successfully");
        });

    }

    $scope.gotoSettingsPage = function ($index) {
        var locations = $scope.data.settings[$index].nases;
        var settingId = $scope.data.settings[$index].settingId;
        ajaxCall.post('/Client/GoToAdminServicesSettingsPage', {}).then(function (response) {
            var url = response.data + "&settingId=" + settingId + "&nasid=" + locations[0]
            window.open(url, "_blank")
        });
    }


    $scope.transferSettingToggle = function ($index) {
        var settingId = $scope.data.settings[$index].settingId;
        $('.setting-menu-' + $index + ' .transfer-input').toggleClass('display-none');

        setTimeout(function () {
            $('.setting-menu-' + $index + ' .transfer-input').toggleClass('display-none');
            $('.setting-menu-' + $index + ' .transfer-input input').val('');
        }, 30000);
    }

    $scope.transferSetting = function ($event, $index) {
        if ($event.which == 13) {
            var obj = {};
            obj.settingId =  $scope.data.settings[$index].settingId;;
            obj.username = $scope.data.transferTo;
            ajaxCall.post('/Client/TransferSetting', obj).then(function (response) {
                messageService.showSuccess(response.msg);
                $scope.data.settings.splice($index, 1);
            });
        }
    }


    var initializeUpload = function (containerId, resultContainer, placeHolderImage, setting, callback) {
        fileUploadService.initializeUpload(containerId, resultContainer, setting.settingId, function (data) {
            $(placeHolderImage).attr('src', data.data);
            callback(data.data);
        });
    }

    
}]);

app.controller('storeOperationsController', ['$window', '$rootScope', '$scope', '$state', '$modal', '$filter', 'ajaxCall', 'service', 'messageService', function ($window, $rootScope, $scope, $state, $modal, $filter, ajaxCall, service, messageService) {
    $scope.data = {};
    $scope.bandwidths = [{
        value: "",
        name: "Not Set"
    }, {
        value: "100",
        name: "100 Kbps"
    }, {
        value: "200",
        name: "200 Kbps"
    }, {
        value: "500",
        name: "500 Kbps"
    }, {
        value: "1000",
        name: "1 Mbps"
    }, {
        value: "2000",
        name: "2 Mbps"
    }, {
        value: "4000",
        name: "4 Mbps"
    }, {
        value: "8000",
        name: "8 Mbps"
    }, {
        value: "10000",
        name: "10 Mbps"
    }, {
        value: "15000",
        name: "15 Mbps"
    }, {
        value: "20000",
        name: "20 Mbps"
    }, {
        value: "30000",
        name: "30 Mbps"
    }, {
        value: "50000",
        name: "50 Mbps"
    }];
    $scope.dataLimits = [{
        value: "",
        name: "Not Set"
    }, {
        value: "50",
        name: "50 MB"
    }, {
        value: "100",
        name: "100 MB"
    }, {
        value: "200",
        name: "200 MB"
    }, {
        value: "500",
        name: "500 MB"
    }, {
        value: "1024",
        name: "1 GB"
    }, {
        value: "2048",
        name: "2 GB"
    }, {
        value: "4096",
        name: "4 GB"
    }, {
        value: "5120",
        name: "5 GB"
    }, {
        value: "8192",
        name: "8 GB"
    }, {
        value: "10240",
        name: "10 GB"
    }, {
        value: "20480",
        name: "20 GB"
    }, {
        value: "30720",
        name: "30 GB"
    }, {
        value: "40960",
        name: "40 GB"
    }, {
        value: "51200",
        name: "50 GB"
    }];

    $scope.blockedWebsites = null;
    $scope.sum = 0;

    $scope.getNasid = function () {
        return $scope.router.selected[0].nasid;
    }

    //-------------------

    if ($scope.$parent.routerDetails.length == 0) {
        $rootScope.loadLocationsForSelect();
    } else {
        if ($scope.$parent.routerDetails[0].nasid == _selectAllNas) {
            $scope.$parent.routerDetails.splice(0, 1);
        }
    }

    $scope.singleLocationSelectorConfig = getSingleLocationSelectorConfig();

    $scope.currentGroup = {
        selected: null
    };
    $scope.router = {
        selected: null
    }
    $scope.groups = [];

    $scope.secret = {
        error: false,
        active: false,
        enablenasid: false,
    }

    $scope.changeme = function (val) {
        if (val.length > 0) {
            $scope.secret.showall = true;
        } else {
            $scope.secret.showall = false;
        }

        $scope.secret.newnasid = $scope.router.selected[0].nasid;
        $scope.secret.enablenasid = false;
        $scope.operation.operationParameter = "";
    }   

    $scope.getWhiteAndBlckListsForStore = function () {
        if ($scope.location && $scope.location.length >= 1) {
            console.log("did something");
            var nasid = $scope.location;
            var array = {};
            array.nasid = nasid;
            $scope.router.selected = [];
            $scope.router.selected.push(array);
            $scope.fetchDetails();
        } else {
            console.log("did something else");
        }
    }

    $scope.getAuthenticationModeHelper = function () {
        if ($scope.router.selected.length == 1) {
            ajaxCall.get('/Client/GetAuthenticationModeHelper?nasid=' + $scope.router.selected[0].nasid).then(function (response) {
                $scope.data = $scope.data || {};
                $scope.data.authentiCationHelper = response.data;
            });
        }
    }

    $scope.fetchStoreGroups = function () {
        ajaxCall.get('/Admin/GetAllStoreGroups').then(function (response) {
            $scope.storeGroups = response.data;
        });
    }

    $scope.fetchDetails = function (hideAutoConfig) {
        if ($scope.router.selected.length == 1) {
            ajaxCall.get('/Client/GetListChecks?nasid=' + $scope.router.selected[0].nasid).then(function (response) {
                var newObject = {};
                var resps = response.data;
                angular.forEach(resps, function (entry, index) {
                    var list = [];
                    angular.forEach(entry, function (value, key) {
                        var elem = {};
                        elem.Key = key;
                        elem.Value = value;
                        list.push(elem);
                    });
                    newObject[index] = list;
                });

                $scope.accessList = newObject;
            });

            $scope.sum = 0;
            ajaxCall.get('/Admin/GetBlockedWebsites?nasid=' + $scope.router.selected[0].nasid).then(function (response) {
                $scope.blockedWebsites = [];
                angular.forEach(response.data, function (val) {
                    $scope.blockedWebsites.push({
                        siteName: val
                    });
                })
            });

            ajaxCall.post('/Client/GetStorePolicy?nasid=' + $scope.router.selected[0].nasid).then(function (response) {
                $scope.data = $scope.data || {};
                $scope.data.storeGroupPolicy = response.data;
            });

            $scope.getAuthenticationModeHelper();
            $scope.fetchHistory();
        }
    }

    $scope.changeDetector = function (config) {
        config.changed = false;
        if (config.attribute) {
            var previous = getObjectFromArray($scope.previousConfig.radiusConfig, 'attribute', config.attribute);
            if (previous.value.value != config.value.value)
                config.changed = true;
        } else {
            var previous = getObjectFromArray($scope.previousConfig.routerConfig, 'configType', config.configType);
            if (previous.values) {
                if (!angular.equals(previous.values, config.values))
                    config.changed = true;
            } else {
                if (previous.value.value != config.value.value)
                    config.changed = true;
            }
        }
    }

    $scope.readConetent = function ($fileContent, listObject) {
        var data = processCsvFileData($fileContent);
        angular.forEach(data, function (elem, index) {
            if (elem[0] != '' && (isValidMac(elem[0]) || isValidIndianMobile(elem[0]))) {
                var obj = {};
                obj.Key = elem[0];
                obj.Value = elem[1];
                listObject.push(obj);
            }
        });

        angular.forEach(
        angular.element("input[type='file']"),
        function (inputElem) {
            angular.element(inputElem).val(null);
        });

        if (listObject.length == 0)
            messageService.showError('Csv file formate is not correct');
 
    }

    $scope.removeAll = function (listObject) {
        for (var i = listObject.length - 1; i >= 0; i--) {
            listObject.splice(i, 1);
        }
    }

    $scope.submitAccessList = function () {
        document.getElementById("saveAccessList").disabled = true;
        setTimeout(function () { document.getElementById("saveAccessList").disabled = false; }, 5000);
        var newObject = {}
        angular.forEach($scope.accessList, function (entry, index) {
            newObject[index] = {};
            angular.forEach(entry, function (elem) {
                if (elem.Key && elem.Key != '')
                    newObject[index][elem.Key] = (elem.Value ? elem.Value : '')
            });
        });
        for (var i = 0; i < $scope.router.selected.length; ++i) {
            ajaxCall.post('/Client/SaveListChecks?nasid=' + $scope.router.selected[i].nasid, {
                accessList: newObject
            }).then(function (response) {
                messageService.showSuccess('Saved Successfully');
                $scope.fetchDetails(true);
            });
        }
    }

    $scope.saveAuthenticationModeHelper = function () {
        ajaxCall.post('/Client/SaveAuthenticationModeHelper?nasid=' + $scope.router.selected[0].nasid, {
            value: $scope.data.authentiCationHelper
        }).then(function (response) {
            messageService.showSuccess('Saved Successfully');
            $scope.fetchDetails(true);
        });
    }

    $scope.saveBlockedWebsite = function () {
        var domains = [];
        angular.forEach($scope.blockedWebsites, function (val) {
            domains.push(val.siteName);
        })
        ajaxCall.post('/Admin/SaveBlockedWebsites?nasid=' + $scope.router.selected[0].nasid, domains)
            .then(function (response) {
                messageService.showSuccess('Saved Successfully');
                $scope.fetchDetails(true);
            });
    }

    $scope.saveStorePolicyGroup = function () {
        ajaxCall.post('/Client/UpdateStorePolicy?nasid=' + $scope.router.selected[0].nasid + '&value=' + $scope.data.storeGroupPolicy)
            .then(function (response) {
                messageService.showSuccess(response.msg);
                $scope.fetchDetails(true);
            });
    }

    $scope.logoutUser = function (obj) {
        ajaxCall.post('/Admin/LogoutUser?nasid=' + $scope.getNasid(), obj).then(function () {
            messageService.showSuccess('User will be logged out in few minutes.');
            $scope.fetchDataUsage();
        });
    }

    var getOperations = function () {
        $scope.operations = [];
        $scope.operationList = [];
        $scope.operation = {};
        ajaxCall.get(remoteEndpoint + '/DeviceConfig/GetAllOperations').then(function (response) {
            $scope.operations = response.data;
        });
        getIpsets();
    }

    var getIpsets = function () {
        $scope.ipsets = [];
        ajaxCall.get('/Admin/GetAllIpsets').then(function (response) {
            $scope.ipsets = response.data;
        });
    }

    $scope.showDomains = function (val) {
        parts = val.split(":");
        $scope.domains = parts[1];
        $scope.operation.operationParameter = parts[0]
    }

    $scope.fetchHistory = function () {
        ajaxCall.get(remoteEndpoint + '/DeviceConfig/GetPendingOp?nasid=' + $scope.router.selected[0].nasid).then(function (response) {
            $scope.operationList = response.data;
        });
    }

    $scope.submitOp = function () {
        $scope.operation.controllerId = $scope.operation.controllerId || 1;        
        ajaxCall.post(remoteEndpoint + '/DeviceConfig/SubmitOp?nasid=' + $scope.router.selected[0].nasid, $scope.operation).then(function (response) {
            messageService.showSuccess('Operation Submitted successfully');
            $scope.operation.operationParameter = "";
        });
    }

    $scope.pageSize = 50;

    $scope.filter = {};

    $scope.storeList = [];
    $scope.data = $scope.data || {};
    $scope.data.infiniteScrollDisabled = false;
    $scope.data.showClientForm = false;

    var getLocationForAdmin = function (qObj) {
        $scope.userBrand.then(function () {
            $scope.groupOfNases = '';
            //qObj.installedState = $scope.selectedInsState ? $scope.selectedInsState : 1;
            qObj.installedState = $scope.selectedInsState;
            qObj.city = $scope.selectedCity;
            qObj.category = $scope.selectedCategory;
            //qObj.partnerCode = $scope.selected_PartnerCode;
            qObj.partnerId = $scope.routerFilters.selected_PartnerId;
            qObj.partnerAccountType = $scope.routerFilters.selected_PartnerAccountType;
            qObj.storeTags = "%";
            if (!isNaN($scope.searchTerm)) {
                qObj.nasid = $scope.searchTerm;
            } else {
                qObj.storeName = qObj.storeNameAlias = qObj.address = qObj.state = $scope.searchTerm;
            }
            //console.log('loading page ' + qObj.pageNumber);
            service.getLocationsForAdmin(qObj).then(function (locations) {
                angular.forEach(locations, function (value, index) {
                    if ($rootScope.username === '<EMAIL>' || $rootScope.name === '<EMAIL>') {
                        service.demofizeStores(value)
                    }
                    value.lastUsed = '<img src="../images/ellipsis.gif">';
                    value.inLast24Hours = '<img src="../images/ellipsis.gif">';

                    if (value.category == '')
                        value.category = "N/A"
                    else if (value.category.indexOf(',') > -1)
                        value.category = value.category.split(',')[0];

                    value.routerState = value.routerState.toString();

                    if (value.routerState != "2") {
                        if (value.lastPingDelay < 0)
                            value.lastPingDelay = -value.lastPingDelay;
                        if (value.lastPingDelay > 35) value.downSinceHalfHour = true;
                        value.lastPingDelay = roundOffTime(value.lastPingDelay * 60);
                    }

                    $scope.storeList.push(value);

                    if (index < locations.length - 1) {
                        $scope.groupOfNases += value.nasid + ','
                    } else {
                        $scope.groupOfNases += value.nasid;
                    }
                });
                //console.log('loaded page ' + qObj.pageNumber);
                if (locations.length < $scope.pageSize)
                    $scope.data.infiniteScrollDisabled = true;
                else
                    $scope.data.infiniteScrollDisabled = false;
                $scope.$broadcast('params-updated');
                $('.selectize-input input').attr('placeholder', 'Filter ' + $scope.storeList.length + ' Search Results');
            });
        });
    }

    $scope.filterRouters = function (options) {
        if ($scope.searchTerm.length == 0)
            $('.selectize-input input').attr('placeholder', 'No Search Query. No Search results to filter.');
        options = options || {};
        var queryObjectCopy = angular.copy(queryObject);
        queryObjectCopy.pageNumber = $scope.pageNumber = 1;
        queryObjectCopy.pageSize = 500;
        $scope.storeList = [];
        getLocationForAdmin(queryObjectCopy);
        options.stats && getDevicesStats(queryObjectCopy);
        if (queryObjectCopy.hasOwnProperty('partnerId') && typeof queryObjectCopy.partnerId != "undefined") {
            service.setUserBrand({ partnerId: queryObjectCopy.partnerId });
        } else if (queryObjectCopy.hasOwnProperty('partnerId') && queryObjectCopy.partnerId == "undefined") {
            service.setUserBrand({ partnerId: 0 });
        } else {
            service.setUserBrand({ partnerId: 0 });
        }
        options.scrollTo && $rootScope.scrollTo(options.scrollTo);
    }

    $scope.fetchStoreGroups();
    getOperations();
}]);

app.controller('packagesController', ['$rootScope', '$scope', '$state', 'service', 'ajaxCall', '$location', 'messageService', '$window', function ($rootScope, $scope, $state, service, ajaxCall, $location, messageService, $window) {
    $scope.newpackage = {};
    var segments = $('#segments');
    var packages = $('#packages');
    var credentialBox = $('#credentialCheck');

    var toggleSections = function() {
        packages.toggleClass('display-none');
        segments.toggleClass('display-none');
    }
    
    $scope.featureList = [];
    $rootScope.userDara.then(function (result) {
        if ($rootScope.isConfigAdminUser) {
            ajaxCall.get('/Client/getAllFeatures').then(function (response) {
                $scope.featureList = response.data;
            });
        }
    });

    $scope.featureListConfig = {
        valueField: 'Key',
        labelField: 'Value',
        searchField: ['Key', 'Value'],
        plugins: ['remove_button'],
        delimiter: '|',
        placeholder: 'Select or search from the list',
        sortField: 'Key',
        render: {
            item: function (item) {
                var ret = '<div class="item" title="' + item.Value + '">';
                ret += '<span class="storeName">' + item.Value + '</span>';
                ret += '</div>';
                return ret;
            },
            option: function (item, escape) {
                var label = escape(item.Value);
                var caption = '<span>Template Name: ' + escape(item.Value) + '</span>';

                return '<div title="' + item.Value + '">' +
                    '<div class="primary">' + label + '</div>' +
                '</div>';
            }
        },
        maxItems: 50
    };
    

    ajaxCall.get('/Client/getPackages').then(function (response) {
        $scope.myPackages = response.data;
        
        angular.forEach($scope.myPackages, function (packages, index) {
            var array = new Array();
            angular.forEach(packages.features, function (feature) {
                for (i = 0; i < featureMap.length; i++) {
                    if (featureMap[i].code == feature) {
                        array.push(featureMap[i])
                    }
                }
                
            });
            $scope.myPackages[index].features = array;
        });
    });

    var queryParams = $location.search();
    if (queryParams && queryParams.payment) {
        var amount = getCookie('i2e1-package-amount');
        if (amount && queryParams.payment == 'success') {
            $rootScope.$broadcast('InfoMessage', {
                type: '', data:
                    [{ msg: 'Payment of Rs ' + amount + ' successful.', type: 'success' },
                     { msg: 'Upgrading the package', type: 'success' }]
            });

            ajaxCall.post('/Payment/PackagePaymentSuccessful', {
                id: queryParams.id
            }).then(function (response) {
                switch (response.status) {
                    case 0:
                        $rootScope.$broadcast('InfoMessage', {
                            type: '', data:
                                [{ msg: 'Package upgraded', type: 'success' }]
                        });
                        break;
                    case 2:
                        $rootScope.$broadcast('InfoMessage', {
                            type: '', data:
                                [{ msg: 'Package upgraded failed', type: 'failure' }]
                        });
                        break;

                }
            });
        }
        if (amount && queryParams.payment == 'failure') {
            $rootScope.$broadcast('InfoMessage', {
                type: '', data:
                    [{ msg: 'Payment of Rs ' + amount + ' failed.', type: 'failure' }]
            });
            ajaxCall.post('/Payment/PackagePaymentFailure', {
                id: queryParams.id
            });
        }
    }

    $scope.getPackagesForSegment = function (segment) {
        ajaxCall.get('/Payment/GetPackages?segment=' + segment.link).then(function (response) {
            angular.forEach(response.data, function (package1) {
                var features = [];
                angular.forEach(package1.features, function (feature) {
                    features.push(getFeatureObject(feature, package1.packageJsonData));
                });
                package1.features = features;
            });
            $scope.packages = response.data;
            toggleSections();
        });
    }

    $scope.upgrade = function (package1) {
        $scope.askCredentials = {
            formData: [
                { name: 'amount', value: package1.price },
                { name: 'sellablePackageId', value: package1.sellablePackageId },
                { name: 'checqueDDNumber', value: '564674' },
                { name: 'paymentMode', value: 1 },
            ],
            OKText: 'Verify',
            action: '/Client/PaymentUserCheck',
            OK: function (askCredentials) {
                var askCredentialsForm = $('#askCredentials');
                var inputs = askCredentialsForm.find('input');
                var data = {};
                angular.forEach(inputs, function (input) {
                    data[input.name] = input.value;
                });
                setCookie('i2e1-package-amount', package1.price, 10 * 60);
                ajaxCall.post(askCredentialsForm[0].action, data).then(function (response) {
                    if (response.status == 0) {
                        $window.location.href = response.data;
                    } else {
                        askCredentials.error = response.msg;
                    }
                    
                });
            }
        }

        if ($rootScope.isConfigAdminUser)
        {
            data = {}
            data.sellablePackageId = package1.sellablePackageId;
            data.amount = package1.price;
            data.checqueDDNumber = '564674';
            data.paymentMode = 1;
            setCookie('i2e1-package-amount', package1.price, 10 * 60);
            ajaxCall.post('/Client/UpgardeUsersPackage', data).then(function (response) {
                if (response.status == 0) {
                    window.location.href = response.data;
                    window.location.reload();
                    return false;
                } else {
                    askCredentials.error = response.msg;
                }
            });
        }
        else
        {
            $('#credentialCheck').toggleClass('display-none');
            $('#credentialCheck')[0].scrollTop = 60;
        }
        
    }

    $scope.createPackages = function () {
        ajaxCall.post('/Client/createPackages', { packages: $scope.newpackage }).then(function (response) {
            if (response.data > 0) {
                messageService.showSuccess('Package created successfully!');
                window.location.reload(true);
            }
                
            else
                messageService.showError('Something went wrong please try again later!');
        });
    }
}]);

app.controller('smsController', function ($scope, $rootScope, service, ajaxCall, $modal, messageService, $state, $timeout) {
    var nases;
    $scope.smsesExhausted = 0;
    $scope.Math = Math;
    loadScript('/jsLibs/Chart.min.js', 'text/javascript', 'utf-8');
    //loadScript('/jsLibs/clipboard.min.js', 'text/javascript', 'utf-8');
    $scope.SmsPageNumber = 0;
    $scope.SmsPageSize = 10;
    $scope.infiniteScrollDisabled = false;
    $scope.smses = [];
    $scope.tooltipText = 'Copy Text to Clipboard';
    $scope.availableCities = [];
    if ($scope.$parent.positiveNases.length == 0) {
        $rootScope.loadLocationsForSelect();
        if ($scope.$parent.positiveNases.length > 0 && $scope.$parent.positiveNases[0].storeName != _selectAllObj.storeName)
            $scope.$parent.positiveNases.insert(0, _selectAllObj);
        else
            $scope.$parent.positiveNases.push(_selectAllObj);
    } else {
        if ($scope.$parent.positiveNases.length > 0 && $scope.$parent.positiveNases[0].storeName != _selectAllObj.storeName)
            $scope.$parent.positiveNases.insert(0, _selectAllObj);
        else
            $scope.$parent.positiveNases.push(_selectAllObj);
    }

    $scope.loadMoreSMS = function () {
        $rootScope.userDara.then(function (result) {
            loadSMS();
        });
    }

    $("#message-text-area span").on('keyPress', function () {
        event.stopImmediatePropagation();
    });
    
    var loadSMS = function () {
        
        if ($scope.infiniteScrollDisabled) return;
        $scope.infiniteScrollDisabled = true;
        $scope.SmsPageNumber = $scope.SmsPageNumber + 1;
        
        ajaxCall.post('/Kelp/GetSMSes', {
            pageNumber: $scope.SmsPageNumber,
            pageSize: $scope.SmsPageSize,
            accountInfo: $rootScope.accountInfo,
            target: $scope.data.smsHistoryTarget
        }).then(function (response) {
            angular.forEach(response.data, function(sms) {
                $scope.smses.push(sms);
                sms.locations = [];
                $scope.smsesExhausted += sms.smsesExhausted;
                sms.scheduleDateView = getDateString(sms.scheduleTime);
                sms.scheduleTime = getTimeString(sms.scheduleTime);
                angular.forEach(sms.nases.split(','), function (nas) {
                    var obj = $scope.positiveNases.find(function (router) {
                        return router.nasid == nas;
                    });
                    obj && sms.locations.push(obj);
                });
            });
            if (response.data.length == 0) {
                $scope.infiniteScrollDisabled = true;
            } else {
                $scope.infiniteScrollDisabled = false;
            }
        });
    }

    $scope.getFilterSmsHistory = function () {
        $scope.SmsPageNumber = 0;
        $scope.SmsPageSize = 10;
        $scope.infiniteScrollDisabled = false;
        $scope.smses = [];
        loadSMS();
    }

    var generateNasids = function (sms) {
        sms.nases = '';

        angular.forEach(sms.locations, function (location) {
            sms.nases += location.nasid + ',';
        });
        sms.nases = sms.nases.slice(0, -1);
    }

    $scope.onCopySuccess = function (e) {
        //$scope.tooltipText = 'copied!';
        $('.tooltip-inner').text('copied!')
    };

    $scope.truncate = function (string,length) {
        return string.substring(0, Math.min(length, string.length));
    }

    $scope.saveSMS = function (sms) {
        //return;
        generateNasids(sms);
        var numbers = [];
        var valid = true;
        if (!sms.campaign) {
            //messageService.showError("Campaign cannot be empty");
            document.getElementById('create_campaign_select').className += " has-error";
            document.getElementById('show-campaign-error').style.display = "block";
            document.getElementById('create_sms_form').scrollTop = 0;
            return;
        } else {
            document.getElementById("create_campaign_select").classList.remove("has-error");
            document.getElementById('show-campaign-error').style.display = "none";
        }

        if (!sms.message) {
            document.getElementById('message-text-area').className += " has-error";
            document.getElementById('show-message-box-error').style.display = "block";
            document.getElementById('create_sms_form').scrollTop = 0;
            return;
        } else {
            document.getElementById("message-text-area").classList.remove("has-error");
            document.getElementById('show-campaign-error').style.display = "none";
        }

        if (!$scope.staticDetails.senderIds.length || !sms.senderId) {
            sms.senderId = 'BULKSMS';
        }

        sms.selectedNumbers = sms.selectedNumbers || [];

        delete sms.numbers;

        var dateRange = sms.rules.find(function (rule) {
            return rule.title == 'Visited between';
        });
        var visitRange = sms.rules && sms.rules.find(function (rule) {
            return rule.title == 'Visit frequency';
        }) || {};

        var notVisitedSince = sms.rules && sms.rules.find(function (rule) { return rule.title == 'Not Visited Since'; }) || {};
        notVisitedSince = notVisitedSince.from;


        var data = {
            sms: sms,
            numbers: sms.selectedNumbers,
            from: 0,
            to: 0,
            accountInfo: $rootScope.accountInfo
        };

        if (!sms.uploadFeature) {
            data = {
                sms: sms,
                numbers: sms.selectedNumbers,
                fromDate: sms.notVisitedSince ? '' : dateRange.from,
                toDate: sms.notVisitedSince ? '' : dateRange.to,
                from: visitRange.from || 0,
                to: visitRange.to || 0,
                notVisitedSince: sms.notVisitedSince ? notVisitedSince : '',
                accountInfo: $rootScope.accountInfo
            }
        }
        if (sms.rules) {
            angular.forEach(sms.rules, function (rule) {
                if (rule.title == "Visit frequency") {
                    if ((rule.from != null || rule.from != '') && (rule.to != null || rule.to != '') && parseInt(rule.from) > parseInt(rule.to)) {
                        rule.error = rule.title + '\'s from value can not be smaller than to value';
                        valid = false;
                        return;
                    } else {
                        rule.error = '';
                    }
                } else if (rule.title == "Visited between") {
                    if ((rule.from != null || rule.from != '') && (rule.to != null || rule.to != '') && new Date(rule.from) > new Date(rule.to)) {
                        rule.error = rule.title + '\'s from value can not be smaller than to value';
                        valid = false;
                        return;
                    } else {
                        rule.error = '';
                    }
                }
            });
        }

        if (valid) {
            ajaxCall.post('/Kelp/SaveSMS', data).then(function (response) {
                if (response.status == 2) {
                    messageService.showError(response.msg);
                } else $state.reload();
            });
        }
    }

    var senderIdsList = [];
    service.getSMSStaticDetails().then(function (response) {
        $scope.staticDetails = response.data;
    });

    
    $scope.createGraph = function (sms, $event) {
        if ($($event.currentTarget).hasClass('collapsed')) {
            sms.selectedNumbers = [];
            var graphData = {
                labels: [],
                datasets: []
            };
            labelMapNew = angular.copy(labelMap);
            ajaxCall.post('/Kelp/LoadSMSReports', {
                sms: sms
            }).then(function (response) {
               angular.forEach(response.data, function (report, key) { 
                    if (labelMapNew.hasOwnProperty(key.toUpperCase().replace('-', '_'))) {
                        labelMapNew[key.toUpperCase().replace('-', '_')].value = report;
                    } else {
                        if (labelMapNew['OTHERS'].value) labelMapNew['OTHERS'].value++
                        else labelMapNew['OTHERS'].value = 1;
                    }
                });

                graphData.labels = [];
                var dataset = {
                    data: [],
                    backgroundColor: [],
                    hoverBackgroundColor: []
                };

                angular.forEach(labelMapNew, function (entry, key) {
                    if (entry.value) {
                        graphData.labels.push(entry.label);
                        dataset.data.push(entry.value);
                        dataset.backgroundColor.push(entry.color);
                        dataset.hoverBackgroundColor.push(entry.color);
                    }
                });
                graphData.datasets[0] = dataset;

                new Chart($('#' + 'sms_' + sms.id), {
                    type: 'pie',
                    data: graphData,
                    options: {
                        responsive: true
                    }
                });
            });
        }
        
    }

    $scope.createSMS = function (sms) {
        sms = sms || {};
        sms.notVisitedSince = false;
        sms.selectedNumbers = sms.selectedNumbers || [];
        var dateRange = sms.rules && sms.rules.find(function (rule) { return rule.title == 'Visited between'; }) || {};
        dateRange.to = dateRange.to || moment()
        dateRange.from = dateRange.from || moment().add('day', -30);

        var today = moment();

        if (!sms.rules && !sms.id) {
            sms.rules = [
            { title: "Visited between" },
            { title: "Visit frequency" },
            { title: "Not Visited Since" }
            ];
        }

        var loadNumbers = function (sms) {
            if (sms.id) return;
            var dateRange = sms.rules.find(function (rule) {
                return rule.title == 'Visited between';
            });
            var visitRange = sms.rules && sms.rules.find(function (rule) {
                return rule.title == 'Visit frequency';
            }) || {};

            if (visitRange.from < 1) {
                messageService.showError('Visit Frequency from should be greater than 1');
                return;
            }

            if (visitRange.to < 1 || visitRange.to < visitRange.from) {
                messageService.showError('Visit Frequency to should be greater than 1 and from range');
                return;
            }

            var notVisitedSince = sms.rules && sms.rules.find(function (rule) {
                return rule.title == 'Not Visited Since';
            }) || {};
            notVisitedSince = notVisitedSince.from;

            if (!(sms.locations && sms.locations.length)) {
                return;
            } else {
                if (sms.locations[sms.locations.length - 1].nasid == _selectAllNas) {
                    var nasLocations = [];
                    var nasIds = [];
                    angular.forEach($scope.$parent.positiveNases, function (location, index) {
                        if (nasIds.indexOf(location.nasid) == -1 && location.nasid != _selectAllNas) {
                            nasLocations.push(location);
                            nasIds.push(location.nasid);
                        }
                    });
                    sms.locations = nasLocations;
                    sms.nases = nasIds.join();
                }
            }
            generateNasids(sms);
            ajaxCall.post('/Kelp/LoadNumbers', {
                nases: sms.nases,
                fromDate: sms.notVisitedSince ? '' : dateRange.from,
                toDate: sms.notVisitedSince ? '' : dateRange.to,
                from: visitRange.from || 0,
                to: visitRange.to || 0,
                notVisitedSince: sms.notVisitedSince ? notVisitedSince : '',
            }).then(function (response) {
                data.sms.selectedNumberCount = response.data;
            });
        }

        var openTab = function (id) {
            $(".get-numbers.nav li").removeClass('active');
            $(".get-numbers.nav li." + id).addClass('active');

            $(".tab-content>div.tab-pane").hide();
            $(".tab-content #" + id).show();
        }


        var readNumberCSV = function (contents) {
            contents = contents.split("\r\n");
            var uniqueNumbers = [];
            sms.selectedNumbers = [];
            sms.nases = "";
            sms.rules = [];
            sms.uploadFeature = true;
            $.each(contents, function (i, el) {
                el = el.replace(/,/g, "");
                if (el && $.inArray(el, uniqueNumbers) === -1 && isValidIndianMobile(el)) {
                    uniqueNumbers.push(el);
                    sms.selectedNumbers.push({ mobile: el });
                }
            });

            data.sms.selectedNumberCount = sms.selectedNumbers.length;
            if (data.sms.selectedNumberCount > 5000) {
                messageService.showError('Please upload less than 5000 numbers');
                sms.selectedNumbers = [];
            }
            $scope.$apply();

        }

        $scope.campaigns = [{
            'id': 1,
            'name' : 'Test Campaign'
        }]
        ajaxCall.get('/Client/GetAllCampaigns').then(function (response) {
            $scope.campaigns = response.data;
        });

        ajaxCall.get('/Client/GetFilterOptions?filter=shop_city').then(function (response) {
            
            var array = [{ city: 'All Cities' }];
            angular.forEach(response.data.sort(), function (value) {
                var obj = {}
                if (value != '') {
                    obj.city = value
                }
                array.push(obj);
            });
            $scope.availableCities = array;
        });

        var refreshResults = function ($select) {
            var search = $select.search,
              list = angular.copy($select.items),
              FLAG = -1;
            //remove last user input
            list = list.filter(function (item) {
                return item.id !== FLAG;
            });

            if (!search) {
                //use the predefined list
                $select.items = list;
            }
            else {
                //manually add user input and set selection
                var userInputItem = {
                    id: FLAG,
                    name: search,
                    content : ''
                };
                $select.items = [userInputItem].concat(list);
                //$select.selected = userInputItem;
            }
        }

        var clear = function ($event, $select) {
            $event.stopPropagation();
            //to allow empty field, in order to force a selection remove the following line
            $select.selected = undefined;
            //reset search query
            $select.search = undefined;
            //focus and open dropdown
            $select.activate();
        }

        var selectCamapaign = function (sms, select) {
            sms.message = select.selected.content;
        }

        var data = {
            sms: sms,
            smsSent: false,
            loadNumbers: loadNumbers,
            lengthCheck: function () {
                if (!sms.message) sms.message.unit = 0;
                if (sms.message.Length <= 160) sms.message.unit = 1;
                sms.message.unit = ((sms.message.Length - 1) / 153) + 1;
            },
            readNumberCSV: readNumberCSV,
            staticDetails: $scope.staticDetails,
            openTab: openTab,
            startDateOptions: {
                dateObject: dateRange.from,
                maxDate: today,
                startDate: dateRange.from,
                endDate: dateRange.to,
                locale: {
                    format: Constants.dateFormat
                },
                drops: 'up'
            },
            endDateOptions: {
                dateObject: dateRange.to,
                maxDate: today,
                startDate: dateRange.to,
                endDate: dateRange.to,
                locale: {
                    format: Constants.dateFormat
                },
                drops: 'up'
            },
            refreshResults : refreshResults,
            clear: clear,
            selectCamapaign: selectCamapaign,
            selectedCity: null,
            //smsHistoryTarget:null
            smsHistoryTarget: 'wifi'
        }

        if (sms.id) {
            data.smsSent = true;
        }
        $scope.data = data;
    }

    $scope.getLocationsForCity = function () {
        $scope.$parent.positiveNases = [];
        var qObj = angular.copy(queryObject);
        qObj.pageNumber = 1;
        qObj.pageSize = 999;
        if ($scope.data.selectedCity) {
            qObj.city = '';

            if ($scope.data.selectedCity[$scope.data.selectedCity.length - 1].city == 'All Cities') {
                var cityArray = []
                var cities = [];
                angular.forEach($scope.availableCities, function (location, index) {
                    if (cities.indexOf(location.city) == -1 && location.city != 'All Cities') {
                        cityArray.push(location);
                        cities.push(location.city);
                    }
                });
                angular.forEach(cities, function (item) {
                    qObj.city += item + ',';
                });
                qObj.city = qObj.city.slice(0, -1);
                $scope.data.selectedCity = cityArray;
            } else {
                angular.forEach($scope.data.selectedCity, function (item) {
                    qObj.city += item.city + ',';
                });
                qObj.city = qObj.city.slice(0, -1)
            }
            
        }
            
        qObj.storeTags = "%";
        $scope.userBrand.then( function() {
            if ($scope.routerFilters.selected_PartnerId && $scope.routerFilters.selected_PartnerId > 0)
                qObj.partnerId = $scope.routerFilters.selected_PartnerId;
            service.getLocationsForAdmin(qObj).then(function (locations) {
                $timeout(function () {
                    var arr = [];
                    angular.forEach(locations, function (store, index) {
                        if (store.storeName && store.nasid > 0) arr.push(store);
                    });
                    $scope.$parent.positiveNases = arr;
                    if ($scope.$parent.positiveNases.length > 0 && $scope.$parent.positiveNases[0].storeName != _selectAllObj.storeName)
                        $scope.$parent.positiveNases.insert(0, _selectAllObj);
                }, 500);
            });
        })
        
    }
    
});

app.controller('promotionController', function ($scope, $rootScope, service, ajaxCall, $modal, fileUploadService, $state, messageService) {
    moment.locale('en');
    var nases;
    $scope.OffersPageNumber = 0;
    $scope.OffersPageSize = 20;
    $scope.infiniteScrollDisabled = false;
    $scope.offers = [];
    
    $scope.checkOffers = function () {
        if ($scope.offers.length == 0)
            $scope.loadMoreOffers();
    }
    
    if ($scope.$parent.positiveNases.length == 0) {
        $rootScope.loadLocationsForSelect();
        if ($scope.$parent.positiveNases.length > 0 && $scope.$parent.positiveNases[0].storeName != _selectAllObj.storeName)
            $scope.$parent.positiveNases.insert(0, _selectAllObj);
        else
            $scope.$parent.positiveNases.push(_selectAllObj);
        
    } else {
        if ($scope.$parent.positiveNases.length > 0 && $scope.$parent.positiveNases[0].storeName != _selectAllObj.storeName)
            $scope.$parent.positiveNases.insert(0, _selectAllObj);
        else
            $scope.$parent.positiveNases.push(_selectAllObj);
    }

    $scope.loadMoreOffers = function () {
        if ($('#campaign_history').is(':hidden')) {
            return;
        }
        $rootScope.userDara.then(function (result) {
            loadOffers();
        });
    }
    
    var campaignIdsForReports = [];

    var loadOffers = function () {
        if ($scope.infiniteScrollDisabled) return;
        $scope.infiniteScrollDisabled = true;
        $scope.OffersPageNumber = $scope.OffersPageNumber + 1;
        ajaxCall.post('/Kelp/GetVouchersForAdmin', {
            pageNumber: $scope.OffersPageNumber,
            pageSize: $scope.OffersPageSize,
            accountInfo: $rootScope.accountInfo,
        }).then(function (response) {
            angular.forEach(response.data, function (offer) {
                offer.pagination = {};
                offer.pagination.currentPage = 1;
                offer.pagination.numPerPage = 5;
                offer.pagination.maxSize = 5;
                offer.senderId = offer.senderApiParameters ? offer.senderApiParameters[0] : null;
                offer.from = getDateString(offer.from);
                offer.to = offer.to ? getDateString(offer.to) : 'non ending';
                campaignIdsForReports.push("'" + offer.id + "'");
                $scope.offers.push(offer);
            });

            if (response.data.length == 0) {
                $scope.infiniteScrollDisabled = true;
            } else {
                $scope.infiniteScrollDisabled = false;
            }
        });
    }

    var getOfferLocations = function (offer) {
        ajaxCall.post('/Kelp/GetVoucherLocations', {
            offerId: offer.id,
        }).then(function (response) {
            offer.locations = [];
            angular.forEach(response.data, function (nasid) {
                var obj = $scope.positiveNases.find(function (router) {
                    return router.nasid == nasid;
                });
                obj && offer.locations.push(obj);
            });
        });
    }

    var changePowerRange = function (rule) {
        switch (rule.from) {
            case "0":
            case "80":
                rule.to = "200"; break;
            case "-1": rule.to = "80"; break;
        }
    }

    $scope.uploadCouponImage = function (input) {
        $('#file_upload').click();
    }

    $scope.saveOffer = function (offer, type) {
        if (!$scope.routerFilters.selected_PartnerId) {
            angular.element('[ng-model="routerFilters.selected_PartnerId"]').parent().addClass('has-error');
            messageService.showError('Please select a brand for this campaign!');
            return;
        } else {
            angular.element('[ng-model="routerFilters.selected_PartnerId"]').parent().removeClass('has-error');
        }
        var valid = true;
        if (offer.image_link == 'https://') offer.image_link = '';

        if ((!offer.locations || !offer.locations.length) && type == 'save') {
            messageService.showError("Please choose atleast 1 location", 5000);
            return;
        }
        
        if (offer.text) {
            if (offer.senderId) {
                offer.senderApiParameters = [];
                offer.senderApiParameters.push(offer.senderId);
            } else {
                alert("Please choose sender id");
                return;
            }
        }

        if(!(offer.image || offer.videoPath)) {
            messageService.showError("Upload image or video", 5000);
            return;
        }

        if (valid) {
            delete offer.senderId
            ajaxCall.post('/Kelp/SaveVoucher', {
                voucher: offer,
                dater: offer.dater
            }).then(function (response) {
                if (response.status == 0)
                    messageService.showSuccess('Promotion Updated');
                else messageService.showError('Error in updating promotion. Please Contact i2e1.');
            });
        }
    }

    

    $scope.deActivateOffer = function (offer) {
        offer.active = 0;  // For Deactivating the offer
        ajaxCall.post('/Kelp/ChangePromotionStatus', {
            voucher: offer,
            dater: offer.dater
        }).then(function (response) {
            if (response.status == 0)
                messageService.showSuccess('Promotion dissbled');
            else messageService.showError('Error in dissbling promotion. Please Contact i2e1.');
        });
    }

    $scope.activateOffer = function (offer) {
        offer.active = 1; // For Activating the offer
        ajaxCall.post('/Kelp/ChangePromotionStatus', {
            voucher: offer,
            dater: offer.dater
        }).then(function (response) {
            if (response.status == 0)
                messageService.showSuccess('Promotion enabled');
            else messageService.showError('Error in enabling promotion. Please Contact i2e1.');
        });
    }

    $scope.editOffer = function (offer) {
        console.log(offer);
        document.getElementById('create_campaign_nav').innerHTML = 'Edit Promotion ' + offer.id;
        getOfferLocations(offer);
        $scope.createOffer(offer);
        service.loadPromotionDailyReports(offer.id).then(function (dailyReports) {
            offer.dailyReports = dailyReports;
        });
        $('#create_campaign_nav').click();
    }

    $scope.consolidateNow = function ($event, offer) {
        $event.stopPropagation();
        ajaxCall.get('/Client/ConsolidateNow?campaignId=' + offer.id).then(function (response) {
            $scope.getOffersDailyReport(offer);
        });
    }

    $scope.getOffersDailyReport = function (offer) {
        service.loadPromotionDailyReports(offer.id, offer.pagination.currentPage, offer.pagination.numPerPage).then(function (dailyReports) {
            if (Object.keys(dailyReports).length > 0) {
                offer.pagination.totalItems = dailyReports[Object.keys(dailyReports)[0]].total_items;
                offer.dailyReports = dailyReports;
            }
            else {
                var obj = {};
                obj[offer.fDate] = {
                    "coupons_viewed": 0,
                    "coupon_clicks": 0,
                    "sms_delivered": 0,
                    "sms_clicks": 0,
                    "campaign_consumption": 0
                }
                offer.pagination.totalItems = obj[Object.keys(obj)[0]].total_items;
                offer.dailyReports = obj;
            }
        });
    }

    $scope.createOffer = function (offer) {
        offer = offer || {};
        offer.tDate = offer.tDate || moment().add('day', 30);
        offer.fDate = offer.fDate || moment();
        offer.dater = offer.dater || 'day';
        offer.voucherPlacement = offer.voucherPlacement || "41";
        offer.image_link = offer.image_link || 'https://';
        var today = moment();
        offer.senderId = 'BULKSMS';

        var checkMultiselect = function (offer) {
            if (offer.locations[offer.locations.length - 1].nasid == _selectAllNas) {
                var nasLocations = [];
                var nasIds = [];
                angular.forEach($scope.positiveNases, function (location, index) {
                    if (nasIds.indexOf(location.nasid) == -1 && location.nasid != _selectAllNas) {
                        nasLocations.push(location);
                        nasIds.push(location.nasid);
                    }
                });
                offer.locations = nasLocations;
                offer.nases = nasIds.join();
            }
            var nases = offer.locations.map(a => a.nasid).join();
            ajaxCall.get('/Client/GetEstimatedUsers?nasids=' + nases + '&dater=' +
               offer.dater + '&daysOfWeek=1,3').then(function (response) {
                   data.reach = response.data.estimaterUsers;
            });
        }

        var data = {
            offer: offer,
            checkMultiselect:checkMultiselect,
            
            startDateOptions: {
                singleDatePicker: true,
                autoApply: true,
                startDate: moment(offer.fDate, 'DD-MMM-YYYY').isValid() ? moment(offer.fDate) : moment(),
                endDate: moment(offer.fDate, 'DD-MMM-YYYY').isValid() ? moment(offer.fDate) : moment(),
                maxDate: moment(offer.tDate, 'DD-MMM-YYYY').isValid() ? moment(offer.tDate) : moment(),
                locale: {
                    format: "DD-MMM-YYYY",
                }
            },
            endDateOptions: {
                singleDatePicker: true,
                autoApply: true,
                startDate: moment(offer.tDate, 'DD-MMM-YYYY').isValid() ? moment(offer.tDate) : moment(),
                endDate: moment(offer.tDate, 'DD-MMM-YYYY').isValid() ? moment(offer.tDate) : moment(),
                minDate: moment(offer.fDate, 'DD-MMM-YYYY').isValid() ? moment(offer.fDate) : moment(),
                locale: {
                    format: "DD-MMM-YYYY",
                }
            },
            offerPreviewAvailable: function (offer) {
                return offer.image || offer.videoPath
            },
            changePowerRange: changePowerRange
        }
        var initializeUpload = function (obj, containerId, resultContainer) {
            var callback = function (response) {
                if (response.data.typeOfFile == "image") {
                    obj.image = response.data.pathToFile;
                    obj.offer_type = 0;
                } else {
                    obj.videoPath = response.data.pathToFile;
                    obj.offer_type = 1;
                }
                
                $scope.$apply();
            }
            fileUploadService.initializeUpload(containerId, resultContainer, $rootScope.username, callback, {
                maxSize: 500000,
                uploadUrl: '/Client/UploadClientFile',
                fileUse: 'campaign',
                clientId: $rootScope.accountInfo.clientId
            });
        }
        initializeUpload(offer, '#upload_file', '#result_container');
        $scope.data = data;
    }
});

app.controller('reportsController', ['$window', '$rootScope', '$scope', '$state', '$modal', '$filter', 'ajaxCall', 'service', 'messageService', function ($window, $rootScope, $scope, $state, $modal, $filter, ajaxCall, service, messageService) {
    $scope.data = {};
    $scope.bandwidths = _portal_static.bandwidths;
    $scope.dataLimits = _portal_static.dataLimits;
    $scope.singleLocationSelectorConfig = getSingleLocationSelectorConfig();

    $scope.sum = 0;

    $scope.open = function ($event) {
        $event.preventDefault();
        $event.stopPropagation();
    };

    $scope.dataUsages = [];

    $scope.getNasid = function () {
        return $scope.router.selected[0].nasid;
    }

    $scope.dates = {};

    $scope.formats = ['dd-MMMM-yyyy', 'yyyy/MM/dd', 'dd.MM.yyyy', 'shortDate'];
    $scope.format = $scope.formats[0];

    $scope.dates.userCount = {};
    $scope.dates.userCount.toDate = moment().subtract(1, 'days');
    $scope.dates.userCount.fromDate = moment().subtract(30, 'days');
    $scope.dates.userCount.completeFromDate = $scope.dates.userCount.fromDate.format(Constants.completeFloorDate);
    $scope.dates.userCount.completeToDate = $scope.dates.userCount.toDate.format(Constants.completeCielDate);
    $scope.dates.userCount.fromShowDate = $scope.dates.userCount.fromDate.format(Constants.dateFormat);
    $scope.dates.userCount.toShowDate = $scope.dates.userCount.toDate.format(Constants.dateFormat);

    $scope.dates.dataUsage = {};
    $scope.dates.dataUsage.toDate = moment().subtract(1, 'days');
    $scope.dates.dataUsage.fromDate = moment().subtract(30, 'days');
    $scope.dates.dataUsage.completeFromDate = $scope.dates.dataUsage.fromDate.format(Constants.completeFloorDate);
    $scope.dates.dataUsage.completeToDate = $scope.dates.dataUsage.toDate.format(Constants.completeCielDate);
    $scope.dates.dataUsage.fromShowDate = $scope.dates.dataUsage.fromDate.format(Constants.dateFormat);
    $scope.dates.dataUsage.toShowDate = $scope.dates.dataUsage.toDate.format(Constants.dateFormat);

    $scope.dates.detailedReport = {};
    $scope.dates.detailedReport.toDate = moment().subtract(1, 'days');
    $scope.dates.detailedReport.fromDate = moment().subtract(30, 'days');
    $scope.dates.detailedReport.completeFromDate = $scope.dates.dataUsage.fromDate.format(Constants.completeFloorDate);
    $scope.dates.detailedReport.completeToDate = $scope.dates.dataUsage.toDate.format(Constants.completeCielDate);
    $scope.dates.detailedReport.fromShowDate = $scope.dates.dataUsage.fromDate.format(Constants.dateFormat);
    $scope.dates.detailedReport.toShowDate = $scope.dates.dataUsage.toDate.format(Constants.dateFormat);

    /************ Deprectaed */
    //$scope.dnsReportdata = {};
    //$scope.dnsReportdata.dates = {};
    //$scope.dnsReportdata.dates.toDate = moment().subtract(1, 'days');
    //$scope.dnsReportdata.dates.fromDate = moment().subtract(7, 'days');
    //$scope.dnsReportdata.dates.completeFromDate = $scope.dnsReportdata.dates.fromDate.format(Constants.completeFloorDate);
    //$scope.dnsReportdata.dates.completeToDate = $scope.dnsReportdata.dates.toDate.format(Constants.completeCielDate);
    //$scope.dnsReportdata.dates.fromShowDate = $scope.dnsReportdata.dates.fromDate.format(Constants.dateFormat);
    //$scope.dnsReportdata.dates.toShowDate = $scope.dnsReportdata.dates.toDate.format(Constants.dateFormat);

    var processDatesForDataUsage = function (start, end) {
        $scope.dates.dataUsage.fromShowDate = start.format(Constants.dateFormat);
        $scope.dates.dataUsage.toShowDate = end.format(Constants.dateFormat);
        $scope.dates.dataUsage.fromDate = start;
        $scope.dates.dataUsage.toDate = end;
        $scope.fetchDataUsage();
    }

    var processDatesForUsersCount = function (start, end) {
        $scope.dates.userCount.fromShowDate = start.format(Constants.dateFormat);
        $scope.dates.userCount.toShowDate = end.format(Constants.dateFormat);
        $scope.dates.userCount.fromDate = start;
        $scope.dates.userCount.toDate = end;
        $scope.fetchUserCountReport();
    }

    $('body').on('focus', "#date-range-picker-for-data-usage", function () {
        $(this).daterangepicker({
            startDate: $scope.dates.dataUsage.fromDate,
            endDate: $scope.dates.dataUsage.toDate,
            linkedCalendars: false,
            maxDate: new Date(),
            ranges: {
                'Last 7 Days': [moment().subtract(7, 'days'), moment().subtract(0, 'days')],
                'Last 30 Days': [moment().subtract(30, 'days'), moment().subtract(0, 'days')],
                'This Month': [moment().startOf('month'), moment().endOf('month')],
                'Last Month': [moment().subtract(1, 'month').startOf('month'), moment().subtract(1, 'month').endOf('month')]
            },
            locale: {
                format: Constants.dateFormat
            },
            drops: 'right'
        }, processDatesForDataUsage);
    });

    $('body').on('focus', "#date-range-picker-for-users-count", function () {
        $(this).daterangepicker({
            startDate: $scope.dates.userCount.fromDate,
            endDate: $scope.dates.userCount.toDate,
            linkedCalendars: false,
            maxDate: new Date(),
            ranges: {
                'Last 7 Days': [moment().subtract(7, 'days'), moment().subtract(0, 'days')],
                'Last 30 Days': [moment().subtract(30, 'days'), moment().subtract(0, 'days')],
                'This Month': [moment().startOf('month'), moment().endOf('month')],
                'Last Month': [moment().subtract(1, 'month').startOf('month'), moment().subtract(1, 'month').endOf('month')]
            },
            locale: {
                format: Constants.dateFormat
            },
            drops: 'right'
        }, processDatesForUsersCount);
    });

    //-------------------

    if ($scope.$parent.routerDetails.length == 0) {
        $rootScope.loadLocationsForSelect();
    } else {
        if ($scope.$parent.routerDetails[0].nasid == _selectAllNas) {
            $scope.$parent.routerDetails.splice(0, 1);
        }
    }

    $scope.currentGroup = {
        selected: null
    };
    $scope.router = {
        selected: null
    }

    $scope.getAllReportsForStore = function () {
        if ($scope.location && $scope.location.length >= 1) {
            var nasid = $scope.location;
            var array = {};
            array.nasid = nasid;
            $scope.router.selected = [];
            $scope.router.selected.push(array);
            $scope.fetchDataUsage();
            $scope.fetchUserCountReport();
            $scope.fetchDetailedReport();
            //fetchDNSReports(); Deprecated
        }
    }

    $scope.fetchDataUsage = function () {
        if (!$rootScope.featureEnabled(14)) return;
        $scope.dataSum = 0;

        ajaxCall.get('/Admin/GetDataUsage?nasid=' + $scope.router.selected[0].nasid + '&startTime=' + $scope.dates.dataUsage.fromDate.toDate().yyyymmdd() +
            '&endTime=' + $scope.dates.dataUsage.toDate.toDate().yyyymmdd()).then(function (response) {
                $scope.dataUsages = response.data;
                for (var i = 0; i < $scope.dataUsages.length; ++i) {
                    sumDataUsage($scope.dataUsages[i].dataDownload + $scope.dataUsages[i].dataUpload);
                }

                ajaxCall.get('/Admin/GetBlockList?nasid=' + $scope.getNasid()).then(function (response) {
                    for (var i = 0; i < $scope.dataUsages.length; ++i) {
                        $scope.dataUsages[i].dataUsed = Math.floor(1000 * $scope.dataUsages[i].dataUsed / (1024 * 1024)) / 1000;
                        $scope.dataUsages[i].blocked = false;
                        for (var j = 0; j < response.data.length; ++j) {
                            if (response.data[j] === $scope.dataUsages[i].mobile) {
                                $scope.dataUsages[i].blocked = true;
                                break;
                            }
                        }
                    }
                });

                ajaxCall.get('/Admin/GetOnlineUsers?nasid=' + $scope.getNasid()).then(function (response) {
                    for (var i = 0; i < $scope.dataUsages.length; ++i) {
                        $scope.dataUsages[i].online = false;
                        for (var j = 0; j < response.data.length; ++j) {
                            if (response.data[j].mobile === $scope.dataUsages[i].mobile) {
                                $scope.dataUsages[i].online = true;
                                $scope.dataUsages[i].sessionId = response.data[j].sessionId;
                                break;
                            }
                        }
                    }
                });

            });
    }

    $scope.fetchDataUsageReportForUser = function (dataUsageObject) {
        if (!($rootScope.featureEnabled(39) || $rootScope.featureEnabled(14))) return;
        var nasids = [];
        for (var i = 0; i < $scope.router.selected.length; ++i)
            nasids.push(parseInt($scope.router.selected[i].nasid));
        var obj = {
            nasids: nasids
        }
        if (dataUsageObject) dataUsageObject.nasids = nasids;
        var selecetedNases = [];

        for (var i = 0; i < $scope.router.selected.length; ++i) {
            var selectedNasId = $scope.router.selected[i]['nasid'];
            var nas = getObjectFromArray($scope.$parent.routerDetails, 'nasid', selectedNasId);
            selecetedNases.push(nas);
        }

        ajaxCall.post('/Admin/GetDetailedReport?startTime=' + $scope.dates.dataUsage.fromDate.toDate().yyyymmdd()
                    + '&endTime=' + $scope.dates.dataUsage.toDate.toDate().yyyymmdd(), (dataUsageObject ? dataUsageObject : obj))
            .then(function (response) {
                if (dataUsageObject) {
                    userMobileDataUsages = response.data;
                    $scope.detailedMobileDataUsages = sort(userMobileDataUsages, 'sessionStart');
                    $('.mobile-detailed-report').removeClass('display-none');
                } else {
                    var userDataObject = response.data;
                    for (var i = 0; i < userDataObject.length; ++i) {
                        if (userDataObject[i].hasOwnProperty('nasid') && userDataObject[i].nasid) {
                            var nas = getObjectFromArray(selecetedNases, 'nasid', userDataObject[i].nasid);
                            userDataObject[i].locationName = nas.storeName;
                            userDataObject[i].city = nas.city;
                            $scope.detailedSum += $scope.convertToMB(userDataObject[i].dataDownload + userDataObject[i].dataUpload);
                            $scope.detailedSum = Math.floor($scope.detailedSum * 100) / 100;
                        }
                    }
                    $scope.detailedDataUsages = sort(userDataObject, 'sessionStart');;
                }
            });
    }

    var sumDataUsage = function (val) {
        val = $scope.convertToMB(val);
        $scope.dataSum += val;
        $scope.dataSum = Math.floor($scope.dataSum * 100) / 100;
    }

    $scope.convertToMB = function (val) {
        return Math.floor(1000 * val / (1024 * 1024)) / 1000;
    }

    $scope.blockNumber = function (obj) {
        ajaxCall.post('/Client/BlockNumber?nasid=' + $scope.getNasid() + '&mKey=' + encodeURIComponent(obj) + '&block=1').then(function () {
            messageService.showSuccess('Number blocked successfully');
            $scope.fetchDataUsage();
        });
    }

    $scope.unblockNumber = function (obj) {
        ajaxCall.post('/Client/BlockNumber?nasid=' + $scope.getNasid() + '&mKey=' + encodeURIComponent(obj) + '&block=0').then(function () {
            messageService.showSuccess('Number unblocked successfully');
            $scope.fetchDataUsage();
        });
    }

    $scope.logoutUser = function (obj) {
        ajaxCall.post('/Admin/LogoutUser?nasid=' + $scope.getNasid(), obj).then(function () {
            messageService.showSuccess('User will be logged out in few minutes.');
            $scope.fetchDataUsage();
        });
    }

    //$scope.exportDataUsage = function (evt) {
    //    var content = ["Mobile,Email,Data Used,Days Logged In(In Days)"];
    //    angular.forEach($scope.dataUsages, function (obj) {
    //        content.push(obj.mobile + ',' + obj.email + ',' + $scope.convertToMB(obj.dataUpload + obj.dataDownload) + ' MB,' + obj.days);
    //    })

    //    var a = document.createElement('a');
    //    a.href = 'data:attachment/csv,' + encodeURIComponent(content.join("\r\n"));
    //    a.target = '_blank';
    //    a.download = 'report.csv';
    //    document.body.appendChild(a);
    //    a.click();
    //}

    $scope.exportDataUsage = function (evt) {
        var content = ["Mobile,Email,Data Used,Days Logged In(In Days)"];
        angular.forEach($scope.dataUsages, function (obj) {
            content.push(obj.mobile + ',' + obj.email + ',' + $scope.convertToMB(obj.dataUpload + obj.dataDownload) + ' MB,' + obj.days );
        })

        var blob = new Blob([content.join("\r\n")], {
            type: 'text/csv'
        });
        var filename = 'report.csv';
        if (window.navigator.msSaveOrOpenBlob) {
            window.navigator.msSaveBlob(blob, filename);
        } else {
            var elem = window.document.createElement('a');
            elem.href = window.URL.createObjectURL(blob);
            elem.download = filename;
            document.body.appendChild(elem);
            elem.click();
            document.body.removeChild(elem);
        }
    }

    /***************** REPORTS *******************/

    $scope.toggleMin = function () {
        $scope.minDate = $scope.minDate ? null : new Date();
    };

    $scope.toggleMin();

    $scope.dateOptions = {
        formatYear: 'yy',
        startingDay: 1
    };

    $scope.chart3 = "1";
    $scope.chart4 = "1";

    var userArray = [],
        loginArray = [],
        loginData = {},
        windowSize;

    var chartArray = _portal_static.report_chartArray;

    /*$scope.changeChart = function (value) {
        var login_line = chartArray["login_line"];
        for (var i = login_line.length - 1; i >= 0; i--) {
            var args = [login_line[i]["id"], loginArray, '', ['Date', 'Logins', {
                role: 'style'
            }], windowSize, login_line[i]["width"], login_line[i]["height"]];
            if (value == 1) {
                drawLineChart.apply(this, args);
            } else {
                drawChart.apply(this, args);
            }
        };

        var user_line = chartArray["user_line"];
        for (var x = 0; x < userArray.length; x++) {
            userArray[x][0] = userArray[x][0].split("/").splice(0, 2).join("/");
        }
        for (var i = user_line.length - 1; i >= 0; i--) {
            args = [user_line[i]["id"], userArray, '', ['Date', 'Users', {
                role: 'style'
            }], windowSize, user_line[i]["width"], user_line[i]["height"]];

            if (value == 1) {
                drawLineChart.apply(this, args);
            } else
                drawChart.apply(this, args);
        };

    }*/

    $scope.changeChart = function (value) {
        var login_line = chartArray["login_line"];
        //setTimeout(function () {
            var args = [login_line["id"], loginArray, '', ['Date', 'Logins', {
                role: 'style'
            }], windowSize, login_line["width"], login_line["height"]];
            if (value == 1) {
                drawLineChart.apply(this, args);
            } else {
                drawChart.apply(this, args);
            }
        //}, 3000);
        

        var user_line = chartArray["user_line"];
        //setTimeout(function () {
            for (var x = 0; x < userArray.length; x++) {
                userArray[x][0] = userArray[x][0].split("/").splice(0, 2).join("/");
            }

            args = [user_line["id"], userArray, '', ['Date', 'Users', {
                role: 'style'
            }], windowSize, user_line["width"], user_line["height"]];

            if (value == 1) {
                drawLineChart.apply(this, args);
            } else {
                drawChart.apply(this, args);
            }

            console.log(JSON.stringify(args));
        //}, 3000);
    }

    $scope.addGroupBy = function (groupBy) {
        loginArray = [];
        userArray = [];
        windowSize = 0;
        angular.forEach(loginData, function (obj) {
            var date = new Date(obj.date);
            var style = 'color: #78BFB9';
            if (date.getDay() === 6 || date.getDay() === 0)
                style = 'color: #FF0000';
            if (windowSize < obj.count)
                windowSize = obj.count;
            loginArray.push([obj.date, obj.count, style]);
            userArray.push([obj.date, obj.userCount, style]);
        })
        windowSize += 2;
    }

    $scope.fetchUserCountReport = function () {
        if (!$rootScope.featureEnabled(39)) return;
        var routers = [];
        $scope.bandwidth = [];
        angular.forEach($rootScope.routerDetails, function (v, k) {
            if (v["nasid"] && routers.indexOf(v.nasid) < 0) {
                routers.push(v.nasid);
            }
        });

        var response = $rootScope.routerDetails;
        var date = new Date();
        var finalresponse = [];

        angular.forEach(response, function (value, key) {
            if (value.nasid && routers.indexOf(value.nasid) > -1) {
                finalresponse.push(value);
            }
        });

        ajaxCall.get('/Admin/GetUsersInPastHour').then(function (response) {
            var data = response["data"];
            var pastHorsUserObject = getObjectFromArray(data, "Key", $scope.router.selected[0].nasid);
            if (pastHorsUserObject)
                $scope.usersPastHour = pastHorsUserObject.Value;
        });

        ajaxCall.get('/Admin/GetUsersInPastDay').then(function (response) {
            var data = response["data"];
            var pastDayUserObject = getObjectFromArray(data, "Key", $scope.router.selected[0].nasid)
            if (pastDayUserObject)
                $scope.usersPastDay = pastDayUserObject.Value;
        });

        ajaxCall.get('/Admin/GetUsersInPastMonth?nasid=' + $scope.router.selected[0].nasid + '&startTime=' + $scope.dates.userCount.fromDate.toDate().yyyymmdd()).then(function (response) {
            var data = response["data"];
            $scope.usersPastMonth = parseInt(data[$scope.router.selected[0].nasid]);
        });

        ajaxCall.get('/Admin/GetUsersInThisDuration?nasid=' + $scope.router.selected[0].nasid + '&startTime=' + $scope.dates.userCount.fromDate.toDate().yyyymmdd() +
            '&endTime=' + $scope.dates.userCount.toDate.toDate().yyyymmdd()).then(function (response) {
                var data = response["data"];
                $scope.usersThisDuration = parseInt(data.length);
            });

        ajaxCall.get('/Admin/GetStats?nasid=' + $scope.router.selected[0].nasid + '&startTime=' + $scope.dates.userCount.fromDate.toDate().yyyymmdd() +
            '&endTime=' + $scope.dates.userCount.toDate.toDate().yyyymmdd()).then(function (response) {
                $scope.logs = [];
                var temp = {};
                var hourData = {},
                    hourArray = [],
                    userData = {},
                    userArray = [];
                angular.forEach(response.data, function (obj) {
                    obj.hour += 5;
                    if (obj.hour > 23)
                        obj.hour -= 24;
                    if (temp[obj.mobile]) {
                        temp[obj.mobile].device++;
                        temp[obj.mobile].count += obj.count;
                    } else {
                        temp[obj.mobile] = {
                            mobile: obj.mobile,
                            device: 1,
                            count: obj.count
                        }
                    }
                    if (hourData[obj.hour]) {
                        hourData[obj.hour].count += obj.count;
                    } else {
                        hourData[obj.hour] = {
                            count: obj.count,
                            hour: obj.hour
                        }
                    }
                    if (userData[obj.hour + obj.mobile]) {
                        if (obj.day !== userData[obj.hour + obj.mobile].day)
                            userData[obj.hour + obj.mobile].count++;
                    } else {
                        userData[obj.hour + obj.mobile] = {
                            count: 1,
                            hour: obj.hour,
                            mobile: obj.mobile,
                            day: obj.day
                        }
                    }
                });
                angular.forEach(temp, function (obj) {
                    $scope.logs.push(obj);
                });
                var windowSize = 0;
                angular.forEach(hourData, function (obj) {
                    if (windowSize < obj.count)
                        windowSize = obj.count;
                    hourArray.push([obj.hour, obj.count, 'color: #76A7FA']);
                });
                var userArrayMerged = {};
                angular.forEach(userData, function (obj) {
                    if (userArrayMerged[obj.hour])
                        userArrayMerged[obj.hour].count += obj.count;
                    else
                        userArrayMerged[obj.hour] = {
                            hour: obj.hour,
                            count: obj.count
                        };
                });
                angular.forEach(userArrayMerged, function (obj) {
                    if (windowSize < obj.count)
                        windowSize = obj.count;
                    userArray.push([obj.hour, obj.count, 'color: #76A7FA']);
                });
                windowSize += 2;
                sort(hourArray, 0);
                sort(userArray, 0);
                angular.forEach(hourArray, function (obj) {
                    obj[0] = mapTime(obj[0]);
                });
                angular.forEach(userArray, function (obj) {
                    obj[0] = mapTime(obj[0]);
                });

                /*setTimeout(function () {
                    //Users Login Distribution
                    var logins = chartArray["login"];
                    for (var i = logins.length - 1; i >= 0; i--) {
                        var args = [logins[i]["id"], hourArray, '', ['Login Hour', 'Logins', {
                            role: 'style'
                        }], windowSize, logins[i]["width"], logins[i]["height"]];
                        console.log(logins[i]["id"]);
                        console.log(args);
                        drawChart.apply(this, args);
                    }
                    
                }, 3000);*/

                setTimeout(function () {
                    //Users Login Distribution
                    var logins = chartArray["login"];
                    var args = [logins["id"], hourArray, '', ['Login Hour', 'Logins', {
                        role: 'style'
                    }], windowSize, logins["width"], logins["height"]];
                    drawChart.apply(this, args);
                }, 3000);

                /*setTimeout(function () {
                    //Users Distribution
                    var users = chartArray["user"]
                    for (var i = users.length - 1; i >= 0; i--) {
                        args = [users[i]["id"], userArray, '', ['Login Hour', 'Users', {
                            role: 'style'
                        }], windowSize, users[i]["width"], users[i]["height"]];
                        console.log(users[i]["id"]);
                        console.log(args);
                        drawChart.apply(this, args);
                    }

                }, 3000);*/

                setTimeout(function () {
                    //Users Distribution
                    var users = chartArray["user"]
                    args = [users["id"], userArray, '', ['Login Hour', 'Users', {
                        role: 'style'
                    }], windowSize, users["width"], users["height"]];
                    drawChart.apply(this, args);
                }, 3000);

            });

        ajaxCall.get('/Admin/GetStatsCompressed?nasid=' + $scope.router.selected[0].nasid + '&startTime=' + $scope.dates.userCount.fromDate.toDate().yyyymmdd() +
            '&endTime=' + $scope.dates.userCount.toDate.toDate().yyyymmdd()).then(function (response) {
               
                    loginData = {};
                    angular.forEach(response.data, function (obj) {
                        var date = parseInt(obj.key);
                        if (loginData[date]) {
                            loginData[date].count += obj.count;
                            loginData[date].userCount++;
                        } else {
                            loginData[date] = {
                                date: date,
                                count: obj.count,
                                userCount: 1
                            }
                        }
                    });
                    $scope.addGroupBy();
                    sort(loginArray, 0);
                    sort(userArray, 0);
                    angular.forEach(loginArray, function (obj) {
                        obj[0] = new Date(obj[0]).ddmmyyyy();
                    });
                    angular.forEach(userArray, function (obj) {
                        obj[0] = new Date(obj[0]).ddmmyyyy();
                    });
                    setTimeout(function () {
                        $scope.changeChart($scope.chart3);
                    },3000);
                    
                
            });

        ajaxCall.get('/Client/GetBandwidthReport?nasid=' + $scope.router.selected[0].nasid + '&startTime=' + $scope.dates.userCount.fromDate.toDate().yyyymmdd() +
            '&endTime=' + $scope.dates.userCount.toDate.toDate().yyyymmdd()).then(function (response) {
                if (response.status == 0) {
                    $scope.bandwidth = [];
                    for (var i = 0; i < response.data.length; ++i) {
                        $scope.bandwidth[i] = {
                            download_bw: 0,
                            timestamp: ''
                        };
                        $scope.bandwidth[i].download_bw = response.data[i].download_bw;
                        $scope.bandwidth[i].timestamp = response.data[i].timestamp;
                    }
                }
            });
    }

    $scope.exportUserCountCSV = function (evt) {
        var numbers = 'Mobile Numbers\n';
        angular.forEach($scope.logs, function (obj) {
            numbers += obj.mobile + "\n";
        })
        $(evt.target).attr('href', 'data:text/html;charset=utf-8,' + encodeURIComponent(numbers));
    }

    $scope.exportDailyCSVReport = function (evt) {
        var csvdata = 'NAS,Event,Count,Date\r\n';
        var csvContent;
        $.ajax({
            url: "/Admin/GenerateDailyReport?userid=" + $rootScope.LoggedInUserId,
            context: document.body,
            success: function (data) {
                csvdata += data;
                var element = document.createElement('a');
                element.setAttribute('href', 'data:text/plain;charset=utf-8,' + encodeURIComponent(csvdata));
                element.setAttribute('download', "report.csv");

                element.style.display = 'none';
                document.body.appendChild(element);

                element.click();

                document.body.removeChild(element);
         
            }
        });
    }

    $scope.exportUserLoginCSVReport = function (evt) {
        var data = 'Date,Login Count,User Count\r\n';
        for (var property in loginData) {
            if (loginData.hasOwnProperty(property)) {
                if (property)
                    data += new Date(parseInt(property)).ddmmyyyy() + ',' + loginData[property].count + ',' + loginData[property].userCount + "\r\n";
            }
        }
        $(evt.target).attr('href', 'data:text/html;charset=utf-8,' + encodeURIComponent(data));
    }

    /************************************* DNS Reports Deprected *********************************/
    //var processDatesForDNSReport = function (start, end) {
    //    $scope.dnsReportdata.dates.fromShowDate = start.format(Constants.dateFormat);
    //    $scope.dnsReportdata.dates.toShowDate = end.format(Constants.dateFormat);
    //    $scope.dnsReportdata.dates.fromDate = start;
    //    $scope.dnsReportdata.dates.toDate = end;
    //    fetchDNSReports();
    //}
    //
    //$('body').on('focus', "#date-range-picker-for-dns-report", function () {
    //    $(this).daterangepicker({
    //        startDate: $scope.dnsReportdata.dates.fromDate,
    //        endDate: $scope.dnsReportdata.dates.toDate,
    //        linkedCalendars: false,
    //        maxDate: new Date(),
    //        ranges: {
    //            'Last 7 Days': [moment().subtract(7, 'days'), moment().subtract(0, 'days')],
    //            'Last 30 Days': [moment().subtract(30, 'days'), moment().subtract(0, 'days')],
    //            'This Month': [moment().startOf('month'), moment().endOf('month')],
    //            'Last Month': [moment().subtract(1, 'month').startOf('month'), moment().subtract(1, 'month').endOf('month')]
    //        },
    //        locale: {
    //            format: Constants.dateFormat
    //        },
    //        drops: 'right'
    //    }, processDatesForDNSReport);
    //});
    //
    //var fetchDNSReports = function () {
    //    if (!$rootScope.featureEnabled(27)) return;
    //
    //    ajaxCall.get('/Client/FetchDNSReports?nasid=' + $scope.router.selected[0].nasid + '&startTime=' + $scope.dnsReportdata.dates.fromDate.toDate().yyyymmdd() +
    //        '&endTime=' + $scope.dnsReportdata.dates.toDate.toDate().yyyymmdd()).then(function (response) {
    //            $scope.dnsReportdata.dnsReports = response.data;
    //        });
    //}
    //
    //$scope.exportDNSReport = function (evt) {
    //    var data = ['Username,Domain,Time\r\n'];
    //    angular.forEach($scope.dnsReportdata.dnsReports, function (report) {
    //        data.push((report.username ? report.username : '-') + ',' +
    //            (report.domain ? report.domain : '-') + ',' +
    //            (report.time ? moment.utc(report.time).local().format(Constants.competeDateFormat) : '-'));
    //    });
    //
    //    var a = document.createElement('a');
    //    a.href = 'data:attachment/csv,' + encodeURIComponent(data.join("\r\n"));
    //    a.target = '_blank';
    //    a.download = 'dns_report.csv';
    //    document.body.appendChild(a);
    //    a.click();
    //}    

    /************************************* Detailed Reports *************************************/

    $scope.detailedDataUsages = [];
    $scope.detailedSum = 0;

    var processDatesForDetailedReport = function (start, end) {
        $scope.dates.detailedReport.fromShowDate = start.format(Constants.dateFormat);
        $scope.dates.detailedReport.toShowDate = end.format(Constants.dateFormat);
        $scope.dates.detailedReport.fromDate = start;
        $scope.dates.detailedReport.toDate = end;
        $scope.fetchDetailedReport();
    }

    $('body').on('focus', "#date-range-picker-for-detailed-data-usage", function () {
        $(this).daterangepicker({
            startDate: $scope.dates.fromDate,
            endDate: $scope.dates.toDate,
            linkedCalendars: false,
            maxDate: new Date(),
            ranges: {
                'Last 7 Days': [moment().subtract(7, 'days'), moment().subtract(0, 'days')],
                'Last 30 Days': [moment().subtract(30, 'days'), moment().subtract(0, 'days')],
                'This Month': [moment().startOf('month'), moment().endOf('month')],
                'Last Month': [moment().subtract(1, 'month').startOf('month'), moment().subtract(1, 'month').endOf('month')]
            },
            locale: {
                format: Constants.dateFormat
            },
            drops: 'right'
        }, processDatesForDetailedReport);
    });

    $scope.removeMobileDetailedReport = function () {
        $('.mobile-detailed-report').addClass('display-none');
    }

    $scope.fetchDetailedReport = function (dataUsageObject) {
        if (!$rootScope.featureEnabled(39)) return;
        var nasids = [];
        for (var i = 0; i < $scope.router.selected.length; ++i)
            nasids.push(parseInt($scope.router.selected[i].nasid));
        var obj = {
            nasids: nasids
        }
        if (dataUsageObject) dataUsageObject.nasids = nasids;
        var selecetedNases = [];

        for (var i = 0; i < $scope.router.selected.length; ++i) {
            var selectedNasId = $scope.router.selected[i]['nasid'];
            var nas = getObjectFromArray($scope.$parent.routerDetails, 'nasid', selectedNasId);
            selecetedNases.push(nas);
        }

        ajaxCall.post('/Admin/GetDetailedReport?startTime=' + $scope.dates.detailedReport.fromDate.toDate().yyyymmdd()
                    + '&endTime=' + $scope.dates.detailedReport.toDate.toDate().yyyymmdd(), (dataUsageObject ? dataUsageObject : obj))
            .then(function (response) {
                if (dataUsageObject) {
                    userMobileDataUsages = response.data;
                    $scope.detailedMobileDataUsages = sort(userMobileDataUsages, 'sessionStart');
                    $('.mobile-detailed-report').removeClass('display-none');
                } else {
                    var userDataObject = response.data;
                    for (var i = 0; i < userDataObject.length; ++i) {
                        if (userDataObject[i].hasOwnProperty('nasid') && userDataObject[i].nasid) {
                            var nas = getObjectFromArray(selecetedNases, 'nasid', userDataObject[i].nasid);
                            userDataObject[i].locationName = nas.storeName;
                            userDataObject[i].city = nas.city;
                            $scope.detailedSum += $scope.convertToMB(userDataObject[i].dataDownload + userDataObject[i].dataUpload);;
                            $scope.detailedSum = Math.floor($scope.detailedSum * 100) / 100;
                        }
                    }
                    $scope.detailedDataUsages = sort(userDataObject, 'sessionStart');;
                }
            });
    }

    $scope.exportDetailedDataUsage = function (evt) {
        var content = ["Nasid,Mobile,Device MAC,Login Time,Logout Time,Data Upload,Data Download,Location Name,City"];
        angular.forEach($scope.detailedDataUsages, function (used) {
            content.push(used.nasid + ',' + used.mobile + ',' + used.macId
            + ',' + used.sessionStart + ',' + used.sessionEnd + ',' + $scope.convertToMB(used.dataUpload) + ' MB,'
            + $scope.convertToMB(used.dataDownload) + ' MB,"' + used.locationName + '",' + used.city);
        })

        content = content.join("\r\n");
        //var a = document.createElement('a');
        //a.href = 'data:attachment/csv,' + encodeURIComponent(content.join("\r\n"));
        //a.target = '_blank';
        //a.download = 'report.csv';
        //document.body.appendChild(a);
        //a.click();
        var blob = new Blob([content], {
            type: 'text/csv'
        });
        var filename = 'report.csv';
        if (window.navigator.msSaveOrOpenBlob) {
            window.navigator.msSaveBlob(blob, filename);
        } else {
            var elem = window.document.createElement('a');
            elem.href = window.URL.createObjectURL(blob);
            elem.download = filename;
            document.body.appendChild(elem);
            elem.click();
            document.body.removeChild(elem);
        }
    }

    $scope.pageSize = 500;

    $scope.filter = {};

    $scope.storeList = [];
    $scope.data = $scope.data || {};
    $scope.data.infiniteScrollDisabled = false;
    $scope.data.showClientForm = false;

    var getLocationForAdmin = function (qObj) {
        $scope.userBrand.then(function () {
            $scope.groupOfNases = '';
            //qObj.installedState = $scope.selectedInsState ? $scope.selectedInsState : 1;
            qObj.installedState = $scope.selectedInsState;
            qObj.city = $scope.selectedCity;
            qObj.category = $scope.selectedCategory;
            //qObj.partnerCode = $scope.selected_PartnerCode;
            qObj.partnerId = $scope.routerFilters.selected_PartnerId;
            qObj.partnerAccountType = $scope.routerFilters.selected_PartnerAccountType;
            qObj.storeTags = "%";
            if (!isNaN($scope.searchTerm)) {
                qObj.nasid = $scope.searchTerm;
            } else {
                qObj.storeName = qObj.storeNameAlias = qObj.address = qObj.state = $scope.searchTerm;
            }
            //console.log('loading page ' + qObj.pageNumber);
            service.getLocationsForAdmin(qObj).then(function (locations) {
                angular.forEach(locations, function (value, index) {
                    if ($rootScope.username === '<EMAIL>' || $rootScope.name === '<EMAIL>') {
                        service.demofizeStores(value)
                    }
                    value.lastUsed = '<img src="../images/ellipsis.gif">';
                    value.inLast24Hours = '<img src="../images/ellipsis.gif">';

                    if (value.category == '')
                        value.category = "N/A"
                    else if (value.category.indexOf(',') > -1)
                        value.category = value.category.split(',')[0];

                    value.routerState = value.routerState.toString();

                    if (value.routerState != "2") {
                        if (value.lastPingDelay < 0)
                            value.lastPingDelay = -value.lastPingDelay;
                        if (value.lastPingDelay > 35) value.downSinceHalfHour = true;
                        value.lastPingDelay = roundOffTime(value.lastPingDelay * 60);
                    }

                    $scope.storeList.push(value);

                    if (index < locations.length - 1) {
                        $scope.groupOfNases += value.nasid + ','
                    } else {
                        $scope.groupOfNases += value.nasid;
                    }
                });
                //console.log('loaded page ' + qObj.pageNumber);
                if (locations.length < $scope.pageSize)
                    $scope.data.infiniteScrollDisabled = true;
                else
                    $scope.data.infiniteScrollDisabled = false;
                $scope.$broadcast('params-updated');
                $('.selectize-input input').attr('placeholder', 'Filter ' + $scope.storeList.length + ' Search Results');
            });
        });
    }

    $scope.filterRouters = function (options) {
        if ($scope.searchTerm.length == 0)
            $('.selectize-input input').attr('placeholder', 'No Search Query. No Search results to filter.');
        options = options || {};
        var queryObjectCopy = angular.copy(queryObject);
        queryObjectCopy.pageNumber = $scope.pageNumber = 1;
        queryObjectCopy.pageSize = 500;
        $scope.storeList = [];
        getLocationForAdmin(queryObjectCopy);
        options.stats && getDevicesStats(queryObjectCopy);
        if (queryObjectCopy.hasOwnProperty('partnerId') && typeof queryObjectCopy.partnerId != "undefined") {
            service.setUserBrand({ partnerId: queryObjectCopy.partnerId });
        } else if (queryObjectCopy.hasOwnProperty('partnerId') && queryObjectCopy.partnerId == "undefined") {
            service.setUserBrand({ partnerId: 0 });
        } else {
            service.setUserBrand({ partnerId: 0 });
        }
        options.scrollTo && $rootScope.scrollTo(options.scrollTo);
    }
}]);

app.controller('smsAnalyticsController', ['$window', '$rootScope', '$scope', '$state', '$modal', '$filter', 'ajaxCall', 'service', 'messageService', function ($window, $rootScope, $scope, $state, $modal, $filter, ajaxCall, service, messageService) {
    $scope.data = {};
    $scope.smsImpactReport = null;
    $scope.open = function ($event) {
        $event.preventDefault();
        $event.stopPropagation();
    };

    $scope.dates = {};
    $scope.dates.fromDate = moment().subtract(30, 'days');
    $scope.dates.toDate = moment().subtract(0, 'days');
    $scope.formats = ['dd-MMMM-yyyy', 'yyyy/MM/dd', 'dd.MM.yyyy', 'shortDate'];
    $scope.format = $scope.formats[0];
    $scope.dates.completeFromDate = $scope.dates.fromDate.format(Constants.completeFloorDate);
    $scope.dates.completeToDate = $scope.dates.toDate.format(Constants.completeCielDate);
    $scope.dates.fromShowDate = $scope.dates.fromDate.format(Constants.dateFormat);
    $scope.dates.toShowDate = $scope.dates.toDate.format(Constants.dateFormat);

    var processDates = function (start, end) {
        $scope.dates.fromShowDate = start.format(Constants.dateFormat);
        $scope.dates.toShowDate = end.format(Constants.dateFormat);
        $scope.dates.fromDate = start;
        $scope.dates.toDate = end;
        $scope.getSmsOverview();
    }

    $('body').on('focus', "#date-range-picker-for-report", function () {
        $(this).daterangepicker({
            startDate: $scope.dates.fromDate,
            endDate: $scope.dates.toDate,
            linkedCalendars: false,
            maxDate: new Date(),
            ranges: {
                'Last 7 Days': [moment().subtract(7, 'days'), moment().subtract(1, 'days')],
                'Last 30 Days': [moment().subtract(30, 'days'), moment().subtract(1, 'days')],
                'This Month': [moment().startOf('month'), moment().endOf('month')],
                'Last Month': [moment().subtract(1, 'month').startOf('month'), moment().subtract(1, 'month').endOf('month')]
            },
            locale: {
                format: Constants.dateFormat
            },
            drops: 'right'
        }, processDates);
    });
    //-------------------positiveNases
    if ($scope.$parent.positiveNases.length == 0) {
        $rootScope.loadLocationsForSelect();
        if ($scope.$parent.positiveNases.length > 0 && $scope.$parent.positiveNases[0].storeName != _selectAllObj.storeName)
            $scope.$parent.positiveNases.insert(0, _selectAllObj);
        else
            $scope.$parent.positiveNases.push(_selectAllObj);

    } else {
        if ($scope.$parent.positiveNases.length > 0 && $scope.$parent.positiveNases[0].storeName != _selectAllObj.storeName)
            $scope.$parent.positiveNases.insert(0, _selectAllObj);
        else
            $scope.$parent.positiveNases.push(_selectAllObj);
    }

    $scope.currentGroup = {
        selected: null
    };
    $scope.router = {
        selected: null
    }
    $scope.location = {};
    $scope.getSmsOverview = function () {
        if ($scope.location.selected && $scope.location.selected.length >= 1) {
            var nasLocations = [];
            $scope.nasIds = [];
            if ($scope.location.selected[$scope.location.selected.length - 1].nasid == _selectAllNas) {
                angular.forEach($scope.$parent.positiveNases, function (location, index) {
                    if ($scope.nasIds.indexOf(location.nasid) == -1 && location.nasid != _selectAllNas) {
                        nasLocations.push(location);
                        $scope.nasIds.push(location.nasid);
                    }
                });
                $scope.location.selected = nasLocations;
            } else {
                angular.forEach($scope.location.selected, function (location, index) {
                    if (location && location.nasid) {
                        $scope.nasIds.push(location.nasid);
                    }
                });
            }
            
            $scope.smsImpactReport = null;
            $scope.fetchSmsOverview();
        }
    }

    $scope.fetchSmsOverview = function () {
        if ($scope.nasIds && $scope.nasIds.length > 0) {
            ajaxCall.post('/Client/GetSmsOverview?startTime=' + $scope.dates.fromDate.toDate().yyyymmdd() +
                '&endTime=' + $scope.dates.toDate.toDate().yyyymmdd(), { nasids: $scope.nasIds }).then(function (response) {
                    $scope.smsOverview = response.data;
                });
        }
    }


    $scope.fetchSmsImpactReport = function () {
        if ($scope.nasIds && $scope.nasIds.length > 0) {
            ajaxCall.post('/Client/GetSmsImpactReport?startTime=' + $scope.dates.fromDate.toDate().yyyymmdd() +
                '&endTime=' + $scope.dates.toDate.toDate().yyyymmdd(), { nasids: $scope.nasIds }).then(function (response) {
                    $scope.smsImpactReport = response.data;
                });
        }
    }

    $scope.$watch("$parent.positiveNases.length", function (newValue, oldValue) {
        if (newValue != oldValue || newValue > 1) {
            $scope.location.selected = [];
            $scope.location.selected.insert(0, $scope.$parent.positiveNases[1]);
            $scope.getSmsOverview();
        }
    });

    $scope.formatNumber = function  (number) {
        var x1 = number || this;
        var rgx = /(\d+)(\d{3})/;
        while (rgx.test(x1)) {
            x1 = x1.toString().replace(rgx, '$1' + ',' + '$2');
        }
        x1 += '';
        return x1 ? x1 : 0;
    };

}]);

app.controller('primeSmsController', function ($scope, ajaxCall, $rootScope, service, messageService, $modal) {

    $scope.users = [];
    $scope.user = {
        selected: null
    }

    $scope.segments = ['', 'Loyal', 'New', 'Attritor'];
    $scope.categories = ['', 'Offer', 'Greeting'];

    //$scope.primalTemplates = primalTemplates;
    $scope.primeSmsConfigue = {};

    $scope.primeSmsDisabled = false;

    

    service.getSMSStaticDetails().then(function (response) {
        $scope.usersStaticDetails = response.data;
        $scope.userBrand.then(function () {
            if (!$scope.routerFilters.selected_PartnerId || $scope.routerFilters.selected_PartnerId == 0) {
                $scope.primeSmsDisabled = true;
                $scope.primeSmsWarning = 'You will need to select a brand for enabling this feature.';
            } else if (response.data.smses_left == 0) {
                $scope.primeSmsDisabled = true;
                $scope.primeSmsWarning = 'You have insufficient SMS credits in your account. Please contact i2e1 at +91-**********.';
            }
        });
    });

    $scope.getPrimalTemplates = function () {
        var data = {
            username: $rootScope.username
        }
        ajaxCall.post('/Admin/GetPrimalTemplates', data).then(function (response) {

            angular.forEach($scope.comms, function (value, key) {
                $scope.primeSmsTemplates[value["id"]] = value["content"];
            });

            $scope.primeSmsTemplates = response.data.data;
            $scope.template_log = {};
            $scope.comms = [];
            var customeTplObj = {
                "id": "custom",
                "value": "Custom Message",
                "isCustom": 1
            };
            if ($scope.primeSmsTemplates) {
                var p = [];

                var loyalSeg = $scope.primeSmsTemplates.filter(function (item) {
                    return item.segment == 1;
                });
                var newSeg = $scope.primeSmsTemplates.filter(function (item) {
                    return item.segment == 2;
                });
                var attriterSeg = $scope.primeSmsTemplates.filter(function (item) {
                    return item.segment == 3;
                });

                if (loyalSeg.length > 0) {
                    var offer = $scope.primeSmsTemplates.filter(function (item) {
                        return item.segment == 1 && item.category == 1;
                    });
                    var greetings = $scope.primeSmsTemplates.filter(function (item) {
                        return item.segment == 1 && item.category == 2;
                    });
                    c = [];
                    if (offer.length > 0) {
                        var loyalOfferTpl = $scope.primeSmsTemplates.filter(function (item) {
                            return item.segment == 1 && item.category == 1;
                        });
                        t = [];
                        angular.forEach(loyalOfferTpl, function (template) {
                            t.push({
                                id: template.id,
                                value: template.content,
                                isCustom: template.isCustom
                            })
                        });
                        t.push(customeTplObj);
                        c.push({
                            name: 'Offer',
                            value: 1,
                            templates: t
                        })
                    }
                    if (greetings.length > 0) {
                        var loyalGreetingTpl = $scope.primeSmsTemplates.filter(function (item) {
                            return item.segment == 1 && item.category == 2;
                        });
                        t = [];
                        angular.forEach(loyalGreetingTpl, function (template) {
                            t.push({
                                id: template.id,
                                value: template.content,
                                isCustom: template.isCustom
                            })
                        });
                        t.push(customeTplObj);
                        c.push({
                            name: 'Greeting',
                            value: 2,
                            templates: t
                        })
                    }
                    p.push({
                        name: 'Loyal',
                        value: 1,
                        categories: c
                    });
                }
                if (newSeg.length > 0) {
                    var offer = $scope.primeSmsTemplates.filter(function (item) {
                        return item.segment == 2 && item.category == 1;
                    });
                    var greetings = $scope.primeSmsTemplates.filter(function (item) {
                        return item.segment == 2 && item.category == 2;
                    });
                    c = [];
                    if (offer.length > 0) {
                        var newOfferTpl = $scope.primeSmsTemplates.filter(function (item) {
                            return item.segment == 2 && item.category == 1;
                        });
                        t = [];
                        angular.forEach(newOfferTpl, function (template) {
                            t.push({
                                id: template.id,
                                value: template.content,
                                isCustom: template.isCustom
                            })
                        });
                        t.push(customeTplObj);
                        c.push({
                            name: 'Offer',
                            value: 1,
                            templates: t
                        })
                    }
                    if (greetings.length > 0) {
                        var newGreetingTpl = $scope.primeSmsTemplates.filter(function (item) {
                            return item.segment == 2 && item.category == 2;
                        });
                        t = [];
                        angular.forEach(newGreetingTpl, function (template) {
                            t.push({
                                id: template.id,
                                value: template.content,
                                isCustom: template.isCustom
                            })
                        });
                        t.push(customeTplObj);
                        c.push({
                            name: 'Greeting',
                            value: 2,
                            templates: t
                        })
                    }
                    p.push({
                        name: 'New',
                        value: 2,
                        categories: c
                    });
                }
                if (attriterSeg.length > 0) {
                    var offer = $scope.primeSmsTemplates.filter(function (item) {
                        return item.segment == 3 && item.category == 1;
                    });
                    var greetings = $scope.primeSmsTemplates.filter(function (item) {
                        return item.segment == 3 && item.category == 2;
                    });
                    c = [];
                    if (offer.length > 0) {
                        var attriterOfferTpl = $scope.primeSmsTemplates.filter(function (item) {
                            return item.segment == 3 && item.category == 1;
                        });
                        t = [];
                        angular.forEach(attriterOfferTpl, function (template) {
                            t.push({
                                id: template.id,
                                value: template.content,
                                isCustom: template.isCustom
                            })
                        });
                        t.push(customeTplObj);
                        c.push({
                            name: 'Offer',
                            value: 1,
                            templates: t
                        })
                    }
                    if (greetings.length > 0) {
                        var attriterGreetingTpl = $scope.primeSmsTemplates.filter(function (item) {
                            return item.segment == 3 && item.category == 2;
                        });
                        t = [];
                        angular.forEach(attriterGreetingTpl, function (template) {
                            t.push({
                                id: template.id,
                                value: template.content,
                                isCustom: template.isCustom
                            })
                        });
                        t.push(customeTplObj);
                        c.push({
                            name: 'Greeting',
                            value: 2,
                            templates: t
                        })
                    }
                    p.push({
                        name: 'Attritor',
                        value: 3,
                        categories: c
                    });
                }
                $scope.primalTemplates = p;

            }

            angular.forEach($scope.primeSmsTemplates, function (value, key) {
                $scope.template_log[value["id"]] = value["content"];
                inserted = false;
                if ($scope.comms.length > 0) {
                    idx = 0
                    angular.forEach($scope.comms, function (v, k) {
                        if (v["category"] == value["category"] && v["segment"] == value["segment"]) {
                            $scope.comms[k].templates.push(value["id"]);
                            inserted = true;
                        }
                    });
                }
                if (inserted == false) {
                    $scope.comms.push({
                        category: value["category"],
                        segment: value["segment"],
                        templates: [value["id"]],
                        userid: value["userid"],
                        preview: value['content'],
                        template: [value["id"]][0],
                        helpText: $scope.help(value["segment"].toString() + value["category"].toString())
                    });
                }


            });

            ajaxCall.post('/Admin/GetPrimalComms', data).then(function (res) {
                var x = res.data.data;
                $scope.primeSmsHistory = x;
                angular.forEach($scope.primeSmsHistory, function (value, i) {
                    $scope.primeSmsHistory[i]["category"] = $scope.primeSmsHistory[i]['smsCategory'];
                    $scope.primeSmsHistory[i]["validTillDate"] = getCalculateDateWithNumberOfDays($scope.primeSmsHistory[i]['valid_upto'], $scope.primeSmsHistory[i]['creat_date']);
                    $scope.primeSmsHistory[i]["template"] = $scope.primeSmsHistory[i]["templateId"];
                    $scope.primeSmsHistory[i].preview = $scope.preview($scope.primeSmsHistory[i])
                });
            });
        });
    };

    $scope.saveComm = function (comm) {
        if (comm.template == "custom") {
            data = {
                content: comm.preview,
                segment: comm.segment,
                category: comm.category,
                userid: $rootScope.LoggedInUserId
            }
            ajaxCall.post('/Admin/SavePrimalCustomTemplate', { template: data, accountInfo: $rootScope.accountInfo }).then(function (response) {
                if (response.data && response.data.data) {
                    comm.template = response.data.data;
                    comm.discount = null;
                    comm.valid_upto = null;
                    comm.storeName = null;
                    comm.commId = null;
                    $scope.savePrimeConfiguration(comm);
                }
            });
        } else {
            $scope.savePrimeConfiguration(comm);
        }

    }

    $scope.savePrimeConfiguration = function (comm) {
        var ifAnyPreviousCampaign = $scope.primeSmsHistory.filter(function (sms) {
            return sms.category == comm.category && sms.segment == comm.segment;
        });

        if (ifAnyPreviousCampaign.length > 0 && comm.enabled == 1) {
            showConfirmationDialog($modal, {
                    title: 'Are you sure?',
                    textContent: 'There is an active campaign for this configuration, enabling new one will deactivate previous.Are you sure you want to enable?',
                    type: 'alert-warning',
                    cancel:'cancel',
                    ok: 'proceed'
                },
                function () {
                    $scope.savePrimeConfigurationData(comm);
                }
            );
            $scope.primeSmsConfigue = {};
        } else {
            $scope.savePrimeConfigurationData(comm)
        }
    }

    $scope.savePrimeConfigurationData = function (comm) {
        if (comm.category == 2) {
            comm.discount = null;
            comm.valid_upto = null;
        }

        data = {
            enabled: comm.enabled,
            commId: comm.commId,
            userid: $rootScope.LoggedInUserId,
            storeName: comm.storeName,
            templateId: comm.template,
            smsCategory: comm.category,
            segment: comm.segment,
            discount: comm.discount,
            valid_upto: comm.valid_upto       
        }

        ajaxCall.post('/Admin/SavePrimalComm', { comm: data, accountInfo: $rootScope.accountInfo }).then(function (response) {
            console.log(response);
            if (response.status == 0) {
                $scope.primeSmsConfigue = {};
                $scope.getPrimalTemplates();
            } else {
                messageService.showError(response.msg);
            }
        });
    }

    $scope.getPrimalComms = function () {
        ajaxCall.post('/Admin/GetPrimalComms').then(function (response) {
            $scope.comms = [];
            var data = response.data;
        });
    };

    $scope.toggleComm = function (comm) {
        if ($scope.validate(comm)) {
            ajaxCall.post('/Admin/SavePrimalComm', comm).then(function (response) {
                var data = response.data;
            });
        } else {
            alert("Details Missing. Unable to activate.");
        }
    };

    $scope.validate = function (comm) {
        if (comm.template != 'custom' && comm.isCustom == 0) {
            if (comm.template == null || comm.template == "") {
                return false;
            } else if (comm.storeName == null || comm.storeName == "") {
                return false;
            } else if (comm.category == 1) {
                if (comm.discount == null || comm.discount == "" || comm.discount <= 0) {
                    return false;
                }
            }

        } else {
            if (comm.preview == null || comm.preview == "") {
                return false;
            }
        }
        return true;
    }

    $scope.preview = function (comm) {
        if (comm.template != null && comm.template == "custom") {
            $scope.primeSmsConfigue.isCustom = 1
        } else {
            var templateArray = $scope.primeSmsTemplates.filter(function (entry) {
                return entry.id === comm.template;
            });
            if (templateArray.length > 0) {
                $scope.primeSmsConfigue.isCustom = templateArray[0]['isCustom']
            } else {
                $scope.primeSmsConfigue.isCustom = 0
            }
        }

        var validity_days = 15;
        if (comm.template == "") {
            comm.preview = "";
        }
        if (comm.hasOwnProperty('valid_upto'))
            var validity_days = comm.valid_upto;
        else
            comm.valid_upto = 15;

        var t = $scope.template_log[comm.template];
        var d = comm.discount;
        var s = comm.storeName;

        var monthNames = ["January", "February", "March", "April", "May", "June",
            "July", "August", "September", "October", "November", "December"
        ];
        var v = new Date();
        var today = new Date();
        v.setDate(today.getDate() + parseInt(validity_days));
        var valid = v.getDate() + " " + monthNames[v.getMonth()];
        if (comm.category == 1)
            var context = {
                valid_upto: valid,
                shop_name: s,
                discount: d
            };
        else
            var context = {
                valid_upto: valid,
                shop_name: s
            };

        for (var k in context) {
            if (typeof (t) !== "undefined" && typeof (context[k]) !== "undefined")
                t = t.replace("{{" + k + "}}", context[k]);
        }

        comm.preview = t;
        return t;
    }

    $scope.help = function (on) {

        var help = {
            "1": "Repeat customers who visited you anytime within the last 30-45 days", // Loyal 
            "11": "Keep your loyal customers happy all the time! Those loyalists who have not visited you for more than 15 days will get this message. Top up your words with a discount to make them smile!", // Loyal + Offer
            "12": "Thank your loyalists for their love towards your brand!  After every visit to your store, your loyalists will get this message the very next day!", // Loyal + Greet
            "2": "New customers that you acquired in the past 30-45 days", // New
            "21": "Send this SMS to convert your newly acquired customers into loyalists! All your new customers who have not visited you for more than a month and a half, will get this message. Add a discount to tempt them to visit you again!", // New + Offer
            "22": "Thank your first-time customers for choosing your brand! After visiting you for the first time, your customers will receive this SMS the very next day!", // New + Greet
            "3": "Your customers who have not visited you for more than 30-45 days", // Attritor
            "31": "You cannot afford to lose customers! Pull back your potential attritors with an offer they cannot refuse! Your attritors will keep receiving this message every 15 days, for the next 2 months post their last visit!", // Attritor + Offer
            "32": "Say something nice to remind your potential attritors about your brand. Send this SMS to those customers who have not visited you for the past 45 days or more!" // Attritor + Greet
        };
        if (help[on]) {
            //alert(help[on]);
            return help[on];
        }
    }

    $rootScope.userDara.then(function (result) {
        if ($rootScope.username) {
            $scope.getPrimalTemplates();
        }
    });

    $scope.range = function (min, max, step) {
        step = step || 1;
        var input = [];
        for (var i = min; i <= max; i += step) {
            input.push(i);
        }
        return input;
    };

    $scope.messageCount = function (messageLength) {
        return Math.ceil(parseInt(messageLength) / 160);
    }

});

app.controller('userManagementController', ['$rootScope', '$scope', 'ajaxCall', 'service', 'messageService', function ($rootScope, $scope, ajaxCall, service, messageService) {
    ajaxCall.get('/Client/getAllPartners', { contentType: "application/json; charset=utf-8", dataType: "json" }).then(function (response) {
        $rootScope.partnersList = response.data.data;
    });

    $scope.userConfig = {
        valueField: 'userid',
        labelField: 'name',
        searchField: ['userid', 'name'],
        delimiter: '|',
        placeholder: 'Choose User',
        sortField: 'parentAdminUserId',
        render: {
            item: function (item) {
                var ret = '<div class="item" ><span class="name">' + item.name + '</span>';
                ret += '</div>';
                return ret;
            },
            option: function (item, escape) {
                var ret = '<div class="item" ><span class="name">' + item.name + '</span>';
                if (item.parentAdminUserId)
                    ret += '</br><span class="parent-name">Distributor - ' + item.parent + '</span>'
                ret += '</div>';
                return ret;
            }
        },
        maxItems: 1
    };



    $scope.smsPackage = {
        addSMS: function (selecteduser, smsCount) {
            if (selecteduser && smsCount)
                ajaxCall.post('/Client/AddSms', {
                    userid: selecteduser,
                    smsCount: smsCount
                }).then(function (response) {
                    switch (response.status) {
                        case 0:
                            messageService.showSuccess(response.msg, 5000);
                            break;
                        case 1:
                            messageService.showError(response.msg, 5000);
                            break;
                    }
                });
        },

        addSenderId: function (selecteduser, senderId) {

            if (!$scope.routerFilters.selected_PartnerId) {
                angular.element('[ng-model="routerFilters.selected_PartnerId"]').parent().addClass('has-error');
                messageService.showError('Please select a brand for this campaign!');
                return;
            } else {
                angular.element('[ng-model="routerFilters.selected_PartnerId"]').parent().removeClass('has-error');
            }

            if (!isValidSenderId(senderId)) {
                angular.element('[ng-model="smsPackage.senderId"]').parent().addClass('has-error');
                messageService.showError('Invalid Sender Id!');
                return;
            } else {
                angular.element('[ng-model="smsPackage.senderId"]').parent().removeClass('has-error');
            }

            if (selecteduser && senderId && $scope.routerFilters.selected_PartnerId > 0)
                ajaxCall.post('/Client/AddSenderId', {
                    userid: selecteduser,
                    senderId: senderId,
                    partnerId: $scope.routerFilters.selected_PartnerId
                }).then(function (response) {
                    switch (response.status) {
                        case 0:
                            messageService.showSuccess(response.msg, 5000);
                            break;
                        case 1:
                            messageService.showError(response.msg, 5000);
                            break;
                    }
                });
        }
    };


    $rootScope.userDara.then(function (result) {

        $scope.userDetials = {};
        $scope.data = {
            myusers: [],
            selected: null
        };

        ajaxCall.get('/Client/GetMyUsers').then(function (response) {
            $scope.data.myusers = response.data;
            if ($rootScope.isConfigAdminUser) {
                $scope.data.parentUsers = {};
                $scope.data.parentUsers.users = response.data;
                $scope.data.parentUsers.selected = null
            }
           
        });

        $scope.updateProduct = function (userid, product) {
            angular.forEach($scope.data.myusers, function (item) {
                if (item.userid == userid) {
                    item.product = product;
                };
            });
        };

        $scope.fetchUserManagementSettings = function () {
            if ($rootScope.featureEnabled(9) === 1) {
                $scope.fetchFeatures();
            }
            if ($rootScope.featureEnabled(50) === 1) {
                $scope.fetchUserLocations();
            }
        }

        if ($rootScope.featureEnabled(9) === 1) {
            $scope.updateFeatureList = function () {
                var features = {};
                for (var i = 0; i < $scope.allFeatureList.length; ++i) {
                    features[$scope.allFeatureList[i].key] = $scope.allFeatureList[i].status;
                }
                ajaxCall.post('/Admin/SaveFeatureList', {
                    userid: $scope.data.selected,
                    features: features
                });
            }
            
            $scope.previousConfig = null;
            $scope.fetchFeatures = function () {
                if ($scope.data.selected) {
                    service.fetchFeatures($scope.data.selected).then(function (data) {
                        if ($scope.allFeatureList && data)
                            for (var i = 0; i < $scope.allFeatureList.length; ++i) {
                                $scope.allFeatureList[i].status = data[i];
                            }
                    });
                }
                
            }
            service.getAllFeatureList().then(function (data) {
                $scope.allFeatureList = data;
            });
        }
        if ($rootScope.featureEnabled(50) === 1) {
            $scope.fetchUserLocations = function () {
                if ($scope.data.selected) {
                    angular.forEach($scope.data.myusers, function (user) {
                        if (user.userid == $scope.data.selected) {
                            $scope.userDetials.username = user.name;
                            $scope.userDetials.parentId = user.parentAdminUserId;
                            $scope.userDetials.userid = user.userid;
                            $scope.userDetials.usertype = user.userType;
                            $scope.userDetials.product = user.product;
                            $scope.userDetials.active = user.active;
                            if ($rootScope.isConfigAdminUser)
                                $scope.data.parentUsers.selected = user.parentAdminUserId;
                            return
                        }
                    });

                    ajaxCall.get('/Client/getAllClients?userId=' + $scope.data.selected, { contentType: "application/json; charset=utf-8", dataType: "json" }).then(function (response) {
                        $scope.userDetials.userclients = response.data.data;
                    });
                    ajaxCall.get('/Client/getAllPartners?userId=' + $scope.data.selected, { contentType: "application/json; charset=utf-8", dataType: "json" }).then(function (response) {
                        $scope.userDetials.userpartners = response.data.data;
                    });
                    ajaxCall.get('/Client/getUserLocations?userId=' + $scope.data.selected).then(function (response) {
                        $scope.userDetials.userlocations = response.data;
                    });
                    ajaxCall.get('/Client/getUsersAllClientsPartnersAndStores?userId=' + $scope.data.selected, { contentType: "application/json; charset=utf-8", dataType: "json" }).then(function (response) {
                        $scope.userDetials.usersClientPartnersAndStores = response.data;
                        if ($scope.userDetials.usertype >= 100 && $scope.userDetials.usersClientPartnersAndStores) {
                            var usersClientArray = $scope.clientsList.filter(function (client) {
                                return $scope.userDetials.usersClientPartnersAndStores.find(function (account) {
                                    return client.clientId == account.client_id;
                                });
                            });
                            $scope.userDetials.userclients = $scope.userDetials.userclients.concat(usersClientArray);
                        }
                        if ($scope.userDetials.usertype > 101 && $scope.userDetials.usersClientPartnersAndStores) {
                            var usersPartnerArray = $rootScope.partnersList.filter(function (partner) {
                                return $scope.userDetials.usersClientPartnersAndStores.find(function (account) {
                                    return account.partners.find(function (brand) {
                                        return partner.partnerId == brand.partner_id;
                                    });
                                });
                            });
                            $scope.userDetials.userpartners = $scope.userDetials.userpartners.concat(usersPartnerArray);
                        }

                    });
                    
                }
            }


            $scope.addMappingForUser = function (item, model,type) {
                if (item) {
                    var findPreviousClients = null;
                    if ($scope.userDetials.usersClientPartnersAndStores && $scope.userDetials.usersClientPartnersAndStores != null) {
                        findPreviousClients = $scope.userDetials.usersClientPartnersAndStores.find(function (element) {
                             return element.client_id != item.clientId;
                         });
                    }
                    
                    console.log(findPreviousClients);

                    if (item.hasOwnProperty('clientId') && type == 'client')
                    {
                        var mapped_id = item.clientId;
                        //if ($scope.userDetials.userclients.length > 1) {
                        if (findPreviousClients && findPreviousClients.hasOwnProperty('client_id') && findPreviousClients.client_id != item.clientId) {
                            $scope.userDetials.userclients.splice($scope.userDetials.userclients.indexOf(item), 1);
                            messageService.showError('You can not assign more than one client to a single user!');
                            return;
                        }
                    }
                    else if (item.hasOwnProperty('partnerId') && type == 'partner')
                    {
                        var mapped_id = item.partnerId;
                        if (findPreviousClients && findPreviousClients.hasOwnProperty('client_id') && findPreviousClients.client_id != item.clientId) {
                            $scope.userDetials.userpartners.splice($scope.userDetials.userpartners.indexOf(item), 1);
                            messageService.showError('You can not assign more than one client\'s partner to a single user!');
                            return;
                        }
                    }
                    else if (item.hasOwnProperty('nasid') && type == 'location')
                    {
                        var mapped_id = item.nasid;
                        if (findPreviousClients && findPreviousClients.hasOwnProperty('client_id') && findPreviousClients.client_id != item.clientId) {
                            $scope.userDetials.userlocations.splice($scope.userDetials.userlocations.indexOf(item), 1);
                            messageService.showError('You can not assign more than one client\' location to a single user!');
                            return;
                        }
                    }
                    var username = $scope.userDetials.username;
                    ajaxCall.get('/Client/addMappingForUser?username=' + username + '&mappedId=' + mapped_id + '&type=' + type).then(function (response) {
                        //ajaxCall.get('/Client/getUsersAllClientsPartnersAndStores?userId=' + $scope.data.selected, { contentType: "application/json; charset=utf-8", dataType: "json" }).then(function (response) {
                        //    $scope.userDetials.usersClientPartnersAndStores = response.data;
                        //});
                        $scope.fetchUserLocations();
                    });
                }
            }


            $scope.removeMappingForUser = function (item, model, type) {
                if (item) {
                    if (item.hasOwnProperty('clientId') && type == 'client') {
                        var mapped_id = item.clientId;
                    }
                    else if (item.hasOwnProperty('partnerId') && type == 'partner') {
                        var mapped_id = item.partnerId;
                    }
                    else if (item.hasOwnProperty('nasid') && type == 'location') {
                        var mapped_id = item.nasid;
                    }
                    var username = $scope.userDetials.username;
                    ajaxCall.get('/Client/removeMappingForUser?username=' + username + '&mappedId=' + mapped_id + '&type=' + type).then(function (response) {
                        //console.log(response);
                        //ajaxCall.get('/Client/getUsersAllClientsPartnersAndStores?userId=' + $scope.data.selected, { contentType: "application/json; charset=utf-8", dataType: "json" }).then(function (response) {
                        //    $scope.userDetials.usersClientPartnersAndStores = response.data;
                        //});
                        $scope.fetchUserLocations();
                    });
                }
            }

            $scope.addNasForUser = function (item, model) {
                if (item && item.nasid != 'undefined') {
                    var username = $scope.userDetials.username;
                    ajaxCall.get('/Client/AssignNasToUser?username=' + username + '&nasid=' + item.nasid).then(function (response) {
                        console.log(response);
                    });
                }
            }

            $scope.removeNasFromUser = function (item, model) {
                if (item && item.nasid != 'undefined') {
                    var username = $scope.userDetials.username;
                    ajaxCall.get('/Client/RemoveNasFromUser?username=' + username + '&nasid=' + item.nasid).then(function (response) {
                        console.log(response);
                    });
                }
            }

            $scope.removeAllNases = function () {
                var username = $scope.userDetials.username;
                var allLocations = $scope.userDetials.userlocations;
                //console.log($scope.userDetials.userlocations)
                if (allLocations.length > 0) {
                    angular.forEach(allLocations, function (item, i) {
                        ajaxCall.get('/Client/removeMappingForUser?username=' + username + '&mappedId=' + item.nasid + '&type=location').then(function (response) {
                            //$scope.userDetials.userlocations.splice(i, 1);

                            $scope.userDetials.userlocations = removeElementFromArray($scope.userDetials.userlocations,item);
                            //console.log('AFter Ajax - ' + i);
                            //console.log('AFter Ajax array length - ' + allLocations.length);
                        });
                    });
                }
            }

            $scope.manageLeads = function (operation) {
                ajaxCall.post('/Client/manageLeads', { userId: $scope.data.selected, operation: operation }).then(function (response) {
                    if (response.data) {
                        if (operation == 1) {
                            messageService.showSuccess('Lead added successfull!');
                        } else {
                            messageService.showSuccess('Lead dropped successfull!');
                        }
                        $scope.userDetials.parentId = $rootScope.LoggedInUserId;
                    } else {
                        messageService.showError('Something went wrong, please try again later!');
                    }
                });
            }
            $scope.userDetials.hideAll = false;
            $scope.assignNewParent = function () {
                var postData = {
                    userId: $scope.userDetials.userid,
                    parentId: $scope.data.parentUsers.selected,
                    operation : 1
                };
                if ($scope.data.parentUsers.selected && $scope.userDetials.userid) {
                    ajaxCall.post('/Client/assignNewParent', postData).then(function (response) {
                        if (response.data) {
                            messageService.showSuccess('Selected user\'s lead change successfully!');
                        } else {
                            messageService.showError('Something went wrong, please try again later!');
                        }
                    });
                }
            }
            
            $scope.updateTAdmin = function () {
                var postData = {
                    userId: $scope.userDetials.userid,
                    userType: $scope.userDetials.usertype,
                    product: $scope.userDetials.product
                };
                ajaxCall.post('/Client/UpdateTAdmin', postData).then(function (response) {
                    if (response.data) {
                        messageService.showSuccess('Product and Feature List updated Successfully!');
                        
                        $scope.updateProduct($scope.userDetials.userid, $scope.userDetials.product);
                        $scope.fetchUserManagementSettings();
                        //$scope.userDetials.product = postData.product;
                        $scope.userDetials.usertype = postData.userType;
                    } else {
                        messageService.showError('Something went wrong, please try again later!');
                    }
                });
            }

            $scope.updatePortalAcess = function () {
                if (confirm('Are you sure you want to change the portal access for user - ' + $scope.userDetials.username + ' ?')) {
                    var postData = {
                        userId: $scope.userDetials.userid,
                        active: $scope.userDetials.active
                    };
                    ajaxCall.post('/Client/updatePortalAcess', postData).then(function (response) {
                        if (response.data) {
                            if (postData.active == 0) {
                                messageService.showSuccess('Poratl access revoked successfully!');
                            } else if (postData.active == 1) {
                                messageService.showSuccess('Poratl access allowed successfully!');
                            }
                        } else {
                            messageService.showError('Something went wrong, please try again later!');
                        }
                    });
                }
            }
        }
    });
}]);

app.controller('shopperDensityController', ['$scope', 'ajaxCall', 'service', 'messageService', function ($scope, ajaxCall, service, messageService) {
    var radius = 0;
    ajaxCall.get('/Client/getLotLangsForGmap').then(function (response) {
        var locationData = response.data;
        var citymap = {};

        if (locationData.length > 0) {
            angular.forEach(locationData, function (item) {
                var latLongObj = {};
                var keyWord = item.marketplacename;
                var key = keyWord.replace(' ', '_')
                latLongObj.lat = parseFloat(item.lat);
                latLongObj.lng = parseFloat(item.long);
                citymap[key] = {};
                citymap[key].center = latLongObj;
                citymap[key].radius = parseInt(item.radius);
                citymap[key].density = item.density;
                citymap[key].marketplace = item.marketplacename;
                citymap[key].cityname = item.city;
                citymap[key].peakhour = parseInt(item.peakhour);
                citymap[key].busiestday = item.busiestday;

                if (parseInt(item.dailyFootFall) > radius) {
                    hieghestfootfall = parseInt(item.radius);
                }
            });
        }

        angular.forEach(citymap, function (item, index) {
            /*var ratio = (parseInt(citymap[index].footfall) * 100 / parseInt(hieghestfootfall));
            citymap[index].color = ColorLuminance("#ff1600", -ratio / 100);
            citymap[index].opacity = citymap[index].footfall / hieghestfootfall;*/
            citymap[index].color = '#ff1600';
            citymap[index].opacity = .5;
        });

        createHeatMap(citymap);

    });
}]);

app.controller('globalReportController', ['$scope', '$rootScope', '$modal', 'ajaxCall', 'service', 'messageService', function ($scope, $rootScope, $modal, ajaxCall, service, messageService) {
    $scope.dateOptions = {
        formatYear: 'yy',
        startingDay: 1
    };
    $scope.openDatePicker = function ($event) {
        $event.preventDefault();
        $event.stopPropagation();
    };
    $scope.intervals = [];
    for (var i = 0; i < 24; ++i) {
        $scope.intervals.push({ name: i + ":" + "00", value: (i * 60).toString() });
        $scope.intervals.push({ name: i + ":" + "15", value: (i * 60 + 15).toString() });
        $scope.intervals.push({ name: i + ":" + "30", value: (i * 60 + 30).toString() });
        $scope.intervals.push({ name: i + ":" + "45", value: (i * 60 + 45).toString() });
    }
    $scope.customReports = [];
    $scope.customSMSReports = [];

    $scope.getReportSubscription = function () {
        ajaxCall.get('/Client/GetReportSubscription').then(function (response) {
            $scope.customReports = response.data;
            $scope.customReports.forEach(function (obj) {
                obj.reportName = obj.triggerName,
                obj.query = obj.parameters[0],
                obj.receivers = obj.parameters[1],
                obj.cycle = "" + obj.scheduleCycle.repeatIntervalInMin,
                obj.startDate = new Date(obj.scheduleCycle.startTime);
                obj.executionTime = getObjectFromArray($scope.intervals, 'value', (obj.startDate.getHours() * 60 + obj.startDate.getMinutes()));
                obj.sheetName = obj.parameters.length > 3 ? obj.parameters[3] : '';
                obj.templatePath = obj.parameters.length > 4 ? obj.parameters[4] : '';
                obj.templateFillerQuery = obj.parameters.length > 5 ? obj.parameters[5] : '';
                obj.subject = obj.parameters.length > 6 ? obj.parameters[6] : '';
                obj.queryType = obj.parameters.length > 7 ? obj.parameters[7] : '';
            });
        });
    }

    $scope.getSMSReportSubscription = function () {
        ajaxCall.get('/Client/GetSMSReportSubscription').then(function (response) {
            $scope.customSMSReports = response.data;
            $scope.customSMSReports.forEach(function (obj) {
                obj.reportName = obj.triggerName,
                obj.query = obj.parameters[0],
                obj.receivers = obj.parameters[1],
                obj.cycle = "" + obj.scheduleCycle.repeatIntervalInMin,
                obj.startDate = new Date(obj.scheduleCycle.startTime);
                obj.executionTime = getObjectFromArray($scope.intervals, 'value', (obj.startDate.getHours() * 60 + obj.startDate.getMinutes()));
                obj.smsText = obj.parameters.length > 3 ? obj.parameters[3] : '';
                obj.queryType = obj.parameters.length > 4 ? obj.parameters[4] : '';
                obj.senderId = obj.parameters.length > 5 ? obj.parameters[5] : 'open-IEWIFI';
                obj.restingDays = obj.parameters.length > 6 ? obj.parameters[6] : '0';
                obj.sendVia = obj.parameters.length > 7 ? obj.parameters[7] : '0';
                obj.header = obj.parameters.length > 8 ? obj.parameters[8] : '';
                obj.footer = obj.parameters.length > 9 ? obj.parameters[9] : '';
            });
        });
    }
    $scope.editCustomReport = function (customReport) {
        showDialog($modal, '/editReport.html', {
            data: {
                report: customReport,
                openDatePicker: $scope.openDatePicker,
                dateOptions: $scope.dateOptions,
                intervals: $scope.intervals
            },
            ok: function () {
                $scope.saveCustomReport(customReport);
            }
        });
    }
    $scope.editCustomSmsReport = function (customReport) {
        showDialog($modal, '/editSmsReport.html', {
            data: {
                report: customReport,
                openDatePicker: $scope.openDatePicker,
                dateOptions: $scope.dateOptions,
                intervals: $scope.intervals
            },
            ok: function () {
                $scope.saveCustomSMSReport(customReport);
            }
        });
    }
    $scope.saveCustomReport = function (customReport) {
        if (!$rootScope.featureEnabled(54)) return;

        ajaxCall.post('/Client/SubmitReportSubscription?executionTime=' + customReport.executionTime.value, {
            triggerId: customReport.triggerId,
            reportName: customReport.reportName,
            sheetName: customReport.sheetName,
            query: customReport.query,
            cycle: customReport.cycle,
            receivers: customReport.receivers,
            startDate: customReport.startDate,
            templatePath: customReport.templatePath,
            templateFillerQuery: customReport.templateFillerQuery,
            subject: customReport.subject,
            queryType: customReport.queryType
        }).then(function (response) {
            messageService.showSuccess(response.msg);
            $scope.getReportSubscription();
        });
    }
    $scope.saveCustomSMSReport = function (customReport) {
        if (!$rootScope.featureEnabled(54)) return;
        ajaxCall.post('/Client/SubmitSMSReportSubscription?executionTime=' + customReport.executionTime.value, {
                triggerId: customReport.triggerId,
                reportName: customReport.reportName,
                smsText: customReport.smsText,
                query: customReport.query,
                cycle: customReport.cycle,
                receivers: customReport.receivers,
                startDate: customReport.startDate,
                queryType: customReport.queryType,
                senderId: customReport.senderId,
                restingDays: customReport.restingDays,
                sendVia: customReport.sendVia,
                header: customReport.header,
                footer: customReport.footer
            }).then(function (response) {
                messageService.showSuccess(response.msg);
                $scope.getSMSReportSubscription();
            });
    }
    $scope.executeNow = function (customReport) {
        ajaxCall.get('/Client/RunTrigger?triggerId=' + customReport.triggerId).then(function (response) {
            messageService.showSuccess(response.msg);
        });
    }
    $scope.executeSmsNow = function (customReport) {
        ajaxCall.get('/Client/RunTrigger?triggerId=' + customReport.triggerId).then(function (response) {
            messageService.showSuccess(response.msg);
        });
    }
    $scope.getReportSubscription();
    $scope.getSMSReportSubscription();
}]);

app.controller('modelController', function ($scope, $modalInstance, data) {
    $scope.data = data.data;

    $scope.ok = function () {
        data.ok();
        //$modalInstance.close();
    };

    $scope.cancel = function () {
        $modalInstance.dismiss('cancel');
    };

    $scope.deleteScheduleStopDateProperty = function () {
        data.deleteScheduleStopDateProperty();
    };

    $scope.parseIntFromStr = function (str) {
        return parseInt(str);
    }

    $scope.range = function (count) {
        var arr = [];
        for (var i = 0; i < count+1; i++) {
            arr.push(i)
        }
        return arr;
    }

    $scope.remove = function (event) {
        angular.element(event.target).parent().remove();
    }


    $scope.resetOffers = function () {
        data.resetOffers();
    }

    $scope.resetOfferContent = function () {
        data.resetOfferContent();
    }

    $scope.setEverydayOffer = function () {
        data.setEverydayOffer();
    }

    $scope.checkForNextOfferSlot = function (day, ruleIndex) {
        data.checkForNextOfferSlot(day, ruleIndex);
    }
});

app.controller('wofrOfferController', ['$scope', 'ajaxCall', '$rootScope', 'service', 'messageService', '$modal', '$state', function ($scope, ajaxCall, $rootScope, service, messageService, $modal, $state) {
    $scope.parentPlaces = {};
    ajaxCall.get('/Wofr/GetWofrParentPlaces').then(function (response) {
        angular.forEach(response, function (data, index) {
            $scope.parentPlaces[data.Key] = data.Value;
        });
    });
    $scope.PastOffers = [];
    $scope.Offers = {};
    $scope.WofrData = [];
    $scope.GetPastWofrOffers = function () {
        $scope.PastOffers = [];
        ajaxCall.get('/Wofr/GetPastWofrOffers').then(function (response) {
            $scope.PastOffers = response;
            messageService.showSuccess("Past Offers Loaded");
        });
    };
    $scope.GetWofrData = function () {
        $scope.Offers = {};
        $scope.WofrData = [];
        ajaxCall.get('/Wofr/GetWofrOffers').then(function (offerResponse) {
            angular.forEach(offerResponse, function(value, key) {
                $scope.Offers[value.nasid] = value;
            });
            ajaxCall.get('/Wofr/GetWofrStores').then(function (storeResponse) {
                $scope.WofrData = storeResponse.data;
                messageService.showSuccess("WOFR Markets Loaded");
            });
        });
    };

    $scope.hasRunningCampaign = function (store) {
        if ($scope.Offers[store.nasid])
            return true;
        else
            return false;
    };

    $scope.hasCompleteProfile = function (store) {
        if (store.profile)
            if (store.profile["nasid"])
                if (store.profile["parentplaceId"])
                    if (store.profile["brand_name"])
                        if (store.profile["shop_image"])
                            return true;
        return false;
    };

    $scope.getOfferCreateBg = function (store) {
        return 'linear-gradient(90deg,rgba(135,49,154,0.90),rgba(68,154,49,0.75)),url(' + store.profile.shop_image + ')';
    };

    $scope.getStoreBg = function (store) {
        if ($scope.Offers[store.nasid]) {
            return 'linear-gradient(90deg,rgba(135,49,154,0.90),rgba(49,69,154,0.75)),url(' + store.profile.shop_image + ')';
        } else {
            if ($scope.hasCompleteProfile(store)) {
                return 'linear-gradient(90deg,rgba(68,154,49,0.9),rgba(255,255,255,0.9)),url(' + store.profile.shop_image + ')';
            } else {
                return 'linear-gradient(0deg,rgba(255,255,255,1),rgba(255,255,255,1));';
            }
        }
    };

    $scope.getStoreDisplayFg = function (store) {
        if ($scope.Offers[store.nasid])
            return 'white';
        else
            if ($scope.hasCompleteProfile(store))
                return 'white';
            else
                return 'black';
    };

    $scope.showStoreProfileEditDialog = function (store) {
        console.log(store);
        store = angular.copy(store);
        showDialog($modal, '/storeEdit.html', {
            data: {
                parentPlaces: $scope.parentPlaces,
                store: store,
                expand: false,
                detailtab: 'basic',
            },
            size: 'md',
            ok: function () {
                function valid(s) {
                    $('.form-group').removeClass('has-error');
                    var result = true;
                    req = ["parentplaceId", "brand_name", "shop_image"];
                    var l = req.length;
                    for (var i = 0; i < l; i++) {
                        if (s[req[i]] == null || s[req[i]] == "") {
                            console.log(i);
                            document.getElementById(req[i]).parentNode.className += " has-error";
                            result = false;
                        }
                    }
                    return result;
                }
                if (valid(store.profile)) {
                    store.profile.nasid = store.nasid;
                    store.profile.parentplaceName = $scope.parentPlaces[parseInt(store.profile.parentplaceId)]["parentPlaceName"];
                    console.log(store.profile);
                    ajaxCall.post('/Wofr/SaveWofrStoreProfile', store.profile).then(function (response) {
                        $state.go("landing.wofrOffer", {}, { reload: true });
                    });
                } else {
                    alert("Form Fields Missing/Blank. Start Again.");
                    dontCloseThisDialog();
                }
            }
        });
    }

    $scope.stopWofrOffer = function (store) {
        var data = {
            offerid: $scope.Offers[store.nasid].campaign_id
        };
        ajaxCall.post('/Wofr/DisableWofrOffer', data).then(function (response) {
            $state.go("landing.wofrOffer", {}, { reload: true });
        });
    };

    $scope.showWofrOfferCreateDialog = function (store) {
        $scope.newOffer = {
            isScheduled: false,
            offerPeriod: 'everyday',
            everdayOffer: 0,
            offers: [
                {
                    name: '',
                    short_text: '',
                    long_text: '',
                }
            ],
            isPremiumListing: false,
            notifyOnRedemption: false,
            content: {
                "Monday": [{
                    start: 0,
                    stop: 23,
                    payload: 0
                }],
                "Tuesday": [{
                    start: 0,
                    stop: 23,
                    payload: 0
                }],
                "Wednesday": [{
                    start: 0,
                    stop: 23,
                    payload: 0
                }],
                "Thursday": [{
                    start: 0,
                    stop: 23,
                    payload: 0
                }],
                "Friday": [{
                    start: 0,
                    stop: 23,
                    payload: 0
                }],
                "Saturday": [{
                    start: 0,
                    stop: 23,
                    payload: 0
                }],
                "Sunday": [{
                    start: 0,
                    stop: 23,
                    payload: 0
                }]
            },
            enabledDay: { "Monday": true, "Tuesday": true, "Wednesday": true, "Thursday": true, "Friday": true, "Saturday": true, "Sunday": true }
        };
        showDialog($modal, '/offerCreate.html', {
            data: {
                store: store,
                newOffer: $scope.newOffer,
                background: $scope.getOfferCreateBg(store),
                color: $scope.getStoreDisplayFg(store)
            },
            size: 'lg',
            ok: function () {
                var uploadData = {
                    nasid: store.nasid,
                    parentCampaignId: $scope.parentPlaces[store.parentPlaceId]["wofrCampaignId"],
                    campaignName: $scope.newOffer.offerName,
                    scheduledStopDate: null,
                    premiumListing: $scope.newOffer.isPremiumListing,
                    notifyOnRedemption: $scope.newOffer.isPremiumListing,
                    content: {},
                    offers: $scope.newOffer.offers
                };
                if ($scope.newOffer.isScheduled) {
                    uploadData.scheduledStopDate = $scope.newOffer.scheduledStopDate;
                }
                if ($scope.newOffer.offerPeriod == "everyday") {
                    uploadData.content = $scope.newOffer.content;
                } else {
                    angular.forEach($scope.newOffer.enabledDay, function (enabledDow, dow) {
                        if (enabledDow)
                            uploadData.content[dow] = $scope.newOffer.content[dow];
                    });
                }
                ajaxCall.post('/Wofr/SaveWofrOffer', uploadData).then(function (response) {
                    $state.go("landing.wofrOffer", {}, { reload: true });
                });
            },
            deleteScheduleStopDateProperty: function () {
                if (!$scope.newOffer.isScheduled)
                    delete $scope.newOffer.scheduleStopDate;
            },
            resetOffers:function() {
                $scope.newOffer.offers = [
                    {
                        name: '',
                        short_text: '',
                        long_text: '',
                    }
                ]
                $scope.newOffer.content = { Monday: [{ start: 0, stop: 23, payload: 0 }], Tuesday: [{ start: 0, stop: 23, payload: 0 }], Wednesday: [{ start: 0, stop: 23, payload: 0 }], Thursday: [{ start: 0, stop: 23, payload: 0 }], Friday: [{ start: 0, stop: 23, payload: 0 }], Saturday: [{ start: 0, stop: 23, payload: 0 }], Sunday: [{ start: 0, stop: 23, payload: 0 }] };
                $scope.newOffer.enabledDay = { Monday: true, Tuesday: true, Wednesday: true, Thursday: true, Friday: true, Saturday: true, Sunday:true };
            },
            resetOfferContent: function () {
                $scope.newOffer.content = { Monday: [{ start: 0, stop: 23, payload: 0 }], Tuesday: [{ start: 0, stop: 23, payload: 0 }], Wednesday: [{ start: 0, stop: 23, payload: 0 }], Thursday: [{ start: 0, stop: 23, payload: 0 }], Friday: [{ start: 0, stop: 23, payload: 0 }], Saturday: [{ start: 0, stop: 23, payload: 0 }], Sunday: [{ start: 0, stop: 23, payload: 0 }] };
                $scope.newOffer.enabledDay = { Monday: true, Tuesday: true, Wednesday: true, Thursday: true, Friday: true, Saturday: true, Sunday: true };
            },
            setEverydayOffer: function () {
                angular.forEach($scope.newOffer.content, function (element) {
                    element[0]['payload'] = $scope.newOffer.everdayOffer;
                })
            },
            checkForNextOfferSlot: function (day, ruleIndex) {
                if ($scope.newOffer.content[day][ruleIndex]['stop'] == 23) {
                    for (var i = $scope.newOffer.content[day].length - 1; i >= 0; i--) {
                        if (i > ruleIndex) {
                            $scope.newOffer.content[day].splice(i, 1);
                        }
                    }
                }
            }
        });
    }
}]);

app.controller('smsSenderController', function ($scope, $rootScope, service, ajaxCall, $modal, messageService, $state, $timeout) {
    $scope.smsesExhausted = 0;
    $scope.Math = Math;
    $scope.smsTemplates = smsTemplates;

    $scope.genders = [{
        key: 'Male',
        value: 'M'
    }, {
        key: 'Female',
        value: 'F'
    }];
    $scope.affluences = [{
        key: 'Low',
        value: 'L'
    }, {
        key: 'Medium',
        value: 'M'
    }, {
        key: 'High',
        value: 'H'
    }];

    $scope.dateRangePickerOpt1 = {
        singleDatePicker: true,
        autoApply: true,
        autoUpdateInput: true,
        ranges: {
            'Today': [moment(), moment()],
            'Yesterday': [moment().subtract(1, 'days'), moment().subtract(1, 'days')],
            'Last 7 Days': [moment().subtract(6, 'days'), moment()],
            'Last 30 Days': [moment().subtract(29, 'days'), moment()],
            'This Month': [moment().startOf('month'), moment().endOf('month')],
            'Last Month': [moment().subtract(1, 'month').startOf('month'), moment().subtract(1, 'month').endOf('month')]
        },
        locale: {
            format: "LL",
        },
        showCustomRangeLabel: false,
        maxDate: moment(),
        startDate: moment().subtract(29, 'days'),
        endDate: moment(),
        eventHandlers: {
            'show.daterangepicker': function (ev, picker) {
                picker.container[0].firstElementChild.style.display = 'block';
            },
            'apply.daterangepicker': function (ev, picker) {
                ev.currentTarget.value = moment(picker.startDate).format('LL');
                $scope.data.setCohortData();
            }
        }
    };

    $scope.dateRangePickerOpt2 = {
        singleDatePicker: true,
        ranges: {
            'Now': [moment(), moment()]
        },
        locale: {
            format: "LLL",
            applyLabel: "Apply",
            cancelLabel: "Cancel",
            customRangeLabel: "Custom",
        },
        showCustomRangeLabel: false,
        timePicker: true,
        minDate: moment(),
        maxDate: moment().add(6, 'days'),
        startDate: moment(),
        endDate: moment(),
        eventHandlers: {
            'show.daterangepicker': function (ev, picker) {
                picker.container[0].firstElementChild.style.display = 'block';
            },
            'apply.daterangepicker': function (ev, picker) {
                ev.currentTarget.value = moment(picker.startDate).format('LLL');
            }
        }
    };

    $scope.dateRangePickerOpt3 = {
        singleDatePicker: true,
        autoApply: true,
        ranges: {
            'For a week': [moment().add(6, 'days'), moment().add(6, 'days')],
            'For a month': [moment().add(1, 'month'), moment().add(1, 'month')],
            'For two months': [moment().add(2, 'month'), moment().add(2, 'month')],
            'For three months': [moment().add(3, 'month'), moment().add(3, 'month')],
            'For six months': [moment().add(6, 'month'), moment().add(6, 'month')],
            'For a year': [moment().add(1, 'year'), moment().add(1, 'year')]
        },
        locale: {
            format: "LL",
            separator: " - ",
            applyLabel: "Apply",
            cancelLabel: "Cancel",
            fromLabel: "From",
            toLabel: "To",
            customRangeLabel: "Custom",
            firstDay: 1
        },
        showCustomRangeLabel: false,
        minDate: moment(),
        startDate: moment().add(6, 'days'),
        endDate: moment().add(6, 'days'),
        eventHandlers: {
            'show.daterangepicker': function (ev, picker) {
                picker.container[0].firstElementChild.style.display = 'block';
            }
        }
    };

    var smsData = {
        sms: {
            senderId: "",
            message: "",
            dataBase: 'account',
            isTrackingEnabled: false,
            trackingUrl: '',
            numbers: [],
            unit: 0,
            chrLength: 0
        },
        sendNow: {
            notVisitedSince: !1,
            cities: [],
            locations: [],
            campaignType: 10,
            fdate: "",
            tdate: "",
            send_on: moment(),
            analytics: {
                gender: [],
                affluence: [],
                isNew: !1
            }
        },
        scheduled: {
            cities: [],
            campaignType: 11,
            analytics: {
                gender: [],
                affluence: [],
                isNew: !1
            },
            schedule: {
                runningTime: new Date('Fri Jan 01 1999 19:00:00 GMT+0530'),
                runningDays: {
                    monday: false,
                    tuesday: false,
                    wednesday: false,
                    thursday: true,
                    friday: false,
                    saturday: false,
                    sunday: false
                },
                runTill: moment().add(6, "days")
            }
        }
    };

    $scope.setMessageDefaultParameters = function (campainType) {
        $scope.data.sms.campaignType = campainType;
        if ($scope.data.sms.campaignType && $scope.data.sms.campaignType == 10 && $scope.data.sms.dataBase == 'account') {
            angular.forEach(smsData.sendNow, function (value, index) {
                $scope.data.sms[index] = value;
            })
            angular.forEach(smsData.scheduled, function (value, key) {
                if ($scope.data.sms.hasOwnProperty(key) && key != 'campaignType' && key != 'analytics') {
                    delete $scope.data.sms[key]
                }
            });
            if ($scope.data.sms.notVisitedSince) {
                $scope.data.sms.fdate = moment().subtract(2, 'years');
                $scope.data.sms.tdate = $scope.data.cohort_date;
            } else {
                $scope.data.sms.fdate = $scope.data.cohort_date;
                $scope.data.sms.tdate = moment();
            }


        } else if ($scope.data.sms.campaignType && $scope.data.sms.campaignType == 10 && $scope.data.sms.dataBase == 'upload') {
            angular.forEach(smsData.sendNow, function (value, index) {
                $scope.data.sms[index] = value;
            })
            angular.forEach(smsData.scheduled, function (value, key) {
                if ($scope.data.sms.hasOwnProperty(key) && key != 'campaignType' && key != 'send_on' && key != 'numbers') {
                    delete $scope.data.sms[key]
                }
            });

        } else if ($scope.data.sms.campaignType && $scope.data.sms.campaignType == 11) {
            angular.forEach(smsData.scheduled, function (value, index) {
                $scope.data.sms[index] = value;
            })
            angular.forEach(smsData.sendNow, function (value, key) {
                if ($scope.data.sms.hasOwnProperty(key) && key != 'campaignType' && key != 'analytics') {
                    delete $scope.data.sms[key]
                }
            })
        }
    }

    $scope.setCohortData = function (bool) {
        if (bool) {
            $scope.data.sms.fdate = moment().subtract(2, 'years');
            $scope.data.sms.tdate = $scope.data.cohort_date;
        } else {
            $scope.data.sms.fdate = $scope.data.cohort_date;
            $scope.data.sms.tdate = moment();
        }
    }

    $scope.getSMSEstimates = function (sms) {

        ajaxCall.post('/Kelp/GetSMSEstimates', {
            campaignStr: JSON.stringify(sms)
        }).then(function (response) {
            if (response.status == 0) {
                result = JSON.parse(response.data);
                if (result.data) {
                    if (result.data.hasOwnProperty('cost')) {
                        document.getElementById('estimation').innerHTML = '<div class="alert alert-dismissible alert-success" role="alert">' +
                            'Total number targeted will be <strong>' + result.data.targets +
                            '</strong>, and total sms cost will be Rs. <strong>' + result.data.cost.toFixed(2) + '</strong>' +
                            '<button type="button" class="close" data-dismiss="alert">' +
                            '<span class="glyphicon glyphicon-remove"></span></button></div>';

                    } else if (result.data.hasOwnProperty('dailyTargets')) {
                        document.getElementById('estimation').innerHTML = '<div class="alert alert-dismissible alert-success" role="alert">' +
                            'Total number targeted daily will be <strong>' + result.data.dailyTargets +
                            '</strong>, and total sms cost per day will be Rs. <strong>' + result.data.dailyBudget + '</strong>' +
                            '<button type="button" class="close" data-dismiss="alert">' +
                            '<span class="glyphicon glyphicon-remove"></span></button></div>';
                    }
                }
            }
        });
    }

    $scope.data = {
        sms: smsData.sms,
        cohort_date: moment().subtract(29, 'days'),
        countUtf8: function (string) {
            var utf8length = 0;
            var chars = "[|\{}~[]^]";
            var extended = chars.split("");

            for (var n = 0; n < string.length; n++) {
                var c = string.charCodeAt(n);
                if (c < 128) {
                    utf8length++;
                    var cr = string.charAt(n);
                    if ($.inArray(cr, extended) !== -1) {
                        utf8length++;
                    }
                } else if ((c > 127) && (c < 2048)) {
                    utf8length = utf8length + 2;
                } else {
                    utf8length = utf8length + 3;
                }
            }
            return utf8length;
        },
        calculateLength: function (str) {
            /*
             *  Most of this function's code has been lifted from Solutions Infini
             *  URL: https://alerts.solutionsinfini.com/system/components/com_sms/assets/send.js
             *  Line: 62 
             *  Method: sms.getLength
             */
            var regx = /[^\u0000-\u00ff]/;
            var patt = new RegExp(regx);
            var res = patt.test(str);
            var len = str.length;
            var udh = 153,
                sms = 160,
                unicode = false,
                credits = 1;
            if (res) {
                udh = 134;
                sms = 140;
                len = len * 2;
                unicode = true
            } else {
                var len = $scope.data.countUtf8(str);
                len -= (str.match(new RegExp('�', 'g')) || []).length;
            }
            if (len > sms) {
                credits = Math.ceil(len / udh);
            }
            return {
                units: credits,
                chrLength: len
            }
        },
        lengthCheck: function () {
            var smsData;
            if ($scope.data.trackingCode && $scope.data.sms.isTrackingEnabled) {
                smsData = $scope.data.calculateLength($scope.data.sms.message + $scope.data.trackingCode)
                $scope.data.sms.chrLength = smsData.chrLength;
            } else {
                smsData = $scope.data.calculateLength($scope.data.sms.message);
                $scope.data.sms.chrLength = smsData.chrLength;
            }
            if (!$scope.data.sms.message) {
                $scope.data.sms.unit = 0;
            } else {
                $scope.data.sms.unit = smsData.units;
            }
        },
        setCohortData: function () {
            if ($scope.data.sms.notVisitedSince) {
                $scope.data.sms.fdate = moment().subtract(2, 'years');
                $scope.data.sms.tdate = $scope.data.cohort_date;
            } else {
                $scope.data.sms.fdate = $scope.data.cohort_date;
                $scope.data.sms.tdate = moment();
            }
        },
        setMessageContent: function (content) {
            $scope.data.sms.message = content;
            $scope.data.sms.creat_date = {
                date: moment(),
                formated_time: moment().format("LT")
            }
            $scope.data.lengthCheck();
        },
        staticDetails: service.getSMSStaticDetails().then(function (response) {
            $scope.data.staticDetails = response;
        }),

    };

    $scope.saveSMS = function (sms, formName) {

        var confirmation = '';

        if (!$scope.routerFilters.selected_PartnerId) {
            angular.element('[ng-model="routerFilters.selected_PartnerId"]').parent().addClass('has-error');
            messageService.showError('Please select a brand for this campaign!');
            return;
        } else {
            angular.element('[ng-model="routerFilters.selected_PartnerId"]').parent().removeClass('has-error');
        }

        if (!sms.senderId) {
            angular.element('[ng-model="data.sms.senderId"]').parent().addClass('has-error');
            formName.senderId.$error.error = true;
            formName.senderId.$error.error = 'Sender Id field can not be empty!';
            return;
        } else {
            angular.element('[ng-model="data.sms.senderId"]').parent().removeClass('has-error');
            formName.senderId.$error.error = false;
        }

        if (!sms.message) {
            angular.element('[ng-model="data.sms.message"]').parent().addClass('has-error');
            formName.messageText.$error.error = true;
            formName.messageText.$error.error = 'Message field can not be empty!';
            return;
        } else {
            angular.element('[ng-model="data.sms.message"]').parent().removeClass('has-error');
            formName.messageText.$error.error = false;
        }
        
       //if (sms.senderId != 'BULKSMS' && !sms.isTrackingEnabled) {
       //    angular.element('[ng-model="data.sms.isTrackingEnabled]').parent().addClass('has-error');
       //    formName.isTrackingEnabled.$error.error = true;
       //    formName.isTrackingEnabled.$error.error = 'Please check on enable tracking!';
       //    return;
       //} else {
       //    angular.element('[ng-model="data.sms.isTrackingEnabled"]').parent().removeClass('has-error');
       //    formName.isTrackingEnabled.$error.error = false;
       //}

        if (sms.isTrackingEnabled && sms.trackingUrl == '') {
            angular.element('[ng-model="data.sms.trackingUrl]').parent().addClass('has-error');
            formName.trackingUrl.$error.error = true;
            formName.trackingUrl.$error.error = 'Please check on enable tracking!';
            return;
        }
        if (sms.isTrackingEnabled && !isValidUrl(sms.trackingUrl)) {
            angular.element('[ng-model="data.sms.trackingUrl]').parent().addClass('has-error');
            formName.trackingUrl.$error.error = true;
            formName.trackingUrl.$error.error = 'Please enter correct url!';
            return;
        } else {
            angular.element('[ng-model="data.sms.trackingUrl"]').parent().removeClass('has-error');
            formName.trackingUrl.$error.error = false;
        }


        if (sms.dataBase != 'upload' && sms.cities && sms.cities.length == 0) {
            angular.element('[ng-model="data.sms.cities"]').closest('.ui-select-input-group').addClass("error");
            formName.city_filter.$error.error = true;
            formName.city_filter.$error.error = 'Please enter cities!';
            return;
        } else if (sms.dataBase != 'upload' && !sms.hasOwnProperty('cities')) {
            angular.element('[ng-model="data.sms.cities"]').closest('.ui-select-input-group').addClass("error");
            formName.city_filter.$error.error = true;
            formName.city_filter.$error.error = 'Please enter cities!';
            return;
        } else if (sms.dataBase != 'upload') {
            angular.element('[ng-model="data.sms.cities"]').closest('.ui-select-input-group').removeClass("error");
            formName.city_filter.$error.error = false;
        }

        if (sms.campaignType == '10') {

            if (sms.dataBase == 'account') {
                if (sms.cities && sms.cities.length > 0 && sms.locations && sms.locations.length == 0) {
                    angular.element('[ng-model="data.sms.locations"]').closest('.ui-select-input-group').addClass("error");
                    formName.store_filter.$error.error = true;
                    formName.store_filter.$error.error = 'Please enter locations!';
                    return;
                }
                if (sms.cities && sms.cities.length > 0 && !sms.hasOwnProperty('locations')) {
                    angular.element('[ng-model="data.sms.locations"]').closest('.ui-select-input-group').addClass("error");
                    formName.store_filter.$error.error = true;
                    formName.store_filter.$error.error = 'Please enter locations!';
                    return;
                } else {
                    angular.element('[ng-model="data.sms.locations"]').closest('.ui-select-input-group').removeClass("error");
                    formName.store_filter.$error.error = false;
                }
            }


            //if (!sms.send_on || sms.send_on == '') {
            //    angular.element('[ng-model="data.sms.send_on"]').parent().addClass('has-error');
            //    formName.sendNowDate.$error.error = true;
            //    formName.sendNowDate.$error.error = 'Please select a send date for your message!';
            //    return;
            //} else {
            //    angular.element('[ng-model="data.sms.send_on"]').parent().removeClass('has-error');
            //    formName.sendNowDate.$error.error = false;
            //}

            if (sms.dataBase == 'upload' && sms.numbers.isEmpty) {
                angular.element('[result="data.sms.numbers"]').parent().addClass('has-error');
                formName.csvFileName.$error.error = true;
                formName.csvFileName.$error.error = 'Please upload a csv file!';
                return;
            } else if (sms.dataBase == 'upload' && !sms.numbers.isEmpty) {
                angular.element('[result="data.sms.numbers"]').parent().removeClass('has-error');
                formName.csvFileName.$error.error = false;
            }

            //confirmation = 'This message will trigger at ' + moment(sms.send_on).format('LLLL');

        } else if (sms.campaignType == '11') {
            if (!sms.schedule || !sms.schedule.runningTime || sms.schedule.runningTime == '') {
                angular.element('[ng-model="data.sms.schedule.runningTime"]').parent().addClass('has-error');
                formName.runningTime.$error.error = true;
                formName.runningTime.$error.error = 'Please select time of the day for your messages to be sent at!';
                return;
            } else {
                angular.element('[ng-model="data.sms.schedule.runningTime"]').parent().removeClass('has-error');
                formName.runningTime.$error.error = false;
            }

            if (sms.schedule.runningDays.monday == false && sms.schedule.runningDays.tuesday == false
            && sms.schedule.runningDays.wednesday == false && sms.schedule.runningDays.thursday == false
            && sms.schedule.runningDays.friday == false && sms.schedule.runningDays.saturday == false
            && sms.schedule.runningDays.sunday == false) {
                angular.element('[ng-model="data.sms.schedule.runningDays.monday"]').parent().addClass('has-error');
                formName.runningDays.$error.error = true;
                formName.runningDays.$error.error = 'Please select days!';
                return;
            } else {
                angular.element('[ng-model="data.sms.schedule.runningDays.monday"]').parent().removeClass('has-error');
                formName.runningDays.$error.error = false;
            }

            if (!sms.dailyBudget || sms.dailyBudget == '') {
                angular.element('[ng-model="data.sms.dailyBudget"]').parent().addClass('has-error');
                formName.dailyBudget.$error.error = true;
                formName.dailyBudget.$error.error = 'Please enter the daily budget for your campaign!';
                return;
            } else {
                angular.element('[ng-model="data.sms.dailyBudget"]').parent().removeClass('has-error');
                formName.dailyBudget.$error.error = false;
            }

            //confirmation = 'This message will be triggered at ' + moment(sms.schedule.runningTime).format('LT') + ' everyday, till ' + moment(sms.schedule.runTill).format('LL');
        }

        /*showConfirmationDialog($modal, {
            title: 'Are you sure?',
            textContent: confirmation,
            type: 'alert-warning',
            ok: 'Send'
        },
            function () {
                ajaxCall.post('/Kelp/GetSMSEstimates', {
                    campaign: sms
                }).then(function (response) {
                    if (response.status == 0) {
                        result = JSON.parse(response.data);
                        if (result.data && result.status == 0) {
                            //sms.parameters = sms.parameters || {};
                            var minBalance = 0
                            if (result.data.hasOwnProperty('cost')) {
                                minBalance = sms.estimatedCost = result.data.cost;
                            } else if (result.data.hasOwnProperty('dailyTargets')) {
                                minBalance = sms.dailyBudget = result.data.dailyBudget;
                            }

                            var data = {
                                sms: sms,
                                accountInfo: $rootScope.accountInfo
                            };

                            ajaxCall.post('/Kelp/getAccountFinalBalance').then(function (response) {
                                if (response.status == 0) {
                                    if (response.data > minBalance) {
                                        ajaxCall.post('/Kelp/SaveSMS', data).then(function (response) {
                                            if (response.status == 2) {
                                                messageService.showError(response.msg);
                                            } else $state.go('landing.smsCampaignReports');
                                        });
                                    } else {
                                        messageService.showError('You have insufficient balance for this campaign!');
                                    }
                                } else {
                                    messageService.showError(response.msg);
                                }

                            });



                        } else if (result.status == 1) {
                            messageService.showError(result.msg);
                        } else {
                            messageService.showError("Something went wrong please try again later");
                        }
                    } else {
                        messageService.showError(response.msg);
                    }
                });


            }
        );*/



        $scope.modalInstance = $modal.open({
            animation: 'true',
            templateUrl: '/reviewModalForm.html',
            controller: 'modelController',
            scope:$scope,
            resolve: {
                data: function () {
                    if (sms.campaignType == 10) {
                        sms.send_on = moment();
                    }
                    return {
                        data: {
                            modelSendNowDate : {
                                singleDatePicker: true,
                                ranges: {
                                    'Now': [moment(), moment()]
                                },
                                locale: {
                                    format: "LLL",
                                    applyLabel: "Apply",
                                    cancelLabel: "Cancel",
                                    customRangeLabel: "Custom",
                                },
                                showCustomRangeLabel: false,
                                timePicker: true,
                                minDate: moment(),
                                maxDate: moment().add(6, 'days'),
                                startDate: moment(),
                                endDate: moment(),
                                eventHandlers: {
                                    'show.daterangepicker': function (ev, picker) {
                                        picker.container[0].firstElementChild.style.display = 'block';
                                    },
                                    'apply.daterangepicker': function (ev, picker) {
                                        ev.currentTarget.value = moment(picker.startDate).format('LLL');
                                    }
                                }
                            },
                            sms: sms
                        },
                        cancel:function(){
                            $modalInstance.dismiss('cancel');
                        }
                    };
                }
            }
        });
    }

    $scope.saveReviewForm = function (sms, formName) {
        document.forms["reviewModalForm"].elements.send.disabled = true;
        document.forms["reviewModalForm"].elements.cancel.disabled = true;
        if (sms.campaignType == '10') {
            if (!sms.send_on || sms.send_on == '') {
                angular.element('[ng-model="data.sms.send_on"]').parent().addClass('has-error');
                formName.modelSendNowDate.$error.error = true;
                formName.modelSendNowDate.$error.error = 'Please select a send date for your message!';
                return;
            } else {
                angular.element('[ng-model="data.sms.send_on"]').parent().removeClass('has-error');
                formName.modelSendNowDate.$error.error = false;
            }
        }
        if (!sms.campaignName || sms.campaignName == '') {
            angular.element('[ng-model="data.sms.campaignName"]').parent().addClass('has-error');
            formName.campaignName.$error.error = true;
            formName.campaignName.$error.error = 'Please select a send date for your message!';
            return;
        } else {
            angular.element('[ng-model="data.sms.campaignName"]').parent().removeClass('has-error');
            formName.campaignName.$error.error = false;
        }
        
        ajaxCall.post('/Kelp/GetSMSEstimates', {
            campaignStr: JSON.stringify(sms)
        }).then(function (response) {
            if (response.status == 0) {
                result = JSON.parse(response.data);
                if (result.data && result.status == 0) {
                    //sms.parameters = sms.parameters || {};
                    var minBalance = 0
                    if (result.data.hasOwnProperty('cost')) {
                        minBalance = sms.estimatedCost = Math.ceil(result.data.cost);
                    } else if (result.data.hasOwnProperty('dailyTargets')) {
                        minBalance = sms.dailyBudget = Math.floor(result.data.dailyBudget);
                    }

                    var data = {
                        smsString: JSON.stringify(sms),
                        accountInfo: $rootScope.accountInfo
                    };

                    ajaxCall.post('/Kelp/getAccountFinalBalance').then(function (response) {
                        if (response.status == 0) {
                            if (response.data > minBalance) {
                                ajaxCall.post('/Kelp/SaveSMS', data).then(function (response) {
                                    if (response.status == 2) {
                                        messageService.showError(response.msg);
                                    } else {
                                        $scope.modalInstance.close();
                                        $state.go('landing.smsCampaignReports');
                                    }
                                });
                            } else {
                                messageService.showError('You have insufficient balance for this campaign!');
                            }
                        } else {
                            messageService.showError(response.msg);
                        }

                    });



                } else if (result.status == 1) {
                    messageService.showError(result.msg);
                } else {
                    messageService.showError("Something went wrong please try again later");
                }
            } else {
                messageService.showError(response.msg);
            }
        });
    }

    /********** Watchers */

    $scope.$watch('data.sms.senderId', function (newVal, oldVal) {
        if (newVal !== oldVal) {
            if (newVal == 'BULKSMS') {
                $scope.data.sms.isTrackingEnabled = false;
                $scope.data.sms.trackingUrl = '';
                angular.element('[ng-model="data.sms.isTrackingEnabled"]').attr("disabled", true);
                angular.element('[ng-model="data.sms.trackingUrl"]').attr("disabled", true);
            } else {
                $scope.data.sms.isTrackingEnabled = true;
                ajaxCall.get('/Client/GetLinqUrlForPartner').then(function (response) {
                    if (response.status == 0 && response.data && response.data.url) {
                        $scope.data.sms.trackingUrl = response.data.url;
                    } else {
                        $scope.data.sms.trackingUrl = 'http://interaction.i2e1.com/Home/sms/{0}';
                    }
                });
                

                //angular.element('[ng-model="data.sms.isTrackingEnabled"]').removeAttr("disabled");
                angular.element('[ng-model="data.sms.trackingUrl"]').removeAttr("disabled");
                $scope.data.trackingCode = ' http://msg.mn/xxxxxx/{advanced}';
            }
            $scope.data.lengthCheck();
        }
    }, true);


    ajaxCall.get('/Client/GetFilterOptions?filter=shop_city').then(function (response) {
        var array = [];
        angular.forEach(response.data.sort(), function (value) {
            var obj = {}
            if (value != '') {
                obj.city = value
            }
            array.push(obj);
        });
        $scope.availableCities = array;
    });

    $scope.getLocationsForCity = function ($item) {

        $scope.$parent.positiveNases = [];
        var qObj = angular.copy(queryObject);
        qObj.pageNumber = 1;
        qObj.pageSize = 999;
        if ($scope.data.sms.cities && $scope.data.sms.cities.length > 0) {
            qObj.city = '';
            angular.forEach($scope.data.sms.cities, function (city) {
                qObj.city += city + ',';
            });
            qObj.city = qObj.city.slice(0, -1)
        }

        qObj.storeTags = "%";
        $scope.userBrand.then(function () {
            if ($scope.routerFilters.selected_PartnerId && $scope.routerFilters.selected_PartnerId > 0)
                qObj.partnerId = $scope.routerFilters.selected_PartnerId;
            service.getLocationsForAdmin(qObj).then(function (locations) {
                $timeout(function () {
                    var arr = [];
                    angular.forEach(locations, function (store, index) {
                        if (store.storeName && store.nasid > 0) arr.push(store);
                    });
                    $scope.$parent.positiveNases = arr;
                }, 500);
            });
        })
    }

});

app.controller('smsCampaignReportsController', function ($scope, $rootScope, service, ajaxCall, $modal, messageService, $state, $timeout) {
    $scope.smsHistory = {}
    $scope.smsHistory.list = [];
    $scope.smsHistory.pageNumber = 0;
    $scope.smsHistory.pageSize = 10;
    $scope.smsHistory.filters = {};
    $scope.smsHistory.filters.date = new Date();
    $scope.smsHistory.filters.name = "";
    $scope.smsHistory.sortbylist = [{ name: "Name", value: "name" }, { name: "Date", value: "date" }];
    $scope.smsHistory.sortBy = "";
    $scope.loadMoreSMS = function (scrollOn) {
       $rootScope.userDara.then(function (result) {
           loadSmsHistory(scrollOn);
       });
    }

    $scope.consolidateNow = function ($event, campaign) {
        $event.stopPropagation();
        ajaxCall.get('/Client/ConsolidateNow?campaignId=' + campaign.id).then(function (response) {
            $scope.getSmsDetailedReport(campaign);

        });
    }
    $scope.enableCampaign = function ($event, campaign, enable) {
        $event.stopPropagation();
        ajaxCall.get('/Client/EnableCampaign?campaignId=' + campaign.id + '&enable=' + enable)
            .then(function (response) {
                campaign.enabled = enable;
            });
    }
    $scope.editContent = function (event, sms) {
        sms.isEditable = !sms.isEditable;
        $('#camp_report_' + sms.id + ' textarea').focus();
        event.stopPropagation();
        //ajaxCall.get('/Client/EnableCampaign?campaignId=' + campaignId + '&enable=' + enable);
    }

    var loadSmsHistory = function (scrollOn) {
        //if ($scope.smsHistory.infiniteScrollDisabled) return;
        //$scope.smsHistory.infiniteScrollDisabled = true;
        if (scrollOn) {
            $scope.smsHistory.pageNumber = $scope.smsHistory.pageNumber + 1;
        } else {
            $scope.smsHistory.pageNumber = 1;
            $scope.smsHistory.list = [];
        }
        ajaxCall.post('/Kelp/GetCampaignHistory', {
            selectedName: $scope.smsHistory.filters.name,
            selectedDate: $scope.smsHistory.filters.date,
            sortBy: $scope.smsHistory.sortBy,
            pageNumber: $scope.smsHistory.pageNumber,
            pageSize: $scope.smsHistory.pageSize,
            accountInfo: $rootScope.accountInfo

        }).then(function (response) {
            angular.forEach(response.data, function (sms) {
                if (sms.parameters != null) {
                    if (sms.hasOwnProperty("parameters") && sms.parameters.hasOwnProperty("notVisitedSince")) {
                        sms.parameters.notVisitedSinceDate = moment(sms.parameters.tdate).format('LL');
                    } else {
                        sms.parameters.visitedSince = true;
                        sms.parameters.visitedSinceDate = moment(sms.parameters.fdate).format('LL');
                    }

                    if (sms.campaignType == 11) {
                        sms.parameters.schedule.rundays = '';
                        angular.forEach(sms.parameters.schedule.running_days, function (value, day) {
                            if (value)
                                sms.parameters.schedule.rundays += day + ', ';
                        });
                        sms.parameters.schedule.runtime = moment().startOf('day').add(sms.parameters.schedule.running_time, 'minutes').format('LT');
                    } else if (sms.campaignType == 10) {
                        if (moment(sms.scheduleStartTime).add(330, 'minutes') < moment()) {
                            sms.hideEnableAction = true;
                        }
                        sms.parameters.scheduledAt = moment(sms.scheduleStartTime).add(330, 'minutes').format('lll');
                    }
                    sms.scheduleStartTimeFormatted = moment(sms.scheduleStartTime).add(330, 'minutes').format('lll');
                    sms.parameters.scheduledAt = moment(sms.scheduleStartTime).add(330, 'minutes').format('lll');
                }

                sms.currentPage = 1;
                sms.totalItems = 0;
                sms.maxSize = 5;
                sms.numPerPage = 5;

                $scope.smsHistory.list.push(sms);
            });
            if (response.data.length == 0) {
                $scope.smsHistory.infiniteScrollDisabled = true;
            } else {
                $scope.smsHistory.infiniteScrollDisabled = false;
            }
        });
    }

    $scope.getSmsDetailedReport = function (sms) {
        ajaxCall.get('/Kelp/GetCampaignReport?campaignId=' + sms.id + '&pageNum=' + sms.currentPage + '&pageSize=' + sms.numPerPage).then(function (response) {
            if (response.data && Object.keys(response.data).length > 0) {
                sms.detailedReport = response.data;
                sms.totalItems = sms.detailedReport[Object.keys(sms.detailedReport)[0]].total_items;
            } else {
                sms.detailedReport = [];
                sms.totalItems = 0;
            }
        });

        ajaxCall.get('/Kelp/GetCampaignDetails?campaignId=' + sms.id).then(function (response) {
            if (response.data && Object.keys(response.data).length > 0) {
                sms.campaignDetails = response.data;
                sms.smsTriggered = sms.campaignDetails["smsTriggered"];
                sms.smsDelivered = sms.campaignDetails["smsDelivered"];
                sms.smsClicks = sms.campaignDetails["smsClicks"];
                sms.consumption = sms.campaignDetails["consumption"]; sms.consumption.toFixed(2);
                sms.parameters.unbilled_amount = sms.campaignDetails["unbilledAmt"];
                sms.parameters.estimated_cost = sms.campaignDetails["estimatedCost"];
            }
        });
    }
});

app.controller('accountStatementController', function ($scope, $rootScope, service, ajaxCall, $modal, messageService, $state, $timeout) {

    $scope.pagination = { currentPage: 1, itemsPerPage:5, maxSize:5 };
    
    ajaxCall.get('/Kelp/getAccountStatement?currentPage=' + $scope.pagination.currentPage + '&pageSize=' + $scope.pagination.itemsPerPage).then(function (response) {
        $scope.finalBalance = response.data.finalBalance;
        if (response.data && response.data.balnceSheet && response.data.balnceSheet.length > 0) {
            $scope.pagination.totalItems = response.data.balnceSheet[0].totalItems;
        }
        
        $scope.pagination.filteredItems = response.data.balnceSheet;
    });

    $scope.pageChanged = function () {
        ajaxCall.get('/Kelp/getAccountStatement?currentPage=' + $scope.pagination.currentPage + '&pageSize=' + $scope.pagination.itemsPerPage).then(function (response) {
            $scope.pagination.filteredItems = response.data.balnceSheet;
        });
    };
    
    ajaxCall.get('/Client/GetPricing').then(function (response) {
        $scope.products = response.data;
    });
});