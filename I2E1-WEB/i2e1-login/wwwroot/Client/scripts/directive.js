app.filter("trustUrl", function ($sce) {
    return function (Url) {
        console.log(Url);
        return $sce.trustAsResourceUrl(Url);
    };
});

/**
 *  This directive is used for reading csv files.
 *  Source: https://github.com/bahaaldine/angular-csv-import
 **/

app.directive('csvFileUpload', function ($rootScope) {
    return {
        restrict: 'EA',
        scope: {
            content: '=?',
            header: '=?',
            mobile: '=?',
            headerVisible: '=?',
            separator: '=?',
            separatorVisible: '=?',
            result: '=?',
            encoding: '=?',
            encodingVisible: '=?',
            accept: '=?',
            acceptSize: '=?',
            acceptSizeExceedCallback: '=?',
            callback: '=?',
            lockImportSameFile: '=?'
        },
        template: '<div class="form-group csv-file-contanier">'
            + '    <input type="file" class="input-ghost" style="visibility:hidden; height:0" name="csvFile">'
            + '    <div class="input-group input-file" name="csvFile">'
            + '        <span class="input-group-btn">'
            + '        	<button class="btn btn-primary btn-choose" type="button" ng-click="onClick($event)">Choose</button>'
            + '        </span>'
            + '        <input type="text" ng-click="onClick($event)" name="csvFileName" ng-model="filename" placeholder="Choose a file..." readonly style="cursor: pointer;height: 37px;background: #f5f5f5;">'
            + '     </div>'
            + '</div>',
        link: function (scope, element, attrs) {
            var input = angular.element(element[0].querySelector('input[type="file"]'));
            var inputContainer = angular.element(element[0].querySelector('csv-file-contanier'));

            //element.find('.btn-choose').on('click', function () {
            //    element.find('.input-ghost').click();
            //});
            //element.find('.input-ghost').change(function () {
            //    element.find("input[type='text']").val((element.find('.input-ghost').val()).split('\\').pop());
            //});

            if (input) {
                //input.removeClass("ng-show");
                //input.addClass("ng-hide");
                //if (inputContainer) {
                //    var errorSpacer = angular.element(inputContainer[0].querySelector('div.md-errors-spacer'));
                //    if (errorSpacer) {
                //        errorSpacer.remove();
                //    }
                //}
                scope.onClick = function () {
                    input.click();
                };
            }
            element.on('change', function (onChangeEvent) {
                if (!onChangeEvent.target.files.length) {
                    return;
                }

                if (onChangeEvent.target.files[0].size > scope.acceptSize) {
                    if (typeof scope.acceptSizeExceedCallback === 'function') {
                        scope.acceptSizeExceedCallback(onChangeEvent.target.files[0]);
                    }
                    return;
                }

                scope.filename = onChangeEvent.target.files[0].name;
                var reader = new FileReader();
                reader.onload = function (onLoadEvent) {
                    scope.$apply(function () {
                        var content = {
                            csv: onLoadEvent.target.result.replace(/\r\n|\r/g, '\n'),
                            header: scope.header,
                            separator: scope.separator,
                            mobile: scope.mobile
                        };
                        scope.content = content.csv;
                        scope.result = csvToJSON(content);
                        scope.result.filename = scope.filename;
                        scope.$$postDigest(function () {
                            if (typeof scope.callback === 'function') {
                                if (scope.callback != null) {
                                    scope.callback(onChangeEvent);
                                }
                            }
                        });
                    });
                };

                if ((onChangeEvent.target.type === "file") && (onChangeEvent.target.files != null || onChangeEvent.srcElement.files != null)) {
                    reader.readAsText((onChangeEvent.srcElement || onChangeEvent.target).files[0], scope.encoding);
                } else {
                    if (scope.content != null) {
                        var content = {
                            csv: scope.content,
                            header: !scope.header,
                            separator: scope.separator
                        };
                        scope.result = csvToJSON(content);
                        scope.$$postDigest(function () {
                            if (typeof scope.callback === 'function') {
                                if (scope.callback != null) {
                                    scope.callback(onChangeEvent);
                                }
                            }
                        });
                    }
                }

                if (!scope.lockImportSameFile) {
                    angular.element(document).find('.ng-isolate-scope input[type="file"]')[0].value = null;
                }
            });

            var csvToJSON = function (content) {
                var lines = content.csv.split(new RegExp('\n(?![^"]*"(?:(?:[^"]*"){2})*[^"]*$)'));
                var result = [];
                var start = 0;
                var columnCount = lines[0].split(content.separator).length;

                var headers = [];
                if (content.header) {
                    headers = lines[0].split(content.separator);
                    start = 1;
                }

                for (var i = start; i < lines.length; i++) {
                    var obj = {};
                    var currentline = lines[i].split(new RegExp(content.separator + '(?![^"]*"(?:(?:[^"]*"){2})*[^"]*$)'));
                    if (currentline.length === columnCount) {
                        if (content.header) {
                            for (var j = 0; j < headers.length; j++) {
                                if (isValidIndianMobile(currentline[j]))
                                    obj[headers[j]] = cleanCsvValue(currentline[j]);
                            }
                        } else {
                            if (content.mobile) {
                                for (var k = 0; k < currentline.length; k++) {
                                    if (isValidIndianMobile(currentline[k]))
                                        obj.mobile = cleanCsvValue(currentline[k]);
                                }
                            } else {
                                for (var k = 0; k < currentline.length; k++) {
                                    if (isValidIndianMobile(currentline[k]))
                                        obj[k] = cleanCsvValue(currentline[k]);
                                }
                            }

                        }
                        if (Object.keys(obj).length > 0 && obj.constructor === Object)
                            result.push(obj);
                    }
                }
                return result;
            };

            var cleanCsvValue = function (value) {
                return value
                    .replace(/^\s*|\s*$/g, "") // remove leading & trailing space
                    .replace(/^"|"$/g, "") // remove " on the beginning and end
                    .replace(/\s+/, "") // remove all white spaces.
                    .replace(/""/g, '"'); // replace "" with "

            };
        }
    }
});

app.directive('angularAutoComplete', function ($parse, $http, $compile) {
    return {
        restrict: 'EA',
        scope: {
            "id": "@id",
            "placeholder": "@placeholder",
            "selectedObject": "=selectedobject",
            "url": "@url",
            "titleField": "@titlefield",
            "descriptionField": "@descriptionfield",
            "imageField": "@imagefield",
            "inputClass": "@inputclass",
            "userPause": "@pause",
            "localData": "=localdata",
            "searchFields": "@searchfields",
            "minLengthUser": "@minlength",
            "inputtype": "@inputtype",
            "inputvalue": "=inputvalue",
            "placeDetails": "@placedetails",
            "someCtrlFn": "&callbackFn"
        },
        //template: '<div class="angucomplete-holder"><input id="{{id}}_value" ng-model="searchStr" type="text" placeholder="{{placeholder}}" class="{{inputClass}}" ng-keyup="keyPressed($event)" ng-model-options="{debounce:1000}"/>' +
        template: '<div class="angucomplete-holder"><input id="{{id}}_value" ng-model="searchStr" type="text" placeholder="{{placeholder}}" class="{{inputClass}}" ng-keyup="keyPressed($event)"/>' +
            '<div id="{{id}}_dropdown" class="angucomplete-dropdown" ng-if="showDropdown">' +
            '<div class="angucomplete-searching" ng-show="searching">Searching...</div>' +
            '<div ng-if="inputtype == \'locationfinder\'" class="angucomplete-searching" ng-show="!searching && (!results || results.length == 0)">No results found <a ng-click="verifyFromGoogle({type:\'marketPlaceName\'},\'{{id}}_value\')">Verify from google</a></div>' +
            '<div ng-if="inputtype == \'brandName\'" class="angucomplete-searching" ng-show="!searching && (!results || results.length == 0)">No results found <a ng-click="verifyFromGoogle({type:\'brandName\'})">Search from google</a></div>' +
            '<div class="angucomplete-row" ng-repeat="result in results" ng-click="selectResult(result)" ng-mouseover="hoverRow()" ng-class="{\'angucomplete-selected-row\': $index == currentIndex}">' +
            '<div ng-if="result.image && result.image != \'\'" class="angucomplete-image-holder">' +
            '<img ng-src="{{result.image}}" class="angucomplete-image"/></div>' +
            '<span class="angucomplete-title">{{result.title}}</span>' +
            '<span ng-if="result.originalObject.vicinity && result.originalObject.vicinity != \'\'" class="angucomplete-description"> - {{result.originalObject.vicinity}}</span>' +
            '<span ng-if="result.originalObject.city && result.originalObject.city != \'\'" class="angucomplete-description"> - {{result.originalObject.city}}</span></div></div></div>',
        controller: function ($scope) {
            $scope.lastFoundWord = null;
            $scope.currentIndex = null;
            $scope.justChanged = false;
            $scope.searchTimer = null;
            $scope.searching = false;
            $scope.pause = 500;
            $scope.minLength = 3;
            $scope.searchStr = $scope.inputvalue;

            if ($scope.minLengthUser && $scope.minLengthUser != "") {
                $scope.minLength = $scope.minLengthUser;
            }

            if ($scope.userPause) {
                $scope.pause = $scope.userPause;
            }

            $scope.processResults = function (responseData) {
                if (responseData && responseData.length > 0) {
                    $scope.results = [];

                    var titleFields = [];
                    if ($scope.titleField && $scope.titleField != "") {
                        titleFields = $scope.titleField.split(",");
                    }

                    for (var i = 0; i < responseData.length; i++) {
                        // Get title variables
                        var titleCode = "";

                        for (var t = 0; t < titleFields.length; t++) {
                            if (t > 0) {
                                titleCode = titleCode + " + ' ' + ";
                            }
                            titleCode = titleCode + "responseData[i]." + titleFields[t];
                        }

                        // Figure out description
                        var description = "";

                        if ($scope.descriptionField && $scope.descriptionField != "") {
                            eval("description = responseData[i]." + $scope.descriptionField);
                        }

                        // Figure out image
                        var image = "";

                        if ($scope.imageField && $scope.imageField != "") {
                            eval("image = responseData[i]." + $scope.imageField);
                        }

                        var resultRow = {
                            title: eval(titleCode),
                            city: responseData[i].city,
                            description: description,
                            image: image,
                            originalObject: responseData[i]
                        }

                        $scope.results[$scope.results.length] = resultRow;
                    }


                } else {
                    $scope.results = [];
                }
            }

            $scope.searchTimerComplete = function (str) {
                // Begin the search

                if (str.length >= $scope.minLength) {
                    if ($scope.localData) {
                        var searchFields = $scope.searchFields.split(",");

                        var matches = [];

                        for (var i = 0; i < $scope.localData.length; i++) {
                            var match = false;

                            for (var s = 0; s < searchFields.length; s++) {
                                var evalStr = 'match = match || ($scope.localData[i].' + searchFields[s] + '.toLowerCase().indexOf("' + str.toLowerCase() + '") >= 0)';
                                eval(evalStr);
                            }

                            if (match) {
                                matches[matches.length] = $scope.localData[i];
                            }
                        }

                        $scope.searching = false;
                        $scope.processResults(matches);
                        $scope.$apply();


                    } else {
                        $http.get($scope.url + str, {}).
                            success(function (responseData, status, headers, config) {
                                $scope.searching = false;
                                $scope.processResults(responseData.data);
                            }).
                            error(function (data, status, headers, config) {
                                console.log("error");
                            });

                    }
                }

            }

            $scope.verifyFromGoogle = function (addressType, inputId) {
                var pyrmont = new google.maps.LatLng(28.5494489, 77.2001368);
                if (addressType.type == 'brandName') {
                    var searchInputs = document.getElementsByClassName("form-control-small");
                    var query = '';
                    for (i = 0; i < searchInputs.length; i++)
                        query += searchInputs[i].value;

                }
                else
                    var query = $scope.searchStr;
                var request = {
                    location: pyrmont,
                    radius: '500',
                    query: query
                };
                service = new google.maps.places.PlacesService(document.createElement('div'));
                service.textSearch(request, function callback(results, status) {
                    if (status == google.maps.places.PlacesServiceStatus.OK) {
                        if (results.length > 0) {
                            var responseArray = new Array();
                            angular.forEach(results, function (address) {
                                var addressObject = {}
                                addressObject[addressType.type] = address.name;
                                addressObject.googlePlaceId = address.place_id;
                                addressObject.latitude = address.geometry.location.lat;
                                addressObject.longitude = address.geometry.location.lng;

                                service.getDetails({
                                    placeId: address.place_id
                                }, function (place, status) {

                                    if (status === google.maps.places.PlacesServiceStatus.OK) {
                                        //addressObject.placeDetails = place;
                                        var placeDetails = new getGooglePlaceDetailsService(place);
                                        addressObject.vicinity = placeDetails.getVicinity(place) ? placeDetails.getVicinity(place) : null;
                                        addressObject.googleCategories = placeDetails.googleCategories(place) ? placeDetails.googleCategories(place) : null;
                                        addressObject.subLocality = placeDetails.getSubLocality(place) ? placeDetails.getSubLocality(place) : null;
                                        addressObject.locality = placeDetails.getLocality(place) ? placeDetails.getLocality(place) : null;
                                        addressObject.city = placeDetails.getCity(place) ? placeDetails.getCity(place) : null;
                                        addressObject.state = placeDetails.getState(place) ? placeDetails.getState(place) : null;
                                        addressObject.stateCode = placeDetails.getStateCode(place) ? placeDetails.getStateCode(place) : null;
                                        addressObject.country = placeDetails.getCountry(place) ? placeDetails.getCountry(place) : null;
                                        addressObject.countryCode = placeDetails.getCountryShort(place) ? placeDetails.getCountryShort(place) : null;
                                        addressObject.pinCode = placeDetails.getPostCode(place) ? placeDetails.getPostCode(place) : null;
                                        addressObject.latitude = placeDetails.getLatitude(place) ? placeDetails.getLatitude(place) : null;
                                        addressObject.longitude = placeDetails.getLongitude(place) ? placeDetails.getLongitude(place) : null;
                                        addressObject.scope = 'google';
                                        if (addressType.type == 'marketPlaceName')
                                            addressObject.addressType = 'marketplace';
                                        else if (addressType.type == 'brandName')
                                            addressObject.addressType = 'storeaddress';
                                    }
                                });
                                responseArray.push(addressObject);
                            });
                            $scope.processResults(responseArray);
                            $scope.$apply();
                        }
                    } else if (status == google.maps.places.PlacesServiceStatus.ZERO_RESULTS) {
                        var responseArray = new Array();
                        var addressObject = {};

                        addressObject.vicinity = "No results found on google";
                        responseArray.push(addressObject);
                        $scope.processResults(responseArray);
                        $scope.$apply();
                        setTimeout(function () {
                            $scope.showDropdown = false;
                            $scope.$apply();
                        }, 500);
                    }
                });
            }

            $scope.hoverRow = function (index) {
                $scope.currentIndex = index;
            }

            $scope.keyPressed = function (event) {
                if (!(event.which == 38 || event.which == 40 || event.which == 13)) {
                    if (!$scope.searchStr || $scope.searchStr == "") {
                        $scope.showDropdown = false;
                    } else {

                        if ($scope.searchStr.length >= $scope.minLength) {
                            $scope.showDropdown = true;
                            $scope.currentIndex = -1;
                            $scope.results = [];

                            if ($scope.searchTimer) {
                                clearTimeout($scope.searchTimer);
                            }

                            $scope.searching = true;

                            $scope.searchTimer = setTimeout(function () {
                                $scope.searchTimerComplete($scope.searchStr);
                            }, $scope.pause);
                        } else { // added for show no results if user deleted all the search string

                            var responseArray = new Array();
                            var addressObject = {};

                            addressObject.vicinity = "No results found";
                            responseArray.push(addressObject);
                            $scope.processResults(responseArray);
                            setTimeout(function () {
                                $scope.showDropdown = false;
                                $scope.$apply();
                            }, 500);
                        }
                    }

                } else {
                    event.preventDefault();
                }
            }

            $scope.selectResult = function (result) {
                $scope.searchStr = result.title;
                $scope.selectedObject = result;
                $scope.showDropdown = false;
                $scope.results = [];
                $scope.placeDetails = result;
                //$scope.$apply();
                $scope.someCtrlFn({
                    place: result
                });
            }
        },

        link: function ($scope, elem, attrs, ctrl) {

            elem.bind("keyup", function (event) {
                if (event.which === 40) {
                    if (($scope.currentIndex + 1) < $scope.results.length) {
                        $scope.currentIndex++;
                        $scope.$apply();
                        event.preventDefault;
                        event.stopPropagation();
                    }

                    $scope.$apply();
                } else if (event.which == 38) {
                    if ($scope.currentIndex >= 1) {
                        $scope.currentIndex--;
                        $scope.$apply();
                        event.preventDefault;
                        event.stopPropagation();
                    }

                } else if (event.which == 13) {
                    if ($scope.currentIndex >= 0 && $scope.currentIndex < $scope.results.length) {
                        $scope.selectResult($scope.results[$scope.currentIndex]);
                        $scope.$apply();
                        event.preventDefault;
                        event.stopPropagation();
                    } else {
                        $scope.results = [];
                        $scope.$apply();
                        event.preventDefault;
                        event.stopPropagation();
                    }

                } else if (event.which == 27) {
                    $scope.results = [];
                    $scope.showDropdown = false;
                    $scope.$apply();
                } else if (event.which == 8) {
                    $scope.selectedObject = null;
                    $scope.$apply();
                }
            });



            $(document).mouseup(function (e) {
                var container = $(elem);

                if (!container.is(e.target) // if the target of the click isn't the container...
                    && container.has(e.target).length === 0
                    && $scope.showDropdown // ... nor a descendant of the container
                    && !(e.target.id === attrs.excludeClick)) // do not execute when clicked on this 
                {
                    $scope.showDropdown = false;
                }
            });

        }
    };
});

app.directive('googleplace', [function () {
    return {
        require: 'ngModel',
        scope: {
            ngModel: '=',
            details: '=?',
            "someCtrlFn": "&callbackFn"
        },
        link: function (scope, element, attrs, model) {
            var options = {
                types: ['(cities)'],
                componentRestrictions: { country: 'in' }
            };

            scope.gPlace = new google.maps.places.Autocomplete(element[0], options);

            google.maps.event.addListener(scope.gPlace, 'place_changed', function () {
                var geoComponents = scope.gPlace.getPlace();
                var latitude = geoComponents.geometry.location.lat();
                var longitude = geoComponents.geometry.location.lng();
                var addressComponents = geoComponents.address_components;

                addressComponents = addressComponents.filter(function (component) {
                    switch (component.types[0]) {
                        case "locality": // city
                            return true;
                        case "administrative_area_level_1": // state
                            return true;
                        case "country": // country
                            return true;
                        default:
                            return false;
                    }
                }).map(function (obj) {
                    return obj.long_name;
                });

                //addressComponents.push(latitude, longitude);

                scope.$apply(function () {
                    scope.details = addressComponents; // array containing each location component
                    model.$setViewValue(scope.details[0]);
                    scope.someCtrlFn({ place: addressComponents });
                });

            });
        }
    };
}]);

app.directive('masonryWallDir', function () {
    return {
        controller: [
            '$scope',
            '$element',
            '$attrs',
            function ($scope, $element, $attrs) {

                var itemSelector,
                    masonryOptions;

                itemSelector = $attrs.masonryWallDir;

                //this element will contain the masonry
                this.wallContainer = $element;

                //we have some default options
                //then overwrite with passed in options
                //then overwrite with the necessary options
                this.masonryOptions = _.assign(
                    {},
                    $scope.$eval($attrs.masonryWallOptions),
                    {
                        itemSelector: itemSelector,
                    }
                );

                //place holder for masonry to be setup and shared across all ng-repeat directive scopes
                this.masonry = new Masonry(
                    this.wallContainer[0],
                    this.masonryOptions
                );

                this.masonry.bindResize();

                var self = this;
                this.debouncedReload = _.debounce(function () {
                    self.masonry.reloadItems();
                    self.masonry.layout();
                }, 100);
            }
        ]
    };
});

app.directive('masonryItemDir',
    function () {
        return {
            scope: true,
            require: '^masonryWallDir',
            link: function (scope, element, attributes, masonryWallDirCtrl) {
                imagesLoaded(element, function () {
                    setTimeout(function () {
                        if (scope.$first) {
                            masonryWallDirCtrl.masonry.prepended(element);
                        } else {
                            masonryWallDirCtrl.masonry.appended(element);
                        }
                    }, 500);
                });

                scope.$on('$destroy', masonryWallDirCtrl.debouncedReload);

            }
        };
    }
);

app.directive('createTags', function () {
    return {
        restrict: 'E',
        scope: {
            tagData: '='
        },
        link: function ($scope, element, attrs) {
            $scope.SplitTheString = function (CommaSepStr) {
                var ResultArray = null;

                if (CommaSepStr != null) {
                    var SplitChars = ',';
                    if (CommaSepStr.indexOf(SplitChars) >= 0) {
                        ResultArray = CommaSepStr.split(SplitChars);
                    }
                }
                return ResultArray;
            }

            $scope.$watch('tagData', function (newValue, oldValue) {
                if (newValue) {
                    var tagsArray = $scope.SplitTheString(newValue);
                    var htmlText = '<ul tag-data="' + newValue + '" class="tags">';
                    if (tagsArray && tagsArray.length > 0) {
                        angular.forEach(tagsArray, function (item, index) {
                            htmlText += '<li><span class="tag">' + item + '</span></li>';
                        });
                    } else {
                        htmlText += '<li><span class="tag">No Tags Found</span></li>';
                    }
                    htmlText += '</ul>';
                    element.replaceWith(htmlText);
                }

            }, true);
        }
    }
});

app.directive('featureToggle', function ($rootScope) {
    return {
        restrict: 'A',
        transclude: true,
        template: "<div class='myTransclude' ng-transclude></div>",
        link: function (scope, element, attrs) {
            var val = parseInt(scope.$eval(attrs.featureToggle));
            var enabled = $rootScope.featureEnabled(val);
            switch (enabled) {
                case 0:
                    element.css({ 'position': 'relative' });
                    var div = $('<div class="overlay"><div><span class="glyphicon glyphicon-lock"></span><br/><span class="overlayText">UPGRADE TO USE</span></div></div>');
                    element.append(div);
                    break;
                case -1:
                    element.remove();
                    break;
            }


            if (!enabled) {

            }
        }
    }
});

app.directive('dateInput', function (dateFilter) {
    return {
        require: 'ngModel',
        template: '<input type="date"></input>',
        replace: true,
        link: function (scope, elm, attrs, ngModelCtrl) {
            ngModelCtrl.$formatters.unshift(function (modelValue) {
                return dateFilter(modelValue, 'yyyy-MM-dd');
            });
            ngModelCtrl.$parsers.unshift(function (viewValue) {
                return new Date(viewValue);
            });
        },
    };
});

//app.directive('dateRangePicker', function ($rootScope) {
//     return {
//         restrict: 'A',
//         link: function (scope, element, attrs) {
//             var data = parseInt(scope.$eval(attrs.dateRangePicker));
//             $(element).daterangepicker(data);
//         }
//     }
//});

/*
 *  app.directive('datePicker', function ($rootScope) {
 *      return {
 *          restrict: 'A',
 *          link: function (scope, element, attrs) {
 *              var data = scope.$eval(attrs.datePicker);
 *              data = data || {};
 *              if (data.minDate) {
 *                  data.minDate = new Date(data.minDate);
 *              }
 *              if (data.maxDate) {
 *                  data.maxDate = new Date(data.maxDate);
 *              }
 *              data.singleDatePicker = true;
 *              $(element).daterangepicker(data, function (start, end) {
 *                  data.dateObject = start;
 *              });
 *          }
 *      }
 *  });
 */

app.directive('changeDetector', function ($rootScope) {
    return {
        restrict: 'A',
        link: function (scope, element, attrs) {
            var index = scope.$eval('$index');
            var field = attrs.changeDetector + '[' + index + ']';
            var previous = angular.copy(scope.$eval(field))
            scope.$watch(field, function (config) {
                config.changed = false;
                if (config.attribute) {
                    if (previous.value.value != config.value.value)
                        config.changed = true;
                }
                else {
                    if (previous.values) {
                        if (!angular.equals(previous.values, config.values))
                            config.changed = true;
                    }
                    else {
                        if (previous.value.value != config.value.value)
                            config.changed = true;
                    }
                }

                if (config.changed)
                    element.css('background-color', 'rgb(206, 255, 206)');
                else
                    element.css('background-color', 'transparent');
            });
        }
    }
});

app.directive('validate', function () {
    return {
        link: function ($scope, elm, attrs, ctrl) {
            if (!$scope.validationScope) $scope.validationScope = [];
            $scope.validationScope.push(elm);
            if (attrs.type === 'checkbox') {
                $scope.$watch(function ($scope) {
                    return $scope.option;
                }, function (val) {
                    if (!(val || elm.hasClass('ng-pristine')))
                        angular.element(elm).parent().addClass('error');
                    else {
                        angular.element(elm).parent().removeAttr('class');
                    }
                });
            }
        }
    };
});

app.directive('draggable', function () {
    return {
        link: function ($scope, elm, attrs, ctrl) {
            $(elm).draggable();
        }
    };
});

app.directive('advertiser', function ($compile) {
    return {
        link: function ($scope, elm, attrs, ctrl) {
            var divs = elm.children('div.ad-container');
            angular.forEach(divs, function (val, key) {
                angular.element(val).attr('addId', key);
                angular.element(val).attr('ng-click', "showAdd(" + key + ")");
            });
            $compile(elm.contents())($scope);
        }
    };
});

app.directive('dynamic', function ($compile) {
    return {
        restrict: 'A',
        replace: true,
        link: function (scope, ele, attrs) {
            scope.$watch(attrs.dynamic, function (html) {
                if (attrs.type === "q")
                    html = "Q. " + html;
                else if (attrs.type === "a")
                    html = "<b>A.</b> " + html;
                ele.html(html);
                $compile(ele.contents())(scope);
            });
        }
    };
});

app.directive('showtab', function () {
    return {
        link: function (scope, element, attrs) {
            element.click(function (e) {
                e.preventDefault();
                $(element).tab('show');
            });
        }
    };
});

app.directive('ngclipboard', function () {

    return {
        restrict: 'A',
        scope: {
            ngclipboardSuccess: '&',
            ngclipboardError: '&'
        },
        link: function (scope, element) {

            var clipboard = new Clipboard(element[0]);

            clipboard.on('success', function (e) {
                scope.$apply(function () {
                    scope.ngclipboardSuccess({
                        e: e
                    });
                });
            });

            clipboard.on('error', function (e) {
                scope.$apply(function () {
                    scope.ngclipboardError({
                        e: e
                    });
                });
            });
        }
    };
});

app.directive('drawstatscircle', function () {
    return {
        restrict: 'A',
        scope: {
            totaltext: '@',
        },
        link: function ($scope, el, attrs, ctrl) {
            attrs.$observe('totaltext', function (totaltext) {
                setTimeout(function () {
                    el.html('');
                    var options = {
                        percent: attrs.percent || 0,
                        size: attrs.size || 100,
                        lineWidth: attrs.line || 5,
                        rotate: attrs.rotate || 0,
                        bgColor: attrs.bgcolor || '#f5f5f5',
                        fhColor: attrs.fhcolor || '#acacac',
                        percentText: attrs.percenttext || 0,
                        totalText: attrs.totaltext || 0
                    }

                    var canvas = document.createElement('canvas');

                    if (typeof (G_vmlCanvasManager) !== 'undefined') {
                        G_vmlCanvasManager.initElement(canvas);
                    }

                    var ctx = canvas.getContext('2d');
                    canvas.width = canvas.height = options.size;
                    el.append(canvas);


                    if (options.percent > 0) {
                        percentTextWidth = ctx.measureText(options.percentText).width;
                        totalTextWidth = ctx.measureText(options.totalText).width;
                        ctx.font = '24px Roboto';
                        ctx.fillText(options.percentText, canvas.width / 2 - percentTextWidth, canvas.height / 2 - 12);
                        ctx.font = '14px Roboto';
                        ctx.fillText(options.totalText, canvas.width / 2 - totalTextWidth / 2, canvas.height / 2 + 12);
                    } else {
                        totalTextWidth = ctx.measureText(options.totalText).width;
                        ctx.font = '24px Roboto';
                        ctx.fillText(options.totalText, canvas.width / 2 - totalTextWidth, canvas.height / 2);
                    }

                    ctx.translate(options.size / 2, options.size / 2);
                    ctx.rotate((-1 / 2 + options.rotate / 180) * Math.PI);

                    var radius = (options.size - options.lineWidth) / 2;

                    var drawCircle = function (color, lineWidth, percent) {
                        percent = Math.min(Math.max(0, percent || 1), 1);
                        ctx.beginPath();
                        ctx.arc(0, 0, radius, 0, Math.PI * 2 * percent, false);
                        ctx.strokeStyle = color;
                        ctx.lineCap = 'round'; // butt, round or square
                        ctx.lineWidth = lineWidth;
                        ctx.stroke();

                    };
                    drawCircle(options.fhColor, 1, 100 / 100);
                    drawCircle(options.bgColor, options.lineWidth, options.percent / 100);
                }, 10);
            });
        }
    };
});

app.directive('confirmClick', function ($window) {
    var i = 0;
    return {
        restrict: 'A',
        priority: 1,
        compile: function (tElem, tAttrs) {
            var fn = '$$confirmClick' + i++,
                _ngClick = tAttrs.ngClick;
            tAttrs.ngClick = fn + '($event)';

            return function (scope, elem, attrs) {
                var confirmMsg = attrs.confirmClick || 'Are you sure?';

                scope[fn] = function (event) {
                    if ($window.confirm(confirmMsg)) {
                        scope.$eval(_ngClick, { $event: event });
                    }
                };
            };
        }
    };
});


app.directive('ngConfirmClick', function ($modal) {
    var i = 0;
    return {
        restrict: 'A',
        priority: 1,
        compile: function (tElem, tAttrs) {
            var fn = '$$ngConfirmClick' + i++,
                _ngClick = tAttrs.ngClick;
            tAttrs.ngClick = fn + '($event)';

            return function (scope, elem, attrs) {
                var options = JSON.parse(attrs.ngConfirmClick);
                scope[fn] = function (event) {
                    showConfirmationDialog($modal, options, function () {
                        scope.$eval(_ngClick, { $event: event });
                    });
                };
            };
        }
    };
});

app.directive("fileReader", function () {

    return {
        restrict: 'A',
        link: function (scope, element, attrs) {
            element.bind("change", function (e) {
                var file = (e.srcElement || e.target).files[0];

                var r = new FileReader();
                r.onload = function (e) {
                    scope.$eval(attrs.fileReader)(e.target.result);
                };
                r.readAsText(file);
            })
        }
    }
});

app.directive('onReadFile', function ($parse) {
    return {
        restrict: 'A',
        scope: false,
        link: function (scope, element, attrs) {
            var fn = $parse(attrs.onReadFile);

            element.on('change', function (onChangeEvent) {
                var reader = new FileReader();

                reader.onload = function (onLoadEvent) {
                    scope.$apply(function () {
                        fn(scope, {
                            $fileContent: onLoadEvent.target.result
                        });
                    });
                };
                reader.onerror = function (error) {
                    alert(error);
                }

                reader.readAsText((onChangeEvent.srcElement || onChangeEvent.target).files[0]);
            });
        }
    };
});

app.directive('collapseToggler', function () {
    return {
        restrict: 'A',
        link: function (scope, elem, attrs) {
            elem.parent().parent().on('mouseenter', function () {
                if (!$(this).find('.collapse').hasClass("in")) {
                    $(this).find('.collapse').toggleClass('in');
                    $(this).find('.collapse').attr("aria-expanded", "true");
                    $(this).find('.collapse').css('height', 'auto');
                }

            }).on('mouseleave', function () {
                if ($(this).find('.collapse').hasClass("in")) {
                    $(this).find('.collapse').removeClass('in');
                    $(this).find('.collapse').attr("aria-expanded", "false");
                    $(this).find('.collapse').css('height', 'auto');
                }

            });
        }
    };
});

app.directive('convertToNumber', function () {
    return {
        require: 'ngModel',
        link: function (scope, element, attrs, ngModel) {
            ngModel.$parsers.push(function (val) {
                return val != null ? parseInt(val, 10) : null;
            });
            ngModel.$formatters.push(function (val) {
                return val != null ? '' + val : null;
            });
        }
    };
});


/*********************************************************************************************/
/** This directive is used for rendering select date range button and label for from and to **/
/** Its need to modify for dates and initialization as well                                 **/
/*********************************************************************************************/

app.directive('dateRangeWidget', function ($parse) {
    return {
        restrict: "E",
        replace: true,
        transclude: false,
        compile: function (element, attrs) {
            //var modelAccessor = $parse(attrs.ngModel);
            var html = '<div class="row date-range-picker">'
                + '<div class="date-range-btn" title="Change date ranges">'
                + '<button id="' + attrs.id + '" class="btn btn-primary btn-lg" ng-model="dates">Select Range</button>'
                + '</div>'
                + '<div class="from-to-dates">'
                + '<div class="from" title="{{ dates.completeToDate}}">'
                + '<span>From:</span>'
                + '<div class="date">{{ dates.fromShowDate}}</div>'
                + '</div>'
                + '<div class="to" title="{{ dates.completeToDate}}">'
                + '<span>To:</span>'
                + '<div class="date">{{ dates.toShowDate}}</div>'
                + '</div>'
                + '</div>'
                + '</div>';

            var newElem = $(html);
            element.replaceWith(newElem);

        }
    };
});

app.directive('inputValidation', function () {
    return {
        require: 'ngModel',
        link: function (scope, element, attr, mCtrl) {
            scope.$watch(attr.ngModel, function (v) {
                if (v == null || v == "") {
                    element[0].parentNode.className += " has-error";
                } else {
                    element[0].parentNode.classList.remove('has-error');
                }
            });
        }
    };
});

app.directive('selectWithPagination', ['$rootScope', '$window', '$timeout', '$http', function ($rootScope, $window, $timeout, $http) {
    return {
        restrict: "A",
        scope: {
            'choicesPagingList': '=',
            'searchTerm': '=',
            'currentItems': '=',
            'pageNumber': '=',
            'pageSize': '=',
            'searchFuncation': '&'
        },

        link: function (scope, elem, attrs) {
            var cellContainer = elem[0].parentNode.parentNode.parentNode;
            var removeDuplicates = function (myArr, prop) {
                return myArr.filter((obj, pos, arr) => {
                    return arr.map(mapObj => mapObj[prop]).indexOf(obj[prop]) === pos;
                });
            }
            var addMoreLocation = function (searchTerm) {
                $(cellContainer).find('.glyphicon-refresh-animate').show(100, function () {
                    $(cellContainer).find('select2-choices').css({ 'opacity': '0.5', 'pointer-events': 'none' });
                });
                scrollEnabled = false;
                queryObjectCopy = angular.copy(queryObject);
                queryObjectCopy.pageNumber = scope.pageNumber;
                queryObjectCopy.pageSize = scope.pageSize;
                if (searchTerm != "" && isNaN(searchTerm)) {
                    queryObjectCopy.storeName = queryObjectCopy.storeNameAlias = queryObjectCopy.address = queryObjectCopy.state = searchTerm;
                } else {
                    queryObjectCopy.nasid = searchTerm
                }

                //if ($scope.routerFilters.selected_PartnerId && $scope.routerFilters.selected_PartnerId > 0)
                //    queryObjectCopy.partnerId = $scope.routerFilters.selected_PartnerId;
                var query = createQueryForLocationFilter(queryObjectCopy);
                $http({
                    method: 'GET',
                    url: '/Client/GetLocationsForAdmin' + query
                }).then(function successCallback(response) {
                    var locations = removeDuplicates(response.data.data, 'nasid');
                    //var locations = response.data.data;
                    locations.filter(function (store, index) {
                        if (store.storeName && store.nasid > 0) {
                            scope.choicesPagingList.push(store);
                            //scope.storesList.push(store);
                        }
                    });
                    $(cellContainer).find('.glyphicon-refresh-animate').hide(100, function () {
                        $(cellContainer).find('select2-choices').removeAttr('style');
                        scope.pageNumber += 1;
                        scrollEnabled = true;
                    });
                }, function errorCallback(response) {

                });
            }


            var checkWhenEnabled, handler, scrollDistance, scrollEnabled;
            $window = angular.element($window);
            elem.css('overflow-y', 'auto');
            elem.css('overflow-x', 'hidden');
            elem.css('height', 'inherit');
            scrollDistance = 0;
            if (attrs.infiniteScrollDistance != null) {
                scope.$watch(attrs.infiniteScrollDistance, function (value) {
                    return (scrollDistance = parseInt(value, 10));
                });
            }
            scrollEnabled = true;
            checkWhenEnabled = false;
            if (attrs.infiniteScrollDisabled != null) {
                scope.$watch(attrs.infiniteScrollDisabled, function (value) {
                    scrollEnabled = !value;
                    if (scrollEnabled && checkWhenEnabled) {
                        checkWhenEnabled = false;
                        return handler();
                    }
                });
            }
            $rootScope.$on('refreshStart', function () {
                elem.animate({
                    scrollTop: "0"
                });
            });
            handler = function () {
                var container, elementBottom, remaining, shouldScroll, containerBottom;
                container = $(elem.children()[0]);
                elementBottom = elem.offset().top + elem.height();
                containerBottom = container.offset().top + container.height();
                remaining = containerBottom - elementBottom;
                shouldScroll = remaining <= elem.height() * scrollDistance;
                if (shouldScroll && scrollEnabled) {
                    if ($rootScope.$$phase) {
                        //return scope.$eval(attrs.infiniteScroll);
                        addMoreLocation(scope.searchTerm);
                    } else {
                        //return scope.$apply(attrs.infiniteScroll);
                        addMoreLocation(scope.searchTerm);
                    }
                } else if (shouldScroll) {
                    return (checkWhenEnabled = true);
                }
            };
            elem.on('scroll', handler);
            scope.$on('$destroy', function () {
                return $window.off('scroll', handler);
            });

            scope.searchLocations = function () {
                scope.pageNumber = 1;
                scope.choicesPagingList = [];
                addMoreLocation(scope.searchTerm);
            }

            scope.searchFuncation({ theDirFn: scope.searchLocations });
            //$(document).bind("keyup", function (event) {
            //    addMoreLocation(scope.searchTerm);
            //});

            return $timeout((function () {
                if (attrs.infiniteScrollImmediateCheck) {
                    if (scope.$eval(attrs.infiniteScrollImmediateCheck)) {
                        return handler();
                    }
                } else {
                    return handler();
                }
            }), 0);
        }
    };
}]);

app.filter('propsFilter', function () {
    return function (items, props) {
        var out = [];

        if (angular.isArray(items)) {
            items.forEach(function (item) {
                var itemMatches = false;

                var keys = Object.keys(props);
                for (var i = 0; i < keys.length; i++) {
                    var prop = keys[i];
                    var text = props[prop].toString().toLowerCase();
                    if (item[prop].toString().toLowerCase().indexOf(text) !== -1) {
                        itemMatches = true;
                        break;
                    }
                }

                if (itemMatches) {
                    out.push(item);
                }
            });
        } else {
            // Let the output be the input untouched
            out = items;
        }

        return out;
    }
});

app.filter('exactFilter', function () {
    return function (items, props) {
        var out = [];

        if (angular.isArray(items)) {
            items.forEach(function (item) {
                var itemMatches = true;

                var keys = Object.keys(props);
                for (var i = 0; i < keys.length; i++) {
                    var prop = keys[i];
                    var vals = props[prop], found = false;
                    for (var j = 0; j < vals.length; ++j) {
                        if (item[prop] && item[prop].toString() === vals[j].toString()) {
                            found = true;
                            break;
                        }
                    }
                    if (!found && vals.length) {
                        itemMatches = false; break;
                    }
                }

                if (itemMatches) {
                    out.push(item);
                }
            });
        } else {
            // Let the output be the input untouched
            out = items;
        }

        return out;
    }
});

app.filter('orderBy', function () {
    return function (items, field, reverse) {
        var filtered = [];
        angular.forEach(items, function (item) {
            filtered.push(item);
        });
        filtered.sort(function (a, b) {
            return (a[field] > b[field] ? 1 : -1);
        });
        if (reverse) filtered.reverse();
        return filtered;
    };
});

app.filter('clientName', function () {
    return function (itm) {
        return itm.clientName;
    }
});

app.filter('unique', function () {
    // we will return a function which will take in a collection
    // and a keyname
    return function (collection, keyname) {
        // we define our output and keys array;
        var output = [],
            keys = [];

        // we utilize angular's foreach function
        // this takes in our original collection and an iterator function
        angular.forEach(collection, function (item) {
            // we check to see whether our object exists
            var key = item[keyname];
            // if it's not already part of our keys array
            if (keys.indexOf(key) === -1) {
                // add it to our keys array
                keys.push(key);
                // push this item to our final output array
                output.push(key);
            }
        });
        // return our array which should be devoid of
        // any duplicates
        return output;
    };
});

/**
 * 
 * @name uiSelectAllAndRemoveAll
 * 
 * @description
 * Angular UI-Select extension. Append "Add all" and "Remove all" feature into multiselect.
 * 
 * <AUTHOR> Brat Rai
 */

app.directive('uiSelectAllAndRemoveAll', ['$rootScope', '$timeout', '$compile', function ($rootScope, $timeout, $compile) {
    return {
        restrict: 'A',
        require: ['^uiSelect'],
        scope: false,

        link: function (scope, element, attrs, $select, $selectMultiple) {
            var dropDown = angular.element(element[0].querySelector('.ui-select-dropdown'));
            var addRemoveButtons = '<button class="btn" ng-click="selectAllItems()">Add All</button><button class="btn" ng-click="removeAllItems()">Remove All</button>';
            var btnGroup = document.createElement("div");
            btnGroup.className = 'btn-group ui-select-all-and-remove-all-button-group';
            dropDown[0].insertBefore(btnGroup, dropDown[0].childNodes[0]);
            angular.element(dropDown[0].childNodes[0]).html($compile(addRemoveButtons)(scope));

            scope.selectAllItems = function () {
                $select[0].selected = [];
                $select[0].items.map(function (item) {
                    $select[0].selected.push(item);
                })
                $select[0].ngModel.$setViewValue(Date.now()); //Set timestamp as a unique string to force changes
                $select[0].refreshItems();
                $timeout(function () {
                    $select[0].sizeSearchInput();
                }, 200);
            }

            scope.removeAllItems = function () {
                $select[0].selected = [];
                $select[0].ngModel.$setViewValue(Date.now());
                // Resize input box to right width
                $timeout(function () {
                    $select[0].sizeSearchInput();
                }, 200);
            }

        }
    };
}]);


/**
 * 
 * @name validateUrl
 * 
 * @description
 * Angular validate of input url string.
 * 
 * <AUTHOR> Brat Rai

 */
app.directive('validateUrl', function () {
    var URL_REGEXP = /^(?:(?:https?|ftp):\/\/)?(?:(?!(?:10|127)(?:\.\d{1,3}){3})(?!(?:169\.254|192\.168)(?:\.\d{1,3}){2})(?!172\.(?:1[6-9]|2\d|3[0-1])(?:\.\d{1,3}){2})(?:[1-9]\d?|1\d\d|2[01]\d|22[0-3])(?:\.(?:1?\d{1,2}|2[0-4]\d|25[0-5])){2}(?:\.(?:[1-9]\d?|1\d\d|2[0-4]\d|25[0-4]))|(?:(?:[a-z\u00a1-\uffff0-9]-*)*[a-z\u00a1-\uffff0-9]+)(?:\.(?:[a-z\u00a1-\uffff0-9]-*)*[a-z\u00a1-\uffff0-9]+)*(?:\.(?:[a-z\u00a1-\uffff]{2,})))(?::\d{2,5})?(?:\/\S*)?$/;
    return {
        require: 'ngModel',
        link: function (scope, elm, attrs, ctrl) {
            ctrl.$validators.url = function (modelValue, viewValue) {
                return URL_REGEXP.test(viewValue);
            };
        }
    };
});

/**
 * @name Checklist-model
 * @description AngularJS directive for list of checkboxes
 * @link https://github.com/vitalets/checklist-model
 * @License: MIT http://opensource.org/licenses/MIT
 */

app.directive('checklistModel', ['$parse', '$compile', function ($parse, $compile) {
    // contains
    function contains(arr, item, comparator) {
        if (angular.isArray(arr)) {
            for (var i = arr.length; i--;) {
                if (comparator(arr[i], item)) {
                    return true;
                }
            }
        }
        return false;
    }

    // add
    function add(arr, item, comparator) {
        arr = angular.isArray(arr) ? arr : [];
        if (!contains(arr, item, comparator)) {
            arr.push(item);
        }
        return arr;
    }

    // remove
    function remove(arr, item, comparator) {
        if (angular.isArray(arr)) {
            for (var i = arr.length; i--;) {
                if (comparator(arr[i], item)) {
                    arr.splice(i, 1);
                    break;
                }
            }
        }
        return arr;
    }

    // http://stackoverflow.com/a/19228302/1458162
    function postLinkFn(scope, elem, attrs) {
        // exclude recursion, but still keep the model
        var checklistModel = attrs.checklistModel;
        attrs.$set("checklistModel", null);
        // compile with `ng-model` pointing to `checked`
        $compile(elem)(scope);
        attrs.$set("checklistModel", checklistModel);

        // getter for original model
        var checklistModelGetter = $parse(checklistModel);
        var checklistChange = $parse(attrs.checklistChange);
        var checklistBeforeChange = $parse(attrs.checklistBeforeChange);
        var ngModelGetter = $parse(attrs.ngModel);



        var comparator = function (a, b) {
            if (!isNaN(a) && !isNaN(b)) {
                return String(a) === String(b);
            } else {
                return angular.equals(a, b);
            }
        };

        if (attrs.hasOwnProperty('checklistComparator')) {
            if (attrs.checklistComparator[0] == '.') {
                var comparatorExpression = attrs.checklistComparator.substring(1);
                comparator = function (a, b) {
                    return a[comparatorExpression] === b[comparatorExpression];
                };

            } else {
                comparator = $parse(attrs.checklistComparator)(scope.$parent);
            }
        }

        // watch UI checked change
        var unbindModel = scope.$watch(attrs.ngModel, function (newValue, oldValue) {
            if (newValue === oldValue) {
                return;
            }

            if (checklistBeforeChange && (checklistBeforeChange(scope) === false)) {
                ngModelGetter.assign(scope, contains(checklistModelGetter(scope.$parent), getChecklistValue(), comparator));
                return;
            }

            setValueInChecklistModel(getChecklistValue(), newValue);

            if (checklistChange) {
                checklistChange(scope);
            }
        });

        // watches for value change of checklistValue
        var unbindCheckListValue = scope.$watch(getChecklistValue, function (newValue, oldValue) {
            if (newValue != oldValue && angular.isDefined(oldValue) && scope[attrs.ngModel] === true) {
                var current = checklistModelGetter(scope.$parent);
                checklistModelGetter.assign(scope.$parent, remove(current, oldValue, comparator));
                checklistModelGetter.assign(scope.$parent, add(current, newValue, comparator));
            }
        }, true);

        var unbindDestroy = scope.$on('$destroy', destroy);

        function destroy() {
            unbindModel();
            unbindCheckListValue();
            unbindDestroy();
        }

        function getChecklistValue() {
            return attrs.checklistValue ? $parse(attrs.checklistValue)(scope.$parent) : attrs.value;
        }

        function setValueInChecklistModel(value, checked) {
            var current = checklistModelGetter(scope.$parent);
            if (angular.isFunction(checklistModelGetter.assign)) {
                if (checked === true) {
                    checklistModelGetter.assign(scope.$parent, add(current, value, comparator));
                } else {
                    checklistModelGetter.assign(scope.$parent, remove(current, value, comparator));
                }
            }

        }

        // declare one function to be used for both $watch functions
        function setChecked(newArr, oldArr) {
            if (checklistBeforeChange && (checklistBeforeChange(scope) === false)) {
                setValueInChecklistModel(getChecklistValue(), ngModelGetter(scope));
                return;
            }
            ngModelGetter.assign(scope, contains(newArr, getChecklistValue(), comparator));
        }

        // watch original model change
        // use the faster $watchCollection method if it's available
        if (angular.isFunction(scope.$parent.$watchCollection)) {
            scope.$parent.$watchCollection(checklistModel, setChecked);
        } else {
            scope.$parent.$watch(checklistModel, setChecked, true);
        }
    }

    return {
        restrict: 'A',
        priority: 1000,
        terminal: true,
        scope: true,
        compile: function (tElement, tAttrs) {

            if (!tAttrs.checklistValue && !tAttrs.value) {
                throw 'You should provide `value` or `checklist-value`.';
            }

            // by default ngModel is 'checked', so we set it if not specified
            if (!tAttrs.ngModel) {
                // local scope var storing individual checkbox model
                tAttrs.$set("ngModel", "checked");
            }

            return postLinkFn;
        }
    };
}]);