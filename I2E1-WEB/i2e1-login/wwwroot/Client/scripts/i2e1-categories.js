var i2e1Categories = {
    categories: {
        hospitality: {
            display: 'Hospitality',
            subcategories: {
                hotel: {
                    display: 'Hotel'
                },
                homestay: {
                    display: 'HomeStay'
                },
                society: {
                    display: 'Residential Society'
                },
                pg: {
                    display: 'Hostel/PG'
                }
            }
        },
        healthcare: {
            display: 'Health Care',
            subcategories: {
                hospital: {
                    display: 'Hospital'
                },
                clinic: {
                    display: 'Clinic'
                },
                diagnostic: {
                    display: 'Diagnostic'
                }
            }
        },
        auto: {
            display: 'Automobile',
            subcategories: {
                service_center: {
                    display: 'Service Centre'
                },
                showroom: {
                    display: 'Showrooms'
                }
            }
        },
        finance: {
            display: 'Financial Institution',
            subcategories: {
                bank: {
                    display: 'bank'
                },
                nbfc: {
                    display: 'NBFC'
                }
            }
        },
        edutech: {
            display: 'Education and Training',
            subcategories: {
                school: {
                    display: 'Schools'
                },
                coaching: {
                    display: 'Coaching'
                },
                vocational_training: {
                    display: 'Vocational Trainings'
                },
                college: {
                    display: 'Colleges'
                }
            }
        },
        entertainment: {
            display: 'Entertainment',
            subcategories: {
                amusement_park: {
                    display: 'Amusement Park'
                },
                movie_theater: {
                    display: 'Movie Theater'
                },
                auditorium: {
                    display: 'Auditoriums'
                },
                gaming_zone: {
                    display: 'Gaming Zone'
                },
                banquet: {
                    display: 'Banquet'
                }
            }
        },
        market_place: {
            display: 'Market Places',
            subcategories: {
                shopping_mall: {
                    display: 'Shopping Mall'
                },
                corporate_centre: {
                    display: 'Corporate Centre'
                },
                high_street: {
                    display: 'High Street'
                },
                food_court: {
                    display: 'Food Court'
                }
            }
        },
        fnb: {
            display: 'F&B',
            subcategories: {
                pub: {
                    display: 'Pub',
                    microcategories: {
                        tea: {
                            display: 'Tea'
                        },
                        coffee: {
                            display: 'Coffee'
                        },
                        chinese: {
                            display: 'Chinese'
                        },
                        burger: {
                            display: 'Burger'
                        },
                        mexican: {
                            display: 'Mexican'
                        },
                        street_food: {
                            display: 'Street Food'
                        },
                        italian: {
                            display: 'Italian'
                        },
                        continental: {
                            display: 'Continental'
                        },
                        desserts: {
                            display: 'Desserts'
                        },
                        beverages: {
                            display: 'Beverages'
                        },
                        north_indian: {
                            display: 'North Indian'
                        },
                        south_indian: {
                            display: 'South Indian'
                        },
                        fast_food: {
                            display: 'Fast Food'
                        },
                        lebanese: {
                            display: 'Lebanese'
                        },
                        alcohol: {
                            display: 'Alcohol'
                        },
                        Salad: {
                            display: 'Salad'
                        },
                        sandwiches: {
                            display: 'Sandwiches'
                        },
                        bakery: {
                            display: 'Bakery'
                        }
                    }
                },
                cafe: {
                    display: 'Café',
                    microcategories: {
                        tea: {
                            display: 'Tea'
                        },
                        coffee: {
                            display: 'Coffee'
                        },
                        chinese: {
                            display: 'Chinese'
                        },
                        burger: {
                            display: 'Burger'
                        },
                        mexican: {
                            display: 'Mexican'
                        },
                        street_food: {
                            display: 'Street Food'
                        },
                        italian: {
                            display: 'Italian'
                        },
                        continental: {
                            display: 'Continental'
                        },
                        desserts: {
                            display: 'Desserts'
                        },
                        beverages: {
                            display: 'Beverages'
                        },
                        north_indian: {
                            display: 'North Indian'
                        },
                        south_indian: {
                            display: 'South Indian'
                        },
                        fast_food: {
                            display: 'Fast Food'
                        },
                        lebanese: {
                            display: 'Lebanese'
                        },
                        alcohol: {
                            display: 'Alcohol'
                        },
                        Salad: {
                            display: 'Salad'
                        },
                        sandwiches: {
                            display: 'Sandwiches'
                        },
                        bakery: {
                            display: 'Bakery'
                        }
                    }
                },
                qsr: {
                    display: 'QSR',
                    microcategories: {
                        tea: {
                            display: 'Tea'
                        },
                        coffee: {
                            display: 'Coffee'
                        },
                        chinese: {
                            display: 'Chinese'
                        },
                        burger: {
                            display: 'Burger'
                        },
                        mexican: {
                            display: 'Mexican'
                        },
                        street_food: {
                            display: 'Street Food'
                        },
                        italian: {
                            display: 'Italian'
                        },
                        continental: {
                            display: 'Continental'
                        },
                        desserts: {
                            display: 'Desserts'
                        },
                        beverages: {
                            display: 'Beverages'
                        },
                        north_indian: {
                            display: 'North Indian'
                        },
                        south_indian: {
                            display: 'South Indian'
                        },
                        fast_food: {
                            display: 'Fast Food'
                        },
                        lebanese: {
                            display: 'Lebanese'
                        },
                        alcohol: {
                            display: 'Alcohol'
                        },
                        Salad: {
                            display: 'Salad'
                        },
                        sandwiches: {
                            display: 'Sandwiches'
                        },
                        bakery: {
                            display: 'Bakery'
                        }
                    }
                },
                restaurant: {
                    display: 'Restaurants',
                    microcategories: {
                        tea: {
                            display: 'Tea'
                        },
                        coffee: {
                            display: 'Coffee'
                        },
                        chinese: {
                            display: 'Chinese'
                        },
                        burger: {
                            display: 'Burger'
                        },
                        mexican: {
                            display: 'Mexican'
                        },
                        street_food: {
                            display: 'Street Food'
                        },
                        italian: {
                            display: 'Italian'
                        },
                        continental: {
                            display: 'Continental'
                        },
                        desserts: {
                            display: 'Desserts'
                        },
                        beverages: {
                            display: 'Beverages'
                        },
                        north_indian: {
                            display: 'North Indian'
                        },
                        south_indian: {
                            display: 'South Indian'
                        },
                        fast_food: {
                            display: 'Fast Food'
                        },
                        lebanese: {
                            display: 'Lebanese'
                        },
                        alcohol: {
                            display: 'Alcohol'
                        },
                        Salad: {
                            display: 'Salad'
                        },
                        sandwiches: {
                            display: 'Sandwiches'
                        },
                        bakery: {
                            display: 'Bakery'
                        }
                    }
                },
                dessert: {
                    display: 'Sweets and Desserts',
                    microcategories: {
                        tea: {
                            display: 'Tea'
                        },
                        coffee: {
                            display: 'Coffee'
                        },
                        chinese: {
                            display: 'Chinese'
                        },
                        burger: {
                            display: 'Burger'
                        },
                        mexican: {
                            display: 'Mexican'
                        },
                        street_food: {
                            display: 'Street Food'
                        },
                        italian: {
                            display: 'Italian'
                        },
                        continental: {
                            display: 'Continental'
                        },
                        desserts: {
                            display: 'Desserts'
                        },
                        beverages: {
                            display: 'Beverages'
                        },
                        north_indian: {
                            display: 'North Indian'
                        },
                        south_indian: {
                            display: 'South Indian'
                        },
                        fast_food: {
                            display: 'Fast Food'
                        },
                        lebanese: {
                            display: 'Lebanese'
                        },
                        alcohol: {
                            display: 'Alcohol'
                        },
                        salad: {
                            display: 'Salad'
                        },
                        sandwiches: {
                            display: 'Sandwiches'
                        },
                        bakery: {
                            display: 'Bakery'
                        }
                    }
                },
                stall: {
                    display: 'Food Stall',
                }
            }
        },
        retail: {
            display: 'Retail',
            subcategories: {
                electronics: {
                    display: 'Electronics',
                    microcategories: {
                        mobiles: {
                            display: 'Mobiles'
                        },
                        computers: {
                            display: 'Computers'
                        },
                        home_appliances: {
                            display: 'Home Appliances'
                        }
                    }
                },
                street_stall: {
                    display: 'Street Stalls',
                    microcategories: {
                        paan_stalls: {
                            display: 'Paan Stalls'
                        },
                        kirana_stores: {
                            display: 'Kirana Stores'
                        },
                        tea_stalls: {
                            display: 'Tea Stalls'
                        },
                        stationery: {
                            display: 'Stationery'
                        }
                    }
                },
                mega_store: {
                    display: 'Mega Store'
                },
                fashion: {
                    display: 'Fashion',
                    microcategories: {
                        footwear: {
                            display: 'Footwear'
                        },
                        eyewear: {
                            display: 'Eyewear'
                        },
                        clothing: {
                            display: 'Clothing'
                        },
                        watches: {
                            display: 'Watches'
                        },
                        sportswear: {
                            display: 'Sportswear'
                        },
                        bags_and_luggage : {
                            display: 'Bags & Luggage'
                        },
                        jewellery: {
                            display: 'Jewellery'
                        }
                    }
                },
                beauty_n_health: {
                    display: 'Beauty and Health',
                    microcategories: {
                        'beauty_personalcare': {
                            display: 'Beauty & Personal Care'
                        },
                        'diet_nutrition': {
                            display: 'Diet & Nutrition'
                        }
                    }
                },
                sports_n_fitness: {
                    display: 'Sports and Fitness',
                    microcategories: {
                        sports_equipments: {
                            display: 'Sports Equipments'
                        },
                        exercise_and_fitness: {
                            display: 'Exercise and Fitness'
                        },
                        camping_and_hiking: {
                            display: 'Camping & Hiking'
                        }
                    }
                },
                grocery: {
                    display: 'Grocery'
                },
                toy_n_baby: {
                    display: 'Toys and Baby products',
                    microcategories: {
                        toys_and_games: {
                            display: 'Toys & Games'
                        },
                        baby_products: {
                            display: 'Baby Products'
                        },
                        kids_fashion: {
                            display: 'Kids Fashion'
                        }
                    }
                },
                pharma: {
                    display: 'pharmaceutical'
                },
                home_n_furniture: {
                    display: 'Home and Furniture',
                    microcategories: {
                        home_furnishing: {
                            display: 'Home Furnishing'
                        },
                        home_decor: {
                            display: 'Home Décor'
                        },
                        furniture: {
                            display: 'Furniture'
                        },
                        pet_supplies: {
                            display: 'Pet Supplies'
                        }
                    }
                }
            }
        },
        travel: {
            display: 'Travel Services',
            subcategories: {
                bus: {
                    display: 'Bus'
                },
                cab: {
                    display: 'Cab'
                }
            }
        },
        workplace: {
            display: 'Workplace',
            subcategories: {
                office: {
                    display: 'Office'
                },
                cowork: {
                    display: 'Co-workingspace'
                },
                i2e1: {
                    display: 'i2e1'
                }
            }
        },
        wellness: {
            display: 'Personal Services',
            subcategories: {
                gym: {
                    display: 'Gym'
                },
                salon: {
                    display: 'Salon and Spa'
                },
                laundry: {
                    display: 'Laundry and dry cleaners'
                }
            }
        },
        telecom: {
            display: 'Telecommunication',
            subcategories: {
                relationship_centre: {
                    display: 'Relationship Centre'
                }
            }
        }
    }
}