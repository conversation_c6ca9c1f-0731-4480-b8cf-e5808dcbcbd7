(function(){var a;a=angular.module("ngAnalytics",[]),a.service("ngAnalyticsService",[function(){var a;this.ga=null,this.setClientId=function(b){return a=b,b},this.getClientId=function(){return a},this.authorize=function(b){this.ga.auth.authorize({container:b,clientid:a,userInfoLabel:this.authLabel})},this.viewSelectors={},this.isReady=!1,this.authLabel=void 0,this.authorized=!1}]),a.directive("ngAnalyticsAuth",["ngAnalyticsService",function(a){return{scope:{label:"@",authContainer:"@",hideOnAuth:"@"},restrict:"E",templateUrl:"ngAnalytics-auth/template.html",link:function(b){a.authLabel=b.label,b.authContainer=b.authContainer||"embed-api-auth-container";var c=b.$watch(function(){return a.isReady},function(d){d&&(b.hideOnAuth&&"true"===b.hideOnAuth&&(a.ga.auth.on("success",function(){b.hide=!0}),b.$watch(function(){return a.ga.auth.isAuthorized()},function(a,c){a&&c!==a?b.hide=!0:a||c===a||(b.hide=!1)})),c())})}}}]),a.directive("ngAnalyticsChart",["ngAnalyticsService",function(a){return{scope:{viewSelectorContainer:"@",authContainer:"@",chart:"="},restrict:"E",templateUrl:"ngAnalytics-chart/template.html",link:function(b){var c,d=a.authorized?!1:!0;a.authorized=!0;var e=b.$watch(function(){return a.isReady},function(f){if(f){var g;!a.ga.auth.isAuthorized()&&d&&a.authorize(b.authContainer||"embed-api-auth-container"),g=new a.ga.googleCharts.DataChart(b.chart),b.viewSelectorContainer?c=b.$watch(function(){return a.viewSelectors[b.viewSelectorContainer]},function(d){d&&(a.viewSelectors[b.viewSelectorContainer].on("change",function(a){var b={query:{ids:a}};g.set(b).execute()}),c())}):a.ga.auth.once("success",function(){g.execute()}),e()}})}}}]),a.directive("ngAnalyticsReport",["$rootScope","$q","ngAnalyticsService",function(a,b,c){return{scope:{queries:"=",authContainer:"@"},restrict:"E",link:function(d,e){function f(a){var d=b.defer(),e=new c.ga.report.Data(a);return e.once("success",function(a){d.resolve(a)}),e.once("error",function(a){d.reject(a)}),e.execute(),d.promise}var g=c.authorized?!1:!0;c.authorized=!0;var h=d.$watch(function(){return c.isReady},function(i){i&&(!c.ga.auth.isAuthorized()&&g&&c.authorize(d.authContainer||"embed-api-auth-container"),c.ga.auth.once("success",function(){var c=[];angular.forEach(d.queries,function(a){c.push(f(a))}),b.all(c).then(function(b){d.report=b,a.$broadcast("$gaReportSuccess",b,e)},function(b){d.error=b,a.$broadcast("$gaReportError",b,e)})}),h())})}}}]),a.directive("ngAnalyticsView",["ngAnalyticsService",function(a){return{scope:{viewSelectorContainer:"@",authContainer:"@",charts:"="},restrict:"E",templateUrl:"ngAnalytics-view/template.html",link:function(b){var c=a.authorized?!1:!0;a.authorized=!0,b.$watch(function(){return a.isReady},function(d){if(d){!a.ga.auth.isAuthorized()&&c&&a.authorize(b.authContainer||"embed-api-auth-container"),b.viewSelectorContainer=b.viewSelectorContainer||"view-selector-container";var e=new a.ga.ViewSelector({container:b.viewSelectorContainer});a.viewSelectors[b.viewSelectorContainer]=e,a.ga.auth.once("success",function(){e.execute()})}})}}}]),a.run(["$templateCache","$timeout","ngAnalyticsService",function(a,b,c){var d=document.createTextNode("(function(w,d,s,g,js,fs){ g=w.gapi||(w.gapi={});g.analytics={q:[],ready:function(f){this.q.push(f);}}; js=d.createElement(s);fs=d.getElementsByTagName(s)[0]; js.src='https://apis.google.com/js/platform.js'; fs.parentNode.insertBefore(js,fs);js.onload=function(){g.load('analytics');}; }(window,document,'script'));"),e=document.createElement("script");e.type="text/javascript",e.appendChild(d),document.body.appendChild(e),gapi.analytics.ready(function(){b(function(){c.ga=gapi.analytics,c.isReady=!0},0)}),a.put("ngAnalytics-auth/template.html",'<div id="{{authContainer}}" ng-hide="hide"></div>'),a.put("ngAnalytics-chart/template.html",'<div id="{{chart.chart.container}}"></div>'),a.put("ngAnalytics-view/template.html",'<div id="{{viewSelectorContainer}}"></div>')}])}).call(this);