var app = angular.module('templateApp', ['ui.router', 'ui.bootstrap']);
app.config(function ($stateProvider, $urlRouterProvider) {
    $stateProvider
       .state('templating', {
           url: '/createTemplate',
           templateUrl: getUrlWithVersion('partial/brandingTemplate.html'),
           controller: 'templateController'
       });
    $urlRouterProvider.otherwise('/createTemplate');
});


app.directive('confirmClick', function ($window) {
    var i = 0;
    return {
        restrict: 'A',
        priority: 1,
        compile: function (tElem, tAttrs) {
            var fn = '$$confirmClick' + i++,
                _ngClick = tAttrs.ngClick;
            tAttrs.ngClick = fn + '($event)';

            return function (scope, elem, attrs) {
                var confirmMsg = attrs.confirmClick || 'Are you sure?';

                scope[fn] = function (event) {
                    if ($window.confirm(confirmMsg)) {
                        scope.$eval(_ngClick, { $event: event });
                    }
                };
            };
        }
    };
});

app.filter('encodeURIComponent', function () {
    return window.encodeURIComponent;
});

app.service('messageService', ['$timeout', function ($timeout) {
    var showMessage = function (text, type, timeout) {
        timeout = timeout || 5000;
        if ($('.message.popup span').hasClass('appear')) {
            $('.message.popup span').removeClass('appear');
        }
        $('.message .' + type).text(text)
        $('.message .' + type).addClass('appear');
        $('.message').addClass('popup');
        $timeout(function () {
            $('.message .' + type).text('');
            $('.message .' + type).remove('appear');
            $('.message').removeClass('popup');
        }, timeout);
    }

    this.showError = function (text, timeout) {
        showMessage(text, 'error', timeout);
    }

    this.showSuccess = function (text, timeout) {
        showMessage(text, 'success', timeout);
    }

    this.showInfo = function (text, timeout) {
        showMessage(text, 'info', timeout);
    }
}]);

app.service('ajaxCall',['$http', '$q', 'messageService', function ($http, $q, messageService) {
    var count = 0;
    var makeCall = function (options) {
        typeof options.loading === "undefined" ? options.loading = true : options.loading = false;
        count++;
        var deferred = $q.defer();
        if (options.loading) {
            $('#loader').show();
        } else {
            $('#loader').hide();
        }
        $http(options).
		success(function (response) {
		    count--;
		    if (count == 0)
		        $('#loader').hide();
            deferred.resolve(response);
		}).error(function () {
		    count--;
		    if (count == 0)
		        $('#loader').hide();
		    if (options.failRedirectUrl) {
		        window.location.href = options.failRedirectUrl;
		    }

		});
        return deferred.promise;
    }

    this.get = function (url, options) {
        var rand = Math.random();
        if (url.indexOf('?') == -1)
            url += '?' + rand;
        else
            url += '&' + rand;

        var getData = { method: 'GET', url: url, cache: false };
        return makeCall(angular.extend(getData, options));
    }
    this.post = function (url, data, options) {
        var postData = { method: 'POST', data: data, url: url }
        return makeCall(angular.extend(postData, options));
    }
}]);


app.controller('templateController', function ($scope, ajaxCall, messageService, fileUploadService) {

    $scope.copyToClipboard = function (event, index) {
        copytext = document.getElementById('temp_img_' + index);
        copytext.select();
        document.execCommand("copy");
    }

    $scope.fetchTemplates = function (setting) {
        ajaxCall.get('/Client/GetAllTemplates').then(function (response) {
            $scope.templates = response.data;
            $scope.templateDetail = {};
        });
    }

    $scope.fetchAllClients = function () {
        ajaxCall.get('/Client/getAllClients').then(function (response) {
            $scope.clients = response.data.data.filter(function (client) {
                if (/^-?\d+\.?\d*$/.test(client.clientName)) {
                    return false;
                }
                return true;
            });
        });
    }

    $scope.fetchAllPartners = function () {
        ajaxCall.post('/Client/getClientPartners', { clientId : $scope.selectedClient }).then(function (response) {
            $scope.partners = response.data.data;
        });
    }

    $scope.fetchTemplateDetails = function () {
        ajaxCall.get('/Client/fetchTemplateDetails?templateId=' + $scope.selectedTemplate).then(function (response) {
            $scope.templateDetail = response.data.template;
            $scope.templateDetail.templatePath && ajaxCall.get($scope.templateDetail.templatePath).then(function (response) {
                $scope.templateDetail.templateContent = response;
            });
        });
    }

    $scope.initializeUpload = function () {
        var callback = function (response) {
            if (response.data) {
                var img = $scope.templateImages.filter(function (image) {
                    return response.data.pathToFile == image;
                });
                !img.length && $scope.templateImages.push(response.data.pathToFile);
                $scope.$apply();
            } else if (response.msg)
                messageService.showError(response.msg);
        }
        fileUploadService.initializeUpload('.add-image .upload-here', '.result_container', 'template uploader', callback, {
            maxSize: 500000,
            uploadUrl: '/Client/UploadClientFile',
            fileUse: 'template',
            clientId: $scope.selectedClient,
            partnerId: $scope.selectedBrand
        });
    }
    $scope.templateImages = [];

    $scope.updateTemplate = function () {
        saveTemplate(false);
    }
    $scope.createTemplate = function () {
        saveTemplate( true);
    }
    var saveTemplate = function (isNew) {
        var template = {
            templateName: $scope.templateDetail.templateName,
            templatePath: $scope.templateDetail.templatePath,
            templateContent: $scope.templateDetail.templateContent,
            id: $scope.templateDetail.id
        }

        if (!$scope.templateDetail.templateName) {
            messageService.showError('Template name cannot be empty');
            return;
        }

        if (isNew) template.id = 0;

        ajaxCall.post('/Client/SaveTemplate', {
            template: template,
            clientId: $scope.selectedClient,
            partnerId: $scope.selectedBrand
        }).then(function (response) {
            if (response.status == 0) {
                $scope.templateDetail.id = response.data.templateId;
                messageService.showSuccess(response.msg);
            }
            else messageService.showError(response.msg);
        });
    }

    $scope.preview = function (event) {
        if (!$scope.templateDetail.templateContent) {
            messageService.showError('Template content cannot be blank');
            return;
        }

        var template = {
            id: 115,
            templateName: 'Preview Template',
            templateContent: $scope.templateDetail.templateContent
        }

        ajaxCall.post('/Client/previewTamplate', template).then(function (response) {
            if (response.status == 0) {
                var nasid = 1425;
                var loginwebsite = $('#loginWebsite iframe')[0];
                var src = "";
                if (window.location.href.indexOf('localhost') >= 0) {
                    src = "https://localhost:44300?res=logoff&uamip=********&uamport=80&challenge=30f8c06d61d8142d81fbdb394640fc13&called=" +
                    mockmac() + "&mac=" + mockmac() + "&power=32&ip=********&nasid=" +
                    nasid + "&sessionid=" + uuidv4();
                } else {
                    if (window.location.href.indexOf('testease') >= 0) {
                        src = "https://testease.i2e1.in?res=logoff&uamip=********&uamport=80&challenge=30f8c06d61d8142d81fbdb394640fc13&called=" +
                            mockmac() + "&mac=" + mockmac() + "&power=32&ip=********&nasid=" +
                            nasid + "&sessionid=" + uuidv4();
                    } else {
                        src = "https://i2e1.in?res=logoff&uamip=********&uamport=80&challenge=30f8c06d61d8142d81fbdb394640fc13&called=" +
                            mockmac() + "&mac=" + mockmac() + "&power=32&ip=********&nasid=" +
                            nasid + "&sessionid=" + uuidv4();
                    }
                }

                loginwebsite.src = src;
                $('#loginWebsite').show();
            } else messageService.showError("preview unavailable");
        });
    }

    $scope.previewOff = function () {
        $('#loginWebsite').hide();
    }

    var uuidv4 = function () {
        return 'xxxxxxxxxxxx'.replace(/[xy]/g, function (c) {
            var r = Math.random() * 16 | 0, v = c == 'x' ? r : (r & 0x3 | 0x8);
            return v.toString(16);
        });
    }

    var mockmac = function () {
        return 'xx-xx-xx-xx-xx-xx'.replace(/[xy]/g, function (c) {
            var r = Math.random() * 16 | 0, v = c == 'x' ? r : (r & 0x3 | 0x8);
            return v.toString(16).toUpperCase();
        });
    }

    $scope.fetchTemplates();
    $scope.fetchAllClients();

});


