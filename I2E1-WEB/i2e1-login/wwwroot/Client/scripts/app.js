var app = angular.module('i2e1Admin', ['ngSanitize', 'ui.router', 'ui.bootstrap', 'ngAnalytics', 'ui.select', 'selectize', 'infinite-scroll', 'rzModule', 'ngDragDrop', 'daterangepicker'])
    .run(['ngAnalyticsService', function (ngAnalyticsService) {
        ngAnalyticsService.setClientId('535071296524-lnrptail900hao4iak84hg6e4k8ccb50.apps.googleusercontent.com');
    }]);

app.config(function($stateProvider, $urlRouterProvider) {
    $urlRouterProvider.otherwise('/landing/home');
    $stateProvider
        .state('landing', {
            url: '/landing',
            templateUrl: getUrlWithVersion('partial/landing.html'),
            controller: 'landingController',
        }).state('landing.home', {
            url: '/home',
            templateUrl: getUrlWithVersion('partial/home.html'),
            controller: 'homeController'
        }).state('landing.settings', {
            url: '/settings',
            templateUrl: getUrlWithVersion('partial/settings.html'),
            controller: 'settingsController'
        }).state('landing.basiconfig', {
            url: '/basiconfig',
            templateUrl: getUrlWithVersion('partial/basiconfig.html'),
            controller: 'basiconfigController'
        }).state('landing.storeOperations', {
            url: '/storeOperations',
            templateUrl: getUrlWithVersion('partial/storeOperations.html'),
            controller: 'storeOperationsController'
        }).state('landing.sms', {
            url: '/sendSms',
            templateUrl: getUrlWithVersion('partial/sms.html'),
            controller: 'smsController'
        }).state('landing.promotion', {
            url: '/promotion',
            templateUrl: getUrlWithVersion('partial/promotion.html'),
            controller: 'promotionController'
        }).state('landing.packages', {
            url: '/packages',
            templateUrl: getUrlWithVersion('partial/packages.html'),
            controller: 'packagesController'
        }).state('landing.reports', {
            url: '/reports',
            templateUrl: getUrlWithVersion('partial/reports.html'),
            controller: 'reportsController'
        }).state('landing.smsAnalytics', {
            url: '/smsAnalytics',
            templateUrl: getUrlWithVersion('partial/smsAnalytics.html'),
            controller: 'smsAnalyticsController'
        }).state('landing.primeSms', {
            url: '/primeSms',
            templateUrl: getUrlWithVersion('partial/primeSms.html'),
            controller: 'primeSmsController'
        }).state('landing.userManagement', {
            url: '/userManagement',
            templateUrl: getUrlWithVersion('partial/userManagement.html'),
            controller: 'userManagementController'
        }).state('landing.shopperDensity', {
            url: '/shopperDensity',
            templateUrl: getUrlWithVersion('partial/shopperDensity.html'),
            controller: 'shopperDensityController'
        }).state('landing.globalReport', {
            url: '/globalReport',
            templateUrl: getUrlWithVersion('partial/globalReport.html'),
            controller: 'globalReportController'
        }).state('landing.wofrOffer', {
            url: '/wofrOffer',
            templateUrl: getUrlWithVersion('partial/wofrOffer.html'),
            controller: 'wofrOfferController'
        }).state('landing.smsSender', {
            url: '/smsSender',
            templateUrl: getUrlWithVersion('partial/smsSender.html'),
            controller: 'smsSenderController'
        }).state('landing.smsCampaignReports', {
            url: '/smsCampaignReports',
            templateUrl: getUrlWithVersion('partial/smsCampaignReports.html'),
            controller: 'smsCampaignReportsController'
        }).state('landing.accountStatement', {
            url: '/accountStatement',
            templateUrl: getUrlWithVersion('partial/accountStatement.html'),
            controller: 'accountStatementController'
        });
});