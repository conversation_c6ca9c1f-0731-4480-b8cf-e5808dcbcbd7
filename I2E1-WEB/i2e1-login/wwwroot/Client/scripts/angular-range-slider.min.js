/*! angular-range-slider 2015-02-12 */
!function(a,b){return"function"==typeof define&&define.amd?void define("angular-range-slider",["angular"],function(a){return b(a)}):b(a)}("undefined"==typeof angular?null:angular,function(a){var b=a.module("angularRangeSlider",[]);return b.directive("rangeSlider",["$timeout",function(){function b(a,b,c,d){var e=(a-d)%c,f=e>c/2?a+c-e:a-e,g=Math.pow(10,b),h=f*g/g;return parseFloat(h.toFixed(b))}function c(a,b){return a.css({left:b})}function d(a){return a+"px"}function e(a){return isNaN(a)?a:Math.min(Math.max(0,a),100)}var f={single:"SINGLE",range:"RANGE"},g={mouse:{start:"mousedown",move:"mousemove",end:"mouseup"},touch:{start:"touchstart",move:"touchmove",end:"touchend"}};return{restrict:"EA",scope:{floor:"@",ceiling:"@",step:"@",highlight:"@",precision:"@",buffer:"@",dragstop:"@",ngModel:"=?",ngModelLow:"=?",ngModelHigh:"=?"},link:function(h,i,j){i.addClass("angular-range-slider");var k,l,m=null==j.ngModel&&null!=j.ngModelLow&&null!=j.ngModelHigh?f.range:f.single,n=i.children(),o=a.element(n[0]),p=a.element(n[1]),q=a.element(n[2]),r=(a.element(n[3]),a.element(n[4])),s=a.element(n[5]),t=a.element(n[6]),u=a.element(o.children()[0]),v=a.element(document);m===f.single?(k="ngModel",q.remove(),t.remove()):(k="ngModelLow",l="ngModelHigh"),h.local={},h.local[k]=h[k],h.local[l]=h[l];var w=0,x=0,y=0,z=0,A=0,B=0,C=0,D=0,E=!1,F=function(){void 0===h.step&&(h.step=1),void 0===h.floor&&(h.floor=0),void 0===h.ceiling&&(h.ceiling=100),void 0===h.precision&&(h.precision=0),m===f.single&&(h.ngModelLow=h.ngModel),h.local[k]=h[k],h.local[l]=h[l],h.floor=b(parseFloat(h.floor),parseInt(h.precision),parseFloat(h.step),parseFloat(h.floor)),h.ceiling=b(parseFloat(h.ceiling),parseInt(h.precision),parseFloat(h.step),parseFloat(h.floor)),m===f.range?(h.ngModelLow=b(parseFloat(h.ngModelLow),parseInt(h.precision),parseFloat(h.step),parseFloat(h.floor)),h.ngModelHigh=b(parseFloat(h.ngModelHigh),parseInt(h.precision),parseFloat(h.step),parseFloat(h.floor))):h.ngModel=b(parseFloat(h.ngModel),parseInt(h.precision),parseFloat(h.step),parseFloat(h.floor)),w=p[0].offsetWidth/2,x=o[0].offsetWidth,y=0,z=x-p[0].offsetWidth,A=parseFloat(h.floor),B=parseFloat(h.ceiling),C=B-A,D=z-y},G=function(){F();var a=function(a){return e((a-y)/D*100)},j=function(a){return e((a-A)/C*100)},n=function(a){return d(a*D/100)},o=function(){c(r,d(x-r[0].offsetWidth));var a,b;a=j(h.local[k]),c(p,n(a)),c(s,d(p[0].offsetLeft-s[0].offsetWidth/2+w)),c(u,d(p[0].offsetLeft+w)),m===f.range?(b=j(h.local[l]),c(q,n(b)),c(t,d(q[0].offsetLeft-t[0].offsetWidth/2+w)),u.css({width:n(b-a)})):"right"===h.highlight?u.css({width:n(110-a)}):"left"===h.highlight&&(u.css({width:n(a)}),c(u,0))},B=function(c,d,e,g){var j=e,n=function(){d.removeClass("active"),c.removeClass("active"),v.unbind(g.move),v.unbind(g.end),h.dragstop&&(h[l]=h.local[l],h[k]=h.local[k]),j=e,h.$apply()},r=function(c){var d=0;void 0!==c.clientX?d=c.clientX:void 0!==c.touches&&c.touches.length?d=c.touches[0].clientX:void 0!==c.originalEvent&&void 0!==c.originalEvent.changedTouches&&c.originalEvent.changedTouches.length&&(d=c.originalEvent.changedTouches[0].clientX);var e=Math.max(Math.min(d-i[0].getBoundingClientRect().left-w,z),y),g=a(e),n=A+C*g/100;if(m===f.range)switch(j){case k:n>h.local[l]?(j=l,p.removeClass("active"),s.removeClass("active"),q.addClass("active"),t.addClass("active"),o()):h.buffer>0&&(n=Math.min(n,h.local[l]-h.buffer));break;case l:n<h.local[k]?(j=k,q.removeClass("active"),t.removeClass("active"),p.addClass("active"),s.addClass("active"),o()):h.buffer>0&&(n=Math.max(n,parseInt(h.local[k])+parseInt(h.buffer)))}n=b(n,parseInt(h.precision),parseFloat(h.step),parseFloat(h.floor)),h.local[j]=n,h.dragstop||(h[j]=n),o(),h.$apply()},u=function(a){return F(),d.addClass("active"),c.addClass("active"),o(),a.stopPropagation(),a.preventDefault(),v.bind(g.move,r),v.bind(g.end,n)};c.bind(g.start,u)},G=function(){var a,b,c=["touch","mouse"];for(b=0;b<c.length;b++)a=c[b],m===f.range?(B(p,s,k,g[a]),B(q,t,l,g[a])):B(p,s,k,g[a]);E=!0};E||G(),o()};h.$watch(k,G),m===f.range&&h.$watch(l,G),window.addEventListener("resize",G)},template:'<div class="bar"><div class="selection"></div></div><div class="handle low"></div><div class="handle high"></div><div class="bubble limit low">{{ floor }}</div><div class="bubble limit high">{{ ceiling }}</div><div class="bubble value high">{{ ngModelHigh }}</div>'}}]),b});