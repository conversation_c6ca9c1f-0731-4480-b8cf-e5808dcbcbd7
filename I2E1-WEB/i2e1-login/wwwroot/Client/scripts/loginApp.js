var loginApp = angular.module('loginApp', ['ui.router', 'ui.bootstrap']);

loginApp.config(function ($stateProvider, $urlRouterProvider) {
    $stateProvider
       .state('login', {
           url: '/login',
           templateUrl: getUrlWithVersion('/Client/partial/login.html'),
           controller: 'loginController',
       }).state('addDevice', {
           url: '/addDevice',
           templateUrl: getUrlWithVersion('/Client/partial/addDevice.html'),
           controller: 'addDeviceController',
       }).state('signUp', {
           url: '/signUp',
           templateUrl: getUrlWithVersion('/Client/partial/registerAdminUser.html'),
           controller: 'signupController',
       }).state('forgetPassword', {
           url: '/forgetPassword',
           templateUrl: getUrlWithVersion('/Client/partial/forgetPassword.html'),
           controller: 'forgetPasswordController',
       }).state('resetPasswordLink', {
           url: '/resetPasswordLink',
           templateUrl: getUrlWithVersion('/Client/partial/resetPasswordLink.html'),
           controller: 'resetPasswordLinkController',
       });
    $urlRouterProvider.otherwise('/login');
});

loginApp.service('messageService', ['$timeout', function ($timeout) {
    var showMessage = function (text, type, timeout) {
        timeout = timeout || 5000;
        if ($('.message.popup span').hasClass('appear')) {
            $('.message.popup span').removeClass('appear');
        }
        $('.message .' + type).text(text)
        $('.message .' + type).addClass('appear');
        $('.message').addClass('popup');
        $timeout(function () {
            $('.message .' + type).text('');
            $('.message .' + type).remove('appear');
            $('.message').removeClass('popup');
        }, timeout);
    }

    this.showError = function (text, timeout) {
        showMessage(text, 'error', timeout);
    }

    this.showSuccess = function (text, timeout) {
        showMessage(text, 'success', timeout);
    }

    this.showInfo = function (text, timeout) {
        showMessage(text, 'info', timeout);
    }
}]);

loginApp.service('ajaxCall',['$http', '$q', 'messageService', function ($http, $q, messageService) {
    var count = 0;
    var makeCall = function (options) {
        typeof options.loading === "undefined" ? options.loading = true : options.loading = false;
        count++;
        var deferred = $q.defer();
        if (options.loading) {
            $('#loader').show();
        } else {
            $('#loader').hide();
        }
        $http(options).
		success(function (response) {
		    count--;
		    if (count == 0)
		        $('#loader').hide();
            deferred.resolve(response);
		}).error(function () {
		    count--;
		    if (count == 0)
		        $('#loader').hide();
		    if (options.failRedirectUrl) {
		        window.location.href = options.failRedirectUrl;
		    }

		});
        return deferred.promise;
    }

    this.get = function (url, options) {
        var rand = Math.random();
        if (url.indexOf('?') == -1)
            url += '?' + rand;
        else
            url += '&' + rand;

        var getData = { method: 'GET', url: url, cache: false };
        return makeCall(angular.extend(getData, options));
    }
    this.post = function (url, data, options) {
        var postData = { method: 'POST', data: data, url: url }
        return makeCall(angular.extend(postData, options));
    }
}]);

loginApp.controller('loginController', ['$scope', function ($scope) {
    var googleUser = {};
    $scope.startApp = function () {
        gapi.load('auth2', function () {
            // Retrieve the singleton for the GoogleAuth library and set up the client.
            auth2 = gapi.auth2.init({
                client_id: '303939939579-a02pc4b6m9hno2a2qggc2kjrdi5c3rko.apps.googleusercontent.com',
                cookiepolicy: 'single_host_origin',
                // Request scopes in addition to 'profile' and 'email'
                //scope: 'additional_scope'
            });
            var source = getUrlVars()["authSource"];
            document.getElementById("authType").value = source;
            attachSignin(document.getElementById('customBtn'));
        });
    };

    function getUrlVars() {
        var vars = [], hash;
        var hashes = window.location.href.slice(window.location.href.indexOf('?') + 1).split('&');
        for (var i = 0; i < hashes.length; i++) {
            hash = hashes[i].split('=');
            vars.push(hash[0]);
            vars[hash[0]] = hash[1];
        }
        return vars;
    }

    function attachSignin(element) {
        auth2.attachClickHandler(element, {},
            function (googleUser) {
                onSignIn(googleUser);
            }, function (error) {
                alert(JSON.stringify(error, undefined, 2));
            });
    }
    function onSignIn(googleUser) {
        document.getElementById('googleToken').value = googleUser.getAuthResponse().id_token;
        document.getElementById('authType').value = 'GMAIL';
        document.getElementById('loginForm').submit.click();
    };
}]);

loginApp.controller('forgetPasswordController', ['$scope', 'ajaxCall', 'messageService', function ($scope, ajaxCall, messageService) {
    var validateEmail = function(email) {
        if (/^\w+([\.-]?\w+)*@\w+([\.-]?\w+)*(\.\w{2,3})+$/.test(email)) {
            return (true)
        }
        return (false)
    }

    $scope.checkIfRegisteredUser = function () {
        if ($scope.username && validateEmail($scope.username)) {
            var data = { emailId: $scope.username };
            ajaxCall.post('/Client/ForgetPassword', data).then(function (response) {
                if (response.status == 0) {
                    messageService.showSuccess(response.msg);
                } else if (response.status == 1) {
                    messageService.showError(response.msg);
                }
            });
        } else {
            messageService.showError('Please enter a valid email address');
        }
    }
    
}]);


loginApp.controller('resetPasswordLinkController', ['$scope', '$location', '$timeout', '$state', 'ajaxCall', 'messageService', function ($scope, $location, $timeout, $state, ajaxCall, messageService) {
    
    var searchObject = $location.search();
    if (searchObject.hasOwnProperty('token')) {
        ajaxCall.post('/Client/ResetPasswordLink?token=' + searchObject.token).then(function (response) {
            if (response.status == 0) {
                $scope.user = response.data;
            } else if (response.status == 1) {
                messageService.showError(response.msg);
            }
        });
    }

    $scope.resetYourPassword = function () {
        if ($scope.user.password == '' || $scope.user.confirmPassword == '') {
            messageService.showError('Password and confirm password field should not be empty');
            return;
        } else if ($scope.user.password != $scope.user.confirmPassword) {
            messageService.showError('Confirm password field should be equal to password field');
            return;
        } else {
            ajaxCall.post('/Client/resetYourPassword?token=' + searchObject.token, $scope.user).then(function (response) {
                if (response.status == 0) {
                    $scope.user = null;
                    messageService.showSuccess(response.msg);
                    $timeout(function () {
                        $state.go('login');
                    }, 300);
                } else if (response.status == 1) {
                    messageService.showError(response.msg);
                }
            });
        }
    }
}]);

loginApp.controller('addDeviceController', ['$scope', 'messageService', 'ajaxCall', '$state', function ($scope, messageService, ajaxCall, $state) {
    $scope.add_device_state = 1;
    $scope.storeDetails = {};
    

    $scope.validateKey = function () {
        ajaxCall.get('/Admin/GetStoreFromDevice?deviceId=' + $scope.storeDetails.deviceId).then(function (response) {
            var store = response.data;
            if (!store)
                messageService.showMessage('Invalid Device Id', 'failure');
            else {
                $scope.storeDetails = store;
                $scope.add_device_state = 2;
            }
        });
    }

    $scope.submitLocationDetails = function () {
        var fields = document.getElementById('addDeviceForm2').getElementsByTagName('input');

        $scope.err = false;
        for (var i = 0; i < fields.length; i++) {
            fields[i].onkeypress = function () {
                this.className = "form-control";
            };
            if (!fields[i].value) {
                $scope.err = true;
                addErrorClass(fields[i]);
            }

            if (fields[i].name == 'contactNumber' && fields[i].value.length != 10) {
                $scope.err = true;
                messageService.showMessage('Please enter 10 digit phone number', 'failure');
                addErrorClass(fields[i]);
            }
        }

        if ($scope.err) {
            return;
        }

        ajaxCall.post('/Admin/SubmitLocationDetails', $scope.storeDetails).then(function (response) {
            $state.go('login');
        });
    }
    
}]);

loginApp.controller('signupController', ['$scope', 'messageService', 'ajaxCall', '$state', function ($scope, messageService, ajaxCall, $state) {

    $scope.signUp = function (event) {
        var err = false;
        var signUpForm = $('#signUpForm');
        var fields = signUpForm.find('input');
        var password = signUpForm.find('[name=password]')[0];
        var confirm_password = signUpForm.find('[name=confirm_password]')[0];
        var email = signUpForm.find('[name=emailId]')[0];
        var contact_no = signUpForm.find('[name=contact_no]')[0];
        var data = {};

        for (var i = 0; i < 5; i++) {
            if (!fields[i].value) {
                err = true;
                showError(email);
            } else {
                data[fields[i].name] = fields[i].value;
            }
        }

        //if (!validateEmail(email.value)) {
        //    showError(email, "Not a valid e-mail address");
        //    err = true;
        //}

        if (contact_no.value.length != 10) {
            showError(contact_no, "Please enter 10 digit phone number");
            err = true;
        }

        if (password.value.length < 8) {
            showError(password, "Password must be greater than 8 characters");
            err = true;
        }

        event.preventDefault();
        if (err) {
            return;
        }

        if (password.value === confirm_password.value) {
            ajaxCall.post(signUpForm[0].action, data).then(function (response) {
                if (response.status == 0) {
                    messageService.showSuccess(response.msg);
                    $('.details .cell').toggleClass('display-none');
                } else {
                    messageService.showError(response.msg);
                }
            });
        } else {
            showError(password, "Password and confirm password do not match");
        }
    };

    var showError = function (field, errMsg) {
        $(field).addClass('error');
        errMsg && messageService.showError(errMsg);
    };

    var validateEmail = function (email) {
        var re = /^[_a-z0-9-]+(\.[_a-z0-9-]+)*@@[a-z0-9-]+(\.[a-z0-9-]+)*(\.[a-z]{2,4})$/;
        return re.test(email);
    }
}]);