{"Logging": {"LogLevel": {"Default": "Warning", "Microsoft.AspNetCore": "Warning"}}, "AllowedHosts": "*", "JWT": {"Issuer": "https://www.i2e1.in", "Audience": "https://www.i2e1.in", "Key": "This is a sample secret key - please don't use in production environment."}, "IpRateLimiting": {"EnableEndpointRateLimiting": true, "ExcludedRoutes": ["/api/wiomapi/*"], "StackBlockedRequests": false, "RealIpHeader": "X-Real-IP", "HttpStatusCode": 429, "GeneralRules": [{"Endpoint": "*", "Period": "5s", "Limit": 100}]}}