[{"outputFileName": "~/bundles/static", "inputFiles": ["~/jsLibs/angular.min.js", "~/jsLibs/jquery.js", "~/jsLibs/angular-cookies.min.js", "~/jsLibs/checklist-model.js", "~/jsLibs/angular-ui-router.min.js", "~/jsLibs/bootstrap/ui-bootstrap-tpls.min.js"]}, {"outputFileName": "~/bundles/newwebsite", "inputFiles": ["~/jsLibs/jquery.js", "~/Templates/i2e1/i2e1-sdk.js", "~/Templates/i2e1/template-utils.js"]}, {"outputFileName": "wwwroot/js/site.min.js", "inputFiles": ["~/scripts/app.js", "~/scripts/directive.js", "~/scripts/controller.js", "~/scripts/service.js"], "minify": {"enabled": true, "renameLocals": true}, "sourceMap": false}]