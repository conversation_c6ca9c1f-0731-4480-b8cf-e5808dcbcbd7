const {BigQuery} = require('@google-cloud/bigquery');
var fs=require("fs");
module.exports=(rowdata,socket_id)=>{
    const bigquery = new BigQuery();
    var promise=new Promise(async(resolve,reject) => {
        console.log(rowdata);
        let now = new Date();
        let eventTime = `${now.getUTCFullYear()}-${(now.getUTCMonth() + 1).toString().padStart(2, '0')}-${(now.getUTCDate()).toString().padStart(2, '0')}T`+ `${(now.getUTCHours()).toString().padStart(2, '0')}:${(now.getUTCMinutes()).toString().padStart(2, '0')}:${(now.getUTCSeconds()).toString().padStart(2, '0')}`;  
        var d=new Date();
        d.setUTCMinutes(d.getUTCMinutes()+330);
        var month=d.getUTCMonth()+1;
	    var date=d.getUTCDate();
        if(month<10)
           month="0"+month;
	  
	    if(date<10)
           date="0"+date;
        var partionDate=d.getUTCFullYear()+"" +month+""+date;
        var closeData={
            socket_id:socket_id,
            session_id:rowdata["session_id"],
            open_time:rowdata["open_time"],
            close_time:eventTime,
            event_count:rowdata["count"]
        }
        var rows=[];
        rows.push(closeData);

        try{
            await bigquery.dataset("i2e1").table("linq_sockets$"+partionDate).insert(rows);
            resolve("success");
        }
        catch(err){
            console.log(JSON.stringify(err));
            var filename="/home/<USER>/BQEvents/close_connection.txt";
            fs.appendFileSync(filename,JSON.stringify(rows),"utf8");
            reject("error");
        }
    });
    return promise;
}
