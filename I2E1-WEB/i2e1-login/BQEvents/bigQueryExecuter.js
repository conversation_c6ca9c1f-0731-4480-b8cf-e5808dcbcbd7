const {BigQuery} = require('@google-cloud/bigquery');
var fs=require("fs");
module.exports=(rows)=>{
    const bigquery = new BigQuery();
    var promise=new Promise(async(resolve,reject) => {
        try {
		    var d=new Date();
            d.setUTCMinutes(d.getUTCMinutes()+330);
            var month=d.getUTCMonth()+1;
	        var date=d.getUTCDate();
            if(month<10)
              month="0"+month;
	    
	        if(date<10)
              date="0"+date;

            await bigquery.dataset("i2e1").table("t_event_log").insert(rows);
            resolve("success");
        }
        catch(err){
           console.log(JSON.stringify(err));
           var filename="/home/<USER>/BQEvents/event_error.txt";
           fs.appendFileSync(filename,JSON.stringify(rows),"utf8");
           reject("error");
        }
    });
    return promise;
}