{"name": "node-js-getting-started", "version": "0.3.0", "description": "A sample Node.js app using Express 4", "engines": {"node": "10.x"}, "main": "index.js", "scripts": {"start": "node index.js", "test": "node test.js"}, "dependencies": {"@google-cloud/bigquery": "^4.1.8", "ejs": "^2.5.6", "express": "^4.15.2", "ws": "^6.2.0"}, "devDependencies": {"request": "^2.81.0", "tape": "^4.7.0"}, "repository": {"type": "git", "url": "https://github.com/heroku/node-js-getting-started"}, "keywords": ["node", "<PERSON><PERSON>", "express"], "license": "MIT"}