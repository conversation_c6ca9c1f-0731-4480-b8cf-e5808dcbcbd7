const express = require("express");
const path = require("path");
const PORT = process.env.PORT || 5010;
var app = express();
var http = require("http");
var serverwss = http.createServer(app);
var BigQueryExecuter = require("./bigQueryExecuter");
var connectionClose=require('./connectionClose');
app.use(express.static(path.join(__dirname, "/")));
app.get("/wsee", function(req, res) {
  res.send("hello wsee");
});
serverwss.listen(PORT, () => console.log(`Listening on ${PORT}`));
var WebSocketServer = require("ws").Server;

var wss = new WebSocketServer({ server: serverwss });
var users = {};
var userCount = 0;
var error = 0;
var success = 0;
wss.on("connection", function(connection) {
  console.log("User connected");
  userCount++;
  connection.on("message", function(message) {
    try {
      data = JSON.parse(message);
    } catch (e) {
      console.log("Invalid JSON");
      data = {};
    }
    if (data["request"]) {
      console.log("req", data["request"]);
      connection.send(
        JSON.stringify({
          userCount: userCount,
          data: users,
          success: success,
          error: error
        })
      );
    } else {
      let now = new Date();
      let eventTime = `${now.getUTCFullYear()}-${(now.getUTCMonth() + 1).toString().padStart(2, '0')}-${(now.getUTCDate()).toString().padStart(2, '0')}T`+ `${(now.getUTCHours()).toString().padStart(2, '0')}:${(now.getUTCMinutes()).toString().padStart(2, '0')}:${(now.getUTCSeconds()).toString().padStart(2, '0')}`;  
      
      if (!users[data["sid"]]) {
        console.log("new user\n",data);
          users[data["sid"]] = {
              last_event: "online",
              count: 1,
              session_id: data["bgtb"][0]["session_id"]
          };
        users[data["sid"]]["open_time"]=eventTime;
        connection.name = data["sid"];
      } else {
        console.log("exist user");
        users[data["sid"]]["last_event"] = "working";
        users[data["sid"]]["count"]++;
      }
	  data["bgtb"][0]["added_time"] = eventTime;
      BigQueryExecuter(data["bgtb"])
        .then(res => {
          success++;
        })
        .catch(err => {
          error++;
        });
    }
  });
  connection.on("close", function() {
    userCount--;
    if (connection.name) {
      connectionClose(users[connection.name],connection.name)
      .then((result)=>{
        console.log("connection close successfult")
        delete users[connection.name];
      })
      .catch((err)=>{
        console.log("error in closing")
        delete users[connection.name];
      })
    }
  });
  //connection.sendnode type":"Hello world"}');
});
