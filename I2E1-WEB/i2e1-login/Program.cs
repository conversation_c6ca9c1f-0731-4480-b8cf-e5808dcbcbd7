using System;
using System.Collections.Generic;
using System.IO;
using System.IO.Compression;
using System.Linq;
using System.Threading;
using i2e1_basics.Cache;
using i2e1_basics.Utilities;
using i2e1_core.MiddleWare;
using i2e1_core.Utilities;
using i2e1_login.MiddleWare;
using i2e1_login.SQSHandlers;
using i2e1_login.WebSockets;
using I2E1_Message.Utils;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Diagnostics;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.ResponseCompression;
using Microsoft.AspNetCore.Server.Kestrel.Core;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using Serilog.Context;
using wifidog_core.Utilities;
using wiom_login_share.Utilities;

ThreadPool.SetMinThreads(1000, 1000);
ThreadPool.SetMaxThreads(5000, 5000);

// Environment.SetEnvironmentVariable("DEPLOYED_ON", "stage");
I2e1ConfigurationManager.GetInstance().SetAllServerConfig();

ThreadPool.SetMinThreads(5000, 5000);
ThreadPool.SetMaxThreads(int.MaxValue, int.MaxValue);
ThreadPool.GetMinThreads(out var workerThreads, out var completionPortThreads);
ThreadPool.GetMaxThreads(out var workerThreads2, out var completionPortThreads2);
Console.WriteLine($"Thread Pool Settings: Min({workerThreads}),Max({workerThreads2}). IO Threads: Min({completionPortThreads}),Max({completionPortThreads2})");

Logger.CreateInstance();
Logger.GetInstance().Info("App Started");

BasicMemoryCache.CreateInstance();
CoreDbCalls.CreateInstance();
CoreCacheHelper.CreateInstance(
    CoreCacheHelper.GetCacheConnectionString(), 0);
SessionCacheHelper.CreateInstance(CoreCacheHelper.GetCacheConnectionString(), 1);

DbCalls.CreateInstance();
CacheHelper.CreateInstance(
    CoreCacheHelper.GetCacheConnectionString(), 0);

WifidogDbCalls.CreateInstance();
WifidogCacheHelper.CreateInstance(CoreCacheHelper.GetCacheConnectionString(), 0);
wiom_routerplan_share.ExposeApi.GenericApi.CreateInstance();

string REMOTE_URL = I2e1ConfigurationManager.GetMicroserviceDomainUrl("remote");
wiom_login_share.ExposeApi.GenericApi.CreateInstance();
wiom_router_api.RouterApiClient.CreateInstance(REMOTE_URL);
/*var hostBuilder = new HostBuilder()
                        .ConfigureServices((hostContext, services) =>
                        {
                            services.AddHostedService<Worker>();

                            // Automatically register all implementations of IQueueHandler
                            services.Scan(scan => scan
                                .FromAssemblyOf<Program>()  // Assuming the handlers are in the same assembly as Program
                                .AddClasses(classes => classes.AssignableTo<IQueueHandler>())  // Only classes implementing ISQSHandler
                                .AsImplementedInterfaces()  // Register as their implemented interfaces
                                .WithTransientLifetime()  // You may adjust the lifetime scope as needed
                            );
                        })
                        .ConfigureLogging(logging =>
                        {
                            logging.ClearProviders();
                            logging.AddConsole();
                        });

hostBuilder.Build().Run();*/
WiomNetHandler.Register();

var builder = WebApplication.CreateBuilder(args);
var configuration = builder.Configuration;

builder.Services.Configure<KestrelServerOptions>(options =>
{
    options.AllowSynchronousIO = true;
});

// configure rate limiting
builder.Services.AddMemoryCache();

builder.Services.AddControllersWithViews()
                .AddRazorRuntimeCompilation()
                .AddNewtonsoftJson(options => options.SerializerSettings.ReferenceLoopHandling = ReferenceLoopHandling.Ignore);

builder.Services.Configure<BrotliCompressionProviderOptions>(options =>
{
    options.Level = CompressionLevel.Fastest;
});
builder.Services.Configure<GzipCompressionProviderOptions>
(options => options.Level = CompressionLevel.Fastest);
builder.Services.AddResponseCompression(options =>
{
    options.EnableForHttps = true;
    options.Providers.Add<BrotliCompressionProvider>();
    options.Providers.Add<GzipCompressionProvider>();
    options.MimeTypes =
        ResponseCompressionDefaults.MimeTypes.Concat(
            new[] { "image/svg+xml" });
});


builder.Logging.ClearProviders();
builder.Logging.AddConsole();
builder.Services.AddPaymentWebSockets();
var app = builder.Build();

app.UseMiddleware<OptionsMiddleware>();
if (app.Environment.IsDevelopment())
{
    app.UseDeveloperExceptionPage();
}
else
{
    app.UseExceptionHandler(errorApp =>
    {
        errorApp.Run(async context =>
        {
            var exceptionHandlerPathFeature = context.Features.Get<IExceptionHandlerPathFeature>();
            if (context.Items.TryGetValue("traceId", out var traceId))
            {
                LogContext.PushProperty("traceId", traceId);
                Logger.GetInstance().Error($"TraceId: {traceId} | Path: {exceptionHandlerPathFeature.Path} Exception: {exceptionHandlerPathFeature.Error}");
            }
            else
            {
                Logger.GetInstance().Error($"Path: {exceptionHandlerPathFeature.Path} Exception: {exceptionHandlerPathFeature.Error.Message}");
            }
            context.Response.StatusCode = 500;
            context.Response.ContentType = "application/json";
            await context.Response.WriteAsync($"{{ \"traceId\": \"{traceId}\" }}");
        });
    });
}
// Create branch to the MyHandlerMiddleware. 
// All requests ending in .report will follow this branch.
app.MapWhen(
    context => context.Request.Path.ToString().EndsWith(".ashx"),
    appBranch => {
        appBranch.UseGetProxyContent();
    });


app.UseResponseCompression();
app.UseStaticFiles();
app.UseCookiePolicy();
app.UseRouting();
app.UsePaymentWebSockets();

app.Use(async (context, next) => {
    await next();
    var callbacks = (List<Callback>)context.Items["OnEndRequest"];
    if (callbacks != null)
    {
        for (int i = 0; i < callbacks.Count; i++)
        {
            callbacks[i]();
        }
    }
});
app.UseEndpoints(endpoints =>
{
    endpoints.MapControllers();
    endpoints.MapControllerRoute(name: "admin", pattern: "{controller=WiomDashboard}/{action=Index}/{id?}").RequireHost("admin.wiom.in");

    endpoints.MapControllerRoute("ShortUrl", pattern: "u/{*id}", defaults: new { controller = "U", action = "Lengthen" });
    endpoints.MapControllerRoute("login", "{action=Index}/{id?}", new { controller = "Login" });
    endpoints.MapControllerRoute("client", "client", new { controller = "Client", action = "Index" });
    endpoints.MapControllerRoute("LoginWeb", "Client/LoginWeb", new { controller = "Client", action = "LoginWeb" });
    endpoints.MapControllerRoute("oneinsights", "oneinsights", new { controller = "OneInsights", action = "Index" });
    endpoints.MapControllerRoute("default", "{controller=Login}/{action=Index}/{id?}");
    endpoints.MapControllerRoute("all", "{*url}", new { controller = "Login", action = "Index" });
});

app.Run();