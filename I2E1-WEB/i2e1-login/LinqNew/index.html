<!DOCTYPE html>
<html>
<head>
    <meta http-equiv="Content-Type" content="text/html" charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=0">
    <!--<link href="https://cdnjs.cloudflare.com/ajax/libs/normalize/8.0.0/normalize.min.css" rel="stylesheet"/>-->
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
    <!--<link href="~/Listing/material.min.css" rel="stylesheet"/> material css-->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/4.7.0/css/font-awesome.css">
    <link href="https://fonts.googleapis.com/css?family=Lato:400,700&amp;subset=latin-ext" rel="stylesheet">
    <title>Listing Form 1</title>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.3.1/jquery.min.js"></script>
    <link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/bootstrap/4.3.1/css/bootstrap.min.css" integrity="sha384-ggOyR0iXCbMQv3Xipma34MD+dH/1fQ784/j6cY/iJTQUOhcWr7x9JvoRxT2MZw1T" crossorigin="anonymous">
    <script>
        function doAjax(url, id, callback) {
            $.ajax({
                type: "GET",
                url: url,
                data: $(id).children('form').serialize()
            }).done(function (o) {
                dirtyBit = false; //clear dirtyBit
                var data = JSON.parse(o);
                window[callback].apply(null, [data]);
            }).fail(function (xhrObject, textStatus, errorThrown) {
            }).always(function () {
                $('#loader').hide();
            });
            $('#loader').show();
        };
        function renderSocial(data) {
            $('#facebookVal').text(data.message.facebook);
            $('#instaVal').text(data.message.instagram);
            $('#twitterVal').text(data.message.twitter);
            $('#websiteVal').text(data.message.website);
            $('#marketDiv').show();
        }
        function renderZomato(data) {
            $('#justdialVal').text(data.message.justdial);
            $('#zomatoVal').text(data.message.zomato);
            $('#zomatoDiv').show();
        }
    </script>
</head>
<body>
    <div style="margin: 20px auto;max-width:320px; text-align:center">
        <img style="width:140px" src="/images/swapp/linq.png"/>
        <br/>
        <br/>
        <div id="brandDiv">
            <form>
                <input class="form-control" width="100%" placeholder="Enter brand name" id="brand" type="text" />
                <br/>
                <button class="btn btn-primary" style="width:100%" type="button" value="Go" onclick="doAjax('http://*************:5000/hack/social?brand=' + $('#brand').val(), '#brandDiv', 'renderSocial')" >Fetch Linq Social Data</button>
            </form>
        </div>
        <div style="display:none;margin-top:20px;text-align:left;" id="marketDiv">
            <h4>Yeeee!!</h4>
            We got following social information for you
            <form>
                <div>
                    <p><h5>Facebook</h5> <span id="facebookVal"></span></p>
                    <p><h5>Instagram</h5> <span id="instaVal"></span></p>
                    <p><h5>Twitter</h5> <span id="twitterVal"></span></p>
                    <p><h5>Website</h5> <span id="websiteVal"></span></p>
                </div>
                Now Locate your asset with
                <input class="form-control" placeholder="Market Place" id="market" type="text" />
                <br/>
                <input style="width:100%" class="btn btn-primary" type="button" value="Go" onclick="doAjax('http://*************:5000/hack/listing?brand=' + $('#brand').val() + '&market=' + $('#market').val(), '#marketDiv', 'renderZomato')" />
            </form>
        </div>
        <div style="display:none;margin-top:20px;text-align:left;" id="zomatoDiv">
            <h4>Got few details for your asset</h4>
            <p><h5>Justdial</h5> <span id="justdialVal"></span></p>
            <p><h5>Zomato</h5> <span id="zomatoVal"></span></p>
            <button class="btn btn-primary" style="width:100%" type="button">Add Asset Now</button>
        </div>
    </div>
    
</body>
</html>