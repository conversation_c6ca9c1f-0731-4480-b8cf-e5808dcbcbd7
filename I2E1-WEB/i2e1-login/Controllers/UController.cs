using Amazon.EC2.Model;
using i2e1_basics.Models;
using i2e1_basics.Utilities;
using i2e1_core.Utilities;
using I2E1_WEB.Utilities;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using wifidog_core.Models.WIOM;
using wiom_login_share.Models;
using wiom_login_share.Utilities;

namespace i2e1_login.Controllers;

public class UController : BaseController
{
    [HttpGet]
    public IActionResult Lengthen(string id)
    {
        string url = CoreCacheHelper.GetInstance().GetLongUrl(id);
        Logger.GetInstance().Info($"UController:Lengthen:url: {url}");
        try
        {
            return Redirect(WebUtils.GetWiomNetServerUrl() + "/u/" + id);
        }
        catch(Exception ex)
        {
            Logger.GetInstance().Error($"UController:Lengthen:Exception: id: {id} ex: {ex}");
            return RedirectToAction("Error");
        }
    }

    [Route("Error")]
    [HttpGet]
    public string Error()
    {
        return "Invalid Short Url";
    }
}
