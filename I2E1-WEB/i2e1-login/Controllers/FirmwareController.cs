using i2e1_core.Models;
using I2E1_WEB.Utilities;
using Microsoft.AspNetCore.Mvc;
using System.Collections.Generic;

namespace I2E1_WEB.Controllers;

public class FirmwareController : Controller
{

    private static List<string> allowedUsers = new List<string>()
    {
        "<EMAIL>",
        "<EMAIL>"
    };


    public ActionResult index()
    {
        var user= JwtObject.GetManagementUser(HttpContext);
        if(user!=null && allowedUsers.Contains(user.email))
        {
                return View("~/Views/Firmware/index.cshtml");
        }
        return Redirect("~/client");
    }

  

   
}
