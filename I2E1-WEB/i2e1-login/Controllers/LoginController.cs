using i2e1_basics.Cache;
using i2e1_basics.Models;
using i2e1_basics.Utilities;
using i2e1_core.Attributes;
using i2e1_core.Models;
using i2e1_core.Models.Client;
using i2e1_core.services;
using i2e1_core.Utilities;
using I2E1_Message.Models;
using I2E1_Message.Utils;
using I2E1_WEB.Attributes;
using I2E1_WEB.Captcha;
using I2E1_WEB.Database;
using I2E1_WEB.ExternelApiClient;
using I2E1_WEB.Models;
using I2E1_WEB.Utilities;
using I2E1_WEB.Validators;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Caching.Memory;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading;
using wifidog_core.Models;
using wifidog_core.Models.WIOM;
using wifidog_core.Utilities;
using wiom_login_share.Models;
using wiom_login_share.Utilities;
using wiom_router_api.Models;
using wiom_routerplan_share.Models.RouterPlan;
using Inventory = wiom_router_api.Models.Inventory;
using RequestUtils = I2E1_Message.Utils.RequestUtils;

namespace I2E1_WEB.Controllers;

public class LoginController : BaseController
{
    private IMemoryCache _memoryCache;
    public LoginController(IMemoryCache memoryCache)
    {
        _memoryCache = memoryCache;
    }

    [HttpPost]
    [CoreModelBinder]
    public string LogJsError(string message, string source, string lineno, string colno, string error, string url)
    {
        Logger.GetInstance().Error($"JS Error: User-Agent:{RequestUtils.getUserAgent(HttpContext)} Message:{message}, Source:{source}, LineNum:{lineno}, ColNum:{colno}, Error:{Encoding.ASCII.GetString(Convert.FromBase64String(error))}, Url:{url}");
        return string.Empty;
    }

    [HttpPost]
    public void levt([FromBody] Dictionary<string, object> evt) {
        DbCalls.GetInstance().SaveUIEvent(evt);
    }

    //[OutputCache(VaryByCustom = "culture", Duration = 2 * 60 * 60)]
    public JsonResult GetResources(string lp = null)
    {
        if (!string.IsNullOrEmpty(lp))
        {
            CookieUtils.SetCookie(HttpContext, "language", lp, false, DateTime.UtcNow.AddYears(1));
        }
        else
        {
            lp = CookieUtils.GetCookie(HttpContext, "language");
        }

        if (!string.IsNullOrEmpty(lp))
        {
            var culture = new System.Globalization.CultureInfo(lp);
            Thread.CurrentThread.CurrentCulture = culture;
            Thread.CurrentThread.CurrentUICulture = culture;
        }

        Dictionary<string, string> resourceDict = new Dictionary<string, string>();
        resourceDict["Culture"] = Thread.CurrentThread.CurrentUICulture.Name;
        return new JsonResult(resourceDict);
    }

    public bool isServer10AppVersion(string appVersion)
    {
        if (CoreCacheHelper.GetInstance().isServer10AppVersion(appVersion))
            return true;
        return false;
    }

    public JsonResult SetServer10(string appName, int appVersion)
    {
        if (string.IsNullOrEmpty(appName))
            return new JsonResult(new JsonResponse(ResponseStatus.FAILURE, ErrorCodes.BAD_REQUEST));

        var staticData = CoreAccountService.GetStaticEnums(appName, appVersion);
        var signalToken = Guid.NewGuid().ToString();
        SessionCacheHelper.GetInstance().SetSession("wg-sig-" + signalToken, "SetServer10", "SetServer10");
        staticData["SignalToken"] = signalToken;
        return new JsonResult(new JsonResponse(ResponseStatus.SUCCESS, staticData));
    }

    [FillFromCaptive]
    // check once
    public ActionResult Index(User user)
    {
        //Logger.GetInstance().Info($"LoginController: Index: user {JsonConvert.SerializeObject(user)}");
        if (user.backEndNasid.type_id==DBObjectType.INACTIVE_NAS)
        {
            var inventory = (List<Inventory>)CoreInventoryService.FetchInventory(new InventorySearchType() { mac = user.called });
            if(inventory.Count > 0 && inventory[0].product == "PDO")
            {
                if (inventory[0].nasid == null)
                {
                    ViewBag.deviceId = inventory[0].deviceId;
                    return View("~/Views/DeviceConfig/coming_soon.cshtml", user);
                }
                else
                {
                    user.nasid = inventory[0].nasid.ToString();
                }
            }
            else
            {
                if (user.isHomeRouter)
                {
                    return Redirect(WebUtils.GetHomeRouterBuild("hr-welcome", user));
                }
                return Redirect(WebUtils.GetWiomNetBuild("welcome", user));
            }
        }

        ViewBag.httpContainerEnabled = true;

        //Success & Already State Handled
        if (!string.IsNullOrEmpty(user.mac) && (user.res == UserState.success || user.res == UserState.already))
        {
            return NewLanding(user.GetToken(), user.mobile, string.Empty, user.GetToken(), user.backEndNasid);
        }

        string url;
        
        if (Request.Host.Host == "localhost")
            url = "https://" + Request.Host.Host + ":44300/RedirectLogin/?login-user-session=" + user.GetToken();
        else
            url = "https://" + Request.Host.Host + "/RedirectLogin/?login-user-session=" + user.GetToken();

        url = url + (Request.Query["doTest"].Count == 0 ? string.Empty : "&doTest");
        return Redirect(url);
    }

    public ActionResult SessionExpired()
    {
        return ViewTo("~/Views/Login/session_expired.cshtml", null);
    }

    [LoginPortalAuthorization]
    public JsonResult GetPlanMacMapping()
    {
        User user = CoreSessionUtils.GetLoginServerUser(HttpContext);
        var planMacMappings = CoreCacheHelper.GetInstance().GetActivePlanMacMapping(user);
        var fdmConfigs = WifidogCacheHelper.GetInstance().GetFDMConfig(user, user.isVip);
        string plan = "";
        if(fdmConfigs.Count > 0)
            plan = Util.GeneratePlanString(fdmConfigs[0].dataPlan/(1024*1024), (long)(fdmConfigs[0].otpExpiryTime - fdmConfigs[0].otpIssuedTime).TotalSeconds);
        return new JsonResult(new JsonResponse(ResponseStatus.SUCCESS, new { planMacMappings, fdmConfigs, plan }));
    }

    public ActionResult SetWaniCookie(string mobile,string token)
    {
        string gateway = "http://**********";
        if(isCookieAllowed(mobile,token))
            CookieUtils.SetCookie(HttpContext, Constants.WANI_COOKIE_NAME, mobile, false);
        return Redirect(gateway);
    }

    private static bool isCookieAllowed(string mobile,string token)
    {
        string expectedToken=LoginUtils.createCookieToken(mobile);
        if (expectedToken == token)
        {
            return true;
        }
        return false;
    }

    [LoginPortalAuthorization]
    public ActionResult RedirectLogin()
    {
        User user = CoreSessionUtils.GetLoginServerUser(HttpContext);
        Logger.GetInstance().Info($"LoginController: RedirectLogin: user {JsonConvert.SerializeObject(user)}");
        var routerConfig = CoreCacheHelper.GetInstance().GetRouterBasic(user.backEndNasid);

        user.templateid = new List<int>() { 0 };
        BasicValidator validator = WebUtils.GetValidatorFromTemplateId(user, this,HttpContext);
        user.storegroupid = routerConfig.storeGroupId;
        user.otp = "1234";

         //Success & Already State Handled
        if (!string.IsNullOrEmpty(user.mac) && (user.res == UserState.success || user.res == UserState.already))
        {
            return NewLanding(user.GetToken(), user.mobile, string.Empty, user.GetToken(), user.backEndNasid);
        }

        ViewBag.GuestAuthType = null;
        user.templateid = new List<int>() { 0 };
        user.combinedSettingId = routerConfig.combinedSettingId;
        user.isHomeRouter = routerConfig.isHomeRouter;
        ViewBag.swapLink = Util.GetLinqShortUrl(user.mobile, string.Empty, string.Empty, "LoginFlow", "i2e1", "Browser");
        ViewBag.FbPage = CoreCacheHelper.GetInstance().GetAdvanceConfigValue(user.combinedSettingId, AdvanceConfigType.FACEBOOK_PAGE);
        ViewBag.FbCheckin = CoreCacheHelper.GetInstance().GetAdvanceConfigValue(user.combinedSettingId, AdvanceConfigType.FACEBOOK_CHECKIN);

        if (user.res == UserState.notyet || user.res == UserState.logoff)
        {
            ViewBag.LoginServerUser = CoreSessionUtils.GetLoginServerUser(HttpContext);
            var localResponse = validator.ValidateFirstState(user);
            if (localResponse != null)
            {
                CoreSessionUtils.SetLoginServerUser(user, HttpContext);
                return localResponse;
            }
        }
        else if (user.res == UserState.failed)
        {
            ViewBag.sessionExpired = true;
            string reply = Request.Query["reply"];
            if (reply != null)
            {
                if (reply == "TimeExceeded")
                {
                    ViewBag.sessionExpired = true;
                }
                else if (reply == "LimitExceeded")
                {
                    ViewBag.dataExhausted = true;
                }
            }
        }

        CoreSessionUtils.SetLoginServerUser(user, HttpContext);

        if (user.res == UserState.notyet || user.res == UserState.failed || user.res == UserState.logoff)
        {
            string[] loginPage = CoreCacheHelper.GetInstance().getLoginPageConfiguration(user.backEndNasid);
            if (loginPage != null && loginPage.Length > 0)
                ViewBag.loginLogo = loginPage[0];
        }
        if (user.isHomeRouter)
        {
            return Redirect(WebUtils.GetHomeRouterBuild("hr-welcome", user));
        }
        else if (user.storegroupid == 1)
        {
            return Redirect(WebUtils.GetWiomNetBuild("welcome", user));
        }
        return validator.GetFirstPageTemplate(user);
    }

    [Route("home-router/{**segment}")]
    public PhysicalFileResult ReturnUIAssets(string segment)
    {
        return FileResult(_memoryCache, "/home-router/"+ segment);
    }
    [HttpGet]
    [Route("redirect/{segment?}")]
    public ActionResult ReturnUIFlow(string segment)
    {
        if (HttpContext.Request.Host.Host.IndexOf("localhost") > -1)
            return Redirect($"http://localhost:4200/redirect/{segment}{Request.QueryString}");

        return ContentResult(_memoryCache, "/home-router/home-router.html");
    }
    public ActionResult PurchasePlan(string mobile, string mac, string nasid, int storegroupid)
    {
        User user = new User() { 
            mobile = mobile, nasid = nasid, storegroupid = storegroupid,
            mac = mac,
            clientAuthType = AuthType.PHONE
        };
        var routerConfig = CoreCacheHelper.GetInstance().GetRouterBasic(user.backEndNasid);

        user.otp = "1234";
        user.combinedSettingId = routerConfig.combinedSettingId;

        if (user.res == UserState.notyet || user.res == UserState.logoff)
        {
            var plans = CoreCacheHelper.GetInstance().GetActivePlansOnNas(user.backEndNasid);
            if (plans != null && plans.Count > 0)
            {
                ViewBag.state = "Data Voucher State";
                ViewBag.plans = plans;
            }
        }

        CoreSessionUtils.SetLoginServerUser(user, HttpContext);

        return ViewTo("~/Views/Login/phone_number_authentication.cshtml", user);
    }

    [LoginPortalAuthorization]
    public JsonResult isFreeSessionAllowed()
    {
        User user = CoreSessionUtils.GetLoginServerUser(HttpContext);

        DateTime startTime = DateTime.UtcNow.Date;
        var freePlans = DbCalls.GetInstance().GetFreePlansCreated(user, startTime,"FREE");
        bool allowFree = false;
        if (freePlans.Count==0)
        {
            allowFree = true;
        }
        return new JsonResult(new JsonResponse(ResponseStatus.SUCCESS, new
        {
            allowFree = allowFree
        }));
        
    }

    [LoginPortalAuthorization]
    public ActionResult GetAuthenticationPage(AuthType authType, bool wipe = true)
    {
        var user = (User)HttpContext.Items["loginUser"];
        BasicValidator validator = WebUtils.GetValidatorFromTemplateId(user, this, HttpContext);
        user.clientAuthType = authType;
        user.guestmodeswitched = true;

        ViewBag.FbPage = CoreCacheHelper.GetInstance().GetAdvanceConfigValue(user.combinedSettingId, AdvanceConfigType.FACEBOOK_PAGE);
        ViewBag.FbCheckin = CoreCacheHelper.GetInstance().GetAdvanceConfigValue(user.combinedSettingId, AdvanceConfigType.FACEBOOK_CHECKIN);

        GlobalOtp gOtp = CacheHelper.GetInstance().GetGlobalOTPValue(user.combinedSettingId);
        ViewBag.GlobalOtp_Enabled = gOtp.enable;
        if (gOtp.enable)
        {
            ViewBag.GlobalOTP_Enforce = gOtp.parameters.enforce;
            ViewBag.GlobalOTP_code = gOtp.parameters.defaultcode;
        }

        string[] loginPage = CoreCacheHelper.GetInstance().getLoginPageConfiguration(user.backEndNasid);
        if (loginPage != null && loginPage.Length > 0)
            ViewBag.loginLogo = loginPage[0];

        CoreSessionUtils.SetLoginServerUser(user, HttpContext);
        /*if (user.backEndNasid == 0)
        {
            CoreDbCalls.GetInstance().SaveEventLog(user.backEndNasid, user.mobile, user.sessionid, user.mac, "BACKEND NASID : 0, NASID : " + user.nasid, USER_LOGIN.ERROR_NASID_ZERO);
        }*/

        return validator.GetFirstPageTemplate(user);
    }

    [HttpPost]
    public void LogEvent(int nasid, string mobile, string macId, string message)
    {
        Logger.GetInstance().Info(string.Format("Nas:{0}, Mac:{1}, Mobile:{2}, Message:{3}", nasid, macId, mobile, message));
    }

    [LoginPortalAuthorization]
    public JsonResult GetUser()
    {
        var user = (User)HttpContext.Items["loginUser"];
        return new JsonResult(new JsonResponse(ResponseStatus.SUCCESS, user));
    }

    public ActionResult NewLanding(string sessionid, string userid, string reply, string token, LongIdInfo nasid = null)
    {
        if (string.IsNullOrEmpty(token))
        {
            token = sessionid;
        }

        if (token.EndsWith(".ac6d65"))
        {
            return new JsonResult(new JsonResponse(ResponseStatus.SUCCESS, "Login Successful"));
        }

        if (Request.Cookies.TryGetValue("login-user-session", out var cookieToken) && !string.IsNullOrEmpty(cookieToken))
            token = cookieToken;
        else
            Request.Headers["login-user-session"] = token;

        User user = CoreSessionUtils.GetLoginServerUser(HttpContext);
        user.res = UserState.success;
        CoreSessionUtils.SetLoginServerUser(user, HttpContext);

        var fdmConfigs = WifidogCacheHelper.GetInstance().GetFDMConfig(user, user.isVip, true);
        string redirectLink = "/JsonLanding?sessionid=" + sessionid + "&userid=" + userid + "&reply=" + reply + "&token=" + token + "&nasid=" + nasid;
        string connectedFor = "internet";
        bool newFlow = false;

        if (fdmConfigs.Count > 0)
        {
            if(fdmConfigs[0].otp == "PAY_ONLINE")
            {
                connectedFor = "payment";
                if (user.attributes.ContainsKey("newFlow"))
                {
                    newFlow = true; 
					redirectLink = WebUtils.GetPaymentDeepLink(user.mobile, long.Parse(user.attributes["planId"]), user.deviceId,user.unique_identifier, user.backEndNasid, false, user.attributes["orderId"], user.attributes["uniqueplankey"], user.attributes["dynamicintentlink"]);
				}
				else
                {
                    redirectLink = WebUtils.GetPaymentLink(fdmConfigs[0].selectedPlanId, user.backEndNasid, user.mobile, user.mac, user.deviceId, uniqueIdentifier: user.unique_identifier, wifi: true);

                    PmWaniUtils.SendPMWaniLogs(HttpContext, "connected", user.unique_identifier, new Dictionary<string, object>()
                    {
                        { "dateTime", DateTime.UtcNow.ToString() },
                        { "mobile", user.mobile },
                        { "nasId", user.backEndNasid },
                        { "connected_for", connectedFor },
                        { "flow", newFlow? "CCP_on_CP" : "" },
                        { "ua", Request.Headers["User-Agent"] }
                    });
                }
            }
            else if(fdmConfigs[0].otp == "APP_OLD")
            {
                LoginUser appUser = CoreUserService.GetUpdatedAppDevice(App.HOME_ROUTER, user.mobile, 0, 0);
                redirectLink = FirebaseHelper.GetWIOMGoldAppDownloadLink("cp_" + fdmConfigs[0].otp, appUser.token);
				connectedFor = "app_download";
			}
        }

		PmWaniUtils.SendPMWaniLogs(HttpContext, "connected", user.unique_identifier, new Dictionary<string, object>()
		{
			{ "dateTime", DateTime.UtcNow.ToString() },
			{ "mobile", user.mobile },
			{ "nasId", user.backEndNasid },
            { "connected_for", connectedFor },
            { "flow", newFlow? "CCP_on_CP" : "" },
            { "ua", Request.Headers["User-Agent"] }
		});

		return Redirect(redirectLink);
    }

    public ActionResult JsonLanding(string sessionid, string userid, string reply, string token, int nasid = 0)
    {
        Logger.GetInstance().Info($"LoginController: JsonLanding: sessionid: {sessionid} userid: {userid} reply: {reply} token: {token} nasid: {nasid}");
        if (string.IsNullOrEmpty(token))
        {
            token = sessionid;
        }
        Request.Headers["login-user-session"] = token;
        User user = CoreSessionUtils.GetLoginServerUser(HttpContext);
        string customLandingPage = string.Empty;

        if (user == null)
        {
            WebUtils.LogErrorToCosmos("User not found", new { state = "JsonLanding", nasid = nasid, sessionid = sessionid, token = token, reply = reply, userid = userid });
            customLandingPage = "/ErrorPages/404.html";
            user = new User();
        }
        else
        {
            user.res = UserState.success;
            if (user.attributes.ContainsKey("linqUrl"))
            {
                customLandingPage = user.attributes["linqUrl"];
            }
            else if (user.attributes.ContainsKey("PaymentInitiated"))
            {
                ViewBag.PaymentStatus = "PaymentInitiated";
                user.res = UserState.notyet;
            }
            else
            {
                customLandingPage = CoreCacheHelper.GetInstance().GetUserBasicConfigInCombinedSetting(user, BasicConfigType.LANDING_PAGE).value;
                if (string.IsNullOrEmpty(customLandingPage))
                {
                    var validator = WebUtils.GetValidatorFromTemplateId(user, this, HttpContext);
                    customLandingPage = validator.GetLandingPage(user, "landing page");
                }
            }
            CoreSessionUtils.SetLoginServerUser(user, HttpContext);
            ViewBag.landingObject = CacheHelper.GetInstance().getLandingPageConfiguration(user.backEndNasid, user.mobile);
        }
        ViewBag.landingPage = customLandingPage.Replace("NewLanding", "LoggedIn");
        ViewBag.Reply = reply;
        return View("~/Views/Login/landing_response.cshtml", user);
    }

    public ActionResult LoggedIn(string sessionid, string userid, string reply, string token, LongIdInfo nasid)
    {
        User user;
        if (!string.IsNullOrEmpty(token))
        {
            Request.Headers.Add("login-user-session", token);
            user = CoreSessionUtils.GetLoginServerUser(HttpContext);
        }
        else
        {
            Request.Headers.Add("login-user-session", sessionid);
            user = CoreSessionUtils.GetLoginServerUser(HttpContext);
        }

        if (user != null)
        {
            var landingPage = CoreCacheHelper.GetInstance().GetUserBasicConfigInCombinedSetting(user, BasicConfigType.LANDING_PAGE).value;
            user.res = UserState.success;
            CoreSessionUtils.SetLoginServerUser(user, HttpContext);
            if (!string.IsNullOrEmpty(landingPage) && !landingPage.Contains("LoggedIn")) {
                return Redirect(landingPage);
            }
        }
        else
        {
            WebUtils.LogErrorToCosmos("User not found", new { state="LoggedIn", nasid = nasid, sessionid = sessionid, token = token, reply = reply, userid = userid });
            return Redirect("/ErrorPages/404.html");
        }

        string[] loginPage = CoreCacheHelper.GetInstance().getLoginPageConfiguration(user.backEndNasid);
        if (loginPage != null && loginPage.Length > 0)
            ViewBag.loginLogo = loginPage[0];

        ViewBag.Reply = reply;
        ViewBag.swapLink = Util.GetLinqShortUrl(user.mobile, string.Empty, string.Empty, "LoginFlow", "i2e1", "Browser");
        ViewBag.landingObject = CacheHelper.GetInstance().getLandingPageConfiguration(user.backEndNasid, user.mobile);
        //user.attributes["partner_cd"] = DbCalls.GetInstance().GetRouterPartnerId(user.backEndNasid).ToString();
        return View("~/Views/Login/landing.cshtml", user);
    }

    [LoginPortalAuthorization]
    public ActionResult DownloadApp(string sessionid, string userid, string reply, string token, int nasid = 0)
    {
        User user = null;
        if (!string.IsNullOrEmpty(token))
        {
            Request.Headers.Add("login-user-session", token);
            user = CoreSessionUtils.GetLoginServerUser(HttpContext);
        }
        else
        {
            Request.Headers.Add("login-user-session", sessionid + "-" + nasid);
            user = CoreSessionUtils.GetLoginServerUser(HttpContext);
        }
        if(!String.IsNullOrEmpty(user.mobile))
            CoreSmsSender.SendSMSViaGupshup("Hurry! Download the Linq WiFi app and get unlimited internet.\nClick here to download: https://wifilinq.page.link/invite", user.mobile, "myWIOM");
        return View("~/Views/Login/download_linq_wifi_app.cshtml", user);
    }
    [HttpGet]
    public ActionResult InitPayment(long planId, LongIdInfo nasid, string mobile, string payOnline, string mac, int storegroupid, string uniqueIdentifier, string deviceId, bool wifi = false)
    {
        return Redirect($"{WebUtils.GetWiomNetServerUrl()}/Login/InitPayment?planId={planId}&nasId={nasid}&mobile={mobile}&payOnline={payOnline}&mac={mac}&storegroupId={storegroupid}&uniqueIdentifier={uniqueIdentifier}&deviceId={deviceId}&wifi={wifi}");
    }

    [LoginPortalAuthorization]
    [CoreModelBinder]
    public JsonResult SubmitToken(string code, string provider, string state)
    {
        
        
        if (string.IsNullOrEmpty(code) || string.IsNullOrEmpty(provider))
        {
            return new JsonResult(new JsonResponse(ResponseStatus.FAILURE, "Empty token/provider"));
            
        }
        User user = (User)HttpContext.Items["loginUser"];
        UserProfile userProfile = new UserProfile();
        switch(provider) {
            case "facebook":
                userProfile = I2e1ApiClient.FetchFacebookUser(null, code,HttpContext);
                break;
            case "google":
                userProfile = I2e1ApiClient.FetchGoogleUser(code);
                break;
            default:
                return new JsonResult(new JsonResponse(ResponseStatus.FAILURE, "Invalid/Unsupported Provider"));
                
        }
        if (string.IsNullOrEmpty(userProfile.mobile) || string.IsNullOrEmpty(userProfile.email))
        {
            return new JsonResult(new JsonResponse(ResponseStatus.FAILURE, "Please give email access in the login dialog"));
            
        }

        user.mobile = userProfile.mobile;
        user.clientAuthType = AuthType.SOCIAL_LOGIN;
        CoreDbCalls.GetInstance().ReloginUser(user, out UserSession userSession, 0);
        CoreSessionUtils.SetLoginServerUser(user, HttpContext);

        BasicValidator validator = WebUtils.GetValidatorFromTemplateId(user, this, HttpContext);
        return new JsonResult(new JsonResponse(ResponseStatus.SUCCESS, LoginUtils.getRouterLoginUrlDict(user, LoginUtils.generateCHAP(user.challenge, user.otp), validator.GetLandingPage(user, "login"))));
    }

    public void pulse(string session, string nasid, string mobile)
    {
        //JsonResponse response = DatabaseRequest.SubmitPulse(session, nasid, mobile);
        return;
    }

    public JsonResult TestHit()
    {
        return new JsonResult("success");
    }

    [LoginPortalAuthorization]
    public JsonResult ChangeToGuestMode()
    {
        User user = (User)HttpContext.Items["loginUser"];
        user.guestmodeswitched = true;
        CoreSessionUtils.SetLoginServerUser(user, HttpContext);
        
        
        return new JsonResult(new JsonResponse(ResponseStatus.SUCCESS, string.Empty, null));
        
    }

    [HttpPost]
    [LoginPortalAuthorization]
    [CoreModelBinder]
    public JsonResult SubmitAnswers(User user1, List<Question> questions)
    {
        
        if (questions != null && questions.Count > 0)
        {
            try
            {
                var validator = WebUtils.GetValidatorFromTemplateId(user1, this, HttpContext);
                validator.SubmitAnswers(user1, questions);
            }
            catch(Exception ex)
            {
                WebUtils.LogErrorToCosmos("failed to submit answers", new { method = "SubmitAnswers", response = ex.ToString() });
                return new JsonResult(new JsonResponse(ResponseStatus.FAILURE, ex.Message));
            }
        }
        return new JsonResult(new JsonResponse(ResponseStatus.SUCCESS, "Answer submitted"));
        
    }

    [HttpPost]
    [LoginPortalAuthorization]
    [CoreModelBinder]
    public JsonResult GenerateOTP(User user1, List<Question> questions)
    {

        if (string.IsNullOrEmpty(user1.mobile))
        {
            return new JsonResult(new JsonResponse(ResponseStatus.FAILURE, "Enter details to proceed", null));
            
        }

        User user = (User)HttpContext.Items["loginUser"];
        user.mobile = user1.mobile;

        user.clientAuthType = GetBackendAuthType(user1.clientAuthType.Value);
        user.askaccesscode = user.askaccesscode && !user.guestmodeswitched;
        user.smsapi = user1.smsapi;
        user.smscount++;
        if (user.smscount >= 10 && user.clientAuthType == AuthType.PHONE)
        {
            return new JsonResult(new JsonResponse(ResponseStatus.FAILURE, "Too many attempts", null));
            
        }

        var validator = WebUtils.GetValidatorFromTemplateId(user, this, HttpContext);
        FDMConfig conf = validator.GetFDMConfig(user);

        if(questions != null && questions.Count > 0)
        {
            try
            {
                validator.SubmitAnswers(user, questions);
            }
            catch(Exception ex)
            {
                WebUtils.LogErrorToCosmos("failed to submit answers", new { method = "GenerateOTP", response = ex.ToString() });
                return new JsonResult(new JsonResponse(ResponseStatus.FAILURE, ex.Message));
                
            }
        }

        UserSession userSession = CoreCacheHelper.GetInstance().GetUserSessions(user, user.isVip, out bool noMapingExists);
        RadiusUserResponse radiusUserResponse = userSession == null ? RadiusUserResponse.FAILED : userSession.radiusUserResponse;
        var isEligible = UserAttribute.IsEligibleForLogin(user.mobile, user.backEndNasid, user.mac, out bool isVip);

        user.isVip = isVip;
        if (!isEligible)
        {
            return new JsonResult(new JsonResponse(ResponseStatus.BLOCKED, "Access is Blocked By Administrator", null));
            
        }
        else if (userSession != null)
        {
            if (radiusUserResponse == RadiusUserResponse.SESSION_EXHAUSTED)
            {
                return new JsonResult(new JsonResponse(ResponseStatus.FAILURE, "Your session is over", null));
                
            }
            else if (radiusUserResponse == RadiusUserResponse.DATA_EXHAUSTED)
            {
                return new JsonResult(new JsonResponse(ResponseStatus.FAILURE, "Your data limit is over", null));
                
            }
        }

        JsonResponse response = validator.GenerateOTPValidation(user, conf);
      
        CoreSessionUtils.SetLoginServerUser(user, HttpContext);
        return new JsonResult(new JsonResponse(response.status, response.msg, new {
            otpResponse = response.data
        }));
        
    }

    [HttpPost]
    [LoginPortalAuthorization]
    [CoreModelBinder]
    public JsonResult GetQuestions(List<QuestionType> questionTypes, string mobile)
    {
        int rowId = 0;
        User user = (User)HttpContext.Items["loginUser"];
        List<Question> questions = null;
        BasicValidator validator = WebUtils.GetValidatorFromTemplateId(user, this, HttpContext);
        if (questionTypes.Count == 2)
        {
            rowId = ClientDatabaseRequest.InsertQuestionRequest(user.backEndNasid, mobile, user.GetToken(), user.GetBaseTemplateId());
            questions = validator.GetSecondPageQuestions(mobile, user);
        }
        else
        {
            questions = validator.GetFirstPageQuestions(String.Empty, user, questionTypes);
        }

        if(questions != null && questions.Count > 0)
        {
            questions = questions.Where(m =>
            {
                return questionTypes.Contains(m.quesType);
            }).ToList();
        }
        
        return new JsonResult(new JsonResponse(ResponseStatus.SUCCESS, questions));
        
    }

    [LoginPortalAuthorization]
    public JsonResult DoUserLogin()
    {
        User user = (User)HttpContext.Items["loginUser"];

        CoreDbCalls.GetInstance().ReloginUser(user, out var userSession, 0);
        return new JsonResult(new JsonResponse(ResponseStatus.SUCCESS, null));
        
    }

    [HttpPost]
    [LoginPortalAuthorization]
    [CoreModelBinder]
    public JsonResult SubmitOTP(User user1, string captcha, string accessCode, List<Question> questions, bool doLogin = true)
    {
        string errorMsg, captchaUrl;
        User user = (User)HttpContext.Items["loginUser"];
        
        
        if (user.clientAuthType != AuthType.NATIONAL_ID)
            user.otp = user1.otp;
        else if(user.clientAuthType == AuthType.PHONE_OR_DATA_VOUCHER_WITHOUT_OTP)
        {
            if (string.IsNullOrEmpty(accessCode))
                user.clientAuthType = AuthType.PHONE;
            else
                user.clientAuthType = AuthType.DATA_VOUCHER_WITHOUT_OTP;
        }

        var validator = WebUtils.GetValidatorFromTemplateId(user, this, HttpContext);

        if (string.IsNullOrEmpty(user.mobile))
        {
            user.mobile = user1.mobile;
        }

        if (CacheHelper.GetInstance().GetTemplateContent(user.GetBaseTemplateId()).isFullOverriden)
        {
            var state = MyCaptcha.CaptchaValidate("SubmitOtp", user.mobile, captcha, out errorMsg, out captchaUrl);
            if (state == CaptchaState.SHOW_CAPTCHA)
            {
                JsonResponse response = new JsonResponse(ResponseStatus.FAILURE, errorMsg, captchaUrl);
                return new JsonResult(response);
                
            }
        }
        var plans = CoreCacheHelper.GetInstance().GetActivePlansOnNas(user.backEndNasid);
        if (plans != null && plans.Count > 0)
        {
            doLogin = false;
        }
        return new JsonResult(validator.ValidateSecondState(user, accessCode, questions, doLogin, plans));

    }

    [HttpPost]
    [LoginPortalAuthorization]
    [CoreModelBinder]
    public JsonResult SubmitPlan(string captcha, string accessCode, long planId = 0)
    {
        string errorMsg, captchaUrl;
        User user = (User)HttpContext.Items["loginUser"];

        JsonResponse response = null;

        DataVoucherValidator validator = new DataVoucherValidator(this, HttpContext);

        if (CacheHelper.GetInstance().GetTemplateContent(user.GetBaseTemplateId()).isFullOverriden)
        {
            var state = MyCaptcha.CaptchaValidate("SubmitOtp", user.mobile, captcha, out errorMsg, out captchaUrl);
            if (state == CaptchaState.SHOW_CAPTCHA)
            {
                response = new JsonResponse(ResponseStatus.FAILURE, errorMsg, captchaUrl);
                return new JsonResult(response);
                
            }
        }
        user.otp = "1234";
        if (planId == 0 && !string.IsNullOrEmpty(accessCode))
            return new JsonResult(validator.ValidatePlan(user, accessCode, DateTime.UtcNow));
        else
        {
            var plans = CoreCacheHelper.GetInstance().GetActivePlansOnNas(user.backEndNasid);
            if(plans != null && plans.Count > 0)
            {
                var plan = plans.Find(m => m.id == planId);
                /*var pUser = new PassportUser()
                {
                    mobile = user.mobile,
                    dataPlan = plan.data_limit,
                    otpExpiryTime = DateTime.UtcNow.AddSeconds(plan.time_limit)
                };*/

                HomeRouterPlan hUser = new HomeRouterPlan();
                SecondaryRouterPlan sUser = new SecondaryRouterPlan();
                if (user.storegroupid == 1)
                {
                    sUser = new SecondaryRouterPlan()
                    {
                        mobile = user.mobile,
                        dataLimit = plan.data_limit,
                        planStartTime= DateTime.UtcNow,
                        planEndTime = DateTime.UtcNow.AddSeconds(plan.time_limit)
                    };
                }
                else
                {
                    hUser = new HomeRouterPlan()
                    {
                        nasId = user.backEndNasid,
                        mobile = user.mobile,
                        dataLimit = plan.data_limit,
                        planStartTime= DateTime.UtcNow,
                        planEndTime = DateTime.UtcNow.AddSeconds(plan.time_limit)
                    };
                }


                if (plan != null)
                {
                    if (plan.price == 0)
                    {
                        /*pUser.otp = HOMEOTP.FREE;*/
                        if(user.storegroupid == 1)
                        {
                            sUser.otp = HOMEOTP.FREE;
                        }
                        else
                        {
                            hUser.otp = HOMEOTP.FREE;
                        }
                    }
                    long fdmId = user.storegroupid == 1 ? CoreDbCalls.GetInstance().GenerateUser(sUser, user.backEndNasid, 1, true, Convert.ToInt32(plan.price)) : CoreDbCalls.GetInstance().GenerateUser(hUser, user.backEndNasid, 1, true, Convert.ToInt32(plan.price));
                    CoreDbCalls.GetInstance().ReloginUser(user, out var userSession, fdmId);
                    var data = LoginUtils.getRouterLoginUrlDict(user, LoginUtils.generateCHAP(user.challenge, user.otp), validator.GetLandingPage(user, "login"));
                    return new JsonResult(new JsonResponse(ResponseStatus.SUCCESS, string.Empty, data));
                }
                else
                {
                    return new JsonResult(new JsonResponse(ResponseStatus.FAILURE, "No Plan Found"));
                }
            }
            else
            {
                throw new Exception("Plan flow does not exists");
            }
        }
        
    }

    [HttpPost]
    [LoginPortalAuthorization]
    [CoreModelBinder]
    public JsonResult CheckOTP(User user1, string captcha, string accessCode, List<Question> questions, bool doLogin = true)
    {
        string errorMsg, captchaUrl;
        User user = (User)HttpContext.Items["loginUser"];
        
        
        if (user.clientAuthType != AuthType.NATIONAL_ID)
            user.otp = user1.otp;
        else if (user.clientAuthType == AuthType.PHONE_OR_DATA_VOUCHER_WITHOUT_OTP)
        {
            if (string.IsNullOrEmpty(accessCode))
                user.clientAuthType = AuthType.PHONE;
            else
                user.clientAuthType = AuthType.DATA_VOUCHER_WITHOUT_OTP;
        }

        var validator = WebUtils.GetValidatorFromTemplateId(user, this, HttpContext);
        if (string.IsNullOrEmpty(user.mobile))
        {
            user.mobile = user1.mobile;
        }

        if (CacheHelper.GetInstance().GetTemplateContent(user.GetBaseTemplateId()).isFullOverriden)
        {
            var state = MyCaptcha.CaptchaValidate("SubmitOtp", user.mobile, captcha, out errorMsg, out captchaUrl);
            if (state == CaptchaState.SHOW_CAPTCHA)
            {
                JsonResponse response = new JsonResponse(ResponseStatus.FAILURE, errorMsg, captchaUrl);
                return new JsonResult(response);
                
            }
        }

        return new JsonResult(validator.ValidateOTP(user));
        
    }

    [HttpPost]
    public JsonResult GetLandingPage([FromBody] User user)
    {
        var landingPage = CacheHelper.GetInstance().getLandingPageConfiguration(user.backEndNasid, user.mobile);
        
        
        return new JsonResult(new JsonResponse(ResponseStatus.SUCCESS, null, landingPage));
        
    }
    
    [LoginPortalAuthorization]
    public JsonResult GetUserAttributes()
    {
        User user = CoreSessionUtils.GetLoginServerUser(HttpContext);
        if (string.IsNullOrEmpty(user.mobile))
        {
            return new JsonResult(new JsonResponse(ResponseStatus.FAILURE, "Mobile number is null or blank"));
        }

        var userSession = CoreCacheHelper.GetInstance().GetUserSessions(user, user.isVip, out bool noMapingExists);
        long dataLeft = 0, timeLeft = 0;

        if(userSession != null)
        {
            if (userSession.accessCode == "PAY_ONLINE")
            {
                var fdmConfigs = WifidogCacheHelper.GetInstance().GetFDMConfig(user, user.isVip);
                if (fdmConfigs.Count > 0)
                {
                    dataLeft = fdmConfigs[0].dataPlan;
                    timeLeft = (long)(fdmConfigs[0].otpExpiryTime - fdmConfigs[0].otpIssuedTime).TotalSeconds;
                }
            }
            else
            {
                dataLeft = userSession.CalculateDataLeft();
                timeLeft = userSession.CalculateSecondsLeft();
            }
            return new JsonResult(new JsonResponse(ResponseStatus.SUCCESS, null, new { dataLeft = dataLeft, timeLeft = timeLeft, status = userSession.radiusUserResponse.ToString() }));
        }
        return new JsonResult(new JsonResponse(ResponseStatus.FAILURE, "Invalid Session"));
        
    }

    [LoginPortalAuthorization]
    public JsonResult GetUserRewardPoints()
    {
        User user = (User)HttpContext.Items["loginUser"];
        return new JsonResult(new JsonResponse(ResponseStatus.SUCCESS, I2e1ApiClient.FetchSnaplionRewardPoints(user)));
    }

    [HttpPost]
    [LoginPortalAuthorization]
    public JsonResult Logout()
    {
        User user = CoreSessionUtils.GetLoginServerUser(HttpContext);
        DatabaseRequest.LogoutUser(user.backEndNasid, user.storegroupid, user.mobile, user.isHomeRouter);
        return new JsonResult(new JsonResponse(ResponseStatus.SUCCESS, null, ""));
    }

    [LoginPortalAuthorization]
    [CoreModelBinder]
    public ActionResult EasyRewardsSignUp(string mobile, string name, string email, string gender, int date, int month, int year)
    {
        DateTime dob = new DateTime(1990, 1, 1);
        try
        {
            dob = new DateTime(year, month, date);
        }
        catch(Exception ex)
        {
            Logger.GetInstance().Error(ex.ToString() + "Data:" + year + "/" + month + "/" + date);
        }

        UserProfile userProfile = new UserProfile()
        {
            mobile = mobile,
            name = name,
            email = email,
            gender = gender
        };
        var user = (User)HttpContext.Items["loginUser"];
        user.mobile = mobile;
        var validator = WebUtils.GetValidatorFromTemplateId(user, this, HttpContext);
        var jsonResponse = validator.PostUserRegistration(user, userProfile);
        if(jsonResponse.status == ResponseStatus.SUCCESS)
        {
            CoreSessionUtils.SetLoginServerUser(user, HttpContext);
        }
        return new JsonResult(jsonResponse);
    }

    //Returns the actual auth type at the backend
    private AuthType? GetBackendAuthType(AuthType authType)
    {
        switch (authType)
        {
            case AuthType.PHONE:
                return AuthType.PHONE;
            case AuthType.REGISTERED_MOBILE:
                return AuthType.REGISTERED_MOBILE;
            case AuthType.NATIONAL_ID:
            case AuthType.LAST_NAME_ROOM_NO:
            case AuthType.ACCESS_CODE:
            case AuthType.PHONE_WITHOUT_OTP:
                return AuthType.NATIONAL_ID;
            case AuthType.SOCIAL_LOGIN:
                return AuthType.SOCIAL_LOGIN;
            case AuthType.WANI_LOGIN:
                return AuthType.WANI_LOGIN;
            case AuthType.DATA_VOUCHER:
            case AuthType.PHONE_OR_DATA_VOUCHER_WITHOUT_OTP:
            case AuthType.DATA_VOUCHER_WITHOUT_OTP:
                return authType;

        }
        return AuthType.PHONE;
    }

    [LoginPortalAuthorization]
    public JsonResult GetMerchantDetails()
    {
        
        User user = CoreSessionUtils.GetLoginServerUser(HttpContext);
        return new JsonResult(new JsonResponse(ResponseStatus.SUCCESS, "", DatabaseRequest.GetMerchantDetails(user)));
        
    }

    [LoginPortalAuthorization]
    [CoreModelBinder]
    public JsonResult confirmPlanPayment(long fdmId)
    {
        
        User user = CoreSessionUtils.GetLoginServerUser(HttpContext);
        var conf = WifidogCacheHelper.GetInstance().GetFDMConfig(user, user.isVip);
        var plan = conf.Find(m => m.id == fdmId);
        if (plan == null)
        {
            return new JsonResult(new JsonResponse(ResponseStatus.FAILURE, "payment not verified"));
        }
        else if(plan.otp == Constants.CASH_DONE && !FDMConfig.IsConfigExpired(plan))
        {
            CoreDbCalls.GetInstance().ReloginUser(user, out var userSession, plan.id);
            var validator = WebUtils.GetValidatorFromTemplateId(user, this, HttpContext);
            var data = LoginUtils.getRouterLoginUrlDict(user, LoginUtils.generateCHAP(user.challenge, user.otp), validator.GetLandingPage(user, "login"));
            return new JsonResult(new JsonResponse(ResponseStatus.SUCCESS, "payment verified", data));
        }
        return new JsonResult(new JsonResponse(ResponseStatus.FAILURE, "payment not verified"));
        
    }

    [CoreModelBinder]
    public JsonResult getPaymentModes(LongIdInfo nasid)
    {
        var routerBasic = CoreCacheHelper.GetInstance().GetRouterBasic(nasid);

        var parameter = CoreCacheHelper.GetInstance().GetAdvanceConfigValue(routerBasic.combinedSettingId, AdvanceConfigType.PAYMENT_MODE);
        if (!string.IsNullOrEmpty(parameter))
        {
            switch (parameter)
            {
                case "ONLINE_AND_COUPON":
                    return new JsonResult(new JsonResponse(ResponseStatus.SUCCESS, "", "acctInfo:{}"));
                case "ONLINE_ONLY":
                    return new JsonResult(new JsonResponse(ResponseStatus.SUCCESS, "", "acctInfo:{};coupon:false"));
                case "COUPON_ONLY":
                    return new JsonResult(new JsonResponse(ResponseStatus.SUCCESS, "", ""));
            }
        }
        if (routerBasic.storeGroupId == 1)
        {
            return new JsonResult(new JsonResponse(ResponseStatus.SUCCESS, "", "acctInfo:{}"));
        }

        return new JsonResult(new JsonResponse(ResponseStatus.SUCCESS, "",
            CoreDbCalls.GetInstance().GetSingleNasOperation(nasid, 21)));
    }

    //Customer portal apis
    [LoginPortalAuthorization]
    [CoreModelBinder]
    public JsonResult GetMyDataUsage(DateTime startTime, DateTime endTime)
    {
        if (startTime == null)
            startTime = DateTime.UtcNow.AddDays(-30);
        if (endTime == null)
            endTime = DateTime.UtcNow;

        User user = CoreSessionUtils.GetLoginServerUser(HttpContext);
        TimeUtils.normalizeTime(ref startTime, ref endTime);
        var nasids = new List<int>();
        List<HomeRouterPlan> dataUsage = DatabaseRequest.GetMyWIOMHistory(user, startTime, endTime);
        
        return new JsonResult(new JsonResponse(ResponseStatus.SUCCESS, "", dataUsage));
    }

    [LoginPortalAuthorization]
    [CoreModelBinder]
    public JsonResult GetDetailedDataUsage()
    {
        User user = CoreSessionUtils.GetLoginServerUser(HttpContext);
        var nasids = new List<int>();
        var dataUsage = RadAcctDbCalls.GetDetailedDataReport(true, new List<LongIdInfo>() { user.backEndNasid }, user.mobile, DateTime.UtcNow.AddDays(-30), DateTime.UtcNow);

        return new JsonResult(new JsonResponse(ResponseStatus.SUCCESS, "", dataUsage));
    }

    [CoreModelBinder]
    public JsonResult GetPDOList(LongIdInfo nasid)
    {
        var routerBasic = CoreCacheHelper.GetInstance().GetRouterBasicDetails(nasid);
        string pdoList = getPDOList(routerBasic);
        if (string.IsNullOrEmpty(pdoList))
        {
            return new JsonResult(new JsonResponse(ResponseStatus.FAILURE, "No PDO Found"));
        }
        
        return new JsonResult(new JsonResponse(ResponseStatus.SUCCESS, "", new
        {
            pdo_list = pdoList
        }));
    }

    [HttpGet]
    public JsonResponse GetDevicePlans(LongIdInfo nasid)
    {
        var response = new List<JObject>();

        if (CoreDbCalls.GetInstance().GetPlanExpiryByDeviceLimit(nasid, Constants.HOME_ROUTER_DEVICE_LIMIT, out var mobile, out var firstRechargeTime, out var expiryTime, out var passportUser))
        {
            var jObject = new JObject();
            var ssid = CoreDbCalls.GetInstance().GetSingleNasOperation(nasid, (int)ListCheckDataType.SSID);
            jObject.Add("nasid", JsonConvert.SerializeObject(nasid));
            jObject.Add("mobile", mobile);
            jObject.Add("ssid", ssid);
            jObject.Add("otp_expiry_time", expiryTime);
            jObject.Add("seconds_in_expiry", (expiryTime - DateTime.UtcNow).TotalSeconds);
            response.Add(jObject);
        }

        return new JsonResponse(ResponseStatus.SUCCESS, response);
    }

    [HttpPost]
    [CoreModelBinder]
    [LoginPortalAuthorization]
    public JsonResult SendWiomDownloadLink(string mobile, string source)
    {
        var user = CoreSessionUtils.GetLoginServerUser(HttpContext);

        if (!string.IsNullOrEmpty(mobile))
        {
            user.mobile = mobile;
            CoreSessionUtils.SetLoginServerUser(user, HttpContext);
        }
        
        string message;
        string wlToken = null;
        if(!string.IsNullOrEmpty(user.mobile))
            wlToken = CoreUserService.GetUpdatedAppDevice(App.HOME_ROUTER, user.mobile, 0, 0).token;
        
        string longLink = FirebaseHelper.GetWIOMGoldAppDownloadLink("cp_" + source, wlToken);
        string appDownloadLink = CoreUtil.Shorten(longLink, DateTime.UtcNow.AddMonths(3));
        if(source == "new-user")
            message = "To set up your WIOM connection, get WIOM app. Click here: " + appDownloadLink;
        else
            message = "Pay your WIOM Gold bill by clicking here: " + appDownloadLink;

        CoreSmsSender.SendTransactionalSMSViaInfini(message, mobile, "myWIOM");
        return new JsonResult(new JsonResponse(ResponseStatus.SUCCESS, "Sms Sent"));
    }

    [HttpPost]
    [CoreModelBinder]
    [LoginPortalAuthorization]
    public JsonResult ActivatePayLater()
    {
        var user = CoreSessionUtils.GetLoginServerUser(HttpContext);

        long fdmId = doActivatePayLater(user.backEndNasid, user.storegroupid);
        if (fdmId != 0)
        {
            CoreCacheHelper.GetInstance().SetPlanMacMapping(user, fdmId);
            CoreDbCalls.GetInstance().ReloginUser(user, out var userSession, fdmId);
            var validator = WebUtils.GetValidatorFromTemplateId(user, this, HttpContext);
            Dictionary<string, object> data = LoginUtils.getRouterLoginUrlDict(user, LoginUtils.generateCHAP(user.challenge, user.otp), validator.GetLandingPage(user, "login"));
            return new JsonResult(new JsonResponse(ResponseStatus.SUCCESS, string.Empty, data));
        }
        return new JsonResult(new JsonResponse(ResponseStatus.FAILURE, "Unable to Activate"));
    }

    [HttpPost]
    [CoreModelBinder]
    public JsonResult ActivatePayLaterViaApp(LongIdInfo nasid)
    {
        var routerBasic = CoreCacheHelper.GetInstance().GetRouterBasic(nasid);
        long fdmId = doActivatePayLater(nasid, routerBasic.storeGroupId);
        if (fdmId != 0)
        {
            return new JsonResult(new JsonResponse(ResponseStatus.SUCCESS, string.Empty, fdmId));
        }
        return new JsonResult(new JsonResponse(ResponseStatus.FAILURE, "Unable to Activate"));
    }

    public JsonResult GetConnectedDevices(LongIdInfo nasid)
    {
        var routerBasic = CoreCacheHelper.GetInstance().GetRouterBasic(nasid);
        var user = new User();
        user.nasid = nasid.ToString();
        user.storegroupid = routerBasic.storeGroupId;
        user.isHomeRouter = routerBasic.isHomeRouter;
        user.mobile = WifidogDbCalls.GetInstance().GetMobileFromMac(user);
        if (string.IsNullOrEmpty(user.mobile))
        {
            user.mobile = CookieUtils.GetCookie(HttpContext, "otp-verified");
        }
        var list = CoreCacheHelper.GetInstance().GetActivePlanMacMapping(user);

        return new JsonResult(new JsonResponse(ResponseStatus.SUCCESS, list));
    }

    public ActionResult CheckInternet(string url)
    {
        return Redirect(url);
    }

    private long doActivatePayLater(LongIdInfo longNasId, int storeGroupId)
    {
        if (CoreDbCalls.GetInstance().GetPlanExpiryByDeviceLimit(longNasId, Constants.HOME_ROUTER_DEVICE_LIMIT, out var mobile, out var firstRechargeTime, out var expiryTime, out var passportUser))
        {
            int timeInSeconds = 0;
            var current = DateTime.UtcNow;

            var startArray = I2e1ConfigurationManager.RENEWAL_AFTER_DUE_DATE_CONFIG["captive_start"];
            string timeInHHmm = current.ToString("HH:mm");
            for (int i = 0; i < startArray.Count(); i++)
            {
                if (timeInHHmm.CompareTo(startArray[i].ToString()) > 0)
                {
                    if(i + 1 < startArray.Count() && timeInHHmm.CompareTo(startArray[i+1].ToString()) < 0)
                    {
                        timeInSeconds = (int)(current.Date.Add(TimeSpan.Parse(startArray[i+1].ToString())) - current).TotalSeconds;
                    }
                    else
                    {
                        timeInSeconds = (int)(current.Date.AddDays(1).Add(TimeSpan.Parse(startArray[0].ToString())) - current).TotalSeconds;
                    }
                    break;
                }
            }

            if (timeInSeconds == 0)
            {
                timeInSeconds = (int)(current.Date.Add(TimeSpan.Parse(startArray[0].ToString())) - current).TotalSeconds;
            }
            /*PassportUser pUser = new PassportUser()
            {
                email = string.Empty,
                mobile = mobile,
                timePlan = timeInSeconds,
                createdOn = current,
                status = "active",
                otpExpiryTime = current.AddSeconds(timeInSeconds),
                otp = "BUFFER"
            };
            pUser.extraDataObject = new PassportUserExtraData()
            {
                bought_from = "system",
                bought_from_nas = longNasId.ToString(),
                time_limit = timeInSeconds.ToString(),
                isTemp = true
            };*/
            HomeRouterPlan pUser = new HomeRouterPlan()
            {
                mobile = mobile,
                timePlan = timeInSeconds,
                createdTime = current,
                planStartTime = current,
                planEndTime = current.AddSeconds(timeInSeconds),
                otp = HOMEOTP.BUFFER,
                source = "system",
                nasId = longNasId
            };
            return CoreDbCalls.GetInstance().GenerateUser(pUser, longNasId, Constants.HOME_ROUTER_DEVICE_LIMIT, true, 0);
        }
        return 0;
    }

    private string getPDOList(RouterBasicDetails routerBasic)
    {
        string data;
        int combinedSettingId = routerBasic.combinedSettingId;
        if (routerBasic.isHomeRouter)
        {
            combinedSettingId = 1009809;
        }

        if(!_memoryCache.TryGetValue("PDO_LIST_" + combinedSettingId, out data))
        {
            var fileName = AppContext.BaseDirectory + "/resources/pdo_list/" + combinedSettingId + ".json";
            if (System.IO.File.Exists(fileName))
            {
                using (var streamReader = new StreamReader(fileName))
                {
                    data = streamReader.ReadToEnd();
                }
                _memoryCache.Set("PDO_LIST_" + combinedSettingId, data, new TimeSpan(24, 0, 0));
            }
        }
        
        return data;
    }

    [CoreModelBinder]
    public JsonResult SetOtpVerifiedCookie(string mobile)
    {
        Logger.GetInstance().Info("SetOtpVerifiedCookie called with mobile number : " + mobile);
        if (string.IsNullOrEmpty(mobile))
        {
            Logger.GetInstance().Error("mobile number is null");
            return JsonResult(ResponseStatus.FAILURE, "Not getting mobile number to set token");
        }
        CookieUtils.SetCookie(HttpContext, "otp-verified", mobile, false, DateTime.UtcNow.AddDays(90), true, SameSiteMode.None, HttpContext.Request.Host.Host);
        var responseCookies = HttpContext.Response.Headers["Set-Cookie"];
        Logger.GetInstance().Info("Response Cookies: " + responseCookies);
        return JsonResult(ResponseStatus.SUCCESS, mobile);
    }

    [CoreModelBinder]
    public JsonResult SetPhoneNumberFoundCookie(string mobile)
    {
        Logger.GetInstance().Info("SetPhoneNumberFoundCookie called with mobile number : " + mobile);
        if (string.IsNullOrEmpty(mobile))
        {
            Logger.GetInstance().Error("mobile number is null");
            return JsonResult(ResponseStatus.FAILURE, "Not getting mobile number to set token");
        }

        Logger.GetInstance().Info("Setting pm wani for: " + mobile);
        CookieUtils.SetCookie(HttpContext, "phonenumber-found", mobile, false, DateTime.UtcNow.AddDays(90), true, SameSiteMode.None, HttpContext.Request.Host.Host);
        var responseCookies = HttpContext.Response.Headers["Set-Cookie"];
        Logger.GetInstance().Info("Response Cookies: " + responseCookies);
        return JsonResult(ResponseStatus.SUCCESS, mobile);
    }

    public JsonResult CheckIfCookieExists()
    {
        Logger.GetInstance().Info("Checking if OTP-verified or PhoneNumber-found cookie exists");

        string otpVerified = CookieUtils.GetCookie(HttpContext, "otp-verified");
        if (!string.IsNullOrEmpty(otpVerified))
        {
            Logger.GetInstance().Info("Response of OTP-verified cookie: " + otpVerified);
            return JsonResult(ResponseStatus.SUCCESS, "otpVerified", otpVerified);
        }

        string phoneNumberFound = CookieUtils.GetCookie(HttpContext, "phonenumber-found");
        if (!string.IsNullOrEmpty(phoneNumberFound))
        {
            Logger.GetInstance().Info("Response of PhoneNumber-found cookie: " + phoneNumberFound);
            return JsonResult(ResponseStatus.SUCCESS, "phoneNumberFound", phoneNumberFound);
        }

        Logger.GetInstance().Info("No relevant cookies found");
        return JsonResult(ResponseStatus.FAILURE, "No relevant cookies found");
    }

    
    public JsonResult IsOTTEnabled()
    {
        try
        {
            Logger.GetInstance().Info("LoginController: IsOTTEnabled: Checking for OTT feature");

            bool isOTTEnabled = CoreCacheHelper.GetInstance().getValueFromCache(CoreCacheHelper.WIOM_NET_OTT_STATUS, "", () =>
            {
                return CoreDbCalls.GetInstance().ReadFromGlobalParam("WIOM_NET_OTT_STATUS");
            });
            return JsonResult(ResponseStatus.SUCCESS, isOTTEnabled);
        }
        catch (Exception ex)
        {
            Logger.GetInstance().Error($"LoginController: IsOTTEnabled: Found Exception while executing IsOTTEnabled with exception: {ex.Message}");
            return JsonResult(ResponseStatus.FAILURE, $"Caught Exception while executing IsOTTEnabled with exception: {ex.Message}");
        }
    }
    

    [HttpGet]
    public JsonResult ConfirmUserActiveUser()
    {
        Logger.GetInstance().Info($"LoginController: ConfirmUserActiveUser: Checking If User  login with in 90 days.");
        try
        {
            string phoneNumberFound = CookieUtils.GetCookie(HttpContext, "phonenumber-found");
            if (!string.IsNullOrEmpty(phoneNumberFound))
            {
                Logger.GetInstance().Info("Response of PhoneNumber-found cookie: " + phoneNumberFound);
                object res = new
                {
                    message = "User did not connect to SS within 90 Days",
                    mobile = phoneNumberFound
                };
                return JsonResult(ResponseStatus.SUCCESS, "phoneNumberFound", res);
            }
            else
            {
                Logger.GetInstance().Info("No relevant cookies found");
                object res = new
                {
                    message = "User did not connect to SS within 90 Days",
                    mobile = ""
                };
                return JsonResult(ResponseStatus.FAILURE, res);
            }
        }
        catch (Exception ex)
        {
            Logger.GetInstance().Error($"LoginController: ConfirmUserActiveUser: Caught exception: {JsonConvert.SerializeObject(ex)}");
            return JsonResult(ResponseStatus.FAILURE, ex.Message);
        }
    }
   
    public JsonResult GetMinimumWiomNetPlans()
    {
        try
        {
            Logger.GetInstance().Info("Checking if WiomNet Plan exist with minimum value");

            List<PDOPlan> secondarySignalPlans = CoreCacheHelper.GetInstance().getValueFromCache(CoreCacheHelper.SERVICE_PLAN_CONF, 1, () =>
            {
                return CoreDbCalls.GetInstance().GetPlanConfigInCombinedSetting(1).Where(m => m.active).ToList();
            });
            double price = secondarySignalPlans.Any() ? secondarySignalPlans.Where((p) => p.time_limit == 86400).Min(m => m.price) : 0;
            
            Logger.GetInstance().Info("Fetched GetMinimumWiomNet  plan price : " + JsonConvert.SerializeObject(price));
            return JsonResult(ResponseStatus.SUCCESS, price);
        }
        catch (Exception ex)
        {
            Logger.GetInstance().Error($"Found Exception while executing GetMinimumWiomNetPlans with exception: {ex}");
            return JsonResult(ResponseStatus.FAILURE, $"Caught Exception while executing GetMinimumWiomNetPlans with exception: {ex} ");
        }
    }
    [LoginPortalAuthorization]
    [CoreModelBinder]
    public JsonResult NotifyOperator(string upiId, string payMode, bool isMobileOrTablet = true, long id = 0, string lang = "hi", string mobile = "", string distinctId = "", bool isAuthenticated = false, long planId = 0)
    {
        Logger.GetInstance().Info($"LoginController:NotifyOperator:upiId: {upiId}, payMode: {payMode}, isMobileOrTablet: {isMobileOrTablet}, id: {id}, planId: {planId}, mobile: {mobile}, lang: {lang}, distinctId: {distinctId}, isAuthenticated: {isAuthenticated}");
        try
        {
            User user = (User)HttpContext.Items["loginUser"];
            if (!string.IsNullOrEmpty(mobile))
                user.mobile = mobile;
            else mobile = user.mobile;
            var validator = WebUtils.GetValidatorFromTemplateId(user, this, HttpContext);
            bool payViaUpi = payMode == "upi";
            bool payOnline = payMode == "online";

            var plans = CoreCacheHelper.GetInstance().GetActivePlansOnNas(user.backEndNasid);
            if (plans != null && plans.Count > 0)
            {
                var selectedPlan = plans.FirstOrDefault(m => m.id == id);
                if (selectedPlan != null)
                {
                    SecondaryRouterPlan sUser = new SecondaryRouterPlan();
                    HomeRouterPlan hUser = new HomeRouterPlan();
                    if (user.storegroupid == 1)
                    {
                        sUser = new SecondaryRouterPlan()
                        {
                            mobile = user.mobile,
                            timePlan = selectedPlan.time_limit,
                            charges = Convert.ToInt32(selectedPlan.price),
                            createdTime = DateTime.UtcNow,
                            nasId = LongIdInfo.IdParser(long.Parse(user.nasid)),
                            dataLimit = selectedPlan.data_limit,
                            planStartTime = DateTime.UtcNow,
                            planId = (int)selectedPlan.id
                        };
                    }
                    else
                    {
                        hUser = new HomeRouterPlan()
                        {
                            mobile = user.mobile,
                            timePlan = selectedPlan.time_limit,
                            charges = Convert.ToInt32(selectedPlan.price),
                            createdTime = DateTime.UtcNow,
                            source = "wiom_dashboard",
                            nasId = LongIdInfo.IdParser(long.Parse(user.nasid)),
                            dataLimit = selectedPlan.data_limit,
                            planId = (int)selectedPlan.id,
                            planStartTime = DateTime.UtcNow,
                            paymentMode = payMode
                        };
                    }

                    long fdmId;
                    if (payViaUpi)
                    {
                        if (user.storegroupid == 1)
                        {
                            sUser.planStartTime = DateTime.UtcNow;
                            sUser.planEndTime = DateTime.UtcNow.AddMinutes(5);
                            sUser.otp = HOMEOTP.PAY_ONLINE;
                            sUser.dataLimit = 100;
                        }
                        else
                        {
                            hUser.planStartTime = DateTime.UtcNow;
                            hUser.planEndTime = DateTime.UtcNow.AddMinutes(5);
                            hUser.otp = HOMEOTP.PAY_ONLINE;
                            hUser.dataLimit = 100;
                        }

                        fdmId = user.storegroupid == 1 ? CoreDbCalls.GetInstance().GenerateUser(sUser, user.backEndNasid, selectedPlan.concurrent_devices, true, Convert.ToInt32(selectedPlan.price), selectedPlan.id) : CoreDbCalls.GetInstance().GenerateUser(hUser, user.backEndNasid, selectedPlan.concurrent_devices, true, Convert.ToInt32(selectedPlan.price), selectedPlan.id); ;
                        CoreDbCalls.GetInstance().ReloginUser(user, out var userSession, fdmId);

                        Dictionary<string, object> data;

                        data = LoginUtils.getRouterLoginUrlDict(user, LoginUtils.generateCHAP(user.challenge, user.otp), validator.GetLandingPage(user, "login"));
                        CookieUtils.SetCookie(HttpContext, "pay-via-upi", upiId, false, DateTime.UtcNow.AddMinutes(5), false);

                        return new JsonResult(new JsonResponse(ResponseStatus.SUCCESS, string.Empty, data));
                    }
                    else
                    {
                        if (user.storegroupid == 1)
                        {
                            sUser.planStartTime = DateTime.UtcNow;
                            sUser.planEndTime = DateTime.UtcNow;
                            sUser.timePlan = selectedPlan.time_limit;
                            sUser.otp = "CASH";
                            sUser.dataLimit = selectedPlan.data_limit;
                        }
                        else
                        {
                            hUser.planStartTime = DateTime.UtcNow;
                            hUser.planEndTime = DateTime.UtcNow;
                            hUser.timePlan = selectedPlan.time_limit;
                            hUser.otp = "CASH";
                            hUser.dataLimit = selectedPlan.data_limit;
                        }

                        fdmId = user.storegroupid == 1 ? CoreDbCalls.GetInstance().GenerateUser(sUser, user.backEndNasid, 1, false, Convert.ToInt32(selectedPlan.price)) : CoreDbCalls.GetInstance().GenerateUser(hUser, user.backEndNasid, 1, false, Convert.ToInt32(selectedPlan.price));
                        var fdmStr = fdmId.ToString();
                        return new JsonResult(new JsonResponse(ResponseStatus.SUCCESS, "notificatiopn sent to operator", new { fdmId = fdmStr }));
                    }
                }
                return new JsonResult(new JsonResponse(ResponseStatus.FAILURE, "wrong plan selected"));
            }
            return new JsonResult(new JsonResponse(ResponseStatus.FAILURE, "plans on this nas dones not exixts"));
        }
        catch (Exception ex)
        {
            Logger.GetInstance().Error($"Caught exception with message: {ex.Message} for mobile: {mobile}");
            return new JsonResult(new JsonResponse(ResponseStatus.FAILURE, $"Caught exception with message: {ex.Message}"));
        }
    }



}