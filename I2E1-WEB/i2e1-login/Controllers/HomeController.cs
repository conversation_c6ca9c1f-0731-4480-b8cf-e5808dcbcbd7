using i2e1_basics.Models;
using i2e1_basics.Utilities;
using i2e1_core.Attributes;
using i2e1_login.Models;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Net;
using System.Text;

namespace i2e1_login.Controllers;

public class HomeController : Controller
{
    public IActionResult Index()
    {
        return View();
    }

    public IActionResult Privacy()
    {
        return View();
    }

    [ResponseCache(Duration = 0, Location = ResponseCacheLocation.None, NoStore = true)]
    public IActionResult Error()
    {
        return View(new ErrorViewModel { RequestId = Activity.Current?.Id ?? HttpContext.TraceIdentifier });
    }

    [HttpPost]
    [CoreModelBinder]
    public JsonResult transliterate([FromBody] List<string> input)
    {
        var result = new List<string>();
        var input2dArray = input.Batch(10);
        try
        {
            foreach (var l in input2dArray)
            {
                WebClient client = new WebClient();
                client.Headers.Add("Content-Type", "application/json");
                client.Headers.Add("Ocp-Apim-Subscription-Key", "64b1801a62b049749b98357b8115460b");
                client.Headers.Add("X-ClientTraceId", "50953530-67a9-4ba1-bddf-7493ee95ccf0");
                byte[] res = client.UploadData("https://api.cognitive.microsofttranslator.com/transliterate?api-version=3.0&language=hi&fromScript=latn&toScript=Deva",
                Encoding.UTF8.GetBytes(JsonConvert.SerializeObject(l)));
                result.AddRange(JsonConvert.DeserializeObject<List<string>>(Encoding.UTF8.GetString(res)));
            }
        }
        catch (Exception ex)
        {
            Logger.GetInstance().Error(ex.Message);
            return new JsonResult(new JsonResponse(ResponseStatus.SUCCESS, input));
        }
        return new JsonResult(new JsonResponse(ResponseStatus.SUCCESS, result));
    }

}
