using i2e1_basics.Models;
using i2e1_basics.Utilities;
using i2e1_core.Attributes;
using i2e1_core.Models;
using i2e1_core.Utilities;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Dynamic;
using System.Reflection;
using wifidog_core.Models;
using wiom_login_share.Models;
using wiom_routerplan_share.ExposeApi;
using wiom_routerplan_share.Models.RouterPlan;

namespace i2e1_login.Controllers
{
	public class SecondaryPlanController : BaseController
    {
        [HttpGet]
        //[ValidateAntiForgeryToken]
        [AuthorizeAdmin(AdminUserType.INTERNAL)]
        [AuthenticateAccount(App.INTERNAL_SERVICES)]
        public JsonResponse GetLatestSecondaryPlanInfo(string mobile)
        {
            Logger.GetInstance().Info($"SecondaryPlanController:GetLatestSecondaryPlanInfo mobile {mobile}");
            try
            {
                if (string.IsNullOrEmpty(mobile) || string.IsNullOrWhiteSpace(mobile))
                {
                    return new JsonResponse(ResponseStatus.FAILURE, "Something Went Wrong");
                }
                List<SecondaryRouterPlan> secondaryRouterPlans = GenericApi.GetInstance().GetSecondarySignalPlansInfo(mobile).Result;
                Logger.GetInstance().Info($"GetLatestSecondaryPlanInfo : secondaryRouterPlans {JsonConvert.SerializeObject(secondaryRouterPlans)}");
                if (secondaryRouterPlans.Count > 0)
                {
                    DateTime planStartTime = secondaryRouterPlans[0].planStartTime;
                    DateTime planEndTime = secondaryRouterPlans[0].planEndTime;
                    TimeSpan timeSpan= planEndTime - planStartTime;
                    dynamic expendObject = new ExpandoObject();
                    expendObject.entryUnixEpochString = secondaryRouterPlans[0].entryUnixEpochTime.ToString();
                    LongIdInfo primaryNasid = new LongIdInfo(secondaryRouterPlans[0].nasId.shard_id, DBObjectType.ACTIVE_NAS, secondaryRouterPlans[0].nasId.local_value);
                    expendObject.deviceId = ShardHelper.GetNasDetailsFromLongNas(primaryNasid)?.deviceId;
                    PropertyInfo[] propertyInfos = secondaryRouterPlans[0].GetType().GetProperties();
                    foreach (var propertyInfo in propertyInfos)
                    {
                        ((IDictionary<string, object>)expendObject)[propertyInfo.Name] = propertyInfo.GetValue(secondaryRouterPlans[0]);
                    }
                    return new JsonResponse(ResponseStatus.SUCCESS, expendObject);
                }
                return new JsonResponse(ResponseStatus.SUCCESS, null);
            }
            catch (Exception ex) {
                return new JsonResponse(ResponseStatus.FAILURE, "Something Went Wrong");
            }
        }

        [HttpPost("extend")]
        [CoreModelBinder]
        [AuthorizeAdmin(AdminUserType.INTERNAL)]
        [AuthenticateAccount(App.INTERNAL_SERVICES)]
        public JsonResponse ExtendSecondaryRouterPlanBySeconds(string mobile, string entryUnixEpoch, int seconds)
        {
            Logger.GetInstance().Info($"SecondaryPlanController:ExtendSecondaryRouterPlan mobile: {mobile} entryUnixEpoch: {entryUnixEpoch} seconds: {seconds}");
            try
            {
                if (string.IsNullOrWhiteSpace(mobile)) {
                    return new JsonResponse(ResponseStatus.FAILURE, "Mobile number is required");
                }

                if (seconds <= 0) {
                    return new JsonResponse(ResponseStatus.FAILURE, "Extension time must be greater than zero");
                }

                long entryUnixEpoch1 = Convert.ToInt64(entryUnixEpoch);

                SecondaryRouterPlan secondaryRouterPlans =
                    GenericApi.GetInstance().GetSingleSecondarySignalPlanInfo(mobile, entryUnixEpoch1).Result;

                Logger.GetInstance().Info($"GetLatestSecondaryPlanInfo : secondaryRouterPlans {JsonConvert.SerializeObject(secondaryRouterPlans)}");

                if (secondaryRouterPlans != default(SecondaryRouterPlan))
                {
                    var planInfo = CoreDbCalls.GetInstance().GetPlanInfo(secondaryRouterPlans.planId);

                    if (planInfo.time_limit < seconds)
                    {
                        return new JsonResponse(ResponseStatus.FAILURE, "Extension time cannot exceed the plan duration");
                    }

                    // check if already extended
                    if (secondaryRouterPlans.planStartTime.AddSeconds(planInfo.time_limit) < secondaryRouterPlans.planEndTime)
                    {
                        return new JsonResponse(ResponseStatus.FAILURE, "Plan is already extended once");
                    }


                    // extend plan
                    secondaryRouterPlans.planEndTime = secondaryRouterPlans.planEndTime.AddSeconds(seconds);
                    _ = GenericApi.GetInstance().UpdateSecondaryPlan(secondaryRouterPlans).Result;

                    // refresh user session if authenticated
                    if (secondaryRouterPlans.authState)
                    {
                        CoreCacheHelper.GetInstance().UpgradeUserSession(
                            new WifiUser()
                            {
                                mobile = secondaryRouterPlans.mobile,
                                nasid = secondaryRouterPlans.nasId.ToString(),
                                storegroupid = 1
                            },
                            secondaryRouterPlans.entryUnixEpochTime,
                            0,
                            false
                        );
                    }

                    return new JsonResponse(ResponseStatus.SUCCESS, "Plan is updated");
                }

                return new JsonResponse(ResponseStatus.SUCCESS, null);
            }
            catch (Exception ex)
            {
                return new JsonResponse(ResponseStatus.FAILURE, "Something Went Wrong");
            }
        }


        [HttpPost]
        [CoreModelBinder]
        [AuthorizeAdmin(AdminUserType.INTERNAL)]
        [AuthenticateAccount(App.INTERNAL_SERVICES)]
        public JsonResponse ExtendSecondaryRouterPlan(string mobile, string entryUnixEpoch, int days)
        {
            Logger.GetInstance().Info($"SecondaryPlanController:ExtendSecondaryRouterPlan mobile: {mobile} entryUnixEpoch: {entryUnixEpoch} days: {days}");
            try
            {
                if (string.IsNullOrEmpty(mobile) || string.IsNullOrWhiteSpace(mobile) || days > 3)
                {
                    return new JsonResponse(ResponseStatus.FAILURE, "Something Went Wrong");
                }
                long entryUnixEpoch1 = Convert.ToInt64(entryUnixEpoch);

                SecondaryRouterPlan secondaryRouterPlans = GenericApi.GetInstance().GetSingleSecondarySignalPlanInfo(mobile, entryUnixEpoch1).Result;
                Logger.GetInstance().Info($"GetLatestSecondaryPlanInfo : secondaryRouterPlans {JsonConvert.SerializeObject(secondaryRouterPlans)}");
                if (secondaryRouterPlans != default(SecondaryRouterPlan))
                {
                    var planInfo = CoreDbCalls.GetInstance().GetPlanInfo(secondaryRouterPlans.planId);
                    if (secondaryRouterPlans.planStartTime.AddSeconds(planInfo.time_limit) < secondaryRouterPlans.planEndTime)
                    {
                        return new JsonResponse(ResponseStatus.FAILURE, "Plan is already extended once");
                    }
                    // if days is 0, means plan extend by plan time limit difference of plan expiry and plan start 
                    if(days == 0)
                    {
                        days = (int)((secondaryRouterPlans.planEndTime - secondaryRouterPlans.planStartTime).TotalDays);
                    }
                    secondaryRouterPlans.planEndTime = secondaryRouterPlans.planEndTime.AddDays(days);
                    _ = GenericApi.GetInstance().UpdateSecondaryPlan(secondaryRouterPlans).Result;

                    if (secondaryRouterPlans.authState)
                        CoreCacheHelper.GetInstance().UpgradeUserSession(new WifiUser() { mobile = secondaryRouterPlans.mobile, nasid = secondaryRouterPlans.nasId.ToString(), storegroupid = 1 }, secondaryRouterPlans.entryUnixEpochTime, 0, false);
                    return new JsonResponse(ResponseStatus.SUCCESS, "Plan is updated");
                }
                return new JsonResponse(ResponseStatus.SUCCESS, null);
            }
            catch (Exception ex)
            {
                return new JsonResponse(ResponseStatus.FAILURE, "Something Went Wrong");
            }
        }

        [CoreModelBinder]
        public JsonResponse GetDataUsageForSecondarySignalUser(string mobile, DateTime from, DateTime to)
        {
            Logger.GetInstance().Info($"SecondaryPlanController: GetDataUsageForSecondarySignalUser mobile: {mobile}, from: {from}, to: {to}");
            if (string.IsNullOrEmpty(mobile))
            {
                Logger.GetInstance().Error($"SecondaryPlanController: GetDataUsageForSecondarySignalUser: mobile is null");
                return new JsonResponse(ResponseStatus.FAILURE, "mobile is null or empty");
            }

            try
            {
                object data = RadAcctDbCalls.GetDataUsedForUser(mobile, from, to);
                return new JsonResponse(ResponseStatus.SUCCESS, data);
            }
            catch (Exception ex)
            {
                return new JsonResponse(ResponseStatus.FAILURE, $"Something Went Wrong with exception: {ex.Message}");
            }

        }
        [CoreModelBinder]
        [HttpPost]
        public JsonResponse InsertUserActivity(User user, long fdmId)
        {
            Logger.GetInstance().Info($"SecondaryPlanController: InsertUserActivity user: {JsonConvert.SerializeObject(user)}, fdmId: {fdmId}");

            try
            {
                CoreDbCalls.GetInstance().ReloginUser(user, out var userSession, fdmId);
                return new JsonResponse(ResponseStatus.SUCCESS, userSession);
            }
            catch (Exception ex)
            {
                return new JsonResponse(ResponseStatus.FAILURE, $"Something Went Wrong with exception: {ex.Message}");
            }

        }
    }
}
