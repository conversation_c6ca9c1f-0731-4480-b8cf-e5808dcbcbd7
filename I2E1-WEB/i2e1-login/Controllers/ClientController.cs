using System;
using System.Collections;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Net;
using System.Text;
using System.Threading.Tasks;
using Amazon.S3;
using Amazon.S3.Model;
using HandlebarsDotNet;
using i2e1_basics.Models;
using i2e1_basics.Utilities;
using i2e1_core.Attributes;
using i2e1_core.Models;
using i2e1_core.Models.Client;
using i2e1_core.services;
using i2e1_core.Utilities;
using i2e1_login.Utilities;
using I2E1_Message.DataSource;
using I2E1_Message.Models;
using I2E1_Message.Models.Client;
using I2E1_Message.Utils;
using I2E1_WEB.Database;
using I2E1_WEB.MiddleTier;
using I2E1_WEB.Models;
using I2E1_WEB.Models.Client;
using I2E1_WEB.Utilities;
using I2E1_WEB.Utils;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Caching.Memory;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using wifidog_core.Models;
using wiom_login_share.Utilities;
using wiom_router_api.Models;
using Notification = i2e1_basics.Models.Notification;

namespace I2E1_WEB.Controllers;

[CoreModelBinder]
public class ClientController : Controller

{
    private IMemoryCache memoryCache;
    public ClientController(IMemoryCache memoryCache)
    {
        this.memoryCache = memoryCache;
    }

    public dynamic DemoRequest( string name, string phoneNumber, string email, string msg)
    {
        string sms_content= "Thank you for reaching out to i2e1. You would shortly receive a call from our sales representative.%0a%0aTeam i2e1";
        
        if (phoneNumber != null)
        {
            CoreSmsSender.SendPromotionalSMSViaInfini(sms_content, phoneNumber, "BULKSMS");
        }
        if (email != null)
        {
            List<string> emails = new List<string>() { email };

            BasicEmailSender.SendEmail(new Notification() {
                emailId = new List<string>() { "<EMAIL>", },// "<EMAIL>" },
                message = "Contact Details\n\n"+"Name: "+name+"\nPhone Number: " + phoneNumber+"\nEmail Address: "+email + "\nMessage: " + msg,
                title = "Please get in touch ASAP (Demo Request)",
                source = "i2e1.com"
            },  "<EMAIL>","i2e1", null);

            var template_demo = Handlebars.Compile(new WebClient().DownloadString("https://s3.ap-south-1.amazonaws.com/i2e1-storage/email-templates/emailer_demo.html"));
            var parameters_demo = new Dictionary<string, string>();
            parameters_demo.Add("username", name);

            BasicEmailSender.SendEmail(new Notification()
            {
                emailId = emails,
                message = template_demo(parameters_demo),
                title = "Demo Request Confirmation",
                source = "i2e1.com",
                isHTML = true
            }, "<EMAIL>", "i2e1", null);
        }

        return new {
            msg = "success"
        };
    }

        [AuthorizeAdmin(AdminUserType.SUPER_ADMIN, AdminUserType.ADMIN, AdminUserType.READ_ONLY_ADMIN, AdminUserType.INTERNAL)]
        public ActionResult Home()
        {
            if(IsFeatureEnabled(Feature.ADMIN_PORTAL_ACCESS))
            {
                return View("~/Views/Client/home.cshtml");
            }
            ViewBag.ErrorMessage = "You dont have access to portal";
            return View("~/Views/Client/login.cshtml");
        }

    public ActionResult Index(string path)
    {
        ViewBag.ErrorMessage = null;
        ManagementUser user = JwtObject.GetManagementUser(HttpContext);
        if (user != null && user.token!=null)
        {
            return RedirectToAction("Home");
        }
        return View("~/Views/Client/login.cshtml");
    }

    public ActionResult Logout()
    {
        /*SessionUtils.setAdminInPower(null,HttpContext);
        SessionUtils.setAdminInView(null,HttpContext);*/
        JWTManager.expireJwtToken(HttpContext);
        return Redirect("/Client/Index#/login");
    }

    public ActionResult LoginWeb(string username, string password, string googleToken, string reply, AdminAuthType authType = AdminAuthType.CUSTOM)
    {
        ActionResult response;
        String doLoginResponse = doLogin(username, password, googleToken, authType);
        if (doLoginResponse == Constants.ADMIN_USER)
        {
            string urlReferrer = Request.Headers["Referer"];
            response = Redirect(string.IsNullOrEmpty(reply) ? "/Client/Home#/landing/home" : reply);
        }
        else
        {
            if (Enum.IsDefined(typeof(AdminAuthType), authType) && authType.ToString() == "LINQ_ADMIN")
                response = View("~/Views/LinqAdmin/login.cshtml");
            else
            {
                ViewBag.ErrorMessage = "Username or password is incorrect!";
                response = View("~/Views/Client/login.cshtml");
            }
        }

        return response;
    }

    public JsonResult LoginApp(string username, string password, string googleToken, AdminAuthType authType = AdminAuthType.CUSTOM)
    {
        JsonResult response;
        String doLoginResponse = doLogin(username, password, googleToken, authType);

        if (doLoginResponse == Constants.ADMIN_USER)
        {
            var admin = JwtObject.GetManagementUser(HttpContext);
            if (onlyAnalyticsUser(admin.features))
            {
                response= new JsonResult(new JsonResponse(ResponseStatus.SUCCESS, "ANALYTICS USER", admin.token));
            }
            else response = new JsonResult(new JsonResponse(ResponseStatus.SUCCESS, "ADMIN USER", admin.token));
        }
        else if (doLoginResponse == Constants.FRESH_ADMIN)
        {
            var admin = JwtObject.GetManagementUser(HttpContext);

            response = new JsonResult(new JsonResponse(ResponseStatus.SUCCESS, "FRESH USER", admin.token));
        }
        else
        {
            response = new JsonResult(new JsonResponse(ResponseStatus.FAILURE, "User not found", null));
        }

        return response;
    }

    [AuthorizeAdmin(AdminUserType.SUPER_ADMIN, AdminUserType.ADMIN, AdminUserType.READ_ONLY_ADMIN)]
    public JsonResult GetNasUser(LongIdInfo nasid)
    {
        return new JsonResult(new JsonResponse(ResponseStatus.SUCCESS, "", AdminDatabaseRequest.getAdminUserFromNas(nasid)));
    }

    private bool onlyAnalyticsUser(Dictionary<string, int> features)
    {
        int value;
        if (features != null && features.Count(x => x.Value == 1) == 1
            && features.TryGetValue(Feature.ADVANCE_ANALYTICS.GetHashCode().ToString(), out value)
            && value == 1)
        {
            return true;
        }
        return false;
    }

    private bool isLinqAdminUser(string username, Dictionary<string, int> features)
    {
        int value;
        if (features != null && features.TryGetValue(Feature.LINQ_DASHBOARD.GetHashCode().ToString(), out value)
            && value == 1 && username.EndsWith("@i2e1.com"))
        {
            return true;
        }
        return false;
    }

    private String doLogin(string username, string password, string googleToken, AdminAuthType authType = AdminAuthType.CUSTOM)
    {
        ManagementUser user = CoreUserService.CheckAdmin(username, password, googleToken, authType);

        string response;
        if (user != null)
        {
            user.features = AdminDatabaseRequest.GetFeatureList(user.userid, user.userType);
            JWTManager.CreateJwtToken(null, null, user, HttpContext);

            response = Constants.ADMIN_USER;
        }
        else
        {
            // Delete Token
            JWTManager.expireJwtToken(HttpContext);
            response = Constants.INVALID_LOGIN;
        }
        return response;
    }

    internal static string GetStringSha256Hash(string text)
    {
        if (String.IsNullOrEmpty(text))
            return String.Empty;

        using (var sha = new System.Security.Cryptography.SHA256Managed())
        {
            byte[] textData = System.Text.Encoding.UTF8.GetBytes(text);
            byte[] hash = sha.ComputeHash(textData);
            return BitConverter.ToString(hash).Replace("-", String.Empty);
        }
    }

    public JsonResult ForgetPassword(string emailId)
    {
        
        ManagementUser user = ManagementUserImpl.GetAdminUser(emailId);
        if (user != null)
        {
            DateTime myDateTime = DateTime.UtcNow;
            string sqlFormattedDate = myDateTime.ToString("yyyy-MM-dd HH:mm:ss.fff");
            var saltId = GetStringSha256Hash(emailId + sqlFormattedDate);
            var update = ClientDatabaseRequest.updateSaltId(user.userid, saltId, sqlFormattedDate);
            if (update)
            {
                List<string>  emailIds = new List<string>() { emailId };
                string title = "Action Required! - I2E1.com";
                var message = "Hello user <br><br>For your password reset please click "
                + "https://" + Request.Host.Host + "/Client/Index#/resetPasswordLink?token=" + saltId
                + "<br><br>Thanks<br>I2E1";

                var success = BasicEmailSender.SendRawEmail("<EMAIL>", emailIds, title , message, "i2e1", null);
                if (success != null)
                {
                    WebUtils.LogInfoToCosmos("Forget password email sent" + emailId);
                }
            }
            return new JsonResult(new JsonResponse(ResponseStatus.SUCCESS, "An Email has been sent to your registered email id,please follow the instruction to reset your password!"));
        }
        else
        {
            return new JsonResult(new JsonResponse(ResponseStatus.FAILURE, "Email is not registered with us!"));
        }
    }
    // check once
    public JsonResult ResetPasswordLink(long shardId,string token)
    {
        
        ManagementUser user = ClientDatabaseRequest.getUserForPasswordReset(shardId,token);
        if (user != null)
        {
            DateTime myDateTime = DateTime.UtcNow;
            TimeSpan span = (myDateTime - user.tokengenratedtime);
            Dictionary<string, string> userdetails = new Dictionary<string, string>();
            userdetails.Add("username", user.name);
            userdetails.Add("userid", user.userid.ToString());
            userdetails.Add("token", user.token);
            return new JsonResult(new JsonResponse(ResponseStatus.SUCCESS, userdetails));
            
        }
        else
        {
            return new JsonResult(new JsonResponse(ResponseStatus.FAILURE, "You are not a valid user or your link may have expire, please try again!"));
            
        }
    }
   // check once
    [HttpPost]
    public JsonResult resetYourPassword(string token, string password, string userid)
    {
        LongIdInfo longId = LongIdInfo.IdParser(long.Parse(userid));
        ManagementUser user = ClientDatabaseRequest.getUserForPasswordReset(longId.shard_id,token);
        if (user != null)
        {
            string sqlFormattedDate = DateTime.UtcNow.ToString("yyyy-MM-dd HH:mm:ss.fff");
            var saltId = GetStringSha256Hash(user.name + sqlFormattedDate);
            var update = ClientDatabaseRequest.updateSaltId(longId, saltId, sqlFormattedDate);
            if (update)
            {
                var passwordUpdate = ClientDatabaseRequest.resetUserPassword(longId, password);
                return new JsonResult(new JsonResponse(ResponseStatus.SUCCESS, "Your password is reset successfully"));
                
            }
            else
            {
                return new JsonResult(new JsonResponse(ResponseStatus.SUCCESS, "Something went wrong please contact our customer support"));
                
            }
        }
        else
        {
            return new JsonResult(new JsonResponse(ResponseStatus.FAILURE, "You are not a valid user or your link may have expire, please try again!"));
            
        }
    }

        [AuthorizeAdmin(AdminUserType.ADMIN, AdminUserType.INTERNAL)]
        public JsonResult SubmitReportSubscription(int queryType, string subject, string templatePath, string templateFillerQuery, int executionTime, DateTime startDate, string reportName, string sheetName, string query, string receivers, int cycle, int triggerId = 0)
        {
            startDate = new DateTime(startDate.Year, startDate.Month, startDate.Day);
            startDate = startDate.AddMinutes(executionTime);
            startDate = Util.ConvertISTToUtc(startDate);
            ClientDatabaseRequest.SaveReportSubscription("CUSTOM_REPORT_TRIGGER-", triggerId, startDate, cycle,
                reportName, "i2e1Notification.Utilities.CustomReportSender", "<EMAIL>,<EMAIL>",
                new string[] { query, receivers, string.Empty, sheetName, templatePath, templateFillerQuery, subject, queryType.ToString() });
            return new JsonResult(new JsonResponse(ResponseStatus.SUCCESS, "Subscription submitted successfully. Please contact our team for activation"));
            
        }

        [AuthorizeAdmin(AdminUserType.ADMIN, AdminUserType.INTERNAL)]
        public JsonResult GetReportSubscription()
        {
            var list = DbCalls.GetInstance().GetTriggerDetails("CUSTOM_REPORT_TRIGGER-");
            list.ForEach(m =>
            {
                m.triggerName = m.triggerName.Replace("CUSTOM_REPORT_TRIGGER-", string.Empty);
                m.scheduleCycle.startTime = Util.ConvertUtcToIST(m.scheduleCycle.startTime);
            });
            return new JsonResult(new JsonResponse(ResponseStatus.SUCCESS, list));
            
        }

    [AuthorizeAdmin(AdminUserType.ADMIN)]
    public JsonResult GetAllCouponTrigger()
    {
        var list = DbCalls.GetInstance().GetTriggerDetails("CUSTOM_COUPON_TRIGGER-");
        list.ForEach(m =>
        {
            m.triggerName = m.triggerName.Replace("CUSTOM_COUPON_TRIGGER-", string.Empty);
            m.scheduleCycle.startTime = Util.ConvertUtcToIST(m.scheduleCycle.startTime);
        });
        return new JsonResult(new JsonResponse(ResponseStatus.SUCCESS, list));

    }


    [AuthorizeAdmin(AdminUserType.ADMIN)]
    public JsonResult SubmitCouponTrigger(string bluePrintId, int executionTime, DateTime startDate, string reportName, string query, string receivers, int cycle, int triggerId = 0)
    {
        startDate = new DateTime(startDate.Year, startDate.Month, startDate.Day);
        startDate = startDate.AddMinutes(executionTime);
        startDate = Util.ConvertISTToUtc(startDate);
        ClientDatabaseRequest.SaveReportSubscription("CUSTOM_COUPON_TRIGGER-", triggerId, startDate, cycle,
            reportName, "i2e1Notification.Utilities.CouponNotification", "<EMAIL>,<EMAIL>",
            new string[] { query, bluePrintId, receivers});
        return new JsonResult(new JsonResponse(ResponseStatus.SUCCESS, "Coupon submitted successfully"));

    }

        [AuthorizeAdmin(AdminUserType.ADMIN, AdminUserType.INTERNAL)]
        public JsonResult SubmitSMSReportSubscription(int queryType, int executionTime, DateTime startDate, string reportName, string smsText, string query, string receivers, string senderId, int cycle, int triggerId = 0, int restingDays = 0, int sendVia = 0, string header = null, string footer = null)
        {
            startDate = new DateTime(startDate.Year, startDate.Month, startDate.Day);
            startDate = startDate.AddMinutes(executionTime);
            startDate = Util.ConvertISTToUtc(startDate);
            ClientDatabaseRequest.SaveReportSubscription("CUSTOM_SMS_REPORT_TRIGGER-", triggerId, startDate, cycle,
                reportName, query.StartsWith("https://") ? "i2e1Notification.Utilities.CustomXlxsMarketCampaign" : "i2e1Notification.Utilities.CustomSMSReportSender", "<EMAIL>,<EMAIL>",
                new string[] { query, receivers, string.Empty, smsText, queryType.ToString(), senderId, restingDays.ToString(), sendVia.ToString(), header, footer });
            return new JsonResult(new JsonResponse(ResponseStatus.SUCCESS, "Subscription submitted successfully. Please contact our team for activation"));
            
        }

        [AuthorizeAdmin(AdminUserType.ADMIN, AdminUserType.INTERNAL)]
        public JsonResult GetSMSReportSubscription()
        {
            var list = DbCalls.GetInstance().GetTriggerDetails("CUSTOM_SMS_REPORT_TRIGGER-");
            list.ForEach(m =>
            {
                m.triggerName = m.triggerName.Replace("CUSTOM_SMS_REPORT_TRIGGER-", string.Empty);
                m.scheduleCycle.startTime = Util.ConvertUtcToIST(m.scheduleCycle.startTime);
            });
            return new JsonResult(new JsonResponse(ResponseStatus.SUCCESS, list));
            
        }

    [AuthorizeAdmin(AdminUserType.ADMIN)]
    public JsonResult RunTrigger(int triggerId)
    {
        DbCalls.GetInstance().ResetTriggerLastRanTime(triggerId);
        return new JsonResult(new JsonResponse(ResponseStatus.SUCCESS, "Execution Request submitted Successfully"));
        
    }

    [AuthorizeClient]
    public JsonResult GetBandwidthReport(LongIdInfo nasid, DateTime startTime, DateTime endTime)
    {
        TimeUtils.normalizeTime(ref startTime, ref endTime);
        
        

        var list = AdminDatabaseRequest.GetBandwidthReport(nasid, startTime, endTime);
        return new JsonResult(new JsonResponse(ResponseStatus.SUCCESS, list));
        
    }

    [AuthorizeClient]
    public async Task<JsonResult> UploadFile(IFormFile file)
    {
        string bucketName = "i2e1-storage";
        string folder = Request.Form["uploadFolder"];
        
        //Hashtable result = new Hashtable();
        
        IAmazonS3 client;
        client = new AmazonS3Client(I2e1ConfigurationManager.AWS_ACCESS_KEY, I2e1ConfigurationManager.AWS_SECRET_KEY, I2e1ConfigurationManager.GetAWSDBRegion());
        string nameAppender = Request.Form["nameAppender"];
        string fileName = System.Text.RegularExpressions.Regex.Replace(nameAppender, @"\s+", "-").Trim().ToLower() + "/" + Guid.NewGuid() + file.FileName.Substring(file.FileName.LastIndexOf('.'));
        
        try
        {
            string filePathName = System.IO.Path.GetFileName(file.FileName);
            PutObjectRequest putRequest = new PutObjectRequest
            {
                BucketName = bucketName,
                Key = folder + fileName,
                InputStream = file.OpenReadStream(),
                CannedACL = S3CannedACL.PublicRead
            };

            PutObjectResponse response = await client.PutObjectAsync(putRequest);
            if (response.HttpStatusCode == HttpStatusCode.OK)
            {
                fileName = "https://s3.ap-south-1.amazonaws.com/" + bucketName + "/" + folder + fileName;
                return new JsonResult(new JsonResponse(ResponseStatus.SUCCESS, fileName, fileName));
            }
            else
            {
                //result.Add("status", "failed");
                //result.Add("message", "Somthing went wronng please try again");
                return new JsonResult(new JsonResponse(ResponseStatus.FAILURE, "Somthing went wronng please try again"));
            }
            
        }

        catch (AmazonS3Exception amazonS3Exception)
        {
            if (amazonS3Exception.ErrorCode != null &&
                (amazonS3Exception.ErrorCode.Equals("InvalidAccessKeyId")
                ||
                amazonS3Exception.ErrorCode.Equals("InvalidSecurity")))
            {
                //result.Add("status", "failed");
                //result.Add("message", "Check the provided AWS Credentials.");
                return new JsonResult(new JsonResponse(ResponseStatus.FAILURE, "Check the provided AWS Credentials."));
            }
            else
            {
                //result.Add("status", "failed");
                //result.Add("message", amazonS3Exception.Message);
                return new JsonResult(new JsonResponse(ResponseStatus.FAILURE, amazonS3Exception.Message));
            }
        }
        
    }

    [AuthorizeClient]
    public async Task<JsonResult> UploadClientFile(IFormFile file)
    {
        
        string relativePath = null;
        string fileUse = Request.Form["fileUse"];
        string clientId = Request.Form["clientId"];
        string partnerId = Request.Form["partnerId"];
        string fileName = "f_" + DateTime.UtcNow.Ticks + "_" + file.FileName.Replace(' ', '_');
        JwtObject jwtObject = JwtObject.GetJWTObject(HttpContext);
        if (string.IsNullOrEmpty(clientId) && clientId == "undefined")
            clientId = jwtObject.clientId.ToString();
        
        switch (fileUse)
        {
            case "template":
                if (string.IsNullOrEmpty(partnerId) && partnerId == "undefined")
                {
                    partnerId = jwtObject.partnerId.ToString();
                }
                    
                if (string.IsNullOrEmpty(clientId) || string.IsNullOrEmpty(partnerId))
                {
                    WebUtils.LogErrorToCosmos("Please select a client and partner before uploading");
                    return new JsonResult(new JsonResponse(ResponseStatus.FAILURE, "Please select a client and partner before uploading"));
                    
                }
                relativePath = "client_" + clientId + "/partner_" + partnerId + "/template/" + fileName;
                break;
            case "campaign":
                if (string.IsNullOrEmpty(clientId))
                {
                    WebUtils.LogErrorToCosmos("Please select a client before uploading");
                    return new JsonResult(new JsonResponse(ResponseStatus.FAILURE, "Please select a client before uploading"));
                    
                }
                relativePath = "client_" + jwtObject.clientId.ToString() +
                    "/campaign/" + fileName;
                break;
            case "partner-logo":
                if (string.IsNullOrEmpty(clientId) || string.IsNullOrEmpty(partnerId))
                {
                    WebUtils.LogErrorToCosmos("Please select a client and partner before uploading");
                    return new JsonResult(new JsonResponse(ResponseStatus.FAILURE, "Please select a client and partner before uploading"));
                    
                }

                relativePath = "client_" + jwtObject.clientId.ToString() +
                    "/partner_" + jwtObject.partnerId.ToString() +
                    "/logo/" + fileName;
                break;
            default:
                return new JsonResult(new JsonResponse(ResponseStatus.FAILURE, "fileUse parameter cannot be null."));
                
        }
        try
        {
            var pathToFile = await S3Utils.UploadFile(file, relativePath,
                I2e1ConfigurationManager.GetInstance().GetSetting("client_data_folder").ToString());
            {
                var typeOfFile = "image";
                if (file.ContentType.ToLower() != "image/jpg" &&
                    file.ContentType.ToLower() != "image/jpeg" &&
                    file.ContentType.ToLower() != "image/pjpeg" &&
                    file.ContentType.ToLower() != "image/gif" &&
                    file.ContentType.ToLower() != "image/x-png" &&
                    file.ContentType.ToLower() != "image/png")
                {
                    typeOfFile = "video";
                }
                return new JsonResult(new JsonResponse(ResponseStatus.SUCCESS, "File uploaded", new {
                    pathToFile = pathToFile,
                    typeOfFile = typeOfFile
                }));
            }
        }
        catch (Exception ex)
        {
            WebUtils.LogErrorToCosmos("Unable to upload file " + ex.Message);
            return new JsonResult(new JsonResponse(ResponseStatus.FAILURE, "Unable to upload file. Please try after some time"));
        }
        
    }

    [AuthorizeAdmin(AdminUserType.SUPER_ADMIN, AdminUserType.ADMIN)]
    public JsonResult getAllClients(LongIdInfo userId)
    {
        return new JsonResult(new JsonResponse(ResponseStatus.SUCCESS, "", ClientDatabaseRequest.getAllClients(userId)));
    }

    [AuthorizeAdmin(AdminUserType.SUPER_ADMIN, AdminUserType.ADMIN)]
    public JsonResult getAllPartners(LongIdInfo userId = null)
    {
        //ManagementUser adminInPower = JwtObject.GetManagementUser(HttpContext);
        JwtObject jwtObject = (JwtObject)HttpContext.Items[Constants.JWT_OBJECT];

        return new JsonResult(new JsonResponse(ResponseStatus.SUCCESS, "", ClientDatabaseRequest.getAllPartners(jwtObject.adminId)));
        
    }

    [AuthorizeAdmin(AdminUserType.SUPER_ADMIN, AdminUserType.ADMIN)]
    public JsonResult getPartnersDetails(string partnerName)
    {
        //ManagementUser adminInPower = JwtObject.GetManagementUser(HttpContext);
        
        
        return new JsonResult(new JsonResponse(ResponseStatus.SUCCESS, "", ClientDatabaseRequest.getPartnersDetails(partnerName)));
        
    }

    [AuthorizeAdmin(AdminUserType.SUPER_ADMIN, AdminUserType.ADMIN)]
    public JsonResult updateClientDetails(I2E1Client client)
    {
        
        var res = ClientDatabaseRequest.updateClientDetails(client);
        WebUtils.LogInfoToCosmos("Client updated or created", res);
        return new JsonResult(new JsonResponse(ResponseStatus.SUCCESS, "", res));
        
    }

    [AuthorizeAdmin(AdminUserType.SUPER_ADMIN, AdminUserType.ADMIN)]
    public JsonResult checkIfContactExists(string username)
    {
        return new JsonResult(new JsonResponse(ResponseStatus.SUCCESS, "", ClientDatabaseRequest.checkIfContactExists(username)));
        
    }

    [AuthorizeAdmin(AdminUserType.SUPER_ADMIN, AdminUserType.ADMIN)]
    public JsonResult checkIfUsernameExist(string username)
    {
        //ManagementUser adminInPower = JwtObject.GetManagementUser(HttpContext);
        
        //
        return new JsonResult(new JsonResponse(ResponseStatus.SUCCESS, "", ClientDatabaseRequest.checkIfContactExists(username)));
        
    }


    [AuthorizeAdmin(AdminUserType.SUPER_ADMIN, AdminUserType.ADMIN)]
    public JsonResult getClientContactPersons(int clientId)
    {
        ManagementUser adminInPower = JwtObject.GetManagementUser(HttpContext);
        
        //
        return new JsonResult(new JsonResponse(ResponseStatus.SUCCESS, "", ClientDatabaseRequest.getClientContactPersons(clientId)));
        
    }


    //TODO: JWT_GO_LIVE_CHECK
    [AuthorizeAdmin(AdminUserType.SUPER_ADMIN, AdminUserType.ADMIN)]
    public JsonResult updateClientContactPersons(StoreUser user, int clientId, bool emailCheck, bool emailChanged)
    {
        var storeUser = ClientDatabaseRequest.UpdateContactPersons(user, clientId, "client", emailChanged ? user.username : string.Empty);
        string saltId = null;
        if (storeUser != null)
        {
            DateTime myDateTime = DateTime.UtcNow;
            string sqlFormattedDate = myDateTime.ToString("yyyy-MM-dd HH:mm:ss.fff");
            saltId = GetStringSha256Hash(user.username + sqlFormattedDate);
            var update = ClientDatabaseRequest.updateSaltId(user.userId, saltId, sqlFormattedDate);
            if (update)
            {
                WebUtils.LogInfoToCosmos("User added and salt updated for user: "+ user.username);
               

            }

        }
        if (emailCheck && !string.IsNullOrEmpty(saltId))
        {
            List<string> emailIds = new List<string>();
            emailIds.Add(user.username);
            var template = Handlebars.Compile(new WebClient().DownloadString("https://s3.ap-south-1.amazonaws.com/i2e1-storage/email-templates/emailer_welcome.html"));
            var parameters = new Dictionary<string, string>();
            parameters.Add("username", user.name);
            parameters.Add("saltId", saltId);
            var success= BasicEmailSender.SendRawEmail("<EMAIL>",
                emailIds,
                "Welcome to i2e1 family", template(parameters), "i2e1", null);
            if (success !=null)
            {
                WebUtils.LogInfoToCosmos("Welcome email sent to user: " + user.username);
                user.isGreeted = 1;                   
            }                
        }
        //ManagementUser adminInPower = JwtObject.GetManagementUser(HttpContext);
        return new JsonResult(new JsonResponse(ResponseStatus.SUCCESS, storeUser));
        
        
    }


    [AuthorizeAdmin(AdminUserType.SUPER_ADMIN, AdminUserType.ADMIN)]
    public JsonResult updateClientContactPersonsFeatures(LongIdInfo userId, int userType, int clientId)
    {
        string productType = ClientDatabaseRequest.getClientsLargestProductType(clientId);
        
        if (IsFeatureEnabled(Feature.USER_MANAGEMENT) && productType != String.Empty)
        {
            bool res = ClientDatabaseRequest.UpdateTAdmin(userId, productType, userType);
            if (res && !string.IsNullOrEmpty(productType))
            {
                ManagementUserImpl.UpdateFeatureList(userId, productType);
            }
            return new JsonResult(new JsonResponse(ResponseStatus.SUCCESS, null, "successfully update feature list"));
            
        }
        return new JsonResult(new JsonResponse(ResponseStatus.BLOCKED, "Sorry you dont have sufficient permissions"));
        
    }

    [AuthorizeAdmin(AdminUserType.SUPER_ADMIN, AdminUserType.ADMIN)]
    public JsonResult getClientPartners(I2E1Client client)
    {
        return new JsonResult(new JsonResponse(ResponseStatus.SUCCESS, "", ClientDatabaseRequest.getClientPartners(client)));
        
    }

    [AuthorizeAdmin(AdminUserType.SUPER_ADMIN, AdminUserType.ADMIN)]
    public JsonResult updateClientsPartnerDetails(I2E1Partner partner)
    {
        //ManagementUser adminInPower = JwtObject.GetManagementUser(HttpContext);
        JwtObject jwtObject = JwtObject.GetJWTObject(HttpContext);
        var data = ClientDatabaseRequest.updateClientsPartnerDetails(partner);
        Logger.GetInstance().Info("A client's partner deatail is updated by admin in power : " + jwtObject.adminName + " and admin in view is : " + jwtObject.adminName);
        return new JsonResult(data);
        
    }

    [AuthorizeAdmin(AdminUserType.SUPER_ADMIN, AdminUserType.ADMIN)]
    public JsonResult getPartnerContactPersons(LongIdInfo partnerId)
    {
        return new JsonResult(new JsonResponse(ResponseStatus.SUCCESS, "", ClientDatabaseRequest.getPartnerContactPersons(partnerId)));
        
    }

    //TODO: JWT_GO_LIVE_CHECK
    [AuthorizeAdmin(AdminUserType.SUPER_ADMIN, AdminUserType.ADMIN)]
    public JsonResult updatePartnerContactPersons(StoreUser user, int partnerId, bool emailCheck, bool emailChanged)
    {
        var storeUser = ClientDatabaseRequest.UpdateContactPersons(user, partnerId, "partner", emailChanged ? user.username : string.Empty);
        string saltId = null;
        if (storeUser != null)
        {
            DateTime myDateTime = DateTime.UtcNow;
            string sqlFormattedDate = myDateTime.ToString("yyyy-MM-dd HH:mm:ss.fff");
            saltId = GetStringSha256Hash(user.username + sqlFormattedDate);
            var update = ClientDatabaseRequest.updateSaltId(user.userId, saltId, sqlFormattedDate);
            if (update)
            {
                WebUtils.LogInfoToCosmos("User added and salt updated for user: " + user.username);


            }

        }
        if (emailCheck && !string.IsNullOrEmpty(saltId))
        {
            List<string> emailIds = new List<string>();
            emailIds.Add(user.username);
            var template = Handlebars.Compile(new WebClient().DownloadString("https://s3.ap-south-1.amazonaws.com/i2e1-storage/email-templates/emailer_welcome.html"));
            var parameters = new Dictionary<string, string>();
            parameters.Add("username", user.name);
            parameters.Add("saltId", saltId);
            var success = BasicEmailSender.SendRawEmail("<EMAIL>",
                emailIds,
                "Welcome to i2e1 family", template(parameters), "i2e1", null);
            if (success != null)
            {
                WebUtils.LogInfoToCosmos("Welcome email sent to user: " + user.username);
                user.isGreeted = 1;
            }
        }
        ///ManagementUser adminInPower = JwtObject.GetManagementUser(HttpContext);
        return new JsonResult(new JsonResponse(ResponseStatus.SUCCESS, storeUser));
        

    }
    

    [AuthorizeAdmin(AdminUserType.SUPER_ADMIN, AdminUserType.ADMIN, AdminUserType.STANDARD)]
    public JsonResult getStoreContactPersons(LongIdInfo nasid, string classification=null)
    {            
        //
        return new JsonResult(new JsonResponse(ResponseStatus.SUCCESS, "", ClientDatabaseRequest.getStoreContactPersons(nasid, classification)));
        
    }


    [AuthorizeAdmin(AdminUserType.SUPER_ADMIN, AdminUserType.ADMIN, AdminUserType.STANDARD)]
    public JsonResult updateStoreContactPersons(StoreUser user, LongIdInfo nasid, bool emailCheck, bool emailChanged, string classification = "")
    {
        
        user.userType = AdminUserType.STANDARD;
        var storeUser = UpdateContactPersons(user, nasid, emailChanged,classification);
        string saltId = UpdateSaltForUser(storeUser, user);
        SendEmailToUser(emailCheck, saltId, user);
        //ManagementUser adminInPower = JwtObject.GetManagementUser(HttpContext);
        return new JsonResult(new JsonResponse(ResponseStatus.SUCCESS, storeUser));
    }
    
    // check once
    public static StoreUser UpdateContactPersons(StoreUser user, LongIdInfo nasid, bool emailChanged, string classification = "")
    {
        var storeUser = ClientDatabaseRequest.UpdateContactPersons(user, nasid, "location", emailChanged ? user.username : string.Empty, classification);
        return storeUser;
    }

    public static string UpdateSaltForUser(StoreUser storeUser, StoreUser user)
    {
        string saltId = null;
        if (storeUser != null)
        {
            DateTime myDateTime = DateTime.UtcNow;
            string sqlFormattedDate = myDateTime.ToString("yyyy-MM-dd HH:mm:ss.fff");
            saltId = GetStringSha256Hash(user.username + sqlFormattedDate);
            var update = ClientDatabaseRequest.updateSaltId(user.userId, saltId, sqlFormattedDate);
            if (update)
            {
                WebUtils.LogInfoToCosmos("User added and salt updated for user: " + user.username);
            }

        }
        return saltId;
    }

    public static void SendEmailToUser(bool emailCheck,string saltId, StoreUser user)
    {
        if (emailCheck && !string.IsNullOrEmpty(saltId))
        {
            List<string> emailIds = new List<string>();
            emailIds.Add(user.username);
            var template = Handlebars.Compile(new WebClient().DownloadString("https://s3.ap-south-1.amazonaws.com/i2e1-storage/email-templates/emailer_welcome.html"));
            var parameters = new Dictionary<string, string>();
            parameters.Add("username", user.name);
            parameters.Add("saltId", saltId);
            var success = BasicEmailSender.SendRawEmail("<EMAIL>",
                emailIds,
                "Welcome to i2e1 family", template(parameters), "i2e1", null);
            if (success != null)
            {
                WebUtils.LogInfoToCosmos("Welcome email sent to user: " + user.username);
                user.isGreeted = 1;
            }
        }
    }


    [AuthorizeAdmin(AdminUserType.SUPER_ADMIN, AdminUserType.ADMIN)]
    public JsonResult removeContact(StoreUser user, int mappingId, string type)
    {            
        //
        return new JsonResult(new JsonResponse(ResponseStatus.SUCCESS, "", ClientDatabaseRequest.removeContact(user, mappingId, type)));
        

    }

    [AuthorizeAdmin(AdminUserType.SUPER_ADMIN, AdminUserType.ADMIN)]
    public JsonResult GetAllUsers()
    {
        
        
        if (IsFeatureEnabled(Feature.TOGGLING_FEATURE) || IsFeatureEnabled(Feature.USER_MANAGEMENT))
        {
            return new JsonResult(new JsonResponse(ResponseStatus.SUCCESS, null, ClientDatabaseRequest.GetAllUsers()));
            
        }
        return new JsonResult(new JsonResponse(ResponseStatus.BLOCKED, "Sorry you dont have sufficient permissions"));
        
    }

    [AuthorizeAdmin(AdminUserType.SUPER_ADMIN, AdminUserType.ADMIN)]
    public JsonResult GetMyUsers()
    {
        // var user = JwtObject.GetManagementUser(HttpContext);
        JwtObject jwtObject = JwtObject.GetJWTObject(HttpContext);

        if (IsFeatureEnabled(Feature.USER_MANAGEMENT))
        {
            return new JsonResult(new JsonResponse(ResponseStatus.SUCCESS, null, ClientDatabaseRequest.GetMyUsers(jwtObject.adminId, jwtObject.userType)));
            
        }
        return new JsonResult(new JsonResponse(ResponseStatus.BLOCKED, "Sorry you dont have sufficient permissions"));
        
    }

    [AuthorizeAdmin(AdminUserType.SUPER_ADMIN, AdminUserType.ADMIN)]
    public JsonResult getUserLocations(LongIdInfo userId)
    {
        
        
        if (IsFeatureEnabled(Feature.USER_MANAGEMENT))
        {
            StoreSearchQuery query = new StoreSearchQuery();
            ManagementUser user = new ManagementUser();
            user.userid = userId;
            return new JsonResult(new JsonResponse(ResponseStatus.SUCCESS, "", ClientDatabaseRequest.getUsersMappedLocations(user)));
            
        }
        return new JsonResult(new JsonResponse(ResponseStatus.BLOCKED, "Sorry you dont have sufficient permissions"));
        
    }

    [AuthorizeAdmin(AdminUserType.SUPER_ADMIN, AdminUserType.ADMIN)]
    public ActionResult addMappingForUser(string username, string mappedId, string type )
    {

        LongIdInfo longId = LongIdInfo.IdParser(long.Parse(mappedId));

        if (IsFeatureEnabled(Feature.USER_MANAGEMENT))
        {
            return new JsonResult(new JsonResponse(ResponseStatus.SUCCESS, null, ClientDatabaseRequest.addMappingForUser(username, longId, type)));
            
        }
        return new JsonResult(new JsonResponse(ResponseStatus.BLOCKED, "Sorry you dont have sufficient permissions"));
        
    }

    [AuthorizeAdmin(AdminUserType.SUPER_ADMIN, AdminUserType.ADMIN)]
    public ActionResult removeMappingForUser(string username, string mappedId, string type)
    {
        
        
        if (IsFeatureEnabled(Feature.USER_MANAGEMENT))
        {
            return new JsonResult(new JsonResponse(ResponseStatus.SUCCESS, null, ClientDatabaseRequest.removeMappingForUser(username, int.Parse(mappedId), type)));
            
        }
        return new JsonResult(new JsonResponse(ResponseStatus.BLOCKED, "Sorry you dont have sufficient permissions"));
        
    }

    //[AuthorizeAdmin(AdminUserType.SUPER_ADMIN, AdminUserType.ADMIN, AdminUserType.DISTRIBUTOR)]
    //public ActionResult AssignNasToUser(string username, string nasid)
    //{
    //    
    //    
    //    if (IsFeatureEnabled(Feature.USER_MANAGEMENT))
    //    {
    //        return new JsonResult(new JsonResponse(ResponseStatus.SUCCESS, null, ClientDatabaseRequest.AssignNasToUser(username, int.Parse(nasid)));
    //        
    //    }
    //    return new JsonResult(new JsonResponse(ResponseStatus.BLOCKED, "Sorry you dont have sufficient permissions");
    //    
    //}

    //[AuthorizeAdmin(AdminUserType.SUPER_ADMIN, AdminUserType.ADMIN, AdminUserType.DISTRIBUTOR)]
    //public ActionResult RemoveNasFromUser(string username, string nasid)
    //{
    //    
    //    
    //    if (IsFeatureEnabled(Feature.USER_MANAGEMENT))
    //    {
    //        return new JsonResult(new JsonResponse(ResponseStatus.SUCCESS, null, ClientDatabaseRequest.RemoveNasFromUser(username, int.Parse(nasid)));
    //        
    //    }
    //    return new JsonResult(new JsonResponse(ResponseStatus.BLOCKED, "Sorry you dont have sufficient permissions");
    //    
    //}

    [AuthorizeAdmin(AdminUserType.SUPER_ADMIN, AdminUserType.ADMIN)]
    public ActionResult manageLeads(LongIdInfo userId, string operation)
    {
        //var user = JwtObject.GetManagementUser(HttpContext);
        JwtObject jwtObject = JwtObject.GetJWTObject(HttpContext);
        var parentId = jwtObject.adminId;
        
        if (IsFeatureEnabled(Feature.USER_MANAGEMENT))
        {
            return new JsonResult(new JsonResponse(ResponseStatus.SUCCESS, null, ClientDatabaseRequest.manageLeads(parentId, userId, int.Parse(operation))));
            
        }
        return new JsonResult(new JsonResponse(ResponseStatus.BLOCKED, "Sorry you dont have sufficient permissions"));
        
    }

    [AuthorizeAdmin(AdminUserType.ADMIN)]
    public ActionResult assignNewParent(LongIdInfo userId, LongIdInfo parentId, string operation)
    {
        
        if (IsFeatureEnabled(Feature.USER_MANAGEMENT))
        {
            return new JsonResult(new JsonResponse(ResponseStatus.SUCCESS, null, ClientDatabaseRequest.manageLeads(parentId, userId, int.Parse(operation))));
            
        }
        return new JsonResult(new JsonResponse(ResponseStatus.BLOCKED, "Sorry you dont have sufficient permissions"));
        
    }

    [AuthorizeAdmin(AdminUserType.ADMIN, AdminUserType.SUPER_ADMIN)]
    public ActionResult changeUserType(string userId, string userType)
    {
        
        if (IsFeatureEnabled(Feature.USER_MANAGEMENT))
        {
            return new JsonResult(new JsonResponse(ResponseStatus.SUCCESS, null, ClientDatabaseRequest.changeUserType(LongIdInfo.IdParser(long.Parse(userId)), int.Parse(userType))));
            
        }
        return new JsonResult(new JsonResponse(ResponseStatus.BLOCKED, "Sorry you dont have sufficient permissions"));
        
    }

    [AuthorizeAdmin(AdminUserType.ADMIN, AdminUserType.SUPER_ADMIN)]
    public ActionResult UpdatePartnerContactFeatures(LongIdInfo partnerId,string product)
    {
        
        if (IsFeatureEnabled(Feature.USER_MANAGEMENT))
        {
            List<Hashtable> contactLists = ClientDatabaseRequest.getPartnersAllLevelContacs(partnerId);
            foreach (var contact in contactLists)
            {
                bool res = ClientDatabaseRequest.UpdateTAdmin(LongIdInfo.IdParser(Convert.ToInt64(contact["userId"])), product, Convert.ToInt32(contact["userType"]));
                if (res && !string.IsNullOrEmpty(product))
                {
                    ManagementUserImpl.UpdateFeatureList(LongIdInfo.IdParser(Convert.ToInt64(contact["userId"])), product);
                }
            }
            return new JsonResult(new JsonResponse(ResponseStatus.SUCCESS, null, "successfully update feature list"));
            
        }
        return new JsonResult(new JsonResponse(ResponseStatus.BLOCKED, "Sorry you dont have sufficient permissions"));
        
    }


    [AuthorizeAdmin(AdminUserType.ADMIN, AdminUserType.SUPER_ADMIN)]
    public ActionResult UpdateTAdmin(LongIdInfo userId, string product, string userType)
    {
        
        if (IsFeatureEnabled(Feature.USER_MANAGEMENT))
        {
            bool res = ClientDatabaseRequest.UpdateTAdmin(userId, product, int.Parse(userType));
            if (res && !string.IsNullOrEmpty(product))
            {
                ManagementUserImpl.UpdateFeatureList(userId, product);
            }
            return new JsonResult(new JsonResponse(ResponseStatus.SUCCESS, null, "successfully update feature list"));
            
        }
        return new JsonResult(new JsonResponse(ResponseStatus.BLOCKED, "Sorry you dont have sufficient permissions"));
        
    }


    [AuthorizeAdmin(AdminUserType.ADMIN, AdminUserType.SUPER_ADMIN)]
    public ActionResult updatePortalAcess(LongIdInfo userId, int active)
    {
        
        if (IsFeatureEnabled(Feature.USER_MANAGEMENT))
        {
            if(active == 1)
                ClientDatabaseRequest.updatePortalAcess(userId, active,HttpContext);
            return new JsonResult(new JsonResponse(ResponseStatus.SUCCESS, null, "successfully update feature list"));
            
        }
        return new JsonResult(new JsonResponse(ResponseStatus.BLOCKED, "Sorry you dont have sufficient permissions"));
        
    }

    [AuthorizeClient]
    public JsonResult GetUser(SearchQuery query)
    {

        JwtObject jwtObject = JwtObject.GetJWTObject(HttpContext);
        StoreUser storeUser = new StoreUser();
        storeUser.userId = jwtObject.adminId;
        storeUser.name = jwtObject.adminName;
        storeUser.username = jwtObject.mobile;
        storeUser.userType = jwtObject.userType;
        storeUser.features = AdminDatabaseRequest.GetFeatureList(jwtObject.adminId, jwtObject.userType);
        storeUser.partners = ClientDatabaseRequest.GetUsersPartnersList(jwtObject.adminId, jwtObject.userType);
        if (jwtObject.userType == AdminUserType.STANDARD)
        {
            storeUser.accountInfo = ClientDatabaseRequest.GetUsersParentAccountInfo(jwtObject.adminId);
            jwtObject.clientId = (int)storeUser.accountInfo[0].clientId.local_value;
            JWTManager.updateJwtToken(jwtObject,HttpContext);
            //SessionUtils.setAdminInView(adminUser, HttpContext);
        }
        storeUser.token = jwtObject.i2e1_admin_token;
        
        return new JsonResult(new JsonResponse(ResponseStatus.SUCCESS, "", storeUser));
        
    }

    [AuthorizeClient]
    public JsonResult GetFilterOptions(string filter)
    {
        
        
        return new JsonResult(new JsonResponse(ResponseStatus.SUCCESS, "", ClientDatabaseRequest.GetFilterOptions(filter,HttpContext)));
        
    }


    [AuthorizeClient]
    public JsonResult GetInternalAccounts()
    {
        
        
        return new JsonResult(new JsonResponse(ResponseStatus.SUCCESS, "", ClientDatabaseRequest.GetInternalAccounts()));
        
    }

    [AuthorizeClient]
    public JsonResult GetLocationsForAdmin(StoreSearchQuery query, string source=null)
    {

        //var mUser = JwtObject.GetManagementUser(HttpContext);
        JwtObject jwtObject = JwtObject.GetJWTObject(HttpContext);

        return new JsonResult(new JsonResponse(ResponseStatus.SUCCESS, "", MemoryCacheUtil.GetLocationsForAdmin(memoryCache, jwtObject.adminId, query, source, jwtObject.userType)));
        
    }

    [AuthorizeClient]
    public void setUserBrand(int partnerId)
    {
        JwtObject jwtObject = JwtObject.GetJWTObject(HttpContext);
        if (partnerId > 0)
            jwtObject.partnerId = partnerId;
        else
            jwtObject.partnerId = 0;
        //SessionUtils.setAdminInView(user,HttpContext);
        JWTManager.updateJwtToken(jwtObject,HttpContext);
    }

    [AuthorizeClient]
    public JsonResult getUserBrand()
    {
        return new JsonResult(new JsonResponse(ResponseStatus.SUCCESS, "", JwtObject.GetJWTObject(HttpContext).partnerId));
    }

    [AuthorizeClient]
    public JsonResult getActiveInactiveDevicesCount(StoreSearchQuery query)
    {

        //var mUser = JwtObject.GetManagementUser(HttpContext);
        JwtObject jwtObject = JwtObject.GetJWTObject(HttpContext);
        return new JsonResult(new JsonResponse(ResponseStatus.SUCCESS, "", MemoryCacheUtil.GetActiveInactiveDevicesCount(memoryCache, new ManagementUser()
        {
            userid = jwtObject.adminId,
            userType = jwtObject.userType
        }, query)));
    }

    private bool IsFeatureEnabled(Feature featureToBeChecked)
    {
        try
        {
            ManagementUser adminInPower = JwtObject.GetManagementUser(HttpContext);
            if (adminInPower.userType == AdminUserType.SUPER_ADMIN) return true;
            Dictionary<string, int> features = adminInPower.features;
            var feature = features.First(x => x.Key == featureToBeChecked.GetHashCode().ToString());
            return feature.Value == 1 ? true : false;
        }
        catch {
            return false;
        }
    }

    //TODO
    public JsonResult Impersonate(string adminUserName)
    {
        
        ManagementUser adminInPower = JwtObject.GetManagementUser(HttpContext);
        ManagementUser impersonatedAdmin = JwtObject.GetManagementUser(HttpContext);
        if (impersonatedAdmin == null)
        {
            return new JsonResult(new JsonResponse(ResponseStatus.FAILURE, "Error impersonating " + adminUserName));
            
        }
        if (impersonatedAdmin.name == adminUserName)
        {
            return new JsonResult(new JsonResponse(ResponseStatus.FAILURE, "Already viewing " + adminUserName));
            
        }

        try
        {
            //if adminid falls under admin in power
            impersonatedAdmin = ManagementUserImpl.GetAdminUser(adminUserName);
        }
        catch
        {
            return new JsonResult(new JsonResponse(ResponseStatus.FAILURE, "Error impersonating " + adminUserName));
        }

        return new JsonResult(new JsonResponse(ResponseStatus.SUCCESS, "Impersonated"));
        
        
    }

    [AuthorizeClient]
    public JsonResult createNewSetting(CombinedSetting newSetting)
    {

        //var user = JwtObject.GetManagementUser(HttpContext);
        JwtObject jwtObject = JwtObject.GetJWTObject(HttpContext);
        int result = ClientDatabaseRequest.createNewSetting(new ManagementUser()
        {
            userid = jwtObject.adminId,
            userType = jwtObject.userType
        }, newSetting);
        WebUtils.LogInfoToCosmos("createNewSetting", "A new setting is created by admin in power : " + jwtObject.loginName + " and admin in view is : " + jwtObject.loginName +
                " : " + ", newly created setting id is : " + result);
        return new JsonResult(new JsonResponse(ResponseStatus.SUCCESS, "", result));
    }


    [AuthorizeClient]
    public JsonResult SaveGroup(WebUserGroupNew group, int settingId)
    {

        JwtObject jwtObject = JwtObject.GetJWTObject(HttpContext);
        group.adminId = jwtObject.adminId;

        bool updated = ClientDatabaseRequest.UpdateGroup(group, settingId, jwtObject.adminId);
        if (updated) {
            WebUtils.LogInfoToCosmos("UpdateGroup", "Group " + group.groupName + " with group id : " + group.groupId + " and setting id : " + settingId +
                " is updated by admin in power : " + jwtObject.loginName + " and admin in view is : " + jwtObject.loginName);
            return new JsonResult(new JsonResponse(ResponseStatus.SUCCESS, "group updated"));
        }
        else
        {
            WebUtils.LogErrorToCosmos("UpdateGroup", "Updating Group " + group.groupName + " with group id : " + group.groupId + " and setting id : " + settingId +
                " is failed attempted by admin in power : " + jwtObject.loginName + " and admin in view is : " + jwtObject.loginName);
            return new JsonResult(new JsonResponse(ResponseStatus.FAILURE, "error updating group"));
        }
            
        
    }

    [AuthorizeClient]
    public JsonResult GetUserGroups(int settingId)
    {
        return new JsonResult(new JsonResponse(ResponseStatus.SUCCESS, "", ClientDatabaseRequest.GetUserGroups(settingId)));
        
    }

    [AuthorizeAdmin(AdminUserType.ADMIN)]
    public JsonResult DeleteSettings(int settingId)
    {
        JwtObject jwtObject = JwtObject.GetJWTObject(HttpContext);
        if (IsFeatureEnabled(Feature.ADVANCE_SETTINGS_OPTIONS)) {
            WebUtils.LogInfoToCosmos("DeleteSettings", "Setting with id : " + settingId + " is deleted by admin in power : " + jwtObject.loginName
                + " and admin in view is : " + jwtObject.loginName);
            return new JsonResult(new JsonResponse(ResponseStatus.SUCCESS, "Setting deleted successfully", ClientDatabaseRequest.DeleteSettings(settingId)));
        }
        else return new JsonResult(new JsonResponse(ResponseStatus.FAILURE, "Not authorised to delete setting"));
        
    }

    [AuthorizeClient]
    public JsonResult GetNassesForSetting(int settingId)
    {
        
        
        return new JsonResult(new JsonResponse(ResponseStatus.SUCCESS, "", CoreDbCalls.GetInstance().GetNassesForSetting(settingId)));
        
    }

    [AuthorizeClient]
    public JsonResult GetUserGroupBasicConfiguration(int settingId, int groupId)
    {
        return new JsonResult(new JsonResponse(ResponseStatus.SUCCESS, "", ClientDatabaseRequest.GetUserGroupBasicConfiguration(settingId, groupId)));
    }

    [AuthorizeClient]
    public JsonResult GetAdvanceConfiguration(int settingId)
    {
        return new JsonResult(new JsonResponse(ResponseStatus.SUCCESS, "", ClientDatabaseRequest.GetAdvanceConfiguration(settingId)));
    }

    [AuthorizeAdmin(AdminUserType.ADMIN, AdminUserType.SUPER_ADMIN)]
    public async Task<JsonResult> SaveTemplate(Template template, int clientId, int partnerId)
    {
        if (!string.IsNullOrEmpty(template.templateContent))
        {
            string relativePath = "client_" + clientId +
            "/partner_" + partnerId +
            "/template/template_" + DateTime.UtcNow.Ticks + ".html";
            try
            {
                MemoryStream fs = new MemoryStream();
                Byte[] info = new UTF8Encoding(true).GetBytes(template.templateContent);
                fs.Write(info, 0, info.Length);
                template.templatePath =  await S3Utils.CreateFile(fs, relativePath,
                I2e1ConfigurationManager.GetInstance().GetSetting("client_data_folder").ToString());
            }
            catch (Exception ex)
            {
                WebUtils.LogErrorToCosmos("error uploading file to aws " + ex.Message);
                return new JsonResult(new JsonResponse(ResponseStatus.FAILURE, "Fail to create/update template " + template.templateName));
            }
        }
        else template.templatePath = String.Empty;

        try
        {
            int id = ClientDatabaseRequest.SaveTemplate(JwtObject.GetManagementUser(HttpContext), template);
            if (id == 0)
            {
                throw new Exception("Failed to update template");
            }
            WebUtils.LogInfoToCosmos("Template with name : " + template.templateName + " is saved", template);
            return new JsonResult(new JsonResponse(ResponseStatus.SUCCESS, "Template " + template.templateName + " updated", new
            {
                templateId = id
            }));
        }
        catch (Exception ex)
        {
            WebUtils.LogErrorToCosmos(template.templateName + " update failed. Ex: " + ex.Message, template);
            if(ex.Message.Contains("Cannot insert duplicate key in object"))
                return new JsonResult(new JsonResponse(ResponseStatus.FAILURE, "A template called '" + template.templateName +"' already exists."));
            else
                return new JsonResult(new JsonResponse(ResponseStatus.FAILURE, "Fail to create/update template " + template.templateName));
            
        }
        
        
        
    }

    [AuthorizeAdmin(AdminUserType.ADMIN, AdminUserType.SUPER_ADMIN)]
    public async Task<JsonResult> previewTamplate(Template template)
    {
        if (!string.IsNullOrEmpty(template.templateContent))
        {
            string relativePath = "branding-template/preview.html";
            try
            {
                MemoryStream fs = new MemoryStream();
                Byte[] info = new UTF8Encoding(true).GetBytes(template.templateContent);
                fs.Write(info, 0, info.Length);
                template.templatePath = await S3Utils.CreateFile(fs, relativePath, "meta-data");
            }
            catch (Exception ex)
            {
                Logger.GetInstance().Info("error uploading file to aws " + ex.Message);
                WebUtils.LogErrorToCosmos("error uploading file to aws " + ex.Message);
                return new JsonResult(new JsonResponse(ResponseStatus.FAILURE, "Fail to create/update template " + template.templateName));
                
            }
        }
        else template.templatePath = String.Empty;

        try
        {
            int id = ClientDatabaseRequest.SaveTemplate(JwtObject.GetManagementUser(HttpContext), template);
            if (id == 0)
            {
                throw new Exception("Failed to update template");
            }
            return new JsonResult(new JsonResponse(ResponseStatus.SUCCESS, "Template " + template.templateName + " updated", new
            {
                templateId = id
            }));
        }
        catch (Exception ex)
        {
            WebUtils.LogErrorToCosmos(template.templateName + " update failed. Ex: " + ex.Message, template);
            return new JsonResult(new JsonResponse(ResponseStatus.FAILURE, "Fail to create/update template " + template.templateName));
            
        }

        WebUtils.LogInfoToCosmos("Template with name : " + template.templateName + " is saved", template);
        
    }

    public ActionResult templateCreator()
    {
        return View("~/Views/Client/brandingTemplateCreator.cshtml");
    }

    [AuthorizeAdmin(AdminUserType.ADMIN, AdminUserType.SUPER_ADMIN)]
    public JsonResult SaveTemplate1(Template template)
    {
        var id = 0;
        try
        {
            id = ClientDatabaseRequest.SaveTemplate(JwtObject.GetManagementUser(HttpContext), template);
            if (id == 0) {
                throw new Exception("Failed to update template");
            }
        }
        catch (Exception ex)
        {
            WebUtils.LogErrorToCosmos(template.templateName + " update failed. Ex: " + ex.Message, template);
            return new JsonResult(new JsonResponse(ResponseStatus.FAILURE, "Fail to update template " +template.templateName));
        }
        return new JsonResult(new JsonResponse(ResponseStatus.SUCCESS, "Template " + template.templateName + " updated", id));
        WebUtils.LogInfoToCosmos("Template with name : " + template.templateName + " is saved", template);
    }

    [AuthorizeAdmin(AdminUserType.ADMIN, AdminUserType.SUPER_ADMIN)]
    public JsonResult UpdateTemplateQuestions(int templateId, List<Question> questions, int firstQuestionId, int lastQuestionId)
    {
        bool updated = ClientDatabaseRequest.SaveTemplateQuestions(templateId, questions, firstQuestionId, lastQuestionId);
        JwtObject jwtObject = JwtObject.GetJWTObject(HttpContext);
        if (updated)
        {
            WebUtils.LogInfoToCosmos("SaveTemplateQuestions", "Template questions with questions : " + JsonConvert.SerializeObject(questions) + " is saved for template id: " + templateId
            + " by admin in power : " + jwtObject.loginName + " and admin in view : " + jwtObject.loginName);

            return new JsonResult(new JsonResponse(ResponseStatus.SUCCESS, "Template questions updated"));
        }
        else
            return new JsonResult(new JsonResponse(ResponseStatus.FAILURE, "Fail to update template questions"));
    }

    [AuthorizeAdmin(AdminUserType.ADMIN, AdminUserType.SUPER_ADMIN)]
    public JsonResult AddTemplateQuestion(int templateId, Question question)
    {
        return new JsonResult(new JsonResponse(ResponseStatus.SUCCESS, "", ClientDatabaseRequest.AddTemplateQuestion(templateId, question)));
    }

    [AuthorizeAdmin(AdminUserType.ADMIN, AdminUserType.SUPER_ADMIN)]
    public JsonResult UpdateQuestion(int templateId, Question question)
    {
        return new JsonResult(new JsonResponse(ResponseStatus.SUCCESS, "", ClientDatabaseRequest.UpdateQuestion(templateId, question)));
    }

    [AuthorizeAdmin(AdminUserType.ADMIN, AdminUserType.SUPER_ADMIN)]
    public JsonResult UpdateQuestionOptions(Question question, int templateId)
    {
        List<Option> options = ClientDatabaseRequest.UpdateQuestionOptions(question, templateId);
        if(options == null)
            return new JsonResult(new JsonResponse(ResponseStatus.FAILURE, "Failed to update question"));
        else 
            return new JsonResult(new JsonResponse(ResponseStatus.SUCCESS, "Question updated", options));
    }

    [AuthorizeAdmin(AdminUserType.SUPER_ADMIN, AdminUserType.ADMIN)]
    public JsonResult GetAllTemplates()
    {
        var list = ClientDatabaseRequest.GetAllTemplates();
        return new JsonResult(new JsonResponse(ResponseStatus.SUCCESS, "", list));
    }


    [AuthorizeAdmin(AdminUserType.SUPER_ADMIN, AdminUserType.ADMIN)]
    public JsonResult fetchTemplateDetails(int templateId)
    {
        var template = DbCalls.GetInstance().GetTemplate(templateId);
        var qlist = ClientDatabaseRequest.GetQuestions(templateId);
        Dictionary<string, dynamic> templateDetails = new Dictionary<string, dynamic>();
        templateDetails.Add("template",template);
        templateDetails.Add("qlist",qlist);
        return new JsonResult(new JsonResponse(ResponseStatus.SUCCESS, "", templateDetails));
    }


    [AuthorizeAdmin(AdminUserType.SUPER_ADMIN, AdminUserType.ADMIN)]
    public JsonResult AddSms(LongIdInfo userid, int smsCount)
    {
        ManagementUser adminInPower = JwtObject.GetManagementUser(HttpContext);
        if (userid != null)
        {
            int clientId = ClientUtils.GetClientIdFromAdminId(userid);
            double balance = smsCount * (new ClientDataSourceImpl()).GetDeliveryCostPerTarget(1);
            return new JsonResult(new JsonResponse(ResponseStatus.SUCCESS, "sms upgraded"));
        }
        else return new JsonResult(new JsonResponse(ResponseStatus.FAILURE, "sms upgrade fail"));
    }

    [AuthorizeAdmin(AdminUserType.SUPER_ADMIN, AdminUserType.ADMIN)]
    public JsonResult AddSenderId(LongIdInfo userid, string senderId, LongIdInfo partnerId)
    {
        ManagementUser adminInPower = JwtObject.GetManagementUser(HttpContext);
        if (userid != null)
        {
            int clientId = ClientUtils.GetClientIdFromAdminId(userid);

            if (clientId > 0)
            {
                var response = ClientDatabaseRequest.AddSenderId(senderId, partnerId);
                if (response.Count > 0)
                {
                    return new JsonResult(new JsonResponse(ResponseStatus.SUCCESS, "sender id added"));
                }
                else
                {
                    return new JsonResult(new JsonResponse(ResponseStatus.SUCCESS, "sender id creation failed"));
                }
            }
            else
            {
                return new JsonResult(new JsonResponse(ResponseStatus.FAILURE, "user not associated with any client"));
            }
        }
        else return new JsonResult(new JsonResponse(ResponseStatus.FAILURE, "user invalid"));
    }

    //[AdminPortalAuthorization]
    //public JsonResult GetDeviceStats(List<LongIdInfo> nases)
    //{
    //    try
    //    {
    //        var lastLoginTime = RadAcctDbCalls.GetLastLoginTime(nases);
    //        var usersIn24Hours = RadAcctDbCalls.GetUsersInDuration(nases, DateTime.UtcNow.AddHours(-24));
    //        var usersIn1Hour = RadAcctDbCalls.GetUsersInDuration(nases, DateTime.UtcNow.AddHours(-1));
    //        var dataUsed = RadAcctDbCalls.GetDataUsedInInterval(30, nases);
    //        return new JsonResult(new JsonResponse(ResponseStatus.SUCCESS, "", 
    //            new { lastLoginTime = lastLoginTime,
    //                usersIn24Hours = usersIn24Hours, usersIn1Hour = usersIn1Hour,
    //                dataUsed = dataUsed }));

    //    }
    //    catch
    //    {
    //        return new JsonResult(new JsonResponse(ResponseStatus.FAILURE, "Error retriving data"));
    //    }
    //}

    //[AdminPortalAuthorization]
    //public JsonResult GetLastLoginTime(List<LongIdInfo> nases)
    //{
    //    try
    //    {
    //        var list = RadAcctDbCalls.GetLastLoginTime(nases);
    //        return new JsonResult(new JsonResponse(ResponseStatus.SUCCESS, "", list));

    //    }
    //    catch
    //    {
    //        return new JsonResult(new JsonResponse(ResponseStatus.FAILURE, "Error retriving last login time"));
    //    }
    //}

    [AdminPortalAuthorization]
    public JsonResult GetUsersInXHours(List<LongIdInfo> nases, int hours)
    {
        try
        {
            var list = RadAcctDbCalls.GetUsersInDuration(nases, DateTime.UtcNow.AddHours(-hours));
            return new JsonResult(new JsonResponse(ResponseStatus.SUCCESS, "", list));
        }
        catch
        {
            return new JsonResult(new JsonResponse(ResponseStatus.FAILURE, "Error retriving users in " + hours + " hours"));
        }
    }

    [AdminPortalAuthorization]
    public JsonResult GetDataUsed(List<LongIdInfo> nases)
    {
        try
        {
            var list = RadAcctDbCalls.GetDataUsedInInterval(30, nases);
            return new JsonResult(new JsonResponse(ResponseStatus.SUCCESS, "", list));
        }
        catch(Exception ex)
        {
            return new JsonResult(new JsonResponse(ResponseStatus.FAILURE, "Error retriving data used"));
        }
    }

    [AdminPortalAuthorization]
    public JsonResult GetStoreContact(LongIdInfo nasid)
    {
        try
        {
            var contact = AdminDatabaseRequest.GetStoreContactDetails(nasid);
            return new JsonResult(new JsonResponse(ResponseStatus.SUCCESS, "", contact));
        }
        catch (Exception ex)
        {
            return new JsonResult(new JsonResponse(ResponseStatus.FAILURE, "Error retriving data used"));
        }
    }

    [AdminPortalAuthorization]
    public JsonResult GetListChecks(LongIdInfo nasid, ListCheckDataType? listType)
    {
        try
        {
            if (listType == null)
            {
                var list = DbCalls.GetInstance().GetListChecks(nasid);
                return new JsonResult(new JsonResponse(ResponseStatus.SUCCESS, "", list));
            }
            else
            {
                var list = CoreDbCalls.GetInstance().GetListChecks(nasid, (ListCheckDataType) listType);
                return new JsonResult(new JsonResponse(ResponseStatus.SUCCESS, "", list));
            }
        }
        catch
        {
            return new JsonResult(new JsonResponse(ResponseStatus.FAILURE, "Error retriving data"));
        }
    }   

    private void SaveListCheck(LongIdInfo nasid, Dictionary<string, Dictionary<string, string>> accessList)
    {
        foreach (ListCheckDataType listType in Enum.GetValues(typeof(ListCheckDataType)))
        {
            if (listType == ListCheckDataType.PAYMENT_OPTIONS)
                continue;

            if (accessList.TryGetValue(listType.ToString(), out var listToSave))
            {
                if (listToSave == null)
                    listToSave = new Dictionary<string, string>();

                var data = new System.Text.StringBuilder();
                foreach (var pair in listToSave)
                {
                    data.Append(pair.Key).Append(";").Append(pair.Value).Append(",");
                }
                string oldValue = ClientDatabaseRequest.SaveListChecks(nasid, listType, data.ToString());
                if (oldValue != data.ToString())
                {
                    switch (listType)
                    {
                        case ListCheckDataType.SSID:
                            BasicSQSHelper.SendFIFOMessage(BasicSQSHelper.GetQueueARN("remote.fifo"), nasid.ToString(), new i2e1_basics.Utilities.MicroServiceMessage()
                            {
                                key = "UPDATE_SSID",
                                payload = new JObject() {
                                    { "nasid", nasid.GetLongId() },
                                    { "ssid", listToSave["i2e1"] } 
                                }
                            });
                            break;
                        case ListCheckDataType.SSID_PASSWORD:
                            BasicSQSHelper.SendFIFOMessage(BasicSQSHelper.GetQueueARN("remote.fifo"), nasid.ToString(), new i2e1_basics.Utilities.MicroServiceMessage()
                            {
                                key = "UPDATE_PASSWORD",
                                payload = new JObject() {
                                    { "nasid", nasid.GetLongId() },
                                    { "password", listToSave["i2e1"] } 
                                }
                            });
                            break;
                    };
                }
            }
        }
    }

    [AdminPortalAuthorization]
    [CoreModelBinder]
    public JsonResult SaveListChecks(LongIdInfo nasid, Dictionary<string, Dictionary<string, string>> accessList)
    {
        try
        {
            SaveListCheck(nasid, accessList);
        }
        catch(Exception ex)
        {
            return new JsonResult(new JsonResponse(ResponseStatus.FAILURE, "Error saving list"));
        }
        return new JsonResult(new JsonResponse(ResponseStatus.SUCCESS, "Saved List"));
    }
    
    public JsonResult BlockNumber(LongIdInfo nasid, string mKey, int block)
    {
        var mobile = new UserBaseModel()
        {
            mkey = mKey
        }.GetDecryptedValue();
        try
        {
            var list = CoreDbCalls.GetInstance().GetListChecks(nasid, (wiom_router_api.Models.ListCheckDataType)ListCheckDataType.BLOCKED_LIST);
            if (block == 1)
                list[mobile] = string.Empty;
            else if (list.ContainsKey(mobile))
            {
                list.Remove(mobile);
            }

            var data = new System.Text.StringBuilder();
            foreach (var pair in list)
            {
                data.Append(pair.Key).Append(";").Append(pair.Value).Append(",");
            }
            ClientDatabaseRequest.SaveListChecks(nasid, ListCheckDataType.BLOCKED_LIST, data.ToString());
        }
        catch
        {
            return new JsonResult(new JsonResponse(ResponseStatus.FAILURE, "Error saving list"));
        }
        return new JsonResult(new JsonResponse(ResponseStatus.SUCCESS, "block list updated"));
    }

    [AdminPortalAuthorization]
    public JsonResult GetAuthenticationModeHelper(LongIdInfo nasid)
    {
        try
        {
            if (nasid != null)
            {
                return new JsonResult(new JsonResponse(ResponseStatus.SUCCESS,"", ClientDatabaseRequest.GetAuthenticationModeHelper(nasid)));
            }
            else
            {
                return new JsonResult(new JsonResponse(ResponseStatus.FAILURE, "No nasid selected"));
            }
        }
        catch
        {
            return new JsonResult(new JsonResponse(ResponseStatus.FAILURE, "Error retriving Authentication Mode Helper information"));
        }
    }

    public JsonResult SaveAuthenticationModeHelper(LongIdInfo nasid, List<String> value)
    {
        try
        {
            if (nasid.local_value > 0 && value != null && value.Count > 0)
            {
                String[] arr = value.ToArray();
                ClientDatabaseRequest.SaveAuthenticationModeHelper(nasid, arr,HttpContext);
                return new JsonResult(new JsonResponse(ResponseStatus.SUCCESS, "Changes updated"));
            }
        }
        catch
        { 
        }
        return new JsonResult(new JsonResponse(ResponseStatus.FAILURE, "Error updating data"));
    }

    public JsonResult UpdateStorePolicy(LongIdInfo nasid, string value)
    {
        bool updated= false;
        try
        {
            updated = ClientDatabaseRequest.SaveSingleNasOperation(nasid, 17, value);
        }
        catch
        {
        }
        if(updated) {
            CoreCacheHelper.GetInstance().Reset(CoreCacheHelper.ROUTER_BASIC, nasid);
            return new JsonResult(new JsonResponse(ResponseStatus.SUCCESS, "Updated store policy"));
        }
        else
        {
            return new JsonResult(new JsonResponse(ResponseStatus.FAILURE, "Failed to update store policy"));
        }
        
    }

    public JsonResult GetStorePolicy(LongIdInfo nasid)
    {
        string value = "0";
        try
        {
            value = CoreDbCalls.GetInstance().GetSingleNasOperation(nasid, 17);
            if (string.IsNullOrEmpty(value))
            {
                value = "0";
            }
        }
        catch
        {
        }
        return new JsonResult(new JsonResponse(ResponseStatus.SUCCESS, "store policy", value));
    }

    public JsonResult TransferSetting(int settingId, string username)
    {
        if (string.IsNullOrEmpty(username))
        {
            return new JsonResult(new JsonResponse(ResponseStatus.FAILURE, "bad request"));
        }
        ManagementUser adminUser = ManagementUserImpl.GetAdminUser(username);
        CombinedSetting setting = new CombinedSetting();
        setting.settingId = settingId;
        if (IsFeatureEnabled(Feature.ADVANCE_SETTINGS_OPTIONS)) {
            WebUtils.LogInfoToCosmos("Settings transfered to other user", new { settingId = settingId, otherUser = ManagementUserImpl.GetAdminUser(username) });
            return new JsonResult(new JsonResponse(ResponseStatus.SUCCESS, "Setting updated successfully", ClientDatabaseRequest.UpdateSetting(adminUser, setting)));
        } 
        else
        {
            WebUtils.LogInfoToCosmos("Settings transfer to other user failed", new { settingId = settingId, otherUser = ManagementUserImpl.GetAdminUser(username) });
            return new JsonResult(new JsonResponse(ResponseStatus.FAILURE, "Unable to transfer to " + username));
        }
    }

    public JsonResult GetMarketPlaces(string marketplace)
    {
        return new JsonResult(new JsonResponse(ResponseStatus.SUCCESS, "", ClientDatabaseRequest.GetMarketPlaceFromDB(marketplace)));
    }

    public JsonResult updateMarketPlace(MarketPlace marketplace)
    {
        if (String.IsNullOrEmpty(marketplace.marketPlaceName))
        {
            return new JsonResult(new JsonResponse(ResponseStatus.FAILURE, "market place is null"));
        }
        else
        {
            return new JsonResult(new JsonResponse(ResponseStatus.SUCCESS, "", ClientDatabaseRequest.updateMarketPlace(marketplace)));
        }
    }

    [AuthorizeClient]
    public JsonResult GetStoreDetailsStatic(LongIdInfo nasid)
    {
        var details = AdminDatabaseRequest.GetStoreDetailsStatic(nasid);
        return new JsonResult(new JsonResponse(ResponseStatus.SUCCESS, "", details));
    }

    [AuthorizeClient]
    public JsonResult GetStoreDetails(LongIdInfo nasid)
    {
        var details = ClientDatabaseRequest.GetStoreDetails(nasid);
        return new JsonResult(new JsonResponse(ResponseStatus.SUCCESS, "", details));
    }

    //check once
    [AdminPortalAuthorization]
    [HttpPost]
    public JsonResult saveStoreDetails(StoreInfo store)
    {
        LongIdInfo router = LongIdInfo.IdParser((long)HttpContext.Items["nasid"]);
        store.nasid = router;

        bool res = ClientDatabaseRequest.saveBrandStore(store, router);

        return new JsonResult(new JsonResponse(ResponseStatus.SUCCESS, "", null));
    }

    public JsonResult getAllFeatures()
    {
        List<KeyValuePair<int, string>> feature_list = new List<KeyValuePair<int, string>>();
        foreach (Feature ct in Enum.GetValues(typeof(Feature)))
        {
            feature_list.Add(new KeyValuePair<int, string>((int)ct, ct.ToString()));
        }
        return new JsonResult(new JsonResponse(ResponseStatus.SUCCESS, "", feature_list));
    }

    [AuthorizeAdmin(AdminUserType.SUPER_ADMIN, AdminUserType.ADMIN)]
    public JsonResult createPackages(SellablePackage packages)
    {
        ManagementUser adminInPower = JwtObject.GetManagementUser(HttpContext);
        return new JsonResult(new JsonResponse(ResponseStatus.SUCCESS, "", ClientDatabaseRequest.createPackages(packages, adminInPower)));
    }

    [AuthorizeAdmin(AdminUserType.SUPER_ADMIN, AdminUserType.ADMIN, AdminUserType.STANDARD)]
    public JsonResult getPackages()
    {
        return new JsonResult(new JsonResponse(ResponseStatus.SUCCESS, "", ClientDatabaseRequest.getPackages()));
    }

    [AuthorizeAdmin(AdminUserType.SUPER_ADMIN, AdminUserType.ADMIN)]
    public JsonResult getUsersAllClientsPartnersAndStores(int userId = 0)
    {
        ManagementUser adminInPower = JwtObject.GetManagementUser(HttpContext);
        return new JsonResult(new JsonResponse(ResponseStatus.SUCCESS, "", JsonConvert.DeserializeObject<JArray>(ClientDatabaseRequest.getUsersAllClientsPartnersAndStores(adminInPower.userid))));
    }

    [AuthorizeClient]
    public JsonResult GetPricing()
    {
        List<dynamic> pricing = new List<dynamic>();
        var clientDatasource = new ClientDataSourceImpl();
        var i2e1Datasource = new I2E1DataSourceImpl();

        pricing.Add(new {
            name = "Send Now sms to my users",
            costPerDelivery = clientDatasource.GetDeliveryCostPerTarget(1),
            costPerClick = clientDatasource.GetClickCostPerTarget(1)
        });

        pricing.Add(new
        {
            name = "Scheduled sms on i2e1 users",
            costPerDelivery = i2e1Datasource.GetDeliveryCostPerTarget(1),
            costPerClick = i2e1Datasource.GetClickCostPerTarget(1)
        });

        pricing.Add(new
        {
            name = "Prime Scheduled sms on my users",
            costPerDelivery = .15,
            costPerClick = clientDatasource.GetClickCostPerTarget(1)
        });

        pricing.Add(new
        {
            name = "promotions to my users",
            costPerDelivery = clientDatasource.GetDeliveryCostPerTarget(8),
            costPerClick = clientDatasource.GetClickCostPerTarget(8)
        });

        return new JsonResult(new JsonResponse(ResponseStatus.SUCCESS, "", pricing));
    }

    public void ConsolidateNow(int campaignId)
    {
        try
        {
            using (var client = new WebClient())
            {
                client.Headers.Add(HttpRequestHeader.ContentType, "application/json");
                string response = client.DownloadString(I2e1ConfigurationManager.GetInstance().GetSetting("InteractionEndPoint") + "Home/ConsolidateImmediately/" + campaignId);
            }
        }
        catch (Exception ex)
        {
            Logger.GetInstance().Error("Error in immidiate consolidation" + ex.Message);
        }
    }


    public JsonResult GoToAdminServicesSettingsPage()
    {
        var url = I2e1ConfigurationManager.IS_PROD ? "https://services.i2e1.in/settings?i2e1-admin-token=" : "https://services.testease.i2e1.in/settings?i2e1-admin-token=";
        url += CookieUtils.GetCookie(HttpContext, "i2e1-admin-token");
        return new JsonResult(new JsonResponse(ResponseStatus.SUCCESS, "url", url));
    }
}
