using System;
using System.Collections.Generic;
using System.Globalization;
using System.IO;
using System.Linq;
using CsvHelper;
using CsvHelper.Configuration;
using i2e1_basics.Models;
using i2e1_basics.Utilities;
using i2e1_core.Attributes;
using i2e1_core.Models;
using i2e1_core.Models.Client;
using i2e1_core.services;
using i2e1_core.Utilities;
using I2E1_Message.Models.Client;
using I2E1_Message.Utils;
using I2E1_WEB.Attributes;
using I2E1_WEB.Database;
using I2E1_WEB.MiddleTier;
using I2E1_WEB.Models.Client;
using I2E1_WEB.Utilities;
using I2E1_WEB.Utils;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using wifidog_core.Models;
using wiom_login_share.Models;
using wiom_login_share.Utilities;
using wiom_router_api.Models;
using wiom_router_api.Utilities;
using wiom_routerplan_share.Models.RouterPlan;
using WiomQueue = wiom_router_api.Utilities.WiomQueue;

namespace I2E1_WEB.Controllers;

public class DeviceConfigController : BaseController
{
    private static int scale = 100;
    private static int init = 1;
    private const int MAX_RECORDS = 500;

    [AuthorizeInternalUserOperation(Feature.DEVICE_CONFIG)]
    public ActionResult Index()
    {
        ViewBag.message = string.Empty;
        return ReturnConfigsForPage(null,init,scale);
    }

    [AuthorizeInternalUserOperation(Feature.DEVICE_CONFIG)]
    public ActionResult Logout()
    {
        //SessionUtils.setAdminInPower(null, HttpContext);
        JWTManager.expireJwtToken(HttpContext);
        return Redirect("~/client");
    }

    [AuthorizeInternalUserOperation(Feature.DEVICE_CONFIG)]
    [HttpPost]
    public ActionResult SubmitConfig(DeviceConfig config)
    {
        CoreWebUtils.submitDeviceConfig(config, JwtObject.GetManagementUser(HttpContext).userid);
        MicroserviceSQSHelper.SendFIFOMessage(WiomQueue.REMOTE, config.nasid.ToString(), new i2e1_basics.Utilities.MicroServiceMessage()
        {
            key = "CHANGE_DEVICE_PASSWORD",
            payload = new Newtonsoft.Json.Linq.JObject() {
                { "nasid", config.nasid.GetLongId() }, 
                { "password", config.devicePassword }
            }
        });

        if (!string.IsNullOrEmpty(config.accessPointPassword))
        {
            MicroserviceSQSHelper.SendFIFOMessage(WiomQueue.REMOTE, config.nasid.ToString(), new i2e1_basics.Utilities.MicroServiceMessage()
            {
                key = "UPDATE_PASSWORD",
                payload = new Newtonsoft.Json.Linq.JObject() {
                { "nasid", config.nasid.GetLongId() }, 
                { "password", config.accessPointPassword }
            }
            });
        }
        return ReturnConfigsForPage(config.nasid);
    }

    [LoginPortalAuthorization]
    [HttpGet]
    public JsonResult UpdateSSIDandPassword(string ssid,string password)
    {
        if (string.IsNullOrEmpty(ssid)|| string.IsNullOrEmpty(password))
        {
            return new JsonResult(new JsonResponse(ResponseStatus.FAILURE, "invalid SSID or Password"));
        }
        DeviceConfig config = new DeviceConfig();
        User user = CoreSessionUtils.GetLoginServerUser(HttpContext);
        config.nasid = user.backEndNasid;
        MicroserviceSQSHelper.SendFIFOMessage(WiomQueue.REMOTE, config.nasid.ToString(), new i2e1_basics.Utilities.MicroServiceMessage()
        {
            key = "UPDATE_SSID_PASSWORD",
            payload = new Newtonsoft.Json.Linq.JObject() {
                { "nasid", config.nasid.GetLongId() }, 
                { "ssid", ssid }, 
                { "password", password }
            }
        });

        return new JsonResult(new JsonResponse(ResponseStatus.SUCCESS, "Updated SSID and Password"));
    }

    [AuthorizeInternalUserOperation(Feature.DEVICE_CONFIG)]
    [HttpGet]
    public ActionResult AddToTController(LongIdInfo nasid, int controllerId)
    {
        DbCalls.GetInstance().CheckAndMakeEntry(nasid, controllerId);
        return ReturnConfigsForPage(nasid);
    }

    private ActionResult ReturnConfigsForPage(LongIdInfo longNasid,int start=0,int end=100)
    {
        ViewBag.nasid = longNasid;
        var configlist = DbCalls.GetInstance().GetConfig(0, 0, 100);
        return View("~/Views/DeviceConfig/index.cshtml", configlist);

    }

    //[AuthorizeInternalUserOperation(Feature.DEVICE_CONFIG)]
    //[HttpPost]
    //public ActionResult GetConfig(LongIdInfo nasid)
    //{
    //    DeviceConfig config = RouterOperation.GetConfig(nasid);
    //    if (config.nasid == nasid)
    //    {
    //        ViewBag.EditConfig = true;
    //    }
    //    return View("~/Views/DeviceConfig/index.cshtml", config);
    //}

    [AuthorizeInternalUserOperation(Feature.DEVICE_CONFIG)]
    public ActionResult ToggleStatus(String mac)
    {
        DbCalls.GetInstance().ToggleDeviceStatus(mac, JwtObject.GetManagementUser(HttpContext).userid);
        CoreCacheHelper.GetInstance().Reset(CoreCacheHelper.NAS_FROM_MACV2, mac);
        return GetConfigsView(1);
    }

    // check once
    [AuthorizeInternalUserOperation(Feature.DEVICE_CONFIG)]
    [HttpPost]
    public ActionResult GetDeviceConfigs(DeviceConfig configs)
    {
        //SessionUtils.DeviceConfigs = true;
        if (string.IsNullOrEmpty(configs.macid))
        {
            configs.macid = "00:00:00:00:00:00";
        }
        if (configs.nasid == null)
        {
            configs.nasid = null;// -99999999;
        }
        configs.macid = CoreUtil.GetNormalisedMac(configs.macid);
        var configlist = DbCalls.GetInstance().GetConfigForNas(configs);
        return View("~/Views/DeviceConfig/index.cshtml", configlist);
    }

    [AuthorizeInternalUserOperation(Feature.DEVICE_CONFIG)]
    public ActionResult GetConfigsView(int seek)
    {
        if (seek != 1 || seek != -1)
        {

        }
        int n = seek;
        if (n < init)
            n = init;
        int start = init, end = scale;
        start = start + scale * (n-1);
        end = end + scale * (n-1);
        var configlist = DbCalls.GetInstance().GetConfig(0, start,end);
        ViewBag.DevicePageCounter = 0;
        return View("~/Views/DeviceConfig/index.cshtml",configlist);
    }

    [LoginPortalAuthorization]
    [CoreModelBinder]
    public ActionResult DIYRegistration(string deviceId)
    {
        User user = CoreSessionUtils.GetLoginServerUser(HttpContext);

        if (!string.IsNullOrEmpty(deviceId))
        {
            ViewBag.deviceId = deviceId;
            //if (CacheHelper.GetInstance().IsNasUnregistered(backendNas))
            {
                return View("~/Views/DeviceConfig/wifi_registration.cshtml", user);
            }
        }

        ViewBag.deviceMacError = "Invalid mac";
        return View("~/Views/DeviceConfig/coming_soon.cshtml", user);
    }

    [HttpPost]
    [CoreModelBinder]
    public JsonResult GenerateDIYOTP(LongIdInfo nasid, string mobile)
    {
        var diyRegistrationKey = Guid.NewGuid().ToString();
        string otp = new Random().Next(1000, 10000).ToString();
        if (mobile == "3333333333") otp = "1111";
        CacheHelper.GetInstance().SetDIYToken(nasid.GetLongId(), mobile, otp);
        CookieUtils.SetCookie(HttpContext, "diy-reg-key", diyRegistrationKey, false, DateTime.UtcNow.AddMinutes(360));
        CoreSmsSender.SendGupshupSms(mobile, otp);            
        return new JsonResult(new JsonResponse(ResponseStatus.SUCCESS, null));
    }

    [HttpPost]
    [CoreModelBinder]
    public JsonResult ResendDIYCredentials(User user)
    {
        var details = AdminDatabaseRequest.GetOwnerDetails(user.mobile);
        string message = string.Format("Use {0} as user id and {0} as password to login to WIOM admin portal. https://admin.wiom.in", user.mobile);
        string senderId = "myWIOM";
        CoreSmsSender.SendSMSViaSolutionInfiniV4(message, user.mobile, senderId);
        //SmsSender.SendGupshupSmsWiomRegistration(user.mobile, details.password);
        return new JsonResult(new JsonResponse(ResponseStatus.SUCCESS, "Resending", null));
    }

    internal static string GeneratePassword(int stringLength)
    {
        Random rd = new Random();
        const string allowedChars = "abcdefghijkmnopqrstuvwxyz0123456789";
        char[] chars = new char[stringLength];
        for (int i = 0; i < stringLength; i++)
        {
            chars[i] = allowedChars[rd.Next(0, allowedChars.Length)];
        }
        return new string(chars);
    }

    [HttpPost]
    [CoreModelBinder]
    //TODO: JWT_GO_LIVE_CHECK
    public JsonResult SubmitDIYREG(string deviceId, User user, string address, string pincode, string shopName, string email)
    {
        StoreUser storeUser = new i2e1_core.Models.Client.StoreUser();
        storeUser.username = user.mobile;
        storeUser.mobile = user.mobile;
        storeUser.name = user.mobile;
        storeUser.email = email;
        storeUser.active = 1;

        var savedOtp = CacheHelper.GetInstance().GetDIYToken(user.backEndNasid, user.mobile);
        if (user.otp == savedOtp && !string.IsNullOrEmpty(deviceId))
        {
            var devicesFetched = (List<Inventory>)CoreInventoryService.FetchInventory(new InventorySearchType() { deviceId = deviceId });
            if (devicesFetched.Count == 0 || devicesFetched[0].nasid != null)
            {
                return new JsonResult(new JsonResponse(ResponseStatus.FAILURE, "ERROR_ALREADY_REGISTERED", null));
            }

            var inventory = CoreInventoryService.UpdateInventorySequence(deviceId, ShardHelper.SHARD0);
            user.nasid = inventory.nasid.ToString();
            var adminUser = CoreUserService.RegisterAdmin(user.backEndNasid.shard_id, new ManagementUser()
            {
                contact_no = user.mobile,
                password = user.mobile,
                name = shopName,
                email = email,
                email_verified = true,
                userType = AdminUserType.STANDARD,
                authType = AdminAuthType.CUSTOM,
                active = 1
            }, "phone");
            ClientUtils.MapNasidToAdmin(adminUser.userid, user.backEndNasid);
            StoreUtils.UpdateStore(user.backEndNasid, shopName, address, pincode, String.Empty, String.Empty, 3136, email, storeUser.mobile);

            CoreUserService.InsertNewUser(user.mobile, Guid.NewGuid().ToString(), ShardHelper.SHARD0);
            CoreSmsSender.SendGupshupSmsWiomRegistration(user.mobile, user.mobile);

            var now = DateTime.UtcNow;
            var pUser = new HomeRouterPlan()
            {
                mobile = user.mobile,
                otp = "DIYBuyer",
                planStartTime = now,
                planEndTime = now.AddMinutes(10),
                nasId = user.backEndNasid
            };
            long planId = CoreDbCalls.GetInstance().GenerateUser(pUser, user.backEndNasid, 4, true, 0);
            CoreDbCalls.GetInstance().ReloginUser(user, out UserSession userSession, planId);
            //Add Prime Product to Userid
            if (ClientDatabaseRequest.UpdateTAdmin(adminUser.userid, "Prime", 0))
                ManagementUserImpl.UpdateFeatureList(adminUser.userid, "Prime");
            //Add Feature 61 to Enable User Portal for Userid
            string featurepayload = "{\"0\":-1,\"1\":1,\"2\":-1,\"3\":-1,\"4\":1,\"5\":1,\"6\":1,\"7\":1,\"8\":1,\"9\":-1,\"10\":-1,\"11\":-1,\"12\":-1,\"13\":1,\"14\":1,\"15\":-1,\"16\":-1,\"17\":-1,\"18\":-1,\"19\":1,\"20\":1,\"21\":-1,\"22\":-1,\"23\":-1,\"24\":1,\"25\":1,\"26\":-1,\"27\":-1,\"28\":-1,\"29\":-1,\"30\":1,\"31\":1,\"32\":-1,\"33\":-1,\"34\":-1,\"35\":1,\"36\":-1,\"37\":-1,\"38\":-1,\"39\":1,\"40\":-1,\"41\":-1,\"42\":-1,\"43\":-1,\"44\":-1,\"45\":-1,\"46\":-1,\"47\":-1,\"48\":-1,\"49\":1,\"50\":-1,\"51\":-1,\"52\":1,\"53\":-1,\"54\":-1,\"55\":-1,\"56\":-1,\"57\":-1,\"58\":-1,\"59\":-1,\"60\":-1,\"61\":1}";
            Dictionary<string, int> features = JsonConvert.DeserializeObject<Dictionary<string, int>>(featurepayload);
            CoreUserService.SaveFeatureList(adminUser.userid, features);
            //Add Setting to Property for Userid
            CombinedSetting newSetting = new CombinedSetting();
            newSetting.name = "wiom";
            int settingId = ClientDatabaseRequest.createNewSetting(adminUser, newSetting);
            ClientDatabaseRequest.UpdateLocations(settingId, new List<LongIdInfo>() { user.backEndNasid }, true);
            string groupBasicConfigString = "{\"settingId\":0,\"userGroups\":[{\"basicConfigs\":{\"LANDING_PAGE\":{\"text\":\"Custom Landing Page\",\"attribute\":null,\"dataType\":0,\"value\":null,\"configType\":2},\"DATA_USAGE_CONTROL_MONTH\":{\"text\":\"Maximum data usage / month\",\"attribute\":null,\"dataType\":2,\"value\":0,\"configType\":10},\"UPLOAD_BANDWIDTH_AFTER_EXHAUSTED\":{\"text\":\"Upload Bandwidth after data exhausted\",\"attribute\":null,\"dataType\":1,\"value\":0,\"configType\":11},\"DOWNLOAD_BANDWIDTH_AFTER_EXHAUSTED\":{\"text\":\"Download Bandwidth after data exhausted\",\"attribute\":null,\"dataType\":1,\"value\":0,\"configType\":12},\"NO_OF_SESSIONS\":{\"text\":\"Number of Sessions / day. Applicable in case of Session less than 24 hours\",\"attribute\":null,\"dataType\":0,\"value\":\"1\",\"configType\":14},\"DATA_USAGE_PER_SESSION\":{\"text\":\"Maximum data usage / session\",\"attribute\":null,\"dataType\":2,\"value\":0,\"configType\":16},\"SESSION_TIMEOUT\":{\"text\":\"Session timeout\",\"attribute\":\"Session-Timeout\",\"dataType\":3,\"value\":\"3600\",\"configType\":101},\"CHILLISPOT_BANDWIDTH_MAX_UP\":{\"text\":\"Maximum Upload Bandwidth\",\"attribute\":\"ChilliSpot-Bandwidth-Max-Up\",\"dataType\":1,\"value\":0,\"configType\":102},\"CHILLISPOT_BANDWIDTH_MAX_DOWN\":{\"text\":\"Maximum Download Bandwidth\",\"attribute\":\"ChilliSpot-Bandwidth-Max-Down\",\"dataType\":1,\"value\":0,\"configType\":103},\"CHILLISPOT_MAX_TOTAL_OCTETS\":{\"text\":\"Maximum data usage / day\",\"attribute\":\"ChilliSpot-Max-Total-Octets\",\"dataType\":2,\"value\":512000,\"configType\":104}},\"groupId\":0}]}";
            CombinedSetting groupBasicConfig = JsonConvert.DeserializeObject<CombinedSetting>(groupBasicConfigString);
            groupBasicConfig.settingId = settingId;
            foreach (UserGroupNew group in groupBasicConfig.userGroups)
            {
                WebUserGroupNew webGroup = new WebUserGroupNew(group);
                webGroup.UpdateUserGroupBasicConfig(groupBasicConfig.settingId, adminUser.userid);
            }
            string advanceGroupBasicConfigString = "{\"settingId\":0,\"advanceConfigs\":{\"AUTH_TYPE\":{\"configType\":1,\"parameters\":[\"0\"],\"value\":\"\"},\"ROUTER_SPLASH_IMAGE\":{\"configType\":2,\"parameters\":[],\"value\":\"\"},\"LOGIN_PAGE_IMAGE\":{\"configType\":3,\"parameters\":[],\"value\":\"\"},\"LANDING_PAGE_CONFIG\":{\"configType\":4,\"parameters\":[],\"value\":\"\"},\"TEMPLATE_CONFIG\":{\"configType\":5,\"parameters\":[],\"value\":\"\"},\"SHINE_PLUS_ACTIVATION\":{\"configType\":6,\"parameters\":[],\"value\":\"\"},\"DEVICE_ACTIVATION\":{\"configType\":7,\"parameters\":[],\"value\":\"\"},\"OPERATING_HOURS\":{\"configType\":8,\"parameters\":[],\"value\":\"\"},\"FACEBOOK_PAGE\":{\"configType\":9,\"parameters\":[],\"value\":\"\"},\"FACEBOOK_CHECKIN\":{\"configType\":11,\"parameters\":[],\"value\":\"\"},\"HIDE_QUESTION\":{\"configType\":12,\"parameters\":[\"1\"],\"value\":\"\"},\"NO_OF_DEVICES_PER_USER\":{\"configType\":13,\"parameters\":[],\"value\":\"\"},\"GLOBAL_OTP\":{\"configType\":14,\"parameters\":[],\"value\":\"\"},\"AUTO_LOGIN_SPAN\":{\"configType\":15,\"parameters\":[],\"value\":\"\"},\"VALIDATE_USER_LOGIN\":{\"configType\":16,\"parameters\":[],\"value\":\"\"},\"DATA_VOUCHER_DETAILS\":{\"configType\":17,\"parameters\":[],\"value\":\"\"},\"OVERRIDE_OTP\":{\"configType\":18,\"parameters\":[],\"value\":\"\"},\"LANGUAGE_PREFERENCE\":{\"configType\":19,\"parameters\":[],\"value\":\"\"},\"NAS_DAILY_DATA_LIMIT\":{\"configType\":20,\"parameters\":[],\"value\":\"\"}}}";
            CombinedSetting advanceGroupBasicConfig = JsonConvert.DeserializeObject<CombinedSetting>(advanceGroupBasicConfigString);
            advanceGroupBasicConfig.settingId = settingId;
            CoreDbCalls.GetInstance().UpdateAdvanceConfigs(settingId, advanceGroupBasicConfig.advanceConfigs, adminUser.userid);
            var validator = WebUtils.GetValidatorFromTemplateId(user, this, HttpContext);
            var data = new System.Text.StringBuilder();
            data.Append("i2e1").Append(";").Append("Free_Wi-Fi").Append(",");
            string oldValue = ClientDatabaseRequest.SaveListChecks(user.backEndNasid, ListCheckDataType.SSID, data.ToString());
            data = new System.Text.StringBuilder();
            data.Append("i2e1").Append(";").Append("").Append(",");
            oldValue = ClientDatabaseRequest.SaveListChecks(user.backEndNasid, ListCheckDataType.SSID_PASSWORD, data.ToString());
            LoginResponse loginResponse = LoginUtils.getRouterLoginUrl(user, LoginUtils.generateCHAP(user.challenge, user.otp), validator.GetLandingPage(user, "autologin"));

            CoreDbCalls.GetInstance().AddWiomSubscription(user.backEndNasid, 10002, 0);
            ShardHelper.ResetDeviceCache(inventory.mac, inventory.deviceId, inventory.nasid);
            ViewBag.name = user.name;
            return new JsonResult(new JsonResponse(ResponseStatus.SUCCESS, "SUCCESS", loginResponse));
        }
        else
        {
            if (user.otp != savedOtp)
                return new JsonResult(new JsonResponse(ResponseStatus.FAILURE, "ERROR_OTP_INCORRECT", null));
            else
                return new JsonResult(new JsonResponse(ResponseStatus.FAILURE, "ERROR_ALREADY_REGISTERED", null));
        }
    }

    [HttpPost]
    [CoreModelBinder]
    public JsonResult SubmitDIYOTP(LongIdInfo nasid, string mobile, string otp)
    {

        //LongIdInfo.TryParse(nasid, out int nas);

        var diyRegistrationKey = CookieUtils.GetCookie(HttpContext,"diy-reg-key");
        var savedOtp = CacheHelper.GetInstance().GetDIYToken(nasid, mobile);

        if (otp == savedOtp)
        {
            diyRegistrationKey = Guid.NewGuid().ToString();
            CookieUtils.SetCookie(HttpContext,"diy-reg-key", diyRegistrationKey, false, DateTime.UtcNow.AddMinutes(360));
            return new JsonResult(new JsonResponse(ResponseStatus.SUCCESS, null));
            
        }
        return new JsonResult(new JsonResponse(ResponseStatus.FAILURE, "Invalid OTP", null));
        
    }


    [HttpPost]
    [RequestSizeLimit(10000000)]
    public ActionResult BulkUploadPortalEntry(IFormFile csv)
    {
        string message = "Successfully Updated Data, changes will reflect after 10 minutes";
        try
        {
            if (csv.Length > 0)
            {
                using (StreamReader streamReader = new StreamReader(csv.OpenReadStream()))
                {
                    CultureInfo cultureInfo = System.Globalization.CultureInfo.CurrentCulture;
                    CsvConfiguration configuration = new CsvConfiguration(cultureInfo);
                    CsvReader csvread = new CsvReader(streamReader, configuration);
                    var records = csvread.GetRecords<PortalCsv>().ToList();
                    if (records.Count > MAX_RECORDS)
                    {
                        message = "Too Many Records, Maximum records allowed is "+MAX_RECORDS;
                    }
                    else
                    {
                        foreach (var item in records)
                        {
                            var clientName = item.clientName;
                            var partnerName = item.partnerName;
                            var nasid = item.nasid;
                            var details = DbCalls.GetInstance().VerifyClientAndPartner(clientName, partnerName);
                            if (details != null)
                            {
                                string marketplacename = DbCalls.GetInstance().getMarketPlaceName(item.marketplaceid);
                                string storename = $"{item.brandname},{marketplacename}";
                                DbCalls.GetInstance().UpdateLocation(item, details.Item1, details.Item2, storename);
                            }
                        }
                    }
                    
                }
            }
            else
            {
                Logger.GetInstance().Info("empty file for portal upload" + csv.Length);
                message = "Empty file";
            }
        }
        catch (Exception e)
        {
            Logger.GetInstance().Error(e.StackTrace);
            message = "invalid file";
        }
        ViewBag.message = message;
        return ReturnConfigsForPage(null, init, scale);
    }

    // check once
    [HttpPost]
    [RequestSizeLimit(10000000)]
    public ActionResult BulkUploadAuthEntry(IFormFile csv)
    {
        string message = "Successfully Updated Data, changes will reflect after 10 minutes";
        try
        {
            if (csv.Length > 0)
            {
                using (StreamReader streamReader = new StreamReader(csv.OpenReadStream()))
                {
                    CultureInfo cultureInfo = System.Globalization.CultureInfo.CurrentCulture;
                    CsvConfiguration configuration = new CsvConfiguration(cultureInfo);
                    CsvReader csvread = new CsvReader(streamReader, configuration);
                    var records = csvread.GetRecords<AuthCSVData>().ToList();
                    if (records.Count > MAX_RECORDS)
                    {
                        message = "Too Many Records, Maximum records allowed is "+MAX_RECORDS;
                    }
                    else
                    {
                        foreach (var record in records)
                        {
                            ClientDatabaseRequest.SaveSingleNasOperation(LongIdInfo.IdParser(record.nasid), 17, record.authGroup.ToString());
                        }
                    }
                    
                }
            }
            else
            {
                message = "Empty file";
            }
        }
        catch (Exception e)
        {
            Logger.GetInstance().Error(e.StackTrace);
            message = "invalid file";
        }

        ViewBag.message = message;
        return ReturnConfigsForPage(null, init, scale);
    }

    //check once
    [HttpPost]
    [RequestSizeLimit(10000000)]
    public ActionResult BulkUploadVipEntry(IFormFile csv)
    {
        string message = "Successfully Updated Data, changes will reflect after 10 minutes";
        try
        {
            if (csv.Length > 0)
            {
                using (StreamReader streamReader = new StreamReader(csv.OpenReadStream()))
                {
                    CultureInfo cultureInfo = System.Globalization.CultureInfo.CurrentCulture;
                    CsvConfiguration configuration = new CsvConfiguration(cultureInfo);
                    CsvReader csvread = new CsvReader(streamReader, configuration);
                    var records = csvread.GetRecords<VipCSVData>().ToList();


                    var groupedNases = records.GroupBy(c => c.nasid);
                    if (groupedNases.Count() > MAX_RECORDS)
                    {
                        message = "Too Many Records, Maximum records allowed is " + MAX_RECORDS;
                    }
                    else
                    {
                        foreach (var group in groupedNases)
                        {
                            var data = new System.Text.StringBuilder();
                            LongIdInfo nasid = group.Key;
                            foreach (var record in group)
                            {
                                nasid = record.nasid;
                                data.Append(record.mobile).Append(";").Append(record.name).Append(",");
                            }
                            string existingList = CoreDbCalls.GetInstance().GetParameterForNasByType(nasid, (wiom_router_api.Models.ListCheckDataType)ListCheckDataType.VIP_LIST) + data.ToString();
                            //ClientDatabaseRequest.SaveListChecks(nasid, ListCheckDataType.VIP_LIST, existingList);
                        }
                    }
                    
                }
            }
            else
            {
                message = "Empty file";
            }
        }
        catch (Exception e)
        {
            Logger.GetInstance().Error(e.Message);
            message = "invalid file";
        }
        ViewBag.message = message;
        return ReturnConfigsForPage(null, init, scale);
    }

    // check once
    [HttpPost]
    [RequestSizeLimit(10000000)]
    public ActionResult BulkUploadNasIDs(IFormFile csv)
    {
        string message = "Successfully Updated Data, changes will reflect after 10 minutes";
        try
        {
            if (csv.Length > 0)
            {
                using (StreamReader streamReader = new StreamReader(csv.OpenReadStream()))
                {
                    CultureInfo cultureInfo = System.Globalization.CultureInfo.CurrentCulture;
                    CsvConfiguration configuration = new CsvConfiguration(cultureInfo);
                    CsvReader csvread = new CsvReader(streamReader, configuration);
                    var records = csvread.GetRecords<NasCSVData>().ToList();

                    if (records.Count() > MAX_RECORDS)
                    {
                        message = "Too Many Records, Maximum records allowed is " + MAX_RECORDS;
                    }
                    else
                    {
                        var nasrecords = records.Select(c => c.nasid).ToList();
                        foreach (var record in nasrecords)
                        {
                            DbCalls.GetInstance().CheckAndMakeEntry(LongIdInfo.IdParser(record), 1);
                        }
                    }
                    
                }
            }
            else
            {
                message = "Empty file";
            }
        }
        catch (Exception e)
        {
            Logger.GetInstance().Error(e.Message);
            message = "invalid file";
        }
        ViewBag.message = message;
        return ReturnConfigsForPage(null, init, scale);
    }

    [HttpPost]
    [RequestSizeLimit(10000000)]
    // check once
    public ActionResult BulkConfigureSecondaryNasIDs(IFormFile csv)
    {
        string message = "Successfully Updated Data, changes will reflect after 10 minutes";
        try
        {
            if (csv.Length > 0)
            {
                using (StreamReader streamReader = new StreamReader(csv.OpenReadStream()))
                {
                    CultureInfo cultureInfo = System.Globalization.CultureInfo.CurrentCulture;
                    CsvConfiguration configuration = new CsvConfiguration(cultureInfo);
                    CsvReader csvread = new CsvReader(streamReader, configuration);
                    var records = csvread.GetRecords<SecondaryNasCSVData>().ToList();

                    if (records.Count() > MAX_RECORDS)
                    {
                        message = "Too Many Records, Maximum records allowed is " + MAX_RECORDS;
                    }
                    else
                    {
                        foreach (var record in records)
                        {
                            var mappings=DbCalls.GetInstance().MapNasWithSecondaryNas(LongIdInfo.IdParser(record.nasid), LongIdInfo.IdParser(record.secondarynasid));
                            foreach(var mapping in mappings)
                            {
                                CoreCacheHelper.GetInstance().Reset(CoreCacheHelper.NAS_FROM_MACV2, mapping.macid.ToUpper());
                            }
                            DbCalls.GetInstance().CheckAndMakeEntry(LongIdInfo.IdParser(record.secondarynasid), 1);
                        }
                    }
                    
                }
            }
            else
            {
                message = "Empty file";
            }
        }
        catch (Exception e)
        {
            Logger.GetInstance().Error(e.Message);
            message = "invalid file";
        }
        ViewBag.message = message;
        return ReturnConfigsForPage(null, init, scale);
    }
    // check once
    [HttpPost]
    [RequestSizeLimit(10000000)]
    public ActionResult BulkUploadStoreContact(IFormFile csv)
    {
        string message = "Successfully Updated Data, changes will reflect after 10 minutes";
        try
        {
            if (csv.Length > 0)
            {
                using (StreamReader streamReader = new StreamReader(csv.OpenReadStream()))
                {
                    CultureInfo cultureInfo = System.Globalization.CultureInfo.CurrentCulture;
                    CsvConfiguration configuration = new CsvConfiguration(cultureInfo);
                    CsvReader csvread = new CsvReader(streamReader, configuration);
                    var records = csvread.GetRecords<StoreContactCSVData>().ToList();

                    if (records.Count() > MAX_RECORDS)
                    {
                        message = "Too Many Records, Maximum records allowed is " + MAX_RECORDS;
                    }
                    else
                    {
                        foreach (var record in records)
                        {
                            var longId = ClientDatabaseRequest.checkIfContactExists(record.email);
                            if (longId != null)
                            {
                                StoreUser user = new StoreUser();
                                user.active = 0;
                                user.name = record.name;
                                user.mobile = record.mobile;
                                user.username = record.email;
                                user.userType = AdminUserType.STANDARD;
                               // var storeUser = ClientController.UpdateContactPersons(user, record.nasid, true, "");
                                //string saltId = ClientController.UpdateSaltForUser(storeUser, user);
                            }
                        }
                    }
                    
                }
            }
            else
            {
                message = "Empty file";
            }
        }
        catch (Exception e)
        {
            Logger.GetInstance().Error(e.Message);
            message = "invalid file";
        }
        ViewBag.message = message;
        return ReturnConfigsForPage(null, init, scale);
    }

    public JsonResult RunSpeedTest(LongIdInfo nasid)
    {
        var date = DateTime.UtcNow;
        string operationId = $"{date.Month}{date.Day}{date.Hour}{date.Minute}";
        using (var client = new ImpatientWebClient())
        {
            client.DownloadString($"http://remote.i2e1.in/Remote/SendSpeedTestCommand.php?nasid={nasid.ToSafeDbObject(1)}&operationId={operationId}");
        }
        return new JsonResult(new JsonResponse(ResponseStatus.SUCCESS, new { operationId = operationId }));
    }

    public JsonResult GetSpeedtestResults(LongIdInfo nasid, int operationId)
    {
        var data = CoreDbCalls.GetInstance().GetSpeedtestResults(nasid, operationId);
        return new JsonResult(new JsonResponse(ResponseStatus.SUCCESS, data));
    }
}
