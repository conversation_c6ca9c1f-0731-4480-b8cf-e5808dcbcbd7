using i2e1_basics.Database;
using i2e1_basics.Models;
using i2e1_basics.Utilities;
using i2e1_core.Attributes;
using i2e1_core.Models;
using i2e1_core.Models.Client;
using i2e1_core.Models.Payment;
using i2e1_core.services;
using i2e1_core.Utilities;
using I2E1_WEB.Database;
using I2E1_WEB.Models;
using I2E1_WEB.Utilities;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json.Linq;
using Razorpay.Api;
using System;
using System.Collections.Generic;
using System.Linq;
using wiom_login_share.Models;
using wiom_router_api;
using wiom_router_api.Models;
using wiom_routerplan_share.Models.RouterPlan;
using Constants = i2e1_core.Utilities.Constants;

namespace I2E1_WEB.Controllers;

public class WiomLocation
{
    public Store store { get; set; }
    public StoreDetailsStatic details { get; set; }
}

public class WiomController : Controller
{
    internal static string GetStringSha256Hash(string text)
    {
        if (String.IsNullOrEmpty(text))
            return String.Empty;

        using (var sha = new System.Security.Cryptography.SHA256Managed())
        {
            byte[] textData = System.Text.Encoding.UTF8.GetBytes(text);
            byte[] hash = sha.ComputeHash(textData);
            return BitConverter.ToString(hash).Replace("-", String.Empty);
        }
    }

    public JsonResult getNasDetails(LongIdInfo nasid)
    {
        if (nasid != null)
            return new JsonResult(new JsonResponse(ResponseStatus.SUCCESS, "", ClientDatabaseRequest.getNasDetails(nasid)));
        else
            return new JsonResult(new JsonResponse(ResponseStatus.FAILURE, "", new Dictionary<string, string>()));
    }

    public JsonResult getUserLocations(LongIdInfo userId)
    {
        
        
        ManagementUser user = new ManagementUser();
        user.userid = userId;
        return new JsonResult(new JsonResponse(ResponseStatus.SUCCESS, "", ClientDatabaseRequest.getUsersMappedLocations(user)));
        
    }


    public JsonResult updateUserLocation(int userId, [FromBody] WiomLocation location)
    {
        
        return new JsonResult(new JsonResponse(ResponseStatus.SUCCESS, "", ClientDatabaseRequest.updateUserLocation(location.store, location.details)));
        
    }



    public JsonResult getUserProfile(LongIdInfo nasid, string classification)
    {
        
        
        return new JsonResult(new JsonResponse(ResponseStatus.SUCCESS, "", ClientDatabaseRequest.getAdminProfile(nasid, classification)));
        
    }

    public JsonResult getownerprofile(LongIdInfo userid)
    {
        
        
        return new JsonResult(new JsonResponse(ResponseStatus.SUCCESS, "", ClientDatabaseRequest.getOwnerProfile(userid)));
        
    }

    public JsonResult updateUserProfile(LongIdInfo userId, [FromBody]UserProfile profile)
    {
        
        
        var mUser = JwtObject.GetManagementUser(HttpContext);
        StoreSearchQuery query = new StoreSearchQuery()
        {
            pageNumber = 1,
            pageSize = 500,
            installedState = "1"
        };
        List<StoreInfo> stores = AdminDatabaseRequest.GetLocationsForAdmin(mUser, query);
        StoreUser user = new StoreUser()
        {
            userId = profile.id,
            mobile = profile.mobile,
            name = profile.name,
            email = profile.email,
            username = profile.username,
            active = 1
        };
        foreach (var store in stores)
        {
            var storeUser = ClientDatabaseRequest.UpdateContactPersons(user, store.nasid, "location", string.Empty, "owner");
            string saltId = null;
            if (storeUser != null)
            {
                DateTime myDateTime = DateTime.UtcNow;
                string sqlFormattedDate = myDateTime.ToString("yyyy-MM-dd HH:mm:ss.fff");
                saltId = GetStringSha256Hash(user.username + sqlFormattedDate);
                var update = ClientDatabaseRequest.updateSaltId(user.userId, saltId, sqlFormattedDate);
                if (update)
                {
                    WebUtils.LogInfoToCosmos("User added and salt updated for user: " + user.username);
                }

            }
        }
        return new JsonResult(new JsonResponse(ResponseStatus.SUCCESS, "", ClientDatabaseRequest.updateAdminProfile(userId, profile)));
        
    }

    [AuthorizeClient]
    public JsonResult GetLocationsForAdmin(StoreSearchQuery query)
    {
        var mUser = JwtObject.GetManagementUser(HttpContext);
        return new JsonResult(new JsonResponse(ResponseStatus.SUCCESS, "", AdminDatabaseRequest.GetLocationsForAdmin(mUser, query)));
    }

    public JsonResult getStoreDetails(LongIdInfo nasid)
    {
        var details = AdminDatabaseRequest.GetStoreDetailsStatic(nasid);
        return new JsonResult(new JsonResponse(ResponseStatus.SUCCESS, "", details));
    }

    public JsonResult getStoreOwnerDetails(LongIdInfo nasid)
    {
        
        
        var details = AdminDatabaseRequest.GetStoreOwnerDetails(nasid);
        return new JsonResult(new JsonResponse(ResponseStatus.SUCCESS, "", details));
        
    }

    public JsonResult GetDeviceStats(List<LongIdInfo> nases, string days, string minutes)
    {
        
        try
        {
            var usersInXDays = new Dictionary<long, long>();
            Dictionary<long, double> dataUsed = new Dictionary<long, double>();
            var lastLoginTime = new Dictionary<long, string>();
            var connectionStatus = ClientDatabaseRequest.GetConnectionStatusForNases(nases);
            if (!string.IsNullOrEmpty(minutes))
            {
                DateTime startTime = DateTime.UtcNow.AddMinutes(-1 * Convert.ToInt32(minutes));
                var dict = RadAcctDbCalls.GetUsersInDuration(nases, startTime);
                foreach(var pair in dict)
                {
                    usersInXDays.Add(pair.Key, (long)pair.Value["userCount"]);
                    dataUsed.Add(pair.Key, (long)pair.Value["dataUsed"]);
                    lastLoginTime.Add(pair.Key, pair.Value["lastUsed"].ToString());
                }
            }
            else
            {
                DateTime startTime = DateTime.UtcNow.AddDays(-1 * Convert.ToInt32(days));
                var dict = RadAcctDbCalls.GetUsersInDuration(nases, startTime);
                foreach (var pair in dict)
                {
                    usersInXDays.Add(pair.Key, (long)pair.Value["userCount"]);
                    dataUsed.Add(pair.Key, (long)pair.Value["dataUsed"]);
                    lastLoginTime.Add(pair.Key, pair.Value["lastUsed"].ToString());
                }
            }
            return new JsonResult(new JsonResponse(ResponseStatus.SUCCESS, "",
                new
                {
                    lastLoginTime,
                    usersInXDays,
                    dataUsed,
                    connectionStatus
                }));

        }
        catch (Exception e)
        {
            return new JsonResult(new JsonResponse(ResponseStatus.FAILURE, "Error retriving data"));
        }

        
        
    }

    public JsonResult LoginWeb(string username, string password, AdminAuthType authType = AdminAuthType.CUSTOM)
    {

        var doLoginResponse = doLogin(username, password, authType);

        if (doLoginResponse["response"].ToString() == i2e1_core.Utilities.Constants.ADMIN_USER)
        {
            return new JsonResult(new { message = "success", user = doLoginResponse["user"] });

        }
        else
        {
            return new JsonResult(new { message = "failure" });
        }


    }


    public JsonResult GetDataUsage(LongIdInfo nasid, DateTime startTime, DateTime endTime)
    {
        TimeUtils.normalizeTime(ref startTime, ref endTime);
        var response = RadAcctDbCalls.GetDataUsage(HttpContext.Items["ShowNumber"] == null || (bool)HttpContext.Items["ShowNumber"] == true, nasid, startTime, endTime);
        return new JsonResult(new JsonResponse(ResponseStatus.SUCCESS, "", response));
    }

    private Dictionary<string, object> doLogin(string username, string password, AdminAuthType authType = AdminAuthType.CUSTOM)
    {
        bool success = false;
        var longId = ShardHelper.getLongUserIdFromMobile(username);
        ManagementUser user = new ManagementUser()
        {
            email = username,
            password = password,
            authType = authType,
            is_password_temporary = false
        };

        user = CoreUserService.CheckAdmin(longId.shard_id, user);
        Dictionary<string, object> res = new Dictionary<string, object>();
        if (user != null)
        {
            success = true;
        }

        if (success)
        {
            user.features = AdminDatabaseRequest.GetFeatureList(user.userid, user.userType);
            JWTManager.CreateJwtToken(null, null, user, HttpContext);
            res["response"] = Constants.ADMIN_USER;
        }
        else
        {
            JWTManager.expireJwtToken(HttpContext);
            res["response"] = Constants.INVALID_LOGIN;
        }
        res["user"] = user;
        res["success"] = success;
        return res;
    }

    public JsonResult getSubscription(LongIdInfo nasid)
    {
        return new JsonResult(new JsonResponse(ResponseStatus.SUCCESS, "", ClientDatabaseRequest.getActiveSubscription(nasid)));
    }

    public JsonResult getPlan(int pid)
    {


        if (pid == 0)
        {
            List<PlanDetail> plan = DbCalls.GetInstance().getPlanDetail();
            return new JsonResult(new JsonResponse(ResponseStatus.SUCCESS, plan));
        }
        else
        {
            PlanDetail userPlan = CoreDbCalls.GetInstance().getPlanDetailById(pid);
            return new JsonResult(new JsonResponse(ResponseStatus.SUCCESS, userPlan));
        }

    }

    // check once
    public JsonResult GetTransactionStatus(long shard_id,string transactionID)
    {
        MPaymentHistory payHist = CoreDbCalls.GetInstance().GetPaymentDetails(transactionID);
        return new JsonResult(new JsonResponse(ResponseStatus.SUCCESS, new List<MPaymentHistory>() { payHist }));
    }

    public JsonResult UpdatePassword(string username, string oldPassword, string newPassword)
    {
        try
        {
            var status = DbCalls.GetInstance().UpdatePassword(username, oldPassword, newPassword, false);
            return new JsonResult(new JsonResponse(ResponseStatus.SUCCESS, "", status));

        }
        catch (Exception ex)
        {
            return new JsonResult(new JsonResponse(ResponseStatus.FAILURE, "Error Updating Password", false));

        }
    }

    public JsonResult RecoverUsername(LongIdInfo nasid)
    {
        try
        {
            var details = AdminDatabaseRequest.GetOwnerDetails(nasid);
            if (string.IsNullOrEmpty(details.username))
                return new JsonResult(new JsonResponse(ResponseStatus.FAILURE, "ERROR", "Incorrect Device Id"));
            else
            {
                string message = String.Format("Use {0} as user id to login to WIOM admin portal. https://admin.wiom.in", details.username);
                CoreSmsSender.SendGupshupSmsWiomRecoverUsername(details.username);
                return new JsonResult(new JsonResponse(ResponseStatus.SUCCESS, "SUCCESS", "XXXXX-X" + details.username.Substring(6, 4)));
            }

        }
        catch (Exception ex)
        {
            return new JsonResult(new JsonResponse(ResponseStatus.FAILURE, "ERROR", "Try Again."));

        }
    }

    internal static string GeneratePassword(int stringLength)
    {
        Random rd = new Random();
        const string allowedChars = "abcdefghijkmnopqrstuvwxyz0123456789";
        char[] chars = new char[stringLength];
        for (int i = 0; i < stringLength; i++)
        {
            chars[i] = allowedChars[rd.Next(0, allowedChars.Length)];
        }
        return new string(chars);
    }

    public async System.Threading.Tasks.Task<JsonResult> GenerateCertificate(LongIdInfo nasid)
    {
        if (nasid != null)
        {
            var details = ClientDatabaseRequest.getNasDetails(nasid);
            if (details.ContainsKey("macId"))
            {
                details.Add("date", DateTime.Now.ToString("dd/MM/yyyy").Replace('-', '/'));
                var result = await CoreUtil.ExecuteLambdaFunction("pdo-certificates", "ap-south-1", details);
                return new JsonResult(new JsonResponse(ResponseStatus.SUCCESS, "", result));
            }
            else
            {
                return new JsonResult(new JsonResponse(ResponseStatus.FAILURE, "", null));
            }
        }
        else
            return new JsonResult(new JsonResponse(ResponseStatus.FAILURE, "", null));
    }

    public JsonResult GetNasidFromMac(string mac)
    {
        var nasDetails = RouterApiClient.GetInstance().GetNasDetailsFromMacAsync(mac).Result;
        LongIdInfo nasid = nasDetails.nasid;

        if (nasid != null)
        {
            return new JsonResult(new JsonResponse(ResponseStatus.SUCCESS, "", nasid));
        }
        else
        {
            return new JsonResult(new JsonResponse(ResponseStatus.FAILURE, "", null));
        }
    }

    public JsonResult RecoverPassword(string username, bool resend = false)
    {
        try
        {
            var details = AdminDatabaseRequest.GetOwnerDetails(username);
            if (resend == true)
            {
                string message = String.Format("Use {0} as password to login to WIOM admin portal. https://admin.wiom.in", details.password);
                string senderId = "myWIOM";
                //SmsSender.SendSMSViaSolutionInfiniV4(message, details.username, senderId);
                CoreSmsSender.SendGupshupSmsWiomRecoverPassword(username, details.password);
                return new JsonResult(new JsonResponse(ResponseStatus.SUCCESS, "SUCCESS", "XXXXX-X" + details.username.Substring(6, 4)));

            }
            if (string.IsNullOrEmpty(details.password))
                return new JsonResult(new JsonResponse(ResponseStatus.FAILURE, "ERROR", "Incorrect Username"));
            else
            {
                string newPassword = GeneratePassword(6);
                DbCalls.GetInstance().UpdatePassword(username, details.password, newPassword);
                string message = String.Format("Use {0} as password to login to WIOM admin portal. https://admin.wiom.in", newPassword);
                string senderId = "myWIOM";
                //SmsSender.SendSMSViaSolutionInfiniV4(message, details.username, senderId);
                CoreSmsSender.SendGupshupSmsWiomRecoverPassword(details.username, newPassword);
                return new JsonResult(new JsonResponse(ResponseStatus.SUCCESS, "SUCCESS", "XXXXX-X" + details.username.Substring(6, 4)));
            }

        }
        catch (Exception ex)
        {
            return new JsonResult(new JsonResponse(ResponseStatus.FAILURE, "ERROR", "Try Again."));

        }
    }
    [CoreModelBinder]
    public ActionResult JusPayResponse(string order_id, int status_id, string mandate_status = "None", string mandate_id = "None", string mandate_token = "None")
    {
        try
        {
            return Redirect(WebUtils.GetWiomNetServerUrl()
                + $"/Payment/JusPayResponse?order_id={order_id}&status_id={status_id}&mandate_status={mandate_status}&mandate_id={mandate_id}&mandate_token={mandate_token}");
        }
        catch (Exception ex)
        {
            Logger.GetInstance().Error(ex.ToString());
        }
        return null;
    }
    public ActionResult RazorPayResponse(string transactionId, string mobile, long planId, int nasid = 0, int planType = 0, int pg=0)
    {
        try
        {
            ViewBag.planId = planId;
            ViewBag.transactionId = transactionId;
            ViewBag.mobile = mobile;
            ViewBag.nasid = nasid;
            ViewBag.pg = pg;

            if (planId == 10001)
                ViewBag.plan = "WIOM Basic";
            else if (planId == 10002)
                ViewBag.plan = "Magic";
            else if (planId == 10003)
                ViewBag.plan = "WIOM PDO";
            else
                ViewBag.plan = "";

            ViewBag.planType = planType;
            if (planType == 0 || planType == 2)
                return View("~/Views/RazorPay/rajor_response.cshtml");
            else if (planType == 1)
            {
                return View("~/Views/RazorPay/razor_wifi_response.cshtml");
            }

        }
        catch (Exception ex)
        {
            Logger.GetInstance().Error(ex.ToString());
        }
        return null;
    }
    public ActionResult PaymentPage()
    {
        return View("~/Views/RazorPay/payment_page.cshtml");
    }

    [AdminPortalAuthorization]
    public JsonResult GetRegisteredUsers(LongIdInfo nasid)
    {
        /*var storeGroupId = CoreDbCalls.GetInstance().GetStoreGroupAndIsAccessCodeApplied(nasid).Key;
        if(storeGroupId == 1)
            return new JsonResult(new JsonResponse(ResponseStatus.SUCCESS, AdminDatabaseRequest.GetAllRegisteredSecondaryUsers(nasid, DateTime.UtcNow.AddDays(-30), DateTime.UtcNow)));*/
        return new JsonResult(new JsonResponse(ResponseStatus.SUCCESS, AdminDatabaseRequest.GetAllRegisteredHomeUsers(nasid, DateTime.UtcNow.AddDays(-30), DateTime.UtcNow)));
    }

    [AdminPortalAuthorization]
    [CoreModelBinder]
    public JsonResult CreateUser(LongIdInfo nasid, SecondaryRouterPlan voucher, string seconds)
    {
        var storeGroupId = CoreDbCalls.GetInstance().GetStoreGroupAndIsAccessCodeApplied(nasid).Key;
        var otp = Guid.NewGuid().ToString();
        voucher.otp = otp.Substring(otp.Length - 6);
        long secs = long.Parse(seconds);
        voucher.planEndTime = DateTime.UtcNow.AddSeconds(secs);
        CoreDbCalls.GetInstance().GenerateUser(voucher, nasid, 1, true, 0);
        var result = new JsonResult(new JsonResponse(ResponseStatus.SUCCESS, "coupon generated", new
        {
            coupon_code = voucher.otp
        }));
        return result;
    }

    [AdminPortalAuthorization]
    [CoreModelBinder]
    public JsonResult AuthorizeUser(LongIdInfo nasid, string mobile)
    {
        var storeGroupId = CoreDbCalls.GetInstance().GetStoreGroupAndIsAccessCodeApplied(nasid).Key;
        return new JsonResult(new JsonResponse(ResponseStatus.SUCCESS, AdminDatabaseRequest.AuthorizeUser(nasid, storeGroupId, mobile)));
    }

    [AdminPortalAuthorization]
    [CoreModelBinder]
    public JsonResult UpdateUser(LongIdInfo nasid, SecondaryRouterPlan user, string action)
    {
        var routerBasic = CoreCacheHelper.GetInstance().GetRouterBasic(nasid);
        if (action == "disconnected")
        {
            DatabaseRequest.LogoutUser(nasid, routerBasic.storeGroupId, user.mobile, routerBasic.isHomeRouter);
        }
        else
        {
            user.authState = false;
        }
        /*if (user.extraDataObject != null && user.extraDataObject.state == "disconnected")
        {
            var admin = JwtObject.GetManagementUser(HttpContext);
            Logger.GetInstance().Info($"User Disconnected: {user.mobile} By {admin.userid}");
        }*/
        user.nasId = nasid;
        /*user.storeGroupId = routerBasic.storeGroupId;*/
        CoreFDMService.UpdateUserPolicy(user);
        return new JsonResult(new JsonResponse(ResponseStatus.SUCCESS, true));
    }

    [AdminPortalAuthorization]
    [CoreModelBinder]
    public JsonResult UpgradeUserPolicy(LongIdInfo nasid, long planId, long fdmId)
    {
        var plans = CoreCacheHelper.GetInstance().GetActivePlansOnNas(nasid);
        if (plans != null || plans.Count == 0)
        {
            var plan = plans.Find(m => m.id == planId);
            if (plan == null)
            {
                return new JsonResult(new JsonResponse(ResponseStatus.FAILURE, "No such plan found"));
            }
            var storeGroupId = CoreDbCalls.GetInstance().GetStoreGroupAndIsAccessCodeApplied(nasid).Key;

            /*var pUser = new PassportUser()
            {
                dataPlan = Convert.ToInt32(plan.data_limit),
                otpExpiryTime = DateTime.UtcNow.AddSeconds(plan.time_limit),
                authState = true,
                otp = "",
                id = fdmId
            };
            pUser.nasid = nasid;
            pUser.storeGroupId = storeGroupId;*/

            var sUser = new SecondaryRouterPlan();
            var hUser = new HomeRouterPlan();
            if(storeGroupId == 1)
            {
                sUser = new SecondaryRouterPlan()
                {
                    dataLimit = Convert.ToInt32(plan.data_limit),
                    planStartTime = DateTime.UtcNow,
                    planEndTime = DateTime.UtcNow.AddSeconds(plan.time_limit),
                    otp = "",
                    entryUnixEpochTime = fdmId,
                    nasId = nasid,
                    authState = true
                };
            }
            else
            {
               hUser = new HomeRouterPlan()
                {
                    dataLimit = Convert.ToInt32(plan.data_limit),
                    planStartTime = DateTime.UtcNow,
                    planEndTime = DateTime.UtcNow.AddSeconds(plan.time_limit),
                    otp = "",
                    entryUnixEpochTime = fdmId,
                    nasId = nasid,
                };
            }
            if(storeGroupId == 1)
            {
                CoreFDMService.UpdateUserPolicy(sUser);
            }
            else
            {
                CoreFDMService.UpdateUserPolicy(hUser);
            }
            return new JsonResult(new JsonResponse(ResponseStatus.SUCCESS, true));
        }
        else
        {
            return new JsonResult(new JsonResponse(ResponseStatus.FAILURE, "Plan does not exist"));
        }
    }

    [AdminPortalAuthorization]
    public JsonResult GetPlans(LongIdInfo nasid)
    {
        var combinedSettingId = CoreCacheHelper.GetInstance().GetRouterBasic(nasid).combinedSettingId;
        var plans = CoreDbCalls.GetInstance().GetPlanConfigInCombinedSetting(combinedSettingId);
        return new JsonResult(new JsonResponse(ResponseStatus.SUCCESS, plans));
    }

    public JsonResult GetPlansOnNas(LongIdInfo nasid, bool getDiscountedPlans = false)
    {            
        var plans = CoreCacheHelper.GetInstance().GetActivePlansOnNas(nasid);
        if(!getDiscountedPlans)
        {
            plans = plans.Where(m => m.discount == 0).ToList();
        }
        return new JsonResult(new JsonResponse(ResponseStatus.SUCCESS, plans));
    }

    //[AdminPortalAuthorization]
    [CoreModelBinder]
    public JsonResult SaveSingleNasOperation(LongIdInfo nasid, int operationId, string value)
    {
        var opr = (ListCheckDataType)operationId;
        switch (opr)
        {
            case ListCheckDataType.SSID:
                BasicSQSHelper.SendFIFOMessage(BasicSQSHelper.GetQueueARN("remote.fifo"), nasid.ToString(), new i2e1_basics.Utilities.MicroServiceMessage()
                {
                    key = "UPDATE_SSID",
                    payload = new JObject() {
                                    { "nasid", nasid.GetLongId() },
                                    { "ssid", value }
                                }
                });
                break;
            case ListCheckDataType.SSID_PASSWORD:
                BasicSQSHelper.SendFIFOMessage(BasicSQSHelper.GetQueueARN("remote.fifo"), nasid.ToString(), new i2e1_basics.Utilities.MicroServiceMessage()
                {
                    key = "UPDATE_PASSWORD",
                    payload = new JObject() {
                                    { "nasid", nasid.GetLongId() },
                                    { "password", value }
                                }
                });
                break;
        }

        return new JsonResult(new JsonResponse(ResponseStatus.SUCCESS, ClientDatabaseRequest.SaveSingleNasOperation(nasid, operationId, value)));
    }

    //[AdminPortalAuthorization]
    public JsonResult GetSingleNasOperation(LongIdInfo nasid, int operationId)
    {
        var result = new JsonResult(new JsonResponse(ResponseStatus.SUCCESS, "", CoreDbCalls.GetInstance().GetSingleNasOperation(nasid, operationId)));
        return result;
    }

    [AdminPortalAuthorization]
    [HttpGet]
    public JsonResult GetVAPIDPUBLICKEY()
    {
        string key = "BJvGOeFuUlWe0MiVTf0iXnJ0vrtvsYmjhBLICa8sTRPWHIBcAr2q0ADzsP0DLQCdfXFw0YIpF06XJUBaeEIhBiA";
        return new JsonResult(new JsonResponse(ResponseStatus.SUCCESS, key));
    }

    [AdminPortalAuthorization]
    [CoreModelBinder]
    public JsonResult LogoutAuthorizedUser(LongIdInfo nasid, string mobile)
    {

        var storeGroupId = CoreDbCalls.GetInstance().GetStoreGroupAndIsAccessCodeApplied(nasid).Key;
        return new JsonResult(new JsonResponse(ResponseStatus.SUCCESS, AdminDatabaseRequest.LogoutRegisteredUser(nasid, storeGroupId, mobile)));

    }
    [HttpGet]
    public List<Plan> RazorpayAllPlan()
    {
        string key = I2e1ConfigurationManager.GetInstance().GetSetting("Razorpay.i2e1.key");
        string secret = I2e1ConfigurationManager.GetInstance().GetSetting("Razorpay.i2e1.secret");
        RazorpayClient client = new RazorpayClient(key, secret);
        //Dictionary<string, object> options = new Dictionary<string, object>();
        //options.Add("amount", amount);
        //options.Add("receipt", "rcptid_" + nasid + "_" + DateTime.UtcNow.Ticks);
        //options.Add("currency", "INR");

        // Order order = client.Order.Create(options);
        List<Plan> planList = new List<Plan>();
        planList = client.Plan.All();
        Console.WriteLine(planList);
        //var json = JsonSerializer.Serialize(client.Plan.All());
        return planList;

    }
    [HttpPost]
    public JsonResult RazorpayCreateSubscription(string plan_id,int total_count)
    {
        string key = I2e1ConfigurationManager.GetInstance().GetSetting("Razorpay.i2e1.key");
        string secret = I2e1ConfigurationManager.GetInstance().GetSetting("Razorpay.i2e1.secret");
        RazorpayClient client = new RazorpayClient(key, secret);
        Dictionary<string, object> input = new Dictionary<string, object>();
        input.Add("plan_id", plan_id); 
        input.Add("total_count", total_count);

        var subscriptionInfo=client.Subscription.Create(input);

        //Console.WriteLine(subscriptionInfo);
        //var json = JsonSerializer.Serialize(client.Plan.All());
        return new JsonResult(new JsonResponse(ResponseStatus.SUCCESS, "subscrptionInfo", subscriptionInfo));

    }

    public JsonResponse GetLocationInfo(double lat, double lng)
    {
        Address locationInfo = Address.GoogleReverseLocation(lat, lng);
        return new JsonResponse(ResponseStatus.SUCCESS, "", locationInfo.city);
    }
}