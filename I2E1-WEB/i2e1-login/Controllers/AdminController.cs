using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Text;
using System.Threading.Tasks;
using i2e1_basics.Models;
using i2e1_basics.Utilities;
using i2e1_core.Attributes;
using i2e1_core.Models;
using i2e1_core.Models.Client;
using i2e1_core.services;
using i2e1_core.Utilities;
using I2E1_WEB.Attributes;
using I2E1_WEB.Captcha;
using I2E1_WEB.Database;
using I2E1_WEB.Models;
using I2E1_WEB.Utilities;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.WindowsAzure.Storage;
using Microsoft.WindowsAzure.Storage.Blob;
using Newtonsoft.Json;
using wifidog_core.Models;

namespace I2E1_WEB.Controllers;

public class AdminController : Controller
{
    public ActionResult Index()
    {
        return View("~/Views/Client/login.cshtml");
    }

    [AdminPortalAuthorization]
    public JsonResult GetStatsCompressed(LongIdInfo nasid, DateTime startTime, DateTime endTime)
    {
        TimeUtils.normalizeTime(ref startTime, ref endTime);
        JsonResponse response = AdminDatabaseRequest.GetCompressedStats(nasid, startTime, endTime, HttpContext.Items["ShowNumber"] == null || (bool)HttpContext.Items["ShowNumber"]);
        return new JsonResult(response);
        
    }

    [HttpGet]
    public async Task<string> CommitUpgrade(string macid,int nasid,int controllerid,string devicetype)
    {
        CloudStorageAccount storageAccount = CloudStorageAccount.Parse(I2e1ConfigurationManager.GetInstance().GetSetting("FirmwareStorage"));
        CloudBlobClient blobClient = storageAccount.CreateCloudBlobClient();
        string val = "false";
        // Retrieve a reference to a container. 
        CloudBlobContainer container = blobClient.GetContainerReference("bin");
        string fileName = nasid + "_" + controllerid+ ".bin";
        CloudBlockBlob blockBlob = container.GetBlockBlobReference(fileName);
        bool exists = await blockBlob.ExistsAsync();
        if (exists)
        {
            blockBlob.DeleteAsync();
            val = "true";
            //below is a 16 digit random string to be used for sessionid
            StringBuilder builder = new StringBuilder();
            char ch;
            Random random = new Random((int)DateTime.Now.Ticks);
            for (int i = 0; i < 16; i++)
            {
                ch = Convert.ToChar(Convert.ToInt32(Math.Floor(26 * random.NextDouble() + 65)));
                builder.Append(ch);
            }
            string sessionid = builder.ToString();
            string mobile = nasid + "_" + controllerid;
        }
        else
        {
            val = "false";
        }
        return val;
    }
    
    [AdminPortalAuthorization]
    [HttpPost]
    public JsonResult SaveGroup([FromBody]UserGroup group)
    {
        LongIdInfo nasid = LongIdInfo.IdParser((long)HttpContext.Items["nasid"]);
        group.nasid = nasid;
        AdminDatabaseRequest.SaveGroup(group);

        
        return new JsonResult(new JsonResponse(ResponseStatus.SUCCESS, "", null));
        
    }

    [AuthorizeClient]
    public JsonResult GetAllIpsets()
    {
        var ipsets = CoreDbCalls.GetInstance().GetAllIpsets();
        return new JsonResult(new JsonResponse(ResponseStatus.SUCCESS, "", ipsets));
    }

    [AuthorizeClient]
    public JsonResult GetUsersInPastMonth(LongIdInfo nasid, DateTime startTime)
    {
        
        try
        {
            startTime = new DateTime(startTime.Year, startTime.Month, 1);
            return new JsonResult(AdminDatabaseRequest.GetUsersInPastMonth(nasid, startTime));
        }
        catch
        {
            return new JsonResult(new JsonResponse(ResponseStatus.FAILURE, "Error retriving users in past month"));
        }

        
    }


    [AuthorizeClient]
    public JsonResult GetUsersInThisDuration(LongIdInfo nasid, DateTime startTime, DateTime endTime)
    {
        
        try
        {
            TimeUtils.normalizeTime(ref startTime, ref endTime);
            return new JsonResult(RadAcctDbCalls.GetUsersInThisDuration(nasid, startTime, endTime));
        }
        catch
        {
            return new JsonResult(new JsonResponse(ResponseStatus.FAILURE, "Error retriving users in duration"));
        }
        
        
    }

    [AuthorizeClient]
    [HttpPost]
    public JsonResult GetUser([FromBody]SearchQuery query)
    {
        
        var mUser = JwtObject.GetManagementUser(HttpContext);
        query.pageSize = 50;
        StoreUser user = AdminDatabaseRequest.GetStoreUser(mUser, query);
        if (user != null)
        {
            return new JsonResult(new JsonResponse(ResponseStatus.SUCCESS, "", user));
        }
        else
        {
            return new JsonResult(new JsonResponse(ResponseStatus.SUCCESS, "", mUser));
        }
        
    }

    [HttpGet]
    public string CommitConfigBackup(string timeinseconds,int nasid=1,int controllerid=1)
    {
        return "done";
    }

    [HttpGet]
    public void ConfigRestoreStatus(int nasid, int controllerid, string deviceresponse)
    {
        if (deviceresponse.Equals("ok"))
        {
            Console.Write("all ok");
        }
        else
        {
            Console.Write("reason to fail" + deviceresponse);
        }
        //write code for router response logging after config restore
    }

    [HttpPost]
    private bool onlyAnalyticsUser([FromBody]Dictionary<string, int> features)
    {
        int value;
        if (features.Count(x => x.Value == 1) == 1
            && features.TryGetValue(Feature.ADVANCE_ANALYTICS.GetHashCode().ToString(), out value)
            && value == 1)
        {
            return true;
        }
        return false;
    }

    [AuthorizeClient]
    public JsonResult DeleteStoreController(LongIdInfo nasid, int controllerid)
    {
        
        AdminDatabaseRequest.DeleteContollerId(nasid, controllerid);
        return new JsonResult(new JsonResponse(ResponseStatus.SUCCESS, "", 1));

        
    }

    [AuthorizeClient]
    public JsonResult GetFeatureList(LongIdInfo userid)
    {
        
        
        var features = AdminDatabaseRequest.GetFeatureList(userid);
        return new JsonResult(new JsonResponse(ResponseStatus.SUCCESS, "", features));
        
    }

    [CoreModelBinder]
    public ActionResult LoginWeb(string username, string password, string token, string reply, string captcha, AdminAuthType authType = AdminAuthType.CUSTOM)
    {
        if (string.IsNullOrEmpty(token))
        {
            string errorMsg, captchaUrl;
            var state = MyCaptcha.CaptchaValidate("AdminLogin", username, captcha, out errorMsg, out captchaUrl);
            ViewBag.ErrorMessage = errorMsg;

            if (state == CaptchaState.SHOW_CAPTCHA)
            {
                ViewBag.CaptchaUrl = captchaUrl;
                ViewBag.username = username;
                return View("~/Views/Admin/index.cshtml");
            }
        }

        String doLoginResponse = doLogin(username, password, token, authType);
        ActionResult response;

        if (doLoginResponse == Constants.ADMIN_USER)
        {
            if (onlyAnalyticsUser(JwtObject.GetManagementUser(HttpContext).features))
            {
                response = Redirect("/OneInsights/Index#/overview");
            }
            else response = Redirect(string.IsNullOrEmpty(reply) ? "/AdminPortal/HomeOld#/landing/home" : reply);
        }
        else if (doLoginResponse == Constants.FRESH_ADMIN)
        {
            response = Redirect("/AdminPortal/HomeOld#/noDevice");
        }
        else
        {
            ViewBag.ErrorMessage = "Incorrect Username or Password";
            response = View("~/Views/Admin/index.cshtml");
        }

        return response;
    }
    // check once
    public ActionResult ActivateAdmin(String emailId, String userid)
    {
        ActionResult response = null;
        Boolean result = AdminDatabaseRequest.ActivateAdmin(emailId, LongIdInfo.IdParser(Int64.Parse(userid)));
        response = View("~/Views/Admin/activated.cshtml");
        return response;
    }

    //Login is getting called from admin app
    public JsonResult Login(string username, string password, string token, AdminAuthType authType = AdminAuthType.CUSTOM)
    {
        String doLoginResponse = doLogin(username, password, token, authType);
        

        

        if (doLoginResponse == Constants.ADMIN_USER)
        {
            return new JsonResult(new JsonResponse(ResponseStatus.SUCCESS, "", null));
        }
        else if (doLoginResponse == Constants.FRESH_ADMIN)
        {
            return new JsonResult(new JsonResponse(ResponseStatus.SUCCESS, "noDevice", null));
        }
        else
        {
            return new JsonResult(new JsonResponse(ResponseStatus.FAILURE, "Incorrect Username or Password", null));
        }

        
    }

    private string doLogin(string username, string password, string googleToken, AdminAuthType authType = AdminAuthType.CUSTOM)
    {
        ManagementUser user = CoreUserService.CheckAdmin(username, password, googleToken, authType);

        string response;
        if (user != null)
        {
            AdminDatabaseRequest.UpdateAdminLogin(user.userid);
            if (AdminDatabaseRequest.GetAdminRoutersCount(user) > 0)
            {
                user.features = AdminDatabaseRequest.GetFeatureList(user.userid, user.userType);
            }

            if (user.features == null)
            {
                response = Constants.FRESH_ADMIN;
            }
            else
            {
                JWTManager.CreateJwtToken(null, null, user, HttpContext);
                response = Constants.ADMIN_USER;
            }
        }
        else
        {
            HttpContext.Session.Clear();
            response = Constants.INVALID_LOGIN;
        }
        return response;
    }

    private ManagementUser VerifyGoogleUser(string googleToken)
    {
        string data = new WebClient().DownloadString("https://www.googleapis.com/oauth2/v3/tokeninfo?id_token=" + googleToken);
        ManagementUser user = JsonConvert.DeserializeObject<ManagementUser>(data);
        user.password = string.Empty;
        user.authType = AdminAuthType.GMAIL;
        user.userType = AdminUserType.STANDARD;
        user.contact_no = String.Empty;
        user.email_verified = true;

        return user;
    }

    [SuperAdminAuthorization]
    [AuthorizeClient]
    public JsonResult getAllFeatureList()
    {
        JsonResponse response;
        response = AdminDatabaseRequest.GetAllFeatureList();
        
        
        return new JsonResult(response);
        
    }

    [SuperAdminAuthorization]
    [AuthorizeClient]
    [HttpPost]
    [CoreModelBinder]
    public JsonResult SaveFeatureList(LongIdInfo userid, Dictionary<string, int> features)
    {
        JsonResponse response;
        response = CoreUserService.SaveFeatureList(userid, features);
        
        
        return new JsonResult(response);
        
    }

    [AdminPortalAuthorization]
    [HttpPost]
    public JsonResult SubmitStoreDetails([FromBody]StoreDetails store)
    {
        long router = (long)HttpContext.Items["nasid"];
        store.nasid = LongIdInfo.IdParser(router);
        bool res = AdminDatabaseRequest.SaveStoreDetails(store);
        return new JsonResult(new JsonResponse(ResponseStatus.SUCCESS, "", null));
    }

    [AuthorizeClient]
    public JsonResult GetStoreDetailsStatic(LongIdInfo nasid)
    {
        var details = AdminDatabaseRequest.GetStoreDetailsStatic(nasid);
        return new JsonResult(new JsonResponse(ResponseStatus.SUCCESS, "", details));
        
    }

    [AdminPortalAuthorization]
    [HttpPost]
    public JsonResult SubmitStoreDetailsStatic([FromBody]StoreDetailsStatic store)
    {
        long router = (long)HttpContext.Items["nasid"];
        store.nasid = LongIdInfo.IdParser(router);
        bool res = AdminDatabaseRequest.SaveStoreDetailsStatic(store);
        
        
        return new JsonResult(new JsonResponse(ResponseStatus.SUCCESS, "", null));
        
    }



    [AdminPortalAuthorization]
    public JsonResult GetDataUsage(LongIdInfo nasid, DateTime startTime, DateTime endTime)
    {
        TimeUtils.normalizeTime(ref startTime, ref endTime);
        var response = RadAcctDbCalls.GetDataUsage(HttpContext.Items["ShowNumber"] == null || (bool)HttpContext.Items["ShowNumber"] == true,
            nasid, startTime, endTime);
        
        
        return new JsonResult(new JsonResponse(ResponseStatus.SUCCESS, "", response));
        
    }

    public JsonResult Logout()
    {
        JWTManager.expireJwtToken(HttpContext);
        
        
        return new JsonResult(new JsonResponse(ResponseStatus.SUCCESS, "", null));
        
    }

    [AdminPortalAuthorization]
    public JsonResult GetBlockList(LongIdInfo nasid)
    {
        var pair = CoreCacheHelper.GetInstance().getPhoneNumberWhiteListAndBlockList(nasid);
        
        var list = pair.Value;
        string[] mobiles = new string[list.Count];
        for (int i = 0; i < list.Count; ++i)
        {
            if (IsFeatureEnabled(Feature.SHOW_PHONE_NUMBER))
                mobiles[i] = UserBaseModel.GetMaskedNumber(HttpContext.Items["ShowNumber"] == null || (bool)HttpContext.Items["ShowNumber"] == true, list[i]);
            else
                mobiles[i] = list[i];

        }
        
        
        return new JsonResult(new JsonResponse(ResponseStatus.SUCCESS, "", mobiles));
        
    }

    [AdminPortalAuthorization]
    [HttpPost]
    public JsonResult GetDetailedReport([FromBody]UserDataUsage data, DateTime startTime, DateTime endTime)
    {
        ManagementUser details = JwtObject.GetManagementUser(HttpContext);
        
        
        bool found = AdminDatabaseRequest.CheckAdminNasMapping(details.userid, details.userType, data.nasids.ToArray());
        if (!found)
        {
            return new JsonResult(new JsonResponse(ResponseStatus.FAILURE, "Unauthorized to make this call", null));
            
        }
        string mobile = data.GetDecryptedValue();
        TimeUtils.normalizeTime(ref startTime, ref endTime);
        var res = RadAcctDbCalls.GetDetailedDataReport(HttpContext.Items["ShowNumber"] == null || (bool)HttpContext.Items["ShowNumber"] == true,
            data.nasids, mobile, startTime, endTime);
        return new JsonResult(new JsonResponse(ResponseStatus.SUCCESS, "", res));
        
    }

    [AuthorizeClient]
    [HttpPost]
    // check once
    public JsonResult LogoutUser([FromBody]UserDataUsage data)
    {
        string mobile = data.GetDecryptedValue();
        if (string.IsNullOrEmpty(mobile) && data.mkey.Length == 10)
            mobile = data.mkey;
        var routerBasic = CoreCacheHelper.GetInstance().GetRouterBasic(data.nasid);
        DatabaseRequest.LogoutUser(data.nasid, routerBasic.storeGroupId, mobile, routerBasic.isHomeRouter);
        
        
        return new JsonResult(new JsonResponse(ResponseStatus.SUCCESS, "", null));
        
    }

    [AuthorizeClient]
    public JsonResult GetAllTemplates()
    {
        var list = AdminTemplateDatabaseRequest.GetAllTemplates();
        
        
        return new JsonResult(new JsonResponse(ResponseStatus.SUCCESS, "", list));
        
    }

    [AuthorizeClient]
    public JsonResult GetAllStoreGroups()
    {
        var list = AdminDatabaseRequest.GetAllStoreGroups();
        
        
        return new JsonResult(new JsonResponse(ResponseStatus.SUCCESS, "", list));
        
    }

    [AuthorizeClient]
    public JsonResult GetBlockedWebsites(LongIdInfo nasid)
    {
        var list = DbCalls.GetInstance().GetBlockedWebsites(nasid);
        
        
        return new JsonResult(new JsonResponse(ResponseStatus.SUCCESS, "", list));
        
    }


    [AuthorizeClient]
    public JsonResult GetNasBandwidth(LongIdInfo nasid)
    {
        JsonResponse response = AdminDatabaseRequest.GetNasBandwidth(nasid);
        return new JsonResult(response);
    }

    [AuthorizeClient]
    [HttpPost]
    [CoreModelBinder]
    public JsonResult SaveBlockedWebsites(LongIdInfo nasid, List<string> blockedWebsites)
    {
        AdminDatabaseRequest.SaveBlockedWebsites(nasid, blockedWebsites);
        
        
        return new JsonResult(new JsonResponse(ResponseStatus.SUCCESS, "", null));
        
    }
    
    private bool IsFeatureEnabled(Feature featureToBeChecked)
    {
        ManagementUser adminInPower = JwtObject.GetManagementUser(HttpContext);
        Dictionary<string, int> features = adminInPower.features;
        var feature = features.First(x => x.Key == featureToBeChecked.GetHashCode().ToString());
        return feature.Value == 1 ? true : false;
    }
}
