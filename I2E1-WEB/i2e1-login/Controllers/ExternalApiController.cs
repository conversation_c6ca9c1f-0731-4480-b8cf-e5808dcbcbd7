using i2e1_basics.Models;
using i2e1_basics.Utilities;
using i2e1_core.Utilities;
using I2E1_Message.Utils;
using I2E1_WEB.Attributes;
using I2E1_WEB.Database;
using I2E1_WEB.Models;
using I2E1_WEB.Utilities;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Collections.Generic;
using System.Linq;
using wiom_router_api.Models;

namespace I2E1_WEB.Controllers;

[Route("api/[controller]/[action]")]
[ApiAuthorization]
[ApiExceptionFilter]
public class ExternalApiController : Controller
{
    [HttpPost]
    // check once
    public JsonResponse UpdateList([FromBody]UpdateListRequest request)
    {   
        string token = (string)HttpContext.Items["i2e1-token"];
        int storeGroupId;
        if (request.nasid == null)
        {
            request.nasid = ApiUtils.GetNasIdFromExternalId(request.locationId, request.nasid, request.producerMobileNumber, token, out storeGroupId);
        }
        ListCheckDataType? type = ApiUtils.mapUpdateListToConfigType(request.updateList);
        var list = CoreDbCalls.GetInstance().GetListChecks(request.nasid, (ListCheckDataType)type);
        var obj = list.FirstOrDefault(m => m.Key == request.username);

        if(request.updateOperation == UpdateOperation.ADD)
        {
            if (String.IsNullOrEmpty(obj.Key))
                list.Add(request.username, request.name);
            else
                return new JsonResponse(ResponseStatus.SUCCESS, "already present", null);
        }
        else if(request.updateOperation == UpdateOperation.DELETE)
        {
            if(list.ContainsKey(request.username))
                list.Remove(request.username);
            else
                return new JsonResponse(ResponseStatus.SUCCESS, "username not found", null);
        }
        var data = new System.Text.StringBuilder();
        foreach (var pair in list)
        {
            data.Append(pair.Key).Append(";").Append(pair.Value).Append(",");
        }
        ClientDatabaseRequest.SaveListChecks(request.nasid, (ListCheckDataType)type, data.ToString());
        return new JsonResponse(ResponseStatus.SUCCESS, "updated");
    }

    // check once
    /*[HttpPost]
    public JsonResponse SyncList([FromBody]SyncListRequest request)
    {
        string token = (string)HttpContext.Items["i2e1-token"];
        int storeGroupId;
        request.sourceNasId = ApiUtils.GetNasIdFromExternalId(request.sourceLocationId, request.sourceNasId, request.sourceProducerMobileNumber, token, out storeGroupId);
        request.destinationNasId = ApiUtils.GetNasIdFromExternalId(request.destinationLocationId, request.destinationNasId, request.destinationProducerMobileNumber, token, out storeGroupId);
        
        ListCheckDataType? type = ApiUtils.mapUpdateListToConfigType(request.updateList);
        if(type != null){
            *//*var list = CoreDbCalls.GetInstance().GetListChecks(request.sourceNasId, (ListCheckDataType)type);

            var data = new System.Text.StringBuilder();
            foreach (var pair in list)
            {
                data.Append(pair.Key).Append(";").Append(pair.Value).Append(",");
            }
            ClientDatabaseRequest.SaveListChecks(request.destinationNasId, (ListCheckDataType)type, data.ToString());*//*
        }
        return new JsonResponse(ResponseStatus.SUCCESS, type.ToString() + " synced");
    }*/

    // check once
    //[HttpPost]
    //public JsonResponse AuthorizeDJUBOUser([FromBody]dynamic request)
    //{
    //    var reqData = JsonConvert.SerializeObject(request);
    //    Logger.GetInstance().Info("DJUBO-checkin-request -- raw data" + reqData);
    //    string token = (string)HttpContext.Items["i2e1-token"];
    //    int storeGroupId;
    //    string locationId = request.property_unit_id.ToString();
    //    string folio_guid = request.folio_guid.ToString();
    //    LongIdInfo nasid = null;
    //    nasid = ApiUtils.GetNasIdFromExternalId(locationId, nasid, 
    //        null, token, out storeGroupId);

    //    foreach(var details in request.room_details)
    //    {
    //        foreach(var guest in details.guests)
    //        {
    //            /*PassportUser pUser = new PassportUser()
    //            {
    //                mobile = guest.first_name + ' ' + guest.last_name + '@' + details.room.room_number,
    //                name = guest.first_name + ' ' + guest.last_name,
    //                extraDataObject = new PassportUserExtraData()
    //                {
    //                    sent_to = details.room.id,
    //                    plan_id = locationId
    //                },
    //                otpExpiryTime = DateTime.Parse(details.departure_date.ToString()).Date.AddMinutes(390),
    //                otpIssuedTime = Util.ConvertISTToUtc(DateTime.Parse(details.arrival_date.ToString()))
    //            };*/
    //            HomeRouterPlan pUser = new HomeRouterPlan()
    //            {
    //                mobile = guest.first_name + ' ' + guest.last_name + '@' + details.room.room_number,
    //                planId = Convert.ToInt32(locationId),
    //                planStartTime = DateTime.Parse(details.departure_date.ToString()).Date.AddMinutes(390),
    //                planEndTime = Util.ConvertISTToUtc(DateTime.Parse(details.arrival_date.ToString()))
    //            };

    //            //CoreDbCalls.GetInstance().GenerateUser(pUser, nasid, storeGroupId, 4, true, 0);
    //            WebUtils.LogInfoToCosmos("DJUBO-checkin-response", new
    //            {
    //                nasid = nasid,
    //                pUser = pUser
    //            });
    //        }
    //    }
        
    //    return new JsonResponse(ResponseStatus.SUCCESS, null);
    //}
    // check once
    [HttpPost]
    public JsonResponse LogoutUser([FromBody]LogoutUserRequest request)
    {
        string token = (string)HttpContext.Items["i2e1-token"];
        int storeGroupId;
        request.nasid = ApiUtils.GetNasIdFromExternalId(request.locationId, request.nasid, request.producerMobileNumber, token, out storeGroupId);

        AdminDatabaseRequest.LogoutRegisteredUser(request.nasid, storeGroupId, request.mobile);
        return new JsonResponse(ResponseStatus.SUCCESS, "", null);
    }

    // check once
    [HttpPost]
    public JsonResponse GetUsersInDuration([FromBody]GetUsersInDurationRequest request)
    {
        string token = (string)HttpContext.Items["i2e1-token"];
        int storeGroupId;
        request.nasid = ApiUtils.GetNasIdFromExternalId(request.locationId, request.nasid, request.producerMobileNumber, token, out storeGroupId);

        if (request.timeInterval == null)
        {
            DateTime start = Util.ConvertISTToUtc(request.startDate), end = Util.ConvertISTToUtc(request.endDate);
            request.startDate = start;
            request.endDate = end;
        }
        else
        {
            request.endDate = DateTime.UtcNow;
            request.startDate = request.endDate.AddMinutes(-request.timeInterval.Value);
        }

        var nasids = new List<LongIdInfo>();
        if(request.nasid != null)
            nasids.Add(request.nasid);
        //var res = RadAcctDbCalls.GetDetailedDataReport(true, nasids, request.mobile, request.startDate , request.endDate);
        var res = 0;
        return new JsonResponse(ResponseStatus.SUCCESS, "", res);
    }

    

    [HttpPost]
    public JsonResponse GetLocationStatus()
    {
        string token = (string)HttpContext.Items["i2e1-token"];
        if (token == "f28c8c0b-de86-47cf-a67f-81ec4bd4b88f")
        {
            DateTime time = DateTime.UtcNow;
            int clientId = 2208;
            var data = AdminDatabaseRequest.GetLocationStatus(clientId);
            return new JsonResponse(ResponseStatus.SUCCESS, "",
                data.Select(m =>
                {
                    return new { nasid = m.Key, timestamp = time, active_1hour = (time - m.Value).TotalSeconds > 3600 ? 0 : 1 };
                }));
        }
        else
            return new JsonResponse(ResponseStatus.FAILURE, "Invalid Token");
    }

    /*[HttpPost]
    public JsonResponse GetFacebookDailyReport()
    {
        string token = (string)HttpContext.Items["i2e1-token"];
        if (token == "f28c8c0b-de86-47cf-a67f-81ec4bd4b88f")
        {
            int clientId = 2208;
            var dict = new Dictionary<long, JObject>();
            var locations = DbCalls.GetInstance().GetLocationData(clientId);
            dict = AdminDatabaseRequest.GetFacebookPeriodRadacctData(dict, 30, locations);
            dict = AdminDatabaseRequest.GetFacebookPeriodRadacctData(dict, 1, locations);
            dict = AdminDatabaseRequest.GetLocationData(dict, locations);
            return new JsonResponse(ResponseStatus.SUCCESS, "", dict.Values);
        }
        else
            return new JsonResponse(ResponseStatus.FAILURE, "Invalid Token");
    }*/

    //[HttpPost]
    //public JsonResponse GetFacebookAggregateReport()
    //{
    //    string token = (string)HttpContext.Items["i2e1-token"];
    //    if (token == "f28c8c0b-de86-47cf-a67f-81ec4bd4b88f")
    //    {
    //        int clientId = 2208;
    //        return new JsonResponse(ResponseStatus.SUCCESS, "", CacheHelper.GetInstance().GetFacebookAggregateReport(clientId));
    //    }
    //    else
    //        return new JsonResponse(ResponseStatus.FAILURE, "Invalid Token");
    //}
}
