using i2e1_basics.Cache;
using i2e1_basics.Utilities;
using i2e1_core.Models;
using i2e1_core.Utilities;
using I2E1_Message.Models;
using I2E1_Message.Utils;
using I2E1_WEB.Database;
using I2E1_WEB.Utilities;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using RestSharp;
using System.Web;
using ResponseStatus = i2e1_basics.Models.ResponseStatus;

namespace I2E1_WEB.Controllers;

public class TRAIController : BaseController
{
    public const string I2E1_PDOA_ID = "91832d3d-5064-4b80-b8c8-13b90d0de484";
    private static FinalObject finaldata
    {
        get
        {
            return CacheHelper.GetInstance().GetCompleteTRAIXML("TRAI_XML_DATA");
        }
    }

    //---------------------------------------API SECTION START------------------------------------------

    //parse the token to fetch app provider id and generate auth url and doa token
    //this is the point to be hit by the app
    [HttpGet]
    public JsonResult parseToken(string waniapptoken)
    {
        
        string urlBase = "https://testease.i2e1.in";
        if (Constants.IS_PRODUCTION)
        {
            urlBase = "https://www.i2e1.in";
        }
        

        //split the wanipapptoken to fetch the app provider id
        WebUtils.LogInfoToCosmos("Request", new
        {
            token =waniapptoken ,
            from = "TRAI"
        });
        string[] paramtoken = waniapptoken.Split('|');
        string appproviderid = paramtoken[0];

        // get the auth url for the provider id
        AppProviderData appdata = verifyproviderid(appproviderid);

        //fetch pdoakey for encryption
        PDOData pdodata = fetchPDOAKEY();
        if (appdata != null)
        {
            WaniData traidata;
            //generate new doatoken to be sent to appauth url
            string wanipdoatoken = generatewanipdoatoken(waniapptoken, pdodata);
            traidata = makeauthorize(wanipdoatoken, appdata, HttpContext);

            traidata.username = traidata.username.Replace("+91", string.Empty);

            string token = LoginUtils.createCookieToken(traidata.username);
            SessionCacheHelper.GetInstance().SetSession(CoreCacheHelper.WANI_CREDENTIALS, traidata.username, traidata, Constants.MINUTES_IN_DAY * 7);
            DatabaseRequest.SaveWaniAppLog(traidata.username, traidata.app_provider_id, traidata.apMacId);
            return new JsonResult(new
            {
                paymentUrl = string.Format("{0}/Login/SetWaniCookie?mobile={1}&token={2}", urlBase, traidata.username, token)
            });
        }
        else
        {
            WebUtils.LogErrorToCosmos("No pdoa data found for appproviderid:" + appproviderid, new
            {
                response = "No pdoa data found for appproviderid:" + appproviderid
            });
            return new JsonResult(new i2e1_basics.Models.JsonResponse(ResponseStatus.FAILURE, "No pdoa data found for appproviderid:" + appproviderid));
        }
        return new JsonResult(new i2e1_basics.Models.JsonResponse(ResponseStatus.FAILURE, "Some error occured"));
    }

    //---------------------------------------API SECTION END------------------------------------------


    //---------------------------------------PARSING METHOD SECTION START------------------------------------------


    //verify a provider id and fetch url and key for the same
    private static AppProviderData verifyproviderid(string id)
    {
        return finaldata.appproviderdata[id].ToObject<AppProviderData>();
    }

    //fetch pdoa key from the server
    private static PDOData fetchPDOAKEY()
    {
        foreach (var x in finaldata.pdolist)
        {
            PDOData data =x.Value.ToObject<PDOData>();
            if(data.id == I2E1_PDOA_ID)
            {
                return data;
            }
        }
        return null;
    }

    //create a wanipdoatoken to send to authurl
    private static string generatewanipdoatoken(string wanipapptoken, PDOData pdodata)
    {
        //fetchPDOAKEY();
        string wanipdoatoken = pdodata.id + "|" + pdodata.keys.exp + "|"+ TRAIUtils.base64encode(Encryption.EcryptUsingCert(wanipapptoken));
        return wanipdoatoken;

    }

    //make the autorize call to the auth url and fetch the payment url
    private static WaniData makeauthorize(string param, AppProviderData appdata,HttpContext httpContext)
    {
        string url =appdata.authUrl + (appdata.authUrl.EndsWith("/") ? "?wanipdoatoken=" : "/?wanipdoatoken=") + HttpUtility.UrlEncode(param);
        var client = new RestClient(url);
        var request = new RestRequest() { Method = Method.Get };
        request.AddHeader("postman-token", "c5b96383-2e0a-888e-f115-ca6e07b99160");
        request.AddHeader("cache-control", "no-cache");
        RestResponse response = client.Execute(request);
        if (response.StatusCode != System.Net.HttpStatusCode.OK)
            return null;
        WaniData traidata = JsonConvert.DeserializeObject<WaniData>(response.Content);
        string compsignature = traidata.timestamp+ traidata.username + traidata.password + traidata.apMacId + traidata.payment_address + traidata.deviceMacId;

        //string compsignature1 = sha256_hash(compsignature);
       // signature = Encryption.DecryptUsingCert(signature);
        //if (signature.Equals(compsignature1))
          //  return paymenturl;
        //else
        return traidata;
    }

    //---------------------------------------PARSING METHOD SECTION START------------------------------------------    
}
