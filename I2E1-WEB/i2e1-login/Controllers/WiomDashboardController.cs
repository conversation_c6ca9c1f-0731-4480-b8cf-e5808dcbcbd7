using i2e1_basics.Utilities;
using i2e1_core.Attributes;
using i2e1_core.Models;
using i2e1_core.services;
using i2e1_core.Utilities;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Caching.Memory;

namespace i2e1_login.Controllers;

public class WiomDashboardController : BaseController
{
    private IMemoryCache memoryCache;
    public WiomDashboardController(IMemoryCache memoryCache)
    {
        this.memoryCache = memoryCache;
    }

    public ActionResult Index()
    {
        var path = HttpContext.Request.Path.Value;
        var host = HttpContext.Request.Host.Host;

        Logger.GetInstance().Info("Client IP: " + Request.Headers["X-Real-IP"]);

        var user = AuthorisationService.AuthenticateRequest(HttpContext);
        
        if (user == null)
            return RedirectTo("/auth/verify-pass?redirectUrl=/");
        return WiomDashboard(user);
    }

    [AdminPortalAuthorization]
    public ActionResult WiomDashboard(ManagementUser user = null)
    {
        ViewBag.title = "Wiom Dashboard";
        if (user == null)
            user = JwtObject.GetManagementUser(HttpContext);
        if (AuthorisationService.IfUserIsAllowedToAccessFeature(user, i2e1_core.Models.Client.Feature.WIOM_DASHBOARD))
        {
            ViewBag.viewBag = new
            {
                user = user
            };
            Logger.GetInstance().Info("Returning wiom-dashboard.cshtml " + user.contact_no + " " + user.email);
            return ContentResult(memoryCache, "/wiom-dashboard/wiom-dashboard.html");
        }

        ViewBag.message = "You dont have permission to access wiom dashboard.";
        return ViewTo("~/Views/no-authorisation.cshtml");
    }

    [Route("/auth/{path?}")]
    public ActionResult Services(string path)
    {
        ViewBag.title = "Wiom Login";
        var user = AuthorisationService.AuthenticateRequest(HttpContext);

        if (user != null)
        {
            var username = user.email;
            if (string.IsNullOrEmpty(username))
                username = user.contact_no;
            ViewBag._viewBag = new
            {
                user = user,
                username = username,
                redirectParams = AuthorisationService.GetRedirectParams(HttpContext, username, user),
            };
        }
        return ContentResult(memoryCache, "/login/login.html");
    }
}
