using System;
using System.Collections.Generic;
using i2e1_basics.Models;
using i2e1_basics.Utilities;
using i2e1_core.Attributes;
using i2e1_core.Models;
using i2e1_core.Utilities;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;

namespace i2e1_login.Controllers;

public class OttEventsController : BaseController
{
    
    [HttpPost]
    [CoreModelBinder]
    public JsonResult SendOttFrontendEvent(string keyName, string uniqueIdentifier, Dictionary<string, object> eventData)
    {
        
        if (string.IsNullOrEmpty(keyName)){
            return JsonResult(ResponseStatus.FAILURE, $"KeyName can not be null or empty");
        }
            
        if (eventData != null && !eventData.ContainsKey("datetime")){
            eventData.Add("datetime", DateTime.UtcNow);
        }
        
        if (string.IsNullOrEmpty(uniqueIdentifier)){
            uniqueIdentifier = DateTime.Now.Ticks.ToString();
        }
        
        OttEventsUtils.SendOttEventLogs(HttpContext, keyName, uniqueIdentifier, eventData);

        return JsonResult(ResponseStatus.SUCCESS, $"Sent Frontend Event Successfully");
    }

}