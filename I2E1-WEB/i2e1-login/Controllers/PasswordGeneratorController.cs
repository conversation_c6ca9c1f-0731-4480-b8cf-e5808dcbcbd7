using ExcelDataReader;
using i2e1_core.Models;
using i2e1_core.Utilities;
using I2E1_Message;
using I2E1_WEB.Database;
using I2E1_WEB.Utilities;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Globalization;
using System.IO;
using wiom_login_share.Utilities;
using wiom_routerplan_share.Models.RouterPlan;

namespace I2E1_WEB.Controllers;

//TODO
public class PasswordGeneratorController : Controller
{
    //TODO
    public ActionResult Index()
    {   
        ViewBag.passwordGeneratorUser = WebUtils.GetPasswordGenerator(HttpContext);
        return View("~/Views/Generator/index.cshtml", WebUtils.GetRegisteredUsers(HttpContext));
    }

    //TODO
    public ActionResult BulkUpload()
    {
        var pgUser = WebUtils.GetPasswordGenerator(HttpContext);
        ViewBag.passwordGeneratorUser = WebUtils.GetPasswordGenerator(HttpContext);
        return View("~/Views/Generator/BulkUpload.cshtml");

    }

    public ActionResult Upload(IFormFile file, long dataPlan, string expiryTime)
    {

        Stream stream = file.OpenReadStream();

        IExcelDataReader reader = ExcelReaderFactory.CreateBinaryReader(stream);
        if (file.FileName.EndsWith(".xls"))
        {
            reader = ExcelReaderFactory.CreateBinaryReader(stream);
        }
        else if (file.FileName.EndsWith(".xlsx"))
        {
            reader = ExcelReaderFactory.CreateOpenXmlReader(stream);
        }
        else
        {
            ModelState.AddModelError("File", "This file format is not supported");
            return View();
        }

        reader.Read();
        Reports report = new Reports();
        int cols = reader.FieldCount;
        HomeRouterPlan user = null;


        while (reader.Read())
        {
            /*user = new PassportUser();
            user.mobile = reader.GetString(0);
            user.otp = reader.GetString(1);
            user.dataPlan = dataPlan;*/
            user = new HomeRouterPlan();
            user.mobile = reader.GetString(0);
            user.otp = reader.GetString(1);
            user.dataLimit = dataPlan;
        }

        reader.Close();
        //TODO
        ViewBag.passwordGeneratorUser = WebUtils.GetPasswordGenerator(HttpContext);
        return View("~/Views/Generator/index.cshtml", WebUtils.GetRegisteredUsers(HttpContext));
    }
    //TODO
    /*public ActionResult ViewOTP()
    {
        var pgUser = CoreSessionUtils.GetPasswordGenerator(HttpContext);

        if (pgUser != null)
        {
            ViewBag.passwordGeneratorUser = CoreSessionUtils.GetPasswordGenerator(HttpContext);
            return View("~/Views/Generator/ViewOTP.cshtml", AdminDatabaseRequest.ViewLatestOTP(pgUser.nasid));
        }
        return Redirect("Index");
    }*/

    [HttpPost]
    public ActionResult Generate(HomeRouterPlan user, string expiryTime)
    {
        var pgUser = WebUtils.GetPasswordGenerator(HttpContext);
        if (pgUser != null)
        {
            user.otp = new Random().Next(1000, 10000).ToString();
            user.planEndTime = DateTime.ParseExact(expiryTime, "dd.MM.yyyy", CultureInfo.InvariantCulture);
            user.planEndTime = user.planEndTime.Add(new TimeSpan(18, 29, 0));
            CoreDbCalls.GetInstance().GenerateUser(user, pgUser.nasid, 4, true, 0);
        }
        return Redirect("Index");
    }
    //TODO
    [HttpPost]
    public ActionResult Login(string username, string password)
    {
        if (string.IsNullOrEmpty(username) || string.IsNullOrEmpty(password))
        {
            return new StatusCodeResult(403);
        }
        PasswordGeneratorUser user = DatabaseRequest.LoginPasswordGenerator(username, password);
        if (user != null)
        {
            WebUtils.SetPasswordGenerator(user, HttpContext);
        }
        else
        {
            ViewBag.PasswordGeneratorError = true;
            ViewBag.passwordGeneratorUser = WebUtils.GetPasswordGenerator(HttpContext);
            return View("~/Views/Generator/index.cshtml", null);
        }
        return Redirect("Index");
    }
    //TODO
    public ActionResult Logout()
    {
        WebUtils.SetPasswordGenerator(null, HttpContext);
        return Redirect("Index");
    }

    public ActionResult LogoutUser(string username)
    {
        var pgUser = WebUtils.GetPasswordGenerator(HttpContext);
        if (pgUser != null)
        {
            WebUtils.LogoutFDMUser(username, pgUser.nasid, pgUser.storeGroupId);
        }
        return Redirect("Index");

    }

    public ActionResult AuthorizeUser(string username)
    {
        var pgUser = WebUtils.GetPasswordGenerator(HttpContext);
        if (pgUser != null)
        {
            AdminDatabaseRequest.AuthorizeUser(pgUser.nasid, pgUser.storeGroupId, username);
        }
        return Redirect("Index");
    }

    public ActionResult ChangePlan(string mobile, string expiryTime, string roomNo, bool authState, int uploadInKbps, int downloadInKbps, int dataPlan = 0)
    {
        var pgUser = WebUtils.GetPasswordGenerator(HttpContext);
        if (pgUser != null)
        {
            var otpExpiryTime = DateTime.ParseExact(expiryTime, "dd.MM.yyyy", CultureInfo.InvariantCulture);
            otpExpiryTime = otpExpiryTime.Add(new TimeSpan(18, 29, 0));
            AdminDatabaseRequest.ChangeDataPlan(pgUser.nasid, pgUser.storeGroupId, mobile, dataPlan, otpExpiryTime, true);
        }
        return Redirect("Index");
    }
}