using i2e1_basics.Cache;
using i2e1_basics.Models;
using i2e1_basics.Utilities;
using i2e1_core.Attributes;
using i2e1_core.Models;
using i2e1_core.PG;
using i2e1_core.services;
using i2e1_core.Utilities;
using I2E1_Message.Utils;
using I2E1_WEB.MiddleTier;
using I2E1_WEB.Models;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Linq;
using wifidog_core.Models.WIOM;
using wiom_login_share.Models;
using wiom_login_share.Utilities;

namespace I2E1_WEB.Controllers;

[Route("v1/auth/[action]")]
[Route("v1/Authentication/[action]")]
public class AuthenticationController_v1 : BaseController
{
    [Route("/auth")]
    public ActionResult login()
    {
        return ViewTo("~/Views/modules/login.cshtml");
    }

    [CoreModelBinder]
    public JsonResult LoginSignal(string userSsid, string ssidPass, string signalToken, string fcmToken = "BY_PASS", string appVersion = null, string brand = null, string model = null, string advertisingId = null)
    {
        if (!string.IsNullOrEmpty(userSsid))
            userSsid = userSsid.Trim();

        if (!string.IsNullOrEmpty(ssidPass))
            ssidPass = ssidPass.Trim();

        if(!string.IsNullOrEmpty(signalToken))
        {
            var user = SessionCacheHelper.GetInstance().GetSession<ManagementUser>("wg-sig-usr-" + signalToken, "0");
            JObject signalLoginDetails = UserService.FetchUserOnSignal(userSsid);

            if (signalLoginDetails != null && (int)signalLoginDetails["signalCount"] == 1)
            {
                string username = signalLoginDetails["username"].ToString();
                if (!string.IsNullOrEmpty(username) && username == user.username)
                {
                    var data = UserService.GenerateOTPPassword(HttpContext, username, fcmToken, "LoginSignal", brand, model, appVersion, advertisingId);
                    return JsonResult(ResponseStatus.SUCCESS, data);
                }
            }
            else
                return JsonResult(ErrorCodes.USER_NOT_EXISTS, JsonConvert.SerializeObject(new { repeatSignalCount = signalLoginDetails["signalCount"] }));
        }

        if (!string.IsNullOrEmpty(userSsid) && !string.IsNullOrEmpty(ssidPass)) {
            JObject signalLoginDetails = UserService.SignalLogin(userSsid, ssidPass);

            if (signalLoginDetails != null && (int)signalLoginDetails["signalCount"] == 1)
            {
                String username = signalLoginDetails["username"].ToString();
                if (!string.IsNullOrEmpty(username))
                {
                    var data = UserService.GenerateOTPPassword(HttpContext, username, fcmToken, "LoginSignal", brand, model, appVersion, advertisingId);
                    return JsonResult(ResponseStatus.SUCCESS, data);
                }
            }
            return JsonResult(ErrorCodes.USER_NOT_EXISTS, JsonConvert.SerializeObject(new { repeatSignalCount = signalLoginDetails["signalCount"] }));
        }
        
        return JsonResult(ErrorCodes.BAD_REQUEST);
    }

    [CoreModelBinder]
    public JsonResult FetchUserOnSignal(string userSsid, string signalToken)
    {
        if (!string.IsNullOrEmpty(signalToken))
        {
            string s10 = SessionCacheHelper.GetInstance().GetSession<String>("wg-sig-" + signalToken, "SetServer10");
            if(string.IsNullOrEmpty(s10))
                return JsonResult(ErrorCodes.BAD_REQUEST);
        }

        if (!string.IsNullOrEmpty(userSsid))
            userSsid = userSsid.Trim();

        if (!string.IsNullOrEmpty(userSsid))
        {
            JObject signalLoginDetails = UserService.FetchUserOnSignal(userSsid);

            if (signalLoginDetails != null && (int)signalLoginDetails["signalCount"] == 1)
            {
                string username = signalLoginDetails["username"].ToString();
                if(!string.IsNullOrEmpty(username))
                {
                    int shardId = (int)signalLoginDetails["shardId"];
                    var user = UserService.GetAdminUser(shardId, username);
                    user.password = null;
                    SessionCacheHelper.GetInstance().SetSession("wg-sig-usr-" + signalToken, "0", user);

                    return JsonResult(ResponseStatus.SUCCESS, user);
                }
            }
            return JsonResult(ErrorCodes.USER_NOT_EXISTS);
        }
        return JsonResult(ErrorCodes.USER_NOT_EXISTS);
    }

    [CoreModelBinder]
    public JsonResult SendOTP(string username, string hash, string fcmToken = "BY_PASS", string appVersion=null, string brand=null, string model= null,string advertisingId = null)
    {

        var user = UserService.GenerateOTPPassword(username, fcmToken, hash, brand, model, appVersion, advertisingId);
        Dictionary<string, ErrorCode> dictForErrorCodes = new Dictionary<string, ErrorCode>();
        dictForErrorCodes.Add(Constants.CUSTOMER_APP_UNAUTHORIZED_LOGIN_POSSIBLE_CASES[0], ErrorCodes.CUSTOMER_APP_LOGIN_FROM_GROWTH_APP_USER);
        dictForErrorCodes.Add(Constants.CUSTOMER_APP_UNAUTHORIZED_LOGIN_POSSIBLE_CASES[1], ErrorCodes.CUSTOMER_APP_LOGIN_FROM_PARTNER_APP_USER);
        dictForErrorCodes.Add(Constants.CUSTOMER_APP_UNAUTHORIZED_LOGIN_POSSIBLE_CASES[2], ErrorCodes.UNAUTHORIZED_CUSTOMER_APP_LOGIN);

        if (user != null && (user.otp == "EXCEED_LOGIN_LIMIT" || Constants.CUSTOMER_APP_UNAUTHORIZED_LOGIN_POSSIBLE_CASES.Contains(user.otp)))
        {
            if (user.otp == "EXCEED_LOGIN_LIMIT")
            {
                return JsonResult(ErrorCodes.MAXIMUM_LOGIN_PER_DEVICE);
            }

            foreach (KeyValuePair<string, ErrorCode> pair in dictForErrorCodes)
            {
                if(user.otp == pair.Key)
                {
                    return JsonResult(pair.Value);
                }
            }
        }
            
        Logger.GetInstance().Info(String.Format("AuthenticationController_v1:SendOTP: username: {0} otp: {1}", username, user.otp));
        if (user != null)
        {
            string guid = Guid.NewGuid().ToString();
            SessionCacheHelper.GetInstance().SetSession("wg-otp-" + guid, username, user);
            CookieUtils.SetCookie(HttpContext, "otp-user", guid, true, Util.ConvertUtcToIST(DateTime.UtcNow).AddMinutes(5), true);
            return JsonResult(ResponseStatus.SUCCESS, "otp sent", guid);
        }
        
        return JsonResult(ErrorCodes.USER_NOT_EXISTS);
    }

    public ActionResult VerifyOTP(string username, string otp, string guid = null)
    {
        Logger.GetInstance().Info(String.Format("AuthenticationController_v1:VerifyOTP: username: {0} otp: {1}, guid: {2}", username, otp, guid));
        if (string.IsNullOrEmpty(guid))
        {
            guid = CookieUtils.GetCookie(HttpContext, "otp-user");
        }
        var user = SessionCacheHelper.GetInstance().GetSession<LoginUser>("wg-otp-" + guid, username);

        if (user != null && otp == user.otp)
        {
            UserService.UpdateOtpSubmitedTime(user);
            //SessionUtils.setLoggedInUser(user, HttpContext);
            ManagementUser adminUser = null;
            Account account = null;
            try
            {
                adminUser = UserService.GetAdminUser(user.id);
                if (adminUser != null)
                    account = CoreAccountService.GetAccountWithUserId(adminUser.userid);

                JwtToken token = JWTManager.CreateJwtToken(user, account, adminUser, HttpContext);
            }
            catch (Exception ex)
            {
                Logger.GetInstance().Info("Exception in getting account from admin user: " + user.mobile + " ex: " + ex.ToString());
            }
            return JsonResult(ResponseStatus.SUCCESS, new
            {
                loginUser = user,
                adminUser = adminUser,
                account = account
            });
        }
        return JsonResult(new ErrorResponse(ResponseStatus.FAILURE, ErrorCodes.INVALID_OTP, "invalid otp"));
    }

    [CoreModelBinder]
    public ActionResult VerifyHMROTP(string username, string otp, string guid = null)
    {
        Logger.GetInstance().Info(String.Format("AuthenticationController_v1:VerifyHMROTP: username: {0} otp: {1}, guid: {2}", username, otp, guid));
        if (string.IsNullOrEmpty(guid))
        {
            guid = CookieUtils.GetCookie(HttpContext, "otp-user");
        }
        var user = SessionCacheHelper.GetInstance().GetSession<LoginUser>("wg-otp-" + guid, username);

        if (user != null && otp == user.otp)
        {
            UserService.UpdateOtpSubmitedTime(user);
            //SessionUtils.setLoggedInUser(user, HttpContext);
            
            ManagementUser adminUser = null;
            Account account = null;
            JwtToken token = null;
            try
            {
                //adminUser = UserService.GetAdminUser(user.id);
                adminUser = UserService.GetAdminUser(user.id.shard_id, user.mobile);
                if(adminUser != null)
                    account = CoreAccountService.GetAccountWithUserId(adminUser.userid);

                token = JWTManager.CreateJwtToken(user, account, adminUser, HttpContext);
                Constants.BOOKING_LOG.Publish("otp_verified", user.mobile, new Dictionary<string, object>() {
                    { "mobile", user.mobile },
                    { "otp", otp }
                });

            }
            catch (Exception ex) 
            {
                Logger.GetInstance().Info("Exception in getting account from admin user: " + user.mobile + " ex: " + ex.ToString());
            }
            return JsonResult(ResponseStatus.SUCCESS, new
            {
                loginUser = user,
                adminUser = adminUser,
                account = account,
                jwtToken = token
            });
        }
        return JsonResult(new ErrorResponse(ResponseStatus.FAILURE, ErrorCodes.INVALID_OTP, "invalid otp"));
    }

    [IfLoggedIn]
    [CoreModelBinder]
    public JsonResult UpdateFCMDetails(string fcmToken, string appVersion, string lat, string lng)
    {
        JwtObject jwtObject = (JwtObject)HttpContext.Items[Constants.JWT_OBJECT];

        var appUser = CoreUserService.GetUpdatedAppDevice(App.HOME_ROUTER, jwtObject.mobile, string.IsNullOrEmpty(lat) ? 0 : double.Parse(lat), string.IsNullOrEmpty(lng) ? 0 : double.Parse(lng), fcmToken, "FCM_UPDATE", null, null, appVersion);
        //SessionUtils.setLoggedInUser(appUser, HttpContext);
        return JsonResult(ResponseStatus.SUCCESS, appUser);
    }

    public JsonResult SendBookNowMail(string number, string name, string address, string pincode)
    {
        var emailResult = BasicEmailSender.SendEmail(new Notification()
        {
            isHTML = true,
            emailId = new List<string>() { "<EMAIL>", "<EMAIL>", "<EMAIL>" },
            //emailId = new List<string>() { "<EMAIL>" },
            message = String.Format(@"Hello wiom 
                <br/><br/>
                A user phone number {0} downloaded wiom gold app and wants to connect to wiom gold team.
                <br/><br/>
                His details are below:<br/>
                Name: {1} <br/>
                Address: {2} <br/>
                Pincode: {3}
                <br/><br/><br/>Thanks<br/>i2e1/wiom Services", number, name, address, pincode),
            title = String.Format("{1}, Phno: {0}, wants to contact wiom gold team", number, name),
            source = "i2e1 wiom admin services"
        }, "<EMAIL>", "i2e1/wiom Services", null);
        return JsonResult(ResponseStatus.SUCCESS, "Someone from wiom services will contact you soon");
    }

    [IfLoggedIn]
    public JsonResult GetAllHomeRouterBasicPLan()
    {
        var list1 = CoreCacheHelper.GetInstance().GetActivePlansInSetting(9);
        var list2 = CoreCacheHelper.GetInstance().GetActivePlansInSetting(10);
        Dictionary<int, List<PDOPlan>> BasicAllPlan = new Dictionary<int, List<PDOPlan>>();
        BasicAllPlan[9] = list1;
        BasicAllPlan[10] = list2;
        return JsonResult(ResponseStatus.SUCCESS, BasicAllPlan);
    }

    [IfLoggedIn]
    public JsonResult GetDeviceIdFromNas(LongIdInfo nasid)
    {
        var nasSplitTemplate = ShardHelper.GetNasDetailsFromLongNas(nasid);
        if(nasSplitTemplate == null)
        {
            return JsonResult(CoreErrorCodes.NO_SUCH_DEVICE_ID);
        }

        return JsonResult(ResponseStatus.SUCCESS, nasSplitTemplate.deviceId);
    }

    [IfLoggedIn]
    [HttpGet]
    public JsonResult ChangeMandateStatus(LongIdInfo accountId, string status, string extraData)
    {
        if(status == "Cancel")
        {
            Mandate mandate = CoreAccountService.GetAccountMandateInfo(accountId);
            if(mandate != null)
            {
                Logger.GetInstance().Info(string.Format("AuthenticationController_v1:ChangeMandateStatus:1stStep: accountId: {0} status: {1} mandate: {2}", accountId, status, mandate));
                JObject response = JuspayPG.ChangeMandateStatus(mandate.mandate_id, status);
                if (response != null)
                {
                    if (response.ContainsKey("mandate_status") && response["mandate_status"].ToString() == "REVOKED")
                    {
                        Logger.GetInstance().Info(string.Format("AuthenticationController_v1:ChangeMandateStatus:2ndStep: accountId: {0} status: {1} response: {2}", accountId, status, response));
                        bool updateMandateStatus = CoreAccountService.InsertAndUpdateMandateInfo(accountId, mandate.mandate_id, mandate.token, "REVOKED", mandate.mandate_end_date,
                        mandate.customer_id, mandate.mandate_amount.ToString(), mandate.mandate_start_date, extraData);
                        Logger.GetInstance().Info(string.Format("AuthenticationController_v1:ChangeMandateStatus:3rdStep: accountId: {0} status: {1} updateMandateStatus: {2}", accountId, status, updateMandateStatus));

                        return JsonResult(ResponseStatus.SUCCESS, updateMandateStatus);
                    }
                    else if(response.ContainsKey("error_message") && response["error_message"].ToString() == "Mandate Not in Active State")
                    {
                        bool updateMandateStatus = CoreAccountService.InsertAndUpdateMandateInfo(accountId, mandate.mandate_id, mandate.token, "REVOKED", mandate.mandate_end_date,
                        mandate.customer_id, mandate.mandate_amount.ToString(), mandate.mandate_start_date, extraData);
                        return JsonResult(ResponseStatus.SUCCESS, updateMandateStatus);
                    }
                }
            }   
        }
        return JsonResult(ResponseStatus.SUCCESS, false);
    }
}
