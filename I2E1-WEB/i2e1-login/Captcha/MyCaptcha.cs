using i2e1_basics.Cache;
using I2E1_WEB.Models;
using System;
using System.Linq;
using System.Web;

namespace I2E1_WEB.Captcha;

public enum CaptchaState{
    NO_CAPTCHA = 0,
    SHOW_CAPTCHA
}
public class MyCaptcha
{
    public static CaptchaState CaptchaValidate(string identifier, string username, string enteredCaptcha, out string errorMsg, out string captchaUrl)
    {
        errorMsg = null;
        captchaUrl = null;
        string key = identifier.ToUpper() + "_" + username.ToUpper();
        var value = SessionCacheHelper.GetInstance().GetSession<CaptchaUser>(key, 0);
        if (value == null)
        {
            value = new CaptchaUser()
            {
                lastRetryCycle = DateTime.UtcNow,
                retryCount = 1
            };
            SessionCacheHelper.GetInstance().SetSession(key, 0, value);
        }

        bool validatedCaptcha = false;
        if (!string.IsNullOrEmpty(value.captchaValue))
        {
            if (enteredCaptcha == null || enteredCaptcha.ToLower() != value.captchaValue.ToLower())
            {
                errorMsg = "Invalid Captcha";
            }
            else
                validatedCaptcha = true;
        }

        if (!validatedCaptcha)
        {
            if ((DateTime.UtcNow - value.lastRetryCycle).TotalMinutes >= 15)
            {
                value = new CaptchaUser()
                {
                    lastRetryCycle = DateTime.UtcNow,
                    retryCount = 1
                };
                SessionCacheHelper.GetInstance().SetSession(key, 0, value);
            }
            else if (value.retryCount >= 5)
            {
                value.captchaValue = new MyCaptcha() { MaxLetterCount = 6 }.TryNew();
                SessionCacheHelper.GetInstance().SetSession(key, 0, value);
                captchaUrl = "/Captcha/GetImgText.ashx?rand=" + new Random().Next() + "&username=" + HttpUtility.UrlEncode(key);
                return CaptchaState.SHOW_CAPTCHA;
            }
            else
            {
                value.retryCount++;
                SessionCacheHelper.GetInstance().SetSession(key, 0, value);
            }
        }

        return CaptchaState.NO_CAPTCHA;
        
    }
    public string TryNew()
    {
        char[] Valichars = {'1','2','3','4','5','6','7','8','9','0','a','b','c','d','e','f','g','h','i',
                       'j','k','l','m','n','o','p','q','r','s','t','u','v','w','x','y','z' };
        string Captcha = "";
        int LetterCount = MaxLetterCount > 5 ? MaxLetterCount : 5;
        for (int i = 0; i < LetterCount; i++)
        {
            char newChar = (char)0;
            do
            {
                newChar = Char.ToUpper(Valichars[new Random(DateTime.Now.Millisecond).Next(Valichars.Count() - 1)]);
            }
            while (Captcha.Contains(newChar));
            Captcha += newChar;
        }

        return Captcha;
    }

    public int MaxLetterCount { get; set; }
}