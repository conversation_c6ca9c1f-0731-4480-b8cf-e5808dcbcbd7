using i2e1_core.Models;
using i2e1_core.Utilities;
using I2E1_Message.Models;
using I2E1_Message.Utils;
using I2E1_WEB.Controllers;
using I2E1_WEB.Database;
using I2E1_WEB.Utilities;
using Microsoft.AspNetCore.Http;
using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Web;

namespace I2E1_WEB.Validators;

public class BataValidatior : BasicValidator
{
    public BataValidatior(BaseController controller, HttpContext httpContext)
        : base(controller, httpContext)
    {
        this.noOfQuestionsOnSecondPage = 2;
    }        
}