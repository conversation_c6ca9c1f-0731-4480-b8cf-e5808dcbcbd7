using i2e1_basics.Models;
using i2e1_core.Models;
using i2e1_core.Utilities;
using I2E1_Message.Models;
using Microsoft.AspNetCore.Http;
using System.Collections.Generic;
using wifidog_core.Models;
using wiom_login_share.Models;

namespace I2E1_WEB.Validators;

public class KacificValidator : DataVoucherValidator
{
    public KacificValidator(BaseController controller, HttpContext httpContext) : base(controller, httpContext)
    {
    }

    public override JsonResponse GenerateOTPValidation(User user, FDMConfig fdmConf)
    {
        if (!string.IsNullOrEmpty(user.mobile))
        {
            var questions = new List<Question>() { new Question()
            {
                answer = user.mobile,
                answerType = AnswerType.TEXT,
                id = 8,
                templateId = user.GetBaseTemplateId(),
                mobile = user.mobile,
            } };
            base.SubmitAnswers(user, questions);
            return base.GenerateI2e1OTPValidation(user);
        }
        return new JsonResponse(ResponseStatus.FAILURE, "Invalid Email Id");
    }
}