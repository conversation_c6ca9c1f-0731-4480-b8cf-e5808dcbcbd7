using i2e1_core.Models;
using i2e1_core.Utilities;
using I2E1_Message.Models;
using Microsoft.AspNetCore.Http;
using Newtonsoft.Json;
using RestSharp;
using System;
using System.Collections.Generic;
using wiom_login_share.Models;
using wiom_routerplan_share.Models.RouterPlan;

namespace I2E1_WEB.Validators;

public class UncubeValidator : BasicValidator
{
    public UncubeValidator(BaseController controller, HttpContext httpContext)
        : base(controller, httpContext)
    {
    }

    private bool VerifyWithUncube(User user, string emailid)
    {
        string json = JsonConvert.SerializeObject(new
        {
            email=emailid,
            phone_number=user.mobile,
            wifi_provider_email="<EMAIL>",
            partner_wifi_id = user.backEndNasid,
            token="amFtZXNzZWthcjAwN0BnbWFpbC5jb20wOThlOWVjZTllNDU2ZTVjNzZmMTYwYjM0YzA3MmQwOGU5MzI2NTNmOThjMmY1OWM2NTdmNzZjNzU0OGRhNGI1MzMxNDYy"
        });
        var client = new RestClient("https://www.theuncube.com/api/v1/wifiauthenticate");
        var request = new RestRequest() { Method = Method.Post }; 
        request.AddHeader("content-type", "application/json");
        request.AddParameter("application/json", json, ParameterType.RequestBody);
        RestResponse response = client.Execute(request);
        dynamic data = JsonConvert.DeserializeObject(response.Content, typeof(object));
        if (data.ContainsKey("errors"))
        {
            return false;
        }
        return true;
    }

    public override void SubmitAnswers(User user, List<Question> questions) 
    {
        if (!(questions.Count == 1 && questions[0].questionKey.ToUpper() == "EMAIL"))
        {
            return;
        }
        string bookingEmail = questions[0].answer.Trim();
        if (!this.VerifyWithUncube(user, bookingEmail))
        {
            throw new Exception("Use same phno/email as used for booking");
        }
        if (user.templateid.Count == 2 && user.templateid[1] == 15)
        {
            CoreDbCalls.GetInstance().GenerateUser(new HomeRouterPlan() {
                mobile = user.mobile,
                dataLimit = (long)1024,
                planStartTime = DateTime.UtcNow,
                planEndTime = DateTime.UtcNow.AddDays(1),
            }, user.backEndNasid, 4, true, 0);
        }
        base.SubmitAnswers(user, questions);
    }        
}