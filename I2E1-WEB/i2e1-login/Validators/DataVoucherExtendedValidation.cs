using i2e1_basics.Models;
using i2e1_core.Models;
using i2e1_core.Models.Client;
using i2e1_core.Utilities;
using Microsoft.AspNetCore.Http;
using System;
using wiom_login_share.Models;
using wiom_routerplan_share.Models.RouterPlan;

namespace I2E1_WEB.Validators;

public class DataVoucherExtendedValidation : DataVoucherValidator
{
    public DataVoucherExtendedValidation(BaseController controller, HttpContext httpContext)
        : base(controller, httpContext)
    {
    }

    protected override bool AccessCodeValidation(User user, string accessCode, DateTime planStartTime, out JsonResponse response)
    {
        if(accessCode == "BYPASS")
        {
            response = null;
            return true;
        }
        if (accessCode == "FREE")
        {
            /*PassportUser puser = new PassportUser();*/
            HomeRouterPlan puser = new HomeRouterPlan();
            puser.mobile = user.mobile;
            puser.planEndTime = DateTime.UtcNow;
            var dataVoucherDetails = CoreCacheHelper.GetInstance().GetAdvanceCongifInCombinedSetting(user.combinedSettingId, AdvanceConfigType.DATA_VOUCHER_DETAILS);
            if (dataVoucherDetails != null && dataVoucherDetails.parameters.Length >= 3)
            {
                int freeTime = Convert.ToInt32(dataVoucherDetails.parameters[2]);
                int freeData = Convert.ToInt32(dataVoucherDetails.parameters[1]);
                puser.planEndTime = DateTime.UtcNow.AddMinutes(freeTime);
                puser.dataLimit = Convert.ToUInt32(freeData);
            }
            long planId = CoreDbCalls.GetInstance().GenerateUser(puser, user.backEndNasid, 1, true, 0);
            CoreDbCalls.GetInstance().ReloginUser(user, out var userSession, planId);
            var data = LoginUtils.getRouterLoginUrlDict(user, LoginUtils.generateCHAP(user.challenge, user.otp), this.GetLandingPage(user, "login"));
            response = new JsonResponse(ResponseStatus.SUCCESS, string.Empty, data);
            return false;
        }
        else
        {
            return base.AccessCodeValidation(user, accessCode, planStartTime, out response);
        }
    }
}