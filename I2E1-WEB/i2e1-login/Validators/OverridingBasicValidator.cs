using i2e1_core.Models;
using i2e1_core.Utilities;
using I2E1_WEB.Controllers;
using I2E1_WEB.Utilities;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using wiom_login_share.Models;

namespace I2E1_WEB.Validators;

public class AirtelValidatior : BasicValidator
{
    public AirtelValidatior(BaseController controller, HttpContext httpContext)
        : base(controller, httpContext)
    {
        this.noOfQuestionsOnFirstPage = 1;
    }
}

public class VodafoneValidatior : BasicValidator
{
    public VodafoneValidatior(BaseController controller, HttpContext httpContext)
        : base(controller, httpContext)
    {
    }

    public override ActionResult GetFirstPageTemplate(User user)
    {
        return new PhysicalFileResult("~/Templates/vodafone/index.html", "text/html");
    }
}