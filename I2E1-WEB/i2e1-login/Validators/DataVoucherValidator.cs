using i2e1_basics.Models;
using i2e1_core.Models;
using i2e1_core.Utilities;
using I2E1_WEB.Controllers;
using I2E1_WEB.Database;
using Microsoft.AspNetCore.Http;
using System;
using wiom_login_share.Models;

namespace I2E1_WEB.Validators;

public class DataVoucherValidator : BasicValidator
{
    public DataVoucherValidator(BaseController controller, HttpContext httpContext)
        : base(controller, httpContext)
    {
    }

    protected override bool AccessCodeValidation(User user, string accessCode, DateTime planStartTime, out JsonResponse response)
    {
        response = null;

        long planId = 0;// DatabaseRequest.LinkDataVoucherToUser(user.backEndNasid, user.storegroupid, user.isHomeRouter, user.mobile, accessCode, planStartTime, out var errorCode);
        if (planId == 0)
        {
            response = new ErrorResponse(ResponseStatus.FAILURE, CoreErrorCodes.PLAN_DOES_NOT_EXISTS);
            return false;
        }

        if (!string.IsNullOrEmpty(user.mac))
            CoreDbCalls.GetInstance().ReloginUser(user, out var userSession, planId);

        return true;
    }
}