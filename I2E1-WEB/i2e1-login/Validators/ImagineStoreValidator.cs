using i2e1_core.Models;
using i2e1_core.Utilities;
using I2E1_Message.Models;
using I2E1_WEB.Controllers;
using I2E1_WEB.Utilities;
using Microsoft.AspNetCore.Http;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using wiom_login_share.Models;

namespace I2E1_WEB.Validators;

public class ImagineStoreValidator: BasicValidator
{
    public ImagineStoreValidator(BaseController controller, HttpContext httpContext)
        : base(controller, httpContext)
    {
    }

    public override List<Question> GetSecondPageQuestions(string mobile, User user)
    {
        var previousTemplate = user.templateid;
        user.templateid = new List<int>(){138};
        var questions = base.GetSecondPageQuestions(mobile, user);
        user.templateid = previousTemplate;
        return questions;
    }
}