using i2e1_core.Models;
using i2e1_core.Utilities;
using I2E1_WEB.Controllers;
using I2E1_WEB.Utilities;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Web;
using wiom_login_share.Models;


namespace I2E1_WEB.Validators;

public class MyGovAuthValidator : BasicValidator
{
    public MyGovAuthValidator(BaseController controller, HttpContext httpContext)
        : base(controller, httpContext)
    {
    }

    public override ActionResult GetFirstPageTemplate(User user)
    {
        return controller.RedirectTo("https://auth.mygov.in/oauth2/authorize?response_type=code&client_id=itwoeone&redirect_uri=" + HttpUtility.UrlEncode(WebUtils.GetCurrentHost(httpContext) + "/Login/SubmitToken") + "&scope=user_profile&state=" + DateTime.UtcNow.Ticks);
    }
}