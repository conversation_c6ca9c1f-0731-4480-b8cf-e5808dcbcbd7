using i2e1_basics.Models;
using i2e1_basics.Utilities;
using i2e1_core.Models;
using i2e1_core.Models.Client;
using i2e1_core.Utilities;
using I2E1_Message.Models;
using I2E1_Message.Utils;
using I2E1_WEB.Database;
using I2E1_WEB.ExternelApiClient;
using I2E1_WEB.Utilities;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Collections.Generic;
using System.Linq;
using wifidog_core.Models;
using wifidog_core.Models.WIOM;
using wifidog_core.Utilities;
using wiom_login_share.Models;
using wiom_login_share.Utilities;
using wiom_routerplan_share.Models.RouterPlan;

namespace I2E1_WEB.Validators;

public class BasicValidator
{
    public int noOfQuestionsOnFirstPage;
    public int noOfQuestionsOnSecondPage;
    public RouterBasicDetails routerBasicDetails;
    protected BaseController controller;
    protected HttpContext httpContext;

    public BasicValidator(BaseController controller, HttpContext httpContext)
    {
        this.controller = controller;
        this.httpContext = httpContext;
        this.noOfQuestionsOnFirstPage = 2;
        this.noOfQuestionsOnSecondPage = 1;
    }

    public ActionResult ValidateFirstState(User user)
    {
        bool foundInCookie = false;
        user.attributes.TryGetValue("uniqueplankey", out var uniqueplankey);
        if (string.IsNullOrEmpty(uniqueplankey))
        {
            foundInCookie = true;
            uniqueplankey = CookieUtils.GetCookie(httpContext, "uniqueplankey");
        }
            

        if (!string.IsNullOrEmpty(uniqueplankey))
		{
            var keys = uniqueplankey.Split('.');
            user.mobile = keys[0];
            if (!string.IsNullOrEmpty(user.mobile))
            {
                string identifer = keys[1];
				var freePlans = DbCalls.GetInstance().GetFreePlansCreated(user, DateTime.UtcNow.Date, "PAY_ONLINE");
				if (freePlans.Count >= 3 || freePlans.FirstOrDefault(m => m.status == identifer) != null)
				{
					Logger.GetInstance().Info($"ValidateFirstState. Limit reached. Count: {freePlans.Count}, mobile:{user.mobile}, cookie:{foundInCookie}");
				}
                else
                {
                    Logger.GetInstance().Info($"Creating plan for key: {uniqueplankey}, mobile:{user.mobile}, cookie:{foundInCookie}");
                    DbCalls.GetInstance().CreatePlanForPayment(user, freePlans.Count, identifer, true);
                }
			}
		}
		if (string.IsNullOrEmpty(user.mobile))
			user.mobile = WifidogDbCalls.GetInstance().GetMobileFromMac(user);

        if (string.IsNullOrEmpty(user.mobile))
        {
            user.mobile = CookieUtils.GetCookie(httpContext, "otp-verified");
        }

        bool isWaniMobile = false;
		if (string.IsNullOrEmpty(user.mobile))
        {
			string waniMobile = CookieUtils.GetCookie(httpContext, Constants.WANI_COOKIE_NAME);
			isWaniMobile = !string.IsNullOrEmpty(waniMobile);
			if (isWaniMobile)
			{
				user.mobile = waniMobile;
			}
		}
        return this.FirstStateValidation(user, isWaniMobile);
    }

    public JsonResponse ValidateOTP(User user)
    {
        if (string.IsNullOrEmpty(user.otp))
            return new JsonResponse(ResponseStatus.FAILURE, "Invalid OTP");
        bool res = DatabaseRequest.CheckOTP(user);
        if (res)
        {
            return new JsonResponse(ResponseStatus.SUCCESS, "Valid OTP");
        }
        else
        {
            return new JsonResponse(ResponseStatus.FAILURE, "Invalid OTP");
        }
    }

    public JsonResponse ValidateSecondState(User user, string accessCode, List<Question> questions, bool doLogin, List<PDOPlan> plans)
    {
        if (string.IsNullOrEmpty(user.mobile))
        {
            return new JsonResponse(ResponseStatus.FAILURE, "Connectivity Issue. Please refresh the page.");
        }
        JsonResponse handled;
        if (!this.AccessCodeValidation(user, accessCode, DateTime.UtcNow, out handled))
        {
            if (questions != null && questions.Count > 0)
            {
                try
                {
                    SubmitAnswers(user, questions);
                }
                catch(Exception ex)
                {
                    WebUtils.LogErrorToCosmos("exception in submitting answer", new { response = ex.ToString() });
                    return new JsonResponse(ResponseStatus.FAILURE, "Invalid Answer");
                }
            }
            return handled;
        }

        if (questions != null && questions.Count > 0)
        {
            try
            {
                SubmitAnswers(user, questions);
            }
            catch (Exception ex)
            {
                WebUtils.LogErrorToCosmos("exception in submitting answer", new { response = ex.ToString() });
                return new JsonResponse(ResponseStatus.FAILURE, "Invalid Answer");
            }
        }

        bool res = false;
        var conf = WifidogCacheHelper.GetInstance().GetFDMConfig(user, user.isVip);
        UserSession userSession = null;
        if (user.clientAuthType == AuthType.REGISTERED_MOBILE || user.clientAuthType == AuthType.NATIONAL_ID || user.clientAuthType == AuthType.DATA_VOUCHER_WITHOUT_OTP)
        {
            if (conf.Count == 0 || FDMConfig.IsConfigExpired(conf[0]))
            {
                return new JsonResponse(ResponseStatus.FAILURE, "Please contact reception for internet access");
            }
            else if (user.clientAuthType == AuthType.NATIONAL_ID || user.clientAuthType == AuthType.DATA_VOUCHER_WITHOUT_OTP)
            {
                CoreDbCalls.GetInstance().ReloginUser(user, out userSession, conf[0].id);
                res = true;
            }
            else if (user.clientAuthType == AuthType.REGISTERED_MOBILE)
                res = DatabaseRequest.SubmitOTP(user, conf[0].id, out userSession, doLogin);
        }
        else
            res = DatabaseRequest.SubmitOTP(user, 0, out userSession, doLogin);

        if(plans != null && plans.Count > 0)
        {
            userSession = WifidogCacheHelper.GetInstance().GetUserSessions(user, false, out var macMappingExists);
            userSession = WifidogUtil.ValidateUserSession(user, userSession, true, out var radiusUserResponse, out bool deviceLimitReached);
            if (userSession != null && userSession.radiusUserResponse == RadiusUserResponse.SUCCESS)
            {
                CoreDbCalls.GetInstance().ReloginUser(user, out userSession, userSession.planId, true);
            }
        }

        if (res)
        {
            this.PostSubmitOTPValidation(user);
          
            var data = LoginUtils.getRouterLoginUrlDict(user, LoginUtils.generateCHAP(user.challenge, user.otp), this.GetLandingPage(user, "login"));
            if (userSession != null && userSession.radiusUserResponse == RadiusUserResponse.SUCCESS)
                data.Add("sessionExists", true);

            return new JsonResponse(ResponseStatus.SUCCESS, string.Empty, data);
        }
        else
        {
            return new JsonResponse(ResponseStatus.FAILURE, "Invalid OTP");
        }
    }

    public JsonResponse ValidatePlan(User user, string accessCode, DateTime planStartTime)
    {
        if (string.IsNullOrEmpty(user.mobile))
        {
            return new JsonResponse(ResponseStatus.FAILURE, "Connectivity Issue. Please refresh the page.");
        }

        if (!this.AccessCodeValidation(user, accessCode, planStartTime, out var handled))
        {
            return handled;
        }

        var data = LoginUtils.getRouterLoginUrlDict(user, LoginUtils.generateCHAP(user.challenge, user.otp), this.GetLandingPage(user, "login"));
        return new JsonResponse(ResponseStatus.SUCCESS, string.Empty, data);
    }

    public virtual ActionResult GetFirstPageTemplate(User user)
    {
        var lang = CookieUtils.GetCookie(httpContext, "language");
        if (string.IsNullOrEmpty(lang))
        {
            var langPref = CoreCacheHelper.GetInstance().GetAdvanceCongifInCombinedSetting(user.combinedSettingId, AdvanceConfigType.LANGUAGE_PREFERENCE);
            if (langPref != null && langPref.parameters != null && langPref.parameters.Length > 0)
                lang = langPref.parameters[0];
            else
                lang = "hi";
        }
            
        controller.ViewBag.locale = lang;
        Template template = CacheHelper.GetInstance().GetTemplateContent(user.GetBaseTemplateId());
        if (template.isFullOverriden)
        {
            controller.ViewBag.fullTemplatePath = template.templatePath;
            return controller.ViewTo("~/Views/Login/sdk.cshtml", user);
        }

        ActionResult result;
        switch (user.clientAuthType)
        {
            case AuthType.NATIONAL_ID:
            case AuthType.NATIONAL_ID_OR_PHONE:
                result = controller.ViewTo("~/Views/Login/national_id_authentication.cshtml", user);
                break;
            case AuthType.LAST_NAME_ROOM_NO:
                result = controller.ViewTo("~/Views/Login/last_name_room_no_authentication.cshtml", user);
                break;
            case AuthType.SOCIAL_LOGIN:
                result = controller.ViewTo("~/Views/Login/social_login_authentication.cshtml", user);
                break;
            case AuthType.PHONE:
            case AuthType.PHONE_OR_NATIONAL_ID:
            case AuthType.REGISTERED_MOBILE:
            case AuthType.REGISTERED_MOBILE_OR_NATIONAL_ID:
            case AuthType.WANI_LOGIN:
            default:
                result = controller.ViewTo("~/Views/Login/phone_number_authentication.cshtml", user);
                break;
        }
        return result;
    }

    protected virtual ActionResult FirstStateValidation(User user, bool initiatedFromWaniApp)
    {
        if (!string.IsNullOrEmpty(user.mobile))
        {
            if (user.isHomeRouter)
                user.res = UserState.notyet;

            if (user.res == UserState.logoff)
            {
                DatabaseRequest.LogoutUser(user.backEndNasid, user.storegroupid, user.mobile, user.isHomeRouter);
            }
            else
            {
                UserSession userSession = WifidogCacheHelper.GetInstance().GetUserSessions(user, false, out var macMappingExists);
                userSession = WifidogUtil.ValidateUserSession(user, userSession, false, out var radiusUserResponse, out bool deviceLimitReached);
                if (radiusUserResponse == RadiusUserResponse.SUCCESS)
                {
                    LoginResponse loginResponse = LoginUtils.getRouterLoginUrl(user, LoginUtils.generateCHAP(user.challenge, user.otp), this.GetLandingPage(user, "autologin"));
                    controller.ViewBag.name = user.name;
                    return controller.ViewTo("~/Views/Login/handle_logged_in_response.cshtml", loginResponse);
                }
                else
                {
                    var plans = CoreCacheHelper.GetInstance().GetActivePlansOnNas(user.backEndNasid);
                    if (plans != null && plans.Count > 0)
                    {
                        if (user.isHomeRouter)
                        {
                            if (deviceLimitReached)
                                return controller.Redirect(WebUtils.GetHomeRouterBuild("deviceLimitReached", user));

                            if (CoreDbCalls.GetInstance().GetPlanExpiryByDeviceLimit(user.backEndNasid, Constants.HOME_ROUTER_DEVICE_LIMIT, out var mobile, out var firstRechargeTime, out var expiryTime, out var passportUser))
                            {
                                user.mobile = mobile;
                                return controller.Redirect(WebUtils.GetHomeRouterBuild("internetStopped", user, "planExpiry="+ expiryTime.ToString("o")));
                            }
                        }
                        else if(!UserAttribute.CheckIfVip(user.mobile, user.backEndNasid))
                        {
                            if (user.IsWaniNas())
                            {
                                if (initiatedFromWaniApp)
                                {
                                    controller.ViewBag.state = "Data Voucher State";
                                    controller.ViewBag.plans = plans;
                                }
                            }
                            else
                            {
                                controller.ViewBag.state = "Data Voucher State";
                                controller.ViewBag.plans = plans;
                            }
                        }
                    }
                    else if (userSession != null)
                    {
                        if (radiusUserResponse == RadiusUserResponse.SESSION_EXHAUSTED)
                        {
                            if (user.clientAuthType == AuthType.PHONE)
                            {
                                controller.ViewBag.sessionExpired = true;
                            }
                        }
                        else if (radiusUserResponse == RadiusUserResponse.DATA_EXHAUSTED)
                        {
                            controller.ViewBag.dataExhausted = true;
                        }
                    }
                    else if (user.clientAuthType == AuthType.PHONE
                        && (radiusUserResponse == RadiusUserResponse.SESSION_EXHAUSTED || radiusUserResponse == RadiusUserResponse.NOT_LOGGED_IN))
                    {
                        controller.ViewBag.name = user.name;

                        CoreDbCalls.GetInstance().ReloginUser(user, out userSession, 0, false);
                        LoginResponse loginResponse = LoginUtils.getRouterLoginUrl(user, LoginUtils.generateCHAP(user.challenge, user.otp), this.GetLandingPage(user, "autologin"));
                        return controller.ViewTo("~/Views/Login/handle_logged_in_response.cshtml", loginResponse);
                    }
                }
            }
        }
        return null;
    }


    public virtual List<Question> GetSecondPageQuestions(string mobile, User user) {
        List<QuestionType> types = new List<QuestionType>();
        types.Add(QuestionType.SECOND_PAGE);
        types.Add(QuestionType.SECOND_PAGE_UPPER_QUESTION);
        if (string.IsNullOrEmpty(mobile))
            return DatabaseRequest.GetSecondPageQuestions(user.backEndNasid, user.mobile, user.GetBaseTemplateId(), types, httpContext);
        else
            return DatabaseRequest.GetSecondPageQuestions(user.backEndNasid, mobile, user.GetBaseTemplateId(), types, httpContext);
    }

    public virtual List<Question> GetFirstPageQuestions(string mobile, User user, List<QuestionType> questionTypes)
    {
        return DatabaseRequest.GetQuestions(mobile, user.GetBaseTemplateId(), questionTypes, CoreSessionUtils.GetLoginServerUser(httpContext));
    }

    public virtual JsonResponse GenerateOTPValidation(User user, FDMConfig fdmConf)
    {
        if (user.mobile == "3333333333") { user.otp = "1111"; }
        else user.otp = new Random().Next(1000, 10000).ToString();

        if ((user.clientAuthType == AuthType.NATIONAL_ID || user.clientAuthType == AuthType.REGISTERED_MOBILE))
        {
            if (FDMConfig.IsConfigExpired(fdmConf))
            {
                /*PassportUser pUser = new PassportUser()
                {
                    mobile = user.mobile,
                    otpExpiryTime = DateTime.UtcNow.AddDays(1)
                };*/
                HomeRouterPlan hUser = null;
                SecondaryRouterPlan sUser = null;

                if(user.storegroupid == 1)
                {
                    sUser = new SecondaryRouterPlan()
                    {
                        mobile = user.mobile,
                        planEndTime = DateTime.UtcNow.AddDays(1)
                    };
                }
                else
                {
                    hUser = new HomeRouterPlan()
                    {
                        mobile = user.mobile,
                        planEndTime = DateTime.UtcNow.AddDays(1),
                        nasId = user.backEndNasid
                    };
                }

                if(user.storegroupid == 1)
                {
                    CoreDbCalls.GetInstance().GenerateUser(sUser, user.backEndNasid, 4, false, 0);
                }
                else
                {
                    CoreDbCalls.GetInstance().GenerateUser(hUser, user.backEndNasid, 4, false, 0);
                }

                return new JsonResponse(ResponseStatus.FAILURE, "Please contact reception for internet access", null);
            }
        }

        int deviceCount;
        var plans = CoreCacheHelper.GetInstance().GetActivePlansOnNas(user.backEndNasid);
        if (plans != null && plans.Count > 0)
            deviceCount = 10;
        else
            deviceCount = CoreCacheHelper.GetInstance().GetNoOfDeviesPerUser(user.combinedSettingId);

        OTPState state = DatabaseRequest.GenerateOTP(user, deviceCount,httpContext);

        if (state == OTPState.DEVICE_COUNT_REACHED)
        {
            return new JsonResponse(ResponseStatus.FAILURE, "You are logged in on multiple devices. First log them out.", null);
        }
        else if (state == OTPState.MOBILE_COUNT_REACHED)
        {
            return new JsonResponse(ResponseStatus.FAILURE, "Cannot Login Using Multiple Mobile Numbers on Same Device", null);
        }

        string msg = null;

        if (!I2e1ApiClient.ClientUserValidator(user, out msg))
        {
            user.mobile = null;
            return new JsonResponse(ResponseStatus.FAILURE, msg);
        }

        return this.GenerateI2e1OTPValidation(user);
    }

    protected JsonResponse GenerateI2e1OTPValidation(User user)
    {
        if (user.clientAuthType == AuthType.NATIONAL_ID)
        {
            if (!user.askaccesscode)
            {
                var res = DatabaseRequest.SubmitOTP(user, 0, out UserSession userSession);
                if (res)
                {
                    var data = LoginUtils.getRouterLoginUrlDict(user, LoginUtils.generateCHAP(user.challenge, user.otp), this.GetLandingPage(user, "login"));
                    return new JsonResponse(ResponseStatus.SUCCESS, string.Empty, data);
                }
                else
                {
                    return new JsonResponse(ResponseStatus.FAILURE, "Carry your ID to the Reception and ask for registration", null);
                }
            }
        }
        else if (user.clientAuthType.Value == AuthType.PHONE ||
            user.clientAuthType.Value == AuthType.REGISTERED_MOBILE ||
            user.clientAuthType.Value == AuthType.DATA_VOUCHER ||
            user.clientAuthType == AuthType.PHONE_OR_DATA_VOUCHER_WITHOUT_OTP)
        {
            if (user.mobile != "3333333333" && !user.IsWaniNas())
                OverrideOTPSms(user);
        }

        return new JsonResponse(ResponseStatus.SUCCESS, "", user.mobile);
    }

    protected virtual void OverrideOTPSms(User user) {
        if (user.mobile.StartsWith("+") && !user.mobile.StartsWith("+91"))
        {
        }
        else
        {
            if (user.smsapi == "change")
            {
                CoreSmsSender.SendInfiniOTP(user.mobile, user.otp);
            }
            else
            {
                CoreSmsSender.SendGupshupSms(user.mobile, user.otp);
            }
        }
    }

    protected virtual bool AccessCodeValidation(User user, string accessCode, DateTime planStartTime, out JsonResponse response)
    {
        response = null;
        if (user.askaccesscode)
        {
            response = new JsonResponse(ResponseStatus.FAILURE, "Invalid Access Code", null);
            return false;
        }
        return true;
    }

    public virtual void PostSubmitOTPValidation(User user)
    {
    }

    public virtual JsonResponse PostUserRegistration(User user, UserProfile userProfile)
    {
        return null;
    }

    public virtual void SubmitAnswers(User user, List<Question> questions)
    {
        List<Question> questions1 = new List<Question>();

        foreach (var question in questions)
        {
            switch (question.questionKey)
            {
                case "EMAIL":
                    if (question.id != 8)
                    {
                        questions1.Add(new Question()
                        {
                            id = 8,
                            answer = question.answer,
                            templateId = question.templateId,
                            mobile = question.mobile,
                            answerType = question.answerType,
                            displayIndex = question.displayIndex,
                            optionsOrder = question.optionsOrder,
                            displayTime = question.displayTime,
                            selectionTime = question.selectionTime
                        });
                    }
                    break;
            }
        }
        questions.AddRange(questions1);
        DatabaseRequest.SubmitAnswers(user.mobile, user.backEndNasid, questions[0].templateId, questions);
    }

    public virtual string GetLandingPage(User user, string source)
    {
        user.attributes["source"] = source;
        CoreSessionUtils.SetLoginServerUser(user,httpContext);
        return $"https://{httpContext.Request.Host}/NewLanding?res=success&utm_source=NAS-{user.backEndNasid}&nasid={user.backEndNasid}&userid={user.mobile}&macid={user.mac}&mode={source}&sessionid={user.GetToken()}";
    }

    public virtual FDMConfig GetFDMConfig(User user)
    {
        var conf = WifidogCacheHelper.GetInstance().GetFDMConfig(user, user.isVip);
        return conf.Count == 0 ? null : conf[0];
    }
}