using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Http;
using System.Threading.Tasks;

namespace i2e1_login.MiddleWare;

public class GetProxyContentMiddleWare
{

    // Must have constructor with this signature, otherwise exception at run time
    public GetProxyContentMiddleWare(RequestDelegate next)
    {
        // This is an HTTP Handler, so no need to store next
    }

    public async Task Invoke(HttpContext context)
    {
        var response = GenerateResponse(context);
        await context.Response.Body.WriteAsync(response);
    }

    // ...

    private byte[] GenerateResponse(HttpContext context)
    {
        var strURL = context.Request.Query["url"];

        var webClient = new System.Net.WebClient();
        try
        {
            var data = webClient.DownloadData(strURL);
            //context.Response.Headers.Clear();
            var myWebHeaderCollection = webClient.ResponseHeaders;

            for (int i = 0; i < myWebHeaderCollection.Count; i++)
            {
                if (myWebHeaderCollection.GetKey(i).ToLower() == "content-type")
                    context.Response.ContentType = myWebHeaderCollection.Get(i);
            }
            return data;
        }
        catch
        {
            return new byte[1] { 0 };
        }
    }
}

public static class GetProxyContentExtensions
{
    public static IApplicationBuilder UseGetProxyContent(this IApplicationBuilder builder)
    {
        return builder.UseMiddleware<GetProxyContentMiddleWare>();
    }
}
