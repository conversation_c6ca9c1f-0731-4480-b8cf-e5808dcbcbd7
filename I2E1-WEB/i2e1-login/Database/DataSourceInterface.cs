using i2e1_core.Utilities;
using I2E1_Message.Utils;
using System;
using System.Collections.Generic;
using System.Linq;

namespace I2E1_Message.DataSource
{
    public abstract class DataSourceInterface
    {
        public virtual Dictionary<string, string> FilterByLocation(List<int> locations, DateTime fdate, DateTime tdate)
        {
            throw new NotImplementedException();
        }
        public virtual Dictionary<string, string> FilterByAnalytics(Dictionary<string, string> targets, string[]  genders, string[]  affluences)
        {
            throw new NotImplementedException();
        }
        public virtual Dictionary<string, string> GetPreparedPage(int campaign, List<string> cities, string[]  genders, string[]  affluences, int pageNumber, int pageSize)
        {
            throw new NotImplementedException();
        }

        public virtual double GetDeliveryCostPerTarget(params int[] deliveryTypes)
        {
            throw new NotImplementedException();
        }

        public virtual double GetClickCostPerTarget(params int[] deliveryTypes)
        {
            double cost = 0;
            foreach (int deliveryType in deliveryTypes)
            {
                switch (deliveryType)
                {
                    case 1: cost += 0; break;
                    case 8: cost += 0; break;
                    case 21: cost += 0; break;
                }
            }
            return cost;
        }
    }
}
