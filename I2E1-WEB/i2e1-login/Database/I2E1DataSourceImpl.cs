using i2e1_basics.Utilities;
using i2e1_core.Utilities;
using I2E1_Message.Utils;
using System.Collections.Generic;
using System.Linq;

namespace I2E1_Message.DataSource
{
    public class I2E1DataSourceImpl : DataSourceInterface
    {
        public override Dictionary<string, string> GetPreparedPage(int campaign, List<string> cities, string[]  genders, string[]  affluences, int pageNumber, int pageSize)
        {
            string query = @"SELECT device_mac, username FROM footfall.p1_target_macs
                where LENGTH(username) = 10 and SUBSTR(username, 1, 1) in ('6', '7', '8', '9') and campaign_id = " + campaign;

            if (cities.Count > 0)
                query += " and Lower(primary_city) in ('" + string.Join("', '", cities.ConvertAll(m=>m.ToLower()))+ "')";
            if (affluences != null && affluences.Length > 0)
                query += " and affluence in ('" + string.Join("','", affluences) + "','" + string.Join("','", affluences.Select(m => m.ToggleCase())) + "')";
            if (genders != null && genders.Length > 0)
                query += " and gender in ('" + string.Join("','", genders) + "','" + string.Join("','", genders.Select(m => m.ToggleCase())) + "')";
            query += " order by username LIMIT " + pageSize + " OFFSET " + ((pageNumber - 1) * pageSize);

            var list = new Dictionary<string, string>();
            return list;
        }

        public override double GetDeliveryCostPerTarget(int[] deliveryTypes)
        {
            double cost = 0;
            foreach (int deliveryType in deliveryTypes)
            {
                switch (deliveryType)
                {
                    case 1: cost += .40; break;
                    case 8: cost += 0; break;
                    case 21: cost += 0; break;
                }
            }
            return cost;
        }
    }
}
