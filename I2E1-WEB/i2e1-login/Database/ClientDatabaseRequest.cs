using i2e1_basics.Database;
using i2e1_basics.Models;
using i2e1_basics.Utilities;
using i2e1_core.Models;
using i2e1_core.Models.Client;
using i2e1_core.Models.WIOM;
using i2e1_core.Utilities;
using I2E1_Message.Models;
using I2E1_Message.Models.Client;
using I2E1_Message.Utils;
using I2E1_WEB.MiddleTier;
using I2E1_WEB.Models;
using I2E1_WEB.Models.Client;
using I2E1_WEB.Utilities;
using Microsoft.AspNetCore.Http;
using MySql.Data.MySqlClient;
using Newtonsoft.Json;
using System;
using System.Collections;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.IO;
using System.Text;
using wiom_login_share.Models;
using wiom_router_api.Models;
using Constants = i2e1_core.Utilities.Constants;

namespace I2E1_WEB.Database;

public class ClientDatabaseRequest
{
    public static bool updateSaltId(LongIdInfo adminId, string saltId, string sqlFormattedDate)
    {
        return new ShardQueryExecutor<bool>(new GetSqlCommand((out ResponseType res) =>
        {
            SqlCommand cmd = new SqlCommand(@"update t_admin 
                                                set salt_id=@saltId, 
                                                salt_added_time=@sqlFormattedDate 
                                                where user_id = @user_id");

            cmd.Parameters.Add(new SqlParameter("@saltId", saltId.ToString()));
            cmd.Parameters.Add(new SqlParameter("@sqlFormattedDate", sqlFormattedDate));
            cmd.Parameters.Add(new SqlParameter("@user_id", adminId.ToSafeDbObject()));
            res = ResponseType.NONQUERY;
            return cmd;
        }), adminId.shard_id,
        new ResponseHandler<bool>((reader) =>
        {
            return true;
        })).Execute();
    }

    public static Dictionary<string, object> getNasDetails(LongIdInfo nasid)
    {
        return new ShardQueryExecutor<Dictionary<string, object>>(new GetSqlCommand((out ResponseType res) =>
        {
            SqlCommand cmd = new SqlCommand("select top 1 ts.router_nas_id, tdb.mac, ts.shop_name from t_store ts INNER JOIN t_device_build tdb on ts.router_nas_id = tdb.router_nas_id where ts.router_nas_id = @nasid and mac is not null order by tdb.modified_time desc");
            cmd.Parameters.Add(new SqlParameter("@nasid", nasid.ToSafeDbObject()));
            res = ResponseType.READER;
            return cmd;
        }), nasid.shard_id,
        new ResponseHandler<Dictionary<string, object>>((reader) =>
        {
            Dictionary<string, object> data = new Dictionary<string, object>();
            if (reader.Read())
            {
                data.Add("shopName", reader["shop_name"].ToString());
                data.Add("nasId", reader["router_nas_id"].ToString());
                data.Add("macId", reader["mac"].ToString());
                return data;
            }
            return data;
        })).Execute();
    }

    public static ManagementUser getUserForPasswordReset(long shardId,string token)
    {
        return new ShardQueryExecutor<ManagementUser>(new GetSqlCommand((out ResponseType res) =>
        {
            SqlCommand cmd = new SqlCommand("select user_id, username, salt_id, salt_added_time FROM t_admin where salt_id = @token");
            cmd.Parameters.Add(new SqlParameter("@token", token));
            res = ResponseType.READER;
            return cmd;
        }), shardId,
        new ResponseHandler<ManagementUser>((reader) =>
        {
            ManagementUser user = new ManagementUser();
            if (reader.Read())
            {
                user.userid = new LongIdInfo(shardId,DBObjectType.USER_TYPE,Convert.ToInt64((int)reader["user_id"]));
                user.name = reader["username"].ToString();
                user.token = reader["salt_id"].ToString();
                user.tokengenratedtime = (DateTime)reader["salt_added_time"];
                return user;
            }
            return null;
        })).Execute();
    }

    public static bool resetUserPassword(LongIdInfo userid, string password)
    {
        return new ShardQueryExecutor<bool>(new GetSqlCommand((out ResponseType res) =>
        {
            SqlCommand cmd = new SqlCommand(@"update t_admin 
                                                set password = @password, auth_type = 0
                                                where user_id = @userid");

            cmd.Parameters.Add(new SqlParameter("@password", password.ToString()));
            cmd.Parameters.Add(new SqlParameter("@userid", userid.ToSafeDbObject()));
            res = ResponseType.NONQUERY;
            return cmd;
        }), userid.shard_id,
        new ResponseHandler<bool>((reader) =>
        {
            return true;
        })).Execute();
    }

    public static bool UpdateGroup(WebUserGroupNew group, int settingId, LongIdInfo longAdminId)
    {
        WebUtils.LogInfoToCosmos("updating user group in a setting", new { settingId = settingId, group = group });
        return new ShardQueryExecutor<bool>(new GetSqlCommand((out ResponseType res) =>
        {
            SqlCommand cmd = new SqlCommand("save_admin_user_group");
            cmd.CommandType = System.Data.CommandType.StoredProcedure;
            cmd.Parameters.Add(new SqlParameter("@group_id", group.groupId));
            cmd.Parameters.Add(new SqlParameter("@group_name", group.groupName));
            cmd.Parameters.Add(new SqlParameter("@setting_id", settingId));
            cmd.Parameters.Add(new SqlParameter("@parameters", string.IsNullOrEmpty(group.values) ? string.Empty : group.values));

            res = ResponseType.READER;
            return cmd;
        }), longAdminId.shard_id,
        new ResponseHandler<bool>((reader) =>
        {
            if (reader.Read())
            {
                if (settingId != 0 && group.groupId == 0)
                {
                    int groupId = (int)reader["user_group_id"];
                    Dictionary<String, BasicConfig> basicConfigs = new Dictionary<string, BasicConfig>();
                    BasicConfig config = new BasicConfig(BasicConfigType.SESSION_TIMEOUT, "3600");
                    basicConfigs.Add(BasicConfigType.SESSION_TIMEOUT.ToString(), config);
                    UpdateUserGroupBasicConfig(settingId, groupId, basicConfigs, longAdminId);
                }
            }
            return true;
        })).Execute();
    }

    public static List<int> GetNasesForMe(LongIdInfo admin_user_id)
    {
        return new ShardQueryExecutor<List<int>>(new GetSqlCommand((out ResponseType res) =>
        {
            //SqlCommand cmd = new SqlCommand("select router_nas_id from t_admin_nas_mapping where user_id = @admin_user_id");

            SqlCommand cmd = new SqlCommand(@"select router_nas_id from (
                                                    SELECT *
                                                    FROM   t_store
                                                    WHERE  partner_id IN(SELECT t_partner.partner_id AS partner_id
                                                                            FROM   t_admin_mapping

                                                                            INNER JOIN t_partner on t_partner.client_id = t_admin_mapping.mapped_id
                                                                            WHERE  admin_id = @admin_user_id
                                                                                    AND mapping_type = 'client')
                                                    UNION
                                                    SELECT *
                                                    FROM   t_store
                                                    WHERE  partner_id IN(SELECT mapped_id AS partner_id
                                                                          FROM   t_admin_mapping
                                                                          WHERE  admin_id = @admin_user_id
                                                                                 AND mapping_type = 'partner')
                                                    UNION
                                                    SELECT *
                                                    FROM   t_store
                                                    WHERE  router_nas_id IN(SELECT mapped_id AS router_nas_id
                                                                             FROM   t_admin_mapping
                                                                             WHERE  admin_id = @admin_user_id
                                                                                    AND mapping_type = 'location')) a ");
            cmd.Parameters.Add(new SqlParameter("@admin_user_id", admin_user_id.ToSafeDbObject()));

            res = ResponseType.READER;
            return cmd;
        }), admin_user_id.shard_id,
        new ResponseHandler<List<int>>((reader) =>
        {
            List<int> nases = new List<int>();
            while (reader.Read())
            {
                nases.Add((int)reader["router_nas_id"]);
            }
            return nases;
        })).Execute();
    }

    public static string SaveListChecks(LongIdInfo longNasId, ListCheckDataType listType, string data)
    {
        WebUtils.LogInfoToCosmos("Saving ListChecks for a nasid", new
        {
            nasid = longNasId,
            listType = listType,
            parameters = data
        });
        return new ShardQueryExecutor<string>(new GetSqlCommand((out ResponseType res) =>
        {
            SqlCommand cmd = new SqlCommand(@"
                IF EXISTS(select * from t_single_nas_operations where single_operation_id = @list_type and nas_id = @nas)
                BEGIN
                   select parameters from t_single_nas_operations where single_operation_id = @list_type and nas_id = @nas
                   begin update t_single_nas_operations set parameters=@data where single_operation_id = @list_type and nas_id = @nas end
                END
                ELSE
                    begin insert into t_single_nas_operations(nas_id, single_operation_id, parameters) values(@nas, @list_type, @data) end");

            cmd.Parameters.Add(new SqlParameter("@data", data));
            cmd.Parameters.Add(new SqlParameter("@list_type", listType));
            cmd.Parameters.Add(new SqlParameter("@nas", longNasId.local_value));
            res = ResponseType.READER;
            return cmd;
        }), longNasId.shard_id,
        new ResponseHandler<string>((reader) =>
        {
            CoreCacheHelper.GetInstance().ResetAll(longNasId);
            if (reader.Read())
            {
                if (reader["parameters"] != DBNull.Value)
                    return reader["parameters"].ToString();
            }
            WebUtils.LogInfoToCosmos("ListChecks saved successfully", new
            {
                nasid = longNasId,
                listType = listType
            });
            return string.Empty;
        })).Execute();
    }

    public static bool SaveSingleNasOperation(LongIdInfo longNasId, int singleNasOperationId, string value)
    {
        //WebUtils.LogInfoToCosmos("Saving single nas operation", new { nasId = nasid, singleNasOperationId = singleNasOperationId, value = value });
        return new ShardQueryExecutor<bool>(new GetSqlCommand((out ResponseType res) =>
        {
            SqlCommand cmd = new SqlCommand(@"
                IF EXISTS(select * from t_single_nas_operations where single_operation_id = @operation_id and nas_id = @nas)
                   begin update t_single_nas_operations set parameters=@data where single_operation_id = @operation_id and nas_id = @nas end
                ELSE
                    begin insert into t_single_nas_operations(nas_id, single_operation_id, parameters) values(@nas, @operation_id, @data) end");

            cmd.Parameters.Add(new SqlParameter("@data", value));
            cmd.Parameters.Add(new SqlParameter("@operation_id", singleNasOperationId));
            cmd.Parameters.Add(new SqlParameter("@nas", longNasId.local_value));
            res = ResponseType.NONQUERY;
            return cmd;
        }), longNasId.shard_id,
        new ResponseHandler<bool>((reader) =>
        {
            WebUtils.LogInfoToCosmos("single nas operation saved successfully", new { nasId = longNasId, singleNasOperationId = singleNasOperationId });
            CoreCacheHelper.GetInstance().ResetAll(longNasId);
            return true;
        })).Execute();
    }

    public static WiomSubscription getActiveSubscription(LongIdInfo longNasId)
    {
        return new ShardQueryExecutor<WiomSubscription>(new GetSqlCommand((out ResponseType res) =>
        {
            SqlCommand cmd = new SqlCommand(@"
                select top 1 * from t_subscriptions ts INNER JOIN wiomPlans wp on ts.plan_id = wp.pid where GETUTCDATE() between ts.start and ts.pause and ts.nasid = @nasid order by ts.start desc");
            cmd.Parameters.Add(new SqlParameter("@nasid", longNasId.local_value));
            res = ResponseType.READER;
            return cmd;
        }), longNasId.shard_id,
        new ResponseHandler<WiomSubscription>((reader) =>
        {
            WiomSubscription sub = new WiomSubscription();
            sub.active = false;
            if(reader.Read())
            {
                sub.active = true;
                sub.name = reader["planName"].ToString();
                sub.start = ((DateTime)reader["start"]).ToString("yyyy-MM-dd");
                sub.end = ((DateTime)reader["start"]).AddMonths(12).ToString("yyyy-MM-dd");
            }
            return sub;
        })).Execute();
    }

    public static JsonResponse getAllClients(LongIdInfo userId)
    {
        return new ShardQueryExecutor<JsonResponse>(new GetSqlCommand((out ResponseType res) =>
        {
            string query = "";
            if (userId == null || userId.local_value == 0)
            {
                query += "select client_id,client_name,legal_business_name,gst_number,contract_signed,billing_address from t_client order by client_name desc";
            }
            else
            {
                query += "select client_id,client_name,legal_business_name,gst_number,contract_signed,billing_address from t_client ";
                query += " left join t_admin_mapping on t_admin_mapping.mapped_id = t_client.client_id ";
                query += " where t_admin_mapping.admin_id = " + userId.ToSafeDbObject() + " and mapping_type='client' ";
                query += " order by client_name desc";
            }
            SqlCommand cmd = new SqlCommand(query);
            res = ResponseType.READER;
            return cmd;
        }), ShardHelper.SHARD0,
        new ResponseHandler<JsonResponse>((reader) =>
        {
            var clients_list = new List<Hashtable>();

            while (reader.Read())
            {
                Hashtable client = new Hashtable();
                client.Add("clientId", (int)reader["client_id"]);
                client.Add("clientName", reader["client_name"].ToString());
                client.Add("clientLegalBusinessName", reader["legal_business_name"].ToString());
                client.Add("clientGSTNumber", reader["gst_number"].ToString());
                client.Add("clientContractSigned", reader["contract_signed"] != DBNull.Value ? Convert.ToInt32(reader["contract_signed"]) : 0);
                client.Add("clientBillingAddress", reader["billing_address"].ToString());
                clients_list.Add(client);
            }
            return new JsonResponse(ResponseStatus.SUCCESS, "", clients_list);
        })).Execute();
    }

    public static LongIdInfo checkIfContactExists(string username)
    {
        var longIdInfo = ShardHelper.getLongUserIdFromMobile(username);
        if(longIdInfo != null)
        {
            return new ShardQueryExecutor<LongIdInfo>(new GetSqlCommand((out ResponseType res) =>
            {
                var query = "select user_id from t_admin where username= @username";
                SqlCommand cmd = new SqlCommand(query);
                cmd.Parameters.Add(new SqlParameter("@username", username));
                res = ResponseType.READER;
                return cmd;
            }), longIdInfo.shard_id,
            new ResponseHandler<LongIdInfo>((reader) =>
            {
                if (reader.Read())
                {
                    if (reader["user_id"] != DBNull.Value)
                        return new LongIdInfo(longIdInfo.shard_id, DBObjectType.ADMIN_TYPE, Convert.ToInt64(reader["user_id"]));
                }
                return null;

            })).Execute();
        }
        return null;
    }

    public static JsonResponse getAllPartners(LongIdInfo userId)
    {
        return new ShardQueryExecutor<JsonResponse>(new GetSqlCommand((out ResponseType res) =>
        {
            string query = "";
            if (userId == null)
            {
                query += "select partner_id,partner_name,t_partner.client_id,t_client.client_name,partner_cd, ";
                query += " partner_logo,partner_image,category,sub_category,micro_category,partner_type, ";
                query += " account_type,product_type,subscription_type,subscription_amt,subscription_start_date, ";
                query += " subscription_renewal_date,subscription_status,discount,t_partner.zoho_account_id,t_partner.added_time ";
                query += " from t_partner left join t_client on t_client.client_id = t_partner.client_id order by partner_name ";
            }
            else
            {
                query += "select partner_id,partner_name,t_partner.client_id,t_client.client_name,partner_cd, ";
                query += " partner_logo,partner_image,category,sub_category,micro_category,partner_type, ";
                query += " account_type,product_type,subscription_type,subscription_amt,subscription_start_date, ";
                query += " subscription_renewal_date,subscription_status,discount,t_partner.zoho_account_id,t_partner.added_time ";
                query += " from t_partner left join t_client on t_client.client_id = t_partner.client_id ";
                query += " left join t_admin_mapping on t_admin_mapping.mapped_id = t_partner.partner_id ";
                query += " where t_admin_mapping.admin_id = " + userId + " and t_admin_mapping.mapping_type='partner' ";
                query += " order by partner_name";
            }
            SqlCommand cmd = new SqlCommand(query);
            res = ResponseType.READER;
            return cmd;
        }), ShardHelper.SHARD0,
        new ResponseHandler<JsonResponse>((reader) =>
        {
            var partners_list = new List<Hashtable>();

            while (reader.Read())
            {
                Hashtable partner = new Hashtable();
                partner.Add("partnerId", (int)reader["partner_id"]);
                partner.Add("clientId", (int)reader["client_id"]);
                partner.Add("clientName", reader["client_name"].ToString());
                partner.Add("partnerName", reader["partner_name"].ToString());
                partner.Add("partnerCd", reader["partner_cd"].ToString());
                partner.Add("partnerLogo", reader["partner_logo"].ToString());
                partner.Add("partnerImage", reader["partner_image"].ToString());
                partner.Add("category", reader["category"].ToString());
                partner.Add("subCategory", reader["sub_category"].ToString());
                partner.Add("microCategory", reader["micro_category"].ToString());
                partner.Add("accountType", reader["account_type"].ToString());
                partner.Add("productType", reader["product_type"].ToString());
                partner.Add("discount", Convert.ToDecimal(reader["discount"]));
                partner.Add("zohoAccountId", reader["zoho_account_id"].ToString());
                partners_list.Add(partner);
            }
            return new JsonResponse(ResponseStatus.SUCCESS, "", partners_list);
        })).Execute();
    }


    public static I2E1Partner getPartnersDetails(string partnerName)
    {
        return new ShardQueryExecutor<I2E1Partner>(new GetSqlCommand((out ResponseType res) =>
        {
            string query = "";
            query += "select top 1 * from t_partner where partner_name= @partnerName";

            SqlCommand cmd = new SqlCommand(query);
            cmd.Parameters.Add(new SqlParameter("@partnerName", partnerName));
            res = ResponseType.READER;
            return cmd;
        }), ShardHelper.SHARD0,
        new ResponseHandler<I2E1Partner>((reader) =>
        {
            var partner = new I2E1Partner();

            if (reader.Read())
            {
                partner.partnerId = new LongIdInfo(ShardHelper.SHARD0, DBObjectType.ADMIN_TYPE, Convert.ToInt64(reader["partner_id"]));
                partner.clientId = new LongIdInfo(ShardHelper.SHARD0, DBObjectType.ADMIN_TYPE, Convert.ToInt64(reader["client_id"]));
                partner.partnerName = reader["partner_name"].ToString();
                partner.partnerCd = reader["partner_cd"].ToString();
                partner.partnerLogo = reader["partner_logo"].ToString();
                partner.partnerImage = reader["partner_image"].ToString();
                partner.category = reader["category"].ToString();
                partner.subCategory = reader["sub_category"].ToString();
                partner.microCategory = reader["micro_category"].ToString();
                partner.accountType = reader["account_type"].ToString();
                partner.productType = reader["product_type"].ToString();
                partner.discount = Convert.ToDecimal(reader["discount"]);
                partner.zohoAccountId = reader["zoho_account_id"].ToString();
            }
            return partner;
        })).Execute();
    }

    public static List<StoreInfo> getUsersMappedLocations(ManagementUser user)
    {
        return new ShardQueryExecutor<List<StoreInfo>>(new GetSqlCommand((out ResponseType res) =>
        {
            SqlCommand cmd = new SqlCommand(@"select ts.router_nas_id,ts.shop_name, ts.locality, ts.loc_close_time,
                           ts.loc_open_time, ts.memail email, ts.mmobile mobile, ts.shop_address, ts.category, ts.loc_sub_cat
                    from t_store ts
                    left join t_admin_mapping on t_admin_mapping.mapped_id = ts.router_nas_id
                    where t_admin_mapping.admin_id = @userid
                    and t_admin_mapping.mapping_type = 'location'");
            cmd.Parameters.Add(new SqlParameter("@userid", user.userid.local_value));
            res = ResponseType.READER;
            return cmd;
        }), user.userid.shard_id,
        new ResponseHandler<List<StoreInfo>>((reader) =>
        {
            List<StoreInfo> storeDetails = new List<StoreInfo>(5000);
            DateTime currentTime = DateTime.UtcNow;
            while (reader.Read())
            {
                StoreInfo details = new StoreInfo();
                details.nasid = new LongIdInfo(user.userid.shard_id,DBObjectType.ACTIVE_NAS,Convert.ToInt64((int)reader["router_nas_id"]));
                details.storeName = reader["shop_name"].ToString();
                details.address = reader["shop_address"].ToString();
                details.locality = reader["locality"].ToString();
                details.locationOpeningTime = reader["loc_open_time"].ToString();
                details.locationClosingTime = reader["loc_close_time"].ToString();
                details.category = reader["category"].ToString();
                details.subCategory = reader["loc_sub_cat"].ToString();
                details.email = reader["email"].ToString();
                details.mobile = reader["mobile"].ToString();
                storeDetails.Add(details);
            }
            return storeDetails;
        })).Execute();
    }

    public static  bool updateUserLocation(Store store, StoreDetailsStatic details)
    {
        return new ShardQueryExecutor<bool>(new GetSqlCommand((out ResponseType res) =>
        {
            SqlCommand cmd = new SqlCommand(@"UPDATE t_store SET shop_name = @shop_name, shop_address = @address,
                    category = @location_category, loc_sub_cat = @location_subcategory, loc_open_time = @location_opening_time,
                    loc_close_time = @location_closing_time, memail = @manager_email, mmobile = @manager_phone, manager_name = @manager_name,
                    legal_business_name = @legal_business_name, legal_business_address = @legal_business_address, gst_number = @gst_number                    
                    WHERE router_nas_id = @nasid");
            cmd.Parameters.Add(new SqlParameter("@nasid", store.nasid.ToSafeDbObject()));
            cmd.Parameters.Add(new SqlParameter("@shop_name", store.storeName));
            cmd.Parameters.Add(new SqlParameter("@address", store.address));
            cmd.Parameters.Add(new SqlParameter("@location_category", details.locationCategory));
            cmd.Parameters.Add(new SqlParameter("@location_subcategory", details.locationSubCategory));
            cmd.Parameters.Add(new SqlParameter("@location_opening_time", details.locationOpeningTime));
            cmd.Parameters.Add(new SqlParameter("@location_closing_time", details.locationClosingTime));
            cmd.Parameters.Add(new SqlParameter("@manager_name", details.managerName));
            cmd.Parameters.Add(new SqlParameter("@manager_email", details.managerEmail));
            cmd.Parameters.Add(new SqlParameter("@manager_phone", details.managerPhone));
            cmd.Parameters.Add(new SqlParameter("@legal_business_name", string.IsNullOrEmpty(details.legalBusinessName) ? "" : details.legalBusinessName));
            cmd.Parameters.Add(new SqlParameter("@legal_business_address", string.IsNullOrEmpty(details.legalBusinessAddress) ? "" : details.legalBusinessAddress));
            cmd.Parameters.Add(new SqlParameter("@gst_number", string.IsNullOrEmpty(details.gstNumber) ? "" : details.gstNumber));
            res = ResponseType.READER;
            return cmd;
        }), store.nasid.shard_id,
        new ResponseHandler<bool>((reader) =>
        {
            return true;
        })).Execute();
    }

    public static UserProfile getOwnerProfile(LongIdInfo longUserId)
    {
        return new ShardQueryExecutor<UserProfile>(new GetSqlCommand((out ResponseType res) =>
        {
            SqlCommand cmd = new SqlCommand("select username,name,email,mobile,url from t_admin a left join i2e1_assets b  on a.user_id=b.user_id where a.user_id=@userid");
            cmd.Parameters.Add(new SqlParameter("@userid", longUserId.local_value));
            res = ResponseType.READER;
            return cmd;
        }), longUserId.shard_id,
        new ResponseHandler<UserProfile>((reader) =>
        {
            UserProfile user = new UserProfile();
            if (reader.Read())
            {
                user.name = reader["name"].ToString();
                user.email = reader["email"].ToString();
                user.mobile = reader["mobile"].ToString();
                user.username = reader["username"].ToString();
                user.profilePhoto = reader["url"].ToString();
            }
            return user;
        })).Execute();
    }

    public static UserProfile getAdminProfile(LongIdInfo longNasId, string classification="owner")
    {
        return new ShardQueryExecutor<UserProfile>(new GetSqlCommand((out ResponseType res) =>
        {
            SqlCommand cmd = new SqlCommand(@"select *,b.url from (
                    select ta.user_id admin_id, ta.username, ta.email, ta.name, ta.mobile, tam.mapped_id nasid
                    from t_admin_mapping tam
                    left
                    join t_admin ta ON ta.user_id = tam.admin_id
                    where tam.mapping_type = 'location'
                    and tam.mapped_id = @nasid
                    and ta.classification = @classification
                 ) x LEFT JOIN i2e1_assets b on x.admin_id = b.user_id");
            cmd.Parameters.Add(new SqlParameter("@nasid", longNasId.local_value));
            cmd.Parameters.Add(new SqlParameter("@classification", classification));
            res = ResponseType.READER;
            return cmd;
        }), longNasId.shard_id,
        new ResponseHandler<UserProfile>((reader) =>
        {
            UserProfile user = new UserProfile();
            if(reader.Read())
            {
                user.id = new LongIdInfo(longNasId.shard_id,DBObjectType.ADMIN_TYPE,Convert.ToInt64((long)reader["admin_id"]));
                user.name=reader["name"].ToString();
                user.email = reader["email"].ToString();
                user.mobile = reader["mobile"].ToString();
                user.username= reader["username"].ToString();
                user.profilePhoto = reader["url"].ToString();
            }
            return user;
        })).Execute();
    }

    public static bool updateAdminProfile(LongIdInfo userid, UserProfile profile)
    {
        return new ShardQueryExecutor<bool>(new GetSqlCommand((out ResponseType res) =>
        {
            SqlCommand cmd = new SqlCommand(@"UPDATE t_admin SET name = @name, mobile = @mobile, email = @email
                    WHERE user_id = @userid;
                    IF (NOT EXISTS(SELECT * FROM i2e1_assets WHERE user_id = @userid and content_type = 1))
                    BEGIN
                        INSERT INTO i2e1_assets(user_id, url, content_type, added_time)
                        VALUES(@userid, @profile_url, 1, GETUTCDATE())
                    END
                    ELSE
                    BEGIN
                        UPDATE i2e1_assets
                        SET added_time = GETUTCDATE(),
                            url = @profile_url
                        WHERE content_type = 1 AND user_id = @userid
                    END");
            cmd.Parameters.Add(new SqlParameter("@userid", userid.ToSafeDbObject()));
            cmd.Parameters.Add(new SqlParameter("@name", profile.name));
            cmd.Parameters.Add(new SqlParameter("@mobile", profile.mobile));
            cmd.Parameters.Add(new SqlParameter("@email", profile.email));
            cmd.Parameters.Add(new SqlParameter("@profile_url", string.IsNullOrEmpty(profile.profilePhoto) ? string.Empty : profile.profilePhoto));
            res = ResponseType.READER;
            return cmd;
        }), userid.shard_id,
        new ResponseHandler<bool>((reader) =>
        {

            if (reader.Read())
            {

            }
            return true;
        })).Execute();
    }

    public static JsonResponse getClientContactPersons(int clientId)
    {
        return new ShardQueryExecutor<JsonResponse>(new GetSqlCommand((out ResponseType res) =>
        {
            SqlCommand cmd = new SqlCommand(@"select * from t_admin_mapping tam
                                                left join t_admin ta on ta.user_id=tam.admin_id
                                                where tam.mapping_type = 'client' and tam.mapped_id=@clientId
                                                and ta.user_type in (100,101,0)");

            cmd.Parameters.Add(new SqlParameter("@clientId", clientId));
            res = ResponseType.READER;
            return cmd;
        }), ShardHelper.SHARD0,
        new ResponseHandler<JsonResponse>((reader) =>
        {
            var contacts_list = new List<Hashtable>();

            while (reader.Read())
            {
                Hashtable contact = new Hashtable();
                contact.Add("userId", (int)reader["admin_id"]);
                contact.Add("username", reader["username"].ToString());
                contact.Add("name", reader["name"].ToString());
                contact.Add("userType", reader["user_type"] != null ? Convert.ToInt32(reader["user_type"]) : 0);
                contact.Add("mobile", reader["contact_no"].ToString());
                contact.Add("active", reader["active"] != DBNull.Value ? Convert.ToInt32(reader["active"]) : 0);
                contact.Add("isGreeted", reader["is_greeted"] != DBNull.Value ? Convert.ToInt32(reader["is_greeted"]) : 0);
                contacts_list.Add(contact);
            }
            return new JsonResponse(ResponseStatus.SUCCESS, "", contacts_list);
        })).Execute();
    }

    public static StoreUser UpdateContactPersons(StoreUser user, LongIdInfo longContactId, string contactType, string password, string classification = "")
    {
        return new ShardQueryExecutor<StoreUser>(new GetSqlCommand((out ResponseType res) =>
        {
            SqlCommand cmd = new SqlCommand(@"
                IF EXISTS (select 1 from t_admin where user_id=@userId or (" + (contactType == "location" ? "username != 'N/A' and " : string.Empty) + @"username=@userName))
                BEGIN
                    update t_admin set 
                    email = @email,
                    password = CASE WHEN @password is null OR @password = '' THEN password ELSE @password END,
                    name = @name, 
                    contact_no = @mobile,
                    mobile = @mobile,
                    active = @active,
                    is_greeted = @isGreeted,
                    classification = @classification
                    where user_id=@userId or username=@userName;
                    select user_id,username,name,auth_type,user_type,contact_no,CAST(active AS INT) AS active,CAST(is_greeted AS INT) AS is_greeted,email,mobile, classification from t_admin
                    where user_id=@userId or username=@userName;
                END
                ELSE
                BEGIN
                    DECLARE @ObjectID int
                    insert into t_admin(username,password,name,auth_type,user_type,contact_no,mobile,active,is_greeted,email,classification, is_password_temporary)
                    values(@userName,@password,@name, 0, @userType,@mobile,@mobile,@active, @isGreeted,@email,@classification, 1);
                    SET @ObjectID = SCOPE_IDENTITY();
                    insert into t_admin_mapping(admin_id,mapped_id,mapping_type) values(@ObjectID, @contact_id, @contact_type);
                    select user_id,username,name,auth_type,user_type,mobile,contact_no,CAST(active AS INT) AS active,CAST(is_greeted AS INT) AS is_greeted, classification, email from t_admin where user_id = @ObjectID;
                END");
            cmd.Parameters.Add(new SqlParameter("@classification", classification));
            cmd.Parameters.Add(new SqlParameter("@userId", user.userId.ToSafeDbObject()));
            cmd.Parameters.Add(new SqlParameter("@userName", user.username.Trim()));
            cmd.Parameters.Add(new SqlParameter("@email", string.IsNullOrEmpty(user.email) ? string.Empty : user.email.Trim()));
            cmd.Parameters.Add(new SqlParameter("@password", password));
            cmd.Parameters.Add(new SqlParameter("@name", user.name));
            cmd.Parameters.Add(new SqlParameter("@authType", user.authType));
            cmd.Parameters.Add(new SqlParameter("@userType", user.userType));
            cmd.Parameters.Add(new SqlParameter("@mobile", user.mobile));
            cmd.Parameters.Add(new SqlParameter("@active", user.active));
            cmd.Parameters.Add(new SqlParameter("@contact_id", longContactId.local_value));
            cmd.Parameters.Add(new SqlParameter("@contact_type", contactType));
            cmd.Parameters.Add(new SqlParameter("@isGreeted", user.isGreeted));
            res = ResponseType.READER;
            return cmd;
        }), longContactId.shard_id,
        new ResponseHandler<StoreUser>((reader) =>
        {
            var storeuser_list = new StoreUser();

            while (reader.Read())
            {
                storeuser_list.classification = reader["classification"].ToString();
                storeuser_list.userId = new LongIdInfo(longContactId.shard_id,DBObjectType.USER_TYPE,Convert.ToInt64(reader["user_id"]));
                storeuser_list.username = reader["username"].ToString();
                storeuser_list.email = reader["email"].ToString();
                storeuser_list.name = reader["name"].ToString();
                storeuser_list.authType = (reader["auth_type"] != null ? (AdminAuthType)(byte)(reader["auth_type"]) : 0);
                storeuser_list.userType = (reader["user_type"] != null ? (AdminUserType)(byte)(reader["user_type"]) : 0);
                storeuser_list.mobile = reader["mobile"].ToString();
                storeuser_list.active = reader["active"] != DBNull.Value ? (int)reader["active"] : 0;
                storeuser_list.isGreeted = reader["is_greeted"] != DBNull.Value ? (int)reader["is_greeted"] : 0;
            }
            return storeuser_list;
        })).Execute();
    }

    public static StoreUser UpdateContactPersons(StoreUser user, int contactId, string contactType, string password)
    {
        return new ShardQueryExecutor<StoreUser>(new GetSqlCommand((out ResponseType res) =>
        {
            SqlCommand cmd = new SqlCommand(@"
                IF EXISTS (select 1 from t_admin where user_id=@userId or ("+ (contactType == "location" ? "username != 'N/A' and ": string.Empty) + @"username=@userName))
                BEGIN
                    update t_admin set 
                    username = @userName,
                    password = CASE WHEN @password is null OR @password = '' THEN password ELSE @password END,
                    name = @name, 
                    contact_no = @mobile,
                    active = @active,
                    is_greeted = @isGreeted
                    where user_id=@userId 
                    select user_id,username,name,auth_type,user_type,contact_no,CAST(active AS INT) AS active,CAST(is_greeted AS INT) AS is_greeted from t_admin
                    where user_id=@userId or username=@userName
                END
                ELSE
                BEGIN
                    DECLARE @ObjectID int
                    insert into t_admin(username,password,name, auth_type,user_type,contact_no,active,is_greeted)
                    values(@userName,@password,@name, 0, @userType,@mobile,@active, @isGreeted);
                    SET @ObjectID = SCOPE_IDENTITY();
                    insert into t_admin_mapping(admin_id,mapped_id,mapping_type) values(@ObjectID, @contact_id, @contact_type);
                    select user_id,username,name,auth_type,user_type,contact_no,CAST(active AS INT) AS active,CAST(is_greeted AS INT) AS is_greeted from t_admin where user_id = @ObjectID;
                END");
            cmd.Parameters.Add(new SqlParameter("@userId", user.userId.local_value));
            cmd.Parameters.Add(new SqlParameter("@userName", user.username.Trim()));
            cmd.Parameters.Add(new SqlParameter("@password", password));
            cmd.Parameters.Add(new SqlParameter("@name", user.name));
            cmd.Parameters.Add(new SqlParameter("@authType", user.authType));
            cmd.Parameters.Add(new SqlParameter("@userType", user.userType));
            cmd.Parameters.Add(new SqlParameter("@mobile", user.mobile));
            cmd.Parameters.Add(new SqlParameter("@active", user.active));
            cmd.Parameters.Add(new SqlParameter("@contact_id", contactId));
            cmd.Parameters.Add(new SqlParameter("@contact_type", contactType));
            cmd.Parameters.Add(new SqlParameter("@isGreeted", user.isGreeted));
            res = ResponseType.READER;
            return cmd;
        }),user.userId.shard_id,
        new ResponseHandler<StoreUser>((reader) =>
        {
            var storeuser_list = new StoreUser();

            while (reader.Read())
            {
                storeuser_list.userId = new LongIdInfo(user.userId.shard_id, DBObjectType.USER_TYPE, Convert.ToInt64((int)reader["user_id"]));
                storeuser_list.username = reader["username"].ToString();
                storeuser_list.name = reader["name"].ToString();
                storeuser_list.authType = (reader["auth_type"] != null ? (AdminAuthType)(byte)(reader["auth_type"]) : 0);
                storeuser_list.userType = (reader["user_type"] != null ? (AdminUserType)(byte)(reader["user_type"]) : 0);
                storeuser_list.mobile = reader["contact_no"].ToString();
                storeuser_list.active = reader["active"] != DBNull.Value ? (int)reader["active"] : 0;
                storeuser_list.isGreeted = reader["is_greeted"] != DBNull.Value ? (int)reader["is_greeted"] : 0;
            }
            return storeuser_list;
        })).Execute();
    }

    public static string getClientsLargestProductType(int clientId)
    {
        return new ShardQueryExecutor<string>(new GetSqlCommand((out ResponseType res) =>
        {
            SqlCommand cmd = new SqlCommand(@"select product_type from (select t_partner.*,
                                                    dense_rank() over (
                                                        order by case when product_type = 'One' then 1
                                                        when product_type = 'Prime' then 2
                                                        when product_type = 'Plus' then 3
                                                        end) as rnk
                                                    from t_partner where client_id = @clientId
                                                    ) a where rnk = 1");

            cmd.Parameters.Add(new SqlParameter("@clientId", clientId));
            res = ResponseType.READER;
            return cmd;
        }), ShardHelper.SHARD0,
        new ResponseHandler<string>((reader) =>
        {
            string product = String.Empty;
            if (reader.Read())
            {
                product = reader["product_type"] != DBNull.Value && reader["product_type"].ToString() != String.Empty ? reader["product_type"].ToString() : String.Empty;
            }
            return product;
        })).Execute();
    }

    public static I2E1Client updateClientDetails(I2E1Client client)
    {
        return new ShardQueryExecutor<I2E1Client>(new GetSqlCommand((out ResponseType res) =>
        {
            SqlCommand cmd = new SqlCommand(@"
                IF EXISTS (select 1 from t_client where client_id = @clientId or client_name = @clientName)
                BEGIN
                    update t_client set client_name = COALESCE(@clientName,client_name),
                    legal_business_name=COALESCE(@clientLegalBusinessName,legal_business_name),
                    gst_number=COALESCE(@clientGSTNumber,gst_number),
                    contract_signed=COALESCE(@clientContractSigned,contract_signed),
                    billing_address=COALESCE(@clientBillingAddress,billing_address)
                    where client_id = @clientId
                    select client_id,client_name,legal_business_name,gst_number,contract_signed,billing_address from t_client where client_id = @clientId
                END
                ELSE
                BEGIN
                    insert into t_client(client_name,legal_business_name,gst_number,contract_signed,billing_address)
                    values(@clientName, @clientLegalBusinessName, @clientGSTNumber, @clientContractSigned, @clientBillingAddress)
                    select client_id,client_name,legal_business_name,gst_number,contract_signed,billing_address from t_client where client_id = (SELECT MAX(client_id) FROM t_client)
                END");

            cmd.Parameters.Add(new SqlParameter("@clientId", client.clientId.local_value));
            cmd.Parameters.Add(new SqlParameter("@clientName", client.clientName));
            cmd.Parameters.Add(new SqlParameter("@clientLegalBusinessName", client.clientLegalBusinessName == "" || client.clientLegalBusinessName == null ? "" : client.clientLegalBusinessName));
            cmd.Parameters.Add(new SqlParameter("@clientGSTNumber", client.clientGSTNumber == "" || client.clientGSTNumber == null ? "" : client.clientGSTNumber));
            cmd.Parameters.Add(new SqlParameter("@clientContractSigned", client.clientContractSigned));
            cmd.Parameters.Add(new SqlParameter("@clientBillingAddress", client.clientBillingAddress == "" || client.clientBillingAddress == null ? "" : client.clientBillingAddress));
            res = ResponseType.READER;
            return cmd;
        }),client.clientId.shard_id,
        new ResponseHandler<I2E1Client>((reader) =>
        {
            var clients_list = new I2E1Client();

            while (reader.Read())
            {
                
                clients_list.clientId = new LongIdInfo(client.clientId.shard_id, DBObjectType.USER_TYPE, Convert.ToInt64(reader["client_id"]));
                clients_list.clientName = reader["client_name"].ToString();
                clients_list.clientLegalBusinessName = reader["legal_business_name"].ToString();
                clients_list.clientGSTNumber = reader["gst_number"].ToString();
                clients_list.clientContractSigned = (reader["contract_signed"] != DBNull.Value ? Convert.ToInt32(reader["contract_signed"]) : 0);
                clients_list.clientBillingAddress = reader["billing_address"].ToString();
            }
            return clients_list;
        })).Execute();
    }

    public static JsonResponse getClientPartners(I2E1Client client)
    {
        return new ShardQueryExecutor<JsonResponse>(new GetSqlCommand((out ResponseType res) =>
        {
            SqlCommand cmd = new SqlCommand(@"select partner_id,client_id,partner_name,partner_cd,
                                                    partner_logo,partner_image,category,sub_category,micro_category,partner_type,
                                                    account_type,product_type,subscription_type,subscription_amt,subscription_start_date,
                                                    subscription_renewal_date,subscription_status,discount,zoho_account_id,added_time 
                                                    from t_partner where client_id=@clientId order by partner_name");

            cmd.Parameters.Add(new SqlParameter("@clientId", client.clientId.ToSafeDbObject()));
            res = ResponseType.READER;
            return cmd;
        }), client.clientId.shard_id,
        new ResponseHandler<JsonResponse>((reader) =>
        {
            var partners_list = new List<Hashtable>();

            while (reader.Read())
            {
                Hashtable partner = new Hashtable();
                partner.Add("partnerId", (int)reader["partner_id"]);
                partner.Add("clientId", (int)reader["client_id"]);
                partner.Add("partnerName", reader["partner_name"].ToString());
                partner.Add("partnerCd", reader["partner_cd"].ToString());
                partner.Add("partnerLogo", reader["partner_logo"].ToString());
                partner.Add("partnerImage", reader["partner_image"].ToString());
                partner.Add("category", reader["category"].ToString());
                partner.Add("subCategory", reader["sub_category"].ToString());
                partner.Add("microCategory", reader["micro_category"].ToString());
                partner.Add("accountType", reader["account_type"].ToString());
                partner.Add("productType", reader["product_type"].ToString());
                partner.Add("subscriptionType", reader["subscription_type"].ToString());
                partner.Add("subscriptionAmt", Convert.ToDecimal(reader["subscription_amt"]));
                DateTime? subscriptionStartDate;
                if (reader["subscription_start_date"] == DBNull.Value)
                {
                    subscriptionStartDate = null;
                }
                else
                {
                    subscriptionStartDate = (DateTime)reader["subscription_start_date"];
                }
                partner.Add("subscriptionStartDate", subscriptionStartDate.ToString());
                DateTime? subscriptionRenewalDate;
                if (reader["subscription_renewal_date"] == DBNull.Value)
                {
                    subscriptionRenewalDate = null;
                }
                else
                {
                    subscriptionRenewalDate = (DateTime)reader["subscription_renewal_date"];
                }
                partner.Add("subscriptionRenewalDate", subscriptionRenewalDate.ToString());
                partner.Add("discount", Convert.ToDecimal(reader["discount"]));
                partner.Add("zohoAccountId", reader["zoho_account_id"].ToString());
                partners_list.Add(partner);
            }
            return new JsonResponse(ResponseStatus.SUCCESS, "", partners_list);
        })).Execute();
    }

    public static JsonResponse updateClientsPartnerDetails(I2E1Partner partner)
    {
        return new ShardQueryExecutor<JsonResponse>(new GetSqlCommand((out ResponseType res) =>
        {
            DateTime? subscriptionStartDate = null;
            if (partner.subscriptionStartDate == null || partner.subscriptionStartDate == DateTime.MinValue)
            {
                subscriptionStartDate = null;
            }
            else
            {
                subscriptionStartDate = (DateTime)partner.subscriptionStartDate;
            }
            DateTime? subscriptionRenewalDate = null;
            if (partner.subscriptionRenewalDate == null || partner.subscriptionStartDate == DateTime.MinValue)
            {
                subscriptionRenewalDate = null;
            }
            else
            {
                subscriptionRenewalDate = (DateTime)partner.subscriptionRenewalDate;
            }

            SqlCommand cmd = new SqlCommand(@"
                IF EXISTS (select 1 from t_partner where partner_id=@partnerId and client_id = @clientId)
                BEGIN
                    update t_partner  set 
                    partner_name = COALESCE(@partnerName, partner_name),
                    partner_cd = COALESCE(@partnerCd, partner_cd),
                    partner_logo = COALESCE(@partnerLogo, partner_logo),
                    partner_image = COALESCE(@partnerImage, partner_image),
                    category = COALESCE(@category, category),
                    sub_category = COALESCE(@subCategory, sub_category),
                    micro_category = COALESCE(@microCategory, micro_category),
                    account_type = COALESCE(@accountType, account_type),
                    product_type = COALESCE(@productType, product_type),
                    subscription_type = COALESCE(@subscriptionType, subscription_type),
                    subscription_amt = COALESCE(@subscriptionAmt, subscription_amt),
                    subscription_start_date = COALESCE(@subscriptionStartDate, subscription_start_date),
                    subscription_renewal_date = COALESCE(@subscriptionRenewalDate, subscription_renewal_date),
                    discount = COALESCE(@discount, discount),
                    zoho_account_id = COALESCE(@zohoAccountId,zoho_account_id)
                    where partner_id=@partnerId 
                    and client_id = @clientId
                    select partner_id, client_id,partner_name,partner_cd,partner_logo,partner_image,category,sub_category,micro_category,account_type,product_type,subscription_type,subscription_amt,subscription_start_date,subscription_renewal_date,discount,zoho_account_id from t_partner
                    where partner_id=@partnerId and client_id = @clientId
                END
                ELSE
                BEGIN
                    insert into t_partner(client_id,partner_name,partner_cd,partner_logo,partner_image,category,sub_category,micro_category,account_type,product_type,subscription_type,subscription_amt,subscription_start_date,subscription_renewal_date,discount,zoho_account_id)
                    values(@clientId, @partnerName, @partnerCd,@partnerLogo,@partnerImage,@category,@subCategory,@microCategory,@accountType,@productType,@subscriptionType,@subscriptionAmt,@subscriptionStartDate,@subscriptionRenewalDate, @discount, @zohoAccountId)
                    update t_partner set partner_cd= (SELECT MAX(partner_id) FROM t_partner)
                    where partner_id=(SELECT MAX(partner_id) FROM t_partner)
                    select partner_id, client_id,partner_name,partner_cd,partner_logo,partner_image,category,sub_category,micro_category,account_type,product_type,subscription_type,subscription_amt,subscription_start_date,subscription_renewal_date,discount,zoho_account_id
                    from t_partner where partner_id = (SELECT MAX(partner_id) FROM t_partner)
                END");

            cmd.Parameters.Add(new SqlParameter("@partnerId", partner.partnerId.ToSafeDbObject()));
            cmd.Parameters.Add(new SqlParameter("@clientId", partner.clientId.ToSafeDbObject()));
            cmd.Parameters.Add(new SqlParameter("@partnerName", partner.partnerName));
            cmd.Parameters.Add(new SqlParameter("@partnerCd", partner.partnerCd == null ? (object)DBNull.Value : partner.partnerCd));
            cmd.Parameters.Add(new SqlParameter("@partnerLogo", partner.partnerLogo == null ? (object)DBNull.Value : partner.partnerLogo));
            cmd.Parameters.Add(new SqlParameter("@partnerImage", partner.partnerImage == null ? (object)DBNull.Value : partner.partnerImage));
            cmd.Parameters.Add(new SqlParameter("@category", partner.category == null ? (object)DBNull.Value : partner.category));
            cmd.Parameters.Add(new SqlParameter("@subCategory", partner.subCategory == null ? (object)DBNull.Value : partner.subCategory));
            cmd.Parameters.Add(new SqlParameter("@microCategory", partner.microCategory == null ? (object)DBNull.Value : partner.microCategory));
            cmd.Parameters.Add(new SqlParameter("@accountType", partner.accountType == null ? (object)DBNull.Value : partner.accountType));
            cmd.Parameters.Add(new SqlParameter("@productType", partner.productType == null ? (object)DBNull.Value : partner.productType));
            cmd.Parameters.Add(new SqlParameter("@subscriptionType", partner.subscriptionType == null ? (object)DBNull.Value : partner.subscriptionType));
            cmd.Parameters.Add(new SqlParameter("@subscriptionAmt", Convert.ToDecimal(partner.subscriptionAmt)));

            cmd.Parameters.Add(new SqlParameter("@subscriptionStartDate", subscriptionStartDate == null ? (object)DBNull.Value : subscriptionStartDate));
            cmd.Parameters.Add(new SqlParameter("@subscriptionRenewalDate", subscriptionRenewalDate == null ? (object)DBNull.Value : subscriptionRenewalDate));
            cmd.Parameters.Add(new SqlParameter("@discount", Convert.ToDecimal(partner.discount)));
            cmd.Parameters.Add(new SqlParameter("@zohoAccountId", partner.zohoAccountId == "" || partner.zohoAccountId == null ? "" : partner.zohoAccountId));
            res = ResponseType.READER;
            return cmd;
        }), partner.partnerId.shard_id,
        new ResponseHandler<JsonResponse>((reader) =>
        {
            var partner_list = new Hashtable();

            while (reader.Read())
            {
                partner_list.Add("partnerId", (int)reader["partner_id"]);
                partner_list.Add("clientId", (int)reader["client_id"]);
                partner_list.Add("partnerName", reader["partner_name"].ToString());
                partner_list.Add("partnerCd", reader["partner_cd"].ToString());
                partner_list.Add("partnerLogo", reader["partner_logo"].ToString());
                partner_list.Add("partnerImage", reader["partner_image"].ToString());
                partner_list.Add("category", reader["category"].ToString());
                partner_list.Add("subCategory", reader["sub_category"].ToString());
                partner_list.Add("microCategory", reader["micro_category"].ToString());
                partner_list.Add("accountType", reader["account_type"].ToString());
                partner_list.Add("productType", reader["product_type"].ToString());
                partner_list.Add("subscriptionType", reader["subscription_type"].ToString());
                partner_list.Add("subscriptionAmt", Convert.ToDecimal(partner.subscriptionAmt));


                DateTime? subscriptionStartDate;
                if (reader["subscription_start_date"] == DBNull.Value)
                {
                    subscriptionStartDate = null;
                }
                else
                {
                    subscriptionStartDate = (DateTime)reader["subscription_start_date"];
                }
                partner_list.Add("subscriptionStartDate", subscriptionStartDate.ToString());
                DateTime? subscriptionRenewalDate;
                if (reader["subscription_start_date"] == DBNull.Value)
                {
                    subscriptionRenewalDate = null;
                }
                else
                {
                    subscriptionRenewalDate = (DateTime)reader["subscription_renewal_date"];
                }

                partner_list.Add("subscriptionRenewalDate", subscriptionRenewalDate.ToString());
                partner_list.Add("discount", Convert.ToDecimal(reader["discount"]));
                partner_list.Add("zohoAccountId", reader["zoho_account_id"].ToString());
            }
            return new JsonResponse(ResponseStatus.SUCCESS, "", partner_list);
        })).Execute();
    }

    public static JsonResponse getPartnerContactPersons(LongIdInfo partnerId)
    {
        return new ShardQueryExecutor<JsonResponse>(new GetSqlCommand((out ResponseType res) =>
        {
            SqlCommand cmd = new SqlCommand(@"select * from t_admin_mapping tam
                                                left join t_admin ta on ta.user_id=tam.admin_id
                                                where tam.mapping_type = 'partner' and tam.mapped_id=@partnerId
                                                and ta.user_type in (100,101,0)");

            cmd.Parameters.Add(new SqlParameter("@partnerId", partnerId.ToSafeDbObject()));
            res = ResponseType.READER;
            return cmd;
        }), partnerId.shard_id,
        new ResponseHandler<JsonResponse>((reader) =>
        {
            var contacts_list = new List<Hashtable>();

            while (reader.Read())
            {
                Hashtable contact = new Hashtable();
                contact.Add("userId", (int)reader["admin_id"]);
                contact.Add("username", reader["username"].ToString());
                contact.Add("name", reader["name"].ToString());
                contact.Add("userType", reader["user_type"] != null ? Convert.ToInt32(reader["user_type"]) : 0);
                contact.Add("mobile", reader["contact_no"].ToString());
                contact.Add("active", reader["active"] != DBNull.Value ? Convert.ToInt32(reader["active"]) : 0);
                contact.Add("isGreeted", reader["is_greeted"] != DBNull.Value ? Convert.ToInt32(reader["is_greeted"]) : 0);
                contacts_list.Add(contact);
            }
            return new JsonResponse(ResponseStatus.SUCCESS, "", contacts_list);
        })).Execute();
    }

    public static List<Hashtable> getPartnersAllLevelContacs(LongIdInfo longPartnerId)
    {
        return new ShardQueryExecutor<List<Hashtable>>(new GetSqlCommand((out ResponseType res) =>
        {
            SqlCommand cmd = new SqlCommand(@"select user_id,user_type from t_admin
                                                inner join (
                                                select partner_id, admin_id from
                                                (select router_nas_id, a.partner_id, client_id from t_store a left join t_partner b
                                                on a.partner_id =b.partner_id) a left join t_admin_mapping b

                                                on b.mapped_id = case when b.mapping_type = 'location' then a.router_nas_id
                                                when b.mapping_type = 'partner' then a.partner_id
                                                when b.mapping_type = 'client' then a.client_id else null end
                                                where partner_id=@partnerId
                                                group by partner_id, admin_id
                                                ) b on b.admin_id = t_admin.user_id
                                                where user_type in (100,101,0)");

            cmd.Parameters.Add(new SqlParameter("@partnerId", longPartnerId.local_value));
            res = ResponseType.READER;
            return cmd;
        }), longPartnerId.shard_id,
        new ResponseHandler<List<Hashtable>>((reader) =>
        {
            var contacts_list = new List<Hashtable>();

            while (reader.Read())
            {
                Hashtable contact = new Hashtable();
                contact.Add("userId", (int)reader["user_id"]);
                contact.Add("userType", reader["user_type"] != null ? Convert.ToInt32(reader["user_type"]) : 0);
                contacts_list.Add(contact);
            }
            return contacts_list;
        })).Execute();
    }

    public static JsonResponse getStoreContactPersons(LongIdInfo nasid, string classification)
    {
        string query = @"select * from t_admin_mapping tam
                                                left join t_admin ta on ta.user_id=tam.admin_id
                                                where tam.mapping_type = 'location' and tam.mapped_id=@nasId
                                                and ta.user_type in (0,1,100,101)";
        if (!string.IsNullOrEmpty(classification))
            query = @"select top 1 * from t_admin_mapping tam
                            left join t_admin ta on ta.user_id=tam.admin_id
                            where tam.mapping_type = 'location' and tam.mapped_id=@nasId
                            and ta.user_type in (100,101,0) and ta.classification = @classification order by ta.added_time desc";
        return new ShardQueryExecutor<JsonResponse>(new GetSqlCommand((out ResponseType res) =>
        {
            SqlCommand cmd = new SqlCommand(query);

            cmd.Parameters.Add(new SqlParameter("@nasId", nasid.ToSafeDbObject()));
            if (!string.IsNullOrEmpty(classification))
                cmd.Parameters.Add(new SqlParameter("@classification", classification));
            res = ResponseType.READER;
            return cmd;
        }), nasid.shard_id,
        new ResponseHandler<JsonResponse>((reader) =>
        {
            var contacts_list = new List<Hashtable>();

            while (reader.Read())
            {
                Hashtable contact = new Hashtable();
                contact.Add("classification", reader["classification"] == DBNull.Value ? "" : reader["classification"].ToString());
                contact.Add("userId", (int)reader["admin_id"]);
                contact.Add("username", reader["username"].ToString());
                contact.Add("email", reader["email"] == DBNull.Value ? "" : reader["email"].ToString());
                contact.Add("name", reader["name"].ToString());
                contact.Add("userType", reader["user_type"] != null ? Convert.ToInt32(reader["user_type"]) : 0);
                contact.Add("mobile", reader["contact_no"].ToString());
                contact.Add("active", reader["active"] != DBNull.Value ? Convert.ToInt32(reader["active"]) : 0);
                contact.Add("isGreeted", reader["is_greeted"] != DBNull.Value ? Convert.ToInt32(reader["is_greeted"]) : 0);
                contacts_list.Add(contact);
            }
            return new JsonResponse(ResponseStatus.SUCCESS, "", contacts_list);
        })).Execute();
    }

    public static JsonResponse removeContact(StoreUser user, int mappingId, string type)
    {
        return new ShardQueryExecutor<JsonResponse>(new GetSqlCommand((out ResponseType res) =>
        {
            SqlCommand cmd = new SqlCommand(@"
                    Delete from t_admin_mapping where admin_id = @userId and mapped_id = @mappingId and mapping_type=@type
                ");

            cmd.Parameters.Add(new SqlParameter("@userId", user.userId.ToSafeDbObject()));
            cmd.Parameters.Add(new SqlParameter("@mappingId", mappingId));
            cmd.Parameters.Add(new SqlParameter("@type", type));
            res = ResponseType.NONQUERY;
            return cmd;
        }), user.userId.shard_id,
        new ResponseHandler<JsonResponse>((reader) =>
        {
            return new JsonResponse(ResponseStatus.SUCCESS, "", "removed");
        })).Execute();
    }

    public static List<ManagementUser> GetAllUsers()
    {
        return new ShardQueryExecutor<List<ManagementUser>>(new GetSqlCommand((out ResponseType res) =>
        {
            SqlCommand cmd = new SqlCommand("Select user_id, username from t_admin where username is not null");
            res = ResponseType.READER;
            return cmd;
        }),0,
       new ResponseHandler<List<ManagementUser>>((reader) =>
       {
           List<ManagementUser> users = new List<ManagementUser>();
           while (reader.Read())
           {
               ManagementUser user = new ManagementUser();
               user.userid = new LongIdInfo(0, DBObjectType.USER_TYPE, Convert.ToInt64((int)reader["user_id"]));
               user.name = reader["username"].ToString();
               users.Add(user);
           }
           return users;
       })).Execute();
    }

    public static List<ManagementUser> GetMyUsers(LongIdInfo longAdminId, AdminUserType adminUserType)
    {
        return new ShardQueryExecutor<List<ManagementUser>>(new GetSqlCommand((out ResponseType res) =>
        {
            SqlCommand cmd = null;
            if (adminUserType == AdminUserType.ADMIN || adminUserType == AdminUserType.SUPER_ADMIN)
            {
                cmd = new SqlCommand(@"Select a.user_id, a.username, a.auth_type, a.user_type, a.parent_admin, a.product,a.active, b.username as parent_username  
                                         from t_admin a left join t_admin b on a.parent_admin = b.user_id
                                         where a.username is not null");
            }
            else
            {
                cmd = new SqlCommand(@"Select a.user_id, a.username, a.auth_type, a.user_type, a.parent_admin, a.product,a.active, b.username as parent_username
                                                    from t_admin a left join t_admin b on a.parent_admin = b.user_id
                                                    where (a.parent_admin = @userId or a.parent_admin is null)
                                                    and a.user_type = 0 and a.user_id != @userId");
            }
            cmd.Parameters.Add(new SqlParameter("@userId", longAdminId.ToSafeDbObject()));
            res = ResponseType.READER;
            return cmd;
        }), longAdminId.shard_id,
       new ResponseHandler<List<ManagementUser>>((reader) =>
       {
           List<ManagementUser> users = new List<ManagementUser>(2000);
           while (reader.Read())
           {
               ManagementUser user = new ManagementUser();
               user.userid = new LongIdInfo(longAdminId.shard_id, DBObjectType.USER_TYPE, Convert.ToInt64((int)reader["user_id"]));
               user.name = reader["username"].ToString();
               user.product = reader["product"].ToString();
               try
               {
                   user.parentAdminUserId = (reader["parent_admin"] == DBNull.Value ? null: new LongIdInfo(longAdminId.shard_id, DBObjectType.USER_TYPE, Convert.ToInt64((int)reader["parent_admin"])));
                   if (user.parentAdminUserId != null)
                   {
                       user.parent = reader["parent_username"].ToString();
                   }
               }
               catch { }
               user.userType = (AdminUserType)(byte)reader["user_type"];
               user.active = reader["active"] != DBNull.Value ? Convert.ToInt32(reader["active"]) : 0;
               users.Add(user);
           }

           return users;
       })).Execute();
    }

    public static bool SaveReportSubscription(string triggerPrefix, int triggerId, DateTime startTime, int cycle,
        string name, string codePath, string sendEmailTo, string[] additionalParameters)
    {
        return new ShardQueryExecutor<bool>(new GetSqlCommand((out ResponseType res) =>
        {
            SqlCommand cmd = new SqlCommand(@"
                IF EXISTS (select 1 from t_trigger_info where trigger_id = @trigger_id)
                BEGIN
                    update t_trigger_info set trigger_name = @trigger_name, schedule_start_time = @schedule_start_time, code_path=@code_path,
                    repeat_interval_in_min = @repeat_interval_in_min, send_email_to = @send_email_to, parameters = @parameters
                    where trigger_id = @trigger_id
                END
                ELSE
                BEGIN
                insert into t_trigger_info(trigger_name, schedule_start_time, repeat_interval_in_min, code_path, send_email_to, parameters)
                values(@trigger_name, @schedule_start_time, @repeat_interval_in_min, @code_path, @send_email_to, @parameters)
                END");

            cmd.Parameters.Add(new SqlParameter("@trigger_id", triggerId));
            cmd.Parameters.Add(new SqlParameter("@trigger_name", triggerPrefix + name));
            cmd.Parameters.Add(new SqlParameter("@schedule_start_time", startTime));
            cmd.Parameters.Add(new SqlParameter("@repeat_interval_in_min", cycle));
            cmd.Parameters.Add(new SqlParameter("@code_path", codePath));
            cmd.Parameters.Add(new SqlParameter("@send_email_to", sendEmailTo));
            cmd.Parameters.Add(new SqlParameter("@parameters", JsonConvert.SerializeObject(additionalParameters)));
            res = ResponseType.NONQUERY;
            return cmd;
        }), ShardHelper.SHARD0,
       new ResponseHandler<bool>((reader) =>
       {
           return true;
       })).Execute();
    }

    public static bool addMappingForUser(string username, LongIdInfo longMappedId, string type)
    {
        WebUtils.LogInfoToCosmos("Adding " + type + " mapping for mappedId", longMappedId.local_value);
        return new ShardQueryExecutor<bool>(new GetSqlCommand((out ResponseType res) =>
        {
            SqlCommand cmd = new SqlCommand("add_router_user_new");
            cmd.CommandType = CommandType.StoredProcedure;
            res = ResponseType.NONQUERY;
            cmd.Parameters.Add(new SqlParameter("@mappedId", longMappedId.local_value));
            cmd.Parameters.Add(new SqlParameter("@username", username));
            cmd.Parameters.Add(new SqlParameter("@password", string.Empty));
            cmd.Parameters.Add(new SqlParameter("@type", type));
            return cmd;
        }), longMappedId.shard_id,
       new ResponseHandler<bool>((reader) =>
       {
           return true;
       })).Execute();
    }

    public static bool removeMappingForUser(string username, int mappedId, string type)
    {
        var longId = ShardHelper.getLongUserIdFromMobile(username);
        WebUtils.LogInfoToCosmos("Removing " + type + " mapping from a user", mappedId);
        return new ShardQueryExecutor<bool>(new GetSqlCommand((out ResponseType res) =>
        {
            SqlCommand cmd = new SqlCommand("delete from t_admin_mapping where admin_id = (select user_id from t_admin where username = @username) AND mapped_id = @mappedId AND mapping_type = @type ");
            res = ResponseType.NONQUERY;
            cmd.Parameters.Add(new SqlParameter("@mappedId", mappedId));
            cmd.Parameters.Add(new SqlParameter("@type", type));
            cmd.Parameters.Add(new SqlParameter("@username", username));
            return cmd;
        }), longId.shard_id,
       new ResponseHandler<bool>((reader) =>
       {
           return true;
       })).Execute();
    }

    public static bool manageLeads(LongIdInfo longParentId, LongIdInfo longUserId, int operation)
    {
        return new ShardQueryExecutor<bool>(new GetSqlCommand((out ResponseType res) =>
        {
            var query = "@";
            if (operation == 1)
            {
                WebUtils.LogInfoToCosmos("Setting parent to a user", new { user = ManagementUserImpl.GetAdminUser(longUserId), parent = ManagementUserImpl.GetAdminUser(longParentId) });
                query = "update t_admin set parent_admin=@parentId where user_id = @userId";
            }
            else
            {
                WebUtils.LogInfoToCosmos("Removing parent from a user", new { user = ManagementUserImpl.GetAdminUser(longUserId) });
                query = "update t_admin set parent_admin=null where user_id = @userId";
            }

            SqlCommand cmd = new SqlCommand(query);
            cmd.Parameters.Add(new SqlParameter("@parentId", longParentId.local_value));
            cmd.Parameters.Add(new SqlParameter("@userId", longUserId.local_value));
            res = ResponseType.NONQUERY;
            return cmd;
        }), longParentId.shard_id,
        new ResponseHandler<bool>((reader) =>
        {
            return true;
        })).Execute();
    }

    public static bool changeUserType(LongIdInfo longUserId, int userType)
    {
        if (userType == 1)
            return false;
        WebUtils.LogInfoToCosmos("Setting userType for a user", new { user = ManagementUserImpl.GetAdminUser(longUserId), userType = (AdminUserType)userType });
        return new ShardQueryExecutor<bool>(new GetSqlCommand((out ResponseType res) =>
        {
            var query = "update t_admin set user_type=@userType where user_id = @userId";
            SqlCommand cmd = new SqlCommand(query);
            cmd.Parameters.Add(new SqlParameter("@userType", userType));
            cmd.Parameters.Add(new SqlParameter("@userId", longUserId.local_value));
            res = ResponseType.NONQUERY;
            return cmd;
        }), longUserId.shard_id,
        new ResponseHandler<bool>((reader) =>
        {
            return true;
        })).Execute();
    }

    public static bool UpdateTAdmin(LongIdInfo longUserId, string product, int userType)
    {
        WebUtils.LogInfoToCosmos("Setting product for a user", new { user = ManagementUserImpl.GetAdminUser(longUserId), product = product });
        return new ShardQueryExecutor<bool>(new GetSqlCommand((out ResponseType res) =>
        {
            var query = @"update t_admin set 
                                product=@product
                            where user_id = @userId";
            SqlCommand cmd = new SqlCommand(query);
            cmd.Parameters.Add(new SqlParameter("@product", product));
            cmd.Parameters.Add(new SqlParameter("@userId", longUserId.ToSafeDbObject()));
            res = ResponseType.NONQUERY;
            return cmd;
        }), longUserId.shard_id,
        new ResponseHandler<bool>((reader) =>
        {
            changeUserType(longUserId, userType);
            return true;
        })).Execute();
    }


    public static bool updatePortalAcess(LongIdInfo longUserId, int active,HttpContext httpContext)
    {
        WebUtils.LogInfoToCosmos("Setting portal access for a user", new { user = ManagementUserImpl.GetAdminUser(longUserId), active = active });
        return new ShardQueryExecutor<bool>(new GetSqlCommand((out ResponseType res) =>
        {
            var query = @"update t_admin set 
                                active=@active
                            where user_id = @userId";
            SqlCommand cmd = new SqlCommand(query);
            cmd.Parameters.Add(new SqlParameter("@active", active));
            cmd.Parameters.Add(new SqlParameter("@userId", longUserId.local_value));
            res = ResponseType.NONQUERY;
            return cmd;
        }), longUserId.shard_id,
        new ResponseHandler<bool>((reader) =>
        {
            return true;
        })).Execute();
    }

    public static Dictionary<string, string> AddSenderId(string senderId, LongIdInfo partnerId)
    {
        return new ShardQueryExecutor<Dictionary<string, string>>(new GetSqlCommand((out ResponseType res) =>
        {
            SqlCommand cmd = new SqlCommand(@"IF EXISTS (select 1 from t_partner_parameters where sender_id = @senderId and partner_id = @partnerId)
                                                BEGIN
                                                    select sender_id, partner_id from t_partner_parameters where sender_id = @senderId and partner_id = @partnerId
                                                END
                                                ELSE
                                                BEGIN
                                                    insert into t_partner_parameters(sender_id, partner_id) values(@senderId, @partnerId)
                                                    select sender_id, partner_id from t_partner_parameters where sender_id = @senderId and partner_id = @partnerId
                                                END");

            cmd.Parameters.Add(new SqlParameter("@senderId", senderId));
            cmd.Parameters.Add(new SqlParameter("@partnerId", partnerId.ToSafeDbObject()));

            res = ResponseType.READER;
            return cmd;
        }), partnerId.shard_id,
        new ResponseHandler<Dictionary<string, string>>((reader) =>
        {
            Dictionary<string, string> result = new Dictionary<string, string>();
            if (reader.Read())
            {
                result.Add("senderId", reader["sender_id"].ToString());
                result.Add("partnerId", reader["partner_id"].ToString());
            }

            return result;
        })).Execute();
    }
    // check once
    public static int createNewSetting(ManagementUser user, CombinedSetting newSetting)
    {
        return new ShardQueryExecutor<int>(new GetSqlCommand((out ResponseType res) =>
        {
            SqlCommand cmd = new SqlCommand(@"insert into t_combined_settings(admin_user_id,name) values(@admin_id, @setting_name);
                                                    select scope_identity() as combined_setting_id;");
            cmd.Parameters.Add(new SqlParameter("@admin_id", user.userid.local_value));
            cmd.Parameters.Add(new SqlParameter("@setting_name", newSetting.name));
            res = ResponseType.READER;
            return cmd;
        }), user.userid.shard_id,
        new ResponseHandler<int>((reader) =>
        {
            if (reader.Read())
            {
                return int.Parse(reader["combined_setting_id"].ToString());
            }
            return 0;
        })).Execute();
    }

    public static bool DeleteSettings(int settingId)
    {
        new ShardQueryExecutor<bool>(new GetSqlCommand((out ResponseType res) =>
        {
            StringBuilder query = new StringBuilder();
            query.Append("delete from t_combined_settings where combined_setting_id = @combined_setting_id;");
            query.Append("delete from t_combined_setting_nas_mapping where combined_setting_id = @combined_setting_id;");
            query.Append("delete from t_user_basic_configuration where combined_setting_id = @combined_setting_id;");
            query.Append("delete from t_advance_configuration where combined_setting_id = @combined_setting_id;");
            query.Append("delete from t_health_alert where combined_setting_id = @combined_setting_id;");
            SqlCommand cmd = new SqlCommand(query.ToString());
            cmd.Parameters.Add(new SqlParameter("@combined_setting_id", settingId));
            res = ResponseType.READER;
            return cmd;
        }),
        (reader, shardId) =>
        {
        }).ExecuteAll();
        return true;
    }
    
    public static Dictionary<long, int> GetUsersInXDays(List<LongIdInfo> nasids, int days)
    {
        var dict = new Dictionary<long, int>();

        foreach (var nas in nasids)
        {
            dict.Add(nas.GetLongId(), 0);
        }
        new MySQLQueryExecutor<Dictionary<string, int>>(new GetMySqlCommand((out ResponseType res) =>
        {
            DateTime start = DateTime.UtcNow;
            start = start.AddDays(-days);
            MySqlCommand cmd = new MySqlCommand("SELECT routernasid, COUNT(DISTINCT username) AS count FROM p_radacct WHERE (AcctStartTime >= @start OR AcctStopTime > @start) AND routernasid IN (@nasList) GROUP BY routernasid");
            cmd.Parameters.Add(new MySqlParameter("@start", start));
            res = ResponseType.READER;
            return cmd;
        }),
       (reader, shardId) =>
       {
           while (reader.Read())
           {
               var nas = new LongIdInfo(shardId, DBObjectType.ACTIVE_NAS, Convert.ToInt64(reader["routernasid"]));
               dict[nas.GetLongId()] = Convert.ToInt32(reader["count"]);
           }
       }).ExecuteAll(nasids, "@nasList");

        return dict;
    }

    public static Dictionary<long, Dictionary<string, double>> GetConnectionStatusForNases(List<LongIdInfo> nasids)
    {
        Dictionary<long, Dictionary<string, double>> resultData = new Dictionary<long, Dictionary<string, double>>();
        foreach (var nas in nasids)
        {
            resultData.Add(nas.GetLongId(), null);
        }
        new ShardQueryExecutor<Dictionary<string, Dictionary<string, double>>>(new GetSqlCommand((out ResponseType res) =>
        {
            SqlCommand cmd = new SqlCommand("select last_ping_time, router_nas_id from t_controller where router_nas_id in (select value from @nasList)");
            res = ResponseType.READER;
            return cmd;
        }),
       (reader, shardId) =>
       {
           while (reader.Read())
           {
               var dict = new Dictionary<string, double>();
               var nas = new LongIdInfo(shardId, DBObjectType.ACTIVE_NAS, Convert.ToInt64(reader["router_nas_id"]));
               double lastPingDelay = (DateTime.UtcNow - (DateTime)reader["last_ping_time"]).TotalMinutes;
               dict["lastPingDelay"] = lastPingDelay;
               dict["status"] = lastPingDelay > Constants.MAX_WARNING_TOLERANCE ? 0 : 1;
               resultData[nas.GetLongId()] =dict;
           }
       }).ExecuteAll(nasids, "@nasList");
        return resultData;
    }

    // check once
    /*public static List<StoreInfo> GetNasesWithDetailsForSetting(int settingId)
    {
        return new AdminQueryExecutor<List<StoreInfo>>(new GetSqlCommand((out ResponseType res) =>
        {
            SqlCommand cmd = new SqlCommand(@"select t2.* from t_combined_setting_nas_mapping t1 
            left join
            (
                SELECT

                    a.router_nas_id, a.shop_name, a.brand_name, a.sub_locality, a.locality, a.pincode,
                    a.shop_address, a.shop_city, a.shop_state, a.install_state, a.category, a.mode,
                    isnull(a.mm_nas_id, 0) as mm_nas_id, a.partner_cd, a.partner_id, d.client_id,
                    a.market_place_id, a.store_tags, b.market_place_name, a.loc_sub_cat, a.micro_category
                FROM t_store a
                INNER JOIN t_controller c ON a.router_nas_id = c.router_nas_id
                LEFT JOIN t_market_place b ON b.market_place_id = a.market_place_id
                LEFT JOIN t_partner d on d.partner_cd = a.partner_cd or d.partner_id = a.partner_id
            ) t2 on t2.router_nas_id = t1.nas_id
            where router_nas_id is not null and t1.combined_setting_id = @combined_setting_id");
            cmd.Parameters.Add(new SqlParameter("@combined_setting_id", settingId));
            res = ResponseType.READER;
            return cmd;
        }),
        new ResponseHandler<List<StoreInfo>>((reader) =>
        {
            List<StoreInfo> storeDetails = new List<StoreInfo>(10000);
            DateTime currentTime = DateTime.UtcNow;
            while (reader.Read())
            {
                StoreInfo details = new StoreInfo();
                details.nasid = (int)reader["router_nas_id"];
                details.storeName = reader["shop_name"].ToString();
                details.brandName = reader["brand_name"].ToString();
                details.partner = reader["partner_cd"].ToString();
                int n;
                details.partnerId = (int.TryParse(reader["partner_id"].ToString(), out n) ? (int)reader["partner_id"] : 0);

                details.clientId = (int.TryParse(reader["client_id"].ToString(), out n) ? (int)reader["client_id"] : 0);

                details.marketPlaceId = (String.IsNullOrEmpty(reader["market_place_id"].ToString()) ? 0 : (int)reader["market_place_id"]);
                details.marketPlaceName = reader["market_place_name"].ToString();
                details.subLocality = reader["sub_locality"].ToString();
                details.locality = reader["locality"].ToString();
                details.address = reader["shop_address"].ToString();
                details.city = reader["shop_city"].ToString();
                details.state = reader["shop_state"].ToString();
                details.pinCode = reader["pincode"].ToString();
                details.category = reader["category"].ToString();
                details.subCategory = reader["loc_sub_cat"].ToString();
                details.microCategory = reader["micro_category"].ToString();
                details.routerState = (RouterState)(byte)reader["install_state"];
                details.mmNasId = (Convert.IsDBNull(reader["mm_nas_id"])) ? (int)reader["router_nas_id"] : (int)reader["mm_nas_id"];
                string tagStr = (String.IsNullOrEmpty(reader["store_tags"].ToString()) ? "0,0," : reader["store_tags"].ToString());
                var substrings = tagStr.Split(',');
                List<string> tagsArr = new List<string>();
                var result = Enum.GetValues(typeof(StoreTags)).Cast<StoreTags>().Where((x, idx) => idx < substrings.Length && substrings[idx] == "1");
                details.storeTags = result.Select(x => x.ToString()).ToList();
                details.mode = reader["mode"].ToString();
                storeDetails.Add(details);
            }

            return storeDetails;
        })).Execute();
    }*/


    public static List<WebUserGroupNew> GetUserGroups(int settingId)
    {
        return new ShardQueryExecutor<List<WebUserGroupNew>>(new GetSqlCommand((out ResponseType res) =>
        {
            SqlCommand cmd = new SqlCommand("select * from t_user_group_new where user_group_id in (select distinct(user_group_id) from t_user_basic_configuration where combined_setting_id = @combined_setting_id)");
            cmd.Parameters.Add(new SqlParameter("@combined_setting_id", settingId));
            res = ResponseType.READER;
            return cmd;
        }), ShardHelper.SHARD0,
        new ResponseHandler<List<WebUserGroupNew>>((reader) =>
        {
            List<WebUserGroupNew> userGroupList = new List<WebUserGroupNew>();
            userGroupList.Add(new WebUserGroupNew()
            {
                groupId = 0,
                groupName = "Global"
            });

            userGroupList.Add(new WebUserGroupNew()
            {
                groupId = -1,
                groupName = "Access Code"
            });

            userGroupList.Add(new WebUserGroupNew()
            {
                groupId = -2,
                groupName = "Linq Promo"
            });
            while (reader.Read())
            {
                WebUserGroupNew userGroup = new WebUserGroupNew();
                userGroup.groupId = (int)reader["user_group_id"];
                userGroup.groupName = reader["user_group_name"].ToString();
                userGroup.values = reader["parameters"].ToString();
                userGroupList.Add(userGroup);
            }

            return userGroupList;
        })).Execute();
    }

    public static bool UpdateLocations(int settingId, List<LongIdInfo> longNases, bool force)
    {
        new ShardQueryExecutor<bool>(new GetSqlCommand((out ResponseType res) =>
        {
            SqlCommand cmd = new SqlCommand("update_combined_setting_locations_v1");
            cmd.CommandType = CommandType.StoredProcedure;
            cmd.Parameters.Add(new SqlParameter("@combined_setting_id", settingId));
            cmd.Parameters.Add(new SqlParameter("@force", force ? 1 : 0));
            res = ResponseType.READER;
            return cmd;
        }),
        (reader, shardId) =>
        {
        }).ExecuteAll(longNases, "@nases");

        foreach (LongIdInfo nas in longNases)
        {
            CoreCacheHelper.GetInstance().Reset(CoreCacheHelper.ROUTER_BASIC, nas);
        }

        List<LongIdInfo> oldNases = CoreDbCalls.GetInstance().GetNassesForSetting(settingId);
        foreach (LongIdInfo nas in oldNases)
        {
            CoreCacheHelper.GetInstance().Reset(CoreCacheHelper.ROUTER_BASIC, nas);
        }

        return true;
    }


    public static WebUserGroupNew GetUserGroupBasicConfiguration(int combinedSettingId, int userGroupId)
    {
        return new ShardQueryExecutor<WebUserGroupNew>(new GetSqlCommand((out ResponseType res) =>
        {
            SqlCommand cmd = new SqlCommand(@"SELECT config_id, parameters from t_user_basic_configuration WHERE combined_setting_id = @combined_setting_id and user_group_id = @groupid");
            cmd.Parameters.Add(new SqlParameter("@combined_setting_id", combinedSettingId));
            cmd.Parameters.Add(new SqlParameter("@groupid", userGroupId));
            res = ResponseType.READER;
            return cmd;
        }), ShardHelper.SHARD0,
        new ResponseHandler<WebUserGroupNew>((reader) =>
        {
            WebUserGroupNew userGroup = new WebUserGroupNew();

            userGroup.groupId = userGroupId;
            userGroup.basicConfigs = new Dictionary<String, BasicConfig>();

            foreach (BasicConfigType basicConfigType in Enum.GetValues(typeof(BasicConfigType)))
            {
                var b = new BasicConfig(basicConfigType);
                userGroup.basicConfigs.Add(basicConfigType.ToString(), b);
            }

            while (reader.Read())
            {
                string value = reader["parameters"].ToString();
                if (!string.IsNullOrEmpty(value) && value != "0")
                {
                    BasicConfigType config = (BasicConfigType)reader["config_id"];
                    BasicConfig basicConfig = new BasicConfig()
                    {
                        configType = config
                    };
                    basicConfig.value = value;
                    userGroup.basicConfigs[config.ToString()] = basicConfig;
                }
            }
            return userGroup;
        })).Execute();
    }

    public static bool UpdateUserGroupBasicConfig(int combinedSettingId, int userGroupId, Dictionary<String, BasicConfig> basicConfigs, LongIdInfo longAdminId)
    {
        WebUtils.LogInfoToCosmos("updating basic configuration in setting", new { settingId = combinedSettingId, groupId = userGroupId, basicConfigs });
        return new ShardQueryExecutor<bool>(new GetSqlCommand((out ResponseType res) =>
        {
            SqlCommand cmd = new SqlCommand(@"
                    DECLARE @old_value VARCHAR(MAX)
                    DECLARE @new_value VARCHAR(MAX)

                    SET @old_value = (SELECT id, config_id, parameters
                    FROM t_user_basic_configuration WHERE combined_setting_id = @setting_id 
					and user_group_id = @user_group_id FOR JSON AUTO)
					SET @old_value = CONCAT('user_group_id: ', @user_group_id, ', settings: ', @old_value)

                    DELETE FROM t_user_basic_configuration WHERE combined_setting_id = @setting_id and user_group_id = @user_group_id

                    INSERT INTO t_user_basic_configuration(combined_setting_id, config_id, user_group_id, parameters)
                    (SELECT @setting_id, int_value, @user_group_id, varchar_value FROM @configs)

                    SET @new_value = (SELECT id, config_id, parameters
                    FROM t_user_basic_configuration WHERE combined_setting_id = @setting_id 
					and user_group_id = @user_group_id FOR JSON AUTO)
					SET @new_value = CONCAT('user_group_id: ', @user_group_id, ', settings: ', @new_value)

					INSERT INTO t_setting_audit(table_name, combined_setting_id, old_value, new_value, modified_time, updated_by)
                    VALUES('t_user_basic_configuration', @setting_id, @old_value, @new_value, GETUTCDATE(), @admin_user_id)
                        
                    SELECT 0 AS status");

            DataTable basicConfigTable = new DataTable();
            basicConfigTable.Columns.Add("varchar_value", typeof(string));
            basicConfigTable.Columns.Add("int_value", typeof(int));

            foreach (KeyValuePair<String, BasicConfig> pair in basicConfigs)
            {
                BasicConfig config = pair.Value;
                if (config.value != null && config.value != "0")
                {
                    basicConfigTable.Rows.Add(config.value, (int)config.configType);
                }
                else
                {
                    basicConfigTable.Rows.Add(String.Empty, (int)config.configType);
                }
            }
            var p = new SqlParameter("@configs", basicConfigTable);
            p.TypeName = "dbo.type_varchar_int";
            cmd.Parameters.Add(p);
            cmd.Parameters.Add(new SqlParameter("@setting_id", combinedSettingId));
            cmd.Parameters.Add(new SqlParameter("@user_group_id", userGroupId));
            cmd.Parameters.Add(new SqlParameter("@admin_user_id", longAdminId.local_value));
            res = ResponseType.READER;
            return cmd;
        }), ShardHelper.SHARD0,
        new ResponseHandler<bool>((reader) =>
        {
            if (reader.Read())
            {
                return (0 == (int)reader["status"]);
            }
            return false;
        })).Execute();
    }

    public static Dictionary<string, AdvanceConfig> GetAdvanceConfiguration(int combinedSettingId)
    {
        return new ShardQueryExecutor<Dictionary<string, AdvanceConfig>>(new GetSqlCommand((out ResponseType res) =>
        {
            SqlCommand cmd = new SqlCommand(@"select * from t_advance_configuration where combined_setting_id = @combined_setting_id");
            cmd.Parameters.Add(new SqlParameter("@combined_setting_id", combinedSettingId));
            res = ResponseType.READER;
            return cmd;
        }), ShardHelper.SHARD0,
        new ResponseHandler<Dictionary<string, AdvanceConfig>>((reader) =>
        {
            Dictionary<string, AdvanceConfig> advanceConfigs = new Dictionary<string, AdvanceConfig>();

            foreach (AdvanceConfigType configType in Enum.GetValues(typeof(AdvanceConfigType)))
            {
                var b = new AdvanceConfig(configType);
                advanceConfigs.Add(configType.ToString(), b);
            }

            while (reader.Read())
            {
                AdvanceConfigType configType = (AdvanceConfigType)reader["config_id"];
                advanceConfigs[configType.ToString()] = new AdvanceConfig(configType, reader["parameters"].ToString());
            }

            return advanceConfigs;
        })).Execute();
    }

    public static int SaveTemplate(ManagementUser user, Template template)
    {
        return new ShardQueryExecutor<int>(new GetSqlCommand((out ResponseType res) =>
        {
            SqlCommand cmd = new SqlCommand("save_new_template");
            cmd.CommandType = CommandType.StoredProcedure;
            cmd.Parameters.Add(new SqlParameter("@template_id", template.id));
            cmd.Parameters.Add(new SqlParameter("@template_name", template.templateName));
            cmd.Parameters.Add(new SqlParameter("@userid", user.userid.ToSafeDbObject()));
            cmd.Parameters.Add(new SqlParameter("@disable_chat", template.disableChat));
            cmd.Parameters.Add(new SqlParameter("@template_path", template.templatePath == null ? string.Empty : template.templatePath));
            res = ResponseType.READER;
            return cmd;
        }), ShardHelper.SHARD0,
        new ResponseHandler<int>((reader) =>
        {
            int id = 0;
            if (reader.Read())
            {
                id = (int)(reader["id"]);

                var file = AppDomain.CurrentDomain.BaseDirectory + "CustomTemplates/" + id + ".html";
                if (File.Exists(file))
                    File.Delete(file);

                CoreCacheHelper.GetInstance().Reset(CacheHelper.TEMPLATE_CONTENT, id);
            }
            return id;
        })).Execute();
    }

    private static void PrepareQuestionsTable(DataTable questionTable, DataTable optionsTable, List<Question> questions)
    {
        int id = -100;
        int qId = 1, optionId = 1;

        questions.ForEach(m =>
        {
            if (!m.hidden || m.id != 0)
            {
                if (m.id == 0)
                    m.id = id--;

                questionTable.Rows.Add(qId++, m.id, m.quesText, m.quesType, m.answerType, -1000, m.hidden);
                if (m.options != null)
                    m.options.ForEach(n =>
                    {
                        optionsTable.Rows.Add(optionId++, n.id, m.id, n.text, n.image);
                    });
            }
        });
    }

    private static bool UpdateQuestionSequenceing(int templateId, List<Question> questions, int firstQuestionId, int lastQuestionId)
    {
        WebUtils.LogInfoToCosmos("UpdateQuestionSequenceing", "going to update sequence for template id: " + templateId);
        Dictionary<string, QuestionSequence> questionSequence = new Dictionary<string, QuestionSequence>();
        foreach (Question que in questions)
        {
            string key = que.templateId + "-" + que.id;
            QuestionSequence sequence = que.CreateSequence(firstQuestionId, lastQuestionId);
            questionSequence.Add(key, sequence);
        }

        return new ShardQueryExecutor<bool>(new GetSqlCommand((out ResponseType res) =>
        {
            SqlCommand cmd = new SqlCommand(@"update t_template set question_sequence = @sequence where template_id = @template_id");
            cmd.Parameters.Add(new SqlParameter("@template_id", templateId));
            cmd.Parameters.Add(new SqlParameter("@sequence", JsonConvert.SerializeObject(questionSequence)));
            res = ResponseType.NONQUERY;
            WebUtils.LogInfoToCosmos("UpdateQuestionSequenceing", "updating sequence for template id: " + templateId + " : new sequence : " + JsonConvert.SerializeObject(questionSequence));
            return cmd;
        }), ShardHelper.SHARD0,
        new ResponseHandler<bool>((reader) =>
        {
            CoreCacheHelper.GetInstance().Reset(CacheHelper.TEMPLATE_CONTENT, templateId);
            WebUtils.LogInfoToCosmos("UpdateQuestionSequenceing", "sequence updated for templateId: " + templateId + " : new sequence : " + JsonConvert.SerializeObject(questionSequence));
            return true;
        })).Execute();
    }

    private static DataTable GetOptionDataTable()
    {
        DataTable optionTable = new DataTable();
        optionTable.Columns.Add("table_id", typeof(int));
        optionTable.Columns.Add("id", typeof(int));
        optionTable.Columns.Add("ques_id", typeof(int));
        optionTable.Columns.Add("text", typeof(string));
        optionTable.Columns.Add("option_img", typeof(string));

        return optionTable;
    }

    public static bool SaveTemplateQuestions(int templateId, List<Question> questions, int firstQuestionId, int lastQuestionId)
    {
        return new ShardQueryExecutor<bool>(new GetSqlCommand((out ResponseType res) =>
        {
            SqlCommand cmd = new SqlCommand("save_template_questions_Remove_from_Remove");
            cmd.CommandType = System.Data.CommandType.StoredProcedure;


            DataTable questionTable = new DataTable();
            questionTable.Columns.Add("table_id", typeof(int));
            questionTable.Columns.Add("id", typeof(int));
            questionTable.Columns.Add("text", typeof(string));
            questionTable.Columns.Add("ques_type", typeof(int));
            questionTable.Columns.Add("answer_type", typeof(int));
            questionTable.Columns.Add("ques_state", typeof(int));
            questionTable.Columns.Add("hidden", typeof(bool));

            DataTable optionTable = GetOptionDataTable();

            PrepareQuestionsTable(questionTable, optionTable, questions);

            cmd.Parameters.Add(new SqlParameter("@template_id", templateId));
            cmd.Parameters.Add(new SqlParameter("@questions", questionTable));
            cmd.Parameters.Add(new SqlParameter("@options", optionTable));

            res = ResponseType.READER;

            WebUtils.LogInfoToCosmos("SaveTemplateQuestions", "saving questions : " + JsonConvert.SerializeObject(questions) + " for template id: " + templateId);
            return cmd;
        }), ShardHelper.SHARD0,
        new ResponseHandler<bool>((reader) =>
        {
            if (reader.Read() && (int)reader["status"] == 0)
            {
                if (templateId == 0 || questions.Find(m => m.templateId != 0) != null)
                {
                    UpdateQuestionSequenceing(templateId, questions, firstQuestionId, lastQuestionId);
                }
                return true;
            }
            return false;
        })).Execute();
    }

    public static int UpdateQuestion(int templateId, Question question)
    {
        return new ShardQueryExecutor<int>(new GetSqlCommand((out ResponseType res) =>
        {
            SqlCommand cmd = new SqlCommand(@"update t_question set template_id = @templateId, hidden = @hidden, 
                                                ques_type = @quesType, answer_type = @answerType
                                                where question_id = @question_id");

            cmd.Parameters.Add(new SqlParameter("@templateId", question.templateId));
            cmd.Parameters.Add(new SqlParameter("@hidden", question.hidden ? 1 : 0));
            cmd.Parameters.Add(new SqlParameter("@quesType", question.quesType));
            cmd.Parameters.Add(new SqlParameter("@answerType", question.answerType));
            cmd.Parameters.Add(new SqlParameter("@question_id", question.id));
            res = ResponseType.NONQUERY;
            return cmd;
        }), ShardHelper.SHARD0,
        new ResponseHandler<int>((reader) =>
        {
            return 0;
        })).Execute();
    }

    public static int AddTemplateQuestion(int templateId, Question question)
    {
        return new ShardQueryExecutor<int>(new GetSqlCommand((out ResponseType res) =>
        {
            SqlCommand cmd = new SqlCommand(@"insert into t_question(template_id, ques_type, ques_text, answer_type, ques_state, hidden) 
                                                values(@template_id, 0, @question_text, 0, -1000, 0); select scope_identity() as question_id;");


            cmd.Parameters.Add(new SqlParameter("@template_id", templateId));
            cmd.Parameters.Add(new SqlParameter("@question_text", question.quesText));
            res = ResponseType.READER;
            return cmd;
        }), ShardHelper.SHARD0,
        new ResponseHandler<int>((reader) =>
        {
            if (reader.Read())
            {
                int questionId = int.Parse(reader["question_id"].ToString());
                if (templateId == 0)
                {
                    question.id = questionId;
                }

                return questionId;
            }
            return 0;
        })).Execute();
    }

    public static List<Option> UpdateQuestionOptions(Question question, int templateId)
    {
        return new ShardQueryExecutor<List<Option>>(new GetSqlCommand((out ResponseType res) =>
        {
            SqlCommand cmd = new SqlCommand("update_question_options");
            cmd.CommandType = System.Data.CommandType.StoredProcedure;

            DataTable optionTable = GetOptionDataTable();

            if (question.options != null)
            {
                int optionId = 1;
                question.options.ForEach(n =>
                {
                    optionTable.Rows.Add(optionId++, n.id, question.id, n.text, n.image);
                });
            }

            cmd.Parameters.Add(new SqlParameter("@question_id", question.id));
            cmd.Parameters.Add(new SqlParameter("@options", optionTable));
            res = ResponseType.READER;
            return cmd;
        }), ShardHelper.SHARD0,
        new ResponseHandler<List<Option>>((reader) =>
        {
            if (reader.Read())
            {
                if (0 == (int)reader["status"])
                {
                    question.options = GetOptions(question.id);
                    return question.options;
                }
            }
            return null;
        })).Execute();
    }

    public static List<Template> GetAllTemplates()
    {
        return new ShardQueryExecutor<List<Template>>(new GetSqlCommand((out ResponseType res) =>
        {
            SqlCommand cmd = new SqlCommand("select * from t_template order by template_name");
            res = ResponseType.READER;
            return cmd;
        }), ShardHelper.SHARD0,
        new ResponseHandler<List<Template>>((reader) =>
        {
            var list = new List<Template>();
            while (reader.Read())
            {
                Template template = new Template()
                {
                    id = (int)reader["template_id"],
                    templateName = reader["template_name"].ToString(),
                    templatePath = reader["template_path"].ToString(),
                    disableChat = (bool)reader["disable_chat"]
                };

                list.Add(template);
            }
            return list;
        })).Execute();
    }

    public static List<Question> GetQuestions(int templateId)
    {
        return new ShardQueryExecutor<List<Question>>(new GetSqlCommand((out ResponseType res) =>
        {
            SqlCommand cmd = new SqlCommand(@"select a.question_id, a.template_id, a.ques_type, a.ques_text, a.answer_type, a.hidden, a.randomized,
                                                  b.option_id, b.option_text, b.option_img
                                                  from t_question a left join t_option b on a.question_id = b.question_id 
                                                  where a.template_id in (0, @template_id) and a.hidden = 0 and ques_type in (0, 5)");
            cmd.Parameters.Add(new SqlParameter("@template_id", templateId));
            res = ResponseType.READER;
            return cmd;
        }), ShardHelper.SHARD0,
        new ResponseHandler<List<Question>>((reader) =>
        {
            List<Question> list = new List<Question>(100);
            while (reader.Read())
            {
                int questionId = (int)reader["question_id"];
                var ques = list.Find(m => m.id == questionId);

                if (ques == null)
                {
                    ques = new Question();
                    ques.id = (int)reader["question_id"];
                    ques.templateId = (int)reader["template_id"];
                    ques.quesType = (QuestionType)reader["ques_type"];
                    ques.quesText = reader["ques_text"].ToString();
                    ques.answerType = (AnswerType)reader["answer_type"];
                    ques.hidden = (bool)reader["hidden"];

                    list.Add(ques);
                }

                if (ques.answerType == AnswerType.RADIO || ques.answerType == AnswerType.CHECKBOX)
                {
                    var option = new Option();
                    option.id = (int)reader["option_id"];
                    option.text = reader["option_text"].ToString();
                    try
                    {
                        option.image = reader["option_img"].ToString();
                    }
                    catch { }

                    if (ques.options == null)
                    {
                        ques.options = new List<Option>();
                    }
                    ques.options.Add(option);
                }
            }
            return list;
        })).Execute();
    }

    public static List<Option> GetOptions(int questionId)
    {
        return new ShardQueryExecutor<List<Option>>(new GetSqlCommand((out ResponseType res) =>
        {
            SqlCommand cmd = new SqlCommand("select * from t_option where question_id = @question_id");
            cmd.Parameters.Add(new SqlParameter("@question_id", questionId));
            res = ResponseType.READER;
            return cmd;
        }), ShardHelper.SHARD0,
        new ResponseHandler<List<Option>>((reader) =>
        {
            List<Option> list = new List<Option>();
            while (reader.Read())
            {
                var option = new Option();
                option.id = (int)reader["option_id"];
                option.text = reader["option_text"].ToString();
                list.Add(option);
            }
            return list;
        })).Execute();
    }

    public static string[] GetAuthenticationModeHelper(LongIdInfo nasid)
    {
        return new ShardQueryExecutor<string[]>(new GetSqlCommand((out ResponseType res) =>
        {
            SqlCommand cmd = new SqlCommand("select parameters from t_single_nas_operations where single_operation_id=16 and nas_id=@nasid");
            cmd.Parameters.Add(new SqlParameter("@nasid", nasid.ToSafeDbObject()));
            res = ResponseType.READER;
            return cmd;
        }), nasid.shard_id,
        new ResponseHandler<string[]>((reader) =>
        {
            string[] resultArr = null;
            if (reader.Read())
            {
                string result = reader["parameters"].ToString();
                if (!String.IsNullOrEmpty(result))
                    resultArr = result.Split(new string[] { "," }, StringSplitOptions.None);
            }
            else
            {
                resultArr = new String[1] { "0" };
            }
            return resultArr;
        })).Execute();
    }

    public static bool SaveAuthenticationModeHelper(LongIdInfo longNasId, string[] parameters,HttpContext httpContext)
    {
        WebUtils.LogInfoToCosmos("Saving authentication mode helper", new { nasId = longNasId.local_value, parameters = parameters });
        return new ShardQueryExecutor<bool>(new GetSqlCommand((out ResponseType res) =>
        {
            SqlCommand cmd = new SqlCommand(@"
                IF EXISTS(select * from t_single_nas_operations where single_operation_id = 16 and nas_id = @nas)
                   begin update t_single_nas_operations set parameters=@data where single_operation_id = 16 and nas_id = @nas end
                ELSE
                    begin insert into t_single_nas_operations(nas_id, single_operation_id, parameters) values(@nas, 16, @data) end");

            cmd.Parameters.Add(new SqlParameter("@nas", longNasId.local_value));
            string configParams = string.Join(",", parameters);
            cmd.Parameters.Add(new SqlParameter("@data", configParams));
            res = ResponseType.NONQUERY;
            return cmd;
        }), longNasId.shard_id,
        new ResponseHandler<bool>((reader) =>
        {
            WebUtils.LogInfoToCosmos("authentication mode helper saved succesfully", new { nasId = longNasId.local_value });
            CoreCacheHelper.GetInstance().ResetAll(longNasId);
            return true;
        })).Execute();
    }

    public static List<MarketPlace> GetMarketPlaceFromDB(string marketplace)
    {

        return new ShardQueryExecutor<List<MarketPlace>>(new GetSqlCommand((out ResponseType res) =>
        {
            SqlCommand cmd = new SqlCommand(@"select * from (
                                                select *,ROW_NUMBER ( )   
                                                    OVER ( PARTITION BY market_place_name,city  order by market_place_name ) as row_count
                                                from t_market_place where market_place_name like @marketPlaceName 
                                                ) a where row_count = 1");
            cmd.Parameters.Add(new SqlParameter("@marketPlaceName", '%' + marketplace + '%'));
            res = ResponseType.READER;
            return cmd;
        }), ShardHelper.SHARD0,
        new ResponseHandler<List<MarketPlace>>((reader) =>
        {

            List<MarketPlace> resultList = new List<MarketPlace>();

            while (reader.Read())
            {
                var rowResultDict = new MarketPlace()
                {
                    marketPlaceId = (int)reader["market_place_id"],
                    marketPlaceName = reader["market_place_name"].ToString(),
                    googlePlaceId = reader["google_place_id"].ToString(),
                    vicinity = reader["vicinity"].ToString(),
                    subLocality = reader["sub_locality"].ToString(),
                    locality = reader["locality"].ToString(),
                    city = reader["city"].ToString(),
                    state = reader["state"].ToString(),
                    stateCode = reader["state_code"].ToString(),
                    country = reader["country"].ToString(),
                    countryCode = reader["country_code"].ToString(),
                    pinCode = reader["pin_code"].ToString(),
                    latitude = reader["latitude"].ToString(),
                    longitude = reader["longitude"].ToString()
                };

                resultList.Add(rowResultDict);
            }
            return resultList;

        })).Execute();
    }

    public static int updateMarketPlace(MarketPlace marketplace)
     {
        WebUtils.LogInfoToCosmos("Updating market place", new { marketplace = marketplace });
        return new ShardQueryExecutor<int>(new GetSqlCommand((out ResponseType res) =>
        {
            SqlCommand cmd = new SqlCommand("save_market_place");
            cmd.CommandType = System.Data.CommandType.StoredProcedure;
            cmd.Parameters.Add(new SqlParameter("@marketPlaceName", marketplace.marketPlaceName));
            cmd.Parameters.Add(new SqlParameter("@marketPlaceId", marketplace.marketPlaceId));
            cmd.Parameters.Add(new SqlParameter("@googlePlaceId", marketplace.googlePlaceId));
            cmd.Parameters.Add(new SqlParameter("@googleCategories", marketplace.googleCategories));
            cmd.Parameters.Add(new SqlParameter("@vicinity", marketplace.vicinity));
            cmd.Parameters.Add(new SqlParameter("@subLocality", marketplace.subLocality));
            cmd.Parameters.Add(new SqlParameter("@locality", marketplace.locality));
            cmd.Parameters.Add(new SqlParameter("@city", marketplace.city));
            cmd.Parameters.Add(new SqlParameter("@state", marketplace.state));
            cmd.Parameters.Add(new SqlParameter("@stateCode", marketplace.stateCode));
            cmd.Parameters.Add(new SqlParameter("@country", marketplace.country));
            cmd.Parameters.Add(new SqlParameter("@countryCode", marketplace.countryCode));
            cmd.Parameters.Add(new SqlParameter("@pinCode", marketplace.pinCode));
            cmd.Parameters.Add(new SqlParameter("@latitude", marketplace.latitude));
            cmd.Parameters.Add(new SqlParameter("@longitude", marketplace.longitude));
            res = ResponseType.READER;
            return cmd;
        }), ShardHelper.SHARD0,
        new ResponseHandler<int>((reader) =>
        {
            if (reader.Read())
            {
                int marketPlaceId = int.Parse(reader["market_place_id"].ToString());
                WebUtils.LogInfoToCosmos("market place successfully updated", new { marketPlaceId = marketPlaceId, marketplace = marketplace });
                return marketPlaceId;
            }
            return 0;
        })).Execute();
    }

    /****************** Call to get the details of a store by nas id ************************/
    public static StoreInfo GetStoreDetails(LongIdInfo longNasId)
    {
        return new ShardQueryExecutor<StoreInfo>(new GetSqlCommand((out ResponseType res) =>
       {
           SqlCommand cmd = new SqlCommand(@"select ts.*,tm.market_place_name, tm.google_place_id as market_place_google_id, 
                                                tm.city as market_place_city, tc.controller_id, tc.last_ping_time, tc.location, tp.client_id
                                                from t_store ts
                                                LEFT JOIN t_market_place tm on tm.market_place_id = ts.market_place_id
                                                INNER JOIN t_controller tc ON ts.router_nas_id = tc.router_nas_id
                                                LEFT JOIN t_partner tp on tp.partner_cd = ts.partner_cd or tp.partner_id = ts.partner_id
                                                where ts.router_nas_id = @nasid");
           cmd.Parameters.Add(new SqlParameter("@nasid", longNasId.local_value));
           res = ResponseType.READER;
           return cmd;
       }), longNasId.shard_id,
        new ResponseHandler<StoreInfo>((reader) =>
        {
            StoreInfo details = new StoreInfo();
            DateTime currentTime = DateTime.UtcNow;
            if (reader.Read())
            {

                details.nasid = new LongIdInfo(longNasId.shard_id, DBObjectType.USER_TYPE, Convert.ToInt64((int)reader["router_nas_id"]));
                details.storeName = reader["shop_name"].ToString();
                details.brandName = reader["brand_name"].ToString();
                details.partner = reader["partner_cd"].ToString();
                details.partnerId = (reader["partner_id"] != DBNull.Value) ? (int)reader["partner_id"] : 0;
                details.clientId = (reader["client_id"] != DBNull.Value) ? (int)reader["client_id"] : 0;
                details.marketPlaceId = (String.IsNullOrEmpty(reader["market_place_id"].ToString()) ? 0 : (int)reader["market_place_id"]);
                details.marketPlaceName = reader["market_place_name"].ToString();
                details.marketPlaceCity = reader["market_place_city"].ToString();
                details.marketPlaceGoogleId = reader["market_place_google_id"].ToString();
                details.subLocality = reader["sub_locality"].ToString();
                details.locality = reader["locality"].ToString();
                details.storeNameAlias = reader["external_store_identifier"].ToString();
                details.address = reader["shop_address"].ToString();
                details.city = reader["shop_city"].ToString();
                details.state = reader["shop_state"].ToString();
                details.pinCode = reader["pincode"].ToString();
                details.latitude = reader["latitude"].ToString();
                details.longitude = reader["longitude"].ToString();
                details.category = reader["category"].ToString();
                details.subCategory = reader["loc_sub_cat"].ToString();
                details.microCategory = reader["micro_category"].ToString();
                details.googleCategories = reader["google_categories"].ToString();
                details.location = reader["position"].ToString();
                details.routerState = (RouterState)(byte)reader["install_state"];
                details.mmNasId = (Convert.IsDBNull(reader["mm_nas_id"])) ? new LongIdInfo(longNasId.shard_id, DBObjectType.USER_TYPE, Convert.ToInt64((int)reader["router_nas_id"])): 
                new LongIdInfo(longNasId.shard_id, DBObjectType.USER_TYPE, Convert.ToInt64(reader["mm_nas_id"]));
                string tagStr = (String.IsNullOrEmpty(reader["store_tags"].ToString()) ? "0,0," : reader["store_tags"].ToString());
                var substrings = tagStr.Split(',');
                int i = 0;
                List<string> tagsArr = new List<string>();
                foreach (StoreTags tag in Enum.GetValues(typeof(StoreTags)))
                {
                    try
                    {
                        if (substrings[i] == "1")
                        {
                            string enumnam = Enum.GetName(typeof(StoreTags), tag).ToString();
                            tagsArr.Add(enumnam);
                        }
                    }
                    catch (Exception)
                    {
                        break;
                    }
                    i = i + 1;
                }

                details.storeTags = tagsArr;

                details.salesId = reader["sales_id"].ToString();
                details.installerId = reader["install_id"].ToString();
                details.mode = reader["mode"].ToString();
                details.locationOpeningTime = reader["loc_open_time"].ToString();
                details.locationClosingTime = reader["loc_close_time"].ToString();
                details.deviceMac = reader["device_mac"].ToString();
                details.deviceType = reader["device_type"].ToString();
                details.deviceVersion = reader["device_version"].ToString();
                details.productName = reader["product_name"].ToString();
                details.salesChannel = reader["sales_channel"].ToString();
                details.salesChannelName = reader["channel_name"].ToString();
                details.zohoContactId = reader["zoho_contact_id"].ToString();
                details.locationStartDate = (Convert.IsDBNull(reader["loc_start_date"]) ? null : (DateTime?)Convert.ToDateTime(reader["loc_start_date"].ToString()));
                details.locationCloseDate = (Convert.IsDBNull(reader["loc_close_date"]) ? null : (DateTime?)Convert.ToDateTime(reader["loc_close_date"].ToString()));
                details.locationRetagDate = (Convert.IsDBNull(reader["retag_date"]) ? null : (DateTime?)Convert.ToDateTime(reader["retag_date"].ToString()));
                details.closeReason = reader["close_reason"].ToString();
                details.mktPlaceAlias = reader["mkt_place_identifier"].ToString();
                details.shopDpName = reader["shop_display_name"].ToString();
                details.lastPingDelay = (currentTime - (DateTime)reader["last_ping_time"]).TotalMinutes;
                details.legalBusinessName = reader["legal_business_name"].ToString();
                details.legalBusinessAddress = reader["legal_business_address"].ToString();
                details.gstNumber = reader["gst_number"].ToString();
            }
            return details;
        })).Execute();
    }

    //public static bool saveBrandStore(StoreDetails brandStore, int nasId)
    public static bool saveBrandStore(StoreInfo brandStore, LongIdInfo longNasId)
    {
        WebUtils.LogInfoToCosmos("Saving brand store for a nas", new { nasId = longNasId.local_value, brandStore = brandStore });
        return new ShardQueryExecutor<bool>(new GetSqlCommand((out ResponseType res) =>
        {
            //SqlCommand cmd = new SqlCommand("merge_store_details");
            SqlCommand cmd = new SqlCommand("merge_store_details_new");
            cmd.CommandType = CommandType.StoredProcedure;
            
            cmd.Parameters.Add(new SqlParameter("@router_nas_id", longNasId.local_value));
            cmd.Parameters.Add(new SqlParameter("@brand_name", brandStore.brandName));
            //if (string.IsNullOrEmpty(brandStore.marketPlaceName))
            //    cmd.Parameters.Add(new SqlParameter("@market_place_name", ""));
            //else
            //    cmd.Parameters.Add(new SqlParameter("@market_place_name", brandStore.marketPlaceName));
            cmd.Parameters.Add(new SqlParameter("@market_place_id", brandStore.marketPlaceId));
            if (string.IsNullOrEmpty(brandStore.marketPlaceName))
                cmd.Parameters.Add(new SqlParameter("@shop_name", brandStore.brandName));
            else
                cmd.Parameters.Add(new SqlParameter("@shop_name", brandStore.brandName + ", " + brandStore.marketPlaceName));
            cmd.Parameters.Add(new SqlParameter("@google_place_id", brandStore.googlePlaceId));
            cmd.Parameters.Add(new SqlParameter("@shop_address", brandStore.address));
            if (string.IsNullOrEmpty(brandStore.subLocality))
                cmd.Parameters.Add(new SqlParameter("@sub_locality", ""));
            else
                cmd.Parameters.Add(new SqlParameter("@sub_locality", brandStore.subLocality));

            if (string.IsNullOrEmpty(brandStore.locality))
                cmd.Parameters.Add(new SqlParameter("@locality", ""));
            else
                cmd.Parameters.Add(new SqlParameter("@locality", brandStore.locality));

            cmd.Parameters.Add(new SqlParameter("@shop_city", brandStore.city));
            cmd.Parameters.Add(new SqlParameter("@shop_state", brandStore.state));

            if (string.IsNullOrEmpty(brandStore.pinCode))
                cmd.Parameters.Add(new SqlParameter("@pincode", ""));
            else
                cmd.Parameters.Add(new SqlParameter("@pincode", brandStore.pinCode));

            cmd.Parameters.Add(new SqlParameter("@latitude", brandStore.latitude));
            cmd.Parameters.Add(new SqlParameter("@longitude", brandStore.longitude));
            cmd.Parameters.Add(new SqlParameter("@category", brandStore.category));
            cmd.Parameters.Add(new SqlParameter("@loc_sub_cat", brandStore.subCategory));
            cmd.Parameters.Add(new SqlParameter("@micro_category", brandStore.microCategory));
            cmd.Parameters.Add(new SqlParameter("@google_categories", brandStore.googleCategories));
            //cmd.Parameters.Add(new SqlParameter("@contact_person", string.IsNullOrEmpty(brandStore.contactPerson) ? string.Empty : brandStore.contactPerson));
            //cmd.Parameters.Add(new SqlParameter("@contact_number", string.IsNullOrEmpty(brandStore.contactNumber) ? string.Empty : brandStore.contactNumber));
            //cmd.Parameters.Add(new SqlParameter("@email", string.IsNullOrEmpty(brandStore.emailId) ? string.Empty : brandStore.emailId));
            cmd.Parameters.Add(new SqlParameter("@install_state", brandStore.routerState));
            cmd.Parameters.Add(new SqlParameter("@location", brandStore.location));
            cmd.Parameters.Add(new SqlParameter("@controllerid", brandStore.controllerid));
            cmd.Parameters.Add(new SqlParameter("@mm_nas_id", brandStore.mmNasId));
            cmd.Parameters.Add(new SqlParameter("@device_mac", string.IsNullOrEmpty(brandStore.deviceMac) ? string.Empty : brandStore.deviceMac));
            cmd.Parameters.Add(new SqlParameter("@loc_start_date", brandStore.locationStartDate == null ? DBNull.Value : (object)brandStore.locationStartDate.Value));
            cmd.Parameters.Add(new SqlParameter("@loc_close_date", brandStore.locationCloseDate == null ? DBNull.Value : (object)brandStore.locationCloseDate.Value));
            cmd.Parameters.Add(new SqlParameter("@retag_date", brandStore.locationRetagDate == null ? DBNull.Value : (object)brandStore.locationRetagDate.Value));
            cmd.Parameters.Add(new SqlParameter("@close_reason", string.IsNullOrEmpty(brandStore.closeReason) ? string.Empty : brandStore.closeReason));
            cmd.Parameters.Add(new SqlParameter("@sales_id", string.IsNullOrEmpty(brandStore.salesId) ? string.Empty : brandStore.salesId));
            cmd.Parameters.Add(new SqlParameter("@install_id", brandStore.installerId));
            cmd.Parameters.Add(new SqlParameter("@partner_cd", string.IsNullOrEmpty(brandStore.partner) ? string.Empty : brandStore.partner));
            cmd.Parameters.Add(new SqlParameter("@partner_id", brandStore.partnerId != 0 ? (int)brandStore.partnerId : 0));
            cmd.Parameters.Add(new SqlParameter("@mode", string.IsNullOrEmpty(brandStore.mode) ? string.Empty : brandStore.mode));
            cmd.Parameters.Add(new SqlParameter("@loc_open_time", string.IsNullOrEmpty(brandStore.locationOpeningTime) ? string.Empty : brandStore.locationOpeningTime));
            cmd.Parameters.Add(new SqlParameter("@loc_close_time", string.IsNullOrEmpty(brandStore.locationClosingTime) ? string.Empty : brandStore.locationClosingTime));
            cmd.Parameters.Add(new SqlParameter("@device_type", string.IsNullOrEmpty(brandStore.deviceType) ? string.Empty : brandStore.deviceType));
            cmd.Parameters.Add(new SqlParameter("@device_version", string.IsNullOrEmpty(brandStore.deviceVersion) ? string.Empty : brandStore.deviceVersion));
            cmd.Parameters.Add(new SqlParameter("@zoho_contact_id", string.IsNullOrEmpty(brandStore.zohoContactId) ? string.Empty : brandStore.zohoContactId));
            cmd.Parameters.Add(new SqlParameter("@mktPlaceAlias", string.IsNullOrEmpty(brandStore.mktPlaceAlias) ? string.Empty : brandStore.mktPlaceAlias));
            cmd.Parameters.Add(new SqlParameter("@shopDpName", string.IsNullOrEmpty(brandStore.shopDpName) ? string.Empty : brandStore.shopDpName));
            cmd.Parameters.Add(new SqlParameter("@sales_channel", string.IsNullOrEmpty(brandStore.salesChannel) ? string.Empty : brandStore.salesChannel));
            cmd.Parameters.Add(new SqlParameter("@channel_name", string.IsNullOrEmpty(brandStore.salesChannelName) ? string.Empty : brandStore.salesChannelName));
            cmd.Parameters.Add(new SqlParameter("@legal_business_name", string.IsNullOrEmpty(brandStore.legalBusinessName) ? string.Empty : brandStore.legalBusinessName));
            cmd.Parameters.Add(new SqlParameter("@legal_business_address", string.IsNullOrEmpty(brandStore.legalBusinessAddress) ? string.Empty : brandStore.legalBusinessAddress));
            cmd.Parameters.Add(new SqlParameter("@gst_number", string.IsNullOrEmpty(brandStore.gstNumber) ? string.Empty : brandStore.gstNumber));
            string stortagsstr = "";
            if (brandStore.storeTags == null || brandStore.storeTags.Count == 0)
            {

                foreach (StoreTags tag in Enum.GetValues(typeof(StoreTags)))
                {
                    stortagsstr = stortagsstr + "0,";
                }
            }
            else
            {
                foreach (StoreTags tag in Enum.GetValues(typeof(StoreTags)))
                {
                    var enumname = Enum.GetName(typeof(StoreTags), tag).ToString();
                    if (brandStore.storeTags.Contains(enumname))
                    {
                        stortagsstr = stortagsstr + "1,";
                    }
                    else
                    {
                        stortagsstr = stortagsstr + "0,";
                    }
                }
            }
            cmd.Parameters.Add(new SqlParameter("@store_tags", stortagsstr));
            res = ResponseType.READER;
            return cmd;
        }), longNasId.shard_id,
        new ResponseHandler<bool>((reader) =>
        {
            if (reader.Read())
            {
                int status = (int)reader["status"];
                WebUtils.LogInfoToCosmos("brand store saved successfully", new { nasId = longNasId.local_value });
                CoreCacheHelper.GetInstance().Reset(CacheHelper.ROUTER_TAG_DETAILS, longNasId);
                return true;
            }
            return false;
        })).Execute();
    }

    public static bool UpdateSetting(ManagementUser user, CombinedSetting setting)
    {
        return new ShardQueryExecutor<bool>(new GetSqlCommand((out ResponseType res) =>
        {
            SqlCommand cmd = new SqlCommand("update t_combined_settings set admin_user_id = @admin_id where combined_setting_id = @combined_setting_id");
            cmd.Parameters.Add(new SqlParameter("@admin_id", user.userid.ToSafeDbObject()));
            cmd.Parameters.Add(new SqlParameter("@combined_setting_id", setting.settingId));
            res = ResponseType.NONQUERY;
            return cmd;
        }), ShardHelper.SHARD0,
        new ResponseHandler<bool>((reader) =>
        {
            return true;
        })).Execute();
    }

    public static List<String> GetFilterOptions(string filter,HttpContext httpContext)
    {
        return new ShardQueryExecutor<List<String>>(new GetSqlCommand((out ResponseType res) =>
        {
            string sql = "";
            JwtObject jwtObject = JwtObject.GetJWTObject(httpContext);
            if (filter == "shop_city" && jwtObject.userType != AdminUserType.ADMIN)
            {
                //sql = "select distinct(" + filter + ") from t_store left join t_admin_nas_mapping on t_admin_nas_mapping.router_nas_id = t_store.router_nas_id where t_admin_nas_mapping.user_id= " + SessionUtils.AdminInView.userid + " and t_store.install_state=1";
                string condition = "";
                if (jwtObject.partnerId > 0)
                {
                    condition = " and partner_id = " + jwtObject.partnerId;
                }

                sql = @"SELECT DISTINCT( " + filter + @" ) 
                            FROM   (
                                    SELECT * 
                                    FROM   t_store 
                                    WHERE  partner_id IN (SELECT t_partner.partner_id AS partner_id 
                                                            FROM   t_admin_mapping 
						                                    INNER JOIN t_partner on t_partner.client_id = t_admin_mapping.mapped_id
                                                            WHERE  admin_id = " + jwtObject.adminId + @"
                                                                    AND mapping_type = 'client') " + condition + @"
                                    UNION 
                                    SELECT * 
                                    FROM   t_store 
                                    WHERE  partner_id IN (SELECT mapped_id AS partner_id 
                                                          FROM   t_admin_mapping 
                                                          WHERE  admin_id = " + jwtObject.adminId + @" 
                                                                 AND mapping_type = 'partner') " + condition + @" 
                                    UNION 
                                    SELECT * 
                                    FROM   t_store 
                                    WHERE  router_nas_id IN (SELECT mapped_id AS router_nas_id 
                                                             FROM   t_admin_mapping 
                                                             WHERE  admin_id = " + jwtObject.adminId + @" 
                                                                    AND mapping_type = 'location') " + condition + ") a ";
            }
            else
            {
                sql = "select distinct(" + filter + ") from t_store where install_state=1 order by " + filter;
            }
            SqlCommand cmd = new SqlCommand(sql);
            res = ResponseType.READER;
            return cmd;
        }), ShardHelper.SHARD0,
        new ResponseHandler<List<String>>((reader) =>
        {
            List<String> list = new List<String>();
            while (reader.Read())
            {
                var str = reader[filter].ToString();
                if (!string.IsNullOrEmpty(str))
                {
                    list.Add(str);
                }

            }
            return list;
        })).Execute();
    }

    public static List<Hashtable> GetInternalAccounts()
    {
        return new ShardQueryExecutor<List<Hashtable>>(new GetSqlCommand((out ResponseType res) =>
        {
            string sql = "select user_id,username,name,user_type from t_admin where user_type in (1,3,4,6) and name is not null";

            SqlCommand cmd = new SqlCommand(sql);
            res = ResponseType.READER;
            return cmd;
        }), ShardHelper.SHARD0,
        new ResponseHandler<List<Hashtable>>((reader) =>
        {
            List<Hashtable> accountList = new List<Hashtable>();
            while (reader.Read())
            {
                Hashtable account = new Hashtable();
                account.Add("name", reader["name"].ToString());
                account.Add("userName", reader["username"].ToString());
                account.Add("userType", Convert.ToInt32(reader["user_type"]));
                account.Add("userId", (int)reader["user_id"]);
                accountList.Add(account);
            }
            return accountList;
        })).Execute();
    }

    public static int createPackages(SellablePackage packages, ManagementUser adminInPower)
    {
        return new ShardQueryExecutor<int>(new GetSqlCommand((out ResponseType res) =>
        {

            SqlCommand cmd = new SqlCommand("save_new_packages");
            cmd.CommandType = CommandType.StoredProcedure;
            cmd.Parameters.Add(new SqlParameter("@packageId", 0));
            cmd.Parameters.Add(new SqlParameter("@packageName", packages.packageName));
            string features = "";
            foreach (Feature f in packages.features)
            {
                if (features == "")
                    features += Convert.ToInt32(f).ToString();
                else
                    features += ',' + Convert.ToInt32(f).ToString();
            }

            cmd.Parameters.Add(new SqlParameter("@features", features));
            if (packages.packageJsonData == null)
            {
                packages.packageJsonData = new PackageJson();
            }
            cmd.Parameters.Add(new SqlParameter("@packageJsonData", JsonConvert.SerializeObject(packages.packageJsonData)));
            cmd.Parameters.Add(new SqlParameter("@userId", adminInPower.userid.ToSafeDbObject()));
            cmd.Parameters.Add(new SqlParameter("@price", packages.price));
            cmd.Parameters.Add(new SqlParameter("@packageValidity", packages.packageValidity));
            res = ResponseType.READER;
            return cmd;
        }), ShardHelper.SHARD0,
        new ResponseHandler<int>((reader) =>
        {
            if (reader.Read())
            {
                return int.Parse(reader["package_id"].ToString());
            }
            return 0;
        })).Execute();
    }

    public static List<SellablePackage> getPackages()
    {
        return new ShardQueryExecutor<List<SellablePackage>>(new GetSqlCommand((out ResponseType res) =>
        {
            SqlCommand cmd;
            cmd = new SqlCommand(@"Select 
                                          p.package_id,
                                          p.package_name, 
                                          p.features,
                                          p.package_category,
                                          p.package_json, 
                                          p.created_by,
                                          p.last_updated_on, 
                                          sp.sellable_package_id,
                                          sp.price,
                                          sp.package_validity
                                          from t_package as p 
                                          left join t_sellable_package as sp 
                                              on sp.package_id = p.package_id");
            res = ResponseType.READER;
            return cmd;
        }), ShardHelper.SHARD0,
        new ResponseHandler<List<SellablePackage>>((reader) =>
        {

            List<SellablePackage> list = new List<SellablePackage>();

            while (reader.Read())
            {
                list.Add(extractPackageFromReader(reader));
            }
            return list;

        })).Execute();
    }

    private static SellablePackage extractPackageFromReader(SqlDataReader reader)
    {
        SellablePackage package = new SellablePackage();
        package.sellablePackageId = Convert.ToInt32(reader["sellable_package_id"]);
        package.packageId = Convert.ToInt32(reader["package_id"]);
        //package.numberOfLocations = Convert.ToInt32(reader["number_of_locations"]);
        package.price = Convert.ToInt32(reader["price"]);
        package.packageValidity = Convert.ToInt32(reader["package_validity"]);
        package.packageName = reader["package_name"].ToString();
        package.packageCategory = reader["package_category"].ToString();

        string jsonData = reader["package_json"].ToString();
        int smsCount = Convert.ToInt32(JsonConvert.DeserializeObject<PackageJson>(jsonData).smsCount);
        package.packageJsonData = new PackageJson()
        {
            smsCount = smsCount
        };

        string f = reader["features"].ToString();
        string[] features = f.Split(new char[] { ',' }, StringSplitOptions.RemoveEmptyEntries);
        package.features = new List<Feature>();
        foreach (string feature in features)
        {
            package.features.Add((Feature)Convert.ToInt32(feature));
        }
        package.createdBy = (int)reader["created_by"];
        package.createdOn = (DateTime)reader["last_updated_on"];
        package.lastModifiedOn = (DateTime)reader["last_updated_on"];
        return package;
    }

    public static List<I2E1Partner> GetUsersPartnersList(LongIdInfo adminId, AdminUserType adminUserType)
    {
        return new ShardQueryExecutor<List<I2E1Partner>>(new GetSqlCommand((out ResponseType res) =>
        {
            string query = "";
            if (adminUserType == AdminUserType.STANDARD)
            {
                query = @"select * from
                                    (
                                        select t_client.client_id,t_client.client_name, 
                                        t_partner.partner_id,t_partner.partner_name,t_partner.partner_cd,
                                        t_partner.partner_logo,t_partner.partner_image,t_partner.category,
                                        t_partner.sub_category,t_partner.micro_category,t_partner.partner_type,
                                        t_partner.account_type,t_partner.product_type,t_partner.subscription_type,
                                        t_partner.subscription_amt,t_partner.subscription_start_date,
                                        t_partner.subscription_renewal_date,t_partner.subscription_status,
                                        t_partner.discount,t_partner.zoho_account_id,t_partner.added_time,
                                        ROW_NUMBER() OVER(Partition by t_partner.partner_id ORDER BY t_partner.partner_id) AS Row_Number from t_store 
                                        left join t_partner on t_partner.partner_id = t_store.partner_id
                                        left join t_client on t_client.client_id = t_partner.client_id
                                        left join t_admin_mapping on t_admin_mapping.mapped_id =
    	                                    case
    		                                    when t_admin_mapping.mapping_type='location' then t_store.router_nas_id
    		                                    when t_admin_mapping.mapping_type='partner' then t_partner.partner_id
    		                                    when t_admin_mapping.mapping_type='client' then t_client.client_id
    	                                    else null end
                                        where t_admin_mapping.admin_id = @userid
                                    ) a
                                    where a.Row_Number = 1
                                    order by a.partner_name";
            }
            else 
            {
                query += "select partner_id,partner_name,t_partner.client_id,t_client.client_name,partner_cd, ";
                query += " partner_logo,partner_image,category,sub_category,micro_category,partner_type, ";
                query += " account_type,product_type,subscription_type,subscription_amt,subscription_start_date, ";
                query += " subscription_renewal_date,subscription_status,discount,t_partner.zoho_account_id,t_partner.added_time ";
                query += " from t_partner left join t_client on t_client.client_id = t_partner.client_id where ISNUMERIC(t_partner.partner_name) = 0 order by partner_name ";
            }
            

            SqlCommand cmd = new SqlCommand(query);

            cmd.Parameters.Add(new SqlParameter("@userid", adminId.ToSafeDbObject()));
            res = ResponseType.READER;
            return cmd;
        }), adminId.shard_id,
        new ResponseHandler<List<I2E1Partner>>((reader) =>
        {
            List<I2E1Partner> partnersList = new List<I2E1Partner>();
            while (reader.Read())
            {
                I2E1Partner partner = new I2E1Partner();
                partner.partnerId = new LongIdInfo(adminId.shard_id, DBObjectType.ADMIN_TYPE, Convert.ToInt64(reader["partner_id"]));
                partner.partnerName = reader["partner_name"].ToString();
                partner.partnerCd = reader["partner_cd"].ToString();
                partner.partnerLogo = reader["partner_logo"].ToString();
                partner.partnerImage = reader["partner_image"].ToString();
                partner.category = reader["category"].ToString();
                partner.subCategory = reader["sub_category"].ToString();
                partner.microCategory = reader["micro_category"].ToString();
                partner.accountType = reader["account_type"].ToString();
                partner.productType = reader["product_type"].ToString();
                partner.discount = Convert.ToDecimal(reader["discount"]);
                partner.zohoAccountId = reader["zoho_account_id"].ToString();

                partnersList.Add(partner);
            }
            return partnersList;
        })).Execute();
    }

    public static List<I2E1Client> GetUsersParentAccountInfo(LongIdInfo longAdminId)
    {
        return new ShardQueryExecutor<List<I2E1Client>>(new GetSqlCommand((out ResponseType res) =>
        {
            string query = "";
            query = @"select account.client_id,tc.client_name from
                        (
	                        SELECT a.router_nas_id, b.partner_id, b.client_id
	                        FROM t_store a 
	                        LEFT JOIN t_partner b ON a.partner_id = b.partner_id
	                        LEFT JOIN t_admin_mapping c ON c.mapped_id = 
	                        CASE
		                        WHEN c.mapping_type = 'location' THEN a.router_nas_id
		                        WHEN c.mapping_type = 'partner' THEN b.partner_id
		                        WHEN c.mapping_type = 'client' THEN b.client_id
	                        ELSE NULL END
	                        WHERE c.admin_id = @userid
                        ) account
                        left join t_client tc on tc.client_id = account.client_id
                        group by account.client_id,tc.client_name";

            SqlCommand cmd = new SqlCommand(query);
            cmd.Parameters.Add(new SqlParameter("@userid", longAdminId.ToSafeDbObject()));
            res = ResponseType.READER;
            return cmd;
        }), longAdminId.shard_id,
        new ResponseHandler<List<I2E1Client>>((reader) =>
        {
            List<I2E1Client> accountList = new List<I2E1Client>();
            while (reader.Read())
            {
                I2E1Client account = new I2E1Client();
                account.clientId = new LongIdInfo(longAdminId.shard_id, DBObjectType.USER_TYPE, Convert.ToInt64((int)reader["client_id"]));
                account.clientName = reader["client_name"].ToString();
                accountList.Add(account);
            }
            return accountList;
        })).Execute();
    }

    public static string getUsersAllClientsPartnersAndStores(LongIdInfo userId)
    {

        return new ShardQueryExecutor<string>(new GetSqlCommand((out ResponseType res) =>
        {

            SqlCommand cmd = new SqlCommand(@"select 
                                                t.client_id,
                                                ( 
	                                                select b_partner.partner_id,
	                                                (
		                                                select a_store.router_nas_id
		                                                from t_store a_store 
		                                                LEFT JOIN t_partner b_store ON a_store.partner_id = b_store.partner_id
		                                                LEFT JOIN t_admin_mapping c_store ON c_store.mapped_id = CASE
		                                                WHEN c_store.mapping_type = 'location' THEN a_store.router_nas_id
		                                                WHEN c_store.mapping_type = 'partner' THEN a_store.partner_id
		                                                WHEN c_store.mapping_type = 'client' THEN b_store.client_id
		                                                ELSE NULL END
		                                                WHERE b_store.partner_id = b_partner.partner_id
		                                                group by a_store.router_nas_id
		                                                for json auto
	                                                ) as locations
	                                                from t_store a_partner 
	                                                LEFT JOIN t_partner b_partner ON a_partner.partner_id = b_partner.partner_id
	                                                LEFT JOIN t_admin_mapping c_partner ON c_partner.mapped_id = CASE
	                                                WHEN c_partner.mapping_type = 'location' THEN a_partner.router_nas_id
	                                                WHEN c_partner.mapping_type = 'partner' THEN a_partner.partner_id
	                                                WHEN c_partner.mapping_type = 'client' THEN b_partner.client_id
	                                                ELSE NULL END
	                                                WHERE b_partner.client_id = t.client_id
	                                                group by b_partner.partner_id
	                                                for json auto
                                                ) as partners
                                                from
                                                (
	                                                select a_client.router_nas_id,b_client.partner_id, b_client.client_id from t_store a_client 
	                                                LEFT JOIN t_partner b_client ON a_client.partner_id = b_client.partner_id
	                                                LEFT JOIN t_admin_mapping c_client ON c_client.mapped_id = CASE
	                                                WHEN c_client.mapping_type = 'location' THEN a_client.router_nas_id
	                                                WHEN c_client.mapping_type = 'partner' THEN a_client.partner_id
	                                                WHEN c_client.mapping_type = 'client' THEN b_client.client_id
	                                                ELSE NULL END
                                                    LEFT JOIN t_admin on t_admin.user_id = c_client.admin_id
	                                                WHERE c_client.admin_id = @userId and t_admin.user_type >= 100
	                                                
                                                ) t
                                                where t.client_id is not null
                                                group by t.client_id
                                                for json path
                                                ");
            cmd.Parameters.Add(new SqlParameter("@userId", userId.ToSafeDbObject()));
            res = ResponseType.READER;
            return cmd;
        }), userId.shard_id,
        new ResponseHandler<string>((reader) =>
        {
            string JSONString = string.Empty;

            while (reader.Read())
            {
                JSONString += reader.GetValue(0).ToString();
            }

            return JSONString;
        })).Execute();
    }

    public static int InsertQuestionRequest(LongIdInfo longNasId, string mobile, string sessionId, int template)
    {
        return new ShardQueryExecutor<int>(new GetSqlCommand((out ResponseType res) =>
        {
            SqlCommand cmd = new SqlCommand(@"insert into t_question_log(nasid, mobile, request_time, session_id, template) values (@nasid, @mobile, GETUTCDATE(), @session_id, @template)
                    SELECT CAST(SCOPE_IDENTITY() AS int) AS row_id");
            cmd.Parameters.Add(new SqlParameter("@nasid", longNasId.local_value));
            cmd.Parameters.Add(new SqlParameter("@mobile", mobile));
            cmd.Parameters.Add(new SqlParameter("@session_id", sessionId));
            cmd.Parameters.Add(new SqlParameter("@template", template));
            res = ResponseType.READER;
            return cmd;
        }), longNasId.shard_id,
        new ResponseHandler<int>((reader) =>
        {
            if (reader.Read())
            {
                int rowId = (int)reader["row_id"];
                return rowId;
            }
            return 0;
        })).Execute();
    }
}