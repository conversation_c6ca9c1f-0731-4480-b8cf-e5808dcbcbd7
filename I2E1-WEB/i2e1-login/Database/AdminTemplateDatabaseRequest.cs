using i2e1_basics.Database;
using i2e1_core.Models;
using i2e1_core.Utilities;
using System.Collections.Generic;
using System.Data.SqlClient;

namespace I2E1_WEB.Database;

public class AdminTemplateDatabaseRequest
{
    public static List<Template> GetAllTemplates()
    {
        return new ShardQueryExecutor<List<Template>>(new GetSqlCommand((out ResponseType res) =>
        {
            SqlCommand cmd = new SqlCommand("select * from t_template");
            res = ResponseType.READER;
            return cmd;
        }), ShardHelper.SHARD0,
        new ResponseHandler<List<Template>>((reader) =>
        {
            var list = new List<Template>();
            while (reader.Read())
            {
                list.Add(new Template()
                {
                    id = (int)reader["template_id"],
                    templateName = reader["template_name"].ToString(),
                    templatePath = reader["template_path"].ToString(),
                    disableChat = (bool)reader["disable_chat"]
                });
            }
            return list;
        })).Execute();
    }
}