namespace I2E1_Message.DataSource
{
    public class ClientDataSourceImpl : DataSourceInterface
    {
        public override double GetDeliveryCostPerTarget(int[] deliveryTypes)
        {
            double cost = 0;
            foreach(int deliveryType in deliveryTypes)
            {
                switch (deliveryType)
                {
                    case 1: cost += .20; break;
                    case 8: cost += 0; break;
                    case 21: cost += 0; break;
                }
            }
            return cost;
        }
    }
}
