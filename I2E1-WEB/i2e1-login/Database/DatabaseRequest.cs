using i2e1_basics.Cache;
using i2e1_basics.Database;
using i2e1_basics.DynamoUtilities;
using i2e1_basics.Models;
using i2e1_basics.Utilities;
using i2e1_core.Models;
using i2e1_core.Utilities;
using I2E1_Message.Models;
using I2E1_Message.Utils;
using Microsoft.AspNetCore.Http;
using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Linq;
using wifidog_core.Models;
using wifidog_core.Models.Entity;
using wifidog_core.Utilities;
using wiom_login_share.Models;
using wiom_login_share.Utilities;
using wiom_routerplan_share.ExposeApi;
using wiom_routerplan_share.Models.RouterPlan;
using static i2e1_basics.DynamoUtilities.DynamoEnum;
namespace I2E1_WEB.Database;

public class DatabaseRequest
{
    public static OTPState GenerateOTP(User user, int deviceCount, HttpContext httpContext, string source = "i2e1", bool doSubmitOTP = false)
    {
        ModelDynamoDb<UserActivity> modelDynamoDb = new ModelDynamoDb<UserActivity>();
        var record = modelDynamoDb.GetById(user.mac, user.storegroupid > 0 ? user.storegroupid : user.backEndNasid.GetLongId());
        if(record == null || record.otpIssuedTime < DateTime.UtcNow.AddMinutes(-5))
        {
            record = new UserActivity()
            {
                mobile = user.mobile,
                mac = user.mac,
                storeGroupOrNas = user.storegroupid > 0 ? user.storegroupid : user.backEndNasid.GetLongId(),
                nasid = user.backEndNasid,
                storeGroupId = user.storegroupid,
                source = source.ToString(),
                otpIssuedTime = DateTime.UtcNow,
                otp = user.otp,
                userAgent = httpContext.Request.Headers["User-Agent"].ToString()
            };
            modelDynamoDb.Update(record);
        }
        else
        {
            user.otp = record.otp;
            user.mobile = record.mobile;
        }

        return OTPState.SUCCESS;
    }

    public static Dictionary<int, int> GetAnswers(LongIdInfo nasid, string mobile)
    {
        return new ShardQueryExecutor<Dictionary<int, int>>(new GetSqlCommand((out ResponseType res) =>
        {
            SqlCommand cmd = new SqlCommand("select question_id, option_id from t_answer with(nolock) where mobile = '" +mobile.Replace("'", "") +"' order by answer_id");
            res = ResponseType.READER;
            return cmd;
        }), nasid.shard_id,
        new ResponseHandler<Dictionary<int, int>>((reader) =>
        {
            Dictionary<int, int> answers = new Dictionary<int, int>();
            int option = 0;
            while (reader.Read())
            {
                int quesId = (int)reader["question_id"];
                if (int.TryParse(reader["option_id"].ToString(), out option))
                {
                    answers[quesId] = option;
                }
                else
                {
                    answers[quesId] = -1;
                }
            }
            return answers;
        })).Execute();
    }

    public static int GetNextQuestion(Dictionary<string, QuestionSequence> questionSequence, int searchFor, int templateId, Dictionary<int, int> answered)
    {
        List<int> looked = new List<int>();
        while (answered.ContainsKey(searchFor))
        {
            if (!looked.Contains(searchFor))
            {
                QuestionSequence sequence = null;
                questionSequence.TryGetValue(templateId + "-" + searchFor, out sequence);
                looked.Add(searchFor);
                if (sequence == null)
                {
                    questionSequence.TryGetValue("0-" + searchFor, out sequence);
                }

                if (sequence != null)
                {
                    int answeredOption = 0;
                    answered.TryGetValue(searchFor, out answeredOption);
                    if (answeredOption > 0)
                    {
                        answered.TryGetValue(searchFor, out answeredOption);
                        int j = sequence.DS.FirstOrDefault(m => m.Key == answeredOption).Value;
                        searchFor = j;
                    }
                    else
                    {
                        searchFor = sequence.NQ;
                    }
                }
            }
            else
            {
                return Constants.NO_QUESTION_LEFT;
            }
        }

        return searchFor;
    }


    public static List<Question> GetSecondPageQuestions(LongIdInfo nasid, string mobile, int templateId, List<QuestionType> questionTypes,HttpContext httpContext)
    {
        //get answers if second page questions
        if (CoreSessionUtils.GetLoginServerUser(httpContext) == null)
            return null;
        var hideQuestions = CacheHelper.GetInstance().GetQuestionConfig(CoreSessionUtils.GetLoginServerUser(httpContext).combinedSettingId);
        int ans = -999;
        int[] demoGraphic = new int[] { Constants.EMAIL_QUESTION, 14, 7, 15, 157, 158, 159};
        List<Question> questions = new List<Question>();

        int nextQuestionId = 0;
        Dictionary<int, int> answered = GetAnswers(nasid, mobile);

        switch (hideQuestions)
        {
            case "1": //hide all
                break;
            case "2": // email and demographic will be asked
                //if answered has email and demographic then stop
                int count = 0;
                for (int q = 0; q < demoGraphic.Length; q++)
                {
                    ans = 0;
                    answered.TryGetValue(demoGraphic[q], out ans);
                    if (ans != 0) count++;
                }
                break;
            case "3": // only email will be asked
                //if answered has email then stop
                answered.TryGetValue(Constants.EMAIL_QUESTION, out ans);
                if (ans == 0)
                {
                    questions.Add(GetQuestionFromId(Constants.EMAIL_QUESTION));
                    return questions;
                }
                break;
            case "4":
                templateId = 518; // Template 518 contains sequence with non-pdo questions
                break;
        }

        return questions;
    }

    public static List<Question> GetQuestions(string mobile, int templateId, List<QuestionType> questionTypes, User user)
    {
        return new List<Question>(0);
    }

    public static Question GetQuestionFromId(int questionId)
    {
        return new ShardQueryExecutor<Question>(new GetSqlCommand((out ResponseType res) =>
        {
            SqlCommand cmd = new SqlCommand("select * from t_question where question_id = @question_id and hidden = 0");
            cmd.Parameters.Add(new SqlParameter("@question_id", questionId));
            res = ResponseType.READER;
            return cmd;
        }), ShardHelper.SHARD0,
        new ResponseHandler<Question>((reader) =>
        {
            if (reader.Read())
            {
                Question question = new Question()
                {
                    id = (int)reader["question_id"],
                    templateId = (int)reader["template_id"],
                    quesText = reader["ques_text"].ToString(),
                    quesType = (QuestionType)reader["ques_type"],
                    answerType = (AnswerType)reader["answer_type"],
                    questionKey = reader["ques_key"].ToString(),
                    randomized = reader["randomized"] == DBNull.Value ? false : Convert.ToBoolean(reader["randomized"]),
                    binarySplit = reader["binary_split"] == DBNull.Value ? false : Convert.ToBoolean(reader["binary_split"])
                };

                if (question.answerType == AnswerType.RADIO || question.answerType == AnswerType.CHECKBOX)
                {
                    FetchOptions(question);
                }

                return question;
            }
            return null;
        })).Execute();
    }

    private static void FetchOptions(Question ques)
    {
        new ShardQueryExecutor<JsonResponse>(new GetSqlCommand((out ResponseType res) =>
        {
            SqlCommand cmd = new SqlCommand("Select option_id, option_text, option_img from t_option where question_id = @question_id");
            cmd.Parameters.Add(new SqlParameter("@question_id", ques.id));
            res = ResponseType.READER;
            return cmd;
        }), ShardHelper.SHARD0,
        new ResponseHandler<JsonResponse>((reader) =>
        {
            ques.options = new List<Option>();
            while (reader.Read())
            {
                Option option = new Option();
                option.id = (int)reader["option_id"];
                option.text = reader["option_text"].ToString();
                option.image = reader["option_img"].ToString();
                ques.options.Add(option);
            }
            return null;
        })).Execute();
    }

    public static bool SubmitOTP(User user, long planId, out UserSession userSession, bool doLogin = true)
    {
        userSession = null;
        bool response = false;

        if(user.IsWaniNas())
        {
            var waniCredentials = SessionCacheHelper.GetInstance().GetSession<WaniData>(CoreCacheHelper.WANI_CREDENTIALS, user.mobile, new TimeSpan(7, 0, 0, 0));
            if(waniCredentials != null && waniCredentials.password == user.otp)
            {
                return true;
            }
            return false;
        }

        ModelDynamoDb<UserActivity> modelDynamoDb = new ModelDynamoDb<UserActivity>();
        var record = modelDynamoDb.GetById(user.mac, user.storegroupid > 0 ? user.storegroupid : user.backEndNasid.GetLongId());
        if (record != null && record.otpIssuedTime > DateTime.UtcNow.AddMinutes(-15) && record.otp == user.otp)
        {
            record.loginTime = DateTime.UtcNow;
            modelDynamoDb.Update(record);
            response = true;
        }

        if (response && doLogin)
        {
            userSession = CoreCacheHelper.GetInstance().GetUserSessions(user, user.isVip, out bool noMappinggExists);
            if (userSession == null || userSession.radiusUserResponse != RadiusUserResponse.SUCCESS)
            {
                userSession = WifidogCacheHelper.GetInstance().CreateUserSessions(user, planId);
            }
        }
        return response;
    }

    public static bool CheckOTP(User user)
    {
        bool response = false;

        ModelDynamoDb<UserActivity> modelDynamoDb = new ModelDynamoDb<UserActivity>();
        var record = modelDynamoDb.GetById(user.mac, user.storegroupid > 0 ? user.storegroupid : user.backEndNasid.GetLongId());
        if (record != null && record.otpIssuedTime > DateTime.UtcNow.AddMinutes(-15) && record.otp == user.otp)
        {
            response = true;
        }

        return response;
    }

    public static bool SubmitAnswers(string mobile, LongIdInfo longNasId, int templateId, List<Question> questions)
    {
        return new ShardQueryExecutor<bool>(new GetSqlCommand((out ResponseType res) =>
        {
            SqlCommand cmd = new SqlCommand("submit_answers_v4");
            cmd.CommandType = System.Data.CommandType.StoredProcedure;
            DataTable answerTable = new DataTable();
            answerTable.Columns.Add("question_id", typeof(int));
            answerTable.Columns.Add("answer_text", typeof(string));
            answerTable.Columns.Add("option_id", typeof(int));
            answerTable.Columns.Add("next_question_id", typeof(int));
            answerTable.Columns.Add("display_index", typeof(int));
            answerTable.Columns.Add("options_order", typeof(string));
            answerTable.Columns.Add("display_time", typeof(DateTime));
            answerTable.Columns.Add("selection_time", typeof(DateTime));
            answerTable.Columns.Add("journey", typeof(int));
            foreach (var ques in questions)
            {
                var answerType = ques.answerType;

                switch (answerType)
                {
                    case AnswerType.TEXT_WITH_SEPERATE_LABEL:
                    case AnswerType.TEXT:
                    case AnswerType.STARRED:
                    case AnswerType.MULTILINE_TEXT:
                        if (String.IsNullOrEmpty(ques.answer))
                        {
                            throw new Exception("Null/Empty Text Answer");
                        }

                        answerTable.Rows.Add(ques.id, ques.answer, DBNull.Value, 0, 0, "", DBNull.Value, DBNull.Value, 0);
                        break;
                    case AnswerType.RADIO:
                        if (String.IsNullOrEmpty(ques.answer))
                        {
                            throw new Exception("Null/Empty Radio Button Answer");
                        }

                        answerTable.Rows.Add(ques.id, DBNull.Value, int.Parse(ques.answer), 0, ques.displayIndex, string.Join(",", ques.optionsOrder), DateTimeOffset.FromUnixTimeMilliseconds(ques.displayTime).UtcDateTime, DateTimeOffset.FromUnixTimeMilliseconds(ques.selectionTime).UtcDateTime, ques.journey);
                        break;
                    case AnswerType.CHECKBOX:
                        bool answerFound = false;
                        ques.options.ForEach(m =>
                        {
                            if (m.isSelected)
                            {
                                answerTable.Rows.Add(ques.id, DBNull.Value, m.id, 0, 0, "", DBNull.Value, DBNull.Value, 0);
                                answerFound = true;
                            }
                        });

                        if (!answerFound)
                        {
                            throw new Exception("Null/Empty Checkbox Answer");
                        }
                        break;
                }

            }
            cmd.Parameters.Add(new SqlParameter("@mobile", mobile));
            cmd.Parameters.Add(new SqlParameter("@nasid", longNasId.local_value));
            cmd.Parameters.Add(new SqlParameter("@template_id", templateId));
            cmd.Parameters.Add(new SqlParameter("@answers", answerTable));

            res = ResponseType.READER;
            return cmd;
        }), longNasId.shard_id,
        new ResponseHandler<bool>((reader) =>
        {
            int status = 0;
            if (reader.Read())
            {
                status = (int)reader["status"];
            }
            return true;
        })).Execute();
    }
    // check once
    public static PasswordGeneratorUser LoginPasswordGenerator(string username, string password)
    {
        return new ShardQueryExecutor<PasswordGeneratorUser>(new GetSqlCommand((out ResponseType res) =>
        {
            SqlCommand cmd = new SqlCommand("select * from t_single_nas_operations where single_operation_id = 16 and parameters like @value1");
            cmd.Parameters.Add(new SqlParameter("@value1", string.Format("_,{0},{1}%", username, password)));
            res = ResponseType.READER;
            return cmd;
        }), ShardHelper.SHARD0,
        new ResponseHandler<PasswordGeneratorUser>((reader) =>
        {
            if (reader.Read())
            {
                int nasid = (int)reader["nas_id"];
                string parameters = reader["parameters"].ToString();
                var pgUser = new PasswordGeneratorUser(username, new LongIdInfo(0, DBObjectType.ACTIVE_NAS, nasid));
                pgUser.ParseConfigString(parameters);
                pgUser.storeGroupId = CoreCacheHelper.GetInstance().GetRouterBasic(new LongIdInfo(0, DBObjectType.ACTIVE_NAS, nasid)).storeGroupId;
                return pgUser;
            }
            return null;
        })).Execute();
    }

    public static List<HomeRouterPlan> GetMyWIOMHistory(User user, DateTime startTime, DateTime endTime)
    {
        try
        {
            long startEpoch = new DateTimeOffset(startTime).ToUnixTimeSeconds();
            long endEpoch = new DateTimeOffset(endTime).ToUnixTimeSeconds();

            var result = GenericApi.GetInstance()
                .GetMyWIOMHistory(user.mobile.Trim(), user.backEndNasid.ToString(), startEpoch, endEpoch)
                .Result;

            return result ?? new List<HomeRouterPlan>();
        }
        catch (Exception ex)
        {
            Logger.GetInstance().Error($"GetMyWIOMHistory: Error occurred while calling Generic API. Exception: {ex}");
            return new List<HomeRouterPlan>();
        }
    }


    public static bool SaveWaniAppLog(string mobile, string appId, string apMac)
    {
        return new ShardQueryExecutor<bool>(new GetSqlCommand((out ResponseType res) =>
        {
            SqlCommand cmd = new SqlCommand(@"INSERT INTO t_wani_app_logs(mobile, app_id, ap_mac)
                    VALUES(@mobile, @app_id, @ap_mac)");
            cmd.Parameters.Add(new SqlParameter("@mobile", mobile));
            cmd.Parameters.Add(new SqlParameter("@app_id", appId));
            cmd.Parameters.Add(new SqlParameter("@ap_mac", apMac.ToSafeDbObject()));
            res = ResponseType.NONQUERY;
            return cmd;
        }), ShardHelper.SHARD0,
        new ResponseHandler<bool>((reader) =>
        {
            return true;
        })).Execute();
    }

    //TODO: Handle this later
    public static void LogoutUser(LongIdInfo longNasId, int storeGroupId, string mobile, bool isHomeRouter)
    {
        if (!isHomeRouter)
        {
            //UserActivityDb userActivityDb = new UserActivityDb(longNasId);

            //userActivityDb.ExecutePartition(@"IF @store_group_id = 0
            //    BEGIN
            //        update {{table_name}} set disable_autologin = 1 where mobile = @mobile and router_nas_id = @nasid
            //    END
            //    ELSE
            //    BEGIN
            //        update {{table_name}} set disable_autologin = 1 where mobile = @mobile and store_group_id = @store_group_id
            //    END",
            //(reader, shard) => { },
            //    new
            //    {
            //        mobile = mobile,
            //        nasid = longNasId,
            //        store_group_id = storeGroupId
            //    },
            //    string.Empty,
            //    PartitionKey.CURRENT_MONTH, PartitionKey.PREVIOUS_MONTH
            //);

            CoreCacheHelper.GetInstance().DeleteUserSession(new User() { mobile = mobile, nasid = longNasId.ToString(), storegroupid = storeGroupId });
        }

    }
    // TO DO DELETE
    /*public static bool RegisterUserDevice(string mobile, string mac, string deviceEndpoint, DeviceType deviceType)
    {
        return new QueryExecutor<bool>(new GetSqlCommand((out ResponseType res) =>
        {
            SqlCommand cmd = new SqlCommand("generate_device_id");
            cmd.CommandType = System.Data.CommandType.StoredProcedure;
            cmd.Parameters.Add(new SqlParameter("@mac", mac == null ? string.Empty : mac));
            cmd.Parameters.Add(new SqlParameter("@mobile", mobile == null ? string.Empty : mobile));
            cmd.Parameters.Add(new SqlParameter("@device_id", deviceEndpoint));
            cmd.Parameters.Add(new SqlParameter("@device_type", deviceType));
            res = ResponseType.NONQUERY;
            return cmd;
        }),
        new ResponseHandler<bool>((reader) =>
        {
            return true;
        })).Execute();
    }*/

    public static dynamic GetMerchantDetails(User user)
    {
        return new ShardQueryExecutor<dynamic>(new GetSqlCommand((out ResponseType res) =>
        {
            SqlCommand cmd = new SqlCommand(@"select top 1 name, username, contact_no,
                    (select shop_name from t_store where router_nas_id =  @nasid) as shop_name
                    from t_admin where user_id in (select admin_id from t_admin_mapping 
                    where mapping_type = 'Location' and mapped_id = @nasid) and user_type=0
                    and name is not null and name != '' and name not like '%-%'");
            cmd.Parameters.Add(new SqlParameter("@nasid", user.nasid));
            res = ResponseType.READER;
            return cmd;
        }),user.backEndNasid.shard_id,
        new ResponseHandler<dynamic>((reader) =>
        {
            if (reader.Read())
            {
                return new {
                    username = reader["username"].ToString(),
                    name = reader["name"].ToString(),
                    contact = reader["contact_no"].ToString(),
                    shopName = reader["shop_name"].ToString()
                };
            }
            return null;
        })).Execute();
    }
}