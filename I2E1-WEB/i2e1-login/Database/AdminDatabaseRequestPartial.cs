using i2e1_basics.Database;
using i2e1_basics.DynamoUtilities;
using i2e1_basics.Models;
using i2e1_basics.Utilities;
using i2e1_core.Models;
using i2e1_core.Models.WIOM;
using i2e1_core.Models.WIOM;
using i2e1_core.Utilities;
using I2E1_WEB.Models;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Linq;
using wifidog_core.Models;
using wiom_login_share.Models;
using wiom_routerplan_share.ExposeApi;
using wiom_routerplan_share.Models.RouterPlan;
using static i2e1_basics.DynamoUtilities.DynamoEnum;

namespace I2E1_WEB.Database;

public partial class AdminDatabaseRequest
{
    public static List<HomeRouterPlan> GetAllRegisteredHomeUsers(LongIdInfo longNasId, DateTime startTime, DateTime stopTime)
    {
        try
        {
            long startEpoch = new DateTimeOffset(startTime).ToUnixTimeSeconds();
            long stopEpoch = new DateTimeOffset(stopTime).ToUnixTimeSeconds();

            // Make async API call and block for result
            var result = GenericApi.GetInstance()
                .GetAllRegisteredHomeUsers(longNasId.GetLongId().ToString(), startEpoch, stopEpoch)
                .Result;

            if (result != null)
            {
                result.Sort((p1, p2) => p2.planStartTime.CompareTo(p1.planStartTime));
                return result;
            }

            Logger.GetInstance().Error("GetAllRegisteredHomeUsers: No data returned from Generic API.");
            return new List<HomeRouterPlan>();
        }
        catch (Exception ex)
        {
            Logger.GetInstance().Error("GetAllRegisteredHomeUsers: Exception occurred - " + ex.ToString());
            return new List<HomeRouterPlan>();
        }
    }

    public static bool LogoutRegisteredUser(LongIdInfo longNasId, int storeGroupId, string mobile)
    {
        return new ShardQueryExecutor<bool>(new GetSqlCommand((out ResponseType res) =>
        {
            SqlCommand cmd = new SqlCommand("change_router_user_mapping");
            cmd.CommandType = CommandType.StoredProcedure;
            cmd.Parameters.Add(new SqlParameter("@nasid", longNasId.local_value));
            cmd.Parameters.Add(new SqlParameter("@store_group_id", storeGroupId));
            cmd.Parameters.Add(new SqlParameter("@mobile", mobile));
            cmd.Parameters.Add(new SqlParameter("@otp_expiry_time", DateTime.UtcNow));

            res = ResponseType.NONQUERY;
            return cmd;
        }), longNasId.shard_id,
        new ResponseHandler<bool>((reader) =>
        {
            CoreCacheHelper.GetInstance().DeleteUserSession(new User { mobile = mobile, nasid = longNasId.GetLongId().ToString(), storegroupid = storeGroupId });
            return true;
        })).Execute();
    }

    public static JsonResponse GetStoreOwnerDetails(LongIdInfo nasid)
    {
        return new ShardQueryExecutor<JsonResponse>(new GetSqlCommand((out ResponseType res) =>
        {
            SqlCommand cmd = new SqlCommand(@"select username,name,email,mobile from t_admin where user_id IN (
                        select admin_id from t_admin_mapping where mapped_id = @nasid and mapping_type = 'location'
                    ) and classification = 'owner'");
            cmd.Parameters.Add(new SqlParameter("@nasid", nasid.ToSafeDbObject()));
            res = ResponseType.READER;
            return cmd;
        }), nasid.shard_id,
        new ResponseHandler<JsonResponse>((reader) =>
        {
            string name, mobile, email, username;
            if (reader.Read())
            {
                username = reader["username"] == DBNull.Value ? "" : reader["username"].ToString();
                name = reader["name"] == DBNull.Value ? "" : reader["name"].ToString();
                mobile = reader["mobile"] == DBNull.Value ? "" : reader["mobile"].ToString();
                email = reader["email"] == DBNull.Value ? "" : reader["email"].ToString();
                return new JsonResponse(ResponseStatus.SUCCESS, "", new
                {
                    username = username,
                    name = name,
                    mobile = mobile,
                    email = email
                });
            }
            return new JsonResponse(ResponseStatus.SUCCESS, "", new
            {
                username = "",
                name = "",
                mobile = "",
                email = ""
            });
        })).Execute();
    }

    public static WiomStoreOwner GetOwnerDetails(LongIdInfo nasid)
    {
        return new ShardQueryExecutor<WiomStoreOwner>(new GetSqlCommand((out ResponseType res) =>
        {
            SqlCommand cmd = new SqlCommand(@"select username,name,email,mobile from t_admin where user_id IN (
                        select admin_id from t_admin_mapping where mapped_id = @nasid and mapping_type = 'location'
                    ) and classification = 'owner'");
            cmd.Parameters.Add(new SqlParameter("@nasid", nasid.ToSafeDbObject()));
            res = ResponseType.READER;
            return cmd;
        }), nasid.shard_id,
        new ResponseHandler<WiomStoreOwner>((reader) =>
        {
            WiomStoreOwner owner = new WiomStoreOwner();
            if (reader.Read())
            {
                owner.username = reader["username"] == DBNull.Value ? "" : reader["username"].ToString();
                owner.name = reader["name"] == DBNull.Value ? "" : reader["name"].ToString();
                owner.mobile = reader["mobile"] == DBNull.Value ? "" : reader["mobile"].ToString();
                owner.email = reader["email"] == DBNull.Value ? "" : reader["email"].ToString();
                return owner;
            }
            else
                return owner;
        })).Execute();
    }

    public static WiomStoreOwner GetOwnerDetails(string username)
    {
        var longId = ShardHelper.getLongUserIdFromMobile(username);
        return new ShardQueryExecutor<WiomStoreOwner>(new GetSqlCommand((out ResponseType res) =>
        {
            SqlCommand cmd = new SqlCommand(@"select username,password,name,email,mobile from t_admin where user_id = @user_id");
            cmd.Parameters.Add(new SqlParameter("@user_id", longId.ToSafeDbObject()));
            res = ResponseType.READER;
            return cmd;
        }), longId.shard_id,
        new ResponseHandler<WiomStoreOwner>((reader) =>
        {
            WiomStoreOwner owner = new WiomStoreOwner();
            if (reader.Read())
            {
                owner.username = reader["username"] == DBNull.Value ? "" : reader["username"].ToString();
                owner.name = reader["name"] == DBNull.Value ? "" : reader["name"].ToString();
                owner.mobile = reader["mobile"] == DBNull.Value ? "" : reader["mobile"].ToString();
                owner.email = reader["email"] == DBNull.Value ? "" : reader["email"].ToString();
                owner.password = reader["password"] == DBNull.Value ? "" : reader["password"].ToString();
                return owner;
            }
            else
                return owner;
        })).Execute();
    }

    public static bool AuthorizeUser(LongIdInfo longNasId, int storeGroupId, string mobile)
    {
        if(storeGroupId == 1)
        {
            List<SecondaryRouterPlan> GetPayOnlineLists = GenericApi.GetInstance().GetSecondaryRouterCheckFreeSessionAvailability(mobile, DateTime.UtcNow, HOMEOTP.PAY_ONLINE).Result;

            List<SecondaryRouterPlan> secondaryRouterPlans = GetPayOnlineLists.Where((p)=>p.nasId.GetLongId() == longNasId.GetLongId()).ToList();
            List<SecondaryRouterPlan> secondaryRouterPlans1 = secondaryRouterPlans.Where((p) => p.planStartTime.Date == DateTime.UtcNow.Date).ToList();
            secondaryRouterPlans1.Sort((p1, p2) => p2.planStartTime.CompareTo(p1.planStartTime));
            if(secondaryRouterPlans1.Count > 0)
            {
                secondaryRouterPlans1[0].authState = true;
                _ = GenericApi.GetInstance().UpdateSecondaryPlan(secondaryRouterPlans1[0]).Result;
				CoreCacheHelper.GetInstance().UpgradeUserSession(new User { mobile = mobile, nasid = longNasId.GetLongId().ToString(), storegroupid = storeGroupId }, secondaryRouterPlans1[0].entryUnixEpochTime, 0, false);
            }
            return true;

        }
        else
        {
            return true;
        }
    }

    public static bool ChangeDataPlan(LongIdInfo longNasId, int storeGroupId, string mobile, int dataPlan, DateTime otpExpiryTime, bool? authState = null, string otp = null, string transactionId = null)
    {
        return new ShardQueryExecutor<bool>(new GetSqlCommand((out ResponseType res) =>
        {
            SqlCommand cmd = new SqlCommand("change_router_user_mapping");
            cmd.CommandType = CommandType.StoredProcedure;
            cmd.Parameters.Add(new SqlParameter("@nasid", longNasId.local_value));
            cmd.Parameters.Add(new SqlParameter("@store_group_id", storeGroupId));
            cmd.Parameters.Add(new SqlParameter("@mobile", mobile));
            cmd.Parameters.Add(new SqlParameter("@data_plan", dataPlan));
            if (authState != null)
                cmd.Parameters.Add(new SqlParameter("@auth_state", authState.Value));
            cmd.Parameters.Add(new SqlParameter("@otp_expiry_time", otpExpiryTime));
            cmd.Parameters.Add(new SqlParameter("@otp", otp));
            cmd.Parameters.Add(new SqlParameter("@transaction_id", transactionId));
            res = ResponseType.READER;
            return cmd;
        }), longNasId.shard_id,
        new ResponseHandler<bool>((reader) =>
        {
        	if (reader.Read()) {
                long id = (long)reader["id"];
                CoreCacheHelper.GetInstance().UpgradeUserSession(new WifiUser { mobile = mobile, nasid = longNasId.GetLongId().ToString(), storegroupid = storeGroupId }, id, 0, false);
                return true;
            }
            return false;
        })).Execute();
    }

    public static bool SaveBlockedWebsites(LongIdInfo nasid, List<string> blockedWebsites)
    {
        return new ShardQueryExecutor<bool>(new GetSqlCommand((out ResponseType res) =>
        {
            SqlCommand cmd = new SqlCommand("save_blocked_domains");
            cmd.CommandType = System.Data.CommandType.StoredProcedure;
            cmd.Parameters.Add(new SqlParameter("@nasid", nasid.ToSafeDbObject()));
            DataTable table = new DataTable();
            table.Columns.Add("text", typeof(string));
            blockedWebsites.ForEach(m =>
            {
                table.Rows.Add(m);
            });
            cmd.Parameters.Add(new SqlParameter("@domains", table));
            res = ResponseType.NONQUERY;
            return cmd;
        }), nasid.shard_id,
        new ResponseHandler<bool>((reader) =>
        {
            return true;
        })).Execute();
    }

    public static List<StoreGroup> GetAllStoreGroups()
    {
        return new ShardQueryExecutor<List<StoreGroup>>(new GetSqlCommand((out ResponseType res) =>
        {
            SqlCommand cmd = new SqlCommand("select * from t_store_group");
            res = ResponseType.READER;
            return cmd;
        }), ShardHelper.SHARD0,
        new ResponseHandler<List<StoreGroup>>((reader) =>
        {
            var list = new List<StoreGroup>();
            while (reader.Read())
            {
                list.Add(new StoreGroup()
                {
                    id = (int)reader["store_group_id"],
                    storeGroupName = reader["store_group_name"].ToString()
                });
            }
            return list;
        })).Execute();
    }

    public static List<KeyValuePair<int, DateTime>> GetLocationStatus(int clientId)
    {
        return new ShardQueryExecutor<List<KeyValuePair<int, DateTime>>>(new GetSqlCommand((out ResponseType res) =>
        {
            SqlCommand cmd = new SqlCommand(@"select router_nas_id, last_ping_time from t_controller 
                    where router_nas_id in
                    (select a.router_nas_id from t_store a inner
                        join t_partner b on a.partner_id = b.partner_id
                        inner join t_client c
                        on b.client_id = c.client_id
                        where
                        (c.client_id = @client_id OR len(store_tags) > 10 and substring(store_tags,11,1) = '1')
                        and a.install_state = 1)");
            cmd.Parameters.Add(new SqlParameter("@client_id", clientId));

            res = ResponseType.READER;
            return cmd;
        }), ShardHelper.SHARD0,
        new ResponseHandler<List<KeyValuePair<int, DateTime>>>((reader) =>
        {
            var list = new List<KeyValuePair<int, DateTime>>();
            while (reader.Read())
            {
                if(reader["last_ping_time"] != DBNull.Value)
                    list.Add(new KeyValuePair<int, DateTime>((int)reader["router_nas_id"], (DateTime)reader["last_ping_time"]));
            }
            return list;
        })).Execute();
    }
    //check with ashutosh: Inner Join
    /*public static Dictionary<long, JObject> GetFacebookPeriodRadacctData(Dictionary<long, JObject> dict, int dayOffset, List<LongIdInfo> locations)
    {
        new MySQLQueryExecutor<Dictionary<long, JObject>>(new GetMySqlCommand((out ResponseType res) =>
        {
            DateTime end = Util.ConvertUtcToIST(DateTime.UtcNow).Date;
            DateTime start = end.AddDays(-dayOffset);
            MySqlCommand cmd = new MySqlCommand(@"select RouterNasId, otp, count(distinct CallingStationId) AS count, 
                    sum(AcctOutputOctets) as data_upload, sum(AcctInputOctets) as data_download
                    from p_radacct a with (nolock)
					inner join t_store b
					on a.RouterNasId = b.router_nas_id
                    where b.router_nas_id in (@nasList) 
	                and (b.retag_date is null OR a.AcctStartTime > b.retag_date)
                    and AcctStartTime BETWEEN @start AND @end
                    group by RouterNasId, otp");
            cmd.Parameters.Add(new SqlParameter("@dayOffset", -dayOffset));
            cmd.Parameters.Add(new SqlParameter("@start", start));
            cmd.Parameters.Add(new SqlParameter("@end", end));
            res = ResponseType.READER;
            return cmd;
        }),
        new MySqlExecuteAllResponseHandler((reader, shardId) =>
        {
            while (reader.Read())
            {
                JObject data;
                LongIdInfo nasid = new LongIdInfo(shardId, DBObjectType.ACTIVE_NAS, Convert.ToInt64(reader["RouterNasId"]));
                if (!dict.TryGetValue(nasid.GetLongId(), out data))
                {
                    data = new JObject();
                    data.Add("router_nas_id", nasid.GetLongId());
                    dict.Add(nasid.GetLongId(), data);
                }

                var uniqueLoginsFullPolicy = data["unique_logins_full_policy_" + dayOffset + "day"];
                var uniqueLogins = data["unique_logins_" + dayOffset + "day"];

                if (reader["otp"].ToString().ToLower() == "facebook")
                    data["unique_logins_full_policy_" + dayOffset + "day"] = (int)reader["count"] + (uniqueLoginsFullPolicy == null ? 0 : (int)uniqueLoginsFullPolicy);
                else
                    data["unique_logins_" + dayOffset + "day"] = (int)reader["count"] + (uniqueLogins == null ? 0 : (int)uniqueLogins);

                var value = data["data_bytes_upload_" + dayOffset + "day"];
                if (value == null)
                    data["data_bytes_upload_" + dayOffset + "day"] = (long)reader["data_upload"];
                else
                    data["data_bytes_upload_" + dayOffset + "day"] = (long)value + (long)reader["data_upload"];

                value = data["data_bytes_download_" + dayOffset + "day"];
                if (value == null)
                    data["data_bytes_download_" + dayOffset + "day"] = (long)reader["data_download"];
                else
                    data["data_bytes_download_" + dayOffset + "day"] = (long)value + (long)reader["data_download"];
            }
            
        })).ExecuteAll(locations, "@nasList");
        return dict;
    }*/

    public static Dictionary<long, JObject> GetLocationData(Dictionary<long, JObject> dict, List<LongIdInfo> locations)
    {
        new ShardQueryExecutor<Dictionary<long, JObject>>(new GetSqlCommand((out ResponseType res) =>
        {
            SqlCommand cmd = new SqlCommand(@"select a.router_nas_id, a.shop_name, a.shop_address, a.shop_city, a.latitude, a.longitude, b.partner_name, c.mac from t_store a
                    inner join t_partner b
                    on a.partner_id = b.partner_id
                    left join t_device_build c
					on a.router_nas_id = c.router_nas_id
                    where
                    a.router_nas_id in (select value from @nasList) and (c.controller_id = 1 OR c.controller_id is null)");
            res = ResponseType.READER;
            return cmd;
        }),
        new ExecuteAllResponseHandler((reader, shardId) =>
        {
            while (reader.Read())
            {
                JObject data;
                LongIdInfo nasid = new LongIdInfo(shardId, DBObjectType.ACTIVE_NAS, Convert.ToInt64(reader["router_nas_id"]));
                if (!dict.TryGetValue(nasid.GetLongId(), out data))
                {
                    data = new JObject();
                    data.Add("router_nas_id", nasid.GetLongId());
                    dict.Add(nasid.GetLongId(), data);
                }

                data["shop_name"] = reader["shop_name"].ToString();
                data["shop_address"] = reader["shop_address"].ToString();
                data["shop_city"] = reader["shop_city"].ToString();
                data["latitude"] = reader["latitude"].ToString();
                data["longitude"] = reader["longitude"].ToString();
                data["partner_name"] = reader["partner_name"].ToString();

                string mac = reader["mac"].ToString();
                if (!string.IsNullOrEmpty(mac))
                {
                    data["bssid"] = mac.Replace(':', '-');
                }
            }
        })).ExecuteAll(locations, "@nasList");
        return dict;
    }
}