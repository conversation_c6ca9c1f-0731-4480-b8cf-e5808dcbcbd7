using i2e1_basics.Database;
using i2e1_basics.Models;
using i2e1_basics.Utilities;
using i2e1_core.Models;
using i2e1_core.Models.Client;
using i2e1_core.Utilities;
using I2E1_WEB.Models;
using I2E1_WEB.Utilities;
using Newtonsoft.Json;
using System;
using System.Collections;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Linq;

namespace I2E1_WEB.Database;

public partial class AdminDatabaseRequest
{
    public static bool UpdateAdminLogin(LongIdInfo userid)
    {
        return new ShardQueryExecutor<bool>(new GetSqlCommand((out ResponseType res) =>
        {
            SqlCommand cmd = new SqlCommand("update t_admin	set last_login_time = GETUTCDATE(),	visit_count = visit_count + 1 where user_id = @userid");
            cmd.Parameters.Add(new SqlParameter("@userid", userid));
            res = ResponseType.NONQUERY;
            return cmd;
        }),userid.shard_id,
        new ResponseHandler<bool>((reader) =>
        {
            return true;
        })).Execute();
    }

    public static List<ManagementUser> getAdminUserFromNas(LongIdInfo longNasId)
    {
        return new ShardQueryExecutor<List<ManagementUser>>(new GetSqlCommand((out ResponseType res) =>
        {
            //SqlCommand cmd = new SqlCommand("select user_id, username, user_type, parent_admin FROM t_admin where user_id in (select user_id from t_admin_nas_mapping where router_nas_id = @nasid)");
            SqlCommand cmd = new SqlCommand("select user_id, username, user_type, parent_admin FROM t_admin where user_id in (select admin_id from t_admin_mapping where mapped_id = @nasid and mapping_type = 'location')");
            cmd.Parameters.Add(new SqlParameter("@nasid", longNasId.local_value));
            res = ResponseType.READER;
            return cmd;
        }), longNasId.shard_id,
        new ResponseHandler<List<ManagementUser>>((reader) =>
        {
            List<ManagementUser> users = new List<ManagementUser>();
            while (reader.Read())
            {
                ManagementUser user = new ManagementUser();
                user.userid = new LongIdInfo(longNasId.shard_id, DBObjectType.ACTIVE_NAS, Convert.ToInt64((int)reader["user_id"]));
                user.userType = (AdminUserType)(byte)reader["user_type"];
                user.name = reader["username"].ToString();
                try
                {
                    user.parentAdminUserId = new LongIdInfo(longNasId.shard_id, DBObjectType.ACTIVE_NAS, Convert.ToInt64((int)reader["parent_admin"]));
                }
                catch
                {
                    user.parentAdminUserId = new LongIdInfo(longNasId.shard_id, DBObjectType.ACTIVE_NAS, Convert.ToInt64(-1));
                }

                users.Add(user);
            }
            return users;
        })).Execute();
    }

    public static bool CheckAdminNasMapping(LongIdInfo longUserId, AdminUserType userType, params LongIdInfo[] longNasId)
    {
        if (userType == AdminUserType.ADMIN)
            return true;
        return new ShardQueryExecutor<bool>(new GetSqlCommand((out ResponseType res) =>
        {
            //string command = string.Format("select router_nas_id from t_admin_nas_mapping where user_id = @userid and router_nas_id in ({0})",
            //    string.Join(",", nasid)
            //    );
            string command = string.Format(@"SELECT a.router_nas_id 
                                                 FROM   (SELECT * 
                                                         FROM   t_store 
                                                         WHERE  partner_id IN (SELECT partner_id 
                                                                               FROM   t_partner 
                                                                               WHERE  client_id IN (SELECT 
                                                                                      t_admin_mapping.mapped_id 
                                                                                                   FROM   t_admin_mapping 
                                                                                                   WHERE  admin_id = @userid 
                                                                                                          AND mapping_type = 'client')) 
                                                         UNION 
                                                         SELECT * 
                                                         FROM   t_store 
                                                         WHERE  partner_id IN (SELECT mapped_id AS partner_id 
                                                                               FROM   t_admin_mapping 
                                                                               WHERE  admin_id = @userid 
                                                                                      AND mapping_type = 'partner') 
                                                         UNION 
                                                         SELECT * 
                                                         FROM   t_store 
                                                         WHERE  router_nas_id IN (SELECT mapped_id AS router_nas_id 
                                                                                  FROM   t_admin_mapping 
                                                                                  WHERE  admin_id = @userid 
                                                                                         AND mapping_type = 'location')) a 
                                                 where a.router_nas_id in ({0})", string.Join(",", longNasId.Select(i=>i.local_value).ToArray())
                                          );
            SqlCommand cmd = new SqlCommand(command);
            cmd.Parameters.Add(new SqlParameter("@userid", longUserId.local_value));
            res = ResponseType.READER;
            return cmd;
        }), longUserId.shard_id,
        new ResponseHandler<bool>((reader) =>
        {
            bool[] markArray = new bool[longNasId.Length];
            while (reader.Read())
            {
                LongIdInfo tNas = new LongIdInfo(longUserId.shard_id,(long)DBObjectType.ACTIVE_NAS, Convert.ToInt64(reader["router_nas_id"]));
                int index = Array.IndexOf(longNasId, tNas);
                if (index == -1)
                    return false;
                markArray[index] = true;
            }
            foreach (var mark in markArray)
            {
                if (!mark)
                    return false;
            }
            return true;
        })).Execute();
    }

    public static Boolean ActivateAdmin(String email, LongIdInfo longUserId)
    {
        return new ShardQueryExecutor<Boolean>(new GetSqlCommand((out ResponseType res) =>
        {
            SqlCommand cmd = new SqlCommand("activate_admin");
            res = ResponseType.READER;
            cmd.CommandType = CommandType.StoredProcedure;
            cmd.Parameters.Add(new SqlParameter("@username", email));
            cmd.Parameters.Add(new SqlParameter("@userid", longUserId.local_value));
            return cmd;
        }), longUserId.shard_id,
        new ResponseHandler<Boolean>((reader) =>
        {
            if (reader.Read())
            {
                return (int)reader["result"] == 1 ? true : false;
            }
            return false;
        })).Execute();
    }

    public static JsonResponse GetCompressedStats(LongIdInfo nasid, DateTime start, DateTime end, bool showNumber)
    {
        return new ShardQueryExecutor<JsonResponse>(new GetSqlCommand((out ResponseType res) =>
        {
            SqlCommand cmd = new SqlCommand("get_stats_compressed");
            cmd.CommandType = CommandType.StoredProcedure;
            cmd.Parameters.Add(new SqlParameter("@nasid", nasid.ToSafeDbObject()));
            cmd.Parameters.Add(new SqlParameter("@start_time", start));
            cmd.Parameters.Add(new SqlParameter("@end_time", end));
            res = ResponseType.READER;
            return cmd;
        }), nasid.shard_id,
        new ResponseHandler<JsonResponse>((reader) =>
        {
            List<RouterStatCompressed> stats = new List<RouterStatCompressed>();
            while (reader.Read())
            {
                RouterStatCompressed stat = new RouterStatCompressed();
                stat.key = TimeUtils.GetMilliSecondsForJavascript((DateTime)reader["date"]).ToString();
                
                stat.SetMobile(showNumber,
                    reader["mobile"].ToString());
                stat.count = (int)reader["count"];
                stats.Add(stat);
            }
            return new JsonResponse(ResponseStatus.SUCCESS, "", stats);
        })).Execute();
    }

    public static List<dynamic> GetBandwidthReport(LongIdInfo nasid, DateTime start, DateTime end)
    {
        return new ShardQueryExecutor<List<dynamic>>(new GetSqlCommand((out ResponseType res) =>
        {
            SqlCommand cmd = new SqlCommand(@"select download_bw, timestamp from t_router_bandwidth where nasid = @nasid and 
                    timestamp between @start_time and @end_time");
            cmd.Parameters.Add(new SqlParameter("@nasid", nasid.ToSafeDbObject()));
            cmd.Parameters.Add(new SqlParameter("@start_time", start));
            cmd.Parameters.Add(new SqlParameter("@end_time", end));
            res = ResponseType.READER;
            return cmd;
        }), nasid.shard_id,
        new ResponseHandler<List<dynamic>>((reader) =>
        {
            var stats = new List<dynamic>();
            while (reader.Read())
            {
                stats.Add(new
                {
                    upload_bw = 0,
                    download_bw = (double)reader["download_bw"],
                    timestamp = ((DateTime)reader["timestamp"]).ToString(@"dd MMM HH \Hour")
                });
            }
            return stats;
        })).Execute();
    }

    public static bool SaveGroup(UserGroup group)
    {
        return new ShardQueryExecutor<bool>(new GetSqlCommand((out ResponseType res) =>
        {
            SqlCommand cmd = new SqlCommand("save_user_group");
            cmd.CommandType = System.Data.CommandType.StoredProcedure;

            cmd.Parameters.Add(new SqlParameter("@nasid", group.nasid.local_value));
            cmd.Parameters.Add(new SqlParameter("@group_id", group.groupId));
            cmd.Parameters.Add(new SqlParameter("@group_name", group.groupName));
            cmd.Parameters.Add(new SqlParameter("@parameters", string.IsNullOrEmpty(group.values) ? string.Empty : group.values));

            res = ResponseType.NONQUERY;
            return cmd;
        }), group.nasid.shard_id,
        new ResponseHandler<bool>((reader) =>
        {
            CoreCacheHelper.GetInstance().ResetAll(group.nasid);
            return true;
        })).Execute();
    }

    public static StoreUser GetStoreUser(ManagementUser user, SearchQuery searchQuery)
    {
        return new ShardQueryExecutor<StoreUser>(new GetSqlCommand((out ResponseType res) =>
        {
            SqlCommand cmd = new SqlCommand("get_admin_routers");
            cmd.CommandType = System.Data.CommandType.StoredProcedure;
            cmd.Parameters.Add(new SqlParameter("@userid", user.userid.local_value));
            cmd.Parameters.Add(new SqlParameter("@user_type", user.userType));
            res = ResponseType.READER;
            return cmd;
        }), user.userid.shard_id,
        new ResponseHandler<StoreUser>((reader) =>
        {
            StoreUser storeUser = new StoreUser();
            List<StoreDetails> storeDetails = new List<StoreDetails>();
            DateTime currentTime = DateTime.UtcNow;
            searchQuery.pageNumber = 0;
            while (reader.Read())
            {
                StoreDetails details = new StoreDetails();
                details.nasid = new LongIdInfo(user.userid.shard_id,DBObjectType.ACTIVE_NAS,Convert.ToInt64((long)reader["router_nas_id"]));
                details.storeName = reader["shop_name"].ToString();
                details.storeNameAlias = reader["external_store_identifier"].ToString();
                details.address = reader["shop_address"].ToString();
                details.location = reader["location"].ToString();
                details.city = reader["shop_city"].ToString();
                details.state = reader["shop_state"].ToString();
                details.latitude = reader["latitude"].ToString();
                details.longitude = reader["longitude"].ToString();
                details.location = reader["location"].ToString();
                //details.contactNumber = reader["contact_number"].ToString();
                //details.emailId = reader["email"].ToString();
                //details.smsLimit = (int)reader["sms_limit"];
                details.controllerid = (int)reader["controller_id"];
                details.category = reader["category"].ToString();
                details.partner = reader["partner_cd"].ToString();
                details.lastPingDelay = (currentTime - (DateTime)reader["last_ping_time"]).TotalMinutes;
                details.routerState = (RouterState)reader["install_state"];
                details.mmNasId = new LongIdInfo(user.userid.shard_id, DBObjectType.ACTIVE_NAS, Convert.ToInt64((long)reader["mm_nas_id"]));

                storeDetails.Add(details);
            }
            if (storeDetails.Count > 0)
            {
                setRoutersStatus(storeDetails);
                storeUser.name = user.name;
                storeUser.username = user.email;
                storeUser.authType = user.authType;
                storeUser.userType = user.userType;
                storeUser.storeDetails = storeDetails;
                storeUser.features = GetFeatureList(user.userid, user.userType);
                return storeUser;
            }

            return null;
        })).Execute();
    }

    public static List<StoreInfo> GetLocationsForAdmin(ManagementUser user, StoreSearchQuery searchObject, string source=null)
    {
        List<StoreInfo> storeDetails = new List<StoreInfo>(999);
        new PaginatedShardQueryExecutor<List<StoreInfo>>(new GetSqlCommand((out ResponseType res) =>
        {
            SqlCommand cmd = new SqlCommand("get_paginated_admin_routers_new");
            cmd.CommandType = System.Data.CommandType.StoredProcedure;
            cmd.Parameters.Add(new SqlParameter("@userid", user.userid.ToSafeDbObject()));
            cmd.Parameters.Add(new SqlParameter("@user_type", user.userType));
            res = ResponseType.READER;
            return cmd;
        }),
        ((reader, shardId) =>
        {
            DateTime currentTime = DateTime.UtcNow;
            while (reader.Read())
            {
                StoreInfo details = new StoreInfo();
                details.nasid = new LongIdInfo(shardId, DBObjectType.ACTIVE_NAS, reader["router_nas_id"]);
                details.storeName = reader["shop_name"].ToString();
                details.brandName = reader["brand_name"].ToString();
                details.partner = reader["partner_cd"].ToString();
                int n;
                details.partnerId = (int.TryParse(reader["partner_id"].ToString(), out n) ? (int)reader["partner_id"] : 0);

                details.clientId = (int.TryParse(reader["client_id"].ToString(), out n) ? (int)reader["client_id"] : 0);

                details.marketPlaceId = (String.IsNullOrEmpty(reader["market_place_id"].ToString()) ? 0 : (int)reader["market_place_id"]);
                details.marketPlaceName = reader["market_place_name"].ToString();
                details.marketPlaceCity = reader["market_place_city"].ToString();
                details.marketPlaceGoogleId = reader["market_place_google_id"].ToString();
                details.subLocality = reader["sub_locality"].ToString();
                details.locality = reader["locality"].ToString();
                details.storeNameAlias = reader["external_store_identifier"].ToString();
                details.address = reader["shop_address"].ToString();
                details.city = reader["shop_city"].ToString();
                details.state = reader["shop_state"].ToString();
                details.pinCode = reader["pincode"].ToString();
                details.latitude = reader["latitude"].ToString();
                details.longitude = reader["longitude"].ToString();
                details.category = reader["category"].ToString();
                details.subCategory = reader["loc_sub_cat"].ToString();
                details.microCategory = reader["micro_category"].ToString();
                details.googleCategories = reader["google_categories"].ToString();
                details.location = reader["position"].ToString();
                details.routerState = reader["install_state"] == DBNull.Value ?  RouterState.NOT_INSTALLED : (RouterState)(byte)reader["install_state"];
                details.mmNasId = (Convert.IsDBNull(reader["mm_nas_id"])) ? new LongIdInfo(shardId, DBObjectType.ACTIVE_NAS, reader["router_nas_id"])
                 : new LongIdInfo(shardId, DBObjectType.ACTIVE_NAS, reader["mm_nas_id"]);
                string tagStr = (String.IsNullOrEmpty(reader["store_tags"].ToString()) ? "0,0," : reader["store_tags"].ToString());
                var substrings = tagStr.Split(',');
                List<string> tagsArr = new List<string>();

                var result = Enum.GetValues(typeof(StoreTags)).Cast<StoreTags>().Where((x, idx) => idx < substrings.Length && substrings[idx] == "1");

                details.storeTags = result.Select(x => x.ToString()).ToList();

                details.salesId = reader["sales_id"].ToString();
                details.installerId = reader["install_id"].ToString();
                details.mode = reader["mode"].ToString();
                details.locationOpeningTime = reader["loc_open_time"].ToString();
                details.locationClosingTime = reader["loc_close_time"].ToString();
                details.deviceMac = reader["device_mac"].ToString();
                details.deviceType = reader["device_type"].ToString();
                details.deviceVersion = reader["device_version"].ToString();
                details.salesChannel = reader["sales_channel"].ToString();
                details.salesChannelName = reader["channel_name"].ToString();
                details.locationStartDate = (Convert.IsDBNull(reader["loc_start_date"]) ? null : (DateTime?)Convert.ToDateTime(reader["loc_start_date"].ToString()));
                details.locationCloseDate = (Convert.IsDBNull(reader["loc_close_date"]) ? null : (DateTime?)Convert.ToDateTime(reader["loc_close_date"].ToString()));
                details.locationRetagDate = (Convert.IsDBNull(reader["retag_date"]) ? null : (DateTime?)Convert.ToDateTime(reader["retag_date"].ToString()));
                details.closeReason = reader["close_reason"].ToString();
                details.mktPlaceAlias = reader["mkt_place_identifier"].ToString();
                details.shopDpName = reader["shop_display_name"].ToString();

                if(reader["last_ping_time"] != DBNull.Value)
                details.lastPingDelay = (currentTime - (DateTime)reader["last_ping_time"]).TotalMinutes;

                details.managerName = reader["manager_name"].ToString();
                details.managerEmail = reader["memail"].ToString();
                details.managerPhone = reader["mmobile"].ToString();

                details.legalBusinessName = reader["legal_business_name"].ToString();
                details.legalBusinessAddress = reader["legal_business_address"].ToString();
                details.gstNumber = reader["gst_number"].ToString();
                if (!string.IsNullOrEmpty(source) && source == "wiom")
                {
                    details.subscription = ClientDatabaseRequest.getActiveSubscription(details.nasid);
                }
                storeDetails.Add(details);
            }
            if (user.name == "<EMAIL>")
            {
                DemoAccountForStores(storeDetails);
            }
        }), searchObject).ExecuteAll();
        return storeDetails;
    }

    private static void DemoAccountForStores(List<StoreInfo> storeDetails)
    {
        foreach (StoreInfo store in storeDetails)
        {
            store.storeName = "Demo Outlet";
            //store.contactNumber = "Demo Store Keeper";
            //store.contactPerson = "Demo Store Keeper";
        }
    }

    public static DevicesStats getActiveInactiveDevicesCount(ManagementUser user, StoreSearchQuery searchObject)
    {
        DevicesStats devicesStats = new DevicesStats();
        new PaginatedShardQueryExecutor<DevicesStats>(new GetSqlCommand((out ResponseType res) =>
        {
            SqlCommand cmd = new SqlCommand("get_paginated_admin_routers_count");
            cmd.CommandType = System.Data.CommandType.StoredProcedure;
            cmd.Parameters.Add(new SqlParameter("@userid", user.userid.ToSafeDbObject()));
            cmd.Parameters.Add(new SqlParameter("@user_type", user.userType));
            res = ResponseType.READER;
            return cmd;
        }),
        ((reader, shardId) =>
        {
            
            while (reader.Read())
            {
                int status = (int)reader["status"];
                int count = (int)reader["count"];
                if (status == 1)
                    devicesStats.activeDeviceCount += count;

                devicesStats.totalDeviceCount += count;
            }
        }), searchObject).ExecuteAll();
        return devicesStats;
    }

    public static int GetAdminRoutersCount(ManagementUser user)
    {
        return new ShardQueryExecutor<int>(new GetSqlCommand((out ResponseType res) =>
        {
            //SqlCommand cmd = new SqlCommand("select case when @user_type = 1 or @user_type = 3 then 1 else count(*) end AS nas_count from t_admin_nas_mapping where user_id = @userid");
            SqlCommand cmd = new SqlCommand("select case when @user_type = 1 or @user_type = 3 then 1 else count(*) end AS nas_count from t_admin_mapping where admin_id = @userid and mapping_type = 'location'");
            cmd.Parameters.Add(new SqlParameter("@userid", user.userid.ToSafeDbObject()));
            cmd.Parameters.Add(new SqlParameter("@user_type", user.userType));
            res = ResponseType.READER;
            return cmd;
        }), user.userid.shard_id,
        new ResponseHandler<int>((reader) =>
        {
            int nasCount = 0;
            if (reader.Read())
            {
                nasCount = (int)reader["nas_count"];
            }

            return nasCount;
        })).Execute();
    }

    public static int DeleteContollerId(LongIdInfo nasid, int controllerid)
    {
        return new ShardQueryExecutor<int>(new GetSqlCommand((out ResponseType res) =>
        {
            SqlCommand cmd = new SqlCommand("delete from t_controller where router_nas_id=@nasid and controller_id=@controllerid");
            cmd.Parameters.Add(new SqlParameter("@nasid", nasid.ToSafeDbObject()));
            cmd.Parameters.Add(new SqlParameter("@controllerid", controllerid));
            res = ResponseType.READER;
            return cmd;
        }), nasid.shard_id,
        new ResponseHandler<int>((reader) =>
        {
            int count = 0;
            if (reader.Read())
            {
                count = 1;
            }

            return count;
        })).Execute();
    }

    public static void setRoutersStatus(List<StoreDetails> storeList)
    {
        int day = (int)DateTime.Now.DayOfWeek;
        foreach (StoreDetails store in storeList)
        {
            store.active = true;
            if (store.lastPingDelay > Constants.MAX_WARNING_TOLERANCE)
                store.active = false;
        }
    }

    public static Dictionary<string, int> GetFeatureList(LongIdInfo longUserId, AdminUserType? userType = null)
    {
        Dictionary<string, int> feature_list = new Dictionary<string, int>();
        foreach (Feature value in Enum.GetValues(typeof(Feature)))
        {
            int on = 0;
            switch (value)
            {
                case Feature.ADVANCE_SETTINGS_OPTIONS:
                case Feature.DATA_USAGE_PER_SESSION:
                case Feature.DATA_USAGE_CONTROL_MONTH:
                case Feature.BANDWIDTH_CONTROL:
                case Feature.BANDWIDTH_AFTER_EXHAUSTED:
                case Feature.MAX_DATA_USAGE_PER_DAY:
                case Feature.SESSION_TIMEOUT:
                case Feature.NUMBER_OF_DEVICE_PER_USER:
                case Feature.HIDE_QUESTIONS:
                case Feature.REPORTS:
                case Feature.SHOW_PHONE_NUMBER:
                case Feature.DATA_USAGE_TAB:
                case Feature.IMPERSONATE:
                case Feature.BLOCKED_PHONE_NUMBER_LIST:
                case Feature.PHONE_NUMBER_WHITELISTING:
                case Feature.VIP_MAC_LIST:
                case Feature.VIP_PHONE_NUMBER_LIST:
                    if (userType == AdminUserType.READ_ONLY_ADMIN)
                    {
                        on = 1;
                    }
                    on = (userType != null && userType == AdminUserType.ADMIN) ? 1 : on;
                    break;
                case Feature.STORE_OPERATIONS_NAVIGATOR:
                case Feature.ADMIN_OPERATIONS:
                case Feature.OPERATIONS_PORTAL:
                case Feature.DEVICE_CONFIG:
                case Feature.EVENT_LOGGER:
                case Feature.MEDIA_MANAGER:
                    on = -1;
                    break;
                case Feature.ADMIN_PORTAL_ACCESS:
                    on = 1;
                    break;
                default:
                    on = 0;
                    on = (userType != null && userType == AdminUserType.ADMIN) ? 1 : on;
                    break;
            }
            feature_list.Add(value.GetHashCode().ToString(), on);
        }

        

        return new ShardQueryExecutor<Dictionary<string, int>>(new GetSqlCommand((out ResponseType res) =>
        {
            SqlCommand cmd = new SqlCommand("Select user_id, feature, state from t_feature_list_new where user_id = @userid");
            cmd.Parameters.Add(new SqlParameter("@userid", longUserId.local_value));
            res = ResponseType.READER;
            return cmd;
        }), longUserId.shard_id,
        new ResponseHandler<Dictionary<string, int>>((reader) =>
        {
            while (reader.Read())
            {
                string feature = reader["feature"].ToString();
                feature_list.Remove(feature);
                if (userType == AdminUserType.ADMIN 
                    && !((Feature)int.Parse(feature) == Feature.OPERATIONS_PORTAL
                    || (Feature)int.Parse(feature) == Feature.EVENT_LOGGER
                    || (Feature)int.Parse(feature) == Feature.MEDIA_MANAGER
                    || (Feature)int.Parse(feature) == Feature.DEVICE_CONFIG))
                {
                    feature_list.Add(feature, 1);
                }
                else
                {
                    feature_list.Add(feature, Convert.ToInt32(reader["state"].ToString()));
                }
            }

            return feature_list;
        })).Execute();
    }

    public static JsonResponse GetAllFeatureList()
    {
        List<KeyValuePair<int, string>> feature_list = new List<KeyValuePair<int, string>>();
        foreach (Feature ct in Enum.GetValues(typeof(Feature)))
        {
            feature_list.Add(new KeyValuePair<int, string>((int)ct, ct.ToString()));
        }
        return new JsonResponse(ResponseStatus.SUCCESS, "", feature_list);
    }

    public static bool SaveStoreDetails(StoreDetails storeDetails)
    {
        return new ShardQueryExecutor<bool>(new GetSqlCommand((out ResponseType res) =>
        {
            SqlCommand cmd = new SqlCommand("update_t_store");
            cmd.CommandType = System.Data.CommandType.StoredProcedure;
            cmd.Parameters.Add(new SqlParameter("@name", string.IsNullOrEmpty(storeDetails.storeName) ? string.Empty : storeDetails.storeName));
            cmd.Parameters.Add(new SqlParameter("@address", string.IsNullOrEmpty(storeDetails.address) ? string.Empty : storeDetails.address));
            cmd.Parameters.Add(new SqlParameter("@city", string.IsNullOrEmpty(storeDetails.city) ? string.Empty : storeDetails.city));
            cmd.Parameters.Add(new SqlParameter("@state", string.IsNullOrEmpty(storeDetails.state) ? string.Empty : storeDetails.state));
            cmd.Parameters.Add(new SqlParameter("@latitude", string.IsNullOrEmpty(storeDetails.latitude) ? string.Empty : storeDetails.latitude));
            cmd.Parameters.Add(new SqlParameter("@longitude", string.IsNullOrEmpty(storeDetails.longitude) ? string.Empty : storeDetails.longitude));
            cmd.Parameters.Add(new SqlParameter("@install_state", storeDetails.routerState));
            cmd.Parameters.Add(new SqlParameter("@location", storeDetails.location));
            cmd.Parameters.Add(new SqlParameter("@category", storeDetails.category));
            cmd.Parameters.Add(new SqlParameter("@controllerid", storeDetails.controllerid));
            cmd.Parameters.Add(new SqlParameter("@nasid", storeDetails.nasid));
            cmd.Parameters.Add(new SqlParameter("@mm_nas_id", storeDetails.nasid.ToSafeDbObject()));
            res = ResponseType.NONQUERY;
            return cmd;
        }), storeDetails.nasid.shard_id,
        new ResponseHandler<bool>((reader) =>
        {
        return true;
        })).Execute();
    }

    public static JsonResponse GetStoreDetailsStatic(LongIdInfo nasid)
    {
        return new ShardQueryExecutor<JsonResponse>(new GetSqlCommand((out ResponseType res) =>
        {
            SqlCommand cmd = new SqlCommand("SELECT * FROM t_store WHERE router_nas_id = @nasid");
            cmd.Parameters.Add(new SqlParameter("@nasid", nasid.ToSafeDbObject()));
            res = ResponseType.READER;
            return cmd;
        }), nasid.shard_id,
       new ResponseHandler<JsonResponse>((reader) =>
       {
           List<StoreDetailsStatic> storeDetailsStatic = new List<StoreDetailsStatic>();
           DateTime currentTime = DateTime.UtcNow;
           while (reader.Read())
           {
               StoreDetailsStatic details = new StoreDetailsStatic();
               details.nasid = nasid;
               details.pincode = reader["pincode"].ToString();
               details.externalStoreIdentifier = reader["external_store_identifier"].ToString();
               details.salesId = reader["sales_id"].ToString();
               details.installerId = reader["install_id"].ToString();
               details.partnerCode = reader["partner_cd"].ToString();
               details.locationSubCategory = reader["loc_sub_cat"].ToString();
               details.mode = reader["mode"].ToString();
               details.address= reader["shop_address"].ToString();
               details.packageType = "Magic Box";
               details.GST = "ABCD1234";
               details.locationOpeningTime = reader["loc_open_time"].ToString();
               details.locationClosingTime = reader["loc_close_time"].ToString();
               details.deviceMac = reader["device_mac"].ToString();
               details.deviceType = reader["device_type"].ToString();
               details.deviceVersion = reader["device_version"].ToString();
               details.salesChannel = reader["sales_channel"].ToString();
               details.salesChannelName = reader["channel_name"].ToString();
               details.locationStartDate = (Convert.IsDBNull(reader["loc_start_date"]) ? null : (DateTime?)Convert.ToDateTime(reader["loc_start_date"].ToString()));
               details.locationCloseDate = (Convert.IsDBNull(reader["loc_close_date"]) ? null : (DateTime?)Convert.ToDateTime(reader["loc_close_date"].ToString()));
               details.locationRetagDate = (Convert.IsDBNull(reader["retag_date"]) ? null : (DateTime?)Convert.ToDateTime(reader["retag_date"].ToString()));
               details.packageExpiry = details.locationCloseDate;
               details.closeReason = reader["close_reason"].ToString();
               storeDetailsStatic.Add(details);
           }
           return new JsonResponse(ResponseStatus.SUCCESS, "", storeDetailsStatic);
       })).Execute();
    }

    public static string GetStoreContactDetails(LongIdInfo nasid)
    {
        return new ShardQueryExecutor<string>(new GetSqlCommand((out ResponseType res) =>
        {
            SqlCommand cmd = new SqlCommand(@"select ta.contact_no from t_admin ta inner join t_admin_mapping tam 
                    on ta.user_id = tam.admin_id where tam.mapped_id = @nasid and tam.mapping_type = 'location' and ta.contact_no is not null and ta.contact_no != ''
                    union
                    select contact_no from t_admin where user_id in
                    (select mapped_id from t_account_mapping1 where account_id =
                    (select account_id from t_account_mapping1 where mapped_id = @nasid and mapping_type = 'location')
                    and mapping_type = 'user')
                ");
            cmd.Parameters.Add(new SqlParameter("@nasid", nasid.ToSafeDbObject()));
            res = ResponseType.READER;
            return cmd;
        }), nasid.shard_id,
       new ResponseHandler<string>((reader) =>
       {
           while (reader.Read())
           {
               string contact = reader["contact_no"].ToString();
               bool isAllNumber = true;
               foreach(char ch in contact)
               {
                   if(char.IsLetter(ch))
                   {
                       isAllNumber = false;
                       break;
                   }
               }
               if(isAllNumber)
                return contact;
           }
           return string.Empty;
       })).Execute();
    }

    public static bool SaveStoreDetailsStatic(StoreDetailsStatic storeDetailsStatic)
    {
        return new ShardQueryExecutor<bool>(new GetSqlCommand((out ResponseType res) =>
        {

            SqlCommand cmd = new SqlCommand("update t_store set device_mac = @device_mac, loc_start_date = @loc_start_date, loc_close_date = @loc_close_date," +
                " retag_date = @retag_date" +
                " pincode = @pincode, " +
                " sales_id = @sales_id," +
                " partner_cd = @partner_cd, partner_id = @partner_id, loc_sub_cat = @loc_sub_cat, " +
                " position=@position, mode = @mode, install_id = @install_id, external_store_identifier = @externalStoreIdentifier," +
                " loc_open_time = @loc_open_time, loc_close_time = @loc_close_time, device_type = @device_type, device_version = @device_version, close_reason = @close_reason" +
                " sales_channel = @sales_channel, channel_name = @channel_name " +
                " where router_nas_id=@nasid");

            cmd.Parameters.Add(new SqlParameter("@device_mac", string.IsNullOrEmpty(storeDetailsStatic.deviceMac) ? string.Empty : storeDetailsStatic.deviceMac));
            cmd.Parameters.Add(new SqlParameter("@loc_start_date", storeDetailsStatic.locationStartDate == null ? DBNull.Value : (object)storeDetailsStatic.locationStartDate.Value));
            cmd.Parameters.Add(new SqlParameter("@loc_close_date", storeDetailsStatic.locationCloseDate == null ? DBNull.Value : (object)storeDetailsStatic.locationCloseDate.Value));
            cmd.Parameters.Add(new SqlParameter("@retag_date", storeDetailsStatic.locationRetagDate == null ? DBNull.Value : (object)storeDetailsStatic.locationRetagDate.Value));

            cmd.Parameters.Add(new SqlParameter("@close_reason", string.IsNullOrEmpty(storeDetailsStatic.closeReason) ? string.Empty : storeDetailsStatic.closeReason));
            cmd.Parameters.Add(new SqlParameter("@pincode", string.IsNullOrEmpty(storeDetailsStatic.pincode) ? string.Empty : storeDetailsStatic.pincode));
            cmd.Parameters.Add(new SqlParameter("@externalStoreIdentifier", string.IsNullOrEmpty(storeDetailsStatic.externalStoreIdentifier) ? string.Empty : storeDetailsStatic.externalStoreIdentifier));
            cmd.Parameters.Add(new SqlParameter("@sales_id", string.IsNullOrEmpty(storeDetailsStatic.salesId) ? string.Empty : storeDetailsStatic.salesId));
            cmd.Parameters.Add(new SqlParameter("@install_id", string.IsNullOrEmpty(storeDetailsStatic.installerId) ? string.Empty : storeDetailsStatic.installerId));
            cmd.Parameters.Add(new SqlParameter("@position", string.IsNullOrEmpty(storeDetailsStatic.position) ? string.Empty : storeDetailsStatic.position));
            cmd.Parameters.Add(new SqlParameter("@partner_cd", string.IsNullOrEmpty(storeDetailsStatic.partnerCode) ? string.Empty : storeDetailsStatic.partnerCode));
            cmd.Parameters.Add(new SqlParameter("@partner_id", storeDetailsStatic.partnerId != 0 ? (int)storeDetailsStatic.partnerId : 0));
            cmd.Parameters.Add(new SqlParameter("@loc_sub_cat", string.IsNullOrEmpty(storeDetailsStatic.locationSubCategory) ? string.Empty : storeDetailsStatic.locationSubCategory));
            cmd.Parameters.Add(new SqlParameter("@mode", string.IsNullOrEmpty(storeDetailsStatic.mode) ? string.Empty : storeDetailsStatic.mode));
            cmd.Parameters.Add(new SqlParameter("@loc_open_time", string.IsNullOrEmpty(storeDetailsStatic.locationOpeningTime) ? string.Empty : storeDetailsStatic.locationOpeningTime));
            cmd.Parameters.Add(new SqlParameter("@loc_close_time", string.IsNullOrEmpty(storeDetailsStatic.locationOpeningTime) ? string.Empty : storeDetailsStatic.locationOpeningTime));
            cmd.Parameters.Add(new SqlParameter("@device_type", string.IsNullOrEmpty(storeDetailsStatic.deviceType) ? string.Empty : storeDetailsStatic.deviceType));
            cmd.Parameters.Add(new SqlParameter("@device_version", string.IsNullOrEmpty(storeDetailsStatic.deviceVersion) ? string.Empty : storeDetailsStatic.deviceVersion));
            cmd.Parameters.Add(new SqlParameter("@sales_channel", string.IsNullOrEmpty(storeDetailsStatic.salesChannel) ? string.Empty : storeDetailsStatic.salesChannel));
            cmd.Parameters.Add(new SqlParameter("@channel_name", string.IsNullOrEmpty(storeDetailsStatic.salesChannelName) ? string.Empty : storeDetailsStatic.salesChannelName));
            cmd.Parameters.Add(new SqlParameter("@nasid", storeDetailsStatic.nasid.ToSafeDbObject()));
            res = ResponseType.NONQUERY;
            return cmd;
        }), storeDetailsStatic.nasid.shard_id,
       new ResponseHandler<bool>((reader) =>
       {
           return true;
       })).Execute();
    }

    public static DateTime? GetFirstWifiPasswordCommandExecutedTime(LongIdInfo nasid)
    {
        return new ShardQueryExecutor<DateTime?>(new GetSqlCommand((out ResponseType res) =>
        {
            SqlCommand cmd = new SqlCommand(@"select top 1 operation_finish_time
                    from t_router_operation WHERE router_nas_id = @nasid and operation_type = 31 and status = 1
                    order by operation_id asc");
            cmd.Parameters.Add(new SqlParameter("@nasid", nasid.ToSafeDbObject()));
            res = ResponseType.READER;
            return cmd;
        }), nasid.shard_id,
        new ResponseHandler<DateTime?>((reader) =>
        {
            if (reader.Read())
            {
                if (reader["operation_finish_time"] != DBNull.Value)
                    return (DateTime)reader["operation_finish_time"];
            }
            return null;
        })).Execute();
    }

    public static JsonResponse GetUsersInPastMonth(LongIdInfo nasid, DateTime startDate)
    {
        return RadAcctDbCalls.GetUsersInThisDuration(nasid, startDate, startDate.AddMonths(1));
    }

    public static JsonResponse GetNasBandwidth(LongIdInfo nasid)
    {
        return new ShardQueryExecutor<JsonResponse>(new GetSqlCommand((out ResponseType res) =>
        {
            SqlCommand cmd = new SqlCommand("SELECT TOP 1 upload_bw, download_bw, DATEADD(mi,330,timestamp) AS timestamp FROM t_router_bandwidth WHERE nasid = @nasid AND upload_bw > 0 AND download_bw > 0 ORDER BY timestamp DESC");
            cmd.Parameters.Add(new SqlParameter("@nasid", nasid.ToSafeDbObject()));
            res = ResponseType.READER;
            return cmd;
        }), nasid.shard_id,
        new ResponseHandler<JsonResponse>((reader) =>
        {
            Hashtable users = new Hashtable();
            while (reader.Read())
            {
                users.Add("upload", reader["upload_bw"].ToString());
                users.Add("download", reader["download_bw"].ToString());
                users.Add("timestamp", reader["timestamp"].ToString());
            }
            return new JsonResponse(ResponseStatus.SUCCESS, "", users);
        })).Execute();
    }

    public static bool AddStoreAdmin(string username, string password, LongIdInfo longNasId)
    {
        return new ShardQueryExecutor<bool>(new GetSqlCommand((out ResponseType res) =>
        {
            SqlCommand cmd = new SqlCommand("add_router_user");
            cmd.CommandType = CommandType.StoredProcedure;
            res = ResponseType.NONQUERY;
            cmd.Parameters.Add(new SqlParameter("@nasid", longNasId.local_value));
            cmd.Parameters.Add(new SqlParameter("@username", username));
            cmd.Parameters.Add(new SqlParameter("@password", password == null ? string.Empty : password));
            return cmd;
        }), longNasId.shard_id,
       new ResponseHandler<bool>((reader) =>
       {
           return true;
       })).Execute();
    }

    public static bool DeleteStoreAdmin(string username, LongIdInfo nasid)
    {
        return new ShardQueryExecutor<bool>(new GetSqlCommand((out ResponseType res) =>
        {
            //SqlCommand cmd = new SqlCommand("delete from t_admin_nas_mapping where user_id = (select user_id from t_admin where username = @username) AND router_nas_id = @nasid");
            SqlCommand cmd = new SqlCommand("delete from t_admin_mapping where admin_id = (select user_id from t_admin where username = @username) AND mapped_id = @nasid and mapping_type = 'location'");
            res = ResponseType.NONQUERY;
            cmd.Parameters.Add(new SqlParameter("@nasid", nasid.ToSafeDbObject()));
            cmd.Parameters.Add(new SqlParameter("@username", username));
            return cmd;
        }), nasid.shard_id,
       new ResponseHandler<bool>((reader) =>
       {
           return true;
       })).Execute();
    }

    public static bool DeleteAdmin(string username)
    {
        var longId = ShardHelper.getLongUserIdFromMobile(username);
        if(longId != null)
        {
            return new ShardQueryExecutor<bool>(new GetSqlCommand((out ResponseType res) =>
            {
                SqlCommand cmd = new SqlCommand("delete from t_admin_mapping where admin_id = (select user_id from t_admin where username = @username);delete from t_admin where username = @username");
                res = ResponseType.NONQUERY;
                cmd.Parameters.Add(new SqlParameter("@username", username));
                return cmd;
            }), longId.shard_id,
            new ResponseHandler<bool>((reader) =>
            {
                return true;
            })).Execute();
        }
        return false;
    }

    public static StoreDetails getStoreFromDevice(long shardId,String deviceId)
    {
        return new ShardQueryExecutor<StoreDetails>(new GetSqlCommand((out ResponseType res) =>
        {
            SqlCommand cmd = new SqlCommand("select a.router_nas_id, b.admin_id from t_controller a left join t_admin_mapping b on a.router_nas_id = b.mapped_id where (a.router_lan_mac = @device_id or a.router_wifi_mac = @device_id) and b.mapping_type = 'location'");
            res = ResponseType.READER;
            cmd.Parameters.Add(new SqlParameter("@device_id", deviceId));
            return cmd;
        }), shardId,
        new ResponseHandler<StoreDetails>((reader) =>
        {
            StoreDetails storeDetails = null;
            if (reader.Read())
            {
                int nasid = (int)reader["router_nas_id"];
                var userid = reader["user_id"];
                storeDetails = new StoreDetails();
                storeDetails.nasid = new LongIdInfo(shardId,DBObjectType.ACTIVE_NAS,Convert.ToInt64(nasid));
                storeDetails.deviceId = deviceId;
            }
            return storeDetails;
        })).Execute();
    }

    public static bool SubmitLocationDetails(StoreDetails storeDetails)
    {
        return new ShardQueryExecutor<bool>(new GetSqlCommand((out ResponseType res) =>
        {
            SqlCommand cmd = new SqlCommand("merge_store_details");
            res = ResponseType.NONQUERY;
            cmd.CommandType = CommandType.StoredProcedure;
            cmd.Parameters.Add(new SqlParameter("@router_nas_id", storeDetails.nasid.ToSafeDbObject()));
            cmd.Parameters.Add(new SqlParameter("@shop_name", storeDetails.storeName));
            cmd.Parameters.Add(new SqlParameter("@shop_city", storeDetails.city));
            cmd.Parameters.Add(new SqlParameter("@shop_state", storeDetails.state));
            cmd.Parameters.Add(new SqlParameter("@shop_address", storeDetails.address));
            cmd.Parameters.Add(new SqlParameter("@contact_person", storeDetails.contactPerson));
            cmd.Parameters.Add(new SqlParameter("@contact_number", storeDetails.contactNumber));
            cmd.Parameters.Add(new SqlParameter("@email", storeDetails.emailId));
            cmd.Parameters.Add(new SqlParameter("@device_id", storeDetails.deviceId));
            cmd.Parameters.Add(new SqlParameter("@category", storeDetails.category));
            cmd.Parameters.Add(new SqlParameter("@install_state", 1));
            return cmd;
        }), storeDetails.nasid.shard_id,
        new ResponseHandler<bool>((reader) =>
        {
            return true;
        })).Execute();
    }

    public static LongIdInfo GetNasIdFromExternalId(LongIdInfo longNasId, string externalId, string producerMobileNumber, string apiToken)
    {
        if (longNasId == null)
            return null;

        return new ShardQueryExecutor<LongIdInfo>(new GetSqlCommand((out ResponseType res) =>
        {
            SqlCommand cmd = new SqlCommand("get_nasid_store_group_id_from_token");
            cmd.CommandType = CommandType.StoredProcedure;
            res = ResponseType.READER;
            cmd.Parameters.Add(new SqlParameter("@nasid", longNasId.local_value));
            cmd.Parameters.Add(new SqlParameter("@external_id", string.IsNullOrEmpty(externalId) ? null : externalId));
            cmd.Parameters.Add(new SqlParameter("@producer_mobile", string.IsNullOrEmpty(producerMobileNumber) ? null : producerMobileNumber));
            cmd.Parameters.Add(new SqlParameter("@api_token", apiToken));
            return cmd;
        }), longNasId.shard_id,
        new ResponseHandler<LongIdInfo>((reader) =>
        {
            if (reader.Read())
            {
                return new LongIdInfo(longNasId.shard_id,DBObjectType.ACTIVE_NAS,Convert.ToInt64(reader[0]));
            }
            return null;
        })).Execute();
    }

    public static JsonResponse SourceBandwidth(List<LongIdInfo> nasids, DateTime startDate, DateTime endDate)
    {
        List<Hashtable> measurements = new List<Hashtable>();
        new ShardQueryExecutor<JsonResponse>(new GetSqlCommand((out ResponseType res) =>
        {
            SqlCommand cmd = new SqlCommand(@"SELECT timestamp, nasid, upload_bw, download_bw FROM t_router_bandwidth
                                                WHERE download_bw > 0 AND nasid IN (select value from @nasList) AND cast(timestamp AS DATE) BETWEEN @start_date AND @end_date");

            cmd.Parameters.Add(new SqlParameter("@start_date", startDate));
            cmd.Parameters.Add(new SqlParameter("@end_date", endDate));
            res = ResponseType.READER;
            return cmd;
        }),
        new ExecuteAllResponseHandler((reader, shardId) =>
        {
            while (reader.Read())
            {
                Hashtable measurement = new Hashtable();
                measurement.Add("timestamp", reader["timestamp"].ToString());
                measurement.Add("loc_id", (int)reader["nasid"]);
                measurement.Add("upload_mbps", reader["upload_bw"].ToString());
                measurement.Add("download_mbps", reader["download_bw"].ToString());
                measurements.Add(measurement);
            }
        })).ExecuteAll(nasids, "@nasList");

        return new JsonResponse(ResponseStatus.SUCCESS, "", measurements);
    }

    public static List<SellablePackage> GetPackages()
    {
        return new ShardQueryExecutor<List<SellablePackage>>(new GetSqlCommand((out ResponseType res) =>
        {

            SqlCommand cmd = new SqlCommand("select * from t_sellable_package a join t_package b on a.package_id = b.package_id");
            res = ResponseType.READER;

            return cmd;
        }), ShardHelper.SHARD0,
        new ResponseHandler<List<SellablePackage>>((reader) =>
        {
            List<SellablePackage> packages = new List<SellablePackage>();
            while (reader.Read())
            {
                packages.Add(extractPackageFromReader(reader));
            }
            return packages;
        })).Execute();
    }

    private static SellablePackage extractPackageFromReader(SqlDataReader reader)
    {
        SellablePackage package = new SellablePackage();
        package.sellablePackageId = Convert.ToInt32(reader["sellable_package_id"]);
        package.packageId = Convert.ToInt32(reader["package_id"]);
        package.numberOfLocations = Convert.ToInt32(reader["number_of_locations"]);
        package.price = Convert.ToInt32(reader["price"]);
        package.packageValidity = Convert.ToInt32(reader["package_validity"]);
        package.packageName = reader["package_name"].ToString();
        package.packageCategory = reader["package_category"].ToString();

        //string packageJsonData = reader["package_json"].ToString();
        //int smsCount = Convert.ToInt32(packageJsonData.Substring(packageJsonData.IndexOf('=') + 1));
        //package.packageJsonData = new PackageJson()
        //{
        //    smsCount = smsCount
        //};
        string jsonData = reader["package_json"].ToString();
        int smsCount = Convert.ToInt32(JsonConvert.DeserializeObject<PackageJson>(jsonData).smsCount);
        package.packageJsonData = new PackageJson()
        {
            smsCount = smsCount
        };

        string f = reader["features"].ToString();
        string[] features = f.Split(new char[] { ',' }, StringSplitOptions.RemoveEmptyEntries);
        package.features = new List<Feature>();
        foreach (string feature in features)
        {
            package.features.Add((Feature)Convert.ToInt32(feature));
        }

        return package;
    }

    public static bool SavePackage(SellablePackage package)
    {
        return new ShardQueryExecutor<bool>(new GetSqlCommand((out ResponseType res) =>
        {
            SqlCommand cmd = new SqlCommand("save_package");
            cmd.CommandType = CommandType.StoredProcedure;
            cmd.Parameters.Add(new SqlParameter("@package_name", package.packageName));
            cmd.Parameters.Add(new SqlParameter("@package_category", package.packageCategory));

            string features = "";
            foreach (Feature f in package.features)
            {
                features += package.features.ToString();
            }

            cmd.Parameters.Add(new SqlParameter("@features", features));
            cmd.Parameters.Add(new SqlParameter("@number_locations", package.numberOfLocations));
            cmd.Parameters.Add(new SqlParameter("@price", package.price));
            cmd.Parameters.Add(new SqlParameter("@package_validity", package.packageValidity));
            cmd.Parameters.Add(new SqlParameter("@modified_by", package.lastModifiedBy));

            if (package.packageJsonData == null)
            {
                package.packageJsonData = new PackageJson();
            }
            cmd.Parameters.Add(new SqlParameter("@package_json", package.packageJsonData.ToString()));

            res = ResponseType.NONQUERY;
            return cmd;
        }), ShardHelper.SHARD0,
        new ResponseHandler<bool>((reader) =>
        {
            return true;
        })).Execute();
    }

    public static bool UpgradeAdminPackage(ManagementUser user, SellablePackage sellablePackage)
    {
        return new ShardQueryExecutor<bool>(new GetSqlCommand((out ResponseType res) =>
        {
            SqlCommand cmd = new SqlCommand("upgrade_admin_package");
            cmd.Parameters.Add(new SqlParameter("@user_id", user.userid.ToSafeDbObject()));

            DateTime validTill = DateTime.UtcNow.AddMonths(sellablePackage.packageValidity);
            cmd.Parameters.Add(new SqlParameter("@valid_till", validTill));

            DataTable table = new DataTable();
            table.Columns.Add("value", typeof(int));

            foreach (Feature feature in sellablePackage.features)
            {
                table.Rows.Add(feature);
            }

            cmd.Parameters.Add(new SqlParameter("@features", table));
            cmd.CommandType = CommandType.StoredProcedure;
            res = ResponseType.READER;
            return cmd;
        }), user.userid.shard_id,
        new ResponseHandler<bool>((reader) =>
        {
            bool result = false;
            try
            {
                if (reader.Read())
                {
                    result = (int)reader["status"] == 0 ? true : false;
                }
            }
            catch
            {
            }

            return result;
        })).Execute();
    }

    public static Boolean AddSms(LongIdInfo longUserId, int smsCount)
    {
        return new ShardQueryExecutor<Boolean>(new GetSqlCommand((out ResponseType res) =>
        {
            SqlCommand cmd = null;

            if (smsCount > 0)
            {
                cmd = new SqlCommand("allot_sms_count_to_user");
                cmd.Parameters.Add(new SqlParameter("@userid", longUserId.local_value));
                cmd.Parameters.Add(new SqlParameter("@sms_count", smsCount));
                cmd.CommandType = CommandType.StoredProcedure;
            }
            res = ResponseType.READER;
            return cmd;
        }), longUserId.shard_id,
        new ResponseHandler<Boolean>((reader) =>
        {
            Boolean result = false;
            if (reader.Read())
            {
                result = true;
            }
            return result;
        })).Execute();
    }

    public static SellablePackage GetPackageById(int sellablePackageId)
    {
        return new ShardQueryExecutor<SellablePackage>(new GetSqlCommand((out ResponseType res) =>
        {

            SqlCommand cmd = new SqlCommand("select * from t_sellable_package a join t_package b on a.package_id = b.package_id and a.sellable_package_id = @sellablePackageId");
            cmd.Parameters.Add(new SqlParameter("@sellablePackageId", sellablePackageId));
            res = ResponseType.READER;

            return cmd;
        }), ShardHelper.SHARD0,
        new ResponseHandler<SellablePackage>((reader) =>
        {
            SellablePackage package = null;
            if (reader.Read())
            {
                package = extractPackageFromReader(reader);
            }
            return package;
        })).Execute();
    }
}