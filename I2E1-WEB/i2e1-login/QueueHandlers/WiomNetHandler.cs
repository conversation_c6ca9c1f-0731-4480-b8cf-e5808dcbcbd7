using System;
using System.Collections.Generic;
using i2e1_basics.ServiceBus;
using i2e1_basics.Utilities;
using i2e1_core.Utilities;
using Newtonsoft.Json;
using wifidog_core.Models.WIOM;
using wiom_routerplan_share.Models.RouterPlan;

namespace i2e1_login.SQSHandlers
{
	public class WiomNetHandler
    {
        private static readonly ServiceBusQueue queue = ServiceBusQueue.wiom_net; // Change to the appropriate queue

        public static void Register()
        {
            ServiceBusHelper.StartStandardQueueListener(queue.ToString(), (msg) =>
            {
                switch (msg.key)
                {
                    case "CREATE_PLAN":
						var nasid = LongIdInfo.IdParser(Convert.ToInt64(msg.payload["nasid"]));
						var mobile = msg.payload["mobile"].ToString();
						var transactionId = msg.payload["transactionId"].ToString();
						var amountPaid = Convert.ToDouble(msg.payload["amountPaid"].ToString());
						var planStartTime = (DateTime)msg.payload["planStartTime"];
						var source = msg.payload["source"].ToString();
						var pdoPlan = msg.payload["pdoPlan"].ToObject<PDOPlan>();
						SendPaymentSuccessPmWaniLogs(nasid, mobile, transactionId, pdoPlan, amountPaid, msg);
						ActivateInternetPlan(nasid, mobile, transactionId, pdoPlan, amountPaid, planStartTime, source);
                        break;
                }

                return true;
            });
        }

		public static void ActivateInternetPlan(LongIdInfo longNasid, string mobile, string transactionId, PDOPlan pDOPlan, double amountPaid, DateTime planStartTime, string source)
		{
			Logger.GetInstance().Info($"PaymentHandler:ActivateInternetPlan called with longNasid: {longNasid.local_value}, mobile: {mobile}, transactionId: {transactionId}, amountPaid: {amountPaid}, source: {source}");
			var routerBasic = CoreCacheHelper.GetInstance().GetRouterBasic(longNasid);

			source = source == null ? "online" : source;

			Logger.GetInstance().Info($"PaymentHandler:ActivateInternetPlan called with longNasid: {longNasid.local_value} is not a HMR nas");

			SecondaryRouterPlan pUser = new SecondaryRouterPlan()
			{
				mobile = mobile,
				charges = (int)pDOPlan.price,
				createdTime = DateTime.UtcNow,
				lcoAccountId = null,
				otp = HOMEOTP.DONE,
				dataLimit = pDOPlan.data_limit,
				timePlan = pDOPlan.time_limit,
				source = source,
				nasId = longNasid,
				planId = (int)pDOPlan.id,
				paymentMode = "online",
				totalPaid = Convert.ToInt32(amountPaid),
				planAmount = (int)pDOPlan.price,
				longSecondaryNas = new LongIdInfo(longNasid.shard_id, DBObjectType.SECONDARY_NAS, longNasid.local_value),
			};

			pUser.planStartTime = planStartTime;
			pUser.planEndTime = planStartTime.AddSeconds(pDOPlan.time_limit);

			CoreDbCalls.GetInstance().GenerateUser(pUser, longNasid, pDOPlan.concurrent_devices, true, (int)pDOPlan.price, pDOPlan.id, transactionId);
		}
		public static void SendPaymentSuccessPmWaniLogs(LongIdInfo nasId, string mobile, string transactionId, PDOPlan pDOPlan, double amountPaid, MicroServiceMessage msg)
		{
			try
			{
				if (msg != null && msg.payload != null && msg.payload.ContainsKey("uniqueIdentifier"))
				{
					var uniqueIdentifier = msg.payload["uniqueIdentifier"].ToString();
					var nasDetails = ShardHelper.GetNasDetailsFromLongNas(nasId);
					if (nasDetails != null)
					{
						PmWaniUtils.SendPMWaniLogs("payment_success_ss", uniqueIdentifier, new Dictionary<string, object>()
						{
							{ "dateTime", DateTime.UtcNow.ToString() },
							{ "mobile", mobile },
							{ "plan_id", pDOPlan.id },
							{ "uniqueIdentifier", uniqueIdentifier },
							{ "deviceId", nasDetails.deviceId },
							{ "amount", amountPaid }
						});
					}
				}
			}
			catch (Exception e) {
				Logger.GetInstance().Error($"WiomNetHandler: SendPaymentSuccessPmWaniLogs nasId: {nasId.GetLongId()} mobile: {mobile} transactionId: {transactionId} msg: {JsonConvert.SerializeObject(msg)} ex: {e.Message}");
			}

		}

	}
}
