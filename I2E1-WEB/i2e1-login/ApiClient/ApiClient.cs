using i2e1_basics.Utilities;
using i2e1_core.Models;
using i2e1_core.Models.Client;
using i2e1_core.Utilities;
using I2E1_WEB.Models;
using I2E1_WEB.Utilities;
using Microsoft.AspNetCore.Http;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.IO;
using System.Net;
using System.Text;
using wiom_login_share.Models;

namespace I2E1_WEB.ExternelApiClient;

public class I2e1ApiClient
    {
        public static string SnaplionToken = null;
        public static DateTime TokenExpiryTime;

    public static bool ValidateChaayosAccessCode(string mobile, string accessCode, out string errorMsg)
    {
        errorMsg = null;
        using (var client = new WebClient())
        {
            var values = new 
            {
                contactNumber = mobile,
                accessCode = accessCode
            };

            client.Headers[HttpRequestHeader.ContentType] = "application/json";
            client.Headers["auth"] = I2e1ConfigurationManager.GetInstance().GetSetting("Chaayos.AuthToken");
            var response = client.UploadString(I2e1ConfigurationManager.GetInstance().GetSetting("Chaayos.AuthUrl"),
                JsonConvert.SerializeObject(values));
            var jObject = JObject.Parse(response);
            var isValid = bool.Parse(jObject["valid"].ToString());
            if (isValid)
                return true;
            else
            {
                errorMsg = jObject["reasonForDecline"].ToString();
                return false;
            }
        }
    }

    public static bool ValidateFabHotelUser(string mobile, out string errorMsg)
    {
        errorMsg = null;
        using (var client = new WebClient())
        {
            var values = new
            {
                accessToken = "65be4378b0d069c0aa167bd4#$4795998",
                bookingId = "3NTEPA",
                mobileNo = mobile
            };

            var response = client.UploadString("https://www.fabhotels.com/wifiAuthorization/checkAccess",
                JsonConvert.SerializeObject(values));
            var jObject = JObject.Parse(response);
            var isValid = jObject["status"].ToString().ToLower() == "success";
            if (isValid)
                return true;
            else
            {
                errorMsg = jObject["message"].ToString();
                return false;
            }
        }
    }


    public static bool ClientUserValidator(User user, out string errorMsg)
    {
        //conf[0] - url
        //cong[1] - get/post
        //conf[2] - token
        //conf[3] - honour response
        var conf = CoreCacheHelper.GetInstance().GetAdvanceCongifInCombinedSetting(user.combinedSettingId, AdvanceConfigType.VALIDATE_USER_LOGIN).parameters;
        errorMsg = null;
        if (conf.Length > 0 && !String.IsNullOrEmpty(conf[0]))
        {
            using (var client = new WebClient())
            {
                var postData = new
                {
                    token = conf.Length >= 3 ? conf[2] : "",
                    mobile = user.mobile,
                    phone = user.mobile,
                };

                string getData = "/?mobile=" + user.mobile + 
                    "&phone=" + user.mobile +
                    "&token=" + (conf.Length >= 3 ? conf[2] : "");
                try
                {
                    //client.Headers[HttpRequestHeader.Authorization] = (conf.Length >= 3 ? conf[2] : "");
                    string response = null;
                    if (conf.Length >= 2 && conf[1] == "1")
                    {
                        response = client.DownloadString(conf[0] + getData);
                    }
                    else
                    {
                        client.Headers[HttpRequestHeader.ContentType] = "application/json";
                        response = client.UploadString(conf[0], JsonConvert.SerializeObject(postData));
                    }
                        
                    var jObject = JObject.Parse(response);

                    if(conf.Length >= 4 && conf[3] == "0")
                    {
                        return true;
                    }

                    var isValid = jObject["status"].ToString().ToLower() == "success" || jObject["status"].ToString().ToLower() == "true";
                    if (isValid)
                        return true;
                    else
                    {
                        errorMsg = jObject["message"].ToString();
                        return false;
                    }

                        
                }
                catch (Exception ex)
                {
                    WebUtils.LogErrorToCosmos(ex.Message, new { 
                        api = conf[0]
                    });
                    errorMsg = "Network failure. Please try after sometime";
                    return false;
                }
                    
            }
        }
        return true;
    }

    public static UserProfile FetchFacebookUser(string code, string accessToken,HttpContext context)
    {
        if (!string.IsNullOrEmpty(accessToken))
        {
            UserProfile userProfile;
            using (var client = new WebClient())
            {
                string response;

                try
                {
                    response = Encoding.UTF8.GetString(client.DownloadData("https://graph.facebook.com/me?fields=id,name,email,gender&access_token=" + accessToken));
                }
                catch (WebException ex)
                {
                    response = new StreamReader(ex.Response.GetResponseStream()).ReadToEnd();
                }
                Logger.GetInstance().Info("Fb accesstoken Api Response: " + response);
                var jObject = JObject.Parse(response);
                userProfile = new UserProfile();
                userProfile.mobile = jObject["id"].ToString();
                userProfile.name = jObject["name"].ToString();
                userProfile.email = jObject["email"] == null ? "" : jObject["email"].ToString();
                userProfile.gender = jObject["gender"] == null ? "" : jObject["gender"].ToString();
            }
            return userProfile;
        }
        else if (!string.IsNullOrEmpty(code))
        {
            using (var client = new WebClient())
            {
                string response = client.DownloadString(string.Format("https://graph.facebook.com/v3.1/oauth/access_token?client_id={0}&redirect_uri={1}&client_secret={2}&code={3}",
                    I2e1ConfigurationManager.GetInstance().GetSetting("facebookAppId"), WebUtils.GetCurrentHost(context) + "/Login/FacebookLogin", I2e1ConfigurationManager.GetInstance().GetSetting("facebookAppSecret"), code
                    ));
                Logger.GetInstance().Info("Fb Code Api Response: " + response);
                var jObject = JsonConvert.DeserializeObject<JObject>(response);
                return FetchFacebookUser(null, jObject["access_token"].ToString(), context);
            }
        }
        return null;
    }

    public static UserProfile FetchGoogleUser(string code)
    {
        UserProfile userProfile;
        using (var client = new WebClient())
        {
            string response;

            try
            {
                response = client.DownloadString("https://www.googleapis.com/oauth2/v3/tokeninfo?id_token=" + code);
            }
            catch (WebException ex)
            {
                response = new StreamReader(ex.Response.GetResponseStream()).ReadToEnd();
            }
            var jObject = JObject.Parse(response);
            userProfile = new UserProfile();
            userProfile.mobile = jObject["sub"].ToString();
            userProfile.name = jObject["name"].ToString();
            userProfile.email = jObject["email"].ToString();
        }
        return userProfile;
    }

    public static UserRewardPoint FetchSnaplionRewardPoints(User user)
    {
        if (SnaplionToken == null || TokenExpiryTime <= DateTime.UtcNow)
        {
            fetchSnaplionToken();
        }

        using (var client = new WebClient())
        {
            string response;
            try
            {
                client.Headers[HttpRequestHeader.ContentType] = "application/x-www-form-urlencoded";
                response = client.UploadString("https://api.snaplion.com/v1/apis/check_balance.json", "token=" + SnaplionToken + "&mobile=" + user.mobile);
            }
            catch (WebException ex)
            {
                response = new StreamReader(ex.Response.GetResponseStream()).ReadToEnd();
            }

            var jObject = JObject.Parse(response);

            if (jObject["result"] != null)
            {
                var resultObject = (JObject)jObject["result"];
                var resultCode = resultObject["status_code"];
                if (resultCode.ToString() == "401")
                {
                    SnaplionToken = null;
                }
                else if (resultCode.ToString() == "200")
                {
                    return new UserRewardPoint()
                    {
                        availableWalletPoints = int.Parse(resultObject["available_wallet_points"].ToString()),
                        usableWalletPoints = int.Parse(resultObject["max_usable_wallet_points"].ToString()),
                        walletPointValue = float.Parse(resultObject["wallet_points_value"].ToString()),
                        walletMoney = float.Parse(resultObject["wallet_money"].ToString())
                    };
                }
            }
            else
            {
                throw new Exception("unable to fetch reward points");
            }
        }

        return null;
    }

    public static void PostEventToSnaplion(string eventName, string mobile, int nasid)
    {
        using (var client = new WebClient())
        {
            string response = null;
            try
            {
                var collection = new System.Collections.Specialized.NameValueCollection();
                collection.Add("key", "21bce19debbb798029607ec8260b75bd");
                collection.Add("secret", "e24d1aeb901ceeda39477960171b0c8d");
                client.Headers[HttpRequestHeader.ContentType] = "application/json";
                var dict = new Dictionary<string,object>();
                dict.Add("event", eventName);
                dict.Add("payload", new Dictionary<string, string>() { 
                    { "mobile", mobile },
                    {"nasid", nasid.ToString()},
                    {"user", "i2e1"}
                });
                response = client.UploadString("https://api.usermatics.com/v1.0/event/?api_key=f10a24a236711f9a3115151ff170c6f5", JsonConvert.SerializeObject(dict));
            }
            catch (WebException ex)
            {
                response = new StreamReader(ex.Response.GetResponseStream()).ReadToEnd();
            }
        }
    }

    private static void fetchSnaplionToken()
    {
        using (var client = new WebClient())
        {
            string response = null;
            try
            {
                var collection = new System.Collections.Specialized.NameValueCollection();
                collection.Add("key", "a19810a2851d10a8557962abee6face6");
                collection.Add("secret", "6bed34d1e38b6cdc2c9e25a1533f9f80");
                var bytes = client.UploadValues("https://api.snaplion.com/v1/apis/get_access_token.json", collection);
                response = Encoding.ASCII.GetString(bytes);
            }
            catch (WebException ex)
            {
                response = new StreamReader(ex.Response.GetResponseStream()).ReadToEnd();
            }

            var jObject = JObject.Parse(response);

            if (jObject["result"] != null)
            {
                var resultObject = (JObject)jObject["result"];
                var resultCode = resultObject["status_code"];
                if (resultCode.ToString() == "200")
                {
                    var token = resultObject["token"];
                    var tokenExpiryTime = int.Parse(resultObject["duration"].ToString());
                    SnaplionToken = token.ToString();
                    TokenExpiryTime = DateTime.UtcNow.AddSeconds(tokenExpiryTime - 10);
                } 
            }
            else
            {
                throw new Exception("unable to fetch token");
            }
        }
    }
}