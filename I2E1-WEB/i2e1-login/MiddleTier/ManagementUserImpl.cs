using i2e1_basics.Utilities;
using i2e1_core.Models;
using i2e1_core.Models.Client;
using i2e1_core.services;
using i2e1_core.Utilities;
using System;
using System.Collections.Generic;


namespace I2E1_WEB.MiddleTier;

public class ManagementUserImpl
{
    public static void UpdateFeatureList(LongIdInfo longUser, string product) 
    {
        Dictionary<string, int> features = GetProductFeatures(product);
           
        CoreUserService.SaveFeatureList(longUser, features);
    }

    public static ManagementUser GetAdminUser(LongIdInfo userId)
    {
        return CoreUserService.GetAdminUser(userId);
    }
    // check once
    public static ManagementUser GetAdminUser(string userName)
    {
        return null;
        //return AdminDatabaseRequest.getAdminUser(userName);
    }

    public static Dictionary<string, int> GetProductFeatures(string product)
    {
        Dictionary<string, int> feature_list = new Dictionary<string, int>();

        foreach (Feature value in Enum.GetValues(typeof(Feature)))
        {
            switch (product)
            {
                case Constants.PLUS:
                    switch (value)
                    {
                        case Feature.BLOCKED_PHONE_NUMBER_LIST:
                        case Feature.VIP_PHONE_NUMBER_LIST:
                        case Feature.BANDWIDTH_CONTROL:
                        case Feature.DATA_USAGE_REPORTS:
                        case Feature.SMS_FEATURE:
                        case Feature.DATA_USAGE_TAB:
                        case Feature.SHOW_PHONE_NUMBER:
                        case Feature.ADVANCE_ANALYTICS:
                        case Feature.WIFI_METRICS:
                        case Feature.MAX_DATA_USAGE_PER_DAY:
                        case Feature.SESSION_TIMEOUT:
                        case Feature.SETTINGS_NAVIGATOR:
                        case Feature.STORE_OPERATIONS_NAVIGATOR:
                        case Feature.REPORTS:
                        case Feature.DETAILED_REPORTS:
                        case Feature.ADMIN_OPERATIONS:
                            feature_list.Add(value.GetHashCode().ToString(), 1);
                            break;

                        default:
                            feature_list.Add(value.GetHashCode().ToString(), -1);
                            break;
                    }
                    break;
                case Constants.PRIME:
                    switch (value)
                    {
                        case Feature.BLOCKED_PHONE_NUMBER_LIST:
                        case Feature.VIP_PHONE_NUMBER_LIST:
                        case Feature.BANDWIDTH_CONTROL:
                        case Feature.DATA_USAGE_REPORTS:
                        case Feature.SMS_FEATURE:
                        case Feature.PROMOTION:
                        case Feature.SMS_PRIME_FEATURE:
                        case Feature.DATA_USAGE_TAB:
                        case Feature.SHOW_PHONE_NUMBER:
                        case Feature.ADVANCE_ANALYTICS:
                        case Feature.MAX_DATA_USAGE_PER_DAY:
                        case Feature.SESSION_TIMEOUT:
                        case Feature.SETTINGS_NAVIGATOR:
                        case Feature.STORE_OPERATIONS_NAVIGATOR:
                        case Feature.WIFI_METRICS:
                        case Feature.REPORTS:
                        case Feature.DETAILED_REPORTS:
                        case Feature.ADMIN_OPERATIONS:
                            feature_list.Add(value.GetHashCode().ToString(), 1);
                            break;
                        default:
                            feature_list.Add(value.GetHashCode().ToString(), -1);
                            break;
                    }
                    break;
                case Constants.ONE:
                    switch (value)
                    {
                        case Feature.BLOCKED_PHONE_NUMBER_LIST:
                        case Feature.VIP_PHONE_NUMBER_LIST:
                        case Feature.BANDWIDTH_CONTROL:
                        case Feature.DATA_USAGE_REPORTS:
                        case Feature.SMS_FEATURE:
                        case Feature.PROMOTION:
                        case Feature.SMS_PRIME_FEATURE:
                        case Feature.DATA_USAGE_TAB:
                        case Feature.SHOW_PHONE_NUMBER:
                        case Feature.ADVANCE_ANALYTICS:
                        case Feature.MAX_DATA_USAGE_PER_DAY:
                        case Feature.SESSION_TIMEOUT:
                        case Feature.SETTINGS_NAVIGATOR:
                        case Feature.STORE_OPERATIONS_NAVIGATOR:
                        case Feature.WIFI_METRICS:
                        case Feature.MAP_METRICS:
                        case Feature.REPORTS:
                        case Feature.DETAILED_REPORTS:
                        case Feature.ADMIN_OPERATIONS:
                            feature_list.Add(value.GetHashCode().ToString(), 1);
                            break;
                        default:
                            feature_list.Add(value.GetHashCode().ToString(), -1);
                            break;
                    }
                    break;
            }
            
        }
        feature_list[Feature.WIOM_DASHBOARD.GetHashCode().ToString()] = 1;
        return feature_list;
    }
}
