using i2e1_basics.Database;
using i2e1_basics.Utilities;
using i2e1_core.Models;
using i2e1_core.services;
using i2e1_core.Utilities;
using Microsoft.AspNetCore.Http;
using Newtonsoft.Json.Linq;
using System;
using System.Data.SqlClient;
using System.Linq;
using wiom_login_share.Models;

namespace I2E1_WEB.MiddleTier;

public class UserService : CoreUserService
{
    // check once
    public static LoginUser GetUserFromToken(long shardId, string token)
    {
        return new ShardQueryExecutor<LoginUser>(new GetSqlCommand((out ResponseType res) =>
        {
            SqlCommand cmd = new SqlCommand("SELECT TOP 1 * FROM t_user WHERE token = @token ORDER BY id DESC");
            cmd.Parameters.Add(new SqlParameter("@token", token));
            res = ResponseType.READER;
            return cmd;
        }), shardId,
        new ResponseHandler<LoginUser>((reader) =>
        {
            if (reader.Read())
            {
                return CoreUserService.CreateLoginUser(new LongIdInfo(shardId, DBObjectType.USER_TYPE, reader["id"]), reader);
            }
            return null;
        })).Execute();
    }

    public static bool UpdateOtpSubmitedTime(LoginUser user)
    {
        return new ShardQueryExecutor<bool>(new GetSqlCommand((out ResponseType res) =>
        {
            SqlCommand cmd = new SqlCommand(@"UPDATE t_app_device SET otp_submitted_time = GETUTCDATE() WHERE t_user_id = @id AND fcm_token = @fcm_token");
            cmd.Parameters.Add(new SqlParameter("@id", user.id.local_value));
            cmd.Parameters.Add(new SqlParameter("@fcm_token", user.fcmToken.ToSafeDbObject()));
            res = ResponseType.READER;
            return cmd;
        }), user.id.shard_id,
       new ResponseHandler<bool>((reader) =>
       {
           return true;
       })).Execute();
    }

    public static dynamic GenerateOTPPassword(HttpContext ctx, string username, string fcmToken, string hash, string brand = null, string model = null, string appVersion = null, string advertisingId = null)
    {
        if (!string.IsNullOrEmpty(username))
        {
            var user = UserService.GenerateOTPPassword(username, fcmToken, "LoginSignal", brand, model, appVersion, advertisingId);
            if (user != null)
            {
                ManagementUser adminUser = null;
                Account account = null;
                JwtToken token = null;
                try
                {
                    adminUser = UserService.GetAdminUser(user.id.shard_id, user.mobile);
                    if (adminUser != null)
                        account = CoreAccountService.GetAccountWithUserId(adminUser.userid);

                    token = JWTManager.CreateJwtToken(user, account, adminUser, ctx);
                }
                catch (Exception ex)
                {
                    Logger.GetInstance().Info("Exception in getting account from admin user: " + user.mobile + " ex: " + ex.ToString());
                }
                return new {
                    loginUser = user,
                    adminUser = adminUser,
                    account = account,
                    jwtToken = token
                };
            }
        }
        return null;
    }


    public static LoginUser GenerateOTPPassword(string mobile, string fcmToken, string hash, string brand = null, string model = null, string appVersion = null, string advertisingId = null)
    {
        string otp = new Random().Next(1000, 10000).ToString();
        if (hash == "LoginSignal")
            otp = "SIGNAL";
        LoginUser user = CoreUserService.GetUpdatedAppDevice(App.HOME_ROUTER, mobile, 0, 0, fcmToken, otp, brand, model, appVersion, advertisingId);
    
        if (user == null)
            return null;
        if (user.otp == "EXCEED_LOGIN_LIMIT" || Constants.CUSTOMER_APP_UNAUTHORIZED_LOGIN_POSSIBLE_CASES.Contains(user.otp))
            return user;
        user.otp = otp;

        if (hash != "LoginSignal")
        {
            if (!string.IsNullOrEmpty(hash))
                CoreSmsSender.SendSMSViaGupshup(string.Format("<#> Your WIOM OTP is {0}\n{1}", otp, hash), mobile, "myWIOM");
            else
                CoreSmsSender.SendSMSViaGupshup(string.Format("Your WIOM OTP is {0}", otp), mobile, "myWIOM");
        }

        return user;
    }

    public static JObject SignalLogin(string userSsid, string ssidPass)
    {
        int signalCount = 0;
        JObject signalLoginDetails = new JObject();
        new ShardQueryExecutor<JObject>(new GetSqlCommand((out ResponseType res) =>
        {
            SqlCommand cmd = new SqlCommand(@"
                declare @nasid bigint
                declare @account_id bigint
                declare @username varchar(10)
                declare @userId bigint
                declare @signal_count int = (SELECT COUNT(DISTINCT(nas_id)) FROM t_single_nas_operations
                    WHERE single_operation_id = 18 AND parameters = @ssid)

                IF @signal_count = 1
                BEGIN
	                SET @nasid = (SELECT nas_id FROM t_single_nas_operations
                        WHERE single_operation_id = 18 AND parameters = @ssid)

	                IF @pass = (SELECT parameters FROM t_single_nas_operations WHERE single_operation_id = 20 AND nas_id = @nasid)
	                BEGIN
		                set @account_id = (SELECT account_id FROM t_account_mapping1 WHERE mapped_id = @nasid and mapping_type = 'location')
		
		                IF @account_id > 0
		                BEGIN
                            SET @userId = (SELECT mapped_id FROM t_account_mapping1 WHERE account_id = @account_id AND mapping_type = 'user')
			                SET @username = (SELECT username FROM t_admin WHERE user_id = @userId)
		                END
	                END
	                
                END
                SELECT @signal_count AS signal_count, @nasid AS nasid, @username AS username, @userId as userId, @account_id AS account_id");


            cmd.Parameters.Add(new SqlParameter("@ssid", userSsid));
            cmd.Parameters.Add(new SqlParameter("@pass", ssidPass));
            res = ResponseType.READER;
            return cmd;
        }), 
        new ExecuteAllResponseHandler((reader, shardId) =>
        {
            JObject obj = new JObject();
            if (reader.Read())
            {
                int sc = reader.GetValueOrDefault<int>("signal_count");
                if(sc == 1) 
                { 
                    signalLoginDetails["username"] = reader.GetValueOrDefault<string>("username");
                    signalLoginDetails["userId"] = reader.GetValueOrDefault<long>("userId");
                    signalLoginDetails["nasid"] = reader.GetValueOrDefault<long>("nasid");
                    signalLoginDetails["accountId"] = reader.GetValueOrDefault<long>("account_id");
                    signalLoginDetails["shardId"] = shardId;
                }
                signalCount += sc;
                signalLoginDetails["signalCount"] = signalCount;
            }
        })).ExecuteAll();
        return signalLoginDetails;
    }

    public static JObject FetchUserOnSignal(string userSsid)
    {
        int signalCount = 0;
        JObject signalLoginDetails = new JObject();
        new ShardQueryExecutor<JObject>(new GetSqlCommand((out ResponseType res) =>
        {
            SqlCommand cmd = new SqlCommand(@"
                declare @nasid bigint
                declare @account_id bigint
                declare @username varchar(20)
                declare @userId bigint
                declare @signal_count int = (SELECT COUNT(DISTINCT(nas_id)) FROM t_single_nas_operations
                    WHERE single_operation_id = 18 AND parameters = @ssid)

                IF @signal_count = 1
                BEGIN
	                SET @nasid = (SELECT nas_id FROM t_single_nas_operations
                        WHERE single_operation_id = 18 AND parameters = @ssid)

	                set @account_id = (SELECT account_id FROM t_account_mapping1 WHERE mapped_id = @nasid and mapping_type = 'location')
		
		            IF @account_id > 0
		            BEGIN
                        SET @userId = (SELECT mapped_id FROM t_account_mapping1 WHERE account_id = @account_id AND mapping_type = 'user')
			            SET @username = (SELECT username FROM t_admin WHERE user_id = @userId)
		            END
	                
                END
                SELECT @signal_count AS signal_count, @nasid AS nasid, @username AS username, @userId as userId, @account_id AS account_id");


            cmd.Parameters.Add(new SqlParameter("@ssid", userSsid));
            res = ResponseType.READER;
            return cmd;
        }),
        new ExecuteAllResponseHandler((reader, shardId) =>
        {
            JObject obj = new JObject();
            if (reader.Read())
            {
                int sc = reader.GetValueOrDefault<int>("signal_count");
                if (sc == 1)
                {
                    signalLoginDetails["username"] = reader.GetValueOrDefault<string>("username");
                    signalLoginDetails["userId"] = reader.GetValueOrDefault<long>("userId");
                    signalLoginDetails["nasid"] = reader.GetValueOrDefault<long>("nasid");
                    signalLoginDetails["accountId"] = reader.GetValueOrDefault<long>("account_id");
                    signalLoginDetails["shardId"] = shardId;
                }
                signalCount += sc;
                signalLoginDetails["signalCount"] = signalCount;
            }
        })).ExecuteAll();
        return signalLoginDetails;
    }
}
