using i2e1_core.Models;
using i2e1_core.Models.Client;
using I2E1_Message.Models;
using I2E1_Message.Models.Client;
using System;
using System.Collections.Generic;

namespace I2E1_WEB.Utilities;

public class TimeUtils
{
    private static DateTime baseTime = new DateTime(1970, 1, 1);

    public static void normalizeTime(ref DateTime startTime, ref DateTime endTime)
    {
        startTime = new DateTime(startTime.Year, startTime.Month, startTime.Day);
        endTime = new DateTime(endTime.Year, endTime.Month, endTime.Day, 23, 59, 59);
    }

    public static long GetMilliSecondsForJavascript(DateTime time)
    {
        return (long)(time - baseTime).TotalMilliseconds;
    }
}