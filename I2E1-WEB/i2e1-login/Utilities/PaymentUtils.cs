using i2e1_basics.Utilities;
using i2e1_core.Utilities;
using wifidog_core.Models.WIOM;

namespace I2E1_WEB.Utilities;

public class PaymentUtils
{

    public static PDOPlan getplansForNas(LongIdInfo nasid, long planId)
    {
        var plans = CoreCacheHelper.GetInstance().GetActivePlansOnNas(nasid);
        PDOPlan plan = null;
        if (plans != null || plans.Count == 0)
        {
            plan = plans.Find(m => m.id == planId);

        }
        return plan;
    }

    
}
