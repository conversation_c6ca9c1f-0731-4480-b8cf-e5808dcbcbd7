using i2e1_core.Utilities;
using I2E1_Message.Models;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Net;
using System.Text;
using System.Xml;

namespace I2E1_Message.Utils
{
    public class TRAIUtils
    {
        public static JObject appprovider;

        public static FinalObject setXmlDataInCache()
        {
            return getaplist();
        }

        public static Dictionary<string, wanListLinq> createTraiDataForLinq()
        {
            return getaplistforLinq();
        }

        private static Dictionary<string, wanListLinq> getaplistforLinq()
        {
            Dictionary<string, wanListLinq> linqList = new Dictionary<string, wanListLinq>();
            JObject pdolist = fetchPDOAList();
            JObject jsonObject = new JObject();
            var values = pdolist.ToObject<Dictionary<string, PDOData>>();
            foreach (var value in values)
            {
                if (value.Value.name.Equals("CSC Wi-Fi Choupal Services India Pvt. Ltd."))
                    continue;
                wanListLinq wanList = new wanListLinq();
                
                string url = value.Value.apUrl;
                url = url.Replace("https", "http");
                JObject tempdata = new JObject();
                try
                {
                    XmlTextReader reader = GetXmlDataFromUrl(url);

                    if (reader != null)
                    {
                        string mackey = "";
                        while (reader.Read())
                        {
                            
                            if (!reader.HasAttributes)
                                continue;
                            if (reader.Name == "AP")
                            {
                                wanList.providerId = value.Value.id;
                                string location = reader.GetAttribute("geoLoc");
                                location = string.IsNullOrEmpty(location) ? "," : location;
                                wanList.ssid = reader.GetAttribute("ssid");
                                string[] locstring = location.Split(',');
                                if (locstring.Length > 1)
                                {
                                    wanList.latitude = locstring[0];
                                    wanList.longitude = locstring[1];
                                }

                                string mactoset = reader.GetAttribute("macid");
                                mactoset = mactoset.Replace("-", ":");
                                mactoset = CoreUtil.GetNormalisedMac(mactoset);
                                if (mactoset.Length < 17)
                                    continue;
                                string cpurl = "";
                                if (reader.GetAttribute("cpURL") != null)
                                {
                                    cpurl = reader.GetAttribute("cpURL").Trim();
                                }
                                else if (reader.GetAttribute("cpUrl") != null)
                                {
                                    cpurl = reader.GetAttribute("cpUrl").Trim();
                                }

                                if (cpurl.Equals("http://wifidabba.com/portal"))
                                    cpurl = "http://192.168.120.1/trai.php";
                                wanList.cpUrl = cpurl;
                                mackey = mactoset;

                            }

                            if (reader.Name == "Tag")
                            {
                                switch (reader.GetAttribute("name"))
                                {
                                    case "PAYMENTMODES":
                                        wanList.PAYMENTMODES = reader.GetAttribute("value");
                                        if(!linqList.ContainsKey(mackey))
                                            linqList.Add(mackey, wanList);
                                        break;
                                }
                            }

                        }

                    }

                }
                catch (Exception e)
                {
                }

                
            }
            return linqList;
        }

        private static FinalObject getaplist()
        {
            JObject pdolist = fetchPDOAList();
            bool complete = true;

            FinalObject finalObject = new FinalObject();
            finalObject.version = "1.0";
            finalObject.complete = complete;
            finalObject.pdolist = pdolist;
            finalObject.appproviderdata = appprovider;
            finalObject.timestamp = DateTime.UtcNow;
            finalObject.expiry = DateTime.UtcNow.AddHours(24);
            return finalObject;
        }

        public static XmlTextReader GetXmlDataFromUrl(string url)
        {
            ServicePointManager.SecurityProtocol = SecurityProtocolType.Tls12;
            HttpWebRequest myReq = (HttpWebRequest)WebRequest.Create(url);

            WebResponse response = myReq.GetResponse();

            Stream responseStream = response.GetResponseStream();

            XmlTextReader reader = new XmlTextReader(responseStream);

            return reader;
        }

        private static JObject fetchPDOAList()
        {
            JObject jsonObject = new JObject();
            try
            {
                XmlTextReader reader = GetXmlDataFromUrl("https://pmwani.gov.in/wani/registry/wani_providers.xml");

                List<KeyValuePair<string, PDOData>> pdo_List = new List<KeyValuePair<string, PDOData>>();
                appprovider = new JObject();
                bool provider = false, app = false;
                PDOData data = null;
                AppProviderData pdata = null;
                while (reader.Read())
                {
                    if (!reader.HasAttributes)
                        continue;

                    if (reader.Name == "PDOA")
                    {
                        provider = true;
                        data = new PDOData();
                        data.apUrl = reader.GetAttribute("apUrl");
                        data.email = reader.GetAttribute("email");
                        data.id = reader.GetAttribute("id");
                        data.name = reader.GetAttribute("name");
                        data.phone = reader.GetAttribute("phone");
                        data.rating = reader.GetAttribute("rating");
                        data.status = reader.GetAttribute("status");
                        continue;
                    }
                    if (reader.Name == "Key" && provider)
                    {
                        provider = false;
                        data.keys = new keydata();
                        data.keys.exp = reader.GetAttribute("exp");
                        data.keys.key = reader.ReadInnerXml();
                        jsonObject[data.id] = JToken.FromObject(data);
                        continue;
                    }
                    if (reader.Name == "AppProvider")
                    {
                        app = true;
                        pdata = new AppProviderData();
                        pdata.authUrl = reader.GetAttribute("authUrl");
                        pdata.id = reader.GetAttribute("id");
                        pdata.name = reader.GetAttribute("name");
                        continue;
                    }
                    if (reader.Name == "Key" && app)
                    {
                        app = false;
                        pdata.keys = new keydata();
                        pdata.keys.exp = reader.GetAttribute("exp");
                        pdata.keys.key = reader.ReadInnerXml();
                        appprovider[pdata.id] = JToken.FromObject(pdata);
                        continue;
                    }

                }

                CacheHelper.GetInstance().SetDataForTrai("PDOLIST", "0", jsonObject);
                CacheHelper.GetInstance().SetDataForTrai("APPPOVIDERLIST", "0", appprovider);
            }
            catch
            {
                jsonObject = CacheHelper.GetInstance().GetTRAIDATA("PDOLIST", jsonObject);
                appprovider = CacheHelper.GetInstance().GetTRAIDATA("APPPROVIDERPDOLIST", appprovider);

            }
            return jsonObject;
        }

        public static string base64encode(byte[] data)
        {
            return Convert.ToBase64String(data);
        }

        public static byte[] convertfrombase64(string data)
        {
            return Convert.FromBase64String(data);
        }

    }
}