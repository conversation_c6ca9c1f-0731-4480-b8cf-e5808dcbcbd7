using i2e1_basics.Utilities;
using I2E1_WEB.Database;
using I2E1_WEB.Models;
using System;
using wiom_router_api.Models;

namespace I2E1_WEB.Utilities;

public class ApiUtils
{

    public static ListCheckDataType? mapUpdateListToConfigType(UpdateList updateList)
    {
        switch (updateList)
        {
            case Models.UpdateList.BLACK_LIST:
                return ListCheckDataType.BLOCKED_LIST;
            case Models.UpdateList.WHITE_LIST:
                return ListCheckDataType.WHITE_LIST;
            case Models.UpdateList.VIP_LIST:
                return ListCheckDataType.VIP_LIST;
            case Models.UpdateList.MAC_WHITE_LIST:
                return ListCheckDataType.MAC_WHITELISTING;
        }

        return null;
    }

    public static LongIdInfo GetNasIdFromExternalId(string externalId, LongIdInfo longNasId, string producerMobileNumber, string apiToken, out int storeGroupId)
    {
        storeGroupId = 0;
        if (apiToken == I2e1ConfigurationManager.GetInstance().GetSetting("SWAP.Token"))
        {
            return longNasId;
        }
        var finalResult = AdminDatabaseRequest.GetNasIdFromExternalId(longNasId, externalId, producerMobileNumber, apiToken);

        if (finalResult == null)
        {
            throw new Exception("Invalid location id or no location attached from External Id : "+externalId);
        }

        if (longNasId == null && string.IsNullOrEmpty(externalId))
        {
            storeGroupId = (int)finalResult.local_value;
            return null;
        }

        return finalResult;
    }
}