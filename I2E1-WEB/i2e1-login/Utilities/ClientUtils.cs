using i2e1_basics.Database;
using i2e1_basics.Utilities;
using i2e1_core.Models.Client;
using System;
using System.Data.SqlClient;

namespace I2E1_WEB.Utils;

public class ClientUtils
{
    public static int GetClientIdFromAdminId(LongIdInfo longAdminId)
    {
        return new ShardQueryExecutor<int>(new GetSqlCommand((out ResponseType res) =>
        {
            SqlCommand cmd = new SqlCommand(@"select client_id from
                        (select router_nas_id, a.partner_id, client_id
                         from t_store a left join t_partner b
                                 on a.partner_id = b.partner_id) a left join t_admin_mapping b
                            on b.mapped_id = case
                                             when b.mapping_type = 'location' then a.router_nas_id
                                             when b.mapping_type = 'partner' then a.partner_id
                                             when b.mapping_type = 'client' then a.client_id
                                             else null end
                        where b.admin_id = @admin_id
                    group by client_id, admin_id
                    order by admin_id");
            cmd.Parameters.Add(new SqlParameter("@admin_id", longAdminId.local_value));
            res = ResponseType.READER;
            return cmd;
        }), longAdminId.shard_id,
        new ResponseHandler<int>((reader) =>
        {
            if (reader.Read())
            {
                if (reader["client_id"] != DBNull.Value)
                    return (int)reader["client_id"];
            }
            return 0;
        })).Execute();
    }

    public static I2E1Client UpdateClientDetails(long shardId, I2E1Client client)
    {
        return new ShardQueryExecutor<I2E1Client>(new GetSqlCommand((out ResponseType res) =>
        {
            SqlCommand cmd = new SqlCommand(@"
                IF EXISTS (select 1 from t_client where client_id = @clientId or client_name = @clientName)
                BEGIN
                    update t_client set client_name = COALESCE(@clientName,client_name),
                    legal_business_name=COALESCE(@clientLegalBusinessName,legal_business_name),
                    gst_number=COALESCE(@clientGSTNumber,gst_number),
                    contract_signed=COALESCE(@clientContractSigned,contract_signed),
                    billing_address=COALESCE(@clientBillingAddress,billing_address)
                    where client_id = @clientId
                    select client_id,client_name,legal_business_name,gst_number,contract_signed,billing_address from t_client where client_id = @clientId or client_name = @clientName
                END
                ELSE
                BEGIN
                    insert into t_client(client_name,legal_business_name,gst_number,contract_signed,billing_address)
                    output inserted.client_id,inserted.client_name,inserted.legal_business_name,inserted.gst_number,inserted.contract_signed,inserted.billing_address
                    values(@clientName, @clientLegalBusinessName, @clientGSTNumber, @clientContractSigned, @clientBillingAddress)
                END");

            cmd.Parameters.Add(new SqlParameter("@clientId", client.clientId.ToSafeDbObject()));
            cmd.Parameters.Add(new SqlParameter("@clientName", client.clientName));
            cmd.Parameters.Add(new SqlParameter("@clientLegalBusinessName", client.clientLegalBusinessName == "" || client.clientLegalBusinessName == null ? "" : client.clientLegalBusinessName));
            cmd.Parameters.Add(new SqlParameter("@clientGSTNumber", client.clientGSTNumber == "" || client.clientGSTNumber == null ? "" : client.clientGSTNumber));
            cmd.Parameters.Add(new SqlParameter("@clientContractSigned", client.clientContractSigned));
            cmd.Parameters.Add(new SqlParameter("@clientBillingAddress", client.clientBillingAddress == "" || client.clientBillingAddress == null ? "" : client.clientBillingAddress));
            res = ResponseType.READER;
            return cmd;
        }), shardId,
        new ResponseHandler<I2E1Client>((reader) =>
        {
            var clients_list = new I2E1Client();

            while (reader.Read())
            {
                clients_list.clientId = new LongIdInfo(shardId, DBObjectType.USER_TYPE, reader["client_id"]);
                clients_list.clientName = reader["client_name"].ToString();
                clients_list.clientLegalBusinessName = reader["legal_business_name"].ToString();
                clients_list.clientGSTNumber = reader["gst_number"].ToString();
                clients_list.clientContractSigned = (reader["contract_signed"] != DBNull.Value ? Convert.ToInt32(reader["contract_signed"]) : 0);
                clients_list.clientBillingAddress = reader["billing_address"].ToString();
            }
            return clients_list;
        })).Execute();
    }

    public static string MapNasidToAdmin(LongIdInfo longAdminId, LongIdInfo longNasId)
    {
        return new ShardQueryExecutor<string>(new GetSqlCommand((out ResponseType res) =>
        {
            SqlCommand cmd = new SqlCommand(@"IF NOT EXISTS (select 1 from t_admin_mapping where admin_id=@adminId and mapped_id=@nasid and mapping_type = 'location')
                BEGIN
                    insert into t_admin_mapping(admin_id,mapped_id,mapping_type) values(@adminId, @nasid, 'location');
                END");
            cmd.Parameters.Add(new SqlParameter("@adminid", longAdminId.local_value));
            cmd.Parameters.Add(new SqlParameter("@nasid", longNasId.local_value));
            res = ResponseType.READER;
            return cmd;
        }), longAdminId.shard_id,
        new ResponseHandler<string>((reader) =>
        {
            if (reader.Read())
            {

            }
            return null;
        })).Execute();
    }
}