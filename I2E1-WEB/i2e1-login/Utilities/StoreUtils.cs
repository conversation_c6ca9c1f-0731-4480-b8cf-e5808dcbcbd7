using i2e1_basics.Database;
using i2e1_basics.Utilities;
using System.Data.SqlClient;

namespace I2E1_WEB.Utils;

public class StoreUtils
{
    public static bool UpdateStore(LongIdInfo longNasId, string shopName, string address, string pincode, string city, string state, int partnerId, string userEmail = "", string mobile="")
    {
        return new ShardQueryExecutor<bool>(new GetSqlCommand((out ResponseType res) =>
        {
            SqlCommand cmd = new SqlCommand(@"
                    IF EXISTS(select 1 from t_store where router_nas_id = @nasid)
                    begin
                    update t_store set 
                    shop_name=@name, 
                    brand_name=@name,
                    shop_address=@address, 
                    shop_city=@city,
                    shop_state=@state,
                    install_state=1,
                    loc_start_date = GETUTCDATE(),
                    partner_id = @partner_id,
                    partner_cd = @partner_cd,
                    install_id = @user_email,
                    memail = @user_email,
                    mmobile = @user_mobile,
                    pincode = @pincode
                    where router_nas_id=@nasid
                    END
                    ELSE
                    BEGIN
                        INSERT INTO t_store(router_nas_id, shop_name, brand_name, shop_address, shop_city, shop_state, install_state, loc_start_date, partner_id, partner_cd, install_id, memail,
                        mmobile, pincode)
                        VALUES(@nasid, @name, @name, @address, @city, @state, 1, GETUTCDATE(), @partner_id, @partner_cd, @user_email, @user_email,
                        @user_mobile, @pincode)
                    END");
            cmd.Parameters.Add(new SqlParameter("@nasid", longNasId.local_value));
            cmd.Parameters.Add(new SqlParameter("@name", shopName));
            cmd.Parameters.Add(new SqlParameter("@address", address));
            cmd.Parameters.Add(new SqlParameter("@city", city));
            cmd.Parameters.Add(new SqlParameter("@state", state));
            cmd.Parameters.Add(new SqlParameter("@partner_id", partnerId));
            cmd.Parameters.Add(new SqlParameter("@partner_cd", partnerId.ToString()));
            cmd.Parameters.Add(new SqlParameter("@user_email", userEmail));
            cmd.Parameters.Add(new SqlParameter("@user_mobile", mobile));
            cmd.Parameters.Add(new SqlParameter("@pincode", pincode));
            res = ResponseType.NONQUERY;
            return cmd;
        }), longNasId.shard_id,
       new ResponseHandler<bool>((reader) =>
       {
           return true;
       })).Execute();
    }

    /*public static JsonResponse getStoreContactPersons(int nasid)
    {
        return new QueryExecutor<JsonResponse>(new GetSqlCommand((out ResponseType res) =>
        {
            SqlCommand cmd = new SqlCommand(@"select * from t_admin_mapping tam
                                            left join t_admin ta on ta.user_id=tam.admin_id
                                            where tam.mapping_type = 'location' and tam.mapped_id=@nasId
                                            and ta.user_type in (100,101,0)");

            cmd.Parameters.Add(new SqlParameter("@nasId", nasid));
            res = ResponseType.READER;
            return cmd;
        }), I2e1ConfigurationManager.GetInstance().GetSetting("DatabaseConnectionString"),
        new ResponseHandler<JsonResponse>((reader) =>
        {
            var contacts_list = new List<Hashtable>();

            while (reader.Read())
            {
                Hashtable contact = new Hashtable();
                contact.Add("userId", (int)reader["admin_id"]);
                contact.Add("username", reader["username"].ToString());
                contact.Add("name", reader["name"].ToString());
                contact.Add("userType", reader["user_type"] != null ? Convert.ToInt32(reader["user_type"]) : 0);
                contact.Add("mobile", reader["contact_no"].ToString());
                contact.Add("active", reader["active"] != DBNull.Value ? Convert.ToInt32(reader["active"]) : 0);
                contact.Add("isGreeted", reader["is_greeted"] != DBNull.Value ? Convert.ToInt32(reader["is_greeted"]) : 0);
                contacts_list.Add(contact);
            }
            return new JsonResponse(ResponseStatus.SUCCESS, "", contacts_list);
        })).Execute();
    }*/
}
