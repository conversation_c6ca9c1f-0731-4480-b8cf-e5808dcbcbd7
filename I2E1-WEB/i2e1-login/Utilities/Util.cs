using Amazon.Lambda;
using Amazon.Lambda.Model;
using Amazon.S3;
using i2e1_core.Utilities;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Security.Cryptography;
using System.Text;
using System.Threading.Tasks;
using System.Web;

namespace I2E1_Message.Utils
{
    public class Util
    {
        public static string Version = (new Random().Next(10000, 100000)).ToString();

        public static string GetUrlWithVersion(string url)
        {
            return url + "?ver=" + Version;
        }

        public static DateTime ConvertUtcToIST(DateTime time)
        {
            return time.Add(new TimeSpan(5, 30, 0));
        }

        public static DateTime ConvertISTToUtc(DateTime time)
        {
            return time.Subtract(new TimeSpan(5, 30, 0));
        }

        public static async Task<string> GenerateShortUrl(string url, string hostName = "offr.ly")
        {
            try
            {
                Dictionary<string, object> parameters = new Dictionary<string, object>();
                parameters.Add("url", url);
                parameters.Add("hostname", hostName);
                var res = await CoreUtil.ExecuteLambdaFunction("shorty-generator", "ap-south-1", parameters);
                return res["url"].ToString();
            }
            catch (Exception ex)
            {
                throw new Exception("Offr.ly didn't reply with a shorturl " + ex.Message);
            }
        }

        public static async Task<string> GetLinqShortUrl(string mobile, string name, string ssid, string campaign, string source, string medium, string defaultApi = "offr.ly")
        {
            using (var client = new WebClient())
            {
                client.Headers[HttpRequestHeader.ContentType] = "application/json";
                string deepLink = string.Format("http://getlinq.in?mobile={0}&name={1}&ssid={2}", mobile, HttpUtility.UrlEncode(name), ssid);
                source = HttpUtility.UrlEncode(source);
                medium = HttpUtility.UrlEncode(medium);
                campaign = HttpUtility.UrlEncode(campaign);
                var data = new
                {
                    longDynamicLink = string.Format("https://hg7q2.app.goo.gl/?link={0}&apn=com.i2e1.swapp&utm_source={1}&utm_medium={2}&utm_campaign={3}&afl={4}", 
                    HttpUtility.UrlEncode(deepLink), source , medium, campaign,
                    HttpUtility.UrlEncode("https://play.google.com/store/apps/details?id=com.i2e1.swapp&referrer=" + HttpUtility.UrlEncode("utm_source="+ source +"&utm_medium="+ medium +"&utm_campaign=" + campaign))),
                    suffix = new { option = "SHORT" }
                };
                if(medium == "Browser")
                {
                    return data.longDynamicLink;
                }
                return await GenerateShortUrl(data.longDynamicLink, defaultApi);
            }
        }

        public static string UrlEncode(string url)
        {
           return WebUtility.UrlEncode(url);
        }

        public static IAmazonS3 s3Client;

        public static string GenerateSHA256(string data)
        {
            using (var hash = SHA256.Create())
            {
                Encoding enc = Encoding.UTF8;
                return String.Concat(hash.ComputeHash(Encoding.UTF8.GetBytes(data)).Select(item => item.ToString("x2")));
            }
            return string.Empty;
        }

        public static byte[] MD5Hash(string input)
        {
            StringBuilder hash = new StringBuilder();
            MD5CryptoServiceProvider md5provider = new MD5CryptoServiceProvider();
            byte[] bytes = md5provider.ComputeHash(new UTF8Encoding().GetBytes(input));

            for (int i = 0; i < bytes.Length; i++)
            {
                hash.Append(bytes[i].ToString("x2"));
            }
            return bytes;
        }

        public static string GeneratePlanString(long data_limit, long time_limit)
        {
            string tU = "Day", dU = "GB";
            int tD = Constants.SECONDS_IN_DAY;
            long dD = Constants.ONE_MB;
            if (data_limit < 1024 && data_limit >= 1)
            {
                dU = "MB";
                dD = 1;
            }
            else if (data_limit >= 1024)
            {
                dU = "GB";
                dD = Constants.ONE_KB;
            }

            if(time_limit >= Constants.SECONDS_IN_DAY)
            {
                tU = "Day";
                tD = Constants.SECONDS_IN_DAY;
            }
            else if (time_limit < Constants.SECONDS_IN_DAY && time_limit >= 3600)
            {
                tU = "Hour";
                tD = 3600;

            }
            else if (time_limit < 3600)
            {
                tU = "Min";
                tD = 60;
            }
            if(data_limit == 0)
                return $"Unlimited/{time_limit / tD}{tU}";

            return $"{data_limit/dD}{dU}/{time_limit/tD}{tU}";
        }
    }

   

    public static class IListExtensions
    {
        private static readonly Random random;
        static IListExtensions()
        {
            random = new Random();
        }

        public static void Shuffle<T>(this IList<T> list)
        {
            if (list == null)
            {
                throw new ArgumentNullException("List", "Is Null.");
            }
            for (var i = list.Count; i > 1; i--)
            {
                var r = random.Next(i--);
                var t = list[r];
                list[r] = list[i];
                list[i] = t;
            }
        }
    }

    public class MyWebClient : WebClient
    {
        Uri _responseUri;

        public Uri ResponseUri
        {
            get { return _responseUri; }
        }

        protected override WebResponse GetWebResponse(WebRequest request)
        {
            WebResponse response = null;
            try
            {
                HttpWebRequest request1 = (HttpWebRequest)request;
                request1.AllowAutoRedirect = false;
                response = base.GetWebResponse(request1);
                _responseUri = response.ResponseUri;
            }
            catch
            {
            }
            return response;
        }
    }
}