using i2e1_basics.Utilities;
using i2e1_core.Models;
using i2e1_core.Models.Client;
using i2e1_core.Models.SWAP;
using i2e1_core.Utilities;
using I2E1_Message.Models;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using StackExchange.Redis;
using System;
using System.IO;
using System.Net;
using System.Text;
using System.Web;

namespace I2E1_Message.Utils
{
    public class CacheHelper : CoreCacheHelper
    {
        public const string FB_REPORT = "FB_REPORT";

        private static CacheHelper cacheHelper = null;

        protected CacheHelper(string cacheConnectionString, int cachedbId):base(cacheConnectionString, cachedbId)
        {
            ConnectionMultiplexer connection = ConnectionMultiplexer.Connect(cacheConnectionString);
        }

        public static CacheHelper CreateInstance(string cacheConnectionString, int cachedbId)
        {
            if (cacheHelper == null)
            {
                cacheHelper = new CacheHelper(cacheConnectionString, cachedbId);
            }
            return cacheHelper;
        }

        public static CacheHelper GetInstance()
        {
            return cacheHelper;
        }

        public Template GetTemplateContent(int templateId, LongIdInfo nasid = null)
        {
            var response = getValueFromCache(TEMPLATE_CONTENT, templateId, () =>
            {
                return DbCalls.GetInstance().GetTemplate(templateId);
            });

            if (response != null && !string.IsNullOrEmpty(response.templatePath))
            {
                var path = AppDomain.CurrentDomain.BaseDirectory + "CustomTemplates";
                if (!Directory.Exists(path))
                    Directory.CreateDirectory(path);

                var file = path + "/" + response.id + ".html";
                string content = string.Empty;

                if (!File.Exists(file))
                {
                    try
                    {
                        string filePath = response.isFullOverriden ? response.templatePath + ".html" : response.templatePath;

                        var temp = new WebClient().DownloadData(filePath);
                        content = Encoding.UTF8.GetString(temp);
                        int index = 0;

                        while ((index = content.IndexOf("blob.core.windows.net", index + 50)) != -1)
                        {
                            int startIndex = content.LastIndexOf("http", index);
                            int lineEnd = content.IndexOf("\n", index);
                            int endIndex = content.IndexOf(".png", index);
                            if (endIndex == -1 || endIndex > lineEnd)
                            {
                                endIndex = content.IndexOf(".jpg", index);
                            }

                            if (endIndex != -1 && endIndex <= lineEnd)
                            {
                                var newContent = content.Substring(0, startIndex);
                                newContent = newContent + "/Proxy/GetContent.ashx?url="
                                    + HttpUtility.UrlEncode(content.Substring(startIndex, endIndex - startIndex + 4).Replace("https", "http"))
                                    + content.Substring(endIndex + 4);
                                content = newContent;
                            }

                            File.WriteAllText(file, content);
                        }

                        File.WriteAllText(file, content);
                    }
                    catch
                    {
                    }
                }
                else
                {
                    content = File.ReadAllText(file);
                }
                response.templateContent = content;
            }

            if (nasid != null && nasid.local_value == 666)
            {
                if (!string.IsNullOrEmpty(response.templatePath))
                {
                    try
                    {
                        using (WebClient client = new WebClient())
                        {
                            response.templateContent = client.DownloadString(response.templatePath);
                        }
                    }
                    catch
                    {
                        response.templateContent = string.Empty;
                    }
                }
            }
            return response;
        }

        public string GetQuestionConfig(int combinedSettingId)
        {
            AdvanceConfig config = CoreCacheHelper.GetInstance().GetAdvanceCongifInCombinedSetting(combinedSettingId, AdvanceConfigType.HIDE_QUESTION);
            if (config != null && config.parameters != null && config.parameters.Length > 0)
            {
                if (!string.IsNullOrEmpty(config.parameters[0]))
                    return config.parameters[0];
            }
            return null;
        }

        public GlobalOtp GetGlobalOTPValue(int combinedSettingId)
        {
            GlobalOtp gOtp = null;
            AdvanceConfig config = CoreCacheHelper.GetInstance().GetAdvanceCongifInCombinedSetting(combinedSettingId, AdvanceConfigType.GLOBAL_OTP);
            if (config != null && !string.IsNullOrEmpty(config.value))
            {
                try
                {
                    gOtp = JsonConvert.DeserializeObject<GlobalOtp>(config.value);
                }
                catch
                {
                }
            }

            if (gOtp == null)
            {
                gOtp = new GlobalOtp();
                gOtp.enable = false;
            }
            return gOtp;
        }

        public void SetDataForTrai(string key, string id, JObject data)
        {
            setCache(key + id, data);
        }

        public JObject GetTRAIDATA(string id, JObject data)
        {
            return getValueFromCache(id, "", () => data);
        }


        public FinalObject GetCompleteTRAIXML(string id)
        {
            return getValueFromCache(id, 0, () => TRAIUtils.setXmlDataInCache());
        }
        public LandingPage getLandingPageConfiguration(LongIdInfo nasid, string mobile)
        {
            AdvanceConfig config = CoreCacheHelper.GetInstance().GetAdvanceCongifOnNas(nasid, AdvanceConfigType.LANDING_PAGE_CONFIG);
            LandingPage landingPage = new LandingPage();
            if (config != null && config.parameters != null && config.parameters.Length > 0 && !string.IsNullOrEmpty(config.parameters[0]))
            {
                landingPage.pageTitle = config.parameters[0];
            }
            else
            {
                landingPage.pageTitle = "You are now connnected";
            }
            landingPage.icons = DbCalls.GetInstance().GetIcons(nasid, mobile);
            return landingPage;
        }

        public LocationDetails GetWeatherDetailsForLocationId(string locationid, string locationname, string url)
        {
            var result = getValueFromCache(LOCATION_WEATHER_DETAILS, locationid, () =>
            {

                dynamic obj = new JObject();

                try
                {
                    string json = new WebClient().DownloadString(url);
                    obj = JsonConvert.DeserializeObject<JObject[]>(json)[0];
                }
                catch (Exception e)
                {
                    return null;

                }

                LocationDetails details = new LocationDetails();
                details.LocationName = locationname;
                details.WeatherType = obj["WeatherText"].ToString();
                details.WeatherIcon = obj["WeatherIcon"].ToString();
                string baseUri = "https://i2e1-linq.s3.ap-south-1.amazonaws.com/weather-icons/" + details.WeatherIcon;
                details.hdpi = baseUri + "/drawable-hdpi/" + details.WeatherIcon + ".png";
                details.ldpi = baseUri + "/drawable-ldpi/" + details.WeatherIcon + ".png";
                details.mdpi = baseUri + "/drawable-mdpi/" + details.WeatherIcon + ".png";
                details.xhdpi = baseUri + "/drawable-xhdpi/" + details.WeatherIcon + ".png";
                details.xxhdpi = baseUri + "/drawable-xxhdpi/" + details.WeatherIcon + ".png";
                details.xxxhdpi = baseUri + "/drawable-xxxhdpi/" + details.WeatherIcon + ".png";
                details.Temprature = obj["Temperature"]["Metric"]["Value"].ToString() + "\u00B0C";
                return details;
            });

            return result;
        }

        public JObject GetLocationIdForLatLong(string key, string url)
        {
            var result = getValueFromCache(LOCATION_ID_DETAILS, key, () =>
            {

                dynamic obj = new JObject();

                try
                {
                    string json = new WebClient().DownloadString(url);
                    obj = JsonConvert.DeserializeObject<JObject>(json);
                    obj["Error"] = false;
                }
                catch (Exception e)
                {
                    obj["Error"] = true;
                }

                return obj;

            });

            return result;
        }

        //public Dictionary<string, long> GetFacebookAggregateReport(int clientId)
        //{
        //    return getValueFromCache(FB_REPORT, clientId, () =>
        //    {
        //        var dict = new Dictionary<string, long>();
        //        var locations = DbCalls.GetInstance().GetLocationData(clientId);
        //        dict.Add("total_hotspots", locations.Count);
        //        DbCalls.GetInstance().GetFacebookPeriodAggregateReport(dict, 30, locations);
        //        DbCalls.GetInstance().GetFacebookPeriodAggregateReport(dict, 1, locations);
        //        return dict;
        //    });
        //}

        public String GetDIYToken(LongIdInfo longNasId, string mobile)
        {
            return GetObjectIfExists<String>(string.Format("{0}_{1}_{2}", DIY_REG_OTP, longNasId, mobile));
        }

        public void SetDIYToken(long nasid, string mobile, string token)
        {
            setCache(string.Format("{0}_{1}_{2}0", DIY_REG_OTP, nasid, mobile), token, TimeSpan.FromHours(1));
        }       
    }
}