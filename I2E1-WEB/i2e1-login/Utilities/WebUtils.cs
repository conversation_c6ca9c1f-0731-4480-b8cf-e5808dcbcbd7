using i2e1_basics.Utilities;
using i2e1_core.Models;
using i2e1_core.Utilities;
using I2E1_WEB.Database;
using I2E1_WEB.Validators;
using Microsoft.AspNetCore.Http;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using wiom_login_share.Models;
using wiom_login_share.Utilities;
using AuthType = wiom_login_share.Models.AuthType;

namespace I2E1_WEB.Utilities;

public class WebUtils
{
    public static string GetCurrentHost(HttpContext httpContext)
    {
        return "https://" + httpContext.Request.Host.Host;
    }

    public static string GetHomeRouterBuild(string state, User user, string query="")
    {
        return $"/redirect/{state}?login-user-session={user.GetToken()}&nasid={user.nasid}&{query}";
    }
    public static string GetWiomNetServerUrl()
    {
        string baseUrl = "https://localhost:44300/WiomNet";
        if (I2e1ConfigurationManager.DEPLOYED_ON == "prod")
        {
            baseUrl = "https://www.i2e1.in/WiomNet";
        }
        else if (!String.IsNullOrEmpty(I2e1ConfigurationManager.DEPLOYED_ON))
        {
            baseUrl = $"https://{I2e1ConfigurationManager.DEPLOYED_ON}.i2e1.in/WiomNet";
        }
        return baseUrl;
    }
    public static string GetWiomNetBuild(string state, User user, string query = "")
	{
		return $"https://{(I2e1ConfigurationManager.DEPLOYED_ON == "prod" ? "www.i2e1.in" : $"{I2e1ConfigurationManager.DEPLOYED_ON}.i2e1.in")}/WiomNet/wiom-front/{state}?login-user-session={user.GetToken()}&nasid={user.nasid}&{query}";
	}
	public static string GetPaymentDeepLink(string mobile, long planId = 0, string deviceId = "", string uniqueIdentifier = "", LongIdInfo nasid = null, bool wifi = false, string orderId = "", string uniqueplankey = "", string dynamicintentlink = "")
	{
		return $"http://{(I2e1ConfigurationManager.DEPLOYED_ON == "prod" ? Constants.DOMAIN_FOR_NET_CHECK : $"{I2e1ConfigurationManager.DEPLOYED_ON}.pmwifi.in")}/WiomNet/Login/RedirectToPaymentPageView?mobile={mobile}&planId={planId}&deviceId={deviceId}&nasid={nasid}&uniqueIdentifier={uniqueIdentifier}&wifi={wifi}&orderId={orderId}&newFlow={true}&uniqueplankey={uniqueplankey}&dynamicintentlink={dynamicintentlink}";
	}
    public static string GetPaymentLink(long planId, LongIdInfo nasid, string mobile, string macId, string deviceId, string language = "hi", string distinctId = "", bool isAuthenticated = false, bool payOnline = true, int storegroupid = 1, string uniqueIdentifier = "ss_unique", bool wifi = false)
    {
        return $"http://{(I2e1ConfigurationManager.DEPLOYED_ON == "prod" ? Constants.DOMAIN_FOR_NET_CHECK : $"{I2e1ConfigurationManager.DEPLOYED_ON}.pmwifi.in")}/WiomNet/Login/InitPayment?planId={planId}&deviceId={deviceId}&nasid={nasid}&mobile={mobile}&lang={language}&id={distinctId}&isAuthenticated={isAuthenticated}&payOnline={payOnline}&storegroupid={storegroupid}&mac={macId}&uniqueIdentifier={uniqueIdentifier}&wifi={wifi}";
    }

    public static List<object> GetRegisteredUsers(HttpContext httpContext)
    {
        var pgUser = GetPasswordGenerator(httpContext);
        if (pgUser == null)
            return null;
        else
        {
                var users = AdminDatabaseRequest.GetAllRegisteredHomeUsers(pgUser.nasid, DateTime.UtcNow.AddDays(-30), DateTime.UtcNow);
                return users.Cast<object>().ToList();
        }
        return null;
    }
    public static PasswordGeneratorUser GetPasswordGenerator(HttpContext httpContext)
    {
        return CoreSessionUtils.GetValueFromToken<PasswordGeneratorUser>(httpContext, "pg-admin-token", "PG_ADMIN");
    }

    public static void SetPasswordGenerator(PasswordGeneratorUser value, HttpContext httpContext)
    {
        CoreSessionUtils.SetValueAndToken(httpContext, value.nasid.shard_id, "pg-admin-token", "PG_ADMIN", value);
    }
    public static void LogoutFDMUser(string mobile, LongIdInfo nasid, int storeGroupId)
    {
        AdminDatabaseRequest.LogoutRegisteredUser(nasid, storeGroupId, mobile);
        CoreCacheHelper.GetInstance().DeleteUserSession(new User { mobile = mobile, nasid = nasid.ToString(), storegroupid = storeGroupId });
    }

    public static BasicValidator GetValidatorFromTemplateId(User user, BaseController controller,HttpContext httpContext)
    {
        List<int> templateIds = user.templateid;
        if (templateIds.Count == 0)
            return new BasicValidator(controller, httpContext);

        switch (templateIds[0])
        {
            case 20:
                return new AirtelValidatior(controller, httpContext);
            case 10:
                return new VodafoneValidatior(controller, httpContext);
            case ClientId.MY_GOV:
                return new MyGovAuthValidator(controller, httpContext);
            case 94:
                return new BataValidatior(controller, httpContext);
            case 199:
                //put case 180 for test database
                return new UncubeValidator(controller, httpContext);
            case 174:
                return new GlamStudioValidator(controller, httpContext);
            case 137:
            case 138:
            case 139:
                return new ImagineStoreValidator(controller, httpContext);
            case 370:
                return new KacificValidator(controller, httpContext);
            case 333:
                return new B2CCValidator(controller, httpContext);
            default:
                AuthType authType = user.clientAuthType == null ? AuthType.PHONE : user.clientAuthType.Value;
                if (authType == AuthType.DATA_VOUCHER)
                    return new DataVoucherExtendedValidation(controller, httpContext);
                else if (authType == AuthType.DATA_VOUCHER_WITHOUT_OTP)
                    return new DataVoucherValidator(controller, httpContext);
                return new BasicValidator(controller, httpContext);
        }
    }

    public static void LogErrorToCosmos(string message, object webData)
    {
        Logger.GetInstance().Error(message + " " + JsonConvert.SerializeObject(webData));
    }

    public static void LogInfoToCosmos(string message, object webData)
    {
        Logger.GetInstance().Info(message + " " + JsonConvert.SerializeObject(webData));
    }

    public static void LogInfoToCosmos(string message)
    {
        Logger.GetInstance().Info(message);
    }

    public static void LogErrorToCosmos(string message)
    {
        Logger.GetInstance().Error(message );
    }
}