using i2e1_basics.Models;
using i2e1_basics.Utilities;
using i2e1_core.Utilities;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using System;
using wiom_login_share.Models;
using wiom_login_share.Utilities;

namespace i2e1_login.Utilities
{
	public class WiomNetPrePlan
	{
		public static bool SetPrePlanUniqueKey(string mobile, long nasId, long planId, HttpContext httpContext, out string response)
		{
			string uniqueplankey = string.Empty;
			response = string.Empty;
			Logger.GetInstance().Info($"WiomNetPrePlan : SetPrePlanUniqueKey:, mobile: {mobile}, nasId: {nasId}, planId: {planId}");
			try
			{

				int storeGroupId = 1;
				var user = new User()
				{
					mobile = mobile,
					nasid = nasId.ToString(),
					storegroupid = storeGroupId
				};
				var freePlans = DbCalls.GetInstance().GetFreePlansCreated(user, DateTime.UtcNow.Date, "PAY_ONLINE");
				if (freePlans.Count >= 3)
				{
					Logger.GetInstance().Info($"WiomNetPrePlan : SetPrePlanUniqueKey: freecount: {freePlans.Count}");
					response = "Please relogin or proceed with cash payment";
					return false;
				}
				else
				{
					uniqueplankey = $"{mobile}.{DateTime.UtcNow.AddMinutes(10).ToString("yyyy-MM-ddTHH-mm")}";
					CookieUtils.SetCookie(httpContext, "uniqueplankey", uniqueplankey, true, DateTime.UtcNow.AddMinutes(1));
					response = uniqueplankey;
				}
			}
			catch (Exception ex)
			{
				Logger.GetInstance().Error($"WiomNetPrePlan : SetPrePlanUniqueKey with exception: {ex}");
				return false;
			}
			return true;
		}
	}
}
