using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace I2E1_Message
{
    public class Reports
    {
        public string month { get; set; }

        public string nasid { get; set; }

        public string venue { get; set; }

        public string email { get; set; }

        public string users { get; set; }

        public string logins { get; set; }

        public string dataused { get; set; }

        public string newusers { get; set; }

        public string newuserspercent { get; set; }

        public string totalduration { get; set; }

        public string avgduration { get; set; }

        public string repeatpercent { get; set; }

        public string mostpopday { get; set; }

        public string weekendlogins { get; set; }

        public string weekdaylogins { get; set; }

        public string ratio { get; set; }

        public string comment1 { get; set; }

        public string comment2 { get; set; }

        public string feature1 { get; set; }

        public string feature2 { get; set; }

        public string mob1 { get; set; }

        public string mob2 { get; set; }

        public string mob3 { get; set; }

        public string mob1percent { get; set; }

        public string mob2percent { get; set; }

        public string mob3percent { get; set; }


    }
}
