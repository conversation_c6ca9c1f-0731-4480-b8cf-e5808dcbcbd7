using i2e1_basics.Utilities;
using i2e1_core.Utilities;
using Microsoft.AspNetCore.Http;
using System;
using System.Collections.Generic;
using System.Text;

namespace I2E1_Message.Utils
{
    public class RequestUtils
    {
        public static void GetForwardedHeader(HttpContext context)
        {
            try
            {
                Logger.GetInstance().Info("========Request Headers========");
                foreach (var item in context.Request.Headers)
                {
                    Logger.GetInstance().Info(string.Format("{0} --> {1}", item.Key, item.Value));
                }

                Logger.GetInstance().Info("========Context Items========");
                foreach (var item in context.Items)
                {
                    Logger.GetInstance().Info(string.Format("{0} --> {1}", item.Key, item.Value));
                }
            }
            catch (Exception ex)
            {
                Logger.GetInstance().Error(ex.Message);
            }
        }

        public static string getHeader<PERSON>y<PERSON>ey(HttpContext context, string key)
        {
            return context.Request.Headers[key];
        }

        public static string getQueryParamByKey(HttpContext context, string key)
        {
            return context.Request.Query[key];
        }

        public static string getCompleteUrl(HttpContext context)
        {
            return context.Request.Host + context.Request.Path + context.Request.QueryString;
        }

        public static string getAbsoluteUrl(HttpContext context)
        {
            return context.Request.Host + context.Request.Path;
        }

        public static string getHostForQueries(HttpContext context)
        {
            string host = context.Request.Host.ToString();

            return host;
        }

        public static object getContextItem(HttpContext context, string key)
        {
            return context.Items[key];
        }

        public static string getUserAgent(HttpContext context)
        {
            return context.Request.Headers["User-Agent"];
        }

        public static string GetIPAddress(HttpContext context)
        {
            try
            {
                return context.Connection.RemoteIpAddress.Address.ToString();
            }
            catch (Exception ex)
            {
                return "*********";
            }

        }

        public static string GetClientIPAddress(HttpContext context)
        {
            try
            {
                return context.Connection.RemoteIpAddress.ToString();
            }
            catch
            {
                return "*********";
            }

        }
    }
}
