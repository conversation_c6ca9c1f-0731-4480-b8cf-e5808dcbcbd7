using i2e1_basics.Utilities;
using i2e1_core.Models;
using i2e1_core.Models.Client;
using i2e1_core.Utilities;
using I2E1_WEB.Database;
using Microsoft.Extensions.Caching.Memory;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;

namespace i2e1_login.Utilities;

public class MemoryCacheUtil
{
    public static List<StoreInfo> GetLocationsForAdmin(IMemoryCache memoryCache, LongIdInfo longAdminId, StoreSearchQuery query, string source, AdminUserType adminUserType)
    {
        try
        {
            bool search = false;
            if (!string.IsNullOrEmpty(query.storeName) || query.nasid != null)
                search = true;

            string[] key;
            if (source == "wiom")
            {
                key = new string[] { "wiom", longAdminId.ToString(), JsonConvert.SerializeObject(query) };
            }
            else
            {
                if (string.IsNullOrEmpty(query.installedState))
                {
                    key = new string[] { "i2e1", longAdminId.ToString(), longAdminId.ToString(), JsonConvert.SerializeObject(query) };
                }
                else
                {
                    key = new string[] { "i2e1", longAdminId.ToString(), adminUserType.ToString(), JsonConvert.SerializeObject(query) };
                }
            }

                if (!search)
                {
                    return MemoryCacheHelper.GetValueFromCache(memoryCache, MemoryCacheHelper.Hash(key), () =>
                    {
                        return AdminDatabaseRequest.GetLocationsForAdmin(new ManagementUser()
                        {
                            userid = longAdminId,
                            userType = adminUserType
                        }, query, source);
                    }, TimeSpan.FromSeconds(60));
                }
                else
                {
                    return AdminDatabaseRequest.GetLocationsForAdmin(new ManagementUser()
                    {
                        userid = longAdminId,
                        userType = adminUserType
                    }, query, source);
                }
            }
            catch (Exception ex)
            {
                throw ex;
            }

    }

    public static DevicesStats GetActiveInactiveDevicesCount(IMemoryCache memoryCache, ManagementUser user, StoreSearchQuery query)
    {
        try
        {
            string[] key = new string[] { "i2e1", user.userid.ToString(), user.userType.ToString(), JsonConvert.SerializeObject(query) };
            return MemoryCacheHelper.GetValueFromCache(memoryCache, MemoryCacheHelper.Hash(key), () =>
            {
                return AdminDatabaseRequest.getActiveInactiveDevicesCount(user, query);
            });
        }
        catch (Exception ex)
        {
            throw ex;
        }
    }
    
}
