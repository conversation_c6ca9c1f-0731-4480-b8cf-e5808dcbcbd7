using i2e1_basics.Database;
using i2e1_basics.Utilities;
using i2e1_core.Models.Client;
using i2e1_core.Utilities;
using System;
using System.Data.SqlClient;

namespace I2E1_WEB.Utils;

public class PartnerUtils
{

    public static I2E1Partner UpdateClientsPartnerDetails(I2E1Partner partner)
    {
        return new ShardQueryExecutor<I2E1Partner>(new GetSqlCommand((out ResponseType res) =>
        {
            DateTime? subscriptionStartDate = null;
            if (partner.subscriptionStartDate == null || partner.subscriptionStartDate == DateTime.MinValue)
            {
                subscriptionStartDate = null;
            }
            else
            {
                subscriptionStartDate = (DateTime)partner.subscriptionStartDate;
            }
            DateTime? subscriptionRenewalDate = null;
            if (partner.subscriptionRenewalDate == null || partner.subscriptionStartDate == DateTime.MinValue)
            {
                subscriptionRenewalDate = null;
            }
            else
            {
                subscriptionRenewalDate = (DateTime)partner.subscriptionRenewalDate;
            }

            SqlCommand cmd = new SqlCommand(@"
                IF EXISTS (select 1 from t_partner where partner_id=@partnerId and client_id = @clientId)
                BEGIN
                    update t_partner  set 
                    partner_name = COALESCE(@partnerName, partner_name),
                    partner_cd = COALESCE(@partnerCd, partner_cd),
                    partner_logo = COALESCE(@partnerLogo, partner_logo),
                    partner_image = COALESCE(@partnerImage, partner_image),
                    category = COALESCE(@category, category),
                    sub_category = COALESCE(@subCategory, sub_category),
                    micro_category = COALESCE(@microCategory, micro_category),
                    account_type = COALESCE(@accountType, account_type),
                    product_type = COALESCE(@productType, product_type),
                    subscription_type = COALESCE(@subscriptionType, subscription_type),
                    subscription_amt = COALESCE(@subscriptionAmt, subscription_amt),
                    subscription_start_date = COALESCE(@subscriptionStartDate, subscription_start_date),
                    subscription_renewal_date = COALESCE(@subscriptionRenewalDate, subscription_renewal_date),
                    discount = COALESCE(@discount, discount),
                    zoho_account_id = COALESCE(@zohoAccountId,zoho_account_id)
                    where partner_id=@partnerId 
                    and client_id = @clientId
                    select partner_id, client_id,partner_name,partner_cd,partner_logo,partner_image,category,sub_category,micro_category,account_type,product_type,subscription_type,subscription_amt,subscription_start_date,subscription_renewal_date,discount,zoho_account_id from t_partner
                    where partner_id=@partnerId and client_id = @clientId
                END
                ELSE
                BEGIN
                    insert into t_partner(client_id,partner_name,partner_cd,partner_logo,partner_image,category,sub_category,micro_category,account_type,product_type,subscription_type,subscription_amt,subscription_start_date,subscription_renewal_date,discount,zoho_account_id)
                    values(@clientId, @partnerName, @partnerCd,@partnerLogo,@partnerImage,@category,@subCategory,@microCategory,@accountType,@productType,@subscriptionType,@subscriptionAmt,@subscriptionStartDate,@subscriptionRenewalDate, @discount, @zohoAccountId)
                    update t_partner set partner_cd= (SELECT MAX(partner_id) FROM t_partner)
                    where partner_id=(SELECT MAX(partner_id) FROM t_partner)
                    select partner_id, client_id,partner_name,partner_cd,partner_logo,partner_image,category,sub_category,micro_category,account_type,product_type,subscription_type,subscription_amt,subscription_start_date,subscription_renewal_date,discount,zoho_account_id
                    from t_partner where partner_id = (SELECT MAX(partner_id) FROM t_partner)
                END");

            cmd.Parameters.Add(new SqlParameter("@partnerId", partner.partnerId.ToSafeDbObject()));
            cmd.Parameters.Add(new SqlParameter("@clientId", partner.clientId.ToSafeDbObject()));
            cmd.Parameters.Add(new SqlParameter("@partnerName", partner.partnerName));
            cmd.Parameters.Add(new SqlParameter("@partnerCd", partner.partnerCd == null ? (object)DBNull.Value : partner.partnerCd));
            cmd.Parameters.Add(new SqlParameter("@partnerLogo", partner.partnerLogo == null ? (object)DBNull.Value : partner.partnerLogo));
            cmd.Parameters.Add(new SqlParameter("@partnerImage", partner.partnerImage == null ? (object)DBNull.Value : partner.partnerImage));
            cmd.Parameters.Add(new SqlParameter("@category", partner.category == null ? (object)DBNull.Value : partner.category));
            cmd.Parameters.Add(new SqlParameter("@subCategory", partner.subCategory == null ? (object)DBNull.Value : partner.subCategory));
            cmd.Parameters.Add(new SqlParameter("@microCategory", partner.microCategory == null ? (object)DBNull.Value : partner.microCategory));
            cmd.Parameters.Add(new SqlParameter("@accountType", partner.accountType == null ? (object)DBNull.Value : partner.accountType));
            cmd.Parameters.Add(new SqlParameter("@productType", partner.productType == null ? (object)DBNull.Value : partner.productType));
            cmd.Parameters.Add(new SqlParameter("@subscriptionType", partner.subscriptionType == null ? (object)DBNull.Value : partner.subscriptionType));
            cmd.Parameters.Add(new SqlParameter("@subscriptionAmt", Convert.ToDecimal(partner.subscriptionAmt)));

            cmd.Parameters.Add(new SqlParameter("@subscriptionStartDate", subscriptionStartDate == null ? (object)DBNull.Value : subscriptionStartDate));
            cmd.Parameters.Add(new SqlParameter("@subscriptionRenewalDate", subscriptionRenewalDate == null ? (object)DBNull.Value : subscriptionRenewalDate));
            cmd.Parameters.Add(new SqlParameter("@discount", Convert.ToDecimal(partner.discount)));
            cmd.Parameters.Add(new SqlParameter("@zohoAccountId", partner.zohoAccountId == "" || partner.zohoAccountId == null ? "" : partner.zohoAccountId));
            res = ResponseType.READER;
            return cmd;
        }), ShardHelper.SHARD0,
        new ResponseHandler<I2E1Partner>((reader) =>
        {
            var ptner = new I2E1Partner();
            while (reader.Read())
            {
                ptner.partnerId = new LongIdInfo(ShardHelper.SHARD0, DBObjectType.ADMIN_TYPE, Convert.ToInt64(reader["partner_id"]));
                ptner.clientId = new LongIdInfo(ShardHelper.SHARD0, DBObjectType.ADMIN_TYPE, Convert.ToInt64(reader["client_id"]));
                ptner.partnerName = reader["partner_name"].ToString();
                ptner.partnerCd = reader["partner_cd"].ToString();
                ptner.partnerLogo = reader["partner_logo"].ToString();
                ptner.partnerImage = reader["partner_image"].ToString();
                ptner.category = reader["category"].ToString();
                ptner.subCategory = reader["sub_category"].ToString();

                ptner.microCategory = reader["micro_category"].ToString();
                ptner.accountType = reader["account_type"].ToString();
                ptner.productType = reader["product_type"].ToString();
                ptner.subscriptionType = reader["subscription_type"].ToString();
                ptner.subscriptionAmt = 0;

                DateTime? subscriptionStartDate;
                if (reader["subscription_start_date"] == DBNull.Value)
                {
                    subscriptionStartDate = null;
                }
                else
                {
                    subscriptionStartDate = (DateTime)reader["subscription_start_date"];
                }

                ptner.subscriptionStartDate = subscriptionStartDate;
                DateTime? subscriptionRenewalDate;
                if (reader["subscription_start_date"] == DBNull.Value)
                {
                    subscriptionRenewalDate = null;
                }
                else
                {
                    subscriptionRenewalDate = (DateTime)reader["subscription_renewal_date"];
                }
                ptner.subscriptionRenewalDate = subscriptionRenewalDate;
                ptner.discount = Convert.ToDecimal(reader["discount"]);
                ptner.zohoAccountId = reader["zoho_account_id"].ToString();
            }
            return ptner;
        })).Execute();
    }
}