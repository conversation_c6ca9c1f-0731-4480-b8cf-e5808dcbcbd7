using Amazon;
using Amazon.S3;
using Amazon.S3.Model;
using i2e1_basics.Database;
using i2e1_basics.DynamoUtilities;
using i2e1_basics.Utilities;
using Microsoft.SqlServer.Management.HadrModel;
using OfficeOpenXml.FormulaParsing.Excel.Functions;
using Org.BouncyCastle.Utilities.Collections;
using System;
using System.Collections.Generic;
using System.Data.SqlClient;
using System.IO;
using System.IO.Compression;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using wifidog_core.Models;
using wiom_routerplan_share.Models.RouterPlan;

namespace i2e1_login.Utilities
{
    public class IPDRValidation
    {
        static readonly Lazy<IPDRValidation> iPDRValidation = new Lazy<IPDRValidation>(() => new IPDRValidation());
        public static IPDRValidation Instance = iPDRValidation.Value;
        public int hour = 0;
        public DateTime date = DateTime.UtcNow.AddDays(-2).Date;
        public void CheckIpdrFlow()
        {
            Dictionary<long, string> nasIdToMacMap = this.GetListOfmacId();
            List<SecondaryRouterPlan> list = this.GetAllWIomNetPlan();
            List<long> nasIds = list.Select((p) => p.nasId.GetLongId()).ToHashSet<long>().ToList();


            HashSet<string> uniqueMacIDs = new HashSet<string>();
            HashSet<long> notExistMacIDs = new HashSet<long>();

            foreach (var nasId in nasIds)
            {
                if (nasIdToMacMap.TryGetValue(nasId, out var mac))
                {
                    uniqueMacIDs.Add(mac.ToLower()); // normalize to lower case
                }
                else
                {
                    notExistMacIDs.Add(nasId);
                }
            }
            try
            {
                HashSet<string> uniqueMacIdFromS3 = this.GetAllMAcIdsFromS3().Result;

                // Normalize MACs from S3
                var normalizedS3Macs = new HashSet<string>(uniqueMacIdFromS3.Select(mac => mac.ToLower()));

                // Now check if all MACs from S3 are in DB
                if (uniqueMacIDs.IsSubsetOf(normalizedS3Macs))
                {
                    Console.WriteLine("✅ All MAC IDs from S3 are present in the DB records.");
                }
                else
                {
                    var missingMacs = uniqueMacIDs.Except(normalizedS3Macs).ToList();
                    Console.WriteLine("❌ Some MAC IDs from S3 are not present in the DB:");
                    foreach (var mac in missingMacs)
                    {
                        Console.WriteLine(mac);
                    }
                }
            }
            catch (Exception ex)
            {
                bool ns = true;
            }

        }


        private List<SecondaryRouterPlan> GetAllWIomNetPlan()
        {
            ModelDynamoDb<SecondaryRouterPlan> modelDynamoDb = new ModelDynamoDb<SecondaryRouterPlan>();
            DynamoQueryBuilder<SecondaryRouterPlan> dynamoQueryBuilder = new DynamoQueryBuilder<SecondaryRouterPlan>();
            dynamoQueryBuilder.AddkeyConditionExpression((p) => p.entryDate, DynamoEnum.DynamoComparisonOperator.Equal, date)
                //.AddkeyConditionExpression((p) => p.createdTime, DynamoEnum.DynamoComparisonOperator.Equal, date)
                .AddFilterCondition((p) => p.otp, DynamoEnum.DynamoComparisonOperator.Equal, HOMEOTP.DONE)
                .AddFilterCondition((p) => p.deviceLimit, DynamoEnum.DynamoComparisonOperator.Equal, 1, DynamoEnum.LogicalOperator.And);
            return modelDynamoDb.GetRecord(dynamoQueryBuilder, "entryDate-createdTime-index", maxRecords: 200000).ToList();
        }

        private Dictionary<long, string> GetListOfmacId()
        {

            return new MasterQueryExecutor<Dictionary<long, string>>(new GetSqlCommand((out ResponseType res) =>
            {
                var cmd = new SqlCommand("set transaction isolation level read uncommitted;SELECT * from t_device where added_time<@date and device_type in (' TP-Link Archer C20 v5', 'SYROTECH-GPON-2000')");

                cmd.Parameters.Add(new SqlParameter("@date", DateTime.UtcNow.Date.AddDays(-3).ToSafeDbObject()));
                res = ResponseType.READER;
                return cmd;
            }), new ResponseHandler<Dictionary<long, string>>((data) =>
            {
                Dictionary<long, string> list = new Dictionary<long, string>();
                while (data.Read())
                {
                    if (data["nasid"] != DBNull.Value)
                    {
                        list.TryAdd(
                        new LongIdInfo(
                            Convert.ToInt64(data["shard_id"]),
                            DBObjectType.SECONDARY_NAS,
                            Convert.ToInt64(data["nasid"])
                        ).GetLongId(),
                        data["mac"]?.ToString()?.ToLower());
                    }
                }
                return list;
            })).Execute();
        }
        async Task<HashSet<string>> GetAllMAcIdsFromS3()
        {
            string bucketName = "wiom-router-logs-prod";
            string prefix = "IPDR/2025/08/03/";
            var region = RegionEndpoint.APSouth1; // Change if your bucket is in a different region

            var s3Client = new AmazonS3Client();
            var request = new ListObjectsV2Request
            {
                BucketName = bucketName,
                Prefix = prefix
            };

            var uniqueFolders = new HashSet<string>();

            ListObjectsV2Response response;
            do
            {
                response = await s3Client.ListObjectsV2Async(request);

                foreach (var obj in response.S3Objects)
                {
                    try
                    {
                        var key = obj.Key;
                        var parts = key.Split('/');
                        if (parts.Length >= 6)
                        {
                            string folderName = parts[5]; // "00-5f-67-36-8e-70"
                            string macFormat = folderName.Replace('-', ':'); // "00:5f:67:36:8e:70"
                            uniqueFolders.Add(macFormat.ToLower()); // optional: normalize to lowercase
                        }

                    }
                    catch (Exception ex)
                    {
                        bool ans = true;
                    }
                }

                request.ContinuationToken = response.NextContinuationToken;

            } while (response.IsTruncated);

            return uniqueFolders;
        }

        public async Task DownloadAllLogsToSingleFile()
        {
            string bucketName = "wiom-router-logs-prod";
            string prefix = "IPDR/2025/08/19/";
            string outputFilePath = "19th_aug_data.txt";
            //string macId = "3c-52-a1-97-e8-39";

            var region = RegionEndpoint.APSouth1;
            var s3Client = new AmazonS3Client(
                "",
                "",
                region
            );

            // Create/overwrite file
            await using var fileStream = new FileStream(outputFilePath, FileMode.Create, FileAccess.Write);

            for (int i = 0; i < 24; i++)
            {
                var request = new ListObjectsV2Request
                {
                    BucketName = bucketName,
                    Prefix = prefix + (i < 10 ? "0" + i : i.ToString()) + "/"
                };

                ListObjectsV2Response response;
                do
                {
                    response = await s3Client.ListObjectsV2Async(request);

                    foreach (var obj in response.S3Objects)
                    {
                        try
                        {
                            var getRequest = new GetObjectRequest
                            {
                                BucketName = bucketName,
                                Key = obj.Key
                            };

                            using var getResponse = await s3Client.GetObjectAsync(getRequest);
                            using var gzipStream = new GZipStream(getResponse.ResponseStream, CompressionMode.Decompress);
                            using var reader = new StreamReader(gzipStream);

                            // Optional: header
                            var header = Encoding.UTF8.GetBytes($"\n--- Start of {obj.Key} ---\n");
                            await fileStream.WriteAsync(header, 0, header.Length);

                            // Read decompressed file line-by-line to avoid memory issues
                            string line;
                            while ((line = await reader.ReadLineAsync()) != null)
                            {
                                var bytes = Encoding.UTF8.GetBytes(line + "\n");
                                await fileStream.WriteAsync(bytes, 0, bytes.Length);
                            }

                            var footer = Encoding.UTF8.GetBytes($"--- End of {obj.Key} ---\n");
                            await fileStream.WriteAsync(footer, 0, footer.Length);
                        }
                        catch (Exception ex)
                        {
                            Console.WriteLine($"Error reading {obj.Key}: {ex.Message}");
                        }
                    }

                    request.ContinuationToken = response.NextContinuationToken;

                } while (response.IsTruncated);
            }

            Console.WriteLine($"All logs saved to {outputFilePath}");
        }

    }
}
