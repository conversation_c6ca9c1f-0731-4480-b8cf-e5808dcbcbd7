using Microsoft.IdentityModel.Tokens;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Globalization;
using System.IdentityModel.Tokens.Jwt;
using System.Linq;
using System.Net;
using System.Security.Cryptography;
using System.Security.Cryptography.X509Certificates;
using System.Web;

namespace I2E1_WEB.Utilities;

public sealed class AttestationStatement
{
    /// <summary>
    /// The original claims dictionary.
    /// </summary>
    public Dictionary<string, string> Claims { get; private set; }

    /// <summary>
    /// The nonce provided when running the attestation on the device.
    /// </summary>
    public byte[] Nonce { get; private set; }

    /// <summary>
    /// The timestamp when the attestation was performed.
    /// </summary>
    public long TimestampMs { get; private set; }

    /// <summary>
    /// The package name of the calling APK.
    /// </summary>
    public string ApkPackageName { get; private set; }

    /// <summary>
    /// The SHA256 digest of the calling APK.
    /// </summary>
    public byte[] ApkDigestSha256 { get; private set; }

    /// <summary>
    /// The SHA256 digest of the signing certificate for the APK.
    /// </summary>
    public byte[] ApkCertificateDigestSha256 { get; private set; }

    /// <summary>
    /// Whether or not the device was determined to have a CTS profile
    /// match.
    /// </summary>
    public bool CtsProfileMatch { get; private set; }

    /// <summary>
    /// Whether or not the device was determined to have basic integrity.
    /// </summary>
    public bool BasicIntegrity { get; private set; }

    /// <summary>
    /// Constructs an Attestation statement from a dictionary of claims.
    /// </summary>
    /// <param name="claims">The claims retrieved from an attestation
    /// statement string.</param>
    public AttestationStatement(Dictionary<string, string> claims)
    {
        Claims = claims;

        if (claims.ContainsKey("nonce"))
        {
            Nonce = Convert.FromBase64String(claims["nonce"]);
        }

        if (claims.ContainsKey("timestampMs"))
        {
            long timestampMsLocal;
            long.TryParse(
                claims["timestampMs"],
                NumberStyles.Integer,
                CultureInfo.InvariantCulture,
                out timestampMsLocal);
            TimestampMs = timestampMsLocal;
        }

        if (claims.ContainsKey("apkPackageName"))
        {
            ApkPackageName = claims["apkPackageName"];
        }

        if (claims.ContainsKey("apkDigestSha256"))
        {
            ApkDigestSha256 = Convert.FromBase64String(
                claims["apkDigestSha256"]);
        }

        if (claims.ContainsKey("apkCertificateDigestSha256"))
        {
            ApkCertificateDigestSha256 = Convert.FromBase64String(
                claims["apkCertificateDigestSha256"]);
        }

        if (claims.ContainsKey("ctsProfileMatch"))
        {
            bool ctsProfileMatchLocal;
            bool.TryParse(
                claims["ctsProfileMatch"],
                out ctsProfileMatchLocal);
            CtsProfileMatch = ctsProfileMatchLocal;
        }

        if (claims.ContainsKey("basicIntegrity"))
        {
            bool basicIntegrityLocal;
            bool.TryParse(
                claims["basicIntegrity"],
                out basicIntegrityLocal);
            BasicIntegrity = basicIntegrityLocal;
        }
    }
}

public static class SafetyNetVerify
{
    /// <summary>
    /// Parses and verifies a SafetyNet attestation statement.
    /// </summary>
    /// <param name="signedAttestationStatement">A string containing the
    /// JWT attestation statement.</param>
    /// <returns>A parsed attestation statement. null if the statement is
    /// invalid.</returns>
    public static AttestationStatement ParseAndVerify(
        string signedAttestationStatement)
    {
        // First parse the token and get the embedded keys.
        JwtSecurityToken token;
        try
        {
            token = new JwtSecurityToken(signedAttestationStatement);
        }
        catch (ArgumentException)
        {
            // The token is not in a valid JWS format.
            return null;
        }

        // We just want to validate the authenticity of the certificate.
        var validationParameters = new TokenValidationParameters
        {
            ValidateIssuer = false,
            ValidateAudience = false,
            ValidateLifetime = false,
            ValidateIssuerSigningKey = true,
            IssuerSigningKeys = GetEmbeddedKeys(token)
        };

        // Perform the validation
        var tokenHandler = new JwtSecurityTokenHandler();
        SecurityToken validatedToken;
        try
        {
            tokenHandler.ValidateToken(
                signedAttestationStatement,
                validationParameters,
                out validatedToken);
        }
        catch (ArgumentException)
        {
            // Signature validation failed.
            return null;
        }

        // Verify the hostname
        if (!(validatedToken.SigningKey is X509SecurityKey))
        {
            // The signing key is invalid.
            return null;
        }
        if (!VerifyHostname(
            "attest.android.com",
            validatedToken.SigningKey as X509SecurityKey))
        {
            // The certificate isn't issued for the hostname
            // attest.android.com.
            return null;
        }

        // Parse and use the data JSON.
        Dictionary<string, string> claimsDictionary = token.Claims
            .ToDictionary(x => x.Type, x => x.Value);

        return new AttestationStatement(claimsDictionary);
    }

    /// <summary>
    /// Verifes an X509Security key, and checks that it is issued for a
    /// given hostname.
    /// </summary>
    /// <param name="hostname">The hostname to check to.</param>
    /// <param name="securityKey">The security key to verify.</param>
    /// <returns>true if securityKey is valid and is issued to the given
    /// hostname.</returns>
    private static bool VerifyHostname(
        string hostname,
        X509SecurityKey securityKey)
    {
        string commonName;
        try
        {
            // Verify the certificate with Verify(). Alternatively, you
            // could use the commented code below instead of Verify(), to
            // get more details of why a particular verification failed.
            //
            // var chain = new X509Chain();
            // var chainBuilt = chain.Build(securityKey.Certificate);
            // if (!chainBuilt)
            // {
            //     string s; // One could use a StringBuilder instead.
            //     foreach (X509ChainStatus chainStatus in
            //         chain.ChainStatus)
            //     {
            //         s += string.Format(
            //             "Chain error: {0} {1}\n",
            //             chainStatus.Status,
            //             chainStatus.StatusInformation);
            //     }
            // }
            if (!securityKey.Certificate.Verify())
            {
                return false;
            }

            commonName = securityKey.Certificate.GetNameInfo(
                X509NameType.DnsName, false);
        }
        catch (CryptographicException)
        {
            return false;
        }
        return (commonName == hostname);
    }

    /// <summary>
    /// Retrieves the X509 security keys embedded in a JwtSecurityToken.
    /// </summary>
    /// <param name="token">The token where the keys are to be retrieved
    /// from.</param>
    /// <returns>The embedded security keys. null if there are no keys in
    /// the security token.</returns>
    /// <exception cref="KeyNotFoundException">Thrown when the JWT data
    /// does not contain a valid signature
    /// header "x5c".</exception>
    /// <exception cref="CryptographicException">Thrwon when the JWT data
    /// does not contain valid signing
    /// keys.</exception>
    private static X509SecurityKey[] GetEmbeddedKeys(
        JwtSecurityToken token)
    {
        // The certificates are embedded in the "x5c" part of the header.
        // We extract them, parse them, and then create X509 keys out of
        // them.
        X509SecurityKey[] keys = null;
        keys = (token.Header["x5c"] as JArray)
            .Values<string>()
            .Select(x => new X509SecurityKey(
                new X509Certificate2(Convert.FromBase64String(x))))
            .ToArray();
        return keys;
    }
}