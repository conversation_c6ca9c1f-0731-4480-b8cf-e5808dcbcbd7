using i2e1_basics.Models;
using i2e1_basics.Database;
using i2e1_basics.Utilities;
using i2e1_core.Models;
using i2e1_core.Models.Client;
using i2e1_core.Models.WIOM;
using i2e1_core.Utilities;
using Microsoft.IdentityModel.Tokens;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Net;
using wifidog_core.Models.WIOM;
using wiom_login_share.Models;

namespace i2e1_core.services
{
    public class CoreUserService
    {
        public static ManagementUser GetAdminUser(LongIdInfo adminUserId)
        {
            return new ShardQueryExecutor<ManagementUser>(new GetSqlCommand((out ResponseType res) =>
            {
                SqlCommand cmd = new SqlCommand(@"SELECT tad.*, tam.mapping_params AS account_params FROM t_admin tad 
                        LEFT JOIN t_account_mapping1 tam on (tad.user_id = tam.mapped_id AND tam.mapping_type = 'user')
                        WHERE tad.user_id = @userid");
                cmd.Parameters.Add(new SqlParameter("@userid", adminUserId.local_value));
                res = ResponseType.READER;
                return cmd;
            }), adminUserId.shard_id,
            new ResponseHandler<ManagementUser>((reader) =>
            {
                ManagementUser user = new ManagementUser();
                if (reader.Read())
                    return CreateManagementUser(adminUserId.shard_id, reader);
                return null;
            })).Execute();
        }


        public static ManagementUser GetAdminUserFromUserId(string username, int ShardId)
        {
            return new ShardQueryExecutor<ManagementUser>(new GetSqlCommand((out ResponseType res) =>
            {
                SqlCommand cmd = new SqlCommand(@"select * from t_admin where username = @username");
                cmd.Parameters.Add(new SqlParameter("@username", username));
                res = ResponseType.READER;
                return cmd;
            }), ShardId,
            new ResponseHandler<ManagementUser>((reader) =>
            {
                ManagementUser user = new ManagementUser();
                if (reader.Read())
                    return CreateManagementUser(ShardId, reader);
                return null;
            })).Execute();
        }


        public static ManagementUser GetAdminUser(long shardId, string adminUsername)
        {
            return new ShardQueryExecutor<ManagementUser>(new GetSqlCommand((out ResponseType res) =>
            {
                SqlCommand cmd = new SqlCommand(@"SELECT tad.*, tam.mapping_params AS account_params FROM t_admin tad 
                        LEFT JOIN t_account_mapping1 tam on (tad.user_id = tam.mapped_id AND tam.mapping_type = 'user')
                        WHERE LTRIM(RTRIM(tad.username)) = @username");
                cmd.Parameters.Add(new SqlParameter("@username", adminUsername.Trim()));
                res = ResponseType.READER;
                return cmd;
            }), shardId, 
            new ResponseHandler<ManagementUser>((reader) =>
            {
                ManagementUser user = new ManagementUser();
                if (reader.Read())
                {
                    return CoreUserService.CreateManagementUser(shardId, reader);
                }
                return null;
            })).Execute();
        }

        //shard entry point need to discuss this
        public static ManagementUser RegisterAdmin(long shardId, ManagementUser user, string username = "email")
        {
            return new ShardQueryExecutor<ManagementUser>(new GetSqlCommand((out ResponseType res) =>
            {
                SqlCommand cmd = new SqlCommand("register_admin_v1");
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.Parameters.Add(new SqlParameter("@password", user.password));
                cmd.Parameters.Add(new SqlParameter("@name", user.name));
                cmd.Parameters.Add(new SqlParameter("@email", user.email));
                cmd.Parameters.Add(new SqlParameter("@contact_no", user.contact_no));
                cmd.Parameters.Add(new SqlParameter("@aadhar", user.aadhar.ToSafeDbObject()));
                if (username == "email")
                {
                    cmd.Parameters.Add(new SqlParameter("@active", user.email_verified ? 1 : 0));
                    cmd.Parameters.Add(new SqlParameter("@username", user.email));
                }
                else
                {
                    cmd.Parameters.Add(new SqlParameter("@username", user.contact_no));
                    cmd.Parameters.Add(new SqlParameter("@active", 1));
                }
                cmd.Parameters.Add(new SqlParameter("@user_type", user.userType));
                cmd.Parameters.Add(new SqlParameter("@auth_type", user.authType));
                res = ResponseType.READER;
                return cmd;
            }), shardId,
            new ResponseHandler<ManagementUser>((reader) =>
            {
                if (reader.Read())
                {
                    user.userid = new LongIdInfo(shardId, DBObjectType.ADMIN_TYPE, Convert.ToInt64(reader["userid"]));
                    user.userType = (AdminUserType)(byte)reader["user_type"];
                    user.token = DBNull.Value == reader["api_token"] ? "" : reader["api_token"].ToString();
                    ShardHelper.SetLongUserIdFromMobile(user.contact_no, InsertNewUser(user.contact_no, Guid.NewGuid().ToString(), user.userid.shard_id));
                    return user;
                }
                return null;
            })).Execute();
        }

        public static ManagementUser GetGoogleUser(string googleToken)
        {
            string data = new WebClient().DownloadString("https://www.googleapis.com/oauth2/v3/tokeninfo?id_token=" + googleToken);
            ManagementUser user = JsonConvert.DeserializeObject<ManagementUser>(data);
            user.username = user.email;
            user.password = string.Empty;
            user.authType = AdminAuthType.GMAIL;
            user.userType = AdminUserType.STANDARD;
            user.contact_no = String.Empty;
            user.email_verified = true;

            return user;
        }

        public static ManagementUser CheckAdmin(string username, string password, string googleToken, AdminAuthType authType = AdminAuthType.CUSTOM)
        {
            ManagementUser user;
            if (!String.IsNullOrEmpty(googleToken))
            {
                user = GetGoogleUser(googleToken);
                var longId = ShardHelper.getLongUserIdFromMobile(user.username);
                return CheckAdmin(longId.shard_id, user, 0);
            }
            else
            {
                user = new ManagementUser()
                {
                    username = username,
                    password = password,
                    authType = authType
                };
                var longId = ShardHelper.getLongUserIdFromMobile(user.username);
                if(longId == null)
                    return null;

                return CheckAdmin(longId.shard_id, user, 1);
            }
        }

        public static ManagementUser CheckAdmin(long shardId, ManagementUser user, int isPasswordMandatory = 1)
        {
            return new ShardQueryExecutor<ManagementUser>(new GetSqlCommand((out ResponseType res) =>
            {
                SqlCommand cmd = new SqlCommand(@"SELECT tad.*, tam.mapping_params AS account_params FROM t_admin tad 
                        LEFT JOIN t_account_mapping1 tam on(tad.user_id = tam.mapped_id AND tam.mapping_type = 'user')
                        WHERE tad.username = @username AND (@passwordCheckMandatory = 0 OR tad.password = @password)");
                cmd.Parameters.Add(new SqlParameter("@username", user.username.ToSafeDbObject()));
                cmd.Parameters.Add(new SqlParameter("@passwordCheckMandatory", isPasswordMandatory));
                cmd.Parameters.Add(new SqlParameter("@auth_type", user.authType));
                cmd.Parameters.Add(new SqlParameter("@password", user.password.ToSafeDbObject()));
                res = ResponseType.READER;
                return cmd;
            }),shardId,
            new ResponseHandler<ManagementUser>((reader) =>
            {
                if (reader.Read())
                {
                    user = CoreUserService.CreateManagementUser(shardId, reader);
                    if (reader["is_password_temporary"] != DBNull.Value)
                        user.is_password_temporary = (bool)reader["is_password_temporary"];
                    return user;
                }
                return null;
            })).Execute();
        }
        public static ManagementUser CreateManagementUser(long shardId, SqlDataReader reader)
        {
            ManagementUser user = new ManagementUser();
            user.userid = new LongIdInfo(shardId, DBObjectType.ADMIN_TYPE, Convert.ToInt64(reader["user_id"])); 
            user.userType = (AdminUserType)(byte)reader["user_type"];
            user.token = reader["api_token"].ToString();
            user.password = reader["password"].ToString();
            user.email = reader["email"].ToString();
            user.contact_no = reader["contact_no"].ToString();
            user.username = reader["username"].ToString();
            user.name = reader["name"].ToString();
            user.active = Convert.ToInt32(reader["active"]);
            user.aadhar = reader["aadhar"] == DBNull.Value ? string.Empty : reader["aadhar"].ToString();
            user.extraData = new JObject();
            try
            {
                user.parentAdminUserId = new LongIdInfo(shardId, DBObjectType.ADMIN_TYPE, Convert.ToInt64(reader["parent_admin"]));
            }
            catch
            {
                user.parentAdminUserId = null;
            }
            if (reader.HasColumn("account_params") && reader["account_params"] != DBNull.Value)
            {
                user.extraData["accountParams"] = JsonConvert.DeserializeObject<JObject>(reader["account_params"].ToString());
            }

            return user;
        }

        public static LongIdInfo InsertNewUser(string mobile, string token, long shard_id)
        {
            Random rnd = new Random();
            return new ShardQueryExecutor<LongIdInfo>(new GetSqlCommand((out ResponseType res) => {

                
                SqlCommand cmd = new SqlCommand(@"
                DECLARE @user_id bigint
                SET @user_id = (select top 1 id from t_user where mobile = @mobile)

                IF @user_id IS NULL
                BEGIN
                INSERT INTO t_user(mobile, token, added_time, modified_time)
                VALUES(@mobile, @token, GETUTCDATE(), GETUTCDATE())
                SET @user_id = CAST(SCOPE_IDENTITY() as bigint)
                END

                SELECT @user_id as user_id");
                cmd.Parameters.Add(new SqlParameter("@mobile", mobile));
                cmd.Parameters.Add(new SqlParameter("@token", token));
                res = ResponseType.READER;
                return cmd;
            }), shard_id,
            new ResponseHandler<LongIdInfo>((reader) => {
                if (reader.Read())
                {
                    LongIdInfo longIdInfo = new LongIdInfo(shard_id, DBObjectType.USER_TYPE, (long)(reader["user_id"]));
                    ShardHelper.InsertIntoBaseMapping(mobile, longIdInfo);
                    return longIdInfo;
                }
                return null;
            })).Execute();
        }
        public static LoginUser GetUpdatedAppDevice(App app, string mobile, double lat, double lng, string fcmToken = null, string otp = null, string brand = null, string model = null, string appVersion = null, string advertisingId = null)
        {
            Logger.GetInstance().Info($"CoreUserService:GetUpdatedAppDevice called with app:{app}, mobile:{mobile}, fcmToken:{fcmToken}, otp:{otp}, brand:{brand}, model:{model}, appVersion:{appVersion}, advertisingId:{advertisingId}");
            LongIdInfo longUserId = ShardHelper.getLongUserIdFromMobile(mobile);
            if (longUserId is null)
            {
                LongIdInfo id = InsertNewUser(mobile, Guid.NewGuid().ToString(), ShardHelper.SHARD0);
                longUserId = id;
                if(app == App.HOME_ROUTER)
                    Constants.BOOKING_LOG.Publish("mobile_identified", mobile, new Dictionary<string, object>() {
                        { "mobile", mobile },
                        { "advertising_id", advertisingId },
                        { "app_version", appVersion }
                    });
            } 
            //else
            //{
            //    if (app == App.HOME_ROUTER && otp != "FCM_UPDATE")
            //        Constants.bookingLogs.Publish("mobile_entered", new Dictionary<string, object>() {
            //            { "mobile", mobile },
            //            { "advertising_id", advertisingId },
            //            { "app_version", appVersion }
            //        });
            //}
            if (!string.IsNullOrEmpty(advertisingId) && !advertisingId.Contains("0000-"))
            {
                bool validLoginAccess = CoreUserService.GetAdvertisingEntry(longUserId, advertisingId);
                Dictionary<string, int> dictionaryForCount = CoreUserService.IsPartnerLoggingThroughCustomer(longUserId, advertisingId);
                if (dictionaryForCount.ContainsKey("HOME_ROUTER") && dictionaryForCount["HOME_ROUTER"] >= 1)
                {
                    if (dictionaryForCount.ContainsKey("LEAD_SERVICES") && !dictionaryForCount.ContainsKey("WIOM_SALES"))
                    {
                        otp = "Growth App User Logging Into Customer";
                    }

                    else if (!dictionaryForCount.ContainsKey("LEAD_SERVICES") && dictionaryForCount.ContainsKey("WIOM_SALES"))
                    {
                        otp = "Partner App User Logging Into Customer";
                    }

                    else
                    {
                        otp = "Warning: Unauthorized Login";
                    }
                    return new LoginUser()
                    {
                        otp = otp
                    };


                }
                if (!validLoginAccess)
                {
                    Logger.GetInstance().Info($"CoreUserService:GetUpdatedAppDevice blocked because of duplicate advertisingid mobile:{mobile}, fcmToken:{fcmToken}, otp:{otp}, brand:{brand}, model:{model}, appVersion:{appVersion}, advertisingId:{advertisingId}");
                    return new LoginUser()
                    {
                        otp = "EXCEED_LOGIN_LIMIT",
                    };
                }
            }
            return new ShardQueryExecutor<LoginUser>(new GetSqlCommand((out ResponseType res) =>
            {
                SqlCommand cmd = new SqlCommand(@"
                    DECLARE @t_app_device_id BIGINT = 0
                    IF (@fcm_token IS NOT NULL AND @otp IS NOT NULL)
                        BEGIN
                            SET @t_app_device_id = (SELECT top 1 id FROM t_app_device WHERE t_user_id = @t_user_id AND fcm_token = @fcm_token order by id)   

                            IF (@t_app_device_id = 0 OR @t_app_device_id IS NULL)
                                BEGIN
                                    INSERT INTO t_app_device(t_user_id, app_name, fcm_token, otp, otp_issued_time, installed_status, app_version, brand, model, added_time, advertisingId, lat, lng)
                                    VALUES(@t_user_id, @app_name, @fcm_token, @otp, GETUTCDATE(), 1, @app_version, @brand, @model, GETUTCDATE(), @advertisingId, @lat, @lng)
                                    SET @t_app_device_id = SCOPE_IDENTITY()
                                END
                            ELSE
                                BEGIN
                                    UPDATE t_app_device SET otp = @otp, otp_issued_time = GETUTCDATE(),
				                    app_version = COALESCE(@app_version, app_version), app_name = COALESCE(@app_name, app_name), advertisingId = COALESCE(@advertisingId, advertisingId),
                                    lat = COALESCE(NULLIF(@lat, 0), lat), lng = COALESCE(NULLIF(@lng, 0), lng)
				                    WHERE id = @t_app_device_id
                                END
                            SELECT * FROM t_user tu LEFT JOIN t_app_device tad ON tu.id = tad.t_user_id WHERE tu.id = @t_user_id AND tad.fcm_token = @fcm_token
                        END
                    ELSE
                        BEGIN
                            SELECT * FROM t_user WHERE id = @t_user_id
                        END");


                cmd.Parameters.Add(new SqlParameter("@mobile", mobile));
                cmd.Parameters.Add(new SqlParameter("@token", Guid.NewGuid().ToString()));
                cmd.Parameters.Add(new SqlParameter("@app_name", app.ToString()));
                cmd.Parameters.Add(new SqlParameter("@fcm_token", CoreUtil.ToSafeDbObject(fcmToken)));
                cmd.Parameters.Add(new SqlParameter("@lat", CoreUtil.ToSafeDbObject(lat)));
                cmd.Parameters.Add(new SqlParameter("@lng", CoreUtil.ToSafeDbObject(lng)));
                cmd.Parameters.Add(new SqlParameter("@otp", CoreUtil.ToSafeDbObject(otp)));
                cmd.Parameters.Add(new SqlParameter("@app_version", CoreUtil.ToSafeDbObject(appVersion)));
                cmd.Parameters.Add(new SqlParameter("@brand", CoreUtil.ToSafeDbObject(brand)));
                cmd.Parameters.Add(new SqlParameter("@model", CoreUtil.ToSafeDbObject(model)));
                cmd.Parameters.Add(new SqlParameter("@t_user_id", longUserId.local_value));
                cmd.Parameters.Add(new SqlParameter("@advertisingId", advertisingId.ToSafeDbObject()));
                res = ResponseType.READER;
                return cmd;
            }), longUserId.shard_id,
            new ResponseHandler<LoginUser>((reader) =>
            {
                if (reader.Read())
                {
                    return CreateLoginUser(longUserId, reader);
                }
                return null;
            })).Execute();
            
        }

        public static List<string> GetFcmToken(long shardId, List<string> mobiles, App app)
        {
            var list = new List<string>();
            if (mobiles == null || mobiles.Count == 0)
                return list;

            return new ShardQueryExecutor<List<string>>(new GetSqlCommand((out ResponseType res) =>
            {
                SqlCommand cmd = new SqlCommand(@"select a.fcm_token from t_app_device a inner join t_user b on (a.t_user_id = b.id)
                    where mobile in ('" + String.Join("','", mobiles) + "') and a.app_name = @app_name");

                cmd.Parameters.Add(new SqlParameter("@app_name", app.ToString()));

                res = ResponseType.READER;
                return cmd;
            }), shardId,
            new ResponseHandler<List<string>>((reader) =>
            {
                while (reader.Read())
                {
                    list.Add(reader["fcm_token"].ToString());
                }
                return list;
            })).Execute();
        }

        public static LoginUser CreateLoginUser(LongIdInfo userId, SqlDataReader reader)
        {
            LoginUser user = new LoginUser();
            user.id = userId;
            user.token = reader["token"].ToString();
            user.mobile = reader["mobile"].ToString();
            try
            {
                user.fcmToken = reader["fcm_token"].ToString();
            }
            
            catch (Exception ex) { }
            return user;
        }

        public static JsonResponse SaveFeatureList(LongIdInfo longUserId, Dictionary<string, int> features)
        {
            long userid = longUserId.GetLongId();
            Logger.GetInstance().Info("Saving feature list");
            return new ShardQueryExecutor<JsonResponse>(new GetSqlCommand((out ResponseType res) =>
            {
                SqlCommand cmd = new SqlCommand("set_feature_new");
                cmd.CommandType = System.Data.CommandType.StoredProcedure;
                DataTable dataTable = new DataTable();
                dataTable.Columns.Add("value1", typeof(int));
                dataTable.Columns.Add("value2", typeof(int));
                dataTable.Columns.Add("value3", typeof(int));
                foreach (KeyValuePair<string, int> feature in features)
                {
                    dataTable.Rows.Add(longUserId.local_value, feature.Key, feature.Value);
                }
                cmd.Parameters.Add(new SqlParameter("@feature_list", dataTable));
                res = ResponseType.NONQUERY;
                return cmd;
            }), longUserId.shard_id,
           new ResponseHandler<JsonResponse>((reader) =>
           {
               return new JsonResponse(ResponseStatus.SUCCESS, "", null);
           })).Execute();
        }

        public static ManagementUser GetAdminUserFromToken(long shardId, string token)
        {
            return new ShardQueryExecutor<ManagementUser>(new GetSqlCommand((out ResponseType res) =>
            {
                SqlCommand cmd = new SqlCommand(@"SELECT tad.*, tam.mapping_params AS account_params FROM t_admin tad
                    LEFT JOIN t_account_mapping1 tam on(tad.user_id = tam.mapped_id AND tam.mapping_type = 'user') WHERE api_token = @token");
                cmd.Parameters.Add(new SqlParameter("@token", token));
                res = ResponseType.READER;
                return cmd;
            }), shardId,
            ((reader) =>
            {
                ManagementUser user = null;
                if (reader.Read())
                {
                    user = CoreUserService.CreateManagementUser(shardId, reader);
                    user.features = GetFeatureList(user.userid, out bool idFresh, user.userType);
                }
                return user;
            })).Execute();
        }

        public static Dictionary<string, int> GetFeatureList(LongIdInfo userid, out bool isFresh, AdminUserType? userType = null)
        {
            isFresh = true;
            Dictionary<string, int> feature_list = new Dictionary<string, int>();
            foreach (Feature value in Enum.GetValues(typeof(Feature)))
            {
                int on = 0;
                switch (value)
                {
                    case Feature.ADVANCE_SETTINGS_OPTIONS:
                    case Feature.DATA_USAGE_PER_SESSION:
                    case Feature.DATA_USAGE_CONTROL_MONTH:
                    case Feature.BANDWIDTH_CONTROL:
                    case Feature.BANDWIDTH_AFTER_EXHAUSTED:
                    case Feature.MAX_DATA_USAGE_PER_DAY:
                    case Feature.SESSION_TIMEOUT:
                    case Feature.NUMBER_OF_DEVICE_PER_USER:
                    case Feature.HIDE_QUESTIONS:
                    case Feature.REPORTS:
                    case Feature.SHOW_PHONE_NUMBER:
                    case Feature.DATA_USAGE_TAB:
                    case Feature.IMPERSONATE:
                    case Feature.BLOCKED_PHONE_NUMBER_LIST:
                    case Feature.PHONE_NUMBER_WHITELISTING:
                    case Feature.VIP_MAC_LIST:
                    case Feature.VIP_PHONE_NUMBER_LIST:
                        if (userType == AdminUserType.READ_ONLY_ADMIN)
                        {
                            on = 1;
                        }
                        on = (userType != null && userType == AdminUserType.ADMIN) ? 1 : on;
                        break;
                    case Feature.STORE_OPERATIONS_NAVIGATOR:
                    case Feature.ADMIN_OPERATIONS:
                        on = (userType != null && userType == AdminUserType.ADMIN) ? 1 : on;
                        break;
                    case Feature.OPERATIONS_PORTAL:
                    case Feature.DEVICE_CONFIG:
                    case Feature.EVENT_LOGGER:
                    case Feature.MEDIA_MANAGER:
                        on = -1;
                        break;
                    case Feature.ADMIN_PORTAL_ACCESS:
                        on = 1;
                        break;
                    default:
                        on = 0;
                        on = (userType != null && userType == AdminUserType.ADMIN) ? 1 : on;
                        break;
                }
                if (on == 1)
                    isFresh = false;
                feature_list.Add(value.GetHashCode().ToString(), on);
            }


            return new ShardQueryExecutor<Dictionary<string, int>>(new GetSqlCommand((out ResponseType res) =>
            {
                SqlCommand cmd = new SqlCommand("Select user_id, feature, state from t_feature_list_new where user_id = @userid");
                cmd.Parameters.Add(new SqlParameter("@userid", userid.local_value));
                res = ResponseType.READER;
                return cmd;
            }), userid.shard_id,
            new ResponseHandler<Dictionary<string, int>>((reader) =>
            {
                while (reader.Read())
                {
                    string feature = reader["feature"].ToString();
                    feature_list.Remove(feature);
                    if (userType == AdminUserType.ADMIN
                        && !((Feature)int.Parse(feature) == Feature.OPERATIONS_PORTAL
                        || (Feature)int.Parse(feature) == Feature.EVENT_LOGGER
                        || (Feature)int.Parse(feature) == Feature.MEDIA_MANAGER
                        || (Feature)int.Parse(feature) == Feature.DEVICE_CONFIG))
                    {
                        feature_list.Add(feature, 1);
                    }
                    else
                    {
                        feature_list.Add(feature, Convert.ToInt32(reader["state"].ToString()));
                    }
                }

                return feature_list;
            })).Execute();
        }

        public static bool GetAdvertisingEntry(LongIdInfo longUserId, string advertisingId)
        {
            return new ShardQueryExecutor<bool>(new GetSqlCommand((out ResponseType res) =>
            {
                SqlCommand cmd = new SqlCommand(@"
                                                IF NOT EXISTS(select 1 from t_app_device where t_user_id=@user_id and advertisingid = @advertisingId)
                                                BEGIN
                                                    SELECT Count(distinct t_user_id) AS unique_login_count
                                                    FROM t_app_device
                                                    WHERE advertisingid = @advertisingId
                                                END
                                                ELSE
                                                BEGIN
                                                    DECLARE @unique_login_count as int= 0
                                                    select @unique_login_count as unique_login_count
                                                END");
                cmd.Parameters.Add(new SqlParameter("@advertisingId", advertisingId));
                cmd.Parameters.Add(new SqlParameter("@user_id", longUserId.ToSafeDbObject()));

                res = ResponseType.READER;
                return cmd;
            }), longUserId.shard_id,
            new ResponseHandler<bool>((reader) =>
            {
                if (reader.Read())
                {
                    return Int64.Parse(reader["unique_login_count"].ToString()) < 5;
                }
                return true;
            })).Execute();
        }

        public static Dictionary<string, int> IsPartnerLoggingThroughCustomer(LongIdInfo longUserId, string advertisingId)
        {
            Dictionary<string, int> myDictionary = new Dictionary<string, int>();

            return new ShardQueryExecutor<Dictionary<string, int>>(new GetSqlCommand((out ResponseType res) =>
            {
                SqlCommand cmd = new SqlCommand(@"
                                                IF EXISTS(select 1 from t_app_device where (otp_submitted_time >= DATEADD(day, -3, GETDATE()) OR (otp_issued_time >= DATEADD(day, -3, GETDATE()) and otp='FCM_UPDATE')) and advertisingId = @advertisingId and app_name IN ('LEAD_SERVICES','WIOM_SALES'))
                                                BEGIN
                                                    IF NOT EXISTS (SELECT 1 FROM t_app_device WHERE t_user_id=@user_id and advertisingid = @advertisingId)
                                                    BEGIN
	                                                    SELECT app_name,COUNT(*) AS count
                                                        FROM t_app_device
                                                        WHERE advertisingId = @advertisingId
                                                        GROUP BY app_name
                                                        END
                                                END");
                cmd.Parameters.Add(new SqlParameter("@advertisingId", advertisingId));
                cmd.Parameters.Add(new SqlParameter("@user_id", longUserId.ToSafeDbObject()));

                res = ResponseType.READER;
                return cmd;
            }), longUserId.shard_id,
            new ResponseHandler<Dictionary<string, int>>((reader) =>
            {

                while (reader.Read())
                {
                    String app_name = (string)reader["app_name"];
                    int count = (int)reader["count"];
                    myDictionary.Add(app_name, count);

                }
                return myDictionary;

            })).Execute();
        }

        public static bool UpdateWlToken(LongIdInfo longUserId)
        {
            return new ShardQueryExecutor<bool>(new GetSqlCommand((out ResponseType res) =>
            {
                SqlCommand cmd = new SqlCommand(@"
                            DECLARE @token varchar(1000) = NULL;
                            -- Check if the user ID exists in the table
                            SELECT @token = token FROM t_user WHERE id = @userId;

                            IF (@token IS NOT NULL)
                            BEGIN
                                -- Check if the token value contains a '#' character
                                IF (CHARINDEX('#', @token, 0) <> 0)
                                BEGIN
                                    -- Replace all '#' characters in the token value
                                    IF (CHARINDEX('#', @token, 0) <> 0)
                                    BEGIN
                                        SET @token = REPLACE(@token, '#', '');
                                    END
                                    -- Update the token value in the table
                                    UPDATE t_user SET token = @token WHERE id = @userId;
                                END
                                ELSE
                                BEGIN
                                    -- Check if the token value already ends with a '#' character
                                    IF (RIGHT(@token, 1) <> '#')
                                    BEGIN
                                        -- Add a '#' character to the end of the token value
                                        SET @token = @token + '#';
                                    END
                                    -- Update the token value in the table
                                    UPDATE t_user SET token = @token WHERE id = @userId;
                                END
                            END");
                cmd.Parameters.Add(new SqlParameter("@userId", longUserId.ToSafeDbObject()));
                res = ResponseType.READER;
                return cmd;
            }), longUserId.shard_id,
            new ResponseHandler<bool>((reader) =>
            {
                if (reader.RecordsAffected >= 1)
                {
                    return true;
                }
                return false;
            })).Execute();
        }

        public static bool IsLocationMapped(ManagementUser user, LongIdInfo nasid)
        {
            return new ShardQueryExecutor<bool>(new GetSqlCommand((out ResponseType res) =>
            {
                SqlCommand cmd = new SqlCommand(@"
                    IF EXISTS(SELECT 1 FROM t_admin_mapping where admin_id = @admin_id and mapped_id = @nasid and mapping_type = 'location')
                        BEGIN
                            SELECT 1 AS mapped
                        END
                    ELSE  
                        BEGIN
                            SELECT 0 AS mapped
                        END");
                cmd.Parameters.Add(new SqlParameter("@admin_id", user.userid.ToSafeDbObject()));
                cmd.Parameters.Add(new SqlParameter("@nasid", nasid.ToSafeDbObject()));
                res = ResponseType.READER;
                return cmd;
            }), user.userid.shard_id,
            new ResponseHandler<bool>((reader) =>
            {
                if (reader.Read())
                    return (int)reader["mapped"] == 1;
                return false;
            })).Execute();
        }
        public static Dictionary<long, PDOPlan> GetALLPlanIdInfoList()
        {
            Dictionary<long, PDOPlan> allPlanIdInfo = new Dictionary<long, PDOPlan>();
            new ShardQueryExecutor<bool>(new GetSqlCommand((out ResponseType res) =>
            {
                SqlCommand cmd = new SqlCommand(@"select * from t_plan_configuration where combined_setting_id <=100");
                res = ResponseType.READER;
                return cmd;
            }), ShardHelper.SHARD0,
            new ResponseHandler<bool>((reader) =>
            {
                while (reader.Read())
                {
                    PDOPlan pDOPlan = CoreDbCalls.GetInstance().planDbReader(reader);
                    if (pDOPlan.id != 0)
                        allPlanIdInfo[pDOPlan.id] = pDOPlan;
                }
                return true;
            })).Execute();
            return allPlanIdInfo;
        }

        public static int CheckUniqueSsidPass(string userSsid, string ssidPass)
        {
            int signalCount = 0;
            new ShardQueryExecutor<int>(new GetSqlCommand((out ResponseType res) =>
            {
                SqlCommand cmd = new SqlCommand(@"SELECT COUNT(DISTINCT(nas_id)) AS signal_count FROM t_single_nas_operations
                    WHERE single_operation_id = 18 AND parameters = @ssid");

                cmd.Parameters.Add(new SqlParameter("@ssid", userSsid));
                res = ResponseType.READER;
                return cmd;
            }),
            new ExecuteAllResponseHandler((reader, shardId) =>
            {
                JObject obj = new JObject();
                if (reader.Read())
                {
                    int sc = reader.GetValueOrDefault<int>("signal_count");
                    signalCount += sc;
                }
            })).ExecuteAll();
            return signalCount;
        }

        public static string GetAdvertisingIdFromMobile(string mobile)
        {
            LongIdInfo userId = ShardHelper.getLongUserIdFromMobile(mobile);
            string adId = new ShardQueryExecutor<string>(new GetSqlCommand((out ResponseType res) =>
            {
                SqlCommand cmd = new SqlCommand(@"SELECT * FROM t_app_device WHERE 
                    t_user_id = @user_id and advertisingId IS NOT NULL 
                    ORDER BY added_time DESC");

                cmd.Parameters.Add(new SqlParameter("@user_id", userId.ToSafeDbObject()));
                res = ResponseType.READER;
                return cmd;
            }), userId.shard_id,
           new ResponseHandler<string>((reader) =>
           {
               string adId = null;
               if (reader.Read())
               {
                   adId = reader.GetValueOrDefault<string>("advertisingId");
                   if (!adId.IsNullOrEmpty())
                       return adId;
               }
               return adId;

           })).Execute();
           return adId;
        }

        public static void RemoveAdvertisingId(string mobile)
        {
            Logger.GetInstance().Info($"Removing advertising id for : {mobile}");
            LongIdInfo userId = ShardHelper.getLongUserIdFromMobile(mobile);
            if (userId == null)
                return;
            bool success = new ShardQueryExecutor<bool>(new GetSqlCommand((out ResponseType res) =>
            {
                SqlCommand cmd = new SqlCommand(@"UPDATE t_app_device SET advertisingId = NULL WHERE t_user_id = @user_id");

                cmd.Parameters.Add(new SqlParameter("@user_id", userId.ToSafeDbObject()));
                res = ResponseType.READER;
                return cmd;
            }), userId.shard_id,
           new ResponseHandler<bool>((reader) =>
           {
               string adId = null;
               if (reader.RecordsAffected > 0)
               {
                   return true;
               }
               return false;

           })).Execute();
        }

        public static LoginUser GetUserFromToken(long shardId, string token)
        {
            return new ShardQueryExecutor<LoginUser>(new GetSqlCommand((out ResponseType res) =>
            {
                SqlCommand cmd = new SqlCommand("SELECT TOP 1 * FROM t_user WHERE token = @token ORDER BY id DESC");
                cmd.Parameters.Add(new SqlParameter("@token", token));
                res = ResponseType.READER;
                return cmd;
            }), shardId,
            new ResponseHandler<LoginUser>((reader) =>
            {
                if (reader.Read())
                {
                    return CoreUserService.CreateLoginUser(new LongIdInfo(shardId, DBObjectType.USER_TYPE, reader["id"]), reader);
                }
                return null;
            })).Execute();
        }

    }
}
