using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using i2e1_basics.Database;
using i2e1_basics.Models;
using i2e1_basics.Utilities;
using i2e1_core.Utilities;
using Newtonsoft.Json;
using wiom_router_api.Models;

namespace i2e1_core.services
{
    public class CoreInventoryService
    {
        public static Inventory InventoryDbReader(SqlDataReader reader)
        {
            var inv = new Inventory();
            var exd = new Dictionary<string, object>();

            exd = !string.IsNullOrEmpty(reader.GetValueOrDefault<string>("extra_data")) ? JsonConvert.DeserializeObject<Dictionary<string, object>>(reader.GetValueOrDefault<string>("extra_data")) : exd;
            exd["serial"] = reader.GetValueOrDefault<string>("serial");
            exd["model"] = reader.GetValueOrDefault<string>("model");

            inv.product = reader["product"].ToString();
            inv.shard_id = reader.GetValueOrDefault<int>("shard_id");
            inv.challan = reader["challan"].ToString();
            inv.lcoAccountId = LongIdInfo.IdParser(reader.GetValueOrDefault<long>("lco_account_id"));
            inv.deviceId = reader.GetValueOrDefault<string>("device_id");
            inv.mac = reader.GetValueOrDefault<string>("mac");
            inv.version = reader.GetValueOrDefault<string>("version");
            inv.addedTime = reader.GetDateTime("added_time").ConvertUtcToIST();
            inv.model = reader.GetValueOrDefault<string>("model");
            inv.inventoryRequestId  = reader.GetValueOrDefault<long>("inventory_request_id");
            inv.user_account_id = LongIdInfo.IdParser(reader.GetValueOrDefault<long>("user_account_id"));
            inv.extra_data = exd;

            if (reader["nasid"] != DBNull.Value)
                inv.nasid = new LongIdInfo(inv.shard_id, DBObjectType.ACTIVE_NAS, reader.GetValueOrDefault<long>("nasid"));

            if (Enum.TryParse<InventoryType>(reader.GetValueOrDefault<string>("type") ?? string.Empty, out var type))
                inv.type = type;

            return inv;
            }
        
        public static Inventory UpdateInventorySequence(string deviceId, long shardId)
        {
            Logger.GetInstance().Info($"InventoryService:UpdateNasIdOfDevice called with deviceId:" + deviceId);
            return new MasterQueryExecutor<Inventory>(new GetSqlCommand((out ResponseType res) =>
            {
                SqlCommand cmd = new SqlCommand(@"UPDATE t_device SET
                                                 nasid = (NEXT VALUE FOR dbo.t_device_Id_Sequence),
                                                 shard_id = @shard_id,       
                                                 modified_time = getutcdate()
                                                 WHERE device_id = @device_id;

                                                 select * from t_device where device_id = @device_id;
                                                ");

                cmd.Parameters.Add(new SqlParameter("@device_id", deviceId.ToSafeDbObject()));
                cmd.Parameters.Add(new SqlParameter("@shard_id", shardId));

                res = ResponseType.READER;
                return cmd;
            }),
            new ResponseHandler<Inventory>((reader) =>
            {
                if (reader.Read())
                {
                    var inventory = InventoryDbReader(reader);
                    ShardHelper.ResetDeviceCache(inventory.mac, deviceId, inventory.nasid);
                    return inventory;
                }
                return null;
            })).Execute();
        }

        public static Object FetchInventory(InventorySearchType search)
        {
            Logger.GetInstance().Info("CoreInventoryService : FetchInventory called with search : " + JsonConvert.SerializeObject(search));

            if (search.pageParams == null)
                search.pageParams = new SearchQuery();
            search.pageParams.setIfEmpty("added_time", "desc");

            List<LongIdInfo> longIds = new List<LongIdInfo>();
            if (search.nasid != null)
                longIds.Add(search.nasid);
            else if (search.lcoAccountId != null)
                longIds.Add(search.lcoAccountId);

            List<Inventory> inventories = new List<Inventory>();

            try
            {
                var x = new PaginatedMasterQueryExecutor<Object>(new GetSqlCommand((out ResponseType res) =>
                {
                    SqlCommand cmd = new SqlCommand(@"
                    select * from t_device where
                    (@challan IS NULL OR challan LIKE '%' + @challan + '%') AND 
                    (@nasid IS NULL OR nasid = @nasid AND (@shard_id IS NULL or shard_id = @shard_id)) AND (@user_account_id is NULL or user_account_id = @user_account_id) AND
                    (@lcoAccountId IS NULL OR lco_account_id = @lcoAccountId) AND (@mac IS NULL OR mac LIKE '%' + @mac + '%') AND
                    (@deviceId IS NULL OR device_id = @deviceId) AND (@serial IS NULL OR serial LIKE '%' + @serial + '%') AND
                    (@from IS NULL OR added_time >= @from) AND (@to IS NULL OR added_time <= @to)");

                    cmd.Parameters.Add(new SqlParameter("@lcoAccountId", search.lcoAccountId.ToSafeDbObject(1)));
                    cmd.Parameters.Add(new SqlParameter("@nasid", search.nasid.ToSafeDbObject()));
                    cmd.Parameters.Add(new SqlParameter("@shard_id", search.shard_id.ToSafeDbObject()));
                    cmd.Parameters.Add(new SqlParameter("@challan", search.challan.ToSafeDbObject()));
                    cmd.Parameters.Add(new SqlParameter("@deviceId", search.deviceId.ToSafeDbObject()));
                    cmd.Parameters.Add(new SqlParameter("@serial", search.serial.ToSafeDbObject()));
                    cmd.Parameters.Add(new SqlParameter("@from", search.fromDate.ToSafeDbObject()));
                    cmd.Parameters.Add(new SqlParameter("@user_account_id", search.user_account_id.ToSafeDbObject()));
                    cmd.Parameters.Add(new SqlParameter("@to", search.toDate.ToSafeDbObject()));
                    cmd.Parameters.Add(new SqlParameter("@mac", string.IsNullOrEmpty(search.mac) ? DBNull.Value : CoreUtil.GetNormalisedMac(search.mac)));
                    cmd.Parameters.Add(new SqlParameter("@type", search.type.ToSafeDbObject()));

                    res = ResponseType.READER;
                    return cmd;
                }),
                ((reader) =>
                {
                    while (reader.Read())
                        inventories.Add(InventoryDbReader(reader));
                    return inventories;
                }), search.pageParams).Execute();
                if (search.pageParams.selectCount || !string.IsNullOrEmpty(search.pageParams.sumOf))
                    return x;
                Logger.GetInstance().Info("CoreInventoryService : FetchInventory returned inventory : " + JsonConvert.SerializeObject(inventories));

                return inventories;
            }
            catch (Exception ex)
            {
                Logger.GetInstance().Error(ex.Message);
            }
            return inventories;
        }
    }
}
