using i2e1_basics.Database;
using i2e1_basics.DynamoUtilities;
using i2e1_basics.Utilities;
using i2e1_core.Utilities;
using System;
using System.Collections.Generic;
using System.Data.SqlClient;
using System.Linq;
using wiom_routerplan_share.ExposeApi;
using wiom_routerplan_share.Models.RouterPlan;
using static i2e1_basics.DynamoUtilities.DynamoEnum;

namespace i2e1_core.services
{
	public class CoreFDMService
    {
        //Call this method only in case user has to be loggedout/data or time plans to be updated/auth_state to be altered
        public static bool UpdateUserPolicy(SecondaryRouterPlan user)
        {
            return GenericApi.GetInstance().UpdateSecondaryPlan(user).Result;
        }

        public static bool UpdateUserPolicy(HomeRouterPlan user)
        {
			return GenericApi.GetInstance().UpdateHomeRouterPlan(user).Result;
		}

		public static List<HomeRouterPlan> FillPasportUsers(long shardId, SqlDataReader reader)
        {
            List<HomeRouterPlan> list = new List<HomeRouterPlan>();
            bool hasShopName = Enumerable.Range(0, reader.FieldCount).Any(i => string.Equals(reader.GetName(i), "shop_name", StringComparison.OrdinalIgnoreCase));
            var now = DateTime.UtcNow;
            while (reader.Read())
            {
                HomeRouterPlan pgUser = FillPasportUser(shardId, reader, hasShopName, now);
                if (pgUser != null)
                    list.Add(pgUser);

            }
            return list;

        }

        public static HomeRouterPlan FillPasportUser(long shardId, SqlDataReader reader, bool hasShopName = false, DateTime? now = null)
        {
            if (now == null)
                now = DateTime.UtcNow;

            HomeRouterPlan pgUser = null;
            try
            {
                pgUser = new HomeRouterPlan()
                {
                    transactionId = reader["transaction_id"].ToString(),
                    planEndTime = (DateTime)reader["otp_expiry_time"],
                    planStartTime = (DateTime)reader["otp_issued_time"],
                    dataLimit = Convert.ToInt32(reader["data_plan"].ToString()),
                    nasId = new LongIdInfo(shardId, (long)DBObjectType.ACTIVE_NAS, Convert.ToInt64(reader["router_nas_id"])),
                    mobile = reader["mobile"].ToString(),
                    charges = Convert.ToInt32(reader["charges"].ToString()),
                    lcoAccountId = reader["created_by"] == DBNull.Value ? null : new LongIdInfo(shardId, DBObjectType.ACCOUNT_TYPE, reader["created_by"])
                };

                pgUser.deviceLimit = (byte)reader["device_limit"];
                pgUser.createdTime = (DateTime)reader["created_on"];


                pgUser.planEndTime = CoreUtil.ConvertUtcToIST(pgUser.planEndTime);
                pgUser.planStartTime = CoreUtil.ConvertUtcToIST(pgUser.planStartTime);
                pgUser.createdTime = CoreUtil.ConvertUtcToIST(pgUser.createdTime);
                pgUser.planId = Convert.ToInt32(reader["selected_plan_id"].ToString());
            }
            catch (Exception ex)
            {
                Logger.GetInstance().Error("error in reading fdm record : " + reader["id"] + " " + ex.Message);
            }

            return pgUser;
        }

        public static HomeRouterPlan FetchLastPlan(LongIdInfo nasid)
        {
            ModelDynamoDb<HomeRouterPlan> modelDynamoDb = new ModelDynamoDb<HomeRouterPlan>();
            DynamoQueryBuilder<HomeRouterPlan> dynamoQueryBuilder = new DynamoQueryBuilder<HomeRouterPlan>();

            dynamoQueryBuilder.AddkeyConditionExpression((p) => p.nasId, DynamoComparisonOperator.Equal, nasid.GetLongId())
                              .AddFilterCondition((p) => p.deviceLimit, DynamoComparisonOperator.GreaterThanOrEqual, Constants.HOME_ROUTER_DEVICE_LIMIT);

            List<HomeRouterPlan> plans = modelDynamoDb.GetRecord(dynamoQueryBuilder, null, 1);
            return plans[0];
        }

        public static HomeRouterPlan ExtendPlanOnNas(HomeRouterPlan lastHomeRouterPlan, LongIdInfo nasid, long increaseBySeconds)
        {
            lastHomeRouterPlan.nasId = nasid;
            lastHomeRouterPlan.entryUnixEpochTime = lastHomeRouterPlan.entryUnixEpochTime;
            lastHomeRouterPlan.planIncreasedInSec = lastHomeRouterPlan.planIncreasedInSec + increaseBySeconds;
            lastHomeRouterPlan.planEndTime = lastHomeRouterPlan.planEndTime.AddSeconds(increaseBySeconds);
            Constants.CUSTOMER_LOG.Publish("plan_extended", lastHomeRouterPlan.mobile, new Dictionary<string, object>() {
                { "nasid", nasid.GetLongId() },
                { "incr_by_secs", increaseBySeconds },
                { "mobile", lastHomeRouterPlan.mobile },
                { "new_exp_time", lastHomeRouterPlan.planEndTime }
            });
            return (new ModelDynamoDb<HomeRouterPlan>()).GetUpdatedValue(lastHomeRouterPlan);
        }
    }
}
