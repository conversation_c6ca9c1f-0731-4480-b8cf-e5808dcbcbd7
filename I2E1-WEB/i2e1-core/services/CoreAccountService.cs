using Amazon.DynamoDBv2.Model;
using i2e1_basics.Cache;
using i2e1_basics.Database;
using i2e1_basics.DynamoUtilities;
using i2e1_basics.Models;
using i2e1_basics.Utilities;
using i2e1_core.Models;
using i2e1_core.Models.Client;
using i2e1_core.Models.Payment;
using i2e1_core.Utilities;
using Microsoft.Extensions.Caching.Memory;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Linq;
using wifidog_core.Models.WIOM;
using wiom_login_share.Models;
using wiom_router_api.Models;
using wiom_routerplan_share.Models.RouterPlan;
using static i2e1_basics.DynamoUtilities.DynamoEnum;

namespace i2e1_core.services
{
	public enum WgStatus
    {
        PAID = 0,
        DUE_ON = 1,
        OVERDUE = 2,
        OVERDUE_INTEREST = 3,
        INTERNET_STOPPED = 4,
        DISCONNECTED = 5,
        REMOVED = 6
    }
    public class CoreAccountService
    {
        public static Account SaveAccount(long shardId, Account account)
        {
            account.extraData = String.Empty;
            if (account.extraDataObject != null)
                account.extraData = JsonConvert.SerializeObject(account.extraDataObject);

           Logger.GetInstance().Info(String.Format("AccountService:SaveAccount called with id:{0}, name: {1}, gst: {2}, address: {3}, logicalGroup: {4}, extra_data: {5}", account.id, account.name, account.gst, account.address, account.logicalGroup, account.extraData));
            return new ShardQueryExecutor<Account>(new GetSqlCommand((out ResponseType res) =>
            {
                SqlCommand cmd = new SqlCommand(@"
                DECLARE @status INT = 0
                IF NOT EXISTS(SELECT 1 FROM t_account WHERE id = @id)
                BEGIN
                    INSERT INTO t_account(name, gst, address, logical_group, extra_data, added_time)
                    VALUES(@name, @gst, @address, @logicalGroup, @extra_data, GETUTCDATE())
                    SET @id = SCOPE_IDENTITY()
                    SET @status = 0
                END
                ELSE
                BEGIN
                    UPDATE t_account set
                    name = COALESCE(@name, name),
                    address = COALESCE(@address, address),
                    gst = COALESCE(@gst, gst),
                    logical_group = COALESCE(@logicalGroup, logical_group),
                    extra_data = Nullif(dbo.fn_json_merge(COALESCE(extra_data, '{}'), COALESCE(@extra_data, '{}')), '{}')
                    WHERE id = @id
                    SET @status = 1
                END
                SELECT *, @status AS status FROM t_account where id = @id");


                cmd.Parameters.Add(new SqlParameter("@id", account.id.ToSafeDbObject()));
                cmd.Parameters.Add(new SqlParameter("@name", account.name.ToSafeDbObject()));
                cmd.Parameters.Add(new SqlParameter("@gst", account.gst.ToSafeDbObject()));
                cmd.Parameters.Add(new SqlParameter("@address", account.address.ToSafeDbObject()));
                cmd.Parameters.Add(new SqlParameter("@logicalGroup", account.logicalGroup.ToSafeDbObject()));
                cmd.Parameters.Add(new SqlParameter("@extra_data", account.extraData.ToSafeDbObject()));

                res = ResponseType.READER;
                return cmd;
            }), shardId,
            new ResponseHandler<Account>((reader) =>
            {
                if (reader.Read() && ((int)reader["status"]) == 0)
                {
                    account.id = new LongIdInfo(shardId, DBObjectType.ACCOUNT_TYPE, Convert.ToInt64(reader["id"]));
                }
                ResetAccountCache(account.id);
                return account;
            })).Execute();
        }

        public static bool UpdateLocationToAccount(LongIdInfo longAccountId, LongIdInfo nasid)
        {
            Logger.GetInstance().Info(String.Format("AccountService:AddLocationToAccount called with accountId:{0}, nasid: {1}, mappingType: {2}", longAccountId, nasid, "location"));

            return new ShardQueryExecutor<bool>(new GetSqlCommand((out ResponseType res) =>
            {
                SqlCommand cmd = new SqlCommand(@"
                            UPDATE t_account_mapping1 
                                SET mapped_id = @mapped_id   WHERE account_id = @acct_id AND mapping_type=@mapping_type");

                cmd.Parameters.Add(new SqlParameter("@acct_id", longAccountId.ToSafeDbObject()));
                cmd.Parameters.Add(new SqlParameter("@mapped_id", nasid.ToSafeDbObject()));
                cmd.Parameters.Add(new SqlParameter("@mapping_type", "location"));
                res = ResponseType.READER;
                return cmd;
            }), longAccountId.shard_id,
            new ResponseHandler<bool>((reader) =>
            {
                if (reader.Read())
                {
                    ResetAccountCache(longAccountId);
                    return true;
                }
                return false;
            })).Execute();
        }

        public static bool AddLocationToAccount(LongIdInfo longAccountId, LongIdInfo longNasid, Dictionary<string, object> locationParams = null)
        {
            long accountId = longAccountId.local_value;
            long nasid = longNasid.local_value;
            string locParams = null;
            if (locationParams != null && locationParams.Count > 0)
                locParams = JsonConvert.SerializeObject(locationParams);
            Logger.GetInstance().Info(String.Format("AccountService:AddLocationToAccount called with accountId:{0}, nasid: {1}, locationParams: {2}", accountId, nasid, locParams));

            return new ShardQueryExecutor<bool>(new GetSqlCommand((out ResponseType res) =>
            {
                SqlCommand cmd = new SqlCommand(@"
                DECLARE @acct_id int = 0;
                SET @acct_id = (SELECT account_id FROM t_account_mapping1 WHERE mapped_id=@nasid AND mapping_type='location')
                IF @acct_id > 0
                    BEGIN
                        IF @acct_id = @accountId AND @mappingParams IS NOT NULL
                        BEGIN
                            UPDATE t_account_mapping1 
                                SET mapping_params = dbo.fn_json_merge(coalesce(mapping_params, '{}'), coalesce(@mappingParams, '{}')) 
                            WHERE mapped_id=@nasid AND account_id = @acct_id AND mapping_type='location'
                        END
                        SELECT @acct_id AS account_id
                    END
                ELSE
                    BEGIN
                        INSERT INTO t_account_mapping1(account_id, mapped_id, mapping_type, sub_mapping_type, mapping_params, added_time)
                        VALUES(@accountId, @nasid, 'location', @subMappingType, @mappingParams, GETUTCDATE())
                        SELECT @accountId AS account_id
                    END");

                cmd.Parameters.Add(new SqlParameter("@nasid", nasid));
                cmd.Parameters.Add(new SqlParameter("@accountId", accountId));
                cmd.Parameters.Add(new SqlParameter("@subMappingType", DBNull.Value));
                cmd.Parameters.Add(new SqlParameter("@mappingParams", locParams.ToSafeDbObject()));
                res = ResponseType.READER;
                return cmd;
            }), longAccountId.shard_id,
            new ResponseHandler<bool>((reader) =>
            {
                if (reader.Read())
                {
                    ResetAccountCache(new LongIdInfo(longAccountId.shard_id, DBObjectType.ACCOUNT_TYPE, accountId));
                    return (accountId == Convert.ToInt64(reader["account_id"]));
                }
                return false;
            })).Execute();
        }

        public static bool AddUserToAccount(LongIdInfo longaccountId, LongIdInfo longadminId, Dictionary<string, object> accountParams = null, bool mergeParams = true)
        {
            long accountId = longaccountId.local_value;
            long adminId = longadminId.local_value;
            Logger.GetInstance().Info(String.Format("AddUserToAccount called with accountId:{0}, adminId: {1}", accountId, adminId));
            return new ShardQueryExecutor<bool>(new GetSqlCommand((out ResponseType res) =>
            {

                SqlCommand cmd = new SqlCommand(@"
                DECLARE @acct_id int = 0;
                DECLARE @ext varchar(max);
                SELECT @acct_id = account_id, @ext = mapping_params FROM t_account_mapping1 where mapped_id=@adminId and mapping_type='user'
                IF @acct_id > 0
                BEGIN
                    IF(@account_params IS NOT NULL AND @merge_params = 1) 
                    BEGIN
                        SET @ext = COALESCE(NULLIF(@ext,''), '{}')
                        SET @account_params = dbo.fn_json_merge(@ext, @account_params)
	                END
                    UPDATE t_account_mapping1 SET mapping_params = @account_params 
                        WHERE mapped_id=@adminId AND account_id=@accountId AND mapping_type='user'
                    SELECT @acct_id AS account_id
                END
                ELSE
                BEGIN
                    INSERT INTO t_account_mapping1(mapped_id, account_id, mapping_type, mapping_params, added_time)
                    VALUES(@adminId, @accountId, 'user', @account_params, GETUTCDATE())
                    SELECT @accountId AS account_id
                END");

                cmd.Parameters.Add(new SqlParameter("@adminId", adminId));
                cmd.Parameters.Add(new SqlParameter("@accountId", accountId));
                cmd.Parameters.Add(new SqlParameter("@merge_params", mergeParams ? 1 : 0));
                SqlParameter p = new SqlParameter("@account_params", accountParams == null ? DBNull.Value : JsonConvert.SerializeObject(accountParams));
                p.Size = 1000;
                cmd.Parameters.Add(p);
                res = ResponseType.READER;
                return cmd;
            }), longaccountId.shard_id,
            new ResponseHandler<bool>((reader) =>
            {
                if (reader.Read())
                {
                    return (accountId == Convert.ToInt64(reader["account_id"]));
                }
                ResetAccountCache(longaccountId, longadminId);
                return false;
            })).Execute();
        }

        public static LongIdInfo UpdateUserToAccount(string usrname, string updatedMobile,string email,string aadhar, LongIdInfo accountId)
        {
            var user = CoreUserService.RegisterAdmin(accountId.shard_id, new ManagementUser()
            {
                contact_no = updatedMobile,
                password = new Random().Next(1000, 10000).ToString(),
                name = usrname,
                email = email,
                email_verified = true,
                userType = AdminUserType.STANDARD,
                authType = AdminAuthType.CUSTOM,
                aadhar = aadhar,
                active = 1
            }, "phone");

            return new ShardQueryExecutor<LongIdInfo>(new GetSqlCommand((out ResponseType res) =>
            {
                SqlCommand cmd = new SqlCommand(@"
                    UPDATE t_account_mapping1 SET mapped_id = @uid 
                        WHERE account_id = @account_id and  mapping_type = 'user'
                       ");
                cmd.Parameters.Add(new SqlParameter("@uid", user.userid.ToSafeDbObject()));
                cmd.Parameters.Add(new SqlParameter("@account_id", accountId.ToSafeDbObject()));
                res = ResponseType.NONQUERY;
                return cmd;
            }), accountId.shard_id,
            new ResponseHandler<LongIdInfo>((reader) =>
            {
                return user.userid;
            })).Execute();
        }

        public static bool AddUserToAccount(LongIdInfo accountId, string username, string name, string email, string aadhar, out ManagementUser user, Dictionary<string, object> accountParams = null)
        {
            user = CoreUserService.RegisterAdmin(accountId.shard_id, new ManagementUser()
            {
                contact_no = username,
                password = new Random().Next(1000, 10000).ToString(),
                name = name,
                email = email,
                email_verified = true,
                userType = AdminUserType.STANDARD,
                authType = AdminAuthType.CUSTOM,
                aadhar = aadhar,
                active = 1
            }, "phone");

            if (user == null)
                return false;

            return AddUserToAccount(accountId, user.userid, accountParams);
        }

        public static bool ResetAccountCache(LongIdInfo accountId, LongIdInfo userId = null)
        {
            if (userId != null)
            {
                CoreCacheHelper.GetInstance().Reset(CoreCacheHelper.ACCOUNT_ID_FROM_USER_ID, userId);
                var usr = CoreUserService.GetAdminUser(userId);
                SessionCacheHelper.GetInstance().Reset($"{usr.token};{Constants.ADMIN_IN_POWER}", "");
                SessionCacheHelper.GetInstance().Reset($"{usr.token};{Constants.ADMIN_USER}", "");
            }
                
            if (accountId == null && userId != null)
                accountId = GetAccountIdFromUserId(userId);
            CoreCacheHelper.GetInstance().Reset(CoreCacheHelper.USER_ACCOUNT, accountId);
            return true;
        }

        public static LongIdInfo GetAccountIdFromUserId(LongIdInfo longuserId, bool disableCache = false)
        {
            return CoreCacheHelper.GetInstance().getValueFromCache(CoreCacheHelper.ACCOUNT_ID_FROM_USER_ID, longuserId.GetLongId(), () => {
                return GetAccountIdFromMappedId(longuserId, "user");
            }, disableCache);
        }

        public static long GetMappedIdFromAccountId(LongIdInfo accountId, string mappingType)
        {
            return new ShardQueryExecutor<long>(new GetSqlCommand((out ResponseType res) =>
            {
                SqlCommand cmd = new SqlCommand(@"SELECT top 1 mapped_id FROM t_account_mapping1 WHERE account_id=@accountId AND mapping_type=@mappingType");

                cmd.Parameters.Add(new SqlParameter("@accountId", accountId.ToSafeDbObject()));
                cmd.Parameters.Add(new SqlParameter("@mappingType", mappingType));
                res = ResponseType.READER;
                return cmd;
            }), accountId.shard_id,
            new ResponseHandler<long>((reader) =>
            {
                if (reader.Read())
                {
                    return Convert.ToInt64(reader["mapped_id"]);
                }
                return 0;
            })).Execute();
        }

        public static LongIdInfo GetAccountIdFromMappedId(LongIdInfo longMappedId, string mappingType)
        {
            return new ShardQueryExecutor<LongIdInfo>(new GetSqlCommand((out ResponseType res) =>
            {
                SqlCommand cmd = new SqlCommand(@"SELECT top 1 account_id FROM t_account_mapping1 WHERE mapped_id=@userId AND mapping_type=@mappingType");

                cmd.Parameters.Add(new SqlParameter("@userId", longMappedId.ToSafeDbObject()));
                cmd.Parameters.Add(new SqlParameter("@mappingType", mappingType));
                res = ResponseType.READER;
                return cmd;
            }), longMappedId.shard_id,
            new ResponseHandler<LongIdInfo>((reader) =>
            {
                if (reader.Read())
                {
                    return new LongIdInfo(longMappedId.shard_id, (long)DBObjectType.ACCOUNT_TYPE,  Convert.ToInt64(reader["account_id"]));
                }
                return null;
            })).Execute();
        }

        public static Object FetchAccounts(AccountSearchType search, DateTime? from = null, DateTime? to = null, SearchQuery query = null, bool fetchMyNestedAccounts = false)
        {
            if (search.includes == null)
                search.includes = new Dictionary<string, int>();

            search.includes.TryGetValue("nases", out int nases);
            search.includes.TryGetValue("users", out int users);

            if (query == null)
                query = new SearchQuery();
            query.setIfEmpty("id", "asc");

            if (string.IsNullOrEmpty(search.appTermIn) || search.appTermIn.ToLower() == "all")
                search.appTermIn = "-1";

            List<Account> accounts = new List<Account>();
            if (!string.IsNullOrEmpty(search.userTerm)){
                LongIdInfo longUserId = ShardHelper.getLongUserIdFromMobile(search.userTerm);
                if(longUserId != null)
                {
                    var usr = CoreUserService.GetAdminUser(longUserId.shard_id, search.userTerm);
                    if (usr != null)
                    {
                        var cstmrAcct = CoreAccountService.GetAccountWithUserId(usr.userid);
                        accounts.Add(cstmrAcct);
                    }
                }
            }
            else if(search.accountId != null)
            {
                var cstmrAcct = CoreAccountService.GetAccountFromId(search.accountId);
                accounts.Add(cstmrAcct);
            }
            else
            {
                new PaginatedShardQueryExecutor<Object>(new GetSqlCommand((out ResponseType res) =>
                {
                    SqlCommand cmd = new SqlCommand(@"SELECT * FROM (
                    SELECT a.*, 
                    CASE WHEN @users = 1 THEN
	                    (SELECT username, contact_no, email FROM t_admin WHERE user_id IN 
                        (SELECT mapped_id FROM t_account_mapping1 WHERE mapping_type = 'user' AND account_id = a.id) FOR JSON AUTO) 
	                    ELSE NULL END AS users,
                    CASE WHEN @nases = 1 THEN
	                    (SELECT mapped_id as nas_id FROM t_account_mapping1 WHERE account_id = a.id AND mapping_type = 'location' FOR JSON AUTO)
                    ELSE NULL END AS nases,
                    (SELECT mapped_id as id, mapping_params as params FROM t_account_mapping1 WHERE account_id = a.id AND mapping_type = 'application' FOR JSON AUTO) AS applications
                    FROM t_account a WHERE (@fetchSubAccounts = 1 AND id in (SELECT mapped_id FROM t_account_mapping1 WHERE mapping_type='account' AND account_id=@acct_id)) OR 
                        (@fetchSubAccounts = 0 AND (@acct_id IS NULL OR id = @acct_id) AND (@term IS NULL OR name LIKE '%' + @term + '%') AND 
                        (-1 IN (" + search.appTermIn + @") OR EXISTS(SELECT * FROM t_account_mapping1 WHERE mapping_type='application' AND account_id = a.id AND mapped_id in (" + search.appTermIn + @")
                            AND (@appMappingParams IS NULL OR mapping_params LIKE '%' + @appMappingParams + '%'))) 
                        AND (@logicalGroup IS NULL OR logical_group LIKE '%' + @logicalGroup + '%') 
                        AND (@logicalGroupNotIn IS NULL OR logical_group NOT LIKE '%' + @logicalGroupNotIn + '%')
                        AND (@clusterId = 0 OR JSON_VALUE(extra_data, '$.cluster') = @clusterId))) x
                    WHERE (@userTerm is NULL OR (@userTerm = '-isnull' AND users IS NULL) OR (@userTerm = '-notnull' AND users IS NOT NULL) OR users LIKE '%' + @userTerm + '%') AND
                    (@nasTerm is NULL OR (@nasTerm = '-isnull' AND nases IS NULL) OR (@nasTerm = '-notnull' AND nases IS NOT NULL) OR nases LIKE '%:' + @nasTerm + '}%')");

                    var nasTerm = (search.nasTerm == "-isnull" || search.nasTerm == "-notnull" || string.IsNullOrEmpty(search.nasTerm)) ? search.nasTerm : (LongIdInfo.IdParser(Convert.ToInt64(search.nasTerm))).ToSafeDbObject().ToString();

                    cmd.Parameters.Add(new SqlParameter("@term", search.term.ToSafeDbObject()));
                    cmd.Parameters.Add(new SqlParameter("@userterm", search.userTerm.ToSafeDbObject()));
                    cmd.Parameters.Add(new SqlParameter("@appTermIn", search.appTermIn.ToSafeDbObject()));
                    cmd.Parameters.Add(new SqlParameter("@acct_id", search.accountId.ToSafeDbObject()));
                    cmd.Parameters.Add(new SqlParameter("@hasBalance", search.hasBalance));
                    cmd.Parameters.Add(new SqlParameter("@nasTerm", nasTerm.ToSafeDbObject()));
                    cmd.Parameters.Add(new SqlParameter("@logicalGroup", search.logicalGroup.ToSafeDbObject()));
                    cmd.Parameters.Add(new SqlParameter("@logicalGroupNotIn", search.logicalGroupNotIn.ToSafeDbObject()));
                    cmd.Parameters.Add(new SqlParameter("@clusterId", string.IsNullOrEmpty(search.clusterId) ? 0 : Convert.ToInt32(search.clusterId)));

                    cmd.Parameters.Add(new SqlParameter("@from", from == null ? (object)DBNull.Value : from.Value));
                    cmd.Parameters.Add(new SqlParameter("@to", to == null ? (object)DBNull.Value : to.Value));

                    cmd.Parameters.Add(new SqlParameter("@nases", nases));
                    cmd.Parameters.Add(new SqlParameter("@users", users));

                    cmd.Parameters.Add(new SqlParameter("@fetchSubAccounts", (fetchMyNestedAccounts && (search.accountId != null)) ? 1 : 0));
                    cmd.Parameters.Add(new SqlParameter("@appMappingParams", search.appMappingParams.ToSafeDbObject()));

                    res = ResponseType.READER;
                    return cmd;
                }), new ExecuteAllResponseHandler((reader, shardId) => {

                    while (reader.Read())
                    {
                        var acc = CreateAccountObject(shardId, reader, new List<string> { "nases", "users", "applications" });
                        accounts.Add(acc);
                    }
                }), query).ExecuteAll();
            }
            return accounts;
        }

        public static Account GetAccountWithUserId(LongIdInfo longUserId, bool disableCache = false)
        {
            return GetAccountFromId(GetAccountIdFromUserId(longUserId, disableCache));
        }
        public static bool IsAgentAccount(Account acc)
        {
            return acc.applications.TryGetValue(App.LEAD_SERVICES.ToString(), out var x);
        }
        public static Account GetAccountFromId(LongIdInfo longAccountId)
        {
            if(longAccountId == null)
            {
                return null;
            }
            return  CoreCacheHelper.GetInstance().getValueFromCache(CoreCacheHelper.USER_ACCOUNT, longAccountId.ToSafeDbObject(1),  () => {
                var account = new ShardQueryExecutor<Account>(new GetSqlCommand((out ResponseType res) =>
                {
                    SqlCommand cmd = new SqlCommand(@"SELECT a.*,
					(SELECT mapped_id as nas_id, mapping_params as account_params FROM t_account_mapping1 WHERE account_id = a.id AND mapping_type = 'location' FOR JSON AUTO) AS nases,
                    (SELECT mapped_id as id, mapping_params as params FROM t_account_mapping1 WHERE account_id = a.id AND mapping_type = 'application' FOR JSON AUTO) AS applications
					FROM t_account a WHERE id=@acct_id");

                    cmd.Parameters.Add(new SqlParameter("@acct_id", longAccountId.local_value));
                    res = ResponseType.READER;
                    return cmd;
                }),longAccountId.shard_id,
                new ResponseHandler<Account>((reader) =>
                {
                    if (reader.Read())
                    {
                        return CreateAccountObject(longAccountId.shard_id, reader, new List<string> { "nases", "applications" });
                    }
                    return null;
                })).Execute();
                
                new ShardQueryExecutor<bool>(new GetSqlCommand((out ResponseType res) =>
                {
                    SqlCommand cmd = new SqlCommand(@"Select (SELECT ta.user_id, ta.username, ta.name, ta.contact_no, ta.email, b.mapping_params FROM t_admin ta
                        inner join t_account_mapping1 b on ta.user_id = b.mapped_id WHERE b.account_id = @acct_id and b.mapping_type = 'user' FOR JSON PATH) AS users");

                    cmd.Parameters.Add(new SqlParameter("@acct_id", longAccountId.local_value));
                    res = ResponseType.READER;
                    return cmd;
                }), longAccountId.shard_id,
                new ResponseHandler<bool>((reader) =>
                {
                    if (reader.Read())
                    {
                        account.options["usersJson"] = reader["users"].ToString();
                    }
                    return true;
                })).Execute();

                account.balance =  CoreDbCalls.GetInstance().FetchAccountBalance(longAccountId).Result;
                return account;
            });
        }

        public static List<ManagementUser> GetUsersInAccount(LongIdInfo longAccountId, string paramFilter = null, LongIdInfo userId = null)
        {
            if (longAccountId == null)
                return null;
            return new ShardQueryExecutor<List<ManagementUser>>(new GetSqlCommand((out ResponseType res) =>
            {
                SqlCommand cmd = new SqlCommand(@"SELECT tad.*, tam.mapping_params AS account_params FROM t_admin tad 
                        LEFT JOIN t_account_mapping1 tam on(tad.user_id = tam.mapped_id AND tam.mapping_type = 'user')
                        WHERE user_id = tam.mapped_id AND tam.account_id = @accountId 
                        AND (@param IS NULL OR mapping_params LIKE '%' + @param + '%')
                        AND (@userid = 0 OR user_id = @userid)");
                cmd.Parameters.Add(new SqlParameter("@accountId", longAccountId.local_value));
                cmd.Parameters.Add(new SqlParameter("@param", paramFilter.ToSafeDbObject()));
                cmd.Parameters.Add(new SqlParameter("@userid", userId == null? 0 : userId.ToSafeDbObject()));
                res = ResponseType.READER;
                return cmd;
            }), longAccountId.shard_id,
           new ResponseHandler<List<ManagementUser>>((reader) =>
           {
               List<ManagementUser> users = new List<ManagementUser>();
               while (reader.Read())
                   users.Add(CoreUserService.CreateManagementUser(longAccountId.shard_id, reader));
               return users;
           })).Execute();
        }

        public static int GetPartnerConfigId(LongIdInfo lcoAccountId)
        {
            var account = GetAccountFromId(lcoAccountId);
            int partnerConfigId = 0;
            if (account.applications.TryGetValue(App.WIOM_SALES.ToString(), out var str))
            {
                if (!string.IsNullOrEmpty(str))
                {
                    var jObject = JsonConvert.DeserializeObject<JObject>(str);
                    if (jObject[Constants.PARTNER_CONFIGURATION_ID] != null)
                    {
                        partnerConfigId = int.Parse(jObject[Constants.PARTNER_CONFIGURATION_ID].ToString());
                    }
                }
            }

            return partnerConfigId == 0 ? 1 : partnerConfigId;
        }

        public static JToken GetCommssionPolicy(LongIdInfo lcoAccountId, LongIdInfo userAccountId, int planCharges, bool isONU = false)
        {
            //var userAccount = CoreAccountService.GetAccountFromId(userAccountId);

            //if (userAccount != null && userAccount.applications.TryGetValue(App.HOME_ROUTER.ToString(), out var str))
            //{
            //    if (!string.IsNullOrEmpty(str))
            //    {
            //        jObject = JsonConvert.DeserializeObject<JObject>(str);
            //        if (jObject[Constants.PARTNER_CONFIGURATION_ID] != null)
            //        {
            //            int partnerConfigId = int.Parse(jObject[Constants.PARTNER_CONFIGURATION_ID].ToString());
            //            var partnerConfig = MasterDbHelper.GetPartnerConfiguration(partnerConfigId);
            //            JToken obj = isONU ? partnerConfig.onuPolicy : partnerConfig.commissionPolicy;
            //            if (obj != null)
            //            {
            //                if (obj[planCharges.ToString()] != null)
            //                    return obj[planCharges.ToString()];
            //                else if (obj["default"] != null)
            //                    return obj["default"];
            //            }
            //        }
            //    }
            //}

            JObject jObject;
            var lcoAccount = CoreAccountService.GetAccountFromId(lcoAccountId);
            if (lcoAccount.applications.TryGetValue(App.WIOM_SALES.ToString(), out var str))
            {
                if (!string.IsNullOrEmpty(str))
                {
                    jObject = JsonConvert.DeserializeObject<JObject>(str);
                    if (jObject[Constants.PARTNER_CONFIGURATION_ID] != null)
                    {
                        int partnerConfigId = int.Parse(jObject[Constants.PARTNER_CONFIGURATION_ID].ToString());
                        var partnerConfig = MasterDbHelper.GetPartnerConfiguration(partnerConfigId);
                        JToken obj = isONU ? partnerConfig.onuPolicy : partnerConfig.commissionPolicy;
                        if (obj != null)
                        {
                            if (obj[planCharges.ToString()] != null)
                                return obj[planCharges.ToString()];
                            else if (obj["default"] != null)
                                return obj["default"];
                        }
                    }
                }
            }

            return null;
        }

        public static double CalculateFixedCommission(LongIdInfo lcoAccountId, LongIdInfo userAccountId, int planCharges, int currentExpiryIndex, JToken commissionObj = null) 
        {
            string commissionStr;
            if(commissionObj == null)
                commissionObj = GetCommssionPolicy(lcoAccountId, userAccountId, planCharges);
            if(commissionObj == null)
            {
                return 0;
            }
            if (commissionObj["fixed"].Count() > currentExpiryIndex)
                commissionStr = commissionObj["fixed"][currentExpiryIndex].ToString();
            else
                commissionStr = commissionObj["fixed"].Last.ToString();

            double commission = 0;
            if (commissionStr.EndsWith('%'))
                commission = double.Parse(commissionStr.Substring(0, commissionStr.Length - 1)) * ((double)planCharges) / 100;
            else
                commission = double.Parse(commissionStr);

            return commission;
        }

        public static double CalculateIncentive(LongIdInfo lcoAccountId, LongIdInfo userAccountId, int planCharges, int monthNumber, out double incentiveWithoutBonus, JToken commissionObj = null)
        {
            double rating = GetPartnerRatings(lcoAccountId);
            double ratingWithoutBonus = GetPartnerRatingsWithoutBonus(lcoAccountId);
            Logger.GetInstance().Info("CalculateIncentive lcoAccountId: " + lcoAccountId.GetLongId() + "rating: " + rating + "ratingWithoutBonus " + ratingWithoutBonus + "difference: " + (rating - ratingWithoutBonus));
            incentiveWithoutBonus = 0;
            if (commissionObj == null)
                commissionObj = GetCommssionPolicy(lcoAccountId, userAccountId, planCharges);
            if (rating >= 4)
            {
                string ratingMin;
                if (commissionObj["incentiveMin"].Count() > monthNumber)
                    ratingMin = commissionObj["incentiveMin"][monthNumber].ToString();
                else
                    ratingMin = commissionObj["incentiveMin"].Last.ToString();

                string ratingMax;
                if (commissionObj["incentiveMax"].Count() > monthNumber)
                    ratingMax = commissionObj["incentiveMax"][monthNumber].ToString();
                else
                    ratingMax = commissionObj["incentiveMax"].Last.ToString();

                double min = 0;
                if (ratingMin.EndsWith('%'))
                    min = double.Parse(ratingMin.Substring(0, ratingMin.Length - 1)) * ((double)planCharges) / 100;
                else
                    min = double.Parse(ratingMin);

                double max = 0;
                if (ratingMax.EndsWith('%'))
                    max = double.Parse(ratingMax.Substring(0, ratingMax.Length - 1)) * ((double)planCharges) / 100;
                else
                    max = double.Parse(ratingMax);

                double incentive = Math.Floor((min + (rating - 4) * (max - min)) * 100) / 100;
                if(ratingWithoutBonus >= 4)
                    incentiveWithoutBonus = Math.Floor((min + (ratingWithoutBonus - 4) * (max - min)) * 100) / 100;
                return incentive;
            }
            return 0;
        }

        public static KeyValuePair<double,double> CalculateCommission(LongIdInfo lcoAccountId, LongIdInfo userAccountId, DateTime firstRechargeTime, int planCharges, bool includeRatingIncentive, out double ratingIncentiveWithoutBonus) 
        {
            double commission = 0;
            double incentive = 0;
            ratingIncentiveWithoutBonus = 0;
            if (planCharges != 0 && lcoAccountId.local_value > 0)
            {
                int currentExpiryIndex = CalculateNumberOfRenewalMonths(firstRechargeTime);
                var commissionObj = GetCommssionPolicy(lcoAccountId, userAccountId, planCharges);
                commission = CalculateFixedCommission(lcoAccountId, userAccountId, planCharges, currentExpiryIndex, commissionObj);
                if (includeRatingIncentive)
                    incentive = CalculateIncentive(lcoAccountId, userAccountId, planCharges, currentExpiryIndex, out ratingIncentiveWithoutBonus, commissionObj);
            }
            return new KeyValuePair<double, double>(commission, incentive);
        }

        public static int CalculateNumberOfRenewalMonths(DateTime firstRechargeTime)
        {
            return Convert.ToInt32((DateTime.UtcNow.AddDays(10) - firstRechargeTime).TotalSeconds / (28 * Constants.SECONDS_IN_DAY));
        }

        public static WgStatus InsertRenewalAndInterestV1(LongIdInfo longNasId, string userMobile, LongIdInfo longAccountId, out Dictionary<string, object> wgPolicy, out HomeRouterPlan homeRouterPlan, out PDOPlan currentPlan, long appliedPlanId = 0, bool advancePay = false, string connectionType="fiber")
        {
            Logger.GetInstance().Info($"CoreAccountService:InsertRenewalAndInterestV1 called with longNasId: {longNasId.local_value}, userMobile: {userMobile}, longAccountId: {longAccountId}");
            int due_days = -2;
            int overdue_int_days = 0;//dynamics depend upon buffer balance of user i.e how many times user pay amount on time
            int disconnection_days = I2e1ConfigurationManager.DISCONNECTION_TIME_DAYS;
            int interest_perday = I2e1ConfigurationManager.RESTART_FEE;
            int maxRestart_fee = (disconnection_days) * interest_perday;
            int restart_membership_benefit_time = I2e1ConfigurationManager.RESTART_FEE_EXEMPTION_TIME_DAYS;
            int mandateDiscount = Constants.SUBSCRIPTION_DISCOUNT;
            homeRouterPlan = null;
            wgPolicy = new Dictionary<string, object>();
            currentPlan = null;
            bool billingStopped = false;
            bool firstRecharge = false;
            bool wiom_member_status = false;
            DateTime wiomMemberCreateDate = DateTime.UtcNow;
            bool internetDisconnected = false;
            bool userMandateStatus = false;
            double advancePendingAmount = 0;
            long revenueId = 0;
            int billPaymentEnableDays = 10;
            WgStatus status = WgStatus.PAID;
            DateTime lastPingTime = DateTime.UtcNow;
            if (longAccountId.local_value != 0)
            {
                var userAccount = GetAccountFromId(longAccountId);
                userMandateStatus = GetAccountMandateInfo(longAccountId) != null ? GetAccountMandateInfo(longAccountId).status == "ACTIVE" : false;
                string extra = null;
                userAccount.applications.TryGetValue("HOME_ROUTER", out extra);
                if (!String.IsNullOrEmpty(extra))
                {
                    JObject json = JObject.Parse(extra);
                    if (json["wiom_member"] != null && ((DateTime)json["expiry_date"]) >= DateTime.UtcNow)
                    {
                        wiom_member_status = (bool)json["wiom_member"];
                        wiomMemberCreateDate = (DateTime)json["create_date"];
                    }
                }

                if (userAccount.options["usersJson"] != null)
                {
                    var users = JsonConvert.DeserializeObject<JArray>(userAccount.options["usersJson"]);
                    if (users.Count > 0)
                        wgPolicy.Add("name", users[0]["name"]);
                }
                wgPolicy.Add("connection", connectionType);
                if (userAccount.options["nasesJson"] != null)
                {
                    var nases = JsonConvert.DeserializeObject<JArray>(userAccount.options["nasesJson"]);
                    if (nases.Count > 0)
                    {
                        var accountParamsStr = nases[0]["account_params"];
                        if(accountParamsStr != null)
                        {
                            JObject accountParams = JsonConvert.DeserializeObject<JObject>(accountParamsStr.ToString());

                            if(accountParams["connection"] != null)
                                wgPolicy["connection"] = accountParams["connection"];
                        }
                    }
                }

                wgPolicy.Add("due_days", due_days);
                wgPolicy.Add("disconnection_days", disconnection_days);
                wgPolicy.Add("interest_perday", interest_perday);
                wgPolicy.Add("wiom_member", I2e1ConfigurationManager.MEMBERSHIP_FEE);
                wgPolicy.Add("wiom_member_status", wiom_member_status); 
                wgPolicy.Add("cash_handling_fee", I2e1ConfigurationManager.CASH_HANDLING_FEE);
                wgPolicy.Add("pm_wani_discount", Constants.PM_WANI_DISCOUNT);
                wgPolicy.Add("mandateDiscount", mandateDiscount);
                wgPolicy.Add("userMandateStatus", userMandateStatus);
                wgPolicy.Add("mandateFeatureEnable", !userMandateStatus ? GetMandateFeatureStatus() : false);
                wgPolicy.Add("billPaymentEnableDays", billPaymentEnableDays);
                
                int pdoAmount = 0, routerRent = 0, interest = 0;
                var nasDetails = ShardHelper.GetNasDetailsFromLongNas(longNasId, true);

                wgPolicy.Add("deviceId", nasDetails != null ? nasDetails.deviceId : null);

                if (!CoreDbCalls.GetInstance().GetPlanExpiryByDeviceLimit(longNasId, Constants.HOME_ROUTER_DEVICE_LIMIT, out var mobile, out var firstRechargeTime, out var expiryTime, out homeRouterPlan))
                {
                    expiryTime = DateTime.UtcNow;
                    firstRechargeTime = DateTime.UtcNow;
                    firstRecharge = true;
                }
                else
                {
                    if(nasDetails == null)
                    {
                        BasicEmailSender.SendRawEmail("<EMAIL>", Constants.failureEmailList, "Nas Detected without device", $"{longNasId} nas found without matching device. Kindly check", null, null);
                        return WgStatus.REMOVED;
                    }
                }
                lastPingTime = expiryTime;
                var renewalDate = expiryTime.AddDays(due_days);
                var currentTime = DateTime.UtcNow;
                DateTime nextExecutionDate = expiryTime.AddDays(-3).Date;
                int attemptCount = 0;


                if (renewalDate < currentTime)
                {
                    status = WgStatus.DUE_ON;
                    if (expiryTime < currentTime)
                    {
                        status = WgStatus.OVERDUE_INTEREST;
                        DateTime minTimeToStartRestartFee = expiryTime.AddDays(6);
                        if (minTimeToStartRestartFee < currentTime)
                        {
                            lastPingTime = CoreDbCalls.GetInstance().GetLastPingTime(longNasId);
                            if (lastPingTime < minTimeToStartRestartFee)
                                lastPingTime = minTimeToStartRestartFee;

                            int daysToStartFee = I2e1ConfigurationManager.STOP_INTERNET_AFTER_DAYS + 2 -1;
                            DateTime maxTimeToStartRestartFee = expiryTime.AddDays(daysToStartFee);
                            if (lastPingTime > maxTimeToStartRestartFee)
                                lastPingTime = maxTimeToStartRestartFee;

                            internetDisconnected = expiryTime.AddDays(1) <= DateTime.UtcNow;
                            if (internetDisconnected)
                            {
                                overdue_int_days = (DateTime.UtcNow.Date - lastPingTime.Date).Days > 0 ? (DateTime.UtcNow.Date - lastPingTime.Date).Days : 0;
                            }
                        }
                    }
                    var plans = CoreCacheHelper.GetInstance().GetActivePlansOnNas(longNasId);
                    if (plans.Count > 0)
                    {
                        currentPlan = appliedPlanId == 0 ? plans[0] : plans.Find(m => m.id == appliedPlanId);
                        int waiver = 0;
                        if (internetDisconnected)
                        {
                            int actualRestartFee = actualRestartFee = overdue_int_days * interest_perday >= maxRestart_fee ? maxRestart_fee : overdue_int_days * interest_perday;
                            if (!wiom_member_status)
                            {
                                interest = actualRestartFee;
                                if (overdue_int_days +7-1 > disconnection_days)
                                    status = WgStatus.DISCONNECTED;
                                else
                                    status = WgStatus.OVERDUE_INTEREST;
                            }
                            else
                            {
                                int overdueDaysAfterWiomMember = Math.Min(overdue_int_days, (DateTime.UtcNow.Date - wiomMemberCreateDate.Date).Days);
                                if(overdueDaysAfterWiomMember > restart_membership_benefit_time)
                                    interest = (overdueDaysAfterWiomMember - restart_membership_benefit_time) * interest_perday >= maxRestart_fee ? maxRestart_fee : (overdueDaysAfterWiomMember - restart_membership_benefit_time) * interest_perday;
                                
                                if (overdueDaysAfterWiomMember +7 -1 > (restart_membership_benefit_time + disconnection_days))
                                    status = WgStatus.DISCONNECTED;
                                else 
                                    status = WgStatus.OVERDUE_INTEREST;
                            }
                            revenueId = InsertBilling(new Billing()
                            {
                                accountId = longAccountId,
                                nasid = longNasId,
                                mobile = userMobile,
                                address = "315/274 Westend Marg",
                                product = "HOME_ROUTER",
                                remark = "RENEWAL",
                                quantity = 1,
                                dueDate = renewalDate.AddDays(-1 * due_days),
                                extraData = new JObject() { { "wiomMember", wiom_member_status }, { "restartFee", interest }, { "actualRestartFee", actualRestartFee }, { "status", status.ToString() } }
                            }, currentPlan.price + interest - currentPlan.discount, out waiver);
                            pdoAmount += (int)(currentPlan.price - 200 - waiver);
                            routerRent += 200;
                            renewalDate = DateTime.UtcNow.AddSeconds(currentPlan.time_limit);

                        }
                        else
                        {
                            if (!billingStopped && currentTime > renewalDate)
                            {
                                if (!firstRecharge)
                                {
                                    //Inserting bill for 1 month
                                    revenueId = InsertBilling(new Billing()
                                    {
                                        accountId = longAccountId,
                                        nasid = longNasId,
                                        mobile = userMobile,
                                        address = "315/274 Westend Marg",
                                        product = "HOME_ROUTER",
                                        remark = "RENEWAL",
                                        quantity = 1,
                                        dueDate = renewalDate.AddDays(-1 * due_days),
                                        extraData = new JObject() { { "wiomMember", wiom_member_status }, { "status", status.ToString() } }
                                    }, currentPlan.price - currentPlan.discount, out waiver);
                                }

                                pdoAmount += (int)(currentPlan.price - 200 - waiver);
                                routerRent += 200;
                            }
                        }
                        wgPolicy.Add("timeLimit", currentPlan.time_limit);
                        wgPolicy.Add("planId", currentPlan.id);
                        
                    }
                }
                else if(advancePay)
                {
                    advancePayInsertBill(longAccountId, longNasId, userMobile, renewalDate, due_days, status, out revenueId, ref advancePendingAmount,ref currentPlan, wiom_member_status, appliedPlanId);
                }
                if(userMandateStatus)
                {
                    if(CheckAutopayExecutionState(status, expiryTime))
                    {
                        MPaymentHistory lastAutopayTransaction = GetLastFailedMandateWiomBillingEntry(longNasId);
                    
                        if (lastAutopayTransaction != null)
                        {
                            TimeSpan difference = DateTime.UtcNow.Date.Subtract(lastAutopayTransaction.createDate.Date);
                            if (difference.TotalDays == 0)
                                nextExecutionDate = DateTime.UtcNow.AddDays(2).Date;
                            if (difference.TotalDays == 1)
                                nextExecutionDate = DateTime.UtcNow.AddDays(1).Date;
                            if (difference.TotalDays == 2)
                                nextExecutionDate = DateTime.UtcNow.Date;
                            TimeSpan timeDiff = DateTime.UtcNow.Date.Subtract(expiryTime.AddDays(-3).Date);
                            attemptCount = ((int)Math.Abs(timeDiff.TotalDays)+1)/2;
                        }
                    }
                    else
                    {
                        if (status != WgStatus.PAID)
                            attemptCount = 3;
                    }
                    wgPolicy.Add("autopay_next_execution_date", nextExecutionDate);
                    wgPolicy.Add("autopay_attempt_count", attemptCount);
                }

                int pendingAmount = pdoAmount + routerRent + interest;
                if (pendingAmount == 0 && advancePay)
                    pendingAmount = (int)advancePendingAmount;
                int walletBalance = (int)userAccount.balance;
                wgPolicy.Add("overdue_int_days", overdue_int_days);
                wgPolicy.Add("accountId", longAccountId.ToSafeDbObject(1));
                wgPolicy.Add("mobile", userMobile);
                wgPolicy.Add("nasid", longNasId.ToSafeDbObject(1));
                wgPolicy.Add("expiryTime", expiryTime);
                wgPolicy.Add("firstRechargeTime", firstRechargeTime);

                wgPolicy.Add("pendingAmount", pendingAmount);
                wgPolicy.Add("pdoAmount", pdoAmount);
                wgPolicy.Add("routerRent", routerRent);
                wgPolicy.Add("interest", interest);

                wgPolicy.Add("walletBalance", walletBalance);
                wgPolicy.Add("revenueId", revenueId); 
                wgPolicy.Add("lastPingTime", lastPingTime);
            }

            return status;
        }

        public static void advancePayInsertBill(LongIdInfo longAccountId, LongIdInfo longNasId, string userMobile, DateTime renewalDate, int due_days, WgStatus status, out long revenueId, ref double pendingAmount, ref PDOPlan currentPlan, bool wiom_member_status = false, long appliedPlanId = 0)
        {
            Logger.GetInstance().Info($"CoreAccountService:advancePayInsertBill called with longNasId: {longNasId.local_value}, userMobile: {userMobile}, longAccountId: {longAccountId}");
            var plans = CoreCacheHelper.GetInstance().GetActivePlansOnNas(longNasId);
            long localRevenueId = 0;
            if (plans.Count>0)
            {
                int waiver = 0;
                currentPlan = appliedPlanId == 0 ? plans[0] : plans.Find(m => m.id == appliedPlanId);
                localRevenueId = InsertBilling(new Billing()
                {
                    accountId = longAccountId,
                    nasid = longNasId,
                    mobile = userMobile,
                    address = "315/274 Westend Marg",
                    product = "HOME_ROUTER",
                    remark = "RENEWAL",
                    quantity = 1,
                    dueDate = renewalDate.AddDays(-1 * due_days),
                    extraData = new JObject() { { "wiomMember", wiom_member_status }, { "restartFee", 0 }, { "actualRestartFee", 0 }, { "status", status.ToString() }, { "advancePay", true } }
                }, currentPlan.price - currentPlan.discount, out waiver);
                pendingAmount = currentPlan.price - waiver;
            }


            revenueId = localRevenueId;
        }
        public static long InsertBilling(Billing billing, double totalPrice, out int discount)
        {
            var breakUp = GetMonthlyBreakup(totalPrice, 0);
            int localDiscount = 0;
            long id = new ShardQueryExecutor<long>(new GetSqlCommand((out ResponseType res) =>
            {
                SqlCommand cmd = new SqlCommand(@"
                DECLARE @id bigint;
                DECLARE @discount int;
                SELECT top 1 @id = id, @discount = discount FROM t_revenue where account_id = @account_id and product = @product and remark=@remark and due_on = @due_on order by id desc
                IF @id IS NULL
                BEGIN
                    INSERT INTO t_revenue(account_id, nasid, mobile, product, remark, price, gst, due_on, paid, paid_on, payment_mode, transaction_id, extra_data)
                    VALUES(@account_id, @nasid, @mobile, @product, @remark, @price, @gst, @due_on, @paid, @paid_on, @payment_mode, @transaction_id, @extra_data)
                    SET @id = SCOPE_IDENTITY()
                    SET @discount = 0
                END
                ELSE
                BEGIN
                    UPDATE t_revenue
                    SET price = @price,
                    gst = @gst,
                    extra_data = COALESCE(@extra_data, extra_data)
                    WHERE id = @id
                END
                SELECT @id as id , @discount as discount
                ");

                cmd.Parameters.Add(new SqlParameter("@account_id", billing.accountId.local_value));
                cmd.Parameters.Add(new SqlParameter("@nasid", billing.nasid.local_value));
                cmd.Parameters.Add(new SqlParameter("@mobile", billing.mobile));
                cmd.Parameters.Add(new SqlParameter("@product", billing.product));
                cmd.Parameters.Add(new SqlParameter("@remark", CoreUtil.ToSafeDbObject(billing.remark)));
                cmd.Parameters.Add(new SqlParameter("@price", breakUp["mainCharges"]));
                cmd.Parameters.Add(new SqlParameter("@gst", breakUp["sgst"] + breakUp["sgst"]));
                cmd.Parameters.Add(new SqlParameter("@due_on", billing.dueDate.Date.Add(new TimeSpan(11, 30, 0))));
                cmd.Parameters.Add(new SqlParameter("@paid", billing.paid));
                cmd.Parameters.Add(new SqlParameter("@paid_on", CoreUtil.ToSafeDbObject(billing.paidOn)));
                cmd.Parameters.Add(new SqlParameter("@payment_mode", CoreUtil.ToSafeDbObject(billing.paymentMode)));
                cmd.Parameters.Add(new SqlParameter("@transaction_id", CoreUtil.ToSafeDbObject(billing.transactionId)));
                cmd.Parameters.Add(new SqlParameter("@extra_data", billing.extraData == null ? "{}" : JsonConvert.SerializeObject(billing.extraData)));
                res = ResponseType.READER;
                return cmd;
            }), billing.accountId.shard_id,
            new ResponseHandler<long>((reader) =>
            {
                if (reader.Read())
                {
                    localDiscount = (int)reader["discount"];
                    return (long)reader["id"];
                }
                return 0;
            })).Execute();

            discount = localDiscount;
            return id;
        }

        public static DateTime MarkBillPaid(long revenueId, LongIdInfo longAccountId, DateTime dueOn, string product, string transactionId, string paymentMode, double totalPrice)
        {
            var breakUp = GetMonthlyBreakup(totalPrice, 0);
            return new ShardQueryExecutor<DateTime>(new GetSqlCommand((out ResponseType res) =>
            {
                SqlCommand cmd = new SqlCommand(@"UPDATE t_revenue set paid = 1, transaction_id = @transaction_id, 
                    paid_on = GETUTCDATE(), payment_mode = @payment_mode, price = @price, gst = @gst
                    OUTPUT inserted.due_on, inserted.remark
                    WHERE (@revenue_id = 0 OR id = @revenue_id) and (@revenue_id != 0 OR (account_id = @account_id and product = @product and due_on >= @due_on))");
                cmd.Parameters.Add(new SqlParameter("@revenue_id", revenueId));
                cmd.Parameters.Add(new SqlParameter("@account_id", longAccountId.local_value));
                cmd.Parameters.Add(new SqlParameter("@due_on", dueOn.Date.Add(new TimeSpan(11, 30, 0))));
                cmd.Parameters.Add(new SqlParameter("@transaction_id", transactionId));
                cmd.Parameters.Add(new SqlParameter("@product", product));
                cmd.Parameters.Add(new SqlParameter("@payment_mode", paymentMode));
                cmd.Parameters.Add(new SqlParameter("@price", breakUp["mainCharges"]));
                cmd.Parameters.Add(new SqlParameter("@gst", breakUp["sgst"] + breakUp["sgst"]));
                res = ResponseType.READER;
                return cmd;
            }), longAccountId.shard_id,
            new ResponseHandler<DateTime>((reader) =>
            {
                if (reader.Read())
                {
                    return (DateTime)reader["due_on"];
                }
                return DateTime.UtcNow;
            })).Execute();
        }

        public static List<Billing> GetBilling(LongIdInfo longAccountId, string product, SearchQuery query)
        {
            query.setIfEmpty("bill.id", "desc");
            return new PaginatedShardQueryExecutor<List<Billing>>(new GetSqlCommand((out ResponseType res) =>
            {
                SqlCommand cmd = new SqlCommand(@"SELECT bill.*, ts.shop_name, ts.shop_address, ts.loc_start_date  FROM t_revenue bill
                    LEFT JOIN v_account_location tam ON tam.account_id = bill.account_id
                    LEFT JOIN t_store ts ON tam.nasid = ts.router_nas_id
                    WHERE bill.account_id = @account_id AND bill.product = @product");
                cmd.Parameters.Add(new SqlParameter("@account_id", longAccountId.local_value));
                cmd.Parameters.Add(new SqlParameter("@product", product));
                res = ResponseType.READER;
                return cmd;
            }), longAccountId.shard_id,
            new ResponseHandler<List<Billing>>((reader) =>
            {
                var list = new List<Billing>();
                while (reader.Read())
                {
                    var extraData = reader["extra_data"].ToString();
                    var bill = new Billing()
                    {
                        id = (long)reader["id"],
                        accountId = longAccountId,
                        transactionId = reader["transaction_id"].ToString(),
                        mobile = reader["mobile"].ToString(),
                        name = reader["shop_name"].ToString(),
                        address = reader["shop_address"].ToString(),
                        product = reader["product"].ToString(),
                        quantity = 1,
                        price = (double)reader["price"],
                        gst = (double)reader["gst"],
                        extraData = string.IsNullOrEmpty(extraData) ? null : JsonConvert.DeserializeObject<JObject>(extraData),
                        addedTime = (DateTime)reader["added_time"],
                        dueDate = (DateTime)reader["due_on"],
                        paid = (bool)reader["paid"]
                    };

                    if (reader["paid_on"] != DBNull.Value)
                        bill.paidOn = (DateTime)reader["paid_on"];

                    list.Add(bill);
                }
                return list;
            }), query).Execute();
        }

        public static List<MPaymentHistory> GetBookingTransactions(String mobile)
        {
            ModelDynamoDb<MPaymentHistory> modelDynamoDb = new ModelDynamoDb<MPaymentHistory>();
            return modelDynamoDb.GetAllById<string, string>(mobile, null).ToList();
        }

        public static List<MPaymentHistory> GetAllWiomWifiTransaction(LongIdInfo longNasId)
        {
            var list = new List<MPaymentHistory>();
            List<PDOPlan> pDOPlans = CoreCacheHelper.GetInstance().GetActivePlansOnNas(longNasId);
            Dictionary<long,long> planDict = new Dictionary<long,long>();
            Dictionary<long, long> planDictAmount = new Dictionary<long, long>();
            pDOPlans.ForEach(x => {
                planDict[x.id] = x.time_limit;
                planDictAmount[(long)x.price - (long)x.discount] = x.time_limit;
            });
            SearchQuery query = new SearchQuery();
            query.setIfEmpty("bill_id", "desc");

            ModelDynamoDb<MPaymentHistory> modelDynamoDb = new ModelDynamoDb<MPaymentHistory>();
            string mobile = CoreDbCalls.GetInstance().GetLastMobileOnHomeRouter(longNasId);
            if(!String.IsNullOrEmpty(mobile))
            {
                IEnumerable<MPaymentHistory> paymentHistory = modelDynamoDb.GetAllById<string,string>(mobile, null);
                list.AddRange(paymentHistory);
                query = new SearchQuery();
                query.setIfEmpty("id", "desc");
                new PaginatedShardQueryExecutor<bool>(new GetSqlCommand((out ResponseType res) =>
                {
                    SqlCommand cmd = new SqlCommand(@"select * from t_revenue where nasid =  @nasId and paid = 1 and (payment_mode = 'reseller' or (payment_mode = 'cash' and added_time <= '2024-01-14')
                                        or (remark='RESET_FEE' and(payment_mode='wallet' or payment_mode='online') and transaction_id not like 'WIFI_SRVC%')) ");
                    cmd.Parameters.Add(new SqlParameter("@nasId", longNasId.ToSafeDbObject()));
                    res = ResponseType.READER;
                    return cmd;
                }), longNasId.shard_id,
                new ResponseHandler<bool>((reader) =>
                {
                    while (reader.Read())
                    {
                        var pdata = new MPaymentHistory()
                        {
                            billId = Convert.ToInt32(reader["id"].ToString()),
                            paymentType = PaymentType.WIFI_RECHARGE,
                            transactionId = reader["transaction_id"].ToString(),
                            couponCode = "",
                            discount = Convert.ToDouble(reader["discount"]),
                            payableAmount = Convert.ToInt32((double)reader["price"] + (double)reader["gst"]),
                            createDate = (DateTime)reader["paid_on"],
                            paymentStatus = reader["paid"] == DBNull.Value ? null : (bool)reader["paid"] ? 1 : 0,
                            planId = 0,
                            extraParam = reader["extra_data"].ToString(),
                            nasid = reader["nasid"] == DBNull.Value ? null : LongIdInfo.IdParser(Convert.ToInt64(reader["nasid"])),
                            accountId = reader["account_id"] == DBNull.Value ? null : LongIdInfo.IdParser(Convert.ToInt64(reader["account_id"]))
                        };
                        pdata.source = reader["payment_mode"] == DBNull.Value ? null : reader["payment_mode"].ToString();
                        JObject jObject = JsonConvert.DeserializeObject<JObject>(pdata.extraParam);
                        if (jObject != null)
                        {
                            jObject["time_limit"] = 28 * 86400;
                            jObject["plan_start_time"] = (DateTime)reader["due_on"];
                            pdata.extraParam = jObject.ToString();
                        }
                        list.Add(pdata);
                    }
                    return true;
                }), query).Execute();

                return list.OrderByDescending(x => x.createDate).ToList();
            }
            return null;
        }
        protected static Account CreateAccountObject(long shardId, SqlDataReader reader, List<string> fields)
        {
            var options = new Dictionary<string, string>();
            var apps = new Dictionary<string, string>();

            if (fields.Contains("nases"))
            {
                var jObject = JsonConvert.DeserializeObject<JArray>(reader["nases"].ToString());
                if(jObject != null && jObject.Count > 0)
                {
                    jObject[0]["nas_id"] = new LongIdInfo(shardId, DBObjectType.ACTIVE_NAS, Convert.ToInt64(jObject[0]["nas_id"])).GetLongId();
                    options["nasesJson"] = JsonConvert.SerializeObject(jObject);
                }
            }
            if (fields.Contains("users"))
                options["usersJson"] = reader["users"].ToString();
                
            if (fields.Contains("applications"))
            {
                try
                {
                    JArray appsArray = JsonConvert.DeserializeObject<JArray>(reader["applications"].ToString());

                    if(appsArray != null)
                    {
                        for (var i = 0; i < appsArray.Count; i++)
                        {
                            var k = (App)Convert.ToInt32(appsArray[i]["id"]);
                            if (appsArray[i]["params"] != null)
                                apps.Add(k.ToString(), appsArray[i]["params"].ToString());
                            else
                                apps.Add(k.ToString(), "{}");
                        }
                    } 
                }
                catch (Exception ex)
                {
                }
            }

            var acct = new Account()
            {
                name = reader["name"].ToString(),
                address = reader["address"].ToString(),
                googleAddressId = new LongIdInfo(shardId, DBObjectType.ADDRESS_TYPE, Convert.ToInt16(reader["google_address_id"])),
                gst = reader["gst"].ToString(),
                logicalGroup = reader["logical_group"].ToString(),
                extraData = reader["extra_data"] == null ? String.Empty : reader["extra_data"].ToString(),
                id = new LongIdInfo(shardId, DBObjectType.ACCOUNT_TYPE, Convert.ToInt64(reader["id"])),
                addedTime = (DateTime)reader["added_time"],
                balance = reader.HasColumn("balance") ? Convert.ToDouble(reader["balance"]) : 0,
                applications = apps,
                options = options
            };
            try
            {
                acct.extraDataObject = JsonConvert.DeserializeObject<AccountExtraData>(acct.extraData);
            }
            catch (Exception ex)
            {
                Logger.GetInstance().Error("Error while reading account: " + acct.id + " " + ex.Message);
            }

            return acct;
        }

        public static double GetPartnerRatings(LongIdInfo longAccountId)
        {
            Dictionary<int, int> ratingMap = GetRatingDistribution(longAccountId);
            int ratings = 0;
            int raters = 0;
            foreach (KeyValuePair<int, int> p in ratingMap)
            {
                int x = p.Key;
                int y = p.Value;
                ratings += p.Key * p.Value;
                raters += y;
            }

            return Math.Round(((Double)ratings / raters), 2);
        }

        public static double GetPartnerRatingsWithoutBonus(LongIdInfo accountId)
        {
            Dictionary<int, int> ratingMap = GetRatingDistribution(accountId, false);
            int ratings = 0;
            int raters = 0;
            foreach (KeyValuePair<int, int> p in ratingMap)
            {
                int x = p.Key;
                int y = p.Value;
                ratings += p.Key * p.Value;
                raters += y;
            }

            return (Double)ratings/raters;
        }

        public static object GetRatingsWithBonus(LongIdInfo longaccountId)
        {
            long accountId = longaccountId.local_value;
            return new ShardQueryExecutor<object>(new GetSqlCommand((out ResponseType res) =>
            {
                SqlCommand cmd = new SqlCommand("select count(*) as _cnt from va_rating where receiver = @account_id and bonus = 1");
                cmd.Parameters.Add(new SqlParameter("@account_id", accountId));
                res = ResponseType.READER;
                return cmd;
            }), longaccountId.shard_id,
            new ResponseHandler<object>((reader) =>
            {
                if(reader.Read())
                    return reader["_cnt"];
                return 0;
            })).Execute();
        }

        public static Dictionary<int, int> GetRatingDistribution(LongIdInfo longAccountId, bool addBonus = true)
        {
            string cacheKey = addBonus ? "PARTNER_RATING_" : "PARTNER_RATING_WITHOUT_BONUS_";
            return CoreCacheHelper.GetInstance().getValueFromCache<Dictionary<int, int>>(cacheKey, longAccountId.local_value, () => {
                return new ShardQueryExecutor<Dictionary<int, int>>(new GetSqlCommand((out ResponseType res) =>
                {
                    SqlCommand cmd = new SqlCommand("get_partner_rating_v1");
                    cmd.CommandType = CommandType.StoredProcedure;


                    var ratingVariables = new Dictionary<string, int>()
                    {
                        { "new_partner_benefit_till_hr", 48 },
                        { "sr_rslv_tat_min", I2e1ConfigurationManager.TICKET_RESOLUTION_TIME_FOR_BONUS_MIN },
                        { "sr_default_rating_after_min", 48*60 },
                        { "instl_rslv_tat_min", I2e1ConfigurationManager.LEAD_INSTALL_TIME_FOR_BONUS_MIN },
                        { "instl_default_rating_after_min", 48*60 },
                    };

                    cmd.Parameters.Add(new SqlParameter("@partner_id", longAccountId.local_value));
                    cmd.Parameters.Add(new SqlParameter("@new_partner_benefit_till_hr", ratingVariables["new_partner_benefit_till_hr"]));

                    cmd.Parameters.Add(new SqlParameter("@sr_rslv_tat_min", ratingVariables["sr_rslv_tat_min"]));
                    cmd.Parameters.Add(new SqlParameter("@sr_default_rating_after_min", ratingVariables["sr_default_rating_after_min"]));

                    cmd.Parameters.Add(new SqlParameter("@instl_rslv_tat_min", ratingVariables["instl_rslv_tat_min"]));
                    cmd.Parameters.Add(new SqlParameter("@instl_default_rating_after_min", ratingVariables["instl_default_rating_after_min"]));

                    cmd.Parameters.Add(new SqlParameter("@bonus_rating", addBonus ? 1 : 0));

                    res = ResponseType.READER;
                    return cmd;
                }), longAccountId.shard_id,
                new ResponseHandler<Dictionary<int, int>>((reader) =>
                {
                    Dictionary<int, int> ratingMap = new Dictionary<int, int>();
                    while (reader.Read())
                        ratingMap.Add((int)reader["final_rating"], (int)reader["_cnt"]);
                    return ratingMap;
                })).Execute();
            });
        }

        public static double GetAveragePartnerRating()
        {
            string cacheKey = "AVERAGE_PARTNER_RATING";
            return CoreCacheHelper.GetInstance().getValueFromCache<double>(cacheKey, "0", () => {
                double avgRating = 0;
                int totalShards = 0;
                new ShardQueryExecutor<double>(new GetSqlCommand((out ResponseType res) =>
                {
                    SqlCommand cmd = new SqlCommand(@"
                        SELECT Round(avg(cast(avg_rating as float)), 2) as avg_rating FROM (
                            SELECT avg(cast(final_rating as float)) AS avg_rating, receiver, ta.name name FROM (
                                SELECT *,
                                CASE
                                    WHEN ratable_type = 'tkt' AND rating IS NULL AND DATEDIFF(MINUTE, added_time, GETUTCDATE()) <= @sr_default_rating_after_min THEN 0
                                    WHEN ratable_type = 'tkt' AND rating IS NULL AND CAST(JSON_VALUE(params, '$.rslvd_min') AS FLOAT) <= @sr_rslv_tat_min THEN 5
                                    WHEN ratable_type = 'tkt' AND rating IS NOT NULL AND CAST(JSON_VALUE(params, '$.rslvd_min') AS FLOAT) <= @sr_rslv_tat_min AND rating < 4 THEN rating + @bonus_rating
                                    WHEN ratable_type = 'tkt' AND rating IS NOT NULL AND CAST(JSON_VALUE(params, '$.rslvd_min') AS FLOAT) <= @sr_rslv_tat_min THEN 5
                                    WHEN ratable_type = 'tkt' AND rating IS NULL THEN 4                
                                    WHEN ratable_type = 'location' AND rating IS NULL AND DATEDIFF(MINUTE, added_time, GETUTCDATE()) <= @instl_default_rating_after_min THEN 0
                                    WHEN ratable_type = 'location' AND rating IS NULL AND CAST(JSON_VALUE(params, '$.rslvd_min') AS FLOAT) <= @instl_rslv_tat_min THEN 5
                                    WHEN ratable_type = 'location' AND rating IS NOT NULL AND CAST(JSON_VALUE(params, '$.rslvd_min') AS FLOAT) <= @instl_rslv_tat_min AND rating < 4 THEN rating + @bonus_rating
                                    WHEN ratable_type = 'location' AND rating IS NOT NULL AND CAST(JSON_VALUE(params, '$.rslvd_min') AS FLOAT) <= @instl_rslv_tat_min THEN 5
                                    WHEN ratable_type = 'location' AND rating IS NULL THEN 4
                                    ELSE rating
                                END AS final_rating,
                                ROW_NUMBER() OVER (PARTITION BY receiver Order by added_time DESC) AS rnk
                                FROM t_rating WHERE receiver_type = 'partner'
                            ) x
                            left join t_account ta on x.receiver = ta.id
                            where rnk <= 500 and final_rating > 0 and name not like '%home_router%' group by receiver, ta.name
                        ) y"
                    );

                    var ratingVariables = new Dictionary<string, int>()
                    {
                        { "new_partner_benefit_till_hr", 48 },
                        { "sr_rslv_tat_min", I2e1ConfigurationManager.TICKET_RESOLUTION_TIME_FOR_BONUS_MIN },
                        { "sr_default_rating_after_min", 48*60 },
                        { "instl_rslv_tat_min", I2e1ConfigurationManager.LEAD_INSTALL_TIME_FOR_BONUS_MIN },
                        { "instl_default_rating_after_min", 48*60 },
                    };

                    cmd.Parameters.Add(new SqlParameter("@new_partner_benefit_till_hr", ratingVariables["new_partner_benefit_till_hr"]));

                    cmd.Parameters.Add(new SqlParameter("@sr_rslv_tat_min", ratingVariables["sr_rslv_tat_min"]));
                    cmd.Parameters.Add(new SqlParameter("@sr_default_rating_after_min", ratingVariables["sr_default_rating_after_min"]));

                    cmd.Parameters.Add(new SqlParameter("@instl_rslv_tat_min", ratingVariables["instl_rslv_tat_min"]));
                    cmd.Parameters.Add(new SqlParameter("@instl_default_rating_after_min", ratingVariables["instl_default_rating_after_min"]));

                    cmd.Parameters.Add(new SqlParameter("@bonus_rating", 1));

                    res = ResponseType.READER;
                    return cmd;
                }),
                new ExecuteAllResponseHandler((reader, sharId) =>
                {
                    while (reader.Read())
                    {
                        if (reader["avg_rating"] != DBNull.Value)
                        {
                            totalShards++;
                            avgRating = double.Parse(reader["avg_rating"].ToString()) + avgRating;
                        }
                    }
                })).ExecuteAll();

                return avgRating / totalShards;
            });
        }

        public static Dictionary<string, double> GetMonthlyBreakup(double amount, double routerRent)
        {
            double charges = (amount - routerRent) / 1.18;
            routerRent = routerRent / 1.18;
            var sgst = 0.09 * (charges + routerRent);
            var cgst = 0.09 * (charges + routerRent);
            var dict = new Dictionary<string, double>();
            dict.Add("mainCharges", charges);
            dict.Add("routerRent", routerRent);
            dict.Add("sgst", sgst);
            dict.Add("cgst", cgst);
            return dict;
        }

        public static RazorpayAccountMapping GetRazorPayAccount(LongIdInfo longAccountId)
        {
            return new ShardQueryExecutor<RazorpayAccountMapping>(new GetSqlCommand((out ResponseType res) =>
            {
                SqlCommand cmd = new SqlCommand("select * from razorpay_account_mapping where account_id = @account_id and active = 1");
                cmd.Parameters.Add(new SqlParameter("@account_id", longAccountId.ToSafeDbObject()));
                res = ResponseType.READER;
                return cmd;
            }), longAccountId.shard_id,
            new ResponseHandler<RazorpayAccountMapping>((reader) =>
            {
                if (reader.Read())
                {
                    return new RazorpayAccountMapping()
                    {
                        accountId = longAccountId,
                        razorPayAccountId = reader["razorpay_account_id"].ToString(),
                        extraData = reader["extra_data"].ToString(),
                        accountName = reader["account_name"].ToString(),
                        accountNumber = reader["account_number"].ToString(),
                        ifscCode = reader["ifsc_code"].ToString()
                    };
                }
                return null;
            })).Execute();
        }

        public static List<KeyValuePair<long, List<LongIdInfo>>> GetAllNasAndLcoMapping(IMemoryCache _memoryCache)
        {
            List<KeyValuePair<long, List<LongIdInfo>>> data;
            if (!_memoryCache.TryGetValue("NAS_FOR_SPEED", out data))
            {
                Dictionary<long, List<LongIdInfo>> dict = new Dictionary<long, List<LongIdInfo>>();
                new ShardQueryExecutor<bool>(new GetSqlCommand((out ResponseType res) =>
                {
                    SqlCommand cmd = new SqlCommand(@"select a.mapped_id as nasid, b.lco_id from t_account_mapping1 a inner join
                        (select account_id as lco_id, mapped_id as account_id from t_account_mapping1 where account_id in (
                        SELECT account_id from t_account_mapping1
                        WHERE mapping_type='application' and mapped_id = 7)
                        and mapping_type = 'account') b
                        on a.account_id = b.account_id and a.mapping_type = 'location'");
                    res = ResponseType.READER;
                    return cmd;
                }),
                (reader, shardId) =>
                {
                    while (reader.Read())
                    {
                        LongIdInfo lcoId = new LongIdInfo(shardId, DBObjectType.ACCOUNT_TYPE, Convert.ToInt64(reader["lco_id"]));
                        LongIdInfo nasid = new LongIdInfo(shardId, DBObjectType.ACTIVE_NAS, Convert.ToInt64(reader["nasid"]));
                        if (dict.TryGetValue(lcoId.GetLongId(), out var list))
                            list.Add(nasid);
                        else
                        {
                            list = new List<LongIdInfo>();
                            list.Add(nasid);
                            dict.Add(lcoId.GetLongId(), list);
                        }
                    }
                }).ExecuteAll();

                data = dict.ToList();
                _memoryCache.Set("NAS_FOR_SPEED", data, new TimeSpan(0, 0, Constants.SECONDS_IN_DAY*2));
            }
            return data;
        }

        private static Dictionary<string, string> appDownloadLinks = new Dictionary<string, string>() { 
            { "WIOM_SALES", "https://wiom.page.link/vgNL" },
            { "HOME_ROUTER", "https://wiom.page.link/9D6W" },
            { "gold", "https://wiom.page.link/9D6W" },
            { "LEAD_SERVICES", "https://wiom.page.link/fnb2" }
        };

        public static Dictionary<string, object> GetStaticEnums(string appName, int appVersion)
        {
            List<KeyValuePair<int, string>> apps = new List<KeyValuePair<int, string>>();
            foreach (App x in Enum.GetValues(typeof(App)))
                apps.Add(new KeyValuePair<int, string>((int)x, x.ToString()));

            List<KeyValuePair<int, string>> features = new List<KeyValuePair<int, string>>();
            foreach (Feature x in Enum.GetValues(typeof(Feature)))
                features.Add(new KeyValuePair<int, string>((int)x, x.ToString()));

            List<KeyValuePair<int, string>> userTypes = new List<KeyValuePair<int, string>>();
            foreach (AdminUserType x in Enum.GetValues(typeof(AdminUserType)))
                userTypes.Add(new KeyValuePair<int, string>((int)x, x.ToString()));

            List<KeyValuePair<int, string>> singleNasOperations = new List<KeyValuePair<int, string>>();
            foreach (ListCheckDataType x in Enum.GetValues(typeof(ListCheckDataType)))
                singleNasOperations.Add(new KeyValuePair<int, string>((int)x, x.ToString()));

            var latestVersions = CoreCacheHelper.GetInstance().getValueFromCache(CoreCacheHelper.LATEST_APP_VERSIONS, 0, () =>
            {
                return new Dictionary<string, object>() { { "sales", -999 }, { "sales_time", "2022-02-22" }, { "gold", -999 }, { "gold_time", "2022-02-22" }, { "HOME_ROUTER", -999 }, { "HOME_ROUTER_time", "2022-02-22" } };
            });

            latestVersions.TryGetValue(appName + "_time", out object tm);

            object latestAppVersion = appVersion;
            if(DateTime.TryParse((string)tm, out DateTime time))
                if (DateTime.UtcNow > time)
                    latestVersions.TryGetValue(appName, out latestAppVersion);

            var dict = new Dictionary<string, object>();

            dict["apps"] = apps;
            dict["features"] = features;
            dict["userTypes"] = userTypes;
            dict["singleNasOperations"] = singleNasOperations;
            dict["speedTestUrl"] = $"https://d387dlbe18j5qk.cloudfront.net/medium_0.bw?tmpstm={DateTime.UtcNow.Ticks}";
            dict["blockOnSpeed"] = true;
            dict["minSpeedLimit"] = 10;
            dict["WIOM_MEMBER_FEE"] = I2e1ConfigurationManager.MEMBERSHIP_FEE;
            dict["CASH_HANDLING_FEE"] = I2e1ConfigurationManager.CASH_HANDLING_FEE;
            dict["WIOM_GOLD_VIDEO_00"] = "https://d387dlbe18j5qk.cloudfront.net/app-videos/hi/00-new-customer.mp4";
            dict["WIOM_GOLD_VIDEO_01"] = "https://d387dlbe18j5qk.cloudfront.net/app-videos/hi/01-Welcome.mp4";
            dict["WIOM_GOLD_VIDEO_02"] = "https://d387dlbe18j5qk.cloudfront.net/app-videos/hi/02-WhatIsRestartFees.mp4";
            dict["WIOM_GOLD_VIDEO_03"] = "https://d387dlbe18j5qk.cloudfront.net/app-videos/hi/03-RouterOnRent.mp4";
            dict["WIOM_GOLD_VIDEO_04"] = "https://d387dlbe18j5qk.cloudfront.net/app-videos/hi/04-RouterisRestarting.mp4";
            dict["WIOM_GOLD_VIDEO_05"] = "https://d387dlbe18j5qk.cloudfront.net/app-videos/hi/05-Speedchecking.mp4";
            dict["SPEED_TEST_WIOM_GOLD_VIDEO"] = "https://d387dlbe18j5qk.cloudfront.net/Welcome_to_wiom_download.mp4";
            dict["PARTNER_EDUCATIONAL_VIDEO"] = I2e1ConfigurationManager.PARTNER_EDUCATIONAL_VIDEO;
            dict["PARTNER_NEW_VIDEO"] = I2e1ConfigurationManager.PARTNER_NEW_VIDEO;
            dict["DEVICE_INACTIVITY_TIME_IN_MIN"] = I2e1ConfigurationManager.DEVICE_INACTIVITY_TIME_IN_MIN;
            dict["TICKET_RESOLUTION_TIME_FOR_BONUS_MIN"] = I2e1ConfigurationManager.TICKET_RESOLUTION_TIME_FOR_BONUS_MIN;
            dict["LEAD_INSTALL_TIME_FOR_BONUS_MIN"] = I2e1ConfigurationManager.LEAD_INSTALL_TIME_FOR_BONUS_MIN;
            dict["GLOBALLY_ACTIVE_PLAN_SETTINGS"] = I2e1ConfigurationManager.GLOBALLY_ACTIVE_PLAN_SETTINGS;
            dict["PARTNER_WORKING_START_END_MINS"] = I2e1ConfigurationManager.PARTNER_WORKING_START_END_MINS;
            dict["STOP_INTERNET_AFTER_DAYS"] = I2e1ConfigurationManager.STOP_INTERNET_AFTER_DAYS;
            dict["RENEWAL_DUE_DATE_CONFIG"] = I2e1ConfigurationManager.RENEWAL_DUE_DATE_CONFIG;
            dict["RENEWAL_AFTER_DUE_DATE_CONFIG"] = I2e1ConfigurationManager.RENEWAL_AFTER_DUE_DATE_CONFIG;
            dict["WIOM_TKT_MGR"] = I2e1ConfigurationManager.WIOM_TKT_MGR_ACCOUNT_ID;
            dict["PARTNER_COMMISSION_POLICY"] = I2e1ConfigurationManager.PARTNER_COMMISSION_POLICY;
            dict["RESTART_FEE"] = I2e1ConfigurationManager.RESTART_FEE;
            dict["MEMBERSHIP_FEE"] = I2e1ConfigurationManager.MEMBERSHIP_FEE;
            dict["CASH_HANDLING_FEE"] = I2e1ConfigurationManager.CASH_HANDLING_FEE;
            dict["DISCONNECTION_TIME_DAYS"] = I2e1ConfigurationManager.DISCONNECTION_TIME_DAYS;
            dict["RESTART_FEE_EXEMPTION_TIME_DAYS"] = I2e1ConfigurationManager.RESTART_FEE_EXEMPTION_TIME_DAYS;
            dict["BOOKING_FEE"] = I2e1ConfigurationManager.BOOKING_FEE;
            dict["CUSTOMER_SCENARIO"] = I2e1ConfigurationManager.CUSTOMER_SCENARIO;

            if (appDownloadLinks.TryGetValue(appName, out var link))
                dict["appDownloadLink"] = link;

            dict["latestAppVersion"] = latestAppVersion == null ?"":latestAppVersion.ToString();
            return dict;
        }

        public static bool AddApplicationToAccount(LongIdInfo longAccountId, App app, Dictionary<string, object> serviceParams = null, bool mergeSrvcPrms = true)
        {
            string srvcParams = null;
            if (serviceParams != null && serviceParams.Count > 0)
                srvcParams = JsonConvert.SerializeObject(serviceParams).Replace("\\", string.Empty);
            Logger.GetInstance().Info(String.Format("AddApplicationToAccount called with accountId:{0}, app: {1}, serviceParams: {2}", longAccountId, app.ToString(), srvcParams));
            return new ShardQueryExecutor<bool>(new GetSqlCommand((out ResponseType res) =>
            {
                SqlCommand cmd = new SqlCommand(@"
                DECLARE @acct_id int = 0;
                DECLARE @prms VARCHAR(1000) = NULL;
                SELECT @acct_id = account_id, @prms = mapping_params FROM t_account_mapping1 WHERE mapped_id=@app_id AND account_id = @accountId AND mapping_type=@mappingType
                IF @acct_id > 0
                BEGIN
                    IF(@mergeSrvcPrms = 1 AND @prms IS NOT NULL)
                    BEGIN
                        SET @prms = dbo.fn_json_merge(@prms,@serviceParams)
                    END
                    ELSE
                    BEGIN
                        SET @prms = @serviceParams
                    END
                    
                    UPDATE t_account_mapping1 SET mapping_params = @prms WHERE mapped_id=@app_id AND account_id = @accountId AND mapping_type=@mappingType
                    SELECT @acct_id AS account_id
                END
                ELSE
                BEGIN
                    INSERT INTO t_account_mapping1(mapped_id, account_id, mapping_type, added_time, mapping_params)
                    VALUES(@app_id, @accountId, @mappingType, GETUTCDATE(), @serviceParams)
                    SELECT @accountId AS account_id
                END");
                cmd.Parameters.Add(new SqlParameter("@app_id", (int)app));
                cmd.Parameters.Add(new SqlParameter("@accountId", longAccountId.local_value));
                cmd.Parameters.Add(new SqlParameter("@mappingType", "application"));
                cmd.Parameters.Add(new SqlParameter("@mergeSrvcPrms", mergeSrvcPrms?1:0));
                cmd.Parameters.Add(new SqlParameter("@serviceParams", srvcParams.ToSafeDbObject()));
                res = ResponseType.READER;
                return cmd;
            }), longAccountId.shard_id,
            new ResponseHandler<bool>((reader) =>
            {
                if (reader.Read())
                {
                    ResetAccountCache(longAccountId);
                    return (longAccountId.local_value == Convert.ToInt64(reader["account_id"]));
                }
                return false;
            })).Execute();
        }
        public static bool UpdateMandateStatus(string mandate_id, string mandate_token, string status)
        {
            Mandate mandate = CoreAccountService.GetMandateInfo(mandate_id);
            InsertAndUpdateMandateInfo(mandate.account_id, mandate_id, mandate_token, status, mandate.mandate_end_date,
                mandate.customer_id, mandate.mandate_amount.ToString(), mandate.mandate_start_date);
            return true;
        }
        // Shift in dynamo db
        public static List<ExpiryNasIdInfo> GetNasIdWithExpiry()
        {
            List<ExpiryNasIdInfo> mobileList = new List<ExpiryNasIdInfo>();
            new ShardQueryExecutor<List<ExpiryNasIdInfo>>(new GetSqlCommand((out ResponseType res) =>
            {
                SqlCommand cmd = new SqlCommand(@"select router_nas_id,mobile,otp_expiry_time from t_router_user_mapping where id in (
                select max(id) from t_router_user_mapping 
                where otp = 'DONE' and device_limit = 10
                group by router_nas_id) and
				otp_expiry_time <= DATEADD(day,2,GETUTCDATE())");
                res = ResponseType.READER;
                return cmd;
            }),
            new ExecuteAllResponseHandler((reader, shardId) =>
            {
                while (reader.Read())
                {
                    mobileList.Add(new ExpiryNasIdInfo(new LongIdInfo(shardId, DBObjectType.ACTIVE_NAS, Convert.ToInt64(reader["router_nas_id"])), reader["mobile"].ToString(), reader["otp_expiry_time"].ToString()));
                }
            })).ExecuteAll();

            return mobileList;
        }

        public static Dictionary<string, List<string>> GetAllNasIdUser(List<string> mobileListExpiry)
        {
            Dictionary<string, List<string>> UserInfoWhoExpiredFCM_TOKEN = new Dictionary<string, List<string>>();
            new ShardQueryExecutor<Dictionary<string, List<string>>>(new GetSqlCommand((out ResponseType res) =>
            {
                SqlCommand cmd = new SqlCommand(@"select app.fcm_token, tuser.mobile from t_app_device as app 
                 inner Join (select max(id) as id,mobile from t_user where mobile in(select text from @mobileListExpiry) group by mobile) 
				as tuser on tuser.id=app.t_user_id");
                DataTable dataTable = new DataTable();
                dataTable.Columns.Add("text", typeof(string));
                mobileListExpiry.ForEach(m => dataTable.Rows.Add(m));
                cmd.Parameters.Add(new SqlParameter("@mobileListExpiry", dataTable) { TypeName = "dbo.type_string" });
                res = ResponseType.READER;
                return cmd;
            }),
            ((reader, shardId) =>
            {
                while (reader.Read())
                {
                    if (UserInfoWhoExpiredFCM_TOKEN.ContainsKey(reader["mobile"].ToString()))
                        UserInfoWhoExpiredFCM_TOKEN[reader["mobile"].ToString()].Add(reader["fcm_token"].ToString());
                    else
                        UserInfoWhoExpiredFCM_TOKEN.Add(reader["mobile"].ToString(), new List<string>() { reader["fcm_token"].ToString() });
                }
            })).ExecuteAll();
            return UserInfoWhoExpiredFCM_TOKEN;
        }

        public static Dictionary<long, DateTime> GetAllLastPingTime(List<LongIdInfo> nasListExpiry)
        {
            Dictionary<long, DateTime> NasInfoLastPingTime = new Dictionary<long, DateTime>();
            new ShardQueryExecutor<Dictionary<long, DateTime>>(new GetSqlCommand((out ResponseType res) =>
            {
                SqlCommand cmd = new SqlCommand(@"select last_ping_time,router_nas_id from t_controller where router_nas_id 
                in (select value from @nasListExpiry)");
                
                res = ResponseType.READER;
                return cmd;
            }),
            new ExecuteAllResponseHandler((reader, shardId) =>
            {
                while (reader.Read())
                {
                    var longId = new LongIdInfo(shardId, DBObjectType.ACTIVE_NAS, Convert.ToInt64(reader["router_nas_id"])).GetLongId();
                    if (!NasInfoLastPingTime.ContainsKey(longId))
                        NasInfoLastPingTime.Add(longId, DateTime.Parse(reader["last_ping_time"].ToString()));
                }
                
            })).ExecuteAll(nasListExpiry, "@nasListExpiry");
            return NasInfoLastPingTime;
        }

        public static Dictionary<long, KeyValuePair<bool,DateTime>> GetWiomMemberStatus(List<LongIdInfo> nasListExpiry)
        {
            Dictionary<long, KeyValuePair<bool, DateTime>> NasWiomMember = new Dictionary<long, KeyValuePair<bool, DateTime>>();
            new ShardQueryExecutor<Dictionary<long, bool>>(new GetSqlCommand((out ResponseType res) =>
            {
                SqlCommand cmd = new SqlCommand(@"select acc1.mapping_params as mapping_params, acc.mapped_id as router_nas_id
                from t_account_mapping1 as acc1 inner join
                (select account_id,mapped_id from t_account_mapping1 WHERE mapped_id in  (select value from @nasListExpiry)
                AND mapping_type='location') as acc
                on acc.account_id=acc1.account_id where acc1.mapping_type='application'");
                res = ResponseType.READER;
                return cmd;
            }),
            new ExecuteAllResponseHandler((reader, shardId) =>
            {
                string extra = null;
                bool wiom_member_status = false;
                DateTime createDate = DateTime.UtcNow;
                while (reader.Read())
                {
                    extra = reader["mapping_params"].ToString();
                    if (!String.IsNullOrEmpty(extra))
                    {
                        JObject json = JObject.Parse(extra);
                        if (json["wiom_member"] != null && ((DateTime)json["expiry_date"]) >= DateTime.UtcNow)
                        {
                            wiom_member_status = (bool)json["wiom_member"];
                            createDate = ((DateTime)json["create_date"]);
                        }
                    }
                    NasWiomMember[new LongIdInfo(shardId, DBObjectType.ACTIVE_NAS, Convert.ToInt64(reader["router_nas_id"])).GetLongId()] = new KeyValuePair<bool, DateTime>(wiom_member_status,createDate);
                    wiom_member_status = false;
                }

            })).ExecuteAll(nasListExpiry, "@nasListExpiry");
            return NasWiomMember;
        }

        public static bool UpdateCouponInstance(long shardId, int id, int coupounBlueId = 0, int accountId = 0, string issuedTime = "", string expiryTime = "", string coupon_code = "", string extraData = "", int usedCount = 0, bool decreaseCount = false)
        {
            Logger.GetInstance().Info(String.Format("AddCouponInstance called with coupounBlueId:{0}, accountId: {1}, issuedTime: {2}, expiryTime: {3}" +
               ", extraData: {4}, usedCount: {5}", coupounBlueId, accountId, issuedTime, expiryTime, extraData, usedCount));
            return new ShardQueryExecutor<bool>(new GetSqlCommand((out ResponseType res) =>
            {
                SqlCommand cmd = new SqlCommand(@"
                UPDATE t_coupon_instance 
                SET
	                blueprint_id = COALESCE(NULLIF(@coupounBlueId, 0), blueprint_id),
	                coupon_code = COALESCE(NULLIF(@coupon_code, ''), coupon_code),
	                account_id = COALESCE(NULLIF(@accountId, 0), account_id),
	                issued_time = COALESCE(@issuedTime, issued_time),
	                expiry_time = COALESCE(@expiryTime, expiry_time),
                    extra_data = COALESCE(NULLIF(@extraData,''), extra_data),
                    used_count = CASE WHEN @decreaseCount = 1 THEN used_count + 1 ELSE COALESCE(NULLIF(@usedCount,0), used_count) END
                where id=@id");

                cmd.Parameters.Add(new SqlParameter("@id", (int)id));
                cmd.Parameters.Add(new SqlParameter("@coupounBlueId", coupounBlueId));
                cmd.Parameters.Add(new SqlParameter("@accountId", accountId));
                cmd.Parameters.Add(new SqlParameter("@issuedTime", string.IsNullOrEmpty(issuedTime) ? DBNull.Value : DateTime.Parse(issuedTime)));
                cmd.Parameters.Add(new SqlParameter("@expiryTime", string.IsNullOrEmpty(expiryTime) ? DBNull.Value : DateTime.Parse(expiryTime)));
                cmd.Parameters.Add(new SqlParameter("@extraData", extraData.ToSafeDbObject()));
                cmd.Parameters.Add(new SqlParameter("@usedCount", usedCount));
                cmd.Parameters.Add(new SqlParameter("@coupon_code", coupon_code.ToSafeDbObject()));
                cmd.Parameters.Add(new SqlParameter("@decreaseCount", decreaseCount ? 1 : 0));
                res = ResponseType.READER;
                return cmd;
            }), shardId,
            new ResponseHandler<bool>((reader) =>
            {
                if (reader.RecordsAffected == 1)
                {
                    return true;
                }

                return false;
            })).Execute();

        }

        public static List<CouponInstance> GetCouponInstance(LongIdInfo accountId)
        {
            Dictionary<int, Coupon> couponDict = new Dictionary<int, Coupon>();
            new ShardQueryExecutor<List<Coupon>>(new GetSqlCommand((out ResponseType res) =>
            {
                SqlCommand cmd = new SqlCommand(@"SELECT * from t_coupon_blueprint");
                res = ResponseType.READER;
                return cmd;
            }), ShardHelper.SHARD0,
            new ResponseHandler<List<Coupon>>((reader) =>
            {
                while (reader.Read())
                {
                    Coupon temp = new Coupon();
                    temp.id = (int)reader["id"];
                    temp.amount = (int)reader["amount"];
                    temp.name = reader["name"].ToSafeDbObject().ToString();
                    temp.vaildity_days = (int)reader["validity_days"];
                    temp.discount = (int)reader["discount"];
                    temp.frequency = (int)reader["frequency"];
                    temp.coupon_type = reader["coupon_type"].ToSafeDbObject().ToString();
                    temp.coupon_code = reader["coupon_code"].ToSafeDbObject().ToString();
                    var exd = reader["extra_data"].ToSafeDbObject().ToString();
                    if (!string.IsNullOrEmpty(exd))
                    {
                        temp.extra_data = JsonConvert.DeserializeObject<Dictionary<string, object>>(exd);
                    }
                    else
                        temp.extra_data = new Dictionary<string, object>();

                    couponDict.Add(temp.id, temp);
                }
                return null;
            })).Execute();


            return new ShardQueryExecutor<List<CouponInstance>>(new GetSqlCommand((out ResponseType res) =>
            {
                SqlCommand cmd = new SqlCommand(@"
                SELECT *
                FROM t_coupon_instance AS INSTANCE
                WHERE instance.account_id = @accountId
                  AND instance.issued_time <= GETUTCDATE()
                ORDER BY instance.expiry_time ASC");

                cmd.Parameters.Add(new SqlParameter("@accountId", accountId.ToSafeDbObject()));
                res = ResponseType.READER;
                return cmd;
            }), accountId.shard_id,
            new ResponseHandler<List<CouponInstance>>((reader) =>
            {
                List<CouponInstance> couponInstance = new List<CouponInstance>();
                while (reader.Read())
                {
                    if (couponDict.ContainsKey((int)reader["blueprint_id"]) && (int)reader["used_count"] < couponDict[(int)reader["blueprint_id"]].frequency)
                    {
                        CouponInstance temp = new CouponInstance();
                        temp.id = (int)reader["id"];
                        temp.blueprint_id = (int)reader["blueprint_id"];
                        temp.account_id = accountId;
                        temp.issued_time = DateTime.Parse(reader["issued_time"].ToSafeDbObject().ToString());
                        temp.expiry_time = DateTime.Parse(reader["expiry_time"].ToSafeDbObject().ToString());
                        temp.coupon_code = String.IsNullOrEmpty(couponDict[temp.blueprint_id].coupon_code) ? reader["couponCode"].ToSafeDbObject().ToString() : couponDict[temp.blueprint_id].coupon_code;
                        temp.discount = couponDict[temp.blueprint_id].discount;
                        var exd = reader["extra_data"].ToSafeDbObject().ToString();
                        if (!string.IsNullOrEmpty(exd))
                        {
                            temp.extra_data = JsonConvert.DeserializeObject<Dictionary<string, object>>(exd);
                            if (temp.extra_data.ContainsKey("discount") && temp.coupon_code == "FLATX")
                            {
                                temp.discount = int.Parse(temp.extra_data["discount"].ToString());
                                temp.coupon_code = temp.coupon_code.Replace("X", temp.discount.ToString());
                            }
                        }
                        else
                            temp.extra_data = new Dictionary<string, object>();
                        temp.extra_data = temp.extra_data.Count == 0 ? couponDict[temp.blueprint_id].extra_data : temp.extra_data;

                        couponInstance.Add(temp);
                    }
                }

                return couponInstance;
            })).Execute();

            return null;

        }

        ///////////Assign and return the reward coupon/////////////////////////////////

        /*  
           ServiceName: AssignRewardCoupon
           Description: Assign reward coupon on renewal
           Params: LongIdInfo accountId
       */
        public static void AssignRewardCoupon(LongIdInfo accountId)
        {
            
            Logger.GetInstance().Info($"Assigning reward coupon to ${accountId.GetLongId()}");
            var couponsByDate = GetRewardCouponsToday("renewal_payment");//this service gets called only in renewal payment
            double totalDiscount = couponsByDate.Aggregate(0, (acc, x) => acc + x.discount);
            int totalCoupons = couponsByDate.Count;
            Random random = new Random();
            int randomValue = 0;
            if (totalCoupons == 0)
            {
                do
                {
                    randomValue = random.Next(1, 51);
                } while (randomValue == 10); // Assigning random coupon between 1 to 50 except 10.
            }
            else
            {
                randomValue = random.Next(1, 51);// New coupon should be between 1 to 50
                                                 // Calculate the maximum allowed sum to maintain a maximum average of 10
            }

            double maxSum = 10 * (totalCoupons + 1); // Inequality: x <= 10(n+1)
            int newCouponValue;
            if (maxSum - totalDiscount <= 0)//minimum coupon given is Re.1
                newCouponValue = 1;
            else
                newCouponValue = (int)Math.Min(maxSum - totalDiscount, randomValue);
            if (newCouponValue == 10)
            {
                // Reduce the frequency of 10 by half
                newCouponValue = random.Next(2, 11);
            }
            AssignRewardCoupon(accountId, newCouponValue);
            
            return;
        }
        
        /*  
           ServiceName: AssignRewardCoupon
           Description: Assign reward coupon 
           Params: LongIdInfo accountId, int discount
       */
        public static bool AssignRewardCoupon(LongIdInfo accountId, int discount)
        {
            ///Update template coupon with desired discount//////////////////////
            discount = Math.Abs(discount);
            int blueprint_id = 0;
            Logger.GetInstance().Info($"Assigning reward coupon to ${accountId.GetLongId()} with discount ${discount}");
            new ShardQueryExecutor<int>(new GetSqlCommand((out ResponseType res) =>
            {
                SqlCommand cmd = new SqlCommand(@"
                    SELECT * from t_coupon_blueprint WHERE coupon_type = 'reward'
                    AND coupon_code = 'FLATX';
                ");
                res = ResponseType.READER;
                return cmd;
            }), ShardHelper.SHARD0,
            new ResponseHandler<int>((reader) =>
            {
                while (reader.Read())
                {
                    blueprint_id = (int)reader["id"];
                }
                return 0;
            })).Execute();


            //////////////////////////////Assign said reward coupon///////////////////////////////////////////
            Coupon couponBlueprint = GetCouponBluePrint(blueprint_id);
            if (couponBlueprint.coupon_code == "FLATX" && couponBlueprint.extra_data.ContainsKey("discount"))
            {
                couponBlueprint.extra_data["discount"] = discount;
                couponBlueprint.coupon_code = couponBlueprint.coupon_code.Replace("X", discount.ToString());
            }
            return new ShardQueryExecutor<bool>(new GetSqlCommand((out ResponseType res) =>
            {
                SqlCommand cmd = new SqlCommand(@"
                    INSERT INTO t_coupon_instance(account_id, blueprint_id, issued_time, expiry_time, extra_data, used_count, coupon_code)
                    select @account_id, @blueprint_id, GETUTCDATE(), DATEADD(DAY, @validity_days, GETUTCDATE()), @extra_data, 0, @coupon_code
                    ");
                cmd.Parameters.Add(new SqlParameter("@blueprint_id", blueprint_id));
                cmd.Parameters.Add(new SqlParameter("@account_id", accountId.ToSafeDbObject()));
                cmd.Parameters.Add(new SqlParameter("@validity_days", couponBlueprint.vaildity_days));
                cmd.Parameters.Add(new SqlParameter("@coupon_code", couponBlueprint.coupon_code));
                cmd.Parameters.Add(new SqlParameter("@extra_data", JsonConvert.SerializeObject(couponBlueprint.extra_data).ToSafeDbObject()));
                res = ResponseType.READER;
                return cmd;
            }), accountId.shard_id,
            new ResponseHandler<bool>((reader) =>
            {
                if (reader.RecordsAffected == 1)
                {
                    var msg = "";
                    if (!string.IsNullOrEmpty(msg))
                    {
                        var data = new Dictionary<string, string>();
                        data.Add("title", "स्पेशल discount सिर्फ आपके लिए :blush:");
                        data.Add("body", msg);
                        List<ManagementUser> mgmUser = FirebaseHelper.SendNotification(accountId, App.HOME_ROUTER, data, "COUPON");
                    }
                    return true;
                }
                return false;
            })).Execute();
        }
 /////////////////////////////////////////////////////////////////////////////////////////////////////////


        /*  
           ServiceName: GetRewardCouponsToday
           Description: Fetches reward coupons disbursed today
           Params: String paymentType
       */
        public static List<CouponInstance> GetRewardCouponsToday(String paymentType)
        {
            Logger.GetInstance().Info($"CoreAccountService:GetRewardCouponsToday called with paymentType: {paymentType}");
            Dictionary<int, Coupon> couponDict = new Dictionary<int, Coupon>();
            List<CouponInstance> couponInstance = new List<CouponInstance>();
            new ShardQueryExecutor<List<Coupon>>(new GetSqlCommand((out ResponseType res) =>
            {
                SqlCommand cmd = new SqlCommand(@"SELECT * from t_coupon_blueprint where coupon_type='reward'");
                res = ResponseType.READER;
                return cmd;
            }), ShardHelper.SHARD0,
            new ResponseHandler<List<Coupon>>((reader) =>
            {
                while (reader.Read())
                {
                    Coupon temp = new Coupon();
                    temp.id = (int)reader["id"];
                    temp.amount = (int)reader["amount"];
                    temp.name = reader["name"].ToSafeDbObject().ToString();
                    temp.vaildity_days = (int)reader["validity_days"];
                    temp.discount = (int)reader["discount"];
                    temp.frequency = (int)reader["frequency"];
                    temp.coupon_type = reader["coupon_type"].ToSafeDbObject().ToString();
                    temp.coupon_code = reader["coupon_code"].ToSafeDbObject().ToString();
                    var exd = reader["extra_data"].ToSafeDbObject().ToString();
                    if (!string.IsNullOrEmpty(exd))
                        temp.extra_data = JsonConvert.DeserializeObject<Dictionary<string, object>>(exd);
                    else
                        temp.extra_data = new Dictionary<string, object>();
                    couponDict.Add(temp.id, temp);
                }
                return null;
            })).Execute();

            new ShardQueryExecutor<List<CouponInstance>>(new GetSqlCommand((out ResponseType res) =>
            {
                SqlCommand cmd = new SqlCommand(@"
                SELECT * FROM t_coupon_instance
                WHERE CAST(issued_time AS DATE) = CAST(GETDATE() AS DATE) and blueprint_id in (select value from @blueprints)");
                DataTable dataTable = new DataTable();
                dataTable.Columns.Add("value", typeof(long));
                foreach (KeyValuePair<int, Coupon> pair in couponDict)
                    dataTable.Rows.Add(pair.Key);

                cmd.Parameters.Add(new SqlParameter("@blueprints", dataTable) { TypeName = "dbo.type_bigint" });
                res = ResponseType.READER;
                return cmd;
            }),
            new ExecuteAllResponseHandler((reader, shardId) =>
            {

                while (reader.Read())
                {
                    CouponInstance temp = new CouponInstance();
                    temp.id = (int)reader["id"];
                    temp.blueprint_id = (int)reader["blueprint_id"];
                    var exd = reader["extra_data"].ToSafeDbObject().ToString();
                    if (!string.IsNullOrEmpty(exd))
                    {
                        temp.extra_data = JsonConvert.DeserializeObject<Dictionary<string, object>>(exd);
                        if (temp.extra_data != null && temp.extra_data.ContainsKey("discount"))
                            temp.discount = int.Parse(temp.extra_data["discount"].ToString());
                    }
                    couponInstance.Add(temp);
                }
            })).ExecuteAll();
            return couponInstance;
        }
     ///////////////////////////////////////////////////////////////////////////////////
     
        //TODO : Change for sharding
        public static Dictionary<long, List<CouponInstance>> GetAllCouponOnNaseIds(List<LongIdInfo> nasListExpiry)
        {
            Dictionary<long, List<CouponInstance>> nasIdCouponInstance = new Dictionary<long, List<CouponInstance>>();

            new ShardQueryExecutor<Dictionary<long, List<CouponInstance>>>(new GetSqlCommand((out ResponseType res) =>
            {
                SqlCommand cmd = new SqlCommand(@"
                SELECT coupon.*, acc.mapped_id
                   FROM t_coupon_instance AS coupon
                   INNER JOIN
                     (SELECT account_id, mapped_id
                      FROM t_account_mapping1
                      WHERE mapped_id in
                          (SELECT value
                           FROM @nasListExpiry)
                        AND mapping_type='location') AS acc ON acc.account_id=coupon.account_id
                   WHERE coupon.used_count>0
                     AND coupon.expiry_time>GETUTCDATE() AND coupon.issued_time<=GETUTCDATE()");

                res = ResponseType.READER;
                return cmd;
            }),
            new ExecuteAllResponseHandler((reader, shardId) =>
            {
                while (reader.Read())
                {
                    CouponInstance temp = new CouponInstance();
                    temp.id = (int)reader["id"];
                    temp.blueprint_id = (int)reader["blueprint_id"];
                    temp.issued_time = DateTime.Parse(reader["issued_time"].ToSafeDbObject().ToString());
                    temp.expiry_time = DateTime.Parse(reader["expiry_time"].ToSafeDbObject().ToString());
                    temp.coupon_code = reader["coupon_code"].ToSafeDbObject().ToString();
                    temp.account_id = new LongIdInfo(shardId, DBObjectType.ACCOUNT_TYPE, reader.GetValueOrDefault<long>("account_id"));

                    LongIdInfo nasid = new LongIdInfo(shardId, DBObjectType.ACTIVE_NAS, Convert.ToInt64(reader["mapped_id"]));
                    if (nasIdCouponInstance.ContainsKey(nasid.GetLongId()))
                        nasIdCouponInstance[nasid.GetLongId()].Add(temp);
                    else
                        nasIdCouponInstance.Add((int)reader["mapped_id"], new List<CouponInstance>() { temp });
                    
                    var exd = reader["extra_data"].ToSafeDbObject().ToString();
                    if (!string.IsNullOrEmpty(exd))
                        temp.extra_data = JsonConvert.DeserializeObject<Dictionary<string, object>>(exd);
                    else
                        temp.extra_data = new Dictionary<string, object>();

                }
            })).ExecuteAll(nasListExpiry, "@nasListExpiry");
            return nasIdCouponInstance;
        }

        public static HomeRouterPlan GetLastEntryNasIdInfoWithExpiry(LongIdInfo longNasId, string transactionId)
        {
            /*return new ShardQueryExecutor<PassportUser>(new GetSqlCommand((out ResponseType res) =>
            {
                SqlCommand cmd = new SqlCommand(@"select * from t_router_user_mapping 
                    where otp = 'DONE' and device_limit = 10 and router_nas_id = @router_nas_id and auth_state = 1
                    and (cast(created_on as date) = cast(GETUTCDATE() as date) OR cast(created_on as date) = cast(DATEADD(day, -1, GETUTCDATE()) as date)) and transaction_id is not null and transaction_id != @transactionId
				");
                cmd.Parameters.Add(new SqlParameter("@router_nas_id", longNasId.ToSafeDbObject()));
                cmd.Parameters.Add(new SqlParameter("@transactionId", transactionId.ToSafeDbObject()));
                res = ResponseType.READER;
                return cmd;
            }), longNasId.shard_id,
            new ResponseHandler<PassportUser>((reader) =>
            {
                PassportUser passportUser = new PassportUser();
                if (reader.Read())
                {
                    passportUser.id = Convert.ToInt64(reader["id"].ToString());
                    passportUser.mobile = reader["mobile"].ToString();
                    passportUser.otpIssuedTime = (DateTime)reader["otp_issued_time"];
                    passportUser.otpExpiryTime = (DateTime)reader["otp_expiry_time"];
                    passportUser.createdOn = (DateTime)reader["created_on"];
                    passportUser.otp = (string)reader["otp"];
                    passportUser.authState = (byte)reader["auth_state"] == 1;
                    passportUser.nasid = new LongIdInfo(longNasId.shard_id, DBObjectType.ACTIVE_NAS, Convert.ToInt64((int)reader["router_nas_id"]));
                }
                return passportUser;
            })).Execute();*/

            ModelDynamoDb<HomeRouterPlan> modelDynamoDb = new ModelDynamoDb<HomeRouterPlan>();
            DynamoQueryBuilder<HomeRouterPlan> dynamoQueryBuilder = new DynamoQueryBuilder<HomeRouterPlan>();
            dynamoQueryBuilder.AddkeyConditionExpression(p => p.nasId, DynamoComparisonOperator.Equal, longNasId.GetLongId())
                              .AddFilterCondition(p => p.deviceLimit, DynamoComparisonOperator.Equal, Constants.HOME_ROUTER_DEVICE_LIMIT)
                              .AddFilterCondition(p => p.otp, DynamoComparisonOperator.Equal, HOMEOTP.DONE, LogicalOperator.And)
                              .AddFilterParenthesisExpression(Parentheses.OpenParentheses, LogicalOperator.And)
                              .AddFilterCondition(p => p.transactionId, DynamoComparisonOperator.NotEqual, null)
                              .AddFilterCondition(p => p.transactionId, DynamoComparisonOperator.NotEqual, transactionId, LogicalOperator.And)
                              .AddFilterParenthesisExpression(Parentheses.ClosedParentheses)
                              .AddFilterParenthesisExpression(Parentheses.OpenParentheses, LogicalOperator.And)
                              .AddFilterCondition(p => p.entryDate, DynamoComparisonOperator.Equal, DateTime.UtcNow.Date)
                              .AddFilterCondition(p => p.entryDate, DynamoComparisonOperator.Equal, DateTime.UtcNow.Date.AddDays(-1), LogicalOperator.Or)
                              .AddFilterParenthesisExpression(Parentheses.ClosedParentheses);
            List<HomeRouterPlan> allHomeRouter = modelDynamoDb.GetRecord(dynamoQueryBuilder);
            if (allHomeRouter.Count >= 1)
                return allHomeRouter[0];
            return null;
        }
        public static HomeRouterPlan GetHomeRouterTRUMInfo(LongIdInfo longNasId, string transactionId)
        {
            /*return new ShardQueryExecutor<PassportUser>(new GetSqlCommand((out ResponseType res) =>
            {
                SqlCommand cmd = new SqlCommand(@"select * from t_router_user_mapping 
                    where otp = 'DONE' and device_limit = 10 and router_nas_id = @router_nas_id and auth_state = 1
                    and transaction_id is not null and transaction_id = @transactionId
				");
                cmd.Parameters.Add(new SqlParameter("@router_nas_id", longNasId.ToSafeDbObject()));
                cmd.Parameters.Add(new SqlParameter("@transactionId", transactionId.ToSafeDbObject()));
                res = ResponseType.READER;
                return cmd;
            }), longNasId.shard_id,
            new ResponseHandler<PassportUser>((reader) =>
            {
                PassportUser passportUser = null;
                if (reader.Read())
                {
                    passportUser = new PassportUser();
                    passportUser.id = Convert.ToInt64(reader["id"].ToString());
                    passportUser.mobile = reader["mobile"].ToString();
                    passportUser.otpIssuedTime = (DateTime)reader["otp_issued_time"];
                    passportUser.otpExpiryTime = (DateTime)reader["otp_expiry_time"];
                    passportUser.createdOn = (DateTime)reader["created_on"];
                    passportUser.otp = (string)reader["otp"];
                    passportUser.authState = (byte)reader["auth_state"] == 1;
                    passportUser.nasid = new LongIdInfo(longNasId.shard_id, DBObjectType.ACTIVE_NAS, Convert.ToInt64((int)reader["router_nas_id"]));
                }
                return passportUser;
            })).Execute();*/

            ModelDynamoDb<HomeRouterPlan> modelDynamoDb = new ModelDynamoDb<HomeRouterPlan>();
            DynamoQueryBuilder<HomeRouterPlan> dynamoQueryBuilder = new DynamoQueryBuilder<HomeRouterPlan>();
            dynamoQueryBuilder.AddkeyConditionExpression(p => p.nasId, DynamoComparisonOperator.Equal, longNasId.GetLongId())
                              .AddFilterCondition(p => p.deviceLimit, DynamoComparisonOperator.Equal, Constants.HOME_ROUTER_DEVICE_LIMIT)
                              .AddFilterCondition(p => p.otp, DynamoComparisonOperator.Equal, HOMEOTP.DONE, LogicalOperator.And)
                              .AddFilterParenthesisExpression(Parentheses.OpenParentheses, LogicalOperator.And)
                              .AddFilterCondition(p => p.transactionId, DynamoComparisonOperator.NotEqual, null)
                              .AddFilterCondition(p => p.transactionId, DynamoComparisonOperator.Equal, transactionId, LogicalOperator.And)
                              .AddFilterParenthesisExpression(Parentheses.ClosedParentheses);
            List<HomeRouterPlan> allHomeRouter = modelDynamoDb.GetRecord(dynamoQueryBuilder);
            if (allHomeRouter.Count >= 1)
                return allHomeRouter[0];
            return null;
        }
        public static bool GetTransactionStatus(LongIdInfo longNasId, long revenueId, string transactionId)
        {
            return new ShardQueryExecutor<bool>(new GetSqlCommand((out ResponseType res) =>
            {
                SqlCommand cmd = new SqlCommand(@"select * from t_revenue where id = @revenueId and paid = 1 and transaction_id !=@transactionId");
                cmd.Parameters.Add(new SqlParameter("@revenueId", revenueId.ToSafeDbObject()));
                cmd.Parameters.Add(new SqlParameter("@transactionId", transactionId.ToSafeDbObject()));
                res = ResponseType.READER;
                return cmd;
            }), longNasId.shard_id,
            new ResponseHandler<bool>((reader) =>
            {
                if (reader.Read())
                {
                    return true;
                }
                return false;
            })).Execute();
        }

        public static Coupon CreateBlueprintObject(SqlDataReader reader) 
        {
            Coupon bp = new Coupon();
            if (reader.Read())
            {
                bp.id = (int)reader["id"];
                bp.amount = (int)reader["amount"];
                bp.name = reader["name"].ToSafeDbObject().ToString();
                bp.vaildity_days = (int)reader["validity_days"];
                bp.discount = (int)reader["discount"];
                bp.frequency = (int)reader["frequency"];
                bp.coupon_type = reader["coupon_type"].ToSafeDbObject().ToString();
                bp.coupon_code = reader["coupon_code"].ToSafeDbObject().ToString();

                if (reader.HasColumn("extra_data")) 
                {
                    var exd = reader["extra_data"].ToSafeDbObject().ToString();
                    if (!string.IsNullOrEmpty(exd))
                        bp.extra_data = JsonConvert.DeserializeObject<Dictionary<string, object>>(exd);
                    else
                        bp.extra_data = new Dictionary<string, object>();
                }
            }
            return bp;
        }

    /*  
        ServiceName: GetCouponBluePrint
        Description: Fetches coupon blueprint using blueprint_id
        Params:int blueprint_id
    */
        public static Coupon GetCouponBluePrint(int blueprint_id)
        {
            return new ShardQueryExecutor<Coupon>(new GetSqlCommand((out ResponseType res) =>
            {
                SqlCommand cmd = new SqlCommand(@"SELECT * FROM t_coupon_blueprint
                                 WHERE  id = @blueprint_id");
                cmd.Parameters.Add(new SqlParameter("@blueprint_id", blueprint_id));
                res = ResponseType.READER;
                return cmd;
            }), ShardHelper.SHARD0,
            new ResponseHandler<Coupon>((reader) =>
            {
                return CreateBlueprintObject(reader);
            })).Execute();
        }
//////////////////////////////////////////////////////////////////////

        /*  
            ServiceName: GetCouponBluePrintByDiscount
            Description: Fetches coupon blueprint using discount and coupon type
            Params:int blueprint_id
        */
        public static Coupon GetCouponBluePrintByDiscount(int discount, string couponType)
        {
            return new ShardQueryExecutor<Coupon>(new GetSqlCommand((out ResponseType res) =>
            {
                SqlCommand cmd = new SqlCommand(@"
                    SELECT * from t_coupon_blueprint WHERE coupon_type = @coupon_type");
                cmd.Parameters.Add(new SqlParameter("@coupon_type", couponType));
                res = ResponseType.READER;
                return cmd;
            }), ShardHelper.SHARD0,
            new ResponseHandler<Coupon>((reader) =>
            {
                Coupon coupon = CreateBlueprintObject(reader);
                coupon.discount = discount;
                coupon.coupon_code = coupon.coupon_code.Replace("X", discount.ToString());
                coupon.name = coupon.coupon_code;
                return coupon;
            })).Execute();

        }

        public static int GetRewardCouponAmount(string paymentType)
        {
            var couponsByDate = GetRewardCouponsToday(paymentType);
            double totalDiscount = couponsByDate.Aggregate(0, (acc, x) => acc + x.discount);
            int totalCoupons = couponsByDate.Count;
            Random random = new Random();
            int randomValue = 0;
            if (totalCoupons == 0)
            {
                do
                {
                    randomValue = random.Next(1, 51);
                } while (randomValue == 10); // Assigning random coupon between 1 to 50 except 10.
            }
            else
            {
                randomValue = random.Next(1, 51);// New coupon should be between 1 to 50
                                                 // Calculate the maximum allowed sum to maintain a maximum average of 10
            }
            double maxSum = 10 * (totalCoupons + 1); // Inequality: x <= 10(n+1)
            int newCouponValue;
            if (maxSum - totalDiscount <= 0)//minimum coupon given is Re.1
                newCouponValue = 1;
            else
                newCouponValue = (int)Math.Min(maxSum - totalDiscount, randomValue);
            if (newCouponValue == 10)
            {
                // Reduce the frequency of 10 by half
                newCouponValue = random.Next(2, 11);
            }
            return Math.Abs(newCouponValue) % 51;
        }
        /////////////////////////////////////////////////////////////////////

        public static bool AssignCoupon(int blueprint_id, LongIdInfo account_id)
        {
            Coupon couponBlueprint = GetCouponBluePrint(blueprint_id);
            if (couponBlueprint.id == 0)
                return false;
            return new ShardQueryExecutor<bool>(new GetSqlCommand((out ResponseType res) =>
            {
                SqlCommand cmd = new SqlCommand(@"
                    INSERT INTO t_coupon_instance(account_id, blueprint_id, issued_time, expiry_time, extra_data, used_count, coupon_code)
                    select @account_id, @blueprint_id, GETUTCDATE(), DATEADD(DAY, @validity_days, GETUTCDATE()), @extra_data, 0, @coupon_code
                    ");
                cmd.Parameters.Add(new SqlParameter("@blueprint_id", blueprint_id));
                cmd.Parameters.Add(new SqlParameter("@account_id", account_id.ToSafeDbObject()));
                cmd.Parameters.Add(new SqlParameter("@validity_days", couponBlueprint.vaildity_days));
                cmd.Parameters.Add(new SqlParameter("@coupon_code", couponBlueprint.coupon_code));
                cmd.Parameters.Add(new SqlParameter("@extra_data", JsonConvert.SerializeObject(couponBlueprint.extra_data).ToSafeDbObject()));
                res = ResponseType.READER;
                return cmd;
            }), account_id.shard_id,
            new ResponseHandler<bool>((reader) =>
            {

                if (reader.RecordsAffected == 1)
                {
                    var msg = "";
                    if (couponBlueprint.extra_data.ContainsKey("text_hi"))
                        msg = couponBlueprint.extra_data["text_hi"].ToString();

                    if (!string.IsNullOrEmpty(msg))
                    {
                        var data = new Dictionary<string, string>();
                        data.Add("title", "स्पेशल discount सिर्फ आपके लिए 😊");
                        data.Add("body", msg);
                        List<ManagementUser> mgmUser = FirebaseHelper.SendNotification(account_id, App.HOME_ROUTER, data, "COUPON");

                    }

                    return true;

                }

                return false;
            })).Execute();
        }

        public static bool AssignCouponToManyAccount(int blueprint_id, List<LongIdInfo> accountIdList)
        {
            Coupon couponBlueprint = GetCouponBluePrint(blueprint_id);

            new ShardQueryExecutor<bool>(new GetSqlCommand((out ResponseType res) =>
            {
                SqlCommand cmd = new SqlCommand(@"
                    INSERT INTO t_coupon_instance(account_id, blueprint_id, issued_time, expiry_time, extra_data, used_count, coupon_code)
                    output inserted.*                    
                    select a.value, @blueprint_id, GETUTCDATE(), DATEADD(DAY, @validity_days, GETUTCDATE()), @extra_data, 0, @coupon_code
                    FROM @accountIdList as a");
                cmd.Parameters.Add(new SqlParameter("@blueprint_id", blueprint_id));
                cmd.Parameters.Add(new SqlParameter("@validity_days", couponBlueprint.vaildity_days));
                cmd.Parameters.Add(new SqlParameter("@coupon_code", couponBlueprint.coupon_code));
                cmd.Parameters.Add(new SqlParameter("@extra_data", JsonConvert.SerializeObject(couponBlueprint.extra_data).ToSafeDbObject()));
                res = ResponseType.READER;
                return cmd;
            }),
            new ExecuteAllResponseHandler((reader, shardId) =>
            {
                while (reader.Read())
                {
                    var msg = "";
                    LongIdInfo account_id = new LongIdInfo(shardId, DBObjectType.ACCOUNT_TYPE, Convert.ToInt64(reader["account_id"]));
                    if (couponBlueprint.extra_data.ContainsKey("text_hi"))
                        msg = couponBlueprint.extra_data["text_hi"].ToString();
                    if (!string.IsNullOrEmpty(msg))
                    {
                        var data = new Dictionary<string, string>();
                        data.Add("title", "स्पेशल discount सिर्फ आपके लिए 😊");
                        data.Add("body", msg);
                        List<ManagementUser> mgmUser = FirebaseHelper.SendNotification(account_id, App.HOME_ROUTER, data, "COUPON");

                    }
                }
            })).ExecuteAll(accountIdList, "@accountIdList");
            return true;
        }

        public static bool GetMandateFeatureStatus()
        {
            bool mandateEnable = true;

            return mandateEnable;
        }

        public static bool InsertAndUpdateMandateInfo(LongIdInfo longAccountId, string mandate_id, string mandate_token, string mandate_status, DateTime mandate_end_date, string mandate_customer_id, string mandate_amount, DateTime mandate_start_date, string extra_data = null, int planId = 0)
        {
            return new ShardQueryExecutor<bool>(new GetSqlCommand((out ResponseType res) =>
            {
                SqlCommand cmd = new SqlCommand(@"IF EXISTS(select 1 from t_mandate_info WHERE  account_id = @accountId and mandate_id = @mandate_id)
                                                    BEGIN
                                                      UPDATE t_mandate_info SET    
                                                                mandate_id = COALESCE (@mandate_id, mandate_id),
                                                                token = COALESCE (@token, token),
                                                                status = COALESCE (@status, status),
                                                                customer_id = COALESCE (@customer_id, customer_id),
                                                                mandate_start_date = COALESCE (@mandate_start_date, mandate_start_date),
                                                                mandate_end_date = COALESCE (@mandate_end_date, mandate_end_date),
                                                                mandate_amount = COALESCE (@mandate_amount, mandate_amount),
                                                                last_updated_time = getutcdate(),
                                                                extra_data = COALESCE (@extra_data,extra_data),
                                                                plan_id = COALESCE (@planId,plan_id)
                                                          WHERE account_id = @accountId and mandate_id = @mandate_id;
                                                    END
                                                ELSE
                                                    BEGIN
                                                      INSERT INTO t_mandate_info(account_id, mandate_id,token,status,customer_id,mandate_start_date,mandate_end_date,mandate_amount,last_updated_time, added_time, plan_id)
                                              values(@accountId,@mandate_id,@token,@status,@customer_id,@mandate_start_date,@mandate_end_date,@mandate_amount,getutcdate(), getutcdate(), @planId)
                                                    END");
                cmd.Parameters.Add(new SqlParameter("@accountId", longAccountId.ToSafeDbObject()));
                cmd.Parameters.Add(new SqlParameter("@mandate_id", mandate_id.ToSafeDbObject())); 
                cmd.Parameters.Add(new SqlParameter("@token", mandate_token.ToSafeDbObject())); 
                cmd.Parameters.Add(new SqlParameter("@status", mandate_status.ToSafeDbObject())); 
                cmd.Parameters.Add(new SqlParameter("@customer_id", mandate_customer_id.ToSafeDbObject())); 
                cmd.Parameters.Add(new SqlParameter("@mandate_start_date", mandate_start_date.ToSafeDbObject())); 
                cmd.Parameters.Add(new SqlParameter("@mandate_end_date", mandate_end_date.ToSafeDbObject())); 
                cmd.Parameters.Add(new SqlParameter("@mandate_amount", Convert.ToInt64(mandate_amount.ToSafeDbObject()))); 
                cmd.Parameters.Add(new SqlParameter("@extra_data", extra_data.ToSafeDbObject())); 
                cmd.Parameters.Add(new SqlParameter("@planId", planId == 0 ? DBNull.Value: planId)); 
                res = ResponseType.READER;
                return cmd;
            }), longAccountId.shard_id,
            new ResponseHandler<bool>((reader) =>
            {
                if (reader.RecordsAffected==1)
                {
                    return true;
                }
                return false;
            })).Execute();
        }

        public static Mandate GetMandateInfo(string mandateId)
        {
             Mandate mandate = new Mandate();
             new ShardQueryExecutor<bool>(new GetSqlCommand((out ResponseType res) => 
             {
                 SqlCommand cmd = new SqlCommand(@"select * from t_mandate_info where mandate_id = @mandateId");
                 cmd.Parameters.Add(new SqlParameter("@mandateId", mandateId.ToSafeDbObject()));
                 res = ResponseType.READER;
                 return cmd;
             }),
             new ExecuteAllResponseHandler((reader, shardId) =>
             {
                 if (reader.Read())
                 {
                     mandate.account_id = new LongIdInfo(shardId, DBObjectType.ACCOUNT_TYPE, Convert.ToInt64(reader["account_id"]));
                     mandate.mandate_id = (string)reader["mandate_id"];
                     mandate.token = (string)reader["token"];
                     mandate.status = (string)reader["status"];
                     mandate.customer_id = (string)reader["customer_id"];
                     mandate.mandate_start_date = (DateTime)reader["mandate_start_date"];
                     mandate.mandate_end_date = (DateTime)reader["mandate_end_date"];
                     mandate.mandate_amount = Convert.ToInt32(reader["mandate_amount"].ToString());
                     mandate.last_updated_time = (DateTime)reader["last_updated_time"];
                 }
             })).ExecuteAll();
             return mandate;
        }
        public static MPaymentHistory GetLastFailedMandateWiomBillingEntry(LongIdInfo longNasId)
        {
            ModelDynamoDb<MPaymentHistory> modelDynamoDb = new ModelDynamoDb<MPaymentHistory>();
            string keyConditionExpression = "#nasid = :nasid AND begins_with(transactionId, :transactionPrefix)";
            string indexName = TableSecondaryIndexMapping.WIOMBILLINGWIFI_NASID;
            string filterExpression = "paymentStatus = :paymentStatus";
            Dictionary<string, string> expressionAttributeNames = new Dictionary<string, string>
            {
                { "#nasid", "nasid" }
            };
            Dictionary<string, AttributeValue>  expressionAttributeValues = new Dictionary<string, AttributeValue>
            {
                { ":nasid", i2e1_core.DynamoUtilities.BaseDynamoDbHelper.ConvertToAttributeValue(longNasId.GetLongId()) },
                { ":paymentStatus", i2e1_core.DynamoUtilities.BaseDynamoDbHelper.ConvertToAttributeValue(0) },
                { ":transactionPrefix", i2e1_core.DynamoUtilities.BaseDynamoDbHelper.ConvertToAttributeValue("wgSubs") }
            };
            List<MPaymentHistory> paymentHistory= modelDynamoDb.GetRecord(keyConditionExpression, filterExpression, expressionAttributeNames, expressionAttributeValues, indexName, 1);
            if(paymentHistory.Count==1)
                return paymentHistory[0];
            return null;
        }

        public static Mandate GetAccountMandateInfo(LongIdInfo account_id)
        {
            return new ShardQueryExecutor<Mandate>(new GetSqlCommand((out ResponseType res) =>
            {
                SqlCommand cmd = new SqlCommand(@"select top 1 * from t_mandate_info where mandate_end_date>GETUTCDATE() 
                                                    and account_id = @account_id order by id desc");
                cmd.Parameters.Add(new SqlParameter("@account_id", account_id.ToSafeDbObject()));
                res = ResponseType.READER;
                return cmd;
            }), account_id.shard_id,
            new ResponseHandler<Mandate>((reader) =>
            {
                Mandate temp = null;
                if (reader.Read())
                {
                    temp = new Mandate();
                    temp.account_id = new LongIdInfo(account_id.shard_id, DBObjectType.ACCOUNT_TYPE, Convert.ToInt64(reader["account_id"].ToString()));
                    temp.mandate_id = (string)reader["mandate_id"];
                    temp.token = (string)reader["token"];
                    temp.status = (string)reader["status"];
                    temp.customer_id = (string)reader["customer_id"];
                    temp.mandate_start_date = (DateTime)reader["mandate_start_date"];
                    temp.mandate_end_date = (DateTime)reader["mandate_end_date"];
                    temp.mandate_amount = Convert.ToInt32(reader["mandate_amount"].ToString());
                    temp.last_updated_time = (DateTime)reader["last_updated_time"];
                }
                return temp;
            })).Execute();
        }
 
        public static bool CheckAutopayExecutionState(WgStatus status, DateTime expiryDate)
        {
            if (status == WgStatus.PAID && expiryDate.AddDays(-3).Date == DateTime.UtcNow.Date)
                return true;
            if (status == WgStatus.DUE_ON)
                return true;
            if (status == WgStatus.OVERDUE && expiryDate.AddDays(2).Date >= DateTime.UtcNow.Date)
                return true;
            return false;
        }

        public static bool CheckAdminNasMapping(LongIdInfo longUserId, AdminUserType userType, params int[] nasid)
        {
            if (userType == AdminUserType.ADMIN || userType == AdminUserType.SUPER_ADMIN)
                return true;
            return new ShardQueryExecutor<bool>(new GetSqlCommand((out ResponseType res) =>
            {
                long userid = longUserId.local_value;
                string command = string.Format(@"SELECT a.router_nas_id 
                                                 FROM   (SELECT * 
                                                         FROM   t_store 
                                                         WHERE  partner_id IN (SELECT partner_id 
                                                                               FROM   t_partner 
                                                                               WHERE  client_id IN (SELECT 
                                                                                      t_admin_mapping.mapped_id 
                                                                                                   FROM   t_admin_mapping 
                                                                                                   WHERE  admin_id = @userid 
                                                                                                          AND mapping_type = 'client')) 
                                                         UNION 
                                                         SELECT * 
                                                         FROM   t_store 
                                                         WHERE  partner_id IN (SELECT mapped_id AS partner_id 
                                                                               FROM   t_admin_mapping 
                                                                               WHERE  admin_id = @userid 
                                                                                      AND mapping_type = 'partner') 
                                                         UNION 
                                                         SELECT * 
                                                         FROM   t_store 
                                                         WHERE  router_nas_id IN (SELECT mapped_id AS router_nas_id 
                                                                                  FROM   t_admin_mapping 
                                                                                  WHERE  admin_id = @userid 
                                                                                         AND mapping_type = 'location')) a 
                                                 where a.router_nas_id in ({0})", string.Join(",", nasid)
                                              );
                SqlCommand cmd = new SqlCommand(command);
                cmd.Parameters.Add(new SqlParameter("@userid", userid));
                res = ResponseType.READER;
                return cmd;
            }), longUserId.shard_id,
            new ResponseHandler<bool>((reader) =>
            {
                bool[] markArray = new bool[nasid.Length];
                while (reader.Read())
                {
                    int tNas = (int)reader["router_nas_id"];
                    int index = Array.IndexOf(nasid, tNas);
                    if (index == -1)
                        return false;
                    markArray[index] = true;
                }
                foreach (var mark in markArray)
                {
                    if (!mark)
                        return false;
                }
                return true;
            })).Execute();
        }

        public static List<Store> FetchLocations(LongIdInfo longaccountId = null, SearchQuery searchQuery = null)
        {
            long accountId = longaccountId.local_value;
            long shardid = longaccountId.shard_id;
            if (searchQuery == null)
                searchQuery = new SearchQuery()
                {
                    pageNumber = 1,
                    pageSize = 50,
                    orderBy = "router_nas_id",
                    orderByDirection = "asc"
                };
            return new PaginatedShardQueryExecutor<List<Store>>(new GetSqlCommand((out ResponseType res) =>
            {
                SqlCommand cmd = new SqlCommand(@"SELECT * FROM t_store WHERE router_nas_id IN 
                    (SELECT mapped_id FROM t_account_mapping1 WHERE (@account_id = 0 OR account_id = @account_id) AND mapping_type='location')");

                cmd.Parameters.Add(new SqlParameter("@account_id", accountId));
                res = ResponseType.READER;
                return cmd;
            }), shardid,
            new ResponseHandler<List<Store>>((reader) =>
            {
                var stores = new List<Store>();
                while (reader.Read())
                {
                    stores.Add(new Store()
                    {
                        nasid = new LongIdInfo(shardid, DBObjectType.ACTIVE_NAS, Convert.ToInt64(reader["router_nas_id"])),
                        storeName = reader["shop_name"].ToString()
                    });
                }
                return stores;
            }), searchQuery).Execute();
        }
    }
}
