using i2e1_basics.Database;
using i2e1_basics.Utilities;
using i2e1_core.Models;
using i2e1_core.Utilities;
using Microsoft.VisualBasic;
using System;
using System.Collections.Generic;
using System.Data.SqlClient;

namespace i2e1_core.services
{
    public class CoreTicketService
    {
        public static long SaveSupportTicket(SupportTicket supportTicket)
        {
            long insertedId = 0;
            return new ShardQueryExecutor<long>(new GetSqlCommand((out ResponseType res) =>
            {
                SqlCommand cmd = new SqlCommand(@"
            declare @task_id bigint;
            INSERT INTO t_tasks 
            (title, description, priority, reporter, assigned_to, mobile, partner_id, t_type, due_date, configs, status,extra_data) 
            VALUES 
            (@title, @description, @priority, @reporter, @assignedTo, @mobile, @partnerId, @ticketType, @dueDate, @configs, @status,@extra_data);
            set @task_id = (SELECT SCOPE_IDENTITY() AS task_id);-- Get the inserted task_id
            SELECT @task_id AS TaskId;
            INSERT INTO t_task_status
            (task_id,created_by,status) VALUES(@task_id,@reporter,@status);
        ");

                cmd.Parameters.AddWithValue("@title", supportTicket.Title.ToSafeDbObject());
                cmd.Parameters.AddWithValue("@description", supportTicket.Description.ToSafeDbObject());
                cmd.Parameters.AddWithValue("@priority", supportTicket.Priority.ToSafeDbObject());
                cmd.Parameters.AddWithValue("@reporter", supportTicket.Reporter.ToSafeDbObject());
                cmd.Parameters.AddWithValue("@assignedTo", supportTicket.AssignedTo.ToSafeDbObject());
                cmd.Parameters.AddWithValue("@mobile", supportTicket.Mobile.ToSafeDbObject());
                cmd.Parameters.AddWithValue("@partnerId", supportTicket.PartnerId.ToSafeDbObject());
                cmd.Parameters.AddWithValue("@ticketType", supportTicket.Type.ToSafeDbObject());
                cmd.Parameters.AddWithValue("@dueDate", supportTicket.DueDate.ToSafeDbObject());
                cmd.Parameters.AddWithValue("@configs", supportTicket.Configs.ToSafeDbObject());
                cmd.Parameters.AddWithValue("@status", supportTicket.Status.ToSafeDbObject());
                cmd.Parameters.AddWithValue("@extra_data", supportTicket.ExtraData.ToSafeDbObject());

                res = ResponseType.READER;
                return cmd;
            }), ShardHelper.SHARD0,
            new ResponseHandler<long>((reader) =>
            {
                if (reader.Read())
                {
                    insertedId = reader.GetValueOrDefault<long>("TaskId");
                }

                return insertedId;
            })).Execute();
        }
        public static bool ResolveSupportTicket(SupportTicket ticket)
        {
            return new ShardQueryExecutor<bool>(new GetSqlCommand((out ResponseType res) =>
            {
                SqlCommand cmd = new SqlCommand(@"
                DECLARE @task_id BIGINT;
                IF EXISTS (
                    SELECT 1 
                    FROM t_tasks 
                    WHERE mobile = @mobile AND status=0 AND t_type = @Type AND due_date <= GETDATE()
                )
                BEGIN
                    set @task_id =(SELECT  id
                    FROM t_tasks 
                    WHERE mobile = @mobile AND status=0 AND t_type = @Type AND due_date <= GETDATE());

                    UPDATE t_tasks 
                    SET status = @Status 
                    WHERE id = @task_id;

                    INSERT INTO t_task_status (task_id, created_by, status) 
                    VALUES (@task_id, @PartnerId, @Status);
                END
            ");

                cmd.Parameters.AddWithValue("@mobile", ticket.Mobile.ToSafeDbObject());
                cmd.Parameters.AddWithValue("@PartnerId", ticket.PartnerId.ToSafeDbObject());
                cmd.Parameters.AddWithValue("@Type", ticket.Type.ToSafeDbObject());
                cmd.Parameters.AddWithValue("@Status", ticket.Status.ToSafeDbObject());
                res = ResponseType.READER;
                return cmd;
            }), ShardHelper.SHARD0,
            new ResponseHandler<bool>((reader) =>
            {
                return reader.RecordsAffected > 0;
            })).Execute();
        }
        public static bool AddTaskAttachment(TaskAttachment attachment)
        {
            return new ShardQueryExecutor<bool>(
                new GetSqlCommand((out ResponseType res) =>
                {
                    SqlCommand cmd = new SqlCommand(@"
                    INSERT INTO t_task_attachments (task_id, file_name, type, created_at, status)
                    VALUES (@taskId, @fileName, @type, CURRENT_TIMESTAMP, @status);
                ");

                    cmd.Parameters.AddWithValue("@taskId", attachment.TaskId.ToSafeDbObject());
                    cmd.Parameters.AddWithValue("@fileName", attachment.FileName.ToSafeDbObject());
                    cmd.Parameters.AddWithValue("@type", attachment.FileType.ToSafeDbObject());
                    cmd.Parameters.AddWithValue("@status", 1);

                    res = ResponseType.READER;
                    return cmd;
                }), ShardHelper.SHARD0,
                new ResponseHandler<bool>((reader) =>
                {
                    return reader.RecordsAffected>0;
                })
            ).Execute();
        }

        public static List<TaskAttachment> GetTaskAttachments(long taskId)
        {
            List<TaskAttachment> attachments = new List<TaskAttachment>();

            return new ShardQueryExecutor<List<TaskAttachment>>(new GetSqlCommand((out ResponseType res) =>
            {
              SqlCommand cmd = new SqlCommand(@"
                SELECT *
                FROM t_task_attachments
                WHERE task_id = @taskId
                ORDER BY created_at DESC;
              ");

                cmd.Parameters.AddWithValue("@taskId", taskId);

                res = ResponseType.READER;
                return cmd;
            }), ShardHelper.SHARD0,
            new ResponseHandler<List<TaskAttachment>>((reader) =>
            {
                while (reader.Read())
                {
                    TaskAttachment entry = new TaskAttachment
                    {
                        Id = reader.GetValueOrDefault<long>("id"),
                        TaskId = reader.GetValueOrDefault<long>("task_id"),
                        FileName = reader.GetValueOrDefault<string>("file_name"),
                        FileType = reader.GetValueOrDefault<string>("type"),
                        Status = reader.GetValueOrDefault<int>("status")
                    };
                    attachments.Add(entry);
                }
                return attachments;
            })).Execute();
        }

        public static List<SupportTicket> GetTaskList(long reporter, long assigned_to, int status)
        {
            List<SupportTicket> tickets = new List<SupportTicket>();

            return new ShardQueryExecutor<List<SupportTicket>>(new GetSqlCommand((out ResponseType res) =>
            {
                if (reporter == 0)
                {
                    SqlCommand cmd = new SqlCommand(@"
                    SELECT *
                    FROM t_tasks
                    WHERE assigned_to = @assigned_to AND t_type='WITHDRAWALREQUEST' AND status =@status
                    ORDER BY created DESC;
                    ");
                    cmd.Parameters.AddWithValue("@assigned_to", assigned_to);
                    cmd.Parameters.AddWithValue("@status", status);

                    res = ResponseType.READER;
                    return cmd;
                }
                else
                {
                    SqlCommand cmd = new SqlCommand(@"
                    SELECT *
                    FROM t_tasks
                    WHERE assigned_to = @assigned_to AND reporter=@reporter AND t_type='WITHDRAWALREQUEST' AND status =@status
                    ORDER BY created DESC;
                    ");

                    cmd.Parameters.AddWithValue("@assigned_to", assigned_to);
                    cmd.Parameters.AddWithValue("@reporter", reporter);
                    cmd.Parameters.AddWithValue("@status", status);

                    res = ResponseType.READER;
                    return cmd;
                }

              

              
            }), ShardHelper.SHARD0,
            new ResponseHandler<List<SupportTicket>>((reader) =>
            {
                while (reader.Read())
                {
                    SupportTicket entry = new SupportTicket
                    {
                        Id = reader.GetValueOrDefault<long>("id"),
                        Title = reader.GetValueOrDefault<string>("title"),
                        Description = reader.GetValueOrDefault<string>("description"),
                        Reporter = reader.GetValueOrDefault<long>("reporter"),
                        Type = reader.GetValueOrDefault<string>("t_type"),
                        Status = reader.GetValueOrDefault<int>("status"),
                        Created = reader.GetValueOrDefault<DateTime>("created"),
                        Modified = reader.GetValueOrDefault<DateTime>("modified"),
                        ExtraData = reader.GetValueOrDefault<string>("extra_data")
                    };
                    tickets.Add(entry);
                }
                return tickets;
            })).Execute();
        }


        public static bool UpdateTaskStatus(long task_id, int status,string extraData)
        {
            return new ShardQueryExecutor<bool>(
                new GetSqlCommand((out ResponseType res) =>
                {
                    SqlCommand cmd = new SqlCommand(@"
                    UPDATE t_tasks set status=@status,extra_data=@extraData where id=@taskId;
                ");

                    cmd.Parameters.AddWithValue("@taskId", task_id.ToSafeDbObject());
                    cmd.Parameters.AddWithValue("@status", status.ToSafeDbObject());
                    cmd.Parameters.AddWithValue("@extraData", extraData.ToSafeDbObject());

                    res = ResponseType.READER;
                    return cmd;
                }), ShardHelper.SHARD0,
                new ResponseHandler<bool>((reader) =>
                {
                    return reader.RecordsAffected > 0;
                })
            ).Execute();
        }

        public static (int count, DateTime created) CheckTaskCount(LongIdInfo reporter, LongIdInfo assigned_to, string t_type)
        {
            int count = 0;
            DateTime created = DateTime.MinValue;
            return new ShardQueryExecutor<(int, DateTime)>(
                new GetSqlCommand((out ResponseType res) =>
                {
                    SqlCommand cmd = new SqlCommand(@"
                    SELECT COUNT(*) as count, MAX(created) as created
                    FROM t_tasks
                    WHERE reporter = @reporter
                      AND assigned_to = @assigned_to
                      AND t_type = @t_type
                      AND status != '-1'
                      AND created >= DATEADD(DAY, -5, GETDATE());;
                ");

                    cmd.Parameters.AddWithValue("@reporter", reporter.GetLongId());
                    cmd.Parameters.AddWithValue("@assigned_to", assigned_to.GetLongId());
                    cmd.Parameters.AddWithValue("@t_type", t_type);

                    res = ResponseType.READER;
                    return cmd;
                }), ShardHelper.SHARD0,
                new ResponseHandler<(int, DateTime)>((reader) =>
                {
                    if (reader.Read())
                    {
                        count = reader.GetValueOrDefault<int>("count");
                        created = reader.GetValueOrDefault<DateTime>("created");
                    }

                    // Add 5 days to the created date
                    created = created.AddDays(5);

                    return (count, created);
                })).Execute();
        }
    }
}
