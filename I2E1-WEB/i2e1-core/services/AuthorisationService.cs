using System;
using System.Collections.Generic;
using System.Linq;
using i2e1_basics.Cache;
using i2e1_basics.Utilities;
using i2e1_core.Models;
using i2e1_core.Models.Client;
using i2e1_core.Utilities;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Primitives;

namespace i2e1_core.services;

public class AuthorisationService
{
    public static bool IfUserIsAllowedToAccessFeature(ManagementUser user, Feature featureToBeChecked)
    {
        if (user.userType == AdminUserType.ADMIN || user.userType == AdminUserType.SUPER_ADMIN)
            return true;
        Dictionary<string, int> features = user.features;
        try
        {
            var feature = features.First(x => x.Key == featureToBeChecked.GetHashCode().ToString());
            return feature.Value == 1;
        }
        catch { }
        return false;

    }

    public static string GetRedirectParams(HttpContext httpContext, string username, ManagementUser user) 
    {
        var guid = Guid.NewGuid().ToString();
        SessionCacheHelper.GetInstance().SetSession(guid, username, user);
        return "_ts=" + guid + "&_un=" + username;
    }

    public static ManagementUser AuthenticateRequest(HttpContext httpContext)
    {
        var path = httpContext.Request.Path.Value;
        ManagementUser user = null;
        httpContext.Request.Query.TryGetValue("_ts", out StringValues redirectGuid);
        httpContext.Request.Query.TryGetValue("_un", out StringValues redirectUsername);

        if (!string.IsNullOrEmpty(redirectGuid) && !string.IsNullOrEmpty(redirectUsername))
        {
            Logger.GetInstance().Info("for path: " + path + ": user not found, checking redirect session");
            user = SessionCacheHelper.GetInstance().GetSession<ManagementUser>(redirectGuid, redirectUsername);
            if (user != null)
            {
                Logger.GetInstance().Info("user found, clearing redirect session and setting user session in cache");
                SessionCacheHelper.GetInstance().DeleteSession(redirectGuid, redirectUsername);
                JWTManager.CreateJwtToken(null, null, user, httpContext);
            }
        }

        if (user == null)
        {
            var jwtObject = JWTManager.Authenticate(httpContext, out var responseStatus);
            if (jwtObject != null)
            {
                user = JwtObject.GetManagementUser(jwtObject);
            }
        }
        
        return user;
    }
}
