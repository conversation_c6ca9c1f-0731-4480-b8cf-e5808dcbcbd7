using i2e1_basics.Models;
using i2e1_core.Models;
using i2e1_core.Utilities;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Filters;

namespace i2e1_core.Attributes;

public class IfLoggedInAttribute : TypeFilterAttribute
{
    public IfLoggedInAttribute() : base(typeof(IfLoggedIn))
    {
    }
}

public class IfLoggedIn : IAuthorizationFilter
{
    public void OnAuthorization(AuthorizationFilterContext filterContext)
    {
        JwtObject jwtObject = JWTManager.Authenticate(filterContext, out ResponseStatus responseStatus);
        if (jwtObject == null)
        {
            if(responseStatus == ResponseStatus.LOGOUT)
                filterContext.Result = new JsonResult(new ErrorResponse(ResponseStatus.LOGOUT, CoreErrorCodes.LOGOUT));
            else if(responseStatus == ResponseStatus.UPDATE)
                filterContext.Result = new JsonResult(new ErrorResponse(ResponseStatus.UPDATE, CoreErrorCodes.UPDATE_APP));
            else
                filterContext.Result = new JsonResult(new ErrorResponse(ResponseStatus.LOGOUT, CoreErrorCodes.NOT_LOGGED_IN));
        }
        else if (string.IsNullOrEmpty(jwtObject.wl_token))
            filterContext.Result = new JsonResult(new ErrorResponse(ResponseStatus.BLOCKED, CoreErrorCodes.NOT_LOGGED_IN));
        else
        {
            filterContext.HttpContext.Items[Constants.JWT_OBJECT] = jwtObject;
        }
    }
}
