using i2e1_basics.Models;
using i2e1_basics.Utilities;
using i2e1_core.Models;
using i2e1_core.Models.Client;
using i2e1_core.services;
using i2e1_core.Utilities;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Filters;
using Microsoft.Extensions.Primitives;
using System;
using System.Collections.Generic;
using System.Linq;

namespace i2e1_core.Attributes;

public class AdminPortalAuthorizationAttribute : TypeFilterAttribute
{
    public AdminPortalAuthorizationAttribute() : base(typeof(AdminPortalAuthorization))
    {
    }
}

public class AdminPortalAuthorization : IAuthorizationFilter
{
    public void OnAuthorization(AuthorizationFilterContext filterContext)
    {
        //var details = SessionUtils.getAdminInPower(filterContext.HttpContext);
        JwtObject jwtObject = JWTManager.Authenticate(filterContext, out ResponseStatus responseStatus);

        if (jwtObject == null)
        {
            if (responseStatus == ResponseStatus.LOGOUT)
                filterContext.Result = new JsonResult(new ErrorResponse(ResponseStatus.LOGOUT, CoreErrorCodes.LOGOUT));
            else if (responseStatus == ResponseStatus.UPDATE)
                filterContext.Result = new JsonResult(new ErrorResponse(ResponseStatus.UPDATE, CoreErrorCodes.UPDATE_APP));
            else
                filterContext.Result = new JsonResult(CoreErrorCodes.NOT_LOGGED_IN);
        }
        else if(string.IsNullOrEmpty(jwtObject.i2e1_admin_token))
        {
            filterContext.Result = new JsonResult(CoreErrorCodes.NOT_LOGGED_IN);
        }
        else
        {
            LongIdInfo nasid = null;
            string nas = filterContext.HttpContext.Request.Query["nasid"];
            if (nas != null)
            {
                bool found = false;
                    
                nasid = LongIdInfo.IdParser(Convert.ToInt64(nas));
                switch (jwtObject.userType)
                {
                    case AdminUserType.ADMIN:
                    case AdminUserType.READ_ONLY_ADMIN:
                    case AdminUserType.SUPER_ADMIN:
                        found = true;
                        break;
                    case AdminUserType.STANDARD:
                        found = CoreAccountService.CheckAdminNasMapping(jwtObject.adminId, jwtObject.userType, (int)nasid.local_value);
                        break;
                }

                if (found)
                    filterContext.HttpContext.Items.Add("nasid", nasid);
                else
                    filterContext.Result = new JsonResult(CoreErrorCodes.NOT_LOGGED_IN);
            }
            if (jwtObject.features != null)
            {
                jwtObject.features.TryGetValue(((int)Feature.SHOW_PHONE_NUMBER).ToString(), out int feature);
                filterContext.HttpContext.Items.Add("ShowNumber", (Feature)feature == 0 ? false : true);
            }
        }
    }
}

public class AuthorizeClientAttribute : TypeFilterAttribute
{
    public AuthorizeClientAttribute() : base(typeof(AuthorizeClient))
    {
    }
}
public class AuthorizeClient : IAuthorizationFilter
{
    public void OnAuthorization(AuthorizationFilterContext filterContext)
    {
        JwtObject jwtObject = JWTManager.Authenticate(filterContext, out ResponseStatus responseStatus);

        if (jwtObject == null)
        {
            if (responseStatus == ResponseStatus.LOGOUT)
                filterContext.Result = new JsonResult(new ErrorResponse(ResponseStatus.LOGOUT, CoreErrorCodes.LOGOUT));
            else if (responseStatus == ResponseStatus.UPDATE)
                filterContext.Result = new JsonResult(new ErrorResponse(ResponseStatus.UPDATE, CoreErrorCodes.UPDATE_APP));
            else
                filterContext.Result = new JsonResult(CoreErrorCodes.NOT_LOGGED_IN);
        }
        if (jwtObject != null && String.IsNullOrEmpty(jwtObject.i2e1_admin_token))
        {
            filterContext.Result = new JsonResult(CoreErrorCodes.NOT_LOGGED_IN);
        }
    }
}

public class AuthorizeNasAdminAttribute : TypeFilterAttribute
{
    public AuthorizeNasAdminAttribute(params AdminUserType[] adminAuthType) : base(typeof(AuthorizeNasUser))
    {
        Arguments = new object[] { adminAuthType };
    }
}

public class AuthorizeNasUser : IAuthorizationFilter
{
    private AdminUserType[] adminUserType;
    public AuthorizeNasUser(AdminUserType[] adminAuthType)
    {
        this.adminUserType = adminAuthType;
    }

        public void OnAuthorization(AuthorizationFilterContext filterContext)
        {
            var context = filterContext.HttpContext;
            //var user = AuthorisationService.AuthenticateRequest(context);
            JwtObject jwtObject = JWTManager.Authenticate(filterContext, out ResponseStatus responseStatus);

            if (jwtObject == null)
            {
                if (responseStatus == ResponseStatus.LOGOUT)
                    filterContext.Result = new JsonResult(new ErrorResponse(ResponseStatus.LOGOUT, CoreErrorCodes.LOGOUT));
                else if (responseStatus == ResponseStatus.UPDATE)
                    filterContext.Result = new JsonResult(new ErrorResponse(ResponseStatus.UPDATE, CoreErrorCodes.UPDATE_APP));
                else
                    filterContext.Result = new JsonResult(CoreErrorCodes.NOT_LOGGED_IN);
            }
            else if(string.IsNullOrEmpty(jwtObject.i2e1_admin_token))
            {
                filterContext.Result = new JsonResult(CoreErrorCodes.NOT_LOGGED_IN);
            }

        bool found = false;
        if (jwtObject != null)
        {
            context.Items["user"] = jwtObject;
            if (jwtObject.userType == AdminUserType.SUPER_ADMIN || jwtObject.userType == AdminUserType.ADMIN)
            {
                found = true;
            }
            else
            {
                Account account = null;
                foreach (AdminUserType userType in adminUserType)
                {
                    if (jwtObject.userType == userType)
                    {
                        found = true;
                        account = CoreAccountService.GetAccountWithUserId(jwtObject.userId);
                        context.Items["account"] = account;
                        if (jwtObject.userType == AdminUserType.STANDARD)
                        {
                            found = false;
                            LongIdInfo nasid = null;
                            StringValues value = String.Empty;
                            context.Request.Query.TryGetValue("nasid", out value);
                            if (value.Count > 0)
                                nasid = LongIdInfo.IdParser(Convert.ToInt64(value[0]));
                            if (nasid != null && account != null)
                            {
                                var stores = CoreAccountService.FetchLocations(account.id);
                                if (stores.Where(s => s.nasid == nasid).ToList().Count == 1)
                                    found = true;
                            }
                            if (nasid != null && !found && CoreUserService.IsLocationMapped(new ManagementUser() {userid = jwtObject.userId }, nasid))
                                found = true;
                            break;
                        }
                    }
                }
            }
        }

        if (!found)
        {
            filterContext.Result = new JsonResult(new ErrorResponse(ResponseStatus.BLOCKED, CoreErrorCodes.NOT_AUTHORISED, "You are not allowed for this action"));
        }
    }
}


public class AuthorizeInternalUserOperationAttribute : TypeFilterAttribute
{
    public AuthorizeInternalUserOperationAttribute(params Feature[] features) : base(typeof(AuthorizeInternalUserOperation))
    {
        Arguments = new object[] { features };
    }
}
public class AuthorizeInternalUserOperation : IAuthorizationFilter
{
    //this attribute authorise feature access for i2e1 internal user
    Feature[] allowedFeatures;
    public AuthorizeInternalUserOperation(Feature[] features)
    {
        this.allowedFeatures = features;
    }

    public void OnAuthorization(AuthorizationFilterContext filterContext)
    {
        //ManagementUser user = SessionUtils.getAdminInPower(filterContext.HttpContext);
        JwtObject jwtObject = JWTManager.Authenticate(filterContext, out ResponseStatus responseStatus);

        if (jwtObject == null)
        {
            if (responseStatus == ResponseStatus.LOGOUT)
                filterContext.Result = new JsonResult(new ErrorResponse(ResponseStatus.LOGOUT, CoreErrorCodes.LOGOUT));
            else if (responseStatus == ResponseStatus.UPDATE)
                filterContext.Result = new JsonResult(new ErrorResponse(ResponseStatus.UPDATE, CoreErrorCodes.UPDATE_APP));
            else
                filterContext.Result = new JsonResult(CoreErrorCodes.NOT_LOGGED_IN);
        }
        else if (!string.IsNullOrEmpty(jwtObject.i2e1_admin_token))
        {
            if (jwtObject.userType == AdminUserType.STANDARD)
            {
                filterContext.Result = new JsonResult(new JsonResponse(ResponseStatus.BLOCKED, "Please contact i2e1 for this action"));
            }

        int count = 0;
        foreach (KeyValuePair<string, int> f in jwtObject.features)
        {
            var f2 = allowedFeatures.Where(m => (f.Key == ((int)m).ToString() && f.Value == 1)).ToList();
            if (f2.Count == 1)
            {
                count++;
            }
        }

            if (count < this.allowedFeatures.Length)
            {
                filterContext.Result = new JsonResult(new JsonResponse(ResponseStatus.BLOCKED, "You are not allowed for this action"));
            }
        }
        else
        {
            filterContext.Result = new JsonResult(CoreErrorCodes.NOT_LOGGED_IN);
        }
    }
    
}

public class AuthorizeAdminAttribute : TypeFilterAttribute
{
    public AuthorizeAdminAttribute(params AdminUserType[] adminAuthType) : base(typeof(AuthorizeAdmin))
    {
        Arguments = new object[] { adminAuthType };
    }
}
public class AuthorizeAdmin : IAuthorizationFilter
{
    private AdminUserType[] adminUserType;
    public AuthorizeAdmin(AdminUserType[] adminAuthType)
    {
        this.adminUserType = adminAuthType;
    }

    public void OnAuthorization(AuthorizationFilterContext filterContext)
    {
        //ManagementUser user = SessionUtils.getAdminInPower(filterContext.HttpContext);
        JwtObject jwtObject = JWTManager.Authenticate(filterContext, out ResponseStatus responseStatus);

        if (jwtObject == null)
        {
            if (responseStatus == ResponseStatus.LOGOUT)
                filterContext.Result = new JsonResult(new ErrorResponse(ResponseStatus.LOGOUT, CoreErrorCodes.LOGOUT));
            else if (responseStatus == ResponseStatus.UPDATE)
                filterContext.Result = new JsonResult(new ErrorResponse(ResponseStatus.UPDATE, CoreErrorCodes.UPDATE_APP));
            else
                filterContext.Result = new JsonResult(CoreErrorCodes.NOT_LOGGED_IN);
        }
        else if (string.IsNullOrEmpty(jwtObject.i2e1_admin_token))
        {
            filterContext.Result = new JsonResult(CoreErrorCodes.NOT_LOGGED_IN);
        }
        bool found = false;
        if (jwtObject != null)
        {
            if (jwtObject.userType == AdminUserType.SUPER_ADMIN || jwtObject.userType == AdminUserType.ADMIN)
            {
                found = true;
            }
            else
            {
                foreach (AdminUserType userType in adminUserType)
                {
                    if (jwtObject.userType == userType)
                    {
                        found = true;
                    }
                }
            }
        }

        if (!found)
        {
            filterContext.Result = new JsonResult(new JsonResponse(ResponseStatus.BLOCKED, "You are not allowed for this action"));
        }
    }
}