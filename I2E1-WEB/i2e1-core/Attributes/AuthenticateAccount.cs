using i2e1_basics.Models;
using i2e1_core.Models;
using i2e1_core.services;
using i2e1_core.Utilities;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Filters;
using wiom_login_share.Models;

namespace i2e1_core.Attributes;

public class AuthenticateAccountAttribute : TypeFilterAttribute
{
    public AuthenticateAccountAttribute(params App[] apps) : base(typeof(AuthenticateAccount))
    {
        Arguments = new object[] { apps };
    }
}

    public class AuthenticateAccount : IAuthorizationFilter
    {
        private App[] apps;
        public AuthenticateAccount(App[] apps)
        {
            this.apps = apps;
        }
        public void OnAuthorization(AuthorizationFilterContext filterContext)
        {
            //var user = (ManagementUser)filterContext.HttpContext.Items["user"];
            JwtObject jwtObject = JWTManager.Authenticate(filterContext, out ResponseStatus responseStatus);

            if (jwtObject == null)
            {
                if (responseStatus == ResponseStatus.LOGOUT)
                    filterContext.Result = new JsonResult(new ErrorResponse(ResponseStatus.LOGOUT, CoreErrorCodes.LOGOUT));
                else if (responseStatus == ResponseStatus.UPDATE)
                    filterContext.Result = new JsonResult(new ErrorResponse(ResponseStatus.UPDATE, CoreErrorCodes.UPDATE_APP));
                else
                    filterContext.Result = new JsonResult(CoreErrorCodes.NOT_LOGGED_IN);
            }
            else if (string.IsNullOrEmpty(jwtObject.wl_token))
                filterContext.Result = new JsonResult(new ErrorResponse(ResponseStatus.FAILURE, CoreErrorCodes.NOT_LOGGED_IN));
            else
            {
                var account = CoreAccountService.GetAccountWithUserId(jwtObject.adminId);
                App requestFromApp = 0;
                var found = false;
                if (jwtObject.userType == AdminUserType.ADMIN || jwtObject.userType == AdminUserType.SUPER_ADMIN)
                    found = true;
                else
                {
                    foreach (var app in apps)
                    {
                        if (account.hasService(app))
                        {
                            found = true;
                            requestFromApp = app;
                            break;
                        }
                    }
                }
                if (!found)
                    filterContext.Result = new JsonResult(new ErrorResponse(ResponseStatus.FAILURE, CoreErrorCodes.THIS_APPLICATION_IS_NOT_CONFIGURED_FOR_YOUR_ACCOUNT));
                else
                {
                    // filterContext.HttpContext.Items["account"] = account;
                    jwtObject.app = requestFromApp;
                    filterContext.HttpContext.Items[Constants.JWT_OBJECT] = jwtObject;
                }
            }
        }
    }
