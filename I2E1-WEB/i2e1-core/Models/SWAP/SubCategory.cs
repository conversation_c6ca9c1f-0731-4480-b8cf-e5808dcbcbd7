using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Web;

namespace i2e1_core.Models.SWAP
{
    public class SubCategory : IComparer<SubCategory>
    {
        public int id { get; set; }

        public string category { get; set; }

        public string brand_cat { get; set; }

        public int brand_cat_id { get; set; }

        public string subCategory { get; set; }

        public bool isProduct { get; set; }

        public bool isService { get; set; }

        public bool isPublic { get; set; }

        public int imgCount { get; set; }

        public string img
        {
            get
            {
                string value = null, url = "https://cdn.linq.app/subcategories/{2}/{0}/{1}_crop_sq.jpg";
                if (imgCount < 1)
                    return null;
                int counter = new Random().Next(1, imgCount + 1);
                value = string.Format(url, id, counter, "xxxhdpi");
                return value;
            }
        }

        public int Compare(SubCategory x, SubCategory y)
        {
            int value = string.Compare(x.subCategory, y.subCategory);
            if (value > 0)
                return 1;
            else if (value < 0)
                return -1;
            return 0;
        }
    }
}
