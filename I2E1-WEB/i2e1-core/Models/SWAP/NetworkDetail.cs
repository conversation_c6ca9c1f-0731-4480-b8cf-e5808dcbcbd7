using i2e1_basics.Utilities;
using i2e1_core.Models.Client;
using i2e1_core.Utilities;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Data.SqlClient;
using System.Web;

namespace i2e1_core.Models.SWAP
{
    public enum SSIDType
    {
        OPEN = 0,
        PROTECTED,
        HIDDEN
    }

    public enum LocationType
    {
        I2E1=1,
        LINQ,
        OTHER = 3,
        I2E1_WANI = 4,
        OTHER_WANI = 5,
        LINQ_BUSINESS = 6
    }

    public class BasicLinqObject
    {
        public BasicLinqObject()
        {
            this.EventTime = DateTime.UtcNow.ToString("yyyy-MM-dd HH:mm:ss");
        }

        public string appId { get; set; }
        public string mobile { get; set; }
        public string EventTime { get; }
    }

    public class ShopPhoto
    {
        private string pPhotoUrl;

        public string photoUrl {
            get
            {
                return pPhotoUrl;
            }
            set {
                if (string.IsNullOrEmpty(value))
                    pPhotoUrl = value;
                else
                    pPhotoUrl = value.Replace(".webp", ".jpg");
            }
        }

        public static string GetPhotoUrlForDB(string photoUrl,int listid=0)
        {
            if (!string.IsNullOrEmpty(photoUrl))
            {
                int firstIndex = photoUrl.IndexOf("://");

                if (listid>0 && photoUrl.IndexOf("/brands") > -1)
                {
                    return "/listings/original/"+listid+photoUrl.Substring(photoUrl.LastIndexOf('/'));
                }

                if(firstIndex != -1)
                {
                    photoUrl = photoUrl.Substring(photoUrl.IndexOf('/', firstIndex + 3));
                }
                return photoUrl.Replace("/i2e1-linq", "");
            }
            return photoUrl;
        }

        public static string GetFullPhotoUrl(string photoUrl, int listingId, int pathIndexOffset = 1)
        {
            if (string.IsNullOrEmpty(photoUrl))
                return string.Empty;
            if (photoUrl.Contains("cdn.linq.app"))
                return photoUrl;

            photoUrl = photoUrl.Replace(".webp", ".jpg");

            if (photoUrl.Contains("/brands/") || photoUrl.Contains("/listings/"))
            {
                photoUrl = photoUrl.Replace("/i2e1-linq", "");
                return CoreUtil.createCompleteUrl(photoUrl);
            }


            if(pathIndexOffset == 2 && photoUrl.Contains("/"))
            {
                return string.Format("https://cdn.linq.app/listings/{0}/{1}", "original", photoUrl);
            }

            return string.Format("https://cdn.linq.app/listings/{2}/{0}/{1}", listingId, HttpUtility.UrlEncode(photoUrl), "original");
        }
    }


    public class HotSpot 
    {
        public string wifi_ssid { get; set; }
        public string latitude { get; set; }
        public string longitude { get; set; }
        public string power { get; set; }
        public SSIDType ssidType { get; set; } //open or protected
        public string password { get; set; }
        public string wifi_mac_address { get; set; }
        public string wifi_lan_address { get; set; }
        public string provider { get; set; }
        public bool connected { get; set; }
        public int count { get; set; }
    }

    public class ShareWifiPolicy
    {
        public string appId { get; set; }
        public string mobile { get; set; }
        public string wifi_mac_address { get; set; }
        public long dataLimit { get; set; }
        public long timeLimit { get; set; }
        public List<string> vipList { get; set; }
        public List<string> whiteList { get; set; }
        public List<string> blackList { get; set; }
    }

    public class NetworkDetail : HotSpot
    {
        public LongIdInfo nasid { get; set; }
        public string distance { get; set; }
        public string rating { get; set; }
        public string name { get; set; }
        public string city { get; set; }
        public string state { get; set; }
        public string address { get; set; }
        public string marketPlace { get; set; }
        public string category { get; set; }
        public string subCategory { get; set; }
        public string partnerCode { get; set; }
        public string brandName { get; set; }
        public string mobile { get; set; }
        public string googlePlaceId { get; set; }
        public DateTime lastUpdated { get; set; }
        public bool isOpen { get; set; }
        public LocationType type { get; set; }
        public JObject extra { get; set; }
        public ShareWifiPolicy wifi_extras { get; set; }
        public bool isActive { get; set; }
        public int listingId { get; set; }
        public byte offerContentType { get; set; }

        public void FillFromReader(SqlDataReader reader, string fromMobile)
        {

            string lat = reader["latitude"].ToString();
            string lng = reader["longitude"].ToString();

            if (!string.IsNullOrEmpty(lat) && !string.IsNullOrEmpty(lng))
            {
                wifi_ssid = reader["ssid"].ToString();
                wifi_lan_address = reader["mac_address"].ToString();
                wifi_mac_address = reader["mac_address"].ToString();
                name = reader["shop_name"].ToString();
                category = reader["category"].ToString();
                subCategory = reader["sub_category"].ToString();
                latitude = lat;
                longitude = lng;
                address = reader["address"].ToString();
                marketPlace = reader["market_place"].ToString();
                city = reader["city"].ToString();
                state = reader["state"].ToString();
                type = (LocationType)Enum.Parse(typeof(LocationType), reader["type"].ToString());
                password = reader["password"].ToString();
                extra = reader["extras"] == DBNull.Value ? new JObject() : JsonConvert.DeserializeObject<JObject>(reader["extras"].ToString());
                listingId = reader["linq_id"] == DBNull.Value ? 0 : (int)reader["linq_id"];
                isActive = (bool)reader["isactive"];
                offerContentType = (byte)reader["offer_content_type"];
                if (extra["thumb"] == null || extra["thumb"].ToString() == string.Empty)
                {
                    var subCatID = reader["sub_cat_id"].ToString();
                    extra["thumb"] = "https://cdn.linq.app/subcategories/mdpi/" + (string.IsNullOrEmpty(subCatID) ? "0" : subCatID) + "/1_crop_sq.jpg";
                }
                else
                    extra["thumb"] = ShopPhoto.GetFullPhotoUrl(extra["thumb"].ToString(), listingId);

                if (extra["cover"] != null)
                    extra["cover"] = ShopPhoto.GetFullPhotoUrl(extra["cover"].ToString(), listingId);
            }
        }
    }

    public class NetworkList
    {
        public double latitude1 { get; set; }

        public double longitude1 { get; set; }

        public double latitude2 { get; set; }

        public double longitude2 { get; set; }

        public List<NetworkDetail> networkDetails {get;set;}

    }

    public class Event
    {
        public Event()
        {
            this.Origin = "NA";
            this.Type = "NA";
            this.Sub_type = "NA";
            this.Latitude = 0;
            this.Longitude = 0;
        }

        public string Id { get; set; }

        public string Origin { get; set; }

        public string Type { get; set; }

        public string Sub_type { get; set; }

        public string Data { get; set; }

        public double Latitude { get; set; }

        public double Longitude { get; set; }

        public long EventOffset
        {
            get
            {
                return 0;
            }
            set
            {
                EventTime = DateTime.UtcNow.AddSeconds(value);
            }
        }

        public DateTime EventTime { get; set; }
      

    }

    public class LinqEvents : BasicLinqObject
    {
        public List<Event> Events { get; set; }

    }

    public class CallLogs
    {
        public string mobile { get; set; }

        public string Type { get; set; }

        public string Data { get; set; }

        public DateTime EventTime { get; set; }

    }

    public class MyLinqCallLogs : BasicLinqObject
    {
        public List<CallLogs> Events { get; set; }

    }

    public class LinqListingEvents : BasicLinqObject
    {
        public string Id { get; set; }
        public string SessionId { get; set; }
        public string Referrer { get; set; }
        public double Latitude { get; set; }
        public double Longitude { get; set; }
        public string Device { get; set; }
        public string Utm_Source { get; set; }
        public string Utm_Medium { get; set; }
        public string Utm_Campaign { get; set; }
        public string Url { get; set; }
        public int ListingId { get; set; }
        public string Origin { get; set; }
        public bool HomeScreen { get; set; }
        public string Data { get; set; }
    }

    public class LinqSessionHeartBeat
    {
        public string Id { get; set; }
        public string SessionId { get; set; }
        public string EventTime { get; set; }
        public int ListingId { get; set; }
    }

    public class UserFeedBack : BasicLinqObject
    {
        public string Subject { get; set; }

        public string FeedBack { get; set; }

        public string[] Images{ get; set; }

        public List<Event> Events { get; set; }

    }

    public class LocationDetails
    {
        public string LocationName { get; set; }

        public string WeatherType { get; set; }

        public string Temprature { get; set; }

        public string WeatherIcon { get; set; }

        public string hdpi { get; set; }

        public string ldpi { get; set; }

        public string mdpi { get; set; }

        public string xhdpi { get; set; }

        public string xxhdpi { get; set; }

        public string xxxhdpi { get; set; }

    }

    public class Linq_Brand : BasicLinqObject
    {
        public int brand_id { get; set; }
        public int client_id { get; set; }
        public int listId { get; set; }
        public string brand_name { get; set; }
        public string brand_owner_name { get; set; }
        public int brand_category { get; set; }
        public List<SubCategory> sub_category { get; set; }
        public string tempSubcat { get; set; }
        public string cover_photo { get; set; }
        public string profile_photo { get; set; }
        public List<string> photos { get; set; }
        public JObject social_media { get;set; }
        public JObject extras { get; set; }
        public string famous_for { get; set; }
        public List<string> products { get; set; }
        public string added_time { get; set; }
        public string modified_time { get; set; }
    }

    public enum CommentOP
    {
        ADD_COMMENT=0,
        EDIT_COMMENT=1,
        DELETE_COMMENT=2,
        ADD_REPLY=3,
        EDIT_REPLY=4,
        DELETE_REPLY=5,
        REPORT_COMMENT=6
    }

    public enum CommentType
    {
        COMMENT = 0,
        REPLY
    }

    public class CommentData : BasicLinqObject
    {
        public int commentId { get; set; }

        public int ParentId { get; set; }

        public CommentOP operation { get; set; }

        public CommentType type { get; set; }

        public string comment { get; set; }

        public string commentUsername { get; set; }

        public DateTime commentTime { get; set; }

        public int listId { get; set; }

        public string listingId { get; set; }

        public int reportCount { get; set; }

        public List<CommentData> childs { get; set; }

    }

}