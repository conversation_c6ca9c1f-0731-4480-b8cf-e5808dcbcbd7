using i2e1_basics.Models;
using i2e1_basics.Utilities;
using i2e1_core.services;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Linq;
using wiom_login_share.Models;

namespace i2e1_core.Models
{
    public class Account
    {
        public const int NEGATIVE_BALANCE_LIMIT = -3000;

        public string name { get; set; }
        public DateTime addedTime { get; set; }
        public LongIdInfo id { get; set; }
        public string gst { get; set; }
        public double balance { get; set; }
        public string address { get; set; }
        public LongIdInfo googleAddressId { get; set; }
        public string aadhar { get; set; }
        public Dictionary<string, string> applications { get; set; }
        public string extraData { get; set; }
        public AccountExtraData extraDataObject { get; set; }
        public string logicalGroup { get; set; }
        public Dictionary<string, string> options { get; set; }

        public bool hasService(App app)
        {
            if (applications.ContainsKey(app.ToString()))
                return true;
            return false;
        }

        public List<UserProfile> GetUsers()
        {
            if (options != null && options["usersJson"] != null)
            {
                var users = JsonConvert.DeserializeObject<JArray>(options["usersJson"]);
                if (users.Count > 0)
                    return users.Select(m => new UserProfile()
                    {
                        id = m["user_id"] == null ? null : new LongIdInfo(this.id.shard_id, DBObjectType.ADMIN_TYPE, m["user_id"]),
                        name = m["name"] == null ? null : m["name"].ToString(),
                        username = m["username"].ToString(),
                        mobile = m["username"].ToString()
                    }).ToList();
            }
            return null;
        }

        public virtual void SaveAccount(long shardId, out ErrorCode errorCode)
        {
            errorCode = null;
            var account = CoreAccountService.SaveAccount(shardId, this);
            this.id = account.id;
        }

        public virtual void SaveAccount(LongIdInfo parentAccountId, out ErrorCode errorCode)
        {
            errorCode = null;
            var account = CoreAccountService.SaveAccount(parentAccountId.shard_id, this);
            this.id = account.id;
        }

        public bool IsWiomMember()
        {
            Logger.GetInstance().Info("isWiomMember called with : " + this.id);
            bool wiom_member_status = false;
            string extra;
            this.applications.TryGetValue("HOME_ROUTER", out extra);
            if (!String.IsNullOrEmpty(extra))
            {
                JObject json = JObject.Parse(extra);
                if (json["wiom_member"] != null && ((DateTime)json["expiry_date"]) >= DateTime.UtcNow)
                {
                    wiom_member_status = (bool)json["wiom_member"];
                }
            }
            return wiom_member_status;
        }
    }

    [Serializable]
    public class AccountSearchType : SearchBaseType
    {
        public LongIdInfo accountId { get; set; }
        public string term { get; set; }
        public string appTermIn { get; set; }
        public string appMappingParams { get; set; }
        public string userTerm { get; set; }
        public string nasTerm { get; set; }
        public string logicalGroup { get; set; }
        public string logicalGroupNotIn { get; set; }
        public int hasBalance { get; set; }
    }

    [Serializable]
    [JsonObject(ItemNullValueHandling = NullValueHandling.Ignore)]
    public class AccountExtraData
    {
        public int cntrlr_nas { get; set; }
        public int cluster { get; set; }
        public string lat { get; set; }
        public string lng { get; set; }
        public Dictionary<string, object> poc { get; set; }
        public string installationStatus { get; set; }
    }

    public class AccountBalanceEntry
    {
        public int id { get; set; }
        public LongIdInfo accountId { get; set; }
        public LongIdInfo userId { get; set; }
        public double balance { get; set; }
        public string source { get; set; }
        public double transactionAmount { get; set; }
        [JsonConverter(typeof(Newtonsoft.Json.Converters.StringEnumConverter))]
        public AccountPaymentActions action { get; set; }
        public string transactionId { get; set; }
        public Dictionary<string, Object> extraData { get; set; }
        public string remark { get; set; }
        public DateTime addedTime { get; set; }

    }

    public class BalanceEntrySearch : SearchBaseType
    {
        public string mobile { get; set; }
        public LongIdInfo nasid { get; set; }
        public LongIdInfo accountId { get; set; }
        public List<string> actionsIn { get; set; }
        public List<string> sourceIn { get; set; }
        public string extraData { get; set; }
        public string deviceId { get; set; }
        public List<string> transactionTypeIn { get; set; }
    }

    public class Billing
    {
        public long id { get; set; }
        public LongIdInfo nasid { get; set; }
        public LongIdInfo accountId { get; set; }
        public string transactionId { get; set; }
        public string mobile { get; set; }
        public string name { get; set; }
        public string address { get; set; }
        public string product { get; set; }
        public string remark { get; set; }
        public byte quantity { get; set; }
        public double price { get; set; }
        public double gst { get; set; }
        public JObject extraData { get; set; }
        public DateTime addedTime { get; set; }
        public DateTime dueDate { get; set; }
        public bool paid { get; set; }
        public DateTime? paidOn { get; set; }
        public string paymentMode { get; set; }
    }

    public enum AccountPaymentActions
    {
        ADD_BALANCE,
        ADD_POINTS_IN_COUPON_RESELLER_ACCOUNT,
        COUPONS_CREATED,
        BILL_PAID,
        COMMISSION_ADDED,
        RATING_BONUS_ADDED,
        AMOUNT_WITHDRAWN,
        SETTLEMENT,
        DISCOUNT_ADDED,
        DISCOUNT_REDEEMED,
        ONU_BONUS_ADDED,
        PERFORMANCE_INCENTIVE,
        PENALTY,
        REFUND,
        DAILY_RECURRING_AMOUNT,
        VERIFIED_BOOKING_PAYMENT,
    }

    public class ExpiryNasIdInfo
    {
        public string mobile;
        public string otp_expiry_time;
        public LongIdInfo router_nas_id;
        public DateTime firstRechargeTime;
        public ExpiryNasIdInfo(LongIdInfo router_nas_id, string mobile, string otp_expiry_time)
        {
            this.mobile = mobile;
            this.otp_expiry_time = otp_expiry_time;
            this.router_nas_id = router_nas_id;
        }
        public ExpiryNasIdInfo(LongIdInfo router_nas_id, string mobile, string otp_expiry_time, DateTime firstRechargeTime)
        {
            this.mobile = mobile;
            this.otp_expiry_time = otp_expiry_time;
            this.router_nas_id = router_nas_id;
            this.firstRechargeTime = firstRechargeTime;
        }
    }
}
