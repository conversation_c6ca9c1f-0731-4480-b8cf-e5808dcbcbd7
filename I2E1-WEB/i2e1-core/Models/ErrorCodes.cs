using i2e1_basics.Models;

namespace I2E1_WEB.Models;

public class ErrorCodes : CoreErrorCodes
{
    public static ErrorCode PLAN_NOT_FOUND = new ErrorCode("LS005", "PLAN_NOT_FOUND");

    public static ErrorCode NON_GOLD_CODE_USED_FOR_GOLD = new ErrorCode("LS006", "NON_GOLD_CODE_USED_FOR_GOLD");

    public static ErrorCode VOUCHER_CODE_INVALID = new ErrorCode("LS007", "VOUCHER_CODE_INVALID");

    public static ErrorCode VOUCHER_ALREADY_USED = new ErrorCode("LS008", "VOUCHER_ALREADY_USED");

    public static ErrorCode NON_PDO_CODE_USED_FOR_PDO = new ErrorCode("LS009", "NON_PDO_CODE_USED_FOR_PDO");

    public static ErrorCode VOUCHER_ALREADY_USED_BY_SAME_USER = new ErrorCode("LS010", "VOUCHER_ALREADY_USED_BY_SAME_USER");

    public static ErrorCode ZERO_NASID = new ErrorCode("LS011", "ZERO_NASID");

    public static ErrorCode BLANK_MOBILE = new ErrorCode("LS012", "BLANK_MOBILE");
} 