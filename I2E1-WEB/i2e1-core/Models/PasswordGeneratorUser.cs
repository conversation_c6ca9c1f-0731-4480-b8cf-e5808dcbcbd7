using i2e1_basics.Utilities;
using i2e1_core.Models;
using i2e1_core.Utilities;
using System;
using wiom_login_share.Models;

namespace i2e1_core.Models
{
    [Serializable]
    public class PasswordGeneratorUser
    {
        public PasswordGeneratorUser()
        {
        }
        public PasswordGeneratorUser(string username, LongIdInfo nasid)
        {
            this.username = username;
            this.nasid = nasid;
        }

        public void ParseConfigString(string parameters)
        {
            string[] param = parameters.Split(',');
            authType = (AuthType)(int.Parse(param[0]));

            if (param.Length > 4 && !string.IsNullOrEmpty(param[4]))
            changeDataPlan = bool.Parse(param[4]);

            if (param.Length > 5 && !string.IsNullOrEmpty(param[5]))
                askName = bool.Parse(param[5]);

            if (param.Length > 6 && !string.IsNullOrEmpty(param[6]))
                askEmail = bool.Parse(param[6]);

            if (param.Length > 8 && !string.IsNullOrEmpty(param[8]))
                askAccessCode = bool.Parse(param[8]);
        }

        public string username { get; set; }

        public LongIdInfo nasid { get; set; }

        public int storeGroupId { get; set; }

        public AuthType authType { get; set; }

        public bool changeDataPlan { get; set; }

        public bool askName { get; set; }

        public bool askEmail { get; set; }

        public bool askAccessCode { get; set; }
    }
}