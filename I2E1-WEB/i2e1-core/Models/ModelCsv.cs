using i2e1_basics.Utilities;
using System;

namespace i2e1_core.Models
{

    [Serializable]
    public class PortalCsv
    {
        public string clientName { get; set; }

        public string partnerName { get; set; }

        public LongIdInfo nasid { get; set; }

        public int marketplaceid { get; set; }

        public string brandname { get; set; }

        public string Address { get; set; }
        public string City { get; set; }
        public string State { get; set; }
        public string Pincode { get; set; }
        public string Category { get; set; }
        public string Subcategory { get; set; }
        public string MicroCategory { get; set; }
        public string Tags { get; set; }
        public string retagdate { get; set; }
        public string Status { get; set; }
        public string Mode { get; set; }
        public int MonitorModeNasId { get; set; }
        public string Salesperson { get; set; }
        public string Startdate { get; set; }
        public string Devicetype { get; set; }
        public string Installerperson { get; set; }
        public string DeviceMAC { get; set; }
        public string Deviceversion { get; set; }

        public string latitude { get; set; }

        public string longitude { get; set; }


    }

    public class VipCSVData
    {
        public LongIdInfo nasid { get; set; }
        public string mobile { get; set; }
        public string name { get; set; }
    }

    public class AuthCSVData
    {
        public long nasid { get; set; }
        public int authGroup { get; set; }
    }

    public class NasCSVData
    {
        public long nasid { get; set; }
    }

    public class SecondaryNasCSVData : NasCSVData
    {
        public long secondarynasid { get; set; }
    }

    public class StoreContactCSVData : VipCSVData
    {
        public string email { get; set; }
    }
}