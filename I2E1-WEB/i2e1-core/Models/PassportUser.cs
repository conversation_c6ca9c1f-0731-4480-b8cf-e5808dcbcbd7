using i2e1_core.Utilities;
using Newtonsoft.Json;
using System;

namespace i2e1_core.Models
{
    /*[Serializable]
    public class PassportUser : FDMConfig
    {
        public string name { get; set; }

        public LongIdInfo nasid { get; set; }

        public int storeGroupId { get; set; }

        public string email { get; set; }

        public string macId { get; set; }

        public string transactionId { get; set; }

        public PassportUserExtraData extraDataObject { get; set; }

        public bool authState { get; set; }

        public int charges { get; set; }

        public LongIdInfo createdBy { get; set; }

        public DateTime createdOn { get; set; }

        public LongIdInfo updatedBy { get; set; } //admin_id of management user who created OR updated voucher

        public string status { get; set; } // its should value fall in (unused, active, expired, disconnected)
    }

    [Serializable]
    [JsonObject(ItemNullValueHandling = NullValueHandling.Ignore)]
    public class PassportUserExtraData
    {
        public string bought_from { get; set; } //created from portal, for ex: coupon_reseller, wiom_dashboard, services_portal
        public string bought_from_nas { get; set; } //to be filled in case bought_from='wiom_dashboard'
        public string plan_id { get; set; }//referrence to id of t_plan_configuration
        public string sent_to { get; set; }
        public string payment_mode { get; set; }
        public string state { get; set; } // its should value falls in (created, viewed, sent)
        public string data_limit { get; set; }
        public string time_limit { get; set; }
        public string speed { get; set; }
        public int speedMbps { get; set; }
        public bool? isTemp { get; set; }
        public int totalPaid { get; set; }
        public int cashFee { get; set; }
        public string firstRechargeTime { get; set; }
        public string connection { get; set; }
        public LongIdInfo accountId { get; set; }
    }*/
}