using i2e1_basics.Utilities;
using i2e1_core.Utilities;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.StaticFiles;
using Microsoft.Extensions.Caching.Memory;
using System;
using System.IO;

namespace i2e1_core.Models;

public class CoreController : Controller
{
    public ActionResult RedirectTo(string url)
    {
        return base.Redirect(url);
    }

    public ActionResult ContentResult(IMemoryCache memoryCache, string filePath)
    {
        var deployedOn = Environment.GetEnvironmentVariable("DEPLOYED_ON");
        if (!string.IsNullOrEmpty(deployedOn))
        {
            deployedOn = "/" + deployedOn;
        }
        var response = MemoryCacheHelper.GetValueFromCache(memoryCache, filePath, () =>
        {
            using (var client = new ImpatientWebClient())
            {
                var str = client.DownloadString(I2e1ConfigurationManager.GetInstance().GetSetting("S3_BASE_URL") + deployedOn + filePath);
                Logger.GetInstance().Info(str);
                return str;
            }

        }, new TimeSpan(1, 0, 0));
        return Content(response, "text/html");
    }

    public PhysicalFileResult FileResult(IMemoryCache memoryCache, string filePath)
    {
        var deployedOn = Environment.GetEnvironmentVariable("DEPLOYED_ON");
        var fileLocalPath = AppDomain.CurrentDomain.BaseDirectory + filePath;

        if (!string.IsNullOrEmpty(deployedOn))
        {
            deployedOn = "/" + deployedOn;
        }

        try
        {
			var response = MemoryCacheHelper.GetValueFromCache(memoryCache, filePath, () =>
			{
				using (var client = new ImpatientWebClient())
				{
					int index = fileLocalPath.LastIndexOf('/');
					Directory.CreateDirectory(fileLocalPath.Substring(0, index));
					client.DownloadFile(I2e1ConfigurationManager.GetInstance().GetSetting("S3_BASE_URL") + deployedOn + filePath, fileLocalPath);
					return true;
				}
			}, new TimeSpan(1, 0, 0));

			new FileExtensionContentTypeProvider().TryGetContentType(fileLocalPath, out var contentType);
			return new PhysicalFileResult(fileLocalPath, contentType);
		}
        catch (Exception ex)
        {
            Logger.GetInstance().Error(ex.ToString());
			return new PhysicalFileResult(AppDomain.CurrentDomain.BaseDirectory + "/ErrorPages/404.html", "text/html");
		}
        
    }
}
