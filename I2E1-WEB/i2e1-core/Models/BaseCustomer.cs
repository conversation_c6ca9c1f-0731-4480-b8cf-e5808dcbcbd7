using i2e1_basics.Utilities;
using i2e1_core.services;
using System;
using System.Collections.Generic;

namespace i2e1_core.Models
{
    [Serializable]
    public class WGPolicy
    {
        public WgStatus status { get; set; }
        public int restartFee { get; set; }

        public static WGPolicy GetDefault()
        {
            return new WGPolicy()
            {
                status = WgStatus.PAID,
                restartFee = 0
            };
        }
    }

    [Serializable]
    public class BaseCustomer
    {
        public LongIdInfo accountId { get; set; }
        public LongIdInfo installerUserId { get; set; }
        public LongIdInfo lcoAccountId { get; set; }
        public LongIdInfo nasid { get; set; }
        public LongIdInfo googleAddressId { get; set; }


        public long id { get; set; }
        public string name { get; set; }
        public string deviceId { get; set; }
        public string firmware { get; set; }
        public string phoneNumber { get; set; }
        public string address { get; set; }
        public string lcoName { get; set; }
        public string phase { get; set; }
        public WgStatus? wgStatus { get; set; }
        public string routerState { get; set; }
        public DateTime addedTime { get; set; }
        public DateTime modifiedTime { get; set; }
        public DateTime? lastPingTime { get; set; }
        public DateTime? planExpiryTime { get; set; }
        public string leadType { get; set; }
        public int renewalCount { get; set; }
        public Dictionary<string, object> extraData;
    }

    [Serializable]
    public class CustomerSearch: SearchBaseType {
        public LongIdInfo nasid { get; set; }
        public LongIdInfo lcoAccountId { get; set; }
        public string number { get; set; }
        public string name { get; set; }
        public string routerState { get; set; }
        public string lcoName { get; set; }
        public string address { get; set; }
        public List<string> wgStatusIn { get; set; }
        public string wgStatus { get; set; }
        public string deviceId { get; set; }
        public string fromPlanExpiry { get; set; }
    }
}

