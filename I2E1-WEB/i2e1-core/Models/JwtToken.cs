using i2e1_basics.Utilities;
using i2e1_core.Utilities;
using Microsoft.AspNetCore.Http;
using Newtonsoft.Json.Linq;
using System.Collections.Generic;
using wiom_login_share.Models;

namespace i2e1_core.Models
{
    public class JwtToken
    {
        public string Token { get; set; }
        public string RefreshToken { get; set; }
    }

    public class JwtObject
    {
        public LongIdInfo accountId { get; set; }
        public LongIdInfo userId { get; set; }
        public LongIdInfo adminId { get; set; }
        public string deviceId { get; set; }
        public string username { get; set; }
        public LongIdInfo nasId { get; set; }
        public long partnerId { get; set; }
        public long clientId { get; set; }
        public int storeGroupId { get; set; }
        public string mobile { get; set; }
        public string loginName { get; set; }
        public string adminName { get; set; }
        public string email { get; set; }
        public string wl_token { get; set; }
        public string i2e1_admin_token { get; set; }
        public AdminUserType userType { get; set; }
        public Dictionary<string, int> features { get; set; }
        public App app { get; set; }
        public JObject adminExtraData { get; set; }

        public static JwtObject GetJWTObject(HttpContext HttpContext)
        {
            JwtObject jwtObject = (JwtObject)HttpContext.Items[Constants.JWT_OBJECT];
            return jwtObject;
        }
        public static ManagementUser GetManagementUser(JwtObject jwtObject)
        {
            if(jwtObject == null)
                return null;

            ManagementUser managementUser = new ManagementUser();
            managementUser.userid = jwtObject.adminId;
            managementUser.token = jwtObject.i2e1_admin_token;
            managementUser.userType = jwtObject.userType;
            managementUser.features = jwtObject.features;
            managementUser.contact_no = jwtObject.mobile;
            managementUser.username = jwtObject.username;
            managementUser.name = jwtObject.adminName;
            managementUser.email = jwtObject.email;
            managementUser.extraData = jwtObject.adminExtraData;

            return managementUser;
        }
        public static ManagementUser GetManagementUser(HttpContext httpContext)
        {
            JwtObject jwtObject = GetJWTObject(httpContext);
            return GetManagementUser(jwtObject);
        }
        public static LoginUser GetLoginUser(JwtObject jwtObject)
        {
            if (jwtObject == null)
                return null;

            LoginUser loginUser = new LoginUser();
            loginUser.id = jwtObject.userId;
            loginUser.mobile = jwtObject.mobile;
            loginUser.token = jwtObject.wl_token;
            loginUser.name = jwtObject.loginName;

            return loginUser;
        }


    }
    
}
