using i2e1_basics.Utilities;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;

namespace i2e1_core.Models
{

    [Serializable]
    public enum AdminAuthType
    {
        CUSTOM = 0,
        GMAIL,
        LINQ_ADMIN = 200,
        WIOM
    }

    [Serializable]
    public enum AdminUserType
    {
        STANDARD = 0,
        ADMIN =1,
        SUPER_ADMIN = 2,
        READ_ONLY_ADMIN = 3,
        INTERNAL = 5
    }

    [Serializable]
    public class ManagementUser
    {
        private AdminUserType pUserType;
        private string pToken;
        
        public LongIdInfo userid { get; set; }

        public string username { get; set; }
        
        public string token { 
            get {
                if (pToken == null || pToken.Contains("$") || userid == null)
                    return pToken;
                return userid.shard_id + "$" + pToken;
            }
            set
            {
                pToken = value;
            }
        }

        public string email {get;set;}

        public string password { get; set; }

        public string contact_no { get; set; }

        public string aadhar { get; set; }

        public AdminAuthType authType { get; set; }

        public AdminUserType userType 
        {
            get
            {
                return pUserType;
            }
            set
            {
                pUserType = value;
            }
        }

        public bool email_verified {get;set;}

        public bool is_password_temporary { get; set; }

        public string name {get;set;}

        public Dictionary<string, int> features { get; set; }

        public LongIdInfo parentAdminUserId { get; set; }

        public string parent { get; set; }

        public string product { get; set; }

        public int partnerId { get; set; }
        public int clientId { get; set; }
        public int active { get; set; }

        public DateTime tokengenratedtime { get; set; }

        public Boolean canBeImpersonatedBy(ManagementUser adminInPower)
        {
            bool result = false;
           
            result = (this.parentAdminUserId == adminInPower.userid);
            result = (result ||
                adminInPower.userType == AdminUserType.READ_ONLY_ADMIN ||
                adminInPower.userType == AdminUserType.ADMIN);
            return result;
        }

        [JsonProperty(PropertyName = "picture")]
        public string googleProfilePicture { get; set; }

        [JsonProperty(PropertyName = "given_name")]
        public string firstName { get; set; }

        [JsonProperty(PropertyName = "family_name")]
        public string lastName { get; set; }

        public JObject extraData { get; set; }

        public bool hasRole(string role) {
            if (extraData != null && extraData.TryGetValue("accountParams", out var apms))
            {
                var accountParams = JsonConvert.DeserializeObject<Dictionary<string, string>>(apms.ToString());
                return (accountParams != null && accountParams.TryGetValue("role", out string r) && r == role);
            }

            return false;
        }
    }

}