using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace i2e1_core.Models
{
    [Serializable]
    public class LandingPage
    {
        public int nasid { get; set; }

        public string pageTitle { get; set; }

        public string facebookLikeUrl { get; set; }

        public string storeLogo { get; set; }

        public List<Icon> icons { get; set; }
    }
    [Serializable]
    public class Icon
    {
        public string title { get; set; }

        public string url { get; set; }

        public string imageUrl { get; set; }

        public int? nasid { get; set; }

        public int? displayOrder { get; set; }

        public int? displayColumns { get; set; }

        public string experimentId { get; set; }
    }
}
