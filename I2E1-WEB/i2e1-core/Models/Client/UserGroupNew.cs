using i2e1_basics.Utilities;
using System;
using System.Collections.Generic;
using wiom_router_api.Models;

namespace i2e1_core.Models.Client
{
    [Serializable]
    public class UserGroupNew
    {
        public string groupName { get; set; }
        public int groupId { get; set; }
        public LongIdInfo adminId { get; set; }
        public string values { get; set; }
        public Dictionary<string, BasicConfig> basicConfigs { get; set; }

        public UserGroupNew(int groupId, string groupName, string values, LongIdInfo adminId)
        {
            this.groupId = groupId;
            this.adminId = adminId;
            this.groupName = groupName;
            this.values = values;
        }

        public UserGroupNew()
        {

        }
    }
    public class UserGroup
    {
        public string groupName { get; set; }

        public int groupId { get; set; }

        public LongIdInfo nasid { get; set; }

        public string values { get; set; }

        public List<RouterRadiusConfig> radiusConfig { get; set; }

        public List<RouterConfig> routerConfig { get; set; }
    }
    public class RouterConfig : Config
    {
        public RouterConfig()
        {
        }

        private ListCheckDataType pConfigType;
        public ListCheckDataType configType
        {
            get
            {
                return pConfigType;
            }
            set
            {
                pConfigType = value;
                switch (pConfigType)
                {
                    case ListCheckDataType.WHITE_LIST: text = "Whitelist Phone Numbers"; dataType = ConfigDataType.LIST; break;
                    case ListCheckDataType.VIP_LIST: text = "VIP Phone Number List"; dataType = ConfigDataType.LIST; break;
                    // Not a White List but a VIP List
                    case ListCheckDataType.MAC_WHITELISTING:
                        text = "VIP MAC Id List"; dataType = ConfigDataType.LIST; break;
                    case ListCheckDataType.MAC_BLACKLISTING:
                        text = "MAC Black List"; dataType = ConfigDataType.LIST; break;
                    case ListCheckDataType.SSID:
                        text = "Router SSID"; dataType = ConfigDataType.LIST; break;
                    case ListCheckDataType.SSID_PASSWORD:
                        text = "Router SSID Password"; dataType = ConfigDataType.LIST; break;
                }
            }
        }
    }
}
