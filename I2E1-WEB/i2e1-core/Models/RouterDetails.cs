using i2e1_basics.Utilities;
using i2e1_core.Utilities;
using wifidog_core.Models;

namespace i2e1_core.Models
{
    public enum ConfigDataType
    {
        STRING = 0,
        SPEED,
        DATA,
        TIME,
        LIST
    }


    public class Config
    {
        public Config()
        {
            this.value = new NameValuePair();
        }

        public static bool IsValidValue(Config conf)
        {
            return conf != null && conf.value != null && !string.IsNullOrEmpty(conf.value.value);
        }

        public ConfigDataType dataType { get; set; }

        public string text { get; set; }

        public NameValuePair value { get; set; }
    }

    public class RouterRadiusConfig : Config
    {
        private string pAttribute;

        public RouterRadiusConfig()
        {
        }
        public RouterRadiusConfig(LongIdInfo nasid, string attribute, string value)
        {
            this.nasid = nasid;
            this.attribute = attribute;
            this.value.value = value;
        }
        public string attribute
        {
            get
            {
                return pAttribute;
            }
            set
            {
                pAttribute = value;
                switch (pAttribute)
                {
                    case "Session-Timeout": text = "Session Timeout"; dataType = ConfigDataType.TIME; break;
                    case "ChilliSpot-Bandwidth-Max-Up":
                        text = "Maximum Upload Bandwidth"; dataType = ConfigDataType.SPEED; break;
                    case "ChilliSpot-Bandwidth-Max-Down":
                        text = "Maximum Download Bandwidth"; dataType = ConfigDataType.SPEED; break;
                    case "ChilliSpot-Max-Total-Octets":
                        text = "Maximum data usage / day"; dataType = ConfigDataType.DATA; break;
                }
            }
        }

        public LongIdInfo nasid { get; set; }
    }
}