using i2e1_basics.Models;
using i2e1_basics.Utilities;
using i2e1_core.services;
using i2e1_core.Utilities;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Linq;
using wiom_login_share.Models;

namespace i2e1_core.Models
{
    public class HomeRouterAccount : Account
    {
        public BaseCustomer baseCustomer { get; set; }

        public override void SaveAccount(LongIdInfo lcoAccountId, out ErrorCode errorCode)
        {
            errorCode = null;

            var mgmtUser = CoreUserService.GetAdminUser(lcoAccountId.shard_id, baseCustomer.phoneNumber);
            if (mgmtUser != null)
            {
                var account = CoreAccountService.GetAccountWithUserId(mgmtUser.userid, true);
                if(account != null)
                {
                    this.id = account.id;
                    this.applications = account.applications;
                    this.options = account.options;
                }
            }

            if (id == null)
            {
                if(string.IsNullOrEmpty(this.name))
                    name = "HOME_ROUTER_" + baseCustomer.phoneNumber;
                base.SaveAccount(lcoAccountId.shard_id, out errorCode);
                applications = new Dictionary<string, string>();
                if(options == null)
                    options = new Dictionary<string, string>();
            }

            if (id.local_value > 0)
            {
                Dictionary<string, object> extraParams = null;
                if (lcoAccountId.local_value != 0)
                {
                    var partnerConfigId = CoreAccountService.GetPartnerConfigId(lcoAccountId);
                    extraParams = new Dictionary<string, object>() { { Constants.PARTNER_CONFIGURATION_ID, partnerConfigId } };
                }
                
                if (applications.Count == 0)
                {
                    CoreAccountService.AddApplicationToAccount(id, App.HOME_ROUTER, extraParams);
                }
                else if (applications.Count == 1 && this.hasService(App.HOME_ROUTER))
                {
                    CoreAccountService.AddApplicationToAccount(id, App.HOME_ROUTER, extraParams);
                    Logger.GetInstance().Info(String.Format("AccountController:AddHomeRouterAccount Application HOME_ROUTER already exists for account:{0}, nasid: {1}, hmrUser: {2}", JsonConvert.SerializeObject(this), baseCustomer.nasid, JsonConvert.SerializeObject(baseCustomer)));
                }
                else
                {
                    Logger.GetInstance().Info($"unable to add application HOME_ROUTER because some other application found on account:{id}, mobile:{baseCustomer.phoneNumber}");
                    errorCode = CoreErrorCodes.UNABLE_TO_ADD_APPLICATION;
                    return;
                }

                if (baseCustomer.nasid != null)
                {
                    JArray nases = new JArray();
                    if(options.TryGetValue("nasesJson", out string nasesJson))
                        nases = JsonConvert.DeserializeObject<JArray>(nasesJson);

                    if (nases.Count() == 0)
                    {
                       LongIdInfo nasAcctId = CoreAccountService.GetAccountIdFromMappedId(baseCustomer.nasid, "location");
                        if (nasAcctId != null)
                        {
                            Logger.GetInstance().Info($"Nas {baseCustomer.nasid} already in use");
                            errorCode = CoreErrorCodes.DEVICE_ALREADY_IN_USE;
                            return;
                        }
                        var srvcParams = new Dictionary<string, object>() { { "app", "home_router" } };
                        if(baseCustomer.extraData != null && baseCustomer.extraData.ContainsKey("connection"))
                            srvcParams["connection"] = baseCustomer.extraData["connection"];
                        
                        if (!string.IsNullOrEmpty(baseCustomer.deviceId))
                            srvcParams["deviceId"] = baseCustomer.deviceId;
                        bool status = CoreAccountService.AddLocationToAccount(id, baseCustomer.nasid, srvcParams);
                        if (!status)
                        {
                            Logger.GetInstance().Info($"unable to add nas {baseCustomer.nasid}");
                            errorCode = CoreErrorCodes.DEVICE_ALREADY_IN_USE;
                            return;
                        }
                    }
                    else if (nases.Count() == 1 && nases[0]["nas_id"].ToString() == baseCustomer.nasid.ToString())
                    {
                        Logger.GetInstance().Info(String.Format("AccountController:AddHomeRouterAccount Nas {0} already exists for account:{1}, user: {2}", baseCustomer.nasid, JsonConvert.SerializeObject(this), JsonConvert.SerializeObject(baseCustomer)));
                    }
                    else
                    {
                        CoreAccountService.UpdateLocationToAccount(id, baseCustomer.nasid);
                        Logger.GetInstance().Info($"overriding old nas {nases[0]} on account:{id}, mobile:{baseCustomer.phoneNumber} with new nas :{baseCustomer.nasid}");
                        //errorCode = CoreErrorCodes.ACCOUNT_MAPPED_WITH_OTHER_DEVICE;
                        //return;
                    }
                }

                JArray users = new JArray();
                if (options.TryGetValue("usersJson", out string usersJson))
                    users = JsonConvert.DeserializeObject<JArray>(usersJson);

                if (users.Count() == 0)
                {
                    bool added = CoreAccountService.AddUserToAccount(id, baseCustomer.phoneNumber, baseCustomer.name, baseCustomer.phoneNumber, null, out ManagementUser user);
                    if (!added)
                    {
                        errorCode = CoreErrorCodes.USER_CANNOT_BE_ADDED_TO_ACCOUNT;
                        return;
                    }
                }
                else if (users.Count() == 1 && users[0]["username"].ToString() == baseCustomer.phoneNumber)
                {
                    Logger.GetInstance().Info(String.Format("AccountController:AddHomeRouterAccount User {0} already exists for account:{1}, nasid: {1}", JsonConvert.SerializeObject(baseCustomer), JsonConvert.SerializeObject(this), baseCustomer.nasid));
                }
                else
                {
                    Logger.GetInstance().Info($"unable to add user {baseCustomer.phoneNumber} because multipe user found on account:{id}");
                    errorCode = CoreErrorCodes.USER_CANNOT_BE_ADDED_TO_ACCOUNT;
                    return;
                }
            }
        }

        public bool IsValidHMRAccount()
        {
            return this.hasService(App.HOME_ROUTER);
        }
    }
}
