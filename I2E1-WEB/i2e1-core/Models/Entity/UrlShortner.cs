using Amazon.DynamoDBv2.DataModel;
using i2e1_basics.DynamoUtilities;
using System;
namespace i2e1_core.Models.Entity
{
    [DynamoDBTable("UrlShortner")]
    public class UrlShortner
    {
        [DynamoDBHashKey]
        public string key { get; set; }

        [DynamoDBProperty]
        public string url { get; set; }

        [DynamoDBProperty(typeof(DateTimeUtcConverter))]
        public DateTime addedTime { get; set; } = DateTime.UtcNow;

        [DynamoDBProperty]
        public long ttl { get; set; }
    }
}