using Google.Apis.Auth.OAuth2;
using Google.Apis.Bigquery.v2.Data;
using Google.Cloud.BigQuery.V2;
using i2e1_basics.Database;
using i2e1_basics.Utilities;
using i2e1_core.Models;
using System;
using System.Collections.Generic;
using System.Data.SqlClient;

namespace i2e1_core.Utilities
{
    public delegate void AthenaResponseHandler(Dictionary<string, object> data);
    public class CoreBigQuery
    {
        private static BigQueryClient bigQueryClient;
        public static TableReference P_RADACCT;
        public static TableReference T_ROUTER_SESSIONS;
        public static TableReference T_ONT_MONOTORING;
        public const string dateFormat = "yyyy-MM-dd";


        private static string projectId = "pioneering-axe-398904";
        private static string datasetId = "wiom";
        private static string jsonString = @"{
  ""type"": ""service_account"",
  ""project_id"": ""pioneering-axe-398904"",
  ""private_key_id"": ""1493ff8689ddb6bf68cb91736e058f3fc564a69c"",
  ""private_key"": ""-----B<PERSON><PERSON> PRIVATE KEY-----\nMIIEvgIBADANBgkqhkiG9w0BAQEFAASCBKgwggSkAgEAAoIBAQClHofU3qLDKAdK\nvO2bXCTUnP6pnjQpyYEK4lzkG71pBp6f6ZPjc0WPy0BMjDbZyCTEdtbhdGCQujgp\nHY9Q3Hx34KSTMpfH+EyB0e9MRxE9jAzhISFLfFgb2v/aepahvz9FkkEnTe7+AFEF\niCsEeWgMZX4MN2Q+zQzjILlz/OfGYG+MtdlGs6kM4UiU5facoC36R/FvNRMcy6W8\n8ZZ7LAnXH36IrnBu/upw/Ft8VxO1NV5+2EvmPv9S7qC82/9wfEHr9+AKatPB+c0r\nU2NuIRGtY7K94T3u2bgIGbHxIRN6qzQSS5EiUgGI2VSBmV6kOh5pzgQ755RgGhFM\nuRHtPtLZAgMBAAECggEAC7WUDVjycuN6TOcD9JH+vKMbfhzNdy8mewadbsG212VU\nR2PRjH4mrzFLMIJ6+0uxq8r/pwJRPMIv7F5/xMy1+OxRz2x1jgVCK6yfCyo5A0PU\nv/xQ96DsQKFxpmUuHG9LBdx+yVJCAfo0xK8o2crgQYzK+QPOlJOG9+5gqGSFbTBZ\nP2d3jU66r65pdx2Sm+jTGsCAqHjuXR2ZP2REbGfUTK3h6X9TQUfiRlMujstz1WQv\n5LpUEEUh1bhQ7MHuohwj8FPiMZb8HJxn4Z1Qmaaw5WMVZgcqCe0J2ASBM3EtWov1\npsnDJuCwTjB+zhFnb7AqLS4gBbRaXs71+/+NXB5erwKBgQDhqOWOhs3sIWgdNnEs\nnPTqdRWy3RcDKhznVtN69h6renYSo7uaIOZTOPQkJ96tdbOsyDahI/Dti+qau804\nmLkgKUKzGr59JlesXikgVv8RplkR5mjDlfkFE1ekUU1So1LYPpp17VSkbg/dJ8F5\nGLX5UjJxVfI+SMISDQUg/PB+cwKBgQC7UdvAcpmV5e6ZXnRA3p9ke8VnlOv5Ffcv\nmISgureQoATMvM6pNqaCjRVODZrf4XpoUcoG74SDeka4sMgVTojSzOFg9sTk9zVd\nzh5FBSja+sldxYKN869wjMvmru09l7vwQ1KyWRsLJbRXkHl7x6JWicBbvd1Q2XB3\nKugrA8PqgwKBgQDYJkbUcc2XbBxlNvvLBwEV/1bsgBgF4PXUpfdmJZAVIvUsP4d4\nSCE3ACvi4gnKzx5u10x0p4+kikwLMO6PUsKoyrzoACsMh4idQ4hTQOGLz3Ir0i7x\ngZsIwJFHhNTy3hyBo92iLdXQttgdN3J8Ay5zhcdphjDGdjzmu2/5PC9EfwKBgQCz\nwznddNz5YmBYpLFx83MJblIiNmNCdhbygS5+RNGWpEoW5PZ6oyymSphwgFPpmCvt\nYtg7Ua5csoKeCWDqOaTKj72WXRrVFHwNWpnn6KytgVCvgbTpXzs1Cpk+9w5LNosw\nZps89pAiYXuxML+0zv92htmn8Qwr44+vfOizJvjj2wKBgGt/mTyx9KdlVXNphQkj\nCwvgshCCkdHpTmcDsxeIPuP9q8oGXt9h8qA2BEcnsUsc5tf1/4v0tq+xjYwwI6Ik\nVZ/v+5bLDt1WcXsRhWHBue9rVajzYBI8lg/WXfZ+QJslVEorw402nzmfsJmU6sYz\nm5cqSptP8XwreOykjXdKdl8K\n-----END PRIVATE KEY-----\n"",
  ""client_email"": ""<EMAIL>"",
  ""client_id"": ""117365634444291350793"",
  ""auth_uri"": ""https://accounts.google.com/o/oauth2/auth"",
  ""token_uri"": ""https://oauth2.googleapis.com/token"",
  ""auth_provider_x509_cert_url"": ""https://www.googleapis.com/oauth2/v1/certs"",
  ""client_x509_cert_url"": ""https://www.googleapis.com/robot/v1/metadata/x509/bigqueryapi%40pioneering-axe-398904.iam.gserviceaccount.com"",
  ""universe_domain"": ""googleapis.com""
}";

        static CoreBigQuery()
        {
            bigQueryClient = BigQueryClient.Create(projectId, GoogleCredential.FromJson(jsonString));
            P_RADACCT = bigQueryClient.GetTableReference(datasetId, "p_radacct");
            T_ROUTER_SESSIONS = bigQueryClient.GetTableReference(datasetId, "t_router_sessions");
            T_ONT_MONOTORING = bigQueryClient.GetTableReference(datasetId, "t_ont_monotoring");
        }

        public static bool uploadRunning = false;

        public static void FetchNasUsageFromDB(string query, string groupByOrOrderBy, DateTime from, DateTime to, AthenaResponseHandler responseHandler)
        {
            if (from < DateTime.UtcNow.AddMonths(-1))
                from = DateTime.UtcNow.AddMonths(-1);

            if (to > DateTime.UtcNow.Date)
                to = DateTime.UtcNow.Date;

            DateTime f = from;
            to = to.AddDays(1);
            query += " AND (";
            bool first = true;
            while ((f - to).TotalSeconds < 0)
            {
                if (!first)
                    query += " OR ";
                query += $"(year ={f.Year} and month = {f.Month} and day = {f.Day})";
                f = f.AddDays(1);
                first = false;
            }
            query += ") "+ groupByOrOrderBy;
            Logger.GetInstance().Info($"ReportService:FetchNasUsage: {query}");

            AthenaDataReader athenaDataReader = new AthenaDataReader();
            var rows = athenaDataReader.ExecuteQueryAsync(query).Result;
            bool firstRow = true;

            Dictionary<int, string> columnIndex = new Dictionary<int, string>();
            foreach (var row in rows)
            {
                var fieldCount = row.Data.Count;
                if (firstRow)
                {
                    for (int index = 0; index < fieldCount; ++index)
                        columnIndex.Add(index, row.Data[index].VarCharValue);
                    firstRow = false;
                }
                else
                {
                    Dictionary<string, object> data = new Dictionary<string, object>();
                    for (int index = 0; index < fieldCount; ++index)
                        data.Add(columnIndex[index], row.Data[index].VarCharValue);

                    responseHandler(data);
                }
            }
        }
    }
}
