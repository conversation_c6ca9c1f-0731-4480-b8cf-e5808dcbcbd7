using FirebaseAdmin;
using FirebaseAdmin.Messaging;
using Google.Apis.Auth.OAuth2;
using i2e1_basics.Utilities;
using i2e1_core.Models;
using i2e1_core.services;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Threading.Tasks;
using System.Web;
using wiom_login_share.Models;

namespace i2e1_core.Utilities
{
    public class FirebaseHelper
    {
        private List<Message> messageList;
        public const string REWARDS_COLLAPSE_KEY = "REWARDS_COLLAPSE_KEY";
        public const string REVIEWS_COLLAPSE_KEY = "REVIEWS_COLLAPSE_KEY";
        public const string CLAPS_COLLAPSE_KEY = "CLAPS_COLLAPSE_KEY";
        public const string SHORT_URL_COLLAPSE_KEY = "SHORT_URL_COLLAPSE_KEY";
        public const string PAYMENT_PLAN_COLLAPSE_KEY = "PAYMENT_PLAN_COLLAPSE_KEY";
        public const string PERFORMANCE_REPORT_COLLAPSE_KEY = "PERFORMANCE_REPORT_COLLAPSE_KEY";
        public const string ASSET_STATUS_COLLAPSE_KEY = "ASSET_STATUS_COLLAPSE_KEY";

        static FirebaseHelper()
        {
            string jsonString = @"{
              ""type"": ""service_account"",
              ""project_id"": ""wiom-47e95"",
              ""private_key_id"": ""0294cd8423d4e393e917e7e0d2fb6d9b6d56738b"",
****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
              ""client_email"": ""<EMAIL>"",
              ""client_id"": ""117756814922876772409"",
              ""auth_uri"": ""https://accounts.google.com/o/oauth2/auth"",
              ""token_uri"": ""https://oauth2.googleapis.com/token"",
              ""auth_provider_x509_cert_url"": ""https://www.googleapis.com/oauth2/v1/certs"",
              ""client_x509_cert_url"": ""https://www.googleapis.com/robot/v1/metadata/x509/firebase-adminsdk-a2bly%40wiom-47e95.iam.gserviceaccount.com""
            }";

            FirebaseApp.Create(new AppOptions()
            {
                Credential = GoogleCredential.FromJson(jsonString)
            });
        }

        public FirebaseHelper()
        {
            messageList = new List<Message>();
        }

        public void QueueNotification(Dictionary<string, string> data, string fcmId, string CollapseKey)
        {
            var msg = new Message()
            {
                Data = data,
                Token = fcmId,
                Android = new AndroidConfig()
                {
                    CollapseKey = CollapseKey,
                    TimeToLive = TimeSpan.FromSeconds(60),
                    Priority = Priority.High
                }
            };
            if (data.ContainsKey("title") && data.ContainsKey("body"))
            {
                msg.Notification = new FirebaseAdmin.Messaging.Notification()
                {
                    Title = data["title"],
                    Body = data["body"]
                };
            }
            messageList.Add(msg);
        }

        public void ClearMessageList()
        {
            messageList.Clear();
            return;
        }

        public int SizeMessageList()
        {
            return messageList.Count();
        }

        public async Task<int> SendAllQueued()
        {
            var res = await FirebaseMessaging.DefaultInstance.SendAllAsync(messageList);
            Logger.GetInstance().Info($"{res.SuccessCount} Fcm Message successfully sent");
            foreach (var entry in res.Responses)
            {
                if(!entry.IsSuccess)
                    Logger.GetInstance().Info($"Fcm Message failed. Execpetion:{entry.Exception}");
            }
            return res.FailureCount;
        }


        public static List<ManagementUser> SendNotification(LongIdInfo longAccountId, App app, Dictionary<string, string> data, string collapseKey, string userRole = null, LongIdInfo userId = null)
        {
            FirebaseHelper firebaseHelper = new FirebaseHelper();
            var notifiedUsers = firebaseHelper.QueueNotificationToAllUsersOfAccount(longAccountId, app, data, collapseKey, userRole, userId);
            firebaseHelper.SendAllQueued();
            return notifiedUsers;
        }

        public static void SendNotificationToAdmin(LongIdInfo longAdminUserId, App app, Dictionary<string, string> data, string collapseKey)
        {
            var user = CoreUserService.GetAdminUser(longAdminUserId);
            var fcmTokens = CoreUserService.GetFcmToken(longAdminUserId.shard_id,new List<string>() { user.username }, app);
            FirebaseHelper firebaseHelper = new FirebaseHelper();
            fcmTokens.ForEach(m =>
            {
                if (!string.IsNullOrEmpty(m))
                    firebaseHelper.QueueNotification(data, m, collapseKey);
            });
            firebaseHelper.SendAllQueued();
        }

        private List<ManagementUser> QueueNotificationToAllUsersOfAccount(LongIdInfo longAccountId, App app, Dictionary<string, string> data, string collapseKey, string userRole, LongIdInfo userId = null)
        {
            List<ManagementUser> notifiedUsers = new List<ManagementUser>();
            if(longAccountId != null && longAccountId.local_value != 0)
            {
                notifiedUsers = CoreAccountService.GetUsersInAccount(longAccountId, userRole, userId);
                var fcmTokens = CoreUserService.GetFcmToken(longAccountId.shard_id, notifiedUsers.Select(m => m.username).ToList(), app);
                fcmTokens.ForEach(m =>
                {
                    if (!string.IsNullOrEmpty(m))
                        QueueNotification(data, m, collapseKey);
                });
            }
            return notifiedUsers;
        }
 
        public static void SendUiAppNotificationAsync(string title, string body, Dictionary<string, string> data, string fcmId, string CollapseKey = "collapse-key")
        {
            try
            {
                // See documentation on defining a message payload.
                var message = new Message()
                {
                    Data = data,
                    Token = fcmId,
                    Android = new AndroidConfig()
                    {
                        CollapseKey = CollapseKey,
                        TimeToLive = TimeSpan.FromSeconds(3600),
                        Priority = Priority.High
                    },
                    Notification = new FirebaseAdmin.Messaging.Notification()
                    {
                        Body = body,
                        Title = title
                    }
                };

                // Send a message to the device corresponding to the provided
                // registration token.
                FirebaseMessaging.DefaultInstance.SendAsync(message);
                //Console.WriteLine("Firebase Response: "+result);
            }
            catch (Exception ex)
            {
                Logger.GetInstance().Error(ex.ToString());
            }
        }

        public static void SubscribeAppToFCMTopic(string topic, string fcmId)
        {
            // These registration tokens come from the client FCM SDKs.
            var registrationTokens = new List<string>(1)
            {
                fcmId
            };

            // Subscribe the devices corresponding to the registration tokens to the
            // topic
            FirebaseMessaging.DefaultInstance.SubscribeToTopicAsync(registrationTokens, topic);
        }

        public async static void SendMessageToTopic(string topic)
        {
            // Define a condition which will send to devices which are subscribed
            // to either the Google stock or the tech industry topics.
            var condition = $"'{topic}' in topics";

            // See documentation on defining a message payload.
            var message = new Message()
            {
                Data = new Dictionary<string, string>()
                {
                    { "action", "update_get_config" },
                    { "canShowSkip", "true" },
                    { "delayOnEducationScreen", "8000" },
                    { "sendEasilyText", "…Send it easily to anyone" },
                    { "showMissedCallPopUp", "true" },
                    {  "displayCovid19", "0" }
                },
                Condition = condition,
            };

            // Send a message to devices subscribed to the combination of topics
            // specified by the provided condition.
            string response = await FirebaseMessaging.DefaultInstance.SendAsync(message);
            // Response is a message ID string.
            Console.WriteLine("Successfully sent message: " + response);
        }

        public static string GetWIOMGoldAppDownloadLink(string utm_campaign, string wlToken)
        {
            return $"https://d.wiom.in/d/?link=https://play.google.com/store/apps/details?id%3Dcom.i2e1.wiom_gold%26utm_campaign%3D{utm_campaign}%26wl_token%3D{ (string.IsNullOrEmpty(wlToken) ? string.Empty : HttpUtility.UrlEncode(wlToken)) }&apn=com.i2e1.wiom_gold&utm_campaign={utm_campaign}&utm_medium=sms&utm_source=cp";
        }

        public static string GetWIOMSalesAppDownloadLink(string utm_campaign, string dynamicLink)
        {
            return $"https://s.wiom.in/d/?link=https://play.google.com/store/apps/details?id%3Dcom.i2e1.wiom.sales%26utm_campaign%3D{utm_campaign}%26dynamicLink%3D{ HttpUtility.UrlEncode(dynamicLink) }&apn=com.i2e1.wiom.sales&utm_campaign={utm_campaign}&utm_medium=sms&utm_source=cp";
        }

        public static string CreateDynamicLink(string link)
        {
            using(var client = new WebClient())
            {
                var data = new {
                    longDynamicLink = link
                };

                var response = client.UploadString("https://firebasedynamiclinks.googleapis.com/v1/shortLinks?key=AIzaSyAgOV_XaCMdRampfWecyZbnOlDzzHjIV1k", JsonConvert.SerializeObject(data));

                var jToken = JsonConvert.DeserializeObject<JToken>(response);
                return jToken["shortLink"].ToString().Replace("https://", string.Empty);
            }
        }
    }
}
