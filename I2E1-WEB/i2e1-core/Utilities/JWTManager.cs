using i2e1_basics.Models;
using i2e1_basics.Utilities;
using i2e1_core.Models;
using i2e1_core.services;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc.Filters;
using Microsoft.IdentityModel.Tokens;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.IdentityModel.Tokens.Jwt;
using System.Linq;
using System.Reflection;
using System.Security.Claims;
using System.Text;
using wiom_login_share.Utilities;

namespace i2e1_core.Utilities
{
    public class JWTManager
	{

		private static string jwtKey = "This is a sample secret key - please use in production and development environment.";
        public static JwtToken CreateJwtToken(LoginUser User, Account Account, ManagementUser adminUser, HttpContext httpContext)
        {
            try
            {                
                List<Claim> objectClaim = FillJwtPayload(User, Account, adminUser);
                JwtToken jwtToken = CreateNewJwtToken(objectClaim);
                setJWTCookie(httpContext, jwtToken);
          
                return jwtToken;
            }
            catch (Exception ex)
            {
                return null;
            }
        }

        public static JwtObject Authenticate(AuthorizationFilterContext filterContext, out ResponseStatus responseStatus)
        {
            return Authenticate(filterContext.HttpContext, out responseStatus);
        }

        public static JwtObject Authenticate(HttpContext context, out ResponseStatus responseStatus)
        {
            responseStatus = ResponseStatus.SUCCESS;
            try
            {
                JwtObject jwtObject = null;
                string token = null;
                if (context.Request.Headers.TryGetValue(Constants.JWT_TOKEN, out var tokenValue))
                {
                    token = tokenValue[0];
                }
                else
                {
                    if (context.Request.Cookies[Constants.JWT_TOKEN] == null)
                        return null;

                    token = context.Request.Cookies[Constants.JWT_TOKEN].ToString();
                }

                if (token == null)
                    return null;

                bool? checkLatestApp = CheckLatestApp(context);
                if (checkLatestApp != null && checkLatestApp == false)
                {
                    // need to discuss
                    responseStatus = ResponseStatus.UPDATE;
                    return null;
                }

                var tokenHandler = new JwtSecurityTokenHandler();
                var key = Encoding.UTF8.GetBytes(jwtKey);
                if (CheckTokenExpiryIsValid(token))
                {
                    try
                    {
                        tokenHandler.ValidateToken(token, new TokenValidationParameters
                        {
                            ValidateIssuerSigningKey = true,
                            IssuerSigningKey = new SymmetricSecurityKey(key),
                            ValidateIssuer = false,
                            ValidateAudience = false,
                            // set clockskew to zero so tokens expire exactly at token expiration time (instead of 5 minutes later)
                            ClockSkew = TimeSpan.Zero
                        }, out SecurityToken validatedToken);

                        JwtSecurityToken jwtToken = (JwtSecurityToken)validatedToken;
                        jwtObject = createJwtObject(jwtToken.Claims);
                    }
                    catch (Exception ex)
                    {
                        expireJwtToken(context);
                        if (ex.Message.Contains("IDX10503"))
                            responseStatus = ResponseStatus.LOGOUT;

                        return null;
                    }
                }
                else
                {
                    var jwtSecurityToken = tokenHandler.ReadJwtToken(token);
                    string wl_token = jwtSecurityToken.Claims.First(claim => claim.Type.Equals("wl_token")).Value;
                    LongIdInfo userId = LongIdInfo.IdParser(Convert.ToInt64(jwtSecurityToken.Claims.First(claim => claim.Type.Equals("userId")).Value));
                    TokenParse(wl_token, out string wltoken);
                    LoginUser loginUser = CoreUserService.GetUserFromToken(userId.shard_id, wltoken);
                    if (loginUser != null)
                    {
                        if (loginUser.id == userId)
                        {
                            JwtToken jwtToken = CreateNewJwtToken(jwtSecurityToken.Claims);
                            jwtObject = createJwtObject(jwtSecurityToken.Claims);
                            setJWTCookie(context, jwtToken);
                        }
                    }
                    else
                    {
                        expireJwtToken(context);
                        responseStatus = ResponseStatus.LOGOUT;
                        return null;
                    }
                }

                context.Items[Constants.JWT_OBJECT] = jwtObject;
                return jwtObject;

            }
            catch
            {
                return null;
            }
        }

        public static JwtToken CreateNewJwtToken(IEnumerable<Claim> objectClaim)
        {
            try
            {
                JwtSecurityTokenHandler tokenHandler = new JwtSecurityTokenHandler();
                var tokenKey = Encoding.UTF8.GetBytes(jwtKey);

                var tokenDescriptor = new SecurityTokenDescriptor
                {
                    Subject = new ClaimsIdentity(objectClaim),
                    Expires = DateTime.UtcNow.AddDays(1),
                    SigningCredentials = new SigningCredentials
                        (new SymmetricSecurityKey(tokenKey),
                        SecurityAlgorithms.HmacSha512Signature)
                };

                var token = tokenHandler.CreateToken(tokenDescriptor);
                //var refreshToken = GenerateRefreshToken();
                JwtToken jwtToken = new JwtToken { Token = tokenHandler.WriteToken(token) };
                return jwtToken;
            }
            catch (Exception ex)
            {
                return null;
            }
        }

        public static JwtToken updateJwtToken(JwtObject jwtObject, HttpContext httpContext)
        {
            try
            {
                List<Claim> claims = jwtObjectToClaimObject(jwtObject);
                JwtToken jwtToken= CreateNewJwtToken(claims);
                setJWTCookie(httpContext, jwtToken);
                return jwtToken;
            }
            catch (Exception ex)
            {
                return null;
            }
            return default(JwtToken);
        }

        public static void expireJwtToken(HttpContext httpContext)
        {
            setJWTCookie(httpContext, null);
        }
        public static List<Claim> FillJwtPayload(LoginUser User, Account Account, ManagementUser adminUser)
        {
            List<Claim> jwtObject = new List<Claim> { };
            try
            {
                if (User != null)
                {
                    jwtObject.Add(new Claim("id", Guid.NewGuid().ToString()));
                    jwtObject.Add(new Claim("userId", User.id.GetLongId().ToString()));
                    jwtObject.Add(new Claim("mobile", !String.IsNullOrEmpty(User.mobile) ? User.mobile.ToString() : ""));
                    jwtObject.Add(new Claim("wl_token", User.token.ToString()));
                    jwtObject.Add(new Claim("loginName", !String.IsNullOrEmpty(User.name) ? User.name.ToString() : ""));
                }
                if (Account != null)
                {
                    jwtObject.Add(new Claim("accountId", Account.id.GetLongId().ToString()));
                    if (Account.options.ContainsKey("nasesJson"))
                    {
                        JArray temp = JsonConvert.DeserializeObject<JArray>(Account.options["nasesJson"]);
                        foreach (JObject item in temp)
                        {
                            if (item.ContainsKey("nas_id"))
                                jwtObject.Add(new Claim("nasId", item.GetValue("nas_id").ToString()));
                            if(item.ContainsKey("account_params"))
                            {
                                JToken temp1 = JObject.Parse(item.GetValue("account_params").ToString())["deviceId"];
                                if (temp1!=null && temp1.Contains("deviceId"))
                                    jwtObject.Add(new Claim("deviceId", ((Newtonsoft.Json.Linq.JValue)temp1).Value.ToString()));
                            }
                        }
                    }

                }
                if (adminUser != null)
                {
                    jwtObject.Add(new Claim("adminId", adminUser.userid.ToString()));
                    jwtObject.Add(new Claim("i2e1_admin_token", adminUser.token.ToString()));
                    jwtObject.Add(new Claim("userType", adminUser.userType.ToString()));
                    jwtObject.Add(new Claim("features", JsonConvert.SerializeObject(adminUser.features).ToString()));
                    jwtObject.Add(new Claim("adminExtraData", JsonConvert.SerializeObject(adminUser.extraData).ToString()));
                    jwtObject.Add(new Claim("email", !String.IsNullOrEmpty(adminUser.email) ? adminUser.email.ToString() : ""));
                    jwtObject.Add(new Claim("username", !String.IsNullOrEmpty(adminUser.username) ? adminUser.username.ToString() : ""));
                    jwtObject.Add(new Claim("adminName", !String.IsNullOrEmpty(adminUser.name) ? adminUser.name.ToString() : ""));
                }
            }
            catch (Exception ex)
            {
                Logger.GetInstance().Info("Exception in getting verify otp " + User.id.GetLongId() + " ex: " + ex.ToString());
            }

            return jwtObject;
        }
        public static JwtObject createJwtObject(IEnumerable<Claim> payload)
        {
            JwtObject jwtObject = new JwtObject();

            foreach (var claim in payload)
            {
                if (claim.Type == "userId")
                    jwtObject.userId = LongIdInfo.IdParser(Convert.ToInt64(claim.Value));
                else if (claim.Type == "mobile")
                    jwtObject.mobile = claim.Value;
                else if (claim.Type == "wl_token")
                    jwtObject.wl_token = claim.Value;
                else if (claim.Type == "accountId")
                    jwtObject.accountId = LongIdInfo.IdParser(Convert.ToInt64(claim.Value));
                else if (claim.Type == "nasId")
                    jwtObject.nasId = LongIdInfo.IdParser(Convert.ToInt64(claim.Value));
                else if (claim.Type == "i2e1_admin_token")
                    jwtObject.i2e1_admin_token = claim.Value;
                else if (claim.Type == "deviceId")
                    jwtObject.deviceId = claim.Value;
                else if (claim.Type == "adminId")
                    jwtObject.adminId = LongIdInfo.IdParser(Convert.ToInt64(claim.Value));
                else if (claim.Type == "email")
                    jwtObject.email = claim.Value;
                else if (claim.Type == "username")
                    jwtObject.username = claim.Value;
                else if (claim.Type == "loginName")
                    jwtObject.loginName = claim.Value;
                else if (claim.Type == "adminName")
                    jwtObject.adminName = claim.Value;
                else if (claim.Type == "userType")
                {
                    if (Enum.TryParse<AdminUserType>(claim.Value, out AdminUserType adminUserType))
                    {
                        jwtObject.userType = adminUserType;
                    }
                    else
                        jwtObject.userType = AdminUserType.STANDARD;
                }
                else if (claim.Type == "features")
                {
                    string input = claim.Value;

                    jwtObject.features = JsonConvert.DeserializeObject<Dictionary<string, int>>(input);
                }
                else if (claim.Type == "adminExtraData")
                {
                    string input = claim.Value;

                    jwtObject.adminExtraData = JsonConvert.DeserializeObject<JObject>(input);
                }


            }

            return jwtObject;
        }
        public static List<Claim> jwtObjectToClaimObject(JwtObject jwtObject)
        {
            List<Claim> claimObject = new List<Claim> { };
            try
            {
                Type type = jwtObject.GetType();
                PropertyInfo[] properties = type.GetProperties();
                foreach (PropertyInfo property in properties)
                {
                    object value = property.GetValue(jwtObject);
                    if(value != null)
                    {
                        Type objType = value.GetType();
                        if (objType.IsPrimitive)
                        {
                            claimObject.Add(new Claim(property.Name.ToString(), value.ToString()));
                        }
                        else
                            claimObject.Add(new Claim(property.Name.ToString(), JsonConvert.SerializeObject(value)));
                    }
                }
                
            }
            catch (Exception ex)
            {
                Logger.GetInstance().Info("Exception in getting modify jwtObject " + " ex: " + ex.ToString());
            }

            return claimObject;
        }
        public static bool CheckTokenExpiryIsValid(string token)
        {
            var tokenTicks = GetTokenExpirationTime(token);
            var tokenDate = DateTimeOffset.FromUnixTimeSeconds(tokenTicks).UtcDateTime;

            var now = DateTime.Now.ToUniversalTime();

            var valid = tokenDate >= now;

            return valid;
        }
        public static long GetTokenExpirationTime(string token)
        {
            var handler = new JwtSecurityTokenHandler();
            var jwtSecurityToken = handler.ReadJwtToken(token);
            var tokenExp = jwtSecurityToken.Claims.First(claim => claim.Type.Equals("exp")).Value;
            var ticks = long.Parse(tokenExp);
            return ticks;
        }

        public static void TokenParse(string token, out string token_value)
        {
            int index = token.IndexOf("$");
            token_value = "";
            if (index > 0)
            {
                token_value = token.Substring(index + 1);
            }
        }
        public static bool? CheckLatestApp(HttpContext httpContext)
        {
            string appName = CoreUtil.GetHeaderValue(httpContext, CustomHeader.APP_NAME);
            string appVersion = CoreUtil.GetHeaderValue(httpContext, CustomHeader.APP_VERSION);
            if (!string.IsNullOrEmpty(appName) && !string.IsNullOrEmpty(appVersion))
            {
                var cache = CoreCacheHelper.GetInstance();
                if (cache.isLatestAppVersion(string.Format("{0}_{1}", appName, appVersion)))
                {
                    return true;
                }
                return false;
            }
            return null;
        }

        private static void setJWTCookie(HttpContext httpContext, JwtToken jwtToken)
        {
            string host = httpContext.Request.Host.Value;
            if (I2e1ConfigurationManager.IS_PROD)
            {
                int index = host.IndexOf('.');
                if (index != -1)
                {
                    int secondIndex = host.IndexOf('.', index + 1);
                    if(secondIndex != -1)
                    {
                        host = host.Substring(index);
                    }
                }
            }
            else
            {
                host = httpContext.Request.Host.Host.Contains("localhost") ||
                httpContext.Request.Host.Host.Contains("192.168.") ?
                httpContext.Request.Host.Host : "." + Environment.GetEnvironmentVariable("DEPLOYED_ON") + ".i2e1.in";
            }
            if (jwtToken != null && jwtToken.Token != null)
                httpContext.Response.Headers[Constants.JWT_TOKEN] = jwtToken.Token.ToString();
            else
                httpContext.Response.Headers[Constants.JWT_TOKEN] = "";
            CookieUtils.SetCookie(httpContext, Constants.JWT_TOKEN, jwtToken != null ? jwtToken.Token.ToString() : "", true, null, true, SameSiteMode.None, host);
        }
    }
}
