using i2e1_basics.Database;
using i2e1_basics.Utilities;
using i2e1_core.Models;
using i2e1_core.Models.Client;
using i2e1_core.Models.Entity;
using Microsoft.Extensions.Caching.Memory;
using Newtonsoft.Json;
using StackExchange.Redis;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Net;
using System.Text;
using System.Web;
using wifidog_core.Models;
using wifidog_core.Models.WIOM;
using wifidog_core.Utilities;
using wiom_login_share.Models;
using wiom_router_api;
using wiom_router_api.Models;

namespace i2e1_core.Utilities
{
    public delegate T GetValue<T>();

    public delegate T GetValueWithExpiry<T>(out TimeSpan expiryTimeSpan);

    public class CoreCacheHelper
    {
        public const string CUSTOMER_PLAN_CONF = "CUSTOMER_PLAN_CONF";
        public const string SERVICE_PLAN_CONF = "SERVICE_PLAN_CONF";
        public const string PLAN_CONF = "PLAN_CONF";
        public const string MAC_CONFIG = "MAC_CONFIG";
        public const string TEMPLATE_CONTENT = "TEMPLATE_CONTENT";
        public const string NAS_VOUCHER = "NAS_VOUCHER";
        public const string FDM_CONFIGS = "FDM_CONFIGS";
        public const string ROOM_NO_LASTNAME = "ROOM_NO_LASTNAME";
        public const string VIP_LIST = "VIP_LIST";
        public const string WHITE_BLOCK_LIST = "WHITE_BLOCK_LIST";
        public const string PLAN_SES = "PLAN_SES";
        public const string PLAN_MAC_MAPPING = "PLAN_MAC_MAPPING";
        public const string ROUTER_BASIC = "ROUTER_BASIC";
        public const string ROUTER_TAG_DETAILS = "ROUTER_TAG_DETAILS";
        public const string NAS_FROM_MACV2 = "NAS_FROM_MACV2";
        public const string GRID_BY_SIZE = "GRID_BY_SIZE";
        public const string NAS_USER_GROUPS = "NAS_USER_GROUPS";
        public const string CAMPAIGN_TARGETING = "CAMPAIGN_TARGETING";
        public const string ACTIVE_CAMPAIGN = "ACTIVE_CAMPAIGN";
        public const string GOOGLE_LOCATION_DETAILS = "GOOGLE_LOCATION_DETAILS";
        public const string LOCATION_WEATHER_DETAILS = "LOCATION_WEATHER_DETAILS";
        public const string LOCATION_ID_DETAILS = "LOCATION_ID_DETAILS";
        public const string B2CC_LINQ_PROMOTION = "B2CC_LINQ_PROMOTION";
        public const string COUNTRY_GROUP = "COUNTRY_GROUP";
        public const string STATE_GROUP = "STATE_GROUP";
        public const string CITY_GROUP = "CITY_GROUP";
        public const string WIKI_RESPONSE = "WIKI_RESPONSE";
        public const string GET_ALL_SUBCATEGORIES = "GET_ALL_SUBCATEGORIES";
        public const string GET_FEATURED_CONTENT = "GET_FEATURED_CONTENT";
        public const string UNREGISTERED_NASES = "UNREGISTERED_NASES";
        public const string DIY_REG_OTP = "DIY_REG_OTP";

        public const string PARTNER_DETAIL = "PARTNER_DETAIL";
        public const string CAMPAIGN_REPORTS = "CAMPAIGN_REPORTS";
        public const string FOOTFALL_SUMMARY = "FOOTFALL_SUMMARY";
        public const string ADVERTISISING_TEMPLATE = "ADVERTISISING_TEMPLATE";

        public const string SWAPP_PROMOTION = "SWAPP_PROMOTION";

        public const string DAILY_NAS_DATA_LIMIT = "DAILY_NAS_DATA_LIMIT";

        public const string ACCOUNT_ID_FROM_USER_ID = "ACCOUNT_ID_FROM_USER_ID";
        public const string USER_ACCOUNT = "USER_ACCOUNT";

        public const string LATEST_APP_VERSIONS = "LATEST_APP_VERSIONS";
        public const string PARTNER_EDUCATION_VIDEO = "PARTNER_EDUCATION_VIDEO";
        public const string APP_USER_ACCESS = "APP_USER_ACCESS";
        public const string RATING_VARIABLES = "RATING_VARIABLES_";
        public const string NAS_DETAILS_FROM_DEVICEID = "NAS_DETAILS_FROM_DEVICEID_";
        public const string NAS_DETAILS_FROM_NAS = "NAS_DETAILS_FROM_NAS_";
        public const string WANI_CREDENTIALS = "WANI_CREDENTIALS";
        public const string SERVER_10_APP_VERSIONS = "SERVER_10_APP_VERSIONS";
        public const string LATEST_APP_VERSIONS0 = "LATEST_APP_VERSIONS0";
        public const string SERVER_10_ACCOUNTS = "SERVER_10_ACCOUNTS";
        public const string FORCE_APP_VERSION_UPDATE = "FORCE_APP_VERSION_UPDATE";
        public const string ADVERTISISING_ID_LOGIN_USER = "ADVERTISISING_ID_LOGIN_USER_";
        public const string CAPTIVE_PORTAL_USER_UNIQUE_TOKEN = "CAPTIVE_PORTAL_USER_UNIQUE_TOKEN";

        public const string WIOM_NET_NAS_SETTING = "WIOM_NET_NAS_SETTING";
        public const string WIOM_NET_OTT_STATUS = "WIOM_NET_OTT_STATUS";
        public const string SHORT_URL = "SHORTY";

        public IDatabase cache;
        public IMemoryCache memoryCache;

        private static CoreCacheHelper cacheHelper = null;

        protected CoreCacheHelper(string cacheConnectionString, int cachedbId)
        {
            ConnectionMultiplexer connection = ConnectionMultiplexer.Connect(cacheConnectionString);
            this.cache = connection.GetDatabase(cachedbId);
            this.memoryCache = new MemoryCache(new MemoryCacheOptions());
        }

        public static CoreCacheHelper CreateInstance(string cacheConnectionString, int cachedbId)
        {
            if (cacheHelper == null)
            {
                cacheHelper = new CoreCacheHelper(cacheConnectionString, cachedbId);
            }
            return cacheHelper;
        }

        public static CoreCacheHelper GetInstance()
        {
            return cacheHelper;
        }

        public Template GetTemplateContent(int templateId, LongIdInfo nasid = null)
        {
            var response = getValueFromCache(TEMPLATE_CONTENT, templateId, () =>
            {
                return DbCalls.GetInstance().GetTemplate(templateId);
            });

            if (response != null && !string.IsNullOrEmpty(response.templatePath))
            {
                var path = AppDomain.CurrentDomain.BaseDirectory + "CustomTemplates";
                if (!Directory.Exists(path))
                    Directory.CreateDirectory(path);

                var file = path + "/" + response.id + ".html";
                string content = string.Empty;

                if (!File.Exists(file))
                {
                    try
                    {
                        string filePath = response.isFullOverriden ? response.templatePath + ".html" : response.templatePath;

                        var temp = new WebClient().DownloadData(filePath);
                        content = Encoding.UTF8.GetString(temp);
                        int index = 0;

                        while ((index = content.IndexOf("blob.core.windows.net", index + 50)) != -1)
                        {
                            int startIndex = content.LastIndexOf("http", index);
                            int lineEnd = content.IndexOf("\n", index);
                            int endIndex = content.IndexOf(".png", index);
                            if (endIndex == -1 || endIndex > lineEnd)
                            {
                                endIndex = content.IndexOf(".jpg", index);
                            }

                            if (endIndex != -1 && endIndex <= lineEnd)
                            {
                                var newContent = content.Substring(0, startIndex);
                                newContent = newContent + "/Proxy/GetContent.ashx?url="
                                    + HttpUtility.UrlEncode(content.Substring(startIndex, endIndex - startIndex + 4).Replace("https", "http"))
                                    + content.Substring(endIndex + 4);
                                content = newContent;
                            }

                            File.WriteAllText(file, content);
                        }

                        File.WriteAllText(file, content);
                    }
                    catch
                    {
                    }
                }
                else
                {
                    content = File.ReadAllText(file);
                }
                response.templateContent = content;
            }

            if (nasid != null && nasid.local_value == 666)
            {
                if (!string.IsNullOrEmpty(response.templatePath))
                {
                    try
                    {
                        using (WebClient client = new WebClient())
                        {
                            response.templateContent = client.DownloadString(response.templatePath);
                        }
                    }
                    catch
                    {
                        response.templateContent = string.Empty;
                    }
                }
            }
            return response;
        }

        protected void setCache(string key, object data, int minutesToExpiry)
        {
            var time = CoreUtil.GetTimeInIST();
            TimeSpan interval = time.AddMinutes(minutesToExpiry).Date - time;
            setCache(key, data, interval);
        }

        public void setCache(string key, object data, TimeSpan? interval = null)
        {
            var stringData = JsonConvert.SerializeObject(data);
            cache.StringSetAsync(key, stringData, interval, When.Always, CommandFlags.FireAndForget);
        }

        protected void setHashCache(string hashName, string key, object data)
        {
            var stringData = JsonConvert.SerializeObject(data);
            cache.HashSetAsync(key, hashName, stringData, When.Always, CommandFlags.FireAndForget);
        }

        protected RedisValue getCache(string key)
        {
            return cache.StringGet(key);
        }

        protected RedisValue getCacheFromHash(string hashName, string key)
        {
            return cache.HashGet(hashName, key);
        }

        protected RedisValue setCacheInHash(string hashName, string key, string value)
        {
            return cache.HashSet(hashName, key, value, When.Always, CommandFlags.FireAndForget);
        }

        protected RedisValue deleteCacheInHash(string hashName, string key)
        {
            return cache.HashDelete(hashName, key, CommandFlags.FireAndForget);
        }

        public void Reset(string key, object id)
        {
            Logger.GetInstance().Info("clearing the cache for : " + key + id);
            cache.KeyDelete(key + id, CommandFlags.FireAndForget);
        }

        public void Reset(string key, LongIdInfo id)
        {
            cache.KeyDelete(key + id.ToSafeDbObject(1), CommandFlags.FireAndForget);
        }

        public RedisValue getCacheAndIncreaseTTL(string key, TimeSpan expirySpan)
        {
            var t1 = cache.StringGetAsync(key);
            cache.KeyExpireAsync(key, expirySpan, CommandFlags.FireAndForget);
            return cache.Wait(t1);
        }

        public void ResetBatch(string[] keys)
        {
            RedisKey[] rKeys = new RedisKey[keys.Length];
            for (int i = 0; i < keys.Length; ++i)
                rKeys[i] = keys[i];
            cache.KeyDeleteAsync(rKeys);
        }

        public void ResetAll(LongIdInfo nasid)
        {
            Reset(MAC_CONFIG, nasid);
            Reset(VIP_LIST, nasid);
            Reset(WHITE_BLOCK_LIST, nasid);
            Reset(ROUTER_BASIC, nasid);
        }

        public T getValueFromCache<T>(string key, object id)
        {
            try
            {
                var data = getCache(key + id);
                T value;
                if (!data.IsNullOrEmpty)
                {
                    value = JsonConvert.DeserializeObject<T>(data);
                    return value;
                }
                else
                {
                    return default(T);
                }
            }
            catch (Exception ex)
            {
                Logger.GetInstance().Error(ex.ToString());
                throw;
            }
        }

        public T getValueFromHashCache<T>(string hashName, string key, object id, GetValue<T>? getValue)
        {
            try
            {
                var data = getCacheFromHash(hashName, key + id);
                T value;
                if (data.IsNullOrEmpty)
                {
                    value = getValue();
                    setHashCache(hashName, key + id, value);
                }
                else
                {
                    if (getValue == null)
                        return default(T);
                    else
                        value = JsonConvert.DeserializeObject<T>(data);
                }
                return value;
            }
            catch (Exception ex)
            {
                Logger.GetInstance().Error(ex.ToString());
                throw;
            }
        }

        public T getValueFromCache<T>(string key, object id, GetValue<T> getValue, bool disableCache = false)
        {
            try
            {
                RedisValue data = RedisValue.Null;
                T value;
                if (!disableCache)
                {
                    data = getCache(key + id);
                }

                if (disableCache || data.IsNullOrEmpty)
                {
                    value = getValue();
                    if(value != null)
                    {
                        var time = CoreUtil.GetTimeInIST();
                        TimeSpan interval = time.AddDays(1).Date - time;
                        setCache(key + id, value, interval);
                    }
                }
                else
                {
                    value = JsonConvert.DeserializeObject<T>(data);
                }

                return value;
            }
            catch (Exception ex)
            {
                Logger.GetInstance().Error(ex.ToString());
                throw;
            }
        }

        public T getValueFromCache<T>(string key, object id, GetValueWithExpiry<T> getValue)
        {
            try
            {
                var data = getCache(key + id);
                T value;
                if (data.IsNullOrEmpty)
                {
                    value = getValue(out var expiryTimeSpan);
                    setCache(key + id, value, expiryTimeSpan);
                }
                else
                {
                    value = JsonConvert.DeserializeObject<T>(data);
                }

                return value;
            }
            catch (Exception ex)
            {
                Logger.GetInstance().Error(ex.ToString());
                throw;
            }
        }

        public List<PDOPlan> GetActivePlansOnNas(LongIdInfo longNasid)
        {
            var combinedSettingId = CoreCacheHelper.GetInstance().GetRouterBasic(longNasid).combinedSettingId;
            return GetActivePlansInSetting(combinedSettingId);
        }
        public RouterBasic GetRouterBasic(LongIdInfo nasid)
        {
            return MemoryCacheHelper.GetValueFromCache("ROUTER_BASIC" + nasid, () =>
            {
                return RouterApiClient.GetInstance().GetRouterBasicAsync(nasid).Result;
            }, TimeSpan.FromSeconds(600));
        }
        public List<PDOPlan> GetActivePlansInSetting(int combinedSettingId)
        {
            var result = getValueFromCache(PLAN_CONF, combinedSettingId, () =>
            {
                return CoreDbCalls.GetInstance().GetPlanConfigInCombinedSetting(combinedSettingId).Where(m => m.active).ToList();
            });

            return result.Count > 0 ? result.OrderBy(m => m.price).ToList() : result;
        }

        public PDOPlan GetPlanFromSetting(int settingId, long planId)
        {
            if (settingId == 0 && planId != 0)
                return CoreDbCalls.GetInstance().GetPlanInfo(planId);
            var plans = GetActivePlansInSetting(settingId);
            if(planId == 0)
            {
                return plans.Count > 0 ? plans[0] : null;
            }
            if(plans != null)
            {
                return plans.First(x => x.id == planId);
            }
            return null;
        }

        public List<PlanMacMapping> GetAllDevicesPlanMacMapping(User user)
        {
            var entries = cache.HashGetAll(PlanMacMapping.GetConfigKey(user));
            var mapping = new List<PlanMacMapping>(entries.Length);
            foreach (var entry in entries)
            {
                var values = entry.Value.ToString().Split('_');
                long planId = long.Parse(values[0]);
                mapping.Add(new PlanMacMapping()
                {
                    mac = entry.Name,
                    planId = planId,
                    device = values[2]
                });
            }
            return mapping;
        }

        public List<PlanMacMapping> GetActivePlanMacMapping(WifiUser user)
        {
            var entries = cache.HashGetAll(PlanMacMapping.GetConfigKey(user));
            var mapping = new List<PlanMacMapping>(entries.Length);
            foreach (var entry in entries)
            {
                var planMacMapping = parsePlanMacMapping(entry.Value, entry.Name, out bool isExpired);
                if (planMacMapping != null && !isExpired)
                {
                    mapping.Add(planMacMapping);
                }
            }
            return mapping;
        }

        public void ResetPlanMacMapping(WifiUser user)
        {
            Reset(PlanMacMapping.GetConfigKey(user), string.Empty);
        }

        public PlanMacMapping GetCurrentDevicePlanMacMapping(WifiUser user, out bool isExpired)
        {
            isExpired = false;
            var entry = cache.HashGet(PlanMacMapping.GetConfigKey(user), user.mac);
            if (!entry.IsNullOrEmpty)
            {
                return parsePlanMacMapping(entry, user.mac, out isExpired);
            }
            return null;
        }

        private PlanMacMapping parsePlanMacMapping(string entry, string mac, out bool isExpired)
        {
            var values = entry.ToString().Split('_');
            long planId = long.Parse(values[0]);

            DateTime lastActive;
            if (long.TryParse(values[1], out long timeStamp))
                lastActive = DateTime.FromBinary(timeStamp);
            else
                lastActive = DateTime.Parse(values[1]);

            isExpired = false;
            if (planId != 0 && (DateTime.UtcNow - lastActive).TotalSeconds > 300)
            {
                isExpired = true;
            }
            return new PlanMacMapping()
            {
                mac = mac,
                planId = planId,
                device = values[2]
            };
        }

        public void SetPlanMacMapping(WifiUser user, long planId, string device = "Unknown Device")
        {
            if (!string.IsNullOrEmpty(user.mac))
                cache.HashSetAsync(PlanMacMapping.GetConfigKey(user), user.mac, planId + "_" + DateTime.UtcNow.ToBinary() + "_" + device);
        }

        public KeyValuePair<List<string>, List<string>> GetMacWhitelistAndBlackList(LongIdInfo nasid)
        {
            return memoryCache.GetOrCreate(MAC_CONFIG + nasid, (entry) =>
            {
                entry.AbsoluteExpirationRelativeToNow = TimeSpan.FromMinutes(15);
                return getValueFromCache(MAC_CONFIG, nasid, () =>
                {
                    Dictionary<string, string> macIds = CoreDbCalls.GetInstance().GetListChecks(nasid, ListCheckDataType.MAC_WHITELISTING);
                    List<string> whiteList = new List<string>();

                    foreach (KeyValuePair<string, string> s in macIds)
                    {
                        if (!String.IsNullOrEmpty(s.Key))
                            whiteList.Add(s.Key.ToUpper().Replace('-', ':'));
                    }

                    macIds = CoreDbCalls.GetInstance().GetListChecks(nasid, ListCheckDataType.MAC_BLACKLISTING);
                    List<string> blackList = new List<string>();
                    foreach (KeyValuePair<string, string> s in macIds)
                    {
                        if (!String.IsNullOrEmpty(s.Key))
                            blackList.Add(s.Key.ToUpper().Replace('-', ':'));
                    }
                    return new KeyValuePair<List<string>, List<string>>(whiteList, blackList);
                });
            });
        }

        public RouterBasicDetails GetRouterBasicDetails(LongIdInfo longNasid)
        {
            if (longNasid.type_id == DBObjectType.SECONDARY_NAS)
            {
                RouterBasicDetails details = new RouterBasicDetails()
                {
                    combinedSettingId = 1,
                    storeGroupId = 1
                };
                return details;
            }
            return getValueFromCache(ROUTER_BASIC, longNasid.ToSafeDbObject(1), () =>
            {
                var routerConfig = new RouterBasicDetails();
                routerConfig.combinedSettingId = CoreDbCalls.GetInstance().GetCombinedSettingIdOnNas(longNasid);
                var pair = CoreDbCalls.GetInstance().GetStoreGroupAndIsAccessCodeApplied(longNasid);
                routerConfig.storeGroupId = pair.Key;

                var homeRouterParameter = CoreDbCalls.GetInstance().GetSingleNasOperation(longNasid, 22);
                if (int.TryParse(homeRouterParameter, out int hr))
                {
                    routerConfig.isHomeRouter = (hr != 0);
                }
                return routerConfig;
            });
        }

        public string getUserBasicConfigKey(int combinedSettingId)
        {
            return combinedSettingId + ".USERGROUP_BASIC_CONFIG_" + 0;
        }

        public string getAdvanceConfigKey(int combinedSettingId)
        {
            return combinedSettingId + ".ADVANCE_CONFIG";
        }

        public int GetNoOfDeviesPerUser(int combinedSettingId)
        {
            int count = GetAdvanceConfigIntValue(combinedSettingId, AdvanceConfigType.NO_OF_DEVICES_PER_USER);
            return count <= 0 ? 5 : count;
        }

        public AdvanceConfig GetAdvanceCongifInCombinedSetting(int combinedSettingId, AdvanceConfigType configType)
        {
            return getValueFromCache(getAdvanceConfigKey(combinedSettingId), configType, () =>
            {
                return CoreDbCalls.GetInstance().GetAdvanceConfigInCombinedSetting(combinedSettingId, configType);
            });
        }

        public AdvanceConfig GetAdvanceCongifOnNas(LongIdInfo longNasId, AdvanceConfigType configType)
        {
            var combinedSettingId = GetRouterBasic(longNasId).combinedSettingId;
            return GetAdvanceCongifInCombinedSetting(combinedSettingId, configType);
        }

        public string GetAdvanceConfigValue(int combinedSettingId, AdvanceConfigType configType)
        {
            AdvanceConfig config = GetAdvanceCongifInCombinedSetting(combinedSettingId, configType);
            if (config != null && config.parameters != null && config.parameters.Length > 0)
            {
                if (!string.IsNullOrEmpty(config.parameters[0]))
                    return config.parameters[0];
            }
            return null;
        }

        public int GetAdvanceConfigIntValue(int combinedSettingId, AdvanceConfigType configType)
        {
            AdvanceConfig config = GetAdvanceCongifInCombinedSetting(combinedSettingId, configType);
            if (config != null && config.parameters != null && config.parameters.Length > 0)
            {
                if (!string.IsNullOrEmpty(config.parameters[0]))
                    return int.Parse(config.parameters[0]);
            }
            return 0;
        }

        public StoreOperatingDetails GetOperatingHours(LongIdInfo longNasId)
        {
            String json = GetAdvanceCongifOnNas(longNasId, AdvanceConfigType.OPERATING_HOURS).value;
            if (json == null) return null;
            StoreOperatingDetails operatingHours = new StoreOperatingDetails();
            operatingHours = JsonConvert.DeserializeObject<StoreOperatingDetails>(json);
            return operatingHours;
        }

        public KeyValuePair<List<string>, List<string>> getPhoneNumberWhiteListAndBlockList(LongIdInfo nasid)
        {
            return memoryCache.GetOrCreate(WHITE_BLOCK_LIST + nasid, (entry) =>
            {
                entry.AbsoluteExpirationRelativeToNow = TimeSpan.FromMinutes(15);
                return getValueFromCache(WHITE_BLOCK_LIST, nasid, () =>
                {
                    Dictionary<string, string> list = CoreDbCalls.GetInstance().GetListChecks(nasid, ListCheckDataType.WHITE_LIST);
                    List<string> whiteList = list.Keys.ToList();

                    list = CoreDbCalls.GetInstance().GetListChecks(nasid, ListCheckDataType.BLOCKED_LIST);
                    List<string> blackList = list.Keys.ToList();
                    return new KeyValuePair<List<string>, List<string>>(whiteList, blackList);
                });
            });
        }

        public List<string> getVipPhoneNumbers(LongIdInfo nasid)
        {
            return memoryCache.GetOrCreate(VIP_LIST + nasid, (entry) =>
            {
                entry.AbsoluteExpirationRelativeToNow = TimeSpan.FromMinutes(15);
                return getValueFromCache(VIP_LIST, nasid, () =>
                {
                    Dictionary<string, string> list = CoreDbCalls.GetInstance().GetListChecks(nasid, ListCheckDataType.VIP_LIST);
                    List<string> vipList = new List<string>();
                    foreach (KeyValuePair<string, string> s in list)
                    {
                        if (!String.IsNullOrEmpty(s.Key))
                            vipList.Add(s.Key);
                    }
                    return vipList;
                });
            });
        }

        public void SetUserSessions(WifiUser user, UserSession userSession, long planId)
        {
            if (user.storegroupid == 1 && planId == 0)
            {
                Logger.GetInstance().Info($"Found invalid case of zero plan id. User:{JsonConvert.SerializeObject(user)}, UserSession: {JsonConvert.SerializeObject(userSession)}");
				return;
			}  

            setCache(UserSession.GetConfigKey(user, planId) + 0, userSession, userSession.policyStopTime - DateTime.UtcNow);
            SetPlanMacMapping(user, planId);
        }

        public void UpgradeUserSession(WifiUser user, long planId, int combinedSettingId, bool isVip)
        {
            if (!string.IsNullOrEmpty(user.mobile))
            {
                Reset(FDMConfig.GetFDMConfigKey(user), "0");
                var userSession = GetObjectIfExists<UserSession>(UserSession.GetConfigKey(user, planId));
                if (userSession != null && userSession.radiusUserResponse == RadiusUserResponse.SUCCESS && userSession.sessions.Count > 0)
                {
                    var oldSessions = userSession.sessions;
                    userSession = WifidogCacheHelper.GetInstance().getUserSessionValue(user, planId);
                    userSession.AddSessions(oldSessions);
                    SetUserSessions(user, userSession, planId);
                }
            }
        }

        public void DeleteUserSession(User user)
        {
            if (!string.IsNullOrEmpty(user.mobile))
            {
                ResetBatch(new string[] {
                    FDMConfig.GetFDMConfigKey(user) + "0",
                    PlanMacMapping.GetConfigKey(user)
                });
            }
        }

        public UserSession GetUserSessions(WifiUser user, bool isVip, out bool noMappingExists)
        {
            var planMacMapping = GetCurrentDevicePlanMacMapping(user, out bool isExpired);
            if (planMacMapping == null)
            {
                noMappingExists = true;
                return null;
            }

            noMappingExists = false;

            if (isExpired)
            {
                var mappings = CoreCacheHelper.GetInstance().GetActivePlanMacMapping(user);
                var fdmConfigs = WifidogCacheHelper.GetInstance().GetFDMConfig(user, isVip);
                var fdmPlan = fdmConfigs.Find(m => m.id == planMacMapping.planId);
                if (fdmPlan != null && mappings.Where(n => n.planId == planMacMapping.planId).Count() < CoreUtil.GetOverriddenDeviceLimit(user.backEndNasid, fdmPlan.deviceAllowed))
                {
                    SetPlanMacMapping(user, planMacMapping.planId);
                }
                else
                    return null;
            }
            return getValueFromCache<UserSession>(UserSession.GetConfigKey(user, planMacMapping.planId), 0);
        }

        public T GetObjectIfExists<T>(string key)
        {
            var data = getCache(key + 0);

            if (data.IsNullOrEmpty)
            {
                return default(T);
            }

            return JsonConvert.DeserializeObject<T>(data);
        }

        public string[] getLoginPageConfiguration(LongIdInfo longNasId)
        {
            AdvanceConfig conf = GetAdvanceCongifOnNas(longNasId, AdvanceConfigType.LOGIN_PAGE_IMAGE);
            return conf.parameters;
        }

        public string GetLongUrl(string id)
        {
            if(string.IsNullOrEmpty(id))
                return null;

            ModelDynamoDb<UrlShortner> modelDynamoDb = new ModelDynamoDb<UrlShortner>();
            var shortUrl = modelDynamoDb.GetById(id, string.Empty);

            if (shortUrl == null)
            {
                return getValueFromCache<string>(SHORT_URL, id, () =>
                {
                    return null;
                });
            }
            else
            {
                return shortUrl.url;
            }
        }

        public static string GetCacheConnectionString()
        {
            int port = I2e1ConfigurationManager.GetInstance().GetInt("RedisCachePort");
            string server = I2e1ConfigurationManager.GetInstance().GetSetting("RedisCacheServer");
            string password = I2e1ConfigurationManager.GetInstance().GetSetting("RedisCachePassword");
         
            var connection = string.Format("{0}:{1},ssl=False,abortConnect=False,syncTimeout=5000,responseTimeout=5000,writeBuffer=********",
                I2e1ConfigurationManager.GetInstance().GetPrivateIP(server), port);
            if (!string.IsNullOrEmpty(password))
            {
                connection += ",password=" + password;
            }
            Logger.GetInstance().Info("cache connection url: " + connection);
            return connection;
        }
        public bool IsServer10Account(int nasid)
        {
            var list = getValueFromCache(SERVER_10_ACCOUNTS, 0, () =>
            {
                return new List<int>();
            });
            return list.Contains(nasid);
        }
        public bool isServer10AppVersion(string appVersion)
        {
            Dictionary<string, int> allAppVersionMap = new Dictionary<string, int>();
            var list = getValueFromCache(SERVER_10_APP_VERSIONS, 0, () =>
            {
                return new List<string>() { "-999" };
            });
            return list.Contains(appVersion);
        }

        public BasicConfig GetUserBasicConfigInCombinedSetting(User user, BasicConfigType configType)
        {
            int combinedSettingId = user.combinedSettingId;
            var conf = getValueFromCache(getUserBasicConfigKey(combinedSettingId), configType, () =>
            {
                var config = CoreDbCalls.GetInstance().GetUserBasicConfigInCombinedSetting(user.backEndNasid.shard_id, combinedSettingId, configType);
                if (configType == BasicConfigType.LANDING_PAGE)
                {
                    if (config != null && !string.IsNullOrEmpty(config.value) && !config.value.StartsWith("http"))
                    {
                        config.value = "http://" + config.value;
                    }
                }
                return config;
            });

            if (conf != null && !string.IsNullOrEmpty(conf.value) && configType == BasicConfigType.LANDING_PAGE)
            {
                if (conf.value.IndexOf("?") > 0)
                {
                    conf.value = conf.value + "&hotspotname=" + user.backEndNasid;
                }
                else
                {
                    conf.value = conf.value + "?hotspotname=" + user.backEndNasid;
                }
                conf.value = conf.value.Replace("{{NASID}}", user.nasid).Replace("{{MOBILE}}", user.mobile);
            }
            return conf;
        }

        public List<UserGroupNew> GetUserGroupInformation(LongIdInfo nasid)
        {
            return getValueFromCache(NAS_USER_GROUPS, nasid, () =>
            {
                return CoreDbCalls.GetInstance().GetUserGroupInformation(nasid);
            });
        }

        public int FindUserGroupForMe(string username, LongIdInfo nasid)
        {
            if (String.IsNullOrWhiteSpace(username))
            {
                return 0;
            }
            var result = GetUserGroupInformation(nasid).Find(s => (s.values.IndexOf(username) > -1));
            return result == null ? 0 : result.groupId;
        }

        public int FindUserGroupForMe(User user)
        {
            if (String.IsNullOrWhiteSpace(user.mobile))
            {
                return 0;
            }
            return FindUserGroupForMe(user.mobile, user.backEndNasid);
        }

        public bool isLatestAppVersion(string appVersion)
        {
            Dictionary<string, object> allAppVersionMap = new Dictionary<string, object>();
            allAppVersionMap = MemoryCacheHelper.GetValueFromCache(memoryCache, LATEST_APP_VERSIONS0, () =>
            {
                var latestVersions = CoreCacheHelper.GetInstance().getValueFromCache(CoreCacheHelper.LATEST_APP_VERSIONS, 0, () =>
                {
                    return new Dictionary<string, object>() { { "sales", -999 }, { "sales_time", "2022-02-22" }, { "gold", -999 }, { "gold_time", "2022-02-22" }, { "HOME_ROUTER", -999 }, { "HOME_ROUTER_time", "2022-02-22" } };
                });
                return latestVersions;
            }, TimeSpan.FromMinutes(10));

            int appLastIndex = appVersion.LastIndexOf("_");
            string name = appVersion.Substring(0, appLastIndex);
            int version = Int32.Parse(appVersion.Substring(appLastIndex + 1, appVersion.Length - appLastIndex - 1));
            if (allAppVersionMap.ContainsKey(name))
            {
                if (version >= Convert.ToInt64(allAppVersionMap[name]))
                {
                    return true;
                }
            }
            return false;
        }
		public void SetWiomNetTransactionInfo(string orderId, object data, int minutesToExpiry)
		{
			setCache("WIOM_NET_PLAN_" + orderId, data, minutesToExpiry);
		}
		public void SetWiomNetDynamicTransactionInfo(string orderId, object data, int minutesToExpiry)
		{
			setCache("WIOM_NET_PLAN_DYNAMIC_" + orderId, data, minutesToExpiry);
		}
		public string GetWiomNetTransactionInfo(string orderId)
		{
            try
            {
                return getValueFromCache<string>("WIOM_NET_PLAN_", orderId);

            }
            catch(Exception ex)
            {
                Logger.GetInstance().Error($"GetWiomNetTransactionInfo: orderId {orderId} ex: {ex.Message}");
                return null;
            }
		}
		public string GetWiomNetDynamicTransactionInfo(string orderId)
		{
			try
			{
				return getValueFromCache<string>("WIOM_NET_PLAN_DYNAMIC_", orderId);

			}
			catch (Exception ex)
			{
				Logger.GetInstance().Error($"GetWiomNetDynamicTransactionInfo: orderId {orderId} ex: {ex.Message}");
				return null;
			}
		}
	}
}