using Amazon.SQS.Model;
using i2e1_basics.Utilities;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace i2e1_core.Utilities
{
    public enum ServiceBusQueue
    {
        plan_start_check,
        wiom_net
    }

    public enum WiomQueue
    {
        NOTIFY_DELAYED = 1,
        HOME_ROUTER_PAYMENT_SUCCESS = 2,
        DELIVERY = 3,
        P_RADACCT_QUEUE = 4,
        CUSTOMER = 5,
        PAYMENT = 6,
        PARTNER = 7,
        GINIE = 8,
        AWS_QUEUE_BIG_QUERY_LOGS = 9,
        TASKS = 10,
        REMOTE = 11
    }

    public class CoreSQSHelper : BasicSQSHelper
    {
        protected static Dictionary<WiomQueue, string> queueMapping = new Dictionary<WiomQueue, string>()
        {
            { WiomQueue.NOTIFY_DELAYED, CoreSQSHelper.GetQueueARN(I2e1ConfigurationManager.AWS_QUEUE_WIOM_FIFO) },
            { WiomQueue.HOME_ROUTER_PAYMENT_SUCCESS, CoreSQSHelper.GetQueueARN(I2e1ConfigurationManager.AWS_QUEUE_HOME_ROUTER_PAYMENT_SUCCESS) },
            { WiomQueue.DELIVERY, CoreSQSHelper.GetQueueARN(I2e1ConfigurationManager.AWS_QUEUE_DELIVERY) },
            { WiomQueue.CUSTOMER, CoreSQSHelper.GetQueueARN(I2e1ConfigurationManager.AWS_QUEUE_CUSTOMER_FIFO) },
            { WiomQueue.PAYMENT, CoreSQSHelper.GetQueueARN(I2e1ConfigurationManager.AWS_QUEUE_PAYMENT_FIFO) },
            { WiomQueue.GINIE, CoreSQSHelper.GetQueueARN(I2e1ConfigurationManager.AWS_QUEUE_GINIE_FIFO) },
            { WiomQueue.TASKS, CoreSQSHelper.GetQueueARN(I2e1ConfigurationManager.AWS_QUEUE_TASKS) },
            { WiomQueue.REMOTE, CoreSQSHelper.GetQueueARN("remote.fifo") }

        };

        public static void SendMessageBatch(WiomQueue wiomQueue, string mobile, List<SQSMessage> messages)
        {
            Logger.GetInstance().Info($"\nSending a batch of messages to queue\n  {wiomQueue}");
            BasicSQSHelper.SendMessageBatch(queueMapping[wiomQueue], mobile, messages);
        }

        public static void SendStandardQueueMessage(WiomQueue wiomQueue, SQSMessage messages)
        {
            Logger.GetInstance().Info($"\nSending of messages to Standard queue\n  {wiomQueue}");
            BasicSQSHelper.SendStandardQueueMessage(queueMapping[wiomQueue], messages);
        }

        protected static async Task<ReceiveMessageResponse> GetMessage(WiomQueue wiomQueue, int visibilityTimeout, int waitTime = 0)
        {
            return await GetMessage(queueMapping[wiomQueue], visibilityTimeout, waitTime);
        }

        public static void RegisterListener(WiomQueue wiomQueue, double intervalInSeconds, QueueResponseHandler queueResponseHandler, int visibilityTimeout = 60)
        {
            RegisterListener(queueMapping[wiomQueue], intervalInSeconds, queueResponseHandler, visibilityTimeout);
        }

        public static async Task DeleteMessageBatchAsyncNonVoid(WiomQueue wiomQueue, List<string> receiptHandles)
        {
            await DeleteMessageBatchAsyncNonVoid(queueMapping[wiomQueue], receiptHandles);
        }

        public static void DeleteMessageBatch(WiomQueue wiomQueue, List<string> receiptHandles)
        {
            DeleteMessageBatch(queueMapping[wiomQueue], receiptHandles);
        }

        public static void DeleteMessage(WiomQueue wiomQueue, string receiptHandle)
        {
            DeleteMessage(queueMapping[wiomQueue], receiptHandle);
        }
    }
}
