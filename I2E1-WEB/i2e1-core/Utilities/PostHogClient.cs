using System;
using System.Collections.Generic;
using System.Net.Http;
using System.Text;
using System.Text.Json;
using System.Threading.Tasks;

namespace i2e1_core.Utilities
{
    public class PostHogClient
    {
        private readonly HttpClient _httpClient;
        private readonly string _apiKey;
        private readonly string _apiUrl = "https://eu.i.posthog.com/capture/";

        public PostHogClient(string apiKey)
        {
            _httpClient = new HttpClient();
            _apiKey = apiKey;
        }

        public async Task CaptureEvent(string eventName, string distinctId, Dictionary<string, object> properties = null, DateTime? timestamp = null)
        {
            // Create the request payload
            var payload = new
            {
                api_key = _apiKey,
                @event = eventName,
                properties = properties,
                timestamp = timestamp?.ToString("o") // ISO 8601 format
            };

            payload.properties["distinct_id"] = distinctId;

            // Serialize to JSON
            var jsonContent = JsonSerializer.Serialize(payload);
            var content = new StringContent(jsonContent, Encoding.UTF8, "application/json");

            // Send the request
            var response = await _httpClient.PostAsync(_apiUrl, content);

            // Ensure success
            response.EnsureSuccessStatusCode();
        }

        // Example usage:
        public async Task Example()
        {
            var client = new PostHogClient("phc_jTwf7M1g9gE7gTB7lpAefeVxQZ7yAHKcUuCvC3dtiCw");

            var properties = new Dictionary<string, object>
    {
        { "key1", "value1" },
        { "key2", "value2" }
    };

            await client.CaptureEvent(
                "[event name]",
                "[your users' distinct id]",
                properties,
                DateTime.UtcNow // Optional: you can provide a specific timestamp
            );
        }
    }
}
