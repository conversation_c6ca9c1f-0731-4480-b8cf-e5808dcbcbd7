using i2e1_basics.Utilities;
using i2e1_core.Models;
using i2e1_core.services;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Net;
using System.Threading.Tasks;
using System.Web;
using Logger = i2e1_basics.Utilities.Logger;

namespace i2e1_core.Utilities
{
    public class SmsSentStatus
    {
        public string status { get; set; }

        public string trackingId { get; set; }
    }

    public class KalreyaBulkRequestUnit
    {
        public string to { get; set; }
        public string msgid { get; set; }
        public string message { get; set; }
        public string custom1 { get; set; }
        public string custom2 { get; set; }
        public string sender { get; set; }
    }

    public class Ka<PERSON>reyaResponse
    {
        public string id { get; set; }
        public string mobile { get; set; }
        public string status { get; set; }
    }

    public class CoreSmsSender
    {
        public static void SendInfiniOTP(string mobile, string random, string message = null, string senderId = null)
        {
            try
            {
                if (string.IsNullOrEmpty(message))
                    message = random + " is your OTP for accessing WiFi\n\n- WIOM";
                if (string.IsNullOrEmpty(senderId))
                    senderId = "myWIOM";
                Task.Run(() =>
                {
                    SendTransactionalSMSViaInfini(message, mobile, senderId);
                });
            }
            catch
            {
            }
        }

        public static async void SendGupshupSms(string mobile, string random)
        {
            try
            {
                string sms = HttpUtility.UrlEncode(random + " is your OTP for accessing WiFi\n\n- WIOM");
                string url = "https://enterprise.smsgupshup.com/GatewayAPI/rest?method=SendMessage&send_to=" + mobile + "&msg=" + sms + "&msg_type=TEXT&userid=**********&auth_scheme=plain&password=qbX5hlkO5&v=1.1&format=text";
                using (var client = new WebClient())
                {
                    await client.DownloadStringTaskAsync(url);
                }
            }
            catch
            {
            }
        }

        public static void SendGupshupSmsWiomRegistration(string mobile, string password)
        {
            try
            {
                string sms = HttpUtility.UrlEncode(string.Format("Use {0} as user id and {1} as password to login to WIOM admin portal. https://admin.wiom.in", mobile, password));
                string url = "https://enterprise.smsgupshup.com/GatewayAPI/rest?method=SendMessage&send_to=" + mobile + "&msg=" + sms + "&msg_type=TEXT&userid=**********&auth_scheme=plain&password=qbX5hlkO5&v=1.1&format=text&mask=myWIOM";
                Task.Run(() =>
                {
                    WebClient client = new WebClient();
                    var response = client.DownloadString(url);
                    return;
                });
            }
            catch
            {
            }
        }

        public static void SendGupshupSmsWiomRecoverUsername(string mobile)
        {
            try
            {
                string sms = HttpUtility.UrlEncode(String.Format("Use {0} as user id to login to WIOM admin portal. https://admin.wiom.in", mobile));
                string url = "https://enterprise.smsgupshup.com/GatewayAPI/rest?method=SendMessage&send_to=" + mobile + "&msg=" + sms + "&msg_type=TEXT&userid=**********&auth_scheme=plain&password=qbX5hlkO5&v=1.1&format=text&mask=myWIOM";
                Task.Run(() =>
                {
                    WebClient client = new WebClient();
                    var response = client.DownloadString(url);
                    return;
                });
            }
            catch
            {
            }
        }

        public static void SendGupshupSmsWiomRecoverPassword(string mobile, string password)
        {
            try
            {

                string sms = HttpUtility.UrlEncode(String.Format("Use {0} as password to login to WIOM admin portal. https://admin.wiom.in", password));
                string url = "https://enterprise.smsgupshup.com/GatewayAPI/rest?method=SendMessage&send_to=" + mobile + "&msg=" + sms + "&msg_type=TEXT&userid=**********&auth_scheme=plain&password=qbX5hlkO5&v=1.1&format=text&mask=myWIOM";
                Task.Run(() =>
                {
                    WebClient client = new WebClient();
                    var response = client.DownloadString(url);
                    return;
                });
            }
            catch
            {
            }
        }

        public static void SendGupshupSmsWiomPlanOnlinePayment(string mobile, string payUrl, string lang)
        {
            string sms;
            if (lang == "en")
            {
                sms = string.Format("Dear customer, complete your Wiom internet plan payment via this link:\r\n{0}\r\n\r\nThank you,\r\nWiom", payUrl);
            }
            else
            {
                sms = $@"अपने Wiom इंटरनेट प्लान की पेमेंट करें {payUrl}";
            }
            SendSMSViaGupshup(sms, mobile, "myWIOM", lang == "hi" ? true : false);
        }

        public static SmsSentStatus SendSMSViaGupshup(string message, string mobile, string senderId, bool unicode = false)
        {
            try
            {
                if (!I2e1ConfigurationManager.IS_PROD)
                {
                    string subject = "SMS Sent to " + mobile;
                    BasicEmailSender.SendRawEmailTo("<EMAIL>", Constants.SMS_EMAIL_LIST, subject, message, "i2e1 Support", null, "text/plain", true, null);
                }

                string sms = HttpUtility.UrlEncode(message);
                string url = "https://enterprise.smsgupshup.com/GatewayAPI/rest?method=SendMessage&send_to=" + mobile
                    + "&msg=" + sms
                    + $"&mask={senderId}&msg_type={(unicode ? "Unicode_Text" : "TEXT")}&userid=**********&auth_scheme=plain&password=qbX5hlkO5&v=1.1&format=text";
                using (WebClient client = new WebClient())
                {
                    var responce = client.DownloadString(url);
                }

                try
                {
                    Dictionary<string, string> data = new Dictionary<string, string>();
                    data["body"] = message;
                    DeliveryMsg msg = new DeliveryMsg()
                    {
                        accountIds = new KeyValuePair<string, List<LongIdInfo>>("", null),
                        userIds = null,
                        msgType = DeliveryMsg.MsgType.SMS,
                        msg = data,
                        numbers = new List<string> { mobile }
                    };
                    DeliveryUtility.StoreMessages(msg);

                    return new SmsSentStatus() { status = "success", trackingId = "DEFAULT_HARD_CODING" };
                }
                catch(Exception ex)
                {
                    Logger.GetInstance().Error(String.Format("Exception in uploading message in S3 inside SendSMSViaGupshup : {0}", JsonConvert.SerializeObject(ex.Message)));
                }
            }
            catch(Exception ex)
            {
                Logger.GetInstance().Error(String.Format("Exception in uploading message in S3 inside SendSMSViaGupshup : {0}", JsonConvert.SerializeObject(ex.Message)));
            }
            return new SmsSentStatus() { status = "failure" };
        }

        public static SmsSentStatus SendTransactionalSMSViaInfini(string message, string phoneNumbers, string senderId, bool unicode = false, bool toGupsup = false)
        {
            if (toGupsup || !unicode)
            {
                return CoreSmsSender.SendSMSViaGupshup(message, phoneNumbers, senderId, unicode);
            }

            return SolutionsInfiniHTTPApi(message, phoneNumbers, senderId, "A2d5160a5721f103af60d92a19081e8cd", unicode);
        }

        public static SmsSentStatus SendPromotionalSMSViaInfini(string message, string phoneNumbers, string senderId, bool unicode = false)
        {
            return SolutionsInfiniHTTPApi(message, phoneNumbers, senderId, "A0e6ef883b2ad33424b2e0615855691c8", unicode);
        }

        private static SmsSentStatus SolutionsInfiniHTTPApi(string message, string phoneNumbers, string senderId, string apiKey, bool unicode = false)
        {
            string url = null;
            if (string.IsNullOrEmpty(apiKey))
                throw new ArgumentNullException("apiKey");

            if (string.IsNullOrWhiteSpace(phoneNumbers))
            {
                return new SmsSentStatus() { status = "failure" };
            }

            try
            {
                if (!I2e1ConfigurationManager.IS_PROD)
                {
                    string subject = "SMS Sent to " + phoneNumbers;
                    BasicEmailSender.SendRawEmailTo("<EMAIL>", Constants.SMS_EMAIL_LIST, subject, message, "i2e1 Support", null, "text/plain", true, null);
                }

                url = "http://api-alerts.solutionsinfini.com/v3/?method=sms"
                     + "&api_key=" + apiKey
                     + "&sender=" + senderId
                     + "&to=" + HttpUtility.UrlEncode(phoneNumbers)
                     + "&message=" + HttpUtility.UrlEncode(message);
                if (unicode)
                {
                    url = url + "&unicode=" + (unicode ? "1" : "0");
                }

                if (senderId == "BULKSMS")
                {
                    url = "http://api-promo.solutionsinfini.com/v3/?method=sms"
                    + "&api_key=" + apiKey
                    + "&sender" + "BULKSMS"
                    + "&to" + HttpUtility.UrlEncode(phoneNumbers)
                    + "&message=" + HttpUtility.UrlEncode(message);
                }

                WebClient client = new WebClient();
                Stream data = client.OpenRead(url);
                StreamReader reader = new StreamReader(data);
                string responseStr = reader.ReadToEnd();
                data.Close();
                reader.Close();

                try
                {
                    dynamic response = JsonConvert.DeserializeObject(responseStr);

                    Dictionary<string, string> dataDict = new Dictionary<string, string>();
                    dataDict["body"] = message;
                    DeliveryMsg msg = new DeliveryMsg()
                    {
                        accountIds = new KeyValuePair<string, List<LongIdInfo>>("", null),
                        userIds = null,
                        msgType = DeliveryMsg.MsgType.SMS,
                        msg = dataDict,
                        numbers = new List<string> { phoneNumbers }
                    };
                    DeliveryUtility.StoreMessages(msg);
                    return new SmsSentStatus() { status = response["status"].ToString(), trackingId = response["data"]["group_id"].ToString() };
                }
                catch(Exception ex)
                {
                    Logger.GetInstance().Error(String.Format("Exception in uploading message in S3 in SolutionsInfiniHTTPApi : {0}", JsonConvert.SerializeObject(ex.Message)));
                }
            }
            catch(Exception ex)
            {
                string subject = "Unable to Send SMS to (Mob no - " + phoneNumbers + ")";
                string emailBody = "Url: "+ url +
                 "<br/>Mobile: " + phoneNumbers + "<br/>" +
                 "Text: " + message + "<br/>" +
                 "SenderId: " + senderId + "<br/>" + ex.ToString();

                BasicEmailSender.SendRawEmailTo("<EMAIL>", Constants.failureEmailList, subject, emailBody, "WIOM Support", null, "text/html", true, null);
            }
            return new SmsSentStatus() { status = "failure" };
        }

        public static dynamic GetSolutionsInfiniSMSGroupReports(string groupId, string senderId)
        {
            string apiKey = GetSolutionInfiniApiKey(senderId);
            WebClient client = new WebClient();
            string url = "http://api-alerts.solutionsinfini.com/v3/?method=sms.groupstatus&api_key=" + apiKey;
            if (apiKey == "A0e6ef883b2ad33424b2e0615855691c8")
            {
                url = "http://api-promo.solutionsinfini.com/v3/?method=sms.groupstatus&api_key=" + apiKey;
            }

            url = url +
                "&format=json&groupid=" + groupId +
                "&numberinfo=1";

            string json = client.DownloadString(url);
            return JsonConvert.DeserializeObject(json);
        }

        public static string GetSolutionInfiniApiKey(string senderId)
        {
            if (senderId == "BULKSMS" || string.IsNullOrEmpty(senderId))
            {
                //no senderid configured
                //sending via promotional gateway
                return "A0e6ef883b2ad33424b2e0615855691c8";
            }
            else if (senderId == "OMIDEV")
            {
                return "Af3f02185031b9e4ac29f346f5402858b";
            }
            else if (senderId.ToLower().IndexOf("open-") == 0)
            {
                //this is transactional open to dnd - i2e1-open
                return "A2d5160a5721f103af60d92a19081e8cd";
            }
            else
            {
                //this is transactional close to dnd - i2e1
                return "A0e235d6163c8337717d1d31db4a6266c";
            }
        }

        public static string GetSolutionInfiniSenderId(string senderId)
        {
            if (!Constants.IS_PRODUCTION) senderId = "OMIDEV";
            if (senderId.ToLower().IndexOf("open-") == 0)
                return senderId.Substring(5);
            else
                return senderId;
        }

        public static string GetSolutionInfiniBaseUrl(string senderId)
        {
            if (senderId == "BULKSMS")
            {
                return "http://promo.sinfini.com/api/v4/index.php";
            }
            else
            {
                return "http://alerts.sinfini.com/api/v4/index.php";
            }
        }

        public static string sendBulkSMSRequestToKalreya(string url, int retryCount,string smsObject)
        {
            if (retryCount <= 3)
            {
                try
                {
                    string jsonText;
                    ImpatientWebClient client = new ImpatientWebClient(15000);
                    client.Headers[HttpRequestHeader.ContentType] = "raw";
                    jsonText = client.UploadString(url,smsObject);
                    return jsonText;
                }
                catch(System.Exception ex)
                {
                    retryCount++;
                    return sendBulkSMSRequestToKalreya(url, retryCount,smsObject);
                }
            }
            else
            {
                //save failed batch to analytics
                return "{\"status\":\"FAILED API REQUEST\"}";
            }

        }

        public static JArray SendSMSViaSolutionInfiniV4(string message, string phoneNumbers, string senderId)
        {
            if (!Constants.IS_PRODUCTION) senderId = "OMIDEV";
            string apiKey = GetSolutionInfiniApiKey(senderId);
            senderId = GetSolutionInfiniSenderId(senderId);
            string baseurl = GetSolutionInfiniBaseUrl(senderId);
            bool isUnicode = CoreSmsSender.containsUnicodeCharacter(message);

            string parameters = "?api_key=" + 
                apiKey + "&method=sms&message=" + HttpUtility.UrlEncode(message) + 
                "&sender=" + senderId + "&unicode=" + (isUnicode ? "1" : "0");
            Logger.GetInstance().Info("infini url=" + baseurl + parameters + ", message=" + message + " to" + phoneNumbers);
            string jsonText = "";
            try
            {
                ImpatientWebClient client = new ImpatientWebClient(15000);
                client.Headers[HttpRequestHeader.ContentType] = "application/x-www-form-urlencoded";
                jsonText = client.UploadString(baseurl + parameters, "to=" + phoneNumbers);
                //string jsonText = client.DownloadString(baseurl + parameters);
            }
            catch (WebException e)
            {
                Logger.GetInstance().Error("Exception in sending infini sms, senderId=" + senderId + ", message=" + message + " " + e);
            }
            JObject smsDynamicResult = new JObject();
            try
            { 
                smsDynamicResult = JsonConvert.DeserializeObject<JObject>(jsonText);
                if (smsDynamicResult["status"].ToString() == "OK")
                {
                    return (JArray)smsDynamicResult["data"];
                }
                else
                {
                    Logger.GetInstance().Info("infini response is not in correct format, senderId=" + senderId + ", message=" + message);
                }
            }
            catch (Exception e)
            {
                Logger.GetInstance().Error("infini response is not in correct format, senderId=" + senderId + ", response=" + e.Message + " " + jsonText);
            }
            return null;
        }

        public static bool containsUnicodeCharacter(string input)
        {
            const int MaxAnsiCode = 255;

            return input.Any(c => c > MaxAnsiCode);
        }

        public static void SendWiomGoldPaymentSuccessSMS(string mobile, int price, string ownerNumber, DateTime nextDueDate)
        {
            string message = $"Payment received - Rs. {price} for WIOM internet.\nPhone No. - {ownerNumber}\nNext Due Date - {nextDueDate.Day}-{nextDueDate.Month}-{nextDueDate.Year}\nUse WIOM App to view Bill: d.wiom.in/d/p";
            string numbers = mobile;
            if (mobile != ownerNumber)
                numbers += "," + ownerNumber;

            CoreSmsSender.SendTransactionalSMSViaInfini(message, numbers, "myWIOM");
        }

        public static bool SendSMSToAllUsersOfAccount(LongIdInfo longAccountId, string message, string textType, bool unicode = false, bool toGupshup = false)
        {
            List<ManagementUser> allUsers = new List<ManagementUser>();
            if (longAccountId != null && longAccountId.local_value != 0)
            {
                allUsers = CoreAccountService.GetUsersInAccount(longAccountId);
                if(allUsers.Count > 0)
                {
                    CoreSmsSender.SendTransactionalSMSViaInfini(message, allUsers[0].username, "myWIOM", textType == "hi", toGupshup);
                }
            }
            return true;
        }
    }

    public class CoreWhatsAppSender
    {

        public static void SendGupshupWhatsAppCustomer(string mobile, string header, string footer, string message, bool isTemplate = false)
        {
            try
            {
                header = string.IsNullOrEmpty(header) ? string.Empty : HttpUtility.UrlEncode(header);
                footer = string.IsNullOrEmpty(footer) ? string.Empty : HttpUtility.UrlEncode(footer);
                isTemplate = isTemplate || !string.IsNullOrEmpty(header) || !string.IsNullOrEmpty(footer);
                string url = $"https://media.smsgupshup.com/GatewayAPI/rest?userid=**********&password=*kqpW9L%23&send_to={mobile}&v=1.1&format=json&msg_type=TEXT&method=SENDMESSAGE&msg={HttpUtility.UrlEncode(message)}{(isTemplate ? "&isTemplate=true" : string.Empty)}&header={header}&footer={footer}";
                using (var client = new WebClient())
                {
                    client.DownloadString(url);
                }
            }
            catch
            {
            }
        }

        public static void GupshupWhatsAppOptInCustomer(string mobile)
        {
            try
            {
                string url = $"https://media.smsgupshup.com/GatewayAPI/rest?method=OPT_IN&format=json&userid=**********&password=*kqpW9L%23&phone_number={mobile}&v=1.1&auth_scheme=plain&channel=WHATSAPP";
                using (var client = new WebClient())
                {
                    client.DownloadString(url);
                }
            }
            catch
            {
            }
        }

        public static void SendGupshupWhatsAppPartner(string mobile, string header, string footer, string message, bool isTemplate = false)
        {
            try
            {
                header = string.IsNullOrEmpty(header) ? string.Empty : HttpUtility.UrlEncode(header);
                footer = string.IsNullOrEmpty(footer) ? string.Empty : HttpUtility.UrlEncode(footer);
                isTemplate = isTemplate || !string.IsNullOrEmpty(header) || !string.IsNullOrEmpty(footer);
                string url = $"https://media.smsgupshup.com/GatewayAPI/rest?userid=2000226564&password=GZMy99nH&send_to={mobile}&v=1.1&format=json&msg_type=TEXT&method=SENDMESSAGE&msg={HttpUtility.UrlEncode(message)}{(isTemplate ? "&isTemplate=true" : string.Empty)}&header={header}&footer={footer}";
                using (var client = new WebClient())
                {
                    client.DownloadString(url);
                }
            }
            catch
            {
            }
        }

        public static void GupshupWhatsAppOptInPartner(string mobile)
        {
            try
            {
                string url = $"https://media.smsgupshup.com/GatewayAPI/rest?method=OPT_IN&format=json&userid=2000226564&password=GZMy99nH&phone_number={mobile}&v=1.1&auth_scheme=plain&channel=WHATSAPP";
                using (var client = new WebClient())
                {
                    client.DownloadString(url);
                }
            }
            catch
            {
            }
        }

    }
}