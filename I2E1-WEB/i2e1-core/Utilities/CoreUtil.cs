using Amazon.Lambda;
using Amazon.Lambda.Model;
using Force.Crc32;
using i2e1_basics.Database;
using i2e1_basics.Utilities;
using i2e1_core.Models.Entity;
using Microsoft.AspNetCore.Http;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.IO;
using System.Text;
using System.Threading.Tasks;
using wiom_routerplan_share.Models.RouterPlan;

namespace i2e1_core.Utilities
{
	public class CoreUtil: i2e1_basics.Utilities.BasicUtil
    {
        public static string GetNormalisedMac(string value)
        {
            if (string.IsNullOrEmpty(value))
                return null;
            if (!value.Contains(':'))
            {
                if (value.Contains('-'))
                    value = value.Replace('-', ':');
                else
                {
                    StringBuilder sb = new StringBuilder();
                    for (int i = 0; i < value.Length; ++i)
                    {
                        if (i != 0 && i % 2 == 0)
                            sb.Append(':');
                        sb.Append(value[i]);
                    }
                    value = sb.ToString();
                }
            }

            return value.ToUpper();
        }

        public static string GetHeaderValue(HttpContext httpContext, string header_key)
        {
            return httpContext.Request.Headers[header_key].ToString();
        }

        public static bool CheckIfMac(string username)
        {
            username = username.Trim();
            if (username == string.Empty || username == "-")
                return false;

            int count = 0;
            foreach (char x in username)
            {
                if (x == '-' || x == ':')
                    count++;
            }
            return count == 5;
        }

        public static string createCompleteUrl(string image)
        {
            if (string.IsNullOrEmpty(image))
                return image;
            string finalurl = "https://cdn.linq.app" + image;
            return finalurl;
        }

        public static void ActivateRoamingPlan(LongIdInfo nasid, string mobile, DateTime startTime, DateTime stopTime, long planId, string transactionId)
        {
            /*var pUser = new PassportUser()
            {
                mobile = mobile,
                otpIssuedTime = startTime,
                otpExpiryTime = stopTime,
                otp = "ROAM"
            };*/

            var pUser = new SecondaryRouterPlan()
            {
                nasId = nasid,
                planId = (int) planId,
                mobile = mobile,
                planStartTime = startTime,
                planEndTime = stopTime,
                otp = HOMEOTP.ROAM,
                transactionId = transactionId
            };

            CoreDbCalls.GetInstance().GenerateUser(pUser, nasid, 1, true, 0, planId, transactionId);
        }

        public static string Shorten(string url, DateTime ttl, string host = null)
        {
            if (string.IsNullOrEmpty(host))
                host = I2e1ConfigurationManager.IS_PROD ?
                    (I2e1ConfigurationManager.DEPLOYED_ON == "stage" ? "stage.i2e1.in" : "wiom.in") :
                    ((string.IsNullOrEmpty(I2e1ConfigurationManager.DEPLOYED_ON) ? "dev" : I2e1ConfigurationManager.DEPLOYED_ON) + ".i2e1.in");

            byte[] crcPayload = Encoding.ASCII.GetBytes(url);
            uint crc = Crc32Algorithm.Compute(crcPayload);
            string id = crc.ToString();

            ModelDynamoDb<UrlShortner> modelDynamoDb = new ModelDynamoDb<UrlShortner>();

            UrlShortner shortUrl = new UrlShortner()
            {
                key = id,
                url = url,
                ttl = DateTimeToUnixEpoch(ttl)
            };
            modelDynamoDb.Insert(shortUrl);

            if (host == "localhost")
                host += ":44300";
            return $"{host}/u/{id}";
        }

        public static async Task<JToken> ExecuteLambdaFunction(string functionName, string region, Dictionary<string, object> parameters)
        {
            AmazonLambdaClient client = new AmazonLambdaClient("********************", "kcwyA+46uL53paYP6u0JlfTIXUY3wEbBEQQyCK2F", Amazon.RegionEndpoint.APSouth1);
            InvokeRequest ir = new InvokeRequest
            {
                FunctionName = "arn:aws:lambda:" + region + ":780604660019:function:" + functionName,
                InvocationType = InvocationType.RequestResponse,
                LogType = "Tail",
                Payload = JsonConvert.SerializeObject(parameters)
            };

            InvokeResponse response = await client.InvokeAsync(ir);
            var sr = new StreamReader(response.Payload);
            return JsonConvert.DeserializeObject<JToken>(sr.ReadToEnd());
        }

        public static int GetOverriddenDeviceLimit(LongIdInfo nasid, int deviceLimit)
        {
            if (deviceLimit == Constants.HOME_ROUTER_DEVICE_LIMIT)
                return 15;
            return deviceLimit;
        }

        public static string GetLoginServerUrl()
        {
            string baseUrl = "https://localhost:44300";
            if (I2e1ConfigurationManager.DEPLOYED_ON == "prod")
            {
                baseUrl = "https://www.i2e1.in";
            }
            else if (!String.IsNullOrEmpty(I2e1ConfigurationManager.DEPLOYED_ON))
            {
                baseUrl = $"https://{I2e1ConfigurationManager.DEPLOYED_ON}.i2e1.in";
            }
            return baseUrl;
        }
        
        public static string GetPaymentServerUrl()
        {
            string baseUrl = "http://localhost:5002";
            if (I2e1ConfigurationManager.DEPLOYED_ON == "prod")
            {
                baseUrl = "https://payment.i2e1.in";
            }
            else if (!String.IsNullOrEmpty(I2e1ConfigurationManager.DEPLOYED_ON))
            {
                baseUrl = $"https://payment.{I2e1ConfigurationManager.DEPLOYED_ON}.i2e1.in";
            }
            return baseUrl;
        }

        public static long DateTimeToUnixEpoch(DateTime dateTime)
        {
            DateTime epoch = new DateTime(1970, 1, 1, 0, 0, 0, DateTimeKind.Utc);
            return (long)(dateTime.ToUniversalTime() - epoch).TotalSeconds;
        }
    }
    
}
