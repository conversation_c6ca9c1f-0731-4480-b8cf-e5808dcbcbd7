using i2e1_core.Models;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Security.Cryptography;
using System.Text;
using System.Web;
using wifidog_core.Models;
using wiom_login_share.Models;
using ControllerType = wiom_login_share.Models.ControllerType;

namespace i2e1_core.Utilities
{
    public class LoginUtils
    {
        public static LoginResponse getRouterLoginUrl(User user, string chap, string landingPage)
        {
            LoginResponse response = new LoginResponse();
            switch (user.controllertype)
            {
                case ControllerType.I2E1:
                    response.isGet = true;
                    response.url = "http://" + user.uamip + ':' + user.uamport + "/logon";
                    response.parameters.Add(new NameValuePair("username", user.GetToken()));
                    response.parameters.Add(new NameValuePair("password", chap));
                    response.parameters.Add(new NameValuePair("userurl", landingPage));
                    return response;
                case ControllerType.ARUBA:
                    response.isGet = false;
                    response.url = "https://" + user.uamip + "/auth/index.html/u";
                    response.parameters.Add(new NameValuePair("cmd", "authenticate"));
                    response.parameters.Add(new NameValuePair("user", user.GetToken()));
                    response.parameters.Add(new NameValuePair("password", user.otp));
                    response.parameters.Add(new NameValuePair("mac", user.mac));
                    response.parameters.Add(new NameValuePair("url", landingPage));
                    return response;
                case ControllerType.RUCKUS:
                    response.isGet = false;
                    response.url = "http://" + user.uamip + ":9997/login";
                    response.parameters.Add(new NameValuePair("username", user.GetToken()));
                    response.parameters.Add(new NameValuePair("password", "1234"));
                    response.parameters.Add(new NameValuePair("ip", user.clientip));
                    response.parameters.Add(new NameValuePair("url", landingPage));
                    return response;
                case ControllerType.MICROTIK:
                    response.isGet = false;
                    response.url = user.uamip;
                    response.parameters.Add(new NameValuePair("username", user.GetToken()));
                    response.parameters.Add(new NameValuePair("password", user.otp));
                    response.parameters.Add(new NameValuePair("dst", landingPage));
                    return response;
                case ControllerType.WIFIDOG:
                    response.isGet = true;
                    response.url = "http://" + user.uamip + ':' + user.uamport + "/wifidog/auth";
                    response.parameters.Add(new NameValuePair("token", user.GetToken()));
                    response.parameters.Add(new NameValuePair("url", landingPage));
                    return response;
                case ControllerType.HFCL:
                    response.isGet = true;
                    response.url = "http://" + user.uamip + ':' + user.uamport + "/logon";
                    response.parameters.Add(new NameValuePair("username", user.GetToken()));
                    response.parameters.Add(new NameValuePair("token", user.GetToken()));
                    response.parameters.Add(new NameValuePair("url", landingPage));
                    return response;
                case ControllerType.CAMBIUM:
                    response.isGet = true;
                    response.url = "http://" + user.uamip + ':' + user.uamport + "/cgi-bin/hotspot_login.cgi" + user.attributes["query"];
                    response.parameters.Add(new NameValuePair("ga_user", user.GetToken()));
                    response.parameters.Add(new NameValuePair("ga_pass", "1234"));
                    return response;
            }
            return null;
        }

        public static string ConvertLoginResponseToUrl(LoginResponse loginResponse)
        {
            StringBuilder url = new StringBuilder(loginResponse.url);
            url.Append("?");
            foreach (var pair in loginResponse.parameters)
            {
                url.Append(pair.name).Append('=').Append(HttpUtility.UrlEncode(pair.value)).Append('&');
            }
            return url.ToString();
        }

        public static string Base64Encode(string plainText)
        {
            var plainTextBytes = System.Text.Encoding.UTF8.GetBytes(plainText);
            return System.Convert.ToBase64String(plainTextBytes);
        }

        public static string createCookieToken(string mobile)
        {
            string token = "";
            string encodedData = Base64Encode(mobile);
            using (var hash = SHA256.Create())
            {
                Encoding enc = Encoding.UTF8;
                token = String.Concat(hash.ComputeHash(Encoding.UTF8.GetBytes(encodedData + encodedData.Substring(5, encodedData.Length - 5) + encodedData.Substring(0, 5))).Select(item => item.ToString("x2")));
            }
            return token;
        }

        public static Dictionary<string, object> getRouterLoginUrlDict(User user, string chap, string landingPage)
        {
            var data = new Dictionary<string, object>();
            data.Add("landingPage", getRouterLoginUrl(user, chap, landingPage));
            data.Add("chap", chap);
            return data;
        }

        public static string generateCHAP(string challenge, string otp)
        {
            if (string.IsNullOrEmpty(challenge))
                return string.Empty;

            string hexChal = pack(challenge);
            string uamSecret = "spartans";
            string s = CreateMD5(hexChal + uamSecret);
            string newChal = pack(s);
            string papp = unpack(calcXor(otp, newChal));
            return papp;
        }

        private static string CreateMD5(string input)
        {
            string testString = input;
            byte[] asciiBytes = new byte[testString.Length];
            for (int i = 0; i < testString.Length; ++i)
            {
                asciiBytes[i] = (byte)testString[i];
            }
            byte[] hashedBytes = MD5CryptoServiceProvider.Create().ComputeHash(asciiBytes);
            return BitConverter.ToString(hashedBytes).Replace("-", "").ToLower();
        }

        private static string calcXor(string a, string b)
        {
            char[] charAArray = a.ToCharArray();
            char[] charBArray = b.ToCharArray();
            int len = 0;

            char[] larger = charAArray;

            if (a.Length > b.Length)
                len = b.Length - 1;
            else
            {
                len = a.Length - 1;
                larger = charBArray;
            }

            for (int i = 0; i <= len; i++)
            {
                larger[i] = (char)(charAArray[i] ^ charBArray[i]); //error here
            }

            return new string(larger);
        }

        private static string pack(string str)
        {
            StringBuilder sb = new StringBuilder();
            for (int i = 0; i < str.Length;)
            {
                int code = getIntFromHex(str[i]) * 16 + getIntFromHex(str[i + 1]);
                sb.Append((char)code);
                i += 2;
            }
            return sb.ToString();
        }

        private static int getIntFromHex(char code)
        {
            if (code >= '0' && code <= '9')
                return code - '0';
            else
                return code - 'a' + 10;
        }

        private static string unpack(string str)
        {
            StringBuilder sb = new StringBuilder();
            for (int i = 0; i < str.Length;)
            {
                sb.Append(getHexFromInt(str[i] / 16));
                sb.Append(getHexFromInt(str[i] % 16));
                i++;
            }
            return sb.ToString();
        }

        private static char getHexFromInt(int code)
        {
            if (code < 10)
                return (char)('0' + code);
            else
                return (char)('a' + code - 10);
        }
    }
}
