using System;
using System.Collections;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using i2e1_basics.Database;
using i2e1_basics.DynamoUtilities;
using i2e1_basics.Models;
using i2e1_basics.Utilities;
using i2e1_core.Models.Entity;
using Newtonsoft.Json.Linq;
using wifidog_core.Models;
using wifidog_core.Models.RouterPlan;
using static i2e1_basics.DynamoUtilities.DynamoEnum;

namespace i2e1_core.Utilities
{
    public class RadAcctDbCalls
    {
        //kindly dont change this method or inform app team before doing so , 
        //so as to not break the existing admin app
        public static List<UserDataUsage> GetDataUsage(bool showNumber, LongIdInfo nasid, DateTime start, DateTime end)
        {
            List<UserDataUsage> dataUsage = new List<UserDataUsage>();
            CoreBigQuery.FetchNasUsageFromDB(@"SELECT
    mobile,
    SUM(cast(inputoctets as bigint)) AS AcctInputOctets,
    SUM(cast(outputoctets as bigint)) AS AcctOutputOctets,
    COUNT(DISTINCT substring(sessionstarttime, 1, 10)) AS days,
    substring(MAX(sessionstarttime), 1, 10) as date,
    MAX(sessionendtime) AS stop
FROM
radacct_logs WHERE 
nasid = '" + nasid + "'", "GROUP BY  mobile ORDER BY stop DESC,mobile ASC", start, end, (reader)=>
            {
                if (reader["AcctInputOctets"] != null && reader["AcctOutputOctets"] != null)
                {
                    var mobile = reader["mobile"].ToString();

                    UserDataUsage userData = new UserDataUsage()
                    {
                        dataUpload = Convert.ToInt64(reader["AcctOutputOctets"]),
                        dataDownload = Convert.ToInt64(reader["AcctInputOctets"]),
                        days = Convert.ToInt32(reader["days"]),
                        sessionEnd = DateTime.ParseExact(reader["date"].ToString(), "MM/dd/yyyy", CultureInfo.InvariantCulture),
                        nasid = nasid
                    };
                    userData.SetMobile(showNumber, mobile);
                    dataUsage.Add(userData);
                }
            });

            return dataUsage;

            
        }

        //TODO : Change for sharding
        public static List<UserDataUsage> GetDetailedDataReport(bool showNumber, List<LongIdInfo> nasids, string mobile, DateTime start, DateTime end)
        {
            List<UserDataUsage> dataUsage = new List<UserDataUsage>();
            string query = null;
            if (string.IsNullOrEmpty(mobile))
            {
                query = @"SELECT
    mobile,
    nasid,
    inputoctets,
    outputoctets,
    sessionstarttime,
    sessionendtime,
    CallingStationId,
    routerNasId,
    plan_id,
    otp
FROM
radacct_logs WHERE 
nasid in (" + string.Join(',', nasids.Select(m => "'" + m + "'")) + @")";
            }
            else
            {

                query = @"SELECT
    mobile,
    nasid,
    inputoctets,
    outputoctets,
    sessionstarttime,
    sessionendtime,
    CallingStationId,
    routerNasId,
    plan_id,
    otp
FROM
radacct_logs WHERE 
nasid in (" + string.Join(',', nasids.Select(m => "'" + m + "'")) + @") and UserName = '" + mobile + "'"; 
            }

            CoreBigQuery.FetchNasUsageFromDB(query, string.Empty, start, end,
                (reader) =>
                {
                    if (reader["inputoctets"] != null && reader["outputoctets"] != null)
                    {
                        UserDataUsage userData = new UserDataUsage();
                        userData.SetMobile(showNumber, reader["mobile"].ToString());
                        userData.dataUpload = (long)reader["inputoctets"];
                        userData.dataDownload = (long)reader["outputoctets"];
                        userData.sessionStart = ((DateTime)reader["sessionstarttime"]).Add(new TimeSpan(5, 30, 0));
                        userData.sessionEnd = ((DateTime)reader["sessionendtime"]).Add(new TimeSpan(5, 30, 0));
                        userData.macId = reader["CallingStationId"].ToString();
                        userData.nasid = LongIdInfo.IdParser(Convert.ToInt64(reader["nasid"]));
                        userData.fdmId = (long)reader["plan_id"];
                        userData.otp = reader["otp"] == null ? string.Empty : reader["otp"].ToString();
                        dataUsage.Add(userData);
                    }
                }
                );

            return dataUsage;
        }

        public static JsonResponse GetUsersInThisDuration(LongIdInfo nasid, DateTime startDate, DateTime endDate)
        {
            RadacctDb radacctDb = new RadacctDb(nasid);
            HashSet<string> users = new HashSet<string>();
            radacctDb.ExecuteAutoPartition("SELECT DISTINCT UserName AS username FROM {{table_name}} WHERE RouterNasId = @nasid AND AcctStartTime >= @start_date AND AcctStartTime < @end_date;",
                new MySqlExecuteAllResponseHandler((reader, shardId) =>
                {
                    while (reader.Read())
                    {
                        users.Add(reader["username"].ToString());
                    }
                }), string.Empty,
                new { nasid, start_date = startDate, end_date = endDate }, startDate, endDate);

            Hashtable data = new Hashtable();
            data.Add(nasid.ToString(), users.Count.ToString());
            return new JsonResponse(ResponseStatus.SUCCESS, "", users);
        }

        public static List<UserDataUsage> GetOnlineUsers(bool showNumber, LongIdInfo nasid)
        {
            List<UserDataUsage> list = new List<UserDataUsage>();
            CoreBigQuery.FetchNasUsageFromDB(@"SELECT UserName, AcctSessionId FROM
radacct_logs WHERE
routernasid = '" + nasid + "' and status != 2", string.Empty, DateTime.UtcNow.AddMinutes(-5), DateTime.UtcNow,
    (reader) =>
    {
        var dataUsage = new UserDataUsage();
        dataUsage.SetMobile(showNumber, reader["UserName"].ToString());
        list.Add(dataUsage);
    }
);
            return list;
        }

        public static Dictionary<long, string> GetDataUsedInInterval(int interval, List<LongIdInfo> nasids, bool intervalIsMinutes = false)
        {
            Dictionary<long, string> dict = new Dictionary<long, string>();
            Dictionary<long, double> temp = new Dictionary<long, double>();
            var time = DateTime.UtcNow;
            if (intervalIsMinutes)
                time = time.AddMinutes(-interval);
            else
                time = time.AddDays(-interval);
            foreach (LongIdInfo nas in nasids)
            {
                temp[nas.GetLongId()] = 0;
            }


            CoreBigQuery.FetchNasUsageFromDB(@"SELECT
    RouterNasId AS routernasid,
    SUM(cast(inputoctets as bigint)) + SUM(cast(outputoctets as bigint)) AS data_used,
    SUM(cast(inputoctets as bigint)) AS download,
    SUM(cast(outputoctets as bigint)) AS upload
FROM radacct_logs WHERE
routernasid IN (" + string.Join(',', nasids) + ")", "GROUP BY routernasid", time,DateTime.UtcNow,
    (reader) =>
    {
        long longNas = Convert.ToInt64(reader["routernasid"]);
        long dataUsed = (reader["data_used"] == DBNull.Value ? 0 : (long)reader["data_used"]);
        temp[longNas] = temp[longNas] + (((double)dataUsed) / (1024 * 1024));
    }
);

            foreach (LongIdInfo nas in nasids)
            {
                dict[nas.GetLongId()] = Convert.ToString(temp[nas.GetLongId()]);
            }
            return dict;
        }

        public static Dictionary<long, JObject> GetUsersInDuration(List<LongIdInfo> nasids, DateTime startTime)
        {
            var dict = new Dictionary<long, JObject>();
            foreach (var nas in nasids)
            {
                ModelDynamoDb<HomeRouterPlan> modelDynamoDb = new ModelDynamoDb<HomeRouterPlan>();
                DynamoQueryBuilder<HomeRouterPlan> dynamoQueryBuilder = new DynamoQueryBuilder<HomeRouterPlan>();
                dynamoQueryBuilder.AddkeyConditionExpression(p => p.nasId, DynamoComparisonOperator.Equal, nas)
                                  .AddFilterCondition(p => p.planEndTime, DynamoComparisonOperator.GreaterThanOrEqual, startTime);

                List<HomeRouterPlan> allHomeRouter = modelDynamoDb.GetRecord(dynamoQueryBuilder);

                dict[nas.GetLongId()]= new JObject(){
                    { "userCount", allHomeRouter.Select(m => m.mobile).Distinct().Count() },
                    { "dataUsed",  0 },
                    { "lastUsed", null }
                };
            }

            CoreBigQuery.FetchNasUsageFromDB(@"SELECT nasid, 
            SUM(cast(inputoctets as bigint)) + SUM(cast(outputoctets as bigint)) AS data_used,  
            COUNT(DISTINCT mobile) AS count,
            MAX(sessionstarttime) AS last_used
            FROM
            radacct_logs WHERE
            nasid IN (" + string.Join(',', nasids.Select(m => "'" + m + "'")) + ")", "GROUP BY nasid", startTime, DateTime.UtcNow,
                (reader) =>
                {
                    long longNas = Convert.ToInt64(reader["nasid"]);
                    double dataUsed = (reader["data_used"] == DBNull.Value ? 0 : Convert.ToInt64(reader["data_used"]));
                    dict[longNas]["dataUsed"] = dataUsed / (1024 * 1024);
                    dict[longNas]["lastUsed"] = (DateTime.UtcNow -
                            DateTime.ParseExact(reader["last_used"].ToString(), "MM/dd/yyyy HH:mm:ss", CultureInfo.InvariantCulture)).TotalMinutes.ToString();
                }
            );
            return dict;
        }

        public static object GetDataUsedForUser(string username, DateTime from, DateTime to)
        {
            string query = @"
                     SELECT
                     SUM(data_used) AS data_used_gb
                     FROM (
                        SELECT *, ((inputoctets + outputcctets) / (1024 * 1024 * 1024)) AS data_used
                        FROM radacct_logs
                        WHERE username = '" + username + @"' ";
            query += "AND store_group_id = 1 ";
            query += @");";

            double dataUsed = 0;
            //CoreBigQuery.FetchNasUsageFromDB(query, from, to, (reader) =>
            //{
            //    dataUsed = Convert.ToDouble(reader["data_used_gb"]);
            //}); ;

            var data = new
            {
                dataUsed = dataUsed
            };
            return data;
        }
    }
}
