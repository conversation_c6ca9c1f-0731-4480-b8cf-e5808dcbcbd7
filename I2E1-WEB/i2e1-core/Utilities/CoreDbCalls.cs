using i2e1_basics.Cache;
using i2e1_basics.Database;
using i2e1_basics.DynamoUtilities;
using i2e1_basics.Utilities;
using i2e1_core.Models;
using i2e1_core.Models.Client;
using i2e1_core.Models.Payment;
using i2e1_core.TabsHelper;
using MySql.Data.MySqlClient;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Threading;
using System.Threading.Tasks;
using wifidog_core.Models;
using wifidog_core.Models.Entity;
using wifidog_core.Models.WIOM;
using wifidog_core.Utilities;
using wiom_login_share.Models;
using wiom_router_api.Models;
using wiom_routerplan_share.ExposeApi;
using wiom_routerplan_share.Models.RouterPlan;
using static i2e1_basics.DynamoUtilities.DynamoEnum;

namespace i2e1_core.Utilities
{
	public class CoreDbCalls
    {
        private static CoreDbCalls dbCalls = null;

        private CoreDbCalls()
        {
        }

        public static CoreDbCalls CreateInstance()
        {
            if (dbCalls == null)
            {
                dbCalls = new CoreDbCalls();
            }
            return dbCalls;
        }

        public static CoreDbCalls GetInstance()
        {
            return dbCalls;
        }

        public async Task<bool> storeRouterDowner(long routerNasid, DateTime lastPingTime, float txPower = 0, float rxPower = 0)
        {
            try
            {
                if (routerNasid == 0) return false;
                var rowToInsert = new JObject
                {
                    { "nasid", routerNasid },
                    {"added_time" , lastPingTime.ToString("yyyy-MM-dd HH:mm:ss")},
                    { "tx_power", txPower},
                    { "rx_power", rxPower }
                };
                
                var response = await S3BasicUtils.GetInstance().CreateJsonFile(rowToInsert, $"t_ont_monitoring/{DateTime.UtcNow.ConvertToUnixEpochMili()}/{routerNasid}", "", "wiom-prod");
                Logger.GetInstance().Info(message: $"Inserted row in table successfully from storeRouterDowner API");
                return true;
            }
            catch (Exception ex)
            {
                Logger.GetInstance().Error($"Error in inserting row in t_ont_monotoring table for router_nasid: {routerNasid} and error msg: {ex.Message}");
                return false;
            }
        }

        public List<BasicConfig> GetUserBasicConfigsInCombinedSetting(long shardId, int combinedSettingId, int userGrourId)
        {
            if (combinedSettingId == 0)
            {
                List<BasicConfig> basicConfigs = new List<BasicConfig>();
                foreach (BasicConfigType basicConfigType in Enum.GetValues(typeof(BasicConfigType)))
                {
                    basicConfigs.Add(new BasicConfig(basicConfigType));
                }
                return basicConfigs;
            }

            return new ShardQueryExecutor<List<BasicConfig>>(new GetSqlCommand((out ResponseType res) =>
            {
                SqlCommand cmd = new SqlCommand(@"select config_id, parameters from t_user_basic_configuration where combined_setting_id = @combinedSettingId and user_group_id = @userGrourId 
                                                and version_no = (select max(version_no) from t_user_basic_configuration where combined_setting_id = @combinedSettingId and user_group_id = @userGrourId)");
                cmd.Parameters.Add(new SqlParameter("@combinedSettingId", combinedSettingId));
                cmd.Parameters.Add(new SqlParameter("@userGrourId", userGrourId));
                res = ResponseType.READER;
                return cmd;
            }), shardId,
            new ResponseHandler<List<BasicConfig>>((reader) =>
            {
                List<BasicConfig> basicConfigs = new List<BasicConfig>();
                foreach (BasicConfigType configType in Enum.GetValues(typeof(BasicConfigType)))
                {
                    basicConfigs.Add(new BasicConfig(configType));
                }

                while (reader.Read())
                {
                    BasicConfigType configType = (BasicConfigType)reader["config_id"];
                    BasicConfig config = basicConfigs.Find(s => s.configType == configType);
                    if (config == null)
                        basicConfigs.Add(new BasicConfig(configType, reader["parameters"].ToString()));
                    else
                        config.value = reader["parameters"].ToString();
                }
                return basicConfigs;
            })).Execute();
        }

        public Dictionary<string, string> GetListChecks(LongIdInfo longNas, ListCheckDataType listType)
        {
            return new ShardQueryExecutor<Dictionary<string, string>>(new GetSqlCommand((out ResponseType res) =>
            {
                SqlCommand cmd = new SqlCommand("select * from t_single_nas_operations where single_operation_id = @listType and nas_id = @nasid");
                cmd.Parameters.Add(new SqlParameter("@nasid", longNas.local_value));
                cmd.Parameters.Add(new SqlParameter("@listType", (int)listType));
                res = ResponseType.READER;
                return cmd;
            }), longNas.shard_id,
            new ResponseHandler<Dictionary<string, string>>((reader) =>
            {
                Dictionary<string, string> data = new Dictionary<string, string>();
                if (reader.Read())
                {
                    var str = reader["parameters"].ToString();
                    if (!string.IsNullOrEmpty(str))
                    {
                        var list = str.Split(new string[] { "," }, StringSplitOptions.RemoveEmptyEntries);
                        foreach (string s in list)
                        {
                            if (s.Contains(";"))
                            {
                                string key = s.Substring(0, s.IndexOf(';'));
                                string value = s.Substring(s.IndexOf(';') + 1);
                                data[key.Trim().Replace('-', ':').ToUpper()] = value;
                            }
                            else
                            {
                                data[s.Trim().Replace('-', ':').ToUpper()] = "";
                            }
                        }
                    }
                }

                return data;
            })).Execute();
        }

        public string GetParameterForNasByType(LongIdInfo nas, ListCheckDataType listType)
        {
            return new ShardQueryExecutor<string>(new GetSqlCommand((out ResponseType res) =>
            {
                SqlCommand cmd = new SqlCommand("select * from t_single_nas_operations where single_operation_id = @listType and nas_id = @nasid");
                cmd.Parameters.Add(new SqlParameter("@nasid", nas.ToSafeDbObject()));
                cmd.Parameters.Add(new SqlParameter("@listType", (int)listType));
                res = ResponseType.READER;
                return cmd;
            }), nas.shard_id,
            new ResponseHandler<string>((reader) =>
            {
                if (reader.Read())
                {
                    var str = reader["parameters"].ToString();
                    return str;
                }

                return string.Empty;
            })).Execute();
        }

        public KeyValuePair<int, bool> GetStoreGroupAndIsAccessCodeApplied(LongIdInfo longNasid)
        {
            return new ShardQueryExecutor<KeyValuePair<int, bool>>(new GetSqlCommand((out ResponseType res) =>
            {
                SqlCommand cmd = new SqlCommand("select single_operation_id, parameters from t_single_nas_operations where single_operation_id in (16, 17) and nas_id=@nasid");
                cmd.Parameters.Add(new SqlParameter("@nasid", longNasid.local_value));
                res = ResponseType.READER;
                return cmd;
            }), longNasid.shard_id,
            new ResponseHandler<KeyValuePair<int, bool>>((reader) =>
            {
                int storeGroup = 0;
                bool askAccessCode = false;
                while (reader.Read())
                {
                    int operationId = (int)reader["single_operation_id"];
                    string result = reader["parameters"].ToString();
                    if (!String.IsNullOrEmpty(result))
                    {
                        if (operationId == 16)
                        {
                            String[] parameters = result.Split(new String[] { "," }, StringSplitOptions.None);
                            if (parameters.Length >= 9 && parameters[8] == "True") askAccessCode = true;
                        }
                        else if (operationId == 17)
                        {
                            storeGroup = int.Parse(result);
                        }
                    }
                }
                return new KeyValuePair<int, bool>(storeGroup, askAccessCode);
            })).Execute();
        }

        public Dictionary<string, string> getStoreDetailsByNasid(LongIdInfo longNasId)
        {
            return new ShardQueryExecutor<Dictionary<string, string>>(new GetSqlCommand((out ResponseType res) =>
            {
                SqlCommand cmd = new SqlCommand(@"SELECT ts.shop_name, ts.shop_city, ts.latitude, ts.longitude, ts.shop_state, ts.brand_name as brand, tmp.market_place_name as market, ts.market_place_id as market_place_id, pincode, category, tmp.parent_place_name
                    FROM t_store ts LEFT JOIN t_market_place tmp ON tmp.market_place_id = ts.market_place_id where ts.router_nas_id = @nasid");
                cmd.Parameters.Add(new SqlParameter("@nasid", longNasId.local_value));
                res = ResponseType.READER;
                return cmd;
            }), longNasId.shard_id,
            new ResponseHandler<Dictionary<string, string>>((reader) =>
            {

                if (reader.Read())
                {
                    var storeDetails = new Dictionary<string, string>();
                    storeDetails.Add("shop_name", reader["shop_name"].ToString());
                    storeDetails.Add("shop_city", reader["shop_city"].ToString());
                    storeDetails.Add("shop_state", reader["shop_state"].ToString());
                    storeDetails.Add("brand", reader["brand"].ToString());
                    storeDetails.Add("market", reader["market"].ToString());
                    storeDetails.Add("pincode", reader["pincode"].ToString());
                    storeDetails.Add("category", reader["category"].ToString());
                    storeDetails.Add("market_place_id", reader["market_place_id"].ToString());
                    storeDetails.Add("parent_place_name", reader["parent_place_name"].ToString());
                    storeDetails.Add("latitude", reader["latitude"].ToString());
                    storeDetails.Add("longitude", reader["longitude"].ToString());
                    return storeDetails;
                }
                return null;
            })).Execute();
        }

        public string GetSingleNasOperation(LongIdInfo longNasId, int singleNasOperationId)
        {
            return new ShardQueryExecutor<string>(new GetSqlCommand((out ResponseType res) =>
            {
                SqlCommand cmd = new SqlCommand("select parameters from t_single_nas_operations where single_operation_id = @operation_id and nas_id = @nas");

                cmd.Parameters.Add(new SqlParameter("@operation_id", singleNasOperationId));
                cmd.Parameters.Add(new SqlParameter("@nas", longNasId.local_value));
                res = ResponseType.READER;
                return cmd;
            }), longNasId.shard_id,
            new ResponseHandler<string>((reader) =>
            {
                string result = "";
                if (reader.Read())
                {
                    result = reader["parameters"].ToString();
                }
                return result;

            })).Execute();
        }

        public int GetCombinedSettingIdOnNas(LongIdInfo longNasid)
        {
            return new ShardQueryExecutor<int>(new GetSqlCommand((out ResponseType res) =>
            {
                SqlCommand cmd = new SqlCommand("select combined_setting_id from t_combined_setting_nas_mapping where nas_id = @nasid");
                cmd.Parameters.Add(new SqlParameter("@nasid", longNasid.local_value));
                res = ResponseType.READER;
                return cmd;
            }), longNasid.shard_id,
            new ResponseHandler<int>((reader) =>
            {
                if (reader.Read())
                {
                    return (int)reader["combined_setting_id"];
                }
                return 0;
            })).Execute();
        }

        //TODO: check once
        public List<LongIdInfo> GetNassesForSetting(int settingId)
        {
            List<LongIdInfo> nases = new List<LongIdInfo>();
            new ShardQueryExecutor<List<LongIdInfo>>(new GetSqlCommand((out ResponseType res) =>
            {
                SqlCommand cmd = new SqlCommand("select nas_id from t_combined_setting_nas_mapping where combined_setting_id = @combined_setting_id");
                cmd.Parameters.Add(new SqlParameter("@combined_setting_id", settingId));
                res = ResponseType.READER;
                return cmd;
            }),
            new ExecuteAllResponseHandler((reader, shardId) =>
            {
                while (reader.Read())
                {
                    nases.Add(new LongIdInfo(shardId, DBObjectType.ACTIVE_NAS, Convert.ToInt64(reader["nas_id"])));
                }
            })).ExecuteAll();
            return nases;
        }

        public bool UpdateAdvanceConfigs(int combinedSettingId, Dictionary<string, AdvanceConfig> advanceConfigs, LongIdInfo longAdminId)
        {
            return new ShardQueryExecutor<bool>(new GetSqlCommand((out ResponseType res) =>
            {
                SqlCommand cmd = new SqlCommand(@"
                    DECLARE @old_value VARCHAR(MAX)
                    DECLARE @new_value VARCHAR(MAX)

                    SET @old_value = (SELECT id, config_id, parameters
                    FROM t_advance_configuration WHERE combined_setting_id = @setting_id
                    FOR JSON AUTO)

                    MERGE t_advance_configuration AS T
	                USING @configs AS S
	                ON (T.combined_setting_id = @setting_id AND T.config_id = S.int_value)
                    WHEN MATCHED
                        THEN UPDATE
                        SET
                        T.parameters = S.varchar_value
	                WHEN NOT MATCHED BY TARGET
		                THEN INSERT(combined_setting_id, config_id, parameters)
		                VALUES(@setting_id, S.int_value, S.varchar_value); 

                    SET @new_value = (SELECT id, config_id, parameters
                    FROM t_advance_configuration WHERE combined_setting_id = @setting_id
                    FOR JSON AUTO)

                    INSERT INTO t_setting_audit(table_name, combined_setting_id, old_value, new_value, modified_time, updated_by)
                    VALUES('t_advance_configuration', @setting_id, @old_value, @new_value, GETUTCDATE(), @admin_user_id)
                    SELECT 0 AS status");

                DataTable configTable = new DataTable();
                configTable.Columns.Add("varchar_value", typeof(string));
                configTable.Columns.Add("int_value", typeof(int));
                foreach (KeyValuePair<String, AdvanceConfig> pair in advanceConfigs)
                {
                    AdvanceConfig config = pair.Value;
                    configTable.Rows.Add(config.GetJoinedParameters(), (int)config.configType);
                }
                var p = new SqlParameter("@configs", configTable);
                p.TypeName = "dbo.type_varchar_int_v2";
                cmd.Parameters.Add(p);
                cmd.Parameters.Add(new SqlParameter("@setting_id", combinedSettingId));
                cmd.Parameters.Add(new SqlParameter("@admin_user_id", longAdminId.local_value));
                res = ResponseType.READER;
                return cmd;
            }), longAdminId.shard_id,
            new ResponseHandler<bool>((reader) =>
            {
                if (reader.Read())
                {
                    return (0 == (int)reader["status"]);
                }
                return false;
            })).Execute();
        }

        public int GetNoOfDeviesPerUser(int combinedSettingId)
        {
            int count = GetAdvanceConfigIntValue(combinedSettingId, AdvanceConfigType.NO_OF_DEVICES_PER_USER);
            return count <= 0 ? 5 : count;
        }
        public int GetAdvanceConfigIntValue(int combinedSettingId, AdvanceConfigType configType)
        {
            AdvanceConfig config = GetAdvanceConfigInCombinedSetting(combinedSettingId, configType);
            if (config != null && config.parameters != null && config.parameters.Length > 0)
            {
                if (!string.IsNullOrEmpty(config.parameters[0]))
                    return int.Parse(config.parameters[0]);
            }
            return 0;
        }

        public AdvanceConfig GetAdvanceConfigInCombinedSetting(int combinedSettingId, AdvanceConfigType configType)
        {
            return new ShardQueryExecutor<AdvanceConfig>(new GetSqlCommand((out ResponseType res) =>
            {
                SqlCommand cmd = new SqlCommand(@"select parameters from t_advance_configuration where combined_setting_id = @combinedSettingId and config_id = @configId");
                cmd.Parameters.Add(new SqlParameter("@combinedSettingId", combinedSettingId));
                cmd.Parameters.Add(new SqlParameter("@configId", configType));
                res = ResponseType.READER;
                return cmd;
            }), ShardHelper.SHARD0,
            new ResponseHandler<AdvanceConfig>((reader) =>
            {
                if (reader.Read())
                {
                    return new AdvanceConfig(configType, reader["parameters"].ToString());
                }
                return new AdvanceConfig(configType);
            })).Execute();
        }

        public List<UserGroupNew> GetUserGroupInformation(LongIdInfo nasid)
        {
            return new ShardQueryExecutor<List<UserGroupNew>>(new GetSqlCommand((out ResponseType res) =>
            {
                string query = "select user_group_id, user_group_name, parameters from t_user_group_new where user_group_id in (select distinct(ubc.user_group_id) from t_combined_setting_nas_mapping cn, t_user_basic_configuration ubc where cn.combined_setting_id = ubc.combined_setting_id and cn.nas_id = @nasid)";
                SqlCommand cmd = new SqlCommand(query);
                cmd.Parameters.Add(new SqlParameter("@nasid", nasid.ToSafeDbObject()));
                res = ResponseType.READER;
                return cmd;
            }), nasid.shard_id,
            new ResponseHandler<List<UserGroupNew>>((reader) =>
            {
                List<UserGroupNew> list = new List<UserGroupNew>();

                UserGroupNew global = new UserGroupNew(0, "Global", String.Empty, null);

                list.Add(global);

                UserGroupNew fdm = new UserGroupNew(-1, "Access Code", String.Empty, null);

                list.Add(fdm);

                while (reader.Read())
                {
                    list.Add(new UserGroupNew((int)reader["user_group_id"], reader["user_group_name"].ToString(), reader["parameters"].ToString(), null));
                }

                return list;
            })).Execute();
        }

        public BasicConfig GetUserBasicConfigInCombinedSetting(long shardId, int combinedSettingId, BasicConfigType configType)
        {
            long userGroupId = 0;
            return new ShardQueryExecutor<BasicConfig>(new GetSqlCommand((out ResponseType res) =>
            {
                SqlCommand cmd = new SqlCommand(@"select config_id, parameters from t_user_basic_configuration where combined_setting_id = @combinedSettingId 
                                                and user_group_id = @userGroupId and config_id = @configId and version_no = (select max(version_no) from 
                                                t_user_basic_configuration where combined_setting_id = @combinedSettingId and user_group_id = @userGroupId)");
                cmd.Parameters.Add(new SqlParameter("@combinedSettingId", combinedSettingId));
                cmd.Parameters.Add(new SqlParameter("@userGroupId", userGroupId));
                cmd.Parameters.Add(new SqlParameter("@configId", configType));
                res = ResponseType.READER;
                return cmd;
            }), shardId,
            new ResponseHandler<BasicConfig>((reader) =>
            {
                if (reader.Read())
                {
                    return new BasicConfig(configType, reader["parameters"].ToString());
                }
                return new BasicConfig(configType);
            })).Execute();
        }

        public string UpdateRouterPing(RemoteManagement remote, int wifiCount, string process)
        {
            long shardId = LongIdInfo.IdParser(Convert.ToInt64(remote.nasid)).shard_id;
            //string[] processlist = process.Split(':');
            return new ShardQueryExecutor<string>(new GetSqlCommand((out ResponseType res) =>
            {
                SqlCommand cmd = new SqlCommand("update_router_ping");
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.Parameters.Add(new SqlParameter("@nasid", remote.backEndNasid.ToSafeDbObject()));
                cmd.Parameters.Add(new SqlParameter("@secondarynasid", remote.secondaryNas.ToSafeDbObject()));
                cmd.Parameters.Add(new SqlParameter("@controller_id", 1));
                cmd.Parameters.Add(new SqlParameter("@version", int.Parse(remote.version)));
                cmd.Parameters.Add(new SqlParameter("@wifi_count", wifiCount));
                cmd.Parameters.Add(new SqlParameter("@process", process));
                res = ResponseType.READER;
                return cmd;
            }), shardId,
            new ResponseHandler<string>((reader) =>
            {
                if (reader.Read())
                {
                    remote.operationType = (OperationType)(byte)reader["operation_type"];
                    remote.operationParameter = reader["operation_parameter"].ToString();
                    remote.operationid = reader["operation_id"].ToString();
                    return reader["operation_text"].ToString();
                }
                return string.Empty;
            })).Execute();
        }

        public bool updateControllerDetails(DeviceConfig config)
        {
            return new ShardQueryExecutor<bool>(new GetSqlCommand((out ResponseType res) =>
            {
                SqlCommand cmd = new SqlCommand(@"update t_controller set ssid = COALESCE(@ssid, ssid), device_mac = COALESCE(@lan_mac, device_mac),
                    router_wifi_mac = COALESCE(@wifi_mac, router_wifi_mac), router_lan_mac= COALESCE(@lan_mac, router_lan_mac), modified_time = getutcdate() 
                    where router_nas_id = @nasid");
                cmd.Parameters.Add(new SqlParameter("@lan_mac", CoreUtil.ToSafeDbObject(config.macid)));
                cmd.Parameters.Add(new SqlParameter("@wifi_mac", CoreUtil.ToSafeDbObject(config.macid2)));
                cmd.Parameters.Add(new SqlParameter("@nasid", config.nasid.ToSafeDbObject()));
                cmd.Parameters.Add(new SqlParameter("@ssid", CoreUtil.ToSafeDbObject(config.ssid)));
                res = ResponseType.NONQUERY;
                return cmd;
            }), config.nasid.shard_id,
            new ResponseHandler<bool>((reader) =>
            {
                return true;
            })).Execute();
        }

        public string updateControllerDetailsv2(DeviceConfig config)
        {
            string configs = string.Empty;
            if (!string.IsNullOrEmpty(config.firmwareVersion) && config.firmwareVersion.Length > 100)
                config.firmwareVersion = config.firmwareVersion.Substring(0, 100);
            if (!string.IsNullOrEmpty(config.deviceType) && config.deviceType.Length > 100)
                config.deviceType = config.deviceType.Substring(0, 100);

            return new ShardQueryExecutor<string>(new GetSqlCommand((out ResponseType res) =>
            {
                SqlCommand cmd = new SqlCommand(@"update t_controller set ssiddata = COALESCE(@ssiddata, ssiddata), device_mac = COALESCE(@lan_mac, device_mac),
                    router_wifi_mac = COALESCE(@wifi_mac, router_wifi_mac), router_lan_mac= COALESCE(@lan_mac, router_lan_mac), modified_time = getutcdate() 
                    , firmwareVersion= COALESCE(@firmwareVersion,firmwareVersion), device_type= COALESCE(@deviceType,device_type),gatewayInfo= COALESCE(@gatewayInfo,gatewayInfo)
                    output inserted.configs
                    where router_nas_id = @nasid");
                cmd.Parameters.Add(new SqlParameter("@lan_mac", CoreUtil.ToSafeDbObject(config.macid)));
                cmd.Parameters.Add(new SqlParameter("@wifi_mac", CoreUtil.ToSafeDbObject(config.macid2)));
                cmd.Parameters.Add(new SqlParameter("@nasid", config.nasid.ToSafeDbObject()));
                cmd.Parameters.Add(new SqlParameter("@ssiddata", CoreUtil.ToSafeDbObject(config.ssid)));
                cmd.Parameters.Add(new SqlParameter("@deviceType", CoreUtil.ToSafeDbObject(config.deviceType)));
                cmd.Parameters.Add(new SqlParameter("@firmwareVersion", CoreUtil.ToSafeDbObject(config.firmwareVersion)));
                cmd.Parameters.Add(new SqlParameter("@gatewayInfo", CoreUtil.ToSafeDbObject(config.gatewayInfo)));
                res = ResponseType.READER;
                return cmd;
            }), config.nasid.shard_id,
            new ResponseHandler<string>((reader) =>
            {
                if (reader.Read())
                {
                    configs = reader["configs"].ToString();
                }
                return configs;
            })).Execute();
        }

        public bool updateControllerRebootTime(LongIdInfo nasid)
        {
            return new ShardQueryExecutor<bool>(new GetSqlCommand((out ResponseType res) =>
            {
                SqlCommand cmd = new SqlCommand(@"update t_controller set lastRebootTime = getutcdate() 
                    where router_nas_id = @nasid");
                cmd.Parameters.Add(new SqlParameter("@nasid", nasid.ToSafeDbObject()));
                res = ResponseType.NONQUERY;
                return cmd;
            }), nasid.shard_id,
            new ResponseHandler<bool>((reader) =>
            {
                return true;
            })).Execute();
        }

        public void CommitOperationSuccess(LongIdInfo nasid, int Operationid, int status)
        {
            new ShardQueryExecutor<bool>(new GetSqlCommand((out ResponseType res) =>
            {
                SqlCommand cmd = new SqlCommand("update t_router_operation set operation_finish_time=GETUTCDATE(),status=@status where operation_id=@op_id and router_nas_id = @nasid");
                cmd.Parameters.Add(new SqlParameter("@nasid", nasid.ToSafeDbObject()));
                cmd.Parameters.Add(new SqlParameter("@op_id", Operationid));
                cmd.Parameters.Add(new SqlParameter("@status", status));
                res = ResponseType.NONQUERY;
                return cmd;
            }), nasid.shard_id,
            new ResponseHandler<bool>((reader) =>
            {
                return true;
            })).Execute();
        }

        public bool MaintainConfig(string config, LongIdInfo longNasid)
        {
            return new ShardQueryExecutor<bool>(new GetSqlCommand((out ResponseType res) =>
            {
                string query = "update t_controller set configs=configs+@config where router_nas_id=@nasid and configs not like @configlike";
                SqlCommand cmd = new SqlCommand(query);
                cmd.Parameters.Add(new SqlParameter("@config", config));
                cmd.Parameters.Add(new SqlParameter("@configlike", string.Format("%{0}%", config)));
                cmd.Parameters.Add(new SqlParameter("@nasid", longNasid.ToSafeDbObject()));
                res = ResponseType.NONQUERY;
                return cmd;
            }), longNasid.shard_id,
             new ResponseHandler<bool>((reader) =>
             {
                 return true;
             })).Execute();
        }

        public bool SubmitConfig(DeviceConfig config, LongIdInfo userId)
        {
            return new ShardQueryExecutor<bool>(new GetSqlCommand((out ResponseType res) =>
            {
                SqlCommand cmd = new SqlCommand("save_device_build_config");
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.Parameters.Add(new SqlParameter("@nasid", config.nasid.ToSafeDbObject()));
                cmd.Parameters.Add(new SqlParameter("@username", userId.ToSafeDbObject()));
                cmd.Parameters.Add(new SqlParameter("@secondarynasid", config.secondaryNasid.ToSafeDbObject()));
                cmd.Parameters.Add(new SqlParameter("@controller_id", 1));
                cmd.Parameters.Add(new SqlParameter("@mac", config.macid));
                cmd.Parameters.Add(new SqlParameter("@device_type", config.deviceType));
                cmd.Parameters.Add(new SqlParameter("@device_password", config.devicePassword));
                cmd.Parameters.Add(new SqlParameter("@ap_password", config.accessPointPassword));
                cmd.Parameters.Add(new SqlParameter("@mode", string.IsNullOrEmpty(config.monitormode) ? "1" : "0"));
                cmd.Parameters.Add(new SqlParameter("@product_id", config.productId));
                cmd.Parameters.Add(new SqlParameter("@channel_type", config.channelType));
                cmd.Parameters.Add(new SqlParameter("@channel_name", string.IsNullOrEmpty(config.channelName) ? string.Empty : config.channelName));
                res = ResponseType.NONQUERY;
                return cmd;
            }), config.nasid.shard_id,
             new ResponseHandler<bool>((reader) =>
             {
                 //ShardHelper.updateMacNasMapping(config.macid, config.nasid);
                 return true;
             })).Execute();
        }

        public bool CheckForBounceAndComplaint(string receiver)
        {
            return new ShardQueryExecutor<bool>(new GetSqlCommand((out ResponseType res) =>
            {
                SqlCommand cmd = new SqlCommand("select 1 FROM t_trigger_report with(nolock) where receiver = @receiver and status in (4,5)");
                cmd.Parameters.Add(new SqlParameter("@receiver", receiver));

                res = ResponseType.READER;
                return cmd;
            }), ShardHelper.SHARD0,
            new ResponseHandler<bool>((reader) =>
            {
                if (reader.Read())
                {
                    return true;
                }
                return false;
            })).Execute();
        }

        public void CommitUpgradeSuccess(LongIdInfo nasid, int status)
        {
            new ShardQueryExecutor<bool>(new GetSqlCommand((out ResponseType res) =>
            {
                SqlCommand cmd = new SqlCommand("update t_router_operation set operation_finish_time=GETUTCDATE(),status= @status where router_nas_id = @nasid and operation_type= 14 and status = 0");
                cmd.Parameters.Add(new SqlParameter("@nasid", nasid.ToSafeDbObject()));
                cmd.Parameters.Add(new SqlParameter("@status", status));

                res = ResponseType.NONQUERY;
                return cmd;
            }), nasid.shard_id,
            new ResponseHandler<bool>((reader) =>
            {
                return true;
            })).Execute();
        }

        public void ReloginUser(User user, out UserSession userSession, long planId, bool doFullLogin = true, Origin source = Origin.I2E1)
        {
            ModelDynamoDb<UserActivity> modelDynamoDb = new ModelDynamoDb<UserActivity>();

            UserActivity userActivity = new UserActivity()
            {
                mobile = user.mobile,
                mac = user.mac,
                storeGroupOrNas = user.storegroupid > 0 ? user.storegroupid : user.backEndNasid.GetLongId(),
                nasid = user.backEndNasid,
                storeGroupId = user.storegroupid,
                source = source.ToString(),
                otpIssuedTime = DateTime.UtcNow,
                loginTime = DateTime.UtcNow,
                otp = new Random().Next(1000, 10000).ToString()
            };
            modelDynamoDb.Insert(userActivity);

            userSession = CoreCacheHelper.GetInstance().GetUserSessions(user, user.isVip, out bool noMappingExists);
            if (userSession == null || userSession.radiusUserResponse != RadiusUserResponse.SUCCESS)
            {
                userSession = WifidogCacheHelper.GetInstance().CreateUserSessions(user, planId);
            }
        }

        public int AddNasBandwidth(LongIdInfo nasid, int type, float down_bw, int operationId, string source)
        {
            return new ShardQueryExecutor<int>(new GetSqlCommand((out ResponseType res) =>
            {
                SqlCommand cmd = new SqlCommand("insert into t_router_bandwidth(nasid, type, download_bw, operation_id, source) values(@nasid, @type, @down_bw, @operation_id, @source);");
                cmd.Parameters.Add(new SqlParameter("@nasid", nasid.ToSafeDbObject()));
                cmd.Parameters.Add(new SqlParameter("@type", type));
                cmd.Parameters.Add(new SqlParameter("@down_bw", down_bw));
                cmd.Parameters.Add(new SqlParameter("@operation_id", operationId));
                cmd.Parameters.Add(new SqlParameter("@source", CoreUtil.ToSafeDbObject(source)));
                res = ResponseType.READER;
                return cmd;
            }), nasid.shard_id,
            new ResponseHandler<int>((reader) =>
            {
                if (reader.Read())
                {
                    return 1;
                }
                else
                {
                    return 0;
                }
            })).Execute();
        }

        /*public long AddRouterTimestamps(LongIdInfo nasid, long id, DateTime? end_time, string status = null)
        {
            if (id == 0)
            {
                return new MySQLQueryExecutor<long>(new GetMySqlCommand((out ResponseType res) =>
                {
                    var dict = new Dictionary<string, string>();
                    dict["type"] = status;
                    MySqlCommand cmd = new MySqlCommand(@"INSERT INTO t_router_sessions(nasid, start_time, end_time, extra_data) 
                    VALUES(@nasid, @start_time, @end_time, @status);
                    SELECT LAST_INSERT_ID() as id;
                    ");
                    cmd.Parameters.Add(new MySqlParameter("@nasid", nasid.ToSafeDbObject()));
                    cmd.Parameters.Add(new MySqlParameter("@start_time", DateTime.UtcNow));
                    cmd.Parameters.Add(new MySqlParameter("@end_time", CoreUtil.ToSafeDbObject(end_time)));
                    cmd.Parameters.Add(new MySqlParameter("@status", JsonConvert.SerializeObject(dict).ToSafeDbObject()));
                    res = ResponseType.READER;
                    return cmd;
                }), nasid.shard_id,
                new MySqlResponseHandler<long>((reader) =>
                {
                    if (reader.Read())
                    {
                        return Convert.ToInt64(reader["id"]);
                    }
                    else
                    {
                        return 0;
                    }
                })).Execute();
            }
            else
            {
                return new MySQLQueryExecutor<long>(new GetMySqlCommand((out ResponseType res) =>
                {
                    var dict = new Dictionary<string, string>();
                    dict["type"] = status;
                    MySqlCommand cmd = new MySqlCommand("UPDATE t_router_sessions SET end_time = @end_time, extra_data = @status where id = @id;");
                    cmd.Parameters.Add(new MySqlParameter("@id", id));
                    cmd.Parameters.Add(new MySqlParameter("@end_time", end_time));
                    cmd.Parameters.Add(new MySqlParameter("@status", JsonConvert.SerializeObject(dict).ToSafeDbObject()));
                    res = ResponseType.NONQUERY;
                    return cmd;
                }), nasid.shard_id,
                new MySqlResponseHandler<long>((reader) =>
                {
                    return 1;
                })).Execute();
            }
        }*/

        public long massUpdateRouterSession(List<LongIdInfo> ids, DateTime end_time, string status = null)
        {
            var data = new MySQLQueryExecutor<long>(new GetMySqlCommand((out ResponseType res) =>
            {

                var dict = new Dictionary<string, string>();
                dict["type"] = status;
                var cmd = new MySqlCommand();
                cmd.CommandText = "UPDATE t_router_sessions SET end_time = @end_time, extra_data =@status where id IN (@idList)";
                cmd.Parameters.Add(new MySqlParameter("@end_time", end_time));
                cmd.Parameters.Add(new MySqlParameter("@status", JsonConvert.SerializeObject(dict).ToSafeDbObject()));
                res = ResponseType.NONQUERY;
                return cmd;
            }),
            ((reader, shard) =>
            {
            })).ExecuteAll(ids, "@idList");

            return Convert.ToInt64(data);
            
        }

        public string GetNasAssociatedConfigs(DeviceConfig config)
        {
            return new ShardQueryExecutor<string>(new GetSqlCommand((out ResponseType res) =>
            {
                SqlCommand cmd = new SqlCommand("select configs from t_controller where router_nas_id = @nasid");
                cmd.Parameters.Add(new SqlParameter("@nasid", config.nasid.ToSafeDbObject()));
                res = ResponseType.READER;
                return cmd;
            }), config.nasid.shard_id,
            new ResponseHandler<string>((reader) =>
            {
                string configs = "";
                while (reader.Read())
                {
                    configs = reader["configs"].ToString();
                }
                return configs;
            })).Execute();
        }

        public List<PDOPlan> GetPlanConfigInCombinedSetting(int combinedSettingId)
        {
            return new ShardQueryExecutor<List<PDOPlan>>(new GetSqlCommand((out ResponseType res) =>
            {
                SqlCommand cmd = new SqlCommand(@"select * from t_plan_configuration where combined_setting_id = @combinedSettingId");
                cmd.Parameters.Add(new SqlParameter("@combinedSettingId", combinedSettingId));
                res = ResponseType.READER;
                return cmd;
            }), ShardHelper.SHARD0,
            new ResponseHandler<List<PDOPlan>>((reader) =>
            {
                List<PDOPlan> plans = new List<PDOPlan>();
                while (reader.Read())
                {
                    var pdoPlan = planDbReader(reader);
                    if (pdoPlan != null)
                        plans.Add(pdoPlan);
                }
                return plans;
            })).Execute();
        }

        public int GetSettingIdForNas(LongIdInfo nasid)
        {
            return new MasterQueryExecutor<int>(new GetSqlCommand((out ResponseType res) =>
            {
                SqlCommand cmd = new SqlCommand(@"select * from (
                    	select * from t_wn_segment_mapping where nasid = @nasid and status = 1
                    ) as segmap
                    left join t_wn_segment seg 
                    ON segmap.segment_mapping_id = seg.id");
                cmd.Parameters.Add(new SqlParameter("@nasid", nasid.ToSafeDbObject(1)));
                res = ResponseType.READER;
                return cmd;
            }),
            new ResponseHandler<int>((reader) =>
            {
                List<PDOPlan> plans = new List<PDOPlan>();
                while (reader.Read())
                {
                    int settingId = Convert.ToInt32(reader["setting_id"]);
                    return settingId;
                }
                return 0;
            })).Execute();
        }

        public bool ReadFromGlobalParam(string key)
        {
            return new ShardQueryExecutor<bool>(new GetSqlCommand((out ResponseType res) =>
            {
                SqlCommand cmd = new SqlCommand(@"SELECT * FROM t_global_params where name = @key");
                cmd.Parameters.Add(new SqlParameter("@key", key.ToSafeDbObject()));
                res = ResponseType.READER;
                return cmd;
            }), ShardHelper.SHARD0,
            new ResponseHandler<bool>((reader) =>
            {
                while (reader.Read())
                {
                    return Convert.ToBoolean(reader["param"]);
                }
                return false;
            })).Execute();
        }

        public PDOPlan GetPlanInfo(long planId)
        {
            return BasicMemoryCache.GetInstance().GetValueFromCache("PLAN_INFO" + planId, () =>
            {
                return new ShardQueryExecutor<PDOPlan>(new GetSqlCommand((out ResponseType res) =>
                {
                    SqlCommand cmd = new SqlCommand(@"select * from t_plan_configuration where id = @planId");
                    cmd.Parameters.Add(new SqlParameter("@planId", planId));
                    res = ResponseType.READER;
                    return cmd;
                }), ShardHelper.SHARD0,
            new ResponseHandler<PDOPlan>((reader) =>
            {
                if (reader.Read())
                    return planDbReader(reader);
                return null;
            })).Execute();
            }, TimeSpan.FromHours(1));
        }

        public PDOPlan planDbReader(SqlDataReader reader)
        {
            var pdoPlan = new PDOPlan()
            {
                data_limit = long.Parse(reader["data_limit"].ToString()),
                time_limit = long.Parse(reader["time_limit"].ToString()),
                speed_limit_mbps = (int)reader["speed_limit_mbps"],
                price = double.Parse(reader["price"].ToString()),
                discount = (int)reader["discount"],
                name = reader["name"].ToString(),
                id = long.Parse(reader["id"].ToString()),
                active = (bool)reader["active"],
                concurrent_devices = (int)reader["concurrent_devices"],
                combined_setting_id = (int)reader["combined_setting_id"]
            };
            if (pdoPlan.concurrent_devices <= 0)
                pdoPlan.concurrent_devices = 1;
            try
            {
                pdoPlan.costPrice = double.Parse(reader["cost_price"].ToString());
            }
            catch (Exception ex) { }
            return pdoPlan;
        }

        public double GetAccountBalance(LongIdInfo accountId)
        {
            return new ShardQueryExecutor<double>(new GetSqlCommand((out ResponseType res) =>
            {
                SqlCommand cmd = new SqlCommand("SELECT TOP 1 balance FROM t_account_balance_sheet WHERE account_id = @account_id ORDER BY id DESC");
                cmd.Parameters.Add(new SqlParameter("@account_id", accountId.ToSafeDbObject()));
                res = ResponseType.READER;
                return cmd;
            }), accountId.shard_id,
            new ResponseHandler<double>((reader) =>
            {
                if (reader.Read())
                {
                    return (double)reader["balance"];
                }
                return 0;
            })).Execute();
        }

        public async Task<double> FetchAccountBalance(LongIdInfo accountId)
        {
            try
            {
                object data = await PaymentServiceCommunication.GetPaymentServiceData($"{ApiConstants.GetAccountBalance}?accountId={accountId.GetLongId()}");
                return data == null ? 0 : (double)data;
            }
            catch (Exception ex)
            {
                Logger.GetInstance().Error("GetAccountBalance exception: " + JsonConvert.SerializeObject(ex));
                return 0;
            }
            return 0;
        }

        public MPaymentHistory GetPaymentDetails(string transaction_id)
        {
            TransactionId transactionId = TransactionId.GetInstance(transaction_id);
            ModelDynamoDb<MPaymentHistory> modelDynamoDb = new ModelDynamoDb<MPaymentHistory>();
            return modelDynamoDb.GetById(transactionId.mobile, transaction_id);
        }


        public string GetLastMobileOnHomeRouter(LongIdInfo nasid)
        {

            ModelDynamoDb<HomeRouterPlan> modelDynamoDb = new ModelDynamoDb<HomeRouterPlan>();
            DynamoQueryBuilder<HomeRouterPlan> dynamoQueryBuilder = new DynamoQueryBuilder<HomeRouterPlan>();
            dynamoQueryBuilder.AddkeyConditionExpression(p => p.nasId, DynamoComparisonOperator.Equal, nasid)
                              .AddFilterCondition(p => p.planStartTime, DynamoComparisonOperator.LessThanOrEqual, DateTime.UtcNow);

            List<HomeRouterPlan> allHomeRouter = modelDynamoDb.GetRecord(dynamoQueryBuilder);
            allHomeRouter.Sort((p1, p2) => p2.planEndTime.CompareTo(p1.planEndTime));
            if (allHomeRouter.Count>0)
                return allHomeRouter[0].mobile;
            return null;
        }

        public bool GetPlanExpiryByDeviceLimit(LongIdInfo longNasid, int limit, out string mobile, out DateTime firstRecharge, out DateTime expiryTime, out HomeRouterPlan homeRouterPlanUser)
        {
            DateTime tempFirstRecharge = DateTime.UtcNow;
            DateTime tempExpiryTime = DateTime.UtcNow;
            HomeRouterPlan tempHomeRouterPlanUser = null;
            string tempMobile = null;
            bool found = false;
            ModelDynamoDb<HomeRouterPlan> modelDynamoDb = new ModelDynamoDb<HomeRouterPlan>();
            DynamoQueryBuilder<HomeRouterPlan> dynamoQueryBuilder= new DynamoQueryBuilder<HomeRouterPlan>();
            dynamoQueryBuilder.AddkeyConditionExpression((p) => p.nasId, DynamoComparisonOperator.Equal, longNasid.GetLongId())
                              .AddFilterCondition((p) => p.deviceLimit, DynamoComparisonOperator.GreaterThanOrEqual, limit);
            
            List<HomeRouterPlan> users = modelDynamoDb.GetRecord(dynamoQueryBuilder);
            users.Sort((p1, p2) => p2.planEndTime.CompareTo(p1.planEndTime));

            foreach (HomeRouterPlan user in users)
            {
                if(user.deviceLimit == Constants.HOME_ROUTER_DEVICE_LIMIT && user.otp == HOMEOTP.DONE)
                {
                    tempFirstRecharge = user.planStartTime;
                    if (!found)
                    {
                        found = true;
                        tempMobile = user.mobile;
                        tempExpiryTime = user.planEndTime;
                        tempHomeRouterPlanUser = user;
                    }
                }
            }
            mobile = tempMobile;
            firstRecharge = tempFirstRecharge;
            expiryTime = tempExpiryTime;
            homeRouterPlanUser = tempHomeRouterPlanUser;
            return found;
        }

        public DateTime GetLastPingTime(LongIdInfo longNasId)
        {
            return new ShardQueryExecutor<DateTime>(new GetSqlCommand((out ResponseType res) =>
            {
                SqlCommand cmd = new SqlCommand(@"select last_ping_time from t_controller where router_nas_id=@nasid");
                cmd.Parameters.Add(new SqlParameter("@nasid", longNasId.ToSafeDbObject()));
                res = ResponseType.READER;
                return cmd;
            }), longNasId.shard_id,
            new ResponseHandler<DateTime>((reader) =>
            {
                DateTime lastPingTime = DateTime.UtcNow;
                if (reader.Read())
                {
                    lastPingTime = Convert.ToDateTime(reader["last_ping_time"].ToString());
                }
                return lastPingTime;

            })).Execute();
        }
        public string SubmitBasicOp(RemoteManagement remote, LongIdInfo nasid, string operationText)
        {
            return new ShardQueryExecutor<string>(new GetSqlCommand((out ResponseType res) =>
            {
                SqlCommand cmd = new SqlCommand("submit_basic_operation");
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.Parameters.Add(new SqlParameter("@nasid", nasid.ToSafeDbObject()));
                cmd.Parameters.Add(new SqlParameter("@controller_id", 1));
                cmd.Parameters.Add(new SqlParameter("@operation_text", operationText == null ? string.Empty : operationText));
                cmd.Parameters.Add(new SqlParameter("@operation_type", remote.operationType));
                cmd.Parameters.Add(new SqlParameter("@operation_parameter", remote.operationParameter));
                cmd.Parameters.Add(new SqlParameter("@expiry", remote.operationExpiryTime));
                res = ResponseType.READER;
                return cmd;
            }), remote.backEndNasid.shard_id,
            new ResponseHandler<string>((reader) =>
            {
                string opid = "";
                if (reader.Read())
                {
                    opid=reader["operation_id"].ToString();
                }
                return opid;

            })).Execute();
        }

        public List<Ipset> GetAllIpsets()
        {
            return new ShardQueryExecutor<List<Ipset>>(new GetSqlCommand((out ResponseType res) =>
            {
                SqlCommand cmd = new SqlCommand("select * from t_ipset_domain_mapping");
                res = ResponseType.READER;
                return cmd;
            }), ShardHelper.SHARD0,
            new ResponseHandler<List<Ipset>>((reader) =>
            {
                List<Ipset> ipsets = new List<Ipset>();
                while (reader.Read())
                {
                    ipsets.Add(new Ipset()
                    {
                        id=(int)reader["id"],
                        ipsetname= reader["ipsetname"].ToString(),
                        domains= reader["domains"].ToString()

                    });
                }
                return ipsets;
            })).Execute();
        }

        public long GenerateUser(HomeRouterPlan user, LongIdInfo nasid, int deviceLimit, bool authState, int charges, long selectedPlanId = 0, string transactionId = null, bool isFirstRecharge = false)
        {
            user.transactionId = transactionId;
            user.deviceLimit = (byte)deviceLimit;
            user.charges = charges;
            user.planId = selectedPlanId;
			if (user.nasId == null)
				user.nasId = new LongIdInfo(0, 0, 1);
			_ = GenericApi.GetInstance().InsertHomeRouterPlan(user).Result;
			CoreCacheHelper.GetInstance().UpgradeUserSession(new WifiUser() { mobile = user.mobile, nasid = nasid.ToString(), storegroupid = 0 }, selectedPlanId, 0, false);
            
            return user.entryUnixEpochTime;
        }

        public long GenerateUser(SecondaryRouterPlan user, LongIdInfo nasid, int deviceLimit, bool authState, int charges, long selectedPlanId = 0, string transactionId = null)
        {
            user.transactionId = transactionId;
            user.deviceLimit = (byte)deviceLimit;
            user.authState = authState;
            user.charges = charges;
            user.planId = selectedPlanId;
            if (user.nasId == null)
                user.nasId = new LongIdInfo(0, 0, 1);
            _ = GenericApi.GetInstance().InsertSecondaryPlan(user).Result;
			if (authState)
            {
                var wifiUser = new WifiUser() { mobile = user.mobile, nasid = nasid.ToString(), storegroupid = 1 };
                var planId = user.entryUnixEpochTime;
                WifidogCacheHelper.GetInstance().Reset(FDMConfig.GetFDMConfigKey(wifiUser), "0");
                var userSession = WifidogCacheHelper.GetInstance().getUserSessionValue(wifiUser, planId);
                if (userSession == null || userSession.radiusUserResponse != RadiusUserResponse.SUCCESS)
                {
					Logger.GetInstance().Error($"Unable to attach session for planId: {planId} to user:{user.mobile}, Session:{(userSession == null ? string.Empty : JsonConvert.SerializeObject(userSession))}");
                    Thread.Sleep(1000);
					WifidogCacheHelper.GetInstance().Reset(FDMConfig.GetFDMConfigKey(wifiUser), "0");
					userSession = WifidogCacheHelper.GetInstance().getUserSessionValue(wifiUser, planId);
					if (userSession == null || userSession.radiusUserResponse != RadiusUserResponse.SUCCESS)
						Logger.GetInstance().Error($"Unable to attach in 2nd retry session for planId: {planId} to user:{user.mobile}, Session:{(userSession == null ? string.Empty : JsonConvert.SerializeObject(userSession))}");
				}

				WifidogCacheHelper.GetInstance().SetUserSessions(wifiUser, userSession, planId);
            }
            return user.entryUnixEpochTime;
        }

        public string GetFirmwareUrlFromId(int id)
        {
            return new ShardQueryExecutor<string>(new GetSqlCommand((out ResponseType res) =>
            {
                SqlCommand cmd = new SqlCommand("select * from t_firmware where id = @id");
                cmd.Parameters.Add(new SqlParameter("@id", id));
                res = ResponseType.READER;
                return cmd;
            }), ShardHelper.SHARD0,
            new ResponseHandler<string>((reader) =>
            {
                if (reader.Read())
                {
                    return reader["url"].ToString();
                }
                return null;
            })).Execute();
        }

        public string getDeviceModel(LongIdInfo nasid)
        {
          return new ShardQueryExecutor<string>(new GetSqlCommand((out ResponseType res) =>
            {
                SqlCommand cmd = new SqlCommand("select device_type from t_controller where router_nas_id=@nasid");
                cmd.Parameters.Add(new SqlParameter("@nasid", nasid.ToSafeDbObject()));
                res = ResponseType.READER;
                return cmd;
            }), nasid.shard_id,
            new ResponseHandler<string>((reader) =>
            {
                if (reader.Read())
                {
                    return reader["device_type"].ToString();
                }
                return null;
            })).Execute();
        }

        public List<RemoteManagement> GetPendingOp(LongIdInfo nasid, string host)
        {
            return new ShardQueryExecutor<List<RemoteManagement>>(new GetSqlCommand((out ResponseType res) =>
            {
                SqlCommand cmd = new SqlCommand(@"select top 20 operation_id, operation_type, operation_finish_time, operation_parameter,
                    status, operation_publish_time, operation_expiry_time
                    from t_router_operation WHERE router_nas_id = @nasid and operation_origin=1
                    order by operation_id desc");
                cmd.Parameters.Add(new SqlParameter("@nasid", nasid.ToSafeDbObject()));
                res = ResponseType.READER;
                return cmd;
            }), nasid.shard_id,
            new ResponseHandler<List<RemoteManagement>>((reader) =>
            {
                var current = DateTime.UtcNow;
                var list = new List<RemoteManagement>();
                while (reader.Read())
                {
                    RemoteManagement remote = new RemoteManagement(host);
                    remote.operationType = (OperationType)(byte)reader["operation_type"];
                    remote.operationParameter = reader["operation_parameter"].ToString();
                    remote.operationTypeText = remote.operationType.ToString();
                    remote.status = (OperationStatus)reader["status"];
                    remote.operationPublishTime = (DateTime)reader["operation_publish_time"];
                    if (remote.operationType != OperationType.NO_OPERATION)
                    {
                        var finishTime = reader["operation_finish_time"];
                        if (finishTime != DBNull.Value)
                            remote.operationFinishTime = CoreUtil.ConvertUtcToIST((DateTime)finishTime).ToString();

                        list.Add(remote);
                    }
                    remote.operationExpiryTime = (DateTime)reader["operation_expiry_time"];
                    if (remote.operationExpiryTime < current && remote.status == 0)
                        remote.status = OperationStatus.EXPIRED;
                }

                return list;

            })).Execute();
        }
        public MPaymentHistory AddWiomSubscription(LongIdInfo longNasId, int planId, int billId)
        {
            if (longNasId != null)
            {
                try
                {
                    return new ShardQueryExecutor<MPaymentHistory>(new GetSqlCommand((out ResponseType res) =>
                    {
                        SqlCommand cmd = new SqlCommand(@"INSERT INTO t_subscriptions(plan_id, nasid, active, start, pause, billing_id)
                        VALUES (@plan_id, @nasid, 1, GETUTCDATE(), DATEADD(yyyy, 1, GETUTCDATE()), @billing_id);");
                        cmd.Parameters.Add(new SqlParameter("@nasid", longNasId.ToSafeDbObject()));
                        cmd.Parameters.Add(new SqlParameter("@plan_id", planId));
                        cmd.Parameters.Add(new SqlParameter("@billing_id", billId));
                        res = ResponseType.READER;
                        return cmd;
                    }), longNasId.shard_id,
                    new ResponseHandler<MPaymentHistory>((reader) =>
                    {
                        return null;
                    })).Execute();
                }
                catch (Exception ex)
                {
                    Logger.GetInstance().Error(ex.ToString());
                }
            }
            return null;
        }
        public PlanDetail getPlanDetailById(int pid)
        {
            List<PlanDetail> plan_details = new List<PlanDetail>();
            string query = @"select * from  wiomPlans where pid=@pid;";
            return new ShardQueryExecutor<PlanDetail>(new GetSqlCommand((out ResponseType res) =>
            {
                SqlCommand cmd = new SqlCommand(query);
                cmd.Parameters.Add(new SqlParameter("@pid", pid));
                res = ResponseType.READER;
                return cmd;
            }), ShardHelper.SHARD0,
            new ResponseHandler<PlanDetail>((reader) =>
            {
                if (reader.Read())
                {
                    var plandetail = new PlanDetail()
                    {
                        planId = (int)reader["pid"],
                        planName = (string)reader["planName"],
                        description = (string)reader["description"],
                        device = (bool)reader["device"],
                        validity = (int)reader["validity"],
                        amount = (double)reader["amount"]
                    };
                    return plandetail;
                }
                else
                {
                    return null;
                }

            })).Execute();
        }
        public Speedtest GetSpeedtestResults(LongIdInfo nasid, int operationId)
        {
            var results = new Speedtest();
            results.result = "false";
            results.download_bw = null;
            results.timestamp = null;
            if (nasid == null)
            {
                return results;
            }

            return new ShardQueryExecutor<Speedtest>(new GetSqlCommand((out ResponseType res) =>
            {
                SqlCommand cmd = new SqlCommand("select top 1 download_bw, timestamp from t_router_bandwidth where nasid = @nasid and operation_id = @operation_id order by timestamp desc");
                cmd.Parameters.Add(new SqlParameter("@nasid", nasid.ToSafeDbObject()));
                cmd.Parameters.Add(new SqlParameter("@operation_id", operationId));
                res = ResponseType.READER;
                return cmd;
            }), nasid.shard_id,
            new ResponseHandler<Speedtest>((reader) =>
            {
                if (reader.Read())
                {
                    results.download_bw = reader["download_bw"].ToString();
                    results.timestamp = reader["timestamp"].ToString();
                    results.result = "true";
                }
                return results;
            })).Execute();
        }
    }
}
