using i2e1_basics.Database;
using i2e1_core.Models.WIOM;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using System;
using System.Data.SqlClient;
using System.Linq;

namespace i2e1_core.Utilities
{
    public class MasterDbHelper
    {
        public static PartnerConfiguration GetPartnerConfiguration(int id)
        {
            return new MasterQueryExecutor<PartnerConfiguration>(new GetSqlCommand((out ResponseType res) =>
            {
                SqlCommand cmd = new SqlCommand(@"select * from t_partner_configuration where id = @id");

                cmd.Parameters.Add(new SqlParameter("@id", id));
                res = ResponseType.READER;
                return cmd;
            }),
            new ResponseHandler<PartnerConfiguration>((reader) =>
            {
                if (reader.Read())
                {
                    var partnerConfiguration = new PartnerConfiguration();

                    var settings = reader["settings"].ToString()
                        .Split(new char[] { ',' }, StringSplitOptions.RemoveEmptyEntries)
                        .Select(x => int.Parse(x));
                    partnerConfiguration.settingIds = settings;
                    partnerConfiguration.commissionPolicy = JsonConvert.DeserializeObject<JObject>(reader["commission_policy"].ToString());
                    partnerConfiguration.onuPolicy = JsonConvert.DeserializeObject<JObject>(reader["onu_policy"].ToString());
                    return partnerConfiguration;
                }
                return null;
            })).Execute();
        }
    }
}
