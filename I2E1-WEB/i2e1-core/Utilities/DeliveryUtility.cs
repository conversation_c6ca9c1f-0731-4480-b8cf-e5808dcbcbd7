using i2e1_basics.Database;
using i2e1_basics.Utilities;
using i2e1_core.Models;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Linq;

namespace i2e1_core.Utilities
{
    public class DeliveryUtility
    {
        public static List<string> GetFCMFromAccountId(List<LongIdInfo> accountIds, string role = null)
        {
            Logger.GetInstance().Info(String.Format("DeliveryUtility : GetFCMFromAccountId called with accountIds : {0}", accountIds.ToString()));
            List<string> FcmTokens = new List<string>();
            new ShardQueryExecutor<List<string>>(new GetSqlCommand((out ResponseType res) =>
            {
                SqlCommand cmd = new SqlCommand(@"SELECT top 10 fcm_token FROM t_app_device tad
                    INNER JOIN 
                    (SELECT * FROM t_user WHERE mobile IN 
                    	(SELECT username FROM t_admin WHERE user_id IN
                    		(SELECT mapped_id FROM t_account_mapping1 WHERE 
                                account_id IN (select value from @account_ids) AND mapping_type = 'user'
                                AND (@role IS NULL OR mapping_params LIKE '%' + @role + '%')
                             )
                        )
                    ) b ON tad.t_user_id = b.id where fcm_token is not null and fcm_token != 'null' order by tad.id desc"
                );
                cmd.Parameters.Add(new SqlParameter("@role", role.ToSafeDbObject()));
                res = ResponseType.READER;
                return cmd;
            }), ((reader, shardId) =>
            {
                while (reader.Read())
                {
                    FcmTokens.Add(reader.GetValueOrDefault<string>("fcm_token"));
                }
            })).ExecuteAll(accountIds, "@account_ids");

            return FcmTokens;
        }

        public static List<string> GetFCMFromUserId(List<LongIdInfo> userIds)
        {
            Logger.GetInstance().Info(String.Format("DeliveryUtility : GetFCMFromUserId called with userIds : {0}", userIds.ToString()));
            List<string> FcmTokens = new List<string>();
            new ShardQueryExecutor<List<string>>(new GetSqlCommand((out ResponseType res) =>
            {
                SqlCommand cmd = new SqlCommand(@"SELECT fcm_token FROM t_app_device tad
                    INNER JOIN 
                    (SELECT * FROM t_user WHERE mobile IN 
                    	(SELECT username FROM t_admin WHERE user_id IN (select value from @user_ids))
                    ) b ON tad.t_user_id = b.id"
                );
                res = ResponseType.READER;
                return cmd;
            }), ((reader, shardId) =>
            {
                while (reader.Read())
                {
                    FcmTokens.Add(reader.GetValueOrDefault<string>("fcm_token"));
                }
            })).ExecuteAll(userIds, "@user_ids");

            return FcmTokens;
        }

        public static List<string> GetFCMFromMobiles(List<string> numbers)
        {
            Logger.GetInstance().Info(String.Format("DeliveryUtility : GetFCMFromUserId called with userIds : {0}", numbers.ToString()));
            List<string> FcmTokens = new List<string>();
            new ShardQueryExecutor<List<string>>(new GetSqlCommand((out ResponseType res) =>
            {
                SqlCommand cmd = new SqlCommand(@"SELECT a.fcm_token FROM t_app_device a INNER JOIN t_user b ON (a.t_user_id = b.id)
                    where mobile in ('" + String.Join("','", numbers) + "')"
                );
                res = ResponseType.READER;
                return cmd;
            }), ((reader, shardId) =>
            {
                while (reader.Read())
                {
                    FcmTokens.Add(reader.GetValueOrDefault<string>("fcm_token"));
                }
            })).ExecuteAll();

            return FcmTokens;
        }
        public static List<string> GetMobileFromUserId(List<LongIdInfo> userIds)
        {
            Logger.GetInstance().Info(String.Format("DeliveryUtility : GetMobileFromUserId called with userIds : {0}", userIds.ToString()));
            List<string> numbers = new List<string>();
            List<long> shortUserIds = userIds.Select(m => (long)m.ToSafeDbObject()).ToList();
            new ShardQueryExecutor<List<string>>(new GetSqlCommand((out ResponseType res) =>
            {
                SqlCommand cmd = new SqlCommand(@"SELECT username FROM t_admin WHERE username IN (" + String.Join(",", shortUserIds) + @")");
                res = ResponseType.READER;
                return cmd;
            }), ((reader, shardId) =>
            {
                while (reader.Read())
                {
                    numbers.Add(reader.GetValueOrDefault<string>("username"));
                }
            })).ExecuteAll();

            return numbers;
        }

        public static int StoreMessages(DeliveryMsg msg)
        {
            Logger.GetInstance().Info(String.Format("DeliveryUtility : StoreMessages called with messages : {0}", JsonConvert.SerializeObject(msg)));
            return new ShardQueryExecutor<int>(new GetSqlCommand((out ResponseType res) =>
            {
                DataTable dt = new DataTable();
                //Add columns  
                dt.Columns.Add(new DataColumn("account_id", typeof(long)));
                dt.Columns.Add(new DataColumn("user_id", typeof(long)));
                dt.Columns.Add(new DataColumn("msg", typeof(string)));
                dt.Columns.Add(new DataColumn("type", typeof(int)));
                dt.Columns.Add(new DataColumn("mobile", typeof(string)));

                if (msg.accountIds.Value != null && msg.accountIds.Value.Count > 0)
                    foreach(LongIdInfo account in msg.accountIds.Value) 
                    {
                        dt.Rows.Add(account.ToSafeDbObject(1), null, JsonConvert.SerializeObject(msg.msg), msg.msgType, null);
                    }

                if(msg.userIds != null && msg.userIds.Count > 0)
                    foreach (LongIdInfo user in msg.userIds)
                    {
                        dt.Rows.Add(null, user.ToSafeDbObject(1), JsonConvert.SerializeObject(msg.msg), msg.msgType, null);
                    }

                if (msg.numbers != null && msg.numbers.Count > 0)
                    foreach (string mobile in msg.numbers)
                    {
                        dt.Rows.Add(null, null, JsonConvert.SerializeObject(msg.msg), msg.msgType, mobile);
                    }

                SqlCommand cmd = new SqlCommand(@"INSERT INTO t_account_messages(account_id, user_id, msg, type, mobile)
                    SELECT * FROM @newRecords");

                cmd.Parameters.Add(new SqlParameter("@newRecords", dt) { TypeName = "dbo.type_account_msg" });
                res = ResponseType.READER;
                return cmd;
            }), ShardHelper.SHARD0,
            new ResponseHandler<int>((reader) => {
                if (reader.RecordsAffected > 0)
                {
                    int insertedRows = reader.RecordsAffected;
                    Logger.GetInstance().Info(String.Format("DeliveryUtility : StoredMessages {0}", insertedRows));
                    return insertedRows;
                }
                return 0;
            })).Execute();
        }

        public static bool SendNotification(Dictionary<string, string> data, List<LongIdInfo> accountIds = null, string role = "", List<LongIdInfo> userIds = null, List<string> numbers = null)
        {
            Logger.GetInstance().Info("DeliveryUtility : SendNotification : called with data : " + JsonConvert.SerializeObject(data) + ", accountIds : " + accountIds != null ? JsonConvert.SerializeObject(accountIds) : "" + ", role : " + role +
                ", userIds" + userIds != null ? JsonConvert.SerializeObject(userIds) : "");

            try
            {
                if (accountIds == null && userIds == null && numbers == null)
                {
                    Logger.GetInstance().Error("DeliveryUtility : SendNotification : No Target Selected");
                    return false;
                }

                DeliveryMsg msg = new DeliveryMsg()
                {
                    accountIds = new KeyValuePair<string, List<LongIdInfo>>(role, accountIds),
                    userIds = userIds,
                    msgType = DeliveryMsg.MsgType.NOTIFICATION,
                    msg = data,
                    numbers = numbers
                };
                SQSMessage message = new SQSMessage()
                {
                    timeToExecute = DateTime.UtcNow,
                    msg = JsonConvert.SerializeObject(msg)
                };

                CoreSQSHelper.SendStandardQueueMessage(WiomQueue.DELIVERY, message);

                return true;
            }
            catch (Exception ex)
            {
                Logger.GetInstance().Error("DeliveryUtility : SendNotification : thrown exception " + ex.Message);
                return false;
            }
        }
        public static bool SendWhatsApp(Dictionary<string, string> data, DeliveryMsg.MsgType msgType, List<string> numbers = null, List<LongIdInfo> userIds = null, bool isTemplate = false)
        {
            Logger.GetInstance().Error("DeliveryUtility : SendWhatsApp : called with data : " + JsonConvert.SerializeObject(data) + ", numbers : " + numbers != null ? JsonConvert.SerializeObject(numbers) : "" +
                ", userIds" + userIds != null ? JsonConvert.SerializeObject(userIds) : "" + ", messageType : " + msgType.ToString());

            try
            {
                if ((numbers == null || numbers.Count == 0) && (userIds == null || userIds.Count == 0))
                {
                    Logger.GetInstance().Error("HomeController : SendWhatsApp : No Target Selected");
                    return false;
                }

                DeliveryMsg msg = new DeliveryMsg()
                {
                    numbers = numbers,
                    userIds = userIds,
                    msgType = msgType,
                    msg = data
                };
                SQSMessage message = new SQSMessage()
                {
                    timeToExecute = DateTime.UtcNow,
                    msg = JsonConvert.SerializeObject(msg)
                };

                CoreSQSHelper.SendStandardQueueMessage(WiomQueue.DELIVERY, message);

                return true;
            }
            catch (Exception ex)
            {
                Logger.GetInstance().Error("DeliveryUtility : SendWhatsApp : thrown exception " + ex.Message);
                return false;
            }
        }
    }
}
