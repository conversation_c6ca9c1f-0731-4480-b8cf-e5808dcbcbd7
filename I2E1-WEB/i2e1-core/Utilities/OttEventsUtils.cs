using System;
using System.Collections.Generic;
using i2e1_basics.Utilities;
using Microsoft.AspNetCore.Http;
using Newtonsoft.Json;

namespace i2e1_core.Utilities
{
    public class OttEventsUtils
    {
	    public static void SendOttEventLogs(HttpContext httpContext, string eventName, string key, Dictionary<string, object> data)
	    {
		    try
		    {
			    Constants.OTT_LOG.Publish_S3(eventName,key, data);
		    }
		    catch (Exception ex)
		    {
			    Logger.GetInstance().Info($"OttEventsUtils SendOttEventLogs: eventName: {eventName}, key: {key} Exception: {JsonConvert.SerializeObject(ex)}");
		    }      
	    }
	}
}