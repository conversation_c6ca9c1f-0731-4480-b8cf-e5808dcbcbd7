using i2e1_basics.Database;
using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.IO;
using System.Linq;

namespace i2e1_core.Utilities
{
    /// <summary>
    /// Helper class for managing segment mappings with NAS IDs
    /// </summary>
    public class SegmentMappingHelper
    {
        public static int SegmentId = 1008;
		public SegmentMappingHelper()
        {

        }

        /// <summary>
        /// Reads NAS IDs from the specified CSV file
        /// </summary>
        /// <returns>List of NAS IDs</returns>
        private List<string> LoadNasIds()
        {
            var nasIds = new List<string>();
            // Try to find the CSV file in multiple locations
            string fileName = "5_12 Router PLan List - Sheet1.csv";
            string[] possiblePaths = {
                Path.Combine(AppDomain.CurrentDomain.BaseDirectory, fileName),
                Path.Combine(Directory.GetCurrentDirectory(), fileName),
                Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.UserProfile), "Downloads", fileName)
            };
            
            string csvPath = null;
            foreach (var path in possiblePaths)
            {
                if (File.Exists(path))
                {
                    csvPath = path;
                    Console.WriteLine($"Found CSV file at: {csvPath}");
                    break;
                }
            }
            
            if (csvPath == null)
            {
                Console.WriteLine($"❌ CSV file not found in any of the checked locations. Please place the file in one of these locations:\n" + 
                                 string.Join("\n", possiblePaths));
                return nasIds;
            }

            try
            {
                using (var reader = new StreamReader(csvPath))
                {
                    // Read header line
                    var headerLine = reader.ReadLine();
                    if (headerLine == null)
                    {
                        Console.WriteLine("❌ CSV file is empty");
                        return nasIds;
                    }

                    var headers = headerLine.Split(',');
                    var nasIdIndex = Array.IndexOf(headers, "sec_lng_nasid");
                    if (nasIdIndex == -1)
                    {
                        Console.WriteLine("❌ CSV file does not contain 'sec_lng_nasid' column");
                        return nasIds;
                    }

                    // Read data lines
                    string line;
                    while ((line = reader.ReadLine()) != null)
                    {
                        var values = line.Split(',');
                        if (values.Length > nasIdIndex && !string.IsNullOrWhiteSpace(values[nasIdIndex]))
                        {
                            var nasId = values[nasIdIndex].Trim();
                            nasIds.Add(nasId);
                            Console.WriteLine(nasId);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ Error reading CSV file: {ex.Message}");
            }

            return nasIds;
        }

        /// <summary>
        /// Inserts NAS IDs into the segment mapping table
        /// </summary>
        /// <returns>True if successful, false otherwise</returns>
        public bool InsertNasIdsIntoSegmentMapping()
        {
            var nasIds = LoadNasIds();

            Console.WriteLine("nas ids nikalne ka function run ho gaya h");

            if (nasIds.Count == 0)
            {
                Console.WriteLine($"❌ No NAS IDs found in");
                return false;
            }

            // Using a single connection with SqlBulkCopy for batch insertion
            try
            {
                // Get connection string from ShardHelper
                string connectionString = i2e1_basics.Utilities.I2e1ConfigurationManager.BASE_DB_STR;
                
                using (SqlConnection connection = new SqlConnection(connectionString))
                {
                    connection.Open();
                    
                    // Create a DataTable to hold all the data
                    DataTable dataTable = new DataTable();
                    dataTable.Columns.Add("segment_mapping_id", typeof(int));
                    dataTable.Columns.Add("nasid", typeof(string));
                    dataTable.Columns.Add("status", typeof(bool));
                    dataTable.Columns.Add("created_by", typeof(string));
                    dataTable.Columns.Add("created_on", typeof(DateTime));
                    dataTable.Columns.Add("updated_by", typeof(string));
                    dataTable.Columns.Add("updated_on", typeof(DateTime));
                    
                    // Current timestamp for created_on and updated_on
                    DateTime now = DateTime.UtcNow;
                    
                    // Add all NAS IDs to the DataTable
                    foreach (var nasId in nasIds)
                    {
                        dataTable.Rows.Add(SegmentId, nasId, true, null, now, null, now);
                    }
                    
                    // Use SqlBulkCopy for efficient batch insertion
                    using (SqlBulkCopy bulkCopy = new SqlBulkCopy(connection))
                    {
                        bulkCopy.DestinationTableName = "dbo.t_wn_segment_mapping";
                        bulkCopy.ColumnMappings.Add("segment_mapping_id", "segment_mapping_id");
                        bulkCopy.ColumnMappings.Add("nasid", "nasid");
                        bulkCopy.ColumnMappings.Add("status", "status");
                        bulkCopy.ColumnMappings.Add("created_by", "created_by");
                        bulkCopy.ColumnMappings.Add("created_on", "created_on");
                        bulkCopy.ColumnMappings.Add("updated_by", "updated_by");
                        bulkCopy.ColumnMappings.Add("updated_on", "updated_on");
                        
                        // Execute the bulk insert
                        bulkCopy.WriteToServer(dataTable);
                    }
                }

                Console.WriteLine($"✅ Successfully inserted {nasIds.Count} rows into t_wn_segment_mapping.");
                return true;
            }
            catch (Exception ex)
            {
                Console.WriteLine("❌ Error: transaction rolled back.");
                Console.WriteLine(ex.Message);
                return false;
            }
        }
    }
}
