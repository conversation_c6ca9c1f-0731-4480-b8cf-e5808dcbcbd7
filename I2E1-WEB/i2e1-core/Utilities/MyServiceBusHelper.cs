using Azure.Messaging.ServiceBus;
using Azure.Messaging.ServiceBus.Administration;
using i2e1_basics.Utilities;
using Newtonsoft.Json;
using System;
using System.Collections.Concurrent;
using System.Threading.Tasks;

namespace i2e1_core.Utilities
{
	public delegate bool StreamResponseHandler(byte[] byteArray);
    public class MyServiceBusHelper
    {
        private ServiceBusClient client ;
        private ServiceBusAdministrationClient sbaClient;
        protected string ServiceBusConnectString;
        protected string topic;
        protected string endPoint;

        public MyServiceBusHelper(string topic, string endPoint)
        {
            this.topic = topic;
            this.endPoint = endPoint;
            this.client = new ServiceBusClient(endPoint);
            this.sbaClient = new ServiceBusAdministrationClient(endPoint);
        }

        public Task StartListener(string subscriptionName, MicroserviceResponseHandler sQSResponseHandler, int maxConcurrentCalls = 1)
        {
            if (!sbaClient.SubscriptionExistsAsync(topic, subscriptionName).Result)
            {
                var res = sbaClient.CreateSubscriptionAsync(new CreateSubscriptionOptions(topic, subscriptionName)
                {
                    AutoDeleteOnIdle = TimeSpan.FromMinutes(5),
                    MaxDeliveryCount = 1
                }).Result;
            }

            var processor = client.CreateProcessor(topic, subscriptionName, new ServiceBusProcessorOptions()
            {
                AutoCompleteMessages = false,
                MaxConcurrentCalls = maxConcurrentCalls
            });

            try
            {
                // add handler to process messages
                processor.ProcessMessageAsync += async (args) =>
                {
                    string body = args.Message.Body.ToString();
                    try
                    {
                        //Logger.GetInstance().Info($"Received: {body}");

                        var msg = JsonConvert.DeserializeObject<MicroServiceMessage>(body);
                        bool handled = sQSResponseHandler(msg);

                        // complete the message. message is deleted from the queue. 
                        if (handled)
                            await args.CompleteMessageAsync(args.Message);
                        else
                            await args.AbandonMessageAsync(args.Message);
                    }
                    catch (Exception ex)
                    {
                        await args.AbandonMessageAsync(args.Message);
                        Logger.GetInstance().Error("Error in processing msg " + body + " " + ex);
                    }
                };

                // add handler to process any errors
                processor.ProcessErrorAsync += (args) =>
                {
                    Logger.GetInstance().Error(args.Exception.ToString());
                    return Task.CompletedTask;
                };

                // start processing 
                return processor.StartProcessingAsync();
            }
            catch (Exception ex)
            {
                Logger.GetInstance().Error($"Error in Listener for topic: {topic} {ex}");
            }

            return null;
        }

        public Task StartStreamListener(string subscriptionName, StreamResponseHandler sQSResponseHandler, int maxConcurrentCalls = 1)
        {
            if (!sbaClient.SubscriptionExistsAsync(topic, subscriptionName).Result)
            {
                var res = sbaClient.CreateSubscriptionAsync(new CreateSubscriptionOptions(topic, subscriptionName)
                {
                    AutoDeleteOnIdle = TimeSpan.FromMinutes(5),
                    MaxDeliveryCount = 1
                }).Result;
            }

            var processor = client.CreateProcessor(topic, subscriptionName, new ServiceBusProcessorOptions()
            {
                AutoCompleteMessages = false,
                MaxConcurrentCalls = maxConcurrentCalls
            });

            try
            {
                // add handler to process messages
                processor.ProcessMessageAsync += async (args) =>
                {
                    byte[] body = args.Message.Body.ToArray();
                    try
                    {
                        Logger.GetInstance().Info($"Received byte array of {body.Length} length");
                        bool handled = sQSResponseHandler(body);

                        // complete the message. message is deleted from the queue. 
                        if (handled)
                            await args.CompleteMessageAsync(args.Message);
                        else
                            await args.AbandonMessageAsync(args.Message);
                    }
                    catch (Exception ex)
                    {
                        await args.AbandonMessageAsync(args.Message);
                        Logger.GetInstance().Error("Error in processing msg " + body + " " + ex);
                    }
                };

                // add handler to process any errors
                processor.ProcessErrorAsync += (args) =>
                {
                    Logger.GetInstance().Error(args.Exception.ToString());
                    return Task.CompletedTask;
                };

                // start processing 
                return processor.StartProcessingAsync();
            }
            catch (Exception ex)
            {
                Logger.GetInstance().Error($"Error in Listener for topic: {topic} {ex}");
            }

            return null;
        }

        public async Task<string> SendMessage(byte[] message, string deduplicationId = null)
        {
            string messageId = deduplicationId ?? Guid.NewGuid().ToString();

            var sbMessage = new ServiceBusMessage(message);

            sbMessage.MessageId = messageId;
            var sender = client.CreateSender(topic);

            try
            {
                await sender.SendMessageAsync(sbMessage);
            }
            catch (ServiceBusException ex)
            {
                //Logger.GetInstance().Error($"ServiceBusTopicHelper: SendMessage - topic not found");
                return "FAILURE";
            }

            Logger.GetInstance().Info($"{messageId} Topic:{topic}");
            return messageId;
        }

		public async Task<string> SendMessage(string topic, MicroServiceMessage messages, DateTime? timeToExecute = null, string deduplicationId = null)
		{
			string messageId = deduplicationId ?? Guid.NewGuid().ToString();

			var sendMessageRequest = new ServiceBusMessage(JsonConvert.SerializeObject(messages, BasicConstants.JSON_SERIALIZER_SETTINGS));

			if (timeToExecute != null)
				sendMessageRequest.ScheduledEnqueueTime = new DateTimeOffset(timeToExecute.Value);

			sendMessageRequest.MessageId = messageId;

			try
			{
				var sender = GetSender(topic);
				await sender.SendMessageAsync(sendMessageRequest);
			}
			catch (ServiceBusException ex)
			{
				Logger.GetInstance().Error("Error in sending message to topic" + ex.ToString());
				return "FAILURE";
			}

			Logger.GetInstance().Info($"{messageId} Topic:{topic}");
			return messageId;
		}

		private static ConcurrentDictionary<string, ServiceBusSender> senderDict = new ConcurrentDictionary<string, ServiceBusSender>();

		protected ServiceBusSender GetSender(string queueOrTopic)
		{
			ServiceBusSender sender = null;
			try
			{
				if (!senderDict.TryGetValue(queueOrTopic, out sender) || sender.IsClosed)
				{
					sender = client.CreateSender(queueOrTopic);
					senderDict[queueOrTopic] = sender;
				}
			}
			catch (Exception ex)
			{
				Logger.GetInstance().Error($"Exception while creating sender. {ex}");
			}

			return sender;
		}

	}
}