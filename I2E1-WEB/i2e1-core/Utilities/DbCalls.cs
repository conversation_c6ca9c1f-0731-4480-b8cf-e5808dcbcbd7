using i2e1_basics.Database;
using i2e1_basics.Utilities;
using i2e1_core.Models;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Data.SqlClient;
using wifidog_core.Models;
using wiom_login_share.Models;
using wiom_router_api.Models;
using wiom_routerplan_share.ExposeApi;
using wiom_routerplan_share.Models.RouterPlan;

namespace i2e1_core.Utilities
{
	public class DbCalls
    {
        private static DbCalls dbCalls = null;

        private DbCalls()
        {
        }

        public static DbCalls CreateInstance()
        {
            if (dbCalls == null)
            {
                dbCalls = new DbCalls();
            }
            return dbCalls;
        }

        public static DbCalls GetInstance()
        {
            return dbCalls;
        }

        public List<DeviceConfig> MapNasWithSecondaryNas(LongIdInfo nas, LongIdInfo secondarynas)
        {
            string query = @"update t_device_build set secondary_nas=@secondarynas
                                output inserted.*
                                where router_nas_id=@nas";
            List<DeviceConfig> res = new List<DeviceConfig>();

            new ShardQueryExecutor<bool>(new GetSqlCommand((out ResponseType res) =>
            {
                SqlCommand cmd = new SqlCommand(query);
                cmd.Parameters.Add(new SqlParameter("@nas", nas.ToSafeDbObject()));
                cmd.Parameters.Add(new SqlParameter("@secondarynas", secondarynas.ToSafeDbObject()));
                res = ResponseType.READER;
                return cmd;
            }), ShardHelper.SHARD0,
            new ResponseHandler<bool>((reader) =>
            {
                while (reader.Read())
                {
                    DeviceConfig deviceConfig = null;
                    deviceConfig = new DeviceConfig();
                    deviceConfig.nasid = new LongIdInfo(ShardHelper.SHARD0, DBObjectType.ACTIVE_NAS, reader["router_nas_id"]);
                    deviceConfig.macid = reader["mac"].ToString();
                    res.Add(deviceConfig);
                }
                return true;
            })).Execute();
            return res;
        }

        public List<string> GetSenderIds(LongIdInfo partnerId)
        {
            return new ShardQueryExecutor<List<string>>(new GetSqlCommand((out ResponseType res) =>
            {
                SqlCommand cmd = new SqlCommand("select sender_id from t_partner_parameters where partner_id = @partner_id");
                cmd.Parameters.Add(new SqlParameter("@partner_id", partnerId.ToSafeDbObject()));
                res = ResponseType.READER;
                return cmd;
            }), partnerId.shard_id,
            new ResponseHandler<List<string>>((reader) =>
            {
                List<string> senderids = new List<string>();
                while (reader.Read())
                {
                    senderids.Add(reader["sender_id"].ToString());
                }
                return senderids;
            })).Execute();
        }

        public static bool UnregisterStoreEntry(LongIdInfo nasid)
        {
            return new ShardQueryExecutor<bool>(new GetSqlCommand((out ResponseType res) =>
            {
                string query = @"UPDATE t_store SET install_state = 5 WHERE router_nas_id = @nasid
                                 DELETE FROM t_combined_setting_nas_mapping WHERE nas_id = @nasid
                                 DELETE FROM t_admin_mapping where mapping_type = 'location' and mapped_id = @nasid";
                SqlCommand cmd = new SqlCommand(query);
                cmd.Parameters.Add(new SqlParameter("@nasid", nasid.ToSafeDbObject()));
                res = ResponseType.READER;
                return cmd;
            }), nasid.shard_id,
            new ResponseHandler<bool>((reader) =>
            {
                return true;
            })).Execute();
        }

        public bool UpdatePassword(string username, string oldPassword, string newPassword, bool automated=true)
        {
            var longId = ShardHelper.getLongUserIdFromMobile(username);
            return new ShardQueryExecutor<bool>(new GetSqlCommand((out ResponseType res) =>
            {
                SqlCommand cmd = new SqlCommand(@"update t_admin set password = @new_password, active = 1, is_password_temporary = @automated where username = @username and password = @old_password");
                cmd.Parameters.Add(new SqlParameter("@new_password", newPassword));
                cmd.Parameters.Add(new SqlParameter("@old_password", oldPassword));
                cmd.Parameters.Add(new SqlParameter("@username", username));
                cmd.Parameters.Add(new SqlParameter("@automated", automated));
                res = ResponseType.READER;
                return cmd;
            }), longId.shard_id,
            new ResponseHandler<bool>((reader) =>
            {
                return true;
            })).Execute();
        }

        public static bool IsPartnerNameDuplicate(string partnerName)
        {
            return new ShardQueryExecutor<bool>(new GetSqlCommand((out ResponseType res) =>
            {
                SqlCommand cmd = new SqlCommand("select count(*) as count from t_partner where partner_name = @partner_name");
                cmd.Parameters.Add(new SqlParameter("@partner_name", partnerName));
                res = ResponseType.READER;
                return cmd;
            }), ShardHelper.SHARD0,
            new ResponseHandler<bool>((reader) =>
            {
                if (reader.Read())
                {
                    if ((int)reader["count"] > 0)
                        return true;
                    else
                        return false;
                }
                else
                {
                    return false;
                }
            })).Execute();
        }

        public Dictionary<string, Dictionary<string, string>> GetListChecks(LongIdInfo longNas)
        {
            return new ShardQueryExecutor<Dictionary<string, Dictionary<string, string>>>(new GetSqlCommand((out ResponseType res) =>
            {
                SqlCommand cmd = new SqlCommand("select * from t_single_nas_operations where nas_id = @nasid");
                cmd.Parameters.Add(new SqlParameter("@nasid", longNas.local_value));

                res = ResponseType.READER;
                return cmd;
            }), longNas.shard_id,
            new ResponseHandler<Dictionary<string, Dictionary<string, string>>>((reader) =>
            {
                Dictionary<string, Dictionary<string, string>> result = new Dictionary<string, Dictionary<string, string>>();
                result.Add(ListCheckDataType.BLOCKED_LIST.ToString(), new Dictionary<string, string>());
                result.Add(ListCheckDataType.WHITE_LIST.ToString(), new Dictionary<string, string>());
                result.Add(ListCheckDataType.VIP_LIST.ToString(), new Dictionary<string, string>());
                result.Add(ListCheckDataType.MAC_WHITELISTING.ToString(), new Dictionary<string, string>());
                result.Add(ListCheckDataType.MAC_BLACKLISTING.ToString(), new Dictionary<string, string>());
                result.Add(ListCheckDataType.MAC_VIPLISTING.ToString(), new Dictionary<string, string>());
                result.Add(ListCheckDataType.SSID.ToString(), new Dictionary<string, string>());
                result.Add(ListCheckDataType.SSID_PASSWORD.ToString(), new Dictionary<string, string>());
                while (reader.Read())
                {
                    ListCheckDataType checkData = (ListCheckDataType)reader["single_operation_id"];
                    var str = reader["parameters"].ToString();
                    if (!string.IsNullOrEmpty(str) && result.TryGetValue(checkData.ToString(), out var data))
                    {
                        var list = str.Split(new string[] { "," }, StringSplitOptions.None);
                        foreach (string s in list)
                        {
                            if (s.Contains(";"))
                            {
                                string key = s.Substring(0, s.IndexOf(';'));
                                string value = s.Substring(s.IndexOf(';') + 1);
                                data[key] = value;
                            }
                        }
                    }
                }
                return result;
            })).Execute();
        }

        public List<string> GetBlockedWebsites(LongIdInfo nasid)
        {
            return new ShardQueryExecutor<List<string>>(new GetSqlCommand((out ResponseType res) =>
            {
                SqlCommand cmd = new SqlCommand("select blocked_domain from t_blocked_domains where router_nas_id = @nasid");
                cmd.Parameters.Add(new SqlParameter("@nasid", nasid.ToSafeDbObject()));
                res = ResponseType.READER;
                return cmd;
            }), nasid.shard_id,
            new ResponseHandler<List<string>>((reader) =>
            {
                var list = new List<string>();
                while (reader.Read())
                {
                    list.Add(reader["blocked_domain"].ToString());
                }
                return list;
            })).Execute();
        }

        public String GetRouterPartnerId(LongIdInfo longNasId)
        {
            return new ShardQueryExecutor<String>(new GetSqlCommand((out ResponseType res) =>
            {
                SqlCommand cmd = new SqlCommand("SELECT partner_cd from t_store WHERE router_nas_id = @nasid");
                cmd.Parameters.Add(new SqlParameter("@nasid", longNasId.local_value));
                res = ResponseType.READER;
                return cmd;
            }), longNasId.shard_id,
            new ResponseHandler<String>((reader) =>
            {
                String partnerid;
                if (reader.Read())
                {
                    partnerid = reader["partner_cd"].ToString();
                    return partnerid;
                }
                return null;
            })).Execute();
        }

        public bool ToggleDeviceStatus(string mac, LongIdInfo userId)
        {
            return new ShardQueryExecutor<bool>(new GetSqlCommand((out ResponseType res) =>
            {
                SqlCommand cmd = new SqlCommand("update t_device_build set status=status^1,modified_by=@userId  where mac=@mac");
                cmd.Parameters.Add(new SqlParameter("@mac", mac));
                cmd.Parameters.Add(new SqlParameter("@userId", userId.ToSafeDbObject()));
                res = ResponseType.NONQUERY;
                return cmd;
            }), ShardHelper.SHARD0,
            new ResponseHandler<bool>((reader) =>
            {
                return true;
            })).Execute();
        }

        public List<DeviceConfig> GetConfig(long shardId, int start, int end)
        {
            return new ShardQueryExecutor<List<DeviceConfig>>(new GetSqlCommand((out ResponseType res) =>
            {
                SqlCommand cmd = new SqlCommand("SELECT b.router_nas_id as portalnas,MyDerivedTable.* FROM(SELECT *, ROW_NUMBER() OVER(ORDER BY modified_time desc) AS RowNum FROM t_device_build ) AS MyDerivedTable left join t_controller b on MyDerivedTable.router_nas_id=b.router_nas_id WHERE MyDerivedTable.RowNum BETWEEN @start AND @end order by MyDerivedTable.modified_time desc");
                cmd.Parameters.Add(new SqlParameter("@start", start));
                cmd.Parameters.Add(new SqlParameter("@end", end));
                res = ResponseType.READER;
                return cmd;
            }), shardId,
             new ResponseHandler<List<DeviceConfig>>((reader) =>
             {
                 var list = new List<DeviceConfig>();
                 DeviceConfig deviceConfig = null;
                 while (reader.Read())
                 {
                     deviceConfig = new DeviceConfig();
                     deviceConfig.nasid = new LongIdInfo(shardId, (long)DBObjectType.ACTIVE_NAS, Convert.ToInt64(reader["router_nas_id"]));
                     deviceConfig.secondaryNasid = new LongIdInfo(shardId, (long)DBObjectType.ACTIVE_NAS, Convert.ToInt64(reader["secondary_nas"]));
                     deviceConfig.macid = reader["mac"].ToString();
                     deviceConfig.deviceMode = reader["mode"].ToString();
                     deviceConfig.deviceMode = reader["mode"].ToString();
                     deviceConfig.deviceType = reader["device_type"].ToString();
                     deviceConfig.addedtime = (DateTime)reader["added_time"];
                     deviceConfig.modtime = (DateTime)reader["modified_time"];
                     deviceConfig.status = (bool)reader["status"];
                     deviceConfig.devicePassword = reader["devicepassword"].ToString();
                     deviceConfig.accessPointPassword = reader["appassword"].ToString();
                     deviceConfig.isOnPortal = DBNull.Value.Equals(reader["portalnas"]) ? false : true;
                     deviceConfig.productId = DBNull.Value.Equals(reader["product_id"]) ? 1 : (int)reader["product_id"];
                     deviceConfig.channelType = DBNull.Value.Equals(reader["channel_type"]) ? 1 : (int)reader["channel_type"];
                     deviceConfig.channelName = DBNull.Value.Equals(reader["channel_name"]) ? "" : reader["channel_name"].ToString();
                     list.Add(deviceConfig);
                 }
                 return list;
             })).Execute();
        }

        public List<DeviceConfig> GetConfigForNas(DeviceConfig config)
        {
            return new ShardQueryExecutor<List<DeviceConfig>>(new GetSqlCommand((out ResponseType res) =>
            {
                SqlCommand cmd = new SqlCommand("SELECT b.router_nas_id as portalnas,a.* FROM t_device_build a left join t_controller b on a.router_nas_id=b.router_nas_id where a.router_nas_id=@nasid or a.mac = @mac order by modified_time desc");
                cmd.Parameters.Add(new SqlParameter("@nasid", config.nasid.ToSafeDbObject(1)));
                cmd.Parameters.Add(new SqlParameter("@mac", config.macid == null ? string.Empty : config.macid));
                res = ResponseType.READER;
                return cmd;
            }), ShardHelper.SHARD0,
             new ResponseHandler<List<DeviceConfig>>((reader) =>
             {
                 var list = new List<DeviceConfig>();
                 DeviceConfig deviceConfig = null;
                 while (reader.Read())
                 {
                     deviceConfig = new DeviceConfig();
                     deviceConfig.nasid = new LongIdInfo(ShardHelper.SHARD0, DBObjectType.ACTIVE_NAS, Convert.ToInt64(reader["router_nas_id"]));
                     deviceConfig.secondaryNasid = new LongIdInfo(ShardHelper.SHARD0, DBObjectType.ACTIVE_NAS, Convert.ToInt64(reader["secondary_nas"]));
                     deviceConfig.macid = reader["mac"].ToString();
                     deviceConfig.deviceMode = reader["mode"].ToString();
                     deviceConfig.deviceType = reader["device_type"].ToString();
                     deviceConfig.devicePassword = reader["devicepassword"].ToString();
                     deviceConfig.accessPointPassword = reader["appassword"].ToString();
                     deviceConfig.addedtime = (DateTime)reader["added_time"];
                     deviceConfig.modtime = (DateTime)reader["modified_time"];
                     deviceConfig.status = (bool)reader["status"];
                     deviceConfig.isOnPortal = DBNull.Value.Equals(reader["portalnas"]) ? false : true;
                     deviceConfig.productId = DBNull.Value.Equals(reader["product_id"]) ? 1 : (int)reader["product_id"];
                     deviceConfig.channelType = DBNull.Value.Equals(reader["channel_type"]) ? 1 : (int)reader["channel_type"];
                     deviceConfig.channelName = DBNull.Value.Equals(reader["channel_name"]) ? "" : reader["channel_name"].ToString();
                     list.Add(deviceConfig);
                 }
                 return list;
             })).Execute();
        }

        public bool CheckAndMakeEntry(LongIdInfo longNasId, int controllerid)
        {
            return new ShardQueryExecutor<bool>(new GetSqlCommand((out ResponseType res) =>
            {
                string query = @"IF NOT EXISTS (SELECT 1 FROM t_controller WHERE router_nas_id=@router_nas_id and controller_id=@controller_id)
                                BEGIN
                                    INSERT INTO t_controller(router_nas_id,controller_id,modified_time,last_ping_time)
                                    VALUES(@router_nas_id,@controller_id,GETUTCDATE(),GETUTCDATE())
                                END
                                IF NOT EXISTS (SELECT 1 FROM t_store WHERE router_nas_id=@router_nas_id)
                                BEGIN
                                    insert into t_store
                                    (router_nas_id,shop_name,added_time)
                                    values (@router_nas_id,@router_nas_id,GETUTCDATE())
                                END
                                ";
                SqlCommand cmd = new SqlCommand(query);
                cmd.Parameters.Add(new SqlParameter("@router_nas_id", longNasId.ToSafeDbObject()));
                cmd.Parameters.Add(new SqlParameter("@controller_id", controllerid));
                res = ResponseType.NONQUERY;
                return cmd;
            }), longNasId.shard_id,
             new ResponseHandler<bool>((reader) =>
             {
                 return true;
             })).Execute();
        }

        public List<SecondaryRouterPlan> GetFreePlansCreated(User user, DateTime startTime,string otp)
        {
			return GenericApi.GetInstance().GetSecondaryRouterCheckFreeSessionAvailability(user.mobile,startTime,otp).Result;
        }

        public void CreatePlanForPayment(User user, int retryCount, string status, bool tryLogin)
        {
            bool isGX = user.deviceId != null && user.deviceId.ToLower().StartsWith("gx");
            long secondaryDataLimit = 75 - (retryCount * 25);
			SecondaryRouterPlan sUser = new SecondaryRouterPlan();
			long fdmId;
            int planExtendTime = isGX ? 150 : 90;
			sUser = new SecondaryRouterPlan()
			{
				mobile = user.mobile,
				timePlan = planExtendTime,
				charges = 0,
				createdTime = DateTime.UtcNow,
				status = status,
				dataLimit = secondaryDataLimit,
				paymentMode = "online",
				planStartTime = DateTime.UtcNow,
				planEndTime = DateTime.UtcNow.AddSeconds(planExtendTime),
				nasId = LongIdInfo.IdParser(long.Parse(user.nasid)),
				otp = "PAY_ONLINE",
				planId = Constants.FREE_PLAN_ID
            };
			fdmId = CoreDbCalls.GetInstance().GenerateUser(sUser, user.backEndNasid, 1, true, 0, Constants.FREE_PLAN_ID);

			if (tryLogin && fdmId != 0)
				CoreDbCalls.GetInstance().ReloginUser(user, out var userSession, fdmId);
		}

        public Template GetTemplate(int templateId)
        {
            return new ShardQueryExecutor<Template>(new GetSqlCommand((out ResponseType res) =>
            {
                SqlCommand cmd = new SqlCommand("select * from t_template where template_id = @template_id");
                cmd.Parameters.Add(new SqlParameter("@template_id", templateId));
                res = ResponseType.READER;
                return cmd;
            }), ShardHelper.SHARD0,
            new ResponseHandler<Template>((reader) =>
            {
                if (reader.Read())
                {
                    var template = new Template()
                    {
                        id = (int)reader["template_id"],
                        templateName = reader["template_name"].ToString(),
                        templatePath = reader["template_path"].ToString(),
                        disableChat = (bool)reader["disable_chat"],
                        isFullOverriden = !string.IsNullOrEmpty(reader["full_login_template_path"].ToString()) &&
                                reader["full_login_template_path"].ToString() == "true" ? true : false
                    };

                    if (!string.IsNullOrEmpty(template.templatePath) && template.templatePath.StartsWith("https://"))
                    {
                        template.templatePath = template.templatePath.Replace("https://", "http://");
                    }
                    return template;
                }
                return null;
            })).Execute();
        }

        public List<TriggerInfo> GetTriggerDetails(string triggerStartString = null)
        {
            return new ShardQueryExecutor<List<TriggerInfo>>(new GetSqlCommand((out ResponseType res) =>
            {
                SqlCommand cmd;
                if (triggerStartString == null)
                {
                    cmd = new SqlCommand(@"select * from t_trigger_info where is_scheduled = 1 and 
                                                schedule_start_time <= @time and 
                                                (schedule_stop_time is null OR 
                                                schedule_stop_time > @time OR 
                                                last_ran_time is null)");
                    cmd.Parameters.Add(new SqlParameter("@time", DateTime.UtcNow));
                }
                else
                {
                    cmd = new SqlCommand(@"select * from t_trigger_info where trigger_name like @pattern + '%'");
                    cmd.Parameters.Add(new SqlParameter("@pattern", triggerStartString));
                }

                res = ResponseType.READER;
                return cmd;
            }), ShardHelper.SHARD0,
            new ResponseHandler<List<TriggerInfo>>((reader) =>
            {
                var triggers = new List<TriggerInfo>();
                while (reader.Read())
                {
                    var trigger = new TriggerInfo()
                    {
                        triggerId = (int)reader["trigger_id"],
                        triggerName = reader["trigger_name"].ToString(),
                        codePath = reader["code_path"].ToString(),
                        sendEmailTo = reader["send_email_to"].ToString().Split(new char[] { ',' }, StringSplitOptions.RemoveEmptyEntries)
                    };

                    var scheduleCycle = new Schedule()
                    {
                        isScheduled = true,
                        repeatIntervalInMin = (int)reader["repeat_interval_in_min"],
                        startTime = (DateTime)reader["schedule_start_time"]
                    };

                    var stopTime = reader["schedule_stop_time"];
                    if (stopTime != DBNull.Value)
                        scheduleCycle.endTime = (DateTime)stopTime;

                    var lastRanTime = reader["last_ran_time"];
                    if (lastRanTime != DBNull.Value)
                        trigger.lastRanTime = (DateTime)lastRanTime;

                    var parameters = reader["parameters"];
                    if (parameters != DBNull.Value)
                        trigger.parameters = JsonConvert.DeserializeObject<string[]>(parameters.ToString());

                    trigger.scheduleCycle = scheduleCycle;
                    triggers.Add(trigger);
                }
                return triggers;
            })).Execute();
        }

        public bool UpdateTriggerLastRanTime(int triggerId, bool ranSuccessfully)
        {
            var now = DateTime.UtcNow;
            return new ShardQueryExecutor<bool>(new GetSqlCommand((out ResponseType res) =>
            {
                SqlCommand cmd = new SqlCommand(@"update t_trigger_info set last_ran_time = @last_ran_time, 
                    last_ran_time_success = CASE WHEN @is_success = 1 THEN getutcdate() ELSE last_ran_time_success END where trigger_id = @trigger_id");
                cmd.Parameters.Add(new SqlParameter("@is_success", ranSuccessfully));
                cmd.Parameters.Add(new SqlParameter("@trigger_id", triggerId));
                cmd.Parameters.Add(new SqlParameter("@last_ran_time", now));
                res = ResponseType.NONQUERY;
                return cmd;
            }), ShardHelper.SHARD0,
            new ResponseHandler<bool>((reader) =>
            {
                return true;
            })).Execute();
        }

        public bool ResetTriggerLastRanTime(int triggerId)
        {
            return new ShardQueryExecutor<bool>(new GetSqlCommand((out ResponseType res) =>
            {
                SqlCommand cmd = new SqlCommand(@"update t_trigger_info set last_ran_time = NULL where trigger_id = @trigger_id");
                cmd.Parameters.Add(new SqlParameter("@trigger_id", triggerId));
                res = ResponseType.NONQUERY;
                return cmd;
            }), ShardHelper.SHARD0,
            new ResponseHandler<bool>((reader) =>
            {
                return true;
            })).Execute();
        }

        public void SaveUIEvent(Dictionary<string, object> evt)
        {
        }

        public List<Icon> GetIcons(LongIdInfo longNasid, string mobile)
        {
            return new ShardQueryExecutor<List<Icon>>(new GetSqlCommand((out ResponseType res) =>
            {
                SqlCommand cmd = new SqlCommand("select top 9 * from t_icons where enabled = 1 and (nasid = @nasid or nasid = 1) ORDER BY nasid, experiment_id, display_order");
                cmd.Parameters.Add(new SqlParameter("@nasid", longNasid.local_value));
                res = ResponseType.READER;
                return cmd;
            }), longNasid.shard_id,
            new ResponseHandler<List<Icon>>((reader) =>
            {
                List<Icon> icons_default = new List<Icon>();
                List<Icon> icons_target = new List<Icon>();
                while (reader.Read())
                {
                    Icon icon = new Icon();
                    icon.title = reader["title"].ToString();
                    icon.url = reader["url"].ToString();
                    if (icon.title == "Flipkart") {
                        icon.url = icon.url.Replace("{{affExtParam1}}", "LPGRID_"+ longNasid.local_value.ToString());
                        icon.url = icon.url.Replace("{{affExtParam2}}", mobile);
                    }
                    if (icon.title == "Amazon")
                    {
                        icon.url = icon.url.Replace("{{subid}}", "LPGRID_" + longNasid.local_value.ToString());
                    }
                    icon.imageUrl = reader["image_url"].ToString();
                    icon.nasid = (int)reader["nasid"];
                    icon.displayOrder = (int)reader["display_order"];
                    icon.displayColumns = (int)reader["display_columns"];
                    icon.experimentId = reader["experiment_id"].ToString();
                    if (longNasid.local_value == icon.nasid)
                        icons_target.Add(icon);
                    else
                        icons_default.Add(icon);
                }
                if (icons_target.Count > 0)
                    return icons_target;
                else
                    return icons_default;
            })).Execute();
        }

        public bool SaveTriggerReport(int triggerId, int baseDeliveryId, string sender, string content, List<KeyValuePair<string, string>> receiverMessageIdMapping)
        {
            if(receiverMessageIdMapping != null)
            {
                foreach (var pair in receiverMessageIdMapping)
                {
                    new ShardQueryExecutor<bool>(new GetSqlCommand((out ResponseType res) =>
                    {
                        SqlCommand cmd = new SqlCommand(@"insert into t_trigger_report(trigger_id, base_delivery_id, delivery_provider_id, sender, receiver, content)
                        values(@trigger_id, @base_delivery_id, @delivery_provider_id, @sender, @receiver, @content)");
                        cmd.Parameters.Add(new SqlParameter("@trigger_id", triggerId));
                        cmd.Parameters.Add(new SqlParameter("@base_delivery_id", baseDeliveryId));
                        cmd.Parameters.Add(new SqlParameter("@delivery_provider_id", CoreUtil.ToSafeDbObject(pair.Value)));
                        cmd.Parameters.Add(new SqlParameter("@sender", CoreUtil.ToSafeDbObject(sender)));
                        cmd.Parameters.Add(new SqlParameter("@receiver", CoreUtil.ToSafeDbObject(pair.Key)));
                        cmd.Parameters.Add(new SqlParameter("@content", CoreUtil.ToSafeDbObject(content)));

                        res = ResponseType.NONQUERY;
                        return cmd;
                    }), ShardHelper.SHARD0,
                    new ResponseHandler<bool>((reader) =>
                    {
                        return true;
                    })).Execute();
                }
            }
            return true;
        }

        public List<PlanDetail> getPlanDetail()
        {
            List<PlanDetail> plan_details = new List<PlanDetail>();
            string query = @"select * from  wiomPlans;";
            return new ShardQueryExecutor<List<PlanDetail>>(new GetSqlCommand((out ResponseType res) =>
            {
                SqlCommand cmd = new SqlCommand(query);
                res = ResponseType.READER;
                return cmd;
            }), ShardHelper.SHARD0,
            new ResponseHandler<List<PlanDetail>>((reader) =>
            {
                while (reader.Read())
                {
                    var plandetail = new PlanDetail()
                    {
                        planId = (int)reader["pid"],
                        planName = (string)reader["planName"],
                        description = (string)reader["description"],
                        device = (bool)reader["device"],
                        validity = (int)reader["validity"],
                        amount = (double)reader["amount"]
                    };
                    plan_details.Add(plandetail);
                }
                return plan_details;
            })).Execute();
        }

        public static bool insertUnregisteredStoreEntry(DeviceConfig config)
        {
            return new ShardQueryExecutor<bool>(new GetSqlCommand((out ResponseType res) =>
            {
                string query = @"IF NOT EXISTS (SELECT router_nas_id FROM t_store WHERE router_nas_id = @nasid)
                    BEGIN
                        INSERT INTO t_store(router_nas_id, install_state, device_mac, mode, loc_start_date, product_name) VALUES(@nasid, 5, @mac, 1, GETUTCDATE(), @product_id);
                        INSERT INTO t_channel_mapping(channel_id, nasid, active, mapping_date) VALUES(2, @nasid, 1, GETUTCDATE());
                    END
                    ELSE
                    BEGIN
                        UPDATE t_store set install_state = 5 where router_nas_id = @nasid
                    END";
                
                SqlCommand cmd = new SqlCommand(query);
                cmd.Parameters.Add(new SqlParameter("@nasid", config.nasid.ToSafeDbObject()));

                cmd.Parameters.Add(new SqlParameter("@mac", config.macid));
                cmd.Parameters.Add(new SqlParameter("@product_id", config.productId));
                cmd.Parameters.Add(new SqlParameter("@channel_type", config.channelType));
                cmd.Parameters.Add(new SqlParameter("@channel_name", string.IsNullOrEmpty(config.channelName) ? string.Empty : config.channelName));
                res = ResponseType.READER;
                return cmd;
            }), config.nasid.shard_id,
            new ResponseHandler<bool>((reader) =>
            {
                return true;
            })).Execute();
        }

        public Tuple<string, int> VerifyClientAndPartner(string clientName, string partnerName)
        {
            return new ShardQueryExecutor<Tuple<string, int>>(new GetSqlCommand((out ResponseType res) =>
            {
                SqlCommand cmd = new SqlCommand(@"select b.partner_cd,b.partner_id from t_client a inner join t_partner b on a.client_id=b.client_id 
where a.client_name=@clientName and b.partner_name=@partnerName");
                cmd.Parameters.Add(new SqlParameter("@partnerName", partnerName));
                cmd.Parameters.Add(new SqlParameter("@clientName", clientName));
                res = ResponseType.READER;
                return cmd;
            }), ShardHelper.SHARD0,
            new ResponseHandler<Tuple<string, int>>((reader) =>
            {
                if (reader.Read())
                {
                    string partner_cd = reader["partner_cd"].ToString();
                    int partner_id = (int)reader["partner_id"];
                    return new Tuple<string, int>(partner_cd, partner_id);
                }
                return null;
            })).Execute();
        }

        public string getMarketPlaceName(int marketplaceid)
        {
            return new ShardQueryExecutor<string>(new GetSqlCommand((out ResponseType res) =>
            {
                SqlCommand cmd = new SqlCommand(@"select market_place_name from t_market_place where market_place_id=@marketplaceid");
                cmd.Parameters.Add(new SqlParameter("@marketplaceid", marketplaceid));
                res = ResponseType.READER;
                return cmd;
            }), ShardHelper.SHARD0,
            new ResponseHandler<string>((reader) =>
            {
                if (reader.Read())
                {
                    return reader["market_place_name"].ToString();
                }
                return string.Empty;
            })).Execute();
        }

        public bool UpdateLocation(PortalCsv data, string partner_cd, int partner_id, string shop_name)
        {
            return new ShardQueryExecutor<bool>(new GetSqlCommand((out ResponseType res) =>
            {
                SqlCommand cmd = new SqlCommand(@"update t_store set install_state=1,
                                                mm_nas_id=@mm_nas_id,
                                                device_mac=@device_mac,
                                                device_type=@device_type,
                                                device_version=@device_version,
                                                mode=@mode,
                                                shop_name=@shop_name,
                                                sales_id=@sales_id,
                                                install_id=@install_id,
                                                brand_name=@brand_name,
                                                market_place_id=@market_place_id,
                                                shop_address=@shop_address,
                                                shop_city=@shop_city,
                                                shop_state=@shop_state,
                                                pincode=@pincode,
                                                category=@category,
                                                loc_sub_cat=@loc_sub_cat,
                                                micro_category=@micro_category,
                                                store_tags=@store_tags,
                                                retag_date=@retag_date,
                                                loc_start_date=@loc_start_date,
                                                partner_cd=@partner_cd,
                                                partner_id=@partner_id,
                                                latitude=@latitude,
                                                longitude=@longitude
                                                where router_nas_id=@nasid");
                DateTime StartdateProper = DateTime.Parse(data.Startdate);
                DateTime retagdateProper = DateTime.Parse(data.retagdate);
                cmd.Parameters.Add(new SqlParameter("@nasid", data.nasid.ToSafeDbObject()));
                cmd.Parameters.Add(new SqlParameter("@mm_nas_id", data.MonitorModeNasId));
                cmd.Parameters.Add(new SqlParameter("@device_mac", data.DeviceMAC));
                cmd.Parameters.Add(new SqlParameter("@device_type", data.Devicetype));
                cmd.Parameters.Add(new SqlParameter("@device_version", data.Deviceversion));
                cmd.Parameters.Add(new SqlParameter("@mode", data.Mode));
                cmd.Parameters.Add(new SqlParameter("@shop_name", shop_name));
                cmd.Parameters.Add(new SqlParameter("@sales_id", data.Salesperson));
                cmd.Parameters.Add(new SqlParameter("@install_id", data.Installerperson));
                cmd.Parameters.Add(new SqlParameter("@brand_name", data.brandname));
                cmd.Parameters.Add(new SqlParameter("@market_place_id", data.marketplaceid));
                cmd.Parameters.Add(new SqlParameter("@shop_address", data.Address));
                cmd.Parameters.Add(new SqlParameter("@shop_city", data.City));
                cmd.Parameters.Add(new SqlParameter("@shop_state", data.State));
                cmd.Parameters.Add(new SqlParameter("@pincode", data.Pincode));
                cmd.Parameters.Add(new SqlParameter("@category", data.Category));
                cmd.Parameters.Add(new SqlParameter("@loc_sub_cat", data.Subcategory));
                cmd.Parameters.Add(new SqlParameter("@micro_category", data.MicroCategory));
                cmd.Parameters.Add(new SqlParameter("@store_tags", data.Tags));
                cmd.Parameters.Add(new SqlParameter("@retag_date", retagdateProper));
                cmd.Parameters.Add(new SqlParameter("@loc_start_date", StartdateProper));
                cmd.Parameters.Add(new SqlParameter("@partner_id", partner_id));
                cmd.Parameters.Add(new SqlParameter("@partner_cd", partner_cd));
                cmd.Parameters.Add(new SqlParameter("@latitude", data.latitude));
                cmd.Parameters.Add(new SqlParameter("@longitude", data.longitude));

                res = ResponseType.READER;
                return cmd;
            }), data.nasid.shard_id,
            new ResponseHandler<bool>((reader) =>
            {
                if (reader.Read())
                {
                    return true;
                }
                return false;
            })).Execute();
        }

     //   public bool GetFacebookPeriodAggregateReport(Dictionary<string, long> jObject, int dayOffset, List<LongIdInfo> locations)
     //   {
     //       new ShardQueryExecutor<bool>(new GetSqlCommand((out ResponseType res) =>
     //       {
     //           DateTime end = Util.ConvertUtcToIST(DateTime.UtcNow).Date;
     //           DateTime start = end.AddDays(-dayOffset);
     //           SqlCommand cmd = new SqlCommand(@"select count(distinct CallingStationId) AS count, 
     //               sum(AcctOutputOctets) as data_upload, sum(AcctInputOctets) as data_download
     //               from radacct a with (nolock)
					//inner join t_store b
					//on a.RouterNasId = b.router_nas_id
     //               where b.router_nas_id in (" + string.Join(",", locations) + @") 
	    //            and (b.retag_date is null OR a.AcctStartTime > b.retag_date)
     //               and AcctStartTime BETWEEN @start AND @end");
     //           cmd.Parameters.Add(new SqlParameter("@dayOffset", -dayOffset));
     //           cmd.Parameters.Add(new SqlParameter("@start", start));
     //           cmd.Parameters.Add(new SqlParameter("@end", end));
     //           res = ResponseType.READER;
     //           return cmd;
     //       }), ShardHelper.SHARD0,
     //       new ResponseHandler<bool>((reader) =>
     //       {
     //           if (reader.Read())
     //           {
     //               jObject["unique_users_" + dayOffset + "day"] = (int)reader["count"];
     //               jObject["data_bytes_upload_" + dayOffset + "day"] = (long)reader["data_upload"];
     //               jObject["data_bytes_download_" + dayOffset + "day"] = (long)reader["data_download"];
     //           }
     //           return true;
     //       })).Execute();
     //       return true;
     //   }

        public List<LongIdInfo> GetLocationData(int clientId)
        {
            return new ShardQueryExecutor<List<LongIdInfo>>(new GetSqlCommand((out ResponseType res) =>
            {
                SqlCommand cmd = new SqlCommand(@"select a.router_nas_id from t_store a inner
                        join t_partner b on a.partner_id = b.partner_id
                        inner join t_client c
                        on b.client_id = c.client_id
                        where
                        (c.client_id = @client_id OR len(store_tags) > 10 and substring(store_tags,11,1) = '1')
                        and a.install_state = 1");
                cmd.Parameters.Add(new SqlParameter("@client_id", clientId));

                res = ResponseType.READER;
                return cmd;
            }), ShardHelper.SHARD0,
            new ResponseHandler<List<LongIdInfo>>((reader) =>
            {
                var list = new List<LongIdInfo>();
                while (reader.Read())
                {
                    list.Add(new LongIdInfo(ShardHelper.SHARD0, DBObjectType.ACTIVE_NAS, Convert.ToInt64(reader["router_nas_id"])));
                }
                return list;
            })).Execute();
        }
    }
}