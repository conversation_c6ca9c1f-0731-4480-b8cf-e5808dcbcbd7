using i2e1_basics.Utilities;
using System;
using System.Collections.Generic;

namespace i2e1_core.Utilities
{
    public class MyBigDataHelper : BigDataHelper
    {

        private MyServiceBusHelper sbHelper;
        public MyBigDataHelper(string table, string queue, string connStr) : base(table, queue, null, null)
        {
            this.sbHelper = new MyServiceBusHelper($"{table}", connStr);
        }

        public static MyBigDataHelper CreateTopic(string table, string queue, string connStr)
        {
            MyBigDataHelper bdh = new MyBigDataHelper(table, queue, connStr);
            return bdh;
        }

        protected void LOG_ANALYTICS_SB(AnalyticsEvent bqEvent, List<BigDataHelper.Destination>? destinations = null, DateTime? addedTime = null)
        {
            Logger.GetInstance().Info($"LOG_ANALYTICS_SB event={bqEvent.eventName}, destinations={destinations}");
            if (bqEvent.addedTime == null)
                bqEvent.addedTime = DateTime.UtcNow;

            this.sbHelper.SendMessage(this.table, CreateFiFoMessage(bqEvent, destinations, addedTime));
        }

        public void Log(string name, string key = null, Dictionary<string, object> data = null, DateTime? addedTime = null)
        {
            this.LOG_ANALYTICS_SB(new AnalyticsEvent(table, name, key, data), new List<BigDataHelper.Destination>() { BigDataHelper.Destination.S3 }, addedTime);
        }

    }
}
