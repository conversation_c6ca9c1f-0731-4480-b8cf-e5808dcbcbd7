using System;
using System.Collections.Generic;
using System.Net;
using i2e1_basics.Utilities;
using Microsoft.AspNetCore.Http;
using Newtonsoft.Json;

namespace i2e1_core.Utilities
{
    public class PmWaniUtils
    {
        public const string REG_NO = "PMWANI-DLI-110030-PDOA000010";
        public static PostHogClient postHogClient = new PostHogClient("phc_jTwf7M1g9gE7gTB7lpAefeVxQZ7yAHKcUuCvC3dtiCw");
        public static void SendDailyData(DateTime date, int newCount, int uniqueCount, long dataConsumedInMB)
        {
            using (var client = new WebClient())
            {
                client.Headers[HttpRequestHeader.ContentType] = "application/json";
                client.Headers.Add("WANI-API-KEY", "A1PMdw9nCnTqYupeUj3OaMBX1xxJq5NOZW13fDhYHGFwFqlfYyfU8YM1E1K5lFVQ0Sy8Gpv7bhIKK86OFcIhjocUe7uOFyy9nBMs");
                var data = new
                {
                    regNumber = REG_NO,
                    day = date.ToString("yyyy-MM-dd"),
                    pdoaDataCountry = new {
                        subscriberCount = new {
                            @new = newCount, unique = uniqueCount, },
                        dataConsumed =  dataConsumedInMB}
                };

                var response = client.UploadString("https://pmwani.gov.in/api/stats/pdoa/country/day", JsonConvert.SerializeObject(data));
                Logger.GetInstance().Info(response);
            };
        }

        public static void SendPMWaniLogs(HttpContext httpContext, string eventName, string key, Dictionary<string, object> data)
        {
            try
            {
                Constants.PMWANI_LOGS.Log(eventName, key, data);
			}
            catch (Exception ex)
            {
                Logger.GetInstance().Info($"PmWaniUtils SendPMWaniLogs: eventName: {eventName}, key: {key} Exception: {JsonConvert.SerializeObject(ex)}");
            }      
        }
		public static void SendPMWaniLogs(string eventName, string key, Dictionary<string, object> data, bool sendToPostHog = false)
		{
			try
			{
				Constants.PMWANI_LOGS.Log(eventName, key, data);
			}
			catch (Exception ex)
			{
				Logger.GetInstance().Info($"PmWaniUtils SendPMWaniLogs: eventName: {eventName}, key: {key} Exception: {JsonConvert.SerializeObject(ex)}");
			}
		}
	}
}
