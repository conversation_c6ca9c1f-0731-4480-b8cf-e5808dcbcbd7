using i2e1_basics.Utilities;
using i2e1_core.Models;
using i2e1_core.Utilities;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using RestSharp;
using System;

namespace i2e1_core.PG
{
    public class JuspayPG
    {
        public static JObject MandateExecutionSubscriptionOrder(string transactionId, string mobile, double amount, string customerId, string mandateId)
        {
            var client = new RestClient(I2e1ConfigurationManager.GetInstance().GetSetting("JUSPAY.API") + "/txns");
            var request = new RestRequest() { Method = Method.Post };
            request.AddHeader("Authorization", "Basic " + I2e1ConfigurationManager.GetInstance().GetSetting("JUSPAY.KEY"));
            request.AddHeader("x-merchantid", "wiom");
            request.AddHeader("Content-Type", "application/json");
            var body = new JObject()
            {
                { "order.order_id", transactionId },
                { "order.amount", amount },
                { "order.customer_id", customerId},
                { "order.customer_email", "<EMAIL>"},
                { "order.customer_phone", mobile },
                { "order.gateway_id" , "19" },
                { "order.return_url", I2e1ConfigurationManager.IS_PROD ? "https://www.i2e1.in/wiom/JusPayResponse" : "https://maxima.i2e1.in/wiom/JusPayResponse"},
                { "merchant_id", "wiom" },
                { "mandate_id", mandateId},
                { "format", "json" },
            };

            request.AddParameter("application/json", JsonConvert.SerializeObject(body), ParameterType.RequestBody);
            RestResponse response = client.Execute(request);
            return JsonConvert.DeserializeObject<JObject>(response.Content);
        }
        public static JObject ChangeMandateStatus(string mandateId, string mandateStatus)
        {
            var client = new RestClient(I2e1ConfigurationManager.GetInstance().GetSetting("JUSPAY.API") + "/mandates/" + mandateId);
            var request = new RestRequest() { Method = Method.Post };
            request.AddHeader("Authorization", "Basic " + I2e1ConfigurationManager.GetInstance().GetSetting("JUSPAY.KEY"));
            request.AddHeader("x-merchantid", "wiom");
            request.AddHeader("Content-Type", "application/json");
            var body = new JObject()
            {
                { "command", "revoke" },
            };

            request.AddParameter("application/json", JsonConvert.SerializeObject(body), ParameterType.RequestBody);
            RestResponse response = client.Execute(request);
            return JsonConvert.DeserializeObject<JObject>(response.Content);
        }
        public static JObject MandateNotificationSubscriptionOrder(string transactionId, double amount, string mandateId)
        {
            var client = new RestClient(I2e1ConfigurationManager.GetInstance().GetSetting("JUSPAY.API") + "/mandates/" + mandateId);
            var request = new RestRequest() { Method = Method.Post };
            request.AddHeader("Authorization", "Basic " + I2e1ConfigurationManager.GetInstance().GetSetting("JUSPAY.KEY"));
            request.AddHeader("x-merchantid", "wiom");
            request.AddHeader("Content-Type", "application/json");
            var sourceInfo = new JObject()
            {
                { "amount", amount.ToString() },
                { "txn_date", System.DateTimeOffset.Now.AddHours(25).ToUnixTimeSeconds().ToString() }
            };
            var body = new JObject()
            {
                { "command", "pre_debit_notify" },
                { "object_reference_id", transactionId },
                { "source_info", JsonConvert.SerializeObject(sourceInfo) },
                { "description", "We Will Deduct Wiom Wifi Plan Tomorrow" },
                { "metadata","Wiom" }
            };

            request.AddParameter("application/json", JsonConvert.SerializeObject(body), ParameterType.RequestBody);
            RestResponse response = client.Execute(request);
            return JsonConvert.DeserializeObject<JObject>(response.Content);
        }
    }
}
