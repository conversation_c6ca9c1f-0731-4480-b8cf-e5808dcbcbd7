using i2e1_basics.Utilities;

namespace i2e1_core.PG
{
    public class PaymentGateway
    {
        // public static string workingKey = "3168E97C0D4DE1D918D0DBD4B2A38C28";//put in the 32bit alpha numeric key in the quotes provided here 	
        //public static string strAccessCode = "AVPR66DH39AH17RPHA"; // put the access code in the quotes provided here.

        public static string workingKey = I2e1ConfigurationManager.GetInstance().GetSetting("CCAvenue.workingKey.i2e1");
        public static string strAccessCode = I2e1ConfigurationManager.GetInstance().GetSetting("CCAvenue.accessCode.i2e1");

        //below details are for i2e1 bank account
        //URL: http://i2e1.com access_code: AVNR68DL13AD06RNDA Working key: 2644D824FC4AA9271C0AEF139EB321AE 
        //URL: https://test.i2e1.in access_code: AVKU00DL81BC06UKCB Working key: A85186515FAF21A4BBBF72F510CC45B8 
        //URL: https://www.localhost:44300 access_code: AVKU00DL81BC05UKCB Working key: 268222B4502D55E922BF4F567CB66621

        //ccavenue test environment - https://test.ccavenue.com/transaction/transaction.do?command=initiateTransaction
        //ccavenue live environment - https://secure.ccavenue.com/transaction/transaction.do?command=initiateTransaction

        //Razorpay UPI
    }
}