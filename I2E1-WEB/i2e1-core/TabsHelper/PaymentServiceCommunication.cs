using i2e1_basics.Models;
using i2e1_basics.Utilities;
using Newtonsoft.Json;
using System;
using System.Net.Http;
using System.Text;
using System.Threading.Tasks;

namespace i2e1_core.TabsHelper
{
    internal class PaymentServiceCommunication
    {
        public static async Task<object> GetPaymentServiceData(string endPoints)
        {
            using (HttpClient httpClient = new HttpClient())
            {
                string BaseUrl = I2e1ConfigurationManager.IS_PROD ? "https://payment.i2e1.in/" : $"https://payment.{I2e1ConfigurationManager.DEPLOYED_ON}.i2e1.in/";
                try
                {
                    string url = BaseUrl + endPoints;
                    HttpResponseMessage response = await httpClient.GetAsync(url);
                    return GetDataFromResponse(response);
                }
                catch (Exception ex)
                {
                    Logger.GetInstance().Error("GetPaymentServiceData exception: " + JsonConvert.SerializeObject(ex));
                    return null;
                }
                return null;
            }
        }
        public static async Task<object> PostPaymentServiceDataAsync(string endPoints, string jsonContent)
        {
            using (HttpClient httpClient = new HttpClient())
            {
                string BaseUrl = I2e1ConfigurationManager.IS_PROD ? "https://payment.i2e1.in/" : $"https://payment.{I2e1ConfigurationManager.DEPLOYED_ON}.i2e1.in/";
                try
                {
                    string url = BaseUrl + endPoints;
                    var stringContent = new StringContent(jsonContent, Encoding.UTF8, "application/json");
                    HttpResponseMessage response = await httpClient.PostAsync(url, stringContent);
                    return GetDataFromResponse(response);    
                }
                catch (Exception ex)
                {
                    Logger.GetInstance().Error("PostPaymentServiceData exception: " + JsonConvert.SerializeObject(ex));
                    return null;
                }
            }
            return null;
        }
        private static object GetDataFromResponse(HttpResponseMessage response)
        {
            try
            {
                if (response.IsSuccessStatusCode)
                {
                    string responseContent = response.Content.ReadAsStringAsync().Result;
                    JsonResponse jsonResponse = JsonConvert.DeserializeObject<JsonResponse>(responseContent);
                    if (jsonResponse.status == ResponseStatus.SUCCESS)
                    {
                        return jsonResponse.data;
                    }
                }
                else
                {
                    Logger.GetInstance().Error("GetDataFromResponse response : " + JsonConvert.SerializeObject(response));
                    return null;
                }
            }
            catch(Exception ex)
            {
                Logger.GetInstance().Error("GetDataFromResponse exception: " + JsonConvert.SerializeObject(ex));
            }
            return null;
        }
    }
}
