<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net6.0</TargetFramework>
    <RootNamespace>i2e1_core</RootNamespace>
    <CopyLocalLockFileAssemblies>true</CopyLocalLockFileAssemblies>
  </PropertyGroup>


  <ItemGroup>
    <None Remove="nlog.config" />
  </ItemGroup>

  <ItemGroup>
    <FrameworkReference Include="Microsoft.AspNetCore.App" />
    <PackageReference Include="AspNetCoreRateLimit" Version="5.0.0" />
    <PackageReference Include="AWSSDK.Athena" Version="3.7.200.63" />
    <PackageReference Include="AWSSDK.Core" Version="3.7.204.10" />
    <PackageReference Include="AWSSDK.DynamoDBv2" Version="3.7.0" />
    <PackageReference Include="AWSSDK.EC2" Version="3.7.128.3" />
    <PackageReference Include="AWSSDK.Extensions.NETCore.Setup" Version="3.7.7" />
    <PackageReference Include="AWSSDK.Lambda" Version="3.7.105.19" />
    <PackageReference Include="AWSSDK.S3" Version="3.7.103.36" />
    <PackageReference Include="AWSSDK.SimpleEmail" Version="3.7.100.99" />
    <PackageReference Include="AWSSDK.SQS" Version="3.7.100.99" />
    <PackageReference Include="Confluent.Kafka" Version="2.1.1" />
    <PackageReference Include="Crc32.NET" Version="1.2.0" />
    <PackageReference Include="Dapper" Version="2.0.123" />
    <PackageReference Include="EPPlus" Version="6.2.0" />
    <PackageReference Include="Google.Cloud.BigQuery.V2" Version="3.2.0" />
	<PackageReference Include="Scrutor" Version="4.2.2" />
    <PackageReference Include="log4net" Version="2.0.15" />
    <PackageReference Include="Logtail" Version="0.2.6" />
    <PackageReference Include="Microsoft.Extensions.Caching.Abstractions" Version="7.0.0" />
	  <PackageReference Include="Microsoft.SqlServer.SqlManagementObjects" Version="170.13.0">
      <Aliases></Aliases>
    </PackageReference>
    <PackageReference Include="MQTTnet" Version="4.1.4.563" />
    <PackageReference Include="MySql.Data" Version="8.1.0" />
    <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
    <PackageReference Include="NLog" Version="5.2.4" />
    <PackageReference Include="Paytm.Checksum" Version="1.0.0" />
    <PackageReference Include="RazorPay.Core" Version="1.0.2" />
    <PackageReference Include="RestSharp" Version="109.0.1" />
    <PackageReference Include="Serilog" Version="3.0.1" />
    <PackageReference Include="Serilog.Sinks.NLog" Version="3.0.0" />
    <PackageReference Include="StackExchange.Redis" Version="2.6.96" />
    <PackageReference Include="System.Data.SqlClient" Version="4.8.5">
		<Aliases></Aliases>
    </PackageReference>
    <PackageReference Include="BouncyCastle.NetCore" Version="1.9.0" />
    <PackageReference Include="wiom-payment-share" Version="1.2.2" />
    <PackageReference Include="wiom-router-api" Version="10.4.6" />
    <PackageReference Include="wiom-routerplan-share" Version="1.0.9" />
  </ItemGroup>

  <ItemGroup>
    <Folder Include="Properties\PublishProfiles\" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\wiom-login-share\wiom-login-share.csproj" />
  </ItemGroup>
  <ItemGroup>
    <None Update="nlog-prod.config">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="nlog.config">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
  </ItemGroup>

</Project>
